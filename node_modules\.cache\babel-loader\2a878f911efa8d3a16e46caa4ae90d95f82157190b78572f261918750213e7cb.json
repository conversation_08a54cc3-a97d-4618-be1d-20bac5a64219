{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\statistiche\\\\tabellaTopFlop.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useEffect } from \"react\";\nimport { APIRequest } from \"../apireq\";\nimport { Costanti } from \"../../traduttore/const\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport CustomDataTable from \"../../customDataTable\";\nimport { stopLoading } from \"../stopLoading\";\nimport { affiliato } from \"../../route\";\nimport { SelectButton } from \"primereact/selectbutton\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TabellaTopFlop = props => {\n  _s();\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedRetailer, setSelectedRetailer] = useState(null);\n  const [retailers, setRetailer] = useState([{\n    neme: 'Tutti',\n    code: 0\n  }]);\n  const [value1, setValue1] = useState({\n    name: Costanti.AnnCorr,\n    code: 'ref=current&type=year&qty=1'\n  });\n  const options = [{\n    name: Costanti.AnnCorr,\n    code: 'ref=current&type=year&qty=1'\n  }, {\n    name: Costanti.UltDodMes,\n    code: 'ref=last&type=year&qty=1'\n  }, {\n    name: Costanti.UltTreMes,\n    code: 'ref=last&type=month&qty=3'\n  }];\n  useEffect(() => {\n    async function trovaRisultato() {\n      //Chiamata axios per la visualizzazione dei retailers\n      await APIRequest('GET', 'retailers/').then(async res => {\n        var retailers = [{\n          name: 'Tutti',\n          code: 0\n        }];\n        res.data.forEach(element => {\n          var x = {\n            name: element.idRegistry.firstName,\n            code: element.id\n          };\n          retailers.push(x);\n        });\n        setRetailer(retailers);\n        setSelectedRetailer(retailers[0]);\n        await APIRequest('GET', 'statistic/productretailer').then(res => {\n          setResults(res.data);\n          setLoading(false);\n        }).catch(e => {\n          console.log(e);\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    trovaRisultato();\n    if (window.localStorage.getItem('role') === affiliato) {\n      stopLoading();\n    }\n  }, []);\n  const onRetailerChange = async e => {\n    setSelectedRetailer(e.value);\n    setResults([]);\n    setLoading(true);\n    var url = e.value.code !== 0 ? 'statistic/productretailer?idRetailer=' + e.value.code + '&' + value1 : 'statistic/productretailer';\n    await APIRequest('GET', url).then(res => {\n      setResults(res.data);\n      setLoading(false);\n    }).catch(e => {\n      console.log(e);\n    });\n  };\n  const cambiaDati = async e => {\n    setValue1(e.value);\n    var url = 'statistic/productretailer' + (selectedRetailer.code ? 'idRetailer=' + selectedRetailer.code + '&' + e.value.code : '?' + e.value.code);\n    await APIRequest('GET', url).then(res => {\n      setResults(res.data);\n      setLoading(false);\n    }).catch(e => {\n      console.log(e);\n    });\n  };\n  const fields = [{\n    field: 'image',\n    body: 'image'\n  }, {\n    field: 'externalcode',\n    header: Costanti.CodProd,\n    body: 'externalcode',\n    showHeader: true,\n    sortable: true\n  }, {\n    field: 'description',\n    header: Costanti.Nome,\n    body: 'description',\n    showHeader: true,\n    sortable: true\n  }, {\n    field: 'qta',\n    header: Costanti.Quantità,\n    body: 'qta',\n    showHeader: true,\n    sortable: true\n  }, {\n    field: 'totale',\n    header: Costanti.Tot,\n    body: 'totale',\n    showHeader: true,\n    sortable: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-3 col-lg-2 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n        className: \"w-100\",\n        value: selectedRetailer,\n        options: retailers,\n        onChange: onRetailerChange,\n        optionLabel: \"name\",\n        placeholder: \"Seleziona cliente\",\n        filter: true,\n        filterBy: \"name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(SelectButton, {\n        value: value1,\n        options: options,\n        optionLabel: 'name',\n        onChange: e => cambiaDati(e),\n        className: \"selectTrend d-flex flex-column\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-9 col-lg-10 mt-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"datatable-responsive-demo wrapper\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          value: results,\n          fields: fields,\n          loading: loading,\n          dataKey: \"id\",\n          responsiveLayout: \"scroll\",\n          paginator: true,\n          rows: 5,\n          rowsPerPageOptions: [5, 10, 20, 50],\n          autoLayout: true,\n          showExportCsvButton: true,\n          fileNames: \"ProdottiPi\\xF9Venduti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 9\n  }, this);\n};\n_s(TabellaTopFlop, \"J+mhmbuaYCmlxx7TUhvk06DGfkI=\");\n_c = TabellaTopFlop;\nvar _c;\n$RefreshReg$(_c, \"TabellaTopFlop\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "APIRequest", "<PERSON><PERSON>", "Dropdown", "CustomDataTable", "stopLoading", "affiliato", "SelectButton", "jsxDEV", "_jsxDEV", "TabellaTop<PERSON>lop", "props", "_s", "results", "setResults", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRetailer", "retailers", "setRetailer", "neme", "code", "value1", "setValue1", "name", "<PERSON><PERSON><PERSON><PERSON>", "options", "UltDodMes", "UltTreMes", "trovaRisultato", "then", "res", "data", "for<PERSON>ach", "element", "x", "idRegistry", "firstName", "id", "push", "catch", "e", "console", "log", "window", "localStorage", "getItem", "onRetailerChange", "value", "url", "cambiaDati", "fields", "field", "body", "header", "CodProd", "showHeader", "sortable", "Nome", "Quantità", "<PERSON><PERSON>", "className", "children", "onChange", "optionLabel", "placeholder", "filter", "filterBy", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dataKey", "responsiveLayout", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "showExportCsvButton", "fileNames", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/statistiche/tabellaTopFlop.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useEffect } from \"react\";\nimport { APIRequest } from \"../apireq\";\nimport { Costanti } from \"../../traduttore/const\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport CustomDataTable from \"../../customDataTable\";\nimport { stopLoading } from \"../stopLoading\";\nimport { affiliato } from \"../../route\";\nimport { SelectButton } from \"primereact/selectbutton\";\n\nexport const TabellaTopFlop = (props) => {\n    const [results, setResults] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [selectedRetailer, setSelectedRetailer] = useState(null);\n    const [retailers, setRetailer] = useState([{ neme: 'Tutti', code: 0 }]);\n    const [value1, setValue1] = useState({ name: Costanti.AnnCorr, code: 'ref=current&type=year&qty=1' });\n    const options = [\n        { name: Costanti.AnnCorr, code: 'ref=current&type=year&qty=1' },\n        { name: Costanti.UltDodMes, code: 'ref=last&type=year&qty=1' },\n        { name: Costanti.UltTreMes, code: 'ref=last&type=month&qty=3' }\n    ]\n\n    useEffect(() => {\n        async function trovaRisultato() {\n            //Chiamata axios per la visualizzazione dei retailers\n            await APIRequest('GET', 'retailers/')\n                .then(async res => {\n                    var retailers = [{ name: 'Tutti', code: 0 }]\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.idRegistry.firstName,\n                            code: element.id\n                        }\n                        retailers.push(x)\n                    })\n                    setRetailer(retailers)\n                    setSelectedRetailer(retailers[0])\n                    await APIRequest('GET', 'statistic/productretailer')\n                        .then(res => {\n                            setResults(res.data)\n                            setLoading(false)\n                        }).catch((e) => {\n                            console.log(e);\n                        })\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        trovaRisultato();\n        if (window.localStorage.getItem('role') === affiliato) {\n            stopLoading()\n        }\n    }, [])\n    const onRetailerChange = async (e) => {\n        setSelectedRetailer(e.value);\n        setResults([])\n        setLoading(true)\n\n        var url = e.value.code !== 0 ? 'statistic/productretailer?idRetailer=' + e.value.code + '&' + value1 : 'statistic/productretailer'\n        await APIRequest('GET', url)\n            .then(res => {\n                setResults(res.data)\n                setLoading(false)\n            }).catch((e) => {\n                console.log(e);\n            })\n    }\n    const cambiaDati = async (e) => {\n        setValue1(e.value)\n        var url = 'statistic/productretailer' + (selectedRetailer.code ? 'idRetailer=' + selectedRetailer.code + '&' + e.value.code : '?' + e.value.code) \n        await APIRequest('GET', url)\n            .then(res => {\n                setResults(res.data)\n                setLoading(false)\n            }).catch((e) => {\n                console.log(e);\n            })\n    }\n    const fields = [\n        { field: 'image', body: 'image' },\n        { field: 'externalcode', header: Costanti.CodProd, body: 'externalcode', showHeader: true, sortable: true },\n        { field: 'description', header: Costanti.Nome, body: 'description', showHeader: true, sortable: true },\n        { field: 'qta', header: Costanti.Quantità, body: 'qta', showHeader: true, sortable: true },\n        { field: 'totale', header: Costanti.Tot, body: 'totale', showHeader: true, sortable: true }\n    ];\n    return (\n        <div className=\"row\">\n            <div className=\"col-12 col-md-3 col-lg-2 mt-3\">\n                <Dropdown className=\"w-100\" value={selectedRetailer} options={retailers} onChange={onRetailerChange} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n                <SelectButton value={value1} options={options} optionLabel={'name'} onChange={(e) => cambiaDati(e)} className='selectTrend d-flex flex-column' />\n            </div>\n            <div className=\"col-12 col-md-9 col-lg-10 mt-3\">\n                <div className=\"datatable-responsive-demo wrapper\">\n                    <CustomDataTable\n                        value={results}\n                        fields={fields}\n                        loading={loading}\n                        dataKey=\"id\"\n                        responsiveLayout=\"scroll\"\n                        paginator\n                        rows={5}\n                        rowsPerPageOptions={[5, 10, 20, 50]}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        fileNames=\"ProdottiPiùVenduti\"\n                    />\n                </div>\n            </div>\n        </div>\n    )\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,YAAY,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,OAAO,MAAMC,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACoB,SAAS,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,CAAC;IAAEsB,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAE,CAAC,CAAC,CAAC;EACvE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC;IAAE0B,IAAI,EAAEvB,QAAQ,CAACwB,OAAO;IAAEJ,IAAI,EAAE;EAA8B,CAAC,CAAC;EACrG,MAAMK,OAAO,GAAG,CACZ;IAAEF,IAAI,EAAEvB,QAAQ,CAACwB,OAAO;IAAEJ,IAAI,EAAE;EAA8B,CAAC,EAC/D;IAAEG,IAAI,EAAEvB,QAAQ,CAAC0B,SAAS;IAAEN,IAAI,EAAE;EAA2B,CAAC,EAC9D;IAAEG,IAAI,EAAEvB,QAAQ,CAAC2B,SAAS;IAAEP,IAAI,EAAE;EAA4B,CAAC,CAClE;EAEDtB,SAAS,CAAC,MAAM;IACZ,eAAe8B,cAAcA,CAAA,EAAG;MAC5B;MACA,MAAM7B,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC8B,IAAI,CAAC,MAAMC,GAAG,IAAI;QACf,IAAIb,SAAS,GAAG,CAAC;UAAEM,IAAI,EAAE,OAAO;UAAEH,IAAI,EAAE;QAAE,CAAC,CAAC;QAC5CU,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAG;YACJX,IAAI,EAAEU,OAAO,CAACE,UAAU,CAACC,SAAS;YAClChB,IAAI,EAAEa,OAAO,CAACI;UAClB,CAAC;UACDpB,SAAS,CAACqB,IAAI,CAACJ,CAAC,CAAC;QACrB,CAAC,CAAC;QACFhB,WAAW,CAACD,SAAS,CAAC;QACtBD,mBAAmB,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjC,MAAMlB,UAAU,CAAC,KAAK,EAAE,2BAA2B,CAAC,CAC/C8B,IAAI,CAACC,GAAG,IAAI;UACTlB,UAAU,CAACkB,GAAG,CAACC,IAAI,CAAC;UACpBjB,UAAU,CAAC,KAAK,CAAC;QACrB,CAAC,CAAC,CAACyB,KAAK,CAAEC,CAAC,IAAK;UACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QAClB,CAAC,CAAC;MACV,CAAC,CAAC,CAACD,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACAZ,cAAc,CAAC,CAAC;IAChB,IAAIe,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAKzC,SAAS,EAAE;MACnDD,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAM2C,gBAAgB,GAAG,MAAON,CAAC,IAAK;IAClCxB,mBAAmB,CAACwB,CAAC,CAACO,KAAK,CAAC;IAC5BnC,UAAU,CAAC,EAAE,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAIkC,GAAG,GAAGR,CAAC,CAACO,KAAK,CAAC3B,IAAI,KAAK,CAAC,GAAG,uCAAuC,GAAGoB,CAAC,CAACO,KAAK,CAAC3B,IAAI,GAAG,GAAG,GAAGC,MAAM,GAAG,2BAA2B;IAClI,MAAMtB,UAAU,CAAC,KAAK,EAAEiD,GAAG,CAAC,CACvBnB,IAAI,CAACC,GAAG,IAAI;MACTlB,UAAU,CAACkB,GAAG,CAACC,IAAI,CAAC;MACpBjB,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CAACyB,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;EACV,CAAC;EACD,MAAMS,UAAU,GAAG,MAAOT,CAAC,IAAK;IAC5BlB,SAAS,CAACkB,CAAC,CAACO,KAAK,CAAC;IAClB,IAAIC,GAAG,GAAG,2BAA2B,IAAIjC,gBAAgB,CAACK,IAAI,GAAG,aAAa,GAAGL,gBAAgB,CAACK,IAAI,GAAG,GAAG,GAAGoB,CAAC,CAACO,KAAK,CAAC3B,IAAI,GAAG,GAAG,GAAGoB,CAAC,CAACO,KAAK,CAAC3B,IAAI,CAAC;IACjJ,MAAMrB,UAAU,CAAC,KAAK,EAAEiD,GAAG,CAAC,CACvBnB,IAAI,CAACC,GAAG,IAAI;MACTlB,UAAU,CAACkB,GAAG,CAACC,IAAI,CAAC;MACpBjB,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CAACyB,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;EACV,CAAC;EACD,MAAMU,MAAM,GAAG,CACX;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAQ,CAAC,EACjC;IAAED,KAAK,EAAE,cAAc;IAAEE,MAAM,EAAErD,QAAQ,CAACsD,OAAO;IAAEF,IAAI,EAAE,cAAc;IAAEG,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC3G;IAAEL,KAAK,EAAE,aAAa;IAAEE,MAAM,EAAErD,QAAQ,CAACyD,IAAI;IAAEL,IAAI,EAAE,aAAa;IAAEG,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACtG;IAAEL,KAAK,EAAE,KAAK;IAAEE,MAAM,EAAErD,QAAQ,CAAC0D,QAAQ;IAAEN,IAAI,EAAE,KAAK;IAAEG,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC1F;IAAEL,KAAK,EAAE,QAAQ;IAAEE,MAAM,EAAErD,QAAQ,CAAC2D,GAAG;IAAEP,IAAI,EAAE,QAAQ;IAAEG,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAC9F;EACD,oBACIjD,OAAA;IAAKqD,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAChBtD,OAAA;MAAKqD,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC1CtD,OAAA,CAACN,QAAQ;QAAC2D,SAAS,EAAC,OAAO;QAACb,KAAK,EAAEhC,gBAAiB;QAACU,OAAO,EAAER,SAAU;QAAC6C,QAAQ,EAAEhB,gBAAiB;QAACiB,WAAW,EAAC,MAAM;QAACC,WAAW,EAAC,mBAAmB;QAACC,MAAM;QAACC,QAAQ,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjL/D,OAAA,CAACF,YAAY;QAAC0C,KAAK,EAAE1B,MAAO;QAACI,OAAO,EAAEA,OAAQ;QAACsC,WAAW,EAAE,MAAO;QAACD,QAAQ,EAAGtB,CAAC,IAAKS,UAAU,CAACT,CAAC,CAAE;QAACoB,SAAS,EAAC;MAAgC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChJ,CAAC,eACN/D,OAAA;MAAKqD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC3CtD,OAAA;QAAKqD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAC9CtD,OAAA,CAACL,eAAe;UACZ6C,KAAK,EAAEpC,OAAQ;UACfuC,MAAM,EAAEA,MAAO;UACfrC,OAAO,EAAEA,OAAQ;UACjB0D,OAAO,EAAC,IAAI;UACZC,gBAAgB,EAAC,QAAQ;UACzBC,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACpCC,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BC,SAAS,EAAC;QAAoB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA5D,EAAA,CApGYF,cAAc;AAAAuE,EAAA,GAAdvE,cAAc;AAAA,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}