# 🔗 Integrazione Backend E-Procurement

Documentazione completa dell'integrazione tra frontend e backend.

## 🌐 Configurazione API

### **Endpoints Base**
```javascript
// Development
const DEV_API_URL = 'http://localhost:3001';

// Production  
const PROD_API_URL = 'https://epbackend-kekl2s5de-vincenzo-2210s-projects.vercel.app';

// Auto-configurazione
const API_URL = process.env.NODE_ENV === 'production' ? PROD_API_URL : DEV_API_URL;
```

### **Headers Standard**
```javascript
headers: {
  'auth': localStorage.getItem('login_token'),    // JWT Token
  'Content-Type': 'application/json',
  'accept': 'application/json'
}
```

## 🔐 Autenticazione

### **Flusso Login**
```javascript
// 1. Login Request
POST /auth/login
{
  "username": "<EMAIL>",
  "password": "password123"
}

// 2. Response
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 123,
    "role": "distributore",
    "name": "Mario Rossi"
  }
}

// 3. Storage
localStorage.setItem('login_token', response.token);
```

### **Refresh Token**
- **Automatico**: Token aggiornato da header risposta
- **Header**: `x-new-token` o simile
- **Storage**: Aggiornamento automatico in localStorage

## 📋 Endpoints Anagrafiche

### **1. Lista Anagrafiche**
```javascript
GET /registry?page=1&limit=10&search=term&role=distributore

Response:
{
  "success": true,
  "data": [
    {
      "id": 123,
      "firstName": "Mario",
      "lastName": "Rossi",
      "email": "<EMAIL>",
      "pIva": "***********",
      "address": "Via Roma 1",
      "city": "Milano",
      "tel": "3331234567/0212345678"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "pages": 15
  }
}
```

### **2. Creazione Anagrafica**
```javascript
POST /registry
{
  "firstName": "Mario",
  "lastName": "Rossi",
  "email": "<EMAIL>",
  "pIva": "***********",
  "address": "Via Roma 1",
  "city": "Milano",
  "cap": "20100",
  "tel": "3331234567/0212345678"
}

Response:
{
  "success": true,
  "data": {
    "id": 124,
    "firstName": "Mario",
    "lastName": "Rossi",
    // ... altri campi
  }
}
```

### **3. Modifica Anagrafica**
```javascript
PUT /registry/:id
{
  "firstName": "Mario",
  "lastName": "Rossi",
  // ... campi modificati
}

Response:
{
  "success": true,
  "data": {
    "id": 123,
    // ... dati aggiornati
  }
}
```

### **4. Eliminazione Anagrafica**
```javascript
DELETE /registry/:id

Response:
{
  "success": true,
  "message": "Anagrafica eliminata con successo"
}
```

## 🔍 Lookup P.IVA (Nuovo)

### **Endpoint VIES**
```javascript
GET /company-lookup?vat=IT***********

Response Success (200):
{
  "success": true,
  "company": {
    "name": "AMAZON ITALIA SERVICES SRL",
    "vatNumber": "*************",
    "address": "VIALE MONTE GRAPPA 3/5, MILANO MI",
    "city": "MILANO MI",
    "postalCode": "20124",
    "country": "IT",
    "isValid": true
  },
  "source": "VIES"
}

Response Not Found (404):
{
  "success": false,
  "error": {
    "code": "VAT_NOT_FOUND",
    "message": "Nessuna azienda trovata con questa Partita IVA"
  }
}

Response Service Down (503):
{
  "success": false,
  "error": {
    "code": "SERVICE_UNAVAILABLE",
    "message": "Il servizio di lookup P.IVA è temporaneamente non disponibile"
  }
}
```

### **Implementazione Frontend**
```javascript
const lookupPIva = async () => {
  try {
    const vatNumber = piva.replace(/\s/g, '');
    const response = await axios.get(
      `${API_URL}/company-lookup?vat=IT${vatNumber}`,
      {
        headers: { auth: localStorage.getItem('login_token') },
        timeout: 10000
      }
    );
    
    if (response.data.success) {
      // Auto-compilazione campi
      setNome(response.data.company.name);
      setIndirizzo(response.data.company.address);
      setCitta(response.data.company.city);
    }
  } catch (error) {
    // Gestione errori specifica
  }
};
```

## 🚨 Gestione Errori

### **Codici di Stato HTTP**

#### **400 - Bad Request**
```javascript
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Dati non validi",
    "field": "email",
    "details": "Formato email non valido"
  }
}
```

#### **401 - Unauthorized**
```javascript
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_ERROR",
    "message": "Token non valido o scaduto"
  }
}
```

#### **403 - Forbidden**
```javascript
{
  "success": false,
  "error": {
    "code": "AUTHORIZATION_ERROR",
    "message": "Permessi insufficienti per questa operazione"
  }
}
```

#### **409 - Conflict**
```javascript
{
  "success": false,
  "error": {
    "code": "DUPLICATE_ENTRY",
    "message": "Partita IVA già esistente",
    "field": "pIva",
    "value": "***********"
  }
}
```

#### **422 - Unprocessable Entity**
```javascript
{
  "success": false,
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Violazione regola business",
    "details": "Non è possibile eliminare anagrafica con ordini attivi"
  }
}
```

#### **500 - Internal Server Error**
```javascript
{
  "success": false,
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "Errore interno del server",
    "requestId": "req_123456789"
  }
}
```

### **Gestione Frontend**
```javascript
const handleApiError = (error) => {
  const status = error.response?.status;
  const errorData = error.response?.data;
  
  switch (status) {
    case 400:
      showToast('error', 'Dati non validi', errorData.error.message);
      break;
    case 401:
      localStorage.removeItem('login_token');
      redirectToLogin();
      break;
    case 403:
      showToast('warn', 'Accesso negato', 'Permessi insufficienti');
      break;
    case 409:
      showToast('warn', 'Conflitto', `${errorData.error.field}: ${errorData.error.message}`);
      break;
    case 500:
      showToast('error', 'Errore server', 'Riprovare più tardi');
      break;
    default:
      showToast('error', 'Errore', 'Errore imprevisto');
  }
};
```

## 📊 Logging e Monitoring

### **Request Logging**
```javascript
// Frontend
console.log('🔗 API Request:', {
  method: 'POST',
  url: '/registry',
  data: requestBody,
  timestamp: new Date().toISOString()
});

// Response Logging
console.log('📡 API Response:', {
  status: response.status,
  data: response.data,
  duration: Date.now() - startTime
});
```

### **Error Logging**
```javascript
console.error('❌ API Error:', {
  url: error.config?.url,
  method: error.config?.method,
  status: error.response?.status,
  data: error.response?.data,
  message: error.message,
  stack: error.stack
});
```

## 🔄 Retry Logic

### **Implementazione Retry**
```javascript
const apiRequestWithRetry = async (method, endpoint, data, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await APIRequest(method, endpoint, data);
    } catch (error) {
      if (attempt === maxRetries || error.response?.status < 500) {
        throw error;
      }
      
      const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

## 🔒 Sicurezza

### **HTTPS Obbligatorio**
- **Production**: Solo HTTPS
- **Development**: HTTP consentito per localhost

### **CORS Configuration**
```javascript
// Backend deve configurare
app.use(cors({
  origin: [
    'http://localhost:3000',
    'https://ep-frontend-coral.vercel.app',
    'https://eprocurement.tmselezioni.it'
  ],
  credentials: true
}));
```

### **Token Security**
- **Storage**: localStorage (considerare httpOnly cookies)
- **Expiration**: Token con scadenza
- **Refresh**: Meccanismo di refresh automatico

## 📈 Performance

### **Caching Strategy**
```javascript
// Cache responses per endpoint specifici
const cache = new Map();

const getCachedResponse = (key) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < 300000) { // 5 min
    return cached.data;
  }
  return null;
};
```

### **Request Optimization**
- **Debouncing**: Ricerche e validazioni
- **Pagination**: Liste grandi
- **Compression**: Gzip responses
- **CDN**: Asset statici

---

**Ultimo aggiornamento**: 16 Gennaio 2025
**Versione**: 1.0.0
