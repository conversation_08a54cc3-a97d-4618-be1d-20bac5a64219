{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport Dropdown from '../dropdown/dropdown';\nimport { ConfigContext } from '../config-provider';\nvar BreadcrumbItem = function BreadcrumbItem(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    _a$separator = _a.separator,\n    separator = _a$separator === void 0 ? '/' : _a$separator,\n    children = _a.children,\n    overlay = _a.overlay,\n    dropdownProps = _a.dropdownProps,\n    restProps = __rest(_a, [\"prefixCls\", \"separator\", \"children\", \"overlay\", \"dropdownProps\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  /** If overlay is have Wrap a Dropdown */\n\n  var renderBreadcrumbNode = function renderBreadcrumbNode(breadcrumbItem) {\n    if (overlay) {\n      return /*#__PURE__*/React.createElement(Dropdown, _extends({\n        overlay: overlay,\n        placement: \"bottom\"\n      }, dropdownProps), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-overlay-link\")\n      }, breadcrumbItem, /*#__PURE__*/React.createElement(DownOutlined, null)));\n    }\n    return breadcrumbItem;\n  };\n  var link;\n  if ('href' in restProps) {\n    link = /*#__PURE__*/React.createElement(\"a\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  } else {\n    link = /*#__PURE__*/React.createElement(\"span\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  } // wrap to dropDown\n\n  link = renderBreadcrumbNode(link);\n  if (children) {\n    return /*#__PURE__*/React.createElement(\"li\", null, link, separator && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-separator\")\n    }, separator));\n  }\n  return null;\n};\nBreadcrumbItem.__ANT_BREADCRUMB_ITEM = true;\nexport default BreadcrumbItem;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "DownOutlined", "Dropdown", "ConfigContext", "BreadcrumbItem", "_a", "customizePrefixCls", "prefixCls", "_a$separator", "separator", "children", "overlay", "dropdownProps", "restProps", "_React$useContext", "useContext", "getPrefixCls", "renderBreadcrumbNode", "breadcrumbItem", "createElement", "placement", "className", "concat", "link", "__ANT_BREADCRUMB_ITEM"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/breadcrumb/BreadcrumbItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport Dropdown from '../dropdown/dropdown';\nimport { ConfigContext } from '../config-provider';\n\nvar BreadcrumbItem = function BreadcrumbItem(_a) {\n  var customizePrefixCls = _a.prefixCls,\n      _a$separator = _a.separator,\n      separator = _a$separator === void 0 ? '/' : _a$separator,\n      children = _a.children,\n      overlay = _a.overlay,\n      dropdownProps = _a.dropdownProps,\n      restProps = __rest(_a, [\"prefixCls\", \"separator\", \"children\", \"overlay\", \"dropdownProps\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  /** If overlay is have Wrap a Dropdown */\n\n  var renderBreadcrumbNode = function renderBreadcrumbNode(breadcrumbItem) {\n    if (overlay) {\n      return /*#__PURE__*/React.createElement(Dropdown, _extends({\n        overlay: overlay,\n        placement: \"bottom\"\n      }, dropdownProps), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-overlay-link\")\n      }, breadcrumbItem, /*#__PURE__*/React.createElement(DownOutlined, null)));\n    }\n\n    return breadcrumbItem;\n  };\n\n  var link;\n\n  if ('href' in restProps) {\n    link = /*#__PURE__*/React.createElement(\"a\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  } else {\n    link = /*#__PURE__*/React.createElement(\"span\", _extends({\n      className: \"\".concat(prefixCls, \"-link\")\n    }, restProps), children);\n  } // wrap to dropDown\n\n\n  link = renderBreadcrumbNode(link);\n\n  if (children) {\n    return /*#__PURE__*/React.createElement(\"li\", null, link, separator && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-separator\")\n    }, separator));\n  }\n\n  return null;\n};\n\nBreadcrumbItem.__ANT_BREADCRUMB_ITEM = true;\nexport default BreadcrumbItem;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AAEzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAElD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,EAAE,EAAE;EAC/C,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACjCC,YAAY,GAAGH,EAAE,CAACI,SAAS;IAC3BA,SAAS,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,YAAY;IACxDE,QAAQ,GAAGL,EAAE,CAACK,QAAQ;IACtBC,OAAO,GAAGN,EAAE,CAACM,OAAO;IACpBC,aAAa,GAAGP,EAAE,CAACO,aAAa;IAChCC,SAAS,GAAG3B,MAAM,CAACmB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;EAE9F,IAAIS,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACZ,aAAa,CAAC;IACnDa,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIT,SAAS,GAAGS,YAAY,CAAC,YAAY,EAAEV,kBAAkB,CAAC;EAC9D;;EAEA,IAAIW,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,cAAc,EAAE;IACvE,IAAIP,OAAO,EAAE;MACX,OAAO,aAAaX,KAAK,CAACmB,aAAa,CAACjB,QAAQ,EAAEjB,QAAQ,CAAC;QACzD0B,OAAO,EAAEA,OAAO;QAChBS,SAAS,EAAE;MACb,CAAC,EAAER,aAAa,CAAC,EAAE,aAAaZ,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;QAC1DE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,eAAe;MACjD,CAAC,EAAEW,cAAc,EAAE,aAAalB,KAAK,CAACmB,aAAa,CAAClB,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3E;IAEA,OAAOiB,cAAc;EACvB,CAAC;EAED,IAAIK,IAAI;EAER,IAAI,MAAM,IAAIV,SAAS,EAAE;IACvBU,IAAI,GAAG,aAAavB,KAAK,CAACmB,aAAa,CAAC,GAAG,EAAElC,QAAQ,CAAC;MACpDoC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,OAAO;IACzC,CAAC,EAAEM,SAAS,CAAC,EAAEH,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACLa,IAAI,GAAG,aAAavB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAElC,QAAQ,CAAC;MACvDoC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,OAAO;IACzC,CAAC,EAAEM,SAAS,CAAC,EAAEH,QAAQ,CAAC;EAC1B,CAAC,CAAC;;EAGFa,IAAI,GAAGN,oBAAoB,CAACM,IAAI,CAAC;EAEjC,IAAIb,QAAQ,EAAE;IACZ,OAAO,aAAaV,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE,IAAI,EAAEI,IAAI,EAAEd,SAAS,IAAI,aAAaT,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC9GE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,YAAY;IAC9C,CAAC,EAAEE,SAAS,CAAC,CAAC;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDL,cAAc,CAACoB,qBAAqB,GAAG,IAAI;AAC3C,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}