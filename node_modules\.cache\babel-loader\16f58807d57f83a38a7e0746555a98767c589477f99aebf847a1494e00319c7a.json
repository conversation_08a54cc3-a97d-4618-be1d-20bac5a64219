{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonAvatar = function SkeletonAvatar(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls', 'className']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-avatar\")\n  }, otherProps)));\n};\nSkeletonAvatar.defaultProps = {\n  size: 'default',\n  shape: 'circle'\n};\nexport default SkeletonAvatar;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "omit", "classNames", "ConfigContext", "Element", "SkeletonAvatar", "props", "customizePrefixCls", "prefixCls", "className", "active", "_React$useContext", "useContext", "getPrefixCls", "otherProps", "cls", "concat", "createElement", "defaultProps", "size", "shape"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Avatar.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\n\nvar SkeletonAvatar = function SkeletonAvatar(props) {\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      active = props.active;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls', 'className']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-avatar\")\n  }, otherProps)));\n};\n\nSkeletonAvatar.defaultProps = {\n  size: 'default',\n  shape: 'circle'\n};\nexport default SkeletonAvatar;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAE/B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACpCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAEzB,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACT,aAAa,CAAC;IACnDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIL,SAAS,GAAGK,YAAY,CAAC,UAAU,EAAEN,kBAAkB,CAAC;EAC5D,IAAIO,UAAU,GAAGb,IAAI,CAACK,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EACxD,IAAIS,GAAG,GAAGb,UAAU,CAACM,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC,EAAET,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACiB,MAAM,CAACR,SAAS,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,EAAED,SAAS,CAAC;EAC1I,OAAO,aAAaT,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC7CR,SAAS,EAAEM;EACb,CAAC,EAAE,aAAaf,KAAK,CAACiB,aAAa,CAACb,OAAO,EAAEN,QAAQ,CAAC;IACpDU,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACR,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEM,UAAU,CAAC,CAAC,CAAC;AAClB,CAAC;AAEDT,cAAc,CAACa,YAAY,GAAG;EAC5BC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACT,CAAC;AACD,eAAef,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}