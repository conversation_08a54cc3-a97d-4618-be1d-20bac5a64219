{"version": 3, "file": "brace-patterns.js", "sourceRoot": "", "sources": ["../../src/brace-patterns.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,QAAQ,CAAA;AACtC,MAAM,CAAC,MAAM,oBAAoB,GAAG,MAAM,CAAA", "sourcesContent": ["/**\n * this isn't for performance or anything, it just confuses vim's\n * brace-matching to have these in the middle of functions, and\n * I'm too lazy to dig into vim-javascript to fix it, so hence these\n * capital snake consts.\n *\n * @module\n */\nexport const OPEN_BRACE_EOL = /\\{\\s*$/\nexport const SPACE_OPEN_BRACE_EOL = / \\{$/\n"]}