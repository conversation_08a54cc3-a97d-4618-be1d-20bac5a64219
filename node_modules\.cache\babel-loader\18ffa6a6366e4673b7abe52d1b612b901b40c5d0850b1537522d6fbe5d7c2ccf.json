{"ast": null, "code": "/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Store - provider del carrello\n*\n*/\nimport { createStore, applyMiddleware } from 'redux';\nimport { composeWithDevTools } from 'redux-devtools-extension';\nimport thunk from 'redux-thunk';\nimport rootReducer from './reducers';\nconst initialState = {};\nconst middleware = [thunk];\nconst store = createStore(rootReducer, initialState, composeWithDevTools(applyMiddleware(...middleware)));\nexport default store;", "map": {"version": 3, "names": ["createStore", "applyMiddleware", "composeWithDevTools", "thunk", "rootReducer", "initialState", "middleware", "store"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/carrello/store.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Store - provider del carrello\n*\n*/\nimport {createStore, applyMiddleware} from 'redux';\nimport { composeWithDevTools } from 'redux-devtools-extension';\nimport thunk from 'redux-thunk';\nimport rootReducer from './reducers'\n\nconst initialState = {};\nconst middleware = [thunk];\n\nconst store = createStore(\n    rootReducer,\n    initialState,\n    composeWithDevTools(applyMiddleware(...middleware))\n);\n\nexport default store;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAQA,WAAW,EAAEC,eAAe,QAAO,OAAO;AAClD,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,YAAY;AAEpC,MAAMC,YAAY,GAAG,CAAC,CAAC;AACvB,MAAMC,UAAU,GAAG,CAACH,KAAK,CAAC;AAE1B,MAAMI,KAAK,GAAGP,WAAW,CACrBI,WAAW,EACXC,YAAY,EACZH,mBAAmB,CAACD,eAAe,CAAC,GAAGK,UAAU,CAAC,CACtD,CAAC;AAED,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}