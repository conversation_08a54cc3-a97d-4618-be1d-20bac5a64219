{"core/audits/accessibility/accesskeys.js | description": {"message": "Les clés d'accès permettent aux utilisateurs de positionner rapidement le curseur dans une partie spécifique de la page. Pour les aider à naviguer correctement, pensez à définir des clés d'accès uniques. [En savoir plus sur les clés d'accès](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Les valeurs `[accesskey]` ne sont pas uniques"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Les valeurs `[accesskey]` sont uniques"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON>que `role` ARIA est rattaché à un sous-ensemble spécifique d'attributs `aria-*`. S'ils ne sont pas correctement associés, les attributs `aria-*` ne seront pas valides. [<PERSON><PERSON><PERSON><PERSON><PERSON>z comment rattacher les attributs ARIA à leurs rôles.](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Les attributs `[aria-*]` ne correspondent pas à leurs rôles"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Les attributs `[aria-*]` correspondent à leurs rôles"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Lorsqu'un élément n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [Découvrez comment rendre les éléments de commande plus accessibles.](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Les éléments `button`, `link` et `menuitem` n'ont pas de noms accessibles."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Les éléments `button`, `link` et `menuitem` ont des noms accessibles"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Les technologies d'assistance, telles que les lecteurs d'écran, présentent un fonctionnement irrégulier lorsque `aria-hidden=\"true\"` est défini sur l'élément `<body>` du document. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> comment `aria-hidden` affecte le corps du document.](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` figure sur le document `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` ne figure pas sur le document `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "La présence de descendants sélectionnables dans un élément `[aria-hidden=\"true\"]` empêche les utilisateurs de technologies d'assistance, telles que des lecteurs d'écran, de se servir de ces éléments interactifs. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> comment `aria-hidden` affecte les éléments sélectionnables.](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Les éléments `[aria-hidden=\"true\"]` contiennent des descendants sélectionnables"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Les éléments `[aria-hidden=\"true\"]` ne contiennent pas de descendants sélectionnables"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Lorsqu'un champ de saisie n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [En savoir plus sur le libellé des champs de saisie](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Les champs de saisie ARIA n'ont pas de noms accessibles"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Les champs de saisie ARIA ont des noms accessibles"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Lorsqu'un élément outil de mesure n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [Découvrez comment nommer des éléments `meter`.](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Les éléments ARIA `meter` n'ont pas de noms accessibles."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Les éléments ARIA `meter` ont des noms accessibles"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Lorsqu'un élément `progressbar` n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [Découvrez comment ajouter des libellés aux éléments `progressbar`.](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Les éléments ARIA `progressbar` n'ont pas de noms accessibles."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Les éléments ARIA `progressbar` ont des noms accessibles"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Certains rôles ARIA ont des attributs obligatoires qui décrivent l'état de l'élément aux lecteurs d'écran. [En savoir plus sur les rôles et les attributs obligatoires](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Les éléments `[role]` ne possèdent pas tous les attributs `[aria-*]` requis"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Tous les éléments `[role]` contiennent les attributs `[aria-*]` requis"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Certains rôles ARIA parents doivent contenir des rôles enfants spécifiques afin de remplir correctement leurs fonctions d'accessibilité. [En savoir plus sur les rôles et les éléments enfants requis](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Les éléments ayant un `[role]` <PERSON>, qui exigent que les enfants incluent un `[role]` spécifique, ne possèdent pas certains ou l'ensemble des enfants requis."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Les éléments ayant un `[role]` <PERSON>, qui exigent que les enfants incluent un `[role]` spécifique, possèdent tous les enfants requis."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Certains rôles ARIA enfants doivent être inclus dans un rôle parent spécifique afin de remplir correctement leurs fonctions d'accessibilité. [En savoir plus sur les rôles ARIA et l'élément parent requis](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Les éléments `[role]` ne sont pas inclus dans l'élément parent requis"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Les éléments `[role]` sont inclus dans l'élément parent approprié"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Les rôles ARIA doivent comporter des valeurs valides afin de remplir correctement leurs fonctions d'accessibilité. [En savoir plus sur les rôles ARIA valides](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Les valeurs `[role]` ne sont pas valides"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Les valeurs `[role]` sont valides"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Lorsqu'un champ d'activation/de désactivation n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [En savoir plus sur les champs d'activation/de désactivation](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Les champs d'activation/de désactivation ARIA n'ont pas de noms accessibles"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Les champs d'activation/de désactivation ARIA ont des noms accessibles"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Lorsqu'un élément info-bulle n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [Découvrez comment nommer des éléments `tooltip`.](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Les éléments ARIA `tooltip` n'ont pas de noms accessibles."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Les éléments ARIA `tooltip` ont des noms accessibles"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Lorsqu'un élément `treeitem` n'a pas de nom accessible, les lecteurs d'écran l'annoncent avec un nom générique, ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [En savoir plus sur l'ajout de libellé aux éléments `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Les éléments ARIA `treeitem` n'ont pas de noms accessibles."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Les éléments ARIA `treeitem` ont des noms accessibles"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Les technologies d'assistance telles que les lecteurs d'écran ne peuvent pas interpréter les attributs ARIA si leurs valeurs ne sont pas valides. [En savoir plus sur les valeurs valides des attributs ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "La valeur des attributs `[aria-*]` n'est pas valide"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Les attributs `[aria-*]` ont des valeurs valides"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Les technologies d'assistance telles que les lecteurs d'écran ne peuvent pas interpréter les attributs ARIA si leurs noms ne sont pas valides. [En savoir plus sur les attributs ARIA valides](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Les attributs `[aria-*]` ne sont pas valides ou sont mal orthographiés"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Les attributs `[aria-*]` sont valides et correctement orthographiés"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Éléments non conformes"}, "core/audits/accessibility/button-name.js | description": {"message": "Lorsqu'un bouton n'a pas de nom accessible, les lecteurs d'écran annoncent simplement qu'il s'agit d'un \"bouton\", ce qui le rend inutilisable pour les personnes qui se servent de tels outils. [Découvrez comment rendre les boutons plus accessibles.](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Les boutons n'ont pas de nom accessible"}, "core/audits/accessibility/button-name.js | title": {"message": "Les boutons ont un nom accessible"}, "core/audits/accessibility/bypass.js | description": {"message": "En ajoutant des méthodes pour contourner les contenus répétitifs, vous permettez aux internautes qui utilisent un clavier de naviguer plus efficacement sur la page. [En savoir plus sur les blocs de contournement](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "La page ne contient pas de titre, de lien \"Ignorer\" ni de point de repère"}, "core/audits/accessibility/bypass.js | title": {"message": "La page contient un titre, un lien \"Ignorer\" ou un point de repère"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Un texte faiblement contrasté est difficile, voire impossible à lire pour de nombreux utilisateurs. [Dé<PERSON>uvrez comment fournir un contraste suffisant des couleurs.](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Les couleurs d'arrière-plan et de premier plan ne sont pas suffisamment contrastées"}, "core/audits/accessibility/color-contrast.js | title": {"message": "Les couleurs d'arrière-plan et de premier plan sont suffisamment contrastées"}, "core/audits/accessibility/definition-list.js | description": {"message": "Si les listes de définition ne sont pas correctement balisées, les lecteurs d'écran risquent de donner des résultats confus ou imprécis. [Découvrez comment structurer correctement les listes de définition.](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Les éléments `<dl>` ne contiennent pas uniquement des groupes `<dt>` et `<dd>` ainsi que des éléments `<script>`, `<template>` ou `<div>` dans le bon ordre."}, "core/audits/accessibility/definition-list.js | title": {"message": "Les éléments `<dl>` ne contiennent que des groupes `<dt>` et `<dd>` ainsi que des éléments `<script>`, `<template>` ou `<div>` dans le bon ordre."}, "core/audits/accessibility/dlitem.js | description": {"message": "Les éléments de liste de définition (`<dt>` et `<dd>`) doivent être encapsulés dans un élément `<dl>` parent afin que les lecteurs d'écran puissent les énoncer correctement. [Découvrez comment structurer correctement les listes de définition.](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Les éléments de liste de définition ne sont pas encapsulés dans des éléments `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Les éléments de liste de définition sont encapsulés dans des éléments `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Le titre donne aux utilisateurs de lecteurs d'écran un aperçu de la page. En outre, les moteurs de recherche s'appuient principalement sur ce dernier pour déterminer la pertinence du contenu proposé. [En savoir plus sur le titre des documents](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Le document ne contient pas d'élément `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Le document contient un élément `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Tous les éléments sélectionnables doivent être associés à un `id` unique pour qu'ils soient visibles par les technologies d'assistance. [Dé<PERSON>uvrez comment résoudre les problèmes d'`id` en double.](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Les attributs `[id]` sur des éléments sélectionnables actifs ne sont pas uniques"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Les attributs `[id]` sur des éléments sélectionnables actifs sont uniques"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "La valeur d'un ID ARIA doit être unique afin que les différentes instances soient toutes prises en compte par les technologies d'assistance. [Découvrez comment résoudre les problèmes d'ID ARIA en double.](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Les ID ARIA ne sont pas uniques"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Les ID ARIA sont uniques"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Les champs de formulaire comprenant plusieurs libellés peuvent être annoncés par les technologies d'assistance comme des lecteurs d'écran utilisant le premier, le dernier ou tous les libellés, ce qui peut prêter à confusion. [Découvrez comment utiliser les libellés de formulaires.](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Les champs de formulaire comprennent plusieurs libellés"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Aucun champ de formulaire ne comporte plusieurs libellés"}, "core/audits/accessibility/frame-title.js | description": {"message": "Les lecteurs d'écran s'appuient sur le titre des frames pour décrire le contenu de ces derniers aux utilisateurs. [En savoir plus sur le titre des frames](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Les éléments `<frame>` ou `<iframe>` n'ont pas de titre"}, "core/audits/accessibility/frame-title.js | title": {"message": "Les éléments `<frame>` ou `<iframe>` ont un titre"}, "core/audits/accessibility/heading-order.js | description": {"message": "Les en-têtes correctement classés qui respectent les niveaux transmettent la structure sémantique de la page, ce qui garantit une navigation plus aisée et permet d'identifier plus facilement dans quels cas utiliser les technologies d'assistance. [En savoir plus sur l'ordre des titres](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Les éléments d'en-tête ne sont pas classés séquentiellement par ordre décroissant"}, "core/audits/accessibility/heading-order.js | title": {"message": "Les éléments d'en-tête sont classés séquentiellement par ordre décroissant"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Lorsqu'une page ne spécifie pas d'attribut `lang`, les lecteurs d'écran considèrent qu'elle est rédigée dans la langue par défaut sélectionnée au moment de leur configuration par l'utilisateur. Si la page n'est pas rédigée dans cette langue par défaut, les lecteurs d'écran risquent de ne pas énoncer correctement son contenu. [En savoir plus sur l'attribut `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "L'élément `<html>` n'a pas d'attribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "L'élément `<html>` contient un attribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Le fait de spécifier une [langue BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valide permet d'aider les lecteurs d'écran à énoncer correctement le texte. [Découvrez comment utiliser l'attribut `lang`.](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "La valeur de l'attribut `[lang]` de l'élément `<html>` n'est pas valide."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "La valeur de l'attribut `[lang]` de l'élément `<html>` est valide"}, "core/audits/accessibility/image-alt.js | description": {"message": "Les éléments informatifs doivent contenir un texte de substitution court et descriptif. L'attribut alt peut rester vide pour les éléments décoratifs. [En savoir plus sur l'attribut `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Des éléments d'image n'ont pas d'attribut `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Les éléments d'image possèdent des attributs `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Lorsqu'une image est utilisée comme bouton `<input>`, vous pouvez aider les utilisateurs de lecteurs d'écran à comprendre son utilité en ajoutant un texte de substitution. [En savoir plus sur le texte de substitution d'une image d'entrée](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Les éléments `<input type=\"image\">` ne contiennent pas de texte `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Les éléments `<input type=\"image\">` contiennent du texte `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Les libellés permettent de s'assurer que les éléments de contrôle des formulaires sont énoncés correctement par les technologies d'assistance, comme les lecteurs d'écran. [En savoir plus sur les libellés d'éléments de formulaires](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Les éléments de formulaire ne sont pas associés à des libellés"}, "core/audits/accessibility/label.js | title": {"message": "Les éléments de formulaire sont associés à des libellés"}, "core/audits/accessibility/link-name.js | description": {"message": "Rédigez du texte visible et unique pour les liens (et pour le texte de substitution des images, si vous vous en servez dans des liens), afin que les utilisateurs de lecteurs d'écran puissent facilement positionner le curseur dessus et bénéficient d'une meilleure expérience de navigation. [Découvrez comment rendre les liens accessibles.](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Les liens n'ont pas de nom visible"}, "core/audits/accessibility/link-name.js | title": {"message": "Les liens ont un nom visible"}, "core/audits/accessibility/list.js | description": {"message": "Les lecteurs d'écran ont une façon spécifique d'énoncer les listes. Pour leur permettre de donner de bons résultats, pensez à bien structurer ces dernières. [En savoir plus sur la bonne structuration des listes](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Les listes ne contiennent pas uniquement des éléments `<li>` et des éléments de type script (`<script>` et `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Les listes contiennent uniquement des éléments `<li>` et des éléments de type script (`<script>` et `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Les lecteurs d'écran requièrent que les éléments de liste (`<li>`) soient contenus dans un élément parent `<ul>`, `<ol>` ou `<menu>` pour les énoncer correctement. [En savoir plus sur la bonne structuration des listes](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Les éléments de liste (`<li>`) ne sont pas inclus dans des éléments parents `<ul>`, `<ol>` ou `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Les éléments de liste (`<li>`) sont inclus dans des éléments parents `<ul>`, `<ol>` ou `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Les utilisateurs ne s'attendent pas à ce qu'une page s'actualise automatiquement. De plus, lorsque cela se produit, le curseur est aussitôt repositionné en haut de la page. Cela peut générer de la frustration et perturber l'expérience utilisateur. [En savoir plus sur la balise Meta refresh](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Le document utilise une balise Meta `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Le document n'utilise pas de balise Meta `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "La désactivation de la fonction de zoom peut être problématique pour les utilisateurs qui souffrent d'une déficience visuelle et qui ont besoin d'agrandir le contenu d'une page Web pour en saisir le sens. [En savoir plus sur la balise Meta viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "L'attribut `[user-scalable=\"no\"]` est utilisé dans l'élément `<meta name=\"viewport\">`, ou l'attribut `[maximum-scale]` est inférieur à 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` n'est pas utilisé dans l'élément `<meta name=\"viewport\">`, et l'attribut `[maximum-scale]` n'est pas inférieur à 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Les lecteurs d'écran ne peuvent pas traduire les contenus non textuels. En ajoutant un texte de substitution aux éléments `<object>`, vous aiderez les lecteurs d'écran à transmettre votre message aux utilisateurs. [En savoir plus sur le texte de substitution aux éléments `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Les éléments `<object>` ne contiennent pas de texte de substitution"}, "core/audits/accessibility/object-alt.js | title": {"message": "Les éléments `<object>` contiennent du texte de substitution"}, "core/audits/accessibility/tabindex.js | description": {"message": "Une valeur supérieure à 0 implique un ordre de navigation explicite. Bien que cela soit valide d'un point de vue technique, cela crée souvent une expérience frustrante pour les utilisateurs qui s'appuient sur des technologies d'assistance. [En savoir plus sur l'attribut `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Certains éléments ont une valeur `[tabindex]` supérieure à 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Aucun élément n'a de valeur `[tabindex]` supérieure à 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Les lecteurs d'écran proposent des fonctionnalités qui permettent de naviguer plus simplement dans les tableaux. En vous assurant que les cellules `<td>` qui comportent l'attribut `[headers]` fassent référence à d'autres cellules dans le même tableau uniquement, vous pourrez améliorer l'expérience des utilisateurs de lecteurs d'écran. [En savoir plus sur l'attribut `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Les cellules d'un élément `<table>` qui utilisent l'attribut `[headers]` font référence à un élément `id` ne figurant pas dans le même tableau."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Les cellules d'un élément `<table>` qui utilisent l'attribut `[headers]` font référence à des cellules figurant dans le même tableau."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Les lecteurs d'écran proposent des fonctionnalités qui permettent de naviguer plus simplement dans les tableaux. En vous assurant que les en-têtes de tableaux fassent toujours référence à un ensemble de cellules spécifique, vous pourrez améliorer l'expérience des utilisateurs de lecteurs d'écran. [En savoir plus sur les en-têtes de tableaux](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Les éléments `<th>` et ceux portant l'attribut `[role=\"columnheader\"/\"rowheader\"]` ne décrivent aucune cellule de données."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Les éléments `<th>` et ceux portant l'attribut `[role=\"columnheader\"/\"rowheader\"]` décrivent des cellules de données."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Le fait de spécifier une [langue BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valide pour les éléments permet de s'assurer que le texte sera prononcé correctement par les lecteurs d'écran. [<PERSON><PERSON><PERSON><PERSON><PERSON>z comment utiliser l'attribut `lang`.](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "La valeur des attributs `[lang]` n'est pas valide"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Les attributs `[lang]` ont une valeur valide"}, "core/audits/accessibility/video-caption.js | description": {"message": "Le fait d'ajouter des sous-titres à une vidéo rend celle-ci plus accessible aux personnes sourdes et malentendantes. [En savoir plus sur les sous-titres de vidéos](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Les éléments `<video>` ne contiennent pas d'élément `<track>` avec `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Les éléments `<video>` contiennent un élément `<track>` avec `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Valeur actuelle"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON> sugg<PERSON>"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` permet aux utilisateurs d'envoyer des formulaires plus rapidement. Pour leur faire gagner du temps, envisagez d'activer cette fonctionnalité en définissant l'attribut `autocomplete` sur une valeur valide. [En savoir plus sur `autocomplete` dans les formulaires](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Les éléments `<input>` ne possèdent pas d'attributs `autocomplete` corrects"}, "core/audits/autocomplete.js | manualReview": {"message": "Nécessite une vérification manuelle"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Vérifiez l'ordre des jetons"}, "core/audits/autocomplete.js | title": {"message": "Les éléments `<input>` utilisent correctement `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "<PERSON><PERSON>(s) `autocomplete` : \"{token}\" n'est pas valide dans {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Vérifier l'ordre des jetons : \"{tokens}\" dans {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Action possible"}, "core/audits/bf-cache.js | description": {"message": "La navigation consiste généralement à revenir à une page précédente ou retourner à une page suivante. Le cache amélioré peut accélérer ce type de navigation. [En savoir plus sur le cache amélioré](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motif d'échec}one{# motif d'échec}other{# motifs d'échec}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "<PERSON><PERSON><PERSON> de l'échec"}, "core/audits/bf-cache.js | failureTitle": {"message": "La page a empêché la restauration du cache amélioré"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Type d'échec"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Aucune action possible"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Assistance pour navigateur en attente"}, "core/audits/bf-cache.js | title": {"message": "La page n'a pas empêché la restauration du cache amélioré"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Les extensions Chrome ont eu un impact négatif sur les performances de chargement de la page. Essayez de contrôler la page en mode navigation privée ou depuis un profil Chrome sans extensions."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Évaluation des scripts"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Analyse des scripts"}, "core/audits/bootup-time.js | columnTotal": {"message": "Temps CPU total"}, "core/audits/bootup-time.js | description": {"message": "Envisagez de réduire le temps consacré à l'analyse, la compilation et l'exécution de JavaScript. La livraison de charges utiles JavaScript plus petites peut vous aider. [Découvrez comment réduire le temps d'exécution de JavaScript.](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> le temps d'exécution de JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Délai d'exécution de JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Supprimez les modules JavaScript volumineux et en double de vos groupes pour réduire les débits d'octets superflus sur le réseau. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Supprimez les modules en double dans les groupes JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Les grandes images GIF sont inefficaces pour diffuser du contenu animé. Envisagez d'utiliser des vidéos MPEG4/WebM pour les animations et PNG/WebP pour les images statiques au lieu d'images GIF afin d'économiser des octets réseau. [En savoir plus sur les formats vidéo efficaces](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Utilisez des formats vidéo pour le contenu animé"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Les polyfills et les transformations permettent aux anciens navigateurs d'utiliser les nouvelles fonctionnalités JavaScript. Dans la majorité des cas cependant, ils ne sont pas nécessaires aux navigateurs récents. Adoptez une stratégie de déploiement de script récente pour votre groupe JavaScript : utilisez la détection de fonctionnalité module/nomodule pour réduire la quantité de code envoyée aux navigateurs récents tout en continuant de prendre en charge les plus anciens. [Découvrez comment utiliser le code JavaScript récent.](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Évitez d'utiliser de l'ancien code JavaScript dans les navigateurs récents"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Les formats d'image comme WebP et AVIF proposent souvent une meilleure compression que PNG et JPEG. Par conséquent, les téléchargements sont plus rapides et la consommation de données est réduite. [En savoir plus sur les formats d'image récents](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Diffusez des images aux formats nouvelle génération"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Envisagez de charger des images masquées ou hors écran après le chargement de toutes les ressources essentielles afin de réduire le délai avant interactivité. [Découvrez comment différer les images hors écran.](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> le chargement des images hors écran"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Des ressources bloquent la première visualisation (first paint) de votre page. Envisagez de diffuser des feuilles JS/CSS essentielles en ligne et de différer la diffusion de toutes les feuilles JS/de style non essentielles. [Découvrez comment éliminer les ressources qui bloquent l'affichage.](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> les ressources qui bloquent le rendu"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Les charges utiles des grands réseaux coûtent de l'argent réel aux utilisateurs et sont fortement corrélées aux délais de chargement interminables. [Découvrez comment réduire la taille des charges utiles.](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "La taille totale était de {totalBytes, number, bytes} Kio"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Évitez d'énormes charges utiles de réseau"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "<PERSON><PERSON><PERSON> d'énormes charges utiles de réseau"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "La minimisation des fichiers CSS peut réduire la taille des charges utiles de réseau. [Découvrez comment minimiser des fichiers CSS.](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Ré<PERSON><PERSON>z la taille des ressources CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "La minimisation des fichiers JavaScript peut réduire la taille des charges utiles et la durée d'analyse des scripts. [Découvrez comment les minimiser.](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "R<PERSON><PERSON><PERSON>z la taille des ressources JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Réduisez les règles inutilisées des feuilles de style et différez les ressources CSS non utilisées pour le contenu au-dessus de la ligne de flottaison afin de réduire la quantité d'octets consommés par l'activité réseau. [Découvrez comment réduire les ressources CSS non utilisées.](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Réduisez les ressources CSS inutilisées"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Réduisez les ressources JavaScript inutilisées et différez le chargement des scripts tant qu'ils ne sont pas requis afin de réduire la quantité d'octets consommés par l'activité réseau. [Découvrez comment réduire les ressources JavaScript inutilisées.](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Réduisez les ressources JavaScript inutilisées"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Une longue durée de vie du cache peut accélérer les visites répétées sur votre page. [En savoir plus sur les règles efficaces liées au cache](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 ressource trouvée}one{# ressource trouvée}other{# ressources trouvées}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Diffusez des éléments statiques grâce à des règles de cache efficaces"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Utiliser des règles de cache efficaces sur les éléments statiques"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Les images optimisées se chargent plus rapidement et consomment moins de données mobiles. [Découvrez comment encoder efficacement des images.](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Encodez les images de manière efficace"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensions réelles"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensions affichées"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Images plus grandes que leur taille affichée"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Images appropriées pour leur taille affichée"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Diffusez des images de taille appropriée afin d'économiser des données mobiles et de réduire le temps de chargement. [Découvrez comment dimensionner les images.](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Dimensionnez correctement les images"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Les ressources textuelles doivent être diffusées compressées (Gzip, Deflate ou Brotli) pour réduire le nombre total d'octets du réseau. [En savoir plus sur la compression de texte](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Activez la compression de texte"}, "core/audits/content-width.js | description": {"message": "Si la largeur du contenu de votre appli ne correspond pas à la largeur de la fenêtre d'affichage, il se peut que votre appli ne soit pas optimisée pour les écrans mobiles. [Découvrez comment dimensionner le contenu de la fenêtre d'affichage.](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "La dimension de la fenêtre d'affichage ({innerWidth} pixels) ne correspond pas à la taille de la fenêtre ({outerWidth} pixels)."}, "core/audits/content-width.js | failureTitle": {"message": "Le contenu n'est pas correctement dimensionné pour la fenêtre d'affichage"}, "core/audits/content-width.js | title": {"message": "Le contenu est correctement dimensionné pour la fenêtre d'affichage"}, "core/audits/critical-request-chains.js | description": {"message": "Les chaînes de demandes critiques ci-dessous vous montrent quelles ressources sont chargées avec une priorité élevée. Envisagez de réduire la longueur des chaînes et la taille de téléchargement des ressources ou de reporter le téléchargement de ressources inutiles afin d'améliorer le chargement des pages. [Découvrez comment éviter de créer des chaînes de demandes critiques.](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 chaîne trouvée}one{# chaîne trouvée}other{# chaînes trouvées}}"}, "core/audits/critical-request-chains.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> de créer des chaînes de requêtes critiques"}, "core/audits/csp-xss.js | columnDirective": {"message": "Directive"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravité"}, "core/audits/csp-xss.js | description": {"message": "Une CSP (Content Security Policy) efficace réduit considérablement le risque d'attaques de script intersites (XSS). [Découvrez comment utiliser une CSP pour empêcher les attaques XSS.](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntaxe"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "La page contient une CSP définie dans une balise <meta>. Envisagez de déplacer la CSP vers un en-tête HTTP ou de définir une autre CSP stricte dans un en-tête HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Aucune CSP trouvée en mode de mise en conformité"}, "core/audits/csp-xss.js | title": {"message": "Garantir l'efficacité de la CSP contre les attaques XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "API obsolète/Avertissement"}, "core/audits/deprecations.js | columnLine": {"message": "Ligne"}, "core/audits/deprecations.js | description": {"message": "Les API obsolètes seront finalement supprimées du navigateur. [En savoir plus sur les API obsolètes](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 avertissement détecté}one{# avertissement détecté}other{# avertissements détectés}}"}, "core/audits/deprecations.js | failureTitle": {"message": "API obsolètes utilisées"}, "core/audits/deprecations.js | title": {"message": "La page n'utilise pas d'API obsolètes"}, "core/audits/dobetterweb/charset.js | description": {"message": "La déclaration d'encodage des caractères est obligatoire. Elle peut être effectuée avec une balise `<meta>` dans les 1 024 premiers octets du code HTML, ou dans l'en-tête de réponse HTTP Content-Type. [Découvrez comment déclarer l'encodage des caractères.](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "La déclaration de charset est manquante ou intervient trop tard dans le code HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Le charset est défini correctement"}, "core/audits/dobetterweb/doctype.js | description": {"message": "La spécification d'un attribut doctype empêche le navigateur de passer en mode quirks. [En savoir plus sur la déclaration d'un attribut doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Le nom de l'attribut doctype doit être la chaîne `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Le document contient un `doctype` qui déclenche `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Le document doit contenir un attribut doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "La chaîne publicId est censée être vide"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "La chaîne systemId est censée être vide"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Le document contient un `doctype` qui déclenche `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "La page n'a pas d'attribut doctype HTML, ce qui déclenche le mode quirks"}, "core/audits/dobetterweb/doctype.js | title": {"message": "La page n'a pas d'attribut doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistique"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Un grand DOM sollicite davantage la mémoire, et entraîne de plus longs [calculs de style](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) et de coûteux [ajustements de la mise en page](https://developers.google.com/speed/articles/reflow). [Découvrez comment éviter une taille de DOM excessive.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 élément}one{# élément}other{# éléments}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "É<PERSON><PERSON>z une taille excessive de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profondeur maximum de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Nombre total d'éléments DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Nombre maximal d'éléments enfants"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "<PERSON><PERSON>ter une taille excessive de DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Les utilisateurs se méfient des sites qui demandent leur position sans contexte. Envisagez plutôt d'associer la demande à une action de l'utilisateur. [En savoir plus sur l'autorisation de géolocalisation](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Demandes d'autorisation de géolocalisation lors du chargement de page"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Aucune autorisation de géolocalisation n'est demandée au chargement de la page"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Type de problème"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Les problèmes enregistrés dans le panneau `Issues` des outils de développement Chrome indiquent des problèmes non résolus. Ceux-ci peuvent être dus à des requêtes réseau qui ont échoué, à des contrôles de sécurité insuffisants ou à d'autres problèmes du navigateur. Ouvrez le panneau \"Issues\" dans les outils de développement Chrome pour en savoir plus sur chaque problème."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Des problèmes ont été enregistrés dans le panneau `Issues` des outils de développement Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloqué par le règlement multi-origines"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Utilisation intensive de ressources par les annonces"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Aucun problème dans le panneau `Issues` des outils de développement Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Version"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Toutes les bibliothèques JavaScript frontales détectées sur la page. [En savoir plus sur cet audit de diagnostic lié à la détection de bibliothèques JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Bibliothèques JavaScript détectées"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Pour les utilisateurs rencontrant des problèmes de connexion lente, les scripts externes injectés dynamiquement via `document.write()` peuvent retarder le chargement des pages de plusieurs dizaines de secondes. [Découvrez comment éviter document.write().](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "<PERSON><PERSON>te `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Les utilisateurs se méfient des sites qui demandent à envoyer des notifications sans contexte. Envisagez plutôt d'associer la demande à des gestes de l'utilisateur. [Découvrez comment obtenir de façon responsable une autorisation pour les notifications.](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Demandes d'autorisation d'envoi de notifications lors du chargement de page"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Aucune autorisation d'envoi de notifications n'est demandée au chargement de la page"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocole"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Le protocole HTTP/2 offre de nombreux avantages par rapport à HTTP/1.1, comme les en-têtes binaires et le multiplexage. [En savoir plus sur le protocole HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 requête non traitée via le protocole HTTP/2}one{# requête non traitée via le protocole HTTP/2}other{# requêtes non traitées via le protocole HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Utilisez HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Envisagez de marquer vos écouteurs d'événements tactiles et à la molette comme `passive` pour améliorer les performances de défilement de votre page. [En savoir plus sur l'utilisation d'écouteurs d'événements passifs](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "La page n'utilise pas d'écouteurs d'événements passifs pour améliorer les performances de défilement"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "La page utilise des écouteurs d'événements passifs pour améliorer les performances de défilement"}, "core/audits/errors-in-console.js | description": {"message": "Les erreurs enregistrées dans la console indiquent des problèmes non résolus. Ceux-ci peuvent être dus à des requêtes réseau qui ont échoué et à d'autres problèmes du navigateur. [En savoir plus sur ces erreurs dans l'audit de diagnostic de la console](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Les erreurs de navigateur ont été enregistrées dans la console"}, "core/audits/errors-in-console.js | title": {"message": "Aucune erreur de navigateur enregistrée dans la console"}, "core/audits/font-display.js | description": {"message": "Utilisez la fonctionnalité `font-display` CSS afin que le texte soit visible par l'utilisateur pendant le chargement des polices Web. [En savoir plus sur `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "Assurez-vous que le texte reste visible pendant le chargement des polices Web"}, "core/audits/font-display.js | title": {"message": "La totalité du texte reste visible pendant le chargement des polices Web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountF<PERSON><PERSON><PERSON><PERSON>,plural, =1{Lighthouse n'a pas pu vérifier automatiquement la valeur `font-display` pour l'origine {fontOrigin}.}one{Lighthouse n'a pas pu vérifier automatiquement la valeur `font-display` pour l'origine {fontOrigin}.}other{Lighthouse n'a pas pu vérifier automatiquement les valeurs `font-display` pour l'origine {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Format (image réelle)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Format (image affichée)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Les dimensions d'affichage des images doivent correspondre au format naturel. [En savoir plus sur le format de l'image](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Images affichées dans un format incorrect"}, "core/audits/image-aspect-ratio.js | title": {"message": "Images affichées au bon format"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON> r<PERSON>"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON> a<PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON>"}, "core/audits/image-size-responsive.js | description": {"message": "Pour que la clarté de l'image soit optimale, ses dimensions naturelles doivent être proportionnelles à la taille d'affichage et au taux de pixels. [Découvrez comment fournir des images responsives.](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Images diffusées en basse résolution"}, "core/audits/image-size-responsive.js | title": {"message": "Images diffusées dans la résolution appropriée"}, "core/audits/installable-manifest.js | already-installed": {"message": "L'application est déjà installée"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Impossible de télécharger une icône requise depuis le fichier manifeste"}, "core/audits/installable-manifest.js | columnValue": {"message": "<PERSON><PERSON><PERSON> de l'échec"}, "core/audits/installable-manifest.js | description": {"message": "Service worker est une technologie qui permet à votre appli d'exploiter de nombreuses fonctionnalités propres aux progressive web apps, comme le fonctionnement hors connexion, l'ajout à l'écran d'accueil et les notifications push. Lorsqu'un service worker et un fichier manifeste sont correctement implémentés, les utilisateurs peuvent être invités à ajouter votre appli à leur écran d'accueil par le biais de leur navigateur. Cette fonctionnalité peut contribuer à une hausse de l'engagement. [En savoir plus sur les exigences liées à l'implémentation du fichier manifeste](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motif}one{# motif}other{# motifs}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Le fichier manifeste ou le service worker de l'application Web ne respectent pas les conditions d'installation requises"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "L'URL de l'application Play Store et l'ID Play Store ne correspondent pas"}, "core/audits/installable-manifest.js | in-incognito": {"message": "La page est chargée dans une fenêtre de navigation privée"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "La propriété \"display\" du fichier manifeste doit avoir la valeur \"standalone\", \"fullscreen\" ou \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Le fichier manifeste contient le champ \"display_override\" et le premier mode d'affichage pris en charge doit être \"standalone\", \"fullscreen\" ou \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Le fichier manifeste n'a pas pu être récupéré, est vide ou n'a pas pu être analysé"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "L'URL du fichier manifeste a changé pendant la récupération du fichier manifeste."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Le fichier manifeste ne contient ni un champ \"name\", ni un champ \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Le fichier manifeste ne contient pas d'icône éligible : format PNG, SVG ou WebP d'au moins {value0} px, avec l'attribut de taille défini et l'attribut de fonction non défini ou défini sur \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Aucune icône fournie ne fait au moins {value0} px carré(s) au format PNG, SVG ou WebP, avec l'attribut de fonction non défini ou défini sur \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "L'icône téléchargée était vide ou corrompue"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Aucun ID Play Store fourni"}, "core/audits/installable-manifest.js | no-manifest": {"message": "La page n'a aucune URL <lien> de fichier manifeste"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Aucun service worker correspondant n'a <PERSON><PERSON> détecté. Vous devrez peut-être recharger la page ou vérifier que le champ d'application du service worker pour la page actuelle inclut le champ d'application et l'URL de démarrage du fichier manifeste."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Impossible de contrôler le service worker sans un champ \"start_url\" dans le fichier manifeste"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Le code d'erreur d'installation \"{errorId}\" n'est pas reconnu"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "L'origine de la page n'est pas sécurisée"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "La page n'est pas chargée dans le frame principal"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "La page ne fonctionne pas hors connexion"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA a été désinstallé, et les vérifications d'installabilité sont en cours de réinitialisation."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "La plate-forme d'applications indiquée n'est pas compatible avec Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Le fichier manifeste indique prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications est uniquement disponible dans la version bêta et les versions stables de Chrome sur Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse n'a pas pu détecter la présence d'un service worker. Veuillez réessayer avec une version plus récente de Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Le schéma d'URL du fichier manifeste ({scheme}) n'est pas compatible avec Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "L'URL de démarrage du fichier manifeste n'est pas valide"}, "core/audits/installable-manifest.js | title": {"message": "Le fichier manifeste et le service worker de l'application Web respectent les conditions d'installation requises"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Une URL du fichier manifeste contient un nom d'utilisateur, un mot de passe ou un port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "La page ne fonctionne pas hors connexion. La page ne sera pas considérée comme installable après le lancement de la version stable de Chrome 93 en août 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Autorisée"}, "core/audits/is-on-https.js | blocked": {"message": "Bloquée"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL non sécurisée"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON> de résol<PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Tous les sites doivent être protégés par le protocole HTTPS, même ceux qui ne traitent pas de données sensibles. Par conséquent, vous devez éviter le [contenu mixte](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), qui provoque le chargement de certaines ressources sur HTTP bien que la demande initiale soit diffusée via HTTPS. Le protocole HTTPS empêche les intrus de détourner ou d’écouter passivement les communications entre votre appli et les utilisateurs. Il constitue également une condition préalable à l'utilisation de HTTP/2 et de nombreuses nouvelles API de plates-formes Web. [En savoir plus sur le protocole HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 requête non sécurisée trouvée}one{# requête non sécurisée trouvée}other{# requêtes non sécurisées trouvées}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "La page n'utilise pas le protocole HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Requêtes HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Mise à niveau automatique au format HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Autorisée avec un avertissement"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Il s'agit de l'élément identifié comme \"Largest Contentful Paint\" dans la fenêtre d'affichage. [En savoir plus cette métrique](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Élément identifié comme \"Largest Contentful Paint\""}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contribution au CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Ces éléments DOM contribuent en grande partie au CLS de la page. [Découvrez comment améliorer le CLS.](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "<PERSON><PERSON><PERSON> les changements de mise en page importants"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Les images de la partie au-dessus de la ligne de flottaison qui ont un chargement différé sont rendues plus tard dans le cycle de vie de la page, ce qui peut retarder Largest Contentful Paint. [En savoir plus sur le chargement différé optimal](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "L'image Largest Contentful Paint a eu un chargement différé"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "L'image Largest Contentful Paint n'a pas eu de chargement différé"}, "core/audits/long-tasks.js | description": {"message": "Indique les tâches les plus longues du thread principal, ce qui est utile pour identifier celles qui entraînent le plus de retard. [Découvrez comment éviter les longues tâches du thread principal.](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# tâche longue trouvée}one{# tâche longue trouvée}other{# tâches longues trouvées}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> les tâches longues dans le thread principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Envisagez de réduire le temps consacré à l'analyse, la compilation et l'exécution de JavaScript. La livraison de charges utiles JavaScript plus petites peut vous aider. [Découvrez comment réduire le travail du thread principal.](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> le travail du thread principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "R<PERSON><PERSON><PERSON> le travail du thread principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Afin de toucher le plus grand nombre d'utilisateurs possible, les sites doivent fonctionner sur tous les principaux navigateurs. [En savoir plus sur la compatibilité multinavigateur](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Le site fonctionne sur différents navigateurs"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Veillez à ce que les URL de vos pages puissent être utilisées dans des liens profonds. En outre, chaque URL doit être unique afin de pouvoir être correctement partagée sur les médias sociaux. [En savoir plus sur l'ajout de liens profonds](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Chaque page a sa propre URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "La navigation sur les pages doit être rapide et fluide, même pour les utilisateurs avec une connexion lente. C'est un critère de performance fondamental pour les utilisateurs. [En savoir plus sur les transitions de pages](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "La navigation entre les différentes pages du site doit être rapide et fluide"}, "core/audits/maskable-icon.js | description": {"message": "Une icône masquable empêche l'apparition de bandes noires (format letterbox) et assure que l'image remplit totalement l'espace disponible lorsque l'appli est installée sur un appareil. [En savoir plus sur les icônes masquables du fichier manifeste](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Le fichier manifeste ne comporte pas d'icône masquable"}, "core/audits/maskable-icon.js | title": {"message": "Le fichier manifeste comporte une icône masquable"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulative Layout Shift mesure le mouvement des éléments visibles dans la fenêtre d'affichage. [En savoir plus sur cette métrique](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "La métrique \"Interaction to <PERSON> Paint\" mesure la réactivité de la page, c'est-à-dire le temps que celle-ci met à répondre de manière visible à l'entrée utilisateur. [En savoir plus sur cette métrique](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "La métrique \"First Contentful Paint\" indique le moment où le premier texte ou la première image sont affichés. [En savoir plus sur cette métrique](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "\"First Meaningful Paint\" mesure quand le contenu principal d'une page est visible. [En savoir plus sur cette métrique](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "La métrique Délai avant interactivité correspond au temps nécessaire pour que la page devienne entièrement interactive. [En savoir plus sur cette métrique](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "La métrique \"Largest Contentful Paint\" indique le moment où le texte le plus long ou l'image la plus grande sont affichés. [En savoir plus sur cette métrique](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Le retard maximal (Maximum Potential First Input Delay) auquel vos utilisateurs peuvent éventuellement être confrontés correspond à la durée de la tâche la plus longue. [En savoir plus sur la métrique \"Maximum Potential First Input Delay\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "La métrique \"Speed Index\" indique la rapidité avec laquelle le contenu d'une page est disponible. [En savoir plus sur cette métrique](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Somme en millisecondes de toutes les périodes entre le FCP et le délai avant interactivité, lorsque la durée de la tâche a dépassé 50 ms. [En savoir plus sur la métrique \"Total Blocking Time\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Les délais aller-retour (DAR) du réseau ont un impact important sur les performances. Si le DAR par rapport à un point d'origine est élevé, cela signifie que les performances des serveurs proches de l'utilisateur peuvent sans doute être améliorées. [En savoir plus sur le délai aller-retour](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON>-retour r<PERSON><PERSON>"}, "core/audits/network-server-latency.js | description": {"message": "La latence du serveur peut avoir une incidence sur les performances Web. Si la latence serveur d'une origine est élevée, cela signifie que le serveur est en surcharge ou que ses performances backend sont médiocres. [En savoir plus sur le temps de réponse du serveur](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Latences du backend serveur"}, "core/audits/no-unload-listeners.js | description": {"message": "L'événement `unload` ne se déclenche pas de manière fiable, et son analyse risque d'empêcher les optimisations du navigateur telles que la mise en cache des pages précédentes et suivantes. Utilisez plutôt les événements `pagehide` ou `visibilitychange`. [En savoir plus sur le déchargement des écouteurs d'événements](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Permet d'enregistrer un écouteur `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Permet d'éviter les écouteurs d'événements `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Les animations non composées peuvent être lentes et augmenter le CLS. [Découvrez comment éviter les animations non composées.](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# élément animé trouvé}one{# élément animé trouvé}other{# éléments animés trouvés}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "La propriété liée à filter peut déplacer des pixels"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "La cible contient une autre animation incompatible"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Le mode de composite de l'effet n'est pas \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "É<PERSON>ter les animations non composées"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "La propriété liée à transform dépend de la taille de la zone"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Propriété CSS incompatible : {properties}}one{Propriété CSS incompatible : {properties}}other{Propriétés CSS incompatibles : {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "L'effet comporte des paramètres de minutage non compatibles"}, "core/audits/performance-budget.js | description": {"message": "Maintenez le volume et la taille des requêtes réseau sous les objectifs définis par le budget de performances fourni. [En savoir plus sur les budgets de performances](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 requête}one{# requête}other{# requêtes}}"}, "core/audits/performance-budget.js | title": {"message": "Budget de performances"}, "core/audits/preload-fonts.js | description": {"message": "Préchargez les polices `optional` pour que les nouveaux visiteurs puissent les utiliser. [En savoir plus sur le préchargement des polices](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Les polices qui utilisent `font-display: optional` ne sont pas préchargées"}, "core/audits/preload-fonts.js | title": {"message": "Les polices qui utilisent `font-display: optional` sont préchargées"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Si l'élément LCP est ajouté de façon dynamique à la page, préchargez l'image pour améliorer le LCP. [En savoir plus sur le préchargement d'éléments LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Précharger l'image Largest Contentful Paint"}, "core/audits/redirects.js | description": {"message": "Les redirections entraînent des retards supplémentaires avant que la page ne puisse être chargée. [Découvrez comment éviter les redirections de page.](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Évi<PERSON>z les redirections de page multiples"}, "core/audits/resource-summary.js | description": {"message": "Pour définir des budgets liés à la quantité et à la taille des ressources de pages, ajoutez un fichier budget.json. [En savoir plus sur les budgets de performances](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 requête • {byteCount, number, bytes} Ki<PERSON>}one{# requête • {byteCount, number, bytes} Kio}other{# requêtes• {byteCount, number, bytes} Ki<PERSON>}}"}, "core/audits/resource-summary.js | title": {"message": "Réduisez au maximum le nombre de requêtes et la taille des transferts"}, "core/audits/seo/canonical.js | description": {"message": "Les liens canoniques suggèrent l'URL à afficher dans les résultats de recherche. [En savoir plus sur les liens canoniques](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Plusieurs URL en conflit ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL incorrecte ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "URL qui mène à un autre emplacement `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "N'est pas une URL absolue ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Pointe vers l'URL racine du domaine (la page d'accueil), et non vers une page de contenu équivalente"}, "core/audits/seo/canonical.js | failureTitle": {"message": "L'attribut `rel=canonical` du document n'est pas valide"}, "core/audits/seo/canonical.js | title": {"message": "L'attribut `rel=canonical` du document est valide"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Lien non explorable"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Les moteurs de recherche peuvent utiliser les attributs `href` des liens pour explorer les sites Web. Assurez-vous que l'attribut `href` des éléments d'ancrage pointe vers une destination appropriée, pour que davantage de pages du site puissent être détectées. [Dé<PERSON>uvrez comment rendre les liens explorables.](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Les liens ne peuvent pas être explorés"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Les liens peuvent être explorés"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Texte supplémentaire illisible"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Taille de police"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% du texte de la page"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Les tailles de police inférieures à 12 pixels sont trop petites pour être lisibles et nécessitent que les visiteurs sur la version mobile pincent l'écran pour zoomer et lire le texte. Veuillez utiliser une police de texte de plus de 12 pixels sur plus de 60 % du texte de la page. [En savoir plus sur les tailles de police lisibles](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} du texte lisibles"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Le texte est illisible, car aucune balise Meta de fenêtre d'affichage n'est optimisée pour les écrans mobiles."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Les tailles de police utilisées dans le document ne sont pas lisibles"}, "core/audits/seo/font-size.js | legibleText": {"message": "Texte lisible"}, "core/audits/seo/font-size.js | title": {"message": "Le document utilise des tailles de police lisibles"}, "core/audits/seo/hreflang.js | description": {"message": "Les liens hreflang indiquent aux moteurs de recherche la version de la page qu'ils doivent présenter dans les résultats de recherche pour une page ou une région donnée. [En savoir plus sur `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Le document ne contient pas d'attribut `hreflang` valide"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "<PERSON>ur href relative"}, "core/audits/seo/hreflang.js | title": {"message": "L'attribut `hreflang` du document est valide"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Code de langue inattendu"}, "core/audits/seo/http-status-code.js | description": {"message": "Les pages renvoyant des codes d'état HTTP d'échec peuvent ne pas être indexées correctement. [En savoir plus sur les codes d'état HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "La page renvoie un code d'état HTTP d'échec"}, "core/audits/seo/http-status-code.js | title": {"message": "La page renvoie un code d'état HTTP de réussite"}, "core/audits/seo/is-crawlable.js | description": {"message": "Les moteurs de recherche ne peuvent pas inclure vos pages dans les résultats de recherche s'ils ne sont pas autorisés à les explorer. [En savoir plus sur les instructions liées au robot d'exploration](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "L'indexation de la page est bloquée"}, "core/audits/seo/is-crawlable.js | title": {"message": "L'indexation de cette page n'est pas bloquée"}, "core/audits/seo/link-text.js | description": {"message": "Le texte descriptif d'un lien aide les moteurs de recherche à comprendre votre contenu. [Découvrez comment rendre les liens plus accessibles.](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 lien trouvé}one{# lien trouvé}other{# liens trouvés}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Les liens ne contiennent pas de texte descriptif"}, "core/audits/seo/link-text.js | title": {"message": "Les liens contiennent un texte descriptif"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Exécutez l'[outil de test des données structurées](https://search.google.com/structured-data/testing-tool/) et le [validateur Lint de données structurées](http://linter.structured-data.org/) pour valider les données structurées. [En savoir plus sur les données structurées](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Les données structurées sont valides"}, "core/audits/seo/meta-description.js | description": {"message": "Les résultats de recherche peuvent inclure des attributs \"meta description\" pour résumer de façon concise le contenu de la page. [En savoir plus sur la meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Le texte de la description est vide."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Le document ne contient pas d'attribut \"meta description\""}, "core/audits/seo/meta-description.js | title": {"message": "Le document contient un attribut \"meta description\""}, "core/audits/seo/plugins.js | description": {"message": "Les moteurs de recherche ne peuvent pas indexer le contenu des plug-ins, et de nombreux appareils limitent l'utilisation de ces derniers, voire ne les acceptent pas. [Découvrez comment éviter les plug-ins.](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Le document utilise des plug-ins"}, "core/audits/seo/plugins.js | title": {"message": "Le document évite les plug-ins"}, "core/audits/seo/robots-txt.js | description": {"message": "Si votre fichier robots.txt n'est pas créé correctement, il se peut que les robots d'exploration ne puissent pas comprendre comment votre site Web doit être exploré ou indexé. [En savoir plus sur les fichiers robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "La requête pour le fichier robots.txt a renvoyé l'état HTTP {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 erreur détectée}one{# erreur détectée}other{# erreurs détectées}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse n'est pas parvenu à télécharger le fichier robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Le fichier robots.txt n'est pas valide"}, "core/audits/seo/robots-txt.js | title": {"message": "Le fichier robots.txt est valide"}, "core/audits/seo/tap-targets.js | description": {"message": "Les éléments interactifs comme les boutons et les liens doivent être suffisamment larges (48 x 48 pixels) et avoir suffisamment d'espace autour d'eux pour que l'utilisateur puisse appuyer facilement dessus sans appuyer en même temps sur d'autres éléments. [En savoir plus sur les éléments tactiles](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} des éléments tactiles sont correctement dimensionnés"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Les éléments tactiles sont trop petits, car aucune balise Meta de fenêtre d'affichage n'est optimisée pour les écrans mobiles"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Les éléments tactiles ne sont pas dimensionnés correctement"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Cible en chevauchement"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Élément tactile"}, "core/audits/seo/tap-targets.js | title": {"message": "Les éléments tactiles sont dimensionnés correctement"}, "core/audits/server-response-time.js | description": {"message": "Le temps de réponse du serveur pour le document principal doit rester court, car toutes les autres requêtes en dépendent. [En savoir plus sur la métrique \"Time to First Byte\"](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "Le document racine a pris {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Réduire le temps de réponse initial du serveur"}, "core/audits/server-response-time.js | title": {"message": "Le temps de réponse initial du serveur était court"}, "core/audits/service-worker.js | description": {"message": "Un service worker est une technologie qui permet à votre appli d'exploiter de nombreuses fonctionnalités propres aux progressive web apps, comme le fonctionnement hors connexion, l'ajout à un écran d'accueil et les notifications push. [En savoir plus sur les service workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Un service worker contrôle cette page. <PERSON><PERSON><PERSON><PERSON>, aucun attribut `start_url` n'a été trouvé en raison d'un échec lors de l'analyse du fichier manifeste (JSON non valide)"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Un service worker contrôle cette page. <PERSON><PERSON><PERSON><PERSON>, l'attribut `start_url` ({startUrl}) est situé en dehors du champ d'application du service worker ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Un service worker contrôle cette page. <PERSON><PERSON><PERSON><PERSON>, aucun attribut `start_url` n'a été trouvé, car le fichier manifeste n'a pas pu être récupéré."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Plusieurs service workers existent pour cette origine. Toutefois, la page ({pageUrl}) est située en dehors du champ d'application."}, "core/audits/service-worker.js | failureTitle": {"message": "Aucun service worker de contrôle de la page et de `start_url` n'est enregistré"}, "core/audits/service-worker.js | title": {"message": "Un service worker de contrôle de la page et de `start_url` est enregistré"}, "core/audits/splash-screen.js | description": {"message": "Avec un écran de démarrage à thème, vous garantissez une expérience de haute qualité aux utilisateurs qui lancent votre appli depuis leur écran d'accueil. [En savoir plus sur les écrans de démarrage](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "Écran de démarrage personnalisé non disponible"}, "core/audits/splash-screen.js | title": {"message": "Écran de démarrage personnalisé disponible"}, "core/audits/themed-omnibox.js | description": {"message": "Vous pouvez définir un thème assorti à votre site pour la barre d'adresse du navigateur. [En savoir plus sur les thèmes de la barre d'adresse](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Aucune couleur de thème n'est configurée pour la barre d'adresse."}, "core/audits/themed-omnibox.js | title": {"message": "Une couleur de thème est configurée pour la barre d'adresse."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (témoignages)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (réseau social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (vidéo)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produit"}, "core/audits/third-party-facades.js | description": {"message": "Certaines intégrations tierces peuvent être chargées de manière différée. Vous pouvez envisager de les remplacer par une façade tant qu'elles ne sont pas requises. [Découvrez comment différer le chargement de codes tiers par une façade.](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# autre façade disponible}one{# autre façade disponible}other{# autres façades disponibles}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Certaines ressources tierces peuvent être chargées en différé avec une façade"}, "core/audits/third-party-facades.js | title": {"message": "Ressources tierces pouvant être chargées de façon différée avec des façades"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Tiers"}, "core/audits/third-party-summary.js | description": {"message": "Le code tiers peut affecter considérablement les performances de chargement des pages. Limitez le nombre de fournisseurs tiers redondants, et essayez de charger du code tiers une fois le chargement de votre page terminé. [Découvrez comment réduire l'impact du code tiers.](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "Le thread principal a été bloqué par du code tiers pendant {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Réduire l'impact du code tiers"}, "core/audits/third-party-summary.js | title": {"message": "Réduire au maximum l'utilisation de code tiers"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Mesure"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Statistique"}, "core/audits/timing-budget.js | description": {"message": "Définit un budget de minutage pour vous permettre de surveiller les performances de votre site. Les sites performants se chargent plus vite et répondent plus rapidement aux événements de saisie des utilisateurs. [En savoir plus sur les budgets de performances](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Budget de minutage"}, "core/audits/unsized-images.js | description": {"message": "Indiquez une largeur et une hauteur explicites sur les éléments d'image afin de réduire les décalages de mise en page et d'améliorer le CLS. [Découvrez comment définir les dimensions de l'image.](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Les éléments d'image ne possèdent pas de `width` ni de `height` explicites"}, "core/audits/unsized-images.js | title": {"message": "Les éléments d'image possèdent une `width` et une `height` explicites"}, "core/audits/user-timings.js | columnType": {"message": "Type"}, "core/audits/user-timings.js | description": {"message": "Envisagez de doter votre appli de l'API User Timing pour mesurer ses performances réelles lors d'expériences utilisateur clés. [En savoir plus sur les marques User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{[=1]1 temps utilisateur}one{# temps utilisateur}other{# temps utilisateur}}"}, "core/audits/user-timings.js | title": {"message": "Marques et mesures du temps utilisateur"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Un élément `<link rel=preconnect>` a été trouvé pour \"{securityOrigin}\", mais il n'a pas été utilisé par le navigateur. Vérifiez que vous utilisez correctement l'attribut `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Envisagez d'ajouter les indices de ressources `preconnect` ou `dns-prefetch` pour établir les premières connexions avec des origines tierces importantes. [Découvrez comment établir des préconnexions aux origines requises.](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Connectez-vous à l'avance aux origines souhaitées"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Plusieurs connexions `<link rel=preconnect>` ont été identifiées. Elles doivent être utilisées avec parcimonie et limitées aux origines les plus importantes."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Un élément `<link rel=preconnect>` a été trouvé pour \"{securityOrigin}\", mais il n'a pas été utilisé par le navigateur. N'utilisez `preconnect` que pour des origines importantes que la page demandera certainement."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Un préchargement `<link>` a été trouvé pour \"{preloadURL}\", mais il n'a pas été utilisé par le navigateur. Vérifiez que vous utilisez correctement l'attribut `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Envisagez d'utiliser `<link rel=preload>` pour hiérarchiser la récupération des ressources actuellement requises pour le chargement ultérieur de la page. [Découvrez comment précharger les requêtes clés.](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Préchargez les demandes clés"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL de mappage du code source"}, "core/audits/valid-source-maps.js | description": {"message": "Les mappages source traduisent le code minimisé pour obtenir le code source d'origine. Ce processus aide les développeurs à effectuer le débogage en phase de production. De plus, Lighthouse est en mesure de fournir d'autres renseignements. Envisagez de déployer des mappages source pour profiter de ces avantages. [En savoir plus sur les mappages source](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Mappages source manquants pour des fichiers JavaScript propriétaires volumineux"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Il manque un mappage source dans un fichier JavaScript volumineux"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Avertissement : 1 élément manquant dans `.sourcesContent`}one{Avertissement : # élément manquant dans `.sourcesContent`}other{Avertissement : # éléments manquants dans `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "La page contient des mappages source valides"}, "core/audits/viewport.js | description": {"message": "Un `<meta name=\"viewport\">` optimise votre appli pour les tailles d'écrans de mobiles, mais empêche aussi un [délai d'entrée utilisateur de 300 millisecondes](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [En savoir plus sur l'utilisation de la balise Meta viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Aucune balise `<meta name=\"viewport\">` trouvée"}, "core/audits/viewport.js | failureTitle": {"message": "Aucune balise `<meta name=\"viewport\">` ayant l'attribut `width` ou `initial-scale` n'est configurée"}, "core/audits/viewport.js | title": {"message": "Une balise `<meta name=\"viewport\">` ayant l'attribut `width` ou `initial-scale` est configurée"}, "core/audits/work-during-interaction.js | description": {"message": "Il s'agit du travail qui bloque le thread pendant la mesure de la valeur INP. [En savoir plus sur la métrique \"Interaction to Next Paint\"](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms pour l'événement \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Événement cible"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Réduire le travail lors d'une interaction clé"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "<PERSON><PERSON><PERSON> à l'entrée utilisateur"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON><PERSON><PERSON>sent<PERSON>"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Temps de traitement"}, "core/audits/work-during-interaction.js | title": {"message": "Réduire le travail lors d'une interaction clé"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Servez-vous de ces indications pour améliorer l'utilisation des éléments ARIA dans votre application et ainsi optimiser l'expérience des utilisateurs de technologies d'assistance, comme les lecteurs d'écran."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Servez-vous de ces indications pour fournir un contenu alternatif pour l'audio et la vidéo. Vous pourrez ainsi améliorer l'expérience des utilisateurs malvoyants ou malentendants."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio et vidéo"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ces indications présentent les bonnes pratiques courantes en matière d'accessibilité."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Bonnes pratiques"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ces vérifications permettent de connaître les possibilités d'[amélioration de l'accessibilité de vos applications Web](https://developer.chrome.com/docs/lighthouse/accessibility/). Seule une partie des problèmes d'accessibilité peut être détectée automatiquement. Il est donc conseillé d'effectuer un test manuel."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ces éléments concernent des zones qu'un outil de test automatique ne peut pas couvrir. Consultez notre guide sur la [réalisation d'un examen d'accessibilité](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accessibilité"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Servez-vous de ces indications pour améliorer la lisibilité de votre contenu."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Servez-vous de ces indications pour améliorer l'interprétation de votre contenu en fonction des différents paramètres régionaux choisis par les utilisateurs."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisation et localisation"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Servez-vous de ces indications pour améliorer la sémantique des éléments de contrôle de votre application. Vous optimiserez ainsi l'expérience des utilisateurs de technologies d'assistance, comme les lecteurs d'écran."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Noms et étiquettes"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Servez-vous de ces indications pour améliorer la navigation au clavier de votre application."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigation"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Servez-vous de ces indications pour améliorer l'expérience de lecture des listes ou tableaux de données en utilisant une technologie d'assistance, comme un lecteur d'écran."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tableaux et listes"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilité du navigateur"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Bonnes pratiques"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Général"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Fiabilité et sécurité"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Expérience utilisateur"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Les budgets de performances établissent des normes sur les performances de votre site."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Au niveau des budgets"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Plus d'informations sur les performances de votre application. Ces chiffres n'ont pas d'[incidence directe](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) sur le score lié aux performances."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostic"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "L'aspect le plus essentiel des performances est la rapidité avec laquelle les pixels sont affichés à l'écran. Statistiques clés : First Contentful Paint, First Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Amélioration de First Paint"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ces suggestions peuvent contribuer à charger votre page plus rapidement. En revanche, elles n'ont pas d'[incidence directe](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) sur le score lié aux performances."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Opportunités"}, "core/config/default-config.js | metricGroupTitle": {"message": "Statistiques"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Améliorez l'expérience globale de chargement, afin que la page soit réactive et disponible dès que possible. Statistiques clés : Time to Interactive, Speed Index"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Améliorations générales"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Performances"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Ces contrôles permettent de vérifier que les conditions requises pour les progressive web apps sont remplies. [Découvrez les critères d'une progressive web app de qualité.](https://web.dev/pwa-checklist/)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ces contrôles font partie des [vérifications de base de la checklist PWA](https://web.dev/pwa-checklist/), mais ne sont pas exécutés automatiquement par Lighthouse. Même s'ils n'ont pas d'influence sur votre score, il est important de les effectuer manuellement."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Possibilités d'installation"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimisation PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Ces vérifications confirment que votre page suit les conseils de base concernant le référencement naturel. De nombreux facteurs supplémentaires ne sont pas comptés par Lighthouse ici, mais peuvent affecter votre classement dans les résultats de recherche, y compris vos performances sur [Signaux Web essentiels](https://web.dev/learn-core-web-vitals/). [En savoir plus sur les Essentiels de la recherche Google](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Exécutez ces outils de validation supplémentaires sur votre site pour vérifier les bonnes pratiques de SEO complémentaires."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Rédigez votre code HTML de sorte à autoriser les robots d'exploration à analyser le contenu de votre application."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Bonnes pratiques relatives au contenu"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Pour que votre contenu apparaisse dans les résultats de recherche, les robots d'exploration doivent accéder à votre application."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Exploration et indexation"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Assurez-vous que vos pages sont adaptées aux mobiles, afin que les utilisateurs n'aient pas besoin de pincer l'écran ni de zoomer pour lire votre contenu. [Découvrez comment adapter vos pages aux mobiles.](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Adapté aux mobiles"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "L'appareil testé semble avoir un processeur plus lent qu'attendu par Lighthouse. Cela peut avoir des répercussions négatives sur votre score lié aux performances. En savoir plus sur le [calibrage d'un multiplicateur de ralentissement de processeur adapté](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Il est possible que cette page ne se charge pas comme prévu, car votre URL de test ({requested}) redirige vers {final}. Essayez de tester directement la seconde URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Le chargement de la page a été trop lent et a dépassé la limite de temps. Il est possible que les résultats soient incomplets."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Le délai pour vider le cache du navigateur a expiré. Réessayez d'auditer cette page et signalez un bug si le problème persiste."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{L'emplacement suivant peut contenir des données qui affectent les performances de chargement : {locations}. Effectuez un audit de cette page dans une fenêtre de navigation privée afin que ces ressources n'affectent pas vos scores.}one{L'emplacement suivant peut contenir des données qui affectent les performances de chargement : {locations}. Effectuez un audit de cette page dans une fenêtre de navigation privée afin que ces ressources n'affectent pas vos scores.}other{Les emplacements suivants peuvent contenir des données qui affectent les performances de chargement : : {locations}. Effectuez un audit de cette page dans une fenêtre de navigation privée afin que ces ressources n'affectent pas vos scores.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Le délai pour la suppression des données d'origine est dépassé. Réessayez d'auditer cette page et signalez un bug si le problème persiste."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Seules les pages chargées via une demande GET sont éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Seules les pages dont le code d'état est 2XX peuvent être mises en cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome a détecté une tentative d'exécuter JavaScript alors que la page se trouve dans le cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Les pages qui ont demandé une AppBanner ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Le cache amélioré est désactivé dans chrome://flags. Accédez à chrome://flags/#back-forward-cache pour l'activer en local sur cet appareil."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "La ligne de commande a désactivé le cache amélioré."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Le cache amélioré est désactivé en raison d'une mémoire insuffisante."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Le délégué n'accepte pas le cache amélioré."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Le cache amélioré est désactivé pour le prérendu."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "La page ne peut pas être mise en cache, car elle contient une instance BroadcastChannel avec des écouteurs inscrits."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Les pages qui contiennent l'en-tête cache-control:no-store ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Le cache a été effacé volontairement."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "La page a été exclue du cache afin de laisser la place à une autre."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Les pages contenant des plug-ins ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Les pages qui utilisent l'API FileChooser ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Les pages qui utilisent l'API File System Access ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Les pages qui utilisent Media Device Dispatcher ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "La page a été quittée alors qu'un lecteur multimédia s'exécutait."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Les pages qui utilisent l'API MediaSession et définissent un état de lecture ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Les pages qui utilisent l'API MediaSession et définissent des gestionnaires d'action ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Le cache amélioré est désactivé en raison du lecteur d'écran."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Les pages qui utilisent SecurityHandler ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Les pages qui utilisent l'API Serial ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Les pages qui utilisent l'API WebAuthentication ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Les pages qui utilisent l'API WebBluetooth ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Les pages qui utilisent l'API WebUSB ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Les pages qui utilisent un worker ou un worklet dédié ne sont pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "La page a été quittée avant que le document soit entièrement chargé."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "La page a été quittée alors qu'une bannière d'appli était présente."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "La page a été quittée alors que le Gestionnaire de mots de passe Chrome était présent."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "La page a été quittée alors que la distillation DOM était en cours."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "La page a été quittée alors que DOM Distiller Viewer était présent."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Le cache amélioré est désactivé en raison d'extensions utilisant une API d'envoi de messages."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Les extensions à connexion longue durée doivent fermer la connexion avant d'accéder au cache amélioré."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Des extensions à connexion longue durée ont tenté d'envoyer des messages aux frames dans le cache amélioré."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Le cache amélioré est désactivé en raison des extensions."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "La page a été quittée alors qu'une boîte de dialogue modale pour renvoyer un formulaire ou indiquer un mot de passe http, par exemple, était affichée pour cette page."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "La page a été quittée alors que la page hors connexion était affichée."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "La page a été quittée alors que la barre d'intervention pour mémoire insuffisante était présente."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "La page a été quittée alors que des demandes d'autorisation étaient en cours."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "La page a été quittée alors que le bloqueur de pop-up était présent."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "La page a été quittée alors que les détails de la navigation sécurisée étaient affichés."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "La navigation sécurisée a considéré cette page comme abusive et a bloqué le pop-up."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Un service worker a été activé alors que la page se trouvait dans le cache amélioré."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Le cache amélioré est désactivé à cause d'une erreur liée au document."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Les pages qui utilisent FencedFrames ne peuvent pas être stockées dans le cache amélioré."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "La page a été exclue du cache afin de laisser la place à une autre."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Les pages sur lesquelles l'accès au flux multimédia est autorisé ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Les pages qui utilisent des portails ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Les pages qui utilisent IdleManager ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Les pages avec une connexion IndexedDB active ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Des API non éligibles ont été utilisées."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Les pages dans lesquelles des extensions injectent du code JavaScript ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Les pages dans lesquelles des extensions injectent une feuille de style ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON> interne."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Le cache amélioré est désactivé en raison d'une requête de message keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Les pages qui utilisent le verrouillage du clavier ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | loading": {"message": "La page a été quittée avant d'être entièrement chargée."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Les pages dont la ressource principale contient \"cache-control:no-cache\" ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Les pages dont la ressource principale contient cache-control:no-store ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "La navigation a été annulée avant que la page puisse être restaurée à partir du cache amélioré."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "La page a été exclue du cache, car une connexion réseau active a reçu trop de données. Chrome limite la quantité de données qu'une page mise en cache peut recevoir."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Les pages ayant une requête fetch() ou XHR en cours ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "La page a été exclue du cache amélioré, car une requête réseau active impliquait une redirection."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "La page a été exclue du cache, car une connexion réseau est restée trop longtemps ouverte. Chrome limite la durée pendant laquelle une page mise en cache peut recevoir des données."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Les pages sans en-tête de réponse valide ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "La navigation a eu lieu dans un frame autre que le principal."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "La page avec des transactions DB indexées en cours n'est actuellement pas éligible au cache amélioré."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Les pages ayant une requête réseau en cours ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Les pages avec une requête réseau fetch() en cours ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Les pages ayant une requête réseau en cours ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Les pages ayant une requête réseau XHR en cours ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Les pages qui utilisent PaymentManager ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Les pages qui utilisent le Picture-in-picture ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | portal": {"message": "Les pages qui utilisent des portails ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | printing": {"message": "Les pages qui affichent l'UI d'impression ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Soit la page a été ouverte avec `window.open()` (et un autre onglet comprend une référence à celle-ci), soit elle a ouvert une fenêtre."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Le processus de rendu de la page située dans le cache amélioré a planté."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Le processus de rendu de la page située dans le cache amélioré a été interrompu."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Les pages qui ont demandé des autorisations pour des captures audio ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Les pages qui ont demandé des autorisations pour des capteurs ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Les pages qui ont demandé une synchronisation en arrière-plan ou des autorisations d'extraction ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Les pages qui ont demandé des autorisations MIDI ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Les pages qui ont demandé des autorisations pour des notifications ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Les pages qui ont demandé à accéder à l'espace de stockage ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Les pages qui ont demandé des autorisations pour des captures vidéo ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Seules les pages dont le schéma d'URL est HTTP/HTTPS peuvent être mises en cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "La page a été revendiquée par un service worker alors qu'elle se trouve dans le cache amélioré."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Un service worker a tenté d'envoyer une propriété `MessageEvent` à la page située dans le cache amélioré."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "ServiceWorker a été désinscrit alors qu'une page se trouvait dans le cache amélioré."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "La page a été exclue du cache amélioré, car un service worker a été activé."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome a redémarré et effacé les entrées du cache amélioré."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Les pages qui utilisent SharedWorker ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Les pages qui utilisent SpeechRecognizer ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Les pages qui utilisent SpeechSynthesis ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Une navigation a démarré sur un iFrame de la page, mais ne s'est pas terminée."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Les pages dont la sous-ressource contient cache-control:no-cache ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Les pages dont la sous-ressource contient cache-control:no-store ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | timeout": {"message": "La page a dépassé la durée maximale autorisée dans le cache amélioré et a expiré."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Le délai d'enregistrement de la page dans le cache amélioré a été dépassé (probablement en raison de gestionnaires pagehide de longue durée)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Le frame principal de l'image contient un gestionnaire unload."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Le sous-frame de l'image contient un gestionnaire unload."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Le navigateur a modifié l'en-tête de forçage user-agent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Les pages qui ont autorisé l'enregistrement audio ou vidéo ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Les pages qui utilisent WebDatabase ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Les pages qui utilisent WebHID ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Les pages qui utilisent WebLocks ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Les pages qui utilisent WebNfc ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Les pages qui utilisent WebOTPService ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Les pages avec WebRTC ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Les pages qui utilisent WebShare ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Les pages avec WebSocket ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Les pages avec WebTransport ne peuvent pas être incluses dans le cache amélioré."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Les pages qui utilisent WebXR ne sont actuellement pas éligibles au cache amélioré."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Pensez à ajouter les schémas d'URL https: et http: (ignorés par les navigateurs prenant en charge 'strict-dynamic') pour assurer la rétrocompatibilité avec les anciens navigateurs."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener est obsolète depuis CSP3. Veuillez utiliser l'en-tête Cross-Origin-Opener-Policy à la place."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer est obsolète depuis CSP2. Veuillez utiliser l'en-tête Referrer-Policy à la place."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss est obsolète depuis CSP2. Veuillez utiliser l'en-tête X-XSS-Protection à la place."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "L'absence de directive base-uri permet aux balises <base> injectées de définir l'URL de base pour toutes les URL relatives (scripts, par exemple) en tant que domaine contrôlé par un pirate informatique. Envisagez de définir base-uri sur \"none\" ou \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "L'absence de object-src rend possible l'injection de plug-ins qui exécutent des scripts non sécurisés. Envisagez de définir object-src sur \"none\" si vous le pouvez."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Directive script-src manquante. Elle permet d'exécuter des scripts non sécurisés."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "<PERSON><PERSON>-vous oublié le point-virgule ? {keyword} semble être une directive, et non un mot clé."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Les nonces doivent utiliser le charset base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Les nonces doivent comporter au moins huit caractères."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Évitez d'utiliser des schémas d'URL simples ({keyword}) dans cette directive. Ces schémas permettent aux scripts de provenir d'un domaine non sécurisé."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Évitez d'utiliser des caractères génériques simples ({keyword}) dans cette directive. Ces caractères génériques permettent aux scripts de provenir d'un domaine non sécurisé."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "La destination de rapport est configurée uniquement via la directive report-to qui n'est compatible qu'avec les navigateurs basés sur Chromium. Il est donc recommandé d'utiliser aussi une directive report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Aucune CSP ne configure de destination de rapport. Il est donc difficile de respecter la CSP au fil du temps et de surveiller les éventuelles failles."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Les listes d'autorisation des hôtes peuvent souvent être contournées. Envisagez d'utiliser des nonces ou des hachages CSP à la place, ainsi que strict-dynamic, si nécessaire."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Directive CSP inconnue."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} semble être un mot clé non valide."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "'unsafe-inline' permet d'exécuter des gestionnaires d'événements et des scripts non sécurisés sur la page. Pensez à utiliser des nonces ou des hachages CSP pour autoriser les scripts un à un."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Pensez à ajouter la commande 'unsafe-inline' (ignorée par les navigateurs prenant en charge les nonces/hachages) pour assurer la rétrocompatibilité avec les anciens navigateurs."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "L'autorisation ne sera pas couverte par le caractère générique (*) dans la gestion `Access-Control-Allow-Headers` de CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Les requêtes de ressources dont les URL contiennent à la fois des espaces blancs `(n|r|t)` et des signes \"strictement inférieur à\" (`<`) supprimés sont bloquées. Veuillez supprimer les nouvelles lignes et encoder les symboles \"strictement inférieur à\" depuis des emplacements tels que des valeurs d'attribut d'élément pour pouvoir charger ces ressources."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` est obsolète. Utilisez l'API normalisée à la place : Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` est obsolète. Veuillez utiliser l'API normalisée à la place : Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` est obsolète. Veuillez utiliser l'API normalisée à la place : `nextHopProtocol` dans Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Les cookies contenant un caractère `(0|r|n)` ne seront pas tronqués, mais refusés."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "La possibilité de contourner la règle d'origine commune en définissant `document.domain` est obsolète et sera désactivée par défaut. Cet avertissement concerne l'accès multi-origine activé en définissant `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "La possibilité de déclencher {PH1} depuis des iFrames multi-origines est obsolète et sera supprimée."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Pour désactiver l'intégration Cast par défaut, utilisez l'attribut `disableRemotePlayback` plutôt que le sélecteur `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} est obsolète. Veuillez utiliser {PH2} à la place."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Voici un exemple de message traduit lié à un problème d'abandon."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "La possibilité de contourner la règle d'origine commune en définissant `document.domain` est obsolète et sera désactivée par défaut. Pour continuer à utiliser cette fonctionnalité, veuillez désactiver les clusters d'agents selon l'origine en envoyant un en-tête `Origin-Agent-Cluster: ?0` avec la réponse HTTP pour le document et les frames. Pour en savoir plus, consultez https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "L'API `Event.path` est obsolète et sera supprimée. Veuillez utiliser `Event.composedPath()` à la place."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "L'en-tête `Expect-CT` est obsolète et sera supprimé. Chrome exige la transparence des certificats pour tous les certificats reconnus publiquement émis après le 30 avril 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Pour en savoir plus, consultez la page d'état de la fonctionnalité."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` et `watchPosition()` ne fonctionnent plus sur les origines non sécurisées. Pour utiliser cette fonctionnalité, vous devriez envisager d'utiliser une origine sûre pour votre appli, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` et `watchPosition()` sont obsolètes pour les origines non sécurisées. Pour utiliser cette fonctionnalité, vous devriez envisager d'utiliser une origine sûre pour votre appli, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ne fonctionne plus sur les origines non sécurisées. Pour utiliser cette fonctionnalité, vous devriez envisager d'utiliser une origine sûre pour votre appli, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` est obsolète. Veuillez utiliser `RTCPeerConnectionIceErrorEvent.address` ou `RTCPeerConnectionIceErrorEvent.port` à la place."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Le point de départ du marchand et les données arbitraires provenant de l'événement service worker `canmakepayment` sont obsolètes et seront supprimés : `topOrigin`, `paymentRequestOrigin`, `methodData` et `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Le site Web a demandé une sous-ressource auprès d'un réseau auquel il pouvait uniquement accéder en raison de la position privilégiée de ses utilisateurs sur le réseau. Ces requêtes exposent les serveurs et les appareils non publics à Internet, ce qui augmente le risque d'attaque par falsification des requêtes intersites (CSRF) et/ou les fuites d'informations. Pour réduire ces risques, Chrome n'accepte plus les requêtes auprès de sous-ressources non publiques émises depuis des contextes non sécurisés et va commencer à les bloquer."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS ne peut pas être chargé depuis des URL `file:`, sauf si elles se terminent par une extension de fichier `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "La possibilité d'utiliser `SourceBuffer.abort()` pour annuler la suppression de la plage asynchrone par `remove()` est obsolète, en raison d'une modification de la spécification. Sa prise en charge sera supprimée prochainement. Écoutez plutôt l'événement `updateend`. `abort()` sert uniquement à annuler l'ajout d'un contenu multimédia asynchrone ou à réinitialiser l'état de l'analyseur."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "La possibilité de régler `MediaSource.duration` à une valeur inférieure au code temporel de présentation le plus élevé de n'importe quel frame codé en mémoire tampon est obsolète en raison d'une modification de la spécification. La possibilité de supprimer implicitement des contenus multimédias tronqués en mémoire tampon sera supprimée prochainement. À la place, vous devriez exécuter `remove(newDuration, oldDuration)` de manière explicite sur tous les `sourceBuffers`, où `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Cette modification prendra effet dans la version majeure {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI demandera une autorisation d'utilisation, même si le SysEx n'est pas spécifié dans les `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "L'API Notification ne peut plus être utilisée depuis des origines non sécurisées. Vous devriez envisager d'utiliser une origine sûre pour votre appli, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "L'autorisation pour l'API Notification ne peut plus être demandée depuis un iFrame multi-origine. À la place, vous devriez envisager de demander l'autorisation depuis un frame de haut niveau ou d'ouvrir une nouvelle fenêtre."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Votre partenaire négocie une version obsolète de (D)TLS. Contactez-le pour qu'il corrige ce problème."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL dans des contextes non sécurisés est obsolète et sera bientôt supprimé. Veuillez utiliser le stockage Web ou la base de données indexée."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Si vous spécifiez `overflow: visible` sur \"img\", \"video\" et \"canvas\", ces tags risquent de générer du contenu visuel en dehors des limites de l'élément. Voir https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "Abandon de `paymentManager.instruments`. Veuillez utiliser l'installation \"juste à temps\" pour les gestionnaires de paiement."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Votre appel `PaymentRequest` a contourné la directive CSP (Content-Security-Policy) `connect-src`. Ce contournement est obsolète. Veuillez ajouter l'identifiant du mode de paiement de l'API `PaymentRequest` (dans le champ `supportedMethods`) à la directive CSP `connect-src`."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` est obsolète. Veuillez plutôt utiliser la version normalisée `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "L'élément `<source src>` avec un parent `<picture>` n'est pas valide et est donc ignoré. Veuillez utiliser `<source srcset>` à la place."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` est obsolète. Veuillez plutôt utiliser la version normalisée `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Les requêtes de sous-ressources dont les URL contiennent des identifiants intégrés (`**********************/`, par exemple) sont bloquées."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "La contrainte `DtlsSrtpKeyAgreement` a été supprimée. Vous avez spécifié une valeur `false` pour cette contrainte, ce qui est interprété comme une tentative d'utiliser la méthode `SDES key negotiation`, qui a été supprimée. Cette fonctionnalité a été supprimée. À la place, utilisez un service compatible avec `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "La contrainte `DtlsSrtpKeyAgreement` a été supprimée. Vous avez spécifié une valeur `true` pour cette contrainte, qui est sans effet. Vous pouvez toutefois la supprimer pour plus de clarté."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` détecté. Ce dialecte du `Session Description Protocol` n'est plus compatible. Veuillez utiliser `Unified Plan SDP` à la place."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, qui sert à établir une `RTCPeerConnection` avec `{sdpSemantics:plan-b}`, est une version ancienne non standard de `Session Description Protocol` qui a été définitivement supprimée de la plate-forme Web. Cette version reste disponible pour établir une connexion avec `IS_FUCHSIA`, mais nous prévoyons de la supprimer dès que possible. Cessez de vous reposer dessus. Pour connaître l'état de ce changement, consultez https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "L'option `rtcpMuxPolicy` est obsolète et sera supprimée."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` nécessite une isolation multi-origine. Pour en savoir plus, consultez https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "La fonctionnalité `speechSynthesis.speak()` sans activation de l'utilisateur est obsolète et sera supprimée."}, "core/lib/deprecations-strings.js | title": {"message": "Fonctionnalité obsolète utilisée"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Les extensions doivent activer l'isolation multi-origine pour pouvoir continuer à utiliser `SharedArrayBuffer`. Consultez https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} est propre au fournisseur. Veuillez utiliser la méthode {PH2} standard à la place."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "Les réponses json n'acceptent pas l'encodage UTF-16 dans `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "La requête `XMLHttpRequest` synchrone sur le thread principal est obsolète en raison de son impact négatif sur l'expérience de l'utilisateur final. Si vous avez besoin d'aide, consultez https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` est obsolète. Veuillez utiliser `isSessionSupported()` et vérifier la valeur booléenne résolue à la place."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "<PERSON><PERSON><PERSON> de blocage du thread principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Cache de la valeur TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Description"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "É<PERSON>ment"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Éléments non conformes"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Emplacement"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nom"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Au-dessus du budget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON> de <PERSON> resso<PERSON>ce"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Type de ressource"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Source"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON> d<PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Te<PERSON> passé"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Économies potentielles"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Économies potentielles"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Économies potentielles de {wastedBytes, number, bytes} Kio"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 élément trouvé}one{# élément trouvé}other{# éléments trouvés}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Économies potentielles de {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Police de caractères"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Image"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Élevée"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Faible"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Max Potential First Input Delay"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Contenu multimédia"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Autres ressources"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Feuille de style"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Tiers"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Un problème est survenu lors de l'enregistrement de la trace du chargement de votre page. Veuillez relancer Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Expiration du délai pendant la tentative de connexion initiale du protocole du débogueur."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome n'a collecté aucune capture d'écran pendant le chargement de la page. Veuillez vous assurer que du contenu est visible sur la page, puis essayez de relancer Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Les serveurs DNS n'ont pas pu résoudre le domaine fourni."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Une erreur s'est produite lors de la collecte de la ressource {artifactName} requise : {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Une erreur Chrome interne s'est produite. Veuillez redémarrer Chrome et essayer de relancer Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "La ressource {artifactName} nécessaire n'a pas été collectée."}, "core/lib/lh-error.js | noFcp": {"message": "Aucun contenu n'a été affiché sur la page. Assurez-vous de conserver la fenêtre du navigateur au premier plan pendant le chargement et réessayez. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "La page ne présentait pas de contenu considéré comme Largest Contentful Paint (LCP). Assurez-vous que la page comporte un élément LCP valide, puis réessayez. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "La page fournie n'est pas au format HTML (diffusée avec le type MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Cette version de Chrome est trop ancienne pour être compatible avec \"{featureName}\". Utilisez une version plus récente pour afficher les résultats complets."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse n'a pas pu charger correctement la page que vous avez demandée. Assurez-vous de tester la bonne URL et vérifiez que le serveur répond correctement à toutes les requêtes."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse n'a pas pu charger correctement l'URL que vous avez demandée, car la page a cessé de répondre."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "L'URL que vous avez fournie n'est pas associée à un certificat de sécurité valide. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Le chargement de la page dans Chrome a été bloqué et remplacé par un écran interstitiel. Assurez-vous de tester la bonne URL et vérifiez que le serveur répond correctement à toutes les requêtes."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse n'a pas pu charger correctement la page que vous avez demandée. Assurez-vous de tester la bonne URL et vérifiez que le serveur répond correctement à toutes les requêtes. (Détails : {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse n'a pas pu charger correctement la page que vous avez demandée. Assurez-vous de tester la bonne URL et vérifiez que le serveur répond correctement à toutes les requêtes. (Code d'état : {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Le chargement de la page a pris trop de temps. Veuillez suivre les indications du rapport pour réduire le temps de chargement de la page, puis essayez de relancer Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Le délai d'attente de la réponse du protocole DevTools est arrivé à expiration. (Méthode : {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Le délai alloué à la récupération des ressources a été atteint"}, "core/lib/lh-error.js | urlInvalid": {"message": "L'URL que vous avez fournie ne semble pas valide."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Le type MIME de la page est XHTML : Lighthouse n'accepte pas explicitement ce type de document"}, "core/user-flow.js | defaultFlowName": {"message": "Parcours utilisateur ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Rapport sur la navigation ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Rapport sur un instantané ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Rapport sur la période ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Tous les rapports"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Catégories"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accessibilité"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Bonnes pratiques"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Performances"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive web app"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Bureau"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Comprendre le rapport Lighthouse sur les flux"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Comprendre les flux"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Utiliser les rapports sur la navigation pour…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Utiliser les rapports sur un instantané pour…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Utiliser les rapports sur la période pour…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obtenir un score de performances Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Mesurer les métriques liées aux performances de chargement des pages comme Largest Contentful Paint et Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Évaluer les fonctionnalités des progressive web apps."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Identifier des problèmes d'accessibilité dans les applis monopages ou les formulaires complexes."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Évaluer les bonnes pratiques concernant les menus et les éléments d'UI cachés derrière l'interaction."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Mesurer les décalages de mise en page et le délai d'exécution de JavaScript dans une série d'interactions."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Découvrir des opportunités de performances pour améliorer l'expérience utilisateur concernant les pages de longue durée et les applis Web monopages."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Impact maximal"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} audit informatif}one{{numInformative} audit informatif}other{{numInformative} audits informatifs}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobile"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Chargement de page"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "À l'instar des rapports Lighthouse d'origine, les rapports sur la navigation analysent le chargement d'une seule page."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Rapport sur la navigation"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} rapport sur la navigation}one{{numNavigation} rapport sur la navigation}other{{numNavigation} rapports sur la navigation}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} audit réalisable}one{{numPassableAudits} audit réalisable}other{{numPassableAudits} audits réalisables}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} audit passé}one{{numPassed} audit passé}other{{numPassed} audits passés}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Enregistrer"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "État capturé de la page"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Les rapports sur un instantané analysent la page à un moment donné, généralement après des interactions d'utilisateurs."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Rapport sur un instantané"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} rapport instantané}one{{numSnapshot} rapport instantané}other{{numSnapshot} rapports instantanés}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Résumé"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interactions des utilisateurs"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Les rapports sur la période analysent une période arbitraire, contenant généralement des interactions d'utilisateurs."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Rapport sur la période"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} rapport périodique}one{{numTimespan} rapport périodique}other{{numTimespan} rapports périodiques}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Rapport sur le flux d'utilisateurs Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Pour le contenu animé, utilisez [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) afin que le processeur soit sollicité le moins possible lorsque le contenu se situe hors écran."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Envisagez d'afficher tous vos composants [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) dans des formats WebP tout en spécifiant une solution de remplacement appropriée pour les autres navigateurs. [En savoir plus](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Vérifiez si vous utilisez bien [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) pour les images qui doivent être chargées automatiquement de manière différée. [En savoir plus](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Utilisez des outils comme [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) pour [effectuer un rendu côté serveur de la mise en page de pages AMP](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consultez la [documentation AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) pour vérifier si tous les styles sont bien compatibles."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Le composant [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) accepte l'attribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) afin de spécifier les éléments image à utiliser en fonction de la taille de l'écran. [En savoir plus](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Envisagez d'utiliser le défilement virtuel avec le kit de développement de composants si des listes très volumineuses sont affichées. [En savoir plus](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "[Divisez le code au niveau du routage](https://web.dev/route-level-code-splitting-in-angular/) afin de réduire au maximum la taille de vos groupes JavaScript. Envisagez également de mettre préalablement en cache les éléments avec le [service worker Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Si vous utilisez Angular CLI, assurez-vous que les builds sont générées en mode de production. [En savoir plus](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Si vous utilisez l'interface de ligne de commande Angular, incluez des mappages sources dans votre build de production pour inspecter vos groupes. [En savoir plus](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Préchargez les routages à l'avance pour une navigation plus rapide. [En savoir plus](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Envisagez d'utiliser l'outil `BreakpointObserver` du kit de développement de composants pour gérer les points d'arrêt de l'image. [En savoir plus](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Envisagez d'importer votre GIF dans un service qui permettra de l'intégrer en tant que vidéo HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Lorsque vous définissez des polices personnalisées dans votre thème, vous pouvez le spécifier avec `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Envisagez de configurer les [formats d'image WebP avec un style de conversion d'image](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) sur votre site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Installez un [module Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) qui peut effectuer le chargement différé des images. Ce type de module offre la possibilité de différer le chargement des images hors écran pour améliorer les performances."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Vous pouvez envisager d'utiliser un module permettant d'aligner les codes CSS et JavaScript critiques, ou éventuellement de charger les éléments de manière asynchrone via JavaScript (le module [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg), par exemple). Gardez en tête qu'à cause des optimisations fournies par ce module, votre site peut cesser de fonctionner. Vous devrez donc probablement modifier le code."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Les thèmes, les modules et les spécifications du serveur sont autant d'éléments qui influent sur le temps de réponse du serveur. Vous pouvez envisager d'utiliser un module plus optimisé ou un plug-in d'optimisation plus performant, ou bien de mettre à niveau votre serveur. Vos serveurs d'hébergement doivent exploiter la mise en cache du code d'opération PHP, la mise en cache de la mémoire pour réduire les temps d'interrogation des bases de données (avec Redis ou Memcached, par exemple), ainsi qu'une logique applicative optimisée pour accélérer la préparation des pages."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Vous pouvez également envisager d'utiliser [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) pour réduire la taille des images chargées sur votre page. Si vous utilisez la fonctionnalité Vues pour afficher plusieurs éléments de contenu sur une même page, pensez à définir la pagination pour limiter le nombre d'éléments de contenu affichés sur une page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Assurez-vous que vous avez activé l'option \"Regrouper les fichiers CSS\" sur la page Administration > Configuration > Développement. Vous pouvez également effectuer une configuration plus avancée des regroupements au moyen de [modules supplémentaires](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search). Ceux-ci vous permettront d'améliorer la rapidité de votre site en concaténant, en minimisant et en compressant vos styles CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Assurez-vous que vous avez activé l'option \"Regrouper les fichiers JavaScript\" sur la page Administration > Configuration > Développement. Vous pouvez également effectuer une configuration plus avancée des regroupements au moyen de [modules supplémentaires](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search). Ceux-ci vous permettront d'améliorer la rapidité de votre site en concaténant, en minimisant et en compressant vos éléments JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Vous pouvez envisager de supprimer les règles CSS inutilisées et de n'attacher que les bibliothèques Drupal nécessaires à la page (ou au composant de page) concernés. Pour plus d'informations, consultez la [documentation Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Pour déterminer quelles bibliothèques attachées ajoutent des CSS superflus, essayez d'exécuter la [couverture de code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) dans Chrome DevTools. Lorsque le regroupement de CSS est désactivé sur votre site Drupal, vous pouvez identifier le thème ou le module qui en est responsable à partir de l'URL de la feuille de style. Recherchez les thèmes ou les modules pour lesquels un grand nombre de feuilles de style présentent beaucoup d'éléments en rouge dans la couverture de code. Un thème ou un module ne doit mettre une feuille de style en file d'attente que si elle est effectivement utilisée sur la page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Vous pouvez envisager de supprimer les éléments JavaScript inutilisés et d'attacher uniquement les bibliothèques Drupal nécessaires à la page ou au composant de page concernés. Pour plus d'informations, consultez la [documentation Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Pour déterminer quelles bibliothèques attachées ajoutent des scripts JavaScript superflus, exécutez une [couverture de code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) dans Chrome DevTools. Lorsque l'agrégation JavaScript est désactivée sur votre site Drupal, vous pouvez identifier le thème ou le module responsable à partir de l'URL du script. Recherchez les thèmes ou les modules pour lesquels un grand nombre de scripts présentent beaucoup d'éléments en rouge dans la couverture de code. Un thème ou un module ne doit mettre un script en file d'attente que s'il est effectivement utilisé dans la page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Définissez le paramètre \"Âge maximum du navigateur et du proxy cache\" sur la page \"Administration > Configuration > Développement. Apprenez-en davantage sur le [cache Drupal et l'optimisation des performances](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Vous pouvez envisager d'utiliser [un module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) qui optimise et réduit automatiquement la taille des images importées sur le site sans perdre en qualité. Assurez-vous également que vous utilisez le module natif [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) fourni par Drupal (disponible avec Drupal 8 et versions ultérieures)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Il est possible d'ajouter des indices de ressources de préconnexion ou dns-prefetch en installant et en configurant [un module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) qui fournit une fonction pour les indices de ressources user-agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Assurez-vous que vous utilisez le module natif [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) fourni par Drupal (disponible sous Drupal 8 et versions ultérieures). Utilisez Responsive Image Styles lors du rendu des champs d'image par les modes Vue, les vues ou les images importés via l'éditeur WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Utilisez [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) et activez `Optimize Fonts` pour utiliser automatiquement la fonction CSS `font-display` afin que le texte soit visible par les utilisateurs pendant le chargement des polices Web."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Next-Gen Formats` pour convertir les images au format WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Lazy Load Images` pour différer le chargement des images en dehors de l'écran tant qu'elles ne sont pas nécessaires."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Critical CSS` et `Script Delay` pour différer les contenus JS/CSS non essentiels."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Utilisez [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) pour mettre en cache votre contenu sur notre réseau mondial, améliorant ainsi le temps de latence du premier octet."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Minify CSS` pour minimiser automatiquement la taille des ressources CSS afin de réduire la taille des charges utiles de réseau."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Minify Javascript` pour minimiser automatiquement la taille de vos ressources JS afin de réduire la taille des charges utiles de réseau."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Remove Unused CSS` pour vous aider à résoudre ce problème. Il permettra d'identifier les classes CSS réellement utilisées sur chaque page de votre site et de supprimer les autres afin de réduire la taille du fichier."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Efficient Static Cache Policy` pour définir les valeurs recommandées dans l'en-tête de mise en cache des éléments statiques."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Next-Gen Formats` pour convertir les images au format WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Pre-Connect Origins` pour ajouter automatiquement des indices de ressources `preconnect` afin d'établir les premières connexions avec des origines tierces importantes."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Preload Fonts` et `Preload Background Images` pour ajouter des liens `preload` afin de hiérarchiser la récupération des ressources actuellement requises pour le chargement ultérieur de la page."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Utilisez [Ezoic Leap](https://pubdash.ezoic.com/speed) et activez `Resize Images` pour redimensionner des images à une taille adaptée à l'appareil, réduisant ainsi la taille des charges utiles de réseau."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Envisagez d'importer votre GIF dans un service qui permettra de l'intégrer en tant que vidéo HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Vous pouvez envisager d'utiliser un [plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ou un service qui convertit automatiquement les images que vous importez dans un format optimal."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Installez un [plug-in Joomla de chargement différé](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) pour différer le chargement des images hors écran, ou remplacez le modèle par un autre qui offre cette fonctionnalité. À partir de Joomla 4.0, toutes les nouvelles images obtiendront [automatiquement](https://github.com/joomla/joomla-cms/pull/30748) l'attribut `loading` du noyau."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Divers plug-ins Joomla peuvent vous aider à [aligner des éléments critiques](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ou à [différer le chargement des ressources moins importantes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Gardez en tête qu'à cause des optimisations fournies par ces plug-ins, certaines fonctionnalités de votre thème ou de vos plug-ins peuvent rencontrer des problèmes. Vous devez donc les tester minutieusement."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Les thèmes, les extensions et les spécifications du serveur sont autant d'éléments qui influent sur le temps de réponse du serveur. Vous pouvez envisager d'utiliser un thème plus optimisé ou une extension d'optimisation plus performante, ou bien de mettre à niveau votre serveur."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Vous pouvez envisager d'afficher des extraits dans vos catégories d'articles (par exemple en utilisant les liens \"en savoir plus\"), de réduire le nombre d'articles affichés dans une page donnée, de répartir vos articles longs sur plusieurs pages ou d'utiliser un plug-in qui charge de façon différée les commentaires."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Un certain nombre d'[extensions Jo<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) peuvent accélérer l'affichage de votre site en concaténant, en minimisant et en compressant vos styles CSS. Certains thèmes offrent également cette fonctionnalité."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Un certain nombre d'[extensions Jo<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) peuvent accélérer l'affichage de votre site en concaténant, en minimisant et en compressant vos scripts. Certains thèmes offrent également cette fonctionnalité."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Envisagez de réduire le nombre d'[extensions Joomla](https://extensions.joomla.org/) qui chargent du code CSS inutilisé dans votre page. Pour déterminer les extensions qui ajoutent des feuilles de style CSS superflues, exécutez une [couverture de code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) dans Chrome DevTools. Vous pouvez identifier le thème ou le plug-in responsable à partir de l'URL de la feuille de style. Recherchez les plug-ins pour lesquels un grand nombre de feuilles de style présentent beaucoup d'éléments en rouge dans la couverture de code. Un plug-in ne doit mettre une feuille de style en file d'attente que si elle est effectivement utilisée dans la page."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Envisagez de réduire le nombre d'[extensions Joomla](https://extensions.joomla.org/) qui chargent du code JavaScript inutilisé dans votre page. Pour déterminer les plug-ins qui ajoutent des scripts JavaScript superflus, exécutez une [couverture de code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) dans Chrome DevTools. Vous pouvez déterminer quelle extension en est responsable à partir de l'URL de votre script. Recherchez les extensions pour lesquelles un grand nombre de scripts présentent beaucoup d'éléments en rouge dans la couverture de code. Une extension ne doit mettre un script en file d'attente que s'il est effectivement utilisé dans la page."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "En savoir plus sur la [mise en cache dans le navigateur dans Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "<PERSON><PERSON> pouvez envisager d'utiliser un [plug-in d'optimisation d'image](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) pour compresser vos images sans dégrader leur qualité."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "<PERSON><PERSON> pouvez envisager d'utiliser un [plug-in d'images responsives](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) pour ajouter des images responsives à votre contenu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Vous pouvez permettre la compression de texte en activant la compression des pages avec Gzip dans Joomla (Système > Configuration globale > Serveur)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Si vous ne groupez pas vos éléments JavaScript, envisagez d'utiliser [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Désactivez les fonctionnalités de [regroupement et minimisation JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) de Magento, et envisagez plutôt d'utiliser [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Indiquez `@font-display` quand [vous définissez des polices personnalisées](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Pensez à rechercher des extensions tierces sur la [Place de marché de Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) afin de profiter des nouveaux formats d'image."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Envisagez de modifier vos modèles de produit et de catalogue pour utiliser la fonctionnalité de [chargement différé](https://web.dev/native-lazy-loading) de la plate-forme Web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Utilisez l'[intégration de Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Activez l'option \"Minifier les fichiers CSS\" dans les paramètres développeur. [En savoir plus](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Utilise<PERSON> [Terser](https://www.npmjs.com/package/terser) pour minimiser tous les éléments JavaScript en commençant par le déploiement de contenu statique, et désactivez la fonctionnalité de minimisation intégrée."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Désactivez la fonctionnalité de [regroupement JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) intégrée de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Pensez à rechercher des extensions tierces sur la [Place de marché de Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) afin d'optimiser les images."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "V<PERSON> pouvez ajouter des indices de ressources de préconnexion ou dns-prefetch en [modifiant la mise en page d'un thème](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "V<PERSON> pouvez ajouter des balises `<link rel=preload>` [en modifiant la mise en page d'un thème](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Utilisez le composant `next/image` au lieu de `<img>` pour optimiser automatiquement le format d'image. [En savoir plus](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Utilisez le composant `next/image` au lieu de `<img>` pour charger les images automatiquement en différé. [En savoir plus](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Utilisez le composant `next/image` et définissez \"priority\" sur \"true\" pour précharger l'image LCP. [En savoir plus](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Utilisez le composant `next/script` pour différer le chargement des scripts tiers non critiques. [En savoir plus.](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Utilisez le composant `next/image` pour vous assurer que la taille des images est toujours appropriée. [En savoir plus](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Envisagez de définir `PurgeCSS` dans la configuration `Next.js` pour supprimer les règles inutilisées des feuilles de style. [En savoir plus.](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Utilisez `Webpack Bundle Analyzer` pour détecter le code JavaScript inutilisé. [En savoir plus](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Envisagez d'utiliser `Next.js Analytics` pour mesurer les performances réelles de votre appli. [En savoir plus.](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configurez la mise en cache pour les assets et les pages `Server-side Rendered` (SSR) immuables. [En savoir plus.](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Utilisez le composant `next/image` au lieu de `<img>` pour ajuster la qualité d'image. [En savoir plus](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Utilisez le composant `next/image` pour définir les `sizes` appropriées. [En savoir plus.](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Activez la compression sur votre serveur Next.js. [En savoir plus.](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Utilisez le composant `nuxt/image` et réglez `format=\"webp\"`. [En savoir plus](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Utilisez le composant `nuxt/image` et réglez `loading=\"lazy\"` pour les images hors écran. [En savoir plus](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Utilisez le composant `nuxt/image` et spécifiez `preload` pour l'image LCP. [En savoir plus](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Utilisez le composant `nuxt/image` et spécifiez explicitement `width` et `height`. [En savoir plus](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Utilisez le composant `nuxt/image` et définissez la `quality` appropriée. [En savoir plus](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Utilisez le composant `nuxt/image` et définissez la `sizes` appropriée. [En savoir plus](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Remplacez les GIF animés par des vidéos](https://web.dev/replace-gifs-with-videos/) pour un chargement plus rapide de la page et envisagez d'utiliser des formats de fichiers modernes comme [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ou [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) pour améliorer le rendement de compression et atteindre un niveau 30 % supérieur au niveau du codec vidéo phare du moment, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Vous pouvez envisager d'utiliser un [plug-in](https://octobercms.com/plugins?search=image) ou un service qui convertit automatiquement les images importées dans un format optimal. Les [images sans perte WebP](https://developers.google.com/speed/webp) sont 26 % plus petites que les images au format PNG et 25 à 34 % plus petites que les images au format JPEG comparables avec un indice de qualité SSIM équivalent. Vous pouvez également envisager d'utiliser le format d'image nouvelle génération [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Envisagez d'installer un [plug-in de chargement différé des images](https://octobercms.com/plugins?search=lazy) offrant la possibilité de différer le chargement des images hors écran, ou passez à un thème qui offre cette fonctionnalité. Vous pouvez également envisager d'utiliser [le plug-in AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Il existe de nombreux plug-ins qui aident à [aligner des éléments critiques](https://octobercms.com/plugins?search=css). Ces plug-ins peuvent affecter le fonctionnement d'autres plug-ins. Vous devez donc les tester minutieusement."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Les thèmes, les plug-ins et les spécifications du serveur sont autant d'éléments qui influent sur le temps de réponse du serveur. Vous pouvez envisager d'utiliser un thème plus optimisé ou un plug-in d'optimisation plus performant, ou bien de mettre le serveur à niveau. Le CMS October permet également aux développeurs d'utiliser [`Queues`](https://octobercms.com/docs/services/queues) pour reporter le traitement d'une tâche chronophage, par exemple l'envoi d'un e-mail. Cela accélère considérablement les requêtes Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Vous pouvez envisager d'afficher des extraits dans vos listes d'articles (par exemple en utilisant un bouton `show more`), de réduire le nombre d'articles affichés sur une page Web donnée, de répartir vos articles longs sur plusieurs pages ou d'utiliser un plug-in qui charge les commentaires de façon différée."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "De nombreux [plug-ins](https://octobercms.com/plugins?search=css) permettent d'accélérer un site Web en concaténant, minimisant et compressant les styles. L'utilisation d'un processus de compilation pour réaliser cette minimisation en amont peut accélérer le développement."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "De nombreux [plug-ins](https://octobercms.com/plugins?search=javascript) permettent d'accélérer un site Web en concaténant, minimisant et compressant les scripts. L'utilisation d'un processus de compilation pour réaliser cette minimisation en amont peut accélérer le développement."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Vous pouvez vérifier si des [plug-ins](https://octobercms.com/plugins) chargent des feuilles de style CSS inutilisées sur le site Web. Pour identifier les plug-ins qui ajoutent des feuilles de style CSS superflues, lancez la [couverture de code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) dans les outils de développement Chrome. Identifiez le thème/plug-in responsable à partir de l'URL de la feuille de style. Recherchez les plug-ins pour lesquels un grand nombre de feuilles de style présentent beaucoup d'éléments en rouge dans la couverture de code. Un plug-in ne doit ajouter une feuille de style que si elle est effectivement utilisée sur la page."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Vous pouvez vérifier si des [plug-ins](https://octobercms.com/plugins?search=javascript) chargent du code JavaScript inutilisé dans la page Web. Pour identifier les plug-ins qui ajoutent du code JavaScript superflu, lancez la [couverture de code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) dans les outils de développement Chrome. Identifiez le thème/plug-in responsable à partir de l'URL du script. Recherchez les plug-ins pour lesquels un grand nombre de scripts présentent beaucoup d'éléments en rouge dans la couverture de code. Un plug-in ne doit ajouter un script que s'il est effectivement utilisé sur la page."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "En savoir plus sur la [prévention de requêtes réseau inutiles avec le cache HTTP](https://web.dev/http-cache/#caching-checklist). De nombreux [plug-ins](https://octobercms.com/plugins?search=Caching) peuvent être utilisés pour accélérer la mise en cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "<PERSON><PERSON> pouvez envisager d'utiliser un [plug-in d'optimisation d'image](https://octobercms.com/plugins?search=image) pour compresser les images sans dégrader leur qualité."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Importez des images directement via le gestionnaire de médias pour vous assurer que les tailles d'images requises sont disponibles. Envisagez d'utiliser le [filtre de redimensionnement](https://octobercms.com/docs/markup/filter-resize) ou un [plug-in de redimensionnement d'images](https://octobercms.com/plugins?search=image) pour vous assurer que les tailles d'image optimales sont utilisées."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Activez la compression de texte dans la configuration de votre serveur Web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Envisagez d'utiliser une bibliothèque de fenêtrage comme `react-window` pour réduire au maximum le nombre de nœuds DOM créés si vous affichez de nombreux éléments répétés sur la page. [En savoir plus](https://web.dev/virtualize-long-lists-react-window/) Limitez également autant que possible les nouveaux rendus inutiles avec [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ou [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo), et [ignorez les effets](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) jusqu'à ce que certaines dépendances aient changé si vous utilisez le hook `Effect` pour améliorer les performances d'exécution."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Si vous utilisez React Router, réduisez au maximum l'utilisation du composant `<Redirect>` pour la [navigation vers des itinéraires](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Si vous êtes en train d'effectuer un rendu de composants React côté serveur, envisagez d'utiliser `renderToPipeableStream()` ou `renderToStaticNodeStream()` pour permettre au client de recevoir et d'hydrater différentes parties du balisage au lieu de tout faire simultanément. [En savoir plus](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Si votre système de compilation réduit automatiquement la taille de vos fichiers CSS, assurez-vous de déployer le build de production de votre application. Vous pouvez le vérifier avec l'extension React Developer Tools. [En savoir plus](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Si votre système de compilation réduit automatiquement la taille des fichiers JS, assurez-vous de déployer le build de production de votre application. Vous pouvez le vérifier avec l'extension React Developer Tools. [En savoir plus](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Si vous n'effectuez pas de rendu côté serveur, [divisez vos groupes JavaScript](https://web.dev/code-splitting-suspense/) avec `React.lazy()`. Sinon, divisez le code en utilisant une bibliothèque tierce, telle que [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Utilisez React DevTools Profiler, qui utilise l'API Profiler, pour mesurer les performances de rendu de vos composants. [En savoir plus](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Envisagez d'importer votre GIF dans un service qui permettra de l'intégrer en tant que vidéo HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Envisagez d'utiliser le plug-in [Performance Lab](https://wordpress.org/plugins/performance-lab/) pour convertir automatiquement vos images JPEG importées au format WebP, lorsque cela est possible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Installez un [plug-in WordPress de chargement différé](https://wordpress.org/plugins/search/lazy+load/) pour différer le chargement des images qui ne sont pas à l'écran, ou remplacez le thème par un autre qui offre cette fonctionnalité. Vous pouvez également envisager d'utiliser [le plug-in AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Divers plug-ins WordPress peuvent vous aider à [aligner des éléments critiques](https://wordpress.org/plugins/search/critical+css/) ou à [différer le chargement des ressources moins importantes](https://wordpress.org/plugins/search/defer+css+javascript/). Gardez en tête qu'à cause des optimisations fournies par ces plug-ins, certaines fonctionnalités de votre thème ou de vos plug-ins peuvent cesser de fonctionner. Vous devrez donc probablement modifier le code."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Les thèmes, les plug-ins et les spécifications du serveur sont autant d'éléments qui influent sur le temps de réponse du serveur. Vous pouvez envisager d'utiliser un thème plus optimisé ou un plug-in d'optimisation plus performant, ou bien de mettre à niveau votre serveur."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Vous pouvez envisager d'afficher des extraits dans vos listes d'articles (par exemple en utilisant la balise \"more\"), de réduire le nombre d'articles affichés dans une page donnée, de répartir vos articles longs sur plusieurs pages ou d'utiliser un plug-in qui charge de façon différée les commentaires."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Un certain nombre de [plug-ins WordPress](https://wordpress.org/plugins/search/minify+css/) peuvent accélérer l'affichage de votre site en concaténant, en minimisant et en compressant vos styles. Si possible, utilisez un processus de build pour réaliser cette minimisation en amont."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Un certain nombre de [plug-ins WordPress](https://wordpress.org/plugins/search/minify+javascript/) peuvent accélérer l'affichage de votre site en concaténant, en minimisant et en compressant vos scripts. Si possible, utilisez un processus de build pour réaliser cette minimisation en amont."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Vous pouvez envisager de réduire le nombre de [plug-ins WordPress](https://wordpress.org/plugins/) qui chargent des feuilles de style CSS inutilisées dans votre page, ou désactiver certains de ces plug-ins. Pour déterminer les plug-ins qui ajoutent des feuilles de style CSS superflues, exécutez une [couverture de code](https://developer.chrome.com/docs/devtools/coverage/) dans Chrome DevTools. Vous pouvez identifier le thème ou le plug-in responsable à partir de l'URL de la feuille de style. Recherchez les plug-ins pour lesquels un grand nombre de feuilles de style présentent beaucoup d'éléments en rouge dans la couverture de code. Un plug-in ne doit mettre une feuille de style en file d'attente que si elle est effectivement utilisée dans la page."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Vous pouvez envisager de réduire le nombre de [plug-ins WordPress](https://wordpress.org/plugins/) qui chargent des scripts JavaScript inutilisés dans votre page, ou désactiver certains de ces plug-ins. Pour déterminer les plug-ins qui ajoutent des scripts JavaScript superflus, exécutez une [couverture de code](https://developer.chrome.com/docs/devtools/coverage/) dans Chrome DevTools. Vous pouvez identifier le thème ou le plug-in responsable à partir de l'URL du script. Recherchez les plug-ins pour lesquels un grand nombre de scripts présentent beaucoup d'éléments en rouge dans la couverture de code. Un plug-in ne doit mettre un script en file d'attente que s'il est effectivement utilisé dans la page."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "En savoir plus sur la [mise en cache dans le navigateur dans WordPress](https://wordpress.org/support/article/optimization/#browser-caching)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "<PERSON><PERSON> pouvez envisager d'utiliser un [plug-in WordPress d'optimisation d'image](https://wordpress.org/plugins/search/optimize+images/) pour compresser vos images sans dégrader leur qualité."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Importez des images directement via la [bibliothèque multimédia](https://wordpress.org/support/article/media-library-screen/) pour vous assurer que les tailles d'images requises sont disponibles. Ensuite, insérez-les depuis la bibliothèque multimédia ou utilisez le widget d'image pour vous assurer que les tailles d'images optimales sont utilisées (y compris celles pour les points d'arrêt réactifs). Évitez d'utiliser les images `Full Size`, sauf si les dimensions sont adéquates pour l'utilisation prévue. [En savoir plus](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Vous pouvez activer la compression du texte dans la configuration de votre serveur Web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Activez l'option \"Imagifier\" dans l'onglet \"Optimisation d'image\" de WP Rocket pour convertir vos images au format WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Activez l'option [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) dans WP Rocket pour corriger cette recommandation. Cette fonctionnalité retarde le chargement des images jusqu'à ce que l'utilisateur fasse défiler la page vers le bas et qu'il ait réellement besoin de les voir."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Activez les options [Supprimer les ressources CSS inutilisées](https://docs.wp-rocket.me/article/1529-remove-unused-css) et [Report du chargement JavaScript](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) dans WP Rocket pour répondre à cette recommandation. Ces fonctionnalités optimisent respectivement les fichiers CSS et JavaScript de sorte qu'ils n'empêchent pas l'affichage de votre page."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Pour résoudre ce problème, activez l'option [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (Minifier les fichiers CSS) dans WP Rocket. Les espaces et les commentaires contenus dans les fichiers CSS de votre site seront supprimés afin de réduire la taille des fichiers et de permettre un téléchargement plus rapide."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Pour résoudre ce problème, activez l'option [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (Minifier les fichiers JavaScript) dans WP Rocket. Les espaces vides et les commentaires seront supprimés des fichiers JavaScript pour réduire leur taille et accélérer leur téléchargement."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Pour résoudre ce problème, activez l'option [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Supprimer les CSS inutilisés) dans WP Rocket. Cette option réduit la taille des pages en supprimant l'ensemble des CSS et feuilles de style inutilisés et conserve uniquement le CSS utilisé pour chaque page."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Pour résoudre ce problème, activez l'option [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (Reporter l'exécution de JavaScript) dans WP Rocket. Activer cette option permet d'améliorer le chargement de votre page en reportant l'exécution des scripts jusqu'à l'intervention de l'utilisateur. Si votre site inclut des iFrames, vous pouvez également utiliser les options de WP Rocket [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (LazyLoad pour iFrames et vidéos) et [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (Remplacer l'iFrame YouTube par une image d'aperçu)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Activez l'option \"Imagifier\" dans l'onglet \"Optimisation d'image\" de WP Rocket, puis exécutez Bulk Optimization pour compresser vos images."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Utilisez l'option [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (Précharger les requêtes DNS) dans WP Rocket pour ajouter \"dns-prefetch\" et accélérer la connexion aux domaines externes. De plus, WP Rocket ajoute automatiquement \"preconnect\" au [domaine Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) et à tous les CNAME(S) ajoutés via la fonctionnalité [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (Activer le CDN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Pour résoudre ce problème de polices, activez l'option [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Supprimer les CSS inutilisés) dans WP Rocket. Les polices essentielles de votre site seront préchargées en priorité."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Affichez la calculatrice."}, "report/renderer/report-utils.js | collapseView": {"message": "Réduire la vue"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navigation initiale"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latence de chemin d'accès critique maximale :"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copier l'objet JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Activer/Désactiver le thème sombre"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Imprimer la version complète"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Imprimer le résumé"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Enregistrer en tant que Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Enregistrer au format HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Enregistrer au format JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Ouvrir dans la visionneuse"}, "report/renderer/report-utils.js | errorLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Erreur de rapport : pas d'information d'audit"}, "report/renderer/report-utils.js | expandView": {"message": "Développer la vue"}, "report/renderer/report-utils.js | footerIssue": {"message": "Signaler un problème"}, "report/renderer/report-utils.js | hide": {"message": "Masquer"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Donn<PERSON> de test"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Analyse [Lighthouse](https://developers.google.com/web/tools/lighthouse/) de la page actuelle sur un réseau mobile émulé. Les valeurs sont estimées et peuvent varier."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Autres éléments à vérifier manuellement"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Non applicable"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Opportunité"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Estimation des économies"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON>ts réuss<PERSON>"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Chargement de page initial"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitation personnalisée"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Émulation (ordinateur)"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Aucune émulation"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Version d'axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Puissance du processeur/de la mémoire non limitée"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitation du processeur"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Appareil"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitation de bande passante réseau"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Émulation de l'écran"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User-agent (r<PERSON><PERSON>)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Chargement de page unique"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Ces données proviennent d'un seul chargement de page, contrairement aux données de champ qui résument plusieurs sessions."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Connexion 4G lente"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Inconnu"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Afficher les audits pertinents pour :"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Réduire l'extrait"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Développer l'extrait"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Afficher les ressources tierces"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Limitation fournie par l'environnement"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Cette exécution de Lighthouse a rencontré des problèmes :"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Les valeurs sont estimées et peuvent varier. Le [calcul du score lié aux performances](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) repose directement sur ces statistiques."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Afficher la trace d'origine"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Afficher la trace"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Consultez la carte proportionnelle"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Réussite des audits, mais avec des avertissements"}, "report/renderer/report-utils.js | warningHeader": {"message": "Avertissements : "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON>ut"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Tous les scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Couverture"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON> en double"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Octets de la ressource"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nom"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Activer/<PERSON><PERSON><PERSON>r le tableau"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Octets inutilisés"}}