{"ast": null, "code": "import * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nvar ESC = KeyCode.ESC,\n  TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n    setTriggerVisible = _ref.setTriggerVisible,\n    triggerRef = _ref.triggerRef,\n    menuRef = _ref.menuRef,\n    onVisibleChange = _ref.onVisibleChange;\n  var focusMenuRef = React.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible && triggerRef.current) {\n      var _triggerRef$current, _triggerRef$current$t, _triggerRef$current$t2, _triggerRef$current$t3;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : (_triggerRef$current$t = _triggerRef$current.triggerRef) === null || _triggerRef$current$t === void 0 ? void 0 : (_triggerRef$current$t2 = _triggerRef$current$t.current) === null || _triggerRef$current$t2 === void 0 ? void 0 : (_triggerRef$current$t3 = _triggerRef$current$t2.focus) === null || _triggerRef$current$t3 === void 0 ? void 0 : _triggerRef$current$t3.call(_triggerRef$current$t2);\n      setTriggerVisible(false);\n      if (typeof onVisibleChange === 'function') {\n        onVisibleChange(false);\n      }\n    }\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    var _menuRef$current;\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        if (!focusMenuRef.current && ((_menuRef$current = menuRef.current) === null || _menuRef$current === void 0 ? void 0 : _menuRef$current.focus)) {\n          event.preventDefault();\n          menuRef.current.focus();\n          focusMenuRef.current = true;\n        } else {\n          handleCloseMenuAndReturnFocus();\n        }\n        break;\n    }\n  };\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener('keydown', handleKeyDown);\n      return function () {\n        window.removeEventListener('keydown', handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      return null;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}", "map": {"version": 3, "names": ["React", "KeyCode", "ESC", "TAB", "useAccessibility", "_ref", "visible", "setTriggerVisible", "triggerRef", "menuRef", "onVisibleChange", "focusMenuRef", "useRef", "handleCloseMenuAndReturnFocus", "current", "_triggerRef$current", "_triggerRef$current$t", "_triggerRef$current$t2", "_triggerRef$current$t3", "focus", "call", "handleKeyDown", "event", "_menuRef$current", "keyCode", "preventDefault", "useEffect", "window", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-dropdown/es/hooks/useAccessibility.js"], "sourcesContent": ["import * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nvar ESC = KeyCode.ESC,\n    TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n      setTriggerVisible = _ref.setTriggerVisible,\n      triggerRef = _ref.triggerRef,\n      menuRef = _ref.menuRef,\n      onVisibleChange = _ref.onVisibleChange;\n  var focusMenuRef = React.useRef(false);\n\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible && triggerRef.current) {\n      var _triggerRef$current, _triggerRef$current$t, _triggerRef$current$t2, _triggerRef$current$t3;\n\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : (_triggerRef$current$t = _triggerRef$current.triggerRef) === null || _triggerRef$current$t === void 0 ? void 0 : (_triggerRef$current$t2 = _triggerRef$current$t.current) === null || _triggerRef$current$t2 === void 0 ? void 0 : (_triggerRef$current$t3 = _triggerRef$current$t2.focus) === null || _triggerRef$current$t3 === void 0 ? void 0 : _triggerRef$current$t3.call(_triggerRef$current$t2);\n      setTriggerVisible(false);\n\n      if (typeof onVisibleChange === 'function') {\n        onVisibleChange(false);\n      }\n    }\n  };\n\n  var handleKeyDown = function handleKeyDown(event) {\n    var _menuRef$current;\n\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n\n      case TAB:\n        if (!focusMenuRef.current && ((_menuRef$current = menuRef.current) === null || _menuRef$current === void 0 ? void 0 : _menuRef$current.focus)) {\n          event.preventDefault();\n          menuRef.current.focus();\n          focusMenuRef.current = true;\n        } else {\n          handleCloseMenuAndReturnFocus();\n        }\n\n        break;\n    }\n  };\n\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener('keydown', handleKeyDown);\n      return function () {\n        window.removeEventListener('keydown', handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n\n    return function () {\n      return null;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,GAAG,GAAGD,OAAO,CAACC,GAAG;EACjBC,GAAG,GAAGF,OAAO,CAACE,GAAG;AACrB,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC7C,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACtBC,iBAAiB,GAAGF,IAAI,CAACE,iBAAiB;IAC1CC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BC,OAAO,GAAGJ,IAAI,CAACI,OAAO;IACtBC,eAAe,GAAGL,IAAI,CAACK,eAAe;EAC1C,IAAIC,YAAY,GAAGX,KAAK,CAACY,MAAM,CAAC,KAAK,CAAC;EAEtC,IAAIC,6BAA6B,GAAG,SAASA,6BAA6BA,CAAA,EAAG;IAC3E,IAAIP,OAAO,IAAIE,UAAU,CAACM,OAAO,EAAE;MACjC,IAAIC,mBAAmB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;MAE9F,CAACH,mBAAmB,GAAGP,UAAU,CAACM,OAAO,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,mBAAmB,CAACP,UAAU,MAAM,IAAI,IAAIQ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACF,OAAO,MAAM,IAAI,IAAIG,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,sBAAsB,CAACE,KAAK,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACE,IAAI,CAACH,sBAAsB,CAAC;MACxeV,iBAAiB,CAAC,KAAK,CAAC;MAExB,IAAI,OAAOG,eAAe,KAAK,UAAU,EAAE;QACzCA,eAAe,CAAC,KAAK,CAAC;MACxB;IACF;EACF,CAAC;EAED,IAAIW,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,IAAIC,gBAAgB;IAEpB,QAAQD,KAAK,CAACE,OAAO;MACnB,KAAKtB,GAAG;QACNW,6BAA6B,CAAC,CAAC;QAC/B;MAEF,KAAKV,GAAG;QACN,IAAI,CAACQ,YAAY,CAACG,OAAO,KAAK,CAACS,gBAAgB,GAAGd,OAAO,CAACK,OAAO,MAAM,IAAI,IAAIS,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACJ,KAAK,CAAC,EAAE;UAC7IG,KAAK,CAACG,cAAc,CAAC,CAAC;UACtBhB,OAAO,CAACK,OAAO,CAACK,KAAK,CAAC,CAAC;UACvBR,YAAY,CAACG,OAAO,GAAG,IAAI;QAC7B,CAAC,MAAM;UACLD,6BAA6B,CAAC,CAAC;QACjC;QAEA;IACJ;EACF,CAAC;EAEDb,KAAK,CAAC0B,SAAS,CAAC,YAAY;IAC1B,IAAIpB,OAAO,EAAE;MACXqB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;MACjD,OAAO,YAAY;QACjBM,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;QACpDV,YAAY,CAACG,OAAO,GAAG,KAAK;MAC9B,CAAC;IACH;IAEA,OAAO,YAAY;MACjB,OAAO,IAAI;IACb,CAAC;EACH,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}