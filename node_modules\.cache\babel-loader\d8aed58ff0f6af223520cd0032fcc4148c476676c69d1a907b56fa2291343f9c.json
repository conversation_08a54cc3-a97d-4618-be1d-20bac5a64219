{"ast": null, "code": "/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* IndexReducer - conversione di un prodotto con metodo reducers \n*\n*/\nimport { combineReducers } from 'redux';\nimport productReducer from './productReducer';\nexport default combineReducers({\n  productState: productReducer\n});", "map": {"version": 3, "names": ["combineReducers", "productReducer", "productState"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/carrello/reducers/index.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* IndexReducer - conversione di un prodotto con metodo reducers \n*\n*/\nimport { combineReducers } from 'redux';\nimport productReducer from './productReducer';\n\nexport default combineReducers({\n    productState: productReducer\n});"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,eAAeD,eAAe,CAAC;EAC3BE,YAAY,EAAED;AAClB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}