{"ast": null, "code": "\"use strict\";\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = exports.SizeContextProvider = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar SizeContext = /*#__PURE__*/React.createContext(undefined);\nvar SizeContextProvider = function SizeContextProvider(_ref) {\n  var children = _ref.children,\n    size = _ref.size;\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {\n    return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n      value: size || originSize\n    }, children);\n  });\n};\nexports.SizeContextProvider = SizeContextProvider;\nvar _default = SizeContext;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_typeof", "require", "Object", "defineProperty", "exports", "value", "SizeContextProvider", "React", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "SizeContext", "createContext", "undefined", "_ref", "children", "size", "createElement", "Consumer", "originSize", "Provider", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/config-provider/SizeContext.js"], "sourcesContent": ["\"use strict\";\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = exports.SizeContextProvider = void 0;\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar SizeContext = /*#__PURE__*/React.createContext(undefined);\n\nvar SizeContextProvider = function SizeContextProvider(_ref) {\n  var children = _ref.children,\n      size = _ref.size;\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {\n    return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n      value: size || originSize\n    }, children);\n  });\n};\n\nexports.SizeContextProvider = SizeContextProvider;\nvar _default = SizeContext;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AAEtDC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGA,OAAO,CAACE,mBAAmB,GAAG,KAAK,CAAC;AAEzD,IAAIC,KAAK,GAAGC,uBAAuB,CAACP,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,SAASQ,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASF,uBAAuBA,CAACM,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAId,OAAO,CAACc,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGlB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACmB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIpB,MAAM,CAACqB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGlB,MAAM,CAACmB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEzB,MAAM,CAACC,cAAc,CAACgB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIS,WAAW,GAAG,aAAarB,KAAK,CAACsB,aAAa,CAACC,SAAS,CAAC;AAE7D,IAAIxB,mBAAmB,GAAG,SAASA,mBAAmBA,CAACyB,IAAI,EAAE;EAC3D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,OAAO,aAAa1B,KAAK,CAAC2B,aAAa,CAACN,WAAW,CAACO,QAAQ,EAAE,IAAI,EAAE,UAAUC,UAAU,EAAE;IACxF,OAAO,aAAa7B,KAAK,CAAC2B,aAAa,CAACN,WAAW,CAACS,QAAQ,EAAE;MAC5DhC,KAAK,EAAE4B,IAAI,IAAIG;IACjB,CAAC,EAAEJ,QAAQ,CAAC;EACd,CAAC,CAAC;AACJ,CAAC;AAED5B,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB;AACjD,IAAIgC,QAAQ,GAAGV,WAAW;AAC1BxB,OAAO,CAAC,SAAS,CAAC,GAAGkC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}