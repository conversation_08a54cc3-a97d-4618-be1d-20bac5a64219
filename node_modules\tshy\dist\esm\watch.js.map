{"version": 3, "file": "watch.js", "sourceRoot": "", "sources": ["../../src/watch.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,2CAA2C;AAC3C,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAA;AACrC,OAAO,EAAE,KAAK,EAAgB,MAAM,UAAU,CAAA;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,IAAI,CAAA;AACjC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,MAAM,CAAA;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,KAAK,CAAA;AACnC,OAAO,KAAK,WAAW,MAAM,cAAc,CAAA;AAE3C,MAAM,MAAM,GAAG,GAAW,EAAE;IAC1B,IAAI,CAAC;QACH,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CACnD,CAAA;QACD,qBAAqB;IACvB,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,MAAM,CAAA;IACf,CAAC;IACD,oBAAoB;AACtB,CAAC,CAAA;AACD,IAAI,UAAU,GAAW,MAAM,CAAA;AAE/B,MAAM,CAAC,MAAM,OAAO,GAAiB;IACnC,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,sBAAsB,EAAE,IAAI;IAC5B,OAAO,EAAE,IAAI,CAAC,EAAE;QACd,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACvB,IAAI,CAAC,KAAK,KAAK;YAAE,OAAO,IAAI,CAAA;QAC5B,IAAI,CAAC,KAAK,KAAK;YAAE,OAAO,IAAI,CAAA;QAC5B,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;QAC1C,OAAO,KAAK,CAAA;IACd,CAAC;CACF,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAA;AAClD,MAAM,CAAC,MAAM,KAAK,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAA;AAClD,MAAM,CAAC,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;AACnC,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAA;AAC/C,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AACpC,MAAM,CAAC,MAAM,GAAG,GAAG,aAAa,CAC9B,IAAI,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CACvC,CAAA;AAED,eAAe,GAAG,EAAE;IAClB,IAAI,QAAQ,GAAG,KAAK,CAAA;IACpB,IAAI,WAAW,GAAG,KAAK,CAAA;IACvB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IACvC,MAAM,KAAK,GAAG,GAAG,EAAE;QACjB,QAAQ,GAAG,IAAI,CAAA;QACf,WAAW,GAAG,KAAK,CAAA;QACnB,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAA;QAClE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACjC,IAAI,IAAI,IAAI,MAAM;gBAAE,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;;gBAClD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;YAChE,IAAI,WAAW;gBAAE,KAAK,EAAE,CAAA;;gBACnB,QAAQ,GAAG,KAAK,CAAA;QACvB,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IACD,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;QAC7B,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACvB,IAAI,CAAC,KAAK,KAAK;YAAE,OAAM;QACvB,IAAI,CAAC,KAAK,MAAM,EAAE,CAAC;YACjB,qCAAqC;YACrC,MAAM,OAAO,GAAG,MAAM,EAAE,CAAA;YACxB,oBAAoB;YACpB,IAAI,OAAO,KAAK,UAAU;gBAAE,OAAM;YAClC,UAAU,GAAG,OAAO,CAAA;QACtB,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,MAAM;gBAAE,WAAW,GAAG,IAAI,CAAA;YACpC,OAAM;QACR,CAAC;QACD,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;QAC3C,KAAK,EAAE,CAAA;IACT,CAAC,CAAC,CAAA;IACF,KAAK,EAAE,CAAA;AACT,CAAC,CAAA", "sourcesContent": ["// any time the root package.json or any typescript files in src\n// are changed/added/removed, run the build\nimport chalk from 'chalk'\nimport { spawn } from 'child_process'\nimport { watch, WatchOptions } from 'chokidar'\nimport { readFileSync } from 'fs'\nimport { resolve, sep } from 'path'\nimport { fileURLToPath } from 'url'\nimport * as tshyConsole from './console.js'\n\nconst pjData = (): string => {\n  try {\n    return JSON.stringify(\n      JSON.parse(readFileSync('./package.json', 'utf8')),\n    )\n    /* c8 ignore start */\n  } catch {\n    return 'null'\n  }\n  /* c8 ignore stop */\n}\nlet lastPJData: string = 'null'\n\nexport const options: WatchOptions = {\n  persistent: true,\n  ignoreInitial: true,\n  ignorePermissionErrors: true,\n  ignored: path => {\n    const r = resolve(path)\n    if (r === srcPJ) return true\n    if (r === srcNM) return true\n    if (r.startsWith(srcNM + sep)) return true\n    return false\n  },\n}\n\nexport const srcPJ = resolve('./src/package.json')\nexport const srcNM = resolve('./src/node_modules')\nexport const src = resolve('./src')\nexport const rootPJ = resolve('./package.json')\nexport const targets = [src, rootPJ]\nexport const bin = fileURLToPath(\n  new URL('./index.js', import.meta.url),\n)\n\nexport default () => {\n  let building = false\n  let needRebuild = false\n  const watcher = watch(targets, options)\n  const build = () => {\n    building = true\n    needRebuild = false\n    const child = spawn(process.execPath, [bin], { stdio: 'inherit' })\n    child.on('close', (code, signal) => {\n      if (code || signal) tshyConsole.error({ code, signal })\n      else console.log(chalk.green('build success'), { code, signal })\n      if (needRebuild) build()\n      else building = false\n    })\n  }\n  watcher.on('all', (ev, path) => {\n    const r = resolve(path)\n    if (r === srcPJ) return\n    if (r === rootPJ) {\n      // check if the data actually changed\n      const newData = pjData()\n      /* c8 ignore next */\n      if (newData === lastPJData) return\n      lastPJData = newData\n    }\n    if (building) {\n      if (r !== rootPJ) needRebuild = true\n      return\n    }\n    tshyConsole.debug(chalk.cyan.dim(ev), path)\n    build()\n  })\n  build()\n}\n"]}