{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n  _excluded2 = [\"fixed\"];\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { INTERNAL_COL_DEFINE } from '../utils/legacyUtil';\nimport { EXPAND_COLUMN } from '../constant';\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n      props = _ref.props;\n    var nodeChildren = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n    return column;\n  });\n}\nfunction flatColumns(columns) {\n  return columns.reduce(function (list, column) {\n    var fixed = column.fixed; // Convert `fixed='true'` to `fixed='left'` instead\n\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({}, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\nfunction warningFixed(flattenColumns) {\n  var allFixLeft = true;\n  for (var i = 0; i < flattenColumns.length; i += 1) {\n    var col = flattenColumns[i];\n    if (allFixLeft && col.fixed !== 'left') {\n      allFixLeft = false;\n    } else if (!allFixLeft && col.fixed === 'left') {\n      warning(false, \"Index \".concat(i - 1, \" of `columns` missing `fixed='left'` prop.\"));\n      break;\n    }\n  }\n  var allFixRight = true;\n  for (var _i = flattenColumns.length - 1; _i >= 0; _i -= 1) {\n    var _col = flattenColumns[_i];\n    if (allFixRight && _col.fixed !== 'right') {\n      allFixRight = false;\n    } else if (!allFixRight && _col.fixed === 'right') {\n      warning(false, \"Index \".concat(_i + 1, \" of `columns` missing `fixed='right'` prop.\"));\n      break;\n    }\n  }\n}\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n      restProps = _objectWithoutProperties(column, _excluded2); // Convert `fixed='left'` to `fixed='right'` instead\n\n    var parsedFixed = fixed;\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n/**\n * Parse `columns` & `children` into `columns`.\n */\n\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n    columns = _ref2.columns,\n    children = _ref2.children,\n    expandable = _ref2.expandable,\n    expandedKeys = _ref2.expandedKeys,\n    getRowKey = _ref2.getRowKey,\n    onTriggerExpand = _ref2.onTriggerExpand,\n    expandIcon = _ref2.expandIcon,\n    rowExpandable = _ref2.rowExpandable,\n    expandIconColumnIndex = _ref2.expandIconColumnIndex,\n    direction = _ref2.direction,\n    expandRowByClick = _ref2.expandRowByClick,\n    columnWidth = _ref2.columnWidth,\n    fixed = _ref2.fixed;\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]); // ========================== Expand ==========================\n\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var _expandColumn;\n      var cloneColumns = baseColumns.slice(); // >>> Warning if use `expandIconColumnIndex`\n\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      } // >>> Insert expand column if not exist\n\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n        if (expandColIndex >= 0) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n      } // >>> Deduplicate additional expand column\n\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      }); // >>> Check if expand column need to fixed\n\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n      if ((fixed === 'left' || fixed) && !expandIconColumnIndex) {\n        fixedColumn = 'left';\n      } else if ((fixed === 'right' || fixed) && expandIconColumnIndex === baseColumns.length) {\n        fixedColumn = 'right';\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      } // >>> Create expandable column\n\n      var expandColumn = (_expandColumn = {}, _defineProperty(_expandColumn, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), _defineProperty(_expandColumn, \"title\", ''), _defineProperty(_expandColumn, \"fixed\", fixedColumn), _defineProperty(_expandColumn, \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), _defineProperty(_expandColumn, \"width\", columnWidth), _defineProperty(_expandColumn, \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n        return icon;\n      }), _expandColumn);\n      return cloneColumns.map(function (col) {\n        return col === EXPAND_COLUMN ? expandColumn : col;\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction]); // ========================= Transform ========================\n\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    } // Always provides at least one column for table display\n\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n    return finalColumns;\n  }, [transformColumns, withExpandColumns, direction]); // ========================== Flatten =========================\n\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n    return flatColumns(mergedColumns);\n  }, [mergedColumns, direction]); // Only check out of production since it's waste for each render\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningFixed(flattenColumns);\n  }\n  return [mergedColumns, flattenColumns];\n}\nexport default useColumns;", "map": {"version": 3, "names": ["_defineProperty", "_toConsumableArray", "_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "warning", "toArray", "INTERNAL_COL_DEFINE", "EXPAND_COLUMN", "convertChildrenToColumns", "children", "filter", "node", "isValidElement", "map", "_ref", "key", "props", "nodeChildren", "restProps", "column", "flatColumns", "columns", "reduce", "list", "fixed", "parsedFixed", "subColumns", "length", "concat", "subColum", "warningFixed", "flattenColumns", "allFixLeft", "i", "col", "allFixRight", "_i", "_col", "revertForRtl", "useColumns", "_ref2", "transformColumns", "prefixCls", "expandable", "expandedKeys", "getRowKey", "onTriggerExpand", "expandIcon", "rowExpandable", "expandIconColumnIndex", "direction", "expandRowByClick", "columnWidth", "baseColumns", "useMemo", "withExpandColumns", "_expandColumn", "cloneColumns", "slice", "process", "env", "NODE_ENV", "includes", "expandColIndex", "splice", "c", "expandColumnIndex", "indexOf", "index", "prevColumn", "fixedColumn", "expandColumn", "className", "columnType", "render", "_", "record", "<PERSON><PERSON><PERSON>", "expanded", "has", "recordExpandable", "icon", "onExpand", "createElement", "onClick", "e", "stopPropagation", "mergedColumns", "finalColumns"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/hooks/useColumns.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n    _excluded2 = [\"fixed\"];\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { INTERNAL_COL_DEFINE } from '../utils/legacyUtil';\nimport { EXPAND_COLUMN } from '../constant';\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n        props = _ref.props;\n\n    var nodeChildren = props.children,\n        restProps = _objectWithoutProperties(props, _excluded);\n\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n\n    return column;\n  });\n}\n\nfunction flatColumns(columns) {\n  return columns.reduce(function (list, column) {\n    var fixed = column.fixed; // Convert `fixed='true'` to `fixed='left'` instead\n\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var subColumns = column.children;\n\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({}, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\n\nfunction warningFixed(flattenColumns) {\n  var allFixLeft = true;\n\n  for (var i = 0; i < flattenColumns.length; i += 1) {\n    var col = flattenColumns[i];\n\n    if (allFixLeft && col.fixed !== 'left') {\n      allFixLeft = false;\n    } else if (!allFixLeft && col.fixed === 'left') {\n      warning(false, \"Index \".concat(i - 1, \" of `columns` missing `fixed='left'` prop.\"));\n      break;\n    }\n  }\n\n  var allFixRight = true;\n\n  for (var _i = flattenColumns.length - 1; _i >= 0; _i -= 1) {\n    var _col = flattenColumns[_i];\n\n    if (allFixRight && _col.fixed !== 'right') {\n      allFixRight = false;\n    } else if (!allFixRight && _col.fixed === 'right') {\n      warning(false, \"Index \".concat(_i + 1, \" of `columns` missing `fixed='right'` prop.\"));\n      break;\n    }\n  }\n}\n\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n        restProps = _objectWithoutProperties(column, _excluded2); // Convert `fixed='left'` to `fixed='right'` instead\n\n\n    var parsedFixed = fixed;\n\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n/**\n * Parse `columns` & `children` into `columns`.\n */\n\n\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n      columns = _ref2.columns,\n      children = _ref2.children,\n      expandable = _ref2.expandable,\n      expandedKeys = _ref2.expandedKeys,\n      getRowKey = _ref2.getRowKey,\n      onTriggerExpand = _ref2.onTriggerExpand,\n      expandIcon = _ref2.expandIcon,\n      rowExpandable = _ref2.rowExpandable,\n      expandIconColumnIndex = _ref2.expandIconColumnIndex,\n      direction = _ref2.direction,\n      expandRowByClick = _ref2.expandRowByClick,\n      columnWidth = _ref2.columnWidth,\n      fixed = _ref2.fixed;\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]); // ========================== Expand ==========================\n\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var _expandColumn;\n\n      var cloneColumns = baseColumns.slice(); // >>> Warning if use `expandIconColumnIndex`\n\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      } // >>> Insert expand column if not exist\n\n\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n\n        if (expandColIndex >= 0) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n      } // >>> Deduplicate additional expand column\n\n\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      }); // >>> Check if expand column need to fixed\n\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n\n      if ((fixed === 'left' || fixed) && !expandIconColumnIndex) {\n        fixedColumn = 'left';\n      } else if ((fixed === 'right' || fixed) && expandIconColumnIndex === baseColumns.length) {\n        fixedColumn = 'right';\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      } // >>> Create expandable column\n\n\n      var expandColumn = (_expandColumn = {}, _defineProperty(_expandColumn, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), _defineProperty(_expandColumn, \"title\", ''), _defineProperty(_expandColumn, \"fixed\", fixedColumn), _defineProperty(_expandColumn, \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), _defineProperty(_expandColumn, \"width\", columnWidth), _defineProperty(_expandColumn, \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n\n        return icon;\n      }), _expandColumn);\n      return cloneColumns.map(function (col) {\n        return col === EXPAND_COLUMN ? expandColumn : col;\n      });\n    }\n\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction]); // ========================= Transform ========================\n\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    } // Always provides at least one column for table display\n\n\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n\n    return finalColumns;\n  }, [transformColumns, withExpandColumns, direction]); // ========================== Flatten =========================\n\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n\n    return flatColumns(mergedColumns);\n  }, [mergedColumns, direction]); // Only check out of production since it's waste for each render\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningFixed(flattenColumns);\n  }\n\n  return [mergedColumns, flattenColumns];\n}\n\nexport default useColumns;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;EACxBC,UAAU,GAAG,CAAC,OAAO,CAAC;AAC1B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,aAAa,QAAQ,aAAa;AAC3C,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAE;EACjD,OAAOJ,OAAO,CAACI,QAAQ,CAAC,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAE;IAC9C,OAAO,aAAaR,KAAK,CAACS,cAAc,CAACD,IAAI,CAAC;EAChD,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;IACrB,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;MACdC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAEtB,IAAIC,YAAY,GAAGD,KAAK,CAACP,QAAQ;MAC7BS,SAAS,GAAGlB,wBAAwB,CAACgB,KAAK,EAAEf,SAAS,CAAC;IAE1D,IAAIkB,MAAM,GAAGpB,aAAa,CAAC;MACzBgB,GAAG,EAAEA;IACP,CAAC,EAAEG,SAAS,CAAC;IAEb,IAAID,YAAY,EAAE;MAChBE,MAAM,CAACV,QAAQ,GAAGD,wBAAwB,CAACS,YAAY,CAAC;IAC1D;IAEA,OAAOE,MAAM;EACf,CAAC,CAAC;AACJ;AAEA,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,OAAOA,OAAO,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAEJ,MAAM,EAAE;IAC5C,IAAIK,KAAK,GAAGL,MAAM,CAACK,KAAK,CAAC,CAAC;;IAE1B,IAAIC,WAAW,GAAGD,KAAK,KAAK,IAAI,GAAG,MAAM,GAAGA,KAAK;IACjD,IAAIE,UAAU,GAAGP,MAAM,CAACV,QAAQ;IAEhC,IAAIiB,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,OAAO,EAAE,CAACC,MAAM,CAAC9B,kBAAkB,CAACyB,IAAI,CAAC,EAAEzB,kBAAkB,CAACsB,WAAW,CAACM,UAAU,CAAC,CAACb,GAAG,CAAC,UAAUgB,QAAQ,EAAE;QAC5G,OAAO9B,aAAa,CAAC;UACnByB,KAAK,EAAEC;QACT,CAAC,EAAEI,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC;IACN;IAEA,OAAO,EAAE,CAACD,MAAM,CAAC9B,kBAAkB,CAACyB,IAAI,CAAC,EAAE,CAACxB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACvFK,KAAK,EAAEC;IACT,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;AACR;AAEA,SAASK,YAAYA,CAACC,cAAc,EAAE;EACpC,IAAIC,UAAU,GAAG,IAAI;EAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACJ,MAAM,EAAEM,CAAC,IAAI,CAAC,EAAE;IACjD,IAAIC,GAAG,GAAGH,cAAc,CAACE,CAAC,CAAC;IAE3B,IAAID,UAAU,IAAIE,GAAG,CAACV,KAAK,KAAK,MAAM,EAAE;MACtCQ,UAAU,GAAG,KAAK;IACpB,CAAC,MAAM,IAAI,CAACA,UAAU,IAAIE,GAAG,CAACV,KAAK,KAAK,MAAM,EAAE;MAC9CpB,OAAO,CAAC,KAAK,EAAE,QAAQ,CAACwB,MAAM,CAACK,CAAC,GAAG,CAAC,EAAE,4CAA4C,CAAC,CAAC;MACpF;IACF;EACF;EAEA,IAAIE,WAAW,GAAG,IAAI;EAEtB,KAAK,IAAIC,EAAE,GAAGL,cAAc,CAACJ,MAAM,GAAG,CAAC,EAAES,EAAE,IAAI,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE;IACzD,IAAIC,IAAI,GAAGN,cAAc,CAACK,EAAE,CAAC;IAE7B,IAAID,WAAW,IAAIE,IAAI,CAACb,KAAK,KAAK,OAAO,EAAE;MACzCW,WAAW,GAAG,KAAK;IACrB,CAAC,MAAM,IAAI,CAACA,WAAW,IAAIE,IAAI,CAACb,KAAK,KAAK,OAAO,EAAE;MACjDpB,OAAO,CAAC,KAAK,EAAE,QAAQ,CAACwB,MAAM,CAACQ,EAAE,GAAG,CAAC,EAAE,6CAA6C,CAAC,CAAC;MACtF;IACF;EACF;AACF;AAEA,SAASE,YAAYA,CAACjB,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACR,GAAG,CAAC,UAAUM,MAAM,EAAE;IACnC,IAAIK,KAAK,GAAGL,MAAM,CAACK,KAAK;MACpBN,SAAS,GAAGlB,wBAAwB,CAACmB,MAAM,EAAEjB,UAAU,CAAC,CAAC,CAAC;;IAG9D,IAAIuB,WAAW,GAAGD,KAAK;IAEvB,IAAIA,KAAK,KAAK,MAAM,EAAE;MACpBC,WAAW,GAAG,OAAO;IACvB,CAAC,MAAM,IAAID,KAAK,KAAK,OAAO,EAAE;MAC5BC,WAAW,GAAG,MAAM;IACtB;IAEA,OAAO1B,aAAa,CAAC;MACnByB,KAAK,EAAEC;IACT,CAAC,EAAEP,SAAS,CAAC;EACf,CAAC,CAAC;AACJ;AACA;AACA;AACA;;AAGA,SAASqB,UAAUA,CAACC,KAAK,EAAEC,gBAAgB,EAAE;EAC3C,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BrB,OAAO,GAAGmB,KAAK,CAACnB,OAAO;IACvBZ,QAAQ,GAAG+B,KAAK,CAAC/B,QAAQ;IACzBkC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,eAAe,GAAGN,KAAK,CAACM,eAAe;IACvCC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,qBAAqB,GAAGT,KAAK,CAACS,qBAAqB;IACnDC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,gBAAgB,GAAGX,KAAK,CAACW,gBAAgB;IACzCC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/B5B,KAAK,GAAGgB,KAAK,CAAChB,KAAK;EACvB,IAAI6B,WAAW,GAAGlD,KAAK,CAACmD,OAAO,CAAC,YAAY;IAC1C,OAAOjC,OAAO,IAAIb,wBAAwB,CAACC,QAAQ,CAAC;EACtD,CAAC,EAAE,CAACY,OAAO,EAAEZ,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEzB,IAAI8C,iBAAiB,GAAGpD,KAAK,CAACmD,OAAO,CAAC,YAAY;IAChD,IAAIX,UAAU,EAAE;MACd,IAAIa,aAAa;MAEjB,IAAIC,YAAY,GAAGJ,WAAW,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;;MAExC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIZ,qBAAqB,IAAI,CAAC,EAAE;QACvE7C,OAAO,CAAC,KAAK,EAAE,+FAA+F,CAAC;MACjH,CAAC,CAAC;;MAGF,IAAI,CAACqD,YAAY,CAACK,QAAQ,CAACvD,aAAa,CAAC,EAAE;QACzC,IAAIwD,cAAc,GAAGd,qBAAqB,IAAI,CAAC;QAE/C,IAAIc,cAAc,IAAI,CAAC,EAAE;UACvBN,YAAY,CAACO,MAAM,CAACD,cAAc,EAAE,CAAC,EAAExD,aAAa,CAAC;QACvD;MACF,CAAC,CAAC;;MAGF,IAAIoD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIJ,YAAY,CAAC/C,MAAM,CAAC,UAAUuD,CAAC,EAAE;QAC5E,OAAOA,CAAC,KAAK1D,aAAa;MAC5B,CAAC,CAAC,CAACoB,MAAM,GAAG,CAAC,EAAE;QACbvB,OAAO,CAAC,KAAK,EAAE,yDAAyD,CAAC;MAC3E;MAEA,IAAI8D,iBAAiB,GAAGT,YAAY,CAACU,OAAO,CAAC5D,aAAa,CAAC;MAC3DkD,YAAY,GAAGA,YAAY,CAAC/C,MAAM,CAAC,UAAUS,MAAM,EAAEiD,KAAK,EAAE;QAC1D,OAAOjD,MAAM,KAAKZ,aAAa,IAAI6D,KAAK,KAAKF,iBAAiB;MAChE,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIG,UAAU,GAAGhB,WAAW,CAACa,iBAAiB,CAAC;MAC/C,IAAII,WAAW;MAEf,IAAI,CAAC9C,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,CAACyB,qBAAqB,EAAE;QACzDqB,WAAW,GAAG,MAAM;MACtB,CAAC,MAAM,IAAI,CAAC9C,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAKyB,qBAAqB,KAAKI,WAAW,CAAC1B,MAAM,EAAE;QACvF2C,WAAW,GAAG,OAAO;MACvB,CAAC,MAAM;QACLA,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAAC7C,KAAK,GAAG,IAAI;MACpD,CAAC,CAAC;;MAGF,IAAI+C,YAAY,IAAIf,aAAa,GAAG,CAAC,CAAC,EAAE3D,eAAe,CAAC2D,aAAa,EAAElD,mBAAmB,EAAE;QAC1FkE,SAAS,EAAE,EAAE,CAAC5C,MAAM,CAACc,SAAS,EAAE,kBAAkB,CAAC;QACnD+B,UAAU,EAAE;MACd,CAAC,CAAC,EAAE5E,eAAe,CAAC2D,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE3D,eAAe,CAAC2D,aAAa,EAAE,OAAO,EAAEc,WAAW,CAAC,EAAEzE,eAAe,CAAC2D,aAAa,EAAE,WAAW,EAAE,EAAE,CAAC5B,MAAM,CAACc,SAAS,EAAE,uBAAuB,CAAC,CAAC,EAAE7C,eAAe,CAAC2D,aAAa,EAAE,OAAO,EAAEJ,WAAW,CAAC,EAAEvD,eAAe,CAAC2D,aAAa,EAAE,QAAQ,EAAE,SAASkB,MAAMA,CAACC,CAAC,EAAEC,MAAM,EAAER,KAAK,EAAE;QAClU,IAAIS,MAAM,GAAGhC,SAAS,CAAC+B,MAAM,EAAER,KAAK,CAAC;QACrC,IAAIU,QAAQ,GAAGlC,YAAY,CAACmC,GAAG,CAACF,MAAM,CAAC;QACvC,IAAIG,gBAAgB,GAAGhC,aAAa,GAAGA,aAAa,CAAC4B,MAAM,CAAC,GAAG,IAAI;QACnE,IAAIK,IAAI,GAAGlC,UAAU,CAAC;UACpBL,SAAS,EAAEA,SAAS;UACpBoC,QAAQ,EAAEA,QAAQ;UAClBnC,UAAU,EAAEqC,gBAAgB;UAC5BJ,MAAM,EAAEA,MAAM;UACdM,QAAQ,EAAEpC;QACZ,CAAC,CAAC;QAEF,IAAIK,gBAAgB,EAAE;UACpB,OAAO,aAAahD,KAAK,CAACgF,aAAa,CAAC,MAAM,EAAE;YAC9CC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B;UACF,CAAC,EAAEL,IAAI,CAAC;QACV;QAEA,OAAOA,IAAI;MACb,CAAC,CAAC,EAAEzB,aAAa,CAAC;MAClB,OAAOC,YAAY,CAAC5C,GAAG,CAAC,UAAUqB,GAAG,EAAE;QACrC,OAAOA,GAAG,KAAK3B,aAAa,GAAGgE,YAAY,GAAGrC,GAAG;MACnD,CAAC,CAAC;IACJ;IAEA,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIR,WAAW,CAACS,QAAQ,CAACvD,aAAa,CAAC,EAAE;MAChFH,OAAO,CAAC,KAAK,EAAE,0EAA0E,CAAC;IAC5F;IAEA,OAAOiD,WAAW,CAAC3C,MAAM,CAAC,UAAUwB,GAAG,EAAE;MACvC,OAAOA,GAAG,KAAK3B,aAAa;IAC9B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACoC,UAAU,EAAEU,WAAW,EAAER,SAAS,EAAED,YAAY,EAAEG,UAAU,EAAEG,SAAS,CAAC,CAAC,CAAC,CAAC;;EAE/E,IAAIqC,aAAa,GAAGpF,KAAK,CAACmD,OAAO,CAAC,YAAY;IAC5C,IAAIkC,YAAY,GAAGjC,iBAAiB;IAEpC,IAAId,gBAAgB,EAAE;MACpB+C,YAAY,GAAG/C,gBAAgB,CAAC+C,YAAY,CAAC;IAC/C,CAAC,CAAC;;IAGF,IAAI,CAACA,YAAY,CAAC7D,MAAM,EAAE;MACxB6D,YAAY,GAAG,CAAC;QACdd,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;UACxB,OAAO,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IAEA,OAAOc,YAAY;EACrB,CAAC,EAAE,CAAC/C,gBAAgB,EAAEc,iBAAiB,EAAEL,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEtD,IAAInB,cAAc,GAAG5B,KAAK,CAACmD,OAAO,CAAC,YAAY;IAC7C,IAAIJ,SAAS,KAAK,KAAK,EAAE;MACvB,OAAOZ,YAAY,CAAClB,WAAW,CAACmE,aAAa,CAAC,CAAC;IACjD;IAEA,OAAOnE,WAAW,CAACmE,aAAa,CAAC;EACnC,CAAC,EAAE,CAACA,aAAa,EAAErC,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC/B,YAAY,CAACC,cAAc,CAAC;EAC9B;EAEA,OAAO,CAACwD,aAAa,EAAExD,cAAc,CAAC;AACxC;AAEA,eAAeQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}