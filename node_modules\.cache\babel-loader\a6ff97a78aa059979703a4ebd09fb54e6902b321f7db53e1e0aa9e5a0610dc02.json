{"ast": null, "code": "var getMapData = require('./_getMapData');\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n    size = data.size;\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\nmodule.exports = mapCacheSet;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheSet", "key", "value", "data", "size", "set", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/lodash/_mapCacheSet.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nmodule.exports = mapCacheSet;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC/B,IAAIC,IAAI,GAAGL,UAAU,CAAC,IAAI,EAAEG,GAAG,CAAC;IAC5BG,IAAI,GAAGD,IAAI,CAACC,IAAI;EAEpBD,IAAI,CAACE,GAAG,CAACJ,GAAG,EAAEC,KAAK,CAAC;EACpB,IAAI,CAACE,IAAI,IAAID,IAAI,CAACC,IAAI,IAAIA,IAAI,GAAG,CAAC,GAAG,CAAC;EACtC,OAAO,IAAI;AACb;AAEAE,MAAM,CAACC,OAAO,GAAGP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}