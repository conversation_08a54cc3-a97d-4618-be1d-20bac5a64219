{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n\n/* eslint react/prop-types: 0 */\nimport * as React from 'react';\nimport OriginCSSMotion from './CSSMotion';\nimport { supportTransition } from './util/motion';\nimport { STATUS_ADD, STATUS_KEEP, STATUS_REMOVE, STATUS_REMOVED, diffKeys, parseKeys } from './util/diff';\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\n\nexport function genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : OriginCSSMotion;\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    _inherits(CSSMotionList, _React$Component);\n    var _super = _createSuper(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      _classCallCheck(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      _this.state = {\n        keyEntities: []\n      };\n      _this.removeKey = function (removeKey) {\n        var keyEntities = _this.state.keyEntities;\n        var nextKeyEntities = keyEntities.map(function (entity) {\n          if (entity.key !== removeKey) return entity;\n          return _objectSpread(_objectSpread({}, entity), {}, {\n            status: STATUS_REMOVED\n          });\n        });\n        _this.setState({\n          keyEntities: nextKeyEntities\n        });\n        return nextKeyEntities.filter(function (_ref) {\n          var status = _ref.status;\n          return status !== STATUS_REMOVED;\n        }).length;\n      };\n      return _this;\n    }\n    _createClass(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = _objectWithoutProperties(_this$props, _excluded);\n        var Component = component || React.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/React.createElement(Component, restProps, keyEntities.map(function (_ref2) {\n          var status = _ref2.status,\n            eventProps = _objectWithoutProperties(_ref2, _excluded2);\n          var visible = status === STATUS_ADD || status === STATUS_KEEP;\n          return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 ? void 0 : _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                var restKeysCount = _this2.removeKey(eventProps.key);\n                if (restKeysCount === 0 && onAllRemoved) {\n                  onAllRemoved();\n                }\n              }\n            }\n          }), children);\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = parseKeys(keys);\n        var mixedKeyEntities = diffKeys(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            }); // Remove if already mark as removed\n\n            if (prevEntity && prevEntity.status === STATUS_REMOVED && entity.status === STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      } // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n    }]);\n    return CSSMotionList;\n  }(React.Component);\n  CSSMotionList.defaultProps = {\n    component: 'div'\n  };\n  return CSSMotionList;\n}\nexport default genCSSMotionList(supportTransition);", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "_excluded", "_excluded2", "React", "OriginCSSMotion", "supportTransition", "STATUS_ADD", "STATUS_KEEP", "STATUS_REMOVE", "STATUS_REMOVED", "diff<PERSON>eys", "parse<PERSON>eys", "MOTION_PROP_NAMES", "genCSSMotionList", "transitionSupport", "CSSMotion", "arguments", "length", "undefined", "CSSMotionList", "_React$Component", "_super", "_this", "_len", "args", "Array", "_key", "call", "apply", "concat", "state", "keyEntities", "<PERSON><PERSON><PERSON>", "nextKeyEntities", "map", "entity", "key", "status", "setState", "filter", "_ref", "value", "render", "_this2", "_this$props", "props", "component", "children", "_onVisibleChanged", "onVisibleChanged", "onAllRemoved", "restProps", "Component", "Fragment", "motionProps", "for<PERSON>ach", "prop", "keys", "createElement", "_ref2", "eventProps", "visible", "changedVisible", "restKeysCount", "getDerivedStateFromProps", "_ref3", "_ref4", "parsedKeyObjects", "mixedKeyEntities", "prevEntity", "find", "_ref5", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/CSSMotionList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n    _excluded2 = [\"status\"];\n\n/* eslint react/prop-types: 0 */\nimport * as React from 'react';\nimport OriginCSSMotion from './CSSMotion';\nimport { supportTransition } from './util/motion';\nimport { STATUS_ADD, STATUS_KEEP, STATUS_REMOVE, STATUS_REMOVED, diffKeys, parseKeys } from './util/diff';\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\n\nexport function genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : OriginCSSMotion;\n\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    _inherits(CSSMotionList, _React$Component);\n\n    var _super = _createSuper(CSSMotionList);\n\n    function CSSMotionList() {\n      var _this;\n\n      _classCallCheck(this, CSSMotionList);\n\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this = _super.call.apply(_super, [this].concat(args));\n      _this.state = {\n        keyEntities: []\n      };\n\n      _this.removeKey = function (removeKey) {\n        var keyEntities = _this.state.keyEntities;\n        var nextKeyEntities = keyEntities.map(function (entity) {\n          if (entity.key !== removeKey) return entity;\n          return _objectSpread(_objectSpread({}, entity), {}, {\n            status: STATUS_REMOVED\n          });\n        });\n\n        _this.setState({\n          keyEntities: nextKeyEntities\n        });\n\n        return nextKeyEntities.filter(function (_ref) {\n          var status = _ref.status;\n          return status !== STATUS_REMOVED;\n        }).length;\n      };\n\n      return _this;\n    }\n\n    _createClass(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n\n        var keyEntities = this.state.keyEntities;\n\n        var _this$props = this.props,\n            component = _this$props.component,\n            children = _this$props.children,\n            _onVisibleChanged = _this$props.onVisibleChanged,\n            onAllRemoved = _this$props.onAllRemoved,\n            restProps = _objectWithoutProperties(_this$props, _excluded);\n\n        var Component = component || React.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/React.createElement(Component, restProps, keyEntities.map(function (_ref2) {\n          var status = _ref2.status,\n              eventProps = _objectWithoutProperties(_ref2, _excluded2);\n\n          var visible = status === STATUS_ADD || status === STATUS_KEEP;\n          return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 ? void 0 : _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n\n              if (!changedVisible) {\n                var restKeysCount = _this2.removeKey(eventProps.key);\n\n                if (restKeysCount === 0 && onAllRemoved) {\n                  onAllRemoved();\n                }\n              }\n            }\n          }), children);\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = parseKeys(keys);\n        var mixedKeyEntities = diffKeys(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            }); // Remove if already mark as removed\n\n            if (prevEntity && prevEntity.status === STATUS_REMOVED && entity.status === STATUS_REMOVE) {\n              return false;\n            }\n\n            return true;\n          })\n        };\n      } // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n\n    }]);\n\n    return CSSMotionList;\n  }(React.Component);\n\n  CSSMotionList.defaultProps = {\n    component: 'div'\n  };\n  return CSSMotionList;\n}\nexport default genCSSMotionList(supportTransition);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,cAAc,CAAC;EACzEC,UAAU,GAAG,CAAC,QAAQ,CAAC;;AAE3B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,aAAa;AACzC,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,UAAU,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,aAAa;AACzG,IAAIC,iBAAiB,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,CAAC;AACzV;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,gBAAgBA,CAACC,iBAAiB,EAAE;EAClD,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGZ,eAAe;EAEnG,IAAIe,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;IAC3DrB,SAAS,CAACoB,aAAa,EAAEC,gBAAgB,CAAC;IAE1C,IAAIC,MAAM,GAAGrB,YAAY,CAACmB,aAAa,CAAC;IAExC,SAASA,aAAaA,CAAA,EAAG;MACvB,IAAIG,KAAK;MAETzB,eAAe,CAAC,IAAI,EAAEsB,aAAa,CAAC;MAEpC,KAAK,IAAII,IAAI,GAAGP,SAAS,CAACC,MAAM,EAAEO,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGV,SAAS,CAACU,IAAI,CAAC;MAC9B;MAEAJ,KAAK,GAAGD,MAAM,CAACM,IAAI,CAACC,KAAK,CAACP,MAAM,EAAE,CAAC,IAAI,CAAC,CAACQ,MAAM,CAACL,IAAI,CAAC,CAAC;MACtDF,KAAK,CAACQ,KAAK,GAAG;QACZC,WAAW,EAAE;MACf,CAAC;MAEDT,KAAK,CAACU,SAAS,GAAG,UAAUA,SAAS,EAAE;QACrC,IAAID,WAAW,GAAGT,KAAK,CAACQ,KAAK,CAACC,WAAW;QACzC,IAAIE,eAAe,GAAGF,WAAW,CAACG,GAAG,CAAC,UAAUC,MAAM,EAAE;UACtD,IAAIA,MAAM,CAACC,GAAG,KAAKJ,SAAS,EAAE,OAAOG,MAAM;UAC3C,OAAOvC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YAClDE,MAAM,EAAE5B;UACV,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFa,KAAK,CAACgB,QAAQ,CAAC;UACbP,WAAW,EAAEE;QACf,CAAC,CAAC;QAEF,OAAOA,eAAe,CAACM,MAAM,CAAC,UAAUC,IAAI,EAAE;UAC5C,IAAIH,MAAM,GAAGG,IAAI,CAACH,MAAM;UACxB,OAAOA,MAAM,KAAK5B,cAAc;QAClC,CAAC,CAAC,CAACQ,MAAM;MACX,CAAC;MAED,OAAOK,KAAK;IACd;IAEAxB,YAAY,CAACqB,aAAa,EAAE,CAAC;MAC3BiB,GAAG,EAAE,QAAQ;MACbK,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;QACvB,IAAIC,MAAM,GAAG,IAAI;QAEjB,IAAIZ,WAAW,GAAG,IAAI,CAACD,KAAK,CAACC,WAAW;QAExC,IAAIa,WAAW,GAAG,IAAI,CAACC,KAAK;UACxBC,SAAS,GAAGF,WAAW,CAACE,SAAS;UACjCC,QAAQ,GAAGH,WAAW,CAACG,QAAQ;UAC/BC,iBAAiB,GAAGJ,WAAW,CAACK,gBAAgB;UAChDC,YAAY,GAAGN,WAAW,CAACM,YAAY;UACvCC,SAAS,GAAGxD,wBAAwB,CAACiD,WAAW,EAAE3C,SAAS,CAAC;QAEhE,IAAImD,SAAS,GAAGN,SAAS,IAAI3C,KAAK,CAACkD,QAAQ;QAC3C,IAAIC,WAAW,GAAG,CAAC,CAAC;QACpB1C,iBAAiB,CAAC2C,OAAO,CAAC,UAAUC,IAAI,EAAE;UACxCF,WAAW,CAACE,IAAI,CAAC,GAAGL,SAAS,CAACK,IAAI,CAAC;UACnC,OAAOL,SAAS,CAACK,IAAI,CAAC;QACxB,CAAC,CAAC;QACF,OAAOL,SAAS,CAACM,IAAI;QACrB,OAAO,aAAatD,KAAK,CAACuD,aAAa,CAACN,SAAS,EAAED,SAAS,EAAEpB,WAAW,CAACG,GAAG,CAAC,UAAUyB,KAAK,EAAE;UAC7F,IAAItB,MAAM,GAAGsB,KAAK,CAACtB,MAAM;YACrBuB,UAAU,GAAGjE,wBAAwB,CAACgE,KAAK,EAAEzD,UAAU,CAAC;UAE5D,IAAI2D,OAAO,GAAGxB,MAAM,KAAK/B,UAAU,IAAI+B,MAAM,KAAK9B,WAAW;UAC7D,OAAO,aAAaJ,KAAK,CAACuD,aAAa,CAAC3C,SAAS,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAE4D,WAAW,EAAE;YAC3ElB,GAAG,EAAEwB,UAAU,CAACxB,GAAG;YACnByB,OAAO,EAAEA,OAAO;YAChBD,UAAU,EAAEA,UAAU;YACtBX,gBAAgB,EAAE,SAASA,gBAAgBA,CAACa,cAAc,EAAE;cAC1Dd,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACc,cAAc,EAAE;gBACtG1B,GAAG,EAAEwB,UAAU,CAACxB;cAClB,CAAC,CAAC;cAEF,IAAI,CAAC0B,cAAc,EAAE;gBACnB,IAAIC,aAAa,GAAGpB,MAAM,CAACX,SAAS,CAAC4B,UAAU,CAACxB,GAAG,CAAC;gBAEpD,IAAI2B,aAAa,KAAK,CAAC,IAAIb,YAAY,EAAE;kBACvCA,YAAY,CAAC,CAAC;gBAChB;cACF;YACF;UACF,CAAC,CAAC,EAAEH,QAAQ,CAAC;QACf,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,EAAE,CAAC;MACHX,GAAG,EAAE,0BAA0B;MAC/BK,KAAK,EAAE,SAASuB,wBAAwBA,CAACC,KAAK,EAAEC,KAAK,EAAE;QACrD,IAAIT,IAAI,GAAGQ,KAAK,CAACR,IAAI;QACrB,IAAI1B,WAAW,GAAGmC,KAAK,CAACnC,WAAW;QACnC,IAAIoC,gBAAgB,GAAGxD,SAAS,CAAC8C,IAAI,CAAC;QACtC,IAAIW,gBAAgB,GAAG1D,QAAQ,CAACqB,WAAW,EAAEoC,gBAAgB,CAAC;QAC9D,OAAO;UACLpC,WAAW,EAAEqC,gBAAgB,CAAC7B,MAAM,CAAC,UAAUJ,MAAM,EAAE;YACrD,IAAIkC,UAAU,GAAGtC,WAAW,CAACuC,IAAI,CAAC,UAAUC,KAAK,EAAE;cACjD,IAAInC,GAAG,GAAGmC,KAAK,CAACnC,GAAG;cACnB,OAAOD,MAAM,CAACC,GAAG,KAAKA,GAAG;YAC3B,CAAC,CAAC,CAAC,CAAC;;YAEJ,IAAIiC,UAAU,IAAIA,UAAU,CAAChC,MAAM,KAAK5B,cAAc,IAAI0B,MAAM,CAACE,MAAM,KAAK7B,aAAa,EAAE;cACzF,OAAO,KAAK;YACd;YAEA,OAAO,IAAI;UACb,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IAEJ,CAAC,CAAC,CAAC;IAEH,OAAOW,aAAa;EACtB,CAAC,CAAChB,KAAK,CAACiD,SAAS,CAAC;EAElBjC,aAAa,CAACqD,YAAY,GAAG;IAC3B1B,SAAS,EAAE;EACb,CAAC;EACD,OAAO3B,aAAa;AACtB;AACA,eAAeN,gBAAgB,CAACR,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}