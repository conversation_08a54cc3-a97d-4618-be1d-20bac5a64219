{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useState, useEffect } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Menu, { MenuItem } from 'rc-menu';\nimport Dropdown from 'rc-dropdown';\nimport AddButton from './AddButton';\nfunction OperationNode(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    tabs = _ref.tabs,\n    locale = _ref.locale,\n    mobile = _ref.mobile,\n    _ref$moreIcon = _ref.moreIcon,\n    moreIcon = _ref$moreIcon === void 0 ? 'More' : _ref$moreIcon,\n    moreTransitionName = _ref.moreTransitionName,\n    style = _ref.style,\n    className = _ref.className,\n    editable = _ref.editable,\n    tabBarGutter = _ref.tabBarGutter,\n    rtl = _ref.rtl,\n    removeAriaLabel = _ref.removeAriaLabel,\n    onTabClick = _ref.onTabClick;\n\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref2) {\n      var key = _ref2.key,\n        domEvent = _ref2.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var removable = editable && tab.closable !== false && !tab.disabled;\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: tab.key,\n      id: \"\".concat(popupId, \"-\").concat(tab.key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(tab.key),\n      disabled: tab.disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, tab.tab), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, tab.key);\n      }\n    }, tab.closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) onTabClick(selectedKey, e);\n        break;\n    }\n  } // ========================= Effect =========================\n\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]); // ========================= Render =========================\n\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, {\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    trigger: ['hover'],\n    visible: open,\n    transitionName: moreTransitionName,\n    onVisibleChange: setOpen,\n    overlayClassName: overlayClassName,\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n}\nexport default /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(OperationNode), function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "React", "classNames", "useState", "useEffect", "KeyCode", "<PERSON><PERSON>", "MenuItem", "Dropdown", "AddButton", "OperationNode", "_ref", "ref", "prefixCls", "id", "tabs", "locale", "mobile", "_ref$moreIcon", "moreIcon", "moreTransitionName", "style", "className", "editable", "tabBarGutter", "rtl", "removeAriaLabel", "onTabClick", "_useState", "_useState2", "open", "<PERSON><PERSON><PERSON>", "_useState3", "_useState4", "<PERSON><PERSON><PERSON>", "setSelectedKey", "popupId", "concat", "dropdownPrefix", "selectedItemId", "dropdownAriaLabel", "onRemoveTab", "event", "key", "preventDefault", "stopPropagation", "onEdit", "menu", "createElement", "onClick", "_ref2", "domEvent", "tabIndex", "role", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "map", "tab", "removable", "closable", "disabled", "type", "e", "closeIcon", "removeIcon", "selectOffset", "offset", "enabledTabs", "filter", "selectedIndex", "findIndex", "len", "length", "i", "onKeyDown", "which", "DOWN", "SPACE", "ENTER", "includes", "UP", "ESC", "ele", "document", "getElementById", "scrollIntoView", "moreStyle", "visibility", "order", "overlayClassName", "moreNode", "overlay", "trigger", "visible", "transitionName", "onVisibleChange", "mouseEnterDelay", "mouseLeaveDelay", "memo", "forwardRef", "_", "next", "tabMoving"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/TabNavList/OperationNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useState, useEffect } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Menu, { MenuItem } from 'rc-menu';\nimport Dropdown from 'rc-dropdown';\nimport AddButton from './AddButton';\n\nfunction OperationNode(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n      id = _ref.id,\n      tabs = _ref.tabs,\n      locale = _ref.locale,\n      mobile = _ref.mobile,\n      _ref$moreIcon = _ref.moreIcon,\n      moreIcon = _ref$moreIcon === void 0 ? 'More' : _ref$moreIcon,\n      moreTransitionName = _ref.moreTransitionName,\n      style = _ref.style,\n      className = _ref.className,\n      editable = _ref.editable,\n      tabBarGutter = _ref.tabBarGutter,\n      rtl = _ref.rtl,\n      removeAriaLabel = _ref.removeAriaLabel,\n      onTabClick = _ref.onTabClick;\n\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      open = _useState2[0],\n      setOpen = _useState2[1];\n\n  var _useState3 = useState(null),\n      _useState4 = _slicedToArray(_useState3, 2),\n      selectedKey = _useState4[0],\n      setSelectedKey = _useState4[1];\n\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref2) {\n      var key = _ref2.key,\n          domEvent = _ref2.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var removable = editable && tab.closable !== false && !tab.disabled;\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: tab.key,\n      id: \"\".concat(popupId, \"-\").concat(tab.key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(tab.key),\n      disabled: tab.disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, tab.tab), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, tab.key);\n      }\n    }, tab.closeIcon || editable.removeIcon || '×'));\n  }));\n\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n\n  function onKeyDown(e) {\n    var which = e.which;\n\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n\n      return;\n    }\n\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) onTabClick(selectedKey, e);\n        break;\n    }\n  } // ========================= Effect =========================\n\n\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]); // ========================= Render =========================\n\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, {\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    trigger: ['hover'],\n    visible: open,\n    transitionName: moreTransitionName,\n    onVisibleChange: setOpen,\n    overlayClassName: overlayClassName,\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n}\n\nexport default /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(OperationNode), function (_, next) {\n  return (// https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,IAAI,IAAIC,QAAQ,QAAQ,SAAS;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,SAAS,MAAM,aAAa;AAEnC,SAASC,aAAaA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAChC,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,EAAE,GAAGH,IAAI,CAACG,EAAE;IACZC,IAAI,GAAGJ,IAAI,CAACI,IAAI;IAChBC,MAAM,GAAGL,IAAI,CAACK,MAAM;IACpBC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,aAAa,GAAGP,IAAI,CAACQ,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,aAAa;IAC5DE,kBAAkB,GAAGT,IAAI,CAACS,kBAAkB;IAC5CC,KAAK,GAAGV,IAAI,CAACU,KAAK;IAClBC,SAAS,GAAGX,IAAI,CAACW,SAAS;IAC1BC,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;IACxBC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCC,GAAG,GAAGd,IAAI,CAACc,GAAG;IACdC,eAAe,GAAGf,IAAI,CAACe,eAAe;IACtCC,UAAU,GAAGhB,IAAI,CAACgB,UAAU;;EAEhC;EACA,IAAIC,SAAS,GAAGzB,QAAQ,CAAC,KAAK,CAAC;IAC3B0B,UAAU,GAAG7B,cAAc,CAAC4B,SAAS,EAAE,CAAC,CAAC;IACzCE,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;IACpBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE3B,IAAIG,UAAU,GAAG7B,QAAQ,CAAC,IAAI,CAAC;IAC3B8B,UAAU,GAAGjC,cAAc,CAACgC,UAAU,EAAE,CAAC,CAAC;IAC1CE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAElC,IAAIG,OAAO,GAAG,EAAE,CAACC,MAAM,CAACvB,EAAE,EAAE,aAAa,CAAC;EAC1C,IAAIwB,cAAc,GAAG,EAAE,CAACD,MAAM,CAACxB,SAAS,EAAE,WAAW,CAAC;EACtD,IAAI0B,cAAc,GAAGL,WAAW,KAAK,IAAI,GAAG,EAAE,CAACG,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACH,WAAW,CAAC,GAAG,IAAI;EAC9F,IAAIM,iBAAiB,GAAGxB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACwB,iBAAiB;EAEhG,SAASC,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/BD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IACvBtB,QAAQ,CAACuB,MAAM,CAAC,QAAQ,EAAE;MACxBH,GAAG,EAAEA,GAAG;MACRD,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EAEA,IAAIK,IAAI,GAAG,aAAa9C,KAAK,CAAC+C,aAAa,CAAC1C,IAAI,EAAE;IAChD2C,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;MAC/B,IAAIP,GAAG,GAAGO,KAAK,CAACP,GAAG;QACfQ,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7BxB,UAAU,CAACgB,GAAG,EAAEQ,QAAQ,CAAC;MACzBpB,OAAO,CAAC,KAAK,CAAC;IAChB,CAAC;IACDjB,EAAE,EAAEsB,OAAO;IACXgB,QAAQ,EAAE,CAAC,CAAC;IACZC,IAAI,EAAE,SAAS;IACf,uBAAuB,EAAEd,cAAc;IACvCe,YAAY,EAAE,CAACpB,WAAW,CAAC;IAC3B,YAAY,EAAEM,iBAAiB,KAAKe,SAAS,GAAGf,iBAAiB,GAAG;EACtE,CAAC,EAAEzB,IAAI,CAACyC,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzB,IAAIC,SAAS,GAAGnC,QAAQ,IAAIkC,GAAG,CAACE,QAAQ,KAAK,KAAK,IAAI,CAACF,GAAG,CAACG,QAAQ;IACnE,OAAO,aAAa3D,KAAK,CAAC+C,aAAa,CAACzC,QAAQ,EAAE;MAChDoC,GAAG,EAAEc,GAAG,CAACd,GAAG;MACZ7B,EAAE,EAAE,EAAE,CAACuB,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACoB,GAAG,CAACd,GAAG,CAAC;MAC3CU,IAAI,EAAE,QAAQ;MACd,eAAe,EAAEvC,EAAE,IAAI,EAAE,CAACuB,MAAM,CAACvB,EAAE,EAAE,SAAS,CAAC,CAACuB,MAAM,CAACoB,GAAG,CAACd,GAAG,CAAC;MAC/DiB,QAAQ,EAAEH,GAAG,CAACG;IAChB,CAAC,EAAE,aAAa3D,KAAK,CAAC+C,aAAa,CAAC,MAAM,EAAE,IAAI,EAAES,GAAG,CAACA,GAAG,CAAC,EAAEC,SAAS,IAAI,aAAazD,KAAK,CAAC+C,aAAa,CAAC,QAAQ,EAAE;MAClHa,IAAI,EAAE,QAAQ;MACd,YAAY,EAAEnC,eAAe,IAAI,QAAQ;MACzC0B,QAAQ,EAAE,CAAC;MACX9B,SAAS,EAAE,EAAE,CAACe,MAAM,CAACC,cAAc,EAAE,mBAAmB,CAAC;MACzDW,OAAO,EAAE,SAASA,OAAOA,CAACa,CAAC,EAAE;QAC3BA,CAAC,CAACjB,eAAe,CAAC,CAAC;QACnBJ,WAAW,CAACqB,CAAC,EAAEL,GAAG,CAACd,GAAG,CAAC;MACzB;IACF,CAAC,EAAEc,GAAG,CAACM,SAAS,IAAIxC,QAAQ,CAACyC,UAAU,IAAI,GAAG,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC;EAEH,SAASC,YAAYA,CAACC,MAAM,EAAE;IAC5B,IAAIC,WAAW,GAAGpD,IAAI,CAACqD,MAAM,CAAC,UAAUX,GAAG,EAAE;MAC3C,OAAO,CAACA,GAAG,CAACG,QAAQ;IACtB,CAAC,CAAC;IACF,IAAIS,aAAa,GAAGF,WAAW,CAACG,SAAS,CAAC,UAAUb,GAAG,EAAE;MACvD,OAAOA,GAAG,CAACd,GAAG,KAAKT,WAAW;IAChC,CAAC,CAAC,IAAI,CAAC;IACP,IAAIqC,GAAG,GAAGJ,WAAW,CAACK,MAAM;IAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/BJ,aAAa,GAAG,CAACA,aAAa,GAAGH,MAAM,GAAGK,GAAG,IAAIA,GAAG;MACpD,IAAId,GAAG,GAAGU,WAAW,CAACE,aAAa,CAAC;MAEpC,IAAI,CAACZ,GAAG,CAACG,QAAQ,EAAE;QACjBzB,cAAc,CAACsB,GAAG,CAACd,GAAG,CAAC;QACvB;MACF;IACF;EACF;EAEA,SAAS+B,SAASA,CAACZ,CAAC,EAAE;IACpB,IAAIa,KAAK,GAAGb,CAAC,CAACa,KAAK;IAEnB,IAAI,CAAC7C,IAAI,EAAE;MACT,IAAI,CAACzB,OAAO,CAACuE,IAAI,EAAEvE,OAAO,CAACwE,KAAK,EAAExE,OAAO,CAACyE,KAAK,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAChE5C,OAAO,CAAC,IAAI,CAAC;QACb+B,CAAC,CAAClB,cAAc,CAAC,CAAC;MACpB;MAEA;IACF;IAEA,QAAQ+B,KAAK;MACX,KAAKtE,OAAO,CAAC2E,EAAE;QACbf,YAAY,CAAC,CAAC,CAAC,CAAC;QAChBH,CAAC,CAAClB,cAAc,CAAC,CAAC;QAClB;MAEF,KAAKvC,OAAO,CAACuE,IAAI;QACfX,YAAY,CAAC,CAAC,CAAC;QACfH,CAAC,CAAClB,cAAc,CAAC,CAAC;QAClB;MAEF,KAAKvC,OAAO,CAAC4E,GAAG;QACdlD,OAAO,CAAC,KAAK,CAAC;QACd;MAEF,KAAK1B,OAAO,CAACwE,KAAK;MAClB,KAAKxE,OAAO,CAACyE,KAAK;QAChB,IAAI5C,WAAW,KAAK,IAAI,EAAEP,UAAU,CAACO,WAAW,EAAE4B,CAAC,CAAC;QACpD;IACJ;EACF,CAAC,CAAC;;EAGF1D,SAAS,CAAC,YAAY;IACpB;IACA,IAAI8E,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC7C,cAAc,CAAC;IAEjD,IAAI2C,GAAG,IAAIA,GAAG,CAACG,cAAc,EAAE;MAC7BH,GAAG,CAACG,cAAc,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAACnD,WAAW,CAAC,CAAC;EACjB9B,SAAS,CAAC,YAAY;IACpB,IAAI,CAAC0B,IAAI,EAAE;MACTK,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,IAAIwD,SAAS,GAAGvF,eAAe,CAAC,CAAC,CAAC,EAAE0B,GAAG,GAAG,aAAa,GAAG,YAAY,EAAED,YAAY,CAAC;EAErF,IAAI,CAACT,IAAI,CAACyD,MAAM,EAAE;IAChBc,SAAS,CAACC,UAAU,GAAG,QAAQ;IAC/BD,SAAS,CAACE,KAAK,GAAG,CAAC;EACrB;EAEA,IAAIC,gBAAgB,GAAGvF,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACsC,MAAM,CAACC,cAAc,EAAE,MAAM,CAAC,EAAEb,GAAG,CAAC,CAAC;EAC9F,IAAIiE,QAAQ,GAAGzE,MAAM,GAAG,IAAI,GAAG,aAAahB,KAAK,CAAC+C,aAAa,CAACxC,QAAQ,EAAE;IACxEK,SAAS,EAAEyB,cAAc;IACzBqD,OAAO,EAAE5C,IAAI;IACb6C,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,OAAO,EAAE/D,IAAI;IACbgE,cAAc,EAAE1E,kBAAkB;IAClC2E,eAAe,EAAEhE,OAAO;IACxB0D,gBAAgB,EAAEA,gBAAgB;IAClCO,eAAe,EAAE,GAAG;IACpBC,eAAe,EAAE;EACnB,CAAC,EAAE,aAAahG,KAAK,CAAC+C,aAAa,CAAC,QAAQ,EAAE;IAC5Ca,IAAI,EAAE,QAAQ;IACdvC,SAAS,EAAE,EAAE,CAACe,MAAM,CAACxB,SAAS,EAAE,WAAW,CAAC;IAC5CQ,KAAK,EAAEiE,SAAS;IAChBlC,QAAQ,EAAE,CAAC,CAAC;IACZ,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,SAAS;IAC1B,eAAe,EAAEhB,OAAO;IACxBtB,EAAE,EAAE,EAAE,CAACuB,MAAM,CAACvB,EAAE,EAAE,OAAO,CAAC;IAC1B,eAAe,EAAEgB,IAAI;IACrB4C,SAAS,EAAEA;EACb,CAAC,EAAEvD,QAAQ,CAAC,CAAC;EACb,OAAO,aAAalB,KAAK,CAAC+C,aAAa,CAAC,KAAK,EAAE;IAC7C1B,SAAS,EAAEpB,UAAU,CAAC,EAAE,CAACmC,MAAM,CAACxB,SAAS,EAAE,iBAAiB,CAAC,EAAES,SAAS,CAAC;IACzED,KAAK,EAAEA,KAAK;IACZT,GAAG,EAAEA;EACP,CAAC,EAAE8E,QAAQ,EAAE,aAAazF,KAAK,CAAC+C,aAAa,CAACvC,SAAS,EAAE;IACvDI,SAAS,EAAEA,SAAS;IACpBG,MAAM,EAAEA,MAAM;IACdO,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AAEA,eAAe,aAAatB,KAAK,CAACiG,IAAI,CAAE,aAAajG,KAAK,CAACkG,UAAU,CAACzF,aAAa,CAAC,EAAE,UAAU0F,CAAC,EAAEC,IAAI,EAAE;EACvG;IAAQ;IACN;IACAA,IAAI,CAACC;EAAS;AAElB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}