{"core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> von Tastenkombinationen können Nutzer schnell den Fokus auf einen Bereich der Seite verschieben. Damit die Navigation richtig funktioniert, darf jede Tastenkombination nur einmal vergeben sein. [Weitere Informationen zu Tastenkombinationen.](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-Werte sind nicht eindeutig"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-Werte sind eindeutig"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON>-`role` unterstützt eine bestimmte Untergruppe von `aria-*`-Attributen. Wenn sie jedoch falsch zugeordnet sind, werden die `aria-*`-Attribute ungültig. [Informationen zum Zuordnen von ARIA-Attributen zu ihren Rollen.](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-Attribute stimmen nicht mit ihren Rollen überein"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-Attribute entsprechen ihren Rollen"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Wenn ein Element keinen barrierefreien Namen hat, wird es von Screenreadern mit einer allgemeinen Bezeichnung angesagt. Dadurch ist es für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Informationen zum barrierefreieren Gestalten von Befehlselementen.](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`-, `link`- und `menuitem`-Elemente haben keine zugänglichen Namen."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`-, `link`- und `menuitem`-Elemente haben zugängliche Namen"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Hilfstechnologien wie Screenreader funktionieren nicht richtig, wenn für den `<body>` des Dokuments `aria-hidden=\"true\"` festgelegt ist. [<PERSON><PERSON> zu den Auswirkungen von `aria-hidden` auf den Textbereich des Dokuments.](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` ist in dem Dokument`<body>` vorhanden"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` ist in dem Dokument `<body>` nicht vorhanden"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Fokussierbare Nachfolgerelemente in einem `[aria-hidden=\"true\"]`-<PERSON><PERSON> führen dazu, das<PERSON> <PERSON><PERSON><PERSON> von Hilfstechnologien wie Screenreadern solche interaktiven Elemente nicht verwenden können. [Informationen zu den Auswirkungen von `aria-hidden` auf fokussierbare Elemente.](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]`-Elemente enthalten fokussierbare Unterelemente"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]`-Elemente enthalten keine fokussierbaren Unterelemente"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Wenn ein Eingabefeld keinen barrierefreien Namen hat, wird es von Screenreadern mit einer allgemeinen Bezeichnung angesagt. Dadurch ist es für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Weitere Informationen zu Labels für Eingabefelder.](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA-Eingabefelder haben keine zugänglichen Namen"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA-Eingabefelder haben zugängliche Namen"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Wenn ein Messtool-Element keinen barrierefreien Namen hat, wird es von Screenreadern mit einer allgemeinen Bezeichnung angesagt. Dadurch ist es für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Weitere Informationen zum Benennen von `meter`-Elementen](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter`-<PERSON>emente haben keine zugänglichen Namen."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter`-Elemente haben zugängliche Namen"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Wenn ein `progressbar`-Element keinen barrierefreien Namen hat, wird es von Screenreadern mit einer allgemeinen Bezeichnung angesagt. Dadurch ist es für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Informationen zum Kennzeichnen von `progressbar`-Elementen.](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar`-Elemente haben keine zugänglichen Namen."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar`-Elemente haben zugängliche Namen"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON>r e<PERSON><PERSON>-Rollen sind Attribute erforderlich, die Screenreadern den Zustand des Elements beschreiben. [Weitere Informationen zu Rollen und erforderlichen Attributen.](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-Elemente weisen nicht alle erforderlichen `[aria-*]`-Attribute auf"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-Elemente verfügen über alle erforderlichen `[aria-*]`-Attribute"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Einige übergeordnete ARIA-Rollen müssen bestimmte untergeordnete Rollen enthalten, damit sie die beabsichtigten Hilfsfunktionen erfüllen können. [Weitere Informationen zu Rollen und erforderlichen untergeordneten Elementen.](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Den Elementen mit einer ARIA-`[role]`, deren untergeordnete Elemente eine bestimmte `[role]` enthalten müssen, fehlen einige oder alle dieser erforderlichen untergeordneten Elemente."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Die Elemente mit einer ARIA-`[role]`, deren untergeordnete Elemente eine bestimmte `[role]` enthalten müssen, haben alle erforderlichen untergeordneten Elemente."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Einige untergeordnete ARIA-<PERSON><PERSON> müssen in bestimmten übergeordneten Rollen enthalten sein, damit sie die beabsichtigten Hilfsfunktionen erfüllen können. [Weitere Informationen zu ARIA-Rollen und erforderlichen übergeordneten Elementen.](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-Elemente sind nicht ihren jeweils erforderlichen übergeordneten Elementen untergeordnet"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-Elemente sind ihren jeweils erforderlichen übergeordneten Elementen untergeordnet"}, "core/audits/accessibility/aria-roles.js | description": {"message": "<PERSON><PERSON><PERSON>-Roll<PERSON> müssen gültige Werte angegeben sein, damit sie die beabsichtigten Hilfsfunktionen erfüllen können. [Weitere Informationen zu gültigen ARIA-Rollen.](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-<PERSON><PERSON> sind ungültig"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-<PERSON><PERSON> sind gü<PERSON>ig"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Wenn eine Ein-/Aus-Schaltfläche keinen barrierefreien Name<PERSON> hat, wird sie von Screenreadern mit einer allgemeinen Bezeichnung angesagt. <PERSON><PERSON>ch ist sie für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Weitere Informationen zu Ein-/Aus-Schaltflächen.](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA-Ein-/Aus-Schaltflächen haben keine zugänglichen Namen"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA-Ein-/Aus-Schaltflächen haben zugängliche Namen"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Wenn ein Kurzinfo-Element keinen barrierefreien Namen hat, wird es von Screenreadern mit einer allgemeinen Bezeichnung angesagt. Dadurch ist es für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Weitere Informationen zum Benennen von `tooltip`-Elementen](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip`-Elemente haben keine zugänglichen Namen."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip`-Elemente haben zugängliche Namen"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Wenn ein `treeitem`-Element keinen barrierefreien Namen hat, wird es von Screenreadern mit einer allgemeinen Bezeichnung angesagt. Dadurch ist es für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Weitere Informationen zum Kennzeichnen von `treeitem`-Elementen.](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem`-Elemente haben keine zugänglichen Namen."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem`-Elemente haben zugängliche Namen"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Hilfstechnologien wie Screenreader können ARIA-Attribute mit ungültigen Werten nicht interpretieren. [Weitere Informationen zu gültigen Werten für ARIA-Attribute.](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-Attribute weisen keine gültigen Werte auf"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-Attribute weisen gültige Werte auf"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Hilfstechnologien wie Screenreader können ARIA-Attribute mit ungültigen Namen nicht interpretieren. [Weitere Informationen zu gültigen ARIA-Attributen.](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-Attribute sind ungültig oder falsch geschrieben"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-Attribute sind gültig und richtig geschrieben"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Fehlerhafte Elemente"}, "core/audits/accessibility/button-name.js | description": {"message": "Wenn eine Schaltfläche keinen barrierefreien Namen hat, wird sie von Screenreadern nur als „Schaltfläche“ angesagt. Dadurch ist sie für Nutzer, die auf Screenreader angewiesen sind, unbrauchbar. [Informationen zum barrierefreien Gestalten von Schaltflächen.](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Schaltflächen haben keinen für Screenreader zugänglichen Namen"}, "core/audits/accessibility/button-name.js | title": {"message": "Die Namen der Schaltflächen sind für Screenreader zugänglich"}, "core/audits/accessibility/bypass.js | description": {"message": "<PERSON><PERSON>er Inhalte umgehen können, die sich wiederholen, sorgt das für eine effizientere Navigation. [Weitere Informationen zum Umgehen von Blockierungen.](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Die Seite enthält keine Überschrift, keinen Link zum Überspringen und keinen Landmark-Bereich"}, "core/audits/accessibility/bypass.js | title": {"message": "Die Seite enthält eine Überschrift, einen Link zum Überspringen oder einen Landmark-Bereich"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Text mit geringem Kontrast ist für viele Nutzer schlecht oder gar nicht lesbar. [Informationen zu einem ausreichenden Farbkontrast.](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Das Kontrastverhältnis von Hintergrund- und Vordergrundfarben ist nicht ausreichend."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Das Kontrastverhältnis von Hintergrund- und Vordergrundfarben ist ausreichend"}, "core/audits/accessibility/definition-list.js | description": {"message": "Wenn Definitionslisten nicht korrekt mit <PERSON><PERSON> versehen sind, kann es zu verwirrenden oder ungenauen Screenreader-Ausgaben kommen. [Informationen zum Strukturieren von Definitionslisten.](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-Elemente enthalten nicht ausschließlich Gruppen aus `<dt>`- und `<dd>`-Elementen sowie `<script>`-, `<template>`- oder `<div>`-<PERSON><PERSON><PERSON>, die richtig angeordnet sind."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`-Elemente enthalten ausschließlich Gruppen aus `<dt>`- und `<dd>`-Element<PERSON> sowie `<script>`-, `<template>`- oder `<div>`-<PERSON><PERSON><PERSON>, die richtig angeordnet sind."}, "core/audits/accessibility/dlitem.js | description": {"message": "Definitionslistenelemente (`<dt>` und `<dd>`) müssen in ein übergeordnetes `<dl>`-<PERSON><PERSON> eingefasst sein, damit sie von Screenreadern richtig angesagt werden können. [Informationen zum Strukturieren von Definitionslisten.](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definitionslistenelemente sind nicht in `<dl>`-Elemente eingefasst"}, "core/audits/accessibility/dlitem.js | title": {"message": "Definitionslistenelemente sind in `<dl>`-Elemente eingefasst"}, "core/audits/accessibility/document-title.js | description": {"message": "Der Titel gibt Screenreader-Nutzern einen Überblick über die Seite. <PERSON><PERSON><PERSON> von Suchmaschinen verlassen sich stark auf ihn, um zu entscheiden, ob eine Seite für ihre Suche relevant ist. [Weitere Informationen zu Dokumenttiteln.](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument hat kein `<title>`-Element"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument enthält ein `<title>`-Element"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Für alle fokussierbaren Elemente ist eine eindeutige `id` <PERSON><PERSON><PERSON><PERSON>, damit sie von Hilfstechnologien erkannt werden können. [Informationen zum Korrigieren doppelter `id`s.](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]`-Attribute zu aktiven, fokussierbaren Elementen sind nicht eindeutig"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]`-Attri<PERSON>e zu aktiven, fokussierbaren Elementen sind eindeutig"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Der Wert einer ARIA-ID muss eindeutig sein, damit andere Instanzen nicht von Hilfstechnologien übersehen werden. [Informationen zum Korrigieren doppelter ARIA-IDs.](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA-IDs sind nicht eindeutig"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA-IDs sind eindeutig"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Formularfelder mit mehreren Labels werden von Hilfstechnologien wie Screenreadern unter Umständen missverständlich angesagt, da sie entweder das erste, das letzte oder alle Labels verwenden. [Informationen zur Verwendung von Formularlabels.](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Formularfelder haben mehrere Labels"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "<PERSON><PERSON> hat mehrere Labels"}, "core/audits/accessibility/frame-title.js | description": {"message": "Screenreader-<PERSON><PERSON><PERSON> sind auf Frametitel angewi<PERSON>n, die die Inhalte von Frames beschreiben. [Weitere Informationen zu Frametiteln.](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- oder `<iframe>`-<PERSON>emente haben keinen Titel"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- oder `<iframe>`-Elemente verfügen über einen Titel"}, "core/audits/accessibility/heading-order.js | description": {"message": "<PERSON><PERSON>g geordnete Überschriften, die keine Ebenen überspringen, geben der Seite eine semantische Struktur. <PERSON><PERSON><PERSON> von Hilfstechnologien können sich so leichter auf der Seite zurechtfinden und die Inhalte besser verstehen. [Weitere Informationen zur Reihenfolge von Überschriften.](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Überschriftenelemente sind nicht in einer fortlaufenden absteigenden Reihenfolge angeordnet"}, "core/audits/accessibility/heading-order.js | title": {"message": "Überschriftenelemente werden in einer fortlaufenden absteigenden Reihenfolge angezeigt"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Wenn für eine Seite kein `lang`-Attribut angegeben ist, nehmen Screenreader an, dass sie in der Standardsprache vorliegt, die der Nutzer beim Einrichten des Screenreaders ausgewählt hat. Ist das nicht der Fall, gibt der Screenreader den Inhalt der Seite möglicherweise falsch aus. [Weitere Informationen zum `lang`-Attribut.](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-<PERSON><PERSON> enthält kein `[lang]`-Attribut"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-<PERSON>ement hat ein `[lang]`-Attribut"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON>n ein gültiger [BCP-47-Sprachcode](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ange<PERSON>ben wird, kann der Text von einem Screenreader korrekt wiedergegeben werden. [Informationen zur Verwendung des Attributs `lang`.](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-<PERSON><PERSON> weist keinen gültigen Wert für sein `[lang]`-Attribut auf."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Das `<html>`-Element hat einen gültigen Wert für sein `[lang]`-Attribut"}, "core/audits/accessibility/image-alt.js | description": {"message": "Für informative Elemente sollte ein kurzer, beschreibenden alternativer Text verwendet werden. Dekorative Elemente können mit einem leeren ALT-Attribut ignoriert werden. [Weitere Informationen zum Attribut `alt`.](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "B<PERSON><PERSON>e haben keine `[alt]`-Attribute"}, "core/audits/accessibility/image-alt.js | title": {"message": "Bildelemente verfügen über `[alt]`-Attribute"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Wenn ein Bild als `<input>`-Schaltfläche verwendet wird, kann alternativer Text Screenreader-Nutzern helfen, den Zweck der Schaltfläche besser zu verstehen. [Informationen zum Alt-Text für Eingabebilder.](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-Elemente haben keinen `[alt]`-Text"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-Elemente haben `[alt]`-Text"}, "core/audits/accessibility/label.js | description": {"message": "Durch Labels wird gewährleistet, dass Steuerelemente für Formulare von Hilfstechnologien wie Screenreadern richtig angesagt werden. [Weitere Informationen zu Labels für Formularelemente.](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Die Formularelemente sind nicht mit Labels verknüpft"}, "core/audits/accessibility/label.js | title": {"message": "Formularelemente sind mit Labels verknüpft"}, "core/audits/accessibility/link-name.js | description": {"message": "Linktext, der erkennbar, einzigartig und fokussierbar ist, erleichtert Screenreader-Nutzern die Verwendung. Dies gilt auch für alternativen Text für Bilder, die als Links verwendet werden. [Informationen zu barrierefreien Links.](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Links haben keinen leicht erkennbaren Namen"}, "core/audits/accessibility/link-name.js | title": {"message": "Links haben einen leicht erkennbaren Namen"}, "core/audits/accessibility/list.js | description": {"message": "Screenreader sagen Listen auf bestimmte Art und Weise an. Wenn die Liste richtig strukturiert ist, kann der Screenreader sie besser ausgeben. [Weitere Informationen zum Strukturieren von Listen.](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Listen enthalten nicht nur `<li>`-Elemente und Elemente zur Skriptunterstützung (`<script>` sowie `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Listen enthalten nur `<li>`-Elemente und Elemente zur Skriptunterstützung (`<script>` sowie `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Listenelemente (`<li>`) müssen sich in einem übergeordneten `<ul>`-, `<ol>`- oder `<menu>`-<PERSON><PERSON> befinden, damit sie von Screenreadern richtig angesagt werden können. [Weitere Informationen zum Strukturieren von Listen.](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Listenelemente (`<li>`) befinden sich nicht in übergeordneten `<ul>`-, `<ol>`- oder `<menu>`-Elementen."}, "core/audits/accessibility/listitem.js | title": {"message": "Listenelemente (`<li>`) befinden sich in übergeordneten `<ul>`-, `<ol>`- oder `<menu>`-Elementen"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "<PERSON>utzer rechnen nicht damit, dass eine Seite automatisch aktualisiert wird. Außerdem wird dadurch der Fokus wieder auf den Seitenanfang verschoben. Das kann für den Nutzer frustrierend oder irritierend sein. [Weitere Informationen zum Meta-Tag „Refresh“.](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Im Dokument wird `<meta http-equiv=\"refresh\">` verwendet"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dieses Dokument verwendet `<meta http-equiv=\"refresh\">` nicht"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Wenn du die Zoomfunktion deaktivierst, k<PERSON><PERSON><PERSON> mit eingeschränktem Sehvermögen, die auf die Bildschirmvergrößerung angewiesen sind, den Inhalt einer Webseite nicht richtig sehen. [Weitere Informationen zum Darstellungsbereich-Meta-Tag.](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` wird im `<meta name=\"viewport\">`-Element verwendet oder das `[maximum-scale]`-Attribut ist kleiner als 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` wird nicht im `<meta name=\"viewport\">`-Element verwendet und das `[maximum-scale]`-Attribut ist nicht kleiner als 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Screenreader können lediglich Textinhalte interpretieren. Wenn du <PERSON><object>`-Elementen alternativen Text hinzufü<PERSON>t, können Screenreader-<PERSON><PERSON><PERSON> besser verstehen, was diese Elemente darstellen. [Weitere Informationen zum Alt-Text für `object`-Elemente.](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-Elemente haben keinen alternativen Text"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-Elemente haben alternativen Text"}, "core/audits/accessibility/tabindex.js | description": {"message": "Ein Wert größer als 0 impliziert eine explizite Navigationsanordnung. Das ist zwar technisch möglich, aber <PERSON>utzer, die auf Hilfstechnologien angewiesen sind, häufig frustrierend. [Weitere Informationen zum Attribut `tabindex`.](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Der `[tabindex]`-<PERSON>rt einiger Elemente ist größer als 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Kein Element hat einen `[tabindex]`-Wert größer als 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Screenreader bieten Funktionen, die die Navigation in Tabellen vereinfachen. <PERSON><PERSON> du da<PERSON>ü<PERSON> sorg<PERSON>, dass `<td><PERSON><PERSON><PERSON><PERSON><PERSON>, die das Attribut `[headers]` ver<PERSON><PERSON>, nur auf andere Zellen in derselben Tabelle verweisen, kann dies für Screenreader-Nutzer hilfreich sein. [Weitere Informationen zum Attribut `headers`.](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> in einem \"`<table>`\"-<PERSON><PERSON>, die das Attribut \"`[headers]`\" enthalten, verweisen auf ein \"`id`\"-El<PERSON>, das sich nicht in derselben Tabelle befindet."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON><PERSON> in einem \"`<table>`\"-<PERSON><PERSON>, die das Attribut \"`[headers]`\" enthalten, verweisen auf Zellen in derselben Tabelle."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Screenreader bieten Funktionen, die die Navigation in Tabellen vereinfachen. <PERSON><PERSON> du da<PERSON><PERSON> sorg<PERSON>, dass Tabellenüberschriften immer auf bestimmte Zellen verweisen, kann dies für Screenreader-Nutzer hilfreich sein. [Weitere Informationen zu Tabellenüberschriften.](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "<PERSON>ür `<th>`-Elemente und Elemente mit `[role=\"columnheader\"/\"rowheader\"]` sind keine Datenzellen vorhanden, die sie beschreiben."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "<PERSON>ür `<th>`-Elemente und Elemente mit `[role=\"columnheader\"/\"rowheader\"]` sind Datenzellen vorhanden, die sie beschreiben."}, "core/audits/accessibility/valid-lang.js | description": {"message": "<PERSON>n ein gültiger [BCP-47-Sprachcode](https://www.w3.org/International/questions/qa-choosing-language-tags#question) für Elemente angegeben wird, kann der Text besser von Screenreadern ausgesprochen werden. [Informationen zur Verwendung des Attributs `lang`.](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-Attribute weisen keinen gültigen Wert auf"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-Attribute weisen einen gültigen Wert auf"}, "core/audits/accessibility/video-caption.js | description": {"message": "<PERSON>n ein Video Untertitel enthält, können gehörl<PERSON> und hörgeschädigte Nutzer die Informationen im Video besser verstehen. [Weitere Informationen zu Untertiteln in Videos.](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>`-<PERSON><PERSON><PERSON> enthalten kein `<track>`-Element mit `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>`-<PERSON><PERSON><PERSON> enthalten ein `<track>`-<PERSON>ement mit `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Aktueller Wert"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Vorgeschlagenes Token"}, "core/audits/autocomplete.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> von `autocomplete` können Nutzer Formulare schneller ausfüllen. Du kannst die Funktion aktivieren, indem du einen gültigen Wert für das Attribut `autocomplete` festlegst. [Wei<PERSON><PERSON> Informationen zu `autocomplete` in Formularen](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` Elemente haben keine korrekten `autocomplete`-Attribute"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON> Prüfu<PERSON>"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Tokenreihenfolge prüfen"}, "core/audits/autocomplete.js | title": {"message": "`<input>`-<PERSON><PERSON><PERSON> verwenden `autocomplete` kor<PERSON>t"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` Token: „{token}“ ist in {snippet} ungültig"}, "core/audits/autocomplete.js | warningOrder": {"message": "Tokenreihenfolge prüfen: \"{tokens}\" in {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Kann behoben werden"}, "core/audits/bf-cache.js | description": {"message": "Viele Bedienvorgänge werden ausgeführt, indem eine vorherige Seite aufgerufen oder zur nächsten gegangen wird. Der Back-Forward-Cache (bfcache) kann diese Vorgänge beschleunigen. [Weitere Informationen zum bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 <PERSON><PERSON><PERSON> für den Fehler}other{# <PERSON><PERSON><PERSON><PERSON> für den Fehler}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Grund für den Fehler"}, "core/audits/bf-cache.js | failureTitle": {"message": "Seite hat die Wiederherstellung des Back-Forward-Caches verhindert"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Fehlertyp"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Kann nicht behoben werden"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Ausstehende Browserunterstützung"}, "core/audits/bf-cache.js | title": {"message": "Seite hat die Wiederherstellung des Back-Forward-Caches nicht verhindert"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome-Erweiterungen haben die Ladegeschwindigkeit dieser Seite beeinträchtigt. Versuche, die Seite im Inkognito-Modus oder mit einem Chrome-Profil ohne Erweiterungen zu überprüfen."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Skriptauswertung"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "CPU-Zeit insgesamt"}, "core/audits/bootup-time.js | description": {"message": "Versuche, die Zeit für das Parsen, Kompilieren und Ausführen von JavaScript zu reduzieren. Die Bereitstellung kleinerer JS-Nutzlasten kann dabei helfen. [Informationen zum Reduzieren der JavaScript-Ausführungszeit.](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "Ausführungszeit von JavaScript reduzieren"}, "core/audits/bootup-time.js | title": {"message": "JavaScript-Ausführungszeit"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Entferne große, doppelt vorhandene JavaScript-Module aus Bundles, um unnötige Datenübertragungen im Netzwerk zu reduzieren."}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "In JavaScript-Bundles doppelt vorhandene Module entfernen"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Große GIF-Dateien sind nur bedingt für die Auslieferung animierter Inhalte geeignet. Du kannst stattdessen MPEG4- oder WebM-Videos für Animationen und PNG oder WebP für statische Bilder verwenden und so die Netzwerkbytes reduzieren. [Weitere Informationen zu effizienten Videoformaten](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Videoformate für animierte Inhalte verwenden"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Dank Polyfills und Transformationen können veraltete Browser die neuen JavaScript-Funktionen nutzen. Bei modernen Browsern hingegen sind viele davon nicht erforderlich. Für dein JavaScript-Bundle solltest du eine moderne Skriptimplementierungsstrategie unter Verwendung der module/nomodule-Funktionserkennung einsetzen. So kannst du einerseits den an moderne Browser übermittelten Code auf das Nötige reduzieren und gleichzeitig veraltete Browser bestmöglich unterstützen. [Informationen zur Verwendung modernen JavaScripts](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dass in modernen Browsern veraltetes JavaScript bereitgestellt wird"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Bildformate wie WebP und AVIF bieten oft eine bessere Komprimierung als PNG oder JPEG, wodurch sie schneller heruntergeladen werden und weniger Daten verbrauchen. [Weitere Informationen zu modernen Bildformaten.](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Bilder in modernen Formaten bereitstellen"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Wenn du Lazy Loading für nicht sichtbare und versteckte Bilder verwendest und sie zurückstellst, bis alle wichtigen Ressourcen geladen wurden, kannst du die Zeit bis Interaktivität reduzieren. [Weitere Informationen.](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Nicht sichtbare Bilder aufschieben"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ressourcen blockieren den First Paint deiner Seite. Versuche, wichtiges JS und wichtige CSS inline anzugeben und alle nicht kritischen JS und Stile zurückzustellen. [Informationen dazu, wie sich Ressourcen eliminieren lassen, die das Rendering blockieren.](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Ressourcen beseitigen, die das Rendering blockieren"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Große Netzwerknutzlasten kosten Nutzer bares Geld und hängen eng mit langen Ladezeiten zusammen. [Informationen zum Verringern der Nutzlastgröße.](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Die Gesamtgröße war {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Sehr große Netzwerknutzlasten vermeiden"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Vermeidet sehr große Netzwerknutzlasten"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Durch das Reduzieren von CSS-<PERSON><PERSON> lassen sich Netzwerknutzlasten senken. [Informationen zum Reduzieren von CSS.](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS komprimieren"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Durch die Komprimierung von JavaScript-Dateien können Nutzlastgrößen und die Zeit zum Parsen von Skripts reduziert werden. [Informationen zum Reduzieren von JavaScript.](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript komprimieren"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Du kannst ungültige Regeln in Stylesheets reduzieren und CSS-Code zurückstellen, der nicht für ohne Scrollen sichtbare Inhalte („above the fold“) verwendet wird, um den Datenverbrauch durch Netzwerkaktivität zu senken. [Informationen zum Reduzieren von nicht verwendetem CSS-Code.](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduziere nicht verwendete CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Um den Datenverbrauch durch Netzwerkaktivität zu senken, kannst du nicht verwendetes JavaScript reduzieren und das Laden von Skripts zurückstellen, bis sie benötigt werden. [Informationen zum Reduzieren von nicht verwendetem JavaScript.](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduziere nicht verwendetes JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Eine lange Verweildauer im Cache kann wiederholte Besuche deiner Seite beschleunigen. [Weitere Informationen zu effizienten Cache-Richtlinien.](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 Ressource gefunden}other{# Ressourcen gefunden}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statische Inhalte mit einer effizienten Cache-Richtlinie bereitstellen"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Verwendet eine effiziente Cache-Richtlinie für statische Inhalte"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimierte Bilder werden schneller geladen und verbrauchen weniger mobile Daten. [Informationen zum effizienten Codieren von Bildern.](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Bilder effizient codieren"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Tatsächliche Abmessungen"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Angezeigte Abmessungen"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Bilder waren größer als die angezeigte Größe"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Bilder entsprachen der angezeigten Größe"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON>elle Bilder bereit, die eine angemessene Größe haben, um mobile Daten zu sparen und die Ladezeit zu verbessern. [Weitere Informationen.](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Bilder rich<PERSON><PERSON>en"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Textbasierte Ressourcen sollten komprimiert (gzip, Deflate oder Brotli) ausgeliefert werden, um die Netzwerkbytes insgesamt zu minimieren. [Weitere Informationen zur Textkomprimierung.](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Textkomprimierung aktivieren"}, "core/audits/content-width.js | description": {"message": "Wenn die Breite deiner App-Inhalte nicht mit der des Darstellungsbereichs übereinstimmt, ist deine App möglicherweise nicht für Bildschirme von Mobilgeräten optimiert. [Informationen zum Anpassen der Größe von Inhalten an den Darstellungsbereich.](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "Die Größe des Darstellungsbereichs von {innerWidth} Pixeln stimmt nicht mit der Fenstergröße von {outerWidth} Pixeln überein."}, "core/audits/content-width.js | failureTitle": {"message": "Inhalt hat nicht die richtige Größe für den Darstellungsbereich"}, "core/audits/content-width.js | title": {"message": "Inhalt hat die richtige Größe für den Darstellungsbereich"}, "core/audits/critical-request-chains.js | description": {"message": "In den unten aufgeführten Ketten kritischer Anfragen kannst du sehen, welche Ressourcen mit einer hohen Priorität geladen werden. Versuche, die Ketten zu kürzen, die Downloadgröße von Ressourcen zu reduzieren oder das Herunterladen unnötiger Ressourcen zurückzustellen, um den Seitenaufbau zu beschleunigen. [<PERSON><PERSON> dazu, wie sich das Verketten kritischer Anfragen vermeiden lässt.](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 Kette gefunden}other{# Ketten gefunden}}"}, "core/audits/critical-request-chains.js | title": {"message": "Verkettung kritischer Anfragen vermeiden"}, "core/audits/csp-xss.js | columnDirective": {"message": "Anweisung"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Schweregrad"}, "core/audits/csp-xss.js | description": {"message": "Eine starke Content Security Policy (CSP) reduziert das Risiko für Cross-Site-Scripting-Angriffe (XSS-Angriffe) erheblich. [Weitere Informationen](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntax"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Diese Seite enthält eine CSP, die in einem „<meta>“-Tag definiert wird. Du kannst die CSP in einen HTTP-Header verschieben oder eine andere strenge CSP in einem HTTP-Header definieren."}, "core/audits/csp-xss.js | noCsp": {"message": "Kein CSP im erzwungenen Modus gefunden"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>, dass CSP effektiv gegen XSS-Angriffe wirkt"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Veraltet/Warnung"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Verworfene APIs werden aus dem Browser entfernt. [Weitere Informationen zu verworfenen APIs.](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 Warnung gefunden}other{# Warnungen gefunden}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Verwendet veraltete APIs"}, "core/audits/deprecations.js | title": {"message": "Vermeidet veraltete APIs"}, "core/audits/dobetterweb/charset.js | description": {"message": "Die Zeichencodierung muss deklariert werden. Dazu kann ein `<meta>`-Tag in den ersten 1024 Byte des HTML-Codes oder im HTTP-Antwortheader „Content-Type“ angegeben werden. [Weitere Informationen zum Deklarieren der Zeichencodierung.](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Die Charset-Deklaration fehlt oder erscheint zu spät im HTML-Code"}, "core/audits/dobetterweb/charset.js | title": {"message": "Korrekt definierter Zeichensatz"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Wenn du einen DOCTYPE angi<PERSON>t, verhin<PERSON>t du, dass der Browser zum Quirks-Modus wechselt. [Weitere Informationen zum Deklarieren eines DOCTYPE.](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE-Name muss dieser String sein: `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Das Dokument enthält das Element „`doctype`“, das den „`limited-quirks-mode`“ auslöst"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument muss einen DOCTYPE enthalten"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "PublicId sollte ein leerer String sein"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "SystemId sollte ein leerer String sein"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Das Dokument enthält das Element „`doctype`“, das den „`quirks-mode`“ auslöst"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Seite verfügt nicht über den HTML-DOCTYPE und startet daher den Quirks-Modus"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Seite verfügt über den HTML-DOCTYPE"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistik"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Wert"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Ein großes DOM führt zu hoher Arbeitsspeichernutzung, langwierigen [Stilberechnungen](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) und kostspieligen [dynamischen Umbrüchen im Layout](https://developers.google.com/speed/articles/reflow). [Informationen zum Vermeiden eines zu großen DOMs.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 Element}other{# Elemente}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Übermäßige DOM-Größe vermeiden"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximale DOM-Tiefe"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM-Elemente insgesamt"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximale Anzahl von untergeordneten Elementen"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Vermeidet eine übermäßige DOM-Größe"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Wenn Websites den Standort ohne Begründung anfordern, sind Nutzer schnell misstrauisch oder irritiert. Versuche stattdessen, die Anforderung mit einer Nutzeraktion zu verbinden. [Weitere Informationen zur Berechtigung zur Standortbestimmung.](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Fordert die Berechtigung zur Standortbestimmung beim Seitenaufbau an"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON> während des Seitenaufbaus keine Berechtigung zur Standortbestimmung an"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Art des Problems"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Im `Issues`-Bereich der Chrome-Entwicklertools wurden ungelöste Probleme protokolliert. Sie können durch fehlgeschlagene Netzwerkanfragen, unzureichende Sicherheitsmaßnahmen und andere Browser-Probleme verursacht sein. <PERSON><PERSON><PERSON> den Bereich mit den Problemen in Chrome-Entwicklertools, um weitere Details zu den einzelnen Problemen aufzurufen."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Im `Issues`-Bereich der Chrome-Entwicklertools wurden Probleme protokolliert"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Von Richtlinie zu ursprungsübergreifenden Anfragen blockiert"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Starke Ressourcennutzung durch Werbung"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Keine Probleme im `Issues`-Bereich der Chrome-Entwicklertools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Version"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Alle Front-End-JavaScript-Bibliotheken auf der Seite wurden erkannt. [Weitere Informationen.](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript-Bibliotheken erkannt"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON> mit langsamen Verbindungen können externe Skripts, die dynamisch über `document.write()` eingefügt werden, den Seitenaufbau um einige Sekunden verzögern. [Informationen zum Vermeiden von „document.write()“.](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` vermeiden"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Verwendet kein `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Wenn Websites die Berechtigung zum Senden von Benachrichtigungen ohne Begründung anfordern, sind <PERSON>utzer schnell misstrauisch oder irritiert. Versuche stattdessen, die Anforderung mit Touch-Gesten zu verbinden. [Weitere Informationen zum verantwortungsvollen Einholen der Berechtigung zum Senden von Benachrichtigungen.](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Fordert die Benachrichtigungsberechtigung beim Seitenaufbau an"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON> während des Seitenaufbaus keine Benachrichtigungsberechtigung an"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokoll"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 bietet gegenüber HTTP/1.1 viele Vorteile, wie z. B. binäre Header und Multiplexverfahren. [Weitere Informationen zu HTTP/2.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{Ressourcen für 1 Anfrage nicht über HTTP/2 bereitgestellt}other{Ressourcen für # Anfragen nicht über HTTP/2 bereitgestellt}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Verwende HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Wenn du deine Event-Listener für Tipp- und Mausradbewegungen als `passive` mark<PERSON>t, kannst du damit die Scrollleistung deiner Seite verbessern. [Weitere Informationen zur Verwendung von passiven Event-Listenern.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Verwendet keine passiven Listener zur Verbesserung der Scrollleistung"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Verwendet passive Listener zur Verbesserung der Scrollleistung"}, "core/audits/errors-in-console.js | description": {"message": "In der Konsole protokollierte Fehler weisen auf ungelöste Probleme hin. Sie können durch fehlgeschlagene Netzwerkanfragen und andere Browserprobleme verursacht werden. [Weitere Informationen](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON> wurden Browserfehler in der Konsole protokolliert"}, "core/audits/errors-in-console.js | title": {"message": "<PERSON><PERSON> wurden keine Brows<PERSON><PERSON>hler in der Konsole protokolliert"}, "core/audits/font-display.js | description": {"message": "<PERSON><PERSON><PERSON> das CSS-Feature `font-display`, damit der Text für Nutzer sichtbar ist, während Webfonts geladen werden. [Weitere Informationen zu `font-display`.](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>, dass der Text während der Webfont-Ladevorgänge sichtbar bleibt"}, "core/audits/font-display.js | title": {"message": "Der gesamte Text bleibt während der Webfont-Ladevorgänge sichtbar"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse konnte nicht automatisch den `font-display`-Wert für den Ursprung {fontOrigin} prüfen.}other{Lighthouse konnte nicht automatisch die `font-display`-Werte für den Ursprung {fontOrigin} prüfen}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Seitenverh<PERSON><PERSON><PERSON> (Original)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Seitenverhältnis (angezeigt)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Die Bildgröße sollte dem natürlichen Seitenverhältnis entsprechen. [Weitere Informationen zum Seitenverhältnis von Bildern.](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> mit einem falschen Seitenverhältnis an"}, "core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON><PERSON> mit einem korrekten Seitenverhältnis an"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Originalgröße"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Angezeigte Größe"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Erwartete Größe"}, "core/audits/image-size-responsive.js | description": {"message": "Die originalen Abmessungen eines Bildes sollten proportional zu der Displaygröße und dem Pixel-Verhältnis sein, damit das Bild optimal angezeigt wird. [Weitere Informationen zu responsiven Bildern.](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Stellt Bilder mit niedriger Auflösung bereit"}, "core/audits/image-size-responsive.js | title": {"message": "Stellt Bilder mit angemessener Auflösung bereit"}, "core/audits/installable-manifest.js | already-installed": {"message": "Die App ist bereits installiert"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Ein erforderliches Symbol konnte nicht aus dem Manifest heruntergeladen werden"}, "core/audits/installable-manifest.js | columnValue": {"message": "Grund für den Fehler"}, "core/audits/installable-manifest.js | description": {"message": "Der Service Worker ermöglicht es deiner App, viele Funktionen von progressiven Web-A<PERSON> zu nutzen, beispielswei<PERSON> den Offlinemodus, das Hinzufügen zum Startbildschirm und Push-Benachrichtigungen. Bei ordnungsgemäßen Service Worker- und Manifestimplementierungen können Browser Nutzer direkt auffordern, deine Web-App zum Startbildschirm hinzuzufügen. Das kann zu mehr Interaktionen führen. [Weitere Informationen zu Anforderungen an die Installierbarkeit für Manifeste](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 <PERSON>rund}other{# <PERSON><PERSON><PERSON><PERSON>}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Das Manifest der Web-App oder der Service Worker erfüllt die Anforderungen an die Installierbarkeit nicht"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Die Play Store App-URL und die Play Store-ID stimmen nicht überein"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Seite wird in einem Inkognitofenster geladen"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Die Property „display“ des Manifests muss entweder „standalone“, „fullscreen“ oder „minimal-ui“ sein"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Das Manifest enthält das Feld „display_override“ und der erste unterstützte Anzeigemodus muss entweder „standalone“, „fullscreen“ oder „minimal-ui“ sein"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifest konnte nicht abgerufen oder geparst werden oder ist leer"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Die Manifest-URL hat sich geändert, während das Manifest abgerufen wurde."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest enthält kein Feld „name“ oder „short_name“"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Das Manifest enthält kein passendes Symbol – das Format PNG, SVG oder WebP mit mindestens {value0} px ist erforderlich, das Attribut „sizes“ muss festgelegt werden und das Attribut „purpose“ muss „any“ enthalten."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Keines der bereitgestellten Symbole weist das Format PNG, SVG oder WebP mit mindestens {value0} px Square und nicht festgelegtem oder auf „any“ gesetztem Attribut „purpose“ auf"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Heruntergeladenes Symbol war leer oder fehlerhaft"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Play Store-ID fehlt"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Die Seite hat keine Manifest-URL <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Es wurde kein passender Service Worker gefunden. Versuche, die Seite zu aktualisieren oder prüfe, ob der Bereich des Service Workers für die aktuelle Seite den Bereich und die Start-URL des Manifests umfasst."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Service Worker konnte ohne das Feld „start_url“ im Manifest nicht geprüft werden"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ID des Installierbarkeitsfehlers „{errorId}“ wurde nicht erkannt"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Seite wird nicht von einer sicheren Quelle geladen"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Seite wird nicht im Hauptframe geladen"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Seite kann offline nicht geladen werden"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA wurde installiert und die Überprüfungen zur Installierbarkeit wurden zurückgesetzt."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Die angegebene Anwendungsplattform wird nicht von <PERSON> un<PERSON>tützt"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Das Manifest gibt an: prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "„prefer_related_applications“ wird nur in Chrome Beta und stabilen Versionen von Android unterstützt."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse konnte nicht feststellen, ob es einen Service Worker gibt. Bitte versuche es mit einer neueren Version von Chrome noch einmal."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "<PERSON> Manifest-URL-Schema ({scheme}) wird unter Android nicht unterstützt."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Start-URL des Manifests ist nicht gültig"}, "core/audits/installable-manifest.js | title": {"message": "Das Manifest der Web-App und der Service Worker erfüllen die Anforderungen an die Installierbarkeit"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Eine URL im Manifest enthält Nutzernamen, Passwort oder Port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Die Seite kann offline nicht geladen werden. Die Seite gilt nach der stabilen Version von Chrome 93 ab August 2021 als nicht installierbar."}, "core/audits/is-on-https.js | allowed": {"message": "Zugelassen"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Unsichere URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Alle Websites sollten durch HTTPS geschützt werden – selbst wenn sie keine sensiblen Daten enthalten. Auch [gemischte Inhalte](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), bei denen einige Ressourcen über HTTP geladen werden, obwohl die ursprüngliche Anfrage über HTTPS gestellt wurde, sind zu vermeiden. HTTPS verhindert, dass andere die Website manipulieren oder die Kommunikation zwischen deiner App und deinen Nutzern mitverfolgen können, und ist eine Voraussetzung für HTTP/2 und viele neue Webplattform-APIs. [Weitere Informationen zu HTTPS.](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 unsichere Anfrage gefunden}other{# unsichere Anfragen gefunden}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Verwendet nicht HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Verwendet HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatisch auf HTTPS umgestellt"}, "core/audits/is-on-https.js | warning": {"message": "Zugelassen mit Warnung"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Dies ist das größte Inhaltselement, das im Darstellungsbereich angezeigt wird. [Weitere Informationen zum Largest Contentful Paint-Element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Largest Contentful Paint-Element"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS-Beitrag"}, "core/audits/layout-shift-elements.js | description": {"message": "Diese DOM-Elemente tragen am stärksten zur CLS der Seite bei. [Informationen zur Verbesserung der CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Umfangreiche Layoutverschiebungen vermeiden"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Ohne Scrollen sichtbare Bilder („above the fold“) die mit Lazy Loading geladen werden, werden später im Lebenszyklus der Seite gerendert. Dies kann zu Verzögerungen beim Largest Contentful Paint führen. [Weitere Informationen zum optimalen Lazy Loading.](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Largest Contentful Paint-Bild wurde mit Lazy Loading geladen"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Largest Contentful Paint-Bild wurde nicht mit Lazy Loading geladen"}, "core/audits/long-tasks.js | description": {"message": "Listet die längsten Aufgaben im Hauptthread auf. Das ist nützlich, um die wichtigsten Ursachen für die Eingabeverzögerungen zu ermitteln. [<PERSON><PERSON> dazu, wie sich lange Aufgaben im Hauptthread vermeiden lassen](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# lange Aufgabe gefunden}other{# lange Aufgaben gefunden}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON>-Aufgaben vermeiden"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "<PERSON>ers<PERSON>, die Zeit für das Parsen, Kompilieren und Ausführen von JavaScript zu reduzieren. Die Bereitstellung kleinerer JS-Nutzlasten kann dabei helfen. [<PERSON><PERSON> da<PERSON>, wie sich der Aufwand für den Hauptthread minimieren lässt](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Aufwand für Hauptthread minimieren"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiert den Aufwand für den Hauptthread"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Damit du möglichst viele Nutzer erreichen kannst, sollte deine Website mit allen gängigen Browsern kompatibel sein. [Weitere Informationen.](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Website funktioniert auf verschiedenen Browsern"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Für die einzelnen Seiten sollten Deeplinks erstellt werden können. Achte darauf, dass die entsprechenden URLs eindeutig sind, sodass sich die Seiten in sozialen Netzwerken leichter teilen lassen. [Weitere Informationen zum Angeben von Deeplinks.](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Jede Seite hat eine URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Übergänge sollten sich auch bei einer langsamen Netzwerkverbindung schnell anfühlen. Dies ist entscheidend dafür, wie der Nutzer die Leistung wahrnimmt. [Weitere Informationen zu Seitenübergängen.](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Seitenübergänge vermitteln nicht das Gefühl von übermäßigen Ladezeiten"}, "core/audits/maskable-icon.js | description": {"message": "Mit einem maskierbaren Symbol wird die Bildform beim Installieren der App auf einem Gerät vollständig gefüllt, ohne dass es zu einem Letterbox-Effekt kommt. [Informationen zu maskierbaren Manifestsymbolen.](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Das Manifest hat kein maskierbares Symbol"}, "core/audits/maskable-icon.js | title": {"message": "Das Manifest hat ein maskierbares Symbol"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "„Cumulative Layout Shift“ misst die Bewegung sichtbarer Elemente innerhalb des Darstellungsbereichs. [Weitere Informationen zum Messwert „Cumulative Layout Shift“.](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "„Interaction to Next Paint“ misst die Reaktionsfähigkeit der Seite, d. h. wie lange die Seite braucht, um sichtbar auf Nutzereingaben zu reagieren. [Weitere Informationen zum Messwert „Interaction to Next Paint“.](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "„First Contentful Paint“ gibt an, wann der erste Text oder das erste Bild gerendert wird. [Weitere Informationen zum Messwert „First Contentful Paint“.](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "„Inhalte weitgehend gezeichnet“ gibt an, wann die Hauptinhalte einer Seite sichtbar sind. [Weitere Informationen zum Messwert „Inhalte weitgehend gezeichnet“.](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "„Zeit bis Interaktivität“ entspricht der Zeit, die vergeht, bis die Seite vollständig interaktiv ist. [Weitere Informationen zum Messwert „Zeit bis Interaktivität“.](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "„Largest Contentful Paint“ gibt an, wie lange das Rendern des größten Textblocks oder Bildelements dauert. [Weitere Informationen zum Messwert „Largest Contentful Paint“](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Das maximale potenzielle First Input Delay, das bei deinen Nutzern auftreten kann, entspricht der Dauer der längsten Aufgabe. [Weitere Informationen zum Messwert „Maximales potenzielles First Input Delay“](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Der Geschwindigkeitsindex gibt an, wie schnell die Inhalte einer Seite sichtbar dargestellt werden. [Weitere Informationen zum Messwert „Geschwindigkeitsindex“.](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Summe aller Zeiträume (in Millisekunden) zwischen FCP und „Zeit bis Interaktivität“, wenn die Aufgabendauer 50 ms überschreitet. [Weitere Informationen zum Messwert „Gesamte Blockierzeit“.](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Die Netzwerk-Umlaufzeit (Round Trip Time, RTT) hat großen Einfluss auf die Leistung. Wenn die RTT zu einem Ursprung hoch ausfällt, weist dies darauf hin, dass die Leistung mit Servern, die sich näher beim <PERSON> befinden, verbessert werden kann. [Weitere Informationen zur Umlaufzeit.](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "Netzwerk-Umlaufzeit"}, "core/audits/network-server-latency.js | description": {"message": "Serverlatenzen können sich auf die Leistung im Web auswirken. Wenn die Serverlatenz eines Ursprungs hoch ist, weist dies darauf hin, dass der Server überlastet ist oder eine schlechte Back-End-Leistung bietet. [Weitere Informationen zur Serverantwortzeit.](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Server-Back-End-<PERSON><PERSON><PERSON>"}, "core/audits/no-unload-listeners.js | description": {"message": "Das `unload`-<PERSON><PERSON><PERSON><PERSON> wird nicht zuverlässig ausgelöst. Wenn der Listener darauf wartet, kann das Browseroptimierungen wie den Back-Forward-<PERSON><PERSON> beeinträchtigen. Bitte verwende stattdessen `pagehide`- oder `visibilitychange`-Ereignisse. [Weitere Informationen zum Entfernen von Event-Listenern](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> einen `unload`-Listener"}, "core/audits/no-unload-listeners.js | title": {"message": "<PERSON>ine `unload`-<PERSON><PERSON><PERSON><PERSON>-Listener gefunden"}, "core/audits/non-composited-animations.js | description": {"message": "Nicht zusammengesetzte Animationen werden eventuell nicht richtig gerendert und können den CLS-Wert erhöhen. [<PERSON><PERSON> dazu, wie sich nicht zusammengefasste Animationen vermeiden lassen](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animiertes Element gefunden}other{# animierte Elemente gefunden}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Filterbezogene Property verschiebt möglicherweise Pixel"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON>iel hat eine andere Animation, die nicht kompatibel ist"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Effekt hat einen zusammengesetzten Modus, der nicht \"replace\" ist"}, "core/audits/non-composited-animations.js | title": {"message": "<PERSON>cht zusammengesetzte Animationen vermeiden"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Transformationsbezogene Property hängt von Boxgröße ab"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{<PERSON>cht unterstützte CSS-Property: {properties}}other{Nicht unterstützte CSS-Properties: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Effekt hat nicht unterstützte Timingparameter"}, "core/audits/performance-budget.js | description": {"message": "<PERSON> Anzahl und Größe der Netzwerkanfragen sollten unter den Zielvorgaben des Leistungsbudgets liegen. [Weitere Informationen zu Leistungsbudgets.](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 <PERSON><PERSON><PERSON>}other{# Anfragen}}"}, "core/audits/performance-budget.js | title": {"message": "Leistungsbudget"}, "core/audits/preload-fonts.js | description": {"message": "Du solltest `optional`-Schriftarten vorab laden, damit sie von <PERSON>uchern verwendet werden können. [Weitere Informationen zum Vorabladen von Schriftarten](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Schriftarten mit `font-display: optional` werden nicht vorab geladen"}, "core/audits/preload-fonts.js | title": {"message": "Schriftarten mit `font-display: optional` werden vorab geladen"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Wenn der Seite das LCP-Element dynamisch hinzugefügt wird, solltest du das Bild vorab laden, um den LCP zu verbessern. [Weitere Informationen zum Vorabladen von LCP-Elementen.](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Largest Contentful Paint-Bild vorab laden"}, "core/audits/redirects.js | description": {"message": "Weiterleitungen führen zu zusätzlichen Verzögerungen, bevor die Seite geladen werden kann. [Informationen zum Vermeiden von Seitenweiterleitungen.](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Mehrere Weiterleitungen auf die Seite vermeiden"}, "core/audits/resource-summary.js | description": {"message": "Füge zum Einrichten von Budgets für die Anzahl und Größe von Seitenressourcen eine budget.json-Datei hinzu. [Weitere Informationen zu Leistungsbudgets.](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 Anfrage • {byteCount, number, bytes} Ki<PERSON>}other{# Anfragen • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Halte die Anfrageanzahl niedrig und die Übertragungsgröße gering"}, "core/audits/seo/canonical.js | description": {"message": "Über kanonische Links wird angegeben, welche URL in den Suchergebnissen angezeigt werden soll. [Weitere Informationen zu kanonischen Links.](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON><PERSON> in Konflikt stehende URLs ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Ungültige URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Verweist auf einen anderen `hreflang`-Speicherort ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Ist keine absolute URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Verweist auf die Stamm-URL (die Startseite) der Domain statt auf eine identische Inhaltsseite"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument enthält kein gültiges `rel=canonical`-Element"}, "core/audits/seo/canonical.js | title": {"message": "Dokument enthält ein gültiges `rel=canonical`-Element"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON> kann nicht gecrawlt werden"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Suchmaschinen verwenden möglicherweise `href`-Attribute für Links, um Websites zu crawlen. Das `href`-Attribut von Anchor-Elementen muss auf ein geeignetes Z<PERSON> verwei<PERSON>, damit mehr Seiten auf der Website gefunden werden können. [<PERSON><PERSON>, wie Links für Crawler zugänglich gemacht werden](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Links können nicht gecrawlt werden"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Links können gecrawlt werden"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Zusätzlicher unlesbarer Text"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Schriftgröße"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% des Seitentexts"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Schriftgrößen unter 12 px sind zu klein. <PERSON><PERSON><PERSON> von Mobilgeräten müssen den Text mit den Fingern heranzoomen, um ihn lesen zu können. <PERSON><PERSON><PERSON> da<PERSON>, dass mehr als 60 % des Seitentextes eine Schriftgröße von mindestens 12 px haben. [Weitere Informationen zu lesbaren Schriftgrößen.](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} gut lesbarer Text"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Text ist nicht lesbar, weil kein Meta-Tag für den Darstellungsbereich vorhanden ist, das für Bildschirme von Mobilgeräten optimiert ist."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokument enthält keine gut lesbaren Schriftgrößen"}, "core/audits/seo/font-size.js | legibleText": {"message": "<PERSON><PERSON>"}, "core/audits/seo/font-size.js | title": {"message": "Dokument enthält gut lesbare Schriftgrößen"}, "core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON> von „hreflang“-Links können Suchmaschinen ermitteln, welche Version einer Seite sie in den Suchergebnissen für eine bestimmte Sprache oder Region anzeigen sollen. [Weitere Informationen zu `hreflang`.](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument enthält kein gültiges `hreflang`-Element"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativer href-<PERSON><PERSON>"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument enthält ein gültiges `hreflang`-Element"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Unerwarteter Sprachcode"}, "core/audits/seo/http-status-code.js | description": {"message": "Seiten mit ungültigen HTTP-Statuscodes werden möglicherweise nicht richtig indexiert. [Weitere Informationen zu HTTP-Statuscodes.](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Seite hat keinen gültigen HTTP-Statuscode"}, "core/audits/seo/http-status-code.js | title": {"message": "Seite hat einen gültigen HTTP-Statuscode"}, "core/audits/seo/is-crawlable.js | description": {"message": "Suchmaschinen können deine Seiten nicht in die Suchergebnisse aufnehmen, wenn sie nicht berechtigt sind, sie zu crawlen. [Weitere Informationen zu Crawler-Anweisungen.](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Seite ist von Indexierung ausgeschlossen"}, "core/audits/seo/is-crawlable.js | title": {"message": "Seite ist nicht von Indexierung ausgeschlossen"}, "core/audits/seo/link-text.js | description": {"message": "Wenn du beschreibenden Linktext verwendest, können Suchmaschinen deine Inhalte besser verstehen. [Informationen zu barrierefreien Links.](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 Link gefunden}other{# Links gefunden}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Links enthalten keinen beschreibenden Text"}, "core/audits/seo/link-text.js | title": {"message": "Links haben beschreibenden Text"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Du kannst das [Testtool für strukturierte Daten](https://search.google.com/structured-data/testing-tool/) und den [Lint für strukturierte Daten](http://linter.structured-data.org/) ausführen, um strukturierte Daten zu validieren. [Weitere Informationen zu strukturierten Daten.](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturierte Daten sind gültig"}, "core/audits/seo/meta-description.js | description": {"message": "Meta-Beschreibungen können in die Suchergebnisse aufgenommen werden, um die Seiteninhalte kurz zusammenzufassen. [Weitere Informationen zu Meta-Beschreibungen.](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Beschreibungstext ist leer."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument enthält keine Meta-Beschreibung"}, "core/audits/seo/meta-description.js | title": {"message": "Dokument enthält eine Meta-Beschreibung"}, "core/audits/seo/plugins.js | description": {"message": "Suchmaschinen können keine Plug-in-Inhalte indexieren. Außerdem werden Plug-ins auf vielen Geräten eingeschränkt oder nicht unterstützt. [Weitere Informationen zum Vermeiden von Plug-ins.](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument verwendet Plug-ins"}, "core/audits/seo/plugins.js | title": {"message": "Dokument verwendet keine Plug-ins"}, "core/audits/seo/robots-txt.js | description": {"message": "Wenn deine robots.txt-<PERSON><PERSON> fehlerhaft ist, können Crawler möglicherweise nicht nachvollziehen, wie deine Website gecrawlt oder indexiert werden soll. [Weitere Informationen zu robots.txt.](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt-Anfrage hat diesen HTTP-Status zurückgegeben: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 Fehler gefunden}other{# Fehler gefunden}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse konnte keine robots.txt-<PERSON><PERSON> her<PERSON>n"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt ist ungültig"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt ist gültig"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktive Elemente wie Schaltflächen und Links sollten groß genug sein (48 × 48 px) und über genügend Zwischenraum verfügen, damit sie leicht angetippt werden können. Sie dürfen sich nicht mit anderen Elementen überschneiden. [Weitere Informationen zu Tippzielen.](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} der Tippziele haben eine passende Größe"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Die Tippziele sind zu klein, weil kein Meta-Tag für den Darstellungsbereich vorhanden ist, das für Bildschirme von Mobilgeräten optimiert ist"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> von Tippzielen ist nicht richtig eingestellt"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Sich überschneidendes Ziel"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> von Tippzielen ist richtig eingestellt"}, "core/audits/server-response-time.js | description": {"message": "Achte auf eine möglichst kurze Serverantwortzeit für das Hauptdokument, weil alle anderen Anfragen davon abhängen. [Weitere Informationen zum Messwert „Time to First Byte“.](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "Stammdokument brauchte {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Erstreaktionszeit des Servers verringern"}, "core/audits/server-response-time.js | title": {"message": "Kurze Erstreaktionszeit des Servers"}, "core/audits/service-worker.js | description": {"message": "Der Service Worker ermöglicht es deiner App, viele Funktionen von progressiven Web-Apps zu nutzen, beispielswei<PERSON> den Offlinemodus, das Hinzufügen zum Startbildschirm und Push-Benachrichtigungen. [Weitere Informationen.](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Diese Seite wird von einem Service Worker kontrolliert. Es wurde jedoch keine `start_url` gefunden, weil das Manifest nicht als gültige JSON-Datei geparst werden konnte."}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Diese Seite wird zwar von einem Service Worker kontrolliert, die `start_url` ({startUrl}) liegt jedoch nicht in dessen Zuständigkeitsbereich ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Diese Seite wird zwar von einem Service Worker kontrolliert, es wurde jedoch keine `start_url` gefunden, da kein Manifest abgerufen wurde."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Dieser Ursprung verfügt über mindestens einen Service Worker. Die Seite ({pageUrl}) liegt jedoch nicht in dessen Zuständigkeitsbereich."}, "core/audits/service-worker.js | failureTitle": {"message": "<PERSON>s wurde kein Service Worker erkannt, der die Seite und `start_url` kontrolliert"}, "core/audits/service-worker.js | title": {"message": "Es wurde ein Service Worker erkannt, der die Seite und `start_url` kontrolliert."}, "core/audits/splash-screen.js | description": {"message": "Wenn du deinen Startbildschirm passend zum Design deiner App gestaltest, vermittelst du den Nutzern schon beim Ladevorgang einen hochwertigen Eindruck. [Weitere Informationen zu Startbildschirmen.](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "Nicht für einen benutzerdefinierten Startbildschirm konfiguriert"}, "core/audits/splash-screen.js | title": {"message": "Konfiguriert für einen benutzerdefinierten Startbildschirm"}, "core/audits/themed-omnibox.js | description": {"message": "Die Adressleiste des Browsers kann an das Design deiner Website angepasst werden. [Weitere Informationen zum Gestalten der Adressleiste.](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Legt keine Designfarbe für die Adressleiste fest."}, "core/audits/themed-omnibox.js | title": {"message": "Legt eine Designfarbe für die Adressleiste fest."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Erfolgsgeschichten)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Soziale Netzwerke)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkt"}, "core/audits/third-party-facades.js | description": {"message": "Für einen Teil des eingebetteten Codes von Drittanbietern kann Lazy Loading verwendet werden. Du kannst ihn durch eine Fassade ersetzen, bis er benötigt wird. [Informationen zum Zurückstellen von Drittanbietern mit einer Fassade.](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternative Fassade verfügbar}other{# alternative Fassaden verfügbar}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "<PERSON>ür einige Ressourcen von Drittanbietern kann ein Lazy Load mit einer Fassade ausgeführt werden"}, "core/audits/third-party-facades.js | title": {"message": "Lazy <PERSON><PERSON> von Ressour<PERSON>n von Drittanbietern mit Fassaden"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Dritt<PERSON><PERSON><PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "Code von Drittanbietern kann die Ladegeschwindigkeit erheblich beeinträchtigen. Beschränke die Zahl redundanter Drittanbieter und versuche, solchen Code erst zu laden, nachdem die Seite vollständig geladen wurde. [<PERSON><PERSON> da<PERSON>, wie sich die Auswirkungen von Drittanbietercode minimieren lassen.](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON> von Drittan<PERSON>tern hat den Hauptthread {timeInMs, number, milliseconds} ms lang blockiert"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Die Auswirkungen von Drittanbieter-Code minimieren"}, "core/audits/third-party-summary.js | title": {"message": "Drittanbieternutzung minimieren"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Messung"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Messwert"}, "core/audits/timing-budget.js | description": {"message": "Mit einem Zeitbudget lässt sich die Leistung deiner Website besser im Auge behalten. Leistungsstarke Websites werden schnell geladen und reagieren umgehend auf Nutzereingaben. [Weitere Informationen zu Leistungsbudgets.](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Zeitbudget"}, "core/audits/unsized-images.js | description": {"message": "Lege eine explizite Breite und Höhe für Bildelemente fest, um Layoutverschiebungen zu reduzieren und den CLS-Wert zu verbessern. [Informationen zum Festlegen von Bildabmessungen](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "B<PERSON><PERSON>e haben keine explizite `width` und `height`"}, "core/audits/unsized-images.js | title": {"message": "Bildelemente haben eine explizite `width` und `height`"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Du kannst die User Timing API in deine App einbinden. Damit lässt sich die Leistung der App während wichtiger Nutzerinteraktionen in der Praxis messen. [Weitere Informationen zu User Timing-Markierungen.](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 Nutzertiming}other{# Nutzertimings}}"}, "core/audits/user-timings.js | title": {"message": "Markierungen und Messungen für das Nutzertiming"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "<PERSON><PERSON>r {security<PERSON><PERSON><PERSON>} wurde ein `<link rel=preconnect>` g<PERSON><PERSON><PERSON>, das jedoch nicht vom Browser verwendet wurde. <PERSON>eh nach, ob das `crossorigin`-Attribut richtig verwendet wird."}, "core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON> auf Ressourcen wie `preconnect` oder `dns-prefetch` lassen sich frühzeitig Verbindungen zu wichtigen Drittanbieterursprüngen herstellen. [Informationen zum Vorverbinden mit erforderlichen Ursprüngen.](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Vorverbindung zu erforderlichen Ursprüngen aufbauen"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "<PERSON>s wurden mehr als zwei `<link rel=preconnect>`-Links gefunden. Diese Links sollten aber sparsam und nur zu den wichtigsten Ursprüngen gesetzt werden."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "<PERSON><PERSON>r {security<PERSON><PERSON><PERSON>} wurde ein `<link rel=preconnect>` g<PERSON><PERSON><PERSON>, das jedoch nicht vom Browser verwendet wurde. E<PERSON>elle nur ein `preconnect` zu wichtigen Quellen, die von der Webseite auf jeden Fall abgefragt werden."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Für {preloadURL} wurde ein vorab geladenes `<link>` g<PERSON><PERSON><PERSON>, das jedoch nicht vom Browser verwendet wurde. <PERSON><PERSON> nach, ob das `crossorigin`-Attribut richtig verwendet wird."}, "core/audits/uses-rel-preload.js | description": {"message": "Mit `<link rel=preload>` kannst du das Abrufen von Ressourcen priorisieren, die derzeit beim Seitenaufbau erst später angefordert werden. [Informationen zum Vorabladen wichtiger Anfragen.](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Wichtige Anforderungen vorab laden"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Zuordnungs-URL"}, "core/audits/valid-source-maps.js | description": {"message": "Quellzuordnungen übersetzen reduzierten Code in den ursprünglichen Quellcode. Dies hilft Entwicklern beim Debugging in der Produktionsphase. Zusätzlich kann Lighthouse weitere Informationen liefern. Wir empfehlen, Quellzuordnungen bereitzustellen, um diese Vorteile zu nutzen. [Weitere Informationen zu Quellzuordnungen.](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Fehlende Quellzuordnungen für große eigene JavaScript-Dateien"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "In großer JavaScript-<PERSON><PERSON> fehlt eine Quellzuordnung"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Warnung: in `.sourcesContent` fehlt 1 Element}other{Warnung: in `.sourcesContent` fehlen # Elemente}}"}, "core/audits/valid-source-maps.js | title": {"message": "Seite hat gültige Quellzuordnungen"}, "core/audits/viewport.js | description": {"message": "Ein `<meta name=\"viewport\">` optimiert deine App für Bildschirmgrößen von Mobilgeräten und verhindert darüber hinaus [300-Millisekunden-Verzögerungen bei Nutzereingaben](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Weitere Informationen zur Verwendung des Darstellungsbereich-Meta-Tags.](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Kein `<meta name=\"viewport\">`-Tag gefunden"}, "core/audits/viewport.js | failureTitle": {"message": "Hat kein `<meta name=\"viewport\">`-Tag mit `width` oder `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Hat ein `<meta name=\"viewport\">`-Tag mit `width` oder `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Dies sind die Aufgaben zur Blockierung des Threads, die beim Erfassen des Messwerts „Interaction to Next Paint“ ausgeführt werden. [Weitere Informationen zum Messwert „Interaction to Next Paint“.](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms für {interactionType}-<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Ereignisziel"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Aufwand während der Hauptinteraktion minimieren"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Eingabeverzögerung"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Verzögerung bei der Präsentation"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Verarbeitungszeit"}, "core/audits/work-during-interaction.js | title": {"message": "Minimiert den Aufwand während der Hauptinteraktion"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON> dieser Möglichkeiten kannst du die Nutzung von ARIA in deiner Anwendung verbessern, wovon <PERSON><PERSON><PERSON> von Hilfstechnologien wie Screenreadern unter Umständen profitieren."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Hier findest du Möglichkeiten, um Alternativen für Audio- und Videoinhalte anzubieten. Dies kann die Nutzung für Personen mit eingeschränktem Hör- und Sehvermögen verbessern."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio und Video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Hier findest du häufig genutzte Best Practices für Barrierefreiheit."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Best Practices"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Mit diesen Prüfungen erfährst du, [wie du die Barrierefreiheit deiner Web-App verbesserst](https://developer.chrome.com/docs/lighthouse/accessibility/). Nur bestimmte Probleme mit der Barrierefreiheit können durch automatisierte Tests erkannt werden. Deshalb ist es empfehlenswert, zusätzlich manuelle Tests durchzuführen."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Diese Prüfungen sind für Bereiche vorgesehen, für die automatische Testtools nicht geeignet sind. Weitere Informationen findest du in unserem Leitfaden zur [Durchführung einer Prüfung auf Barrierefreiheit](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Barrierefreiheit"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON> dieser Möglichkeiten kannst du die Lesbarkeit deiner Inhalte verbessern."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Damit kannst du da<PERSON><PERSON>r sorgen, dass deine Inhalte in verschiedenen Sprachen besser verstanden werden."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisierung und Lokalisierung"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON> dieser Möglichkeiten kannst du die Semantik der Steuerelemente deiner Anwendung verbessern. Dies kommt Nutzern von Hilfstechnologien wie Screenreadern zugute."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Namen und Labels"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Hier findest du Möglichkeiten, die Tastaturnavigation in deiner App zu verbessern."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigation"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Hier findest du Möglichkeiten, um das Lesen von Daten in Tabellen oder Listen mit Hilfstechnologie wie Screenreadern zu verbessern."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabellen und Listen"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Browserkompatibilität"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Best Practices"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Allgemein"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Vertrauen und Sicherheit"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Nutzererfahrung"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> von Leistungsbudgets werden Standards für die Leistung deiner Website definiert."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budgets"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Weitere Informationen zur Leistung deiner App findest du hier. Diese <PERSON>n haben keinen [direkten Einfluss](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) auf die Leistungsbewertung."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnose"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Der wichtigste Faktor bei der Leistung ist, wie schnell Pixel auf dem Bildschirm gezeichnet werden. Wichtige Messwerte: \"First Contentful Paint\", \"First Meaningful Paint\""}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Verbesserungen beim Zeichnen der ersten Inhalte"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> dieser Empfehlungen lässt sich die Ladezeit deiner Seite möglicherweise verkürzen. Sie haben keinen [direkten Einfluss](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) auf die Leistungsbewertung."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Empfehlungen"}, "core/config/default-config.js | metricGroupTitle": {"message": "Messwerte"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Hier kannst du die Ladezeiten verkürzen, damit die Seite so schnell wie möglich reagiert und Einsatzbereit ist. Wichtige Messwerte: „Zeit bis Interaktivität“, „Geschwindigkeitsindex“"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Allgemeine Verbesserungen"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Le<PERSON><PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Diese Prüfungen dienen dazu, die einzelnen Aspekte einer progressiven Web-App zu überprüfen. [Weitere Informationen dazu, was eine gute progressive Web-App ausmacht](https://web.dev/pwa-checklist/)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Diese Prüfungen sind laut der grundlegenden [PWA-Checkliste](https://web.dev/pwa-checklist/) <PERSON><PERSON><PERSON><PERSON>, werden von Lighthouse jedoch nicht automatisch durchgeführt. Sie haben zwar keine Auswirkung auf deine Leistungsbewertung, aber es ist wichtig, sie manuell durchzuführen."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Installierbar"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA-optimiert"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Mit diesen Prüfungen ist gewährleistet, dass bei deiner Seite grundlegende Tipps für die Suchmaschinenoptimierung berücksichtigt werden. Es gibt viele verschiedene Faktoren, die Lighthouse hier nicht bewertet und die sich auf das Such-Ranking deiner Seite auswirken können, einschließlich der [Core Web Vitals](https://web.dev/learn-core-web-vitals/)-Performance. [Weitere Informationen zu Google Search Essentials](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Du kannst diese zusätzlichen Validierungen für deine Website ausführen, um weitere Best Practices für die SEO zu prüfen."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatiere deinen HTML-Code so, dass Crawler den Inhalt deiner App besser verstehen."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Best Practices für Inhalte"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Damit deine Website in den Suchergebnissen angezeigt werden kann, benötigen Crawler Zugriff auf deine App."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawling und Indexierung"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON>, dass deine Seiten für Mobilgeräte optimiert sind, damit <PERSON> problemlos Inhalte lesen können, ohne mit den Fingern heranzoomen zu müssen. [Weitere Informationen zur Optimierung von Seiten für Mobilgeräte](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON>r Mobilgeräte optimiert"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Das getestete Gerät hat eine langsamere CPU, als für Lighthouse erforderlich ist. Dies kann sich negativ auf die Leistungsbewertung auswirken. [Hier findest du weitere Informationen zur Kalibrierung eines geeigneten CPU-Verlangsamungsmultiplikators.](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Die Seite lädt möglicherweise nicht wie erwartet, weil deine Test-URL ({requested}) auf {final} weitergeleitet wurde. Versuche, die zweite URL direkt zu testen."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Diese Seite wurde nicht innerhalb der Zeitbegrenzung geladen. Da<PERSON> sind die Ergebnisse möglicherweise unvollständig."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Zeitüberschreitung beim Leeren des Browsercache. Prüfe diese Seite noch einmal und melde einen Fehler, falls das Problem weiterhin besteht."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Gespeicherte Daten haben möglicherweise die Ladeleistung an diesem Ort beeinflusst: {locations}. Teste diese Seite in einem Inkognitofenster, um zu verhindern, dass sich diese Ressourcen auf deine Ergebnisse auswirken.}other{Gespeicherte Daten haben möglicherweise die Ladeleistungen an diesen Orten beeinflusst: {locations}. Teste diese Seite in einem Inkognitofenster, um zu verhindern, dass sich diese Ressourcen auf deine Ergebnisse auswirken.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Zeitüberschreitung beim Löschen der Ursprungsdaten. Prüfe diese Seite noch einmal und melde einen <PERSON>hler, falls das Problem weiterhin besteht."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON><PERSON>, die über GET-Anfragen geladen werden, k<PERSON><PERSON><PERSON> den Back-Forward-<PERSON><PERSON> verwenden."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Nur Seiten mit dem Statuscode 2XX können im Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome hat einen Versuch erkannt, JavaScript auszuführen, während die Seite im Cache gespeichert war."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON><PERSON>, für die ein App-Banner angefragt wurde, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Der Back-Forward-<PERSON><PERSON> wurde durch Flags deaktiviert. Rufe chrome://flags/#back-forward-cache auf, um die Funktion lokal für dieses Gerät zu aktivieren."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Der Back-Forward-<PERSON><PERSON> wurde über die Befehlszeile deaktiviert."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Der Back-Forward-<PERSON><PERSON> wurde aufgrund fehlenden Arbeitsspeichers deaktiviert."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Der Back-Forward-<PERSON><PERSON> wird vom Delegaten nicht unterstützt."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Der Back-Forward-<PERSON><PERSON> ist für Pre-Rendering deaktiviert."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Die Seite konnte nicht im Cache gespeichert werden, da sie eine BroadcastChannel-Instanz mit registrierten Listenern hat."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Seiten mit „cache-control:no-store“ im Header können nicht im Back-Forward-Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Der Cache wurde absichtlich gelöscht."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Die Seite wurde aus dem Cache entfernt, damit eine andere Seite im Cache gespeichert werden kann."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "<PERSON><PERSON><PERSON>, die Plug-ins enthalten, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "<PERSON><PERSON><PERSON>, auf denen die FileChooser API genutzt wird, k<PERSON><PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "<PERSON><PERSON><PERSON>, auf denen die File System Access API genutzt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON><PERSON>, auf denen der Media Device Dispatcher genutzt wird, k<PERSON><PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Ein Mediaplayer wurde verwendet, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "<PERSON><PERSON><PERSON>, auf denen die MediaSession API genutzt wird und ein Wiedergabestatus festgelegt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "<PERSON><PERSON><PERSON>, auf denen die MediaSession API genutzt wird und Aktions-Handler festgelegt werden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Der Back-Forward-<PERSON><PERSON> wurde aufgrund des Screenreaders deaktiviert."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON>, auf denen SecurityHandler genutzt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "<PERSON><PERSON><PERSON>, auf denen die Serial API genutzt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON><PERSON>, auf denen die WebAuthentication API genutzt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "<PERSON><PERSON><PERSON>, auf denen die WebBluetooth API genutzt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "<PERSON><PERSON><PERSON>, auf denen die WebUSB API genutzt wird, kö<PERSON><PERSON> den Back-Forward-<PERSON><PERSON> nicht verwenden."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON><PERSON>, auf denen ein Dedicated Worker oder ein Worklet genutzt wird, k<PERSON><PERSON><PERSON> den Back-Forward-<PERSON>ache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Das Dokument wurde verlassen, bevor es vollständig geladen werden konnte."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Ein App-Banner wurde angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Der Passwortmanager von Chrome wurde angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Die DOM-Zusammenfassung war in Bearbeitung, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer wurde angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Der Back-Forward-<PERSON><PERSON> wurde aufgrund der Nutzung der Messaging API durch Erweiterungen deaktiviert."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Erweiterungen mit dauerhafter Verbindung müssen diese trennen, bevor sie im Back-Forward-<PERSON>ache gespeichert werden können."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Erweiterungen mit dauerhafter Verbindung haben versucht, <PERSON><PERSON><PERSON><PERSON> an Frames im Back-Forward-<PERSON><PERSON> zu senden."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Der Back-Forward-<PERSON><PERSON> ist aufgrund von Erweiterungen deaktiviert."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Ein modales Dialogfeld, z. B. für eine erneute Formularübermittlung oder ein HTTP-Passwort, wurde angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Die Offlineseite wurde angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Die Leiste zum Eingreifen bei unzureichendem Arbeitsspeicher wurde angezeigt, als die Se<PERSON> verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Berechtigungsanfragen waren in Bearbeitung, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Ein Pop-up-Blocker wurde angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Safe Browsing-Details wurden angezeigt, als die Seite verlassen wurde."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Diese Seite wur<PERSON> von <PERSON> Browsing als missbräuchlich eingestuft. Pop-ups wurden blockiert."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Ein Service Worker wurde aktiviert, während die Seite im Back-Forward-Cache gespeichert war."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Der Back-Forward-<PERSON><PERSON> wurde aufgrund eines Dokumentfehlers deaktiviert."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON>, auf denen FencedFrames genutzt wird, können nicht im bfcache gespeichert werden."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Die Seite wurde aus dem Cache entfernt, damit eine andere Seite im Cache gespeichert werden kann."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Seiten mit Zugriff auf Medienstreams können den Back-Forward-<PERSON>ache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON><PERSON>, auf denen Portale genutzt werden, k<PERSON><PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON><PERSON>, auf denen IdleManager genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Seiten mit einer offenen IndexedDB-Verbindung können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Es wurden ungültige APIs verwendet."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON><PERSON>, bei denen durch Erweiterungen JavaScript eingeschleust wird, <PERSON><PERSON><PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON>, bei denen durch Erweiterungen Stylesheetinformationen eingeschleust werden, kö<PERSON><PERSON> den Back-Forward-<PERSON>ache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Der Back-Forward-<PERSON><PERSON> wurde aufgrund einer Keepalive-An<PERSON><PERSON> deak<PERSON>."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON><PERSON>, auf denen die Tastatursperre genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | loading": {"message": "Die Seite wurde verlassen, bevor sie vollständig geladen werden konnte."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> „cache-control:no-cache“ enthält, können nicht im Back-Forward-<PERSON>ache gespeichert werden."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> „cache-control:no-store“ enthält, können nicht im Back-Forward-Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Die Navigation wurde abgebrochen, bevor die Seite aus dem Back-Forward-<PERSON><PERSON> wiederhergestellt werden konnte."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Diese Seite wurde aus dem Cache entfernt, weil eine aktive Netzwerkverbindung zu viele Daten empfangen hat. Chrome schränkt die Menge der Daten ein, die eine Seite empfangen darf, während sie im Cache gespeichert ist."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Seiten mit einer laufenden „fetch()“- oder XHR-Anfrage können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Die Seite wurde aus dem Back-Forward-<PERSON><PERSON> entfernt, weil eine aktive Netzwerkanfrage eine Weiterleitung enthielt."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Die Seite wurde aus dem Cache entfernt, weil eine Netzwerkverbindung zu lange offen war. Chrome schränkt ein, wie lange die Seite Daten empfangen darf, während sie im Cache gespeichert ist."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Seiten ohne gültigen Antwortheader können nicht im Back-Forward-Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Außerhalb des Hauptframes ist ein Navigationsvorgang aufgetreten."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Seiten mit laufenden indexierten DB-Transaktionen können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Seiten mit einer laufenden Netzwerkanfrage können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Seiten mit einer laufenden Netzwerkabrufanfrage können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Seiten mit einer laufenden Netzwerkanfrage können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Seiten mit einer laufenden XHR-Netzwerkanfrage können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON><PERSON>, auf denen PaymentManager genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON><PERSON>, auf denen „Picture-in-Picture“ genutzt wird, könne<PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON><PERSON>, auf denen Portale genutzt werden, k<PERSON><PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON><PERSON><PERSON>, die die Benutzeroberfläche zum Drucken anzeigen, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Die Seite wurde mithilfe von „`window.open()`“ ge<PERSON><PERSON><PERSON> und ein anderer Tab verweist darauf oder die Seite hat ein Fenster geöffnet."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Der Renderingvorgang für die Seite im Back-Forward-Cache ist abgestürzt."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Der Renderingvorgang für die Seite im Back-Forward-<PERSON><PERSON> wurde beendet."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON>, für die Berechtigungen zur Audioaufnahme angefragt wurden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON><PERSON>, für die Sensorberechtigungen angefragt wurden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON><PERSON>, für die Berechtigungen für die Hintergrundsynchronisierung oder den Hintergrundabruf angefragt wurden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON>, für die MIDI-Berechtigungen angefragt wurden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON>, für die Berechtigungen zum Senden von Benachrichtigungen angefragt wurden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON>, für die Speicherzugriff angefragt wurde, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON>, für die Berechtigungen zur Videoaufnahme angefragt wurden, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Nur Seiten mit dem URL-Schema HTTP oder HTTPS können im Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Die Seite wurde von einem Service Worker beansprucht, während sie im Back-Forward-Cache gespeichert war."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Ein Service Worker hat versucht, `MessageEvent` an die Seite im Back-Forward-<PERSON>ache zu senden."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Die Funktion „ServiceWorkers“ war nicht registriert, während die Seite im Back-Forward-Cache gespeichert war."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Die Seite wurde aufgrund einer Service-Worker-Aktivierung aus dem Back-Forward-Cache entfernt."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome wurde neu gestartet und hat die Einträge im Back-Forward-<PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON><PERSON>, auf denen SharedWorker genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON><PERSON>, auf denen SpeechRecognizer genutzt wird, k<PERSON><PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON><PERSON>, auf denen SpeechSynthesis genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Ein iFrame auf der Seite hat einen Navigationsvorgang gestartet, der nicht abgeschlossen wurde."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, deren Unterressource „cache-control:no-cache“ enthält, können nicht im Back-Forward-Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON>, deren Unterressource „cache-control:no-store“ enthält, können nicht im Back-Forward-Cache gespeichert werden."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Die Seite hat die maximale Speicherdauer für den Back-Forward-Cache überschritten und ist abgelaufen."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Beim S<PERSON>ichern der Seite im Back-Forward-Cache ist eine Zeitüberschreitung aufgetreten (vermutlich, weil Pagehide-Handler zu lange ausgeführt wurden)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Im <PERSON>uptframe dieser Seite befindet sich ein Unload-Handler."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "In einem Subframe dieser Seite befindet sich ein Unload-Handler."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Der Browser hat den Überschreibungsheader für den User-Agent geändert."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Seiten mit der Berechtigung zum Aufnehmen von Videos oder Audio können den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "<PERSON><PERSON><PERSON>, auf denen WebDatabase genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webHID": {"message": "<PERSON><PERSON><PERSON>, auf denen WebHID genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON><PERSON><PERSON>, auf denen WebLocks genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "<PERSON><PERSON><PERSON>, auf denen WebNfc genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "<PERSON><PERSON><PERSON>, auf denen WebOTPService genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Seiten mit WebRTC können nicht im Back-Forward-<PERSON>ache gespeichert werden."}, "core/lib/bf-cache-strings.js | webShare": {"message": "<PERSON><PERSON><PERSON>, auf denen WebShare genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Seiten mit WebSocket können nicht im Back-Forward-<PERSON>ache gespeichert werden."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Seiten mit WebTransport können nicht im Back-Forward-<PERSON>ache gespeichert werden."}, "core/lib/bf-cache-strings.js | webXR": {"message": "<PERSON><PERSON><PERSON>, auf denen WebXR genutzt wird, kö<PERSON><PERSON> den Back-Forward-Cache aktuell nicht verwenden."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Du kannst „https:“- und „http“:-URL-<PERSON><PERSON><PERSON> (wird von Browsern ignoriert, die „strict-dynamic“ unterstützen), um mit älteren Browsern kompatibel zu sein."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "„disown-opener“ ist seit CSP3 veraltet. Verwende stattdessen den Header „Cross-Origin-Opener-Policy“."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "„referrer“ ist seit CSP2 veraltet. Verwende stattdessen den Header „Referrer-Policy“."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "„reflected-xss“ ist seit CSP2 veraltet. Verwende stattdessen den Header „X-XSS-Protection“."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Durch eingeschleuste <base>-<PERSON><PERSON> kann „base-uri“ für alle relativen URLs (beispielsweise für Skripts) auf eine von Angreifern kontrollierte Domain festgelegt werden. Du kannst „base-uri“ auf „none“ oder „self“ setzen."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "<PERSON><PERSON> „object-src“ fehlt, können Plug-ins eingeschleust werden, die unsichere Skripts ausführen. Du solltest daher „object-src“ auf „none“ setzen."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Die Direktive „script-src“ fehlt. Dies kann die Ausführung unsicherer Skripts ermöglichen."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Hast du das Semikolon vergessen? „{keyword}“ ist eine Direktive, kein Keyword."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces sollten das base64-<PERSON><PERSON><PERSON> nutzen."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces sollten aus mindestens 8 Zeichen bestehen."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Vermeide die Verwendung von einfachen URL-Schemas ({keyword}) in dieser Richtlinie. Durch einfache URL-Schemas können Skripts aus einer unsicheren Domain abgerufen werden."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Vermeide die Verwendung von einfachen Platzhaltern ({keyword}) in dieser Richtlinie. Durch einfache Platzhalter können Skripts aus einer unsicheren Domain abgerufen werden."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Das Ziel für die Berichterstellung wird nur über die Direktive „report-to“ konfiguriert. Diese Direktive unterstützen nur Chromium-basierte Browser. Deshalb empfehlen wir, auch eine „report-uri“-Direktive zu verwenden."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Keine CSP konfiguriert ein Ziel für die Berichterstellung. Dadurch ist es schwierig, die CSP langfristig zu verwalten und Schwachstellen im Blick zu behalten."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Host-Zulassungslisten können häufig umgangen werden. Falls notwendig, kannst du CSP-Nonces oder -Hashes mit „strict-dynamic“ verwenden."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Unbekannte CSP-Direktive."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "„{keyword}“ ist ein ungültiges Keyword."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "„unsafe-inline“ lässt die Ausführung unsicherer In-Page-Skripts und Event-Handler zu. Du kannst CSP-Nonces oder -Hashes verwenden, um Skripts einzeln zuzulassen."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Du kannst „unsafe-inline“ hinzuf<PERSON>gen (wird von Browsern ignoriert, die Nonces/Hashes unterstützen), um mit älteren Browsern kompatibel zu sein."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Die Autorisierung wird vom Platzhaltersymbol (*) bei der `Access-Control-Allow-Headers`-Verarbeitung durch CORS nicht abgedeckt."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Ressourcenanfragen mit entfernten Leerzeichen (`(n|r|t)`) und Kleiner-als-Zeichen (`<`) in den URLs werden blockiert. Du musst Zeilenumbrüche entfernen und Kleiner-als-Zeichen an Stellen wie Elementattributwerten codieren, um diese Ressourcen laden zu können."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` wurde verworfen. Bitte verwende stattdessen die standardisierte API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` wurde verworfen. Bitte verwende stattdessen die standardisierte API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` wurde verworfen. Bitte verwende stattdessen die standardisierte API: `nextHopProtocol` in Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "<PERSON><PERSON>, die ein `(0|r|n)`-<PERSON><PERSON><PERSON> enthalten, werden abgelehnt und nicht abgeschnitten."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Das Abschwächen der Richtlinie für denselben Ursprung durch das Festlegen von `document.domain` wurde verworfen und wird standardmäßig deaktiviert. Diese Warnung zum Status als verworfen gilt für einen ursprungsübergreifenden Zugriff, der durch das Festlegen von `document.domain` aktiviert wurde."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "<PERSON> Auslösen von {PH1} über ursprungsübergreifende iFrames wurde verworfen und wird in Zukunft entfernt."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Zum Deaktivieren der standardmäßigen Cast-Integration sollte statt der `-internal-media-controls-overlay-cast-button`-Auswahl das Attribut `disableRemotePlayback` verwendet werden."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} wurde verworfen. Bitte verwende stattdessen {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Dies ist ein Beispiel für eine übersetzte Meldung zu einem Problem aufgrund einer eingestellten Funktion."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Das Abschwächen der Richtlinie für denselben Ursprung durch das Festlegen von `document.domain` wurde verworfen und wird standardmäßig deaktiviert. Wenn du diese Funktion weiter verwenden möchtest, musst du das an Ursprünge gebundene Agent-Clustering deaktivieren. Dazu sendest du einen `Origin-Agent-Cluster: ?0`-Header zusammen mit der HTTP-Antwort für das Dokument und Frames. Weitere Informationen findest du unter https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` wurde verworfen und wird entfernt. Bitte verwende stattdessen `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Der `Expect-CT`-Header wurde verworfen und wird entfernt. In Chrome müssen alle nach dem 30. April 2018 ausgestellten öffentlich vertrauenswürdigen Zertifikate die Certificate Transparency-Anforderungen erfüllen."}, "core/lib/deprecations-strings.js | feature": {"message": "Weitere Details findest du auf der Funktionsstatus-Seite."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` und `watchPosition()` funktionieren nicht mehr bei unsicheren Ursprüngen. Wenn du diese Funktion nutzen möchtest, solltest du deine App auf einen sicheren Ursprung umstellen, z. B. HTTPS. Weitere Informationen findest du unter https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` und `watchPosition()` für unsichere Ursprünge wurden verworfen. Wenn du diese Funktion nutzen möchtest, solltest du deine App auf einen sicheren Ursprung umstellen, z. B. HTTPS. Weitere Informationen findest du unter https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` funktioniert nicht mehr bei unsicheren Ursprüngen. Wenn du diese Funktion nutzen möchtest, solltest du deine App auf einen sicheren Ursprung umstellen, z. B. HTTPS. Weitere Informationen findest du unter https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` wurde verworfen. Bitte verwende stattdessen `RTCPeerConnectionIceErrorEvent.address` oder `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Der Händlerursprung und beliebige Daten aus dem Service-Worker-Ereignis `canmakepayment` wurden verworfen und werden entfernt: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Die Website hat eine Unterressource von einem Netzwerk angefordert, auf das sie nur aufgrund der privilegierten Netzwerkposition ihrer Nutzer zugreifen konnte. Durch solche Anfragen werden nicht öffentliche Geräte und Server im Internet preisgegeben, was das Risiko von CSRF-Angriffen (Cross-Site Request Forgery) und/oder Datenlecks erhöht. Um diesen Risiken entgegenzuwirken, werden Anfragen an nicht öffentliche Unterressourcen, die über nicht sichere Kontexte initiiert werden, in Chrome verworfen und in Zukunft blockiert."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS kann nur von `file:`-U<PERSON><PERSON> geladen werden, wenn sie die Dateiendung `.css` haben."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Der Einsatz von `SourceBuffer.abort()` zum Abbrechen der asynchronen Bereichsentfernung von `remove()` wurde aufgrund einer Änderung der Spezifikation verworfen. Die Unterstützung wird in Zukunft eingestellt. Wir empfehlen, stattdessen das Ereignis `updateend` zu beobachten. `abort()` ist nur dazu vorgesehen, ein asynchrones Anfügen von Medien abzubrechen oder den Zustand des Parsers zurückzusetzen."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Das Festlegen von `MediaSource.duration` auf einen Wert unter dem höchsten Präsentationszeitstempel beliebiger zwischengespeicherter codierter Frames wurde aufgrund einer Änderung der Spezifikation verworfen. Die Unterstützung für die implizite Entfernung abgeschnittener zwischengespeicherter Medien wird in Zukunft entfernt. Führe stattdessen `remove(newDuration, oldDuration)` für alle `sourceBuffers`, für die `newDuration < oldDuration` gilt, explizit aus."}, "core/lib/deprecations-strings.js | milestone": {"message": "Diese Änderung wird im Rahmen von Mei<PERSON>stein {milestone} angewandt."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI fordert eine Berechtigung zur Nutzung an, auch dann, wenn die Systemexklusivität (SysEx) nicht in `MIDIOptions` angegeben ist."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Die Notification API darf nicht mehr über unsichere Ursprünge verwendet werden. Wir empfehlen dir, deine App auf einen sicheren Ursprung umzustellen, z. B. HTTPS. Weitere Informationen findest du unter https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Die Berechtigung für die Notification API darf nicht mehr über einen ursprungsübergreifenden iFrame angefordert werden. Stattdessen sollte sie über einen Frame auf höchster Ebene angefordert oder ein neues Fenster geöffnet werden."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Dein Partner verwendet eine veraltete (D)TLS-Version. Bitte wende dich an den Partner, um dieses Problem zu beheben."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL in unsicheren Kontexten wurde verworfen und wird bald entfernt. Bitte verwende Web Storage oder IndexedDB."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Wenn du „`overflow: visible`“ für „img“-, „video“- und „canvas“-Tags angibst, können visuelle Inhalte dadurch außerhalb der Elementgrenzen gerendert werden. Weitere Informationen findest du unter https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "„`paymentManager.instruments`“ wurde verworfen. Verwende für Zahlungs-Handler stattdessen die JIT-Installation (Just In Time)."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "<PERSON><PERSON> <PERSON> Au<PERSON><PERSON> „`PaymentRequest`“ wird die Anweisung „`connect-src`“ der Content Security Policy (CSP) umgangen. Diese Umgehung wurde verworfen. Füge die Kennung der Zahlungsmethode aus der `PaymentRequest` API (im Feld „`supportedMethods`“) in die CSP-Anweisung „`connect-src`“ ein."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` wurde verworfen. Bitte verwende stattdessen die standardisierte API`navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` mit einem übergeordneten `<picture>`-Element ist ungültig und wird deshalb ignoriert. Bitte verwende stattdessen `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` wurde verworfen. Bitte verwende stattdessen die standardisierte API`navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Anforderungen von Unterressourcen, deren URLs eingebettete Anmeldedaten enthalten (z. B. `**********************/`), werden blockiert."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Die Einschränkung `DtlsSrtpKeyAgreement` wird entfernt. Du hast den Wert `false` für diese Einschränkung angegeben, was als Versuch interpretiert wird, die entfernte `SDES key negotiation`-<PERSON><PERSON> zu verwenden. Diese Funktion wird entfernt. Verwende stattdessen einen Dienst, der `DTLS key negotiation` unterstützt."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Die Einschränkung `DtlsSrtpKeyAgreement` wird entfernt. Du hast `true` für diese Einschränkung angegeben. Das hatte keine Auswirkungen, du kannst diese Einschränkung aber für mehr Übersichtlichkeit entfernen."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` erkannt. Dieser Dialekt des `Session Description Protocol` wird nicht mehr unterstützt. Bitte verwende stattdessen das `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, die beim Konstruieren einer `RTCPeerConnection` mit `{sdpSemantics:plan-b}` verwendet wird, ist eine alte, nicht standardisierte Version des `Session Description Protocol`, die dauerhaft von der Webplattform gelöscht wurde. Für die Entwicklung mit `IS_FUCHSIA` ist sie weiterhin verfügbar, wir haben jedoch vor, sie so schnell wie möglich zu löschen. Aus diesem Grund solltest du sie nicht mehr einsetzen. Weitere Informationen zum Status findest du unter https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Die Option `rtcpMuxPolicy` wurde verworfen und wird entfernt."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "<PERSON><PERSON><PERSON> `SharedArrayBuffer` muss die ursprungsübergreifende Isolierung aktiviert werden. Weitere Informationen findest du unter https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` ohne Nutzeraktivierung wurde verworfen und wird entfernt."}, "core/lib/deprecations-strings.js | title": {"message": "Verworfene Funktion verwendet"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "<PERSON><PERSON>r <PERSON>weiterungen muss die ursprungsübergreifende Isolierung aktiviert werden, um `SharedArrayBuffer` weiter zu nutzen. Weitere Informationen findest du unter https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} ist anbieterspezifisch. Bitte verwende stattdessen die standardmäßige Methode {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 wird <PERSON>-JSO<PERSON> in einer `XMLHttpRequest` nicht unterstützt"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchrone `XMLHttpRequest`s im Hauptthread wurden verworfen, da sie sich nachteilig auf die Endnutzererfahrung auswirken. Weitere Informationen findest du unter https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` wurde verworfen. Bitte verwende stattdessen `isSessionSupported()` und prüfe den aufgelösten booleschen Wert."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Dauer der Blockierung des Hauptthreads"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Cache-TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Beschreibung"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Fehlerhafte Elemente"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Position"}, "core/lib/i18n/i18n.js | columnName": {"message": "Name"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON> dem <PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Anfragen"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Größe der Ressource"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Ressourcentyp"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Größe"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Begin<PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Z<PERSON>au<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Übertragungsgröße"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Mögliche Einsparungen"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Mögliche Einsparungen"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Mögliche Einsparung von {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 Element gefunden}other{# Elemente gefunden}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Mögliche Einsparung von {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Bild"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Hoch"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maximaler potenzieller First Input Delay"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Medien"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Sonstige"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON> Ressourcen"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Dritt<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Gesamt"}, "core/lib/lh-error.js | badTraceRecording": {"message": "<PERSON>im Aufzeichnen des Trace über deinen Seitenaufbau ist ein Problem aufgetreten. Bitte führe Lighthouse noch einmal aus. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Zeitüberschreitung beim Warten auf die ursprüngliche Verbindung zum Debugger-Protokoll."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "<PERSON>im Se<PERSON>naufbau wurden von Chrome keine Screenshots erfasst. Acht<PERSON> da<PERSON>, dass auf der Seite Inhalte sichtbar sind, und versuche dann, Lighthouse noch einmal auszuführen. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Die angegebene Domain konnte von den DNS-Servern nicht aufgelöst werden."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON> {artifactName}-<PERSON><PERSON>er ist ein Fehler aufgetreten: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Ein interner Chrome-Fehler ist aufgetreten. Starte Chrome neu und versuche anschließend, Lighthouse noch einmal auszuführen."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Erforderlicher {artifact<PERSON>ame}-<PERSON><PERSON><PERSON> wurde nicht ausgeführt."}, "core/lib/lh-error.js | noFcp": {"message": "Die Seite hat keine Inhalte gezeichnet. <PERSON>cht<PERSON> da<PERSON>, dass das Browserfenster während des Ladens im Vordergrund bleibt und versuche es noch einmal. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Auf der Seite wurden keine Inhalte angezeigt, die als Largest Contentful Paint (LCP) infrage kommen. Prüfe, ob die Seite ein gültiges LCP-Element enthält, und versuch es dann noch einmal. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Die bereitgestellte Seite ist nicht vom Typ HTML (als MIME-Typ {mimeType} bereitgestellt)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Diese Version von Chrome ist zu alt, um \"{featureName}\" zu unterstützen. Verwende eine neuere Version, um vollständige Ergebnisse anzu<PERSON>hen."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Die von dir angeforderte Seite konnte von Lighthouse nicht zuverlässig geladen werden. Überprüfe, ob du die richtige URL testest und der Server auf alle Anfragen angemessen reagiert."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Die angeforderte URL konnte von Lighthouse nicht zuverlässig geladen werden, weil die Seite nicht mehr reagiert hat."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Die von dir angegebene URL hat kein gültiges Sicherheitszertifikat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome hat den Seitenaufbau mit einem Interstitial verhindert. Überprüfe, ob du die richtige URL testest und der Server auf alle Anfragen angemessen reagiert."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Die von dir angeforderte Seite konnte von Lighthouse nicht zuverlässig geladen werden. Überprüfe, ob du die richtige URL testest und der Server auf alle Anfragen angemessen reagiert. (Details: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Die von dir angeforderte Seite konnte von Lighthouse nicht zuverlässig geladen werden. Überprüfe, ob du die richtige URL testest und der Server auf alle Anfragen angemessen reagiert. (Statuscode: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Das Laden deiner Seite hat zu lange gedauert. Nutze die Tipps im Bericht, um die Seitenladezeit zu verringern, und versuche anschließend noch einmal, Lighthouse auszuführen. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Die maximal zulässige Antwortzeit des DevTools-Protokolls wurde überschritten. (Methode: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Die maximal zulässige Zeit für das Abrufen von Ressourceninhalten wurde überschritten"}, "core/lib/lh-error.js | urlInvalid": {"message": "Die von dir angegebene URL scheint ungültig zu sein."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Der Seiten-MIME-Typ ist XHTML: Lighthouse unterstützt diesen Dokumenttyp nicht explizit"}, "core/user-flow.js | defaultFlowName": {"message": "Aufrufab<PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Navigationsbericht ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Snapshot-Bericht ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Zeitspannenbericht ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Alle Berichte"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Barrierefreiheit"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Best Practices"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Le<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive Web-App"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Computer"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Informationen über den Lighthouse-Bericht zur Aufrufabfolge"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Informationen über Aufrufabfolgen"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Navigationsberichte können für Folgendes verwendet werden:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Snapshot-Berichte können für Folgendes verwendet werden:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Zeitspannenberichte können für Folgendes verwendet werden:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Eine Lighthouse-Leistungsbewertung erhalten."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Messwerte zur Leistung beim Seitenaufbau erfassen, z. B. Largest Contentful Paint oder Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Funktionen progressiver Web-Apps bewerten."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Probleme mit der Barrierefreiheit in Single-Page-Anwendungen oder komplexen Formularen finden."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Best Practices für hinter einer Interaktion versteckte Menüs und UI-Elemente bewerten."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Layoutverschiebungen und JavaScript-Ausführungszeit bei einer Reihe von Interaktionen messen."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Leistungsmöglichkeiten finden, um die Nutzung für langlebige Seiten und Single-Page-Anwendungen zu verbessern."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Größte Wirkung"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informative Prüfung}other{{numInformative} informative Prüfungen}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Seitenaufbau"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Mit Navigationsberichten wird der Aufbau einer einzelnen Seite analysiert, genau wie mit den ursprünglichen Lighthouse-Berichten."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Navigationsbericht"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} Navigationsbericht}other{{numNavigation} Navigationsberichte}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} bestehbare Prüfung}other{{numPassableAudits} bestehbare Prüfungen}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} Prüfung bestanden}other{{numPassed} Prüfungen bestanden}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Gut"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Speichern"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Mit Snapshot-Berichten werden Seiten in einem bestimmten Zustand analysiert, in der Regel nach Nutzerinteraktionen."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Snapshot-Bericht"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} Snapshot-Bericht}other{{numSnapshot} Snapshot-Berichte}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Zusammenfassung"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Nutzerinteraktionen"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "<PERSON>t Zeitspannenberichten wird ein beliebiger Zeitraum analysiert, normalerweise einer, der Nutzerinteraktionen enthält."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Zeitspannenbericht"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} Zeitspannenbericht}other{{numTimespan} Zeitspannenberichte}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse-Bericht zur Aufrufabfolge"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Verwende [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) für animierte Inhalte, um die CPU-Last auf ein Mindestmaß zu reduzieren, wenn die Inhalte nicht zu sehen sind."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Du kannst alle [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-Komponenten in WebP-Formaten darstellen lassen, während du einen geeigneten Ersatz für andere Browser angibst. [Weitere Informationen](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> darauf, [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) für deine Bilder zu verwenden, damit sie automatisch per Lazy Load geladen werden. [Weitere Informationen](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "<PERSON>erwende Tools wie [AMP-Optimierer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer), um [AMP-Layouts serverseitig zu rendern](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "In der [AMP-Dokumentation](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) kannst du nachsehen, ob alle Stile unterstützt werden."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Das Element [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) unterstützt das Attribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) und gibt auf Grundlage der Bildschirmgröße an, welche Bild-Assets verwendet werden sollten. [Weitere Informationen](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Du kannst virtuelles Scrolling mit dem Component Dev Kit (CDK) verwenden, wenn sehr umfangreiche Listen gerendert werden. [Weitere Informationen.](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Verwende die [Codeaufteilung auf Routing-Ebene](https://web.dev/route-level-code-splitting-in-angular/), um die Größe deiner JavaScript-Bundles zu reduzieren. Außerdem kannst du Assets mit dem [Angular-Service-Worker](https://web.dev/precaching-with-the-angular-service-worker/) vorab im Cache speichern lassen."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Wenn du Angular CLI verwendest, sollten Builds im Produktionsmodus erzeugt werden. [Weitere Informationen.](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Wenn du Angular CLI verwendest, musst du Quellzuordnungen in den Produktions-Build aufnehmen, um deine Bundles zu prüfen. [Weitere Informationen](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Lasse Routings vorab laden, um die Bedienung zu beschleunigen. [Weitere Informationen.](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Du kannst das Dienstprogramm `BreakpointObserver` im Component Dev Kit (CDK) verwenden, um Haltepunkte in Bildern zu verwalten. [Weitere Informationen.](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Du hast die Möglichkeit, dein GIF bei einem Dienst hochzuladen, der da<PERSON><PERSON>r sorgt, dass es als HTML5-Video eingebettet werden kann."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Gib \"`@font-display`\" an, wenn du benutzerdefinierte Schriftarten für dein Design definierst."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Du hast die Möglichkeit, [WebP-Bildformate mit einem Convert-Bildstil](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) auf deiner Website zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Du kannst ein [<PERSON><PERSON><PERSON>-Modu<PERSON>](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) installieren, das das Lazy Loading von Bildern erlaubt. Solche Module bieten die Möglichkeit, nicht sichtbare Bilder aufzuschieben, um die Leistung zu verbessern."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Du hast die Möglichkeit, ein Modul zu verwenden, um wichtiges CSS und JavaScript einzubetten. Du kannst Assets auch asynchron über JavaScript laden, beispielsweise mit dem Modul [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Beachte, dass über dieses Modul bereitgestellte Optimierungen dazu führen können, dass deine Website nicht funktioniert. Daher musst du wahrscheinlich Änderungen am Code vornehmen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "So<PERSON>hl Designs, Module als auch Serverspezifikationen tragen zur Serverantwortzeit bei. <PERSON>ers<PERSON>, ein noch weiter optimiertes Design zu finden, wähle ein geeignetes Optimierungsmodul aus und/oder upgrade deinen Server. Deine Hosting-Server sollten PHP-Opcode-Caching und Memory-Caching wie Redis oder Memcached zur Verkürzung der Datenbankabfragezeiten sowie optimierte Anwendungslogik zur schnelleren Bereitstellung von Seiten nutzen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Du hast die Möglichkeit, [responsive Bildstile (Responsive Image Styles)](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) zu verwenden, um die Größe der auf deiner Seite geladenen Bilder zu reduzieren. Wenn du Views verwendest, um mehrere Inhaltselemente auf einer Seite anzuzeigen, kannst du mithilfe von Paginierung die Anzahl der auf einer bestimmten Seite eingeblendeten Inhaltselemente begrenzen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "<PERSON>chte darauf, dass die Option \"Aggregate CSS files\" (CSS-Dateien aggregieren) unter \"Administration\" (Verwalten) > \"Configuration\" (Konfiguration) > \"Development\" (Entwicklung) aktiviert ist. Durch [zusätzliche Module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) kannst du auch erweiterte Aggregierungsoptionen konfigurieren, die CSS-Stile verketten, reduzieren und komprimieren, um deine Website zu beschleunigen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "<PERSON>chte darauf, dass die Option \"Aggregate JavaScript files\" (JavaScript-Dateien aggregieren) unter \"Administration\" (Verwalten) > \"Configuration\" (Konfiguration) > \"Development\" (Entwicklung) aktiviert ist. Durch [zusätzliche Module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) kannst du auch erweiterte Aggregierungsoptionen konfigurieren, die JavaScript-Assets verketten, reduzieren und komprimieren, um deine Website zu beschleunigen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Du kannst ungenutzte CSS-Regeln entfernen und nur die benötigten Drupal-Bibliotheken zu relevanten Seiten oder Seitenkomponenten hinzufügen. Weitere Informationen findest du in der [Drupal-Dokumentation](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Wenn du die angehängten Bibliotheken ermitteln möchtest, über die irrelevantes CSS hinzugefügt wird, kannst du das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Modul kannst du anhand der URL des Stylesheets erkennen, wenn die CSS-Aggregation auf deiner Drupal-Website deaktiviert ist. Suche in der Liste nach Designs/Modulen mit vielen Stylesheets, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Stylesheet sollte nur dann in ein Design/Modul aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Du hast die Möglichkeit, ungenutzte JavaScript-Assets zu entfernen und nur die benötigten Drupal-Bibliotheken zu relevanten Seiten oder Seitenkomponenten hinzuzufügen. Weitere Informationen findest du in der [Drupal-Dokumentation](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Wenn du die angehängten Bibliotheken ermitteln möchtest, über die irrelevantes JavaScript hinzugefügt wird, kannst du das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Modul kannst du anhand der URL des Skripts erkennen, wenn die JavaScript-Aggregation auf deiner Drupal-Website deaktiviert ist. Suche in der Liste nach Designs/Modulen mit vielen Skripts, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Skript sollte nur dann in ein Design/Modul aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Konfiguriere die Option \"Browser and proxy cache maximum age\" (Maximale Lebensdauer für Browser- und Proxy-Cache) unter \"Administration\" (Verwalten) > \"Configuration\" (Konfiguration) > Development (Entwicklung). [Hier findest du weitere Informationen über den Drupal-Cache und Leistungsoptimierungen.](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Du hast die Möglichkeit, [ein Modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) zu verwenden, das automatisch und ohne Qualitätsverlust die Größe von Bildern, die über die Website hochgeladen werden, optimiert und reduziert. Achte außerdem darauf, dass du für alle auf deiner Website gerenderten Bilder die von Drupal bereitgestellten [responsiven Bildstile (Responsive Image Styles)](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (verfügbar ab Drupal 8.0) verwendest."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Du kannst Hinweise auf Ressourcen für den User-Agent als \"preconnect\" oder \"dns-prefetch\" hinzufügen, indem du [ein Modul installierst oder konfigurierst](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), das die entsprechende Funktionalität bietet."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON>, dass du die von Drupal bereitgestellten [responsiven Bildstile (Responsive Image Styles)](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (verfügbar ab Drupal 8.0) verwendest. Verwende die responsiven Bildstile für Bildfelder, die über Anzeigemodi gerendert werden, sowie für Bilder, die über den WYSIWYG-Editor hochgeladen wurden."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Optimize Fonts`“, um automatisch die CSS-Funktion „`font-display`“ zu nutzen. So können Nutzer Text lesen, während Webfonts geladen werden."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Next-Gen Formats`“, um Bilder in WebP zu konvertieren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Lazy <PERSON>ad Images`“, um das Laden von nicht sichtbaren Bildern zu verzö<PERSON>n, bis sie ben<PERSON><PERSON>gt werden."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere sowohl „`Critical CSS`“ als auch „`Script Delay`“, um das Laden von bei Ladebeginn nicht unbedingt erforderlichem JS-Code bzw. von CSS zu verzögern."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Verwende [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching), um deine Inhalte über unser weltweites Netzwerk im Cache zu speichern und die TTFB (Time To First Byte) zu verbessern."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Minify CSS`“, um CSS automatisch zu reduzieren und so die Netzwerknutzlast zu senken."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Minify Javascript`“, um JS-Code automatisch zu reduzieren und die Netzwerknutzlast zu senken."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Remove Unused CSS`“, um zur Lösung dieses Problems beizutragen. Dabei werden die CSS-Klassen identifiziert, die von den einzelnen Seiten deiner Website tatsächlich verwendet werden – alle anderen werden entfernt, um die Dateigröße zu reduzieren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Efficient Static Cache Policy`“, um für statische Assets empfohlene Werte im Caching-Header festzulegen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Next-Gen Formats`“, um Bilder in WebP zu konvertieren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Pre-Connect Origins`“, um automatisch Hinweise auf Ressourcen als „`preconnect`“ hinzuzufügen und möglichst frühzeitig eine Verbindung zu wichtigen Drittanbieterursprüngen herstellen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere sowohl „`Preload Fonts`“ als auch „`Preload Background Images`“, um „`preload`“-Links hinzuzufügen und das Abrufen von Ressourcen zu priorisieren, die beim Seitenaufbau erst später angefordert werden."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Verwende [Ezoic Leap](https://pubdash.ezoic.com/speed) und aktiviere „`Resize Images`“, um Bilder auf eine für das jeweilige Gerät angemessene Größe zu verkleinern und so die Netzwerknutzlast zu reduzieren."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Du hast die Möglichkeit, dein GIF bei einem Dienst hochzuladen, der da<PERSON><PERSON>r sorgt, dass es als HTML5-Video eingebettet werden kann."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Sie haben die Möglichkeit, Ihre hochgeladenen Bilder mithilfe eines [Plug-ins](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) oder eines Dienstes automatisch in das optimale Format zu konvertieren."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Du kannst ein [Lazy-Loading-Plug-in für Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) installieren, um nicht sichtbare Bilder aufzuschieben. Alternativ kannst du auch zu einer Vorlage wechseln, die diese Funktion bietet. Ab Joomla 4.0 erhalten alle neuen Bilder [automatisch](https://github.com/joomla/joomla-cms/pull/30748) das Attribut „`loading`“."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Es gibt eine Reih<PERSON> von <PERSON>-Plug-ins, mit denen du [wichtige Assets einbetten](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) oder [weniger wichtige Ressourcen aufschieben](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) kannst. Beachte, dass über diese Plug-ins bereitgestellte Optimierungen dazu führen können, dass deine Vorlagen oder Plug-ins nicht funktionieren. Daher solltest du sie sorgfältig testen."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "So<PERSON>hl Vorlagen, Erweiterungen als auch Serverspezifikationen tragen zur Serverantwortzeit bei. <PERSON><PERSON><PERSON>, eine noch weiter optimierte Vorlage zu finden, wähle eine geeignete Optimierungs-Erweiterung aus und/oder upgrade deinen Server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Du hast die Möglichkeit, <PERSON>szüge in deinen Artikelkategorien einzublenden (z. B. über den Link \"Weiterlesen\"), die Anzahl der Artikel auf einer Seite zu verringern, lange Beiträge auf mehrere Seiten aufzuteilen oder ein Plug-in für das Lazy Loading von Kommentaren zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Deine Website lässt sich mit einer Reihe von [Joomla-Erweiterungen](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) beschleunigen, durch die deine CSS-Stile verkettet, reduziert und komprimiert werden. Es gibt auch Vorlagen, die diese Funktionen bieten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Deine Website lässt sich mit einer Reihe von [Joomla-Erweiterungen](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) beschleunigen, durch die deine Skripts verkettet und komprimiert werden. Es gibt auch Vorlagen, die diese Funktionen bieten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON>, ob du [Joomla-Erweiterungen](https://extensions.joomla.org/), über die nicht verwendetes CSS auf deine Seite geladen wird, entfernen oder durch alternative Erweiterungen ersetzen kannst. Wenn du die Erweiterungen ermitteln möchtest, über die irrelevantes CSS hinzugefügt wird, kannst du das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in kannst du anhand der URL des Stylesheets erkennen. Suche in der Liste nach Plug-ins mit vielen Stylesheets, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Stylesheet sollte nur dann in ein Plug-in aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON>, ob du [Joomla-Erweiterungen](https://extensions.joomla.org/), über die nicht verwendetes JavaScript auf deine Seite geladen wird, entfernen oder durch alternative Plug-ins ersetzen kannst. Wenn du die Plug-ins ermitteln möchtest, über die irrelevantes JavaScript hinzugefügt wird, kannst du das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Die entsprechende Erweiterung kannst du anhand der URL des Skripts erkennen. Suche in der Liste nach Erweiterungen mit vielen Skripts, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Skript sollte nur dann in eine Erweiterung aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[Hier erhältst du Informationen zum Browser-Caching in Joomla.](https://docs.joomla.org/Cache)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Du hast die Möglichkeit, ein [Plug-in für die Bildoptimierung](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) zu verwenden, durch das deine Bilder komprimiert werden, die Qualität jedoch gleich bleibt."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Du hast die Möglichkeit, ein [Plug-in für responsive Bilder](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) zu verwenden, um für deine Inhalte responsive Bilder zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Du kannst die Textkomprimierung aktivieren, indem du in Joomla die Gzip-Seitenkomprimierung aktivierst (\"System\" > \"Konfiguration – Global\" > \"Server\")."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Wenn du deine JavaScript-Assets nicht in einem Bundle zusammenführst, empfehlen wir dir die Verwendung von [<PERSON><PERSON>](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Deaktiviere die integrierte [JavaScript-Bundle-Erstellung und -Komprimierung](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) von Ma<PERSON>o und nutze stattdessen [Baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Gib \"`@font-display`\" an, wenn du [benutzerdefinierte Schriftarten definierst](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Auf dem [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) findest du eine Auswahl an Drittanbietererweiterungen, mit denen du aktuelle Bildformate nutzen kannst."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Du kannst deine Produkt- und Katalogvorlagen anpassen, um die [<PERSON><PERSON>](https://web.dev/native-lazy-loading)-Funktion der Webplattform nutzen können."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON> die [Varnish-Integration](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) von <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Du kannst die Option zum Komprimieren von CSS-Dateien in den Entwicklereinstellungen des Stores aktivieren. [Weitere Informationen.](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Verwende [Terser](https://www.npmjs.com/package/terser), um alle JavaScript-Assets aus der statischen Inhaltsbereitstellung zu reduzieren, und deaktiviere die integrierte Reduzierungsfunktion."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Deaktiviere die integrierte [JavaScript-Bundle-Erstellung](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) von <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Auf dem [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) findest du eine Auswahl an Drittanbietererweiterungen, mit denen du Bilder optimieren kannst."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Du kannst Hinweise auf Ressourcen als \"preconnect\" oder \"dns-prefetch\" hinz<PERSON><PERSON><PERSON>, indem du [das Layout eines Designs änderst](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Du kannst \"`<link rel=preload>`\"-<PERSON><PERSON> hinzufügen, indem du [das Layout eines Designs änderst](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Du kannst statt „`<img>`“ die Komponente „`next/image`“ verwenden, um das Bildformat automatisch zu optimieren. [Weitere Informationen](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Du kannst statt „`<img>`“ die Komponente „`next/image`“ verwenden, um Bilder automatisch per Lazy Loading zu laden. [Weitere Informationen](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Verwende die Komponente „`next/image`“ und setze „priority“ auf „wahr“, um das LCP-Bild vorab zu laden. [Weitere Informationen](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Du kannst die Komponente „`next/script`“ verwen<PERSON>, um das Laden von unkritischen Drittanbieter-Skripts zu verzögern. [Weitere Informationen](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Du kannst die Komponente „`next/image`“ ver<PERSON><PERSON>, damit Bilder immer die korrekte Größe haben. [Weitere Informationen](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Du kannst „`PurgeCSS`“ in der`Next.js`-Konfiguration e<PERSON><PERSON><PERSON>, um nicht verwendete Regeln aus Stylesheets zu entfernen. [Weitere Informationen](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "<PERSON> kannst „`Webpack Bundle Analyzer`“ verwenden, um nicht verwendeten JavaScript-Code zu entdecken. [Weitere Informationen](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON> kannst „`Next.js Analytics`“ verwen<PERSON>, um die Leistung deiner App in der Praxis zu messen. [Weitere Informationen](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfiguriere das Caching von unveränderlichen Assets und `Server-side Rendered`-Seiten (SSR). [Weitere Informationen](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Du kannst statt „`<img>`“ die Komponente „`next/image`“ verwenden, um die Bildqualität anzupassen. [Weitere Informationen](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Du kannst die Komponente „`next/image`“ verwenden, um den geeigneten Wert für „`sizes`“ festzulegen. [Weitere Informationen](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Aktiviere die Komprimierung auf deinem Next.js-Server. [Weitere Informationen](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Du kannst die Komponente „`nuxt/image`“ verwenden und „`format=\"webp\"`“ festlegen. [Weitere Informationen](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Du kannst für nicht sichtbare Bilder die Komponente „`nuxt/image`“ verwenden und „`loading=\"lazy\"`“ festlegen. [Weitere Informationen](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Du kannst für LCP-Bilder die Komponente „`nuxt/image`“ verwenden und „`preload`“ angeben. [Weitere Informationen](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Du kannst die Komponente „`nuxt/image`“ verwenden und explizite Werte für „`width`“ und „`height`“ festlegen. [Weitere Informationen](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Du kannst die Komponente „`nuxt/image`“ verwenden und den geeigneten Wert für „`quality`“ festlegen. [Weitere Informationen](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Du kannst die Komponente „`nuxt/image`“ verwenden und den geeigneten Wert für „`sizes`“ festlegen. [Weitere Informationen](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Ersetze animierte GIFs durch Videos](https://web.dev/replace-gifs-with-videos/), damit die Webseite schneller lädt, und verwende moderne Dateiformate wie [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) oder [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), um die Kompressionseffizienz um mehr als 30 % gegenüber dem neuesten Video-Codec bzw. VP9 zu verbessern."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Du hast die Möglichkeit, die hochgeladenen Bilder mithilfe eines [Plug-ins](https://octobercms.com/plugins?search=image) oder eines Dienstes automatisch in das optimale Format zu konvertieren. [Verlustfreie WebP-Bilder](https://developers.google.com/speed/webp) sind 26 % kleiner als PNGs und 25 bis 34 % kleiner als vergleichbare JPEG-Bilder bei gleichem SSIM-Qualitätsindex. Ein weiteres Bildformat der nächsten Generation, das du in Betracht ziehen könntest, ist [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Du kannst ein [La<PERSON>-Loading-Plug-in für Bilder](https://octobercms.com/plugins?search=lazy) installieren, um nicht sichtbare Bilder aufzuschieben. Alternativ kannst du auch zu einem Design wechseln, das diese Funktion bietet. Du solltest dir auch überlegen, [das AMP-Plug-in](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Es gibt zahlreiche Plug-ins, mit denen [wichtige Assets eingebettet werden können](https://octobercms.com/plugins?search=css). Diese Plug-ins können dazu führen, dass andere Plug-ins nicht mehr funktionieren. Daher solltest du sorgfältig testen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Sowohl Designs, Plug-ins als auch Serverspezifikationen tragen zur Serverantwortzeit bei. Versuche, ein noch weiter optimiertes Design zu finden, wähle ein geeignetes Optimierungs-Plug-in aus und/oder upgrade deinen Server. Bei October CMS können Entwickler mit [`Queues`](https://octobercms.com/docs/services/queues) die Verarbeitung einer zeitaufwendigen Aufgabe wie das Senden einer E-Mail aufschieben. Dadurch werden Webanfragen erheblich beschleunigt."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Du hast die Möglichkeit, Auszüge in deiner Beitragsliste einzublenden (z. B. über die Schaltfläche „`show more`“), die Anzahl der Beiträge auf einer Webseite zu verringern, lange Beiträge auf mehrere Seiten aufzuteilen oder ein Plug-in für das Lazy Loading von Kommentaren zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON>s gibt zahlreiche [Plug-ins](https://octobercms.com/plugins?search=css), die eine Website durch Verketten, Verkleinern und Komprimieren von Stilen beschleunigen können. Eine Komprimierung im Voraus über einen Build-Prozess kann die Entwicklung beschleunigen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON>s gibt zahlreiche [Plug-ins](https://octobercms.com/plugins?search=javascript), die eine Website durch Verketten, Verkleinern und Komprimieren von Skripten beschleunigen können. Eine Komprimierung im Voraus über einen Build-Prozess kann die Entwicklung beschleunigen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Pr<PERSON><PERSON> die [Plug-ins](https://octobercms.com/plugins), über die nicht verwendete CSS auf der Website geladen werden. Wenn du Plug-ins ermitteln möchtest, über die nicht notwendige CSS hinzugefügt werden, kannst du das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in kannst du anhand der URL des Stylesheets erkennen. Suche nach Plug-ins mit vielen Stylesheets und viel nicht verwendetem Code (markiert in Rot) in der Codeabdeckung. Ein Plug-in sollte nur dann ein Stylesheet hinzufügen, wenn es auch tatsächlich auf der Webseite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Du solltest prüfen, über welche [Plug-ins](https://octobercms.com/plugins?search=javascript) nicht verwendetes JavaScript auf deine Webseite geladen wird. Wenn du Plug-ins ermitteln möchtest, über die nicht notwendiges JavaScript hinzugefügt wird, kannst du das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in kannst du anhand der URL des Skripts erkennen. Suche nach Plug-ins mit vielen Skripts, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Plug-in sollte nur dann ein Skript hinzufügen, wenn es auch tatsächlich auf der Webseite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[Hier findest du Informationen, wie du unnötige Netzwerkanfragen mit dem HTTP-Cache verhinderst.](https://web.dev/http-cache/#caching-checklist). Es gibt zahlreiche [Plug-ins](https://octobercms.com/plugins?search=Caching), die zum Beschleunigen des Cachings verwendet werden können."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Du hast die Möglichkeit, ein [Plug-in für die Bildoptimierung](https://octobercms.com/plugins?search=image) zu verwenden, durch das Bilder komprimiert werden, die Qualität jedoch gleich bleibt."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Lade Bilder direkt in den Medienmanager hoch, damit die erforderlichen Bildgrößen verfügbar sind. Mithi<PERSON><PERSON> eines [Filters zur Größenanpassung](https://octobercms.com/docs/markup/filter-resize) oder eines [Plug-ins zur Bildgrößenanpassung](https://octobercms.com/plugins?search=image) kannst du sicherstellen, dass die optimalen Bildgrößen verwendet werden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Aktiviere die Textkomprimierung in der Konfiguration des Webservers."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Du hast die Möglichkeit, eine Bibliothek wie „`react-window`“ zur Fensterverwaltung zu nutzen, um die Anzahl an DOM-Knoten zu minimieren, die beim Rendern vieler sich wiederholender Elemente auf der Seite erstellt werden. [Weitere Informationen](https://web.dev/virtualize-long-lists-react-window/) <PERSON> kann<PERSON> [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) oder [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) nutzen, um wiederholtes Rendern auf ein Mindestmaß zu reduzieren. Auch [skip effects](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) sind möglich, aber nur, bis sich bestimmte Abhängigkeiten geändert haben (sofern du den `Effect`-Hook zur Verbesserung der Laufzeitleistung verwendest)."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON> du React Router nutzt, em<PERSON><PERSON><PERSON> wir, die Komponente \"`<Redirect>`\" für das [Bedienungs-Routing](https://reacttraining.com/react-router/web/api/Redirect) so wenig wie möglich zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "<PERSON>n du serverseitig React-Komponenten renderst, empfehlen wir den Einsatz von \"`renderToPipeableStream()`\" oder \"`renderToStaticNodeStream()`\", um dem Client zu ermöglichen, einzelne Teile des Markups abzurufen und auszufüllen. So müssen nicht alle auf einmal abgerufen werden. [Weitere Informationen.](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Wenn dein Build-System CSS-Dateien automatisch komprimiert, achte dara<PERSON>, dass du den Produktions-Build deiner App verfügbar machen. Das kannst du mit der Erweiterung „React Developer Tools“ prüfen. [Weitere Informationen](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Wenn dein Build-System JS-Dateien automatisch komprimiert, achte darauf, dass du den Produktions-Build deiner App verfügbar machst. Das kannst du mit der Erweiterung „React Developer Tools“ prüfen. [Weitere Informationen](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Wenn du nicht serverseitig rendern lässt, [kannst du deine JavaScript-Bundles mit \"`React.lazy()`\" aufteilen](https://web.dev/code-splitting-suspense/). Andernfalls hast du die Möglichkeit, deinen Code mit einer Drittanbieterbibliothek wie [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/) zu splitten."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Verwende den React DevTools Profiler. Dieser greift auf die Profiler API zurück, um die Rendering-Leistung deiner Komponenten zu messen. [Weitere Informationen.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Du hast die Möglichkeit, dein GIF bei einem Dienst hochzuladen, der da<PERSON><PERSON>r sorgt, dass es als HTML5-Video eingebettet werden kann."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Du kannst das [Performance Lab](https://wordpress.org/plugins/performance-lab/)-Plug-in verwenden, um deine hochgeladenen JPEG-Bilder automatisch in WebP konvertieren zu lassen, sofern dies unterstützt wird."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Du kannst ein [La<PERSON>-Loading-Plug-in für WordPress](https://wordpress.org/plugins/search/lazy+load/) installieren, mit dem du nicht sichtbare Bilder aufschiebst. Alternativ kannst du auch zu einem Design wechseln, das diese Funktion bietet. Du solltest dir auch überlegen, [das AMP-Plug-in](https://wordpress.org/plugins/amp/) zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Es gibt eine Reih<PERSON> von WordPress-Plug-ins, mit denen du [wichtige Assets einbetten](https://wordpress.org/plugins/search/critical+css/) oder [weniger wichtige Ressourcen aufschieben](https://wordpress.org/plugins/search/defer+css+javascript/) kannst. Beachte, dass über diese Plug-ins bereitgestellte Optimierungen dazu führen können, dass deine Designs oder Plug-ins nicht funktionieren. Daher musst du wahrscheinlich Änderungen am Code vornehmen."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "So<PERSON>hl Designs, Plug-ins als auch Serverspezifikationen tragen zur Serverantwortzeit bei. <PERSON><PERSON><PERSON>, ein noch weiter optimiertes Design zu finden, wähle ein geeignetes Optimierungs-Plug-in aus und/oder upgrade deinen Server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Du hast die Möglichkeit, Auszüge in deiner Beitragsliste einzublenden (z. B. über das Tag \"Mehr\"), die Anzahl der Beiträge auf einer Seite zu verringern, lange Beiträge auf mehrere Seiten aufzuteilen oder ein Plug-in für das Lazy Loading von Kommentaren zu verwenden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Deine Website lässt sich mit einer Reihe von [WordPress-Plug-ins](https://wordpress.org/plugins/search/minify+css/) beschleunigen, durch die deine Stile verkettet und komprimiert werden. So<PERSON><PERSON> mö<PERSON>, kannst du diese Komprimierung auch im Voraus über einen Build-Prozess vornehmen."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Deine Website lässt sich mit einer Reihe von [WordPress-Plug-ins](https://wordpress.org/plugins/search/minify+javascript/) beschleunigen, durch die deine Skripts verkettet und komprimiert werden. <PERSON><PERSON><PERSON> m<PERSON>, kannst du diese Komprimierung auch im Voraus über einen Build-Prozess vornehmen."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON>, ob du [WordPress-Plug-ins](https://wordpress.org/plugins/), über die nicht verwendete CSS auf deine Seite geladen werden, entfernen oder durch alternative Plug-ins ersetzen kannst. Wenn du die Plug-ins ermitteln möchtest, über die irrelevante CSS hinzugefügt werden, kannst du das Prüftool zur [Codeabdeckung](https://developer.chrome.com/docs/devtools/coverage/) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in kannst du anhand der URL des Stylesheets erkennen. Suche in der Liste nach Plug-ins mit vielen Stylesheets, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Stylesheet sollte nur dann in ein Plug-in aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON>, ob du [WordPress-Plug-ins](https://wordpress.org/plugins/), über die nicht verwendete JavaScript-Dateien auf deiner Seite geladen werden, entfernen oder durch alternative Plug-ins ersetzen kannst. Wenn du die Plug-ins ermitteln möchtest, über die irrelevante JavaScript-Dateien hinzugefügt werden, kannst du das Prüftool zur [Codeabdeckung](https://developer.chrome.com/docs/devtools/coverage/) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in kannst du anhand der URL des Skripts erkennen. Suche in der Liste nach Plug-ins mit vielen Skripts, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Skript sollte nur dann in ein Plug-in aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Hier erhältst du Informationen zum [Browser-Caching in WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Du hast die Möglichkeit, ein [WordPress-Plug-in für die Bildoptimierung](https://wordpress.org/plugins/search/optimize+images/) zu verwenden, durch das deine Bilder komprimiert werden, die Qualität jedoch gleich bleibt."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Du hast die Möglichkeit, Bilder direkt über die [Mediathek](https://wordpress.org/support/article/media-library-screen/) ho<PERSON><PERSON><PERSON><PERSON>, damit die erforderlichen Bildgrößen verfügbar sind. Die Bilder kannst du dann aus der Mediathek einfügen oder auch das Bild-Widget nutzen, damit die optimalen Bildgrößen verwendet werden (einschließlich derjenigen für die responsiven Haltepunkte). Bilder in `Full Size` sollten nur verwendet werden, wenn die Abmessungen für die entsprechende Nutzung geeignet sind. [Weitere Informationen.](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Du kannst die Textkomprimierung in der Konfiguration deines Webservers aktivieren."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Aktiviere „Imagify“ auf dem Tab zur Bildoptimierung in WP Rocket, um deine Bilder in WebP zu konvertieren."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Aktiviere [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) in WP Rocket, um diese Empfehlung umzusetzen. Mit dieser Funktion wird das Laden der Bilder verzögert, bis der Nutzer auf der Seite nach unten scrollt und sie tatsächlich angezeigt werden müssen."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Aktiviere das [Ent<PERSON><PERSON> von nicht verwendetem CSS-Code](https://docs.wp-rocket.me/article/1529-remove-unused-css) und das [aufgeschobene Laden von JavaScript](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) in WP Rocket, um diese Empfehlung umzusetzen. Durch diese Funktionen werden die CSS- und JavaScript-Dateien so optimiert, dass das Rendering deiner Seite nicht blockiert wird."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Aktiviere die Funktion zum [Reduzieren der Größe von CSS-Dateien](https://docs.wp-rocket.me/article/1350-css-minify-combine) in WP Rocket, um dieses Problem zu beheben. Alle Leerzeichen und Kommentare werden aus den CSS-Dateien deiner Website entfernt. So wird die Dateigröße verringert und der Download beschleunigt."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Aktiviere die Funktion zum [Reduzieren der Größe von JavaScript-Dateien](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) in WP Rocket, um dieses Problem zu beheben. Leere Bereiche und Kommentare werden aus den JavaScript-Dateien entfernt. So wird die Dateigröße verringert und der Download beschleunigt."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Aktiviere das [Entfernen von nicht verwendetem CSS-Code](https://docs.wp-rocket.me/article/1529-remove-unused-css) in WP Rocket, um dieses Problem zu beheben. Die Funktion verringert die Seitengröße, indem sie sämtlichen CSS-Code und alle Stylesheets entfernt, die nicht verwendet werden, und nur den für einzelne Seiten verwendeten CSS-Code beibehält."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Aktiviere [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) in WP Rocket, um dieses Problem zu beheben. Das Laden der Seite wird dadurch verbessert, da die Ausführung von Skripts bis zur Nutzerinteraktion verzögert wird. Wenn deine Website iFrames hat, kannst du in WP Rocket auch [LazyLoad für iFrames und Videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) sowie die Funktion zum [Ersetzen von YouTube-iFrames durch Vorschaubilder](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) verwenden."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Aktiviere „Imagify“ auf dem Tab zur Bildoptimierung in WP Rocket und führe die Bulk-Optimierung aus, um deine Bilder zu komprimieren."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Mit [Prefetch-DNS-Anfragen](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) in WP Rocket kannst du „dns-prefetch“ hinzufügen und die Verbindung mit externen Domains beschleunigen. Außerdem fügt WP <PERSON> der [Google Fonts-Domain](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) und allen über die Funktion zum [Aktivieren des CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) hinzugefügten CNAMEs automatisch „preconnect“ hinzu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Aktiviere die [Entfernung von nicht verwendetem CSS-Code](https://docs.wp-rocket.me/article/1529-remove-unused-css) in WP Rocket, um dieses Schriftartenproblem zu beheben. Die wichtigsten Schriftarten deiner Website werden vorab mit Priorität geladen."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON><PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "Ansicht minimieren"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Anfangsnavigation"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maximale Latenz für kritischen Pfad:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSON kopieren"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "<PERSON><PERSON> dunkles Design umschalten"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Erweiterten Bericht drucken"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Zusammenfassung drucken"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Als G<PERSON> s<PERSON>ichern"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Als HTML speichern"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Als JSON speichern"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | errorLabel": {"message": "<PERSON><PERSON>."}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Fehler gemeldet: keine Informationen zur Überprüfung"}, "report/renderer/report-utils.js | expandView": {"message": "Ansicht maximieren"}, "report/renderer/report-utils.js | footerIssue": {"message": "Problem melden"}, "report/renderer/report-utils.js | hide": {"message": "Ausblenden"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Labdaten"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/)-Analyse der aktuellen Seite in einem emulierten Mobilfunknetz. Die Werte sind Schätzungen und können variieren."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Zusätzliche Elemente zur manuellen Überprüfung"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON>cht zu<PERSON>nd"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Empfehlung"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Geschätzte Einsparung"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Bestandene Prüfungen"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Benutzerdefinierte Drosselung"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Desktopemulation"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Keine Emulation"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-Version"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Leistung der ungedrosselten CPU / des ungedrosselten Arbeitsspeichers"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU-Drosselung"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "G<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Netzwerkdrosselung"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Bildschirmemulation"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User-Agent (Netzwerk)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Einzelner Seitenaufbau"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Diese Daten wurden einem einzelnen Seitenaufbau entnommen – Felddaten fassen dagegen mehrere Sitzungen zusammen."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Langsame 4G-Drosselung"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Unbekannt"}, "report/renderer/report-utils.js | show": {"message": "Anzeigen"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Prüfungen anzeigen, die für folgende Messwerte relevant sind:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Snippet minimieren"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Snippet maximieren"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Drittanbieter-Ressourcen anzeigen"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Durch die Umgebung bereitgestellt"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Einige Probleme haben diese Ausführung von Lighthouse beeinträchtigt:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Die Werte sind geschätzt und können variieren. Die [Leistungsbewertung](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) wird direkt aus diesen Messwerten berechnet."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Ursprünglichen Trace anzeigen"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON> anzeigen"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Strukturkarte anzeigen"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Bestandene Prüfungen mit Warnungen"}, "report/renderer/report-utils.js | warningHeader": {"message": "Warnungen: "}, "treemap/app/src/util.js | allLabel": {"message": "Alles"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Alle Skripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Ressourcengröße in Byte"}, "treemap/app/src/util.js | tableColumnName": {"message": "Name"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "<PERSON><PERSON><PERSON> ein- oder ausblenden"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Nicht verwendete Byte"}}