{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getStyle = getStyle;\nexports.registerTheme = registerTheme;\nvar _dynamicCSS = require(\"rc-util/lib/Dom/dynamicCSS\");\nvar _canUseDom = _interopRequireDefault(require(\"rc-util/lib/Dom/canUseDom\"));\nvar _tinycolor = require(\"@ctrl/tinycolor\");\nvar _colors = require(\"@ant-design/colors\");\nvar _devWarning = _interopRequireDefault(require(\"../_util/devWarning\"));\n\n/* eslint-disable import/prefer-default-export, prefer-destructuring */\nvar dynamicStyleMark = \"-ant-\".concat(Date.now(), \"-\").concat(Math.random());\nfunction getStyle(globalPrefixCls, theme) {\n  var variables = {};\n  var formatColor = function formatColor(color, updater) {\n    var clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n  var fillColor = function fillColor(colorVal, type) {\n    var baseColor = new _tinycolor.TinyColor(colorVal);\n    var colorPalettes = (0, _colors.generate)(baseColor.toRgbString());\n    variables[\"\".concat(type, \"-color\")] = formatColor(baseColor);\n    variables[\"\".concat(type, \"-color-disabled\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-hover\")] = colorPalettes[4];\n    variables[\"\".concat(type, \"-color-active\")] = colorPalettes[7];\n    variables[\"\".concat(type, \"-color-outline\")] = baseColor.clone().setAlpha(0.2).toRgbString();\n    variables[\"\".concat(type, \"-color-deprecated-bg\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-deprecated-border\")] = colorPalettes[3];\n  }; // ================ Primary Color ================\n\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    var primaryColor = new _tinycolor.TinyColor(theme.primaryColor);\n    var primaryColors = (0, _colors.generate)(primaryColor.toRgbString()); // Legacy - We should use semantic naming standard\n\n    primaryColors.forEach(function (color, index) {\n      variables[\"primary-\".concat(index + 1)] = color;\n    }); // Deprecated\n\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, function (c) {\n      return c.lighten(35);\n    });\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, function (c) {\n      return c.lighten(20);\n    });\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, function (c) {\n      return c.tint(20);\n    });\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, function (c) {\n      return c.tint(50);\n    });\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.12);\n    });\n    var primaryActiveColor = new _tinycolor.TinyColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.3);\n    });\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, function (c) {\n      return c.darken(2);\n    });\n  } // ================ Success Color ================\n\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  } // ================ Warning Color ================\n\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  } // ================= Error Color =================\n\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  } // ================= Info Color ==================\n\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  } // Convert to css variables\n\n  var cssList = Object.keys(variables).map(function (key) {\n    return \"--\".concat(globalPrefixCls, \"-\").concat(key, \": \").concat(variables[key], \";\");\n  });\n  return \"\\n  :root {\\n    \".concat(cssList.join('\\n'), \"\\n  }\\n  \").trim();\n}\nfunction registerTheme(globalPrefixCls, theme) {\n  var style = getStyle(globalPrefixCls, theme);\n  if ((0, _canUseDom[\"default\"])()) {\n    (0, _dynamicCSS.updateCSS)(style, \"\".concat(dynamicStyleMark, \"-dynamic-theme\"));\n  } else {\n    (0, _devWarning[\"default\"])(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.');\n  }\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "getStyle", "registerTheme", "_dynamicCSS", "_canUseDom", "_tinycolor", "_colors", "_devWarning", "dynamicStyleMark", "concat", "Date", "now", "Math", "random", "globalPrefixCls", "theme", "variables", "formatColor", "color", "updater", "clone", "toRgbString", "fillColor", "colorVal", "type", "baseColor", "TinyColor", "colorPalettes", "generate", "<PERSON><PERSON><PERSON><PERSON>", "primaryColor", "primaryColors", "for<PERSON>ach", "index", "c", "lighten", "tint", "get<PERSON><PERSON><PERSON>", "primaryActiveColor", "darken", "successColor", "warningColor", "errorColor", "infoColor", "cssList", "keys", "map", "key", "join", "trim", "style", "updateCSS"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/config-provider/cssVariables.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getStyle = getStyle;\nexports.registerTheme = registerTheme;\n\nvar _dynamicCSS = require(\"rc-util/lib/Dom/dynamicCSS\");\n\nvar _canUseDom = _interopRequireDefault(require(\"rc-util/lib/Dom/canUseDom\"));\n\nvar _tinycolor = require(\"@ctrl/tinycolor\");\n\nvar _colors = require(\"@ant-design/colors\");\n\nvar _devWarning = _interopRequireDefault(require(\"../_util/devWarning\"));\n\n/* eslint-disable import/prefer-default-export, prefer-destructuring */\nvar dynamicStyleMark = \"-ant-\".concat(Date.now(), \"-\").concat(Math.random());\n\nfunction getStyle(globalPrefixCls, theme) {\n  var variables = {};\n\n  var formatColor = function formatColor(color, updater) {\n    var clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n\n  var fillColor = function fillColor(colorVal, type) {\n    var baseColor = new _tinycolor.TinyColor(colorVal);\n    var colorPalettes = (0, _colors.generate)(baseColor.toRgbString());\n    variables[\"\".concat(type, \"-color\")] = formatColor(baseColor);\n    variables[\"\".concat(type, \"-color-disabled\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-hover\")] = colorPalettes[4];\n    variables[\"\".concat(type, \"-color-active\")] = colorPalettes[7];\n    variables[\"\".concat(type, \"-color-outline\")] = baseColor.clone().setAlpha(0.2).toRgbString();\n    variables[\"\".concat(type, \"-color-deprecated-bg\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-deprecated-border\")] = colorPalettes[3];\n  }; // ================ Primary Color ================\n\n\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    var primaryColor = new _tinycolor.TinyColor(theme.primaryColor);\n    var primaryColors = (0, _colors.generate)(primaryColor.toRgbString()); // Legacy - We should use semantic naming standard\n\n    primaryColors.forEach(function (color, index) {\n      variables[\"primary-\".concat(index + 1)] = color;\n    }); // Deprecated\n\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, function (c) {\n      return c.lighten(35);\n    });\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, function (c) {\n      return c.lighten(20);\n    });\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, function (c) {\n      return c.tint(20);\n    });\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, function (c) {\n      return c.tint(50);\n    });\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.12);\n    });\n    var primaryActiveColor = new _tinycolor.TinyColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.3);\n    });\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, function (c) {\n      return c.darken(2);\n    });\n  } // ================ Success Color ================\n\n\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  } // ================ Warning Color ================\n\n\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  } // ================= Error Color =================\n\n\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  } // ================= Info Color ==================\n\n\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  } // Convert to css variables\n\n\n  var cssList = Object.keys(variables).map(function (key) {\n    return \"--\".concat(globalPrefixCls, \"-\").concat(key, \": \").concat(variables[key], \";\");\n  });\n  return \"\\n  :root {\\n    \".concat(cssList.join('\\n'), \"\\n  }\\n  \").trim();\n}\n\nfunction registerTheme(globalPrefixCls, theme) {\n  var style = getStyle(globalPrefixCls, theme);\n\n  if ((0, _canUseDom[\"default\"])()) {\n    (0, _dynamicCSS.updateCSS)(style, \"\".concat(dynamicStyleMark, \"-dynamic-theme\"));\n  } else {\n    (0, _devWarning[\"default\"])(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.');\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAC3BF,OAAO,CAACG,aAAa,GAAGA,aAAa;AAErC,IAAIC,WAAW,GAAGP,OAAO,CAAC,4BAA4B,CAAC;AAEvD,IAAIQ,UAAU,GAAGT,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAE7E,IAAIS,UAAU,GAAGT,OAAO,CAAC,iBAAiB,CAAC;AAE3C,IAAIU,OAAO,GAAGV,OAAO,CAAC,oBAAoB,CAAC;AAE3C,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;;AAExE;AACA,IAAIY,gBAAgB,GAAG,OAAO,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACF,MAAM,CAACG,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;AAE5E,SAASZ,QAAQA,CAACa,eAAe,EAAEC,KAAK,EAAE;EACxC,IAAIC,SAAS,GAAG,CAAC,CAAC;EAElB,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACrD,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAAC,CAAC;IACzBA,KAAK,GAAG,CAACD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,KAAK,CAAC,KAAKA,KAAK;IACnF,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC;EAC5B,CAAC;EAED,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,QAAQ,EAAEC,IAAI,EAAE;IACjD,IAAIC,SAAS,GAAG,IAAIpB,UAAU,CAACqB,SAAS,CAACH,QAAQ,CAAC;IAClD,IAAII,aAAa,GAAG,CAAC,CAAC,EAAErB,OAAO,CAACsB,QAAQ,EAAEH,SAAS,CAACJ,WAAW,CAAC,CAAC,CAAC;IAClEL,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,QAAQ,CAAC,CAAC,GAAGP,WAAW,CAACQ,SAAS,CAAC;IAC7DT,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,iBAAiB,CAAC,CAAC,GAAGG,aAAa,CAAC,CAAC,CAAC;IAChEX,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,cAAc,CAAC,CAAC,GAAGG,aAAa,CAAC,CAAC,CAAC;IAC7DX,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,eAAe,CAAC,CAAC,GAAGG,aAAa,CAAC,CAAC,CAAC;IAC9DX,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,gBAAgB,CAAC,CAAC,GAAGC,SAAS,CAACL,KAAK,CAAC,CAAC,CAACS,QAAQ,CAAC,GAAG,CAAC,CAACR,WAAW,CAAC,CAAC;IAC5FL,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,sBAAsB,CAAC,CAAC,GAAGG,aAAa,CAAC,CAAC,CAAC;IACrEX,SAAS,CAAC,EAAE,CAACP,MAAM,CAACe,IAAI,EAAE,0BAA0B,CAAC,CAAC,GAAGG,aAAa,CAAC,CAAC,CAAC;EAC3E,CAAC,CAAC,CAAC;;EAGH,IAAIZ,KAAK,CAACe,YAAY,EAAE;IACtBR,SAAS,CAACP,KAAK,CAACe,YAAY,EAAE,SAAS,CAAC;IACxC,IAAIA,YAAY,GAAG,IAAIzB,UAAU,CAACqB,SAAS,CAACX,KAAK,CAACe,YAAY,CAAC;IAC/D,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAEzB,OAAO,CAACsB,QAAQ,EAAEE,YAAY,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvEU,aAAa,CAACC,OAAO,CAAC,UAAUd,KAAK,EAAEe,KAAK,EAAE;MAC5CjB,SAAS,CAAC,UAAU,CAACP,MAAM,CAACwB,KAAK,GAAG,CAAC,CAAC,CAAC,GAAGf,KAAK;IACjD,CAAC,CAAC,CAAC,CAAC;;IAEJF,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACa,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC;IACFnB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACa,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC;IACFnB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACa,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;IACFpB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACa,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;IACFpB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACa,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACL,QAAQ,CAACK,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IACxC,CAAC,CAAC;IACF,IAAIC,kBAAkB,GAAG,IAAIjC,UAAU,CAACqB,SAAS,CAACK,aAAa,CAAC,CAAC,CAAC,CAAC;IACnEf,SAAS,CAAC,sCAAsC,CAAC,GAAGC,WAAW,CAACqB,kBAAkB,EAAE,UAAUJ,CAAC,EAAE;MAC/F,OAAOA,CAAC,CAACL,QAAQ,CAACK,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;IACvC,CAAC,CAAC;IACFrB,SAAS,CAAC,sCAAsC,CAAC,GAAGC,WAAW,CAACqB,kBAAkB,EAAE,UAAUJ,CAAC,EAAE;MAC/F,OAAOA,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIxB,KAAK,CAACyB,YAAY,EAAE;IACtBlB,SAAS,CAACP,KAAK,CAACyB,YAAY,EAAE,SAAS,CAAC;EAC1C,CAAC,CAAC;;EAGF,IAAIzB,KAAK,CAAC0B,YAAY,EAAE;IACtBnB,SAAS,CAACP,KAAK,CAAC0B,YAAY,EAAE,SAAS,CAAC;EAC1C,CAAC,CAAC;;EAGF,IAAI1B,KAAK,CAAC2B,UAAU,EAAE;IACpBpB,SAAS,CAACP,KAAK,CAAC2B,UAAU,EAAE,OAAO,CAAC;EACtC,CAAC,CAAC;;EAGF,IAAI3B,KAAK,CAAC4B,SAAS,EAAE;IACnBrB,SAAS,CAACP,KAAK,CAAC4B,SAAS,EAAE,MAAM,CAAC;EACpC,CAAC,CAAC;;EAGF,IAAIC,OAAO,GAAG/C,MAAM,CAACgD,IAAI,CAAC7B,SAAS,CAAC,CAAC8B,GAAG,CAAC,UAAUC,GAAG,EAAE;IACtD,OAAO,IAAI,CAACtC,MAAM,CAACK,eAAe,EAAE,GAAG,CAAC,CAACL,MAAM,CAACsC,GAAG,EAAE,IAAI,CAAC,CAACtC,MAAM,CAACO,SAAS,CAAC+B,GAAG,CAAC,EAAE,GAAG,CAAC;EACxF,CAAC,CAAC;EACF,OAAO,mBAAmB,CAACtC,MAAM,CAACmC,OAAO,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAACC,IAAI,CAAC,CAAC;AAC3E;AAEA,SAAS/C,aAAaA,CAACY,eAAe,EAAEC,KAAK,EAAE;EAC7C,IAAImC,KAAK,GAAGjD,QAAQ,CAACa,eAAe,EAAEC,KAAK,CAAC;EAE5C,IAAI,CAAC,CAAC,EAAEX,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;IAChC,CAAC,CAAC,EAAED,WAAW,CAACgD,SAAS,EAAED,KAAK,EAAE,EAAE,CAACzC,MAAM,CAACD,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;EAClF,CAAC,MAAM;IACL,CAAC,CAAC,EAAED,WAAW,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,sDAAsD,CAAC;EAC9G;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}