{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nexport var omit = function omit(obj) {\n  var clone = _objectSpread({}, obj);\n  for (var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    keys[_key - 1] = arguments[_key];\n  }\n  keys.forEach(function (key) {\n    delete clone[key];\n  });\n  return clone;\n};\n/**\n * Cut input selection into 2 part and return text before selection start\n */\n\nexport function getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\n\nexport function getLastMeasureIndex(text) {\n  var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  var prefixList = Array.isArray(prefix) ? prefix : [prefix];\n  return prefixList.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n  if (!firstChar || firstChar === split) {\n    return text;\n  } // Reuse rest text as it can\n\n  var restText = text;\n  var targetTextLen = targetText.length;\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n  return restText;\n}\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\n\nexport function replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n    prefix = measureConfig.prefix,\n    targetText = measureConfig.targetText,\n    selectionStart = measureConfig.selectionStart,\n    split = measureConfig.split; // Before text will append one space if have other text\n\n  var beforeMeasureText = text.slice(0, measureLocation);\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  } // Cut duplicate string with current targetText\n\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, props) {\n  var split = props.split;\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n    value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}", "map": {"version": 3, "names": ["_objectSpread", "omit", "obj", "clone", "_len", "arguments", "length", "keys", "Array", "_key", "for<PERSON>ach", "key", "getBeforeSelectionText", "input", "selectionStart", "value", "slice", "getLastMeasureIndex", "text", "prefix", "undefined", "prefixList", "isArray", "reduce", "lastMatch", "prefixStr", "lastIndex", "lastIndexOf", "location", "lower", "char", "toLowerCase", "reduceText", "targetText", "split", "firstChar", "restText", "targetTextLen", "i", "replaceWithMeasure", "measureConfig", "measureLocation", "beforeMeasureText", "concat", "connectedStartText", "selectionLocation", "setInputSelection", "setSelectionRange", "blur", "focus", "validateSearch", "props", "indexOf", "filterOption", "_ref", "_ref$value", "lowerCase"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-mentions/es/util.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nexport var omit = function omit(obj) {\n  var clone = _objectSpread({}, obj);\n\n  for (var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    keys[_key - 1] = arguments[_key];\n  }\n\n  keys.forEach(function (key) {\n    delete clone[key];\n  });\n  return clone;\n};\n/**\n * Cut input selection into 2 part and return text before selection start\n */\n\nexport function getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\n\nexport function getLastMeasureIndex(text) {\n  var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  var prefixList = Array.isArray(prefix) ? prefix : [prefix];\n  return prefixList.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\n\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\n\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n\n  if (!firstChar || firstChar === split) {\n    return text;\n  } // Reuse rest text as it can\n\n\n  var restText = text;\n  var targetTextLen = targetText.length;\n\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n\n  return restText;\n}\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\n\n\nexport function replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n      prefix = measureConfig.prefix,\n      targetText = measureConfig.targetText,\n      selectionStart = measureConfig.selectionStart,\n      split = measureConfig.split; // Before text will append one space if have other text\n\n  var beforeMeasureText = text.slice(0, measureLocation);\n\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  } // Cut duplicate string with current targetText\n\n\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, props) {\n  var split = props.split;\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n      value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,GAAG,EAAE;EACnC,IAAIC,KAAK,GAAGH,aAAa,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC;EAElC,KAAK,IAAIE,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAClC;EAEAF,IAAI,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC1B,OAAOR,KAAK,CAACQ,GAAG,CAAC;EACnB,CAAC,CAAC;EACF,OAAOR,KAAK;AACd,CAAC;AACD;AACA;AACA;;AAEA,OAAO,SAASS,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,IAAIC,cAAc,GAAGD,KAAK,CAACC,cAAc;EACzC,OAAOD,KAAK,CAACE,KAAK,CAACC,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC;AAC7C;AACA;AACA;AACA;;AAEA,OAAO,SAASG,mBAAmBA,CAACC,IAAI,EAAE;EACxC,IAAIC,MAAM,GAAGd,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKe,SAAS,GAAGf,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,IAAIgB,UAAU,GAAGb,KAAK,CAACc,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;EAC1D,OAAOE,UAAU,CAACE,MAAM,CAAC,UAAUC,SAAS,EAAEC,SAAS,EAAE;IACvD,IAAIC,SAAS,GAAGR,IAAI,CAACS,WAAW,CAACF,SAAS,CAAC;IAE3C,IAAIC,SAAS,GAAGF,SAAS,CAACI,QAAQ,EAAE;MAClC,OAAO;QACLA,QAAQ,EAAEF,SAAS;QACnBP,MAAM,EAAEM;MACV,CAAC;IACH;IAEA,OAAOD,SAAS;EAClB,CAAC,EAAE;IACDI,QAAQ,EAAE,CAAC,CAAC;IACZT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,SAASU,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;AACnC;AAEA,SAASC,UAAUA,CAACd,IAAI,EAAEe,UAAU,EAAEC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGjB,IAAI,CAAC,CAAC,CAAC;EAEvB,IAAI,CAACiB,SAAS,IAAIA,SAAS,KAAKD,KAAK,EAAE;IACrC,OAAOhB,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIkB,QAAQ,GAAGlB,IAAI;EACnB,IAAImB,aAAa,GAAGJ,UAAU,CAAC3B,MAAM;EAErC,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,EAAEC,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIT,KAAK,CAACO,QAAQ,CAACE,CAAC,CAAC,CAAC,KAAKT,KAAK,CAACI,UAAU,CAACK,CAAC,CAAC,CAAC,EAAE;MAC/CF,QAAQ,GAAGA,QAAQ,CAACpB,KAAK,CAACsB,CAAC,CAAC;MAC5B;IACF,CAAC,MAAM,IAAIA,CAAC,KAAKD,aAAa,GAAG,CAAC,EAAE;MAClCD,QAAQ,GAAGA,QAAQ,CAACpB,KAAK,CAACqB,aAAa,CAAC;IAC1C;EACF;EAEA,OAAOD,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASG,kBAAkBA,CAACrB,IAAI,EAAEsB,aAAa,EAAE;EACtD,IAAIC,eAAe,GAAGD,aAAa,CAACC,eAAe;IAC/CtB,MAAM,GAAGqB,aAAa,CAACrB,MAAM;IAC7Bc,UAAU,GAAGO,aAAa,CAACP,UAAU;IACrCnB,cAAc,GAAG0B,aAAa,CAAC1B,cAAc;IAC7CoB,KAAK,GAAGM,aAAa,CAACN,KAAK,CAAC,CAAC;;EAEjC,IAAIQ,iBAAiB,GAAGxB,IAAI,CAACF,KAAK,CAAC,CAAC,EAAEyB,eAAe,CAAC;EAEtD,IAAIC,iBAAiB,CAACA,iBAAiB,CAACpC,MAAM,GAAG4B,KAAK,CAAC5B,MAAM,CAAC,KAAK4B,KAAK,EAAE;IACxEQ,iBAAiB,GAAGA,iBAAiB,CAAC1B,KAAK,CAAC,CAAC,EAAE0B,iBAAiB,CAACpC,MAAM,GAAG4B,KAAK,CAAC5B,MAAM,CAAC;EACzF;EAEA,IAAIoC,iBAAiB,EAAE;IACrBA,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACD,iBAAiB,CAAC,CAACC,MAAM,CAACT,KAAK,CAAC;EAChE,CAAC,CAAC;;EAGF,IAAIE,QAAQ,GAAGJ,UAAU,CAACd,IAAI,CAACF,KAAK,CAACF,cAAc,CAAC,EAAEmB,UAAU,CAACjB,KAAK,CAACF,cAAc,GAAG2B,eAAe,GAAGtB,MAAM,CAACb,MAAM,CAAC,EAAE4B,KAAK,CAAC;EAEhI,IAAIE,QAAQ,CAACpB,KAAK,CAAC,CAAC,EAAEkB,KAAK,CAAC5B,MAAM,CAAC,KAAK4B,KAAK,EAAE;IAC7CE,QAAQ,GAAGA,QAAQ,CAACpB,KAAK,CAACkB,KAAK,CAAC5B,MAAM,CAAC;EACzC;EAEA,IAAIsC,kBAAkB,GAAG,EAAE,CAACD,MAAM,CAACD,iBAAiB,CAAC,CAACC,MAAM,CAACxB,MAAM,CAAC,CAACwB,MAAM,CAACV,UAAU,CAAC,CAACU,MAAM,CAACT,KAAK,CAAC;EACrG,OAAO;IACLhB,IAAI,EAAE,EAAE,CAACyB,MAAM,CAACC,kBAAkB,CAAC,CAACD,MAAM,CAACP,QAAQ,CAAC;IACpDS,iBAAiB,EAAED,kBAAkB,CAACtC;EACxC,CAAC;AACH;AACA,OAAO,SAASwC,iBAAiBA,CAACjC,KAAK,EAAEe,QAAQ,EAAE;EACjDf,KAAK,CAACkC,iBAAiB,CAACnB,QAAQ,EAAEA,QAAQ,CAAC;EAC3C;AACF;AACA;AACA;;EAEEf,KAAK,CAACmC,IAAI,CAAC,CAAC;EACZnC,KAAK,CAACoC,KAAK,CAAC,CAAC;AACf;AACA,OAAO,SAASC,cAAcA,CAAChC,IAAI,EAAEiC,KAAK,EAAE;EAC1C,IAAIjB,KAAK,GAAGiB,KAAK,CAACjB,KAAK;EACvB,OAAO,CAACA,KAAK,IAAIhB,IAAI,CAACkC,OAAO,CAAClB,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7C;AACA,OAAO,SAASmB,YAAYA,CAACxC,KAAK,EAAEyC,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAGD,IAAI,CAACvC,KAAK;IACvBA,KAAK,GAAGwC,UAAU,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,UAAU;EACnD,IAAIC,SAAS,GAAG3C,KAAK,CAACkB,WAAW,CAAC,CAAC;EACnC,OAAOhB,KAAK,CAACgB,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACI,SAAS,CAAC,KAAK,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}