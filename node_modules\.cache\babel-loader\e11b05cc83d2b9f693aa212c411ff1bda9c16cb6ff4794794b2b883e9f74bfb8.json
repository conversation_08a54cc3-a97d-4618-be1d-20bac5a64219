{"ast": null, "code": "export function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport var isClient = typeof window !== 'undefined' && window.document && window.document.documentElement;\n/** Is client side and not jsdom */\n\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && isClient;", "map": {"version": 3, "names": ["toArray", "value", "Array", "isArray", "undefined", "isClient", "window", "document", "documentElement", "isBrowserClient", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/utils/commonUtil.js"], "sourcesContent": ["export function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return value !== undefined ? [value] : [];\n}\nexport var isClient = typeof window !== 'undefined' && window.document && window.document.documentElement;\n/** Is client side and not jsdom */\n\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && isClient;"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EACd;EAEA,OAAOA,KAAK,KAAKG,SAAS,GAAG,CAACH,KAAK,CAAC,GAAG,EAAE;AAC3C;AACA,OAAO,IAAII,QAAQ,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,eAAe;AACzG;;AAEA,OAAO,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}