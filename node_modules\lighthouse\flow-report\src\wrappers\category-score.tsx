/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

import {FunctionComponent} from 'preact';

import {renderCategoryScore} from '../../../report/renderer/api';
import {useExternalRenderer} from '../util';

export const CategoryScore: FunctionComponent<{
  category: LH.ReportResult.Category,
  href: string,
  gatherMode: LH.Result.GatherMode,
}> = ({category, href, gatherMode}) => {
  const ref = useExternalRenderer<HTMLDivElement>(() => {
    return renderCategoryScore(category, {
      gatherMode,
      omitLabel: true,
      onPageAnchorRendered: link => link.href = href,
    });
  }, [category, href]);

  return (
    <div ref={ref} data-testid="CategoryScore"/>
  );
};
