/**
 * @param {LH.Artifacts.NetworkRequest[]} records
 */
export function getJavaScriptURLs(records: LH.Artifacts.NetworkRequest[]): Set<string>;
/**
 * @param {LH.Artifacts.TaskNode} task
 * @param {Set<string>} jsURLs
 * @return {string}
 */
export function getAttributableURLForTask(task: LH.Artifacts.TaskNode, jsURLs: Set<string>): string;
/**
 * @param {LH.Artifacts.TaskNode[]} tasks
 * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
 * @return {Map<string, Record<string, number>>}
 */
export function getExecutionTimingsByURL(tasks: LH.Artifacts.TaskNode[], networkRecords: Array<LH.Artifacts.NetworkRequest>): Map<string, Record<string, number>>;
import { NetworkRequest } from "../network-request.js";
//# sourceMappingURL=task-summary.d.ts.map