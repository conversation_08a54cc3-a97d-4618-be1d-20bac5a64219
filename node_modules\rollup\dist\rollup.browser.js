/*
  @license
	Rollup.js v2.72.0
	Thu, 05 May 2022 04:32:50 GMT - commit 19aef1315cf45b04c74c37a290cbef8072ddfa6b

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
var e,t;e=this,t=function(e){for(var t="2.72.0",s={},i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n=0;n<i.length;n++)s[i.charCodeAt(n)]=n;function r(e,t,s){4===s?e.push([t[0],t[1],t[2],t[3]]):5===s?e.push([t[0],t[1],t[2],t[3],t[4]]):1===s&&e.push([t[0]])}function a(e){var t="";e=e<0?-e<<1|1:e<<1;do{var s=31&e;(e>>>=5)>0&&(s|=32),t+=i[s]}while(e>0);return t}class o{constructor(e){this.bits=e instanceof o?e.bits.slice():[]}add(e){this.bits[e>>5]|=1<<(31&e)}has(e){return!!(this.bits[e>>5]&1<<(31&e))}}class h{constructor(e,t,s){this.start=e,this.end=t,this.original=s,this.intro="",this.outro="",this.content=s,this.storeName=!1,this.edited=!1,Object.defineProperties(this,{previous:{writable:!0,value:null},next:{writable:!0,value:null}})}appendLeft(e){this.outro+=e}appendRight(e){this.intro=this.intro+e}clone(){const e=new h(this.start,this.end,this.original);return e.intro=this.intro,e.outro=this.outro,e.content=this.content,e.storeName=this.storeName,e.edited=this.edited,e}contains(e){return this.start<e&&e<this.end}eachNext(e){let t=this;for(;t;)e(t),t=t.next}eachPrevious(e){let t=this;for(;t;)e(t),t=t.previous}edit(e,t,s){return this.content=e,s||(this.intro="",this.outro=""),this.storeName=t,this.edited=!0,this}prependLeft(e){this.outro=e+this.outro}prependRight(e){this.intro=e+this.intro}split(e){const t=e-this.start,s=this.original.slice(0,t),i=this.original.slice(t);this.original=s;const n=new h(e,this.end,i);return n.outro=this.outro,this.outro="",this.end=e,this.edited?(n.edit("",!1),this.content=""):this.content=s,n.next=this.next,n.next&&(n.next.previous=n),n.previous=this,this.next=n,n}toString(){return this.intro+this.content+this.outro}trimEnd(e){if(this.outro=this.outro.replace(e,""),this.outro.length)return!0;const t=this.content.replace(e,"");return t.length?(t!==this.content&&this.split(this.start+t.length).edit("",void 0,!0),!0):(this.edit("",void 0,!0),this.intro=this.intro.replace(e,""),!!this.intro.length||void 0)}trimStart(e){if(this.intro=this.intro.replace(e,""),this.intro.length)return!0;const t=this.content.replace(e,"");return t.length?(t!==this.content&&(this.split(this.end-t.length),this.edit("",void 0,!0)),!0):(this.edit("",void 0,!0),this.outro=this.outro.replace(e,""),!!this.outro.length||void 0)}}let l=()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")};"undefined"!=typeof window&&"function"==typeof window.btoa?l=e=>window.btoa(unescape(encodeURIComponent(e))):"function"==typeof Buffer&&(l=e=>Buffer.from(e,"utf-8").toString("base64"));class c{constructor(e){this.version=3,this.file=e.file,this.sources=e.sources,this.sourcesContent=e.sourcesContent,this.names=e.names,this.mappings=function(e){for(var t=0,s=0,i=0,n=0,r="",o=0;o<e.length;o++){var h=e[o];if(o>0&&(r+=";"),0!==h.length){for(var l=0,c=[],u=0,d=h;u<d.length;u++){var p=d[u],f=a(p[0]-l);l=p[0],p.length>1&&(f+=a(p[1]-t)+a(p[2]-s)+a(p[3]-i),t=p[1],s=p[2],i=p[3]),5===p.length&&(f+=a(p[4]-n),n=p[4]),c.push(f)}r+=c.join(",")}}return r}(e.mappings)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+l(this.toString())}}function u(e){const t=e.split("\n"),s=t.filter((e=>/^\t+/.test(e))),i=t.filter((e=>/^ {2,}/.test(e)));if(0===s.length&&0===i.length)return null;if(s.length>=i.length)return"\t";const n=i.reduce(((e,t)=>{const s=/^ +/.exec(t)[0].length;return Math.min(s,e)}),1/0);return new Array(n+1).join(" ")}function d(e,t){const s=e.split(/[/\\]/),i=t.split(/[/\\]/);for(s.pop();s[0]===i[0];)s.shift(),i.shift();if(s.length){let e=s.length;for(;e--;)s[e]=".."}return s.concat(i).join("/")}const p=Object.prototype.toString;function f(e){return"[object Object]"===p.call(e)}function m(e){const t=e.split("\n"),s=[];for(let e=0,i=0;e<t.length;e++)s.push(i),i+=t[e].length+1;return function(e){let t=0,i=s.length;for(;t<i;){const n=t+i>>1;e<s[n]?i=n:t=n+1}const n=t-1;return{line:n,column:e-s[n]}}}class g{constructor(e){this.hires=e,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(e,t,s,i){if(t.length){const t=[this.generatedCodeColumn,e,s.line,s.column];i>=0&&t.push(i),this.rawSegments.push(t)}else this.pending&&this.rawSegments.push(this.pending);this.advance(t),this.pending=null}addUneditedChunk(e,t,s,i,n){let r=t.start,a=!0;for(;r<t.end;)(this.hires||a||n.has(r))&&this.rawSegments.push([this.generatedCodeColumn,e,i.line,i.column]),"\n"===s[r]?(i.line+=1,i.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,a=!0):(i.column+=1,this.generatedCodeColumn+=1,a=!1),r+=1;this.pending=null}advance(e){if(!e)return;const t=e.split("\n");if(t.length>1){for(let e=0;e<t.length-1;e++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=t[t.length-1].length}}const y="\n",E={insertLeft:!1,insertRight:!1,storeName:!1};class x{constructor(e,t={}){const s=new h(0,e.length,e);Object.defineProperties(this,{original:{writable:!0,value:e},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:s},lastChunk:{writable:!0,value:s},lastSearchedChunk:{writable:!0,value:s},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:t.filename},indentExclusionRanges:{writable:!0,value:t.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new o},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:u(e)}}),this.byStart[0]=s,this.byEnd[e.length]=s}addSourcemapLocation(e){this.sourcemapLocations.add(e)}append(e){if("string"!=typeof e)throw new TypeError("outro content must be a string");return this.outro+=e,this}appendLeft(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const s=this.byEnd[e];return s?s.appendLeft(t):this.intro+=t,this}appendRight(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const s=this.byStart[e];return s?s.appendRight(t):this.outro+=t,this}clone(){const e=new x(this.original,{filename:this.filename});let t=this.firstChunk,s=e.firstChunk=e.lastSearchedChunk=t.clone();for(;t;){e.byStart[s.start]=s,e.byEnd[s.end]=s;const i=t.next,n=i&&i.clone();n&&(s.next=n,n.previous=s,s=n),t=i}return e.lastChunk=s,this.indentExclusionRanges&&(e.indentExclusionRanges=this.indentExclusionRanges.slice()),e.sourcemapLocations=new o(this.sourcemapLocations),e.intro=this.intro,e.outro=this.outro,e}generateDecodedMap(e){e=e||{};const t=Object.keys(this.storedNames),s=new g(e.hires),i=m(this.original);return this.intro&&s.advance(this.intro),this.firstChunk.eachNext((e=>{const n=i(e.start);e.intro.length&&s.advance(e.intro),e.edited?s.addEdit(0,e.content,n,e.storeName?t.indexOf(e.original):-1):s.addUneditedChunk(0,e,this.original,n,this.sourcemapLocations),e.outro.length&&s.advance(e.outro)})),{file:e.file?e.file.split(/[/\\]/).pop():null,sources:[e.source?d(e.file||"",e.source):null],sourcesContent:e.includeContent?[this.original]:[null],names:t,mappings:s.raw}}generateMap(e){return new c(this.generateDecodedMap(e))}getIndentString(){return null===this.indentStr?"\t":this.indentStr}indent(e,t){const s=/^[^\r\n]/gm;if(f(e)&&(t=e,e=void 0),""===(e=void 0!==e?e:this.indentStr||"\t"))return this;const i={};(t=t||{}).exclude&&("number"==typeof t.exclude[0]?[t.exclude]:t.exclude).forEach((e=>{for(let t=e[0];t<e[1];t+=1)i[t]=!0}));let n=!1!==t.indentStart;const r=t=>n?`${e}${t}`:(n=!0,t);this.intro=this.intro.replace(s,r);let a=0,o=this.firstChunk;for(;o;){const t=o.end;if(o.edited)i[a]||(o.content=o.content.replace(s,r),o.content.length&&(n="\n"===o.content[o.content.length-1]));else for(a=o.start;a<t;){if(!i[a]){const t=this.original[a];"\n"===t?n=!0:"\r"!==t&&n&&(n=!1,a===o.start||(this._splitChunk(o,a),o=o.next),o.prependRight(e))}a+=1}a=o.end,o=o.next}return this.outro=this.outro.replace(s,r),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(e,t){return E.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),E.insertLeft=!0),this.appendLeft(e,t)}insertRight(e,t){return E.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),E.insertRight=!0),this.prependRight(e,t)}move(e,t,s){if(s>=e&&s<=t)throw new Error("Cannot move a selection inside itself");this._split(e),this._split(t),this._split(s);const i=this.byStart[e],n=this.byEnd[t],r=i.previous,a=n.next,o=this.byStart[s];if(!o&&n===this.lastChunk)return this;const h=o?o.previous:this.lastChunk;return r&&(r.next=a),a&&(a.previous=r),h&&(h.next=i),o&&(o.previous=n),i.previous||(this.firstChunk=n.next),n.next||(this.lastChunk=i.previous,this.lastChunk.next=null),i.previous=h,n.next=o||null,h||(this.firstChunk=i),o||(this.lastChunk=n),this}overwrite(e,t,s,i){if("string"!=typeof s)throw new TypeError("replacement content must be a string");for(;e<0;)e+=this.original.length;for(;t<0;)t+=this.original.length;if(t>this.original.length)throw new Error("end is out of bounds");if(e===t)throw new Error("Cannot overwrite a zero-length range – use appendLeft or prependRight instead");this._split(e),this._split(t),!0===i&&(E.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),E.storeName=!0),i={storeName:!0});const n=void 0!==i&&i.storeName,r=void 0!==i&&i.contentOnly;if(n){const s=this.original.slice(e,t);Object.defineProperty(this.storedNames,s,{writable:!0,value:!0,enumerable:!0})}const a=this.byStart[e],o=this.byEnd[t];if(a){let e=a;for(;e!==o;){if(e.next!==this.byStart[e.end])throw new Error("Cannot overwrite across a split point");e=e.next,e.edit("",!1)}a.edit(s,n,r)}else{const i=new h(e,t,"").edit(s,n);o.next=i,i.previous=o}return this}prepend(e){if("string"!=typeof e)throw new TypeError("outro content must be a string");return this.intro=e+this.intro,this}prependLeft(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const s=this.byEnd[e];return s?s.prependLeft(t):this.intro=t+this.intro,this}prependRight(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const s=this.byStart[e];return s?s.prependRight(t):this.outro=t+this.outro,this}remove(e,t){for(;e<0;)e+=this.original.length;for(;t<0;)t+=this.original.length;if(e===t)return this;if(e<0||t>this.original.length)throw new Error("Character is out of bounds");if(e>t)throw new Error("end must be greater than start");this._split(e),this._split(t);let s=this.byStart[e];for(;s;)s.intro="",s.outro="",s.edit(""),s=t>s.end?this.byStart[s.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let e=this.lastChunk;do{if(e.outro.length)return e.outro[e.outro.length-1];if(e.content.length)return e.content[e.content.length-1];if(e.intro.length)return e.intro[e.intro.length-1]}while(e=e.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let e=this.outro.lastIndexOf(y);if(-1!==e)return this.outro.substr(e+1);let t=this.outro,s=this.lastChunk;do{if(s.outro.length>0){if(e=s.outro.lastIndexOf(y),-1!==e)return s.outro.substr(e+1)+t;t=s.outro+t}if(s.content.length>0){if(e=s.content.lastIndexOf(y),-1!==e)return s.content.substr(e+1)+t;t=s.content+t}if(s.intro.length>0){if(e=s.intro.lastIndexOf(y),-1!==e)return s.intro.substr(e+1)+t;t=s.intro+t}}while(s=s.previous);return e=this.intro.lastIndexOf(y),-1!==e?this.intro.substr(e+1)+t:this.intro+t}slice(e=0,t=this.original.length){for(;e<0;)e+=this.original.length;for(;t<0;)t+=this.original.length;let s="",i=this.firstChunk;for(;i&&(i.start>e||i.end<=e);){if(i.start<t&&i.end>=t)return s;i=i.next}if(i&&i.edited&&i.start!==e)throw new Error(`Cannot use replaced character ${e} as slice start anchor.`);const n=i;for(;i;){!i.intro||n===i&&i.start!==e||(s+=i.intro);const r=i.start<t&&i.end>=t;if(r&&i.edited&&i.end!==t)throw new Error(`Cannot use replaced character ${t} as slice end anchor.`);const a=n===i?e-i.start:0,o=r?i.content.length+t-i.end:i.content.length;if(s+=i.content.slice(a,o),!i.outro||r&&i.end!==t||(s+=i.outro),r)break;i=i.next}return s}snip(e,t){const s=this.clone();return s.remove(0,e),s.remove(t,s.original.length),s}_split(e){if(this.byStart[e]||this.byEnd[e])return;let t=this.lastSearchedChunk;const s=e>t.end;for(;t;){if(t.contains(e))return this._splitChunk(t,e);t=s?this.byStart[t.end]:this.byEnd[t.start]}}_splitChunk(e,t){if(e.edited&&e.content.length){const s=m(this.original)(t);throw new Error(`Cannot split a chunk that has already been edited (${s.line}:${s.column} – "${e.original}")`)}const s=e.split(t);return this.byEnd[t]=e,this.byStart[t]=s,this.byEnd[s.end]=s,e===this.lastChunk&&(this.lastChunk=s),this.lastSearchedChunk=e,!0}toString(){let e=this.intro,t=this.firstChunk;for(;t;)e+=t.toString(),t=t.next;return e+this.outro}isEmpty(){let e=this.firstChunk;do{if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim())return!1}while(e=e.next);return!0}length(){let e=this.firstChunk,t=0;do{t+=e.intro.length+e.content.length+e.outro.length}while(e=e.next);return t}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimEndAborted(e){const t=new RegExp((e||"\\s")+"+$");if(this.outro=this.outro.replace(t,""),this.outro.length)return!0;let s=this.lastChunk;do{const e=s.end,i=s.trimEnd(t);if(s.end!==e&&(this.lastChunk===s&&(this.lastChunk=s.next),this.byEnd[s.end]=s,this.byStart[s.next.start]=s.next,this.byEnd[s.next.end]=s.next),i)return!0;s=s.previous}while(s);return!1}trimEnd(e){return this.trimEndAborted(e),this}trimStartAborted(e){const t=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(t,""),this.intro.length)return!0;let s=this.firstChunk;do{const e=s.end,i=s.trimStart(t);if(s.end!==e&&(s===this.lastChunk&&(this.lastChunk=s.next),this.byEnd[s.end]=s,this.byStart[s.next.start]=s.next,this.byEnd[s.next.end]=s.next),i)return!0;s=s.next}while(s);return!1}trimStart(e){return this.trimStartAborted(e),this}hasChanged(){return this.original!==this.toString()}replace(e,t){function s(e,s){return"string"==typeof t?t.replace(/\$(\$|&|\d+)/g,((t,s)=>"$"===s?"$":"&"===s?e[0]:+s<e.length?e[+s]:`$${s}`)):t(...e,e.index,s,e.groups)}if("string"!=typeof e&&e.global)(function(e,t){let s;const i=[];for(;s=e.exec(t);)i.push(s);return i})(e,this.original).forEach((e=>{null!=e.index&&this.overwrite(e.index,e.index+e[0].length,s(e,this.original))}));else{const t=this.original.match(e);t&&null!=t.index&&this.overwrite(t.index,t.index+t[0].length,s(t,this.original))}return this}}const b=Object.prototype.hasOwnProperty;class v{constructor(e={}){this.intro=e.intro||"",this.separator=void 0!==e.separator?e.separator:"\n",this.sources=[],this.uniqueSources=[],this.uniqueSourceIndexByFilename={}}addSource(e){if(e instanceof x)return this.addSource({content:e,filename:e.filename,separator:this.separator});if(!f(e)||!e.content)throw new Error("bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`");if(["filename","indentExclusionRanges","separator"].forEach((t=>{b.call(e,t)||(e[t]=e.content[t])})),void 0===e.separator&&(e.separator=this.separator),e.filename)if(b.call(this.uniqueSourceIndexByFilename,e.filename)){const t=this.uniqueSources[this.uniqueSourceIndexByFilename[e.filename]];if(e.content.original!==t.content)throw new Error(`Illegal source: same filename (${e.filename}), different contents`)}else this.uniqueSourceIndexByFilename[e.filename]=this.uniqueSources.length,this.uniqueSources.push({filename:e.filename,content:e.content.original});return this.sources.push(e),this}append(e,t){return this.addSource({content:new x(e),separator:t&&t.separator||""}),this}clone(){const e=new v({intro:this.intro,separator:this.separator});return this.sources.forEach((t=>{e.addSource({filename:t.filename,content:t.content.clone(),separator:t.separator})})),e}generateDecodedMap(e={}){const t=[];this.sources.forEach((e=>{Object.keys(e.content.storedNames).forEach((e=>{~t.indexOf(e)||t.push(e)}))}));const s=new g(e.hires);return this.intro&&s.advance(this.intro),this.sources.forEach(((e,i)=>{i>0&&s.advance(this.separator);const n=e.filename?this.uniqueSourceIndexByFilename[e.filename]:-1,r=e.content,a=m(r.original);r.intro&&s.advance(r.intro),r.firstChunk.eachNext((i=>{const o=a(i.start);i.intro.length&&s.advance(i.intro),e.filename?i.edited?s.addEdit(n,i.content,o,i.storeName?t.indexOf(i.original):-1):s.addUneditedChunk(n,i,r.original,o,r.sourcemapLocations):s.advance(i.content),i.outro.length&&s.advance(i.outro)})),r.outro&&s.advance(r.outro)})),{file:e.file?e.file.split(/[/\\]/).pop():null,sources:this.uniqueSources.map((t=>e.file?d(e.file,t.filename):t.filename)),sourcesContent:this.uniqueSources.map((t=>e.includeContent?t.content:null)),names:t,mappings:s.raw}}generateMap(e){return new c(this.generateDecodedMap(e))}getIndentString(){const e={};return this.sources.forEach((t=>{const s=t.content.indentStr;null!==s&&(e[s]||(e[s]=0),e[s]+=1)})),Object.keys(e).sort(((t,s)=>e[t]-e[s]))[0]||"\t"}indent(e){if(arguments.length||(e=this.getIndentString()),""===e)return this;let t=!this.intro||"\n"===this.intro.slice(-1);return this.sources.forEach(((s,i)=>{const n=void 0!==s.separator?s.separator:this.separator,r=t||i>0&&/\r?\n$/.test(n);s.content.indent(e,{exclude:s.indentExclusionRanges,indentStart:r}),t="\n"===s.content.lastChar()})),this.intro&&(this.intro=e+this.intro.replace(/^[^\n]/gm,((t,s)=>s>0?e+t:t))),this}prepend(e){return this.intro=e+this.intro,this}toString(){const e=this.sources.map(((e,t)=>{const s=void 0!==e.separator?e.separator:this.separator;return(t>0?s:"")+e.content.toString()})).join("");return this.intro+e}isEmpty(){return!(this.intro.length&&this.intro.trim()||this.sources.some((e=>!e.content.isEmpty())))}length(){return this.sources.reduce(((e,t)=>e+t.content.length()),this.intro.length)}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimStart(e){const t=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(t,""),!this.intro){let t,s=0;do{if(t=this.sources[s++],!t)break}while(!t.content.trimStartAborted(e))}return this}trimEnd(e){const t=new RegExp((e||"\\s")+"+$");let s,i=this.sources.length-1;do{if(s=this.sources[i--],!s){this.intro=this.intro.replace(t,"");break}}while(!s.content.trimEndAborted(e));return this}}const A=/^(?:\/|(?:[A-Za-z]:)?[\\|/])/,S=/^\.?\.\//,P=/\\/g,k=/[/\\]/,w=/\.[^.]+$/;function C(e){return A.test(e)}function I(e){return S.test(e)}function N(e){return e.replace(P,"/")}function _(e){return e.split(k).pop()||""}function $(e){const t=/[/\\][^/\\]*$/.exec(e);if(!t)return".";const s=e.slice(0,-t[0].length);return s||"/"}function T(e){const t=w.exec(_(e));return t?t[0]:""}function R(e,t){const s=e.split(k).filter(Boolean),i=t.split(k).filter(Boolean);for("."===s[0]&&s.shift(),"."===i[0]&&i.shift();s[0]&&i[0]&&s[0]===i[0];)s.shift(),i.shift();for(;".."===i[0]&&s.length>0;)i.shift(),s.pop();for(;s.pop();)i.unshift("..");return i.join("/")}function M(...e){const t=e.shift();if(!t)return"/";let s=t.split(k);for(const t of e)if(C(t))s=t.split(k);else{const e=t.split(k);for(;"."===e[0]||".."===e[0];)".."===e.shift()&&s.pop();s.push(...e)}return s.join("/")}function D(e,t,s){const i=e.get(t);if(i)return i;const n=s();return e.set(t,n),n}const L=Symbol("Unknown Key"),O=Symbol("Unknown Integer"),V=[],B=[L],F=[O],z=Symbol("Entities");class W{constructor(){this.entityPaths=Object.create(null,{[z]:{value:new Set}})}trackEntityAtPathAndGetIfTracked(e,t){const s=this.getEntities(e);return!!s.has(t)||(s.add(t),!1)}withTrackedEntityAtPath(e,t,s,i){const n=this.getEntities(e);if(n.has(t))return i;n.add(t);const r=s();return n.delete(t),r}getEntities(e){let t=this.entityPaths;for(const s of e)t=t[s]=t[s]||Object.create(null,{[z]:{value:new Set}});return t[z]}}const j=new W;class U{constructor(){this.entityPaths=Object.create(null,{[z]:{value:new Map}})}trackEntityAtPathAndGetIfTracked(e,t,s){let i=this.entityPaths;for(const t of e)i=i[t]=i[t]||Object.create(null,{[z]:{value:new Map}});const n=D(i[z],t,(()=>new Set));return!!n.has(s)||(n.add(s),!1)}}const G=Symbol("Unknown Value");class H{constructor(){this.included=!1}deoptimizePath(e){}deoptimizeThisOnEventAtPath(e,t,s,i){s.deoptimizePath(B)}getLiteralValueAtPath(e,t,s){return G}getReturnExpressionWhenCalledAtPath(e,t,s,i){return q}hasEffectsWhenAccessedAtPath(e,t){return!0}hasEffectsWhenAssignedAtPath(e,t){return!0}hasEffectsWhenCalledAtPath(e,t,s){return!0}include(e,t){this.included=!0}includeCallArguments(e,t){for(const s of t)s.include(e,!1)}}const q=new class extends H{};class K extends H{constructor(e){super(),this.name=e,this.alwaysRendered=!1,this.initReached=!1,this.isId=!1,this.isReassigned=!1,this.kind=null,this.renderBaseName=null,this.renderName=null}addReference(e){}getBaseVariableName(){return this.renderBaseName||this.renderName||this.name}getName(e){const t=this.renderName||this.name;return this.renderBaseName?`${this.renderBaseName}${e(t)}`:t}hasEffectsWhenAccessedAtPath(e,t){return e.length>0}include(){this.included=!0}markCalledFromTryStatement(){}setRenderNames(e,t){this.renderBaseName=e,this.renderName=t}}class X extends K{constructor(e,t){super(t),this.referenced=!1,this.module=e,this.isNamespace="*"===t}addReference(e){this.referenced=!0,"default"!==this.name&&"*"!==this.name||this.module.suggestName(e.name)}hasEffectsWhenAccessedAtPath(e){return e.length>(this.isNamespace?1:0)}include(){this.included||(this.included=!0,this.module.used=!0)}}const Y=Object.freeze(Object.create(null)),Q=Object.freeze({}),Z=Object.freeze([]);function J(e,t,s){if("number"==typeof s)throw new Error("locate takes a { startIndex, offsetLine, offsetColumn } object as the third argument");return function(e,t){void 0===t&&(t={});var s=t.offsetLine||0,i=t.offsetColumn||0,n=e.split("\n"),r=0,a=n.map((function(e,t){var s=r+e.length+1,i={start:r,end:s,line:t};return r=s,i})),o=0;function h(e,t){return e.start<=t&&t<e.end}function l(e,t){return{line:s+e.line,column:i+t-e.start,character:t}}return function(t,s){"string"==typeof t&&(t=e.indexOf(t,s||0));for(var i=a[o],n=t>=i.end?1:-1;i;){if(h(i,t))return l(i,t);i=a[o+=n]}}}(e,s)(t,s&&s.startIndex)}function ee(e){return e.replace(/^\t+/,(e=>e.split("\t").join("  ")))}function te(e,t){const s=e.length<=1,i=e.map((e=>`"${e}"`));let n=s?i[0]:`${i.slice(0,-1).join(", ")} and ${i.slice(-1)[0]}`;return t&&(n+=` ${s?t[0]:t[1]}`),n}function se(e){const t=_(e);return t.substring(0,t.length-T(e).length)}function ie(e){return C(e)?R(M(),e):e}function ne(e){return"/"===e[0]||"."===e[0]&&("/"===e[1]||"."===e[1])||C(e)}const re=/^(\.\.\/)*\.\.$/;function ae(e,t,s,i){let n=N(R($(e),t));if(s&&n.endsWith(".js")&&(n=n.slice(0,-3)),i){if(""===n)return"../"+_(t);if(re.test(n))return n.split("/").concat(["..",_(t)]).join("/")}return n?n.startsWith("..")?n:"./"+n:"."}function oe(e){throw e instanceof Error||(e=Object.assign(new Error(e.message),e)),e}function he(e,t,s,i){if("object"==typeof t){const{line:s,column:n}=t;e.loc={column:n,file:i,line:s}}else{e.pos=t;const{line:n,column:r}=J(s,t,{offsetLine:1});e.loc={column:r,file:i,line:n}}if(void 0===e.frame){const{line:t,column:i}=e.loc;e.frame=function(e,t,s){let i=e.split("\n");const n=Math.max(0,t-3);let r=Math.min(t+2,i.length);for(i=i.slice(n,r);!/\S/.test(i[i.length-1]);)i.pop(),r-=1;const a=String(r).length;return i.map(((e,i)=>{const r=n+i+1===t;let o=String(i+n+1);for(;o.length<a;)o=` ${o}`;if(r){const t=function(e){let t="";for(;e--;)t+=" ";return t}(a+2+ee(e.slice(0,s)).length)+"^";return`${o}: ${ee(e)}\n${t}`}return`${o}: ${ee(e)}`})).join("\n")}(s,t,i)}}var le;function ce({fileName:e,code:t},s){const i={code:le.CHUNK_INVALID,message:`Chunk "${e}" is not valid JavaScript: ${s.message}.`};return he(i,s.loc,t,e),i}function ue(e,t,s){return{code:"INVALID_EXPORT_OPTION",message:`"${e}" was specified for "output.exports", but entry module "${ie(s)}" has the following exports: ${t.join(", ")}`}}function de(e,t,s,i){return{code:le.INVALID_OPTION,message:`Invalid value ${void 0!==i?`${JSON.stringify(i)} `:""}for option "${e}" - ${s}.`,url:`https://rollupjs.org/guide/en/#${t}`}}function pe(e,t,s){return{code:le.MISSING_EXPORT,message:`'${e}' is not exported by ${ie(s)}, imported by ${ie(t)}`,url:"https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module"}}function fe(e){const t=Array.from(e.implicitlyLoadedBefore,(e=>ie(e.id))).sort();return{code:le.MISSING_IMPLICIT_DEPENDANT,message:`Module "${ie(e.id)}" that should be implicitly loaded before ${te(t)} is not included in the module graph. Either it was not imported by an included module or only via a tree-shaken dynamic import, or no imported bindings were used and it had otherwise no side-effects.`}}function me(e,t,s){const i=s?"reexport":"import";return{code:le.UNEXPECTED_NAMED_IMPORT,id:e,message:`The named export "${t}" was ${i}ed from the external module ${ie(e)} even though its interop type is "defaultOnly". Either remove or change this ${i} or change the value of the "output.interop" option.`,url:"https://rollupjs.org/guide/en/#outputinterop"}}function ge(e){return{code:le.UNEXPECTED_NAMED_IMPORT,id:e,message:`There was a namespace "*" reexport from the external module ${ie(e)} even though its interop type is "defaultOnly". This will be ignored as namespace reexports only reexport named exports. If this is not intended, either remove or change this reexport or change the value of the "output.interop" option.`,url:"https://rollupjs.org/guide/en/#outputinterop"}}function ye(e){return{code:le.VALIDATION_ERROR,message:e}}function Ee(){return{code:le.ALREADY_CLOSED,message:'Bundle is already closed, no more calls to "generate" or "write" are allowed.'}}function xe(e,t,s){be(e,t,s.onwarn,s.strictDeprecations)}function be(e,t,s,i){if(t||i){const t=function(e){return{code:le.DEPRECATED_FEATURE,..."string"==typeof e?{message:e}:e}}(e);if(i)return oe(t);s(t)}}!function(e){e.ALREADY_CLOSED="ALREADY_CLOSED",e.ASSET_NOT_FINALISED="ASSET_NOT_FINALISED",e.ASSET_NOT_FOUND="ASSET_NOT_FOUND",e.ASSET_SOURCE_ALREADY_SET="ASSET_SOURCE_ALREADY_SET",e.ASSET_SOURCE_MISSING="ASSET_SOURCE_MISSING",e.BAD_LOADER="BAD_LOADER",e.CANNOT_EMIT_FROM_OPTIONS_HOOK="CANNOT_EMIT_FROM_OPTIONS_HOOK",e.CHUNK_NOT_GENERATED="CHUNK_NOT_GENERATED",e.CHUNK_INVALID="CHUNK_INVALID",e.CIRCULAR_REEXPORT="CIRCULAR_REEXPORT",e.CYCLIC_CROSS_CHUNK_REEXPORT="CYCLIC_CROSS_CHUNK_REEXPORT",e.DEPRECATED_FEATURE="DEPRECATED_FEATURE",e.EXTERNAL_SYNTHETIC_EXPORTS="EXTERNAL_SYNTHETIC_EXPORTS",e.FILE_NAME_CONFLICT="FILE_NAME_CONFLICT",e.FILE_NOT_FOUND="FILE_NOT_FOUND",e.INPUT_HOOK_IN_OUTPUT_PLUGIN="INPUT_HOOK_IN_OUTPUT_PLUGIN",e.INVALID_CHUNK="INVALID_CHUNK",e.INVALID_EXPORT_OPTION="INVALID_EXPORT_OPTION",e.INVALID_EXTERNAL_ID="INVALID_EXTERNAL_ID",e.INVALID_OPTION="INVALID_OPTION",e.INVALID_PLUGIN_HOOK="INVALID_PLUGIN_HOOK",e.INVALID_ROLLUP_PHASE="INVALID_ROLLUP_PHASE",e.MISSING_EXPORT="MISSING_EXPORT",e.MISSING_IMPLICIT_DEPENDANT="MISSING_IMPLICIT_DEPENDANT",e.MIXED_EXPORTS="MIXED_EXPORTS",e.NAMESPACE_CONFLICT="NAMESPACE_CONFLICT",e.AMBIGUOUS_EXTERNAL_NAMESPACES="AMBIGUOUS_EXTERNAL_NAMESPACES",e.NO_TRANSFORM_MAP_OR_AST_WITHOUT_CODE="NO_TRANSFORM_MAP_OR_AST_WITHOUT_CODE",e.PLUGIN_ERROR="PLUGIN_ERROR",e.PREFER_NAMED_EXPORTS="PREFER_NAMED_EXPORTS",e.SYNTHETIC_NAMED_EXPORTS_NEED_NAMESPACE_EXPORT="SYNTHETIC_NAMED_EXPORTS_NEED_NAMESPACE_EXPORT",e.UNEXPECTED_NAMED_IMPORT="UNEXPECTED_NAMED_IMPORT",e.UNRESOLVED_ENTRY="UNRESOLVED_ENTRY",e.UNRESOLVED_IMPORT="UNRESOLVED_IMPORT",e.VALIDATION_ERROR="VALIDATION_ERROR"}(le||(le={}));var ve=new Set(["await","break","case","catch","class","const","continue","debugger","default","delete","do","else","enum","eval","export","extends","false","finally","for","function","if","implements","import","in","instanceof","interface","let","NaN","new","null","package","private","protected","public","return","static","super","switch","this","throw","true","try","typeof","undefined","var","void","while","with","yield"]);const Ae=/[^$_a-zA-Z0-9]/g,Se=e=>/\d/.test(e[0]);function Pe(e){return e=e.replace(/-(\w)/g,((e,t)=>t.toUpperCase())).replace(Ae,"_"),(Se(e)||ve.has(e))&&(e=`_${e}`),e||"_"}class ke{constructor(e,t,s,i,n){this.options=e,this.id=t,this.renormalizeRenderPath=n,this.declarations=new Map,this.defaultVariableName="",this.dynamicImporters=[],this.execIndex=1/0,this.exportedVariables=new Map,this.importers=[],this.mostCommonSuggestion=0,this.nameSuggestions=new Map,this.namespaceVariableName="",this.reexported=!1,this.renderPath=void 0,this.used=!1,this.variableName="",this.suggestedVariableName=Pe(t.split(/[\\/]/).pop());const{importers:r,dynamicImporters:a}=this,o=this.info={ast:null,code:null,dynamicallyImportedIdResolutions:Z,dynamicallyImportedIds:Z,get dynamicImporters(){return a.sort()},hasDefaultExport:null,get hasModuleSideEffects(){return xe("Accessing ModuleInfo.hasModuleSideEffects from plugins is deprecated. Please use ModuleInfo.moduleSideEffects instead.",!1,e),o.moduleSideEffects},id:t,implicitlyLoadedAfterOneOf:Z,implicitlyLoadedBefore:Z,importedIdResolutions:Z,importedIds:Z,get importers(){return r.sort()},isEntry:!1,isExternal:!0,isIncluded:null,meta:i,moduleSideEffects:s,syntheticNamedExports:!1};Object.defineProperty(this.info,"hasModuleSideEffects",{enumerable:!1})}getVariableForExportName(e){const t=this.declarations.get(e);if(t)return[t];const s=new X(this,e);return this.declarations.set(e,s),this.exportedVariables.set(s,e),[s]}setRenderPath(e,t){this.renderPath="function"==typeof e.paths?e.paths(this.id):e.paths[this.id],this.renderPath||(this.renderPath=this.renormalizeRenderPath?N(R(t,this.id)):this.id)}suggestName(e){var t;const s=(null!==(t=this.nameSuggestions.get(e))&&void 0!==t?t:0)+1;this.nameSuggestions.set(e,s),s>this.mostCommonSuggestion&&(this.mostCommonSuggestion=s,this.suggestedVariableName=e)}warnUnusedImports(){const e=Array.from(this.declarations).filter((([e,t])=>"*"!==e&&!t.included&&!this.reexported&&!t.referenced)).map((([e])=>e));if(0===e.length)return;const t=new Set;for(const s of e)for(const e of this.declarations.get(s).module.importers)t.add(e);const s=[...t];this.options.onwarn({code:"UNUSED_EXTERNAL_IMPORT",message:`${te(e,["is","are"])} imported from external module "${this.id}" but never used in ${te(s.map((e=>ie(e))))}.`,names:e,source:this.id,sources:s})}}const we={ArrayPattern(e,t){for(const s of t.elements)s&&we[s.type](e,s)},AssignmentPattern(e,t){we[t.left.type](e,t.left)},Identifier(e,t){e.push(t.name)},MemberExpression(){},ObjectPattern(e,t){for(const s of t.properties)"RestElement"===s.type?we.RestElement(e,s):we[s.value.type](e,s.value)},RestElement(e,t){we[t.argument.type](e,t.argument)}},Ce=function(e){const t=[];return we[e.type](t,e),t};function Ie(){return{brokenFlow:0,includedCallArguments:new Set,includedLabels:new Set}}function Ne(){return{accessed:new W,assigned:new W,brokenFlow:0,called:new U,ignore:{breaks:!1,continues:!1,labels:new Set,returnYield:!1},includedLabels:new Set,instantiated:new U,replacedVariableInits:new Map}}new Set("break case class catch const continue debugger default delete do else export extends finally for function if import in instanceof let new return super switch this throw try typeof var void while with yield enum await implements package protected static interface private public arguments Infinity NaN undefined null true false eval uneval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Symbol Error EvalError InternalError RangeError ReferenceError SyntaxError TypeError URIError Number Math Date String RegExp Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array Map Set WeakMap WeakSet SIMD ArrayBuffer DataView JSON Promise Generator GeneratorFunction Reflect Proxy Intl".split(" ")).add("");const _e=[];function $e(e,t=null){return Object.create(t,e)}const Te=new class extends H{getLiteralValueAtPath(){}},Re={value:{callsArgs:null,returns:q}},Me=new class extends H{getReturnExpressionWhenCalledAtPath(e){return 1===e.length?Ge(ze,e[0]):q}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenCalledAtPath(e,t,s){return 1!==e.length||Ue(ze,e[0],t,s)}},De={value:{callsArgs:null,returns:Me}},Le=new class extends H{getReturnExpressionWhenCalledAtPath(e){return 1===e.length?Ge(We,e[0]):q}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenCalledAtPath(e,t,s){return 1!==e.length||Ue(We,e[0],t,s)}},Oe={value:{callsArgs:null,returns:Le}},Ve=new class extends H{getReturnExpressionWhenCalledAtPath(e){return 1===e.length?Ge(je,e[0]):q}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenCalledAtPath(e,t,s){return 1!==e.length||Ue(je,e[0],t,s)}},Be={value:{callsArgs:null,returns:Ve}},Fe=$e({hasOwnProperty:De,isPrototypeOf:De,propertyIsEnumerable:De,toLocaleString:Be,toString:Be,valueOf:Re}),ze=$e({valueOf:De},Fe),We=$e({toExponential:Be,toFixed:Be,toLocaleString:Be,toPrecision:Be,valueOf:Oe},Fe),je=$e({anchor:Be,at:Re,big:Be,blink:Be,bold:Be,charAt:Be,charCodeAt:Oe,codePointAt:Re,concat:Be,endsWith:De,fixed:Be,fontcolor:Be,fontsize:Be,includes:De,indexOf:Oe,italics:Be,lastIndexOf:Oe,link:Be,localeCompare:Oe,match:Re,matchAll:Re,normalize:Be,padEnd:Be,padStart:Be,repeat:Be,replace:{value:{callsArgs:[1],returns:Ve}},replaceAll:{value:{callsArgs:[1],returns:Ve}},search:Oe,slice:Be,small:Be,split:Re,startsWith:De,strike:Be,sub:Be,substr:Be,substring:Be,sup:Be,toLocaleLowerCase:Be,toLocaleUpperCase:Be,toLowerCase:Be,toString:Be,toUpperCase:Be,trim:Be,trimEnd:Be,trimLeft:Be,trimRight:Be,trimStart:Be,valueOf:Be},Fe);function Ue(e,t,s,i){if("string"!=typeof t||!e[t])return!0;if(!e[t].callsArgs)return!1;for(const n of e[t].callsArgs)if(s.args[n]&&s.args[n].hasEffectsWhenCalledAtPath(V,{args:_e,thisParam:null,withNew:!1},i))return!0;return!1}function Ge(e,t){return"string"==typeof t&&e[t]?e[t].returns:q}function He(e,t,s){s(e,t)}function qe(e,t,s){}var Ke={};Ke.Program=Ke.BlockStatement=Ke.StaticBlock=function(e,t,s){for(var i=0,n=e.body;i<n.length;i+=1)s(n[i],t,"Statement")},Ke.Statement=He,Ke.EmptyStatement=qe,Ke.ExpressionStatement=Ke.ParenthesizedExpression=Ke.ChainExpression=function(e,t,s){return s(e.expression,t,"Expression")},Ke.IfStatement=function(e,t,s){s(e.test,t,"Expression"),s(e.consequent,t,"Statement"),e.alternate&&s(e.alternate,t,"Statement")},Ke.LabeledStatement=function(e,t,s){return s(e.body,t,"Statement")},Ke.BreakStatement=Ke.ContinueStatement=qe,Ke.WithStatement=function(e,t,s){s(e.object,t,"Expression"),s(e.body,t,"Statement")},Ke.SwitchStatement=function(e,t,s){s(e.discriminant,t,"Expression");for(var i=0,n=e.cases;i<n.length;i+=1){var r=n[i];r.test&&s(r.test,t,"Expression");for(var a=0,o=r.consequent;a<o.length;a+=1)s(o[a],t,"Statement")}},Ke.SwitchCase=function(e,t,s){e.test&&s(e.test,t,"Expression");for(var i=0,n=e.consequent;i<n.length;i+=1)s(n[i],t,"Statement")},Ke.ReturnStatement=Ke.YieldExpression=Ke.AwaitExpression=function(e,t,s){e.argument&&s(e.argument,t,"Expression")},Ke.ThrowStatement=Ke.SpreadElement=function(e,t,s){return s(e.argument,t,"Expression")},Ke.TryStatement=function(e,t,s){s(e.block,t,"Statement"),e.handler&&s(e.handler,t),e.finalizer&&s(e.finalizer,t,"Statement")},Ke.CatchClause=function(e,t,s){e.param&&s(e.param,t,"Pattern"),s(e.body,t,"Statement")},Ke.WhileStatement=Ke.DoWhileStatement=function(e,t,s){s(e.test,t,"Expression"),s(e.body,t,"Statement")},Ke.ForStatement=function(e,t,s){e.init&&s(e.init,t,"ForInit"),e.test&&s(e.test,t,"Expression"),e.update&&s(e.update,t,"Expression"),s(e.body,t,"Statement")},Ke.ForInStatement=Ke.ForOfStatement=function(e,t,s){s(e.left,t,"ForInit"),s(e.right,t,"Expression"),s(e.body,t,"Statement")},Ke.ForInit=function(e,t,s){"VariableDeclaration"===e.type?s(e,t):s(e,t,"Expression")},Ke.DebuggerStatement=qe,Ke.FunctionDeclaration=function(e,t,s){return s(e,t,"Function")},Ke.VariableDeclaration=function(e,t,s){for(var i=0,n=e.declarations;i<n.length;i+=1)s(n[i],t)},Ke.VariableDeclarator=function(e,t,s){s(e.id,t,"Pattern"),e.init&&s(e.init,t,"Expression")},Ke.Function=function(e,t,s){e.id&&s(e.id,t,"Pattern");for(var i=0,n=e.params;i<n.length;i+=1)s(n[i],t,"Pattern");s(e.body,t,e.expression?"Expression":"Statement")},Ke.Pattern=function(e,t,s){"Identifier"===e.type?s(e,t,"VariablePattern"):"MemberExpression"===e.type?s(e,t,"MemberPattern"):s(e,t)},Ke.VariablePattern=qe,Ke.MemberPattern=He,Ke.RestElement=function(e,t,s){return s(e.argument,t,"Pattern")},Ke.ArrayPattern=function(e,t,s){for(var i=0,n=e.elements;i<n.length;i+=1){var r=n[i];r&&s(r,t,"Pattern")}},Ke.ObjectPattern=function(e,t,s){for(var i=0,n=e.properties;i<n.length;i+=1){var r=n[i];"Property"===r.type?(r.computed&&s(r.key,t,"Expression"),s(r.value,t,"Pattern")):"RestElement"===r.type&&s(r.argument,t,"Pattern")}},Ke.Expression=He,Ke.ThisExpression=Ke.Super=Ke.MetaProperty=qe,Ke.ArrayExpression=function(e,t,s){for(var i=0,n=e.elements;i<n.length;i+=1){var r=n[i];r&&s(r,t,"Expression")}},Ke.ObjectExpression=function(e,t,s){for(var i=0,n=e.properties;i<n.length;i+=1)s(n[i],t)},Ke.FunctionExpression=Ke.ArrowFunctionExpression=Ke.FunctionDeclaration,Ke.SequenceExpression=function(e,t,s){for(var i=0,n=e.expressions;i<n.length;i+=1)s(n[i],t,"Expression")},Ke.TemplateLiteral=function(e,t,s){for(var i=0,n=e.quasis;i<n.length;i+=1)s(n[i],t);for(var r=0,a=e.expressions;r<a.length;r+=1)s(a[r],t,"Expression")},Ke.TemplateElement=qe,Ke.UnaryExpression=Ke.UpdateExpression=function(e,t,s){s(e.argument,t,"Expression")},Ke.BinaryExpression=Ke.LogicalExpression=function(e,t,s){s(e.left,t,"Expression"),s(e.right,t,"Expression")},Ke.AssignmentExpression=Ke.AssignmentPattern=function(e,t,s){s(e.left,t,"Pattern"),s(e.right,t,"Expression")},Ke.ConditionalExpression=function(e,t,s){s(e.test,t,"Expression"),s(e.consequent,t,"Expression"),s(e.alternate,t,"Expression")},Ke.NewExpression=Ke.CallExpression=function(e,t,s){if(s(e.callee,t,"Expression"),e.arguments)for(var i=0,n=e.arguments;i<n.length;i+=1)s(n[i],t,"Expression")},Ke.MemberExpression=function(e,t,s){s(e.object,t,"Expression"),e.computed&&s(e.property,t,"Expression")},Ke.ExportNamedDeclaration=Ke.ExportDefaultDeclaration=function(e,t,s){e.declaration&&s(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&s(e.source,t,"Expression")},Ke.ExportAllDeclaration=function(e,t,s){e.exported&&s(e.exported,t),s(e.source,t,"Expression")},Ke.ImportDeclaration=function(e,t,s){for(var i=0,n=e.specifiers;i<n.length;i+=1)s(n[i],t);s(e.source,t,"Expression")},Ke.ImportExpression=function(e,t,s){s(e.source,t,"Expression")},Ke.ImportSpecifier=Ke.ImportDefaultSpecifier=Ke.ImportNamespaceSpecifier=Ke.Identifier=Ke.PrivateIdentifier=Ke.Literal=qe,Ke.TaggedTemplateExpression=function(e,t,s){s(e.tag,t,"Expression"),s(e.quasi,t,"Expression")},Ke.ClassDeclaration=Ke.ClassExpression=function(e,t,s){return s(e,t,"Class")},Ke.Class=function(e,t,s){e.id&&s(e.id,t,"Pattern"),e.superClass&&s(e.superClass,t,"Expression"),s(e.body,t)},Ke.ClassBody=function(e,t,s){for(var i=0,n=e.body;i<n.length;i+=1)s(n[i],t)},Ke.MethodDefinition=Ke.PropertyDefinition=Ke.Property=function(e,t,s){e.computed&&s(e.key,t,"Expression"),e.value&&s(e.value,t,"Expression")};const Xe="ArrowFunctionExpression",Ye="BlockStatement",Qe="CallExpression",Ze="ExpressionStatement",Je="Identifier",et="Program";let tt="sourceMa";tt+="ppingURL";const st=new RegExp("^#[ \\f\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+sourceMappingURL=.+"),it="_rollupAnnotations",nt="_rollupRemoved";function rt(e,t,s=e.type){const{annotations:i}=t;let n=i[t.annotationIndex];for(;n&&e.start>=n.end;)ht(e,n,t.code),n=i[++t.annotationIndex];if(n&&n.end<=e.end)for(Ke[s](e,t,rt);(n=i[t.annotationIndex])&&n.end<=e.end;)++t.annotationIndex,ut(e,n,!1)}const at=/[^\s(]/g,ot=/\S/g;function ht(e,t,s){const i=[];let n;if(lt(s.slice(t.end,e.start),at)){const t=e.start;for(;;){switch(i.push(e),e.type){case Ze:case"ChainExpression":e=e.expression;continue;case"SequenceExpression":if(lt(s.slice(t,e.start),ot)){e=e.expressions[0];continue}n=!0;break;case"ConditionalExpression":if(lt(s.slice(t,e.start),ot)){e=e.test;continue}n=!0;break;case"LogicalExpression":case"BinaryExpression":if(lt(s.slice(t,e.start),ot)){e=e.left;continue}n=!0;break;case Qe:case"NewExpression":break;default:n=!0}break}}else n=!0;if(n)ut(e,t,!1);else for(const e of i)ut(e,t,!0)}function lt(e,t){let s;for(;null!==(s=t.exec(e));){if("/"===s[0]){const s=e.charCodeAt(t.lastIndex);if(42===s){t.lastIndex=e.indexOf("*/",t.lastIndex+1)+2;continue}if(47===s){t.lastIndex=e.indexOf("\n",t.lastIndex+1)+1;continue}}return t.lastIndex=0,!1}return!0}const ct=/[@#]__PURE__/;function ut(e,t,s){const i=s?it:nt,n=e[i];n?n.push(t):e[i]=[t]}const dt={Literal:[],Program:["body"]},pt="variables";class ft extends H{constructor(e,t,s){super(),this.esTreeNode=e,this.keys=dt[e.type]||function(e){return dt[e.type]=Object.keys(e).filter((t=>"object"==typeof e[t]&&95!==t.charCodeAt(0))),dt[e.type]}(e),this.parent=t,this.context=t.context,this.createScope(s),this.parseNode(e),this.initialise(),this.context.magicString.addSourcemapLocation(this.start),this.context.magicString.addSourcemapLocation(this.end)}addExportedVariables(e,t){}bind(){for(const e of this.keys){const t=this[e];if(null!==t)if(Array.isArray(t))for(const e of t)null!==e&&e.bind();else t.bind()}}createScope(e){this.scope=e}hasEffects(e){!1===this.deoptimized&&this.applyDeoptimizations();for(const t of this.keys){const s=this[t];if(null!==s)if(Array.isArray(s)){for(const t of s)if(null!==t&&t.hasEffects(e))return!0}else if(s.hasEffects(e))return!0}return!1}include(e,t){!1===this.deoptimized&&this.applyDeoptimizations(),this.included=!0;for(const s of this.keys){const i=this[s];if(null!==i)if(Array.isArray(i))for(const s of i)null!==s&&s.include(e,t);else i.include(e,t)}}includeAsSingleStatement(e,t){this.include(e,t)}initialise(){}insertSemicolon(e){";"!==e.original[this.end-1]&&e.appendLeft(this.end,";")}parseNode(e){for(const[t,s]of Object.entries(e))if(!this.hasOwnProperty(t))if(95===t.charCodeAt(0)){if(t===it)this.annotations=s;else if(t===nt)for(const{start:e,end:t}of s)this.context.magicString.remove(e,t)}else if("object"!=typeof s||null===s)this[t]=s;else if(Array.isArray(s)){this[t]=[];for(const e of s)this[t].push(null===e?null:new(this.context.getNodeConstructor(e.type))(e,this,this.scope))}else this[t]=new(this.context.getNodeConstructor(s.type))(s,this,this.scope)}render(e,t){for(const s of this.keys){const i=this[s];if(null!==i)if(Array.isArray(i))for(const s of i)null!==s&&s.render(e,t);else i.render(e,t)}}shouldBeIncluded(e){return this.included||!e.brokenFlow&&this.hasEffects(Ne())}applyDeoptimizations(){}}class mt extends ft{constructor(){super(...arguments),this.deoptimized=!1}deoptimizeThisOnEventAtPath(e,t,s,i){t.length>0&&this.argument.deoptimizeThisOnEventAtPath(e,[L,...t],s,i)}hasEffects(e){this.deoptimized||this.applyDeoptimizations();const{propertyReadSideEffects:t}=this.context.options.treeshake;return this.argument.hasEffects(e)||t&&("always"===t||this.argument.hasEffectsWhenAccessedAtPath(B,e))}applyDeoptimizations(){this.deoptimized=!0,this.argument.deoptimizePath([L,L]),this.context.requestTreeshakingPass()}}class gt extends H{constructor(e){super(),this.description=e}deoptimizeThisOnEventAtPath(e,t,s){2===e&&0===t.length&&this.description.mutatesSelfAsArray&&s.deoptimizePath(F)}getReturnExpressionWhenCalledAtPath(e,t){return e.length>0?q:this.description.returnsPrimitive||("self"===this.description.returns?t.thisParam||q:this.description.returns())}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenAssignedAtPath(e){return e.length>0}hasEffectsWhenCalledAtPath(e,t,s){var i,n;if(e.length>0||!0===this.description.mutatesSelfAsArray&&(null===(i=t.thisParam)||void 0===i?void 0:i.hasEffectsWhenAssignedAtPath(F,s)))return!0;if(!this.description.callsArgs)return!1;for(const e of this.description.callsArgs)if(null===(n=t.args[e])||void 0===n?void 0:n.hasEffectsWhenCalledAtPath(V,{args:_e,thisParam:null,withNew:!1},s))return!0;return!1}includeCallArguments(e,t){for(const s of t)s.include(e,!1)}}const yt=[new gt({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:Me})],Et=[new gt({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:Ve})],xt=[new gt({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:Le})],bt=[new gt({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:q})],vt=/^\d+$/;class At extends H{constructor(e,t,s=!1){if(super(),this.prototypeExpression=t,this.immutable=s,this.allProperties=[],this.deoptimizedPaths=Object.create(null),this.expressionsToBeDeoptimizedByKey=Object.create(null),this.gettersByKey=Object.create(null),this.hasUnknownDeoptimizedInteger=!1,this.hasUnknownDeoptimizedProperty=!1,this.propertiesAndGettersByKey=Object.create(null),this.propertiesAndSettersByKey=Object.create(null),this.settersByKey=Object.create(null),this.thisParametersToBeDeoptimized=new Set,this.unknownIntegerProps=[],this.unmatchableGetters=[],this.unmatchablePropertiesAndGetters=[],this.unmatchableSetters=[],Array.isArray(e))this.buildPropertyMaps(e);else{this.propertiesAndGettersByKey=this.propertiesAndSettersByKey=e;for(const t of Object.values(e))this.allProperties.push(...t)}}deoptimizeAllProperties(){var e;if(!this.hasUnknownDeoptimizedProperty){this.hasUnknownDeoptimizedProperty=!0;for(const e of Object.values(this.propertiesAndGettersByKey).concat(Object.values(this.settersByKey)))for(const t of e)t.deoptimizePath(B);null===(e=this.prototypeExpression)||void 0===e||e.deoptimizePath([L,L]),this.deoptimizeCachedEntities()}}deoptimizeIntegerProperties(){if(!this.hasUnknownDeoptimizedProperty&&!this.hasUnknownDeoptimizedInteger){this.hasUnknownDeoptimizedInteger=!0;for(const[e,t]of Object.entries(this.propertiesAndGettersByKey))if(vt.test(e))for(const e of t)e.deoptimizePath(B);this.deoptimizeCachedIntegerEntities()}}deoptimizePath(e){var t;if(this.hasUnknownDeoptimizedProperty||this.immutable)return;const s=e[0];if(1===e.length){if("string"!=typeof s)return s===O?this.deoptimizeIntegerProperties():this.deoptimizeAllProperties();if(!this.deoptimizedPaths[s]){this.deoptimizedPaths[s]=!0;const e=this.expressionsToBeDeoptimizedByKey[s];if(e)for(const t of e)t.deoptimizeCache()}}const i=1===e.length?B:e.slice(1);for(const e of"string"==typeof s?(this.propertiesAndGettersByKey[s]||this.unmatchablePropertiesAndGetters).concat(this.settersByKey[s]||this.unmatchableSetters):this.allProperties)e.deoptimizePath(i);null===(t=this.prototypeExpression)||void 0===t||t.deoptimizePath(1===e.length?[L,L]:e)}deoptimizeThisOnEventAtPath(e,t,s,i){var n;const[r,...a]=t;if(this.hasUnknownDeoptimizedProperty||(2===e||t.length>1)&&"string"==typeof r&&this.deoptimizedPaths[r])return void s.deoptimizePath(B);const[o,h,l]=2===e||t.length>1?[this.propertiesAndGettersByKey,this.propertiesAndGettersByKey,this.unmatchablePropertiesAndGetters]:0===e?[this.propertiesAndGettersByKey,this.gettersByKey,this.unmatchableGetters]:[this.propertiesAndSettersByKey,this.settersByKey,this.unmatchableSetters];if("string"==typeof r){if(o[r]){const t=h[r];if(t)for(const n of t)n.deoptimizeThisOnEventAtPath(e,a,s,i);return void(this.immutable||this.thisParametersToBeDeoptimized.add(s))}for(const t of l)t.deoptimizeThisOnEventAtPath(e,a,s,i);if(vt.test(r))for(const t of this.unknownIntegerProps)t.deoptimizeThisOnEventAtPath(e,a,s,i)}else{for(const t of Object.values(h).concat([l]))for(const n of t)n.deoptimizeThisOnEventAtPath(e,a,s,i);for(const t of this.unknownIntegerProps)t.deoptimizeThisOnEventAtPath(e,a,s,i)}this.immutable||this.thisParametersToBeDeoptimized.add(s),null===(n=this.prototypeExpression)||void 0===n||n.deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){if(0===e.length)return G;const i=e[0],n=this.getMemberExpressionAndTrackDeopt(i,s);return n?n.getLiteralValueAtPath(e.slice(1),t,s):this.prototypeExpression?this.prototypeExpression.getLiteralValueAtPath(e,t,s):1!==e.length?G:void 0}getReturnExpressionWhenCalledAtPath(e,t,s,i){if(0===e.length)return q;const n=e[0],r=this.getMemberExpressionAndTrackDeopt(n,i);return r?r.getReturnExpressionWhenCalledAtPath(e.slice(1),t,s,i):this.prototypeExpression?this.prototypeExpression.getReturnExpressionWhenCalledAtPath(e,t,s,i):q}hasEffectsWhenAccessedAtPath(e,t){const[s,...i]=e;if(e.length>1){if("string"!=typeof s)return!0;const n=this.getMemberExpression(s);return n?n.hasEffectsWhenAccessedAtPath(i,t):!this.prototypeExpression||this.prototypeExpression.hasEffectsWhenAccessedAtPath(e,t)}if(this.hasUnknownDeoptimizedProperty)return!0;if("string"==typeof s){if(this.propertiesAndGettersByKey[s]){const e=this.gettersByKey[s];if(e)for(const s of e)if(s.hasEffectsWhenAccessedAtPath(i,t))return!0;return!1}for(const e of this.unmatchableGetters)if(e.hasEffectsWhenAccessedAtPath(i,t))return!0}else for(const e of Object.values(this.gettersByKey).concat([this.unmatchableGetters]))for(const s of e)if(s.hasEffectsWhenAccessedAtPath(i,t))return!0;return!!this.prototypeExpression&&this.prototypeExpression.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){const[s,...i]=e;if(e.length>1){if("string"!=typeof s)return!0;const n=this.getMemberExpression(s);return n?n.hasEffectsWhenAssignedAtPath(i,t):!this.prototypeExpression||this.prototypeExpression.hasEffectsWhenAssignedAtPath(e,t)}if(this.hasUnknownDeoptimizedProperty)return!0;if("string"==typeof s){if(this.propertiesAndSettersByKey[s]){const e=this.settersByKey[s];if(e)for(const s of e)if(s.hasEffectsWhenAssignedAtPath(i,t))return!0;return!1}for(const e of this.unmatchableSetters)if(e.hasEffectsWhenAssignedAtPath(i,t))return!0}return!!this.prototypeExpression&&this.prototypeExpression.hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){const i=e[0],n=this.getMemberExpression(i);return n?n.hasEffectsWhenCalledAtPath(e.slice(1),t,s):!this.prototypeExpression||this.prototypeExpression.hasEffectsWhenCalledAtPath(e,t,s)}buildPropertyMaps(e){const{allProperties:t,propertiesAndGettersByKey:s,propertiesAndSettersByKey:i,settersByKey:n,gettersByKey:r,unknownIntegerProps:a,unmatchablePropertiesAndGetters:o,unmatchableGetters:h,unmatchableSetters:l}=this,c=[];for(let u=e.length-1;u>=0;u--){const{key:d,kind:p,property:f}=e[u];if(t.push(f),"string"!=typeof d){if(d===O){a.push(f);continue}"set"===p&&l.push(f),"get"===p&&h.push(f),"get"!==p&&c.push(f),"set"!==p&&o.push(f)}else"set"===p?i[d]||(i[d]=[f,...c],n[d]=[f,...l]):"get"===p?s[d]||(s[d]=[f,...o],r[d]=[f,...h]):(i[d]||(i[d]=[f,...c]),s[d]||(s[d]=[f,...o]))}}deoptimizeCachedEntities(){for(const e of Object.values(this.expressionsToBeDeoptimizedByKey))for(const t of e)t.deoptimizeCache();for(const e of this.thisParametersToBeDeoptimized)e.deoptimizePath(B)}deoptimizeCachedIntegerEntities(){for(const[e,t]of Object.entries(this.expressionsToBeDeoptimizedByKey))if(vt.test(e))for(const e of t)e.deoptimizeCache();for(const e of this.thisParametersToBeDeoptimized)e.deoptimizePath(F)}getMemberExpression(e){if(this.hasUnknownDeoptimizedProperty||"string"!=typeof e||this.hasUnknownDeoptimizedInteger&&vt.test(e)||this.deoptimizedPaths[e])return q;const t=this.propertiesAndGettersByKey[e];return 1===(null==t?void 0:t.length)?t[0]:t||this.unmatchablePropertiesAndGetters.length>0||this.unknownIntegerProps.length&&vt.test(e)?q:null}getMemberExpressionAndTrackDeopt(e,t){if("string"!=typeof e)return q;const s=this.getMemberExpression(e);return s===q||this.immutable||(this.expressionsToBeDeoptimizedByKey[e]=this.expressionsToBeDeoptimizedByKey[e]||[]).push(t),s}}const St=e=>"string"==typeof e&&/^\d+$/.test(e),Pt=new class extends H{deoptimizeThisOnEventAtPath(e,t,s){2!==e||1!==t.length||St(t[0])||s.deoptimizePath(B)}getLiteralValueAtPath(e){return 1===e.length&&St(e[0])?void 0:G}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenAssignedAtPath(e){return e.length>1}},kt=new At({__proto__:null,hasOwnProperty:yt,isPrototypeOf:yt,propertyIsEnumerable:yt,toLocaleString:Et,toString:Et,valueOf:bt},Pt,!0),wt=[{key:O,kind:"init",property:q},{key:"length",kind:"init",property:Le}],Ct=[new gt({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:Me})],It=[new gt({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:Le})],Nt=[new gt({callsArgs:null,mutatesSelfAsArray:!0,returns:()=>new At(wt,Vt),returnsPrimitive:null})],_t=[new gt({callsArgs:null,mutatesSelfAsArray:"deopt-only",returns:()=>new At(wt,Vt),returnsPrimitive:null})],$t=[new gt({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:()=>new At(wt,Vt),returnsPrimitive:null})],Tt=[new gt({callsArgs:null,mutatesSelfAsArray:!0,returns:null,returnsPrimitive:Le})],Rt=[new gt({callsArgs:null,mutatesSelfAsArray:!0,returns:null,returnsPrimitive:q})],Mt=[new gt({callsArgs:null,mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:q})],Dt=[new gt({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:q})],Lt=[new gt({callsArgs:null,mutatesSelfAsArray:!0,returns:"self",returnsPrimitive:null})],Ot=[new gt({callsArgs:[0],mutatesSelfAsArray:!0,returns:"self",returnsPrimitive:null})],Vt=new At({__proto__:null,at:Mt,concat:_t,copyWithin:Lt,entries:_t,every:Ct,fill:Lt,filter:$t,find:Dt,findIndex:It,findLast:Dt,findLastIndex:It,flat:_t,flatMap:$t,forEach:Dt,groupBy:Dt,groupByToMap:Dt,includes:yt,indexOf:xt,join:Et,keys:bt,lastIndexOf:xt,map:$t,pop:Rt,push:Tt,reduce:Dt,reduceRight:Dt,reverse:Lt,shift:Rt,slice:_t,some:Ct,sort:Ot,splice:Nt,toLocaleString:Et,toString:Et,unshift:Tt,values:Mt},kt,!0);class Bt extends K{constructor(e,t,s,i){super(e),this.calledFromTryStatement=!1,this.additionalInitializers=null,this.expressionsToBeDeoptimized=[],this.declarations=t?[t]:[],this.init=s,this.deoptimizationTracker=i.deoptimizationTracker,this.module=i.module}addDeclaration(e,t){this.declarations.push(e);const s=this.markInitializersForDeoptimization();null!==t&&s.push(t)}consolidateInitializers(){if(null!==this.additionalInitializers){for(const e of this.additionalInitializers)e.deoptimizePath(B);this.additionalInitializers=null}}deoptimizePath(e){var t,s;if(!this.isReassigned&&!this.deoptimizationTracker.trackEntityAtPathAndGetIfTracked(e,this))if(0===e.length){if(!this.isReassigned){this.isReassigned=!0;const e=this.expressionsToBeDeoptimized;this.expressionsToBeDeoptimized=[];for(const t of e)t.deoptimizeCache();null===(t=this.init)||void 0===t||t.deoptimizePath(B)}}else null===(s=this.init)||void 0===s||s.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){if(this.isReassigned||!this.init)return s.deoptimizePath(B);i.withTrackedEntityAtPath(t,this.init,(()=>this.init.deoptimizeThisOnEventAtPath(e,t,s,i)),void 0)}getLiteralValueAtPath(e,t,s){return this.isReassigned||!this.init?G:t.withTrackedEntityAtPath(e,this.init,(()=>(this.expressionsToBeDeoptimized.push(s),this.init.getLiteralValueAtPath(e,t,s))),G)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.isReassigned||!this.init?q:s.withTrackedEntityAtPath(e,this.init,(()=>(this.expressionsToBeDeoptimized.push(i),this.init.getReturnExpressionWhenCalledAtPath(e,t,s,i))),q)}hasEffectsWhenAccessedAtPath(e,t){return!!this.isReassigned||this.init&&!t.accessed.trackEntityAtPathAndGetIfTracked(e,this)&&this.init.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return!!this.included||0!==e.length&&(!!this.isReassigned||this.init&&!t.assigned.trackEntityAtPathAndGetIfTracked(e,this)&&this.init.hasEffectsWhenAssignedAtPath(e,t))}hasEffectsWhenCalledAtPath(e,t,s){return!!this.isReassigned||this.init&&!(t.withNew?s.instantiated:s.called).trackEntityAtPathAndGetIfTracked(e,t,this)&&this.init.hasEffectsWhenCalledAtPath(e,t,s)}include(){if(!this.included){this.included=!0;for(const e of this.declarations){e.included||e.include(Ie(),!1);let t=e.parent;for(;!t.included&&(t.included=!0,t.type!==et);)t=t.parent}}}includeCallArguments(e,t){if(this.isReassigned||this.init&&e.includedCallArguments.has(this.init))for(const s of t)s.include(e,!1);else this.init&&(e.includedCallArguments.add(this.init),this.init.includeCallArguments(e,t),e.includedCallArguments.delete(this.init))}markCalledFromTryStatement(){this.calledFromTryStatement=!0}markInitializersForDeoptimization(){return null===this.additionalInitializers&&(this.additionalInitializers=null===this.init?[]:[this.init],this.init=q,this.isReassigned=!0),this.additionalInitializers}}function Ft(e){let t="";do{const s=e%64;e=Math.floor(e/64),t="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_$"[s]+t}while(0!==e);return t}function zt(e,t){let s=e,i=1;for(;t.has(s)||ve.has(s);)s=`${e}$${Ft(i++)}`;return t.add(s),s}class Wt{constructor(){this.children=[],this.variables=new Map}addDeclaration(e,t,s,i){const n=e.name;let r=this.variables.get(n);return r?r.addDeclaration(e,s):(r=new Bt(e.name,e,s||Te,t),this.variables.set(n,r)),r}contains(e){return this.variables.has(e)}findVariable(e){throw new Error("Internal Error: findVariable needs to be implemented by a subclass")}}class jt extends Wt{constructor(e){super(),this.accessedOutsideVariables=new Map,this.parent=e,e.children.push(this)}addAccessedDynamicImport(e){(this.accessedDynamicImports||(this.accessedDynamicImports=new Set)).add(e),this.parent instanceof jt&&this.parent.addAccessedDynamicImport(e)}addAccessedGlobals(e,t){const s=t.get(this)||new Set;for(const t of e)s.add(t);t.set(this,s),this.parent instanceof jt&&this.parent.addAccessedGlobals(e,t)}addNamespaceMemberAccess(e,t){this.accessedOutsideVariables.set(e,t),this.parent.addNamespaceMemberAccess(e,t)}addReturnExpression(e){this.parent instanceof jt&&this.parent.addReturnExpression(e)}addUsedOutsideNames(e,t,s,i){for(const i of this.accessedOutsideVariables.values())i.included&&(e.add(i.getBaseVariableName()),"system"===t&&s.has(i)&&e.add("exports"));const n=i.get(this);if(n)for(const t of n)e.add(t)}contains(e){return this.variables.has(e)||this.parent.contains(e)}deconflict(e,t,s){const i=new Set;if(this.addUsedOutsideNames(i,e,t,s),this.accessedDynamicImports)for(const e of this.accessedDynamicImports)e.inlineNamespace&&i.add(e.inlineNamespace.getBaseVariableName());for(const[e,t]of this.variables)(t.included||t.alwaysRendered)&&t.setRenderNames(null,zt(e,i));for(const i of this.children)i.deconflict(e,t,s)}findLexicalBoundary(){return this.parent.findLexicalBoundary()}findVariable(e){const t=this.variables.get(e)||this.accessedOutsideVariables.get(e);if(t)return t;const s=this.parent.findVariable(e);return this.accessedOutsideVariables.set(e,s),s}}class Ut extends jt{constructor(e,t){super(e),this.parameters=[],this.hasRest=!1,this.context=t,this.hoistedBodyVarScope=new jt(this)}addParameterDeclaration(e){const t=e.name;let s=this.hoistedBodyVarScope.variables.get(t);return s?s.addDeclaration(e,null):s=new Bt(t,e,q,this.context),this.variables.set(t,s),s}addParameterVariables(e,t){this.parameters=e;for(const t of e)for(const e of t)e.alwaysRendered=!0;this.hasRest=t}includeCallArguments(e,t){let s=!1,i=!1;const n=this.hasRest&&this.parameters[this.parameters.length-1];for(const s of t)if(s instanceof mt){for(const s of t)s.include(e,!1);break}for(let r=t.length-1;r>=0;r--){const a=this.parameters[r]||n,o=t[r];if(a)if(s=!1,0===a.length)i=!0;else for(const e of a)e.included&&(i=!0),e.calledFromTryStatement&&(s=!0);!i&&o.shouldBeIncluded(e)&&(i=!0),i&&o.include(e,s)}}}class Gt extends Ut{constructor(){super(...arguments),this.returnExpression=null,this.returnExpressions=[]}addReturnExpression(e){this.returnExpressions.push(e)}getReturnExpression(){return null===this.returnExpression&&this.updateReturnExpression(),this.returnExpression}updateReturnExpression(){if(1===this.returnExpressions.length)this.returnExpression=this.returnExpressions[0];else{this.returnExpression=q;for(const e of this.returnExpressions)e.deoptimizePath(B)}}}function Ht(e,t,s,i){if(t.remove(s,i),e.annotations)for(const i of e.annotations){if(!(i.start<s))return;t.remove(i.start,i.end)}}function qt(e,t){if(e.annotations||e.parent.type!==Ze||(e=e.parent),e.annotations)for(const s of e.annotations)t.remove(s.start,s.end)}const Kt={isNoStatement:!0};function Xt(e,t,s=0){let i,n;for(i=e.indexOf(t,s);;){if(-1===(s=e.indexOf("/",s))||s>=i)return i;n=e.charCodeAt(++s),++s,(s=47===n?e.indexOf("\n",s)+1:e.indexOf("*/",s)+2)>i&&(i=e.indexOf(t,s))}}const Yt=/\S/g;function Qt(e,t){return Yt.lastIndex=t,Yt.exec(e).index}function Zt(e){let t,s,i=0;for(t=e.indexOf("\n",i);;){if(i=e.indexOf("/",i),-1===i||i>t)return[t,t+1];if(s=e.charCodeAt(i+1),47===s)return[i,t+1];i=e.indexOf("*/",i+3)+2,i>t&&(t=e.indexOf("\n",i))}}function Jt(e,t,s,i,n){let r,a,o,h,l=e[0],c=!l.included||l.needsBoundaries;c&&(h=s+Zt(t.original.slice(s,l.start))[1]);for(let s=1;s<=e.length;s++)r=l,a=h,o=c,l=e[s],c=void 0!==l&&(!l.included||l.needsBoundaries),o||c?(h=r.end+Zt(t.original.slice(r.end,void 0===l?i:l.start))[1],r.included?o?r.render(t,n,{end:h,start:a}):r.render(t,n):Ht(r,t,a,h)):r.render(t,n)}function es(e,t,s,i){const n=[];let r,a,o,h,l,c=s-1;for(let i=0;i<e.length;i++){for(a=e[i],void 0!==r&&(c=r.end+Xt(t.original.slice(r.end,a.start),",")),o=h=c+1+Zt(t.original.slice(c+1,a.start))[1];l=t.original.charCodeAt(o),32===l||9===l||10===l||13===l;)o++;void 0!==r&&n.push({contentEnd:h,end:o,node:r,separator:c,start:s}),r=a,s=o}return n.push({contentEnd:i,end:i,node:r,separator:null,start:s}),n}function ts(e,t,s){for(;;){const[i,n]=Zt(e.original.slice(t,s));if(-1===i)break;e.remove(t+i,t+=n)}}class ss extends jt{addDeclaration(e,t,s,i){if(i){const n=this.parent.addDeclaration(e,t,s,i);return n.markInitializersForDeoptimization(),n}return super.addDeclaration(e,t,s,!1)}}class is extends ft{initialise(){this.directive&&"use strict"!==this.directive&&this.parent.type===et&&this.context.warn({code:"MODULE_LEVEL_DIRECTIVE",message:`Module level directives cause errors when bundled, '${this.directive}' was ignored.`},this.start)}render(e,t){super.render(e,t),this.included&&this.insertSemicolon(e)}shouldBeIncluded(e){return this.directive&&"use strict"!==this.directive?this.parent.type!==et:super.shouldBeIncluded(e)}}class ns extends ft{constructor(){super(...arguments),this.directlyIncluded=!1}addImplicitReturnExpressionToScope(){const e=this.body[this.body.length-1];e&&"ReturnStatement"===e.type||this.scope.addReturnExpression(q)}createScope(e){this.scope=this.parent.preventChildBlockScope?e:new ss(e)}hasEffects(e){if(this.deoptimizeBody)return!0;for(const t of this.body){if(e.brokenFlow)break;if(t.hasEffects(e))return!0}return!1}include(e,t){if(!this.deoptimizeBody||!this.directlyIncluded){this.included=!0,this.directlyIncluded=!0,this.deoptimizeBody&&(t=!0);for(const s of this.body)(t||s.shouldBeIncluded(e))&&s.include(e,t)}}initialise(){const e=this.body[0];this.deoptimizeBody=e instanceof is&&"use asm"===e.directive}render(e,t){this.body.length?Jt(this.body,e,this.start+1,this.end-1,t):super.render(e,t)}}function rs(e,t){if("MemberExpression"===e.type)return!e.computed&&rs(e.object,e);if("Identifier"===e.type){if(!t)return!0;switch(t.type){case"MemberExpression":return t.computed||e===t.object;case"MethodDefinition":return t.computed;case"PropertyDefinition":case"Property":return t.computed||e===t.value;case"ExportSpecifier":case"ImportSpecifier":return e===t.local;case"LabeledStatement":case"BreakStatement":case"ContinueStatement":return!1;default:return!0}}return!1}const as=Symbol("Value Properties"),os={pure:!0},hs={pure:!1},ls={__proto__:null,[as]:hs},cs={__proto__:null,[as]:os},us={__proto__:null,[as]:hs,prototype:ls},ds={__proto__:null,[as]:os,prototype:ls},ps={__proto__:null,[as]:os,from:cs,of:cs,prototype:ls},fs={__proto__:null,[as]:os,supportedLocalesOf:ds},ms={global:ls,globalThis:ls,self:ls,window:ls,__proto__:null,[as]:hs,Array:{__proto__:null,[as]:hs,from:ls,isArray:cs,of:cs,prototype:ls},ArrayBuffer:{__proto__:null,[as]:os,isView:cs,prototype:ls},Atomics:ls,BigInt:us,BigInt64Array:us,BigUint64Array:us,Boolean:ds,constructor:us,DataView:ds,Date:{__proto__:null,[as]:os,now:cs,parse:cs,prototype:ls,UTC:cs},decodeURI:cs,decodeURIComponent:cs,encodeURI:cs,encodeURIComponent:cs,Error:ds,escape:cs,eval:ls,EvalError:ds,Float32Array:ps,Float64Array:ps,Function:us,hasOwnProperty:ls,Infinity:ls,Int16Array:ps,Int32Array:ps,Int8Array:ps,isFinite:cs,isNaN:cs,isPrototypeOf:ls,JSON:ls,Map:ds,Math:{__proto__:null,[as]:hs,abs:cs,acos:cs,acosh:cs,asin:cs,asinh:cs,atan:cs,atan2:cs,atanh:cs,cbrt:cs,ceil:cs,clz32:cs,cos:cs,cosh:cs,exp:cs,expm1:cs,floor:cs,fround:cs,hypot:cs,imul:cs,log:cs,log10:cs,log1p:cs,log2:cs,max:cs,min:cs,pow:cs,random:cs,round:cs,sign:cs,sin:cs,sinh:cs,sqrt:cs,tan:cs,tanh:cs,trunc:cs},NaN:ls,Number:{__proto__:null,[as]:os,isFinite:cs,isInteger:cs,isNaN:cs,isSafeInteger:cs,parseFloat:cs,parseInt:cs,prototype:ls},Object:{__proto__:null,[as]:os,create:cs,getOwnPropertyDescriptor:cs,getOwnPropertyNames:cs,getOwnPropertySymbols:cs,getPrototypeOf:cs,hasOwn:cs,is:cs,isExtensible:cs,isFrozen:cs,isSealed:cs,keys:cs,fromEntries:cs,entries:cs,prototype:ls},parseFloat:cs,parseInt:cs,Promise:{__proto__:null,[as]:hs,all:ls,prototype:ls,race:ls,reject:ls,resolve:ls},propertyIsEnumerable:ls,Proxy:ls,RangeError:ds,ReferenceError:ds,Reflect:ls,RegExp:ds,Set:ds,SharedArrayBuffer:us,String:{__proto__:null,[as]:os,fromCharCode:cs,fromCodePoint:cs,prototype:ls,raw:cs},Symbol:{__proto__:null,[as]:os,for:cs,keyFor:cs,prototype:ls},SyntaxError:ds,toLocaleString:ls,toString:ls,TypeError:ds,Uint16Array:ps,Uint32Array:ps,Uint8Array:ps,Uint8ClampedArray:ps,unescape:cs,URIError:ds,valueOf:ls,WeakMap:ds,WeakSet:ds,clearInterval:us,clearTimeout:us,console:ls,Intl:{__proto__:null,[as]:hs,Collator:fs,DateTimeFormat:fs,ListFormat:fs,NumberFormat:fs,PluralRules:fs,RelativeTimeFormat:fs},setInterval:us,setTimeout:us,TextDecoder:us,TextEncoder:us,URL:us,URLSearchParams:us,AbortController:us,AbortSignal:us,addEventListener:ls,alert:ls,AnalyserNode:us,Animation:us,AnimationEvent:us,applicationCache:ls,ApplicationCache:us,ApplicationCacheErrorEvent:us,atob:ls,Attr:us,Audio:us,AudioBuffer:us,AudioBufferSourceNode:us,AudioContext:us,AudioDestinationNode:us,AudioListener:us,AudioNode:us,AudioParam:us,AudioProcessingEvent:us,AudioScheduledSourceNode:us,AudioWorkletNode:us,BarProp:us,BaseAudioContext:us,BatteryManager:us,BeforeUnloadEvent:us,BiquadFilterNode:us,Blob:us,BlobEvent:us,blur:ls,BroadcastChannel:us,btoa:ls,ByteLengthQueuingStrategy:us,Cache:us,caches:ls,CacheStorage:us,cancelAnimationFrame:ls,cancelIdleCallback:ls,CanvasCaptureMediaStreamTrack:us,CanvasGradient:us,CanvasPattern:us,CanvasRenderingContext2D:us,ChannelMergerNode:us,ChannelSplitterNode:us,CharacterData:us,clientInformation:ls,ClipboardEvent:us,close:ls,closed:ls,CloseEvent:us,Comment:us,CompositionEvent:us,confirm:ls,ConstantSourceNode:us,ConvolverNode:us,CountQueuingStrategy:us,createImageBitmap:ls,Credential:us,CredentialsContainer:us,crypto:ls,Crypto:us,CryptoKey:us,CSS:us,CSSConditionRule:us,CSSFontFaceRule:us,CSSGroupingRule:us,CSSImportRule:us,CSSKeyframeRule:us,CSSKeyframesRule:us,CSSMediaRule:us,CSSNamespaceRule:us,CSSPageRule:us,CSSRule:us,CSSRuleList:us,CSSStyleDeclaration:us,CSSStyleRule:us,CSSStyleSheet:us,CSSSupportsRule:us,CustomElementRegistry:us,customElements:ls,CustomEvent:us,DataTransfer:us,DataTransferItem:us,DataTransferItemList:us,defaultstatus:ls,defaultStatus:ls,DelayNode:us,DeviceMotionEvent:us,DeviceOrientationEvent:us,devicePixelRatio:ls,dispatchEvent:ls,document:ls,Document:us,DocumentFragment:us,DocumentType:us,DOMError:us,DOMException:us,DOMImplementation:us,DOMMatrix:us,DOMMatrixReadOnly:us,DOMParser:us,DOMPoint:us,DOMPointReadOnly:us,DOMQuad:us,DOMRect:us,DOMRectReadOnly:us,DOMStringList:us,DOMStringMap:us,DOMTokenList:us,DragEvent:us,DynamicsCompressorNode:us,Element:us,ErrorEvent:us,Event:us,EventSource:us,EventTarget:us,external:ls,fetch:ls,File:us,FileList:us,FileReader:us,find:ls,focus:ls,FocusEvent:us,FontFace:us,FontFaceSetLoadEvent:us,FormData:us,frames:ls,GainNode:us,Gamepad:us,GamepadButton:us,GamepadEvent:us,getComputedStyle:ls,getSelection:ls,HashChangeEvent:us,Headers:us,history:ls,History:us,HTMLAllCollection:us,HTMLAnchorElement:us,HTMLAreaElement:us,HTMLAudioElement:us,HTMLBaseElement:us,HTMLBodyElement:us,HTMLBRElement:us,HTMLButtonElement:us,HTMLCanvasElement:us,HTMLCollection:us,HTMLContentElement:us,HTMLDataElement:us,HTMLDataListElement:us,HTMLDetailsElement:us,HTMLDialogElement:us,HTMLDirectoryElement:us,HTMLDivElement:us,HTMLDListElement:us,HTMLDocument:us,HTMLElement:us,HTMLEmbedElement:us,HTMLFieldSetElement:us,HTMLFontElement:us,HTMLFormControlsCollection:us,HTMLFormElement:us,HTMLFrameElement:us,HTMLFrameSetElement:us,HTMLHeadElement:us,HTMLHeadingElement:us,HTMLHRElement:us,HTMLHtmlElement:us,HTMLIFrameElement:us,HTMLImageElement:us,HTMLInputElement:us,HTMLLabelElement:us,HTMLLegendElement:us,HTMLLIElement:us,HTMLLinkElement:us,HTMLMapElement:us,HTMLMarqueeElement:us,HTMLMediaElement:us,HTMLMenuElement:us,HTMLMetaElement:us,HTMLMeterElement:us,HTMLModElement:us,HTMLObjectElement:us,HTMLOListElement:us,HTMLOptGroupElement:us,HTMLOptionElement:us,HTMLOptionsCollection:us,HTMLOutputElement:us,HTMLParagraphElement:us,HTMLParamElement:us,HTMLPictureElement:us,HTMLPreElement:us,HTMLProgressElement:us,HTMLQuoteElement:us,HTMLScriptElement:us,HTMLSelectElement:us,HTMLShadowElement:us,HTMLSlotElement:us,HTMLSourceElement:us,HTMLSpanElement:us,HTMLStyleElement:us,HTMLTableCaptionElement:us,HTMLTableCellElement:us,HTMLTableColElement:us,HTMLTableElement:us,HTMLTableRowElement:us,HTMLTableSectionElement:us,HTMLTemplateElement:us,HTMLTextAreaElement:us,HTMLTimeElement:us,HTMLTitleElement:us,HTMLTrackElement:us,HTMLUListElement:us,HTMLUnknownElement:us,HTMLVideoElement:us,IDBCursor:us,IDBCursorWithValue:us,IDBDatabase:us,IDBFactory:us,IDBIndex:us,IDBKeyRange:us,IDBObjectStore:us,IDBOpenDBRequest:us,IDBRequest:us,IDBTransaction:us,IDBVersionChangeEvent:us,IdleDeadline:us,IIRFilterNode:us,Image:us,ImageBitmap:us,ImageBitmapRenderingContext:us,ImageCapture:us,ImageData:us,indexedDB:ls,innerHeight:ls,innerWidth:ls,InputEvent:us,IntersectionObserver:us,IntersectionObserverEntry:us,isSecureContext:ls,KeyboardEvent:us,KeyframeEffect:us,length:ls,localStorage:ls,location:ls,Location:us,locationbar:ls,matchMedia:ls,MediaDeviceInfo:us,MediaDevices:us,MediaElementAudioSourceNode:us,MediaEncryptedEvent:us,MediaError:us,MediaKeyMessageEvent:us,MediaKeySession:us,MediaKeyStatusMap:us,MediaKeySystemAccess:us,MediaList:us,MediaQueryList:us,MediaQueryListEvent:us,MediaRecorder:us,MediaSettingsRange:us,MediaSource:us,MediaStream:us,MediaStreamAudioDestinationNode:us,MediaStreamAudioSourceNode:us,MediaStreamEvent:us,MediaStreamTrack:us,MediaStreamTrackEvent:us,menubar:ls,MessageChannel:us,MessageEvent:us,MessagePort:us,MIDIAccess:us,MIDIConnectionEvent:us,MIDIInput:us,MIDIInputMap:us,MIDIMessageEvent:us,MIDIOutput:us,MIDIOutputMap:us,MIDIPort:us,MimeType:us,MimeTypeArray:us,MouseEvent:us,moveBy:ls,moveTo:ls,MutationEvent:us,MutationObserver:us,MutationRecord:us,name:ls,NamedNodeMap:us,NavigationPreloadManager:us,navigator:ls,Navigator:us,NetworkInformation:us,Node:us,NodeFilter:ls,NodeIterator:us,NodeList:us,Notification:us,OfflineAudioCompletionEvent:us,OfflineAudioContext:us,offscreenBuffering:ls,OffscreenCanvas:us,open:ls,openDatabase:ls,Option:us,origin:ls,OscillatorNode:us,outerHeight:ls,outerWidth:ls,PageTransitionEvent:us,pageXOffset:ls,pageYOffset:ls,PannerNode:us,parent:ls,Path2D:us,PaymentAddress:us,PaymentRequest:us,PaymentRequestUpdateEvent:us,PaymentResponse:us,performance:ls,Performance:us,PerformanceEntry:us,PerformanceLongTaskTiming:us,PerformanceMark:us,PerformanceMeasure:us,PerformanceNavigation:us,PerformanceNavigationTiming:us,PerformanceObserver:us,PerformanceObserverEntryList:us,PerformancePaintTiming:us,PerformanceResourceTiming:us,PerformanceTiming:us,PeriodicWave:us,Permissions:us,PermissionStatus:us,personalbar:ls,PhotoCapabilities:us,Plugin:us,PluginArray:us,PointerEvent:us,PopStateEvent:us,postMessage:ls,Presentation:us,PresentationAvailability:us,PresentationConnection:us,PresentationConnectionAvailableEvent:us,PresentationConnectionCloseEvent:us,PresentationConnectionList:us,PresentationReceiver:us,PresentationRequest:us,print:ls,ProcessingInstruction:us,ProgressEvent:us,PromiseRejectionEvent:us,prompt:ls,PushManager:us,PushSubscription:us,PushSubscriptionOptions:us,queueMicrotask:ls,RadioNodeList:us,Range:us,ReadableStream:us,RemotePlayback:us,removeEventListener:ls,Request:us,requestAnimationFrame:ls,requestIdleCallback:ls,resizeBy:ls,ResizeObserver:us,ResizeObserverEntry:us,resizeTo:ls,Response:us,RTCCertificate:us,RTCDataChannel:us,RTCDataChannelEvent:us,RTCDtlsTransport:us,RTCIceCandidate:us,RTCIceTransport:us,RTCPeerConnection:us,RTCPeerConnectionIceEvent:us,RTCRtpReceiver:us,RTCRtpSender:us,RTCSctpTransport:us,RTCSessionDescription:us,RTCStatsReport:us,RTCTrackEvent:us,screen:ls,Screen:us,screenLeft:ls,ScreenOrientation:us,screenTop:ls,screenX:ls,screenY:ls,ScriptProcessorNode:us,scroll:ls,scrollbars:ls,scrollBy:ls,scrollTo:ls,scrollX:ls,scrollY:ls,SecurityPolicyViolationEvent:us,Selection:us,ServiceWorker:us,ServiceWorkerContainer:us,ServiceWorkerRegistration:us,sessionStorage:ls,ShadowRoot:us,SharedWorker:us,SourceBuffer:us,SourceBufferList:us,speechSynthesis:ls,SpeechSynthesisEvent:us,SpeechSynthesisUtterance:us,StaticRange:us,status:ls,statusbar:ls,StereoPannerNode:us,stop:ls,Storage:us,StorageEvent:us,StorageManager:us,styleMedia:ls,StyleSheet:us,StyleSheetList:us,SubtleCrypto:us,SVGAElement:us,SVGAngle:us,SVGAnimatedAngle:us,SVGAnimatedBoolean:us,SVGAnimatedEnumeration:us,SVGAnimatedInteger:us,SVGAnimatedLength:us,SVGAnimatedLengthList:us,SVGAnimatedNumber:us,SVGAnimatedNumberList:us,SVGAnimatedPreserveAspectRatio:us,SVGAnimatedRect:us,SVGAnimatedString:us,SVGAnimatedTransformList:us,SVGAnimateElement:us,SVGAnimateMotionElement:us,SVGAnimateTransformElement:us,SVGAnimationElement:us,SVGCircleElement:us,SVGClipPathElement:us,SVGComponentTransferFunctionElement:us,SVGDefsElement:us,SVGDescElement:us,SVGDiscardElement:us,SVGElement:us,SVGEllipseElement:us,SVGFEBlendElement:us,SVGFEColorMatrixElement:us,SVGFEComponentTransferElement:us,SVGFECompositeElement:us,SVGFEConvolveMatrixElement:us,SVGFEDiffuseLightingElement:us,SVGFEDisplacementMapElement:us,SVGFEDistantLightElement:us,SVGFEDropShadowElement:us,SVGFEFloodElement:us,SVGFEFuncAElement:us,SVGFEFuncBElement:us,SVGFEFuncGElement:us,SVGFEFuncRElement:us,SVGFEGaussianBlurElement:us,SVGFEImageElement:us,SVGFEMergeElement:us,SVGFEMergeNodeElement:us,SVGFEMorphologyElement:us,SVGFEOffsetElement:us,SVGFEPointLightElement:us,SVGFESpecularLightingElement:us,SVGFESpotLightElement:us,SVGFETileElement:us,SVGFETurbulenceElement:us,SVGFilterElement:us,SVGForeignObjectElement:us,SVGGElement:us,SVGGeometryElement:us,SVGGradientElement:us,SVGGraphicsElement:us,SVGImageElement:us,SVGLength:us,SVGLengthList:us,SVGLinearGradientElement:us,SVGLineElement:us,SVGMarkerElement:us,SVGMaskElement:us,SVGMatrix:us,SVGMetadataElement:us,SVGMPathElement:us,SVGNumber:us,SVGNumberList:us,SVGPathElement:us,SVGPatternElement:us,SVGPoint:us,SVGPointList:us,SVGPolygonElement:us,SVGPolylineElement:us,SVGPreserveAspectRatio:us,SVGRadialGradientElement:us,SVGRect:us,SVGRectElement:us,SVGScriptElement:us,SVGSetElement:us,SVGStopElement:us,SVGStringList:us,SVGStyleElement:us,SVGSVGElement:us,SVGSwitchElement:us,SVGSymbolElement:us,SVGTextContentElement:us,SVGTextElement:us,SVGTextPathElement:us,SVGTextPositioningElement:us,SVGTitleElement:us,SVGTransform:us,SVGTransformList:us,SVGTSpanElement:us,SVGUnitTypes:us,SVGUseElement:us,SVGViewElement:us,TaskAttributionTiming:us,Text:us,TextEvent:us,TextMetrics:us,TextTrack:us,TextTrackCue:us,TextTrackCueList:us,TextTrackList:us,TimeRanges:us,toolbar:ls,top:ls,Touch:us,TouchEvent:us,TouchList:us,TrackEvent:us,TransitionEvent:us,TreeWalker:us,UIEvent:us,ValidityState:us,visualViewport:ls,VisualViewport:us,VTTCue:us,WaveShaperNode:us,WebAssembly:ls,WebGL2RenderingContext:us,WebGLActiveInfo:us,WebGLBuffer:us,WebGLContextEvent:us,WebGLFramebuffer:us,WebGLProgram:us,WebGLQuery:us,WebGLRenderbuffer:us,WebGLRenderingContext:us,WebGLSampler:us,WebGLShader:us,WebGLShaderPrecisionFormat:us,WebGLSync:us,WebGLTexture:us,WebGLTransformFeedback:us,WebGLUniformLocation:us,WebGLVertexArrayObject:us,WebSocket:us,WheelEvent:us,Window:us,Worker:us,WritableStream:us,XMLDocument:us,XMLHttpRequest:us,XMLHttpRequestEventTarget:us,XMLHttpRequestUpload:us,XMLSerializer:us,XPathEvaluator:us,XPathExpression:us,XPathResult:us,XSLTProcessor:us};for(const e of["window","global","self","globalThis"])ms[e]=ms;function gs(e){let t=ms;for(const s of e){if("string"!=typeof s)return null;if(t=t[s],!t)return null}return t[as]}class ys extends K{constructor(){super(...arguments),this.isReassigned=!0}hasEffectsWhenAccessedAtPath(e){return!function(e){return 1===e.length?"undefined"===e[0]||null!==gs(e):null!==gs(e.slice(0,-1))}([this.name,...e])}hasEffectsWhenCalledAtPath(e){return!function(e){const t=gs(e);return null!==t&&t.pure}([this.name,...e])}}const Es={__proto__:null,class:!0,const:!0,let:!0,var:!0};class xs extends ft{constructor(){super(...arguments),this.variable=null,this.deoptimized=!1,this.isTDZAccess=null}addExportedVariables(e,t){null!==this.variable&&t.has(this.variable)&&e.push(this.variable)}bind(){null===this.variable&&rs(this,this.parent)&&(this.variable=this.scope.findVariable(this.name),this.variable.addReference(this))}declare(e,t){let s;const{treeshake:i}=this.context.options;switch(e){case"var":s=this.scope.addDeclaration(this,this.context,t,!0),i&&i.correctVarValueBeforeDeclaration&&s.markInitializersForDeoptimization();break;case"function":case"let":case"const":case"class":s=this.scope.addDeclaration(this,this.context,t,!1);break;case"parameter":s=this.scope.addParameterDeclaration(this);break;default:throw new Error(`Internal Error: Unexpected identifier kind ${e}.`)}return s.kind=e,[this.variable=s]}deoptimizePath(e){0!==e.length||this.scope.contains(this.name)||this.disallowImportReassignment(),this.variable.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.variable.deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.getVariableRespectingTDZ().getLiteralValueAtPath(e,t,s)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.getVariableRespectingTDZ().getReturnExpressionWhenCalledAtPath(e,t,s,i)}hasEffects(){return this.deoptimized||this.applyDeoptimizations(),!(!this.isPossibleTDZ()||"var"===this.variable.kind)||this.context.options.treeshake.unknownGlobalSideEffects&&this.variable instanceof ys&&this.variable.hasEffectsWhenAccessedAtPath(V)}hasEffectsWhenAccessedAtPath(e,t){return null!==this.variable&&this.getVariableRespectingTDZ().hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return!this.variable||(e.length>0?this.getVariableRespectingTDZ():this.variable).hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return!this.variable||this.getVariableRespectingTDZ().hasEffectsWhenCalledAtPath(e,t,s)}include(){this.deoptimized||this.applyDeoptimizations(),this.included||(this.included=!0,null!==this.variable&&this.context.includeVariableInModule(this.variable))}includeCallArguments(e,t){this.getVariableRespectingTDZ().includeCallArguments(e,t)}isPossibleTDZ(){if(null!==this.isTDZAccess)return this.isTDZAccess;if(!(this.variable instanceof Bt&&this.variable.kind&&this.variable.kind in Es))return this.isTDZAccess=!1;let e;return this.variable.declarations&&1===this.variable.declarations.length&&(e=this.variable.declarations[0])&&this.start<e.start&&bs(this)===bs(e)?this.isTDZAccess=!0:this.variable.initReached?this.isTDZAccess=!1:this.isTDZAccess=!0}markDeclarationReached(){this.variable.initReached=!0}render(e,{snippets:{getPropertyAccess:t}},{renderedParentType:s,isCalleeOfRenderedParent:i,isShorthandProperty:n}=Y){if(this.variable){const r=this.variable.getName(t);r!==this.name&&(e.overwrite(this.start,this.end,r,{contentOnly:!0,storeName:!0}),n&&e.prependRight(this.start,`${this.name}: `)),"eval"===r&&s===Qe&&i&&e.appendRight(this.start,"0, ")}}applyDeoptimizations(){this.deoptimized=!0,null!==this.variable&&this.variable instanceof Bt&&(this.variable.consolidateInitializers(),this.context.requestTreeshakingPass())}disallowImportReassignment(){return this.context.error({code:"ILLEGAL_REASSIGNMENT",message:`Illegal reassignment to import '${this.name}'`},this.start)}getVariableRespectingTDZ(){return this.isPossibleTDZ()?q:this.variable}}function bs(e){for(;e&&!/^Program|Function/.test(e.type);)e=e.parent;return e}class vs extends ft{constructor(){super(...arguments),this.deoptimized=!1,this.declarationInit=null}addExportedVariables(e,t){this.argument.addExportedVariables(e,t)}declare(e,t){return this.declarationInit=t,this.argument.declare(e,q)}deoptimizePath(e){0===e.length&&this.argument.deoptimizePath(V)}hasEffectsWhenAssignedAtPath(e,t){return e.length>0||this.argument.hasEffectsWhenAssignedAtPath(V,t)}markDeclarationReached(){this.argument.markDeclarationReached()}applyDeoptimizations(){this.deoptimized=!0,null!==this.declarationInit&&(this.declarationInit.deoptimizePath([L,L]),this.context.requestTreeshakingPass())}}class As extends ft{constructor(){super(...arguments),this.deoptimizedReturn=!1}createScope(e){this.scope=new Gt(e,this.context)}deoptimizePath(e){1===e.length&&e[0]===L&&this.scope.getReturnExpression().deoptimizePath(B)}deoptimizeThisOnEventAtPath(){}getReturnExpressionWhenCalledAtPath(e){return 0!==e.length?q:this.async?(this.deoptimizedReturn||(this.deoptimizedReturn=!0,this.scope.getReturnExpression().deoptimizePath(B),this.context.requestTreeshakingPass()),q):this.scope.getReturnExpression()}hasEffects(){return!1}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenAssignedAtPath(e){return e.length>1}hasEffectsWhenCalledAtPath(e,t,s){if(e.length>0)return!0;if(this.async){const{propertyReadSideEffects:e}=this.context.options.treeshake,t=this.scope.getReturnExpression();if(t.hasEffectsWhenCalledAtPath(["then"],{args:_e,thisParam:null,withNew:!1},s)||e&&("always"===e||t.hasEffectsWhenAccessedAtPath(["then"],s)))return!0}for(const e of this.params)if(e.hasEffects(s))return!0;const{ignore:i,brokenFlow:n}=s;return s.ignore={breaks:!1,continues:!1,labels:new Set,returnYield:!0},!!this.body.hasEffects(s)||(s.ignore=i,s.brokenFlow=n,!1)}include(e,t){this.included=!0;for(const s of this.params)s instanceof xs||s.include(e,t);const{brokenFlow:s}=e;e.brokenFlow=0,this.body.include(e,t),e.brokenFlow=s}includeCallArguments(e,t){this.scope.includeCallArguments(e,t)}initialise(){this.scope.addParameterVariables(this.params.map((e=>e.declare("parameter",q))),this.params[this.params.length-1]instanceof vs),this.body instanceof ns?this.body.addImplicitReturnExpressionToScope():this.scope.addReturnExpression(this.body)}parseNode(e){e.body.type===Ye&&(this.body=new ns(e.body,this,this.scope.hoistedBodyVarScope)),super.parseNode(e)}}function Ss(e,{exportNamesByVariable:t,snippets:{_:s,getObject:i,getPropertyAccess:n}},r=""){if(1===e.length&&1===t.get(e[0]).length){const i=e[0];return`exports('${t.get(i)}',${s}${i.getName(n)}${r})`}{const s=[];for(const i of e)for(const e of t.get(i))s.push([e,i.getName(n)+r]);return`exports(${i(s,{lineBreakIndent:null})})`}}function Ps(e,t,s,i,{exportNamesByVariable:n,snippets:{_:r}}){i.prependRight(t,`exports('${n.get(e)}',${r}`),i.appendLeft(s,")")}function ks(e,t,s,i,n,r){const{_:a,getPropertyAccess:o}=r.snippets;n.appendLeft(s,`,${a}${Ss([e],r)},${a}${e.getName(o)}`),i&&(n.prependRight(t,"("),n.appendLeft(s,")"))}As.prototype.preventChildBlockScope=!0;class ws extends ft{addExportedVariables(e,t){for(const s of this.properties)"Property"===s.type?s.value.addExportedVariables(e,t):s.argument.addExportedVariables(e,t)}declare(e,t){const s=[];for(const i of this.properties)s.push(...i.declare(e,t));return s}deoptimizePath(e){if(0===e.length)for(const t of this.properties)t.deoptimizePath(e)}hasEffectsWhenAssignedAtPath(e,t){if(e.length>0)return!0;for(const e of this.properties)if(e.hasEffectsWhenAssignedAtPath(V,t))return!0;return!1}markDeclarationReached(){for(const e of this.properties)e.markDeclarationReached()}}class Cs extends ft{constructor(){super(...arguments),this.deoptimized=!1}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),this.right.hasEffects(e)||this.left.hasEffects(e)||this.left.hasEffectsWhenAssignedAtPath(V,e)}hasEffectsWhenAccessedAtPath(e,t){return e.length>0&&this.right.hasEffectsWhenAccessedAtPath(e,t)}include(e,t){let s;this.deoptimized||this.applyDeoptimizations(),this.included=!0,(t||"="!==this.operator||this.left.included||(s=Ne(),this.left.hasEffects(s)||this.left.hasEffectsWhenAssignedAtPath(V,s)))&&this.left.include(e,t),this.right.include(e,t)}render(e,t,{preventASI:s,renderedParentType:i,renderedSurroundingElement:n}=Y){if(this.left.included)this.left.render(e,t),this.right.render(e,t);else{const r=Qt(e.original,Xt(e.original,"=",this.left.end)+1);e.remove(this.start,r),s&&ts(e,r,this.right.start),this.right.render(e,t,{renderedParentType:i||this.parent.type,renderedSurroundingElement:n||this.parent.type})}if("system"===t.format)if(this.left instanceof xs){const s=this.left.variable,i=t.exportNamesByVariable.get(s);if(i)return void(1===i.length?Ps(s,this.start,this.end,e,t):ks(s,this.start,this.end,this.parent.type!==Ze,e,t))}else{const s=[];if(this.left.addExportedVariables(s,t.exportNamesByVariable),s.length>0)return void function(e,t,s,i,n,r){const{_:a,getDirectReturnIifeLeft:o}=r.snippets;n.prependRight(t,o(["v"],`${Ss(e,r)},${a}v`,{needsArrowReturnParens:!0,needsWrappedFunction:i})),n.appendLeft(s,")")}(s,this.start,this.end,n===Ze,e,t)}this.left.included&&this.left instanceof ws&&(n===Ze||n===Xe)&&(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(V),this.right.deoptimizePath(B),this.context.requestTreeshakingPass()}}class Is extends Bt{constructor(e){super("arguments",null,q,e)}hasEffectsWhenAccessedAtPath(e){return e.length>1}hasEffectsWhenAssignedAtPath(){return!0}hasEffectsWhenCalledAtPath(){return!0}}class Ns extends Bt{constructor(e){super("this",null,null,e),this.deoptimizedPaths=[],this.entitiesToBeDeoptimized=new Set,this.thisDeoptimizationList=[],this.thisDeoptimizations=new U}addEntityToBeDeoptimized(e){for(const t of this.deoptimizedPaths)e.deoptimizePath(t);for(const t of this.thisDeoptimizationList)this.applyThisDeoptimizationEvent(e,t);this.entitiesToBeDeoptimized.add(e)}deoptimizePath(e){if(0!==e.length&&!this.deoptimizationTracker.trackEntityAtPathAndGetIfTracked(e,this)){this.deoptimizedPaths.push(e);for(const t of this.entitiesToBeDeoptimized)t.deoptimizePath(e)}}deoptimizeThisOnEventAtPath(e,t,s){const i={event:e,path:t,thisParameter:s};if(!this.thisDeoptimizations.trackEntityAtPathAndGetIfTracked(t,e,s)){for(const e of this.entitiesToBeDeoptimized)this.applyThisDeoptimizationEvent(e,i);this.thisDeoptimizationList.push(i)}}hasEffectsWhenAccessedAtPath(e,t){return this.getInit(t).hasEffectsWhenAccessedAtPath(e,t)||super.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return this.getInit(t).hasEffectsWhenAssignedAtPath(e,t)||super.hasEffectsWhenAssignedAtPath(e,t)}applyThisDeoptimizationEvent(e,{event:t,path:s,thisParameter:i}){e.deoptimizeThisOnEventAtPath(t,s,i===this?e:i,j)}getInit(e){return e.replacedVariableInits.get(this)||q}}class _s extends Gt{constructor(e,t){super(e,t),this.variables.set("arguments",this.argumentsVariable=new Is(t)),this.variables.set("this",this.thisVariable=new Ns(t))}findLexicalBoundary(){return this}includeCallArguments(e,t){if(super.includeCallArguments(e,t),this.argumentsVariable.included)for(const s of t)s.included||s.include(e,!1)}}class $s extends ft{constructor(){super(...arguments),this.deoptimizedReturn=!1,this.isPrototypeDeoptimized=!1}createScope(e){this.scope=new _s(e,this.context)}deoptimizePath(e){1===e.length&&("prototype"===e[0]?this.isPrototypeDeoptimized=!0:e[0]===L&&(this.isPrototypeDeoptimized=!0,this.scope.getReturnExpression().deoptimizePath(B)))}deoptimizeThisOnEventAtPath(e,t,s){2===e&&(t.length>0?s.deoptimizePath(B):this.scope.thisVariable.addEntityToBeDeoptimized(s))}getReturnExpressionWhenCalledAtPath(e){return 0!==e.length?q:this.async?(this.deoptimizedReturn||(this.deoptimizedReturn=!0,this.scope.getReturnExpression().deoptimizePath(B),this.context.requestTreeshakingPass()),q):this.scope.getReturnExpression()}hasEffects(){return null!==this.id&&this.id.hasEffects()}hasEffectsWhenAccessedAtPath(e){return!(e.length<=1)&&(e.length>2||"prototype"!==e[0]||this.isPrototypeDeoptimized)}hasEffectsWhenAssignedAtPath(e){return!(e.length<=1)&&(e.length>2||"prototype"!==e[0]||this.isPrototypeDeoptimized)}hasEffectsWhenCalledAtPath(e,t,s){if(e.length>0)return!0;if(this.async){const{propertyReadSideEffects:e}=this.context.options.treeshake,t=this.scope.getReturnExpression();if(t.hasEffectsWhenCalledAtPath(["then"],{args:_e,thisParam:null,withNew:!1},s)||e&&("always"===e||t.hasEffectsWhenAccessedAtPath(["then"],s)))return!0}for(const e of this.params)if(e.hasEffects(s))return!0;const i=s.replacedVariableInits.get(this.scope.thisVariable);s.replacedVariableInits.set(this.scope.thisVariable,t.withNew?new At(Object.create(null),kt):q);const{brokenFlow:n,ignore:r}=s;return s.ignore={breaks:!1,continues:!1,labels:new Set,returnYield:!0},!!this.body.hasEffects(s)||(s.brokenFlow=n,i?s.replacedVariableInits.set(this.scope.thisVariable,i):s.replacedVariableInits.delete(this.scope.thisVariable),s.ignore=r,!1)}include(e,t){this.included=!0,this.id&&this.id.include();const s=this.scope.argumentsVariable.included;for(const i of this.params)i instanceof xs&&!s||i.include(e,t);const{brokenFlow:i}=e;e.brokenFlow=0,this.body.include(e,t),e.brokenFlow=i}includeCallArguments(e,t){this.scope.includeCallArguments(e,t)}initialise(){null!==this.id&&this.id.declare("function",this),this.scope.addParameterVariables(this.params.map((e=>e.declare("parameter",q))),this.params[this.params.length-1]instanceof vs),this.body.addImplicitReturnExpressionToScope()}parseNode(e){this.body=new ns(e.body,this,this.scope.hoistedBodyVarScope),super.parseNode(e)}}$s.prototype.preventChildBlockScope=!0;const Ts={"!=":(e,t)=>e!=t,"!==":(e,t)=>e!==t,"%":(e,t)=>e%t,"&":(e,t)=>e&t,"*":(e,t)=>e*t,"**":(e,t)=>e**t,"+":(e,t)=>e+t,"-":(e,t)=>e-t,"/":(e,t)=>e/t,"<":(e,t)=>e<t,"<<":(e,t)=>e<<t,"<=":(e,t)=>e<=t,"==":(e,t)=>e==t,"===":(e,t)=>e===t,">":(e,t)=>e>t,">=":(e,t)=>e>=t,">>":(e,t)=>e>>t,">>>":(e,t)=>e>>>t,"^":(e,t)=>e^t,in:()=>G,instanceof:()=>G,"|":(e,t)=>e|t};class Rs extends ft{deoptimizeThisOnEventAtPath(){}getLiteralValueAtPath(e){return e.length>0||null===this.value&&110!==this.context.code.charCodeAt(this.start)||"bigint"==typeof this.value||47===this.context.code.charCodeAt(this.start)?G:this.value}getReturnExpressionWhenCalledAtPath(e){return 1!==e.length?q:Ge(this.members,e[0])}hasEffectsWhenAccessedAtPath(e){return null===this.value?e.length>0:e.length>1}hasEffectsWhenAssignedAtPath(e){return e.length>0}hasEffectsWhenCalledAtPath(e,t,s){return 1!==e.length||Ue(this.members,e[0],t,s)}initialise(){this.members=function(e){switch(typeof e){case"boolean":return ze;case"number":return We;case"string":return je}return Object.create(null)}(this.value)}parseNode(e){this.value=e.value,this.regex=e.regex,super.parseNode(e)}render(e){"string"==typeof this.value&&e.indentExclusionRanges.push([this.start+1,this.end-1])}}function Ms(e){return e.computed?(t=e.property)instanceof Rs?String(t.value):null:e.property.name;var t}function Ds(e){const t=e.propertyKey,s=e.object;if("string"==typeof t){if(s instanceof xs)return[{key:s.name,pos:s.start},{key:t,pos:e.property.start}];if(s instanceof Ls){const i=Ds(s);return i&&[...i,{key:t,pos:e.property.start}]}}return null}class Ls extends ft{constructor(){super(...arguments),this.variable=null,this.deoptimized=!1,this.bound=!1,this.expressionsToBeDeoptimized=[],this.replacement=null}bind(){this.bound=!0;const e=Ds(this),t=e&&this.scope.findVariable(e[0].key);if(t&&t.isNamespace){const s=this.resolveNamespaceVariables(t,e.slice(1));s?"string"==typeof s?this.replacement=s:(this.variable=s,this.scope.addNamespaceMemberAccess(function(e){let t=e[0].key;for(let s=1;s<e.length;s++)t+="."+e[s].key;return t}(e),s)):super.bind()}else super.bind()}deoptimizeCache(){const e=this.expressionsToBeDeoptimized;this.expressionsToBeDeoptimized=[],this.propertyKey=L,this.object.deoptimizePath(B);for(const t of e)t.deoptimizeCache()}deoptimizePath(e){0===e.length&&this.disallowNamespaceReassignment(),this.variable?this.variable.deoptimizePath(e):this.replacement||e.length<7&&this.object.deoptimizePath([this.getPropertyKey(),...e])}deoptimizeThisOnEventAtPath(e,t,s,i){this.variable?this.variable.deoptimizeThisOnEventAtPath(e,t,s,i):this.replacement||(t.length<7?this.object.deoptimizeThisOnEventAtPath(e,[this.getPropertyKey(),...t],s,i):s.deoptimizePath(B))}getLiteralValueAtPath(e,t,s){return null!==this.variable?this.variable.getLiteralValueAtPath(e,t,s):this.replacement?G:(this.expressionsToBeDeoptimized.push(s),e.length<7?this.object.getLiteralValueAtPath([this.getPropertyKey(),...e],t,s):G)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return null!==this.variable?this.variable.getReturnExpressionWhenCalledAtPath(e,t,s,i):this.replacement?q:(this.expressionsToBeDeoptimized.push(i),e.length<7?this.object.getReturnExpressionWhenCalledAtPath([this.getPropertyKey(),...e],t,s,i):q)}hasEffects(e){this.deoptimized||this.applyDeoptimizations();const{propertyReadSideEffects:t}=this.context.options.treeshake;return this.property.hasEffects(e)||this.object.hasEffects(e)||!(this.variable||this.replacement||this.parent instanceof Cs&&"="===this.parent.operator)&&t&&("always"===t||this.object.hasEffectsWhenAccessedAtPath([this.getPropertyKey()],e))}hasEffectsWhenAccessedAtPath(e,t){return null!==this.variable?this.variable.hasEffectsWhenAccessedAtPath(e,t):!!this.replacement||!(e.length<7)||this.object.hasEffectsWhenAccessedAtPath([this.getPropertyKey(),...e],t)}hasEffectsWhenAssignedAtPath(e,t){return null!==this.variable?this.variable.hasEffectsWhenAssignedAtPath(e,t):!!this.replacement||!(e.length<7)||this.object.hasEffectsWhenAssignedAtPath([this.getPropertyKey(),...e],t)}hasEffectsWhenCalledAtPath(e,t,s){return null!==this.variable?this.variable.hasEffectsWhenCalledAtPath(e,t,s):!!this.replacement||!(e.length<7)||this.object.hasEffectsWhenCalledAtPath([this.getPropertyKey(),...e],t,s)}include(e,t){this.deoptimized||this.applyDeoptimizations(),this.included||(this.included=!0,null!==this.variable&&this.context.includeVariableInModule(this.variable)),this.object.include(e,t),this.property.include(e,t)}includeCallArguments(e,t){this.variable?this.variable.includeCallArguments(e,t):super.includeCallArguments(e,t)}initialise(){this.propertyKey=Ms(this)}render(e,t,{renderedParentType:s,isCalleeOfRenderedParent:i,renderedSurroundingElement:n}=Y){if(this.variable||this.replacement){const{snippets:{getPropertyAccess:n}}=t;let r=this.variable?this.variable.getName(n):this.replacement;s&&i&&(r="0, "+r),e.overwrite(this.start,this.end,r,{contentOnly:!0,storeName:!0})}else s&&i&&e.appendRight(this.start,"0, "),this.object.render(e,t,{renderedSurroundingElement:n}),this.property.render(e,t)}applyDeoptimizations(){this.deoptimized=!0;const{propertyReadSideEffects:e}=this.context.options.treeshake;this.bound&&e&&!this.variable&&!this.replacement&&(this.parent instanceof Cs&&"="===this.parent.operator||this.object.deoptimizeThisOnEventAtPath(0,[this.propertyKey],this.object,j),this.parent instanceof Cs&&this.object.deoptimizeThisOnEventAtPath(1,[this.propertyKey],this.object,j),this.context.requestTreeshakingPass())}disallowNamespaceReassignment(){this.object instanceof xs&&this.scope.findVariable(this.object.name).isNamespace&&(this.variable&&this.context.includeVariableInModule(this.variable),this.context.warn({code:"ILLEGAL_NAMESPACE_REASSIGNMENT",message:`Illegal reassignment to import '${this.object.name}'`},this.start))}getPropertyKey(){if(null===this.propertyKey){this.propertyKey=L;const e=this.property.getLiteralValueAtPath(V,j,this);return this.propertyKey=e===G?L:String(e)}return this.propertyKey}resolveNamespaceVariables(e,t){if(0===t.length)return e;if(!e.isNamespace||e instanceof X)return null;const s=t[0].key,i=e.context.traceExport(s);if(!i){const i=e.context.fileName;return this.context.warn({code:"MISSING_EXPORT",exporter:ie(i),importer:ie(this.context.fileName),message:`'${s}' is not exported by '${ie(i)}'`,missing:s,url:"https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module"},t[0].pos),"undefined"}return this.resolveNamespaceVariables(i,t.slice(1))}}class Os extends Ut{addDeclaration(e,t,s,i){const n=this.variables.get(e.name);return n?(this.parent.addDeclaration(e,t,Te,i),n.addDeclaration(e,s),n):this.parent.addDeclaration(e,t,s,i)}}class Vs extends jt{constructor(e,t,s){super(e),this.variables.set("this",this.thisVariable=new Bt("this",null,t,s)),this.instanceScope=new jt(this),this.instanceScope.variables.set("this",new Ns(s))}findLexicalBoundary(){return this}}class Bs extends ft{constructor(){super(...arguments),this.accessedValue=null,this.accessorCallOptions={args:_e,thisParam:null,withNew:!1}}deoptimizeCache(){}deoptimizePath(e){this.getAccessedValue().deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){return 0===e&&"get"===this.kind&&0===t.length||1===e&&"set"===this.kind&&0===t.length?this.value.deoptimizeThisOnEventAtPath(2,V,s,i):void this.getAccessedValue().deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.getAccessedValue().getLiteralValueAtPath(e,t,s)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.getAccessedValue().getReturnExpressionWhenCalledAtPath(e,t,s,i)}hasEffects(e){return this.key.hasEffects(e)}hasEffectsWhenAccessedAtPath(e,t){return"get"===this.kind&&0===e.length?this.value.hasEffectsWhenCalledAtPath(V,this.accessorCallOptions,t):this.getAccessedValue().hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return"set"===this.kind?this.value.hasEffectsWhenCalledAtPath(V,this.accessorCallOptions,t):this.getAccessedValue().hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return this.getAccessedValue().hasEffectsWhenCalledAtPath(e,t,s)}getAccessedValue(){return null===this.accessedValue?"get"===this.kind?(this.accessedValue=q,this.accessedValue=this.value.getReturnExpressionWhenCalledAtPath(V,this.accessorCallOptions,j,this)):this.accessedValue=this.value:this.accessedValue}}class Fs extends Bs{}class zs extends H{constructor(e,t){super(),this.object=e,this.key=t}deoptimizePath(e){this.object.deoptimizePath([this.key,...e])}deoptimizeThisOnEventAtPath(e,t,s,i){this.object.deoptimizeThisOnEventAtPath(e,[this.key,...t],s,i)}getLiteralValueAtPath(e,t,s){return this.object.getLiteralValueAtPath([this.key,...e],t,s)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.object.getReturnExpressionWhenCalledAtPath([this.key,...e],t,s,i)}hasEffectsWhenAccessedAtPath(e,t){return 0!==e.length&&this.object.hasEffectsWhenAccessedAtPath([this.key,...e],t)}hasEffectsWhenAssignedAtPath(e,t){return this.object.hasEffectsWhenAssignedAtPath([this.key,...e],t)}hasEffectsWhenCalledAtPath(e,t,s){return this.object.hasEffectsWhenCalledAtPath([this.key,...e],t,s)}}class Ws extends ft{constructor(){super(...arguments),this.objectEntity=null}createScope(e){this.scope=new jt(e)}deoptimizeCache(){this.getObjectEntity().deoptimizeAllProperties()}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.getObjectEntity().deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.getObjectEntity().getLiteralValueAtPath(e,t,s)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,s,i)}hasEffects(e){var t,s;const i=(null===(t=this.superClass)||void 0===t?void 0:t.hasEffects(e))||this.body.hasEffects(e);return null===(s=this.id)||void 0===s||s.markDeclarationReached(),i||super.hasEffects(e)}hasEffectsWhenAccessedAtPath(e,t){return this.getObjectEntity().hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return this.getObjectEntity().hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return 0===e.length?!t.withNew||(null!==this.classConstructor?this.classConstructor.hasEffectsWhenCalledAtPath(V,t,s):null!==this.superClass&&this.superClass.hasEffectsWhenCalledAtPath(e,t,s)):this.getObjectEntity().hasEffectsWhenCalledAtPath(e,t,s)}include(e,t){var s;this.included=!0,null===(s=this.superClass)||void 0===s||s.include(e,t),this.body.include(e,t),this.id&&(this.id.markDeclarationReached(),this.id.include())}initialise(){var e;null===(e=this.id)||void 0===e||e.declare("class",this);for(const e of this.body.body)if(e instanceof Fs&&"constructor"===e.kind)return void(this.classConstructor=e);this.classConstructor=null}getObjectEntity(){if(null!==this.objectEntity)return this.objectEntity;const e=[],t=[];for(const s of this.body.body){const i=s.static?e:t,n=s.kind;if(i===t&&!n)continue;const r="set"===n||"get"===n?n:"init";let a;if(s.computed){const e=s.key.getLiteralValueAtPath(V,j,this);if(e===G){i.push({key:L,kind:r,property:s});continue}a=String(e)}else a=s.key instanceof xs?s.key.name:String(s.key.value);i.push({key:a,kind:r,property:s})}return e.unshift({key:"prototype",kind:"init",property:new At(t,this.superClass?new zs(this.superClass,"prototype"):kt)}),this.objectEntity=new At(e,this.superClass||kt)}}class js extends Ws{initialise(){super.initialise(),null!==this.id&&(this.id.variable.isId=!0)}parseNode(e){null!==e.id&&(this.id=new xs(e.id,this,this.scope.parent)),super.parseNode(e)}render(e,t){const{exportNamesByVariable:s,format:i,snippets:{_:n}}=t;"system"===i&&this.id&&s.has(this.id.variable)&&e.appendLeft(this.end,`${n}${Ss([this.id.variable],t)};`),super.render(e,t)}}class Us extends H{constructor(e){super(),this.expressions=e,this.included=!1}deoptimizePath(e){for(const t of this.expressions)t.deoptimizePath(e)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return new Us(this.expressions.map((n=>n.getReturnExpressionWhenCalledAtPath(e,t,s,i))))}hasEffectsWhenAccessedAtPath(e,t){for(const s of this.expressions)if(s.hasEffectsWhenAccessedAtPath(e,t))return!0;return!1}hasEffectsWhenAssignedAtPath(e,t){for(const s of this.expressions)if(s.hasEffectsWhenAssignedAtPath(e,t))return!0;return!1}hasEffectsWhenCalledAtPath(e,t,s){for(const i of this.expressions)if(i.hasEffectsWhenCalledAtPath(e,t,s))return!0;return!1}include(e,t){for(const s of this.expressions)s.included||s.include(e,t)}}class Gs extends ft{hasEffects(){return!1}initialise(){this.context.addExport(this)}render(e,t,s){e.remove(s.start,s.end)}}Gs.prototype.needsBoundaries=!0;class Hs extends $s{initialise(){super.initialise(),null!==this.id&&(this.id.variable.isId=!0)}parseNode(e){null!==e.id&&(this.id=new xs(e.id,this,this.scope.parent)),super.parseNode(e)}}class qs extends ft{include(e,t){super.include(e,t),t&&this.context.includeVariableInModule(this.variable)}initialise(){const e=this.declaration;this.declarationName=e.id&&e.id.name||this.declaration.name,this.variable=this.scope.addExportDefaultDeclaration(this.declarationName||this.context.getModuleName(),this,this.context),this.context.addExport(this)}render(e,t,s){const{start:i,end:n}=s,r=function(e,t){return Qt(e,Xt(e,"default",t)+7)}(e.original,this.start);if(this.declaration instanceof Hs)this.renderNamedDeclaration(e,r,"function","(",null===this.declaration.id,t);else if(this.declaration instanceof js)this.renderNamedDeclaration(e,r,"class","{",null===this.declaration.id,t);else{if(this.variable.getOriginalVariable()!==this.variable)return void Ht(this,e,i,n);if(!this.variable.included)return e.remove(this.start,r),this.declaration.render(e,t,{renderedSurroundingElement:Ze}),void(";"!==e.original[this.end-1]&&e.appendLeft(this.end,";"));this.renderVariableDeclaration(e,r,t)}this.declaration.render(e,t)}renderNamedDeclaration(e,t,s,i,n,r){const{exportNamesByVariable:a,format:o,snippets:{getPropertyAccess:h}}=r,l=this.variable.getName(h);e.remove(this.start,t),n&&e.appendLeft(function(e,t,s,i){const n=Xt(e,t,i)+t.length;e=e.slice(n,Xt(e,s,n));const r=Xt(e,"*");return-1===r?n:n+r+1}(e.original,s,i,t),` ${l}`),"system"===o&&this.declaration instanceof js&&a.has(this.variable)&&e.appendLeft(this.end,` ${Ss([this.variable],r)};`)}renderVariableDeclaration(e,t,{format:s,exportNamesByVariable:i,snippets:{cnst:n,getPropertyAccess:r}}){const a=59===e.original.charCodeAt(this.end-1),o="system"===s&&i.get(this.variable);o?(e.overwrite(this.start,t,`${n} ${this.variable.getName(r)} = exports('${o[0]}', `),e.appendRight(a?this.end-1:this.end,")"+(a?"":";"))):(e.overwrite(this.start,t,`${n} ${this.variable.getName(r)} = `),a||e.appendLeft(this.end,";"))}}qs.prototype.needsBoundaries=!0;class Ks extends ft{bind(){null!==this.declaration&&this.declaration.bind()}hasEffects(e){return null!==this.declaration&&this.declaration.hasEffects(e)}initialise(){this.context.addExport(this)}render(e,t,s){const{start:i,end:n}=s;null===this.declaration?e.remove(i,n):(e.remove(this.start,this.declaration.start),this.declaration.render(e,t,{end:n,start:i}))}}Ks.prototype.needsBoundaries=!0;class Xs extends ss{constructor(){super(...arguments),this.hoistedDeclarations=[]}addDeclaration(e,t,s,i){return this.hoistedDeclarations.push(e),super.addDeclaration(e,t,s,i)}}const Ys=Symbol("unset");class Qs extends ft{constructor(){super(...arguments),this.testValue=Ys}deoptimizeCache(){this.testValue=G}hasEffects(e){if(this.test.hasEffects(e))return!0;const t=this.getTestValue();if(t===G){const{brokenFlow:t}=e;if(this.consequent.hasEffects(e))return!0;const s=e.brokenFlow;return e.brokenFlow=t,null!==this.alternate&&(!!this.alternate.hasEffects(e)||(e.brokenFlow=e.brokenFlow<s?e.brokenFlow:s,!1))}return t?this.consequent.hasEffects(e):null!==this.alternate&&this.alternate.hasEffects(e)}include(e,t){if(this.included=!0,t)this.includeRecursively(t,e);else{const t=this.getTestValue();t===G?this.includeUnknownTest(e):this.includeKnownTest(e,t)}}parseNode(e){this.consequentScope=new Xs(this.scope),this.consequent=new(this.context.getNodeConstructor(e.consequent.type))(e.consequent,this,this.consequentScope),e.alternate&&(this.alternateScope=new Xs(this.scope),this.alternate=new(this.context.getNodeConstructor(e.alternate.type))(e.alternate,this,this.alternateScope)),super.parseNode(e)}render(e,t){const{snippets:{getPropertyAccess:s}}=t,i=this.getTestValue(),n=[],r=this.test.included,a=!this.context.options.treeshake;r?this.test.render(e,t):e.remove(this.start,this.consequent.start),this.consequent.included&&(a||i===G||i)?this.consequent.render(e,t):(e.overwrite(this.consequent.start,this.consequent.end,r?";":""),n.push(...this.consequentScope.hoistedDeclarations)),this.alternate&&(!this.alternate.included||!a&&i!==G&&i?(r&&this.shouldKeepAlternateBranch()?e.overwrite(this.alternate.start,this.end,";"):e.remove(this.consequent.end,this.end),n.push(...this.alternateScope.hoistedDeclarations)):(r?101===e.original.charCodeAt(this.alternate.start-1)&&e.prependLeft(this.alternate.start," "):e.remove(this.consequent.end,this.alternate.start),this.alternate.render(e,t))),this.renderHoistedDeclarations(n,e,s)}getTestValue(){return this.testValue===Ys?this.testValue=this.test.getLiteralValueAtPath(V,j,this):this.testValue}includeKnownTest(e,t){this.test.shouldBeIncluded(e)&&this.test.include(e,!1),t&&this.consequent.shouldBeIncluded(e)&&this.consequent.includeAsSingleStatement(e,!1),null!==this.alternate&&!t&&this.alternate.shouldBeIncluded(e)&&this.alternate.includeAsSingleStatement(e,!1)}includeRecursively(e,t){this.test.include(t,e),this.consequent.include(t,e),null!==this.alternate&&this.alternate.include(t,e)}includeUnknownTest(e){this.test.include(e,!1);const{brokenFlow:t}=e;let s=0;this.consequent.shouldBeIncluded(e)&&(this.consequent.includeAsSingleStatement(e,!1),s=e.brokenFlow,e.brokenFlow=t),null!==this.alternate&&this.alternate.shouldBeIncluded(e)&&(this.alternate.includeAsSingleStatement(e,!1),e.brokenFlow=e.brokenFlow<s?e.brokenFlow:s)}renderHoistedDeclarations(e,t,s){const i=[...new Set(e.map((e=>{const t=e.variable;return t.included?t.getName(s):""})))].filter(Boolean).join(", ");if(i){const e=this.parent.type,s=e!==et&&e!==Ye;t.prependRight(this.start,`${s?"{ ":""}var ${i}; `),s&&t.appendLeft(this.end," }")}}shouldKeepAlternateBranch(){let e=this.parent;do{if(e instanceof Qs&&e.alternate)return!0;if(e instanceof ns)return!1;e=e.parent}while(e);return!1}}class Zs extends ft{bind(){}hasEffects(){return!1}initialise(){this.context.addImport(this)}render(e,t,s){e.remove(s.start,s.end)}}Zs.prototype.needsBoundaries=!0;const Js="_interopDefault",ei="_interopDefaultLegacy",ti="_interopNamespace",si="_interopNamespaceDefault",ii="_interopNamespaceDefaultOnly",ni="_mergeNamespaces",ri={auto:Js,default:null,defaultOnly:null,esModule:null,false:null,true:ei},ai=(e,t)=>"esModule"===e||t&&("auto"===e||"true"===e),oi={auto:ti,default:si,defaultOnly:ii,esModule:null,false:null,true:ti},hi=(e,t)=>ai(e,t)&&ri[e]===Js,li=(e,t,s,i,n,r,a)=>{const o=new Set(e);for(const e of Ai)t.has(e)&&o.add(e);return Ai.map((e=>o.has(e)?ci[e](s,i,n,r,a,o):"")).join("")},ci={[ei](e,t,s){const{_:i,getDirectReturnFunction:n,n:r}=t,[a,o]=n(["e"],{functionReturn:!0,lineBreakIndent:null,name:ei});return`${a}e${i}&&${i}typeof e${i}===${i}'object'${i}&&${i}'default'${i}in e${i}?${i}${s?ui(t):di(t)}${o}${r}${r}`},[Js](e,t,s){const{_:i,getDirectReturnFunction:n,n:r}=t,[a,o]=n(["e"],{functionReturn:!0,lineBreakIndent:null,name:Js});return`${a}e${i}&&${i}e.__esModule${i}?${i}${s?ui(t):di(t)}${o}${r}${r}`},[ii](e,t,s,i,n){const{getDirectReturnFunction:r,getObject:a,n:o}=t,[h,l]=r(["e"],{functionReturn:!0,lineBreakIndent:null,name:ii});return`${h}${bi(i,vi(n,a([["__proto__","null"],["default","e"]],{lineBreakIndent:null}),t))}${l}${o}${o}`},[si](e,t,s,i,n){const{_:r,n:a}=t;return`function _interopNamespaceDefault(e)${r}{${a}`+pi(e,e,t,s,i,n)+`}${a}${a}`},[ti](e,t,s,i,n,r){const{_:a,getDirectReturnFunction:o,n:h}=t;if(r.has(si)){const[e,t]=o(["e"],{functionReturn:!0,lineBreakIndent:null,name:ti});return`${e}e${a}&&${a}e.__esModule${a}?${a}e${a}:${a}_interopNamespaceDefault(e)${t}${h}${h}`}return`function _interopNamespace(e)${a}{${h}${e}if${a}(e${a}&&${a}e.__esModule)${a}return e;${h}`+pi(e,e,t,s,i,n)+`}${h}${h}`},[ni](e,t,s,i,n){const{_:r,cnst:a,n:o}=t,h="var"===a&&s;return`function _mergeNamespaces(n, m)${r}{${o}${e}${mi(`{${o}${e}${e}${e}if${r}(k${r}!==${r}'default'${r}&&${r}!(k in n))${r}{${o}`+(s?h?yi:Ei:xi)(e,e+e+e+e,t)+`${e}${e}${e}}${o}`+`${e}${e}}`,h,e,t)}${o}${e}return ${bi(i,vi(n,"n",t))};${o}}${o}${o}`}},ui=({_:e,getObject:t})=>`e${e}:${e}${t([["default","e"]],{lineBreakIndent:null})}`,di=({_:e,getPropertyAccess:t})=>`e${t("default")}${e}:${e}e`,pi=(e,t,s,i,n,r)=>{const{_:a,cnst:o,getObject:h,getPropertyAccess:l,n:c,s:u}=s,d=`{${c}`+(i?gi:xi)(e,t+e+e,s)+`${t}${e}}`;return`${t}${o} n${a}=${a}Object.create(null${r?`,${a}{${a}[Symbol.toStringTag]:${a}${Si(h)}${a}}`:""});${c}${t}if${a}(e)${a}{${c}${t}${e}${fi(d,!i,s)}${c}${t}}${c}${t}n${l("default")}${a}=${a}e;${c}${t}return ${bi(n,"n")}${u}${c}`},fi=(e,t,{_:s,cnst:i,getFunctionIntro:n,s:r})=>"var"!==i||t?`for${s}(${i} k in e)${s}${e}`:`Object.keys(e).forEach(${n(["k"],{isAsync:!1,name:null})}${e})${r}`,mi=(e,t,s,{_:i,cnst:n,getDirectReturnFunction:r,getFunctionIntro:a,n:o})=>{if(t){const[t,n]=r(["e"],{functionReturn:!1,lineBreakIndent:{base:s,t:s},name:null});return`m.forEach(${t}e${i}&&${i}typeof e${i}!==${i}'string'${i}&&${i}!Array.isArray(e)${i}&&${i}Object.keys(e).forEach(${a(["k"],{isAsync:!1,name:null})}${e})${n});`}return`for${i}(var i${i}=${i}0;${i}i${i}<${i}m.length;${i}i++)${i}{${o}${s}${s}${n} e${i}=${i}m[i];${o}${s}${s}if${i}(typeof e${i}!==${i}'string'${i}&&${i}!Array.isArray(e))${i}{${i}for${i}(${n} k in e)${i}${e}${i}}${o}${s}}`},gi=(e,t,s)=>{const{_:i,n:n}=s;return`${t}if${i}(k${i}!==${i}'default')${i}{${n}`+yi(e,t+e,s)+`${t}}${n}`},yi=(e,t,{_:s,cnst:i,getDirectReturnFunction:n,n:r})=>{const[a,o]=n([],{functionReturn:!0,lineBreakIndent:null,name:null});return`${t}${i} d${s}=${s}Object.getOwnPropertyDescriptor(e,${s}k);${r}${t}Object.defineProperty(n,${s}k,${s}d.get${s}?${s}d${s}:${s}{${r}${t}${e}enumerable:${s}true,${r}${t}${e}get:${s}${a}e[k]${o}${r}${t}});${r}`},Ei=(e,t,{_:s,cnst:i,getDirectReturnFunction:n,n:r})=>{const[a,o]=n([],{functionReturn:!0,lineBreakIndent:null,name:null});return`${t}${i} d${s}=${s}Object.getOwnPropertyDescriptor(e,${s}k);${r}${t}if${s}(d)${s}{${r}${t}${e}Object.defineProperty(n,${s}k,${s}d.get${s}?${s}d${s}:${s}{${r}${t}${e}${e}enumerable:${s}true,${r}${t}${e}${e}get:${s}${a}e[k]${o}${r}${t}${e}});${r}${t}}${r}`},xi=(e,t,{_:s,n:i})=>`${t}n[k]${s}=${s}e[k];${i}`,bi=(e,t)=>e?`Object.freeze(${t})`:t,vi=(e,t,{_:s,getObject:i})=>e?`Object.defineProperty(${t},${s}Symbol.toStringTag,${s}${Si(i)})`:t,Ai=Object.keys(ci);function Si(e){return e([["value","'Module'"]],{lineBreakIndent:null})}function Pi(e,t,s){return"external"===t?oi[String(s(e instanceof ke?e.id:null))]:"default"===t?ii:null}const ki={amd:["require"],cjs:["require"],system:["module"]},wi="ROLLUP_ASSET_URL_",Ci="ROLLUP_CHUNK_URL_",Ii="ROLLUP_FILE_URL_",Ni={amd:["document","module","URL"],cjs:["document","require","URL"],es:[],iife:["document","URL"],system:["module"],umd:["document","require","URL"]},_i={amd:["document","require","URL"],cjs:["document","require","URL"],es:[],iife:["document","URL"],system:["module","URL"],umd:["document","require","URL"]},$i=(e,t="URL")=>`new ${t}(${e}).href`,Ti=(e,t=!1)=>$i(`'${e}', ${t?"typeof document === 'undefined' ? location.href : ":""}document.currentScript && document.currentScript.src || document.baseURI`),Ri=e=>(t,{chunkId:s})=>{const i=e(s);return null===t?`({ url: ${i} })`:"url"===t?i:"undefined"},Mi=(e,t=!1)=>`${t?"typeof document === 'undefined' ? location.href : ":""}(document.currentScript && document.currentScript.src || new URL('${e}', document.baseURI).href)`,Di={amd:e=>("."!==e[0]&&(e="./"+e),$i(`require.toUrl('${e}'), document.baseURI`)),cjs:e=>`(typeof document === 'undefined' ? ${$i(`'file:' + __dirname + '/${e}'`,"(require('u' + 'rl').URL)")} : ${Ti(e)})`,es:e=>$i(`'${e}', import.meta.url`),iife:e=>Ti(e),system:e=>$i(`'${e}', module.meta.url`),umd:e=>`(typeof document === 'undefined' && typeof location === 'undefined' ? ${$i(`'file:' + __dirname + '/${e}'`,"(require('u' + 'rl').URL)")} : ${Ti(e,!0)})`},Li={amd:Ri((()=>$i("module.uri, document.baseURI"))),cjs:Ri((e=>`(typeof document === 'undefined' ? ${$i("'file:' + __filename","(require('u' + 'rl').URL)")} : ${Mi(e)})`)),iife:Ri((e=>Mi(e))),system:(e,{snippets:{getPropertyAccess:t}})=>null===e?"module.meta":`module.meta${t(e)}`,umd:Ri((e=>`(typeof document === 'undefined' && typeof location === 'undefined' ? ${$i("'file:' + __filename","(require('u' + 'rl').URL)")} : ${Mi(e,!0)})`))};class Oi extends ft{constructor(){super(...arguments),this.hasCachedEffect=!1}hasEffects(e){if(this.hasCachedEffect)return!0;for(const t of this.body)if(t.hasEffects(e))return this.hasCachedEffect=!0;return!1}include(e,t){this.included=!0;for(const s of this.body)(t||s.shouldBeIncluded(e))&&s.include(e,t)}render(e,t){this.body.length?Jt(this.body,e,this.start,this.end,t):super.render(e,t)}}class Vi extends ft{hasEffects(e){if(this.test&&this.test.hasEffects(e))return!0;for(const t of this.consequent){if(e.brokenFlow)break;if(t.hasEffects(e))return!0}return!1}include(e,t){this.included=!0,this.test&&this.test.include(e,t);for(const s of this.consequent)(t||s.shouldBeIncluded(e))&&s.include(e,t)}render(e,t,s){if(this.consequent.length){this.test&&this.test.render(e,t);const i=this.test?this.test.end:Xt(e.original,"default",this.start)+7,n=Xt(e.original,":",i)+1;Jt(this.consequent,e,n,s.end,t)}else super.render(e,t)}}Vi.prototype.needsBoundaries=!0;class Bi extends ft{getLiteralValueAtPath(e){return e.length>0||1!==this.quasis.length?G:this.quasis[0].value.cooked}render(e,t){e.indentExclusionRanges.push([this.start,this.end]),super.render(e,t)}}class Fi extends K{constructor(){super("undefined")}getLiteralValueAtPath(){}}class zi extends Bt{constructor(e,t,s){super(e,t,t.declaration,s),this.hasId=!1,this.originalId=null,this.originalVariable=null;const i=t.declaration;(i instanceof Hs||i instanceof js)&&i.id?(this.hasId=!0,this.originalId=i.id):i instanceof xs&&(this.originalId=i)}addReference(e){this.hasId||(this.name=e.name)}getAssignedVariableName(){return this.originalId&&this.originalId.name||null}getBaseVariableName(){const e=this.getOriginalVariable();return e===this?super.getBaseVariableName():e.getBaseVariableName()}getDirectOriginalVariable(){return!this.originalId||!this.hasId&&(this.originalId.isPossibleTDZ()||this.originalId.variable.isReassigned||this.originalId.variable instanceof Fi||"syntheticNamespace"in this.originalId.variable)?null:this.originalId.variable}getName(e){const t=this.getOriginalVariable();return t===this?super.getName(e):t.getName(e)}getOriginalVariable(){if(this.originalVariable)return this.originalVariable;let e,t=this;const s=new Set;do{s.add(t),e=t,t=e.getDirectOriginalVariable()}while(t instanceof zi&&!s.has(t));return this.originalVariable=t||e}}class Wi extends jt{constructor(e,t){super(e),this.context=t,this.variables.set("this",new Bt("this",null,Te,t))}addExportDefaultDeclaration(e,t,s){const i=new zi(e,t,s);return this.variables.set("default",i),i}addNamespaceMemberAccess(){}deconflict(e,t,s){for(const i of this.children)i.deconflict(e,t,s)}findLexicalBoundary(){return this}findVariable(e){const t=this.variables.get(e)||this.accessedOutsideVariables.get(e);if(t)return t;const s=this.context.traceVariable(e)||this.parent.findVariable(e);return s instanceof ys&&this.accessedOutsideVariables.set(e,s),s}}const ji={"!":e=>!e,"+":e=>+e,"-":e=>-e,delete:()=>G,typeof:e=>typeof e,void:()=>{},"~":e=>~e};function Ui(e,t){return null!==e.renderBaseName&&t.has(e)&&e.isReassigned}class Gi extends ft{deoptimizePath(){for(const e of this.declarations)e.deoptimizePath(V)}hasEffectsWhenAssignedAtPath(){return!1}include(e,t){this.included=!0;for(const s of this.declarations)(t||s.shouldBeIncluded(e))&&s.include(e,t)}includeAsSingleStatement(e,t){this.included=!0;for(const s of this.declarations)(t||s.shouldBeIncluded(e))&&(s.include(e,t),s.id.include(e,t))}initialise(){for(const e of this.declarations)e.declareDeclarator(this.kind)}render(e,t,s=Y){if(function(e,t){for(const s of e){if(!s.id.included)return!1;if(s.id.type===Je){if(t.has(s.id.variable))return!1}else{const e=[];if(s.id.addExportedVariables(e,t),e.length>0)return!1}}return!0}(this.declarations,t.exportNamesByVariable)){for(const s of this.declarations)s.render(e,t);s.isNoStatement||59===e.original.charCodeAt(this.end-1)||e.appendLeft(this.end,";")}else this.renderReplacedDeclarations(e,t,s)}renderDeclarationEnd(e,t,s,i,n,r,a,o){59===e.original.charCodeAt(this.end-1)&&e.remove(this.end-1,this.end),o||(t+=";"),null!==s?(10!==e.original.charCodeAt(i-1)||10!==e.original.charCodeAt(this.end)&&13!==e.original.charCodeAt(this.end)||(i--,13===e.original.charCodeAt(i)&&i--),i===s+1?e.overwrite(s,n,t):(e.overwrite(s,s+1,t),e.remove(i,n))):e.appendLeft(n,t),r.length>0&&e.appendLeft(n,` ${Ss(r,a)};`)}renderReplacedDeclarations(e,t,{isNoStatement:s}){const i=es(this.declarations,e,this.start+this.kind.length,this.end-(59===e.original.charCodeAt(this.end-1)?1:0));let n,r;r=Qt(e.original,this.start+this.kind.length);let a=r-1;e.remove(this.start,a);let o,h,l=!1,c=!1,u="";const d=[],p=function(e,t,s){var i;let n=null;if("system"===t.format){for(const{node:r}of e)r.id instanceof xs&&r.init&&0===s.length&&1===(null===(i=t.exportNamesByVariable.get(r.id.variable))||void 0===i?void 0:i.length)?(n=r.id.variable,s.push(n)):r.id.addExportedVariables(s,t.exportNamesByVariable);s.length>1?n=null:n&&(s.length=0)}return n}(i,t,d);for(const{node:s,start:d,separator:f,contentEnd:m,end:g}of i)if(s.included){if(s.render(e,t),o="",h="",!s.id.included||s.id instanceof xs&&Ui(s.id.variable,t.exportNamesByVariable))c&&(u+=";"),l=!1;else{if(p&&p===s.id.variable){const i=Xt(e.original,"=",s.id.end);Ps(p,Qt(e.original,i+1),null===f?m:f,e,t)}l?u+=",":(c&&(u+=";"),o+=`${this.kind} `,l=!0)}r===a+1?e.overwrite(a,r,u+o):(e.overwrite(a,a+1,u),e.appendLeft(r,o)),n=m,r=g,c=!0,a=f,u=""}else e.remove(d,g);this.renderDeclarationEnd(e,u,a,n,r,d,t,s)}}const Hi={ArrayExpression:class extends ft{constructor(){super(...arguments),this.objectEntity=null}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.getObjectEntity().deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.getObjectEntity().getLiteralValueAtPath(e,t,s)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,s,i)}hasEffectsWhenAccessedAtPath(e,t){return this.getObjectEntity().hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return this.getObjectEntity().hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return this.getObjectEntity().hasEffectsWhenCalledAtPath(e,t,s)}getObjectEntity(){if(null!==this.objectEntity)return this.objectEntity;const e=[{key:"length",kind:"init",property:Le}];let t=!1;for(let s=0;s<this.elements.length;s++){const i=this.elements[s];i instanceof mt||t?i&&(t=!0,e.unshift({key:O,kind:"init",property:i})):i?e.push({key:String(s),kind:"init",property:i}):e.push({key:String(s),kind:"init",property:Te})}return this.objectEntity=new At(e,Vt)}},ArrayPattern:class extends ft{addExportedVariables(e,t){for(const s of this.elements)null!==s&&s.addExportedVariables(e,t)}declare(e){const t=[];for(const s of this.elements)null!==s&&t.push(...s.declare(e,q));return t}deoptimizePath(e){if(0===e.length)for(const t of this.elements)null!==t&&t.deoptimizePath(e)}hasEffectsWhenAssignedAtPath(e,t){if(e.length>0)return!0;for(const e of this.elements)if(null!==e&&e.hasEffectsWhenAssignedAtPath(V,t))return!0;return!1}markDeclarationReached(){for(const e of this.elements)null!==e&&e.markDeclarationReached()}},ArrowFunctionExpression:As,AssignmentExpression:Cs,AssignmentPattern:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}addExportedVariables(e,t){this.left.addExportedVariables(e,t)}declare(e,t){return this.left.declare(e,t)}deoptimizePath(e){0===e.length&&this.left.deoptimizePath(e)}hasEffectsWhenAssignedAtPath(e,t){return e.length>0||this.left.hasEffectsWhenAssignedAtPath(V,t)}markDeclarationReached(){this.left.markDeclarationReached()}render(e,t,{isShorthandProperty:s}=Y){this.left.render(e,t,{isShorthandProperty:s}),this.right.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(V),this.right.deoptimizePath(B),this.context.requestTreeshakingPass()}},AwaitExpression:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}hasEffects(){return this.deoptimized||this.applyDeoptimizations(),!0}include(e,t){if(this.deoptimized||this.applyDeoptimizations(),!this.included){this.included=!0;e:if(!this.context.usesTopLevelAwait){let e=this.parent;do{if(e instanceof $s||e instanceof As)break e}while(e=e.parent);this.context.usesTopLevelAwait=!0}}this.argument.include(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.argument.deoptimizePath(B),this.context.requestTreeshakingPass()}},BinaryExpression:class extends ft{deoptimizeCache(){}getLiteralValueAtPath(e,t,s){if(e.length>0)return G;const i=this.left.getLiteralValueAtPath(V,t,s);if(i===G)return G;const n=this.right.getLiteralValueAtPath(V,t,s);if(n===G)return G;const r=Ts[this.operator];return r?r(i,n):G}hasEffects(e){return"+"===this.operator&&this.parent instanceof is&&""===this.left.getLiteralValueAtPath(V,j,this)||super.hasEffects(e)}hasEffectsWhenAccessedAtPath(e){return e.length>1}render(e,t,{renderedSurroundingElement:s}=Y){this.left.render(e,t,{renderedSurroundingElement:s}),this.right.render(e,t)}},BlockStatement:ns,BreakStatement:class extends ft{hasEffects(e){if(this.label){if(!e.ignore.labels.has(this.label.name))return!0;e.includedLabels.add(this.label.name),e.brokenFlow=2}else{if(!e.ignore.breaks)return!0;e.brokenFlow=1}return!1}include(e){this.included=!0,this.label&&(this.label.include(),e.includedLabels.add(this.label.name)),e.brokenFlow=this.label?2:1}},CallExpression:class extends ft{constructor(){super(...arguments),this.deoptimized=!1,this.deoptimizableDependentExpressions=[],this.expressionsToBeDeoptimized=new Set,this.returnExpression=null}bind(){super.bind(),this.callee instanceof xs&&(this.scope.findVariable(this.callee.name).isNamespace&&this.context.warn({code:"CANNOT_CALL_NAMESPACE",message:`Cannot call a namespace ('${this.callee.name}')`},this.start),"eval"===this.callee.name&&this.context.warn({code:"EVAL",message:"Use of eval is strongly discouraged, as it poses security risks and may cause issues with minification",url:"https://rollupjs.org/guide/en/#avoiding-eval"},this.start)),this.callOptions={args:this.arguments,thisParam:this.callee instanceof Ls&&!this.callee.variable?this.callee.object:null,withNew:!1}}deoptimizeCache(){if(this.returnExpression!==q){this.returnExpression=q;for(const e of this.deoptimizableDependentExpressions)e.deoptimizeCache();for(const e of this.expressionsToBeDeoptimized)e.deoptimizePath(B)}}deoptimizePath(e){if(0===e.length||this.context.deoptimizationTracker.trackEntityAtPathAndGetIfTracked(e,this))return;const t=this.getReturnExpression();t!==q&&t.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){const n=this.getReturnExpression(i);n===q?s.deoptimizePath(B):i.withTrackedEntityAtPath(t,n,(()=>{this.expressionsToBeDeoptimized.add(s),n.deoptimizeThisOnEventAtPath(e,t,s,i)}),void 0)}getLiteralValueAtPath(e,t,s){const i=this.getReturnExpression(t);return i===q?G:t.withTrackedEntityAtPath(e,i,(()=>(this.deoptimizableDependentExpressions.push(s),i.getLiteralValueAtPath(e,t,s))),G)}getReturnExpressionWhenCalledAtPath(e,t,s,i){const n=this.getReturnExpression(s);return this.returnExpression===q?q:s.withTrackedEntityAtPath(e,n,(()=>(this.deoptimizableDependentExpressions.push(i),n.getReturnExpressionWhenCalledAtPath(e,t,s,i))),q)}hasEffects(e){try{for(const t of this.arguments)if(t.hasEffects(e))return!0;return(!this.context.options.treeshake.annotations||!this.annotations)&&(this.callee.hasEffects(e)||this.callee.hasEffectsWhenCalledAtPath(V,this.callOptions,e))}finally{this.deoptimized||this.applyDeoptimizations()}}hasEffectsWhenAccessedAtPath(e,t){return!t.accessed.trackEntityAtPathAndGetIfTracked(e,this)&&this.getReturnExpression().hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return!t.assigned.trackEntityAtPathAndGetIfTracked(e,this)&&this.getReturnExpression().hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return!(t.withNew?s.instantiated:s.called).trackEntityAtPathAndGetIfTracked(e,t,this)&&this.getReturnExpression().hasEffectsWhenCalledAtPath(e,t,s)}include(e,t){this.deoptimized||this.applyDeoptimizations(),t?(super.include(e,t),t===pt&&this.callee instanceof xs&&this.callee.variable&&this.callee.variable.markCalledFromTryStatement()):(this.included=!0,this.callee.include(e,!1)),this.callee.includeCallArguments(e,this.arguments);const s=this.getReturnExpression();s.included||s.include(e,!1)}render(e,t,{renderedSurroundingElement:s}=Y){if(this.callee.render(e,t,{isCalleeOfRenderedParent:!0,renderedSurroundingElement:s}),this.arguments.length>0)if(this.arguments[this.arguments.length-1].included)for(const s of this.arguments)s.render(e,t);else{let s=this.arguments.length-2;for(;s>=0&&!this.arguments[s].included;)s--;if(s>=0){for(let i=0;i<=s;i++)this.arguments[i].render(e,t);e.remove(Xt(e.original,",",this.arguments[s].end),this.end-1)}else e.remove(Xt(e.original,"(",this.callee.end)+1,this.end-1)}}applyDeoptimizations(){this.deoptimized=!0;const{thisParam:e}=this.callOptions;e&&this.callee.deoptimizeThisOnEventAtPath(2,V,e,j);for(const e of this.arguments)e.deoptimizePath(B);this.context.requestTreeshakingPass()}getReturnExpression(e=j){return null===this.returnExpression?(this.returnExpression=q,this.returnExpression=this.callee.getReturnExpressionWhenCalledAtPath(V,this.callOptions,e,this)):this.returnExpression}},CatchClause:class extends ft{createScope(e){this.scope=new Os(e,this.context)}parseNode(e){const{param:t}=e;t&&(this.param=new(this.context.getNodeConstructor(t.type))(t,this,this.scope),this.param.declare("parameter",q)),super.parseNode(e)}},ChainExpression:class extends ft{},ClassBody:class extends ft{createScope(e){this.scope=new Vs(e,this.parent,this.context)}include(e,t){this.included=!0,this.context.includeVariableInModule(this.scope.thisVariable);for(const s of this.body)s.include(e,t)}parseNode(e){const t=this.body=[];for(const s of e.body)t.push(new(this.context.getNodeConstructor(s.type))(s,this,s.static?this.scope:this.scope.instanceScope));super.parseNode(e)}},ClassDeclaration:js,ClassExpression:class extends Ws{render(e,t,{renderedSurroundingElement:s}=Y){super.render(e,t),s===Ze&&(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}},ConditionalExpression:class extends ft{constructor(){super(...arguments),this.expressionsToBeDeoptimized=[],this.isBranchResolutionAnalysed=!1,this.usedBranch=null}deoptimizeCache(){if(null!==this.usedBranch){const e=this.usedBranch===this.consequent?this.alternate:this.consequent;this.usedBranch=null,e.deoptimizePath(B);for(const e of this.expressionsToBeDeoptimized)e.deoptimizeCache()}}deoptimizePath(e){const t=this.getUsedBranch();null===t?(this.consequent.deoptimizePath(e),this.alternate.deoptimizePath(e)):t.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.consequent.deoptimizeThisOnEventAtPath(e,t,s,i),this.alternate.deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){const i=this.getUsedBranch();return null===i?G:(this.expressionsToBeDeoptimized.push(s),i.getLiteralValueAtPath(e,t,s))}getReturnExpressionWhenCalledAtPath(e,t,s,i){const n=this.getUsedBranch();return null===n?new Us([this.consequent.getReturnExpressionWhenCalledAtPath(e,t,s,i),this.alternate.getReturnExpressionWhenCalledAtPath(e,t,s,i)]):(this.expressionsToBeDeoptimized.push(i),n.getReturnExpressionWhenCalledAtPath(e,t,s,i))}hasEffects(e){if(this.test.hasEffects(e))return!0;const t=this.getUsedBranch();return null===t?this.consequent.hasEffects(e)||this.alternate.hasEffects(e):t.hasEffects(e)}hasEffectsWhenAccessedAtPath(e,t){const s=this.getUsedBranch();return null===s?this.consequent.hasEffectsWhenAccessedAtPath(e,t)||this.alternate.hasEffectsWhenAccessedAtPath(e,t):s.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){const s=this.getUsedBranch();return null===s?this.consequent.hasEffectsWhenAssignedAtPath(e,t)||this.alternate.hasEffectsWhenAssignedAtPath(e,t):s.hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){const i=this.getUsedBranch();return null===i?this.consequent.hasEffectsWhenCalledAtPath(e,t,s)||this.alternate.hasEffectsWhenCalledAtPath(e,t,s):i.hasEffectsWhenCalledAtPath(e,t,s)}include(e,t){this.included=!0;const s=this.getUsedBranch();t||this.test.shouldBeIncluded(e)||null===s?(this.test.include(e,t),this.consequent.include(e,t),this.alternate.include(e,t)):s.include(e,t)}includeCallArguments(e,t){const s=this.getUsedBranch();null===s?(this.consequent.includeCallArguments(e,t),this.alternate.includeCallArguments(e,t)):s.includeCallArguments(e,t)}render(e,t,{isCalleeOfRenderedParent:s,preventASI:i,renderedParentType:n,renderedSurroundingElement:r}=Y){const a=this.getUsedBranch();if(this.test.included)this.test.render(e,t,{renderedSurroundingElement:r}),this.consequent.render(e,t),this.alternate.render(e,t);else{const o=Xt(e.original,":",this.consequent.end),h=Qt(e.original,(this.consequent.included?Xt(e.original,"?",this.test.end):o)+1);i&&ts(e,h,a.start),e.remove(this.start,h),this.consequent.included&&e.remove(o,this.end),qt(this,e),a.render(e,t,{isCalleeOfRenderedParent:s,preventASI:!0,renderedParentType:n||this.parent.type,renderedSurroundingElement:r||this.parent.type})}}getUsedBranch(){if(this.isBranchResolutionAnalysed)return this.usedBranch;this.isBranchResolutionAnalysed=!0;const e=this.test.getLiteralValueAtPath(V,j,this);return e===G?null:this.usedBranch=e?this.consequent:this.alternate}},ContinueStatement:class extends ft{hasEffects(e){if(this.label){if(!e.ignore.labels.has(this.label.name))return!0;e.includedLabels.add(this.label.name),e.brokenFlow=2}else{if(!e.ignore.continues)return!0;e.brokenFlow=1}return!1}include(e){this.included=!0,this.label&&(this.label.include(),e.includedLabels.add(this.label.name)),e.brokenFlow=this.label?2:1}},DoWhileStatement:class extends ft{hasEffects(e){if(this.test.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:s,continues:i}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=s,e.ignore.continues=i,e.brokenFlow=t,!1)}include(e,t){this.included=!0,this.test.include(e,t);const{brokenFlow:s}=e;this.body.includeAsSingleStatement(e,t),e.brokenFlow=s}},EmptyStatement:class extends ft{hasEffects(){return!1}},ExportAllDeclaration:Gs,ExportDefaultDeclaration:qs,ExportNamedDeclaration:Ks,ExportSpecifier:class extends ft{},ExpressionStatement:is,ForInStatement:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}createScope(e){this.scope=new ss(e)}hasEffects(e){if(this.deoptimized||this.applyDeoptimizations(),this.left&&(this.left.hasEffects(e)||this.left.hasEffectsWhenAssignedAtPath(V,e))||this.right&&this.right.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:s,continues:i}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=s,e.ignore.continues=i,e.brokenFlow=t,!1)}include(e,t){this.deoptimized||this.applyDeoptimizations(),this.included=!0,this.left.include(e,t||!0),this.right.include(e,t);const{brokenFlow:s}=e;this.body.includeAsSingleStatement(e,t),e.brokenFlow=s}render(e,t){this.left.render(e,t,Kt),this.right.render(e,t,Kt),110===e.original.charCodeAt(this.right.start-1)&&e.prependLeft(this.right.start," "),this.body.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(V),this.context.requestTreeshakingPass()}},ForOfStatement:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}createScope(e){this.scope=new ss(e)}hasEffects(){return this.deoptimized||this.applyDeoptimizations(),!0}include(e,t){this.deoptimized||this.applyDeoptimizations(),this.included=!0,this.left.include(e,t||!0),this.right.include(e,t);const{brokenFlow:s}=e;this.body.includeAsSingleStatement(e,t),e.brokenFlow=s}render(e,t){this.left.render(e,t,Kt),this.right.render(e,t,Kt),102===e.original.charCodeAt(this.right.start-1)&&e.prependLeft(this.right.start," "),this.body.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(V),this.context.requestTreeshakingPass()}},ForStatement:class extends ft{createScope(e){this.scope=new ss(e)}hasEffects(e){if(this.init&&this.init.hasEffects(e)||this.test&&this.test.hasEffects(e)||this.update&&this.update.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:s,continues:i}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=s,e.ignore.continues=i,e.brokenFlow=t,!1)}include(e,t){this.included=!0,this.init&&this.init.includeAsSingleStatement(e,t),this.test&&this.test.include(e,t);const{brokenFlow:s}=e;this.update&&this.update.include(e,t),this.body.includeAsSingleStatement(e,t),e.brokenFlow=s}render(e,t){this.init&&this.init.render(e,t,Kt),this.test&&this.test.render(e,t,Kt),this.update&&this.update.render(e,t,Kt),this.body.render(e,t)}},FunctionDeclaration:Hs,FunctionExpression:class extends $s{render(e,t,{renderedSurroundingElement:s}=Y){super.render(e,t),s===Ze&&(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}},Identifier:xs,IfStatement:Qs,ImportDeclaration:Zs,ImportDefaultSpecifier:class extends ft{},ImportExpression:class extends ft{constructor(){super(...arguments),this.inlineNamespace=null,this.mechanism=null,this.resolution=null}hasEffects(){return!0}include(e,t){this.included||(this.included=!0,this.context.includeDynamicImport(this),this.scope.addAccessedDynamicImport(this)),this.source.include(e,t)}initialise(){this.context.addDynamicImport(this)}render(e,t){if(this.inlineNamespace){const{snippets:{getDirectReturnFunction:s,getPropertyAccess:i}}=t,[n,r]=s([],{functionReturn:!0,lineBreakIndent:null,name:null});e.overwrite(this.start,this.end,`Promise.resolve().then(${n}${this.inlineNamespace.getName(i)}${r})`,{contentOnly:!0})}else this.mechanism&&(e.overwrite(this.start,Xt(e.original,"(",this.start+6)+1,this.mechanism.left,{contentOnly:!0}),e.overwrite(this.end-1,this.end,this.mechanism.right,{contentOnly:!0})),this.source.render(e,t)}renderFinalResolution(e,t,s,{getDirectReturnFunction:i}){if(e.overwrite(this.source.start,this.source.end,t),s){const[t,n]=i(["n"],{functionReturn:!0,lineBreakIndent:null,name:null});e.prependLeft(this.end,`.then(${t}n.${s}${n})`)}}setExternalResolution(e,t,s,i,n,r){const{format:a}=s;this.resolution=t;const o=[...ki[a]||[]];let h;({helper:h,mechanism:this.mechanism}=this.getDynamicImportMechanismAndHelper(t,e,s,i,n)),h&&o.push(h),o.length>0&&this.scope.addAccessedGlobals(o,r)}setInternalResolution(e){this.inlineNamespace=e}getDynamicImportMechanismAndHelper(e,t,{compact:s,dynamicImportFunction:i,format:n,generatedCode:{arrowFunctions:r},interop:a},{_:o,getDirectReturnFunction:h,getDirectReturnIifeLeft:l},c){const u=c.hookFirstSync("renderDynamicImport",[{customResolution:"string"==typeof this.resolution?this.resolution:null,format:n,moduleId:this.context.module.id,targetModuleId:this.resolution&&"string"!=typeof this.resolution?this.resolution.id:null}]);if(u)return{helper:null,mechanism:u};const d=!this.resolution||"string"==typeof this.resolution;switch(n){case"cjs":{const s=Pi(e,t,a);let i="require(",n=")";s&&(i=`/*#__PURE__*/${s}(${i}`,n+=")");const[o,c]=h([],{functionReturn:!0,lineBreakIndent:null,name:null});return i=`Promise.resolve().then(${o}${i}`,n+=`${c})`,!r&&d&&(i=l(["t"],`${i}t${n}`,{needsArrowReturnParens:!1,needsWrappedFunction:!0}),n=")"),{helper:s,mechanism:{left:i,right:n}}}case"amd":{const i=s?"c":"resolve",n=s?"e":"reject",c=Pi(e,t,a),[u,p]=h(["m"],{functionReturn:!1,lineBreakIndent:null,name:null}),f=c?`${u}${i}(/*#__PURE__*/${c}(m))${p}`:i,[m,g]=h([i,n],{functionReturn:!1,lineBreakIndent:null,name:null});let y=`new Promise(${m}require([`,E=`],${o}${f},${o}${n})${g})`;return!r&&d&&(y=l(["t"],`${y}t${E}`,{needsArrowReturnParens:!1,needsWrappedFunction:!0}),E=")"),{helper:c,mechanism:{left:y,right:E}}}case"system":return{helper:null,mechanism:{left:"module.import(",right:")"}};case"es":if(i)return{helper:null,mechanism:{left:`${i}(`,right:")"}}}return{helper:null,mechanism:null}}},ImportNamespaceSpecifier:class extends ft{},ImportSpecifier:class extends ft{},LabeledStatement:class extends ft{hasEffects(e){const t=e.brokenFlow;return e.ignore.labels.add(this.label.name),!!this.body.hasEffects(e)||(e.ignore.labels.delete(this.label.name),e.includedLabels.has(this.label.name)&&(e.includedLabels.delete(this.label.name),e.brokenFlow=t),!1)}include(e,t){this.included=!0;const s=e.brokenFlow;this.body.include(e,t),(t||e.includedLabels.has(this.label.name))&&(this.label.include(),e.includedLabels.delete(this.label.name),e.brokenFlow=s)}render(e,t){this.label.included?this.label.render(e,t):e.remove(this.start,Qt(e.original,Xt(e.original,":",this.label.end)+1)),this.body.render(e,t)}},Literal:Rs,LogicalExpression:class extends ft{constructor(){super(...arguments),this.expressionsToBeDeoptimized=[],this.isBranchResolutionAnalysed=!1,this.usedBranch=null}deoptimizeCache(){if(null!==this.usedBranch){const e=this.usedBranch===this.left?this.right:this.left;this.usedBranch=null,e.deoptimizePath(B);for(const e of this.expressionsToBeDeoptimized)e.deoptimizeCache()}}deoptimizePath(e){const t=this.getUsedBranch();null===t?(this.left.deoptimizePath(e),this.right.deoptimizePath(e)):t.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.left.deoptimizeThisOnEventAtPath(e,t,s,i),this.right.deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){const i=this.getUsedBranch();return null===i?G:(this.expressionsToBeDeoptimized.push(s),i.getLiteralValueAtPath(e,t,s))}getReturnExpressionWhenCalledAtPath(e,t,s,i){const n=this.getUsedBranch();return null===n?new Us([this.left.getReturnExpressionWhenCalledAtPath(e,t,s,i),this.right.getReturnExpressionWhenCalledAtPath(e,t,s,i)]):(this.expressionsToBeDeoptimized.push(i),n.getReturnExpressionWhenCalledAtPath(e,t,s,i))}hasEffects(e){return!!this.left.hasEffects(e)||this.getUsedBranch()!==this.left&&this.right.hasEffects(e)}hasEffectsWhenAccessedAtPath(e,t){const s=this.getUsedBranch();return null===s?this.left.hasEffectsWhenAccessedAtPath(e,t)||this.right.hasEffectsWhenAccessedAtPath(e,t):s.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){const s=this.getUsedBranch();return null===s?this.left.hasEffectsWhenAssignedAtPath(e,t)||this.right.hasEffectsWhenAssignedAtPath(e,t):s.hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){const i=this.getUsedBranch();return null===i?this.left.hasEffectsWhenCalledAtPath(e,t,s)||this.right.hasEffectsWhenCalledAtPath(e,t,s):i.hasEffectsWhenCalledAtPath(e,t,s)}include(e,t){this.included=!0;const s=this.getUsedBranch();t||s===this.right&&this.left.shouldBeIncluded(e)||null===s?(this.left.include(e,t),this.right.include(e,t)):s.include(e,t)}render(e,t,{isCalleeOfRenderedParent:s,preventASI:i,renderedParentType:n,renderedSurroundingElement:r}=Y){if(this.left.included&&this.right.included)this.left.render(e,t,{preventASI:i,renderedSurroundingElement:r}),this.right.render(e,t);else{const a=Xt(e.original,this.operator,this.left.end);if(this.right.included){const t=Qt(e.original,a+2);e.remove(this.start,t),i&&ts(e,t,this.right.start)}else e.remove(a,this.end);qt(this,e),this.getUsedBranch().render(e,t,{isCalleeOfRenderedParent:s,preventASI:i,renderedParentType:n||this.parent.type,renderedSurroundingElement:r||this.parent.type})}}getUsedBranch(){if(!this.isBranchResolutionAnalysed){this.isBranchResolutionAnalysed=!0;const e=this.left.getLiteralValueAtPath(V,j,this);if(e===G)return null;this.usedBranch="||"===this.operator&&e||"&&"===this.operator&&!e||"??"===this.operator&&null!=e?this.left:this.right}return this.usedBranch}},MemberExpression:Ls,MetaProperty:class extends ft{addAccessedGlobals(e,t){const s=this.metaProperty,i=(s&&(s.startsWith(Ii)||s.startsWith(wi)||s.startsWith(Ci))?_i:Ni)[e];i.length>0&&this.scope.addAccessedGlobals(i,t)}getReferencedFileName(e){const t=this.metaProperty;return t&&t.startsWith(Ii)?e.getFileName(t.substring(Ii.length)):null}hasEffects(){return!1}hasEffectsWhenAccessedAtPath(e){return e.length>1}include(){if(!this.included&&(this.included=!0,"import"===this.meta.name)){this.context.addImportMeta(this);const e=this.parent;this.metaProperty=e instanceof Ls&&"string"==typeof e.propertyKey?e.propertyKey:null}}renderFinalMechanism(e,t,s,i,n){var r;const a=this.parent,o=this.metaProperty;if(o&&(o.startsWith(Ii)||o.startsWith(wi)||o.startsWith(Ci))){let i,r=null,h=null,l=null;o.startsWith(Ii)?(r=o.substring(Ii.length),i=n.getFileName(r)):o.startsWith(wi)?(xe(`Using the "${wi}" prefix to reference files is deprecated. Use the "${Ii}" prefix instead.`,!0,this.context.options),h=o.substring(wi.length),i=n.getFileName(h)):(xe(`Using the "${Ci}" prefix to reference files is deprecated. Use the "${Ii}" prefix instead.`,!0,this.context.options),l=o.substring(Ci.length),i=n.getFileName(l));const c=N(R($(t),i));let u;return null!==h&&(u=n.hookFirstSync("resolveAssetUrl",[{assetFileName:i,chunkId:t,format:s,moduleId:this.context.module.id,relativeAssetPath:c}])),u||(u=n.hookFirstSync("resolveFileUrl",[{assetReferenceId:h,chunkId:t,chunkReferenceId:l,fileName:i,format:s,moduleId:this.context.module.id,referenceId:r||h||l,relativePath:c}])||Di[s](c)),void e.overwrite(a.start,a.end,u,{contentOnly:!0})}const h=n.hookFirstSync("resolveImportMeta",[o,{chunkId:t,format:s,moduleId:this.context.module.id}])||(null===(r=Li[s])||void 0===r?void 0:r.call(Li,o,{chunkId:t,snippets:i}));"string"==typeof h&&(a instanceof Ls?e.overwrite(a.start,a.end,h,{contentOnly:!0}):e.overwrite(this.start,this.end,h,{contentOnly:!0}))}},MethodDefinition:Fs,NewExpression:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}hasEffects(e){this.deoptimized||this.applyDeoptimizations();for(const t of this.arguments)if(t.hasEffects(e))return!0;return(!this.context.options.treeshake.annotations||!this.annotations)&&(this.callee.hasEffects(e)||this.callee.hasEffectsWhenCalledAtPath(V,this.callOptions,e))}hasEffectsWhenAccessedAtPath(e){return e.length>0}initialise(){this.callOptions={args:this.arguments,thisParam:null,withNew:!0}}applyDeoptimizations(){this.deoptimized=!0;for(const e of this.arguments)e.deoptimizePath(B);this.context.requestTreeshakingPass()}},ObjectExpression:class extends ft{constructor(){super(...arguments),this.objectEntity=null}deoptimizeCache(){this.getObjectEntity().deoptimizeAllProperties()}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.getObjectEntity().deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.getObjectEntity().getLiteralValueAtPath(e,t,s)}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,s,i)}hasEffectsWhenAccessedAtPath(e,t){return this.getObjectEntity().hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return this.getObjectEntity().hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return this.getObjectEntity().hasEffectsWhenCalledAtPath(e,t,s)}render(e,t,{renderedSurroundingElement:s}=Y){super.render(e,t),s!==Ze&&s!==Xe||(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}getObjectEntity(){if(null!==this.objectEntity)return this.objectEntity;let e=kt;const t=[];for(const s of this.properties){if(s instanceof mt){t.push({key:L,kind:"init",property:s});continue}let i;if(s.computed){const e=s.key.getLiteralValueAtPath(V,j,this);if(e===G){t.push({key:L,kind:s.kind,property:s});continue}i=String(e)}else if(i=s.key instanceof xs?s.key.name:String(s.key.value),"__proto__"===i&&"init"===s.kind){e=s.value instanceof Rs&&null===s.value.value?null:s.value;continue}t.push({key:i,kind:s.kind,property:s})}return this.objectEntity=new At(t,e)}},ObjectPattern:ws,PrivateIdentifier:class extends ft{},Program:Oi,Property:class extends Bs{constructor(){super(...arguments),this.deoptimized=!1,this.declarationInit=null}declare(e,t){return this.declarationInit=t,this.value.declare(e,q)}hasEffects(e){this.deoptimized||this.applyDeoptimizations();const t=this.context.options.treeshake.propertyReadSideEffects;return"ObjectPattern"===this.parent.type&&"always"===t||this.key.hasEffects(e)||this.value.hasEffects(e)}markDeclarationReached(){this.value.markDeclarationReached()}render(e,t){this.shorthand||this.key.render(e,t),this.value.render(e,t,{isShorthandProperty:this.shorthand})}applyDeoptimizations(){this.deoptimized=!0,null!==this.declarationInit&&(this.declarationInit.deoptimizePath([L,L]),this.context.requestTreeshakingPass())}},PropertyDefinition:class extends ft{deoptimizePath(e){var t;null===(t=this.value)||void 0===t||t.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){var n;null===(n=this.value)||void 0===n||n.deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.value?this.value.getLiteralValueAtPath(e,t,s):G}getReturnExpressionWhenCalledAtPath(e,t,s,i){return this.value?this.value.getReturnExpressionWhenCalledAtPath(e,t,s,i):q}hasEffects(e){return this.key.hasEffects(e)||this.static&&null!==this.value&&this.value.hasEffects(e)}hasEffectsWhenAccessedAtPath(e,t){return!this.value||this.value.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return!this.value||this.value.hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return!this.value||this.value.hasEffectsWhenCalledAtPath(e,t,s)}},RestElement:vs,ReturnStatement:class extends ft{hasEffects(e){return!(e.ignore.returnYield&&(null===this.argument||!this.argument.hasEffects(e))&&(e.brokenFlow=2,1))}include(e,t){this.included=!0,this.argument&&this.argument.include(e,t),e.brokenFlow=2}initialise(){this.scope.addReturnExpression(this.argument||q)}render(e,t){this.argument&&(this.argument.render(e,t,{preventASI:!0}),this.argument.start===this.start+6&&e.prependLeft(this.start+6," "))}},SequenceExpression:class extends ft{deoptimizePath(e){this.expressions[this.expressions.length-1].deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.expressions[this.expressions.length-1].deoptimizeThisOnEventAtPath(e,t,s,i)}getLiteralValueAtPath(e,t,s){return this.expressions[this.expressions.length-1].getLiteralValueAtPath(e,t,s)}hasEffects(e){for(const t of this.expressions)if(t.hasEffects(e))return!0;return!1}hasEffectsWhenAccessedAtPath(e,t){return e.length>0&&this.expressions[this.expressions.length-1].hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return this.expressions[this.expressions.length-1].hasEffectsWhenAssignedAtPath(e,t)}hasEffectsWhenCalledAtPath(e,t,s){return this.expressions[this.expressions.length-1].hasEffectsWhenCalledAtPath(e,t,s)}include(e,t){this.included=!0;const s=this.expressions[this.expressions.length-1];for(const i of this.expressions)(t||i===s&&!(this.parent instanceof is)||i.shouldBeIncluded(e))&&i.include(e,t)}render(e,t,{renderedParentType:s,isCalleeOfRenderedParent:i,preventASI:n}=Y){let r=0,a=null;const o=this.expressions[this.expressions.length-1];for(const{node:h,separator:l,start:c,end:u}of es(this.expressions,e,this.start,this.end))if(h.included)if(r++,a=l,1===r&&n&&ts(e,c,h.start),1===r){const n=s||this.parent.type;h.render(e,t,{isCalleeOfRenderedParent:i&&h===o,renderedParentType:n,renderedSurroundingElement:n})}else h.render(e,t);else Ht(h,e,c,u);a&&e.remove(a,this.end)}},SpreadElement:mt,StaticBlock:class extends ft{createScope(e){this.scope=new ss(e)}hasEffects(e){for(const t of this.body)if(t.hasEffects(e))return!0;return!1}include(e,t){this.included=!0;for(const s of this.body)(t||s.shouldBeIncluded(e))&&s.include(e,t)}render(e,t){this.body.length?Jt(this.body,e,this.start+1,this.end-1,t):super.render(e,t)}},Super:class extends ft{bind(){this.variable=this.scope.findVariable("this")}deoptimizePath(e){this.variable.deoptimizePath(e)}include(){this.included||(this.included=!0,this.context.includeVariableInModule(this.variable))}},SwitchCase:Vi,SwitchStatement:class extends ft{createScope(e){this.scope=new ss(e)}hasEffects(e){if(this.discriminant.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:s}}=e;let i=1/0;e.ignore.breaks=!0;for(const s of this.cases){if(s.hasEffects(e))return!0;i=e.brokenFlow<i?e.brokenFlow:i,e.brokenFlow=t}return null!==this.defaultCase&&1!==i&&(e.brokenFlow=i),e.ignore.breaks=s,!1}include(e,t){this.included=!0,this.discriminant.include(e,t);const{brokenFlow:s}=e;let i=1/0,n=t||null!==this.defaultCase&&this.defaultCase<this.cases.length-1;for(let r=this.cases.length-1;r>=0;r--){const a=this.cases[r];if(a.included&&(n=!0),!n){const e=Ne();e.ignore.breaks=!0,n=a.hasEffects(e)}n?(a.include(e,t),i=i<e.brokenFlow?i:e.brokenFlow,e.brokenFlow=s):i=s}n&&null!==this.defaultCase&&1!==i&&(e.brokenFlow=i)}initialise(){for(let e=0;e<this.cases.length;e++)if(null===this.cases[e].test)return void(this.defaultCase=e);this.defaultCase=null}render(e,t){this.discriminant.render(e,t),this.cases.length>0&&Jt(this.cases,e,this.cases[0].start,this.end-1,t)}},TaggedTemplateExpression:class extends ft{bind(){if(super.bind(),this.tag.type===Je){const e=this.tag.name;this.scope.findVariable(e).isNamespace&&this.context.warn({code:"CANNOT_CALL_NAMESPACE",message:`Cannot call a namespace ('${e}')`},this.start)}}hasEffects(e){return super.hasEffects(e)||this.tag.hasEffectsWhenCalledAtPath(V,this.callOptions,e)}initialise(){this.callOptions={args:_e,thisParam:null,withNew:!1}}render(e,t){this.tag.render(e,t,{isCalleeOfRenderedParent:!0}),this.quasi.render(e,t)}},TemplateElement:class extends ft{bind(){}hasEffects(){return!1}include(){this.included=!0}parseNode(e){this.value=e.value,super.parseNode(e)}render(){}},TemplateLiteral:Bi,ThisExpression:class extends ft{bind(){this.variable=this.scope.findVariable("this")}deoptimizePath(e){this.variable.deoptimizePath(e)}deoptimizeThisOnEventAtPath(e,t,s,i){this.variable.deoptimizeThisOnEventAtPath(e,t,s===this?this.variable:s,i)}hasEffectsWhenAccessedAtPath(e,t){return e.length>0&&this.variable.hasEffectsWhenAccessedAtPath(e,t)}hasEffectsWhenAssignedAtPath(e,t){return this.variable.hasEffectsWhenAssignedAtPath(e,t)}include(){this.included||(this.included=!0,this.context.includeVariableInModule(this.variable))}initialise(){this.alias=this.scope.findLexicalBoundary()instanceof Wi?this.context.moduleContext:null,"undefined"===this.alias&&this.context.warn({code:"THIS_IS_UNDEFINED",message:"The 'this' keyword is equivalent to 'undefined' at the top level of an ES module, and has been rewritten",url:"https://rollupjs.org/guide/en/#error-this-is-undefined"},this.start)}render(e){null!==this.alias&&e.overwrite(this.start,this.end,this.alias,{contentOnly:!1,storeName:!0})}},ThrowStatement:class extends ft{hasEffects(){return!0}include(e,t){this.included=!0,this.argument.include(e,t),e.brokenFlow=2}render(e,t){this.argument.render(e,t,{preventASI:!0}),this.argument.start===this.start+5&&e.prependLeft(this.start+5," ")}},TryStatement:class extends ft{constructor(){super(...arguments),this.directlyIncluded=!1,this.includedLabelsAfterBlock=null}hasEffects(e){return(this.context.options.treeshake.tryCatchDeoptimization?this.block.body.length>0:this.block.hasEffects(e))||null!==this.finalizer&&this.finalizer.hasEffects(e)}include(e,t){var s;const i=null===(s=this.context.options.treeshake)||void 0===s?void 0:s.tryCatchDeoptimization,{brokenFlow:n}=e;if(this.directlyIncluded&&i){if(this.includedLabelsAfterBlock)for(const t of this.includedLabelsAfterBlock)e.includedLabels.add(t)}else this.included=!0,this.directlyIncluded=!0,this.block.include(e,i?pt:t),e.includedLabels.size>0&&(this.includedLabelsAfterBlock=[...e.includedLabels]),e.brokenFlow=n;null!==this.handler&&(this.handler.include(e,t),e.brokenFlow=n),null!==this.finalizer&&this.finalizer.include(e,t)}},UnaryExpression:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}getLiteralValueAtPath(e,t,s){if(e.length>0)return G;const i=this.argument.getLiteralValueAtPath(V,t,s);return i===G?G:ji[this.operator](i)}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),!("typeof"===this.operator&&this.argument instanceof xs)&&(this.argument.hasEffects(e)||"delete"===this.operator&&this.argument.hasEffectsWhenAssignedAtPath(V,e))}hasEffectsWhenAccessedAtPath(e){return"void"===this.operator?e.length>0:e.length>1}applyDeoptimizations(){this.deoptimized=!0,"delete"===this.operator&&(this.argument.deoptimizePath(V),this.context.requestTreeshakingPass())}},UnknownNode:class extends ft{hasEffects(){return!0}include(e){super.include(e,!0)}},UpdateExpression:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),this.argument.hasEffects(e)||this.argument.hasEffectsWhenAssignedAtPath(V,e)}hasEffectsWhenAccessedAtPath(e){return e.length>1}render(e,t){const{exportNamesByVariable:s,format:i,snippets:{_:n}}=t;if(this.argument.render(e,t),"system"===i){const i=this.argument.variable,r=s.get(i);if(r)if(this.prefix)1===r.length?Ps(i,this.start,this.end,e,t):ks(i,this.start,this.end,this.parent.type!==Ze,e,t);else{const s=this.operator[0];!function(e,t,s,i,n,r,a){const{_:o}=r.snippets;n.prependRight(t,`${Ss([e],r,a)},${o}`),i&&(n.prependRight(t,"("),n.appendLeft(s,")"))}(i,this.start,this.end,this.parent.type!==Ze,e,t,`${n}${s}${n}1`)}}}applyDeoptimizations(){this.deoptimized=!0,this.argument.deoptimizePath(V),this.argument instanceof xs&&(this.scope.findVariable(this.argument.name).isReassigned=!0),this.context.requestTreeshakingPass()}},VariableDeclaration:Gi,VariableDeclarator:class extends ft{declareDeclarator(e){this.id.declare(e,this.init||Te)}deoptimizePath(e){this.id.deoptimizePath(e)}hasEffects(e){const t=null!==this.init&&this.init.hasEffects(e);return this.id.markDeclarationReached(),t||this.id.hasEffects(e)}include(e,t){this.included=!0,this.init&&this.init.include(e,t),this.id.markDeclarationReached(),(t||this.id.shouldBeIncluded(e))&&this.id.include(e,t)}render(e,t){const{exportNamesByVariable:s,snippets:{_:i}}=t,n=this.id.included;if(n)this.id.render(e,t);else{const t=Xt(e.original,"=",this.id.end);e.remove(this.start,Qt(e.original,t+1))}this.init?this.init.render(e,t,n?Y:{renderedSurroundingElement:Ze}):this.id instanceof xs&&Ui(this.id.variable,s)&&e.appendLeft(this.end,`${i}=${i}void 0`)}},WhileStatement:class extends ft{hasEffects(e){if(this.test.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:s,continues:i}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=s,e.ignore.continues=i,e.brokenFlow=t,!1)}include(e,t){this.included=!0,this.test.include(e,t);const{brokenFlow:s}=e;this.body.includeAsSingleStatement(e,t),e.brokenFlow=s}},YieldExpression:class extends ft{constructor(){super(...arguments),this.deoptimized=!1}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),!e.ignore.returnYield||null!==this.argument&&this.argument.hasEffects(e)}render(e,t){this.argument&&(this.argument.render(e,t,{preventASI:!0}),this.argument.start===this.start+5&&e.prependLeft(this.start+5," "))}applyDeoptimizations(){this.deoptimized=!0;const{argument:e}=this;e&&(e.deoptimizePath(B),this.context.requestTreeshakingPass())}}},qi="_missingExportShim";class Ki extends K{constructor(e){super(qi),this.module=e}include(){super.include(),this.module.needsExportShim=!0}}class Xi extends K{constructor(e){super(e.getModuleName()),this.memberVariables=null,this.mergedNamespaces=[],this.referencedEarly=!1,this.references=[],this.context=e,this.module=e.module}addReference(e){this.references.push(e),this.name=e.name}getMemberVariables(){if(this.memberVariables)return this.memberVariables;const e=Object.create(null);for(const t of this.context.getExports().concat(this.context.getReexports()))if("*"!==t[0]&&t!==this.module.info.syntheticNamedExports){const s=this.context.traceExport(t);s&&(e[t]=s)}return this.memberVariables=e}include(){this.included=!0,this.context.includeAllExports()}prepare(e){this.mergedNamespaces.length>0&&this.module.scope.addAccessedGlobals([ni],e)}renderBlock(e){const{exportNamesByVariable:t,format:s,freeze:i,indent:n,namespaceToStringTag:r,snippets:{_:a,cnst:o,getObject:h,getPropertyAccess:l,n:c,s:u}}=e,d=this.getMemberVariables(),p=Object.entries(d).map((([e,t])=>this.referencedEarly||t.isReassigned?[null,`get ${e}${a}()${a}{${a}return ${t.getName(l)}${u}${a}}`]:[e,t.getName(l)]));p.unshift([null,`__proto__:${a}null`]);let f=h(p,{lineBreakIndent:{base:"",t:n}});if(this.mergedNamespaces.length>0){const e=this.mergedNamespaces.map((e=>e.getName(l)));f=`/*#__PURE__*/_mergeNamespaces(${f},${a}[${e.join(`,${a}`)}])`}else r&&(f=`/*#__PURE__*/Object.defineProperty(${f},${a}Symbol.toStringTag,${a}${Si(h)})`),i&&(f=`/*#__PURE__*/Object.freeze(${f})`);return f=`${o} ${this.getName(l)}${a}=${a}${f};`,"system"===s&&t.has(this)&&(f+=`${c}${Ss([this],e)};`),f}renderFirst(){return this.referencedEarly}setMergedNamespaces(e){this.mergedNamespaces=e;const t=this.context.getModuleExecIndex();for(const e of this.references)if(e.context.getModuleExecIndex()<=t){this.referencedEarly=!0;break}}}Xi.prototype.isNamespace=!0;class Yi extends K{constructor(e,t,s){super(t),this.baseVariable=null,this.context=e,this.module=e.module,this.syntheticNamespace=s}getBaseVariable(){if(this.baseVariable)return this.baseVariable;let e=this.syntheticNamespace;for(;e instanceof zi||e instanceof Yi;){if(e instanceof zi){const t=e.getOriginalVariable();if(t===e)break;e=t}e instanceof Yi&&(e=e.syntheticNamespace)}return this.baseVariable=e}getBaseVariableName(){return this.syntheticNamespace.getBaseVariableName()}getName(e){return`${this.syntheticNamespace.getName(e)}${e(this.name)}`}include(){this.included=!0,this.context.includeVariableInModule(this.syntheticNamespace)}setRenderNames(e,t){super.setRenderNames(e,t)}}var Qi;function Zi(e){return e.id}!function(e){e[e.LOAD_AND_PARSE=0]="LOAD_AND_PARSE",e[e.ANALYSE=1]="ANALYSE",e[e.GENERATE=2]="GENERATE"}(Qi||(Qi={}));var Ji="performance"in("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{})?performance:{now:()=>0},en={memoryUsage:()=>({heapUsed:0})};const tn=()=>{};let sn=new Map;function nn(e,t){switch(t){case 1:return`# ${e}`;case 2:return`## ${e}`;case 3:return e;default:return`${"  ".repeat(t-4)}- ${e}`}}function rn(e,t=3){e=nn(e,t);const s=en.memoryUsage().heapUsed,i=Ji.now(),n=sn.get(e);void 0===n?sn.set(e,{memory:0,startMemory:s,startTime:i,time:0,totalMemory:0}):(n.startMemory=s,n.startTime=i)}function an(e,t=3){e=nn(e,t);const s=sn.get(e);if(void 0!==s){const e=en.memoryUsage().heapUsed;s.memory+=e-s.startMemory,s.time+=Ji.now()-s.startTime,s.totalMemory=Math.max(s.totalMemory,e)}}function on(){const e={};for(const[t,{memory:s,time:i,totalMemory:n}]of sn)e[t]=[i,s,n];return e}let hn=tn,ln=tn;const cn=["load","resolveDynamicImport","resolveId","transform"];function un(e,t){for(const s of cn)if(s in e){let i=`plugin ${t}`;e.name&&(i+=` (${e.name})`),i+=` - ${s}`;const n=e[s];e[s]=function(...e){hn(i,4);const t=n.apply(this,e);return ln(i,4),t&&"function"==typeof t.then?(hn(`${i} (async)`,4),t.then((e=>(ln(`${i} (async)`,4),e)))):t}}return e}function dn(e){e.isExecuted=!0;const t=[e],s=new Set;for(const e of t)for(const i of[...e.dependencies,...e.implicitlyLoadedBefore])i instanceof ke||i.isExecuted||!i.info.moduleSideEffects&&!e.implicitlyLoadedBefore.has(i)||s.has(i.id)||(i.isExecuted=!0,s.add(i.id),t.push(i))}const pn={identifier:null,localName:qi};function fn(e,t,s,i,n=new Map){const r=n.get(t);if(r){if(r.has(e))return i?[null]:oe((a=t,o=e.id,{code:le.CIRCULAR_REEXPORT,id:o,message:`"${a}" cannot be exported from ${ie(o)} as it is a reexport that references itself.`}));r.add(e)}else n.set(t,new Set([e]));var a,o;return e.getVariableForExportName(t,{importerForSideEffects:s,isExportAllSearch:i,searchedNamesAndModules:n})}class mn{constructor(e,t,s,i,n,r,a){this.graph=e,this.id=t,this.options=s,this.alternativeReexportModules=new Map,this.chunkFileNames=new Set,this.chunkNames=[],this.cycles=new Set,this.dependencies=new Set,this.dynamicDependencies=new Set,this.dynamicImporters=[],this.dynamicImports=[],this.execIndex=1/0,this.implicitlyLoadedAfter=new Set,this.implicitlyLoadedBefore=new Set,this.importDescriptions=new Map,this.importMetas=[],this.importedFromNotTreeshaken=!1,this.importers=[],this.imports=new Set,this.includedDynamicImporters=[],this.isExecuted=!1,this.isUserDefinedEntryPoint=!1,this.needsExportShim=!1,this.sideEffectDependenciesByVariable=new Map,this.sources=new Set,this.usesTopLevelAwait=!1,this.allExportNames=null,this.ast=null,this.exportAllModules=[],this.exportAllSources=new Set,this.exportNamesByVariable=null,this.exportShimVariable=new Ki(this),this.exports=new Map,this.namespaceReexportsByName=new Map,this.reexportDescriptions=new Map,this.relevantDependencies=null,this.syntheticExports=new Map,this.syntheticNamespace=null,this.transformDependencies=[],this.transitiveReexports=null,this.excludeFromSourcemap=/\0/.test(t),this.context=s.moduleContext(t),this.preserveSignature=this.options.preserveEntrySignatures;const o=this,{dynamicImports:h,dynamicImporters:l,implicitlyLoadedAfter:c,implicitlyLoadedBefore:u,importers:d,reexportDescriptions:p,sources:f}=this;this.info={ast:null,code:null,get dynamicallyImportedIdResolutions(){return h.map((({argument:e})=>"string"==typeof e&&o.resolvedIds[e])).filter(Boolean)},get dynamicallyImportedIds(){return h.map((({id:e})=>e)).filter((e=>null!=e))},get dynamicImporters(){return l.sort()},get hasDefaultExport(){return o.ast?o.exports.has("default")||p.has("default"):null},get hasModuleSideEffects(){return xe("Accessing ModuleInfo.hasModuleSideEffects from plugins is deprecated. Please use ModuleInfo.moduleSideEffects instead.",!1,s),this.moduleSideEffects},id:t,get implicitlyLoadedAfterOneOf(){return Array.from(c,Zi).sort()},get implicitlyLoadedBefore(){return Array.from(u,Zi).sort()},get importedIdResolutions(){return Array.from(f,(e=>o.resolvedIds[e])).filter(Boolean)},get importedIds(){return Array.from(f,(e=>{var t;return null===(t=o.resolvedIds[e])||void 0===t?void 0:t.id})).filter(Boolean)},get importers(){return d.sort()},isEntry:i,isExternal:!1,get isIncluded(){return e.phase!==Qi.GENERATE?null:o.isIncluded()},meta:{...a},moduleSideEffects:n,syntheticNamedExports:r},Object.defineProperty(this.info,"hasModuleSideEffects",{enumerable:!1})}basename(){const e=_(this.id),t=T(this.id);return Pe(t?e.slice(0,-t.length):e)}bindReferences(){this.ast.bind()}error(e,t){return this.addLocationToLogProps(e,t),oe(e)}getAllExportNames(){if(this.allExportNames)return this.allExportNames;this.allExportNames=new Set([...this.exports.keys(),...this.reexportDescriptions.keys()]);for(const e of this.exportAllModules)if(e instanceof ke)this.allExportNames.add(`*${e.id}`);else for(const t of e.getAllExportNames())"default"!==t&&this.allExportNames.add(t);return"string"==typeof this.info.syntheticNamedExports&&this.allExportNames.delete(this.info.syntheticNamedExports),this.allExportNames}getDependenciesToBeIncluded(){if(this.relevantDependencies)return this.relevantDependencies;this.relevantDependencies=new Set;const e=new Set,t=new Set,s=new Set(this.imports);if(this.info.isEntry||this.includedDynamicImporters.length>0||this.namespace.included||this.implicitlyLoadedAfter.size>0)for(const e of[...this.getReexports(),...this.getExports()]){const[t]=this.getVariableForExportName(e);t&&s.add(t)}for(let i of s){const s=this.sideEffectDependenciesByVariable.get(i);if(s)for(const e of s)t.add(e);i instanceof Yi?i=i.getBaseVariable():i instanceof zi&&(i=i.getOriginalVariable()),e.add(i.module)}if(this.options.treeshake&&"no-treeshake"!==this.info.moduleSideEffects)this.addRelevantSideEffectDependencies(this.relevantDependencies,e,t);else for(const e of this.dependencies)this.relevantDependencies.add(e);for(const t of e)this.relevantDependencies.add(t);return this.relevantDependencies}getExportNamesByVariable(){if(this.exportNamesByVariable)return this.exportNamesByVariable;const e=new Map;for(const t of this.getAllExportNames()){let[s]=this.getVariableForExportName(t);if(s instanceof zi&&(s=s.getOriginalVariable()),!s||!(s.included||s instanceof X))continue;const i=e.get(s);i?i.push(t):e.set(s,[t])}return this.exportNamesByVariable=e}getExports(){return Array.from(this.exports.keys())}getReexports(){if(this.transitiveReexports)return this.transitiveReexports;this.transitiveReexports=[];const e=new Set(this.reexportDescriptions.keys());for(const t of this.exportAllModules)if(t instanceof ke)e.add(`*${t.id}`);else for(const s of[...t.getReexports(),...t.getExports()])"default"!==s&&e.add(s);return this.transitiveReexports=[...e]}getRenderedExports(){const e=[],t=[];for(const s of this.exports.keys()){const[i]=this.getVariableForExportName(s);(i&&i.included?e:t).push(s)}return{removedExports:t,renderedExports:e}}getSyntheticNamespace(){return null===this.syntheticNamespace&&(this.syntheticNamespace=void 0,[this.syntheticNamespace]=this.getVariableForExportName("string"==typeof this.info.syntheticNamedExports?this.info.syntheticNamedExports:"default",{onlyExplicit:!0})),this.syntheticNamespace?this.syntheticNamespace:oe((e=this.id,t=this.info.syntheticNamedExports,{code:le.SYNTHETIC_NAMED_EXPORTS_NEED_NAMESPACE_EXPORT,id:e,message:`Module "${ie(e)}" that is marked with 'syntheticNamedExports: ${JSON.stringify(t)}' needs ${"string"==typeof t&&"default"!==t?`an explicit export named "${t}"`:"a default export"} that does not reexport an unresolved named export of the same module.`}));var e,t}getVariableForExportName(e,{importerForSideEffects:t,isExportAllSearch:s,onlyExplicit:i,searchedNamesAndModules:n}=Q){var r;if("*"===e[0])return 1===e.length?[this.namespace]:this.graph.modulesById.get(e.slice(1)).getVariableForExportName("*");const a=this.reexportDescriptions.get(e);if(a){const[e]=fn(a.module,a.localName,t,!1,n);return e?(t&&gn(e,t,this),[e]):this.error(pe(a.localName,this.id,a.module.id),a.start)}const o=this.exports.get(e);if(o){if(o===pn)return[this.exportShimVariable];const e=o.localName,s=this.traceVariable(e,{importerForSideEffects:t,searchedNamesAndModules:n});return t&&(D(t.sideEffectDependenciesByVariable,s,(()=>new Set)).add(this),gn(s,t,this)),[s]}if(i)return[null];if("default"!==e){const s=null!==(r=this.namespaceReexportsByName.get(e))&&void 0!==r?r:this.getVariableFromNamespaceReexports(e,t,n);if(this.namespaceReexportsByName.set(e,s),s[0])return s}return this.info.syntheticNamedExports?[D(this.syntheticExports,e,(()=>new Yi(this.astContext,e,this.getSyntheticNamespace())))]:!s&&this.options.shimMissingExports?(this.shimMissingExport(e),[this.exportShimVariable]):[null]}hasEffects(){return"no-treeshake"===this.info.moduleSideEffects||this.ast.included&&this.ast.hasEffects(Ne())}include(){const e=Ie();this.ast.shouldBeIncluded(e)&&this.ast.include(e,!1)}includeAllExports(e){this.isExecuted||(dn(this),this.graph.needsTreeshakingPass=!0);for(const t of this.exports.keys())if(e||t!==this.info.syntheticNamedExports){const e=this.getVariableForExportName(t)[0];e.deoptimizePath(B),e.included||this.includeVariable(e)}for(const e of this.getReexports()){const[t]=this.getVariableForExportName(e);t&&(t.deoptimizePath(B),t.included||this.includeVariable(t),t instanceof X&&(t.module.reexported=!0))}e&&this.namespace.setMergedNamespaces(this.includeAndGetAdditionalMergedNamespaces())}includeAllInBundle(){this.ast.include(Ie(),!0),this.includeAllExports(!1)}isIncluded(){return this.ast.included||this.namespace.included||this.importedFromNotTreeshaken}linkImports(){this.addModulesToImportDescriptions(this.importDescriptions),this.addModulesToImportDescriptions(this.reexportDescriptions);const e=[];for(const t of this.exportAllSources){const s=this.graph.modulesById.get(this.resolvedIds[t].id);s instanceof ke?e.push(s):this.exportAllModules.push(s)}this.exportAllModules.push(...e)}render(e){const t=this.magicString.clone();return this.ast.render(t,e),this.usesTopLevelAwait=this.astContext.usesTopLevelAwait,t}setSource({ast:e,code:t,customTransformCache:s,originalCode:i,originalSourcemap:n,resolvedIds:r,sourcemapChain:a,transformDependencies:o,transformFiles:h,...l}){this.info.code=t,this.originalCode=i,this.originalSourcemap=n,this.sourcemapChain=a,h&&(this.transformFiles=h),this.transformDependencies=o,this.customTransformCache=s,this.updateOptions(l),hn("generate ast",3),e||(e=this.tryParse()),ln("generate ast",3),this.resolvedIds=r||Object.create(null);const c=this.id;this.magicString=new x(t,{filename:this.excludeFromSourcemap?null:c,indentExclusionRanges:[]}),hn("analyse ast",3),this.astContext={addDynamicImport:this.addDynamicImport.bind(this),addExport:this.addExport.bind(this),addImport:this.addImport.bind(this),addImportMeta:this.addImportMeta.bind(this),code:t,deoptimizationTracker:this.graph.deoptimizationTracker,error:this.error.bind(this),fileName:c,getExports:this.getExports.bind(this),getModuleExecIndex:()=>this.execIndex,getModuleName:this.basename.bind(this),getNodeConstructor:e=>Hi[e]||Hi.UnknownNode,getReexports:this.getReexports.bind(this),importDescriptions:this.importDescriptions,includeAllExports:()=>this.includeAllExports(!0),includeDynamicImport:this.includeDynamicImport.bind(this),includeVariableInModule:this.includeVariableInModule.bind(this),magicString:this.magicString,module:this,moduleContext:this.context,options:this.options,requestTreeshakingPass:()=>this.graph.needsTreeshakingPass=!0,traceExport:e=>this.getVariableForExportName(e)[0],traceVariable:this.traceVariable.bind(this),usesTopLevelAwait:!1,warn:this.warn.bind(this)},this.scope=new Wi(this.graph.scope,this.astContext),this.namespace=new Xi(this.astContext),this.ast=new Oi(e,{context:this.astContext,type:"Module"},this.scope),this.info.ast=e,ln("analyse ast",3)}toJSON(){return{ast:this.ast.esTreeNode,code:this.info.code,customTransformCache:this.customTransformCache,dependencies:Array.from(this.dependencies,Zi),id:this.id,meta:this.info.meta,moduleSideEffects:this.info.moduleSideEffects,originalCode:this.originalCode,originalSourcemap:this.originalSourcemap,resolvedIds:this.resolvedIds,sourcemapChain:this.sourcemapChain,syntheticNamedExports:this.info.syntheticNamedExports,transformDependencies:this.transformDependencies,transformFiles:this.transformFiles}}traceVariable(e,{importerForSideEffects:t,isExportAllSearch:s,searchedNamesAndModules:i}=Q){const n=this.scope.variables.get(e);if(n)return n;const r=this.importDescriptions.get(e);if(r){const e=r.module;if(e instanceof mn&&"*"===r.name)return e.namespace;const[n]=fn(e,r.name,t||this,s,i);return n||this.error(pe(r.name,this.id,e.id),r.start)}return null}tryParse(){try{return this.graph.contextParse(this.info.code)}catch(e){let t=e.message.replace(/ \(\d+:\d+\)$/,"");return this.id.endsWith(".json")?t+=" (Note that you need @rollup/plugin-json to import JSON files)":this.id.endsWith(".js")||(t+=" (Note that you need plugins to import files that are not JavaScript)"),this.error({code:"PARSE_ERROR",message:t,parserError:e},e.pos)}}updateOptions({meta:e,moduleSideEffects:t,syntheticNamedExports:s}){null!=t&&(this.info.moduleSideEffects=t),null!=s&&(this.info.syntheticNamedExports=s),null!=e&&Object.assign(this.info.meta,e)}warn(e,t){this.addLocationToLogProps(e,t),this.options.onwarn(e)}addDynamicImport(e){let t=e.source;t instanceof Bi?1===t.quasis.length&&t.quasis[0].value.cooked&&(t=t.quasis[0].value.cooked):t instanceof Rs&&"string"==typeof t.value&&(t=t.value),this.dynamicImports.push({argument:t,id:null,node:e,resolution:null})}addExport(e){if(e instanceof qs)this.exports.set("default",{identifier:e.variable.getAssignedVariableName(),localName:"default"});else if(e instanceof Gs){const t=e.source.value;if(this.sources.add(t),e.exported){const s=e.exported.name;this.reexportDescriptions.set(s,{localName:"*",module:null,source:t,start:e.start})}else this.exportAllSources.add(t)}else if(e.source instanceof Rs){const t=e.source.value;this.sources.add(t);for(const s of e.specifiers){const e=s.exported.name;this.reexportDescriptions.set(e,{localName:s.local.name,module:null,source:t,start:s.start})}}else if(e.declaration){const t=e.declaration;if(t instanceof Gi)for(const e of t.declarations)for(const t of Ce(e.id))this.exports.set(t,{identifier:null,localName:t});else{const e=t.id.name;this.exports.set(e,{identifier:null,localName:e})}}else for(const t of e.specifiers){const e=t.local.name,s=t.exported.name;this.exports.set(s,{identifier:null,localName:e})}}addImport(e){const t=e.source.value;this.sources.add(t);for(const s of e.specifiers){const e="ImportDefaultSpecifier"===s.type,i="ImportNamespaceSpecifier"===s.type,n=e?"default":i?"*":s.imported.name;this.importDescriptions.set(s.local.name,{module:null,name:n,source:t,start:s.start})}}addImportMeta(e){this.importMetas.push(e)}addLocationToLogProps(e,t){e.id=this.id,e.pos=t;let s=this.info.code;const i=J(s,t,{offsetLine:1});if(i){let{column:n,line:r}=i;try{({column:n,line:r}=function(e,t){const s=e.filter((e=>!!e.mappings));e:for(;s.length>0;){const e=s.pop().mappings[t.line-1];if(e){const s=e.filter((e=>e.length>1)),i=s[s.length-1];for(const e of s)if(e[0]>=t.column||e===i){t={column:e[3],line:e[2]+1};continue e}}throw new Error("Can't resolve original location of error.")}return t}(this.sourcemapChain,{column:n,line:r})),s=this.originalCode}catch(e){this.options.onwarn({code:"SOURCEMAP_ERROR",id:this.id,loc:{column:n,file:this.id,line:r},message:`Error when using sourcemap for reporting an error: ${e.message}`,pos:t})}he(e,{column:n,line:r},s,this.id)}}addModulesToImportDescriptions(e){for(const t of e.values()){const{id:e}=this.resolvedIds[t.source];t.module=this.graph.modulesById.get(e)}}addRelevantSideEffectDependencies(e,t,s){const i=new Set,n=r=>{for(const a of r)i.has(a)||(i.add(a),t.has(a)?e.add(a):(a.info.moduleSideEffects||s.has(a))&&(a instanceof ke||a.hasEffects()?e.add(a):n(a.dependencies)))};n(this.dependencies),n(s)}getVariableFromNamespaceReexports(e,t,s){let i=null;const n=new Map,r=new Set;for(const a of this.exportAllModules){if(a.info.syntheticNamedExports===e)continue;const[o,h]=fn(a,e,t,!0,yn(s));a instanceof ke||h?r.add(o):o instanceof Yi?i||(i=o):o&&n.set(o,a)}if(n.size>0){const t=[...n],s=t[0][0];return 1===t.length?[s]:(this.options.onwarn(function(e,t,s){return{code:le.NAMESPACE_CONFLICT,message:`Conflicting namespaces: "${ie(t)}" re-exports "${e}" from one of the modules ${te(s.map((e=>ie(e))))} (will be ignored)`,name:e,reexporter:t,sources:s}}(e,this.id,t.map((([,e])=>e.id)))),[null])}if(r.size>0){const t=[...r],s=t[0];return t.length>1&&this.options.onwarn(function(e,t,s,i){return{code:le.AMBIGUOUS_EXTERNAL_NAMESPACES,message:`Ambiguous external namespace resolution: "${ie(t)}" re-exports "${e}" from one of the external modules ${te(i.map((e=>ie(e))))}, guessing "${ie(s)}".`,name:e,reexporter:t,sources:i}}(e,this.id,s.module.id,t.map((e=>e.module.id)))),[s,!0]}return i?[i]:[null]}includeAndGetAdditionalMergedNamespaces(){const e=new Set,t=new Set;for(const s of[this,...this.exportAllModules])if(s instanceof ke){const[t]=s.getVariableForExportName("*");t.include(),this.imports.add(t),e.add(t)}else if(s.info.syntheticNamedExports){const e=s.getSyntheticNamespace();e.include(),this.imports.add(e),t.add(e)}return[...t,...e]}includeDynamicImport(e){const t=this.dynamicImports.find((t=>t.node===e)).resolution;t instanceof mn&&(t.includedDynamicImporters.push(this),t.includeAllExports(!0))}includeVariable(e){if(!e.included){e.include(),this.graph.needsTreeshakingPass=!0;const t=e.module;if(t instanceof mn&&(t.isExecuted||dn(t),t!==this)){const t=function(e,t){const s=D(t.sideEffectDependenciesByVariable,e,(()=>new Set));let i=e;const n=new Set([i]);for(;;){const e=i.module;if(i=i instanceof zi?i.getDirectOriginalVariable():i instanceof Yi?i.syntheticNamespace:null,!i||n.has(i))break;n.add(i),s.add(e);const t=e.sideEffectDependenciesByVariable.get(i);if(t)for(const e of t)s.add(e)}return s}(e,this);for(const e of t)e.isExecuted||dn(e)}}}includeVariableInModule(e){this.includeVariable(e);const t=e.module;t&&t!==this&&this.imports.add(e)}shimMissingExport(e){this.options.onwarn({code:"SHIMMED_EXPORT",exporter:ie(this.id),exportName:e,message:`Missing export "${e}" has been shimmed in module ${ie(this.id)}.`}),this.exports.set(e,pn)}}function gn(e,t,s){if(e.module instanceof mn&&e.module!==s){const i=e.module.cycles;if(i.size>0){const n=s.cycles;for(const r of n)if(i.has(r)){t.alternativeReexportModules.set(e,s);break}}}}const yn=e=>e&&new Map(Array.from(e,(([e,t])=>[e,new Set(t)])));function En(e){return e.endsWith(".js")?e.slice(0,-3):e}function xn(e,t){return e.autoId?`${e.basePath?e.basePath+"/":""}${En(t)}`:e.id||""}function bn(e,t,s,i,n,r,a,o="return "){const{_:h,cnst:l,getDirectReturnFunction:c,getFunctionIntro:u,getPropertyAccess:d,n:p,s:f}=n;if(!s)return`${p}${p}${o}${function(e,t,s,i,n){if(e.length>0)return e[0].local;for(const{defaultVariableName:e,id:r,isChunk:a,name:o,namedExportsMode:h,namespaceVariableName:l,reexports:c}of t)if(c)return vn(o,c[0].imported,h,a,e,l,s,r,i,n)}(e,t,i,a,d)};`;let m="";for(const{defaultVariableName:e,id:n,isChunk:o,name:l,namedExportsMode:u,namespaceVariableName:f,reexports:g}of t)if(g&&s)for(const t of g)if("*"!==t.reexported){const s=vn(l,t.imported,u,o,e,f,i,n,a,d);if(m&&(m+=p),"*"!==t.imported&&t.needsLiveBinding){const[e,i]=c([],{functionReturn:!0,lineBreakIndent:null,name:null});m+=`Object.defineProperty(exports,${h}'${t.reexported}',${h}{${p}${r}enumerable:${h}true,${p}${r}get:${h}${e}${s}${i}${p}});`}else m+=`exports${d(t.reexported)}${h}=${h}${s};`}for(const{exported:t,local:s}of e){const e=`exports${d(t)}`,i=s;e!==i&&(m&&(m+=p),m+=`${e}${h}=${h}${i};`)}for(const{name:e,reexports:i}of t)if(i&&s)for(const t of i)if("*"===t.reexported){m&&(m+=p);const s=`{${p}${r}if${h}(k${h}!==${h}'default'${h}&&${h}!exports.hasOwnProperty(k))${h}${Pn(e,t.needsLiveBinding,r,n)}${f}${p}}`;m+="var"===l&&t.needsLiveBinding?`Object.keys(${e}).forEach(${u(["k"],{isAsync:!1,name:null})}${s});`:`for${h}(${l} k in ${e})${h}${s}`}return m?`${p}${p}${m}`:""}function vn(e,t,s,i,n,r,a,o,h,l){if("default"===t){if(!i){const t=String(a(o)),s=ri[t]?n:e;return ai(t,h)?`${s}${l("default")}`:s}return s?`${e}${l("default")}`:e}return"*"===t?(i?!s:oi[String(a(o))])?r:e:`${e}${l(t)}`}function An(e){return e([["value","true"]],{lineBreakIndent:null})}function Sn(e,t,s,{_:i,getObject:n}){if(e){if(t)return s?`Object.defineProperties(exports,${i}${n([["__esModule",An(n)],[null,`[Symbol.toStringTag]:${i}${Si(n)}`]],{lineBreakIndent:null})});`:`Object.defineProperty(exports,${i}'__esModule',${i}${An(n)});`;if(s)return`Object.defineProperty(exports,${i}Symbol.toStringTag,${i}${Si(n)});`}return""}const Pn=(e,t,s,{_:i,getDirectReturnFunction:n,n:r})=>{if(t){const[t,a]=n([],{functionReturn:!0,lineBreakIndent:null,name:null});return`Object.defineProperty(exports,${i}k,${i}{${r}${s}${s}enumerable:${i}true,${r}${s}${s}get:${i}${t}${e}[k]${a}${r}${s}})`}return`exports[k]${i}=${i}${e}[k]`};function kn(e,t,s,i,n,r,a,o){const{_:h,cnst:l,n:c}=o,u=new Set,d=[],p=(e,t,s)=>{u.add(t),d.push(`${l} ${e}${h}=${h}/*#__PURE__*/${t}(${s});`)};for(const{defaultVariableName:s,imports:i,id:n,isChunk:r,name:a,namedExportsMode:o,namespaceVariableName:h,reexports:l}of e)if(r){for(const{imported:e,reexported:t}of[...i||[],...l||[]])if("*"===e&&"*"!==t){o||p(h,ii,a);break}}else{const e=String(t(n));let r=!1,o=!1;for(const{imported:t,reexported:n}of[...i||[],...l||[]]){let i,l;"default"===t?r||(r=!0,s!==h&&(l=s,i=ri[e])):"*"===t&&"*"!==n&&(o||(o=!0,i=oi[e],l=h)),i&&p(l,i,a)}}return`${li(u,r,a,o,s,i,n)}${d.length>0?`${d.join(c)}${c}${c}`:""}`}function wn(e){return"."===e[0]?En(e):e}const Cn={assert:!0,buffer:!0,console:!0,constants:!0,domain:!0,events:!0,http:!0,https:!0,os:!0,path:!0,process:!0,punycode:!0,querystring:!0,stream:!0,string_decoder:!0,timers:!0,tty:!0,url:!0,util:!0,vm:!0,zlib:!0};function In(e,t){const s=t.map((({id:e})=>e)).filter((e=>e in Cn));s.length&&e({code:"MISSING_NODE_BUILTINS",message:`Creating a browser bundle that depends on Node.js built-in modules (${te(s)}). You might need to include https://github.com/snowpackjs/rollup-plugin-polyfill-node`,modules:s})}const Nn=(e,t)=>e.split(".").map(t).join("");function _n(e,t,s,i,{_:n,getPropertyAccess:r}){const a=e.split(".");a[0]=("function"==typeof s?s(a[0]):s[a[0]])||a[0];const o=a.pop();let h=t,l=a.map((e=>(h+=r(e),`${h}${n}=${n}${h}${n}||${n}{}`))).concat(`${h}${r(o)}`).join(`,${n}`)+`${n}=${n}${i}`;return a.length>0&&(l=`(${l})`),l}function $n(e){let t=e.length;for(;t--;){const{imports:s,reexports:i}=e[t];if(s||i)return e.slice(0,t+1)}return[]}const Tn=({dependencies:e,exports:t})=>{const s=new Set(t.map((e=>e.exported)));s.add("default");for(const{reexports:t}of e)if(t)for(const e of t)"*"!==e.reexported&&s.add(e.reexported);return s},Rn=(e,t,{_:s,cnst:i,getObject:n,n:r})=>e?`${r}${t}${i} _starExcludes${s}=${s}${n([...e].map((e=>[e,"1"])),{lineBreakIndent:{base:t,t:t}})};`:"",Mn=(e,t,{_:s,n:i})=>e.length?`${i}${t}var ${e.join(`,${s}`)};`:"",Dn=(e,t,s)=>Ln(e.filter((e=>e.hoisted)).map((e=>({name:e.exported,value:e.local}))),t,s);function Ln(e,t,{_:s,n:i}){return 0===e.length?"":1===e.length?`exports('${e[0].name}',${s}${e[0].value});${i}${i}`:`exports({${i}`+e.map((({name:e,value:i})=>`${t}${e}:${s}${i}`)).join(`,${i}`)+`${i}});${i}${i}`}const On=(e,t,s)=>Ln(e.filter((e=>e.expression)).map((e=>({name:e.exported,value:e.local}))),t,s),Vn=(e,t,s)=>Ln(e.filter((e=>e.local===qi)).map((e=>({name:e.exported,value:qi}))),t,s);function Bn(e,t,s){return e?`${t}${Nn(e,s)}`:"null"}var Fn={amd:function(e,{accessedGlobals:t,dependencies:s,exports:i,hasExports:n,id:r,indent:a,intro:o,isEntryFacade:h,isModuleFacade:l,namedExportsMode:c,outro:u,snippets:d,warn:p},{amd:f,esModule:m,externalLiveBindings:g,freeze:y,interop:E,namespaceToStringTag:x,strict:b}){In(p,s);const v=s.map((e=>`'${wn(e.id)}'`)),A=s.map((e=>e.name)),{n:S,getNonArrowFunctionIntro:P,_:k}=d;c&&n&&(A.unshift("exports"),v.unshift("'exports'")),t.has("require")&&(A.unshift("require"),v.unshift("'require'")),t.has("module")&&(A.unshift("module"),v.unshift("'module'"));const w=xn(f,r),C=(w?`'${w}',${k}`:"")+(v.length?`[${v.join(`,${k}`)}],${k}`:""),I=b?`${k}'use strict';`:"";e.prepend(`${o}${kn(s,E,g,y,x,t,a,d)}`);const N=bn(i,s,c,E,d,a,g);let _=Sn(c&&n,h&&m,l&&x,d);return _&&(_=S+S+_),e.append(`${N}${_}${u}`),e.indent(a).prepend(`${f.define}(${C}(${P(A,{isAsync:!1,name:null})}{${I}${S}${S}`).append(`${S}${S}}));`)},cjs:function(e,{accessedGlobals:t,dependencies:s,exports:i,hasExports:n,indent:r,intro:a,isEntryFacade:o,isModuleFacade:h,namedExportsMode:l,outro:c,snippets:u},{compact:d,esModule:p,externalLiveBindings:f,freeze:m,interop:g,namespaceToStringTag:y,strict:E}){const{_:x,n:b}=u,v=E?`'use strict';${b}${b}`:"";let A=Sn(l&&n,o&&p,h&&y,u);A&&(A+=b+b);const S=function(e,{_:t,cnst:s,n:i},n){let r="",a=!1;for(const{id:o,name:h,reexports:l,imports:c}of e)l||c?(r+=n&&a?",":`${r?`;${i}`:""}${s} `,a=!0,r+=`${h}${t}=${t}require('${o}')`):(r&&(r+=n&&!a?",":`;${i}`),a=!1,r+=`require('${o}')`);return r?`${r};${i}${i}`:""}(s,u,d),P=kn(s,g,f,m,y,t,r,u);e.prepend(`${v}${a}${A}${S}${P}`);const k=bn(i,s,l,g,u,r,f,`module.exports${x}=${x}`);return e.append(`${k}${c}`)},es:function(e,{accessedGlobals:t,indent:s,intro:i,outro:n,dependencies:r,exports:a,snippets:o},{externalLiveBindings:h,freeze:l,namespaceToStringTag:c}){const{_:u,n:d}=o,p=function(e,t){const s=[];for(const{id:i,reexports:n,imports:r,name:a}of e)if(n||r){if(r){let e=null,n=null;const a=[];for(const t of r)"default"===t.imported?e=t:"*"===t.imported?n=t:a.push(t);n&&s.push(`import${t}*${t}as ${n.local} from${t}'${i}';`),e&&0===a.length?s.push(`import ${e.local} from${t}'${i}';`):a.length>0&&s.push(`import ${e?`${e.local},${t}`:""}{${t}${a.map((e=>e.imported===e.local?e.imported:`${e.imported} as ${e.local}`)).join(`,${t}`)}${t}}${t}from${t}'${i}';`)}if(n){let e=null;const o=[],h=[];for(const t of n)"*"===t.reexported?e=t:"*"===t.imported?o.push(t):h.push(t);if(e&&s.push(`export${t}*${t}from${t}'${i}';`),o.length>0){r&&r.some((e=>"*"===e.imported&&e.local===a))||s.push(`import${t}*${t}as ${a} from${t}'${i}';`);for(const e of o)s.push(`export${t}{${t}${a===e.reexported?a:`${a} as ${e.reexported}`} };`)}h.length>0&&s.push(`export${t}{${t}${h.map((e=>e.imported===e.reexported?e.imported:`${e.imported} as ${e.reexported}`)).join(`,${t}`)}${t}}${t}from${t}'${i}';`)}}else s.push(`import${t}'${i}';`);return s}(r,u);p.length>0&&(i+=p.join(d)+d+d),(i+=li(null,t,s,o,h,l,c))&&e.prepend(i);const f=function(e,{_:t,cnst:s}){const i=[],n=[];for(const r of e)r.expression&&i.push(`${s} ${r.local}${t}=${t}${r.expression};`),n.push(r.exported===r.local?r.local:`${r.local} as ${r.exported}`);return n.length&&i.push(`export${t}{${t}${n.join(`,${t}`)}${t}};`),i}(a,o);return f.length&&e.append(d+d+f.join(d).trim()),n&&e.append(n),e.trim()},iife:function(e,{accessedGlobals:t,dependencies:s,exports:i,hasExports:n,indent:r,intro:a,namedExportsMode:o,outro:h,snippets:l,warn:c},{compact:u,esModule:d,extend:p,freeze:f,externalLiveBindings:m,globals:g,interop:y,name:E,namespaceToStringTag:x,strict:b}){const{_:v,cnst:A,getNonArrowFunctionIntro:S,getPropertyAccess:P,n:k}=l,w=E&&E.includes("."),C=!p&&!w;if(E&&C&&(Se(I=E)||ve.has(I)||Ae.test(I)))return oe({code:"ILLEGAL_IDENTIFIER_AS_NAME",message:`Given name "${E}" is not a legal JS identifier. If you need this, you can try "output.extend: true".`});var I;In(c,s);const N=$n(s),_=N.map((e=>e.globalName||"null")),$=N.map((e=>e.name));n&&!E&&c({code:"MISSING_NAME_OPTION_FOR_IIFE_EXPORT",message:'If you do not supply "output.name", you may not be able to access the exports of an IIFE bundle.'}),o&&n&&(p?(_.unshift(`this${Nn(E,P)}${v}=${v}this${Nn(E,P)}${v}||${v}{}`),$.unshift("exports")):(_.unshift("{}"),$.unshift("exports")));const T=b?`${r}'use strict';${k}`:"",R=kn(s,y,m,f,x,t,r,l);e.prepend(`${a}${R}`);let M=`(${S($,{isAsync:!1,name:null})}{${k}${T}${k}`;n&&(!E||p&&o||(M=(C?`${A} ${E}`:`this${Nn(E,P)}`)+`${v}=${v}${M}`),w&&(M=function(e,t,s,{_:i,getPropertyAccess:n,s:r},a){const o=e.split(".");o[0]=("function"==typeof s?s(o[0]):s[o[0]])||o[0],o.pop();let h=t;return o.map((e=>(h+=n(e),`${h}${i}=${i}${h}${i}||${i}{}${r}`))).join(a?",":"\n")+(a&&o.length?";":"\n")}(E,"this",g,l,u)+M));let D=`${k}${k}})(${_.join(`,${v}`)});`;n&&!p&&o&&(D=`${k}${k}${r}return exports;${D}`);const L=bn(i,s,o,y,l,r,m);let O=Sn(o&&n,d,x,l);return O&&(O=k+k+O),e.append(`${L}${O}${h}`),e.indent(r).prepend(M).append(D)},system:function(e,{accessedGlobals:t,dependencies:s,exports:i,hasExports:n,indent:r,intro:a,snippets:o,outro:h,usesTopLevelAwait:l},{externalLiveBindings:c,freeze:u,name:d,namespaceToStringTag:p,strict:f,systemNullSetters:m}){const{_:g,getFunctionIntro:y,getNonArrowFunctionIntro:E,n:x,s:b}=o,{importBindings:v,setters:A,starExcludes:S}=function(e,t,s,{_:i,cnst:n,getObject:r,getPropertyAccess:a,n:o}){const h=[],l=[];let c=null;for(const{imports:u,reexports:d}of e){const p=[];if(u)for(const e of u)h.push(e.local),"*"===e.imported?p.push(`${e.local}${i}=${i}module;`):p.push(`${e.local}${i}=${i}module${a(e.imported)};`);if(d){const o=[];let h=!1;for(const{imported:e,reexported:t}of d)"*"===t?h=!0:o.push([t,"*"===e?"module":`module${a(e)}`]);if(o.length>1||h){const a=r(o,{lineBreakIndent:null});h?(c||(c=Tn({dependencies:e,exports:t})),p.push(`${n} setter${i}=${i}${a};`,`for${i}(${n} name in module)${i}{`,`${s}if${i}(!_starExcludes[name])${i}setter[name]${i}=${i}module[name];`,"}","exports(setter);")):p.push(`exports(${a});`)}else{const[e,t]=o[0];p.push(`exports('${e}',${i}${t});`)}}l.push(p.join(`${o}${s}${s}${s}`))}return{importBindings:h,setters:l,starExcludes:c}}(s,i,r,o),P=d?`'${d}',${g}`:"",k=t.has("module")?["exports","module"]:n?["exports"]:[];let w=`System.register(${P}[`+s.map((({id:e})=>`'${e}'`)).join(`,${g}`)+`],${g}(${E(k,{isAsync:!1,name:null})}{${x}${r}${f?"'use strict';":""}`+Rn(S,r,o)+Mn(v,r,o)+`${x}${r}return${g}{${A.length?`${x}${r}${r}setters:${g}[${A.map((e=>e?`${y(["module"],{isAsync:!1,name:null})}{${x}${r}${r}${r}${e}${x}${r}${r}}`:m?"null":`${y([],{isAsync:!1,name:null})}{}`)).join(`,${g}`)}],`:""}${x}`;w+=`${r}${r}execute:${g}(${E([],{isAsync:l,name:null})}{${x}${x}`;const C=`${r}${r}})${x}${r}}${b}${x}}));`;return e.prepend(a+li(null,t,r,o,c,u,p)+Dn(i,r,o)),e.append(`${h}${x}${x}`+On(i,r,o)+Vn(i,r,o)),e.indent(`${r}${r}${r}`).append(C).prepend(w)},umd:function(e,{accessedGlobals:t,dependencies:s,exports:i,hasExports:n,id:r,indent:a,intro:o,namedExportsMode:h,outro:l,snippets:c,warn:u},{amd:d,compact:p,esModule:f,extend:m,externalLiveBindings:g,freeze:y,interop:E,name:x,namespaceToStringTag:b,globals:v,noConflict:A,strict:S}){const{_:P,cnst:k,getFunctionIntro:w,getNonArrowFunctionIntro:C,getPropertyAccess:I,n:N,s:_}=c,$=p?"f":"factory",T=p?"g":"global";if(n&&!x)return oe({code:"MISSING_NAME_OPTION_FOR_IIFE_EXPORT",message:'You must supply "output.name" for UMD bundles that have exports so that the exports are accessible in environments without a module loader.'});In(u,s);const R=s.map((e=>`'${wn(e.id)}'`)),M=s.map((e=>`require('${e.id}')`)),D=$n(s),L=D.map((e=>Bn(e.globalName,T,I))),O=D.map((e=>e.name));h&&(n||A)&&(R.unshift("'exports'"),M.unshift("exports"),L.unshift(_n(x,T,v,(m?`${Bn(x,T,I)}${P}||${P}`:"")+"{}",c)),O.unshift("exports"));const V=xn(d,r),B=(V?`'${V}',${P}`:"")+(R.length?`[${R.join(`,${P}`)}],${P}`:""),F=d.define,z=!h&&n?`module.exports${P}=${P}`:"",W=S?`${P}'use strict';${N}`:"";let j;if(A){const e=p?"e":"exports";let t;t=!h&&n?`${k} ${e}${P}=${P}${_n(x,T,v,`${$}(${L.join(`,${P}`)})`,c)};`:`${k} ${e}${P}=${P}${L.shift()};${N}${a}${a}${$}(${[e].concat(L).join(`,${P}`)});`,j=`(${w([],{isAsync:!1,name:null})}{${N}${a}${a}${k} current${P}=${P}${function(e,t,{_:s,getPropertyAccess:i}){let n=t;return e.split(".").map((e=>n+=i(e))).join(`${s}&&${s}`)}(x,T,c)};${N}${a}${a}${t}${N}${a}${a}${e}.noConflict${P}=${P}${w([],{isAsync:!1,name:null})}{${P}${Bn(x,T,I)}${P}=${P}current;${P}return ${e}${_}${P}};${N}${a}})()`}else j=`${$}(${L.join(`,${P}`)})`,!h&&n&&(j=_n(x,T,v,j,c));const U=n||A&&h||L.length>0,G=[$];U&&G.unshift(T);const H=U?`this,${P}`:"",q=U?`(${T}${P}=${P}typeof globalThis${P}!==${P}'undefined'${P}?${P}globalThis${P}:${P}${T}${P}||${P}self,${P}`:"",K=U?")":"",X=U?`${a}typeof exports${P}===${P}'object'${P}&&${P}typeof module${P}!==${P}'undefined'${P}?${P}${z}${$}(${M.join(`,${P}`)})${P}:${N}`:"",Y=`(${C(G,{isAsync:!1,name:null})}{${N}`+X+`${a}typeof ${F}${P}===${P}'function'${P}&&${P}${F}.amd${P}?${P}${F}(${B}${$})${P}:${N}`+`${a}${q}${j}${K};${N}`+`})(${H}(${C(O,{isAsync:!1,name:null})}{${W}${N}`,Q=N+N+"}));";e.prepend(`${o}${kn(s,E,g,y,b,t,a,c)}`);const Z=bn(i,s,h,E,c,a,g);let J=Sn(h&&n,f,b,c);return J&&(J=N+N+J),e.append(`${Z}${J}${l}`),e.trim().indent(a).append(Q).prepend(Y)}};class zn{constructor(e,t){this.isOriginal=!0,this.filename=e,this.content=t}traceSegment(e,t,s){return{column:t,line:e,name:s,source:this}}}class Wn{constructor(e,t){this.sources=t,this.names=e.names,this.mappings=e.mappings}traceMappings(){const e=[],t=new Map,s=[],i=[],n=new Map,r=[];for(const a of this.mappings){const o=[];for(const r of a){if(1===r.length)continue;const a=this.sources[r[1]];if(!a)continue;const h=a.traceSegment(r[2],r[3],5===r.length?this.names[r[4]]:"");if(h){const{column:a,line:l,name:c,source:{content:u,filename:d}}=h;let p=t.get(d);if(void 0===p)p=e.length,e.push(d),t.set(d,p),s[p]=u;else if(null==s[p])s[p]=u;else if(null!=u&&s[p]!==u)return oe({message:`Multiple conflicting contents for sourcemap source ${d}`});const f=[r[0],p,l,a];if(c){let e=n.get(c);void 0===e&&(e=i.length,i.push(c),n.set(c,e)),f[4]=e}o.push(f)}}r.push(o)}return{mappings:r,names:i,sources:e,sourcesContent:s}}traceSegment(e,t,s){const i=this.mappings[e];if(!i)return null;let n=0,r=i.length-1;for(;n<=r;){const e=n+r>>1,a=i[e];if(a[0]===t||n===r){if(1==a.length)return null;const e=this.sources[a[1]];return e?e.traceSegment(a[2],a[3],5===a.length?this.names[a[4]]:s):null}a[0]>t?r=e-1:n=e+1}return null}}function jn(e){return function(t,s){return s.mappings?new Wn(s,[t]):(e({code:"SOURCEMAP_BROKEN",message:`Sourcemap is likely to be incorrect: a plugin (${s.plugin}) was used to transform files, but didn't generate a sourcemap for the transformation. Consult the plugin documentation for help`,plugin:s.plugin,url:"https://rollupjs.org/guide/en/#warning-sourcemap-is-likely-to-be-incorrect"}),new Wn({mappings:[],names:[]},[t]))}}function Un(e,t,s,i,n){let r;if(s){const t=s.sources,i=s.sourcesContent||[],n=$(e)||".",a=s.sourceRoot||".",o=t.map(((e,t)=>new zn(M(n,a,e),i[t])));r=new Wn(s,o)}else r=new zn(e,t);return i.reduce(n,r)}var Gn={},Hn=qn;function qn(e,t){if(!e)throw new Error(t||"Assertion failed")}qn.equal=function(e,t,s){if(e!=t)throw new Error(s||"Assertion failed: "+e+" != "+t)};var Kn={exports:{}};"function"==typeof Object.create?Kn.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Kn.exports=function(e,t){if(t){e.super_=t;var s=function(){};s.prototype=t.prototype,e.prototype=new s,e.prototype.constructor=e}};var Xn=Hn,Yn=Kn.exports;function Qn(e,t){return 55296==(64512&e.charCodeAt(t))&&!(t<0||t+1>=e.length)&&56320==(64512&e.charCodeAt(t+1))}function Zn(e){return(e>>>24|e>>>8&65280|e<<8&16711680|(255&e)<<24)>>>0}function Jn(e){return 1===e.length?"0"+e:e}function er(e){return 7===e.length?"0"+e:6===e.length?"00"+e:5===e.length?"000"+e:4===e.length?"0000"+e:3===e.length?"00000"+e:2===e.length?"000000"+e:1===e.length?"0000000"+e:e}Gn.inherits=Yn,Gn.toArray=function(e,t){if(Array.isArray(e))return e.slice();if(!e)return[];var s=[];if("string"==typeof e)if(t){if("hex"===t)for((e=e.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(e="0"+e),n=0;n<e.length;n+=2)s.push(parseInt(e[n]+e[n+1],16))}else for(var i=0,n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?s[i++]=r:r<2048?(s[i++]=r>>6|192,s[i++]=63&r|128):Qn(e,n)?(r=65536+((1023&r)<<10)+(1023&e.charCodeAt(++n)),s[i++]=r>>18|240,s[i++]=r>>12&63|128,s[i++]=r>>6&63|128,s[i++]=63&r|128):(s[i++]=r>>12|224,s[i++]=r>>6&63|128,s[i++]=63&r|128)}else for(n=0;n<e.length;n++)s[n]=0|e[n];return s},Gn.toHex=function(e){for(var t="",s=0;s<e.length;s++)t+=Jn(e[s].toString(16));return t},Gn.htonl=Zn,Gn.toHex32=function(e,t){for(var s="",i=0;i<e.length;i++){var n=e[i];"little"===t&&(n=Zn(n)),s+=er(n.toString(16))}return s},Gn.zero2=Jn,Gn.zero8=er,Gn.join32=function(e,t,s,i){var n=s-t;Xn(n%4==0);for(var r=new Array(n/4),a=0,o=t;a<r.length;a++,o+=4){var h;h="big"===i?e[o]<<24|e[o+1]<<16|e[o+2]<<8|e[o+3]:e[o+3]<<24|e[o+2]<<16|e[o+1]<<8|e[o],r[a]=h>>>0}return r},Gn.split32=function(e,t){for(var s=new Array(4*e.length),i=0,n=0;i<e.length;i++,n+=4){var r=e[i];"big"===t?(s[n]=r>>>24,s[n+1]=r>>>16&255,s[n+2]=r>>>8&255,s[n+3]=255&r):(s[n+3]=r>>>24,s[n+2]=r>>>16&255,s[n+1]=r>>>8&255,s[n]=255&r)}return s},Gn.rotr32=function(e,t){return e>>>t|e<<32-t},Gn.rotl32=function(e,t){return e<<t|e>>>32-t},Gn.sum32=function(e,t){return e+t>>>0},Gn.sum32_3=function(e,t,s){return e+t+s>>>0},Gn.sum32_4=function(e,t,s,i){return e+t+s+i>>>0},Gn.sum32_5=function(e,t,s,i,n){return e+t+s+i+n>>>0},Gn.sum64=function(e,t,s,i){var n=e[t],r=i+e[t+1]>>>0,a=(r<i?1:0)+s+n;e[t]=a>>>0,e[t+1]=r},Gn.sum64_hi=function(e,t,s,i){return(t+i>>>0<t?1:0)+e+s>>>0},Gn.sum64_lo=function(e,t,s,i){return t+i>>>0},Gn.sum64_4_hi=function(e,t,s,i,n,r,a,o){var h=0,l=t;return h+=(l=l+i>>>0)<t?1:0,h+=(l=l+r>>>0)<r?1:0,e+s+n+a+(h+=(l=l+o>>>0)<o?1:0)>>>0},Gn.sum64_4_lo=function(e,t,s,i,n,r,a,o){return t+i+r+o>>>0},Gn.sum64_5_hi=function(e,t,s,i,n,r,a,o,h,l){var c=0,u=t;return c+=(u=u+i>>>0)<t?1:0,c+=(u=u+r>>>0)<r?1:0,c+=(u=u+o>>>0)<o?1:0,e+s+n+a+h+(c+=(u=u+l>>>0)<l?1:0)>>>0},Gn.sum64_5_lo=function(e,t,s,i,n,r,a,o,h,l){return t+i+r+o+l>>>0},Gn.rotr64_hi=function(e,t,s){return(t<<32-s|e>>>s)>>>0},Gn.rotr64_lo=function(e,t,s){return(e<<32-s|t>>>s)>>>0},Gn.shr64_hi=function(e,t,s){return e>>>s},Gn.shr64_lo=function(e,t,s){return(e<<32-s|t>>>s)>>>0};var tr={},sr=Gn,ir=Hn;function nr(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}tr.BlockHash=nr,nr.prototype.update=function(e,t){if(e=sr.toArray(e,t),this.pending?this.pending=this.pending.concat(e):this.pending=e,this.pendingTotal+=e.length,this.pending.length>=this._delta8){var s=(e=this.pending).length%this._delta8;this.pending=e.slice(e.length-s,e.length),0===this.pending.length&&(this.pending=null),e=sr.join32(e,0,e.length-s,this.endian);for(var i=0;i<e.length;i+=this._delta32)this._update(e,i,i+this._delta32)}return this},nr.prototype.digest=function(e){return this.update(this._pad()),ir(null===this.pending),this._digest(e)},nr.prototype._pad=function(){var e=this.pendingTotal,t=this._delta8,s=t-(e+this.padLength)%t,i=new Array(s+this.padLength);i[0]=128;for(var n=1;n<s;n++)i[n]=0;if(e<<=3,"big"===this.endian){for(var r=8;r<this.padLength;r++)i[n++]=0;i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=e>>>24&255,i[n++]=e>>>16&255,i[n++]=e>>>8&255,i[n++]=255&e}else for(i[n++]=255&e,i[n++]=e>>>8&255,i[n++]=e>>>16&255,i[n++]=e>>>24&255,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,r=8;r<this.padLength;r++)i[n++]=0;return i};var rr={},ar=Gn.rotr32;function or(e,t,s){return e&t^~e&s}function hr(e,t,s){return e&t^e&s^t&s}function lr(e,t,s){return e^t^s}rr.ft_1=function(e,t,s,i){return 0===e?or(t,s,i):1===e||3===e?lr(t,s,i):2===e?hr(t,s,i):void 0},rr.ch32=or,rr.maj32=hr,rr.p32=lr,rr.s0_256=function(e){return ar(e,2)^ar(e,13)^ar(e,22)},rr.s1_256=function(e){return ar(e,6)^ar(e,11)^ar(e,25)},rr.g0_256=function(e){return ar(e,7)^ar(e,18)^e>>>3},rr.g1_256=function(e){return ar(e,17)^ar(e,19)^e>>>10};var cr=Gn,ur=tr,dr=rr,pr=Hn,fr=cr.sum32,mr=cr.sum32_4,gr=cr.sum32_5,yr=dr.ch32,Er=dr.maj32,xr=dr.s0_256,br=dr.s1_256,vr=dr.g0_256,Ar=dr.g1_256,Sr=ur.BlockHash,Pr=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function kr(){if(!(this instanceof kr))return new kr;Sr.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=Pr,this.W=new Array(64)}cr.inherits(kr,Sr);var wr=kr;kr.blockSize=512,kr.outSize=256,kr.hmacStrength=192,kr.padLength=64,kr.prototype._update=function(e,t){for(var s=this.W,i=0;i<16;i++)s[i]=e[t+i];for(;i<s.length;i++)s[i]=mr(Ar(s[i-2]),s[i-7],vr(s[i-15]),s[i-16]);var n=this.h[0],r=this.h[1],a=this.h[2],o=this.h[3],h=this.h[4],l=this.h[5],c=this.h[6],u=this.h[7];for(pr(this.k.length===s.length),i=0;i<s.length;i++){var d=gr(u,br(h),yr(h,l,c),this.k[i],s[i]),p=fr(xr(n),Er(n,r,a));u=c,c=l,l=h,h=fr(o,d),o=a,a=r,r=n,n=fr(d,p)}this.h[0]=fr(this.h[0],n),this.h[1]=fr(this.h[1],r),this.h[2]=fr(this.h[2],a),this.h[3]=fr(this.h[3],o),this.h[4]=fr(this.h[4],h),this.h[5]=fr(this.h[5],l),this.h[6]=fr(this.h[6],c),this.h[7]=fr(this.h[7],u)},kr.prototype._digest=function(e){return"hex"===e?cr.toHex32(this.h,"big"):cr.split32(this.h,"big")};var Cr=wr;const Ir=()=>Cr(),Nr={amd:Tr,cjs:Tr,es:$r,iife:Tr,system:$r,umd:Tr};function _r(e,t,s,i,n,r,a,o,h,l,c,u,d){const p=e.slice().reverse();for(const e of p)e.scope.addUsedOutsideNames(i,n,c,u);!function(e,t,s){for(const i of t){for(const t of i.scope.variables.values())t.included&&!(t.renderBaseName||t instanceof zi&&t.getOriginalVariable()!==t)&&t.setRenderNames(null,zt(t.name,e));if(s.has(i)){const t=i.namespace;t.setRenderNames(null,zt(t.name,e))}}}(i,p,d),Nr[n](i,s,t,r,a,o,h,l);for(const e of p)e.scope.deconflict(n,c,u)}function $r(e,t,s,i,n,r,a,o){for(const t of s.dependencies)(n||t instanceof ke)&&(t.variableName=zt(t.suggestedVariableName,e));for(const s of t){const t=s.module,i=s.name;s.isNamespace&&(n||t instanceof ke)?s.setRenderNames(null,(t instanceof ke?t:a.get(t)).variableName):t instanceof ke&&"default"===i?s.setRenderNames(null,zt([...t.exportedVariables].some((([e,t])=>"*"===t&&e.included))?t.suggestedVariableName+"__default":t.suggestedVariableName,e)):s.setRenderNames(null,zt(i,e))}for(const t of o)t.setRenderNames(null,zt(t.name,e))}function Tr(e,t,{deconflictedDefault:s,deconflictedNamespace:i,dependencies:n},r,a,o,h){for(const t of n)t.variableName=zt(t.suggestedVariableName,e);for(const t of i)t.namespaceVariableName=zt(`${t.suggestedVariableName}__namespace`,e);for(const t of s)i.has(t)&&hi(String(r(t.id)),o)?t.defaultVariableName=t.namespaceVariableName:t.defaultVariableName=zt(`${t.suggestedVariableName}__default`,e);for(const e of t){const t=e.module;if(t instanceof ke){const s=e.name;if("default"===s){const s=String(r(t.id)),i=ri[s]?t.defaultVariableName:t.variableName;ai(s,o)?e.setRenderNames(i,"default"):e.setRenderNames(null,i)}else"*"===s?e.setRenderNames(null,oi[String(r(t.id))]?t.namespaceVariableName:t.variableName):e.setRenderNames(t.variableName,null)}else{const s=h.get(t);a&&e.isNamespace?e.setRenderNames(null,"default"===s.exportMode?s.namespaceVariableName:s.variableName):"default"===s.exportMode?e.setRenderNames(null,s.variableName):e.setRenderNames(s.variableName,s.getVariableExportName(e))}}}const Rr=/[\\'\r\n\u2028\u2029]/,Mr=/(['\r\n\u2028\u2029])/g,Dr=/\\/g;function Lr(e){return e.match(Rr)?e.replace(Dr,"\\\\").replace(Mr,"\\$1"):e}function Or(e,{exports:t,name:s,format:i},n,r,a){const o=e.getExportNames();if("default"===t){if(1!==o.length||"default"!==o[0])return oe(ue("default",o,r))}else if("none"===t&&o.length)return oe(ue("none",o,r));return"auto"===t&&(0===o.length?t="none":1===o.length&&"default"===o[0]?("cjs"===i&&n.has("exports")&&a(function(e){const t=ie(e);return{code:le.PREFER_NAMED_EXPORTS,id:e,message:`Entry module "${t}" is implicitly using "default" export mode, which means for CommonJS output that its default export is assigned to "module.exports". For many tools, such CommonJS output will not be interchangeable with the original ES module. If this is intended, explicitly set "output.exports" to either "auto" or "default", otherwise you might want to consider changing the signature of "${t}" to use named exports only.`,url:"https://rollupjs.org/guide/en/#outputexports"}}(r)),t="default"):("es"!==i&&"system"!==i&&o.includes("default")&&a(function(e,t){return{code:le.MIXED_EXPORTS,id:e,message:`Entry module "${ie(e)}" is using named and default exports together. Consumers of your bundle will have to use \`${t||"chunk"}["default"]\` to access the default export, which may not be what you want. Use \`output.exports: "named"\` to disable this warning`,url:"https://rollupjs.org/guide/en/#outputexports"}}(r,s)),t="named")),t}function Vr(e){const t=e.split("\n"),s=t.filter((e=>/^\t+/.test(e))),i=t.filter((e=>/^ {2,}/.test(e)));if(0===s.length&&0===i.length)return null;if(s.length>=i.length)return"\t";const n=i.reduce(((e,t)=>{const s=/^ +/.exec(t)[0].length;return Math.min(s,e)}),1/0);return new Array(n+1).join(" ")}function Br(e,t,s,i,n){const r=e.getDependenciesToBeIncluded();for(const e of r){if(e instanceof ke){t.push(e);continue}const r=n.get(e);r===i?s.has(e)||(s.add(e),Br(e,t,s,i,n)):t.push(r)}}function Fr(e){if(!e)return null;if("string"==typeof e&&(e=JSON.parse(e)),""===e.mappings)return{mappings:[],names:[],sources:[],version:3};const t="string"==typeof e.mappings?function(e){for(var t=[],i=[],n=[0,0,0,0,0],a=0,o=0,h=0,l=0;o<e.length;o++){var c=e.charCodeAt(o);if(44===c)r(i,n,a),a=0;else if(59===c)r(i,n,a),a=0,t.push(i),i=[],n[0]=0;else{var u=s[c];if(void 0===u)throw new Error("Invalid character ("+String.fromCharCode(c)+")");var d=32&u;if(l+=(u&=31)<<h,d)h+=5;else{var p=1&l;l>>>=1,p&&(l=0===l?-2147483648:-l),n[a]+=l,a++,l=h=0}}}return r(i,n,a),t.push(i),t}(e.mappings):e.mappings;return{...e,mappings:t}}function zr(e,t,s){return ne(e)?oe(ye(`Invalid pattern "${e}" for "${t}", patterns can be neither absolute nor relative paths.`)):e.replace(/\[(\w+)\]/g,((e,i)=>{if(!s.hasOwnProperty(i))return oe(ye(`"[${i}]" is not a valid placeholder in "${t}" pattern.`));const n=s[i]();return ne(n)?oe(ye(`Invalid substitution "${n}" for placeholder "[${i}]" in "${t}" pattern, can be neither absolute nor relative path.`)):n}))}function Wr(e,t){const s=new Set(Object.keys(t).map((e=>e.toLowerCase())));if(!s.has(e.toLocaleLowerCase()))return e;const i=T(e);e=e.substring(0,e.length-i.length);let n,r=1;for(;s.has((n=e+ ++r+i).toLowerCase()););return n}const jr=[".js",".jsx",".ts",".tsx"];function Ur(e,t,s,i){const n="function"==typeof t?t(e.id):t[e.id];return n||(s?(i({code:"MISSING_GLOBAL_NAME",guess:e.variableName,message:`No name was provided for external module '${e.id}' in output.globals – guessing '${e.variableName}'`,source:e.id}),e.variableName):void 0)}class Gr{constructor(e,t,s,i,n,r,a,o,h,l){this.orderedModules=e,this.inputOptions=t,this.outputOptions=s,this.unsetOptions=i,this.pluginDriver=n,this.modulesById=r,this.chunkByModule=a,this.facadeChunkByModule=o,this.includedNamespaces=h,this.manualChunkAlias=l,this.entryModules=[],this.exportMode="named",this.facadeModule=null,this.id=null,this.namespaceVariableName="",this.needsExportsShim=!1,this.variableName="",this.accessedGlobalsByScope=new Map,this.dependencies=new Set,this.dynamicDependencies=new Set,this.dynamicEntryModules=[],this.dynamicName=null,this.exportNamesByVariable=new Map,this.exports=new Set,this.exportsByName=new Map,this.fileName=null,this.implicitEntryModules=[],this.implicitlyLoadedBefore=new Set,this.imports=new Set,this.indentString=void 0,this.isEmpty=!0,this.name=null,this.renderedDependencies=null,this.renderedExports=null,this.renderedHash=void 0,this.renderedModuleSources=new Map,this.renderedModules=Object.create(null),this.renderedSource=null,this.sortedExportNames=null,this.strictFacade=!1,this.usedModules=void 0,this.execIndex=e.length>0?e[0].execIndex:1/0;const c=new Set(e);for(const t of e){t.namespace.included&&h.add(t),this.isEmpty&&t.isIncluded()&&(this.isEmpty=!1),(t.info.isEntry||s.preserveModules)&&this.entryModules.push(t);for(const e of t.includedDynamicImporters)c.has(e)||(this.dynamicEntryModules.push(t),t.info.syntheticNamedExports&&!s.preserveModules&&(h.add(t),this.exports.add(t.namespace)));t.implicitlyLoadedAfter.size>0&&this.implicitEntryModules.push(t)}this.suggestedVariableName=Pe(this.generateVariableName())}static generateFacade(e,t,s,i,n,r,a,o,h,l){const c=new Gr([],e,t,s,i,n,r,a,o,null);c.assignFacadeName(l,h),a.has(h)||a.set(h,c);for(const e of h.getDependenciesToBeIncluded())c.dependencies.add(e instanceof mn?r.get(e):e);return!c.dependencies.has(r.get(h))&&h.info.moduleSideEffects&&h.hasEffects()&&c.dependencies.add(r.get(h)),c.ensureReexportsAreAvailableForModule(h),c.facadeModule=h,c.strictFacade=!0,c}canModuleBeFacade(e,t){const s=e.getExportNamesByVariable();for(const t of this.exports)if(!s.has(t))return 0===s.size&&e.isUserDefinedEntryPoint&&"strict"===e.preserveSignature&&this.unsetOptions.has("preserveEntrySignatures")&&this.inputOptions.onwarn({code:"EMPTY_FACADE",id:e.id,message:`To preserve the export signature of the entry module "${ie(e.id)}", an empty facade chunk was created. This often happens when creating a bundle for a web app where chunks are placed in script tags and exports are ignored. In this case it is recommended to set "preserveEntrySignatures: false" to avoid this and reduce the number of chunks. Otherwise if this is intentional, set "preserveEntrySignatures: 'strict'" explicitly to silence this warning.`,url:"https://rollupjs.org/guide/en/#preserveentrysignatures"}),!1;for(const i of t)if(!s.has(i)&&i.module!==e)return!1;return!0}generateExports(){this.sortedExportNames=null;const e=new Set(this.exports);if(null!==this.facadeModule&&(!1!==this.facadeModule.preserveSignature||this.strictFacade)){const t=this.facadeModule.getExportNamesByVariable();for(const[s,i]of t){this.exportNamesByVariable.set(s,[...i]);for(const e of i)this.exportsByName.set(e,s);e.delete(s)}}this.outputOptions.minifyInternalExports?function(e,t,s){let i=0;for(const n of e){let[e]=n.name;if(t.has(e))do{e=Ft(++i),49===e.charCodeAt(0)&&(i+=9*64**(e.length-1),e=Ft(i))}while(ve.has(e)||t.has(e));t.set(e,n),s.set(n,[e])}}(e,this.exportsByName,this.exportNamesByVariable):function(e,t,s){for(const i of e){let e=0,n=i.name;for(;t.has(n);)n=i.name+"$"+ ++e;t.set(n,i),s.set(i,[n])}}(e,this.exportsByName,this.exportNamesByVariable),(this.outputOptions.preserveModules||this.facadeModule&&this.facadeModule.info.isEntry)&&(this.exportMode=Or(this,this.outputOptions,this.unsetOptions,this.facadeModule.id,this.inputOptions.onwarn))}generateFacades(){var e;const t=[],s=new Set([...this.entryModules,...this.implicitEntryModules]),i=new Set(this.dynamicEntryModules.map((({namespace:e})=>e)));for(const e of s)if(e.preserveSignature)for(const t of e.getExportNamesByVariable().keys())i.add(t);for(const e of s){const s=Array.from(new Set(e.chunkNames.filter((({isUserDefined:e})=>e)).map((({name:e})=>e))),(e=>({name:e})));if(0===s.length&&e.isUserDefinedEntryPoint&&s.push({}),s.push(...Array.from(e.chunkFileNames,(e=>({fileName:e})))),0===s.length&&s.push({}),!this.facadeModule){const t="strict"===e.preserveSignature||"exports-only"===e.preserveSignature&&0!==e.getExportNamesByVariable().size;(!t||this.outputOptions.preserveModules||this.canModuleBeFacade(e,i))&&(this.facadeModule=e,this.facadeChunkByModule.set(e,this),e.preserveSignature&&(this.strictFacade=t),this.assignFacadeName(s.shift(),e))}for(const i of s)t.push(Gr.generateFacade(this.inputOptions,this.outputOptions,this.unsetOptions,this.pluginDriver,this.modulesById,this.chunkByModule,this.facadeChunkByModule,this.includedNamespaces,e,i))}for(const t of this.dynamicEntryModules)t.info.syntheticNamedExports||(!this.facadeModule&&this.canModuleBeFacade(t,i)?(this.facadeModule=t,this.facadeChunkByModule.set(t,this),this.strictFacade=!0,this.dynamicName=Hr(t)):this.facadeModule===t&&!this.strictFacade&&this.canModuleBeFacade(t,i)?this.strictFacade=!0:(null===(e=this.facadeChunkByModule.get(t))||void 0===e?void 0:e.strictFacade)||(this.includedNamespaces.add(t),this.exports.add(t.namespace)));return t}generateId(e,t,s,i){if(null!==this.fileName)return this.fileName;const[n,r]=this.facadeModule&&this.facadeModule.isUserDefinedEntryPoint?[t.entryFileNames,"output.entryFileNames"]:[t.chunkFileNames,"output.chunkFileNames"];return Wr(zr("function"==typeof n?n(this.getChunkInfo()):n,r,{format:()=>t.format,hash:()=>i?this.computeContentHashWithDependencies(e,t,s):"[hash]",name:()=>this.getChunkName()}),s)}generateIdPreserveModules(e,t,s,i){const[{id:n}]=this.orderedModules,r=this.outputOptions.sanitizeFileName(n.split(qr,1)[0]);let a;const o=i.has("entryFileNames")?"[name][assetExtname].js":t.entryFileNames,h="function"==typeof o?o(this.getChunkInfo()):o;if(C(r)){const s=$(r),i=T(r),n=`${s}/${zr(h,"output.entryFileNames",{assetExtname:()=>jr.includes(i)?"":i,ext:()=>i.substring(1),extname:()=>i,format:()=>t.format,name:()=>this.getChunkName()})}`,{preserveModulesRoot:o}=t;a=o&&n.startsWith(o)?n.slice(o.length).replace(/^[\\/]/,""):R(e,n)}else{const e=T(r);a=`_virtual/${zr(h,"output.entryFileNames",{assetExtname:()=>jr.includes(e)?"":e,ext:()=>e.substring(1),extname:()=>e,format:()=>t.format,name:()=>se(r)})}`}return Wr(N(a),s)}getChunkInfo(){const e=this.facadeModule,t=this.getChunkName.bind(this);return{exports:this.getExportNames(),facadeModuleId:e&&e.id,isDynamicEntry:this.dynamicEntryModules.length>0,isEntry:null!==e&&e.info.isEntry,isImplicitEntry:this.implicitEntryModules.length>0,modules:this.renderedModules,get name(){return t()},type:"chunk"}}getChunkInfoWithFileNames(){return Object.assign(this.getChunkInfo(),{code:void 0,dynamicImports:Array.from(this.dynamicDependencies,Zi),fileName:this.id,implicitlyLoadedBefore:Array.from(this.implicitlyLoadedBefore,Zi),importedBindings:this.getImportedBindingsPerDependency(),imports:Array.from(this.dependencies,Zi),map:void 0,referencedFiles:this.getReferencedFiles()})}getChunkName(){var e;return null!==(e=this.name)&&void 0!==e?e:this.name=this.outputOptions.sanitizeFileName(this.getFallbackChunkName())}getExportNames(){var e;return null!==(e=this.sortedExportNames)&&void 0!==e?e:this.sortedExportNames=Array.from(this.exportsByName.keys()).sort()}getRenderedHash(){if(this.renderedHash)return this.renderedHash;const e=Ir(),t=this.pluginDriver.hookReduceValueSync("augmentChunkHash","",[this.getChunkInfo()],((e,t)=>(t&&(e+=t),e)));return e.update(t),e.update(this.renderedSource.toString()),e.update(this.getExportNames().map((e=>{const t=this.exportsByName.get(e);return`${ie(t.module.id).replace(/\\/g,"/")}:${t.name}:${e}`})).join(",")),this.renderedHash=e.digest("hex")}getVariableExportName(e){return this.outputOptions.preserveModules&&e instanceof Xi?"*":this.exportNamesByVariable.get(e)[0]}link(){this.dependencies=function(e,t,s){const i=[],n=new Set;for(let r=t.length-1;r>=0;r--){const a=t[r];if(!n.has(a)){const t=[];Br(a,t,n,e,s),i.unshift(t)}}const r=new Set;for(const e of i)for(const t of e)r.add(t);return r}(this,this.orderedModules,this.chunkByModule);for(const e of this.orderedModules)this.addDependenciesToChunk(e.dynamicDependencies,this.dynamicDependencies),this.addDependenciesToChunk(e.implicitlyLoadedBefore,this.implicitlyLoadedBefore),this.setUpChunkImportsAndExportsForModule(e)}preRender(e,t,s){const{_:i,getPropertyAccess:n,n:r}=s,a=new v({separator:`${r}${r}`});this.usedModules=[],this.indentString=function(e,t){if(!0!==t.indent)return t.indent;for(const t of e){const e=Vr(t.originalCode);if(null!==e)return e}return"\t"}(this.orderedModules,e);const o={dynamicImportFunction:e.dynamicImportFunction,exportNamesByVariable:this.exportNamesByVariable,format:e.format,freeze:e.freeze,indent:this.indentString,namespaceToStringTag:e.namespaceToStringTag,outputPluginDriver:this.pluginDriver,snippets:s};if(e.hoistTransitiveImports&&!this.outputOptions.preserveModules&&null!==this.facadeModule)for(const e of this.dependencies)e instanceof Gr&&this.inlineChunkDependencies(e);this.prepareModulesForRendering(s),this.setIdentifierRenderResolutions(e);let h="";const l=this.renderedModules;for(const t of this.orderedModules){let s=0;if(t.isIncluded()||this.includedNamespaces.has(t)){const i=t.render(o).trim();s=i.length(),s&&(e.compact&&i.lastLine().includes("//")&&i.append("\n"),this.renderedModuleSources.set(t,i),a.addSource(i),this.usedModules.push(t));const n=t.namespace;if(this.includedNamespaces.has(t)&&!this.outputOptions.preserveModules){const e=n.renderBlock(o);n.renderFirst()?h+=r+e:a.addSource(new x(e))}}const{renderedExports:i,removedExports:n}=t.getRenderedExports(),{renderedModuleSources:c}=this;l[t.id]={get code(){var e,s;return null!==(s=null===(e=c.get(t))||void 0===e?void 0:e.toString())&&void 0!==s?s:null},originalLength:t.originalCode.length,removedExports:n,renderedExports:i,renderedLength:s}}if(h&&a.prepend(h+r+r),this.needsExportsShim&&a.prepend(`${r}${s.cnst} _missingExportShim${i}=${i}void 0;${r}${r}`),e.compact?this.renderedSource=a:this.renderedSource=a.trim(),this.renderedHash=void 0,this.isEmpty&&0===this.getExportNames().length&&0===this.dependencies.size){const e=this.getChunkName();this.inputOptions.onwarn({chunkName:e,code:"EMPTY_BUNDLE",message:`Generated an empty chunk: "${e}"`})}this.setExternalRenderPaths(e,t),this.renderedDependencies=this.getChunkDependencyDeclarations(e,n),this.renderedExports="none"===this.exportMode?[]:this.getChunkExportDeclarations(e.format,n)}async render(e,t,s,i){hn("render format",2);const n=e.format,r=Fn[n];e.dynamicImportFunction&&"es"!==n&&this.inputOptions.onwarn(de("output.dynamicImportFunction","outputdynamicImportFunction",'this option is ignored for formats other than "es"'));for(const e of this.dependencies){const t=this.renderedDependencies.get(e);if(e instanceof ke){const s=e.renderPath;t.id=Lr(e.renormalizeRenderPath?ae(this.id,s,!1,!1):s)}else t.namedExportsMode="default"!==e.exportMode,t.id=Lr(ae(this.id,e.id,!1,!0))}this.finaliseDynamicImports(e,i),this.finaliseImportMetas(n,i);const a=0!==this.renderedExports.length||[...this.renderedDependencies.values()].some((e=>e.reexports&&0!==e.reexports.length));let o=null;const h=new Set;for(const e of this.orderedModules){e.usesTopLevelAwait&&(o=e.id);const t=this.accessedGlobalsByScope.get(e.scope);if(t)for(const e of t)h.add(e)}if(null!==o&&"es"!==n&&"system"!==n)return oe({code:"INVALID_TLA_FORMAT",id:o,message:`Module format ${n} does not support top-level await. Use the "es" or "system" output formats rather.`});if(!this.id)throw new Error("Internal Error: expecting chunk id");const l=r(this.renderedSource,{accessedGlobals:h,dependencies:[...this.renderedDependencies.values()],exports:this.renderedExports,hasExports:a,id:this.id,indent:this.indentString,intro:t.intro,isEntryFacade:this.outputOptions.preserveModules||null!==this.facadeModule&&this.facadeModule.info.isEntry,isModuleFacade:null!==this.facadeModule,namedExportsMode:"default"!==this.exportMode,outro:t.outro,snippets:i,usesTopLevelAwait:null!==o,warn:this.inputOptions.onwarn},e);t.banner&&l.prepend(t.banner),t.footer&&l.append(t.footer);const u=l.toString();ln("render format",2);let d=null;const p=[];let f=await function({code:e,options:t,outputPluginDriver:s,renderChunk:i,sourcemapChain:n}){return s.hookReduceArg0("renderChunk",[e,i,t],((e,t,s)=>{if(null==t)return e;if("string"==typeof t&&(t={code:t,map:void 0}),null!==t.map){const e=Fr(t.map);n.push(e||{missing:!0,plugin:s.name})}return t.code}))}({code:u,options:e,outputPluginDriver:this.pluginDriver,renderChunk:s,sourcemapChain:p});if(e.sourcemap){let t;hn("sourcemap",2),t=e.file?M(e.sourcemapFile||e.file):e.dir?M(e.dir,this.id):M(this.id);const s=l.generateDecodedMap({});d=function(e,t,s,i,n,r){const a=jn(r),o=s.filter((e=>!e.excludeFromSourcemap)).map((e=>Un(e.id,e.originalCode,e.originalSourcemap,e.sourcemapChain,a))),h=new Wn(t,o),l=i.reduce(a,h);let{sources:u,sourcesContent:d,names:p,mappings:f}=l.traceMappings();if(e){const t=$(e);u=u.map((e=>R(t,e))),e=_(e)}return d=n?null:d,new c({file:e,mappings:f,names:p,sources:u,sourcesContent:d})}(t,s,this.usedModules,p,e.sourcemapExcludeSources,this.inputOptions.onwarn),d.sources=d.sources.map((s=>{const{sourcemapPathTransform:i}=e;if(i){const e=i(s,`${t}.map`);return"string"!=typeof e&&oe(ye("sourcemapPathTransform function must return a string.")),e}return s})).map(N),ln("sourcemap",2)}return e.compact||"\n"===f[f.length-1]||(f+="\n"),{code:f,map:d}}addDependenciesToChunk(e,t){for(const s of e)if(s instanceof mn){const e=this.chunkByModule.get(s);e&&e!==this&&t.add(e)}else t.add(s)}assignFacadeName({fileName:e,name:t},s){e?this.fileName=e:this.name=this.outputOptions.sanitizeFileName(t||Hr(s))}checkCircularDependencyImport(e,t){const s=e.module;if(s instanceof mn){const o=this.chunkByModule.get(s);let h;do{if(h=t.alternativeReexportModules.get(e),h){const l=this.chunkByModule.get(h);l&&l!==o&&this.inputOptions.onwarn((i=s.getExportNamesByVariable().get(e)[0],n=s.id,r=h.id,a=t.id,{code:le.CYCLIC_CROSS_CHUNK_REEXPORT,exporter:n,importer:a,message:`Export "${i}" of module ${ie(n)} was reexported through module ${ie(r)} while both modules are dependencies of each other and will end up in different chunks by current Rollup settings. This scenario is not well supported at the moment as it will produce a circular dependency between chunks and will likely lead to broken execution order.\nEither change the import in ${ie(a)} to point directly to the exporting module or do not use "preserveModules" to ensure these modules end up in the same chunk.`,reexporter:r})),t=h}}while(h)}var i,n,r,a}computeContentHashWithDependencies(e,t,s){const i=Ir();i.update([e.intro,e.outro,e.banner,e.footer].join(":")),i.update(t.format);const n=new Set([this]);for(const r of n)if(r instanceof ke?i.update(`:${r.renderPath}`):(i.update(r.getRenderedHash()),i.update(r.generateId(e,t,s,!1))),!(r instanceof ke))for(const e of[...r.dependencies,...r.dynamicDependencies])n.add(e);return i.digest("hex").substr(0,8)}ensureReexportsAreAvailableForModule(e){const t=e.getExportNamesByVariable();for(const s of t.keys()){const t=s instanceof Yi,i=t?s.getBaseVariable():s;if(!(i instanceof Xi&&this.outputOptions.preserveModules)){this.checkCircularDependencyImport(i,e);const s=i.module;if(s instanceof mn){const e=this.chunkByModule.get(s);e&&e!==this&&(e.exports.add(i),t&&this.imports.add(i))}}}}finaliseDynamicImports(e,t){const s="amd"===e.format;for(const[e,i]of this.renderedModuleSources)for(const{node:n,resolution:r}of e.dynamicImports){const e=this.chunkByModule.get(r),a=this.facadeChunkByModule.get(r);if(!r||!n.included||e===this)continue;const o=r instanceof mn?`'${Lr(ae(this.id,(a||e).id,s,!0))}'`:r instanceof ke?`'${Lr(r.renormalizeRenderPath?ae(this.id,r.renderPath,s,!1):r.renderPath)}'`:r;n.renderFinalResolution(i,o,r instanceof mn&&!(null==a?void 0:a.strictFacade)&&e.exportNamesByVariable.get(r.namespace)[0],t)}}finaliseImportMetas(e,t){for(const[s,i]of this.renderedModuleSources)for(const n of s.importMetas)n.renderFinalMechanism(i,this.id,e,t,this.pluginDriver)}generateVariableName(){if(this.manualChunkAlias)return this.manualChunkAlias;const e=this.entryModules[0]||this.implicitEntryModules[0]||this.dynamicEntryModules[0]||this.orderedModules[this.orderedModules.length-1];return e?Hr(e):"chunk"}getChunkDependencyDeclarations(e,t){const s=this.getImportSpecifiers(t),i=this.getReexportSpecifiers(),n=new Map;for(const t of this.dependencies){const r=s.get(t)||null,a=i.get(t)||null,o=t instanceof ke||"default"!==t.exportMode;n.set(t,{defaultVariableName:t.defaultVariableName,globalName:t instanceof ke&&("umd"===e.format||"iife"===e.format)&&Ur(t,e.globals,null!==(r||a),this.inputOptions.onwarn),id:void 0,imports:r,isChunk:t instanceof Gr,name:t.variableName,namedExportsMode:o,namespaceVariableName:t.namespaceVariableName,reexports:a})}return n}getChunkExportDeclarations(e,t){const s=[];for(const i of this.getExportNames()){if("*"===i[0])continue;const n=this.exportsByName.get(i);if(!(n instanceof Yi)){const e=n.module;if(e&&this.chunkByModule.get(e)!==this)continue}let r=null,a=!1,o=n.getName(t);if(n instanceof Bt){for(const e of n.declarations)if(e.parent instanceof Hs||e instanceof qs&&e.declaration instanceof Hs){a=!0;break}}else n instanceof Yi&&(r=o,"es"===e&&(o=n.renderName));s.push({exported:i,expression:r,hoisted:a,local:o})}return s}getDependenciesToBeDeconflicted(e,t,s){const i=new Set,n=new Set,r=new Set;for(const t of[...this.exportNamesByVariable.keys(),...this.imports])if(e||t.isNamespace){const a=t.module;if(a instanceof ke)i.add(a),e&&("default"===t.name?ri[String(s(a.id))]&&n.add(a):"*"===t.name&&oi[String(s(a.id))]&&r.add(a));else{const s=this.chunkByModule.get(a);s!==this&&(i.add(s),e&&"default"===s.exportMode&&t.isNamespace&&r.add(s))}}if(t)for(const e of this.dependencies)i.add(e);return{deconflictedDefault:n,deconflictedNamespace:r,dependencies:i}}getFallbackChunkName(){return this.manualChunkAlias?this.manualChunkAlias:this.dynamicName?this.dynamicName:this.fileName?se(this.fileName):se(this.orderedModules[this.orderedModules.length-1].id)}getImportSpecifiers(e){const{interop:t}=this.outputOptions,s=new Map;for(const i of this.imports){const n=i.module;let r,a;if(n instanceof ke){if(r=n,a=i.name,"default"!==a&&"*"!==a&&"defaultOnly"===t(n.id))return oe(me(n.id,a,!1))}else r=this.chunkByModule.get(n),a=r.getVariableExportName(i);D(s,r,(()=>[])).push({imported:a,local:i.getName(e)})}return s}getImportedBindingsPerDependency(){const e={};for(const[t,s]of this.renderedDependencies){const i=new Set;if(s.imports)for(const{imported:e}of s.imports)i.add(e);if(s.reexports)for(const{imported:e}of s.reexports)i.add(e);e[t.id]=[...i]}return e}getReexportSpecifiers(){const{externalLiveBindings:e,interop:t}=this.outputOptions,s=new Map;for(let i of this.getExportNames()){let n,r,a=!1;if("*"===i[0]){const s=i.substring(1);"defaultOnly"===t(s)&&this.inputOptions.onwarn(ge(s)),a=e,n=this.modulesById.get(s),r=i="*"}else{const s=this.exportsByName.get(i);if(s instanceof Yi)continue;const o=s.module;if(o instanceof mn){if(n=this.chunkByModule.get(o),n===this)continue;r=n.getVariableExportName(s),a=s.isReassigned}else{if(n=o,r=s.name,"default"!==r&&"*"!==r&&"defaultOnly"===t(o.id))return oe(me(o.id,r,!0));a=e&&("default"!==r||ai(String(t(o.id)),!0))}}D(s,n,(()=>[])).push({imported:r,needsLiveBinding:a,reexported:i})}return s}getReferencedFiles(){const e=[];for(const t of this.orderedModules)for(const s of t.importMetas){const t=s.getReferencedFileName(this.pluginDriver);t&&e.push(t)}return e}inlineChunkDependencies(e){for(const t of e.dependencies)this.dependencies.has(t)||(this.dependencies.add(t),t instanceof Gr&&this.inlineChunkDependencies(t))}prepareModulesForRendering(e){var t;const s=this.accessedGlobalsByScope;for(const i of this.orderedModules){for(const{node:n,resolution:r}of i.dynamicImports)if(n.included)if(r instanceof mn){const i=this.chunkByModule.get(r);i===this?n.setInternalResolution(r.namespace):n.setExternalResolution((null===(t=this.facadeChunkByModule.get(r))||void 0===t?void 0:t.exportMode)||i.exportMode,r,this.outputOptions,e,this.pluginDriver,s)}else n.setExternalResolution("external",r,this.outputOptions,e,this.pluginDriver,s);for(const e of i.importMetas)e.addAccessedGlobals(this.outputOptions.format,s);this.includedNamespaces.has(i)&&!this.outputOptions.preserveModules&&i.namespace.prepare(s)}}setExternalRenderPaths(e,t){for(const s of[...this.dependencies,...this.dynamicDependencies])s instanceof ke&&s.setRenderPath(e,t)}setIdentifierRenderResolutions({format:e,interop:t,namespaceToStringTag:s}){const i=new Set;for(const t of this.getExportNames()){const s=this.exportsByName.get(t);"es"!==e&&"system"!==e&&s.isReassigned&&!s.isId?s.setRenderNames("exports",t):s instanceof Yi?i.add(s):s.setRenderNames(null,null)}for(const e of this.orderedModules)if(e.needsExportShim){this.needsExportsShim=!0;break}const n=new Set(["Object","Promise"]);switch(this.needsExportsShim&&n.add(qi),s&&n.add("Symbol"),e){case"system":n.add("module").add("exports");break;case"es":break;case"cjs":n.add("module").add("require").add("__filename").add("__dirname");default:n.add("exports");for(const e of Ai)n.add(e)}_r(this.orderedModules,this.getDependenciesToBeDeconflicted("es"!==e&&"system"!==e,"amd"===e||"umd"===e||"iife"===e,t),this.imports,n,e,t,this.outputOptions.preserveModules,this.outputOptions.externalLiveBindings,this.chunkByModule,i,this.exportNamesByVariable,this.accessedGlobalsByScope,this.includedNamespaces)}setUpChunkImportsAndExportsForModule(e){const t=new Set(e.imports);if(!this.outputOptions.preserveModules&&this.includedNamespaces.has(e)){const s=e.namespace.getMemberVariables();for(const e of Object.values(s))t.add(e)}for(let s of t){s instanceof zi&&(s=s.getOriginalVariable()),s instanceof Yi&&(s=s.getBaseVariable());const t=this.chunkByModule.get(s.module);t!==this&&(this.imports.add(s),!(s instanceof Xi&&this.outputOptions.preserveModules)&&s.module instanceof mn&&(t.exports.add(s),this.checkCircularDependencyImport(s,e)))}(this.includedNamespaces.has(e)||e.info.isEntry&&!1!==e.preserveSignature||e.includedDynamicImporters.some((e=>this.chunkByModule.get(e)!==this)))&&this.ensureReexportsAreAvailableForModule(e);for(const{node:t,resolution:s}of e.dynamicImports)t.included&&s instanceof mn&&this.chunkByModule.get(s)===this&&!this.includedNamespaces.has(s)&&(this.includedNamespaces.add(s),this.ensureReexportsAreAvailableForModule(s))}}function Hr(e){var t,s,i,n;return null!==(n=null!==(s=null===(t=e.chunkNames.find((({isUserDefined:e})=>e)))||void 0===t?void 0:t.name)&&void 0!==s?s:null===(i=e.chunkNames[0])||void 0===i?void 0:i.name)&&void 0!==n?n:se(e.id)}const qr=/[?#]/;function Kr(e,t,s){e in t&&s(function(e){return{code:le.FILE_NAME_CONFLICT,message:`The emitted file "${e}" overwrites a previously emitted file of the same name.`}}(e)),t[e]=Xr}const Xr={type:"placeholder"};function Yr(e,t,s){if(!("string"==typeof e||e instanceof Uint8Array)){const e=t.fileName||t.name||s;return oe(ye(`Could not set source for ${"string"==typeof e?`asset "${e}"`:"unnamed asset"}, asset source needs to be a string, Uint8Array or Buffer.`))}return e}function Qr(e,t){return"string"!=typeof e.fileName?oe((s=e.name||t,{code:le.ASSET_NOT_FINALISED,message:`Plugin error - Unable to get file name for asset "${s}". Ensure that the source is set and that generate is called first.`})):e.fileName;var s}function Zr(e,t){var s;const i=e.fileName||e.module&&(null===(s=null==t?void 0:t.get(e.module))||void 0===s?void 0:s.id);return i||oe((n=e.fileName||e.name,{code:le.CHUNK_NOT_GENERATED,message:`Plugin error - Unable to get file name for chunk "${n}". Ensure that generate is called first.`}));var n}class Jr{constructor(e,t,s){this.graph=e,this.options=t,this.bundle=null,this.facadeChunkByModule=null,this.outputOptions=null,this.assertAssetsFinalized=()=>{for(const[t,s]of this.filesByReferenceId)if("asset"===s.type&&"string"!=typeof s.fileName)return oe((e=s.name||t,{code:le.ASSET_SOURCE_MISSING,message:`Plugin error creating asset "${e}" - no asset source set.`}));var e},this.emitFile=e=>function(e){return Boolean(e&&("asset"===e.type||"chunk"===e.type))}(e)?function(e){const t=e.fileName||e.name;return!t||"string"==typeof t&&!ne(t)}(e)?"chunk"===e.type?this.emitChunk(e):this.emitAsset(e):oe(ye(`The "fileName" or "name" properties of emitted files must be strings that are neither absolute nor relative paths, received "${e.fileName||e.name}".`)):oe(ye(`Emitted files must be of type "asset" or "chunk", received "${e&&e.type}".`)),this.getFileName=e=>{const t=this.filesByReferenceId.get(e);return t?"chunk"===t.type?Zr(t,this.facadeChunkByModule):Qr(t,e):oe((s=e,{code:le.FILE_NOT_FOUND,message:`Plugin error - Unable to get file name for unknown file "${s}".`}));var s},this.setAssetSource=(e,t)=>{const s=this.filesByReferenceId.get(e);if(!s)return oe((i=e,{code:le.ASSET_NOT_FOUND,message:`Plugin error - Unable to set the source for unknown asset "${i}".`}));var i,n;if("asset"!==s.type)return oe(ye(`Asset sources can only be set for emitted assets but "${e}" is an emitted chunk.`));if(void 0!==s.source)return oe((n=s.name||e,{code:le.ASSET_SOURCE_ALREADY_SET,message:`Unable to set the source for asset "${n}", source already set.`}));const r=Yr(t,s,e);this.bundle?this.finalizeAsset(s,r,e,this.bundle):s.source=r},this.setOutputBundle=(e,t,s)=>{this.outputOptions=t,this.bundle=e,this.facadeChunkByModule=s;for(const e of this.filesByReferenceId.values())e.fileName&&Kr(e.fileName,this.bundle,this.options.onwarn);for(const[e,t]of this.filesByReferenceId)"asset"===t.type&&void 0!==t.source&&this.finalizeAsset(t,t.source,e,this.bundle)},this.filesByReferenceId=s?new Map(s.filesByReferenceId):new Map}assignReferenceId(e,t){let s;do{s=Ir().update(s||t).digest("hex").substring(0,8)}while(this.filesByReferenceId.has(s));return this.filesByReferenceId.set(s,e),s}emitAsset(e){const t=void 0!==e.source?Yr(e.source,e,null):void 0,s={fileName:e.fileName,name:e.name,source:t,type:"asset"},i=this.assignReferenceId(s,e.fileName||e.name||e.type);return this.bundle&&(e.fileName&&Kr(e.fileName,this.bundle,this.options.onwarn),void 0!==t&&this.finalizeAsset(s,t,i,this.bundle)),i}emitChunk(e){if(this.graph.phase>Qi.LOAD_AND_PARSE)return oe({code:le.INVALID_ROLLUP_PHASE,message:"Cannot emit chunks after module loading has finished."});if("string"!=typeof e.id)return oe(ye(`Emitted chunks need to have a valid string id, received "${e.id}"`));const t={fileName:e.fileName,module:null,name:e.name||e.id,type:"chunk"};return this.graph.moduleLoader.emitChunk(e).then((e=>t.module=e)).catch((()=>{})),this.assignReferenceId(t,e.id)}finalizeAsset(e,t,s,i){const n=e.fileName||function(e,t){for(const[s,i]of Object.entries(e))if("asset"===i.type&&ea(t,i.source))return s;return null}(i,t)||function(e,t,s,i){const n=s.sanitizeFileName(e||"asset");return Wr(zr("function"==typeof s.assetFileNames?s.assetFileNames({name:e,source:t,type:"asset"}):s.assetFileNames,"output.assetFileNames",{ext:()=>T(n).substring(1),extname:()=>T(n),hash:()=>Ir().update(n).update(":").update(t).digest("hex").substring(0,8),name:()=>n.substring(0,n.length-T(n).length)}),i)}(e.name,t,this.outputOptions,i),r={...e,fileName:n,source:t};this.filesByReferenceId.set(s,r);const{options:a}=this;i[n]={fileName:n,get isAsset(){return xe('Accessing "isAsset" on files in the bundle is deprecated, please use "type === \'asset\'" instead',!0,a),!0},name:e.name,source:t,type:"asset"}}}function ea(e,t){if("string"==typeof e)return e===t;if("string"==typeof t)return!1;if("equals"in e)return e.equals(t);if(e.length!==t.length)return!1;for(let s=0;s<e.length;s++)if(e[s]!==t[s])return!1;return!0}const ta=(e,t)=>t?`${e}\n${t}`:e,sa=(e,t)=>t?`${e}\n\n${t}`:e;function ia(e,t){const s=[],i=new Set(t.keys()),n=Object.create(null);for(const[e,s]of t)na(e,n[s]=n[s]||[],i);for(const[e,t]of Object.entries(n))s.push({alias:e,modules:t});const r=new Map,{dependentEntryPointsByModule:a,dynamicEntryModules:o}=function(e){const t=new Set,s=new Map,i=new Set(e);for(const e of i){const n=new Set([e]);for(const r of n){D(s,r,(()=>new Set)).add(e);for(const e of r.getDependenciesToBeIncluded())e instanceof ke||n.add(e);for(const{resolution:e}of r.dynamicImports)e instanceof mn&&e.includedDynamicImporters.length>0&&(t.add(e),i.add(e));for(const e of r.implicitlyLoadedBefore)t.add(e),i.add(e)}}return{dependentEntryPointsByModule:s,dynamicEntryModules:t}}(e),h=function(e,t){const s=new Map;for(const i of t){const t=D(s,i,(()=>new Set));for(const s of[...i.includedDynamicImporters,...i.implicitlyLoadedAfter])for(const i of e.get(s))t.add(i)}return s}(a,o),l=new Set(e);function c(e,t){const s=new Set([e]);for(const n of s){const o=D(r,n,(()=>new Set));if(!t||!u(t,a.get(n))){o.add(e);for(const e of n.getDependenciesToBeIncluded())e instanceof ke||i.has(e)||s.add(e)}}}function u(e,t){const s=new Set(e);for(const e of s)if(!t.has(e)){if(l.has(e))return!1;const t=h.get(e);for(const e of t)s.add(e)}return!0}for(const t of e)i.has(t)||c(t,null);for(const e of o)i.has(e)||c(e,h.get(e));return s.push(...function(e,t){const s=Object.create(null);for(const[i,n]of t){let t="";for(const s of e)t+=n.has(s)?"X":"_";const r=s[t];r?r.push(i):s[t]=[i]}return Object.values(s).map((e=>({alias:null,modules:e})))}([...e,...o],r)),s}function na(e,t,s){const i=new Set([e]);for(const e of i){s.add(e),t.push(e);for(const t of e.dependencies)t instanceof ke||s.has(t)||i.add(t)}}const ra=(e,t)=>e.execIndex>t.execIndex?1:-1;function aa(e,t,s){const i=Symbol(e.id),n=[ie(e.id)];let r=t;for(e.cycles.add(i);r!==e;)r.cycles.add(i),n.push(ie(r.id)),r=s.get(r);return n.push(n[0]),n.reverse(),n}const oa=(e,t)=>t?`(${e})`:e,ha=/^(?!\d)[\w$]+$/;class la{constructor(e,t,s,i,n){this.outputOptions=e,this.unsetOptions=t,this.inputOptions=s,this.pluginDriver=i,this.graph=n,this.facadeChunkByModule=new Map,this.includedNamespaces=new Set}async generate(e){hn("GENERATE",1);const t=Object.create(null);this.pluginDriver.setOutputBundle(t,this.outputOptions,this.facadeChunkByModule);try{await this.pluginDriver.hookParallel("renderStart",[this.outputOptions,this.inputOptions]),hn("generate chunks",2);const e=await this.generateChunks();e.length>1&&(s=this.outputOptions,i=this.inputOptions.onwarn,"umd"===s.format||"iife"===s.format?oe(de("output.format","outputformat","UMD and IIFE output formats are not supported for code-splitting builds",s.format)):"string"==typeof s.file?oe(de("output.file","outputdir",'when building multiple chunks, the "output.dir" option must be used, not "output.file". To inline dynamic imports, set the "inlineDynamicImports" option')):s.sourcemapFile?oe(de("output.sourcemapFile","outputsourcemapfile",'"output.sourcemapFile" is only supported for single-file builds')):!s.amd.autoId&&s.amd.id&&i(de("output.amd.id","outputamd",'this option is only properly supported for single-file builds. Use "output.amd.autoId" and "output.amd.basePath" instead')));const n=function(e){if(0===e.length)return"/";if(1===e.length)return $(e[0]);const t=e.slice(1).reduce(((e,t)=>{const s=t.split(/\/+|\\+/);let i;for(i=0;e[i]===s[i]&&i<Math.min(e.length,s.length);i++);return e.slice(0,i)}),e[0].split(/\/+|\\+/));return t.length>1?t.join("/"):"/"}(function(e){const t=[];for(const s of e)for(const e of s.entryModules)C(e.id)&&t.push(e.id);return t}(e));ln("generate chunks",2),hn("render modules",2);const r=await async function(e,t){try{let[s,i,n,r]=await Promise.all([t.hookReduceValue("banner",e.banner(),[],ta),t.hookReduceValue("footer",e.footer(),[],ta),t.hookReduceValue("intro",e.intro(),[],sa),t.hookReduceValue("outro",e.outro(),[],sa)]);return n&&(n+="\n\n"),r&&(r=`\n\n${r}`),s.length&&(s+="\n"),i.length&&(i="\n"+i),{banner:s,footer:i,intro:n,outro:r}}catch(e){return oe({code:"ADDON_ERROR",message:`Could not retrieve ${e.hook}. Check configuration of plugin ${e.plugin}.\n\tError Message: ${e.message}`})}}(this.outputOptions,this.pluginDriver),a=function({compact:e,generatedCode:{arrowFunctions:t,constBindings:s,objectShorthand:i,reservedNamesAsProps:n}}){const{_:r,n:a,s:o}=e?{_:"",n:"",s:""}:{_:" ",n:"\n",s:";"},h=s?"const":"var",l=(e,{isAsync:t,name:s})=>`${t?"async ":""}function${s?` ${s}`:""}${r}(${e.join(`,${r}`)})${r}`,c=t?(e,{isAsync:t,name:s})=>{const i=1===e.length;return`${s?`${h} ${s}${r}=${r}`:""}${t?`async${i?" ":r}`:""}${i?e[0]:`(${e.join(`,${r}`)})`}${r}=>${r}`}:l,u=(e,{functionReturn:s,lineBreakIndent:i,name:n})=>[`${c(e,{isAsync:!1,name:n})}${t?i?`${a}${i.base}${i.t}`:"":`{${i?`${a}${i.base}${i.t}`:r}${s?"return ":""}`}`,t?`${n?";":""}${i?`${a}${i.base}`:""}`:`${o}${i?`${a}${i.base}`:r}}`],d=n?e=>ha.test(e):e=>!ve.has(e)&&ha.test(e);return{_:r,cnst:h,getDirectReturnFunction:u,getDirectReturnIifeLeft:(e,s,{needsArrowReturnParens:i,needsWrappedFunction:n})=>{const[r,a]=u(e,{functionReturn:!0,lineBreakIndent:null,name:null});return`${oa(`${r}${oa(s,t&&i)}${a}`,t||n)}(`},getFunctionIntro:c,getNonArrowFunctionIntro:l,getObject(e,{lineBreakIndent:t}){const s=t?`${a}${t.base}${t.t}`:r;return`{${e.map((([e,t])=>{if(null===e)return`${s}${t}`;const n=!d(e);return e===t&&i&&!n?s+e:`${s}${n?`'${e}'`:e}:${r}${t}`})).join(",")}${0===e.length?"":t?`${a}${t.base}`:r}}`},getPropertyAccess:e=>d(e)?`.${e}`:`[${JSON.stringify(e)}]`,n:a,s:o}}(this.outputOptions);this.prerenderChunks(e,n,a),ln("render modules",2),await this.addFinalizedChunksToBundle(e,n,r,t,a)}catch(e){throw await this.pluginDriver.hookParallel("renderError",[e]),e}var s,i;return await this.pluginDriver.hookSeq("generateBundle",[this.outputOptions,t,e]),this.finaliseAssets(t),ln("GENERATE",1),t}async addFinalizedChunksToBundle(e,t,s,i,n){this.assignChunkIds(e,t,s,i);for(const t of e)i[t.id]=t.getChunkInfoWithFileNames();await Promise.all(e.map((async e=>{const t=i[e.id];Object.assign(t,await e.render(this.outputOptions,s,t,n))})))}async addManualChunks(e){const t=new Map,s=await Promise.all(Object.entries(e).map((async([e,t])=>({alias:e,entries:await this.graph.moduleLoader.addAdditionalModules(t)}))));for(const{alias:e,entries:i}of s)for(const s of i)ua(e,s,t);return t}assignChunkIds(e,t,s,i){const n=[],r=[];for(const t of e)(t.facadeModule&&t.facadeModule.isUserDefinedEntryPoint?n:r).push(t);const a=n.concat(r);for(const e of a)this.outputOptions.file?e.id=_(this.outputOptions.file):this.outputOptions.preserveModules?e.id=e.generateIdPreserveModules(t,this.outputOptions,i,this.unsetOptions):e.id=e.generateId(s,this.outputOptions,i,!0),i[e.id]=Xr}assignManualChunks(e){const t=[],s={getModuleIds:()=>this.graph.modulesById.keys(),getModuleInfo:this.graph.getModuleInfo};for(const i of this.graph.modulesById.values())if(i instanceof mn){const n=e(i.id,s);"string"==typeof n&&t.push([n,i])}t.sort((([e],[t])=>e>t?1:e<t?-1:0));const i=new Map;for(const[e,s]of t)ua(e,s,i);return i}finaliseAssets(e){for(const t of Object.values(e))if(t.type||(xe('A plugin is directly adding properties to the bundle object in the "generateBundle" hook. This is deprecated and will be removed in a future Rollup version, please use "this.emitFile" instead.',!0,this.inputOptions),t.type="asset"),this.outputOptions.validate&&"code"in t)try{this.graph.contextParse(t.code,{allowHashBang:!0,ecmaVersion:"latest"})}catch(e){this.inputOptions.onwarn(ce(t,e))}this.pluginDriver.finaliseAssets()}async generateChunks(){const{manualChunks:e}=this.outputOptions,t="object"==typeof e?await this.addManualChunks(e):this.assignManualChunks(e),s=[],i=new Map;for(const{alias:e,modules:n}of this.outputOptions.inlineDynamicImports?[{alias:null,modules:ca(this.graph.modulesById)}]:this.outputOptions.preserveModules?ca(this.graph.modulesById).map((e=>({alias:null,modules:[e]}))):ia(this.graph.entryModules,t)){n.sort(ra);const t=new Gr(n,this.inputOptions,this.outputOptions,this.unsetOptions,this.pluginDriver,this.graph.modulesById,i,this.facadeChunkByModule,this.includedNamespaces,e);s.push(t);for(const e of n)i.set(e,t)}for(const e of s)e.link();const n=[];for(const e of s)n.push(...e.generateFacades());return[...s,...n]}prerenderChunks(e,t,s){for(const t of e)t.generateExports();for(const i of e)i.preRender(this.outputOptions,t,s)}}function ca(e){return[...e.values()].filter((e=>e instanceof mn&&(e.isIncluded()||e.info.isEntry||e.includedDynamicImporters.length>0)))}function ua(e,t,s){const i=s.get(t);if("string"==typeof i&&i!==e)return oe((n=t.id,r=e,a=i,{code:le.INVALID_CHUNK,message:`Cannot assign ${ie(n)} to the "${r}" chunk as it is already in the "${a}" chunk.`}));var n,r,a;s.set(t,e)}var da=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,357,0,62,13,1495,6,110,6,6,9,4759,9,787719,239],pa=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2637,96,16,1070,4050,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,46,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,482,44,11,6,17,0,322,29,19,43,1269,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4152,8,221,3,5761,15,7472,3104,541,1507,4938],fa="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟊꟐꟑꟓꟕ-ꟙꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",ma={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},ga="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",ya={5:ga,"5module":ga+" export import",6:ga+" const class extends export import super"},Ea=/^in(stanceof)?$/,xa=new RegExp("["+fa+"]"),ba=new RegExp("["+fa+"‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿]");function va(e,t){for(var s=65536,i=0;i<t.length;i+=2){if((s+=t[i])>e)return!1;if((s+=t[i+1])>=e)return!0}}function Aa(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&xa.test(String.fromCharCode(e)):!1!==t&&va(e,pa)))}function Sa(e,t){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&ba.test(String.fromCharCode(e)):!1!==t&&(va(e,pa)||va(e,da)))))}var Pa=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null};function ka(e,t){return new Pa(e,{beforeExpr:!0,binop:t})}var wa={beforeExpr:!0},Ca={startsExpr:!0},Ia={};function Na(e,t){return void 0===t&&(t={}),t.keyword=e,Ia[e]=new Pa(e,t)}var _a={num:new Pa("num",Ca),regexp:new Pa("regexp",Ca),string:new Pa("string",Ca),name:new Pa("name",Ca),privateId:new Pa("privateId",Ca),eof:new Pa("eof"),bracketL:new Pa("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new Pa("]"),braceL:new Pa("{",{beforeExpr:!0,startsExpr:!0}),braceR:new Pa("}"),parenL:new Pa("(",{beforeExpr:!0,startsExpr:!0}),parenR:new Pa(")"),comma:new Pa(",",wa),semi:new Pa(";",wa),colon:new Pa(":",wa),dot:new Pa("."),question:new Pa("?",wa),questionDot:new Pa("?."),arrow:new Pa("=>",wa),template:new Pa("template"),invalidTemplate:new Pa("invalidTemplate"),ellipsis:new Pa("...",wa),backQuote:new Pa("`",Ca),dollarBraceL:new Pa("${",{beforeExpr:!0,startsExpr:!0}),eq:new Pa("=",{beforeExpr:!0,isAssign:!0}),assign:new Pa("_=",{beforeExpr:!0,isAssign:!0}),incDec:new Pa("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new Pa("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:ka("||",1),logicalAND:ka("&&",2),bitwiseOR:ka("|",3),bitwiseXOR:ka("^",4),bitwiseAND:ka("&",5),equality:ka("==/!=/===/!==",6),relational:ka("</>/<=/>=",7),bitShift:ka("<</>>/>>>",8),plusMin:new Pa("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:ka("%",10),star:ka("*",10),slash:ka("/",10),starstar:new Pa("**",{beforeExpr:!0}),coalesce:ka("??",1),_break:Na("break"),_case:Na("case",wa),_catch:Na("catch"),_continue:Na("continue"),_debugger:Na("debugger"),_default:Na("default",wa),_do:Na("do",{isLoop:!0,beforeExpr:!0}),_else:Na("else",wa),_finally:Na("finally"),_for:Na("for",{isLoop:!0}),_function:Na("function",Ca),_if:Na("if"),_return:Na("return",wa),_switch:Na("switch"),_throw:Na("throw",wa),_try:Na("try"),_var:Na("var"),_const:Na("const"),_while:Na("while",{isLoop:!0}),_with:Na("with"),_new:Na("new",{beforeExpr:!0,startsExpr:!0}),_this:Na("this",Ca),_super:Na("super",Ca),_class:Na("class",Ca),_extends:Na("extends",wa),_export:Na("export"),_import:Na("import",Ca),_null:Na("null",Ca),_true:Na("true",Ca),_false:Na("false",Ca),_in:Na("in",{beforeExpr:!0,binop:7}),_instanceof:Na("instanceof",{beforeExpr:!0,binop:7}),_typeof:Na("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:Na("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:Na("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},$a=/\r\n?|\n|\u2028|\u2029/,Ta=new RegExp($a.source,"g");function Ra(e){return 10===e||13===e||8232===e||8233===e}function Ma(e,t,s){void 0===s&&(s=e.length);for(var i=t;i<s;i++){var n=e.charCodeAt(i);if(Ra(n))return i<s-1&&13===n&&10===e.charCodeAt(i+1)?i+2:i+1}return-1}var Da=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,La=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,Oa=Object.prototype,Va=Oa.hasOwnProperty,Ba=Oa.toString,Fa=Object.hasOwn||function(e,t){return Va.call(e,t)},za=Array.isArray||function(e){return"[object Array]"===Ba.call(e)};function Wa(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}function ja(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}var Ua=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,Ga=function(e,t){this.line=e,this.column=t};Ga.prototype.offset=function(e){return new Ga(this.line,this.column+e)};var Ha=function(e,t,s){this.start=t,this.end=s,null!==e.sourceFile&&(this.source=e.sourceFile)};function qa(e,t){for(var s=1,i=0;;){var n=Ma(e,i,t);if(n<0)return new Ga(s,t-i);++s,i=n}}var Ka={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},Xa=!1;function Ya(e){var t={};for(var s in Ka)t[s]=e&&Fa(e,s)?e[s]:Ka[s];if("latest"===t.ecmaVersion?t.ecmaVersion=1e8:null==t.ecmaVersion?(!Xa&&"object"==typeof console&&console.warn&&(Xa=!0,console.warn("Since Acorn 8.0.0, options.ecmaVersion is required.\nDefaulting to 2020, but this will stop working in the future.")),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),za(t.onToken)){var i=t.onToken;t.onToken=function(e){return i.push(e)}}return za(t.onComment)&&(t.onComment=function(e,t){return function(s,i,n,r,a,o){var h={type:s?"Block":"Line",value:i,start:n,end:r};e.locations&&(h.loc=new Ha(this,a,o)),e.ranges&&(h.range=[n,r]),t.push(h)}}(t,t.onComment)),t}var Qa=256;function Za(e,t){return 2|(e?4:0)|(t?8:0)}var Ja=function(e,t,s){this.options=e=Ya(e),this.sourceFile=e.sourceFile,this.keywords=Wa(ya[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var i="";!0!==e.allowReserved&&(i=ma[e.ecmaVersion>=6?6:5===e.ecmaVersion?5:3],"module"===e.sourceType&&(i+=" await")),this.reservedWords=Wa(i);var n=(i?i+" ":"")+ma.strict;this.reservedWordsStrict=Wa(n),this.reservedWordsStrictBind=Wa(n+" "+ma.strictBind),this.input=String(t),this.containsEsc=!1,s?(this.pos=s,this.lineStart=this.input.lastIndexOf("\n",s-1)+1,this.curLine=this.input.slice(0,this.lineStart).split($a).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=_a.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null,this.privateNameStack=[]},eo={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};Ja.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},eo.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},eo.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0&&!this.currentVarScope().inClassFieldInit},eo.inAsync.get=function(){return(4&this.currentVarScope().flags)>0&&!this.currentVarScope().inClassFieldInit},eo.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e];if(t.inClassFieldInit||t.flags&Qa)return!1;if(2&t.flags)return(4&t.flags)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},eo.allowSuper.get=function(){var e=this.currentThisScope(),t=e.flags,s=e.inClassFieldInit;return(64&t)>0||s||this.options.allowSuperOutsideMethod},eo.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},eo.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},eo.allowNewDotTarget.get=function(){var e=this.currentThisScope(),t=e.flags,s=e.inClassFieldInit;return(258&t)>0||s},eo.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&Qa)>0},Ja.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var s=this,i=0;i<e.length;i++)s=e[i](s);return s},Ja.parse=function(e,t){return new this(t,e).parse()},Ja.parseExpressionAt=function(e,t,s){var i=new this(s,e,t);return i.nextToken(),i.parseExpression()},Ja.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(Ja.prototype,eo);var to=Ja.prototype,so=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;to.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){La.lastIndex=e,e+=La.exec(this.input)[0].length;var t=so.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){La.lastIndex=e+t[0].length;var s=La.exec(this.input),i=s.index+s[0].length,n=this.input.charAt(i);return";"===n||"}"===n||$a.test(s[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(n)||"!"===n&&"="===this.input.charAt(i+1))}e+=t[0].length,La.lastIndex=e,e+=La.exec(this.input)[0].length,";"===this.input[e]&&e++}},to.eat=function(e){return this.type===e&&(this.next(),!0)},to.isContextual=function(e){return this.type===_a.name&&this.value===e&&!this.containsEsc},to.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},to.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},to.canInsertSemicolon=function(){return this.type===_a.eof||this.type===_a.braceR||$a.test(this.input.slice(this.lastTokEnd,this.start))},to.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},to.semicolon=function(){this.eat(_a.semi)||this.insertSemicolon()||this.unexpected()},to.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},to.expect=function(e){this.eat(e)||this.unexpected()},to.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")};var io=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};to.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var s=t?e.parenthesizedAssign:e.parenthesizedBind;s>-1&&this.raiseRecoverable(s,"Parenthesized pattern")}},to.checkExpressionErrors=function(e,t){if(!e)return!1;var s=e.shorthandAssign,i=e.doubleProto;if(!t)return s>=0||i>=0;s>=0&&this.raise(s,"Shorthand property assignments are valid only in destructuring patterns"),i>=0&&this.raiseRecoverable(i,"Redefinition of __proto__ property")},to.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},to.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var no=Ja.prototype;no.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==_a.eof;){var s=this.parseStatement(null,!0,t);e.body.push(s)}if(this.inModule)for(var i=0,n=Object.keys(this.undefinedExports);i<n.length;i+=1){var r=n[i];this.raiseRecoverable(this.undefinedExports[r].start,"Export '"+r+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var ro={kind:"loop"},ao={kind:"switch"};no.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;La.lastIndex=this.pos;var t=La.exec(this.input),s=this.pos+t[0].length,i=this.input.charCodeAt(s);if(91===i||92===i||i>55295&&i<56320)return!0;if(e)return!1;if(123===i)return!0;if(Aa(i,!0)){for(var n=s+1;Sa(i=this.input.charCodeAt(n),!0);)++n;if(92===i||i>55295&&i<56320)return!0;var r=this.input.slice(s,n);if(!Ea.test(r))return!0}return!1},no.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;La.lastIndex=this.pos;var e,t=La.exec(this.input),s=this.pos+t[0].length;return!($a.test(this.input.slice(this.pos,s))||"function"!==this.input.slice(s,s+8)||s+8!==this.input.length&&(Sa(e=this.input.charCodeAt(s+8))||e>55295&&e<56320))},no.parseStatement=function(e,t,s){var i,n=this.type,r=this.startNode();switch(this.isLet(e)&&(n=_a._var,i="let"),n){case _a._break:case _a._continue:return this.parseBreakContinueStatement(r,n.keyword);case _a._debugger:return this.parseDebuggerStatement(r);case _a._do:return this.parseDoStatement(r);case _a._for:return this.parseForStatement(r);case _a._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(r,!1,!e);case _a._class:return e&&this.unexpected(),this.parseClass(r,!0);case _a._if:return this.parseIfStatement(r);case _a._return:return this.parseReturnStatement(r);case _a._switch:return this.parseSwitchStatement(r);case _a._throw:return this.parseThrowStatement(r);case _a._try:return this.parseTryStatement(r);case _a._const:case _a._var:return i=i||this.value,e&&"var"!==i&&this.unexpected(),this.parseVarStatement(r,i);case _a._while:return this.parseWhileStatement(r);case _a._with:return this.parseWithStatement(r);case _a.braceL:return this.parseBlock(!0,r);case _a.semi:return this.parseEmptyStatement(r);case _a._export:case _a._import:if(this.options.ecmaVersion>10&&n===_a._import){La.lastIndex=this.pos;var a=La.exec(this.input),o=this.pos+a[0].length,h=this.input.charCodeAt(o);if(40===h||46===h)return this.parseExpressionStatement(r,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),n===_a._import?this.parseImport(r):this.parseExport(r,s);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(r,!0,!e);var l=this.value,c=this.parseExpression();return n===_a.name&&"Identifier"===c.type&&this.eat(_a.colon)?this.parseLabeledStatement(r,l,c,e):this.parseExpressionStatement(r,c)}},no.parseBreakContinueStatement=function(e,t){var s="break"===t;this.next(),this.eat(_a.semi)||this.insertSemicolon()?e.label=null:this.type!==_a.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var i=0;i<this.labels.length;++i){var n=this.labels[i];if(null==e.label||n.name===e.label.name){if(null!=n.kind&&(s||"loop"===n.kind))break;if(e.label&&s)break}}return i===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,s?"BreakStatement":"ContinueStatement")},no.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},no.parseDoStatement=function(e){return this.next(),this.labels.push(ro),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(_a._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(_a.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},no.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(ro),this.enterScope(0),this.expect(_a.parenL),this.type===_a.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var s=this.isLet();if(this.type===_a._var||this.type===_a._const||s){var i=this.startNode(),n=s?"let":this.value;return this.next(),this.parseVar(i,!0,n),this.finishNode(i,"VariableDeclaration"),(this.type===_a._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===i.declarations.length?(this.options.ecmaVersion>=9&&(this.type===_a._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,i)):(t>-1&&this.unexpected(t),this.parseFor(e,i))}var r=this.isContextual("let"),a=!1,o=new io,h=this.parseExpression(!(t>-1)||"await",o);return this.type===_a._in||(a=this.options.ecmaVersion>=6&&this.isContextual("of"))?(this.options.ecmaVersion>=9&&(this.type===_a._in?t>-1&&this.unexpected(t):e.await=t>-1),r&&a&&this.raise(h.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(h,!1,o),this.checkLValPattern(h),this.parseForIn(e,h)):(this.checkExpressionErrors(o,!0),t>-1&&this.unexpected(t),this.parseFor(e,h))},no.parseFunctionStatement=function(e,t,s){return this.next(),this.parseFunction(e,ho|(s?0:lo),!1,t)},no.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(_a._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},no.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(_a.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},no.parseSwitchStatement=function(e){var t;this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(_a.braceL),this.labels.push(ao),this.enterScope(0);for(var s=!1;this.type!==_a.braceR;)if(this.type===_a._case||this.type===_a._default){var i=this.type===_a._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),i?t.test=this.parseExpression():(s&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),s=!0,t.test=null),this.expect(_a.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},no.parseThrowStatement=function(e){return this.next(),$a.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var oo=[];no.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===_a._catch){var t=this.startNode();if(this.next(),this.eat(_a.parenL)){t.param=this.parseBindingAtom();var s="Identifier"===t.param.type;this.enterScope(s?32:0),this.checkLValPattern(t.param,s?4:2),this.expect(_a.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(_a._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},no.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},no.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(ro),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},no.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},no.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},no.parseLabeledStatement=function(e,t,s,i){for(var n=0,r=this.labels;n<r.length;n+=1)r[n].name===t&&this.raise(s.start,"Label '"+t+"' is already declared");for(var a=this.type.isLoop?"loop":this.type===_a._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var h=this.labels[o];if(h.statementStart!==e.start)break;h.statementStart=this.start,h.kind=a}return this.labels.push({name:t,kind:a,statementStart:this.start}),e.body=this.parseStatement(i?-1===i.indexOf("label")?i+"label":i:"label"),this.labels.pop(),e.label=s,this.finishNode(e,"LabeledStatement")},no.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},no.parseBlock=function(e,t,s){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(_a.braceL),e&&this.enterScope(0);this.type!==_a.braceR;){var i=this.parseStatement(null);t.body.push(i)}return s&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},no.parseFor=function(e,t){return e.init=t,this.expect(_a.semi),e.test=this.type===_a.semi?null:this.parseExpression(),this.expect(_a.semi),e.update=this.type===_a.parenR?null:this.parseExpression(),this.expect(_a.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},no.parseForIn=function(e,t){var s=this.type===_a._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!s||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)&&this.raise(t.start,(s?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=s?this.parseExpression():this.parseMaybeAssign(),this.expect(_a.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,s?"ForInStatement":"ForOfStatement")},no.parseVar=function(e,t,s){for(e.declarations=[],e.kind=s;;){var i=this.startNode();if(this.parseVarId(i,s),this.eat(_a.eq)?i.init=this.parseMaybeAssign(t):"const"!==s||this.type===_a._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===i.id.type||t&&(this.type===_a._in||this.isContextual("of"))?i.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),e.declarations.push(this.finishNode(i,"VariableDeclarator")),!this.eat(_a.comma))break}return e},no.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLValPattern(e.id,"var"===t?1:2,!1)};var ho=1,lo=2;function co(e,t){var s=t.key.name,i=e[s],n="true";return"MethodDefinition"!==t.type||"get"!==t.kind&&"set"!==t.kind||(n=(t.static?"s":"i")+t.kind),"iget"===i&&"iset"===n||"iset"===i&&"iget"===n||"sget"===i&&"sset"===n||"sset"===i&&"sget"===n?(e[s]="true",!1):!!i||(e[s]=n,!1)}function uo(e,t){var s=e.computed,i=e.key;return!s&&("Identifier"===i.type&&i.name===t||"Literal"===i.type&&i.value===t)}no.parseFunction=function(e,t,s,i,n){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!i)&&(this.type===_a.star&&t&lo&&this.unexpected(),e.generator=this.eat(_a.star)),this.options.ecmaVersion>=8&&(e.async=!!i),t&ho&&(e.id=4&t&&this.type!==_a.name?null:this.parseIdent(),!e.id||t&lo||this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?1:2:3));var r=this.yieldPos,a=this.awaitPos,o=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(Za(e.async,e.generator)),t&ho||(e.id=this.type===_a.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,s,!1,n),this.yieldPos=r,this.awaitPos=a,this.awaitIdentPos=o,this.finishNode(e,t&ho?"FunctionDeclaration":"FunctionExpression")},no.parseFunctionParams=function(e){this.expect(_a.parenL),e.params=this.parseBindingList(_a.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},no.parseClass=function(e,t){this.next();var s=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var i=this.enterClassBody(),n=this.startNode(),r=!1;for(n.body=[],this.expect(_a.braceL);this.type!==_a.braceR;){var a=this.parseClassElement(null!==e.superClass);a&&(n.body.push(a),"MethodDefinition"===a.type&&"constructor"===a.kind?(r&&this.raise(a.start,"Duplicate constructor in the same class"),r=!0):a.key&&"PrivateIdentifier"===a.key.type&&co(i,a)&&this.raiseRecoverable(a.key.start,"Identifier '#"+a.key.name+"' has already been declared"))}return this.strict=s,this.next(),e.body=this.finishNode(n,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},no.parseClassElement=function(e){if(this.eat(_a.semi))return null;var t=this.options.ecmaVersion,s=this.startNode(),i="",n=!1,r=!1,a="method",o=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(_a.braceL))return this.parseClassStaticBlock(s),s;this.isClassElementNameStart()||this.type===_a.star?o=!0:i="static"}if(s.static=o,!i&&t>=8&&this.eatContextual("async")&&(!this.isClassElementNameStart()&&this.type!==_a.star||this.canInsertSemicolon()?i="async":r=!0),!i&&(t>=9||!r)&&this.eat(_a.star)&&(n=!0),!i&&!r&&!n){var h=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?a=h:i=h)}if(i?(s.computed=!1,s.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),s.key.name=i,this.finishNode(s.key,"Identifier")):this.parseClassElementName(s),t<13||this.type===_a.parenL||"method"!==a||n||r){var l=!s.static&&uo(s,"constructor"),c=l&&e;l&&"method"!==a&&this.raise(s.key.start,"Constructor can't have get/set modifier"),s.kind=l?"constructor":a,this.parseClassMethod(s,n,r,c)}else this.parseClassField(s);return s},no.isClassElementNameStart=function(){return this.type===_a.name||this.type===_a.privateId||this.type===_a.num||this.type===_a.string||this.type===_a.bracketL||this.type.keyword},no.parseClassElementName=function(e){this.type===_a.privateId?("constructor"===this.value&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},no.parseClassMethod=function(e,t,s,i){var n=e.key;"constructor"===e.kind?(t&&this.raise(n.start,"Constructor can't be a generator"),s&&this.raise(n.start,"Constructor can't be an async method")):e.static&&uo(e,"prototype")&&this.raise(n.start,"Classes may not have a static property named prototype");var r=e.value=this.parseMethod(t,s,i);return"get"===e.kind&&0!==r.params.length&&this.raiseRecoverable(r.start,"getter should have no params"),"set"===e.kind&&1!==r.params.length&&this.raiseRecoverable(r.start,"setter should have exactly one param"),"set"===e.kind&&"RestElement"===r.params[0].type&&this.raiseRecoverable(r.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},no.parseClassField=function(e){if(uo(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&uo(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(_a.eq)){var t=this.currentThisScope(),s=t.inClassFieldInit;t.inClassFieldInit=!0,e.value=this.parseMaybeAssign(),t.inClassFieldInit=s}else e.value=null;return this.semicolon(),this.finishNode(e,"PropertyDefinition")},no.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(320);this.type!==_a.braceR;){var s=this.parseStatement(null);e.body.push(s)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},no.parseClassId=function(e,t){this.type===_a.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,2,!1)):(!0===t&&this.unexpected(),e.id=null)},no.parseClassSuper=function(e){e.superClass=this.eat(_a._extends)?this.parseExprSubscripts(!1):null},no.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},no.exitClassBody=function(){for(var e=this.privateNameStack.pop(),t=e.declared,s=e.used,i=this.privateNameStack.length,n=0===i?null:this.privateNameStack[i-1],r=0;r<s.length;++r){var a=s[r];Fa(t,a.name)||(n?n.used.push(a):this.raiseRecoverable(a.start,"Private field '#"+a.name+"' must be declared in an enclosing class"))}},no.parseExport=function(e,t){if(this.next(),this.eat(_a.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==_a.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(_a._default)){var s;if(this.checkExport(t,"default",this.lastTokStart),this.type===_a._function||(s=this.isAsyncFunction())){var i=this.startNode();this.next(),s&&this.next(),e.declaration=this.parseFunction(i,4|ho,!1,s)}else if(this.type===_a._class){var n=this.startNode();e.declaration=this.parseClass(n,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==_a.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var r=0,a=e.specifiers;r<a.length;r+=1){var o=a[r];this.checkUnreserved(o.local),this.checkLocalExport(o.local),"Literal"===o.local.type&&this.raise(o.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},no.checkExport=function(e,t,s){e&&("string"!=typeof t&&(t="Identifier"===t.type?t.name:t.value),Fa(e,t)&&this.raiseRecoverable(s,"Duplicate export '"+t+"'"),e[t]=!0)},no.checkPatternExport=function(e,t){var s=t.type;if("Identifier"===s)this.checkExport(e,t,t.start);else if("ObjectPattern"===s)for(var i=0,n=t.properties;i<n.length;i+=1){var r=n[i];this.checkPatternExport(e,r)}else if("ArrayPattern"===s)for(var a=0,o=t.elements;a<o.length;a+=1){var h=o[a];h&&this.checkPatternExport(e,h)}else"Property"===s?this.checkPatternExport(e,t.value):"AssignmentPattern"===s?this.checkPatternExport(e,t.left):"RestElement"===s?this.checkPatternExport(e,t.argument):"ParenthesizedExpression"===s&&this.checkPatternExport(e,t.expression)},no.checkVariableExport=function(e,t){if(e)for(var s=0,i=t;s<i.length;s+=1){var n=i[s];this.checkPatternExport(e,n.id)}},no.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},no.parseExportSpecifiers=function(e){var t=[],s=!0;for(this.expect(_a.braceL);!this.eat(_a.braceR);){if(s)s=!1;else if(this.expect(_a.comma),this.afterTrailingComma(_a.braceR))break;var i=this.startNode();i.local=this.parseModuleExportName(),i.exported=this.eatContextual("as")?this.parseModuleExportName():i.local,this.checkExport(e,i.exported,i.exported.start),t.push(this.finishNode(i,"ExportSpecifier"))}return t},no.parseImport=function(e){return this.next(),this.type===_a.string?(e.specifiers=oo,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===_a.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},no.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===_a.name){var s=this.startNode();if(s.local=this.parseIdent(),this.checkLValSimple(s.local,2),e.push(this.finishNode(s,"ImportDefaultSpecifier")),!this.eat(_a.comma))return e}if(this.type===_a.star){var i=this.startNode();return this.next(),this.expectContextual("as"),i.local=this.parseIdent(),this.checkLValSimple(i.local,2),e.push(this.finishNode(i,"ImportNamespaceSpecifier")),e}for(this.expect(_a.braceL);!this.eat(_a.braceR);){if(t)t=!1;else if(this.expect(_a.comma),this.afterTrailingComma(_a.braceR))break;var n=this.startNode();n.imported=this.parseModuleExportName(),this.eatContextual("as")?n.local=this.parseIdent():(this.checkUnreserved(n.imported),n.local=n.imported),this.checkLValSimple(n.local,2),e.push(this.finishNode(n,"ImportSpecifier"))}return e},no.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===_a.string){var e=this.parseLiteral(this.value);return Ua.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},no.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},no.isDirectiveCandidate=function(e){return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var po=Ja.prototype;po.toAssignable=function(e,t,s){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",s&&this.checkPatternErrors(s,!0);for(var i=0,n=e.properties;i<n.length;i+=1){var r=n[i];this.toAssignable(r,t),"RestElement"!==r.type||"ArrayPattern"!==r.argument.type&&"ObjectPattern"!==r.argument.type||this.raise(r.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",s&&this.checkPatternErrors(s,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,s);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else s&&this.checkPatternErrors(s,!0);return e},po.toAssignableList=function(e,t){for(var s=e.length,i=0;i<s;i++){var n=e[i];n&&this.toAssignable(n,t)}if(s){var r=e[s-1];6===this.options.ecmaVersion&&t&&r&&"RestElement"===r.type&&"Identifier"!==r.argument.type&&this.unexpected(r.argument.start)}return e},po.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},po.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==_a.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},po.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case _a.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(_a.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case _a.braceL:return this.parseObj(!0)}return this.parseIdent()},po.parseBindingList=function(e,t,s){for(var i=[],n=!0;!this.eat(e);)if(n?n=!1:this.expect(_a.comma),t&&this.type===_a.comma)i.push(null);else{if(s&&this.afterTrailingComma(e))break;if(this.type===_a.ellipsis){var r=this.parseRestBinding();this.parseBindingListItem(r),i.push(r),this.type===_a.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a),i.push(a)}return i},po.parseBindingListItem=function(e){return e},po.parseMaybeDefault=function(e,t,s){if(s=s||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(_a.eq))return s;var i=this.startNodeAt(e,t);return i.left=s,i.right=this.parseMaybeAssign(),this.finishNode(i,"AssignmentPattern")},po.checkLValSimple=function(e,t,s){void 0===t&&(t=0);var i=0!==t;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(i?"Binding ":"Assigning to ")+e.name+" in strict mode"),i&&(2===t&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),s&&(Fa(s,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),s[e.name]=!0),5!==t&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":i&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return i&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,s);default:this.raise(e.start,(i?"Binding":"Assigning to")+" rvalue")}},po.checkLValPattern=function(e,t,s){switch(void 0===t&&(t=0),e.type){case"ObjectPattern":for(var i=0,n=e.properties;i<n.length;i+=1){var r=n[i];this.checkLValInnerPattern(r,t,s)}break;case"ArrayPattern":for(var a=0,o=e.elements;a<o.length;a+=1){var h=o[a];h&&this.checkLValInnerPattern(h,t,s)}break;default:this.checkLValSimple(e,t,s)}},po.checkLValInnerPattern=function(e,t,s){switch(void 0===t&&(t=0),e.type){case"Property":this.checkLValInnerPattern(e.value,t,s);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,s);break;case"RestElement":this.checkLValPattern(e.argument,t,s);break;default:this.checkLValPattern(e,t,s)}};var fo=function(e,t,s,i,n){this.token=e,this.isExpr=!!t,this.preserveSpace=!!s,this.override=i,this.generator=!!n},mo={b_stat:new fo("{",!1),b_expr:new fo("{",!0),b_tmpl:new fo("${",!1),p_stat:new fo("(",!1),p_expr:new fo("(",!0),q_tmpl:new fo("`",!0,!0,(function(e){return e.tryReadTemplateToken()})),f_stat:new fo("function",!1),f_expr:new fo("function",!0),f_expr_gen:new fo("function",!0,!1,null,!0),f_gen:new fo("function",!1,!1,null,!0)},go=Ja.prototype;go.initialContext=function(){return[mo.b_stat]},go.curContext=function(){return this.context[this.context.length-1]},go.braceIsBlock=function(e){var t=this.curContext();return t===mo.f_expr||t===mo.f_stat||(e!==_a.colon||t!==mo.b_stat&&t!==mo.b_expr?e===_a._return||e===_a.name&&this.exprAllowed?$a.test(this.input.slice(this.lastTokEnd,this.start)):e===_a._else||e===_a.semi||e===_a.eof||e===_a.parenR||e===_a.arrow||(e===_a.braceL?t===mo.b_stat:e!==_a._var&&e!==_a._const&&e!==_a.name&&!this.exprAllowed):!t.isExpr)},go.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},go.updateContext=function(e){var t,s=this.type;s.keyword&&e===_a.dot?this.exprAllowed=!1:(t=s.updateContext)?t.call(this,e):this.exprAllowed=s.beforeExpr},go.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},_a.parenR.updateContext=_a.braceR.updateContext=function(){if(1!==this.context.length){var e=this.context.pop();e===mo.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},_a.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?mo.b_stat:mo.b_expr),this.exprAllowed=!0},_a.dollarBraceL.updateContext=function(){this.context.push(mo.b_tmpl),this.exprAllowed=!0},_a.parenL.updateContext=function(e){var t=e===_a._if||e===_a._for||e===_a._with||e===_a._while;this.context.push(t?mo.p_stat:mo.p_expr),this.exprAllowed=!0},_a.incDec.updateContext=function(){},_a._function.updateContext=_a._class.updateContext=function(e){!e.beforeExpr||e===_a._else||e===_a.semi&&this.curContext()!==mo.p_stat||e===_a._return&&$a.test(this.input.slice(this.lastTokEnd,this.start))||(e===_a.colon||e===_a.braceL)&&this.curContext()===mo.b_stat?this.context.push(mo.f_stat):this.context.push(mo.f_expr),this.exprAllowed=!1},_a.backQuote.updateContext=function(){this.curContext()===mo.q_tmpl?this.context.pop():this.context.push(mo.q_tmpl),this.exprAllowed=!1},_a.star.updateContext=function(e){if(e===_a._function){var t=this.context.length-1;this.context[t]===mo.f_expr?this.context[t]=mo.f_expr_gen:this.context[t]=mo.f_gen}this.exprAllowed=!0},_a.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==_a.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var yo=Ja.prototype;function Eo(e){return"MemberExpression"===e.type&&"PrivateIdentifier"===e.property.type||"ChainExpression"===e.type&&Eo(e.expression)}yo.checkPropClash=function(e,t,s){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===e.type||this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var i,n=e.key;switch(n.type){case"Identifier":i=n.name;break;case"Literal":i=String(n.value);break;default:return}var r=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===i&&"init"===r&&(t.proto&&(s?s.doubleProto<0&&(s.doubleProto=n.start):this.raiseRecoverable(n.start,"Redefinition of __proto__ property")),t.proto=!0);else{var a=t[i="$"+i];a?("init"===r?this.strict&&a.init||a.get||a.set:a.init||a[r])&&this.raiseRecoverable(n.start,"Redefinition of property"):a=t[i]={init:!1,get:!1,set:!1},a[r]=!0}}},yo.parseExpression=function(e,t){var s=this.start,i=this.startLoc,n=this.parseMaybeAssign(e,t);if(this.type===_a.comma){var r=this.startNodeAt(s,i);for(r.expressions=[n];this.eat(_a.comma);)r.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(r,"SequenceExpression")}return n},yo.parseMaybeAssign=function(e,t,s){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var i=!1,n=-1,r=-1,a=-1;t?(n=t.parenthesizedAssign,r=t.trailingComma,a=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new io,i=!0);var o=this.start,h=this.startLoc;this.type!==_a.parenL&&this.type!==_a.name||(this.potentialArrowAt=this.start,this.potentialArrowInForAwait="await"===e);var l=this.parseMaybeConditional(e,t);if(s&&(l=s.call(this,l,o,h)),this.type.isAssign){var c=this.startNodeAt(o,h);return c.operator=this.value,this.type===_a.eq&&(l=this.toAssignable(l,!1,t)),i||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=l.start&&(t.shorthandAssign=-1),this.type===_a.eq?this.checkLValPattern(l):this.checkLValSimple(l),c.left=l,this.next(),c.right=this.parseMaybeAssign(e),a>-1&&(t.doubleProto=a),this.finishNode(c,"AssignmentExpression")}return i&&this.checkExpressionErrors(t,!0),n>-1&&(t.parenthesizedAssign=n),r>-1&&(t.trailingComma=r),l},yo.parseMaybeConditional=function(e,t){var s=this.start,i=this.startLoc,n=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return n;if(this.eat(_a.question)){var r=this.startNodeAt(s,i);return r.test=n,r.consequent=this.parseMaybeAssign(),this.expect(_a.colon),r.alternate=this.parseMaybeAssign(e),this.finishNode(r,"ConditionalExpression")}return n},yo.parseExprOps=function(e,t){var s=this.start,i=this.startLoc,n=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||n.start===s&&"ArrowFunctionExpression"===n.type?n:this.parseExprOp(n,s,i,-1,e)},yo.parseExprOp=function(e,t,s,i,n){var r=this.type.binop;if(null!=r&&(!n||this.type!==_a._in)&&r>i){var a=this.type===_a.logicalOR||this.type===_a.logicalAND,o=this.type===_a.coalesce;o&&(r=_a.logicalAND.binop);var h=this.value;this.next();var l=this.start,c=this.startLoc,u=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,n),l,c,r,n),d=this.buildBinary(t,s,e,u,h,a||o);return(a&&this.type===_a.coalesce||o&&(this.type===_a.logicalOR||this.type===_a.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(d,t,s,i,n)}return e},yo.buildBinary=function(e,t,s,i,n,r){"PrivateIdentifier"===i.type&&this.raise(i.start,"Private identifier can only be left side of binary expression");var a=this.startNodeAt(e,t);return a.left=s,a.operator=n,a.right=i,this.finishNode(a,r?"LogicalExpression":"BinaryExpression")},yo.parseMaybeUnary=function(e,t,s,i){var n,r=this.start,a=this.startLoc;if(this.isContextual("await")&&this.canAwait)n=this.parseAwait(i),t=!0;else if(this.type.prefix){var o=this.startNode(),h=this.type===_a.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0,h,i),this.checkExpressionErrors(e,!0),h?this.checkLValSimple(o.argument):this.strict&&"delete"===o.operator&&"Identifier"===o.argument.type?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):"delete"===o.operator&&Eo(o.argument)?this.raiseRecoverable(o.start,"Private fields can not be deleted"):t=!0,n=this.finishNode(o,h?"UpdateExpression":"UnaryExpression")}else if(t||this.type!==_a.privateId){if(n=this.parseExprSubscripts(e,i),this.checkExpressionErrors(e))return n;for(;this.type.postfix&&!this.canInsertSemicolon();){var l=this.startNodeAt(r,a);l.operator=this.value,l.prefix=!1,l.argument=n,this.checkLValSimple(n),this.next(),n=this.finishNode(l,"UpdateExpression")}}else(i||0===this.privateNameStack.length)&&this.unexpected(),n=this.parsePrivateIdent(),this.type!==_a._in&&this.unexpected();return s||!this.eat(_a.starstar)?n:t?void this.unexpected(this.lastTokStart):this.buildBinary(r,a,n,this.parseMaybeUnary(null,!1,!1,i),"**",!1)},yo.parseExprSubscripts=function(e,t){var s=this.start,i=this.startLoc,n=this.parseExprAtom(e,t);if("ArrowFunctionExpression"===n.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return n;var r=this.parseSubscripts(n,s,i,!1,t);return e&&"MemberExpression"===r.type&&(e.parenthesizedAssign>=r.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=r.start&&(e.parenthesizedBind=-1),e.trailingComma>=r.start&&(e.trailingComma=-1)),r},yo.parseSubscripts=function(e,t,s,i,n){for(var r=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&this.potentialArrowAt===e.start,a=!1;;){var o=this.parseSubscript(e,t,s,i,r,a,n);if(o.optional&&(a=!0),o===e||"ArrowFunctionExpression"===o.type){if(a){var h=this.startNodeAt(t,s);h.expression=o,o=this.finishNode(h,"ChainExpression")}return o}e=o}},yo.parseSubscript=function(e,t,s,i,n,r,a){var o=this.options.ecmaVersion>=11,h=o&&this.eat(_a.questionDot);i&&h&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var l=this.eat(_a.bracketL);if(l||h&&this.type!==_a.parenL&&this.type!==_a.backQuote||this.eat(_a.dot)){var c=this.startNodeAt(t,s);c.object=e,l?(c.property=this.parseExpression(),this.expect(_a.bracketR)):this.type===_a.privateId&&"Super"!==e.type?c.property=this.parsePrivateIdent():c.property=this.parseIdent("never"!==this.options.allowReserved),c.computed=!!l,o&&(c.optional=h),e=this.finishNode(c,"MemberExpression")}else if(!i&&this.eat(_a.parenL)){var u=new io,d=this.yieldPos,p=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var m=this.parseExprList(_a.parenR,this.options.ecmaVersion>=8,!1,u);if(n&&!h&&!this.canInsertSemicolon()&&this.eat(_a.arrow))return this.checkPatternErrors(u,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=d,this.awaitPos=p,this.awaitIdentPos=f,this.parseArrowExpression(this.startNodeAt(t,s),m,!0,a);this.checkExpressionErrors(u,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=p||this.awaitPos,this.awaitIdentPos=f||this.awaitIdentPos;var g=this.startNodeAt(t,s);g.callee=e,g.arguments=m,o&&(g.optional=h),e=this.finishNode(g,"CallExpression")}else if(this.type===_a.backQuote){(h||r)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var y=this.startNodeAt(t,s);y.tag=e,y.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(y,"TaggedTemplateExpression")}return e},yo.parseExprAtom=function(e,t){this.type===_a.slash&&this.readRegexp();var s,i=this.potentialArrowAt===this.start;switch(this.type){case _a._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),s=this.startNode(),this.next(),this.type!==_a.parenL||this.allowDirectSuper||this.raise(s.start,"super() call outside constructor of a subclass"),this.type!==_a.dot&&this.type!==_a.bracketL&&this.type!==_a.parenL&&this.unexpected(),this.finishNode(s,"Super");case _a._this:return s=this.startNode(),this.next(),this.finishNode(s,"ThisExpression");case _a.name:var n=this.start,r=this.startLoc,a=this.containsEsc,o=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!a&&"async"===o.name&&!this.canInsertSemicolon()&&this.eat(_a._function))return this.overrideContext(mo.f_expr),this.parseFunction(this.startNodeAt(n,r),0,!1,!0,t);if(i&&!this.canInsertSemicolon()){if(this.eat(_a.arrow))return this.parseArrowExpression(this.startNodeAt(n,r),[o],!1,t);if(this.options.ecmaVersion>=8&&"async"===o.name&&this.type===_a.name&&!a&&(!this.potentialArrowInForAwait||"of"!==this.value||this.containsEsc))return o=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(_a.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(n,r),[o],!0,t)}return o;case _a.regexp:var h=this.value;return(s=this.parseLiteral(h.value)).regex={pattern:h.pattern,flags:h.flags},s;case _a.num:case _a.string:return this.parseLiteral(this.value);case _a._null:case _a._true:case _a._false:return(s=this.startNode()).value=this.type===_a._null?null:this.type===_a._true,s.raw=this.type.keyword,this.next(),this.finishNode(s,"Literal");case _a.parenL:var l=this.start,c=this.parseParenAndDistinguishExpression(i,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(c)&&(e.parenthesizedAssign=l),e.parenthesizedBind<0&&(e.parenthesizedBind=l)),c;case _a.bracketL:return s=this.startNode(),this.next(),s.elements=this.parseExprList(_a.bracketR,!0,!0,e),this.finishNode(s,"ArrayExpression");case _a.braceL:return this.overrideContext(mo.b_expr),this.parseObj(!1,e);case _a._function:return s=this.startNode(),this.next(),this.parseFunction(s,0);case _a._class:return this.parseClass(this.startNode(),!1);case _a._new:return this.parseNew();case _a.backQuote:return this.parseTemplate();case _a._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},yo.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case _a.parenL:return this.parseDynamicImport(e);case _a.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},yo.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(_a.parenR)){var t=this.start;this.eat(_a.comma)&&this.eat(_a.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},yo.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"===this.options.sourceType||this.options.allowImportExportEverywhere||this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},yo.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},yo.parseParenExpression=function(){this.expect(_a.parenL);var e=this.parseExpression();return this.expect(_a.parenR),e},yo.parseParenAndDistinguishExpression=function(e,t){var s,i=this.start,n=this.startLoc,r=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var a,o=this.start,h=this.startLoc,l=[],c=!0,u=!1,d=new io,p=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==_a.parenR;){if(c?c=!1:this.expect(_a.comma),r&&this.afterTrailingComma(_a.parenR,!0)){u=!0;break}if(this.type===_a.ellipsis){a=this.start,l.push(this.parseParenItem(this.parseRestBinding())),this.type===_a.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}l.push(this.parseMaybeAssign(!1,d,this.parseParenItem))}var m=this.lastTokEnd,g=this.lastTokEndLoc;if(this.expect(_a.parenR),e&&!this.canInsertSemicolon()&&this.eat(_a.arrow))return this.checkPatternErrors(d,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=p,this.awaitPos=f,this.parseParenArrowList(i,n,l,t);l.length&&!u||this.unexpected(this.lastTokStart),a&&this.unexpected(a),this.checkExpressionErrors(d,!0),this.yieldPos=p||this.yieldPos,this.awaitPos=f||this.awaitPos,l.length>1?((s=this.startNodeAt(o,h)).expressions=l,this.finishNodeAt(s,"SequenceExpression",m,g)):s=l[0]}else s=this.parseParenExpression();if(this.options.preserveParens){var y=this.startNodeAt(i,n);return y.expression=s,this.finishNode(y,"ParenthesizedExpression")}return s},yo.parseParenItem=function(e){return e},yo.parseParenArrowList=function(e,t,s,i){return this.parseArrowExpression(this.startNodeAt(e,t),s,!1,i)};var xo=[];yo.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(_a.dot)){e.meta=t;var s=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),s&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var i=this.start,n=this.startLoc,r=this.type===_a._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),i,n,!0,!1),r&&"ImportExpression"===e.callee.type&&this.raise(i,"Cannot use new with import()"),this.eat(_a.parenL)?e.arguments=this.parseExprList(_a.parenR,this.options.ecmaVersion>=8,!1):e.arguments=xo,this.finishNode(e,"NewExpression")},yo.parseTemplateElement=function(e){var t=e.isTagged,s=this.startNode();return this.type===_a.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),s.value={raw:this.value,cooked:null}):s.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),s.tail=this.type===_a.backQuote,this.finishNode(s,"TemplateElement")},yo.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var s=this.startNode();this.next(),s.expressions=[];var i=this.parseTemplateElement({isTagged:t});for(s.quasis=[i];!i.tail;)this.type===_a.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(_a.dollarBraceL),s.expressions.push(this.parseExpression()),this.expect(_a.braceR),s.quasis.push(i=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(s,"TemplateLiteral")},yo.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===_a.name||this.type===_a.num||this.type===_a.string||this.type===_a.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===_a.star)&&!$a.test(this.input.slice(this.lastTokEnd,this.start))},yo.parseObj=function(e,t){var s=this.startNode(),i=!0,n={};for(s.properties=[],this.next();!this.eat(_a.braceR);){if(i)i=!1;else if(this.expect(_a.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(_a.braceR))break;var r=this.parseProperty(e,t);e||this.checkPropClash(r,n,t),s.properties.push(r)}return this.finishNode(s,e?"ObjectPattern":"ObjectExpression")},yo.parseProperty=function(e,t){var s,i,n,r,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(_a.ellipsis))return e?(a.argument=this.parseIdent(!1),this.type===_a.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(this.type===_a.parenL&&t&&(t.parenthesizedAssign<0&&(t.parenthesizedAssign=this.start),t.parenthesizedBind<0&&(t.parenthesizedBind=this.start)),a.argument=this.parseMaybeAssign(!1,t),this.type===_a.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(e||t)&&(n=this.start,r=this.startLoc),e||(s=this.eat(_a.star)));var o=this.containsEsc;return this.parsePropertyName(a),!e&&!o&&this.options.ecmaVersion>=8&&!s&&this.isAsyncProp(a)?(i=!0,s=this.options.ecmaVersion>=9&&this.eat(_a.star),this.parsePropertyName(a,t)):i=!1,this.parsePropertyValue(a,e,s,i,n,r,t,o),this.finishNode(a,"Property")},yo.parsePropertyValue=function(e,t,s,i,n,r,a,o){if((s||i)&&this.type===_a.colon&&this.unexpected(),this.eat(_a.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===_a.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(s,i);else if(t||o||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===_a.comma||this.type===_a.braceR||this.type===_a.eq)this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((s||i)&&this.unexpected(),this.checkUnreserved(e.key),"await"!==e.key.name||this.awaitIdentPos||(this.awaitIdentPos=n),e.kind="init",t?e.value=this.parseMaybeDefault(n,r,this.copyNode(e.key)):this.type===_a.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),e.value=this.parseMaybeDefault(n,r,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.shorthand=!0):this.unexpected();else{(s||i)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var h="get"===e.kind?0:1;if(e.value.params.length!==h){var l=e.value.start;"get"===e.kind?this.raiseRecoverable(l,"getter should have no params"):this.raiseRecoverable(l,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}},yo.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(_a.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(_a.bracketR),e.key;e.computed=!1}return e.key=this.type===_a.num||this.type===_a.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},yo.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},yo.parseMethod=function(e,t,s){var i=this.startNode(),n=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(i),this.options.ecmaVersion>=6&&(i.generator=e),this.options.ecmaVersion>=8&&(i.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|Za(t,i.generator)|(s?128:0)),this.expect(_a.parenL),i.params=this.parseBindingList(_a.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(i,!1,!0,!1),this.yieldPos=n,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(i,"FunctionExpression")},yo.parseArrowExpression=function(e,t,s,i){var n=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.enterScope(16|Za(s,!1)),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!s),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,i),this.yieldPos=n,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(e,"ArrowFunctionExpression")},yo.parseFunctionBody=function(e,t,s,i){var n=t&&this.type!==_a.braceL,r=this.strict,a=!1;if(n)e.body=this.parseMaybeAssign(i),e.expression=!0,this.checkParams(e,!1);else{var o=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);r&&!o||(a=this.strictDirective(this.end))&&o&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var h=this.labels;this.labels=[],a&&(this.strict=!0),this.checkParams(e,!r&&!a&&!t&&!s&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,5),e.body=this.parseBlock(!1,void 0,a&&!r),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=h}this.exitScope()},yo.isSimpleParamList=function(e){for(var t=0,s=e;t<s.length;t+=1)if("Identifier"!==s[t].type)return!1;return!0},yo.checkParams=function(e,t){for(var s=Object.create(null),i=0,n=e.params;i<n.length;i+=1){var r=n[i];this.checkLValInnerPattern(r,1,t?null:s)}},yo.parseExprList=function(e,t,s,i){for(var n=[],r=!0;!this.eat(e);){if(r)r=!1;else if(this.expect(_a.comma),t&&this.afterTrailingComma(e))break;var a=void 0;s&&this.type===_a.comma?a=null:this.type===_a.ellipsis?(a=this.parseSpread(i),i&&this.type===_a.comma&&i.trailingComma<0&&(i.trailingComma=this.start)):a=this.parseMaybeAssign(!1,i),n.push(a)}return n},yo.checkUnreserved=function(e){var t=e.start,s=e.end,i=e.name;this.inGenerator&&"yield"===i&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===i&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().inClassFieldInit&&"arguments"===i&&this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),!this.inClassStaticBlock||"arguments"!==i&&"await"!==i||this.raise(t,"Cannot use "+i+" in class static initialization block"),this.keywords.test(i)&&this.raise(t,"Unexpected keyword '"+i+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(t,s).indexOf("\\")||(this.strict?this.reservedWordsStrict:this.reservedWords).test(i)&&(this.inAsync||"await"!==i||this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+i+"' is reserved"))},yo.parseIdent=function(e,t){var s=this.startNode();return this.type===_a.name?s.name=this.value:this.type.keyword?(s.name=this.type.keyword,"class"!==s.name&&"function"!==s.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(s,"Identifier"),e||(this.checkUnreserved(s),"await"!==s.name||this.awaitIdentPos||(this.awaitIdentPos=s.start)),s},yo.parsePrivateIdent=function(){var e=this.startNode();return this.type===_a.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),0===this.privateNameStack.length?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e),e},yo.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===_a.semi||this.canInsertSemicolon()||this.type!==_a.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(_a.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},yo.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var bo=Ja.prototype;bo.raise=function(e,t){var s=qa(this.input,e);t+=" ("+s.line+":"+s.column+")";var i=new SyntaxError(t);throw i.pos=e,i.loc=s,i.raisedAt=this.pos,i},bo.raiseRecoverable=bo.raise,bo.curPosition=function(){if(this.options.locations)return new Ga(this.curLine,this.pos-this.lineStart)};var vo=Ja.prototype,Ao=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[],this.inClassFieldInit=!1};vo.enterScope=function(e){this.scopeStack.push(new Ao(e))},vo.exitScope=function(){this.scopeStack.pop()},vo.treatFunctionsAsVarInScope=function(e){return 2&e.flags||!this.inModule&&1&e.flags},vo.declareName=function(e,t,s){var i=!1;if(2===t){var n=this.currentScope();i=n.lexical.indexOf(e)>-1||n.functions.indexOf(e)>-1||n.var.indexOf(e)>-1,n.lexical.push(e),this.inModule&&1&n.flags&&delete this.undefinedExports[e]}else if(4===t)this.currentScope().lexical.push(e);else if(3===t){var r=this.currentScope();i=this.treatFunctionsAsVar?r.lexical.indexOf(e)>-1:r.lexical.indexOf(e)>-1||r.var.indexOf(e)>-1,r.functions.push(e)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(e)>-1&&!(32&o.flags&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){i=!0;break}if(o.var.push(e),this.inModule&&1&o.flags&&delete this.undefinedExports[e],259&o.flags)break}i&&this.raiseRecoverable(s,"Identifier '"+e+"' has already been declared")},vo.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},vo.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},vo.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(259&t.flags)return t}},vo.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(259&t.flags&&!(16&t.flags))return t}};var So=function(e,t,s){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new Ha(e,s)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},Po=Ja.prototype;function ko(e,t,s,i){return e.type=t,e.end=s,this.options.locations&&(e.loc.end=i),this.options.ranges&&(e.range[1]=s),e}Po.startNode=function(){return new So(this,this.start,this.startLoc)},Po.startNodeAt=function(e,t){return new So(this,e,t)},Po.finishNode=function(e,t){return ko.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},Po.finishNodeAt=function(e,t,s,i){return ko.call(this,e,t,s,i)},Po.copyNode=function(e){var t=new So(this,e.start,this.startLoc);for(var s in e)t[s]=e[s];return t};var wo="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",Co=wo+" Extended_Pictographic",Io=Co+" EBase EComp EMod EPres ExtPict",No={9:wo,10:Co,11:Co,12:Io,13:"ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS Extended_Pictographic EBase EComp EMod EPres ExtPict"},_o="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",$o="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",To=$o+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",Ro=To+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Mo=Ro+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",Do={9:$o,10:To,11:Ro,12:Mo,13:"Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith"},Lo={};function Oo(e){var t=Lo[e]={binary:Wa(No[e]+" "+_o),nonBinary:{General_Category:Wa(_o),Script:Wa(Do[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var Vo=0,Bo=[9,10,11,12,13];Vo<Bo.length;Vo+=1)Oo(Bo[Vo]);var Fo=Ja.prototype,zo=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"")+(e.options.ecmaVersion>=13?"d":""),this.unicodeProperties=Lo[e.options.ecmaVersion>=13?13:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};function Wo(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function jo(e){return e>=65&&e<=90||e>=97&&e<=122}function Uo(e){return jo(e)||95===e}function Go(e){return Uo(e)||Ho(e)}function Ho(e){return e>=48&&e<=57}function qo(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function Ko(e){return e>=65&&e<=70?e-65+10:e>=97&&e<=102?e-97+10:e-48}function Xo(e){return e>=48&&e<=55}zo.prototype.reset=function(e,t,s){var i=-1!==s.indexOf("u");this.start=0|e,this.source=t+"",this.flags=s,this.switchU=i&&this.parser.options.ecmaVersion>=6,this.switchN=i&&this.parser.options.ecmaVersion>=9},zo.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},zo.prototype.at=function(e,t){void 0===t&&(t=!1);var s=this.source,i=s.length;if(e>=i)return-1;var n=s.charCodeAt(e);if(!t&&!this.switchU||n<=55295||n>=57344||e+1>=i)return n;var r=s.charCodeAt(e+1);return r>=56320&&r<=57343?(n<<10)+r-56613888:n},zo.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var s=this.source,i=s.length;if(e>=i)return i;var n,r=s.charCodeAt(e);return!t&&!this.switchU||r<=55295||r>=57344||e+1>=i||(n=s.charCodeAt(e+1))<56320||n>57343?e+1:e+2},zo.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},zo.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},zo.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},zo.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},Fo.validateRegExpFlags=function(e){for(var t=e.validFlags,s=e.flags,i=0;i<s.length;i++){var n=s.charAt(i);-1===t.indexOf(n)&&this.raise(e.start,"Invalid regular expression flag"),s.indexOf(n,i+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},Fo.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},Fo.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,s=e.backReferenceNames;t<s.length;t+=1){var i=s[t];-1===e.groupNames.indexOf(i)&&e.raise("Invalid named capture referenced")}},Fo.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},Fo.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},Fo.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):!!(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},Fo.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var s=!1;if(this.options.ecmaVersion>=9&&(s=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!s,!0}return e.pos=t,!1},Fo.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},Fo.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},Fo.regexp_eatBracedQuantifier=function(e,t){var s=e.pos;if(e.eat(123)){var i=0,n=-1;if(this.regexp_eatDecimalDigits(e)&&(i=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(n=e.lastIntValue),e.eat(125)))return-1!==n&&n<i&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=s}return!1},Fo.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},Fo.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},Fo.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},Fo.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},Fo.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},Fo.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},Fo.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!Wo(t)&&(e.lastIntValue=t,e.advance(),!0)},Fo.regexp_eatPatternCharacters=function(e){for(var t=e.pos,s=0;-1!==(s=e.current())&&!Wo(s);)e.advance();return e.pos!==t},Fo.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return!(-1===t||36===t||t>=40&&t<=43||46===t||63===t||91===t||94===t||124===t||(e.advance(),0))},Fo.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e))return-1!==e.groupNames.indexOf(e.lastStringValue)&&e.raise("Duplicate capture group name"),void e.groupNames.push(e.lastStringValue);e.raise("Invalid group")}},Fo.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},Fo.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=ja(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=ja(e.lastIntValue);return!0}return!1},Fo.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,s=this.options.ecmaVersion>=11,i=e.current(s);return e.advance(s),92===i&&this.regexp_eatRegExpUnicodeEscapeSequence(e,s)&&(i=e.lastIntValue),function(e){return Aa(e,!0)||36===e||95===e}(i)?(e.lastIntValue=i,!0):(e.pos=t,!1)},Fo.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,s=this.options.ecmaVersion>=11,i=e.current(s);return e.advance(s),92===i&&this.regexp_eatRegExpUnicodeEscapeSequence(e,s)&&(i=e.lastIntValue),function(e){return Sa(e,!0)||36===e||95===e||8204===e||8205===e}(i)?(e.lastIntValue=i,!0):(e.pos=t,!1)},Fo.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},Fo.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var s=e.lastIntValue;if(e.switchU)return s>e.maxBackReference&&(e.maxBackReference=s),!0;if(s<=e.numCapturingParens)return!0;e.pos=t}return!1},Fo.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},Fo.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},Fo.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},Fo.regexp_eatZero=function(e){return 48===e.current()&&!Ho(e.lookahead())&&(e.lastIntValue=0,e.advance(),!0)},Fo.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},Fo.regexp_eatControlLetter=function(e){var t=e.current();return!!jo(t)&&(e.lastIntValue=t%32,e.advance(),!0)},Fo.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var s,i=e.pos,n=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var r=e.lastIntValue;if(n&&r>=55296&&r<=56319){var a=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var o=e.lastIntValue;if(o>=56320&&o<=57343)return e.lastIntValue=1024*(r-55296)+(o-56320)+65536,!0}e.pos=a,e.lastIntValue=r}return!0}if(n&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&(s=e.lastIntValue)>=0&&s<=1114111)return!0;n&&e.raise("Invalid unicode escape"),e.pos=i}return!1},Fo.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return!(99===t||e.switchN&&107===t||(e.lastIntValue=t,e.advance(),0))},Fo.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48),e.advance()}while((t=e.current())>=48&&t<=57);return!0}return!1},Fo.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(function(e){return 100===e||68===e||115===e||83===e||119===e||87===e}(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(80===t||112===t)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1},Fo.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var s=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var i=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,s,i),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var n=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,n),!0}return!1},Fo.regexp_validateUnicodePropertyNameAndValue=function(e,t,s){Fa(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(s)||e.raise("Invalid property value")},Fo.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},Fo.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";Uo(t=e.current());)e.lastStringValue+=ja(t),e.advance();return""!==e.lastStringValue},Fo.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";Go(t=e.current());)e.lastStringValue+=ja(t),e.advance();return""!==e.lastStringValue},Fo.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},Fo.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},Fo.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var s=e.lastIntValue;!e.switchU||-1!==t&&-1!==s||e.raise("Invalid character class"),-1!==t&&-1!==s&&t>s&&e.raise("Range out of order in character class")}}},Fo.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var s=e.current();(99===s||Xo(s))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var i=e.current();return 93!==i&&(e.lastIntValue=i,e.advance(),!0)},Fo.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},Fo.regexp_eatClassControlLetter=function(e){var t=e.current();return!(!Ho(t)&&95!==t||(e.lastIntValue=t%32,e.advance(),0))},Fo.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},Fo.regexp_eatDecimalDigits=function(e){var t=e.pos,s=0;for(e.lastIntValue=0;Ho(s=e.current());)e.lastIntValue=10*e.lastIntValue+(s-48),e.advance();return e.pos!==t},Fo.regexp_eatHexDigits=function(e){var t=e.pos,s=0;for(e.lastIntValue=0;qo(s=e.current());)e.lastIntValue=16*e.lastIntValue+Ko(s),e.advance();return e.pos!==t},Fo.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var s=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*s+e.lastIntValue:e.lastIntValue=8*t+s}else e.lastIntValue=t;return!0}return!1},Fo.regexp_eatOctalDigit=function(e){var t=e.current();return Xo(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},Fo.regexp_eatFixedHexDigits=function(e,t){var s=e.pos;e.lastIntValue=0;for(var i=0;i<t;++i){var n=e.current();if(!qo(n))return e.pos=s,!1;e.lastIntValue=16*e.lastIntValue+Ko(n),e.advance()}return!0};var Yo=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new Ha(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},Qo=Ja.prototype;function Zo(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}Qo.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new Yo(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},Qo.getToken=function(){return this.next(),new Yo(this)},"undefined"!=typeof Symbol&&(Qo[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===_a.eof,value:t}}}}),Qo.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(_a.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},Qo.readToken=function(e){return Aa(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},Qo.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},Qo.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,s=this.input.indexOf("*/",this.pos+=2);if(-1===s&&this.raise(this.pos-2,"Unterminated comment"),this.pos=s+2,this.options.locations)for(var i=void 0,n=t;(i=Ma(this.input,n,this.pos))>-1;)++this.curLine,n=this.lineStart=i;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,s),t,this.pos,e,this.curPosition())},Qo.skipLineComment=function(e){for(var t=this.pos,s=this.options.onComment&&this.curPosition(),i=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!Ra(i);)i=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,s,this.curPosition())},Qo.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&Da.test(String.fromCharCode(e))))break e;++this.pos}}},Qo.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var s=this.type;this.type=e,this.value=t,this.updateContext(s)},Qo.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(_a.ellipsis)):(++this.pos,this.finishToken(_a.dot))},Qo.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(_a.assign,2):this.finishOp(_a.slash,1)},Qo.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),s=1,i=42===e?_a.star:_a.modulo;return this.options.ecmaVersion>=7&&42===e&&42===t&&(++s,i=_a.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(_a.assign,s+1):this.finishOp(i,s)},Qo.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?this.options.ecmaVersion>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(_a.assign,3):this.finishOp(124===e?_a.logicalOR:_a.logicalAND,2):61===t?this.finishOp(_a.assign,2):this.finishOp(124===e?_a.bitwiseOR:_a.bitwiseAND,1)},Qo.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(_a.assign,2):this.finishOp(_a.bitwiseXOR,1)},Qo.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!==t||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!$a.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(_a.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(_a.assign,2):this.finishOp(_a.plusMin,1)},Qo.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),s=1;return t===e?(s=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+s)?this.finishOp(_a.assign,s+1):this.finishOp(_a.bitShift,s)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(s=2),this.finishOp(_a.relational,s)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},Qo.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(_a.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(_a.arrow)):this.finishOp(61===e?_a.eq:_a.prefix,1)},Qo.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var s=this.input.charCodeAt(this.pos+2);if(s<48||s>57)return this.finishOp(_a.questionDot,2)}if(63===t)return e>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(_a.assign,3):this.finishOp(_a.coalesce,2)}return this.finishOp(_a.question,1)},Qo.readToken_numberSign=function(){var e=35;if(this.options.ecmaVersion>=13&&(++this.pos,Aa(e=this.fullCharCodeAtPos(),!0)||92===e))return this.finishToken(_a.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+ja(e)+"'")},Qo.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(_a.parenL);case 41:return++this.pos,this.finishToken(_a.parenR);case 59:return++this.pos,this.finishToken(_a.semi);case 44:return++this.pos,this.finishToken(_a.comma);case 91:return++this.pos,this.finishToken(_a.bracketL);case 93:return++this.pos,this.finishToken(_a.bracketR);case 123:return++this.pos,this.finishToken(_a.braceL);case 125:return++this.pos,this.finishToken(_a.braceR);case 58:return++this.pos,this.finishToken(_a.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(_a.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(_a.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+ja(e)+"'")},Qo.finishOp=function(e,t){var s=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,s)},Qo.readRegexp=function(){for(var e,t,s=this.pos;;){this.pos>=this.input.length&&this.raise(s,"Unterminated regular expression");var i=this.input.charAt(this.pos);if($a.test(i)&&this.raise(s,"Unterminated regular expression"),e)e=!1;else{if("["===i)t=!0;else if("]"===i&&t)t=!1;else if("/"===i&&!t)break;e="\\"===i}++this.pos}var n=this.input.slice(s,this.pos);++this.pos;var r=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(r);var o=this.regexpState||(this.regexpState=new zo(this));o.reset(s,n,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var h=null;try{h=new RegExp(n,a)}catch(e){}return this.finishToken(_a.regexp,{pattern:n,flags:a,value:h})},Qo.readInt=function(e,t,s){for(var i=this.options.ecmaVersion>=12&&void 0===t,n=s&&48===this.input.charCodeAt(this.pos),r=this.pos,a=0,o=0,h=0,l=null==t?1/0:t;h<l;++h,++this.pos){var c=this.input.charCodeAt(this.pos),u=void 0;if(i&&95===c)n&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===h&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=c;else{if((u=c>=97?c-97+10:c>=65?c-65+10:c>=48&&c<=57?c-48:1/0)>=e)break;o=c,a=a*e+u}}return i&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===r||null!=t&&this.pos-r!==t?null:a},Qo.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var s=this.readInt(e);return null==s&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(s=Zo(this.input.slice(t,this.pos)),++this.pos):Aa(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(_a.num,s)},Qo.readNumber=function(e){var t=this.pos;e||null!==this.readInt(10,void 0,!0)||this.raise(t,"Invalid number");var s=this.pos-t>=2&&48===this.input.charCodeAt(t);s&&this.strict&&this.raise(t,"Invalid number");var i=this.input.charCodeAt(this.pos);if(!s&&!e&&this.options.ecmaVersion>=11&&110===i){var n=Zo(this.input.slice(t,this.pos));return++this.pos,Aa(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(_a.num,n)}s&&/[89]/.test(this.input.slice(t,this.pos))&&(s=!1),46!==i||s||(++this.pos,this.readInt(10),i=this.input.charCodeAt(this.pos)),69!==i&&101!==i||s||(43!==(i=this.input.charCodeAt(++this.pos))&&45!==i||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),Aa(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var r,a=(r=this.input.slice(t,this.pos),s?parseInt(r,8):parseFloat(r.replace(/_/g,"")));return this.finishToken(_a.num,a)},Qo.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},Qo.readString=function(e){for(var t="",s=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var i=this.input.charCodeAt(this.pos);if(i===e)break;92===i?(t+=this.input.slice(s,this.pos),t+=this.readEscapedChar(!1),s=this.pos):8232===i||8233===i?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(Ra(i)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(s,this.pos++),this.finishToken(_a.string,t)};var Jo={};Qo.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==Jo)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},Qo.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Jo;this.raise(e,t)},Qo.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var s=this.input.charCodeAt(this.pos);if(96===s||36===s&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==_a.template&&this.type!==_a.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(_a.template,e)):36===s?(this.pos+=2,this.finishToken(_a.dollarBraceL)):(++this.pos,this.finishToken(_a.backQuote));if(92===s)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(Ra(s)){switch(e+=this.input.slice(t,this.pos),++this.pos,s){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(s)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},Qo.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(_a.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},Qo.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return ja(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var s=this.pos-1;return this.invalidStringToken(s,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var i=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],n=parseInt(i,8);return n>255&&(i=i.slice(0,-1),n=parseInt(i,8)),this.pos+=i.length-1,t=this.input.charCodeAt(this.pos),"0"===i&&56!==t&&57!==t||!this.strict&&!e||this.invalidStringToken(this.pos-1-i.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(n)}return Ra(t)?"":String.fromCharCode(t)}},Qo.readHexChar=function(e){var t=this.pos,s=this.readInt(16,e);return null===s&&this.invalidStringToken(t,"Bad character escape sequence"),s},Qo.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,s=this.pos,i=this.options.ecmaVersion>=6;this.pos<this.input.length;){var n=this.fullCharCodeAtPos();if(Sa(n,i))this.pos+=n<=65535?1:2;else{if(92!==n)break;this.containsEsc=!0,e+=this.input.slice(s,this.pos);var r=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(t?Aa:Sa)(a,i)||this.invalidStringToken(r,"Invalid Unicode escape"),e+=ja(a),s=this.pos}t=!1}return e+this.input.slice(s,this.pos)},Qo.readWord=function(){var e=this.readWord1(),t=_a.name;return this.keywords.test(e)&&(t=Ia[e]),this.finishToken(t,e)},Ja.acorn={Parser:Ja,version:"8.7.1",defaultOptions:Ka,Position:Ga,SourceLocation:Ha,getLineInfo:qa,Node:So,TokenType:Pa,tokTypes:_a,keywordTypes:Ia,TokContext:fo,tokContexts:mo,isIdentifierChar:Sa,isIdentifierStart:Aa,Token:Yo,isNewLine:Ra,lineBreak:$a,lineBreakG:Ta,nonASCIIwhitespace:Da};class eh{constructor(e){this.maxParallel=e,this.queue=[],this.workerCount=0}run(e){return new Promise(((t,s)=>{this.queue.push({reject:s,resolve:t,task:e}),this.work()}))}async work(){if(this.workerCount>=this.maxParallel)return;let e;for(this.workerCount++;e=this.queue.shift();){const{reject:t,resolve:s,task:i}=e;try{s(await i())}catch(e){t(e)}}this.workerCount--}}const th=e=>()=>{oe({code:"NO_FS_IN_BROWSER",message:`Cannot access the file system (via "${e}") when using the browser build of Rollup. Make sure you supply a plugin with custom resolveId and load hooks to Rollup.`,url:"https://rollupjs.org/guide/en/#a-simple-example"})},sh={mkdir:th("fs.mkdir"),readFile:th("fs.readFile"),writeFile:th("fs.writeFile")};async function ih(e,t,s,i,n,r,a,o){const h=await function(e,t,s,i,n,r,a){let o=null,h=null;if(n){o=new Set;for(const s of n)e===s.source&&t===s.importer&&o.add(s.plugin);h=(e,t)=>({...e,resolve:(e,s,{custom:r,isEntry:a,skipSelf:o}=Y)=>i(e,s,r,a,o?[...n,{importer:s,plugin:t,source:e}]:n)})}return s.hookFirst("resolveId",[e,t,{custom:r,isEntry:a}],h,o)}(e,t,i,n,r,a,o);return h}const nh="at position ",rh="at output position ";function ah(e,t,{hook:s,id:i}={}){return"string"==typeof e&&(e={message:e}),e.code&&e.code!==le.PLUGIN_ERROR&&(e.pluginCode=e.code),e.code=le.PLUGIN_ERROR,e.plugin=t,s&&(e.hook=s),i&&(e.id=i),oe(e)}const oh=[{active:!0,deprecated:"resolveAssetUrl",replacement:"resolveFileUrl"}],hh={delete:()=>!1,get(){},has:()=>!1,set(){}};function lh(e){return e.startsWith(nh)||e.startsWith(rh)?oe({code:"ANONYMOUS_PLUGIN_CACHE",message:"A plugin is trying to use the Rollup cache but is not declaring a plugin name or cacheKey."}):oe({code:"DUPLICATE_PLUGIN_NAME",message:`The plugin name ${e} is being used twice in the same build. Plugin names must be distinct or provide a cacheKey (please post an issue to the plugin if you are a plugin user).`})}async function ch(e,t,s,i){const n=t.id,r=[];let a=null===e.map?null:Fr(e.map);const o=e.code;let h=e.ast;const l=[],u=[];let d=!1;const p=()=>d=!0;let f="";const m=e.code;let g;try{g=await s.hookReduceArg0("transform",[m,n],(function(e,s,n){let a,o;if("string"==typeof s)a=s;else{if(!s||"object"!=typeof s)return e;if(t.updateOptions(s),null==s.code)return(s.map||s.ast)&&i(function(e){return{code:le.NO_TRANSFORM_MAP_OR_AST_WITHOUT_CODE,message:`The plugin "${e}" returned a "map" or "ast" without returning a "code". This will be ignored.`}}(n.name)),e;({code:a,map:o,ast:h}=s)}return null!==o&&r.push(Fr("string"==typeof o?JSON.parse(o):o)||{missing:!0,plugin:n.name}),a}),((e,t)=>{return f=t.name,{...e,addWatchFile(t){l.push(t),e.addWatchFile(t)},cache:d?e.cache:(h=e.cache,g=p,{delete:e=>(g(),h.delete(e)),get:e=>(g(),h.get(e)),has:e=>(g(),h.has(e)),set:(e,t)=>(g(),h.set(e,t))}),emitAsset:(t,s)=>(u.push({name:t,source:s,type:"asset"}),e.emitAsset(t,s)),emitChunk:(t,s)=>(u.push({id:t,name:s&&s.name,type:"chunk"}),e.emitChunk(t,s)),emitFile:e=>(u.push(e),s.emitFile(e)),error:(t,s)=>("string"==typeof t&&(t={message:t}),s&&he(t,s,m,n),t.id=n,t.hook="transform",e.error(t)),getCombinedSourcemap(){const e=function(e,t,s,i,n){return i.length?{version:3,...Un(e,t,s,i,jn(n)).traceMappings()}:s}(n,o,a,r,i);return e?(a!==e&&(a=e,r.length=0),new c({...e,file:null,sourcesContent:e.sourcesContent})):new x(o).generateMap({hires:!0,includeContent:!0,source:n})},setAssetSource(){return this.error({code:"INVALID_SETASSETSOURCE",message:"setAssetSource cannot be called in transform for caching reasons. Use emitFile with a source, or call setAssetSource in another hook."})},warn(t,s){"string"==typeof t&&(t={message:t}),s&&he(t,s,m,n),t.id=n,t.hook="transform",e.warn(t)}};var h,g}))}catch(e){ah(e,f,{hook:"transform",id:n})}return d||u.length&&(t.transformFiles=u),{ast:h,code:g,customTransformCache:d,originalCode:o,originalSourcemap:a,sourcemapChain:r,transformDependencies:l}}const uh="resolveDependencies";class dh{constructor(e,t,s,i){this.graph=e,this.modulesById=t,this.options=s,this.pluginDriver=i,this.implicitEntryModules=new Set,this.indexedEntryModules=[],this.latestLoadModulesPromise=Promise.resolve(),this.moduleLoadPromises=new Map,this.modulesWithLoadedDependencies=new Set,this.nextChunkNamePriority=0,this.nextEntryModuleIndex=0,this.resolveId=async(e,t,s,i,n=null)=>this.getResolvedIdWithDefaults(this.getNormalizedResolvedIdWithoutDefaults(!this.options.external(e,t,!1)&&await ih(e,t,this.options.preserveSymlinks,this.pluginDriver,this.resolveId,n,s,"boolean"==typeof i?i:!t),t,e)),this.hasModuleSideEffects=s.treeshake?s.treeshake.moduleSideEffects:()=>!0,this.readQueue=new eh(s.maxParallelFileReads)}async addAdditionalModules(e){const t=this.extendLoadModulesPromise(Promise.all(e.map((e=>this.loadEntryModule(e,!1,void 0,null)))));return await this.awaitLoadModulesPromise(),t}async addEntryModules(e,t){const s=this.nextEntryModuleIndex;this.nextEntryModuleIndex+=e.length;const i=this.nextChunkNamePriority;this.nextChunkNamePriority+=e.length;const n=await this.extendLoadModulesPromise(Promise.all(e.map((({id:e,importer:t})=>this.loadEntryModule(e,!0,t,null)))).then((n=>{for(let r=0;r<n.length;r++){const a=n[r];a.isUserDefinedEntryPoint=a.isUserDefinedEntryPoint||t,fh(a,e[r],t,i+r);const o=this.indexedEntryModules.find((e=>e.module===a));o?o.index=Math.min(o.index,s+r):this.indexedEntryModules.push({index:s+r,module:a})}return this.indexedEntryModules.sort((({index:e},{index:t})=>e>t?1:-1)),n})));return await this.awaitLoadModulesPromise(),{entryModules:this.indexedEntryModules.map((({module:e})=>e)),implicitEntryModules:[...this.implicitEntryModules],newEntryModules:n}}async emitChunk({fileName:e,id:t,importer:s,name:i,implicitlyLoadedAfterOneOf:n,preserveSignature:r}){const a={fileName:e||null,id:t,importer:s,name:i||null},o=n?await this.addEntryWithImplicitDependants(a,n):(await this.addEntryModules([a],!1)).newEntryModules[0];return null!=r&&(o.preserveSignature=r),o}async preloadModule(e){return(await this.fetchModule(this.getResolvedIdWithDefaults(e),void 0,!1,!e.resolveDependencies||uh)).info}addEntryWithImplicitDependants(e,t){const s=this.nextChunkNamePriority++;return this.extendLoadModulesPromise(this.loadEntryModule(e.id,!1,e.importer,null).then((async i=>{if(fh(i,e,!1,s),!i.info.isEntry){this.implicitEntryModules.add(i);const s=await Promise.all(t.map((t=>this.loadEntryModule(t,!1,e.importer,i.id))));for(const e of s)i.implicitlyLoadedAfter.add(e);for(const e of i.implicitlyLoadedAfter)e.implicitlyLoadedBefore.add(i)}return i})))}async addModuleSource(e,t,s){let i;hn("load modules",3);try{i=await this.readQueue.run((async()=>{var t;return null!==(t=await this.pluginDriver.hookFirst("load",[e]))&&void 0!==t?t:await sh.readFile(e,"utf8")}))}catch(s){ln("load modules",3);let i=`Could not load ${e}`;throw t&&(i+=` (imported by ${ie(t)})`),i+=`: ${s.message}`,s.message=i,s}ln("load modules",3);const n="string"==typeof i?{code:i}:null!=i&&"object"==typeof i&&"string"==typeof i.code?i:oe(function(e){return{code:le.BAD_LOADER,message:`Error loading ${ie(e)}: plugin load hook should return a string, a { code, map } object, or nothing/null`}}(e)),r=this.graph.cachedModules.get(e);if(!r||r.customTransformCache||r.originalCode!==n.code||await this.pluginDriver.hookFirst("shouldTransformCachedModule",[{ast:r.ast,code:r.code,id:r.id,meta:r.meta,moduleSideEffects:r.moduleSideEffects,resolvedSources:r.resolvedIds,syntheticNamedExports:r.syntheticNamedExports}]))s.updateOptions(n),s.setSource(await ch(n,s,this.pluginDriver,this.options.onwarn));else{if(r.transformFiles)for(const e of r.transformFiles)this.pluginDriver.emitFile(e);s.setSource(r)}}async awaitLoadModulesPromise(){let e;do{e=this.latestLoadModulesPromise,await e}while(e!==this.latestLoadModulesPromise)}extendLoadModulesPromise(e){return this.latestLoadModulesPromise=Promise.all([e,this.latestLoadModulesPromise]),this.latestLoadModulesPromise.catch((()=>{})),e}async fetchDynamicDependencies(e,t){const s=await Promise.all(t.map((t=>t.then((async([t,s])=>null===s?null:"string"==typeof s?(t.resolution=s,null):t.resolution=await this.fetchResolvedDependency(ie(s.id),e.id,s))))));for(const t of s)t&&(e.dynamicDependencies.add(t),t.dynamicImporters.push(e.id))}async fetchModule({id:e,meta:t,moduleSideEffects:s,syntheticNamedExports:i},n,r,a){const o=this.modulesById.get(e);if(o instanceof mn)return await this.handleExistingModule(o,r,a),o;const h=new mn(this.graph,e,this.options,r,s,i,t);this.modulesById.set(e,h),this.graph.watchFiles[e]=!0;const l=this.addModuleSource(e,n,h).then((()=>[this.getResolveStaticDependencyPromises(h),this.getResolveDynamicImportPromises(h),c])),c=gh(l).then((()=>this.pluginDriver.hookParallel("moduleParsed",[h.info])));c.catch((()=>{})),this.moduleLoadPromises.set(h,l);const u=await l;return a?a===uh&&await c:await this.fetchModuleDependencies(h,...u),h}async fetchModuleDependencies(e,t,s,i){this.modulesWithLoadedDependencies.has(e)||(this.modulesWithLoadedDependencies.add(e),await Promise.all([this.fetchStaticDependencies(e,t),this.fetchDynamicDependencies(e,s)]),e.linkImports(),await i)}fetchResolvedDependency(e,t,s){if(s.external){const{external:i,id:n,moduleSideEffects:r,meta:a}=s;this.modulesById.has(n)||this.modulesById.set(n,new ke(this.options,n,r,a,"absolute"!==i&&C(n)));const o=this.modulesById.get(n);return o instanceof ke?Promise.resolve(o):oe(function(e,t){return{code:le.INVALID_EXTERNAL_ID,message:`'${e}' is imported as an external by ${ie(t)}, but is already an existing non-external module id.`}}(e,t))}return this.fetchModule(s,t,!1,!1)}async fetchStaticDependencies(e,t){for(const s of await Promise.all(t.map((t=>t.then((([t,s])=>this.fetchResolvedDependency(t,e.id,s)))))))e.dependencies.add(s),s.importers.push(e.id);if(!this.options.treeshake||"no-treeshake"===e.info.moduleSideEffects)for(const t of e.dependencies)t instanceof mn&&(t.importedFromNotTreeshaken=!0)}getNormalizedResolvedIdWithoutDefaults(e,t,s){const{makeAbsoluteExternalsRelative:i}=this.options;if(e){if("object"==typeof e){const n=e.external||this.options.external(e.id,t,!0);return{...e,external:n&&("relative"===n||!C(e.id)||!0===n&&mh(e.id,s,i)||"absolute")}}const n=this.options.external(e,t,!0);return{external:n&&(mh(e,s,i)||"absolute"),id:n&&i?ph(e,t):e}}const n=i?ph(s,t):s;return!1===e||this.options.external(n,t,!0)?{external:mh(n,s,i)||"absolute",id:n}:null}getResolveDynamicImportPromises(e){return e.dynamicImports.map((async t=>{const s=await this.resolveDynamicImport(e,"string"==typeof t.argument?t.argument:t.argument.esTreeNode,e.id);return s&&"object"==typeof s&&(t.id=s.id),[t,s]}))}getResolveStaticDependencyPromises(e){return Array.from(e.sources,(async t=>[t,e.resolvedIds[t]=e.resolvedIds[t]||this.handleResolveId(await this.resolveId(t,e.id,Q,!1),t,e.id)]))}getResolvedIdWithDefaults(e){var t,s;if(!e)return null;const i=e.external||!1;return{external:i,id:e.id,meta:e.meta||{},moduleSideEffects:null!==(t=e.moduleSideEffects)&&void 0!==t?t:this.hasModuleSideEffects(e.id,!!i),syntheticNamedExports:null!==(s=e.syntheticNamedExports)&&void 0!==s&&s}}async handleExistingModule(e,t,s){const i=this.moduleLoadPromises.get(e);if(s)return s===uh?gh(i):i;if(t){e.info.isEntry=!0,this.implicitEntryModules.delete(e);for(const t of e.implicitlyLoadedAfter)t.implicitlyLoadedBefore.delete(e);e.implicitlyLoadedAfter.clear()}return this.fetchModuleDependencies(e,...await i)}handleResolveId(e,t,s){return null===e?I(t)?oe(function(e,t){return{code:le.UNRESOLVED_IMPORT,message:`Could not resolve '${e}' from ${ie(t)}`}}(t,s)):(this.options.onwarn(function(e,t){return{code:le.UNRESOLVED_IMPORT,importer:ie(t),message:`'${e}' is imported by ${ie(t)}, but could not be resolved – treating it as an external dependency`,source:e,url:"https://rollupjs.org/guide/en/#warning-treating-module-as-external-dependency"}}(t,s)),{external:!0,id:t,meta:{},moduleSideEffects:this.hasModuleSideEffects(t,!0),syntheticNamedExports:!1}):(e.external&&e.syntheticNamedExports&&this.options.onwarn(function(e,t){return{code:le.EXTERNAL_SYNTHETIC_EXPORTS,importer:ie(t),message:`External '${e}' can not have 'syntheticNamedExports' enabled.`,source:e}}(t,s)),e)}async loadEntryModule(e,t,s,i){const n=await ih(e,s,this.options.preserveSymlinks,this.pluginDriver,this.resolveId,null,Q,!0);return null==n?oe(null===i?function(e){return{code:le.UNRESOLVED_ENTRY,message:`Could not resolve entry module (${ie(e)}).`}}(e):function(e,t){return{code:le.MISSING_IMPLICIT_DEPENDANT,message:`Module "${ie(e)}" that should be implicitly loaded before "${ie(t)}" could not be resolved.`}}(e,i)):!1===n||"object"==typeof n&&n.external?oe(null===i?function(e){return{code:le.UNRESOLVED_ENTRY,message:`Entry module cannot be external (${ie(e)}).`}}(e):function(e,t){return{code:le.MISSING_IMPLICIT_DEPENDANT,message:`Module "${ie(e)}" that should be implicitly loaded before "${ie(t)}" cannot be external.`}}(e,i)):this.fetchModule(this.getResolvedIdWithDefaults("object"==typeof n?n:{id:n}),void 0,t,!1)}async resolveDynamicImport(e,t,s){var i,n;const r=await this.pluginDriver.hookFirst("resolveDynamicImport",[t,s]);return"string"!=typeof t?"string"==typeof r?r:r?{external:!1,moduleSideEffects:!0,...r}:null:null==r?null!==(i=(n=e.resolvedIds)[t])&&void 0!==i?i:n[t]=this.handleResolveId(await this.resolveId(t,e.id,Q,!1),t,e.id):this.handleResolveId(this.getResolvedIdWithDefaults(this.getNormalizedResolvedIdWithoutDefaults(r,s,t)),t,s)}}function ph(e,t){return I(e)?t?M(t,"..",e):M(e):e}function fh(e,{fileName:t,name:s},i,n){var r;if(null!==t)e.chunkFileNames.add(t);else if(null!==s){let t=0;for(;(null===(r=e.chunkNames[t])||void 0===r?void 0:r.priority)<n;)t++;e.chunkNames.splice(t,0,{isUserDefined:i,name:s,priority:n})}}function mh(e,t,s){return!0===s||"ifRelativeSource"===s&&I(t)||!C(e)}async function gh(e){const[t,s]=await e;return Promise.all([...t,...s])}class yh extends Wt{constructor(){super(),this.parent=null,this.variables.set("undefined",new Fi)}findVariable(e){let t=this.variables.get(e);return t||(t=new ys(e),this.variables.set(e,t)),t}}function Eh(e,t,s,i,n,r){let a=!1;return(...o)=>(a||(a=!0,xe({message:`The "this.${t}" plugin context function used by plugin ${i} is deprecated. The "this.${s}" plugin context function should be used instead.`,plugin:i},n,r)),e(...o))}function xh(e,s,i,n,r,a){let o,h=!0;if("string"!=typeof e.cacheKey&&(e.name.startsWith(nh)||e.name.startsWith(rh)||a.has(e.name)?h=!1:a.add(e.name)),s)if(h){const t=e.cacheKey||e.name;c=s[t]||(s[t]=Object.create(null)),o={delete:e=>delete c[e],get(e){const t=c[e];if(t)return t[0]=0,t[1]},has(e){const t=c[e];return!!t&&(t[0]=0,!0)},set(e,t){c[e]=[0,t]}}}else l=e.name,o={delete:()=>lh(l),get:()=>lh(l),has:()=>lh(l),set:()=>lh(l)};else o=hh;var l,c;const u={addWatchFile(e){if(i.phase>=Qi.GENERATE)return this.error({code:le.INVALID_ROLLUP_PHASE,message:"Cannot call addWatchFile after the build has finished."});i.watchFiles[e]=!0},cache:o,emitAsset:Eh(((e,t)=>r.emitFile({name:e,source:t,type:"asset"})),"emitAsset","emitFile",e.name,!0,n),emitChunk:Eh(((e,t)=>r.emitFile({id:e,name:t&&t.name,type:"chunk"})),"emitChunk","emitFile",e.name,!0,n),emitFile:r.emitFile.bind(r),error:t=>ah(t,e.name),getAssetFileName:Eh(r.getFileName,"getAssetFileName","getFileName",e.name,!0,n),getChunkFileName:Eh(r.getFileName,"getChunkFileName","getFileName",e.name,!0,n),getFileName:r.getFileName,getModuleIds:()=>i.modulesById.keys(),getModuleInfo:i.getModuleInfo,getWatchFiles:()=>Object.keys(i.watchFiles),isExternal:Eh(((e,t,s=!1)=>n.external(e,t,s)),"isExternal","resolve",e.name,!0,n),load:e=>i.moduleLoader.preloadModule(e),meta:{rollupVersion:t,watchMode:i.watchMode},get moduleIds(){const t=i.modulesById.keys();return function*(){xe({message:`Accessing "this.moduleIds" on the plugin context by plugin ${e.name} is deprecated. The "this.getModuleIds" plugin context function should be used instead.`,plugin:e.name},!1,n),yield*t}()},parse:i.contextParse.bind(i),resolve:(t,s,{custom:n,isEntry:r,skipSelf:a}=Y)=>i.moduleLoader.resolveId(t,s,n,r,a?[{importer:s,plugin:e,source:t}]:null),resolveId:Eh(((e,t)=>i.moduleLoader.resolveId(e,t,Y,void 0).then((e=>e&&e.id))),"resolveId","resolve",e.name,!0,n),setAssetSource:r.setAssetSource,warn(t){"string"==typeof t&&(t={message:t}),t.code&&(t.pluginCode=t.code),t.code="PLUGIN_WARNING",t.plugin=e.name,n.onwarn(t)}};return u}const bh=Object.keys({buildEnd:1,buildStart:1,closeBundle:1,closeWatcher:1,load:1,moduleParsed:1,options:1,resolveDynamicImport:1,resolveId:1,shouldTransformCachedModule:1,transform:1,watchChange:1});function vh(e,t){return oe({code:"INVALID_PLUGIN_HOOK",message:`Error running plugin hook ${e} for ${t}, expected a function hook.`})}class Ah{constructor(e,t,s,i,n){this.graph=e,this.options=t,this.unfulfilledActions=new Set,function(e,t){for(const{active:s,deprecated:i,replacement:n}of oh)for(const r of e)i in r&&xe({message:`The "${i}" hook used by plugin ${r.name} is deprecated. The "${n}" hook should be used instead.`,plugin:r.name},s,t)}(s,t),this.pluginCache=i,this.fileEmitter=new Jr(e,t,n&&n.fileEmitter),this.emitFile=this.fileEmitter.emitFile.bind(this.fileEmitter),this.getFileName=this.fileEmitter.getFileName.bind(this.fileEmitter),this.finaliseAssets=this.fileEmitter.assertAssetsFinalized.bind(this.fileEmitter),this.setOutputBundle=this.fileEmitter.setOutputBundle.bind(this.fileEmitter),this.plugins=s.concat(n?n.plugins:[]);const r=new Set;if(this.pluginContexts=new Map(this.plugins.map((s=>[s,xh(s,i,e,t,this.fileEmitter,r)]))),n)for(const e of s)for(const s of bh)s in e&&t.onwarn((a=e.name,o=s,{code:le.INPUT_HOOK_IN_OUTPUT_PLUGIN,message:`The "${o}" hook used by the output plugin ${a} is a build time hook and will not be run for that plugin. Either this plugin cannot be used as an output plugin, or it should have an option to configure it as an output plugin.`}));var a,o}createOutputPluginDriver(e){return new Ah(this.graph,this.options,e,this.pluginCache,this)}getUnfulfilledHookActions(){return this.unfulfilledActions}hookFirst(e,t,s,i){let n=Promise.resolve(void 0);for(const r of this.plugins)i&&i.has(r)||(n=n.then((i=>null!=i?i:this.runHook(e,t,r,!1,s))));return n}hookFirstSync(e,t,s){for(const i of this.plugins){const n=this.runHookSync(e,t,i,s);if(null!=n)return n}return null}hookParallel(e,t,s){const i=[];for(const n of this.plugins){const r=this.runHook(e,t,n,!1,s);r&&i.push(r)}return Promise.all(i).then((()=>{}))}hookReduceArg0(e,[t,...s],i,n){let r=Promise.resolve(t);for(const t of this.plugins)r=r.then((r=>{const a=[r,...s],o=this.runHook(e,a,t,!1,n);return o?o.then((e=>i.call(this.pluginContexts.get(t),r,e,t))):r}));return r}hookReduceArg0Sync(e,[t,...s],i,n){for(const r of this.plugins){const a=[t,...s],o=this.runHookSync(e,a,r,n);t=i.call(this.pluginContexts.get(r),t,o,r)}return t}hookReduceValue(e,t,s,i,n){let r=Promise.resolve(t);for(const t of this.plugins)r=r.then((r=>{const a=this.runHook(e,s,t,!0,n);return a?a.then((e=>i.call(this.pluginContexts.get(t),r,e,t))):r}));return r}hookReduceValueSync(e,t,s,i,n){let r=t;for(const t of this.plugins){const a=this.runHookSync(e,s,t,n);r=i.call(this.pluginContexts.get(t),r,a,t)}return r}hookSeq(e,t,s){let i=Promise.resolve();for(const n of this.plugins)i=i.then((()=>this.runHook(e,t,n,!1,s)));return i}runHook(e,t,s,i,n){const r=s[e];if(!r)return;let a=this.pluginContexts.get(s);n&&(a=n(a,s));let o=null;return Promise.resolve().then((()=>{if("function"!=typeof r)return i?r:vh(e,s.name);const n=r.apply(a,t);return n&&n.then?(o=[s.name,e,t],this.unfulfilledActions.add(o),Promise.resolve(n).then((e=>(this.unfulfilledActions.delete(o),e)))):n})).catch((t=>(null!==o&&this.unfulfilledActions.delete(o),ah(t,s.name,{hook:e}))))}runHookSync(e,t,s,i){const n=s[e];if(!n)return;let r=this.pluginContexts.get(s);i&&(r=i(r,s));try{return"function"!=typeof n?vh(e,s.name):n.apply(r,t)}catch(t){return ah(t,s.name,{hook:e})}}}class Sh{constructor(e,t){var s,i;if(this.options=e,this.cachedModules=new Map,this.deoptimizationTracker=new W,this.entryModules=[],this.modulesById=new Map,this.needsTreeshakingPass=!1,this.phase=Qi.LOAD_AND_PARSE,this.scope=new yh,this.watchFiles=Object.create(null),this.watchMode=!1,this.externalModules=[],this.implicitEntryModules=[],this.modules=[],this.getModuleInfo=e=>{const t=this.modulesById.get(e);return t?t.info:null},!1!==e.cache){if(null===(s=e.cache)||void 0===s?void 0:s.modules)for(const t of e.cache.modules)this.cachedModules.set(t.id,t);this.pluginCache=(null===(i=e.cache)||void 0===i?void 0:i.plugins)||Object.create(null);for(const e in this.pluginCache){const t=this.pluginCache[e];for(const e of Object.values(t))e[0]++}}if(t){this.watchMode=!0;const e=(...e)=>this.pluginDriver.hookParallel("watchChange",e),s=()=>this.pluginDriver.hookParallel("closeWatcher",[]);t.onCurrentAwaited("change",e),t.onCurrentAwaited("close",s)}this.pluginDriver=new Ah(this,e,e.plugins,this.pluginCache),this.acornParser=Ja.extend(...e.acornInjectPlugins),this.moduleLoader=new dh(this,this.modulesById,this.options,this.pluginDriver)}async build(){hn("generate module graph",2),await this.generateModuleGraph(),ln("generate module graph",2),hn("sort modules",2),this.phase=Qi.ANALYSE,this.sortModules(),ln("sort modules",2),hn("mark included statements",2),this.includeStatements(),ln("mark included statements",2),this.phase=Qi.GENERATE}contextParse(e,t={}){const s=t.onComment,i=[];t.onComment=s&&"function"==typeof s?(e,n,r,a,...o)=>(i.push({end:a,start:r,type:e?"Block":"Line",value:n}),s.call(t,e,n,r,a,...o)):i;const n=this.acornParser.parse(e,{...this.options.acorn,...t});return"object"==typeof s&&s.push(...i),t.onComment=s,function(e,t,s){const i=[],n=[];for(const t of e)ct.test(t.value)?i.push(t):st.test(t.value)&&n.push(t);for(const e of n)ut(t,e,!1);rt(t,{annotationIndex:0,annotations:i,code:s})}(i,n,e),n}getCache(){for(const e in this.pluginCache){const t=this.pluginCache[e];let s=!0;for(const[e,i]of Object.entries(t))i[0]>=this.options.experimentalCacheExpiry?delete t[e]:s=!1;s&&delete this.pluginCache[e]}return{modules:this.modules.map((e=>e.toJSON())),plugins:this.pluginCache}}async generateModuleGraph(){var e;if(({entryModules:this.entryModules,implicitEntryModules:this.implicitEntryModules}=await this.moduleLoader.addEntryModules((e=this.options.input,Array.isArray(e)?e.map((e=>({fileName:null,id:e,implicitlyLoadedAfter:[],importer:void 0,name:null}))):Object.entries(e).map((([e,t])=>({fileName:null,id:t,implicitlyLoadedAfter:[],importer:void 0,name:e})))),!0)),0===this.entryModules.length)throw new Error("You must supply options.input to rollup");for(const e of this.modulesById.values())e instanceof mn?this.modules.push(e):this.externalModules.push(e)}includeStatements(){for(const e of[...this.entryModules,...this.implicitEntryModules])dn(e);if(this.options.treeshake){let e=1;do{hn(`treeshaking pass ${e}`,3),this.needsTreeshakingPass=!1;for(const e of this.modules)e.isExecuted&&("no-treeshake"===e.info.moduleSideEffects?e.includeAllInBundle():e.include());if(1===e)for(const e of[...this.entryModules,...this.implicitEntryModules])!1!==e.preserveSignature&&(e.includeAllExports(!1),this.needsTreeshakingPass=!0);ln("treeshaking pass "+e++,3)}while(this.needsTreeshakingPass)}else for(const e of this.modules)e.includeAllInBundle();for(const e of this.externalModules)e.warnUnusedImports();for(const e of this.implicitEntryModules)for(const t of e.implicitlyLoadedAfter)t.info.isEntry||t.isIncluded()||oe(fe(t))}sortModules(){const{orderedModules:e,cyclePaths:t}=function(e){let t=0;const s=[],i=new Set,n=new Set,r=new Map,a=[],o=e=>{if(e instanceof mn){for(const t of e.dependencies)r.has(t)?i.has(t)||s.push(aa(t,e,r)):(r.set(t,e),o(t));for(const t of e.implicitlyLoadedBefore)n.add(t);for(const{resolution:t}of e.dynamicImports)t instanceof mn&&n.add(t);a.push(e)}e.execIndex=t++,i.add(e)};for(const t of e)r.has(t)||(r.set(t,null),o(t));for(const e of n)r.has(e)||(r.set(e,null),o(e));return{cyclePaths:s,orderedModules:a}}(this.entryModules);for(const e of t)this.options.onwarn({code:"CIRCULAR_DEPENDENCY",cycle:e,importer:e[0],message:`Circular dependency: ${e.join(" -> ")}`});this.modules=e;for(const e of this.modules)e.bindReferences();this.warnForMissingExports()}warnForMissingExports(){for(const e of this.modules)for(const t of e.importDescriptions.values())"*"===t.name||t.module.getVariableForExportName(t.name)[0]||e.warn({code:"NON_EXISTENT_EXPORT",message:`Non-existent export '${t.name}' is imported from ${ie(t.module.id)}`,name:t.name,source:t.module.id},t.start)}}function Ph(e){return Array.isArray(e)?e.filter(Boolean):e?[e]:[]}function kh(e,t){return t()}const wh=e=>console.warn(e.message||e);function Ch(e,t,s,i,n=/$./){const r=new Set(t),a=Object.keys(e).filter((e=>!(r.has(e)||n.test(e))));a.length>0&&i({code:"UNKNOWN_OPTION",message:`Unknown ${s}: ${a.join(", ")}. Allowed options: ${[...r].sort().join(", ")}`})}const Ih={recommended:{annotations:!0,correctVarValueBeforeDeclaration:!1,moduleSideEffects:()=>!0,propertyReadSideEffects:!0,tryCatchDeoptimization:!0,unknownGlobalSideEffects:!1},safest:{annotations:!0,correctVarValueBeforeDeclaration:!0,moduleSideEffects:()=>!0,propertyReadSideEffects:!0,tryCatchDeoptimization:!0,unknownGlobalSideEffects:!0},smallest:{annotations:!0,correctVarValueBeforeDeclaration:!1,moduleSideEffects:()=>!1,propertyReadSideEffects:!1,tryCatchDeoptimization:!1,unknownGlobalSideEffects:!1}},Nh={es2015:{arrowFunctions:!0,constBindings:!0,objectShorthand:!0,reservedNamesAsProps:!0,symbols:!0},es5:{arrowFunctions:!1,constBindings:!1,objectShorthand:!1,reservedNamesAsProps:!0,symbols:!1}},_h=(e,t,s,i)=>{const n=null==e?void 0:e.preset;if(n){const i=t[n];if(i)return{...i,...e};oe(de(`${s}.preset`,$h(s),`valid values are ${te(Object.keys(t))}`,n))}return((e,t,s)=>i=>{if("string"==typeof i){const n=e[i];if(n)return n;oe(de(t,$h(t),`valid values are ${s}${te(Object.keys(e))}. You can also supply an object for more fine-grained control`,i))}return(e=>e&&"object"==typeof e?e:{})(i)})(t,s,i)(e)},$h=e=>e.split(".").join("").toLowerCase(),Th=e=>{const{onwarn:t}=e;return t?e=>{e.toString=()=>{let t="";return e.plugin&&(t+=`(${e.plugin} plugin) `),e.loc&&(t+=`${ie(e.loc.file)} (${e.loc.line}:${e.loc.column}) `),t+=e.message,t},t(e,wh)}:wh},Rh=e=>({allowAwaitOutsideFunction:!0,ecmaVersion:"latest",preserveParens:!1,sourceType:"module",...e.acorn}),Mh=e=>Ph(e.acornInjectPlugins),Dh=e=>{var t;return(null===(t=e.cache)||void 0===t?void 0:t.cache)||e.cache},Lh=e=>{if(!0===e)return()=>!0;if("function"==typeof e)return(t,...s)=>!t.startsWith("\0")&&e(t,...s)||!1;if(e){const t=new Set,s=[];for(const i of Ph(e))i instanceof RegExp?s.push(i):t.add(i);return(e,...i)=>t.has(e)||s.some((t=>t.test(e)))}return()=>!1},Oh=(e,t,s)=>{const i=e.inlineDynamicImports;return i&&be('The "inlineDynamicImports" option is deprecated. Use the "output.inlineDynamicImports" option instead.',!1,t,s),i},Vh=e=>{const t=e.input;return null==t?[]:"string"==typeof t?[t]:t},Bh=(e,t,s)=>{const i=e.manualChunks;return i&&be('The "manualChunks" option is deprecated. Use the "output.manualChunks" option instead.',!1,t,s),i},Fh=e=>{const t=e.maxParallelFileReads;return"number"==typeof t?t<=0?1/0:t:20},zh=(e,t)=>{const s=e.moduleContext;if("function"==typeof s)return e=>{var i;return null!==(i=s(e))&&void 0!==i?i:t};if(s){const e=Object.create(null);for(const[t,i]of Object.entries(s))e[M(t)]=i;return s=>e[s]||t}return()=>t},Wh=(e,t)=>{const s=e.preserveEntrySignatures;return null==s&&t.add("preserveEntrySignatures"),null!=s?s:"strict"},jh=(e,t,s)=>{const i=e.preserveModules;return i&&be('The "preserveModules" option is deprecated. Use the "output.preserveModules" option instead.',!1,t,s),i},Uh=(e,t,s)=>{const i=e.treeshake;if(!1===i)return!1;const n=_h(e.treeshake,Ih,"treeshake","false, true, ");return void 0!==n.pureExternalModules&&be('The "treeshake.pureExternalModules" option is deprecated. The "treeshake.moduleSideEffects" option should be used instead. "treeshake.pureExternalModules: true" is equivalent to "treeshake.moduleSideEffects: \'no-external\'"',!0,t,s),{annotations:!1!==n.annotations,correctVarValueBeforeDeclaration:!0===n.correctVarValueBeforeDeclaration,moduleSideEffects:"object"==typeof i&&i.pureExternalModules?Gh(i.moduleSideEffects,i.pureExternalModules):Gh(n.moduleSideEffects,void 0),propertyReadSideEffects:"always"===n.propertyReadSideEffects?"always":!1!==n.propertyReadSideEffects,tryCatchDeoptimization:!1!==n.tryCatchDeoptimization,unknownGlobalSideEffects:!1!==n.unknownGlobalSideEffects}},Gh=(e,t)=>{if("boolean"==typeof e)return()=>e;if("no-external"===e)return(e,t)=>!t;if("function"==typeof e)return(t,s)=>!!t.startsWith("\0")||!1!==e(t,s);if(Array.isArray(e)){const t=new Set(e);return e=>t.has(e)}e&&oe(de("treeshake.moduleSideEffects","treeshake",'please use one of false, "no-external", a function or an array'));const s=Lh(t);return(e,t)=>!(t&&s(e))},Hh=/[\x00-\x1F\x7F<>*#"{}|^[\]`;?:&=+$,]/g,qh=/^[a-z]:/i;function Kh(e){const t=qh.exec(e),s=t?t[0]:"";return s+e.substr(s.length).replace(Hh,"_")}const Xh=(e,t,s)=>{const{file:i}=e;if("string"==typeof i){if(t)return oe(de("output.file","outputdir",'you must set "output.dir" instead of "output.file" when using the "output.preserveModules" option'));if(!Array.isArray(s.input))return oe(de("output.file","outputdir",'you must set "output.dir" instead of "output.file" when providing named inputs'))}return i},Yh=e=>{const t=e.format;switch(t){case void 0:case"es":case"esm":case"module":return"es";case"cjs":case"commonjs":return"cjs";case"system":case"systemjs":return"system";case"amd":case"iife":case"umd":return t;default:return oe({message:'You must specify "output.format", which can be one of "amd", "cjs", "system", "es", "iife" or "umd".',url:"https://rollupjs.org/guide/en/#outputformat"})}},Qh=(e,t)=>{var s;const i=(null!==(s=e.inlineDynamicImports)&&void 0!==s?s:t.inlineDynamicImports)||!1,{input:n}=t;return i&&(Array.isArray(n)?n:Object.keys(n)).length>1?oe(de("output.inlineDynamicImports","outputinlinedynamicimports",'multiple inputs are not supported when "output.inlineDynamicImports" is true')):i},Zh=(e,t,s)=>{var i;const n=(null!==(i=e.preserveModules)&&void 0!==i?i:s.preserveModules)||!1;if(n){if(t)return oe(de("output.inlineDynamicImports","outputinlinedynamicimports",'this option is not supported for "output.preserveModules"'));if(!1===s.preserveEntrySignatures)return oe(de("preserveEntrySignatures","preserveentrysignatures",'setting this option to false is not supported for "output.preserveModules"'))}return n},Jh=(e,t)=>{const s=e.preferConst;return null!=s&&xe('The "output.preferConst" option is deprecated. Use the "output.generatedCode.constBindings" option instead.',!1,t),!!s},el=e=>{const{preserveModulesRoot:t}=e;if(null!=t)return M(t)},tl=e=>{const t={autoId:!1,basePath:"",define:"define",...e.amd};if((t.autoId||t.basePath)&&t.id)return oe(de("output.amd.id","outputamd",'this option cannot be used together with "output.amd.autoId"/"output.amd.basePath"'));if(t.basePath&&!t.autoId)return oe(de("output.amd.basePath","outputamd",'this option only works with "output.amd.autoId"'));let s;return s=t.autoId?{autoId:!0,basePath:t.basePath,define:t.define}:{autoId:!1,define:t.define,id:t.id},s},sl=(e,t)=>{const s=e[t];return"function"==typeof s?s:()=>s||""},il=(e,t)=>{const{dir:s}=e;return"string"==typeof s&&"string"==typeof t?oe(de("output.dir","outputdir",'you must set either "output.file" for a single-file build or "output.dir" when generating multiple chunks')):s},nl=(e,t)=>{const s=e.dynamicImportFunction;return s&&xe('The "output.dynamicImportFunction" option is deprecated. Use the "renderDynamicImport" plugin hook instead.',!1,t),s},rl=(e,t)=>{const s=e.entryFileNames;return null==s&&t.add("entryFileNames"),null!=s?s:"[name].js"};function al(e,t){const s=e.exports;if(null==s)t.add("exports");else if(!["default","named","none","auto"].includes(s))return oe((i=s,{code:le.INVALID_EXPORT_OPTION,message:`"output.exports" must be "default", "named", "none", "auto", or left unspecified (defaults to "auto"), received "${i}"`,url:"https://rollupjs.org/guide/en/#outputexports"}));var i;return s||"auto"}const ol=(e,t)=>{const s=_h(e.generatedCode,Nh,"output.generatedCode","");return{arrowFunctions:!0===s.arrowFunctions,constBindings:!0===s.constBindings||t,objectShorthand:!0===s.objectShorthand,reservedNamesAsProps:!0===s.reservedNamesAsProps,symbols:!0===s.symbols}},hl=(e,t)=>{if(t)return"";const s=e.indent;return!1===s?"":null==s||s},ll=new Set(["auto","esModule","default","defaultOnly",!0,!1]),cl=(e,t)=>{const s=e.interop,i=new Set,n=e=>{if(!i.has(e)){if(i.add(e),!ll.has(e))return oe(de("output.interop","outputinterop",`use one of ${Array.from(ll,(e=>JSON.stringify(e))).join(", ")}`,e));"boolean"==typeof e&&xe({message:`The boolean value "${e}" for the "output.interop" option is deprecated. Use ${e?'"auto"':'"esModule", "default" or "defaultOnly"'} instead.`,url:"https://rollupjs.org/guide/en/#outputinterop"},!1,t)}return e};if("function"==typeof s){const e=Object.create(null);let t=null;return i=>null===i?t||n(t=s(i)):i in e?e[i]:n(e[i]=s(i))}return void 0===s?()=>!0:()=>n(s)},ul=(e,t,s,i)=>{const n=e.manualChunks||i.manualChunks;if(n){if(t)return oe(de("output.manualChunks","outputmanualchunks",'this option is not supported for "output.inlineDynamicImports"'));if(s)return oe(de("output.manualChunks","outputmanualchunks",'this option is not supported for "output.preserveModules"'))}return n||{}},dl=(e,t,s)=>{var i;return null!==(i=e.minifyInternalExports)&&void 0!==i?i:s||"es"===t||"system"===t},pl=(e,t,s)=>{const i=e.namespaceToStringTag;return null!=i?(xe('The "output.namespaceToStringTag" option is deprecated. Use the "output.generatedCode.symbols" option instead.',!1,s),i):t.symbols||!1};function fl(e,t){e.forEach(((e,s)=>{e.name||(e.name=`${t}${s+1}`)}))}function ml(e,t,s,i,n){const{options:r,outputPluginDriver:a,unsetOptions:o}=function(e,t,s,i){if(!e)throw new Error("You must supply an options object");const n=Ph(e.plugins);fl(n,rh);const r=t.createOutputPluginDriver(n);return{...gl(s,i,e,r),outputPluginDriver:r}}(i,n.pluginDriver,t,s);return kh(0,(async()=>{const s=new la(r,o,t,a,n),i=await s.generate(e);if(e){if(!r.dir&&!r.file)return oe({code:"MISSING_OPTION",message:'You must specify "output.file" or "output.dir" for the build.'});await Promise.all(Object.values(i).map((e=>async function(e,t){const s=M(t.dir||$(t.file),e.fileName);let i,n;if(await sh.mkdir($(s),{recursive:!0}),"asset"===e.type)n=e.source;else if(n=e.code,t.sourcemap&&e.map){let r;"inline"===t.sourcemap?r=e.map.toUrl():(r=`${_(e.fileName)}.map`,i=sh.writeFile(`${s}.map`,e.map.toString())),"hidden"!==t.sourcemap&&(n+=`//# sourceMappingURL=${r}\n`)}return Promise.all([sh.writeFile(s,n),i])}(e,r)))),await a.hookParallel("writeBundle",[r,i])}return h=i,{output:Object.values(h).filter((e=>Object.keys(e).length>0)).sort(((e,t)=>{const s=El(e),i=El(t);return s===i?0:s<i?-1:1}))};var h}))}function gl(e,t,s,i){return function(e,t,s){var i,n,r,a,o,h,l;const c=new Set(s),u=e.compact||!1,d=Yh(e),p=Qh(e,t),f=Zh(e,p,t),m=Xh(e,f,t),g=Jh(e,t),y=ol(e,g),E={amd:tl(e),assetFileNames:null!==(i=e.assetFileNames)&&void 0!==i?i:"assets/[name]-[hash][extname]",banner:sl(e,"banner"),chunkFileNames:null!==(n=e.chunkFileNames)&&void 0!==n?n:"[name]-[hash].js",compact:u,dir:il(e,m),dynamicImportFunction:nl(e,t),entryFileNames:rl(e,c),esModule:null===(r=e.esModule)||void 0===r||r,exports:al(e,c),extend:e.extend||!1,externalLiveBindings:null===(a=e.externalLiveBindings)||void 0===a||a,file:m,footer:sl(e,"footer"),format:d,freeze:null===(o=e.freeze)||void 0===o||o,generatedCode:y,globals:e.globals||{},hoistTransitiveImports:null===(h=e.hoistTransitiveImports)||void 0===h||h,indent:hl(e,u),inlineDynamicImports:p,interop:cl(e,t),intro:sl(e,"intro"),manualChunks:ul(e,p,f,t),minifyInternalExports:dl(e,d,u),name:e.name,namespaceToStringTag:pl(e,y,t),noConflict:e.noConflict||!1,outro:sl(e,"outro"),paths:e.paths||{},plugins:Ph(e.plugins),preferConst:g,preserveModules:f,preserveModulesRoot:el(e),sanitizeFileName:"function"==typeof e.sanitizeFileName?e.sanitizeFileName:!1===e.sanitizeFileName?e=>e:Kh,sourcemap:e.sourcemap||!1,sourcemapExcludeSources:e.sourcemapExcludeSources||!1,sourcemapFile:e.sourcemapFile,sourcemapPathTransform:e.sourcemapPathTransform,strict:null===(l=e.strict)||void 0===l||l,systemNullSetters:e.systemNullSetters||!1,validate:e.validate||!1};return Ch(e,Object.keys(E),"output options",t.onwarn),{options:E,unsetOptions:c}}(i.hookReduceArg0Sync("outputOptions",[s.output||s],((e,t)=>t||e),(e=>{const t=()=>e.error({code:le.CANNOT_EMIT_FROM_OPTIONS_HOOK,message:'Cannot emit files or set asset sources in the "outputOptions" hook, use the "renderStart" hook instead.'});return{...e,emitFile:t,setAssetSource:t}})),e,t)}var yl;function El(e){return"asset"===e.type?yl.ASSET:e.isEntry?yl.ENTRY_CHUNK:yl.SECONDARY_CHUNK}!function(e){e[e.ENTRY_CHUNK=0]="ENTRY_CHUNK",e[e.SECONDARY_CHUNK=1]="SECONDARY_CHUNK",e[e.ASSET=2]="ASSET"}(yl||(yl={})),e.VERSION=t,e.defineConfig=function(e){return e},e.rollup=function(e){return async function(e,s){const{options:i,unsetOptions:n}=await async function(e,s){if(!e)throw new Error("You must supply an options object to rollup");const i=Ph(e.plugins),{options:n,unsetOptions:r}=function(e){var t,s,i;const n=new Set,r=null!==(t=e.context)&&void 0!==t?t:"undefined",a=Th(e),o=e.strictDeprecations||!1,h={acorn:Rh(e),acornInjectPlugins:Mh(e),cache:Dh(e),context:r,experimentalCacheExpiry:null!==(s=e.experimentalCacheExpiry)&&void 0!==s?s:10,external:Lh(e.external),inlineDynamicImports:Oh(e,a,o),input:Vh(e),makeAbsoluteExternalsRelative:null===(i=e.makeAbsoluteExternalsRelative)||void 0===i||i,manualChunks:Bh(e,a,o),maxParallelFileReads:Fh(e),moduleContext:zh(e,r),onwarn:a,perf:e.perf||!1,plugins:Ph(e.plugins),preserveEntrySignatures:Wh(e,n),preserveModules:jh(e,a,o),preserveSymlinks:e.preserveSymlinks||!1,shimMissingExports:e.shimMissingExports||!1,strictDeprecations:o,treeshake:Uh(e,a,o)};return Ch(e,[...Object.keys(h),"watch"],"input options",h.onwarn,/^(output)$/),{options:h,unsetOptions:n}}(await i.reduce(function(e){return async(s,i)=>i.options&&await i.options.call({meta:{rollupVersion:t,watchMode:e}},await s)||s}(s),Promise.resolve(e)));return fl(n.plugins,nh),{options:n,unsetOptions:r}}(e,null!==s);!function(e){e.perf?(sn=new Map,hn=rn,ln=an,e.plugins=e.plugins.map(un)):(hn=tn,ln=tn)}(i);const r=new Sh(i,s),a=!1!==e.cache;delete i.cache,delete e.cache,hn("BUILD",1),await kh(r.pluginDriver,(async()=>{try{await r.pluginDriver.hookParallel("buildStart",[i]),await r.build()}catch(e){const t=Object.keys(r.watchFiles);throw t.length>0&&(e.watchFiles=t),await r.pluginDriver.hookParallel("buildEnd",[e]),await r.pluginDriver.hookParallel("closeBundle",[]),e}await r.pluginDriver.hookParallel("buildEnd",[])})),ln("BUILD",1);const o={cache:a?r.getCache():void 0,async close(){o.closed||(o.closed=!0,await r.pluginDriver.hookParallel("closeBundle",[]))},closed:!1,generate:async e=>o.closed?oe(Ee()):ml(!1,i,n,e,r),watchFiles:Object.keys(r.watchFiles),write:async e=>o.closed?oe(Ee()):ml(!0,i,n,e,r)};return i.perf&&(o.getTimings=on),o}(e,null)},Object.defineProperty(e,"__esModule",{value:!0})},"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).rollup={});
//# sourceMappingURL=rollup.browser.js.map
