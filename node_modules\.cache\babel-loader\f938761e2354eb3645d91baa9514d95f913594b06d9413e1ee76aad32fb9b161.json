{"ast": null, "code": "import CSSMotion from './CSSMotion';\nimport CSSMotionList from './CSSMotionList';\nexport { CSSMotionList };\nexport default CSSMotion;", "map": {"version": 3, "names": ["CSSMotion", "CSSMotionList"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/index.js"], "sourcesContent": ["import CSSMotion from './CSSMotion';\nimport CSSMotionList from './CSSMotionList';\nexport { CSSMotionList };\nexport default CSSMotion;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASA,aAAa;AACtB,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}