{"ast": null, "code": "\"use strict\";\n\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\nvar getDotCount = function getDotCount(spec) {\n  var dots;\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots = Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) + 1;\n  }\n  return dots;\n};\nexport var Dots = /*#__PURE__*/function (_React$PureComponent) {\n  _inherits(Dots, _React$PureComponent);\n  var _super = _createSuper(Dots);\n  function Dots() {\n    _classCallCheck(this, Dots);\n    return _super.apply(this, arguments);\n  }\n  _createClass(Dots, [{\n    key: \"clickHandler\",\n    value: function clickHandler(options, e) {\n      // In Autoplay the focus stays on clicked button even after transition\n      // to next slide. That only goes away by click somewhere outside\n      e.preventDefault();\n      this.props.clickHandler(options);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        onMouseOver = _this$props.onMouseOver,\n        onMouseLeave = _this$props.onMouseLeave,\n        infinite = _this$props.infinite,\n        slidesToScroll = _this$props.slidesToScroll,\n        slidesToShow = _this$props.slidesToShow,\n        slideCount = _this$props.slideCount,\n        currentSlide = _this$props.currentSlide;\n      var dotCount = getDotCount({\n        slideCount: slideCount,\n        slidesToScroll: slidesToScroll,\n        slidesToShow: slidesToShow,\n        infinite: infinite\n      });\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      var dots = [];\n      for (var i = 0; i < dotCount; i++) {\n        var _rightBound = (i + 1) * slidesToScroll - 1;\n        var rightBound = infinite ? _rightBound : clamp(_rightBound, 0, slideCount - 1);\n        var _leftBound = rightBound - (slidesToScroll - 1);\n        var leftBound = infinite ? _leftBound : clamp(_leftBound, 0, slideCount - 1);\n        var className = classnames({\n          \"slick-active\": infinite ? currentSlide >= leftBound && currentSlide <= rightBound : currentSlide === leftBound\n        });\n        var dotOptions = {\n          message: \"dots\",\n          index: i,\n          slidesToScroll: slidesToScroll,\n          currentSlide: currentSlide\n        };\n        var onClick = this.clickHandler.bind(this, dotOptions);\n        dots = dots.concat(/*#__PURE__*/React.createElement(\"li\", {\n          key: i,\n          className: className\n        }, /*#__PURE__*/React.cloneElement(this.props.customPaging(i), {\n          onClick: onClick\n        })));\n      }\n      return /*#__PURE__*/React.cloneElement(this.props.appendDots(dots), _objectSpread({\n        className: this.props.dotsClass\n      }, mouseEvents));\n    }\n  }]);\n  return Dots;\n}(React.PureComponent);", "map": {"version": 3, "names": ["_objectSpread", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "classnames", "clamp", "getDotCount", "spec", "dots", "infinite", "Math", "ceil", "slideCount", "slidesToScroll", "slidesToShow", "Dots", "_React$PureComponent", "_super", "apply", "arguments", "key", "value", "clickHandler", "options", "e", "preventDefault", "props", "render", "_this$props", "onMouseEnter", "onMouseOver", "onMouseLeave", "currentSlide", "dotCount", "mouseEvents", "i", "_rightBound", "rightBound", "_leftBound", "leftBound", "className", "dotOptions", "message", "index", "onClick", "bind", "concat", "createElement", "cloneElement", "customPaging", "appendDots", "dotsClass", "PureComponent"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/react-slick/es/dots.js"], "sourcesContent": ["\"use strict\";\n\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\n\nvar getDotCount = function getDotCount(spec) {\n  var dots;\n\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots = Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) + 1;\n  }\n\n  return dots;\n};\n\nexport var Dots = /*#__PURE__*/function (_React$PureComponent) {\n  _inherits(Dots, _React$PureComponent);\n\n  var _super = _createSuper(Dots);\n\n  function Dots() {\n    _classCallCheck(this, Dots);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(Dots, [{\n    key: \"clickHandler\",\n    value: function clickHandler(options, e) {\n      // In Autoplay the focus stays on clicked button even after transition\n      // to next slide. That only goes away by click somewhere outside\n      e.preventDefault();\n      this.props.clickHandler(options);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          onMouseEnter = _this$props.onMouseEnter,\n          onMouseOver = _this$props.onMouseOver,\n          onMouseLeave = _this$props.onMouseLeave,\n          infinite = _this$props.infinite,\n          slidesToScroll = _this$props.slidesToScroll,\n          slidesToShow = _this$props.slidesToShow,\n          slideCount = _this$props.slideCount,\n          currentSlide = _this$props.currentSlide;\n      var dotCount = getDotCount({\n        slideCount: slideCount,\n        slidesToScroll: slidesToScroll,\n        slidesToShow: slidesToShow,\n        infinite: infinite\n      });\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      var dots = [];\n\n      for (var i = 0; i < dotCount; i++) {\n        var _rightBound = (i + 1) * slidesToScroll - 1;\n\n        var rightBound = infinite ? _rightBound : clamp(_rightBound, 0, slideCount - 1);\n\n        var _leftBound = rightBound - (slidesToScroll - 1);\n\n        var leftBound = infinite ? _leftBound : clamp(_leftBound, 0, slideCount - 1);\n        var className = classnames({\n          \"slick-active\": infinite ? currentSlide >= leftBound && currentSlide <= rightBound : currentSlide === leftBound\n        });\n        var dotOptions = {\n          message: \"dots\",\n          index: i,\n          slidesToScroll: slidesToScroll,\n          currentSlide: currentSlide\n        };\n        var onClick = this.clickHandler.bind(this, dotOptions);\n        dots = dots.concat( /*#__PURE__*/React.createElement(\"li\", {\n          key: i,\n          className: className\n        }, /*#__PURE__*/React.cloneElement(this.props.customPaging(i), {\n          onClick: onClick\n        })));\n      }\n\n      return /*#__PURE__*/React.cloneElement(this.props.appendDots(dots), _objectSpread({\n        className: this.props.dotsClass\n      }, mouseEvents));\n    }\n  }]);\n\n  return Dots;\n}(React.PureComponent);"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,0BAA0B;AAEhD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,IAAI;EAER,IAAID,IAAI,CAACE,QAAQ,EAAE;IACjBD,IAAI,GAAGE,IAAI,CAACC,IAAI,CAACJ,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACM,cAAc,CAAC;EACzD,CAAC,MAAM;IACLL,IAAI,GAAGE,IAAI,CAACC,IAAI,CAAC,CAACJ,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACO,YAAY,IAAIP,IAAI,CAACM,cAAc,CAAC,GAAG,CAAC;EACnF;EAEA,OAAOL,IAAI;AACb,CAAC;AAED,OAAO,IAAIO,IAAI,GAAG,aAAa,UAAUC,oBAAoB,EAAE;EAC7Df,SAAS,CAACc,IAAI,EAAEC,oBAAoB,CAAC;EAErC,IAAIC,MAAM,GAAGf,YAAY,CAACa,IAAI,CAAC;EAE/B,SAASA,IAAIA,CAAA,EAAG;IACdhB,eAAe,CAAC,IAAI,EAAEgB,IAAI,CAAC;IAE3B,OAAOE,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtC;EAEAnB,YAAY,CAACe,IAAI,EAAE,CAAC;IAClBK,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASC,YAAYA,CAACC,OAAO,EAAEC,CAAC,EAAE;MACvC;MACA;MACAA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC;IAClC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASM,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACF,KAAK;QACxBG,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,YAAY,GAAGH,WAAW,CAACG,YAAY;QACvCtB,QAAQ,GAAGmB,WAAW,CAACnB,QAAQ;QAC/BI,cAAc,GAAGe,WAAW,CAACf,cAAc;QAC3CC,YAAY,GAAGc,WAAW,CAACd,YAAY;QACvCF,UAAU,GAAGgB,WAAW,CAAChB,UAAU;QACnCoB,YAAY,GAAGJ,WAAW,CAACI,YAAY;MAC3C,IAAIC,QAAQ,GAAG3B,WAAW,CAAC;QACzBM,UAAU,EAAEA,UAAU;QACtBC,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA,YAAY;QAC1BL,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF,IAAIyB,WAAW,GAAG;QAChBL,YAAY,EAAEA,YAAY;QAC1BC,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA;MAChB,CAAC;MACD,IAAIvB,IAAI,GAAG,EAAE;MAEb,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;QACjC,IAAIC,WAAW,GAAG,CAACD,CAAC,GAAG,CAAC,IAAItB,cAAc,GAAG,CAAC;QAE9C,IAAIwB,UAAU,GAAG5B,QAAQ,GAAG2B,WAAW,GAAG/B,KAAK,CAAC+B,WAAW,EAAE,CAAC,EAAExB,UAAU,GAAG,CAAC,CAAC;QAE/E,IAAI0B,UAAU,GAAGD,UAAU,IAAIxB,cAAc,GAAG,CAAC,CAAC;QAElD,IAAI0B,SAAS,GAAG9B,QAAQ,GAAG6B,UAAU,GAAGjC,KAAK,CAACiC,UAAU,EAAE,CAAC,EAAE1B,UAAU,GAAG,CAAC,CAAC;QAC5E,IAAI4B,SAAS,GAAGpC,UAAU,CAAC;UACzB,cAAc,EAAEK,QAAQ,GAAGuB,YAAY,IAAIO,SAAS,IAAIP,YAAY,IAAIK,UAAU,GAAGL,YAAY,KAAKO;QACxG,CAAC,CAAC;QACF,IAAIE,UAAU,GAAG;UACfC,OAAO,EAAE,MAAM;UACfC,KAAK,EAAER,CAAC;UACRtB,cAAc,EAAEA,cAAc;UAC9BmB,YAAY,EAAEA;QAChB,CAAC;QACD,IAAIY,OAAO,GAAG,IAAI,CAACtB,YAAY,CAACuB,IAAI,CAAC,IAAI,EAAEJ,UAAU,CAAC;QACtDjC,IAAI,GAAGA,IAAI,CAACsC,MAAM,CAAE,aAAa3C,KAAK,CAAC4C,aAAa,CAAC,IAAI,EAAE;UACzD3B,GAAG,EAAEe,CAAC;UACNK,SAAS,EAAEA;QACb,CAAC,EAAE,aAAarC,KAAK,CAAC6C,YAAY,CAAC,IAAI,CAACtB,KAAK,CAACuB,YAAY,CAACd,CAAC,CAAC,EAAE;UAC7DS,OAAO,EAAEA;QACX,CAAC,CAAC,CAAC,CAAC;MACN;MAEA,OAAO,aAAazC,KAAK,CAAC6C,YAAY,CAAC,IAAI,CAACtB,KAAK,CAACwB,UAAU,CAAC1C,IAAI,CAAC,EAAEV,aAAa,CAAC;QAChF0C,SAAS,EAAE,IAAI,CAACd,KAAK,CAACyB;MACxB,CAAC,EAAEjB,WAAW,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOnB,IAAI;AACb,CAAC,CAACZ,KAAK,CAACiD,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}