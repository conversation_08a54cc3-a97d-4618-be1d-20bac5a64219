{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _en_US = _interopRequireDefault(require(\"rc-picker/lib/locale/en_US\"));\nvar _en_US2 = _interopRequireDefault(require(\"../../time-picker/locale/en_US\"));\n\n// Merge into a locale object\nvar locale = {\n  lang: (0, _extends2[\"default\"])({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, _en_US[\"default\"]),\n  timePickerLocale: (0, _extends2[\"default\"])({}, _en_US2[\"default\"])\n}; // All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\n\nvar _default = locale;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_en_US", "_en_US2", "locale", "lang", "placeholder", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "rangePlaceholder", "rangeYearPlaceholder", "rangeQuarterPlaceholder", "rangeMonthPlaceholder", "rangeWeekPlaceholder", "timePickerLocale", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/date-picker/locale/en_US.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _en_US = _interopRequireDefault(require(\"rc-picker/lib/locale/en_US\"));\n\nvar _en_US2 = _interopRequireDefault(require(\"../../time-picker/locale/en_US\"));\n\n// Merge into a locale object\nvar locale = {\n  lang: (0, _extends2[\"default\"])({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, _en_US[\"default\"]),\n  timePickerLocale: (0, _extends2[\"default\"])({}, _en_US2[\"default\"])\n}; // All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\n\nvar _default = locale;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAE1E,IAAIO,OAAO,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;;AAE/E;AACA,IAAIQ,MAAM,GAAG;EACXC,IAAI,EAAE,CAAC,CAAC,EAAEJ,SAAS,CAAC,SAAS,CAAC,EAAE;IAC9BK,WAAW,EAAE,aAAa;IAC1BC,eAAe,EAAE,aAAa;IAC9BC,kBAAkB,EAAE,gBAAgB;IACpCC,gBAAgB,EAAE,cAAc;IAChCC,eAAe,EAAE,aAAa;IAC9BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAC5CC,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAChDC,uBAAuB,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;IACzDC,qBAAqB,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;IACnDC,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU;EACjD,CAAC,EAAEb,MAAM,CAAC,SAAS,CAAC,CAAC;EACrBc,gBAAgB,EAAE,CAAC,CAAC,EAAEf,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEE,OAAO,CAAC,SAAS,CAAC;AACpE,CAAC,CAAC,CAAC;AACH;;AAEA,IAAIc,QAAQ,GAAGb,MAAM;AACrBL,OAAO,CAAC,SAAS,CAAC,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}