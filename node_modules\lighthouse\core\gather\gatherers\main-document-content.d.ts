export default MainDocumentContent;
/**
 * Collects the content of the main html document.
 */
declare class MainDocumentContent extends FRGatherer {
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Artifacts['DevtoolsLog']} devtoolsLog
     * @return {Promise<LH.Artifacts['MainDocumentContent']>}
     */
    _getArtifact(context: LH.Gatherer.FRTransitionalContext, devtoolsLog: LH.Artifacts['DevtoolsLog']): Promise<LH.Artifacts['MainDocumentContent']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['MainDocumentContent']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['MainDocumentContent']>;
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Artifacts['MainDocumentContent']>}
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Artifacts['MainDocumentContent']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=main-document-content.d.ts.map