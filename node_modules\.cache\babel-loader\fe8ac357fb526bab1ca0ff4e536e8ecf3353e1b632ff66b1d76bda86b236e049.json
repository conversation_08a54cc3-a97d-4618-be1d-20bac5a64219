{"ast": null, "code": "import * as React from 'react';\nvar ResizeContext = /*#__PURE__*/React.createContext(null);\nexport default ResizeContext;", "map": {"version": 3, "names": ["React", "ResizeContext", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/context/ResizeContext.js"], "sourcesContent": ["import * as React from 'react';\nvar ResizeContext = /*#__PURE__*/React.createContext(null);\nexport default ResizeContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1D,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}