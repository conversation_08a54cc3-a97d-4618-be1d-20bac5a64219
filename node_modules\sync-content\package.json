{"name": "sync-content", "version": "1.0.2", "description": "Synchronize the contents of one folder to another location, only copying files if contents differ.", "author": "<PERSON> <<EMAIL>> (https://blog.izs.me)", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "bin": "./dist/mjs/bin.mjs", "exports": {"./package.json": {"import": "./package.json", "require": "./package.json"}, ".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "files": ["dist"], "license": "BlueOak-1.0.0", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "dependencies": {"glob": "^10.2.6", "mkdirp": "^3.0.1", "path-scurry": "^1.9.2", "rimraf": "^5.0.1"}, "devDependencies": {"@types/node": "^20.1.5", "@types/tap": "^15.0.8", "c8": "^7.14.0", "prettier": "^2.8.6", "tap": "^16.3.6", "ts-node": "^10.9.1", "typedoc": "^0.24.7", "typescript": "^5.0.2"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": "https://github.com/isaacs/sync-content"}