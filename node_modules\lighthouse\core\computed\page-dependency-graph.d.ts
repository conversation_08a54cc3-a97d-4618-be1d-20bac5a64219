export { PageDependencyGraphComputed as PageDependencyGraph };
export type Node = import('../lib/dependency-graph/base-node.js').Node;
export type URLArtifact = Omit<LH.Artifacts['URL'], 'finalDisplayedUrl'>;
export type NetworkNodeOutput = {
    nodes: Array<NetworkNode>;
    idToNodeMap: Map<string, NetworkNode>;
    urlToNodeMap: Map<string, Array<NetworkNode>>;
    frameIdToNodeMap: Map<string, NetworkNode | null>;
};
declare const PageDependencyGraphComputed: typeof PageDependencyGraph & {
    request: (dependencies: {
        trace: LH.Trace;
        devtoolsLog: import("../index.js").DevtoolsLog;
        URL: LH.Artifacts['URL'];
    }, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../lib/dependency-graph/base-node.js").Node>;
};
import { NetworkNode } from "../lib/dependency-graph/network-node.js";
declare class PageDependencyGraph {
    /**
     * @param {LH.Artifacts.NetworkRequest} record
     * @return {Array<string>}
     */
    static getNetworkInitiators(record: LH.Artifacts.NetworkRequest): Array<string>;
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {NetworkNodeOutput}
     */
    static getNetworkNodeOutput(networkRecords: Array<LH.Artifacts.NetworkRequest>): NetworkNodeOutput;
    /**
     * @param {LH.Artifacts.ProcessedTrace} processedTrace
     * @return {Array<CPUNode>}
     */
    static getCPUNodes({ mainThreadEvents }: LH.Artifacts.ProcessedTrace): Array<CPUNode>;
    /**
     * @param {NetworkNode} rootNode
     * @param {NetworkNodeOutput} networkNodeOutput
     */
    static linkNetworkNodes(rootNode: NetworkNode, networkNodeOutput: NetworkNodeOutput): void;
    /**
     * @param {Node} rootNode
     * @param {NetworkNodeOutput} networkNodeOutput
     * @param {Array<CPUNode>} cpuNodes
     */
    static linkCPUNodes(rootNode: Node, networkNodeOutput: NetworkNodeOutput, cpuNodes: Array<CPUNode>): void;
    /**
     * Removes the given node from the graph, but retains all paths between its dependencies and
     * dependents.
     * @param {Node} node
     */
    static _pruneNode(node: Node): void;
    /**
     * @param {LH.Artifacts.ProcessedTrace} processedTrace
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {URLArtifact} URL
     * @return {Node}
     */
    static createGraph(processedTrace: LH.Artifacts.ProcessedTrace, networkRecords: Array<LH.Artifacts.NetworkRequest>, URL: URLArtifact): Node;
    /**
     *
     * @param {Node} rootNode
     */
    static printGraph(rootNode: Node, widthInCharacters?: number): void;
    /**
     * Recalculate `artifacts.URL` for clients that don't provide it.
     *
     * @param {LH.DevtoolsLog} devtoolsLog
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @param {LH.Artifacts.ProcessedTrace} processedTrace
     * @return {URLArtifact}
     */
    static getDocumentUrls(devtoolsLog: import("../index.js").DevtoolsLog, networkRecords: LH.Artifacts.NetworkRequest[], processedTrace: LH.Artifacts.ProcessedTrace): URLArtifact;
    /**
     * @param {{trace: LH.Trace, devtoolsLog: LH.DevtoolsLog, URL: LH.Artifacts['URL']}} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<Node>}
     */
    static compute_(data: {
        trace: LH.Trace;
        devtoolsLog: import("../index.js").DevtoolsLog;
        URL: LH.Artifacts['URL'];
    }, context: LH.Artifacts.ComputedContext): Promise<Node>;
}
import { NetworkRequest } from "../lib/network-request.js";
import { ProcessedTrace } from "./processed-trace.js";
import { CPUNode } from "../lib/dependency-graph/cpu-node.js";
//# sourceMappingURL=page-dependency-graph.d.ts.map