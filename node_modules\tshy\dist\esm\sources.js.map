{"version": 3, "file": "sources.js", "sourceRoot": "", "sources": ["../../src/sources.ts"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,OAAO,EAAE,WAAW,EAAE,MAAM,IAAI,CAAA;AAChC,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AAEjC,MAAM,UAAU,GAAG,CAAC,GAAG,GAAG,KAAK,EAAY,EAAE;IAC3C,MAAM,OAAO,GAAa,EAAE,CAAA;IAC5B,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;IACzD,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAA;QAClC,IAAI,CAAC,CAAC,MAAM,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aAC1B,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;QAChC,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAED,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC,CAAA;AACrC,eAAe,OAAO,CAAA", "sourcesContent": ["// get the list of sources in ./src\n\nimport { readdirSync } from 'fs'\nimport { join } from 'path/posix'\n\nconst getSources = (dir = 'src'): string[] => {\n  const sources: string[] = []\n  const entries = readdirSync(dir, { withFileTypes: true })\n  for (const e of entries) {\n    const j = `./${join(dir, e.name)}`\n    if (e.isFile()) sources.push(j)\n    else if (e.isDirectory()) {\n      sources.push(...getSources(j))\n    }\n  }\n  return sources\n}\n\nconst sources = new Set(getSources())\nexport default sources\n"]}