{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\ring\\\\composizioneMagazzino.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* OpJobsUscita - operazioni sulle lavorazioni in uscita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { AggiungiCommposizioneMagazzino } from \"../../aggiunta_dati/aggiungiCommposizioneMagazzino\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { InputText } from \"primereact/inputtext\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ComposizioneMagazzinoRing extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      area: \"\",\n      scaffale: \"\",\n      ripiano: \"\",\n      posizione: \"\",\n      eanCode: \"\"\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      result: this.emptyResult,\n      value1: null,\n      value2: null,\n      value3: null,\n      value4: null,\n      value5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      deleteResultDialog: false,\n      selectedWarehouse: (() => {\n        try {\n          var _user$warehousesCross, _user$warehousesCross2;\n          const user = JSON.parse(localStorage.getItem('user') || '{}');\n          return (user === null || user === void 0 ? void 0 : (_user$warehousesCross = user.warehousesCross) === null || _user$warehousesCross === void 0 ? void 0 : (_user$warehousesCross2 = _user$warehousesCross[0]) === null || _user$warehousesCross2 === void 0 ? void 0 : _user$warehousesCross2.idWarehouse) || null;\n        } catch (error) {\n          console.warn('Failed to parse user data for selectedWarehouse:', error);\n          return null;\n        }\n      })(),\n      displayed: false,\n      loading: true\n    };\n    this.aggiungiCompMag = this.aggiungiCompMag.bind(this);\n    this.hideCompMag = this.hideCompMag.bind(this);\n    this.modificaComp = this.modificaComp.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.modifica = this.modifica.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var url = 'warehousescomp?idWarehouse=' + this.state.selectedWarehouse;\n    await APIRequest(\"GET\", url).then(res => {\n      this.setState({\n        results: res.data,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  aggiungiCompMag() {\n    this.setState({\n      resultDialog: true\n    });\n  }\n  hideCompMag() {\n    this.setState({\n      resultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"warehousescomp/?idComp=\" + this.state.result.id;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    window.location.reload();\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Posizione eliminata con successo\",\n      life: 3000\n    });\n  }\n  modificaComp(result) {\n    this.setState({\n      result,\n      resultDialog2: true,\n      value1: result.area,\n      value2: result.scaffale,\n      value3: result.ripiano,\n      value4: result.posizione,\n      value5: result.eanCode\n    });\n  }\n  hideDialog() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  async modifica() {\n    var body = {\n      composition: {\n        id: this.state.result.id,\n        area: this.state.value1,\n        scaffale: this.state.value2,\n        ripiano: this.state.value3,\n        posizione: this.state.value4,\n        eanCode: this.state.value5\n      }\n    };\n    await APIRequest('PUT', 'warehousescomp', body).then(async res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"Posizione modificata con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile modificare la Posizione. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideCompMag,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 13\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: 'id',\n      header: \"Id\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'area',\n      header: Costanti.Area,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'scaffale',\n      header: Costanti.Scaffale,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'ripiano',\n      header: Costanti.Ripiano,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'posizione',\n      header: Costanti.Posizione,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'eanCode',\n      header: Costanti.eanCode,\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.Modifica,\n      handler: this.modificaComp\n    }, {\n      name: Costanti.Elimina,\n      handler: this.confirmDeleteResult\n    }];\n    const items = [{\n      label: Costanti.AggCompMag,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiCompMag();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Composizione\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          actionsColumn: actionFields,\n          fileNames: \"ComposizioneMagazzino\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.AggCompMag,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideCompMag,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(AggiungiCommposizioneMagazzino, {\n          idWarehouse: this.state.selectedWarehouse\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeletePos, \" \", Costanti.Area, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.area\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 74\n            }, this), \" \", Costanti.Scaffale, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.scaffale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 127\n            }, this), \" \", Costanti.Ripiano, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.ripiano\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 183\n            }, this), \" \", Costanti.Posizione, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.posizione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 240\n            }, this), \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        style: {\n          width: '800px'\n        },\n        header: Costanti.Modifica,\n        modal: true,\n        className: \"p-fluid\",\n        footer: resultDialogFooter2,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3 d-flex justify-content-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"area\",\n                  children: Costanti.Area\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  inputId: \"area\",\n                  value: this.state.value1,\n                  onChange: e => this.setState({\n                    value1: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"scaffale\",\n                  children: Costanti.Scaffale\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"scaffale\",\n                  value: this.state.value2,\n                  onChange: e => this.setState({\n                    value2: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"ripiano\",\n                  children: Costanti.Ripiano\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"ripiano\",\n                  value: this.state.value3,\n                  onChange: e => this.setState({\n                    value3: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"posizione\",\n                  children: Costanti.Posizione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"posizione\",\n                  value: this.state.value4,\n                  onChange: e => this.setState({\n                    value4: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 mt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"p-float-label\",\n                children: [/*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"eanCode\",\n                  value: this.state.value5,\n                  onChange: e => this.setState({\n                    value5: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"eanCode\",\n                  children: Costanti.eanCode\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 d-flex justify-content-end\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  className: \"p-button\",\n                  onClick: this.modifica,\n                  children: [Costanti.Conferma, /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-check ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default ComposizioneMagazzinoRing;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "AggiungiCommposizioneMagazzino", "<PERSON><PERSON>", "Dialog", "InputText", "Nav", "CustomDataTable", "Caricamento", "jsxDEV", "_jsxDEV", "ComposizioneMagazzinoRing", "constructor", "props", "emptyResult", "id", "area", "scaffale", "<PERSON><PERSON>", "posizione", "eanCode", "state", "results", "results2", "result", "value1", "value2", "value3", "value4", "value5", "resultDialog", "resultDialog2", "deleteResultDialog", "selectedWarehouse", "_user$warehousesCross", "_user$warehousesCross2", "user", "JSON", "parse", "localStorage", "getItem", "warehousesCross", "idWarehouse", "error", "console", "warn", "displayed", "loading", "aggiungiCompMag", "bind", "hideCompMag", "modificaComp", "confirmDeleteResult", "hideDeleteResultDialog", "deleteResult", "hideDialog", "modifica", "componentDidMount", "url", "then", "res", "setState", "data", "catch", "e", "_e$response", "_e$response2", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "filter", "val", "window", "location", "reload", "body", "composition", "setTimeout", "_e$response3", "_e$response4", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "deleteResultDialogFooter", "label", "icon", "Si", "fields", "field", "header", "sortable", "showHeader", "Area", "<PERSON><PERSON><PERSON><PERSON>", "Ripiano", "Posizione", "actionFields", "name", "Modifica", "handler", "Elimina", "items", "AggCompMag", "command", "ref", "el", "Composizione", "dt", "value", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "splitButtonClass", "actionsColumn", "fileNames", "visible", "modal", "footer", "onHide", "Conferma", "style", "fontSize", "ResDeletePos", "width", "htmlFor", "inputId", "onChange", "target"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/ring/composizioneMagazzino.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* OpJobsUscita - operazioni sulle lavorazioni in uscita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { AggiungiCommposizioneMagazzino } from \"../../aggiunta_dati/aggiungiCommposizioneMagazzino\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { InputText } from \"primereact/inputtext\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\n\nclass ComposizioneMagazzinoRing extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        area: \"\",\n        scaffale: \"\",\n        ripiano: \"\",\n        posizione: \"\",\n        eanCode: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            result: this.emptyResult,\n            value1: null,\n            value2: null,\n            value3: null,\n            value4: null,\n            value5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            deleteResultDialog: false,\n            selectedWarehouse: (() => {\n                try {\n                    const user = JSON.parse(localStorage.getItem('user') || '{}')\n                    return user?.warehousesCross?.[0]?.idWarehouse || null\n                } catch (error) {\n                    console.warn('Failed to parse user data for selectedWarehouse:', error)\n                    return null\n                }\n            })(),\n            displayed: false,\n            loading: true\n        }\n        this.aggiungiCompMag = this.aggiungiCompMag.bind(this);\n        this.hideCompMag = this.hideCompMag.bind(this);\n        this.modificaComp = this.modificaComp.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.modifica = this.modifica.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var url = 'warehousescomp?idWarehouse=' + this.state.selectedWarehouse;\n        await APIRequest(\"GET\", url)\n            .then(res => {\n                this.setState({\n                    results: res.data,\n                    loading: false\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    aggiungiCompMag() {\n        this.setState({\n            resultDialog: true\n        })\n    }\n    hideCompMag() {\n        this.setState({\n            resultDialog: false\n        })\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Chiusura dialogo eliminazione senza azioni\n    hideDeleteResultDialog() {\n        this.setState({ deleteResultDialog: false });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"warehousescomp/?idComp=\" + this.state.result.id;\n        var res = await APIRequest(\"DELETE\", url);\n        console.log(res.data);\n        window.location.reload();\n        this.toast.show({\n            severity: \"success\",\n            summary: \"Successful\",\n            detail: \"Posizione eliminata con successo\",\n            life: 3000,\n        });\n    }\n    modificaComp(result) {\n        this.setState({\n            result,\n            resultDialog2: true,\n            value1: result.area,\n            value2: result.scaffale,\n            value3: result.ripiano,\n            value4: result.posizione,\n            value5: result.eanCode\n        });\n    }\n    hideDialog() {\n        this.setState({\n            resultDialog2: false\n        });\n    }\n    async modifica() {\n        var body = {\n            composition: {\n                id: this.state.result.id,\n                area: this.state.value1,\n                scaffale: this.state.value2,\n                ripiano: this.state.value3,\n                posizione: this.state.value4,\n                eanCode: this.state.value5,\n            }\n        }\n        await APIRequest('PUT', 'warehousescomp', body)\n            .then(async res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Posizione modificata con successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile modificare la Posizione. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dell'aggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideCompMag}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideDialog} > {Costanti.Chiudi} </Button>\n            </React.Fragment>\n        );\n        //Elementi di conferma o annullamento del dialogo di cancellazione\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            { field: 'id', header: \"Id\", sortable: true, showHeader: true },\n            { field: 'area', header: Costanti.Area, sortable: true, showHeader: true },\n            { field: 'scaffale', header: Costanti.Scaffale, sortable: true, showHeader: true },\n            { field: 'ripiano', header: Costanti.Ripiano, sortable: true, showHeader: true },\n            { field: 'posizione', header: Costanti.Posizione, sortable: true, showHeader: true },\n            { field: 'eanCode', header: Costanti.eanCode, sortable: true, showHeader: true }\n        ];\n        const actionFields = [\n            { name: Costanti.Modifica, handler: this.modificaComp },\n            { name: Costanti.Elimina, handler: this.confirmDeleteResult }\n        ];\n        const items = [\n            {\n                label: Costanti.AggCompMag,\n                icon: 'pi pi-plus-circle',\n                command: () => {\n                    this.aggiungiCompMag()\n                }\n            },\n        ]\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.Composizione}</h1>\n                </div>\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        paginator\n                        rows={20}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        splitButtonClass={true}\n                        items={items}\n                        actionsColumn={actionFields}\n                        fileNames=\"ComposizioneMagazzino\"\n                    />\n                </div>\n                {/* Struttura dialogo per la composizione del magazzino */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={Costanti.AggCompMag}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hideCompMag}\n                >\n                    <Caricamento />\n                    <AggiungiCommposizioneMagazzino idWarehouse={this.state.selectedWarehouse} />\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeletePos} {Costanti.Area}: <b>{this.state.result.area}</b> {Costanti.Scaffale}: <b>{this.state.result.scaffale}</b> {Costanti.Ripiano}: <b>{this.state.result.ripiano}</b> {Costanti.Posizione}: <b>{this.state.result.posizione}</b>?\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog visible={this.state.resultDialog2} style={{ width: '800px' }} header={Costanti.Modifica} modal className=\"p-fluid\" footer={resultDialogFooter2} onHide={this.hideDialog}>\n                    <div className='my-3 d-flex justify-content-center'>\n                        <div className='row'>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"area\">{Costanti.Area}</label>\n                                    <InputText inputId='area' value={this.state.value1} onChange={(e) => this.setState({ value1: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"scaffale\">{Costanti.Scaffale}</label>\n                                    <InputText id='scaffale' value={this.state.value2} onChange={(e) => this.setState({ value2: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"ripiano\">{Costanti.Ripiano}</label>\n                                    <InputText id='ripiano' value={this.state.value3} onChange={(e) => this.setState({ value3: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"posizione\">{Costanti.Posizione}</label>\n                                    <InputText id='posizione' value={this.state.value4} onChange={(e) => this.setState({ value4: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 mt-4'>\n                                <span className=\"p-float-label\" >\n                                    <InputText id='eanCode' value={this.state.value5} onChange={(e) => this.setState({ value5: e.target.value })} />\n                                    <label htmlFor=\"eanCode\">{Costanti.eanCode}</label>\n                                </span>\n                            </div>\n                            <div className='col-12 d-flex justify-content-end'>\n                                <div className='mt-4'>\n                                    <Button\n                                        className=\"p-button\"\n                                        onClick={this.modifica}\n                                    >\n                                        {Costanti.Conferma}\n                                        <i className='pi pi-check ml-2'></i>\n                                    </Button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </Dialog>\n            </div>\n        )\n    }\n}\n\nexport default ComposizioneMagazzinoRing;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,8BAA8B,QAAQ,oDAAoD;AACnG,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,yBAAyB,SAASb,SAAS,CAAC;EAU9Cc,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAVhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE;IACb,CAAC;IAGG,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAACV,WAAW;MACxBW,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,iBAAiB,EAAE,CAAC,MAAM;QACtB,IAAI;UAAA,IAAAC,qBAAA,EAAAC,sBAAA;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;UAC7D,OAAO,CAAAJ,IAAI,aAAJA,IAAI,wBAAAF,qBAAA,GAAJE,IAAI,CAAEK,eAAe,cAAAP,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4BO,WAAW,KAAI,IAAI;QAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;UACZC,OAAO,CAACC,IAAI,CAAC,kDAAkD,EAAEF,KAAK,CAAC;UACvE,OAAO,IAAI;QACf;MACJ,CAAC,EAAE,CAAC;MACJG,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACG,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACH,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACI,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACK,YAAY,GAAG,IAAI,CAACA,YAAY,CAACL,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACM,UAAU,GAAG,IAAI,CAACA,UAAU,CAACN,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACO,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACP,IAAI,CAAC,IAAI,CAAC;EAC5C;EACA;EACA,MAAMQ,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,GAAG,GAAG,6BAA6B,GAAG,IAAI,CAACrC,KAAK,CAACY,iBAAiB;IACtE,MAAMhC,UAAU,CAAC,KAAK,EAAEyD,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;MACT,IAAI,CAACC,QAAQ,CAAC;QACVvC,OAAO,EAAEsC,GAAG,CAACE,IAAI;QACjBf,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CAACgB,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACZtB,OAAO,CAACuB,GAAG,CAACH,CAAC,CAAC;MACd,IAAI,CAACI,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAR,WAAA,GAAAD,CAAC,CAACU,QAAQ,cAAAT,WAAA,uBAAVA,WAAA,CAAYH,IAAI,MAAKa,SAAS,IAAAT,YAAA,GAAGF,CAAC,CAACU,QAAQ,cAAAR,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,GAAGE,CAAC,CAACY,OAAO,CAAE;QAC9JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA7B,eAAeA,CAAA,EAAG;IACd,IAAI,CAACa,QAAQ,CAAC;MACV/B,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAoB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACW,QAAQ,CAAC;MACV/B,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACAsB,mBAAmBA,CAAC5B,MAAM,EAAE;IACxB,IAAI,CAACqC,QAAQ,CAAC;MACVrC,MAAM;MACNQ,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACAqB,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACQ,QAAQ,CAAC;MAAE7B,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAChD;EACA;EACA,MAAMsB,YAAYA,CAAA,EAAG;IACjB,IAAIhC,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAACwD,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAAChE,EAAE,KAAK,IAAI,CAACM,KAAK,CAACG,MAAM,CAACT,EAC1C,CAAC;IACD,IAAI,CAAC8C,QAAQ,CAAC;MACVvC,OAAO;MACPU,kBAAkB,EAAE,KAAK;MACzBR,MAAM,EAAE,IAAI,CAACV;IACjB,CAAC,CAAC;IACF,IAAI4C,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACrC,KAAK,CAACG,MAAM,CAACT,EAAE;IAC1D,IAAI6C,GAAG,GAAG,MAAM3D,UAAU,CAAC,QAAQ,EAAEyD,GAAG,CAAC;IACzCd,OAAO,CAACuB,GAAG,CAACP,GAAG,CAACE,IAAI,CAAC;IACrBkB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IACxB,IAAI,CAACd,KAAK,CAACC,IAAI,CAAC;MACZC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,YAAY;MACrBC,MAAM,EAAE,kCAAkC;MAC1CK,IAAI,EAAE;IACV,CAAC,CAAC;EACN;EACA1B,YAAYA,CAAC3B,MAAM,EAAE;IACjB,IAAI,CAACqC,QAAQ,CAAC;MACVrC,MAAM;MACNO,aAAa,EAAE,IAAI;MACnBN,MAAM,EAAED,MAAM,CAACR,IAAI;MACnBU,MAAM,EAAEF,MAAM,CAACP,QAAQ;MACvBU,MAAM,EAAEH,MAAM,CAACN,OAAO;MACtBU,MAAM,EAAEJ,MAAM,CAACL,SAAS;MACxBU,MAAM,EAAEL,MAAM,CAACJ;IACnB,CAAC,CAAC;EACN;EACAmC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACM,QAAQ,CAAC;MACV9B,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA,MAAMyB,QAAQA,CAAA,EAAG;IACb,IAAI2B,IAAI,GAAG;MACPC,WAAW,EAAE;QACTrE,EAAE,EAAE,IAAI,CAACM,KAAK,CAACG,MAAM,CAACT,EAAE;QACxBC,IAAI,EAAE,IAAI,CAACK,KAAK,CAACI,MAAM;QACvBR,QAAQ,EAAE,IAAI,CAACI,KAAK,CAACK,MAAM;QAC3BR,OAAO,EAAE,IAAI,CAACG,KAAK,CAACM,MAAM;QAC1BR,SAAS,EAAE,IAAI,CAACE,KAAK,CAACO,MAAM;QAC5BR,OAAO,EAAE,IAAI,CAACC,KAAK,CAACQ;MACxB;IACJ,CAAC;IACD,MAAM5B,UAAU,CAAC,KAAK,EAAE,gBAAgB,EAAEkF,IAAI,CAAC,CAC1CxB,IAAI,CAAC,MAAMC,GAAG,IAAI;MACfhB,OAAO,CAACuB,GAAG,CAACP,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACM,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,mCAAmC;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;MACpHQ,UAAU,CAAC,MAAM;QACbL,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAACnB,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAsB,YAAA,EAAAC,YAAA;MACZ3C,OAAO,CAACuB,GAAG,CAACH,CAAC,CAAC;MACd,IAAI,CAACI,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAa,YAAA,GAAAtB,CAAC,CAACU,QAAQ,cAAAY,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,MAAKa,SAAS,IAAAY,YAAA,GAAGvB,CAAC,CAACU,QAAQ,cAAAa,YAAA,uBAAVA,YAAA,CAAYzB,IAAI,GAAGE,CAAC,CAACY,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC/N,CAAC,CAAC;EACV;EACAW,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpB/E,OAAA,CAACb,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,eACXjF,OAAA,CAACP,MAAM;QAACyF,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC3C,WAAY;QAAAyC,QAAA,GACvD,GAAG,EACH3F,QAAQ,CAAC8F,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMC,mBAAmB,gBACrBzF,OAAA,CAACb,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,eACXjF,OAAA,CAACP,MAAM;QAACyF,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACtC,UAAW;QAAAoC,QAAA,GAAE,GAAC,EAAC3F,QAAQ,CAAC8F,MAAM,EAAC,GAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACnB;IACD;IACA,MAAME,wBAAwB,gBAC1B1F,OAAA,CAACb,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,gBACXjF,OAAA,CAACP,MAAM;QACHkG,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBV,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAACxC;MAAuB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFxF,OAAA,CAACP,MAAM;QAACyF,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACvC,YAAa;QAAAqC,QAAA,GACxD,GAAG,EACH3F,QAAQ,CAACuG,EAAE,EAAE,GAAG;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMM,MAAM,GAAG,CACX;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC/D;MAAEH,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE1G,QAAQ,CAAC6G,IAAI;MAAEF,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC1E;MAAEH,KAAK,EAAE,UAAU;MAAEC,MAAM,EAAE1G,QAAQ,CAAC8G,QAAQ;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAClF;MAAEH,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAE1G,QAAQ,CAAC+G,OAAO;MAAEJ,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAChF;MAAEH,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE1G,QAAQ,CAACgH,SAAS;MAAEL,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACpF;MAAEH,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAE1G,QAAQ,CAACoB,OAAO;MAAEuF,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CACnF;IACD,MAAMK,YAAY,GAAG,CACjB;MAAEC,IAAI,EAAElH,QAAQ,CAACmH,QAAQ;MAAEC,OAAO,EAAE,IAAI,CAACjE;IAAa,CAAC,EACvD;MAAE+D,IAAI,EAAElH,QAAQ,CAACqH,OAAO;MAAED,OAAO,EAAE,IAAI,CAAChE;IAAoB,CAAC,CAChE;IACD,MAAMkE,KAAK,GAAG,CACV;MACIjB,KAAK,EAAErG,QAAQ,CAACuH,UAAU;MAC1BjB,IAAI,EAAE,mBAAmB;MACzBkB,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACxE,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CACJ;IACD,oBACItC,OAAA;MAAKkF,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CjF,OAAA,CAACX,KAAK;QAAC0H,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACtD,KAAK,GAAGsD;MAAG;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCxF,OAAA,CAACJ,GAAG;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPxF,OAAA;QAAKkF,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCjF,OAAA;UAAAiF,QAAA,EAAK3F,QAAQ,CAAC2H;QAAY;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACNxF,OAAA;QAAKkF,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBjF,OAAA,CAACH,eAAe;UACZkH,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACE,EAAE,GAAGF,EAAG;UAC1BG,KAAK,EAAE,IAAI,CAACxG,KAAK,CAACC,OAAQ;UAC1BkF,MAAM,EAAEA,MAAO;UACfzD,OAAO,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,OAAQ;UAC5B+E,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBb,KAAK,EAAEA,KAAM;UACbc,aAAa,EAAEnB,YAAa;UAC5BoB,SAAS,EAAC;QAAuB;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENxF,OAAA,CAACN,MAAM;QACHkI,OAAO,EAAE,IAAI,CAACjH,KAAK,CAACS,YAAa;QACjC4E,MAAM,EAAE1G,QAAQ,CAACuH,UAAW;QAC5BgB,KAAK;QACL3C,SAAS,EAAC,kBAAkB;QAC5B4C,MAAM,EAAE/C,kBAAmB;QAC3BgD,MAAM,EAAE,IAAI,CAACvF,WAAY;QAAAyC,QAAA,gBAEzBjF,OAAA,CAACF,WAAW;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfxF,OAAA,CAACR,8BAA8B;UAACwC,WAAW,EAAE,IAAI,CAACrB,KAAK,CAACY;QAAkB;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAETxF,OAAA,CAACN,MAAM;QACHkI,OAAO,EAAE,IAAI,CAACjH,KAAK,CAACW,kBAAmB;QACvC0E,MAAM,EAAE1G,QAAQ,CAAC0I,QAAS;QAC1BH,KAAK;QACLC,MAAM,EAAEpC,wBAAyB;QACjCqC,MAAM,EAAE,IAAI,CAACpF,sBAAuB;QAAAsC,QAAA,eAEpCjF,OAAA;UAAKkF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCjF,OAAA;YACIkF,SAAS,EAAC,mCAAmC;YAC7C+C,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAAC7E,KAAK,CAACG,MAAM,iBACdd,OAAA;YAAAiF,QAAA,GACK3F,QAAQ,CAAC6I,YAAY,EAAC,GAAC,EAAC7I,QAAQ,CAAC6G,IAAI,EAAC,IAAE,eAAAnG,OAAA;cAAAiF,QAAA,EAAI,IAAI,CAACtE,KAAK,CAACG,MAAM,CAACR;YAAI;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAAClG,QAAQ,CAAC8G,QAAQ,EAAC,IAAE,eAAApG,OAAA;cAAAiF,QAAA,EAAI,IAAI,CAACtE,KAAK,CAACG,MAAM,CAACP;YAAQ;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAAClG,QAAQ,CAAC+G,OAAO,EAAC,IAAE,eAAArG,OAAA;cAAAiF,QAAA,EAAI,IAAI,CAACtE,KAAK,CAACG,MAAM,CAACN;YAAO;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAAClG,QAAQ,CAACgH,SAAS,EAAC,IAAE,eAAAtG,OAAA;cAAAiF,QAAA,EAAI,IAAI,CAACtE,KAAK,CAACG,MAAM,CAACL;YAAS;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KACvP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETxF,OAAA,CAACN,MAAM;QAACkI,OAAO,EAAE,IAAI,CAACjH,KAAK,CAACU,aAAc;QAAC4G,KAAK,EAAE;UAAEG,KAAK,EAAE;QAAQ,CAAE;QAACpC,MAAM,EAAE1G,QAAQ,CAACmH,QAAS;QAACoB,KAAK;QAAC3C,SAAS,EAAC,SAAS;QAAC4C,MAAM,EAAErC,mBAAoB;QAACsC,MAAM,EAAE,IAAI,CAAClF,UAAW;QAAAoC,QAAA,eAC5KjF,OAAA;UAAKkF,SAAS,EAAC,oCAAoC;UAAAD,QAAA,eAC/CjF,OAAA;YAAKkF,SAAS,EAAC,KAAK;YAAAD,QAAA,gBAChBjF,OAAA;cAAKkF,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CjF,OAAA;gBAAMkF,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCjF,OAAA;kBAAOqI,OAAO,EAAC,MAAM;kBAAApD,QAAA,EAAE3F,QAAQ,CAAC6G;gBAAI;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7CxF,OAAA,CAACL,SAAS;kBAAC2I,OAAO,EAAC,MAAM;kBAACnB,KAAK,EAAE,IAAI,CAACxG,KAAK,CAACI,MAAO;kBAACwH,QAAQ,EAAGjF,CAAC,IAAK,IAAI,CAACH,QAAQ,CAAC;oBAAEpC,MAAM,EAAEuC,CAAC,CAACkF,MAAM,CAACrB;kBAAM,CAAC;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxF,OAAA;cAAKkF,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CjF,OAAA;gBAAMkF,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCjF,OAAA;kBAAOqI,OAAO,EAAC,UAAU;kBAAApD,QAAA,EAAE3F,QAAQ,CAAC8G;gBAAQ;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrDxF,OAAA,CAACL,SAAS;kBAACU,EAAE,EAAC,UAAU;kBAAC8G,KAAK,EAAE,IAAI,CAACxG,KAAK,CAACK,MAAO;kBAACuH,QAAQ,EAAGjF,CAAC,IAAK,IAAI,CAACH,QAAQ,CAAC;oBAAEnC,MAAM,EAAEsC,CAAC,CAACkF,MAAM,CAACrB;kBAAM,CAAC;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxF,OAAA;cAAKkF,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CjF,OAAA;gBAAMkF,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCjF,OAAA;kBAAOqI,OAAO,EAAC,SAAS;kBAAApD,QAAA,EAAE3F,QAAQ,CAAC+G;gBAAO;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnDxF,OAAA,CAACL,SAAS;kBAACU,EAAE,EAAC,SAAS;kBAAC8G,KAAK,EAAE,IAAI,CAACxG,KAAK,CAACM,MAAO;kBAACsH,QAAQ,EAAGjF,CAAC,IAAK,IAAI,CAACH,QAAQ,CAAC;oBAAElC,MAAM,EAAEqC,CAAC,CAACkF,MAAM,CAACrB;kBAAM,CAAC;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxF,OAAA;cAAKkF,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CjF,OAAA;gBAAMkF,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCjF,OAAA;kBAAOqI,OAAO,EAAC,WAAW;kBAAApD,QAAA,EAAE3F,QAAQ,CAACgH;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDxF,OAAA,CAACL,SAAS;kBAACU,EAAE,EAAC,WAAW;kBAAC8G,KAAK,EAAE,IAAI,CAACxG,KAAK,CAACO,MAAO;kBAACqH,QAAQ,EAAGjF,CAAC,IAAK,IAAI,CAACH,QAAQ,CAAC;oBAAEjC,MAAM,EAAEoC,CAAC,CAACkF,MAAM,CAACrB;kBAAM,CAAC;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxF,OAAA;cAAKkF,SAAS,EAAC,aAAa;cAAAD,QAAA,eACxBjF,OAAA;gBAAMkF,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC3BjF,OAAA,CAACL,SAAS;kBAACU,EAAE,EAAC,SAAS;kBAAC8G,KAAK,EAAE,IAAI,CAACxG,KAAK,CAACQ,MAAO;kBAACoH,QAAQ,EAAGjF,CAAC,IAAK,IAAI,CAACH,QAAQ,CAAC;oBAAEhC,MAAM,EAAEmC,CAAC,CAACkF,MAAM,CAACrB;kBAAM,CAAC;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChHxF,OAAA;kBAAOqI,OAAO,EAAC,SAAS;kBAAApD,QAAA,EAAE3F,QAAQ,CAACoB;gBAAO;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxF,OAAA;cAAKkF,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CjF,OAAA;gBAAKkF,SAAS,EAAC,MAAM;gBAAAD,QAAA,eACjBjF,OAAA,CAACP,MAAM;kBACHyF,SAAS,EAAC,UAAU;kBACpBC,OAAO,EAAE,IAAI,CAACrC,QAAS;kBAAAmC,QAAA,GAEtB3F,QAAQ,CAAC0I,QAAQ,eAClBhI,OAAA;oBAAGkF,SAAS,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAevF,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}