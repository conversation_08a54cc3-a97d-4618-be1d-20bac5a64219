{"ast": null, "code": "import * as React from 'react';\nvar AnchorContext = /*#__PURE__*/React.createContext(null);\nexport default AnchorContext;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/anchor/context.js"], "sourcesContent": ["import * as React from 'react';\nvar AnchorContext = /*#__PURE__*/React.createContext(null);\nexport default AnchorContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1D,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}