export type LighthouseErrorDefinition = {
    code: string;
    message: string;
    pattern?: RegExp | undefined;
    /**
     * True if it should appear in the top-level LHR.runtimeError property.
     */
    lhrRuntimeError?: boolean | undefined;
};
export type SerializedLighthouseError = {
    [p: string]: string | undefined;
    sentinel: '__LighthouseErrorSentinel';
    code: string;
    stack?: string | undefined;
};
export type SerializedBaseError = {
    sentinel: '__ErrorSentinel';
    message: string;
    code?: string;
    stack?: string;
};
/**
 * @typedef {{sentinel: '__LighthouseErrorSentinel', code: string, stack?: string, [p: string]: string|undefined}} SerializedLighthouseError
 * @typedef {{sentinel: '__ErrorSentinel', message: string, code?: string, stack?: string}} SerializedBaseError
 */
export class LighthouseError extends Error {
    /**
     * @param {string} method
     * @param {{message: string, data?: string|undefined}} protocolError
     * @return {Error|LighthouseError}
     */
    static fromProtocolMessage(method: string, protocolError: {
        message: string;
        data?: string | undefined;
    }): Error | LighthouseError;
    /**
     * A JSON.stringify replacer to serialize LighthouseErrors and (as a fallback) Errors.
     * Returns a simplified version of the error object that can be reconstituted
     * as a copy of the original error at parse time.
     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter
     * @param {Error|LighthouseError} err
     * @return {SerializedBaseError|SerializedLighthouseError}
     */
    static stringifyReplacer(err: Error | LighthouseError): SerializedBaseError | SerializedLighthouseError;
    /**
     * A JSON.parse reviver. If any value passed in is a serialized Error or
     * LighthouseError, the error is recreated as the original object. Otherwise, the
     * value is passed through unchanged.
     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter
     * @param {string} key
     * @param {any} possibleError
     * @return {any}
     */
    static parseReviver(key: string, possibleError: any): any;
    /**
     * @param {LighthouseErrorDefinition} errorDefinition
     * @param {Record<string, string|undefined>=} properties
     */
    constructor(errorDefinition: LighthouseErrorDefinition, properties?: Record<string, string | undefined> | undefined);
    code: string;
    friendlyMessage: import("../index.js").IcuMessage;
    lhrRuntimeError: boolean;
}
export namespace LighthouseError {
    export { ERRORS as errors };
    export const NO_ERROR: string;
    export const UNKNOWN_ERROR: string;
}
export namespace UIStrings {
    const didntCollectScreenshots: string;
    const badTraceRecording: string;
    const noFcp: string;
    const noLcp: string;
    const pageLoadTookTooLong: string;
    const pageLoadFailed: string;
    const pageLoadFailedWithStatusCode: string;
    const pageLoadFailedWithDetails: string;
    const pageLoadFailedInsecure: string;
    const pageLoadFailedInterstitial: string;
    const internalChromeError: string;
    const requestContentTimeout: string;
    const notHtml: string;
    const urlInvalid: string;
    const protocolTimeout: string;
    const dnsFailure: string;
    const pageLoadFailedHung: string;
    const criTimeout: string;
    const missingRequiredArtifact: string;
    const erroredRequiredArtifact: string;
    const oldChromeDoesNotSupportFeature: string;
}
declare namespace ERRORS {
    namespace NO_SPEEDLINE_FRAMES {
        export const code: string;
        import message = UIStrings.didntCollectScreenshots;
        export { message };
        export const lhrRuntimeError: boolean;
    }
    namespace SPEEDINDEX_OF_ZERO {
        const code_1: string;
        export { code_1 as code };
        import message_1 = UIStrings.didntCollectScreenshots;
        export { message_1 as message };
        const lhrRuntimeError_1: boolean;
        export { lhrRuntimeError_1 as lhrRuntimeError };
    }
    namespace NO_SCREENSHOTS {
        const code_2: string;
        export { code_2 as code };
        import message_2 = UIStrings.didntCollectScreenshots;
        export { message_2 as message };
        const lhrRuntimeError_2: boolean;
        export { lhrRuntimeError_2 as lhrRuntimeError };
    }
    namespace INVALID_SPEEDLINE {
        const code_3: string;
        export { code_3 as code };
        import message_3 = UIStrings.didntCollectScreenshots;
        export { message_3 as message };
        const lhrRuntimeError_3: boolean;
        export { lhrRuntimeError_3 as lhrRuntimeError };
    }
    namespace NO_TRACING_STARTED {
        const code_4: string;
        export { code_4 as code };
        import message_4 = UIStrings.badTraceRecording;
        export { message_4 as message };
        const lhrRuntimeError_4: boolean;
        export { lhrRuntimeError_4 as lhrRuntimeError };
    }
    namespace NO_RESOURCE_REQUEST {
        const code_5: string;
        export { code_5 as code };
        import message_5 = UIStrings.badTraceRecording;
        export { message_5 as message };
        const lhrRuntimeError_5: boolean;
        export { lhrRuntimeError_5 as lhrRuntimeError };
    }
    namespace NO_NAVSTART {
        const code_6: string;
        export { code_6 as code };
        import message_6 = UIStrings.badTraceRecording;
        export { message_6 as message };
        const lhrRuntimeError_6: boolean;
        export { lhrRuntimeError_6 as lhrRuntimeError };
    }
    namespace NO_FCP {
        const code_7: string;
        export { code_7 as code };
        import message_7 = UIStrings.noFcp;
        export { message_7 as message };
        const lhrRuntimeError_7: boolean;
        export { lhrRuntimeError_7 as lhrRuntimeError };
    }
    namespace NO_DCL {
        const code_8: string;
        export { code_8 as code };
        import message_8 = UIStrings.badTraceRecording;
        export { message_8 as message };
        const lhrRuntimeError_8: boolean;
        export { lhrRuntimeError_8 as lhrRuntimeError };
    }
    namespace NO_FMP {
        const code_9: string;
        export { code_9 as code };
        import message_9 = UIStrings.badTraceRecording;
        export { message_9 as message };
    }
    namespace NO_LCP {
        const code_10: string;
        export { code_10 as code };
        import message_10 = UIStrings.noLcp;
        export { message_10 as message };
    }
    namespace NO_LCP_ALL_FRAMES {
        const code_11: string;
        export { code_11 as code };
        import message_11 = UIStrings.noLcp;
        export { message_11 as message };
    }
    namespace UNSUPPORTED_OLD_CHROME {
        const code_12: string;
        export { code_12 as code };
        import message_12 = UIStrings.oldChromeDoesNotSupportFeature;
        export { message_12 as message };
    }
    namespace NO_TTI_CPU_IDLE_PERIOD {
        const code_13: string;
        export { code_13 as code };
        import message_13 = UIStrings.pageLoadTookTooLong;
        export { message_13 as message };
    }
    namespace NO_TTI_NETWORK_IDLE_PERIOD {
        const code_14: string;
        export { code_14 as code };
        import message_14 = UIStrings.pageLoadTookTooLong;
        export { message_14 as message };
    }
    namespace NO_DOCUMENT_REQUEST {
        const code_15: string;
        export { code_15 as code };
        import message_15 = UIStrings.pageLoadFailed;
        export { message_15 as message };
        const lhrRuntimeError_9: boolean;
        export { lhrRuntimeError_9 as lhrRuntimeError };
    }
    namespace FAILED_DOCUMENT_REQUEST {
        const code_16: string;
        export { code_16 as code };
        import message_16 = UIStrings.pageLoadFailedWithDetails;
        export { message_16 as message };
        const lhrRuntimeError_10: boolean;
        export { lhrRuntimeError_10 as lhrRuntimeError };
    }
    namespace ERRORED_DOCUMENT_REQUEST {
        const code_17: string;
        export { code_17 as code };
        import message_17 = UIStrings.pageLoadFailedWithStatusCode;
        export { message_17 as message };
        const lhrRuntimeError_11: boolean;
        export { lhrRuntimeError_11 as lhrRuntimeError };
    }
    namespace INSECURE_DOCUMENT_REQUEST {
        const code_18: string;
        export { code_18 as code };
        import message_18 = UIStrings.pageLoadFailedInsecure;
        export { message_18 as message };
        const lhrRuntimeError_12: boolean;
        export { lhrRuntimeError_12 as lhrRuntimeError };
    }
    namespace CHROME_INTERSTITIAL_ERROR {
        const code_19: string;
        export { code_19 as code };
        import message_19 = UIStrings.pageLoadFailedInterstitial;
        export { message_19 as message };
        const lhrRuntimeError_13: boolean;
        export { lhrRuntimeError_13 as lhrRuntimeError };
    }
    namespace PAGE_HUNG {
        const code_20: string;
        export { code_20 as code };
        import message_20 = UIStrings.pageLoadFailedHung;
        export { message_20 as message };
        const lhrRuntimeError_14: boolean;
        export { lhrRuntimeError_14 as lhrRuntimeError };
    }
    namespace NOT_HTML {
        const code_21: string;
        export { code_21 as code };
        import message_21 = UIStrings.notHtml;
        export { message_21 as message };
        const lhrRuntimeError_15: boolean;
        export { lhrRuntimeError_15 as lhrRuntimeError };
    }
    namespace TRACING_ALREADY_STARTED {
        const code_22: string;
        export { code_22 as code };
        import message_22 = UIStrings.internalChromeError;
        export { message_22 as message };
        export const pattern: RegExp;
        const lhrRuntimeError_16: boolean;
        export { lhrRuntimeError_16 as lhrRuntimeError };
    }
    namespace PARSING_PROBLEM {
        const code_23: string;
        export { code_23 as code };
        import message_23 = UIStrings.internalChromeError;
        export { message_23 as message };
        const pattern_1: RegExp;
        export { pattern_1 as pattern };
        const lhrRuntimeError_17: boolean;
        export { lhrRuntimeError_17 as lhrRuntimeError };
    }
    namespace READ_FAILED {
        const code_24: string;
        export { code_24 as code };
        import message_24 = UIStrings.internalChromeError;
        export { message_24 as message };
        const pattern_2: RegExp;
        export { pattern_2 as pattern };
        const lhrRuntimeError_18: boolean;
        export { lhrRuntimeError_18 as lhrRuntimeError };
    }
    namespace INVALID_URL {
        const code_25: string;
        export { code_25 as code };
        import message_25 = UIStrings.urlInvalid;
        export { message_25 as message };
    }
    namespace PROTOCOL_TIMEOUT {
        const code_26: string;
        export { code_26 as code };
        import message_26 = UIStrings.protocolTimeout;
        export { message_26 as message };
        const lhrRuntimeError_19: boolean;
        export { lhrRuntimeError_19 as lhrRuntimeError };
    }
    namespace DNS_FAILURE {
        const code_27: string;
        export { code_27 as code };
        import message_27 = UIStrings.dnsFailure;
        export { message_27 as message };
        const lhrRuntimeError_20: boolean;
        export { lhrRuntimeError_20 as lhrRuntimeError };
    }
    namespace CRI_TIMEOUT {
        const code_28: string;
        export { code_28 as code };
        import message_28 = UIStrings.criTimeout;
        export { message_28 as message };
        const lhrRuntimeError_21: boolean;
        export { lhrRuntimeError_21 as lhrRuntimeError };
    }
    namespace MISSING_REQUIRED_ARTIFACT {
        const code_29: string;
        export { code_29 as code };
        import message_29 = UIStrings.missingRequiredArtifact;
        export { message_29 as message };
    }
    namespace ERRORED_REQUIRED_ARTIFACT {
        const code_30: string;
        export { code_30 as code };
        import message_30 = UIStrings.erroredRequiredArtifact;
        export { message_30 as message };
    }
}
export {};
//# sourceMappingURL=lh-error.d.ts.map