{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\movimentazioni.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneMovimentazioni - operazioni sui prodotti movimentati\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass GestioneMovimentazioni extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      area: \"\",\n      scaffale: \"\",\n      ripiano: \"\",\n      posizione: \"\",\n      eanCode: \"\"\n    };\n    this.state = {\n      results: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      selectedWarehouse: null,\n      warehouse: null,\n      loading: true,\n      products: null,\n      dateFrom: new Date(Date.UTC(1900, 0, 1)),\n      dateTo: new Date(),\n      productOptions: null\n    };\n    this.warehouse = [];\n    this.onProductSelect = this.onProductSelect.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"warehouses/cross\").then(res => {\n      res.data.forEach(element => {\n        var x = {\n          name: element.idWarehouse.warehouseName,\n          code: element.idWarehouse.id\n        };\n        this.warehouse.push(x);\n      });\n      this.setState({\n        warehouse: res.data\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    var idWarehouse = JSON.parse(sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'productsposition?idWarehouse=' + idWarehouse.code;\n      await APIRequest(\"GET\", url).then(res => {\n        const singleArray = [];\n        res.data.forEach(el => {\n          const name = \"\".concat(el.idProductsPackaging.idProduct.description, \" (\").concat(el.idProductsPackaging.idProduct.externalCode, \")\");\n          const value = el.idProductsPackaging.idProduct.id;\n          if (!singleArray.some(item => item.name === name && item.value === value)) {\n            singleArray.push({\n              name,\n              value\n            });\n          }\n        });\n        this.setState({\n          productOptions: singleArray\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n      this.setState({\n        displayed: false,\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", \"statistic/productindocument?dateFrom=\".concat(this.state.dateFrom, \"&dateTo=\").concat(this.state.dateTo, \"&documentType=CLI-ORDINE,CLI-DDT&idwarehouse=\").concat(idWarehouse.code)).then(res => {\n        if (idWarehouse.code === 19) {\n          res.data = res.data.filter(el => el.ragione_sociale);\n          res.data = res.data.filter(el => !el.codice.includes(\"VX\"));\n        }\n        this.setState({\n          results: res.data,\n          totalRecords: res.data.totalCount,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n  }\n  async onWarehouseSelect(e) {\n    this.setState({\n      selectedWarehouse: e.value\n    });\n    sessionStorage.setItem(\"idWarehouse\", JSON.stringify(e.value));\n    var url = 'productsposition?idWarehouse=' + e.value.code;\n    await APIRequest(\"GET\", url).then(res => {\n      const singleArray = [];\n      res.data.forEach(el => {\n        const name = \"\".concat(el.idProductsPackaging.idProduct.description, \" (\").concat(el.idProductsPackaging.idProduct.externalCode, \")\");\n        const value = el.idProductsPackaging.idProduct.id;\n        if (!singleArray.some(item => item.name === name && item.value === value)) {\n          singleArray.push({\n            name,\n            value\n          });\n        }\n      });\n      this.setState({\n        productOptions: singleArray\n      });\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n    /* await APIRequest(\"GET\", `statistic/productindocument?dateFrom=${this.state.dateFrom}&dateTo=${this.state.dateTo}&documentType=CLI-ORDINE,CLI-DDT&idwarehouse=${e.value.code}`)\n        .then(res => {\n            if (e.value.code === 19) {\n                res.data = res.data.filter(el => el.ragione_sociale)\n                res.data = res.data.filter(el => !el.codice.includes(\"VX\"))\n            }\n            this.setState({\n                results: res.data,\n                totalRecords: res.data.totalCount,\n                loading: false\n            })\n        }).catch((e) => {\n            console.log(e)\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                life: 3000,\n            });\n        }) */\n  }\n  async onProductSelect(e) {\n    this.setState({\n      products: e.value\n    });\n    await APIRequest(\"GET\", \"statistic/productindocument?dateFrom=\".concat(this.state.dateFrom, \"&dateTo=\").concat(this.state.dateTo, \"&documentType=CLI-ORDINE,CLI-DDT&idwarehouse=\").concat(this.state.selectedWarehouse.code, \"&idproduct=\").concat(e.value)).then(res => {\n      if (e.value.code === 19) {\n        res.data = res.data.filter(el => el.ragione_sociale);\n        res.data = res.data.filter(el => !el.codice.includes(\"VX\"));\n      }\n      this.setState({\n        results: res.data,\n        loading: false,\n        totalRecords: res.data.totalCount\n      });\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: 'ragione_sociale',\n      header: Costanti.rSociale,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'numero',\n      header: Costanti.NDoc,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'tipo_documento',\n      header: Costanti.type,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'data_documento',\n      header: Costanti.DataDoc,\n      body: 'data_documento',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'codice',\n      header: Costanti.CodProd,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'prodotto',\n      header: Costanti.Prodotto,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'ordinato_unitario',\n      header: Costanti.Ordinato,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'venduto_unitario',\n      header: Costanti.delivered,\n      sortable: true,\n      showHeader: true\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneMovimentazioni\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activeFilterContainer p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0 w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-home mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 73\n                  }, this), Costanti.Magazzino, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                  className: \"selWar\",\n                  value: this.state.selectedWarehouse,\n                  options: this.warehouse,\n                  onChange: this.onWarehouseSelect,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona magazzino\",\n                  filter: true,\n                  filterBy: \"name\",\n                  emptyMessage: \"Nessun elemento disponibile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0 w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-list mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 73\n                  }, this), Costanti.Prodotti, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                  value: this.state.products,\n                  options: this.state.productOptions,\n                  onChange: this.onProductSelect,\n                  optionLabel: \"name\",\n                  filter: true,\n                  filterBy: \"name\",\n                  placeholder: \"Seleziona prodotto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n            ref: el => this.dt = el,\n            value: this.state.results,\n            fields: fields,\n            loading: this.state.loading,\n            dataKey: \"id\",\n            paginator: true,\n            rows: 20,\n            rowsPerPageOptions: [10, 20, 50],\n            autoLayout: true,\n            showExportCsvButton: true,\n            fileNames: \"GiacenzaMagazzino\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona il magazzino\",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 21\n        }, this), this.state.productOptions && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(JoyrideGen, {\n            title: \"Prima di procedere\",\n            content: \"Seleziona un prodotto\",\n            target: \".selProd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center flex-column pb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-list mr-2\",\n                style: {\n                  'fontSize': '.8em'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 46\n              }, this), Costanti.Prodotti]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n              className: \"selProd\",\n              value: this.state.products,\n              options: this.state.productOptions,\n              onChange: this.onProductSelect,\n              optionLabel: \"name\",\n              filter: true,\n              filterBy: \"name\",\n              placeholder: \"Seleziona prodotto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default GestioneMovimentazioni;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "JoyrideGen", "Dropdown", "Dialog", "<PERSON><PERSON>", "Nav", "CustomDataTable", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GestioneMovimentazioni", "constructor", "props", "emptyResult", "id", "area", "scaffale", "<PERSON><PERSON>", "posizione", "eanCode", "state", "results", "result", "resultDialog", "selectedWarehouse", "warehouse", "loading", "products", "dateFrom", "Date", "UTC", "dateTo", "productOptions", "onProductSelect", "bind", "onWarehouseSelect", "closeSelectBefore", "componentDidMount", "then", "res", "data", "for<PERSON>ach", "element", "x", "name", "idWarehouse", "warehouseName", "code", "push", "setState", "catch", "e", "console", "log", "JSON", "parse", "sessionStorage", "getItem", "url", "singleArray", "el", "concat", "idProductsPackaging", "idProduct", "description", "externalCode", "value", "some", "item", "_e$response", "_e$response2", "toast", "show", "severity", "summary", "detail", "response", "undefined", "message", "life", "displayed", "filter", "ragione_sociale", "codice", "includes", "totalRecords", "totalCount", "_e$response3", "_e$response4", "setItem", "stringify", "_e$response5", "_e$response6", "_e$response7", "_e$response8", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fields", "field", "header", "rSociale", "sortable", "showHeader", "NDoc", "type", "DataDoc", "body", "CodProd", "<PERSON><PERSON><PERSON>", "Ordinato", "delivered", "ref", "gestioneMovimentazioni", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "emptyMessage", "<PERSON><PERSON>tti", "dt", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "showExportCsvButton", "fileNames", "visible", "Primadiproseguire", "modal", "onHide", "footer", "title", "content", "target"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/movimentazioni.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneMovimentazioni - operazioni sui prodotti movimentati\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON>nti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\n\nclass GestioneMovimentazioni extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        area: \"\",\n        scaffale: \"\",\n        ripiano: \"\",\n        posizione: \"\",\n        eanCode: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            result: this.emptyResult,\n            resultDialog: false,\n            selectedWarehouse: null,\n            warehouse: null,\n            loading: true,\n            products: null,\n            dateFrom: new Date(Date.UTC(1900, 0, 1)),\n            dateTo: new Date(),\n            productOptions: null\n        }\n        this.warehouse = []\n        this.onProductSelect = this.onProductSelect.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"warehouses/cross\")\n            .then((res) => {\n                res.data.forEach(element => {\n                    var x = {\n                        name: element.idWarehouse.warehouseName,\n                        code: element.idWarehouse.id\n                    }\n                    this.warehouse.push(x)\n                })\n                this.setState({ warehouse: res.data })\n            }).catch((e) => {\n                console.log(e)\n            })\n        var idWarehouse = JSON.parse(sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'productsposition?idWarehouse=' + idWarehouse.code\n            await APIRequest(\"GET\", url)\n                .then(res => {\n                    const singleArray = [];\n\n                    res.data.forEach(el => {\n                        const name = `${el.idProductsPackaging.idProduct.description} (${el.idProductsPackaging.idProduct.externalCode})`;\n                        const value = el.idProductsPackaging.idProduct.id;\n\n                        if (!singleArray.some(item => item.name === name && item.value === value)) {\n                            singleArray.push({ name, value });\n                        }\n                    });\n\n                    this.setState({productOptions: singleArray})\n                }).catch((e) => {\n                    console.log(e)\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                })\n            this.setState({ displayed: false, selectedWarehouse: idWarehouse })\n            await APIRequest(\"GET\", `statistic/productindocument?dateFrom=${this.state.dateFrom}&dateTo=${this.state.dateTo}&documentType=CLI-ORDINE,CLI-DDT&idwarehouse=${idWarehouse.code}`)\n                .then(res => {\n                    if (idWarehouse.code === 19) {\n                        res.data = res.data.filter(el => el.ragione_sociale)\n                        res.data = res.data.filter(el => !el.codice.includes(\"VX\"))\n                    }\n\n                    this.setState({\n                        results: res.data,\n                        totalRecords: res.data.totalCount,\n                        loading: false\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                })\n\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n    }\n    async onWarehouseSelect(e) {\n        this.setState({ selectedWarehouse: e.value })\n        sessionStorage.setItem(\"idWarehouse\", JSON.stringify(e.value))\n        var url = 'productsposition?idWarehouse=' + e.value.code\n        await APIRequest(\"GET\", url)\n            .then(res => {\n                const singleArray = [];\n\n                res.data.forEach(el => {\n                    const name = `${el.idProductsPackaging.idProduct.description} (${el.idProductsPackaging.idProduct.externalCode})`;\n                    const value = el.idProductsPackaging.idProduct.id;\n\n                    if (!singleArray.some(item => item.name === name && item.value === value)) {\n                        singleArray.push({ name, value });\n                    }\n                });\n\n                this.setState({productOptions: singleArray})\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        /* await APIRequest(\"GET\", `statistic/productindocument?dateFrom=${this.state.dateFrom}&dateTo=${this.state.dateTo}&documentType=CLI-ORDINE,CLI-DDT&idwarehouse=${e.value.code}`)\n            .then(res => {\n                if (e.value.code === 19) {\n                    res.data = res.data.filter(el => el.ragione_sociale)\n                    res.data = res.data.filter(el => !el.codice.includes(\"VX\"))\n                }\n                this.setState({\n                    results: res.data,\n                    totalRecords: res.data.totalCount,\n                    loading: false\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            }) */\n\n    }\n    async onProductSelect(e) {\n        this.setState({ products: e.value })\n        await APIRequest(\"GET\", `statistic/productindocument?dateFrom=${this.state.dateFrom}&dateTo=${this.state.dateTo}&documentType=CLI-ORDINE,CLI-DDT&idwarehouse=${this.state.selectedWarehouse.code}&idproduct=${e.value}`)\n            .then(res => {\n                if (e.value.code === 19) {\n                    res.data = res.data.filter(el => el.ragione_sociale)\n                    res.data = res.data.filter(el => !el.codice.includes(\"VX\"))\n                }\n                this.setState({\n                    results: res.data,\n                    loading: false,\n                    totalRecords: res.data.totalCount\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    render() {\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            { field: 'ragione_sociale', header: Costanti.rSociale, sortable: true, showHeader: true },\n            { field: 'numero', header: Costanti.NDoc, sortable: true, showHeader: true },\n            { field: 'tipo_documento', header: Costanti.type, sortable: true, showHeader: true },\n            { field: 'data_documento', header: Costanti.DataDoc, body: 'data_documento', sortable: true, showHeader: true },\n            { field: 'codice', header: Costanti.CodProd, sortable: true, showHeader: true },\n            { field: 'prodotto', header: Costanti.Prodotto, sortable: true, showHeader: true },\n            { field: 'ordinato_unitario', header: Costanti.Ordinato, sortable: true, showHeader: true },\n            { field: 'venduto_unitario', header: Costanti.delivered, sortable: true, showHeader: true },\n\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.gestioneMovimentazioni}</h1>\n                </div>\n                {this.state.selectedWarehouse &&\n                    <>\n                        <div className='activeFilterContainer p-2'>\n                            <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                                <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                    <div className='d-flex justify-content-center align-items-center'>\n                                        <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                                    </div>\n                                </li>\n                                <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                    <div className='d-flex justify-content-center align-items-center'>\n                                        <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-list mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Prodotti}:</h5>\n                                        <Dropdown value={this.state.products} options={this.state.productOptions} onChange={this.onProductSelect} optionLabel=\"name\" filter filterBy=\"name\" placeholder=\"Seleziona prodotto\" />\n                                    </div>\n                                </li>\n                            </ul>\n                        </div>\n                        <div className=\"card\">\n                            {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n                            <CustomDataTable\n                                ref={(el) => this.dt = el}\n                                value={this.state.results}\n                                fields={fields}\n                                loading={this.state.loading}\n                                dataKey=\"id\"\n                                paginator\n                                rows={20}\n                                rowsPerPageOptions={[10, 20, 50]}\n                                autoLayout={true}\n                                showExportCsvButton\n                                fileNames=\"GiacenzaMagazzino\"\n                            />\n                        </div>\n                    </>\n                }\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona il magazzino' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" />\n                    </div>\n                    {this.state.productOptions &&\n                    <>\n                    <JoyrideGen title='Prima di procedere' content='Seleziona un prodotto' target='.selProd' />\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-list mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Prodotti}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selProd\" value={this.state.products} options={this.state.productOptions} onChange={this.onProductSelect} optionLabel=\"name\" filter filterBy=\"name\" placeholder=\"Seleziona prodotto\" />\n                    </div>\n                    </>\n                    }\n                </Dialog>\n            </div>\n        )\n    }\n}\n\nexport default GestioneMovimentazioni;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,MAAMC,sBAAsB,SAASd,SAAS,CAAC;EAU3Ce,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAVhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE;IACb,CAAC;IAGG,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI,CAACT,WAAW;MACxBU,YAAY,EAAE,KAAK;MACnBC,iBAAiB,EAAE,IAAI;MACvBC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACxCC,MAAM,EAAE,IAAIF,IAAI,CAAC,CAAC;MAClBG,cAAc,EAAE;IACpB,CAAC;IACD,IAAI,CAACP,SAAS,GAAG,EAAE;IACnB,IAAI,CAACQ,eAAe,GAAG,IAAI,CAACA,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACF,IAAI,CAAC,IAAI,CAAC;EAC9D;EACA;EACA,MAAMG,iBAAiBA,CAAA,EAAG;IACtB,MAAMtC,UAAU,CAAC,KAAK,EAAE,kBAAkB,CAAC,CACtCuC,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIC,CAAC,GAAG;UACJC,IAAI,EAAEF,OAAO,CAACG,WAAW,CAACC,aAAa;UACvCC,IAAI,EAAEL,OAAO,CAACG,WAAW,CAAC/B;QAC9B,CAAC;QACD,IAAI,CAACW,SAAS,CAACuB,IAAI,CAACL,CAAC,CAAC;MAC1B,CAAC,CAAC;MACF,IAAI,CAACM,QAAQ,CAAC;QAAExB,SAAS,EAAEc,GAAG,CAACC;MAAK,CAAC,CAAC;IAC1C,CAAC,CAAC,CAACU,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAIN,WAAW,GAAGS,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;IACnE,IAAIZ,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAIa,GAAG,GAAG,+BAA+B,GAAGb,WAAW,CAACE,IAAI;MAC5D,MAAMhD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBpB,IAAI,CAACC,GAAG,IAAI;QACT,MAAMoB,WAAW,GAAG,EAAE;QAEtBpB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACmB,EAAE,IAAI;UACnB,MAAMhB,IAAI,MAAAiB,MAAA,CAAMD,EAAE,CAACE,mBAAmB,CAACC,SAAS,CAACC,WAAW,QAAAH,MAAA,CAAKD,EAAE,CAACE,mBAAmB,CAACC,SAAS,CAACE,YAAY,MAAG;UACjH,MAAMC,KAAK,GAAGN,EAAE,CAACE,mBAAmB,CAACC,SAAS,CAACjD,EAAE;UAEjD,IAAI,CAAC6C,WAAW,CAACQ,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACxB,IAAI,KAAKA,IAAI,IAAIwB,IAAI,CAACF,KAAK,KAAKA,KAAK,CAAC,EAAE;YACvEP,WAAW,CAACX,IAAI,CAAC;cAAEJ,IAAI;cAAEsB;YAAM,CAAC,CAAC;UACrC;QACJ,CAAC,CAAC;QAEF,IAAI,CAACjB,QAAQ,CAAC;UAACjB,cAAc,EAAE2B;QAAW,CAAC,CAAC;MAChD,CAAC,CAAC,CAACT,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAkB,WAAA,EAAAC,YAAA;QACZlB,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACoB,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAd,MAAA,CAAyF,EAAAQ,WAAA,GAAAlB,CAAC,CAACyB,QAAQ,cAAAP,WAAA,uBAAVA,WAAA,CAAY7B,IAAI,MAAKqC,SAAS,IAAAP,YAAA,GAAGnB,CAAC,CAACyB,QAAQ,cAAAN,YAAA,uBAAVA,YAAA,CAAY9B,IAAI,GAAGW,CAAC,CAAC2B,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,IAAI,CAAC9B,QAAQ,CAAC;QAAE+B,SAAS,EAAE,KAAK;QAAExD,iBAAiB,EAAEqB;MAAY,CAAC,CAAC;MACnE,MAAM9C,UAAU,CAAC,KAAK,0CAAA8D,MAAA,CAA0C,IAAI,CAACzC,KAAK,CAACQ,QAAQ,cAAAiC,MAAA,CAAW,IAAI,CAACzC,KAAK,CAACW,MAAM,mDAAA8B,MAAA,CAAgDhB,WAAW,CAACE,IAAI,CAAE,CAAC,CAC7KT,IAAI,CAACC,GAAG,IAAI;QACT,IAAIM,WAAW,CAACE,IAAI,KAAK,EAAE,EAAE;UACzBR,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,CAACyC,MAAM,CAACrB,EAAE,IAAIA,EAAE,CAACsB,eAAe,CAAC;UACpD3C,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,CAACyC,MAAM,CAACrB,EAAE,IAAI,CAACA,EAAE,CAACuB,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/D;QAEA,IAAI,CAACnC,QAAQ,CAAC;UACV5B,OAAO,EAAEkB,GAAG,CAACC,IAAI;UACjB6C,YAAY,EAAE9C,GAAG,CAACC,IAAI,CAAC8C,UAAU;UACjC5D,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CAACwB,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAoC,YAAA,EAAAC,YAAA;QACZpC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACoB,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAd,MAAA,CAAyF,EAAA0B,YAAA,GAAApC,CAAC,CAACyB,QAAQ,cAAAW,YAAA,uBAAVA,YAAA,CAAY/C,IAAI,MAAKqC,SAAS,IAAAW,YAAA,GAAGrC,CAAC,CAACyB,QAAQ,cAAAY,YAAA,uBAAVA,YAAA,CAAYhD,IAAI,GAAGW,CAAC,CAAC2B,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IAEV,CAAC,MAAM;MACH,IAAI,CAAC9B,QAAQ,CAAC;QAAE1B,YAAY,EAAE,IAAI;QAAEyD,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;EACJ;EACA,MAAM7C,iBAAiBA,CAACgB,CAAC,EAAE;IACvB,IAAI,CAACF,QAAQ,CAAC;MAAEzB,iBAAiB,EAAE2B,CAAC,CAACe;IAAM,CAAC,CAAC;IAC7CV,cAAc,CAACiC,OAAO,CAAC,aAAa,EAAEnC,IAAI,CAACoC,SAAS,CAACvC,CAAC,CAACe,KAAK,CAAC,CAAC;IAC9D,IAAIR,GAAG,GAAG,+BAA+B,GAAGP,CAAC,CAACe,KAAK,CAACnB,IAAI;IACxD,MAAMhD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBpB,IAAI,CAACC,GAAG,IAAI;MACT,MAAMoB,WAAW,GAAG,EAAE;MAEtBpB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACmB,EAAE,IAAI;QACnB,MAAMhB,IAAI,MAAAiB,MAAA,CAAMD,EAAE,CAACE,mBAAmB,CAACC,SAAS,CAACC,WAAW,QAAAH,MAAA,CAAKD,EAAE,CAACE,mBAAmB,CAACC,SAAS,CAACE,YAAY,MAAG;QACjH,MAAMC,KAAK,GAAGN,EAAE,CAACE,mBAAmB,CAACC,SAAS,CAACjD,EAAE;QAEjD,IAAI,CAAC6C,WAAW,CAACQ,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACxB,IAAI,KAAKA,IAAI,IAAIwB,IAAI,CAACF,KAAK,KAAKA,KAAK,CAAC,EAAE;UACvEP,WAAW,CAACX,IAAI,CAAC;YAAEJ,IAAI;YAAEsB;UAAM,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;MAEF,IAAI,CAACjB,QAAQ,CAAC;QAACjB,cAAc,EAAE2B;MAAW,CAAC,CAAC;IAChD,CAAC,CAAC,CAACT,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAwC,YAAA,EAAAC,YAAA;MACZxC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACoB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,4FAAAd,MAAA,CAAyF,EAAA8B,YAAA,GAAAxC,CAAC,CAACyB,QAAQ,cAAAe,YAAA,uBAAVA,YAAA,CAAYnD,IAAI,MAAKqC,SAAS,IAAAe,YAAA,GAAGzC,CAAC,CAACyB,QAAQ,cAAAgB,YAAA,uBAAVA,YAAA,CAAYpD,IAAI,GAAGW,CAAC,CAAC2B,OAAO,CAAE;QAC9JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEI;EACA,MAAM9C,eAAeA,CAACkB,CAAC,EAAE;IACrB,IAAI,CAACF,QAAQ,CAAC;MAAEtB,QAAQ,EAAEwB,CAAC,CAACe;IAAM,CAAC,CAAC;IACpC,MAAMnE,UAAU,CAAC,KAAK,0CAAA8D,MAAA,CAA0C,IAAI,CAACzC,KAAK,CAACQ,QAAQ,cAAAiC,MAAA,CAAW,IAAI,CAACzC,KAAK,CAACW,MAAM,mDAAA8B,MAAA,CAAgD,IAAI,CAACzC,KAAK,CAACI,iBAAiB,CAACuB,IAAI,iBAAAc,MAAA,CAAcV,CAAC,CAACe,KAAK,CAAE,CAAC,CACnN5B,IAAI,CAACC,GAAG,IAAI;MACT,IAAIY,CAAC,CAACe,KAAK,CAACnB,IAAI,KAAK,EAAE,EAAE;QACrBR,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,CAACyC,MAAM,CAACrB,EAAE,IAAIA,EAAE,CAACsB,eAAe,CAAC;QACpD3C,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,CAACyC,MAAM,CAACrB,EAAE,IAAI,CAACA,EAAE,CAACuB,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC;MAC/D;MACA,IAAI,CAACnC,QAAQ,CAAC;QACV5B,OAAO,EAAEkB,GAAG,CAACC,IAAI;QACjBd,OAAO,EAAE,KAAK;QACd2D,YAAY,EAAE9C,GAAG,CAACC,IAAI,CAAC8C;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC,CAACpC,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAA0C,YAAA,EAAAC,YAAA;MACZ1C,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACoB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,4FAAAd,MAAA,CAAyF,EAAAgC,YAAA,GAAA1C,CAAC,CAACyB,QAAQ,cAAAiB,YAAA,uBAAVA,YAAA,CAAYrD,IAAI,MAAKqC,SAAS,IAAAiB,YAAA,GAAG3C,CAAC,CAACyB,QAAQ,cAAAkB,YAAA,uBAAVA,YAAA,CAAYtD,IAAI,GAAGW,CAAC,CAAC2B,OAAO,CAAE;QAC9JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA3C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAChB,KAAK,CAACI,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACyB,QAAQ,CAAC;QACV1B,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACgD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEI,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAgB,MAAMA,CAAA,EAAG;IACL,MAAMC,kBAAkB,gBACpBzF,OAAA,CAACZ,KAAK,CAACa,QAAQ;MAAAyF,QAAA,eACX1F,OAAA;QAAK2F,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1D1F,OAAA,CAACJ,MAAM;UAAC+F,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAAC/D,iBAAkB;UAAA6D,QAAA,GAAE,GAAC,EAACnG,QAAQ,CAACsG,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAMC,MAAM,GAAG,CACX;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,MAAM,EAAE7G,QAAQ,CAAC8G,QAAQ;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACzF;MAAEJ,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE7G,QAAQ,CAACiH,IAAI;MAAEF,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC5E;MAAEJ,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE7G,QAAQ,CAACkH,IAAI;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACpF;MAAEJ,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE7G,QAAQ,CAACmH,OAAO;MAAEC,IAAI,EAAE,gBAAgB;MAAEL,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC/G;MAAEJ,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE7G,QAAQ,CAACqH,OAAO;MAAEN,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC/E;MAAEJ,KAAK,EAAE,UAAU;MAAEC,MAAM,EAAE7G,QAAQ,CAACsH,QAAQ;MAAEP,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAClF;MAAEJ,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE7G,QAAQ,CAACuH,QAAQ;MAAER,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC3F;MAAEJ,KAAK,EAAE,kBAAkB;MAAEC,MAAM,EAAE7G,QAAQ,CAACwH,SAAS;MAAET,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CAE9F;IACD,oBACIvG,OAAA;MAAK2F,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9C1F,OAAA,CAACV,KAAK;QAAC0H,GAAG,EAAG3D,EAAE,IAAK,IAAI,CAACW,KAAK,GAAGX;MAAG;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCjG,OAAA,CAACH,GAAG;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPjG,OAAA;QAAK2F,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnC1F,OAAA;UAAA0F,QAAA,EAAKnG,QAAQ,CAAC0H;QAAsB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EACL,IAAI,CAACpF,KAAK,CAACI,iBAAiB,iBACzBjB,OAAA,CAAAE,SAAA;QAAAwF,QAAA,gBACI1F,OAAA;UAAK2F,SAAS,EAAC,2BAA2B;UAAAD,QAAA,eACtC1F,OAAA;YAAI2F,SAAS,EAAC,4DAA4D;YAAAD,QAAA,gBACtE1F,OAAA;cAAI2F,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACjE1F,OAAA;gBAAK2F,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC7D1F,OAAA;kBAAI2F,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC1F,OAAA;oBAAG2F,SAAS,EAAC,iBAAiB;oBAACuB,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAAC1G,QAAQ,CAAC4H,SAAS,EAAC,GAAC;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5HjG,OAAA,CAACN,QAAQ;kBAACiG,SAAS,EAAC,QAAQ;kBAAChC,KAAK,EAAE,IAAI,CAAC9C,KAAK,CAACI,iBAAkB;kBAACmG,OAAO,EAAE,IAAI,CAAClG,SAAU;kBAACmG,QAAQ,EAAE,IAAI,CAACzF,iBAAkB;kBAAC0F,WAAW,EAAC,MAAM;kBAACC,WAAW,EAAC,qBAAqB;kBAAC7C,MAAM;kBAAC8C,QAAQ,EAAC,MAAM;kBAACC,YAAY,EAAC;gBAA6B;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACLjG,OAAA;cAAI2F,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACjE1F,OAAA;gBAAK2F,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC7D1F,OAAA;kBAAI2F,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC1F,OAAA;oBAAG2F,SAAS,EAAC,iBAAiB;oBAACuB,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAAC1G,QAAQ,CAACmI,QAAQ,EAAC,GAAC;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3HjG,OAAA,CAACN,QAAQ;kBAACiE,KAAK,EAAE,IAAI,CAAC9C,KAAK,CAACO,QAAS;kBAACgG,OAAO,EAAE,IAAI,CAACvG,KAAK,CAACY,cAAe;kBAAC4F,QAAQ,EAAE,IAAI,CAAC3F,eAAgB;kBAAC4F,WAAW,EAAC,MAAM;kBAAC5C,MAAM;kBAAC8C,QAAQ,EAAC,MAAM;kBAACD,WAAW,EAAC;gBAAoB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNjG,OAAA;UAAK2F,SAAS,EAAC,MAAM;UAAAD,QAAA,eAEjB1F,OAAA,CAACF,eAAe;YACZkH,GAAG,EAAG3D,EAAE,IAAK,IAAI,CAACsE,EAAE,GAAGtE,EAAG;YAC1BM,KAAK,EAAE,IAAI,CAAC9C,KAAK,CAACC,OAAQ;YAC1BoF,MAAM,EAAEA,MAAO;YACf/E,OAAO,EAAE,IAAI,CAACN,KAAK,CAACM,OAAQ;YAC5ByG,OAAO,EAAC,IAAI;YACZC,SAAS;YACTC,IAAI,EAAE,EAAG;YACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;YACjCC,UAAU,EAAE,IAAK;YACjBC,mBAAmB;YACnBC,SAAS,EAAC;UAAmB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,eACR,CAAC,eAEPjG,OAAA,CAACL,MAAM;QAACwI,OAAO,EAAE,IAAI,CAACtH,KAAK,CAACG,YAAa;QAACoF,MAAM,EAAE7G,QAAQ,CAAC6I,iBAAkB;QAACC,KAAK;QAAC1C,SAAS,EAAC,kBAAkB;QAAC2C,MAAM,EAAE,IAAI,CAACzG,iBAAkB;QAAC0G,MAAM,EAAE9C,kBAAmB;QAAAC,QAAA,GACvK,IAAI,CAAC7E,KAAK,CAAC4D,SAAS,iBACjBzE,OAAA,CAACP,UAAU;UAAC+I,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,wBAAwB;UAACC,MAAM,EAAC;QAAS;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE/FjG,OAAA;UAAK2F,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3D1F,OAAA;YAAI2F,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAC1F,OAAA;cAAG2F,SAAS,EAAC,iBAAiB;cAACuB,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC1G,QAAQ,CAAC4H,SAAS;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHjG,OAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjG,OAAA,CAACN,QAAQ;YAACiG,SAAS,EAAC,QAAQ;YAAChC,KAAK,EAAE,IAAI,CAAC9C,KAAK,CAACI,iBAAkB;YAACmG,OAAO,EAAE,IAAI,CAAClG,SAAU;YAACmG,QAAQ,EAAE,IAAI,CAACzF,iBAAkB;YAAC0F,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC;UAAqB;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnL,CAAC,EACL,IAAI,CAACpF,KAAK,CAACY,cAAc,iBAC1BzB,OAAA,CAAAE,SAAA;UAAAwF,QAAA,gBACA1F,OAAA,CAACP,UAAU;YAAC+I,KAAK,EAAC,oBAAoB;YAACC,OAAO,EAAC,uBAAuB;YAACC,MAAM,EAAC;UAAU;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3FjG,OAAA;YAAK2F,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC3D1F,OAAA;cAAI2F,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAAC1F,OAAA;gBAAG2F,SAAS,EAAC,iBAAiB;gBAACuB,KAAK,EAAE;kBAAE,UAAU,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAAC1G,QAAQ,CAACmI,QAAQ;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/GjG,OAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjG,OAAA,CAACN,QAAQ;cAACiG,SAAS,EAAC,SAAS;cAAChC,KAAK,EAAE,IAAI,CAAC9C,KAAK,CAACO,QAAS;cAACgG,OAAO,EAAE,IAAI,CAACvG,KAAK,CAACY,cAAe;cAAC4F,QAAQ,EAAE,IAAI,CAAC3F,eAAgB;cAAC4F,WAAW,EAAC,MAAM;cAAC5C,MAAM;cAAC8C,QAAQ,EAAC,MAAM;cAACD,WAAW,EAAC;YAAoB;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1M,CAAC;QAAA,eACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAe9F,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}