{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport PickerButton from '../PickerButton';\nimport PickerTag from '../PickerTag';\nimport generateSinglePicker from './generateSinglePicker';\nimport generateRangePicker from './generateRangePicker';\nimport { tuple } from '../../_util/type';\nexport var Components = {\n  button: PickerButton,\n  rangeItem: PickerTag\n};\nfunction toArray(list) {\n  if (!list) {\n    return [];\n  }\n  return Array.isArray(list) ? list : [list];\n}\nexport function getTimeProps(props) {\n  var format = props.format,\n    picker = props.picker,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    use12Hours = props.use12Hours;\n  var firstFormat = toArray(format)[0];\n  var showTimeObj = _extends({}, props);\n  if (firstFormat && typeof firstFormat === 'string') {\n    if (!firstFormat.includes('s') && showSecond === undefined) {\n      showTimeObj.showSecond = false;\n    }\n    if (!firstFormat.includes('m') && showMinute === undefined) {\n      showTimeObj.showMinute = false;\n    }\n    if (!firstFormat.includes('H') && !firstFormat.includes('h') && showHour === undefined) {\n      showTimeObj.showHour = false;\n    }\n    if ((firstFormat.includes('a') || firstFormat.includes('A')) && use12Hours === undefined) {\n      showTimeObj.use12Hours = true;\n    }\n  }\n  if (picker === 'time') {\n    return showTimeObj;\n  }\n  if (typeof firstFormat === 'function') {\n    // format of showTime should use default when format is custom format function\n    delete showTimeObj.format;\n  }\n  return {\n    showTime: showTimeObj\n  };\n}\nvar DataPickerPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\nfunction generatePicker(generateConfig) {\n  // =========================== Picker ===========================\n  var _generateSinglePicker = generateSinglePicker(generateConfig),\n    DatePicker = _generateSinglePicker.DatePicker,\n    WeekPicker = _generateSinglePicker.WeekPicker,\n    MonthPicker = _generateSinglePicker.MonthPicker,\n    YearPicker = _generateSinglePicker.YearPicker,\n    TimePicker = _generateSinglePicker.TimePicker,\n    QuarterPicker = _generateSinglePicker.QuarterPicker; // ======================== Range Picker ========================\n\n  var RangePicker = generateRangePicker(generateConfig);\n  var MergedDatePicker = DatePicker;\n  MergedDatePicker.WeekPicker = WeekPicker;\n  MergedDatePicker.MonthPicker = MonthPicker;\n  MergedDatePicker.YearPicker = YearPicker;\n  MergedDatePicker.RangePicker = RangePicker;\n  MergedDatePicker.TimePicker = TimePicker;\n  MergedDatePicker.QuarterPicker = QuarterPicker;\n  return MergedDatePicker;\n}\nexport default generatePicker;", "map": {"version": 3, "names": ["_extends", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PickerTag", "generateSinglePicker", "generateRangePicker", "tuple", "Components", "button", "rangeItem", "toArray", "list", "Array", "isArray", "getTimeProps", "props", "format", "picker", "showHour", "showMinute", "showSecond", "use12Hours", "firstFormat", "showTimeObj", "includes", "undefined", "showTime", "DataPickerPlacements", "generatePicker", "generateConfig", "_generateSinglePicker", "DatePicker", "WeekPicker", "MonthPicker", "YearPicker", "TimePicker", "QuarterPicker", "RangePicker", "MergedDatePicker"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/date-picker/generatePicker/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport PickerButton from '../PickerButton';\nimport PickerTag from '../PickerTag';\nimport generateSinglePicker from './generateSinglePicker';\nimport generateRangePicker from './generateRangePicker';\nimport { tuple } from '../../_util/type';\nexport var Components = {\n  button: PickerButton,\n  rangeItem: PickerTag\n};\n\nfunction toArray(list) {\n  if (!list) {\n    return [];\n  }\n\n  return Array.isArray(list) ? list : [list];\n}\n\nexport function getTimeProps(props) {\n  var format = props.format,\n      picker = props.picker,\n      showHour = props.showHour,\n      showMinute = props.showMinute,\n      showSecond = props.showSecond,\n      use12Hours = props.use12Hours;\n  var firstFormat = toArray(format)[0];\n\n  var showTimeObj = _extends({}, props);\n\n  if (firstFormat && typeof firstFormat === 'string') {\n    if (!firstFormat.includes('s') && showSecond === undefined) {\n      showTimeObj.showSecond = false;\n    }\n\n    if (!firstFormat.includes('m') && showMinute === undefined) {\n      showTimeObj.showMinute = false;\n    }\n\n    if (!firstFormat.includes('H') && !firstFormat.includes('h') && showHour === undefined) {\n      showTimeObj.showHour = false;\n    }\n\n    if ((firstFormat.includes('a') || firstFormat.includes('A')) && use12Hours === undefined) {\n      showTimeObj.use12Hours = true;\n    }\n  }\n\n  if (picker === 'time') {\n    return showTimeObj;\n  }\n\n  if (typeof firstFormat === 'function') {\n    // format of showTime should use default when format is custom format function\n    delete showTimeObj.format;\n  }\n\n  return {\n    showTime: showTimeObj\n  };\n}\nvar DataPickerPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\n\nfunction generatePicker(generateConfig) {\n  // =========================== Picker ===========================\n  var _generateSinglePicker = generateSinglePicker(generateConfig),\n      DatePicker = _generateSinglePicker.DatePicker,\n      WeekPicker = _generateSinglePicker.WeekPicker,\n      MonthPicker = _generateSinglePicker.MonthPicker,\n      YearPicker = _generateSinglePicker.YearPicker,\n      TimePicker = _generateSinglePicker.TimePicker,\n      QuarterPicker = _generateSinglePicker.QuarterPicker; // ======================== Range Picker ========================\n\n\n  var RangePicker = generateRangePicker(generateConfig);\n  var MergedDatePicker = DatePicker;\n  MergedDatePicker.WeekPicker = WeekPicker;\n  MergedDatePicker.MonthPicker = MonthPicker;\n  MergedDatePicker.YearPicker = YearPicker;\n  MergedDatePicker.RangePicker = RangePicker;\n  MergedDatePicker.TimePicker = TimePicker;\n  MergedDatePicker.QuarterPicker = QuarterPicker;\n  return MergedDatePicker;\n}\n\nexport default generatePicker;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,IAAIC,UAAU,GAAG;EACtBC,MAAM,EAAEN,YAAY;EACpBO,SAAS,EAAEN;AACb,CAAC;AAED,SAASO,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EAEA,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC5C;AAEA,OAAO,SAASG,YAAYA,CAACC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,UAAU,GAAGN,KAAK,CAACM,UAAU;EACjC,IAAIC,WAAW,GAAGZ,OAAO,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EAEpC,IAAIO,WAAW,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,CAAC;EAErC,IAAIO,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IAClD,IAAI,CAACA,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIJ,UAAU,KAAKK,SAAS,EAAE;MAC1DF,WAAW,CAACH,UAAU,GAAG,KAAK;IAChC;IAEA,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIL,UAAU,KAAKM,SAAS,EAAE;MAC1DF,WAAW,CAACJ,UAAU,GAAG,KAAK;IAChC;IAEA,IAAI,CAACG,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACF,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIN,QAAQ,KAAKO,SAAS,EAAE;MACtFF,WAAW,CAACL,QAAQ,GAAG,KAAK;IAC9B;IAEA,IAAI,CAACI,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,KAAKH,UAAU,KAAKI,SAAS,EAAE;MACxFF,WAAW,CAACF,UAAU,GAAG,IAAI;IAC/B;EACF;EAEA,IAAIJ,MAAM,KAAK,MAAM,EAAE;IACrB,OAAOM,WAAW;EACpB;EAEA,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;IACrC;IACA,OAAOC,WAAW,CAACP,MAAM;EAC3B;EAEA,OAAO;IACLU,QAAQ,EAAEH;EACZ,CAAC;AACH;AACA,IAAII,oBAAoB,GAAGrB,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC;AAEpF,SAASsB,cAAcA,CAACC,cAAc,EAAE;EACtC;EACA,IAAIC,qBAAqB,GAAG1B,oBAAoB,CAACyB,cAAc,CAAC;IAC5DE,UAAU,GAAGD,qBAAqB,CAACC,UAAU;IAC7CC,UAAU,GAAGF,qBAAqB,CAACE,UAAU;IAC7CC,WAAW,GAAGH,qBAAqB,CAACG,WAAW;IAC/CC,UAAU,GAAGJ,qBAAqB,CAACI,UAAU;IAC7CC,UAAU,GAAGL,qBAAqB,CAACK,UAAU;IAC7CC,aAAa,GAAGN,qBAAqB,CAACM,aAAa,CAAC,CAAC;;EAGzD,IAAIC,WAAW,GAAGhC,mBAAmB,CAACwB,cAAc,CAAC;EACrD,IAAIS,gBAAgB,GAAGP,UAAU;EACjCO,gBAAgB,CAACN,UAAU,GAAGA,UAAU;EACxCM,gBAAgB,CAACL,WAAW,GAAGA,WAAW;EAC1CK,gBAAgB,CAACJ,UAAU,GAAGA,UAAU;EACxCI,gBAAgB,CAACD,WAAW,GAAGA,WAAW;EAC1CC,gBAAgB,CAACH,UAAU,GAAGA,UAAU;EACxCG,gBAAgB,CAACF,aAAa,GAAGA,aAAa;EAC9C,OAAOE,gBAAgB;AACzB;AAEA,eAAeV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}