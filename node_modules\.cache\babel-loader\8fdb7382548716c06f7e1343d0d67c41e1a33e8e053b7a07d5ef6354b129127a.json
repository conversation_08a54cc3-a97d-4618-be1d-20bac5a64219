{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport devWarning from '../_util/devWarning';\nimport Base from './Base';\nimport { tupleNum } from '../_util/type';\nvar TITLE_ELE_LIST = tupleNum(1, 2, 3, 4, 5);\nvar Title = function Title(props, ref) {\n  var _props$level = props.level,\n    level = _props$level === void 0 ? 1 : _props$level,\n    restProps = __rest(props, [\"level\"]);\n  var component;\n  if (TITLE_ELE_LIST.indexOf(level) !== -1) {\n    component = \"h\".concat(level);\n  } else {\n    devWarning(false, 'Typography.Title', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.');\n    component = 'h1';\n  }\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Title);", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "dev<PERSON><PERSON><PERSON>", "Base", "tupleNum", "TITLE_ELE_LIST", "Title", "props", "ref", "_props$level", "level", "restProps", "component", "concat", "createElement", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Title.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport devWarning from '../_util/devWarning';\nimport Base from './Base';\nimport { tupleNum } from '../_util/type';\nvar TITLE_ELE_LIST = tupleNum(1, 2, 3, 4, 5);\n\nvar Title = function Title(props, ref) {\n  var _props$level = props.level,\n      level = _props$level === void 0 ? 1 : _props$level,\n      restProps = __rest(props, [\"level\"]);\n\n  var component;\n\n  if (TITLE_ELE_LIST.indexOf(level) !== -1) {\n    component = \"h\".concat(level);\n  } else {\n    devWarning(false, 'Typography.Title', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.');\n    component = 'h1';\n  }\n\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n};\n\nexport default /*#__PURE__*/React.forwardRef(Title);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AAEzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,QAAQ,QAAQ,eAAe;AACxC,IAAIC,cAAc,GAAGD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAE5C,IAAIE,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrC,IAAIC,YAAY,GAAGF,KAAK,CAACG,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IAClDE,SAAS,GAAGxB,MAAM,CAACoB,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;EAExC,IAAIK,SAAS;EAEb,IAAIP,cAAc,CAACT,OAAO,CAACc,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACxCE,SAAS,GAAG,GAAG,CAACC,MAAM,CAACH,KAAK,CAAC;EAC/B,CAAC,MAAM;IACLR,UAAU,CAAC,KAAK,EAAE,kBAAkB,EAAE,sFAAsF,CAAC;IAC7HU,SAAS,GAAG,IAAI;EAClB;EAEA,OAAO,aAAaX,KAAK,CAACa,aAAa,CAACX,IAAI,EAAEjB,QAAQ,CAAC;IACrDsB,GAAG,EAAEA;EACP,CAAC,EAAEG,SAAS,EAAE;IACZC,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,aAAaX,KAAK,CAACc,UAAU,CAACT,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}