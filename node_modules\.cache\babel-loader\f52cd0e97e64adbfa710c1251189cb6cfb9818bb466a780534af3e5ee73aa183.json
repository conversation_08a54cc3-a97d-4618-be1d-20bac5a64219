{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\statistiche\\\\bannerUtili.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useEffect } from \"react\";\nimport { Dropdown } from 'primereact/dropdown';\nimport { APIRequest } from \"../apireq\";\nimport { Costanti } from \"../../traduttore/const\";\nimport { Chart } from \"primereact/chart\";\nimport { affiliato } from \"../../route\";\nimport { ChartHorizontal } from \"./chartHorizontal\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport nodata from '../../../img/visualdata-placeholder.svg';\nimport Caricamento from '../../../utils/caricamento';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const BannerUtili = props => {\n  _s();\n  const [totTri, setTotTri] = useState('');\n  const [totAnno, setTotAnno] = useState('');\n  const [selectedRetailer, setSelectedRetailer] = useState(null);\n  const [retailers, setRetailer] = useState([]);\n  const [basicData, setBasicData] = useState([]);\n  const [basicData2, setBasicData2] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [validate, setValidate] = useState(true);\n  const [display, setDisplay] = useState(false);\n  useEffect(() => {\n    async function trovaRisultato() {\n      //Chiamata axios per la visualizzazione dei retailers\n      var res = await APIRequest('GET', 'retailers/');\n      var retailers = [];\n      res.data.forEach(element => {\n        var x = {\n          name: element.idRegistry.firstName,\n          code: element.id\n        };\n        retailers.push(x);\n      });\n      setRetailer(retailers);\n      setSelectedRetailer(retailers[0]);\n      if (retailers.length === 0) {\n        setLoading(false);\n      }\n      var url = 'statistic/ordersretailerstatistic?idRetailer=' + retailers[0].code + \"&ref=last&type=month&qty=3\";\n      await APIRequest('GET', url).then(res => {\n        var tot = 0;\n        var dati = [];\n        var label = [];\n        res.data.forEach(element => {\n          tot += parseFloat(element.total);\n          dati.push(parseFloat(element.total));\n          label.push('');\n        });\n        setBasicData({\n          labels: label,\n          datasets: [{\n            label: 'Fatturato',\n            data: dati,\n            fill: true,\n            borderColor: '#42A5F5',\n            tension: .0\n          }]\n        });\n        if (res.data.length === 0) {\n          setValidate(false);\n        }\n        setTotTri(new Intl.NumberFormat('it-IT', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(tot));\n        setLoading(false);\n      }).catch(e => {\n        console.log(e);\n      });\n      var url2 = 'statistic/ordersretailerstatistic?idRetailer=' + retailers[0].code + \"&ref=last&type=year&qty=1\";\n      await APIRequest('GET', url2).then(res => {\n        var tot = 0;\n        var dati = [];\n        var label = [];\n        res.data.forEach(element => {\n          tot += parseFloat(element.total);\n          dati.push(parseFloat(element.total));\n          label.push('');\n        });\n        setBasicData2({\n          labels: label,\n          datasets: [{\n            label: 'Fatturato',\n            data: dati,\n            fill: true,\n            borderColor: '#42A5F5',\n            tension: .0\n          }]\n        });\n        setTotAnno(new Intl.NumberFormat('it-IT', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(tot));\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    trovaRisultato();\n  }, []);\n  const onRetailerChange = async e => {\n    setSelectedRetailer(e.value);\n    var url = 'statistic/ordersretailerstatistic?idRetailer=' + e.value.code + \"&ref=last&type=month&qty=3\";\n    await APIRequest('GET', url).then(res => {\n      var tot = 0;\n      var dati = [];\n      var label = [];\n      res.data.forEach(element => {\n        tot += parseFloat(element.total);\n        dati.push(parseFloat(element.total));\n        label.push('');\n      });\n      setBasicData({\n        labels: label,\n        datasets: [{\n          label: 'Fatturato',\n          data: dati,\n          fill: true,\n          borderColor: '#42A5F5',\n          tension: .0\n        }]\n      });\n      setTotTri(new Intl.NumberFormat('it-IT', {\n        style: 'currency',\n        currency: 'EUR'\n      }).format(tot));\n    }).catch(e => {\n      console.log(e);\n    });\n    var url2 = 'statistic/ordersretailerstatistic?idRetailer=' + e.value.code + \"&ref=last&type=year&qty=1\";\n    await APIRequest('GET', url2).then(res => {\n      var tot = 0;\n      var dati = [];\n      var label = [];\n      res.data.forEach(element => {\n        tot += parseFloat(element.total);\n        dati.push(parseFloat(element.total));\n        label.push('');\n      });\n      setBasicData2({\n        labels: label,\n        datasets: [{\n          label: 'Fatturato',\n          data: dati,\n          fill: true,\n          borderColor: '#42A5F5',\n          tension: .0\n        }]\n      });\n      setTotAnno(new Intl.NumberFormat('it-IT', {\n        style: 'currency',\n        currency: 'EUR'\n      }).format(tot));\n    }).catch(e => {\n      console.log(e);\n    });\n  };\n  const openModal = () => {\n    setDisplay(true);\n  };\n  const closeModal = () => {\n    setDisplay(false);\n  };\n  const renderFooter = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => closeModal(),\n        className: \"p-button-text\",\n        children: Costanti.Chiudi\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this);\n  };\n  const getLightTheme = () => {\n    let basicOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: .6,\n      plugins: {\n        legend: {\n          labels: {\n            color: '#495057'\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        },\n        y: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        }\n      }\n    };\n    return {\n      basicOptions\n    };\n  };\n  const {\n    basicOptions\n  } = getLightTheme();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"widget-utili\",\n      className: \"widget-dashboard card mb-4 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"titleBox px-3 align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row w-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-2 iconaBanner d-flex flex-column justify-content-center align-items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n              name: \"server-outline\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"titolo bannerTitle\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: Costanti.Utili\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 65\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 29\n            }, this), selectedRetailer && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"titolo mb-0\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-bold\",\n                children: /*#__PURE__*/_jsxDEV(Dropdown, {\n                  value: selectedRetailer,\n                  options: retailers,\n                  onChange: onRetailerChange,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona cliente\",\n                  filter: true,\n                  filterBy: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 88\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 62\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"mb-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-2 iconaBanner d-flex flex-column justify-content-center align-items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"eyeIcon d-flex flex-column justify-content-center align-items-center\",\n              children: window.localStorage.getItem('role') !== affiliato ? /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n                name: \"eye-outline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 41\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                onClick: () => openModal(),\n                children: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n                  name: \"eye-outline\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 17\n      }, this), loading && /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 21\n      }, this), validate === false && selectedRetailer === null && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-nodata mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 d-flex flex-column justify-content-center align-items-center flex-fill h-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                width: 200,\n                src: nodata,\n                alt: \"Start to fill your charts and stats\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-100 px-4 mt-3 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"hr\", {\n                  className: \"mb-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center m-0 px-4 h-100 flex-grow\",\n                children: /*#__PURE__*/_jsxDEV(\"medium\", {\n                  children: Costanti.WidgetDashMessageUtili\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 25\n        }, this)\n      }, void 0, false), selectedRetailer && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"accordion\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"serviceBox\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-column\",\n                children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-center\",\n                  children: Costanti.UltTreMes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bannerChart\",\n                children: /*#__PURE__*/_jsxDEV(Chart, {\n                  className: \"chartLine\",\n                  type: \"line\",\n                  data: basicData,\n                  options: basicOptions\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-center\",\n                  children: totTri !== '' ? totTri : '€ 0,00'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-column\",\n                children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-center\",\n                  children: Costanti.UltimoAnno\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bannerChart\",\n                children: /*#__PURE__*/_jsxDEV(Chart, {\n                  className: \"chartLine\",\n                  type: \"line\",\n                  data: basicData2,\n                  options: basicOptions\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-center\",\n                  children: totAnno !== '' ? totAnno : '€ 0,00'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        header: \"Statistiche di fatturato per periodo\",\n        visible: display,\n        style: {\n          width: '60vw'\n        },\n        footer: renderFooter(),\n        onHide: () => closeModal(),\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ChartHorizontal, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(BannerUtili, \"1puL8+9jKV8HV7r5JiYZcAPXIck=\");\n_c = BannerUtili;\nvar _c;\n$RefreshReg$(_c, \"BannerUtili\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dropdown", "APIRequest", "<PERSON><PERSON>", "Chart", "affiliato", "ChartHorizontal", "Dialog", "<PERSON><PERSON>", "nodata", "Caricamento", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON>", "props", "_s", "totTri", "setTotTri", "totAnno", "setTotAnno", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRetailer", "retailers", "setRetailer", "basicData", "setBasicData", "basicData2", "setBasicData2", "loading", "setLoading", "validate", "setValidate", "display", "setDisplay", "trovaRisultato", "res", "data", "for<PERSON>ach", "element", "x", "name", "idRegistry", "firstName", "code", "id", "push", "length", "url", "then", "tot", "dati", "label", "parseFloat", "total", "labels", "datasets", "fill", "borderColor", "tension", "Intl", "NumberFormat", "style", "currency", "format", "catch", "e", "console", "log", "url2", "onRetailerChange", "value", "openModal", "closeModal", "renderFooter", "children", "onClick", "className", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getLightTheme", "basicOptions", "maintainAspectRatio", "aspectRatio", "plugins", "legend", "color", "scales", "ticks", "grid", "y", "<PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filter", "filterBy", "window", "localStorage", "getItem", "width", "src", "alt", "WidgetDashMessageUtili", "UltTreMes", "type", "UltimoAnno", "header", "visible", "footer", "onHide", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/statistiche/bannerUtili.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useEffect } from \"react\";\nimport { Dropdown } from 'primereact/dropdown';\nimport { APIRequest } from \"../apireq\";\nimport { Costanti } from \"../../traduttore/const\";\nimport { Chart } from \"primereact/chart\";\nimport { affiliato } from \"../../route\";\nimport { ChartHorizontal } from \"./chartHorizontal\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport nodata from '../../../img/visualdata-placeholder.svg';\nimport Caricamento from '../../../utils/caricamento';\n\nexport const BannerUtili = (props) => {\n    const [totTri, setTotTri] = useState('')\n    const [totAnno, setTotAnno] = useState('')\n    const [selectedRetailer, setSelectedRetailer] = useState(null);\n    const [retailers, setRetailer] = useState([]);\n    const [basicData, setBasicData] = useState([]);\n    const [basicData2, setBasicData2] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [validate, setValidate] = useState(true);\n    const [display, setDisplay] = useState(false)\n\n    useEffect(() => {\n        async function trovaRisultato() {\n            //Chiamata axios per la visualizzazione dei retailers\n            var res = await APIRequest('GET', 'retailers/')\n            var retailers = []\n            res.data.forEach(element => {\n                var x = {\n                    name: element.idRegistry.firstName,\n                    code: element.id\n                }\n                retailers.push(x)\n            })\n            setRetailer(retailers)\n            setSelectedRetailer(retailers[0])\n            if (retailers.length === 0) {\n                setLoading(false);\n            }\n            var url = 'statistic/ordersretailerstatistic?idRetailer=' + retailers[0].code + \"&ref=last&type=month&qty=3\"\n            await APIRequest('GET', url)\n                .then(res => {\n                    var tot = 0;\n                    var dati = []\n                    var label = []\n                    res.data.forEach(element => {\n                        tot += parseFloat(element.total)\n                        dati.push(parseFloat(element.total))\n                        label.push('')\n                    })\n                    setBasicData({\n                        labels: label,\n                        datasets: [\n                            {\n                                label: 'Fatturato',\n                                data: dati,\n                                fill: true,\n                                borderColor: '#42A5F5',\n                                tension: .0\n                            }\n                        ]\n                    })\n                    if (res.data.length === 0) {\n                        setValidate(false)\n                    }\n                    setTotTri(new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(tot))\n                    setLoading(false);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            var url2 = 'statistic/ordersretailerstatistic?idRetailer=' + retailers[0].code + \"&ref=last&type=year&qty=1\"\n            await APIRequest('GET', url2)\n                .then(res => {\n                    var tot = 0\n                    var dati = []\n                    var label = []\n                    res.data.forEach(element => {\n                        tot += parseFloat(element.total)\n                        dati.push(parseFloat(element.total))\n                        label.push('')\n                    })\n                    setBasicData2({\n                        labels: label,\n                        datasets: [\n                            {\n                                label: 'Fatturato',\n                                data: dati,\n                                fill: true,\n                                borderColor: '#42A5F5',\n                                tension: .0\n                            }\n                        ]\n                    })\n                    setTotAnno(new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(tot))\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        trovaRisultato();\n    }, []);\n\n    const onRetailerChange = async (e) => {\n        setSelectedRetailer(e.value);\n        var url = 'statistic/ordersretailerstatistic?idRetailer=' + e.value.code + \"&ref=last&type=month&qty=3\"\n        await APIRequest('GET', url)\n            .then(res => {\n                var tot = 0;\n                var dati = []\n                var label = []\n                res.data.forEach(element => {\n                    tot += parseFloat(element.total)\n                    dati.push(parseFloat(element.total))\n                    label.push('')\n                })\n                setBasicData({\n                    labels: label,\n                    datasets: [\n                        {\n                            label: 'Fatturato',\n                            data: dati,\n                            fill: true,\n                            borderColor: '#42A5F5',\n                            tension: .0\n                        }\n                    ]\n                })\n                setTotTri(new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(tot))\n            }).catch((e) => {\n                console.log(e)\n            })\n        var url2 = 'statistic/ordersretailerstatistic?idRetailer=' + e.value.code + \"&ref=last&type=year&qty=1\"\n        await APIRequest('GET', url2)\n            .then(res => {\n                var tot = 0\n                var dati = []\n                var label = []\n                res.data.forEach(element => {\n                    tot += parseFloat(element.total)\n                    dati.push(parseFloat(element.total))\n                    label.push('')\n                })\n                setBasicData2({\n                    labels: label,\n                    datasets: [\n                        {\n                            label: 'Fatturato',\n                            data: dati,\n                            fill: true,\n                            borderColor: '#42A5F5',\n                            tension: .0\n                        }\n                    ]\n                })\n                setTotAnno(new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(tot))\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    const openModal = () => {\n        setDisplay(true)\n    }\n    const closeModal = () => {\n        setDisplay(false)\n    }\n    const renderFooter = () => {\n        return (\n            <div>\n                <Button onClick={() => closeModal()} className=\"p-button-text\" >{Costanti.Chiudi}</Button>\n            </div>\n        );\n    }\n    const getLightTheme = () => {\n        let basicOptions = {\n            maintainAspectRatio: false,\n            aspectRatio: .6,\n            plugins: {\n                legend: {\n                    labels: {\n                        color: '#495057'\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                }\n            }\n        };\n        return {\n            basicOptions\n        }\n    }\n    const { basicOptions } = getLightTheme();\n    return (\n        <>\n            <div id=\"widget-utili\" className='widget-dashboard card mb-4 shadow-sm'>\n                <div className=\"titleBox px-3 align-items-center\">\n                    <div className=\"row w-100\">\n                        <div className=\"col-2 iconaBanner d-flex flex-column justify-content-center align-items-center\">\n                            <ion-icon name=\"server-outline\"></ion-icon>\n                        </div>\n                        <div className=\"col-8\">\n                            <div className=\"titolo bannerTitle\"><h5 className=\"mb-0\">{Costanti.Utili}</h5></div>\n                            {selectedRetailer &&\n                                <div className=\"titolo mb-0\"><h5 className=\"text-bold\"><Dropdown value={selectedRetailer} options={retailers} onChange={onRetailerChange} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" /></h5></div>\n                            }\n                            <hr className='mb-0' />\n                        </div>\n                        <div className=\"col-2 iconaBanner d-flex flex-column justify-content-center align-items-center\">\n                            <div className=\"eyeIcon d-flex flex-column justify-content-center align-items-center\">\n                                {window.localStorage.getItem('role') !== affiliato ?\n                                    (\n                                        <ion-icon\n                                            name=\"eye-outline\"\n                                        />\n                                    )\n                                    :\n                                    (\n                                        <span\n                                            onClick={() => openModal()}\n                                        >\n                                            <ion-icon\n                                                name=\"eye-outline\"\n                                            />\n                                        </span>\n                                    )\n                                }\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                {loading &&\n                    <Caricamento />\n                }\n                {validate === false && selectedRetailer === null &&\n                    <>\n                        <div className=\"card-nodata mt-4\">\n                            <div className=\"row\">\n                                <div className=\"col-12 d-flex flex-column justify-content-center align-items-center flex-fill h-100\">\n                                    <img width={200} src={nodata} alt=\"Start to fill your charts and stats\" />\n                                    <div className=\"w-100 px-4 mt-3 mb-2\">\n                                        <hr className='mb-0' />\n                                    </div>\n                                    <p className=\"text-center m-0 px-4 h-100 flex-grow\"><medium>{Costanti.WidgetDashMessageUtili}</medium></p>\n                                </div>\n                            </div>\n                        </div>\n                    </>\n                }\n                {selectedRetailer &&\n                    <div className=\"card-body\">\n                        <div id=\"accordion\">\n                            <div className=\"card\">\n                                <div className=\"serviceBox\">\n                                    <div className=\"d-flex flex-column\">\n                                        <h5 className=\"text-center\">\n                                            {Costanti.UltTreMes}\n                                        </h5>\n                                    </div>\n                                    <div className=\"bannerChart\">\n                                        {/* <ChartLine results={results} /> */}\n                                        <Chart className=\"chartLine\" type=\"line\" data={basicData} options={basicOptions} />\n                                    </div>\n                                    <div className=\"d-flex justify-content-center\">\n                                        <span className=\"text-center\">{totTri !== '' ? totTri : '€ 0,00'}</span>\n                                    </div>\n                                    <hr />\n                                    <div className=\"d-flex flex-column\">\n                                        <h5 className=\"text-center\">\n                                            {Costanti.UltimoAnno}\n                                        </h5>\n                                    </div>\n                                    <div className=\"bannerChart\">\n                                        <Chart className=\"chartLine\" type=\"line\" data={basicData2} options={basicOptions} />\n                                    </div>\n                                    <div className=\"d-flex justify-content-center\">\n                                        <span className=\"text-center\">{totAnno !== '' ? totAnno : '€ 0,00'}</span>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                }\n                <Dialog header=\"Statistiche di fatturato per periodo\" visible={display} style={{ width: '60vw' }} footer={renderFooter()} onHide={() => closeModal()}>\n                    <Caricamento />\n                    <ChartHorizontal />\n                </Dialog>\n            </div>\n        </>\n    )\n\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,MAAM,MAAM,yCAAyC;AAC5D,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,OAAO,MAAMC,WAAW,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyB,SAAS,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC7C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACZ,eAAeoC,cAAcA,CAAA,EAAG;MAC5B;MACA,IAAIC,GAAG,GAAG,MAAMnC,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC;MAC/C,IAAIsB,SAAS,GAAG,EAAE;MAClBa,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIC,CAAC,GAAG;UACJC,IAAI,EAAEF,OAAO,CAACG,UAAU,CAACC,SAAS;UAClCC,IAAI,EAAEL,OAAO,CAACM;QAClB,CAAC;QACDtB,SAAS,CAACuB,IAAI,CAACN,CAAC,CAAC;MACrB,CAAC,CAAC;MACFhB,WAAW,CAACD,SAAS,CAAC;MACtBD,mBAAmB,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;MACjC,IAAIA,SAAS,CAACwB,MAAM,KAAK,CAAC,EAAE;QACxBjB,UAAU,CAAC,KAAK,CAAC;MACrB;MACA,IAAIkB,GAAG,GAAG,+CAA+C,GAAGzB,SAAS,CAAC,CAAC,CAAC,CAACqB,IAAI,GAAG,4BAA4B;MAC5G,MAAM3C,UAAU,CAAC,KAAK,EAAE+C,GAAG,CAAC,CACvBC,IAAI,CAACb,GAAG,IAAI;QACT,IAAIc,GAAG,GAAG,CAAC;QACX,IAAIC,IAAI,GAAG,EAAE;QACb,IAAIC,KAAK,GAAG,EAAE;QACdhB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxBW,GAAG,IAAIG,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC;UAChCH,IAAI,CAACL,IAAI,CAACO,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC,CAAC;UACpCF,KAAK,CAACN,IAAI,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC;QACFpB,YAAY,CAAC;UACT6B,MAAM,EAAEH,KAAK;UACbI,QAAQ,EAAE,CACN;YACIJ,KAAK,EAAE,WAAW;YAClBf,IAAI,EAAEc,IAAI;YACVM,IAAI,EAAE,IAAI;YACVC,WAAW,EAAE,SAAS;YACtBC,OAAO,EAAE;UACb,CAAC;QAET,CAAC,CAAC;QACF,IAAIvB,GAAG,CAACC,IAAI,CAACU,MAAM,KAAK,CAAC,EAAE;UACvBf,WAAW,CAAC,KAAK,CAAC;QACtB;QACAd,SAAS,CAAC,IAAI0C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACC,MAAM,CAACd,GAAG,CAAC,CAAC;QAC7FpB,UAAU,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,CAACmC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACN,IAAIG,IAAI,GAAG,+CAA+C,GAAG9C,SAAS,CAAC,CAAC,CAAC,CAACqB,IAAI,GAAG,2BAA2B;MAC5G,MAAM3C,UAAU,CAAC,KAAK,EAAEoE,IAAI,CAAC,CACxBpB,IAAI,CAACb,GAAG,IAAI;QACT,IAAIc,GAAG,GAAG,CAAC;QACX,IAAIC,IAAI,GAAG,EAAE;QACb,IAAIC,KAAK,GAAG,EAAE;QACdhB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxBW,GAAG,IAAIG,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC;UAChCH,IAAI,CAACL,IAAI,CAACO,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC,CAAC;UACpCF,KAAK,CAACN,IAAI,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC;QACFlB,aAAa,CAAC;UACV2B,MAAM,EAAEH,KAAK;UACbI,QAAQ,EAAE,CACN;YACIJ,KAAK,EAAE,WAAW;YAClBf,IAAI,EAAEc,IAAI;YACVM,IAAI,EAAE,IAAI;YACVC,WAAW,EAAE,SAAS;YACtBC,OAAO,EAAE;UACb,CAAC;QAET,CAAC,CAAC;QACFvC,UAAU,CAAC,IAAIwC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACC,MAAM,CAACd,GAAG,CAAC,CAAC;MAClG,CAAC,CAAC,CAACe,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACA/B,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,gBAAgB,GAAG,MAAOJ,CAAC,IAAK;IAClC5C,mBAAmB,CAAC4C,CAAC,CAACK,KAAK,CAAC;IAC5B,IAAIvB,GAAG,GAAG,+CAA+C,GAAGkB,CAAC,CAACK,KAAK,CAAC3B,IAAI,GAAG,4BAA4B;IACvG,MAAM3C,UAAU,CAAC,KAAK,EAAE+C,GAAG,CAAC,CACvBC,IAAI,CAACb,GAAG,IAAI;MACT,IAAIc,GAAG,GAAG,CAAC;MACX,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,KAAK,GAAG,EAAE;MACdhB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxBW,GAAG,IAAIG,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC;QAChCH,IAAI,CAACL,IAAI,CAACO,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC,CAAC;QACpCF,KAAK,CAACN,IAAI,CAAC,EAAE,CAAC;MAClB,CAAC,CAAC;MACFpB,YAAY,CAAC;QACT6B,MAAM,EAAEH,KAAK;QACbI,QAAQ,EAAE,CACN;UACIJ,KAAK,EAAE,WAAW;UAClBf,IAAI,EAAEc,IAAI;UACVM,IAAI,EAAE,IAAI;UACVC,WAAW,EAAE,SAAS;UACtBC,OAAO,EAAE;QACb,CAAC;MAET,CAAC,CAAC;MACFzC,SAAS,CAAC,IAAI0C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC,CAACC,MAAM,CAACd,GAAG,CAAC,CAAC;IACjG,CAAC,CAAC,CAACe,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAIG,IAAI,GAAG,+CAA+C,GAAGH,CAAC,CAACK,KAAK,CAAC3B,IAAI,GAAG,2BAA2B;IACvG,MAAM3C,UAAU,CAAC,KAAK,EAAEoE,IAAI,CAAC,CACxBpB,IAAI,CAACb,GAAG,IAAI;MACT,IAAIc,GAAG,GAAG,CAAC;MACX,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,KAAK,GAAG,EAAE;MACdhB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxBW,GAAG,IAAIG,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC;QAChCH,IAAI,CAACL,IAAI,CAACO,UAAU,CAACd,OAAO,CAACe,KAAK,CAAC,CAAC;QACpCF,KAAK,CAACN,IAAI,CAAC,EAAE,CAAC;MAClB,CAAC,CAAC;MACFlB,aAAa,CAAC;QACV2B,MAAM,EAAEH,KAAK;QACbI,QAAQ,EAAE,CACN;UACIJ,KAAK,EAAE,WAAW;UAClBf,IAAI,EAAEc,IAAI;UACVM,IAAI,EAAE,IAAI;UACVC,WAAW,EAAE,SAAS;UACtBC,OAAO,EAAE;QACb,CAAC;MAET,CAAC,CAAC;MACFvC,UAAU,CAAC,IAAIwC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC,CAACC,MAAM,CAACd,GAAG,CAAC,CAAC;IAClG,CAAC,CAAC,CAACe,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;EACV,CAAC;EACD,MAAMM,SAAS,GAAGA,CAAA,KAAM;IACpBtC,UAAU,CAAC,IAAI,CAAC;EACpB,CAAC;EACD,MAAMuC,UAAU,GAAGA,CAAA,KAAM;IACrBvC,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EACD,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACvB,oBACI/D,OAAA;MAAAgE,QAAA,eACIhE,OAAA,CAACJ,MAAM;QAACqE,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,CAAE;QAACI,SAAS,EAAC,eAAe;QAAAF,QAAA,EAAGzE,QAAQ,CAAC4E;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC;EAEd,CAAC;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIC,YAAY,GAAG;MACfC,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJjC,MAAM,EAAE;YACJkC,KAAK,EAAE;UACX;QACJ;MACJ,CAAC;MACDC,MAAM,EAAE;QACJlD,CAAC,EAAE;UACCmD,KAAK,EAAE;YACHF,KAAK,EAAE;UACX,CAAC;UACDG,IAAI,EAAE;YACFH,KAAK,EAAE;UACX;QACJ,CAAC;QACDI,CAAC,EAAE;UACCF,KAAK,EAAE;YACHF,KAAK,EAAE;UACX,CAAC;UACDG,IAAI,EAAE;YACFH,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC;IACD,OAAO;MACHL;IACJ,CAAC;EACL,CAAC;EACD,MAAM;IAAEA;EAAa,CAAC,GAAGD,aAAa,CAAC,CAAC;EACxC,oBACIxE,OAAA,CAAAE,SAAA;IAAA8D,QAAA,eACIhE,OAAA;MAAKkC,EAAE,EAAC,cAAc;MAACgC,SAAS,EAAC,sCAAsC;MAAAF,QAAA,gBACnEhE,OAAA;QAAKkE,SAAS,EAAC,kCAAkC;QAAAF,QAAA,eAC7ChE,OAAA;UAAKkE,SAAS,EAAC,WAAW;UAAAF,QAAA,gBACtBhE,OAAA;YAAKkE,SAAS,EAAC,gFAAgF;YAAAF,QAAA,eAC3FhE,OAAA;cAAU8B,IAAI,EAAC;YAAgB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNvE,OAAA;YAAKkE,SAAS,EAAC,OAAO;YAAAF,QAAA,gBAClBhE,OAAA;cAAKkE,SAAS,EAAC,oBAAoB;cAAAF,QAAA,eAAChE,OAAA;gBAAIkE,SAAS,EAAC,MAAM;gBAAAF,QAAA,EAAEzE,QAAQ,CAAC4F;cAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACnF7D,gBAAgB,iBACbV,OAAA;cAAKkE,SAAS,EAAC,aAAa;cAAAF,QAAA,eAAChE,OAAA;gBAAIkE,SAAS,EAAC,WAAW;gBAAAF,QAAA,eAAChE,OAAA,CAACX,QAAQ;kBAACuE,KAAK,EAAElD,gBAAiB;kBAAC0E,OAAO,EAAExE,SAAU;kBAACyE,QAAQ,EAAE1B,gBAAiB;kBAAC2B,WAAW,EAAC,MAAM;kBAACC,WAAW,EAAC,mBAAmB;kBAACC,MAAM;kBAACC,QAAQ,EAAC;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAErOvE,OAAA;cAAIkE,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNvE,OAAA;YAAKkE,SAAS,EAAC,gFAAgF;YAAAF,QAAA,eAC3FhE,OAAA;cAAKkE,SAAS,EAAC,sEAAsE;cAAAF,QAAA,EAChF0B,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAKnG,SAAS,gBAE1CO,OAAA;gBACI8B,IAAI,EAAC;cAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,gBAIFvE,OAAA;gBACIiE,OAAO,EAAEA,CAAA,KAAMJ,SAAS,CAAC,CAAE;gBAAAG,QAAA,eAE3BhE,OAAA;kBACI8B,IAAI,EAAC;gBAAa;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YACT;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EACLrD,OAAO,iBACJlB,OAAA,CAACF,WAAW;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAElBnD,QAAQ,KAAK,KAAK,IAAIV,gBAAgB,KAAK,IAAI,iBAC5CV,OAAA,CAAAE,SAAA;QAAA8D,QAAA,eACIhE,OAAA;UAAKkE,SAAS,EAAC,kBAAkB;UAAAF,QAAA,eAC7BhE,OAAA;YAAKkE,SAAS,EAAC,KAAK;YAAAF,QAAA,eAChBhE,OAAA;cAAKkE,SAAS,EAAC,qFAAqF;cAAAF,QAAA,gBAChGhE,OAAA;gBAAK6F,KAAK,EAAE,GAAI;gBAACC,GAAG,EAAEjG,MAAO;gBAACkG,GAAG,EAAC;cAAqC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1EvE,OAAA;gBAAKkE,SAAS,EAAC,sBAAsB;gBAAAF,QAAA,eACjChE,OAAA;kBAAIkE,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNvE,OAAA;gBAAGkE,SAAS,EAAC,sCAAsC;gBAAAF,QAAA,eAAChE,OAAA;kBAAAgE,QAAA,EAASzE,QAAQ,CAACyG;gBAAsB;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC,gBACR,CAAC,EAEN7D,gBAAgB,iBACbV,OAAA;QAAKkE,SAAS,EAAC,WAAW;QAAAF,QAAA,eACtBhE,OAAA;UAAKkC,EAAE,EAAC,WAAW;UAAA8B,QAAA,eACfhE,OAAA;YAAKkE,SAAS,EAAC,MAAM;YAAAF,QAAA,eACjBhE,OAAA;cAAKkE,SAAS,EAAC,YAAY;cAAAF,QAAA,gBACvBhE,OAAA;gBAAKkE,SAAS,EAAC,oBAAoB;gBAAAF,QAAA,eAC/BhE,OAAA;kBAAIkE,SAAS,EAAC,aAAa;kBAAAF,QAAA,EACtBzE,QAAQ,CAAC0G;gBAAS;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,aAAa;gBAAAF,QAAA,eAExBhE,OAAA,CAACR,KAAK;kBAAC0E,SAAS,EAAC,WAAW;kBAACgC,IAAI,EAAC,MAAM;kBAACxE,IAAI,EAAEZ,SAAU;kBAACsE,OAAO,EAAEX;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,+BAA+B;gBAAAF,QAAA,eAC1ChE,OAAA;kBAAMkE,SAAS,EAAC,aAAa;kBAAAF,QAAA,EAAE1D,MAAM,KAAK,EAAE,GAAGA,MAAM,GAAG;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNvE,OAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,oBAAoB;gBAAAF,QAAA,eAC/BhE,OAAA;kBAAIkE,SAAS,EAAC,aAAa;kBAAAF,QAAA,EACtBzE,QAAQ,CAAC4G;gBAAU;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,aAAa;gBAAAF,QAAA,eACxBhE,OAAA,CAACR,KAAK;kBAAC0E,SAAS,EAAC,WAAW;kBAACgC,IAAI,EAAC,MAAM;kBAACxE,IAAI,EAAEV,UAAW;kBAACoE,OAAO,EAAEX;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,+BAA+B;gBAAAF,QAAA,eAC1ChE,OAAA;kBAAMkE,SAAS,EAAC,aAAa;kBAAAF,QAAA,EAAExD,OAAO,KAAK,EAAE,GAAGA,OAAO,GAAG;gBAAQ;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEVvE,OAAA,CAACL,MAAM;QAACyG,MAAM,EAAC,sCAAsC;QAACC,OAAO,EAAE/E,OAAQ;QAAC6B,KAAK,EAAE;UAAE0C,KAAK,EAAE;QAAO,CAAE;QAACS,MAAM,EAAEvC,YAAY,CAAC,CAAE;QAACwC,MAAM,EAAEA,CAAA,KAAMzC,UAAU,CAAC,CAAE;QAAAE,QAAA,gBACjJhE,OAAA,CAACF,WAAW;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfvE,OAAA,CAACN,eAAe;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC,gBACR,CAAC;AAGX,CAAC;AAAAlE,EAAA,CArSYF,WAAW;AAAAqG,EAAA,GAAXrG,WAAW;AAAA,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}