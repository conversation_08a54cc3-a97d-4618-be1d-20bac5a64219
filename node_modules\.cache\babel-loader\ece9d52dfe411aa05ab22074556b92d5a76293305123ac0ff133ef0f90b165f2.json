{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { observe, unobserve } from '../utils/observerUtil';\nimport DomWrapper from './DomWrapper';\nimport { CollectionContext } from '../Collection';\nexport default function SingleObserver(props) {\n  var children = props.children,\n    disabled = props.disabled;\n  var elementRef = React.useRef(null);\n  var wrapperRef = React.useRef(null);\n  var onCollectionResize = React.useContext(CollectionContext); // =========================== Children ===========================\n\n  var isRenderProps = typeof children === 'function';\n  var mergedChildren = isRenderProps ? children(elementRef) : children; // ============================= Size =============================\n\n  var sizeRef = React.useRef({\n    width: -1,\n    height: -1,\n    offsetWidth: -1,\n    offsetHeight: -1\n  }); // ============================= Ref ==============================\n\n  var canRef = !isRenderProps && /*#__PURE__*/React.isValidElement(mergedChildren) && supportRef(mergedChildren);\n  var originRef = canRef ? mergedChildren.ref : null;\n  var mergedRef = React.useMemo(function () {\n    return composeRef(originRef, elementRef);\n  }, [originRef, elementRef]); // =========================== Observe ============================\n\n  var propsRef = React.useRef(props);\n  propsRef.current = props; // Handler\n\n  var onInternalResize = React.useCallback(function (target) {\n    var _propsRef$current = propsRef.current,\n      onResize = _propsRef$current.onResize,\n      data = _propsRef$current.data;\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n      width = _target$getBoundingCl.width,\n      height = _target$getBoundingCl.height;\n    var offsetWidth = target.offsetWidth,\n      offsetHeight = target.offsetHeight;\n    /**\n     * Resize observer trigger when content size changed.\n     * In most case we just care about element size,\n     * let's use `boundary` instead of `contentRect` here to avoid shaking.\n     */\n\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n    if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {\n      var size = {\n        width: fixedWidth,\n        height: fixedHeight,\n        offsetWidth: offsetWidth,\n        offsetHeight: offsetHeight\n      };\n      sizeRef.current = size; // IE is strange, right?\n\n      var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;\n      var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;\n      var sizeInfo = _objectSpread(_objectSpread({}, size), {}, {\n        offsetWidth: mergedOffsetWidth,\n        offsetHeight: mergedOffsetHeight\n      }); // Let collection know what happened\n\n      onCollectionResize === null || onCollectionResize === void 0 ? void 0 : onCollectionResize(sizeInfo, target, data);\n      if (onResize) {\n        // defer the callback but not defer to next frame\n        Promise.resolve().then(function () {\n          onResize(sizeInfo, target);\n        });\n      }\n    }\n  }, []); // Dynamic observe\n\n  React.useEffect(function () {\n    var currentElement = findDOMNode(elementRef.current) || findDOMNode(wrapperRef.current);\n    if (currentElement && !disabled) {\n      observe(currentElement, onInternalResize);\n    }\n    return function () {\n      return unobserve(currentElement, onInternalResize);\n    };\n  }, [elementRef.current, disabled]); // ============================ Render ============================\n\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(mergedChildren, {\n    ref: mergedRef\n  }) : mergedChildren);\n}", "map": {"version": 3, "names": ["_objectSpread", "composeRef", "supportRef", "React", "findDOMNode", "observe", "unobserve", "DomWrapper", "CollectionContext", "SingleObserver", "props", "children", "disabled", "elementRef", "useRef", "wrapperRef", "onCollectionResize", "useContext", "isRenderProps", "mergedChildren", "sizeRef", "width", "height", "offsetWidth", "offsetHeight", "canRef", "isValidElement", "originRef", "ref", "mergedRef", "useMemo", "propsRef", "current", "onInternalResize", "useCallback", "target", "_propsRef$current", "onResize", "data", "_target$getBoundingCl", "getBoundingClientRect", "fixedWidth", "Math", "floor", "fixedHeight", "size", "mergedOffsetWidth", "round", "mergedOffsetHeight", "sizeInfo", "Promise", "resolve", "then", "useEffect", "currentElement", "createElement", "cloneElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-resize-observer/es/SingleObserver/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { observe, unobserve } from '../utils/observerUtil';\nimport DomWrapper from './DomWrapper';\nimport { CollectionContext } from '../Collection';\nexport default function SingleObserver(props) {\n  var children = props.children,\n      disabled = props.disabled;\n  var elementRef = React.useRef(null);\n  var wrapperRef = React.useRef(null);\n  var onCollectionResize = React.useContext(CollectionContext); // =========================== Children ===========================\n\n  var isRenderProps = typeof children === 'function';\n  var mergedChildren = isRenderProps ? children(elementRef) : children; // ============================= Size =============================\n\n  var sizeRef = React.useRef({\n    width: -1,\n    height: -1,\n    offsetWidth: -1,\n    offsetHeight: -1\n  }); // ============================= Ref ==============================\n\n  var canRef = !isRenderProps && /*#__PURE__*/React.isValidElement(mergedChildren) && supportRef(mergedChildren);\n  var originRef = canRef ? mergedChildren.ref : null;\n  var mergedRef = React.useMemo(function () {\n    return composeRef(originRef, elementRef);\n  }, [originRef, elementRef]); // =========================== Observe ============================\n\n  var propsRef = React.useRef(props);\n  propsRef.current = props; // Handler\n\n  var onInternalResize = React.useCallback(function (target) {\n    var _propsRef$current = propsRef.current,\n        onResize = _propsRef$current.onResize,\n        data = _propsRef$current.data;\n\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n        width = _target$getBoundingCl.width,\n        height = _target$getBoundingCl.height;\n\n    var offsetWidth = target.offsetWidth,\n        offsetHeight = target.offsetHeight;\n    /**\n     * Resize observer trigger when content size changed.\n     * In most case we just care about element size,\n     * let's use `boundary` instead of `contentRect` here to avoid shaking.\n     */\n\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n\n    if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {\n      var size = {\n        width: fixedWidth,\n        height: fixedHeight,\n        offsetWidth: offsetWidth,\n        offsetHeight: offsetHeight\n      };\n      sizeRef.current = size; // IE is strange, right?\n\n      var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;\n      var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;\n\n      var sizeInfo = _objectSpread(_objectSpread({}, size), {}, {\n        offsetWidth: mergedOffsetWidth,\n        offsetHeight: mergedOffsetHeight\n      }); // Let collection know what happened\n\n\n      onCollectionResize === null || onCollectionResize === void 0 ? void 0 : onCollectionResize(sizeInfo, target, data);\n\n      if (onResize) {\n        // defer the callback but not defer to next frame\n        Promise.resolve().then(function () {\n          onResize(sizeInfo, target);\n        });\n      }\n    }\n  }, []); // Dynamic observe\n\n  React.useEffect(function () {\n    var currentElement = findDOMNode(elementRef.current) || findDOMNode(wrapperRef.current);\n\n    if (currentElement && !disabled) {\n      observe(currentElement, onInternalResize);\n    }\n\n    return function () {\n      return unobserve(currentElement, onInternalResize);\n    };\n  }, [elementRef.current, disabled]); // ============================ Render ============================\n\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(mergedChildren, {\n    ref: mergedRef\n  }) : mergedChildren);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,EAAEC,SAAS,QAAQ,uBAAuB;AAC1D,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,iBAAiB,QAAQ,eAAe;AACjD,eAAe,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC5C,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IACzBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;EAC7B,IAAIC,UAAU,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,UAAU,GAAGZ,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIE,kBAAkB,GAAGb,KAAK,CAACc,UAAU,CAACT,iBAAiB,CAAC,CAAC,CAAC;;EAE9D,IAAIU,aAAa,GAAG,OAAOP,QAAQ,KAAK,UAAU;EAClD,IAAIQ,cAAc,GAAGD,aAAa,GAAGP,QAAQ,CAACE,UAAU,CAAC,GAAGF,QAAQ,CAAC,CAAC;;EAEtE,IAAIS,OAAO,GAAGjB,KAAK,CAACW,MAAM,CAAC;IACzBO,KAAK,EAAE,CAAC,CAAC;IACTC,MAAM,EAAE,CAAC,CAAC;IACVC,WAAW,EAAE,CAAC,CAAC;IACfC,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,MAAM,GAAG,CAACP,aAAa,IAAI,aAAaf,KAAK,CAACuB,cAAc,CAACP,cAAc,CAAC,IAAIjB,UAAU,CAACiB,cAAc,CAAC;EAC9G,IAAIQ,SAAS,GAAGF,MAAM,GAAGN,cAAc,CAACS,GAAG,GAAG,IAAI;EAClD,IAAIC,SAAS,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,YAAY;IACxC,OAAO7B,UAAU,CAAC0B,SAAS,EAAEd,UAAU,CAAC;EAC1C,CAAC,EAAE,CAACc,SAAS,EAAEd,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE7B,IAAIkB,QAAQ,GAAG5B,KAAK,CAACW,MAAM,CAACJ,KAAK,CAAC;EAClCqB,QAAQ,CAACC,OAAO,GAAGtB,KAAK,CAAC,CAAC;;EAE1B,IAAIuB,gBAAgB,GAAG9B,KAAK,CAAC+B,WAAW,CAAC,UAAUC,MAAM,EAAE;IACzD,IAAIC,iBAAiB,GAAGL,QAAQ,CAACC,OAAO;MACpCK,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;MACrCC,IAAI,GAAGF,iBAAiB,CAACE,IAAI;IAEjC,IAAIC,qBAAqB,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MACtDnB,KAAK,GAAGkB,qBAAqB,CAAClB,KAAK;MACnCC,MAAM,GAAGiB,qBAAqB,CAACjB,MAAM;IAEzC,IAAIC,WAAW,GAAGY,MAAM,CAACZ,WAAW;MAChCC,YAAY,GAAGW,MAAM,CAACX,YAAY;IACtC;AACJ;AACA;AACA;AACA;;IAEI,IAAIiB,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACtB,KAAK,CAAC;IAClC,IAAIuB,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACrB,MAAM,CAAC;IAEpC,IAAIF,OAAO,CAACY,OAAO,CAACX,KAAK,KAAKoB,UAAU,IAAIrB,OAAO,CAACY,OAAO,CAACV,MAAM,KAAKsB,WAAW,IAAIxB,OAAO,CAACY,OAAO,CAACT,WAAW,KAAKA,WAAW,IAAIH,OAAO,CAACY,OAAO,CAACR,YAAY,KAAKA,YAAY,EAAE;MAClL,IAAIqB,IAAI,GAAG;QACTxB,KAAK,EAAEoB,UAAU;QACjBnB,MAAM,EAAEsB,WAAW;QACnBrB,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA;MAChB,CAAC;MACDJ,OAAO,CAACY,OAAO,GAAGa,IAAI,CAAC,CAAC;;MAExB,IAAIC,iBAAiB,GAAGvB,WAAW,KAAKmB,IAAI,CAACK,KAAK,CAAC1B,KAAK,CAAC,GAAGA,KAAK,GAAGE,WAAW;MAC/E,IAAIyB,kBAAkB,GAAGxB,YAAY,KAAKkB,IAAI,CAACK,KAAK,CAACzB,MAAM,CAAC,GAAGA,MAAM,GAAGE,YAAY;MAEpF,IAAIyB,QAAQ,GAAGjD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6C,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACxDtB,WAAW,EAAEuB,iBAAiB;QAC9BtB,YAAY,EAAEwB;MAChB,CAAC,CAAC,CAAC,CAAC;;MAGJhC,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACiC,QAAQ,EAAEd,MAAM,EAAEG,IAAI,CAAC;MAElH,IAAID,QAAQ,EAAE;QACZ;QACAa,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;UACjCf,QAAQ,CAACY,QAAQ,EAAEd,MAAM,CAAC;QAC5B,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERhC,KAAK,CAACkD,SAAS,CAAC,YAAY;IAC1B,IAAIC,cAAc,GAAGlD,WAAW,CAACS,UAAU,CAACmB,OAAO,CAAC,IAAI5B,WAAW,CAACW,UAAU,CAACiB,OAAO,CAAC;IAEvF,IAAIsB,cAAc,IAAI,CAAC1C,QAAQ,EAAE;MAC/BP,OAAO,CAACiD,cAAc,EAAErB,gBAAgB,CAAC;IAC3C;IAEA,OAAO,YAAY;MACjB,OAAO3B,SAAS,CAACgD,cAAc,EAAErB,gBAAgB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACpB,UAAU,CAACmB,OAAO,EAAEpB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpC,OAAO,aAAaT,KAAK,CAACoD,aAAa,CAAChD,UAAU,EAAE;IAClDqB,GAAG,EAAEb;EACP,CAAC,EAAEU,MAAM,GAAG,aAAatB,KAAK,CAACqD,YAAY,CAACrC,cAAc,EAAE;IAC1DS,GAAG,EAAEC;EACP,CAAC,CAAC,GAAGV,cAAc,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}