{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nimport { useState, useEffect, useContext, useRef } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context';\nimport { warnOnce, loadNamespaces, hasLoadedNamespace } from './utils';\nexport function useTranslation(ns) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var i18nFromProps = props.i18n;\n  var _ref = useContext(I18nContext) || {},\n    i18nFromContext = _ref.i18n,\n    defaultNSFromContext = _ref.defaultNS;\n  var i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using initReactI18next');\n    var notReadyT = function notReadyT(k) {\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    var retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react && i18n.options.react.wait !== undefined) warnOnce('It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  var i18nOptions = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), i18n.options.react), props);\n  var useSuspense = i18nOptions.useSuspense,\n    keyPrefix = i18nOptions.keyPrefix;\n  var namespaces = ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  if (i18n.reportNamespaces.addUsedNamespaces) i18n.reportNamespaces.addUsedNamespaces(namespaces);\n  var ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(function (n) {\n    return hasLoadedNamespace(n, i18n, i18nOptions);\n  });\n  function getT() {\n    return i18n.getFixedT(null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  }\n  var _useState = useState(getT),\n    _useState2 = _slicedToArray(_useState, 2),\n    t = _useState2[0],\n    setT = _useState2[1];\n  var isMounted = useRef(true);\n  useEffect(function () {\n    var bindI18n = i18nOptions.bindI18n,\n      bindI18nStore = i18nOptions.bindI18nStore;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      loadNamespaces(i18n, namespaces, function () {\n        if (isMounted.current) setT(getT);\n      });\n    }\n    function boundReset() {\n      if (isMounted.current) setT(getT);\n    }\n    if (bindI18n && i18n) i18n.on(bindI18n, boundReset);\n    if (bindI18nStore && i18n) i18n.store.on(bindI18nStore, boundReset);\n    return function () {\n      isMounted.current = false;\n      if (bindI18n && i18n) bindI18n.split(' ').forEach(function (e) {\n        return i18n.off(e, boundReset);\n      });\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(function (e) {\n        return i18n.store.off(e, boundReset);\n      });\n    };\n  }, [i18n, namespaces.join()]);\n  var isInitial = useRef(true);\n  useEffect(function () {\n    if (isMounted.current && !isInitial.current) {\n      setT(getT);\n    }\n    isInitial.current = false;\n  }, [i18n]);\n  var ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(function (resolve) {\n    loadNamespaces(i18n, namespaces, function () {\n      resolve();\n    });\n  });\n}", "map": {"version": 3, "names": ["_slicedToArray", "_defineProperty", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "useState", "useEffect", "useContext", "useRef", "getI18n", "getDefaults", "ReportNamespaces", "I18nContext", "warnOnce", "loadNamespaces", "hasLoadedNamespace", "useTranslation", "ns", "props", "undefined", "i18nFromProps", "i18n", "_ref", "i18nFromContext", "defaultNSFromContext", "defaultNS", "reportNamespaces", "notReadyT", "k", "Array", "isArray", "retNotReady", "t", "ready", "options", "react", "wait", "i18nOptions", "useSuspense", "keyPrefix", "namespaces", "addUsedNamespaces", "isInitialized", "initializedStoreOnce", "every", "n", "getT", "getFixedT", "nsMode", "_useState", "_useState2", "setT", "isMounted", "bindI18n", "bindI18nStore", "current", "boundReset", "on", "store", "split", "e", "off", "join", "isInitial", "ret", "Promise", "resolve"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/useTranslation.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport { useState, useEffect, useContext, useRef } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context';\nimport { warnOnce, loadNamespaces, hasLoadedNamespace } from './utils';\nexport function useTranslation(ns) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var i18nFromProps = props.i18n;\n\n  var _ref = useContext(I18nContext) || {},\n      i18nFromContext = _ref.i18n,\n      defaultNSFromContext = _ref.defaultNS;\n\n  var i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using initReactI18next');\n\n    var notReadyT = function notReadyT(k) {\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n\n    var retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n\n  if (i18n.options.react && i18n.options.react.wait !== undefined) warnOnce('It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n\n  var i18nOptions = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), i18n.options.react), props);\n\n  var useSuspense = i18nOptions.useSuspense,\n      keyPrefix = i18nOptions.keyPrefix;\n  var namespaces = ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  if (i18n.reportNamespaces.addUsedNamespaces) i18n.reportNamespaces.addUsedNamespaces(namespaces);\n  var ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(function (n) {\n    return hasLoadedNamespace(n, i18n, i18nOptions);\n  });\n\n  function getT() {\n    return i18n.getFixedT(null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  }\n\n  var _useState = useState(getT),\n      _useState2 = _slicedToArray(_useState, 2),\n      t = _useState2[0],\n      setT = _useState2[1];\n\n  var isMounted = useRef(true);\n  useEffect(function () {\n    var bindI18n = i18nOptions.bindI18n,\n        bindI18nStore = i18nOptions.bindI18nStore;\n    isMounted.current = true;\n\n    if (!ready && !useSuspense) {\n      loadNamespaces(i18n, namespaces, function () {\n        if (isMounted.current) setT(getT);\n      });\n    }\n\n    function boundReset() {\n      if (isMounted.current) setT(getT);\n    }\n\n    if (bindI18n && i18n) i18n.on(bindI18n, boundReset);\n    if (bindI18nStore && i18n) i18n.store.on(bindI18nStore, boundReset);\n    return function () {\n      isMounted.current = false;\n      if (bindI18n && i18n) bindI18n.split(' ').forEach(function (e) {\n        return i18n.off(e, boundReset);\n      });\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(function (e) {\n        return i18n.store.off(e, boundReset);\n      });\n    };\n  }, [i18n, namespaces.join()]);\n  var isInitial = useRef(true);\n  useEffect(function () {\n    if (isMounted.current && !isInitial.current) {\n      setT(getT);\n    }\n\n    isInitial.current = false;\n  }, [i18n]);\n  var ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(function (resolve) {\n    loadNamespaces(i18n, namespaces, function () {\n      resolve();\n    });\n  });\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sCAAsC;AACjE,OAAOC,eAAe,MAAM,uCAAuC;AAEnE,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAErB,eAAe,CAACe,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACiB,yBAAyB,EAAE;MAAEjB,MAAM,CAACkB,gBAAgB,CAACR,MAAM,EAAEV,MAAM,CAACiB,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACmB,cAAc,CAACT,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,SAASU,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC/D,SAASC,OAAO,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,WAAW;AAC/E,SAASC,QAAQ,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,SAAS;AACtE,OAAO,SAASC,cAAcA,CAACC,EAAE,EAAE;EACjC,IAAIC,KAAK,GAAGrB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsB,SAAS,GAAGtB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIuB,aAAa,GAAGF,KAAK,CAACG,IAAI;EAE9B,IAAIC,IAAI,GAAGf,UAAU,CAACK,WAAW,CAAC,IAAI,CAAC,CAAC;IACpCW,eAAe,GAAGD,IAAI,CAACD,IAAI;IAC3BG,oBAAoB,GAAGF,IAAI,CAACG,SAAS;EAEzC,IAAIJ,IAAI,GAAGD,aAAa,IAAIG,eAAe,IAAId,OAAO,CAAC,CAAC;EACxD,IAAIY,IAAI,IAAI,CAACA,IAAI,CAACK,gBAAgB,EAAEL,IAAI,CAACK,gBAAgB,GAAG,IAAIf,gBAAgB,CAAC,CAAC;EAElF,IAAI,CAACU,IAAI,EAAE;IACTR,QAAQ,CAAC,wEAAwE,CAAC;IAElF,IAAIc,SAAS,GAAG,SAASA,SAASA,CAACC,CAAC,EAAE;MACpC,OAAOC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAAC9B,MAAM,GAAG,CAAC,CAAC,GAAG8B,CAAC;IAC/C,CAAC;IAED,IAAIG,WAAW,GAAG,CAACJ,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IACxCI,WAAW,CAACC,CAAC,GAAGL,SAAS;IACzBI,WAAW,CAACV,IAAI,GAAG,CAAC,CAAC;IACrBU,WAAW,CAACE,KAAK,GAAG,KAAK;IACzB,OAAOF,WAAW;EACpB;EAEA,IAAIV,IAAI,CAACa,OAAO,CAACC,KAAK,IAAId,IAAI,CAACa,OAAO,CAACC,KAAK,CAACC,IAAI,KAAKjB,SAAS,EAAEN,QAAQ,CAAC,qGAAqG,CAAC;EAEhL,IAAIwB,WAAW,GAAG3C,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,WAAW,CAAC,CAAC,CAAC,EAAEW,IAAI,CAACa,OAAO,CAACC,KAAK,CAAC,EAAEjB,KAAK,CAAC;EAE3G,IAAIoB,WAAW,GAAGD,WAAW,CAACC,WAAW;IACrCC,SAAS,GAAGF,WAAW,CAACE,SAAS;EACrC,IAAIC,UAAU,GAAGvB,EAAE,IAAIO,oBAAoB,IAAIH,IAAI,CAACa,OAAO,IAAIb,IAAI,CAACa,OAAO,CAACT,SAAS;EACrFe,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAC1F,IAAInB,IAAI,CAACK,gBAAgB,CAACe,iBAAiB,EAAEpB,IAAI,CAACK,gBAAgB,CAACe,iBAAiB,CAACD,UAAU,CAAC;EAChG,IAAIP,KAAK,GAAG,CAACZ,IAAI,CAACqB,aAAa,IAAIrB,IAAI,CAACsB,oBAAoB,KAAKH,UAAU,CAACI,KAAK,CAAC,UAAUC,CAAC,EAAE;IAC7F,OAAO9B,kBAAkB,CAAC8B,CAAC,EAAExB,IAAI,EAAEgB,WAAW,CAAC;EACjD,CAAC,CAAC;EAEF,SAASS,IAAIA,CAAA,EAAG;IACd,OAAOzB,IAAI,CAAC0B,SAAS,CAAC,IAAI,EAAEV,WAAW,CAACW,MAAM,KAAK,UAAU,GAAGR,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC;EACxG;EAEA,IAAIU,SAAS,GAAG5C,QAAQ,CAACyC,IAAI,CAAC;IAC1BI,UAAU,GAAGvE,cAAc,CAACsE,SAAS,EAAE,CAAC,CAAC;IACzCjB,CAAC,GAAGkB,UAAU,CAAC,CAAC,CAAC;IACjBC,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;EAExB,IAAIE,SAAS,GAAG5C,MAAM,CAAC,IAAI,CAAC;EAC5BF,SAAS,CAAC,YAAY;IACpB,IAAI+C,QAAQ,GAAGhB,WAAW,CAACgB,QAAQ;MAC/BC,aAAa,GAAGjB,WAAW,CAACiB,aAAa;IAC7CF,SAAS,CAACG,OAAO,GAAG,IAAI;IAExB,IAAI,CAACtB,KAAK,IAAI,CAACK,WAAW,EAAE;MAC1BxB,cAAc,CAACO,IAAI,EAAEmB,UAAU,EAAE,YAAY;QAC3C,IAAIY,SAAS,CAACG,OAAO,EAAEJ,IAAI,CAACL,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ;IAEA,SAASU,UAAUA,CAAA,EAAG;MACpB,IAAIJ,SAAS,CAACG,OAAO,EAAEJ,IAAI,CAACL,IAAI,CAAC;IACnC;IAEA,IAAIO,QAAQ,IAAIhC,IAAI,EAAEA,IAAI,CAACoC,EAAE,CAACJ,QAAQ,EAAEG,UAAU,CAAC;IACnD,IAAIF,aAAa,IAAIjC,IAAI,EAAEA,IAAI,CAACqC,KAAK,CAACD,EAAE,CAACH,aAAa,EAAEE,UAAU,CAAC;IACnE,OAAO,YAAY;MACjBJ,SAAS,CAACG,OAAO,GAAG,KAAK;MACzB,IAAIF,QAAQ,IAAIhC,IAAI,EAAEgC,QAAQ,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC3D,OAAO,CAAC,UAAU4D,CAAC,EAAE;QAC7D,OAAOvC,IAAI,CAACwC,GAAG,CAACD,CAAC,EAAEJ,UAAU,CAAC;MAChC,CAAC,CAAC;MACF,IAAIF,aAAa,IAAIjC,IAAI,EAAEiC,aAAa,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC3D,OAAO,CAAC,UAAU4D,CAAC,EAAE;QACvE,OAAOvC,IAAI,CAACqC,KAAK,CAACG,GAAG,CAACD,CAAC,EAAEJ,UAAU,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACnC,IAAI,EAAEmB,UAAU,CAACsB,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAIC,SAAS,GAAGvD,MAAM,CAAC,IAAI,CAAC;EAC5BF,SAAS,CAAC,YAAY;IACpB,IAAI8C,SAAS,CAACG,OAAO,IAAI,CAACQ,SAAS,CAACR,OAAO,EAAE;MAC3CJ,IAAI,CAACL,IAAI,CAAC;IACZ;IAEAiB,SAAS,CAACR,OAAO,GAAG,KAAK;EAC3B,CAAC,EAAE,CAAClC,IAAI,CAAC,CAAC;EACV,IAAI2C,GAAG,GAAG,CAAChC,CAAC,EAAEX,IAAI,EAAEY,KAAK,CAAC;EAC1B+B,GAAG,CAAChC,CAAC,GAAGA,CAAC;EACTgC,GAAG,CAAC3C,IAAI,GAAGA,IAAI;EACf2C,GAAG,CAAC/B,KAAK,GAAGA,KAAK;EACjB,IAAIA,KAAK,EAAE,OAAO+B,GAAG;EACrB,IAAI,CAAC/B,KAAK,IAAI,CAACK,WAAW,EAAE,OAAO0B,GAAG;EACtC,MAAM,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;IACnCpD,cAAc,CAACO,IAAI,EAAEmB,UAAU,EAAE,YAAY;MAC3C0B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}