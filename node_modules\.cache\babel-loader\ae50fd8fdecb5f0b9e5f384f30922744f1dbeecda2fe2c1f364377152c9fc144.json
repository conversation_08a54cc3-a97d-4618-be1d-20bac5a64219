{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = require(\"react\");\nvar LocaleContext = /*#__PURE__*/(0, _react.createContext)(undefined);\nvar _default = LocaleContext;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_react", "require", "LocaleContext", "createContext", "undefined", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/locale-provider/context.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _react = require(\"react\");\n\nvar LocaleContext = /*#__PURE__*/(0, _react.createContext)(undefined);\nvar _default = LocaleContext;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,aAAa,GAAG,aAAa,CAAC,CAAC,EAAEF,MAAM,CAACG,aAAa,EAAEC,SAAS,CAAC;AACrE,IAAIC,QAAQ,GAAGH,aAAa;AAC5BJ,OAAO,CAAC,SAAS,CAAC,GAAGO,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}