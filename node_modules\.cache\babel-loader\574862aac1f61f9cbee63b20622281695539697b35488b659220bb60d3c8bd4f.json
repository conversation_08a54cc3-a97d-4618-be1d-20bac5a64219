{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"getContainer\"];\nimport * as React from 'react';\nimport { Component } from 'react';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport classNames from 'classnames';\nimport { CSSMotionList } from 'rc-motion';\nimport Notice from './Notice';\nimport _useNotification from './useNotification';\nvar seed = 0;\nvar now = Date.now();\nfunction getUuid() {\n  var id = seed;\n  seed += 1;\n  return \"rcNotification_\".concat(now, \"_\").concat(id);\n}\nvar Notification = /*#__PURE__*/function (_Component) {\n  _inherits(Notification, _Component);\n  var _super = _createSuper(Notification);\n  function Notification() {\n    var _this;\n    _classCallCheck(this, Notification);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      notices: []\n    };\n    _this.hookRefs = new Map();\n    _this.add = function (originNotice, holderCallback) {\n      var key = originNotice.key || getUuid();\n      var notice = _objectSpread(_objectSpread({}, originNotice), {}, {\n        key: key\n      });\n      var maxCount = _this.props.maxCount;\n      _this.setState(function (previousState) {\n        var notices = previousState.notices;\n        var noticeIndex = notices.map(function (v) {\n          return v.notice.key;\n        }).indexOf(key);\n        var updatedNotices = notices.concat();\n        if (noticeIndex !== -1) {\n          updatedNotices.splice(noticeIndex, 1, {\n            notice: notice,\n            holderCallback: holderCallback\n          });\n        } else {\n          if (maxCount && notices.length >= maxCount) {\n            // XXX, use key of first item to update new added (let React to move exsiting\n            // instead of remove and mount). Same key was used before for both a) external\n            // manual control and b) internal react 'key' prop , which is not that good.\n            // eslint-disable-next-line no-param-reassign\n            // zombieJ: Not know why use `updateKey`. This makes Notice infinite loop in jest.\n            // Change to `updateMark` for compare instead.\n            // https://github.com/react-component/notification/commit/32299e6be396f94040bfa82517eea940db947ece\n            notice.key = updatedNotices[0].notice.key;\n            notice.updateMark = getUuid(); // zombieJ: That's why. User may close by key directly.\n            // We need record this but not re-render to avoid upper issue\n            // https://github.com/react-component/notification/issues/129\n\n            notice.userPassKey = key;\n            updatedNotices.shift();\n          }\n          updatedNotices.push({\n            notice: notice,\n            holderCallback: holderCallback\n          });\n        }\n        return {\n          notices: updatedNotices\n        };\n      });\n    };\n    _this.remove = function (removeKey) {\n      _this.setState(function (_ref) {\n        var notices = _ref.notices;\n        return {\n          notices: notices.filter(function (_ref2) {\n            var _ref2$notice = _ref2.notice,\n              key = _ref2$notice.key,\n              userPassKey = _ref2$notice.userPassKey;\n            var mergedKey = userPassKey || key;\n            return mergedKey !== removeKey;\n          })\n        };\n      });\n    };\n    _this.noticePropsMap = {};\n    return _this;\n  }\n  _createClass(Notification, [{\n    key: \"getTransitionName\",\n    value: function getTransitionName() {\n      var _this$props = this.props,\n        prefixCls = _this$props.prefixCls,\n        animation = _this$props.animation;\n      var transitionName = this.props.transitionName;\n      if (!transitionName && animation) {\n        transitionName = \"\".concat(prefixCls, \"-\").concat(animation);\n      }\n      return transitionName;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var notices = this.state.notices;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        closeIcon = _this$props2.closeIcon,\n        style = _this$props2.style;\n      var noticeKeys = [];\n      notices.forEach(function (_ref3, index) {\n        var notice = _ref3.notice,\n          holderCallback = _ref3.holderCallback;\n        var updateMark = index === notices.length - 1 ? notice.updateMark : undefined;\n        var key = notice.key,\n          userPassKey = notice.userPassKey;\n        var noticeProps = _objectSpread(_objectSpread(_objectSpread({\n          prefixCls: prefixCls,\n          closeIcon: closeIcon\n        }, notice), notice.props), {}, {\n          key: key,\n          noticeKey: userPassKey || key,\n          updateMark: updateMark,\n          onClose: function onClose(noticeKey) {\n            var _notice$onClose;\n            _this2.remove(noticeKey);\n            (_notice$onClose = notice.onClose) === null || _notice$onClose === void 0 ? void 0 : _notice$onClose.call(notice);\n          },\n          onClick: notice.onClick,\n          children: notice.content\n        }); // Give to motion\n\n        noticeKeys.push(key);\n        _this2.noticePropsMap[key] = {\n          props: noticeProps,\n          holderCallback: holderCallback\n        };\n      });\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(prefixCls, className),\n        style: style\n      }, /*#__PURE__*/React.createElement(CSSMotionList, {\n        keys: noticeKeys,\n        motionName: this.getTransitionName(),\n        onVisibleChanged: function onVisibleChanged(changedVisible, _ref4) {\n          var key = _ref4.key;\n          if (!changedVisible) {\n            delete _this2.noticePropsMap[key];\n          }\n        }\n      }, function (_ref5) {\n        var key = _ref5.key,\n          motionClassName = _ref5.className,\n          motionStyle = _ref5.style,\n          visible = _ref5.visible;\n        var _this2$noticePropsMap = _this2.noticePropsMap[key],\n          noticeProps = _this2$noticePropsMap.props,\n          holderCallback = _this2$noticePropsMap.holderCallback;\n        if (holderCallback) {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            key: key,\n            className: classNames(motionClassName, \"\".concat(prefixCls, \"-hook-holder\")),\n            style: _objectSpread({}, motionStyle),\n            ref: function ref(div) {\n              if (typeof key === 'undefined') {\n                return;\n              }\n              if (div) {\n                _this2.hookRefs.set(key, div);\n                holderCallback(div, noticeProps);\n              } else {\n                _this2.hookRefs.delete(key);\n              }\n            }\n          });\n        }\n        return /*#__PURE__*/React.createElement(Notice, _extends({}, noticeProps, {\n          className: classNames(motionClassName, noticeProps === null || noticeProps === void 0 ? void 0 : noticeProps.className),\n          style: _objectSpread(_objectSpread({}, motionStyle), noticeProps === null || noticeProps === void 0 ? void 0 : noticeProps.style),\n          visible: visible\n        }));\n      }));\n    }\n  }]);\n  return Notification;\n}(Component);\nNotification.newInstance = void 0;\nNotification.defaultProps = {\n  prefixCls: 'rc-notification',\n  animation: 'fade',\n  style: {\n    top: 65,\n    left: '50%'\n  }\n};\nNotification.newInstance = function newNotificationInstance(properties, callback) {\n  var _ref6 = properties || {},\n    getContainer = _ref6.getContainer,\n    props = _objectWithoutProperties(_ref6, _excluded);\n  var div = document.createElement('div');\n  if (getContainer) {\n    var root = getContainer();\n    root.appendChild(div);\n  } else {\n    document.body.appendChild(div);\n  }\n  var called = false;\n  function ref(notification) {\n    if (called) {\n      return;\n    }\n    called = true;\n    callback({\n      notice: function notice(noticeProps) {\n        notification.add(noticeProps);\n      },\n      removeNotice: function removeNotice(key) {\n        notification.remove(key);\n      },\n      component: notification,\n      destroy: function destroy() {\n        unmount(div);\n        if (div.parentNode) {\n          div.parentNode.removeChild(div);\n        }\n      },\n      // Hooks\n      useNotification: function useNotification() {\n        return _useNotification(notification);\n      }\n    });\n  } // Only used for test case usage\n\n  if (process.env.NODE_ENV === 'test' && properties.TEST_RENDER) {\n    properties.TEST_RENDER(/*#__PURE__*/React.createElement(Notification, _extends({}, props, {\n      ref: ref\n    })));\n    return;\n  }\n  render(/*#__PURE__*/React.createElement(Notification, _extends({}, props, {\n    ref: ref\n  })), div);\n};\nexport default Notification;", "map": {"version": 3, "names": ["_objectWithoutProperties", "_extends", "_objectSpread", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "_excluded", "React", "Component", "render", "unmount", "classNames", "CSSMotionList", "Notice", "_useNotification", "seed", "now", "Date", "getUuid", "id", "concat", "Notification", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "state", "notices", "hookRefs", "Map", "add", "originNotice", "<PERSON><PERSON><PERSON><PERSON>", "key", "notice", "maxCount", "props", "setState", "previousState", "noticeIndex", "map", "v", "indexOf", "updatedNotices", "splice", "updateMark", "userPassKey", "shift", "push", "remove", "<PERSON><PERSON><PERSON>", "_ref", "filter", "_ref2", "_ref2$notice", "mergedKey", "noticePropsMap", "value", "getTransitionName", "_this$props", "prefixCls", "animation", "transitionName", "_this2", "_this$props2", "className", "closeIcon", "style", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "_ref3", "index", "undefined", "noticeProps", "<PERSON><PERSON><PERSON>", "onClose", "_notice$onClose", "onClick", "children", "content", "createElement", "keys", "motionName", "onVisibleChanged", "changedVisible", "_ref4", "_ref5", "motionClassName", "motionStyle", "visible", "_this2$noticePropsMap", "ref", "div", "set", "delete", "newInstance", "defaultProps", "top", "left", "newNotificationInstance", "properties", "callback", "_ref6", "getContainer", "document", "root", "append<PERSON><PERSON><PERSON>", "body", "called", "notification", "removeNotice", "component", "destroy", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "useNotification", "process", "env", "NODE_ENV", "TEST_RENDER"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-notification/es/Notification.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"getContainer\"];\nimport * as React from 'react';\nimport { Component } from 'react';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport classNames from 'classnames';\nimport { CSSMotionList } from 'rc-motion';\nimport Notice from './Notice';\nimport _useNotification from './useNotification';\nvar seed = 0;\nvar now = Date.now();\n\nfunction getUuid() {\n  var id = seed;\n  seed += 1;\n  return \"rcNotification_\".concat(now, \"_\").concat(id);\n}\n\nvar Notification = /*#__PURE__*/function (_Component) {\n  _inherits(Notification, _Component);\n\n  var _super = _createSuper(Notification);\n\n  function Notification() {\n    var _this;\n\n    _classCallCheck(this, Notification);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      notices: []\n    };\n    _this.hookRefs = new Map();\n\n    _this.add = function (originNotice, holderCallback) {\n      var key = originNotice.key || getUuid();\n\n      var notice = _objectSpread(_objectSpread({}, originNotice), {}, {\n        key: key\n      });\n\n      var maxCount = _this.props.maxCount;\n\n      _this.setState(function (previousState) {\n        var notices = previousState.notices;\n        var noticeIndex = notices.map(function (v) {\n          return v.notice.key;\n        }).indexOf(key);\n        var updatedNotices = notices.concat();\n\n        if (noticeIndex !== -1) {\n          updatedNotices.splice(noticeIndex, 1, {\n            notice: notice,\n            holderCallback: holderCallback\n          });\n        } else {\n          if (maxCount && notices.length >= maxCount) {\n            // XXX, use key of first item to update new added (let React to move exsiting\n            // instead of remove and mount). Same key was used before for both a) external\n            // manual control and b) internal react 'key' prop , which is not that good.\n            // eslint-disable-next-line no-param-reassign\n            // zombieJ: Not know why use `updateKey`. This makes Notice infinite loop in jest.\n            // Change to `updateMark` for compare instead.\n            // https://github.com/react-component/notification/commit/32299e6be396f94040bfa82517eea940db947ece\n            notice.key = updatedNotices[0].notice.key;\n            notice.updateMark = getUuid(); // zombieJ: That's why. User may close by key directly.\n            // We need record this but not re-render to avoid upper issue\n            // https://github.com/react-component/notification/issues/129\n\n            notice.userPassKey = key;\n            updatedNotices.shift();\n          }\n\n          updatedNotices.push({\n            notice: notice,\n            holderCallback: holderCallback\n          });\n        }\n\n        return {\n          notices: updatedNotices\n        };\n      });\n    };\n\n    _this.remove = function (removeKey) {\n      _this.setState(function (_ref) {\n        var notices = _ref.notices;\n        return {\n          notices: notices.filter(function (_ref2) {\n            var _ref2$notice = _ref2.notice,\n                key = _ref2$notice.key,\n                userPassKey = _ref2$notice.userPassKey;\n            var mergedKey = userPassKey || key;\n            return mergedKey !== removeKey;\n          })\n        };\n      });\n    };\n\n    _this.noticePropsMap = {};\n    return _this;\n  }\n\n  _createClass(Notification, [{\n    key: \"getTransitionName\",\n    value: function getTransitionName() {\n      var _this$props = this.props,\n          prefixCls = _this$props.prefixCls,\n          animation = _this$props.animation;\n      var transitionName = this.props.transitionName;\n\n      if (!transitionName && animation) {\n        transitionName = \"\".concat(prefixCls, \"-\").concat(animation);\n      }\n\n      return transitionName;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var notices = this.state.notices;\n      var _this$props2 = this.props,\n          prefixCls = _this$props2.prefixCls,\n          className = _this$props2.className,\n          closeIcon = _this$props2.closeIcon,\n          style = _this$props2.style;\n      var noticeKeys = [];\n      notices.forEach(function (_ref3, index) {\n        var notice = _ref3.notice,\n            holderCallback = _ref3.holderCallback;\n        var updateMark = index === notices.length - 1 ? notice.updateMark : undefined;\n        var key = notice.key,\n            userPassKey = notice.userPassKey;\n\n        var noticeProps = _objectSpread(_objectSpread(_objectSpread({\n          prefixCls: prefixCls,\n          closeIcon: closeIcon\n        }, notice), notice.props), {}, {\n          key: key,\n          noticeKey: userPassKey || key,\n          updateMark: updateMark,\n          onClose: function onClose(noticeKey) {\n            var _notice$onClose;\n\n            _this2.remove(noticeKey);\n\n            (_notice$onClose = notice.onClose) === null || _notice$onClose === void 0 ? void 0 : _notice$onClose.call(notice);\n          },\n          onClick: notice.onClick,\n          children: notice.content\n        }); // Give to motion\n\n\n        noticeKeys.push(key);\n        _this2.noticePropsMap[key] = {\n          props: noticeProps,\n          holderCallback: holderCallback\n        };\n      });\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(prefixCls, className),\n        style: style\n      }, /*#__PURE__*/React.createElement(CSSMotionList, {\n        keys: noticeKeys,\n        motionName: this.getTransitionName(),\n        onVisibleChanged: function onVisibleChanged(changedVisible, _ref4) {\n          var key = _ref4.key;\n\n          if (!changedVisible) {\n            delete _this2.noticePropsMap[key];\n          }\n        }\n      }, function (_ref5) {\n        var key = _ref5.key,\n            motionClassName = _ref5.className,\n            motionStyle = _ref5.style,\n            visible = _ref5.visible;\n        var _this2$noticePropsMap = _this2.noticePropsMap[key],\n            noticeProps = _this2$noticePropsMap.props,\n            holderCallback = _this2$noticePropsMap.holderCallback;\n\n        if (holderCallback) {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            key: key,\n            className: classNames(motionClassName, \"\".concat(prefixCls, \"-hook-holder\")),\n            style: _objectSpread({}, motionStyle),\n            ref: function ref(div) {\n              if (typeof key === 'undefined') {\n                return;\n              }\n\n              if (div) {\n                _this2.hookRefs.set(key, div);\n\n                holderCallback(div, noticeProps);\n              } else {\n                _this2.hookRefs.delete(key);\n              }\n            }\n          });\n        }\n\n        return /*#__PURE__*/React.createElement(Notice, _extends({}, noticeProps, {\n          className: classNames(motionClassName, noticeProps === null || noticeProps === void 0 ? void 0 : noticeProps.className),\n          style: _objectSpread(_objectSpread({}, motionStyle), noticeProps === null || noticeProps === void 0 ? void 0 : noticeProps.style),\n          visible: visible\n        }));\n      }));\n    }\n  }]);\n\n  return Notification;\n}(Component);\n\nNotification.newInstance = void 0;\nNotification.defaultProps = {\n  prefixCls: 'rc-notification',\n  animation: 'fade',\n  style: {\n    top: 65,\n    left: '50%'\n  }\n};\n\nNotification.newInstance = function newNotificationInstance(properties, callback) {\n  var _ref6 = properties || {},\n      getContainer = _ref6.getContainer,\n      props = _objectWithoutProperties(_ref6, _excluded);\n\n  var div = document.createElement('div');\n\n  if (getContainer) {\n    var root = getContainer();\n    root.appendChild(div);\n  } else {\n    document.body.appendChild(div);\n  }\n\n  var called = false;\n\n  function ref(notification) {\n    if (called) {\n      return;\n    }\n\n    called = true;\n    callback({\n      notice: function notice(noticeProps) {\n        notification.add(noticeProps);\n      },\n      removeNotice: function removeNotice(key) {\n        notification.remove(key);\n      },\n      component: notification,\n      destroy: function destroy() {\n        unmount(div);\n\n        if (div.parentNode) {\n          div.parentNode.removeChild(div);\n        }\n      },\n      // Hooks\n      useNotification: function useNotification() {\n        return _useNotification(notification);\n      }\n    });\n  } // Only used for test case usage\n\n\n  if (process.env.NODE_ENV === 'test' && properties.TEST_RENDER) {\n    properties.TEST_RENDER( /*#__PURE__*/React.createElement(Notification, _extends({}, props, {\n      ref: ref\n    })));\n    return;\n  }\n\n  render( /*#__PURE__*/React.createElement(Notification, _extends({}, props, {\n    ref: ref\n  })), div);\n};\n\nexport default Notification;"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,cAAc,CAAC;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,EAAEC,OAAO,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,gBAAgB,MAAM,mBAAmB;AAChD,IAAIC,IAAI,GAAG,CAAC;AACZ,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;AAEpB,SAASE,OAAOA,CAAA,EAAG;EACjB,IAAIC,EAAE,GAAGJ,IAAI;EACbA,IAAI,IAAI,CAAC;EACT,OAAO,iBAAiB,CAACK,MAAM,CAACJ,GAAG,EAAE,GAAG,CAAC,CAACI,MAAM,CAACD,EAAE,CAAC;AACtD;AAEA,IAAIE,YAAY,GAAG,aAAa,UAAUC,UAAU,EAAE;EACpDlB,SAAS,CAACiB,YAAY,EAAEC,UAAU,CAAC;EAEnC,IAAIC,MAAM,GAAGlB,YAAY,CAACgB,YAAY,CAAC;EAEvC,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIG,KAAK;IAETtB,eAAe,CAAC,IAAI,EAAEmB,YAAY,CAAC;IAEnC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACH,MAAM,CAACQ,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACS,KAAK,GAAG;MACZC,OAAO,EAAE;IACX,CAAC;IACDV,KAAK,CAACW,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE1BZ,KAAK,CAACa,GAAG,GAAG,UAAUC,YAAY,EAAEC,cAAc,EAAE;MAClD,IAAIC,GAAG,GAAGF,YAAY,CAACE,GAAG,IAAItB,OAAO,CAAC,CAAC;MAEvC,IAAIuB,MAAM,GAAGxC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DE,GAAG,EAAEA;MACP,CAAC,CAAC;MAEF,IAAIE,QAAQ,GAAGlB,KAAK,CAACmB,KAAK,CAACD,QAAQ;MAEnClB,KAAK,CAACoB,QAAQ,CAAC,UAAUC,aAAa,EAAE;QACtC,IAAIX,OAAO,GAAGW,aAAa,CAACX,OAAO;QACnC,IAAIY,WAAW,GAAGZ,OAAO,CAACa,GAAG,CAAC,UAAUC,CAAC,EAAE;UACzC,OAAOA,CAAC,CAACP,MAAM,CAACD,GAAG;QACrB,CAAC,CAAC,CAACS,OAAO,CAACT,GAAG,CAAC;QACf,IAAIU,cAAc,GAAGhB,OAAO,CAACd,MAAM,CAAC,CAAC;QAErC,IAAI0B,WAAW,KAAK,CAAC,CAAC,EAAE;UACtBI,cAAc,CAACC,MAAM,CAACL,WAAW,EAAE,CAAC,EAAE;YACpCL,MAAM,EAAEA,MAAM;YACdF,cAAc,EAAEA;UAClB,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAIG,QAAQ,IAAIR,OAAO,CAACP,MAAM,IAAIe,QAAQ,EAAE;YAC1C;YACA;YACA;YACA;YACA;YACA;YACA;YACAD,MAAM,CAACD,GAAG,GAAGU,cAAc,CAAC,CAAC,CAAC,CAACT,MAAM,CAACD,GAAG;YACzCC,MAAM,CAACW,UAAU,GAAGlC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B;YACA;;YAEAuB,MAAM,CAACY,WAAW,GAAGb,GAAG;YACxBU,cAAc,CAACI,KAAK,CAAC,CAAC;UACxB;UAEAJ,cAAc,CAACK,IAAI,CAAC;YAClBd,MAAM,EAAEA,MAAM;YACdF,cAAc,EAAEA;UAClB,CAAC,CAAC;QACJ;QAEA,OAAO;UACLL,OAAO,EAAEgB;QACX,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAED1B,KAAK,CAACgC,MAAM,GAAG,UAAUC,SAAS,EAAE;MAClCjC,KAAK,CAACoB,QAAQ,CAAC,UAAUc,IAAI,EAAE;QAC7B,IAAIxB,OAAO,GAAGwB,IAAI,CAACxB,OAAO;QAC1B,OAAO;UACLA,OAAO,EAAEA,OAAO,CAACyB,MAAM,CAAC,UAAUC,KAAK,EAAE;YACvC,IAAIC,YAAY,GAAGD,KAAK,CAACnB,MAAM;cAC3BD,GAAG,GAAGqB,YAAY,CAACrB,GAAG;cACtBa,WAAW,GAAGQ,YAAY,CAACR,WAAW;YAC1C,IAAIS,SAAS,GAAGT,WAAW,IAAIb,GAAG;YAClC,OAAOsB,SAAS,KAAKL,SAAS;UAChC,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAEDjC,KAAK,CAACuC,cAAc,GAAG,CAAC,CAAC;IACzB,OAAOvC,KAAK;EACd;EAEArB,YAAY,CAACkB,YAAY,EAAE,CAAC;IAC1BmB,GAAG,EAAE,mBAAmB;IACxBwB,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,WAAW,GAAG,IAAI,CAACvB,KAAK;QACxBwB,SAAS,GAAGD,WAAW,CAACC,SAAS;QACjCC,SAAS,GAAGF,WAAW,CAACE,SAAS;MACrC,IAAIC,cAAc,GAAG,IAAI,CAAC1B,KAAK,CAAC0B,cAAc;MAE9C,IAAI,CAACA,cAAc,IAAID,SAAS,EAAE;QAChCC,cAAc,GAAG,EAAE,CAACjD,MAAM,CAAC+C,SAAS,EAAE,GAAG,CAAC,CAAC/C,MAAM,CAACgD,SAAS,CAAC;MAC9D;MAEA,OAAOC,cAAc;IACvB;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,QAAQ;IACbwB,KAAK,EAAE,SAASvD,MAAMA,CAAA,EAAG;MACvB,IAAI6D,MAAM,GAAG,IAAI;MAEjB,IAAIpC,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO;MAChC,IAAIqC,YAAY,GAAG,IAAI,CAAC5B,KAAK;QACzBwB,SAAS,GAAGI,YAAY,CAACJ,SAAS;QAClCK,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,KAAK,GAAGH,YAAY,CAACG,KAAK;MAC9B,IAAIC,UAAU,GAAG,EAAE;MACnBzC,OAAO,CAAC0C,OAAO,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;QACtC,IAAIrC,MAAM,GAAGoC,KAAK,CAACpC,MAAM;UACrBF,cAAc,GAAGsC,KAAK,CAACtC,cAAc;QACzC,IAAIa,UAAU,GAAG0B,KAAK,KAAK5C,OAAO,CAACP,MAAM,GAAG,CAAC,GAAGc,MAAM,CAACW,UAAU,GAAG2B,SAAS;QAC7E,IAAIvC,GAAG,GAAGC,MAAM,CAACD,GAAG;UAChBa,WAAW,GAAGZ,MAAM,CAACY,WAAW;QAEpC,IAAI2B,WAAW,GAAG/E,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UAC1DkE,SAAS,EAAEA,SAAS;UACpBM,SAAS,EAAEA;QACb,CAAC,EAAEhC,MAAM,CAAC,EAAEA,MAAM,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7BH,GAAG,EAAEA,GAAG;UACRyC,SAAS,EAAE5B,WAAW,IAAIb,GAAG;UAC7BY,UAAU,EAAEA,UAAU;UACtB8B,OAAO,EAAE,SAASA,OAAOA,CAACD,SAAS,EAAE;YACnC,IAAIE,eAAe;YAEnBb,MAAM,CAACd,MAAM,CAACyB,SAAS,CAAC;YAExB,CAACE,eAAe,GAAG1C,MAAM,CAACyC,OAAO,MAAM,IAAI,IAAIC,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACpD,IAAI,CAACU,MAAM,CAAC;UACnH,CAAC;UACD2C,OAAO,EAAE3C,MAAM,CAAC2C,OAAO;UACvBC,QAAQ,EAAE5C,MAAM,CAAC6C;QACnB,CAAC,CAAC,CAAC,CAAC;;QAGJX,UAAU,CAACpB,IAAI,CAACf,GAAG,CAAC;QACpB8B,MAAM,CAACP,cAAc,CAACvB,GAAG,CAAC,GAAG;UAC3BG,KAAK,EAAEqC,WAAW;UAClBzC,cAAc,EAAEA;QAClB,CAAC;MACH,CAAC,CAAC;MACF,OAAO,aAAahC,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QAC7Cf,SAAS,EAAE7D,UAAU,CAACwD,SAAS,EAAEK,SAAS,CAAC;QAC3CE,KAAK,EAAEA;MACT,CAAC,EAAE,aAAanE,KAAK,CAACgF,aAAa,CAAC3E,aAAa,EAAE;QACjD4E,IAAI,EAAEb,UAAU;QAChBc,UAAU,EAAE,IAAI,CAACxB,iBAAiB,CAAC,CAAC;QACpCyB,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,cAAc,EAAEC,KAAK,EAAE;UACjE,IAAIpD,GAAG,GAAGoD,KAAK,CAACpD,GAAG;UAEnB,IAAI,CAACmD,cAAc,EAAE;YACnB,OAAOrB,MAAM,CAACP,cAAc,CAACvB,GAAG,CAAC;UACnC;QACF;MACF,CAAC,EAAE,UAAUqD,KAAK,EAAE;QAClB,IAAIrD,GAAG,GAAGqD,KAAK,CAACrD,GAAG;UACfsD,eAAe,GAAGD,KAAK,CAACrB,SAAS;UACjCuB,WAAW,GAAGF,KAAK,CAACnB,KAAK;UACzBsB,OAAO,GAAGH,KAAK,CAACG,OAAO;QAC3B,IAAIC,qBAAqB,GAAG3B,MAAM,CAACP,cAAc,CAACvB,GAAG,CAAC;UAClDwC,WAAW,GAAGiB,qBAAqB,CAACtD,KAAK;UACzCJ,cAAc,GAAG0D,qBAAqB,CAAC1D,cAAc;QAEzD,IAAIA,cAAc,EAAE;UAClB,OAAO,aAAahC,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;YAC7C/C,GAAG,EAAEA,GAAG;YACRgC,SAAS,EAAE7D,UAAU,CAACmF,eAAe,EAAE,EAAE,CAAC1E,MAAM,CAAC+C,SAAS,EAAE,cAAc,CAAC,CAAC;YAC5EO,KAAK,EAAEzE,aAAa,CAAC,CAAC,CAAC,EAAE8F,WAAW,CAAC;YACrCG,GAAG,EAAE,SAASA,GAAGA,CAACC,GAAG,EAAE;cACrB,IAAI,OAAO3D,GAAG,KAAK,WAAW,EAAE;gBAC9B;cACF;cAEA,IAAI2D,GAAG,EAAE;gBACP7B,MAAM,CAACnC,QAAQ,CAACiE,GAAG,CAAC5D,GAAG,EAAE2D,GAAG,CAAC;gBAE7B5D,cAAc,CAAC4D,GAAG,EAAEnB,WAAW,CAAC;cAClC,CAAC,MAAM;gBACLV,MAAM,CAACnC,QAAQ,CAACkE,MAAM,CAAC7D,GAAG,CAAC;cAC7B;YACF;UACF,CAAC,CAAC;QACJ;QAEA,OAAO,aAAajC,KAAK,CAACgF,aAAa,CAAC1E,MAAM,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEgF,WAAW,EAAE;UACxER,SAAS,EAAE7D,UAAU,CAACmF,eAAe,EAAEd,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACR,SAAS,CAAC;UACvHE,KAAK,EAAEzE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8F,WAAW,CAAC,EAAEf,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACN,KAAK,CAAC;UACjIsB,OAAO,EAAEA;QACX,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAO3E,YAAY;AACrB,CAAC,CAACb,SAAS,CAAC;AAEZa,YAAY,CAACiF,WAAW,GAAG,KAAK,CAAC;AACjCjF,YAAY,CAACkF,YAAY,GAAG;EAC1BpC,SAAS,EAAE,iBAAiB;EAC5BC,SAAS,EAAE,MAAM;EACjBM,KAAK,EAAE;IACL8B,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE;EACR;AACF,CAAC;AAEDpF,YAAY,CAACiF,WAAW,GAAG,SAASI,uBAAuBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAChF,IAAIC,KAAK,GAAGF,UAAU,IAAI,CAAC,CAAC;IACxBG,YAAY,GAAGD,KAAK,CAACC,YAAY;IACjCnE,KAAK,GAAG5C,wBAAwB,CAAC8G,KAAK,EAAEvG,SAAS,CAAC;EAEtD,IAAI6F,GAAG,GAAGY,QAAQ,CAACxB,aAAa,CAAC,KAAK,CAAC;EAEvC,IAAIuB,YAAY,EAAE;IAChB,IAAIE,IAAI,GAAGF,YAAY,CAAC,CAAC;IACzBE,IAAI,CAACC,WAAW,CAACd,GAAG,CAAC;EACvB,CAAC,MAAM;IACLY,QAAQ,CAACG,IAAI,CAACD,WAAW,CAACd,GAAG,CAAC;EAChC;EAEA,IAAIgB,MAAM,GAAG,KAAK;EAElB,SAASjB,GAAGA,CAACkB,YAAY,EAAE;IACzB,IAAID,MAAM,EAAE;MACV;IACF;IAEAA,MAAM,GAAG,IAAI;IACbP,QAAQ,CAAC;MACPnE,MAAM,EAAE,SAASA,MAAMA,CAACuC,WAAW,EAAE;QACnCoC,YAAY,CAAC/E,GAAG,CAAC2C,WAAW,CAAC;MAC/B,CAAC;MACDqC,YAAY,EAAE,SAASA,YAAYA,CAAC7E,GAAG,EAAE;QACvC4E,YAAY,CAAC5D,MAAM,CAAChB,GAAG,CAAC;MAC1B,CAAC;MACD8E,SAAS,EAAEF,YAAY;MACvBG,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B7G,OAAO,CAACyF,GAAG,CAAC;QAEZ,IAAIA,GAAG,CAACqB,UAAU,EAAE;UAClBrB,GAAG,CAACqB,UAAU,CAACC,WAAW,CAACtB,GAAG,CAAC;QACjC;MACF,CAAC;MACD;MACAuB,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;QAC1C,OAAO5G,gBAAgB,CAACsG,YAAY,CAAC;MACvC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIlB,UAAU,CAACmB,WAAW,EAAE;IAC7DnB,UAAU,CAACmB,WAAW,CAAE,aAAavH,KAAK,CAACgF,aAAa,CAAClE,YAAY,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;MACzFuD,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC,CAAC;IACJ;EACF;EAEAzF,MAAM,CAAE,aAAaF,KAAK,CAACgF,aAAa,CAAClE,YAAY,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;IACzEuD,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC;AACX,CAAC;AAED,eAAe9E,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}