{"version": 3, "file": "write-package.js", "sourceRoot": "", "sources": ["../../src/write-package.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,IAAI,CAAA;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAA;AACvC,OAAO,GAAG,MAAM,cAAc,CAAA;AAE9B,eAAe,GAAG,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA", "sourcesContent": ["import { writeFileSync } from 'fs'\nimport { stringify } from 'polite-json'\nimport pkg from './package.js'\n\nexport default () => writeFileSync('package.json', stringify(pkg))\n"]}