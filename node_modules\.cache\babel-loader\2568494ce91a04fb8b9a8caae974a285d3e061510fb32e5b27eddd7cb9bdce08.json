{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Trigger from 'rc-trigger';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { MenuContext } from '../context/MenuContext';\nimport { placements, placementsRtl } from '../placements';\nimport { getMotion } from '../utils/motionUtil';\nvar popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default function PopupTrigger(_ref) {\n  var prefixCls = _ref.prefixCls,\n    visible = _ref.visible,\n    children = _ref.children,\n    popup = _ref.popup,\n    popupClassName = _ref.popupClassName,\n    popupOffset = _ref.popupOffset,\n    disabled = _ref.disabled,\n    mode = _ref.mode,\n    onVisibleChange = _ref.onVisibleChange;\n  var _React$useContext = React.useContext(MenuContext),\n    getPopupContainer = _React$useContext.getPopupContainer,\n    rtl = _React$useContext.rtl,\n    subMenuOpenDelay = _React$useContext.subMenuOpenDelay,\n    subMenuCloseDelay = _React$useContext.subMenuCloseDelay,\n    builtinPlacements = _React$useContext.builtinPlacements,\n    triggerSubMenuAction = _React$useContext.triggerSubMenuAction,\n    forceSubMenuRender = _React$useContext.forceSubMenuRender,\n    rootClassName = _React$useContext.rootClassName,\n    motion = _React$useContext.motion,\n    defaultMotions = _React$useContext.defaultMotions;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerVisible = _React$useState2[0],\n    setInnerVisible = _React$useState2[1];\n  var placement = rtl ? _objectSpread(_objectSpread({}, placementsRtl), builtinPlacements) : _objectSpread(_objectSpread({}, placements), builtinPlacements);\n  var popupPlacement = popupPlacementMap[mode];\n  var targetMotion = getMotion(mode, motion, defaultMotions);\n  var mergedMotion = _objectSpread(_objectSpread({}, targetMotion), {}, {\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\"),\n    removeOnLeave: false,\n    motionAppear: true\n  }); // Delay to change visible\n\n  var visibleRef = React.useRef();\n  React.useEffect(function () {\n    visibleRef.current = raf(function () {\n      setInnerVisible(visible);\n    });\n    return function () {\n      raf.cancel(visibleRef.current);\n    };\n  }, [visible]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: prefixCls,\n    popupClassName: classNames(\"\".concat(prefixCls, \"-popup\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), rtl), popupClassName, rootClassName),\n    stretch: mode === 'horizontal' ? 'minWidth' : null,\n    getPopupContainer: getPopupContainer,\n    builtinPlacements: placement,\n    popupPlacement: popupPlacement,\n    popupVisible: innerVisible,\n    popup: popup,\n    popupAlign: popupOffset && {\n      offset: popupOffset\n    },\n    action: disabled ? [] : [triggerSubMenuAction],\n    mouseEnterDelay: subMenuOpenDelay,\n    mouseLeaveDelay: subMenuCloseDelay,\n    onPopupVisibleChange: onVisibleChange,\n    forceRender: forceSubMenuRender,\n    popupMotion: mergedMotion\n  }, children);\n}", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "React", "<PERSON><PERSON>", "classNames", "raf", "MenuContext", "placements", "placementsRtl", "getMotion", "popupPlacementMap", "horizontal", "vertical", "PopupTrigger", "_ref", "prefixCls", "visible", "children", "popup", "popupClassName", "popupOffset", "disabled", "mode", "onVisibleChange", "_React$useContext", "useContext", "getPopupContainer", "rtl", "subMenuOpenDelay", "subMenuCloseDelay", "builtinPlacements", "triggerSubMenuAction", "forceSubMenuRender", "rootClassName", "motion", "defaultMotions", "_React$useState", "useState", "_React$useState2", "innerVisible", "setInnerVisible", "placement", "popupPlacement", "targetMotion", "mergedMotion", "leavedClassName", "concat", "removeOnLeave", "motionAppear", "visibleRef", "useRef", "useEffect", "current", "cancel", "createElement", "stretch", "popupVisible", "popupAlign", "offset", "action", "mouseEnterDelay", "mouseLeaveDelay", "onPopupVisibleChange", "forceRender", "popupMotion"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-menu/es/SubMenu/PopupTrigger.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Trigger from 'rc-trigger';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { MenuContext } from '../context/MenuContext';\nimport { placements, placementsRtl } from '../placements';\nimport { getMotion } from '../utils/motionUtil';\nvar popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default function PopupTrigger(_ref) {\n  var prefixCls = _ref.prefixCls,\n      visible = _ref.visible,\n      children = _ref.children,\n      popup = _ref.popup,\n      popupClassName = _ref.popupClassName,\n      popupOffset = _ref.popupOffset,\n      disabled = _ref.disabled,\n      mode = _ref.mode,\n      onVisibleChange = _ref.onVisibleChange;\n\n  var _React$useContext = React.useContext(MenuContext),\n      getPopupContainer = _React$useContext.getPopupContainer,\n      rtl = _React$useContext.rtl,\n      subMenuOpenDelay = _React$useContext.subMenuOpenDelay,\n      subMenuCloseDelay = _React$useContext.subMenuCloseDelay,\n      builtinPlacements = _React$useContext.builtinPlacements,\n      triggerSubMenuAction = _React$useContext.triggerSubMenuAction,\n      forceSubMenuRender = _React$useContext.forceSubMenuRender,\n      rootClassName = _React$useContext.rootClassName,\n      motion = _React$useContext.motion,\n      defaultMotions = _React$useContext.defaultMotions;\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      innerVisible = _React$useState2[0],\n      setInnerVisible = _React$useState2[1];\n\n  var placement = rtl ? _objectSpread(_objectSpread({}, placementsRtl), builtinPlacements) : _objectSpread(_objectSpread({}, placements), builtinPlacements);\n  var popupPlacement = popupPlacementMap[mode];\n  var targetMotion = getMotion(mode, motion, defaultMotions);\n\n  var mergedMotion = _objectSpread(_objectSpread({}, targetMotion), {}, {\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\"),\n    removeOnLeave: false,\n    motionAppear: true\n  }); // Delay to change visible\n\n\n  var visibleRef = React.useRef();\n  React.useEffect(function () {\n    visibleRef.current = raf(function () {\n      setInnerVisible(visible);\n    });\n    return function () {\n      raf.cancel(visibleRef.current);\n    };\n  }, [visible]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: prefixCls,\n    popupClassName: classNames(\"\".concat(prefixCls, \"-popup\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), rtl), popupClassName, rootClassName),\n    stretch: mode === 'horizontal' ? 'minWidth' : null,\n    getPopupContainer: getPopupContainer,\n    builtinPlacements: placement,\n    popupPlacement: popupPlacement,\n    popupVisible: innerVisible,\n    popup: popup,\n    popupAlign: popupOffset && {\n      offset: popupOffset\n    },\n    action: disabled ? [] : [triggerSubMenuAction],\n    mouseEnterDelay: subMenuOpenDelay,\n    mouseLeaveDelay: subMenuCloseDelay,\n    onPopupVisibleChange: onVisibleChange,\n    forceRender: forceSubMenuRender,\n    popupMotion: mergedMotion\n  }, children);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,UAAU,EAAEC,aAAa,QAAQ,eAAe;AACzD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,iBAAiB,GAAG;EACtBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpB,eAAe,EAAE,UAAU;EAC3B,gBAAgB,EAAE;AACpB,CAAC;AACD,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAE;EACzC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,cAAc,GAAGL,IAAI,CAACK,cAAc;IACpCC,WAAW,GAAGN,IAAI,CAACM,WAAW;IAC9BC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,IAAI,GAAGR,IAAI,CAACQ,IAAI;IAChBC,eAAe,GAAGT,IAAI,CAACS,eAAe;EAE1C,IAAIC,iBAAiB,GAAGtB,KAAK,CAACuB,UAAU,CAACnB,WAAW,CAAC;IACjDoB,iBAAiB,GAAGF,iBAAiB,CAACE,iBAAiB;IACvDC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,gBAAgB,GAAGJ,iBAAiB,CAACI,gBAAgB;IACrDC,iBAAiB,GAAGL,iBAAiB,CAACK,iBAAiB;IACvDC,iBAAiB,GAAGN,iBAAiB,CAACM,iBAAiB;IACvDC,oBAAoB,GAAGP,iBAAiB,CAACO,oBAAoB;IAC7DC,kBAAkB,GAAGR,iBAAiB,CAACQ,kBAAkB;IACzDC,aAAa,GAAGT,iBAAiB,CAACS,aAAa;IAC/CC,MAAM,GAAGV,iBAAiB,CAACU,MAAM;IACjCC,cAAc,GAAGX,iBAAiB,CAACW,cAAc;EAErD,IAAIC,eAAe,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAGrC,cAAc,CAACmC,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,IAAIG,SAAS,GAAGd,GAAG,GAAG3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEQ,aAAa,CAAC,EAAEsB,iBAAiB,CAAC,GAAG9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEO,UAAU,CAAC,EAAEuB,iBAAiB,CAAC;EAC1J,IAAIY,cAAc,GAAGhC,iBAAiB,CAACY,IAAI,CAAC;EAC5C,IAAIqB,YAAY,GAAGlC,SAAS,CAACa,IAAI,EAAEY,MAAM,EAAEC,cAAc,CAAC;EAE1D,IAAIS,YAAY,GAAG5C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;IACpEE,eAAe,EAAE,EAAE,CAACC,MAAM,CAAC/B,SAAS,EAAE,SAAS,CAAC;IAChDgC,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC;;EAGJ,IAAIC,UAAU,GAAG/C,KAAK,CAACgD,MAAM,CAAC,CAAC;EAC/BhD,KAAK,CAACiD,SAAS,CAAC,YAAY;IAC1BF,UAAU,CAACG,OAAO,GAAG/C,GAAG,CAAC,YAAY;MACnCmC,eAAe,CAACxB,OAAO,CAAC;IAC1B,CAAC,CAAC;IACF,OAAO,YAAY;MACjBX,GAAG,CAACgD,MAAM,CAACJ,UAAU,CAACG,OAAO,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAACpC,OAAO,CAAC,CAAC;EACb,OAAO,aAAad,KAAK,CAACoD,aAAa,CAACnD,OAAO,EAAE;IAC/CY,SAAS,EAAEA,SAAS;IACpBI,cAAc,EAAEf,UAAU,CAAC,EAAE,CAAC0C,MAAM,CAAC/B,SAAS,EAAE,QAAQ,CAAC,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+C,MAAM,CAAC/B,SAAS,EAAE,MAAM,CAAC,EAAEY,GAAG,CAAC,EAAER,cAAc,EAAEc,aAAa,CAAC;IACjJsB,OAAO,EAAEjC,IAAI,KAAK,YAAY,GAAG,UAAU,GAAG,IAAI;IAClDI,iBAAiB,EAAEA,iBAAiB;IACpCI,iBAAiB,EAAEW,SAAS;IAC5BC,cAAc,EAAEA,cAAc;IAC9Bc,YAAY,EAAEjB,YAAY;IAC1BrB,KAAK,EAAEA,KAAK;IACZuC,UAAU,EAAErC,WAAW,IAAI;MACzBsC,MAAM,EAAEtC;IACV,CAAC;IACDuC,MAAM,EAAEtC,QAAQ,GAAG,EAAE,GAAG,CAACU,oBAAoB,CAAC;IAC9C6B,eAAe,EAAEhC,gBAAgB;IACjCiC,eAAe,EAAEhC,iBAAiB;IAClCiC,oBAAoB,EAAEvC,eAAe;IACrCwC,WAAW,EAAE/B,kBAAkB;IAC/BgC,WAAW,EAAEpB;EACf,CAAC,EAAE3B,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}