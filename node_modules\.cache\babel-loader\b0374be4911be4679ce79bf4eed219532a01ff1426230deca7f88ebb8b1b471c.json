{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n    onColumnResize = _ref.onColumnResize;\n  var cellRef = React.useRef();\n  React.useEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      padding: 0,\n      border: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, \"\\xA0\")));\n}", "map": {"version": 3, "names": ["React", "ResizeObserver", "MeasureCell", "_ref", "column<PERSON>ey", "onColumnResize", "cellRef", "useRef", "useEffect", "current", "offsetWidth", "createElement", "data", "ref", "style", "padding", "border", "height", "overflow"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Body/MeasureCell.js"], "sourcesContent": ["import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n      onColumnResize = _ref.onColumnResize;\n  var cellRef = React.useRef();\n  React.useEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      padding: 0,\n      border: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, \"\\xA0\")));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,cAAc,GAAGF,IAAI,CAACE,cAAc;EACxC,IAAIC,OAAO,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EAC5BP,KAAK,CAACQ,SAAS,CAAC,YAAY;IAC1B,IAAIF,OAAO,CAACG,OAAO,EAAE;MACnBJ,cAAc,CAACD,SAAS,EAAEE,OAAO,CAACG,OAAO,CAACC,WAAW,CAAC;IACxD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaV,KAAK,CAACW,aAAa,CAACV,cAAc,EAAE;IACtDW,IAAI,EAAER;EACR,CAAC,EAAE,aAAaJ,KAAK,CAACW,aAAa,CAAC,IAAI,EAAE;IACxCE,GAAG,EAAEP,OAAO;IACZQ,KAAK,EAAE;MACLC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV;EACF,CAAC,EAAE,aAAajB,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACzCG,KAAK,EAAE;MACLG,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}