{"ast": null, "code": "export function warn() {\n  if (console && console.warn) {\n    var _console;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (typeof args[0] === 'string') args[0] = \"react-i18next:: \".concat(args[0]);\n    (_console = console).warn.apply(_console, args);\n  }\n}\nvar alreadyWarned = {};\nexport function warnOnce() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  if (typeof args[0] === 'string' && alreadyWarned[args[0]]) return;\n  if (typeof args[0] === 'string') alreadyWarned[args[0]] = new Date();\n  warn.apply(void 0, args);\n}\nexport function loadNamespaces(i18n, ns, cb) {\n  i18n.loadNamespaces(ns, function () {\n    if (i18n.isInitialized) {\n      cb();\n    } else {\n      var initialized = function initialized() {\n        setTimeout(function () {\n          i18n.off('initialized', initialized);\n        }, 0);\n        cb();\n      };\n      i18n.on('initialized', initialized);\n    }\n  });\n}\nfunction oldI18nextHasLoadedNamespace(ns, i18n) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var lng = i18n.languages[0];\n  var fallbackLng = i18n.options ? i18n.options.fallbackLng : false;\n  var lastLng = i18n.languages[i18n.languages.length - 1];\n  if (lng.toLowerCase() === 'cimode') return true;\n  var loadNotPending = function loadNotPending(l, n) {\n    var loadState = i18n.services.backendConnector.state[\"\".concat(l, \"|\").concat(n)];\n    return loadState === -1 || loadState === 2;\n  };\n  if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18n.services.backendConnector.backend && i18n.isLanguageChangingTo && !loadNotPending(i18n.isLanguageChangingTo, ns)) return false;\n  if (i18n.hasResourceBundle(lng, ns)) return true;\n  if (!i18n.services.backendConnector.backend || i18n.options.resources && !i18n.options.partialBundledLanguages) return true;\n  if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n  return false;\n}\nexport function hasLoadedNamespace(ns, i18n) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce('i18n.languages were undefined or empty', i18n.languages);\n    return true;\n  }\n  var isNewerI18next = i18n.options.ignoreJSONStructure !== undefined;\n  if (!isNewerI18next) {\n    return oldI18nextHasLoadedNamespace(ns, i18n, options);\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    precheck: function precheck(i18nInstance, loadNotPending) {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n}\nexport function getDisplayName(Component) {\n  return Component.displayName || Component.name || (typeof Component === 'string' && Component.length > 0 ? Component : 'Unknown');\n}", "map": {"version": 3, "names": ["warn", "console", "_console", "_len", "arguments", "length", "args", "Array", "_key", "concat", "apply", "alreadyWarned", "warnOnce", "_len2", "_key2", "Date", "loadNamespaces", "i18n", "ns", "cb", "isInitialized", "initialized", "setTimeout", "off", "on", "oldI18nextHasLoadedNamespace", "options", "undefined", "lng", "languages", "fallbackLng", "lastLng", "toLowerCase", "loadNotPending", "l", "n", "loadState", "services", "backendConnector", "state", "bindI18n", "indexOf", "backend", "isLanguageChangingTo", "hasResourceBundle", "resources", "partialBundledLanguages", "hasLoadedNamespace", "isNewerI18next", "ignoreJSONStructure", "precheck", "i18nInstance", "getDisplayName", "Component", "displayName", "name"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/utils.js"], "sourcesContent": ["export function warn() {\n  if (console && console.warn) {\n    var _console;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    if (typeof args[0] === 'string') args[0] = \"react-i18next:: \".concat(args[0]);\n\n    (_console = console).warn.apply(_console, args);\n  }\n}\nvar alreadyWarned = {};\nexport function warnOnce() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n\n  if (typeof args[0] === 'string' && alreadyWarned[args[0]]) return;\n  if (typeof args[0] === 'string') alreadyWarned[args[0]] = new Date();\n  warn.apply(void 0, args);\n}\nexport function loadNamespaces(i18n, ns, cb) {\n  i18n.loadNamespaces(ns, function () {\n    if (i18n.isInitialized) {\n      cb();\n    } else {\n      var initialized = function initialized() {\n        setTimeout(function () {\n          i18n.off('initialized', initialized);\n        }, 0);\n        cb();\n      };\n\n      i18n.on('initialized', initialized);\n    }\n  });\n}\n\nfunction oldI18nextHasLoadedNamespace(ns, i18n) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var lng = i18n.languages[0];\n  var fallbackLng = i18n.options ? i18n.options.fallbackLng : false;\n  var lastLng = i18n.languages[i18n.languages.length - 1];\n  if (lng.toLowerCase() === 'cimode') return true;\n\n  var loadNotPending = function loadNotPending(l, n) {\n    var loadState = i18n.services.backendConnector.state[\"\".concat(l, \"|\").concat(n)];\n    return loadState === -1 || loadState === 2;\n  };\n\n  if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18n.services.backendConnector.backend && i18n.isLanguageChangingTo && !loadNotPending(i18n.isLanguageChangingTo, ns)) return false;\n  if (i18n.hasResourceBundle(lng, ns)) return true;\n  if (!i18n.services.backendConnector.backend || i18n.options.resources && !i18n.options.partialBundledLanguages) return true;\n  if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n  return false;\n}\n\nexport function hasLoadedNamespace(ns, i18n) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce('i18n.languages were undefined or empty', i18n.languages);\n    return true;\n  }\n\n  var isNewerI18next = i18n.options.ignoreJSONStructure !== undefined;\n\n  if (!isNewerI18next) {\n    return oldI18nextHasLoadedNamespace(ns, i18n, options);\n  }\n\n  return i18n.hasLoadedNamespace(ns, {\n    precheck: function precheck(i18nInstance, loadNotPending) {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n}\nexport function getDisplayName(Component) {\n  return Component.displayName || Component.name || (typeof Component === 'string' && Component.length > 0 ? Component : 'Unknown');\n}"], "mappings": "AAAA,OAAO,SAASA,IAAIA,CAAA,EAAG;EACrB,IAAIC,OAAO,IAAIA,OAAO,CAACD,IAAI,EAAE;IAC3B,IAAIE,QAAQ;IAEZ,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEA,IAAI,OAAOF,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;IAE7E,CAACJ,QAAQ,GAAGD,OAAO,EAAED,IAAI,CAACU,KAAK,CAACR,QAAQ,EAAEI,IAAI,CAAC;EACjD;AACF;AACA,IAAIK,aAAa,GAAG,CAAC,CAAC;AACtB,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,KAAK,IAAIC,KAAK,GAAGT,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC7FR,IAAI,CAACQ,KAAK,CAAC,GAAGV,SAAS,CAACU,KAAK,CAAC;EAChC;EAEA,IAAI,OAAOR,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIK,aAAa,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3D,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEK,aAAa,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIS,IAAI,CAAC,CAAC;EACpEf,IAAI,CAACU,KAAK,CAAC,KAAK,CAAC,EAAEJ,IAAI,CAAC;AAC1B;AACA,OAAO,SAASU,cAAcA,CAACC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC3CF,IAAI,CAACD,cAAc,CAACE,EAAE,EAAE,YAAY;IAClC,IAAID,IAAI,CAACG,aAAa,EAAE;MACtBD,EAAE,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;QACvCC,UAAU,CAAC,YAAY;UACrBL,IAAI,CAACM,GAAG,CAAC,aAAa,EAAEF,WAAW,CAAC;QACtC,CAAC,EAAE,CAAC,CAAC;QACLF,EAAE,CAAC,CAAC;MACN,CAAC;MAEDF,IAAI,CAACO,EAAE,CAAC,aAAa,EAAEH,WAAW,CAAC;IACrC;EACF,CAAC,CAAC;AACJ;AAEA,SAASI,4BAA4BA,CAACP,EAAE,EAAED,IAAI,EAAE;EAC9C,IAAIS,OAAO,GAAGtB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuB,SAAS,GAAGvB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIwB,GAAG,GAAGX,IAAI,CAACY,SAAS,CAAC,CAAC,CAAC;EAC3B,IAAIC,WAAW,GAAGb,IAAI,CAACS,OAAO,GAAGT,IAAI,CAACS,OAAO,CAACI,WAAW,GAAG,KAAK;EACjE,IAAIC,OAAO,GAAGd,IAAI,CAACY,SAAS,CAACZ,IAAI,CAACY,SAAS,CAACxB,MAAM,GAAG,CAAC,CAAC;EACvD,IAAIuB,GAAG,CAACI,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAI;EAE/C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjD,IAAIC,SAAS,GAAGnB,IAAI,CAACoB,QAAQ,CAACC,gBAAgB,CAACC,KAAK,CAAC,EAAE,CAAC9B,MAAM,CAACyB,CAAC,EAAE,GAAG,CAAC,CAACzB,MAAM,CAAC0B,CAAC,CAAC,CAAC;IACjF,OAAOC,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAK,CAAC;EAC5C,CAAC;EAED,IAAIV,OAAO,CAACc,QAAQ,IAAId,OAAO,CAACc,QAAQ,CAACC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAIxB,IAAI,CAACoB,QAAQ,CAACC,gBAAgB,CAACI,OAAO,IAAIzB,IAAI,CAAC0B,oBAAoB,IAAI,CAACV,cAAc,CAAChB,IAAI,CAAC0B,oBAAoB,EAAEzB,EAAE,CAAC,EAAE,OAAO,KAAK;EAChN,IAAID,IAAI,CAAC2B,iBAAiB,CAAChB,GAAG,EAAEV,EAAE,CAAC,EAAE,OAAO,IAAI;EAChD,IAAI,CAACD,IAAI,CAACoB,QAAQ,CAACC,gBAAgB,CAACI,OAAO,IAAIzB,IAAI,CAACS,OAAO,CAACmB,SAAS,IAAI,CAAC5B,IAAI,CAACS,OAAO,CAACoB,uBAAuB,EAAE,OAAO,IAAI;EAC3H,IAAIb,cAAc,CAACL,GAAG,EAAEV,EAAE,CAAC,KAAK,CAACY,WAAW,IAAIG,cAAc,CAACF,OAAO,EAAEb,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;EACzF,OAAO,KAAK;AACd;AAEA,OAAO,SAAS6B,kBAAkBA,CAAC7B,EAAE,EAAED,IAAI,EAAE;EAC3C,IAAIS,OAAO,GAAGtB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuB,SAAS,GAAGvB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpF,IAAI,CAACa,IAAI,CAACY,SAAS,IAAI,CAACZ,IAAI,CAACY,SAAS,CAACxB,MAAM,EAAE;IAC7CO,QAAQ,CAAC,wCAAwC,EAAEK,IAAI,CAACY,SAAS,CAAC;IAClE,OAAO,IAAI;EACb;EAEA,IAAImB,cAAc,GAAG/B,IAAI,CAACS,OAAO,CAACuB,mBAAmB,KAAKtB,SAAS;EAEnE,IAAI,CAACqB,cAAc,EAAE;IACnB,OAAOvB,4BAA4B,CAACP,EAAE,EAAED,IAAI,EAAES,OAAO,CAAC;EACxD;EAEA,OAAOT,IAAI,CAAC8B,kBAAkB,CAAC7B,EAAE,EAAE;IACjCgC,QAAQ,EAAE,SAASA,QAAQA,CAACC,YAAY,EAAElB,cAAc,EAAE;MACxD,IAAIP,OAAO,CAACc,QAAQ,IAAId,OAAO,CAACc,QAAQ,CAACC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAIU,YAAY,CAACd,QAAQ,CAACC,gBAAgB,CAACI,OAAO,IAAIS,YAAY,CAACR,oBAAoB,IAAI,CAACV,cAAc,CAACkB,YAAY,CAACR,oBAAoB,EAAEzB,EAAE,CAAC,EAAE,OAAO,KAAK;IAC1O;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASkC,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAOA,SAAS,CAACC,WAAW,IAAID,SAAS,CAACE,IAAI,KAAK,OAAOF,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAAChD,MAAM,GAAG,CAAC,GAAGgD,SAAS,GAAG,SAAS,CAAC;AACnI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}