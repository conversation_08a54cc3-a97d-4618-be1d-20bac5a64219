{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nfunction cloneDeep(val) {\n  if (Array.isArray(val)) {\n    return cloneArrayDeep(val);\n  } else if (_typeof(val) === 'object' && val !== null) {\n    return cloneObjectDeep(val);\n  }\n  return val;\n}\nfunction cloneObjectDeep(val) {\n  if (Object.getPrototypeOf(val) === Object.prototype) {\n    var res = {};\n    for (var key in val) {\n      res[key] = cloneDeep(val[key]);\n    }\n    return res;\n  }\n  return val;\n}\nfunction cloneArrayDeep(val) {\n  return val.map(function (item) {\n    return cloneDeep(item);\n  });\n}\nexport default cloneDeep;", "map": {"version": 3, "names": ["_typeof", "cloneDeep", "val", "Array", "isArray", "cloneArrayDeep", "cloneObjectDeep", "Object", "getPrototypeOf", "prototype", "res", "key", "map", "item"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/utils/cloneDeep.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\nfunction cloneDeep(val) {\n  if (Array.isArray(val)) {\n    return cloneArrayDeep(val);\n  } else if (_typeof(val) === 'object' && val !== null) {\n    return cloneObjectDeep(val);\n  }\n\n  return val;\n}\n\nfunction cloneObjectDeep(val) {\n  if (Object.getPrototypeOf(val) === Object.prototype) {\n    var res = {};\n\n    for (var key in val) {\n      res[key] = cloneDeep(val[key]);\n    }\n\n    return res;\n  }\n\n  return val;\n}\n\nfunction cloneArrayDeep(val) {\n  return val.map(function (item) {\n    return cloneDeep(item);\n  });\n}\n\nexport default cloneDeep;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AAEvD,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IACtB,OAAOG,cAAc,CAACH,GAAG,CAAC;EAC5B,CAAC,MAAM,IAAIF,OAAO,CAACE,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IACpD,OAAOI,eAAe,CAACJ,GAAG,CAAC;EAC7B;EAEA,OAAOA,GAAG;AACZ;AAEA,SAASI,eAAeA,CAACJ,GAAG,EAAE;EAC5B,IAAIK,MAAM,CAACC,cAAc,CAACN,GAAG,CAAC,KAAKK,MAAM,CAACE,SAAS,EAAE;IACnD,IAAIC,GAAG,GAAG,CAAC,CAAC;IAEZ,KAAK,IAAIC,GAAG,IAAIT,GAAG,EAAE;MACnBQ,GAAG,CAACC,GAAG,CAAC,GAAGV,SAAS,CAACC,GAAG,CAACS,GAAG,CAAC,CAAC;IAChC;IAEA,OAAOD,GAAG;EACZ;EAEA,OAAOR,GAAG;AACZ;AAEA,SAASG,cAAcA,CAACH,GAAG,EAAE;EAC3B,OAAOA,GAAG,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC7B,OAAOZ,SAAS,CAACY,IAAI,CAAC;EACxB,CAAC,CAAC;AACJ;AAEA,eAAeZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}