"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = processTailwindFeatures;
var _normalizeTailwindDirectives = _interopRequireDefault(require("./lib/normalizeTailwindDirectives"));
var _expandTailwindAtRules = _interopRequireDefault(require("./lib/expandTailwindAtRules"));
var _expandApplyAtRules = _interopRequireDefault(require("./lib/expandApplyAtRules"));
var _evaluateTailwindFunctions = _interopRequireDefault(require("./lib/evaluateTailwindFunctions"));
var _substituteScreenAtRules = _interopRequireDefault(require("./lib/substituteScreenAtRules"));
var _resolveDefaultsAtRules = _interopRequireDefault(require("./lib/resolveDefaultsAtRules"));
var _collapseAdjacentRules = _interopRequireDefault(require("./lib/collapseAdjacentRules"));
var _collapseDuplicateDeclarations = _interopRequireDefault(require("./lib/collapseDuplicateDeclarations"));
var _partitionApplyAtRules = _interopRequireDefault(require("./lib/partitionApplyAtRules"));
var _detectNesting = _interopRequireDefault(require("./lib/detectNesting"));
var _setupContextUtils = require("./lib/setupContextUtils");
var _featureFlags = require("./featureFlags");
function processTailwindFeatures(setupContext) {
    return function(root, result) {
        let { tailwindDirectives , applyDirectives  } = (0, _normalizeTailwindDirectives).default(root);
        (0, _detectNesting).default()(root, result);
        // Partition apply rules that are found in the css
        // itself.
        (0, _partitionApplyAtRules).default()(root, result);
        let context = setupContext({
            tailwindDirectives,
            applyDirectives,
            registerDependency (dependency) {
                result.messages.push({
                    plugin: "tailwindcss",
                    parent: result.opts.from,
                    ...dependency
                });
            },
            createContext (tailwindConfig, changedContent) {
                return (0, _setupContextUtils).createContext(tailwindConfig, changedContent, root);
            }
        })(root, result);
        if (context.tailwindConfig.separator === "-") {
            throw new Error("The '-' character cannot be used as a custom separator in JIT mode due to parsing ambiguity. Please use another character like '_' instead.");
        }
        (0, _featureFlags).issueFlagNotices(context.tailwindConfig);
        (0, _expandTailwindAtRules).default(context)(root, result);
        // Partition apply rules that are generated by
        // addComponents, addUtilities and so on.
        (0, _partitionApplyAtRules).default()(root, result);
        (0, _expandApplyAtRules).default(context)(root, result);
        (0, _evaluateTailwindFunctions).default(context)(root, result);
        (0, _substituteScreenAtRules).default(context)(root, result);
        (0, _resolveDefaultsAtRules).default(context)(root, result);
        (0, _collapseAdjacentRules).default(context)(root, result);
        (0, _collapseDuplicateDeclarations).default(context)(root, result);
    };
}
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
