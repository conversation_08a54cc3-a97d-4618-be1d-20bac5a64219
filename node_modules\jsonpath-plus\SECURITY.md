# Security Policy

## Reporting a Vulnerability

**Please do not report security vulnerabilities through public GitHub issues.**

If you believe you’ve found a security vulnerability, please send it to us by emailing [<EMAIL>](mailto:<EMAIL>). Please include the following details with your report:

1. Description of the location and potential impact of the vulnerability

2. A detailed description of the steps required to reproduce the vulnerability (POC scripts, etc.).

3. How you would like to be credited.

We will evaluate the vulnerability and, if necessary, release a fix or unertake mitigating steps to address it. We will contact you to let you know the outcome, and will credit you in the report.

Please **do not disclose the vulnerability publicly** until we have sufficient time to release a fix.

Once we have either a) published a fix, b) declined to address the vulnerability for whatever reason, or c) taken more than 30 days to reply, we welcome you to publicly report the vulnerability on our tracker and disclose it publicly. If you intend to
disclose sooner regardless of our requested policy, please at least indicate to us when you plan to disclose.
