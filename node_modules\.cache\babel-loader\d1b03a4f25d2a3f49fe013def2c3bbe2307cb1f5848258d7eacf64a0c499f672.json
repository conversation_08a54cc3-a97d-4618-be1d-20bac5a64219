{"ast": null, "code": "var _excluded = [\"prefixCls\", \"className\", \"height\", \"itemHeight\", \"fullHeight\", \"style\", \"data\", \"children\", \"itemKey\", \"virtual\", \"component\", \"onScroll\", \"onVisibleChange\"];\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport Filler from './Filler';\nimport ScrollBar from './ScrollBar';\nimport useChildren from './hooks/useChildren';\nimport useHeights from './hooks/useHeights';\nimport useScrollTo from './hooks/useScrollTo';\nimport useDiffItem from './hooks/useDiffItem';\nimport useFrameWheel from './hooks/useFrameWheel';\nimport useMobileTouchMove from './hooks/useMobileTouchMove';\nimport useOriginScroll from './hooks/useOriginScroll';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nvar EMPTY_DATA = [];\nvar ScrollStyle = {\n  overflowY: 'auto',\n  overflowAnchor: 'none'\n};\nexport function RawList(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-virtual-list' : _props$prefixCls,\n    className = props.className,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    _props$fullHeight = props.fullHeight,\n    fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight,\n    style = props.style,\n    data = props.data,\n    children = props.children,\n    itemKey = props.itemKey,\n    virtual = props.virtual,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    onScroll = props.onScroll,\n    onVisibleChange = props.onVisibleChange,\n    restProps = _objectWithoutProperties(props, _excluded); // ================================= MISC =================================\n\n  var useVirtual = !!(virtual !== false && height && itemHeight);\n  var inVirtual = useVirtual && data && itemHeight * data.length > height;\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    scrollTop = _useState2[0],\n    setScrollTop = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    scrollMoving = _useState4[0],\n    setScrollMoving = _useState4[1];\n  var mergedClassName = classNames(prefixCls, className);\n  var mergedData = data || EMPTY_DATA;\n  var componentRef = useRef();\n  var fillerInnerRef = useRef();\n  var scrollBarRef = useRef(); // Hack on scrollbar to enable flash call\n  // =============================== Item Key ===============================\n\n  var getKey = React.useCallback(function (item) {\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return item === null || item === void 0 ? void 0 : item[itemKey];\n  }, [itemKey]);\n  var sharedConfig = {\n    getKey: getKey\n  }; // ================================ Scroll ================================\n\n  function syncScrollTop(newTop) {\n    setScrollTop(function (origin) {\n      var value;\n      if (typeof newTop === 'function') {\n        value = newTop(origin);\n      } else {\n        value = newTop;\n      }\n      var alignedTop = keepInRange(value);\n      componentRef.current.scrollTop = alignedTop;\n      return alignedTop;\n    });\n  } // ================================ Legacy ================================\n  // Put ref here since the range is generate by follow\n\n  var rangeRef = useRef({\n    start: 0,\n    end: mergedData.length\n  });\n  var diffItemRef = useRef();\n  var _useDiffItem = useDiffItem(mergedData, getKey),\n    _useDiffItem2 = _slicedToArray(_useDiffItem, 1),\n    diffItem = _useDiffItem2[0];\n  diffItemRef.current = diffItem; // ================================ Height ================================\n\n  var _useHeights = useHeights(getKey, null, null),\n    _useHeights2 = _slicedToArray(_useHeights, 4),\n    setInstanceRef = _useHeights2[0],\n    collectHeight = _useHeights2[1],\n    heights = _useHeights2[2],\n    heightUpdatedMark = _useHeights2[3]; // ========================== Visible Calculation =========================\n\n  var _React$useMemo = React.useMemo(function () {\n      if (!useVirtual) {\n        return {\n          scrollHeight: undefined,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      } // Always use virtual scroll bar in avoid shaking\n\n      // Always use virtual scroll bar in avoid shaking\n      if (!inVirtual) {\n        var _fillerInnerRef$curre;\n        return {\n          scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n      var itemTop = 0;\n      var startIndex;\n      var startOffset;\n      var endIndex;\n      var dataLen = mergedData.length;\n      for (var i = 0; i < dataLen; i += 1) {\n        var item = mergedData[i];\n        var key = getKey(item);\n        var cacheHeight = heights.get(key);\n        var currentItemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight); // Check item top in the range\n\n        // Check item top in the range\n        if (currentItemBottom >= scrollTop && startIndex === undefined) {\n          startIndex = i;\n          startOffset = itemTop;\n        } // Check item bottom in the range. We will render additional one item for motion usage\n\n        // Check item bottom in the range. We will render additional one item for motion usage\n        if (currentItemBottom > scrollTop + height && endIndex === undefined) {\n          endIndex = i;\n        }\n        itemTop = currentItemBottom;\n      } // Fallback to normal if not match. This code should never reach\n\n      /* istanbul ignore next */\n\n      // Fallback to normal if not match. This code should never reach\n\n      /* istanbul ignore next */\n      if (startIndex === undefined) {\n        startIndex = 0;\n        startOffset = 0;\n      }\n      if (endIndex === undefined) {\n        endIndex = mergedData.length - 1;\n      } // Give cache to improve scroll experience\n\n      // Give cache to improve scroll experience\n      endIndex = Math.min(endIndex + 1, mergedData.length);\n      return {\n        scrollHeight: itemTop,\n        start: startIndex,\n        end: endIndex,\n        offset: startOffset\n      };\n    }, [inVirtual, useVirtual, scrollTop, mergedData, heightUpdatedMark, height]),\n    scrollHeight = _React$useMemo.scrollHeight,\n    start = _React$useMemo.start,\n    end = _React$useMemo.end,\n    offset = _React$useMemo.offset;\n  rangeRef.current.start = start;\n  rangeRef.current.end = end; // =============================== In Range ===============================\n\n  var maxScrollHeight = scrollHeight - height;\n  var maxScrollHeightRef = useRef(maxScrollHeight);\n  maxScrollHeightRef.current = maxScrollHeight;\n  function keepInRange(newScrollTop) {\n    var newTop = newScrollTop;\n    if (!Number.isNaN(maxScrollHeightRef.current)) {\n      newTop = Math.min(newTop, maxScrollHeightRef.current);\n    }\n    newTop = Math.max(newTop, 0);\n    return newTop;\n  }\n  var isScrollAtTop = scrollTop <= 0;\n  var isScrollAtBottom = scrollTop >= maxScrollHeight;\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom); // ================================ Scroll ================================\n\n  function onScrollBar(newScrollTop) {\n    var newTop = newScrollTop;\n    syncScrollTop(newTop);\n  } // When data size reduce. It may trigger native scroll event back to fit scroll position\n\n  function onFallbackScroll(e) {\n    var newScrollTop = e.currentTarget.scrollTop;\n    if (newScrollTop !== scrollTop) {\n      syncScrollTop(newScrollTop);\n    } // Trigger origin onScroll\n\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll(e);\n  } // Since this added in global,should use ref to keep update\n\n  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, function (offsetY) {\n      syncScrollTop(function (top) {\n        var newTop = top + offsetY;\n        return newTop;\n      });\n    }),\n    _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2),\n    onRawWheel = _useFrameWheel2[0],\n    onFireFoxScroll = _useFrameWheel2[1]; // Mobile touch move\n\n  useMobileTouchMove(useVirtual, componentRef, function (deltaY, smoothOffset) {\n    if (originScroll(deltaY, smoothOffset)) {\n      return false;\n    }\n    onRawWheel({\n      preventDefault: function preventDefault() {},\n      deltaY: deltaY\n    });\n    return true;\n  });\n  useLayoutEffect(function () {\n    // Firefox only\n    function onMozMousePixelScroll(e) {\n      if (useVirtual) {\n        e.preventDefault();\n      }\n    }\n    componentRef.current.addEventListener('wheel', onRawWheel);\n    componentRef.current.addEventListener('DOMMouseScroll', onFireFoxScroll);\n    componentRef.current.addEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n    return function () {\n      if (componentRef.current) {\n        componentRef.current.removeEventListener('wheel', onRawWheel);\n        componentRef.current.removeEventListener('DOMMouseScroll', onFireFoxScroll);\n        componentRef.current.removeEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n      }\n    };\n  }, [useVirtual]); // ================================= Ref ==================================\n\n  var scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, collectHeight, syncScrollTop, function () {\n    var _scrollBarRef$current;\n    (_scrollBarRef$current = scrollBarRef.current) === null || _scrollBarRef$current === void 0 ? void 0 : _scrollBarRef$current.delayHidden();\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: scrollTo\n    };\n  }); // ================================ Effect ================================\n\n  /** We need told outside that some list not rendered */\n\n  useLayoutEffect(function () {\n    if (onVisibleChange) {\n      var renderList = mergedData.slice(start, end + 1);\n      onVisibleChange(renderList, mergedData);\n    }\n  }, [start, end, mergedData]); // ================================ Render ================================\n\n  var listChildren = useChildren(mergedData, start, end, setInstanceRef, children, sharedConfig);\n  var componentStyle = null;\n  if (height) {\n    componentStyle = _objectSpread(_defineProperty({}, fullHeight ? 'height' : 'maxHeight', height), ScrollStyle);\n    if (useVirtual) {\n      componentStyle.overflowY = 'hidden';\n      if (scrollMoving) {\n        componentStyle.pointerEvents = 'none';\n      }\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      position: 'relative'\n    }),\n    className: mergedClassName\n  }, restProps), /*#__PURE__*/React.createElement(Component, {\n    className: \"\".concat(prefixCls, \"-holder\"),\n    style: componentStyle,\n    ref: componentRef,\n    onScroll: onFallbackScroll\n  }, /*#__PURE__*/React.createElement(Filler, {\n    prefixCls: prefixCls,\n    height: scrollHeight,\n    offset: offset,\n    onInnerResize: collectHeight,\n    ref: fillerInnerRef\n  }, listChildren)), useVirtual && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: scrollBarRef,\n    prefixCls: prefixCls,\n    scrollTop: scrollTop,\n    height: height,\n    scrollHeight: scrollHeight,\n    count: mergedData.length,\n    onScroll: onScrollBar,\n    onStartMove: function onStartMove() {\n      setScrollMoving(true);\n    },\n    onStopMove: function onStopMove() {\n      setScrollMoving(false);\n    }\n  }));\n}\nvar List = /*#__PURE__*/React.forwardRef(RawList);\nList.displayName = 'List';\nexport default List;", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "constructor", "name", "Array", "from", "test", "len", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "err", "isArray", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "React", "useRef", "useState", "classNames", "Filler", "<PERSON><PERSON>Bar", "useChildren", "useHeights", "useScrollTo", "useDiffItem", "useFrameWheel", "useMobileTouchMove", "useOriginScroll", "useLayoutEffect", "EMPTY_DATA", "ScrollStyle", "overflowY", "overflowAnchor", "RawList", "props", "ref", "_props$prefixCls", "prefixCls", "className", "height", "itemHeight", "_props$fullHeight", "fullHeight", "style", "data", "children", "itemKey", "virtual", "_props$component", "component", "Component", "onScroll", "onVisibleChange", "restProps", "useVirtual", "inVirtual", "_useState", "_useState2", "scrollTop", "setScrollTop", "_useState3", "_useState4", "scrollMoving", "setScrollMoving", "mergedClassName", "mergedData", "componentRef", "fillerInnerRef", "scrollBarRef", "<PERSON><PERSON><PERSON>", "useCallback", "item", "sharedConfig", "syncScrollTop", "newTop", "origin", "alignedTop", "keepInRange", "current", "rangeRef", "start", "end", "diffItemRef", "_useDiffItem", "_useDiffItem2", "diffItem", "_useHeights", "_useHeights2", "setInstanceRef", "collectHeight", "heights", "heightUpdatedMark", "_React$useMemo", "useMemo", "scrollHeight", "undefined", "offset", "_fillerInnerRef$curre", "offsetHeight", "itemTop", "startIndex", "startOffset", "endIndex", "dataLen", "cacheHeight", "get", "currentItemBottom", "Math", "min", "maxScrollHeight", "maxScrollHeightRef", "newScrollTop", "Number", "isNaN", "max", "isScrollAtTop", "isScrollAtBottom", "originScroll", "onScrollBar", "onFallbackScroll", "e", "currentTarget", "_useFrameWheel", "offsetY", "top", "_useFrameWheel2", "onRawWheel", "onFireFoxScroll", "deltaY", "smoothOffset", "preventDefault", "onMozMousePixelScroll", "addEventListener", "removeEventListener", "scrollTo", "_scrollBarRef$current", "delayHidden", "useImperativeHandle", "renderList", "listC<PERSON><PERSON>n", "componentStyle", "pointerEvents", "createElement", "position", "concat", "onInnerResize", "count", "onStartMove", "onStopMove", "List", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-virtual-list/es/List.js"], "sourcesContent": ["var _excluded = [\"prefixCls\", \"className\", \"height\", \"itemHeight\", \"fullHeight\", \"style\", \"data\", \"children\", \"itemKey\", \"virtual\", \"component\", \"onScroll\", \"onVisibleChange\"];\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport Filler from './Filler';\nimport ScrollBar from './ScrollBar';\nimport useChildren from './hooks/useChildren';\nimport useHeights from './hooks/useHeights';\nimport useScrollTo from './hooks/useScrollTo';\nimport useDiffItem from './hooks/useDiffItem';\nimport useFrameWheel from './hooks/useFrameWheel';\nimport useMobileTouchMove from './hooks/useMobileTouchMove';\nimport useOriginScroll from './hooks/useOriginScroll';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nvar EMPTY_DATA = [];\nvar ScrollStyle = {\n  overflowY: 'auto',\n  overflowAnchor: 'none'\n};\nexport function RawList(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-virtual-list' : _props$prefixCls,\n      className = props.className,\n      height = props.height,\n      itemHeight = props.itemHeight,\n      _props$fullHeight = props.fullHeight,\n      fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight,\n      style = props.style,\n      data = props.data,\n      children = props.children,\n      itemKey = props.itemKey,\n      virtual = props.virtual,\n      _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      onScroll = props.onScroll,\n      onVisibleChange = props.onVisibleChange,\n      restProps = _objectWithoutProperties(props, _excluded); // ================================= MISC =================================\n\n\n  var useVirtual = !!(virtual !== false && height && itemHeight);\n  var inVirtual = useVirtual && data && itemHeight * data.length > height;\n\n  var _useState = useState(0),\n      _useState2 = _slicedToArray(_useState, 2),\n      scrollTop = _useState2[0],\n      setScrollTop = _useState2[1];\n\n  var _useState3 = useState(false),\n      _useState4 = _slicedToArray(_useState3, 2),\n      scrollMoving = _useState4[0],\n      setScrollMoving = _useState4[1];\n\n  var mergedClassName = classNames(prefixCls, className);\n  var mergedData = data || EMPTY_DATA;\n  var componentRef = useRef();\n  var fillerInnerRef = useRef();\n  var scrollBarRef = useRef(); // Hack on scrollbar to enable flash call\n  // =============================== Item Key ===============================\n\n  var getKey = React.useCallback(function (item) {\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n\n    return item === null || item === void 0 ? void 0 : item[itemKey];\n  }, [itemKey]);\n  var sharedConfig = {\n    getKey: getKey\n  }; // ================================ Scroll ================================\n\n  function syncScrollTop(newTop) {\n    setScrollTop(function (origin) {\n      var value;\n\n      if (typeof newTop === 'function') {\n        value = newTop(origin);\n      } else {\n        value = newTop;\n      }\n\n      var alignedTop = keepInRange(value);\n      componentRef.current.scrollTop = alignedTop;\n      return alignedTop;\n    });\n  } // ================================ Legacy ================================\n  // Put ref here since the range is generate by follow\n\n\n  var rangeRef = useRef({\n    start: 0,\n    end: mergedData.length\n  });\n  var diffItemRef = useRef();\n\n  var _useDiffItem = useDiffItem(mergedData, getKey),\n      _useDiffItem2 = _slicedToArray(_useDiffItem, 1),\n      diffItem = _useDiffItem2[0];\n\n  diffItemRef.current = diffItem; // ================================ Height ================================\n\n  var _useHeights = useHeights(getKey, null, null),\n      _useHeights2 = _slicedToArray(_useHeights, 4),\n      setInstanceRef = _useHeights2[0],\n      collectHeight = _useHeights2[1],\n      heights = _useHeights2[2],\n      heightUpdatedMark = _useHeights2[3]; // ========================== Visible Calculation =========================\n\n\n  var _React$useMemo = React.useMemo(function () {\n    if (!useVirtual) {\n      return {\n        scrollHeight: undefined,\n        start: 0,\n        end: mergedData.length - 1,\n        offset: undefined\n      };\n    } // Always use virtual scroll bar in avoid shaking\n\n\n    // Always use virtual scroll bar in avoid shaking\n    if (!inVirtual) {\n      var _fillerInnerRef$curre;\n\n      return {\n        scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,\n        start: 0,\n        end: mergedData.length - 1,\n        offset: undefined\n      };\n    }\n\n    var itemTop = 0;\n    var startIndex;\n    var startOffset;\n    var endIndex;\n    var dataLen = mergedData.length;\n\n    for (var i = 0; i < dataLen; i += 1) {\n      var item = mergedData[i];\n      var key = getKey(item);\n      var cacheHeight = heights.get(key);\n      var currentItemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight); // Check item top in the range\n\n      // Check item top in the range\n      if (currentItemBottom >= scrollTop && startIndex === undefined) {\n        startIndex = i;\n        startOffset = itemTop;\n      } // Check item bottom in the range. We will render additional one item for motion usage\n\n\n      // Check item bottom in the range. We will render additional one item for motion usage\n      if (currentItemBottom > scrollTop + height && endIndex === undefined) {\n        endIndex = i;\n      }\n\n      itemTop = currentItemBottom;\n    } // Fallback to normal if not match. This code should never reach\n\n    /* istanbul ignore next */\n\n\n    // Fallback to normal if not match. This code should never reach\n\n    /* istanbul ignore next */\n    if (startIndex === undefined) {\n      startIndex = 0;\n      startOffset = 0;\n    }\n\n    if (endIndex === undefined) {\n      endIndex = mergedData.length - 1;\n    } // Give cache to improve scroll experience\n\n\n    // Give cache to improve scroll experience\n    endIndex = Math.min(endIndex + 1, mergedData.length);\n    return {\n      scrollHeight: itemTop,\n      start: startIndex,\n      end: endIndex,\n      offset: startOffset\n    };\n  }, [inVirtual, useVirtual, scrollTop, mergedData, heightUpdatedMark, height]),\n      scrollHeight = _React$useMemo.scrollHeight,\n      start = _React$useMemo.start,\n      end = _React$useMemo.end,\n      offset = _React$useMemo.offset;\n\n  rangeRef.current.start = start;\n  rangeRef.current.end = end; // =============================== In Range ===============================\n\n  var maxScrollHeight = scrollHeight - height;\n  var maxScrollHeightRef = useRef(maxScrollHeight);\n  maxScrollHeightRef.current = maxScrollHeight;\n\n  function keepInRange(newScrollTop) {\n    var newTop = newScrollTop;\n\n    if (!Number.isNaN(maxScrollHeightRef.current)) {\n      newTop = Math.min(newTop, maxScrollHeightRef.current);\n    }\n\n    newTop = Math.max(newTop, 0);\n    return newTop;\n  }\n\n  var isScrollAtTop = scrollTop <= 0;\n  var isScrollAtBottom = scrollTop >= maxScrollHeight;\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom); // ================================ Scroll ================================\n\n  function onScrollBar(newScrollTop) {\n    var newTop = newScrollTop;\n    syncScrollTop(newTop);\n  } // When data size reduce. It may trigger native scroll event back to fit scroll position\n\n\n  function onFallbackScroll(e) {\n    var newScrollTop = e.currentTarget.scrollTop;\n\n    if (newScrollTop !== scrollTop) {\n      syncScrollTop(newScrollTop);\n    } // Trigger origin onScroll\n\n\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll(e);\n  } // Since this added in global,should use ref to keep update\n\n\n  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, function (offsetY) {\n    syncScrollTop(function (top) {\n      var newTop = top + offsetY;\n      return newTop;\n    });\n  }),\n      _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2),\n      onRawWheel = _useFrameWheel2[0],\n      onFireFoxScroll = _useFrameWheel2[1]; // Mobile touch move\n\n\n  useMobileTouchMove(useVirtual, componentRef, function (deltaY, smoothOffset) {\n    if (originScroll(deltaY, smoothOffset)) {\n      return false;\n    }\n\n    onRawWheel({\n      preventDefault: function preventDefault() {},\n      deltaY: deltaY\n    });\n    return true;\n  });\n  useLayoutEffect(function () {\n    // Firefox only\n    function onMozMousePixelScroll(e) {\n      if (useVirtual) {\n        e.preventDefault();\n      }\n    }\n\n    componentRef.current.addEventListener('wheel', onRawWheel);\n    componentRef.current.addEventListener('DOMMouseScroll', onFireFoxScroll);\n    componentRef.current.addEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n    return function () {\n      if (componentRef.current) {\n        componentRef.current.removeEventListener('wheel', onRawWheel);\n        componentRef.current.removeEventListener('DOMMouseScroll', onFireFoxScroll);\n        componentRef.current.removeEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n      }\n    };\n  }, [useVirtual]); // ================================= Ref ==================================\n\n  var scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, collectHeight, syncScrollTop, function () {\n    var _scrollBarRef$current;\n\n    (_scrollBarRef$current = scrollBarRef.current) === null || _scrollBarRef$current === void 0 ? void 0 : _scrollBarRef$current.delayHidden();\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: scrollTo\n    };\n  }); // ================================ Effect ================================\n\n  /** We need told outside that some list not rendered */\n\n  useLayoutEffect(function () {\n    if (onVisibleChange) {\n      var renderList = mergedData.slice(start, end + 1);\n      onVisibleChange(renderList, mergedData);\n    }\n  }, [start, end, mergedData]); // ================================ Render ================================\n\n  var listChildren = useChildren(mergedData, start, end, setInstanceRef, children, sharedConfig);\n  var componentStyle = null;\n\n  if (height) {\n    componentStyle = _objectSpread(_defineProperty({}, fullHeight ? 'height' : 'maxHeight', height), ScrollStyle);\n\n    if (useVirtual) {\n      componentStyle.overflowY = 'hidden';\n\n      if (scrollMoving) {\n        componentStyle.pointerEvents = 'none';\n      }\n    }\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      position: 'relative'\n    }),\n    className: mergedClassName\n  }, restProps), /*#__PURE__*/React.createElement(Component, {\n    className: \"\".concat(prefixCls, \"-holder\"),\n    style: componentStyle,\n    ref: componentRef,\n    onScroll: onFallbackScroll\n  }, /*#__PURE__*/React.createElement(Filler, {\n    prefixCls: prefixCls,\n    height: scrollHeight,\n    offset: offset,\n    onInnerResize: collectHeight,\n    ref: fillerInnerRef\n  }, listChildren)), useVirtual && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: scrollBarRef,\n    prefixCls: prefixCls,\n    scrollTop: scrollTop,\n    height: height,\n    scrollHeight: scrollHeight,\n    count: mergedData.length,\n    onScroll: onScrollBar,\n    onStartMove: function onStartMove() {\n      setScrollMoving(true);\n    },\n    onStopMove: function onStopMove() {\n      setScrollMoving(false);\n    }\n  }));\n}\nvar List = /*#__PURE__*/React.forwardRef(RawList);\nList.displayName = 'List';\nexport default List;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,CAAC;AAE/K,SAASC,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOH,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAAE;AAE5T,SAASQ,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIb,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjB,MAAM,CAACgB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnB,MAAM,CAACoB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACX,KAAK,CAACI,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAEpV,SAASQ,aAAaA,CAACrB,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGS,OAAO,CAACZ,MAAM,CAACM,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACkB,OAAO,CAAC,UAAUjB,GAAG,EAAE;MAAEkB,eAAe,CAACvB,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGP,MAAM,CAAC0B,yBAAyB,GAAG1B,MAAM,CAAC2B,gBAAgB,CAACzB,MAAM,EAAEF,MAAM,CAAC0B,yBAAyB,CAACpB,MAAM,CAAC,CAAC,GAAGM,OAAO,CAACZ,MAAM,CAACM,MAAM,CAAC,CAAC,CAACkB,OAAO,CAAC,UAAUjB,GAAG,EAAE;MAAEP,MAAM,CAAC4B,cAAc,CAAC1B,MAAM,EAAEK,GAAG,EAAEP,MAAM,CAACoB,wBAAwB,CAACd,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AAEzf,SAASuB,eAAeA,CAACI,GAAG,EAAEtB,GAAG,EAAEuB,KAAK,EAAE;EAAE,IAAIvB,GAAG,IAAIsB,GAAG,EAAE;IAAE7B,MAAM,CAAC4B,cAAc,CAACC,GAAG,EAAEtB,GAAG,EAAE;MAAEuB,KAAK,EAAEA,KAAK;MAAET,UAAU,EAAE,IAAI;MAAEU,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACtB,GAAG,CAAC,GAAGuB,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,cAAcA,CAACC,GAAG,EAAE/B,CAAC,EAAE;EAAE,OAAOgC,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAE/B,CAAC,CAAC,IAAIkC,2BAA2B,CAACH,GAAG,EAAE/B,CAAC,CAAC,IAAImC,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAG3C,MAAM,CAACQ,SAAS,CAACoC,QAAQ,CAAClC,IAAI,CAAC8B,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACM,WAAW,EAAEH,CAAC,GAAGH,CAAC,CAACM,WAAW,CAACC,IAAI;EAAE,IAAIJ,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOK,KAAK,CAACC,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACO,IAAI,CAACP,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACR,GAAG,EAAEiB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGjB,GAAG,CAAC7B,MAAM,EAAE8C,GAAG,GAAGjB,GAAG,CAAC7B,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEiD,IAAI,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,EAAEhD,CAAC,GAAGgD,GAAG,EAAEhD,CAAC,EAAE,EAAE;IAAEiD,IAAI,CAACjD,CAAC,CAAC,GAAG+B,GAAG,CAAC/B,CAAC,CAAC;EAAE;EAAE,OAAOiD,IAAI;AAAE;AAEtL,SAAShB,qBAAqBA,CAACF,GAAG,EAAE/B,CAAC,EAAE;EAAE,IAAIkD,EAAE,GAAGnB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOoB,MAAM,KAAK,WAAW,IAAIpB,GAAG,CAACoB,MAAM,CAACC,QAAQ,CAAC,IAAIrB,GAAG,CAAC,YAAY,CAAC;EAAE,IAAImB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAAC3C,IAAI,CAACwB,GAAG,CAAC,EAAE,EAAEuB,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAAClC,IAAI,CAACqC,EAAE,CAAC7B,KAAK,CAAC;MAAE,IAAI3B,CAAC,IAAIqD,IAAI,CAACnD,MAAM,KAAKF,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAO4D,GAAG,EAAE;IAAEL,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGG,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACN,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASrB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIc,KAAK,CAACgB,OAAO,CAAC9B,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,SAAS+B,wBAAwBA,CAAC3D,MAAM,EAAE4D,QAAQ,EAAE;EAAE,IAAI5D,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGiE,6BAA6B,CAAC7D,MAAM,EAAE4D,QAAQ,CAAC;EAAE,IAAI3D,GAAG,EAAEJ,CAAC;EAAE,IAAIH,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIoD,gBAAgB,GAAGpE,MAAM,CAACgB,qBAAqB,CAACV,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,gBAAgB,CAAC/D,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAG6D,gBAAgB,CAACjE,CAAC,CAAC;MAAE,IAAI+D,QAAQ,CAACG,OAAO,CAAC9D,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACP,MAAM,CAACQ,SAAS,CAAC8D,oBAAoB,CAAC5D,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAE3e,SAASiE,6BAA6BA,CAAC7D,MAAM,EAAE4D,QAAQ,EAAE;EAAE,IAAI5D,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIqE,UAAU,GAAGvE,MAAM,CAACe,IAAI,CAACT,MAAM,CAAC;EAAE,IAAIC,GAAG,EAAEJ,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,UAAU,CAAClE,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAEI,GAAG,GAAGgE,UAAU,CAACpE,CAAC,CAAC;IAAE,IAAI+D,QAAQ,CAACG,OAAO,CAAC9D,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AAElT,OAAO,KAAKsE,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,WAAW,GAAG;EAChBC,SAAS,EAAE,MAAM;EACjBC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC9EE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,iBAAiB,GAAGP,KAAK,CAACQ,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;IACvBC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,gBAAgB,GAAGd,KAAK,CAACe,SAAS;IAClCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEG,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,eAAe,GAAGlB,KAAK,CAACkB,eAAe;IACvCC,SAAS,GAAG7C,wBAAwB,CAAC0B,KAAK,EAAE7F,SAAS,CAAC,CAAC,CAAC;;EAG5D,IAAIiH,UAAU,GAAG,CAAC,EAAEP,OAAO,KAAK,KAAK,IAAIR,MAAM,IAAIC,UAAU,CAAC;EAC9D,IAAIe,SAAS,GAAGD,UAAU,IAAIV,IAAI,IAAIJ,UAAU,GAAGI,IAAI,CAAChG,MAAM,GAAG2F,MAAM;EAEvE,IAAIiB,SAAS,GAAGvC,QAAQ,CAAC,CAAC,CAAC;IACvBwC,UAAU,GAAGjF,cAAc,CAACgF,SAAS,EAAE,CAAC,CAAC;IACzCE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAIG,UAAU,GAAG3C,QAAQ,CAAC,KAAK,CAAC;IAC5B4C,UAAU,GAAGrF,cAAc,CAACoF,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,eAAe,GAAG9C,UAAU,CAACmB,SAAS,EAAEC,SAAS,CAAC;EACtD,IAAI2B,UAAU,GAAGrB,IAAI,IAAIf,UAAU;EACnC,IAAIqC,YAAY,GAAGlD,MAAM,CAAC,CAAC;EAC3B,IAAImD,cAAc,GAAGnD,MAAM,CAAC,CAAC;EAC7B,IAAIoD,YAAY,GAAGpD,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B;;EAEA,IAAIqD,MAAM,GAAGtD,KAAK,CAACuD,WAAW,CAAC,UAAUC,IAAI,EAAE;IAC7C,IAAI,OAAOzB,OAAO,KAAK,UAAU,EAAE;MACjC,OAAOA,OAAO,CAACyB,IAAI,CAAC;IACtB;IAEA,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACzB,OAAO,CAAC;EAClE,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,IAAI0B,YAAY,GAAG;IACjBH,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;;EAEH,SAASI,aAAaA,CAACC,MAAM,EAAE;IAC7Bf,YAAY,CAAC,UAAUgB,MAAM,EAAE;MAC7B,IAAItG,KAAK;MAET,IAAI,OAAOqG,MAAM,KAAK,UAAU,EAAE;QAChCrG,KAAK,GAAGqG,MAAM,CAACC,MAAM,CAAC;MACxB,CAAC,MAAM;QACLtG,KAAK,GAAGqG,MAAM;MAChB;MAEA,IAAIE,UAAU,GAAGC,WAAW,CAACxG,KAAK,CAAC;MACnC6F,YAAY,CAACY,OAAO,CAACpB,SAAS,GAAGkB,UAAU;MAC3C,OAAOA,UAAU;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;;EAGA,IAAIG,QAAQ,GAAG/D,MAAM,CAAC;IACpBgE,KAAK,EAAE,CAAC;IACRC,GAAG,EAAEhB,UAAU,CAACrH;EAClB,CAAC,CAAC;EACF,IAAIsI,WAAW,GAAGlE,MAAM,CAAC,CAAC;EAE1B,IAAImE,YAAY,GAAG3D,WAAW,CAACyC,UAAU,EAAEI,MAAM,CAAC;IAC9Ce,aAAa,GAAG5G,cAAc,CAAC2G,YAAY,EAAE,CAAC,CAAC;IAC/CE,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;EAE/BF,WAAW,CAACJ,OAAO,GAAGO,QAAQ,CAAC,CAAC;;EAEhC,IAAIC,WAAW,GAAGhE,UAAU,CAAC+C,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5CkB,YAAY,GAAG/G,cAAc,CAAC8G,WAAW,EAAE,CAAC,CAAC;IAC7CE,cAAc,GAAGD,YAAY,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,YAAY,CAAC,CAAC,CAAC;IAC/BG,OAAO,GAAGH,YAAY,CAAC,CAAC,CAAC;IACzBI,iBAAiB,GAAGJ,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzC,IAAIK,cAAc,GAAG7E,KAAK,CAAC8E,OAAO,CAAC,YAAY;MAC7C,IAAI,CAACvC,UAAU,EAAE;QACf,OAAO;UACLwC,YAAY,EAAEC,SAAS;UACvBf,KAAK,EAAE,CAAC;UACRC,GAAG,EAAEhB,UAAU,CAACrH,MAAM,GAAG,CAAC;UAC1BoJ,MAAM,EAAED;QACV,CAAC;MACH,CAAC,CAAC;;MAGF;MACA,IAAI,CAACxC,SAAS,EAAE;QACd,IAAI0C,qBAAqB;QAEzB,OAAO;UACLH,YAAY,EAAE,CAAC,CAACG,qBAAqB,GAAG9B,cAAc,CAACW,OAAO,MAAM,IAAI,IAAImB,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,YAAY,KAAK,CAAC;UAChKlB,KAAK,EAAE,CAAC;UACRC,GAAG,EAAEhB,UAAU,CAACrH,MAAM,GAAG,CAAC;UAC1BoJ,MAAM,EAAED;QACV,CAAC;MACH;MAEA,IAAII,OAAO,GAAG,CAAC;MACf,IAAIC,UAAU;MACd,IAAIC,WAAW;MACf,IAAIC,QAAQ;MACZ,IAAIC,OAAO,GAAGtC,UAAU,CAACrH,MAAM;MAE/B,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6J,OAAO,EAAE7J,CAAC,IAAI,CAAC,EAAE;QACnC,IAAI6H,IAAI,GAAGN,UAAU,CAACvH,CAAC,CAAC;QACxB,IAAII,GAAG,GAAGuH,MAAM,CAACE,IAAI,CAAC;QACtB,IAAIiC,WAAW,GAAGd,OAAO,CAACe,GAAG,CAAC3J,GAAG,CAAC;QAClC,IAAI4J,iBAAiB,GAAGP,OAAO,IAAIK,WAAW,KAAKT,SAAS,GAAGvD,UAAU,GAAGgE,WAAW,CAAC,CAAC,CAAC;;QAE1F;QACA,IAAIE,iBAAiB,IAAIhD,SAAS,IAAI0C,UAAU,KAAKL,SAAS,EAAE;UAC9DK,UAAU,GAAG1J,CAAC;UACd2J,WAAW,GAAGF,OAAO;QACvB,CAAC,CAAC;;QAGF;QACA,IAAIO,iBAAiB,GAAGhD,SAAS,GAAGnB,MAAM,IAAI+D,QAAQ,KAAKP,SAAS,EAAE;UACpEO,QAAQ,GAAG5J,CAAC;QACd;QAEAyJ,OAAO,GAAGO,iBAAiB;MAC7B,CAAC,CAAC;;MAEF;;MAGA;;MAEA;MACA,IAAIN,UAAU,KAAKL,SAAS,EAAE;QAC5BK,UAAU,GAAG,CAAC;QACdC,WAAW,GAAG,CAAC;MACjB;MAEA,IAAIC,QAAQ,KAAKP,SAAS,EAAE;QAC1BO,QAAQ,GAAGrC,UAAU,CAACrH,MAAM,GAAG,CAAC;MAClC,CAAC,CAAC;;MAGF;MACA0J,QAAQ,GAAGK,IAAI,CAACC,GAAG,CAACN,QAAQ,GAAG,CAAC,EAAErC,UAAU,CAACrH,MAAM,CAAC;MACpD,OAAO;QACLkJ,YAAY,EAAEK,OAAO;QACrBnB,KAAK,EAAEoB,UAAU;QACjBnB,GAAG,EAAEqB,QAAQ;QACbN,MAAM,EAAEK;MACV,CAAC;IACH,CAAC,EAAE,CAAC9C,SAAS,EAAED,UAAU,EAAEI,SAAS,EAAEO,UAAU,EAAE0B,iBAAiB,EAAEpD,MAAM,CAAC,CAAC;IACzEuD,YAAY,GAAGF,cAAc,CAACE,YAAY;IAC1Cd,KAAK,GAAGY,cAAc,CAACZ,KAAK;IAC5BC,GAAG,GAAGW,cAAc,CAACX,GAAG;IACxBe,MAAM,GAAGJ,cAAc,CAACI,MAAM;EAElCjB,QAAQ,CAACD,OAAO,CAACE,KAAK,GAAGA,KAAK;EAC9BD,QAAQ,CAACD,OAAO,CAACG,GAAG,GAAGA,GAAG,CAAC,CAAC;;EAE5B,IAAI4B,eAAe,GAAGf,YAAY,GAAGvD,MAAM;EAC3C,IAAIuE,kBAAkB,GAAG9F,MAAM,CAAC6F,eAAe,CAAC;EAChDC,kBAAkB,CAAChC,OAAO,GAAG+B,eAAe;EAE5C,SAAShC,WAAWA,CAACkC,YAAY,EAAE;IACjC,IAAIrC,MAAM,GAAGqC,YAAY;IAEzB,IAAI,CAACC,MAAM,CAACC,KAAK,CAACH,kBAAkB,CAAChC,OAAO,CAAC,EAAE;MAC7CJ,MAAM,GAAGiC,IAAI,CAACC,GAAG,CAAClC,MAAM,EAAEoC,kBAAkB,CAAChC,OAAO,CAAC;IACvD;IAEAJ,MAAM,GAAGiC,IAAI,CAACO,GAAG,CAACxC,MAAM,EAAE,CAAC,CAAC;IAC5B,OAAOA,MAAM;EACf;EAEA,IAAIyC,aAAa,GAAGzD,SAAS,IAAI,CAAC;EAClC,IAAI0D,gBAAgB,GAAG1D,SAAS,IAAImD,eAAe;EACnD,IAAIQ,YAAY,GAAG1F,eAAe,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,CAAC,CAAC;;EAErE,SAASE,WAAWA,CAACP,YAAY,EAAE;IACjC,IAAIrC,MAAM,GAAGqC,YAAY;IACzBtC,aAAa,CAACC,MAAM,CAAC;EACvB,CAAC,CAAC;;EAGF,SAAS6C,gBAAgBA,CAACC,CAAC,EAAE;IAC3B,IAAIT,YAAY,GAAGS,CAAC,CAACC,aAAa,CAAC/D,SAAS;IAE5C,IAAIqD,YAAY,KAAKrD,SAAS,EAAE;MAC9Be,aAAa,CAACsC,YAAY,CAAC;IAC7B,CAAC,CAAC;;IAGF5D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACqE,CAAC,CAAC;EACjE,CAAC,CAAC;;EAGF,IAAIE,cAAc,GAAGjG,aAAa,CAAC6B,UAAU,EAAE6D,aAAa,EAAEC,gBAAgB,EAAE,UAAUO,OAAO,EAAE;MACjGlD,aAAa,CAAC,UAAUmD,GAAG,EAAE;QAC3B,IAAIlD,MAAM,GAAGkD,GAAG,GAAGD,OAAO;QAC1B,OAAOjD,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IACEmD,eAAe,GAAGrJ,cAAc,CAACkJ,cAAc,EAAE,CAAC,CAAC;IACnDI,UAAU,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC/BE,eAAe,GAAGF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG1CnG,kBAAkB,CAAC4B,UAAU,EAAEY,YAAY,EAAE,UAAU8D,MAAM,EAAEC,YAAY,EAAE;IAC3E,IAAIZ,YAAY,CAACW,MAAM,EAAEC,YAAY,CAAC,EAAE;MACtC,OAAO,KAAK;IACd;IAEAH,UAAU,CAAC;MACTI,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC,CAAC;MAC5CF,MAAM,EAAEA;IACV,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC,CAAC;EACFpG,eAAe,CAAC,YAAY;IAC1B;IACA,SAASuG,qBAAqBA,CAACX,CAAC,EAAE;MAChC,IAAIlE,UAAU,EAAE;QACdkE,CAAC,CAACU,cAAc,CAAC,CAAC;MACpB;IACF;IAEAhE,YAAY,CAACY,OAAO,CAACsD,gBAAgB,CAAC,OAAO,EAAEN,UAAU,CAAC;IAC1D5D,YAAY,CAACY,OAAO,CAACsD,gBAAgB,CAAC,gBAAgB,EAAEL,eAAe,CAAC;IACxE7D,YAAY,CAACY,OAAO,CAACsD,gBAAgB,CAAC,qBAAqB,EAAED,qBAAqB,CAAC;IACnF,OAAO,YAAY;MACjB,IAAIjE,YAAY,CAACY,OAAO,EAAE;QACxBZ,YAAY,CAACY,OAAO,CAACuD,mBAAmB,CAAC,OAAO,EAAEP,UAAU,CAAC;QAC7D5D,YAAY,CAACY,OAAO,CAACuD,mBAAmB,CAAC,gBAAgB,EAAEN,eAAe,CAAC;QAC3E7D,YAAY,CAACY,OAAO,CAACuD,mBAAmB,CAAC,qBAAqB,EAAEF,qBAAqB,CAAC;MACxF;IACF,CAAC;EACH,CAAC,EAAE,CAAC7E,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB,IAAIgF,QAAQ,GAAG/G,WAAW,CAAC2C,YAAY,EAAED,UAAU,EAAEyB,OAAO,EAAElD,UAAU,EAAE6B,MAAM,EAAEoB,aAAa,EAAEhB,aAAa,EAAE,YAAY;IAC1H,IAAI8D,qBAAqB;IAEzB,CAACA,qBAAqB,GAAGnE,YAAY,CAACU,OAAO,MAAM,IAAI,IAAIyD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,WAAW,CAAC,CAAC;EAC5I,CAAC,CAAC;EACFzH,KAAK,CAAC0H,mBAAmB,CAACtG,GAAG,EAAE,YAAY;IACzC,OAAO;MACLmG,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ;;EAEA1G,eAAe,CAAC,YAAY;IAC1B,IAAIwB,eAAe,EAAE;MACnB,IAAIsF,UAAU,GAAGzE,UAAU,CAAC7E,KAAK,CAAC4F,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC;MACjD7B,eAAe,CAACsF,UAAU,EAAEzE,UAAU,CAAC;IACzC;EACF,CAAC,EAAE,CAACe,KAAK,EAAEC,GAAG,EAAEhB,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE9B,IAAI0E,YAAY,GAAGtH,WAAW,CAAC4C,UAAU,EAAEe,KAAK,EAAEC,GAAG,EAAEO,cAAc,EAAE3C,QAAQ,EAAE2B,YAAY,CAAC;EAC9F,IAAIoE,cAAc,GAAG,IAAI;EAEzB,IAAIrG,MAAM,EAAE;IACVqG,cAAc,GAAG9K,aAAa,CAACE,eAAe,CAAC,CAAC,CAAC,EAAE0E,UAAU,GAAG,QAAQ,GAAG,WAAW,EAAEH,MAAM,CAAC,EAAET,WAAW,CAAC;IAE7G,IAAIwB,UAAU,EAAE;MACdsF,cAAc,CAAC7G,SAAS,GAAG,QAAQ;MAEnC,IAAI+B,YAAY,EAAE;QAChB8E,cAAc,CAACC,aAAa,GAAG,MAAM;MACvC;IACF;EACF;EAEA,OAAO,aAAa9H,KAAK,CAAC+H,aAAa,CAAC,KAAK,EAAExM,QAAQ,CAAC;IACtDqG,KAAK,EAAE7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6E,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDoG,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFzG,SAAS,EAAE0B;EACb,CAAC,EAAEX,SAAS,CAAC,EAAE,aAAatC,KAAK,CAAC+H,aAAa,CAAC5F,SAAS,EAAE;IACzDZ,SAAS,EAAE,EAAE,CAAC0G,MAAM,CAAC3G,SAAS,EAAE,SAAS,CAAC;IAC1CM,KAAK,EAAEiG,cAAc;IACrBzG,GAAG,EAAE+B,YAAY;IACjBf,QAAQ,EAAEoE;EACZ,CAAC,EAAE,aAAaxG,KAAK,CAAC+H,aAAa,CAAC3H,MAAM,EAAE;IAC1CkB,SAAS,EAAEA,SAAS;IACpBE,MAAM,EAAEuD,YAAY;IACpBE,MAAM,EAAEA,MAAM;IACdiD,aAAa,EAAExD,aAAa;IAC5BtD,GAAG,EAAEgC;EACP,CAAC,EAAEwE,YAAY,CAAC,CAAC,EAAErF,UAAU,IAAI,aAAavC,KAAK,CAAC+H,aAAa,CAAC1H,SAAS,EAAE;IAC3Ee,GAAG,EAAEiC,YAAY;IACjB/B,SAAS,EAAEA,SAAS;IACpBqB,SAAS,EAAEA,SAAS;IACpBnB,MAAM,EAAEA,MAAM;IACduD,YAAY,EAAEA,YAAY;IAC1BoD,KAAK,EAAEjF,UAAU,CAACrH,MAAM;IACxBuG,QAAQ,EAAEmE,WAAW;IACrB6B,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClCpF,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC;IACDqF,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCrF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,CAAC,CAAC;AACL;AACA,IAAIsF,IAAI,GAAG,aAAatI,KAAK,CAACuI,UAAU,CAACrH,OAAO,CAAC;AACjDoH,IAAI,CAACE,WAAW,GAAG,MAAM;AACzB,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}