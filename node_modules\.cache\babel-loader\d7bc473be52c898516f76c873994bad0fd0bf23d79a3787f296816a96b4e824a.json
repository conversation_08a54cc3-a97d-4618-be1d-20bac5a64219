{"ast": null, "code": "// This icon file is generated automatically.\nvar ExclamationCircleOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\"\n      }\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z\"\n      }\n    }]\n  },\n  \"name\": \"exclamation-circle\",\n  \"theme\": \"outlined\"\n};\nexport default ExclamationCircleOutlined;", "map": {"version": 3, "names": ["ExclamationCircleOutlined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons-svg/es/asn/ExclamationCircleOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar ExclamationCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z\" } }] }, \"name\": \"exclamation-circle\", \"theme\": \"outlined\" };\nexport default ExclamationCircleOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,yBAAyB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAgL;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAmI;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,oBAAoB;EAAE,OAAO,EAAE;AAAW,CAAC;AAC3jB,eAAeA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}