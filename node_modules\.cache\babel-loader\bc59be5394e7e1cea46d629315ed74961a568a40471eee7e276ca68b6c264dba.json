{"ast": null, "code": "function loopFiles(item, callback) {\n  var dirReader = item.createReader();\n  var fileList = [];\n  function sequence() {\n    dirReader.readEntries(function (entries) {\n      var entryList = Array.prototype.slice.apply(entries);\n      fileList = fileList.concat(entryList); // Check if all the file has been viewed\n\n      var isFinished = !entryList.length;\n      if (isFinished) {\n        callback(fileList);\n      } else {\n        sequence();\n      }\n    });\n  }\n  sequence();\n}\nvar traverseFileTree = function traverseFileTree(files, callback, isAccepted) {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  var _traverseFileTree = function _traverseFileTree(item, path) {\n    // eslint-disable-next-line no-param-reassign\n    item.path = path || '';\n    if (item.isFile) {\n      item.file(function (file) {\n        if (isAccepted(file)) {\n          // https://github.com/ant-design/ant-design/issues/16426\n          if (item.fullPath && !file.webkitRelativePath) {\n            Object.defineProperties(file, {\n              webkitRelativePath: {\n                writable: true\n              }\n            }); // eslint-disable-next-line no-param-reassign\n\n            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');\n            Object.defineProperties(file, {\n              webkitRelativePath: {\n                writable: false\n              }\n            });\n          }\n          callback([file]);\n        }\n      });\n    } else if (item.isDirectory) {\n      loopFiles(item, function (entries) {\n        entries.forEach(function (entryItem) {\n          _traverseFileTree(entryItem, \"\".concat(path).concat(item.name, \"/\"));\n        });\n      });\n    }\n  };\n  files.forEach(function (file) {\n    _traverseFileTree(file.webkitGetAsEntry());\n  });\n};\nexport default traverseFileTree;", "map": {"version": 3, "names": ["loopFiles", "item", "callback", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "fileList", "sequence", "readEntries", "entries", "entryList", "Array", "prototype", "slice", "apply", "concat", "isFinished", "length", "traverseFileTree", "files", "isAccepted", "_traverseFileTree", "path", "isFile", "file", "fullPath", "webkitRelativePath", "Object", "defineProperties", "writable", "replace", "isDirectory", "for<PERSON>ach", "entryItem", "name", "webkitGetAsEntry"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-upload/es/traverseFileTree.js"], "sourcesContent": ["function loopFiles(item, callback) {\n  var dirReader = item.createReader();\n  var fileList = [];\n\n  function sequence() {\n    dirReader.readEntries(function (entries) {\n      var entryList = Array.prototype.slice.apply(entries);\n      fileList = fileList.concat(entryList); // Check if all the file has been viewed\n\n      var isFinished = !entryList.length;\n\n      if (isFinished) {\n        callback(fileList);\n      } else {\n        sequence();\n      }\n    });\n  }\n\n  sequence();\n}\n\nvar traverseFileTree = function traverseFileTree(files, callback, isAccepted) {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  var _traverseFileTree = function _traverseFileTree(item, path) {\n    // eslint-disable-next-line no-param-reassign\n    item.path = path || '';\n\n    if (item.isFile) {\n      item.file(function (file) {\n        if (isAccepted(file)) {\n          // https://github.com/ant-design/ant-design/issues/16426\n          if (item.fullPath && !file.webkitRelativePath) {\n            Object.defineProperties(file, {\n              webkitRelativePath: {\n                writable: true\n              }\n            }); // eslint-disable-next-line no-param-reassign\n\n            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');\n            Object.defineProperties(file, {\n              webkitRelativePath: {\n                writable: false\n              }\n            });\n          }\n\n          callback([file]);\n        }\n      });\n    } else if (item.isDirectory) {\n      loopFiles(item, function (entries) {\n        entries.forEach(function (entryItem) {\n          _traverseFileTree(entryItem, \"\".concat(path).concat(item.name, \"/\"));\n        });\n      });\n    }\n  };\n\n  files.forEach(function (file) {\n    _traverseFileTree(file.webkitGetAsEntry());\n  });\n};\n\nexport default traverseFileTree;"], "mappings": "AAAA,SAASA,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACjC,IAAIC,SAAS,GAAGF,IAAI,CAACG,YAAY,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAG,EAAE;EAEjB,SAASC,QAAQA,CAAA,EAAG;IAClBH,SAAS,CAACI,WAAW,CAAC,UAAUC,OAAO,EAAE;MACvC,IAAIC,SAAS,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,KAAK,CAACL,OAAO,CAAC;MACpDH,QAAQ,GAAGA,QAAQ,CAACS,MAAM,CAACL,SAAS,CAAC,CAAC,CAAC;;MAEvC,IAAIM,UAAU,GAAG,CAACN,SAAS,CAACO,MAAM;MAElC,IAAID,UAAU,EAAE;QACdb,QAAQ,CAACG,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLC,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ;EAEAA,QAAQ,CAAC,CAAC;AACZ;AAEA,IAAIW,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEhB,QAAQ,EAAEiB,UAAU,EAAE;EAC5E;EACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACnB,IAAI,EAAEoB,IAAI,EAAE;IAC7D;IACApB,IAAI,CAACoB,IAAI,GAAGA,IAAI,IAAI,EAAE;IAEtB,IAAIpB,IAAI,CAACqB,MAAM,EAAE;MACfrB,IAAI,CAACsB,IAAI,CAAC,UAAUA,IAAI,EAAE;QACxB,IAAIJ,UAAU,CAACI,IAAI,CAAC,EAAE;UACpB;UACA,IAAItB,IAAI,CAACuB,QAAQ,IAAI,CAACD,IAAI,CAACE,kBAAkB,EAAE;YAC7CC,MAAM,CAACC,gBAAgB,CAACJ,IAAI,EAAE;cAC5BE,kBAAkB,EAAE;gBAClBG,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC,CAAC,CAAC;;YAEJL,IAAI,CAACE,kBAAkB,GAAGxB,IAAI,CAACuB,QAAQ,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;YAC1DH,MAAM,CAACC,gBAAgB,CAACJ,IAAI,EAAE;cAC5BE,kBAAkB,EAAE;gBAClBG,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC;UACJ;UAEA1B,QAAQ,CAAC,CAACqB,IAAI,CAAC,CAAC;QAClB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAItB,IAAI,CAAC6B,WAAW,EAAE;MAC3B9B,SAAS,CAACC,IAAI,EAAE,UAAUO,OAAO,EAAE;QACjCA,OAAO,CAACuB,OAAO,CAAC,UAAUC,SAAS,EAAE;UACnCZ,iBAAiB,CAACY,SAAS,EAAE,EAAE,CAAClB,MAAM,CAACO,IAAI,CAAC,CAACP,MAAM,CAACb,IAAI,CAACgC,IAAI,EAAE,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EAEDf,KAAK,CAACa,OAAO,CAAC,UAAUR,IAAI,EAAE;IAC5BH,iBAAiB,CAACG,IAAI,CAACW,gBAAgB,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;AACJ,CAAC;AAED,eAAejB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}