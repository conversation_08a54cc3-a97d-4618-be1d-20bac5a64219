{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(function () {\n    var support = !!propConfig;\n    return [support, _extends(_extends({}, templateConfig), support && _typeof(propConfig) === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}", "map": {"version": 3, "names": ["_typeof", "_extends", "React", "useMergedConfig", "propConfig", "templateConfig", "useMemo", "support"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/hooks/useMergedConfig.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(function () {\n    var support = !!propConfig;\n    return [support, _extends(_extends({}, templateConfig), support && _typeof(propConfig) === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAEC,cAAc,EAAE;EAClE,OAAOH,KAAK,CAACI,OAAO,CAAC,YAAY;IAC/B,IAAIC,OAAO,GAAG,CAAC,CAACH,UAAU;IAC1B,OAAO,CAACG,OAAO,EAAEN,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEI,cAAc,CAAC,EAAEE,OAAO,IAAIP,OAAO,CAACI,UAAU,CAAC,KAAK,QAAQ,GAAGA,UAAU,GAAG,IAAI,CAAC,CAAC;EAC3H,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}