{"ast": null, "code": "export const stopLoading = () => {\n  var stopLoading = document.getElementById(\"pleaseWait\");\n  stopLoading.classList.add(\"fade-out-bck\");\n  setTimeout(function () {\n    stopLoading.classList.add(\"d-none\");\n  }, 1000);\n};", "map": {"version": 3, "names": ["stopLoading", "document", "getElementById", "classList", "add", "setTimeout"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/stopLoading.jsx"], "sourcesContent": ["export const stopLoading = () => {\n\n    var stopLoading = document.getElementById(\"pleaseWait\");\n    stopLoading.classList.add(\"fade-out-bck\");\n    setTimeout(function () { stopLoading.classList.add(\"d-none\"); }, 1000);\n\n}"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAGA,CAAA,KAAM;EAE7B,IAAIA,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;EACvDF,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;EACzCC,UAAU,CAAC,YAAY;IAAEL,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAE1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}