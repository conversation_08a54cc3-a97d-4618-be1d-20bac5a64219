export default UnminifiedJavaScript;
/**
 * @fileOverview Estimates minification savings by determining the ratio of parseable JS tokens to the
 * length of the entire string. Though simple, this method is quite accurate at identifying whether
 * a script was already minified and offers a relatively conservative minification estimate (our two
 * primary goals).
 *
 * This audit only examines scripts that were independent network requests and not inlined or eval'd.
 *
 * See https://github.com/GoogleChrome/lighthouse/pull/3950#issue-277887798 for stats on accuracy.
 */
declare class UnminifiedJavaScript extends ByteEfficiencyAudit {
    /**
     * @param {string} scriptContent
     * @param {string} displayUrl
     * @param {LH.Artifacts.NetworkRequest|undefined} networkRecord
     * @return {{url: string, totalBytes: number, wastedBytes: number, wastedPercent: number}}
     */
    static computeWaste(scriptContent: string, displayUrl: string, networkRecord: LH.Artifacts.NetworkRequest | undefined): {
        url: string;
        totalBytes: number;
        wastedBytes: number;
        wastedPercent: number;
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {import('./byte-efficiency-audit.js').ByteEfficiencyProduct}
     */
    static audit_(artifacts: LH.Artifacts, networkRecords: Array<LH.Artifacts.NetworkRequest>): import('./byte-efficiency-audit.js').ByteEfficiencyProduct;
}
export namespace UIStrings {
    const title: string;
    const description: string;
}
import { ByteEfficiencyAudit } from "./byte-efficiency-audit.js";
//# sourceMappingURL=unminified-javascript.d.ts.map