{"version": 3, "file": "resolve-export.js", "sourceRoot": "", "sources": ["../../src/resolve-export.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,aAAa,GAAG,CAC3B,GAAQ,EACR,UAAoB,EACO,EAAE;IAC7B,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,GAAG,CAAA;IACvC,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,SAAS,CAAA;IAC7C,IAAI,GAAG,KAAK,IAAI;QAAE,OAAO,GAAG,CAAA;IAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;YACpB,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;gBAAE,OAAO,CAAC,CAAA;QAC/B,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IACD,MAAM,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;IAChD,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACzC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;gBAAE,OAAO,CAAC,CAAA;QAC/B,CAAC;IACH,CAAC;AACH,CAAC,CAAA", "sourcesContent": ["export const resolveExport = (\n  exp: any,\n  conditions: string[],\n): string | undefined | null => {\n  if (typeof exp === 'string') return exp\n  if (typeof exp !== 'object') return undefined\n  if (exp === null) return exp\n  if (Array.isArray(exp)) {\n    for (const e of exp) {\n      const u = resolveExport(e, conditions)\n      if (u || u === null) return u\n    }\n    return undefined\n  }\n  const conds = [...conditions, 'node', 'default']\n  for (const [c, e] of Object.entries(exp)) {\n    if (conds.includes(c)) {\n      const u = resolveExport(e, conditions)\n      if (u || u === null) return u\n    }\n  }\n}\n"]}