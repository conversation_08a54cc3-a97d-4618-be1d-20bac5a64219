{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Audio = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar Audio = function Audio(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    height: props.height,\n    width: props.width,\n    fill: props.color,\n    viewBox: \"0 0 55 80\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"matrix(1 0 0 -1 0 80)\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    width: \"10\",\n    height: \"20\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"4.3s\",\n    values: \"20;45;57;80;64;32;66;45;64;23;66;13;64;56;34;34;2;23;76;79;20\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    x: \"15\",\n    width: \"10\",\n    height: \"80\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"2s\",\n    values: \"80;55;33;5;75;23;73;33;12;14;60;80\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    x: \"30\",\n    width: \"10\",\n    height: \"50\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"1.4s\",\n    values: \"50;34;78;23;56;23;34;76;80;54;21;50\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    x: \"45\",\n    width: \"10\",\n    height: \"30\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"2s\",\n    values: \"30;45;13;80;56;72;45;76;34;23;67;30\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }))));\n};\nexports.Audio = Audio;\nAudio.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string\n};\nAudio.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Audio", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "height", "width", "fill", "color", "viewBox", "xmlns", "label", "transform", "rx", "attributeName", "begin", "dur", "values", "calcMode", "repeatCount", "x", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/Audio.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Audio = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar Audio = function Audio(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    height: props.height,\n    width: props.width,\n    fill: props.color,\n    viewBox: \"0 0 55 80\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"matrix(1 0 0 -1 0 80)\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    width: \"10\",\n    height: \"20\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"4.3s\",\n    values: \"20;45;57;80;64;32;66;45;64;23;66;13;64;56;34;34;2;23;76;79;20\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    x: \"15\",\n    width: \"10\",\n    height: \"80\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"2s\",\n    values: \"80;55;33;5;75;23;73;33;12;14;60;80\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    x: \"30\",\n    width: \"10\",\n    height: \"50\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"1.4s\",\n    values: \"50;34;78;23;56;23;34;76;80;54;21;50\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"rect\", {\n    x: \"45\",\n    width: \"10\",\n    height: \"30\",\n    rx: \"3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"height\",\n    begin: \"0s\",\n    dur: \"2s\",\n    values: \"30;45;13;80;56;72;45;76;34;23;67;30\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }))));\n};\n\nexports.Audio = Audio;\nAudio.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string\n};\nAudio.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AAEtB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,KAAK,GAAG,SAASA,KAAKA,CAACO,KAAK,EAAE;EAChC,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,MAAM,EAAEF,KAAK,CAACE,MAAM;IACpBC,KAAK,EAAEH,KAAK,CAACG,KAAK;IAClBC,IAAI,EAAEJ,KAAK,CAACK,KAAK;IACjBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnC,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDQ,SAAS,EAAE;EACb,CAAC,EAAE,aAAaf,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACtDE,KAAK,EAAE,IAAI;IACXD,MAAM,EAAE,IAAI;IACZQ,EAAE,EAAE;EACN,CAAC,EAAE,aAAahB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDU,aAAa,EAAE,QAAQ;IACvBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,+DAA+D;IACvEC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACxDgB,CAAC,EAAE,IAAI;IACPd,KAAK,EAAE,IAAI;IACXD,MAAM,EAAE,IAAI;IACZQ,EAAE,EAAE;EACN,CAAC,EAAE,aAAahB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDU,aAAa,EAAE,QAAQ;IACvBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,oCAAoC;IAC5CC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACxDgB,CAAC,EAAE,IAAI;IACPd,KAAK,EAAE,IAAI;IACXD,MAAM,EAAE,IAAI;IACZQ,EAAE,EAAE;EACN,CAAC,EAAE,aAAahB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDU,aAAa,EAAE,QAAQ;IACvBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,qCAAqC;IAC7CC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACxDgB,CAAC,EAAE,IAAI;IACPd,KAAK,EAAE,IAAI;IACXD,MAAM,EAAE,IAAI;IACZQ,EAAE,EAAE;EACN,CAAC,EAAE,aAAahB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDU,aAAa,EAAE,QAAQ;IACvBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,qCAAqC;IAC7CC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAEDzB,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrBA,KAAK,CAACyB,SAAS,GAAG;EAChBhB,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACsB,SAAS,CAAC,CAACtB,UAAU,CAAC,SAAS,CAAC,CAACuB,MAAM,EAAEvB,UAAU,CAAC,SAAS,CAAC,CAACwB,MAAM,CAAC,CAAC;EACrGlB,KAAK,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACsB,SAAS,CAAC,CAACtB,UAAU,CAAC,SAAS,CAAC,CAACuB,MAAM,EAAEvB,UAAU,CAAC,SAAS,CAAC,CAACwB,MAAM,CAAC,CAAC;EACpGhB,KAAK,EAAER,UAAU,CAAC,SAAS,CAAC,CAACuB,MAAM;EACnCZ,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACuB;AAC/B,CAAC;AACD3B,KAAK,CAAC6B,YAAY,GAAG;EACnBpB,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTE,KAAK,EAAE,OAAO;EACdG,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}