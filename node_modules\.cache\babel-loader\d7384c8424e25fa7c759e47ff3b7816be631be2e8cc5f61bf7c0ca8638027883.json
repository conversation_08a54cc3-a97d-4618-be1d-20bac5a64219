{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\marketplaceUffAcq.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* marketplaceUfficioAcquisti - Marketplace ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { affiliato } from '../../components/route';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Nav from \"../../components/navigation/Nav\";\nimport '../../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass marketplaceUffAcq extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    this.state = {\n      role: localStorage.getItem('role')\n    };\n  }\n  componentDidMount() {\n    window.sessionStorage.setItem('affForOrd', this.state.role === affiliato ? true : false);\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {\n        disabled: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Ordina\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MarketplaceGen, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default marketplaceUffAcq;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "affiliato", "MarketplaceGen", "Nav", "jsxDEV", "_jsxDEV", "marketplaceUffAcq", "constructor", "props", "state", "role", "localStorage", "getItem", "componentDidMount", "window", "sessionStorage", "setItem", "render", "className", "children", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Ordina"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/marketplaceUffAcq.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* marketplaceUfficioAcquisti - Marketplace ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { affiliato } from '../../components/route';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Nav from \"../../components/navigation/Nav\";\nimport '../../css/DataTableDemo.css';\n\nclass marketplaceUffAcq extends Component {\n    constructor(props) {\n        super(props);\n        //Dichiarazione variabili di scena\n        this.state = {\n            role: localStorage.getItem('role')\n        };\n    }\n    componentDidMount() {\n        window.sessionStorage.setItem('affForOrd', this.state.role === affiliato ? true : false)\n    }\n    render() {\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente NavAgente contiene l'header ed il menù di navigazione */}\n                <Nav disabled={true} />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.Ordina}</h1>\n                </div>\n                <MarketplaceGen />\n            </div>\n        );\n    }\n}\n\nexport default marketplaceUffAcq;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAOC,cAAc,MAAM,2DAA2D;AACtF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,iBAAiB,SAASP,SAAS,CAAC;EACtCQ,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IACA,IAAI,CAACC,KAAK,GAAG;MACTC,IAAI,EAAEC,YAAY,CAACC,OAAO,CAAC,MAAM;IACrC,CAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChBC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,CAACP,KAAK,CAACC,IAAI,KAAKT,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;EAC5F;EACAgB,MAAMA,CAAA,EAAG;IACL,oBACIZ,OAAA;MAAKa,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE9Cd,OAAA,CAACF,GAAG;QAACiB,QAAQ,EAAE;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBnB,OAAA;QAAKa,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACnCd,OAAA;UAAAc,QAAA,EAAKnB,QAAQ,CAACyB;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACNnB,OAAA,CAACH,cAAc;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEd;AACJ;AAEA,eAAelB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}