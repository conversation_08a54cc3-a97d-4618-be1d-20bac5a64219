{"ast": null, "code": "var global = require('../internals/global');\nvar setGlobal = require('../internals/set-global');\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\nmodule.exports = store;", "map": {"version": 3, "names": ["global", "require", "setGlobal", "SHARED", "store", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/shared-store.js"], "sourcesContent": ["var global = require('../internals/global');\nvar setGlobal = require('../internals/set-global');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\n\nmodule.exports = store;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,SAAS,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAElD,IAAIE,MAAM,GAAG,oBAAoB;AACjC,IAAIC,KAAK,GAAGJ,MAAM,CAACG,MAAM,CAAC,IAAID,SAAS,CAACC,MAAM,EAAE,CAAC,CAAC,CAAC;AAEnDE,MAAM,CAACC,OAAO,GAAGF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}