{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport get from './get';\nfunction internalSet(entity, paths, value, removeIfUndefined) {\n  if (!paths.length) {\n    return value;\n  }\n  var _paths = _toArray(paths),\n    path = _paths[0],\n    restPath = _paths.slice(1);\n  var clone;\n  if (!entity && typeof path === 'number') {\n    clone = [];\n  } else if (Array.isArray(entity)) {\n    clone = _toConsumableArray(entity);\n  } else {\n    clone = _objectSpread({}, entity);\n  } // Delete prop if `removeIfUndefined` and value is undefined\n\n  if (removeIfUndefined && value === undefined && restPath.length === 1) {\n    delete clone[path][restPath[0]];\n  } else {\n    clone[path] = internalSet(clone[path], restPath, value, removeIfUndefined);\n  }\n  return clone;\n}\nexport default function set(entity, paths, value) {\n  var removeIfUndefined = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n  // Do nothing if `removeIfUndefined` and parent object not exist\n  if (paths.length && removeIfUndefined && value === undefined && !get(entity, paths.slice(0, -1))) {\n    return entity;\n  }\n  return internalSet(entity, paths, value, removeIfUndefined);\n}", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "_toArray", "get", "internalSet", "entity", "paths", "value", "removeIfUndefined", "length", "_paths", "path", "restPath", "slice", "clone", "Array", "isArray", "undefined", "set", "arguments"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/utils/set.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport get from './get';\n\nfunction internalSet(entity, paths, value, removeIfUndefined) {\n  if (!paths.length) {\n    return value;\n  }\n\n  var _paths = _toArray(paths),\n      path = _paths[0],\n      restPath = _paths.slice(1);\n\n  var clone;\n\n  if (!entity && typeof path === 'number') {\n    clone = [];\n  } else if (Array.isArray(entity)) {\n    clone = _toConsumableArray(entity);\n  } else {\n    clone = _objectSpread({}, entity);\n  } // Delete prop if `removeIfUndefined` and value is undefined\n\n\n  if (removeIfUndefined && value === undefined && restPath.length === 1) {\n    delete clone[path][restPath[0]];\n  } else {\n    clone[path] = internalSet(clone[path], restPath, value, removeIfUndefined);\n  }\n\n  return clone;\n}\n\nexport default function set(entity, paths, value) {\n  var removeIfUndefined = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n  // Do nothing if `removeIfUndefined` and parent object not exist\n  if (paths.length && removeIfUndefined && value === undefined && !get(entity, paths.slice(0, -1))) {\n    return entity;\n  }\n\n  return internalSet(entity, paths, value, removeIfUndefined);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,GAAG,MAAM,OAAO;AAEvB,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;EAC5D,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;IACjB,OAAOF,KAAK;EACd;EAEA,IAAIG,MAAM,GAAGR,QAAQ,CAACI,KAAK,CAAC;IACxBK,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;IAChBE,QAAQ,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;EAE9B,IAAIC,KAAK;EAET,IAAI,CAACT,MAAM,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;IACvCG,KAAK,GAAG,EAAE;EACZ,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACX,MAAM,CAAC,EAAE;IAChCS,KAAK,GAAGb,kBAAkB,CAACI,MAAM,CAAC;EACpC,CAAC,MAAM;IACLS,KAAK,GAAGd,aAAa,CAAC,CAAC,CAAC,EAAEK,MAAM,CAAC;EACnC,CAAC,CAAC;;EAGF,IAAIG,iBAAiB,IAAID,KAAK,KAAKU,SAAS,IAAIL,QAAQ,CAACH,MAAM,KAAK,CAAC,EAAE;IACrE,OAAOK,KAAK,CAACH,IAAI,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,MAAM;IACLE,KAAK,CAACH,IAAI,CAAC,GAAGP,WAAW,CAACU,KAAK,CAACH,IAAI,CAAC,EAAEC,QAAQ,EAAEL,KAAK,EAAEC,iBAAiB,CAAC;EAC5E;EAEA,OAAOM,KAAK;AACd;AAEA,eAAe,SAASI,GAAGA,CAACb,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAChD,IAAIC,iBAAiB,GAAGW,SAAS,CAACV,MAAM,GAAG,CAAC,IAAIU,SAAS,CAAC,CAAC,CAAC,KAAKF,SAAS,GAAGE,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;;EAEjG;EACA,IAAIb,KAAK,CAACG,MAAM,IAAID,iBAAiB,IAAID,KAAK,KAAKU,SAAS,IAAI,CAACd,GAAG,CAACE,MAAM,EAAEC,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAChG,OAAOR,MAAM;EACf;EAEA,OAAOD,WAAW,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}