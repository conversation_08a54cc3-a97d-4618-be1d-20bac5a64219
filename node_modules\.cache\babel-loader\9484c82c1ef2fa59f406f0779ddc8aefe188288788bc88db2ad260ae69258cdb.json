{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { STATUS_APPEAR, STATUS_NONE, STATUS_LEAVE, STATUS_ENTER, STEP_PREPARE, STEP_START, STEP_ACTIVE } from '../interface';\nimport useStepQueue, { DoStep, SkipStep, isActive } from './useStepQueue';\nimport useDomMotionEvents from './useDomMotionEvents';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useState3 = useState(STATUS_NONE),\n    _useState4 = _slicedToArray(_useState3, 2),\n    status = _useState4[0],\n    setStatus = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    style = _useState6[0],\n    setStyle = _useState6[1];\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null); // =========================== Dom Node ===========================\n\n  function getDomElement() {\n    return getElement();\n  } // ========================== Motion End ==========================\n\n  var activeRef = useRef(false);\n  function onInternalMotionEnd(event) {\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    } // Only update status when `canEnd` and not destroyed\n\n    if (status !== STATUS_NONE && currentActive && canEnd !== false) {\n      setStatus(STATUS_NONE, true);\n      setStyle(null, true);\n    }\n  }\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n    _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0]; // ============================= Step =============================\n\n  var eventHandlers = React.useMemo(function () {\n    var _ref2, _ref3, _ref4;\n    switch (status) {\n      case STATUS_APPEAR:\n        return _ref2 = {}, _defineProperty(_ref2, STEP_PREPARE, onAppearPrepare), _defineProperty(_ref2, STEP_START, onAppearStart), _defineProperty(_ref2, STEP_ACTIVE, onAppearActive), _ref2;\n      case STATUS_ENTER:\n        return _ref3 = {}, _defineProperty(_ref3, STEP_PREPARE, onEnterPrepare), _defineProperty(_ref3, STEP_START, onEnterStart), _defineProperty(_ref3, STEP_ACTIVE, onEnterActive), _ref3;\n      case STATUS_LEAVE:\n        return _ref4 = {}, _defineProperty(_ref4, STEP_PREPARE, onLeavePrepare), _defineProperty(_ref4, STEP_START, onLeaveStart), _defineProperty(_ref4, STEP_ACTIVE, onLeaveActive), _ref4;\n      default:\n        return {};\n    }\n  }, [status]);\n  var _useStepQueue = useStepQueue(status, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === STEP_PREPARE) {\n        var onPrepare = eventHandlers[STEP_PREPARE];\n        if (!onPrepare) {\n          return SkipStep;\n        }\n        return onPrepare(getDomElement());\n      } // Rest step is sync update\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === STEP_ACTIVE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      return DoStep;\n    }),\n    _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = isActive(step);\n  activeRef.current = active; // ============================ Status ============================\n  // Update with new status\n\n  useIsomorphicLayoutEffect(function () {\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n    if (!supportMotion) {\n      return;\n    }\n    var nextStatus; // Appear\n\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    } // Enter\n\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    } // Leave\n\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    } // Update to next status\n\n    if (nextStatus) {\n      setStatus(nextStatus);\n      startStep();\n    }\n  }, [visible]); // ============================ Effect ============================\n  // Reset when motion changed\n\n  useEffect(function () {\n    if (\n    // Cancel appear\n    status === STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    status === STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    status === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []); // Trigger `onVisibleChanged`\n\n  useEffect(function () {\n    if (asyncVisible !== undefined && status === STATUS_NONE) {\n      onVisibleChanged === null || onVisibleChanged === void 0 ? void 0 : onVisibleChanged(asyncVisible);\n    }\n  }, [asyncVisible, status]); // ============================ Styles ============================\n\n  var mergedStyle = style;\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [status, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_slicedToArray", "React", "useRef", "useEffect", "useState", "STATUS_APPEAR", "STATUS_NONE", "STATUS_LEAVE", "STATUS_ENTER", "STEP_PREPARE", "STEP_START", "STEP_ACTIVE", "useStepQueue", "DoStep", "SkipStep", "isActive", "useDomMotionEvents", "useIsomorphicLayoutEffect", "useStatus", "supportMotion", "visible", "getElement", "_ref", "_ref$motionEnter", "motionEnter", "_ref$motionAppear", "motionAppear", "_ref$motionLeave", "motionLeave", "motionDeadline", "motionLeaveImmediately", "onAppearPrepare", "onEnterPrepare", "onLeavePrepare", "onAppearStart", "onEnterStart", "onLeaveStart", "onAppearActive", "onEnterActive", "onLeaveActive", "onAppearEnd", "onEnterEnd", "onLeaveEnd", "onVisibleChanged", "_useState", "_useState2", "asyncVisible", "setAsyncVisible", "_useState3", "_useState4", "status", "setStatus", "_useState5", "_useState6", "style", "setStyle", "mountedRef", "deadlineRef", "getDomElement", "activeRef", "onInternalMotionEnd", "event", "element", "deadline", "target", "currentActive", "current", "canEnd", "_useDomMotionEvents", "_useDomMotionEvents2", "patchMotionEvents", "eventHandlers", "useMemo", "_ref2", "_ref3", "_ref4", "_useStepQueue", "newStep", "onPrepare", "step", "_eventHandlers$step", "call", "clearTimeout", "setTimeout", "_useStepQueue2", "startStep", "active", "isMounted", "nextStatus", "undefined", "mergedStyle", "transition"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/hooks/useStatus.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { STATUS_APPEAR, STATUS_NONE, STATUS_LEAVE, STATUS_ENTER, STEP_PREPARE, STEP_START, STEP_ACTIVE } from '../interface';\nimport useStepQueue, { DoStep, SkipStep, isActive } from './useStepQueue';\nimport useDomMotionEvents from './useDomMotionEvents';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n      motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n      _ref$motionAppear = _ref.motionAppear,\n      motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n      _ref$motionLeave = _ref.motionLeave,\n      motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n      motionDeadline = _ref.motionDeadline,\n      motionLeaveImmediately = _ref.motionLeaveImmediately,\n      onAppearPrepare = _ref.onAppearPrepare,\n      onEnterPrepare = _ref.onEnterPrepare,\n      onLeavePrepare = _ref.onLeavePrepare,\n      onAppearStart = _ref.onAppearStart,\n      onEnterStart = _ref.onEnterStart,\n      onLeaveStart = _ref.onLeaveStart,\n      onAppearActive = _ref.onAppearActive,\n      onEnterActive = _ref.onEnterActive,\n      onLeaveActive = _ref.onLeaveActive,\n      onAppearEnd = _ref.onAppearEnd,\n      onEnterEnd = _ref.onEnterEnd,\n      onLeaveEnd = _ref.onLeaveEnd,\n      onVisibleChanged = _ref.onVisibleChanged;\n\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n      _useState2 = _slicedToArray(_useState, 2),\n      asyncVisible = _useState2[0],\n      setAsyncVisible = _useState2[1];\n\n  var _useState3 = useState(STATUS_NONE),\n      _useState4 = _slicedToArray(_useState3, 2),\n      status = _useState4[0],\n      setStatus = _useState4[1];\n\n  var _useState5 = useState(null),\n      _useState6 = _slicedToArray(_useState5, 2),\n      style = _useState6[0],\n      setStyle = _useState6[1];\n\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null); // =========================== Dom Node ===========================\n\n  function getDomElement() {\n    return getElement();\n  } // ========================== Motion End ==========================\n\n\n  var activeRef = useRef(false);\n\n  function onInternalMotionEnd(event) {\n    var element = getDomElement();\n\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n\n    var currentActive = activeRef.current;\n    var canEnd;\n\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    } // Only update status when `canEnd` and not destroyed\n\n\n    if (status !== STATUS_NONE && currentActive && canEnd !== false) {\n      setStatus(STATUS_NONE, true);\n      setStyle(null, true);\n    }\n  }\n\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n      _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n      patchMotionEvents = _useDomMotionEvents2[0]; // ============================= Step =============================\n\n\n  var eventHandlers = React.useMemo(function () {\n    var _ref2, _ref3, _ref4;\n\n    switch (status) {\n      case STATUS_APPEAR:\n        return _ref2 = {}, _defineProperty(_ref2, STEP_PREPARE, onAppearPrepare), _defineProperty(_ref2, STEP_START, onAppearStart), _defineProperty(_ref2, STEP_ACTIVE, onAppearActive), _ref2;\n\n      case STATUS_ENTER:\n        return _ref3 = {}, _defineProperty(_ref3, STEP_PREPARE, onEnterPrepare), _defineProperty(_ref3, STEP_START, onEnterStart), _defineProperty(_ref3, STEP_ACTIVE, onEnterActive), _ref3;\n\n      case STATUS_LEAVE:\n        return _ref4 = {}, _defineProperty(_ref4, STEP_PREPARE, onLeavePrepare), _defineProperty(_ref4, STEP_START, onLeaveStart), _defineProperty(_ref4, STEP_ACTIVE, onLeaveActive), _ref4;\n\n      default:\n        return {};\n    }\n  }, [status]);\n\n  var _useStepQueue = useStepQueue(status, function (newStep) {\n    // Only prepare step can be skip\n    if (newStep === STEP_PREPARE) {\n      var onPrepare = eventHandlers[STEP_PREPARE];\n\n      if (!onPrepare) {\n        return SkipStep;\n      }\n\n      return onPrepare(getDomElement());\n    } // Rest step is sync update\n\n\n    // Rest step is sync update\n    if (step in eventHandlers) {\n      var _eventHandlers$step;\n\n      setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n    }\n\n    if (step === STEP_ACTIVE) {\n      // Patch events when motion needed\n      patchMotionEvents(getDomElement());\n\n      if (motionDeadline > 0) {\n        clearTimeout(deadlineRef.current);\n        deadlineRef.current = setTimeout(function () {\n          onInternalMotionEnd({\n            deadline: true\n          });\n        }, motionDeadline);\n      }\n    }\n\n    return DoStep;\n  }),\n      _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n      startStep = _useStepQueue2[0],\n      step = _useStepQueue2[1];\n\n  var active = isActive(step);\n  activeRef.current = active; // ============================ Status ============================\n  // Update with new status\n\n  useIsomorphicLayoutEffect(function () {\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    if (!supportMotion) {\n      return;\n    }\n\n    var nextStatus; // Appear\n\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    } // Enter\n\n\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    } // Leave\n\n\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    } // Update to next status\n\n\n    if (nextStatus) {\n      setStatus(nextStatus);\n      startStep();\n    }\n  }, [visible]); // ============================ Effect ============================\n  // Reset when motion changed\n\n  useEffect(function () {\n    if ( // Cancel appear\n    status === STATUS_APPEAR && !motionAppear || // Cancel enter\n    status === STATUS_ENTER && !motionEnter || // Cancel leave\n    status === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []); // Trigger `onVisibleChanged`\n\n  useEffect(function () {\n    if (asyncVisible !== undefined && status === STATUS_NONE) {\n      onVisibleChanged === null || onVisibleChanged === void 0 ? void 0 : onVisibleChanged(asyncVisible);\n    }\n  }, [asyncVisible, status]); // ============================ Styles ============================\n\n  var mergedStyle = style;\n\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n\n  return [status, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,aAAa,EAAEC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,QAAQ,cAAc;AAC5H,OAAOC,YAAY,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACzE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,eAAe,SAASC,SAASA,CAACC,aAAa,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAC1E,IAAIC,gBAAgB,GAAGD,IAAI,CAACE,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACnEE,iBAAiB,GAAGH,IAAI,CAACI,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACtEE,gBAAgB,GAAGL,IAAI,CAACM,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACnEE,cAAc,GAAGP,IAAI,CAACO,cAAc;IACpCC,sBAAsB,GAAGR,IAAI,CAACQ,sBAAsB;IACpDC,eAAe,GAAGT,IAAI,CAACS,eAAe;IACtCC,cAAc,GAAGV,IAAI,CAACU,cAAc;IACpCC,cAAc,GAAGX,IAAI,CAACW,cAAc;IACpCC,aAAa,GAAGZ,IAAI,CAACY,aAAa;IAClCC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCC,YAAY,GAAGd,IAAI,CAACc,YAAY;IAChCC,cAAc,GAAGf,IAAI,CAACe,cAAc;IACpCC,aAAa,GAAGhB,IAAI,CAACgB,aAAa;IAClCC,aAAa,GAAGjB,IAAI,CAACiB,aAAa;IAClCC,WAAW,GAAGlB,IAAI,CAACkB,WAAW;IAC9BC,UAAU,GAAGnB,IAAI,CAACmB,UAAU;IAC5BC,UAAU,GAAGpB,IAAI,CAACoB,UAAU;IAC5BC,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB;;EAE5C;EACA,IAAIC,SAAS,GAAGxC,QAAQ,CAAC,CAAC;IACtByC,UAAU,GAAG7C,cAAc,CAAC4C,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,UAAU,GAAG5C,QAAQ,CAACE,WAAW,CAAC;IAClC2C,UAAU,GAAGjD,cAAc,CAACgD,UAAU,EAAE,CAAC,CAAC;IAC1CE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE7B,IAAIG,UAAU,GAAGhD,QAAQ,CAAC,IAAI,CAAC;IAC3BiD,UAAU,GAAGrD,cAAc,CAACoD,UAAU,EAAE,CAAC,CAAC;IAC1CE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE5B,IAAIG,UAAU,GAAGtD,MAAM,CAAC,KAAK,CAAC;EAC9B,IAAIuD,WAAW,GAAGvD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhC,SAASwD,aAAaA,CAAA,EAAG;IACvB,OAAOrC,UAAU,CAAC,CAAC;EACrB,CAAC,CAAC;;EAGF,IAAIsC,SAAS,GAAGzD,MAAM,CAAC,KAAK,CAAC;EAE7B,SAAS0D,mBAAmBA,CAACC,KAAK,EAAE;IAClC,IAAIC,OAAO,GAAGJ,aAAa,CAAC,CAAC;IAE7B,IAAIG,KAAK,IAAI,CAACA,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,MAAM,KAAKF,OAAO,EAAE;MACxD;MACA;MACA;MACA;IACF;IAEA,IAAIG,aAAa,GAAGN,SAAS,CAACO,OAAO;IACrC,IAAIC,MAAM;IAEV,IAAIjB,MAAM,KAAK7C,aAAa,IAAI4D,aAAa,EAAE;MAC7CE,MAAM,GAAG3B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACsB,OAAO,EAAED,KAAK,CAAC;IAChG,CAAC,MAAM,IAAIX,MAAM,KAAK1C,YAAY,IAAIyD,aAAa,EAAE;MACnDE,MAAM,GAAG1B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACqB,OAAO,EAAED,KAAK,CAAC;IAC7F,CAAC,MAAM,IAAIX,MAAM,KAAK3C,YAAY,IAAI0D,aAAa,EAAE;MACnDE,MAAM,GAAGzB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACoB,OAAO,EAAED,KAAK,CAAC;IAC7F,CAAC,CAAC;;IAGF,IAAIX,MAAM,KAAK5C,WAAW,IAAI2D,aAAa,IAAIE,MAAM,KAAK,KAAK,EAAE;MAC/DhB,SAAS,CAAC7C,WAAW,EAAE,IAAI,CAAC;MAC5BiD,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;IACtB;EACF;EAEA,IAAIa,mBAAmB,GAAGpD,kBAAkB,CAAC4C,mBAAmB,CAAC;IAC7DS,oBAAoB,GAAGrE,cAAc,CAACoE,mBAAmB,EAAE,CAAC,CAAC;IAC7DE,iBAAiB,GAAGD,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGjD,IAAIE,aAAa,GAAGtE,KAAK,CAACuE,OAAO,CAAC,YAAY;IAC5C,IAAIC,KAAK,EAAEC,KAAK,EAAEC,KAAK;IAEvB,QAAQzB,MAAM;MACZ,KAAK7C,aAAa;QAChB,OAAOoE,KAAK,GAAG,CAAC,CAAC,EAAE1E,eAAe,CAAC0E,KAAK,EAAEhE,YAAY,EAAEsB,eAAe,CAAC,EAAEhC,eAAe,CAAC0E,KAAK,EAAE/D,UAAU,EAAEwB,aAAa,CAAC,EAAEnC,eAAe,CAAC0E,KAAK,EAAE9D,WAAW,EAAE0B,cAAc,CAAC,EAAEoC,KAAK;MAEzL,KAAKjE,YAAY;QACf,OAAOkE,KAAK,GAAG,CAAC,CAAC,EAAE3E,eAAe,CAAC2E,KAAK,EAAEjE,YAAY,EAAEuB,cAAc,CAAC,EAAEjC,eAAe,CAAC2E,KAAK,EAAEhE,UAAU,EAAEyB,YAAY,CAAC,EAAEpC,eAAe,CAAC2E,KAAK,EAAE/D,WAAW,EAAE2B,aAAa,CAAC,EAAEoC,KAAK;MAEtL,KAAKnE,YAAY;QACf,OAAOoE,KAAK,GAAG,CAAC,CAAC,EAAE5E,eAAe,CAAC4E,KAAK,EAAElE,YAAY,EAAEwB,cAAc,CAAC,EAAElC,eAAe,CAAC4E,KAAK,EAAEjE,UAAU,EAAE0B,YAAY,CAAC,EAAErC,eAAe,CAAC4E,KAAK,EAAEhE,WAAW,EAAE4B,aAAa,CAAC,EAAEoC,KAAK;MAEtL;QACE,OAAO,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACzB,MAAM,CAAC,CAAC;EAEZ,IAAI0B,aAAa,GAAGhE,YAAY,CAACsC,MAAM,EAAE,UAAU2B,OAAO,EAAE;MAC1D;MACA,IAAIA,OAAO,KAAKpE,YAAY,EAAE;QAC5B,IAAIqE,SAAS,GAAGP,aAAa,CAAC9D,YAAY,CAAC;QAE3C,IAAI,CAACqE,SAAS,EAAE;UACd,OAAOhE,QAAQ;QACjB;QAEA,OAAOgE,SAAS,CAACpB,aAAa,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC;;MAGF;MACA,IAAIqB,IAAI,IAAIR,aAAa,EAAE;QACzB,IAAIS,mBAAmB;QAEvBzB,QAAQ,CAAC,CAAC,CAACyB,mBAAmB,GAAGT,aAAa,CAACQ,IAAI,CAAC,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,IAAI,CAACV,aAAa,EAAEb,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC;MACtL;MAEA,IAAIqB,IAAI,KAAKpE,WAAW,EAAE;QACxB;QACA2D,iBAAiB,CAACZ,aAAa,CAAC,CAAC,CAAC;QAElC,IAAI7B,cAAc,GAAG,CAAC,EAAE;UACtBqD,YAAY,CAACzB,WAAW,CAACS,OAAO,CAAC;UACjCT,WAAW,CAACS,OAAO,GAAGiB,UAAU,CAAC,YAAY;YAC3CvB,mBAAmB,CAAC;cAClBG,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAC,EAAElC,cAAc,CAAC;QACpB;MACF;MAEA,OAAOhB,MAAM;IACf,CAAC,CAAC;IACEuE,cAAc,GAAGpF,cAAc,CAAC4E,aAAa,EAAE,CAAC,CAAC;IACjDS,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7BL,IAAI,GAAGK,cAAc,CAAC,CAAC,CAAC;EAE5B,IAAIE,MAAM,GAAGvE,QAAQ,CAACgE,IAAI,CAAC;EAC3BpB,SAAS,CAACO,OAAO,GAAGoB,MAAM,CAAC,CAAC;EAC5B;;EAEArE,yBAAyB,CAAC,YAAY;IACpC8B,eAAe,CAAC3B,OAAO,CAAC;IACxB,IAAImE,SAAS,GAAG/B,UAAU,CAACU,OAAO;IAClCV,UAAU,CAACU,OAAO,GAAG,IAAI;IAEzB,IAAI,CAAC/C,aAAa,EAAE;MAClB;IACF;IAEA,IAAIqE,UAAU,CAAC,CAAC;;IAEhB,IAAI,CAACD,SAAS,IAAInE,OAAO,IAAIM,YAAY,EAAE;MACzC8D,UAAU,GAAGnF,aAAa;IAC5B,CAAC,CAAC;;IAGF,IAAIkF,SAAS,IAAInE,OAAO,IAAII,WAAW,EAAE;MACvCgE,UAAU,GAAGhF,YAAY;IAC3B,CAAC,CAAC;;IAGF,IAAI+E,SAAS,IAAI,CAACnE,OAAO,IAAIQ,WAAW,IAAI,CAAC2D,SAAS,IAAIzD,sBAAsB,IAAI,CAACV,OAAO,IAAIQ,WAAW,EAAE;MAC3G4D,UAAU,GAAGjF,YAAY;IAC3B,CAAC,CAAC;;IAGF,IAAIiF,UAAU,EAAE;MACdrC,SAAS,CAACqC,UAAU,CAAC;MACrBH,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACjE,OAAO,CAAC,CAAC,CAAC,CAAC;EACf;;EAEAjB,SAAS,CAAC,YAAY;IACpB;IAAK;IACL+C,MAAM,KAAK7C,aAAa,IAAI,CAACqB,YAAY;IAAI;IAC7CwB,MAAM,KAAK1C,YAAY,IAAI,CAACgB,WAAW;IAAI;IAC3C0B,MAAM,KAAK3C,YAAY,IAAI,CAACqB,WAAW,EAAE;MACvCuB,SAAS,CAAC7C,WAAW,CAAC;IACxB;EACF,CAAC,EAAE,CAACoB,YAAY,EAAEF,WAAW,EAAEI,WAAW,CAAC,CAAC;EAC5CzB,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBqD,UAAU,CAACU,OAAO,GAAG,KAAK;MAC1BgB,YAAY,CAACzB,WAAW,CAACS,OAAO,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER/D,SAAS,CAAC,YAAY;IACpB,IAAI2C,YAAY,KAAK2C,SAAS,IAAIvC,MAAM,KAAK5C,WAAW,EAAE;MACxDqC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACG,YAAY,CAAC;IACpG;EACF,CAAC,EAAE,CAACA,YAAY,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE5B,IAAIwC,WAAW,GAAGpC,KAAK;EAEvB,IAAIiB,aAAa,CAAC9D,YAAY,CAAC,IAAIsE,IAAI,KAAKrE,UAAU,EAAE;IACtDgF,WAAW,GAAG5F,aAAa,CAAC;MAC1B6F,UAAU,EAAE;IACd,CAAC,EAAED,WAAW,CAAC;EACjB;EAEA,OAAO,CAACxC,MAAM,EAAE6B,IAAI,EAAEW,WAAW,EAAE5C,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG1B,OAAO,CAAC;AAC/G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}