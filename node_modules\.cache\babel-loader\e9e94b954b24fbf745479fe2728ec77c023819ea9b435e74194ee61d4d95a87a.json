{"ast": null, "code": "import * as React from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nexport default function getIcons(_ref) {\n  var suffixIcon = _ref.suffixIcon,\n    clearIcon = _ref.clearIcon,\n    menuItemSelectedIcon = _ref.menuItemSelectedIcon,\n    removeIcon = _ref.removeIcon,\n    loading = _ref.loading,\n    multiple = _ref.multiple,\n    hasFeedback = _ref.hasFeedback,\n    prefixCls = _ref.prefixCls,\n    showArrow = _ref.showArrow,\n    feedbackIcon = _ref.feedbackIcon;\n  // Clear Icon\n  var mergedClearIcon = clearIcon;\n  if (!clearIcon) {\n    mergedClearIcon = /*#__PURE__*/React.createElement(CloseCircleFilled, null);\n  } // Validation Feedback Icon\n\n  var getSuffixIconNode = function getSuffixIconNode(arrowIcon) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, showArrow !== false && arrowIcon, hasFeedback && feedbackIcon);\n  }; // Arrow item icon\n\n  var mergedSuffixIcon = null;\n  if (suffixIcon !== undefined) {\n    mergedSuffixIcon = getSuffixIconNode(suffixIcon);\n  } else if (loading) {\n    mergedSuffixIcon = getSuffixIconNode(/*#__PURE__*/React.createElement(LoadingOutlined, {\n      spin: true\n    }));\n  } else {\n    var iconCls = \"\".concat(prefixCls, \"-suffix\");\n    mergedSuffixIcon = function mergedSuffixIcon(_ref2) {\n      var open = _ref2.open,\n        showSearch = _ref2.showSearch;\n      if (open && showSearch) {\n        return getSuffixIconNode(/*#__PURE__*/React.createElement(SearchOutlined, {\n          className: iconCls\n        }));\n      }\n      return getSuffixIconNode(/*#__PURE__*/React.createElement(DownOutlined, {\n        className: iconCls\n      }));\n    };\n  } // Checked item icon\n\n  var mergedItemIcon = null;\n  if (menuItemSelectedIcon !== undefined) {\n    mergedItemIcon = menuItemSelectedIcon;\n  } else if (multiple) {\n    mergedItemIcon = /*#__PURE__*/React.createElement(CheckOutlined, null);\n  } else {\n    mergedItemIcon = null;\n  }\n  var mergedRemoveIcon = null;\n  if (removeIcon !== undefined) {\n    mergedRemoveIcon = removeIcon;\n  } else {\n    mergedRemoveIcon = /*#__PURE__*/React.createElement(CloseOutlined, null);\n  }\n  return {\n    clearIcon: mergedClearIcon,\n    suffixIcon: mergedSuffixIcon,\n    itemIcon: mergedItemIcon,\n    removeIcon: mergedRemoveIcon\n  };\n}", "map": {"version": 3, "names": ["React", "DownOutlined", "LoadingOutlined", "CheckOutlined", "CloseOutlined", "CloseCircleFilled", "SearchOutlined", "getIcons", "_ref", "suffixIcon", "clearIcon", "menuItemSelectedIcon", "removeIcon", "loading", "multiple", "hasFeedback", "prefixCls", "showArrow", "feedbackIcon", "mergedClearIcon", "createElement", "getSuffixIconNode", "arrowIcon", "Fragment", "mergedSuffixIcon", "undefined", "spin", "iconCls", "concat", "_ref2", "open", "showSearch", "className", "mergedItemIcon", "mergedRemoveIcon", "itemIcon"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/select/utils/iconUtil.js"], "sourcesContent": ["import * as React from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nexport default function getIcons(_ref) {\n  var suffixIcon = _ref.suffixIcon,\n      clearIcon = _ref.clearIcon,\n      menuItemSelectedIcon = _ref.menuItemSelectedIcon,\n      removeIcon = _ref.removeIcon,\n      loading = _ref.loading,\n      multiple = _ref.multiple,\n      hasFeedback = _ref.hasFeedback,\n      prefixCls = _ref.prefixCls,\n      showArrow = _ref.showArrow,\n      feedbackIcon = _ref.feedbackIcon;\n  // Clear Icon\n  var mergedClearIcon = clearIcon;\n\n  if (!clearIcon) {\n    mergedClearIcon = /*#__PURE__*/React.createElement(CloseCircleFilled, null);\n  } // Validation Feedback Icon\n\n\n  var getSuffixIconNode = function getSuffixIconNode(arrowIcon) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, showArrow !== false && arrowIcon, hasFeedback && feedbackIcon);\n  }; // Arrow item icon\n\n\n  var mergedSuffixIcon = null;\n\n  if (suffixIcon !== undefined) {\n    mergedSuffixIcon = getSuffixIconNode(suffixIcon);\n  } else if (loading) {\n    mergedSuffixIcon = getSuffixIconNode( /*#__PURE__*/React.createElement(LoadingOutlined, {\n      spin: true\n    }));\n  } else {\n    var iconCls = \"\".concat(prefixCls, \"-suffix\");\n\n    mergedSuffixIcon = function mergedSuffixIcon(_ref2) {\n      var open = _ref2.open,\n          showSearch = _ref2.showSearch;\n\n      if (open && showSearch) {\n        return getSuffixIconNode( /*#__PURE__*/React.createElement(SearchOutlined, {\n          className: iconCls\n        }));\n      }\n\n      return getSuffixIconNode( /*#__PURE__*/React.createElement(DownOutlined, {\n        className: iconCls\n      }));\n    };\n  } // Checked item icon\n\n\n  var mergedItemIcon = null;\n\n  if (menuItemSelectedIcon !== undefined) {\n    mergedItemIcon = menuItemSelectedIcon;\n  } else if (multiple) {\n    mergedItemIcon = /*#__PURE__*/React.createElement(CheckOutlined, null);\n  } else {\n    mergedItemIcon = null;\n  }\n\n  var mergedRemoveIcon = null;\n\n  if (removeIcon !== undefined) {\n    mergedRemoveIcon = removeIcon;\n  } else {\n    mergedRemoveIcon = /*#__PURE__*/React.createElement(CloseOutlined, null);\n  }\n\n  return {\n    clearIcon: mergedClearIcon,\n    suffixIcon: mergedSuffixIcon,\n    itemIcon: mergedItemIcon,\n    removeIcon: mergedRemoveIcon\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC5BC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,oBAAoB,GAAGH,IAAI,CAACG,oBAAoB;IAChDC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,OAAO,GAAGL,IAAI,CAACK,OAAO;IACtBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,WAAW,GAAGP,IAAI,CAACO,WAAW;IAC9BC,SAAS,GAAGR,IAAI,CAACQ,SAAS;IAC1BC,SAAS,GAAGT,IAAI,CAACS,SAAS;IAC1BC,YAAY,GAAGV,IAAI,CAACU,YAAY;EACpC;EACA,IAAIC,eAAe,GAAGT,SAAS;EAE/B,IAAI,CAACA,SAAS,EAAE;IACdS,eAAe,GAAG,aAAanB,KAAK,CAACoB,aAAa,CAACf,iBAAiB,EAAE,IAAI,CAAC;EAC7E,CAAC,CAAC;;EAGF,IAAIgB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,SAAS,EAAE;IAC5D,OAAO,aAAatB,KAAK,CAACoB,aAAa,CAACpB,KAAK,CAACuB,QAAQ,EAAE,IAAI,EAAEN,SAAS,KAAK,KAAK,IAAIK,SAAS,EAAEP,WAAW,IAAIG,YAAY,CAAC;EAC9H,CAAC,CAAC,CAAC;;EAGH,IAAIM,gBAAgB,GAAG,IAAI;EAE3B,IAAIf,UAAU,KAAKgB,SAAS,EAAE;IAC5BD,gBAAgB,GAAGH,iBAAiB,CAACZ,UAAU,CAAC;EAClD,CAAC,MAAM,IAAII,OAAO,EAAE;IAClBW,gBAAgB,GAAGH,iBAAiB,CAAE,aAAarB,KAAK,CAACoB,aAAa,CAAClB,eAAe,EAAE;MACtFwB,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL,IAAIC,OAAO,GAAG,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC;IAE7CQ,gBAAgB,GAAG,SAASA,gBAAgBA,CAACK,KAAK,EAAE;MAClD,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;QACjBC,UAAU,GAAGF,KAAK,CAACE,UAAU;MAEjC,IAAID,IAAI,IAAIC,UAAU,EAAE;QACtB,OAAOV,iBAAiB,CAAE,aAAarB,KAAK,CAACoB,aAAa,CAACd,cAAc,EAAE;UACzE0B,SAAS,EAAEL;QACb,CAAC,CAAC,CAAC;MACL;MAEA,OAAON,iBAAiB,CAAE,aAAarB,KAAK,CAACoB,aAAa,CAACnB,YAAY,EAAE;QACvE+B,SAAS,EAAEL;MACb,CAAC,CAAC,CAAC;IACL,CAAC;EACH,CAAC,CAAC;;EAGF,IAAIM,cAAc,GAAG,IAAI;EAEzB,IAAItB,oBAAoB,KAAKc,SAAS,EAAE;IACtCQ,cAAc,GAAGtB,oBAAoB;EACvC,CAAC,MAAM,IAAIG,QAAQ,EAAE;IACnBmB,cAAc,GAAG,aAAajC,KAAK,CAACoB,aAAa,CAACjB,aAAa,EAAE,IAAI,CAAC;EACxE,CAAC,MAAM;IACL8B,cAAc,GAAG,IAAI;EACvB;EAEA,IAAIC,gBAAgB,GAAG,IAAI;EAE3B,IAAItB,UAAU,KAAKa,SAAS,EAAE;IAC5BS,gBAAgB,GAAGtB,UAAU;EAC/B,CAAC,MAAM;IACLsB,gBAAgB,GAAG,aAAalC,KAAK,CAACoB,aAAa,CAAChB,aAAa,EAAE,IAAI,CAAC;EAC1E;EAEA,OAAO;IACLM,SAAS,EAAES,eAAe;IAC1BV,UAAU,EAAEe,gBAAgB;IAC5BW,QAAQ,EAAEF,cAAc;IACxBrB,UAAU,EAAEsB;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}