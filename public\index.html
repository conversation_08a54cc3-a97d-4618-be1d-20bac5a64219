<!DOCTYPE html>
<html lang="it">

<head>
    <meta charset="utf-8" />
    <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.ico" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
    <meta name="theme-color" content="#292C4B" />
    <meta name="description" content="E-procurement system" />
    <!-- <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" /> -->
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.3.1/mapbox-gl.css' rel='stylesheet' />
    <!--     <link rel="icon" type="image/svg+xml" href="%PUBLIC_URL%/favicon.svg">
    <link rel="icon" type="image/png" href="%PUBLIC_URL%/favicon.png">
    <link rel="icon" type="image/png" href="%PUBLIC_URL%/apple-touch-icon.png">
    <link rel="icon" type="image/png" href="%PUBLIC_URL%/apple-touch-icon-precomposed.png"> -->

    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon-16x16.png">
    <!-- <link rel="manifest" href="%PUBLIC_URL%/site.webmanifest"> -->
    <link rel="mask-icon" href="%PUBLIC_URL%/safari-pinned-tab.svg" color="#292C4B">
    <meta name="msapplication-TileColor" content="#292C4B">

    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/leaflet/1.0.1/leaflet.css">
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <!--     <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
        integrity="sha384-JcKb8q3iqJ61gNV9KGb8thSsNjpSL0n8PARn9HuZOnIxN0hoP+VmmDGMN5t9UJ0Z" crossorigin="anonymous"> -->

    <title>e-procurement</title>
    <!-- START ServiceWorker to create a Progressive Web Application (PWA) -->
    <script>
        /*             if ("serviceWorker" in navigator) {
                      navigator.serviceWorker
                        .register("/sw.js")
                        .then(function (registration) {
                          console.log("Winet e-procurement -> ServiceWorker (sw.js) loaded correctly");
                        })
                        .catch(function (err) {
                          console.log(err);
                        });
                     } */
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js').then(reg => {
                    reg.installing; // the installing worker, or undefined
                    reg.waiting; // the waiting worker, or undefined
                    reg.active; // the active worker, or undefined

                    reg.addEventListener('updatefound', () => {
                        // A wild service worker has appeared in reg.installing!
                        const newWorker = reg.installing;

                        newWorker.state;
                        // "installing" - the install event has fired, but not yet complete
                        // "installed"  - install complete
                        // "activating" - the activate event has fired, but not yet complete
                        // "activated"  - fully active
                        // "redundant"  - discarded. Either failed install, or it's been
                        //                replaced by a newer version

                        newWorker.addEventListener('statechange', () => {
                            // newWorker.state has changed
                        });
                    });
                });

                navigator.serviceWorker.addEventListener('controllerchange', () => {
                    // This fires when the service worker controlling this page
                    // changes, eg a new worker has skipped waiting and become
                    // the new active worker.
                })
                    /* .then((reg) => console.log('Winet e-procurement -> ServiceWorker (sw.js) loaded correctly', reg.scope))
                    .catch((err) => console.log('Winet e-procurement -> ServiceWorker (sw.js) loading failure: ', err)); */
            })
        }
    </script>
    <!-- END ServiceWorker to create a Progressive Web Application (PWA) -->
    <style>.svgLoader{animation:spin 0.5s linear infinite;margin:auto}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.ipl-progress-indicator.available{opacity:0}.ipl-progress-indicator{background-color:#f5f5f5;width:100%;height:100%;position:fixed;opacity:1;pointer-events:none;-webkit-transition:opacity cubic-bezier(.4,0,.2,1) 436ms;-moz-transition:opacity cubic-bezier(.4,0,.2,1) 436ms;transition:opacity cubic-bezier(.4,0,.2,1) 436ms;z-index:9999}.insp-logo-frame{display:-webkit-flex;display:-moz-flex;display:flex;-webkit-flex-direction:column;-moz-flex-direction:column;flex-direction:column;-webkit-justify-content:center;-moz-justify-content:center;justify-content:center;-webkit-animation:fadein 436ms;-moz-animation:fadein 436ms;animation:fadein 436ms;height:98%}.insp-logo-frame-img{width:112px;height:112px;-webkit-align-self:center;-moz-align-self:center;align-self:center;border-radius:50%}.ipl-progress-indicator-head{background-color:#CED4DA;height:4px;overflow:hidden;position:relative}.ipl-progress-indicator .first-indicator,.ipl-progress-indicator .second-indicator{background-color:#000023;bottom:0;left:0;right:0;top:0;position:absolute;-webkit-transform-origin:left center;-moz-transform-origin:left center;transform-origin:left center;-webkit-transform:scaleX(0);-moz-transform:scaleX(0);transform:scaleX(0)}.ipl-progress-indicator .first-indicator{-webkit-animation:first-indicator 2s linear infinite;-moz-animation:first-indicator 2s linear infinite;animation:first-indicator 2s linear infinite}.ipl-progress-indicator .second-indicator{-webkit-animation:second-indicator 2s linear infinite;-moz-animation:second-indicator 2s linear infinite;animation:second-indicator 2s linear infinite}.ipl-progress-indicator .insp-logo{animation:App-logo-spin infinite 20s linear;border-radius:50%;-webkit-align-self:center;-moz-align-self:center;align-self:center}@keyframes App-logo-spin{from{transform:rotate(0)}to{transform:rotate(360deg)}}@-webkit-keyframes fadein{from{opacity:0}to{opacity:1}}@-moz-keyframes fadein{from{opacity:0}to{opacity:1}}@keyframes fadein{from{opacity:0}to{opacity:1}}@keyframes first-indicator{0%{transform:translate(0) scaleX(0)}25%{transform:translate(0) scaleX(.5)}50%{transform:translate(25%) scaleX(.75)}75%{transform:translate(100%) scaleX(0)}100%{transform:translate(100%) scaleX(0)}}@keyframes second-indicator{0%{transform:translate(0) scaleX(0)}60%{transform:translate(0) scaleX(0)}80%{transform:translate(0) scaleX(.6)}100%{transform:translate(100%) scaleX(.1)}}</style>
</head>

<body style="margin: 0!important;">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
        <!--Progress Bar Loading Page-->
        <div class="ipl-progress-indicator" id="ipl-progress-indicator">
            <div class="ipl-progress-indicator-head">
                <div class="first-indicator"></div>
                <div class="second-indicator"></div>
            </div>
            <div class="insp-logo-frame">
                <svg class="svgLoader" viewBox="0 0 100 100" width="10em" height="10em">
                    <path ng-attr-d="{{config.pathCmd}}" ng-attr-fill="{{config.color}}" stroke="none"
                        d="M10 50A40 40 0 0 0 90 50A40 42 0 0 1 10 50" fill="#292C4B" transform="rotate(179.719 50 51)">
                        <animateTransform attributeName="transform" type="rotate" calcMode="linear"
                            values="0 50 51;360 50 51" keyTimes="0;1" dur="1s" begin="0s" repeatCount="indefinite">
                        </animateTransform>
                    </path>
                </svg>
            </div>
        </div>
        <!--Progress Bar Loading Page-->
    </div>
    <script src="/ionicons/ionicons.esm.js" type="module"></script>
    <script nomodule src="/ionicons/ionicons.js"></script>
    <!-- <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule="" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script> -->
    <!-- <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"
        integrity="sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN"
        crossorigin="anonymous"></script> -->
    <!--     <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"
        integrity="sha384-B4gt1jrGC7Jh4AgTPSdUtOBvfO8shuf57BaghqFfPlYxofvL8/KUEfYiJOMMV+rV"
        crossorigin="anonymous"></script> -->

    <!-- Script per rimuovere indicatori backend ridondanti -->
    <script>
        // Pulizia indicatori backend ridondanti
        function cleanupBackendIndicators() {
            // Rimuovi indicatore fisso
            const indicator = document.getElementById('backend-status-indicator');
            if (indicator) {
                indicator.remove();
                console.log('✅ Rimosso indicatore backend fisso');
            }

            // Rimuovi notifiche popup backend
            const notifications = document.querySelectorAll('div[style*="position: fixed"]');
            notifications.forEach(notification => {
                const text = notification.textContent || notification.innerText;
                if (text.includes('Backend') || text.includes('backend')) {
                    notification.remove();
                }
            });
        }

        // Esegui pulizia quando il DOM è pronto
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', cleanupBackendIndicators);
        } else {
            cleanupBackendIndicators();
        }

        // Monitora per nuovi elementi
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1 && node.id === 'backend-status-indicator') {
                        node.remove();
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    </script>
</body>



</html>