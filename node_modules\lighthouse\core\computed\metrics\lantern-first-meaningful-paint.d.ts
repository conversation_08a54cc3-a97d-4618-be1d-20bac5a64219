export { LanternFirstMeaningfulPaintComputed as Lantern<PERSON>irstMeaningfulPaint };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare const LanternFirstMeaningfulPaintComputed: typeof LanternFirstMeaningfulPaint & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
declare class LanternFirstMeaningfulPaint extends LanternMetric {
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-first-meaningful-paint.d.ts.map