{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\bannerWelcome.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useEffect } from \"react\";\nimport { useState } from \"react\";\nimport { Logo } from \"../logo\";\nimport { agente } from \"../route\";\n/* import { Costanti } from \"../traduttore/const\"; */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BannerWelcome = props => {\n  _s();\n  var _JSON$parse$idCorpora;\n  /* const [classe, setClasse] = useState('d-none'); */\n  const [nome, setNome] = useState('');\n  useEffect(() => {\n    if (props.nome !== undefined) {\n      setNome(props.nome);\n    } else {\n      try {\n        const userid = window.localStorage.userid;\n        if (userid && userid.trim() !== '') {\n          const user = JSON.parse(userid);\n          setNome((user === null || user === void 0 ? void 0 : user.firstName) || 'Utente');\n        } else {\n          setNome('Utente');\n        }\n      } catch (error) {\n        console.warn('Failed to parse userid in bannerWelcome:', error);\n        setNome('Utente');\n      }\n      /* setClasse(''); */\n    }\n  }, [props.nome]);\n  var userRole = localStorage.getItem(\"role\");\n  var corporate = (_JSON$parse$idCorpora = JSON.parse(localStorage.getItem(\"userid\")).idCorporate) === null || _JSON$parse$idCorpora === void 0 ? void 0 : _JSON$parse$idCorpora.corporateName;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashAffBanner row solid-head\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"backg col-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center px-3 px-md-0 pb-5 py-sm-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"circle-logo py-0 pt-4 py-sm-4 mr-5\",\n          children: /*#__PURE__*/_jsxDEV(Logo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"pl-0 pt-0 text-center text-sm-left mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"d-block d-sm-inline\",\n              children: [nome, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 81\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: props.createFor\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 133\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 25\n          }, this), userRole === agente && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-sm-left\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              children: /*#__PURE__*/_jsxDEV(\"big\", {\n                children: [\"per \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: corporate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 83\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 74\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 9\n  }, this);\n};\n_s(BannerWelcome, \"Oct+q7/+BCOk4AfekElpb9atNV0=\");\n_c = BannerWelcome;\nvar _c;\n$RefreshReg$(_c, \"BannerWelcome\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Logo", "agente", "jsxDEV", "_jsxDEV", "BannerWelcome", "props", "_s", "_JSON$parse$idCorpora", "nome", "setNome", "undefined", "userid", "window", "localStorage", "trim", "user", "JSON", "parse", "firstName", "error", "console", "warn", "userRole", "getItem", "corporate", "idCorporate", "corporateName", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "createFor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/bannerWelcome.jsx"], "sourcesContent": ["import React from \"react\";\nimport { useEffect } from \"react\";\nimport { useState } from \"react\";\nimport { Logo } from \"../logo\";\nimport { agente } from \"../route\";\n/* import { Costanti } from \"../traduttore/const\"; */\n\nexport const BannerWelcome = (props) => {\n    /* const [classe, setClasse] = useState('d-none'); */\n    const [nome, setNome] = useState('');\n    useEffect(() => {\n        if (props.nome !== undefined) {\n            setNome(props.nome);\n        } else {\n            try {\n                const userid = window.localStorage.userid\n                if (userid && userid.trim() !== '') {\n                    const user = JSON.parse(userid)\n                    setNome(user?.firstName || 'Utente')\n                } else {\n                    setNome('Utente')\n                }\n            } catch (error) {\n                console.warn('Failed to parse userid in bannerWelcome:', error)\n                setNome('Utente')\n            }\n            /* setClasse(''); */\n        }\n    }, [props.nome])\n\n    var userRole = localStorage.getItem(\"role\");\n    var corporate = JSON.parse(localStorage.getItem(\"userid\")).idCorporate?.corporateName;\n\n    return (\n        <div className=\"dashAffBanner row solid-head\">\n            <div className=\"backg col-12\">\n                <div className=\"d-flex justify-content-center align-items-center px-3 px-md-0 pb-5 py-sm-0\">\n                    <div className=\"circle-logo py-0 pt-4 py-sm-4 mr-5\">\n                        <Logo />\n                    </div>\n                    <div className=\"d-flex justify-content-center align-items-center\">\n                        <h3 className=\"pl-0 pt-0 text-center text-sm-left mb-0\"><span className=\"d-block d-sm-inline\">{nome} </span><span>{props.createFor}</span></h3>\n                        {\n                            userRole === agente &&\n                            <div className=\"text-center text-sm-left\"><i><big>per <strong>{corporate}</strong></big></i></div>\n                        }\n                    </div>\n                </div>\n                {/*  <div className=\"row h-100\">\n                    <div className=\"col-12 col-sm-3 col-lg-2 logospace\">\n                        <div className=\"circle-logo py-0 pt-4 py-sm-4 mx-auto\">\n                            <Logo />\n                        </div>\n                    </div>\n                    <div className='col-12 col-sm-9 col-lg-10 px-5 px-md-0 pb-4 pt-5 py-sm-0 align-self-center'>\n                        <h3 className=\"pl-0 pt-0 text-center text-sm-left mb-0\"><span className=\"d-block d-sm-inline\">{nome} </span><span>{props.createFor}</span></h3>\n                    {\n                        userRole === agente &&\n                        <div className=\"text-center text-sm-left\"><i><big>per <strong>{corporate}</strong></big></i></div>\n                    }\n                    </div>\n                </div> */}\n            </div>\n        </div>\n    )\n}"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,QAAQ,SAAS;AAC9B,SAASC,MAAM,QAAQ,UAAU;AACjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,OAAO,MAAMC,aAAa,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACpC;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpCD,SAAS,CAAC,MAAM;IACZ,IAAIO,KAAK,CAACG,IAAI,KAAKE,SAAS,EAAE;MAC1BD,OAAO,CAACJ,KAAK,CAACG,IAAI,CAAC;IACvB,CAAC,MAAM;MACH,IAAI;QACA,MAAMG,MAAM,GAAGC,MAAM,CAACC,YAAY,CAACF,MAAM;QACzC,IAAIA,MAAM,IAAIA,MAAM,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAChC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACN,MAAM,CAAC;UAC/BF,OAAO,CAAC,CAAAM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,SAAS,KAAI,QAAQ,CAAC;QACxC,CAAC,MAAM;UACHT,OAAO,CAAC,QAAQ,CAAC;QACrB;MACJ,CAAC,CAAC,OAAOU,KAAK,EAAE;QACZC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,KAAK,CAAC;QAC/DV,OAAO,CAAC,QAAQ,CAAC;MACrB;MACA;IACJ;EACJ,CAAC,EAAE,CAACJ,KAAK,CAACG,IAAI,CAAC,CAAC;EAEhB,IAAIc,QAAQ,GAAGT,YAAY,CAACU,OAAO,CAAC,MAAM,CAAC;EAC3C,IAAIC,SAAS,IAAAjB,qBAAA,GAAGS,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACU,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACE,WAAW,cAAAlB,qBAAA,uBAAtDA,qBAAA,CAAwDmB,aAAa;EAErF,oBACIvB,OAAA;IAAKwB,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eACzCzB,OAAA;MAAKwB,SAAS,EAAC,cAAc;MAAAC,QAAA,eACzBzB,OAAA;QAAKwB,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACvFzB,OAAA;UAAKwB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/CzB,OAAA,CAACH,IAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC7DzB,OAAA;YAAIwB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBAACzB,OAAA;cAAMwB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAEpB,IAAI,EAAC,GAAC;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAAA7B,OAAA;cAAAyB,QAAA,EAAOvB,KAAK,CAAC4B;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAE3IV,QAAQ,KAAKrB,MAAM,iBACnBE,OAAA;YAAKwB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eAACzB,OAAA;cAAAyB,QAAA,eAAGzB,OAAA;gBAAAyB,QAAA,GAAK,MAAI,eAAAzB,OAAA;kBAAAyB,QAAA,EAASJ;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA1B,EAAA,CA1DYF,aAAa;AAAA8B,EAAA,GAAb9B,aAAa;AAAA,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}