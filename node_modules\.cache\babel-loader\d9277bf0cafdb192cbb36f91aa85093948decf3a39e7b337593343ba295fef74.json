{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Removed props:\n *  - childrenProps\n */\nimport React from 'react';\nimport { composeRef } from \"rc-util/es/ref\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport { alignElement, alignPoint } from 'dom-align';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport isEqual from 'lodash/isEqual';\nimport { isSamePoint, restoreFocus, monitorResize } from './util';\nimport useBuffer from './hooks/useBuffer';\nfunction getElement(func) {\n  if (typeof func !== 'function') return null;\n  return func();\n}\nfunction getPoint(point) {\n  if (_typeof(point) !== 'object' || !point) return null;\n  return point;\n}\nvar Align = function Align(_ref, ref) {\n  var children = _ref.children,\n    disabled = _ref.disabled,\n    target = _ref.target,\n    align = _ref.align,\n    onAlign = _ref.onAlign,\n    monitorWindowResize = _ref.monitorWindowResize,\n    _ref$monitorBufferTim = _ref.monitorBufferTime,\n    monitorBufferTime = _ref$monitorBufferTim === void 0 ? 0 : _ref$monitorBufferTim;\n  var cacheRef = React.useRef({});\n  var nodeRef = React.useRef();\n  var childNode = React.Children.only(children); // ===================== Align ======================\n  // We save the props here to avoid closure makes props ood\n\n  var forceAlignPropsRef = React.useRef({});\n  forceAlignPropsRef.current.disabled = disabled;\n  forceAlignPropsRef.current.target = target;\n  forceAlignPropsRef.current.align = align;\n  forceAlignPropsRef.current.onAlign = onAlign;\n  var _useBuffer = useBuffer(function () {\n      var _forceAlignPropsRef$c = forceAlignPropsRef.current,\n        latestDisabled = _forceAlignPropsRef$c.disabled,\n        latestTarget = _forceAlignPropsRef$c.target,\n        latestAlign = _forceAlignPropsRef$c.align,\n        latestOnAlign = _forceAlignPropsRef$c.onAlign;\n      if (!latestDisabled && latestTarget) {\n        var source = nodeRef.current;\n        var result;\n        var element = getElement(latestTarget);\n        var point = getPoint(latestTarget);\n        cacheRef.current.element = element;\n        cacheRef.current.point = point;\n        cacheRef.current.align = latestAlign; // IE lose focus after element realign\n        // We should record activeElement and restore later\n\n        // IE lose focus after element realign\n        // We should record activeElement and restore later\n        var _document = document,\n          activeElement = _document.activeElement; // We only align when element is visible\n\n        // We only align when element is visible\n        if (element && isVisible(element)) {\n          result = alignElement(source, element, latestAlign);\n        } else if (point) {\n          result = alignPoint(source, point, latestAlign);\n        }\n        restoreFocus(activeElement, source);\n        if (latestOnAlign && result) {\n          latestOnAlign(source, result);\n        }\n        return true;\n      }\n      return false;\n    }, monitorBufferTime),\n    _useBuffer2 = _slicedToArray(_useBuffer, 2),\n    _forceAlign = _useBuffer2[0],\n    cancelForceAlign = _useBuffer2[1]; // ===================== Effect =====================\n  // Listen for target updated\n\n  var resizeMonitor = React.useRef({\n    cancel: function cancel() {}\n  }); // Listen for source updated\n\n  var sourceResizeMonitor = React.useRef({\n    cancel: function cancel() {}\n  });\n  React.useEffect(function () {\n    var element = getElement(target);\n    var point = getPoint(target);\n    if (nodeRef.current !== sourceResizeMonitor.current.element) {\n      sourceResizeMonitor.current.cancel();\n      sourceResizeMonitor.current.element = nodeRef.current;\n      sourceResizeMonitor.current.cancel = monitorResize(nodeRef.current, _forceAlign);\n    }\n    if (cacheRef.current.element !== element || !isSamePoint(cacheRef.current.point, point) || !isEqual(cacheRef.current.align, align)) {\n      _forceAlign(); // Add resize observer\n\n      if (resizeMonitor.current.element !== element) {\n        resizeMonitor.current.cancel();\n        resizeMonitor.current.element = element;\n        resizeMonitor.current.cancel = monitorResize(element, _forceAlign);\n      }\n    }\n  }); // Listen for disabled change\n\n  React.useEffect(function () {\n    if (!disabled) {\n      _forceAlign();\n    } else {\n      cancelForceAlign();\n    }\n  }, [disabled]); // Listen for window resize\n\n  var winResizeRef = React.useRef(null);\n  React.useEffect(function () {\n    if (monitorWindowResize) {\n      if (!winResizeRef.current) {\n        winResizeRef.current = addEventListener(window, 'resize', _forceAlign);\n      }\n    } else if (winResizeRef.current) {\n      winResizeRef.current.remove();\n      winResizeRef.current = null;\n    }\n  }, [monitorWindowResize]); // Clear all if unmount\n\n  React.useEffect(function () {\n    return function () {\n      resizeMonitor.current.cancel();\n      sourceResizeMonitor.current.cancel();\n      if (winResizeRef.current) winResizeRef.current.remove();\n      cancelForceAlign();\n    };\n  }, []); // ====================== Ref =======================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {\n        return _forceAlign(true);\n      }\n    };\n  }); // ===================== Render =====================\n\n  if (/*#__PURE__*/React.isValidElement(childNode)) {\n    childNode = /*#__PURE__*/React.cloneElement(childNode, {\n      ref: composeRef(childNode.ref, nodeRef)\n    });\n  }\n  return childNode;\n};\nvar RcAlign = /*#__PURE__*/React.forwardRef(Align);\nRcAlign.displayName = 'Align';\nexport default RcAlign;", "map": {"version": 3, "names": ["_slicedToArray", "_typeof", "React", "composeRef", "isVisible", "alignElement", "alignPoint", "addEventListener", "isEqual", "isSamePoint", "restoreFocus", "monitorResize", "useBuffer", "getElement", "func", "getPoint", "point", "Align", "_ref", "ref", "children", "disabled", "target", "align", "onAlign", "monitorWindowResize", "_ref$monitorBufferTim", "monitorBufferTime", "cacheRef", "useRef", "nodeRef", "childNode", "Children", "only", "forceAlignPropsRef", "current", "_useBuffer", "_forceAlignPropsRef$c", "latestDisabled", "latestTarget", "latestAlign", "latestOnAlign", "source", "result", "element", "_document", "document", "activeElement", "_useBuffer2", "_forceAlign", "cancelForceAlign", "resizeMonitor", "cancel", "sourceResizeMonitor", "useEffect", "winResizeRef", "window", "remove", "useImperativeHandle", "forceAlign", "isValidElement", "cloneElement", "RcAlign", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-align/es/Align.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Removed props:\n *  - childrenProps\n */\nimport React from 'react';\nimport { composeRef } from \"rc-util/es/ref\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport { alignElement, alignPoint } from 'dom-align';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport isEqual from 'lodash/isEqual';\nimport { isSamePoint, restoreFocus, monitorResize } from './util';\nimport useBuffer from './hooks/useBuffer';\n\nfunction getElement(func) {\n  if (typeof func !== 'function') return null;\n  return func();\n}\n\nfunction getPoint(point) {\n  if (_typeof(point) !== 'object' || !point) return null;\n  return point;\n}\n\nvar Align = function Align(_ref, ref) {\n  var children = _ref.children,\n      disabled = _ref.disabled,\n      target = _ref.target,\n      align = _ref.align,\n      onAlign = _ref.onAlign,\n      monitorWindowResize = _ref.monitorWindowResize,\n      _ref$monitorBufferTim = _ref.monitorBufferTime,\n      monitorBufferTime = _ref$monitorBufferTim === void 0 ? 0 : _ref$monitorBufferTim;\n  var cacheRef = React.useRef({});\n  var nodeRef = React.useRef();\n  var childNode = React.Children.only(children); // ===================== Align ======================\n  // We save the props here to avoid closure makes props ood\n\n  var forceAlignPropsRef = React.useRef({});\n  forceAlignPropsRef.current.disabled = disabled;\n  forceAlignPropsRef.current.target = target;\n  forceAlignPropsRef.current.align = align;\n  forceAlignPropsRef.current.onAlign = onAlign;\n\n  var _useBuffer = useBuffer(function () {\n    var _forceAlignPropsRef$c = forceAlignPropsRef.current,\n        latestDisabled = _forceAlignPropsRef$c.disabled,\n        latestTarget = _forceAlignPropsRef$c.target,\n        latestAlign = _forceAlignPropsRef$c.align,\n        latestOnAlign = _forceAlignPropsRef$c.onAlign;\n\n    if (!latestDisabled && latestTarget) {\n      var source = nodeRef.current;\n      var result;\n      var element = getElement(latestTarget);\n      var point = getPoint(latestTarget);\n      cacheRef.current.element = element;\n      cacheRef.current.point = point;\n      cacheRef.current.align = latestAlign; // IE lose focus after element realign\n      // We should record activeElement and restore later\n\n      // IE lose focus after element realign\n      // We should record activeElement and restore later\n      var _document = document,\n          activeElement = _document.activeElement; // We only align when element is visible\n\n      // We only align when element is visible\n      if (element && isVisible(element)) {\n        result = alignElement(source, element, latestAlign);\n      } else if (point) {\n        result = alignPoint(source, point, latestAlign);\n      }\n\n      restoreFocus(activeElement, source);\n\n      if (latestOnAlign && result) {\n        latestOnAlign(source, result);\n      }\n\n      return true;\n    }\n\n    return false;\n  }, monitorBufferTime),\n      _useBuffer2 = _slicedToArray(_useBuffer, 2),\n      _forceAlign = _useBuffer2[0],\n      cancelForceAlign = _useBuffer2[1]; // ===================== Effect =====================\n  // Listen for target updated\n\n\n  var resizeMonitor = React.useRef({\n    cancel: function cancel() {}\n  }); // Listen for source updated\n\n  var sourceResizeMonitor = React.useRef({\n    cancel: function cancel() {}\n  });\n  React.useEffect(function () {\n    var element = getElement(target);\n    var point = getPoint(target);\n\n    if (nodeRef.current !== sourceResizeMonitor.current.element) {\n      sourceResizeMonitor.current.cancel();\n      sourceResizeMonitor.current.element = nodeRef.current;\n      sourceResizeMonitor.current.cancel = monitorResize(nodeRef.current, _forceAlign);\n    }\n\n    if (cacheRef.current.element !== element || !isSamePoint(cacheRef.current.point, point) || !isEqual(cacheRef.current.align, align)) {\n      _forceAlign(); // Add resize observer\n\n\n      if (resizeMonitor.current.element !== element) {\n        resizeMonitor.current.cancel();\n        resizeMonitor.current.element = element;\n        resizeMonitor.current.cancel = monitorResize(element, _forceAlign);\n      }\n    }\n  }); // Listen for disabled change\n\n  React.useEffect(function () {\n    if (!disabled) {\n      _forceAlign();\n    } else {\n      cancelForceAlign();\n    }\n  }, [disabled]); // Listen for window resize\n\n  var winResizeRef = React.useRef(null);\n  React.useEffect(function () {\n    if (monitorWindowResize) {\n      if (!winResizeRef.current) {\n        winResizeRef.current = addEventListener(window, 'resize', _forceAlign);\n      }\n    } else if (winResizeRef.current) {\n      winResizeRef.current.remove();\n      winResizeRef.current = null;\n    }\n  }, [monitorWindowResize]); // Clear all if unmount\n\n  React.useEffect(function () {\n    return function () {\n      resizeMonitor.current.cancel();\n      sourceResizeMonitor.current.cancel();\n      if (winResizeRef.current) winResizeRef.current.remove();\n      cancelForceAlign();\n    };\n  }, []); // ====================== Ref =======================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {\n        return _forceAlign(true);\n      }\n    };\n  }); // ===================== Render =====================\n\n  if ( /*#__PURE__*/React.isValidElement(childNode)) {\n    childNode = /*#__PURE__*/React.cloneElement(childNode, {\n      ref: composeRef(childNode.ref, nodeRef)\n    });\n  }\n\n  return childNode;\n};\n\nvar RcAlign = /*#__PURE__*/React.forwardRef(Align);\nRcAlign.displayName = 'Align';\nexport default RcAlign;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;;AAEvD;AACA;AACA;AACA;AACA,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,YAAY,EAAEC,UAAU,QAAQ,WAAW;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,WAAW,EAAEC,YAAY,EAAEC,aAAa,QAAQ,QAAQ;AACjE,OAAOC,SAAS,MAAM,mBAAmB;AAEzC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,OAAO,IAAI;EAC3C,OAAOA,IAAI,CAAC,CAAC;AACf;AAEA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIf,OAAO,CAACe,KAAK,CAAC,KAAK,QAAQ,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACtD,OAAOA,KAAK;AACd;AAEA,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACpC,IAAIC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,mBAAmB,GAAGP,IAAI,CAACO,mBAAmB;IAC9CC,qBAAqB,GAAGR,IAAI,CAACS,iBAAiB;IAC9CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;EACpF,IAAIE,QAAQ,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAIC,OAAO,GAAG5B,KAAK,CAAC2B,MAAM,CAAC,CAAC;EAC5B,IAAIE,SAAS,GAAG7B,KAAK,CAAC8B,QAAQ,CAACC,IAAI,CAACb,QAAQ,CAAC,CAAC,CAAC;EAC/C;;EAEA,IAAIc,kBAAkB,GAAGhC,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAAC;EACzCK,kBAAkB,CAACC,OAAO,CAACd,QAAQ,GAAGA,QAAQ;EAC9Ca,kBAAkB,CAACC,OAAO,CAACb,MAAM,GAAGA,MAAM;EAC1CY,kBAAkB,CAACC,OAAO,CAACZ,KAAK,GAAGA,KAAK;EACxCW,kBAAkB,CAACC,OAAO,CAACX,OAAO,GAAGA,OAAO;EAE5C,IAAIY,UAAU,GAAGxB,SAAS,CAAC,YAAY;MACrC,IAAIyB,qBAAqB,GAAGH,kBAAkB,CAACC,OAAO;QAClDG,cAAc,GAAGD,qBAAqB,CAAChB,QAAQ;QAC/CkB,YAAY,GAAGF,qBAAqB,CAACf,MAAM;QAC3CkB,WAAW,GAAGH,qBAAqB,CAACd,KAAK;QACzCkB,aAAa,GAAGJ,qBAAqB,CAACb,OAAO;MAEjD,IAAI,CAACc,cAAc,IAAIC,YAAY,EAAE;QACnC,IAAIG,MAAM,GAAGZ,OAAO,CAACK,OAAO;QAC5B,IAAIQ,MAAM;QACV,IAAIC,OAAO,GAAG/B,UAAU,CAAC0B,YAAY,CAAC;QACtC,IAAIvB,KAAK,GAAGD,QAAQ,CAACwB,YAAY,CAAC;QAClCX,QAAQ,CAACO,OAAO,CAACS,OAAO,GAAGA,OAAO;QAClChB,QAAQ,CAACO,OAAO,CAACnB,KAAK,GAAGA,KAAK;QAC9BY,QAAQ,CAACO,OAAO,CAACZ,KAAK,GAAGiB,WAAW,CAAC,CAAC;QACtC;;QAEA;QACA;QACA,IAAIK,SAAS,GAAGC,QAAQ;UACpBC,aAAa,GAAGF,SAAS,CAACE,aAAa,CAAC,CAAC;;QAE7C;QACA,IAAIH,OAAO,IAAIxC,SAAS,CAACwC,OAAO,CAAC,EAAE;UACjCD,MAAM,GAAGtC,YAAY,CAACqC,MAAM,EAAEE,OAAO,EAAEJ,WAAW,CAAC;QACrD,CAAC,MAAM,IAAIxB,KAAK,EAAE;UAChB2B,MAAM,GAAGrC,UAAU,CAACoC,MAAM,EAAE1B,KAAK,EAAEwB,WAAW,CAAC;QACjD;QAEA9B,YAAY,CAACqC,aAAa,EAAEL,MAAM,CAAC;QAEnC,IAAID,aAAa,IAAIE,MAAM,EAAE;UAC3BF,aAAa,CAACC,MAAM,EAAEC,MAAM,CAAC;QAC/B;QAEA,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,EAAEhB,iBAAiB,CAAC;IACjBqB,WAAW,GAAGhD,cAAc,CAACoC,UAAU,EAAE,CAAC,CAAC;IAC3Ca,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC5BE,gBAAgB,GAAGF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC;;EAGA,IAAIG,aAAa,GAAGjD,KAAK,CAAC2B,MAAM,CAAC;IAC/BuB,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,mBAAmB,GAAGnD,KAAK,CAAC2B,MAAM,CAAC;IACrCuB,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG,CAAC;EAC7B,CAAC,CAAC;EACFlD,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAIV,OAAO,GAAG/B,UAAU,CAACS,MAAM,CAAC;IAChC,IAAIN,KAAK,GAAGD,QAAQ,CAACO,MAAM,CAAC;IAE5B,IAAIQ,OAAO,CAACK,OAAO,KAAKkB,mBAAmB,CAAClB,OAAO,CAACS,OAAO,EAAE;MAC3DS,mBAAmB,CAAClB,OAAO,CAACiB,MAAM,CAAC,CAAC;MACpCC,mBAAmB,CAAClB,OAAO,CAACS,OAAO,GAAGd,OAAO,CAACK,OAAO;MACrDkB,mBAAmB,CAAClB,OAAO,CAACiB,MAAM,GAAGzC,aAAa,CAACmB,OAAO,CAACK,OAAO,EAAEc,WAAW,CAAC;IAClF;IAEA,IAAIrB,QAAQ,CAACO,OAAO,CAACS,OAAO,KAAKA,OAAO,IAAI,CAACnC,WAAW,CAACmB,QAAQ,CAACO,OAAO,CAACnB,KAAK,EAAEA,KAAK,CAAC,IAAI,CAACR,OAAO,CAACoB,QAAQ,CAACO,OAAO,CAACZ,KAAK,EAAEA,KAAK,CAAC,EAAE;MAClI0B,WAAW,CAAC,CAAC,CAAC,CAAC;;MAGf,IAAIE,aAAa,CAAChB,OAAO,CAACS,OAAO,KAAKA,OAAO,EAAE;QAC7CO,aAAa,CAAChB,OAAO,CAACiB,MAAM,CAAC,CAAC;QAC9BD,aAAa,CAAChB,OAAO,CAACS,OAAO,GAAGA,OAAO;QACvCO,aAAa,CAAChB,OAAO,CAACiB,MAAM,GAAGzC,aAAa,CAACiC,OAAO,EAAEK,WAAW,CAAC;MACpE;IACF;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ/C,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACjC,QAAQ,EAAE;MACb4B,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC7B,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAIkC,YAAY,GAAGrD,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EACrC3B,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAI7B,mBAAmB,EAAE;MACvB,IAAI,CAAC8B,YAAY,CAACpB,OAAO,EAAE;QACzBoB,YAAY,CAACpB,OAAO,GAAG5B,gBAAgB,CAACiD,MAAM,EAAE,QAAQ,EAAEP,WAAW,CAAC;MACxE;IACF,CAAC,MAAM,IAAIM,YAAY,CAACpB,OAAO,EAAE;MAC/BoB,YAAY,CAACpB,OAAO,CAACsB,MAAM,CAAC,CAAC;MAC7BF,YAAY,CAACpB,OAAO,GAAG,IAAI;IAC7B;EACF,CAAC,EAAE,CAACV,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAE3BvB,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBH,aAAa,CAAChB,OAAO,CAACiB,MAAM,CAAC,CAAC;MAC9BC,mBAAmB,CAAClB,OAAO,CAACiB,MAAM,CAAC,CAAC;MACpC,IAAIG,YAAY,CAACpB,OAAO,EAAEoB,YAAY,CAACpB,OAAO,CAACsB,MAAM,CAAC,CAAC;MACvDP,gBAAgB,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERhD,KAAK,CAACwD,mBAAmB,CAACvC,GAAG,EAAE,YAAY;IACzC,OAAO;MACLwC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOV,WAAW,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAK,aAAa/C,KAAK,CAAC0D,cAAc,CAAC7B,SAAS,CAAC,EAAE;IACjDA,SAAS,GAAG,aAAa7B,KAAK,CAAC2D,YAAY,CAAC9B,SAAS,EAAE;MACrDZ,GAAG,EAAEhB,UAAU,CAAC4B,SAAS,CAACZ,GAAG,EAAEW,OAAO;IACxC,CAAC,CAAC;EACJ;EAEA,OAAOC,SAAS;AAClB,CAAC;AAED,IAAI+B,OAAO,GAAG,aAAa5D,KAAK,CAAC6D,UAAU,CAAC9C,KAAK,CAAC;AAClD6C,OAAO,CAACE,WAAW,GAAG,OAAO;AAC7B,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}