{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useNotification;\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _Notice = _interopRequireDefault(require(\"./Notice\"));\nfunction useNotification(notificationInstance) {\n  var createdRef = React.useRef({});\n  var _React$useState = React.useState([]),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  function notify(noticeProps) {\n    var firstMount = true;\n    notificationInstance.add(noticeProps, function (div, props) {\n      var key = props.key;\n      if (div && (!createdRef.current[key] || firstMount)) {\n        var noticeEle = /*#__PURE__*/React.createElement(_Notice.default, (0, _extends2.default)({}, props, {\n          holder: div\n        }));\n        createdRef.current[key] = noticeEle;\n        setElements(function (originElements) {\n          var index = originElements.findIndex(function (ele) {\n            return ele.key === props.key;\n          });\n          if (index === -1) {\n            return [].concat((0, _toConsumableArray2.default)(originElements), [noticeEle]);\n          }\n          var cloneList = (0, _toConsumableArray2.default)(originElements);\n          cloneList[index] = noticeEle;\n          return cloneList;\n        });\n      }\n      firstMount = false;\n    });\n  }\n  return [notify, /*#__PURE__*/React.createElement(React.Fragment, null, elements)];\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "default", "useNotification", "_toConsumableArray2", "_extends2", "_slicedToArray2", "React", "_Notice", "notificationInstance", "createdRef", "useRef", "_React$useState", "useState", "_React$useState2", "elements", "setElements", "notify", "noticeProps", "firstMount", "add", "div", "props", "key", "current", "noticeEle", "createElement", "holder", "originElements", "index", "findIndex", "ele", "concat", "cloneList", "Fragment"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-notification/lib/useNotification.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useNotification;\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _Notice = _interopRequireDefault(require(\"./Notice\"));\n\nfunction useNotification(notificationInstance) {\n  var createdRef = React.useRef({});\n\n  var _React$useState = React.useState([]),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      elements = _React$useState2[0],\n      setElements = _React$useState2[1];\n\n  function notify(noticeProps) {\n    var firstMount = true;\n    notificationInstance.add(noticeProps, function (div, props) {\n      var key = props.key;\n\n      if (div && (!createdRef.current[key] || firstMount)) {\n        var noticeEle = /*#__PURE__*/React.createElement(_Notice.default, (0, _extends2.default)({}, props, {\n          holder: div\n        }));\n        createdRef.current[key] = noticeEle;\n        setElements(function (originElements) {\n          var index = originElements.findIndex(function (ele) {\n            return ele.key === props.key;\n          });\n\n          if (index === -1) {\n            return [].concat((0, _toConsumableArray2.default)(originElements), [noticeEle]);\n          }\n\n          var cloneList = (0, _toConsumableArray2.default)(originElements);\n          cloneList[index] = noticeEle;\n          return cloneList;\n        });\n      }\n\n      firstMount = false;\n    });\n  }\n\n  return [notify, /*#__PURE__*/React.createElement(React.Fragment, null, elements)];\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,eAAe;AAEjC,IAAIC,mBAAmB,GAAGP,sBAAsB,CAACD,OAAO,CAAC,0CAA0C,CAAC,CAAC;AAErG,IAAIS,SAAS,GAAGR,sBAAsB,CAACD,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIU,eAAe,GAAGT,sBAAsB,CAACD,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE7F,IAAIW,KAAK,GAAGZ,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIY,OAAO,GAAGX,sBAAsB,CAACD,OAAO,CAAC,UAAU,CAAC,CAAC;AAEzD,SAASO,eAAeA,CAACM,oBAAoB,EAAE;EAC7C,IAAIC,UAAU,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;EAEjC,IAAIC,eAAe,GAAGL,KAAK,CAACM,QAAQ,CAAC,EAAE,CAAC;IACpCC,gBAAgB,GAAG,CAAC,CAAC,EAAER,eAAe,CAACJ,OAAO,EAAEU,eAAe,EAAE,CAAC,CAAC;IACnEG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAErC,SAASG,MAAMA,CAACC,WAAW,EAAE;IAC3B,IAAIC,UAAU,GAAG,IAAI;IACrBV,oBAAoB,CAACW,GAAG,CAACF,WAAW,EAAE,UAAUG,GAAG,EAAEC,KAAK,EAAE;MAC1D,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;MAEnB,IAAIF,GAAG,KAAK,CAACX,UAAU,CAACc,OAAO,CAACD,GAAG,CAAC,IAAIJ,UAAU,CAAC,EAAE;QACnD,IAAIM,SAAS,GAAG,aAAalB,KAAK,CAACmB,aAAa,CAAClB,OAAO,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEG,SAAS,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEoB,KAAK,EAAE;UAClGK,MAAM,EAAEN;QACV,CAAC,CAAC,CAAC;QACHX,UAAU,CAACc,OAAO,CAACD,GAAG,CAAC,GAAGE,SAAS;QACnCT,WAAW,CAAC,UAAUY,cAAc,EAAE;UACpC,IAAIC,KAAK,GAAGD,cAAc,CAACE,SAAS,CAAC,UAAUC,GAAG,EAAE;YAClD,OAAOA,GAAG,CAACR,GAAG,KAAKD,KAAK,CAACC,GAAG;UAC9B,CAAC,CAAC;UAEF,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,EAAE,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE5B,mBAAmB,CAACF,OAAO,EAAE0B,cAAc,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;UACjF;UAEA,IAAIQ,SAAS,GAAG,CAAC,CAAC,EAAE7B,mBAAmB,CAACF,OAAO,EAAE0B,cAAc,CAAC;UAChEK,SAAS,CAACJ,KAAK,CAAC,GAAGJ,SAAS;UAC5B,OAAOQ,SAAS;QAClB,CAAC,CAAC;MACJ;MAEAd,UAAU,GAAG,KAAK;IACpB,CAAC,CAAC;EACJ;EAEA,OAAO,CAACF,MAAM,EAAE,aAAaV,KAAK,CAACmB,aAAa,CAACnB,KAAK,CAAC2B,QAAQ,EAAE,IAAI,EAAEnB,QAAQ,CAAC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}