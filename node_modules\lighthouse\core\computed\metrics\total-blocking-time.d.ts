export { TotalBlockingTimeComputed as TotalBlockingTime };
declare const TotalBlockingTimeComputed: typeof TotalBlockingTime & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.Metric | import("../../index.js").Artifacts.LanternMetric>;
};
/**
 * @fileoverview This audit determines Total Blocking Time.

 * We define Blocking Time as any time interval in the loading timeline where task length exceeds
 * 50ms. For example, if there is a 110ms main thread task, the last 60ms of it is blocking time.
 * Total Blocking Time is the sum of all Blocking Time between First Contentful Paint and
 * Interactive Time (TTI).
 *
 * This is a new metric designed to accompany Time to Interactive. TTI is strict and does not
 * reflect incremental improvements to the site performance unless the improvement concerns the last
 * long task. Total Blocking Time on the other hand is designed to be much more responsive
 * to smaller improvements to main thread responsiveness.
 */
declare class TotalBlockingTime extends ComputedMetric {
}
import ComputedMetric from "./metric.js";
//# sourceMappingURL=total-blocking-time.d.ts.map