{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from './Input';\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    inputRef = props.inputRef,\n    disabled = props.disabled,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    mode = props.mode,\n    open = props.open,\n    values = props.values,\n    placeholder = props.placeholder,\n    tabIndex = props.tabIndex,\n    showSearch = props.showSearch,\n    searchValue = props.searchValue,\n    activeValue = props.activeValue,\n    maxLength = props.maxLength,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    inputChanged = _React$useState2[0],\n    setInputChanged = _React$useState2[1];\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]); // Not show text when closed expect combobox mode\n\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n  var title = item && (typeof item.label === 'string' || typeof item.label === 'number') ? item.label.toString() : undefined;\n  var renderPlaceholder = function renderPlaceholder() {\n    if (item) {\n      return null;\n    }\n    var hiddenStyle = hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hiddenStyle\n    }, placeholder);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item && !hasTextInput && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: title\n  }, item.label), renderPlaceholder());\n};\nexport default SingleSelector;", "map": {"version": 3, "names": ["_slicedToArray", "React", "pickAttrs", "Input", "SingleSelector", "props", "inputElement", "prefixCls", "id", "inputRef", "disabled", "autoFocus", "autoComplete", "activeDescendantId", "mode", "open", "values", "placeholder", "tabIndex", "showSearch", "searchValue", "activeValue", "max<PERSON><PERSON><PERSON>", "onInputKeyDown", "onInputMouseDown", "onInputChange", "onInputPaste", "onInputCompositionStart", "onInputCompositionEnd", "_React$useState", "useState", "_React$useState2", "inputChanged", "setInputChanged", "combobox", "inputEditable", "item", "inputValue", "useEffect", "hasTextInput", "title", "label", "toString", "undefined", "renderPlaceholder", "hiddenStyle", "visibility", "createElement", "className", "concat", "style", "Fragment", "ref", "editable", "value", "onKeyDown", "onMouseDown", "onChange", "e", "onPaste", "onCompositionStart", "onCompositionEnd", "attrs"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/Selector/SingleSelector.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from './Input';\n\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n      prefixCls = props.prefixCls,\n      id = props.id,\n      inputRef = props.inputRef,\n      disabled = props.disabled,\n      autoFocus = props.autoFocus,\n      autoComplete = props.autoComplete,\n      activeDescendantId = props.activeDescendantId,\n      mode = props.mode,\n      open = props.open,\n      values = props.values,\n      placeholder = props.placeholder,\n      tabIndex = props.tabIndex,\n      showSearch = props.showSearch,\n      searchValue = props.searchValue,\n      activeValue = props.activeValue,\n      maxLength = props.maxLength,\n      onInputKeyDown = props.onInputKeyDown,\n      onInputMouseDown = props.onInputMouseDown,\n      onInputChange = props.onInputChange,\n      onInputPaste = props.onInputPaste,\n      onInputCompositionStart = props.onInputCompositionStart,\n      onInputCompositionEnd = props.onInputCompositionEnd;\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      inputChanged = _React$useState2[0],\n      setInputChanged = _React$useState2[1];\n\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]); // Not show text when closed expect combobox mode\n\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n  var title = item && (typeof item.label === 'string' || typeof item.label === 'number') ? item.label.toString() : undefined;\n\n  var renderPlaceholder = function renderPlaceholder() {\n    if (item) {\n      return null;\n    }\n\n    var hiddenStyle = hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hiddenStyle\n    }, placeholder);\n  };\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item && !hasTextInput && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: title\n  }, item.label), renderPlaceholder());\n};\n\nexport default SingleSelector;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,SAAS;AAE3B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACjCC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,kBAAkB,GAAGR,KAAK,CAACQ,kBAAkB;IAC7CC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,cAAc,GAAGlB,KAAK,CAACkB,cAAc;IACrCC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,aAAa,GAAGpB,KAAK,CAACoB,aAAa;IACnCC,YAAY,GAAGrB,KAAK,CAACqB,YAAY;IACjCC,uBAAuB,GAAGtB,KAAK,CAACsB,uBAAuB;IACvDC,qBAAqB,GAAGvB,KAAK,CAACuB,qBAAqB;EAEvD,IAAIC,eAAe,GAAG5B,KAAK,CAAC6B,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAG/B,cAAc,CAAC6B,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,IAAIG,QAAQ,GAAGpB,IAAI,KAAK,UAAU;EAClC,IAAIqB,aAAa,GAAGD,QAAQ,IAAIf,UAAU;EAC1C,IAAIiB,IAAI,GAAGpB,MAAM,CAAC,CAAC,CAAC;EACpB,IAAIqB,UAAU,GAAGjB,WAAW,IAAI,EAAE;EAElC,IAAIc,QAAQ,IAAIb,WAAW,IAAI,CAACW,YAAY,EAAE;IAC5CK,UAAU,GAAGhB,WAAW;EAC1B;EAEApB,KAAK,CAACqC,SAAS,CAAC,YAAY;IAC1B,IAAIJ,QAAQ,EAAE;MACZD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACC,QAAQ,EAAEb,WAAW,CAAC,CAAC,CAAC,CAAC;;EAE7B,IAAIkB,YAAY,GAAGzB,IAAI,KAAK,UAAU,IAAI,CAACC,IAAI,IAAI,CAACI,UAAU,GAAG,KAAK,GAAG,CAAC,CAACkB,UAAU;EACrF,IAAIG,KAAK,GAAGJ,IAAI,KAAK,OAAOA,IAAI,CAACK,KAAK,KAAK,QAAQ,IAAI,OAAOL,IAAI,CAACK,KAAK,KAAK,QAAQ,CAAC,GAAGL,IAAI,CAACK,KAAK,CAACC,QAAQ,CAAC,CAAC,GAAGC,SAAS;EAE1H,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIR,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IAEA,IAAIS,WAAW,GAAGN,YAAY,GAAG;MAC/BO,UAAU,EAAE;IACd,CAAC,GAAGH,SAAS;IACb,OAAO,aAAa1C,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1C,SAAS,EAAE,wBAAwB,CAAC;MACzD2C,KAAK,EAAEL;IACT,CAAC,EAAE5B,WAAW,CAAC;EACjB,CAAC;EAED,OAAO,aAAahB,KAAK,CAAC8C,aAAa,CAAC9C,KAAK,CAACkD,QAAQ,EAAE,IAAI,EAAE,aAAalD,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;IACrGC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1C,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAE,aAAaN,KAAK,CAAC8C,aAAa,CAAC5C,KAAK,EAAE;IACzCiD,GAAG,EAAE3C,QAAQ;IACbF,SAAS,EAAEA,SAAS;IACpBC,EAAE,EAAEA,EAAE;IACNO,IAAI,EAAEA,IAAI;IACVT,YAAY,EAAEA,YAAY;IAC1BI,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1ByC,QAAQ,EAAElB,aAAa;IACvBtB,kBAAkB,EAAEA,kBAAkB;IACtCyC,KAAK,EAAEjB,UAAU;IACjBkB,SAAS,EAAEhC,cAAc;IACzBiC,WAAW,EAAEhC,gBAAgB;IAC7BiC,QAAQ,EAAE,SAASA,QAAQA,CAACC,CAAC,EAAE;MAC7BzB,eAAe,CAAC,IAAI,CAAC;MACrBR,aAAa,CAACiC,CAAC,CAAC;IAClB,CAAC;IACDC,OAAO,EAAEjC,YAAY;IACrBkC,kBAAkB,EAAEjC,uBAAuB;IAC3CkC,gBAAgB,EAAEjC,qBAAqB;IACvCV,QAAQ,EAAEA,QAAQ;IAClB4C,KAAK,EAAE5D,SAAS,CAACG,KAAK,EAAE,IAAI,CAAC;IAC7BiB,SAAS,EAAEY,QAAQ,GAAGZ,SAAS,GAAGqB;EACpC,CAAC,CAAC,CAAC,EAAE,CAACT,QAAQ,IAAIE,IAAI,IAAI,CAACG,YAAY,IAAI,aAAatC,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;IAClFC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1C,SAAS,EAAE,iBAAiB,CAAC;IAClDiC,KAAK,EAAEA;EACT,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAAC,EAAEG,iBAAiB,CAAC,CAAC,CAAC;AACtC,CAAC;AAED,eAAexC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}