{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AACH,OAAO,EAAE,CAAC,EAAE,MAAM,WAAW,CAAA;AAC7B,cAAc,WAAW,CAAA;AACzB,eAAe,CAAC,CAAA", "sourcesContent": ["/**\n * The main tap export, ESM style\n *\n * Essentially just a re-export of everything defined in the index,\n * but with the `t` member as the default export.\n *\n * @module\n */\nimport { t } from './main.js'\nexport * from './main.js'\nexport default t\n"]}