{"ast": null, "code": "import _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nfunction defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\n\n// eslint-disable-next-line no-control-regex\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nvar serializeCookie = function serializeCookie(name, val, options) {\n  var opt = options || {};\n  opt.path = opt.path || '/';\n  var value = encodeURIComponent(val);\n  var str = name + '=' + value;\n  if (opt.maxAge > 0) {\n    var maxAge = opt.maxAge - 0;\n    if (isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += '; Max-Age=' + Math.floor(maxAge);\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += '; Domain=' + opt.domain;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += '; Path=' + opt.path;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += '; Expires=' + opt.expires.toUTCString();\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  return str;\n};\nvar cookie = {\n  create: function create(name, value, minutes, domain) {\n    var cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read: function read(name) {\n    var nameEQ = name + '=';\n    var ca = document.cookie.split(';');\n    for (var i = 0; i < ca.length; i++) {\n      var c = ca[i];\n      while (c.charAt(0) === ' ') {\n        c = c.substring(1, c.length);\n      }\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove: function remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      var c = cookie.read(options.lookupCookie);\n      if (c) found = c;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      cookie.create(options.lookupCookie, lng, options.cookieMinutes, options.cookieDomain, options.cookieOptions);\n    }\n  }\n};\nvar querystring = {\n  name: 'querystring',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var search = window.location.search;\n      if (!window.location.search && window.location.hash && window.location.hash.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      var query = search.substring(1);\n      var params = query.split('&');\n      for (var i = 0; i < params.length; i++) {\n        var pos = params[i].indexOf('=');\n        if (pos > 0) {\n          var key = params[i].substring(0, pos);\n          if (key === options.lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\nvar hasLocalStorageSupport = null;\nvar localStorageAvailable = function localStorageAvailable() {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = window !== 'undefined' && window.localStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      var lng = window.localStorage.getItem(options.lookupLocalStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(options.lookupLocalStorage, lng);\n    }\n  }\n};\nvar hasSessionStorageSupport = null;\nvar sessionStorageAvailable = function sessionStorageAvailable() {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = window !== 'undefined' && window.sessionStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      var lng = window.sessionStorage.getItem(options.lookupSessionStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(options.lookupSessionStorage, lng);\n    }\n  }\n};\nvar navigator$1 = {\n  name: 'navigator',\n  lookup: function lookup(options) {\n    var found = [];\n    if (typeof navigator !== 'undefined') {\n      if (navigator.languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (var i = 0; i < navigator.languages.length; i++) {\n          found.push(navigator.languages[i]);\n        }\n      }\n      if (navigator.userLanguage) {\n        found.push(navigator.userLanguage);\n      }\n      if (navigator.language) {\n        found.push(navigator.language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\nvar htmlTag = {\n  name: 'htmlTag',\n  lookup: function lookup(options) {\n    var found;\n    var htmlTag = options.htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (htmlTag && typeof htmlTag.getAttribute === 'function') {\n      found = htmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\nvar path = {\n  name: 'path',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n      if (language instanceof Array) {\n        if (typeof options.lookupFromPathIndex === 'number') {\n          if (typeof language[options.lookupFromPathIndex] !== 'string') {\n            return undefined;\n          }\n          found = language[options.lookupFromPathIndex].replace('/', '');\n        } else {\n          found = language[0].replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\nvar subdomain = {\n  name: 'subdomain',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var language = window.location.href.match(/(?:http[s]*\\:\\/\\/)*(.*?)\\.(?=[^\\/]*\\..{2,5})/gi);\n      if (language instanceof Array) {\n        if (typeof options.lookupFromSubdomainIndex === 'number') {\n          found = language[options.lookupFromSubdomainIndex].replace('http://', '').replace('https://', '').replace('.', '');\n        } else {\n          found = language[0].replace('http://', '').replace('https://', '').replace('.', '');\n        }\n      }\n    }\n    return found;\n  }\n};\nfunction getDefaults() {\n  return {\n    order: ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'],\n    lookupQuerystring: 'lng',\n    lookupCookie: 'i18next',\n    lookupLocalStorage: 'i18nextLng',\n    lookupSessionStorage: 'i18nextLng',\n    // cache user language\n    caches: ['localStorage'],\n    excludeCacheFor: ['cimode'] //cookieMinutes: 10,\n    //cookieDomain: 'myDomain'\n  };\n}\nvar Browser = /*#__PURE__*/function () {\n  function Browser(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Browser);\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  _createClass(Browser, [{\n    key: \"init\",\n    value: function init(services) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = defaults(options, this.options || {}, getDefaults()); // backwards compatibility\n\n      if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n      this.i18nOptions = i18nOptions;\n      this.addDetector(cookie$1);\n      this.addDetector(querystring);\n      this.addDetector(localStorage);\n      this.addDetector(sessionStorage);\n      this.addDetector(navigator$1);\n      this.addDetector(htmlTag);\n      this.addDetector(path);\n      this.addDetector(subdomain);\n    }\n  }, {\n    key: \"addDetector\",\n    value: function addDetector(detector) {\n      this.detectors[detector.name] = detector;\n    }\n  }, {\n    key: \"detect\",\n    value: function detect(detectionOrder) {\n      var _this = this;\n      if (!detectionOrder) detectionOrder = this.options.order;\n      var detected = [];\n      detectionOrder.forEach(function (detectorName) {\n        if (_this.detectors[detectorName]) {\n          var lookup = _this.detectors[detectorName].lookup(_this.options);\n          if (lookup && typeof lookup === 'string') lookup = [lookup];\n          if (lookup) detected = detected.concat(lookup);\n        }\n      });\n      if (this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n\n      return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n    }\n  }, {\n    key: \"cacheUserLanguage\",\n    value: function cacheUserLanguage(lng, caches) {\n      var _this2 = this;\n      if (!caches) caches = this.options.caches;\n      if (!caches) return;\n      if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n      caches.forEach(function (cacheName) {\n        if (_this2.detectors[cacheName]) _this2.detectors[cacheName].cacheUserLanguage(lng, _this2.options);\n      });\n    }\n  }]);\n  return Browser;\n}();\nBrowser.type = 'languageDetector';\nexport { Browser as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "arr", "each", "for<PERSON>ach", "slice", "defaults", "obj", "call", "arguments", "source", "prop", "undefined", "fieldContentRegExp", "serializeCookie", "name", "val", "options", "opt", "path", "value", "encodeURIComponent", "str", "maxAge", "isNaN", "Error", "Math", "floor", "domain", "test", "TypeError", "expires", "toUTCString", "httpOnly", "secure", "sameSite", "toLowerCase", "cookie", "create", "minutes", "cookieOptions", "length", "Date", "setTime", "getTime", "document", "read", "nameEQ", "ca", "split", "i", "c", "char<PERSON>t", "substring", "indexOf", "remove", "cookie$1", "lookup", "found", "lookup<PERSON><PERSON><PERSON>", "cacheUserLanguage", "lng", "cookieMinutes", "cookieDomain", "querystring", "window", "search", "location", "hash", "query", "params", "pos", "key", "lookupQuerystring", "hasLocalStorageSupport", "localStorageAvailable", "localStorage", "<PERSON><PERSON><PERSON>", "setItem", "removeItem", "e", "lookupLocalStorage", "getItem", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "navigator", "languages", "push", "userLanguage", "language", "htmlTag", "documentElement", "getAttribute", "pathname", "match", "Array", "lookupFromPathIndex", "replace", "subdomain", "href", "lookupFromSubdomainIndex", "getDefaults", "order", "caches", "excludeCache<PERSON>or", "Browser", "services", "type", "detectors", "init", "i18nOptions", "lookupFromUrlIndex", "addDetector", "detector", "detect", "detectionOrder", "_this", "detected", "detectorName", "concat", "languageUtils", "getBestMatchFromCodes", "_this2", "cacheName", "default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["import _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\n\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nfunction defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\n\n// eslint-disable-next-line no-control-regex\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n\nvar serializeCookie = function serializeCookie(name, val, options) {\n  var opt = options || {};\n  opt.path = opt.path || '/';\n  var value = encodeURIComponent(val);\n  var str = name + '=' + value;\n\n  if (opt.maxAge > 0) {\n    var maxAge = opt.maxAge - 0;\n    if (isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += '; Max-Age=' + Math.floor(maxAge);\n  }\n\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + opt.expires.toUTCString();\n  }\n\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n\n      case 'none':\n        str += '; SameSite=None';\n        break;\n\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n};\n\nvar cookie = {\n  create: function create(name, value, minutes, domain) {\n    var cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read: function read(name) {\n    var nameEQ = name + '=';\n    var ca = document.cookie.split(';');\n\n    for (var i = 0; i < ca.length; i++) {\n      var c = ca[i];\n\n      while (c.charAt(0) === ' ') {\n        c = c.substring(1, c.length);\n      }\n\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n\n    return null;\n  },\n  remove: function remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  lookup: function lookup(options) {\n    var found;\n\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      var c = cookie.read(options.lookupCookie);\n      if (c) found = c;\n    }\n\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      cookie.create(options.lookupCookie, lng, options.cookieMinutes, options.cookieDomain, options.cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  lookup: function lookup(options) {\n    var found;\n\n    if (typeof window !== 'undefined') {\n      var search = window.location.search;\n\n      if (!window.location.search && window.location.hash && window.location.hash.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n\n      var query = search.substring(1);\n      var params = query.split('&');\n\n      for (var i = 0; i < params.length; i++) {\n        var pos = params[i].indexOf('=');\n\n        if (pos > 0) {\n          var key = params[i].substring(0, pos);\n\n          if (key === options.lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n\n    return found;\n  }\n};\n\nvar hasLocalStorageSupport = null;\n\nvar localStorageAvailable = function localStorageAvailable() {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n\n  try {\n    hasLocalStorageSupport = window !== 'undefined' && window.localStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n\n  return hasLocalStorageSupport;\n};\n\nvar localStorage = {\n  name: 'localStorage',\n  lookup: function lookup(options) {\n    var found;\n\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      var lng = window.localStorage.getItem(options.lookupLocalStorage);\n      if (lng) found = lng;\n    }\n\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(options.lookupLocalStorage, lng);\n    }\n  }\n};\n\nvar hasSessionStorageSupport = null;\n\nvar sessionStorageAvailable = function sessionStorageAvailable() {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n\n  try {\n    hasSessionStorageSupport = window !== 'undefined' && window.sessionStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n\n  return hasSessionStorageSupport;\n};\n\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup: function lookup(options) {\n    var found;\n\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      var lng = window.sessionStorage.getItem(options.lookupSessionStorage);\n      if (lng) found = lng;\n    }\n\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(options.lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup: function lookup(options) {\n    var found = [];\n\n    if (typeof navigator !== 'undefined') {\n      if (navigator.languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (var i = 0; i < navigator.languages.length; i++) {\n          found.push(navigator.languages[i]);\n        }\n      }\n\n      if (navigator.userLanguage) {\n        found.push(navigator.userLanguage);\n      }\n\n      if (navigator.language) {\n        found.push(navigator.language);\n      }\n    }\n\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  lookup: function lookup(options) {\n    var found;\n    var htmlTag = options.htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n\n    if (htmlTag && typeof htmlTag.getAttribute === 'function') {\n      found = htmlTag.getAttribute('lang');\n    }\n\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  lookup: function lookup(options) {\n    var found;\n\n    if (typeof window !== 'undefined') {\n      var language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n\n      if (language instanceof Array) {\n        if (typeof options.lookupFromPathIndex === 'number') {\n          if (typeof language[options.lookupFromPathIndex] !== 'string') {\n            return undefined;\n          }\n\n          found = language[options.lookupFromPathIndex].replace('/', '');\n        } else {\n          found = language[0].replace('/', '');\n        }\n      }\n    }\n\n    return found;\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup: function lookup(options) {\n    var found;\n\n    if (typeof window !== 'undefined') {\n      var language = window.location.href.match(/(?:http[s]*\\:\\/\\/)*(.*?)\\.(?=[^\\/]*\\..{2,5})/gi);\n\n      if (language instanceof Array) {\n        if (typeof options.lookupFromSubdomainIndex === 'number') {\n          found = language[options.lookupFromSubdomainIndex].replace('http://', '').replace('https://', '').replace('.', '');\n        } else {\n          found = language[0].replace('http://', '').replace('https://', '').replace('.', '');\n        }\n      }\n    }\n\n    return found;\n  }\n};\n\nfunction getDefaults() {\n  return {\n    order: ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'],\n    lookupQuerystring: 'lng',\n    lookupCookie: 'i18next',\n    lookupLocalStorage: 'i18nextLng',\n    lookupSessionStorage: 'i18nextLng',\n    // cache user language\n    caches: ['localStorage'],\n    excludeCacheFor: ['cimode'] //cookieMinutes: 10,\n    //cookieDomain: 'myDomain'\n\n  };\n}\n\nvar Browser = /*#__PURE__*/function () {\n  function Browser(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Browser);\n\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n\n  _createClass(Browser, [{\n    key: \"init\",\n    value: function init(services) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = defaults(options, this.options || {}, getDefaults()); // backwards compatibility\n\n      if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n      this.i18nOptions = i18nOptions;\n      this.addDetector(cookie$1);\n      this.addDetector(querystring);\n      this.addDetector(localStorage);\n      this.addDetector(sessionStorage);\n      this.addDetector(navigator$1);\n      this.addDetector(htmlTag);\n      this.addDetector(path);\n      this.addDetector(subdomain);\n    }\n  }, {\n    key: \"addDetector\",\n    value: function addDetector(detector) {\n      this.detectors[detector.name] = detector;\n    }\n  }, {\n    key: \"detect\",\n    value: function detect(detectionOrder) {\n      var _this = this;\n\n      if (!detectionOrder) detectionOrder = this.options.order;\n      var detected = [];\n      detectionOrder.forEach(function (detectorName) {\n        if (_this.detectors[detectorName]) {\n          var lookup = _this.detectors[detectorName].lookup(_this.options);\n\n          if (lookup && typeof lookup === 'string') lookup = [lookup];\n          if (lookup) detected = detected.concat(lookup);\n        }\n      });\n      if (this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n\n      return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n    }\n  }, {\n    key: \"cacheUserLanguage\",\n    value: function cacheUserLanguage(lng, caches) {\n      var _this2 = this;\n\n      if (!caches) caches = this.options.caches;\n      if (!caches) return;\n      if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n      caches.forEach(function (cacheName) {\n        if (_this2.detectors[cacheName]) _this2.detectors[cacheName].cacheUserLanguage(lng, _this2.options);\n      });\n    }\n  }]);\n\n  return Browser;\n}();\n\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AAEjE,IAAIC,GAAG,GAAG,EAAE;AACZ,IAAIC,IAAI,GAAGD,GAAG,CAACE,OAAO;AACtB,IAAIC,KAAK,GAAGH,GAAG,CAACG,KAAK;AACrB,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrBJ,IAAI,CAACK,IAAI,CAACH,KAAK,CAACG,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC,EAAE,UAAUC,MAAM,EAAE;IACpD,IAAIA,MAAM,EAAE;MACV,KAAK,IAAIC,IAAI,IAAID,MAAM,EAAE;QACvB,IAAIH,GAAG,CAACI,IAAI,CAAC,KAAKC,SAAS,EAAEL,GAAG,CAACI,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ;;AAEA;AACA,IAAIM,kBAAkB,GAAG,uCAAuC;AAEhE,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACjE,IAAIC,GAAG,GAAGD,OAAO,IAAI,CAAC,CAAC;EACvBC,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,IAAI,GAAG;EAC1B,IAAIC,KAAK,GAAGC,kBAAkB,CAACL,GAAG,CAAC;EACnC,IAAIM,GAAG,GAAGP,IAAI,GAAG,GAAG,GAAGK,KAAK;EAE5B,IAAIF,GAAG,CAACK,MAAM,GAAG,CAAC,EAAE;IAClB,IAAIA,MAAM,GAAGL,GAAG,CAACK,MAAM,GAAG,CAAC;IAC3B,IAAIC,KAAK,CAACD,MAAM,CAAC,EAAE,MAAM,IAAIE,KAAK,CAAC,2BAA2B,CAAC;IAC/DH,GAAG,IAAI,YAAY,GAAGI,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;EAC1C;EAEA,IAAIL,GAAG,CAACU,MAAM,EAAE;IACd,IAAI,CAACf,kBAAkB,CAACgB,IAAI,CAACX,GAAG,CAACU,MAAM,CAAC,EAAE;MACxC,MAAM,IAAIE,SAAS,CAAC,0BAA0B,CAAC;IACjD;IAEAR,GAAG,IAAI,WAAW,GAAGJ,GAAG,CAACU,MAAM;EACjC;EAEA,IAAIV,GAAG,CAACC,IAAI,EAAE;IACZ,IAAI,CAACN,kBAAkB,CAACgB,IAAI,CAACX,GAAG,CAACC,IAAI,CAAC,EAAE;MACtC,MAAM,IAAIW,SAAS,CAAC,wBAAwB,CAAC;IAC/C;IAEAR,GAAG,IAAI,SAAS,GAAGJ,GAAG,CAACC,IAAI;EAC7B;EAEA,IAAID,GAAG,CAACa,OAAO,EAAE;IACf,IAAI,OAAOb,GAAG,CAACa,OAAO,CAACC,WAAW,KAAK,UAAU,EAAE;MACjD,MAAM,IAAIF,SAAS,CAAC,2BAA2B,CAAC;IAClD;IAEAR,GAAG,IAAI,YAAY,GAAGJ,GAAG,CAACa,OAAO,CAACC,WAAW,CAAC,CAAC;EACjD;EAEA,IAAId,GAAG,CAACe,QAAQ,EAAEX,GAAG,IAAI,YAAY;EACrC,IAAIJ,GAAG,CAACgB,MAAM,EAAEZ,GAAG,IAAI,UAAU;EAEjC,IAAIJ,GAAG,CAACiB,QAAQ,EAAE;IAChB,IAAIA,QAAQ,GAAG,OAAOjB,GAAG,CAACiB,QAAQ,KAAK,QAAQ,GAAGjB,GAAG,CAACiB,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAGlB,GAAG,CAACiB,QAAQ;IAE3F,QAAQA,QAAQ;MACd,KAAK,IAAI;QACPb,GAAG,IAAI,mBAAmB;QAC1B;MAEF,KAAK,KAAK;QACRA,GAAG,IAAI,gBAAgB;QACvB;MAEF,KAAK,QAAQ;QACXA,GAAG,IAAI,mBAAmB;QAC1B;MAEF,KAAK,MAAM;QACTA,GAAG,IAAI,iBAAiB;QACxB;MAEF;QACE,MAAM,IAAIQ,SAAS,CAAC,4BAA4B,CAAC;IACrD;EACF;EAEA,OAAOR,GAAG;AACZ,CAAC;AAED,IAAIe,MAAM,GAAG;EACXC,MAAM,EAAE,SAASA,MAAMA,CAACvB,IAAI,EAAEK,KAAK,EAAEmB,OAAO,EAAEX,MAAM,EAAE;IACpD,IAAIY,aAAa,GAAG/B,SAAS,CAACgC,MAAM,GAAG,CAAC,IAAIhC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG;MACtFU,IAAI,EAAE,GAAG;MACTgB,QAAQ,EAAE;IACZ,CAAC;IAED,IAAII,OAAO,EAAE;MACXC,aAAa,CAACT,OAAO,GAAG,IAAIW,IAAI,CAAC,CAAC;MAClCF,aAAa,CAACT,OAAO,CAACY,OAAO,CAACH,aAAa,CAACT,OAAO,CAACa,OAAO,CAAC,CAAC,GAAGL,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;IACtF;IAEA,IAAIX,MAAM,EAAEY,aAAa,CAACZ,MAAM,GAAGA,MAAM;IACzCiB,QAAQ,CAACR,MAAM,GAAGvB,eAAe,CAACC,IAAI,EAAEM,kBAAkB,CAACD,KAAK,CAAC,EAAEoB,aAAa,CAAC;EACnF,CAAC;EACDM,IAAI,EAAE,SAASA,IAAIA,CAAC/B,IAAI,EAAE;IACxB,IAAIgC,MAAM,GAAGhC,IAAI,GAAG,GAAG;IACvB,IAAIiC,EAAE,GAAGH,QAAQ,CAACR,MAAM,CAACY,KAAK,CAAC,GAAG,CAAC;IAEnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,EAAE,CAACP,MAAM,EAAES,CAAC,EAAE,EAAE;MAClC,IAAIC,CAAC,GAAGH,EAAE,CAACE,CAAC,CAAC;MAEb,OAAOC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC1BD,CAAC,GAAGA,CAAC,CAACE,SAAS,CAAC,CAAC,EAAEF,CAAC,CAACV,MAAM,CAAC;MAC9B;MAEA,IAAIU,CAAC,CAACG,OAAO,CAACP,MAAM,CAAC,KAAK,CAAC,EAAE,OAAOI,CAAC,CAACE,SAAS,CAACN,MAAM,CAACN,MAAM,EAAEU,CAAC,CAACV,MAAM,CAAC;IAC1E;IAEA,OAAO,IAAI;EACb,CAAC;EACDc,MAAM,EAAE,SAASA,MAAMA,CAACxC,IAAI,EAAE;IAC5B,IAAI,CAACuB,MAAM,CAACvB,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EAC3B;AACF,CAAC;AACD,IAAIyC,QAAQ,GAAG;EACbzC,IAAI,EAAE,QAAQ;EACd0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IAET,IAAIzC,OAAO,CAAC0C,YAAY,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;MAC3D,IAAIM,CAAC,GAAGd,MAAM,CAACS,IAAI,CAAC7B,OAAO,CAAC0C,YAAY,CAAC;MACzC,IAAIR,CAAC,EAAEO,KAAK,GAAGP,CAAC;IAClB;IAEA,OAAOO,KAAK;EACd,CAAC;EACDE,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,GAAG,EAAE5C,OAAO,EAAE;IAC1D,IAAIA,OAAO,CAAC0C,YAAY,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;MAC3DR,MAAM,CAACC,MAAM,CAACrB,OAAO,CAAC0C,YAAY,EAAEE,GAAG,EAAE5C,OAAO,CAAC6C,aAAa,EAAE7C,OAAO,CAAC8C,YAAY,EAAE9C,OAAO,CAACuB,aAAa,CAAC;IAC9G;EACF;AACF,CAAC;AAED,IAAIwB,WAAW,GAAG;EAChBjD,IAAI,EAAE,aAAa;EACnB0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IAET,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAE;MACjC,IAAIC,MAAM,GAAGD,MAAM,CAACE,QAAQ,CAACD,MAAM;MAEnC,IAAI,CAACD,MAAM,CAACE,QAAQ,CAACD,MAAM,IAAID,MAAM,CAACE,QAAQ,CAACC,IAAI,IAAIH,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACd,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7FY,MAAM,GAAGD,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACf,SAAS,CAACY,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACd,OAAO,CAAC,GAAG,CAAC,CAAC;MAC5E;MAEA,IAAIe,KAAK,GAAGH,MAAM,CAACb,SAAS,CAAC,CAAC,CAAC;MAC/B,IAAIiB,MAAM,GAAGD,KAAK,CAACpB,KAAK,CAAC,GAAG,CAAC;MAE7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,CAAC7B,MAAM,EAAES,CAAC,EAAE,EAAE;QACtC,IAAIqB,GAAG,GAAGD,MAAM,CAACpB,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,CAAC;QAEhC,IAAIiB,GAAG,GAAG,CAAC,EAAE;UACX,IAAIC,GAAG,GAAGF,MAAM,CAACpB,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC,EAAEkB,GAAG,CAAC;UAErC,IAAIC,GAAG,KAAKvD,OAAO,CAACwD,iBAAiB,EAAE;YACrCf,KAAK,GAAGY,MAAM,CAACpB,CAAC,CAAC,CAACG,SAAS,CAACkB,GAAG,GAAG,CAAC,CAAC;UACtC;QACF;MACF;IACF;IAEA,OAAOb,KAAK;EACd;AACF,CAAC;AAED,IAAIgB,sBAAsB,GAAG,IAAI;AAEjC,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;EAC3D,IAAID,sBAAsB,KAAK,IAAI,EAAE,OAAOA,sBAAsB;EAElE,IAAI;IACFA,sBAAsB,GAAGT,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACW,YAAY,KAAK,IAAI;IAC/E,IAAIC,OAAO,GAAG,uBAAuB;IACrCZ,MAAM,CAACW,YAAY,CAACE,OAAO,CAACD,OAAO,EAAE,KAAK,CAAC;IAC3CZ,MAAM,CAACW,YAAY,CAACG,UAAU,CAACF,OAAO,CAAC;EACzC,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVN,sBAAsB,GAAG,KAAK;EAChC;EAEA,OAAOA,sBAAsB;AAC/B,CAAC;AAED,IAAIE,YAAY,GAAG;EACjB7D,IAAI,EAAE,cAAc;EACpB0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IAET,IAAIzC,OAAO,CAACgE,kBAAkB,IAAIN,qBAAqB,CAAC,CAAC,EAAE;MACzD,IAAId,GAAG,GAAGI,MAAM,CAACW,YAAY,CAACM,OAAO,CAACjE,OAAO,CAACgE,kBAAkB,CAAC;MACjE,IAAIpB,GAAG,EAAEH,KAAK,GAAGG,GAAG;IACtB;IAEA,OAAOH,KAAK;EACd,CAAC;EACDE,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,GAAG,EAAE5C,OAAO,EAAE;IAC1D,IAAIA,OAAO,CAACgE,kBAAkB,IAAIN,qBAAqB,CAAC,CAAC,EAAE;MACzDV,MAAM,CAACW,YAAY,CAACE,OAAO,CAAC7D,OAAO,CAACgE,kBAAkB,EAAEpB,GAAG,CAAC;IAC9D;EACF;AACF,CAAC;AAED,IAAIsB,wBAAwB,GAAG,IAAI;AAEnC,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;EAC/D,IAAID,wBAAwB,KAAK,IAAI,EAAE,OAAOA,wBAAwB;EAEtE,IAAI;IACFA,wBAAwB,GAAGlB,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACoB,cAAc,KAAK,IAAI;IACnF,IAAIR,OAAO,GAAG,uBAAuB;IACrCZ,MAAM,CAACoB,cAAc,CAACP,OAAO,CAACD,OAAO,EAAE,KAAK,CAAC;IAC7CZ,MAAM,CAACoB,cAAc,CAACN,UAAU,CAACF,OAAO,CAAC;EAC3C,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVG,wBAAwB,GAAG,KAAK;EAClC;EAEA,OAAOA,wBAAwB;AACjC,CAAC;AAED,IAAIE,cAAc,GAAG;EACnBtE,IAAI,EAAE,gBAAgB;EACtB0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IAET,IAAIzC,OAAO,CAACqE,oBAAoB,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MAC7D,IAAIvB,GAAG,GAAGI,MAAM,CAACoB,cAAc,CAACH,OAAO,CAACjE,OAAO,CAACqE,oBAAoB,CAAC;MACrE,IAAIzB,GAAG,EAAEH,KAAK,GAAGG,GAAG;IACtB;IAEA,OAAOH,KAAK;EACd,CAAC;EACDE,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,GAAG,EAAE5C,OAAO,EAAE;IAC1D,IAAIA,OAAO,CAACqE,oBAAoB,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MAC7DnB,MAAM,CAACoB,cAAc,CAACP,OAAO,CAAC7D,OAAO,CAACqE,oBAAoB,EAAEzB,GAAG,CAAC;IAClE;EACF;AACF,CAAC;AAED,IAAI0B,WAAW,GAAG;EAChBxE,IAAI,EAAE,WAAW;EACjB0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK,GAAG,EAAE;IAEd,IAAI,OAAO8B,SAAS,KAAK,WAAW,EAAE;MACpC,IAAIA,SAAS,CAACC,SAAS,EAAE;QACvB;QACA,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,SAAS,CAACC,SAAS,CAAChD,MAAM,EAAES,CAAC,EAAE,EAAE;UACnDQ,KAAK,CAACgC,IAAI,CAACF,SAAS,CAACC,SAAS,CAACvC,CAAC,CAAC,CAAC;QACpC;MACF;MAEA,IAAIsC,SAAS,CAACG,YAAY,EAAE;QAC1BjC,KAAK,CAACgC,IAAI,CAACF,SAAS,CAACG,YAAY,CAAC;MACpC;MAEA,IAAIH,SAAS,CAACI,QAAQ,EAAE;QACtBlC,KAAK,CAACgC,IAAI,CAACF,SAAS,CAACI,QAAQ,CAAC;MAChC;IACF;IAEA,OAAOlC,KAAK,CAACjB,MAAM,GAAG,CAAC,GAAGiB,KAAK,GAAG9C,SAAS;EAC7C;AACF,CAAC;AAED,IAAIiF,OAAO,GAAG;EACZ9E,IAAI,EAAE,SAAS;EACf0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IACT,IAAImC,OAAO,GAAG5E,OAAO,CAAC4E,OAAO,KAAK,OAAOhD,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACiD,eAAe,GAAG,IAAI,CAAC;IAEpG,IAAID,OAAO,IAAI,OAAOA,OAAO,CAACE,YAAY,KAAK,UAAU,EAAE;MACzDrC,KAAK,GAAGmC,OAAO,CAACE,YAAY,CAAC,MAAM,CAAC;IACtC;IAEA,OAAOrC,KAAK;EACd;AACF,CAAC;AAED,IAAIvC,IAAI,GAAG;EACTJ,IAAI,EAAE,MAAM;EACZ0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IAET,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAE;MACjC,IAAI2B,QAAQ,GAAG3B,MAAM,CAACE,QAAQ,CAAC6B,QAAQ,CAACC,KAAK,CAAC,iBAAiB,CAAC;MAEhE,IAAIL,QAAQ,YAAYM,KAAK,EAAE;QAC7B,IAAI,OAAOjF,OAAO,CAACkF,mBAAmB,KAAK,QAAQ,EAAE;UACnD,IAAI,OAAOP,QAAQ,CAAC3E,OAAO,CAACkF,mBAAmB,CAAC,KAAK,QAAQ,EAAE;YAC7D,OAAOvF,SAAS;UAClB;UAEA8C,KAAK,GAAGkC,QAAQ,CAAC3E,OAAO,CAACkF,mBAAmB,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QAChE,CAAC,MAAM;UACL1C,KAAK,GAAGkC,QAAQ,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QACtC;MACF;IACF;IAEA,OAAO1C,KAAK;EACd;AACF,CAAC;AAED,IAAI2C,SAAS,GAAG;EACdtF,IAAI,EAAE,WAAW;EACjB0C,MAAM,EAAE,SAASA,MAAMA,CAACxC,OAAO,EAAE;IAC/B,IAAIyC,KAAK;IAET,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAE;MACjC,IAAI2B,QAAQ,GAAG3B,MAAM,CAACE,QAAQ,CAACmC,IAAI,CAACL,KAAK,CAAC,gDAAgD,CAAC;MAE3F,IAAIL,QAAQ,YAAYM,KAAK,EAAE;QAC7B,IAAI,OAAOjF,OAAO,CAACsF,wBAAwB,KAAK,QAAQ,EAAE;UACxD7C,KAAK,GAAGkC,QAAQ,CAAC3E,OAAO,CAACsF,wBAAwB,CAAC,CAACH,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QACpH,CAAC,MAAM;UACL1C,KAAK,GAAGkC,QAAQ,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QACrF;MACF;IACF;IAEA,OAAO1C,KAAK;EACd;AACF,CAAC;AAED,SAAS8C,WAAWA,CAAA,EAAG;EACrB,OAAO;IACLC,KAAK,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,CAAC;IAC1FhC,iBAAiB,EAAE,KAAK;IACxBd,YAAY,EAAE,SAAS;IACvBsB,kBAAkB,EAAE,YAAY;IAChCK,oBAAoB,EAAE,YAAY;IAClC;IACAoB,MAAM,EAAE,CAAC,cAAc,CAAC;IACxBC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC5B;EAEF,CAAC;AACH;AAEA,IAAIC,OAAO,GAAG,aAAa,YAAY;EACrC,SAASA,OAAOA,CAACC,QAAQ,EAAE;IACzB,IAAI5F,OAAO,GAAGR,SAAS,CAACgC,MAAM,GAAG,CAAC,IAAIhC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpFT,eAAe,CAAC,IAAI,EAAE4G,OAAO,CAAC;IAE9B,IAAI,CAACE,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,IAAI,CAACH,QAAQ,EAAE5F,OAAO,CAAC;EAC9B;EAEAhB,YAAY,CAAC2G,OAAO,EAAE,CAAC;IACrBpC,GAAG,EAAE,MAAM;IACXpD,KAAK,EAAE,SAAS4F,IAAIA,CAACH,QAAQ,EAAE;MAC7B,IAAI5F,OAAO,GAAGR,SAAS,CAACgC,MAAM,GAAG,CAAC,IAAIhC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIwG,WAAW,GAAGxG,SAAS,CAACgC,MAAM,GAAG,CAAC,IAAIhC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACxF,IAAI,CAACoG,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC5F,OAAO,GAAGX,QAAQ,CAACW,OAAO,EAAE,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAEuF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErE,IAAI,IAAI,CAACvF,OAAO,CAACiG,kBAAkB,EAAE,IAAI,CAACjG,OAAO,CAACkF,mBAAmB,GAAG,IAAI,CAAClF,OAAO,CAACiG,kBAAkB;MACvG,IAAI,CAACD,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACE,WAAW,CAAC3D,QAAQ,CAAC;MAC1B,IAAI,CAAC2D,WAAW,CAACnD,WAAW,CAAC;MAC7B,IAAI,CAACmD,WAAW,CAACvC,YAAY,CAAC;MAC9B,IAAI,CAACuC,WAAW,CAAC9B,cAAc,CAAC;MAChC,IAAI,CAAC8B,WAAW,CAAC5B,WAAW,CAAC;MAC7B,IAAI,CAAC4B,WAAW,CAACtB,OAAO,CAAC;MACzB,IAAI,CAACsB,WAAW,CAAChG,IAAI,CAAC;MACtB,IAAI,CAACgG,WAAW,CAACd,SAAS,CAAC;IAC7B;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,aAAa;IAClBpD,KAAK,EAAE,SAAS+F,WAAWA,CAACC,QAAQ,EAAE;MACpC,IAAI,CAACL,SAAS,CAACK,QAAQ,CAACrG,IAAI,CAAC,GAAGqG,QAAQ;IAC1C;EACF,CAAC,EAAE;IACD5C,GAAG,EAAE,QAAQ;IACbpD,KAAK,EAAE,SAASiG,MAAMA,CAACC,cAAc,EAAE;MACrC,IAAIC,KAAK,GAAG,IAAI;MAEhB,IAAI,CAACD,cAAc,EAAEA,cAAc,GAAG,IAAI,CAACrG,OAAO,CAACwF,KAAK;MACxD,IAAIe,QAAQ,GAAG,EAAE;MACjBF,cAAc,CAAClH,OAAO,CAAC,UAAUqH,YAAY,EAAE;QAC7C,IAAIF,KAAK,CAACR,SAAS,CAACU,YAAY,CAAC,EAAE;UACjC,IAAIhE,MAAM,GAAG8D,KAAK,CAACR,SAAS,CAACU,YAAY,CAAC,CAAChE,MAAM,CAAC8D,KAAK,CAACtG,OAAO,CAAC;UAEhE,IAAIwC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAEA,MAAM,GAAG,CAACA,MAAM,CAAC;UAC3D,IAAIA,MAAM,EAAE+D,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACjE,MAAM,CAAC;QAChD;MACF,CAAC,CAAC;MACF,IAAI,IAAI,CAACoD,QAAQ,CAACc,aAAa,CAACC,qBAAqB,EAAE,OAAOJ,QAAQ,CAAC,CAAC;;MAExE,OAAOA,QAAQ,CAAC/E,MAAM,GAAG,CAAC,GAAG+E,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACnD;EACF,CAAC,EAAE;IACDhD,GAAG,EAAE,mBAAmB;IACxBpD,KAAK,EAAE,SAASwC,iBAAiBA,CAACC,GAAG,EAAE6C,MAAM,EAAE;MAC7C,IAAImB,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACnB,MAAM,EAAEA,MAAM,GAAG,IAAI,CAACzF,OAAO,CAACyF,MAAM;MACzC,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,IAAI,CAACzF,OAAO,CAAC0F,eAAe,IAAI,IAAI,CAAC1F,OAAO,CAAC0F,eAAe,CAACrD,OAAO,CAACO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACpF6C,MAAM,CAACtG,OAAO,CAAC,UAAU0H,SAAS,EAAE;QAClC,IAAID,MAAM,CAACd,SAAS,CAACe,SAAS,CAAC,EAAED,MAAM,CAACd,SAAS,CAACe,SAAS,CAAC,CAAClE,iBAAiB,CAACC,GAAG,EAAEgE,MAAM,CAAC5G,OAAO,CAAC;MACrG,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EAEH,OAAO2F,OAAO;AAChB,CAAC,CAAC,CAAC;AAEHA,OAAO,CAACE,IAAI,GAAG,kBAAkB;AAEjC,SAASF,OAAO,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}