{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\dashboard\\\\dashboardAffiliato.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardAffiliato - dashboard \n*\n*/\nimport React, { Component } from \"react\";\nimport { Messages } from \"primereact/messages\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport { BannerUtili } from \"../../../components/generalizzazioni/statistiche/bannerUtili\";\nimport { BannerProd } from \"../../../components/generalizzazioni/statistiche/bannerProd\";\nimport { BannerProd2 } from \"../../../components/generalizzazioni/statistiche/bannerProd2\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from '../../../components/navigation/dashboard';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass DashboardAffiliato extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      disabled: false,\n      results: null,\n      loading: true,\n      disabledMex: \"\"\n    };\n  }\n  async componentDidMount() {\n    this.msgs1.show([{\n      severity: 'error',\n      summary: '',\n      detail: 'Siamo spiacenti al suo profilo utente non è associato un affiliato per tanto le è impossibile compiere azioni',\n      sticky: true\n    }]);\n    var user = '';\n    user = JSON.parse(localStorage.getItem(\"userid\"));\n    if (user.affiliate === null) {\n      this.setState({\n        disabled: true\n      });\n    } else {\n      this.setState({\n        disabledMex: \"d-none\"\n      });\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: this.state.disabledMex,\n          children: /*#__PURE__*/_jsxDEV(Messages, {\n            ref: el => this.msgs1 = el\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"album pb-5\",\n          children: [/*#__PURE__*/_jsxDEV(BannerWelcome, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container mt-3 pb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card border-0\",\n              children: [/*#__PURE__*/_jsxDEV(Dashboard, {\n                disabled: this.state.disabled\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-columns mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(BannerUtili, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(BannerProd, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(BannerProd2, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)\n    }, void 0, false);\n  }\n}\nexport default DashboardAffiliato;", "map": {"version": 3, "names": ["React", "Component", "Messages", "BannerWelcome", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "BannerProd2", "Nav", "Dashboard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashboardAffiliato", "constructor", "props", "state", "disabled", "results", "loading", "disabledMex", "componentDidMount", "msgs1", "show", "severity", "summary", "detail", "sticky", "user", "JSON", "parse", "localStorage", "getItem", "affiliate", "setState", "render", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "el"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/dashboard/dashboardAffiliato.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardAffiliato - dashboard \n*\n*/\nimport React, { Component } from \"react\";\nimport { Messages } from \"primereact/messages\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport { BannerUtili } from \"../../../components/generalizzazioni/statistiche/bannerUtili\";\nimport { BannerProd } from \"../../../components/generalizzazioni/statistiche/bannerProd\";\nimport { BannerProd2 } from \"../../../components/generalizzazioni/statistiche/bannerProd2\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from '../../../components/navigation/dashboard'\n\nclass DashboardAffiliato extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            disabled: false,\n            results: null,\n            loading: true,\n            disabledMex: \"\"\n        }\n    }\n    async componentDidMount() {\n        this.msgs1.show([\n            { severity: 'error', summary: '', detail: 'Siamo spiacenti al suo profilo utente non è associato un affiliato per tanto le è impossibile compiere azioni', sticky: true }\n        ]);\n        var user = ''\n        user = JSON.parse(localStorage.getItem(\"userid\"))\n        if (user.affiliate === null) {\n            this.setState({\n                disabled: true,\n            })\n        } else {\n            this.setState({ disabledMex: \"d-none\" })\n        }\n    }\n    render() {\n        return (\n            <>\n                <div className=\"dashboard wrapper\">\n                    <Nav />\n                    <div className={this.state.disabledMex}>\n                        <Messages ref={(el) => this.msgs1 = el} />\n                    </div>\n                    <div className=\"album pb-5\">\n                        <BannerWelcome />\n                        <div className=\"container mt-3 pb-0\">\n                            <div className=\"card border-0\">\n                                <Dashboard disabled={this.state.disabled} />\n                                <div>\n                                    <hr></hr>\n                                </div>\n                                <div className='card-columns mt-4'>\n                                    <BannerUtili />\n                                    <BannerProd />\n                                    <BannerProd2 />\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </>\n        );\n    }\n}\n\nexport default DashboardAffiliato;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,aAAa,QAAQ,oDAAoD;AAClF,SAASC,WAAW,QAAQ,8DAA8D;AAC1F,SAASC,UAAU,QAAQ,6DAA6D;AACxF,SAASC,WAAW,QAAQ,8DAA8D;AAC1F,OAAOC,GAAG,MAAM,oCAAoC;AACpD,OAAOC,SAAS,MAAM,0CAA0C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhE,MAAMC,kBAAkB,SAASZ,SAAS,CAAC;EACvCa,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE;IACjB,CAAC;EACL;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACtB,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,CACZ;MAAEC,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,+GAA+G;MAAEC,MAAM,EAAE;IAAK,CAAC,CAC5K,CAAC;IACF,IAAIC,IAAI,GAAG,EAAE;IACbA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjD,IAAIJ,IAAI,CAACK,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACC,QAAQ,CAAC;QACVjB,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACiB,QAAQ,CAAC;QAAEd,WAAW,EAAE;MAAS,CAAC,CAAC;IAC5C;EACJ;EACAe,MAAMA,CAAA,EAAG;IACL,oBACIzB,OAAA,CAAAE,SAAA;MAAAwB,QAAA,eACI1B,OAAA;QAAK2B,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAC9B1B,OAAA,CAACH,GAAG;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACP/B,OAAA;UAAK2B,SAAS,EAAE,IAAI,CAACrB,KAAK,CAACI,WAAY;UAAAgB,QAAA,eACnC1B,OAAA,CAACR,QAAQ;YAACwC,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACrB,KAAK,GAAGqB;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN/B,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACvB1B,OAAA,CAACP,aAAa;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjB/B,OAAA;YAAK2B,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAChC1B,OAAA;cAAK2B,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC1B1B,OAAA,CAACF,SAAS;gBAACS,QAAQ,EAAE,IAAI,CAACD,KAAK,CAACC;cAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5C/B,OAAA;gBAAA0B,QAAA,eACI1B,OAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACN/B,OAAA;gBAAK2B,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9B1B,OAAA,CAACN,WAAW;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACf/B,OAAA,CAACL,UAAU;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACd/B,OAAA,CAACJ,WAAW;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC,gBACR,CAAC;EAEX;AACJ;AAEA,eAAe5B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}