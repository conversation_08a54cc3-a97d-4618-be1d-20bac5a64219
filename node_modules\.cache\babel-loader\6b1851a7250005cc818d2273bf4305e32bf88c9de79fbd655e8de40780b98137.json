{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport EyeInvisibleOutlined from \"@ant-design/icons/es/icons/EyeInvisibleOutlined\";\nimport { useState } from 'react';\nimport Input from './Input';\nimport { ConfigConsumer } from '../config-provider';\nvar ActionMap = {\n  click: 'onClick',\n  hover: 'onMouseOver'\n};\nvar Password = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    visible = _useState2[0],\n    setVisible = _useState2[1];\n  var onVisibleChange = function onVisibleChange() {\n    var disabled = props.disabled;\n    if (disabled) {\n      return;\n    }\n    setVisible(!visible);\n  };\n  var getIcon = function getIcon(prefixCls) {\n    var _iconProps;\n    var action = props.action,\n      _props$iconRender = props.iconRender,\n      iconRender = _props$iconRender === void 0 ? function () {\n        return null;\n      } : _props$iconRender;\n    var iconTrigger = ActionMap[action] || '';\n    var icon = iconRender(visible);\n    var iconProps = (_iconProps = {}, _defineProperty(_iconProps, iconTrigger, onVisibleChange), _defineProperty(_iconProps, \"className\", \"\".concat(prefixCls, \"-icon\")), _defineProperty(_iconProps, \"key\", 'passwordIcon'), _defineProperty(_iconProps, \"onMouseDown\", function onMouseDown(e) {\n      // Prevent focused state lost\n      // https://github.com/ant-design/ant-design/issues/15173\n      e.preventDefault();\n    }), _defineProperty(_iconProps, \"onMouseUp\", function onMouseUp(e) {\n      // Prevent caret position change\n      // https://github.com/ant-design/ant-design/issues/23524\n      e.preventDefault();\n    }), _iconProps);\n    return /*#__PURE__*/React.cloneElement(/*#__PURE__*/React.isValidElement(icon) ? icon : /*#__PURE__*/React.createElement(\"span\", null, icon), iconProps);\n  };\n  var renderPassword = function renderPassword(_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var className = props.className,\n      customizePrefixCls = props.prefixCls,\n      customizeInputPrefixCls = props.inputPrefixCls,\n      size = props.size,\n      visibilityToggle = props.visibilityToggle,\n      restProps = __rest(props, [\"className\", \"prefixCls\", \"inputPrefixCls\", \"size\", \"visibilityToggle\"]);\n    var inputPrefixCls = getPrefixCls('input', customizeInputPrefixCls);\n    var prefixCls = getPrefixCls('input-password', customizePrefixCls);\n    var suffixIcon = visibilityToggle && getIcon(prefixCls);\n    var inputClassName = classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-\").concat(size), !!size));\n    var omittedProps = _extends(_extends({}, omit(restProps, ['suffix', 'iconRender'])), {\n      type: visible ? 'text' : 'password',\n      className: inputClassName,\n      prefixCls: inputPrefixCls,\n      suffix: suffixIcon\n    });\n    if (size) {\n      omittedProps.size = size;\n    }\n    return /*#__PURE__*/React.createElement(Input, _extends({\n      ref: ref\n    }, omittedProps));\n  };\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, renderPassword);\n});\nPassword.defaultProps = {\n  action: 'click',\n  visibilityToggle: true,\n  iconRender: function iconRender(visible) {\n    return visible ? /*#__PURE__*/React.createElement(EyeOutlined, null) : /*#__PURE__*/React.createElement(EyeInvisibleOutlined, null);\n  }\n};\nPassword.displayName = 'Password';\nexport default Password;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "omit", "EyeOutlined", "EyeInvisibleOutlined", "useState", "Input", "ConfigConsumer", "ActionMap", "click", "hover", "Password", "forwardRef", "props", "ref", "_useState", "_useState2", "visible", "setVisible", "onVisibleChange", "disabled", "getIcon", "prefixCls", "_iconProps", "action", "_props$iconRender", "iconRender", "iconTrigger", "icon", "iconProps", "concat", "onMouseDown", "preventDefault", "onMouseUp", "cloneElement", "isValidElement", "createElement", "renderPassword", "_ref", "getPrefixCls", "className", "customizePrefixCls", "customizeInputPrefixCls", "inputPrefixCls", "size", "visibilityToggle", "restProps", "suffixIcon", "inputClassName", "omittedProps", "type", "suffix", "defaultProps", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/input/Password.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport EyeInvisibleOutlined from \"@ant-design/icons/es/icons/EyeInvisibleOutlined\";\nimport { useState } from 'react';\nimport Input from './Input';\nimport { ConfigConsumer } from '../config-provider';\nvar ActionMap = {\n  click: 'onClick',\n  hover: 'onMouseOver'\n};\nvar Password = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      visible = _useState2[0],\n      setVisible = _useState2[1];\n\n  var onVisibleChange = function onVisibleChange() {\n    var disabled = props.disabled;\n\n    if (disabled) {\n      return;\n    }\n\n    setVisible(!visible);\n  };\n\n  var getIcon = function getIcon(prefixCls) {\n    var _iconProps;\n\n    var action = props.action,\n        _props$iconRender = props.iconRender,\n        iconRender = _props$iconRender === void 0 ? function () {\n      return null;\n    } : _props$iconRender;\n    var iconTrigger = ActionMap[action] || '';\n    var icon = iconRender(visible);\n    var iconProps = (_iconProps = {}, _defineProperty(_iconProps, iconTrigger, onVisibleChange), _defineProperty(_iconProps, \"className\", \"\".concat(prefixCls, \"-icon\")), _defineProperty(_iconProps, \"key\", 'passwordIcon'), _defineProperty(_iconProps, \"onMouseDown\", function onMouseDown(e) {\n      // Prevent focused state lost\n      // https://github.com/ant-design/ant-design/issues/15173\n      e.preventDefault();\n    }), _defineProperty(_iconProps, \"onMouseUp\", function onMouseUp(e) {\n      // Prevent caret position change\n      // https://github.com/ant-design/ant-design/issues/23524\n      e.preventDefault();\n    }), _iconProps);\n    return /*#__PURE__*/React.cloneElement( /*#__PURE__*/React.isValidElement(icon) ? icon : /*#__PURE__*/React.createElement(\"span\", null, icon), iconProps);\n  };\n\n  var renderPassword = function renderPassword(_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n\n    var className = props.className,\n        customizePrefixCls = props.prefixCls,\n        customizeInputPrefixCls = props.inputPrefixCls,\n        size = props.size,\n        visibilityToggle = props.visibilityToggle,\n        restProps = __rest(props, [\"className\", \"prefixCls\", \"inputPrefixCls\", \"size\", \"visibilityToggle\"]);\n\n    var inputPrefixCls = getPrefixCls('input', customizeInputPrefixCls);\n    var prefixCls = getPrefixCls('input-password', customizePrefixCls);\n    var suffixIcon = visibilityToggle && getIcon(prefixCls);\n    var inputClassName = classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-\").concat(size), !!size));\n\n    var omittedProps = _extends(_extends({}, omit(restProps, ['suffix', 'iconRender'])), {\n      type: visible ? 'text' : 'password',\n      className: inputClassName,\n      prefixCls: inputPrefixCls,\n      suffix: suffixIcon\n    });\n\n    if (size) {\n      omittedProps.size = size;\n    }\n\n    return /*#__PURE__*/React.createElement(Input, _extends({\n      ref: ref\n    }, omittedProps));\n  };\n\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, renderPassword);\n});\nPassword.defaultProps = {\n  action: 'click',\n  visibilityToggle: true,\n  iconRender: function iconRender(visible) {\n    return visible ? /*#__PURE__*/React.createElement(EyeOutlined, null) : /*#__PURE__*/React.createElement(EyeInvisibleOutlined, null);\n  }\n};\nPassword.displayName = 'Password';\nexport default Password;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AAErE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,oBAAoB,MAAM,iDAAiD;AAClF,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,IAAIC,SAAS,GAAG;EACdC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,QAAQ,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACjE,IAAIC,SAAS,GAAGV,QAAQ,CAAC,KAAK,CAAC;IAC3BW,UAAU,GAAG/B,cAAc,CAAC8B,SAAS,EAAE,CAAC,CAAC;IACzCE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE9B,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IAE7B,IAAIA,QAAQ,EAAE;MACZ;IACF;IAEAF,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,IAAII,OAAO,GAAG,SAASA,OAAOA,CAACC,SAAS,EAAE;IACxC,IAAIC,UAAU;IAEd,IAAIC,MAAM,GAAGX,KAAK,CAACW,MAAM;MACrBC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU;MACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,YAAY;QAC1D,OAAO,IAAI;MACb,CAAC,GAAGA,iBAAiB;IACrB,IAAIE,WAAW,GAAGnB,SAAS,CAACgB,MAAM,CAAC,IAAI,EAAE;IACzC,IAAII,IAAI,GAAGF,UAAU,CAACT,OAAO,CAAC;IAC9B,IAAIY,SAAS,IAAIN,UAAU,GAAG,CAAC,CAAC,EAAEvC,eAAe,CAACuC,UAAU,EAAEI,WAAW,EAAER,eAAe,CAAC,EAAEnC,eAAe,CAACuC,UAAU,EAAE,WAAW,EAAE,EAAE,CAACO,MAAM,CAACR,SAAS,EAAE,OAAO,CAAC,CAAC,EAAEtC,eAAe,CAACuC,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC,EAAEvC,eAAe,CAACuC,UAAU,EAAE,aAAa,EAAE,SAASQ,WAAWA,CAAC3C,CAAC,EAAE;MAC3R;MACA;MACAA,CAAC,CAAC4C,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,EAAEhD,eAAe,CAACuC,UAAU,EAAE,WAAW,EAAE,SAASU,SAASA,CAAC7C,CAAC,EAAE;MACjE;MACA;MACAA,CAAC,CAAC4C,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,EAAET,UAAU,CAAC;IACf,OAAO,aAAavB,KAAK,CAACkC,YAAY,CAAE,aAAalC,KAAK,CAACmC,cAAc,CAACP,IAAI,CAAC,GAAGA,IAAI,GAAG,aAAa5B,KAAK,CAACoC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAER,IAAI,CAAC,EAAEC,SAAS,CAAC;EAC3J,CAAC;EAED,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;IACjD,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IAEpC,IAAIC,SAAS,GAAG3B,KAAK,CAAC2B,SAAS;MAC3BC,kBAAkB,GAAG5B,KAAK,CAACS,SAAS;MACpCoB,uBAAuB,GAAG7B,KAAK,CAAC8B,cAAc;MAC9CC,IAAI,GAAG/B,KAAK,CAAC+B,IAAI;MACjBC,gBAAgB,GAAGhC,KAAK,CAACgC,gBAAgB;MACzCC,SAAS,GAAG5D,MAAM,CAAC2B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC;IAEvG,IAAI8B,cAAc,GAAGJ,YAAY,CAAC,OAAO,EAAEG,uBAAuB,CAAC;IACnE,IAAIpB,SAAS,GAAGiB,YAAY,CAAC,gBAAgB,EAAEE,kBAAkB,CAAC;IAClE,IAAIM,UAAU,GAAGF,gBAAgB,IAAIxB,OAAO,CAACC,SAAS,CAAC;IACvD,IAAI0B,cAAc,GAAG/C,UAAU,CAACqB,SAAS,EAAEkB,SAAS,EAAExD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC8C,MAAM,CAACR,SAAS,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACc,IAAI,CAAC,EAAE,CAAC,CAACA,IAAI,CAAC,CAAC;IAE1H,IAAIK,YAAY,GAAGlE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmB,IAAI,CAAC4C,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE;MACnFI,IAAI,EAAEjC,OAAO,GAAG,MAAM,GAAG,UAAU;MACnCuB,SAAS,EAAEQ,cAAc;MACzB1B,SAAS,EAAEqB,cAAc;MACzBQ,MAAM,EAAEJ;IACV,CAAC,CAAC;IAEF,IAAIH,IAAI,EAAE;MACRK,YAAY,CAACL,IAAI,GAAGA,IAAI;IAC1B;IAEA,OAAO,aAAa5C,KAAK,CAACoC,aAAa,CAAC9B,KAAK,EAAEvB,QAAQ,CAAC;MACtD+B,GAAG,EAAEA;IACP,CAAC,EAAEmC,YAAY,CAAC,CAAC;EACnB,CAAC;EAED,OAAO,aAAajD,KAAK,CAACoC,aAAa,CAAC7B,cAAc,EAAE,IAAI,EAAE8B,cAAc,CAAC;AAC/E,CAAC,CAAC;AACF1B,QAAQ,CAACyC,YAAY,GAAG;EACtB5B,MAAM,EAAE,OAAO;EACfqB,gBAAgB,EAAE,IAAI;EACtBnB,UAAU,EAAE,SAASA,UAAUA,CAACT,OAAO,EAAE;IACvC,OAAOA,OAAO,GAAG,aAAajB,KAAK,CAACoC,aAAa,CAACjC,WAAW,EAAE,IAAI,CAAC,GAAG,aAAaH,KAAK,CAACoC,aAAa,CAAChC,oBAAoB,EAAE,IAAI,CAAC;EACrI;AACF,CAAC;AACDO,QAAQ,CAAC0C,WAAW,GAAG,UAAU;AACjC,eAAe1C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}