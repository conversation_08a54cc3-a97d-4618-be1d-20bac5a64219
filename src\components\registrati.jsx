import React, { useState, useRef, useEffect } from "react";
import { InputText } from 'primereact/inputtext';
import { <PERSON><PERSON> } from '../components/traduttore/const';
import { APIRequest, SITE_KEY } from '../components/generalizzazioni/apireq';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { Password } from 'primereact/password';
import { Divider } from 'primereact/divider';
import { Checkbox } from 'primereact/checkbox';
import { Form, Field } from 'react-final-form';
import { Dialog } from "primereact/dialog";
import { Captcha } from 'primereact/captcha';
import classNames from 'classnames/bind';
import cookies from 'js-cookie';
import logo from '../img/tm_logo-01.svg';
import '../css/modale.css';
import { login } from "./route";

const Registrati = () => {
    const currentLanguageCode = cookies.get('i18next') || 'it';
    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati
    const [showMessage, setShowMessage] = useState(false);
    const [formData, setFormData] = useState({});
    const toast = useRef(null);
    const [gtoken, setGToken] = useState('');

    useEffect(() => {
        // aggiungo classe loginPage a body per stile login
        document.body.classList.add('loginPage');
        return () => {
            // rimuovo classe loginPage a body se non sono in login
            document.body.classList.remove('loginPage');
        };
    });
    /* Validazione elementi inseriti */
    const validate = (data) => {
        let errors = {};
        if (!data.firstName) {
            errors.firstName = Costanti.NomeObb/* 'Name is required.' */;
        }

        if (!data.lastName) {
            errors.lastName = Costanti.CognObb;
        }

        if (!data.telnum) {
            errors.telnum = Costanti.TelObb;
        }

        if (!data.cellnum) {
            errors.cellnum = Costanti.CelObb;
        }

        if (!data.pIva) {
            errors.pIva = Costanti.pIvaObb;
        }

        if (!data.address) {
            errors.address = Costanti.IndObb;
        }

        if (!data.city) {
            errors.city = Costanti.CityObb;
        }

        if (!data.cap) {
            errors.cap = Costanti.CapObb;
        }

        if (!data.paymentMetod) {
            errors.paymentMetod = Costanti.paymentMetodObb;
        }

        if (!data.email) {
            errors.email = Costanti.userNameObb;
        }
        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(data.email)) {
            errors.email = Costanti.userNameNoVal;
        }
        if (!data.password) {
            errors.password = Costanti.PassObb;
        }
        if (!data.confirmPassword) {
            errors.confirmPassword = Costanti.ConfPassObb;
        }
        else if (data.confirmPassword !== data.password) {
            errors.confirmPassword = Costanti.PassValid;
        }
        return errors;
    };
    const onSubmit = async (data, form) => {
        if(gtoken !== '') {
            setFormData(data);
            /* var nTel = telnum + '/' + cellnum */
            var corpo = {
                registry: {
                    firstName: data.firstName,
                    lastName: data.lastName,
                    email: data.email,
                    telnum: data.telnum,
                    cellnum: data.cellnum,
                    pIva: data.pIva,
                    address: data.address,
                    city: data.city,
                    cap: data.cap,
                    paymentMetod: data.paymentMetod
                },
                user: {
                    username: data.email,
                    password: data.password
                }
            }
            //Chiamata axios per la creazione del registry
            await APIRequest('POST', 'registry/anonimous/', corpo)
                .then(res => {
                    console.log(res.data);
                    toast.current.show({ severity: 'success', summary: 'Ottimo', detail: "L'anagrafica è stata inserita con successo", life: 3000 });
                    form.restart();
                    setTimeout(() => {
                        window.location.pathname = '/login'
                    }, 3000)
                }).catch((e) => {
                    console.error('❌ Errore registrazione anagrafica:', e);

                    // Gestione specifica degli errori
                    const errorStatus = e.response?.status;
                    const errorData = e.response?.data;
                    const errorMessage = e.response?.data?.message || e.message;

                    let userMessage = '';
                    let summary = 'Errore';

                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||
                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {
                        // Violazione unique constraint (P.IVA duplicata)
                        summary = '⚠️ Partita IVA già presente';
                        userMessage = `La Partita IVA "${corpo.pIva}" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;
                    } else if (errorStatus === 400) {
                        // Errori di validazione
                        summary = '📝 Dati non validi';
                        if (errorMessage.toLowerCase().includes('email')) {
                            userMessage = 'L\'indirizzo email inserito non è valido. Verificare il formato.';
                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {
                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';
                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('cellulare')) {
                            userMessage = 'Il numero di telefono inserito non è valido. Verificare il formato.';
                        } else {
                            userMessage = `Dati inseriti non validi: ${errorMessage}`;
                        }
                    } else if (errorStatus === 422) {
                        // Errori di business logic
                        summary = '⚠️ Dati non accettabili';
                        userMessage = errorMessage || 'I dati inseriti non rispettano le regole di business.';
                    } else if (errorStatus === 500) {
                        // Errore server
                        summary = '🔧 Errore del server';
                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto.';
                    } else if (errorStatus === 503) {
                        // Servizio non disponibile
                        summary = '⏱️ Servizio temporaneamente non disponibile';
                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';
                    } else if (!errorStatus) {
                        // Errore di rete
                        summary = '🌐 Errore di connessione';
                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
                    } else {
                        // Altri errori
                        summary = '❌ Errore imprevisto';
                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
                    }

                    toast.current.show({
                        severity: 'error',
                        summary: summary,
                        detail: userMessage,
                        life: 6000
                    });

                    // Log dettagliato per debugging
                    console.error('Dettagli errore registrazione:', {
                        status: errorStatus,
                        data: errorData,
                        message: errorMessage,
                        fullError: e
                    });
                })
        }else {
            toast.current.show({severity: 'error', summary: 'Siamo spiacenti', detail: "La validazione recaptcha è necessaria", life: 3000})
        }
        
    };
    const showResponse = (e) => {
        console.log(e);
        setGToken(e.response)
        toast.current.show({ severity: 'success', summary: 'Verifica effettuata', detail: 'Grazie, la verifica è stata effettuata con successo.' });
    }

    const isFormFieldValid = (meta) => !!(meta.touched && meta.error);
    const getFormErrorMessage = (meta) => {
        return isFormFieldValid(meta) && <small className="p-error">{meta.error}</small>;
    };
    const dialogFooter = <div className="p-d-flex p-jc-center"><Button label="OK" className="p-button-text" autoFocus onClick={() => setShowMessage(false)} /></div>;
    const passwordHeader = <h6>{Costanti.SelectPass}</h6>;
    const passwordFooter = (
        <React.Fragment>
            <Divider />
            <p className="p-mt-2">{Costanti.PassDevCont}</p>
            <ul className="p-pl-2 p-ml-2 p-mt-0" style={{ lineHeight: '1.5' }}>
                <li>{Costanti.Minuscola}</li>
                <li>{Costanti.Maiuscola}</li>
                <li>{Costanti.AlmUnNum}</li>
                <li>{Costanti.Alm8Car}</li>
            </ul>
        </React.Fragment>
    );
    return (
        <div className="App registerPage">
            <Toast ref={toast} />
            {/* <div className="redirectToWinet">
                {/* Bottone di annullamento operazione con funzione go back *//*}
                <a className="d-flex align-items-center" href="/login"><ion-icon name="arrow-back-circle" />{Costanti.GoBack}</a>
            </div> */}
            <div className="container">
                <Dialog visible={showMessage} onHide={() => setShowMessage(false)} position="top" footer={dialogFooter} showHeader={false} breakpoints={{ '960px': '80vw' }} style={{ width: '30vw' }}>
                    <div className="p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3">
                        <i className="pi pi-check-circle" style={{ fontSize: '5rem', color: 'var(--green-500)' }}></i>
                        <h5>{Costanti.RegSucc}</h5>
                        <p style={{ lineHeight: 1.5, textIndent: '1rem' }}>
                            <br /> {Costanti.GiaReg}<br /> {Costanti.ConEmail}: <b>{formData.email}</b> .
                        </p>
                    </div>
                </Dialog>
                <Form onSubmit={onSubmit} initialValues={{ firstName: '', lastName: '', telnum: '', cellnum: '', pIva: '', address: '', city: '', cap: '', paymentMetod: '', email: '', password: '', confirmPassword: '' }} validate={validate} render={({ handleSubmit }) => (
                    <form onSubmit={handleSubmit} className="p-fluid w-100 form-login registration row">
                        <div id="fontLogo" className="logo text-center my-3 d-flex justify-content-center w-100"><img src={logo} onError={(e) => e.target.src = logo} alt="Logo" width="200" />{/* <span>central</span> unit <span className="payoff text-center">food & beverage • e-procurement system</span> */}</div>
                        <div className="col-12">
                            <hr />
                        </div>
                        <div className="d-flex justify-content-center w-100 mt-2">
                            <h3>Informazioni anagrafiche</h3>
                        </div>
                        <Field name="firstName" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label">
                                    <InputText id="firstName" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="firstName" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Nome}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="lastName" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label">
                                    <InputText id="lastName" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="lastName" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Cognome}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="telnum" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-phone" />
                                    <InputText type="tel" id="telnum" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="telnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Tel}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="cellnum" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-mobile" />
                                    <InputText type="tel" id="cellnum" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="cellnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Cell}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="pIva" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-credit-card" />
                                    <InputText id="pIva" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="pIva" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.pIva}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="address" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-directions" />
                                    <InputText id="address" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="address" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Indirizzo}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="city" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-4">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-map-marker" />
                                    <InputText id="city" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="city" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Città}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="cap" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-2">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-compass" />
                                    <InputText id="cap" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="cap" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.CodPost}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="paymentMetod" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-money-bill" />
                                    <InputText id="paymentMetod" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="paymentMetod" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Pagamento}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <div className="d-flex justify-content-center w-100 mt-3">
                            <h3>Informazioni account</h3>
                        </div>
                        <Field name="email" render={({ input, meta }) => (
                            <div className="p-field col-12">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-envelope" />
                                    <InputText id="email" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="email" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Email}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="password" render={({ input, meta }) => (
                            <div className="p-field col-12">
                                <span className="p-float-label">
                                    <Password id="password" {...input} toggleMask className={classNames({ 'p-invalid': isFormFieldValid(meta) })} header={passwordHeader} footer={passwordFooter} />
                                    <label htmlFor="password" className={classNames({ 'p-error': isFormFieldValid(meta) })}>Password*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="confirmPassword" render={({ input, meta }) => (
                            <div className="p-field col-12">
                                <span className="p-float-label">
                                    <Password id="confirmPassword" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} feedback={false} />
                                    <label htmlFor="confirmPassword" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Conferma} password*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <div className="col-12">
                            <hr />
                        </div>
                        <Field name="privacyCheck" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 d-flex">
                                <div className="privacyCheck text-left">
                                    <Checkbox className="mr-3" inputId="cb1" value="Informativa Privacy" onChange="" checked></Checkbox>
                                    <label htmlFor="cb1" className="p-checkbox-label mb-0"><small>Acconsento al trattamento dei miei dati personali, dichiaro espressamente di essere maggiorenne e di aver acquisito <a href="/#" alt="Privacy Policy" target="_parent">l'informativa di TMSELEZIONI</a>. <i>(Obbligatorio)</i></small></label>
                                </div>
                            </div>
                        )} />
                        <Field name="googleReCaptcha" render={({ input, meta }) => (
                            <div className="p-field col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <span className="p-float-label d-flex justify-content-center">
                                    <Captcha siteKey={SITE_KEY} language={currentLanguageCode} onResponse={(e) => showResponse(e)} />
                                </span>
                            </div>
                        )} />
                        <div className="col-12">
                            <hr />
                        </div>
                        <div className="w-100">
                                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}
                                <Button type="submit" className="btn btn-lg btn-primary buttonreg px-5 mx-auto w-auto d-block" >{Costanti.registrati}</Button>
                        </div>
                        <div className="col-12">
                            <hr />
                        </div>
                        <div className="col-md-12">
                            Hai già un account? <a href={login} alt="Accedi">Accedi</a>
                        </div>
                    </form>
                )} />
            </div>
        </div>
    )
}

export default Registrati;