{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\icone fisse\\\\goToTopOfPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const GoToTheTop = () => {\n  _s();\n  const [showButton, setShowButton] = useState(false);\n\n  // Uso useEffect per controllare quando lo scroll raggiunge il valore sufficiente per visualizzare il bottone\n  useEffect(() => {\n    const scroller = document.querySelector(\"#root\");\n\n    // Controllo se l'elemento esiste (importante per i test)\n    if (!scroller) {\n      return;\n    }\n    const handleScroll = () => {\n      if (scroller.scrollTop > 50) {\n        setShowButton(true);\n      } else {\n        setShowButton(false);\n      }\n    };\n    scroller.addEventListener(\"scroll\", handleScroll);\n\n    // Cleanup function per rimuovere l'event listener\n    return () => {\n      scroller.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  // Al click scorro verso l'alto\n  const scrollToTop = () => {\n    const scroller = document.querySelector(\"#root\");\n    if (scroller) {\n      scroller.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      }); // for smoothly scrolling\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: showButton && /*#__PURE__*/_jsxDEV(Button, {\n      className: \"buttonGoToTheTop p-button-rounded\",\n      onClick: () => scrollToTop(),\n      icon: \"pi pi-arrow-up\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n\n/* export const IconAssistance = () => {\n\n    const contactMe = () => {\n        window.location.href = \"https://wa.me/2348100000000\"\n    }\n\n    return (\n        <>\n        <Button className=\"buttonIconAssistance p-button-rounded\" onClick={() => contactMe()} icon=\"pi pi-whatsapp\" />\n    </>\n    )\n} */\n_s(GoToTheTop, \"rGOrX3CCwlIbUUAZRpGZUtVAB5g=\");\n_c = GoToTheTop;\nvar _c;\n$RefreshReg$(_c, \"GoToTheTop\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GoToTheTop", "_s", "showButton", "setShowButton", "scroller", "document", "querySelector", "handleScroll", "scrollTop", "addEventListener", "removeEventListener", "scrollToTop", "scrollTo", "top", "behavior", "children", "className", "onClick", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/icone fisse/goToTopOfPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { Button } from \"primereact/button\";\n\nexport const GoToTheTop = () => {\n    const [showButton, setShowButton] = useState(false);\n\n    // Uso useEffect per controllare quando lo scroll raggiunge il valore sufficiente per visualizzare il bottone\n    useEffect(() => {\n        const scroller = document.querySelector(\"#root\");\n\n        // Controllo se l'elemento esiste (importante per i test)\n        if (!scroller) {\n            return;\n        }\n\n        const handleScroll = () => {\n            if (scroller.scrollTop > 50) {\n                setShowButton(true);\n            } else {\n                setShowButton(false);\n            }\n        };\n\n        scroller.addEventListener(\"scroll\", handleScroll);\n\n        // Cleanup function per rimuovere l'event listener\n        return () => {\n            scroller.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n\n    // Al click scorro verso l'alto\n    const scrollToTop = () => {\n        const scroller = document.querySelector(\"#root\");\n        if (scroller) {\n            scroller.scrollTo({ top: 0, behavior: 'smooth' }); // for smoothly scrolling\n        }\n    };\n    return (\n        <>\n            {showButton && (\n                <Button className=\"buttonGoToTheTop p-button-rounded\" onClick={() => scrollToTop()} icon=\"pi pi-arrow-up\" />\n            )}\n        </>\n    )\n}\n\n/* export const IconAssistance = () => {\n\n    const contactMe = () => {\n        window.location.href = \"https://wa.me/2348100000000\"\n    }\n\n    return (\n        <>\n        <Button className=\"buttonIconAssistance p-button-rounded\" onClick={() => contactMe()} icon=\"pi pi-whatsapp\" />\n    </>\n    )\n} */"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IACZ,MAAMW,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;;IAEhD;IACA,IAAI,CAACF,QAAQ,EAAE;MACX;IACJ;IAEA,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACvB,IAAIH,QAAQ,CAACI,SAAS,GAAG,EAAE,EAAE;QACzBL,aAAa,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACHA,aAAa,CAAC,KAAK,CAAC;MACxB;IACJ,CAAC;IAEDC,QAAQ,CAACK,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAEjD;IACA,OAAO,MAAM;MACTH,QAAQ,CAACM,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACxD,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMP,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAChD,IAAIF,QAAQ,EAAE;MACVA,QAAQ,CAACQ,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC,CAAC,CAAC;IACvD;EACJ,CAAC;EACD,oBACIjB,OAAA,CAAAE,SAAA;IAAAgB,QAAA,EACKb,UAAU,iBACPL,OAAA,CAACF,MAAM;MAACqB,SAAS,EAAC,mCAAmC;MAACC,OAAO,EAAEA,CAAA,KAAMN,WAAW,CAAC,CAAE;MAACO,IAAI,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9G,gBACH,CAAC;AAEX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXArB,EAAA,CA5CaD,UAAU;AAAAuB,EAAA,GAAVvB,UAAU;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}