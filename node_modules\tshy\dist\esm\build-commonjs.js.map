{"version": 3, "file": "build-commonjs.js", "sourceRoot": "", "sources": ["../../src/build-commonjs.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAA;AAC9C,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAA;AACnD,OAAO,SAAS,MAAM,iBAAiB,CAAA;AACvC,OAAO,MAAM,MAAM,aAAa,CAAA;AAChC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,OAAO,MAAM,eAAe,CAAA;AACnC,OAAO,SAAS,MAAM,gBAAgB,CAAA;AACtC,OAAO,gBAAgB,MAAM,yBAAyB,CAAA;AACtD,OAAO,eAAe,CAAA;AACtB,OAAO,GAAG,MAAM,gBAAgB,CAAA;AAEhC,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAA;AAC7B,MAAM,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,MAAM,CAAA;AAExC,MAAM,CAAC,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IACnC,KAAK,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC;QAClD,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACtD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAA;QAC9C,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE;YAC1D,KAAK,EAAE,SAAS;SACjB,CAAC,CAAA;QACF,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAC7B,gBAAgB,CAAC,KAAK,CAAC,CAAA;YACvB,OAAO,SAAS,CAAC,GAAG,CAAC,CAAA;QACvB,CAAC;QACD,gBAAgB,CAAC,cAAc,GAAG,CAAC,EAAE,UAAU,CAAC,CAAA;QAChD,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;YACvD,MAAM,QAAQ,GAAG,OAAO,CACtB,eAAe,CAAC,EAAE,EAClB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAC5C,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;YACvB,MAAM,MAAM,GAAG,OAAO,CACpB,eAAe,CAAC,EAAE,EAClB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CACxC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;YACxB,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,CAAA;YACrC,MAAM,aAAa,GAAG,GAAG,MAAM,WAAW,CAAA;YAC1C,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;YAC1B,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;YAC7B,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,CAAA;YACjD,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,CAAA;QACvD,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA;IAClD,CAAC;IACD,gBAAgB,CAAC,KAAK,CAAC,CAAA;AACzB,CAAC,CAAA", "sourcesContent": ["import chalk from 'chalk'\nimport { spawnSync } from 'node:child_process'\nimport { relative, resolve } from 'node:path/posix'\nimport buildFail from './build-fail.js'\nimport config from './config.js'\nimport * as console from './console.js'\nimport ifExist from './if-exist.js'\nimport polyfills from './polyfills.js'\nimport setFolderDialect from './set-folder-dialect.js'\nimport './tsconfig.js'\nimport tsc from './which-tsc.js'\n\nconst node = process.execPath\nconst { commonjsDialects = [] } = config\n\nexport const buildCommonJS = () => {\n  setFolderDialect('src', 'commonjs')\n  for (const d of ['commonjs', ...commonjsDialects]) {\n    const pf = polyfills.get(d === 'commonjs' ? 'cjs' : d)\n    console.debug(chalk.cyan.dim('building ' + d))\n    const res = spawnSync(node, [tsc, '-p', `.tshy/${d}.json`], {\n      stdio: 'inherit',\n    })\n    if (res.status || res.signal) {\n      setFolderDialect('src')\n      return buildFail(res)\n    }\n    setFolderDialect('.tshy-build/' + d, 'commonjs')\n    for (const [override, orig] of pf?.map.entries() ?? []) {\n      const stemFrom = resolve(\n        `.tshy-build/${d}`,\n        relative(resolve('src'), resolve(override)),\n      ).replace(/\\.cts$/, '')\n      const stemTo = resolve(\n        `.tshy-build/${d}`,\n        relative(resolve('src'), resolve(orig)),\n      ).replace(/\\.tsx?$/, '')\n      const stemToPath = `${stemTo}.js.map`\n      const stemToDtsPath = `${stemTo}.d.ts.map`\n      ifExist.unlink(stemToPath)\n      ifExist.unlink(stemToDtsPath)\n      ifExist.rename(`${stemFrom}.cjs`, `${stemTo}.js`)\n      ifExist.rename(`${stemFrom}.d.cts`, `${stemTo}.d.ts`)\n    }\n    console.error(chalk.cyan.bold('built commonjs'))\n  }\n  setFolderDialect('src')\n}\n"]}