{"ast": null, "code": "import { useRef, useEffect } from 'react';\nexport var defaultProps = {\n  className: '',\n  percent: 0,\n  prefixCls: 'rc-progress',\n  strokeColor: '#2db7f5',\n  strokeLinecap: 'round',\n  strokeWidth: 1,\n  style: {},\n  trailColor: '#D9D9D9',\n  trailWidth: 1\n};\nexport var useTransitionDuration = function useTransitionDuration(percentList) {\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  var paths = percentList.map(function () {\n    return useRef();\n  });\n  var prevTimeStamp = useRef(null);\n  useEffect(function () {\n    var now = Date.now();\n    var updated = false;\n    Object.keys(paths).forEach(function (key) {\n      var path = paths[key].current;\n      if (!path) {\n        return;\n      }\n      updated = true;\n      var pathStyle = path.style;\n      pathStyle.transitionDuration = '.3s, .3s, .3s, .06s';\n      if (prevTimeStamp.current && now - prevTimeStamp.current < 100) {\n        pathStyle.transitionDuration = '0s, 0s';\n      }\n    });\n    if (updated) {\n      prevTimeStamp.current = Date.now();\n    }\n  });\n  return [paths];\n};", "map": {"version": 3, "names": ["useRef", "useEffect", "defaultProps", "className", "percent", "prefixCls", "strokeColor", "strokeLinecap", "strokeWidth", "style", "trailColor", "trailWidth", "useTransitionDuration", "percentList", "paths", "map", "prevTimeStamp", "now", "Date", "updated", "Object", "keys", "for<PERSON>ach", "key", "path", "current", "pathStyle", "transitionDuration"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-progress/es/common.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\nexport var defaultProps = {\n  className: '',\n  percent: 0,\n  prefixCls: 'rc-progress',\n  strokeColor: '#2db7f5',\n  strokeLinecap: 'round',\n  strokeWidth: 1,\n  style: {},\n  trailColor: '#D9D9D9',\n  trailWidth: 1\n};\nexport var useTransitionDuration = function useTransitionDuration(percentList) {\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  var paths = percentList.map(function () {\n    return useRef();\n  });\n  var prevTimeStamp = useRef(null);\n  useEffect(function () {\n    var now = Date.now();\n    var updated = false;\n    Object.keys(paths).forEach(function (key) {\n      var path = paths[key].current;\n\n      if (!path) {\n        return;\n      }\n\n      updated = true;\n      var pathStyle = path.style;\n      pathStyle.transitionDuration = '.3s, .3s, .3s, .06s';\n\n      if (prevTimeStamp.current && now - prevTimeStamp.current < 100) {\n        pathStyle.transitionDuration = '0s, 0s';\n      }\n    });\n\n    if (updated) {\n      prevTimeStamp.current = Date.now();\n    }\n  });\n  return [paths];\n};"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAO,IAAIC,YAAY,GAAG;EACxBC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,aAAa;EACxBC,WAAW,EAAE,SAAS;EACtBC,aAAa,EAAE,OAAO;EACtBC,WAAW,EAAE,CAAC;EACdC,KAAK,EAAE,CAAC,CAAC;EACTC,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE;AACd,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,WAAW,EAAE;EAC7E;EACA,IAAIC,KAAK,GAAGD,WAAW,CAACE,GAAG,CAAC,YAAY;IACtC,OAAOf,MAAM,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,IAAIgB,aAAa,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAChCC,SAAS,CAAC,YAAY;IACpB,IAAIgB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACpB,IAAIE,OAAO,GAAG,KAAK;IACnBC,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC,CAACQ,OAAO,CAAC,UAAUC,GAAG,EAAE;MACxC,IAAIC,IAAI,GAAGV,KAAK,CAACS,GAAG,CAAC,CAACE,OAAO;MAE7B,IAAI,CAACD,IAAI,EAAE;QACT;MACF;MAEAL,OAAO,GAAG,IAAI;MACd,IAAIO,SAAS,GAAGF,IAAI,CAACf,KAAK;MAC1BiB,SAAS,CAACC,kBAAkB,GAAG,qBAAqB;MAEpD,IAAIX,aAAa,CAACS,OAAO,IAAIR,GAAG,GAAGD,aAAa,CAACS,OAAO,GAAG,GAAG,EAAE;QAC9DC,SAAS,CAACC,kBAAkB,GAAG,QAAQ;MACzC;IACF,CAAC,CAAC;IAEF,IAAIR,OAAO,EAAE;MACXH,aAAa,CAACS,OAAO,GAAGP,IAAI,CAACD,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,CAAC;EACF,OAAO,CAACH,KAAK,CAAC;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}