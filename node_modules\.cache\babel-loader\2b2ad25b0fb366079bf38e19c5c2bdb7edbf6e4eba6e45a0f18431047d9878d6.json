{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\legendaStatiTask.jsx\";\nimport React from \"react\";\nimport { <PERSON>nti } from \"../traduttore/const\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport class LegendaStatiTask extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"p-2 mb-0\",\n          children: Costanti.LegendaTipoDocForOrder\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table table-sm table-bordered table-hover table-striped\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    width: '15%'\n                  },\n                  scope: \"col\",\n                  children: Costanti.Stato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 18,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    width: '85%'\n                  },\n                  scope: \"col\",\n                  children: Costanti.Descrizione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 19,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  scope: \"row\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border border-primary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-primary\",\n                    children: \"create\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 24,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-1\",\n                  children: Costanti.StatoCreateDescr\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 25,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  scope: \"row\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border border-info rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-info\",\n                    children: \"counted\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 28,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-1\",\n                  children: Costanti.StatoCountedDescr\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  scope: \"row\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border border-success rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-success\",\n                    children: \"approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-1\",\n                  children: Costanti.StatoApprovedDescr\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  scope: \"row\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border border-secondary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-secondary\",\n                    children: \"positioned\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-1\",\n                  children: Costanti.StatoPositionedDescr\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  scope: \"row\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border border-danger rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-danger\",\n                    children: \"canceled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-1\",\n                  children: Costanti.StatoCanceledDescr\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"mt-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"p-2 mb-0\",\n              children: Costanti.LegendaTipoDocCliOrder\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-sm table-bordered table-hover table-striped\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '15%'\n                      },\n                      scope: \"col\",\n                      children: Costanti.Stato\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 54,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '85%'\n                      },\n                      scope: \"col\",\n                      children: Costanti.Descrizione\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 55,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border border-primary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-primary\",\n                        children: \"create\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 60,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 60,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-1\",\n                      children: Costanti.StatoCreateDescr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 61,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border border-warning rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-warning\",\n                        children: \"prepared\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 64,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 64,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-1\",\n                      children: Costanti.StatoPreparedDescr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 65,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border border-danger rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-danger\",\n                        children: \"canceled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 68,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 68,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-1\",\n                      children: Costanti.StatoCanceledDescr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 69,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"mt-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"p-2 mb-0\",\n              children: \"Tipo di documento: CLI-DDT (Documento di trasporto)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-sm table-bordered table-hover table-striped\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '15%'\n                      },\n                      scope: \"col\",\n                      children: Costanti.Stato\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 84,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '85%'\n                      },\n                      scope: \"col\",\n                      children: Costanti.Descrizione\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 85,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border border-primary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-primary\",\n                        children: \"assigned\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 90,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-1\",\n                      children: Costanti.StatoAssignedDescr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 91,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border border-info rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-info\",\n                        children: \"delivered\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 94,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 94,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-1\",\n                      children: Costanti.StatoDeliveredDescr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 95,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border border-danger rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-danger\",\n                        children: \"not delivered\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 98,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 98,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-1\",\n                      children: Costanti.StatoNotDeliveredDescr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 99,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LegendaStatiTask", "Component", "constructor", "props", "state", "render", "className", "children", "LegendaTipoDocForOrder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "scope", "Stato", "Descrizione", "StatoCreateDescr", "StatoCountedDescr", "StatoApprovedDescr", "StatoPositionedDescr", "StatoCanceledDescr", "LegendaTipoDocCliOrder", "StatoPreparedDescr", "StatoAssignedDescr", "StatoDeliveredDescr", "StatoNotDeliveredDescr"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/legendaStatiTask.jsx"], "sourcesContent": ["import React from \"react\";\nimport { <PERSON><PERSON> } from \"../traduttore/const\";\n\nexport class LegendaStatiTask extends React.Component {\n    constructor(props) {\n        super(props);\n        this.state = {}\n    }\n    render() {\n        return (\n            <div className=\"row\">\n                <div className=\"col-12\">\n                    <h6 className=\"p-2 mb-0\">{Costanti.LegendaTipoDocForOrder}</h6>\n                    <div className=\"table-responsive-sm\">\n                        <table className=\"table table-sm table-bordered table-hover table-striped\">\n                            <thead>\n                                <tr>\n                                    <th style={{ width: '15%' }} scope=\"col\">{Costanti.Stato}</th>\n                                    <th style={{ width: '85%' }} scope=\"col\">{Costanti.Descrizione}</th>\n                                </tr>\n                            </thead>\n                            <tbody>\n                                <tr>\n                                    <th scope=\"row\"><div className=\"border border-primary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-primary\">create</div></th>\n                                    <td className=\"px-1\">{Costanti.StatoCreateDescr}</td>\n                                </tr>\n                                <tr>\n                                    <th scope=\"row\"><div className=\"border border-info rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-info\">counted</div></th>\n                                    <td className=\"px-1\">{Costanti.StatoCountedDescr}</td>\n                                </tr>\n                                <tr>\n                                    <th scope=\"row\"><div className=\"border border-success rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-success\">approved</div></th>\n                                    <td className=\"px-1\">{Costanti.StatoApprovedDescr}</td>\n                                </tr>\n                                <tr>\n                                    <th scope=\"row\"><div className=\"border border-secondary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-secondary\">positioned</div></th>\n                                    <td className=\"px-1\">{Costanti.StatoPositionedDescr}</td>\n                                </tr>\n                                <tr>\n                                    <th scope=\"row\"><div className=\"border border-danger rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-danger\">canceled</div></th>\n                                    <td className=\"px-1\">{Costanti.StatoCanceledDescr}</td>\n                                </tr>\n                            </tbody>\n                        </table>\n                    </div>\n                    <hr className=\"mt-0\" />\n                    <div className=\"row\">\n                        <div className=\"col-12\">\n                            <h6 className=\"p-2 mb-0\">{Costanti.LegendaTipoDocCliOrder}</h6>\n                            <div className=\"table-responsive-sm\">\n                                <table className=\"table table-sm table-bordered table-hover table-striped\">\n                                    <thead>\n                                        <tr>\n                                            <th style={{ width: '15%' }} scope=\"col\">{Costanti.Stato}</th>\n                                            <th style={{ width: '85%' }} scope=\"col\">{Costanti.Descrizione}</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        <tr>\n                                            <th scope=\"row\"><div className=\"border border-primary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-primary\">create</div></th>\n                                            <td className=\"px-1\">{Costanti.StatoCreateDescr}</td>\n                                        </tr>\n                                        <tr>\n                                            <th scope=\"row\"><div className=\"border border-warning rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-warning\">prepared</div></th>\n                                            <td className=\"px-1\">{Costanti.StatoPreparedDescr}</td>\n                                        </tr>\n                                        <tr>\n                                            <th scope=\"row\"><div className=\"border border-danger rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-danger\">canceled</div></th>\n                                            <td className=\"px-1\">{Costanti.StatoCanceledDescr}</td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </div>\n                        </div>\n                    </div>\n                    <hr className=\"mt-0\" />\n                    <div className=\"row\">\n                        <div className=\"col-12\">\n                            <h6 className=\"p-2 mb-0\">Tipo di documento: CLI-DDT (Documento di trasporto)</h6>\n                            <div className=\"table-responsive-sm\">\n                                <table className=\"table table-sm table-bordered table-hover table-striped\">\n                                    <thead>\n                                        <tr>\n                                            <th style={{ width: '15%' }} scope=\"col\">{Costanti.Stato}</th>\n                                            <th style={{ width: '85%' }} scope=\"col\">{Costanti.Descrizione}</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        <tr>\n                                            <th scope=\"row\"><div className=\"border border-primary rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-primary\">assigned</div></th>\n                                            <td className=\"px-1\">{Costanti.StatoAssignedDescr}</td>\n                                        </tr>\n                                        <tr>\n                                            <th scope=\"row\"><div className=\"border border-info rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-info\">delivered</div></th>\n                                            <td className=\"px-1\">{Costanti.StatoDeliveredDescr}</td>\n                                        </tr>\n                                        <tr>\n                                            <th scope=\"row\"><div className=\"border border-danger rounded w-100 px-2 min-w-50 d-flex justify-content-center text-white bg-danger\">not delivered</div></th>\n                                            <td className=\"px-1\">{Costanti.StatoNotDeliveredDescr}</td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        )\n    }\n\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,OAAO,MAAMC,gBAAgB,SAASJ,KAAK,CAACK,SAAS,CAAC;EAClDC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,MAAMA,CAAA,EAAG;IACL,oBACIN,OAAA;MAAKO,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChBR,OAAA;QAAKO,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACnBR,OAAA;UAAIO,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAEV,QAAQ,CAACW;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/Db,OAAA;UAAKO,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAChCR,OAAA;YAAOO,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtER,OAAA;cAAAQ,QAAA,eACIR,OAAA;gBAAAQ,QAAA,gBACIR,OAAA;kBAAIc,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM,CAAE;kBAACC,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAEV,QAAQ,CAACmB;gBAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9Db,OAAA;kBAAIc,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM,CAAE;kBAACC,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAEV,QAAQ,CAACoB;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACRb,OAAA;cAAAQ,QAAA,gBACIR,OAAA;gBAAAQ,QAAA,gBACIR,OAAA;kBAAIgB,KAAK,EAAC,KAAK;kBAAAR,QAAA,eAACR,OAAA;oBAAKO,SAAS,EAAC,uGAAuG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxJb,OAAA;kBAAIO,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEV,QAAQ,CAACqB;gBAAgB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACLb,OAAA;gBAAAQ,QAAA,gBACIR,OAAA;kBAAIgB,KAAK,EAAC,KAAK;kBAAAR,QAAA,eAACR,OAAA;oBAAKO,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnJb,OAAA;kBAAIO,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEV,QAAQ,CAACsB;gBAAiB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACLb,OAAA;gBAAAQ,QAAA,gBACIR,OAAA;kBAAIgB,KAAK,EAAC,KAAK;kBAAAR,QAAA,eAACR,OAAA;oBAAKO,SAAS,EAAC,uGAAuG;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1Jb,OAAA;kBAAIO,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEV,QAAQ,CAACuB;gBAAkB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACLb,OAAA;gBAAAQ,QAAA,gBACIR,OAAA;kBAAIgB,KAAK,EAAC,KAAK;kBAAAR,QAAA,eAACR,OAAA;oBAAKO,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChKb,OAAA;kBAAIO,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEV,QAAQ,CAACwB;gBAAoB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACLb,OAAA;gBAAAQ,QAAA,gBACIR,OAAA;kBAAIgB,KAAK,EAAC,KAAK;kBAAAR,QAAA,eAACR,OAAA;oBAAKO,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxJb,OAAA;kBAAIO,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEV,QAAQ,CAACyB;gBAAkB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNb,OAAA;UAAIO,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvBb,OAAA;UAAKO,SAAS,EAAC,KAAK;UAAAC,QAAA,eAChBR,OAAA;YAAKO,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACnBR,OAAA;cAAIO,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEV,QAAQ,CAAC0B;YAAsB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/Db,OAAA;cAAKO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAChCR,OAAA;gBAAOO,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBACtER,OAAA;kBAAAQ,QAAA,eACIR,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIc,KAAK,EAAE;wBAAEC,KAAK,EAAE;sBAAM,CAAE;sBAACC,KAAK,EAAC,KAAK;sBAAAR,QAAA,EAAEV,QAAQ,CAACmB;oBAAK;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9Db,OAAA;sBAAIc,KAAK,EAAE;wBAAEC,KAAK,EAAE;sBAAM,CAAE;sBAACC,KAAK,EAAC,KAAK;sBAAAR,QAAA,EAAEV,QAAQ,CAACoB;oBAAW;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACRb,OAAA;kBAAAQ,QAAA,gBACIR,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIgB,KAAK,EAAC,KAAK;sBAAAR,QAAA,eAACR,OAAA;wBAAKO,SAAS,EAAC,uGAAuG;wBAAAC,QAAA,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxJb,OAAA;sBAAIO,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEV,QAAQ,CAACqB;oBAAgB;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eACLb,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIgB,KAAK,EAAC,KAAK;sBAAAR,QAAA,eAACR,OAAA;wBAAKO,SAAS,EAAC,uGAAuG;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1Jb,OAAA;sBAAIO,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEV,QAAQ,CAAC2B;oBAAkB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACLb,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIgB,KAAK,EAAC,KAAK;sBAAAR,QAAA,eAACR,OAAA;wBAAKO,SAAS,EAAC,qGAAqG;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxJb,OAAA;sBAAIO,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEV,QAAQ,CAACyB;oBAAkB;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNb,OAAA;UAAIO,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvBb,OAAA;UAAKO,SAAS,EAAC,KAAK;UAAAC,QAAA,eAChBR,OAAA;YAAKO,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACnBR,OAAA;cAAIO,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAmD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFb,OAAA;cAAKO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAChCR,OAAA;gBAAOO,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBACtER,OAAA;kBAAAQ,QAAA,eACIR,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIc,KAAK,EAAE;wBAAEC,KAAK,EAAE;sBAAM,CAAE;sBAACC,KAAK,EAAC,KAAK;sBAAAR,QAAA,EAAEV,QAAQ,CAACmB;oBAAK;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9Db,OAAA;sBAAIc,KAAK,EAAE;wBAAEC,KAAK,EAAE;sBAAM,CAAE;sBAACC,KAAK,EAAC,KAAK;sBAAAR,QAAA,EAAEV,QAAQ,CAACoB;oBAAW;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACRb,OAAA;kBAAAQ,QAAA,gBACIR,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIgB,KAAK,EAAC,KAAK;sBAAAR,QAAA,eAACR,OAAA;wBAAKO,SAAS,EAAC,uGAAuG;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1Jb,OAAA;sBAAIO,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEV,QAAQ,CAAC4B;oBAAkB;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACLb,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIgB,KAAK,EAAC,KAAK;sBAAAR,QAAA,eAACR,OAAA;wBAAKO,SAAS,EAAC,iGAAiG;wBAAAC,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrJb,OAAA;sBAAIO,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEV,QAAQ,CAAC6B;oBAAmB;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLb,OAAA;oBAAAQ,QAAA,gBACIR,OAAA;sBAAIgB,KAAK,EAAC,KAAK;sBAAAR,QAAA,eAACR,OAAA;wBAAKO,SAAS,EAAC,qGAAqG;wBAAAC,QAAA,EAAC;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7Jb,OAAA;sBAAIO,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEV,QAAQ,CAAC8B;oBAAsB;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}