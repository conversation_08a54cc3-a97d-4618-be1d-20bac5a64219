{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigConsumer } from '../config-provider';\nimport AnchorContext from './context';\nvar AnchorLink = /*#__PURE__*/function (_React$Component) {\n  _inherits(AnchorLink, _React$Component);\n  var _super = _createSuper(AnchorLink);\n  function AnchorLink() {\n    var _this;\n    _classCallCheck(this, AnchorLink);\n    _this = _super.apply(this, arguments);\n    _this.handleClick = function (e) {\n      var _this$context = _this.context,\n        scrollTo = _this$context.scrollTo,\n        onClick = _this$context.onClick;\n      var _this$props = _this.props,\n        href = _this$props.href,\n        title = _this$props.title;\n      onClick === null || onClick === void 0 ? void 0 : onClick(e, {\n        title: title,\n        href: href\n      });\n      scrollTo(href);\n    };\n    _this.renderAnchorLink = function (_ref) {\n      var getPrefixCls = _ref.getPrefixCls;\n      var _this$props2 = _this.props,\n        customizePrefixCls = _this$props2.prefixCls,\n        href = _this$props2.href,\n        title = _this$props2.title,\n        children = _this$props2.children,\n        className = _this$props2.className,\n        target = _this$props2.target;\n      var prefixCls = getPrefixCls('anchor', customizePrefixCls);\n      var active = _this.context.activeLink === href;\n      var wrapperClassName = classNames(\"\".concat(prefixCls, \"-link\"), _defineProperty({}, \"\".concat(prefixCls, \"-link-active\"), active), className);\n      var titleClassName = classNames(\"\".concat(prefixCls, \"-link-title\"), _defineProperty({}, \"\".concat(prefixCls, \"-link-title-active\"), active));\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: wrapperClassName\n      }, /*#__PURE__*/React.createElement(\"a\", {\n        className: titleClassName,\n        href: href,\n        title: typeof title === 'string' ? title : '',\n        target: target,\n        onClick: _this.handleClick\n      }, title), children);\n    };\n    return _this;\n  }\n  _createClass(AnchorLink, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.context.registerLink(this.props.href);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(_ref2) {\n      var prevHref = _ref2.href;\n      var href = this.props.href;\n      if (prevHref !== href) {\n        this.context.unregisterLink(prevHref);\n        this.context.registerLink(href);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.context.unregisterLink(this.props.href);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, this.renderAnchorLink);\n    }\n  }]);\n  return AnchorLink;\n}(React.Component);\nAnchorLink.defaultProps = {\n  href: '#'\n};\nAnchorLink.contextType = AnchorContext;\nexport default AnchorLink;", "map": {"version": 3, "names": ["_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "classNames", "ConfigConsumer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AnchorLink", "_React$Component", "_super", "_this", "apply", "arguments", "handleClick", "e", "_this$context", "context", "scrollTo", "onClick", "_this$props", "props", "href", "title", "renderAnchorLink", "_ref", "getPrefixCls", "_this$props2", "customizePrefixCls", "prefixCls", "children", "className", "target", "active", "activeLink", "wrapperClassName", "concat", "titleClassName", "createElement", "key", "value", "componentDidMount", "registerLink", "componentDidUpdate", "_ref2", "prevHref", "unregisterLink", "componentWillUnmount", "render", "Component", "defaultProps", "contextType"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/anchor/AnchorLink.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigConsumer } from '../config-provider';\nimport AnchorContext from './context';\n\nvar AnchorLink = /*#__PURE__*/function (_React$Component) {\n  _inherits(AnchorLink, _React$Component);\n\n  var _super = _createSuper(AnchorLink);\n\n  function AnchorLink() {\n    var _this;\n\n    _classCallCheck(this, AnchorLink);\n\n    _this = _super.apply(this, arguments);\n\n    _this.handleClick = function (e) {\n      var _this$context = _this.context,\n          scrollTo = _this$context.scrollTo,\n          onClick = _this$context.onClick;\n      var _this$props = _this.props,\n          href = _this$props.href,\n          title = _this$props.title;\n      onClick === null || onClick === void 0 ? void 0 : onClick(e, {\n        title: title,\n        href: href\n      });\n      scrollTo(href);\n    };\n\n    _this.renderAnchorLink = function (_ref) {\n      var getPrefixCls = _ref.getPrefixCls;\n      var _this$props2 = _this.props,\n          customizePrefixCls = _this$props2.prefixCls,\n          href = _this$props2.href,\n          title = _this$props2.title,\n          children = _this$props2.children,\n          className = _this$props2.className,\n          target = _this$props2.target;\n      var prefixCls = getPrefixCls('anchor', customizePrefixCls);\n      var active = _this.context.activeLink === href;\n      var wrapperClassName = classNames(\"\".concat(prefixCls, \"-link\"), _defineProperty({}, \"\".concat(prefixCls, \"-link-active\"), active), className);\n      var titleClassName = classNames(\"\".concat(prefixCls, \"-link-title\"), _defineProperty({}, \"\".concat(prefixCls, \"-link-title-active\"), active));\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: wrapperClassName\n      }, /*#__PURE__*/React.createElement(\"a\", {\n        className: titleClassName,\n        href: href,\n        title: typeof title === 'string' ? title : '',\n        target: target,\n        onClick: _this.handleClick\n      }, title), children);\n    };\n\n    return _this;\n  }\n\n  _createClass(AnchorLink, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.context.registerLink(this.props.href);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(_ref2) {\n      var prevHref = _ref2.href;\n      var href = this.props.href;\n\n      if (prevHref !== href) {\n        this.context.unregisterLink(prevHref);\n        this.context.registerLink(href);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.context.unregisterLink(this.props.href);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, this.renderAnchorLink);\n    }\n  }]);\n\n  return AnchorLink;\n}(React.Component);\n\nAnchorLink.defaultProps = {\n  href: '#'\n};\nAnchorLink.contextType = AnchorContext;\nexport default AnchorLink;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,aAAa,MAAM,WAAW;AAErC,IAAIC,UAAU,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACxDP,SAAS,CAACM,UAAU,EAAEC,gBAAgB,CAAC;EAEvC,IAAIC,MAAM,GAAGP,YAAY,CAACK,UAAU,CAAC;EAErC,SAASA,UAAUA,CAAA,EAAG;IACpB,IAAIG,KAAK;IAETX,eAAe,CAAC,IAAI,EAAEQ,UAAU,CAAC;IAEjCG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAErCF,KAAK,CAACG,WAAW,GAAG,UAAUC,CAAC,EAAE;MAC/B,IAAIC,aAAa,GAAGL,KAAK,CAACM,OAAO;QAC7BC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;QACjCC,OAAO,GAAGH,aAAa,CAACG,OAAO;MACnC,IAAIC,WAAW,GAAGT,KAAK,CAACU,KAAK;QACzBC,IAAI,GAAGF,WAAW,CAACE,IAAI;QACvBC,KAAK,GAAGH,WAAW,CAACG,KAAK;MAC7BJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACJ,CAAC,EAAE;QAC3DQ,KAAK,EAAEA,KAAK;QACZD,IAAI,EAAEA;MACR,CAAC,CAAC;MACFJ,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC;IAEDX,KAAK,CAACa,gBAAgB,GAAG,UAAUC,IAAI,EAAE;MACvC,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;MACpC,IAAIC,YAAY,GAAGhB,KAAK,CAACU,KAAK;QAC1BO,kBAAkB,GAAGD,YAAY,CAACE,SAAS;QAC3CP,IAAI,GAAGK,YAAY,CAACL,IAAI;QACxBC,KAAK,GAAGI,YAAY,CAACJ,KAAK;QAC1BO,QAAQ,GAAGH,YAAY,CAACG,QAAQ;QAChCC,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,MAAM,GAAGL,YAAY,CAACK,MAAM;MAChC,IAAIH,SAAS,GAAGH,YAAY,CAAC,QAAQ,EAAEE,kBAAkB,CAAC;MAC1D,IAAIK,MAAM,GAAGtB,KAAK,CAACM,OAAO,CAACiB,UAAU,KAAKZ,IAAI;MAC9C,IAAIa,gBAAgB,GAAG9B,UAAU,CAAC,EAAE,CAAC+B,MAAM,CAACP,SAAS,EAAE,OAAO,CAAC,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqC,MAAM,CAACP,SAAS,EAAE,cAAc,CAAC,EAAEI,MAAM,CAAC,EAAEF,SAAS,CAAC;MAC9I,IAAIM,cAAc,GAAGhC,UAAU,CAAC,EAAE,CAAC+B,MAAM,CAACP,SAAS,EAAE,aAAa,CAAC,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqC,MAAM,CAACP,SAAS,EAAE,oBAAoB,CAAC,EAAEI,MAAM,CAAC,CAAC;MAC7I,OAAO,aAAa7B,KAAK,CAACkC,aAAa,CAAC,KAAK,EAAE;QAC7CP,SAAS,EAAEI;MACb,CAAC,EAAE,aAAa/B,KAAK,CAACkC,aAAa,CAAC,GAAG,EAAE;QACvCP,SAAS,EAAEM,cAAc;QACzBf,IAAI,EAAEA,IAAI;QACVC,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE;QAC7CS,MAAM,EAAEA,MAAM;QACdb,OAAO,EAAER,KAAK,CAACG;MACjB,CAAC,EAAES,KAAK,CAAC,EAAEO,QAAQ,CAAC;IACtB,CAAC;IAED,OAAOnB,KAAK;EACd;EAEAV,YAAY,CAACO,UAAU,EAAE,CAAC;IACxB+B,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAAC,IAAI,CAACrB,KAAK,CAACC,IAAI,CAAC;IAC5C;EACF,CAAC,EAAE;IACDiB,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASG,kBAAkBA,CAACC,KAAK,EAAE;MACxC,IAAIC,QAAQ,GAAGD,KAAK,CAACtB,IAAI;MACzB,IAAIA,IAAI,GAAG,IAAI,CAACD,KAAK,CAACC,IAAI;MAE1B,IAAIuB,QAAQ,KAAKvB,IAAI,EAAE;QACrB,IAAI,CAACL,OAAO,CAAC6B,cAAc,CAACD,QAAQ,CAAC;QACrC,IAAI,CAAC5B,OAAO,CAACyB,YAAY,CAACpB,IAAI,CAAC;MACjC;IACF;EACF,CAAC,EAAE;IACDiB,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASO,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAC9B,OAAO,CAAC6B,cAAc,CAAC,IAAI,CAACzB,KAAK,CAACC,IAAI,CAAC;IAC9C;EACF,CAAC,EAAE;IACDiB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASQ,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAa5C,KAAK,CAACkC,aAAa,CAAChC,cAAc,EAAE,IAAI,EAAE,IAAI,CAACkB,gBAAgB,CAAC;IACtF;EACF,CAAC,CAAC,CAAC;EAEH,OAAOhB,UAAU;AACnB,CAAC,CAACJ,KAAK,CAAC6C,SAAS,CAAC;AAElBzC,UAAU,CAAC0C,YAAY,GAAG;EACxB5B,IAAI,EAAE;AACR,CAAC;AACDd,UAAU,CAAC2C,WAAW,GAAG5C,aAAa;AACtC,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}