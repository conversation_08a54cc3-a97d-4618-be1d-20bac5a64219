import { ExportsSubpaths } from 'resolve-import';
import type { PolyfillSet } from './polyfills.js';
import { Package, TshyConfig, TshyExport } from './types.js';
export declare const getImpTarget: (s: string | TshyExport | undefined | null, polyfills?: Map<string, PolyfillSet>) => string | null | undefined;
export declare const getReqTarget: (s: string | TshyExport | undefined | null, polyfills?: Map<string, PolyfillSet>) => string | null | undefined;
export declare const setMain: (c: TshyConfig | undefined, pkg: Package & {
    exports: ExportsSubpaths;
}) => undefined;
declare const _default: ExportsSubpaths;
export default _default;
//# sourceMappingURL=exports.d.ts.map