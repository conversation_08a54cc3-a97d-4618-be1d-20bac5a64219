{"core/audits/accessibility/accesskeys.js | description": {"message": "Med tilgjengelighetsnøkler kan brukere raskt fokusere på deler av siden. Hver tilgjengelighetsnøkkel må være unik for at navigeringen skal fungere riktig. [Finn ut mer om tilgjengelighetsnøkler](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-verdier er ikke unike"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-verdiene er unike"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON> <PERSON>-`role` støtter en spesifikk undergruppe av `aria-*`-attributter. Manglende samsvar mellom disse gjør `aria-*`-attributtene ugyldige. [Finn ut hvordan du knytter ARIA-attributter til de riktige rollene](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-attributter samsvarer ikke med rollene sine"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-attributtene samsvarer med rollene sine"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "<PERSON><PERSON>r elementer ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut hvordan du gjør kommandoelementer mer tilgjengelige](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`-, `link`- og `menuitem`-elementer har ikke tilgjengelige navn."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`-, `link`- og `menuitem`-elementer har tilgjengelige navn"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Assisterende teknologi, for e<PERSON><PERSON><PERSON>ere, fungerer ikke konsekvent når `aria-hidden=\"true\"` er angitt på dokumentets `<body>`-element. [<PERSON> ut hvordan `aria-hidden` påvirker dokumentets «body»-element](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` er til stede på dokumentets `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` er ikke til stede på dokumentets `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Fokuserbare underelementer i `[aria-hidden=\"true\"]`-elementer gjør at disse interaktive elementene ikke er tilgjengelige for brukere av assisterende teknologi, for eksempel skjermlesere. [<PERSON> ut hvordan `aria-hidden` påvirker fokuserbare elementer](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]`-elementer inneholder fokuserbare underelementer"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]`-elementer inneholder ikke fokuserbare underelementer"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Når inndatafelt ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut mer om merking av inndatafelt](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA-inndatafelt har ikke tilgjengelige navn"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA-inndatafelt har tilgjengelige navn"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "<PERSON><PERSON><PERSON> «meter»-elementer ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut hvordan du gir navn til `meter`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA-`meter`-elementer har ikke tilgjengelige navn."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA-`meter`-elementer har tilgjengelige navn"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "N<PERSON>r `progressbar`-elementer ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut hvordan du merker `progressbar`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA-`progressbar`-elementer har ikke tilgjengelige navn."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA-`progressbar`-elementer har tilgjengelige navn"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON>-roller har obligatoriske attributter som beskriver elementenes tilstand for skjermlesere. [Finn ut mer om roller og obligatoriske attributter](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-elementer har ikke alle de obligatoriske `[aria-*]`-attributtene"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-elementene har alle de obligatoriske `[aria-*]`-attributtene"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "<PERSON><PERSON> overordnede ARIA-roller må inneholde spesifikke underordnede roller for å utføre de tiltenkte tilgjengelighetsfunksjonene. [Finn ut mer om roller og obligatoriske underordnede elementer](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementer som har en ARIA-`[role]` og krever underordnede elementer som inneholder en spesifikk `[role]`, mangler noen av eller alle disse underordnede elementene."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementer som har en ARIA-`[role]` og krever underordnede elementer som inneholder en spesifikk `[role]`, har alle de nødvendige underordnede elementene."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON><PERSON> underordnede ARIA-roller må ligge innenfor spesifik<PERSON> overordnede roller for å utføre de tiltenkte tilgjengelighetsfunksjonene på riktig måte. [Finn ut mer om ARIA-roller og obligatoriske overordnede elementer](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-elementer ligger ikke i de obligatoriske overordnede elementene"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-elementer ligger i de obligatoriske overordnede elementene"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA-roller må ha gyldige verdier for å utføre de tiltenkte tilgjengelighetsfunksjonene. [Finn ut mer om gyldige ARIA-roller](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-verdi<PERSON> er ikke gyldige"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-verdi<PERSON> er gyldige"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Når av/på-felt ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut mer om av/på-felt](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA-av/på-felt har ikke tilgjengelige navn"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA-av/på-felt har tilgjengelige navn"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "<PERSON><PERSON><PERSON> «tooltip»-elementer ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut hvordan du gir navn til `tooltip`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA-`tooltip`-elementer har ikke tilgjengelige navn."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA-`tooltip`-elementer har tilgjengelige navn"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "<PERSON><PERSON>r `treeitem`-elementer ikke har tilgjengelige navn, beskriver skjermlesere dem med generiske navn. Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut mer om merking av `treeitem`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA-`treeitem`-elementer har ikke tilgjengelige navn."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA-`treeitem`-elementer har tilgjengelige navn"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Assisterende teknologi, for eks<PERSON><PERSON> skjermlesere, kan ikke tolke ARIA-attributter med ugyldige verdier. [Finn ut mer om gyldige verdier for ARIA-attributter](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-attributter har ikke gyldige verdier"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-attributtene har gyldige verdier"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Assisterende teknologi, som s<PERSON><PERSON>, kan ikke tolke <PERSON>-attributter med ugyldige navn. [Finn ut mer om gyldige ARIA-attributter](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-attributter er ugyldige eller feilstavet"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-attributtene er gyldige og ikke feilstavet"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementer som ikke besto kontrollen"}, "core/audits/accessibility/button-name.js | description": {"message": "<PERSON><PERSON><PERSON> knapper ikke har tilgjengelige navn, beskriver skjermlesere dem som «knapp». Dermed er de ubrukelige for brukere som er avhengige av skjermlesere. [Finn ut hvordan du gjør knapper mer tilgjengelige](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Knapper har ikke tilgjengelige navn"}, "core/audits/accessibility/button-name.js | title": {"message": "Knappene har tilgjengelige navn"}, "core/audits/accessibility/bypass.js | description": {"message": "<PERSON><PERSON> du legger til måter å hoppe over repeterende innhold på, kan tastaturbrukere navigere mer effektivt på siden. [<PERSON> ut mer om blokker for omgåelse](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "<PERSON><PERSON> mangler overskrift, landemerkeregion eller link for å hoppe over innhold"}, "core/audits/accessibility/bypass.js | title": {"message": "Siden inneholder en overskrift, en landemerkeregion eller en link for å hoppe over innhold"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Tekst med lav kontrast er vanskelig eller umulig å lese for mange brukere. [Finn ut hvordan du kan sørge for tilstrekkelig fargekontrast](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Kontrastforholdet mellom bakgrunns- og forgrunnsfarger er ikke tilstrekkelig."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Kontrastforholdet mellom bakgrunns- og forgrunnsfargene er tilstrekkelig"}, "core/audits/accessibility/definition-list.js | description": {"message": "<PERSON><PERSON>r definisjonslister ikke er riktig kodet, kan resultatene fra skjermlesere bli forvirrende eller unøyaktige. [Finn ut hvordan du strukturerer definisjonslister på riktig måte](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-elementer inneholder ikke bare velordnede `<dt>`- og `<dd>`-grupper og `<script>`-, `<template>`- eller `<div>`-elementer."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`-elementer inneholder bare velordnede `<dt>`- og `<dd>`-grupper og `<script>`-, `<template>`- eller `<div>`-elementer."}, "core/audits/accessibility/dlitem.js | description": {"message": "Elementer på definisjonslister (`<dt>` og `<dd>`) må være omsluttet av overordnede `<dl>`-elementer for å sørge for at skjermlesere kan lese dem opp riktig. [Finn ut hvordan du strukturerer definisjonslister på riktig måte](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementer i definisjonslister er ikke omsluttet av `<dl>`-elementer"}, "core/audits/accessibility/dlitem.js | title": {"message": "Elementene i definisjonslister er omsluttet av `<dl>`-elementer"}, "core/audits/accessibility/document-title.js | description": {"message": "Tittelen gir brukere av skjermlesere oversikt over siden, og søkemotorbrukere er svært avhengige av den for å avgjøre om siden er relevant for søket deres. [Finn ut mer om dokumenttitler](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumentet har ikke noe `<title>`-element"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokumentet har et `<title>`-element"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Alle fokuserbare elementer må ha unike `id`-attributter for å sikre at de er synlige for assisterende teknologi. [Finn ut hvordan du korrigerer dupliserte `id`-verdier](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]`-attributter på aktive, fokuserbare elementer er ikke unike"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]`-attributter på aktive, fokuserbare elementer er unike"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA-ID-attributter må ha unike verdier for å unngå at andre forekomster blir oversett av assisterende teknologi. [Finn ut hvordan du korrigerer dupliserte ARIA-ID-er](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA-ID-er er ikke unike"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA-ID-er er unike"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Skjemafelt med flere etiketter kan bli lest opp på en forvirrende måte av assisterende teknologi, for eks<PERSON>pel skjermlesere som bruker enten den første etiketten, den siste etiketten eller alle etikettene. [Finn ut hvordan du bruker skjemaetiketter](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> har flere etiketter"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Det finnes ingen skjemafelt med flere etiketter"}, "core/audits/accessibility/frame-title.js | description": {"message": "Brukere av skjermlesere er avhengige av titler for rammeelementer (som «frame» og «iframe») for å forstå innholdet i dem. [Finn ut mer om titler for rammeelementer](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- eller `<iframe>`-elementer mangler tittel"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- og `<iframe>`-elementer har titler"}, "core/audits/accessibility/heading-order.js | description": {"message": "Godt organiserte overskrifter som ikke hopper over <PERSON><PERSON><PERSON><PERSON>, form<PERSON><PERSON> sidens semantiske struktur, slik at den blir enklere å forstå og navigere ved bruk av assisterende teknologi. [Finn ut mer om overskriftsrekkefølge](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Overskriftselementer vises ikke i sekvensielt synkende rekkefølge"}, "core/audits/accessibility/heading-order.js | title": {"message": "Overskriftselementer vises i sekvensielt synkende rekkefølge"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Hvis en side ikke angir noe `lang`-attributt, antar skjerm<PERSON>ere at siden er på standardspråket som brukeren valgte under konfigureringen av skjermleseren. Hvis siden ikke faktisk er på standardspråket, kan det hende skjermleseren leser teksten på siden feil. [Finn ut mer om `lang`-attributtet](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-elementet har ikke noe gyldig `[lang]`-attributt"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-elementet har et `[lang]`-attributt"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Ved å angi et gyldig [BCP 47-språk](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hjelper du skjermlesere med å lese opp teksten riktig. [Finn ut hvordan du bruker `lang`-attributtet](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-elementet har ikke noen gyldig verdi for `[lang]`-attributtet."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>`-elementet har en gyldig verdi for `[lang]`-attributtet"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informative elementer bør ta sikte på korte og beskrivende alternative tekster. Dekorative elementer kan ignoreres med tomme alt-attributter. [Finn ut mer om `alt`-attributtet](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Bildeelementer har ikke `[alt]`-attributter"}, "core/audits/accessibility/image-alt.js | title": {"message": "Bildeelementene har `[alt]`-attributter"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> et bilde brukes som `<input><PERSON><PERSON><PERSON><PERSON><PERSON>, bør du oppgi en alternativ tekst som hjelper brukere av skjermlesere med å forstå hva knappen er til. [Finn ut mer om alt-tekst for «input»-bilder](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-elementer har ikke `[alt]`-tekst"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-elementer har `[alt]`-tekst"}, "core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> for at skjemakontroller leses opp på riktig måte av assisterende teknologi, som skjermlesere. [<PERSON> ut mer om etiketter for skjemaelementer](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Skjemaelementer har ikke tilknyttede etiketter"}, "core/audits/accessibility/label.js | title": {"message": "Skjemaelementene har tilknyttede etiketter"}, "core/audits/accessibility/link-name.js | description": {"message": "<PERSON><PERSON>r du bruker linktekst (og alternativ tekst for bilder som brukes som linker) som er tydelig, unik og fokuserbar, blir navigeringsopplevelsen bedre for brukere av skjermlesere. [Finn ut hvordan du gjør linker tilgjengelige](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON>er har ikke tydelige navn"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON> har tydelige navn"}, "core/audits/accessibility/list.js | description": {"message": "Skjermlesere har en spesifikk måte å lese opp lister på. Du kan øke kvaliteten på resultatene fra skjermlesere ved å bruke en god listestruktur. [Finn ut mer om god listestruktur](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Lister inneholder ikke kun `<li>`-elementer og elementer som støtter skript (`<script>` og `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Listene inneholder bare `<li>`-elementer og elementer som støtter skript (`<script>` og `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Skjer<PERSON>lesere krever at listeelementer (`<li>`) ligger i overordnede `<ul>`-, `<ol>`- eller `<menu>`-elementer, ellers kan de ikke leses opp på riktig måte. [Finn ut mer om god listestruktur](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Listeelementer (`<li>`) ligger ikke i overordnede `<ul>`-, `<ol>`- eller `<menu>`-elementer."}, "core/audits/accessibility/listitem.js | title": {"message": "Listeelementene (`<li>`) ligger i overordnede `<ul>`-, `<ol>`- eller `<menu>`-elementer"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "<PERSON><PERSON><PERSON> forventer ikke at sider oppdateres automatisk, og hvis dette skjer, flyttes fokuset tilbake til toppen av siden. Dette kan føre til en frustrerende eller forvirrende brukeropplevelse. [Finn ut mer om metataggen for oppdatering](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumentet bruker `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumentet bruker ikke `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Deaktivering av zoom er problematisk for brukere med nedsatt synsevne som har behov for å forstørre skjermen for å se innholdet på nettsider. [Finn ut mer om metataggen for synlig område](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` brukes i `<meta name=\"viewport\">`-elementet, eller `[maximum-scale]`-attributtet er mindre enn 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` brukes ikke i `<meta name=\"viewport\">`-elementet, og `[maximum-scale]`-attributtet er ikke mindre enn 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Skjermlesere kan ikke oversette innhold som ikke er tekst. Ved å legge til alternativ tekst i `<object>`-elementer hjelper du skjermlesere med å formidle mening til brukerne. [Finn ut mer om alt-tekst for `object`-elementer](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-elementer har ikke alternativ tekst"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-elementer har alternativ tekst"}, "core/audits/accessibility/tabindex.js | description": {"message": "Større verdier enn 0 antyder en eksplisitt navigeringsrekkefølge. Selv om dette teknisk sett er gyldig, kan det ofte være frustrerende for brukere som er avhengige av assisterende teknologi. [Finn ut mer om `tabindex`-attributtet](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "<PERSON>en elementer har en `[tabindex]`-verdi som er større enn 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Ingen elementer har en `[tabindex]`-verdi som er større enn 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Skjermlesere har funksjonalitet som gjør det enklere å navigere i tabeller. Ved å sørge for at `<td>`-celler som bruker `[headers]`-attributtet, kun refererer til andre celler i den samme tabellen, kan du gjøre opplevelsen bedre for brukere av skjermlesere. [Finn ut mer om `headers`-attributtet](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Celler som er en del av et `<table>`-element og bruker `[headers]`-attributtet, refererer til et element (`id`) som ikke finnes i den samme tabellen."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Celler som er en del av et `<table>`-element og bruker `[headers]`-attributtet, refererer til tabellceller i den samme tabellen."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Skjermlesere har funksjonalitet som gjør det enklere å navigere i tabeller. Ved å sørge for at tabelloverskrifter alltid refererer til spesifikke cellesett, kan du gjøre opplevelsen bedre for brukere av skjermlesere. [Finn ut mer om tabelloverskrifter](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>`-elementer og elementer med `[role=\"columnheader\"/\"rowheader\"]` har ikke datacellene de beskriver."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>`-elementene og elementene med `[role=\"columnheader\"/\"rowheader\"]` har datacellene de beskriver."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ved å angi et gyldig [BCP 47-språk](https://www.w3.org/International/questions/qa-choosing-language-tags#question) for elementer bidrar du til at skjermlesere uttaler teksten riktig. [Finn ut hvordan du bruker `lang`-attributtet](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-attributter mangler gyldige verdier"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-attributtene har gyldige verdier"}, "core/audits/accessibility/video-caption.js | description": {"message": "<PERSON><PERSON><PERSON> videoer har teksting, blir det lettere for døve og hørselshemmede brukere å få med seg informasjonen i dem. [Finn ut mer om videoteksting](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>`-elementer mangler et `<track>`-element med `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>`-elementer inneholder et `<track>`-element med `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Nåværende verdi"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> token"}, "core/audits/autocomplete.js | description": {"message": "Med `autocomplete` kan brukere sende inn skjemaer raskere. For å gjøre ting lettere for brukerne, vurder å slå dette på ved å angi en gyldig verdi for `autocomplete`-attributtet. [Finn ut mer om `autocomplete` i skjemaer](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>`-elementer har ikke riktige `autocomplete`-attributter"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON><PERSON> manuell gjenn<PERSON>g"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Gjennomgå rekkefølgen på tokener"}, "core/audits/autocomplete.js | title": {"message": "`<input>`-<PERSON><PERSON> bruker `autocomplete` på riktig måte"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete`-tokenet/-tokenene «{token}» er ugyldig(e) i {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Gjennomgå tokenrekkefølgen «{tokens}» i {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON> u<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | description": {"message": "Mange navigeringer utføres ved å gå tilbake til en tidligere side eller fremover. Frem-og-tilbake-bufferen (bfcache) kan gjøre disse gjengivelsene raskere. [Finn ut mer om bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 årsak til feil}other{# årsaker til feil}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Årsak til feilen"}, "core/audits/bf-cache.js | failureTitle": {"message": "<PERSON>n hindret gjenoppretting i frem-og-tilbake-bufferen"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Feiltype"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Kan ikke ut<PERSON>"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Ventende nettleserstøtte"}, "core/audits/bf-cache.js | title": {"message": "<PERSON>n hindret ikke gjenoppretting i frem-og-tilbake-bufferen"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome-utvidelser gjør innlastingen av denne siden tregere. Prøv å revidere siden i inkognitomodus eller fra en Chrome-profil uten utvidelser."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "S<PERSON><PERSON>te<PERSON>uering"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Skriptparsing"}, "core/audits/bootup-time.js | columnTotal": {"message": "Total CPU-tid"}, "core/audits/bootup-time.js | description": {"message": "Vurder å redusere tiden som brukes til parsing, kompilering og kjøring av JavaScript. Levering av mindre JS-ressurser kan bidra til dette. [Finn ut hvordan du reduserer kjøretiden for JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Reduser JavaScript-kjøretiden"}, "core/audits/bootup-time.js | title": {"message": "JavaScript-kjøretid"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Fjern store, dupliserte JavaScript-moduler <PERSON><PERSON>ak<PERSON> for å redusere antall byte som brukes unødvendig av nettverksaktiviteten. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Fjern duplikatmoduler i JavaScript-pakker"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Store GIF-er er mindre effektive for visning av animert innhold. I stedet for GIF bør du vurdere å bruke MPEG4/WebM-videoer for animasjon og PNG/WebP for statiske bilder, da dette belaster nettverket mindre. [Finn ut mer om effektive videoformater](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)."}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Bruk videoformat for animert innhold"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill- og transform-kode gjør det mulig for nettlesere i eldre versjoner å bruke nye JavaScript-funksjoner. Mange former for slik kode trengs imidlertid ikke i moderne nettlesere. Ta i bruk en moderne skriptimplementeringsstrategi for JavaScript-pakken din ved å bruke module/nomodule-funksjonsoppdagelse for å redusere mengden kode som sendes til moderne nettlesere, samtidig som støtten for nettlesere i eldre versjoner ivaretas. [Finn ut hvordan du bruker moderne JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Unngå å sende JavaScript i en eldre versjon til moderne nettlesere"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Bildeformater som WebP og AVIF gir ofte bedre komprimering enn PNG og JPEG, noe som betyr raskere nedlastinger og lavere dataforbruk. [Finn ut mer om moderne bildeformater](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Bruk nyere bildeformater"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Vurder å utsette innlastingen av bilder som er utenfor skjermen eller skjult, til etter at alle kritiske ressurser er ferdig innlastet. Dette reduserer tiden det tar før siden blir interaktiv. [Finn ut hvordan du utsetter innlasting av bilder som ikke er synlige på skjermen](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON><PERSON> bilder utenfor skjermen"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ressurser blokkerer første opptegning (FP) av siden. Vurder å levere kritisk JS/CSS innebygd og utsette all JS/CSS som ikke er kritisk. [Finn ut hvordan du eliminerer ressurser som blokkerer gjengivelse](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Eliminer ressurser som blokkerer gjengivelse"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Store nettverksressurser koster brukerne ekte penger og er sterkt forbundet med lange innlastingstider. [Finn ut hvordan du reduserer mengden data som må overføres](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Den totale st<PERSON><PERSON><PERSON> var {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Unngå enorme nettverksressurser"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Unngår enorme nettverksbelastninger"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifiserte CSS-filer kan redusere nettverksbelastningen. [Finn ut hvordan du minifiserer CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Forminsk CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifiserte JavaScript-filer kan redusere mengden data som må overføres, og parsetiden for skript. [Finn ut hvordan du minifiserer JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Forminsk JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Reduser antall ubrukte regler i stilark, og utsett innlasting av CSS som ikke brukes på innholdet på den synlige delen av nettsiden, for å redusere antall byte som går med til nettverksaktivitet. [Finn ut hvordan du reduserer mengden ubrukt CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduser mengden ubrukt CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reduser mengden ubrukt JavaScript, og utsett innlasting av skript frem til de trengs, for å redusere antall byte som går med til nettverksaktivitet. [Finn ut hvordan du reduserer mengden ubrukt JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduser mengden ubrukt JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "En lang bufferlevetid kan gjøre at gjentatte besøk på siden din går raskere. [Finn ut mer om effektive bufferregler](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 ressurs funnet}other{# ressurser funnet}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Vis statiske ressurser med effektive buffer-retningslinjer"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Bruker effektive buffer-retningslinjer på statiske ressurser"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimaliserte bilder lastes inn raskere og bruker mindre mobildata. [Finn ut hvordan du kan kode bilder på en effektiv måte](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Effektiviser omgjøring av bilder til kode"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Faktiske dimensjoner"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Viste dimensjoner"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Bildene var større enn visningsstø<PERSON>sen"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Bildene var passende for visningsstørrelsen"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Vis bilder som har passende størrelse, for å spare mobildata og redusere innlastingstiden. [Finn ut hvordan du endrer størrelsen på bilder](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Velg riktige bildestø<PERSON>"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstbaserte ressurser bør leveres komprimert (med gzip, deflate eller brotli) for å minimere antall byte som sendes over nettverket. [Finn ut mer om tekstkomprimering](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Aktiver tekstkomprimering"}, "core/audits/content-width.js | description": {"message": "<PERSON><PERSON> bredden på appinnholdet ikke samsvarer med bredden på det synlige området, er appen kanskje ikke optimalisert for mobilskjermer. [Finn ut hvordan du tilpasser innholdet etter størrelsen på visningsområdet](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Det synlige områdets størrelse på {innerWidth} px samsvarer ikke med vindusstø<PERSON>sen på {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "Innholdet har ikke riktig størrelse i forhold til det synlige området"}, "core/audits/content-width.js | title": {"message": "Innholdet har riktig størrelse i forhold til det synlige området"}, "core/audits/critical-request-chains.js | description": {"message": "Kjedene med kritiske forespørsler nedenfor viser hvilke ressurser som lastes inn med høy prioritet. Vurder å redusere lengden på kjedene, redusere nedlastingsstørrelsen på ressursene eller utsette nedlasting av unødvendige ressurser for å bedre sideinnlastingen. [Finn ut hvordan du unngår kjeder med kritiske forespørsler](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 kjede funnet}other{# kjeder funnet}}"}, "core/audits/critical-request-chains.js | title": {"message": "Unngå kjeding av kritiske forespørsler"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiv"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Intensitet"}, "core/audits/csp-xss.js | description": {"message": "En sterk Content Security Policy (CSP) reduserer faren for skriptangrep på tvers av nettsteder (XSS) betydelig. [Finn ut hvordan du bruker en CSP for å forhindre XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntaks"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "<PERSON>n inneholder en CSP som er definert i en <meta>-tagg. Vurder å flytte CSP-en til en HTTP-overskrift eller definere en annen streng CSP i en HTTP-overskrift."}, "core/audits/csp-xss.js | noCsp": {"message": "Fant ingen CSP i håndhevelsesmodus"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON> for at CSP-en er effektiv mot XSS-angrep"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Avvikling/varsel"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Avviklede API-er kommer etter hvert til å bli fjernet fra nettleseren. [Finn ut mer om avviklede API-er](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 varsel er funnet}other{# varsler er funnet}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Bruker avviklede API-er"}, "core/audits/deprecations.js | title": {"message": "Unngår å bruke avviklede API-er"}, "core/audits/dobetterweb/charset.js | description": {"message": "En tegnkodingsdeklarasjon kreves. Dette kan gjøres med en `<meta>`-tagg i de første 1024 bytene av HTML-koden eller i HTTP-svarhodet «Content-Type». [Finn ut mer om deklarering av tegnkodingen](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Tegnsettsdeklarasjonen mangler eller forekommer for sent i HTML-koden"}, "core/audits/dobetterweb/charset.js | title": {"message": "Definerer tegnsettet ordentlig"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Ved å angi en doctype forhindrer du nettleseren fra å bytte til modus for bred kompatibilitet. [Finn ut mer om doctype-deklarasjonen](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Doctype-navnet må være strengen `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokumentet inneholder en `doctype` som utløser `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumentet må ha en doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Forventet at publicId skulle være en tom streng"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Forventet at systemId skulle være en tom streng"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokumentet inneholder en `doctype` som utløser `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Siden har ikke HTML som doctype og utløser derfor modus for bred kompatibilitet."}, "core/audits/dobetterweb/doctype.js | title": {"message": "Siden har HTML som doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistikk"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Verdi"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Store DOM-er øker minnebruken, forårsa<PERSON> lengre [stilberegninger](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) og utløser kostbare [dynamiske tilpasninger av layouten](https://developers.google.com/speed/articles/reflow). [Finn ut hvordan du unngår at DOM-en blir for stor](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}other{# elementer}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Unngå for stor DOM-struktur"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimal DOM-dybde"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Totalt antall <PERSON>-elementer"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksimalt antall underordnede elementer"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Unngå for stor DOM-struktur"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Brukere er mistroiske overfor eller blir forvirret av nettsteder som uten kontekst spør om å få se posisjonen deres. Vurder å knytte forespørselen opp mot en brukerhandling i stedet. [Finn ut mer om tillatelsen for geolokalisering](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Spør om geolokaliseringstillatelsen ved sideinnlasting"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Unngår å spørre om geolokaliseringstillatelsen ved sideinnlasting"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Sakstype"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Problemer som logges i `Issues`-panelet i Chrome DevTools, tyder på uløste problemer. De kan stamme fra mislykkede nettverksforespørsler, utilstrekkelige sikkerhetskontroller og andre nettleserproblemer. Åpne Issues-panelet i Chrome DevTools for å se flere detaljer om hvert enkelt problem."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problemer ble logget i `Issues`-panelet i Chrome DevTools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blokkert av regel for opphavsuavhengige forespørsler"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Tung ressursbruk på grunn av annonser"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Ingen problemer i `Issues`-panelet i Chrome DevTools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versjon"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Alle JavaScript-grensesnittsbiblioteker som ble funnet på siden. [Finn ut mer om denne diagnostikkrutinen for oppdagelse av JavaScript-biblioteker](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript-biblioteker som ble oppdaget"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "For brukere som har trege til<PERSON>, kan eksterne skript som injiseres dynamisk via `document.write()`, forsinke sideinnlastingen med flere titalls sekunder. [Finn ut hvordan du unngår document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Unngå `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Unngår `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Brukere er mistroiske overfor eller blir forvirret av nettsteder som uten kontekst spør om å få sende varsler. Vurder å knytte forespørselen opp mot brukerbevegelser i stedet. [Finn ut mer om ansvarlige måter å be om varseltillatelse på](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> om varseltillatelsen ved sideinnlasting"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Unngår å spørre om varseltillatelsen ved sideinnlasting"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokoll"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 har mange fordeler sammenlignet med HTTP/1.1, blant annet binære hoder og multipleksing. [Finn ut mer om HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 forespørsel ble ikke vist via HTTP/2}other{# forespørsler ble ikke vist via HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Bruk HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Vurder å markere hendelseslytterne for berøring og musehjul som `passive` for å oppnå bedre ytelse ved rulling på siden. [Finn ut mer om bruk av passive hendelseslyttere](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Bruker ikke passive lyttere for å oppnå bedre ytelse ved rulling på siden"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Bruker passive lyttere for å oppnå bedre ytelse ved rulling på siden"}, "core/audits/errors-in-console.js | description": {"message": "Feil som loggføres i konsollen, tyder på uløste problemer. De kan stamme fra mislykkede nettverksforespørsler og andre nettleserproblemer. [Finn ut mer om disse feilene ved å gå gjennom konsolldiagnostikken](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Nettleserfeil ble loggført i konsollen"}, "core/audits/errors-in-console.js | title": {"message": "Ingen nettleserfeil ble loggført i konsollen"}, "core/audits/font-display.js | description": {"message": "Bruk CSS-funksjonen `font-display` for <PERSON> sikre at teksten er synlig for brukerne mens skrifttyper for nettet lastes inn. [Finn ut mer om `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> for at teksten forblir synlig under innlasting av skrifttyper for nettet"}, "core/audits/font-display.js | title": {"message": "All tekst forblir synlig under innlasting av skrifttype for nettet"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse kunne ikke sjekke `font-display`-verdien for opprinnelsen {fontOrigin} automatisk.}other{Lighthouse kunne ikke sjekke `font-display`-verdiene for opprinnelsen {fontOrigin} automatisk.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Høyde/bredde-forhold (faktisk)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Høyde/bredde-forhold (vist)"}, "core/audits/image-aspect-ratio.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for bilder bør samsvare med det naturlige høyde/bredde-forholdet. [Finn ut mer om høyde/bredde-forholdet til bilder](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Viser bilder med feil høyde/bredde-forhold"}, "core/audits/image-aspect-ratio.js | title": {"message": "Viser bilder med riktig høyde/bredde-forhold"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Faktisk størrelse"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON> st<PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Forventet størrelse"}, "core/audits/image-size-responsive.js | description": {"message": "Bildets naturlige dimensjoner bør være proporsjonale med skjermstørrelsen og pikslenes høyde/bredde-forhold for at bildet skal være så tydelig som mulig. [Finn ut hvordan du viser responsive bilder](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Viser bilder med lav opplø<PERSON>ning"}, "core/audits/image-size-responsive.js | title": {"message": "Viser bilder med riktig opplø<PERSON>ning"}, "core/audits/installable-manifest.js | already-installed": {"message": "Appen er allerede installert"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Kunne ikke laste ned et obligatorisk ikon fra manifestet"}, "core/audits/installable-manifest.js | columnValue": {"message": "Årsak til feilen"}, "core/audits/installable-manifest.js | description": {"message": "Service Worker er teknologien som gjør at appen din kan bruke mange funksjoner for progressive nettprogrammer, f.eks. muligheten til å bruke appen uten nett, legge den til på startskjermen og sende pushvarslinger. Med ordentlige Service Worker- og manifestimplementasjoner kan nettlesere aktivt spørre brukere om de vil legge til appen på startskjermen. Det kan føre til høyere engasjement. [Finn ut mer om kravene til installerbarhet av manifester](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 årsak}other{# årsaker}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifestet til nettappen eller Service Worker oppfyller ikke kravene til installerbarhet"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play Butikk-appnettadressen og Play Butikk-ID-en samsvarer ikke"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Siden er lastet inn i et inkognitovindu"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "«display»-egenskapen i manifestet må være enten «standalone», «fullscreen» eller «minimal-ui»"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifestet inneholder feltet «display_override», og den første støttede skjermmodusen må være enten «standalone», «fullscreen» eller «minimal-ui»"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifestet kunne ikke hentes, er tomt eller kunne ikke parses"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifestets nettadresse ble endret mens manifestet holdt på å bli lastet ned."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifestet inneholder ikke noen av feltene «name» eller «short_name»"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifestet inneholder ikke noe egnet ikon. Ikonet må være i PNG-, SVG- eller WebP-format og ha en størrelse på minst {value0} px, sizes-attributtet må være angitt, og hvis purpose-attributtet er angitt, må det inneholde «any»."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Ingen oppgitte ikoner har en størrelse på minst {value0} px (kvadrat) og er i PNG-, SVG- eller WebP-format med purpose-attributtet satt til «any» eller ikke angitt"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Det nedlastede ikonet var tomt eller skadet"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Ingen Play Butikk-ID er oppgitt"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Siden har ingen manifest-<link>-nettadresse"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Fant ingen samsvarende Service Worker. Du må kanskje laste inn siden på nytt eller sjekke at Service Worker for den gjeldende siden har et omfang som omslutter omfanget («scope») og start-nettadressen («start_url») fra manifestet."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "<PERSON>nne ikke sjekke Service Worker uten et «start_url»-felt i manifestet"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Installerbarhetsfeil-ID-en «{errorId}» gjenkjennes ikke"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Siden vises ikke fra et sikkert opphavsdomene"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Siden lastes ikke inn i hovedrammen"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "<PERSON>n fungerer ikke uten nett"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Det progressive nettappen er avinstallert, og kontrollene knyttet til om apper kan installeres, blir tilbakestilt."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Den angitte applikasjonsplattformen støttes ikke på Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifestet spesifiserer prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications støttes kun i Chrome Beta- og Stable-kanalene på Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse kunne ikke fastslå om det fantes noen Service Worker. Prøv med en nyere versjon av Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Protokollen for manifestets nettadresse ({scheme}) støttes ikke i Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Start-nettadressen til manifestet er ikke gyldig"}, "core/audits/installable-manifest.js | title": {"message": "Manifestet til nettappen og Service Worker oppfyller kravene til installerbarhet"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "En nettadresse i manifestet inneholder et brukernavn, et passord eller en port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Siden fungerer ikke uten nett. Siden kommer ikke til å anses som installerbar når Chrome 93 lanseres i stabil utgave i august 2021."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Blokkert"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Utrygg nettadresse"}, "core/audits/is-on-https.js | columnResolution": {"message": "Forespørselløsning"}, "core/audits/is-on-https.js | description": {"message": "Alle nettsteder bør være beskyttet med HTTPS, selv de som ikke håndterer sensitive opplysninger. Blant annet bør du unngå [blandet innhold](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), som innebærer at enkelte ressurser lastes inn via HTTP selv om den innledende forespørselen foregår via HTTPS. HTTPS forhindrer inntrengere fra å tukle med eller lytte passivt til kommunikasjonen mellom appen din og brukerne dine. Dette er en forutsetning for å kunne bruke HTTP/2 og mange nye nettplattform-API-er. [Finn ut mer om HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 utrygg forespørsel er funnet}other{# utrygge forespørsler er funnet}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Bruker ikke HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Bruker HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Oppgradert til HTTPS automatisk"}, "core/audits/is-on-https.js | warning": {"message": "Tillatt med advarsel"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Dette er det største innholdsrike elementet som ble tegnet innenfor det synlige området. [Finn ut mer om elementet med Største innholdsrike opptegning (LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elementet med største innholdsrike opptegning (LCP)"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS-bidrag"}, "core/audits/layout-shift-elements.js | description": {"message": "Disse DOM-elementene bidrar mest til CLS på siden. [Finn ut hvordan du kan gjøre CLS bedre](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Unngå store utseendeforskyvninger"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Bilder på den synlige delen av nettsiden som lastes inn utsatt, gje<PERSON><PERSON> senere i sidens livssyklus, noe som kan forsinke den største innholdsrike opptegningen (LCP). [Finn ut mer om optimal bruk av utsatt innlasting](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Bildet med største innholdsrike opptegning (LCP) ble lastet inn utsatt"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Bildet med største innholdsrike opptegning (LCP) ble ikke lastet inn utsatt"}, "core/audits/long-tasks.js | description": {"message": "Viser de lengste oppgavene på hovedtråden. Dette er nyttig for å finne ut hvilke oppgaver som bidrar mest til inndataforsinkelser. [Finn ut hvordan du unngår langvarige oppgaver på hovedtråden](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# lang oppgave funnet}other{# lange oppgaver funnet}}"}, "core/audits/long-tasks.js | title": {"message": "Unngå lange oppgaver på hovedtråden"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Vurder å redusere tiden som brukes til parsing, kompilering og kjøring av JS. Levering av mindre JS-ressurser kan bidra til dette. [Finn ut hvordan du minimerer mengden arbeid på hovedtråden](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimer arbeidet på hovedtråden"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimerer arbeidet på hovedtråden"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "For å nå så mange brukere som mulig bør nettsteder fungere i alle de vanligste nettleserne. [Finn ut mer om kompatibilitet med flere nettlesere](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Nettstedet fungerer i ulike nettlesere"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON> for at individuelle sider kan dyplinkes via nettadresser, og at nettadressene er unike, slik at de kan deles på sosiale medier. [Finn ut mer om hvordan du legger inn dyplinker](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Hver side har en nettadresse"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Overganger skal føles smidige når du trykker deg rundt, selv på et tregt nettverk. Denne opplevelsen er avgjørende for brukerens opplevelse av ytelsen. [Finn ut mer om sideoverganger](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Sideoverganger føles ikke som om de blokkerer på nettverket"}, "core/audits/maskable-icon.js | description": {"message": "Maskerbar<PERSON> i<PERSON><PERSON> sørger for at bildet fyller hele omrisset og vises uten kanter når appen installeres på enheter. [Finn ut mer om maskerbare manifestikoner](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifestet har ikke noe maskerbart ikon"}, "core/audits/maskable-icon.js | title": {"message": "Manifestet har et maskerbart ikon"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Akkumulert utseendeforskyvning (CLS) måler bevegelsene til synlige elementer i det synlige området. [Finn ut mer om beregningen Akkumulert utseendeforskyvning (CLS)](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Tid fra interaksjon til neste opptegning måler hvor responsiv siden er, altså hvor lang tid siden bruker på å gi en synlig respons på inndata fra brukerne. [Finn ut mer om beregningen Tid fra interaksjon til neste opptegning](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Den første innholdsrike opptegningen (FCP) markerer den første gangen tekst eller bilder tegnes opp. [Finn ut mer om beregningen Første innholdsrike opptegning (FCP)](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Første vesentlige opptegning (FMP) måler når hovedinnholdet på en side er synlig. [Finn ut mer om beregningen Første vesentlige opptegning (FMP)](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Tid til interaktiv vil si hvor lang tid det tar før siden blir helt interaktiv. [Finn ut mer om beregningen Tid til interaktiv](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Største innholdsrike opptegning (LCP) markerer tidspunktet når den største teksten eller det største bildet tegnes opp. [Finn ut mer om beregningen Største innholdsrike opptegning (LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Den maksimale potensielle forsinkelsen for første inndata som brukerne dine kan oppleve, er varigheten av den lengste oppgaven. [Finn ut mer om beregningen Maksimal potensiell forsinkelse for første inndata](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Hastighetsindeksen viser hvor raskt innholdet på siden blir synlig. [Finn ut mer om beregningen Hastighetsindeks](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Summen av alle tidsperiodene mellom første innholdsrike opptegning (FCP) og Tid til interaktiv, når oppgavelengden har overskredet 50 ms, uttrykt i millisekunder. [Finn ut mer om beregningen Total blokkeringstid](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Rundturstidene (RTT) for nettverket har stor innvirkning på ytelsen. Hvis RTT-en til et bestemt opphav er høy, tyder det på at tjenere som befinner seg nærmere brukeren, muligens kan gi bedre ytelse. [Finn ut mer om rundturstid](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> for nettverket"}, "core/audits/network-server-latency.js | description": {"message": "Tidsforsinkelser fra tjeneren kan påvirke nettytelsen. Hvis tjeneren for et bestemt opphav har høy tidsforsinkelse, tyder det på at tjeneren er overbelastet eller har dårlig ytelse i tjenerdelen. [Finn ut mer om tjenerresponstid](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Forsinkelser i tjenerdelen"}, "core/audits/no-unload-listeners.js | description": {"message": "`unload`-he<PERSON><PERSON><PERSON> utløses ikke på en pålitelig måte. Å lytte til den kan forhindre nettleseroptimaliseringer, som frem-og-tilbake-bufferen. Bruk hendelsene `pagehide` eller `visibilitychange` i stedet. [Finn ut mer om utlasting av hendelseslyttere](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> en `unload`-l<PERSON><PERSON>"}, "core/audits/no-unload-listeners.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `unload`-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/non-composited-animations.js | description": {"message": "Animasjoner som ikke er sammensatte, kan være hakkete og bidra til økt CLS. [Finn ut hvordan du unngår ikke-sammensatte animasjoner](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animert element funnet}other{# animerte elementer funnet}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "En filter-<PERSON>rt egenskap kan flytte piksler"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Målet har en annen animasjon som ikke er kompatibel"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Effekten har en annen sammensetningsmodus enn «replace»"}, "core/audits/non-composited-animations.js | title": {"message": "Unngå ikke-sammensatte animasjoner"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "En transform-<PERSON>rt egenskap er avhengig av stø<PERSON>sen på boksen"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Denne CSS-egenskapen støttes ikke: {properties}}other{Disse CSS-egenskapene støttes ikke: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Effekten har tidsparametere som ikke støttes"}, "core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON><PERSON> for at antall nettverksforespørsler og størrelsen på disse er lavere enn målene som er angitt i de aktuelle ytelsesbegrensningene. [Finn ut mer om ytelsesbegrensninger](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 forespørsel}other{# forespø<PERSON><PERSON>}}"}, "core/audits/performance-budget.js | title": {"message": "Ytelsesbudsjett"}, "core/audits/preload-fonts.js | description": {"message": "Forhåndslast `optional` skrifttyper, slik at førstegangsbesøkende kan bruke dem. [Finn ut mer om forhåndslasting av skrifttyper](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Skrifttyper med `font-display: optional` forhåndslastes ikke"}, "core/audits/preload-fonts.js | title": {"message": "Skrifttyper med `font-display: optional` forhåndslastes"}, "core/audits/prioritize-lcp-image.js | description": {"message": "<PERSON><PERSON> LCP-elementet legges til på siden dynamisk, bør du forhåndslaste bildet for å forbedre LCP-tiden. [Finn ut mer om forhåndslasting av LCP-elementer](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Forhåndslast bildet med største innholdsrike opptegning"}, "core/audits/redirects.js | description": {"message": "Viderekoblinger fører til flere forsinkelser før siden kan lastes inn. [Finn ut hvordan du kan unngå viderekoblinger fra sider](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Unngå flere viderekoblinger av siden"}, "core/audits/resource-summary.js | description": {"message": "For å angi begrensninger for antall sideressurser og størrelsen på disse, legg til en budget.json-fil. [Finn ut mer om ytelsesbegrensninger](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 forespørsel • {byteCount, number, bytes} KiB}other{# forespø<PERSON>ler • {byteCount, number, bytes} Ki<PERSON>}}"}, "core/audits/resource-summary.js | title": {"message": "<PERSON><PERSON> antall forespø<PERSON><PERSON> og størrelsen på overføringer"}, "core/audits/seo/canonical.js | description": {"message": "Kanoniske linker foreslår hvilken nettadresse som skal vises i søkeresultater. [Finn ut mer om kanoniske linker](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Flere motstridende nettadresser ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Ugyldig nettadresse ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "<PERSON><PERSON><PERSON> til en annen `hreflang`-plassering ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Er ikke en fullstendig nettadresse ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "<PERSON><PERSON><PERSON> til domenets rotadresse (startsiden) i stedet for en tilsvarende side med innhold"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumentet har ikke noe gyldig `rel=canonical`-element"}, "core/audits/seo/canonical.js | title": {"message": "Dokumentet har en gyldig `rel=canonical`-link"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Link som ikke kan gje<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Søkemotorer kan bruke `href`-attributter på linker for å gjennomsøke nettsteder. <PERSON><PERSON><PERSON> for at `href`-attributter i a-tagger linker til hensiktsmessige destinasjoner, slik at flere sider på nettstedet kan bli oppdaget. [Finn ut hvordan du gjør linker gjennomsøkbare](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Linker kan ikke gjennomsøkes"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON><PERSON> kan gje<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "<PERSON><PERSON> te<PERSON>t"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Skriftstørrelse"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% av sidetekst"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Skriftstørrelser på mindre enn 12 px er for små til å være leselige og gjør at besøkende på mobil må «klype for å zoome» for å klare å lese teksten. Prøv å sørge for at mer enn 60 % av teksten på siden er 12 px eller større. [Finn ut mer om leselige skriftstørrelser](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} lesbar tekst"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Teksten er uleselig fordi det ikke er noen viewport-metatag som er optimalisert for mobilskjermer."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumentet bruker ikke leselige skriftstørrelser"}, "core/audits/seo/font-size.js | legibleText": {"message": "<PERSON><PERSON><PERSON> te<PERSON>"}, "core/audits/seo/font-size.js | title": {"message": "Dokumentet bruker leselige skriftstørrelser"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang-linker forteller søkemotorer hvilken sideversjon som skal føres opp i søkeresultatene for bestemte språk eller regioner. [Finn ut mer om `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumentet har ikke noe gyldig `hreflang`-attributt"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativ href-verdi"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumentet har et gyldig `hreflang`-attributt"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Uventet språkkode"}, "core/audits/seo/http-status-code.js | description": {"message": "Sider med HTTP-statuskoder som indikerer mislyk<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>, indekseres kanskje ikke skikkelig. [Finn ut mer om HTTP-statuskoder](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Sidens HTTP-statuskode indikerer mislykket forespørsel"}, "core/audits/seo/http-status-code.js | title": {"message": "Sidens HTTP-statuskode er gyldig"}, "core/audits/seo/is-crawlable.js | description": {"message": "Søkemotorer kan ikke ta med sidene dine i søkeresultatene hvis de ikke har tillatelse til å gjennomsøke dem. [Finn ut mer om søkerobotdirektiver](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON>n er blokkert for indeksering"}, "core/audits/seo/is-crawlable.js | title": {"message": "<PERSON>n er ikke blokkert for indeksering"}, "core/audits/seo/link-text.js | description": {"message": "Beskrivende linktekst hjelper søkemotorer med å forstå innholdet. [Finn ut hvordan du gjør linker mer tilgjengelige](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Fant 1 link}other{Fant # linker}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Linker har ikke beskrivende tekst"}, "core/audits/seo/link-text.js | title": {"message": "Linkene har beskrivende tekst"}, "core/audits/seo/manual/structured-data.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> [Testverktøy for strukturerte data](https://search.google.com/structured-data/testing-tool/) og [Structured Data Linter](http://linter.structured-data.org/) for å validere strukturerte data. [Finn ut mer om strukturerte data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturerte data er gyldige"}, "core/audits/seo/meta-description.js | description": {"message": "Metabeskrivelser kan tas med i søkeresultater for å oppsummere sideinnholdet kort og konsist. [Finn ut mer om metabeskrivelsen](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Beskrivelsesteksten er tom."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumentet har ingen metabeskrivelse"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumentet har en metabeskrivelse"}, "core/audits/seo/plugins.js | description": {"message": "Søkemotorer kan ikke indeksere innholdet i programtillegg, og mange enheter begrenser programtillegg eller støtter dem ikke. [Finn ut mer om hvordan du unngår programtillegg](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumentet bruker programtillegg"}, "core/audits/seo/plugins.js | title": {"message": "Dokumentet bruker ikke programtillegg"}, "core/audits/seo/robots-txt.js | description": {"message": "Hvis robots.txt-filen har feil format, kan det hende at søkeroboter ikke forstår hvordan du vil at nettstedet ditt skal gjennomsøkes eller indekseres. [Finn ut mer om robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Forespørselen om robots.txt returnerte HTTP-statusen: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Fant 1 feil}other{Fant # feil}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse kunne ikke laste ned noen robots.txt-fil"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt er ikke gyldig"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt er gyldig"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktive elementer, som knapper og linker, må være store nok (48 x 48 px) og ha nok plass rundt seg til at det er lett å trykke på dem uten at de overlapper andre elementer. [Finn ut mer om trykkbare områder](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} av de trykkbare elementene er store nok"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "De trykkbare elementene er for små fordi det ikke er noen viewport-metatag som er optimalisert for mobilskjermer"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Trykkbare elementer er ikke store nok"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Overlappende trykkbart element"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Trykkbart element"}, "core/audits/seo/tap-targets.js | title": {"message": "Trykkbare elementer er store nok"}, "core/audits/server-response-time.js | description": {"message": "Hold tjenerresponstiden for hoveddokumentet lav, siden alle andre forespørsler avhenger av den. [Finn ut mer om beregningen Tid til første byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Root-dokumentet brukte {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reduser den innledende tjenerresponstiden"}, "core/audits/server-response-time.js | title": {"message": "Den innledende tjenerresponstiden var kort"}, "core/audits/service-worker.js | description": {"message": "Service Worker er teknologien som gjør at appen din kan bruke mange funksjoner for progressive nettprogrammer, f.eks. muligheten til å bruke appen uten nett, legge den til på startskjermen og sende pushvarslinger. [<PERSON> ut mer om Service Worker](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON><PERSON> siden styres av en tjenestearbeider, men ingen `start_url` ble funnet fordi manifestet ikke kunne parses som gyldig JSON"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "<PERSON>ne siden styres av en tjenestearbeider, men `start_url` ({startUrl}) ligger utenfor tjenestearbeiderens omfang ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON><PERSON> siden styres av en tjenestearbeider, men ingen `start_url` ble funnet fordi manifestet ikke ble hentet."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON>ne p<PERSON>en har én eller flere tjenestearbeidere, men siden ({pageUrl}) ligger utenfor omfanget."}, "core/audits/service-worker.js | failureTitle": {"message": "Registrerer ikke en tjenestearbeider som styrer siden og `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registrerer en tjenestearbeider som styrer siden og `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Tematiske splash-skjermer gjør at brukerne får en kvalitetsopplevelse når de starter appen fra startskjermen. [Finn ut mer om splash-skjermer](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Er ikke konfigurert med tilpasset splash-skjerm"}, "core/audits/splash-screen.js | title": {"message": "Konfigurert med tilpasset splash-skjerm"}, "core/audits/themed-omnibox.js | description": {"message": "Adressefeltet i nettleseren kan gis et tema for å stå i stil med nettstedet. [Finn ut mer om angivelse av tema i adressefeltet](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Angir ikke en temafarge for adressefeltet."}, "core/audits/themed-omnibox.js | title": {"message": "Ang<PERSON> en temafarge for adressefeltet."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (kundestøtte)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marked<PERSON>f<PERSON><PERSON>)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (sosiale medier)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkt"}, "core/audits/third-party-facades.js | description": {"message": "Innlastingen kan utsettes for enkelte tredjepartsinnbygginger. Vurder å erstatte dem med fasader frem til de trengs. [Finn ut hvordan du kan utsette tredjepartselementer med fasader](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# fasadealternativ er tilgjengelig}other{# fasadealternativer er tilgjengelige}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Innlasting av enkelte tredjepartsressurser kan utsettes med fasader"}, "core/audits/third-party-facades.js | title": {"message": "Utsett innlastingen av tredjepartsressurser med fasader"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Tredjepart"}, "core/audits/third-party-summary.js | description": {"message": "Tredjepartskode kan ha betydelig innvirkning på hvor lang tid det tar å laste inn siden. Begrens antallet overflødige tredjepartsleverandører, og prøv å laste inn tredjepartskode etter at siden for det meste er ferdig innlastet. [Finn ut hvordan du minimerer påvirkningen fra tredjepartskode](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Tredjepartskode blokkerte hovedtråden i {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reduser innvirkningen av tredjepartskode"}, "core/audits/third-party-summary.js | title": {"message": "Minimer bruken av tredjepartskode"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Verdi"}, "core/audits/timing-budget.js | description": {"message": "<PERSON><PERSON> et <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, slik at du kan holde øye med ytelsen på nettstedet. Nettsteder med høy ytelse lastes inn raskt og svarer kjapt på brukerinndatahendelser. [Finn ut mer om ytelsesbegrensninger](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Tidsbudsjett"}, "core/audits/unsized-images.js | description": {"message": "<PERSON><PERSON> bredden og høyden på bildeelementer eksplisitt for å redusere utseendeforskyvninger og oppnå bedre CLS. [Finn ut hvordan du angir bildedimensjoner](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Bildeelementer har ikke eksplisitte `width`- og `height`-attributter"}, "core/audits/unsized-images.js | title": {"message": "Bildeelementer har eksplisitte `width`- og `height`-attributter"}, "core/audits/user-timings.js | columnType": {"message": "Type"}, "core/audits/user-timings.js | description": {"message": "Vurder å instrumentere appen din med User Timing API for å måle appens reelle ytelse under viktige brukeropplevelser. [Finn ut mer om User Timing-merker](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{ 1 brukertiming}other{# brukertiminger}}"}, "core/audits/user-timings.js | title": {"message": "User <PERSON> – merker og intervaller"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Det ble funnet en `<link rel=preconnect>` for «{securityOrigin}», men denne ble ikke brukt av nettleseren. Kontroller at du bruker `crossorigin`-attributtet riktig."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Vurder å legge til `preconnect`- eller `dns-prefetch`-ressurshint for å opprette tilkoblinger til viktige tredjepartsopphav på et tidlig tidspunkt. [Finn ut hvordan du forhåndskobler til nødvendige opphav](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON><PERSON>t forhåndstilkobling til nødvendige domenenavn"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Fant mer enn to `<link rel=preconnect>`-tilkoblinger. Disse bør brukes sparsomt og kun til de viktigste plasseringene."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Det ble funnet en `<link rel=preconnect>` for «{securityOrigin}», men denne ble ikke brukt av nettleseren. Bruk `preconnect` kun for viktige plasseringer som det er helt sikkert at siden kommer til å forespørre."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Det ble funnet en `<link>` for forhåndslasting av «{preloadURL}», men denne ble ikke brukt av nettleseren. Kontroller at du bruker `crossorigin`-attributtet riktig."}, "core/audits/uses-rel-preload.js | description": {"message": "Vurder å bruke `<link rel=preload>` for å prioritere henting av ressurser som for øyeblikket blir forespurt senere i sideinnlastingen. [Finn ut hvordan du forhåndslaster viktige forespørsler](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Forhåndsinnlast (preload) nøkkelforespørsler"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Nettadresse til kildekart"}, "core/audits/valid-source-maps.js | description": {"message": "Kildekart oversetter minifisert kode til den opprinnelige kildekoden. Dette hjelper utviklere med feilsøking i produksjonsmiljøet. I tillegg kan Lighthouse gi ytterligere innsikt. Vurder å implementere kildekart for å dra nytte av disse fordelene. [Finn ut mer om kildekart](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Manglende kildekart for stor førsteparts JavaScript-kode"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "En stor JavaScript-fil mangler kildekart"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Advarsel: Mangler 1 element i `.sourcesContent`}other{Advarsel: Mangler # elementer i `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "<PERSON>n har gyldige kildekart"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">`-taggen optimaliserer ikke bare appen din for mob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, men forhin<PERSON><PERSON> og<PERSON> [at inndata fra brukerne blir forsinket i 300 millisekunder](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Finn ut mer om bruk av metataggen for synlig område](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Fant ingen `<meta name=\"viewport\">`-tag"}, "core/audits/viewport.js | failureTitle": {"message": "Har ingen `<meta name=\"viewport\">`-tag med `width` eller `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Har en `<meta name=\"viewport\">`-tag med `width` eller `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Dette er det trådblokkerende arbeidet som utføres under beregningen av Tid fra interaksjon til neste opptegning. [Finn ut mer om beregningen Tid fra interaksjon til neste opptegning](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms ble brukt på hendelsen «{interactionType}»"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Hendelsesmål"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimer mengden arbeid under viktige interaksjoner"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Inndataforsinkelse"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Presentasjonsforsinkelse"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Behandlingstid"}, "core/audits/work-during-interaction.js | title": {"message": "Minimerer mengden arbeid under viktige interaksjoner"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Dette er muligheter til å forbedre bruken av ARIA i programmet ditt, noe som kan gjøre opplevelsen bedre for brukere av assisterende teknologi, som skjermlesere."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Dette er muligheter til å tilby alternativt innhold for lyd og video. Dette kan gjøre opplevelsen bedre for brukere med nedsatt syn eller hørsel."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON> <PERSON> video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Disse punktene fremhever de vanligste anbefalte fremgangsmåtene for å sikre god tilgjengelighet."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Anbefalte fremgangsmåter"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Disse kontrollene fremhever muligheter til [å gjøre nettappen din mer tilgjengelig](https://developer.chrome.com/docs/lighthouse/accessibility/). Kun et utvalg av tilgjengelighetsproblemer kan oppdages automatisk, så du bør teste manuelt i tillegg."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Disse punktene tar for seg områder som ikke kan dekkes av automatiske testverktøy. Finn ut mer i veiledningen vår om [å utføre tilgjengelighetsgjennomganger](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Tilgjengelighet"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Dette er muligheter til å gjøre innholdet mer leselig."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Dette er muligheter til å gjøre innholdet lettere å tolke for brukere med forskjellige lokaliteter."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internasjonalisering og lokalisering"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Dette er muligheter til å gjøre kontrollene i programmet mer semantiske. Dette kan gjøre opplevelsen bedre for brukere av assisterende teknologi, som skjermlesere."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Navn og etiketter"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Dette er muligheter til å gjøre tastaturnavigeringen i programmet ditt bedre."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigering"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Dette er muligheter til å gi en bedre opplevelse når assisterende teknologi, som skjermlesere, brukes til å lese data i tabeller eller lister."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> og lister"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Nettleserkompatibilitet"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON> fremgan<PERSON>må<PERSON>"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Generelt"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Sikkerhet og pålitelighet"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Brukeropplevelse"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Ytelsesbudsjetter angir standarder for ytelsen på nettstedet."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budsjetter"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mer informasjon om ytelsen til appen din. Disse tallene har ingen [direkte innvirkning](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) på ytelsespoengsummen."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostikk"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Det mest kritiske aspektet for ytelse er hvor raskt piksler blir gjengitt på skjermen. Nøkkelberegninger: F<PERSON>rste innholdsrike opptegning, Første vesentlige opptegning"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Forbedringer av første opptegning"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Disse forslagene kan bidra til at siden lastes inn raskere. De har ingen [direkte innvirkning](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) på ytelsespoengsummen."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> den totale innlastingsopplevelsen bedre, slik at siden reagerer og er klar til bruk så snart som mulig. Nøkkelberegninger: Tid til interaktiv, Hastighetsindeks"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON>rings<PERSON>ligh<PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Resultater"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Disse kontrollene validerer ulike aspekter av progressive nettprogrammer. [Finn ut hva som gjør et progressivt nettprogram bra](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Disse kontrollene kreves av den grunnleggende [sjekklisten for progressive nettprogrammer](https://web.dev/pwa-checklist/), men kontrolleres ikke automatisk av Lighthouse. De påvirker ikke poengsummen din, men det er viktig at du verifiserer dem manuelt."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA (progressive web app – progressivt nettprogram)"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Kan <PERSON>eres"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA-optimalisert"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON>sse kontrollene sørger for at siden din følger grunnleggende råd for søkemotoroptimalisering. Det finnes mange tilleggsfaktorer som Lighthouse ikke evaluerer her, men som kan påvirke søkerangeringen din, inkludert ytelse på [Kjernestatistikk for nett](https://web.dev/learn-core-web-vitals/). [Finn ut mer om Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> disse tilleggsvalideringene på nettstedet ditt for å sjekke flere anbefalte fremgangsmåter for SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatér HTML-koden din på en måte som gjør det enklere for søkeroboter å forstå innholdet i appen."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Anbefalte fremgangsmåter for innhold"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Søkeroboter trenger tilgang til appen din for at den skal vises i søkeresultater."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Gjennomsøking og indeksering"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON> for at sidene dine er mobilvennlige, så brukere ikke trenger å klype eller zoome inn for å lese innholdssidene. [Finn ut hvordan du gjør sider mobilvennlige](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobilvennlig"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Den testede enheten ser ut til å ha tregere prosessor enn Lighthouse forventer. Dette kan virke negativt inn på ytelsespoengsummen din. Finn ut mer om [kalibrering av en hensiktsmessig multiplikator for prosessorforsinkelse](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON>n lastes kanskje ikke inn som forventet, fordi testnettadressen ({requested}) ble viderekoblet til {final}. Prøv å teste den andre nettadressen direkte."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "<PERSON>n ble lastet inn for tregt til å bli ferdig innenfor tidsgrensen. Resultatene kan være ufullstendige."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Tømmingen av nettleserens buffer ble tidsavbrutt. Prøv å gå gjennom denne siden på nytt, og rapportér en feil hvis problemet vedvarer."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Det kan være lagrede data som påvirker innlastingsytelsen, på denne plasseringen: {locations}. Utfør en revisjon på denne siden i et inkognitovindu for å forhindre disse ressursene fra å påvirke poengsummene.}other{Det kan være lagrede data som påvirker innlastingsytelsen, på disse plasseringene: {locations}. Utfør en revisjon på denne siden i et inkognitovindu for å forhindre disse ressursene fra å påvirke poengsummene.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Fjerning av de opprinnelige dataene ble tidsavbrutt. Prøv å gå gjennom denne siden på nytt, og rapportér en feil hvis problemet vedvarer."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Kun sider som lastes inn via GET-forespørsler, kan lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Kun sider med statuskode 2XX kan bufres."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome oppdaget et forsøk på kjøring av JavaScript mens siden var i bufferen."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Sider som bruker har forespurt <PERSON><PERSON><PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Frem-og-tilbake-bufferen er deaktivert via flagg. Gå til chrome://flags/#back-forward-cache for å aktivere den lokalt på denne enheten."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Frem-og-tilbake-bufferen er deaktivert via kommandolinjen."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av utilstrekkelig minne."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Frem-og-tilbake-bufferen støttes ikke av delegat."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Frem-og-tilbake-bufferen er deaktivert for forhåndsgjengivelse."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "<PERSON>n kan ikke buf<PERSON>, ettersom den har en BroadcastChannel-forekomst med registrerte lyttere."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Sider med hodet «cache-control:no-store» kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON><PERSON>n ble tømt med vilje."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "<PERSON>n ble fjernet fra bufferen, slik at en annen side kunne bli bufret."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Sider som inneholder programtillegg, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Sider som bruker FileChooser-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Sider som bruker File System Access-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Sider som bruker Media Device Dispatcher, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "En mediespiller holdt på å spille av da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Sider som bruker MediaSession-API-et og angir en avspillingsstatus, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Sider som bruker MediaSession-API-et og angir handlingsbehandlere, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av skjermleseren."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Sider som bruker <PERSON><PERSON>, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Sider som bruker Serial-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Sider som bruker WebAuthentication-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Sider som bruker WebBluetooth-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Sider som bruker WebUSB-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Sider som bruker en dedikert Service Worker eller worklet, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokumentet ble ikke ferdig innlastet før nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> var til stede da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome <PERSON> var til stede da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-destillering pågikk da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM-destilleringsvisningen var til stede da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi utvidelser bruker meldings-API-et."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Utvidelser med tilkoblinger med lang levetid må lukke tilkoblingen før de kan lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Utvidelser med tilkoblinger med lang levetid prøvde å sende meldinger til rammer i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av utvidelser."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "En modaldialogboks for siden, for eksempel en dialogboks om å sende inn et skjema på nytt eller en dialogboks for HTTP-passord, ble vist da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "<PERSON>n hvor det står at enheten er uten nett, ble vist da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Intervensjonsfeltet hvor det står at enheten ikke har nok minne, var til stede da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Det eksisterte tillatelsesforespørsler da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Blokkering av forgrunnsvinduer var til stede da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Safe Browsing-de<PERSON><PERSON> ble vist da nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing ans<PERSON> denne siden for å være villedende og blokkerte forgrunnsvinduet."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "En Service Worker ble aktivert mens siden var i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av en dokumentfeil."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Sider som bruker <PERSON>, kan ikke lagres i bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "<PERSON>n ble fjernet fra bufferen, slik at en annen side kunne bli bufret."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON>r som har gitt mediestr<PERSON>m<PERSON>g, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Sider som bruker portaler, er for øyeblikket ikke kvalifisert for frem-og-tilbake-hurtigbuffer."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Sider som bruker <PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Sider med åpne IndexedDB-tilkoblinger kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Det ble brukt API-er som ikke er kvalifisert."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Sider som utvidelser injiserer JavaScript i, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Sider som utvidelser har injisert StyleSheet i, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Intern feil."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av en keepalive-forespørsel."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON>r som bruker ta<PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | loading": {"message": "<PERSON>n ble ikke ferdig innlastet før nettleseren navigerte bort."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Sider som har «cache-control:no-cache» i hovedressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Sider som har «cache-control:no-store» i hovedressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigeringen ble avbrutt før siden kunne gjenopprettes fra frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Siden ble fjernet fra bufferen fordi en aktiv nettverkstilkobling mottok for mye data. Chrome begrenser hvor mye data sider kan motta mens de er bufret."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON>r med påg<PERSON>ende fetch()- eller XHR-foresp<PERSON><PERSON><PERSON> kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "<PERSON>n ble fjernet fra frem-og-tilbake-bufferen fordi en aktiv nettverksforespørsel medførte en viderekobling."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Siden ble fjernet fra bufferen fordi en nettverkstilkobling var åpen for lenge. Chrome begrenser hvor lenge sider kan motta data mens de er bufret."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Sider som ikke har noe gyldig svarhode, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigeringen skjedde i en annen ramme enn hovedrammen."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Sider med pågående IndexedDB-transaksjoner, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "<PERSON>r med pågående nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Sider med påg<PERSON>e fetch()-nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "<PERSON>r med pågående nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Sider med pågående XHR-nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Sider som bruker <PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Sider som bruker bilde-i-bilde, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | portal": {"message": "Sider som bruker portaler, er for øyeblikket ikke kvalifisert for frem-og-tilbake-hurtigbuffer."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON>r som viser UI for utskrift, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Siden ble åpnet med '`window.open()`', og en annen fane har en henvisning til den, eller siden åpnet et vindu."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Gjengivelsesprosessen for siden i frem-og-tilbake-bufferen krasjet."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Gjengivelsesprosessen for siden i frem-og-tilbake-bufferen ble avsluttet."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Sider som har bedt om tillatelser til lydopptak, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Sider som har bedt om sensortillate<PERSON>er, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Sider som har bedt om bakgrunnssynkronisering eller fetch()-till<PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON>r som har bedt om MIDI-till<PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Sider som har bedt om var<PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Sider som har bedt om tilgang til lagring, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Sider som har bedt om tillatelser til videoopptak, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Kun sider med HTTP eller HTTPS som nettadresseprotokoll kan bufres."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "<PERSON>n ble gjort krav på av en Service Worker mens den var i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "En Service Worker prøvde å sende en `MessageEvent` til siden i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "ServiceWorker ble avregistrert mens en side var i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "<PERSON>n ble fjernet fra frem-og-tilbake-bufferen på grunn av en Service Worker-aktivering."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome ble startet på nytt og slettet oppføringene i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Sider som bruker Shared<PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Sider som bruker SpeechRecogni<PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Sider som bruker SpeechSynthesis, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Et iframe-element på siden startet en navigering som ikke ble fullført."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Sider som har «cache-control:no-cache» i underressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Sider som har «cache-control:no-store» i underressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | timeout": {"message": "<PERSON>n overskred den maksimale tiden i frem-og-tilbake-bufferen og har utløpt."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Siden fikk tidsavbrudd under lagring i frem-og-tilbake-bufferen (sannsynligvis på grunn av pagehide-behandlere med lang kjøretid)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Siden har en utlastingsbehandler i hovedrammen."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Siden har en utlastingsbehandler i en underramme."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON> har endret hodet for overstyring av brukeragenten."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Sider som har gitt tilgang til opptak av video eller lyd, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Sider som bruker WebDatabase, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Sider som bruker WebHID, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Sider som bruker WebLocks, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Sider som bruker WebNfc, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Sider som bruker WebOTPService, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Sider med WebRTC kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Sider som bruker WebShare, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Sider med WebSocket kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Sider med WebTransport kan ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Sider som bruker WebXR, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Vurder å legge til nettadresseskjemaene https: og http: (ignoreres av nettlesere som støtter «strict-dynamic») for bakoverkompatibilitet med eldre nettlesere."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener er avviklet siden CSP3. Bruk hodet Cross-Origin-Opener-Policy i stedet."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer er avviklet siden CSP2. Bruk hodet Referrer-Policy i stedet."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss er avviklet siden CSP2. Bruk hodet X-XSS-Protection i stedet."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Når base-uri mangler, kan in<PERSON><PERSON><PERSON> <base>-tagger sette grunn-nettadressen for alle relative nettadresser (f.eks. skript) til et domene som en angriper kontrollerer. Vurder å sette base-uri til «none» eller «self»."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Manglende object-src muliggjør injisering av programtillegg som kjører utrygge skript. Vurder å sette object-src til «none» om du kan."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Direktivet script-src mangler. <PERSON>te kan gjøre det mulig å kjøre utrygge skript."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Har du glemt et semikolon? {keyword} ser ut til å være et direktiv, ikke et nøkkelord."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Engangsverdier må bruke base64-tegnsettet."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Engangsverdier må være på minst åtte tegn."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Unngå bruk av enkle nettadresseordninger ({keyword}) i dette direktivet. Med enkle nettadresseordninger kan skript ha utrygge domener som kilde."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Unngå bruk av enkle jokertegn ({keyword}) i dette direktivet. Med enkle jokertegn kan skript ha utrygge domener som kilde."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Rapporteringsdestinasjonen er bare konfigurert via direktivet report-to. Dette direktivet støttes kun i Chromium-baserte nettlesere, så det anbefales at du også bruker direktivet report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Det er ingen CSP som konfigurerer en rapporteringsdestinasjon. Dette gjør det vanskelig å vedlikeholde CSP-en over tid og følge med på om noe slutter å virke."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Godkjenningslister for verter kan ofte forbigås. Vurder å bruke CSP-engangsverdier eller -hasher i stedet, samt «strict-dynamic» om nødvendig."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Ukjent CSP-direktiv."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} ser ut til å være et ugyldig nøkkelord."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "«unsafe-inline» muli<PERSON><PERSON><PERSON><PERSON> kjø<PERSON> av utrygge skript og hendelsesbehandlere på siden. Vurder å bruke CSP-engangsverdier eller -hasher for å la skript kjøre individuelt."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Vurder å legge til «unsafe-inline» (ignoreres av nettlesere som støtter engangsverdier eller hasher) for bakoverkompatibilitet med eldre nettlesere."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Autorisasjon kommer ikke til å bli dekket av jokertegnet (*) ved håndtering av CORS-hodet `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Ressursforespørsler med nettadresser som inneholdt både fjernede mellomromstegn (`(n|r|t)`) og mindre-enn-tegn (`<`), er blokkert. For å kunne laste inn disse ressursene, fjern linjeskift og gjør mindre-enn-tegn om til kode på steder som i attributtverdiene til elementer."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` er avviklet. Bruk heller det standardiserte API-et Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` er avviklet. Bruk heller det standardiserte API-et Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` er avviklet. Bruk heller det standardiserte API-et `nextHopProtocol` i Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Informasjonskapsler som inneholder tegnet `(0|r|n)`, blir avvist i stedet for å bli avkortet."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Oppmykning av samme opphav-reglene ved å angi `document.domain` er avviklet og kommer til å være avslått som standard. Dette avviklingsvarselet gjelder en opphavsuavhengig tilgang som ble påslått ved å angi `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Utl<PERSON>sing av {PH1} fra «iframe»-elementer med andre opphav er avviklet og kommer til å bli fjernet i fremtiden."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "For å slå av Cast-integreringen som brukes som standard, bruk `disableRemotePlayback`-attributtet i stedet for `-internal-media-controls-overlay-cast-button`-velgeren."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} er avviklet. Bruk {PH2} i stedet."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Dette er et eksempel på en oversatt melding om et avviklingsproblem."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Oppmykning av samme opphav-reglene ved å angi `document.domain` er avviklet og kommer til å være avslått som standard. For å fortsette å bruke denne funksjonen, velg bort agentgrupper med opphavsnøkler ved å sende et `Origin-Agent-Cluster: ?0`-hode sammen med HTTP-svaret for dokumentet og rammene. Se https://developer.chrome.com/blog/immutable-document-domain/ for mer informasjon."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` er avviklet og kommer til å bli fjernet. Bruk `Event.composedPath()` i stedet."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT`-hodet er avviklet og kommer til å bli fjernet. Chrome krever sertifikatåpenhet for alle offentlig klarerte sertifikater som er utstedt etter 30. april 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Se statussiden for funksjonen for å få mer informasjon."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` og `watchPosition()` fungerer ikke på usikre opphav lenger. For å bruke denne funksjonen bør du vurdere å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` og `watchPosition()` er avviklet på usikre opphav. For å bruke denne funksjonen bør du vurdere å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` fungerer ikke på usikre opphav lenger. For å bruke denne funksjonen bør du vurdere å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` er avviklet. Bruk `RTCPeerConnectionIceErrorEvent.address` eller `RTCPeerConnectionIceErrorEvent.port` i stedet."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Selgerens opprinnelse og vilkårlige data fra Service Worker-hendelsen `canmakepayment` er avviklet og blir fjernet: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Nettstedet forespurte en underressurs fra et nettverk det kun hadde tilgang til på grunn av den privilegerte nettverksposisjonen til brukerne sine. Disse forespørslene eksponerer ikke-offentlige enheter og tjenere for internett, noe som øker risikoen for angrep via forfalskning av tredjepartsforespørsler (CSRF) og/eller informasjonslekkasjer. For å forebygge disse risikoene avvikler Chrome forespørsler til ikke-offentlige underressurser når disse igangsettes fra ikke-sikre kontekster, og kommer til å begynne å blokkere dem."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS-filer kan ikke lastes inn fra `file:`-nettadresser med mindre de har filetternavnet `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Bruk av `SourceBuffer.abort()` for å avbryte den asynkrone områdefjerningen som utføres av `remove()`, er avviklet på grunn av endringer i spesifikasjonen. Støtten kommer til å bli fjernet i fremtiden. Du bør lytte etter `updateend`-hendelsen i stedet. `abort()` er kun ment å avbryte asynkrone medietilføyninger eller tilbakestille parsertilstanden."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Å angi en verdi for `MediaSource.duration` som er lavere enn det høyeste presentasjonstidsstempelet for noen bufret, kodet ramme, er avviklet på grunn av endringer i spesifikasjonen. St<PERSON>tte for implisitt fjerning av avkortede, bufrede medier kommer til å bli fjernet i fremtiden. Du bør i stedet utføre eksplisitte `remove(newDuration, oldDuration)`-kall på alle `sourceBuffers`, hvor `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "<PERSON>ne endringen trer i kraft med milepæl {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI kommer til å be om brukstillatelse selv om sysex ikke er angitt i `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Notification API kan ikke lenger brukes fra usikre opphav. Vurder å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Det er ikke lenger mulig å be om tillatelse for Notification API via «iframe»-elementer med andre opphav. Vurder å be om tillatelse via en toppnivåramme eller åpne et nytt vindu i stedet."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Partneren din bruker en foreldet (D)TLS-versjon. Snakk med partneren din for å få rettet dette."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL i usikre kontekster er avviklet og blir snart fjernet. Bruk nettlagring eller indeksert database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON> du angir `overflow: visible` på img-, video- eller canvas-tagger, kan det hende at de produserer visuelt innhold utenfor elementgrensene. Se https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` er avviklet. Bruk siste liten-installasjon for betalingsbehandlere i stedet."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest`-kallet ditt omgikk Content-Security-Policy (CSP)-direktivet `connect-src`. Denne omgåelsen er avviklet. Legg til identifikatoren for betalingsmåten fra `PaymentRequest`-API-et (i `supportedMethods`-feltet) i CSP-direktivet `connect-src`."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` er avviklet. Bruk standardisert `navigator.storage` i stedet."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` med `<picture>` som overordnet element er ugyldig og blir derfor ignorert. Bruk `<source srcset>` i stedet."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` er avviklet. Bruk standardisert `navigator.storage` i stedet."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Underressursforespørsler med nettadresser som inneholder innbygd legitimasjon (f.eks. `**********************/`), er blokkert."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Begrensningen `DtlsSrtpKeyAgreement` er fjernet. Du har angitt verdien `false` for denne begren<PERSON>, noe som tolkes som et forsøk på å bruke den fjernede `SDES key negotiation`-metoden. Denne funksjonaliteten er fjernet. Bruk en tjeneste som støtter `DTLS key negotiation` i stedet."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Begrensningen `DtlsSrtpKeyAgreement` er fjernet. Du har angitt verdien `true` for denne begrensningen. Det<PERSON> har ingen virkning, men du kan fjerne denne begrensningen for ryddighets skyld."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Oppdaget bruk av `Complex Plan B SDP`. Denne dialekten av `Session Description Protocol` støttes ikke lenger. Bruk `Unified Plan SDP` i stedet."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, som brukes ved konstruering av `RTCPeerConnection` med `{sdpSemantics:plan-b}`, er en eldre ikke-standardversjon av `Session Description Protocol` som er permanent slettet fra Web Platform. Den er fremdeles tilgjengelig ved bygging med `IS_FUCHSIA`, men vi planlegger å slette den så snart som mulig. Slutt å være avhengig av den. Se https://crbug.com/1302249 for status."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy`-alternativet er avviklet og kommer til å bli fjernet."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` kommer til å kreve isolering fra andre opphav. Se https://developer.chrome.com/blog/enabling-shared-array-buffer/ for mer informasjon."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` uten brukeraktivering er avviklet og kommer til å bli fjernet."}, "core/lib/deprecations-strings.js | title": {"message": "En avviklet funksjon ble brukt"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Utvidelser må slå på isolering fra andre opphav for å fortsette å bruke `SharedArrayBuffer`. Se https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "Metoden {PH1} er leverandørspesifikk. Bruk standardmetoden {PH2} i stedet."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 støttes ikke av JSON-svaret i `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synkrone `XMLHttpRequest`-fore<PERSON><PERSON><PERSON><PERSON> på hovedtråden er avviklet på grunn av de negative virkningene på sluttbrukerens opplevelse. For å få mer hjelp, se https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` er avviklet. Bruk `isSessionSupported()` og sjekk den resulterende boolske verdien i stedet."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Blokkeringstid i hovedtråden"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Buffer-TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Beskrivelse"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementer som ikke besto kontrollen"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Posisjon"}, "core/lib/i18n/i18n.js | columnName": {"message": "Navn"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Over budsjett"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Ressursstørrelse"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Ressurstype"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Starttid"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON> tid"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Overføringsstørrelse"}, "core/lib/i18n/i18n.js | columnURL": {"message": "Nettadresse"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>arelser"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>arelser"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potensielle besparelser på {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element funnet}other{# elementer funnet}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potensielle besparelser på {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Første vesentlige opptegning"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Skrifttype"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Tid fra interaksjon til neste opptegning"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Hø<PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Lav"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Middels"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "<PERSON><PERSON> for første inndata"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Medier"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stilark"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Tredjepart"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Totalt"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Noe gikk galt med sporingsregistreringen for sideinnlastingen. Kjør Lighthouse på nytt. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Tidsavbrudd under venting på første tilkobling fra feilsøkingsprogramprotokollen."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome samlet ingen skjermdumper under innlasting av siden. <PERSON><PERSON><PERSON> for at det finnes synlig innhold på siden, og prøv deretter å kjøre Lighthouse på nytt. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS-tje<PERSON>ne kunne ikke oversette domenet du oppga."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Den obligatoriske {artifactName}-sa<PERSON><PERSON> støtte på en feil: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Det oppsto en intern Chrome-feil. Start Chrome på nytt, og prøv å kjøre Lighthouse igjen."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Den obligatoriske {artifactName}-sa<PERSON><PERSON> ble ikke kjørt."}, "core/lib/lh-error.js | noFcp": {"message": "Siden tegnet ikke noe innhold. Pass på å holde nettleservinduet i forgrunnen under innlasting, og prøv på nytt. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Siden viste ikke innhold som kvalifiserer som Største innholdsrike opptegning (LCP). S<PERSON>rg for at siden har et gyldig LCP-element, og prøv deretter på nytt. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "<PERSON><PERSON>, er ikke HTML (sendes med MIME-typen {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Denne versjonen av Chrome er for gammel til å støtte «{featureName}». Bruk en nyere versjon for å se de fullstendige resultatene."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse kunne ikke laste inn den forespurte siden på en pålitelig måte. Sjekk at du tester riktig nettadresse, og at tjeneren svarer ordentlig på alle forespørsler."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse kunne ikke laste inn den forespurte nettadressen på en pålitelig måte, fordi siden sluttet å svare."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Nettadressen du oppga, har ikke noe gyldig sikkerhetssertifikat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome forhindret siden fra å lastes inn og viste en interstitial-skjerm i stedet. Sjekk at du tester riktig nettadresse, og at tjeneren svarer ordentlig på alle forespørsler."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse kunne ikke laste inn den forespurte siden på en pålitelig måte. Sjekk at du tester riktig nettadresse, og at tjeneren svarer ordentlig på alle forespørsler. (Detaljer: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse kunne ikke laste inn den forespurte siden på en pålitelig måte. Sjekk at du tester riktig nettadresse, og at tjeneren svarer ordentlig på alle forespørsler. (Statuskode: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Det tok for lang tid å laste inn siden. Følg tipsene i rapporten for å redusere sideinnlastingstiden, og prøv deretter å kjøre Lighthouse på nytt. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Venting på DevTools-protokollsvar har overskredet den kvoterte tiden. (Metode: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Henting av ressursinnhold har overskredet den kvoterte tiden"}, "core/lib/lh-error.js | urlInvalid": {"message": "Nettadressen du oppga, ser ut til å være ugyldig."}, "core/lib/navigation-error.js | warningXhtml": {"message": "MIME-typen for siden er XHTML: Lighthouse støtter ikke denne dokumenttypen eksplisitt"}, "core/user-flow.js | defaultFlowName": {"message": "Brukerflyt ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Navigasjonsrapport ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Øyeblikksbilderapport ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Tidsspennrapport ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Alle rapporter"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Tilgjengelighet"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "<PERSON><PERSON> fremgan<PERSON>må<PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Resultater"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressivt nettprogram"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Datamaskin"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Forstå Lighthouse-flytrapporten"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Forstå flyter"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Bruk navigasjonsrapporter for å …"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Bruk oversiktsrapporter for å …"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Bruk tidsromrapporter for å …"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "skaffe en Lighthouse-ytelsespoengsum"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "måle ytelsesverdier knyttet til sideinnlasting, for eksempel Største innholdsrike opptegning (LCP) og Hastighetsindeks"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "vurdere egenskapene til progressive nettprogrammer"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "finne tilgjengelighetsproblemer i enkeltsideapper (SPA-er) og komplekse skjemaer"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "evaluere anbefalte fremgangsmåter for menyer og elementer i brukergrensesnittet som er skjult bak interaksjon"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "måle utseendeforskyvninger og JavaScript-kjøretid for en interaksjonsserie"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "oppdage muligheter til å oppnå bedre ytelse og brukeropplevelse for sider og enkeltsideapper (SPA-er) med lang levetid"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Høyest effekt"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativ revisjon}other{{numInformative} informative revisjoner}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Sideinnlasting"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Navigasjonsrapporter analyserer en enkelt sideinnlasting, akkurat som de opprinnelige Lighthouse-rapportene."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Navigasjonsrapport"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navigasjonsrapport}other{{numNavigation} navigasjonsrapporter}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} revisjon som kan bestås}other{{numPassableAudits} revisjoner som kan bestås}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} revisjon er bestått}other{{numPassed} revisjoner er bestått}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Gjennomsnitt"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "God"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Lagre"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Registrert sidetilstand"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Oversiktsrapporter analyserer siden i bestemte tilstander, van<PERSON><PERSON><PERSON> etter brukerinteraksjoner."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Øyeblikksbilde-rapport"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} øyeblikksbilderapport}other{{numSnapshot} øyeblikksbilderapporter}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Brukerinteraksjoner"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Tidsromrapporter analyserer en vilkårlig tidsperiode, vanligvis med brukerinteraksjoner."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Tidsspennrapport"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} tidsspennrapport}other{{numTimespan} tidsspennrapporter}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse-rapport over bruke<PERSON><PERSON><PERSON>"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "For animert innhold, bruk [`amp-anim`‏](https://amp.dev/documentation/components/amp-anim/) for å minimere prosessorbruken når innholdet er utenfor skjermen."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "V<PERSON>der å vise alle [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-komponenter i WebP-formater og spesifisere passende reservealternativer for andre nettlesere. [Finn ut mer](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Pass på å bruke [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) for bilder som skal lastes inn med utsettelse automatisk. [Finn ut mer](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Bruk verktøy som [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) for å [gjengi AMP-layouter på tje<PERSON>iden](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Se [AMP-dokumentasjonen](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) for å forsikre deg om at alle stilene støttes."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-komponenten støtter [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/)-attributtet, som gjør det mulig å spesifisere hvilke bilderessurser som skal brukes, basert på skjermstørrelsen. [Finn ut mer](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "<PERSON>urder å bruke virtuell rulling med Component Dev Kit (CDK) hvis du skal gjengi svært store lister. [<PERSON> ut mer](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Bruk [kodeoppdeling på rutenivå](https://web.dev/route-level-code-splitting-in-angular/) for å minimere størrelsen på JavaScript-pakkene. Vurder også å forhåndsbufre ressurser med [Angular service worker](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON> du bruker Angular CLI, må du passe på at delversjonene genereres i produksjonsmodus. [Finn ut mer](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Hvis du bruker Angular CLI, kan du inkludere kildekodekart i produksjonsdelversjonen for å inspisere pakkene dine. [<PERSON> ut mer](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Last inn ruter på forhånd for å gjøre navigasjonen raskere. [Finn ut mer](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Vurder å bruke `BreakpointObserver`-verktøyet i Component Dev Kit (CDK) for å administrere stoppunkter for bilder. [<PERSON> ut mer](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Vurder å laste opp GIF-filen til en tjeneste som gjør det mulig å bygge den inn som en HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "<PERSON>i `@font-display` når du definerer egendefinerte skrifttyper i temaet ditt."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Vurder å konfigurere [WebP-bildeformater med bildestilen «Convert» (konverter)](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) på nettstedet ditt."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Installer [en Drupal-modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) som kan utsette innlastingen av bilder. Slike moduler gir mulighet til å utsette innlasting av bilder som ikke er på skjermen, slik at ytelsen økes."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Vurder å bruke en modul for å bygge inn kritisk CSS- og JavaScript-kode eller potensielt laste inn ressurser asynkront via JavaScript, som for eksempel modulen [avansert CSS-/JS-aggregering](https://www.drupal.org/project/advagg). <PERSON><PERSON><PERSON> obs på at optimalisering som gjøres av denne modulen, kan gjøre at nettstedet ditt slutter å fungere, så du må sannsynligvis gjøre kodeendringer."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, moduler og tjenerspesifikasjoner virker inn på tjenerens responstid. Vurder å finne et mer optimalisert tema, velge en optimaliseringsmodul nøye og/eller oppgradere tjeneren. Vertstjenerne dine bør benytte seg av bufring av PHP-operasjonskoder, minnebufring som Redis eller Memcached for å redusere tiden databasespørringer tar, samt optimalisert applikasjonslogikk for å klargjøre sider raskere."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Vurder å bruke [responsive bildestiler](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) for å redusere størrelsen på bilder som lastes inn på siden. Hvis du bruker Views til å vise flere innholdselementer på siden, bør du vurdere å implementere sideinndeling for å begrense antallet innholdselementer som vises på en gitt side."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Forsikre deg om at du har slått på «Aggreger CSS-filer» på siden «Administrasjon > Konfigurasjon > Utvikling». Du kan også konfigurere mer avanserte aggregeringsalternativer gjennom [tilleggsmoduler](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) for å gjøre nettstedet ditt raskere ved å spleise, minifisere og komprimere CSS-stilene dine."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Forsikre deg om at du har slått på «Aggreger JavaScript-filer» på siden «Administrasjon > Konfigurasjon > Utvikling». Du kan også konfigurere mer avanserte aggregeringsalternativer gjennom [tilleggsmoduler](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) for å gjøre nettstedet ditt raskere ved å spleise, minifisere og komprimere JavaScript-ressursene dine."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Vurder å fjerne ubrukte CSS-regler og kun legge ved de nødvendige Drupal-bibliotekene på den relevante siden eller komponenten på siden. Se [linken til Drupal-dokumentasjonen](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) for mer informasjon. For å identifisere vedlagte bibliotek som legger til overflødig CSS, prøv å kjøre [kodedekning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan finne temaet eller modulen som er ansvarlig, i nettadressen til stilarket når CSS-aggregering er avslått på Drupal-nettstedet ditt. Se opp for temaer og moduler som har mange stilark på listen, og som viser mye rødt i kodedekningen. Temaer og moduler bør bare legge stilark i kø hvis disse faktisk brukes på siden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Vurder å fjerne ubrukte JavaScript-ressurser og kun legge ved de nødvendige Drupal-bibliotekene på den relevante siden eller komponenten på siden. Se [linken til Drupal-dokumentasjonen](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) for mer informasjon. For å identifisere vedlagte bibliotek som legger til overflødig JavaScript, prøv å kjøre [kodedekning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan finne temaet eller modulen som er ansvarlig, i nettadressen til skriptet når JavaScript-aggregering er avslått på Drupal-nettstedet ditt. Se opp for temaer og moduler som har mange skript på listen, og som viser mye rødt i kodedekningen. Temaer og moduler bør bare legge skript i kø hvis disse faktisk brukes på siden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON> «<PERSON><PERSON><PERSON><PERSON><PERSON>er for nettleser- og proxy-tjenerbuffer» på <PERSON>n «Administrasjon > Konfigurasjon > Utvikling». Les om [Drupal-bufferen og optimalisering for ytelse](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Vurder å bruke [en modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) som automatisk optimaliserer og reduserer størrelsen på bilder som lastes opp via nettstedet, uten at det går ut over kvaliteten. Pass også på at du bruker de innebygde [responsive bildestilene](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) i Drupal (tilgjengelig i Drupal 8 og nyere) for alle bilder som gjengis på nettstedet."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Ressurshintene preconnect og dns-prefetch kan legges til ved å installere og konfigurere [en modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) som inneholder funksjonalitet for å gi brukeragenten ressurshint."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Pass på at du bruker de innebygde [responsive bildestilene](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) i Drupal (tilgjengelig i Drupal 8 og nyere). Bruk de responsive bildestilene ved gjengivelse av bildefelt gjennom visningsmoduser, visninger eller bilder som lastes opp gjennom WYSIWYG-redigeringsverktøyet."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Bruk [E<PERSON> Leap](https://pubdash.ezoic.com/speed) og slå på `Optimize Fonts` for å bruke CSS-funksjonen `font-display` automatisk. <PERSON><PERSON> sikrer at tekst er synlig for brukere mens skrifttyper for nettet lastes inn."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Next-Gen Formats` for å konvertere bilder til WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Lazy Load Images` for å utsette innlasting av bilder som ikke er på skjermen, frem til de trengs."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Critical CSS` og `Script Delay` for å utsette ikke-kritisk JS/CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Bruk [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) for å bufre innholdet ditt på det verdensomspennende nettverket vårt, slik at ventetiden frem til første byte blir kortere."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Minify CSS` for å minifisere CSS-koden din automatisk, slik at størrelsen på nyttelastene som sendes over nettverket, reduseres."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Bruk [E<PERSON> Leap](https://pubdash.ezoic.com/speed) og slå på `Minify Javascript` for å minifisere JS-koden din automatisk, slik at størrelsen på nyttelastene som sendes over nettverket, reduseres."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Remove Unused CSS` for å avbøte dette problemet. Dette identifiserer CSS-klassene som faktisk brukes på de ulike sidene på nettstedet ditt, og fjerner eventuelle andre, slik at filstørrelsen holdes lav."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Efficient Static Cache Policy` for å angi anbefalte verdier i bufringshodet for statiske ressurser."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Next-Gen Formats` for å konvertere bilder til WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Pre-Connect Origins` for å legge til `preconnect`-ressurshint automatisk, slik at du tidlig kan opprette tilkoblinger til viktige tredjepartsopphav."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Preload Fonts` og `Preload Background Images` for å legge til `preload`-linker, slik at du kan prioritere henting av ressurser som ellers hadde blitt forespurt senere i sideinnlastingen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Bruk [Ezoic Leap](https://pubdash.ezoic.com/speed) og slå på `Resize Images` for å endre bilder til en passelig størrelse for mottakerenheten, slik at størrelsen på nyttelastene som sendes over nettverket, reduseres."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Vurder å laste opp GIF-filen til en tjeneste som gjør det mulig å bygge den inn som en HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Vurder å bruke et [programtillegg](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) eller en tjeneste som automatisk konverterer opplastede bilder til de optimale formatene."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Installer et [Joomla-programtillegg for utsatt innlasting](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) som gjør det mulig å utsette innlasting av bilder som ikke er på skjermen, el<PERSON> bytt til en mal som gir denne funksjonaliteten. Fra og med Joomla 4.0 får alle nye bilder [automatisk](https://github.com/joomla/joomla-cms/pull/30748) `loading`-attributtet fra kjernen."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Det finnes en rekke Joomla-programtillegg som kan hjelpe deg med å [bygge inn kritiske elementer direkte på siden](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) eller [utsette innlasting av mindre viktige ressurser](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Vær obs på at optimalisering som gjøres av disse programtilleggene, kan ødelegge funksjonalitet i malene eller programtilleggene dine, så du må teste disse grundig."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, utvidelser og tjenerspesifikasjoner virker inn på tjenerens responstid. Vurder å finne en mer optimalisert mal, velge en utvidelse for optimalisering nøye og/eller oppgradere tjeneren."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Vurder å vise utdrag i artikkelkategoriene (f.eks. via les mer-linken), redusere antall artikler som vises på en gitt side, fordele lange innlegg på flere sider eller bruke et programtillegg for utsatt innlasting av kommentarer."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "En rekke [<PERSON><PERSON><PERSON><PERSON>ut<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) kan gjøre nettstedet ditt raskere ved å spleise, minifisere og komprimere CSS-stilene dine. Det finnes også maler som tilbyr denne funksjonaliteten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Det finnes en rekke [Jo<PERSON><PERSON>-ut<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) som kan gjøre nettstedet ditt raskere ved å spleise, minifisere og komprimere skriptene dine. Det finnes også maler som tilbyr denne funksjonaliteten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Vurder å redusere eller endre antall [Joomla-utvidelser](https://extensions.joomla.org/) som laster inn ubrukt CSS på siden. For å identifisere utvidelser som legger til overflødig CSS, prøv å kjøre [k<PERSON><PERSON><PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan finne det ansvarlige temaet eller programtillegget i nettadressen til stilarket. Se opp for programtillegg som har mange stilark på listen, og som viser mye rødt i kodedekningen. Programtillegg bør bare legge stilark i kø hvis de faktisk brukes på siden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Vurder å redusere eller endre antall [Joomla-utvidelser](https://extensions.joomla.org/) som laster inn ubrukt JavaScript på siden. For å identifisere utvidelser som legger til overflødig JavaScript, prøv å kjøre [kode<PERSON><PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan finne den ansvarlige utvidelsen i nettadressen til skriptet. Se opp for utvidelser som har mange skript på listen, og som viser mye rødt i kodedekningen. Utvidelser bør bare legge skript i kø hvis de faktisk brukes på siden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Les om [nettleserbuf<PERSON> i Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Vurder å bruke et [programtillegg for bildeoptimalisering](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) som komprimerer bildene uten at det går ut over kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Vurder å bruke et [programtillegg for responsive bilder](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) for å bruke responsive bilder i innholdet ditt."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Du kan slå på tekstkomprimering ved å slå på Gzip-sidekomprimering i Joomla (System > Global konfigurasjon > Tjener)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "<PERSON><PERSON> du ikke pakker JavaScript-ress<PERSON><PERSON> dine, bør du vurdere å bruke [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Slå av Magentos innebygde [JavaScript-pakking og -forminskning](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), og vurder å bruke [baler](https://github.com/magento/baler/) i stedet."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Spesifiser `@font-display` n<PERSON>r du [definerer tilpassede skrifttyper](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Vurder å søke på [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) etter diverse tredjepartsutvidelser som gjør det mulig å bruke nyere bildeformater."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Vurder å endre produkt- og katalogmalene for å nyttiggjøre deg av funksjonaliteten for [utsatt innlasting](https://web.dev/native-lazy-loading) på nettplattformen."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Bruk Magentos [Varnish-integrasjon](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Slå på alternativet «Forminsk CSS-filer» i Utvikler-innstillingene for butikken. [Finn ut mer](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Bruk [Terser](https://www.npmjs.com/package/terser) til å minifisere alle JavaScript-ressurser fra implementering av statisk innhold, og slå av den innebygde minifiseringsfunksjonen."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Slå av Magentos innebygde [JavaScript-pakking](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Vurder å søke på [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) etter diverse tredjepartsutvidelser for å optimalisere bilder."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Ressurshintene preconnect og dns-prefetch kan legges til ved å [endre temalayouten](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>`-tagger kan legges til ved å [endre temalayouten](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Bruk `next/image`-komponenten i stedet for `<img>` for å optimalisere bildeformatet automatisk. [Finn ut mer](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Bruk `next/image`-komponenten i stedet for `<img>` for å bruke utsatt innlasting av bilder automatisk. [Finn ut mer](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Bruk `next/image`-komponenten og sett «priority» til «true» (sann) for å forhåndslaste LCP-bilder. [Finn ut mer](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Bruk `next/script`-komponenten til å utsette innlasting av ikke-kritiske tredjepartsskript. [Finn ut mer](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Bruk `next/image`-komponenten for å sørge for at bilder alltid har rett størrelse. [<PERSON> ut mer](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Vurder å konfigurere `PurgeCSS` i `Next.js`-konfigurasjonen for å fjerne ubrukte regler fra stilark. [Finn ut mer](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Bruk `Webpack Bundle Analyzer` for å oppdage ubrukt JavaScript-kode. [Finn ut mer](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Vurder å bruke `Next.js Analytics` for å måle appens ytelse i reelle situasjoner. [<PERSON> ut mer](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> bufring for uforanderlige ressurser og `Server-side Rendered` (SSR)-sider. [<PERSON> ut mer](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Bruk `next/image`-komponenten i stedet for `<img>` for å justere bildekvaliteten. [Finn ut mer](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Bruk `next/image`-komponenten for å angi riktig verdi for `sizes`. [<PERSON> ut mer](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Slå på komprimering på Next.js-tjeneren din. [<PERSON> ut mer](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Bruk `nuxt/image`-komponenten og angi `format=\"webp\"`. [<PERSON> ut mer](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Bruk `nuxt/image`-komponenten og angi `loading=\"lazy\"` for bilder som ikke er på skjermen. [Finn ut mer](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Bruk `nuxt/image`-komponenten og angi `preload` for LCP-bildet. [Finn ut mer](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Bruk `nuxt/image`-komponenten og angi attributtene `width` og `height` eksplisitt. [<PERSON> ut mer](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Bruk `nuxt/image`-komponenten og angi riktig verdi for `quality`. [<PERSON> ut mer](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Bruk `nuxt/image`-komponenten og angi riktig verdi for `sizes`. [<PERSON> ut mer](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Erstatt animerte GIF-er med video](https://web.dev/replace-gifs-with-videos/) for raskere nettsideinnlasteringer, og vurder å bruke moderne filformater, som [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) eller [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), for å øke komprimeringseffektiviteten med mer enn 30 % sammenlignet med den mest avanserte videokodeken, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Vurder å bruke et [programtillegg](https://octobercms.com/plugins?search=image) eller en tjeneste som automatisk konverterer opplastede bilder til de optimale formatene. [Tapsfrie WebP-bilder](https://developers.google.com/speed/webp) er 26 % mindre enn PNG-bilder og 25–34 % mindre enn sammenlignbare JPEG-bilder med tilsvarende SSIM-kvalitetsindeks. Et annet nestegenerasjons bildeformat du kan vurdere, er [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Vurder å installere et [programtillegg for utsatt innlasting av bilder](https://octobercms.com/plugins?search=lazy) som gjør det mulig å utsette innlasting av bilder som ikke er på skjermen, eller bytt til et tema som gir denne funksjonaliteten. Vurder også å bruke [AMP-programtillegget](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Det er mange programtillegg som bidrar til å [bygge inn kritiske elementer](https://octobercms.com/plugins?search=css). Disse programtilleggene kan gjøre at andre programtillegg slutter å fungere, så du bør teste dem grundig."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> te<PERSON>, programtillegg og tjenerspesifikasjoner virker inn på tjenerens responstid. Vurder å finne et mer optimalisert tema, velge et programtillegg for optimalisering nøye og/eller oppgradere tjeneren. Med October CMS kan utviklere også bruke [`Queues`](https://octobercms.com/docs/services/queues) for å utsette behandlingen av tidkrevende oppgaver, som å sende e-poster. Dette gjør nettforespørsler betydelig raskere."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Vurder å vise utdrag i innleggslistene (f.eks. ved bruk av en `show more`-knapp), redusere antall innlegg som vises på en gitt nettside, fordele lange innlegg på flere nettsider eller bruke et programtillegg for utsatt innlasting av kommentarer."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Det finnes mange [programtillegg](https://octobercms.com/plugins?search=css) som kan gjøre nettsteder raskere ved å spleise, minifisere og komprimere stilene. Utviklingen kan gå raskere hvis du bruker en byggeprosess til å gjøre denne minifiseringen på forhånd."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Det finnes mange [programtillegg](https://octobercms.com/plugins?search=javascript) som kan gjøre nettsteder raskere ved å spleise, minifisere og komprimere skriptene. Utviklingen kan gå raskere hvis du bruker en byggeprosess til å gjøre denne minifiseringen på forhånd."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Vurder å gå gjennom [programtilleggene](https://octobercms.com/plugins) som laster inn ubrukte gjennomgripende stilark (CSS) på nettstedet. For å identifisere programtillegg som legger til unødvendige gjennomgripende stilark (CSS), k<PERSON><PERSON><PERSON> [kodedek<PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Finn det ansvarlige temaet eller programtillegget i nettadressen til stilarket. Se etter programtillegg med mange stilark som viser mye rødt i kodedekningen. Programtillegg bør bare legge til stilark hvis de faktisk brukes på nettsiden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Vurder å gå gjennom [programtilleggene](https://octobercms.com/plugins?search=javascript) som laster inn ubrukt JavaScript-kode på nettsiden. For å identifisere programtillegg som legger til unødvendig JavaScript-kode, kjør [kodedekning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Finn det ansvarlige temaet eller programtillegget i nettadressen til skriptet. Se etter programtillegg med mange skript som viser mye rødt i kodedekningen. Programtillegg bør bare legge til skript hvis de faktisk brukes på nettsiden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Les om [forebygging av unødvendige nettverksforespørsler med HTTP-bufferen](https://web.dev/http-cache/#caching-checklist). Det finnes mange [programtillegg](https://octobercms.com/plugins?search=Caching) som kan brukes til å gjøre bufringen raskere."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Vurder å bruke et [programtillegg for bildeoptimalisering](https://octobercms.com/plugins?search=image) for å komprimere bilder uten at det går ut over kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Last opp bilder direkte i mediebehandling for å sikre at de nødvendige bildestørrelsene er tilgjengelige. Vurder å bruke [filteret for størrelsesendring](https://octobercms.com/docs/markup/filter-resize) eller et [programtillegg for endring av bildestørrelser](https://octobercms.com/plugins?search=image) for å sikre at optimale bildestørrelser brukes."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Slå på tekstkomprimering i konfigurasjonen av nettjeneren."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Vurder å bruke et bibliotek for «vinduer», som `react-window`, for å minimere antallet DOM-noder som opprettes hvis du gjengir mange gjentatte elementer på siden. [<PERSON> ut mer](https://web.dev/virtualize-long-lists-react-window/). Du kan også minimere antallet gjengivelser som gjentas unødvendig, ved å bruke [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) eller [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) og [hoppe over effekter](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) kun frem til visse avhengigheter har endret seg, hvis du bruker `Effect`-hooken for å oppnå bedre ytelse under k<PERSON><PERSON><PERSON>."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Hvis du bruker React Router, kan du minimere bruken av `<Redirect>`-komponenten for [rutenavigasjoner](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "<PERSON><PERSON> du gjengir React-komponenter på tjenersiden, bør du vurdere å bruke `renderToPipeableStream()` eller `renderToStaticNodeStream()` for å gi klienten mulighet til å motta og hydrere ulike deler av oppmerkingen i stedet for å gjøre alt på en gang. [Finn ut mer](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "<PERSON><PERSON> kodebyggingssystemet minifiserer CSS-filer automatisk, må du passe på at du implementerer produksjonsdelversjonen av appen. Dette kan du sjekke med React Developer Tools-utvidelsen. [Finn ut mer](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "<PERSON><PERSON> kodebyggingssystemet minifiserer JS-filer automatisk, må du passe på at du implementerer produksjonsdelversjonen av appen. Dette kan du sjekke med React Developer Tools-utvidelsen. [Finn ut mer](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON><PERSON> du ikke gjeng<PERSON> på tje<PERSON>iden, kan du [dele opp JavaScript-pakkene](https://web.dev/code-splitting-suspense/) med `React.lazy()`. Ellers kan du dele opp koden med et tredjepartsbibliotek som [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Bruk React DevTools Profiler, som benytter Profiler-API-et, til å måle gjengivelsesytelsen for komponentene. [Finn ut mer.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Vurder å laste opp GIF-filen til en tjeneste som gjør det mulig å bygge den inn som en HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> å bruke [Performance Lab](https://wordpress.org/plugins/performance-lab/)-programtillegget for å konvertere JPEG-bilder du laster opp, til WebP automatisk, der det støttes."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Installer et [WordPress-programtillegg for utsatt innlasting](https://wordpress.org/plugins/search/lazy+load/) som gjør det mulig å utsette innlasting av bilder som ikke er på skjermen, eller bytt til et tema som gir denne funksjonaliteten. Vurder også å bruke [AMP-programtillegget](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Det finnes en rekke WordPress-programtillegg som kan hjelpe deg med å [bygge inn kritiske elementer direkte på siden](https://wordpress.org/plugins/search/critical+css/) el<PERSON> [utsette innlasting av mindre viktige ressurser](https://wordpress.org/plugins/search/defer+css+javascript/). V<PERSON>r obs på at optimalisering som gjøres av disse programtilleggene, kan ødelegge funksjonalitet i temaet ditt eller programtilleggene dine, så du må sannsynligvis gjøre kodeendringer."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, programtillegg og tjenerspesifikasjoner virker inn på tjenerens responstid. Vurder å finne et mer optimalisert tema, velge et programtillegg for optimalisering nøye og/eller oppgradere tjeneren."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Vurder å vise utdrag på innleggslistene (f.eks. via Mer-taggen), redusere antall innlegg som vises på en gitt side, fordele lange innlegg på flere sider eller bruke et programtillegg for utsatt innlasting av kommentarer."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "En rekke [WordPress-programtillegg](https://wordpress.org/plugins/search/minify+css/) kan gjøre nettstedet ditt raskere ved å slå sammen, forminske og komprimere stilarkene dine. Det kan også være lurt å bruke en byggeprosess til å gjøre denne forminskningen på forhånd hvis mulig."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "En rekke [WordPress-programtillegg](https://wordpress.org/plugins/search/minify+javascript/) kan gjøre nettstedet ditt raskere ved å slå sammen, forminske og komprimere skriptene dine. Det kan også være lurt å bruke en byggeprosess til å gjøre denne forminskningen på forhånd hvis mulig."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Vurder å redusere eller endre antall [WordPress-programtillegg](https://wordpress.org/plugins/) som laster inn ubrukt CSS på siden. For å identifisere programtillegg som legger til overflødig CSS, prøv å kjøre [kodedek<PERSON>](https://developer.chrome.com/docs/devtools/coverage/) i Chrome DevTools. Du kan finne det ansvarlige temaet eller programtillegget i nettadressen til stilarket. Se opp for programtillegg som har mange stilark på listen, og som viser mye rødt i kodedekningen. Programtillegg bør bare legge stilark i kø hvis de faktisk brukes på siden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Vurder å redusere eller endre antall [WordPress-programtillegg](https://wordpress.org/plugins/) som laster inn ubrukt JavaScript på siden. For å identifisere programtillegg som legger til overflødig JavaScript, prøv å kjøre [kodedek<PERSON>](https://developer.chrome.com/docs/devtools/coverage/) i Chrome DevTools. Du kan finne det ansvarlige temaet eller programtillegget i nettadressen til skriptet. Se opp for programtillegg som har mange skript på listen, og som viser mye rødt i kodedekningen. Programtillegg bør bare legge skript i kø hvis de faktisk brukes på siden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Les om [nettleserbufring i WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "V<PERSON>der å bruke et [WordPress-programtillegg for bildeoptimalisering](https://wordpress.org/plugins/search/optimize+images/) som komprimerer bildene uten at det går ut over kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Last opp bilder direkte gjennom [mediebiblioteket](https://wordpress.org/support/article/media-library-screen/) for å sikre at de nødvendige bildestørrelsene er tilgjengelige, og sett dem deretter inn fra mediebiblioteket eller bruk bildemodulen for å sikre at de optimale bildestørrelsene brukes (inkludert dem som brukes for responsive stoppunkter). Unngå å bruke `Full Size`-bilder med mindre størrelsen er tilstrekkelig for bruksområdet. [Finn ut mer](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Du kan slå på tekstkomprimering i oppsettet av nettjeneren."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Slå på «Forstørr» fra Bildeoptimalisering-fanen i «WP Rocket» for å konvertere bildene dine til WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "S<PERSON>å på [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) i WP Rocket for å løse denne anbefalingen. Denne funksjonen forsinker innlastingen av bildene til den besøkende ruller ned på siden og faktisk trenger å se dem."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> på [<PERSON><PERSON><PERSON> ubrukt CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) og [Last inn JavaScript utsatt](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) i «WP Rocket» for å løse denne anbefalingen. Disse funksjonene optimaliserer henholdsvis CSS- og JavaScript-filene slik at de ikke blokkerer gjengivelsen av siden din."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Slå på [Minifiser CSS-filer](https://docs.wp-rocket.me/article/1350-css-minify-combine) i «WP Rocket» for å løse dette problemet. Eventuelle rom og kommentarer i CSS-filene på nettstedet ditt fjernes for å gjøre filstørrelsen mindre og raskere å laste ned."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> på [Minimer JavaScript-filer](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) i «WP Rocket» for å løse dette problemet. Tomme rom og kommentarer fjernes fra JavaScript-filer for å gjøre størrelsen mindre og raskere å laste ned."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Slå på [<PERSON><PERSON><PERSON> ubrukt CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) i «WP Rocket» for å løse dette problemet. Den reduserer sidestørrelsen ved å fjerne alle CSS-elementer og stilark som ikke brukes, samtidig som bare den brukte CSS-koden beholdes for hver side."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Slå på [<PERSON><PERSON>ett JavaScript-kjø<PERSON>](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) i «WP Rocket» for å løse dette problemet. Dette forbedrer innlastingen av siden ved å forsinke kjøringen av skript til brukerinteraksjon. Hvis nettstedet ditt har iframes, kan du også bruke WP Rockets [LazyLoad for iframes og videoer](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) og [Erstatt YouTube iframe med forhåndsvisningsbilde](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Slå på «Forstørr» fra Bildeoptimalisering-fanen i «WP Rocket» og kjør masseoptimalisering for å komprimere bildene dine."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Bruk [Hent DNS-forespø<PERSON><PERSON> på forhånd](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) i «WP Rocket» for å legge til «dns-prefetch» og øke hastigheten til forbindelsen med eksterne domener. «WP Rocket» legger også automatisk til «preconnect» (forhåndstilkoble) på [Google Fonts-domenet](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) og eventuelle CNAME-er som legges til via funksjonen [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (aktiver ILN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "For å løse dette problemet med skrifttyper, slå på [Fjern ubrukt CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) i «WP Rocket». De kritiske skrifttypene på nettstedet ditt forhåndslastes med prioritet."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON> kalk<PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "Skjul visningen"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navigasjonsstart"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maksimum kritisk baneforsinkelse:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON><PERSON><PERSON>r JSO<PERSON>"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Slå av/på mørkt tema"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Skriv ut utvidet rapport"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Skriv ut sammendrag"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Lagre som Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Lagre som HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Lagre som JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Åpne i Viewer"}, "report/renderer/report-utils.js | errorLabel": {"message": "Feil!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Rapportfeil: ingen revisjonsinformasjon"}, "report/renderer/report-utils.js | expandView": {"message": "<PERSON><PERSON><PERSON> v<PERSON>"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON><PERSON><PERSON><PERSON> sak"}, "report/renderer/report-utils.js | hide": {"message": "Skjul"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Prøvefunksjonsdata"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/)-analyse av den nåværende siden på et emulert mobilnettverk. Verdiene er anslått og kan variere."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Tilleggselementer for manuell kontroll"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Ikke <PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Mulighet"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Estimerte besparelser"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON> revisjoner"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "<PERSON><PERSON><PERSON><PERSON> sideinnlasting"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Egendefinert struping"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON>rt skrivebord"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Ingen emulering"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-versjon"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Prosessor-/minnelogg uten begrensninger"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Prosessorstruping"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Struping av nettverkshastighet"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Skjermemulering"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Brukeragent (nettverk)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Enkelt sideinnlasting"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Disse dataene er hentet fra én enkelt sideinnlasting, i motsetning til feltdata som oppsummerer mange økter."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Treg 4G-struping"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | show": {"message": "Vis"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Vis revisjoner som er relevant for:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Skjul fragment"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Vis fragment"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON> t<PERSON>"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Utført av kjøretidsmiljøet"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Det oppsto problemer som påvirker denne kjøringen av Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Verdiene er anslått og kan variere. [Beregningen av ytelsespoengsummen](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) er basert direkte på disse verdiene."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Vis opprinnelig sporing"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Vis sporing"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON>is trekart"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Revisjoner som er bestått, men med advarsler"}, "report/renderer/report-utils.js | warningHeader": {"message": "<PERSON><PERSON><PERSON>: "}, "treemap/app/src/util.js | allLabel": {"message": "Alt"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON>e skript"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Dekning"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Ressursstørrelse i byte"}, "treemap/app/src/util.js | tableColumnName": {"message": "Navn"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Slå av/på tabell"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Ubrukte byte"}}