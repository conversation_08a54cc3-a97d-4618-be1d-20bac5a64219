{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"format\"],\n  _excluded2 = [\"children\", \"count\", \"parent\", \"i18nKey\", \"context\", \"tOptions\", \"values\", \"defaults\", \"components\", \"ns\", \"i18n\", \"t\", \"shouldUnescape\"];\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nimport React, { useContext } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { unescape } from 'html-escaper';\nimport { getI18n, I18nContext, getDefaults } from './context';\nimport { warn, warnOnce } from './utils';\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  var base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\nfunction getChildren(node) {\n  if (!node) return [];\n  return node && node.children ? node.children : node.props && node.props.children;\n}\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(function (child) {\n    return React.isValidElement(child);\n  });\n}\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\nfunction mergeProps(source, target) {\n  var newTarget = _objectSpread({}, target);\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\nexport function nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  var stringNode = '';\n  var childrenArray = getAsArray(children);\n  var keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach(function (child, childIndex) {\n    if (typeof child === 'string') {\n      stringNode += \"\".concat(child);\n    } else if (React.isValidElement(child)) {\n      var childPropsCount = Object.keys(child.props).length;\n      var shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      var childChildren = child.props.children;\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += \"<\".concat(child.type, \"/>\");\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += \"<\".concat(childIndex, \"></\").concat(childIndex, \">\");\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += \"<\".concat(childIndex, \"></\").concat(childIndex, \">\");\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += \"<\".concat(child.type, \">\").concat(childChildren, \"</\").concat(child.type, \">\");\n      } else {\n        var content = nodesToString(childChildren, i18nOptions);\n        stringNode += \"<\".concat(childIndex, \">\").concat(content, \"</\").concat(childIndex, \">\");\n      }\n    } else if (child === null) {\n      warn(\"Trans: the passed in value is invalid - seems you passed in a null child.\");\n    } else if (_typeof(child) === 'object') {\n      var format = child.format,\n        clone = _objectWithoutProperties(child, _excluded);\n      var keys = Object.keys(clone);\n      if (keys.length === 1) {\n        var value = format ? \"\".concat(keys[0], \", \").concat(format) : keys[0];\n        stringNode += \"{{\".concat(value, \"}}\");\n      } else {\n        warn(\"react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.\", child);\n      }\n    } else {\n      warn(\"Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.\", child);\n    }\n  });\n  return stringNode;\n}\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  var keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  var emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling) return [targetString];\n  var data = {};\n  function getData(childs) {\n    var childrenArray = getAsArray(childs);\n    childrenArray.forEach(function (child) {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (_typeof(child) === 'object' && !React.isValidElement(child)) Object.assign(data, child);\n    });\n  }\n  getData(children);\n  var ast = HTML.parse(\"<0>\".concat(targetString, \"</0>\"));\n  var opts = _objectSpread(_objectSpread({}, data), combinedTOpts);\n  function renderInner(child, node, rootReactNode) {\n    var childs = getChildren(child);\n    var mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 ? childs : mappedChildren;\n  }\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) child.children = inner;\n    mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n      key: i\n    }), isVoid ? undefined : inner));\n  }\n  function mapAST(reactNode, astNode, rootReactNode) {\n    var reactNodes = getAsArray(reactNode);\n    var astNodes = getAsArray(astNode);\n    return astNodes.reduce(function (mem, node, i) {\n      var translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        var tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && rootReactNode.length === 1 && rootReactNode[0][node.name]) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        var child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        var isElement = React.isValidElement(child);\n        var isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        var isEmptyTransWithHTML = emptyChildrenButNeedsHandling && _typeof(child) === 'object' && child.dummy && !isElement;\n        var isKnownComponent = _typeof(children) === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n        if (typeof child === 'string') {\n          var value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          var inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          var _inner = mapAST(reactNodes, node.children, rootReactNode);\n          mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n            key: i\n          }), _inner));\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            var _inner2 = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, _inner2, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(React.createElement(node.name, {\n                key: \"\".concat(node.name, \"-\").concat(i)\n              }));\n            } else {\n              var _inner3 = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(React.createElement(node.name, {\n                key: \"\".concat(node.name, \"-\").concat(i)\n              }, _inner3));\n            }\n          } else if (node.voidElement) {\n            mem.push(\"<\".concat(node.name, \" />\"));\n          } else {\n            var _inner4 = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(\"<\".concat(node.name, \">\").concat(_inner4, \"</\").concat(node.name, \">\"));\n          }\n        } else if (_typeof(child) === 'object' && !isElement) {\n          var content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else if (node.children.length === 1 && translationContent) {\n          mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n            key: i\n          }), translationContent));\n        } else {\n          mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n            key: i\n          })));\n        }\n      } else if (node.type === 'text') {\n        var wrapTextNodes = i18nOptions.transWrapTextNodes;\n        var _content = shouldUnescape ? unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(React.createElement(wrapTextNodes, {\n            key: \"\".concat(node.name, \"-\").concat(i)\n          }, _content));\n        } else {\n          mem.push(_content);\n        }\n      }\n      return mem;\n    }, []);\n  }\n  var result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\nexport function Trans(_ref) {\n  var children = _ref.children,\n    count = _ref.count,\n    parent = _ref.parent,\n    i18nKey = _ref.i18nKey,\n    context = _ref.context,\n    _ref$tOptions = _ref.tOptions,\n    tOptions = _ref$tOptions === void 0 ? {} : _ref$tOptions,\n    values = _ref.values,\n    defaults = _ref.defaults,\n    components = _ref.components,\n    ns = _ref.ns,\n    i18nFromProps = _ref.i18n,\n    tFromProps = _ref.t,\n    shouldUnescape = _ref.shouldUnescape,\n    additionalProps = _objectWithoutProperties(_ref, _excluded2);\n  var _ref2 = useContext(I18nContext) || {},\n    i18nFromContext = _ref2.i18n,\n    defaultNSFromContext = _ref2.defaultNS;\n  var i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  var t = tFromProps || i18n.t.bind(i18n) || function (k) {\n    return k;\n  };\n  if (context) tOptions.context = context;\n  var reactI18nextOptions = _objectSpread(_objectSpread({}, getDefaults()), i18n.options && i18n.options.react);\n  var namespaces = ns || t.ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  var defaultValue = defaults || nodesToString(children, reactI18nextOptions) || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  var hashTransKey = reactI18nextOptions.hashTransKey;\n  var key = i18nKey || (hashTransKey ? hashTransKey(defaultValue) : defaultValue);\n  var interpolationOverride = values ? tOptions.interpolation : {\n    interpolation: _objectSpread(_objectSpread({}, tOptions.interpolation), {}, {\n      prefix: '#$?',\n      suffix: '?$#'\n    })\n  };\n  var combinedTOpts = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, tOptions), {}, {\n    count: count\n  }, values), interpolationOverride), {}, {\n    defaultValue: defaultValue,\n    ns: namespaces\n  });\n  var translation = key ? t(key, combinedTOpts) : defaultValue;\n  var content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  var useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? React.createElement(useAsParent, additionalProps, content) : content;\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_typeof", "_defineProperty", "_excluded", "_excluded2", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "React", "useContext", "HTML", "unescape", "getI18n", "I18nContext", "getDefaults", "warn", "warnOnce", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "checkLength", "base", "props", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasValidReactChildren", "prototype", "toString", "call", "every", "child", "isValidElement", "getAsArray", "data", "Array", "isArray", "mergeProps", "newTarget", "assign", "nodesToString", "i18nOptions", "stringNode", "childrenA<PERSON>y", "keepArray", "transSupportBasicHtmlNodes", "transKeepBasicHtmlNodesFor", "childIndex", "concat", "childPropsCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18nIsDynamicList", "content", "format", "clone", "value", "renderNodes", "targetString", "i18n", "combinedTOpts", "shouldUnescape", "emptyChildrenButNeedsHandling", "RegExp", "join", "test", "getData", "childs", "ast", "parse", "opts", "renderInner", "rootReactNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapAST", "pushTranslatedJSX", "inner", "mem", "isVoid", "dummy", "cloneElement", "undefined", "reactNode", "astNode", "reactNodes", "astNodes", "reduce", "translationContent", "services", "interpolator", "interpolate", "language", "tmp", "parseInt", "name", "attrs", "isElement", "isValidTranslationWithChildren", "voidElement", "isEmptyTransWithHTML", "isKnownComponent", "hasOwnProperty", "_inner", "Number", "isNaN", "parseFloat", "_inner2", "createElement", "_inner3", "_inner4", "wrapTextNodes", "transWrapTextNodes", "_content", "result", "Trans", "_ref", "count", "parent", "i18nKey", "context", "_ref$tOptions", "tOptions", "values", "defaults", "components", "ns", "i18nFromProps", "tFromProps", "t", "additionalProps", "_ref2", "i18nFromContext", "defaultNSFromContext", "defaultNS", "bind", "k", "reactI18nextOptions", "options", "react", "namespaces", "defaultValue", "transEmptyNodeValue", "hashTransKey", "interpolationOverride", "interpolation", "prefix", "suffix", "translation", "useAsParent", "defaultTransParent"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/Trans.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"format\"],\n    _excluded2 = [\"children\", \"count\", \"parent\", \"i18nKey\", \"context\", \"tOptions\", \"values\", \"defaults\", \"components\", \"ns\", \"i18n\", \"t\", \"shouldUnescape\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport React, { useContext } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { unescape } from 'html-escaper';\nimport { getI18n, I18nContext, getDefaults } from './context';\nimport { warn, warnOnce } from './utils';\n\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  var base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\n\nfunction getChildren(node) {\n  if (!node) return [];\n  return node && node.children ? node.children : node.props && node.props.children;\n}\n\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(function (child) {\n    return React.isValidElement(child);\n  });\n}\n\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\n\nfunction mergeProps(source, target) {\n  var newTarget = _objectSpread({}, target);\n\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\n\nexport function nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  var stringNode = '';\n  var childrenArray = getAsArray(children);\n  var keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach(function (child, childIndex) {\n    if (typeof child === 'string') {\n      stringNode += \"\".concat(child);\n    } else if (React.isValidElement(child)) {\n      var childPropsCount = Object.keys(child.props).length;\n      var shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      var childChildren = child.props.children;\n\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += \"<\".concat(child.type, \"/>\");\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += \"<\".concat(childIndex, \"></\").concat(childIndex, \">\");\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += \"<\".concat(childIndex, \"></\").concat(childIndex, \">\");\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += \"<\".concat(child.type, \">\").concat(childChildren, \"</\").concat(child.type, \">\");\n      } else {\n        var content = nodesToString(childChildren, i18nOptions);\n        stringNode += \"<\".concat(childIndex, \">\").concat(content, \"</\").concat(childIndex, \">\");\n      }\n    } else if (child === null) {\n      warn(\"Trans: the passed in value is invalid - seems you passed in a null child.\");\n    } else if (_typeof(child) === 'object') {\n      var format = child.format,\n          clone = _objectWithoutProperties(child, _excluded);\n\n      var keys = Object.keys(clone);\n\n      if (keys.length === 1) {\n        var value = format ? \"\".concat(keys[0], \", \").concat(format) : keys[0];\n        stringNode += \"{{\".concat(value, \"}}\");\n      } else {\n        warn(\"react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.\", child);\n      }\n    } else {\n      warn(\"Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.\", child);\n    }\n  });\n  return stringNode;\n}\n\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  var keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  var emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling) return [targetString];\n  var data = {};\n\n  function getData(childs) {\n    var childrenArray = getAsArray(childs);\n    childrenArray.forEach(function (child) {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (_typeof(child) === 'object' && !React.isValidElement(child)) Object.assign(data, child);\n    });\n  }\n\n  getData(children);\n  var ast = HTML.parse(\"<0>\".concat(targetString, \"</0>\"));\n\n  var opts = _objectSpread(_objectSpread({}, data), combinedTOpts);\n\n  function renderInner(child, node, rootReactNode) {\n    var childs = getChildren(child);\n    var mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 ? childs : mappedChildren;\n  }\n\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) child.children = inner;\n    mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n      key: i\n    }), isVoid ? undefined : inner));\n  }\n\n  function mapAST(reactNode, astNode, rootReactNode) {\n    var reactNodes = getAsArray(reactNode);\n    var astNodes = getAsArray(astNode);\n    return astNodes.reduce(function (mem, node, i) {\n      var translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n\n      if (node.type === 'tag') {\n        var tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && rootReactNode.length === 1 && rootReactNode[0][node.name]) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        var child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        var isElement = React.isValidElement(child);\n        var isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        var isEmptyTransWithHTML = emptyChildrenButNeedsHandling && _typeof(child) === 'object' && child.dummy && !isElement;\n        var isKnownComponent = _typeof(children) === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n\n        if (typeof child === 'string') {\n          var value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n            var inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i);\n          } else if (isEmptyTransWithHTML) {\n          var _inner = mapAST(reactNodes, node.children, rootReactNode);\n\n          mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n            key: i\n          }), _inner));\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            var _inner2 = renderInner(child, node, rootReactNode);\n\n            pushTranslatedJSX(child, _inner2, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(React.createElement(node.name, {\n                key: \"\".concat(node.name, \"-\").concat(i)\n              }));\n            } else {\n              var _inner3 = mapAST(reactNodes, node.children, rootReactNode);\n\n              mem.push(React.createElement(node.name, {\n                key: \"\".concat(node.name, \"-\").concat(i)\n              }, _inner3));\n            }\n          } else if (node.voidElement) {\n            mem.push(\"<\".concat(node.name, \" />\"));\n          } else {\n            var _inner4 = mapAST(reactNodes, node.children, rootReactNode);\n\n            mem.push(\"<\".concat(node.name, \">\").concat(_inner4, \"</\").concat(node.name, \">\"));\n          }\n        } else if (_typeof(child) === 'object' && !isElement) {\n          var content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else if (node.children.length === 1 && translationContent) {\n          mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n            key: i\n          }), translationContent));\n        } else {\n          mem.push(React.cloneElement(child, _objectSpread(_objectSpread({}, child.props), {}, {\n            key: i\n          })));\n        }\n      } else if (node.type === 'text') {\n        var wrapTextNodes = i18nOptions.transWrapTextNodes;\n\n        var _content = shouldUnescape ? unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n\n        if (wrapTextNodes) {\n          mem.push(React.createElement(wrapTextNodes, {\n            key: \"\".concat(node.name, \"-\").concat(i)\n          }, _content));\n        } else {\n          mem.push(_content);\n        }\n      }\n\n      return mem;\n    }, []);\n  }\n\n  var result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\n\nexport function Trans(_ref) {\n  var children = _ref.children,\n      count = _ref.count,\n      parent = _ref.parent,\n      i18nKey = _ref.i18nKey,\n      context = _ref.context,\n      _ref$tOptions = _ref.tOptions,\n      tOptions = _ref$tOptions === void 0 ? {} : _ref$tOptions,\n      values = _ref.values,\n      defaults = _ref.defaults,\n      components = _ref.components,\n      ns = _ref.ns,\n      i18nFromProps = _ref.i18n,\n      tFromProps = _ref.t,\n      shouldUnescape = _ref.shouldUnescape,\n      additionalProps = _objectWithoutProperties(_ref, _excluded2);\n\n  var _ref2 = useContext(I18nContext) || {},\n      i18nFromContext = _ref2.i18n,\n      defaultNSFromContext = _ref2.defaultNS;\n\n  var i18n = i18nFromProps || i18nFromContext || getI18n();\n\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n\n  var t = tFromProps || i18n.t.bind(i18n) || function (k) {\n    return k;\n  };\n\n  if (context) tOptions.context = context;\n\n  var reactI18nextOptions = _objectSpread(_objectSpread({}, getDefaults()), i18n.options && i18n.options.react);\n\n  var namespaces = ns || t.ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  var defaultValue = defaults || nodesToString(children, reactI18nextOptions) || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  var hashTransKey = reactI18nextOptions.hashTransKey;\n  var key = i18nKey || (hashTransKey ? hashTransKey(defaultValue) : defaultValue);\n  var interpolationOverride = values ? tOptions.interpolation : {\n    interpolation: _objectSpread(_objectSpread({}, tOptions.interpolation), {}, {\n      prefix: '#$?',\n      suffix: '?$#'\n    })\n  };\n\n  var combinedTOpts = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, tOptions), {}, {\n    count: count\n  }, values), interpolationOverride), {}, {\n    defaultValue: defaultValue,\n    ns: namespaces\n  });\n\n  var translation = key ? t(key, combinedTOpts) : defaultValue;\n  var content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  var useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? React.createElement(useAsParent, additionalProps, content) : content;\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,gDAAgD;AACrF,OAAOC,OAAO,MAAM,+BAA+B;AACnD,OAAOC,eAAe,MAAM,uCAAuC;AACnE,IAAIC,SAAS,GAAG,CAAC,QAAQ,CAAC;EACtBC,UAAU,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,gBAAgB,CAAC;AAE3J,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEvB,eAAe,CAACiB,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACiB,yBAAyB,EAAE;MAAEjB,MAAM,CAACkB,gBAAgB,CAACR,MAAM,EAAEV,MAAM,CAACiB,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACmB,cAAc,CAACT,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,OAAOU,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,IAAI,MAAM,sBAAsB;AACvC,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,WAAW;AAC7D,SAASC,IAAI,EAAEC,QAAQ,QAAQ,SAAS;AAExC,SAASC,WAAWA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB,IAAIE,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC3D,IAAIH,WAAW,EAAE,OAAOC,IAAI,CAACnB,MAAM,GAAG,CAAC;EACvC,OAAO,CAAC,CAACmB,IAAI;AACf;AAEA,SAASG,WAAWA,CAACL,IAAI,EAAE;EACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,OAAOA,IAAI,IAAIA,IAAI,CAACI,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,GAAGJ,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACG,KAAK,CAACC,QAAQ;AAClF;AAEA,SAASE,qBAAqBA,CAACF,QAAQ,EAAE;EACvC,IAAIlC,MAAM,CAACqC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,QAAQ,CAAC,KAAK,gBAAgB,EAAE,OAAO,KAAK;EAC/E,OAAOA,QAAQ,CAACM,KAAK,CAAC,UAAUC,KAAK,EAAE;IACrC,OAAOrB,KAAK,CAACsB,cAAc,CAACD,KAAK,CAAC;EACpC,CAAC,CAAC;AACJ;AAEA,SAASE,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC5C;AAEA,SAASG,UAAUA,CAACjC,MAAM,EAAEJ,MAAM,EAAE;EAClC,IAAIsC,SAAS,GAAGvC,aAAa,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC;EAEzCsC,SAAS,CAACf,KAAK,GAAGjC,MAAM,CAACiD,MAAM,CAACnC,MAAM,CAACmB,KAAK,EAAEvB,MAAM,CAACuB,KAAK,CAAC;EAC3D,OAAOe,SAAS;AAClB;AAEA,OAAO,SAASE,aAAaA,CAAChB,QAAQ,EAAEiB,WAAW,EAAE;EACnD,IAAI,CAACjB,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIkB,UAAU,GAAG,EAAE;EACnB,IAAIC,aAAa,GAAGV,UAAU,CAACT,QAAQ,CAAC;EACxC,IAAIoB,SAAS,GAAGH,WAAW,CAACI,0BAA0B,IAAIJ,WAAW,CAACK,0BAA0B,GAAGL,WAAW,CAACK,0BAA0B,GAAG,EAAE;EAC9IH,aAAa,CAACtC,OAAO,CAAC,UAAU0B,KAAK,EAAEgB,UAAU,EAAE;IACjD,IAAI,OAAOhB,KAAK,KAAK,QAAQ,EAAE;MAC7BW,UAAU,IAAI,EAAE,CAACM,MAAM,CAACjB,KAAK,CAAC;IAChC,CAAC,MAAM,IAAIrB,KAAK,CAACsB,cAAc,CAACD,KAAK,CAAC,EAAE;MACtC,IAAIkB,eAAe,GAAG3D,MAAM,CAACD,IAAI,CAAC0C,KAAK,CAACR,KAAK,CAAC,CAACpB,MAAM;MACrD,IAAI+C,eAAe,GAAGN,SAAS,CAACO,OAAO,CAACpB,KAAK,CAACqB,IAAI,CAAC,GAAG,CAAC,CAAC;MACxD,IAAIC,aAAa,GAAGtB,KAAK,CAACR,KAAK,CAACC,QAAQ;MAExC,IAAI,CAAC6B,aAAa,IAAIH,eAAe,IAAID,eAAe,KAAK,CAAC,EAAE;QAC9DP,UAAU,IAAI,GAAG,CAACM,MAAM,CAACjB,KAAK,CAACqB,IAAI,EAAE,IAAI,CAAC;MAC5C,CAAC,MAAM,IAAI,CAACC,aAAa,KAAK,CAACH,eAAe,IAAID,eAAe,KAAK,CAAC,CAAC,EAAE;QACxEP,UAAU,IAAI,GAAG,CAACM,MAAM,CAACD,UAAU,EAAE,KAAK,CAAC,CAACC,MAAM,CAACD,UAAU,EAAE,GAAG,CAAC;MACrE,CAAC,MAAM,IAAIhB,KAAK,CAACR,KAAK,CAAC+B,iBAAiB,EAAE;QACxCZ,UAAU,IAAI,GAAG,CAACM,MAAM,CAACD,UAAU,EAAE,KAAK,CAAC,CAACC,MAAM,CAACD,UAAU,EAAE,GAAG,CAAC;MACrE,CAAC,MAAM,IAAIG,eAAe,IAAID,eAAe,KAAK,CAAC,IAAI,OAAOI,aAAa,KAAK,QAAQ,EAAE;QACxFX,UAAU,IAAI,GAAG,CAACM,MAAM,CAACjB,KAAK,CAACqB,IAAI,EAAE,GAAG,CAAC,CAACJ,MAAM,CAACK,aAAa,EAAE,IAAI,CAAC,CAACL,MAAM,CAACjB,KAAK,CAACqB,IAAI,EAAE,GAAG,CAAC;MAC/F,CAAC,MAAM;QACL,IAAIG,OAAO,GAAGf,aAAa,CAACa,aAAa,EAAEZ,WAAW,CAAC;QACvDC,UAAU,IAAI,GAAG,CAACM,MAAM,CAACD,UAAU,EAAE,GAAG,CAAC,CAACC,MAAM,CAACO,OAAO,EAAE,IAAI,CAAC,CAACP,MAAM,CAACD,UAAU,EAAE,GAAG,CAAC;MACzF;IACF,CAAC,MAAM,IAAIhB,KAAK,KAAK,IAAI,EAAE;MACzBd,IAAI,CAAC,2EAA2E,CAAC;IACnF,CAAC,MAAM,IAAInC,OAAO,CAACiD,KAAK,CAAC,KAAK,QAAQ,EAAE;MACtC,IAAIyB,MAAM,GAAGzB,KAAK,CAACyB,MAAM;QACrBC,KAAK,GAAG5E,wBAAwB,CAACkD,KAAK,EAAE/C,SAAS,CAAC;MAEtD,IAAIK,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACoE,KAAK,CAAC;MAE7B,IAAIpE,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;QACrB,IAAIuD,KAAK,GAAGF,MAAM,GAAG,EAAE,CAACR,MAAM,CAAC3D,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC2D,MAAM,CAACQ,MAAM,CAAC,GAAGnE,IAAI,CAAC,CAAC,CAAC;QACtEqD,UAAU,IAAI,IAAI,CAACM,MAAM,CAACU,KAAK,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLzC,IAAI,CAAC,kJAAkJ,EAAEc,KAAK,CAAC;MACjK;IACF,CAAC,MAAM;MACLd,IAAI,CAAC,oKAAoK,EAAEc,KAAK,CAAC;IACnL;EACF,CAAC,CAAC;EACF,OAAOW,UAAU;AACnB;AAEA,SAASiB,WAAWA,CAACnC,QAAQ,EAAEoC,YAAY,EAAEC,IAAI,EAAEpB,WAAW,EAAEqB,aAAa,EAAEC,cAAc,EAAE;EAC7F,IAAIH,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE;EAClC,IAAIhB,SAAS,GAAGH,WAAW,CAACK,0BAA0B,IAAI,EAAE;EAC5D,IAAIkB,6BAA6B,GAAGJ,YAAY,IAAI,IAAIK,MAAM,CAACrB,SAAS,CAACsB,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACP,YAAY,CAAC;EACtG,IAAI,CAACpC,QAAQ,IAAI,CAACwC,6BAA6B,EAAE,OAAO,CAACJ,YAAY,CAAC;EACtE,IAAI1B,IAAI,GAAG,CAAC,CAAC;EAEb,SAASkC,OAAOA,CAACC,MAAM,EAAE;IACvB,IAAI1B,aAAa,GAAGV,UAAU,CAACoC,MAAM,CAAC;IACtC1B,aAAa,CAACtC,OAAO,CAAC,UAAU0B,KAAK,EAAE;MACrC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,IAAIZ,WAAW,CAACY,KAAK,CAAC,EAAEqC,OAAO,CAAC3C,WAAW,CAACM,KAAK,CAAC,CAAC,CAAC,KAAK,IAAIjD,OAAO,CAACiD,KAAK,CAAC,KAAK,QAAQ,IAAI,CAACrB,KAAK,CAACsB,cAAc,CAACD,KAAK,CAAC,EAAEzC,MAAM,CAACiD,MAAM,CAACL,IAAI,EAAEH,KAAK,CAAC;IACtJ,CAAC,CAAC;EACJ;EAEAqC,OAAO,CAAC5C,QAAQ,CAAC;EACjB,IAAI8C,GAAG,GAAG1D,IAAI,CAAC2D,KAAK,CAAC,KAAK,CAACvB,MAAM,CAACY,YAAY,EAAE,MAAM,CAAC,CAAC;EAExD,IAAIY,IAAI,GAAGzE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,IAAI,CAAC,EAAE4B,aAAa,CAAC;EAEhE,SAASW,WAAWA,CAAC1C,KAAK,EAAEX,IAAI,EAAEsD,aAAa,EAAE;IAC/C,IAAIL,MAAM,GAAG5C,WAAW,CAACM,KAAK,CAAC;IAC/B,IAAI4C,cAAc,GAAGC,MAAM,CAACP,MAAM,EAAEjD,IAAI,CAACI,QAAQ,EAAEkD,aAAa,CAAC;IACjE,OAAOhD,qBAAqB,CAAC2C,MAAM,CAAC,IAAIM,cAAc,CAACxE,MAAM,KAAK,CAAC,GAAGkE,MAAM,GAAGM,cAAc;EAC/F;EAEA,SAASE,iBAAiBA,CAAC9C,KAAK,EAAE+C,KAAK,EAAEC,GAAG,EAAE9E,CAAC,EAAE+E,MAAM,EAAE;IACvD,IAAIjD,KAAK,CAACkD,KAAK,EAAElD,KAAK,CAACP,QAAQ,GAAGsD,KAAK;IACvCC,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACwE,YAAY,CAACnD,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACR,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACnFjB,GAAG,EAAEL;IACP,CAAC,CAAC,EAAE+E,MAAM,GAAGG,SAAS,GAAGL,KAAK,CAAC,CAAC;EAClC;EAEA,SAASF,MAAMA,CAACQ,SAAS,EAAEC,OAAO,EAAEX,aAAa,EAAE;IACjD,IAAIY,UAAU,GAAGrD,UAAU,CAACmD,SAAS,CAAC;IACtC,IAAIG,QAAQ,GAAGtD,UAAU,CAACoD,OAAO,CAAC;IAClC,OAAOE,QAAQ,CAACC,MAAM,CAAC,UAAUT,GAAG,EAAE3D,IAAI,EAAEnB,CAAC,EAAE;MAC7C,IAAIwF,kBAAkB,GAAGrE,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC+B,OAAO,IAAIM,IAAI,CAAC6B,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACxE,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC+B,OAAO,EAAEiB,IAAI,EAAEX,IAAI,CAACgC,QAAQ,CAAC;MAE/K,IAAIzE,IAAI,CAACgC,IAAI,KAAK,KAAK,EAAE;QACvB,IAAI0C,GAAG,GAAGR,UAAU,CAACS,QAAQ,CAAC3E,IAAI,CAAC4E,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI,CAACF,GAAG,IAAIpB,aAAa,CAACvE,MAAM,KAAK,CAAC,IAAIuE,aAAa,CAAC,CAAC,CAAC,CAACtD,IAAI,CAAC4E,IAAI,CAAC,EAAEF,GAAG,GAAGpB,aAAa,CAAC,CAAC,CAAC,CAACtD,IAAI,CAAC4E,IAAI,CAAC;QACxG,IAAI,CAACF,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAClB,IAAI/D,KAAK,GAAGzC,MAAM,CAACD,IAAI,CAAC+B,IAAI,CAAC6E,KAAK,CAAC,CAAC9F,MAAM,KAAK,CAAC,GAAGkC,UAAU,CAAC;UAC5Dd,KAAK,EAAEH,IAAI,CAAC6E;QACd,CAAC,EAAEH,GAAG,CAAC,GAAGA,GAAG;QACb,IAAII,SAAS,GAAGxF,KAAK,CAACsB,cAAc,CAACD,KAAK,CAAC;QAC3C,IAAIoE,8BAA8B,GAAGD,SAAS,IAAI/E,WAAW,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAACgF,WAAW;QAC9F,IAAIC,oBAAoB,GAAGrC,6BAA6B,IAAIlF,OAAO,CAACiD,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,CAACkD,KAAK,IAAI,CAACiB,SAAS;QACpH,IAAII,gBAAgB,GAAGxH,OAAO,CAAC0C,QAAQ,CAAC,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIlC,MAAM,CAACiH,cAAc,CAAC1E,IAAI,CAACL,QAAQ,EAAEJ,IAAI,CAAC4E,IAAI,CAAC;QAE7H,IAAI,OAAOjE,KAAK,KAAK,QAAQ,EAAE;UAC7B,IAAI2B,KAAK,GAAGG,IAAI,CAAC6B,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAC7D,KAAK,EAAEyC,IAAI,EAAEX,IAAI,CAACgC,QAAQ,CAAC;UAC9Ed,GAAG,CAAClF,IAAI,CAAC6D,KAAK,CAAC;QACjB,CAAC,MAAM,IAAIvC,WAAW,CAACY,KAAK,CAAC,IAAIoE,8BAA8B,EAAE;UAC7D,IAAIrB,KAAK,GAAGL,WAAW,CAAC1C,KAAK,EAAEX,IAAI,EAAEsD,aAAa,CAAC;UACnDG,iBAAiB,CAAC9C,KAAK,EAAE+C,KAAK,EAAEC,GAAG,EAAE9E,CAAC,CAAC;QACzC,CAAC,MAAM,IAAIoG,oBAAoB,EAAE;UACjC,IAAIG,MAAM,GAAG5B,MAAM,CAACU,UAAU,EAAElE,IAAI,CAACI,QAAQ,EAAEkD,aAAa,CAAC;UAE7DK,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACwE,YAAY,CAACnD,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACR,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACnFjB,GAAG,EAAEL;UACP,CAAC,CAAC,EAAEuG,MAAM,CAAC,CAAC;QACd,CAAC,MAAM,IAAIC,MAAM,CAACC,KAAK,CAACC,UAAU,CAACvF,IAAI,CAAC4E,IAAI,CAAC,CAAC,EAAE;UAC9C,IAAIM,gBAAgB,EAAE;YACpB,IAAIM,OAAO,GAAGnC,WAAW,CAAC1C,KAAK,EAAEX,IAAI,EAAEsD,aAAa,CAAC;YAErDG,iBAAiB,CAAC9C,KAAK,EAAE6E,OAAO,EAAE7B,GAAG,EAAE9E,CAAC,EAAEmB,IAAI,CAACgF,WAAW,CAAC;UAC7D,CAAC,MAAM,IAAI3D,WAAW,CAACI,0BAA0B,IAAID,SAAS,CAACO,OAAO,CAAC/B,IAAI,CAAC4E,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACtF,IAAI5E,IAAI,CAACgF,WAAW,EAAE;cACpBrB,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACmG,aAAa,CAACzF,IAAI,CAAC4E,IAAI,EAAE;gBACtC1F,GAAG,EAAE,EAAE,CAAC0C,MAAM,CAAC5B,IAAI,CAAC4E,IAAI,EAAE,GAAG,CAAC,CAAChD,MAAM,CAAC/C,CAAC;cACzC,CAAC,CAAC,CAAC;YACL,CAAC,MAAM;cACL,IAAI6G,OAAO,GAAGlC,MAAM,CAACU,UAAU,EAAElE,IAAI,CAACI,QAAQ,EAAEkD,aAAa,CAAC;cAE9DK,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACmG,aAAa,CAACzF,IAAI,CAAC4E,IAAI,EAAE;gBACtC1F,GAAG,EAAE,EAAE,CAAC0C,MAAM,CAAC5B,IAAI,CAAC4E,IAAI,EAAE,GAAG,CAAC,CAAChD,MAAM,CAAC/C,CAAC;cACzC,CAAC,EAAE6G,OAAO,CAAC,CAAC;YACd;UACF,CAAC,MAAM,IAAI1F,IAAI,CAACgF,WAAW,EAAE;YAC3BrB,GAAG,CAAClF,IAAI,CAAC,GAAG,CAACmD,MAAM,CAAC5B,IAAI,CAAC4E,IAAI,EAAE,KAAK,CAAC,CAAC;UACxC,CAAC,MAAM;YACL,IAAIe,OAAO,GAAGnC,MAAM,CAACU,UAAU,EAAElE,IAAI,CAACI,QAAQ,EAAEkD,aAAa,CAAC;YAE9DK,GAAG,CAAClF,IAAI,CAAC,GAAG,CAACmD,MAAM,CAAC5B,IAAI,CAAC4E,IAAI,EAAE,GAAG,CAAC,CAAChD,MAAM,CAAC+D,OAAO,EAAE,IAAI,CAAC,CAAC/D,MAAM,CAAC5B,IAAI,CAAC4E,IAAI,EAAE,GAAG,CAAC,CAAC;UACnF;QACF,CAAC,MAAM,IAAIlH,OAAO,CAACiD,KAAK,CAAC,KAAK,QAAQ,IAAI,CAACmE,SAAS,EAAE;UACpD,IAAI3C,OAAO,GAAGnC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGiE,kBAAkB,GAAG,IAAI;UAC1D,IAAIlC,OAAO,EAAEwB,GAAG,CAAClF,IAAI,CAAC0D,OAAO,CAAC;QAChC,CAAC,MAAM,IAAInC,IAAI,CAACI,QAAQ,CAACrB,MAAM,KAAK,CAAC,IAAIsF,kBAAkB,EAAE;UAC3DV,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACwE,YAAY,CAACnD,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACR,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACnFjB,GAAG,EAAEL;UACP,CAAC,CAAC,EAAEwF,kBAAkB,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLV,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACwE,YAAY,CAACnD,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACR,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACnFjB,GAAG,EAAEL;UACP,CAAC,CAAC,CAAC,CAAC;QACN;MACF,CAAC,MAAM,IAAImB,IAAI,CAACgC,IAAI,KAAK,MAAM,EAAE;QAC/B,IAAI4D,aAAa,GAAGvE,WAAW,CAACwE,kBAAkB;QAElD,IAAIC,QAAQ,GAAGnD,cAAc,GAAGlD,QAAQ,CAACgD,IAAI,CAAC6B,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACxE,IAAI,CAACmC,OAAO,EAAEiB,IAAI,EAAEX,IAAI,CAACgC,QAAQ,CAAC,CAAC,GAAGhC,IAAI,CAAC6B,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACxE,IAAI,CAACmC,OAAO,EAAEiB,IAAI,EAAEX,IAAI,CAACgC,QAAQ,CAAC;QAE/L,IAAImB,aAAa,EAAE;UACjBjC,GAAG,CAAClF,IAAI,CAACa,KAAK,CAACmG,aAAa,CAACG,aAAa,EAAE;YAC1C1G,GAAG,EAAE,EAAE,CAAC0C,MAAM,CAAC5B,IAAI,CAAC4E,IAAI,EAAE,GAAG,CAAC,CAAChD,MAAM,CAAC/C,CAAC;UACzC,CAAC,EAAEiH,QAAQ,CAAC,CAAC;QACf,CAAC,MAAM;UACLnC,GAAG,CAAClF,IAAI,CAACqH,QAAQ,CAAC;QACpB;MACF;MAEA,OAAOnC,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EAEA,IAAIoC,MAAM,GAAGvC,MAAM,CAAC,CAAC;IACnBK,KAAK,EAAE,IAAI;IACXzD,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC,CAAC,EAAE8C,GAAG,EAAErC,UAAU,CAACT,QAAQ,IAAI,EAAE,CAAC,CAAC;EACpC,OAAOC,WAAW,CAAC0F,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;AAEA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI7F,QAAQ,GAAG6F,IAAI,CAAC7F,QAAQ;IACxB8F,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;IACtBC,aAAa,GAAGL,IAAI,CAACM,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;IACxDE,MAAM,GAAGP,IAAI,CAACO,MAAM;IACpBC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IACxBC,UAAU,GAAGT,IAAI,CAACS,UAAU;IAC5BC,EAAE,GAAGV,IAAI,CAACU,EAAE;IACZC,aAAa,GAAGX,IAAI,CAACxD,IAAI;IACzBoE,UAAU,GAAGZ,IAAI,CAACa,CAAC;IACnBnE,cAAc,GAAGsD,IAAI,CAACtD,cAAc;IACpCoE,eAAe,GAAGtJ,wBAAwB,CAACwI,IAAI,EAAEpI,UAAU,CAAC;EAEhE,IAAImJ,KAAK,GAAGzH,UAAU,CAACI,WAAW,CAAC,IAAI,CAAC,CAAC;IACrCsH,eAAe,GAAGD,KAAK,CAACvE,IAAI;IAC5ByE,oBAAoB,GAAGF,KAAK,CAACG,SAAS;EAE1C,IAAI1E,IAAI,GAAGmE,aAAa,IAAIK,eAAe,IAAIvH,OAAO,CAAC,CAAC;EAExD,IAAI,CAAC+C,IAAI,EAAE;IACT3C,QAAQ,CAAC,0EAA0E,CAAC;IACpF,OAAOM,QAAQ;EACjB;EAEA,IAAI0G,CAAC,GAAGD,UAAU,IAAIpE,IAAI,CAACqE,CAAC,CAACM,IAAI,CAAC3E,IAAI,CAAC,IAAI,UAAU4E,CAAC,EAAE;IACtD,OAAOA,CAAC;EACV,CAAC;EAED,IAAIhB,OAAO,EAAEE,QAAQ,CAACF,OAAO,GAAGA,OAAO;EAEvC,IAAIiB,mBAAmB,GAAG3I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,WAAW,CAAC,CAAC,CAAC,EAAE6C,IAAI,CAAC8E,OAAO,IAAI9E,IAAI,CAAC8E,OAAO,CAACC,KAAK,CAAC;EAE7G,IAAIC,UAAU,GAAGd,EAAE,IAAIG,CAAC,CAACH,EAAE,IAAIO,oBAAoB,IAAIzE,IAAI,CAAC8E,OAAO,IAAI9E,IAAI,CAAC8E,OAAO,CAACJ,SAAS;EAC7FM,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAC1F,IAAIC,YAAY,GAAGjB,QAAQ,IAAIrF,aAAa,CAAChB,QAAQ,EAAEkH,mBAAmB,CAAC,IAAIA,mBAAmB,CAACK,mBAAmB,IAAIvB,OAAO;EACjI,IAAIwB,YAAY,GAAGN,mBAAmB,CAACM,YAAY;EACnD,IAAI1I,GAAG,GAAGkH,OAAO,KAAKwB,YAAY,GAAGA,YAAY,CAACF,YAAY,CAAC,GAAGA,YAAY,CAAC;EAC/E,IAAIG,qBAAqB,GAAGrB,MAAM,GAAGD,QAAQ,CAACuB,aAAa,GAAG;IAC5DA,aAAa,EAAEnJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4H,QAAQ,CAACuB,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MAC1EC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV,CAAC;EACH,CAAC;EAED,IAAItF,aAAa,GAAG/D,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4H,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;IAC7FL,KAAK,EAAEA;EACT,CAAC,EAAEM,MAAM,CAAC,EAAEqB,qBAAqB,CAAC,EAAE,CAAC,CAAC,EAAE;IACtCH,YAAY,EAAEA,YAAY;IAC1Bf,EAAE,EAAEc;EACN,CAAC,CAAC;EAEF,IAAIQ,WAAW,GAAG/I,GAAG,GAAG4H,CAAC,CAAC5H,GAAG,EAAEwD,aAAa,CAAC,GAAGgF,YAAY;EAC5D,IAAIvF,OAAO,GAAGI,WAAW,CAACmE,UAAU,IAAItG,QAAQ,EAAE6H,WAAW,EAAExF,IAAI,EAAE6E,mBAAmB,EAAE5E,aAAa,EAAEC,cAAc,CAAC;EACxH,IAAIuF,WAAW,GAAG/B,MAAM,KAAKpC,SAAS,GAAGoC,MAAM,GAAGmB,mBAAmB,CAACa,kBAAkB;EACxF,OAAOD,WAAW,GAAG5I,KAAK,CAACmG,aAAa,CAACyC,WAAW,EAAEnB,eAAe,EAAE5E,OAAO,CAAC,GAAGA,OAAO;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}