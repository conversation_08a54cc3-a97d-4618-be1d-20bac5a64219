function e(e,t){var n=Object(t).className,r=Object(t).attr||"blank",o=Object(t).force;try{if(e.querySelector(":blank"),!o)return}catch(e){}var a,i,c,s=(e.ownerDocument||e).defaultView;d(s.HTMLInputElement),d(s.HTMLSelectElement),d(s.HTMLTextAreaElement),a=s.HTMLOptionElement,i=Object.getOwnPropertyDescriptor(a.prototype,"selected"),c=i.set,i.set=function(t){c.apply(this,arguments);var n=e.createEvent("Event");n.initEvent("change",!0,!0),this.dispatchEvent(n)},Object.defineProperty(a.prototype,"selected",i);var l=/^(INPUT|SELECT|TEXTAREA)$/;function p(){this.value||"SELECT"===this.nodeName&&this.options[this.selectedIndex].value?(r&&this.removeAttribute(r),n&&this.classList.remove(n),this.removeAttribute("blank")):(r&&this.setAttribute("blank",r),n&&this.classList.add(n))}function d(e){var t=Object.getOwnPropertyDescriptor(e.prototype,"value"),n=t.set;t.set=function(e){n.apply(this,arguments),p.apply(this)},Object.defineProperty(e.prototype,"value",t)}Array.prototype.forEach.call(e.querySelectorAll("INPUT,SELECT,TEXTAREA"),(function(e){"SELECT"===e.nodeName?e.addEventListener("change",p):e.addEventListener("input",p),p.call(e)})),new MutationObserver((function(e){e.forEach((function(e){Array.prototype.forEach.call(e.addedNodes||[],(function(e){1===e.nodeType&&l.test(e.nodeName)&&("SELECT"===e.nodeName?e.addEventListener("change",p):e.addEventListener("input",p),p.call(e))})),Array.prototype.forEach.call(e.removedNodes||[],(function(e){1===e.nodeType&&l.test(e.nodeName)&&("SELECT"===e.nodeName?e.removeEventListener("change",p):e.removeEventListener("input",p))}))}))})).observe(e,{childList:!0,subtree:!0})}export{e as default};
//# sourceMappingURL=browser.mjs.map
