{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nvar _excluded = [\"name\", \"errors\"];\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport { HOOK_MARK } from './FieldContext';\nimport { allPromiseFinish } from './utils/asyncUtil';\nimport NameMap from './utils/NameMap';\nimport { defaultValidateMessages } from './utils/messages';\nimport { cloneByNamePathList, containsNamePath, getNamePath, getValue, matchNamePath, setValue, setValues } from './utils/valueUtil';\nimport cloneDeep from './utils/cloneDeep';\nexport var FormStore = /*#__PURE__*/_createClass(function FormStore(forceRootUpdate) {\n  var _this = this;\n  _classCallCheck(this, FormStore);\n  this.formHooked = false;\n  this.forceRootUpdate = void 0;\n  this.subscribable = true;\n  this.store = {};\n  this.fieldEntities = [];\n  this.initialValues = {};\n  this.callbacks = {};\n  this.validateMessages = null;\n  this.preserve = null;\n  this.lastValidatePromise = null;\n  this.getForm = function () {\n    return {\n      getFieldValue: _this.getFieldValue,\n      getFieldsValue: _this.getFieldsValue,\n      getFieldError: _this.getFieldError,\n      getFieldWarning: _this.getFieldWarning,\n      getFieldsError: _this.getFieldsError,\n      isFieldsTouched: _this.isFieldsTouched,\n      isFieldTouched: _this.isFieldTouched,\n      isFieldValidating: _this.isFieldValidating,\n      isFieldsValidating: _this.isFieldsValidating,\n      resetFields: _this.resetFields,\n      setFields: _this.setFields,\n      setFieldsValue: _this.setFieldsValue,\n      validateFields: _this.validateFields,\n      submit: _this.submit,\n      _init: true,\n      getInternalHooks: _this.getInternalHooks\n    };\n  };\n  this.getInternalHooks = function (key) {\n    if (key === HOOK_MARK) {\n      _this.formHooked = true;\n      return {\n        dispatch: _this.dispatch,\n        initEntityValue: _this.initEntityValue,\n        registerField: _this.registerField,\n        useSubscribe: _this.useSubscribe,\n        setInitialValues: _this.setInitialValues,\n        destroyForm: _this.destroyForm,\n        setCallbacks: _this.setCallbacks,\n        setValidateMessages: _this.setValidateMessages,\n        getFields: _this.getFields,\n        setPreserve: _this.setPreserve,\n        getInitialValue: _this.getInitialValue,\n        registerWatch: _this.registerWatch\n      };\n    }\n    warning(false, '`getInternalHooks` is internal usage. Should not call directly.');\n    return null;\n  };\n  this.useSubscribe = function (subscribable) {\n    _this.subscribable = subscribable;\n  };\n  this.prevWithoutPreserves = null;\n  this.setInitialValues = function (initialValues, init) {\n    _this.initialValues = initialValues || {};\n    if (init) {\n      var _this$prevWithoutPres;\n      var nextStore = setValues({}, initialValues, _this.store); // We will take consider prev form unmount fields.\n      // When the field is not `preserve`, we need fill this with initialValues instead of store.\n      // eslint-disable-next-line array-callback-return\n\n      (_this$prevWithoutPres = _this.prevWithoutPreserves) === null || _this$prevWithoutPres === void 0 ? void 0 : _this$prevWithoutPres.map(function (_ref) {\n        var namePath = _ref.key;\n        nextStore = setValue(nextStore, namePath, getValue(initialValues, namePath));\n      });\n      _this.prevWithoutPreserves = null;\n      _this.updateStore(nextStore);\n    }\n  };\n  this.destroyForm = function () {\n    var prevWithoutPreserves = new NameMap();\n    _this.getFieldEntities(true).forEach(function (entity) {\n      if (!entity.isPreserve()) {\n        prevWithoutPreserves.set(entity.getNamePath(), true);\n      }\n    });\n    _this.prevWithoutPreserves = prevWithoutPreserves;\n  };\n  this.getInitialValue = function (namePath) {\n    var initValue = getValue(_this.initialValues, namePath); // Not cloneDeep when without `namePath`\n\n    return namePath.length ? cloneDeep(initValue) : initValue;\n  };\n  this.setCallbacks = function (callbacks) {\n    _this.callbacks = callbacks;\n  };\n  this.setValidateMessages = function (validateMessages) {\n    _this.validateMessages = validateMessages;\n  };\n  this.setPreserve = function (preserve) {\n    _this.preserve = preserve;\n  };\n  this.watchList = [];\n  this.registerWatch = function (callback) {\n    _this.watchList.push(callback);\n    return function () {\n      _this.watchList = _this.watchList.filter(function (fn) {\n        return fn !== callback;\n      });\n    };\n  };\n  this.notifyWatch = function () {\n    var namePath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n\n    // No need to cost perf when nothing need to watch\n    if (_this.watchList.length) {\n      var values = _this.getFieldsValue();\n      _this.watchList.forEach(function (callback) {\n        callback(values, namePath);\n      });\n    }\n  };\n  this.timeoutId = null;\n  this.warningUnhooked = function () {\n    if (process.env.NODE_ENV !== 'production' && !_this.timeoutId && typeof window !== 'undefined') {\n      _this.timeoutId = setTimeout(function () {\n        _this.timeoutId = null;\n        if (!_this.formHooked) {\n          warning(false, 'Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');\n        }\n      });\n    }\n  };\n  this.updateStore = function (nextStore) {\n    _this.store = nextStore;\n  };\n  this.getFieldEntities = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!pure) {\n      return _this.fieldEntities;\n    }\n    return _this.fieldEntities.filter(function (field) {\n      return field.getNamePath().length;\n    });\n  };\n  this.getFieldsMap = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var cache = new NameMap();\n    _this.getFieldEntities(pure).forEach(function (field) {\n      var namePath = field.getNamePath();\n      cache.set(namePath, field);\n    });\n    return cache;\n  };\n  this.getFieldEntitiesForNamePathList = function (nameList) {\n    if (!nameList) {\n      return _this.getFieldEntities(true);\n    }\n    var cache = _this.getFieldsMap(true);\n    return nameList.map(function (name) {\n      var namePath = getNamePath(name);\n      return cache.get(namePath) || {\n        INVALIDATE_NAME_PATH: getNamePath(name)\n      };\n    });\n  };\n  this.getFieldsValue = function (nameList, filterFunc) {\n    _this.warningUnhooked();\n    if (nameList === true && !filterFunc) {\n      return _this.store;\n    }\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(Array.isArray(nameList) ? nameList : null);\n    var filteredNameList = [];\n    fieldEntities.forEach(function (entity) {\n      var _entity$isListField;\n      var namePath = 'INVALIDATE_NAME_PATH' in entity ? entity.INVALIDATE_NAME_PATH : entity.getNamePath(); // Ignore when it's a list item and not specific the namePath,\n      // since parent field is already take in count\n\n      if (!nameList && ((_entity$isListField = entity.isListField) === null || _entity$isListField === void 0 ? void 0 : _entity$isListField.call(entity))) {\n        return;\n      }\n      if (!filterFunc) {\n        filteredNameList.push(namePath);\n      } else {\n        var meta = 'getMeta' in entity ? entity.getMeta() : null;\n        if (filterFunc(meta)) {\n          filteredNameList.push(namePath);\n        }\n      }\n    });\n    return cloneByNamePathList(_this.store, filteredNameList.map(getNamePath));\n  };\n  this.getFieldValue = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    return getValue(_this.store, namePath);\n  };\n  this.getFieldsError = function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(nameList);\n    return fieldEntities.map(function (entity, index) {\n      if (entity && !('INVALIDATE_NAME_PATH' in entity)) {\n        return {\n          name: entity.getNamePath(),\n          errors: entity.getErrors(),\n          warnings: entity.getWarnings()\n        };\n      }\n      return {\n        name: getNamePath(nameList[index]),\n        errors: [],\n        warnings: []\n      };\n    });\n  };\n  this.getFieldError = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.errors;\n  };\n  this.getFieldWarning = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.warnings;\n  };\n  this.isFieldsTouched = function () {\n    _this.warningUnhooked();\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var arg0 = args[0],\n      arg1 = args[1];\n    var namePathList;\n    var isAllFieldsTouched = false;\n    if (args.length === 0) {\n      namePathList = null;\n    } else if (args.length === 1) {\n      if (Array.isArray(arg0)) {\n        namePathList = arg0.map(getNamePath);\n        isAllFieldsTouched = false;\n      } else {\n        namePathList = null;\n        isAllFieldsTouched = arg0;\n      }\n    } else {\n      namePathList = arg0.map(getNamePath);\n      isAllFieldsTouched = arg1;\n    }\n    var fieldEntities = _this.getFieldEntities(true);\n    var isFieldTouched = function isFieldTouched(field) {\n      return field.isFieldTouched();\n    }; // ===== Will get fully compare when not config namePathList =====\n\n    if (!namePathList) {\n      return isAllFieldsTouched ? fieldEntities.every(isFieldTouched) : fieldEntities.some(isFieldTouched);\n    } // Generate a nest tree for validate\n\n    var map = new NameMap();\n    namePathList.forEach(function (shortNamePath) {\n      map.set(shortNamePath, []);\n    });\n    fieldEntities.forEach(function (field) {\n      var fieldNamePath = field.getNamePath(); // Find matched entity and put into list\n\n      namePathList.forEach(function (shortNamePath) {\n        if (shortNamePath.every(function (nameUnit, i) {\n          return fieldNamePath[i] === nameUnit;\n        })) {\n          map.update(shortNamePath, function (list) {\n            return [].concat(_toConsumableArray(list), [field]);\n          });\n        }\n      });\n    }); // Check if NameMap value is touched\n\n    var isNamePathListTouched = function isNamePathListTouched(entities) {\n      return entities.some(isFieldTouched);\n    };\n    var namePathListEntities = map.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    return isAllFieldsTouched ? namePathListEntities.every(isNamePathListTouched) : namePathListEntities.some(isNamePathListTouched);\n  };\n  this.isFieldTouched = function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsTouched([name]);\n  };\n  this.isFieldsValidating = function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntities();\n    if (!nameList) {\n      return fieldEntities.some(function (testField) {\n        return testField.isFieldValidating();\n      });\n    }\n    var namePathList = nameList.map(getNamePath);\n    return fieldEntities.some(function (testField) {\n      var fieldNamePath = testField.getNamePath();\n      return containsNamePath(namePathList, fieldNamePath) && testField.isFieldValidating();\n    });\n  };\n  this.isFieldValidating = function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsValidating([name]);\n  };\n  this.resetWithFieldInitialValue = function () {\n    var info = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // Create cache\n    var cache = new NameMap();\n    var fieldEntities = _this.getFieldEntities(true);\n    fieldEntities.forEach(function (field) {\n      var initialValue = field.props.initialValue;\n      var namePath = field.getNamePath(); // Record only if has `initialValue`\n\n      if (initialValue !== undefined) {\n        var records = cache.get(namePath) || new Set();\n        records.add({\n          entity: field,\n          value: initialValue\n        });\n        cache.set(namePath, records);\n      }\n    }); // Reset\n\n    var resetWithFields = function resetWithFields(entities) {\n      entities.forEach(function (field) {\n        var initialValue = field.props.initialValue;\n        if (initialValue !== undefined) {\n          var namePath = field.getNamePath();\n          var formInitialValue = _this.getInitialValue(namePath);\n          if (formInitialValue !== undefined) {\n            // Warning if conflict with form initialValues and do not modify value\n            warning(false, \"Form already set 'initialValues' with path '\".concat(namePath.join('.'), \"'. Field can not overwrite it.\"));\n          } else {\n            var records = cache.get(namePath);\n            if (records && records.size > 1) {\n              // Warning if multiple field set `initialValue`and do not modify value\n              warning(false, \"Multiple Field with path '\".concat(namePath.join('.'), \"' set 'initialValue'. Can not decide which one to pick.\"));\n            } else if (records) {\n              var originValue = _this.getFieldValue(namePath); // Set `initialValue`\n\n              if (!info.skipExist || originValue === undefined) {\n                _this.updateStore(setValue(_this.store, namePath, _toConsumableArray(records)[0].value));\n              }\n            }\n          }\n        }\n      });\n    };\n    var requiredFieldEntities;\n    if (info.entities) {\n      requiredFieldEntities = info.entities;\n    } else if (info.namePathList) {\n      requiredFieldEntities = [];\n      info.namePathList.forEach(function (namePath) {\n        var records = cache.get(namePath);\n        if (records) {\n          var _requiredFieldEntitie;\n          (_requiredFieldEntitie = requiredFieldEntities).push.apply(_requiredFieldEntitie, _toConsumableArray(_toConsumableArray(records).map(function (r) {\n            return r.entity;\n          })));\n        }\n      });\n    } else {\n      requiredFieldEntities = fieldEntities;\n    }\n    resetWithFields(requiredFieldEntities);\n  };\n  this.resetFields = function (nameList) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (!nameList) {\n      _this.updateStore(setValues({}, _this.initialValues));\n      _this.resetWithFieldInitialValue();\n      _this.notifyObservers(prevStore, null, {\n        type: 'reset'\n      });\n      _this.notifyWatch();\n      return;\n    } // Reset by `nameList`\n\n    var namePathList = nameList.map(getNamePath);\n    namePathList.forEach(function (namePath) {\n      var initialValue = _this.getInitialValue(namePath);\n      _this.updateStore(setValue(_this.store, namePath, initialValue));\n    });\n    _this.resetWithFieldInitialValue({\n      namePathList: namePathList\n    });\n    _this.notifyObservers(prevStore, namePathList, {\n      type: 'reset'\n    });\n    _this.notifyWatch(namePathList);\n  };\n  this.setFields = function (fields) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    var namePathList = [];\n    fields.forEach(function (fieldData) {\n      var name = fieldData.name,\n        errors = fieldData.errors,\n        data = _objectWithoutProperties(fieldData, _excluded);\n      var namePath = getNamePath(name);\n      namePathList.push(namePath); // Value\n\n      if ('value' in data) {\n        _this.updateStore(setValue(_this.store, namePath, data.value));\n      }\n      _this.notifyObservers(prevStore, [namePath], {\n        type: 'setField',\n        data: fieldData\n      });\n    });\n    _this.notifyWatch(namePathList);\n  };\n  this.getFields = function () {\n    var entities = _this.getFieldEntities(true);\n    var fields = entities.map(function (field) {\n      var namePath = field.getNamePath();\n      var meta = field.getMeta();\n      var fieldData = _objectSpread(_objectSpread({}, meta), {}, {\n        name: namePath,\n        value: _this.getFieldValue(namePath)\n      });\n      Object.defineProperty(fieldData, 'originRCField', {\n        value: true\n      });\n      return fieldData;\n    });\n    return fields;\n  };\n  this.initEntityValue = function (entity) {\n    var initialValue = entity.props.initialValue;\n    if (initialValue !== undefined) {\n      var namePath = entity.getNamePath();\n      var prevValue = getValue(_this.store, namePath);\n      if (prevValue === undefined) {\n        _this.updateStore(setValue(_this.store, namePath, initialValue));\n      }\n    }\n  };\n  this.registerField = function (entity) {\n    _this.fieldEntities.push(entity);\n    var namePath = entity.getNamePath();\n    _this.notifyWatch([namePath]); // Set initial values\n\n    if (entity.props.initialValue !== undefined) {\n      var prevStore = _this.store;\n      _this.resetWithFieldInitialValue({\n        entities: [entity],\n        skipExist: true\n      });\n      _this.notifyObservers(prevStore, [entity.getNamePath()], {\n        type: 'valueUpdate',\n        source: 'internal'\n      });\n    } // un-register field callback\n\n    return function (isListField, preserve) {\n      var subNamePath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      _this.fieldEntities = _this.fieldEntities.filter(function (item) {\n        return item !== entity;\n      }); // Clean up store value if not preserve\n\n      var mergedPreserve = preserve !== undefined ? preserve : _this.preserve;\n      if (mergedPreserve === false && (!isListField || subNamePath.length > 1)) {\n        var defaultValue = isListField ? undefined : _this.getInitialValue(namePath);\n        if (namePath.length && _this.getFieldValue(namePath) !== defaultValue && _this.fieldEntities.every(function (field) {\n          return (\n            // Only reset when no namePath exist\n            !matchNamePath(field.getNamePath(), namePath)\n          );\n        })) {\n          var _prevStore = _this.store;\n          _this.updateStore(setValue(_prevStore, namePath, defaultValue, true)); // Notify that field is unmount\n\n          _this.notifyObservers(_prevStore, [namePath], {\n            type: 'remove'\n          }); // Dependencies update\n\n          _this.triggerDependenciesUpdate(_prevStore, namePath);\n        }\n      }\n      _this.notifyWatch([namePath]);\n    };\n  };\n  this.dispatch = function (action) {\n    switch (action.type) {\n      case 'updateValue':\n        {\n          var namePath = action.namePath,\n            value = action.value;\n          _this.updateValue(namePath, value);\n          break;\n        }\n      case 'validateField':\n        {\n          var _namePath = action.namePath,\n            triggerName = action.triggerName;\n          _this.validateFields([_namePath], {\n            triggerName: triggerName\n          });\n          break;\n        }\n      default: // Currently we don't have other action. Do nothing.\n    }\n  };\n  this.notifyObservers = function (prevStore, namePathList, info) {\n    if (_this.subscribable) {\n      var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n        store: _this.getFieldsValue(true)\n      });\n      _this.getFieldEntities().forEach(function (_ref3) {\n        var onStoreChange = _ref3.onStoreChange;\n        onStoreChange(prevStore, namePathList, mergedInfo);\n      });\n    } else {\n      _this.forceRootUpdate();\n    }\n  };\n  this.triggerDependenciesUpdate = function (prevStore, namePath) {\n    var childrenFields = _this.getDependencyChildrenFields(namePath);\n    if (childrenFields.length) {\n      _this.validateFields(childrenFields);\n    }\n    _this.notifyObservers(prevStore, childrenFields, {\n      type: 'dependenciesUpdate',\n      relatedFields: [namePath].concat(_toConsumableArray(childrenFields))\n    });\n    return childrenFields;\n  };\n  this.updateValue = function (name, value) {\n    var namePath = getNamePath(name);\n    var prevStore = _this.store;\n    _this.updateStore(setValue(_this.store, namePath, value));\n    _this.notifyObservers(prevStore, [namePath], {\n      type: 'valueUpdate',\n      source: 'internal'\n    });\n    _this.notifyWatch([namePath]); // Dependencies update\n\n    var childrenFields = _this.triggerDependenciesUpdate(prevStore, namePath); // trigger callback function\n\n    var onValuesChange = _this.callbacks.onValuesChange;\n    if (onValuesChange) {\n      var changedValues = cloneByNamePathList(_this.store, [namePath]);\n      onValuesChange(changedValues, _this.getFieldsValue());\n    }\n    _this.triggerOnFieldsChange([namePath].concat(_toConsumableArray(childrenFields)));\n  };\n  this.setFieldsValue = function (store) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (store) {\n      var nextStore = setValues(_this.store, store);\n      _this.updateStore(nextStore);\n    }\n    _this.notifyObservers(prevStore, null, {\n      type: 'valueUpdate',\n      source: 'external'\n    });\n    _this.notifyWatch();\n  };\n  this.getDependencyChildrenFields = function (rootNamePath) {\n    var children = new Set();\n    var childrenFields = [];\n    var dependencies2fields = new NameMap();\n    /**\n     * Generate maps\n     * Can use cache to save perf if user report performance issue with this\n     */\n\n    _this.getFieldEntities().forEach(function (field) {\n      var dependencies = field.props.dependencies;\n      (dependencies || []).forEach(function (dependency) {\n        var dependencyNamePath = getNamePath(dependency);\n        dependencies2fields.update(dependencyNamePath, function () {\n          var fields = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Set();\n          fields.add(field);\n          return fields;\n        });\n      });\n    });\n    var fillChildren = function fillChildren(namePath) {\n      var fields = dependencies2fields.get(namePath) || new Set();\n      fields.forEach(function (field) {\n        if (!children.has(field)) {\n          children.add(field);\n          var fieldNamePath = field.getNamePath();\n          if (field.isFieldDirty() && fieldNamePath.length) {\n            childrenFields.push(fieldNamePath);\n            fillChildren(fieldNamePath);\n          }\n        }\n      });\n    };\n    fillChildren(rootNamePath);\n    return childrenFields;\n  };\n  this.triggerOnFieldsChange = function (namePathList, filedErrors) {\n    var onFieldsChange = _this.callbacks.onFieldsChange;\n    if (onFieldsChange) {\n      var fields = _this.getFields();\n      /**\n       * Fill errors since `fields` may be replaced by controlled fields\n       */\n\n      if (filedErrors) {\n        var cache = new NameMap();\n        filedErrors.forEach(function (_ref4) {\n          var name = _ref4.name,\n            errors = _ref4.errors;\n          cache.set(name, errors);\n        });\n        fields.forEach(function (field) {\n          // eslint-disable-next-line no-param-reassign\n          field.errors = cache.get(field.name) || field.errors;\n        });\n      }\n      var changedFields = fields.filter(function (_ref5) {\n        var fieldName = _ref5.name;\n        return containsNamePath(namePathList, fieldName);\n      });\n      onFieldsChange(changedFields, fields);\n    }\n  };\n  this.validateFields = function (nameList, options) {\n    _this.warningUnhooked();\n    var provideNameList = !!nameList;\n    var namePathList = provideNameList ? nameList.map(getNamePath) : []; // Collect result in promise list\n\n    var promiseList = [];\n    _this.getFieldEntities(true).forEach(function (field) {\n      // Add field if not provide `nameList`\n      if (!provideNameList) {\n        namePathList.push(field.getNamePath());\n      }\n      /**\n       * Recursive validate if configured.\n       * TODO: perf improvement @zombieJ\n       */\n\n      if ((options === null || options === void 0 ? void 0 : options.recursive) && provideNameList) {\n        var namePath = field.getNamePath();\n        if (\n        // nameList[i] === undefined 说明是以 nameList 开头的\n        // ['name'] -> ['name','list']\n        namePath.every(function (nameUnit, i) {\n          return nameList[i] === nameUnit || nameList[i] === undefined;\n        })) {\n          namePathList.push(namePath);\n        }\n      } // Skip if without rule\n\n      if (!field.props.rules || !field.props.rules.length) {\n        return;\n      }\n      var fieldNamePath = field.getNamePath(); // Add field validate rule in to promise list\n\n      if (!provideNameList || containsNamePath(namePathList, fieldNamePath)) {\n        var promise = field.validateRules(_objectSpread({\n          validateMessages: _objectSpread(_objectSpread({}, defaultValidateMessages), _this.validateMessages)\n        }, options)); // Wrap promise with field\n\n        promiseList.push(promise.then(function () {\n          return {\n            name: fieldNamePath,\n            errors: [],\n            warnings: []\n          };\n        }).catch(function (ruleErrors) {\n          var mergedErrors = [];\n          var mergedWarnings = [];\n          ruleErrors.forEach(function (_ref6) {\n            var warningOnly = _ref6.rule.warningOnly,\n              errors = _ref6.errors;\n            if (warningOnly) {\n              mergedWarnings.push.apply(mergedWarnings, _toConsumableArray(errors));\n            } else {\n              mergedErrors.push.apply(mergedErrors, _toConsumableArray(errors));\n            }\n          });\n          if (mergedErrors.length) {\n            return Promise.reject({\n              name: fieldNamePath,\n              errors: mergedErrors,\n              warnings: mergedWarnings\n            });\n          }\n          return {\n            name: fieldNamePath,\n            errors: mergedErrors,\n            warnings: mergedWarnings\n          };\n        }));\n      }\n    });\n    var summaryPromise = allPromiseFinish(promiseList);\n    _this.lastValidatePromise = summaryPromise; // Notify fields with rule that validate has finished and need update\n\n    summaryPromise.catch(function (results) {\n      return results;\n    }).then(function (results) {\n      var resultNamePathList = results.map(function (_ref7) {\n        var name = _ref7.name;\n        return name;\n      });\n      _this.notifyObservers(_this.store, resultNamePathList, {\n        type: 'validateFinish'\n      });\n      _this.triggerOnFieldsChange(resultNamePathList, results);\n    });\n    var returnPromise = summaryPromise.then(function () {\n      if (_this.lastValidatePromise === summaryPromise) {\n        return Promise.resolve(_this.getFieldsValue(namePathList));\n      }\n      return Promise.reject([]);\n    }).catch(function (results) {\n      var errorList = results.filter(function (result) {\n        return result && result.errors.length;\n      });\n      return Promise.reject({\n        values: _this.getFieldsValue(namePathList),\n        errorFields: errorList,\n        outOfDate: _this.lastValidatePromise !== summaryPromise\n      });\n    }); // Do not throw in console\n\n    returnPromise.catch(function (e) {\n      return e;\n    });\n    return returnPromise;\n  };\n  this.submit = function () {\n    _this.warningUnhooked();\n    _this.validateFields().then(function (values) {\n      var onFinish = _this.callbacks.onFinish;\n      if (onFinish) {\n        try {\n          onFinish(values);\n        } catch (err) {\n          // Should print error if user `onFinish` callback failed\n          console.error(err);\n        }\n      }\n    }).catch(function (e) {\n      var onFinishFailed = _this.callbacks.onFinishFailed;\n      if (onFinishFailed) {\n        onFinishFailed(e);\n      }\n    });\n  };\n  this.forceRootUpdate = forceRootUpdate;\n});\nfunction useForm(form) {\n  var formRef = React.useRef();\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  if (!formRef.current) {\n    if (form) {\n      formRef.current = form;\n    } else {\n      // Create a new FormStore if not provided\n      var forceReRender = function forceReRender() {\n        forceUpdate({});\n      };\n      var formStore = new FormStore(forceReRender);\n      formRef.current = formStore.getForm();\n    }\n  }\n  return [formRef.current];\n}\nexport default useForm;", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "_objectWithoutProperties", "_toConsumableArray", "_createClass", "_classCallCheck", "_excluded", "React", "warning", "HOOK_MARK", "allPromiseFinish", "NameMap", "defaultValidateMessages", "cloneByNamePathList", "containsNamePath", "getNamePath", "getValue", "matchNamePath", "setValue", "set<PERSON><PERSON><PERSON>", "cloneDeep", "FormStore", "forceRootUpdate", "_this", "formHooked", "subscribable", "store", "fieldEntities", "initialValues", "callbacks", "validateMessages", "preserve", "lastValidatePromise", "getForm", "getFieldValue", "getFieldsValue", "getFieldError", "getFieldWarning", "getFieldsError", "isFieldsTouched", "isFieldTouched", "isFieldValidating", "isFieldsValidating", "resetFields", "setFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "submit", "_init", "getInternalHooks", "key", "dispatch", "initEntityValue", "registerField", "useSubscribe", "setInitialValues", "destroyForm", "setCallbacks", "setValidateMessages", "getFields", "setPreserve", "getInitialValue", "registerWatch", "prevWithoutPreserves", "init", "_this$prevWithoutPres", "nextStore", "map", "_ref", "namePath", "updateStore", "getFieldEntities", "for<PERSON>ach", "entity", "isPreserve", "set", "initValue", "length", "watchList", "callback", "push", "filter", "fn", "notifyWatch", "arguments", "undefined", "values", "timeoutId", "warningUnhooked", "process", "env", "NODE_ENV", "window", "setTimeout", "pure", "field", "getFieldsMap", "cache", "getFieldEntitiesForNamePathList", "nameList", "name", "get", "INVALIDATE_NAME_PATH", "filterFunc", "Array", "isArray", "filteredNameList", "_entity$isListField", "isListField", "call", "meta", "getMeta", "index", "errors", "getErrors", "warnings", "getWarnings", "fieldError", "_len", "args", "_key", "arg0", "arg1", "namePathList", "isAllFieldsTouched", "every", "some", "shortNamePath", "fieldNamePath", "nameUnit", "i", "update", "list", "concat", "isNamePathListTouched", "entities", "namePathListEntities", "_ref2", "value", "testField", "resetWithFieldInitialValue", "info", "initialValue", "props", "records", "Set", "add", "reset<PERSON><PERSON><PERSON><PERSON>s", "formInitialValue", "join", "size", "originValue", "skipExist", "requiredFieldEntities", "_requiredF<PERSON><PERSON><PERSON><PERSON>", "apply", "r", "prevStore", "notifyObservers", "type", "fields", "fieldData", "data", "Object", "defineProperty", "prevValue", "source", "subNamePath", "item", "mergedPreserve", "defaultValue", "_prevStore", "triggerDependenciesUpdate", "action", "updateValue", "_namePath", "triggerName", "mergedInfo", "_ref3", "onStoreChange", "childrenFields", "getDep<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "relatedFields", "onValuesChange", "changedValues", "trigger<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootNamePath", "children", "dependencies2fields", "dependencies", "dependency", "dependencyNamePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filedErrors", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "changed<PERSON>ields", "_ref5", "fieldName", "options", "provideNameList", "promiseList", "recursive", "rules", "promise", "validateRules", "then", "catch", "ruleErrors", "mergedErrors", "mergedWarnings", "_ref6", "warningOnly", "rule", "Promise", "reject", "summaryPromise", "results", "resultNamePathList", "_ref7", "returnPromise", "resolve", "errorList", "result", "errorFields", "outOfDate", "e", "onFinish", "err", "console", "error", "onFinishFailed", "useForm", "form", "formRef", "useRef", "_React$useState", "useState", "_React$useState2", "forceUpdate", "current", "forceReRender", "formStore"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/useForm.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nvar _excluded = [\"name\", \"errors\"];\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport { HOOK_MARK } from './FieldContext';\nimport { allPromiseFinish } from './utils/asyncUtil';\nimport NameMap from './utils/NameMap';\nimport { defaultValidateMessages } from './utils/messages';\nimport { cloneByNamePathList, containsNamePath, getNamePath, getValue, matchNamePath, setValue, setValues } from './utils/valueUtil';\nimport cloneDeep from './utils/cloneDeep';\nexport var FormStore = /*#__PURE__*/_createClass(function FormStore(forceRootUpdate) {\n  var _this = this;\n\n  _classCallCheck(this, FormStore);\n\n  this.formHooked = false;\n  this.forceRootUpdate = void 0;\n  this.subscribable = true;\n  this.store = {};\n  this.fieldEntities = [];\n  this.initialValues = {};\n  this.callbacks = {};\n  this.validateMessages = null;\n  this.preserve = null;\n  this.lastValidatePromise = null;\n\n  this.getForm = function () {\n    return {\n      getFieldValue: _this.getFieldValue,\n      getFieldsValue: _this.getFieldsValue,\n      getFieldError: _this.getFieldError,\n      getFieldWarning: _this.getFieldWarning,\n      getFieldsError: _this.getFieldsError,\n      isFieldsTouched: _this.isFieldsTouched,\n      isFieldTouched: _this.isFieldTouched,\n      isFieldValidating: _this.isFieldValidating,\n      isFieldsValidating: _this.isFieldsValidating,\n      resetFields: _this.resetFields,\n      setFields: _this.setFields,\n      setFieldsValue: _this.setFieldsValue,\n      validateFields: _this.validateFields,\n      submit: _this.submit,\n      _init: true,\n      getInternalHooks: _this.getInternalHooks\n    };\n  };\n\n  this.getInternalHooks = function (key) {\n    if (key === HOOK_MARK) {\n      _this.formHooked = true;\n      return {\n        dispatch: _this.dispatch,\n        initEntityValue: _this.initEntityValue,\n        registerField: _this.registerField,\n        useSubscribe: _this.useSubscribe,\n        setInitialValues: _this.setInitialValues,\n        destroyForm: _this.destroyForm,\n        setCallbacks: _this.setCallbacks,\n        setValidateMessages: _this.setValidateMessages,\n        getFields: _this.getFields,\n        setPreserve: _this.setPreserve,\n        getInitialValue: _this.getInitialValue,\n        registerWatch: _this.registerWatch\n      };\n    }\n\n    warning(false, '`getInternalHooks` is internal usage. Should not call directly.');\n    return null;\n  };\n\n  this.useSubscribe = function (subscribable) {\n    _this.subscribable = subscribable;\n  };\n\n  this.prevWithoutPreserves = null;\n\n  this.setInitialValues = function (initialValues, init) {\n    _this.initialValues = initialValues || {};\n\n    if (init) {\n      var _this$prevWithoutPres;\n\n      var nextStore = setValues({}, initialValues, _this.store); // We will take consider prev form unmount fields.\n      // When the field is not `preserve`, we need fill this with initialValues instead of store.\n      // eslint-disable-next-line array-callback-return\n\n      (_this$prevWithoutPres = _this.prevWithoutPreserves) === null || _this$prevWithoutPres === void 0 ? void 0 : _this$prevWithoutPres.map(function (_ref) {\n        var namePath = _ref.key;\n        nextStore = setValue(nextStore, namePath, getValue(initialValues, namePath));\n      });\n      _this.prevWithoutPreserves = null;\n\n      _this.updateStore(nextStore);\n    }\n  };\n\n  this.destroyForm = function () {\n    var prevWithoutPreserves = new NameMap();\n\n    _this.getFieldEntities(true).forEach(function (entity) {\n      if (!entity.isPreserve()) {\n        prevWithoutPreserves.set(entity.getNamePath(), true);\n      }\n    });\n\n    _this.prevWithoutPreserves = prevWithoutPreserves;\n  };\n\n  this.getInitialValue = function (namePath) {\n    var initValue = getValue(_this.initialValues, namePath); // Not cloneDeep when without `namePath`\n\n    return namePath.length ? cloneDeep(initValue) : initValue;\n  };\n\n  this.setCallbacks = function (callbacks) {\n    _this.callbacks = callbacks;\n  };\n\n  this.setValidateMessages = function (validateMessages) {\n    _this.validateMessages = validateMessages;\n  };\n\n  this.setPreserve = function (preserve) {\n    _this.preserve = preserve;\n  };\n\n  this.watchList = [];\n\n  this.registerWatch = function (callback) {\n    _this.watchList.push(callback);\n\n    return function () {\n      _this.watchList = _this.watchList.filter(function (fn) {\n        return fn !== callback;\n      });\n    };\n  };\n\n  this.notifyWatch = function () {\n    var namePath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n\n    // No need to cost perf when nothing need to watch\n    if (_this.watchList.length) {\n      var values = _this.getFieldsValue();\n\n      _this.watchList.forEach(function (callback) {\n        callback(values, namePath);\n      });\n    }\n  };\n\n  this.timeoutId = null;\n\n  this.warningUnhooked = function () {\n    if (process.env.NODE_ENV !== 'production' && !_this.timeoutId && typeof window !== 'undefined') {\n      _this.timeoutId = setTimeout(function () {\n        _this.timeoutId = null;\n\n        if (!_this.formHooked) {\n          warning(false, 'Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');\n        }\n      });\n    }\n  };\n\n  this.updateStore = function (nextStore) {\n    _this.store = nextStore;\n  };\n\n  this.getFieldEntities = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n    if (!pure) {\n      return _this.fieldEntities;\n    }\n\n    return _this.fieldEntities.filter(function (field) {\n      return field.getNamePath().length;\n    });\n  };\n\n  this.getFieldsMap = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var cache = new NameMap();\n\n    _this.getFieldEntities(pure).forEach(function (field) {\n      var namePath = field.getNamePath();\n      cache.set(namePath, field);\n    });\n\n    return cache;\n  };\n\n  this.getFieldEntitiesForNamePathList = function (nameList) {\n    if (!nameList) {\n      return _this.getFieldEntities(true);\n    }\n\n    var cache = _this.getFieldsMap(true);\n\n    return nameList.map(function (name) {\n      var namePath = getNamePath(name);\n      return cache.get(namePath) || {\n        INVALIDATE_NAME_PATH: getNamePath(name)\n      };\n    });\n  };\n\n  this.getFieldsValue = function (nameList, filterFunc) {\n    _this.warningUnhooked();\n\n    if (nameList === true && !filterFunc) {\n      return _this.store;\n    }\n\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(Array.isArray(nameList) ? nameList : null);\n\n    var filteredNameList = [];\n    fieldEntities.forEach(function (entity) {\n      var _entity$isListField;\n\n      var namePath = 'INVALIDATE_NAME_PATH' in entity ? entity.INVALIDATE_NAME_PATH : entity.getNamePath(); // Ignore when it's a list item and not specific the namePath,\n      // since parent field is already take in count\n\n      if (!nameList && ((_entity$isListField = entity.isListField) === null || _entity$isListField === void 0 ? void 0 : _entity$isListField.call(entity))) {\n        return;\n      }\n\n      if (!filterFunc) {\n        filteredNameList.push(namePath);\n      } else {\n        var meta = 'getMeta' in entity ? entity.getMeta() : null;\n\n        if (filterFunc(meta)) {\n          filteredNameList.push(namePath);\n        }\n      }\n    });\n    return cloneByNamePathList(_this.store, filteredNameList.map(getNamePath));\n  };\n\n  this.getFieldValue = function (name) {\n    _this.warningUnhooked();\n\n    var namePath = getNamePath(name);\n    return getValue(_this.store, namePath);\n  };\n\n  this.getFieldsError = function (nameList) {\n    _this.warningUnhooked();\n\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(nameList);\n\n    return fieldEntities.map(function (entity, index) {\n      if (entity && !('INVALIDATE_NAME_PATH' in entity)) {\n        return {\n          name: entity.getNamePath(),\n          errors: entity.getErrors(),\n          warnings: entity.getWarnings()\n        };\n      }\n\n      return {\n        name: getNamePath(nameList[index]),\n        errors: [],\n        warnings: []\n      };\n    });\n  };\n\n  this.getFieldError = function (name) {\n    _this.warningUnhooked();\n\n    var namePath = getNamePath(name);\n\n    var fieldError = _this.getFieldsError([namePath])[0];\n\n    return fieldError.errors;\n  };\n\n  this.getFieldWarning = function (name) {\n    _this.warningUnhooked();\n\n    var namePath = getNamePath(name);\n\n    var fieldError = _this.getFieldsError([namePath])[0];\n\n    return fieldError.warnings;\n  };\n\n  this.isFieldsTouched = function () {\n    _this.warningUnhooked();\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    var arg0 = args[0],\n        arg1 = args[1];\n    var namePathList;\n    var isAllFieldsTouched = false;\n\n    if (args.length === 0) {\n      namePathList = null;\n    } else if (args.length === 1) {\n      if (Array.isArray(arg0)) {\n        namePathList = arg0.map(getNamePath);\n        isAllFieldsTouched = false;\n      } else {\n        namePathList = null;\n        isAllFieldsTouched = arg0;\n      }\n    } else {\n      namePathList = arg0.map(getNamePath);\n      isAllFieldsTouched = arg1;\n    }\n\n    var fieldEntities = _this.getFieldEntities(true);\n\n    var isFieldTouched = function isFieldTouched(field) {\n      return field.isFieldTouched();\n    }; // ===== Will get fully compare when not config namePathList =====\n\n\n    if (!namePathList) {\n      return isAllFieldsTouched ? fieldEntities.every(isFieldTouched) : fieldEntities.some(isFieldTouched);\n    } // Generate a nest tree for validate\n\n\n    var map = new NameMap();\n    namePathList.forEach(function (shortNamePath) {\n      map.set(shortNamePath, []);\n    });\n    fieldEntities.forEach(function (field) {\n      var fieldNamePath = field.getNamePath(); // Find matched entity and put into list\n\n      namePathList.forEach(function (shortNamePath) {\n        if (shortNamePath.every(function (nameUnit, i) {\n          return fieldNamePath[i] === nameUnit;\n        })) {\n          map.update(shortNamePath, function (list) {\n            return [].concat(_toConsumableArray(list), [field]);\n          });\n        }\n      });\n    }); // Check if NameMap value is touched\n\n    var isNamePathListTouched = function isNamePathListTouched(entities) {\n      return entities.some(isFieldTouched);\n    };\n\n    var namePathListEntities = map.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    return isAllFieldsTouched ? namePathListEntities.every(isNamePathListTouched) : namePathListEntities.some(isNamePathListTouched);\n  };\n\n  this.isFieldTouched = function (name) {\n    _this.warningUnhooked();\n\n    return _this.isFieldsTouched([name]);\n  };\n\n  this.isFieldsValidating = function (nameList) {\n    _this.warningUnhooked();\n\n    var fieldEntities = _this.getFieldEntities();\n\n    if (!nameList) {\n      return fieldEntities.some(function (testField) {\n        return testField.isFieldValidating();\n      });\n    }\n\n    var namePathList = nameList.map(getNamePath);\n    return fieldEntities.some(function (testField) {\n      var fieldNamePath = testField.getNamePath();\n      return containsNamePath(namePathList, fieldNamePath) && testField.isFieldValidating();\n    });\n  };\n\n  this.isFieldValidating = function (name) {\n    _this.warningUnhooked();\n\n    return _this.isFieldsValidating([name]);\n  };\n\n  this.resetWithFieldInitialValue = function () {\n    var info = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // Create cache\n    var cache = new NameMap();\n\n    var fieldEntities = _this.getFieldEntities(true);\n\n    fieldEntities.forEach(function (field) {\n      var initialValue = field.props.initialValue;\n      var namePath = field.getNamePath(); // Record only if has `initialValue`\n\n      if (initialValue !== undefined) {\n        var records = cache.get(namePath) || new Set();\n        records.add({\n          entity: field,\n          value: initialValue\n        });\n        cache.set(namePath, records);\n      }\n    }); // Reset\n\n    var resetWithFields = function resetWithFields(entities) {\n      entities.forEach(function (field) {\n        var initialValue = field.props.initialValue;\n\n        if (initialValue !== undefined) {\n          var namePath = field.getNamePath();\n\n          var formInitialValue = _this.getInitialValue(namePath);\n\n          if (formInitialValue !== undefined) {\n            // Warning if conflict with form initialValues and do not modify value\n            warning(false, \"Form already set 'initialValues' with path '\".concat(namePath.join('.'), \"'. Field can not overwrite it.\"));\n          } else {\n            var records = cache.get(namePath);\n\n            if (records && records.size > 1) {\n              // Warning if multiple field set `initialValue`and do not modify value\n              warning(false, \"Multiple Field with path '\".concat(namePath.join('.'), \"' set 'initialValue'. Can not decide which one to pick.\"));\n            } else if (records) {\n              var originValue = _this.getFieldValue(namePath); // Set `initialValue`\n\n\n              if (!info.skipExist || originValue === undefined) {\n                _this.updateStore(setValue(_this.store, namePath, _toConsumableArray(records)[0].value));\n              }\n            }\n          }\n        }\n      });\n    };\n\n    var requiredFieldEntities;\n\n    if (info.entities) {\n      requiredFieldEntities = info.entities;\n    } else if (info.namePathList) {\n      requiredFieldEntities = [];\n      info.namePathList.forEach(function (namePath) {\n        var records = cache.get(namePath);\n\n        if (records) {\n          var _requiredFieldEntitie;\n\n          (_requiredFieldEntitie = requiredFieldEntities).push.apply(_requiredFieldEntitie, _toConsumableArray(_toConsumableArray(records).map(function (r) {\n            return r.entity;\n          })));\n        }\n      });\n    } else {\n      requiredFieldEntities = fieldEntities;\n    }\n\n    resetWithFields(requiredFieldEntities);\n  };\n\n  this.resetFields = function (nameList) {\n    _this.warningUnhooked();\n\n    var prevStore = _this.store;\n\n    if (!nameList) {\n      _this.updateStore(setValues({}, _this.initialValues));\n\n      _this.resetWithFieldInitialValue();\n\n      _this.notifyObservers(prevStore, null, {\n        type: 'reset'\n      });\n\n      _this.notifyWatch();\n\n      return;\n    } // Reset by `nameList`\n\n\n    var namePathList = nameList.map(getNamePath);\n    namePathList.forEach(function (namePath) {\n      var initialValue = _this.getInitialValue(namePath);\n\n      _this.updateStore(setValue(_this.store, namePath, initialValue));\n    });\n\n    _this.resetWithFieldInitialValue({\n      namePathList: namePathList\n    });\n\n    _this.notifyObservers(prevStore, namePathList, {\n      type: 'reset'\n    });\n\n    _this.notifyWatch(namePathList);\n  };\n\n  this.setFields = function (fields) {\n    _this.warningUnhooked();\n\n    var prevStore = _this.store;\n    var namePathList = [];\n    fields.forEach(function (fieldData) {\n      var name = fieldData.name,\n          errors = fieldData.errors,\n          data = _objectWithoutProperties(fieldData, _excluded);\n\n      var namePath = getNamePath(name);\n      namePathList.push(namePath); // Value\n\n      if ('value' in data) {\n        _this.updateStore(setValue(_this.store, namePath, data.value));\n      }\n\n      _this.notifyObservers(prevStore, [namePath], {\n        type: 'setField',\n        data: fieldData\n      });\n    });\n\n    _this.notifyWatch(namePathList);\n  };\n\n  this.getFields = function () {\n    var entities = _this.getFieldEntities(true);\n\n    var fields = entities.map(function (field) {\n      var namePath = field.getNamePath();\n      var meta = field.getMeta();\n\n      var fieldData = _objectSpread(_objectSpread({}, meta), {}, {\n        name: namePath,\n        value: _this.getFieldValue(namePath)\n      });\n\n      Object.defineProperty(fieldData, 'originRCField', {\n        value: true\n      });\n      return fieldData;\n    });\n    return fields;\n  };\n\n  this.initEntityValue = function (entity) {\n    var initialValue = entity.props.initialValue;\n\n    if (initialValue !== undefined) {\n      var namePath = entity.getNamePath();\n      var prevValue = getValue(_this.store, namePath);\n\n      if (prevValue === undefined) {\n        _this.updateStore(setValue(_this.store, namePath, initialValue));\n      }\n    }\n  };\n\n  this.registerField = function (entity) {\n    _this.fieldEntities.push(entity);\n\n    var namePath = entity.getNamePath();\n\n    _this.notifyWatch([namePath]); // Set initial values\n\n\n    if (entity.props.initialValue !== undefined) {\n      var prevStore = _this.store;\n\n      _this.resetWithFieldInitialValue({\n        entities: [entity],\n        skipExist: true\n      });\n\n      _this.notifyObservers(prevStore, [entity.getNamePath()], {\n        type: 'valueUpdate',\n        source: 'internal'\n      });\n    } // un-register field callback\n\n\n    return function (isListField, preserve) {\n      var subNamePath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      _this.fieldEntities = _this.fieldEntities.filter(function (item) {\n        return item !== entity;\n      }); // Clean up store value if not preserve\n\n      var mergedPreserve = preserve !== undefined ? preserve : _this.preserve;\n\n      if (mergedPreserve === false && (!isListField || subNamePath.length > 1)) {\n        var defaultValue = isListField ? undefined : _this.getInitialValue(namePath);\n\n        if (namePath.length && _this.getFieldValue(namePath) !== defaultValue && _this.fieldEntities.every(function (field) {\n          return (// Only reset when no namePath exist\n            !matchNamePath(field.getNamePath(), namePath)\n          );\n        })) {\n          var _prevStore = _this.store;\n\n          _this.updateStore(setValue(_prevStore, namePath, defaultValue, true)); // Notify that field is unmount\n\n\n          _this.notifyObservers(_prevStore, [namePath], {\n            type: 'remove'\n          }); // Dependencies update\n\n\n          _this.triggerDependenciesUpdate(_prevStore, namePath);\n        }\n      }\n\n      _this.notifyWatch([namePath]);\n    };\n  };\n\n  this.dispatch = function (action) {\n    switch (action.type) {\n      case 'updateValue':\n        {\n          var namePath = action.namePath,\n              value = action.value;\n\n          _this.updateValue(namePath, value);\n\n          break;\n        }\n\n      case 'validateField':\n        {\n          var _namePath = action.namePath,\n              triggerName = action.triggerName;\n\n          _this.validateFields([_namePath], {\n            triggerName: triggerName\n          });\n\n          break;\n        }\n\n      default: // Currently we don't have other action. Do nothing.\n\n    }\n  };\n\n  this.notifyObservers = function (prevStore, namePathList, info) {\n    if (_this.subscribable) {\n      var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n        store: _this.getFieldsValue(true)\n      });\n\n      _this.getFieldEntities().forEach(function (_ref3) {\n        var onStoreChange = _ref3.onStoreChange;\n        onStoreChange(prevStore, namePathList, mergedInfo);\n      });\n    } else {\n      _this.forceRootUpdate();\n    }\n  };\n\n  this.triggerDependenciesUpdate = function (prevStore, namePath) {\n    var childrenFields = _this.getDependencyChildrenFields(namePath);\n\n    if (childrenFields.length) {\n      _this.validateFields(childrenFields);\n    }\n\n    _this.notifyObservers(prevStore, childrenFields, {\n      type: 'dependenciesUpdate',\n      relatedFields: [namePath].concat(_toConsumableArray(childrenFields))\n    });\n\n    return childrenFields;\n  };\n\n  this.updateValue = function (name, value) {\n    var namePath = getNamePath(name);\n    var prevStore = _this.store;\n\n    _this.updateStore(setValue(_this.store, namePath, value));\n\n    _this.notifyObservers(prevStore, [namePath], {\n      type: 'valueUpdate',\n      source: 'internal'\n    });\n\n    _this.notifyWatch([namePath]); // Dependencies update\n\n\n    var childrenFields = _this.triggerDependenciesUpdate(prevStore, namePath); // trigger callback function\n\n\n    var onValuesChange = _this.callbacks.onValuesChange;\n\n    if (onValuesChange) {\n      var changedValues = cloneByNamePathList(_this.store, [namePath]);\n      onValuesChange(changedValues, _this.getFieldsValue());\n    }\n\n    _this.triggerOnFieldsChange([namePath].concat(_toConsumableArray(childrenFields)));\n  };\n\n  this.setFieldsValue = function (store) {\n    _this.warningUnhooked();\n\n    var prevStore = _this.store;\n\n    if (store) {\n      var nextStore = setValues(_this.store, store);\n\n      _this.updateStore(nextStore);\n    }\n\n    _this.notifyObservers(prevStore, null, {\n      type: 'valueUpdate',\n      source: 'external'\n    });\n\n    _this.notifyWatch();\n  };\n\n  this.getDependencyChildrenFields = function (rootNamePath) {\n    var children = new Set();\n    var childrenFields = [];\n    var dependencies2fields = new NameMap();\n    /**\n     * Generate maps\n     * Can use cache to save perf if user report performance issue with this\n     */\n\n    _this.getFieldEntities().forEach(function (field) {\n      var dependencies = field.props.dependencies;\n      (dependencies || []).forEach(function (dependency) {\n        var dependencyNamePath = getNamePath(dependency);\n        dependencies2fields.update(dependencyNamePath, function () {\n          var fields = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Set();\n          fields.add(field);\n          return fields;\n        });\n      });\n    });\n\n    var fillChildren = function fillChildren(namePath) {\n      var fields = dependencies2fields.get(namePath) || new Set();\n      fields.forEach(function (field) {\n        if (!children.has(field)) {\n          children.add(field);\n          var fieldNamePath = field.getNamePath();\n\n          if (field.isFieldDirty() && fieldNamePath.length) {\n            childrenFields.push(fieldNamePath);\n            fillChildren(fieldNamePath);\n          }\n        }\n      });\n    };\n\n    fillChildren(rootNamePath);\n    return childrenFields;\n  };\n\n  this.triggerOnFieldsChange = function (namePathList, filedErrors) {\n    var onFieldsChange = _this.callbacks.onFieldsChange;\n\n    if (onFieldsChange) {\n      var fields = _this.getFields();\n      /**\n       * Fill errors since `fields` may be replaced by controlled fields\n       */\n\n\n      if (filedErrors) {\n        var cache = new NameMap();\n        filedErrors.forEach(function (_ref4) {\n          var name = _ref4.name,\n              errors = _ref4.errors;\n          cache.set(name, errors);\n        });\n        fields.forEach(function (field) {\n          // eslint-disable-next-line no-param-reassign\n          field.errors = cache.get(field.name) || field.errors;\n        });\n      }\n\n      var changedFields = fields.filter(function (_ref5) {\n        var fieldName = _ref5.name;\n        return containsNamePath(namePathList, fieldName);\n      });\n      onFieldsChange(changedFields, fields);\n    }\n  };\n\n  this.validateFields = function (nameList, options) {\n    _this.warningUnhooked();\n\n    var provideNameList = !!nameList;\n    var namePathList = provideNameList ? nameList.map(getNamePath) : []; // Collect result in promise list\n\n    var promiseList = [];\n\n    _this.getFieldEntities(true).forEach(function (field) {\n      // Add field if not provide `nameList`\n      if (!provideNameList) {\n        namePathList.push(field.getNamePath());\n      }\n      /**\n       * Recursive validate if configured.\n       * TODO: perf improvement @zombieJ\n       */\n\n\n      if ((options === null || options === void 0 ? void 0 : options.recursive) && provideNameList) {\n        var namePath = field.getNamePath();\n\n        if ( // nameList[i] === undefined 说明是以 nameList 开头的\n        // ['name'] -> ['name','list']\n        namePath.every(function (nameUnit, i) {\n          return nameList[i] === nameUnit || nameList[i] === undefined;\n        })) {\n          namePathList.push(namePath);\n        }\n      } // Skip if without rule\n\n\n      if (!field.props.rules || !field.props.rules.length) {\n        return;\n      }\n\n      var fieldNamePath = field.getNamePath(); // Add field validate rule in to promise list\n\n      if (!provideNameList || containsNamePath(namePathList, fieldNamePath)) {\n        var promise = field.validateRules(_objectSpread({\n          validateMessages: _objectSpread(_objectSpread({}, defaultValidateMessages), _this.validateMessages)\n        }, options)); // Wrap promise with field\n\n        promiseList.push(promise.then(function () {\n          return {\n            name: fieldNamePath,\n            errors: [],\n            warnings: []\n          };\n        }).catch(function (ruleErrors) {\n          var mergedErrors = [];\n          var mergedWarnings = [];\n          ruleErrors.forEach(function (_ref6) {\n            var warningOnly = _ref6.rule.warningOnly,\n                errors = _ref6.errors;\n\n            if (warningOnly) {\n              mergedWarnings.push.apply(mergedWarnings, _toConsumableArray(errors));\n            } else {\n              mergedErrors.push.apply(mergedErrors, _toConsumableArray(errors));\n            }\n          });\n\n          if (mergedErrors.length) {\n            return Promise.reject({\n              name: fieldNamePath,\n              errors: mergedErrors,\n              warnings: mergedWarnings\n            });\n          }\n\n          return {\n            name: fieldNamePath,\n            errors: mergedErrors,\n            warnings: mergedWarnings\n          };\n        }));\n      }\n    });\n\n    var summaryPromise = allPromiseFinish(promiseList);\n    _this.lastValidatePromise = summaryPromise; // Notify fields with rule that validate has finished and need update\n\n    summaryPromise.catch(function (results) {\n      return results;\n    }).then(function (results) {\n      var resultNamePathList = results.map(function (_ref7) {\n        var name = _ref7.name;\n        return name;\n      });\n\n      _this.notifyObservers(_this.store, resultNamePathList, {\n        type: 'validateFinish'\n      });\n\n      _this.triggerOnFieldsChange(resultNamePathList, results);\n    });\n    var returnPromise = summaryPromise.then(function () {\n      if (_this.lastValidatePromise === summaryPromise) {\n        return Promise.resolve(_this.getFieldsValue(namePathList));\n      }\n\n      return Promise.reject([]);\n    }).catch(function (results) {\n      var errorList = results.filter(function (result) {\n        return result && result.errors.length;\n      });\n      return Promise.reject({\n        values: _this.getFieldsValue(namePathList),\n        errorFields: errorList,\n        outOfDate: _this.lastValidatePromise !== summaryPromise\n      });\n    }); // Do not throw in console\n\n    returnPromise.catch(function (e) {\n      return e;\n    });\n    return returnPromise;\n  };\n\n  this.submit = function () {\n    _this.warningUnhooked();\n\n    _this.validateFields().then(function (values) {\n      var onFinish = _this.callbacks.onFinish;\n\n      if (onFinish) {\n        try {\n          onFinish(values);\n        } catch (err) {\n          // Should print error if user `onFinish` callback failed\n          console.error(err);\n        }\n      }\n    }).catch(function (e) {\n      var onFinishFailed = _this.callbacks.onFinishFailed;\n\n      if (onFinishFailed) {\n        onFinishFailed(e);\n      }\n    });\n  };\n\n  this.forceRootUpdate = forceRootUpdate;\n});\n\nfunction useForm(form) {\n  var formRef = React.useRef();\n\n  var _React$useState = React.useState({}),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      forceUpdate = _React$useState2[1];\n\n  if (!formRef.current) {\n    if (form) {\n      formRef.current = form;\n    } else {\n      // Create a new FormStore if not provided\n      var forceReRender = function forceReRender() {\n        forceUpdate({});\n      };\n\n      var formStore = new FormStore(forceReRender);\n      formRef.current = formStore.getForm();\n    }\n  }\n\n  return [formRef.current];\n}\n\nexport default useForm;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,uBAAuB,QAAQ,kBAAkB;AAC1D,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACpI,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,IAAIC,SAAS,GAAG,aAAajB,YAAY,CAAC,SAASiB,SAASA,CAACC,eAAe,EAAE;EACnF,IAAIC,KAAK,GAAG,IAAI;EAEhBlB,eAAe,CAAC,IAAI,EAAEgB,SAAS,CAAC;EAEhC,IAAI,CAACG,UAAU,GAAG,KAAK;EACvB,IAAI,CAACF,eAAe,GAAG,KAAK,CAAC;EAC7B,IAAI,CAACG,YAAY,GAAG,IAAI;EACxB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACf,IAAI,CAACC,aAAa,GAAG,EAAE;EACvB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;EACvB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACnB,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,mBAAmB,GAAG,IAAI;EAE/B,IAAI,CAACC,OAAO,GAAG,YAAY;IACzB,OAAO;MACLC,aAAa,EAAEX,KAAK,CAACW,aAAa;MAClCC,cAAc,EAAEZ,KAAK,CAACY,cAAc;MACpCC,aAAa,EAAEb,KAAK,CAACa,aAAa;MAClCC,eAAe,EAAEd,KAAK,CAACc,eAAe;MACtCC,cAAc,EAAEf,KAAK,CAACe,cAAc;MACpCC,eAAe,EAAEhB,KAAK,CAACgB,eAAe;MACtCC,cAAc,EAAEjB,KAAK,CAACiB,cAAc;MACpCC,iBAAiB,EAAElB,KAAK,CAACkB,iBAAiB;MAC1CC,kBAAkB,EAAEnB,KAAK,CAACmB,kBAAkB;MAC5CC,WAAW,EAAEpB,KAAK,CAACoB,WAAW;MAC9BC,SAAS,EAAErB,KAAK,CAACqB,SAAS;MAC1BC,cAAc,EAAEtB,KAAK,CAACsB,cAAc;MACpCC,cAAc,EAAEvB,KAAK,CAACuB,cAAc;MACpCC,MAAM,EAAExB,KAAK,CAACwB,MAAM;MACpBC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE1B,KAAK,CAAC0B;IAC1B,CAAC;EACH,CAAC;EAED,IAAI,CAACA,gBAAgB,GAAG,UAAUC,GAAG,EAAE;IACrC,IAAIA,GAAG,KAAKzC,SAAS,EAAE;MACrBc,KAAK,CAACC,UAAU,GAAG,IAAI;MACvB,OAAO;QACL2B,QAAQ,EAAE5B,KAAK,CAAC4B,QAAQ;QACxBC,eAAe,EAAE7B,KAAK,CAAC6B,eAAe;QACtCC,aAAa,EAAE9B,KAAK,CAAC8B,aAAa;QAClCC,YAAY,EAAE/B,KAAK,CAAC+B,YAAY;QAChCC,gBAAgB,EAAEhC,KAAK,CAACgC,gBAAgB;QACxCC,WAAW,EAAEjC,KAAK,CAACiC,WAAW;QAC9BC,YAAY,EAAElC,KAAK,CAACkC,YAAY;QAChCC,mBAAmB,EAAEnC,KAAK,CAACmC,mBAAmB;QAC9CC,SAAS,EAAEpC,KAAK,CAACoC,SAAS;QAC1BC,WAAW,EAAErC,KAAK,CAACqC,WAAW;QAC9BC,eAAe,EAAEtC,KAAK,CAACsC,eAAe;QACtCC,aAAa,EAAEvC,KAAK,CAACuC;MACvB,CAAC;IACH;IAEAtD,OAAO,CAAC,KAAK,EAAE,iEAAiE,CAAC;IACjF,OAAO,IAAI;EACb,CAAC;EAED,IAAI,CAAC8C,YAAY,GAAG,UAAU7B,YAAY,EAAE;IAC1CF,KAAK,CAACE,YAAY,GAAGA,YAAY;EACnC,CAAC;EAED,IAAI,CAACsC,oBAAoB,GAAG,IAAI;EAEhC,IAAI,CAACR,gBAAgB,GAAG,UAAU3B,aAAa,EAAEoC,IAAI,EAAE;IACrDzC,KAAK,CAACK,aAAa,GAAGA,aAAa,IAAI,CAAC,CAAC;IAEzC,IAAIoC,IAAI,EAAE;MACR,IAAIC,qBAAqB;MAEzB,IAAIC,SAAS,GAAG/C,SAAS,CAAC,CAAC,CAAC,EAAES,aAAa,EAAEL,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;MAC3D;MACA;;MAEA,CAACuC,qBAAqB,GAAG1C,KAAK,CAACwC,oBAAoB,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;QACrJ,IAAIC,QAAQ,GAAGD,IAAI,CAAClB,GAAG;QACvBgB,SAAS,GAAGhD,QAAQ,CAACgD,SAAS,EAAEG,QAAQ,EAAErD,QAAQ,CAACY,aAAa,EAAEyC,QAAQ,CAAC,CAAC;MAC9E,CAAC,CAAC;MACF9C,KAAK,CAACwC,oBAAoB,GAAG,IAAI;MAEjCxC,KAAK,CAAC+C,WAAW,CAACJ,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,IAAI,CAACV,WAAW,GAAG,YAAY;IAC7B,IAAIO,oBAAoB,GAAG,IAAIpD,OAAO,CAAC,CAAC;IAExCY,KAAK,CAACgD,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;MACrD,IAAI,CAACA,MAAM,CAACC,UAAU,CAAC,CAAC,EAAE;QACxBX,oBAAoB,CAACY,GAAG,CAACF,MAAM,CAAC1D,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;MACtD;IACF,CAAC,CAAC;IAEFQ,KAAK,CAACwC,oBAAoB,GAAGA,oBAAoB;EACnD,CAAC;EAED,IAAI,CAACF,eAAe,GAAG,UAAUQ,QAAQ,EAAE;IACzC,IAAIO,SAAS,GAAG5D,QAAQ,CAACO,KAAK,CAACK,aAAa,EAAEyC,QAAQ,CAAC,CAAC,CAAC;;IAEzD,OAAOA,QAAQ,CAACQ,MAAM,GAAGzD,SAAS,CAACwD,SAAS,CAAC,GAAGA,SAAS;EAC3D,CAAC;EAED,IAAI,CAACnB,YAAY,GAAG,UAAU5B,SAAS,EAAE;IACvCN,KAAK,CAACM,SAAS,GAAGA,SAAS;EAC7B,CAAC;EAED,IAAI,CAAC6B,mBAAmB,GAAG,UAAU5B,gBAAgB,EAAE;IACrDP,KAAK,CAACO,gBAAgB,GAAGA,gBAAgB;EAC3C,CAAC;EAED,IAAI,CAAC8B,WAAW,GAAG,UAAU7B,QAAQ,EAAE;IACrCR,KAAK,CAACQ,QAAQ,GAAGA,QAAQ;EAC3B,CAAC;EAED,IAAI,CAAC+C,SAAS,GAAG,EAAE;EAEnB,IAAI,CAAChB,aAAa,GAAG,UAAUiB,QAAQ,EAAE;IACvCxD,KAAK,CAACuD,SAAS,CAACE,IAAI,CAACD,QAAQ,CAAC;IAE9B,OAAO,YAAY;MACjBxD,KAAK,CAACuD,SAAS,GAAGvD,KAAK,CAACuD,SAAS,CAACG,MAAM,CAAC,UAAUC,EAAE,EAAE;QACrD,OAAOA,EAAE,KAAKH,QAAQ;MACxB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;EAED,IAAI,CAACI,WAAW,GAAG,YAAY;IAC7B,IAAId,QAAQ,GAAGe,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;;IAErF;IACA,IAAI7D,KAAK,CAACuD,SAAS,CAACD,MAAM,EAAE;MAC1B,IAAIS,MAAM,GAAG/D,KAAK,CAACY,cAAc,CAAC,CAAC;MAEnCZ,KAAK,CAACuD,SAAS,CAACN,OAAO,CAAC,UAAUO,QAAQ,EAAE;QAC1CA,QAAQ,CAACO,MAAM,EAAEjB,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAI,CAACkB,SAAS,GAAG,IAAI;EAErB,IAAI,CAACC,eAAe,GAAG,YAAY;IACjC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACpE,KAAK,CAACgE,SAAS,IAAI,OAAOK,MAAM,KAAK,WAAW,EAAE;MAC9FrE,KAAK,CAACgE,SAAS,GAAGM,UAAU,CAAC,YAAY;QACvCtE,KAAK,CAACgE,SAAS,GAAG,IAAI;QAEtB,IAAI,CAAChE,KAAK,CAACC,UAAU,EAAE;UACrBhB,OAAO,CAAC,KAAK,EAAE,iGAAiG,CAAC;QACnH;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAI,CAAC8D,WAAW,GAAG,UAAUJ,SAAS,EAAE;IACtC3C,KAAK,CAACG,KAAK,GAAGwC,SAAS;EACzB,CAAC;EAED,IAAI,CAACK,gBAAgB,GAAG,YAAY;IAClC,IAAIuB,IAAI,GAAGV,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAEpF,IAAI,CAACU,IAAI,EAAE;MACT,OAAOvE,KAAK,CAACI,aAAa;IAC5B;IAEA,OAAOJ,KAAK,CAACI,aAAa,CAACsD,MAAM,CAAC,UAAUc,KAAK,EAAE;MACjD,OAAOA,KAAK,CAAChF,WAAW,CAAC,CAAC,CAAC8D,MAAM;IACnC,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACmB,YAAY,GAAG,YAAY;IAC9B,IAAIF,IAAI,GAAGV,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACpF,IAAIa,KAAK,GAAG,IAAItF,OAAO,CAAC,CAAC;IAEzBY,KAAK,CAACgD,gBAAgB,CAACuB,IAAI,CAAC,CAACtB,OAAO,CAAC,UAAUuB,KAAK,EAAE;MACpD,IAAI1B,QAAQ,GAAG0B,KAAK,CAAChF,WAAW,CAAC,CAAC;MAClCkF,KAAK,CAACtB,GAAG,CAACN,QAAQ,EAAE0B,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEF,OAAOE,KAAK;EACd,CAAC;EAED,IAAI,CAACC,+BAA+B,GAAG,UAAUC,QAAQ,EAAE;IACzD,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO5E,KAAK,CAACgD,gBAAgB,CAAC,IAAI,CAAC;IACrC;IAEA,IAAI0B,KAAK,GAAG1E,KAAK,CAACyE,YAAY,CAAC,IAAI,CAAC;IAEpC,OAAOG,QAAQ,CAAChC,GAAG,CAAC,UAAUiC,IAAI,EAAE;MAClC,IAAI/B,QAAQ,GAAGtD,WAAW,CAACqF,IAAI,CAAC;MAChC,OAAOH,KAAK,CAACI,GAAG,CAAChC,QAAQ,CAAC,IAAI;QAC5BiC,oBAAoB,EAAEvF,WAAW,CAACqF,IAAI;MACxC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACjE,cAAc,GAAG,UAAUgE,QAAQ,EAAEI,UAAU,EAAE;IACpDhF,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAIW,QAAQ,KAAK,IAAI,IAAI,CAACI,UAAU,EAAE;MACpC,OAAOhF,KAAK,CAACG,KAAK;IACpB;IAEA,IAAIC,aAAa,GAAGJ,KAAK,CAAC2E,+BAA+B,CAACM,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAAC;IAEpG,IAAIO,gBAAgB,GAAG,EAAE;IACzB/E,aAAa,CAAC6C,OAAO,CAAC,UAAUC,MAAM,EAAE;MACtC,IAAIkC,mBAAmB;MAEvB,IAAItC,QAAQ,GAAG,sBAAsB,IAAII,MAAM,GAAGA,MAAM,CAAC6B,oBAAoB,GAAG7B,MAAM,CAAC1D,WAAW,CAAC,CAAC,CAAC,CAAC;MACtG;;MAEA,IAAI,CAACoF,QAAQ,KAAK,CAACQ,mBAAmB,GAAGlC,MAAM,CAACmC,WAAW,MAAM,IAAI,IAAID,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACE,IAAI,CAACpC,MAAM,CAAC,CAAC,EAAE;QACpJ;MACF;MAEA,IAAI,CAAC8B,UAAU,EAAE;QACfG,gBAAgB,CAAC1B,IAAI,CAACX,QAAQ,CAAC;MACjC,CAAC,MAAM;QACL,IAAIyC,IAAI,GAAG,SAAS,IAAIrC,MAAM,GAAGA,MAAM,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI;QAExD,IAAIR,UAAU,CAACO,IAAI,CAAC,EAAE;UACpBJ,gBAAgB,CAAC1B,IAAI,CAACX,QAAQ,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACF,OAAOxD,mBAAmB,CAACU,KAAK,CAACG,KAAK,EAAEgF,gBAAgB,CAACvC,GAAG,CAACpD,WAAW,CAAC,CAAC;EAC5E,CAAC;EAED,IAAI,CAACmB,aAAa,GAAG,UAAUkE,IAAI,EAAE;IACnC7E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAInB,QAAQ,GAAGtD,WAAW,CAACqF,IAAI,CAAC;IAChC,OAAOpF,QAAQ,CAACO,KAAK,CAACG,KAAK,EAAE2C,QAAQ,CAAC;EACxC,CAAC;EAED,IAAI,CAAC/B,cAAc,GAAG,UAAU6D,QAAQ,EAAE;IACxC5E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAI7D,aAAa,GAAGJ,KAAK,CAAC2E,+BAA+B,CAACC,QAAQ,CAAC;IAEnE,OAAOxE,aAAa,CAACwC,GAAG,CAAC,UAAUM,MAAM,EAAEuC,KAAK,EAAE;MAChD,IAAIvC,MAAM,IAAI,EAAE,sBAAsB,IAAIA,MAAM,CAAC,EAAE;QACjD,OAAO;UACL2B,IAAI,EAAE3B,MAAM,CAAC1D,WAAW,CAAC,CAAC;UAC1BkG,MAAM,EAAExC,MAAM,CAACyC,SAAS,CAAC,CAAC;UAC1BC,QAAQ,EAAE1C,MAAM,CAAC2C,WAAW,CAAC;QAC/B,CAAC;MACH;MAEA,OAAO;QACLhB,IAAI,EAAErF,WAAW,CAACoF,QAAQ,CAACa,KAAK,CAAC,CAAC;QAClCC,MAAM,EAAE,EAAE;QACVE,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAAC/E,aAAa,GAAG,UAAUgE,IAAI,EAAE;IACnC7E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAInB,QAAQ,GAAGtD,WAAW,CAACqF,IAAI,CAAC;IAEhC,IAAIiB,UAAU,GAAG9F,KAAK,CAACe,cAAc,CAAC,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpD,OAAOgD,UAAU,CAACJ,MAAM;EAC1B,CAAC;EAED,IAAI,CAAC5E,eAAe,GAAG,UAAU+D,IAAI,EAAE;IACrC7E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAInB,QAAQ,GAAGtD,WAAW,CAACqF,IAAI,CAAC;IAEhC,IAAIiB,UAAU,GAAG9F,KAAK,CAACe,cAAc,CAAC,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpD,OAAOgD,UAAU,CAACF,QAAQ;EAC5B,CAAC;EAED,IAAI,CAAC5E,eAAe,GAAG,YAAY;IACjChB,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,KAAK,IAAI8B,IAAI,GAAGlC,SAAS,CAACP,MAAM,EAAE0C,IAAI,GAAG,IAAIf,KAAK,CAACc,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGpC,SAAS,CAACoC,IAAI,CAAC;IAC9B;IAEA,IAAIC,IAAI,GAAGF,IAAI,CAAC,CAAC,CAAC;MACdG,IAAI,GAAGH,IAAI,CAAC,CAAC,CAAC;IAClB,IAAII,YAAY;IAChB,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,IAAIL,IAAI,CAAC1C,MAAM,KAAK,CAAC,EAAE;MACrB8C,YAAY,GAAG,IAAI;IACrB,CAAC,MAAM,IAAIJ,IAAI,CAAC1C,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI2B,KAAK,CAACC,OAAO,CAACgB,IAAI,CAAC,EAAE;QACvBE,YAAY,GAAGF,IAAI,CAACtD,GAAG,CAACpD,WAAW,CAAC;QACpC6G,kBAAkB,GAAG,KAAK;MAC5B,CAAC,MAAM;QACLD,YAAY,GAAG,IAAI;QACnBC,kBAAkB,GAAGH,IAAI;MAC3B;IACF,CAAC,MAAM;MACLE,YAAY,GAAGF,IAAI,CAACtD,GAAG,CAACpD,WAAW,CAAC;MACpC6G,kBAAkB,GAAGF,IAAI;IAC3B;IAEA,IAAI/F,aAAa,GAAGJ,KAAK,CAACgD,gBAAgB,CAAC,IAAI,CAAC;IAEhD,IAAI/B,cAAc,GAAG,SAASA,cAAcA,CAACuD,KAAK,EAAE;MAClD,OAAOA,KAAK,CAACvD,cAAc,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;;IAGH,IAAI,CAACmF,YAAY,EAAE;MACjB,OAAOC,kBAAkB,GAAGjG,aAAa,CAACkG,KAAK,CAACrF,cAAc,CAAC,GAAGb,aAAa,CAACmG,IAAI,CAACtF,cAAc,CAAC;IACtG,CAAC,CAAC;;IAGF,IAAI2B,GAAG,GAAG,IAAIxD,OAAO,CAAC,CAAC;IACvBgH,YAAY,CAACnD,OAAO,CAAC,UAAUuD,aAAa,EAAE;MAC5C5D,GAAG,CAACQ,GAAG,CAACoD,aAAa,EAAE,EAAE,CAAC;IAC5B,CAAC,CAAC;IACFpG,aAAa,CAAC6C,OAAO,CAAC,UAAUuB,KAAK,EAAE;MACrC,IAAIiC,aAAa,GAAGjC,KAAK,CAAChF,WAAW,CAAC,CAAC,CAAC,CAAC;;MAEzC4G,YAAY,CAACnD,OAAO,CAAC,UAAUuD,aAAa,EAAE;QAC5C,IAAIA,aAAa,CAACF,KAAK,CAAC,UAAUI,QAAQ,EAAEC,CAAC,EAAE;UAC7C,OAAOF,aAAa,CAACE,CAAC,CAAC,KAAKD,QAAQ;QACtC,CAAC,CAAC,EAAE;UACF9D,GAAG,CAACgE,MAAM,CAACJ,aAAa,EAAE,UAAUK,IAAI,EAAE;YACxC,OAAO,EAAE,CAACC,MAAM,CAAClI,kBAAkB,CAACiI,IAAI,CAAC,EAAE,CAACrC,KAAK,CAAC,CAAC;UACrD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIuC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,QAAQ,EAAE;MACnE,OAAOA,QAAQ,CAACT,IAAI,CAACtF,cAAc,CAAC;IACtC,CAAC;IAED,IAAIgG,oBAAoB,GAAGrE,GAAG,CAACA,GAAG,CAAC,UAAUsE,KAAK,EAAE;MAClD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACF,OAAOd,kBAAkB,GAAGY,oBAAoB,CAACX,KAAK,CAACS,qBAAqB,CAAC,GAAGE,oBAAoB,CAACV,IAAI,CAACQ,qBAAqB,CAAC;EAClI,CAAC;EAED,IAAI,CAAC9F,cAAc,GAAG,UAAU4D,IAAI,EAAE;IACpC7E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,OAAOjE,KAAK,CAACgB,eAAe,CAAC,CAAC6D,IAAI,CAAC,CAAC;EACtC,CAAC;EAED,IAAI,CAAC1D,kBAAkB,GAAG,UAAUyD,QAAQ,EAAE;IAC5C5E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAI7D,aAAa,GAAGJ,KAAK,CAACgD,gBAAgB,CAAC,CAAC;IAE5C,IAAI,CAAC4B,QAAQ,EAAE;MACb,OAAOxE,aAAa,CAACmG,IAAI,CAAC,UAAUa,SAAS,EAAE;QAC7C,OAAOA,SAAS,CAAClG,iBAAiB,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ;IAEA,IAAIkF,YAAY,GAAGxB,QAAQ,CAAChC,GAAG,CAACpD,WAAW,CAAC;IAC5C,OAAOY,aAAa,CAACmG,IAAI,CAAC,UAAUa,SAAS,EAAE;MAC7C,IAAIX,aAAa,GAAGW,SAAS,CAAC5H,WAAW,CAAC,CAAC;MAC3C,OAAOD,gBAAgB,CAAC6G,YAAY,EAAEK,aAAa,CAAC,IAAIW,SAAS,CAAClG,iBAAiB,CAAC,CAAC;IACvF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACA,iBAAiB,GAAG,UAAU2D,IAAI,EAAE;IACvC7E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,OAAOjE,KAAK,CAACmB,kBAAkB,CAAC,CAAC0D,IAAI,CAAC,CAAC;EACzC,CAAC;EAED,IAAI,CAACwC,0BAA0B,GAAG,YAAY;IAC5C,IAAIC,IAAI,GAAGzD,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjF;IACA,IAAIa,KAAK,GAAG,IAAItF,OAAO,CAAC,CAAC;IAEzB,IAAIgB,aAAa,GAAGJ,KAAK,CAACgD,gBAAgB,CAAC,IAAI,CAAC;IAEhD5C,aAAa,CAAC6C,OAAO,CAAC,UAAUuB,KAAK,EAAE;MACrC,IAAI+C,YAAY,GAAG/C,KAAK,CAACgD,KAAK,CAACD,YAAY;MAC3C,IAAIzE,QAAQ,GAAG0B,KAAK,CAAChF,WAAW,CAAC,CAAC,CAAC,CAAC;;MAEpC,IAAI+H,YAAY,KAAKzD,SAAS,EAAE;QAC9B,IAAI2D,OAAO,GAAG/C,KAAK,CAACI,GAAG,CAAChC,QAAQ,CAAC,IAAI,IAAI4E,GAAG,CAAC,CAAC;QAC9CD,OAAO,CAACE,GAAG,CAAC;UACVzE,MAAM,EAAEsB,KAAK;UACb2C,KAAK,EAAEI;QACT,CAAC,CAAC;QACF7C,KAAK,CAACtB,GAAG,CAACN,QAAQ,EAAE2E,OAAO,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACZ,QAAQ,EAAE;MACvDA,QAAQ,CAAC/D,OAAO,CAAC,UAAUuB,KAAK,EAAE;QAChC,IAAI+C,YAAY,GAAG/C,KAAK,CAACgD,KAAK,CAACD,YAAY;QAE3C,IAAIA,YAAY,KAAKzD,SAAS,EAAE;UAC9B,IAAIhB,QAAQ,GAAG0B,KAAK,CAAChF,WAAW,CAAC,CAAC;UAElC,IAAIqI,gBAAgB,GAAG7H,KAAK,CAACsC,eAAe,CAACQ,QAAQ,CAAC;UAEtD,IAAI+E,gBAAgB,KAAK/D,SAAS,EAAE;YAClC;YACA7E,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC6H,MAAM,CAAChE,QAAQ,CAACgF,IAAI,CAAC,GAAG,CAAC,EAAE,gCAAgC,CAAC,CAAC;UAC7H,CAAC,MAAM;YACL,IAAIL,OAAO,GAAG/C,KAAK,CAACI,GAAG,CAAChC,QAAQ,CAAC;YAEjC,IAAI2E,OAAO,IAAIA,OAAO,CAACM,IAAI,GAAG,CAAC,EAAE;cAC/B;cACA9I,OAAO,CAAC,KAAK,EAAE,4BAA4B,CAAC6H,MAAM,CAAChE,QAAQ,CAACgF,IAAI,CAAC,GAAG,CAAC,EAAE,yDAAyD,CAAC,CAAC;YACpI,CAAC,MAAM,IAAIL,OAAO,EAAE;cAClB,IAAIO,WAAW,GAAGhI,KAAK,CAACW,aAAa,CAACmC,QAAQ,CAAC,CAAC,CAAC;;cAGjD,IAAI,CAACwE,IAAI,CAACW,SAAS,IAAID,WAAW,KAAKlE,SAAS,EAAE;gBAChD9D,KAAK,CAAC+C,WAAW,CAACpD,QAAQ,CAACK,KAAK,CAACG,KAAK,EAAE2C,QAAQ,EAAElE,kBAAkB,CAAC6I,OAAO,CAAC,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC;cAC1F;YACF;UACF;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IAED,IAAIe,qBAAqB;IAEzB,IAAIZ,IAAI,CAACN,QAAQ,EAAE;MACjBkB,qBAAqB,GAAGZ,IAAI,CAACN,QAAQ;IACvC,CAAC,MAAM,IAAIM,IAAI,CAAClB,YAAY,EAAE;MAC5B8B,qBAAqB,GAAG,EAAE;MAC1BZ,IAAI,CAAClB,YAAY,CAACnD,OAAO,CAAC,UAAUH,QAAQ,EAAE;QAC5C,IAAI2E,OAAO,GAAG/C,KAAK,CAACI,GAAG,CAAChC,QAAQ,CAAC;QAEjC,IAAI2E,OAAO,EAAE;UACX,IAAIU,qBAAqB;UAEzB,CAACA,qBAAqB,GAAGD,qBAAqB,EAAEzE,IAAI,CAAC2E,KAAK,CAACD,qBAAqB,EAAEvJ,kBAAkB,CAACA,kBAAkB,CAAC6I,OAAO,CAAC,CAAC7E,GAAG,CAAC,UAAUyF,CAAC,EAAE;YAChJ,OAAOA,CAAC,CAACnF,MAAM;UACjB,CAAC,CAAC,CAAC,CAAC;QACN;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLgF,qBAAqB,GAAG9H,aAAa;IACvC;IAEAwH,eAAe,CAACM,qBAAqB,CAAC;EACxC,CAAC;EAED,IAAI,CAAC9G,WAAW,GAAG,UAAUwD,QAAQ,EAAE;IACrC5E,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAIqE,SAAS,GAAGtI,KAAK,CAACG,KAAK;IAE3B,IAAI,CAACyE,QAAQ,EAAE;MACb5E,KAAK,CAAC+C,WAAW,CAACnD,SAAS,CAAC,CAAC,CAAC,EAAEI,KAAK,CAACK,aAAa,CAAC,CAAC;MAErDL,KAAK,CAACqH,0BAA0B,CAAC,CAAC;MAElCrH,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAE,IAAI,EAAE;QACrCE,IAAI,EAAE;MACR,CAAC,CAAC;MAEFxI,KAAK,CAAC4D,WAAW,CAAC,CAAC;MAEnB;IACF,CAAC,CAAC;;IAGF,IAAIwC,YAAY,GAAGxB,QAAQ,CAAChC,GAAG,CAACpD,WAAW,CAAC;IAC5C4G,YAAY,CAACnD,OAAO,CAAC,UAAUH,QAAQ,EAAE;MACvC,IAAIyE,YAAY,GAAGvH,KAAK,CAACsC,eAAe,CAACQ,QAAQ,CAAC;MAElD9C,KAAK,CAAC+C,WAAW,CAACpD,QAAQ,CAACK,KAAK,CAACG,KAAK,EAAE2C,QAAQ,EAAEyE,YAAY,CAAC,CAAC;IAClE,CAAC,CAAC;IAEFvH,KAAK,CAACqH,0BAA0B,CAAC;MAC/BjB,YAAY,EAAEA;IAChB,CAAC,CAAC;IAEFpG,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAElC,YAAY,EAAE;MAC7CoC,IAAI,EAAE;IACR,CAAC,CAAC;IAEFxI,KAAK,CAAC4D,WAAW,CAACwC,YAAY,CAAC;EACjC,CAAC;EAED,IAAI,CAAC/E,SAAS,GAAG,UAAUoH,MAAM,EAAE;IACjCzI,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAIqE,SAAS,GAAGtI,KAAK,CAACG,KAAK;IAC3B,IAAIiG,YAAY,GAAG,EAAE;IACrBqC,MAAM,CAACxF,OAAO,CAAC,UAAUyF,SAAS,EAAE;MAClC,IAAI7D,IAAI,GAAG6D,SAAS,CAAC7D,IAAI;QACrBa,MAAM,GAAGgD,SAAS,CAAChD,MAAM;QACzBiD,IAAI,GAAGhK,wBAAwB,CAAC+J,SAAS,EAAE3J,SAAS,CAAC;MAEzD,IAAI+D,QAAQ,GAAGtD,WAAW,CAACqF,IAAI,CAAC;MAChCuB,YAAY,CAAC3C,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAC;;MAE7B,IAAI,OAAO,IAAI6F,IAAI,EAAE;QACnB3I,KAAK,CAAC+C,WAAW,CAACpD,QAAQ,CAACK,KAAK,CAACG,KAAK,EAAE2C,QAAQ,EAAE6F,IAAI,CAACxB,KAAK,CAAC,CAAC;MAChE;MAEAnH,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAE,CAACxF,QAAQ,CAAC,EAAE;QAC3C0F,IAAI,EAAE,UAAU;QAChBG,IAAI,EAAED;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF1I,KAAK,CAAC4D,WAAW,CAACwC,YAAY,CAAC;EACjC,CAAC;EAED,IAAI,CAAChE,SAAS,GAAG,YAAY;IAC3B,IAAI4E,QAAQ,GAAGhH,KAAK,CAACgD,gBAAgB,CAAC,IAAI,CAAC;IAE3C,IAAIyF,MAAM,GAAGzB,QAAQ,CAACpE,GAAG,CAAC,UAAU4B,KAAK,EAAE;MACzC,IAAI1B,QAAQ,GAAG0B,KAAK,CAAChF,WAAW,CAAC,CAAC;MAClC,IAAI+F,IAAI,GAAGf,KAAK,CAACgB,OAAO,CAAC,CAAC;MAE1B,IAAIkD,SAAS,GAAGhK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDV,IAAI,EAAE/B,QAAQ;QACdqE,KAAK,EAAEnH,KAAK,CAACW,aAAa,CAACmC,QAAQ;MACrC,CAAC,CAAC;MAEF8F,MAAM,CAACC,cAAc,CAACH,SAAS,EAAE,eAAe,EAAE;QAChDvB,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAOuB,SAAS;IAClB,CAAC,CAAC;IACF,OAAOD,MAAM;EACf,CAAC;EAED,IAAI,CAAC5G,eAAe,GAAG,UAAUqB,MAAM,EAAE;IACvC,IAAIqE,YAAY,GAAGrE,MAAM,CAACsE,KAAK,CAACD,YAAY;IAE5C,IAAIA,YAAY,KAAKzD,SAAS,EAAE;MAC9B,IAAIhB,QAAQ,GAAGI,MAAM,CAAC1D,WAAW,CAAC,CAAC;MACnC,IAAIsJ,SAAS,GAAGrJ,QAAQ,CAACO,KAAK,CAACG,KAAK,EAAE2C,QAAQ,CAAC;MAE/C,IAAIgG,SAAS,KAAKhF,SAAS,EAAE;QAC3B9D,KAAK,CAAC+C,WAAW,CAACpD,QAAQ,CAACK,KAAK,CAACG,KAAK,EAAE2C,QAAQ,EAAEyE,YAAY,CAAC,CAAC;MAClE;IACF;EACF,CAAC;EAED,IAAI,CAACzF,aAAa,GAAG,UAAUoB,MAAM,EAAE;IACrClD,KAAK,CAACI,aAAa,CAACqD,IAAI,CAACP,MAAM,CAAC;IAEhC,IAAIJ,QAAQ,GAAGI,MAAM,CAAC1D,WAAW,CAAC,CAAC;IAEnCQ,KAAK,CAAC4D,WAAW,CAAC,CAACd,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAG/B,IAAII,MAAM,CAACsE,KAAK,CAACD,YAAY,KAAKzD,SAAS,EAAE;MAC3C,IAAIwE,SAAS,GAAGtI,KAAK,CAACG,KAAK;MAE3BH,KAAK,CAACqH,0BAA0B,CAAC;QAC/BL,QAAQ,EAAE,CAAC9D,MAAM,CAAC;QAClB+E,SAAS,EAAE;MACb,CAAC,CAAC;MAEFjI,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAE,CAACpF,MAAM,CAAC1D,WAAW,CAAC,CAAC,CAAC,EAAE;QACvDgJ,IAAI,EAAE,aAAa;QACnBO,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,OAAO,UAAU1D,WAAW,EAAE7E,QAAQ,EAAE;MACtC,IAAIwI,WAAW,GAAGnF,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACxF7D,KAAK,CAACI,aAAa,GAAGJ,KAAK,CAACI,aAAa,CAACsD,MAAM,CAAC,UAAUuF,IAAI,EAAE;QAC/D,OAAOA,IAAI,KAAK/F,MAAM;MACxB,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIgG,cAAc,GAAG1I,QAAQ,KAAKsD,SAAS,GAAGtD,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;MAEvE,IAAI0I,cAAc,KAAK,KAAK,KAAK,CAAC7D,WAAW,IAAI2D,WAAW,CAAC1F,MAAM,GAAG,CAAC,CAAC,EAAE;QACxE,IAAI6F,YAAY,GAAG9D,WAAW,GAAGvB,SAAS,GAAG9D,KAAK,CAACsC,eAAe,CAACQ,QAAQ,CAAC;QAE5E,IAAIA,QAAQ,CAACQ,MAAM,IAAItD,KAAK,CAACW,aAAa,CAACmC,QAAQ,CAAC,KAAKqG,YAAY,IAAInJ,KAAK,CAACI,aAAa,CAACkG,KAAK,CAAC,UAAU9B,KAAK,EAAE;UAClH;YAAQ;YACN,CAAC9E,aAAa,CAAC8E,KAAK,CAAChF,WAAW,CAAC,CAAC,EAAEsD,QAAQ;UAAC;QAEjD,CAAC,CAAC,EAAE;UACF,IAAIsG,UAAU,GAAGpJ,KAAK,CAACG,KAAK;UAE5BH,KAAK,CAAC+C,WAAW,CAACpD,QAAQ,CAACyJ,UAAU,EAAEtG,QAAQ,EAAEqG,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;UAGvEnJ,KAAK,CAACuI,eAAe,CAACa,UAAU,EAAE,CAACtG,QAAQ,CAAC,EAAE;YAC5C0F,IAAI,EAAE;UACR,CAAC,CAAC,CAAC,CAAC;;UAGJxI,KAAK,CAACqJ,yBAAyB,CAACD,UAAU,EAAEtG,QAAQ,CAAC;QACvD;MACF;MAEA9C,KAAK,CAAC4D,WAAW,CAAC,CAACd,QAAQ,CAAC,CAAC;IAC/B,CAAC;EACH,CAAC;EAED,IAAI,CAAClB,QAAQ,GAAG,UAAU0H,MAAM,EAAE;IAChC,QAAQA,MAAM,CAACd,IAAI;MACjB,KAAK,aAAa;QAChB;UACE,IAAI1F,QAAQ,GAAGwG,MAAM,CAACxG,QAAQ;YAC1BqE,KAAK,GAAGmC,MAAM,CAACnC,KAAK;UAExBnH,KAAK,CAACuJ,WAAW,CAACzG,QAAQ,EAAEqE,KAAK,CAAC;UAElC;QACF;MAEF,KAAK,eAAe;QAClB;UACE,IAAIqC,SAAS,GAAGF,MAAM,CAACxG,QAAQ;YAC3B2G,WAAW,GAAGH,MAAM,CAACG,WAAW;UAEpCzJ,KAAK,CAACuB,cAAc,CAAC,CAACiI,SAAS,CAAC,EAAE;YAChCC,WAAW,EAAEA;UACf,CAAC,CAAC;UAEF;QACF;MAEF,QAAQ,CAAC;IAEX;EACF,CAAC;EAED,IAAI,CAAClB,eAAe,GAAG,UAAUD,SAAS,EAAElC,YAAY,EAAEkB,IAAI,EAAE;IAC9D,IAAItH,KAAK,CAACE,YAAY,EAAE;MACtB,IAAIwJ,UAAU,GAAGhL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1DnH,KAAK,EAAEH,KAAK,CAACY,cAAc,CAAC,IAAI;MAClC,CAAC,CAAC;MAEFZ,KAAK,CAACgD,gBAAgB,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU0G,KAAK,EAAE;QAChD,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;QACvCA,aAAa,CAACtB,SAAS,EAAElC,YAAY,EAAEsD,UAAU,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL1J,KAAK,CAACD,eAAe,CAAC,CAAC;IACzB;EACF,CAAC;EAED,IAAI,CAACsJ,yBAAyB,GAAG,UAAUf,SAAS,EAAExF,QAAQ,EAAE;IAC9D,IAAI+G,cAAc,GAAG7J,KAAK,CAAC8J,2BAA2B,CAAChH,QAAQ,CAAC;IAEhE,IAAI+G,cAAc,CAACvG,MAAM,EAAE;MACzBtD,KAAK,CAACuB,cAAc,CAACsI,cAAc,CAAC;IACtC;IAEA7J,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAEuB,cAAc,EAAE;MAC/CrB,IAAI,EAAE,oBAAoB;MAC1BuB,aAAa,EAAE,CAACjH,QAAQ,CAAC,CAACgE,MAAM,CAAClI,kBAAkB,CAACiL,cAAc,CAAC;IACrE,CAAC,CAAC;IAEF,OAAOA,cAAc;EACvB,CAAC;EAED,IAAI,CAACN,WAAW,GAAG,UAAU1E,IAAI,EAAEsC,KAAK,EAAE;IACxC,IAAIrE,QAAQ,GAAGtD,WAAW,CAACqF,IAAI,CAAC;IAChC,IAAIyD,SAAS,GAAGtI,KAAK,CAACG,KAAK;IAE3BH,KAAK,CAAC+C,WAAW,CAACpD,QAAQ,CAACK,KAAK,CAACG,KAAK,EAAE2C,QAAQ,EAAEqE,KAAK,CAAC,CAAC;IAEzDnH,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAE,CAACxF,QAAQ,CAAC,EAAE;MAC3C0F,IAAI,EAAE,aAAa;MACnBO,MAAM,EAAE;IACV,CAAC,CAAC;IAEF/I,KAAK,CAAC4D,WAAW,CAAC,CAACd,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAG/B,IAAI+G,cAAc,GAAG7J,KAAK,CAACqJ,yBAAyB,CAACf,SAAS,EAAExF,QAAQ,CAAC,CAAC,CAAC;;IAG3E,IAAIkH,cAAc,GAAGhK,KAAK,CAACM,SAAS,CAAC0J,cAAc;IAEnD,IAAIA,cAAc,EAAE;MAClB,IAAIC,aAAa,GAAG3K,mBAAmB,CAACU,KAAK,CAACG,KAAK,EAAE,CAAC2C,QAAQ,CAAC,CAAC;MAChEkH,cAAc,CAACC,aAAa,EAAEjK,KAAK,CAACY,cAAc,CAAC,CAAC,CAAC;IACvD;IAEAZ,KAAK,CAACkK,qBAAqB,CAAC,CAACpH,QAAQ,CAAC,CAACgE,MAAM,CAAClI,kBAAkB,CAACiL,cAAc,CAAC,CAAC,CAAC;EACpF,CAAC;EAED,IAAI,CAACvI,cAAc,GAAG,UAAUnB,KAAK,EAAE;IACrCH,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAIqE,SAAS,GAAGtI,KAAK,CAACG,KAAK;IAE3B,IAAIA,KAAK,EAAE;MACT,IAAIwC,SAAS,GAAG/C,SAAS,CAACI,KAAK,CAACG,KAAK,EAAEA,KAAK,CAAC;MAE7CH,KAAK,CAAC+C,WAAW,CAACJ,SAAS,CAAC;IAC9B;IAEA3C,KAAK,CAACuI,eAAe,CAACD,SAAS,EAAE,IAAI,EAAE;MACrCE,IAAI,EAAE,aAAa;MACnBO,MAAM,EAAE;IACV,CAAC,CAAC;IAEF/I,KAAK,CAAC4D,WAAW,CAAC,CAAC;EACrB,CAAC;EAED,IAAI,CAACkG,2BAA2B,GAAG,UAAUK,YAAY,EAAE;IACzD,IAAIC,QAAQ,GAAG,IAAI1C,GAAG,CAAC,CAAC;IACxB,IAAImC,cAAc,GAAG,EAAE;IACvB,IAAIQ,mBAAmB,GAAG,IAAIjL,OAAO,CAAC,CAAC;IACvC;AACJ;AACA;AACA;;IAEIY,KAAK,CAACgD,gBAAgB,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUuB,KAAK,EAAE;MAChD,IAAI8F,YAAY,GAAG9F,KAAK,CAACgD,KAAK,CAAC8C,YAAY;MAC3C,CAACA,YAAY,IAAI,EAAE,EAAErH,OAAO,CAAC,UAAUsH,UAAU,EAAE;QACjD,IAAIC,kBAAkB,GAAGhL,WAAW,CAAC+K,UAAU,CAAC;QAChDF,mBAAmB,CAACzD,MAAM,CAAC4D,kBAAkB,EAAE,YAAY;UACzD,IAAI/B,MAAM,GAAG5E,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI6D,GAAG,CAAC,CAAC;UAC1Fe,MAAM,CAACd,GAAG,CAACnD,KAAK,CAAC;UACjB,OAAOiE,MAAM;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIgC,YAAY,GAAG,SAASA,YAAYA,CAAC3H,QAAQ,EAAE;MACjD,IAAI2F,MAAM,GAAG4B,mBAAmB,CAACvF,GAAG,CAAChC,QAAQ,CAAC,IAAI,IAAI4E,GAAG,CAAC,CAAC;MAC3De,MAAM,CAACxF,OAAO,CAAC,UAAUuB,KAAK,EAAE;QAC9B,IAAI,CAAC4F,QAAQ,CAACM,GAAG,CAAClG,KAAK,CAAC,EAAE;UACxB4F,QAAQ,CAACzC,GAAG,CAACnD,KAAK,CAAC;UACnB,IAAIiC,aAAa,GAAGjC,KAAK,CAAChF,WAAW,CAAC,CAAC;UAEvC,IAAIgF,KAAK,CAACmG,YAAY,CAAC,CAAC,IAAIlE,aAAa,CAACnD,MAAM,EAAE;YAChDuG,cAAc,CAACpG,IAAI,CAACgD,aAAa,CAAC;YAClCgE,YAAY,CAAChE,aAAa,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IAEDgE,YAAY,CAACN,YAAY,CAAC;IAC1B,OAAON,cAAc;EACvB,CAAC;EAED,IAAI,CAACK,qBAAqB,GAAG,UAAU9D,YAAY,EAAEwE,WAAW,EAAE;IAChE,IAAIC,cAAc,GAAG7K,KAAK,CAACM,SAAS,CAACuK,cAAc;IAEnD,IAAIA,cAAc,EAAE;MAClB,IAAIpC,MAAM,GAAGzI,KAAK,CAACoC,SAAS,CAAC,CAAC;MAC9B;AACN;AACA;;MAGM,IAAIwI,WAAW,EAAE;QACf,IAAIlG,KAAK,GAAG,IAAItF,OAAO,CAAC,CAAC;QACzBwL,WAAW,CAAC3H,OAAO,CAAC,UAAU6H,KAAK,EAAE;UACnC,IAAIjG,IAAI,GAAGiG,KAAK,CAACjG,IAAI;YACjBa,MAAM,GAAGoF,KAAK,CAACpF,MAAM;UACzBhB,KAAK,CAACtB,GAAG,CAACyB,IAAI,EAAEa,MAAM,CAAC;QACzB,CAAC,CAAC;QACF+C,MAAM,CAACxF,OAAO,CAAC,UAAUuB,KAAK,EAAE;UAC9B;UACAA,KAAK,CAACkB,MAAM,GAAGhB,KAAK,CAACI,GAAG,CAACN,KAAK,CAACK,IAAI,CAAC,IAAIL,KAAK,CAACkB,MAAM;QACtD,CAAC,CAAC;MACJ;MAEA,IAAIqF,aAAa,GAAGtC,MAAM,CAAC/E,MAAM,CAAC,UAAUsH,KAAK,EAAE;QACjD,IAAIC,SAAS,GAAGD,KAAK,CAACnG,IAAI;QAC1B,OAAOtF,gBAAgB,CAAC6G,YAAY,EAAE6E,SAAS,CAAC;MAClD,CAAC,CAAC;MACFJ,cAAc,CAACE,aAAa,EAAEtC,MAAM,CAAC;IACvC;EACF,CAAC;EAED,IAAI,CAAClH,cAAc,GAAG,UAAUqD,QAAQ,EAAEsG,OAAO,EAAE;IACjDlL,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvB,IAAIkH,eAAe,GAAG,CAAC,CAACvG,QAAQ;IAChC,IAAIwB,YAAY,GAAG+E,eAAe,GAAGvG,QAAQ,CAAChC,GAAG,CAACpD,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;;IAErE,IAAI4L,WAAW,GAAG,EAAE;IAEpBpL,KAAK,CAACgD,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUuB,KAAK,EAAE;MACpD;MACA,IAAI,CAAC2G,eAAe,EAAE;QACpB/E,YAAY,CAAC3C,IAAI,CAACe,KAAK,CAAChF,WAAW,CAAC,CAAC,CAAC;MACxC;MACA;AACN;AACA;AACA;;MAGM,IAAI,CAAC0L,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,SAAS,KAAKF,eAAe,EAAE;QAC5F,IAAIrI,QAAQ,GAAG0B,KAAK,CAAChF,WAAW,CAAC,CAAC;QAElC;QAAK;QACL;QACAsD,QAAQ,CAACwD,KAAK,CAAC,UAAUI,QAAQ,EAAEC,CAAC,EAAE;UACpC,OAAO/B,QAAQ,CAAC+B,CAAC,CAAC,KAAKD,QAAQ,IAAI9B,QAAQ,CAAC+B,CAAC,CAAC,KAAK7C,SAAS;QAC9D,CAAC,CAAC,EAAE;UACFsC,YAAY,CAAC3C,IAAI,CAACX,QAAQ,CAAC;QAC7B;MACF,CAAC,CAAC;;MAGF,IAAI,CAAC0B,KAAK,CAACgD,KAAK,CAAC8D,KAAK,IAAI,CAAC9G,KAAK,CAACgD,KAAK,CAAC8D,KAAK,CAAChI,MAAM,EAAE;QACnD;MACF;MAEA,IAAImD,aAAa,GAAGjC,KAAK,CAAChF,WAAW,CAAC,CAAC,CAAC,CAAC;;MAEzC,IAAI,CAAC2L,eAAe,IAAI5L,gBAAgB,CAAC6G,YAAY,EAAEK,aAAa,CAAC,EAAE;QACrE,IAAI8E,OAAO,GAAG/G,KAAK,CAACgH,aAAa,CAAC9M,aAAa,CAAC;UAC9C6B,gBAAgB,EAAE7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEW,uBAAuB,CAAC,EAAEW,KAAK,CAACO,gBAAgB;QACpG,CAAC,EAAE2K,OAAO,CAAC,CAAC,CAAC,CAAC;;QAEdE,WAAW,CAAC3H,IAAI,CAAC8H,OAAO,CAACE,IAAI,CAAC,YAAY;UACxC,OAAO;YACL5G,IAAI,EAAE4B,aAAa;YACnBf,MAAM,EAAE,EAAE;YACVE,QAAQ,EAAE;UACZ,CAAC;QACH,CAAC,CAAC,CAAC8F,KAAK,CAAC,UAAUC,UAAU,EAAE;UAC7B,IAAIC,YAAY,GAAG,EAAE;UACrB,IAAIC,cAAc,GAAG,EAAE;UACvBF,UAAU,CAAC1I,OAAO,CAAC,UAAU6I,KAAK,EAAE;YAClC,IAAIC,WAAW,GAAGD,KAAK,CAACE,IAAI,CAACD,WAAW;cACpCrG,MAAM,GAAGoG,KAAK,CAACpG,MAAM;YAEzB,IAAIqG,WAAW,EAAE;cACfF,cAAc,CAACpI,IAAI,CAAC2E,KAAK,CAACyD,cAAc,EAAEjN,kBAAkB,CAAC8G,MAAM,CAAC,CAAC;YACvE,CAAC,MAAM;cACLkG,YAAY,CAACnI,IAAI,CAAC2E,KAAK,CAACwD,YAAY,EAAEhN,kBAAkB,CAAC8G,MAAM,CAAC,CAAC;YACnE;UACF,CAAC,CAAC;UAEF,IAAIkG,YAAY,CAACtI,MAAM,EAAE;YACvB,OAAO2I,OAAO,CAACC,MAAM,CAAC;cACpBrH,IAAI,EAAE4B,aAAa;cACnBf,MAAM,EAAEkG,YAAY;cACpBhG,QAAQ,EAAEiG;YACZ,CAAC,CAAC;UACJ;UAEA,OAAO;YACLhH,IAAI,EAAE4B,aAAa;YACnBf,MAAM,EAAEkG,YAAY;YACpBhG,QAAQ,EAAEiG;UACZ,CAAC;QACH,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;IAEF,IAAIM,cAAc,GAAGhN,gBAAgB,CAACiM,WAAW,CAAC;IAClDpL,KAAK,CAACS,mBAAmB,GAAG0L,cAAc,CAAC,CAAC;;IAE5CA,cAAc,CAACT,KAAK,CAAC,UAAUU,OAAO,EAAE;MACtC,OAAOA,OAAO;IAChB,CAAC,CAAC,CAACX,IAAI,CAAC,UAAUW,OAAO,EAAE;MACzB,IAAIC,kBAAkB,GAAGD,OAAO,CAACxJ,GAAG,CAAC,UAAU0J,KAAK,EAAE;QACpD,IAAIzH,IAAI,GAAGyH,KAAK,CAACzH,IAAI;QACrB,OAAOA,IAAI;MACb,CAAC,CAAC;MAEF7E,KAAK,CAACuI,eAAe,CAACvI,KAAK,CAACG,KAAK,EAAEkM,kBAAkB,EAAE;QACrD7D,IAAI,EAAE;MACR,CAAC,CAAC;MAEFxI,KAAK,CAACkK,qBAAqB,CAACmC,kBAAkB,EAAED,OAAO,CAAC;IAC1D,CAAC,CAAC;IACF,IAAIG,aAAa,GAAGJ,cAAc,CAACV,IAAI,CAAC,YAAY;MAClD,IAAIzL,KAAK,CAACS,mBAAmB,KAAK0L,cAAc,EAAE;QAChD,OAAOF,OAAO,CAACO,OAAO,CAACxM,KAAK,CAACY,cAAc,CAACwF,YAAY,CAAC,CAAC;MAC5D;MAEA,OAAO6F,OAAO,CAACC,MAAM,CAAC,EAAE,CAAC;IAC3B,CAAC,CAAC,CAACR,KAAK,CAAC,UAAUU,OAAO,EAAE;MAC1B,IAAIK,SAAS,GAAGL,OAAO,CAAC1I,MAAM,CAAC,UAAUgJ,MAAM,EAAE;QAC/C,OAAOA,MAAM,IAAIA,MAAM,CAAChH,MAAM,CAACpC,MAAM;MACvC,CAAC,CAAC;MACF,OAAO2I,OAAO,CAACC,MAAM,CAAC;QACpBnI,MAAM,EAAE/D,KAAK,CAACY,cAAc,CAACwF,YAAY,CAAC;QAC1CuG,WAAW,EAAEF,SAAS;QACtBG,SAAS,EAAE5M,KAAK,CAACS,mBAAmB,KAAK0L;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;;IAEJI,aAAa,CAACb,KAAK,CAAC,UAAUmB,CAAC,EAAE;MAC/B,OAAOA,CAAC;IACV,CAAC,CAAC;IACF,OAAON,aAAa;EACtB,CAAC;EAED,IAAI,CAAC/K,MAAM,GAAG,YAAY;IACxBxB,KAAK,CAACiE,eAAe,CAAC,CAAC;IAEvBjE,KAAK,CAACuB,cAAc,CAAC,CAAC,CAACkK,IAAI,CAAC,UAAU1H,MAAM,EAAE;MAC5C,IAAI+I,QAAQ,GAAG9M,KAAK,CAACM,SAAS,CAACwM,QAAQ;MAEvC,IAAIA,QAAQ,EAAE;QACZ,IAAI;UACFA,QAAQ,CAAC/I,MAAM,CAAC;QAClB,CAAC,CAAC,OAAOgJ,GAAG,EAAE;UACZ;UACAC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;QACpB;MACF;IACF,CAAC,CAAC,CAACrB,KAAK,CAAC,UAAUmB,CAAC,EAAE;MACpB,IAAIK,cAAc,GAAGlN,KAAK,CAACM,SAAS,CAAC4M,cAAc;MAEnD,IAAIA,cAAc,EAAE;QAClBA,cAAc,CAACL,CAAC,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAAC9M,eAAe,GAAGA,eAAe;AACxC,CAAC,CAAC;AAEF,SAASoN,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIC,OAAO,GAAGrO,KAAK,CAACsO,MAAM,CAAC,CAAC;EAE5B,IAAIC,eAAe,GAAGvO,KAAK,CAACwO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpCC,gBAAgB,GAAGhP,cAAc,CAAC8O,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAErC,IAAI,CAACJ,OAAO,CAACM,OAAO,EAAE;IACpB,IAAIP,IAAI,EAAE;MACRC,OAAO,CAACM,OAAO,GAAGP,IAAI;IACxB,CAAC,MAAM;MACL;MACA,IAAIQ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;QAC3CF,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;MAED,IAAIG,SAAS,GAAG,IAAI/N,SAAS,CAAC8N,aAAa,CAAC;MAC5CP,OAAO,CAACM,OAAO,GAAGE,SAAS,CAACnN,OAAO,CAAC,CAAC;IACvC;EACF;EAEA,OAAO,CAAC2M,OAAO,CAACM,OAAO,CAAC;AAC1B;AAEA,eAAeR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}