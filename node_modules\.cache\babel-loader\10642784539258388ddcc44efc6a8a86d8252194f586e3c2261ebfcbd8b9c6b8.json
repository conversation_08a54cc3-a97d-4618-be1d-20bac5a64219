{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\tree-changes\\src\\types.ts"], "sourcesContent": ["export type ValidTypes = string | boolean | number | PlainObject;\nexport type Comparator = Array<string | any[]>;\nexport type Data = PlainObject | ValidTypes[];\nexport type Key = string | number;\nexport type KeyType<P, D> = P | D extends any[] ? Key : keyof P | keyof D;\nexport type PlainObject = Record<string, any>;\nexport type Value = ValidTypes | ValidTypes[];\n\nexport interface Options<T = Key> {\n  actual?: Value;\n  filter?: boolean;\n  key?: T;\n  previous?: Value;\n  type?: 'decreased' | 'increased';\n}\n\nexport interface CompareValuesOptions<T = Key> {\n  key?: T;\n  type: 'added' | 'removed';\n  value?: Value;\n}\n\nexport interface TreeChanges<K> {\n  added: (key?: K, value?: Value) => boolean;\n  changed: (key?: K | string, actual?: Value, previous?: Value) => boolean;\n  changedFrom: (key: K | string, previous: Value, actual?: Value) => boolean;\n  /**\n   * @deprecated\n   * Use \"changed\" instead.\n   */\n  changedTo: (key: K | string, actual: Value) => boolean;\n  decreased: (key: K, actual?: Value, previous?: Value) => boolean;\n  emptied: (key?: K) => boolean;\n  filled: (key?: K) => boolean;\n  increased: (key: K, actual?: Value, previous?: Value) => boolean;\n  removed: (key?: K, value?: Value) => boolean;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}