{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\gestioneOPLogistica.jsx\";\n/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneOperatoriLogistica - operazioni sugli operatori della logistica\n *\n */\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Caricamento from \"../../utils/caricamento\";\nimport UtenteOPLogistica from \"../../aggiunta_dati/utenteOPLogistica\";\nimport AggiungiOPLogistica from \"../../aggiunta_dati/aggiungiOPLogistica\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport \"../../css/DataTableDemo.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneLogisticaUtentiChain extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      customerName: \"\",\n      address: \"\",\n      pIva: \"\",\n      email: \"\",\n      isValid: \"\",\n      createAt: \"\",\n      updateAt: \"\"\n    };\n    this.array = [];\n    this.state = {\n      results: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: true\n    };\n    //Dichiarazione funzioni e metodi\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.aggiungiOpLogistica = this.aggiungiOpLogistica.bind(this);\n    this.hideaggiungiAffiliati = this.hideaggiungiAffiliati.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.addUser = this.addUser.bind(this);\n    this.hideUtenteAffiliato = this.hideUtenteAffiliato.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var res = await APIRequest(\"GET\", \"employees/?employeesEffort=true&role=LOGISTICA\");\n    this.setState({\n      results: res.data,\n      loading: false\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Apertura dialogo aggiunta utente\n  addUser(rowData, value) {\n    localStorage.setItem(\"datiComodo\", rowData.id);\n    this.setState({\n      resultDialog3: true\n    });\n  }\n  //Chiusura dialogo aggiunta utente\n  hideUtenteAffiliato() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiOpLogistica() {\n    this.setState({\n      resultDialog2: true\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAffiliati() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.idemployee !== this.state.result.idemployee);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"employees/?id=\" + this.state.result.idemployee;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Operatore di logistica eliminato con successo\",\n      life: 3000\n    });\n    window.location.reload();\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideaggiungiAffiliati,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideUtenteAffiliato,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this);\n    const fields = [{\n      field: \"username\",\n      header: \"Username\",\n      body: \"username\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"first_name\",\n      header: Costanti.Nome,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"address\",\n      header: Costanti.Indirizzo,\n      body: \"address\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"city\",\n      header: Costanti.Città,\n      body: \"city\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"cap\",\n      header: Costanti.CodPost,\n      body: \"cap\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"p_iva\",\n      header: Costanti.pIva,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tel\",\n      header: Costanti.Tel,\n      body: \"tel\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"email\",\n      header: Costanti.Email,\n      body: \"email\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"isValid\",\n      header: Costanti.Validità,\n      body: \"isValid\",\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.addUser,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-user-plus\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 39\n      }, this),\n      handler: this.addUser\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 39\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    const items = [{\n      label: Costanti.AggOpLog,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiOpLogistica();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneLogistica\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          selectionMode: \"single\",\n          cellSelection: true,\n          fileNames: \"OpLogistica\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.AggUtOpLog,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideUtenteAffiliato,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UtenteOPLogistica, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.SelAnagrafica,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hideaggiungiAffiliati,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AggiungiOPLogistica, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteCli, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.first_name, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default GestioneLogisticaUtentiChain;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "Dialog", "<PERSON><PERSON>", "APIRequest", "Nav", "Caricamento", "UtenteOPLogistica", "AggiungiOPLogistica", "CustomDataTable", "jsxDEV", "_jsxDEV", "GestioneLogistica<PERSON>ti<PERSON>hain", "constructor", "props", "emptyResult", "id", "customerName", "address", "pIva", "email", "<PERSON><PERSON><PERSON><PERSON>", "createAt", "updateAt", "array", "state", "results", "resultDialog", "resultDialog2", "resultDialog3", "deleteResultDialog", "submitted", "result", "globalFilter", "loading", "confirmDeleteResult", "bind", "aggiungiOpLogistica", "hideaggiungiAffiliati", "deleteResult", "hideDeleteResultDialog", "addUser", "hideUtenteAffiliato", "componentDidMount", "res", "setState", "data", "rowData", "value", "localStorage", "setItem", "filter", "val", "idemployee", "url", "console", "log", "toast", "show", "severity", "summary", "detail", "life", "window", "location", "reload", "render", "resultDialogFooter2", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter3", "deleteResultDialogFooter", "label", "icon", "Si", "fields", "field", "header", "body", "sortable", "showHeader", "Nome", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Tel", "Email", "Validità", "actionFields", "name", "handler", "Elimina", "items", "AggOpLog", "command", "ref", "el", "gestioneLogistica", "dt", "dataKey", "paginator", "rows", "rowsPerPageOptions", "actionsColumn", "autoLayout", "splitButtonClass", "selectionMode", "cellSelection", "fileNames", "visible", "AggUtOpLog", "modal", "footer", "onHide", "SelAnagrafica", "Conferma", "style", "fontSize", "ResDeleteCli", "first_name"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/gestioneOPLogistica.jsx"], "sourcesContent": ["/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneOperatoriLogistica - operazioni sugli operatori della logistica\n *\n */\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { But<PERSON> } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Caricamento from \"../../utils/caricamento\";\nimport UtenteOPLogistica from \"../../aggiunta_dati/utenteOPLogistica\";\nimport AggiungiOPLogistica from \"../../aggiunta_dati/aggiungiOPLogistica\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport \"../../css/DataTableDemo.css\";\n\nclass GestioneLogisticaUtentiChain extends Component {\n  //Stato iniziale elementi tabella\n  emptyResult = {\n    id: null,\n    customerName: \"\",\n    address: \"\",\n    pIva: \"\",\n    email: \"\",\n    isValid: \"\",\n    createAt: \"\",\n    updateAt: \"\",\n  };\n  array = [];\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    this.state = {\n      results: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: true,\n    };\n    //Dichiarazione funzioni e metodi\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.aggiungiOpLogistica = this.aggiungiOpLogistica.bind(this);\n    this.hideaggiungiAffiliati = this.hideaggiungiAffiliati.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.addUser = this.addUser.bind(this);\n    this.hideUtenteAffiliato = this.hideUtenteAffiliato.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var res = await APIRequest(\"GET\", \"employees/?employeesEffort=true&role=LOGISTICA\");\n    this.setState({\n      results: res.data,\n      loading: false\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({ deleteResultDialog: false });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true,\n    });\n  }\n  //Apertura dialogo aggiunta utente\n  addUser(rowData, value) {\n    localStorage.setItem(\"datiComodo\", rowData.id);\n    this.setState({\n      resultDialog3: true,\n    });\n  }\n  //Chiusura dialogo aggiunta utente\n  hideUtenteAffiliato() {\n    this.setState({\n      resultDialog3: false,\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiOpLogistica() {\n    this.setState({\n      resultDialog2: true,\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAffiliati() {\n    this.setState({\n      resultDialog2: false,\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(\n      (val) => val.idemployee !== this.state.result.idemployee\n    );\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult,\n    });\n    let url = \"employees/?id=\" + this.state.result.idemployee;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data)\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Operatore di logistica eliminato con successo\",\n      life: 3000,\n    });\n    window.location.reload();\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideaggiungiAffiliati}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter3 = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideUtenteAffiliato}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = (\n      <React.Fragment>\n        <Button\n          label=\"No\"\n          icon=\"pi pi-times\"\n          className=\"p-button-text\"\n          onClick={this.hideDeleteResultDialog}\n        />\n        <Button className=\"p-button-text\" onClick={this.deleteResult}>\n          {\" \"}\n          {Costanti.Si}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    const fields = [\n      {\n        field: \"username\",\n        header: \"Username\",\n        body: \"username\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"first_name\",\n        header: Costanti.Nome,\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"address\",\n        header: Costanti.Indirizzo,\n        body: \"address\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"city\",\n        header: Costanti.Città,\n        body: \"city\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"cap\",\n        header: Costanti.CodPost,\n        body: \"cap\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"p_iva\",\n        header: Costanti.pIva,\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"tel\",\n        header: Costanti.Tel,\n        body: \"tel\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"email\",\n        header: Costanti.Email,\n        body: \"email\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"isValid\",\n        header: Costanti.Validità,\n        body: \"isValid\",\n        showHeader: true,\n      },\n    ];\n    const actionFields = [\n      { name: Costanti.addUser, icon: <i className=\"pi pi-user-plus\" />, handler: this.addUser },\n      { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n    ];\n    const items = [\n      {\n        label: Costanti.AggOpLog,\n        icon: 'pi pi-plus-circle',\n        command: () => {\n          this.aggiungiOpLogistica()\n        }\n      },\n    ]\n    return (\n      <div className=\"datatable-responsive-demo wrapper\">\n        {/* Il componente Toast permette di creare e visualizzare messaggi */}\n        <Toast ref={(el) => (this.toast = el)} />\n        {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n        <Nav />\n        <div className=\"col-12 px-0 solid-head\">\n          <h1>{Costanti.gestioneLogistica}</h1>\n        </div>\n        <div className=\"card\">\n          {/* Componente primereact per la creazione della tabella */}\n          <CustomDataTable\n            ref={(el) => (this.dt = el)}\n            value={this.state.results}\n            fields={fields}\n            loading={this.state.loading}\n            dataKey=\"id\"\n            paginator\n            rows={20}\n            rowsPerPageOptions={[10, 20, 50]}\n            actionsColumn={actionFields}\n            autoLayout={true}\n            splitButtonClass={true}\n            items={items}\n            selectionMode=\"single\"\n            cellSelection={true}\n            fileNames=\"OpLogistica\"\n          />\n        </div>\n        {/* Struttura dialogo per l'aggiunta utente */}\n        <Dialog\n          visible={this.state.resultDialog3}\n          header={Costanti.AggUtOpLog}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter3}\n          onHide={this.hideUtenteAffiliato}\n        >\n          <Caricamento />\n          <UtenteOPLogistica />\n        </Dialog>\n        {/* Struttura dialogo per l'aggiunta */}\n        <Dialog\n          visible={this.state.resultDialog2}\n          header={Costanti.SelAnagrafica}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter2}\n          onHide={this.hideaggiungiAffiliati}\n        >\n          <Caricamento />\n          <AggiungiOPLogistica />\n        </Dialog>\n        {/* Struttura dialogo per la cancellazione */}\n        <Dialog\n          visible={this.state.deleteResultDialog}\n          header={Costanti.Conferma}\n          modal\n          footer={deleteResultDialogFooter}\n          onHide={this.hideDeleteResultDialog}\n        >\n          <div className=\"confirmation-content\">\n            <i\n              className=\"pi pi-exclamation-triangle p-mr-3\"\n              style={{ fontSize: \"2rem\" }}\n            />\n            {this.state.result && (\n              <span>\n                {Costanti.ResDeleteCli} <b>{this.state.result.first_name}?</b>\n              </span>\n            )}\n          </div>\n        </Dialog>\n      </div>\n    );\n  }\n}\n\nexport default GestioneLogisticaUtentiChain;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,mBAAmB,MAAM,yCAAyC;AACzE,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,4BAA4B,SAASb,SAAS,CAAC;EAanDc,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ;IAdF;IAAA,KACAC,WAAW,GAAG;MACZC,EAAE,EAAE,IAAI;MACRC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;IAAA,KACDC,KAAK,GAAG,EAAE;IAIR,IAAI,CAACC,KAAK,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,IAAI,CAACjB,WAAW;MACxBkB,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;IACX,CAAC;IACD;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACF,IAAI,CAAC,IAAI,CAAC;IAClE,IAAI,CAACG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACI,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO,CAACL,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACN,IAAI,CAAC,IAAI,CAAC;EAChE;EACA;EACA,MAAMO,iBAAiBA,CAAA,EAAG;IACxB,IAAIC,GAAG,GAAG,MAAMxC,UAAU,CAAC,KAAK,EAAE,gDAAgD,CAAC;IACnF,IAAI,CAACyC,QAAQ,CAAC;MACZnB,OAAO,EAAEkB,GAAG,CAACE,IAAI;MACjBZ,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACA;EACAM,sBAAsBA,CAAA,EAAG;IACvB,IAAI,CAACK,QAAQ,CAAC;MAAEf,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAC9C;EACA;EACAK,mBAAmBA,CAACH,MAAM,EAAE;IAC1B,IAAI,CAACa,QAAQ,CAAC;MACZb,MAAM;MACNF,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;EACA;EACAW,OAAOA,CAACM,OAAO,EAAEC,KAAK,EAAE;IACtBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEH,OAAO,CAAC/B,EAAE,CAAC;IAC9C,IAAI,CAAC6B,QAAQ,CAAC;MACZhB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAa,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACG,QAAQ,CAAC;MACZhB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAQ,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACQ,QAAQ,CAAC;MACZjB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAU,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAACO,QAAQ,CAAC;MACZjB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACA,MAAMW,YAAYA,CAAA,EAAG;IACnB,IAAIb,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAACyB,MAAM,CACpCC,GAAG,IAAKA,GAAG,CAACC,UAAU,KAAK,IAAI,CAAC5B,KAAK,CAACO,MAAM,CAACqB,UAChD,CAAC;IACD,IAAI,CAACR,QAAQ,CAAC;MACZnB,OAAO;MACPI,kBAAkB,EAAE,KAAK;MACzBE,MAAM,EAAE,IAAI,CAACjB;IACf,CAAC,CAAC;IACF,IAAIuC,GAAG,GAAG,gBAAgB,GAAG,IAAI,CAAC7B,KAAK,CAACO,MAAM,CAACqB,UAAU;IACzD,IAAIT,GAAG,GAAG,MAAMxC,UAAU,CAAC,QAAQ,EAAEkD,GAAG,CAAC;IACzCC,OAAO,CAACC,GAAG,CAACZ,GAAG,CAACE,IAAI,CAAC;IACrB,IAAI,CAACW,KAAK,CAACC,IAAI,CAAC;MACdC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,YAAY;MACrBC,MAAM,EAAE,+CAA+C;MACvDC,IAAI,EAAE;IACR,CAAC,CAAC;IACFC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACP;IACA,MAAMC,mBAAmB,gBACvBxD,OAAA,CAACb,KAAK,CAACsE,QAAQ;MAAAC,QAAA,eACb1D,OAAA,CAACV,MAAM;QAACqE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACjC,qBAAsB;QAAA+B,QAAA,GACnE,GAAG,EACHlE,QAAQ,CAACqE,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAMC,mBAAmB,gBACvBlE,OAAA,CAACb,KAAK,CAACsE,QAAQ;MAAAC,QAAA,eACb1D,OAAA,CAACV,MAAM;QAACqE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC7B,mBAAoB;QAAA2B,QAAA,GACjE,GAAG,EACHlE,QAAQ,CAACqE,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAME,wBAAwB,gBAC5BnE,OAAA,CAACb,KAAK,CAACsE,QAAQ;MAAAC,QAAA,gBACb1D,OAAA,CAACV,MAAM;QACL8E,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBV,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAAC/B;MAAuB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACFjE,OAAA,CAACV,MAAM;QAACqE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAChC,YAAa;QAAA8B,QAAA,GAC1D,GAAG,EACHlE,QAAQ,CAAC8E,EAAE,EAAE,GAAG;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD,MAAMM,MAAM,GAAG,CACb;MACEC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,YAAY;MACnBC,MAAM,EAAEjF,QAAQ,CAACqF,IAAI;MACrBF,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAEjF,QAAQ,CAACsF,SAAS;MAC1BJ,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAEjF,QAAQ,CAACuF,KAAK;MACtBL,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,KAAK;MACZC,MAAM,EAAEjF,QAAQ,CAACwF,OAAO;MACxBN,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,OAAO;MACdC,MAAM,EAAEjF,QAAQ,CAACgB,IAAI;MACrBmE,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,KAAK;MACZC,MAAM,EAAEjF,QAAQ,CAACyF,GAAG;MACpBP,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,OAAO;MACdC,MAAM,EAAEjF,QAAQ,CAAC0F,KAAK;MACtBR,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAEjF,QAAQ,CAAC2F,QAAQ;MACzBT,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IACd,CAAC,CACF;IACD,MAAMQ,YAAY,GAAG,CACnB;MAAEC,IAAI,EAAE7F,QAAQ,CAACsC,OAAO;MAAEuC,IAAI,eAAErE,OAAA;QAAG2D,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEqB,OAAO,EAAE,IAAI,CAACxD;IAAQ,CAAC,EAC1F;MAAEuD,IAAI,EAAE7F,QAAQ,CAAC+F,OAAO;MAAElB,IAAI,eAAErE,OAAA;QAAG2D,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEqB,OAAO,EAAE,IAAI,CAAC9D;IAAoB,CAAC,CACnG;IACD,MAAMgE,KAAK,GAAG,CACZ;MACEpB,KAAK,EAAE5E,QAAQ,CAACiG,QAAQ;MACxBpB,IAAI,EAAE,mBAAmB;MACzBqB,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAAChE,mBAAmB,CAAC,CAAC;MAC5B;IACF,CAAC,CACF;IACD,oBACE1B,OAAA;MAAK2D,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAEhD1D,OAAA,CAACX,KAAK;QAACsG,GAAG,EAAGC,EAAE,IAAM,IAAI,CAAC9C,KAAK,GAAG8C;MAAI;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzCjE,OAAA,CAACN,GAAG;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPjE,OAAA;QAAK2D,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC1D,OAAA;UAAA0D,QAAA,EAAKlE,QAAQ,CAACqG;QAAiB;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNjE,OAAA;QAAK2D,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEnB1D,OAAA,CAACF,eAAe;UACd6F,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACE,EAAE,GAAGF,EAAI;UAC5BvD,KAAK,EAAE,IAAI,CAACvB,KAAK,CAACC,OAAQ;UAC1BwD,MAAM,EAAEA,MAAO;UACfhD,OAAO,EAAE,IAAI,CAACT,KAAK,CAACS,OAAQ;UAC5BwE,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEf,YAAa;UAC5BgB,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBb,KAAK,EAAEA,KAAM;UACbc,aAAa,EAAC,QAAQ;UACtBC,aAAa,EAAE,IAAK;UACpBC,SAAS,EAAC;QAAa;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjE,OAAA,CAACT,MAAM;QACLkH,OAAO,EAAE,IAAI,CAAC3F,KAAK,CAACI,aAAc;QAClCuD,MAAM,EAAEjF,QAAQ,CAACkH,UAAW;QAC5BC,KAAK;QACLhD,SAAS,EAAC,kBAAkB;QAC5BiD,MAAM,EAAE1C,mBAAoB;QAC5B2C,MAAM,EAAE,IAAI,CAAC9E,mBAAoB;QAAA2B,QAAA,gBAEjC1D,OAAA,CAACL,WAAW;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfjE,OAAA,CAACJ,iBAAiB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAETjE,OAAA,CAACT,MAAM;QACLkH,OAAO,EAAE,IAAI,CAAC3F,KAAK,CAACG,aAAc;QAClCwD,MAAM,EAAEjF,QAAQ,CAACsH,aAAc;QAC/BH,KAAK;QACLhD,SAAS,EAAC,kBAAkB;QAC5BiD,MAAM,EAAEpD,mBAAoB;QAC5BqD,MAAM,EAAE,IAAI,CAAClF,qBAAsB;QAAA+B,QAAA,gBAEnC1D,OAAA,CAACL,WAAW;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfjE,OAAA,CAACH,mBAAmB;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAETjE,OAAA,CAACT,MAAM;QACLkH,OAAO,EAAE,IAAI,CAAC3F,KAAK,CAACK,kBAAmB;QACvCsD,MAAM,EAAEjF,QAAQ,CAACuH,QAAS;QAC1BJ,KAAK;QACLC,MAAM,EAAEzC,wBAAyB;QACjC0C,MAAM,EAAE,IAAI,CAAChF,sBAAuB;QAAA6B,QAAA,eAEpC1D,OAAA;UAAK2D,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnC1D,OAAA;YACE2D,SAAS,EAAC,mCAAmC;YAC7CqD,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,EACD,IAAI,CAACnD,KAAK,CAACO,MAAM,iBAChBrB,OAAA;YAAA0D,QAAA,GACGlE,QAAQ,CAAC0H,YAAY,EAAC,GAAC,eAAAlH,OAAA;cAAA0D,QAAA,GAAI,IAAI,CAAC5C,KAAK,CAACO,MAAM,CAAC8F,UAAU,EAAC,GAAC;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;AACF;AAEA,eAAehE,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}