{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Button from '../button';\nexport default function PickerButton(props) {\n  return /*#__PURE__*/React.createElement(But<PERSON>, _extends({\n    size: \"small\",\n    type: \"primary\"\n  }, props));\n}", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "size", "type"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/date-picker/PickerButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Button from '../button';\nexport default function PickerButton(props) {\n  return /*#__PURE__*/React.createElement(But<PERSON>, _extends({\n    size: \"small\",\n    type: \"primary\"\n  }, props));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC1C,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,MAAM,EAAEF,QAAQ,CAAC;IACvDM,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;EACR,CAAC,EAAEH,KAAK,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}