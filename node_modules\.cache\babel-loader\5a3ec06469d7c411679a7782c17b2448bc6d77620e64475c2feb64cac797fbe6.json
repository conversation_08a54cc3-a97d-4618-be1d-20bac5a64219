{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\agenti\\\\listinoAgente.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ListinoAgente - visualizzazione del listino per l'agente \n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Caricamento from '../../utils/caricamento';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Nav from \"../../components/navigation/Nav\";\nimport NavAgenteOrdini from './navIcon';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { agenteGestioneClientiAgente } from '../../components/route';\nimport '../../css/ToastDemo.css';\nimport '../../css/DataViewDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiOrdineAgente = () => {\n  _s();\n  const [results, setResults] = useState(null);\n  const [results2, setResults2] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function fetchData() {\n      var idRetailer = JSON.parse(localStorage.getItem(\"datiComodo\")).id;\n      if (idRetailer === '0') {\n        window.location.pathname = agenteGestioneClientiAgente;\n      }\n      var url = \"pricelistretailer?idRetailer=\" + idRetailer;\n      await APIRequest('GET', url).then(data => {\n        var datasource = data.data.idPriceList2.priceListProducts;\n        setResults(datasource);\n        setResults2(datasource);\n        setLoading(false);\n      }).catch(async () => {\n        await APIRequest('GET', 'pricelistaffiliate/').then(data => {\n          var datasource = data.data[0].idPriceList2.priceListProducts;\n          setResults(datasource);\n          setResults2(datasource);\n          setLoading(false);\n        }).catch(e => {\n          var _e$response, _e$response2;\n          toast.current.show({\n            severity: 'error',\n            summary: 'Attenzione',\n            detail: \"Al momento non \\xE8 disponibile un listino per il cliente selezionato. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.pathname = agenteGestioneClientiAgente;\n          }, 3000);\n        });\n      });\n    }\n    fetchData();\n  }, []);\n  var dati = [];\n  if (localStorage.getItem(\"DatiConsegna\") !== '') {\n    dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dataview-demo creaOrdine\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dataview-demo creaOrdine\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 px-0 solid-head\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: [Costanti.CreaOrdinePDV, /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-center d-block subtitle\",\n          children: dati.firstName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MarketplaceGen, {\n      results: results,\n      results2: results2,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row bg-white border-top pt-3 mt-0 sticky-bottom steppingOrderDetail\",\n      children: /*#__PURE__*/_jsxDEV(NavAgenteOrdini, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiOrdineAgente, \"VGRDdzdI/RAA7GW1rsYIp9ubYTg=\");\n_c = AggiungiOrdineAgente;\nexport default AggiungiOrdineAgente;\nvar _c;\n$RefreshReg$(_c, \"AggiungiOrdineAgente\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Caricamento", "MarketplaceGen", "Nav", "NavAgenteOrdini", "Toast", "<PERSON><PERSON>", "APIRequest", "agenteGestioneClientiAgente", "jsxDEV", "_jsxDEV", "AggiungiOrdineAgente", "_s", "results", "setResults", "results2", "setResults2", "loading", "setLoading", "toast", "fetchData", "idRetailer", "JSON", "parse", "localStorage", "getItem", "id", "window", "location", "pathname", "url", "then", "data", "datasource", "idPriceList2", "priceListProducts", "catch", "e", "_e$response", "_e$response2", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "setTimeout", "dati", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "CreaOrdinePDV", "firstName", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/agenti/listinoAgente.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ListinoAgente - visualizzazione del listino per l'agente \n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Caricamento from '../../utils/caricamento';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Nav from \"../../components/navigation/Nav\";\nimport NavAgenteOrdini from './navIcon';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { agenteGestioneClientiAgente } from '../../components/route';\nimport '../../css/ToastDemo.css';\nimport '../../css/DataViewDemo.css';\n\nconst AggiungiOrdineAgente = () => {\n    const [results, setResults] = useState(null)\n    const [results2, setResults2] = useState(null)\n    const [loading, setLoading] = useState(true)\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function fetchData() {\n            var idRetailer = JSON.parse(localStorage.getItem(\"datiComodo\")).id;\n            if (idRetailer === '0') {\n                window.location.pathname = agenteGestioneClientiAgente\n            }\n            var url = \"pricelistretailer?idRetailer=\" + idRetailer;\n            await APIRequest('GET', url)\n                .then(data => {\n                    var datasource = data.data.idPriceList2.priceListProducts;\n                    setResults(datasource)\n                    setResults2(datasource)\n                    setLoading(false)\n                }).catch(async () => {\n                    await APIRequest('GET', 'pricelistaffiliate/')\n                        .then(data => {\n                            var datasource = data.data[0].idPriceList2.priceListProducts;\n                            setResults(datasource)\n                            setResults2(datasource)\n                            setLoading(false)\n                        }).catch((e) => {\n                            toast.current.show({ severity: 'error', summary: 'Attenzione', detail: `Al momento non è disponibile un listino per il cliente selezionato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                            setTimeout(() => {\n                                window.location.pathname = agenteGestioneClientiAgente;\n                            }, 3000)\n                        })\n                })\n        }\n        fetchData()\n    }, []);\n    var dati = []\n    if (localStorage.getItem(\"DatiConsegna\") !== '') {\n        dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"))\n    }\n    if (loading) {\n        return <div className=\"dataview-demo creaOrdine\">\n            <Toast ref={toast} />\n            <Caricamento />\n        </div>\n    }\n    return (\n        <div className=\"dataview-demo creaOrdine\">\n            <Toast ref={toast} />\n            <Nav />\n            <div className=\"col-12 px-0 solid-head\">\n                <h1>\n                    {Costanti.CreaOrdinePDV}\n                    <span className=\"text-center d-block subtitle\">{dati.firstName}</span>\n                </h1>\n            </div>\n            <MarketplaceGen results={results} results2={results2} loading={loading} />\n            <div className=\"row bg-white border-top pt-3 mt-0 sticky-bottom steppingOrderDetail\">\n                <NavAgenteOrdini />\n            </div>\n        </div>\n    )\n}\n\nexport default AggiungiOrdineAgente;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,cAAc,MAAM,2DAA2D;AACtF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,WAAW;AACvC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,OAAO,yBAAyB;AAChC,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMmB,KAAK,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZ,eAAesB,SAASA,CAAA,EAAG;MACvB,IAAIC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACC,EAAE;MAClE,IAAIL,UAAU,KAAK,GAAG,EAAE;QACpBM,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGrB,2BAA2B;MAC1D;MACA,IAAIsB,GAAG,GAAG,+BAA+B,GAAGT,UAAU;MACtD,MAAMd,UAAU,CAAC,KAAK,EAAEuB,GAAG,CAAC,CACvBC,IAAI,CAACC,IAAI,IAAI;QACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAACE,YAAY,CAACC,iBAAiB;QACzDrB,UAAU,CAACmB,UAAU,CAAC;QACtBjB,WAAW,CAACiB,UAAU,CAAC;QACvBf,UAAU,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,CAACkB,KAAK,CAAC,YAAY;QACjB,MAAM7B,UAAU,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACzCwB,IAAI,CAACC,IAAI,IAAI;UACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,CAACC,iBAAiB;UAC5DrB,UAAU,CAACmB,UAAU,CAAC;UACtBjB,WAAW,CAACiB,UAAU,CAAC;UACvBf,UAAU,CAAC,KAAK,CAAC;QACrB,CAAC,CAAC,CAACkB,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAC,WAAA,EAAAC,YAAA;UACZpB,KAAK,CAACqB,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,YAAY;YAAEC,MAAM,8FAAAC,MAAA,CAA2F,EAAAP,WAAA,GAAAD,CAAC,CAACS,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAYN,IAAI,MAAKe,SAAS,IAAAR,YAAA,GAAGF,CAAC,CAACS,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAYP,IAAI,GAAGK,CAAC,CAACW,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UAC9OC,UAAU,CAAC,MAAM;YACbvB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGrB,2BAA2B;UAC1D,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC;MACV,CAAC,CAAC;IACV;IACAY,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAI+B,IAAI,GAAG,EAAE;EACb,IAAI3B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE;IAC7C0B,IAAI,GAAG7B,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;EAC3D;EACA,IAAIR,OAAO,EAAE;IACT,oBAAOP,OAAA;MAAK0C,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAC5C3C,OAAA,CAACL,KAAK;QAACiD,GAAG,EAAEnC;MAAM;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBhD,OAAA,CAACT,WAAW;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EACV;EACA,oBACIhD,OAAA;IAAK0C,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACrC3C,OAAA,CAACL,KAAK;MAACiD,GAAG,EAAEnC;IAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBhD,OAAA,CAACP,GAAG;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACPhD,OAAA;MAAK0C,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACnC3C,OAAA;QAAA2C,QAAA,GACK/C,QAAQ,CAACqD,aAAa,eACvBjD,OAAA;UAAM0C,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAEF,IAAI,CAACS;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNhD,OAAA,CAACR,cAAc;MAACW,OAAO,EAAEA,OAAQ;MAACE,QAAQ,EAAEA,QAAS;MAACE,OAAO,EAAEA;IAAQ;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1EhD,OAAA;MAAK0C,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAChF3C,OAAA,CAACN,eAAe;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA9C,EAAA,CA9DKD,oBAAoB;AAAAkD,EAAA,GAApBlD,oBAAoB;AAgE1B,eAAeA,oBAAoB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}