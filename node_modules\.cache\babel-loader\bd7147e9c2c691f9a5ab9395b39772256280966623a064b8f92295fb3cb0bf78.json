{"ast": null, "code": "/* istanbul ignore next */\n\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\n// eslint-disable-next-line no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;", "map": {"version": 3, "names": ["Column", "_"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/Column.js"], "sourcesContent": ["/* istanbul ignore next */\n\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\n// eslint-disable-next-line no-unused-vars\nfunction Column(_) {\n  return null;\n}\n\nexport default Column;"], "mappings": "AAAA;;AAEA;AACA;AACA,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,OAAO,IAAI;AACb;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}