/**
 * Script per rimuovere tutti gli indicatori di backend ridondanti
 * Questo script pulisce la pagina da elementi duplicati
 */

// Funzione per rimuovere l'indicatore fisso
function removeBackendStatusIndicator() {
    const indicator = document.getElementById('backend-status-indicator');
    if (indicator) {
        indicator.remove();
        console.log('✅ Rimosso indicatore di stato backend fisso');
        return true;
    }
    return false;
}

// Funzione per rimuovere tutte le notifiche popup
function removeBackendNotifications() {
    // Cerca tutti gli elementi che potrebbero essere notifiche
    const notifications = document.querySelectorAll('div[style*="position: fixed"][style*="top:"][style*="right:"]');
    let removed = 0;
    
    notifications.forEach(notification => {
        // Controlla se contiene testo relativo al backend
        const text = notification.textContent || notification.innerText;
        if (text.includes('Backend') || text.includes('backend') || text.includes('Connected') || text.includes('Offline')) {
            notification.remove();
            removed++;
        }
    });
    
    if (removed > 0) {
        console.log(`✅ Rimosse ${removed} notifiche backend`);
    }
    
    return removed;
}

// Funzione per pulire tutto
function cleanupBackendIndicators() {
    console.log('🧹 Pulizia indicatori backend...');
    
    const indicatorRemoved = removeBackendStatusIndicator();
    const notificationsRemoved = removeBackendNotifications();
    
    if (indicatorRemoved || notificationsRemoved > 0) {
        console.log('✅ Pulizia completata');
    } else {
        console.log('ℹ️ Nessun indicatore da rimuovere');
    }
}

// Esegui la pulizia
cleanupBackendIndicators();

// Monitora per nuovi elementi che potrebbero essere aggiunti
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
                // Controlla se è l'indicatore di stato
                if (node.id === 'backend-status-indicator') {
                    console.log('⚠️ Rilevato nuovo indicatore di stato, rimozione...');
                    node.remove();
                }
                
                // Controlla se è una notifica backend
                if (node.style && 
                    node.style.position === 'fixed' && 
                    (node.textContent.includes('Backend') || node.textContent.includes('backend'))) {
                    console.log('⚠️ Rilevata nuova notifica backend, rimozione...');
                    node.remove();
                }
            }
        });
    });
});

// Avvia il monitoraggio
observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log('👀 Monitoraggio attivo per prevenire nuovi indicatori ridondanti');

// Esporta le funzioni per uso manuale
if (typeof window !== 'undefined') {
    window.cleanupBackendIndicators = cleanupBackendIndicators;
    window.removeBackendStatusIndicator = removeBackendStatusIndicator;
    window.removeBackendNotifications = removeBackendNotifications;
}
