{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from '../Header';\nimport { DECADE_DISTANCE_COUNT } from '.';\nimport PanelContext from '../../PanelContext';\nfunction DecadeHeader(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    viewDate = props.viewDate,\n    onPrevDecades = props.onPrevDecades,\n    onNextDecades = props.onNextDecades;\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / DECADE_DISTANCE_COUNT) * DECADE_DISTANCE_COUNT;\n  var endYear = startYear + DECADE_DISTANCE_COUNT - 1;\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevDecades,\n    onSuperNext: onNextDecades\n  }), startYear, \"-\", endYear);\n}\nexport default DecadeHeader;", "map": {"version": 3, "names": ["_extends", "React", "Header", "DECADE_DISTANCE_COUNT", "PanelContext", "DecadeH<PERSON>er", "props", "prefixCls", "generateConfig", "viewDate", "onPrevDecades", "onNextDecades", "_React$useContext", "useContext", "<PERSON><PERSON>ead<PERSON>", "headerPrefixCls", "concat", "yearNumber", "getYear", "startYear", "Math", "floor", "endYear", "createElement", "onSuperPrev", "onSuperNext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-picker/es/panels/DecadePanel/DecadeHeader.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from '../Header';\nimport { DECADE_DISTANCE_COUNT } from '.';\nimport PanelContext from '../../PanelContext';\n\nfunction DecadeHeader(props) {\n  var prefixCls = props.prefixCls,\n      generateConfig = props.generateConfig,\n      viewDate = props.viewDate,\n      onPrevDecades = props.onPrevDecades,\n      onNextDecades = props.onNextDecades;\n\n  var _React$useContext = React.useContext(PanelContext),\n      hideHeader = _React$useContext.hideHeader;\n\n  if (hideHeader) {\n    return null;\n  }\n\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / DECADE_DISTANCE_COUNT) * DECADE_DISTANCE_COUNT;\n  var endYear = startYear + DECADE_DISTANCE_COUNT - 1;\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevDecades,\n    onSuperNext: onNextDecades\n  }), startYear, \"-\", endYear);\n}\n\nexport default DecadeHeader;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,qBAAqB,QAAQ,GAAG;AACzC,OAAOC,YAAY,MAAM,oBAAoB;AAE7C,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,aAAa,GAAGL,KAAK,CAACK,aAAa;EAEvC,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACT,YAAY,CAAC;IAClDU,UAAU,GAAGF,iBAAiB,CAACE,UAAU;EAE7C,IAAIA,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,SAAS,CAAC;EACrD,IAAIU,UAAU,GAAGT,cAAc,CAACU,OAAO,CAACT,QAAQ,CAAC;EACjD,IAAIU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAGd,qBAAqB,CAAC,GAAGA,qBAAqB;EACtF,IAAImB,OAAO,GAAGH,SAAS,GAAGhB,qBAAqB,GAAG,CAAC;EACnD,OAAO,aAAaF,KAAK,CAACsB,aAAa,CAACrB,MAAM,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IAClEC,SAAS,EAAEQ,eAAe;IAC1BS,WAAW,EAAEd,aAAa;IAC1Be,WAAW,EAAEd;EACf,CAAC,CAAC,EAAEQ,SAAS,EAAE,GAAG,EAAEG,OAAO,CAAC;AAC9B;AAEA,eAAejB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}