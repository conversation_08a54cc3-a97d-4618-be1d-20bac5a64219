{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { ConfigContext } from '../config-provider';\nimport Item from './Item';\nimport useFlexGapSupport from '../_util/hooks/useFlexGapSupport';\nexport var SpaceContext = /*#__PURE__*/React.createContext({\n  latestIndex: 0,\n  horizontalSize: 0,\n  verticalSize: 0,\n  supportFlexGap: false\n});\nvar spaceSize = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\nfunction getNumberSize(size) {\n  return typeof size === 'string' ? spaceSize[size] : size || 0;\n}\nvar Space = function Space(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    space = _React$useContext.space,\n    directionConfig = _React$useContext.direction;\n  var _props$size = props.size,\n    size = _props$size === void 0 ? (space === null || space === void 0 ? void 0 : space.size) || 'small' : _props$size,\n    align = props.align,\n    className = props.className,\n    children = props.children,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'horizontal' : _props$direction,\n    customizePrefixCls = props.prefixCls,\n    split = props.split,\n    style = props.style,\n    _props$wrap = props.wrap,\n    wrap = _props$wrap === void 0 ? false : _props$wrap,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\"]);\n  var supportFlexGap = useFlexGapSupport();\n  var _React$useMemo = React.useMemo(function () {\n      return (Array.isArray(size) ? size : [size, size]).map(function (item) {\n        return getNumberSize(item);\n      });\n    }, [size]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    horizontalSize = _React$useMemo2[0],\n    verticalSize = _React$useMemo2[1];\n  var childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  var mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  var prefixCls = getPrefixCls('space', customizePrefixCls);\n  var cn = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), directionConfig === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-align-\").concat(mergedAlign), mergedAlign), _classNames), className);\n  var itemClassName = \"\".concat(prefixCls, \"-item\");\n  var marginDirection = directionConfig === 'rtl' ? 'marginLeft' : 'marginRight'; // Calculate latest one\n\n  var latestIndex = 0;\n  var nodes = childNodes.map(function (child, i) {\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    var keyOfChild = child && child.key; // Add `-space-item` as suffix in case simple key string trigger duplicated key warning\n    // https://github.com/ant-design/ant-design/issues/35305\n\n    var defaultKey = \"\".concat(i, \"-space-item\");\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: \"\".concat(itemClassName, \"-\").concat(keyOfChild || defaultKey),\n      direction: direction,\n      index: i,\n      marginDirection: marginDirection,\n      split: split,\n      wrap: wrap\n    }, child);\n  });\n  var spaceContext = React.useMemo(function () {\n    return {\n      horizontalSize: horizontalSize,\n      verticalSize: verticalSize,\n      latestIndex: latestIndex,\n      supportFlexGap: supportFlexGap\n    };\n  }, [horizontalSize, verticalSize, latestIndex, supportFlexGap]); // =========================== Render ===========================\n\n  if (childNodes.length === 0) {\n    return null;\n  }\n  var gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap'; // Patch for gap not support\n\n    if (!supportFlexGap) {\n      gapStyle.marginBottom = -verticalSize;\n    }\n  }\n  if (supportFlexGap) {\n    gapStyle.columnGap = horizontalSize;\n    gapStyle.rowGap = verticalSize;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: cn,\n    style: _extends(_extends({}, gapStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContext.Provider, {\n    value: spaceContext\n  }, nodes));\n};\nexport default Space;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "toArray", "ConfigContext", "<PERSON><PERSON>", "useFlexGapSupport", "SpaceContext", "createContext", "latestIndex", "horizontalSize", "verticalSize", "supportFlexGap", "spaceSize", "small", "middle", "large", "getNumberSize", "size", "Space", "props", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "space", "directionConfig", "direction", "_props$size", "align", "className", "children", "_props$direction", "customizePrefixCls", "prefixCls", "split", "style", "_props$wrap", "wrap", "otherProps", "_React$useMemo", "useMemo", "Array", "isArray", "map", "item", "_React$useMemo2", "childNodes", "keepEmpty", "mergedAlign", "undefined", "cn", "concat", "itemClassName", "marginDirection", "nodes", "child", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "defaultKey", "createElement", "index", "spaceContext", "gapStyle", "flexWrap", "marginBottom", "columnGap", "rowGap", "Provider", "value"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/space/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { ConfigContext } from '../config-provider';\nimport Item from './Item';\nimport useFlexGapSupport from '../_util/hooks/useFlexGapSupport';\nexport var SpaceContext = /*#__PURE__*/React.createContext({\n  latestIndex: 0,\n  horizontalSize: 0,\n  verticalSize: 0,\n  supportFlexGap: false\n});\nvar spaceSize = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\n\nfunction getNumberSize(size) {\n  return typeof size === 'string' ? spaceSize[size] : size || 0;\n}\n\nvar Space = function Space(props) {\n  var _classNames;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      space = _React$useContext.space,\n      directionConfig = _React$useContext.direction;\n\n  var _props$size = props.size,\n      size = _props$size === void 0 ? (space === null || space === void 0 ? void 0 : space.size) || 'small' : _props$size,\n      align = props.align,\n      className = props.className,\n      children = props.children,\n      _props$direction = props.direction,\n      direction = _props$direction === void 0 ? 'horizontal' : _props$direction,\n      customizePrefixCls = props.prefixCls,\n      split = props.split,\n      style = props.style,\n      _props$wrap = props.wrap,\n      wrap = _props$wrap === void 0 ? false : _props$wrap,\n      otherProps = __rest(props, [\"size\", \"align\", \"className\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\"]);\n\n  var supportFlexGap = useFlexGapSupport();\n\n  var _React$useMemo = React.useMemo(function () {\n    return (Array.isArray(size) ? size : [size, size]).map(function (item) {\n      return getNumberSize(item);\n    });\n  }, [size]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      horizontalSize = _React$useMemo2[0],\n      verticalSize = _React$useMemo2[1];\n\n  var childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  var mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  var prefixCls = getPrefixCls('space', customizePrefixCls);\n  var cn = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), directionConfig === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-align-\").concat(mergedAlign), mergedAlign), _classNames), className);\n  var itemClassName = \"\".concat(prefixCls, \"-item\");\n  var marginDirection = directionConfig === 'rtl' ? 'marginLeft' : 'marginRight'; // Calculate latest one\n\n  var latestIndex = 0;\n  var nodes = childNodes.map(function (child, i) {\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n\n    var keyOfChild = child && child.key; // Add `-space-item` as suffix in case simple key string trigger duplicated key warning\n    // https://github.com/ant-design/ant-design/issues/35305\n\n    var defaultKey = \"\".concat(i, \"-space-item\");\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: \"\".concat(itemClassName, \"-\").concat(keyOfChild || defaultKey),\n      direction: direction,\n      index: i,\n      marginDirection: marginDirection,\n      split: split,\n      wrap: wrap\n    }, child);\n  });\n  var spaceContext = React.useMemo(function () {\n    return {\n      horizontalSize: horizontalSize,\n      verticalSize: verticalSize,\n      latestIndex: latestIndex,\n      supportFlexGap: supportFlexGap\n    };\n  }, [horizontalSize, verticalSize, latestIndex, supportFlexGap]); // =========================== Render ===========================\n\n  if (childNodes.length === 0) {\n    return null;\n  }\n\n  var gapStyle = {};\n\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap'; // Patch for gap not support\n\n    if (!supportFlexGap) {\n      gapStyle.marginBottom = -verticalSize;\n    }\n  }\n\n  if (supportFlexGap) {\n    gapStyle.columnGap = horizontalSize;\n    gapStyle.rowGap = verticalSize;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: cn,\n    style: _extends(_extends({}, gapStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContext.Provider, {\n    value: spaceContext\n  }, nodes));\n};\n\nexport default Space;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AAErE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAO,IAAIC,YAAY,GAAG,aAAaN,KAAK,CAACO,aAAa,CAAC;EACzDC,WAAW,EAAE,CAAC;EACdC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE;AAClB,CAAC,CAAC;AACF,IAAIC,SAAS,GAAG;EACdC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AAED,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAGL,SAAS,CAACK,IAAI,CAAC,GAAGA,IAAI,IAAI,CAAC;AAC/D;AAEA,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,WAAW;EAEf,IAAIC,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU,CAACnB,aAAa,CAAC;IACnDoB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,KAAK,GAAGH,iBAAiB,CAACG,KAAK;IAC/BC,eAAe,GAAGJ,iBAAiB,CAACK,SAAS;EAEjD,IAAIC,WAAW,GAAGR,KAAK,CAACF,IAAI;IACxBA,IAAI,GAAGU,WAAW,KAAK,KAAK,CAAC,GAAG,CAACH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACP,IAAI,KAAK,OAAO,GAAGU,WAAW;IACnHC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,gBAAgB,GAAGZ,KAAK,CAACO,SAAS;IAClCA,SAAS,GAAGK,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEC,kBAAkB,GAAGb,KAAK,CAACc,SAAS;IACpCC,KAAK,GAAGf,KAAK,CAACe,KAAK;IACnBC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,WAAW,GAAGjB,KAAK,CAACkB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACnDE,UAAU,GAAGpD,MAAM,CAACiC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAE9H,IAAIR,cAAc,GAAGN,iBAAiB,CAAC,CAAC;EAExC,IAAIkC,cAAc,GAAGvC,KAAK,CAACwC,OAAO,CAAC,YAAY;MAC7C,OAAO,CAACC,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC,EAAE0B,GAAG,CAAC,UAAUC,IAAI,EAAE;QACrE,OAAO5B,aAAa,CAAC4B,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC3B,IAAI,CAAC,CAAC;IACN4B,eAAe,GAAG5D,cAAc,CAACsD,cAAc,EAAE,CAAC,CAAC;IACnD9B,cAAc,GAAGoC,eAAe,CAAC,CAAC,CAAC;IACnCnC,YAAY,GAAGmC,eAAe,CAAC,CAAC,CAAC;EAErC,IAAIC,UAAU,GAAG5C,OAAO,CAAC4B,QAAQ,EAAE;IACjCiB,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAIC,WAAW,GAAGpB,KAAK,KAAKqB,SAAS,IAAIvB,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAGE,KAAK;EACtF,IAAIK,SAAS,GAAGV,YAAY,CAAC,OAAO,EAAES,kBAAkB,CAAC;EACzD,IAAIkB,EAAE,GAAGjD,UAAU,CAACgC,SAAS,EAAE,EAAE,CAACkB,MAAM,CAAClB,SAAS,EAAE,GAAG,CAAC,CAACkB,MAAM,CAACzB,SAAS,CAAC,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAEpC,eAAe,CAACoC,WAAW,EAAE,EAAE,CAAC+B,MAAM,CAAClB,SAAS,EAAE,MAAM,CAAC,EAAER,eAAe,KAAK,KAAK,CAAC,EAAEzC,eAAe,CAACoC,WAAW,EAAE,EAAE,CAAC+B,MAAM,CAAClB,SAAS,EAAE,SAAS,CAAC,CAACkB,MAAM,CAACH,WAAW,CAAC,EAAEA,WAAW,CAAC,EAAE5B,WAAW,GAAGS,SAAS,CAAC;EAC9S,IAAIuB,aAAa,GAAG,EAAE,CAACD,MAAM,CAAClB,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIoB,eAAe,GAAG5B,eAAe,KAAK,KAAK,GAAG,YAAY,GAAG,aAAa,CAAC,CAAC;;EAEhF,IAAIjB,WAAW,GAAG,CAAC;EACnB,IAAI8C,KAAK,GAAGR,UAAU,CAACH,GAAG,CAAC,UAAUY,KAAK,EAAE1D,CAAC,EAAE;IAC7C,IAAI0D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKN,SAAS,EAAE;MACzCzC,WAAW,GAAGX,CAAC;IACjB;IAEA,IAAI2D,UAAU,GAAGD,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC,CAAC;IACrC;;IAEA,IAAIC,UAAU,GAAG,EAAE,CAACP,MAAM,CAACtD,CAAC,EAAE,aAAa,CAAC;IAC5C,OAAO,aAAaG,KAAK,CAAC2D,aAAa,CAACvD,IAAI,EAAE;MAC5CyB,SAAS,EAAEuB,aAAa;MACxBK,GAAG,EAAE,EAAE,CAACN,MAAM,CAACC,aAAa,EAAE,GAAG,CAAC,CAACD,MAAM,CAACK,UAAU,IAAIE,UAAU,CAAC;MACnEhC,SAAS,EAAEA,SAAS;MACpBkC,KAAK,EAAE/D,CAAC;MACRwD,eAAe,EAAEA,eAAe;MAChCnB,KAAK,EAAEA,KAAK;MACZG,IAAI,EAAEA;IACR,CAAC,EAAEkB,KAAK,CAAC;EACX,CAAC,CAAC;EACF,IAAIM,YAAY,GAAG7D,KAAK,CAACwC,OAAO,CAAC,YAAY;IAC3C,OAAO;MACL/B,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1BF,WAAW,EAAEA,WAAW;MACxBG,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACF,cAAc,EAAEC,YAAY,EAAEF,WAAW,EAAEG,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEjE,IAAImC,UAAU,CAAChD,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,IAAIgE,QAAQ,GAAG,CAAC,CAAC;EAEjB,IAAIzB,IAAI,EAAE;IACRyB,QAAQ,CAACC,QAAQ,GAAG,MAAM,CAAC,CAAC;;IAE5B,IAAI,CAACpD,cAAc,EAAE;MACnBmD,QAAQ,CAACE,YAAY,GAAG,CAACtD,YAAY;IACvC;EACF;EAEA,IAAIC,cAAc,EAAE;IAClBmD,QAAQ,CAACG,SAAS,GAAGxD,cAAc;IACnCqD,QAAQ,CAACI,MAAM,GAAGxD,YAAY;EAChC;EAEA,OAAO,aAAaV,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE5E,QAAQ,CAAC;IACtD8C,SAAS,EAAEqB,EAAE;IACbf,KAAK,EAAEpD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE+E,QAAQ,CAAC,EAAE3B,KAAK;EAC/C,CAAC,EAAEG,UAAU,CAAC,EAAE,aAAatC,KAAK,CAAC2D,aAAa,CAACrD,YAAY,CAAC6D,QAAQ,EAAE;IACtEC,KAAK,EAAEP;EACT,CAAC,EAAEP,KAAK,CAAC,CAAC;AACZ,CAAC;AAED,eAAepC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}