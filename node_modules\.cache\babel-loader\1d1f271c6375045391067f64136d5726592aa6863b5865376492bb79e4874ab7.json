{"ast": null, "code": "import _typeof from '@babel/runtime/helpers/esm/typeof';\nimport _objectSpread from '@babel/runtime/helpers/esm/objectSpread';\nimport _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\nimport _possibleConstructorReturn from '@babel/runtime/helpers/esm/possibleConstructorReturn';\nimport _getPrototypeOf from '@babel/runtime/helpers/esm/getPrototypeOf';\nimport _assertThisInitialized from '@babel/runtime/helpers/esm/assertThisInitialized';\nimport _inherits from '@babel/runtime/helpers/esm/inherits';\nvar consoleLogger = {\n  type: 'logger',\n  log: function log(args) {\n    this.output('log', args);\n  },\n  warn: function warn(args) {\n    this.output('warn', args);\n  },\n  error: function error(args) {\n    this.output('error', args);\n  },\n  output: function output(type, args) {\n    if (console && console[type]) console[type].apply(console, args);\n  }\n};\nvar Logger = function () {\n  function Logger(concreteLogger) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Logger);\n    this.init(concreteLogger, options);\n  }\n  _createClass(Logger, [{\n    key: \"init\",\n    value: function init(concreteLogger) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      this.prefix = options.prefix || 'i18next:';\n      this.logger = concreteLogger || consoleLogger;\n      this.options = options;\n      this.debug = options.debug;\n    }\n  }, {\n    key: \"setDebug\",\n    value: function setDebug(bool) {\n      this.debug = bool;\n    }\n  }, {\n    key: \"log\",\n    value: function log() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return this.forward(args, 'log', '', true);\n    }\n  }, {\n    key: \"warn\",\n    value: function warn() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return this.forward(args, 'warn', '', true);\n    }\n  }, {\n    key: \"error\",\n    value: function error() {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      return this.forward(args, 'error', '');\n    }\n  }, {\n    key: \"deprecate\",\n    value: function deprecate() {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n    }\n  }, {\n    key: \"forward\",\n    value: function forward(args, lvl, prefix, debugOnly) {\n      if (debugOnly && !this.debug) return null;\n      if (typeof args[0] === 'string') args[0] = \"\".concat(prefix).concat(this.prefix, \" \").concat(args[0]);\n      return this.logger[lvl](args);\n    }\n  }, {\n    key: \"create\",\n    value: function create(moduleName) {\n      return new Logger(this.logger, _objectSpread({}, {\n        prefix: \"\".concat(this.prefix, \":\").concat(moduleName, \":\")\n      }, this.options));\n    }\n  }]);\n  return Logger;\n}();\nvar baseLogger = new Logger();\nvar EventEmitter = function () {\n  function EventEmitter() {\n    _classCallCheck(this, EventEmitter);\n    this.observers = {};\n  }\n  _createClass(EventEmitter, [{\n    key: \"on\",\n    value: function on(events, listener) {\n      var _this = this;\n      events.split(' ').forEach(function (event) {\n        _this.observers[event] = _this.observers[event] || [];\n        _this.observers[event].push(listener);\n      });\n      return this;\n    }\n  }, {\n    key: \"off\",\n    value: function off(event, listener) {\n      if (!this.observers[event]) return;\n      if (!listener) {\n        delete this.observers[event];\n        return;\n      }\n      this.observers[event] = this.observers[event].filter(function (l) {\n        return l !== listener;\n      });\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(event) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (this.observers[event]) {\n        var cloned = [].concat(this.observers[event]);\n        cloned.forEach(function (observer) {\n          observer.apply(void 0, args);\n        });\n      }\n      if (this.observers['*']) {\n        var _cloned = [].concat(this.observers['*']);\n        _cloned.forEach(function (observer) {\n          observer.apply(observer, [event].concat(args));\n        });\n      }\n    }\n  }]);\n  return EventEmitter;\n}();\nfunction defer() {\n  var res;\n  var rej;\n  var promise = new Promise(function (resolve, reject) {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n}\nfunction makeString(object) {\n  if (object == null) return '';\n  return '' + object;\n}\nfunction copy(a, s, t) {\n  a.forEach(function (m) {\n    if (s[m]) t[m] = s[m];\n  });\n}\nfunction getLastOfPath(object, path, Empty) {\n  function cleanKey(key) {\n    return key && key.indexOf('###') > -1 ? key.replace(/###/g, '.') : key;\n  }\n  function canNotTraverseDeeper() {\n    return !object || typeof object === 'string';\n  }\n  var stack = typeof path !== 'string' ? [].concat(path) : path.split('.');\n  while (stack.length > 1) {\n    if (canNotTraverseDeeper()) return {};\n    var key = cleanKey(stack.shift());\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n  }\n  if (canNotTraverseDeeper()) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack.shift())\n  };\n}\nfunction setPath(object, path, newValue) {\n  var _getLastOfPath = getLastOfPath(object, path, Object),\n    obj = _getLastOfPath.obj,\n    k = _getLastOfPath.k;\n  obj[k] = newValue;\n}\nfunction pushPath(object, path, newValue, concat) {\n  var _getLastOfPath2 = getLastOfPath(object, path, Object),\n    obj = _getLastOfPath2.obj,\n    k = _getLastOfPath2.k;\n  obj[k] = obj[k] || [];\n  if (concat) obj[k] = obj[k].concat(newValue);\n  if (!concat) obj[k].push(newValue);\n}\nfunction getPath(object, path) {\n  var _getLastOfPath3 = getLastOfPath(object, path),\n    obj = _getLastOfPath3.obj,\n    k = _getLastOfPath3.k;\n  if (!obj) return undefined;\n  return obj[k];\n}\nfunction getPathWithDefaults(data, defaultData, key) {\n  var value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n}\nfunction deepExtend(target, source, overwrite) {\n  for (var prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (typeof target[prop] === 'string' || target[prop] instanceof String || typeof source[prop] === 'string' || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n}\nfunction regexEscape(str) {\n  return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\n}\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nfunction escape(data) {\n  if (typeof data === 'string') {\n    return data.replace(/[&<>\"'\\/]/g, function (s) {\n      return _entityMap[s];\n    });\n  }\n  return data;\n}\nvar isIE10 = typeof window !== 'undefined' && window.navigator && window.navigator.userAgent && window.navigator.userAgent.indexOf('MSIE') > -1;\nfunction deepFind(obj, path) {\n  var keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) return obj[path];\n  var paths = path.split(keySeparator);\n  var current = obj;\n  for (var i = 0; i < paths.length; ++i) {\n    if (!current) return undefined;\n    if (typeof current[paths[i]] === 'string' && i + 1 < paths.length) {\n      return undefined;\n    }\n    if (current[paths[i]] === undefined) {\n      var j = 2;\n      var p = paths.slice(i, i + j).join(keySeparator);\n      var mix = current[p];\n      while (mix === undefined && paths.length > i + j) {\n        j++;\n        p = paths.slice(i, i + j).join(keySeparator);\n        mix = current[p];\n      }\n      if (mix === undefined) return undefined;\n      if (typeof mix === 'string') return mix;\n      if (p && typeof mix[p] === 'string') return mix[p];\n      var joinedPath = paths.slice(i + j).join(keySeparator);\n      if (joinedPath) return deepFind(mix, joinedPath, keySeparator);\n      return undefined;\n    }\n    current = current[paths[i]];\n  }\n  return current;\n}\nvar ResourceStore = function (_EventEmitter) {\n  _inherits(ResourceStore, _EventEmitter);\n  function ResourceStore(data) {\n    var _this;\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n    _classCallCheck(this, ResourceStore);\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(ResourceStore).call(this));\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n    _this.data = data || {};\n    _this.options = options;\n    if (_this.options.keySeparator === undefined) {\n      _this.options.keySeparator = '.';\n    }\n    if (_this.options.ignoreJSONStructure === undefined) {\n      _this.options.ignoreJSONStructure = true;\n    }\n    return _this;\n  }\n  _createClass(ResourceStore, [{\n    key: \"addNamespaces\",\n    value: function addNamespaces(ns) {\n      if (this.options.ns.indexOf(ns) < 0) {\n        this.options.ns.push(ns);\n      }\n    }\n  }, {\n    key: \"removeNamespaces\",\n    value: function removeNamespaces(ns) {\n      var index = this.options.ns.indexOf(ns);\n      if (index > -1) {\n        this.options.ns.splice(index, 1);\n      }\n    }\n  }, {\n    key: \"getResource\",\n    value: function getResource(lng, ns, key) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n      var keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n      var ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n      var path = [lng, ns];\n      if (key && typeof key !== 'string') path = path.concat(key);\n      if (key && typeof key === 'string') path = path.concat(keySeparator ? key.split(keySeparator) : key);\n      if (lng.indexOf('.') > -1) {\n        path = lng.split('.');\n      }\n      var result = getPath(this.data, path);\n      if (result || !ignoreJSONStructure || typeof key !== 'string') return result;\n      return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n    }\n  }, {\n    key: \"addResource\",\n    value: function addResource(lng, ns, key, value) {\n      var options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n        silent: false\n      };\n      var keySeparator = this.options.keySeparator;\n      if (keySeparator === undefined) keySeparator = '.';\n      var path = [lng, ns];\n      if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n      if (lng.indexOf('.') > -1) {\n        path = lng.split('.');\n        value = ns;\n        ns = path[1];\n      }\n      this.addNamespaces(ns);\n      setPath(this.data, path, value);\n      if (!options.silent) this.emit('added', lng, ns, key, value);\n    }\n  }, {\n    key: \"addResources\",\n    value: function addResources(lng, ns, resources) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n        silent: false\n      };\n      for (var m in resources) {\n        if (typeof resources[m] === 'string' || Object.prototype.toString.apply(resources[m]) === '[object Array]') this.addResource(lng, ns, m, resources[m], {\n          silent: true\n        });\n      }\n      if (!options.silent) this.emit('added', lng, ns, resources);\n    }\n  }, {\n    key: \"addResourceBundle\",\n    value: function addResourceBundle(lng, ns, resources, deep, overwrite) {\n      var options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n        silent: false\n      };\n      var path = [lng, ns];\n      if (lng.indexOf('.') > -1) {\n        path = lng.split('.');\n        deep = resources;\n        resources = ns;\n        ns = path[1];\n      }\n      this.addNamespaces(ns);\n      var pack = getPath(this.data, path) || {};\n      if (deep) {\n        deepExtend(pack, resources, overwrite);\n      } else {\n        pack = _objectSpread({}, pack, resources);\n      }\n      setPath(this.data, path, pack);\n      if (!options.silent) this.emit('added', lng, ns, resources);\n    }\n  }, {\n    key: \"removeResourceBundle\",\n    value: function removeResourceBundle(lng, ns) {\n      if (this.hasResourceBundle(lng, ns)) {\n        delete this.data[lng][ns];\n      }\n      this.removeNamespaces(ns);\n      this.emit('removed', lng, ns);\n    }\n  }, {\n    key: \"hasResourceBundle\",\n    value: function hasResourceBundle(lng, ns) {\n      return this.getResource(lng, ns) !== undefined;\n    }\n  }, {\n    key: \"getResourceBundle\",\n    value: function getResourceBundle(lng, ns) {\n      if (!ns) ns = this.options.defaultNS;\n      if (this.options.compatibilityAPI === 'v1') return _objectSpread({}, {}, this.getResource(lng, ns));\n      return this.getResource(lng, ns);\n    }\n  }, {\n    key: \"getDataByLanguage\",\n    value: function getDataByLanguage(lng) {\n      return this.data[lng];\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      return this.data;\n    }\n  }]);\n  return ResourceStore;\n}(EventEmitter);\nvar postProcessor = {\n  processors: {},\n  addPostProcessor: function addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle: function handle(processors, value, key, options, translator) {\n    var _this = this;\n    processors.forEach(function (processor) {\n      if (_this.processors[processor]) value = _this.processors[processor].process(value, key, options, translator);\n    });\n    return value;\n  }\n};\nvar checkedLoadedFor = {};\nvar Translator = function (_EventEmitter) {\n  _inherits(Translator, _EventEmitter);\n  function Translator(services) {\n    var _this;\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Translator);\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Translator).call(this));\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, _assertThisInitialized(_this));\n    _this.options = options;\n    if (_this.options.keySeparator === undefined) {\n      _this.options.keySeparator = '.';\n    }\n    _this.logger = baseLogger.create('translator');\n    return _this;\n  }\n  _createClass(Translator, [{\n    key: \"changeLanguage\",\n    value: function changeLanguage(lng) {\n      if (lng) this.language = lng;\n    }\n  }, {\n    key: \"exists\",\n    value: function exists(key) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        interpolation: {}\n      };\n      if (key === undefined || key === null) {\n        return false;\n      }\n      var resolved = this.resolve(key, options);\n      return resolved && resolved.res !== undefined;\n    }\n  }, {\n    key: \"extractFromKey\",\n    value: function extractFromKey(key, options) {\n      var nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n      if (nsSeparator === undefined) nsSeparator = ':';\n      var keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n      var namespaces = options.ns || this.options.defaultNS;\n      if (nsSeparator && key.indexOf(nsSeparator) > -1) {\n        var m = key.match(this.interpolator.nestingRegexp);\n        if (m && m.length > 0) {\n          return {\n            key: key,\n            namespaces: namespaces\n          };\n        }\n        var parts = key.split(nsSeparator);\n        if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n        key = parts.join(keySeparator);\n      }\n      if (typeof namespaces === 'string') namespaces = [namespaces];\n      return {\n        key: key,\n        namespaces: namespaces\n      };\n    }\n  }, {\n    key: \"translate\",\n    value: function translate(keys, options, lastKey) {\n      var _this2 = this;\n      if (_typeof(options) !== 'object' && this.options.overloadTranslationOptionHandler) {\n        options = this.options.overloadTranslationOptionHandler(arguments);\n      }\n      if (!options) options = {};\n      if (keys === undefined || keys === null) return '';\n      if (!Array.isArray(keys)) keys = [String(keys)];\n      var keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n      var _this$extractFromKey = this.extractFromKey(keys[keys.length - 1], options),\n        key = _this$extractFromKey.key,\n        namespaces = _this$extractFromKey.namespaces;\n      var namespace = namespaces[namespaces.length - 1];\n      var lng = options.lng || this.language;\n      var appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n      if (lng && lng.toLowerCase() === 'cimode') {\n        if (appendNamespaceToCIMode) {\n          var nsSeparator = options.nsSeparator || this.options.nsSeparator;\n          return namespace + nsSeparator + key;\n        }\n        return key;\n      }\n      var resolved = this.resolve(keys, options);\n      var res = resolved && resolved.res;\n      var resUsedKey = resolved && resolved.usedKey || key;\n      var resExactUsedKey = resolved && resolved.exactUsedKey || key;\n      var resType = Object.prototype.toString.apply(res);\n      var noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n      var joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n      var handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n      var handleAsObject = typeof res !== 'string' && typeof res !== 'boolean' && typeof res !== 'number';\n      if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(typeof joinArrays === 'string' && resType === '[object Array]')) {\n        if (!options.returnObjects && !this.options.returnObjects) {\n          if (!this.options.returnedObjectHandler) {\n            this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n          }\n          return this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, _objectSpread({}, options, {\n            ns: namespaces\n          })) : \"key '\".concat(key, \" (\").concat(this.language, \")' returned an object instead of string.\");\n        }\n        if (keySeparator) {\n          var resTypeIsArray = resType === '[object Array]';\n          var copy = resTypeIsArray ? [] : {};\n          var newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n          for (var m in res) {\n            if (Object.prototype.hasOwnProperty.call(res, m)) {\n              var deepKey = \"\".concat(newKeyToUse).concat(keySeparator).concat(m);\n              copy[m] = this.translate(deepKey, _objectSpread({}, options, {\n                joinArrays: false,\n                ns: namespaces\n              }));\n              if (copy[m] === deepKey) copy[m] = res[m];\n            }\n          }\n          res = copy;\n        }\n      } else if (handleAsObjectInI18nFormat && typeof joinArrays === 'string' && resType === '[object Array]') {\n        res = res.join(joinArrays);\n        if (res) res = this.extendTranslation(res, keys, options, lastKey);\n      } else {\n        var usedDefault = false;\n        var usedKey = false;\n        var needsPluralHandling = options.count !== undefined && typeof options.count !== 'string';\n        var hasDefaultValue = Translator.hasDefaultValue(options);\n        var defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count) : '';\n        var defaultValue = options[\"defaultValue\".concat(defaultValueSuffix)] || options.defaultValue;\n        if (!this.isValidLookup(res) && hasDefaultValue) {\n          usedDefault = true;\n          res = defaultValue;\n        }\n        if (!this.isValidLookup(res)) {\n          usedKey = true;\n          res = key;\n        }\n        var missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n        var resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n        var updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n        if (usedKey || usedDefault || updateMissing) {\n          this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n          if (keySeparator) {\n            var fk = this.resolve(key, _objectSpread({}, options, {\n              keySeparator: false\n            }));\n            if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n          }\n          var lngs = [];\n          var fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n          if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n            for (var i = 0; i < fallbackLngs.length; i++) {\n              lngs.push(fallbackLngs[i]);\n            }\n          } else if (this.options.saveMissingTo === 'all') {\n            lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n          } else {\n            lngs.push(options.lng || this.language);\n          }\n          var send = function send(l, k, fallbackValue) {\n            if (_this2.options.missingKeyHandler) {\n              _this2.options.missingKeyHandler(l, namespace, k, updateMissing ? fallbackValue : resForMissing, updateMissing, options);\n            } else if (_this2.backendConnector && _this2.backendConnector.saveMissing) {\n              _this2.backendConnector.saveMissing(l, namespace, k, updateMissing ? fallbackValue : resForMissing, updateMissing, options);\n            }\n            _this2.emit('missingKey', l, namespace, k, res);\n          };\n          if (this.options.saveMissing) {\n            if (this.options.saveMissingPlurals && needsPluralHandling) {\n              lngs.forEach(function (language) {\n                _this2.pluralResolver.getSuffixes(language).forEach(function (suffix) {\n                  send([language], key + suffix, options[\"defaultValue\".concat(suffix)] || defaultValue);\n                });\n              });\n            } else {\n              send(lngs, key, defaultValue);\n            }\n          }\n        }\n        res = this.extendTranslation(res, keys, options, resolved, lastKey);\n        if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = \"\".concat(namespace, \":\").concat(key);\n        if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) res = this.options.parseMissingKeyHandler(res);\n      }\n      return res;\n    }\n  }, {\n    key: \"extendTranslation\",\n    value: function extendTranslation(res, key, options, resolved, lastKey) {\n      var _this3 = this;\n      if (this.i18nFormat && this.i18nFormat.parse) {\n        res = this.i18nFormat.parse(res, options, resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n          resolved: resolved\n        });\n      } else if (!options.skipInterpolation) {\n        if (options.interpolation) this.interpolator.init(_objectSpread({}, options, {\n          interpolation: _objectSpread({}, this.options.interpolation, options.interpolation)\n        }));\n        var skipOnVariables = options.interpolation && options.interpolation.skipOnVariables || this.options.interpolation.skipOnVariables;\n        var nestBef;\n        if (skipOnVariables) {\n          var nb = res.match(this.interpolator.nestingRegexp);\n          nestBef = nb && nb.length;\n        }\n        var data = options.replace && typeof options.replace !== 'string' ? options.replace : options;\n        if (this.options.interpolation.defaultVariables) data = _objectSpread({}, this.options.interpolation.defaultVariables, data);\n        res = this.interpolator.interpolate(res, data, options.lng || this.language, options);\n        if (skipOnVariables) {\n          var na = res.match(this.interpolator.nestingRegexp);\n          var nestAft = na && na.length;\n          if (nestBef < nestAft) options.nest = false;\n        }\n        if (options.nest !== false) res = this.interpolator.nest(res, function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          if (lastKey && lastKey[0] === args[0] && !options.context) {\n            _this3.logger.warn(\"It seems you are nesting recursively key: \".concat(args[0], \" in key: \").concat(key[0]));\n            return null;\n          }\n          return _this3.translate.apply(_this3, args.concat([key]));\n        }, options);\n        if (options.interpolation) this.interpolator.reset();\n      }\n      var postProcess = options.postProcess || this.options.postProcess;\n      var postProcessorNames = typeof postProcess === 'string' ? [postProcess] : postProcess;\n      if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n        res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? _objectSpread({\n          i18nResolved: resolved\n        }, options) : options, this);\n      }\n      return res;\n    }\n  }, {\n    key: \"resolve\",\n    value: function resolve(keys) {\n      var _this4 = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var found;\n      var usedKey;\n      var exactUsedKey;\n      var usedLng;\n      var usedNS;\n      if (typeof keys === 'string') keys = [keys];\n      keys.forEach(function (k) {\n        if (_this4.isValidLookup(found)) return;\n        var extracted = _this4.extractFromKey(k, options);\n        var key = extracted.key;\n        usedKey = key;\n        var namespaces = extracted.namespaces;\n        if (_this4.options.fallbackNS) namespaces = namespaces.concat(_this4.options.fallbackNS);\n        var needsPluralHandling = options.count !== undefined && typeof options.count !== 'string';\n        var needsContextHandling = options.context !== undefined && (typeof options.context === 'string' || typeof options.context === 'number') && options.context !== '';\n        var codes = options.lngs ? options.lngs : _this4.languageUtils.toResolveHierarchy(options.lng || _this4.language, options.fallbackLng);\n        namespaces.forEach(function (ns) {\n          if (_this4.isValidLookup(found)) return;\n          usedNS = ns;\n          if (!checkedLoadedFor[\"\".concat(codes[0], \"-\").concat(ns)] && _this4.utils && _this4.utils.hasLoadedNamespace && !_this4.utils.hasLoadedNamespace(usedNS)) {\n            checkedLoadedFor[\"\".concat(codes[0], \"-\").concat(ns)] = true;\n            _this4.logger.warn(\"key \\\"\".concat(usedKey, \"\\\" for languages \\\"\").concat(codes.join(', '), \"\\\" won't get resolved as namespace \\\"\").concat(usedNS, \"\\\" was not yet loaded\"), 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n          }\n          codes.forEach(function (code) {\n            if (_this4.isValidLookup(found)) return;\n            usedLng = code;\n            var finalKey = key;\n            var finalKeys = [finalKey];\n            if (_this4.i18nFormat && _this4.i18nFormat.addLookupKeys) {\n              _this4.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n            } else {\n              var pluralSuffix;\n              if (needsPluralHandling) pluralSuffix = _this4.pluralResolver.getSuffix(code, options.count);\n              if (needsPluralHandling && needsContextHandling) finalKeys.push(finalKey + pluralSuffix);\n              if (needsContextHandling) finalKeys.push(finalKey += \"\".concat(_this4.options.contextSeparator).concat(options.context));\n              if (needsPluralHandling) finalKeys.push(finalKey += pluralSuffix);\n            }\n            var possibleKey;\n            while (possibleKey = finalKeys.pop()) {\n              if (!_this4.isValidLookup(found)) {\n                exactUsedKey = possibleKey;\n                found = _this4.getResource(code, ns, possibleKey, options);\n              }\n            }\n          });\n        });\n      });\n      return {\n        res: found,\n        usedKey: usedKey,\n        exactUsedKey: exactUsedKey,\n        usedLng: usedLng,\n        usedNS: usedNS\n      };\n    }\n  }, {\n    key: \"isValidLookup\",\n    value: function isValidLookup(res) {\n      return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n    }\n  }, {\n    key: \"getResource\",\n    value: function getResource(code, ns, key) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n      if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n      return this.resourceStore.getResource(code, ns, key, options);\n    }\n  }], [{\n    key: \"hasDefaultValue\",\n    value: function hasDefaultValue(options) {\n      var prefix = 'defaultValue';\n      for (var option in options) {\n        if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n          return true;\n        }\n      }\n      return false;\n    }\n  }]);\n  return Translator;\n}(EventEmitter);\nfunction capitalize(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\nvar LanguageUtil = function () {\n  function LanguageUtil(options) {\n    _classCallCheck(this, LanguageUtil);\n    this.options = options;\n    this.whitelist = this.options.supportedLngs || false;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  _createClass(LanguageUtil, [{\n    key: \"getScriptPartFromCode\",\n    value: function getScriptPartFromCode(code) {\n      if (!code || code.indexOf('-') < 0) return null;\n      var p = code.split('-');\n      if (p.length === 2) return null;\n      p.pop();\n      if (p[p.length - 1].toLowerCase() === 'x') return null;\n      return this.formatLanguageCode(p.join('-'));\n    }\n  }, {\n    key: \"getLanguagePartFromCode\",\n    value: function getLanguagePartFromCode(code) {\n      if (!code || code.indexOf('-') < 0) return code;\n      var p = code.split('-');\n      return this.formatLanguageCode(p[0]);\n    }\n  }, {\n    key: \"formatLanguageCode\",\n    value: function formatLanguageCode(code) {\n      if (typeof code === 'string' && code.indexOf('-') > -1) {\n        var specialCases = ['hans', 'hant', 'latn', 'cyrl', 'cans', 'mong', 'arab'];\n        var p = code.split('-');\n        if (this.options.lowerCaseLng) {\n          p = p.map(function (part) {\n            return part.toLowerCase();\n          });\n        } else if (p.length === 2) {\n          p[0] = p[0].toLowerCase();\n          p[1] = p[1].toUpperCase();\n          if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n        } else if (p.length === 3) {\n          p[0] = p[0].toLowerCase();\n          if (p[1].length === 2) p[1] = p[1].toUpperCase();\n          if (p[0] !== 'sgn' && p[2].length === 2) p[2] = p[2].toUpperCase();\n          if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n          if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n        }\n        return p.join('-');\n      }\n      return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n    }\n  }, {\n    key: \"isWhitelisted\",\n    value: function isWhitelisted(code) {\n      this.logger.deprecate('languageUtils.isWhitelisted', 'function \"isWhitelisted\" will be renamed to \"isSupportedCode\" in the next major - please make sure to rename it\\'s usage asap.');\n      return this.isSupportedCode(code);\n    }\n  }, {\n    key: \"isSupportedCode\",\n    value: function isSupportedCode(code) {\n      if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n        code = this.getLanguagePartFromCode(code);\n      }\n      return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n    }\n  }, {\n    key: \"getBestMatchFromCodes\",\n    value: function getBestMatchFromCodes(codes) {\n      var _this = this;\n      if (!codes) return null;\n      var found;\n      codes.forEach(function (code) {\n        if (found) return;\n        var cleanedLng = _this.formatLanguageCode(code);\n        if (!_this.options.supportedLngs || _this.isSupportedCode(cleanedLng)) found = cleanedLng;\n      });\n      if (!found && this.options.supportedLngs) {\n        codes.forEach(function (code) {\n          if (found) return;\n          var lngOnly = _this.getLanguagePartFromCode(code);\n          if (_this.isSupportedCode(lngOnly)) return found = lngOnly;\n          found = _this.options.supportedLngs.find(function (supportedLng) {\n            if (supportedLng.indexOf(lngOnly) === 0) return supportedLng;\n          });\n        });\n      }\n      if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n      return found;\n    }\n  }, {\n    key: \"getFallbackCodes\",\n    value: function getFallbackCodes(fallbacks, code) {\n      if (!fallbacks) return [];\n      if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n      if (typeof fallbacks === 'string') fallbacks = [fallbacks];\n      if (Object.prototype.toString.apply(fallbacks) === '[object Array]') return fallbacks;\n      if (!code) return fallbacks[\"default\"] || [];\n      var found = fallbacks[code];\n      if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n      if (!found) found = fallbacks[this.formatLanguageCode(code)];\n      if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n      if (!found) found = fallbacks[\"default\"];\n      return found || [];\n    }\n  }, {\n    key: \"toResolveHierarchy\",\n    value: function toResolveHierarchy(code, fallbackCode) {\n      var _this2 = this;\n      var fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n      var codes = [];\n      var addCode = function addCode(c) {\n        if (!c) return;\n        if (_this2.isSupportedCode(c)) {\n          codes.push(c);\n        } else {\n          _this2.logger.warn(\"rejecting language code not found in supportedLngs: \".concat(c));\n        }\n      };\n      if (typeof code === 'string' && code.indexOf('-') > -1) {\n        if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n        if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n        if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n      } else if (typeof code === 'string') {\n        addCode(this.formatLanguageCode(code));\n      }\n      fallbackCodes.forEach(function (fc) {\n        if (codes.indexOf(fc) < 0) addCode(_this2.formatLanguageCode(fc));\n      });\n      return codes;\n    }\n  }]);\n  return LanguageUtil;\n}();\nvar sets = [{\n  lngs: ['ach', 'ak', 'am', 'arn', 'br', 'fil', 'gun', 'ln', 'mfe', 'mg', 'mi', 'oc', 'pt', 'pt-BR', 'tg', 'tl', 'ti', 'tr', 'uz', 'wa'],\n  nr: [1, 2],\n  fc: 1\n}, {\n  lngs: ['af', 'an', 'ast', 'az', 'bg', 'bn', 'ca', 'da', 'de', 'dev', 'el', 'en', 'eo', 'es', 'et', 'eu', 'fi', 'fo', 'fur', 'fy', 'gl', 'gu', 'ha', 'hi', 'hu', 'hy', 'ia', 'it', 'kk', 'kn', 'ku', 'lb', 'mai', 'ml', 'mn', 'mr', 'nah', 'nap', 'nb', 'ne', 'nl', 'nn', 'no', 'nso', 'pa', 'pap', 'pms', 'ps', 'pt-PT', 'rm', 'sco', 'se', 'si', 'so', 'son', 'sq', 'sv', 'sw', 'ta', 'te', 'tk', 'ur', 'yo'],\n  nr: [1, 2],\n  fc: 2\n}, {\n  lngs: ['ay', 'bo', 'cgg', 'fa', 'ht', 'id', 'ja', 'jbo', 'ka', 'km', 'ko', 'ky', 'lo', 'ms', 'sah', 'su', 'th', 'tt', 'ug', 'vi', 'wo', 'zh'],\n  nr: [1],\n  fc: 3\n}, {\n  lngs: ['be', 'bs', 'cnr', 'dz', 'hr', 'ru', 'sr', 'uk'],\n  nr: [1, 2, 5],\n  fc: 4\n}, {\n  lngs: ['ar'],\n  nr: [0, 1, 2, 3, 11, 100],\n  fc: 5\n}, {\n  lngs: ['cs', 'sk'],\n  nr: [1, 2, 5],\n  fc: 6\n}, {\n  lngs: ['csb', 'pl'],\n  nr: [1, 2, 5],\n  fc: 7\n}, {\n  lngs: ['cy'],\n  nr: [1, 2, 3, 8],\n  fc: 8\n}, {\n  lngs: ['fr'],\n  nr: [1, 2],\n  fc: 9\n}, {\n  lngs: ['ga'],\n  nr: [1, 2, 3, 7, 11],\n  fc: 10\n}, {\n  lngs: ['gd'],\n  nr: [1, 2, 3, 20],\n  fc: 11\n}, {\n  lngs: ['is'],\n  nr: [1, 2],\n  fc: 12\n}, {\n  lngs: ['jv'],\n  nr: [0, 1],\n  fc: 13\n}, {\n  lngs: ['kw'],\n  nr: [1, 2, 3, 4],\n  fc: 14\n}, {\n  lngs: ['lt'],\n  nr: [1, 2, 10],\n  fc: 15\n}, {\n  lngs: ['lv'],\n  nr: [1, 2, 0],\n  fc: 16\n}, {\n  lngs: ['mk'],\n  nr: [1, 2],\n  fc: 17\n}, {\n  lngs: ['mnk'],\n  nr: [0, 1, 2],\n  fc: 18\n}, {\n  lngs: ['mt'],\n  nr: [1, 2, 11, 20],\n  fc: 19\n}, {\n  lngs: ['or'],\n  nr: [2, 1],\n  fc: 2\n}, {\n  lngs: ['ro'],\n  nr: [1, 2, 20],\n  fc: 20\n}, {\n  lngs: ['sl'],\n  nr: [5, 1, 2, 3],\n  fc: 21\n}, {\n  lngs: ['he', 'iw'],\n  nr: [1, 2, 20, 21],\n  fc: 22\n}];\nvar _rulesPluralsTypes = {\n  1: function _(n) {\n    return Number(n > 1);\n  },\n  2: function _(n) {\n    return Number(n != 1);\n  },\n  3: function _(n) {\n    return 0;\n  },\n  4: function _(n) {\n    return Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n  },\n  5: function _(n) {\n    return Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5);\n  },\n  6: function _(n) {\n    return Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2);\n  },\n  7: function _(n) {\n    return Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n  },\n  8: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3);\n  },\n  9: function _(n) {\n    return Number(n >= 2);\n  },\n  10: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4);\n  },\n  11: function _(n) {\n    return Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3);\n  },\n  12: function _(n) {\n    return Number(n % 10 != 1 || n % 100 == 11);\n  },\n  13: function _(n) {\n    return Number(n !== 0);\n  },\n  14: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3);\n  },\n  15: function _(n) {\n    return Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n  },\n  16: function _(n) {\n    return Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2);\n  },\n  17: function _(n) {\n    return Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1);\n  },\n  18: function _(n) {\n    return Number(n == 0 ? 0 : n == 1 ? 1 : 2);\n  },\n  19: function _(n) {\n    return Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3);\n  },\n  20: function _(n) {\n    return Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2);\n  },\n  21: function _(n) {\n    return Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0);\n  },\n  22: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3);\n  }\n};\nfunction createRules() {\n  var rules = {};\n  sets.forEach(function (set) {\n    set.lngs.forEach(function (l) {\n      rules[l] = {\n        numbers: set.nr,\n        plurals: _rulesPluralsTypes[set.fc]\n      };\n    });\n  });\n  return rules;\n}\nvar PluralResolver = function () {\n  function PluralResolver(languageUtils) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, PluralResolver);\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.rules = createRules();\n  }\n  _createClass(PluralResolver, [{\n    key: \"addRule\",\n    value: function addRule(lng, obj) {\n      this.rules[lng] = obj;\n    }\n  }, {\n    key: \"getRule\",\n    value: function getRule(code) {\n      return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n    }\n  }, {\n    key: \"needsPlural\",\n    value: function needsPlural(code) {\n      var rule = this.getRule(code);\n      return rule && rule.numbers.length > 1;\n    }\n  }, {\n    key: \"getPluralFormsOfKey\",\n    value: function getPluralFormsOfKey(code, key) {\n      return this.getSuffixes(code).map(function (suffix) {\n        return key + suffix;\n      });\n    }\n  }, {\n    key: \"getSuffixes\",\n    value: function getSuffixes(code) {\n      var _this = this;\n      var rule = this.getRule(code);\n      if (!rule) {\n        return [];\n      }\n      return rule.numbers.map(function (number) {\n        return _this.getSuffix(code, number);\n      });\n    }\n  }, {\n    key: \"getSuffix\",\n    value: function getSuffix(code, count) {\n      var _this2 = this;\n      var rule = this.getRule(code);\n      if (rule) {\n        var idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n        var suffix = rule.numbers[idx];\n        if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n          if (suffix === 2) {\n            suffix = 'plural';\n          } else if (suffix === 1) {\n            suffix = '';\n          }\n        }\n        var returnSuffix = function returnSuffix() {\n          return _this2.options.prepend && suffix.toString() ? _this2.options.prepend + suffix.toString() : suffix.toString();\n        };\n        if (this.options.compatibilityJSON === 'v1') {\n          if (suffix === 1) return '';\n          if (typeof suffix === 'number') return \"_plural_\".concat(suffix.toString());\n          return returnSuffix();\n        } else if (this.options.compatibilityJSON === 'v2') {\n          return returnSuffix();\n        } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n          return returnSuffix();\n        }\n        return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n      }\n      this.logger.warn(\"no plural rule found for: \".concat(code));\n      return '';\n    }\n  }]);\n  return PluralResolver;\n}();\nvar Interpolator = function () {\n  function Interpolator() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, Interpolator);\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options.interpolation && options.interpolation.format || function (value) {\n      return value;\n    };\n    this.init(options);\n  }\n  _createClass(Interpolator, [{\n    key: \"init\",\n    value: function init() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (!options.interpolation) options.interpolation = {\n        escapeValue: true\n      };\n      var iOpts = options.interpolation;\n      this.escape = iOpts.escape !== undefined ? iOpts.escape : escape;\n      this.escapeValue = iOpts.escapeValue !== undefined ? iOpts.escapeValue : true;\n      this.useRawValueToEscape = iOpts.useRawValueToEscape !== undefined ? iOpts.useRawValueToEscape : false;\n      this.prefix = iOpts.prefix ? regexEscape(iOpts.prefix) : iOpts.prefixEscaped || '{{';\n      this.suffix = iOpts.suffix ? regexEscape(iOpts.suffix) : iOpts.suffixEscaped || '}}';\n      this.formatSeparator = iOpts.formatSeparator ? iOpts.formatSeparator : iOpts.formatSeparator || ',';\n      this.unescapePrefix = iOpts.unescapeSuffix ? '' : iOpts.unescapePrefix || '-';\n      this.unescapeSuffix = this.unescapePrefix ? '' : iOpts.unescapeSuffix || '';\n      this.nestingPrefix = iOpts.nestingPrefix ? regexEscape(iOpts.nestingPrefix) : iOpts.nestingPrefixEscaped || regexEscape('$t(');\n      this.nestingSuffix = iOpts.nestingSuffix ? regexEscape(iOpts.nestingSuffix) : iOpts.nestingSuffixEscaped || regexEscape(')');\n      this.nestingOptionsSeparator = iOpts.nestingOptionsSeparator ? iOpts.nestingOptionsSeparator : iOpts.nestingOptionsSeparator || ',';\n      this.maxReplaces = iOpts.maxReplaces ? iOpts.maxReplaces : 1000;\n      this.alwaysFormat = iOpts.alwaysFormat !== undefined ? iOpts.alwaysFormat : false;\n      this.resetRegExp();\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      if (this.options) this.init(this.options);\n    }\n  }, {\n    key: \"resetRegExp\",\n    value: function resetRegExp() {\n      var regexpStr = \"\".concat(this.prefix, \"(.+?)\").concat(this.suffix);\n      this.regexp = new RegExp(regexpStr, 'g');\n      var regexpUnescapeStr = \"\".concat(this.prefix).concat(this.unescapePrefix, \"(.+?)\").concat(this.unescapeSuffix).concat(this.suffix);\n      this.regexpUnescape = new RegExp(regexpUnescapeStr, 'g');\n      var nestingRegexpStr = \"\".concat(this.nestingPrefix, \"(.+?)\").concat(this.nestingSuffix);\n      this.nestingRegexp = new RegExp(nestingRegexpStr, 'g');\n    }\n  }, {\n    key: \"interpolate\",\n    value: function interpolate(str, data, lng, options) {\n      var _this = this;\n      var match;\n      var value;\n      var replaces;\n      var defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n      function regexSafe(val) {\n        return val.replace(/\\$/g, '$$$$');\n      }\n      var handleFormat = function handleFormat(key) {\n        if (key.indexOf(_this.formatSeparator) < 0) {\n          var path = getPathWithDefaults(data, defaultData, key);\n          return _this.alwaysFormat ? _this.format(path, undefined, lng, _objectSpread({}, options, data, {\n            interpolationkey: key\n          })) : path;\n        }\n        var p = key.split(_this.formatSeparator);\n        var k = p.shift().trim();\n        var f = p.join(_this.formatSeparator).trim();\n        return _this.format(getPathWithDefaults(data, defaultData, k), f, lng, _objectSpread({}, options, data, {\n          interpolationkey: k\n        }));\n      };\n      this.resetRegExp();\n      var missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n      var skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables || this.options.interpolation.skipOnVariables;\n      var todos = [{\n        regex: this.regexpUnescape,\n        safeValue: function safeValue(val) {\n          return regexSafe(val);\n        }\n      }, {\n        regex: this.regexp,\n        safeValue: function safeValue(val) {\n          return _this.escapeValue ? regexSafe(_this.escape(val)) : regexSafe(val);\n        }\n      }];\n      todos.forEach(function (todo) {\n        replaces = 0;\n        while (match = todo.regex.exec(str)) {\n          value = handleFormat(match[1].trim());\n          if (value === undefined) {\n            if (typeof missingInterpolationHandler === 'function') {\n              var temp = missingInterpolationHandler(str, match, options);\n              value = typeof temp === 'string' ? temp : '';\n            } else if (skipOnVariables) {\n              value = match[0];\n              continue;\n            } else {\n              _this.logger.warn(\"missed to pass in variable \".concat(match[1], \" for interpolating \").concat(str));\n              value = '';\n            }\n          } else if (typeof value !== 'string' && !_this.useRawValueToEscape) {\n            value = makeString(value);\n          }\n          var safeValue = todo.safeValue(value);\n          str = str.replace(match[0], safeValue);\n          if (skipOnVariables) {\n            todo.regex.lastIndex += safeValue.length;\n            todo.regex.lastIndex -= match[0].length;\n          } else {\n            todo.regex.lastIndex = 0;\n          }\n          replaces++;\n          if (replaces >= _this.maxReplaces) {\n            break;\n          }\n        }\n      });\n      return str;\n    }\n  }, {\n    key: \"nest\",\n    value: function nest(str, fc) {\n      var _this2 = this;\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      var match;\n      var value;\n      var clonedOptions = _objectSpread({}, options);\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      function handleHasOptions(key, inheritedOptions) {\n        var sep = this.nestingOptionsSeparator;\n        if (key.indexOf(sep) < 0) return key;\n        var c = key.split(new RegExp(\"\".concat(sep, \"[ ]*{\")));\n        var optionsString = \"{\".concat(c[1]);\n        key = c[0];\n        optionsString = this.interpolate(optionsString, clonedOptions);\n        optionsString = optionsString.replace(/'/g, '\"');\n        try {\n          clonedOptions = JSON.parse(optionsString);\n          if (inheritedOptions) clonedOptions = _objectSpread({}, inheritedOptions, clonedOptions);\n        } catch (e) {\n          this.logger.warn(\"failed parsing options string in nesting for key \".concat(key), e);\n          return \"\".concat(key).concat(sep).concat(optionsString);\n        }\n        delete clonedOptions.defaultValue;\n        return key;\n      }\n      while (match = this.nestingRegexp.exec(str)) {\n        var formatters = [];\n        var doReduce = false;\n        if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n          var r = match[1].split(this.formatSeparator).map(function (elem) {\n            return elem.trim();\n          });\n          match[1] = r.shift();\n          formatters = r;\n          doReduce = true;\n        }\n        value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n        if (value && match[0] === str && typeof value !== 'string') return value;\n        if (typeof value !== 'string') value = makeString(value);\n        if (!value) {\n          this.logger.warn(\"missed to resolve \".concat(match[1], \" for nesting \").concat(str));\n          value = '';\n        }\n        if (doReduce) {\n          value = formatters.reduce(function (v, f) {\n            return _this2.format(v, f, options.lng, _objectSpread({}, options, {\n              interpolationkey: match[1].trim()\n            }));\n          }, value.trim());\n        }\n        str = str.replace(match[0], value);\n        this.regexp.lastIndex = 0;\n      }\n      return str;\n    }\n  }]);\n  return Interpolator;\n}();\nfunction remove(arr, what) {\n  var found = arr.indexOf(what);\n  while (found !== -1) {\n    arr.splice(found, 1);\n    found = arr.indexOf(what);\n  }\n}\nvar Connector = function (_EventEmitter) {\n  _inherits(Connector, _EventEmitter);\n  function Connector(backend, store, services) {\n    var _this;\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    _classCallCheck(this, Connector);\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Connector).call(this));\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n    _this.backend = backend;\n    _this.store = store;\n    _this.services = services;\n    _this.languageUtils = services.languageUtils;\n    _this.options = options;\n    _this.logger = baseLogger.create('backendConnector');\n    _this.state = {};\n    _this.queue = [];\n    if (_this.backend && _this.backend.init) {\n      _this.backend.init(services, options.backend, options);\n    }\n    return _this;\n  }\n  _createClass(Connector, [{\n    key: \"queueLoad\",\n    value: function queueLoad(languages, namespaces, options, callback) {\n      var _this2 = this;\n      var toLoad = [];\n      var pending = [];\n      var toLoadLanguages = [];\n      var toLoadNamespaces = [];\n      languages.forEach(function (lng) {\n        var hasAllNamespaces = true;\n        namespaces.forEach(function (ns) {\n          var name = \"\".concat(lng, \"|\").concat(ns);\n          if (!options.reload && _this2.store.hasResourceBundle(lng, ns)) {\n            _this2.state[name] = 2;\n          } else if (_this2.state[name] < 0) ;else if (_this2.state[name] === 1) {\n            if (pending.indexOf(name) < 0) pending.push(name);\n          } else {\n            _this2.state[name] = 1;\n            hasAllNamespaces = false;\n            if (pending.indexOf(name) < 0) pending.push(name);\n            if (toLoad.indexOf(name) < 0) toLoad.push(name);\n            if (toLoadNamespaces.indexOf(ns) < 0) toLoadNamespaces.push(ns);\n          }\n        });\n        if (!hasAllNamespaces) toLoadLanguages.push(lng);\n      });\n      if (toLoad.length || pending.length) {\n        this.queue.push({\n          pending: pending,\n          loaded: {},\n          errors: [],\n          callback: callback\n        });\n      }\n      return {\n        toLoad: toLoad,\n        pending: pending,\n        toLoadLanguages: toLoadLanguages,\n        toLoadNamespaces: toLoadNamespaces\n      };\n    }\n  }, {\n    key: \"loaded\",\n    value: function loaded(name, err, data) {\n      var s = name.split('|');\n      var lng = s[0];\n      var ns = s[1];\n      if (err) this.emit('failedLoading', lng, ns, err);\n      if (data) {\n        this.store.addResourceBundle(lng, ns, data);\n      }\n      this.state[name] = err ? -1 : 2;\n      var loaded = {};\n      this.queue.forEach(function (q) {\n        pushPath(q.loaded, [lng], ns);\n        remove(q.pending, name);\n        if (err) q.errors.push(err);\n        if (q.pending.length === 0 && !q.done) {\n          Object.keys(q.loaded).forEach(function (l) {\n            if (!loaded[l]) loaded[l] = [];\n            if (q.loaded[l].length) {\n              q.loaded[l].forEach(function (ns) {\n                if (loaded[l].indexOf(ns) < 0) loaded[l].push(ns);\n              });\n            }\n          });\n          q.done = true;\n          if (q.errors.length) {\n            q.callback(q.errors);\n          } else {\n            q.callback();\n          }\n        }\n      });\n      this.emit('loaded', loaded);\n      this.queue = this.queue.filter(function (q) {\n        return !q.done;\n      });\n    }\n  }, {\n    key: \"read\",\n    value: function read(lng, ns, fcName) {\n      var _this3 = this;\n      var tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n      var wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 350;\n      var callback = arguments.length > 5 ? arguments[5] : undefined;\n      if (!lng.length) return callback(null, {});\n      return this.backend[fcName](lng, ns, function (err, data) {\n        if (err && data && tried < 5) {\n          setTimeout(function () {\n            _this3.read.call(_this3, lng, ns, fcName, tried + 1, wait * 2, callback);\n          }, wait);\n          return;\n        }\n        callback(err, data);\n      });\n    }\n  }, {\n    key: \"prepareLoading\",\n    value: function prepareLoading(languages, namespaces) {\n      var _this4 = this;\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      var callback = arguments.length > 3 ? arguments[3] : undefined;\n      if (!this.backend) {\n        this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n        return callback && callback();\n      }\n      if (typeof languages === 'string') languages = this.languageUtils.toResolveHierarchy(languages);\n      if (typeof namespaces === 'string') namespaces = [namespaces];\n      var toLoad = this.queueLoad(languages, namespaces, options, callback);\n      if (!toLoad.toLoad.length) {\n        if (!toLoad.pending.length) callback();\n        return null;\n      }\n      toLoad.toLoad.forEach(function (name) {\n        _this4.loadOne(name);\n      });\n    }\n  }, {\n    key: \"load\",\n    value: function load(languages, namespaces, callback) {\n      this.prepareLoading(languages, namespaces, {}, callback);\n    }\n  }, {\n    key: \"reload\",\n    value: function reload(languages, namespaces, callback) {\n      this.prepareLoading(languages, namespaces, {\n        reload: true\n      }, callback);\n    }\n  }, {\n    key: \"loadOne\",\n    value: function loadOne(name) {\n      var _this5 = this;\n      var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      var s = name.split('|');\n      var lng = s[0];\n      var ns = s[1];\n      this.read(lng, ns, 'read', undefined, undefined, function (err, data) {\n        if (err) _this5.logger.warn(\"\".concat(prefix, \"loading namespace \").concat(ns, \" for language \").concat(lng, \" failed\"), err);\n        if (!err && data) _this5.logger.log(\"\".concat(prefix, \"loaded namespace \").concat(ns, \" for language \").concat(lng), data);\n        _this5.loaded(name, err, data);\n      });\n    }\n  }, {\n    key: \"saveMissing\",\n    value: function saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n      var options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n      if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n        this.logger.warn(\"did not save key \\\"\".concat(key, \"\\\" as the namespace \\\"\").concat(namespace, \"\\\" was not yet loaded\"), 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        return;\n      }\n      if (key === undefined || key === null || key === '') return;\n      if (this.backend && this.backend.create) {\n        this.backend.create(languages, namespace, key, fallbackValue, null, _objectSpread({}, options, {\n          isUpdate: isUpdate\n        }));\n      }\n      if (!languages || !languages[0]) return;\n      this.store.addResource(languages[0], namespace, key, fallbackValue);\n    }\n  }]);\n  return Connector;\n}(EventEmitter);\nfunction get() {\n  return {\n    debug: false,\n    initImmediate: true,\n    ns: ['translation'],\n    defaultNS: ['translation'],\n    fallbackLng: ['dev'],\n    fallbackNS: false,\n    whitelist: false,\n    nonExplicitWhitelist: false,\n    supportedLngs: false,\n    nonExplicitSupportedLngs: false,\n    load: 'all',\n    preload: false,\n    simplifyPluralSuffix: true,\n    keySeparator: '.',\n    nsSeparator: ':',\n    pluralSeparator: '_',\n    contextSeparator: '_',\n    partialBundledLanguages: false,\n    saveMissing: false,\n    updateMissing: false,\n    saveMissingTo: 'fallback',\n    saveMissingPlurals: true,\n    missingKeyHandler: false,\n    missingInterpolationHandler: false,\n    postProcess: false,\n    postProcessPassResolved: false,\n    returnNull: true,\n    returnEmptyString: true,\n    returnObjects: false,\n    joinArrays: false,\n    returnedObjectHandler: false,\n    parseMissingKeyHandler: false,\n    appendNamespaceToMissingKey: false,\n    appendNamespaceToCIMode: false,\n    overloadTranslationOptionHandler: function handle(args) {\n      var ret = {};\n      if (_typeof(args[1]) === 'object') ret = args[1];\n      if (typeof args[1] === 'string') ret.defaultValue = args[1];\n      if (typeof args[2] === 'string') ret.tDescription = args[2];\n      if (_typeof(args[2]) === 'object' || _typeof(args[3]) === 'object') {\n        var options = args[3] || args[2];\n        Object.keys(options).forEach(function (key) {\n          ret[key] = options[key];\n        });\n      }\n      return ret;\n    },\n    interpolation: {\n      escapeValue: true,\n      format: function format(value, _format, lng, options) {\n        return value;\n      },\n      prefix: '{{',\n      suffix: '}}',\n      formatSeparator: ',',\n      unescapePrefix: '-',\n      nestingPrefix: '$t(',\n      nestingSuffix: ')',\n      nestingOptionsSeparator: ',',\n      maxReplaces: 1000,\n      skipOnVariables: false\n    }\n  };\n}\nfunction transformOptions(options) {\n  if (typeof options.ns === 'string') options.ns = [options.ns];\n  if (typeof options.fallbackLng === 'string') options.fallbackLng = [options.fallbackLng];\n  if (typeof options.fallbackNS === 'string') options.fallbackNS = [options.fallbackNS];\n  if (options.whitelist) {\n    if (options.whitelist && options.whitelist.indexOf('cimode') < 0) {\n      options.whitelist = options.whitelist.concat(['cimode']);\n    }\n    options.supportedLngs = options.whitelist;\n  }\n  if (options.nonExplicitWhitelist) {\n    options.nonExplicitSupportedLngs = options.nonExplicitWhitelist;\n  }\n  if (options.supportedLngs && options.supportedLngs.indexOf('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  return options;\n}\nfunction noop() {}\nvar I18n = function (_EventEmitter) {\n  _inherits(I18n, _EventEmitter);\n  function I18n() {\n    var _this;\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var callback = arguments.length > 1 ? arguments[1] : undefined;\n    _classCallCheck(this, I18n);\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(I18n).call(this));\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n    _this.options = transformOptions(options);\n    _this.services = {};\n    _this.logger = baseLogger;\n    _this.modules = {\n      external: []\n    };\n    if (callback && !_this.isInitialized && !options.isClone) {\n      if (!_this.options.initImmediate) {\n        _this.init(options, callback);\n        return _possibleConstructorReturn(_this, _assertThisInitialized(_this));\n      }\n      setTimeout(function () {\n        _this.init(options, callback);\n      }, 0);\n    }\n    return _this;\n  }\n  _createClass(I18n, [{\n    key: \"init\",\n    value: function init() {\n      var _this2 = this;\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var callback = arguments.length > 1 ? arguments[1] : undefined;\n      if (typeof options === 'function') {\n        callback = options;\n        options = {};\n      }\n      if (options.whitelist && !options.supportedLngs) {\n        this.logger.deprecate('whitelist', 'option \"whitelist\" will be renamed to \"supportedLngs\" in the next major - please make sure to rename this option asap.');\n      }\n      if (options.nonExplicitWhitelist && !options.nonExplicitSupportedLngs) {\n        this.logger.deprecate('whitelist', 'options \"nonExplicitWhitelist\" will be renamed to \"nonExplicitSupportedLngs\" in the next major - please make sure to rename this option asap.');\n      }\n      this.options = _objectSpread({}, get(), this.options, transformOptions(options));\n      this.format = this.options.interpolation.format;\n      if (!callback) callback = noop;\n      function createClassOnDemand(ClassOrObject) {\n        if (!ClassOrObject) return null;\n        if (typeof ClassOrObject === 'function') return new ClassOrObject();\n        return ClassOrObject;\n      }\n      if (!this.options.isClone) {\n        if (this.modules.logger) {\n          baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n        } else {\n          baseLogger.init(null, this.options);\n        }\n        var lu = new LanguageUtil(this.options);\n        this.store = new ResourceStore(this.options.resources, this.options);\n        var s = this.services;\n        s.logger = baseLogger;\n        s.resourceStore = this.store;\n        s.languageUtils = lu;\n        s.pluralResolver = new PluralResolver(lu, {\n          prepend: this.options.pluralSeparator,\n          compatibilityJSON: this.options.compatibilityJSON,\n          simplifyPluralSuffix: this.options.simplifyPluralSuffix\n        });\n        s.interpolator = new Interpolator(this.options);\n        s.utils = {\n          hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n        };\n        s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n        s.backendConnector.on('*', function (event) {\n          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n            args[_key - 1] = arguments[_key];\n          }\n          _this2.emit.apply(_this2, [event].concat(args));\n        });\n        if (this.modules.languageDetector) {\n          s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n          s.languageDetector.init(s, this.options.detection, this.options);\n        }\n        if (this.modules.i18nFormat) {\n          s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n          if (s.i18nFormat.init) s.i18nFormat.init(this);\n        }\n        this.translator = new Translator(this.services, this.options);\n        this.translator.on('*', function (event) {\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n          _this2.emit.apply(_this2, [event].concat(args));\n        });\n        this.modules.external.forEach(function (m) {\n          if (m.init) m.init(_this2);\n        });\n      }\n      if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n        var codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n      }\n      if (!this.services.languageDetector && !this.options.lng) {\n        this.logger.warn('init: no languageDetector is used and no lng is defined');\n      }\n      var storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n      storeApi.forEach(function (fcName) {\n        _this2[fcName] = function () {\n          var _this2$store;\n          return (_this2$store = _this2.store)[fcName].apply(_this2$store, arguments);\n        };\n      });\n      var storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n      storeApiChained.forEach(function (fcName) {\n        _this2[fcName] = function () {\n          var _this2$store2;\n          (_this2$store2 = _this2.store)[fcName].apply(_this2$store2, arguments);\n          return _this2;\n        };\n      });\n      var deferred = defer();\n      var load = function load() {\n        var finish = function finish(err, t) {\n          if (_this2.isInitialized && !_this2.initializedStoreOnce) _this2.logger.warn('init: i18next is already initialized. You should call init just once!');\n          _this2.isInitialized = true;\n          if (!_this2.options.isClone) _this2.logger.log('initialized', _this2.options);\n          _this2.emit('initialized', _this2.options);\n          deferred.resolve(t);\n          callback(err, t);\n        };\n        if (_this2.languages && _this2.options.compatibilityAPI !== 'v1' && !_this2.isInitialized) return finish(null, _this2.t.bind(_this2));\n        _this2.changeLanguage(_this2.options.lng, finish);\n      };\n      if (this.options.resources || !this.options.initImmediate) {\n        load();\n      } else {\n        setTimeout(load, 0);\n      }\n      return deferred;\n    }\n  }, {\n    key: \"loadResources\",\n    value: function loadResources(language) {\n      var _this3 = this;\n      var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n      var usedCallback = callback;\n      var usedLng = typeof language === 'string' ? language : this.language;\n      if (typeof language === 'function') usedCallback = language;\n      if (!this.options.resources || this.options.partialBundledLanguages) {\n        if (usedLng && usedLng.toLowerCase() === 'cimode') return usedCallback();\n        var toLoad = [];\n        var append = function append(lng) {\n          if (!lng) return;\n          var lngs = _this3.services.languageUtils.toResolveHierarchy(lng);\n          lngs.forEach(function (l) {\n            if (toLoad.indexOf(l) < 0) toLoad.push(l);\n          });\n        };\n        if (!usedLng) {\n          var fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n          fallbacks.forEach(function (l) {\n            return append(l);\n          });\n        } else {\n          append(usedLng);\n        }\n        if (this.options.preload) {\n          this.options.preload.forEach(function (l) {\n            return append(l);\n          });\n        }\n        this.services.backendConnector.load(toLoad, this.options.ns, usedCallback);\n      } else {\n        usedCallback(null);\n      }\n    }\n  }, {\n    key: \"reloadResources\",\n    value: function reloadResources(lngs, ns, callback) {\n      var deferred = defer();\n      if (!lngs) lngs = this.languages;\n      if (!ns) ns = this.options.ns;\n      if (!callback) callback = noop;\n      this.services.backendConnector.reload(lngs, ns, function (err) {\n        deferred.resolve();\n        callback(err);\n      });\n      return deferred;\n    }\n  }, {\n    key: \"use\",\n    value: function use(module) {\n      if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n      if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n      if (module.type === 'backend') {\n        this.modules.backend = module;\n      }\n      if (module.type === 'logger' || module.log && module.warn && module.error) {\n        this.modules.logger = module;\n      }\n      if (module.type === 'languageDetector') {\n        this.modules.languageDetector = module;\n      }\n      if (module.type === 'i18nFormat') {\n        this.modules.i18nFormat = module;\n      }\n      if (module.type === 'postProcessor') {\n        postProcessor.addPostProcessor(module);\n      }\n      if (module.type === '3rdParty') {\n        this.modules.external.push(module);\n      }\n      return this;\n    }\n  }, {\n    key: \"changeLanguage\",\n    value: function changeLanguage(lng, callback) {\n      var _this4 = this;\n      this.isLanguageChangingTo = lng;\n      var deferred = defer();\n      this.emit('languageChanging', lng);\n      var done = function done(err, l) {\n        if (l) {\n          _this4.language = l;\n          _this4.languages = _this4.services.languageUtils.toResolveHierarchy(l);\n          _this4.translator.changeLanguage(l);\n          _this4.isLanguageChangingTo = undefined;\n          _this4.emit('languageChanged', l);\n          _this4.logger.log('languageChanged', l);\n        } else {\n          _this4.isLanguageChangingTo = undefined;\n        }\n        deferred.resolve(function () {\n          return _this4.t.apply(_this4, arguments);\n        });\n        if (callback) callback(err, function () {\n          return _this4.t.apply(_this4, arguments);\n        });\n      };\n      var setLng = function setLng(lngs) {\n        if (!lng && !lngs && _this4.services.languageDetector) lngs = [];\n        var l = typeof lngs === 'string' ? lngs : _this4.services.languageUtils.getBestMatchFromCodes(lngs);\n        if (l) {\n          if (!_this4.language) {\n            _this4.language = l;\n            _this4.languages = _this4.services.languageUtils.toResolveHierarchy(l);\n          }\n          if (!_this4.translator.language) _this4.translator.changeLanguage(l);\n          if (_this4.services.languageDetector) _this4.services.languageDetector.cacheUserLanguage(l);\n        }\n        _this4.loadResources(l, function (err) {\n          done(err, l);\n        });\n      };\n      if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n        setLng(this.services.languageDetector.detect());\n      } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n        this.services.languageDetector.detect(setLng);\n      } else {\n        setLng(lng);\n      }\n      return deferred;\n    }\n  }, {\n    key: \"getFixedT\",\n    value: function getFixedT(lng, ns, keyPrefix) {\n      var _this5 = this;\n      var fixedT = function fixedT(key, opts) {\n        var options;\n        if (_typeof(opts) !== 'object') {\n          for (var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n            rest[_key3 - 2] = arguments[_key3];\n          }\n          options = _this5.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n        } else {\n          options = _objectSpread({}, opts);\n        }\n        options.lng = options.lng || fixedT.lng;\n        options.lngs = options.lngs || fixedT.lngs;\n        options.ns = options.ns || fixedT.ns;\n        var keySeparator = _this5.options.keySeparator || '.';\n        var resultKey = keyPrefix ? \"\".concat(keyPrefix).concat(keySeparator).concat(key) : key;\n        return _this5.t(resultKey, options);\n      };\n      if (typeof lng === 'string') {\n        fixedT.lng = lng;\n      } else {\n        fixedT.lngs = lng;\n      }\n      fixedT.ns = ns;\n      fixedT.keyPrefix = keyPrefix;\n      return fixedT;\n    }\n  }, {\n    key: \"t\",\n    value: function t() {\n      var _this$translator;\n      return this.translator && (_this$translator = this.translator).translate.apply(_this$translator, arguments);\n    }\n  }, {\n    key: \"exists\",\n    value: function exists() {\n      var _this$translator2;\n      return this.translator && (_this$translator2 = this.translator).exists.apply(_this$translator2, arguments);\n    }\n  }, {\n    key: \"setDefaultNamespace\",\n    value: function setDefaultNamespace(ns) {\n      this.options.defaultNS = ns;\n    }\n  }, {\n    key: \"hasLoadedNamespace\",\n    value: function hasLoadedNamespace(ns) {\n      var _this6 = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (!this.isInitialized) {\n        this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n        return false;\n      }\n      if (!this.languages || !this.languages.length) {\n        this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n        return false;\n      }\n      var lng = this.languages[0];\n      var fallbackLng = this.options ? this.options.fallbackLng : false;\n      var lastLng = this.languages[this.languages.length - 1];\n      if (lng.toLowerCase() === 'cimode') return true;\n      var loadNotPending = function loadNotPending(l, n) {\n        var loadState = _this6.services.backendConnector.state[\"\".concat(l, \"|\").concat(n)];\n        return loadState === -1 || loadState === 2;\n      };\n      if (options.precheck) {\n        var preResult = options.precheck(this, loadNotPending);\n        if (preResult !== undefined) return preResult;\n      }\n      if (this.hasResourceBundle(lng, ns)) return true;\n      if (!this.services.backendConnector.backend) return true;\n      if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n      return false;\n    }\n  }, {\n    key: \"loadNamespaces\",\n    value: function loadNamespaces(ns, callback) {\n      var _this7 = this;\n      var deferred = defer();\n      if (!this.options.ns) {\n        callback && callback();\n        return Promise.resolve();\n      }\n      if (typeof ns === 'string') ns = [ns];\n      ns.forEach(function (n) {\n        if (_this7.options.ns.indexOf(n) < 0) _this7.options.ns.push(n);\n      });\n      this.loadResources(function (err) {\n        deferred.resolve();\n        if (callback) callback(err);\n      });\n      return deferred;\n    }\n  }, {\n    key: \"loadLanguages\",\n    value: function loadLanguages(lngs, callback) {\n      var deferred = defer();\n      if (typeof lngs === 'string') lngs = [lngs];\n      var preloaded = this.options.preload || [];\n      var newLngs = lngs.filter(function (lng) {\n        return preloaded.indexOf(lng) < 0;\n      });\n      if (!newLngs.length) {\n        if (callback) callback();\n        return Promise.resolve();\n      }\n      this.options.preload = preloaded.concat(newLngs);\n      this.loadResources(function (err) {\n        deferred.resolve();\n        if (callback) callback(err);\n      });\n      return deferred;\n    }\n  }, {\n    key: \"dir\",\n    value: function dir(lng) {\n      if (!lng) lng = this.languages && this.languages.length > 0 ? this.languages[0] : this.language;\n      if (!lng) return 'rtl';\n      var rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam'];\n      return rtlLngs.indexOf(this.services.languageUtils.getLanguagePartFromCode(lng)) >= 0 ? 'rtl' : 'ltr';\n    }\n  }, {\n    key: \"createInstance\",\n    value: function createInstance() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var callback = arguments.length > 1 ? arguments[1] : undefined;\n      return new I18n(options, callback);\n    }\n  }, {\n    key: \"cloneInstance\",\n    value: function cloneInstance() {\n      var _this8 = this;\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n      var mergedOptions = _objectSpread({}, this.options, options, {\n        isClone: true\n      });\n      var clone = new I18n(mergedOptions);\n      var membersToCopy = ['store', 'services', 'language'];\n      membersToCopy.forEach(function (m) {\n        clone[m] = _this8[m];\n      });\n      clone.services = _objectSpread({}, this.services);\n      clone.services.utils = {\n        hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n      };\n      clone.translator = new Translator(clone.services, clone.options);\n      clone.translator.on('*', function (event) {\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n        clone.emit.apply(clone, [event].concat(args));\n      });\n      clone.init(mergedOptions, callback);\n      clone.translator.options = clone.options;\n      clone.translator.backendConnector.services.utils = {\n        hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n      };\n      return clone;\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        options: this.options,\n        store: this.store,\n        language: this.language,\n        languages: this.languages\n      };\n    }\n  }]);\n  return I18n;\n}(EventEmitter);\nvar i18next = new I18n();\nexport default i18next;", "map": {"version": 3, "names": ["_typeof", "_objectSpread", "_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_assertThisInitialized", "_inherits", "consoleLogger", "type", "log", "args", "output", "warn", "error", "console", "apply", "<PERSON><PERSON>", "concreteLogger", "options", "arguments", "length", "undefined", "init", "key", "value", "prefix", "logger", "debug", "setDebug", "bool", "_len", "Array", "_key", "forward", "_len2", "_key2", "_len3", "_key3", "deprecate", "_len4", "_key4", "lvl", "debugOnly", "concat", "create", "moduleName", "baseLogger", "EventEmitter", "observers", "on", "events", "listener", "_this", "split", "for<PERSON>ach", "event", "push", "off", "filter", "l", "emit", "cloned", "observer", "_cloned", "defer", "res", "rej", "promise", "Promise", "resolve", "reject", "makeString", "object", "copy", "a", "s", "t", "m", "getLastOfPath", "path", "Empty", "<PERSON><PERSON><PERSON>", "indexOf", "replace", "canNotTraverseDeeper", "stack", "shift", "Object", "prototype", "hasOwnProperty", "call", "obj", "k", "set<PERSON>ath", "newValue", "_getLastOfPath", "push<PERSON><PERSON>", "_getLastOfPath2", "<PERSON><PERSON><PERSON>", "_getLastOfPath3", "getPathWithDefaults", "data", "defaultData", "deepExtend", "target", "source", "overwrite", "prop", "String", "regexEscape", "str", "_entityMap", "escape", "isIE10", "window", "navigator", "userAgent", "deepFind", "keySeparator", "paths", "current", "i", "j", "p", "slice", "join", "mix", "joinedPath", "ResourceStore", "_EventEmitter", "ns", "defaultNS", "ignoreJSONStructure", "addNamespaces", "removeNamespaces", "index", "splice", "getResource", "lng", "result", "addResource", "silent", "addResources", "resources", "toString", "addResourceBundle", "deep", "pack", "removeResourceBundle", "hasResourceBundle", "getResourceBundle", "compatibilityAPI", "getDataByLanguage", "toJSON", "postProcessor", "processors", "addPostProcessor", "module", "name", "handle", "translator", "processor", "process", "checkedLoadedFor", "Translator", "services", "changeLanguage", "language", "exists", "interpolation", "resolved", "extractFromKey", "nsSeparator", "namespaces", "match", "interpolator", "nestingRegexp", "parts", "translate", "keys", "last<PERSON>ey", "_this2", "overloadTranslationOptionHandler", "isArray", "_this$extractFromKey", "namespace", "appendNamespaceToCIMode", "toLowerCase", "resUsed<PERSON><PERSON>", "usedKey", "resExactUsedKey", "exactUsed<PERSON>ey", "resType", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "i18nFormat", "handleAsObject", "returnObjects", "returnedObjectHandler", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "extendTranslation", "usedDefault", "needsPluralHandling", "count", "hasDefaultValue", "defaultValueSuffix", "pluralResolver", "getSuffix", "defaultValue", "isValidLookup", "missingKeyNoValueFallbackToKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "languageUtils", "getFallbackCodes", "fallbackLng", "saveMissingTo", "toResolveHierarchy", "send", "fallback<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backendConnector", "saveMissing", "saveMissingPlurals", "getSuffixes", "suffix", "appendNamespaceToMissingKey", "parseMissingKeyHandler", "_this3", "parse", "usedLng", "usedNS", "skipInterpolation", "skipOnVariables", "nestBef", "nb", "defaultVariables", "interpolate", "na", "nestAft", "nest", "context", "reset", "postProcess", "postProcessorNames", "applyPostProcessor", "postProcessPassResolved", "i18nResolved", "_this4", "found", "extracted", "fallbackNS", "needsContextHandling", "codes", "utils", "hasLoadedNamespace", "code", "final<PERSON>ey", "finalKeys", "addLookupKeys", "pluralSuffix", "contextSeparator", "<PERSON><PERSON><PERSON>", "pop", "returnNull", "returnEmptyString", "resourceStore", "option", "substring", "capitalize", "string", "char<PERSON>t", "toUpperCase", "LanguageUtil", "whitelist", "supportedLngs", "getScriptPartFromCode", "formatLanguageCode", "getLanguagePartFromCode", "specialCases", "lowerCaseLng", "map", "part", "cleanCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSupportedCode", "load", "nonExplicitSupportedLngs", "getBestMatchFromCodes", "cleanedLng", "lngOnly", "find", "supportedLng", "fallbacks", "fallbackCode", "fallbackCodes", "addCode", "c", "fc", "sets", "nr", "_rulesPluralsTypes", "_", "n", "Number", "createRules", "rules", "set", "numbers", "plurals", "PluralResolver", "addRule", "getRule", "needsPlural", "rule", "getPluralFormsOfKey", "number", "idx", "noAbs", "Math", "abs", "simplifyPluralSuffix", "returnSuffix", "prepend", "compatibilityJSON", "Interpolator", "format", "escapeValue", "iOpts", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapePrefix", "unescapeSuffix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "resetRegExp", "regexpStr", "regexp", "RegExp", "regexpUnescapeStr", "regexpUnescape", "nestingRegexpStr", "replaces", "regexSafe", "val", "handleFormat", "interpolationkey", "trim", "f", "missingInterpolationHandler", "todos", "regex", "safeValue", "todo", "exec", "temp", "lastIndex", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "JSON", "e", "formatters", "doReduce", "test", "r", "elem", "reduce", "v", "remove", "arr", "what", "Connector", "backend", "store", "state", "queue", "queueLoad", "languages", "callback", "toLoad", "pending", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "reload", "loaded", "errors", "err", "q", "done", "read", "fcName", "tried", "wait", "setTimeout", "prepareLoading", "loadOne", "_this5", "isUpdate", "get", "initImmediate", "nonExplicit<PERSON><PERSON><PERSON><PERSON>", "preload", "pluralSeparator", "partialBundledLanguages", "ret", "tDescription", "_format", "transformOptions", "noop", "I18n", "modules", "external", "isInitialized", "isClone", "createClassOnDemand", "ClassOrObject", "lu", "bind", "languageDetector", "detection", "storeApi", "_this2$store", "storeApiChained", "_this2$store2", "deferred", "finish", "initializedStoreOnce", "loadResources", "usedCallback", "append", "reloadResources", "use", "Error", "isLanguageChangingTo", "setLng", "cacheUserLanguage", "async", "detect", "getFixedT", "keyPrefix", "fixedT", "opts", "rest", "<PERSON><PERSON><PERSON>", "_this$translator", "_this$translator2", "setDefaultNamespace", "_this6", "lastLng", "loadNotPending", "loadState", "precheck", "preResult", "loadNamespaces", "_this7", "loadLanguages", "preloaded", "newLngs", "dir", "rtlLngs", "createInstance", "cloneInstance", "_this8", "mergedOptions", "clone", "membersToCopy", "i18next"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/i18next/dist/esm/i18next.js"], "sourcesContent": ["import _typeof from '@babel/runtime/helpers/esm/typeof';\nimport _objectSpread from '@babel/runtime/helpers/esm/objectSpread';\nimport _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\nimport _possibleConstructorReturn from '@babel/runtime/helpers/esm/possibleConstructorReturn';\nimport _getPrototypeOf from '@babel/runtime/helpers/esm/getPrototypeOf';\nimport _assertThisInitialized from '@babel/runtime/helpers/esm/assertThisInitialized';\nimport _inherits from '@babel/runtime/helpers/esm/inherits';\n\nvar consoleLogger = {\n  type: 'logger',\n  log: function log(args) {\n    this.output('log', args);\n  },\n  warn: function warn(args) {\n    this.output('warn', args);\n  },\n  error: function error(args) {\n    this.output('error', args);\n  },\n  output: function output(type, args) {\n    if (console && console[type]) console[type].apply(console, args);\n  }\n};\n\nvar Logger = function () {\n  function Logger(concreteLogger) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Logger);\n\n    this.init(concreteLogger, options);\n  }\n\n  _createClass(Logger, [{\n    key: \"init\",\n    value: function init(concreteLogger) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      this.prefix = options.prefix || 'i18next:';\n      this.logger = concreteLogger || consoleLogger;\n      this.options = options;\n      this.debug = options.debug;\n    }\n  }, {\n    key: \"setDebug\",\n    value: function setDebug(bool) {\n      this.debug = bool;\n    }\n  }, {\n    key: \"log\",\n    value: function log() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return this.forward(args, 'log', '', true);\n    }\n  }, {\n    key: \"warn\",\n    value: function warn() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return this.forward(args, 'warn', '', true);\n    }\n  }, {\n    key: \"error\",\n    value: function error() {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      return this.forward(args, 'error', '');\n    }\n  }, {\n    key: \"deprecate\",\n    value: function deprecate() {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n\n      return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n    }\n  }, {\n    key: \"forward\",\n    value: function forward(args, lvl, prefix, debugOnly) {\n      if (debugOnly && !this.debug) return null;\n      if (typeof args[0] === 'string') args[0] = \"\".concat(prefix).concat(this.prefix, \" \").concat(args[0]);\n      return this.logger[lvl](args);\n    }\n  }, {\n    key: \"create\",\n    value: function create(moduleName) {\n      return new Logger(this.logger, _objectSpread({}, {\n        prefix: \"\".concat(this.prefix, \":\").concat(moduleName, \":\")\n      }, this.options));\n    }\n  }]);\n\n  return Logger;\n}();\n\nvar baseLogger = new Logger();\n\nvar EventEmitter = function () {\n  function EventEmitter() {\n    _classCallCheck(this, EventEmitter);\n\n    this.observers = {};\n  }\n\n  _createClass(EventEmitter, [{\n    key: \"on\",\n    value: function on(events, listener) {\n      var _this = this;\n\n      events.split(' ').forEach(function (event) {\n        _this.observers[event] = _this.observers[event] || [];\n\n        _this.observers[event].push(listener);\n      });\n      return this;\n    }\n  }, {\n    key: \"off\",\n    value: function off(event, listener) {\n      if (!this.observers[event]) return;\n\n      if (!listener) {\n        delete this.observers[event];\n        return;\n      }\n\n      this.observers[event] = this.observers[event].filter(function (l) {\n        return l !== listener;\n      });\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(event) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      if (this.observers[event]) {\n        var cloned = [].concat(this.observers[event]);\n        cloned.forEach(function (observer) {\n          observer.apply(void 0, args);\n        });\n      }\n\n      if (this.observers['*']) {\n        var _cloned = [].concat(this.observers['*']);\n\n        _cloned.forEach(function (observer) {\n          observer.apply(observer, [event].concat(args));\n        });\n      }\n    }\n  }]);\n\n  return EventEmitter;\n}();\n\nfunction defer() {\n  var res;\n  var rej;\n  var promise = new Promise(function (resolve, reject) {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n}\nfunction makeString(object) {\n  if (object == null) return '';\n  return '' + object;\n}\nfunction copy(a, s, t) {\n  a.forEach(function (m) {\n    if (s[m]) t[m] = s[m];\n  });\n}\n\nfunction getLastOfPath(object, path, Empty) {\n  function cleanKey(key) {\n    return key && key.indexOf('###') > -1 ? key.replace(/###/g, '.') : key;\n  }\n\n  function canNotTraverseDeeper() {\n    return !object || typeof object === 'string';\n  }\n\n  var stack = typeof path !== 'string' ? [].concat(path) : path.split('.');\n\n  while (stack.length > 1) {\n    if (canNotTraverseDeeper()) return {};\n    var key = cleanKey(stack.shift());\n    if (!object[key] && Empty) object[key] = new Empty();\n\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n  }\n\n  if (canNotTraverseDeeper()) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack.shift())\n  };\n}\n\nfunction setPath(object, path, newValue) {\n  var _getLastOfPath = getLastOfPath(object, path, Object),\n      obj = _getLastOfPath.obj,\n      k = _getLastOfPath.k;\n\n  obj[k] = newValue;\n}\nfunction pushPath(object, path, newValue, concat) {\n  var _getLastOfPath2 = getLastOfPath(object, path, Object),\n      obj = _getLastOfPath2.obj,\n      k = _getLastOfPath2.k;\n\n  obj[k] = obj[k] || [];\n  if (concat) obj[k] = obj[k].concat(newValue);\n  if (!concat) obj[k].push(newValue);\n}\nfunction getPath(object, path) {\n  var _getLastOfPath3 = getLastOfPath(object, path),\n      obj = _getLastOfPath3.obj,\n      k = _getLastOfPath3.k;\n\n  if (!obj) return undefined;\n  return obj[k];\n}\nfunction getPathWithDefaults(data, defaultData, key) {\n  var value = getPath(data, key);\n\n  if (value !== undefined) {\n    return value;\n  }\n\n  return getPath(defaultData, key);\n}\nfunction deepExtend(target, source, overwrite) {\n  for (var prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (typeof target[prop] === 'string' || target[prop] instanceof String || typeof source[prop] === 'string' || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n\n  return target;\n}\nfunction regexEscape(str) {\n  return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\n}\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nfunction escape(data) {\n  if (typeof data === 'string') {\n    return data.replace(/[&<>\"'\\/]/g, function (s) {\n      return _entityMap[s];\n    });\n  }\n\n  return data;\n}\nvar isIE10 = typeof window !== 'undefined' && window.navigator && window.navigator.userAgent && window.navigator.userAgent.indexOf('MSIE') > -1;\n\nfunction deepFind(obj, path) {\n  var keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) return obj[path];\n  var paths = path.split(keySeparator);\n  var current = obj;\n\n  for (var i = 0; i < paths.length; ++i) {\n    if (!current) return undefined;\n\n    if (typeof current[paths[i]] === 'string' && i + 1 < paths.length) {\n      return undefined;\n    }\n\n    if (current[paths[i]] === undefined) {\n      var j = 2;\n      var p = paths.slice(i, i + j).join(keySeparator);\n      var mix = current[p];\n\n      while (mix === undefined && paths.length > i + j) {\n        j++;\n        p = paths.slice(i, i + j).join(keySeparator);\n        mix = current[p];\n      }\n\n      if (mix === undefined) return undefined;\n      if (typeof mix === 'string') return mix;\n      if (p && typeof mix[p] === 'string') return mix[p];\n      var joinedPath = paths.slice(i + j).join(keySeparator);\n      if (joinedPath) return deepFind(mix, joinedPath, keySeparator);\n      return undefined;\n    }\n\n    current = current[paths[i]];\n  }\n\n  return current;\n}\n\nvar ResourceStore = function (_EventEmitter) {\n  _inherits(ResourceStore, _EventEmitter);\n\n  function ResourceStore(data) {\n    var _this;\n\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n\n    _classCallCheck(this, ResourceStore);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(ResourceStore).call(this));\n\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n\n    _this.data = data || {};\n    _this.options = options;\n\n    if (_this.options.keySeparator === undefined) {\n      _this.options.keySeparator = '.';\n    }\n\n    if (_this.options.ignoreJSONStructure === undefined) {\n      _this.options.ignoreJSONStructure = true;\n    }\n\n    return _this;\n  }\n\n  _createClass(ResourceStore, [{\n    key: \"addNamespaces\",\n    value: function addNamespaces(ns) {\n      if (this.options.ns.indexOf(ns) < 0) {\n        this.options.ns.push(ns);\n      }\n    }\n  }, {\n    key: \"removeNamespaces\",\n    value: function removeNamespaces(ns) {\n      var index = this.options.ns.indexOf(ns);\n\n      if (index > -1) {\n        this.options.ns.splice(index, 1);\n      }\n    }\n  }, {\n    key: \"getResource\",\n    value: function getResource(lng, ns, key) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n      var keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n      var ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n      var path = [lng, ns];\n      if (key && typeof key !== 'string') path = path.concat(key);\n      if (key && typeof key === 'string') path = path.concat(keySeparator ? key.split(keySeparator) : key);\n\n      if (lng.indexOf('.') > -1) {\n        path = lng.split('.');\n      }\n\n      var result = getPath(this.data, path);\n      if (result || !ignoreJSONStructure || typeof key !== 'string') return result;\n      return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n    }\n  }, {\n    key: \"addResource\",\n    value: function addResource(lng, ns, key, value) {\n      var options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n        silent: false\n      };\n      var keySeparator = this.options.keySeparator;\n      if (keySeparator === undefined) keySeparator = '.';\n      var path = [lng, ns];\n      if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n\n      if (lng.indexOf('.') > -1) {\n        path = lng.split('.');\n        value = ns;\n        ns = path[1];\n      }\n\n      this.addNamespaces(ns);\n      setPath(this.data, path, value);\n      if (!options.silent) this.emit('added', lng, ns, key, value);\n    }\n  }, {\n    key: \"addResources\",\n    value: function addResources(lng, ns, resources) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n        silent: false\n      };\n\n      for (var m in resources) {\n        if (typeof resources[m] === 'string' || Object.prototype.toString.apply(resources[m]) === '[object Array]') this.addResource(lng, ns, m, resources[m], {\n          silent: true\n        });\n      }\n\n      if (!options.silent) this.emit('added', lng, ns, resources);\n    }\n  }, {\n    key: \"addResourceBundle\",\n    value: function addResourceBundle(lng, ns, resources, deep, overwrite) {\n      var options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n        silent: false\n      };\n      var path = [lng, ns];\n\n      if (lng.indexOf('.') > -1) {\n        path = lng.split('.');\n        deep = resources;\n        resources = ns;\n        ns = path[1];\n      }\n\n      this.addNamespaces(ns);\n      var pack = getPath(this.data, path) || {};\n\n      if (deep) {\n        deepExtend(pack, resources, overwrite);\n      } else {\n        pack = _objectSpread({}, pack, resources);\n      }\n\n      setPath(this.data, path, pack);\n      if (!options.silent) this.emit('added', lng, ns, resources);\n    }\n  }, {\n    key: \"removeResourceBundle\",\n    value: function removeResourceBundle(lng, ns) {\n      if (this.hasResourceBundle(lng, ns)) {\n        delete this.data[lng][ns];\n      }\n\n      this.removeNamespaces(ns);\n      this.emit('removed', lng, ns);\n    }\n  }, {\n    key: \"hasResourceBundle\",\n    value: function hasResourceBundle(lng, ns) {\n      return this.getResource(lng, ns) !== undefined;\n    }\n  }, {\n    key: \"getResourceBundle\",\n    value: function getResourceBundle(lng, ns) {\n      if (!ns) ns = this.options.defaultNS;\n      if (this.options.compatibilityAPI === 'v1') return _objectSpread({}, {}, this.getResource(lng, ns));\n      return this.getResource(lng, ns);\n    }\n  }, {\n    key: \"getDataByLanguage\",\n    value: function getDataByLanguage(lng) {\n      return this.data[lng];\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      return this.data;\n    }\n  }]);\n\n  return ResourceStore;\n}(EventEmitter);\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor: function addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle: function handle(processors, value, key, options, translator) {\n    var _this = this;\n\n    processors.forEach(function (processor) {\n      if (_this.processors[processor]) value = _this.processors[processor].process(value, key, options, translator);\n    });\n    return value;\n  }\n};\n\nvar checkedLoadedFor = {};\n\nvar Translator = function (_EventEmitter) {\n  _inherits(Translator, _EventEmitter);\n\n  function Translator(services) {\n    var _this;\n\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, Translator);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Translator).call(this));\n\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, _assertThisInitialized(_this));\n    _this.options = options;\n\n    if (_this.options.keySeparator === undefined) {\n      _this.options.keySeparator = '.';\n    }\n\n    _this.logger = baseLogger.create('translator');\n    return _this;\n  }\n\n  _createClass(Translator, [{\n    key: \"changeLanguage\",\n    value: function changeLanguage(lng) {\n      if (lng) this.language = lng;\n    }\n  }, {\n    key: \"exists\",\n    value: function exists(key) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        interpolation: {}\n      };\n\n      if (key === undefined || key === null) {\n        return false;\n      }\n\n      var resolved = this.resolve(key, options);\n      return resolved && resolved.res !== undefined;\n    }\n  }, {\n    key: \"extractFromKey\",\n    value: function extractFromKey(key, options) {\n      var nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n      if (nsSeparator === undefined) nsSeparator = ':';\n      var keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n      var namespaces = options.ns || this.options.defaultNS;\n\n      if (nsSeparator && key.indexOf(nsSeparator) > -1) {\n        var m = key.match(this.interpolator.nestingRegexp);\n\n        if (m && m.length > 0) {\n          return {\n            key: key,\n            namespaces: namespaces\n          };\n        }\n\n        var parts = key.split(nsSeparator);\n        if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n        key = parts.join(keySeparator);\n      }\n\n      if (typeof namespaces === 'string') namespaces = [namespaces];\n      return {\n        key: key,\n        namespaces: namespaces\n      };\n    }\n  }, {\n    key: \"translate\",\n    value: function translate(keys, options, lastKey) {\n      var _this2 = this;\n\n      if (_typeof(options) !== 'object' && this.options.overloadTranslationOptionHandler) {\n        options = this.options.overloadTranslationOptionHandler(arguments);\n      }\n\n      if (!options) options = {};\n      if (keys === undefined || keys === null) return '';\n      if (!Array.isArray(keys)) keys = [String(keys)];\n      var keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n\n      var _this$extractFromKey = this.extractFromKey(keys[keys.length - 1], options),\n          key = _this$extractFromKey.key,\n          namespaces = _this$extractFromKey.namespaces;\n\n      var namespace = namespaces[namespaces.length - 1];\n      var lng = options.lng || this.language;\n      var appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n\n      if (lng && lng.toLowerCase() === 'cimode') {\n        if (appendNamespaceToCIMode) {\n          var nsSeparator = options.nsSeparator || this.options.nsSeparator;\n          return namespace + nsSeparator + key;\n        }\n\n        return key;\n      }\n\n      var resolved = this.resolve(keys, options);\n      var res = resolved && resolved.res;\n      var resUsedKey = resolved && resolved.usedKey || key;\n      var resExactUsedKey = resolved && resolved.exactUsedKey || key;\n      var resType = Object.prototype.toString.apply(res);\n      var noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n      var joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n      var handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n      var handleAsObject = typeof res !== 'string' && typeof res !== 'boolean' && typeof res !== 'number';\n\n      if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(typeof joinArrays === 'string' && resType === '[object Array]')) {\n        if (!options.returnObjects && !this.options.returnObjects) {\n          if (!this.options.returnedObjectHandler) {\n            this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n          }\n\n          return this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, _objectSpread({}, options, {\n            ns: namespaces\n          })) : \"key '\".concat(key, \" (\").concat(this.language, \")' returned an object instead of string.\");\n        }\n\n        if (keySeparator) {\n          var resTypeIsArray = resType === '[object Array]';\n          var copy = resTypeIsArray ? [] : {};\n          var newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n\n          for (var m in res) {\n            if (Object.prototype.hasOwnProperty.call(res, m)) {\n              var deepKey = \"\".concat(newKeyToUse).concat(keySeparator).concat(m);\n              copy[m] = this.translate(deepKey, _objectSpread({}, options, {\n                joinArrays: false,\n                ns: namespaces\n              }));\n              if (copy[m] === deepKey) copy[m] = res[m];\n            }\n          }\n\n          res = copy;\n        }\n      } else if (handleAsObjectInI18nFormat && typeof joinArrays === 'string' && resType === '[object Array]') {\n        res = res.join(joinArrays);\n        if (res) res = this.extendTranslation(res, keys, options, lastKey);\n      } else {\n        var usedDefault = false;\n        var usedKey = false;\n        var needsPluralHandling = options.count !== undefined && typeof options.count !== 'string';\n        var hasDefaultValue = Translator.hasDefaultValue(options);\n        var defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count) : '';\n        var defaultValue = options[\"defaultValue\".concat(defaultValueSuffix)] || options.defaultValue;\n\n        if (!this.isValidLookup(res) && hasDefaultValue) {\n          usedDefault = true;\n          res = defaultValue;\n        }\n\n        if (!this.isValidLookup(res)) {\n          usedKey = true;\n          res = key;\n        }\n\n        var missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n        var resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n        var updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n\n        if (usedKey || usedDefault || updateMissing) {\n          this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n\n          if (keySeparator) {\n            var fk = this.resolve(key, _objectSpread({}, options, {\n              keySeparator: false\n            }));\n            if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n          }\n\n          var lngs = [];\n          var fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n\n          if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n            for (var i = 0; i < fallbackLngs.length; i++) {\n              lngs.push(fallbackLngs[i]);\n            }\n          } else if (this.options.saveMissingTo === 'all') {\n            lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n          } else {\n            lngs.push(options.lng || this.language);\n          }\n\n          var send = function send(l, k, fallbackValue) {\n            if (_this2.options.missingKeyHandler) {\n              _this2.options.missingKeyHandler(l, namespace, k, updateMissing ? fallbackValue : resForMissing, updateMissing, options);\n            } else if (_this2.backendConnector && _this2.backendConnector.saveMissing) {\n              _this2.backendConnector.saveMissing(l, namespace, k, updateMissing ? fallbackValue : resForMissing, updateMissing, options);\n            }\n\n            _this2.emit('missingKey', l, namespace, k, res);\n          };\n\n          if (this.options.saveMissing) {\n            if (this.options.saveMissingPlurals && needsPluralHandling) {\n              lngs.forEach(function (language) {\n                _this2.pluralResolver.getSuffixes(language).forEach(function (suffix) {\n                  send([language], key + suffix, options[\"defaultValue\".concat(suffix)] || defaultValue);\n                });\n              });\n            } else {\n              send(lngs, key, defaultValue);\n            }\n          }\n        }\n\n        res = this.extendTranslation(res, keys, options, resolved, lastKey);\n        if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = \"\".concat(namespace, \":\").concat(key);\n        if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) res = this.options.parseMissingKeyHandler(res);\n      }\n\n      return res;\n    }\n  }, {\n    key: \"extendTranslation\",\n    value: function extendTranslation(res, key, options, resolved, lastKey) {\n      var _this3 = this;\n\n      if (this.i18nFormat && this.i18nFormat.parse) {\n        res = this.i18nFormat.parse(res, options, resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n          resolved: resolved\n        });\n      } else if (!options.skipInterpolation) {\n        if (options.interpolation) this.interpolator.init(_objectSpread({}, options, {\n          interpolation: _objectSpread({}, this.options.interpolation, options.interpolation)\n        }));\n        var skipOnVariables = options.interpolation && options.interpolation.skipOnVariables || this.options.interpolation.skipOnVariables;\n        var nestBef;\n\n        if (skipOnVariables) {\n          var nb = res.match(this.interpolator.nestingRegexp);\n          nestBef = nb && nb.length;\n        }\n\n        var data = options.replace && typeof options.replace !== 'string' ? options.replace : options;\n        if (this.options.interpolation.defaultVariables) data = _objectSpread({}, this.options.interpolation.defaultVariables, data);\n        res = this.interpolator.interpolate(res, data, options.lng || this.language, options);\n\n        if (skipOnVariables) {\n          var na = res.match(this.interpolator.nestingRegexp);\n          var nestAft = na && na.length;\n          if (nestBef < nestAft) options.nest = false;\n        }\n\n        if (options.nest !== false) res = this.interpolator.nest(res, function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n\n          if (lastKey && lastKey[0] === args[0] && !options.context) {\n            _this3.logger.warn(\"It seems you are nesting recursively key: \".concat(args[0], \" in key: \").concat(key[0]));\n\n            return null;\n          }\n\n          return _this3.translate.apply(_this3, args.concat([key]));\n        }, options);\n        if (options.interpolation) this.interpolator.reset();\n      }\n\n      var postProcess = options.postProcess || this.options.postProcess;\n      var postProcessorNames = typeof postProcess === 'string' ? [postProcess] : postProcess;\n\n      if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n        res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? _objectSpread({\n          i18nResolved: resolved\n        }, options) : options, this);\n      }\n\n      return res;\n    }\n  }, {\n    key: \"resolve\",\n    value: function resolve(keys) {\n      var _this4 = this;\n\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var found;\n      var usedKey;\n      var exactUsedKey;\n      var usedLng;\n      var usedNS;\n      if (typeof keys === 'string') keys = [keys];\n      keys.forEach(function (k) {\n        if (_this4.isValidLookup(found)) return;\n\n        var extracted = _this4.extractFromKey(k, options);\n\n        var key = extracted.key;\n        usedKey = key;\n        var namespaces = extracted.namespaces;\n        if (_this4.options.fallbackNS) namespaces = namespaces.concat(_this4.options.fallbackNS);\n        var needsPluralHandling = options.count !== undefined && typeof options.count !== 'string';\n        var needsContextHandling = options.context !== undefined && (typeof options.context === 'string' || typeof options.context === 'number') && options.context !== '';\n        var codes = options.lngs ? options.lngs : _this4.languageUtils.toResolveHierarchy(options.lng || _this4.language, options.fallbackLng);\n        namespaces.forEach(function (ns) {\n          if (_this4.isValidLookup(found)) return;\n          usedNS = ns;\n\n          if (!checkedLoadedFor[\"\".concat(codes[0], \"-\").concat(ns)] && _this4.utils && _this4.utils.hasLoadedNamespace && !_this4.utils.hasLoadedNamespace(usedNS)) {\n            checkedLoadedFor[\"\".concat(codes[0], \"-\").concat(ns)] = true;\n\n            _this4.logger.warn(\"key \\\"\".concat(usedKey, \"\\\" for languages \\\"\").concat(codes.join(', '), \"\\\" won't get resolved as namespace \\\"\").concat(usedNS, \"\\\" was not yet loaded\"), 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n          }\n\n          codes.forEach(function (code) {\n            if (_this4.isValidLookup(found)) return;\n            usedLng = code;\n            var finalKey = key;\n            var finalKeys = [finalKey];\n\n            if (_this4.i18nFormat && _this4.i18nFormat.addLookupKeys) {\n              _this4.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n            } else {\n              var pluralSuffix;\n              if (needsPluralHandling) pluralSuffix = _this4.pluralResolver.getSuffix(code, options.count);\n              if (needsPluralHandling && needsContextHandling) finalKeys.push(finalKey + pluralSuffix);\n              if (needsContextHandling) finalKeys.push(finalKey += \"\".concat(_this4.options.contextSeparator).concat(options.context));\n              if (needsPluralHandling) finalKeys.push(finalKey += pluralSuffix);\n            }\n\n            var possibleKey;\n\n            while (possibleKey = finalKeys.pop()) {\n              if (!_this4.isValidLookup(found)) {\n                exactUsedKey = possibleKey;\n                found = _this4.getResource(code, ns, possibleKey, options);\n              }\n            }\n          });\n        });\n      });\n      return {\n        res: found,\n        usedKey: usedKey,\n        exactUsedKey: exactUsedKey,\n        usedLng: usedLng,\n        usedNS: usedNS\n      };\n    }\n  }, {\n    key: \"isValidLookup\",\n    value: function isValidLookup(res) {\n      return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n    }\n  }, {\n    key: \"getResource\",\n    value: function getResource(code, ns, key) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n      if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n      return this.resourceStore.getResource(code, ns, key, options);\n    }\n  }], [{\n    key: \"hasDefaultValue\",\n    value: function hasDefaultValue(options) {\n      var prefix = 'defaultValue';\n\n      for (var option in options) {\n        if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n          return true;\n        }\n      }\n\n      return false;\n    }\n  }]);\n\n  return Translator;\n}(EventEmitter);\n\nfunction capitalize(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\nvar LanguageUtil = function () {\n  function LanguageUtil(options) {\n    _classCallCheck(this, LanguageUtil);\n\n    this.options = options;\n    this.whitelist = this.options.supportedLngs || false;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n\n  _createClass(LanguageUtil, [{\n    key: \"getScriptPartFromCode\",\n    value: function getScriptPartFromCode(code) {\n      if (!code || code.indexOf('-') < 0) return null;\n      var p = code.split('-');\n      if (p.length === 2) return null;\n      p.pop();\n      if (p[p.length - 1].toLowerCase() === 'x') return null;\n      return this.formatLanguageCode(p.join('-'));\n    }\n  }, {\n    key: \"getLanguagePartFromCode\",\n    value: function getLanguagePartFromCode(code) {\n      if (!code || code.indexOf('-') < 0) return code;\n      var p = code.split('-');\n      return this.formatLanguageCode(p[0]);\n    }\n  }, {\n    key: \"formatLanguageCode\",\n    value: function formatLanguageCode(code) {\n      if (typeof code === 'string' && code.indexOf('-') > -1) {\n        var specialCases = ['hans', 'hant', 'latn', 'cyrl', 'cans', 'mong', 'arab'];\n        var p = code.split('-');\n\n        if (this.options.lowerCaseLng) {\n          p = p.map(function (part) {\n            return part.toLowerCase();\n          });\n        } else if (p.length === 2) {\n          p[0] = p[0].toLowerCase();\n          p[1] = p[1].toUpperCase();\n          if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n        } else if (p.length === 3) {\n          p[0] = p[0].toLowerCase();\n          if (p[1].length === 2) p[1] = p[1].toUpperCase();\n          if (p[0] !== 'sgn' && p[2].length === 2) p[2] = p[2].toUpperCase();\n          if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n          if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n        }\n\n        return p.join('-');\n      }\n\n      return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n    }\n  }, {\n    key: \"isWhitelisted\",\n    value: function isWhitelisted(code) {\n      this.logger.deprecate('languageUtils.isWhitelisted', 'function \"isWhitelisted\" will be renamed to \"isSupportedCode\" in the next major - please make sure to rename it\\'s usage asap.');\n      return this.isSupportedCode(code);\n    }\n  }, {\n    key: \"isSupportedCode\",\n    value: function isSupportedCode(code) {\n      if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n        code = this.getLanguagePartFromCode(code);\n      }\n\n      return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n    }\n  }, {\n    key: \"getBestMatchFromCodes\",\n    value: function getBestMatchFromCodes(codes) {\n      var _this = this;\n\n      if (!codes) return null;\n      var found;\n      codes.forEach(function (code) {\n        if (found) return;\n\n        var cleanedLng = _this.formatLanguageCode(code);\n\n        if (!_this.options.supportedLngs || _this.isSupportedCode(cleanedLng)) found = cleanedLng;\n      });\n\n      if (!found && this.options.supportedLngs) {\n        codes.forEach(function (code) {\n          if (found) return;\n\n          var lngOnly = _this.getLanguagePartFromCode(code);\n\n          if (_this.isSupportedCode(lngOnly)) return found = lngOnly;\n          found = _this.options.supportedLngs.find(function (supportedLng) {\n            if (supportedLng.indexOf(lngOnly) === 0) return supportedLng;\n          });\n        });\n      }\n\n      if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n      return found;\n    }\n  }, {\n    key: \"getFallbackCodes\",\n    value: function getFallbackCodes(fallbacks, code) {\n      if (!fallbacks) return [];\n      if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n      if (typeof fallbacks === 'string') fallbacks = [fallbacks];\n      if (Object.prototype.toString.apply(fallbacks) === '[object Array]') return fallbacks;\n      if (!code) return fallbacks[\"default\"] || [];\n      var found = fallbacks[code];\n      if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n      if (!found) found = fallbacks[this.formatLanguageCode(code)];\n      if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n      if (!found) found = fallbacks[\"default\"];\n      return found || [];\n    }\n  }, {\n    key: \"toResolveHierarchy\",\n    value: function toResolveHierarchy(code, fallbackCode) {\n      var _this2 = this;\n\n      var fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n      var codes = [];\n\n      var addCode = function addCode(c) {\n        if (!c) return;\n\n        if (_this2.isSupportedCode(c)) {\n          codes.push(c);\n        } else {\n          _this2.logger.warn(\"rejecting language code not found in supportedLngs: \".concat(c));\n        }\n      };\n\n      if (typeof code === 'string' && code.indexOf('-') > -1) {\n        if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n        if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n        if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n      } else if (typeof code === 'string') {\n        addCode(this.formatLanguageCode(code));\n      }\n\n      fallbackCodes.forEach(function (fc) {\n        if (codes.indexOf(fc) < 0) addCode(_this2.formatLanguageCode(fc));\n      });\n      return codes;\n    }\n  }]);\n\n  return LanguageUtil;\n}();\n\nvar sets = [{\n  lngs: ['ach', 'ak', 'am', 'arn', 'br', 'fil', 'gun', 'ln', 'mfe', 'mg', 'mi', 'oc', 'pt', 'pt-BR', 'tg', 'tl', 'ti', 'tr', 'uz', 'wa'],\n  nr: [1, 2],\n  fc: 1\n}, {\n  lngs: ['af', 'an', 'ast', 'az', 'bg', 'bn', 'ca', 'da', 'de', 'dev', 'el', 'en', 'eo', 'es', 'et', 'eu', 'fi', 'fo', 'fur', 'fy', 'gl', 'gu', 'ha', 'hi', 'hu', 'hy', 'ia', 'it', 'kk', 'kn', 'ku', 'lb', 'mai', 'ml', 'mn', 'mr', 'nah', 'nap', 'nb', 'ne', 'nl', 'nn', 'no', 'nso', 'pa', 'pap', 'pms', 'ps', 'pt-PT', 'rm', 'sco', 'se', 'si', 'so', 'son', 'sq', 'sv', 'sw', 'ta', 'te', 'tk', 'ur', 'yo'],\n  nr: [1, 2],\n  fc: 2\n}, {\n  lngs: ['ay', 'bo', 'cgg', 'fa', 'ht', 'id', 'ja', 'jbo', 'ka', 'km', 'ko', 'ky', 'lo', 'ms', 'sah', 'su', 'th', 'tt', 'ug', 'vi', 'wo', 'zh'],\n  nr: [1],\n  fc: 3\n}, {\n  lngs: ['be', 'bs', 'cnr', 'dz', 'hr', 'ru', 'sr', 'uk'],\n  nr: [1, 2, 5],\n  fc: 4\n}, {\n  lngs: ['ar'],\n  nr: [0, 1, 2, 3, 11, 100],\n  fc: 5\n}, {\n  lngs: ['cs', 'sk'],\n  nr: [1, 2, 5],\n  fc: 6\n}, {\n  lngs: ['csb', 'pl'],\n  nr: [1, 2, 5],\n  fc: 7\n}, {\n  lngs: ['cy'],\n  nr: [1, 2, 3, 8],\n  fc: 8\n}, {\n  lngs: ['fr'],\n  nr: [1, 2],\n  fc: 9\n}, {\n  lngs: ['ga'],\n  nr: [1, 2, 3, 7, 11],\n  fc: 10\n}, {\n  lngs: ['gd'],\n  nr: [1, 2, 3, 20],\n  fc: 11\n}, {\n  lngs: ['is'],\n  nr: [1, 2],\n  fc: 12\n}, {\n  lngs: ['jv'],\n  nr: [0, 1],\n  fc: 13\n}, {\n  lngs: ['kw'],\n  nr: [1, 2, 3, 4],\n  fc: 14\n}, {\n  lngs: ['lt'],\n  nr: [1, 2, 10],\n  fc: 15\n}, {\n  lngs: ['lv'],\n  nr: [1, 2, 0],\n  fc: 16\n}, {\n  lngs: ['mk'],\n  nr: [1, 2],\n  fc: 17\n}, {\n  lngs: ['mnk'],\n  nr: [0, 1, 2],\n  fc: 18\n}, {\n  lngs: ['mt'],\n  nr: [1, 2, 11, 20],\n  fc: 19\n}, {\n  lngs: ['or'],\n  nr: [2, 1],\n  fc: 2\n}, {\n  lngs: ['ro'],\n  nr: [1, 2, 20],\n  fc: 20\n}, {\n  lngs: ['sl'],\n  nr: [5, 1, 2, 3],\n  fc: 21\n}, {\n  lngs: ['he', 'iw'],\n  nr: [1, 2, 20, 21],\n  fc: 22\n}];\nvar _rulesPluralsTypes = {\n  1: function _(n) {\n    return Number(n > 1);\n  },\n  2: function _(n) {\n    return Number(n != 1);\n  },\n  3: function _(n) {\n    return 0;\n  },\n  4: function _(n) {\n    return Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n  },\n  5: function _(n) {\n    return Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5);\n  },\n  6: function _(n) {\n    return Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2);\n  },\n  7: function _(n) {\n    return Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n  },\n  8: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3);\n  },\n  9: function _(n) {\n    return Number(n >= 2);\n  },\n  10: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4);\n  },\n  11: function _(n) {\n    return Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3);\n  },\n  12: function _(n) {\n    return Number(n % 10 != 1 || n % 100 == 11);\n  },\n  13: function _(n) {\n    return Number(n !== 0);\n  },\n  14: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3);\n  },\n  15: function _(n) {\n    return Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n  },\n  16: function _(n) {\n    return Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2);\n  },\n  17: function _(n) {\n    return Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1);\n  },\n  18: function _(n) {\n    return Number(n == 0 ? 0 : n == 1 ? 1 : 2);\n  },\n  19: function _(n) {\n    return Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3);\n  },\n  20: function _(n) {\n    return Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2);\n  },\n  21: function _(n) {\n    return Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0);\n  },\n  22: function _(n) {\n    return Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3);\n  }\n};\n\nfunction createRules() {\n  var rules = {};\n  sets.forEach(function (set) {\n    set.lngs.forEach(function (l) {\n      rules[l] = {\n        numbers: set.nr,\n        plurals: _rulesPluralsTypes[set.fc]\n      };\n    });\n  });\n  return rules;\n}\n\nvar PluralResolver = function () {\n  function PluralResolver(languageUtils) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, PluralResolver);\n\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.rules = createRules();\n  }\n\n  _createClass(PluralResolver, [{\n    key: \"addRule\",\n    value: function addRule(lng, obj) {\n      this.rules[lng] = obj;\n    }\n  }, {\n    key: \"getRule\",\n    value: function getRule(code) {\n      return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n    }\n  }, {\n    key: \"needsPlural\",\n    value: function needsPlural(code) {\n      var rule = this.getRule(code);\n      return rule && rule.numbers.length > 1;\n    }\n  }, {\n    key: \"getPluralFormsOfKey\",\n    value: function getPluralFormsOfKey(code, key) {\n      return this.getSuffixes(code).map(function (suffix) {\n        return key + suffix;\n      });\n    }\n  }, {\n    key: \"getSuffixes\",\n    value: function getSuffixes(code) {\n      var _this = this;\n\n      var rule = this.getRule(code);\n\n      if (!rule) {\n        return [];\n      }\n\n      return rule.numbers.map(function (number) {\n        return _this.getSuffix(code, number);\n      });\n    }\n  }, {\n    key: \"getSuffix\",\n    value: function getSuffix(code, count) {\n      var _this2 = this;\n\n      var rule = this.getRule(code);\n\n      if (rule) {\n        var idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n        var suffix = rule.numbers[idx];\n\n        if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n          if (suffix === 2) {\n            suffix = 'plural';\n          } else if (suffix === 1) {\n            suffix = '';\n          }\n        }\n\n        var returnSuffix = function returnSuffix() {\n          return _this2.options.prepend && suffix.toString() ? _this2.options.prepend + suffix.toString() : suffix.toString();\n        };\n\n        if (this.options.compatibilityJSON === 'v1') {\n          if (suffix === 1) return '';\n          if (typeof suffix === 'number') return \"_plural_\".concat(suffix.toString());\n          return returnSuffix();\n        } else if (this.options.compatibilityJSON === 'v2') {\n          return returnSuffix();\n        } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n          return returnSuffix();\n        }\n\n        return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n      }\n\n      this.logger.warn(\"no plural rule found for: \".concat(code));\n      return '';\n    }\n  }]);\n\n  return PluralResolver;\n}();\n\nvar Interpolator = function () {\n  function Interpolator() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, Interpolator);\n\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n\n    this.format = options.interpolation && options.interpolation.format || function (value) {\n      return value;\n    };\n\n    this.init(options);\n  }\n\n  _createClass(Interpolator, [{\n    key: \"init\",\n    value: function init() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (!options.interpolation) options.interpolation = {\n        escapeValue: true\n      };\n      var iOpts = options.interpolation;\n      this.escape = iOpts.escape !== undefined ? iOpts.escape : escape;\n      this.escapeValue = iOpts.escapeValue !== undefined ? iOpts.escapeValue : true;\n      this.useRawValueToEscape = iOpts.useRawValueToEscape !== undefined ? iOpts.useRawValueToEscape : false;\n      this.prefix = iOpts.prefix ? regexEscape(iOpts.prefix) : iOpts.prefixEscaped || '{{';\n      this.suffix = iOpts.suffix ? regexEscape(iOpts.suffix) : iOpts.suffixEscaped || '}}';\n      this.formatSeparator = iOpts.formatSeparator ? iOpts.formatSeparator : iOpts.formatSeparator || ',';\n      this.unescapePrefix = iOpts.unescapeSuffix ? '' : iOpts.unescapePrefix || '-';\n      this.unescapeSuffix = this.unescapePrefix ? '' : iOpts.unescapeSuffix || '';\n      this.nestingPrefix = iOpts.nestingPrefix ? regexEscape(iOpts.nestingPrefix) : iOpts.nestingPrefixEscaped || regexEscape('$t(');\n      this.nestingSuffix = iOpts.nestingSuffix ? regexEscape(iOpts.nestingSuffix) : iOpts.nestingSuffixEscaped || regexEscape(')');\n      this.nestingOptionsSeparator = iOpts.nestingOptionsSeparator ? iOpts.nestingOptionsSeparator : iOpts.nestingOptionsSeparator || ',';\n      this.maxReplaces = iOpts.maxReplaces ? iOpts.maxReplaces : 1000;\n      this.alwaysFormat = iOpts.alwaysFormat !== undefined ? iOpts.alwaysFormat : false;\n      this.resetRegExp();\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      if (this.options) this.init(this.options);\n    }\n  }, {\n    key: \"resetRegExp\",\n    value: function resetRegExp() {\n      var regexpStr = \"\".concat(this.prefix, \"(.+?)\").concat(this.suffix);\n      this.regexp = new RegExp(regexpStr, 'g');\n      var regexpUnescapeStr = \"\".concat(this.prefix).concat(this.unescapePrefix, \"(.+?)\").concat(this.unescapeSuffix).concat(this.suffix);\n      this.regexpUnescape = new RegExp(regexpUnescapeStr, 'g');\n      var nestingRegexpStr = \"\".concat(this.nestingPrefix, \"(.+?)\").concat(this.nestingSuffix);\n      this.nestingRegexp = new RegExp(nestingRegexpStr, 'g');\n    }\n  }, {\n    key: \"interpolate\",\n    value: function interpolate(str, data, lng, options) {\n      var _this = this;\n\n      var match;\n      var value;\n      var replaces;\n      var defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n\n      function regexSafe(val) {\n        return val.replace(/\\$/g, '$$$$');\n      }\n\n      var handleFormat = function handleFormat(key) {\n        if (key.indexOf(_this.formatSeparator) < 0) {\n          var path = getPathWithDefaults(data, defaultData, key);\n          return _this.alwaysFormat ? _this.format(path, undefined, lng, _objectSpread({}, options, data, {\n            interpolationkey: key\n          })) : path;\n        }\n\n        var p = key.split(_this.formatSeparator);\n        var k = p.shift().trim();\n        var f = p.join(_this.formatSeparator).trim();\n        return _this.format(getPathWithDefaults(data, defaultData, k), f, lng, _objectSpread({}, options, data, {\n          interpolationkey: k\n        }));\n      };\n\n      this.resetRegExp();\n      var missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n      var skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables || this.options.interpolation.skipOnVariables;\n      var todos = [{\n        regex: this.regexpUnescape,\n        safeValue: function safeValue(val) {\n          return regexSafe(val);\n        }\n      }, {\n        regex: this.regexp,\n        safeValue: function safeValue(val) {\n          return _this.escapeValue ? regexSafe(_this.escape(val)) : regexSafe(val);\n        }\n      }];\n      todos.forEach(function (todo) {\n        replaces = 0;\n\n        while (match = todo.regex.exec(str)) {\n          value = handleFormat(match[1].trim());\n\n          if (value === undefined) {\n            if (typeof missingInterpolationHandler === 'function') {\n              var temp = missingInterpolationHandler(str, match, options);\n              value = typeof temp === 'string' ? temp : '';\n            } else if (skipOnVariables) {\n              value = match[0];\n              continue;\n            } else {\n              _this.logger.warn(\"missed to pass in variable \".concat(match[1], \" for interpolating \").concat(str));\n\n              value = '';\n            }\n          } else if (typeof value !== 'string' && !_this.useRawValueToEscape) {\n            value = makeString(value);\n          }\n\n          var safeValue = todo.safeValue(value);\n          str = str.replace(match[0], safeValue);\n\n          if (skipOnVariables) {\n            todo.regex.lastIndex += safeValue.length;\n            todo.regex.lastIndex -= match[0].length;\n          } else {\n            todo.regex.lastIndex = 0;\n          }\n\n          replaces++;\n\n          if (replaces >= _this.maxReplaces) {\n            break;\n          }\n        }\n      });\n      return str;\n    }\n  }, {\n    key: \"nest\",\n    value: function nest(str, fc) {\n      var _this2 = this;\n\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      var match;\n      var value;\n\n      var clonedOptions = _objectSpread({}, options);\n\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n\n      function handleHasOptions(key, inheritedOptions) {\n        var sep = this.nestingOptionsSeparator;\n        if (key.indexOf(sep) < 0) return key;\n        var c = key.split(new RegExp(\"\".concat(sep, \"[ ]*{\")));\n        var optionsString = \"{\".concat(c[1]);\n        key = c[0];\n        optionsString = this.interpolate(optionsString, clonedOptions);\n        optionsString = optionsString.replace(/'/g, '\"');\n\n        try {\n          clonedOptions = JSON.parse(optionsString);\n          if (inheritedOptions) clonedOptions = _objectSpread({}, inheritedOptions, clonedOptions);\n        } catch (e) {\n          this.logger.warn(\"failed parsing options string in nesting for key \".concat(key), e);\n          return \"\".concat(key).concat(sep).concat(optionsString);\n        }\n\n        delete clonedOptions.defaultValue;\n        return key;\n      }\n\n      while (match = this.nestingRegexp.exec(str)) {\n        var formatters = [];\n        var doReduce = false;\n\n        if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n          var r = match[1].split(this.formatSeparator).map(function (elem) {\n            return elem.trim();\n          });\n          match[1] = r.shift();\n          formatters = r;\n          doReduce = true;\n        }\n\n        value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n        if (value && match[0] === str && typeof value !== 'string') return value;\n        if (typeof value !== 'string') value = makeString(value);\n\n        if (!value) {\n          this.logger.warn(\"missed to resolve \".concat(match[1], \" for nesting \").concat(str));\n          value = '';\n        }\n\n        if (doReduce) {\n          value = formatters.reduce(function (v, f) {\n            return _this2.format(v, f, options.lng, _objectSpread({}, options, {\n              interpolationkey: match[1].trim()\n            }));\n          }, value.trim());\n        }\n\n        str = str.replace(match[0], value);\n        this.regexp.lastIndex = 0;\n      }\n\n      return str;\n    }\n  }]);\n\n  return Interpolator;\n}();\n\nfunction remove(arr, what) {\n  var found = arr.indexOf(what);\n\n  while (found !== -1) {\n    arr.splice(found, 1);\n    found = arr.indexOf(what);\n  }\n}\n\nvar Connector = function (_EventEmitter) {\n  _inherits(Connector, _EventEmitter);\n\n  function Connector(backend, store, services) {\n    var _this;\n\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n\n    _classCallCheck(this, Connector);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Connector).call(this));\n\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n\n    _this.backend = backend;\n    _this.store = store;\n    _this.services = services;\n    _this.languageUtils = services.languageUtils;\n    _this.options = options;\n    _this.logger = baseLogger.create('backendConnector');\n    _this.state = {};\n    _this.queue = [];\n\n    if (_this.backend && _this.backend.init) {\n      _this.backend.init(services, options.backend, options);\n    }\n\n    return _this;\n  }\n\n  _createClass(Connector, [{\n    key: \"queueLoad\",\n    value: function queueLoad(languages, namespaces, options, callback) {\n      var _this2 = this;\n\n      var toLoad = [];\n      var pending = [];\n      var toLoadLanguages = [];\n      var toLoadNamespaces = [];\n      languages.forEach(function (lng) {\n        var hasAllNamespaces = true;\n        namespaces.forEach(function (ns) {\n          var name = \"\".concat(lng, \"|\").concat(ns);\n\n          if (!options.reload && _this2.store.hasResourceBundle(lng, ns)) {\n            _this2.state[name] = 2;\n          } else if (_this2.state[name] < 0) ; else if (_this2.state[name] === 1) {\n            if (pending.indexOf(name) < 0) pending.push(name);\n          } else {\n            _this2.state[name] = 1;\n            hasAllNamespaces = false;\n            if (pending.indexOf(name) < 0) pending.push(name);\n            if (toLoad.indexOf(name) < 0) toLoad.push(name);\n            if (toLoadNamespaces.indexOf(ns) < 0) toLoadNamespaces.push(ns);\n          }\n        });\n        if (!hasAllNamespaces) toLoadLanguages.push(lng);\n      });\n\n      if (toLoad.length || pending.length) {\n        this.queue.push({\n          pending: pending,\n          loaded: {},\n          errors: [],\n          callback: callback\n        });\n      }\n\n      return {\n        toLoad: toLoad,\n        pending: pending,\n        toLoadLanguages: toLoadLanguages,\n        toLoadNamespaces: toLoadNamespaces\n      };\n    }\n  }, {\n    key: \"loaded\",\n    value: function loaded(name, err, data) {\n      var s = name.split('|');\n      var lng = s[0];\n      var ns = s[1];\n      if (err) this.emit('failedLoading', lng, ns, err);\n\n      if (data) {\n        this.store.addResourceBundle(lng, ns, data);\n      }\n\n      this.state[name] = err ? -1 : 2;\n      var loaded = {};\n      this.queue.forEach(function (q) {\n        pushPath(q.loaded, [lng], ns);\n        remove(q.pending, name);\n        if (err) q.errors.push(err);\n\n        if (q.pending.length === 0 && !q.done) {\n          Object.keys(q.loaded).forEach(function (l) {\n            if (!loaded[l]) loaded[l] = [];\n\n            if (q.loaded[l].length) {\n              q.loaded[l].forEach(function (ns) {\n                if (loaded[l].indexOf(ns) < 0) loaded[l].push(ns);\n              });\n            }\n          });\n          q.done = true;\n\n          if (q.errors.length) {\n            q.callback(q.errors);\n          } else {\n            q.callback();\n          }\n        }\n      });\n      this.emit('loaded', loaded);\n      this.queue = this.queue.filter(function (q) {\n        return !q.done;\n      });\n    }\n  }, {\n    key: \"read\",\n    value: function read(lng, ns, fcName) {\n      var _this3 = this;\n\n      var tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n      var wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 350;\n      var callback = arguments.length > 5 ? arguments[5] : undefined;\n      if (!lng.length) return callback(null, {});\n      return this.backend[fcName](lng, ns, function (err, data) {\n        if (err && data && tried < 5) {\n          setTimeout(function () {\n            _this3.read.call(_this3, lng, ns, fcName, tried + 1, wait * 2, callback);\n          }, wait);\n          return;\n        }\n\n        callback(err, data);\n      });\n    }\n  }, {\n    key: \"prepareLoading\",\n    value: function prepareLoading(languages, namespaces) {\n      var _this4 = this;\n\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      var callback = arguments.length > 3 ? arguments[3] : undefined;\n\n      if (!this.backend) {\n        this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n        return callback && callback();\n      }\n\n      if (typeof languages === 'string') languages = this.languageUtils.toResolveHierarchy(languages);\n      if (typeof namespaces === 'string') namespaces = [namespaces];\n      var toLoad = this.queueLoad(languages, namespaces, options, callback);\n\n      if (!toLoad.toLoad.length) {\n        if (!toLoad.pending.length) callback();\n        return null;\n      }\n\n      toLoad.toLoad.forEach(function (name) {\n        _this4.loadOne(name);\n      });\n    }\n  }, {\n    key: \"load\",\n    value: function load(languages, namespaces, callback) {\n      this.prepareLoading(languages, namespaces, {}, callback);\n    }\n  }, {\n    key: \"reload\",\n    value: function reload(languages, namespaces, callback) {\n      this.prepareLoading(languages, namespaces, {\n        reload: true\n      }, callback);\n    }\n  }, {\n    key: \"loadOne\",\n    value: function loadOne(name) {\n      var _this5 = this;\n\n      var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      var s = name.split('|');\n      var lng = s[0];\n      var ns = s[1];\n      this.read(lng, ns, 'read', undefined, undefined, function (err, data) {\n        if (err) _this5.logger.warn(\"\".concat(prefix, \"loading namespace \").concat(ns, \" for language \").concat(lng, \" failed\"), err);\n        if (!err && data) _this5.logger.log(\"\".concat(prefix, \"loaded namespace \").concat(ns, \" for language \").concat(lng), data);\n\n        _this5.loaded(name, err, data);\n      });\n    }\n  }, {\n    key: \"saveMissing\",\n    value: function saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n      var options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n\n      if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n        this.logger.warn(\"did not save key \\\"\".concat(key, \"\\\" as the namespace \\\"\").concat(namespace, \"\\\" was not yet loaded\"), 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        return;\n      }\n\n      if (key === undefined || key === null || key === '') return;\n\n      if (this.backend && this.backend.create) {\n        this.backend.create(languages, namespace, key, fallbackValue, null, _objectSpread({}, options, {\n          isUpdate: isUpdate\n        }));\n      }\n\n      if (!languages || !languages[0]) return;\n      this.store.addResource(languages[0], namespace, key, fallbackValue);\n    }\n  }]);\n\n  return Connector;\n}(EventEmitter);\n\nfunction get() {\n  return {\n    debug: false,\n    initImmediate: true,\n    ns: ['translation'],\n    defaultNS: ['translation'],\n    fallbackLng: ['dev'],\n    fallbackNS: false,\n    whitelist: false,\n    nonExplicitWhitelist: false,\n    supportedLngs: false,\n    nonExplicitSupportedLngs: false,\n    load: 'all',\n    preload: false,\n    simplifyPluralSuffix: true,\n    keySeparator: '.',\n    nsSeparator: ':',\n    pluralSeparator: '_',\n    contextSeparator: '_',\n    partialBundledLanguages: false,\n    saveMissing: false,\n    updateMissing: false,\n    saveMissingTo: 'fallback',\n    saveMissingPlurals: true,\n    missingKeyHandler: false,\n    missingInterpolationHandler: false,\n    postProcess: false,\n    postProcessPassResolved: false,\n    returnNull: true,\n    returnEmptyString: true,\n    returnObjects: false,\n    joinArrays: false,\n    returnedObjectHandler: false,\n    parseMissingKeyHandler: false,\n    appendNamespaceToMissingKey: false,\n    appendNamespaceToCIMode: false,\n    overloadTranslationOptionHandler: function handle(args) {\n      var ret = {};\n      if (_typeof(args[1]) === 'object') ret = args[1];\n      if (typeof args[1] === 'string') ret.defaultValue = args[1];\n      if (typeof args[2] === 'string') ret.tDescription = args[2];\n\n      if (_typeof(args[2]) === 'object' || _typeof(args[3]) === 'object') {\n        var options = args[3] || args[2];\n        Object.keys(options).forEach(function (key) {\n          ret[key] = options[key];\n        });\n      }\n\n      return ret;\n    },\n    interpolation: {\n      escapeValue: true,\n      format: function format(value, _format, lng, options) {\n        return value;\n      },\n      prefix: '{{',\n      suffix: '}}',\n      formatSeparator: ',',\n      unescapePrefix: '-',\n      nestingPrefix: '$t(',\n      nestingSuffix: ')',\n      nestingOptionsSeparator: ',',\n      maxReplaces: 1000,\n      skipOnVariables: false\n    }\n  };\n}\nfunction transformOptions(options) {\n  if (typeof options.ns === 'string') options.ns = [options.ns];\n  if (typeof options.fallbackLng === 'string') options.fallbackLng = [options.fallbackLng];\n  if (typeof options.fallbackNS === 'string') options.fallbackNS = [options.fallbackNS];\n\n  if (options.whitelist) {\n    if (options.whitelist && options.whitelist.indexOf('cimode') < 0) {\n      options.whitelist = options.whitelist.concat(['cimode']);\n    }\n\n    options.supportedLngs = options.whitelist;\n  }\n\n  if (options.nonExplicitWhitelist) {\n    options.nonExplicitSupportedLngs = options.nonExplicitWhitelist;\n  }\n\n  if (options.supportedLngs && options.supportedLngs.indexOf('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n\n  return options;\n}\n\nfunction noop() {}\n\nvar I18n = function (_EventEmitter) {\n  _inherits(I18n, _EventEmitter);\n\n  function I18n() {\n    var _this;\n\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var callback = arguments.length > 1 ? arguments[1] : undefined;\n\n    _classCallCheck(this, I18n);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(I18n).call(this));\n\n    if (isIE10) {\n      EventEmitter.call(_assertThisInitialized(_this));\n    }\n\n    _this.options = transformOptions(options);\n    _this.services = {};\n    _this.logger = baseLogger;\n    _this.modules = {\n      external: []\n    };\n\n    if (callback && !_this.isInitialized && !options.isClone) {\n      if (!_this.options.initImmediate) {\n        _this.init(options, callback);\n\n        return _possibleConstructorReturn(_this, _assertThisInitialized(_this));\n      }\n\n      setTimeout(function () {\n        _this.init(options, callback);\n      }, 0);\n    }\n\n    return _this;\n  }\n\n  _createClass(I18n, [{\n    key: \"init\",\n    value: function init() {\n      var _this2 = this;\n\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var callback = arguments.length > 1 ? arguments[1] : undefined;\n\n      if (typeof options === 'function') {\n        callback = options;\n        options = {};\n      }\n\n      if (options.whitelist && !options.supportedLngs) {\n        this.logger.deprecate('whitelist', 'option \"whitelist\" will be renamed to \"supportedLngs\" in the next major - please make sure to rename this option asap.');\n      }\n\n      if (options.nonExplicitWhitelist && !options.nonExplicitSupportedLngs) {\n        this.logger.deprecate('whitelist', 'options \"nonExplicitWhitelist\" will be renamed to \"nonExplicitSupportedLngs\" in the next major - please make sure to rename this option asap.');\n      }\n\n      this.options = _objectSpread({}, get(), this.options, transformOptions(options));\n      this.format = this.options.interpolation.format;\n      if (!callback) callback = noop;\n\n      function createClassOnDemand(ClassOrObject) {\n        if (!ClassOrObject) return null;\n        if (typeof ClassOrObject === 'function') return new ClassOrObject();\n        return ClassOrObject;\n      }\n\n      if (!this.options.isClone) {\n        if (this.modules.logger) {\n          baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n        } else {\n          baseLogger.init(null, this.options);\n        }\n\n        var lu = new LanguageUtil(this.options);\n        this.store = new ResourceStore(this.options.resources, this.options);\n        var s = this.services;\n        s.logger = baseLogger;\n        s.resourceStore = this.store;\n        s.languageUtils = lu;\n        s.pluralResolver = new PluralResolver(lu, {\n          prepend: this.options.pluralSeparator,\n          compatibilityJSON: this.options.compatibilityJSON,\n          simplifyPluralSuffix: this.options.simplifyPluralSuffix\n        });\n        s.interpolator = new Interpolator(this.options);\n        s.utils = {\n          hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n        };\n        s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n        s.backendConnector.on('*', function (event) {\n          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n            args[_key - 1] = arguments[_key];\n          }\n\n          _this2.emit.apply(_this2, [event].concat(args));\n        });\n\n        if (this.modules.languageDetector) {\n          s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n          s.languageDetector.init(s, this.options.detection, this.options);\n        }\n\n        if (this.modules.i18nFormat) {\n          s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n          if (s.i18nFormat.init) s.i18nFormat.init(this);\n        }\n\n        this.translator = new Translator(this.services, this.options);\n        this.translator.on('*', function (event) {\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n\n          _this2.emit.apply(_this2, [event].concat(args));\n        });\n        this.modules.external.forEach(function (m) {\n          if (m.init) m.init(_this2);\n        });\n      }\n\n      if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n        var codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n      }\n\n      if (!this.services.languageDetector && !this.options.lng) {\n        this.logger.warn('init: no languageDetector is used and no lng is defined');\n      }\n\n      var storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n      storeApi.forEach(function (fcName) {\n        _this2[fcName] = function () {\n          var _this2$store;\n\n          return (_this2$store = _this2.store)[fcName].apply(_this2$store, arguments);\n        };\n      });\n      var storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n      storeApiChained.forEach(function (fcName) {\n        _this2[fcName] = function () {\n          var _this2$store2;\n\n          (_this2$store2 = _this2.store)[fcName].apply(_this2$store2, arguments);\n\n          return _this2;\n        };\n      });\n      var deferred = defer();\n\n      var load = function load() {\n        var finish = function finish(err, t) {\n          if (_this2.isInitialized && !_this2.initializedStoreOnce) _this2.logger.warn('init: i18next is already initialized. You should call init just once!');\n          _this2.isInitialized = true;\n          if (!_this2.options.isClone) _this2.logger.log('initialized', _this2.options);\n\n          _this2.emit('initialized', _this2.options);\n\n          deferred.resolve(t);\n          callback(err, t);\n        };\n\n        if (_this2.languages && _this2.options.compatibilityAPI !== 'v1' && !_this2.isInitialized) return finish(null, _this2.t.bind(_this2));\n\n        _this2.changeLanguage(_this2.options.lng, finish);\n      };\n\n      if (this.options.resources || !this.options.initImmediate) {\n        load();\n      } else {\n        setTimeout(load, 0);\n      }\n\n      return deferred;\n    }\n  }, {\n    key: \"loadResources\",\n    value: function loadResources(language) {\n      var _this3 = this;\n\n      var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n      var usedCallback = callback;\n      var usedLng = typeof language === 'string' ? language : this.language;\n      if (typeof language === 'function') usedCallback = language;\n\n      if (!this.options.resources || this.options.partialBundledLanguages) {\n        if (usedLng && usedLng.toLowerCase() === 'cimode') return usedCallback();\n        var toLoad = [];\n\n        var append = function append(lng) {\n          if (!lng) return;\n\n          var lngs = _this3.services.languageUtils.toResolveHierarchy(lng);\n\n          lngs.forEach(function (l) {\n            if (toLoad.indexOf(l) < 0) toLoad.push(l);\n          });\n        };\n\n        if (!usedLng) {\n          var fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n          fallbacks.forEach(function (l) {\n            return append(l);\n          });\n        } else {\n          append(usedLng);\n        }\n\n        if (this.options.preload) {\n          this.options.preload.forEach(function (l) {\n            return append(l);\n          });\n        }\n\n        this.services.backendConnector.load(toLoad, this.options.ns, usedCallback);\n      } else {\n        usedCallback(null);\n      }\n    }\n  }, {\n    key: \"reloadResources\",\n    value: function reloadResources(lngs, ns, callback) {\n      var deferred = defer();\n      if (!lngs) lngs = this.languages;\n      if (!ns) ns = this.options.ns;\n      if (!callback) callback = noop;\n      this.services.backendConnector.reload(lngs, ns, function (err) {\n        deferred.resolve();\n        callback(err);\n      });\n      return deferred;\n    }\n  }, {\n    key: \"use\",\n    value: function use(module) {\n      if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n      if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n\n      if (module.type === 'backend') {\n        this.modules.backend = module;\n      }\n\n      if (module.type === 'logger' || module.log && module.warn && module.error) {\n        this.modules.logger = module;\n      }\n\n      if (module.type === 'languageDetector') {\n        this.modules.languageDetector = module;\n      }\n\n      if (module.type === 'i18nFormat') {\n        this.modules.i18nFormat = module;\n      }\n\n      if (module.type === 'postProcessor') {\n        postProcessor.addPostProcessor(module);\n      }\n\n      if (module.type === '3rdParty') {\n        this.modules.external.push(module);\n      }\n\n      return this;\n    }\n  }, {\n    key: \"changeLanguage\",\n    value: function changeLanguage(lng, callback) {\n      var _this4 = this;\n\n      this.isLanguageChangingTo = lng;\n      var deferred = defer();\n      this.emit('languageChanging', lng);\n\n      var done = function done(err, l) {\n        if (l) {\n          _this4.language = l;\n          _this4.languages = _this4.services.languageUtils.toResolveHierarchy(l);\n\n          _this4.translator.changeLanguage(l);\n\n          _this4.isLanguageChangingTo = undefined;\n\n          _this4.emit('languageChanged', l);\n\n          _this4.logger.log('languageChanged', l);\n        } else {\n          _this4.isLanguageChangingTo = undefined;\n        }\n\n        deferred.resolve(function () {\n          return _this4.t.apply(_this4, arguments);\n        });\n        if (callback) callback(err, function () {\n          return _this4.t.apply(_this4, arguments);\n        });\n      };\n\n      var setLng = function setLng(lngs) {\n        if (!lng && !lngs && _this4.services.languageDetector) lngs = [];\n        var l = typeof lngs === 'string' ? lngs : _this4.services.languageUtils.getBestMatchFromCodes(lngs);\n\n        if (l) {\n          if (!_this4.language) {\n            _this4.language = l;\n            _this4.languages = _this4.services.languageUtils.toResolveHierarchy(l);\n          }\n\n          if (!_this4.translator.language) _this4.translator.changeLanguage(l);\n          if (_this4.services.languageDetector) _this4.services.languageDetector.cacheUserLanguage(l);\n        }\n\n        _this4.loadResources(l, function (err) {\n          done(err, l);\n        });\n      };\n\n      if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n        setLng(this.services.languageDetector.detect());\n      } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n        this.services.languageDetector.detect(setLng);\n      } else {\n        setLng(lng);\n      }\n\n      return deferred;\n    }\n  }, {\n    key: \"getFixedT\",\n    value: function getFixedT(lng, ns, keyPrefix) {\n      var _this5 = this;\n\n      var fixedT = function fixedT(key, opts) {\n        var options;\n\n        if (_typeof(opts) !== 'object') {\n          for (var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n            rest[_key3 - 2] = arguments[_key3];\n          }\n\n          options = _this5.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n        } else {\n          options = _objectSpread({}, opts);\n        }\n\n        options.lng = options.lng || fixedT.lng;\n        options.lngs = options.lngs || fixedT.lngs;\n        options.ns = options.ns || fixedT.ns;\n        var keySeparator = _this5.options.keySeparator || '.';\n        var resultKey = keyPrefix ? \"\".concat(keyPrefix).concat(keySeparator).concat(key) : key;\n        return _this5.t(resultKey, options);\n      };\n\n      if (typeof lng === 'string') {\n        fixedT.lng = lng;\n      } else {\n        fixedT.lngs = lng;\n      }\n\n      fixedT.ns = ns;\n      fixedT.keyPrefix = keyPrefix;\n      return fixedT;\n    }\n  }, {\n    key: \"t\",\n    value: function t() {\n      var _this$translator;\n\n      return this.translator && (_this$translator = this.translator).translate.apply(_this$translator, arguments);\n    }\n  }, {\n    key: \"exists\",\n    value: function exists() {\n      var _this$translator2;\n\n      return this.translator && (_this$translator2 = this.translator).exists.apply(_this$translator2, arguments);\n    }\n  }, {\n    key: \"setDefaultNamespace\",\n    value: function setDefaultNamespace(ns) {\n      this.options.defaultNS = ns;\n    }\n  }, {\n    key: \"hasLoadedNamespace\",\n    value: function hasLoadedNamespace(ns) {\n      var _this6 = this;\n\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n      if (!this.isInitialized) {\n        this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n        return false;\n      }\n\n      if (!this.languages || !this.languages.length) {\n        this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n        return false;\n      }\n\n      var lng = this.languages[0];\n      var fallbackLng = this.options ? this.options.fallbackLng : false;\n      var lastLng = this.languages[this.languages.length - 1];\n      if (lng.toLowerCase() === 'cimode') return true;\n\n      var loadNotPending = function loadNotPending(l, n) {\n        var loadState = _this6.services.backendConnector.state[\"\".concat(l, \"|\").concat(n)];\n\n        return loadState === -1 || loadState === 2;\n      };\n\n      if (options.precheck) {\n        var preResult = options.precheck(this, loadNotPending);\n        if (preResult !== undefined) return preResult;\n      }\n\n      if (this.hasResourceBundle(lng, ns)) return true;\n      if (!this.services.backendConnector.backend) return true;\n      if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n      return false;\n    }\n  }, {\n    key: \"loadNamespaces\",\n    value: function loadNamespaces(ns, callback) {\n      var _this7 = this;\n\n      var deferred = defer();\n\n      if (!this.options.ns) {\n        callback && callback();\n        return Promise.resolve();\n      }\n\n      if (typeof ns === 'string') ns = [ns];\n      ns.forEach(function (n) {\n        if (_this7.options.ns.indexOf(n) < 0) _this7.options.ns.push(n);\n      });\n      this.loadResources(function (err) {\n        deferred.resolve();\n        if (callback) callback(err);\n      });\n      return deferred;\n    }\n  }, {\n    key: \"loadLanguages\",\n    value: function loadLanguages(lngs, callback) {\n      var deferred = defer();\n      if (typeof lngs === 'string') lngs = [lngs];\n      var preloaded = this.options.preload || [];\n      var newLngs = lngs.filter(function (lng) {\n        return preloaded.indexOf(lng) < 0;\n      });\n\n      if (!newLngs.length) {\n        if (callback) callback();\n        return Promise.resolve();\n      }\n\n      this.options.preload = preloaded.concat(newLngs);\n      this.loadResources(function (err) {\n        deferred.resolve();\n        if (callback) callback(err);\n      });\n      return deferred;\n    }\n  }, {\n    key: \"dir\",\n    value: function dir(lng) {\n      if (!lng) lng = this.languages && this.languages.length > 0 ? this.languages[0] : this.language;\n      if (!lng) return 'rtl';\n      var rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam'];\n      return rtlLngs.indexOf(this.services.languageUtils.getLanguagePartFromCode(lng)) >= 0 ? 'rtl' : 'ltr';\n    }\n  }, {\n    key: \"createInstance\",\n    value: function createInstance() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var callback = arguments.length > 1 ? arguments[1] : undefined;\n      return new I18n(options, callback);\n    }\n  }, {\n    key: \"cloneInstance\",\n    value: function cloneInstance() {\n      var _this8 = this;\n\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n\n      var mergedOptions = _objectSpread({}, this.options, options, {\n        isClone: true\n      });\n\n      var clone = new I18n(mergedOptions);\n      var membersToCopy = ['store', 'services', 'language'];\n      membersToCopy.forEach(function (m) {\n        clone[m] = _this8[m];\n      });\n      clone.services = _objectSpread({}, this.services);\n      clone.services.utils = {\n        hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n      };\n      clone.translator = new Translator(clone.services, clone.options);\n      clone.translator.on('*', function (event) {\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n\n        clone.emit.apply(clone, [event].concat(args));\n      });\n      clone.init(mergedOptions, callback);\n      clone.translator.options = clone.options;\n      clone.translator.backendConnector.services.utils = {\n        hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n      };\n      return clone;\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        options: this.options,\n        store: this.store,\n        language: this.language,\n        languages: this.languages\n      };\n    }\n  }]);\n\n  return I18n;\n}(EventEmitter);\n\nvar i18next = new I18n();\n\nexport default i18next;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAE3D,IAAIC,aAAa,GAAG;EAClBC,IAAI,EAAE,QAAQ;EACdC,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;IACtB,IAAI,CAACC,MAAM,CAAC,KAAK,EAAED,IAAI,CAAC;EAC1B,CAAC;EACDE,IAAI,EAAE,SAASA,IAAIA,CAACF,IAAI,EAAE;IACxB,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,IAAI,CAAC;EAC3B,CAAC;EACDG,KAAK,EAAE,SAASA,KAAKA,CAACH,IAAI,EAAE;IAC1B,IAAI,CAACC,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;EAC5B,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAACH,IAAI,EAAEE,IAAI,EAAE;IAClC,IAAII,OAAO,IAAIA,OAAO,CAACN,IAAI,CAAC,EAAEM,OAAO,CAACN,IAAI,CAAC,CAACO,KAAK,CAACD,OAAO,EAAEJ,IAAI,CAAC;EAClE;AACF,CAAC;AAED,IAAIM,MAAM,GAAG,YAAY;EACvB,SAASA,MAAMA,CAACC,cAAc,EAAE;IAC9B,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpFlB,eAAe,CAAC,IAAI,EAAEe,MAAM,CAAC;IAE7B,IAAI,CAACM,IAAI,CAACL,cAAc,EAAEC,OAAO,CAAC;EACpC;EAEAhB,YAAY,CAACc,MAAM,EAAE,CAAC;IACpBO,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASF,IAAIA,CAACL,cAAc,EAAE;MACnC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAI,CAACM,MAAM,GAAGP,OAAO,CAACO,MAAM,IAAI,UAAU;MAC1C,IAAI,CAACC,MAAM,GAAGT,cAAc,IAAIV,aAAa;MAC7C,IAAI,CAACW,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACS,KAAK,GAAGT,OAAO,CAACS,KAAK;IAC5B;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASI,QAAQA,CAACC,IAAI,EAAE;MAC7B,IAAI,CAACF,KAAK,GAAGE,IAAI;IACnB;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASf,GAAGA,CAAA,EAAG;MACpB,KAAK,IAAIqB,IAAI,GAAGX,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACD,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;QACvFtB,IAAI,CAACsB,IAAI,CAAC,GAAGb,SAAS,CAACa,IAAI,CAAC;MAC9B;MAEA,OAAO,IAAI,CAACC,OAAO,CAACvB,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IAC5C;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASZ,IAAIA,CAAA,EAAG;MACrB,KAAK,IAAIsB,KAAK,GAAGf,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACG,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FzB,IAAI,CAACyB,KAAK,CAAC,GAAGhB,SAAS,CAACgB,KAAK,CAAC;MAChC;MAEA,OAAO,IAAI,CAACF,OAAO,CAACvB,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;IAC7C;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASX,KAAKA,CAAA,EAAG;MACtB,KAAK,IAAIuB,KAAK,GAAGjB,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACK,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7F3B,IAAI,CAAC2B,KAAK,CAAC,GAAGlB,SAAS,CAACkB,KAAK,CAAC;MAChC;MAEA,OAAO,IAAI,CAACJ,OAAO,CAACvB,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC;IACxC;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASc,SAASA,CAAA,EAAG;MAC1B,KAAK,IAAIC,KAAK,GAAGpB,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACQ,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7F9B,IAAI,CAAC8B,KAAK,CAAC,GAAGrB,SAAS,CAACqB,KAAK,CAAC;MAChC;MAEA,OAAO,IAAI,CAACP,OAAO,CAACvB,IAAI,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,CAAC;IACjE;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASS,OAAOA,CAACvB,IAAI,EAAE+B,GAAG,EAAEhB,MAAM,EAAEiB,SAAS,EAAE;MACpD,IAAIA,SAAS,IAAI,CAAC,IAAI,CAACf,KAAK,EAAE,OAAO,IAAI;MACzC,IAAI,OAAOjB,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAACiC,MAAM,CAAClB,MAAM,CAAC,CAACkB,MAAM,CAAC,IAAI,CAAClB,MAAM,EAAE,GAAG,CAAC,CAACkB,MAAM,CAACjC,IAAI,CAAC,CAAC,CAAC,CAAC;MACrG,OAAO,IAAI,CAACgB,MAAM,CAACe,GAAG,CAAC,CAAC/B,IAAI,CAAC;IAC/B;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASoB,MAAMA,CAACC,UAAU,EAAE;MACjC,OAAO,IAAI7B,MAAM,CAAC,IAAI,CAACU,MAAM,EAAE1B,aAAa,CAAC,CAAC,CAAC,EAAE;QAC/CyB,MAAM,EAAE,EAAE,CAACkB,MAAM,CAAC,IAAI,CAAClB,MAAM,EAAE,GAAG,CAAC,CAACkB,MAAM,CAACE,UAAU,EAAE,GAAG;MAC5D,CAAC,EAAE,IAAI,CAAC3B,OAAO,CAAC,CAAC;IACnB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOF,MAAM;AACf,CAAC,CAAC,CAAC;AAEH,IAAI8B,UAAU,GAAG,IAAI9B,MAAM,CAAC,CAAC;AAE7B,IAAI+B,YAAY,GAAG,YAAY;EAC7B,SAASA,YAAYA,CAAA,EAAG;IACtB9C,eAAe,CAAC,IAAI,EAAE8C,YAAY,CAAC;IAEnC,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACrB;EAEA9C,YAAY,CAAC6C,YAAY,EAAE,CAAC;IAC1BxB,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,SAASyB,EAAEA,CAACC,MAAM,EAAEC,QAAQ,EAAE;MACnC,IAAIC,KAAK,GAAG,IAAI;MAEhBF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;QACzCH,KAAK,CAACJ,SAAS,CAACO,KAAK,CAAC,GAAGH,KAAK,CAACJ,SAAS,CAACO,KAAK,CAAC,IAAI,EAAE;QAErDH,KAAK,CAACJ,SAAS,CAACO,KAAK,CAAC,CAACC,IAAI,CAACL,QAAQ,CAAC;MACvC,CAAC,CAAC;MACF,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASiC,GAAGA,CAACF,KAAK,EAAEJ,QAAQ,EAAE;MACnC,IAAI,CAAC,IAAI,CAACH,SAAS,CAACO,KAAK,CAAC,EAAE;MAE5B,IAAI,CAACJ,QAAQ,EAAE;QACb,OAAO,IAAI,CAACH,SAAS,CAACO,KAAK,CAAC;QAC5B;MACF;MAEA,IAAI,CAACP,SAAS,CAACO,KAAK,CAAC,GAAG,IAAI,CAACP,SAAS,CAACO,KAAK,CAAC,CAACG,MAAM,CAAC,UAAUC,CAAC,EAAE;QAChE,OAAOA,CAAC,KAAKR,QAAQ;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASoC,IAAIA,CAACL,KAAK,EAAE;MAC1B,KAAK,IAAIzB,IAAI,GAAGX,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;QAC1GtB,IAAI,CAACsB,IAAI,GAAG,CAAC,CAAC,GAAGb,SAAS,CAACa,IAAI,CAAC;MAClC;MAEA,IAAI,IAAI,CAACgB,SAAS,CAACO,KAAK,CAAC,EAAE;QACzB,IAAIM,MAAM,GAAG,EAAE,CAAClB,MAAM,CAAC,IAAI,CAACK,SAAS,CAACO,KAAK,CAAC,CAAC;QAC7CM,MAAM,CAACP,OAAO,CAAC,UAAUQ,QAAQ,EAAE;UACjCA,QAAQ,CAAC/C,KAAK,CAAC,KAAK,CAAC,EAAEL,IAAI,CAAC;QAC9B,CAAC,CAAC;MACJ;MAEA,IAAI,IAAI,CAACsC,SAAS,CAAC,GAAG,CAAC,EAAE;QACvB,IAAIe,OAAO,GAAG,EAAE,CAACpB,MAAM,CAAC,IAAI,CAACK,SAAS,CAAC,GAAG,CAAC,CAAC;QAE5Ce,OAAO,CAACT,OAAO,CAAC,UAAUQ,QAAQ,EAAE;UAClCA,QAAQ,CAAC/C,KAAK,CAAC+C,QAAQ,EAAE,CAACP,KAAK,CAAC,CAACZ,MAAM,CAACjC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC,CAAC;EAEH,OAAOqC,YAAY;AACrB,CAAC,CAAC,CAAC;AAEH,SAASiB,KAAKA,CAAA,EAAG;EACf,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IACnDL,GAAG,GAAGI,OAAO;IACbH,GAAG,GAAGI,MAAM;EACd,CAAC,CAAC;EACFH,OAAO,CAACE,OAAO,GAAGJ,GAAG;EACrBE,OAAO,CAACG,MAAM,GAAGJ,GAAG;EACpB,OAAOC,OAAO;AAChB;AACA,SAASI,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAIA,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE;EAC7B,OAAO,EAAE,GAAGA,MAAM;AACpB;AACA,SAASC,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrBF,CAAC,CAACpB,OAAO,CAAC,UAAUuB,CAAC,EAAE;IACrB,IAAIF,CAAC,CAACE,CAAC,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC;EACvB,CAAC,CAAC;AACJ;AAEA,SAASC,aAAaA,CAACN,MAAM,EAAEO,IAAI,EAAEC,KAAK,EAAE;EAC1C,SAASC,QAAQA,CAAC1D,GAAG,EAAE;IACrB,OAAOA,GAAG,IAAIA,GAAG,CAAC2D,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG3D,GAAG,CAAC4D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG5D,GAAG;EACxE;EAEA,SAAS6D,oBAAoBA,CAAA,EAAG;IAC9B,OAAO,CAACZ,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ;EAC9C;EAEA,IAAIa,KAAK,GAAG,OAAON,IAAI,KAAK,QAAQ,GAAG,EAAE,CAACpC,MAAM,CAACoC,IAAI,CAAC,GAAGA,IAAI,CAAC1B,KAAK,CAAC,GAAG,CAAC;EAExE,OAAOgC,KAAK,CAACjE,MAAM,GAAG,CAAC,EAAE;IACvB,IAAIgE,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACrC,IAAI7D,GAAG,GAAG0D,QAAQ,CAACI,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;IACjC,IAAI,CAACd,MAAM,CAACjD,GAAG,CAAC,IAAIyD,KAAK,EAAER,MAAM,CAACjD,GAAG,CAAC,GAAG,IAAIyD,KAAK,CAAC,CAAC;IAEpD,IAAIO,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAClB,MAAM,EAAEjD,GAAG,CAAC,EAAE;MACrDiD,MAAM,GAAGA,MAAM,CAACjD,GAAG,CAAC;IACtB,CAAC,MAAM;MACLiD,MAAM,GAAG,CAAC,CAAC;IACb;EACF;EAEA,IAAIY,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACrC,OAAO;IACLO,GAAG,EAAEnB,MAAM;IACXoB,CAAC,EAAEX,QAAQ,CAACI,KAAK,CAACC,KAAK,CAAC,CAAC;EAC3B,CAAC;AACH;AAEA,SAASO,OAAOA,CAACrB,MAAM,EAAEO,IAAI,EAAEe,QAAQ,EAAE;EACvC,IAAIC,cAAc,GAAGjB,aAAa,CAACN,MAAM,EAAEO,IAAI,EAAEQ,MAAM,CAAC;IACpDI,GAAG,GAAGI,cAAc,CAACJ,GAAG;IACxBC,CAAC,GAAGG,cAAc,CAACH,CAAC;EAExBD,GAAG,CAACC,CAAC,CAAC,GAAGE,QAAQ;AACnB;AACA,SAASE,QAAQA,CAACxB,MAAM,EAAEO,IAAI,EAAEe,QAAQ,EAAEnD,MAAM,EAAE;EAChD,IAAIsD,eAAe,GAAGnB,aAAa,CAACN,MAAM,EAAEO,IAAI,EAAEQ,MAAM,CAAC;IACrDI,GAAG,GAAGM,eAAe,CAACN,GAAG;IACzBC,CAAC,GAAGK,eAAe,CAACL,CAAC;EAEzBD,GAAG,CAACC,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC,IAAI,EAAE;EACrB,IAAIjD,MAAM,EAAEgD,GAAG,CAACC,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC,CAACjD,MAAM,CAACmD,QAAQ,CAAC;EAC5C,IAAI,CAACnD,MAAM,EAAEgD,GAAG,CAACC,CAAC,CAAC,CAACpC,IAAI,CAACsC,QAAQ,CAAC;AACpC;AACA,SAASI,OAAOA,CAAC1B,MAAM,EAAEO,IAAI,EAAE;EAC7B,IAAIoB,eAAe,GAAGrB,aAAa,CAACN,MAAM,EAAEO,IAAI,CAAC;IAC7CY,GAAG,GAAGQ,eAAe,CAACR,GAAG;IACzBC,CAAC,GAAGO,eAAe,CAACP,CAAC;EAEzB,IAAI,CAACD,GAAG,EAAE,OAAOtE,SAAS;EAC1B,OAAOsE,GAAG,CAACC,CAAC,CAAC;AACf;AACA,SAASQ,mBAAmBA,CAACC,IAAI,EAAEC,WAAW,EAAE/E,GAAG,EAAE;EACnD,IAAIC,KAAK,GAAG0E,OAAO,CAACG,IAAI,EAAE9E,GAAG,CAAC;EAE9B,IAAIC,KAAK,KAAKH,SAAS,EAAE;IACvB,OAAOG,KAAK;EACd;EAEA,OAAO0E,OAAO,CAACI,WAAW,EAAE/E,GAAG,CAAC;AAClC;AACA,SAASgF,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAE;EAC7C,KAAK,IAAIC,IAAI,IAAIF,MAAM,EAAE;IACvB,IAAIE,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,aAAa,EAAE;MAClD,IAAIA,IAAI,IAAIH,MAAM,EAAE;QAClB,IAAI,OAAOA,MAAM,CAACG,IAAI,CAAC,KAAK,QAAQ,IAAIH,MAAM,CAACG,IAAI,CAAC,YAAYC,MAAM,IAAI,OAAOH,MAAM,CAACE,IAAI,CAAC,KAAK,QAAQ,IAAIF,MAAM,CAACE,IAAI,CAAC,YAAYC,MAAM,EAAE;UAC5I,IAAIF,SAAS,EAAEF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;QAC5C,CAAC,MAAM;UACLJ,UAAU,CAACC,MAAM,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACE,IAAI,CAAC,EAAED,SAAS,CAAC;QACnD;MACF,CAAC,MAAM;QACLF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;MAC7B;IACF;EACF;EAEA,OAAOH,MAAM;AACf;AACA,SAASK,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,CAAC3B,OAAO,CAAC,qCAAqC,EAAE,MAAM,CAAC;AACnE;AACA,IAAI4B,UAAU,GAAG;EACf,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE;AACP,CAAC;AACD,SAASC,MAAMA,CAACX,IAAI,EAAE;EACpB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOA,IAAI,CAAClB,OAAO,CAAC,YAAY,EAAE,UAAUR,CAAC,EAAE;MAC7C,OAAOoC,UAAU,CAACpC,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ;EAEA,OAAO0B,IAAI;AACb;AACA,IAAIY,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACC,SAAS,IAAIF,MAAM,CAACC,SAAS,CAACC,SAAS,CAAClC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAE/I,SAASmC,QAAQA,CAAC1B,GAAG,EAAEZ,IAAI,EAAE;EAC3B,IAAIuC,YAAY,GAAGnG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EAC1F,IAAI,CAACwE,GAAG,EAAE,OAAOtE,SAAS;EAC1B,IAAIsE,GAAG,CAACZ,IAAI,CAAC,EAAE,OAAOY,GAAG,CAACZ,IAAI,CAAC;EAC/B,IAAIwC,KAAK,GAAGxC,IAAI,CAAC1B,KAAK,CAACiE,YAAY,CAAC;EACpC,IAAIE,OAAO,GAAG7B,GAAG;EAEjB,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACnG,MAAM,EAAE,EAAEqG,CAAC,EAAE;IACrC,IAAI,CAACD,OAAO,EAAE,OAAOnG,SAAS;IAE9B,IAAI,OAAOmG,OAAO,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,GAAGF,KAAK,CAACnG,MAAM,EAAE;MACjE,OAAOC,SAAS;IAClB;IAEA,IAAImG,OAAO,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,KAAKpG,SAAS,EAAE;MACnC,IAAIqG,CAAC,GAAG,CAAC;MACT,IAAIC,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGC,CAAC,CAAC,CAACG,IAAI,CAACP,YAAY,CAAC;MAChD,IAAIQ,GAAG,GAAGN,OAAO,CAACG,CAAC,CAAC;MAEpB,OAAOG,GAAG,KAAKzG,SAAS,IAAIkG,KAAK,CAACnG,MAAM,GAAGqG,CAAC,GAAGC,CAAC,EAAE;QAChDA,CAAC,EAAE;QACHC,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGC,CAAC,CAAC,CAACG,IAAI,CAACP,YAAY,CAAC;QAC5CQ,GAAG,GAAGN,OAAO,CAACG,CAAC,CAAC;MAClB;MAEA,IAAIG,GAAG,KAAKzG,SAAS,EAAE,OAAOA,SAAS;MACvC,IAAI,OAAOyG,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;MACvC,IAAIH,CAAC,IAAI,OAAOG,GAAG,CAACH,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAOG,GAAG,CAACH,CAAC,CAAC;MAClD,IAAII,UAAU,GAAGR,KAAK,CAACK,KAAK,CAACH,CAAC,GAAGC,CAAC,CAAC,CAACG,IAAI,CAACP,YAAY,CAAC;MACtD,IAAIS,UAAU,EAAE,OAAOV,QAAQ,CAACS,GAAG,EAAEC,UAAU,EAAET,YAAY,CAAC;MAC9D,OAAOjG,SAAS;IAClB;IAEAmG,OAAO,GAAGA,OAAO,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC;EAC7B;EAEA,OAAOD,OAAO;AAChB;AAEA,IAAIQ,aAAa,GAAG,UAAUC,aAAa,EAAE;EAC3C3H,SAAS,CAAC0H,aAAa,EAAEC,aAAa,CAAC;EAEvC,SAASD,aAAaA,CAAC3B,IAAI,EAAE;IAC3B,IAAIjD,KAAK;IAET,IAAIlC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;MAChF+G,EAAE,EAAE,CAAC,aAAa,CAAC;MACnBC,SAAS,EAAE;IACb,CAAC;IAEDlI,eAAe,CAAC,IAAI,EAAE+H,aAAa,CAAC;IAEpC5E,KAAK,GAAGjD,0BAA0B,CAAC,IAAI,EAAEC,eAAe,CAAC4H,aAAa,CAAC,CAACtC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnF,IAAIuB,MAAM,EAAE;MACVlE,YAAY,CAAC2C,IAAI,CAACrF,sBAAsB,CAAC+C,KAAK,CAAC,CAAC;IAClD;IAEAA,KAAK,CAACiD,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACvBjD,KAAK,CAAClC,OAAO,GAAGA,OAAO;IAEvB,IAAIkC,KAAK,CAAClC,OAAO,CAACoG,YAAY,KAAKjG,SAAS,EAAE;MAC5C+B,KAAK,CAAClC,OAAO,CAACoG,YAAY,GAAG,GAAG;IAClC;IAEA,IAAIlE,KAAK,CAAClC,OAAO,CAACkH,mBAAmB,KAAK/G,SAAS,EAAE;MACnD+B,KAAK,CAAClC,OAAO,CAACkH,mBAAmB,GAAG,IAAI;IAC1C;IAEA,OAAOhF,KAAK;EACd;EAEAlD,YAAY,CAAC8H,aAAa,EAAE,CAAC;IAC3BzG,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAAS6G,aAAaA,CAACH,EAAE,EAAE;MAChC,IAAI,IAAI,CAAChH,OAAO,CAACgH,EAAE,CAAChD,OAAO,CAACgD,EAAE,CAAC,GAAG,CAAC,EAAE;QACnC,IAAI,CAAChH,OAAO,CAACgH,EAAE,CAAC1E,IAAI,CAAC0E,EAAE,CAAC;MAC1B;IACF;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,SAAS8G,gBAAgBA,CAACJ,EAAE,EAAE;MACnC,IAAIK,KAAK,GAAG,IAAI,CAACrH,OAAO,CAACgH,EAAE,CAAChD,OAAO,CAACgD,EAAE,CAAC;MAEvC,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACrH,OAAO,CAACgH,EAAE,CAACM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAClC;IACF;EACF,CAAC,EAAE;IACDhH,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASiH,WAAWA,CAACC,GAAG,EAAER,EAAE,EAAE3G,GAAG,EAAE;MACxC,IAAIL,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAImG,YAAY,GAAGpG,OAAO,CAACoG,YAAY,KAAKjG,SAAS,GAAGH,OAAO,CAACoG,YAAY,GAAG,IAAI,CAACpG,OAAO,CAACoG,YAAY;MACxG,IAAIc,mBAAmB,GAAGlH,OAAO,CAACkH,mBAAmB,KAAK/G,SAAS,GAAGH,OAAO,CAACkH,mBAAmB,GAAG,IAAI,CAAClH,OAAO,CAACkH,mBAAmB;MACpI,IAAIrD,IAAI,GAAG,CAAC2D,GAAG,EAAER,EAAE,CAAC;MACpB,IAAI3G,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAEwD,IAAI,GAAGA,IAAI,CAACpC,MAAM,CAACpB,GAAG,CAAC;MAC3D,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAEwD,IAAI,GAAGA,IAAI,CAACpC,MAAM,CAAC2E,YAAY,GAAG/F,GAAG,CAAC8B,KAAK,CAACiE,YAAY,CAAC,GAAG/F,GAAG,CAAC;MAEpG,IAAImH,GAAG,CAACxD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACzBH,IAAI,GAAG2D,GAAG,CAACrF,KAAK,CAAC,GAAG,CAAC;MACvB;MAEA,IAAIsF,MAAM,GAAGzC,OAAO,CAAC,IAAI,CAACG,IAAI,EAAEtB,IAAI,CAAC;MACrC,IAAI4D,MAAM,IAAI,CAACP,mBAAmB,IAAI,OAAO7G,GAAG,KAAK,QAAQ,EAAE,OAAOoH,MAAM;MAC5E,OAAOtB,QAAQ,CAAC,IAAI,CAAChB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACqC,GAAG,CAAC,IAAI,IAAI,CAACrC,IAAI,CAACqC,GAAG,CAAC,CAACR,EAAE,CAAC,EAAE3G,GAAG,EAAE+F,YAAY,CAAC;IACvF;EACF,CAAC,EAAE;IACD/F,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASoH,WAAWA,CAACF,GAAG,EAAER,EAAE,EAAE3G,GAAG,EAAEC,KAAK,EAAE;MAC/C,IAAIN,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;QAChF0H,MAAM,EAAE;MACV,CAAC;MACD,IAAIvB,YAAY,GAAG,IAAI,CAACpG,OAAO,CAACoG,YAAY;MAC5C,IAAIA,YAAY,KAAKjG,SAAS,EAAEiG,YAAY,GAAG,GAAG;MAClD,IAAIvC,IAAI,GAAG,CAAC2D,GAAG,EAAER,EAAE,CAAC;MACpB,IAAI3G,GAAG,EAAEwD,IAAI,GAAGA,IAAI,CAACpC,MAAM,CAAC2E,YAAY,GAAG/F,GAAG,CAAC8B,KAAK,CAACiE,YAAY,CAAC,GAAG/F,GAAG,CAAC;MAEzE,IAAImH,GAAG,CAACxD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACzBH,IAAI,GAAG2D,GAAG,CAACrF,KAAK,CAAC,GAAG,CAAC;QACrB7B,KAAK,GAAG0G,EAAE;QACVA,EAAE,GAAGnD,IAAI,CAAC,CAAC,CAAC;MACd;MAEA,IAAI,CAACsD,aAAa,CAACH,EAAE,CAAC;MACtBrC,OAAO,CAAC,IAAI,CAACQ,IAAI,EAAEtB,IAAI,EAAEvD,KAAK,CAAC;MAC/B,IAAI,CAACN,OAAO,CAAC2H,MAAM,EAAE,IAAI,CAACjF,IAAI,CAAC,OAAO,EAAE8E,GAAG,EAAER,EAAE,EAAE3G,GAAG,EAAEC,KAAK,CAAC;IAC9D;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASsH,YAAYA,CAACJ,GAAG,EAAER,EAAE,EAAEa,SAAS,EAAE;MAC/C,IAAI7H,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;QAChF0H,MAAM,EAAE;MACV,CAAC;MAED,KAAK,IAAIhE,CAAC,IAAIkE,SAAS,EAAE;QACvB,IAAI,OAAOA,SAAS,CAAClE,CAAC,CAAC,KAAK,QAAQ,IAAIU,MAAM,CAACC,SAAS,CAACwD,QAAQ,CAACjI,KAAK,CAACgI,SAAS,CAAClE,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE,IAAI,CAAC+D,WAAW,CAACF,GAAG,EAAER,EAAE,EAAErD,CAAC,EAAEkE,SAAS,CAAClE,CAAC,CAAC,EAAE;UACrJgE,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC3H,OAAO,CAAC2H,MAAM,EAAE,IAAI,CAACjF,IAAI,CAAC,OAAO,EAAE8E,GAAG,EAAER,EAAE,EAAEa,SAAS,CAAC;IAC7D;EACF,CAAC,EAAE;IACDxH,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASyH,iBAAiBA,CAACP,GAAG,EAAER,EAAE,EAAEa,SAAS,EAAEG,IAAI,EAAExC,SAAS,EAAE;MACrE,IAAIxF,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;QAChF0H,MAAM,EAAE;MACV,CAAC;MACD,IAAI9D,IAAI,GAAG,CAAC2D,GAAG,EAAER,EAAE,CAAC;MAEpB,IAAIQ,GAAG,CAACxD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACzBH,IAAI,GAAG2D,GAAG,CAACrF,KAAK,CAAC,GAAG,CAAC;QACrB6F,IAAI,GAAGH,SAAS;QAChBA,SAAS,GAAGb,EAAE;QACdA,EAAE,GAAGnD,IAAI,CAAC,CAAC,CAAC;MACd;MAEA,IAAI,CAACsD,aAAa,CAACH,EAAE,CAAC;MACtB,IAAIiB,IAAI,GAAGjD,OAAO,CAAC,IAAI,CAACG,IAAI,EAAEtB,IAAI,CAAC,IAAI,CAAC,CAAC;MAEzC,IAAImE,IAAI,EAAE;QACR3C,UAAU,CAAC4C,IAAI,EAAEJ,SAAS,EAAErC,SAAS,CAAC;MACxC,CAAC,MAAM;QACLyC,IAAI,GAAGnJ,aAAa,CAAC,CAAC,CAAC,EAAEmJ,IAAI,EAAEJ,SAAS,CAAC;MAC3C;MAEAlD,OAAO,CAAC,IAAI,CAACQ,IAAI,EAAEtB,IAAI,EAAEoE,IAAI,CAAC;MAC9B,IAAI,CAACjI,OAAO,CAAC2H,MAAM,EAAE,IAAI,CAACjF,IAAI,CAAC,OAAO,EAAE8E,GAAG,EAAER,EAAE,EAAEa,SAAS,CAAC;IAC7D;EACF,CAAC,EAAE;IACDxH,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAAS4H,oBAAoBA,CAACV,GAAG,EAAER,EAAE,EAAE;MAC5C,IAAI,IAAI,CAACmB,iBAAiB,CAACX,GAAG,EAAER,EAAE,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC7B,IAAI,CAACqC,GAAG,CAAC,CAACR,EAAE,CAAC;MAC3B;MAEA,IAAI,CAACI,gBAAgB,CAACJ,EAAE,CAAC;MACzB,IAAI,CAACtE,IAAI,CAAC,SAAS,EAAE8E,GAAG,EAAER,EAAE,CAAC;IAC/B;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAAS6H,iBAAiBA,CAACX,GAAG,EAAER,EAAE,EAAE;MACzC,OAAO,IAAI,CAACO,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC,KAAK7G,SAAS;IAChD;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAAS8H,iBAAiBA,CAACZ,GAAG,EAAER,EAAE,EAAE;MACzC,IAAI,CAACA,EAAE,EAAEA,EAAE,GAAG,IAAI,CAAChH,OAAO,CAACiH,SAAS;MACpC,IAAI,IAAI,CAACjH,OAAO,CAACqI,gBAAgB,KAAK,IAAI,EAAE,OAAOvJ,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACyI,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC,CAAC;MACnG,OAAO,IAAI,CAACO,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC;IAClC;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASgI,iBAAiBA,CAACd,GAAG,EAAE;MACrC,OAAO,IAAI,CAACrC,IAAI,CAACqC,GAAG,CAAC;IACvB;EACF,CAAC,EAAE;IACDnH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASiI,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACpD,IAAI;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAO2B,aAAa;AACtB,CAAC,CAACjF,YAAY,CAAC;AAEf,IAAI2G,aAAa,GAAG;EAClBC,UAAU,EAAE,CAAC,CAAC;EACdC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,MAAM,EAAE;IAClD,IAAI,CAACF,UAAU,CAACE,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM;EACvC,CAAC;EACDE,MAAM,EAAE,SAASA,MAAMA,CAACJ,UAAU,EAAEnI,KAAK,EAAED,GAAG,EAAEL,OAAO,EAAE8I,UAAU,EAAE;IACnE,IAAI5G,KAAK,GAAG,IAAI;IAEhBuG,UAAU,CAACrG,OAAO,CAAC,UAAU2G,SAAS,EAAE;MACtC,IAAI7G,KAAK,CAACuG,UAAU,CAACM,SAAS,CAAC,EAAEzI,KAAK,GAAG4B,KAAK,CAACuG,UAAU,CAACM,SAAS,CAAC,CAACC,OAAO,CAAC1I,KAAK,EAAED,GAAG,EAAEL,OAAO,EAAE8I,UAAU,CAAC;IAC/G,CAAC,CAAC;IACF,OAAOxI,KAAK;EACd;AACF,CAAC;AAED,IAAI2I,gBAAgB,GAAG,CAAC,CAAC;AAEzB,IAAIC,UAAU,GAAG,UAAUnC,aAAa,EAAE;EACxC3H,SAAS,CAAC8J,UAAU,EAAEnC,aAAa,CAAC;EAEpC,SAASmC,UAAUA,CAACC,QAAQ,EAAE;IAC5B,IAAIjH,KAAK;IAET,IAAIlC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpFlB,eAAe,CAAC,IAAI,EAAEmK,UAAU,CAAC;IAEjChH,KAAK,GAAGjD,0BAA0B,CAAC,IAAI,EAAEC,eAAe,CAACgK,UAAU,CAAC,CAAC1E,IAAI,CAAC,IAAI,CAAC,CAAC;IAEhF,IAAIuB,MAAM,EAAE;MACVlE,YAAY,CAAC2C,IAAI,CAACrF,sBAAsB,CAAC+C,KAAK,CAAC,CAAC;IAClD;IAEAqB,IAAI,CAAC,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE4F,QAAQ,EAAEhK,sBAAsB,CAAC+C,KAAK,CAAC,CAAC;IAC9JA,KAAK,CAAClC,OAAO,GAAGA,OAAO;IAEvB,IAAIkC,KAAK,CAAClC,OAAO,CAACoG,YAAY,KAAKjG,SAAS,EAAE;MAC5C+B,KAAK,CAAClC,OAAO,CAACoG,YAAY,GAAG,GAAG;IAClC;IAEAlE,KAAK,CAAC1B,MAAM,GAAGoB,UAAU,CAACF,MAAM,CAAC,YAAY,CAAC;IAC9C,OAAOQ,KAAK;EACd;EAEAlD,YAAY,CAACkK,UAAU,EAAE,CAAC;IACxB7I,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAAS8I,cAAcA,CAAC5B,GAAG,EAAE;MAClC,IAAIA,GAAG,EAAE,IAAI,CAAC6B,QAAQ,GAAG7B,GAAG;IAC9B;EACF,CAAC,EAAE;IACDnH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASgJ,MAAMA,CAACjJ,GAAG,EAAE;MAC1B,IAAIL,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;QAChFsJ,aAAa,EAAE,CAAC;MAClB,CAAC;MAED,IAAIlJ,GAAG,KAAKF,SAAS,IAAIE,GAAG,KAAK,IAAI,EAAE;QACrC,OAAO,KAAK;MACd;MAEA,IAAImJ,QAAQ,GAAG,IAAI,CAACrG,OAAO,CAAC9C,GAAG,EAAEL,OAAO,CAAC;MACzC,OAAOwJ,QAAQ,IAAIA,QAAQ,CAACzG,GAAG,KAAK5C,SAAS;IAC/C;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAASmJ,cAAcA,CAACpJ,GAAG,EAAEL,OAAO,EAAE;MAC3C,IAAI0J,WAAW,GAAG1J,OAAO,CAAC0J,WAAW,KAAKvJ,SAAS,GAAGH,OAAO,CAAC0J,WAAW,GAAG,IAAI,CAAC1J,OAAO,CAAC0J,WAAW;MACpG,IAAIA,WAAW,KAAKvJ,SAAS,EAAEuJ,WAAW,GAAG,GAAG;MAChD,IAAItD,YAAY,GAAGpG,OAAO,CAACoG,YAAY,KAAKjG,SAAS,GAAGH,OAAO,CAACoG,YAAY,GAAG,IAAI,CAACpG,OAAO,CAACoG,YAAY;MACxG,IAAIuD,UAAU,GAAG3J,OAAO,CAACgH,EAAE,IAAI,IAAI,CAAChH,OAAO,CAACiH,SAAS;MAErD,IAAIyC,WAAW,IAAIrJ,GAAG,CAAC2D,OAAO,CAAC0F,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;QAChD,IAAI/F,CAAC,GAAGtD,GAAG,CAACuJ,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QAElD,IAAInG,CAAC,IAAIA,CAAC,CAACzD,MAAM,GAAG,CAAC,EAAE;UACrB,OAAO;YACLG,GAAG,EAAEA,GAAG;YACRsJ,UAAU,EAAEA;UACd,CAAC;QACH;QAEA,IAAII,KAAK,GAAG1J,GAAG,CAAC8B,KAAK,CAACuH,WAAW,CAAC;QAClC,IAAIA,WAAW,KAAKtD,YAAY,IAAIsD,WAAW,KAAKtD,YAAY,IAAI,IAAI,CAACpG,OAAO,CAACgH,EAAE,CAAChD,OAAO,CAAC+F,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEJ,UAAU,GAAGI,KAAK,CAAC3F,KAAK,CAAC,CAAC;QACtI/D,GAAG,GAAG0J,KAAK,CAACpD,IAAI,CAACP,YAAY,CAAC;MAChC;MAEA,IAAI,OAAOuD,UAAU,KAAK,QAAQ,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAC7D,OAAO;QACLtJ,GAAG,EAAEA,GAAG;QACRsJ,UAAU,EAAEA;MACd,CAAC;IACH;EACF,CAAC,EAAE;IACDtJ,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAAS0J,SAASA,CAACC,IAAI,EAAEjK,OAAO,EAAEkK,OAAO,EAAE;MAChD,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAItL,OAAO,CAACmB,OAAO,CAAC,KAAK,QAAQ,IAAI,IAAI,CAACA,OAAO,CAACoK,gCAAgC,EAAE;QAClFpK,OAAO,GAAG,IAAI,CAACA,OAAO,CAACoK,gCAAgC,CAACnK,SAAS,CAAC;MACpE;MAEA,IAAI,CAACD,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;MAC1B,IAAIiK,IAAI,KAAK9J,SAAS,IAAI8J,IAAI,KAAK,IAAI,EAAE,OAAO,EAAE;MAClD,IAAI,CAACpJ,KAAK,CAACwJ,OAAO,CAACJ,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACvE,MAAM,CAACuE,IAAI,CAAC,CAAC;MAC/C,IAAI7D,YAAY,GAAGpG,OAAO,CAACoG,YAAY,KAAKjG,SAAS,GAAGH,OAAO,CAACoG,YAAY,GAAG,IAAI,CAACpG,OAAO,CAACoG,YAAY;MAExG,IAAIkE,oBAAoB,GAAG,IAAI,CAACb,cAAc,CAACQ,IAAI,CAACA,IAAI,CAAC/J,MAAM,GAAG,CAAC,CAAC,EAAEF,OAAO,CAAC;QAC1EK,GAAG,GAAGiK,oBAAoB,CAACjK,GAAG;QAC9BsJ,UAAU,GAAGW,oBAAoB,CAACX,UAAU;MAEhD,IAAIY,SAAS,GAAGZ,UAAU,CAACA,UAAU,CAACzJ,MAAM,GAAG,CAAC,CAAC;MACjD,IAAIsH,GAAG,GAAGxH,OAAO,CAACwH,GAAG,IAAI,IAAI,CAAC6B,QAAQ;MACtC,IAAImB,uBAAuB,GAAGxK,OAAO,CAACwK,uBAAuB,IAAI,IAAI,CAACxK,OAAO,CAACwK,uBAAuB;MAErG,IAAIhD,GAAG,IAAIA,GAAG,CAACiD,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;QACzC,IAAID,uBAAuB,EAAE;UAC3B,IAAId,WAAW,GAAG1J,OAAO,CAAC0J,WAAW,IAAI,IAAI,CAAC1J,OAAO,CAAC0J,WAAW;UACjE,OAAOa,SAAS,GAAGb,WAAW,GAAGrJ,GAAG;QACtC;QAEA,OAAOA,GAAG;MACZ;MAEA,IAAImJ,QAAQ,GAAG,IAAI,CAACrG,OAAO,CAAC8G,IAAI,EAAEjK,OAAO,CAAC;MAC1C,IAAI+C,GAAG,GAAGyG,QAAQ,IAAIA,QAAQ,CAACzG,GAAG;MAClC,IAAI2H,UAAU,GAAGlB,QAAQ,IAAIA,QAAQ,CAACmB,OAAO,IAAItK,GAAG;MACpD,IAAIuK,eAAe,GAAGpB,QAAQ,IAAIA,QAAQ,CAACqB,YAAY,IAAIxK,GAAG;MAC9D,IAAIyK,OAAO,GAAGzG,MAAM,CAACC,SAAS,CAACwD,QAAQ,CAACjI,KAAK,CAACkD,GAAG,CAAC;MAClD,IAAIgI,QAAQ,GAAG,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;MAC1E,IAAIC,UAAU,GAAGhL,OAAO,CAACgL,UAAU,KAAK7K,SAAS,GAAGH,OAAO,CAACgL,UAAU,GAAG,IAAI,CAAChL,OAAO,CAACgL,UAAU;MAChG,IAAIC,0BAA0B,GAAG,CAAC,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,cAAc;MACnF,IAAIA,cAAc,GAAG,OAAOpI,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ;MAEnG,IAAIkI,0BAA0B,IAAIlI,GAAG,IAAIoI,cAAc,IAAIJ,QAAQ,CAAC/G,OAAO,CAAC8G,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,OAAOE,UAAU,KAAK,QAAQ,IAAIF,OAAO,KAAK,gBAAgB,CAAC,EAAE;QAC7J,IAAI,CAAC9K,OAAO,CAACoL,aAAa,IAAI,CAAC,IAAI,CAACpL,OAAO,CAACoL,aAAa,EAAE;UACzD,IAAI,CAAC,IAAI,CAACpL,OAAO,CAACqL,qBAAqB,EAAE;YACvC,IAAI,CAAC7K,MAAM,CAACd,IAAI,CAAC,iEAAiE,CAAC;UACrF;UAEA,OAAO,IAAI,CAACM,OAAO,CAACqL,qBAAqB,GAAG,IAAI,CAACrL,OAAO,CAACqL,qBAAqB,CAACX,UAAU,EAAE3H,GAAG,EAAEjE,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAE;YACzHgH,EAAE,EAAE2C;UACN,CAAC,CAAC,CAAC,GAAG,OAAO,CAAClI,MAAM,CAACpB,GAAG,EAAE,IAAI,CAAC,CAACoB,MAAM,CAAC,IAAI,CAAC4H,QAAQ,EAAE,0CAA0C,CAAC;QACnG;QAEA,IAAIjD,YAAY,EAAE;UAChB,IAAIkF,cAAc,GAAGR,OAAO,KAAK,gBAAgB;UACjD,IAAIvH,IAAI,GAAG+H,cAAc,GAAG,EAAE,GAAG,CAAC,CAAC;UACnC,IAAIC,WAAW,GAAGD,cAAc,GAAGV,eAAe,GAAGF,UAAU;UAE/D,KAAK,IAAI/G,CAAC,IAAIZ,GAAG,EAAE;YACjB,IAAIsB,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACzB,GAAG,EAAEY,CAAC,CAAC,EAAE;cAChD,IAAI6H,OAAO,GAAG,EAAE,CAAC/J,MAAM,CAAC8J,WAAW,CAAC,CAAC9J,MAAM,CAAC2E,YAAY,CAAC,CAAC3E,MAAM,CAACkC,CAAC,CAAC;cACnEJ,IAAI,CAACI,CAAC,CAAC,GAAG,IAAI,CAACqG,SAAS,CAACwB,OAAO,EAAE1M,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAE;gBAC3DgL,UAAU,EAAE,KAAK;gBACjBhE,EAAE,EAAE2C;cACN,CAAC,CAAC,CAAC;cACH,IAAIpG,IAAI,CAACI,CAAC,CAAC,KAAK6H,OAAO,EAAEjI,IAAI,CAACI,CAAC,CAAC,GAAGZ,GAAG,CAACY,CAAC,CAAC;YAC3C;UACF;UAEAZ,GAAG,GAAGQ,IAAI;QACZ;MACF,CAAC,MAAM,IAAI0H,0BAA0B,IAAI,OAAOD,UAAU,KAAK,QAAQ,IAAIF,OAAO,KAAK,gBAAgB,EAAE;QACvG/H,GAAG,GAAGA,GAAG,CAAC4D,IAAI,CAACqE,UAAU,CAAC;QAC1B,IAAIjI,GAAG,EAAEA,GAAG,GAAG,IAAI,CAAC0I,iBAAiB,CAAC1I,GAAG,EAAEkH,IAAI,EAAEjK,OAAO,EAAEkK,OAAO,CAAC;MACpE,CAAC,MAAM;QACL,IAAIwB,WAAW,GAAG,KAAK;QACvB,IAAIf,OAAO,GAAG,KAAK;QACnB,IAAIgB,mBAAmB,GAAG3L,OAAO,CAAC4L,KAAK,KAAKzL,SAAS,IAAI,OAAOH,OAAO,CAAC4L,KAAK,KAAK,QAAQ;QAC1F,IAAIC,eAAe,GAAG3C,UAAU,CAAC2C,eAAe,CAAC7L,OAAO,CAAC;QACzD,IAAI8L,kBAAkB,GAAGH,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAACxE,GAAG,EAAExH,OAAO,CAAC4L,KAAK,CAAC,GAAG,EAAE;QACrG,IAAIK,YAAY,GAAGjM,OAAO,CAAC,cAAc,CAACyB,MAAM,CAACqK,kBAAkB,CAAC,CAAC,IAAI9L,OAAO,CAACiM,YAAY;QAE7F,IAAI,CAAC,IAAI,CAACC,aAAa,CAACnJ,GAAG,CAAC,IAAI8I,eAAe,EAAE;UAC/CH,WAAW,GAAG,IAAI;UAClB3I,GAAG,GAAGkJ,YAAY;QACpB;QAEA,IAAI,CAAC,IAAI,CAACC,aAAa,CAACnJ,GAAG,CAAC,EAAE;UAC5B4H,OAAO,GAAG,IAAI;UACd5H,GAAG,GAAG1C,GAAG;QACX;QAEA,IAAI8L,8BAA8B,GAAGnM,OAAO,CAACmM,8BAA8B,IAAI,IAAI,CAACnM,OAAO,CAACmM,8BAA8B;QAC1H,IAAIC,aAAa,GAAGD,8BAA8B,IAAIxB,OAAO,GAAGxK,SAAS,GAAG4C,GAAG;QAC/E,IAAIsJ,aAAa,GAAGR,eAAe,IAAII,YAAY,KAAKlJ,GAAG,IAAI,IAAI,CAAC/C,OAAO,CAACqM,aAAa;QAEzF,IAAI1B,OAAO,IAAIe,WAAW,IAAIW,aAAa,EAAE;UAC3C,IAAI,CAAC7L,MAAM,CAACjB,GAAG,CAAC8M,aAAa,GAAG,WAAW,GAAG,YAAY,EAAE7E,GAAG,EAAE+C,SAAS,EAAElK,GAAG,EAAEgM,aAAa,GAAGJ,YAAY,GAAGlJ,GAAG,CAAC;UAEpH,IAAIqD,YAAY,EAAE;YAChB,IAAIkG,EAAE,GAAG,IAAI,CAACnJ,OAAO,CAAC9C,GAAG,EAAEvB,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAE;cACpDoG,YAAY,EAAE;YAChB,CAAC,CAAC,CAAC;YACH,IAAIkG,EAAE,IAAIA,EAAE,CAACvJ,GAAG,EAAE,IAAI,CAACvC,MAAM,CAACd,IAAI,CAAC,iLAAiL,CAAC;UACvN;UAEA,IAAI6M,IAAI,GAAG,EAAE;UACb,IAAIC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAAC1M,OAAO,CAAC2M,WAAW,EAAE3M,OAAO,CAACwH,GAAG,IAAI,IAAI,CAAC6B,QAAQ,CAAC;UAE9G,IAAI,IAAI,CAACrJ,OAAO,CAAC4M,aAAa,KAAK,UAAU,IAAIJ,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;YAChF,KAAK,IAAIjG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,YAAY,CAACtM,MAAM,EAAEqG,CAAC,EAAE,EAAE;cAC5CgG,IAAI,CAACjK,IAAI,CAACkK,YAAY,CAACjG,CAAC,CAAC,CAAC;YAC5B;UACF,CAAC,MAAM,IAAI,IAAI,CAACvG,OAAO,CAAC4M,aAAa,KAAK,KAAK,EAAE;YAC/CL,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAAC7M,OAAO,CAACwH,GAAG,IAAI,IAAI,CAAC6B,QAAQ,CAAC;UAC5E,CAAC,MAAM;YACLkD,IAAI,CAACjK,IAAI,CAACtC,OAAO,CAACwH,GAAG,IAAI,IAAI,CAAC6B,QAAQ,CAAC;UACzC;UAEA,IAAIyD,IAAI,GAAG,SAASA,IAAIA,CAACrK,CAAC,EAAEiC,CAAC,EAAEqI,aAAa,EAAE;YAC5C,IAAI5C,MAAM,CAACnK,OAAO,CAACgN,iBAAiB,EAAE;cACpC7C,MAAM,CAACnK,OAAO,CAACgN,iBAAiB,CAACvK,CAAC,EAAE8H,SAAS,EAAE7F,CAAC,EAAE2H,aAAa,GAAGU,aAAa,GAAGX,aAAa,EAAEC,aAAa,EAAErM,OAAO,CAAC;YAC1H,CAAC,MAAM,IAAImK,MAAM,CAAC8C,gBAAgB,IAAI9C,MAAM,CAAC8C,gBAAgB,CAACC,WAAW,EAAE;cACzE/C,MAAM,CAAC8C,gBAAgB,CAACC,WAAW,CAACzK,CAAC,EAAE8H,SAAS,EAAE7F,CAAC,EAAE2H,aAAa,GAAGU,aAAa,GAAGX,aAAa,EAAEC,aAAa,EAAErM,OAAO,CAAC;YAC7H;YAEAmK,MAAM,CAACzH,IAAI,CAAC,YAAY,EAAED,CAAC,EAAE8H,SAAS,EAAE7F,CAAC,EAAE3B,GAAG,CAAC;UACjD,CAAC;UAED,IAAI,IAAI,CAAC/C,OAAO,CAACkN,WAAW,EAAE;YAC5B,IAAI,IAAI,CAAClN,OAAO,CAACmN,kBAAkB,IAAIxB,mBAAmB,EAAE;cAC1DY,IAAI,CAACnK,OAAO,CAAC,UAAUiH,QAAQ,EAAE;gBAC/Bc,MAAM,CAAC4B,cAAc,CAACqB,WAAW,CAAC/D,QAAQ,CAAC,CAACjH,OAAO,CAAC,UAAUiL,MAAM,EAAE;kBACpEP,IAAI,CAAC,CAACzD,QAAQ,CAAC,EAAEhJ,GAAG,GAAGgN,MAAM,EAAErN,OAAO,CAAC,cAAc,CAACyB,MAAM,CAAC4L,MAAM,CAAC,CAAC,IAAIpB,YAAY,CAAC;gBACxF,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC,MAAM;cACLa,IAAI,CAACP,IAAI,EAAElM,GAAG,EAAE4L,YAAY,CAAC;YAC/B;UACF;QACF;QAEAlJ,GAAG,GAAG,IAAI,CAAC0I,iBAAiB,CAAC1I,GAAG,EAAEkH,IAAI,EAAEjK,OAAO,EAAEwJ,QAAQ,EAAEU,OAAO,CAAC;QACnE,IAAIS,OAAO,IAAI5H,GAAG,KAAK1C,GAAG,IAAI,IAAI,CAACL,OAAO,CAACsN,2BAA2B,EAAEvK,GAAG,GAAG,EAAE,CAACtB,MAAM,CAAC8I,SAAS,EAAE,GAAG,CAAC,CAAC9I,MAAM,CAACpB,GAAG,CAAC;QACnH,IAAI,CAACsK,OAAO,IAAIe,WAAW,KAAK,IAAI,CAAC1L,OAAO,CAACuN,sBAAsB,EAAExK,GAAG,GAAG,IAAI,CAAC/C,OAAO,CAACuN,sBAAsB,CAACxK,GAAG,CAAC;MACrH;MAEA,OAAOA,GAAG;IACZ;EACF,CAAC,EAAE;IACD1C,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASmL,iBAAiBA,CAAC1I,GAAG,EAAE1C,GAAG,EAAEL,OAAO,EAAEwJ,QAAQ,EAAEU,OAAO,EAAE;MACtE,IAAIsD,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACtC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACuC,KAAK,EAAE;QAC5C1K,GAAG,GAAG,IAAI,CAACmI,UAAU,CAACuC,KAAK,CAAC1K,GAAG,EAAE/C,OAAO,EAAEwJ,QAAQ,CAACkE,OAAO,EAAElE,QAAQ,CAACmE,MAAM,EAAEnE,QAAQ,CAACmB,OAAO,EAAE;UAC7FnB,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAACxJ,OAAO,CAAC4N,iBAAiB,EAAE;QACrC,IAAI5N,OAAO,CAACuJ,aAAa,EAAE,IAAI,CAACM,YAAY,CAACzJ,IAAI,CAACtB,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAE;UAC3EuJ,aAAa,EAAEzK,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkB,OAAO,CAACuJ,aAAa,EAAEvJ,OAAO,CAACuJ,aAAa;QACpF,CAAC,CAAC,CAAC;QACH,IAAIsE,eAAe,GAAG7N,OAAO,CAACuJ,aAAa,IAAIvJ,OAAO,CAACuJ,aAAa,CAACsE,eAAe,IAAI,IAAI,CAAC7N,OAAO,CAACuJ,aAAa,CAACsE,eAAe;QAClI,IAAIC,OAAO;QAEX,IAAID,eAAe,EAAE;UACnB,IAAIE,EAAE,GAAGhL,GAAG,CAAC6G,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;UACnDgE,OAAO,GAAGC,EAAE,IAAIA,EAAE,CAAC7N,MAAM;QAC3B;QAEA,IAAIiF,IAAI,GAAGnF,OAAO,CAACiE,OAAO,IAAI,OAAOjE,OAAO,CAACiE,OAAO,KAAK,QAAQ,GAAGjE,OAAO,CAACiE,OAAO,GAAGjE,OAAO;QAC7F,IAAI,IAAI,CAACA,OAAO,CAACuJ,aAAa,CAACyE,gBAAgB,EAAE7I,IAAI,GAAGrG,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkB,OAAO,CAACuJ,aAAa,CAACyE,gBAAgB,EAAE7I,IAAI,CAAC;QAC5HpC,GAAG,GAAG,IAAI,CAAC8G,YAAY,CAACoE,WAAW,CAAClL,GAAG,EAAEoC,IAAI,EAAEnF,OAAO,CAACwH,GAAG,IAAI,IAAI,CAAC6B,QAAQ,EAAErJ,OAAO,CAAC;QAErF,IAAI6N,eAAe,EAAE;UACnB,IAAIK,EAAE,GAAGnL,GAAG,CAAC6G,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;UACnD,IAAIqE,OAAO,GAAGD,EAAE,IAAIA,EAAE,CAAChO,MAAM;UAC7B,IAAI4N,OAAO,GAAGK,OAAO,EAAEnO,OAAO,CAACoO,IAAI,GAAG,KAAK;QAC7C;QAEA,IAAIpO,OAAO,CAACoO,IAAI,KAAK,KAAK,EAAErL,GAAG,GAAG,IAAI,CAAC8G,YAAY,CAACuE,IAAI,CAACrL,GAAG,EAAE,YAAY;UACxE,KAAK,IAAInC,IAAI,GAAGX,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACD,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;YACvFtB,IAAI,CAACsB,IAAI,CAAC,GAAGb,SAAS,CAACa,IAAI,CAAC;UAC9B;UAEA,IAAIoJ,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK1K,IAAI,CAAC,CAAC,CAAC,IAAI,CAACQ,OAAO,CAACqO,OAAO,EAAE;YACzDb,MAAM,CAAChN,MAAM,CAACd,IAAI,CAAC,4CAA4C,CAAC+B,MAAM,CAACjC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAACiC,MAAM,CAACpB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5G,OAAO,IAAI;UACb;UAEA,OAAOmN,MAAM,CAACxD,SAAS,CAACnK,KAAK,CAAC2N,MAAM,EAAEhO,IAAI,CAACiC,MAAM,CAAC,CAACpB,GAAG,CAAC,CAAC,CAAC;QAC3D,CAAC,EAAEL,OAAO,CAAC;QACX,IAAIA,OAAO,CAACuJ,aAAa,EAAE,IAAI,CAACM,YAAY,CAACyE,KAAK,CAAC,CAAC;MACtD;MAEA,IAAIC,WAAW,GAAGvO,OAAO,CAACuO,WAAW,IAAI,IAAI,CAACvO,OAAO,CAACuO,WAAW;MACjE,IAAIC,kBAAkB,GAAG,OAAOD,WAAW,KAAK,QAAQ,GAAG,CAACA,WAAW,CAAC,GAAGA,WAAW;MAEtF,IAAIxL,GAAG,KAAK5C,SAAS,IAAI4C,GAAG,KAAK,IAAI,IAAIyL,kBAAkB,IAAIA,kBAAkB,CAACtO,MAAM,IAAIF,OAAO,CAACyO,kBAAkB,KAAK,KAAK,EAAE;QAChI1L,GAAG,GAAGyF,aAAa,CAACK,MAAM,CAAC2F,kBAAkB,EAAEzL,GAAG,EAAE1C,GAAG,EAAE,IAAI,CAACL,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC0O,uBAAuB,GAAG5P,aAAa,CAAC;UAC5H6P,YAAY,EAAEnF;QAChB,CAAC,EAAExJ,OAAO,CAAC,GAAGA,OAAO,EAAE,IAAI,CAAC;MAC9B;MAEA,OAAO+C,GAAG;IACZ;EACF,CAAC,EAAE;IACD1C,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAAS6C,OAAOA,CAAC8G,IAAI,EAAE;MAC5B,IAAI2E,MAAM,GAAG,IAAI;MAEjB,IAAI5O,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAI4O,KAAK;MACT,IAAIlE,OAAO;MACX,IAAIE,YAAY;MAChB,IAAI6C,OAAO;MACX,IAAIC,MAAM;MACV,IAAI,OAAO1D,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;MAC3CA,IAAI,CAAC7H,OAAO,CAAC,UAAUsC,CAAC,EAAE;QACxB,IAAIkK,MAAM,CAAC1C,aAAa,CAAC2C,KAAK,CAAC,EAAE;QAEjC,IAAIC,SAAS,GAAGF,MAAM,CAACnF,cAAc,CAAC/E,CAAC,EAAE1E,OAAO,CAAC;QAEjD,IAAIK,GAAG,GAAGyO,SAAS,CAACzO,GAAG;QACvBsK,OAAO,GAAGtK,GAAG;QACb,IAAIsJ,UAAU,GAAGmF,SAAS,CAACnF,UAAU;QACrC,IAAIiF,MAAM,CAAC5O,OAAO,CAAC+O,UAAU,EAAEpF,UAAU,GAAGA,UAAU,CAAClI,MAAM,CAACmN,MAAM,CAAC5O,OAAO,CAAC+O,UAAU,CAAC;QACxF,IAAIpD,mBAAmB,GAAG3L,OAAO,CAAC4L,KAAK,KAAKzL,SAAS,IAAI,OAAOH,OAAO,CAAC4L,KAAK,KAAK,QAAQ;QAC1F,IAAIoD,oBAAoB,GAAGhP,OAAO,CAACqO,OAAO,KAAKlO,SAAS,KAAK,OAAOH,OAAO,CAACqO,OAAO,KAAK,QAAQ,IAAI,OAAOrO,OAAO,CAACqO,OAAO,KAAK,QAAQ,CAAC,IAAIrO,OAAO,CAACqO,OAAO,KAAK,EAAE;QAClK,IAAIY,KAAK,GAAGjP,OAAO,CAACuM,IAAI,GAAGvM,OAAO,CAACuM,IAAI,GAAGqC,MAAM,CAACnC,aAAa,CAACI,kBAAkB,CAAC7M,OAAO,CAACwH,GAAG,IAAIoH,MAAM,CAACvF,QAAQ,EAAErJ,OAAO,CAAC2M,WAAW,CAAC;QACtIhD,UAAU,CAACvH,OAAO,CAAC,UAAU4E,EAAE,EAAE;UAC/B,IAAI4H,MAAM,CAAC1C,aAAa,CAAC2C,KAAK,CAAC,EAAE;UACjClB,MAAM,GAAG3G,EAAE;UAEX,IAAI,CAACiC,gBAAgB,CAAC,EAAE,CAACxH,MAAM,CAACwN,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACxN,MAAM,CAACuF,EAAE,CAAC,CAAC,IAAI4H,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACM,KAAK,CAACC,kBAAkB,IAAI,CAACP,MAAM,CAACM,KAAK,CAACC,kBAAkB,CAACxB,MAAM,CAAC,EAAE;YACzJ1E,gBAAgB,CAAC,EAAE,CAACxH,MAAM,CAACwN,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACxN,MAAM,CAACuF,EAAE,CAAC,CAAC,GAAG,IAAI;YAE5D4H,MAAM,CAACpO,MAAM,CAACd,IAAI,CAAC,QAAQ,CAAC+B,MAAM,CAACkJ,OAAO,EAAE,qBAAqB,CAAC,CAAClJ,MAAM,CAACwN,KAAK,CAACtI,IAAI,CAAC,IAAI,CAAC,EAAE,uCAAuC,CAAC,CAAClF,MAAM,CAACkM,MAAM,EAAE,uBAAuB,CAAC,EAAE,0NAA0N,CAAC;UAC3Y;UAEAsB,KAAK,CAAC7M,OAAO,CAAC,UAAUgN,IAAI,EAAE;YAC5B,IAAIR,MAAM,CAAC1C,aAAa,CAAC2C,KAAK,CAAC,EAAE;YACjCnB,OAAO,GAAG0B,IAAI;YACd,IAAIC,QAAQ,GAAGhP,GAAG;YAClB,IAAIiP,SAAS,GAAG,CAACD,QAAQ,CAAC;YAE1B,IAAIT,MAAM,CAAC1D,UAAU,IAAI0D,MAAM,CAAC1D,UAAU,CAACqE,aAAa,EAAE;cACxDX,MAAM,CAAC1D,UAAU,CAACqE,aAAa,CAACD,SAAS,EAAEjP,GAAG,EAAE+O,IAAI,EAAEpI,EAAE,EAAEhH,OAAO,CAAC;YACpE,CAAC,MAAM;cACL,IAAIwP,YAAY;cAChB,IAAI7D,mBAAmB,EAAE6D,YAAY,GAAGZ,MAAM,CAAC7C,cAAc,CAACC,SAAS,CAACoD,IAAI,EAAEpP,OAAO,CAAC4L,KAAK,CAAC;cAC5F,IAAID,mBAAmB,IAAIqD,oBAAoB,EAAEM,SAAS,CAAChN,IAAI,CAAC+M,QAAQ,GAAGG,YAAY,CAAC;cACxF,IAAIR,oBAAoB,EAAEM,SAAS,CAAChN,IAAI,CAAC+M,QAAQ,IAAI,EAAE,CAAC5N,MAAM,CAACmN,MAAM,CAAC5O,OAAO,CAACyP,gBAAgB,CAAC,CAAChO,MAAM,CAACzB,OAAO,CAACqO,OAAO,CAAC,CAAC;cACxH,IAAI1C,mBAAmB,EAAE2D,SAAS,CAAChN,IAAI,CAAC+M,QAAQ,IAAIG,YAAY,CAAC;YACnE;YAEA,IAAIE,WAAW;YAEf,OAAOA,WAAW,GAAGJ,SAAS,CAACK,GAAG,CAAC,CAAC,EAAE;cACpC,IAAI,CAACf,MAAM,CAAC1C,aAAa,CAAC2C,KAAK,CAAC,EAAE;gBAChChE,YAAY,GAAG6E,WAAW;gBAC1Bb,KAAK,GAAGD,MAAM,CAACrH,WAAW,CAAC6H,IAAI,EAAEpI,EAAE,EAAE0I,WAAW,EAAE1P,OAAO,CAAC;cAC5D;YACF;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAO;QACL+C,GAAG,EAAE8L,KAAK;QACVlE,OAAO,EAAEA,OAAO;QAChBE,YAAY,EAAEA,YAAY;QAC1B6C,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC,EAAE;IACDtN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAAS4L,aAAaA,CAACnJ,GAAG,EAAE;MACjC,OAAOA,GAAG,KAAK5C,SAAS,IAAI,EAAE,CAAC,IAAI,CAACH,OAAO,CAAC4P,UAAU,IAAI7M,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC/C,OAAO,CAAC6P,iBAAiB,IAAI9M,GAAG,KAAK,EAAE,CAAC;IAC7H;EACF,CAAC,EAAE;IACD1C,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASiH,WAAWA,CAAC6H,IAAI,EAAEpI,EAAE,EAAE3G,GAAG,EAAE;MACzC,IAAIL,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAI,IAAI,CAACiL,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC3D,WAAW,EAAE,OAAO,IAAI,CAAC2D,UAAU,CAAC3D,WAAW,CAAC6H,IAAI,EAAEpI,EAAE,EAAE3G,GAAG,EAAEL,OAAO,CAAC;MAC9G,OAAO,IAAI,CAAC8P,aAAa,CAACvI,WAAW,CAAC6H,IAAI,EAAEpI,EAAE,EAAE3G,GAAG,EAAEL,OAAO,CAAC;IAC/D;EACF,CAAC,CAAC,EAAE,CAAC;IACHK,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAASuL,eAAeA,CAAC7L,OAAO,EAAE;MACvC,IAAIO,MAAM,GAAG,cAAc;MAE3B,KAAK,IAAIwP,MAAM,IAAI/P,OAAO,EAAE;QAC1B,IAAIqE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACxE,OAAO,EAAE+P,MAAM,CAAC,IAAIxP,MAAM,KAAKwP,MAAM,CAACC,SAAS,CAAC,CAAC,EAAEzP,MAAM,CAACL,MAAM,CAAC,IAAIC,SAAS,KAAKH,OAAO,CAAC+P,MAAM,CAAC,EAAE;UAC3I,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd;EACF,CAAC,CAAC,CAAC;EAEH,OAAO7G,UAAU;AACnB,CAAC,CAACrH,YAAY,CAAC;AAEf,SAASoO,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACxJ,KAAK,CAAC,CAAC,CAAC;AACzD;AAEA,IAAI2J,YAAY,GAAG,YAAY;EAC7B,SAASA,YAAYA,CAACrQ,OAAO,EAAE;IAC7BjB,eAAe,CAAC,IAAI,EAAEsR,YAAY,CAAC;IAEnC,IAAI,CAACrQ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsQ,SAAS,GAAG,IAAI,CAACtQ,OAAO,CAACuQ,aAAa,IAAI,KAAK;IACpD,IAAI,CAACA,aAAa,GAAG,IAAI,CAACvQ,OAAO,CAACuQ,aAAa,IAAI,KAAK;IACxD,IAAI,CAAC/P,MAAM,GAAGoB,UAAU,CAACF,MAAM,CAAC,eAAe,CAAC;EAClD;EAEA1C,YAAY,CAACqR,YAAY,EAAE,CAAC;IAC1BhQ,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE,SAASkQ,qBAAqBA,CAACpB,IAAI,EAAE;MAC1C,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACpL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;MAC/C,IAAIyC,CAAC,GAAG2I,IAAI,CAACjN,KAAK,CAAC,GAAG,CAAC;MACvB,IAAIsE,CAAC,CAACvG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC/BuG,CAAC,CAACkJ,GAAG,CAAC,CAAC;MACP,IAAIlJ,CAAC,CAACA,CAAC,CAACvG,MAAM,GAAG,CAAC,CAAC,CAACuK,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;MACtD,OAAO,IAAI,CAACgG,kBAAkB,CAAChK,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE;IACDtG,GAAG,EAAE,yBAAyB;IAC9BC,KAAK,EAAE,SAASoQ,uBAAuBA,CAACtB,IAAI,EAAE;MAC5C,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACpL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAOoL,IAAI;MAC/C,IAAI3I,CAAC,GAAG2I,IAAI,CAACjN,KAAK,CAAC,GAAG,CAAC;MACvB,OAAO,IAAI,CAACsO,kBAAkB,CAAChK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC;EACF,CAAC,EAAE;IACDpG,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASmQ,kBAAkBA,CAACrB,IAAI,EAAE;MACvC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACpL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACtD,IAAI2M,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QAC3E,IAAIlK,CAAC,GAAG2I,IAAI,CAACjN,KAAK,CAAC,GAAG,CAAC;QAEvB,IAAI,IAAI,CAACnC,OAAO,CAAC4Q,YAAY,EAAE;UAC7BnK,CAAC,GAAGA,CAAC,CAACoK,GAAG,CAAC,UAAUC,IAAI,EAAE;YACxB,OAAOA,IAAI,CAACrG,WAAW,CAAC,CAAC;UAC3B,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIhE,CAAC,CAACvG,MAAM,KAAK,CAAC,EAAE;UACzBuG,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC;UACzBhE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC2J,WAAW,CAAC,CAAC;UACzB,IAAIO,YAAY,CAAC3M,OAAO,CAACyC,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEhE,CAAC,CAAC,CAAC,CAAC,GAAGwJ,UAAU,CAACxJ,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,CAAC;QAC1F,CAAC,MAAM,IAAIhE,CAAC,CAACvG,MAAM,KAAK,CAAC,EAAE;UACzBuG,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC;UACzB,IAAIhE,CAAC,CAAC,CAAC,CAAC,CAACvG,MAAM,KAAK,CAAC,EAAEuG,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC2J,WAAW,CAAC,CAAC;UAChD,IAAI3J,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACvG,MAAM,KAAK,CAAC,EAAEuG,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC2J,WAAW,CAAC,CAAC;UAClE,IAAIO,YAAY,CAAC3M,OAAO,CAACyC,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEhE,CAAC,CAAC,CAAC,CAAC,GAAGwJ,UAAU,CAACxJ,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,CAAC;UACxF,IAAIkG,YAAY,CAAC3M,OAAO,CAACyC,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEhE,CAAC,CAAC,CAAC,CAAC,GAAGwJ,UAAU,CAACxJ,CAAC,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,CAAC;QAC1F;QAEA,OAAOhE,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;MACpB;MAEA,OAAO,IAAI,CAAC3G,OAAO,CAAC+Q,SAAS,IAAI,IAAI,CAAC/Q,OAAO,CAAC4Q,YAAY,GAAGxB,IAAI,CAAC3E,WAAW,CAAC,CAAC,GAAG2E,IAAI;IACxF;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAAS0Q,aAAaA,CAAC5B,IAAI,EAAE;MAClC,IAAI,CAAC5O,MAAM,CAACY,SAAS,CAAC,6BAA6B,EAAE,gIAAgI,CAAC;MACtL,OAAO,IAAI,CAAC6P,eAAe,CAAC7B,IAAI,CAAC;IACnC;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAAS2Q,eAAeA,CAAC7B,IAAI,EAAE;MACpC,IAAI,IAAI,CAACpP,OAAO,CAACkR,IAAI,KAAK,cAAc,IAAI,IAAI,CAAClR,OAAO,CAACmR,wBAAwB,EAAE;QACjF/B,IAAI,GAAG,IAAI,CAACsB,uBAAuB,CAACtB,IAAI,CAAC;MAC3C;MAEA,OAAO,CAAC,IAAI,CAACmB,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACrQ,MAAM,IAAI,IAAI,CAACqQ,aAAa,CAACvM,OAAO,CAACoL,IAAI,CAAC,GAAG,CAAC,CAAC;IACnG;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE,SAAS8Q,qBAAqBA,CAACnC,KAAK,EAAE;MAC3C,IAAI/M,KAAK,GAAG,IAAI;MAEhB,IAAI,CAAC+M,KAAK,EAAE,OAAO,IAAI;MACvB,IAAIJ,KAAK;MACTI,KAAK,CAAC7M,OAAO,CAAC,UAAUgN,IAAI,EAAE;QAC5B,IAAIP,KAAK,EAAE;QAEX,IAAIwC,UAAU,GAAGnP,KAAK,CAACuO,kBAAkB,CAACrB,IAAI,CAAC;QAE/C,IAAI,CAAClN,KAAK,CAAClC,OAAO,CAACuQ,aAAa,IAAIrO,KAAK,CAAC+O,eAAe,CAACI,UAAU,CAAC,EAAExC,KAAK,GAAGwC,UAAU;MAC3F,CAAC,CAAC;MAEF,IAAI,CAACxC,KAAK,IAAI,IAAI,CAAC7O,OAAO,CAACuQ,aAAa,EAAE;QACxCtB,KAAK,CAAC7M,OAAO,CAAC,UAAUgN,IAAI,EAAE;UAC5B,IAAIP,KAAK,EAAE;UAEX,IAAIyC,OAAO,GAAGpP,KAAK,CAACwO,uBAAuB,CAACtB,IAAI,CAAC;UAEjD,IAAIlN,KAAK,CAAC+O,eAAe,CAACK,OAAO,CAAC,EAAE,OAAOzC,KAAK,GAAGyC,OAAO;UAC1DzC,KAAK,GAAG3M,KAAK,CAAClC,OAAO,CAACuQ,aAAa,CAACgB,IAAI,CAAC,UAAUC,YAAY,EAAE;YAC/D,IAAIA,YAAY,CAACxN,OAAO,CAACsN,OAAO,CAAC,KAAK,CAAC,EAAE,OAAOE,YAAY;UAC9D,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC3C,KAAK,EAAEA,KAAK,GAAG,IAAI,CAACnC,gBAAgB,CAAC,IAAI,CAAC1M,OAAO,CAAC2M,WAAW,CAAC,CAAC,CAAC,CAAC;MACtE,OAAOkC,KAAK;IACd;EACF,CAAC,EAAE;IACDxO,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,SAASoM,gBAAgBA,CAAC+E,SAAS,EAAErC,IAAI,EAAE;MAChD,IAAI,CAACqC,SAAS,EAAE,OAAO,EAAE;MACzB,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAEA,SAAS,GAAGA,SAAS,CAACrC,IAAI,CAAC;MAChE,IAAI,OAAOqC,SAAS,KAAK,QAAQ,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;MAC1D,IAAIpN,MAAM,CAACC,SAAS,CAACwD,QAAQ,CAACjI,KAAK,CAAC4R,SAAS,CAAC,KAAK,gBAAgB,EAAE,OAAOA,SAAS;MACrF,IAAI,CAACrC,IAAI,EAAE,OAAOqC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE;MAC5C,IAAI5C,KAAK,GAAG4C,SAAS,CAACrC,IAAI,CAAC;MAC3B,IAAI,CAACP,KAAK,EAAEA,KAAK,GAAG4C,SAAS,CAAC,IAAI,CAACjB,qBAAqB,CAACpB,IAAI,CAAC,CAAC;MAC/D,IAAI,CAACP,KAAK,EAAEA,KAAK,GAAG4C,SAAS,CAAC,IAAI,CAAChB,kBAAkB,CAACrB,IAAI,CAAC,CAAC;MAC5D,IAAI,CAACP,KAAK,EAAEA,KAAK,GAAG4C,SAAS,CAAC,IAAI,CAACf,uBAAuB,CAACtB,IAAI,CAAC,CAAC;MACjE,IAAI,CAACP,KAAK,EAAEA,KAAK,GAAG4C,SAAS,CAAC,SAAS,CAAC;MACxC,OAAO5C,KAAK,IAAI,EAAE;IACpB;EACF,CAAC,EAAE;IACDxO,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASuM,kBAAkBA,CAACuC,IAAI,EAAEsC,YAAY,EAAE;MACrD,IAAIvH,MAAM,GAAG,IAAI;MAEjB,IAAIwH,aAAa,GAAG,IAAI,CAACjF,gBAAgB,CAACgF,YAAY,IAAI,IAAI,CAAC1R,OAAO,CAAC2M,WAAW,IAAI,EAAE,EAAEyC,IAAI,CAAC;MAC/F,IAAIH,KAAK,GAAG,EAAE;MAEd,IAAI2C,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAE;QAChC,IAAI,CAACA,CAAC,EAAE;QAER,IAAI1H,MAAM,CAAC8G,eAAe,CAACY,CAAC,CAAC,EAAE;UAC7B5C,KAAK,CAAC3M,IAAI,CAACuP,CAAC,CAAC;QACf,CAAC,MAAM;UACL1H,MAAM,CAAC3J,MAAM,CAACd,IAAI,CAAC,sDAAsD,CAAC+B,MAAM,CAACoQ,CAAC,CAAC,CAAC;QACtF;MACF,CAAC;MAED,IAAI,OAAOzC,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACpL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACtD,IAAI,IAAI,CAAChE,OAAO,CAACkR,IAAI,KAAK,cAAc,EAAEU,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACrB,IAAI,CAAC,CAAC;QAChF,IAAI,IAAI,CAACpP,OAAO,CAACkR,IAAI,KAAK,cAAc,IAAI,IAAI,CAAClR,OAAO,CAACkR,IAAI,KAAK,aAAa,EAAEU,OAAO,CAAC,IAAI,CAACpB,qBAAqB,CAACpB,IAAI,CAAC,CAAC;QAC1H,IAAI,IAAI,CAACpP,OAAO,CAACkR,IAAI,KAAK,aAAa,EAAEU,OAAO,CAAC,IAAI,CAAClB,uBAAuB,CAACtB,IAAI,CAAC,CAAC;MACtF,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACnCwC,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACrB,IAAI,CAAC,CAAC;MACxC;MAEAuC,aAAa,CAACvP,OAAO,CAAC,UAAU0P,EAAE,EAAE;QAClC,IAAI7C,KAAK,CAACjL,OAAO,CAAC8N,EAAE,CAAC,GAAG,CAAC,EAAEF,OAAO,CAACzH,MAAM,CAACsG,kBAAkB,CAACqB,EAAE,CAAC,CAAC;MACnE,CAAC,CAAC;MACF,OAAO7C,KAAK;IACd;EACF,CAAC,CAAC,CAAC;EAEH,OAAOoB,YAAY;AACrB,CAAC,CAAC,CAAC;AAEH,IAAI0B,IAAI,GAAG,CAAC;EACVxF,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtIyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9YyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7IyF,EAAE,EAAE,CAAC,CAAC,CAAC;EACPF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvDyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC;EACzBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClByF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EACnByF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACpBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACjBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACdF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,KAAK,CAAC;EACbyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAClBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACdF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClByF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAClBF,EAAE,EAAE;AACN,CAAC,CAAC;AACF,IAAIG,kBAAkB,GAAG;EACvB,CAAC,EAAE,SAASC,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC;EACtB,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,CAAC;EACvB,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAO,CAAC;EACV,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzH,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACjH,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnG,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACpE,CAAC;EACD,CAAC,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IACf,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,CAAC;EACvB,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACrE,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACxF,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;EAC7C,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,KAAK,CAAC,CAAC;EACxB,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzD,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1G,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnE,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/D,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5C,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC9G,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3E,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3F,CAAC;EACD,EAAE,EAAE,SAASD,CAACA,CAACC,CAAC,EAAE;IAChB,OAAOC,MAAM,CAACD,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,KAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnF;AACF,CAAC;AAED,SAASE,WAAWA,CAAA,EAAG;EACrB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACdP,IAAI,CAAC3P,OAAO,CAAC,UAAUmQ,GAAG,EAAE;IAC1BA,GAAG,CAAChG,IAAI,CAACnK,OAAO,CAAC,UAAUK,CAAC,EAAE;MAC5B6P,KAAK,CAAC7P,CAAC,CAAC,GAAG;QACT+P,OAAO,EAAED,GAAG,CAACP,EAAE;QACfS,OAAO,EAAER,kBAAkB,CAACM,GAAG,CAACT,EAAE;MACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOQ,KAAK;AACd;AAEA,IAAII,cAAc,GAAG,YAAY;EAC/B,SAASA,cAAcA,CAACjG,aAAa,EAAE;IACrC,IAAIzM,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpFlB,eAAe,CAAC,IAAI,EAAE2T,cAAc,CAAC;IAErC,IAAI,CAACjG,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzM,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACQ,MAAM,GAAGoB,UAAU,CAACF,MAAM,CAAC,gBAAgB,CAAC;IACjD,IAAI,CAAC4Q,KAAK,GAAGD,WAAW,CAAC,CAAC;EAC5B;EAEArT,YAAY,CAAC0T,cAAc,EAAE,CAAC;IAC5BrS,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASqS,OAAOA,CAACnL,GAAG,EAAE/C,GAAG,EAAE;MAChC,IAAI,CAAC6N,KAAK,CAAC9K,GAAG,CAAC,GAAG/C,GAAG;IACvB;EACF,CAAC,EAAE;IACDpE,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASsS,OAAOA,CAACxD,IAAI,EAAE;MAC5B,OAAO,IAAI,CAACkD,KAAK,CAAClD,IAAI,CAAC,IAAI,IAAI,CAACkD,KAAK,CAAC,IAAI,CAAC7F,aAAa,CAACiE,uBAAuB,CAACtB,IAAI,CAAC,CAAC;IACzF;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASuS,WAAWA,CAACzD,IAAI,EAAE;MAChC,IAAI0D,IAAI,GAAG,IAAI,CAACF,OAAO,CAACxD,IAAI,CAAC;MAC7B,OAAO0D,IAAI,IAAIA,IAAI,CAACN,OAAO,CAACtS,MAAM,GAAG,CAAC;IACxC;EACF,CAAC,EAAE;IACDG,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,SAASyS,mBAAmBA,CAAC3D,IAAI,EAAE/O,GAAG,EAAE;MAC7C,OAAO,IAAI,CAAC+M,WAAW,CAACgC,IAAI,CAAC,CAACyB,GAAG,CAAC,UAAUxD,MAAM,EAAE;QAClD,OAAOhN,GAAG,GAAGgN,MAAM;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAAS8M,WAAWA,CAACgC,IAAI,EAAE;MAChC,IAAIlN,KAAK,GAAG,IAAI;MAEhB,IAAI4Q,IAAI,GAAG,IAAI,CAACF,OAAO,CAACxD,IAAI,CAAC;MAE7B,IAAI,CAAC0D,IAAI,EAAE;QACT,OAAO,EAAE;MACX;MAEA,OAAOA,IAAI,CAACN,OAAO,CAAC3B,GAAG,CAAC,UAAUmC,MAAM,EAAE;QACxC,OAAO9Q,KAAK,CAAC8J,SAAS,CAACoD,IAAI,EAAE4D,MAAM,CAAC;MACtC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD3S,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAAS0L,SAASA,CAACoD,IAAI,EAAExD,KAAK,EAAE;MACrC,IAAIzB,MAAM,GAAG,IAAI;MAEjB,IAAI2I,IAAI,GAAG,IAAI,CAACF,OAAO,CAACxD,IAAI,CAAC;MAE7B,IAAI0D,IAAI,EAAE;QACR,IAAIG,GAAG,GAAGH,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACL,OAAO,CAAC7G,KAAK,CAAC,GAAGkH,IAAI,CAACL,OAAO,CAACU,IAAI,CAACC,GAAG,CAACxH,KAAK,CAAC,CAAC;QAC1E,IAAIyB,MAAM,GAAGyF,IAAI,CAACN,OAAO,CAACS,GAAG,CAAC;QAE9B,IAAI,IAAI,CAACjT,OAAO,CAACqT,oBAAoB,IAAIP,IAAI,CAACN,OAAO,CAACtS,MAAM,KAAK,CAAC,IAAI4S,IAAI,CAACN,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UAC3F,IAAInF,MAAM,KAAK,CAAC,EAAE;YAChBA,MAAM,GAAG,QAAQ;UACnB,CAAC,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE;YACvBA,MAAM,GAAG,EAAE;UACb;QACF;QAEA,IAAIiG,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;UACzC,OAAOnJ,MAAM,CAACnK,OAAO,CAACuT,OAAO,IAAIlG,MAAM,CAACvF,QAAQ,CAAC,CAAC,GAAGqC,MAAM,CAACnK,OAAO,CAACuT,OAAO,GAAGlG,MAAM,CAACvF,QAAQ,CAAC,CAAC,GAAGuF,MAAM,CAACvF,QAAQ,CAAC,CAAC;QACrH,CAAC;QAED,IAAI,IAAI,CAAC9H,OAAO,CAACwT,iBAAiB,KAAK,IAAI,EAAE;UAC3C,IAAInG,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;UAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,OAAO,UAAU,CAAC5L,MAAM,CAAC4L,MAAM,CAACvF,QAAQ,CAAC,CAAC,CAAC;UAC3E,OAAOwL,YAAY,CAAC,CAAC;QACvB,CAAC,MAAM,IAAI,IAAI,CAACtT,OAAO,CAACwT,iBAAiB,KAAK,IAAI,EAAE;UAClD,OAAOF,YAAY,CAAC,CAAC;QACvB,CAAC,MAAM,IAAI,IAAI,CAACtT,OAAO,CAACqT,oBAAoB,IAAIP,IAAI,CAACN,OAAO,CAACtS,MAAM,KAAK,CAAC,IAAI4S,IAAI,CAACN,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UAClG,OAAOc,YAAY,CAAC,CAAC;QACvB;QAEA,OAAO,IAAI,CAACtT,OAAO,CAACuT,OAAO,IAAIN,GAAG,CAACnL,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC9H,OAAO,CAACuT,OAAO,GAAGN,GAAG,CAACnL,QAAQ,CAAC,CAAC,GAAGmL,GAAG,CAACnL,QAAQ,CAAC,CAAC;MACxG;MAEA,IAAI,CAACtH,MAAM,CAACd,IAAI,CAAC,4BAA4B,CAAC+B,MAAM,CAAC2N,IAAI,CAAC,CAAC;MAC3D,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CAAC;EAEH,OAAOsD,cAAc;AACvB,CAAC,CAAC,CAAC;AAEH,IAAIe,YAAY,GAAG,YAAY;EAC7B,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIzT,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpFlB,eAAe,CAAC,IAAI,EAAE0U,YAAY,CAAC;IAEnC,IAAI,CAACjT,MAAM,GAAGoB,UAAU,CAACF,MAAM,CAAC,cAAc,CAAC;IAC/C,IAAI,CAAC1B,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAAC0T,MAAM,GAAG1T,OAAO,CAACuJ,aAAa,IAAIvJ,OAAO,CAACuJ,aAAa,CAACmK,MAAM,IAAI,UAAUpT,KAAK,EAAE;MACtF,OAAOA,KAAK;IACd,CAAC;IAED,IAAI,CAACF,IAAI,CAACJ,OAAO,CAAC;EACpB;EAEAhB,YAAY,CAACyU,YAAY,EAAE,CAAC;IAC1BpT,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASF,IAAIA,CAAA,EAAG;MACrB,IAAIJ,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAI,CAACD,OAAO,CAACuJ,aAAa,EAAEvJ,OAAO,CAACuJ,aAAa,GAAG;QAClDoK,WAAW,EAAE;MACf,CAAC;MACD,IAAIC,KAAK,GAAG5T,OAAO,CAACuJ,aAAa;MACjC,IAAI,CAACzD,MAAM,GAAG8N,KAAK,CAAC9N,MAAM,KAAK3F,SAAS,GAAGyT,KAAK,CAAC9N,MAAM,GAAGA,MAAM;MAChE,IAAI,CAAC6N,WAAW,GAAGC,KAAK,CAACD,WAAW,KAAKxT,SAAS,GAAGyT,KAAK,CAACD,WAAW,GAAG,IAAI;MAC7E,IAAI,CAACE,mBAAmB,GAAGD,KAAK,CAACC,mBAAmB,KAAK1T,SAAS,GAAGyT,KAAK,CAACC,mBAAmB,GAAG,KAAK;MACtG,IAAI,CAACtT,MAAM,GAAGqT,KAAK,CAACrT,MAAM,GAAGoF,WAAW,CAACiO,KAAK,CAACrT,MAAM,CAAC,GAAGqT,KAAK,CAACE,aAAa,IAAI,IAAI;MACpF,IAAI,CAACzG,MAAM,GAAGuG,KAAK,CAACvG,MAAM,GAAG1H,WAAW,CAACiO,KAAK,CAACvG,MAAM,CAAC,GAAGuG,KAAK,CAACG,aAAa,IAAI,IAAI;MACpF,IAAI,CAACC,eAAe,GAAGJ,KAAK,CAACI,eAAe,GAAGJ,KAAK,CAACI,eAAe,GAAGJ,KAAK,CAACI,eAAe,IAAI,GAAG;MACnG,IAAI,CAACC,cAAc,GAAGL,KAAK,CAACM,cAAc,GAAG,EAAE,GAAGN,KAAK,CAACK,cAAc,IAAI,GAAG;MAC7E,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,cAAc,GAAG,EAAE,GAAGL,KAAK,CAACM,cAAc,IAAI,EAAE;MAC3E,IAAI,CAACC,aAAa,GAAGP,KAAK,CAACO,aAAa,GAAGxO,WAAW,CAACiO,KAAK,CAACO,aAAa,CAAC,GAAGP,KAAK,CAACQ,oBAAoB,IAAIzO,WAAW,CAAC,KAAK,CAAC;MAC9H,IAAI,CAAC0O,aAAa,GAAGT,KAAK,CAACS,aAAa,GAAG1O,WAAW,CAACiO,KAAK,CAACS,aAAa,CAAC,GAAGT,KAAK,CAACU,oBAAoB,IAAI3O,WAAW,CAAC,GAAG,CAAC;MAC5H,IAAI,CAAC4O,uBAAuB,GAAGX,KAAK,CAACW,uBAAuB,GAAGX,KAAK,CAACW,uBAAuB,GAAGX,KAAK,CAACW,uBAAuB,IAAI,GAAG;MACnI,IAAI,CAACC,WAAW,GAAGZ,KAAK,CAACY,WAAW,GAAGZ,KAAK,CAACY,WAAW,GAAG,IAAI;MAC/D,IAAI,CAACC,YAAY,GAAGb,KAAK,CAACa,YAAY,KAAKtU,SAAS,GAAGyT,KAAK,CAACa,YAAY,GAAG,KAAK;MACjF,IAAI,CAACC,WAAW,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDrU,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASgO,KAAKA,CAAA,EAAG;MACtB,IAAI,IAAI,CAACtO,OAAO,EAAE,IAAI,CAACI,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASoU,WAAWA,CAAA,EAAG;MAC5B,IAAIC,SAAS,GAAG,EAAE,CAAClT,MAAM,CAAC,IAAI,CAAClB,MAAM,EAAE,OAAO,CAAC,CAACkB,MAAM,CAAC,IAAI,CAAC4L,MAAM,CAAC;MACnE,IAAI,CAACuH,MAAM,GAAG,IAAIC,MAAM,CAACF,SAAS,EAAE,GAAG,CAAC;MACxC,IAAIG,iBAAiB,GAAG,EAAE,CAACrT,MAAM,CAAC,IAAI,CAAClB,MAAM,CAAC,CAACkB,MAAM,CAAC,IAAI,CAACwS,cAAc,EAAE,OAAO,CAAC,CAACxS,MAAM,CAAC,IAAI,CAACyS,cAAc,CAAC,CAACzS,MAAM,CAAC,IAAI,CAAC4L,MAAM,CAAC;MACnI,IAAI,CAAC0H,cAAc,GAAG,IAAIF,MAAM,CAACC,iBAAiB,EAAE,GAAG,CAAC;MACxD,IAAIE,gBAAgB,GAAG,EAAE,CAACvT,MAAM,CAAC,IAAI,CAAC0S,aAAa,EAAE,OAAO,CAAC,CAAC1S,MAAM,CAAC,IAAI,CAAC4S,aAAa,CAAC;MACxF,IAAI,CAACvK,aAAa,GAAG,IAAI+K,MAAM,CAACG,gBAAgB,EAAE,GAAG,CAAC;IACxD;EACF,CAAC,EAAE;IACD3U,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAAS2N,WAAWA,CAACrI,GAAG,EAAET,IAAI,EAAEqC,GAAG,EAAExH,OAAO,EAAE;MACnD,IAAIkC,KAAK,GAAG,IAAI;MAEhB,IAAI0H,KAAK;MACT,IAAItJ,KAAK;MACT,IAAI2U,QAAQ;MACZ,IAAI7P,WAAW,GAAG,IAAI,CAACpF,OAAO,IAAI,IAAI,CAACA,OAAO,CAACuJ,aAAa,IAAI,IAAI,CAACvJ,OAAO,CAACuJ,aAAa,CAACyE,gBAAgB,IAAI,CAAC,CAAC;MAEjH,SAASkH,SAASA,CAACC,GAAG,EAAE;QACtB,OAAOA,GAAG,CAAClR,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACnC;MAEA,IAAImR,YAAY,GAAG,SAASA,YAAYA,CAAC/U,GAAG,EAAE;QAC5C,IAAIA,GAAG,CAAC2D,OAAO,CAAC9B,KAAK,CAAC8R,eAAe,CAAC,GAAG,CAAC,EAAE;UAC1C,IAAInQ,IAAI,GAAGqB,mBAAmB,CAACC,IAAI,EAAEC,WAAW,EAAE/E,GAAG,CAAC;UACtD,OAAO6B,KAAK,CAACuS,YAAY,GAAGvS,KAAK,CAACwR,MAAM,CAAC7P,IAAI,EAAE1D,SAAS,EAAEqH,GAAG,EAAE1I,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAEmF,IAAI,EAAE;YAC9FkQ,gBAAgB,EAAEhV;UACpB,CAAC,CAAC,CAAC,GAAGwD,IAAI;QACZ;QAEA,IAAI4C,CAAC,GAAGpG,GAAG,CAAC8B,KAAK,CAACD,KAAK,CAAC8R,eAAe,CAAC;QACxC,IAAItP,CAAC,GAAG+B,CAAC,CAACrC,KAAK,CAAC,CAAC,CAACkR,IAAI,CAAC,CAAC;QACxB,IAAIC,CAAC,GAAG9O,CAAC,CAACE,IAAI,CAACzE,KAAK,CAAC8R,eAAe,CAAC,CAACsB,IAAI,CAAC,CAAC;QAC5C,OAAOpT,KAAK,CAACwR,MAAM,CAACxO,mBAAmB,CAACC,IAAI,EAAEC,WAAW,EAAEV,CAAC,CAAC,EAAE6Q,CAAC,EAAE/N,GAAG,EAAE1I,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAEmF,IAAI,EAAE;UACtGkQ,gBAAgB,EAAE3Q;QACpB,CAAC,CAAC,CAAC;MACL,CAAC;MAED,IAAI,CAACgQ,WAAW,CAAC,CAAC;MAClB,IAAIc,2BAA2B,GAAGxV,OAAO,IAAIA,OAAO,CAACwV,2BAA2B,IAAI,IAAI,CAACxV,OAAO,CAACwV,2BAA2B;MAC5H,IAAI3H,eAAe,GAAG7N,OAAO,IAAIA,OAAO,CAACuJ,aAAa,IAAIvJ,OAAO,CAACuJ,aAAa,CAACsE,eAAe,IAAI,IAAI,CAAC7N,OAAO,CAACuJ,aAAa,CAACsE,eAAe;MAC7I,IAAI4H,KAAK,GAAG,CAAC;QACXC,KAAK,EAAE,IAAI,CAACX,cAAc;QAC1BY,SAAS,EAAE,SAASA,SAASA,CAACR,GAAG,EAAE;UACjC,OAAOD,SAAS,CAACC,GAAG,CAAC;QACvB;MACF,CAAC,EAAE;QACDO,KAAK,EAAE,IAAI,CAACd,MAAM;QAClBe,SAAS,EAAE,SAASA,SAASA,CAACR,GAAG,EAAE;UACjC,OAAOjT,KAAK,CAACyR,WAAW,GAAGuB,SAAS,CAAChT,KAAK,CAAC4D,MAAM,CAACqP,GAAG,CAAC,CAAC,GAAGD,SAAS,CAACC,GAAG,CAAC;QAC1E;MACF,CAAC,CAAC;MACFM,KAAK,CAACrT,OAAO,CAAC,UAAUwT,IAAI,EAAE;QAC5BX,QAAQ,GAAG,CAAC;QAEZ,OAAOrL,KAAK,GAAGgM,IAAI,CAACF,KAAK,CAACG,IAAI,CAACjQ,GAAG,CAAC,EAAE;UACnCtF,KAAK,GAAG8U,YAAY,CAACxL,KAAK,CAAC,CAAC,CAAC,CAAC0L,IAAI,CAAC,CAAC,CAAC;UAErC,IAAIhV,KAAK,KAAKH,SAAS,EAAE;YACvB,IAAI,OAAOqV,2BAA2B,KAAK,UAAU,EAAE;cACrD,IAAIM,IAAI,GAAGN,2BAA2B,CAAC5P,GAAG,EAAEgE,KAAK,EAAE5J,OAAO,CAAC;cAC3DM,KAAK,GAAG,OAAOwV,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG,EAAE;YAC9C,CAAC,MAAM,IAAIjI,eAAe,EAAE;cAC1BvN,KAAK,GAAGsJ,KAAK,CAAC,CAAC,CAAC;cAChB;YACF,CAAC,MAAM;cACL1H,KAAK,CAAC1B,MAAM,CAACd,IAAI,CAAC,6BAA6B,CAAC+B,MAAM,CAACmI,KAAK,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAACnI,MAAM,CAACmE,GAAG,CAAC,CAAC;cAEpGtF,KAAK,GAAG,EAAE;YACZ;UACF,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC4B,KAAK,CAAC2R,mBAAmB,EAAE;YAClEvT,KAAK,GAAG+C,UAAU,CAAC/C,KAAK,CAAC;UAC3B;UAEA,IAAIqV,SAAS,GAAGC,IAAI,CAACD,SAAS,CAACrV,KAAK,CAAC;UACrCsF,GAAG,GAAGA,GAAG,CAAC3B,OAAO,CAAC2F,KAAK,CAAC,CAAC,CAAC,EAAE+L,SAAS,CAAC;UAEtC,IAAI9H,eAAe,EAAE;YACnB+H,IAAI,CAACF,KAAK,CAACK,SAAS,IAAIJ,SAAS,CAACzV,MAAM;YACxC0V,IAAI,CAACF,KAAK,CAACK,SAAS,IAAInM,KAAK,CAAC,CAAC,CAAC,CAAC1J,MAAM;UACzC,CAAC,MAAM;YACL0V,IAAI,CAACF,KAAK,CAACK,SAAS,GAAG,CAAC;UAC1B;UAEAd,QAAQ,EAAE;UAEV,IAAIA,QAAQ,IAAI/S,KAAK,CAACsS,WAAW,EAAE;YACjC;UACF;QACF;MACF,CAAC,CAAC;MACF,OAAO5O,GAAG;IACZ;EACF,CAAC,EAAE;IACDvF,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAAS8N,IAAIA,CAACxI,GAAG,EAAEkM,EAAE,EAAE;MAC5B,IAAI3H,MAAM,GAAG,IAAI;MAEjB,IAAInK,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAI2J,KAAK;MACT,IAAItJ,KAAK;MAET,IAAI0V,aAAa,GAAGlX,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,CAAC;MAE9CgW,aAAa,CAACvH,kBAAkB,GAAG,KAAK;MACxC,OAAOuH,aAAa,CAAC/J,YAAY;MAEjC,SAASgK,gBAAgBA,CAAC5V,GAAG,EAAE6V,gBAAgB,EAAE;QAC/C,IAAIC,GAAG,GAAG,IAAI,CAAC5B,uBAAuB;QACtC,IAAIlU,GAAG,CAAC2D,OAAO,CAACmS,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO9V,GAAG;QACpC,IAAIwR,CAAC,GAAGxR,GAAG,CAAC8B,KAAK,CAAC,IAAI0S,MAAM,CAAC,EAAE,CAACpT,MAAM,CAAC0U,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;QACtD,IAAIC,aAAa,GAAG,GAAG,CAAC3U,MAAM,CAACoQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACpCxR,GAAG,GAAGwR,CAAC,CAAC,CAAC,CAAC;QACVuE,aAAa,GAAG,IAAI,CAACnI,WAAW,CAACmI,aAAa,EAAEJ,aAAa,CAAC;QAC9DI,aAAa,GAAGA,aAAa,CAACnS,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;QAEhD,IAAI;UACF+R,aAAa,GAAGK,IAAI,CAAC5I,KAAK,CAAC2I,aAAa,CAAC;UACzC,IAAIF,gBAAgB,EAAEF,aAAa,GAAGlX,aAAa,CAAC,CAAC,CAAC,EAAEoX,gBAAgB,EAAEF,aAAa,CAAC;QAC1F,CAAC,CAAC,OAAOM,CAAC,EAAE;UACV,IAAI,CAAC9V,MAAM,CAACd,IAAI,CAAC,mDAAmD,CAAC+B,MAAM,CAACpB,GAAG,CAAC,EAAEiW,CAAC,CAAC;UACpF,OAAO,EAAE,CAAC7U,MAAM,CAACpB,GAAG,CAAC,CAACoB,MAAM,CAAC0U,GAAG,CAAC,CAAC1U,MAAM,CAAC2U,aAAa,CAAC;QACzD;QAEA,OAAOJ,aAAa,CAAC/J,YAAY;QACjC,OAAO5L,GAAG;MACZ;MAEA,OAAOuJ,KAAK,GAAG,IAAI,CAACE,aAAa,CAAC+L,IAAI,CAACjQ,GAAG,CAAC,EAAE;QAC3C,IAAI2Q,UAAU,GAAG,EAAE;QACnB,IAAIC,QAAQ,GAAG,KAAK;QAEpB,IAAI5M,KAAK,CAAC,CAAC,CAAC,CAAC5F,OAAO,CAAC,IAAI,CAACgQ,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAACyC,IAAI,CAAC7M,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3E,IAAI8M,CAAC,GAAG9M,KAAK,CAAC,CAAC,CAAC,CAACzH,KAAK,CAAC,IAAI,CAAC6R,eAAe,CAAC,CAACnD,GAAG,CAAC,UAAU8F,IAAI,EAAE;YAC/D,OAAOA,IAAI,CAACrB,IAAI,CAAC,CAAC;UACpB,CAAC,CAAC;UACF1L,KAAK,CAAC,CAAC,CAAC,GAAG8M,CAAC,CAACtS,KAAK,CAAC,CAAC;UACpBmS,UAAU,GAAGG,CAAC;UACdF,QAAQ,GAAG,IAAI;QACjB;QAEAlW,KAAK,GAAGwR,EAAE,CAACmE,gBAAgB,CAACzR,IAAI,CAAC,IAAI,EAAEoF,KAAK,CAAC,CAAC,CAAC,CAAC0L,IAAI,CAAC,CAAC,EAAEU,aAAa,CAAC,EAAEA,aAAa,CAAC;QACtF,IAAI1V,KAAK,IAAIsJ,KAAK,CAAC,CAAC,CAAC,KAAKhE,GAAG,IAAI,OAAOtF,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;QACxE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAEA,KAAK,GAAG+C,UAAU,CAAC/C,KAAK,CAAC;QAExD,IAAI,CAACA,KAAK,EAAE;UACV,IAAI,CAACE,MAAM,CAACd,IAAI,CAAC,oBAAoB,CAAC+B,MAAM,CAACmI,KAAK,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAACnI,MAAM,CAACmE,GAAG,CAAC,CAAC;UACpFtF,KAAK,GAAG,EAAE;QACZ;QAEA,IAAIkW,QAAQ,EAAE;UACZlW,KAAK,GAAGiW,UAAU,CAACK,MAAM,CAAC,UAAUC,CAAC,EAAEtB,CAAC,EAAE;YACxC,OAAOpL,MAAM,CAACuJ,MAAM,CAACmD,CAAC,EAAEtB,CAAC,EAAEvV,OAAO,CAACwH,GAAG,EAAE1I,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAE;cACjEqV,gBAAgB,EAAEzL,KAAK,CAAC,CAAC,CAAC,CAAC0L,IAAI,CAAC;YAClC,CAAC,CAAC,CAAC;UACL,CAAC,EAAEhV,KAAK,CAACgV,IAAI,CAAC,CAAC,CAAC;QAClB;QAEA1P,GAAG,GAAGA,GAAG,CAAC3B,OAAO,CAAC2F,KAAK,CAAC,CAAC,CAAC,EAAEtJ,KAAK,CAAC;QAClC,IAAI,CAACsU,MAAM,CAACmB,SAAS,GAAG,CAAC;MAC3B;MAEA,OAAOnQ,GAAG;IACZ;EACF,CAAC,CAAC,CAAC;EAEH,OAAO6N,YAAY;AACrB,CAAC,CAAC,CAAC;AAEH,SAASqD,MAAMA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,IAAInI,KAAK,GAAGkI,GAAG,CAAC/S,OAAO,CAACgT,IAAI,CAAC;EAE7B,OAAOnI,KAAK,KAAK,CAAC,CAAC,EAAE;IACnBkI,GAAG,CAACzP,MAAM,CAACuH,KAAK,EAAE,CAAC,CAAC;IACpBA,KAAK,GAAGkI,GAAG,CAAC/S,OAAO,CAACgT,IAAI,CAAC;EAC3B;AACF;AAEA,IAAIC,SAAS,GAAG,UAAUlQ,aAAa,EAAE;EACvC3H,SAAS,CAAC6X,SAAS,EAAElQ,aAAa,CAAC;EAEnC,SAASkQ,SAASA,CAACC,OAAO,EAAEC,KAAK,EAAEhO,QAAQ,EAAE;IAC3C,IAAIjH,KAAK;IAET,IAAIlC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpFlB,eAAe,CAAC,IAAI,EAAEkY,SAAS,CAAC;IAEhC/U,KAAK,GAAGjD,0BAA0B,CAAC,IAAI,EAAEC,eAAe,CAAC+X,SAAS,CAAC,CAACzS,IAAI,CAAC,IAAI,CAAC,CAAC;IAE/E,IAAIuB,MAAM,EAAE;MACVlE,YAAY,CAAC2C,IAAI,CAACrF,sBAAsB,CAAC+C,KAAK,CAAC,CAAC;IAClD;IAEAA,KAAK,CAACgV,OAAO,GAAGA,OAAO;IACvBhV,KAAK,CAACiV,KAAK,GAAGA,KAAK;IACnBjV,KAAK,CAACiH,QAAQ,GAAGA,QAAQ;IACzBjH,KAAK,CAACuK,aAAa,GAAGtD,QAAQ,CAACsD,aAAa;IAC5CvK,KAAK,CAAClC,OAAO,GAAGA,OAAO;IACvBkC,KAAK,CAAC1B,MAAM,GAAGoB,UAAU,CAACF,MAAM,CAAC,kBAAkB,CAAC;IACpDQ,KAAK,CAACkV,KAAK,GAAG,CAAC,CAAC;IAChBlV,KAAK,CAACmV,KAAK,GAAG,EAAE;IAEhB,IAAInV,KAAK,CAACgV,OAAO,IAAIhV,KAAK,CAACgV,OAAO,CAAC9W,IAAI,EAAE;MACvC8B,KAAK,CAACgV,OAAO,CAAC9W,IAAI,CAAC+I,QAAQ,EAAEnJ,OAAO,CAACkX,OAAO,EAAElX,OAAO,CAAC;IACxD;IAEA,OAAOkC,KAAK;EACd;EAEAlD,YAAY,CAACiY,SAAS,EAAE,CAAC;IACvB5W,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASgX,SAASA,CAACC,SAAS,EAAE5N,UAAU,EAAE3J,OAAO,EAAEwX,QAAQ,EAAE;MAClE,IAAIrN,MAAM,GAAG,IAAI;MAEjB,IAAIsN,MAAM,GAAG,EAAE;MACf,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,gBAAgB,GAAG,EAAE;MACzBL,SAAS,CAACnV,OAAO,CAAC,UAAUoF,GAAG,EAAE;QAC/B,IAAIqQ,gBAAgB,GAAG,IAAI;QAC3BlO,UAAU,CAACvH,OAAO,CAAC,UAAU4E,EAAE,EAAE;UAC/B,IAAI4B,IAAI,GAAG,EAAE,CAACnH,MAAM,CAAC+F,GAAG,EAAE,GAAG,CAAC,CAAC/F,MAAM,CAACuF,EAAE,CAAC;UAEzC,IAAI,CAAChH,OAAO,CAAC8X,MAAM,IAAI3N,MAAM,CAACgN,KAAK,CAAChP,iBAAiB,CAACX,GAAG,EAAER,EAAE,CAAC,EAAE;YAC9DmD,MAAM,CAACiN,KAAK,CAACxO,IAAI,CAAC,GAAG,CAAC;UACxB,CAAC,MAAM,IAAIuB,MAAM,CAACiN,KAAK,CAACxO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAM,IAAIuB,MAAM,CAACiN,KAAK,CAACxO,IAAI,CAAC,KAAK,CAAC,EAAE;YACtE,IAAI8O,OAAO,CAAC1T,OAAO,CAAC4E,IAAI,CAAC,GAAG,CAAC,EAAE8O,OAAO,CAACpV,IAAI,CAACsG,IAAI,CAAC;UACnD,CAAC,MAAM;YACLuB,MAAM,CAACiN,KAAK,CAACxO,IAAI,CAAC,GAAG,CAAC;YACtBiP,gBAAgB,GAAG,KAAK;YACxB,IAAIH,OAAO,CAAC1T,OAAO,CAAC4E,IAAI,CAAC,GAAG,CAAC,EAAE8O,OAAO,CAACpV,IAAI,CAACsG,IAAI,CAAC;YACjD,IAAI6O,MAAM,CAACzT,OAAO,CAAC4E,IAAI,CAAC,GAAG,CAAC,EAAE6O,MAAM,CAACnV,IAAI,CAACsG,IAAI,CAAC;YAC/C,IAAIgP,gBAAgB,CAAC5T,OAAO,CAACgD,EAAE,CAAC,GAAG,CAAC,EAAE4Q,gBAAgB,CAACtV,IAAI,CAAC0E,EAAE,CAAC;UACjE;QACF,CAAC,CAAC;QACF,IAAI,CAAC6Q,gBAAgB,EAAEF,eAAe,CAACrV,IAAI,CAACkF,GAAG,CAAC;MAClD,CAAC,CAAC;MAEF,IAAIiQ,MAAM,CAACvX,MAAM,IAAIwX,OAAO,CAACxX,MAAM,EAAE;QACnC,IAAI,CAACmX,KAAK,CAAC/U,IAAI,CAAC;UACdoV,OAAO,EAAEA,OAAO;UAChBK,MAAM,EAAE,CAAC,CAAC;UACVC,MAAM,EAAE,EAAE;UACVR,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ;MAEA,OAAO;QACLC,MAAM,EAAEA,MAAM;QACdC,OAAO,EAAEA,OAAO;QAChBC,eAAe,EAAEA,eAAe;QAChCC,gBAAgB,EAAEA;MACpB,CAAC;IACH;EACF,CAAC,EAAE;IACDvX,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASyX,MAAMA,CAACnP,IAAI,EAAEqP,GAAG,EAAE9S,IAAI,EAAE;MACtC,IAAI1B,CAAC,GAAGmF,IAAI,CAACzG,KAAK,CAAC,GAAG,CAAC;MACvB,IAAIqF,GAAG,GAAG/D,CAAC,CAAC,CAAC,CAAC;MACd,IAAIuD,EAAE,GAAGvD,CAAC,CAAC,CAAC,CAAC;MACb,IAAIwU,GAAG,EAAE,IAAI,CAACvV,IAAI,CAAC,eAAe,EAAE8E,GAAG,EAAER,EAAE,EAAEiR,GAAG,CAAC;MAEjD,IAAI9S,IAAI,EAAE;QACR,IAAI,CAACgS,KAAK,CAACpP,iBAAiB,CAACP,GAAG,EAAER,EAAE,EAAE7B,IAAI,CAAC;MAC7C;MAEA,IAAI,CAACiS,KAAK,CAACxO,IAAI,CAAC,GAAGqP,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;MAC/B,IAAIF,MAAM,GAAG,CAAC,CAAC;MACf,IAAI,CAACV,KAAK,CAACjV,OAAO,CAAC,UAAU8V,CAAC,EAAE;QAC9BpT,QAAQ,CAACoT,CAAC,CAACH,MAAM,EAAE,CAACvQ,GAAG,CAAC,EAAER,EAAE,CAAC;QAC7B8P,MAAM,CAACoB,CAAC,CAACR,OAAO,EAAE9O,IAAI,CAAC;QACvB,IAAIqP,GAAG,EAAEC,CAAC,CAACF,MAAM,CAAC1V,IAAI,CAAC2V,GAAG,CAAC;QAE3B,IAAIC,CAAC,CAACR,OAAO,CAACxX,MAAM,KAAK,CAAC,IAAI,CAACgY,CAAC,CAACC,IAAI,EAAE;UACrC9T,MAAM,CAAC4F,IAAI,CAACiO,CAAC,CAACH,MAAM,CAAC,CAAC3V,OAAO,CAAC,UAAUK,CAAC,EAAE;YACzC,IAAI,CAACsV,MAAM,CAACtV,CAAC,CAAC,EAAEsV,MAAM,CAACtV,CAAC,CAAC,GAAG,EAAE;YAE9B,IAAIyV,CAAC,CAACH,MAAM,CAACtV,CAAC,CAAC,CAACvC,MAAM,EAAE;cACtBgY,CAAC,CAACH,MAAM,CAACtV,CAAC,CAAC,CAACL,OAAO,CAAC,UAAU4E,EAAE,EAAE;gBAChC,IAAI+Q,MAAM,CAACtV,CAAC,CAAC,CAACuB,OAAO,CAACgD,EAAE,CAAC,GAAG,CAAC,EAAE+Q,MAAM,CAACtV,CAAC,CAAC,CAACH,IAAI,CAAC0E,EAAE,CAAC;cACnD,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;UACFkR,CAAC,CAACC,IAAI,GAAG,IAAI;UAEb,IAAID,CAAC,CAACF,MAAM,CAAC9X,MAAM,EAAE;YACnBgY,CAAC,CAACV,QAAQ,CAACU,CAAC,CAACF,MAAM,CAAC;UACtB,CAAC,MAAM;YACLE,CAAC,CAACV,QAAQ,CAAC,CAAC;UACd;QACF;MACF,CAAC,CAAC;MACF,IAAI,CAAC9U,IAAI,CAAC,QAAQ,EAAEqV,MAAM,CAAC;MAC3B,IAAI,CAACV,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC7U,MAAM,CAAC,UAAU0V,CAAC,EAAE;QAC1C,OAAO,CAACA,CAAC,CAACC,IAAI;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9X,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAAS8X,IAAIA,CAAC5Q,GAAG,EAAER,EAAE,EAAEqR,MAAM,EAAE;MACpC,IAAI7K,MAAM,GAAG,IAAI;MAEjB,IAAI8K,KAAK,GAAGrY,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MACjF,IAAIsY,IAAI,GAAGtY,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;MAClF,IAAIuX,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;MAC9D,IAAI,CAACqH,GAAG,CAACtH,MAAM,EAAE,OAAOsX,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;MAC1C,OAAO,IAAI,CAACN,OAAO,CAACmB,MAAM,CAAC,CAAC7Q,GAAG,EAAER,EAAE,EAAE,UAAUiR,GAAG,EAAE9S,IAAI,EAAE;QACxD,IAAI8S,GAAG,IAAI9S,IAAI,IAAImT,KAAK,GAAG,CAAC,EAAE;UAC5BE,UAAU,CAAC,YAAY;YACrBhL,MAAM,CAAC4K,IAAI,CAAC5T,IAAI,CAACgJ,MAAM,EAAEhG,GAAG,EAAER,EAAE,EAAEqR,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEf,QAAQ,CAAC;UAC1E,CAAC,EAAEe,IAAI,CAAC;UACR;QACF;QAEAf,QAAQ,CAACS,GAAG,EAAE9S,IAAI,CAAC;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAASmY,cAAcA,CAAClB,SAAS,EAAE5N,UAAU,EAAE;MACpD,IAAIiF,MAAM,GAAG,IAAI;MAEjB,IAAI5O,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIuX,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;MAE9D,IAAI,CAAC,IAAI,CAAC+W,OAAO,EAAE;QACjB,IAAI,CAAC1W,MAAM,CAACd,IAAI,CAAC,gEAAgE,CAAC;QAClF,OAAO8X,QAAQ,IAAIA,QAAQ,CAAC,CAAC;MAC/B;MAEA,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAEA,SAAS,GAAG,IAAI,CAAC9K,aAAa,CAACI,kBAAkB,CAAC0K,SAAS,CAAC;MAC/F,IAAI,OAAO5N,UAAU,KAAK,QAAQ,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAC7D,IAAI8N,MAAM,GAAG,IAAI,CAACH,SAAS,CAACC,SAAS,EAAE5N,UAAU,EAAE3J,OAAO,EAAEwX,QAAQ,CAAC;MAErE,IAAI,CAACC,MAAM,CAACA,MAAM,CAACvX,MAAM,EAAE;QACzB,IAAI,CAACuX,MAAM,CAACC,OAAO,CAACxX,MAAM,EAAEsX,QAAQ,CAAC,CAAC;QACtC,OAAO,IAAI;MACb;MAEAC,MAAM,CAACA,MAAM,CAACrV,OAAO,CAAC,UAAUwG,IAAI,EAAE;QACpCgG,MAAM,CAAC8J,OAAO,CAAC9P,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDvI,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAAS4Q,IAAIA,CAACqG,SAAS,EAAE5N,UAAU,EAAE6N,QAAQ,EAAE;MACpD,IAAI,CAACiB,cAAc,CAAClB,SAAS,EAAE5N,UAAU,EAAE,CAAC,CAAC,EAAE6N,QAAQ,CAAC;IAC1D;EACF,CAAC,EAAE;IACDnX,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASwX,MAAMA,CAACP,SAAS,EAAE5N,UAAU,EAAE6N,QAAQ,EAAE;MACtD,IAAI,CAACiB,cAAc,CAAClB,SAAS,EAAE5N,UAAU,EAAE;QACzCmO,MAAM,EAAE;MACV,CAAC,EAAEN,QAAQ,CAAC;IACd;EACF,CAAC,EAAE;IACDnX,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAASoY,OAAOA,CAAC9P,IAAI,EAAE;MAC5B,IAAI+P,MAAM,GAAG,IAAI;MAEjB,IAAIpY,MAAM,GAAGN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACnF,IAAIwD,CAAC,GAAGmF,IAAI,CAACzG,KAAK,CAAC,GAAG,CAAC;MACvB,IAAIqF,GAAG,GAAG/D,CAAC,CAAC,CAAC,CAAC;MACd,IAAIuD,EAAE,GAAGvD,CAAC,CAAC,CAAC,CAAC;MACb,IAAI,CAAC2U,IAAI,CAAC5Q,GAAG,EAAER,EAAE,EAAE,MAAM,EAAE7G,SAAS,EAAEA,SAAS,EAAE,UAAU8X,GAAG,EAAE9S,IAAI,EAAE;QACpE,IAAI8S,GAAG,EAAEU,MAAM,CAACnY,MAAM,CAACd,IAAI,CAAC,EAAE,CAAC+B,MAAM,CAAClB,MAAM,EAAE,oBAAoB,CAAC,CAACkB,MAAM,CAACuF,EAAE,EAAE,gBAAgB,CAAC,CAACvF,MAAM,CAAC+F,GAAG,EAAE,SAAS,CAAC,EAAEyQ,GAAG,CAAC;QAC7H,IAAI,CAACA,GAAG,IAAI9S,IAAI,EAAEwT,MAAM,CAACnY,MAAM,CAACjB,GAAG,CAAC,EAAE,CAACkC,MAAM,CAAClB,MAAM,EAAE,mBAAmB,CAAC,CAACkB,MAAM,CAACuF,EAAE,EAAE,gBAAgB,CAAC,CAACvF,MAAM,CAAC+F,GAAG,CAAC,EAAErC,IAAI,CAAC;QAE1HwT,MAAM,CAACZ,MAAM,CAACnP,IAAI,EAAEqP,GAAG,EAAE9S,IAAI,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAAS4M,WAAWA,CAACqK,SAAS,EAAEhN,SAAS,EAAElK,GAAG,EAAE0M,aAAa,EAAE6L,QAAQ,EAAE;MAC9E,IAAI5Y,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAEpF,IAAI,IAAI,CAACkJ,QAAQ,CAAC+F,KAAK,IAAI,IAAI,CAAC/F,QAAQ,CAAC+F,KAAK,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAAChG,QAAQ,CAAC+F,KAAK,CAACC,kBAAkB,CAAC5E,SAAS,CAAC,EAAE;QACvH,IAAI,CAAC/J,MAAM,CAACd,IAAI,CAAC,qBAAqB,CAAC+B,MAAM,CAACpB,GAAG,EAAE,wBAAwB,CAAC,CAACoB,MAAM,CAAC8I,SAAS,EAAE,uBAAuB,CAAC,EAAE,0NAA0N,CAAC;QACpV;MACF;MAEA,IAAIlK,GAAG,KAAKF,SAAS,IAAIE,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,EAAE;MAErD,IAAI,IAAI,CAAC6W,OAAO,IAAI,IAAI,CAACA,OAAO,CAACxV,MAAM,EAAE;QACvC,IAAI,CAACwV,OAAO,CAACxV,MAAM,CAAC6V,SAAS,EAAEhN,SAAS,EAAElK,GAAG,EAAE0M,aAAa,EAAE,IAAI,EAAEjO,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,EAAE;UAC7F4Y,QAAQ,EAAEA;QACZ,CAAC,CAAC,CAAC;MACL;MAEA,IAAI,CAACrB,SAAS,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EAAE;MACjC,IAAI,CAACJ,KAAK,CAACzP,WAAW,CAAC6P,SAAS,CAAC,CAAC,CAAC,EAAEhN,SAAS,EAAElK,GAAG,EAAE0M,aAAa,CAAC;IACrE;EACF,CAAC,CAAC,CAAC;EAEH,OAAOkK,SAAS;AAClB,CAAC,CAACpV,YAAY,CAAC;AAEf,SAASgX,GAAGA,CAAA,EAAG;EACb,OAAO;IACLpY,KAAK,EAAE,KAAK;IACZqY,aAAa,EAAE,IAAI;IACnB9R,EAAE,EAAE,CAAC,aAAa,CAAC;IACnBC,SAAS,EAAE,CAAC,aAAa,CAAC;IAC1B0F,WAAW,EAAE,CAAC,KAAK,CAAC;IACpBoC,UAAU,EAAE,KAAK;IACjBuB,SAAS,EAAE,KAAK;IAChByI,oBAAoB,EAAE,KAAK;IAC3BxI,aAAa,EAAE,KAAK;IACpBY,wBAAwB,EAAE,KAAK;IAC/BD,IAAI,EAAE,KAAK;IACX8H,OAAO,EAAE,KAAK;IACd3F,oBAAoB,EAAE,IAAI;IAC1BjN,YAAY,EAAE,GAAG;IACjBsD,WAAW,EAAE,GAAG;IAChBuP,eAAe,EAAE,GAAG;IACpBxJ,gBAAgB,EAAE,GAAG;IACrByJ,uBAAuB,EAAE,KAAK;IAC9BhM,WAAW,EAAE,KAAK;IAClBb,aAAa,EAAE,KAAK;IACpBO,aAAa,EAAE,UAAU;IACzBO,kBAAkB,EAAE,IAAI;IACxBH,iBAAiB,EAAE,KAAK;IACxBwI,2BAA2B,EAAE,KAAK;IAClCjH,WAAW,EAAE,KAAK;IAClBG,uBAAuB,EAAE,KAAK;IAC9BkB,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvBzE,aAAa,EAAE,KAAK;IACpBJ,UAAU,EAAE,KAAK;IACjBK,qBAAqB,EAAE,KAAK;IAC5BkC,sBAAsB,EAAE,KAAK;IAC7BD,2BAA2B,EAAE,KAAK;IAClC9C,uBAAuB,EAAE,KAAK;IAC9BJ,gCAAgC,EAAE,SAASvB,MAAMA,CAACrJ,IAAI,EAAE;MACtD,IAAI2Z,GAAG,GAAG,CAAC,CAAC;MACZ,IAAIta,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE2Z,GAAG,GAAG3Z,IAAI,CAAC,CAAC,CAAC;MAChD,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE2Z,GAAG,CAAClN,YAAY,GAAGzM,IAAI,CAAC,CAAC,CAAC;MAC3D,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE2Z,GAAG,CAACC,YAAY,GAAG5Z,IAAI,CAAC,CAAC,CAAC;MAE3D,IAAIX,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIX,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAClE,IAAIQ,OAAO,GAAGR,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC;QAChC6E,MAAM,CAAC4F,IAAI,CAACjK,OAAO,CAAC,CAACoC,OAAO,CAAC,UAAU/B,GAAG,EAAE;UAC1C8Y,GAAG,CAAC9Y,GAAG,CAAC,GAAGL,OAAO,CAACK,GAAG,CAAC;QACzB,CAAC,CAAC;MACJ;MAEA,OAAO8Y,GAAG;IACZ,CAAC;IACD5P,aAAa,EAAE;MACboK,WAAW,EAAE,IAAI;MACjBD,MAAM,EAAE,SAASA,MAAMA,CAACpT,KAAK,EAAE+Y,OAAO,EAAE7R,GAAG,EAAExH,OAAO,EAAE;QACpD,OAAOM,KAAK;MACd,CAAC;MACDC,MAAM,EAAE,IAAI;MACZ8M,MAAM,EAAE,IAAI;MACZ2G,eAAe,EAAE,GAAG;MACpBC,cAAc,EAAE,GAAG;MACnBE,aAAa,EAAE,KAAK;MACpBE,aAAa,EAAE,GAAG;MAClBE,uBAAuB,EAAE,GAAG;MAC5BC,WAAW,EAAE,IAAI;MACjB3G,eAAe,EAAE;IACnB;EACF,CAAC;AACH;AACA,SAASyL,gBAAgBA,CAACtZ,OAAO,EAAE;EACjC,IAAI,OAAOA,OAAO,CAACgH,EAAE,KAAK,QAAQ,EAAEhH,OAAO,CAACgH,EAAE,GAAG,CAAChH,OAAO,CAACgH,EAAE,CAAC;EAC7D,IAAI,OAAOhH,OAAO,CAAC2M,WAAW,KAAK,QAAQ,EAAE3M,OAAO,CAAC2M,WAAW,GAAG,CAAC3M,OAAO,CAAC2M,WAAW,CAAC;EACxF,IAAI,OAAO3M,OAAO,CAAC+O,UAAU,KAAK,QAAQ,EAAE/O,OAAO,CAAC+O,UAAU,GAAG,CAAC/O,OAAO,CAAC+O,UAAU,CAAC;EAErF,IAAI/O,OAAO,CAACsQ,SAAS,EAAE;IACrB,IAAItQ,OAAO,CAACsQ,SAAS,IAAItQ,OAAO,CAACsQ,SAAS,CAACtM,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAChEhE,OAAO,CAACsQ,SAAS,GAAGtQ,OAAO,CAACsQ,SAAS,CAAC7O,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC1D;IAEAzB,OAAO,CAACuQ,aAAa,GAAGvQ,OAAO,CAACsQ,SAAS;EAC3C;EAEA,IAAItQ,OAAO,CAAC+Y,oBAAoB,EAAE;IAChC/Y,OAAO,CAACmR,wBAAwB,GAAGnR,OAAO,CAAC+Y,oBAAoB;EACjE;EAEA,IAAI/Y,OAAO,CAACuQ,aAAa,IAAIvQ,OAAO,CAACuQ,aAAa,CAACvM,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACxEhE,OAAO,CAACuQ,aAAa,GAAGvQ,OAAO,CAACuQ,aAAa,CAAC9O,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClE;EAEA,OAAOzB,OAAO;AAChB;AAEA,SAASuZ,IAAIA,CAAA,EAAG,CAAC;AAEjB,IAAIC,IAAI,GAAG,UAAUzS,aAAa,EAAE;EAClC3H,SAAS,CAACoa,IAAI,EAAEzS,aAAa,CAAC;EAE9B,SAASyS,IAAIA,CAAA,EAAG;IACd,IAAItX,KAAK;IAET,IAAIlC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIuX,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;IAE9DpB,eAAe,CAAC,IAAI,EAAEya,IAAI,CAAC;IAE3BtX,KAAK,GAAGjD,0BAA0B,CAAC,IAAI,EAAEC,eAAe,CAACsa,IAAI,CAAC,CAAChV,IAAI,CAAC,IAAI,CAAC,CAAC;IAE1E,IAAIuB,MAAM,EAAE;MACVlE,YAAY,CAAC2C,IAAI,CAACrF,sBAAsB,CAAC+C,KAAK,CAAC,CAAC;IAClD;IAEAA,KAAK,CAAClC,OAAO,GAAGsZ,gBAAgB,CAACtZ,OAAO,CAAC;IACzCkC,KAAK,CAACiH,QAAQ,GAAG,CAAC,CAAC;IACnBjH,KAAK,CAAC1B,MAAM,GAAGoB,UAAU;IACzBM,KAAK,CAACuX,OAAO,GAAG;MACdC,QAAQ,EAAE;IACZ,CAAC;IAED,IAAIlC,QAAQ,IAAI,CAACtV,KAAK,CAACyX,aAAa,IAAI,CAAC3Z,OAAO,CAAC4Z,OAAO,EAAE;MACxD,IAAI,CAAC1X,KAAK,CAAClC,OAAO,CAAC8Y,aAAa,EAAE;QAChC5W,KAAK,CAAC9B,IAAI,CAACJ,OAAO,EAAEwX,QAAQ,CAAC;QAE7B,OAAOvY,0BAA0B,CAACiD,KAAK,EAAE/C,sBAAsB,CAAC+C,KAAK,CAAC,CAAC;MACzE;MAEAsW,UAAU,CAAC,YAAY;QACrBtW,KAAK,CAAC9B,IAAI,CAACJ,OAAO,EAAEwX,QAAQ,CAAC;MAC/B,CAAC,EAAE,CAAC,CAAC;IACP;IAEA,OAAOtV,KAAK;EACd;EAEAlD,YAAY,CAACwa,IAAI,EAAE,CAAC;IAClBnZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASF,IAAIA,CAAA,EAAG;MACrB,IAAI+J,MAAM,GAAG,IAAI;MAEjB,IAAInK,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIuX,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;MAE9D,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;QACjCwX,QAAQ,GAAGxX,OAAO;QAClBA,OAAO,GAAG,CAAC,CAAC;MACd;MAEA,IAAIA,OAAO,CAACsQ,SAAS,IAAI,CAACtQ,OAAO,CAACuQ,aAAa,EAAE;QAC/C,IAAI,CAAC/P,MAAM,CAACY,SAAS,CAAC,WAAW,EAAE,wHAAwH,CAAC;MAC9J;MAEA,IAAIpB,OAAO,CAAC+Y,oBAAoB,IAAI,CAAC/Y,OAAO,CAACmR,wBAAwB,EAAE;QACrE,IAAI,CAAC3Q,MAAM,CAACY,SAAS,CAAC,WAAW,EAAE,+IAA+I,CAAC;MACrL;MAEA,IAAI,CAACpB,OAAO,GAAGlB,aAAa,CAAC,CAAC,CAAC,EAAE+Z,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7Y,OAAO,EAAEsZ,gBAAgB,CAACtZ,OAAO,CAAC,CAAC;MAChF,IAAI,CAAC0T,MAAM,GAAG,IAAI,CAAC1T,OAAO,CAACuJ,aAAa,CAACmK,MAAM;MAC/C,IAAI,CAAC8D,QAAQ,EAAEA,QAAQ,GAAG+B,IAAI;MAE9B,SAASM,mBAAmBA,CAACC,aAAa,EAAE;QAC1C,IAAI,CAACA,aAAa,EAAE,OAAO,IAAI;QAC/B,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE,OAAO,IAAIA,aAAa,CAAC,CAAC;QACnE,OAAOA,aAAa;MACtB;MAEA,IAAI,CAAC,IAAI,CAAC9Z,OAAO,CAAC4Z,OAAO,EAAE;QACzB,IAAI,IAAI,CAACH,OAAO,CAACjZ,MAAM,EAAE;UACvBoB,UAAU,CAACxB,IAAI,CAACyZ,mBAAmB,CAAC,IAAI,CAACJ,OAAO,CAACjZ,MAAM,CAAC,EAAE,IAAI,CAACR,OAAO,CAAC;QACzE,CAAC,MAAM;UACL4B,UAAU,CAACxB,IAAI,CAAC,IAAI,EAAE,IAAI,CAACJ,OAAO,CAAC;QACrC;QAEA,IAAI+Z,EAAE,GAAG,IAAI1J,YAAY,CAAC,IAAI,CAACrQ,OAAO,CAAC;QACvC,IAAI,CAACmX,KAAK,GAAG,IAAIrQ,aAAa,CAAC,IAAI,CAAC9G,OAAO,CAAC6H,SAAS,EAAE,IAAI,CAAC7H,OAAO,CAAC;QACpE,IAAIyD,CAAC,GAAG,IAAI,CAAC0F,QAAQ;QACrB1F,CAAC,CAACjD,MAAM,GAAGoB,UAAU;QACrB6B,CAAC,CAACqM,aAAa,GAAG,IAAI,CAACqH,KAAK;QAC5B1T,CAAC,CAACgJ,aAAa,GAAGsN,EAAE;QACpBtW,CAAC,CAACsI,cAAc,GAAG,IAAI2G,cAAc,CAACqH,EAAE,EAAE;UACxCxG,OAAO,EAAE,IAAI,CAACvT,OAAO,CAACiZ,eAAe;UACrCzF,iBAAiB,EAAE,IAAI,CAACxT,OAAO,CAACwT,iBAAiB;UACjDH,oBAAoB,EAAE,IAAI,CAACrT,OAAO,CAACqT;QACrC,CAAC,CAAC;QACF5P,CAAC,CAACoG,YAAY,GAAG,IAAI4J,YAAY,CAAC,IAAI,CAACzT,OAAO,CAAC;QAC/CyD,CAAC,CAACyL,KAAK,GAAG;UACRC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAAC6K,IAAI,CAAC,IAAI;QACvD,CAAC;QACDvW,CAAC,CAACwJ,gBAAgB,GAAG,IAAIgK,SAAS,CAAC4C,mBAAmB,CAAC,IAAI,CAACJ,OAAO,CAACvC,OAAO,CAAC,EAAEzT,CAAC,CAACqM,aAAa,EAAErM,CAAC,EAAE,IAAI,CAACzD,OAAO,CAAC;QAC/GyD,CAAC,CAACwJ,gBAAgB,CAAClL,EAAE,CAAC,GAAG,EAAE,UAAUM,KAAK,EAAE;UAC1C,KAAK,IAAIzB,IAAI,GAAGX,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;YAC1GtB,IAAI,CAACsB,IAAI,GAAG,CAAC,CAAC,GAAGb,SAAS,CAACa,IAAI,CAAC;UAClC;UAEAqJ,MAAM,CAACzH,IAAI,CAAC7C,KAAK,CAACsK,MAAM,EAAE,CAAC9H,KAAK,CAAC,CAACZ,MAAM,CAACjC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC;QAEF,IAAI,IAAI,CAACia,OAAO,CAACQ,gBAAgB,EAAE;UACjCxW,CAAC,CAACwW,gBAAgB,GAAGJ,mBAAmB,CAAC,IAAI,CAACJ,OAAO,CAACQ,gBAAgB,CAAC;UACvExW,CAAC,CAACwW,gBAAgB,CAAC7Z,IAAI,CAACqD,CAAC,EAAE,IAAI,CAACzD,OAAO,CAACka,SAAS,EAAE,IAAI,CAACla,OAAO,CAAC;QAClE;QAEA,IAAI,IAAI,CAACyZ,OAAO,CAACvO,UAAU,EAAE;UAC3BzH,CAAC,CAACyH,UAAU,GAAG2O,mBAAmB,CAAC,IAAI,CAACJ,OAAO,CAACvO,UAAU,CAAC;UAC3D,IAAIzH,CAAC,CAACyH,UAAU,CAAC9K,IAAI,EAAEqD,CAAC,CAACyH,UAAU,CAAC9K,IAAI,CAAC,IAAI,CAAC;QAChD;QAEA,IAAI,CAAC0I,UAAU,GAAG,IAAII,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACnJ,OAAO,CAAC;QAC7D,IAAI,CAAC8I,UAAU,CAAC/G,EAAE,CAAC,GAAG,EAAE,UAAUM,KAAK,EAAE;UACvC,KAAK,IAAIrB,KAAK,GAAGf,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACG,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;YACjHzB,IAAI,CAACyB,KAAK,GAAG,CAAC,CAAC,GAAGhB,SAAS,CAACgB,KAAK,CAAC;UACpC;UAEAkJ,MAAM,CAACzH,IAAI,CAAC7C,KAAK,CAACsK,MAAM,EAAE,CAAC9H,KAAK,CAAC,CAACZ,MAAM,CAACjC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC;QACF,IAAI,CAACia,OAAO,CAACC,QAAQ,CAACtX,OAAO,CAAC,UAAUuB,CAAC,EAAE;UACzC,IAAIA,CAAC,CAACvD,IAAI,EAAEuD,CAAC,CAACvD,IAAI,CAAC+J,MAAM,CAAC;QAC5B,CAAC,CAAC;MACJ;MAEA,IAAI,IAAI,CAACnK,OAAO,CAAC2M,WAAW,IAAI,CAAC,IAAI,CAACxD,QAAQ,CAAC8Q,gBAAgB,IAAI,CAAC,IAAI,CAACja,OAAO,CAACwH,GAAG,EAAE;QACpF,IAAIyH,KAAK,GAAG,IAAI,CAAC9F,QAAQ,CAACsD,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAAC1M,OAAO,CAAC2M,WAAW,CAAC;QAClF,IAAIsC,KAAK,CAAC/O,MAAM,GAAG,CAAC,IAAI+O,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,IAAI,CAACjP,OAAO,CAACwH,GAAG,GAAGyH,KAAK,CAAC,CAAC,CAAC;MACzE;MAEA,IAAI,CAAC,IAAI,CAAC9F,QAAQ,CAAC8Q,gBAAgB,IAAI,CAAC,IAAI,CAACja,OAAO,CAACwH,GAAG,EAAE;QACxD,IAAI,CAAChH,MAAM,CAACd,IAAI,CAAC,yDAAyD,CAAC;MAC7E;MAEA,IAAIya,QAAQ,GAAG,CAAC,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;MAC7FA,QAAQ,CAAC/X,OAAO,CAAC,UAAUiW,MAAM,EAAE;QACjClO,MAAM,CAACkO,MAAM,CAAC,GAAG,YAAY;UAC3B,IAAI+B,YAAY;UAEhB,OAAO,CAACA,YAAY,GAAGjQ,MAAM,CAACgN,KAAK,EAAEkB,MAAM,CAAC,CAACxY,KAAK,CAACua,YAAY,EAAEna,SAAS,CAAC;QAC7E,CAAC;MACH,CAAC,CAAC;MACF,IAAIoa,eAAe,GAAG,CAAC,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;MAClGA,eAAe,CAACjY,OAAO,CAAC,UAAUiW,MAAM,EAAE;QACxClO,MAAM,CAACkO,MAAM,CAAC,GAAG,YAAY;UAC3B,IAAIiC,aAAa;UAEjB,CAACA,aAAa,GAAGnQ,MAAM,CAACgN,KAAK,EAAEkB,MAAM,CAAC,CAACxY,KAAK,CAACya,aAAa,EAAEra,SAAS,CAAC;UAEtE,OAAOkK,MAAM;QACf,CAAC;MACH,CAAC,CAAC;MACF,IAAIoQ,QAAQ,GAAGzX,KAAK,CAAC,CAAC;MAEtB,IAAIoO,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;QACzB,IAAIsJ,MAAM,GAAG,SAASA,MAAMA,CAACvC,GAAG,EAAEvU,CAAC,EAAE;UACnC,IAAIyG,MAAM,CAACwP,aAAa,IAAI,CAACxP,MAAM,CAACsQ,oBAAoB,EAAEtQ,MAAM,CAAC3J,MAAM,CAACd,IAAI,CAAC,uEAAuE,CAAC;UACrJyK,MAAM,CAACwP,aAAa,GAAG,IAAI;UAC3B,IAAI,CAACxP,MAAM,CAACnK,OAAO,CAAC4Z,OAAO,EAAEzP,MAAM,CAAC3J,MAAM,CAACjB,GAAG,CAAC,aAAa,EAAE4K,MAAM,CAACnK,OAAO,CAAC;UAE7EmK,MAAM,CAACzH,IAAI,CAAC,aAAa,EAAEyH,MAAM,CAACnK,OAAO,CAAC;UAE1Cua,QAAQ,CAACpX,OAAO,CAACO,CAAC,CAAC;UACnB8T,QAAQ,CAACS,GAAG,EAAEvU,CAAC,CAAC;QAClB,CAAC;QAED,IAAIyG,MAAM,CAACoN,SAAS,IAAIpN,MAAM,CAACnK,OAAO,CAACqI,gBAAgB,KAAK,IAAI,IAAI,CAAC8B,MAAM,CAACwP,aAAa,EAAE,OAAOa,MAAM,CAAC,IAAI,EAAErQ,MAAM,CAACzG,CAAC,CAACsW,IAAI,CAAC7P,MAAM,CAAC,CAAC;QAErIA,MAAM,CAACf,cAAc,CAACe,MAAM,CAACnK,OAAO,CAACwH,GAAG,EAAEgT,MAAM,CAAC;MACnD,CAAC;MAED,IAAI,IAAI,CAACxa,OAAO,CAAC6H,SAAS,IAAI,CAAC,IAAI,CAAC7H,OAAO,CAAC8Y,aAAa,EAAE;QACzD5H,IAAI,CAAC,CAAC;MACR,CAAC,MAAM;QACLsH,UAAU,CAACtH,IAAI,EAAE,CAAC,CAAC;MACrB;MAEA,OAAOqJ,QAAQ;IACjB;EACF,CAAC,EAAE;IACDla,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAASoa,aAAaA,CAACrR,QAAQ,EAAE;MACtC,IAAImE,MAAM,GAAG,IAAI;MAEjB,IAAIgK,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGsZ,IAAI;MACvF,IAAIoB,YAAY,GAAGnD,QAAQ;MAC3B,IAAI9J,OAAO,GAAG,OAAOrE,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACrE,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAEsR,YAAY,GAAGtR,QAAQ;MAE3D,IAAI,CAAC,IAAI,CAACrJ,OAAO,CAAC6H,SAAS,IAAI,IAAI,CAAC7H,OAAO,CAACkZ,uBAAuB,EAAE;QACnE,IAAIxL,OAAO,IAAIA,OAAO,CAACjD,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAOkQ,YAAY,CAAC,CAAC;QACxE,IAAIlD,MAAM,GAAG,EAAE;QAEf,IAAImD,MAAM,GAAG,SAASA,MAAMA,CAACpT,GAAG,EAAE;UAChC,IAAI,CAACA,GAAG,EAAE;UAEV,IAAI+E,IAAI,GAAGiB,MAAM,CAACrE,QAAQ,CAACsD,aAAa,CAACI,kBAAkB,CAACrF,GAAG,CAAC;UAEhE+E,IAAI,CAACnK,OAAO,CAAC,UAAUK,CAAC,EAAE;YACxB,IAAIgV,MAAM,CAACzT,OAAO,CAACvB,CAAC,CAAC,GAAG,CAAC,EAAEgV,MAAM,CAACnV,IAAI,CAACG,CAAC,CAAC;UAC3C,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,CAACiL,OAAO,EAAE;UACZ,IAAI+D,SAAS,GAAG,IAAI,CAACtI,QAAQ,CAACsD,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAAC1M,OAAO,CAAC2M,WAAW,CAAC;UACtF8E,SAAS,CAACrP,OAAO,CAAC,UAAUK,CAAC,EAAE;YAC7B,OAAOmY,MAAM,CAACnY,CAAC,CAAC;UAClB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLmY,MAAM,CAAClN,OAAO,CAAC;QACjB;QAEA,IAAI,IAAI,CAAC1N,OAAO,CAACgZ,OAAO,EAAE;UACxB,IAAI,CAAChZ,OAAO,CAACgZ,OAAO,CAAC5W,OAAO,CAAC,UAAUK,CAAC,EAAE;YACxC,OAAOmY,MAAM,CAACnY,CAAC,CAAC;UAClB,CAAC,CAAC;QACJ;QAEA,IAAI,CAAC0G,QAAQ,CAAC8D,gBAAgB,CAACiE,IAAI,CAACuG,MAAM,EAAE,IAAI,CAACzX,OAAO,CAACgH,EAAE,EAAE2T,YAAY,CAAC;MAC5E,CAAC,MAAM;QACLA,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDta,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAASua,eAAeA,CAACtO,IAAI,EAAEvF,EAAE,EAAEwQ,QAAQ,EAAE;MAClD,IAAI+C,QAAQ,GAAGzX,KAAK,CAAC,CAAC;MACtB,IAAI,CAACyJ,IAAI,EAAEA,IAAI,GAAG,IAAI,CAACgL,SAAS;MAChC,IAAI,CAACvQ,EAAE,EAAEA,EAAE,GAAG,IAAI,CAAChH,OAAO,CAACgH,EAAE;MAC7B,IAAI,CAACwQ,QAAQ,EAAEA,QAAQ,GAAG+B,IAAI;MAC9B,IAAI,CAACpQ,QAAQ,CAAC8D,gBAAgB,CAAC6K,MAAM,CAACvL,IAAI,EAAEvF,EAAE,EAAE,UAAUiR,GAAG,EAAE;QAC7DsC,QAAQ,CAACpX,OAAO,CAAC,CAAC;QAClBqU,QAAQ,CAACS,GAAG,CAAC;MACf,CAAC,CAAC;MACF,OAAOsC,QAAQ;IACjB;EACF,CAAC,EAAE;IACDla,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASwa,GAAGA,CAACnS,MAAM,EAAE;MAC1B,IAAI,CAACA,MAAM,EAAE,MAAM,IAAIoS,KAAK,CAAC,+FAA+F,CAAC;MAC7H,IAAI,CAACpS,MAAM,CAACrJ,IAAI,EAAE,MAAM,IAAIyb,KAAK,CAAC,0FAA0F,CAAC;MAE7H,IAAIpS,MAAM,CAACrJ,IAAI,KAAK,SAAS,EAAE;QAC7B,IAAI,CAACma,OAAO,CAACvC,OAAO,GAAGvO,MAAM;MAC/B;MAEA,IAAIA,MAAM,CAACrJ,IAAI,KAAK,QAAQ,IAAIqJ,MAAM,CAACpJ,GAAG,IAAIoJ,MAAM,CAACjJ,IAAI,IAAIiJ,MAAM,CAAChJ,KAAK,EAAE;QACzE,IAAI,CAAC8Z,OAAO,CAACjZ,MAAM,GAAGmI,MAAM;MAC9B;MAEA,IAAIA,MAAM,CAACrJ,IAAI,KAAK,kBAAkB,EAAE;QACtC,IAAI,CAACma,OAAO,CAACQ,gBAAgB,GAAGtR,MAAM;MACxC;MAEA,IAAIA,MAAM,CAACrJ,IAAI,KAAK,YAAY,EAAE;QAChC,IAAI,CAACma,OAAO,CAACvO,UAAU,GAAGvC,MAAM;MAClC;MAEA,IAAIA,MAAM,CAACrJ,IAAI,KAAK,eAAe,EAAE;QACnCkJ,aAAa,CAACE,gBAAgB,CAACC,MAAM,CAAC;MACxC;MAEA,IAAIA,MAAM,CAACrJ,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAI,CAACma,OAAO,CAACC,QAAQ,CAACpX,IAAI,CAACqG,MAAM,CAAC;MACpC;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDtI,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAAS8I,cAAcA,CAAC5B,GAAG,EAAEgQ,QAAQ,EAAE;MAC5C,IAAI5I,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACoM,oBAAoB,GAAGxT,GAAG;MAC/B,IAAI+S,QAAQ,GAAGzX,KAAK,CAAC,CAAC;MACtB,IAAI,CAACJ,IAAI,CAAC,kBAAkB,EAAE8E,GAAG,CAAC;MAElC,IAAI2Q,IAAI,GAAG,SAASA,IAAIA,CAACF,GAAG,EAAExV,CAAC,EAAE;QAC/B,IAAIA,CAAC,EAAE;UACLmM,MAAM,CAACvF,QAAQ,GAAG5G,CAAC;UACnBmM,MAAM,CAAC2I,SAAS,GAAG3I,MAAM,CAACzF,QAAQ,CAACsD,aAAa,CAACI,kBAAkB,CAACpK,CAAC,CAAC;UAEtEmM,MAAM,CAAC9F,UAAU,CAACM,cAAc,CAAC3G,CAAC,CAAC;UAEnCmM,MAAM,CAACoM,oBAAoB,GAAG7a,SAAS;UAEvCyO,MAAM,CAAClM,IAAI,CAAC,iBAAiB,EAAED,CAAC,CAAC;UAEjCmM,MAAM,CAACpO,MAAM,CAACjB,GAAG,CAAC,iBAAiB,EAAEkD,CAAC,CAAC;QACzC,CAAC,MAAM;UACLmM,MAAM,CAACoM,oBAAoB,GAAG7a,SAAS;QACzC;QAEAoa,QAAQ,CAACpX,OAAO,CAAC,YAAY;UAC3B,OAAOyL,MAAM,CAAClL,CAAC,CAAC7D,KAAK,CAAC+O,MAAM,EAAE3O,SAAS,CAAC;QAC1C,CAAC,CAAC;QACF,IAAIuX,QAAQ,EAAEA,QAAQ,CAACS,GAAG,EAAE,YAAY;UACtC,OAAOrJ,MAAM,CAAClL,CAAC,CAAC7D,KAAK,CAAC+O,MAAM,EAAE3O,SAAS,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MAED,IAAIgb,MAAM,GAAG,SAASA,MAAMA,CAAC1O,IAAI,EAAE;QACjC,IAAI,CAAC/E,GAAG,IAAI,CAAC+E,IAAI,IAAIqC,MAAM,CAACzF,QAAQ,CAAC8Q,gBAAgB,EAAE1N,IAAI,GAAG,EAAE;QAChE,IAAI9J,CAAC,GAAG,OAAO8J,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGqC,MAAM,CAACzF,QAAQ,CAACsD,aAAa,CAAC2E,qBAAqB,CAAC7E,IAAI,CAAC;QAEnG,IAAI9J,CAAC,EAAE;UACL,IAAI,CAACmM,MAAM,CAACvF,QAAQ,EAAE;YACpBuF,MAAM,CAACvF,QAAQ,GAAG5G,CAAC;YACnBmM,MAAM,CAAC2I,SAAS,GAAG3I,MAAM,CAACzF,QAAQ,CAACsD,aAAa,CAACI,kBAAkB,CAACpK,CAAC,CAAC;UACxE;UAEA,IAAI,CAACmM,MAAM,CAAC9F,UAAU,CAACO,QAAQ,EAAEuF,MAAM,CAAC9F,UAAU,CAACM,cAAc,CAAC3G,CAAC,CAAC;UACpE,IAAImM,MAAM,CAACzF,QAAQ,CAAC8Q,gBAAgB,EAAErL,MAAM,CAACzF,QAAQ,CAAC8Q,gBAAgB,CAACiB,iBAAiB,CAACzY,CAAC,CAAC;QAC7F;QAEAmM,MAAM,CAAC8L,aAAa,CAACjY,CAAC,EAAE,UAAUwV,GAAG,EAAE;UACrCE,IAAI,CAACF,GAAG,EAAExV,CAAC,CAAC;QACd,CAAC,CAAC;MACJ,CAAC;MAED,IAAI,CAAC+E,GAAG,IAAI,IAAI,CAAC2B,QAAQ,CAAC8Q,gBAAgB,IAAI,CAAC,IAAI,CAAC9Q,QAAQ,CAAC8Q,gBAAgB,CAACkB,KAAK,EAAE;QACnFF,MAAM,CAAC,IAAI,CAAC9R,QAAQ,CAAC8Q,gBAAgB,CAACmB,MAAM,CAAC,CAAC,CAAC;MACjD,CAAC,MAAM,IAAI,CAAC5T,GAAG,IAAI,IAAI,CAAC2B,QAAQ,CAAC8Q,gBAAgB,IAAI,IAAI,CAAC9Q,QAAQ,CAAC8Q,gBAAgB,CAACkB,KAAK,EAAE;QACzF,IAAI,CAAChS,QAAQ,CAAC8Q,gBAAgB,CAACmB,MAAM,CAACH,MAAM,CAAC;MAC/C,CAAC,MAAM;QACLA,MAAM,CAACzT,GAAG,CAAC;MACb;MAEA,OAAO+S,QAAQ;IACjB;EACF,CAAC,EAAE;IACDla,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAAS+a,SAASA,CAAC7T,GAAG,EAAER,EAAE,EAAEsU,SAAS,EAAE;MAC5C,IAAI3C,MAAM,GAAG,IAAI;MAEjB,IAAI4C,MAAM,GAAG,SAASA,MAAMA,CAAClb,GAAG,EAAEmb,IAAI,EAAE;QACtC,IAAIxb,OAAO;QAEX,IAAInB,OAAO,CAAC2c,IAAI,CAAC,KAAK,QAAQ,EAAE;UAC9B,KAAK,IAAIta,KAAK,GAAGjB,SAAS,CAACC,MAAM,EAAEub,IAAI,GAAG,IAAI5a,KAAK,CAACK,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;YACjHsa,IAAI,CAACta,KAAK,GAAG,CAAC,CAAC,GAAGlB,SAAS,CAACkB,KAAK,CAAC;UACpC;UAEAnB,OAAO,GAAG2Y,MAAM,CAAC3Y,OAAO,CAACoK,gCAAgC,CAAC,CAAC/J,GAAG,EAAEmb,IAAI,CAAC,CAAC/Z,MAAM,CAACga,IAAI,CAAC,CAAC;QACrF,CAAC,MAAM;UACLzb,OAAO,GAAGlB,aAAa,CAAC,CAAC,CAAC,EAAE0c,IAAI,CAAC;QACnC;QAEAxb,OAAO,CAACwH,GAAG,GAAGxH,OAAO,CAACwH,GAAG,IAAI+T,MAAM,CAAC/T,GAAG;QACvCxH,OAAO,CAACuM,IAAI,GAAGvM,OAAO,CAACuM,IAAI,IAAIgP,MAAM,CAAChP,IAAI;QAC1CvM,OAAO,CAACgH,EAAE,GAAGhH,OAAO,CAACgH,EAAE,IAAIuU,MAAM,CAACvU,EAAE;QACpC,IAAIZ,YAAY,GAAGuS,MAAM,CAAC3Y,OAAO,CAACoG,YAAY,IAAI,GAAG;QACrD,IAAIsV,SAAS,GAAGJ,SAAS,GAAG,EAAE,CAAC7Z,MAAM,CAAC6Z,SAAS,CAAC,CAAC7Z,MAAM,CAAC2E,YAAY,CAAC,CAAC3E,MAAM,CAACpB,GAAG,CAAC,GAAGA,GAAG;QACvF,OAAOsY,MAAM,CAACjV,CAAC,CAACgY,SAAS,EAAE1b,OAAO,CAAC;MACrC,CAAC;MAED,IAAI,OAAOwH,GAAG,KAAK,QAAQ,EAAE;QAC3B+T,MAAM,CAAC/T,GAAG,GAAGA,GAAG;MAClB,CAAC,MAAM;QACL+T,MAAM,CAAChP,IAAI,GAAG/E,GAAG;MACnB;MAEA+T,MAAM,CAACvU,EAAE,GAAGA,EAAE;MACduU,MAAM,CAACD,SAAS,GAAGA,SAAS;MAC5B,OAAOC,MAAM;IACf;EACF,CAAC,EAAE;IACDlb,GAAG,EAAE,GAAG;IACRC,KAAK,EAAE,SAASoD,CAACA,CAAA,EAAG;MAClB,IAAIiY,gBAAgB;MAEpB,OAAO,IAAI,CAAC7S,UAAU,IAAI,CAAC6S,gBAAgB,GAAG,IAAI,CAAC7S,UAAU,EAAEkB,SAAS,CAACnK,KAAK,CAAC8b,gBAAgB,EAAE1b,SAAS,CAAC;IAC7G;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASgJ,MAAMA,CAAA,EAAG;MACvB,IAAIsS,iBAAiB;MAErB,OAAO,IAAI,CAAC9S,UAAU,IAAI,CAAC8S,iBAAiB,GAAG,IAAI,CAAC9S,UAAU,EAAEQ,MAAM,CAACzJ,KAAK,CAAC+b,iBAAiB,EAAE3b,SAAS,CAAC;IAC5G;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,SAASub,mBAAmBA,CAAC7U,EAAE,EAAE;MACtC,IAAI,CAAChH,OAAO,CAACiH,SAAS,GAAGD,EAAE;IAC7B;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAAS6O,kBAAkBA,CAACnI,EAAE,EAAE;MACrC,IAAI8U,MAAM,GAAG,IAAI;MAEjB,IAAI9b,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAEpF,IAAI,CAAC,IAAI,CAAC0Z,aAAa,EAAE;QACvB,IAAI,CAACnZ,MAAM,CAACd,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC6X,SAAS,CAAC;QACnF,OAAO,KAAK;MACd;MAEA,IAAI,CAAC,IAAI,CAACA,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACrX,MAAM,EAAE;QAC7C,IAAI,CAACM,MAAM,CAACd,IAAI,CAAC,4DAA4D,EAAE,IAAI,CAAC6X,SAAS,CAAC;QAC9F,OAAO,KAAK;MACd;MAEA,IAAI/P,GAAG,GAAG,IAAI,CAAC+P,SAAS,CAAC,CAAC,CAAC;MAC3B,IAAI5K,WAAW,GAAG,IAAI,CAAC3M,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC2M,WAAW,GAAG,KAAK;MACjE,IAAIoP,OAAO,GAAG,IAAI,CAACxE,SAAS,CAAC,IAAI,CAACA,SAAS,CAACrX,MAAM,GAAG,CAAC,CAAC;MACvD,IAAIsH,GAAG,CAACiD,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAI;MAE/C,IAAIuR,cAAc,GAAG,SAASA,cAAcA,CAACvZ,CAAC,EAAE0P,CAAC,EAAE;QACjD,IAAI8J,SAAS,GAAGH,MAAM,CAAC3S,QAAQ,CAAC8D,gBAAgB,CAACmK,KAAK,CAAC,EAAE,CAAC3V,MAAM,CAACgB,CAAC,EAAE,GAAG,CAAC,CAAChB,MAAM,CAAC0Q,CAAC,CAAC,CAAC;QAEnF,OAAO8J,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAK,CAAC;MAC5C,CAAC;MAED,IAAIjc,OAAO,CAACkc,QAAQ,EAAE;QACpB,IAAIC,SAAS,GAAGnc,OAAO,CAACkc,QAAQ,CAAC,IAAI,EAAEF,cAAc,CAAC;QACtD,IAAIG,SAAS,KAAKhc,SAAS,EAAE,OAAOgc,SAAS;MAC/C;MAEA,IAAI,IAAI,CAAChU,iBAAiB,CAACX,GAAG,EAAER,EAAE,CAAC,EAAE,OAAO,IAAI;MAChD,IAAI,CAAC,IAAI,CAACmC,QAAQ,CAAC8D,gBAAgB,CAACiK,OAAO,EAAE,OAAO,IAAI;MACxD,IAAI8E,cAAc,CAACxU,GAAG,EAAER,EAAE,CAAC,KAAK,CAAC2F,WAAW,IAAIqP,cAAc,CAACD,OAAO,EAAE/U,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;MACzF,OAAO,KAAK;IACd;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAAS8b,cAAcA,CAACpV,EAAE,EAAEwQ,QAAQ,EAAE;MAC3C,IAAI6E,MAAM,GAAG,IAAI;MAEjB,IAAI9B,QAAQ,GAAGzX,KAAK,CAAC,CAAC;MAEtB,IAAI,CAAC,IAAI,CAAC9C,OAAO,CAACgH,EAAE,EAAE;QACpBwQ,QAAQ,IAAIA,QAAQ,CAAC,CAAC;QACtB,OAAOtU,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B;MAEA,IAAI,OAAO6D,EAAE,KAAK,QAAQ,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC;MACrCA,EAAE,CAAC5E,OAAO,CAAC,UAAU+P,CAAC,EAAE;QACtB,IAAIkK,MAAM,CAACrc,OAAO,CAACgH,EAAE,CAAChD,OAAO,CAACmO,CAAC,CAAC,GAAG,CAAC,EAAEkK,MAAM,CAACrc,OAAO,CAACgH,EAAE,CAAC1E,IAAI,CAAC6P,CAAC,CAAC;MACjE,CAAC,CAAC;MACF,IAAI,CAACuI,aAAa,CAAC,UAAUzC,GAAG,EAAE;QAChCsC,QAAQ,CAACpX,OAAO,CAAC,CAAC;QAClB,IAAIqU,QAAQ,EAAEA,QAAQ,CAACS,GAAG,CAAC;MAC7B,CAAC,CAAC;MACF,OAAOsC,QAAQ;IACjB;EACF,CAAC,EAAE;IACDla,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAASgc,aAAaA,CAAC/P,IAAI,EAAEiL,QAAQ,EAAE;MAC5C,IAAI+C,QAAQ,GAAGzX,KAAK,CAAC,CAAC;MACtB,IAAI,OAAOyJ,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;MAC3C,IAAIgQ,SAAS,GAAG,IAAI,CAACvc,OAAO,CAACgZ,OAAO,IAAI,EAAE;MAC1C,IAAIwD,OAAO,GAAGjQ,IAAI,CAAC/J,MAAM,CAAC,UAAUgF,GAAG,EAAE;QACvC,OAAO+U,SAAS,CAACvY,OAAO,CAACwD,GAAG,CAAC,GAAG,CAAC;MACnC,CAAC,CAAC;MAEF,IAAI,CAACgV,OAAO,CAACtc,MAAM,EAAE;QACnB,IAAIsX,QAAQ,EAAEA,QAAQ,CAAC,CAAC;QACxB,OAAOtU,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B;MAEA,IAAI,CAACnD,OAAO,CAACgZ,OAAO,GAAGuD,SAAS,CAAC9a,MAAM,CAAC+a,OAAO,CAAC;MAChD,IAAI,CAAC9B,aAAa,CAAC,UAAUzC,GAAG,EAAE;QAChCsC,QAAQ,CAACpX,OAAO,CAAC,CAAC;QAClB,IAAIqU,QAAQ,EAAEA,QAAQ,CAACS,GAAG,CAAC;MAC7B,CAAC,CAAC;MACF,OAAOsC,QAAQ;IACjB;EACF,CAAC,EAAE;IACDla,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASmc,GAAGA,CAACjV,GAAG,EAAE;MACvB,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAG,IAAI,CAAC+P,SAAS,IAAI,IAAI,CAACA,SAAS,CAACrX,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqX,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAClO,QAAQ;MAC/F,IAAI,CAAC7B,GAAG,EAAE,OAAO,KAAK;MACtB,IAAIkV,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;MAC/a,OAAOA,OAAO,CAAC1Y,OAAO,CAAC,IAAI,CAACmF,QAAQ,CAACsD,aAAa,CAACiE,uBAAuB,CAAClJ,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK;IACvG;EACF,CAAC,EAAE;IACDnH,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,SAASqc,cAAcA,CAAA,EAAG;MAC/B,IAAI3c,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIuX,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;MAC9D,OAAO,IAAIqZ,IAAI,CAACxZ,OAAO,EAAEwX,QAAQ,CAAC;IACpC;EACF,CAAC,EAAE;IACDnX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAASsc,aAAaA,CAAA,EAAG;MAC9B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI7c,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIuX,QAAQ,GAAGvX,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGsZ,IAAI;MAEvF,IAAIuD,aAAa,GAAGhe,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkB,OAAO,EAAEA,OAAO,EAAE;QAC3D4Z,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,IAAImD,KAAK,GAAG,IAAIvD,IAAI,CAACsD,aAAa,CAAC;MACnC,IAAIE,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;MACrDA,aAAa,CAAC5a,OAAO,CAAC,UAAUuB,CAAC,EAAE;QACjCoZ,KAAK,CAACpZ,CAAC,CAAC,GAAGkZ,MAAM,CAAClZ,CAAC,CAAC;MACtB,CAAC,CAAC;MACFoZ,KAAK,CAAC5T,QAAQ,GAAGrK,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqK,QAAQ,CAAC;MACjD4T,KAAK,CAAC5T,QAAQ,CAAC+F,KAAK,GAAG;QACrBC,kBAAkB,EAAE4N,KAAK,CAAC5N,kBAAkB,CAAC6K,IAAI,CAAC+C,KAAK;MACzD,CAAC;MACDA,KAAK,CAACjU,UAAU,GAAG,IAAII,UAAU,CAAC6T,KAAK,CAAC5T,QAAQ,EAAE4T,KAAK,CAAC/c,OAAO,CAAC;MAChE+c,KAAK,CAACjU,UAAU,CAAC/G,EAAE,CAAC,GAAG,EAAE,UAAUM,KAAK,EAAE;QACxC,KAAK,IAAIhB,KAAK,GAAGpB,SAAS,CAACC,MAAM,EAAEV,IAAI,GAAG,IAAIqB,KAAK,CAACQ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjH9B,IAAI,CAAC8B,KAAK,GAAG,CAAC,CAAC,GAAGrB,SAAS,CAACqB,KAAK,CAAC;QACpC;QAEAyb,KAAK,CAACra,IAAI,CAAC7C,KAAK,CAACkd,KAAK,EAAE,CAAC1a,KAAK,CAAC,CAACZ,MAAM,CAACjC,IAAI,CAAC,CAAC;MAC/C,CAAC,CAAC;MACFud,KAAK,CAAC3c,IAAI,CAAC0c,aAAa,EAAEtF,QAAQ,CAAC;MACnCuF,KAAK,CAACjU,UAAU,CAAC9I,OAAO,GAAG+c,KAAK,CAAC/c,OAAO;MACxC+c,KAAK,CAACjU,UAAU,CAACmE,gBAAgB,CAAC9D,QAAQ,CAAC+F,KAAK,GAAG;QACjDC,kBAAkB,EAAE4N,KAAK,CAAC5N,kBAAkB,CAAC6K,IAAI,CAAC+C,KAAK;MACzD,CAAC;MACD,OAAOA,KAAK;IACd;EACF,CAAC,EAAE;IACD1c,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASiI,MAAMA,CAAA,EAAG;MACvB,OAAO;QACLvI,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBmX,KAAK,EAAE,IAAI,CAACA,KAAK;QACjB9N,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBkO,SAAS,EAAE,IAAI,CAACA;MAClB,CAAC;IACH;EACF,CAAC,CAAC,CAAC;EAEH,OAAOiC,IAAI;AACb,CAAC,CAAC3X,YAAY,CAAC;AAEf,IAAIob,OAAO,GAAG,IAAIzD,IAAI,CAAC,CAAC;AAExB,eAAeyD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}