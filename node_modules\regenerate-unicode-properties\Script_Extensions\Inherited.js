const set = require('regenerate')(0x30F, 0x312, 0x32F, 0x1DF9, 0x101FD);
set.addRange(0x314, 0x31F).addRange(0x321, 0x322).addRange(0x326, 0x32C).addRange(0x332, 0x341).addRange(0x343, 0x344).addRange(0x346, 0x357).addRange(0x359, 0x35D).addRange(0x35F, 0x362).addRange(0x953, 0x954).addRange(0x1AB0, 0x1ACE).addRange(0x1DC2, 0x1DF7).addRange(0x1DFB, 0x1DFF).addRange(0x200C, 0x200D).addRange(0x20D0, 0x20EF).addRange(0xFE00, 0xFE0F).addRange(0xFE20, 0xFE2D).addRange(0x1CF00, 0x1CF2D).addRange(0x1CF30, 0x1CF46).addRange(0x1D167, 0x1D169).addRange(0x1D17B, 0x1D182).addRange(0x1D185, 0x1D18B).addRange(0x1D1AA, 0x1D1AD).addRange(0xE0100, 0xE01EF);
exports.characters = set;
