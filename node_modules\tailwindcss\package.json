{"name": "tailwindcss", "version": "3.0.24", "description": "A utility-first CSS framework for rapidly building custom user interfaces.", "license": "MIT", "main": "lib/index.js", "repository": "https://github.com/tailwindlabs/tailwindcss.git", "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "scripts": {"preswcify": "npm run generate && rimraf lib", "swcify": "swc src --out-dir lib --copy-files", "postswcify": "esbuild lib/cli-peer-dependencies.js --bundle --platform=node --outfile=peers/index.js", "rebuild-fixtures": "npm run swcify && node -r @swc/register scripts/rebuildFixtures.js", "prepublishOnly": "npm install --force && npm run swcify", "style": "eslint .", "pretest": "npm run generate", "test": "jest", "test:integrations": "npm run test --prefix ./integrations", "install:integrations": "node scripts/install-integrations.js", "posttest": "npm run style", "generate:plugin-list": "node -r @swc/register scripts/create-plugin-list.js", "generate:types": "node -r @swc/register scripts/generate-types.js", "generate": "npm run generate:plugin-list"}, "files": ["src/*", "cli/*", "lib/*", "peers/*", "scripts/*.js", "stubs/*.stub.js", "nesting/*", "*.css", "*.js"], "devDependencies": {"@swc/cli": "^0.1.56", "@swc/core": "^1.2.160", "@swc/jest": "^0.2.20", "@swc/register": "^0.1.10", "autoprefixer": "^10.4.4", "cssnano": "^5.1.7", "esbuild": "^0.14.25", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.5.1", "jest-diff": "^27.5.1", "prettier": "^2.6.0", "prettier-plugin-tailwindcss": "^0.1.8", "rimraf": "^3.0.0", "source-map-js": "^1.0.2"}, "peerDependencies": {"postcss": "^8.0.9"}, "dependencies": {"arg": "^5.0.1", "chokidar": "^3.5.3", "color-name": "^1.1.4", "detective": "^5.2.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.2.11", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "lilconfig": "^2.0.5", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.12", "postcss-js": "^4.0.0", "postcss-load-config": "^3.1.4", "postcss-nested": "5.0.6", "postcss-selector-parser": "^6.0.10", "postcss-value-parser": "^4.2.0", "quick-lru": "^5.1.1", "resolve": "^1.22.0"}, "browserslist": ["> 1%", "not edge <= 18", "not ie 11", "not op_mini all"], "jest": {"testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/jest/customMatchers.js"], "testPathIgnorePatterns": ["/node_modules/", "/integrations/", "/standalone-cli/"], "transform": {"\\.js$": "@swc/jest"}}, "engines": {"node": ">=12.13.0"}}