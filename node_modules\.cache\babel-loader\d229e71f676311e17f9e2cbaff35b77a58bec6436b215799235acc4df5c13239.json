{"ast": null, "code": "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n  return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n  return !!value && typeof value === 'object';\n}\nfunction isSpecial(value) {\n  var stringValue = Object.prototype.toString.call(value);\n  return stringValue === '[object RegExp]' || stringValue === '[object Date]' || isReactElement(value);\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\nfunction isReactElement(value) {\n  return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n  return Array.isArray(val) ? [] : {};\n}\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n  return options.clone !== false && options.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options) : value;\n}\nfunction defaultArrayMerge(target, source, options) {\n  return target.concat(source).map(function (element) {\n    return cloneUnlessOtherwiseSpecified(element, options);\n  });\n}\nfunction getMergeFunction(key, options) {\n  if (!options.customMerge) {\n    return deepmerge;\n  }\n  var customMerge = options.customMerge(key);\n  return typeof customMerge === 'function' ? customMerge : deepmerge;\n}\nfunction getEnumerableOwnPropertySymbols(target) {\n  return Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(target).filter(function (symbol) {\n    return target.propertyIsEnumerable(symbol);\n  }) : [];\n}\nfunction getKeys(target) {\n  return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target));\n}\nfunction propertyIsOnObject(object, property) {\n  try {\n    return property in object;\n  } catch (_) {\n    return false;\n  }\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n  return propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n  && !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n  && Object.propertyIsEnumerable.call(target, key)); // and also unsafe if they're nonenumerable.\n}\nfunction mergeObject(target, source, options) {\n  var destination = {};\n  if (options.isMergeableObject(target)) {\n    getKeys(target).forEach(function (key) {\n      destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n    });\n  }\n  getKeys(source).forEach(function (key) {\n    if (propertyIsUnsafe(target, key)) {\n      return;\n    }\n    if (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n      destination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n    } else {\n      destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n    }\n  });\n  return destination;\n}\nfunction deepmerge(target, source, options) {\n  options = options || {};\n  options.arrayMerge = options.arrayMerge || defaultArrayMerge;\n  options.isMergeableObject = options.isMergeableObject || isMergeableObject;\n  // cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n  // implementations can use it. The caller may not replace it.\n  options.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n  var sourceIsArray = Array.isArray(source);\n  var targetIsArray = Array.isArray(target);\n  var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n  if (!sourceAndTargetTypesMatch) {\n    return cloneUnlessOtherwiseSpecified(source, options);\n  } else if (sourceIsArray) {\n    return options.arrayMerge(target, source, options);\n  } else {\n    return mergeObject(target, source, options);\n  }\n}\ndeepmerge.all = function deepmergeAll(array, options) {\n  if (!Array.isArray(array)) {\n    throw new Error('first argument should be an array');\n  }\n  return array.reduce(function (prev, next) {\n    return deepmerge(prev, next, options);\n  }, {});\n};\nvar deepmerge_1 = deepmerge;\nmodule.exports = deepmerge_1;", "map": {"version": 3, "names": ["isMergeableObject", "value", "isNonNullObject", "isSpecial", "stringValue", "Object", "prototype", "toString", "call", "isReactElement", "canUseSymbol", "Symbol", "for", "REACT_ELEMENT_TYPE", "$$typeof", "emptyTarget", "val", "Array", "isArray", "cloneUnlessOtherwiseSpecified", "options", "clone", "deepmerge", "defaultArrayMerge", "target", "source", "concat", "map", "element", "getMergeFunction", "key", "customMerge", "getEnumerableOwnPropertySymbols", "getOwnPropertySymbols", "filter", "symbol", "propertyIsEnumerable", "get<PERSON><PERSON><PERSON>", "keys", "propertyIsOnObject", "object", "property", "_", "propertyIsUnsafe", "hasOwnProperty", "mergeObject", "destination", "for<PERSON>ach", "arrayMerge", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "all", "deepmergeAll", "array", "Error", "reduce", "prev", "next", "deepmerge_1", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/deepmerge/dist/cjs.js"], "sourcesContent": ["'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn target.propertyIsEnumerable(symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EACzD,OAAOC,eAAe,CAACD,KAAK,CAAC,IACzB,CAACE,SAAS,CAACF,KAAK,CAAC;AACtB,CAAC;AAED,SAASC,eAAeA,CAACD,KAAK,EAAE;EAC/B,OAAO,CAAC,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAC5C;AAEA,SAASE,SAASA,CAACF,KAAK,EAAE;EACzB,IAAIG,WAAW,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;EAEvD,OAAOG,WAAW,KAAK,iBAAiB,IACpCA,WAAW,KAAK,eAAe,IAC/BK,cAAc,CAACR,KAAK,CAAC;AAC1B;;AAEA;AACA,IAAIS,YAAY,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG;AAC7D,IAAIC,kBAAkB,GAAGH,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM;AAE5E,SAASH,cAAcA,CAACR,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACa,QAAQ,KAAKD,kBAAkB;AAC7C;AAEA,SAASE,WAAWA,CAACC,GAAG,EAAE;EACzB,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpC;AAEA,SAASG,6BAA6BA,CAAClB,KAAK,EAAEmB,OAAO,EAAE;EACtD,OAAQA,OAAO,CAACC,KAAK,KAAK,KAAK,IAAID,OAAO,CAACpB,iBAAiB,CAACC,KAAK,CAAC,GAChEqB,SAAS,CAACP,WAAW,CAACd,KAAK,CAAC,EAAEA,KAAK,EAAEmB,OAAO,CAAC,GAC7CnB,KAAK;AACT;AAEA,SAASsB,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EACnD,OAAOI,MAAM,CAACE,MAAM,CAACD,MAAM,CAAC,CAACE,GAAG,CAAC,UAASC,OAAO,EAAE;IAClD,OAAOT,6BAA6B,CAACS,OAAO,EAAER,OAAO,CAAC;EACvD,CAAC,CAAC;AACH;AAEA,SAASS,gBAAgBA,CAACC,GAAG,EAAEV,OAAO,EAAE;EACvC,IAAI,CAACA,OAAO,CAACW,WAAW,EAAE;IACzB,OAAOT,SAAS;EACjB;EACA,IAAIS,WAAW,GAAGX,OAAO,CAACW,WAAW,CAACD,GAAG,CAAC;EAC1C,OAAO,OAAOC,WAAW,KAAK,UAAU,GAAGA,WAAW,GAAGT,SAAS;AACnE;AAEA,SAASU,+BAA+BA,CAACR,MAAM,EAAE;EAChD,OAAOnB,MAAM,CAAC4B,qBAAqB,GAChC5B,MAAM,CAAC4B,qBAAqB,CAACT,MAAM,CAAC,CAACU,MAAM,CAAC,UAASC,MAAM,EAAE;IAC9D,OAAOX,MAAM,CAACY,oBAAoB,CAACD,MAAM,CAAC;EAC3C,CAAC,CAAC,GACA,EAAE;AACN;AAEA,SAASE,OAAOA,CAACb,MAAM,EAAE;EACxB,OAAOnB,MAAM,CAACiC,IAAI,CAACd,MAAM,CAAC,CAACE,MAAM,CAACM,+BAA+B,CAACR,MAAM,CAAC,CAAC;AAC3E;AAEA,SAASe,kBAAkBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC7C,IAAI;IACH,OAAOA,QAAQ,IAAID,MAAM;EAC1B,CAAC,CAAC,OAAME,CAAC,EAAE;IACV,OAAO,KAAK;EACb;AACD;;AAEA;AACA,SAASC,gBAAgBA,CAACnB,MAAM,EAAEM,GAAG,EAAE;EACtC,OAAOS,kBAAkB,CAACf,MAAM,EAAEM,GAAG,CAAC,CAAC;EAAA,GACnC,EAAEzB,MAAM,CAACuC,cAAc,CAACpC,IAAI,CAACgB,MAAM,EAAEM,GAAG,CAAC,CAAC;EAAA,GACzCzB,MAAM,CAAC+B,oBAAoB,CAAC5B,IAAI,CAACgB,MAAM,EAAEM,GAAG,CAAC,CAAC,EAAC;AACrD;AAEA,SAASe,WAAWA,CAACrB,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAC7C,IAAI0B,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI1B,OAAO,CAACpB,iBAAiB,CAACwB,MAAM,CAAC,EAAE;IACtCa,OAAO,CAACb,MAAM,CAAC,CAACuB,OAAO,CAAC,UAASjB,GAAG,EAAE;MACrCgB,WAAW,CAAChB,GAAG,CAAC,GAAGX,6BAA6B,CAACK,MAAM,CAACM,GAAG,CAAC,EAAEV,OAAO,CAAC;IACvE,CAAC,CAAC;EACH;EACAiB,OAAO,CAACZ,MAAM,CAAC,CAACsB,OAAO,CAAC,UAASjB,GAAG,EAAE;IACrC,IAAIa,gBAAgB,CAACnB,MAAM,EAAEM,GAAG,CAAC,EAAE;MAClC;IACD;IAEA,IAAIS,kBAAkB,CAACf,MAAM,EAAEM,GAAG,CAAC,IAAIV,OAAO,CAACpB,iBAAiB,CAACyB,MAAM,CAACK,GAAG,CAAC,CAAC,EAAE;MAC9EgB,WAAW,CAAChB,GAAG,CAAC,GAAGD,gBAAgB,CAACC,GAAG,EAAEV,OAAO,CAAC,CAACI,MAAM,CAACM,GAAG,CAAC,EAAEL,MAAM,CAACK,GAAG,CAAC,EAAEV,OAAO,CAAC;IACrF,CAAC,MAAM;MACN0B,WAAW,CAAChB,GAAG,CAAC,GAAGX,6BAA6B,CAACM,MAAM,CAACK,GAAG,CAAC,EAAEV,OAAO,CAAC;IACvE;EACD,CAAC,CAAC;EACF,OAAO0B,WAAW;AACnB;AAEA,SAASxB,SAASA,CAACE,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAC3CA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBA,OAAO,CAAC4B,UAAU,GAAG5B,OAAO,CAAC4B,UAAU,IAAIzB,iBAAiB;EAC5DH,OAAO,CAACpB,iBAAiB,GAAGoB,OAAO,CAACpB,iBAAiB,IAAIA,iBAAiB;EAC1E;EACA;EACAoB,OAAO,CAACD,6BAA6B,GAAGA,6BAA6B;EAErE,IAAI8B,aAAa,GAAGhC,KAAK,CAACC,OAAO,CAACO,MAAM,CAAC;EACzC,IAAIyB,aAAa,GAAGjC,KAAK,CAACC,OAAO,CAACM,MAAM,CAAC;EACzC,IAAI2B,yBAAyB,GAAGF,aAAa,KAAKC,aAAa;EAE/D,IAAI,CAACC,yBAAyB,EAAE;IAC/B,OAAOhC,6BAA6B,CAACM,MAAM,EAAEL,OAAO,CAAC;EACtD,CAAC,MAAM,IAAI6B,aAAa,EAAE;IACzB,OAAO7B,OAAO,CAAC4B,UAAU,CAACxB,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;EACnD,CAAC,MAAM;IACN,OAAOyB,WAAW,CAACrB,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;EAC5C;AACD;AAEAE,SAAS,CAAC8B,GAAG,GAAG,SAASC,YAAYA,CAACC,KAAK,EAAElC,OAAO,EAAE;EACrD,IAAI,CAACH,KAAK,CAACC,OAAO,CAACoC,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;EACrD;EAEA,OAAOD,KAAK,CAACE,MAAM,CAAC,UAASC,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAOpC,SAAS,CAACmC,IAAI,EAAEC,IAAI,EAAEtC,OAAO,CAAC;EACtC,CAAC,EAAE,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAIuC,WAAW,GAAGrC,SAAS;AAE3BsC,MAAM,CAACC,OAAO,GAAGF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}