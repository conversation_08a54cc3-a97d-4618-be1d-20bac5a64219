{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\footer\\\\joyride.jsx\";\nimport React from 'react';\nimport Joyride from 'react-joyride';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport class JoyrideGen extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      steps: []\n    };\n  }\n  componentDidMount() {\n    this.setState({\n      steps: [{\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [this.props.content, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 29\n          }, this), this.props.subContent]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 25\n        }, this),\n        disableBeacon: true,\n        /* disableOverlayClose: true,*/\n        hideCloseButton: true,\n        hideFooter: true,\n        placement: 'bottom',\n        spotlightClicks: false,\n        styles: {\n          options: {\n            zIndex: 10000\n          }\n        },\n        target: this.props.target,\n        title: this.props.title\n      }]\n    });\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      children: /*#__PURE__*/_jsxDEV(Joyride, {\n        scrollOffset: \"130\",\n        steps: this.state.steps\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["React", "Joyride", "jsxDEV", "_jsxDEV", "JoyrideGen", "Component", "constructor", "props", "state", "steps", "componentDidMount", "setState", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subContent", "disableBea<PERSON>", "hideClose<PERSON><PERSON>on", "hideFooter", "placement", "spotlightClicks", "styles", "options", "zIndex", "target", "title", "render", "className", "scrollOffset"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/footer/joyride.jsx"], "sourcesContent": ["import React from 'react';\nimport Joyride from 'react-joyride';\n\nexport class JoyrideGen extends React.Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            steps: []\n        }\n    }\n\n    componentDidMount() {\n        this.setState({\n            steps: [\n                {\n                    content: (\n                        <div>\n                            {this.props.content}\n                            <br />\n                            {this.props.subContent}\n                        </div>\n                    ),\n                    disableBeacon: true,\n                    /* disableOverlayClose: true,*/\n                    hideCloseButton: true,\n                    hideFooter: true,\n                    placement: 'bottom',\n                    spotlightClicks: false,\n                    styles: {\n                        options: {\n                            zIndex: 10000,\n                        },\n                    },\n                    target: this.props.target,\n                    title: this.props.title,\n                }\n            ]\n        })\n    }\n\n    render() {\n\n        return (\n            <div className=\"app\">\n                <Joyride\n                    scrollOffset='130'\n                    steps={this.state.steps}\n                />\n            </div>\n        );\n    }\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,OAAO,MAAMC,UAAU,SAASJ,KAAK,CAACK,SAAS,CAAC;EAC5CC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,KAAK,EAAE;IACX,CAAC;EACL;EAEAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,QAAQ,CAAC;MACVF,KAAK,EAAE,CACH;QACIG,OAAO,eACHT,OAAA;UAAAU,QAAA,GACK,IAAI,CAACN,KAAK,CAACK,OAAO,eACnBT,OAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL,IAAI,CAACV,KAAK,CAACW,UAAU;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR;QACDE,aAAa,EAAE,IAAI;QACnB;QACAC,eAAe,EAAE,IAAI;QACrBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,QAAQ;QACnBC,eAAe,EAAE,KAAK;QACtBC,MAAM,EAAE;UACJC,OAAO,EAAE;YACLC,MAAM,EAAE;UACZ;QACJ,CAAC;QACDC,MAAM,EAAE,IAAI,CAACpB,KAAK,CAACoB,MAAM;QACzBC,KAAK,EAAE,IAAI,CAACrB,KAAK,CAACqB;MACtB,CAAC;IAET,CAAC,CAAC;EACN;EAEAC,MAAMA,CAAA,EAAG;IAEL,oBACI1B,OAAA;MAAK2B,SAAS,EAAC,KAAK;MAAAjB,QAAA,eAChBV,OAAA,CAACF,OAAO;QACJ8B,YAAY,EAAC,KAAK;QAClBtB,KAAK,EAAE,IAAI,CAACD,KAAK,CAACC;MAAM;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}