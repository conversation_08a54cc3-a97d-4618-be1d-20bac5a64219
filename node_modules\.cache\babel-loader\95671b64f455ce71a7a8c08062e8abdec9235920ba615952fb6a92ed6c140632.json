{"ast": null, "code": "'use strict';\n\nvar pkg = require('./../../package.json');\nvar validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function (type, i) {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\nvar deprecatedWarnings = {};\nvar currentVerArr = pkg.version.split('.');\n\n/**\n * Compare package versions\n * @param {string} version\n * @param {string?} thanVersion\n * @returns {boolean}\n */\nfunction isOlderVersion(version, thanVersion) {\n  var pkgVersionArr = thanVersion ? thanVersion.split('.') : currentVerArr;\n  var destVer = version.split('.');\n  for (var i = 0; i < 3; i++) {\n    if (pkgVersionArr[i] > destVer[i]) {\n      return true;\n    } else if (pkgVersionArr[i] < destVer[i]) {\n      return false;\n    }\n  }\n  return false;\n}\n\n/**\n * Transitional option validator\n * @param {function|boolean?} validator\n * @param {string?} version\n * @param {string} message\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  var isDeprecated = version && isOlderVersion(version);\n  function formatMessage(opt, desc) {\n    return '[Axios v' + pkg.version + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return function (value, opt, opts) {\n    if (validator === false) {\n      throw new Error(formatMessage(opt, ' has been removed in ' + version));\n    }\n    if (isDeprecated && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(formatMessage(opt, ' has been deprecated since v' + version + ' and will be removed in the near future'));\n    }\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\n/**\n * Assert object's properties type\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new TypeError('options must be an object');\n  }\n  var keys = Object.keys(options);\n  var i = keys.length;\n  while (i-- > 0) {\n    var opt = keys[i];\n    var validator = schema[opt];\n    if (validator) {\n      var value = options[opt];\n      var result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new TypeError('option ' + opt + ' must be ' + result);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw Error('Unknown option ' + opt);\n    }\n  }\n}\nmodule.exports = {\n  isOlderVersion: isOlderVersion,\n  assertOptions: assertOptions,\n  validators: validators\n};", "map": {"version": 3, "names": ["pkg", "require", "validators", "for<PERSON>ach", "type", "i", "validator", "thing", "deprecatedWarnings", "currentVerArr", "version", "split", "isOlderVersion", "thanVersion", "pkgVersionArr", "destV<PERSON>", "transitional", "message", "isDeprecated", "formatMessage", "opt", "desc", "value", "opts", "Error", "console", "warn", "assertOptions", "options", "schema", "allowUnknown", "TypeError", "keys", "Object", "length", "result", "undefined", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/axios/lib/helpers/validator.js"], "sourcesContent": ["'use strict';\n\nvar pkg = require('./../../package.json');\n\nvar validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function(type, i) {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nvar deprecatedWarnings = {};\nvar currentVerArr = pkg.version.split('.');\n\n/**\n * Compare package versions\n * @param {string} version\n * @param {string?} thanVersion\n * @returns {boolean}\n */\nfunction isOlderVersion(version, thanVersion) {\n  var pkgVersionArr = thanVersion ? thanVersion.split('.') : currentVerArr;\n  var destVer = version.split('.');\n  for (var i = 0; i < 3; i++) {\n    if (pkgVersionArr[i] > destVer[i]) {\n      return true;\n    } else if (pkgVersionArr[i] < destVer[i]) {\n      return false;\n    }\n  }\n  return false;\n}\n\n/**\n * Transitional option validator\n * @param {function|boolean?} validator\n * @param {string?} version\n * @param {string} message\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  var isDeprecated = version && isOlderVersion(version);\n\n  function formatMessage(opt, desc) {\n    return '[Axios v' + pkg.version + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return function(value, opt, opts) {\n    if (validator === false) {\n      throw new Error(formatMessage(opt, ' has been removed in ' + version));\n    }\n\n    if (isDeprecated && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\n/**\n * Assert object's properties type\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new TypeError('options must be an object');\n  }\n  var keys = Object.keys(options);\n  var i = keys.length;\n  while (i-- > 0) {\n    var opt = keys[i];\n    var validator = schema[opt];\n    if (validator) {\n      var value = options[opt];\n      var result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new TypeError('option ' + opt + ' must be ' + result);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw Error('Unknown option ' + opt);\n    }\n  }\n}\n\nmodule.exports = {\n  isOlderVersion: isOlderVersion,\n  assertOptions: assertOptions,\n  validators: validators\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,GAAG,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAEzC,IAAIC,UAAU,GAAG,CAAC,CAAC;;AAEnB;AACA,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,OAAO,CAAC,UAASC,IAAI,EAAEC,CAAC,EAAE;EACxFH,UAAU,CAACE,IAAI,CAAC,GAAG,SAASE,SAASA,CAACC,KAAK,EAAE;IAC3C,OAAO,OAAOA,KAAK,KAAKH,IAAI,IAAI,GAAG,IAAIC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAGD,IAAI;EACnE,CAAC;AACH,CAAC,CAAC;AAEF,IAAII,kBAAkB,GAAG,CAAC,CAAC;AAC3B,IAAIC,aAAa,GAAGT,GAAG,CAACU,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACF,OAAO,EAAEG,WAAW,EAAE;EAC5C,IAAIC,aAAa,GAAGD,WAAW,GAAGA,WAAW,CAACF,KAAK,CAAC,GAAG,CAAC,GAAGF,aAAa;EACxE,IAAIM,OAAO,GAAGL,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC;EAChC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1B,IAAIS,aAAa,CAACT,CAAC,CAAC,GAAGU,OAAO,CAACV,CAAC,CAAC,EAAE;MACjC,OAAO,IAAI;IACb,CAAC,MAAM,IAAIS,aAAa,CAACT,CAAC,CAAC,GAAGU,OAAO,CAACV,CAAC,CAAC,EAAE;MACxC,OAAO,KAAK;IACd;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACAH,UAAU,CAACc,YAAY,GAAG,SAASA,YAAYA,CAACV,SAAS,EAAEI,OAAO,EAAEO,OAAO,EAAE;EAC3E,IAAIC,YAAY,GAAGR,OAAO,IAAIE,cAAc,CAACF,OAAO,CAAC;EAErD,SAASS,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAChC,OAAO,UAAU,GAAGrB,GAAG,CAACU,OAAO,GAAG,0BAA0B,GAAGU,GAAG,GAAG,IAAI,GAAGC,IAAI,IAAIJ,OAAO,GAAG,IAAI,GAAGA,OAAO,GAAG,EAAE,CAAC;EACpH;;EAEA;EACA,OAAO,UAASK,KAAK,EAAEF,GAAG,EAAEG,IAAI,EAAE;IAChC,IAAIjB,SAAS,KAAK,KAAK,EAAE;MACvB,MAAM,IAAIkB,KAAK,CAACL,aAAa,CAACC,GAAG,EAAE,uBAAuB,GAAGV,OAAO,CAAC,CAAC;IACxE;IAEA,IAAIQ,YAAY,IAAI,CAACV,kBAAkB,CAACY,GAAG,CAAC,EAAE;MAC5CZ,kBAAkB,CAACY,GAAG,CAAC,GAAG,IAAI;MAC9B;MACAK,OAAO,CAACC,IAAI,CACVP,aAAa,CACXC,GAAG,EACH,8BAA8B,GAAGV,OAAO,GAAG,yCAC7C,CACF,CAAC;IACH;IAEA,OAAOJ,SAAS,GAAGA,SAAS,CAACgB,KAAK,EAAEF,GAAG,EAAEG,IAAI,CAAC,GAAG,IAAI;EACvD,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAE;EACpD,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAIG,SAAS,CAAC,2BAA2B,CAAC;EAClD;EACA,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACJ,OAAO,CAAC;EAC/B,IAAIvB,CAAC,GAAG2B,IAAI,CAACE,MAAM;EACnB,OAAO7B,CAAC,EAAE,GAAG,CAAC,EAAE;IACd,IAAIe,GAAG,GAAGY,IAAI,CAAC3B,CAAC,CAAC;IACjB,IAAIC,SAAS,GAAGuB,MAAM,CAACT,GAAG,CAAC;IAC3B,IAAId,SAAS,EAAE;MACb,IAAIgB,KAAK,GAAGM,OAAO,CAACR,GAAG,CAAC;MACxB,IAAIe,MAAM,GAAGb,KAAK,KAAKc,SAAS,IAAI9B,SAAS,CAACgB,KAAK,EAAEF,GAAG,EAAEQ,OAAO,CAAC;MAClE,IAAIO,MAAM,KAAK,IAAI,EAAE;QACnB,MAAM,IAAIJ,SAAS,CAAC,SAAS,GAAGX,GAAG,GAAG,WAAW,GAAGe,MAAM,CAAC;MAC7D;MACA;IACF;IACA,IAAIL,YAAY,KAAK,IAAI,EAAE;MACzB,MAAMN,KAAK,CAAC,iBAAiB,GAAGJ,GAAG,CAAC;IACtC;EACF;AACF;AAEAiB,MAAM,CAACC,OAAO,GAAG;EACf1B,cAAc,EAAEA,cAAc;EAC9Be,aAAa,EAAEA,aAAa;EAC5BzB,UAAU,EAAEA;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}