{"ast": null, "code": "import { tuple } from './type'; // ================== Collapse Motion ==================\n\nvar getCollapsedHeight = function getCollapsedHeight() {\n  return {\n    height: 0,\n    opacity: 0\n  };\n};\nvar getRealHeight = function getRealHeight(node) {\n  var scrollHeight = node.scrollHeight;\n  return {\n    height: scrollHeight,\n    opacity: 1\n  };\n};\nvar getCurrentHeight = function getCurrentHeight(node) {\n  return {\n    height: node ? node.offsetHeight : 0\n  };\n};\nvar skipOpacityTransition = function skipOpacityTransition(_, event) {\n  return (event === null || event === void 0 ? void 0 : event.deadline) === true || event.propertyName === 'height';\n};\nvar collapseMotion = {\n  motionName: 'ant-motion-collapse',\n  onAppearStart: getCollapsedHeight,\n  onEnterStart: getCollapsedHeight,\n  onAppearActive: getRealHeight,\n  onEnterActive: getRealHeight,\n  onLeaveStart: getCurrentHeight,\n  onLeaveActive: getCollapsedHeight,\n  onAppearEnd: skipOpacityTransition,\n  onEnterEnd: skipOpacityTransition,\n  onLeaveEnd: skipOpacityTransition,\n  motionDeadline: 500\n};\nvar SelectPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\nvar getTransitionDirection = function getTransitionDirection(placement) {\n  if (placement !== undefined && (placement === 'topLeft' || placement === 'topRight')) {\n    return \"slide-down\";\n  }\n  return \"slide-up\";\n};\nvar getTransitionName = function getTransitionName(rootPrefixCls, motion, transitionName) {\n  if (transitionName !== undefined) {\n    return transitionName;\n  }\n  return \"\".concat(rootPrefixCls, \"-\").concat(motion);\n};\nexport { getTransitionName, getTransitionDirection };\nexport default collapseMotion;", "map": {"version": 3, "names": ["tuple", "getCollapsedHeight", "height", "opacity", "getRealHeight", "node", "scrollHeight", "getCurrentHeight", "offsetHeight", "skipOpacityTransition", "_", "event", "deadline", "propertyName", "collapseMotion", "motionName", "onAppearStart", "onEnterStart", "onAppearActive", "onEnterActive", "onLeaveStart", "onLeaveActive", "onAppearEnd", "onEnterEnd", "onLeaveEnd", "motionDeadline", "SelectPlacements", "getTransitionDirection", "placement", "undefined", "getTransitionName", "rootPrefixCls", "motion", "transitionName", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/motion.js"], "sourcesContent": ["import { tuple } from './type'; // ================== Collapse Motion ==================\n\nvar getCollapsedHeight = function getCollapsedHeight() {\n  return {\n    height: 0,\n    opacity: 0\n  };\n};\n\nvar getRealHeight = function getRealHeight(node) {\n  var scrollHeight = node.scrollHeight;\n  return {\n    height: scrollHeight,\n    opacity: 1\n  };\n};\n\nvar getCurrentHeight = function getCurrentHeight(node) {\n  return {\n    height: node ? node.offsetHeight : 0\n  };\n};\n\nvar skipOpacityTransition = function skipOpacityTransition(_, event) {\n  return (event === null || event === void 0 ? void 0 : event.deadline) === true || event.propertyName === 'height';\n};\n\nvar collapseMotion = {\n  motionName: 'ant-motion-collapse',\n  onAppearStart: getCollapsedHeight,\n  onEnterStart: getCollapsedHeight,\n  onAppearActive: getRealHeight,\n  onEnterActive: getRealHeight,\n  onLeaveStart: getCurrentHeight,\n  onLeaveActive: getCollapsedHeight,\n  onAppearEnd: skipOpacityTransition,\n  onEnterEnd: skipOpacityTransition,\n  onLeaveEnd: skipOpacityTransition,\n  motionDeadline: 500\n};\nvar SelectPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\n\nvar getTransitionDirection = function getTransitionDirection(placement) {\n  if (placement !== undefined && (placement === 'topLeft' || placement === 'topRight')) {\n    return \"slide-down\";\n  }\n\n  return \"slide-up\";\n};\n\nvar getTransitionName = function getTransitionName(rootPrefixCls, motion, transitionName) {\n  if (transitionName !== undefined) {\n    return transitionName;\n  }\n\n  return \"\".concat(rootPrefixCls, \"-\").concat(motion);\n};\n\nexport { getTransitionName, getTransitionDirection };\nexport default collapseMotion;"], "mappings": "AAAA,SAASA,KAAK,QAAQ,QAAQ,CAAC,CAAC;;AAEhC,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;EACrD,OAAO;IACLC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAED,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC/C,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;EACpC,OAAO;IACLJ,MAAM,EAAEI,YAAY;IACpBH,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAED,IAAII,gBAAgB,GAAG,SAASA,gBAAgBA,CAACF,IAAI,EAAE;EACrD,OAAO;IACLH,MAAM,EAAEG,IAAI,GAAGA,IAAI,CAACG,YAAY,GAAG;EACrC,CAAC;AACH,CAAC;AAED,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,CAAC,EAAEC,KAAK,EAAE;EACnE,OAAO,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,QAAQ,MAAM,IAAI,IAAID,KAAK,CAACE,YAAY,KAAK,QAAQ;AACnH,CAAC;AAED,IAAIC,cAAc,GAAG;EACnBC,UAAU,EAAE,qBAAqB;EACjCC,aAAa,EAAEf,kBAAkB;EACjCgB,YAAY,EAAEhB,kBAAkB;EAChCiB,cAAc,EAAEd,aAAa;EAC7Be,aAAa,EAAEf,aAAa;EAC5BgB,YAAY,EAAEb,gBAAgB;EAC9Bc,aAAa,EAAEpB,kBAAkB;EACjCqB,WAAW,EAAEb,qBAAqB;EAClCc,UAAU,EAAEd,qBAAqB;EACjCe,UAAU,EAAEf,qBAAqB;EACjCgB,cAAc,EAAE;AAClB,CAAC;AACD,IAAIC,gBAAgB,GAAG1B,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC;AAEhF,IAAI2B,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,SAAS,EAAE;EACtE,IAAIA,SAAS,KAAKC,SAAS,KAAKD,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,UAAU,CAAC,EAAE;IACpF,OAAO,YAAY;EACrB;EAEA,OAAO,UAAU;AACnB,CAAC;AAED,IAAIE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,aAAa,EAAEC,MAAM,EAAEC,cAAc,EAAE;EACxF,IAAIA,cAAc,KAAKJ,SAAS,EAAE;IAChC,OAAOI,cAAc;EACvB;EAEA,OAAO,EAAE,CAACC,MAAM,CAACH,aAAa,EAAE,GAAG,CAAC,CAACG,MAAM,CAACF,MAAM,CAAC;AACrD,CAAC;AAED,SAASF,iBAAiB,EAAEH,sBAAsB;AAClD,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}