{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useRCNotification from \"rc-notification/es/useNotification\";\nimport { ConfigConsumer } from '../../config-provider';\nimport { attachTypeApi, getKeyThenIncreaseKey, typeList } from '..';\nexport default function createUseMessage(getRcNotificationInstance, getRCNoticeProps) {\n  var useMessage = function useMessage() {\n    // We can only get content by render\n    var getPrefixCls;\n    var getPopupContainer; // We create a proxy to handle delay created instance\n\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = useRCNotification(proxy),\n      _useRCNotification2 = _slicedToArray(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('message', customizePrefixCls);\n      var rootPrefixCls = getPrefixCls();\n      var target = args.key || getKeyThenIncreaseKey();\n      var closePromise = new Promise(function (resolve) {\n        var callback = function callback() {\n          if (typeof args.onClose === 'function') {\n            args.onClose();\n          }\n          return resolve(true);\n        };\n        getRcNotificationInstance(_extends(_extends({}, args), {\n          prefixCls: mergedPrefixCls,\n          rootPrefixCls: rootPrefixCls,\n          getPopupContainer: getPopupContainer\n        }), function (_ref) {\n          var prefixCls = _ref.prefixCls,\n            instance = _ref.instance;\n          innerInstance = instance;\n          hookNotify(getRCNoticeProps(_extends(_extends({}, args), {\n            key: target,\n            onClose: callback\n          }), prefixCls));\n        });\n      });\n      var result = function result() {\n        if (innerInstance) {\n          innerInstance.removeNotice(target);\n        }\n      };\n      result.then = function (filled, rejected) {\n        return closePromise.then(filled, rejected);\n      };\n      result.promise = closePromise;\n      return result;\n    } // Fill functions\n\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    typeList.forEach(function (type) {\n      return attachTypeApi(hookApiRef.current, type);\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      getPopupContainer = context.getPopupContainer;\n      return holder;\n    })];\n  };\n  return useMessage;\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "useRCNotification", "ConfigConsumer", "attachTypeApi", "getKeyThenIncreaseKey", "typeList", "createUseMessage", "getRcNotificationInstance", "getRCNoticeProps", "useMessage", "getPrefixCls", "getPopupContainer", "innerInstance", "proxy", "add", "noticeProps", "<PERSON><PERSON><PERSON><PERSON>", "component", "_useRCNotification", "_useRCNotification2", "hookNotify", "holder", "notify", "args", "customizePrefixCls", "prefixCls", "mergedPrefixCls", "rootPrefixCls", "target", "key", "closePromise", "Promise", "resolve", "callback", "onClose", "_ref", "instance", "result", "removeNotice", "then", "filled", "rejected", "promise", "hookApiRef", "useRef", "current", "open", "for<PERSON>ach", "type", "createElement", "context"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/message/hooks/useMessage.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useRCNotification from \"rc-notification/es/useNotification\";\nimport { ConfigConsumer } from '../../config-provider';\nimport { attachTypeApi, getKeyThenIncreaseKey, typeList } from '..';\nexport default function createUseMessage(getRcNotificationInstance, getRCNoticeProps) {\n  var useMessage = function useMessage() {\n    // We can only get content by render\n    var getPrefixCls;\n    var getPopupContainer; // We create a proxy to handle delay created instance\n\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n\n    var _useRCNotification = useRCNotification(proxy),\n        _useRCNotification2 = _slicedToArray(_useRCNotification, 2),\n        hookNotify = _useRCNotification2[0],\n        holder = _useRCNotification2[1];\n\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('message', customizePrefixCls);\n      var rootPrefixCls = getPrefixCls();\n      var target = args.key || getKeyThenIncreaseKey();\n      var closePromise = new Promise(function (resolve) {\n        var callback = function callback() {\n          if (typeof args.onClose === 'function') {\n            args.onClose();\n          }\n\n          return resolve(true);\n        };\n\n        getRcNotificationInstance(_extends(_extends({}, args), {\n          prefixCls: mergedPrefixCls,\n          rootPrefixCls: rootPrefixCls,\n          getPopupContainer: getPopupContainer\n        }), function (_ref) {\n          var prefixCls = _ref.prefixCls,\n              instance = _ref.instance;\n          innerInstance = instance;\n          hookNotify(getRCNoticeProps(_extends(_extends({}, args), {\n            key: target,\n            onClose: callback\n          }), prefixCls));\n        });\n      });\n\n      var result = function result() {\n        if (innerInstance) {\n          innerInstance.removeNotice(target);\n        }\n      };\n\n      result.then = function (filled, rejected) {\n        return closePromise.then(filled, rejected);\n      };\n\n      result.promise = closePromise;\n      return result;\n    } // Fill functions\n\n\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    typeList.forEach(function (type) {\n      return attachTypeApi(hookApiRef.current, type);\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      getPopupContainer = context.getPopupContainer;\n      return holder;\n    })];\n  };\n\n  return useMessage;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,aAAa,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,IAAI;AACnE,eAAe,SAASC,gBAAgBA,CAACC,yBAAyB,EAAEC,gBAAgB,EAAE;EACpF,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC;IACA,IAAIC,YAAY;IAChB,IAAIC,iBAAiB,CAAC,CAAC;;IAEvB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,KAAK,GAAG;MACVC,GAAG,EAAE,SAASA,GAAGA,CAACC,WAAW,EAAEC,cAAc,EAAE;QAC7CJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,SAAS,CAACH,GAAG,CAACC,WAAW,EAAEC,cAAc,CAAC;MACxH;IACF,CAAC;IAED,IAAIE,kBAAkB,GAAGjB,iBAAiB,CAACY,KAAK,CAAC;MAC7CM,mBAAmB,GAAGpB,cAAc,CAACmB,kBAAkB,EAAE,CAAC,CAAC;MAC3DE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;MACnCE,MAAM,GAAGF,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAASG,MAAMA,CAACC,IAAI,EAAE;MACpB,IAAIC,kBAAkB,GAAGD,IAAI,CAACE,SAAS;MACvC,IAAIC,eAAe,GAAGhB,YAAY,CAAC,SAAS,EAAEc,kBAAkB,CAAC;MACjE,IAAIG,aAAa,GAAGjB,YAAY,CAAC,CAAC;MAClC,IAAIkB,MAAM,GAAGL,IAAI,CAACM,GAAG,IAAIzB,qBAAqB,CAAC,CAAC;MAChD,IAAI0B,YAAY,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;QAChD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;UACjC,IAAI,OAAOV,IAAI,CAACW,OAAO,KAAK,UAAU,EAAE;YACtCX,IAAI,CAACW,OAAO,CAAC,CAAC;UAChB;UAEA,OAAOF,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QAEDzB,yBAAyB,CAACT,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,IAAI,CAAC,EAAE;UACrDE,SAAS,EAAEC,eAAe;UAC1BC,aAAa,EAAEA,aAAa;UAC5BhB,iBAAiB,EAAEA;QACrB,CAAC,CAAC,EAAE,UAAUwB,IAAI,EAAE;UAClB,IAAIV,SAAS,GAAGU,IAAI,CAACV,SAAS;YAC1BW,QAAQ,GAAGD,IAAI,CAACC,QAAQ;UAC5BxB,aAAa,GAAGwB,QAAQ;UACxBhB,UAAU,CAACZ,gBAAgB,CAACV,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,IAAI,CAAC,EAAE;YACvDM,GAAG,EAAED,MAAM;YACXM,OAAO,EAAED;UACX,CAAC,CAAC,EAAER,SAAS,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAIY,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QAC7B,IAAIzB,aAAa,EAAE;UACjBA,aAAa,CAAC0B,YAAY,CAACV,MAAM,CAAC;QACpC;MACF,CAAC;MAEDS,MAAM,CAACE,IAAI,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAE;QACxC,OAAOX,YAAY,CAACS,IAAI,CAACC,MAAM,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MAEDJ,MAAM,CAACK,OAAO,GAAGZ,YAAY;MAC7B,OAAOO,MAAM;IACf,CAAC,CAAC;;IAGF,IAAIM,UAAU,GAAG3C,KAAK,CAAC4C,MAAM,CAAC,CAAC,CAAC,CAAC;IACjCD,UAAU,CAACE,OAAO,CAACC,IAAI,GAAGxB,MAAM;IAChCjB,QAAQ,CAAC0C,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC/B,OAAO7C,aAAa,CAACwC,UAAU,CAACE,OAAO,EAAEG,IAAI,CAAC;IAChD,CAAC,CAAC;IACF,OAAO,CAACL,UAAU,CAACE,OAAO,EAAE,aAAa7C,KAAK,CAACiD,aAAa,CAAC/C,cAAc,EAAE;MAC3E2B,GAAG,EAAE;IACP,CAAC,EAAE,UAAUqB,OAAO,EAAE;MACpBxC,YAAY,GAAGwC,OAAO,CAACxC,YAAY;MACnCC,iBAAiB,GAAGuC,OAAO,CAACvC,iBAAiB;MAC7C,OAAOU,MAAM;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAOZ,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}