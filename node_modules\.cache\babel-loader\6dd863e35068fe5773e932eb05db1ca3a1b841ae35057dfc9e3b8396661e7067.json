{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport Menu, { MenuItem } from 'rc-menu';\nimport * as React from 'react';\nimport { MentionsContextConsumer } from './MentionsContext';\n/**\n * We only use Menu to display the candidate.\n * The focus is controlled by textarea to make accessibility easy.\n */\n\nvar DropdownMenu = /*#__PURE__*/function (_React$Component) {\n  _inherits(DropdownMenu, _React$Component);\n  var _super = _createSuper(DropdownMenu);\n  function DropdownMenu() {\n    var _this;\n    _classCallCheck(this, DropdownMenu);\n    _this = _super.apply(this, arguments);\n    _this.renderDropdown = function (_ref) {\n      var notFoundContent = _ref.notFoundContent,\n        activeIndex = _ref.activeIndex,\n        setActiveIndex = _ref.setActiveIndex,\n        selectOption = _ref.selectOption,\n        onFocus = _ref.onFocus,\n        onBlur = _ref.onBlur;\n      var _this$props = _this.props,\n        prefixCls = _this$props.prefixCls,\n        options = _this$props.options;\n      var activeOption = options[activeIndex] || {};\n      return /*#__PURE__*/React.createElement(Menu, {\n        prefixCls: \"\".concat(prefixCls, \"-menu\"),\n        activeKey: activeOption.key,\n        onSelect: function onSelect(_ref2) {\n          var key = _ref2.key;\n          var option = options.find(function (_ref3) {\n            var optionKey = _ref3.key;\n            return optionKey === key;\n          });\n          selectOption(option);\n        },\n        onFocus: onFocus,\n        onBlur: onBlur\n      }, options.map(function (option, index) {\n        var key = option.key,\n          disabled = option.disabled,\n          children = option.children,\n          className = option.className,\n          style = option.style;\n        return /*#__PURE__*/React.createElement(MenuItem, {\n          key: key,\n          disabled: disabled,\n          className: className,\n          style: style,\n          onMouseEnter: function onMouseEnter() {\n            setActiveIndex(index);\n          }\n        }, children);\n      }), !options.length && /*#__PURE__*/React.createElement(MenuItem, {\n        disabled: true\n      }, notFoundContent));\n    };\n    return _this;\n  }\n  _createClass(DropdownMenu, [{\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(MentionsContextConsumer, null, this.renderDropdown);\n    }\n  }]);\n  return DropdownMenu;\n}(React.Component);\nexport default DropdownMenu;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "<PERSON><PERSON>", "MenuItem", "React", "MentionsContextConsumer", "DropdownMenu", "_React$Component", "_super", "_this", "apply", "arguments", "renderDropdown", "_ref", "notFoundContent", "activeIndex", "setActiveIndex", "selectOption", "onFocus", "onBlur", "_this$props", "props", "prefixCls", "options", "activeOption", "createElement", "concat", "active<PERSON><PERSON>", "key", "onSelect", "_ref2", "option", "find", "_ref3", "optionKey", "map", "index", "disabled", "children", "className", "style", "onMouseEnter", "length", "value", "render", "Component"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-mentions/es/DropdownMenu.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport Menu, { MenuItem } from 'rc-menu';\nimport * as React from 'react';\nimport { MentionsContextConsumer } from './MentionsContext';\n/**\n * We only use Menu to display the candidate.\n * The focus is controlled by textarea to make accessibility easy.\n */\n\nvar DropdownMenu = /*#__PURE__*/function (_React$Component) {\n  _inherits(DropdownMenu, _React$Component);\n\n  var _super = _createSuper(DropdownMenu);\n\n  function DropdownMenu() {\n    var _this;\n\n    _classCallCheck(this, DropdownMenu);\n\n    _this = _super.apply(this, arguments);\n\n    _this.renderDropdown = function (_ref) {\n      var notFoundContent = _ref.notFoundContent,\n          activeIndex = _ref.activeIndex,\n          setActiveIndex = _ref.setActiveIndex,\n          selectOption = _ref.selectOption,\n          onFocus = _ref.onFocus,\n          onBlur = _ref.onBlur;\n      var _this$props = _this.props,\n          prefixCls = _this$props.prefixCls,\n          options = _this$props.options;\n      var activeOption = options[activeIndex] || {};\n      return /*#__PURE__*/React.createElement(Menu, {\n        prefixCls: \"\".concat(prefixCls, \"-menu\"),\n        activeKey: activeOption.key,\n        onSelect: function onSelect(_ref2) {\n          var key = _ref2.key;\n          var option = options.find(function (_ref3) {\n            var optionKey = _ref3.key;\n            return optionKey === key;\n          });\n          selectOption(option);\n        },\n        onFocus: onFocus,\n        onBlur: onBlur\n      }, options.map(function (option, index) {\n        var key = option.key,\n            disabled = option.disabled,\n            children = option.children,\n            className = option.className,\n            style = option.style;\n        return /*#__PURE__*/React.createElement(MenuItem, {\n          key: key,\n          disabled: disabled,\n          className: className,\n          style: style,\n          onMouseEnter: function onMouseEnter() {\n            setActiveIndex(index);\n          }\n        }, children);\n      }), !options.length && /*#__PURE__*/React.createElement(MenuItem, {\n        disabled: true\n      }, notFoundContent));\n    };\n\n    return _this;\n  }\n\n  _createClass(DropdownMenu, [{\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(MentionsContextConsumer, null, this.renderDropdown);\n    }\n  }]);\n\n  return DropdownMenu;\n}(React.Component);\n\nexport default DropdownMenu;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,IAAI,IAAIC,QAAQ,QAAQ,SAAS;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC1DP,SAAS,CAACM,YAAY,EAAEC,gBAAgB,CAAC;EAEzC,IAAIC,MAAM,GAAGP,YAAY,CAACK,YAAY,CAAC;EAEvC,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIG,KAAK;IAETX,eAAe,CAAC,IAAI,EAAEQ,YAAY,CAAC;IAEnCG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAErCF,KAAK,CAACG,cAAc,GAAG,UAAUC,IAAI,EAAE;MACrC,IAAIC,eAAe,GAAGD,IAAI,CAACC,eAAe;QACtCC,WAAW,GAAGF,IAAI,CAACE,WAAW;QAC9BC,cAAc,GAAGH,IAAI,CAACG,cAAc;QACpCC,YAAY,GAAGJ,IAAI,CAACI,YAAY;QAChCC,OAAO,GAAGL,IAAI,CAACK,OAAO;QACtBC,MAAM,GAAGN,IAAI,CAACM,MAAM;MACxB,IAAIC,WAAW,GAAGX,KAAK,CAACY,KAAK;QACzBC,SAAS,GAAGF,WAAW,CAACE,SAAS;QACjCC,OAAO,GAAGH,WAAW,CAACG,OAAO;MACjC,IAAIC,YAAY,GAAGD,OAAO,CAACR,WAAW,CAAC,IAAI,CAAC,CAAC;MAC7C,OAAO,aAAaX,KAAK,CAACqB,aAAa,CAACvB,IAAI,EAAE;QAC5CoB,SAAS,EAAE,EAAE,CAACI,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC;QACxCK,SAAS,EAAEH,YAAY,CAACI,GAAG;QAC3BC,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;UACjC,IAAIF,GAAG,GAAGE,KAAK,CAACF,GAAG;UACnB,IAAIG,MAAM,GAAGR,OAAO,CAACS,IAAI,CAAC,UAAUC,KAAK,EAAE;YACzC,IAAIC,SAAS,GAAGD,KAAK,CAACL,GAAG;YACzB,OAAOM,SAAS,KAAKN,GAAG;UAC1B,CAAC,CAAC;UACFX,YAAY,CAACc,MAAM,CAAC;QACtB,CAAC;QACDb,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA;MACV,CAAC,EAAEI,OAAO,CAACY,GAAG,CAAC,UAAUJ,MAAM,EAAEK,KAAK,EAAE;QACtC,IAAIR,GAAG,GAAGG,MAAM,CAACH,GAAG;UAChBS,QAAQ,GAAGN,MAAM,CAACM,QAAQ;UAC1BC,QAAQ,GAAGP,MAAM,CAACO,QAAQ;UAC1BC,SAAS,GAAGR,MAAM,CAACQ,SAAS;UAC5BC,KAAK,GAAGT,MAAM,CAACS,KAAK;QACxB,OAAO,aAAapC,KAAK,CAACqB,aAAa,CAACtB,QAAQ,EAAE;UAChDyB,GAAG,EAAEA,GAAG;UACRS,QAAQ,EAAEA,QAAQ;UAClBE,SAAS,EAAEA,SAAS;UACpBC,KAAK,EAAEA,KAAK;UACZC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;YACpCzB,cAAc,CAACoB,KAAK,CAAC;UACvB;QACF,CAAC,EAAEE,QAAQ,CAAC;MACd,CAAC,CAAC,EAAE,CAACf,OAAO,CAACmB,MAAM,IAAI,aAAatC,KAAK,CAACqB,aAAa,CAACtB,QAAQ,EAAE;QAChEkC,QAAQ,EAAE;MACZ,CAAC,EAAEvB,eAAe,CAAC,CAAC;IACtB,CAAC;IAED,OAAOL,KAAK;EACd;EAEAV,YAAY,CAACO,YAAY,EAAE,CAAC;IAC1BsB,GAAG,EAAE,QAAQ;IACbe,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAaxC,KAAK,CAACqB,aAAa,CAACpB,uBAAuB,EAAE,IAAI,EAAE,IAAI,CAACO,cAAc,CAAC;IAC7F;EACF,CAAC,CAAC,CAAC;EAEH,OAAON,YAAY;AACrB,CAAC,CAACF,KAAK,CAACyC,SAAS,CAAC;AAElB,eAAevC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}