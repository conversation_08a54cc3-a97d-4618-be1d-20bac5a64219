export { ManifestValuesComputed as ManifestValues };
declare const ManifestValuesComputed: typeof ManifestValues & {
    request: (dependencies: Pick<import("../index.js").Artifacts, "InstallabilityErrors" | "WebAppManifest">, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../index.js").Artifacts.ManifestValues>;
};
declare class ManifestValues {
    /** @typedef {(val: NonNullable<LH.Artifacts.Manifest['value']>, errors: LH.Artifacts.InstallabilityErrors['errors']) => boolean} Validator */
    /**
     * @return {Array<{id: LH.Artifacts.ManifestValueCheckID, failureText: string, validate: Validator}>}
     */
    static get manifestChecks(): {
        id: LH.Artifacts.ManifestValueCheckID;
        failureText: string;
        validate: (val: NonNullable<LH.Artifacts.Manifest['value']>, errors: LH.Artifacts.InstallabilityErrors['errors']) => boolean;
    }[];
    /**
     * Returns results of all manifest checks
     * @param {Pick<LH.Artifacts, 'WebAppManifest'|'InstallabilityErrors'>} Manifest
     * @return {Promise<LH.Artifacts.ManifestValues>}
     */
    static compute_({ WebAppManifest, InstallabilityErrors }: Pick<LH.Artifacts, 'WebAppManifest' | 'InstallabilityErrors'>): Promise<LH.Artifacts.ManifestValues>;
}
//# sourceMappingURL=manifest-values.d.ts.map