{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport Statistic from './Statistic';\nimport { formatCountdown } from './utils';\nimport { cloneElement } from '../_util/reactNode';\nvar REFRESH_INTERVAL = 1000 / 30;\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nvar Countdown = /*#__PURE__*/function (_React$Component) {\n  _inherits(Countdown, _React$Component);\n  var _super = _createSuper(Countdown);\n  function Countdown() {\n    var _this;\n    _classCallCheck(this, Countdown);\n    _this = _super.apply(this, arguments);\n    _this.syncTimer = function () {\n      var value = _this.props.value;\n      var timestamp = getTime(value);\n      if (timestamp >= Date.now()) {\n        _this.startTimer();\n      } else {\n        _this.stopTimer();\n      }\n    };\n    _this.startTimer = function () {\n      if (_this.countdownId) return;\n      var _this$props = _this.props,\n        onChange = _this$props.onChange,\n        value = _this$props.value;\n      var timestamp = getTime(value);\n      _this.countdownId = window.setInterval(function () {\n        _this.forceUpdate();\n        if (onChange && timestamp > Date.now()) {\n          onChange(timestamp - Date.now());\n        }\n      }, REFRESH_INTERVAL);\n    };\n    _this.stopTimer = function () {\n      var _this$props2 = _this.props,\n        onFinish = _this$props2.onFinish,\n        value = _this$props2.value;\n      if (_this.countdownId) {\n        clearInterval(_this.countdownId);\n        _this.countdownId = undefined;\n        var timestamp = getTime(value);\n        if (onFinish && timestamp < Date.now()) {\n          onFinish();\n        }\n      }\n    };\n    _this.formatCountdown = function (value, config) {\n      var format = _this.props.format;\n      return formatCountdown(value, _extends(_extends({}, config), {\n        format: format\n      }));\n    }; // Countdown do not need display the timestamp\n    // eslint-disable-next-line class-methods-use-this\n\n    _this.valueRender = function (node) {\n      return cloneElement(node, {\n        title: undefined\n      });\n    };\n    return _this;\n  }\n  _createClass(Countdown, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.syncTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncTimer();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.stopTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(Statistic, _extends({\n        valueRender: this.valueRender\n      }, this.props, {\n        formatter: this.formatCountdown\n      }));\n    }\n  }]);\n  return Countdown;\n}(React.Component);\nCountdown.defaultProps = {\n  format: 'HH:mm:ss'\n};\nexport default Countdown;", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "Statistic", "formatCountdown", "cloneElement", "REFRESH_INTERVAL", "getTime", "value", "Date", "Countdown", "_React$Component", "_super", "_this", "apply", "arguments", "syncTimer", "props", "timestamp", "now", "startTimer", "stopTimer", "countdownId", "_this$props", "onChange", "window", "setInterval", "forceUpdate", "_this$props2", "onFinish", "clearInterval", "undefined", "config", "format", "valueRender", "node", "title", "key", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "render", "createElement", "formatter", "Component", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/statistic/Countdown.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport Statistic from './Statistic';\nimport { formatCountdown } from './utils';\nimport { cloneElement } from '../_util/reactNode';\nvar REFRESH_INTERVAL = 1000 / 30;\n\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\n\nvar Countdown = /*#__PURE__*/function (_React$Component) {\n  _inherits(Countdown, _React$Component);\n\n  var _super = _createSuper(Countdown);\n\n  function Countdown() {\n    var _this;\n\n    _classCallCheck(this, Countdown);\n\n    _this = _super.apply(this, arguments);\n\n    _this.syncTimer = function () {\n      var value = _this.props.value;\n      var timestamp = getTime(value);\n\n      if (timestamp >= Date.now()) {\n        _this.startTimer();\n      } else {\n        _this.stopTimer();\n      }\n    };\n\n    _this.startTimer = function () {\n      if (_this.countdownId) return;\n      var _this$props = _this.props,\n          onChange = _this$props.onChange,\n          value = _this$props.value;\n      var timestamp = getTime(value);\n      _this.countdownId = window.setInterval(function () {\n        _this.forceUpdate();\n\n        if (onChange && timestamp > Date.now()) {\n          onChange(timestamp - Date.now());\n        }\n      }, REFRESH_INTERVAL);\n    };\n\n    _this.stopTimer = function () {\n      var _this$props2 = _this.props,\n          onFinish = _this$props2.onFinish,\n          value = _this$props2.value;\n\n      if (_this.countdownId) {\n        clearInterval(_this.countdownId);\n        _this.countdownId = undefined;\n        var timestamp = getTime(value);\n\n        if (onFinish && timestamp < Date.now()) {\n          onFinish();\n        }\n      }\n    };\n\n    _this.formatCountdown = function (value, config) {\n      var format = _this.props.format;\n      return formatCountdown(value, _extends(_extends({}, config), {\n        format: format\n      }));\n    }; // Countdown do not need display the timestamp\n    // eslint-disable-next-line class-methods-use-this\n\n\n    _this.valueRender = function (node) {\n      return cloneElement(node, {\n        title: undefined\n      });\n    };\n\n    return _this;\n  }\n\n  _createClass(Countdown, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.syncTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncTimer();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.stopTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(Statistic, _extends({\n        valueRender: this.valueRender\n      }, this.props, {\n        formatter: this.formatCountdown\n      }));\n    }\n  }]);\n\n  return Countdown;\n}(React.Component);\n\nCountdown.defaultProps = {\n  format: 'HH:mm:ss'\n};\nexport default Countdown;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,eAAe,QAAQ,SAAS;AACzC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,IAAIC,gBAAgB,GAAG,IAAI,GAAG,EAAE;AAEhC,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,OAAO,IAAIC,IAAI,CAACD,KAAK,CAAC,CAACD,OAAO,CAAC,CAAC;AAClC;AAEA,IAAIG,SAAS,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACvDX,SAAS,CAACU,SAAS,EAAEC,gBAAgB,CAAC;EAEtC,IAAIC,MAAM,GAAGX,YAAY,CAACS,SAAS,CAAC;EAEpC,SAASA,SAASA,CAAA,EAAG;IACnB,IAAIG,KAAK;IAETf,eAAe,CAAC,IAAI,EAAEY,SAAS,CAAC;IAEhCG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAErCF,KAAK,CAACG,SAAS,GAAG,YAAY;MAC5B,IAAIR,KAAK,GAAGK,KAAK,CAACI,KAAK,CAACT,KAAK;MAC7B,IAAIU,SAAS,GAAGX,OAAO,CAACC,KAAK,CAAC;MAE9B,IAAIU,SAAS,IAAIT,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;QAC3BN,KAAK,CAACO,UAAU,CAAC,CAAC;MACpB,CAAC,MAAM;QACLP,KAAK,CAACQ,SAAS,CAAC,CAAC;MACnB;IACF,CAAC;IAEDR,KAAK,CAACO,UAAU,GAAG,YAAY;MAC7B,IAAIP,KAAK,CAACS,WAAW,EAAE;MACvB,IAAIC,WAAW,GAAGV,KAAK,CAACI,KAAK;QACzBO,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BhB,KAAK,GAAGe,WAAW,CAACf,KAAK;MAC7B,IAAIU,SAAS,GAAGX,OAAO,CAACC,KAAK,CAAC;MAC9BK,KAAK,CAACS,WAAW,GAAGG,MAAM,CAACC,WAAW,CAAC,YAAY;QACjDb,KAAK,CAACc,WAAW,CAAC,CAAC;QAEnB,IAAIH,QAAQ,IAAIN,SAAS,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;UACtCK,QAAQ,CAACN,SAAS,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,CAAC;QAClC;MACF,CAAC,EAAEb,gBAAgB,CAAC;IACtB,CAAC;IAEDO,KAAK,CAACQ,SAAS,GAAG,YAAY;MAC5B,IAAIO,YAAY,GAAGf,KAAK,CAACI,KAAK;QAC1BY,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCrB,KAAK,GAAGoB,YAAY,CAACpB,KAAK;MAE9B,IAAIK,KAAK,CAACS,WAAW,EAAE;QACrBQ,aAAa,CAACjB,KAAK,CAACS,WAAW,CAAC;QAChCT,KAAK,CAACS,WAAW,GAAGS,SAAS;QAC7B,IAAIb,SAAS,GAAGX,OAAO,CAACC,KAAK,CAAC;QAE9B,IAAIqB,QAAQ,IAAIX,SAAS,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;UACtCU,QAAQ,CAAC,CAAC;QACZ;MACF;IACF,CAAC;IAEDhB,KAAK,CAACT,eAAe,GAAG,UAAUI,KAAK,EAAEwB,MAAM,EAAE;MAC/C,IAAIC,MAAM,GAAGpB,KAAK,CAACI,KAAK,CAACgB,MAAM;MAC/B,OAAO7B,eAAe,CAACI,KAAK,EAAEX,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmC,MAAM,CAAC,EAAE;QAC3DC,MAAM,EAAEA;MACV,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH;;IAGApB,KAAK,CAACqB,WAAW,GAAG,UAAUC,IAAI,EAAE;MAClC,OAAO9B,YAAY,CAAC8B,IAAI,EAAE;QACxBC,KAAK,EAAEL;MACT,CAAC,CAAC;IACJ,CAAC;IAED,OAAOlB,KAAK;EACd;EAEAd,YAAY,CAACW,SAAS,EAAE,CAAC;IACvB2B,GAAG,EAAE,mBAAmB;IACxB7B,KAAK,EAAE,SAAS8B,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACtB,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDqB,GAAG,EAAE,oBAAoB;IACzB7B,KAAK,EAAE,SAAS+B,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACvB,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDqB,GAAG,EAAE,sBAAsB;IAC3B7B,KAAK,EAAE,SAASgC,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACnB,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDgB,GAAG,EAAE,QAAQ;IACb7B,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAavC,KAAK,CAACwC,aAAa,CAACvC,SAAS,EAAEN,QAAQ,CAAC;QAC1DqC,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,EAAE,IAAI,CAACjB,KAAK,EAAE;QACb0B,SAAS,EAAE,IAAI,CAACvC;MAClB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAOM,SAAS;AAClB,CAAC,CAACR,KAAK,CAAC0C,SAAS,CAAC;AAElBlC,SAAS,CAACmC,YAAY,GAAG;EACvBZ,MAAM,EAAE;AACV,CAAC;AACD,eAAevB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}