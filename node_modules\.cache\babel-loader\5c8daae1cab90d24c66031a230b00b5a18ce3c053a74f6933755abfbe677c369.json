{"ast": null, "code": "import { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport isFF from '../utils/isFirefox';\nimport useOriginScroll from './useOriginScroll';\nexport default function useFrameWheel(inVirtual, isScrollAtTop, isScrollAtBottom, onWheelDelta) {\n  var offsetRef = useRef(0);\n  var nextFrameRef = useRef(null); // Firefox patch\n\n  var wheelValueRef = useRef(null);\n  var isMouseScrollRef = useRef(false); // Scroll status sync\n\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom);\n  function onWheel(event) {\n    if (!inVirtual) return;\n    raf.cancel(nextFrameRef.current);\n    var deltaY = event.deltaY;\n    offsetRef.current += deltaY;\n    wheelValueRef.current = deltaY; // Do nothing when scroll at the edge, Skip check when is in scroll\n\n    if (originScroll(deltaY)) return; // Proxy of scroll events\n\n    if (!isFF) {\n      event.preventDefault();\n    }\n    nextFrameRef.current = raf(function () {\n      // Patch a multiple for Firefox to fix wheel number too small\n      // ref: https://github.com/ant-design/ant-design/issues/26372#issuecomment-679460266\n      var patchMultiple = isMouseScrollRef.current ? 10 : 1;\n      onWheelDelta(offsetRef.current * patchMultiple);\n      offsetRef.current = 0;\n    });\n  } // A patch for firefox\n\n  function onFireFoxScroll(event) {\n    if (!inVirtual) return;\n    isMouseScrollRef.current = event.detail === wheelValueRef.current;\n  }\n  return [onWheel, onFireFoxScroll];\n}", "map": {"version": 3, "names": ["useRef", "raf", "isFF", "useOriginScroll", "useFrameWheel", "inVirtual", "isScrollAtTop", "isScrollAtBottom", "onWheelDelta", "offsetRef", "nextFrameRef", "wheelValueRef", "isMouseScrollRef", "originScroll", "onWheel", "event", "cancel", "current", "deltaY", "preventDefault", "patchMultiple", "onFireFoxScroll", "detail"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-virtual-list/es/hooks/useFrameWheel.js"], "sourcesContent": ["import { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport isFF from '../utils/isFirefox';\nimport useOriginScroll from './useOriginScroll';\nexport default function useFrameWheel(inVirtual, isScrollAtTop, isScrollAtBottom, onWheelDelta) {\n  var offsetRef = useRef(0);\n  var nextFrameRef = useRef(null); // Firefox patch\n\n  var wheelValueRef = useRef(null);\n  var isMouseScrollRef = useRef(false); // Scroll status sync\n\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom);\n\n  function onWheel(event) {\n    if (!inVirtual) return;\n    raf.cancel(nextFrameRef.current);\n    var deltaY = event.deltaY;\n    offsetRef.current += deltaY;\n    wheelValueRef.current = deltaY; // Do nothing when scroll at the edge, Skip check when is in scroll\n\n    if (originScroll(deltaY)) return; // Proxy of scroll events\n\n    if (!isFF) {\n      event.preventDefault();\n    }\n\n    nextFrameRef.current = raf(function () {\n      // Patch a multiple for Firefox to fix wheel number too small\n      // ref: https://github.com/ant-design/ant-design/issues/26372#issuecomment-679460266\n      var patchMultiple = isMouseScrollRef.current ? 10 : 1;\n      onWheelDelta(offsetRef.current * patchMultiple);\n      offsetRef.current = 0;\n    });\n  } // A patch for firefox\n\n\n  function onFireFoxScroll(event) {\n    if (!inVirtual) return;\n    isMouseScrollRef.current = event.detail === wheelValueRef.current;\n  }\n\n  return [onWheel, onFireFoxScroll];\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,EAAE;EAC9F,IAAIC,SAAS,GAAGT,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIU,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEjC,IAAIW,aAAa,GAAGX,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIY,gBAAgB,GAAGZ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEtC,IAAIa,YAAY,GAAGV,eAAe,CAACG,aAAa,EAAEC,gBAAgB,CAAC;EAEnE,SAASO,OAAOA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACV,SAAS,EAAE;IAChBJ,GAAG,CAACe,MAAM,CAACN,YAAY,CAACO,OAAO,CAAC;IAChC,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACzBT,SAAS,CAACQ,OAAO,IAAIC,MAAM;IAC3BP,aAAa,CAACM,OAAO,GAAGC,MAAM,CAAC,CAAC;;IAEhC,IAAIL,YAAY,CAACK,MAAM,CAAC,EAAE,OAAO,CAAC;;IAElC,IAAI,CAAChB,IAAI,EAAE;MACTa,KAAK,CAACI,cAAc,CAAC,CAAC;IACxB;IAEAT,YAAY,CAACO,OAAO,GAAGhB,GAAG,CAAC,YAAY;MACrC;MACA;MACA,IAAImB,aAAa,GAAGR,gBAAgB,CAACK,OAAO,GAAG,EAAE,GAAG,CAAC;MACrDT,YAAY,CAACC,SAAS,CAACQ,OAAO,GAAGG,aAAa,CAAC;MAC/CX,SAAS,CAACQ,OAAO,GAAG,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,SAASI,eAAeA,CAACN,KAAK,EAAE;IAC9B,IAAI,CAACV,SAAS,EAAE;IAChBO,gBAAgB,CAACK,OAAO,GAAGF,KAAK,CAACO,MAAM,KAAKX,aAAa,CAACM,OAAO;EACnE;EAEA,OAAO,CAACH,OAAO,EAAEO,eAAe,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}