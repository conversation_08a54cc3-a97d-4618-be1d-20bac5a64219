{"ast": null, "code": "import _regeneratorRuntime from \"@babel/runtime/regenerator\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as ReactDOM from 'react-dom'; // Let compiler not to search module usage\n\nvar fullClone = _objectSpread({}, ReactDOM);\nvar version = fullClone.version,\n  reactRender = fullClone.render,\n  unmountComponentAtNode = fullClone.unmountComponentAtNode;\nvar createRoot;\ntry {\n  var mainVersion = Number((version || '').split('.')[0]);\n  if (mainVersion >= 18) {\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {// Do nothing;\n}\nfunction toggleWarning(skip) {\n  var __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = fullClone.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && _typeof(__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\nvar MARK = '__rc_react_root__';\nfunction modernRender(node, container) {\n  toggleWarning(true);\n  var root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\nfunction legacyRender(node, container) {\n  reactRender(node, container);\n}\n/** @private Test usage. Not work in prod */\n\nexport function _r(node, container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyRender(node, container);\n  }\n}\nexport function render(node, container) {\n  if (createRoot) {\n    modernRender(node, container);\n    return;\n  }\n  legacyRender(node, container);\n} // ========================= Unmount ==========================\n\nfunction modernUnmount(_x) {\n  return _modernUnmount.apply(this, arguments);\n}\nfunction _modernUnmount() {\n  _modernUnmount = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee(container) {\n    return _regeneratorRuntime.wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            return _context.abrupt(\"return\", Promise.resolve().then(function () {\n              var _container$MARK;\n              (_container$MARK = container[MARK]) === null || _container$MARK === void 0 ? void 0 : _container$MARK.unmount();\n              delete container[MARK];\n            }));\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee);\n  }));\n  return _modernUnmount.apply(this, arguments);\n}\nfunction legacyUnmount(container) {\n  unmountComponentAtNode(container);\n}\n/** @private Test usage. Not work in prod */\n\nexport function _u(container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyUnmount(container);\n  }\n}\nexport function unmount(_x2) {\n  return _unmount.apply(this, arguments);\n}\nfunction _unmount() {\n  _unmount = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee2(container) {\n    return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n      while (1) {\n        switch (_context2.prev = _context2.next) {\n          case 0:\n            if (!(createRoot !== undefined)) {\n              _context2.next = 2;\n              break;\n            }\n            return _context2.abrupt(\"return\", modernUnmount(container));\n          case 2:\n            legacyUnmount(container);\n          case 3:\n          case \"end\":\n            return _context2.stop();\n        }\n      }\n    }, _callee2);\n  }));\n  return _unmount.apply(this, arguments);\n}", "map": {"version": 3, "names": ["_regeneratorRuntime", "_asyncToGenerator", "_typeof", "_objectSpread", "ReactDOM", "fullClone", "version", "reactRender", "render", "unmountComponentAtNode", "createRoot", "mainVersion", "Number", "split", "e", "toggle<PERSON><PERSON>ning", "skip", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "usingClientEntryPoint", "MARK", "modernRender", "node", "container", "root", "legacyRender", "_r", "process", "env", "NODE_ENV", "modernUnmount", "_x", "_modernUnmount", "apply", "arguments", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "Promise", "resolve", "then", "_container$MARK", "unmount", "stop", "legacyUnmount", "_u", "_x2", "_unmount", "_callee2", "_callee2$", "_context2", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/React/render.js"], "sourcesContent": ["import _regeneratorRuntime from \"@babel/runtime/regenerator\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as ReactDOM from 'react-dom'; // Let compiler not to search module usage\n\nvar fullClone = _objectSpread({}, ReactDOM);\n\nvar version = fullClone.version,\n    reactRender = fullClone.render,\n    unmountComponentAtNode = fullClone.unmountComponentAtNode;\nvar createRoot;\n\ntry {\n  var mainVersion = Number((version || '').split('.')[0]);\n\n  if (mainVersion >= 18) {\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {// Do nothing;\n}\n\nfunction toggleWarning(skip) {\n  var __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = fullClone.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && _typeof(__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\n\nvar MARK = '__rc_react_root__';\n\nfunction modernRender(node, container) {\n  toggleWarning(true);\n  var root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\n\nfunction legacyRender(node, container) {\n  reactRender(node, container);\n}\n/** @private Test usage. Not work in prod */\n\n\nexport function _r(node, container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyRender(node, container);\n  }\n}\nexport function render(node, container) {\n  if (createRoot) {\n    modernRender(node, container);\n    return;\n  }\n\n  legacyRender(node, container);\n} // ========================= Unmount ==========================\n\nfunction modernUnmount(_x) {\n  return _modernUnmount.apply(this, arguments);\n}\n\nfunction _modernUnmount() {\n  _modernUnmount = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(container) {\n    return _regeneratorRuntime.wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            return _context.abrupt(\"return\", Promise.resolve().then(function () {\n              var _container$MARK;\n\n              (_container$MARK = container[MARK]) === null || _container$MARK === void 0 ? void 0 : _container$MARK.unmount();\n              delete container[MARK];\n            }));\n\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee);\n  }));\n  return _modernUnmount.apply(this, arguments);\n}\n\nfunction legacyUnmount(container) {\n  unmountComponentAtNode(container);\n}\n/** @private Test usage. Not work in prod */\n\n\nexport function _u(container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyUnmount(container);\n  }\n}\nexport function unmount(_x2) {\n  return _unmount.apply(this, arguments);\n}\n\nfunction _unmount() {\n  _unmount = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2(container) {\n    return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n      while (1) {\n        switch (_context2.prev = _context2.next) {\n          case 0:\n            if (!(createRoot !== undefined)) {\n              _context2.next = 2;\n              break;\n            }\n\n            return _context2.abrupt(\"return\", modernUnmount(container));\n\n          case 2:\n            legacyUnmount(container);\n\n          case 3:\n          case \"end\":\n            return _context2.stop();\n        }\n      }\n    }, _callee2);\n  }));\n  return _unmount.apply(this, arguments);\n}"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,4BAA4B;AAC5D,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,QAAQ,MAAM,WAAW,CAAC,CAAC;;AAEvC,IAAIC,SAAS,GAAGF,aAAa,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;AAE3C,IAAIE,OAAO,GAAGD,SAAS,CAACC,OAAO;EAC3BC,WAAW,GAAGF,SAAS,CAACG,MAAM;EAC9BC,sBAAsB,GAAGJ,SAAS,CAACI,sBAAsB;AAC7D,IAAIC,UAAU;AAEd,IAAI;EACF,IAAIC,WAAW,GAAGC,MAAM,CAAC,CAACN,OAAO,IAAI,EAAE,EAAEO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAEvD,IAAIF,WAAW,IAAI,EAAE,EAAE;IACrBD,UAAU,GAAGL,SAAS,CAACK,UAAU;EACnC;AACF,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;AAAA;AAGb,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,kDAAkD,GAAGZ,SAAS,CAACY,kDAAkD;EAErH,IAAIA,kDAAkD,IAAIf,OAAO,CAACe,kDAAkD,CAAC,KAAK,QAAQ,EAAE;IAClIA,kDAAkD,CAACC,qBAAqB,GAAGF,IAAI;EACjF;AACF;AAEA,IAAIG,IAAI,GAAG,mBAAmB;AAE9B,SAASC,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACrCP,aAAa,CAAC,IAAI,CAAC;EACnB,IAAIQ,IAAI,GAAGD,SAAS,CAACH,IAAI,CAAC,IAAIT,UAAU,CAACY,SAAS,CAAC;EACnDP,aAAa,CAAC,KAAK,CAAC;EACpBQ,IAAI,CAACf,MAAM,CAACa,IAAI,CAAC;EACjBC,SAAS,CAACH,IAAI,CAAC,GAAGI,IAAI;AACxB;AAEA,SAASC,YAAYA,CAACH,IAAI,EAAEC,SAAS,EAAE;EACrCf,WAAW,CAACc,IAAI,EAAEC,SAAS,CAAC;AAC9B;AACA;;AAGA,OAAO,SAASG,EAAEA,CAACJ,IAAI,EAAEC,SAAS,EAAE;EAClC,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOJ,YAAY,CAACH,IAAI,EAAEC,SAAS,CAAC;EACtC;AACF;AACA,OAAO,SAASd,MAAMA,CAACa,IAAI,EAAEC,SAAS,EAAE;EACtC,IAAIZ,UAAU,EAAE;IACdU,YAAY,CAACC,IAAI,EAAEC,SAAS,CAAC;IAC7B;EACF;EAEAE,YAAY,CAACH,IAAI,EAAEC,SAAS,CAAC;AAC/B,CAAC,CAAC;;AAEF,SAASO,aAAaA,CAACC,EAAE,EAAE;EACzB,OAAOC,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAC9C;AAEA,SAASF,cAAcA,CAAA,EAAG;EACxBA,cAAc,GAAG9B,iBAAiB,CAAE,aAAaD,mBAAmB,CAACkC,IAAI,CAAC,SAASC,OAAOA,CAACb,SAAS,EAAE;IACpG,OAAOtB,mBAAmB,CAACoC,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;MAC1D,OAAO,CAAC,EAAE;QACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;UACnC,KAAK,CAAC;YACJ,OAAOF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;cAClE,IAAIC,eAAe;cAEnB,CAACA,eAAe,GAAGvB,SAAS,CAACH,IAAI,CAAC,MAAM,IAAI,IAAI0B,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACC,OAAO,CAAC,CAAC;cAC/G,OAAOxB,SAAS,CAACH,IAAI,CAAC;YACxB,CAAC,CAAC,CAAC;UAEL,KAAK,CAAC;UACN,KAAK,KAAK;YACR,OAAOmB,QAAQ,CAACS,IAAI,CAAC,CAAC;QAC1B;MACF;IACF,CAAC,EAAEZ,OAAO,CAAC;EACb,CAAC,CAAC,CAAC;EACH,OAAOJ,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAC9C;AAEA,SAASe,aAAaA,CAAC1B,SAAS,EAAE;EAChCb,sBAAsB,CAACa,SAAS,CAAC;AACnC;AACA;;AAGA,OAAO,SAAS2B,EAAEA,CAAC3B,SAAS,EAAE;EAC5B,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOoB,aAAa,CAAC1B,SAAS,CAAC;EACjC;AACF;AACA,OAAO,SAASwB,OAAOA,CAACI,GAAG,EAAE;EAC3B,OAAOC,QAAQ,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AACxC;AAEA,SAASkB,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGlD,iBAAiB,CAAE,aAAaD,mBAAmB,CAACkC,IAAI,CAAC,SAASkB,QAAQA,CAAC9B,SAAS,EAAE;IAC/F,OAAOtB,mBAAmB,CAACoC,IAAI,CAAC,SAASiB,SAASA,CAACC,SAAS,EAAE;MAC5D,OAAO,CAAC,EAAE;QACR,QAAQA,SAAS,CAACf,IAAI,GAAGe,SAAS,CAACd,IAAI;UACrC,KAAK,CAAC;YACJ,IAAI,EAAE9B,UAAU,KAAK6C,SAAS,CAAC,EAAE;cAC/BD,SAAS,CAACd,IAAI,GAAG,CAAC;cAClB;YACF;YAEA,OAAOc,SAAS,CAACb,MAAM,CAAC,QAAQ,EAAEZ,aAAa,CAACP,SAAS,CAAC,CAAC;UAE7D,KAAK,CAAC;YACJ0B,aAAa,CAAC1B,SAAS,CAAC;UAE1B,KAAK,CAAC;UACN,KAAK,KAAK;YACR,OAAOgC,SAAS,CAACP,IAAI,CAAC,CAAC;QAC3B;MACF;IACF,CAAC,EAAEK,QAAQ,CAAC;EACd,CAAC,CAAC,CAAC;EACH,OAAOD,QAAQ,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}