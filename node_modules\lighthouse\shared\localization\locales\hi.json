{"core/audits/accessibility/accesskeys.js | description": {"message": "ऐक्सेस कुंजी की मदद से लोग, पेज के किसी हिस्से पर तुरंत फ़ोकस कर सकते हैं. सही नेविगेशन के लिए, हर ऐक्सेस कुंजी यूनीक होनी चाहिए. [ऐक्सेस कुंजियों के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` मान सबसे अलग नहीं हैं"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` के मान सबसे अलग हैं"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "`aria-*` एट्रिब्यूट के खास सबसेट पर, हर ARIA `role` काम करता है. इनमें मिलान न होने पर `aria-*` एट्रिब्यूट गलत हो जाते हैं. [ARIA एट्रिब्यूट का उनकी भूमिकाओं से मिलान करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` विशेषताएं और उनकी भूमिकाएं आपस में मेल नहीं खातीं"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` विशेषताएं और उनकी भूमिकाएं एक-दूसरे से मेल खाती हैं"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "अगर किसी एलिमेंट का ऐक्सेस किया जा सकने वाला नाम नहीं है, तो स्क्रीन रीडर उस एलिमेंट को किसी सामान्य नाम से बुलाते हैं. इस वजह से, वह एलिमेंट उन उपयोगकर्ताओं के लिए किसी काम का नहीं रहता जो स्क्रीन रीडर की मदद से ही टेक्स्ट पढ़ या समझ सकते हैं. [कमांड एलिमेंट को ज़्यादा से ज़्यादा लोगों तक पहुंचाने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link`, और `menuitem` एलिमेंट के नाम ऐक्सेस नहीं किए जा सकते."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link`, और `menuitem` एलिमेंट के नाम ऐक्सेस किए जा सकते हैं"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "जब `aria-hidden=\"true\"`, दस्तावेज़ `<body>` पर सेट होता है, तो स्क्रीन रीडर जैसी सहायक टेक्नोलॉजी हर समय एक जैसा काम नहीं करती. [जानें कि `aria-hidden` का दस्तावेज़ के मुख्य हिस्से पर क्या असर पड़ता है](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`<body>` दस्तावेज़ में `[aria-hidden=\"true\"]` मौजूद है"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`<body>` दस्तावेज़ में `[aria-hidden=\"true\"]` मौजूद नहीं है"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "किसी `[aria-hidden=\"true\"]` एलिमेंट में फ़ोकस करने लायक डिसेंडेंट, स्क्रीन रीडर जैसी सहायक टेक्नोलॉजी का इस्तेमाल करने वालों को उन इंटरैक्टिव एलिमेंट का इस्तेमाल करने से रोकते हैं. [जानें कि फ़ोकस करने लायक एलिमेंट पर, `aria-hidden` कैसे असर डालता है](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` एलिमेंट में फ़ोकस करने लायक डिसेंडेंट हैं"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` एलिमेंट में फ़ोकस करने लायक डिसेंडेंट नहीं हैं"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "अगर किसी इनपुट फ़ील्ड का ऐक्सेस किया जा सकने वाला नाम नहीं है, तो स्क्रीन रीडर उसे किसी सामान्य नाम से बुलाते हैं. इस वजह से, यह ऐसे लोगों के लिए किसी काम का नहीं रहता जो स्क्रीन रीडर से ही टेक्स्ट पढ़ या समझ सकते हैं. [इनपुट फ़ील्ड लेबल के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA इनपुट फ़ील्ड का कोई ऐसा नाम नहीं है जिसे ऐक्सेस किया जा सकता हो"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA इनपुट फ़ील्ड के ऐसे नाम हैं जिन्हें ऐक्सेस किया जा सकता है"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "अगर किसी मीटर एलिमेंट का नाम ऐसा है जिसे ऐक्सेस नहीं किया जा सकता, तो स्क्रीन रीडर उसे किसी सामान्य नाम से बुलाते हैं. इस वजह से, यह उन लोगों के लिए किसी काम का नहीं रहता जो स्क्रीन रीडर की मदद से ही टेक्स्ट पढ़ या समझ सकते हैं. [`meter` एलिमेंट को नाम देने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` एलिमेंट के नाम ऐक्सेस नहीं किए जा सकते."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` एलिमेंट के नाम ऐक्सेस किए जा सकते हैं"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "अगर किसी `progressbar` एलिमेंट का ऐक्सेस किया जा सकने वाला नाम न हो, तो स्क्रीन रीडर उसे किसी सामान्य नाम से बुलाते हैं. इस वजह से, यह उन लोगों के लिए किसी काम का नहीं रहता जो स्क्रीन रीडर की मदद से ही, टेक्स्ट पढ़ या समझ सकते हैं. [`progressbar` एलिमेंट को लेबल करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` एलिमेंट के नाम ऐक्सेस नहीं किए जा सकते."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` एलिमेंट के नाम ऐक्सेस किए जा सकते हैं"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "ARIA की कुछ भूमिकाओं में ऐसे एट्रिब्यूट की ज़रूरत होती है जो स्क्रीन रीडर को एलिमेंट की स्थिति के बारे में बताते हैं. [ज़रूरी एट्रिब्यूट और भूमिकाओं के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`s में सभी ज़रूरी `[aria-*]` विशेषताएं नहीं हैं"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` के पास सभी ज़रूरी `[aria-*]` विशेषताएं हैं"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "ARIA की कुछ पैरंट भूमिकाओं में खास चाइल्ड भूमिकाएं होनी चाहिए, ताकि वे तय किए गए सुलभता फ़ंक्शन कर सकें. [भूमिकाओं और ज़रूरी चाइल्ड एलिमेंट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "ARIA `[role]` वाले एलिमेंट में बच्चों के लिए खास `[role]` शामिल करने की ज़रूरत होती है. इन एलिमेंट में बच्चों के लिए कुछ या पूरी जानकारी नहीं है."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "ARIA `[role]` वाले एलिमेंट में बच्चों के लिए खास `[role]` शामिल करने की ज़रूरत होती है. इन एलिमेंट में बच्चों के लिए पूरी जानकारी है."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "ARIA की कुछ चाइल्ड भूमिकाओं को खास पैरंट भूमिकाओं में होना चाहिए, ताकि वे तय किए गए सुलभता फ़ंक्शन को ठीक से पूरा कर सकें. [ARIA भूमिकाओं और ज़रूरी पैरंट एलिमेंट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` अपने ज़रूरी पैरंट एलिमेंट में शामिल नहीं हैं"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`अपने लिए ज़रूरी पैरंट एलिमेंट में शामिल हैं."}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA भूमिकाओं की वैल्यू सही होनी चाहिए, ताकि वे तय किए गए सुलभता फ़ंक्शन कर सकें. [ARIA से जुड़ी सही भूमिकाओं के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` मान सही नहीं हैं"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` मान सही हैं"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "अगर किसी टॉगल फ़ील्ड के पास ऐक्सेस किया जा सकने वाला नाम नहीं है, तो स्क्रीन रीडर उसे किसी सामान्य नाम से बुलाते हैं. इस वजह से, यह स्क्रीन रीडर की मदद से टेक्स्ट पढ़ने या समझने वालों के लिए किसी काम का नहीं रहता. [टॉगल फ़ील्ड के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA टॉगल फ़ील्ड का कोई ऐसा नाम नहीं है जिसे ऐक्सेस किया जा सकता हो"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA टॉगल फ़ील्ड के ऐसे नाम हैं जिन्हें ऐक्सेस किया जा सकता है"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "अगर किसी टूलटिप एलिमेंट का नाम ऐसा है जिसे ऐक्सेस नहीं किया जा सकता, तो स्क्रीन रीडर उसे किसी सामान्य नाम से बुलाते हैं. इस वजह से, यह उन लोगों के लिए किसी काम का नहीं रहता जो स्क्रीन रीडर की मदद से ही टेक्स्ट पढ़ या समझ सकते हैं. [`tooltip` एलिमेंट को नाम देने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` एलिमेंट के नाम ऐक्सेस नहीं किए जा सकते."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` एलिमेंट के नाम ऐक्सेस किए जा सकते हैं"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "अगर किसी `treeitem` एलिमेंट का ऐक्सेस किया जा सकने वाला नाम न हो, तो स्क्रीन रीडर उसे किसी सामान्य नाम से बुलाते हैं. इस वजह से, यह उन लोगों के लिए किसी काम का नहीं रहता जो स्क्रीन रीडर की मदद से ही, टेक्स्ट पढ़ या समझ सकते हैं. [`treeitem` एलिमेंट को लेबल करने के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` एलिमेंट के नाम ऐक्सेस नहीं किए जा सकते."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` एलिमेंट के नाम ऐक्सेस किए जा सकते हैं"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "स्क्रीन रीडर जैसी सहायक टेक्नोलॉजी, गलत वैल्यू वाली ARIA एट्रिब्यूट को समझ नहीं सकतीं. [ARIA एट्रिब्यूट के लिए सही वैल्यू के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` विशेषताओं में सही मान नहीं हैं"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` विशेषताओं के मान सही हैं"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "स्क्रीन रीडर जैसी सहायक टेक्नोलॉजी, गलत नामों वाले ARIA एट्रिब्यूट को समझ नहीं सकतीं. [सही ARIA एट्रिब्यूट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` विशेषताएं गलत हैं या उनकी स्पेलिंग गलत है"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` विशेषताएं सही हैं और उनकी स्पेलिंग गलत नहीं है"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "फ़ेल होने वाले एलिमेंट"}, "core/audits/accessibility/button-name.js | description": {"message": "जब किसी बटन का ऐक्सेस किया जा सकने वाला नाम नहीं होता, तो स्क्रीन रीडर सिर्फ़ \"बटन\" कहता है. इस वजह से, स्क्रीन रीडर का इस्तेमाल करने वाले लोगों के लिए यह किसी काम का नहीं रहता. [बटन के इस्तेमाल को आसान बनाने के बारे में जानें](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "बटन के नाम ऐक्सेस करने लायक नहीं हैं"}, "core/audits/accessibility/button-name.js | title": {"message": "बटनों का एक सुलभता नाम है"}, "core/audits/accessibility/bypass.js | description": {"message": "दोहराव वाले कॉन्टेंट को बायपास करने के तरीके जोड़ने से, कीबोर्ड का इस्तेमाल करने वालों को, पेज को बेहतर तरीके से नेविगेट करने में मदद मिलती है. [कॉन्टेंट के ब्लॉक को बायपास करने के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "पेज में 'हेडिंग', 'स्किप लिंक' या 'लैंडमार्क' क्षेत्र नहीं है"}, "core/audits/accessibility/bypass.js | title": {"message": "पेज में 'हेडिंग', 'स्किप लिंक' या 'लैंडमार्क' क्षेत्र हैं"}, "core/audits/accessibility/color-contrast.js | description": {"message": "कई लोगों के लिए, कम कंट्रास्ट वाला टेक्स्ट पढ़ना मुश्किल या नामुमकिन होता है. [रंगों का सही कंट्रास्ट देने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "बैकग्राउंड और फ़ोरग्राउंड रंगों में काफ़ी कंट्रास्ट अनुपात नहीं है."}, "core/audits/accessibility/color-contrast.js | title": {"message": "बैकग्राउंड और फ़ोरग्राउंड के रंगों का कंट्रास्ट अनुपात काफ़ी है"}, "core/audits/accessibility/definition-list.js | description": {"message": "जब परिभाषा सूचियां ठीक से मार्क अप नहीं की जातीं, तो स्क्रीन रीडर उलझन भरे या गलत आउटपुट दे सकते हैं. [जानें कि परिभाषा की सूचियां सही तरीके से कैसे बनाई जाती हैं](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` में सिर्फ़ ठीक तरह से क्रम में लगे `<dt>` और `<dd>` ग्रुप, `<script>`, `<template>` या `<div>` एलिमेंट नहीं हैं."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` में सिर्फ़ ठीक तरह से क्रम से लगे `<dt>` और `<dd>` ग्रुप, `<script>`, `<template>` या `<div>` एलिमेंट हैं."}, "core/audits/accessibility/dlitem.js | description": {"message": "परिभाषा सूची के आइटम (`<dt>` और `<dd>`) को पैरंट `<dl>` एलिमेंट में रैप किया जाना चाहिए. इससे यह पक्का किया जा सकेगा कि स्क्रीन रीडर उन्हें सही तरीके से बोल सकें. [जानें कि परिभाषा की सूचियां सही तरीके से कैसे बनाई जाती हैं](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "परिभाषा सूची के आइटम `<dl>` एलिमेंट में रैप नहीं किए जाते"}, "core/audits/accessibility/dlitem.js | title": {"message": "परिभाषा सूची के आइटम `<dl>` एलिमेंट में रैप किए जाते हैं"}, "core/audits/accessibility/document-title.js | description": {"message": "टाइटल, स्क्रीन रीडर का इस्तेमाल करने वालों को पेज की खास जानकारी देता है. सर्च इंजन का इस्तेमाल करने वाले इस पर काफ़ी भरोसा करते हैं. टाइटल की मदद से, वे यह पता करते हैं कि कोई पेज उनकी खोज के हिसाब से काम का है या नहीं. [दस्तावेज़ के टाइटल के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "दस्तावेज़ में कोई `<title>` एलिमेंट नहीं है"}, "core/audits/accessibility/document-title.js | title": {"message": "दस्तावेज़ में `<title>` एलिमेंट है"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "फ़ोकस करने लायक सभी एलिमेंट में यूनीक `id` होना चाहिए, ताकि यह पक्का किया जा सके कि वे सहायक टेक्नोलॉजी को दिख रहे हों. [डुप्लीकेट `id` को ठीक करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "फ़ोकस करने लायक चालू एलिमेंट में `[id]` विशेषताएं खास नहीं हैं"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "फ़ोकस करने लायक चालू एलिमेंट में `[id]` विशेषताएं खास हैं"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA आईडी की वैल्यू सबसे यूनीक होनी चाहिए, ताकि सहायक टेक्नोलॉजी दूसरे इंस्टेंस को नज़रअंदाज़ न कर दें. [डुप्लीकेट ARIA आईडी ठीक करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA आईडी खास नहीं हैं"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA आईडी खास हैं"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "स्क्रीन रीडर जैसी सहायक टेक्नोलॉजी, एक से ज़्यादा लेबल वाले फ़ॉर्म फ़ील्ड का नाम गलत तरीके से बोल सकती हैं. ये टेक्नोलॉजी सिर्फ़ पहला, आखिरी या सभी लेबल इस्तेमाल करती हैं. [फ़ॉर्म लेबल इस्तेमाल करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "फ़ॉर्म फ़ील्ड के कई लेबल हैं"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "किसी भी फ़ॉर्म फ़ील्ड में एक से ज़्यादा लेबल नहीं हैं"}, "core/audits/accessibility/frame-title.js | description": {"message": "फ़्रेम के कॉन्टेंट के बारे में जानने के लिए, स्क्रीन रीडर का इस्तेमाल करने वाले लोग फ़्रेम के टाइटल पर भरोसा करते हैं. [फ़्रेम के टाइटल के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` या `<iframe>` एलिमेंट का कोई शीर्षक नहीं है"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` या `<iframe>` एलिमेंट का एक शीर्षक है"}, "core/audits/accessibility/heading-order.js | description": {"message": "सही क्रम में लगाई गई ऐसी हेडिंग जो लेवल को नहीं छोड़तीं वे पेज का सिमेंटिक स्ट्रक्चर दिखाती हैं. इससे सहायक टेक्नोलॉजी इस्तेमाल करते समय, पेज पर नेविगेट करना और उसे समझना आसान हो जाता है. [हेडिंग के क्रम के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "हेडिंग एलिमेंट, कम होने वाले क्रम में नहीं हैं"}, "core/audits/accessibility/heading-order.js | title": {"message": "हेडिंग एलिमेंट, कम होने वाले क्रम में दिखते हैं"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "अगर कोई पेज, `lang` एट्रिब्यूट को तय नहीं करता, तो स्क्रीन रीडर को लगता है कि पेज उस डिफ़ॉल्ट भाषा में है जिसे इस्तेमाल करने वाले ने स्क्रीन रीडर सेट अप करते समय चुना था. अगर पेज डिफ़ॉल्ट भाषा में नहीं है, तो हो सकता है कि स्क्रीन रीडर, पेज के टेक्स्ट को ठीक से न बोल पाए. [`lang` एट्रिब्यूट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` एलिमेंट में `[lang]` विशेषता नहीं है"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` एलिमेंट में `[lang]` विशेषता है"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "सही [BCP 47 भाषा ](https://www.w3.org/International/questions/qa-choosing-language-tags#question) तय करने से, स्क्रीन रीडर को टेक्स्ट ठीक से बोलने में मदद मिलती है. [`lang` एट्रिब्यूट को इस्तेमाल करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` एलिमेंट के पास अपनी `[lang]`विशेषता के लिए कोई सही मान नहीं है."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` एलिमेंट के पास अपनी `[lang]` विशेषता के लिए एक सही मान है"}, "core/audits/accessibility/image-alt.js | description": {"message": "जानकारी वाले एलिमेंट में, छोटा और ब्यौरे वाला वैकल्पिक टेक्स्ट होना चाहिए. सजावटी एलिमेंट में एक खाली ऑल्ट एट्रिब्यूट का इस्तेमाल किया जा सकता है. [`alt` एट्रिब्यूट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "इमेज एलिमेंट में `[alt]` विशेषताएं नहीं हैं"}, "core/audits/accessibility/image-alt.js | title": {"message": "इमेज एलिमेंट में `[alt]` विशेषताएं शामिल हैं"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "जब किसी इमेज को `<input>` बटन के रूप में इस्तेमाल किया जाता है, तब वैकल्पिक टेक्स्ट देने से, स्क्रीन रीडर इस्तेमाल करने वालों को उस बटन के मकसद को समझने में मदद मिलती है. [इनपुट इमेज के वैकल्पिक टेक्स्ट के बारे में जानें](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` एलिमेंट में `[alt]` टेक्स्ट नहीं है"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` एलिमेंट में`[alt]` टेक्स्ट है"}, "core/audits/accessibility/label.js | description": {"message": "लेबल यह पक्का करते हैं कि स्क्रीन रीडर जैसी सहायक टेक्नोलॉजी की मदद से, फ़ॉर्म कंट्रोल का नाम सही तरीके से बोला जाए. [फ़ॉर्म एलिमेंट के लेबल के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "फ़ॉर्म एलिमेंट में जुड़े हुए लेबल नहीं हैं"}, "core/audits/accessibility/label.js | title": {"message": "फ़ॉर्म एलिमेंट में सहभागी लेबल हैं"}, "core/audits/accessibility/link-name.js | description": {"message": "समझने लायक, यूनीक, और फ़ोकस करने लायक लिंक टेक्स्ट (और इमेज के लिए इस्तेमाल किया जाने वाला वैकल्पिक टेक्स्ट, जब लिंक के तौर पर इस्तेमाल किया जाए) की मदद से, स्क्रीन रीडर इस्तेमाल करने वालों का नेविगेशन अनुभव बेहतर होता है. [लिंक को ऐक्सेस करने लायक बनाने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "लिंक का समझने लायक नहीं है"}, "core/audits/accessibility/link-name.js | title": {"message": "लिंक का समझने लायक नाम है"}, "core/audits/accessibility/list.js | description": {"message": "स्क्रीन रीडर, सूचियों को एक खास तरीके से बोलते हैं. सूची की बनावट सही होने से स्क्रीन रीडर को आउटपुट देने में मदद मिलती है. [सूची की सही बनावट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "सूचियों में सिर्फ़ `<li>` एलिमेंट और स्क्रिप्ट के साथ काम करने वाले एलिमेंट (`<script>`और `<template>`) ही शामिल नहीं हैं."}, "core/audits/accessibility/list.js | title": {"message": "सूची में सिर्फ़ `<li>` एलिमेंट और स्क्रिप्ट के साथ काम करने वाले एलिमेंट शामिल हैं (`<script>` और `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "स्क्रीन रीडर के लिए ज़रूरी है कि उनमें `<ul>`, `<ol>` या `<menu>` पैरंट में सूची आइटम (`<li>`) शामिल हों, ताकि उन्हें ठीक तरह से बोला जा सके. [सूची की सही बनावट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "सूची आइटम (`<li>`), `<ul>`, `<ol>` या `<menu>` पैरंट एलिमेंट में शामिल नहीं हैं."}, "core/audits/accessibility/listitem.js | title": {"message": "सूची वाले आइटम (`<li>`), `<ul>`, `<ol>` या `<menu>` पैरंट एलिमेंट में हैं"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "लोगों को यह उम्मीद नहीं होती कि कोई पेज अपने-आप रीफ़्रेश हो जाएगा. हालांकि, जब ऐसा होता है, तो लोगों का फ़ोकस वापस पेज के सबसे ऊपर के हिस्से पर चला जाता है. इससे निराशा या भ्रम की स्थिति का अनुभव हो सकता है. [रीफ़्रेश मेटा टैग के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "यह दस्तावेज़ `<meta http-equiv=\"refresh\">` का इस्तेमाल करता है"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "दस्तावेज़ `<meta http-equiv=\"refresh\">` का इस्तेमाल नहीं करते"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "ज़ूम की सुविधा बंद करने से, उन लोगों को परेशानी होती है जिन्हें कम दिखता है. ऐसे लोग वेब पेज के कॉन्टेंट को ठीक तरह से देखने के लिए, स्क्रीन को ज़ूम करने की सुविधा पर निर्भर रहते हैं. [व्यूपोर्ट मेटा टैग के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` का इस्तेमाल `<meta name=\"viewport\">` एलिमेंट में किया जाता है या `[maximum-scale]`विशेषता पांच से कम है."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` का इस्तेमाल `<meta name=\"viewport\">` एलिमेंट में नहीं किया जाता और `[maximum-scale]`विशेषता पाँच से कम नहीं है."}, "core/audits/accessibility/object-alt.js | description": {"message": "स्क्रीन रीडर बिना टेक्स्ट वाले कॉन्टेंट का अनुवाद नहीं कर सकता. `<object>` एलिमेंट में वैकल्पिक टेक्स्ट जोड़ने पर, स्क्रीन रीडर की मदद से उपयोगकर्ताओं को कॉन्टेंट का मतलब आसानी से समझ आ जाता है. [`object` एलिमेंट के लिए वैकल्पिक टेक्स्ट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` एलिमेंट में वैकल्पिक टेक्स्ट नहीं है"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` एलिमेंट में वैकल्पिक टेक्स्ट है"}, "core/audits/accessibility/tabindex.js | description": {"message": "शून्य से ज़्यादा की वैल्यू साफ़ तौर पर, नेविगेशन के क्रम को दिखाती है. हालांकि, यह वैल्यू तकनीकी रूप से मान्य है. इसके बावजूद, सहायक टेक्नोलॉजी पर भरोसा करने वाले लोगों को यह वैल्यू अक्सर परेशान करती है. [`tabindex` एट्रिब्यूट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "कुछ एलिमेंट का `[tabindex]` मान 0 से ज़्यादा होता है"}, "core/audits/accessibility/tabindex.js | title": {"message": "किसी भी एलिमेंट का `[tabindex]` मान 0 से ज़्यादा नहीं हो सकता"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "स्क्रीन रीडर में ऐसी सुविधाएं होती हैं जिनकी मदद से, टेबल पर नेविगेट करना आसान हो जाता है. `[headers]` एट्रिब्यूट का इस्तेमाल करके, `<td>` सेल की मौजूदगी पक्की करने के लिए, उसी टेबल में मौजूद दूसरे सेल का हवाला दिया जाता है. इससे स्क्रीन रीडर का इस्तेमाल करने वालों के अनुभव को बेहतर बनाया जा सकता है. [`headers` एट्रिब्यूट के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`<table>` एलिमेंट में शामिल सेल जिस `[headers]` विशेषता का इस्तेमाल करते हैं वे उस एलिमेंट `id` का हवाला देते हैं जाे उसी टेबल में शामिल नहीं हाेता."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`<table>` एलिमेंट में शामिल सेल जो `[headers]` विशेषता का इस्तेमाल करते हैं वे सिर्फ़ उसी टेबल में शामिल सेल का हवाला देते हैं."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "स्क्रीन रीडर में ऐसी सुविधाएं होती हैं जिनकी मदद से, टेबल पर नेविगेट करना आसान हो जाता है. यह पक्का करें कि टेबल हेडर हमेशा सेल के कुछ सेट के बारे में बताते हों. इससे स्क्रीन रीडर का इस्तेमाल करने वालों को बेहतर अनुभव मिल सकता है. [टेबल हेडर के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` एलिमेंट और `[role=\"columnheader\"/\"rowheader\"]` वाले एलिमेंट में वे डेटा सेल मौजूद नहीं हैं जो उनके ब्यौरे में शामिल हैं."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` एलिमेंट और `[role=\"columnheader\"/\"rowheader\"]` वाले एलिमेंट में वे डेटा सेल शामिल हैं जिनकी वे जानकारी देते हैं."}, "core/audits/accessibility/valid-lang.js | description": {"message": "एलिमेंट पर एक सही [BCP 47 भाषा ](https://www.w3.org/International/questions/qa-choosing-language-tags#question) तय करने से, यह पक्का किया जा सकता है कि स्क्रीन रीडर, टेक्स्ट को सही से बोले. [`lang` एट्रिब्यूट को इस्तेमाल करने का तरीका जानें](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` विशेषताओं का कोई सही मान नहीं है"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` विशेषताओं का मान सही है"}, "core/audits/accessibility/video-caption.js | description": {"message": "जब किसी वीडियो में कैप्शन की सुविधा उपलब्ध होती है, तो ऐसे लोग जो सुन नहीं सकते और जिन्हें सुनने में परेशानी है उनके लिए वीडियो को समझना आसान हो जाता है. [वीडियो कैप्शन के बारे में ज़्यादा जानें](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` एलिमेंट में `[kind=\"captions\"]` वाला कोई `<track>` एलिमेंट नहीं है."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` एलिमेंट में `[kind=\"captions\"]` वाला एक `<track>` एलिमेंट है"}, "core/audits/autocomplete.js | columnCurrent": {"message": "मौजूदा मान"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "सुझाया गया टोकन"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` की मदद से, लोग फटाफट फ़ॉर्म सबमिट कर सकते हैं. लोगों की मेहनत कम करने के लिए, `autocomplete` एट्रिब्यूट को सही वैल्यू पर सेट करके चालू करें. [फ़ॉर्म में `autocomplete` के बारे में ज़्यादा जानें](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` एलिमेंट में सही `autocomplete` एट्रिब्यूट नहीं हैं"}, "core/audits/autocomplete.js | manualReview": {"message": "मैन्युअल समीक्षा की ज़रूरत है"}, "core/audits/autocomplete.js | reviewOrder": {"message": "टोकन के लिए किए गए अपने ऑर्डर की समीक्षा करें"}, "core/audits/autocomplete.js | title": {"message": "`<input>` एलिमेंट, `autocomplete` का ठीक से इस्तेमाल कर रहे हैं"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` टोकन: {snippet} में \"{token}\" अमान्य है"}, "core/audits/autocomplete.js | warningOrder": {"message": "टोकन के ऑर्डर की समीक्षा करें: {snippet} में \"{tokens}\""}, "core/audits/bf-cache.js | actionableFailureType": {"message": "कार्रवाई की जा सकती है"}, "core/audits/bf-cache.js | description": {"message": "बहुत से नेविगेशन, पिछले पेज पर जाकर या दोबारा मौजूदा पेज पर जाकर परफ़ॉर्म किए जाते हैं. बैक/फ़ॉरवर्ड कैश मेमोरी (bfcache) की सुविधा की वजह से, पीछे वाले पेजों पर तेज़ी से नेविगेट किया जा सकता है. [bfcache के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{सफल न होने की 1 वजह}one{सफल न होने की # वजह}other{सफल न होने की # वजहें}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "इंस्टॉल न हो पाने की वजह"}, "core/audits/bf-cache.js | failureTitle": {"message": "पेज ने, बैक-फ़ॉरवर्ड कैश मेमोरी सुविधा को पहले जैसा करने से रोका"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "असफलता किस तरह की है"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "कार्रवाई नहीं की जा सकती"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "ब्राउज़र के लिए सहायता को मंज़ूरी मिलना बाकी है"}, "core/audits/bf-cache.js | title": {"message": "पेज ने, बैक-फ़ॉरवर्ड कैश मेमोरी सुविधा को पहले जैसा करने से नहीं रोका"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome एक्सटेंशन ने इस पेज के लोड परफ़ॉर्मेंस पर नकारात्मक रूप से असर डाला है. 'गुप्त मोड' में या बिना किसी एक्सटेंशन के Chrome प्रोफ़ाइल से पेज ऑडिट करके देखें."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "स्क्रिप्ट मूल्यांकन"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "स्क्रिप्ट पार्स"}, "core/audits/bootup-time.js | columnTotal": {"message": "कुल सीपीयू समय"}, "core/audits/bootup-time.js | description": {"message": "JS (JavaScript) को पार्स, कंपाइल, और एक्ज़ीक्यूट करने में लगने वाला समय कम करें. छोटे-छोटे JS पेलोड डिलीवर करने से, इसमें आपको मदद मिल सकती है. [JavaScript की कार्रवाई में लगने वाला समय कम करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "JavaScript क्रियान्वयन समय कम करें"}, "core/audits/bootup-time.js | title": {"message": "JavaScript क्रियान्वयन समय"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "बंडल से बड़े और डुप्लीकेट JavaScript मॉड्यूल हटाएं. ऐसा करके, नेटवर्क गतिविधि में खर्च होने वाले गैर-ज़रूरी बाइट कम किए जा सकते हैं. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "JavaScript बंडल में मौजूद डुप्लीकेट मॉड्यूल हटाएं"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "बड़े GIF से ऐनिमेशन वाला कॉन्टेंट नहीं बनाया जा सकता. नेटवर्क बाइट बचाने के लिए, GIF का इस्तेमाल करने के बजाय, ऐनिमेशन के लिए MPEG4/WebM वीडियो फ़ॉर्मैट और स्टैटिक इमेज के लिए, PNG/WebP फ़ॉर्मैट का इस्तेमाल करें. [वीडियो के बेहतर फ़ॉर्मैट के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "ऐनिमेट की गई सामग्री के लिए वीडियो फ़ॉर्मेट का इस्तेमाल करें"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "पॉलीफ़िल और ट्रांसफ़ॉर्म की मदद से, लेगसी ब्राउज़र पर JavaScript की नई सुविधाएं इस्तेमाल की जा सकती हैं. हालांकि, मॉडर्न ब्राउज़र के लिए इनमें से कई सुविधाओं की ज़रूरत नहीं होती. बंडल किए गए JavaScript के लिए, मॉडर्न स्क्रिप्ट डिप्लॉयमेंट रणनीति अपनाएं. इसके लिए, मॉड्यूल/नोमॉड्यूल सुविधा वाले पहचान टूल का इस्तेमाल करें. इससे मॉडर्न ब्राउज़र पर भेजे जाने वाले कोड की संख्या कम करने और लेगसी ब्राउज़र को चालू रखने में मदद मिलती है. [नया JavaScript इस्तेमाल करने का तरीका जानें](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "पुरानी JavaScript को नए ब्राउज़र में खोलने से बचें"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "WebP और AVIF जैसे इमेज फ़ॉर्मैट, PNG या JPEG की तुलना में, अक्सर बेहतर तरीके से कंप्रेस करने की सुविधा देते हैं. इससे इमेज तेज़ी से डाउनलोड होती है और डेटा का खर्च भी कम होता है. [इमेज के नए फ़ॉर्मैट के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "इमेज अगली जेनरेशन के फ़ॉर्मेट में ऑफ़र करें"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "इंटरैक्टिव में लगने वाला समय कम करने के लिए, सभी अहम संसाधन लोड होने के बाद, लेज़ी लोडिंग की मदद से ऑफ़स्क्रीन और छिपी हुई इमेज लोड करें. [ऑफ़स्क्रीन इमेज को कुछ समय के लिए रोकने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "ऑफ़स्क्रीन इमेज टालें"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "संसाधन आपके पेज का फ़र्स्ट पेंट ब्लॉक कर रहे हैं. हमारा सुझाव है कि ज़रूरी JS/सीएसएस इनलाइन को डिलीवर करें और गैर-ज़रूरी JS/स्टाइल को कुछ समय के लिए रोकें. [रेंडर ब्लॉक करने से जुड़े संसाधनों को हटाने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "रेंडर ब्लॉक करने वाले संसाधनों को खत्म करें"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "बड़े नेटवर्क वाले पेलोड के लिए लोगों को रकम खर्च करनी होती है. साथ ही, उन पर लोड होने में ज़्यादा समय लगता है. [पेलोड के साइज़ को कम करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "कुल साइज़ {totalBytes, number, bytes} किबीबाइट था"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "बहुत ज़्यादा नेटवर्क पेलोड से बचें"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "भारी नेटवर्क पेलोड से बचाता है"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "सीएसएस फ़ाइलों को छोटा करने से नेटवर्क पेलोड के साइज़ कम किए जा सकते हैं. [सीएसएस फ़ाइलों का साइज़ कम करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS कम करें"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript फ़ाइलों को छोटा करने से, पेलोड का साइज़ और स्क्रिप्ट पार्स करने में लगने वाला समय कम हो सकता है. [JavaScript को छोटा करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript का आकार कम करें"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "स्टाइलशीट से ऐसे नियमों को कम करें जिनका इस्तेमाल नहीं किया जाता. साथ ही, पेज के ऊपरी हिस्से वाले कॉन्टेंट के लिए जो सीएसएस इस्तेमाल न हुए हों उन्हें अभी लोड न होने दें. इससे नेटवर्क गतिविधि में इस्तेमाल होने वाले बाइट कम होंगे. [इस्तेमाल न होने वाले सीएसएस को कम करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "इस्तेमाल न किए गए सीएसएस को कम करना"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "इस्तेमाल न किए गए JavaScript को कम करें और स्क्रिप्ट को तब तक लोड न करें, जब तक उनके लिए नेटवर्क गतिविधि में खर्च होने वाले बाइट को कम करना ज़रूरी न हो जाए. [इस्तेमाल नहीं किए गए JavaScript को कम करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "इस्तेमाल न किए गए JavaScript को कम करना"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "अगर कैश मेमोरी ज़्यादा समय तक बनी रहे, तो लोगों के आपके पेज पर बार-बार लौटकर आने की प्रक्रिया में तेज़ी आती है. [कैश मेमोरी से जुड़ी बेहतर नीतियों के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 संसाधन मिला}one{# संसाधन मिले}other{# संसाधन मिले}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "कुशल कैश नीति के साथ स्थिर एसेट ऑफ़र करें"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "स्थिर एसेट पर कुशल कैश नीति का इस्तेमाल करता है"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "ऑप्टिमाइज़ की गई इमेज तेज़ी से लोड होती हैं. इसमें मोबाइल डेटा का खर्च भी कम होता है. [इमेज को बेहतर ढंग से कोड में बदलने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "इमेज को कुशलता से एन्कोड करें"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "असल डाइमेंशन"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "दिखाए गए डाइमेंशन"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "इमेज अपने दिखाए गए साइज़ के हिसाब से बड़ी थीं"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "इमेज अपने दिखाए गए साइज़ के हिसाब से सही थीं"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "ब्राउज़र में ऐसी इमेज दिखाएं जिनका साइज़ सही हो. इससे सेल्युलर डेटा बचाया जा सकता है और लोड होने में लगने वाला समय भी कम किया जा सकता है. [इमेज का साइज़ सेट करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "सही तरीके के आकार वाली इमेज"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "नेटवर्क में बाइट को कम से कम खर्च करने के लिए, टेक्स्ट आधारित संसाधन, कंप्रेशन (gzip, deflate या brotli) के साथ ऑफ़र किए जाने चाहिए. [टेक्स्ट कंप्रेस करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "लेख कंप्रेशन चालू करें"}, "core/audits/content-width.js | description": {"message": "अगर आपके ऐप्लिकेशन के कॉन्टेंट की चौड़ाई का मिलान, व्यूपोर्ट की चौड़ाई से नहीं होता, तो हो सकता है कि आपका ऐप्लिकेशन, मोबाइल स्क्रीन पर ऑप्टिमाइज़ न किया जा सके. [व्यूपोर्ट के हिसाब से कॉन्टेंट के साइज़ को सेट करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "{innerWidth}px के व्यूपोर्ट का आकार {outerWidth}px की विंडो के आकार से मेल नहीं खाता है."}, "core/audits/content-width.js | failureTitle": {"message": "सामग्री का आकार व्यूपोर्ट (किसी वेब पेज के स्क्रीन पर दिखने वाले हिस्से) के मुताबिक नहीं है"}, "core/audits/content-width.js | title": {"message": "सामग्री का आकार व्यूपोर्ट (किसी वेब पेज के स्क्रीन पर दिखने वाले हिस्से) के मुताबिक है"}, "core/audits/critical-request-chains.js | description": {"message": "नीचे दी गई अहम अनुरोध चेन बताती हैं कि किन संसाधनों को ज़्यादा प्राथमिकता से लोड किया गया है. चेन का और डाउनलोड किए गए रिसॉर्स का साइज़ कम करें या गैर-ज़रूरी रिसॉर्स को फ़िलहाल डाउनलोड न करें. इससे पेज लोड होने की प्रोसेस को बेहतर बनाया जा सकता है. [अहम अनुरोधों की चेन बनाने से बचने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 शृंखला मिली}one{# शृंखलाएं मिलीं}other{# शृंखलाएं मिलीं}}"}, "core/audits/critical-request-chains.js | title": {"message": "गंभीर अनुरोधों की चेन न बनाएं"}, "core/audits/csp-xss.js | columnDirective": {"message": "डायरेक्टिव"}, "core/audits/csp-xss.js | columnSeverity": {"message": "गंभीरता"}, "core/audits/csp-xss.js | description": {"message": "कॉन्टेंट की सुरक्षा से जुड़ी एक मज़बूत नीति (सीएसपी), क्रॉस-साइट स्क्रिप्टिंग (XSS) के हमलाें के खतरे को काफ़ी हद तक कम कर देती है. [XSS को रोकने के लिए सीएसपी इस्तेमाल करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "सिंटैक्स"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "इस पेज पर एक <meta> टैग में सीएसपी की जानकारी दी गई है. सीएसपी को एचटीटीपी हेडर पर ले जाएं या एचटीटीपी हेडर में एक अन्य सख्त सीएसपी की जानकारी दें."}, "core/audits/csp-xss.js | noCsp": {"message": "एनफ़ोर्समेंट मोड में कोई CSP नहीं मिला"}, "core/audits/csp-xss.js | title": {"message": "पक्का करें कि CSP, XSS हमलों से बेहतर तरीके से सुरक्षा देता है"}, "core/audits/deprecations.js | columnDeprecate": {"message": "रुक गया है / चेतावनी"}, "core/audits/deprecations.js | columnLine": {"message": "लाइन"}, "core/audits/deprecations.js | description": {"message": "पाबंदी वाले एपीआई को ब्राउज़र से हटा दिया जाएगा. [पाबंदी वाले एपीआई के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 चेतावनी मिली}one{# चेतावनियां मिलीं}other{# चेतावनियां मिलीं}}"}, "core/audits/deprecations.js | failureTitle": {"message": "रुके हुए एपीआई का इस्तेमाल करता है"}, "core/audits/deprecations.js | title": {"message": "रुके हुई एपीआई का इस्तेमाल नहीं किया जाता"}, "core/audits/dobetterweb/charset.js | description": {"message": "कैरेक्टर एन्कोडिंग को सेट करना ज़रूरी है. ऐसा `<meta>` टैग की मदद से, एचटीएमएल की शुरुआती 1024 बाइट या कॉन्टेंट-टाइप एचटीटीपी रिस्पॉन्स हेडर में किया जा सकता है. [कैरेक्टर एन्कोडिंग सेट करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "एचटीएमएल में charset को सेट नहीं है या बहुत देरी से सेट किया गया है."}, "core/audits/dobetterweb/charset.js | title": {"message": "सही तरीके से सेट किया गया charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "doctype को तय करने से, ब्राउज़र क्वर्क-मोड (पुराने वर्शन पर काम करने की सुविधा) पर स्विच नहीं करता. [doctype की जानकारी देने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Doctype नाम स्ट्रिंग `html` होना चाहिए"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "दस्तावेज़ में `limited-quirks-mode` ट्रिगर करने वाला एक `doctype` शामिल है"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "दस्तावेज़ में doctype होना ज़रूरी है"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "उम्मीद थी कि publicId खाली स्ट्रिंग होगी"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "उम्मीद थी कि systemId खाली स्ट्रिंग होगी"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "दस्तावेज़ में `quirks-mode` ट्रिगर करने वाला एक `doctype` शामिल है"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "पेज में HTML doctype नहीं है जिसकी वजह से क्वर्क-मोड (पुराने वर्शन पर काम करने की सुविधा) ट्रिगर हो रही है"}, "core/audits/dobetterweb/doctype.js | title": {"message": "पेज में एचटीएमएल doctype है"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "आंकड़े"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "मान"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "ज़्यादा बड़े डॉक्यूमेंट ऑब्जेक्ट मॉडल (DOM) से मेमोरी का इस्तेमाल बढ़ सकता है. इससे [स्टाइल कैलकुलेशन](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) लंबे हो सकते हैं और [लेआउट रीफ़्लो](https://developers.google.com/speed/articles/reflow) महंगा हो सकता है. [बड़े साइज़ के DOM से बचने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 ऐलिमेंट}one{# ऐलिमेंट}other{# ऐलिमेंट}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "बहुत ज़्यादा बड़े DOM आकार से बचें"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "सबसे ज़्यादा DOM गहराई"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "कुल डीओएम ऐलिमेंट"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "ज़्यादातर बच्चों की चीजें"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "बहुत ज़्यादा बड़े DOM आकार से बचता है"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "ऐसी साइटों पर लोग भरोसा नहीं करते या उनकी वजह से उलझन में रहते हैं जो बिना किसी संदर्भ के, उनकी जगह की जानकारी पता करने का अनुरोध करती हैं. इसके बजाय, लोगों की कार्रवाई के आधार पर अनुरोध करें. [किसी भौगोलिक स्थान की अनुमति के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "पेज लोड पर भौगोलिक स्थान जानने का अनुरोध किया जाता है"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "पेज लोड पर भौगोलिक स्थान जानने की मंज़ूरी का अनुरोध नहीं किया जाता"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "इस तरह की समस्या है"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome डेवलपर टूल के `Issues` पैनल में मौजूद वेबसाइट से जुड़ी समस्याएं उन मुश्किलों की तरफ़ इशारा करती हैं जिनका समाधान करना बाकी है. ये मुश्किलें, नेटवर्क अनुरोधों के काम न करने और ब्राउज़र से जुड़ी अन्य वजहों से हो सकती हैं. साथ ही, ये तब भी हो सकती हैं, जब सुरक्षा कंट्रोल काफ़ी न हों. हर समस्या के बारे में ज़्यादा जानकारी पाने के लिए, Chrome डेवलपर टूल में 'समस्याएं' पैनल खोलें."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Chrome डेवलपर टूल के `Issues` पैनल में वेबसाइट से जुड़ी समस्याएं मौजूद हैं"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "क्रॉस-ओरिजन नीति ने ब्लॉक किया"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "विज्ञापनों में बड़े साइज़ के संसाधनों का इस्तेमाल"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome डेवलपर टूल के `Issues` पैनल में वेबसाइट से जुड़ी कोई भी समस्या मौजूद नहीं है"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "वर्शन"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "सभी फ़्रंट-एंड JavaScript लाइब्रेरी जिनकी पहचान इस पेज पर की गई है. [JavaScript लाइब्रेरी की पहचान करने की सुविधा के इस डाइग्नोस्टिक्स ऑडिट के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "पहचानी गई JavaScript लाइब्रेरी"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "धीमा इंटरनेट कनेक्शन इस्तेमाल करने वाले लोगों को, `document.write()` के ज़रिए डाइनैमिक तरीके से इंजेक्ट की गई बाहरी स्क्रिप्ट की वजह से, पेज लोड होने में 10 सेकंड से ज़्यादा देरी हो सकती है. [document.write() से बचने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` से बचें"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` का इस्तेमाल नहीं किया जाता"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "ऐसी साइटों पर लोग भरोसा नहीं करते या उनकी वजह से उलझन में रहते हैं जो बिना किसी संदर्भ के उन्हें सूचनाएं भेजने का अनुरोध करती हैं. इसके बजाय, इस तरह अनुरोध करें कि वह लोगों के ऐक्शन के साथ कनेक्ट हो. [सूचनाओं के लिए, ज़िम्मेदारी से अनुमति पाने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "पेज लोड पर सूचनाओं की अनुमति पाने का अनुरोध किया जाता है"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "पेज लोड पर सूचना भेजने की मंज़ूरी का अनुरोध नहीं किया जाता"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "प्रोटोकॉल"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "एचटीटीपी/1.1 की तुलना में एचटीटीपी/2 ज़्यादा फ़ायदे देता है. इनमें बाइनरी हेडर और मल्टिप्लेक्सिंग शामिल हैं. [एचटीटीपी/2 के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 अनुरोध HTTP/2 से सर्व नहीं किया गया}one{# अनुरोध HTTP/2 से सर्व नहीं किए गए}other{# अनुरोध HTTP/2 से सर्व नहीं किए गए}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2 का इस्तेमाल करें"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "अपने 'टच ऐंड व्हील' इवेंट लिसनर को `passive` पर रखें, ताकि आपके पेज को स्क्रोल करने की परफ़ॉर्मेंस बेहतर बनाई जा सके. [पैसिव इवेंट लिसनर का इस्तेमाल करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "स्क्रोल परफ़ॉर्मेंस बेहतर करने के लिए पैसिव श्रोताओं का इस्तेमाल नहीं करता"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "पैसिव श्रोताओं की मदद से स्क्रोल परफ़ॉर्मेंस बेहतर की जाती है"}, "core/audits/errors-in-console.js | description": {"message": "कंसोल में लॉग की गई गड़बड़ियां, उन मुश्किलों की तरफ़ इशारा करती हैं जिन्हें हल किया जाना अभी बाकी है. ये गड़बड़ियां, नेटवर्क अनुरोधों के काम न करने और ब्राउज़र से जुड़ी दूसरी समस्याओं की वजह से हो सकती हैं. [कंसोल डाइग्नोस्टिक्स ऑडिट में, इस गड़बड़ी के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "ब्राउज़र की गड़बड़ियां कंसोल में लॉग की गईं"}, "core/audits/errors-in-console.js | title": {"message": "ब्राउज़र की किसी गड़बड़ी को कंसोल में लॉग नहीं किया गया"}, "core/audits/font-display.js | description": {"message": "`font-display` सीएसएस सुविधा का इस्तेमाल करें, ताकि वेबफ़ॉन्ट लोड होने के दौरान, लोगों को टेक्स्ट दिखता रहे. [`font-display` के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "पक्का करें कि वेबफ़ॉन्ट लोड होने के दौरान लेख दिखाई देता रहे"}, "core/audits/font-display.js | title": {"message": "वेबफ़ॉन्ट लोड होने के दौरान सभी लेख दिखाई देते रहते हैं"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse, अपने-आप मूल {fontOrigin} के लिए `font-display` का मान नहीं देख सका.}one{Lighthouse, अपने-आप मूल {fontOrigin} के लिए `font-display` का मान नहीं देख सका.}other{Lighthouse, अपने-आप मूल {fontOrigin} के लिए `font-display` के मान नहीं देख सका.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "चौड़ाई-ऊंचाई का अनुपात (असल)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "चौड़ाई-ऊंचाई का अनुपात (डिसप्ले किया गया)"}, "core/audits/image-aspect-ratio.js | description": {"message": "इमेज डिसप्ले का डाइमेंशन, नैचुरल आसपेक्ट रेशियो (लंबाई-चौड़ाई का अनुपात) से मिलान होना चाहिए. [इमेज के आसपेक्ट रेशियो (लंबाई-चौड़ाई का अनुपात) के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "चौड़ाई-ऊंचाई के गलत अनुपात वाली इमेज दिखाता है"}, "core/audits/image-aspect-ratio.js | title": {"message": "चौड़ाई-ऊंचाई के सही अनुपात वाली इमेज दिखाता है"}, "core/audits/image-size-responsive.js | columnActual": {"message": "इमेज का असल साइज़"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "इमेज का दिखाया गया साइज़"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "इमेज का अनुमानित साइज़"}, "core/audits/image-size-responsive.js | description": {"message": "इमेज का नैचुरल डाइमेंशन उसके डिसप्ले साइज़ और पिक्सल के अनुपात में होना चाहिए, ताकि इमेज ज़्यादा से ज़्यादा साफ़ दिख सके. [रिस्पॉन्सिव इमेज देने का तरीका जानें](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "पेज पर दिख रही इमेज का रिज़ॉल्यूशन कम है"}, "core/audits/image-size-responsive.js | title": {"message": "पेज पर दिख रही इमेज का रिज़ॉल्यूशन ठीक है"}, "core/audits/installable-manifest.js | already-installed": {"message": "यह ऐप्लिकेशन पहले से इंस्टॉल किया जा चुका है"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "मेनिफ़ेस्ट में से एक ज़रूरी आइकॉन को डाउनलोड नहीं किया जा सका"}, "core/audits/installable-manifest.js | columnValue": {"message": "इंस्टॉल न हो पाने की वजह"}, "core/audits/installable-manifest.js | description": {"message": "सर्विस वर्कर ऐसी टेक्नोलॉजी है जिसकी मदद से आपका ऐप्लिकेशन, प्रोग्रेसिव वेब ऐप्लिकेशन वाली कई सुविधाएं इस्तेमाल कर पाता है. जैसे, ऑफ़लाइन मोड, होमस्क्रीन पर जोड़ने, और पुश नोटिफ़िकेशन की सुविधाएं. सर्विस वर्कर और मेनिफ़ेस्ट को सही तरीके से लागू करने पर, ब्राउज़र लोगों को अपनी होमस्क्रीन पर आपका ऐप्लिकेशन जोड़ने का सुझाव अपने-आप दे सकते हैं. इस तरह, आपको ज़्यादा लोगों के साथ जुड़ने में मदद मिल सकती है. [मेनिफ़ेस्ट इंस्टॉल करने की ज़रूरी शर्तों के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 गड़बड़ी}one{# गड़बड़ी}other{# गड़बड़ियां}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "वेब ऐप्लिकेशन मेनिफ़ेस्ट या सर्विस वर्कर, इंस्टॉल करने की ज़रूरी शर्तों को नहीं पूरा करते"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play Store ऐप्लिकेशन का यूआरएल और आईडी आपस में मेल नहीं खाते"}, "core/audits/installable-manifest.js | in-incognito": {"message": "पेज को गुप्त विंडो में लोड किया गया है"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "'standalone', 'fullscreen' या 'minimal-ui' में से किसी एक मेनिफ़ेस्ट 'display' प्रॉपर्टी का होना ज़रूरी है"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "मेनिफ़ेस्ट में 'display_override' फ़ील्ड है, जबकि इसमें 'standalone', 'fullscreen' या 'minimal-ui' में से कोई एक डिसप्ले मोड होना ज़रूरी है. यह पहले डिसप्ले मोड के तौर पर काम करता है"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "मेनिफ़ेस्ट को फ़ेच नहीं किया जा सका, वह खाली है या उसे पार्स नहीं किया जा सका"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "मेनिफ़ेस्ट को फ़ेच करते समय उसका यूआरएल बदल गया."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "मेनिफ़ेस्ट में 'name' या 'short_name' फ़ील्ड नहीं है"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "मेनिफ़ेस्ट में सही आइकॉन नहीं है. इसमें PNG, SVG या WebP फ़ॉर्मैट में ऐसा आइकॉन होना ज़रूरी है जो कम से कम {value0} पिक्सल का हो. साथ ही, sizes एट्रिब्यूट को सेट करना ज़रूरी है. अगर purpose एट्रिब्यूट को भी सेट किया जाता है, तो यह ज़रूरी है कि उसमें \"any\" शामिल हो."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "सेट न करें या \"any\" पर सेट करें वाले purpose एट्रिब्यूट के साथ दिया गया कोई भी आइकॉन, ऐसे PNG, SVG या WebP फ़ॉर्मैट में नहीं है जो कम से कम {value0} पिक्सल स्क्वेयर का हो"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "डाउनलोड किया गया आइकॉन खाली या खराब था"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "'Play Store' आईडी नहीं दिया गया"}, "core/audits/installable-manifest.js | no-manifest": {"message": "पेज में कोई भी मेनिफ़ेस्ट <link> यूआरएल नहीं है"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "कोई भी मिलता-जुलता सर्विस वर्कर नहीं मिला. हो सकता है कि आपको पेज फिर से लोड करना पड़े या यह जांच करनी पड़े कि मौजूदा पेज के लिए सर्विस वर्कर का दायरा, मेनिफ़ेस्ट के दायरे और स्टार्ट यूआरएल जितना हो."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "मेनिफ़ेस्ट में 'start_url' फ़ील्ड न होने की वजह से सर्विस वर्कर की जांच नहीं की जा सकती"}, "core/audits/installable-manifest.js | noErrorId": {"message": "इंस्टॉल करने के दौरान हुई गड़बड़ी के '{errorId}' आईडी की पहचान नहीं हो पाई"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "पेज को किसी सुरक्षित ओरिजन पर नहीं खोला गया है"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "पेज को मुख्य फ़्रेम में लोड नहीं किया गया"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "यह पेज, ऑफ़लाइन मोड में काम नहीं करता"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "P<PERSON> को अनइंस्टॉल कर दिया गया है और इंस्टॉल होने की स्थिति को रीसेट किया जा रहा है."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "बताया गया ऐप्लिकेशन प्लैटफ़ॉर्म, Android पर काम नहीं करता"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "मेनिफ़ेस्ट, prefer_related_applications देता है: सही"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications सिर्फ़ Chrome बीटा और Android पर स्थिर चैनलों पर काम करता है."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse यह पता नहीं कर सका कि कोई सर्विस वर्कर था या नहीं. Chrome के नए वर्शन के साथ इस्तेमाल करें."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "मेनिफ़ेस्ट का यूआरएल जिस स्कीम ({scheme}) का इस्तेमाल करता है वह Android पर काम नहीं करती."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "दिया गया मेनिफ़ेस्ट स्टार्ट यूआरएल मान्य नहीं है"}, "core/audits/installable-manifest.js | title": {"message": "वेब ऐप्लिकेशन मेनिफ़ेस्ट और सर्विस वर्कर, इंस्टॉल करने की ज़रूरी शर्तों को पूरा करते हैं"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "मेनिफ़ेस्ट में मौजूद किसी यूआरएल में एक उपयोगकर्ता नाम, पासवर्ड या पोर्ट है"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "यह पेज, ऑफ़लाइन मोड में काम नहीं करता. अगस्त 2021 में Chrome 93 स्थिर चैनल रिलीज़ होने के बाद इस पेज को इंस्टॉल नहीं किया जा सकेगा."}, "core/audits/is-on-https.js | allowed": {"message": "अनुमति दी गई"}, "core/audits/is-on-https.js | blocked": {"message": "ब्लॉक किया गया"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "असुरक्षित यूआरएल"}, "core/audits/is-on-https.js | columnResolution": {"message": "अनुरोध का रिज़ॉल्यूशन"}, "core/audits/is-on-https.js | description": {"message": "सभी साइटों को एचटीटीपीएस की मदद से सुरक्षित किया जाना चाहिए. इनमें वे साइटें भी शामिल हैं जिनमें संवेदनशील जानकारी मौजूद नहीं होती. ऐसा करते समय, हमें [मिले-जुले कॉन्टेंट](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) से बचना होगा, जहां एचटीटीपीएस के शुरुआती अनुरोध के बावजूद कुछ रिसॉर्स, एचटीटीपी पर लोड किए गए हैं. एचटीटीपीएस की वजह से अनचाहे लोग, आपके ऐप्लिकेशन और उसका इस्तेमाल करने वालों के बीच हुई बातों को न तो सुन पाएंगे और न ही उसमें किसी तरह की छेड़खानी कर पाएंगे. एचटीटीपी/2 और कई नए वेब प्लैटफ़ॉर्म एपीआई के लिए, एचटीटीपीएस इस्तेमाल करना ज़रूरी है. [एचटीटीपीएस के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 असुरक्षित अनुरोध मिला}one{# असुरक्षित अनुरोध मिले}other{# असुरक्षित अनुरोध मिले}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "HTTPS का इस्तेमाल नहीं किया जाता"}, "core/audits/is-on-https.js | title": {"message": "HTTPS का इस्तेमाल करता है"}, "core/audits/is-on-https.js | upgraded": {"message": "अपने-आप एचटीटीपीएस भाषा कोड में अपग्रेड हो गया"}, "core/audits/is-on-https.js | warning": {"message": "चेतावनी के साथ अनुमति दी गई"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "यह व्यूपोर्ट में सबसे बड़ा कॉन्टेंटफ़ुल पेंट एलिमेंट है. [सबसे बड़े कॉन्टेंटफ़ुल पेंट एलिमेंट के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "सबसे बड़ा कॉन्टेंटफ़ुल पेंट वाला एलिमेंट"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "सीएलएस में योगदान"}, "core/audits/layout-shift-elements.js | description": {"message": "पेज के सीएलएस में सबसे ज़्यादा योगदान ये DOM एलिमेंट देते हैं. [सीएलएस को बेहतर बनाने का तरीका जानें](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "बड़े लेआउट शिफ़्ट से बचें"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "पेज के ऊपरी हिस्से पर मौजूद जिन इमेज को पेज के लाइफ़साइकल में लेज़ी लोडिंग की मदद से, लोड और रेंडर किया गया है उनसे सबसे बड़े कॉन्टेंटफ़ुल पेंट में ऑडिट करने में देरी हो सकती है. [लेज़ी लोडिंग के बारे में ज़्यादा जानें](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "सबसे बड़े कॉन्टेंटफ़ुल पेंट वाली इमेज को लेज़ी तरीके से लोड किया गया है"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "सबसे बड़े कॉन्टेंटफ़ुल पेंट वाली इमेज को लेज़ी तरीके से नहीं लोड किया गया है"}, "core/audits/long-tasks.js | description": {"message": "यह मुख्य थ्रेड पर सबसे लंबे टास्क की सूची बनाता है. इससे इनपुट डिले में, सबसे खराब योगदान देने वालों की पहचान करने में मदद मिलती है. [मुख्य थ्रेड के लंबे टास्क से बचने का तरीका जानें](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# लंबा टास्क मिला}one{# लंबा टास्क मिला}other{# लंबे टास्क मिले}}"}, "core/audits/long-tasks.js | title": {"message": "मुख्य थ्रेड के लंबे टास्क से बचें"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "श्रेणी"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "JS को पार्स, कंपाइल, और एक्ज़ीक्यूट करने में लगने वाला समय कम करें. छोटे-छोटे JS पेलोड डिलीवर करने से, इसमें आपको मदद मिल सकती है. [मुख्य थ्रेड के काम को कम से कम करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "मुख्य थ्रेड के काम को कम करना"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "मुख्य थ्रेड के काम को कम करता है"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "ज़्यादा से ज़्यादा उपयोगकर्ताओं तक पहुंचने के लिए, साइटों का सभी प्रमुख ब्राउज़र पर काम करना ज़रूरी है. [अलग-अलग ब्राउज़र के साथ काम करने की सुविधा के बारे में जानें](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "यह वेबसाइट अलग-अलग ब्राउज़र पर काम करती है"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "पक्का करें कि हर पेज को यूआरएल के ज़रिए डीप लिंक किया गया हो. साथ ही, वे ऐसे यूनीक यूआरएल हों जिन्हें सोशल मीडिया पर शेयर किया जा सके. [डीप लिंक देने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "हर पेज का एक यूआरएल होता है"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "नेटवर्क धीमा होने पर भी, वेब ऐप्लिकेशन में टैप करने पर ट्रांज़िशन की रफ़्तार तेज़ महसूस होनी चाहिए. इससे लोगों को आपके पेज की परफ़ॉर्मेंस अच्छी नज़र आएगी. [पेज ट्रांज़िशन के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "पेज ट्रांज़िशन को देखकर ऐसा नहीं लगना चाहिए जैसे कि वे नेटवर्क लोड होने का इंतज़ार कर रहे हैं"}, "core/audits/maskable-icon.js | description": {"message": "मास्केबल आइकॉन से यह पक्का किया जाता है कि डिवाइस पर ऐप्लिकेशन इंस्टॉल करते समय, लेटरबॉक्स किए बिना इमेज पूरी जगह पर दिखे. [मास्केबल मेनिफ़ेस्ट आइकॉन के बारे में जानें](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "मेनिफ़ेस्ट में ऐसा कोई भी आइकॉन नहीं मिला जिसके लिए मास्क का इस्तेमाल किया जा सके"}, "core/audits/maskable-icon.js | title": {"message": "मेनिफ़ेस्ट में ऐसा आइकॉन है जिसके लिए मास्क का इस्तेमाल किया जा सकता है"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "कुल लेआउट शिफ़्ट, व्यूपोर्ट में दिखने वाले एलिमेंट की हलचल बताता है. [कुल लेआउट शिफ़्ट से जुड़ी मेट्रिक के बारे में ज़्यादा जानें](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "इंटरैक्शन टू नेक्स्ट पेंट मेट्रिक, पेज पर रिस्पॉन्स मिलने में लगने वाले समय को मापती है. इससे पता चलता है कि उपयोगकर्ता के इनपुट का जवाब देने में पेज को कितना समय लगता है. [इंटरैक्शन टू नेक्स्ट पेंट मेट्रिक के बारे में ज़्यादा जानें](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "फ़र्स्ट कॉन्टेंटफ़ुल पेंट से, यह पता चलता है कि आपकी वेबसाइट का जो टेक्स्ट या इमेज किसी उपयोगकर्ता को सबसे पहले दिखा उसे दिखने में कितना समय लगा. [फ़र्स्ट कॉन्टेंटफ़ुल पेंट से जुड़ी मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "फ़र्स्ट मीनिंगफ़ुल पेंट मेट्रिक इस बात की जानकारी देती है कि किसी पेज का मुख्य कॉन्टेंट कब दिखा. [फ़र्स्ट मीनिंगफ़ुल पेंट से जुड़ी मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "इंटरैक्टिव में लगने वाला समय, वह समय है जितनी देर में पेज पूरी तरह से इंटरैक्टिव हो जाता है. [इंटरैक्टिव में लगने वाले समय से जुड़ी मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "सबसे बड़ा कॉन्टेंटफ़ुल पेंट, सबसे बड़े टेक्स्ट या इमेज को लोड करने में लगा समय बताता है. [सबसे बड़े कॉन्टेंटफ़ुल पेंट से जुड़ी मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "मैक्सिमम पोटेंशियल फ़र्स्ट इनपुट डिले मेट्रिक, आपके पेज पर उपयोगकर्ता के इनपुट मिलने और पेज को उस इनपुट का जवाब देने में लगने वाला सबसे लंबा संभावित समय बताती है. [\"मैक्सिमम पोटेंशियल फ़र्स्ट इनपुट डिले\" मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "स्पीड इंडेक्स से पता चलता है कि किसी पेज का कॉन्टेंट कितनी जल्दी दिखता है. [स्पीड इंडेक्स से जुड़ी मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "एफ़सीपी और इंटरैक्टिव में लगने वाले समय के बीच का समय जब 50 मिलीसेकंड से ज़्यादा होता है, तब यह मेट्रिक उस कुल समय की जानकारी मिलीसेकंड में देती है. [टोटल ब्लॉकिंग टाइम मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "नेटवर्क के 'दोतरफ़ा यात्रा के समय' (आरटीटी) का परफ़ॉर्मेंस पर काफ़ी असर पड़ता है. किसी ऑरिजिन का आरटीटी ज़्यादा होने का मतलब है कि अगर सर्वर, इस्तेमाल करने वालों के नज़दीक हो, तो परफ़ॉर्मेंस बेहतर हो सकती है. [दोतरफ़ा यात्रा के समय (आरटीटी) के बारे में ज़्यादा जानें](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "नेटवर्क का दोतरफ़ा यात्रा का समय"}, "core/audits/network-server-latency.js | description": {"message": "सर्वर के इंतज़ार के समय का असर वेब की परफ़ॉर्मेंस पर हो सकता है. किसी ऑरिजिन के सर्वर के 'इंतज़ार का समय' ज़्यादा होने से यह पता चलता है कि सर्वर पर उसकी क्षमता से ज़्यादा लोड है या उसकी बैकएंड परफ़ॉर्मेंस खराब है. [सर्वर से जवाब मिलने में लगने वाले समय के बारे में ज़्यादा जानें](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "सर्वर बैकएंड के इंतज़ार का समय"}, "core/audits/no-unload-listeners.js | description": {"message": "`unload` इवेंट भरोसेमंद नहीं होता और इसे इस्तेमाल करने की वजह से, ब्राउज़र को ऑप्टिमाइज़ करने की कुछ सुविधाएं रुक सकती हैं. जैसे, बैक-फ़ॉरवर्ड कैश मेमोरी. इसके बजाय, `pagehide` या `visibilitychange` इवेंट का इस्तेमाल करें. [अनलोड इवेंट लिसनर के बारे में ज़्यादा जानें](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "एक `unload` लिसनर रजिस्टर करता है"}, "core/audits/no-unload-listeners.js | title": {"message": "`unload` इवेंट लिसनर का इस्तेमाल नहीं किया जाता"}, "core/audits/non-composited-animations.js | description": {"message": "कंपोज़िट नहीं किए गए ऐनिमेशन की क्वालिटी खराब हो सकती है और सीएलएस बढ़ सकता है. [कंपोज़ नहीं किए गए ऐनिमेशन से बचने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# ऐनिमेशन वाला एलिमेंट मिला}one{# ऐनिमेशन वाला एलिमेंट मिला}other{# ऐनिमेशन वाले एलिमेंट मिले}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "फ़िल्टर से जुड़ी प्रॉपर्टी पिक्सल को अपनी जगह से हटा सकती है"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "टारगेट में दूसरा एनिमेशन है जो इसके साथ काम नहीं करता है"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "इफ़ेक्ट में \"replace\" के अलावा कंपोज़िट मोड है"}, "core/audits/non-composited-animations.js | title": {"message": "कंपोज़िट नहीं किए गए ऐनिमेशन से बचें"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "रूपांतरण से जुड़ी प्रॉपर्टी बॉक्स के साइज़ पर निर्भर करती है"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{इसके साथ काम नहीं करने वाली सीएसएस प्रॉपर्टी: {properties}}one{इसके साथ काम नहीं करने वाली सीएसएस प्रॉपर्टी: {properties}}other{इसके साथ काम नहीं करने वाली सीएसएस प्रॉपर्टी: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "इस इफ़ेक्ट में समय के ऐसे पैरामीटर हैं जो काम नहीं करते"}, "core/audits/performance-budget.js | description": {"message": "नेटवर्क अनुरोधों की संख्या और साइज़, आपको दिए गए परफ़ॉर्मेंस बजट के मुताबिक सेट किए गए टारगेट के हिसाब से होना चाहिए. [परफ़ॉर्मेंस बजट के बारे में ज़्यादा जानें](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{अनुरोध}one{# अनुरोध}other{# अनुरोध}}"}, "core/audits/performance-budget.js | title": {"message": "परफ़ॉर्मेंस बजट"}, "core/audits/preload-fonts.js | description": {"message": "`optional` फ़ॉन्ट पहले से लोड करें, ताकि वेबसाइट पर पहली बार आने वाले उनका इस्तेमाल कर सकें. [पहले से फ़ॉन्ट लोड करने के बारे में ज़्यादा जानें](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "`font-display: optional` वाले फ़ॉन्ट पहले से लोड नहीं किए गए हैं"}, "core/audits/preload-fonts.js | title": {"message": "`font-display: optional` वाले फ़ॉन्ट पहले से लोड किए गए हैं"}, "core/audits/prioritize-lcp-image.js | description": {"message": "अगर पेज में एलसीपी एलिमेंट को डाइनैमिक तरीके से जोड़ा गया है, तो एलसीपी को बेहतर बनाने के लिए इमेज को पहले से लोड कर लेना चाहिए. [एलसीपी एलिमेंट को पहले से लोड करने के बारे में ज़्यादा जानें](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "सबसे बड़े कॉन्टेंटफ़ुल पेंट वाली इमेज को पहले से लोड करें"}, "core/audits/redirects.js | description": {"message": "रीडायरेक्ट की वजह से, पेज के लोड होने में लगने वाला समय और बढ़ जाता है. [पेज रीडायरेक्ट करने से बचने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "एक से ज़्यादा पेज रीडायरेक्ट करने से बचें"}, "core/audits/resource-summary.js | description": {"message": "पेज रिसॉर्स की संख्या और साइज़ का बजट सेट करने के लिए, budget.json फ़ाइल जोड़ें. [परफ़ॉर्मेंस बजट के बारे में ज़्यादा जानें](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 अनुरोध • {byteCount, number, bytes} किबीबाइट}one{# अनुरोध • {byteCount, number, bytes} किबीबाइट}other{# अनुरोध • {byteCount, number, bytes} किबीबाइट}}"}, "core/audits/resource-summary.js | title": {"message": "अनुरोधों की संख्या कम और ट्रांसफ़र का आकार छोटा रखें"}, "core/audits/seo/canonical.js | description": {"message": "कैननिकल लिंक, इस बारे में जानकारी देते हैं कि खोज के नतीजों में कौनसे यूआरएल दिखाए जाएं. [कैननिकल लिंक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "कई कॉन्फ़्लिक्टिंग यूआरएल हैं ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "गलत यूआरएल ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "किसी दूसरी `hreflang` जगह वाली विशेषता ({url}) की तरफ़ इशारा करता है"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "यह एक संपूर्ण यूआरएल नहीं है ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "मिलती-जुलती सामग्री वाले पेज पर ले जाने के बजाय डोमेन के रूट यूआरएल (होम पेज) पर ले जाता है"}, "core/audits/seo/canonical.js | failureTitle": {"message": "दस्तावेज़ में सही `rel=canonical` नहीं है"}, "core/audits/seo/canonical.js | title": {"message": "दस्तावेज़ में सही `rel=canonical` शामिल है"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "ऐसा लिंक जिसे क्रॉल नहीं किया जा सकता"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "वेबसाइटों को क्रॉल करने के लिए, सर्च इंजन लिंक पर `href` एट्रिब्यूट का इस्तेमाल कर सकते हैं. पक्का करें कि ऐंकर एलिमेंट का `href` एट्रिब्यूट किसी सही डेस्टिनेशन से लिंक हो, ताकि साइट के ज़्यादा पेज खोजे जा सकें. [लिंक को क्रॉल करने लायक बनाने का तरीका जानें](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "ये लिंक क्रॉल नहीं किए जा सकते"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "ये लिंक क्रॉल किए जा सकते हैं"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "पढ़ा न जा सकने वाला अन्य टेक्स्ट"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "फ़ॉन्ट का साइज़"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "पेज के टेक्स्ट का %"}, "core/audits/seo/font-size.js | columnSelector": {"message": "सिलेक्टर"}, "core/audits/seo/font-size.js | description": {"message": "12px से कम साइज़ वाले फ़ॉन्ट काफ़ी छोटे होते हैं, जिन्हें साफ़-साफ़ पढ़ा नहीं जा सकता. ऐसे में, मोबाइल पर वेबसाइट देखने वालों को “ज़ूम करके पढ़ने के लिए पिंच करना” ज़रूरी होता है. कोशिश करें कि पेज का 60% से ज़्यादा टेक्स्ट 12px या उससे बड़े साइज़ का हो. [साफ़-साफ़ पढ़े जाने वाले फ़ॉन्ट साइज़ के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} टेक्स्ट पढ़ने लायक है"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "मोबाइल स्क्रीन के लिए कोई व्यूपोर्ट मेटा टैग ऑप्टिमाइज़ नहीं किए जाने की वजह से टेक्स्ट पढ़ने लायक नहीं है."}, "core/audits/seo/font-size.js | failureTitle": {"message": "दस्तावेज़ पढ़ने लायक फ़ॉन्ट आकारों का इस्तेमाल नहीं करता है"}, "core/audits/seo/font-size.js | legibleText": {"message": "पढ़ा जा सकने वाला टेक्स्ट"}, "core/audits/seo/font-size.js | title": {"message": "दस्तावेज़ पढ़ने लायक फ़ॉन्ट आकारों का इस्तेमाल करता है"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang लिंक से सर्च इंजन को यह पता चलता है कि किसी खास भाषा या क्षेत्र के लिए, पेज के किस वर्शन को खोज के नतीजों में रखना चाहिए. [`hreflang` के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "दस्तावेज़ में सही `hreflang` नहीं है"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "मिलते-जुलते href मान को गलत तरीके से इस्तेमाल किया गया"}, "core/audits/seo/hreflang.js | title": {"message": "दस्तावेज़ में सही `hreflang` शामिल है"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "ऐसे भाषा कोड का इस्तेमाल किया गया जिसकी उम्मीद नहीं थी"}, "core/audits/seo/http-status-code.js | description": {"message": "ऐसे पेजों को ठीक से इंडेक्स नहीं किया जा सकता जिनमें एचटीटीपी स्टेटस कोड काम न करते हों. [एचटीटीपी स्टेटस कोड के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "पेज में काम नहीं करने वाला एचटीटीपी स्थिति कोड है"}, "core/audits/seo/http-status-code.js | title": {"message": "पेज में काम करने वाला एचटीटीपी स्थिति कोड है"}, "core/audits/seo/is-crawlable.js | description": {"message": "अगर सर्च इंजन के पास आपके पेज क्रॉल करने की अनुमति न हो, तो वे खोज के नतीजों में आपके पेजों को शामिल नहीं कर पाएंगे. [क्रॉलर डायरेक्टिव के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "पेज को इंडेक्स करने से ब्लॉक किया गया है"}, "core/audits/seo/is-crawlable.js | title": {"message": "पेज को इंडेक्स करने से ब्लॉक नहीं किया गया है"}, "core/audits/seo/link-text.js | description": {"message": "लिंक की पूरी जानकारी देने वाले टेक्स्ट की मदद से, सर्च इंजन आपके कॉन्टेंट को समझ पाते हैं. [लिंक को आसानी से ऐक्सेस करने लायक बनाने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 लिंक मिला}one{# लिंक मिले}other{# लिंक मिले}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "लिंक में पूरी जानकारी देने वाला टेक्स्ट नहीं है"}, "core/audits/seo/link-text.js | title": {"message": "लिंक में पूरी जानकारी देने वाला टेक्स्ट है"}, "core/audits/seo/manual/structured-data.js | description": {"message": "स्ट्रक्चर्ड डेटा की पुष्टि करने के लिए, [स्ट्रक्चर्ड डेटा टेस्टिंग टूल](https://search.google.com/structured-data/testing-tool/) और [स्ट्रक्चर्ड डेटा लिंटर](http://linter.structured-data.org/) का इस्तेमाल करें. [स्ट्रक्चर्ड डेटा के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "स्ट्रक्चर्ड डेटा सही है"}, "core/audits/seo/meta-description.js | description": {"message": "खोज के नतीजों में मुख्य जानकारी शामिल हो सकती है, ताकि पेज के कॉन्टेंट के बारे में थोड़े शब्दों में खास जानकारी दी जा सके. [मुख्य जानकारी के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "ब्यौरे का टेक्स्ट खाली है."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "दस्तावेज़ में संक्षिप्त विवरण नहीं है"}, "core/audits/seo/meta-description.js | title": {"message": "दस्तावेज़ में संक्षिप्त विवरण है"}, "core/audits/seo/plugins.js | description": {"message": "सर्च इंजन, प्लगिन के कॉन्टेंट को इंडेक्स नहीं कर सकते. साथ ही, कई डिवाइस, प्लगिन पर पाबंदी लगाते हैं या उन पर काम नहीं करते. [प्लगिन से बचने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "दस्तावेज़ प्लग इन का इस्तेमाल करता है"}, "core/audits/seo/plugins.js | title": {"message": "दस्तावेज़ प्लग इन से बचता है"}, "core/audits/seo/robots-txt.js | description": {"message": "अगर आपकी robots.txt फ़ाइल सही नहीं है, तो क्रॉलर यह नहीं समझ पाएंगे कि आपको अपनी वेबसाइट को किस तरह क्रॉल या इंडेक्स कराना है. [robots.txt के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt के अनुरोध ने यह एचटीटीपी स्थिति लौटाई है: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 गड़बड़ी मिली}one{# गड़बड़ियां मिलीं}other{# गड़बड़ियां मिलीं}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse, robots.txt फ़ाइल डाउनलोड नहीं कर सका"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt सही नहीं है"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt सही है"}, "core/audits/seo/tap-targets.js | description": {"message": "बटन और लिंक जैसे इंटरैक्टिव एलिमेंट, आकार में बड़े (48x48px) होने चाहिए और इनके आस-पास काफ़ी जगह होनी चाहिए. इससे दूसरे एलिमेंट को ओवरलैप किए बिना आसानी से उन पर टैप किया जा सकेगा. [टैप टारगेट के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} टैप की जाने वाली जगहें सही आकार में हैं"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "मोबाइल स्क्रीन के लिए कोई व्यूपोर्ट मेटा टैग ऑप्टिमाइज़ नहीं किए जाने की वजह से टैप की जाने वाली जगहें काफ़ी छोटी हैं"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "टैप की जाने वाली जगहें सही आकार में नहीं हैं"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "ओवरलैप करने वाली जगह"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "टैप की जाने वाली जगह"}, "core/audits/seo/tap-targets.js | title": {"message": "टैप की जाने वाली जगहें सही आकार में हैं"}, "core/audits/server-response-time.js | description": {"message": "मुख्य दस्तावेज़ के लिए, अनुरोध का जवाब देने में सर्वर को लगने वाला समय कम रखें, क्योंकि बाकी सभी अनुरोध इस पर ही निर्भर हैं. [टाइम टू फ़र्स्ट बाइट मेट्रिक के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "रुट दस्तावेज़ बनने में {timeInMs, number, milliseconds} मि.से. का समय लगा"}, "core/audits/server-response-time.js | failureTitle": {"message": "जवाब देने में सर्वर को लगने वाला शुरुआती समय कम करें"}, "core/audits/server-response-time.js | title": {"message": "जवाब देने में सर्वर को लगने वाला शुरुआती समय कम था"}, "core/audits/service-worker.js | description": {"message": "सर्विस वर्कर एक ऐसी टेक्नोलॉजी है जो आपके ऐप्लिकेशन को, प्रोग्रेसिव वेब ऐप्लिकेशन के कई फ़ीचर इस्तेमाल करने की अनुमति देती है. इनमें ऑफ़लाइन, होमस्क्रीन पर जोड़ें, और पुश नोटिफ़िकेशन जैसे फ़ीचर शामिल हैं. [सर्विस वर्कर के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "इस पेज का नियंत्रण सर्विस वर्कर के पास है. हालांकि, कोई `start_url` नहीं मिला, क्योंकि मेनिफ़ेस्ट को मान्य JSON के रूप में पार्स नहीं किया जा सका"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "इस पेज का नियंत्रण सर्विस वर्कर के पास है. हालांकि, `start_url` ({startUrl}) सर्विस वर्कर के दायरे में नहीं है ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "इस पेज का नियंत्रण सर्विस वर्कर के पास है. हालांकि, कोई `start_url` नहीं मिला, क्योंकि किसी पेज पर कोई मेनिफ़ेस्ट ही नहीं था."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "यहां पर एक या ज़्यादा सर्विस वर्कर हैं. हालांकि, पेज ({pageUrl}) दायरे में नहीं है."}, "core/audits/service-worker.js | failureTitle": {"message": "किसी ऐसे सर्विस वर्कर को रजिस्टर नहीं करता जो पेज और `start_url` को नियंत्रित करता है"}, "core/audits/service-worker.js | title": {"message": "किसी ऐसे सर्विस वर्कर को रजिस्टर करता है जो पेज और `start_url` को नियंत्रित करता है"}, "core/audits/splash-screen.js | description": {"message": "जब लोग अपने डिवाइस की होमस्क्रीन से आपका ऐप्लिकेशन लॉन्च करते हैं, तो थीम वाली स्प्लैश स्क्रीन की वजह से उन्हें अच्छा अनुभव मिलता है. [स्प्लैश स्क्रीन के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "पसंद के मुताबिक स्प्लैश स्क्रीन के लिए कॉन्फ़िगर नहीं किया गया"}, "core/audits/splash-screen.js | title": {"message": "पसंद के मुताबिक स्प्लैश स्क्रीन के लिए कॉन्फ़िगर किया गया"}, "core/audits/themed-omnibox.js | description": {"message": "ब्राउज़र के पता बार की थीम को ऐसा बनाया जा सकता है जो आपकी साइट से मेल खाए. [पता बार की थीम सेट करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "'पता बार' के लिए थीम का रंग सेट नहीं करता है."}, "core/audits/themed-omnibox.js | title": {"message": "'पता बार' के लिए थीम का रंग सेट करता है."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (ग्राहक सहायता)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (मार्केटिंग)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (सोशल)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (वीडियो)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "प्रॉडक्ट"}, "core/audits/third-party-facades.js | description": {"message": "तीसरे पक्ष के एम्बेड किए गए कुछ ऑब्जेक्ट को लोड होने में समय लग सकता है. जब तक ये ऑब्जेक्ट ज़रूरी न हों, तब तक इनकी जगह facade इस्तेमाल करें. [facade की मदद से, तीसरे पक्ष को कुछ समय के लिए रोकने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{विकल्प के तौर पर # फ़साड उपलब्ध है}one{विकल्प के तौर पर # फ़साड उपलब्ध है}other{विकल्प के तौर पर # फ़साड उपलब्ध हैं}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "तीसरे पक्ष के कुछ संसाधनों को फ़साड की मदद से धीमी रफ़्तार से लोड किया जा सकता है"}, "core/audits/third-party-facades.js | title": {"message": "तीसरे पक्ष के संसाधनों को फ़साड की मदद से धीमी रफ़्तार से लोड करें"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "तीसरा पक्ष"}, "core/audits/third-party-summary.js | description": {"message": "तीसरे पक्ष के कोड, आपके पेज के लोड होने की परफ़ॉर्मेंस पर गहरा असर डाल सकते हैं. तीसरे-पक्ष की सेवा देने वाली ऐसी कंपनियों का ज़्यादा इस्तेमाल न करें जिनके कोड अब आपके लिए काम के नहीं हैं. साथ ही, तीसरे पक्ष के कोड को तब लोड करें, जब आपके पेज पर लोड होने का काम मुख्य रूप से पूरा हो जाए. [तीसरे पक्ष के असर को कम करने का तरीका जानें](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "तीसरे पक्ष के कोड ने {timeInMs, number, milliseconds} एमएस के लिए मुख्य थ्रेड को ब्लॉक कर दिया है"}, "core/audits/third-party-summary.js | failureTitle": {"message": "तीसरे पक्ष के कोड का असर कम करें"}, "core/audits/third-party-summary.js | title": {"message": "तीसरे पक्ष के इस्तेमाल को कम करें"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "मा<PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "मेट्रिक"}, "core/audits/timing-budget.js | description": {"message": "अपनी साइट की परफ़ॉर्मेस पर नज़र रखने के लिए टाइमिंग बजट सेट करें. अच्छा परफ़ॉर्म करने वाली साइटें जल्दी लोड होती हैं और लोगों के इनपुट इवेंट का जल्दी जवाब देती हैं. [परफ़ॉर्मेंस बजट के बारे में ज़्यादा जानें](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "टाइमिंग बजट"}, "core/audits/unsized-images.js | description": {"message": "इमेज एलिमेंट पर, चौड़ाई और ऊंचाई की जानकारी साफ़ तौर पर सेट करें, ताकि लेआउट शिफ़्ट को कम किया जा सके और सीएलएस को बेहतर बनाया जा सके. [इमेज का डाइमेंशन सेट करने का तरीका जानें](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "इमेज एलिमेंट पर, `width` और `height` के बारे में साफ़-साफ़ नहीं बताया गया है"}, "core/audits/unsized-images.js | title": {"message": "इमेज एलिमेंट पर, `width` और `height` की जानकारी साफ़ तौर पर दी गई है"}, "core/audits/user-timings.js | columnType": {"message": "प्र<PERSON><PERSON>र"}, "core/audits/user-timings.js | description": {"message": "User Timing API के साथ अपने ऐप्लिकेशन का इस्तेमाल करें. इससे उपयोगकर्ता के मुख्य अनुभवों के दौरान, यह मापा जा सकता है कि आपका ऐप्लिकेशन असल में कैसा परफ़ॉर्म कर रहा है. [User Timing के टाइमस्टैंप के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 उपयोगकर्ता समय}one{# उपयोगकर्ता समय}other{# उपयोगकर्ता समय}}"}, "core/audits/user-timings.js | title": {"message": "उपयोगकर्ता समय अंक और मापन"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "\"{<PERSON><PERSON><PERSON><PERSON>}\" के लिए एक `<link rel=preconnect>` मिला था, लेकिन ब्राउज़र ने इसका इस्तेमाल नहीं किया. जांच करें कि आप `crossorigin` एट्रिब्यूट का ठीक से इस्तेमाल कर रहे हैं."}, "core/audits/uses-rel-preconnect.js | description": {"message": "तीसरे पक्ष के ज़रूरी ऑरिजिन के साथ पहले से कनेक्शन बनाने के लिए, `preconnect` या `dns-prefetch` रिसॉर्स हिंट जोड़ें. [ज़रूरी ऑरिजिन के साथ पहले से कनेक्शन बनाने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "ज़रूरी मूल से प्री-कनेक्ट करें"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "दो से ज़्यादा `<link rel=preconnect>` लिंक पाए गए. इनका इस्तेमाल सिर्फ़ अहम कनेक्शन के लिए सावधानी के साथ किया जाना चाहिए."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "\"{security<PERSON><PERSON><PERSON>}\" के लिए एक `<link rel=preconnect>` मिला था, लेकिन ब्राउज़र ने इसका इस्तेमाल नहीं किया. `preconnect` का इस्तेमाल सिर्फ़ उन ज़रूरी वेबसाइटों के लिए करें जिनके लिए कोई पेज खास तौर पर अनुरोध करेगा."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "\"{preloadURL}\" के लिए पहले से लोड किया गया एक `<link>` मिला था, लेकिन ब्राउज़र ने इसका इस्तेमाल नहीं किया. जांच करें कि आप `crossorigin` एट्रिब्यूट का ठीक से इस्तेमाल कर रहे हैं."}, "core/audits/uses-rel-preload.js | description": {"message": "`<link rel=preload>` का इस्तेमाल करें. इससे उन संसाधनों को पहले फ़ेच किया जा सकेगा जिनके लिए फ़िलहाल पेज के लोड होने के बाद अनुरोध किया जाता है. [मुख्य अनुरोधों को पहले से लोड करने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "प्रमुख अनुरोधों को पहले से लोड करें"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "मैप का यूआरएल"}, "core/audits/valid-source-maps.js | description": {"message": "सोर्स मैप, छोटे किए गए कोड का मूल सोर्स कोड में अनुवाद करते हैं. इससे डेवलपर को डेटा तैयार करने के दौरान, डीबग करने में मदद मिलती है. इसके अलावा, Lighthouse आगे की अहम जानकारी दे सकता है. ये फ़ायदे पाने के लिए, सोर्स मैप को डिप्लॉय करें. [सोर्स मैप के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "पहले पक्ष की बड़ी JavaScript के लिए सोर्स मैप नहीं मिले"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "बड़ी JavaScript फ़ाइल में एक सोर्स मैप नहीं है"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{चेतावनी: `.sourcesContent` में 1 आइटम नहीं है}one{चेतावनी: `.sourcesContent` में # आइटम नहीं है}other{चेतावनी: `.sourcesContent` में # आइटम नहीं हैं}}"}, "core/audits/valid-source-maps.js | title": {"message": "पेज में मान्य सोर्स मैप हैं"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">`, आपके ऐप्लिकेशन को मोबाइल की स्क्रीन के साइज़ के हिसाब से ऑप्टिमाइज़ करता है. साथ ही, यह [उपयोगकर्ता के इनपुट में, 300 मिलीसेकंड की देरी नहीं होने देता](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [व्यूपोर्ट मेटा टैग इस्तेमाल करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "कोई `<meta name=\"viewport\">` टैग नहीं मिला"}, "core/audits/viewport.js | failureTitle": {"message": "वेब पेज पर कोई `width` या `initial-scale` वाला `<meta name=\"viewport\">` टैग नहीं है"}, "core/audits/viewport.js | title": {"message": "वेब पेज पर `width` या `initial-scale` वाला `<meta name=\"viewport\">` टैग है"}, "core/audits/work-during-interaction.js | description": {"message": "यह थ्रेड ब्लॉक करने का काम है, जो इंटरैक्शन टू नेक्स्ट पेंट मेट्रिक के लिए मेज़रमेंट के दौरान होता है. [इंटरैक्शन टू नेक्स्ट पेंट मेट्रिक के बारे में ज़्यादा जानें](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "'{interactionType}' इवेंट में {timeInMs, number, milliseconds} मिलीसेकंड का समय लगा"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "इवेंट का टारगेट"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "मुख्य इंटरैक्शन के दौरान कम काम करें"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "इनपुट डिले"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "प्रज़ेंटेशन में देरी"}, "core/audits/work-during-interaction.js | processingTime": {"message": "प्रोसेस करने में लगा समय"}, "core/audits/work-during-interaction.js | title": {"message": "मुख्य इंटरैक्शन के दौरान काम को कम करता है"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "ये आपके ऐप्लिकेशन में ARIA के इस्तेमाल को बेहतर बनाने के अवसर हैं, जिससे उपयोगकर्ताओं का स्क्रीन रीडर जैसी सहायक तकनीक का अनुभव बेहतर हो सकता है."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "ये अवसर ऑडियो और वीडियो के लिए वैकल्पिक सामग्री मुहैया कराते हैं. इससे ऐसे इस्तेमाल करने वालों को बेहतर सुविधा मिल सकती है जो ठीक से सुन या देख नहीं पाते हैं."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "ऑडियो और वीडियो"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "ये आइटम सुलभता के सबसे अच्छे सामान्य तरीके हाइलाइट करते हैं."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "सबसे अच्छे तरीके"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "ये सभी जांच आपको [आपके वेब ऐप्लिकेशन की सुलभता बेहतर करने](https://developer.chrome.com/docs/lighthouse/accessibility/) के अवसर देती हैं. सुलभता गड़बड़ियों के सिर्फ़ एक उपसेट के बारे में अपने आप पता लगाया जा सकता है, इसलिए हम मैन्युअल टेस्टिंग का सुझाव देते हैं."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "ये आइटम ऐसे मामलों में भी काम करते हैं जहां अपने आप काम करने वाला टेस्टिंग टूल नाकाम रहता है. हमारी गाइड में जाकर [सुलभता समीक्षा करने](https://web.dev/how-to-review/) के बारे में ज़्यादा जानें."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "सुलभता"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "आपकी सामग्री को पढ़ने में आसान बनाने के अवसर मौजूद हैं."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "कंट्रास्ट"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "इन मौकों से कई भाषाओं में उपयोगकर्ताओं के ज़रिए आपके कॉन्टेंट के पेश करने के तरीके को बेहतर बनाया जा सकता है."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "अंतरराष्ट्रीय और स्थानीय भाषा के अनुसार"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "इन अवसरों से आपके ऐप्लिकेशन में नियंत्रणों के सीमेंटिक (शब्दार्थ विज्ञान) को बेहतर बनाया जा सकता है. इससे उपयोगकर्ता का स्क्रीन रीडर जैसी सहायक तकनीक का अनुभव बेहतर हो सकता है."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "नाम और लेबल"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "ये अवसर आपके ऐप्लिकेशन में कीबोर्ड नेविगेशन को बेहतर बनाते हैं."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "नेविगेशन"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "स्क्रीन रीडर जैसी इन सहायक टेक्नोलॉजी का इस्तेमाल करके, आप टेबल या सूची का डेटा पढ़ने की सुविधा को बेहतर बना सकते हैं."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "टेबल और सूचियां"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "ब्राउज़र किस-किस के साथ काम करता है"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "सबसे अच्छे तरीके"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "सामान्य"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "भरोसा और सुरक्षा"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "उपयोगकर्ता का अनुभव"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "परफ़ॉर्मेंस बजट बनाकर आप अपनी साइट की परफ़ॉर्मेंस के मानक तय करते हैं."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "बजट"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "आपके ऐप्लिकेशन के परफ़ॉर्मेंस के बारे में ज़्यादा जानकारी. इन आंकड़ों का परफ़ॉर्मेंस स्कोर पर [सीधा असर](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) नहीं पड़ता."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "निदान"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "परफ़ॉर्मेंस का सबसे अहम पहलू यह है कि स्क्रीन पर पिक्सेल कितनी तेज़ी से रेंडर होते हैं. प्रमुख मेट्रिक: उपयोगी सामग्री वाला पहला पेंट, पहला उपयोगी पेंट"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "पहले पेंट के सुधार"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "इन सुझावों से आप अपने पेज को तेज़ी से लोड करा सकते हैं. इनसे आपके परफ़ॉर्मेंस स्कोर पर [सीधा असर](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) नहीं होगा."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "अवसर"}, "core/config/default-config.js | metricGroupTitle": {"message": "मेट्रिक"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "पूरे लोडिंग अनुभव को बेहतर बनाएं ताकि पेज जवाब दे और जल्दी से जल्दी इस्तेमाल के लिए तैयार हो जाए. प्रमुख मेट्रिक: इंटरेक्टिव में लगने वाला समय, गति इंडेक्स"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "समस्त सुधार"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "परफ़ॉर्मेंस"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "इन जांचों से, किसी प्रोग्रेसिव वेब ऐप्लिकेशन के पहलुओं की पुष्टि की जाती है. [जानें कि एक अच्छे प्रोग्रेसिव वेब ऐप्लिकेशन के लिए क्या-क्या ज़रूरी है.](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "इस तरह की जांच, बेसलाइन [PWA चेकलिस्ट](https://web.dev/pwa-checklist/) के लिए ज़रूरी हैं, लेकिन Lighthouse इनकी जांच अपने आप नहीं करता है. वे आपके स्कोर पर असर नहीं डालते हैं, लेकिन इनकी मैन्युअल तरीके से पुष्टि करना ज़रूरी है."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "इंस्टॉल किया जा सकता है"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA ऑप्टिमाइज़ किया गया"}, "core/config/default-config.js | seoCategoryDescription": {"message": "इन जांचों से यह पक्का होता है कि आपका पेज, सर्च इंजन ऑप्टिमाइज़ेशन से जुड़ी बुनियादी सलाह का पालन कर रहा है. ऐसी कई चीज़ें हैं जिनकी वजह से लाइटहाउस आपके पेज की जांच नहीं करता. इससे, खोज नतीजों में आपके पेज की रैंकिंग और [वेबसाइट की परफ़ॉर्मेंस से जुड़ी जानकारी](https://web.dev/learn-core-web-vitals/) की रिपोर्ट पर असर पड़ सकता है. [Google Search Essentials के बारे में ज़्यादा जानें](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "SEO के दूसरे सबसे अच्छे तरीके देखने के लिए अपनी साइट पर पुष्टि करने वाले ये और भी टूल चलाएं."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "अपने एचटीएमएल को इस तरह फ़ॉर्मैट करें जिससे क्रॉलर आपके ऐप्लिकेशन की सामग्री को बेहतर ढंग से समझ सकें."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "सामग्री से जुड़े सबसे अच्छे तरीके"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "खोज नतीजों में दिखाई देने के लिए, क्रॉलर को आपके ऐप्लिकेशन का ऐक्सेस चाहिए."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "क्रॉल करना और इंडेक्स करना"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "यह पक्का करें कि आपके पेज मोबाइल-फ़्रेंडली हों. इससे लोगों को कॉन्टेंट वाले पेजों को पढ़ने के लिए पिंच या ज़ूम नहीं करना पड़ेगा. [पेजों को मोबाइल-फ़्रेंडली बनाने का तरीका जानें](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "मोबाइल फ़्रेंडली"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "ऐसा लगता है कि जांचे गए डिवाइस का सीपीयू, अनुमानित Lighthouse बेसलाइन से धीमा है. इससे आपके परफ़ॉर्मेंस स्कोर पर बुरा असर पड़ सकता है. [सही सीपीयू स्लोडाउन मल्टिप्लायर को कैलिब्रेट करने](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling) के बारे में ज़्यादा जानें."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "शायद पेज उम्मीद के मुताबिक लोड नहीं हो रहा है, क्योंकि आपके टेस्ट यूआरएल ({requested}) को {final} की ओर रीडायरेक्ट किया गया था. टेस्टिंग के लिए दूसरा यूआरएल आज़माएं."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "धीरे लोड होने की वजह से, पेज तय समयसीमा में पूरी तरह लोड नहीं हो पाया. नतीजे अधूरे हो सकते हैं."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "ब्राउज़र की कैश मेमोरी मिटाने का समय खत्म हो गया. इस पेज का फिर से ऑडिट करके देखें. अगर समस्या बनी रहती है, तो गड़बड़ी की शिकायत करें."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{हो सकता है कि सेव किया गया डेटा इस जगह पर, लोड होने की परफ़ॉर्मेंस पर असर डाल रहा हो: {locations}. इस पेज को गुप्त विंडो में ऑडिट करें, ताकि उन संसाधनों का आपके स्कोर पर कोई असर न पड़े.}one{हो सकता है कि सेव किया गया डेटा इस जगह पर, लोड होने की परफ़ॉर्मेंस पर असर डाल रहा हो: {locations}. इस पेज को गुप्त विंडो में ऑडिट करें, ताकि उन संसाधनों का आपके स्कोर पर कोई असर न पड़े.}other{हो सकता है कि सेव किया गया डेटा इन जगहों पर, लोड होने की परफ़ॉर्मेंस पर असर डाल रहा हो: {locations}. इस पेज को गुप्त विंडो में ऑडिट करें, ताकि उन संसाधनों का आपके स्कोर पर कोई असर न पड़े.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "ऑरिजिन डेटा को हटाने का समय खत्म हो गया. इस पेज का फिर से ऑडिट करके देखें. अगर समस्या बनी रहती है, तो गड़बड़ी की शिकायत करें."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "जीईटी अनुरोध की मदद से लोड किए गए पेज ही बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल कर सकते हैं."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "सिर्फ़ 2XX स्टेटस कोड वाले पेजों को कैश मेमोरी में सेव किया जा सकता है."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome ने कैश मेमोरी में सेव पेज के लिए, JavaScript चलाने की कोशिश का पता लगाया."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "जिन पेजों के लिए AppBanner का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "फ़्लैग की मदद से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद की गई. इस डिवाइस पर यह सुविधा चालू करने के लिए, chrome://flags/#back-forward-cache पर जाएं."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को कमांड लाइन की मदद से बंद किया गया."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "कम मेमोरी की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद कर दिया गया है."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "डेलिगेट के लिए, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा उपलब्ध नहीं है."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "प्रीरेंडरिंग करने वाली सुविधा के लिए, बैक-फ़ॉरवर्ड कैश मेमोरी को बंद किया गया."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "पेज को कैश मेमोरी में सेव नहीं किया जा सकता, क्योंकि इसमें रजिस्टर किए गए लिसनर के साथ BroadcastChannel इंस्टेंस मौजूद है."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा, उन पेजों के लिए चालू नहीं की जा सकती जिनके हेडर में cache-control:no-store मौजूद हो."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "कैश मेमोरी जान-बूझकर मिटाई गई."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "इस पेज को कैश मेमोरी से हटा दिया गया था, ताकि दूसरे पेज को कैश मेमोरी में सेव करने की अनुमति दी जा सके."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "जिन पेजों पर प्लग इन मौजूद हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "FileChooser API का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "File System Access API का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcher का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "पेज छोड़ने के बाद भी, मीडिया प्लेयर पर मीडिया चल रहा था."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "MediaSession API और सेट प्लेबैक स्टेट का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "MediaSession API और सेट ऐक्शन हैंडलर का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "स्क्रीन रीडर वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON><PERSON> का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Serial API का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "WebAuthetication API का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "WebBluetooth API का इस्तेमाल करने वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "WebUSB API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "किसी काम को करने के लिए खास तौर पर तय किए गए वर्कर या वर्कलेट का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "इस दस्तावेज़ से जाने से पहले, यह पूरी तरह से लोड नहीं हुआ था."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "पेज छोड़कर जाने पर, ऐप्लिकेशन बैनर दिखाया गया था."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "पेज छोड़कर जाने पर, Chrome Password Manager का पेज दिखाया गया था."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "पेज छोड़कर जाने के दौरान, DOM डिस्टिलेशन की प्रोसेस जारी थी."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "पेज छोड़कर जाने पर, DOM Distiller Viewer दिखाया गया था."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "मैसेजिंग एपीआई का इस्तेमाल करने वाले एक्सटेंशन की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "अगर कोई एक्सटेंशन लंबे समय से किसी एक कनेक्शन से जुड़ा है, तो उसे बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल करने से पहले, कनेक्शन को बंद करना होगा."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "उन एक्सटेंशन ने बैक-फ़ॉरवर्ड कैश मेमोरी में मौजूद फ़्रेम को मैसेज भेजने की कोशिश की जो लंबे समय से किसी एक कनेक्शन से जुड़े हैं."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "एक्सटेंशन की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "पेज छोड़कर जाने पर, मॉडल डायलॉग दिखाए गए थे, जैसे कि फ़ॉर्म को फिर से सबमिट करने या एचटीटीपी पासवर्ड वाले डायलॉग."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "पेज छोड़कर जाने पर, ऑफ़लाइन पेज दिखाया गया था."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "पेज छोड़कर जाने पर, Out-Of-Memory Intervention बार दिखाया गया था."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "पेज छोड़कर जाने पर, अनुमति के अनुरोध दिखाए गए थे."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "पेज छोड़कर जाने पर, पॉप-अप ब्लॉकर दिखाया गया था."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "पेज छोड़कर जाने पर, सुरक्षित ब्राउज़िंग की जानकारी दिखाई गई थी."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "सुरक्षित ब्राउज़िंग की मदद से पता चला है कि इस पेज पर नुकसान पहुंचाने वाली जानकारी मौजूद है. इसलिए, पॉप-अप को ब्लॉक कर दिया गया है."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "पेज के बैक-फ़ॉरवर्ड कैश मेमोरी सुविधा का इस्तेमाल करने के दौरान, सर्विस वर्कर को चालू किया गया."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "दस्तावेज़ में किसी गड़बड़ी की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद कर दिया गया है."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Fenced<PERSON>rames का इस्तेमाल करने वाले पेजों को बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "इस पेज को कैश मेमोरी से हटा दिया गया था, ताकि दूसरे पेज को कैश मेमोरी में सेव करने की अनुमति दी जा सके."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "जिन पेजों पर मीडिया स्ट्रीम को ऐक्सेस किया जा सकता है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "पोर्टल का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "जिन पेजों के लिए कोई ओपेन IndexedDB कनेक्शन मौजूद है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "गलत एपीआई इस्तेमाल किए गए थे."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "जिन पेजों के लिए, एक्सटेंशन की मदद से JavaScript को इंजेक्ट किया जाता है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "जिन पेजों की StyleSheet, एक्सटेंशन की मदद से इंजेक्ट की जाती है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | internalError": {"message": "कोई अंदरूनी गड़बड़ी हुई."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "कीपअलाइव (चालू रखें) के अनुरोध की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "कीबोर्ड लॉक का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | loading": {"message": "इस पेज को छोड़ने से पहले, यह पूरी तरह से लोड नहीं हुआ था."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा, उन पेजों के लिए चालू नहीं की जा सकती जिनके मुख्य संसाधन में cache-control:no-cache मौजूद हो."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा, उन पेजों के लिए चालू नहीं की जा सकती जिनके मुख्य संसाधन में cache-control:no-store मौजूद हो."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "पेज को बैक-फ़ॉरवर्ड कैश मेमोरी से वापस लाने से पहले ही, नेविगेशन को रद्द कर दिया गया था."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "पेज को कैश मेमोरी से हटा दिया गया, क्योंकि चालू इंटरनेट कनेक्शन में बहुत ज़्यादा डेटा मिला था. Chrome, कैश मेमोरी में सेव किए गए पेज को मिलने वाले डेटा की मात्रा को सीमित करता है."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "जिन पेजों में इनफ़्लाइट फ़ेच() या XHR मौजूद होता है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "पेज को बैक-फ़ॉरवर्ड कैश मेमोरी से हटा दिया गया था, क्योंकि दूसरे वेबलिंक पर भेजने का एक नेटवर्क अनुरोध चालू था."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "पेज को कैश मेमोरी से हटा दिया गया था, क्योंकि कोई इंटरनेट कनेक्शन बहुत देर से चालू था. Chrome, कैश मेमोरी में सेव किए गए पेज को मिलने वाले डेटा की अवधि को सीमित करता है."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा, उन पेजों के लिए चालू नहीं की जा सकती जिनके रिस्पॉन्स हेड मान्य नहीं होते."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "नेविगेशन, मुख्य फ़्रेम की जगह किसी और फ़्रेम में हुआ."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "जिन पेजों के लिए इंडेक्स किया गया DB ट्रांज़ैक्शन चल रहा हो वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "जिन पेजों के लिए इन-फ़्लाइट नेटवर्क का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "जिन पेजों के लिए इन-फ़्लाइट नेटवर्क को फ़ेच करने का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "जिन पेजों के लिए इन-फ़्लाइट नेटवर्क का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "जिन पेजों के लिए इन-फ़्लाइट XHR नेटवर्क का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Payment<PERSON><PERSON>ger का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "पिक्चर में पिक्चर सुविधा का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | portal": {"message": "पोर्टल का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | printing": {"message": "जिन पेजों के लिए प्रिंटिंग यूज़र इंटरफ़ेस (यूआई) दिखता है उनके लिए फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा इस्तेमाल नहीं की जा सकती."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "पेज को '`window.open()`' की मदद से खोला गया था और इसका एक रेफ़रंस दूसरे टैब में मौजूद है या पेज किसी नई विंडो में खुला है."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में पेज को रेंडर करने की प्रोसेस क्रैश हो गई है."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में, पेज को पेज को रेंडर करने की प्रक्रिया रोक दी गई है."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "जिन पेजों के लिए ऑडियो रिकॉर्ड करने की अनुमतियां मांगी गई हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "जिन पेजों ने सेंसर अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "जिन पेजों ने बैकग्राउंड सिंक या अनुमतियां फ़ेच करने का अनुरोध किया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "जिन पेजों ने MIDI अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "जिन पेजों ने सूचनाएं भेजने के लिए अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "जिन पेजों ने डिवाइस के स्टोरेज का ऐक्सेस मांगा है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "जिन पेजों के लिए वीडियो रिकॉर्ड करने की अनुमतियां मांगी गई हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "सिर्फ़ उन पेजों को कैश मेमोरी में सेव किया जा सकता है जिनकी यूआरएल स्कीम एचटीटीपी / एचटीटीपीएस है."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "जब यह पेज बैक-फ़ॉरवर्ड कैश मेमोरी में सेव किया गया था, तब उस पर किसी सर्विस वर्कर ने दावा किया था."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "किसी सर्विस वर्कर ने बैक-फ़ॉरवर्ड कैश मेमोरी में मौजूद पेज को `MessageEvent` भेजने की कोशिश की."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "पेज के बैक-फ़ॉरवर्ड कैश मेमोरी में मौजूद होने के दौरान, ServiceWorker का रजिस्ट्रेशन रद्द किया गया."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "सर्विस वर्कर ऐक्टिवेट होने की वजह से, इस पेज को बैक-फ़ॉरवर्ड कैश मेमोरी से हटा दिया गया था."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome रीस्टार्ट हो गया और बैक-फ़ॉरवर्ड कैश मेमोरी में सेव की गई एंट्री मिट गई हैं."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "SpeechRecog<PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "SpeechSynthesis का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "पेज पर मौजूद किसी iframe ने नेविगेशन शुरू किया, जो पूरा नहीं हो सका."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा, उन पेजों के लिए चालू नहीं की जा सकती जिनके सब-रिसोर्स में cache-control:no-cache मौजूद हो."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा, उन पेजों के लिए चालू नहीं की जा सकती जिनके सबरिसोर्स में cache-control:no-store मौजूद हो."}, "core/lib/bf-cache-strings.js | timeout": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में रहने की समय-सीमा पार हो जाने की वजह से, पेज एक्सपायर हो गया."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी का इस्तेमाल करने के दौरान, पेज की समय-सीमा खत्म हो गई थी. इस बात की ज़्यादा संभावना है कि pagehide हैंडलर के ज़्यादा समय तक चलते रहने की वजह से ऐसा हुआ होगा."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "पेज के मुख्य फ़्रेम में अनलोड हैंडलर मौजूद है."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "पेज के किसी सब फ़्रेम में, अनलोड करने वाला हैंडलर मौजूद है."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "ब्राउज़र ने उपयोगकर्ता एजेंट को बदलने वाला हेडर बदल दिया है."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "जिन पेजों ने वीडियो या ऑडियो रिकॉर्ड करने का ऐक्सेस दिया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "WebDatabase का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webHID": {"message": "WebHID का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "WebLocks का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "WebNfc का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "WebOTPService का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी को उन पेजों के लिए चालू नहीं किया जा सकता जो WebRTC का इस्तेमाल करते हैं."}, "core/lib/bf-cache-strings.js | webShare": {"message": "WebShare का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी को उन पेजों के लिए चालू नहीं किया जा सकता जो WebSocket API का इस्तेमाल करते हैं."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को उन पेजों के लिए चालू नहीं किया जा सकता जो WebTransport का इस्तेमाल करते हैं."}, "core/lib/bf-cache-strings.js | webXR": {"message": "WebXR का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "https: और http: यूआरएल स्कीम जोड़ें, ताकि CSP पुराने ब्राउज़र के साथ काम कर सके. 'strict-dynamic' के साथ काम करने वाले ब्राउज़र पर इन स्कीम को अनदेखा किया जा सकता है."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "CSP3 के बाद से disown-opener काम नहीं करता. इसके बजाय, कृपया Cross-Origin-Opener-Policy हेडर का इस्तेमाल करें."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "CSP2 के बाद से referrer काम नहीं करता. इसके बजाय, कृपया Referrer-Policy हेडर का इस्तेमाल करें."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "CSP2 के बाद से reflected-xss काम नहीं करता. इसके बजाय, कृपया X-XSS-Protection हेडर का इस्तेमाल करें."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "base-uri डायरेक्टिव शामिल न करने पर, इंजेक्ट किए गए <base> टैग की मदद से सभी मिलते-जुलते यूआरएल (जैसे, स्क्रिप्ट) के लिए बेस यूआरएल सेट हो जाता है. ऐसा उस डोमेन में होता है जिसे कोई हमलावर कंट्रोल करता है. base-uri को 'none' या 'self' पर सेट करें."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "जो object-src उपलब्ध नहीं है, वह ऐसे प्लग इन के इंजेक्शन की अनुमति देता है जो असुरक्षित स्क्रिप्ट को लागू करते हैं. अगर आप कर सकते हैं, तो object-src को 'none' पर सेट करें."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src डायरेक्टिव मौजूद नहीं है. इस वजह से, असुरक्षित स्क्रिप्ट चल सकती हैं."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "क्या आप सेमीकोलन डालना भूल गए? {keyword} एक कीवर्ड नहीं, डायरेक्टिव लग रहा है."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces में base64 charset का इस्तेमाल किया जाना चाहिए."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces में कम से कम आठ वर्ण होने चाहिए."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "इस डायरेक्टिव में, प्लेन यूआरएल स्कीम ({keyword}) का इस्तेमाल करने से बचें. प्लेन यूआरएल स्कीम, स्क्रिप्ट को असुरक्षित डोमेन से पाने की अनुमति देती हैं."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "इस डायरेक्टिव में, प्लेन वाइल्डकार्ड ({keyword}) का इस्तेमाल करने से बचें. प्लेन वाइल्डकार्ड, स्क्रिप्ट को असुरक्षित डोमेन से पाने की अनुमति देते हैं."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "रिपोर्टिंग डेस्टिनेशन को सिर्फ़ report-to डायरेक्टिव की मदद से कॉन्फ़िगर किया जाता है. यह डायरेक्टिव सिर्फ़ Chromium कोड वाले ब्राउज़र में काम करता है, इसलिए report-uri डायरेक्टिव भी इस्तेमाल करने का सुझाव दिया जाता है."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "कोई भी CSP, रिपोर्टिंग डेस्टिनेशन को कॉन्फ़िगर नहीं करता. समय के साथ, इसकी वजह से CSP को मेंटेन करने और किसी भी तरह के ब्रेकेज को मॉनिटर करने में दिक्कत होती है."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "होस्ट की 'व्हाइटलिस्ट' को अक्सर बायपास किया जा सकता है. अगर ज़रूरी हो, तो इसके बजाय 'strict-dynamic' के साथ-साथ CSP nonces या hashes का इस्तेमाल करें."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "इस CSP डायरेक्टिव की जानकारी नहीं है."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} एक गलत/अमान्य कीवर्ड लग रहा है."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "'unsafe-inline', असुरक्षित इन-पेज स्क्रिप्ट और इवेंट हैंडलर चलाने की अनुमति देता है. एक-एक करके स्क्रिप्ट को अनुमति देने के लिए, CSP nonces या hashes इस्तेमाल करें."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "'unsafe-inline' जोड़ें, ता<PERSON><PERSON> CSP पुराने ब्राउज़र के साथ काम कर सके. nonces/hashes के साथ काम करने वाले ब्राउज़र पर 'unsafe-inline' को अनदेखा किया जा सकता है."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "सीओआरएस `Access-Control-Allow-Headers` हैंडलिंग में, वाइल्डकार्ड सिंबल (*) की ओर से अनुमति देने की सुविधा कवर नहीं की जाएगी."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "जिन रिसॉर्स के यूआरएल में खाली सफ़ेद जगह के हटाए गए वर्ण `(n|r|t)` और लेस-दैन वर्ण (`<`), दोनों शामिल हों उनका अनुरोध नहीं किया जा सकता है. उन रिसॉर्स को ब्लॉक किया गया है. एलिमेंट एट्रिब्यूट की वैल्यू जैसी जगहों से, कृपया नई लाइनों को हटाएं और कम वर्णों को एन्कोड करें, ताकि इन रिसॉर्स को लोड किया जा सके."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` is deprecated, instead use standardized API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` is deprecated, instead use standardized API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` is deprecated, instead use standardized API: `nextHopProtocol` in Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Cookies containing a `(0|r|n)` character will be rejected instead of truncated."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "`document.domain` को सेट करके, एक ही ऑरिजिन से जुड़ी नीति में पाबंदियों को हटाने की सुविधा पर रोक लगा दी गई है. इसे डिफ़ॉल्ट रूप से बंद कर दिया जाएगा. इस्तेमाल पर पाबंदी लागने की यह चेतावनी, उस क्रॉस-ऑरिजिन ऐक्सेस के लिए है जिसे `document.domain` सेटिंग की मदद से चालू किया गया था."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "क्रॉस-ओरिजिन iframes में {PH1} को ट्रिगर करने पर रोक लगा दी गई है और आने वाले समय में इसे हटा दिया जाएगा."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "`-internal-media-controls-overlay-cast-button` सिलेक्टर के बजाय, `disableRemotePlayback` एट्रिब्यूट का इस्तेमाल किया जाना चाहिए, ताकि डिफ़ॉल्ट कास्ट इंटिग्रेशन को बंद किया जा सके."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} के इस्तेमाल पर रोक लगा दी गई है. इसके बजाय, कृपया {PH2} का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "यह अनुवाद किए गए एक ऐसे मैसेज का उदाहरण है जिसमें रोकी गई सुविधा से जुड़ी समस्या के बारे में बताया गया हो."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "`document.domain` को सेट करके, एक ही ऑरिजिन से जुड़ी नीति में पाबंदियों को हटाने की सुविधा पर रोक लगा दी गई है. इसे डिफ़ॉल्ट रूप से बंद कर दिया जाएगा. इस सुविधा का इस्तेमाल जारी रखने के लिए, कृपया ऑरिजिन के हिसाब से बने एजेंट क्लस्टर से ऑप्ट आउट करें. इसके लिए, `Origin-Agent-Cluster: ?0` हेडर के साथ-साथ, दस्तावेज़ और फ़्रेम के लिए एचटीटीपी रिस्पॉन्स भेजें. ज़्यादा जानकारी के लिए, https://developer.chrome.com/blog/immutable-document-domain/ पर जाएं."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` के इस्तेमाल पर रोक लगा दी गई है और इसे हटा दिया जाएगा. इसके बजाय, कृपया `Event.composedPath()` का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT` हेडर के इस्तेमाल पर पाबंदी लगा दी गई है और इसे हटा दिया जाएगा. Chrome को 30 अप्रैल, 2018 के बाद जारी किए गए सभी सर्टिफ़िकेट के लिए, पारदर्शिता सर्टिफ़िकेट फ़्रेमवर्क की ज़रूरत होगी. ऐसा सार्वजनिक रूप से भरोसेमंद सर्टिफ़िकेट के तौर पर पुष्टि किए गए सर्टिफ़िकेट के लिए ज़रूरी है."}, "core/lib/deprecations-strings.js | feature": {"message": "ज़्यादा जानकारी के लिए, सुविधा की स्थिति वाला पेज देखें."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` और `watchPosition()` असुरक्षित ऑरिजिन पर, अब काम नहीं करते. इस सुविधा का इस्तेमाल करने के लिए, आपको अपने ऐप्लिकेशन को एचटीटीपीएस जैसे किसी सुरक्षित ऑरिजिन पर स्विच करना होगा. ज़्यादा जानकारी के लिए, https://goo.gle/chrome-insecure-origins पर जाएं."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "असुरक्षित ऑरिजिन पर, `getCurrentPosition()` और `watchPosition()` के इस्तेमाल पर पाबंदी लगा दी गई है. इस सुविधा का इस्तेमाल करने के लिए, आपको अपने ऐप्लिकेशन को एचटीटीपीएस जैसे किसी सुरक्षित ऑरिजिन पर स्विच करना होगा. ज़्यादा जानकारी के लिए, https://goo.gle/chrome-insecure-origins पर जाएं."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` के इस्तेमाल पर पाबंदी लगा दी गई है. इसके बजाय `RTCPeerConnectionIceErrorEvent.address` या `RTCPeerConnectionIceErrorEvent.port` इस्तेमाल करें."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "`canmakepayment` सर्विस वर्कर इवेंट से लिए गए, व्यापारी या कंपनी के ऑरिजिन और उनके आर्बिट्रेरी डेटा के इस्तेमाल पर पाबंदी लगा दी गई है और उन्हें हटा दिया जाएगा: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS cannot be loaded from `file:` URLs unless they end in a `.css` file extension."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Using `SourceBuffer.abort()` to abort `remove()`'s asynchronous range removal is deprecated due to specification change. Support will be removed in the future. You should listen to the `updateend` event instead. `abort()` is intended to only abort an asynchronous media append or reset parser state."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Setting `MediaSource.duration` below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit `remove(newDuration, oldDuration)` on all `sourceBuffers`, where `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "माइलस्टोन {milestone} इस्तेमाल करने पर, यह बदलाव लागू हो जाएगा."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "अब असुरक्षित ऑरिजिन से, Notification API का इस्तेमाल नहीं किया जा सकेगा. कृपया अपने ऐप्लिकेशन को एचटीटीपीएस जैसे सुरक्षित ऑरिजिन पर ले जाएं. ज़्यादा जानकारी के लिए, https://goo.gle/chrome-insecure-origins पर जाएं."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Notification API को इस्तेमाल करने की अनुमति से जुड़ा अनुरोध, क्रॉस-ऑरिजिन iframe से नहीं किया जा सकता. इसके बजाय, आप किसी टॉप लेवल फ़्रेम से अनुरोध करें या नई विंडो का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "असुरक्षित कॉन्टेक्स्ट में WebSQL के इस्तेमाल पर रोक लगा दी गई है और इसे जल्द ही हटा दिया जाएगा. Web Storage या Indexed Database का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "img, video, और canvas टैग पर `overflow: visible` लागू होने से, विज़ुअल कॉन्टेंट एलिमेंट की सीमाओं के बाहर दिखते हैं. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md देखें."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` के इस्तेमाल पर रोक लगा दी गई है. इसके बजाय, पेमेंट हैंडलर के लिए just-in-time इंस्टॉल करने की सुविधा इस्तेमाल करें."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "आपके `PaymentRequest` कॉल ने Content-Security-Policy (सीएसपी) `connect-src` डायरेक्टिव को बायपास किया है. हालांकि, यह बायपास अब काम नहीं करता. कृपया `PaymentRequest` API (`supportedMethods` फ़ील्ड में) से अपने सीएसपी `connect-src` डायरेक्टिव में, पैसे चुकाने के तरीके के आइडेंटिफ़ायर को जोड़ें."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` के इस्तेमाल पर पाबंदी लगा दी गई है. इसके बजाय, कृपया स्टैंडर्ड `navigator.storage` का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<picture>` पैरंट वाला `<source src>` गलत है. इसलिए, इसे अनदेखा किया गया है. इसके बजाय, कृपया `<source srcset>` का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` के इस्तेमाल पर पाबंदी लगा दी गई है. इसके बजाय, कृपया स्टैंडर्ड `navigator.storage` का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. `**********************/`) are blocked."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "कंस्ट्रेंट `DtlsSrtpKeyAgreement` को हटा दिया गया है. आपने इस कंस्ट्रेंट के लिए, `false` वैल्यू दी है. इससे ऐसा लगता है कि आपने हटाए गए `SDES key negotiation` तरीके को इस्तेमाल करने की कोशिश की है. इस सुविधा को हटा दिया गया है. इसके बजाय, ऐसी सेवा का इस्तेमाल करें जो `DTLS key negotiation` के साथ काम करती हो."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "कंस्ट्रेंट `DtlsSrtpKeyAgreement` को हटा दिया गया है. आपने इस कंस्ट्रेंट के लिए `true` वैल्यू दी है, जिससे कोई फ़र्क़ नहीं पड़ेगा. हालांकि, व्यवस्थित रखने के लिए, इस कंस्ट्रेंट को हटाया जा सकता है."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` की पहचान की गई. `Session Description Protocol` का यह डायलेक्ट अब काम नहीं करता. इसके बजाय, कृपया `Unified Plan SDP` का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`{sdpSemantics:plan-b}` वाला `RTCPeerConnection` बनाते समय, `Plan B SDP semantics` का इस्तेमाल किया जाता है. यह `Session Description Protocol` का ऐसा पुराना वर्शन है जो स्टैंडर्ड नहीं है. इसे वेब प्लैटफ़ॉर्म से हमेशा के लिए मिटा दिया गया है. `IS_FUCHSIA` के साथ प्रोटोकॉल बनाते समय, यह अब भी उपलब्ध रहता है. हालांकि, हम इसे जल्द से जल्द मिटाना चाहते हैं. इसलिए, इसका इस्तेमाल न करें. इसका स्टेटस जानने के लिए, https://crbug.com/1302249 पर जाएं."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` विकल्प के इस्तेमाल पर रोक लगा दी गई है और इसे हटा दिया जाएगा."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` के लिए क्रॉस-ऑरिजिन आइसोलेशन ज़रूरी है. ज़्यादा जानकारी के लिए, https://developer.chrome.com/blog/enabling-shared-array-buffer/ देखें."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "उपयोगकर्ता की गतिविधि के बिना, `speechSynthesis.speak()` का इस्तेमाल नहीं किया जा सकता और इस सुविधा को हटा दिया जाएगा."}, "core/lib/deprecations-strings.js | title": {"message": "पाबंदी वाली सुविधा का इस्तेमाल किया गया"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "`SharedArrayBuffer` का इस्तेमाल जारी रखने के लिए, एक्सटेंशन को क्रॉस-ऑरिजिन आइसोलेशन के लिए ऑप्ट करना चाहिए. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ देखें."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1}, वेंडर के हिसाब से है. इसके बजाय, कृपया स्टैंडर्ड {PH2} का इस्तेमाल करें."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16, `XMLHttpRequest` में response.json के साथ काम नहीं करता"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "मुख्य थ्रेड पर सिंक किए गए `XMLHttpRequest` के इस्तेमाल पर रोक लगा दी गई है. इसकी वजह यह है कि असली उपयोगकर्ता के अनुभव पर इसका बुरा असर पड़ रहा था. इस बारे में किसी भी तरह की मदद के लिए, https://xhr.spec.whatwg.org/ पर जाएं."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` के इस्तेमाल पर पाबंदी लगा दी गई है. इसके बजाय, कृपया `isSessionSupported()` का इस्तेमाल करें और रिज़ॉल्व की गई बूलियन वैल्यू देखें."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "मुख्य थ्रेड ब्लॉक होने का समय"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "कैश TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "ब्यौरा"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "कुल समय"}, "core/lib/i18n/i18n.js | columnElement": {"message": "तत्व"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "काम नहीं कर पाने वाले एलिमेंट"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "जग<PERSON> की जानकारी"}, "core/lib/i18n/i18n.js | columnName": {"message": "नाम"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "बजट से ज़्यादा"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "अनुरोध"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "संसाधन का साइज़"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "संसाधन का प्रकार"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "स्रोत"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "शुरुआत का समय"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "बिताया गया समय"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "ट्रांसफ़र आकार"}, "core/lib/i18n/i18n.js | columnURL": {"message": "यूआरएल"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "संभावित बचत"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "संभावित बचत"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "{wastedBytes, number, bytes} किबीबाइट मेमोरी बचाई जा सकती है"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 एलिमेंट मिला}one{# एलिमेंट मिला}other{# एलिमेंट मिले}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "{wastedMs, number, milliseconds} मि. से. की संभावित बचत"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "दस्तावेज़"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "पहला सार्थक पेंट"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "फ़ॉन्ट"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "इमेज"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "इंटरैक्शन टू नेक्स्ट पेंट"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "ज़्यादा"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "कम"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "मीडियम"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "सबसे ज़्यादा संभावित फ़र्स्ट इनपुट डिले"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "मीडिया"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} मि.से."}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "कोई दूसरा विकल्प"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "अन्य संसाधन"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "स्क्रिप्ट"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} से."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "स्टाइलशीट"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "तीसरा पक्ष"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "कुल"}, "core/lib/lh-error.js | badTraceRecording": {"message": "आपके पेज लोड में ट्रेस की रिकॉर्डिंग करते समय कोई गड़बड़ी हुई. कृपया Lighthouse को फिर से चलाएं. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "शुरुआती डीबगर प्रोटोकॉल कनेक्शन का इंतज़ार करते हुए समय खत्म हो गया."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome ने पेज लोड के दौरान कोई भी स्क्रीन शॉट इकट्ठा नहीं किया. कृपया पक्का करें कि पेज पर सामग्री दिखाई दे रही है. इसके बाद, Lighthouse को फिर से चलाकर देखें. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS सर्वर दिए गए डोमेन को हल नहीं कर सका."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "ज़रूरी {artifactName} इकट्ठा करने वाला संसाधन चलाने में गड़बड़ी हुई: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "एक अंदरूनी Chrome गड़बड़ी हुई. कृपया Chrome को रीस्टार्ट करें और Lighthouse को फिर से चलाकर देखें."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "ज़रूरी {artifactName} इकट्ठा करने वाला संसाधन नहीं चलाया जा सका."}, "core/lib/lh-error.js | noFcp": {"message": "पेज ने किसी भी कॉन्टेंट को पेंट नहीं किया. कृपया पक्का करें कि आप पेज को लोड करते समय, ब्राउज़र विंडो को फ़ोरग्राउंड में रखें और फिर से कोशिश करें. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "पेज पर वह कॉन्टेंट नहीं दिखाया गया जिसे सबसे बड़े कॉन्टेंटफ़ुल पेंट (एलसीपी) के तौर पर मंज़ूरी मिली है. पक्का करें कि पेज का एलसीपी एलिमेंट मान्य हो और दोबारा कोशिश करें. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "यह पेज, एचटीएमएल भाषा कोड में नहीं है. यह MIME टाइप {mimeType} में दिया गया है."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Chrome का यह वर्शन बहुत पुराना है, इसलिए यह '{featureName}' के साथ काम नहीं करता. पूरे नतीजे देखने के लिए, नए वर्शन का इस्तेमाल करें."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse आपका अनुरोध किया गया पेज ठीक से लोड नहीं कर सका. पक्का करें कि आप सही यूआरएल को टेस्ट कर रहे हैं और सर्वर सभी अनुरोधों के लिए ठीक से काम कर रहा है."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse उस यूआरएल को भरोसेमंद रूप से लोड नहीं कर सका जिसका आपने अनुरोध किया था क्योंकि पेज ने काम करना बंद कर दिया था."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "आपके दिए यूआरएल में सही सुरक्षा सर्टिफ़िकेट नहीं है. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome ने वे पेज लोड नहीं किए जिन पर अचानक दिखने वाले विज्ञापन होते हैं. पक्का करें कि आप सही यूआरएल को टेस्ट कर रहे हैं और सर्वर सभी अनुरोधों के लिए ठीक से काम कर रहा है."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse आपका अनुरोध किया गया पेज ठीक से लोड नहीं कर सका. पक्का करें कि आप सही यूआरएल को टेस्ट कर रहे हैं और सर्वर सभी अनुरोधों के लिए ठीक से काम कर रहा है. (जानकारी: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse आपका अनुरोध किया गया पेज ठीक से लोड नहीं कर सका. पक्का करें कि आप सही यूआरएल को टेस्ट कर रहे हैं और सर्वर सभी अनुरोधों के लिए ठीक से काम कर रहा है. (स्थिति कोड: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "आपके पेज को लोड होने में बहुत ज़्यादा समय लगा. अपने पेज के लोड होने का समय कम करने के लिए, कृपया रिपोर्ट में दिए गए अवसरों का फ़ायदा लें. इसके बाद, Lighthouse को फिर से चलाकर देखें. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools प्रोटोकॉल जवाब के लिए इंतज़ार का समय, तय समय से ज़्यादा हो गया है. (तरीका:{protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "संसाधन की सामग्री लाने में दिए गए समय से ज़्यादा समय लगा"}, "core/lib/lh-error.js | urlInvalid": {"message": "आपका दिया हुआ यूआरएल गलत लगता है."}, "core/lib/navigation-error.js | warningXhtml": {"message": "पेज MIME टाइप, XHTML है: Lighthouse साफ़ तौर पर, इस तरह के दस्तावेज़ के साथ काम नहीं करता"}, "core/user-flow.js | defaultFlowName": {"message": "यूज़र फ़्लो ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "नेविगेशन रिपोर्ट ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "स्नैपशॉट रिपोर्ट ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "टाइमस्पैन रिपोर्ट ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "सभी रिपोर्ट"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "कैटगरी"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "सुलभता"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "सबसे अच्छे तरीके"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "परफ़ॉर्मेंस"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "प्रगतिशील वेब ऐप्लिकेशन"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "डेस्कटॉप"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "लाइटहाउस फ़्लो रिपोर्ट को समझें"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "फ़्लो रिपोर्ट को समझें"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "इसके लिए नेविगेशन रिपोर्ट का इस्तेमाल करें..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "इसके लिए स्नैपशॉट रिपोर्ट का इस्तेमाल करें..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "इसके लिए टाइमस्पैन रिपोर्ट का इस्तेमाल करें..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "लाइटहाउस परफ़ॉर्मेंस स्कोर हासिल करें."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "सबसे बड़ा कॉन्टेंटफ़ुल पेंट और स्पीड इंडेक्स जैसी पेज लोड की परफ़ॉर्मेंस मेट्रिक का आकलन करें."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "प्रोग्रेसिव वेब ऐप्लिकेशन की क्षमताओं का आकलन करें."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "एक पेज के ऐप्लिकेशन या जटिल फ़ॉर्म में, सुलभता से जुड़ी समस्याएं ढूंढें."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "इंटरैक्शन के पीछे छिपे हुए मेन्यू और यूज़र इंटरफ़ेस (यूआई) एलिमेंट के सबसे सही तरीकों का आकलन करें."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "कई इंटरैक्शन पर, लेआउट में हुए बदलाव और JavaScript लागू होने का समय मापें."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "लंबे समय तक इस्तेमाल किए वाले पेजों और एक पेज के ऐप्लिकेशन के अनुभव को बेहतर बनाने के लिए, परफ़ॉर्मेंस से जुड़े अवसरों के बारे में जानें."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "सबसे असरदार ऑडिट"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{जानकारी देने वाला {numInformative} ऑडिट}one{जानकारी देने वाला {numInformative} ऑडिट}other{जानकारी देने वाले {numInformative} ऑडिट}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "मोबाइल"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "पेज लोड"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "नेविगेशन रिपोर्ट, मूल लाइटहाउस रिपोर्ट की तरह ही एक पेज लोड का विश्लेषण करती है."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "नेविगेशन रिपोर्ट"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} नेविगेशन रिपोर्ट}one{{numNavigation} नेविगेशन रिपोर्ट}other{{numNavigation} नेविगेशन रिपोर्ट}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} पासेबल ऑडिट}one{{numPassableAudits} पासेबल ऑडिट}other{{numPassableAudits} पासेबल ऑडिट}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} ऑडिट पास किया गया}one{{numPassed} ऑडिट पास किया गया}other{{numPassed} ऑडिट पास किए गए}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "ठीक-ठाक"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "गड़बड़ी"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "खराब"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "अच्छी"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "सेव करें"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "पेज की कैप्चर की गई स्थिति"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "स्नैपशॉट रिपोर्ट किसी खास स्थिति में, खास तौर पर उपयोगकर्ता इंटरैक्शन के बाद पेज का विश्लेषण करती है."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "किसी खास समय पर, वेब पेज की स्थिति बताने वाली रिपोर्ट"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} स्नैपशॉट रिपोर्ट}one{{numSnapshot} स्नैपशॉट रिपोर्ट}other{{numSnapshot} स्नैपशॉट रिपोर्ट}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "खास जानकारी"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "उपयोगकर्ता के इंटरैक्शन"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "टाइमस्पैन रिपोर्ट किसी भी समय अवधि का, खास तौर पर उपयोगकर्ता इंटरैक्शन वाली समय अवधि का विश्लेषण करती है."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "पेज पर उपयोगकर्ता के इंटरैक्शन की जानकारी देने वाली रिपोर्ट"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} टाइमस्पैन रिपोर्ट}one{{numTimespan} टाइमस्पैन रिपोर्ट}other{{numTimespan} टाइमस्पैन रिपोर्ट}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse की यूज़र फ़्लो रिपोर्ट"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "ऐनिमेशन वाले कॉन्टेंट के लिए, [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) का इस्तेमाल करें. इससे, कॉन्टेंट के ऑफ़स्क्रीन होने पर सीपीयू का इस्तेमाल कम होगा."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "अपने सभी [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) कॉम्पोनेंट को WebP फ़ॉर्मैट में दिखाएं. साथ ही, अन्य ब्राउज़र के लिए सही फ़ॉलबैक तय करें. [ज़्यादा जानें](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "पक्का करें कि आप इमेज को अपने-आप धीमे लोड करने के लिए, [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) का इस्तेमाल कर रहे हैं. [ज़्यादा जानें](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[एएमपी लेआउट को सर्वर-साइड पर रेंडर करने](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) के लिए, [एएमपी ऑप्टिमाइज़र](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) जैसे टूल इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "[AMP दस्तावेज़](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) देखें और पक्का करें कि सभी स्टाइल इसके साथ काम करते हैं."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) कॉम्पोनेंट, [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) एट्रिब्यूट के साथ काम करता है. यह बताता है कि अलग-अलग साइज़ की स्क्रीन के हिसाब से किन इमेज एसेट का इस्तेमाल किया जाए. [ज़्यादा जानें](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "अगर बहुत बड़ी सूचियां रेंडर की जा रही हैं, तो कॉम्पोनेंट डेवलपमेंट किट (सीडीके) के साथ इसे वर्चुअल तरीके से स्क्रोल करें. [ज़्यादा जानें](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "अपने JavaScript बंडलों को छोटा करने के लिए, [रूट-लेवल पर कोड को अलग-अलग करने की सुविधा](https://web.dev/route-level-code-splitting-in-angular/) लागू करें. साथ ही, [ऐंगुलर सर्विस वर्कर](https://web.dev/precaching-with-the-angular-service-worker/) की मदद से, एसेट को पहले से कैश मेमोरी में सेव करें."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "अगर आप Angular CLI का इस्तेमाल कर रहे हैं, तो पक्का करें कि बिल्ड, प्रोडक्शन मोड में जनरेट किए गए हैं. [ज़्यादा जानें](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "अगर आप ऐंगुलर सीएलआई का इस्तेमाल कर रहे हैं, तो अपने बंडलों की जांच करने के लिए प्रोडक्शन बिल्ड में स्रोत मैप जोड़ें. [ज़्यादा जानें](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "नेविगेशन की रफ़्तार बढ़ाने के लिए रूट समय से पहले लोड करें. [ज़्यादा जानें](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "इमेज ब्रेकपॉइंट प्रबंधित करने के लिए, कॉम्पोनेंट डेवलपमेंट किट (सीडीके) में `BreakpointObserver` सुविधा का इस्तेमाल करें. [ज़्यादा जानें](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "किसी ऐसी सेवा में अपनी GIF अपलोड करें जो उसे HTML5 वीडियो में जोड़ने के लिए तैयार रख सके."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "अपनी थीम में पसंद के मुताबिक फ़ॉन्ट की जानकारी देने के दौरान, `@font-display` के बारे में बताएं."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "अपनी साइट पर, [कन्वर्ट इमेज स्टाइल का इस्तेमाल करके WebP इमेज फ़ॉर्मैट](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) को कॉन्फ़िगर करें."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "ऐसा [Drupal मॉड्यूल](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) इंस्टॉल करें जो इमेज को धीमे लोड कर पाए. परफ़ॉर्मेंस को बेहतर बनाने के लिए, इस तरह के मॉड्यूल किसी भी ऑफ़स्क्रीन इमेज को अलग करने की सुविधा देते हैं."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "ज़रूरी सीएसएस और JavaScipt को इनलाइन करने के लिए या एसिंक्रोनस तौर पर JavaScript से लोड हो सकने वाली एसेट के लिए किसी मॉड्यूल का इस्तेमाल करें, जैसे कि [सीएसएस/जेएस का, बेहतर तरीके से एक साथ दिखने वाला](https://www.drupal.org/project/advagg) मॉड्यूल. ध्यान रखें कि इस मॉड्यूल से मिलने वाली ऑप्टिमाइज़ेशन आपकी साइट को नुकसान पहुंचा सकती हैं. इसलिए, शायद आपको कोड में बदलाव करना पड़े."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "थीम, मॉड्यूल, और सर्वर की खास बातें, सर्वर से जवाब मिलने का समय तय करने में मदद करती हैं. ज़्यादा ऑप्टिमाइज़ की हुई थीम ढूंढें, ऑप्टिमाइज़ेशन मॉड्यूल को सावधानी से चुनें, और/या अपना सर्वर अपग्रेड करें. आपके होस्टिंग सर्वर को डेटाबेस क्वेरी में लगने वाले समय को कम करने के लिए, Redis या Memcached जैसे PHP opcode कैशिंग, मेमोरी-कैशिंग का इस्तेमाल करना चाहिए. साथ ही, पेजों को तेज़ी से तैयार करने के लिए, ऑप्टिमाइज़ किए हुए ऐप्लिकेशन का लॉजिक भी होना चाहिए."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "आपके पेज पर लोड की गई इमेज के साइज़ को कम करने के लिए, [जवाब देने वाली इमेज की शैली](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) का इस्तेमाल करें. अगर आप किसी पेज पर कई कॉन्टेंट आइटम को दिखाने के लिए, व्यू की सुविधा का इस्तेमाल कर रहे हैं, तो उस पेज पर दिखने वाले कॉन्टेंट आइटम को कम करने के लिए, आप पेज पर नंबर डालने की सुविधा को लागू कर सकते हैं."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "पक्का करें कि आपने \"Administration » Configuration » Development\" पेज में \"सीएसएस फ़ाइलों को एक साथ दिखाने\" की सुविधा को चालू किया है. आप फ़ाइलों को एक साथ दिखाने के ज़्यादा बेहतर विकल्पों को [अतिरिक्त मॉड्यूल](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) की मदद से भी कॉन्फ़िगर करके, अपनी साइट की रफ़्तार को तेज़ कर सकते हैं. ऐसा करने के लिए, आपको सीएसएस शैली को जोड़ना, छोटा करना, और कंप्रेस करना होगा."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "पक्का करें कि आपने \"Administration » Configuration » Development\" पेज में, \"JavaScript फ़ाइलों को एक साथ दिखाने\" की सुविधा को चालू किया है. आप फ़ाइलों को एक साथ दिखाने के ज़्यादा बेहतर विकल्पों को [अतिरिक्त मॉड्यूल](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) की मदद से भी कॉन्फ़िगर करके, अपनी साइट की रफ़्तार को तेज़ कर सकते हैं. ऐसा करने के लिए, आपको अपनी JavaScript एसेट को जोड़ना, छोटा करना, और कंप्रेस करना होगा."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "ऐसे सीएसएस नियमों को हटाएं जिनका इस्तेमाल नहीं हुआ है. साथ ही, सही पेज या पेज के कॉम्पोनेंट में सिर्फ़ ज़रूरी Drupal लाइब्रेरी अटैच करें. ज़्यादा जानकारी के लिए, [Drupal के दस्तावेज़ का लिंक](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) देखें. Chrome DevTools में [कोड कवरेज](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) चलाकर, उन अटैच की गई लाइब्रेरी की पहचान करें जो आपके पेज में बाहरी सीएसएस को जोड़ रही हैं. जब आपकी Drupal साइट में सीएसएस के एक साथ दिखने की सुविधा बंद हो जाती है, तो आप स्टाइलशीट के यूआरएल से पहचान सकते हैं कि ऐसा किस थीम/मॉड्यूल की वजह से हुआ है. ऐसे थीम/मॉड्यूल खोजें जिनके पास उस सूची में कई ऐसी स्टाइलशीट हैं जिनके कोड कवरेज में बहुत से लाल निशान हैं. थीम/मॉड्यूल को स्टाइलशीट तभी क्यू में लगानी चाहिए, जब पेज पर उसका वाकई इस्तेमाल किया गया हो."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "ऐसी JavaScript एसेट हटाएं जिनका इस्तेमाल नहीं हुआ है. साथ ही, सही पेज या पेज के कॉम्पोनेंट में, सिर्फ़ ज़रूरी Drupal लाइब्रेरी अटैच करें. ज़्यादा जानकारी के लिए, [Drupal के दस्तावेज़ का लिंक](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) देखें. Chrome DevTools में [कोड कवरेज](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) चलाकर, अटैच की गई उन लाइब्रेरी की पहचान करें जो आपके पेज में बाहरी JavaScript जोड़ रही हैं. जब आपकी Drupal साइट में JavaScript के एक साथ दिखने की सुविधा बंद हो जाती है, तो आप स्क्रिप्ट के यूआरएल से पहचान सकते हैं कि ऐसा किस थीम/मॉड्यूल की वजह से हुआ है. ऐसी थीम/मॉड्यूल खोजें जिनके पास उस सूची में कई ऐसी स्क्रिप्ट हैं जिनके कोड कवरेज में बहुत सारे लाल निशान हैं. थीम/मॉड्यूल को स्क्रिप्ट तभी क्यू में लगानी चाहिए, जब पेज पर उसका वाकई इस्तेमाल किया गया हो."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "\"Administration » Configuration » Development\" पेज में, \"ब्राउज़र और प्रॉक्सी कैश मेमोरी की ज़्यादा से ज़्यादा उम्र\" सेट करें. [Drupal कैश मेमोरी और परफ़ॉर्मेंस ऑप्टिमाइज़ करने](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources) के बारे में पढ़ें."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "आप ऐसे [मॉड्यूल](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) का इस्तेमाल करें जो क्वालिटी को बनाए रखते हुए, साइट पर अपलोड होने वाली इमेज के साइज़ को अपने-आप कम और ज़्यादा कर सके. यह भी पक्का करें कि आप साइट पर रेंडर की गई सभी इमेज के लिए, Drupal के [जवाब देने वाली इमेज की मूल शैली](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) का इस्तेमाल कर रहे हैं (यह Drupal 8 और इसके बाद वाले वर्शन में उपलब्ध है)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "प्रीकनेक्ट या डीएनएस-प्रीफ़ेच संसाधन संकेतों को, किसी ऐसे [मॉड्यूल](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) को इंस्टॉल और कॉन्फ़िगर करके जोड़ा जा सकता है जो उपयोगकर्ता एजेंट के संसाधन संकेतों के लिए सुविधाएं उपलब्ध कराता हो."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "पक्का करें कि आप Drupal के [जवाब देने वाली इमेज की मूल शैली](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) का इस्तेमाल कर रहे हैं (यह Drupal 8 और इसके बाद वाले वर्शन में उपलब्ध है). इमेज फ़ील्ड को व्यू मोड से रेंडर करने के दौरान, देखते समय या WYSIWYG एडिटर की मदद से अपलोड करने के दौरान, जवाब देने वाली इमेज की शैली का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `Optimize Fonts` को चालू करें, ताकि`font-display` सीएसएस की सुविधा का फ़ायदा अपने-आप मिले. इससे, यह पक्का किया जा सकेगा कि वेबफ़ॉन्ट लोड होने के दौरान, उपयोगकर्ता को टेक्स्ट दिखता है."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और इमेज को WebP में बदलने के लिए, `Next-Gen Formats` को चालू करें."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `<PERSON>zy Load Images` को चालू करें, ताकि ऑफ़-स्क्रीन इमेज तब तक लोड न की जाएं, जब तक उनकी ज़रूरत न हो."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और गैर-ज़रूरी JS/सीएसएस फ़ाइल को कुछ समय तक रोकने के लिए, `Critical CSS` और `Script Delay` को चालू करें."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "[Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) का इस्तेमाल करें, ताकि दुनिया भर में फैले हमारे नेटवर्क पर मौजूद आपके कॉन्टेंट को कैश मेमोरी में सेव किया जा सके. साथ ही, पहली बाइट के समय को बेहतर बनाया जा सके."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `Minify CSS` को चालू करें, ताकि आपकी सीएसएस फ़ाइल अपने-आप छोटी हो जाए. इससे, नेटवर्क के पेलोड साइज़ को कम किया जा सकेगा."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `Minify Javascript` को चालू करें, ताकि आपकी JS फ़ाइल अपने-आप छोटी हो जाए. इससे, नेटवर्क के पेलोड साइज़ को कम किया जा सकेगा."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और इस समस्या को हल करने में मदद पाने के लिए, `Remove Unused CSS` को चालू करें. यह आपकी साइट के हर पेज पर इस्तेमाल होने वाले सीएसएस क्लास की पहचान करेगा. साथ ही, फ़ाइल को छोटा रखने के लिए, दूसरे सभी क्लास को हटाएगा."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `Efficient Static Cache Policy` को चालू करें, ताकि स्टैटिक एसेट के लिए कैश मेमोरी के हेडर में, सुझाई गई वैल्यू सेट की जा सकें."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और इमेज को WebP में बदलने के लिए, `Next-Gen Formats` को चालू करें."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `Pre-Connect Origins` को चालू करें, ताकि `preconnect` संसाधन संकेत अपने-आप जुड़ जाएं. इससे, तीसरे पक्ष की अहम कंपनियों से जल्दी कनेक्शन बनाए जा सकेंगे."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें. साथ ही, `Preload Fonts` और `Preload Background Images` को चालू करें, ताकि `preload` लिंक जोड़े जा सकें. इससे, उन संसाधनों को पहले फ़ेच किया जा सकेगा जिनके लिए फ़िलहाल पेज के लोड होने के बाद अनुरोध किया जाता है."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed) का इस्तेमाल करें और `Resize Images` को चालू करें, ताकि इमेज का साइज़ बदलकर, डिवाइस के हिसाब से सही साइज़ बनाया जा सके. इससे, नेटवर्क के पेलोड साइज़ को कम किया जा सकेगा."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "किसी ऐसी सेवा में अपनी GIF अपलोड करें जो उसे HTML5 वीडियो में जोड़ने के लिए तैयार रख सके."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "ऐसे [प्लग इन](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) या सेवा का इस्तेमाल करें जो आपकी अपलोड की गई इमेज को अपने आप ही सबसे सही फ़ॉर्मैट में बदल देगी."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "[धीमी रफ़्तार से लोड करने वाला ऐसा Jo<PERSON><PERSON> प्लग इन](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) इंस्टॉल करें जिसमें किसी भी ऑफ़स्क्रीन इमेज को अलग करने की सुविधा हो या किसी ऐसे टेंप्लेट का इस्तेमाल करें जो यह सुविधा मुहैया कराता हो. Joomla 4.0 से शुरू करते हुए, सभी नई इमेज में `loading` एट्रिब्यूट [अपने-आप](https://github.com/joomla/joomla-cms/pull/30748) शामिल हो जाएगा."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "ऐसे कई Joomla प्लग इन हैं जो [क्रिटिकल एसेट इनलाइन करने ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) या [कम ज़रूरी संसाधनों को अलग करने](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) में आपकी मदद कर सकते हैं. ध्यान रखें कि ऐसे प्लग इन से मिलने वाली ऑप्टिमाइज़ेशन आपके टेंप्लेट या प्लग इन की सुविधाओं में दिक्कतें पैदा कर सकती हैं. इसलिए, आपको पूरी तरह इनकी जांच करनी होगी."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "टेंप्लेट, एक्सटेंशन, और सर्वर की खास बातें, सर्वर से जवाब मिलने का समय तय करने में मदद करती हैं. ज़्यादा ऑप्टिमाइज़ किया हुआ टेंप्लेट ढूंढें, किसी ऑप्टिमाइज़ेशन एक्सटेंशन को सावधानी से चुनें, और/या अपना सर्वर अपग्रेड करने पर विचार करें."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "अपनी लेख कैटगरी में खास हिस्से दिखाएं (जैसे, ज़्यादा पढ़ें लिंक की मदद से), किसी पेज पर दिखाए गए लेख की संख्या कम करें, अपनी लंबी पोस्ट को कई पेज में बांटें या फिर टिप्पणियों को धीरे-धीरे लोड करने वाले प्लग इन का इस्तेमाल करने पर विचार करें."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "कई [<PERSON><PERSON><PERSON> एक्सटेंशन](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) आपकी साइट की रफ़्तार को बढ़ा सकते हैं. ऐसा करने के लिए वे आपके सीएसएस स्टाइल को जोड़ते हैं, छोटा करते हैं, और कंप्रेस करते हैं. ऐसे टेंप्लेट भी हैं जिनमें यह फ़ंक्शन है."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "कई [<PERSON><PERSON><PERSON> एक्सटेंशन](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) आपकी साइट की रफ़्तार बढ़ा सकते हैं. ऐसा करने के लिए, वे आपकी स्क्रिप्ट को जोड़ते हैं, छोटा करते हैं, और कंप्रेस करते हैं. ऐसे टेंप्लेट भी हैं जिनमें यह फ़ंक्शन है."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "आपके पेज में बिना इस्तेमाल वाले सीएसएस को लोड करने वाले, [Jo<PERSON><PERSON> एक्सटेंशन](https://extensions.joomla.org/) की संख्या को कम करें या स्विच करें. Chrome DevTools में [कोड कवरेज](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) चलाकर, उन एक्सटेंशन की पहचान करें जो आपके पेज में बाहरी सीएसएस जोड़ रहे हैं. आप स्टाइलशीट के यूआरएल से पहचान सकते हैं कि ऐसा किस थीम/प्लग इन ने किया. ऐसे प्लग इन खोजें जिनके पास उस सूची की कई स्टाइलशीट हैं जिनके कोड कवरेज में बहुत से लाल निशान हैं. प्लग इन को स्क्रिप्ट तभी क्यू में लगानी चाहिए, जब पेज पर उसका वाकई इस्तेमाल किया गया हो."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "आपके पेज में बिना इस्तेमाल वाले JavaScript को लोड करने वाले, [Jo<PERSON><PERSON> एक्सटेंशन](https://extensions.joomla.org/) की संख्या को कम करें या स्विच करें. Chrome DevTools में [कोड कवरेज](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) चलाकर, उन प्लग इन की पहचान करें जो आपके पेज में बाहरी JS जोड़ रहे हैं. आप स्क्रिप्ट के यूआरएल से पहचान सकते हैं कि ऐसा किस एक्सटेंशन की वजह से हुआ है. ऐसे एक्सटेंशन खोजें जिनके पास उस सूची में ऐसी कई स्क्रिप्ट हैं जिनके कोड कवरेज में बहुत से लाल निशान हैं. एक्सटेंशन को स्क्रिप्ट तभी क्यू में लगानी चाहिए, जब पेज पर उसका वाकई इस्तेमाल किया गया हो."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[Jo<PERSON><PERSON> में ब्राउज़र कैशिंग](https://docs.joomla.org/Cache) के बारे में पढ़ें."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "ऐसे [इमेज ऑप्टिमाइज़ेशन प्लग इन](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) का इस्तेमाल करें जो आपकी क्वालिटी बरकरार रखते हुए आपकी इमेज कंप्रेस करता हो."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "अपने कॉन्टेंट में जवाब देने वाली इमेज का इस्तेमाल करने के लिए, [जवाब देने वाली इमेज के प्लग इन](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "आप Jo<PERSON>la में Gzip की पेज कंप्रेस करने की सुविधा चालू करके, टेक्स्ट कंप्रेस करने की सुविधा चालू कर सकते हैं (सिस्टम > ग्लोबल कॉन्फ़िगरेशन > सर्वर)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "अगर आप अपने JavaScript एसेट का बंडल नहीं बना रहे हैं, तो [baler](https://github.com/magento/baler) का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Magento की डिफ़ॉल्ट [JavaScript बंडलिंग और छोटा करने की सुविधा](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) बंद करें. इसके बजाय, [baler](https://github.com/magento/baler/) का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[पसंद के मुताबिक फ़ॉन्ट बनाते समय](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html), `@font-display` तय करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "तीसरे पक्ष के कई तरह के एक्सटेंशन [Magento मार्केटप्लेस](https://marketplace.magento.com/catalogsearch/result/?q=webp) में खोजें, ताकि आप नए इमेज फ़ॉर्मैट का फ़ायदा पा सकें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "वेब प्लैटफ़ॉर्म की [धीमे लोड होने](https://web.dev/native-lazy-loading) की सुविधा का इस्तेमाल करने के लिए, अपने प्रॉडक्ट और कैटलॉग टेम्प्लेट में बदलाव करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> का [Varnish इंटीग्रेशन](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "अपने स्टोर की डेवलपर सेटिंग में, \"सीएसएस फ़ाइलें छोटी करें\" विकल्प चालू करें. [ज़्यादा जानें](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "स्टैटिक कॉन्टेंट डिप्लॉयमेंट से मिली सभी JavaScript एसेट को छोटा करने के लिए, [Terser](https://www.npmjs.com/package/terser) का इस्तेमाल करें. साथ ही, छोटा करने की पहले से मौजूद सुविधा को बंद करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Magento की डिफ़ॉल्ट [JavaScript बंडलिंग](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) बंद करें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "तीसरे पक्ष के कई तरह के एक्सटेंशन [Magento मार्केटप्लेस](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) में खोजें, ताकि आप इमेज ऑप्टिमाइज़ कर सकें."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "[किसी थीम के लेआउट में बदलाव करके](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) Preconnect या dns-prefetch संसाधन संकेत जोड़े जा सकते हैं."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>` टैग, [किसी थीम के लेआउट में बदलाव करके](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) जोड़े जा सकते हैं."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "इमेज फ़ॉर्मैट को अपने-आप ऑप्टिमाइज़ होने के लिए, `<img>` के बजाय `next/image` कॉम्पोनेंट का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "इमेज को अपने-आप लेज़ी-लोड होने के लिए, `<img>` के बजाय `next/image` कॉम्पोनेंट का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "`next/image` कॉम्पोनेंट का इस्तेमाल करें और एलसीपी इमेज पहले से लोड करने के लिए, \"प्राथमिकता\" को सही पर सेट करें. [ज़्यादा जानें](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "तीसरे पक्ष के गै़र-ज़रूरी स्क्रिप्ट को उसी समय लोड होने से रोकने के लिए, `next/script` कॉम्पोनेंट का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "यह पक्का करने के लिए कि इमेज का साइज़ हमेशा सही हो, `next/image` कॉम्पोनेंट का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "जिन नियमों का इस्तेमाल न किया गया हो उन्हें स्टाइलशीट से हटाने के लिए, `Next.js` कॉन्फ़िगरेशन में `PurgeCSS` को सेट अप करें. [ज़्यादा जानें](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "ऐसे JavaScript कोड का पता लगाने के लिए, `Webpack Bundle Analyzer` का इस्तेमाल करें जिसका इस्तेमाल न किया गया हो. [ज़्यादा जानें](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "आपका ऐप्लिकेशन असल में कैसा परफ़ॉर्म कर रहा है, यह मापने के लिए `Next.js Analytics` का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "ऐसी एसेट और `Server-side Rendered` एसएसआर पेजों को कैश मेमोरी में सेव करने की सुविधा चालू करें जिनमें बदलाव न होता हो. [ज़्यादा जानें](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "इमेज क्वालिटी को अडजस्ट करने के लिए, `<img>` के बजाय `next/image` कॉम्पोनेंट का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "सही `sizes` को सेट करने के लिए, `next/image` कॉम्पोनेंट का इस्तेमाल करें. [ज़्यादा जानें](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "अपने Next.js सर्वर पर कंप्रेस करने की सुविधा चालू करें. [ज़्यादा जानें](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "`nuxt/image` कॉम्पोनेंट का इस्तेमाल करें और `format=\"webp\"` सेट करें. [ज़्यादा जानें](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "ऑफ़स्क्रीन इमेज के लिए, `nuxt/image` कॉम्पोनेंट का इस्तेमाल करें और `loading=\"lazy\"` सेट करें. [ज़्यादा जानें](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "एलसीपी इमेज के लिए, `nuxt/image` कॉम्पोनेंट का इस्तेमाल करें और `preload` सेट करें. [ज़्यादा जानें](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "`nuxt/image` कॉम्पोनेंट का इस्तेमाल करें. साथ ही, `width` और `height` को साफ़ तौर पर सेट करें. [ज़्यादा जानें](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "`nuxt/image` कॉम्पोनेंट का इस्तेमाल करें और सही `quality` सेट करें. [ज़्यादा जानें](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "`nuxt/image` कॉम्पोनेंट का इस्तेमाल करें और सही `sizes` सेट करें. [ज़्यादा जानें](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "वेब पेजों को तेज़ी से लोड करने के लिए, [ऐनिमेट किए गए GIF की जगह वीडियो का इस्तेमाल करें](https://web.dev/replace-gifs-with-videos/). साथ ही, [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) या [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) जैसे मॉडर्न फ़ाइल फ़ॉर्मैट इस्तेमाल करें, ताकि कंप्रेस करने पर क्वालिटी, मौजूदा state-of-the-art वीडियो कोडेक VP9 के मुकाबले 30% से ज़्यादा हो."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "ऐसे [प्लग इन](https://octobercms.com/plugins?search=image) या सेवा का इस्तेमाल करें जो अपलोड की गई इमेज को अपने-आप सबसे सही फ़ॉर्मैट में बदल दे. [WebP लॉसलेस इमेज](https://developers.google.com/speed/webp), PNG इमेज से साइज़ में 26% छोटी होती हैं. साथ ही, इक्विवैलेंट SSIM क्वालिटी इंडेक्स में JPEG इमेज से 25-34% छोटी होती हैं. आप अगली जनरेशन के [AVIF](https://jakearchibald.com/2020/avif-has-landed/) इमेज फ़ॉर्मैट का भी इस्तेमाल कर सकते हैं."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "[इमेज को धीमी रफ़्तार से लोड करने वाला ऐसा प्लग इन](https://octobercms.com/plugins?search=lazy) इंस्टॉल करें जो किसी भी ऑफ़स्क्रीन इमेज को अलग करने की सुविधा देता हो या किसी ऐसी थीम का इस्तेमाल करें जो यह सुविधा मुहैया कराती हो. साथ ही, [एएमपी प्लग इन](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "ऐसे कई प्लग इन हैं जो [क्रिटिकल एसेट को इनलाइन](https://octobercms.com/plugins?search=css) करने में आपकी मदद कर सकते हैं. ये प्लग इन अन्य प्लग इन के काम में रुकावट डाल सकते हैं, इसलिए आपको इनकी अच्छी तरह जांच करनी चाहिए."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "थीम, प्लग इन, और सर्वर की विशेषताओं से तय होता है कि सर्वर से कितनी देर में रिस्पॉन्स मिलेगा. ज़्यादा ऑप्टिमाइज़ की गई थीम ढूंढें, किसी ऑप्टिमाइज़ेशन प्लग इन को सावधानी से चुनें, और/या अपना सर्वर अपग्रेड करें. October CMS, डेवलपर को [`Queues`](https://octobercms.com/docs/services/queues) का भी इस्तेमाल करने देता है, ताकि वे किसी ऐसे टास्क की प्रोसेस को अलग कर सकें जिसमें ज़्यादा समय लगता हो. जैसे, ईमेल भेजना. इससे, वेब अनुरोधों की रफ़्तार बहुत ज़्यादा बढ़ जाती है."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "पोस्ट की सूचियों में खास हिस्से दिखाएं (जैसे, `show more` बटन की मदद से), किसी वेब पेज पर दिखाई गई पोस्ट की संख्या कम करें, लंबी पोस्ट को कई वेब पेजों में बांटें या टिप्पणियों को धीमी रफ़्तार से लोड करने वाले प्लग इन का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "ऐसे कई [प्लग इन](https://octobercms.com/plugins?search=css) हैं जो किसी वेबसाइट के लोड होने की रफ़्तार बढ़ा सकते हैं. इसके लिए, आपको स्टाइल को जोड़ना, उनमें काट-छांट करना, और उन्हें कंप्रेस करना होगा. पहले से काट-छांट करने के लिए बिल्ड प्रोसेस का इस्तेमाल करने से डेवलपमेंट की रफ़्तार बढ़ सकती है."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "ऐसे कई [प्लग इन](https://octobercms.com/plugins?search=javascript) हैं जो किसी वेबसाइट के लोड होने की रफ़्तार बढ़ा सकते हैं. इसके लिए, आपको स्क्रिप्ट को जोड़ना, उनमें काट-छांट करना, और उन्हें कंप्रेस करना होगा. पहले से काट-छांट करने के लिए बिल्ड प्रोसेस का इस्तेमाल करने से डेवलपमेंट की रफ़्तार बढ़ सकती है."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "वेबसाइट पर, इस्तेमाल न किए जाने वाले सीएसएस लोड करने वाले [प्लग इन](https://octobercms.com/plugins) की समीक्षा करें. ग़ैर-ज़रूरी सीएसएस जोड़ने वाले प्लग इन पहचानने के लिए, Chrome डेवलपर टूल में [कोड कवरेज](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) चलाएं. इसके बाद, स्टाइलशीट के यूआरएल से उस थीम या प्लग इन को पहचानें. ऐसी कई स्टाइलशीट वाले प्लग इन ढूंढें जिनके कोड कवरेज में बहुत सारे लाल निशान हों. किसी प्लग इन को कोई स्टाइलशीट सिर्फ़ तब जोड़नी चाहिए, जब वेब पेज पर उस स्टाइलशीट को वाकई में इस्तेमाल किया जाता हो."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "वेब पेज में, इस्तेमाल न की जाने वाली JavaScript फ़ाइलें लोड करने वाले [प्लग इन](https://octobercms.com/plugins?search=javascript) की समीक्षा करें. ग़ैर-ज़रूरी JavaScript फ़ाइलें जोड़ने वाले प्लग इन पहचानने के लिए, Chrome डेवलपर टूल में [कोड कवरेज](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) चलाएं. इसके बाद, स्क्रिप्ट के यूआरएल से उस थीम या प्लग इन को पहचानें. ऐसी कई स्क्रिप्ट वाले प्लग इन ढूंढें जिनके कोड कवरेज में बहुत सारे लाल निशान हों. किसी प्लग इन को कोई स्क्रिप्ट सिर्फ़ तब जोड़नी चाहिए, जब वेब पेज पर उस स्क्रिप्ट को वाकई में इस्तेमाल किया जाता हो."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[एचटीटीपी कैश मेमोरी की मदद से ग़ैर-ज़रूरी नेटवर्क अनुरोधों से बचने](https://web.dev/http-cache/#caching-checklist) के बारे में पढ़ें. ऐसे कई [प्लग इन](https://octobercms.com/plugins?search=Caching) हैं जो कैश मेमोरी में डेटा सेव करने की रफ़्तार बढ़ाने के लिए इस्तेमाल किए जा सकते हैं."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "इमेज की क्वालिटी को बरकरार रखते हुए, उसे कंप्रेस करने के लिए, [इमेज ऑप्टिमाइज़ेशन प्लग इन](https://octobercms.com/plugins?search=image) का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "सीधे मीडिया मैनेजर में इमेज अपलोड करें, ताकि आप यह पक्का कर सकें कि इमेज के ज़रूरी साइज़ उपलब्ध हैं. [साइज़ बदलने वाला फ़िल्टर](https://octobercms.com/docs/markup/filter-resize) या [इमेज का साइज़ बदलने वाला प्लग इन](https://octobercms.com/plugins?search=image) इस्तेमाल करें, ताकि आप यह पक्का कर सकें कि सबसे सही साइज़ की इमेज इस्तेमाल की जाएं."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "वेब सर्वर कॉन्फ़िगरेशन में टेक्स्ट कंप्रेस करने की सुविधा चालू करें."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "अगर आप पेज पर कई एलिमेंट एक से ज़्यादा बार रेंडर कर रहे हैं, तो बनाए गए डीओएम नोड की संख्या कम करने के लिए, `react-window` जैसी \"छोटी विंडो वाली\" लाइब्रेरी का इस्तेमाल करें. [ज़्यादा जानें](https://web.dev/virtualize-long-lists-react-window/). साथ ही, ज़रूरी न होने पर बार-बार रेंडर करने से बचने के लिए, [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) या [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) और [इफ़ेक्ट छोड़ें](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) का इस्तेमाल करें. ऐसा तब तक करें, जब तक कुछ और करने की ज़रूरत न हो. साथ ही, जब आप रनटाइम परफ़ॉर्मेंस को बेहतर बनाने के लिए `Effect` हुक का इस्तेमाल कर रहे हों."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "अगर आप React Router इस्तेमाल कर रहे हैं, तो [रूट नेविगेशन](https://reacttraining.com/react-router/web/api/Redirect) के लिए `<Redirect>` कॉम्पोनेंट का इस्तेमाल कम करें."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "अगर आप सर्वर-साइड पर किसी React कॉम्पोनेंट को रेंडर कर रहे हैं, तो `renderToPipeableStream()` या `renderToStaticNodeStream()` का इस्तेमाल करें. इससे क्लाइंट को एक साथ पूरे मार्कअप के बजाय, उसके अलग-अलग हिस्से मिलेंगे और वह उन्हें अलग-अलग हाइड्रेट कर पाएगा. [ज़्यादा जानें](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "अगर बिल्ड सिस्टम आपकी सीएसएस फ़ाइलों को अपने-आप छोटा कर देता है, तो पक्का करें कि आप अपने ऐप्लिकेशन के प्रोडक्शन बिल्ड को डिप्लॉय कर रहे हैं. आप React डेवलपर टूल एक्सटेंशन की मदद से इसकी पुष्टि कर सकते हैं. [ज़्यादा जानें](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "अगर बिल्ड सिस्टम आपकी जेएस फ़ाइलों को अपने-आप छोटा कर देता है, तो पक्का करें कि आप अपने ऐप्लिकेशन के प्रोडक्शन बिल्ड को डिप्लॉय कर रहे हैं. आप React डेवलपर टूल एक्सटेंशन की मदद से इसकी पुष्टि कर सकते हैं. [ज़्यादा जानें](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "अगर आप सर्वर-साइड पर रेंडर नहीं कर रहे हैं, तो `React.lazy()` के साथ [अपने JavaScript बंडलों को अलग-अलग करें](https://web.dev/code-splitting-suspense/). इसके अलावा, आप तीसरे पक्ष की लाइब्रेरी का इस्तेमाल करके कोड को अलग-अलग कर सकते हैं, जैसे कि [वे कॉम्पोनेंट जो लोड हो सकते हैं](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "React DevTools प्रोफ़ाइलर इस्तेमाल करें. यह आपके कॉम्पोनेंट की रेंडरिंग परफ़ॉर्मेंस मापने के लिए प्रोफ़ाइलर एपीआई का इस्तेमाल करता है. [ज़्यादा जानें](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "किसी ऐसी सेवा में अपनी GIF अपलोड करें जो उसे HTML5 वीडियो में जोड़ने के लिए तैयार रख सके."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "[परफ़ॉर्मेंस लैब](https://wordpress.org/plugins/performance-lab/) प्लगिन का इस्तेमाल करें. इससे आपकी अपलोड की गई JPEG इमेज का फ़ॉर्मैट अपने-आप WebP में बदल जाएगा. हालांकि, ऐसा सिर्फ़ वहीं होगा जहां यह सुविधा काम करती हो."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "ऐसा [धीमे लोड होने वाले WordPress प्लग इन](https://wordpress.org/plugins/search/lazy+load/) इंस्टॉल करें जिसमें किसी भी ऑफ़स्क्रीन इमेज को अलग करने की सुविधा हो. अगर नहीं, तो किसी ऐसी थीम पर जाएं जो यह सुविधा मुहैया कराती हो. साथ ही, [एएमपी प्लग इन](https://wordpress.org/plugins/amp/) का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "ऐसे कई WordPress प्लग इन हैं जो [क्रिटिकल एसेट इनलाइन करने ](https://wordpress.org/plugins/search/critical+css/) या [कम ज़रूरी संसाधनों को डेफ़र करने](https://wordpress.org/plugins/search/defer+css+javascript/) में आपकी मदद कर सकते हैं. ध्यान रखें कि ऐसी प्लग इन से मिलने वाले ऑप्टिमाइज़ेशन आपकी थीम या प्लग इन की सुविधाएं बिगाड़ सकते हैं. इसलिए, आपको कोड में बदलावों को करने की ज़रुरत हो सकती है."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "थीम, प्लगइन, और सर्वर की खास बातें सर्वर से जवाब मिलने के समय में योगदान करती हैं. ज़्यादा ऑप्टिमाइज़ की हुई थीम ढूंढने, एक ऑप्टिमाइज़ेशन प्लगइन को सावधानी से चुनने, और/या अपना सर्वर अपग्रेड करने पर विचार करें."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "अपनी पोस्ट सूचियों में खास हिस्से दिखाने पर विचार करें (जैसे 'ज़्यादा' टैग से), किसी पेज पर दिखाई गई पोस्ट की संख्या घटाने, अपनी लंबी पोस्ट को कई पेज में बाँटने या फिर टिप्पणियों को धीरे-धीरे लोड करने वाले प्लगइन का इस्तेमाल करने पर विचार करें."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "कई [WordPress प्लग इन](https://wordpress.org/plugins/search/minify+css/) आपकी साइट की गति को बढ़ा सकते हैं. ऐसा करने के लिए वे आपके स्टाइल को जोड़ते हैं, उन्हें छोटा करते हैं, और कंप्रेस करते हैं. ऐसा हो सकता है कि आप काट-छांट करने के लिए, एक बिल्ड प्रोसेस का इस्तेमाल भी करना चाहें, अगर ऐसा करना मुमकिन हो."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "कई [WordPress प्लग इन](https://wordpress.org/plugins/search/minify+javascript/) आपकी साइट की गति को बढ़ा सकते हैं. ऐसा करने के लिए वह आपकी स्क्रिप्ट को जोड़ते हैं, उन्हें छोटा करते हैं, और कंप्रेस करते हैं. ऐसा हो सकता है कि आप काट-छांट करने की इस प्रक्रिया के लिए, एक बिल्ड प्रोसेस का इस्तेमाल भी करना चाहें, अगर ऐसा करना मुमकिन हो."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "उन [WordPress प्लग इन](https://wordpress.org/plugins/) की संख्या कम करके या स्विच करके देखें जो आपके पेज में ऐसी सीएसएस लोड कर रहे हैं जिसका कभी इस्तेमाल नहीं हुआ. Chrome DevTools में [कोड कवरेज](https://developer.chrome.com/docs/devtools/coverage/) चलाकर उन प्लग इन की पहचान करें जो आपके पेज में गैर-ज़रूरी सीएसएस जोड़ रहे हैं. आप स्क्रिप्ट के यूआरएल से पहचान सकते हैं कि ऐसा किस थीम/प्लग इन ने किया. ऐसे प्लग इन खोजें जिनके पास उस सूची की कई स्टाइल शीट हैं जिसमें कोड कवरेज में बहुत से लाल निशान हैं. प्लग इन को स्क्रिप्ट तभी क्यू में लगानी चाहिए, अगर पेज पर उसका वाकई इस्तेमाल किया गया हो."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "उन [WordPress प्लग इन](https://wordpress.org/plugins/) की संख्या कम करके या स्विच करके देखें जो आपके पेज में ऐसी JavaScript लोड कर रहे हैं जिसका कभी इस्तेमाल नहीं हुआ. Chrome DevTools में [कोड कवरेज](https://developer.chrome.com/docs/devtools/coverage/) चलाकर उन प्लग इन की पहचान करें जो आपके पेज में गैर-ज़रूरी JS जोड़ रहे हैं. आप स्क्रिप्ट के यूआरएल से पहचान सकते हैं कि ऐसा किस थीम/प्लग इन ने किया. ऐसे प्लग इन खोजें जिनके पास उस सूची में कई स्क्रिप्ट हैं जिसमें कोड कवरेज में बहुत से लाल निशान हैं. प्लग इन को स्क्रिप्ट तभी क्यू में लगानी चाहिए, अगर पेज पर उसका वाकई इस्तेमाल किया गया हो."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "[WordPress में ब्राउज़र कैशिंग](https://wordpress.org/support/article/optimization/#browser-caching) के बारे में पढ़ें."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "ऐसे [इमेज ऑप्टिमाइज़ेशन WordPress प्लग इन](https://wordpress.org/plugins/search/optimize+images/) का इस्तेमाल करें जो आपकी क्वालिटी बरकरार रखते हुए आपकी इमेज कंप्रेस करता है."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "[मीडिया लाइब्रेरी](https://wordpress.org/support/article/media-library-screen/) की मदद से इमेज सीधे अपलोड करें, ताकि आप यह पक्का कर सकें कि आपके पास इमेज के वे आकार मौजूद हैं जिनकी आपको ज़रूरत पड़ेगी. इसके बाद, उन्हें डालने के लिए मीडिया लाइब्रेरी या इमेज विजेट का इस्तेमाल करें, ताकि आप यह पक्का कर सकें कि सबसे बेहतर इमेज आकारों का इस्तेमाल किया गया है (इसमें जवाब देने वाले ब्रेकपॉइंट की इमेज भी शामिल हैं). `Full Size` इमेज का इस्तेमाल तब तक न करें जब तक डाइमेंशन उनके इस्तेमाल के हिसाब से ठीक न हों. [ज़्यादा जानें](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "आप अपने वेब सर्वर कॉन्फ़िगरेशन में टेक्स्ट कंप्रेस करने की सुविधा चालू कर सकते हैं."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "अपनी इमेज को WebP में बदलने के लिए, 'WP Rocket' में 'इमेज ऑप्टिमाइज़ेशन' टैब से 'Imagify' सुविधा को चालू करें."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "इस सुझाव को ठीक करने के लिए, WP Rocket में [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) सुविधा को चालू करें. इस सुविधा के ज़रिए, इमेज लोड होने में तब तक देरी होती है, जब तक वेबसाइट पर आने वाला व्यक्ति पेज पर नीचे स्क्रोल नहीं करता और असल में उसे इमेज देखने की ज़रूरत नहीं होती."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "इस सुझाव का इस्तेमाल करने के लिए, 'WP Rocket' में [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) और [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) सुविधा को चालू करें. ये सुविधाएं, सीएसएस और JavaScript फ़ाइलों को ऑप्टिमाइज़ करेंगी, ताकि वे आपके पेज को रेंडर होने से न रोकें."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "इस समस्या को हल करने के लिए, 'WP Rocket' में [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) सुविधा को चालू करें. फ़ाइल के साइज़ को छोटा और उसे तेज़ी से डाउनलोड करने के लिए, आपकी साइट की सीएसएस फ़ाइलों में मौजूद खाली जगहों और टिप्पणियों को हटा दिया जाएगा."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "इस समस्या को हल करने के लिए, 'WP Rocket' में [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) सुविधा को चालू करें. फ़ाइल के साइज़ को छोटा और उसे तेज़ी से डाउनलोड करने के लिए, JavaScript फ़ाइलों में मौजूद खाली जगहों और टिप्पणियों को हटा दिया जाएगा."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "इस समस्या को हल करने के लिए, 'WP Rocket' में [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) सुविधा को चालू करें. यह सुविधा, इस्तेमाल नहीं किए गए सभी सीएसएस और स्टाइलशीट को हटाकर पेज का साइज़ कम करती है. हालांकि, यह हर पेज के लिए सिर्फ़ इस्तेमाल किए गए सीएसएस को बनाए रखती है."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "इस समस्या को हल करने के लिए, 'WP Rocket' में [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) की सुविधा चालू करें. यह सुविधा स्क्रिप्ट को तब तक लोड नहीं होने देती, जब तक उपयोगकर्ता कोई इंटरैक्शन न करे. इस वजह से, आपके पेज की लोडिंग परफ़ॉर्मेंस बेहतर हो जाती है. अगर आपकी साइट पर iframe हैं, तो WP Rocket की [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) और [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) सुविधा का इस्तेमाल करें."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "'WP Rocket' में इमेज ऑप्टिमाइज़ेशन टैब से 'Imagify' सुविधा चालू करें और अपनी इमेज कंप्रेस करने के लिए Bulk Optimization चलाएं."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "\"dns-prefetch\" जोड़ने और बाहरी डोमेन के साथ कनेक्शन की रफ़्तार बढ़ाने के लिए, 'WP Rocket' में [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) सुविधा का इस्तेमाल करें. साथ ही, 'WP Rocket', [Google Fonts डोमेन](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) में \"preconnect\" की सुविधा को अपने-आप जोड़ देता है. इसके अलावा, यह [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) सुविधा के ज़रिए जोड़े गए सभी CNAME से भी \"preconnect\"  को जोड़ देता है."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "फ़ॉन्ट की इस समस्या को हल करने के लिए, 'WP Rocket' में [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) सुविधा को चालू करें. आपकी साइट के अहम फ़ॉन्ट पहले से लोड किए जाएंगे."}, "report/renderer/report-utils.js | calculatorLink": {"message": "कैलकुलेटर देखें."}, "report/renderer/report-utils.js | collapseView": {"message": "व्यू को छोटा करें"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "शुरुआती नेविगेशन"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "पाथ का ज़्यादा से ज़्यादा अहम प्रतीक्षा समय:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSON कॉपी करें"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "गहरे रंग वाली थीम को टॉगल करें"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "प्रिंट को बड़ा किया गया"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "प्रिंट के बारे में खास जानकारी"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Gist के रूप में सेव करें"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "एचटीएमएल के रूप में सेव करें"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "JSON के रूप में सेव करें"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "व्यूअर में खोलें"}, "report/renderer/report-utils.js | errorLabel": {"message": "गड़बड़ी!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "गड़बड़ी की रिपोर्ट करें: कोई ऑडिट जानकारी नहीं"}, "report/renderer/report-utils.js | expandView": {"message": "व्यू को बड़ा करें"}, "report/renderer/report-utils.js | footerIssue": {"message": "समस्या की रिपोर्ट करें"}, "report/renderer/report-utils.js | hide": {"message": "छिपाएं"}, "report/renderer/report-utils.js | labDataTitle": {"message": "किसी नई या दुबारा जाँची जाने वाली ऐप्लिकेशन के लिए तैयार किया गया डेटा"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "एम्युलेट किए गए मोबाइल नेटवर्क पर मौजूद पेज का [Lighthouse](https://developers.google.com/web/tools/lighthouse/) विश्लेषण. मान अनुमान के हिसाब से लिखे गए हैं और इनमें अंतर हो सकता है."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "मैन्युअल रूप से देखे जाने वाले और ज़्यादा आइटम"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "लागू नहीं"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "अवसर"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "अनुमानित बचत"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "पास हुए ऑडिट"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "शुरुआती पेज लोड"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "कस्टम थ्रॉटलिंग"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "एम्युलेट किया गया डेस्कटॉप"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "कोई भी एम्युलेशन नहीं"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe वर्शन"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "थ्रॉटल नहीं किया गया सीपीयू/मेमोरी पावर"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "सीपीयू थ्रॉटलिंग"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "डिवाइस"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "नेटवर्क थ्रॉटलिंग"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "स्क्रीन एम्युलेशन"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "उपयोगकर्ता एजेंट (नेटवर्क)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "एक पेज लोड"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "यह डेटा, कई सेशन के बारे में जानकारी देने वाले फ़ील्ड डेटा के बजाय, लोड हुए एक पेज से लिया गया है."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "धीमी 4G थ्रॉटलिंग"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "अज्ञात"}, "report/renderer/report-utils.js | show": {"message": "दिखाएं"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "वे ऑडिट दिखाएं जो इनके लिए काम के हों:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "स्निपेट को छोटा करें"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "स्निपेट को बड़ा करें"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "तीसरे पक्ष के संसाधन दिखाएं"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "रनटाइम एनवायरमेंट से मिला"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "कुछ समस्याएं आने के कारण Lighthouse के इस रन पर असर पड़ा है:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "मान अनुमानित हैं और इनमें बदलाव हो सकता है. सीधे तौर पर इन मेट्रिक से [परफ़ॉर्मेंस स्कोर तय किए ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) जाते हैं."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "मूल ट्रेस देखें"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "ट्रेस देखें"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Treemap देखें"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "चेतावनियों के साथ पास हुए ऑडिट"}, "report/renderer/report-utils.js | warningHeader": {"message": "चेतावनियां: "}, "treemap/app/src/util.js | allLabel": {"message": "सभी"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "सभी स्क्रिप्ट"}, "treemap/app/src/util.js | coverageColumnName": {"message": "कवरेज"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "डुप्लीकेट मॉड्यूल"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "फ़ाइल का साइज़ (बाइट में)"}, "treemap/app/src/util.js | tableColumnName": {"message": "नाम"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "टेबल को टॉगल करें"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "इस्तेमाल न की गई बाइट की संख्या"}}