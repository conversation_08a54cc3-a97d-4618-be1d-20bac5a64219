{"ast": null, "code": "export { Trans } from './Trans';\nexport { useTranslation } from './useTranslation';\nexport { withTranslation } from './withTranslation';\nexport { Translation } from './Translation';\nexport { I18nextProvider } from './I18nextProvider';\nexport { withSSR } from './withSSR';\nexport { useSSR } from './useSSR';\nexport { I18nContext, initReactI18next, setDefaults, getDefaults, setI18n, getI18n, composeInitialProps, getInitialProps } from './context';\nexport var date = function date() {\n  return '';\n};\nexport var time = function time() {\n  return '';\n};\nexport var number = function number() {\n  return '';\n};\nexport var select = function select() {\n  return '';\n};\nexport var plural = function plural() {\n  return '';\n};\nexport var selectOrdinal = function selectOrdinal() {\n  return '';\n};", "map": {"version": 3, "names": ["Trans", "useTranslation", "withTranslation", "Translation", "I18nextProvider", "withSSR", "useSSR", "I18nContext", "initReactI18next", "setDefaults", "getDefaults", "setI18n", "getI18n", "composeInitialProps", "getInitialProps", "date", "time", "number", "select", "plural", "selectOrdinal"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/index.js"], "sourcesContent": ["export { Trans } from './Trans';\nexport { useTranslation } from './useTranslation';\nexport { withTranslation } from './withTranslation';\nexport { Translation } from './Translation';\nexport { I18nextProvider } from './I18nextProvider';\nexport { withSSR } from './withSSR';\nexport { useSSR } from './useSSR';\nexport { I18nContext, initReactI18next, setDefaults, getDefaults, setI18n, getI18n, composeInitialProps, getInitialProps } from './context';\nexport var date = function date() {\n  return '';\n};\nexport var time = function time() {\n  return '';\n};\nexport var number = function number() {\n  return '';\n};\nexport var select = function select() {\n  return '';\n};\nexport var plural = function plural() {\n  return '';\n};\nexport var selectOrdinal = function selectOrdinal() {\n  return '';\n};"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,WAAW;AAC3I,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;EAChC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;EAChC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EACpC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EACpC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EACpC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;EAClD,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}