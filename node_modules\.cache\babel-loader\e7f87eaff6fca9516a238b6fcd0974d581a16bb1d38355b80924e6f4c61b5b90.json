{"ast": null, "code": "import Menu from './Menu';\nimport MenuItem from './MenuItem';\nimport SubMenu from './SubMenu';\nimport MenuItemGroup from './MenuItemGroup';\nimport { useFullPath as useOriginFullPath } from './context/PathContext';\nimport Divider from './Divider';\n/** @private Only used for antd internal. Do not use in your production. */\n\nvar useFullPath = useOriginFullPath;\nexport { SubMenu, MenuItem as Item, MenuItem, MenuItemGroup, MenuItemGroup as ItemGroup, Divider, useFullPath };\nvar ExportMenu = Menu;\nExportMenu.Item = MenuItem;\nExportMenu.SubMenu = SubMenu;\nExportMenu.ItemGroup = MenuItemGroup;\nExportMenu.Divider = Divider;\nexport default ExportMenu;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "MenuItem", "SubMenu", "MenuItemGroup", "useFullPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Divider", "<PERSON><PERSON>", "ItemGroup", "ExportMenu"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-menu/es/index.js"], "sourcesContent": ["import Menu from './Menu';\nimport MenuItem from './MenuItem';\nimport SubMenu from './SubMenu';\nimport MenuItemGroup from './MenuItemGroup';\nimport { useFullPath as useOriginFullPath } from './context/PathContext';\nimport Divider from './Divider';\n/** @private Only used for antd internal. Do not use in your production. */\n\nvar useFullPath = useOriginFullPath;\nexport { SubMenu, MenuItem as Item, MenuItem, MenuItemGroup, MenuItemGroup as ItemGroup, Divider, useFullPath };\nvar ExportMenu = Menu;\nExportMenu.Item = MenuItem;\nExportMenu.SubMenu = SubMenu;\nExportMenu.ItemGroup = MenuItemGroup;\nExportMenu.Divider = Divider;\nexport default ExportMenu;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,WAAW,IAAIC,iBAAiB,QAAQ,uBAAuB;AACxE,OAAOC,OAAO,MAAM,WAAW;AAC/B;;AAEA,IAAIF,WAAW,GAAGC,iBAAiB;AACnC,SAASH,OAAO,EAAED,QAAQ,IAAIM,IAAI,EAAEN,QAAQ,EAAEE,aAAa,EAAEA,aAAa,IAAIK,SAAS,EAAEF,OAAO,EAAEF,WAAW;AAC7G,IAAIK,UAAU,GAAGT,IAAI;AACrBS,UAAU,CAACF,IAAI,GAAGN,QAAQ;AAC1BQ,UAAU,CAACP,OAAO,GAAGA,OAAO;AAC5BO,UAAU,CAACD,SAAS,GAAGL,aAAa;AACpCM,UAAU,CAACH,OAAO,GAAGA,OAAO;AAC5B,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}