{"ast": null, "code": "/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GetAction - operazioni sulle azioni del carrello\n*\n*/\nimport { GET_NUMBERS_PRODUCT } from './types';\n//Metodo aggiunta prodotti nel carrello\nexport const getNumbers = () => {\n  return dispatch => {\n    dispatch({\n      type: GET_NUMBERS_PRODUCT\n    });\n    if (window.localStorage.Cart.length > 2) {\n      var cartCopy = 0;\n      var prodInCart = [];\n      var tot = 0;\n      prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n      prodInCart.forEach(element => {\n        if (element.quantità !== undefined) {\n          cartCopy += element.quantità;\n          tot += element.quantità * parseFloat(element.price !== undefined ? element.price : element.sell_in);\n        } else {\n          cartCopy += element.quantity;\n          tot += element.quantity * parseFloat(element.price !== undefined ? element.price : element.sell_in);\n        }\n        window.sessionStorage.setItem(\"totCart\", parseFloat(tot).toFixed(2) + ' €');\n        window.sessionStorage.setItem(\"Carrello\", cartCopy);\n      });\n    } else {\n      cartCopy = 0;\n      tot = '0,00';\n      window.sessionStorage.setItem(\"totCart\", parseFloat(tot).toFixed(2) + ' €');\n      window.sessionStorage.setItem(\"Carrello\", cartCopy);\n    }\n  };\n};", "map": {"version": 3, "names": ["GET_NUMBERS_PRODUCT", "getNumbers", "dispatch", "type", "window", "localStorage", "<PERSON><PERSON>", "length", "cartCopy", "prodInCart", "tot", "JSON", "parse", "getItem", "for<PERSON>ach", "element", "quantità", "undefined", "parseFloat", "price", "sell_in", "quantity", "sessionStorage", "setItem", "toFixed"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/carrello/actions/getAction.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GetAction - operazioni sulle azioni del carrello\n*\n*/\nimport { GET_NUMBERS_PRODUCT } from './types';\n//Metodo aggiunta prodotti nel carrello\nexport const getNumbers = () => {\n    return (dispatch) => {\n        dispatch({\n            type: GET_NUMBERS_PRODUCT\n        });\n        if (window.localStorage.Cart.length > 2 ) {\n            var cartCopy = 0\n            var prodInCart = []\n            var tot = 0\n            prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n            prodInCart.forEach(element => {\n                if (element.quantità !== undefined) {\n                    cartCopy += element.quantità;\n                    tot += element.quantità * parseFloat(element.price !== undefined ? element.price : element.sell_in)\n                } else {\n                    cartCopy += element.quantity;\n                    tot += element.quantity * parseFloat(element.price !== undefined ? element.price : element.sell_in)\n                }\n                window.sessionStorage.setItem(\"totCart\", parseFloat(tot).toFixed(2) + ' €')\n                window.sessionStorage.setItem(\"Carrello\", cartCopy)\n            })\n        } else {\n            cartCopy = 0\n            tot = '0,00'\n            window.sessionStorage.setItem(\"totCart\", parseFloat(tot).toFixed(2) + ' €')\n            window.sessionStorage.setItem(\"Carrello\", cartCopy)\n        }\n    }\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,SAAS;AAC7C;AACA,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAC5B,OAAQC,QAAQ,IAAK;IACjBA,QAAQ,CAAC;MACLC,IAAI,EAAEH;IACV,CAAC,CAAC;IACF,IAAII,MAAM,CAACC,YAAY,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAG;MACtC,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,GAAG,GAAG,CAAC;MACXD,UAAU,GAAGE,IAAI,CAACC,KAAK,CAACP,YAAY,CAACQ,OAAO,CAAC,MAAM,CAAC,CAAC;MACrDJ,UAAU,CAACK,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAIA,OAAO,CAACC,QAAQ,KAAKC,SAAS,EAAE;UAChCT,QAAQ,IAAIO,OAAO,CAACC,QAAQ;UAC5BN,GAAG,IAAIK,OAAO,CAACC,QAAQ,GAAGE,UAAU,CAACH,OAAO,CAACI,KAAK,KAAKF,SAAS,GAAGF,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACK,OAAO,CAAC;QACvG,CAAC,MAAM;UACHZ,QAAQ,IAAIO,OAAO,CAACM,QAAQ;UAC5BX,GAAG,IAAIK,OAAO,CAACM,QAAQ,GAAGH,UAAU,CAACH,OAAO,CAACI,KAAK,KAAKF,SAAS,GAAGF,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACK,OAAO,CAAC;QACvG;QACAhB,MAAM,CAACkB,cAAc,CAACC,OAAO,CAAC,SAAS,EAAEL,UAAU,CAACR,GAAG,CAAC,CAACc,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAC3EpB,MAAM,CAACkB,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEf,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN,CAAC,MAAM;MACHA,QAAQ,GAAG,CAAC;MACZE,GAAG,GAAG,MAAM;MACZN,MAAM,CAACkB,cAAc,CAACC,OAAO,CAAC,SAAS,EAAEL,UAAU,CAACR,GAAG,CAAC,CAACc,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MAC3EpB,MAAM,CAACkB,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEf,QAAQ,CAAC;IACvD;EACJ,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}