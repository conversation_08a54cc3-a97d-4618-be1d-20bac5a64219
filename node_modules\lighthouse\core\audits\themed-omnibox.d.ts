export default ThemedOmnibox;
/**
 * @fileoverview
 * Audits if a page is configured for a themed address bar
 *
 * Requirements:
 *   * manifest is not empty
 *   * manifest has a theme_color
 *   * HTML has a theme-color meta
 *
 * Color validity is explicitly not checked.
 */
declare class ThemedOmnibox extends MultiCheckAudit {
    /**
     * @param {LH.Artifacts.MetaElement|undefined} themeColorMeta
     * @param {Array<string>} failures
     */
    static assessMetaThemecolor(themeColorMeta: LH.Artifacts.MetaElement | undefined, failures: Array<string>): void;
    /**
     * @param {LH.Artifacts.ManifestValues} manifestValues
     * @param {Array<string>} failures
     */
    static assessManifest(manifestValues: LH.Artifacts.ManifestValues, failures: Array<string>): void;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<{failures: Array<string>, manifestValues: LH.Artifacts.ManifestValues, themeColor: ?string}>}
     */
    static audit_(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<{
        failures: Array<string>;
        manifestValues: LH.Artifacts.ManifestValues;
        themeColor: string | null;
    }>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
import MultiCheckAudit from "./multi-check-audit.js";
import { ManifestValues } from "../computed/manifest-values.js";
//# sourceMappingURL=themed-omnibox.d.ts.map