{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar cookies = require('./../helpers/cookies');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n    var responseType = config.responseType;\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n    function onloadend() {\n      if (!request) {\n        return;\n      }\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !responseType || responseType === 'text' || responseType === 'json' ? request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    }\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        }\n\n        // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        }\n        // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n        setTimeout(onloadend);\n      };\n    }\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(timeoutErrorMessage, config, config.transitional && config.transitional.clarifyTimeoutError ? 'ETIMEDOUT' : 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ? cookies.read(config.xsrfCookieName) : undefined;\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (responseType && responseType !== 'json') {\n      request.responseType = config.responseType;\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n    if (!requestData) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};", "map": {"version": 3, "names": ["utils", "require", "settle", "cookies", "buildURL", "buildFullPath", "parseHeaders", "isURLSameOrigin", "createError", "module", "exports", "xhrAdapter", "config", "Promise", "dispatchXhrRequest", "resolve", "reject", "requestData", "data", "requestHeaders", "headers", "responseType", "isFormData", "request", "XMLHttpRequest", "auth", "username", "password", "unescape", "encodeURIComponent", "Authorization", "btoa", "fullPath", "baseURL", "url", "open", "method", "toUpperCase", "params", "paramsSerializer", "timeout", "onloadend", "responseHeaders", "getAllResponseHeaders", "responseData", "responseText", "response", "status", "statusText", "onreadystatechange", "handleLoad", "readyState", "responseURL", "indexOf", "setTimeout", "<PERSON>ab<PERSON>", "handleAbort", "onerror", "handleError", "ontimeout", "handleTimeout", "timeoutErrorMessage", "transitional", "clarifyTimeoutError", "isStandardBrowserEnv", "xsrfValue", "withCredentials", "xsrfCookieName", "read", "undefined", "xsrfHeaderName", "for<PERSON>ach", "setRequestHeader", "val", "key", "toLowerCase", "isUndefined", "onDownloadProgress", "addEventListener", "onUploadProgress", "upload", "cancelToken", "promise", "then", "onCanceled", "cancel", "abort", "send"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/axios/lib/adapters/xhr.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar cookies = require('./../helpers/cookies');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\n\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n    var responseType = config.responseType;\n\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    function onloadend() {\n      if (!request) {\n        return;\n      }\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !responseType || responseType === 'text' ||  responseType === 'json' ?\n        request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    }\n\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        }\n\n        // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        }\n        // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n        setTimeout(onloadend);\n      };\n    }\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(\n        timeoutErrorMessage,\n        config,\n        config.transitional && config.transitional.clarifyTimeoutError ? 'ETIMEDOUT' : 'ECONNABORTED',\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ?\n        cookies.read(config.xsrfCookieName) :\n        undefined;\n\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (responseType && responseType !== 'json') {\n      request.responseType = config.responseType;\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n\n    if (!requestData) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,MAAM,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACxC,IAAIE,OAAO,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIG,QAAQ,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAC/C,IAAII,aAAa,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AACpD,IAAIK,YAAY,GAAGL,OAAO,CAAC,2BAA2B,CAAC;AACvD,IAAIM,eAAe,GAAGN,OAAO,CAAC,8BAA8B,CAAC;AAC7D,IAAIO,WAAW,GAAGP,OAAO,CAAC,qBAAqB,CAAC;AAEhDQ,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC3C,OAAO,IAAIC,OAAO,CAAC,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,MAAM,EAAE;IAC9D,IAAIC,WAAW,GAAGL,MAAM,CAACM,IAAI;IAC7B,IAAIC,cAAc,GAAGP,MAAM,CAACQ,OAAO;IACnC,IAAIC,YAAY,GAAGT,MAAM,CAACS,YAAY;IAEtC,IAAIrB,KAAK,CAACsB,UAAU,CAACL,WAAW,CAAC,EAAE;MACjC,OAAOE,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC;IACzC;IAEA,IAAII,OAAO,GAAG,IAAIC,cAAc,CAAC,CAAC;;IAElC;IACA,IAAIZ,MAAM,CAACa,IAAI,EAAE;MACf,IAAIC,QAAQ,GAAGd,MAAM,CAACa,IAAI,CAACC,QAAQ,IAAI,EAAE;MACzC,IAAIC,QAAQ,GAAGf,MAAM,CAACa,IAAI,CAACE,QAAQ,GAAGC,QAAQ,CAACC,kBAAkB,CAACjB,MAAM,CAACa,IAAI,CAACE,QAAQ,CAAC,CAAC,GAAG,EAAE;MAC7FR,cAAc,CAACW,aAAa,GAAG,QAAQ,GAAGC,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAGC,QAAQ,CAAC;IAC3E;IAEA,IAAIK,QAAQ,GAAG3B,aAAa,CAACO,MAAM,CAACqB,OAAO,EAAErB,MAAM,CAACsB,GAAG,CAAC;IACxDX,OAAO,CAACY,IAAI,CAACvB,MAAM,CAACwB,MAAM,CAACC,WAAW,CAAC,CAAC,EAAEjC,QAAQ,CAAC4B,QAAQ,EAAEpB,MAAM,CAAC0B,MAAM,EAAE1B,MAAM,CAAC2B,gBAAgB,CAAC,EAAE,IAAI,CAAC;;IAE3G;IACAhB,OAAO,CAACiB,OAAO,GAAG5B,MAAM,CAAC4B,OAAO;IAEhC,SAASC,SAASA,CAAA,EAAG;MACnB,IAAI,CAAClB,OAAO,EAAE;QACZ;MACF;MACA;MACA,IAAImB,eAAe,GAAG,uBAAuB,IAAInB,OAAO,GAAGjB,YAAY,CAACiB,OAAO,CAACoB,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;MAC/G,IAAIC,YAAY,GAAG,CAACvB,YAAY,IAAIA,YAAY,KAAK,MAAM,IAAKA,YAAY,KAAK,MAAM,GACrFE,OAAO,CAACsB,YAAY,GAAGtB,OAAO,CAACuB,QAAQ;MACzC,IAAIA,QAAQ,GAAG;QACb5B,IAAI,EAAE0B,YAAY;QAClBG,MAAM,EAAExB,OAAO,CAACwB,MAAM;QACtBC,UAAU,EAAEzB,OAAO,CAACyB,UAAU;QAC9B5B,OAAO,EAAEsB,eAAe;QACxB9B,MAAM,EAAEA,MAAM;QACdW,OAAO,EAAEA;MACX,CAAC;MAEDrB,MAAM,CAACa,OAAO,EAAEC,MAAM,EAAE8B,QAAQ,CAAC;;MAEjC;MACAvB,OAAO,GAAG,IAAI;IAChB;IAEA,IAAI,WAAW,IAAIA,OAAO,EAAE;MAC1B;MACAA,OAAO,CAACkB,SAAS,GAAGA,SAAS;IAC/B,CAAC,MAAM;MACL;MACAlB,OAAO,CAAC0B,kBAAkB,GAAG,SAASC,UAAUA,CAAA,EAAG;QACjD,IAAI,CAAC3B,OAAO,IAAIA,OAAO,CAAC4B,UAAU,KAAK,CAAC,EAAE;UACxC;QACF;;QAEA;QACA;QACA;QACA;QACA,IAAI5B,OAAO,CAACwB,MAAM,KAAK,CAAC,IAAI,EAAExB,OAAO,CAAC6B,WAAW,IAAI7B,OAAO,CAAC6B,WAAW,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;UAChG;QACF;QACA;QACA;QACAC,UAAU,CAACb,SAAS,CAAC;MACvB,CAAC;IACH;;IAEA;IACAlB,OAAO,CAACgC,OAAO,GAAG,SAASC,WAAWA,CAAA,EAAG;MACvC,IAAI,CAACjC,OAAO,EAAE;QACZ;MACF;MAEAP,MAAM,CAACR,WAAW,CAAC,iBAAiB,EAAEI,MAAM,EAAE,cAAc,EAAEW,OAAO,CAAC,CAAC;;MAEvE;MACAA,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACAA,OAAO,CAACkC,OAAO,GAAG,SAASC,WAAWA,CAAA,EAAG;MACvC;MACA;MACA1C,MAAM,CAACR,WAAW,CAAC,eAAe,EAAEI,MAAM,EAAE,IAAI,EAAEW,OAAO,CAAC,CAAC;;MAE3D;MACAA,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACAA,OAAO,CAACoC,SAAS,GAAG,SAASC,aAAaA,CAAA,EAAG;MAC3C,IAAIC,mBAAmB,GAAG,aAAa,GAAGjD,MAAM,CAAC4B,OAAO,GAAG,aAAa;MACxE,IAAI5B,MAAM,CAACiD,mBAAmB,EAAE;QAC9BA,mBAAmB,GAAGjD,MAAM,CAACiD,mBAAmB;MAClD;MACA7C,MAAM,CAACR,WAAW,CAChBqD,mBAAmB,EACnBjD,MAAM,EACNA,MAAM,CAACkD,YAAY,IAAIlD,MAAM,CAACkD,YAAY,CAACC,mBAAmB,GAAG,WAAW,GAAG,cAAc,EAC7FxC,OAAO,CAAC,CAAC;;MAEX;MACAA,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACA;IACA;IACA,IAAIvB,KAAK,CAACgE,oBAAoB,CAAC,CAAC,EAAE;MAChC;MACA,IAAIC,SAAS,GAAG,CAACrD,MAAM,CAACsD,eAAe,IAAI3D,eAAe,CAACyB,QAAQ,CAAC,KAAKpB,MAAM,CAACuD,cAAc,GAC5FhE,OAAO,CAACiE,IAAI,CAACxD,MAAM,CAACuD,cAAc,CAAC,GACnCE,SAAS;MAEX,IAAIJ,SAAS,EAAE;QACb9C,cAAc,CAACP,MAAM,CAAC0D,cAAc,CAAC,GAAGL,SAAS;MACnD;IACF;;IAEA;IACA,IAAI,kBAAkB,IAAI1C,OAAO,EAAE;MACjCvB,KAAK,CAACuE,OAAO,CAACpD,cAAc,EAAE,SAASqD,gBAAgBA,CAACC,GAAG,EAAEC,GAAG,EAAE;QAChE,IAAI,OAAOzD,WAAW,KAAK,WAAW,IAAIyD,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,cAAc,EAAE;UAC9E;UACA,OAAOxD,cAAc,CAACuD,GAAG,CAAC;QAC5B,CAAC,MAAM;UACL;UACAnD,OAAO,CAACiD,gBAAgB,CAACE,GAAG,EAAED,GAAG,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACzE,KAAK,CAAC4E,WAAW,CAAChE,MAAM,CAACsD,eAAe,CAAC,EAAE;MAC9C3C,OAAO,CAAC2C,eAAe,GAAG,CAAC,CAACtD,MAAM,CAACsD,eAAe;IACpD;;IAEA;IACA,IAAI7C,YAAY,IAAIA,YAAY,KAAK,MAAM,EAAE;MAC3CE,OAAO,CAACF,YAAY,GAAGT,MAAM,CAACS,YAAY;IAC5C;;IAEA;IACA,IAAI,OAAOT,MAAM,CAACiE,kBAAkB,KAAK,UAAU,EAAE;MACnDtD,OAAO,CAACuD,gBAAgB,CAAC,UAAU,EAAElE,MAAM,CAACiE,kBAAkB,CAAC;IACjE;;IAEA;IACA,IAAI,OAAOjE,MAAM,CAACmE,gBAAgB,KAAK,UAAU,IAAIxD,OAAO,CAACyD,MAAM,EAAE;MACnEzD,OAAO,CAACyD,MAAM,CAACF,gBAAgB,CAAC,UAAU,EAAElE,MAAM,CAACmE,gBAAgB,CAAC;IACtE;IAEA,IAAInE,MAAM,CAACqE,WAAW,EAAE;MACtB;MACArE,MAAM,CAACqE,WAAW,CAACC,OAAO,CAACC,IAAI,CAAC,SAASC,UAAUA,CAACC,MAAM,EAAE;QAC1D,IAAI,CAAC9D,OAAO,EAAE;UACZ;QACF;QAEAA,OAAO,CAAC+D,KAAK,CAAC,CAAC;QACftE,MAAM,CAACqE,MAAM,CAAC;QACd;QACA9D,OAAO,GAAG,IAAI;MAChB,CAAC,CAAC;IACJ;IAEA,IAAI,CAACN,WAAW,EAAE;MAChBA,WAAW,GAAG,IAAI;IACpB;;IAEA;IACAM,OAAO,CAACgE,IAAI,CAACtE,WAAW,CAAC;EAC3B,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}