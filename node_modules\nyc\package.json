{"name": "nyc", "version": "15.1.0", "description": "the Istanbul command line interface", "main": "index.js", "scripts": {"lint": "standard", "pretest": "npm run lint && npm run clean && npm run instrument", "test": "tap", "snap": "npm test -- --snapshot", "posttest": "npm run report", "clean": "node ./npm-run-clean.js", "instrument": "node ./build-self-coverage.js", "report": "node ./bin/nyc report --temp-dir ./.self_coverage/ -r text -r lcov", "release": "standard-version"}, "bin": {"nyc": "./bin/nyc.js"}, "files": ["index.js", "bin/*.js", "lib/**/*.js"], "standard": {"ignore": ["/tap-snapshots/", "**/fixtures/**", "**/test/build/*"]}, "keywords": ["coverage", "reporter", "subprocess", "testing"], "contributors": [{"name": "<PERSON>", "website": "https://github.com/isaacs"}, {"name": "<PERSON>", "website": "https://novemberborn.net"}, {"name": "<PERSON>", "website": "https://twitter.com/jamestalmage"}, {"name": "<PERSON><PERSON>", "website": "https://github.com/gotwarlost"}], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "caching-transform": "^4.0.0", "convert-source-map": "^1.7.0", "decamelize": "^1.2.0", "find-cache-dir": "^3.2.0", "find-up": "^4.1.0", "foreground-child": "^2.0.0", "get-package-type": "^0.1.0", "glob": "^7.1.6", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-hook": "^3.0.0", "istanbul-lib-instrument": "^4.0.0", "istanbul-lib-processinfo": "^2.0.2", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "make-dir": "^3.0.0", "node-preload": "^0.2.1", "p-map": "^3.0.0", "process-on-spawn": "^1.0.0", "resolve-from": "^5.0.0", "rimraf": "^3.0.0", "signal-exit": "^3.0.2", "spawn-wrap": "^2.0.0", "test-exclude": "^6.0.0", "yargs": "^15.0.2"}, "devDependencies": {"any-path": "^1.3.0", "coveralls": "^3.0.9", "is-windows": "^1.0.2", "requirejs": "^2.3.6", "source-map-support": "^0.5.16", "standard": "^14.3.1", "standard-version": "^8.0.0", "tap": "^14.10.5", "which": "^2.0.2"}, "engines": {"node": ">=8.9"}, "homepage": "https://istanbul.js.org/", "bugs": "https://github.com/istanbuljs/nyc/issues", "repository": {"type": "git", "url": "**************:istanbuljs/nyc.git"}}