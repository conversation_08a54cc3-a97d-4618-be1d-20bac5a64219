{"ast": null, "code": "// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = function (argument) {\n  return typeof argument == 'function';\n};", "map": {"version": 3, "names": ["module", "exports", "argument"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/is-callable.js"], "sourcesContent": ["// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = function (argument) {\n  return typeof argument == 'function';\n};\n"], "mappings": "AAAA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,OAAO,OAAOA,QAAQ,IAAI,UAAU;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}