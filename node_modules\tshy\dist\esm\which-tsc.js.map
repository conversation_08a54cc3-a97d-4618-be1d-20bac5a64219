{"version": 3, "file": "which-tsc.js", "sourceRoot": "", "sources": ["../../src/which-tsc.ts"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,qEAAqE;AACrE,qEAAqE;AACrE,gEAAgE;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAA;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,KAAK,CAAA;AAEnC,eAAe,aAAa,CAC1B,IAAI,GAAG,CACL,YAAY,EACZ,MAAM,aAAa,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CACnD,CACF,CAAA", "sourcesContent": ["// find the location of the tsc binary\n// This is necessary because pnpm install trees don't expose binaries\n// of meta-deps, and it's nicer to not require that the tshy user has\n// a dep on typescript directly if they don't need it otherwise.\nimport { resolveImport } from 'resolve-import'\nimport { fileURLToPath } from 'url'\n\nexport default fileURLToPath(\n  new URL(\n    '../bin/tsc',\n    await resolveImport('typescript', import.meta.url),\n  ),\n)\n"]}