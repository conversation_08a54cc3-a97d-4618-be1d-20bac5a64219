{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"defaultOpen\", \"getContainer\", \"wrapperClassName\", \"forceRender\", \"handler\"],\n  _excluded2 = [\"visible\", \"afterClose\"];\nimport Portal from \"rc-util/es/PortalWrapper\";\nimport * as React from 'react';\nimport Child from './DrawerChild';\nvar DrawerWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(DrawerWrapper, _React$Component);\n  var _super = _createSuper(DrawerWrapper);\n  function DrawerWrapper(props) {\n    var _this;\n    _classCallCheck(this, DrawerWrapper);\n    _this = _super.call(this, props);\n    _this.dom = void 0;\n    _this.onHandleClick = function (e) {\n      var _this$props = _this.props,\n        onHandleClick = _this$props.onHandleClick,\n        $open = _this$props.open;\n      if (onHandleClick) {\n        onHandleClick(e);\n      }\n      if (typeof $open === 'undefined') {\n        var open = _this.state.open;\n        _this.setState({\n          open: !open\n        });\n      }\n    };\n    _this.onClose = function (e) {\n      var _this$props2 = _this.props,\n        onClose = _this$props2.onClose,\n        open = _this$props2.open;\n      if (onClose) {\n        onClose(e);\n      }\n      if (typeof open === 'undefined') {\n        _this.setState({\n          open: false\n        });\n      }\n    };\n    var _open = typeof props.open !== 'undefined' ? props.open : !!props.defaultOpen;\n    _this.state = {\n      open: _open\n    };\n    if ('onMaskClick' in props) {\n      console.warn('`onMaskClick` are removed, please use `onClose` instead.');\n    }\n    return _this;\n  }\n  _createClass(DrawerWrapper, [{\n    key: \"render\",\n    value:\n    // tslint:disable-next-line:member-ordering\n    function render() {\n      var _this2 = this;\n      var _this$props3 = this.props,\n        defaultOpen = _this$props3.defaultOpen,\n        getContainer = _this$props3.getContainer,\n        wrapperClassName = _this$props3.wrapperClassName,\n        forceRender = _this$props3.forceRender,\n        handler = _this$props3.handler,\n        props = _objectWithoutProperties(_this$props3, _excluded);\n      var open = this.state.open; // 渲染在当前 dom 里；\n\n      if (!getContainer) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: wrapperClassName,\n          ref: function ref(c) {\n            _this2.dom = c;\n          }\n        }, /*#__PURE__*/React.createElement(Child, _extends({}, props, {\n          open: open,\n          handler: handler,\n          getContainer: function getContainer() {\n            return _this2.dom;\n          },\n          onClose: this.onClose,\n          onHandleClick: this.onHandleClick\n        })));\n      } // 如果有 handler 为内置强制渲染；\n\n      var $forceRender = !!handler || forceRender;\n      return /*#__PURE__*/React.createElement(Portal, {\n        visible: open,\n        forceRender: $forceRender,\n        getContainer: getContainer,\n        wrapperClassName: wrapperClassName\n      }, function (_ref) {\n        var visible = _ref.visible,\n          afterClose = _ref.afterClose,\n          rest = _objectWithoutProperties(_ref, _excluded2);\n        return (/*#__PURE__*/\n          // react 15，componentWillUnmount 时 Portal 返回 afterClose, visible.\n          React.createElement(Child, _extends({}, props, rest, {\n            open: visible !== undefined ? visible : open,\n            afterVisibleChange: afterClose !== undefined ? afterClose : props.afterVisibleChange,\n            handler: handler,\n            onClose: _this2.onClose,\n            onHandleClick: _this2.onHandleClick\n          }))\n        );\n      });\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, _ref2) {\n      var prevProps = _ref2.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      if (typeof prevProps !== 'undefined' && props.open !== prevProps.open) {\n        newState.open = props.open;\n      }\n      return newState;\n    }\n  }]);\n  return DrawerWrapper;\n}(React.Component);\nDrawerWrapper.defaultProps = {\n  prefixCls: 'drawer',\n  placement: 'left',\n  getContainer: 'body',\n  defaultOpen: false,\n  level: 'all',\n  duration: '.3s',\n  ease: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',\n  onChange: function onChange() {},\n  afterVisibleChange: function afterVisibleChange() {},\n  handler: /*#__PURE__*/React.createElement(\"div\", {\n    className: \"drawer-handle\"\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"drawer-handle-icon\"\n  })),\n  showMask: true,\n  maskClosable: true,\n  maskStyle: {},\n  wrapperClassName: '',\n  className: '',\n  keyboard: true,\n  forceRender: false,\n  autoFocus: true\n};\nexport default DrawerWrapper;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "_excluded", "_excluded2", "Portal", "React", "Child", "Drawer<PERSON><PERSON><PERSON>", "_React$Component", "_super", "props", "_this", "call", "dom", "onHandleClick", "e", "_this$props", "$open", "open", "state", "setState", "onClose", "_this$props2", "_open", "defaultOpen", "console", "warn", "key", "value", "render", "_this2", "_this$props3", "getContainer", "wrapperClassName", "forceRender", "handler", "createElement", "className", "ref", "c", "$forceRender", "visible", "_ref", "afterClose", "rest", "undefined", "afterVisibleChange", "getDerivedStateFromProps", "_ref2", "prevProps", "newState", "Component", "defaultProps", "prefixCls", "placement", "level", "duration", "ease", "onChange", "showMask", "maskClosable", "maskStyle", "keyboard", "autoFocus"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-drawer/es/DrawerWrapper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"defaultOpen\", \"getContainer\", \"wrapperClassName\", \"forceRender\", \"handler\"],\n    _excluded2 = [\"visible\", \"afterClose\"];\nimport Portal from \"rc-util/es/PortalWrapper\";\nimport * as React from 'react';\nimport Child from './DrawerChild';\n\nvar DrawerWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(DrawerWrapper, _React$Component);\n\n  var _super = _createSuper(DrawerWrapper);\n\n  function DrawerWrapper(props) {\n    var _this;\n\n    _classCallCheck(this, DrawerWrapper);\n\n    _this = _super.call(this, props);\n    _this.dom = void 0;\n\n    _this.onHandleClick = function (e) {\n      var _this$props = _this.props,\n          onHandleClick = _this$props.onHandleClick,\n          $open = _this$props.open;\n\n      if (onHandleClick) {\n        onHandleClick(e);\n      }\n\n      if (typeof $open === 'undefined') {\n        var open = _this.state.open;\n\n        _this.setState({\n          open: !open\n        });\n      }\n    };\n\n    _this.onClose = function (e) {\n      var _this$props2 = _this.props,\n          onClose = _this$props2.onClose,\n          open = _this$props2.open;\n\n      if (onClose) {\n        onClose(e);\n      }\n\n      if (typeof open === 'undefined') {\n        _this.setState({\n          open: false\n        });\n      }\n    };\n\n    var _open = typeof props.open !== 'undefined' ? props.open : !!props.defaultOpen;\n\n    _this.state = {\n      open: _open\n    };\n\n    if ('onMaskClick' in props) {\n      console.warn('`onMaskClick` are removed, please use `onClose` instead.');\n    }\n\n    return _this;\n  }\n\n  _createClass(DrawerWrapper, [{\n    key: \"render\",\n    value: // tslint:disable-next-line:member-ordering\n    function render() {\n      var _this2 = this;\n\n      var _this$props3 = this.props,\n          defaultOpen = _this$props3.defaultOpen,\n          getContainer = _this$props3.getContainer,\n          wrapperClassName = _this$props3.wrapperClassName,\n          forceRender = _this$props3.forceRender,\n          handler = _this$props3.handler,\n          props = _objectWithoutProperties(_this$props3, _excluded);\n\n      var open = this.state.open; // 渲染在当前 dom 里；\n\n      if (!getContainer) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: wrapperClassName,\n          ref: function ref(c) {\n            _this2.dom = c;\n          }\n        }, /*#__PURE__*/React.createElement(Child, _extends({}, props, {\n          open: open,\n          handler: handler,\n          getContainer: function getContainer() {\n            return _this2.dom;\n          },\n          onClose: this.onClose,\n          onHandleClick: this.onHandleClick\n        })));\n      } // 如果有 handler 为内置强制渲染；\n\n\n      var $forceRender = !!handler || forceRender;\n      return /*#__PURE__*/React.createElement(Portal, {\n        visible: open,\n        forceRender: $forceRender,\n        getContainer: getContainer,\n        wrapperClassName: wrapperClassName\n      }, function (_ref) {\n        var visible = _ref.visible,\n            afterClose = _ref.afterClose,\n            rest = _objectWithoutProperties(_ref, _excluded2);\n\n        return (\n          /*#__PURE__*/\n          // react 15，componentWillUnmount 时 Portal 返回 afterClose, visible.\n          React.createElement(Child, _extends({}, props, rest, {\n            open: visible !== undefined ? visible : open,\n            afterVisibleChange: afterClose !== undefined ? afterClose : props.afterVisibleChange,\n            handler: handler,\n            onClose: _this2.onClose,\n            onHandleClick: _this2.onHandleClick\n          }))\n        );\n      });\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, _ref2) {\n      var prevProps = _ref2.prevProps;\n      var newState = {\n        prevProps: props\n      };\n\n      if (typeof prevProps !== 'undefined' && props.open !== prevProps.open) {\n        newState.open = props.open;\n      }\n\n      return newState;\n    }\n  }]);\n\n  return DrawerWrapper;\n}(React.Component);\n\nDrawerWrapper.defaultProps = {\n  prefixCls: 'drawer',\n  placement: 'left',\n  getContainer: 'body',\n  defaultOpen: false,\n  level: 'all',\n  duration: '.3s',\n  ease: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',\n  onChange: function onChange() {},\n  afterVisibleChange: function afterVisibleChange() {},\n  handler: /*#__PURE__*/React.createElement(\"div\", {\n    className: \"drawer-handle\"\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"drawer-handle-icon\"\n  })),\n  showMask: true,\n  maskClosable: true,\n  maskStyle: {},\n  wrapperClassName: '',\n  className: '',\n  keyboard: true,\n  forceRender: false,\n  autoFocus: true\n};\nexport default DrawerWrapper;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,aAAa,EAAE,cAAc,EAAE,kBAAkB,EAAE,aAAa,EAAE,SAAS,CAAC;EACzFC,UAAU,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC;AAC1C,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,eAAe;AAEjC,IAAIC,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3DR,SAAS,CAACO,aAAa,EAAEC,gBAAgB,CAAC;EAE1C,IAAIC,MAAM,GAAGR,YAAY,CAACM,aAAa,CAAC;EAExC,SAASA,aAAaA,CAACG,KAAK,EAAE;IAC5B,IAAIC,KAAK;IAETb,eAAe,CAAC,IAAI,EAAES,aAAa,CAAC;IAEpCI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,GAAG,GAAG,KAAK,CAAC;IAElBF,KAAK,CAACG,aAAa,GAAG,UAAUC,CAAC,EAAE;MACjC,IAAIC,WAAW,GAAGL,KAAK,CAACD,KAAK;QACzBI,aAAa,GAAGE,WAAW,CAACF,aAAa;QACzCG,KAAK,GAAGD,WAAW,CAACE,IAAI;MAE5B,IAAIJ,aAAa,EAAE;QACjBA,aAAa,CAACC,CAAC,CAAC;MAClB;MAEA,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;QAChC,IAAIC,IAAI,GAAGP,KAAK,CAACQ,KAAK,CAACD,IAAI;QAE3BP,KAAK,CAACS,QAAQ,CAAC;UACbF,IAAI,EAAE,CAACA;QACT,CAAC,CAAC;MACJ;IACF,CAAC;IAEDP,KAAK,CAACU,OAAO,GAAG,UAAUN,CAAC,EAAE;MAC3B,IAAIO,YAAY,GAAGX,KAAK,CAACD,KAAK;QAC1BW,OAAO,GAAGC,YAAY,CAACD,OAAO;QAC9BH,IAAI,GAAGI,YAAY,CAACJ,IAAI;MAE5B,IAAIG,OAAO,EAAE;QACXA,OAAO,CAACN,CAAC,CAAC;MACZ;MAEA,IAAI,OAAOG,IAAI,KAAK,WAAW,EAAE;QAC/BP,KAAK,CAACS,QAAQ,CAAC;UACbF,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF,CAAC;IAED,IAAIK,KAAK,GAAG,OAAOb,KAAK,CAACQ,IAAI,KAAK,WAAW,GAAGR,KAAK,CAACQ,IAAI,GAAG,CAAC,CAACR,KAAK,CAACc,WAAW;IAEhFb,KAAK,CAACQ,KAAK,GAAG;MACZD,IAAI,EAAEK;IACR,CAAC;IAED,IAAI,aAAa,IAAIb,KAAK,EAAE;MAC1Be,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;IAC1E;IAEA,OAAOf,KAAK;EACd;EAEAZ,YAAY,CAACQ,aAAa,EAAE,CAAC;IAC3BoB,GAAG,EAAE,QAAQ;IACbC,KAAK;IAAE;IACP,SAASC,MAAMA,CAAA,EAAG;MAChB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAACrB,KAAK;QACzBc,WAAW,GAAGO,YAAY,CAACP,WAAW;QACtCQ,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxCC,gBAAgB,GAAGF,YAAY,CAACE,gBAAgB;QAChDC,WAAW,GAAGH,YAAY,CAACG,WAAW;QACtCC,OAAO,GAAGJ,YAAY,CAACI,OAAO;QAC9BzB,KAAK,GAAGb,wBAAwB,CAACkC,YAAY,EAAE7B,SAAS,CAAC;MAE7D,IAAIgB,IAAI,GAAG,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC,CAAC;;MAE5B,IAAI,CAACc,YAAY,EAAE;QACjB,OAAO,aAAa3B,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;UAC7CC,SAAS,EAAEJ,gBAAgB;UAC3BK,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;YACnBT,MAAM,CAACjB,GAAG,GAAG0B,CAAC;UAChB;QACF,CAAC,EAAE,aAAalC,KAAK,CAAC+B,aAAa,CAAC9B,KAAK,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,EAAE;UAC7DQ,IAAI,EAAEA,IAAI;UACViB,OAAO,EAAEA,OAAO;UAChBH,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;YACpC,OAAOF,MAAM,CAACjB,GAAG;UACnB,CAAC;UACDQ,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBP,aAAa,EAAE,IAAI,CAACA;QACtB,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC;;MAGF,IAAI0B,YAAY,GAAG,CAAC,CAACL,OAAO,IAAID,WAAW;MAC3C,OAAO,aAAa7B,KAAK,CAAC+B,aAAa,CAAChC,MAAM,EAAE;QAC9CqC,OAAO,EAAEvB,IAAI;QACbgB,WAAW,EAAEM,YAAY;QACzBR,YAAY,EAAEA,YAAY;QAC1BC,gBAAgB,EAAEA;MACpB,CAAC,EAAE,UAAUS,IAAI,EAAE;QACjB,IAAID,OAAO,GAAGC,IAAI,CAACD,OAAO;UACtBE,UAAU,GAAGD,IAAI,CAACC,UAAU;UAC5BC,IAAI,GAAG/C,wBAAwB,CAAC6C,IAAI,EAAEvC,UAAU,CAAC;QAErD,QACE;UACA;UACAE,KAAK,CAAC+B,aAAa,CAAC9B,KAAK,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,EAAEkC,IAAI,EAAE;YACnD1B,IAAI,EAAEuB,OAAO,KAAKI,SAAS,GAAGJ,OAAO,GAAGvB,IAAI;YAC5C4B,kBAAkB,EAAEH,UAAU,KAAKE,SAAS,GAAGF,UAAU,GAAGjC,KAAK,CAACoC,kBAAkB;YACpFX,OAAO,EAAEA,OAAO;YAChBd,OAAO,EAAES,MAAM,CAACT,OAAO;YACvBP,aAAa,EAAEgB,MAAM,CAAChB;UACxB,CAAC,CAAC;QAAC;MAEP,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EAAE,CAAC;IACHa,GAAG,EAAE,0BAA0B;IAC/BC,KAAK,EAAE,SAASmB,wBAAwBA,CAACrC,KAAK,EAAEsC,KAAK,EAAE;MACrD,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;MAC/B,IAAIC,QAAQ,GAAG;QACbD,SAAS,EAAEvC;MACb,CAAC;MAED,IAAI,OAAOuC,SAAS,KAAK,WAAW,IAAIvC,KAAK,CAACQ,IAAI,KAAK+B,SAAS,CAAC/B,IAAI,EAAE;QACrEgC,QAAQ,CAAChC,IAAI,GAAGR,KAAK,CAACQ,IAAI;MAC5B;MAEA,OAAOgC,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EAEH,OAAO3C,aAAa;AACtB,CAAC,CAACF,KAAK,CAAC8C,SAAS,CAAC;AAElB5C,aAAa,CAAC6C,YAAY,GAAG;EAC3BC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,MAAM;EACjBtB,YAAY,EAAE,MAAM;EACpBR,WAAW,EAAE,KAAK;EAClB+B,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,sCAAsC;EAC5CC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG,CAAC,CAAC;EAChCZ,kBAAkB,EAAE,SAASA,kBAAkBA,CAAA,EAAG,CAAC,CAAC;EACpDX,OAAO,EAAE,aAAa9B,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC/CC,SAAS,EAAE;EACb,CAAC,EAAE,aAAahC,KAAK,CAAC+B,aAAa,CAAC,GAAG,EAAE;IACvCC,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;EACHsB,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,CAAC,CAAC;EACb5B,gBAAgB,EAAE,EAAE;EACpBI,SAAS,EAAE,EAAE;EACbyB,QAAQ,EAAE,IAAI;EACd5B,WAAW,EAAE,KAAK;EAClB6B,SAAS,EAAE;AACb,CAAC;AACD,eAAexD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}