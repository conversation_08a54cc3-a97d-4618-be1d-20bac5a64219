{"ast": null, "code": "/* istanbul ignore next */\n\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;", "map": {"version": 3, "names": ["Column", "_"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/sugar/Column.js"], "sourcesContent": ["/* istanbul ignore next */\n\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Column(_) {\n  return null;\n}\n\nexport default Column;"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,OAAO,IAAI;AACb;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}