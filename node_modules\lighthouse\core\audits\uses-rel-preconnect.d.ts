export default UsesRelPreconnectAudit;
declare class UsesRelPreconnectAudit extends Audit {
    /**
     * Check if record has valid timing
     * @param {LH.Artifacts.NetworkRequest} record
     * @return {boolean}
     */
    static hasValidTiming(record: LH.Artifacts.NetworkRequest): boolean;
    /**
     * Check is the connection is already open
     * @param {LH.Artifacts.NetworkRequest} record
     * @return {boolean}
     */
    static hasAlreadyConnectedToOrigin(record: LH.Artifacts.NetworkRequest): boolean;
    /**
     * Check is the connection has started before the socket idle time
     * @param {LH.Artifacts.NetworkRequest} record
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @return {boolean}
     */
    static socketStartTimeIsBelowThreshold(record: LH.Artifacts.NetworkRequest, mainResource: LH.Artifacts.NetworkRequest): boolean;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const unusedWarning: string;
    const crossoriginWarning: string;
    const tooManyPreconnectLinksWarning: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=uses-rel-preconnect.d.ts.map