{"ast": null, "code": "// Default to a dummy \"batch\" implementation that just runs the callback\nfunction defaultNoopBatch(callback) {\n  callback();\n}\nvar batch = defaultNoopBatch; // Allow injecting another batching function later\n\nexport var setBatch = function setBatch(newBatch) {\n  return batch = newBatch;\n}; // Supply a getter just to skip dealing with ESM bindings\n\nexport var getBatch = function getBatch() {\n  return batch;\n};", "map": {"version": 3, "names": ["defaultNoopBatch", "callback", "batch", "setBatch", "newBatch", "getBatch"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-redux/es/utils/batch.js"], "sourcesContent": ["// Default to a dummy \"batch\" implementation that just runs the callback\nfunction defaultNoopBatch(callback) {\n  callback();\n}\n\nvar batch = defaultNoopBatch; // Allow injecting another batching function later\n\nexport var setBatch = function setBatch(newBatch) {\n  return batch = newBatch;\n}; // Supply a getter just to skip dealing with ESM bindings\n\nexport var getBatch = function getBatch() {\n  return batch;\n};"], "mappings": "AAAA;AACA,SAASA,gBAAgBA,CAACC,QAAQ,EAAE;EAClCA,QAAQ,CAAC,CAAC;AACZ;AAEA,IAAIC,KAAK,GAAGF,gBAAgB,CAAC,CAAC;;AAE9B,OAAO,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,QAAQ,EAAE;EAChD,OAAOF,KAAK,GAAGE,QAAQ;AACzB,CAAC,CAAC,CAAC;;AAEH,OAAO,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACxC,OAAOH,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}