{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from '../Cell';\nimport TableContext from '../context/TableContext';\nimport { getCellFixedInfo } from '../utils/fixUtil';\nimport { getColumnsKey } from '../utils/valueUtil';\nfunction HeaderRow(_ref) {\n  var cells = _ref.cells,\n    stickyOffsets = _ref.stickyOffsets,\n    flattenColumns = _ref.flattenColumns,\n    RowComponent = _ref.rowComponent,\n    CellComponent = _ref.cellComponent,\n    onHeaderRow = _ref.onHeaderRow,\n    index = _ref.index;\n  var _React$useContext = React.useContext(TableContext),\n    prefixCls = _React$useContext.prefixCls,\n    direction = _React$useContext.direction;\n  var rowProps;\n  if (onHeaderRow) {\n    rowProps = onHeaderRow(cells.map(function (cell) {\n      return cell.column;\n    }), index);\n  }\n  var columnsKey = getColumnsKey(cells.map(function (cell) {\n    return cell.column;\n  }));\n  return /*#__PURE__*/React.createElement(RowComponent, rowProps, cells.map(function (cell, cellIndex) {\n    var column = cell.column;\n    var fixedInfo = getCellFixedInfo(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction);\n    var additionalProps;\n    if (column && column.onHeaderCell) {\n      additionalProps = cell.column.onHeaderCell(column);\n    }\n    return /*#__PURE__*/React.createElement(Cell, _extends({}, cell, {\n      ellipsis: column.ellipsis,\n      align: column.align,\n      component: CellComponent,\n      prefixCls: prefixCls,\n      key: columnsKey[cellIndex]\n    }, fixedInfo, {\n      additionalProps: additionalProps,\n      rowType: \"header\"\n    }));\n  }));\n}\nHeaderRow.displayName = 'HeaderRow';\nexport default HeaderRow;", "map": {"version": 3, "names": ["_extends", "React", "Cell", "TableContext", "getCellFixedInfo", "getColumnsKey", "HeaderRow", "_ref", "cells", "stickyOffsets", "flattenColumns", "RowComponent", "rowComponent", "CellComponent", "cellComponent", "onHeaderRow", "index", "_React$useContext", "useContext", "prefixCls", "direction", "rowProps", "map", "cell", "column", "columnsKey", "createElement", "cellIndex", "fixedInfo", "colStart", "colEnd", "additionalProps", "onHeaderCell", "ellipsis", "align", "component", "key", "rowType", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Header/HeaderRow.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from '../Cell';\nimport TableContext from '../context/TableContext';\nimport { getCellFixedInfo } from '../utils/fixUtil';\nimport { getColumnsKey } from '../utils/valueUtil';\n\nfunction HeaderRow(_ref) {\n  var cells = _ref.cells,\n      stickyOffsets = _ref.stickyOffsets,\n      flattenColumns = _ref.flattenColumns,\n      RowComponent = _ref.rowComponent,\n      CellComponent = _ref.cellComponent,\n      onHeaderRow = _ref.onHeaderRow,\n      index = _ref.index;\n\n  var _React$useContext = React.useContext(TableContext),\n      prefixCls = _React$useContext.prefixCls,\n      direction = _React$useContext.direction;\n\n  var rowProps;\n\n  if (onHeaderRow) {\n    rowProps = onHeaderRow(cells.map(function (cell) {\n      return cell.column;\n    }), index);\n  }\n\n  var columnsKey = getColumnsKey(cells.map(function (cell) {\n    return cell.column;\n  }));\n  return /*#__PURE__*/React.createElement(RowComponent, rowProps, cells.map(function (cell, cellIndex) {\n    var column = cell.column;\n    var fixedInfo = getCellFixedInfo(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction);\n    var additionalProps;\n\n    if (column && column.onHeaderCell) {\n      additionalProps = cell.column.onHeaderCell(column);\n    }\n\n    return /*#__PURE__*/React.createElement(Cell, _extends({}, cell, {\n      ellipsis: column.ellipsis,\n      align: column.align,\n      component: CellComponent,\n      prefixCls: prefixCls,\n      key: columnsKey[cellIndex]\n    }, fixedInfo, {\n      additionalProps: additionalProps,\n      rowType: \"header\"\n    }));\n  }));\n}\n\nHeaderRow.displayName = 'HeaderRow';\nexport default HeaderRow;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,aAAa,QAAQ,oBAAoB;AAElD,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,YAAY,GAAGJ,IAAI,CAACK,YAAY;IAChCC,aAAa,GAAGN,IAAI,CAACO,aAAa;IAClCC,WAAW,GAAGR,IAAI,CAACQ,WAAW;IAC9BC,KAAK,GAAGT,IAAI,CAACS,KAAK;EAEtB,IAAIC,iBAAiB,GAAGhB,KAAK,CAACiB,UAAU,CAACf,YAAY,CAAC;IAClDgB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,QAAQ;EAEZ,IAAIN,WAAW,EAAE;IACfM,QAAQ,GAAGN,WAAW,CAACP,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC/C,OAAOA,IAAI,CAACC,MAAM;IACpB,CAAC,CAAC,EAAER,KAAK,CAAC;EACZ;EAEA,IAAIS,UAAU,GAAGpB,aAAa,CAACG,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAE;IACvD,OAAOA,IAAI,CAACC,MAAM;EACpB,CAAC,CAAC,CAAC;EACH,OAAO,aAAavB,KAAK,CAACyB,aAAa,CAACf,YAAY,EAAEU,QAAQ,EAAEb,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAEI,SAAS,EAAE;IACnG,IAAIH,MAAM,GAAGD,IAAI,CAACC,MAAM;IACxB,IAAII,SAAS,GAAGxB,gBAAgB,CAACmB,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAACO,MAAM,EAAEpB,cAAc,EAAED,aAAa,EAAEW,SAAS,CAAC;IACtG,IAAIW,eAAe;IAEnB,IAAIP,MAAM,IAAIA,MAAM,CAACQ,YAAY,EAAE;MACjCD,eAAe,GAAGR,IAAI,CAACC,MAAM,CAACQ,YAAY,CAACR,MAAM,CAAC;IACpD;IAEA,OAAO,aAAavB,KAAK,CAACyB,aAAa,CAACxB,IAAI,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEuB,IAAI,EAAE;MAC/DU,QAAQ,EAAET,MAAM,CAACS,QAAQ;MACzBC,KAAK,EAAEV,MAAM,CAACU,KAAK;MACnBC,SAAS,EAAEtB,aAAa;MACxBM,SAAS,EAAEA,SAAS;MACpBiB,GAAG,EAAEX,UAAU,CAACE,SAAS;IAC3B,CAAC,EAAEC,SAAS,EAAE;MACZG,eAAe,EAAEA,eAAe;MAChCM,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL;AAEA/B,SAAS,CAACgC,WAAW,GAAG,WAAW;AACnC,eAAehC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}