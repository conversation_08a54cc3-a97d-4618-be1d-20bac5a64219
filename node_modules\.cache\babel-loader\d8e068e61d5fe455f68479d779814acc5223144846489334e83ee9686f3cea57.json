{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport memoizeOne from 'memoize-one';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport Affix from '../affix';\nimport { ConfigContext } from '../config-provider';\nimport scrollTo from '../_util/scrollTo';\nimport getScroll from '../_util/getScroll';\nimport AnchorContext from './context';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  var rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      container = element.ownerDocument.documentElement;\n      return rect.top - container.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nvar sharpMatcherRegx = /#([\\S ]+)$/;\nvar Anchor = /*#__PURE__*/function (_React$Component) {\n  _inherits(Anchor, _React$Component);\n  var _super = _createSuper(Anchor);\n  function Anchor() {\n    var _this;\n    _classCallCheck(this, Anchor);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      activeLink: null\n    };\n    _this.wrapperRef = /*#__PURE__*/React.createRef();\n    _this.links = []; // Context\n\n    _this.registerLink = function (link) {\n      if (!_this.links.includes(link)) {\n        _this.links.push(link);\n      }\n    };\n    _this.unregisterLink = function (link) {\n      var index = _this.links.indexOf(link);\n      if (index !== -1) {\n        _this.links.splice(index, 1);\n      }\n    };\n    _this.getContainer = function () {\n      var getTargetContainer = _this.context.getTargetContainer;\n      var getContainer = _this.props.getContainer;\n      var getFunc = getContainer || getTargetContainer || getDefaultContainer;\n      return getFunc();\n    };\n    _this.handleScrollTo = function (link) {\n      var _this$props = _this.props,\n        offsetTop = _this$props.offsetTop,\n        targetOffset = _this$props.targetOffset;\n      _this.setCurrentActiveLink(link);\n      var container = _this.getContainer();\n      var scrollTop = getScroll(container, true);\n      var sharpLinkMatch = sharpMatcherRegx.exec(link);\n      if (!sharpLinkMatch) {\n        return;\n      }\n      var targetElement = document.getElementById(sharpLinkMatch[1]);\n      if (!targetElement) {\n        return;\n      }\n      var eleOffsetTop = getOffsetTop(targetElement, container);\n      var y = scrollTop + eleOffsetTop;\n      y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n      _this.animating = true;\n      scrollTo(y, {\n        callback: function callback() {\n          _this.animating = false;\n        },\n        getContainer: _this.getContainer\n      });\n    };\n    _this.saveInkNode = function (node) {\n      _this.inkNode = node;\n    };\n    _this.setCurrentActiveLink = function (link) {\n      var activeLink = _this.state.activeLink;\n      var _this$props2 = _this.props,\n        onChange = _this$props2.onChange,\n        getCurrentAnchor = _this$props2.getCurrentAnchor;\n      if (activeLink === link) {\n        return;\n      } // https://github.com/ant-design/ant-design/issues/30584\n\n      _this.setState({\n        activeLink: typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link\n      });\n      onChange === null || onChange === void 0 ? void 0 : onChange(link);\n    };\n    _this.handleScroll = function () {\n      if (_this.animating) {\n        return;\n      }\n      var _this$props3 = _this.props,\n        offsetTop = _this$props3.offsetTop,\n        bounds = _this$props3.bounds,\n        targetOffset = _this$props3.targetOffset;\n      var currentActiveLink = _this.getCurrentAnchor(targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n      _this.setCurrentActiveLink(currentActiveLink);\n    };\n    _this.updateInk = function () {\n      var _assertThisInitialize = _assertThisInitialized(_this),\n        prefixCls = _assertThisInitialize.prefixCls,\n        wrapperRef = _assertThisInitialize.wrapperRef;\n      var anchorNode = wrapperRef.current;\n      var linkNode = anchorNode === null || anchorNode === void 0 ? void 0 : anchorNode.getElementsByClassName(\"\".concat(prefixCls, \"-link-title-active\"))[0];\n      if (linkNode) {\n        _this.inkNode.style.top = \"\".concat(linkNode.offsetTop + linkNode.clientHeight / 2 - 4.5, \"px\");\n      }\n    };\n    _this.getMemoizedContextValue = memoizeOne(function (link, onClickFn) {\n      return {\n        registerLink: _this.registerLink,\n        unregisterLink: _this.unregisterLink,\n        scrollTo: _this.handleScrollTo,\n        activeLink: link,\n        onClick: onClickFn\n      };\n    });\n    return _this;\n  }\n  _createClass(Anchor, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.scrollContainer = this.getContainer();\n      this.scrollEvent = addEventListener(this.scrollContainer, 'scroll', this.handleScroll);\n      this.handleScroll();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (this.scrollEvent) {\n        var currentContainer = this.getContainer();\n        if (this.scrollContainer !== currentContainer) {\n          this.scrollContainer = currentContainer;\n          this.scrollEvent.remove();\n          this.scrollEvent = addEventListener(this.scrollContainer, 'scroll', this.handleScroll);\n          this.handleScroll();\n        }\n      }\n      this.updateInk();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.scrollEvent) {\n        this.scrollEvent.remove();\n      }\n    }\n  }, {\n    key: \"getCurrentAnchor\",\n    value: function getCurrentAnchor() {\n      var offsetTop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var bounds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n      var linkSections = [];\n      var container = this.getContainer();\n      this.links.forEach(function (link) {\n        var sharpLinkMatch = sharpMatcherRegx.exec(link.toString());\n        if (!sharpLinkMatch) {\n          return;\n        }\n        var target = document.getElementById(sharpLinkMatch[1]);\n        if (target) {\n          var top = getOffsetTop(target, container);\n          if (top < offsetTop + bounds) {\n            linkSections.push({\n              link: link,\n              top: top\n            });\n          }\n        }\n      });\n      if (linkSections.length) {\n        var maxSection = linkSections.reduce(function (prev, curr) {\n          return curr.top > prev.top ? curr : prev;\n        });\n        return maxSection.link;\n      }\n      return '';\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var direction = this.context.direction;\n      var _this$props4 = this.props,\n        prefixCls = _this$props4.anchorPrefixCls,\n        _this$props4$classNam = _this$props4.className,\n        className = _this$props4$classNam === void 0 ? '' : _this$props4$classNam,\n        style = _this$props4.style,\n        offsetTop = _this$props4.offsetTop,\n        affix = _this$props4.affix,\n        showInkInFixed = _this$props4.showInkInFixed,\n        children = _this$props4.children,\n        onClick = _this$props4.onClick;\n      var activeLink = this.state.activeLink; // To support old version react.\n      // Have to add prefixCls on the instance.\n      // https://github.com/facebook/react/issues/12397\n\n      this.prefixCls = prefixCls;\n      var inkClass = classNames(\"\".concat(prefixCls, \"-ink-ball\"), {\n        visible: activeLink\n      });\n      var wrapperClass = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n      var anchorClass = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-fixed\"), !affix && !showInkInFixed));\n      var wrapperStyle = _extends({\n        maxHeight: offsetTop ? \"calc(100vh - \".concat(offsetTop, \"px)\") : '100vh'\n      }, style);\n      var anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.wrapperRef,\n        className: wrapperClass,\n        style: wrapperStyle\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: anchorClass\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-ink\")\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: inkClass,\n        ref: this.saveInkNode\n      })), children));\n      var contextValue = this.getMemoizedContextValue(activeLink, onClick);\n      return /*#__PURE__*/React.createElement(AnchorContext.Provider, {\n        value: contextValue\n      }, !affix ? anchorContent : /*#__PURE__*/React.createElement(Affix, {\n        offsetTop: offsetTop,\n        target: this.getContainer\n      }, anchorContent));\n    }\n  }]);\n  return Anchor;\n}(React.Component);\nAnchor.defaultProps = {\n  affix: true,\n  showInkInFixed: false\n};\nAnchor.contextType = ConfigContext;\nvar AnchorFC = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var customizePrefixCls = props.prefixCls;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var anchorPrefixCls = getPrefixCls('anchor', customizePrefixCls);\n  var anchorProps = _extends(_extends({}, props), {\n    anchorPrefixCls: anchorPrefixCls\n  });\n  return /*#__PURE__*/React.createElement(Anchor, _extends({}, anchorProps, {\n    ref: ref\n  }));\n});\nexport default AnchorFC;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "React", "classNames", "memoizeOne", "addEventListener", "Affix", "ConfigContext", "scrollTo", "getScroll", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDefaultContainer", "window", "getOffsetTop", "element", "container", "getClientRects", "length", "rect", "getBoundingClientRect", "width", "height", "ownerDocument", "documentElement", "top", "clientTop", "sharpMatcherRegx", "<PERSON><PERSON>", "_React$Component", "_super", "_this", "apply", "arguments", "state", "activeLink", "wrapperRef", "createRef", "links", "registerLink", "link", "includes", "push", "unregisterLink", "index", "indexOf", "splice", "getContainer", "getTargetContainer", "context", "props", "getFunc", "handleScrollTo", "_this$props", "offsetTop", "targetOffset", "setCurrentActiveLink", "scrollTop", "sharpLinkMatch", "exec", "targetElement", "document", "getElementById", "eleOffsetTop", "y", "undefined", "animating", "callback", "saveInkNode", "node", "inkNode", "_this$props2", "onChange", "getCurrentAnchor", "setState", "handleScroll", "_this$props3", "bounds", "currentActiveLink", "updateInk", "_assertThisInitialize", "prefixCls", "anchorNode", "current", "linkNode", "getElementsByClassName", "concat", "style", "clientHeight", "getMemoizedContextValue", "onClickFn", "onClick", "key", "value", "componentDidMount", "scrollContainer", "scrollEvent", "componentDidUpdate", "currentC<PERSON><PERSON>", "remove", "componentWillUnmount", "linkSections", "for<PERSON>ach", "toString", "target", "maxSection", "reduce", "prev", "curr", "render", "direction", "_this$props4", "anchorPrefixCls", "_this$props4$classNam", "className", "affix", "showInkInFixed", "children", "inkClass", "visible", "wrapperClass", "anchorClass", "wrapperStyle", "maxHeight", "anchorContent", "createElement", "ref", "contextValue", "Provider", "Component", "defaultProps", "contextType", "AnchorFC", "forwardRef", "customizePrefixCls", "_React$useContext", "useContext", "getPrefixCls", "anchorProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/anchor/Anchor.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport memoizeOne from 'memoize-one';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport Affix from '../affix';\nimport { ConfigContext } from '../config-provider';\nimport scrollTo from '../_util/scrollTo';\nimport getScroll from '../_util/getScroll';\nimport AnchorContext from './context';\n\nfunction getDefaultContainer() {\n  return window;\n}\n\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n\n  var rect = element.getBoundingClientRect();\n\n  if (rect.width || rect.height) {\n    if (container === window) {\n      container = element.ownerDocument.documentElement;\n      return rect.top - container.clientTop;\n    }\n\n    return rect.top - container.getBoundingClientRect().top;\n  }\n\n  return rect.top;\n}\n\nvar sharpMatcherRegx = /#([\\S ]+)$/;\n\nvar Anchor = /*#__PURE__*/function (_React$Component) {\n  _inherits(Anchor, _React$Component);\n\n  var _super = _createSuper(Anchor);\n\n  function Anchor() {\n    var _this;\n\n    _classCallCheck(this, Anchor);\n\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      activeLink: null\n    };\n    _this.wrapperRef = /*#__PURE__*/React.createRef();\n    _this.links = []; // Context\n\n    _this.registerLink = function (link) {\n      if (!_this.links.includes(link)) {\n        _this.links.push(link);\n      }\n    };\n\n    _this.unregisterLink = function (link) {\n      var index = _this.links.indexOf(link);\n\n      if (index !== -1) {\n        _this.links.splice(index, 1);\n      }\n    };\n\n    _this.getContainer = function () {\n      var getTargetContainer = _this.context.getTargetContainer;\n      var getContainer = _this.props.getContainer;\n      var getFunc = getContainer || getTargetContainer || getDefaultContainer;\n      return getFunc();\n    };\n\n    _this.handleScrollTo = function (link) {\n      var _this$props = _this.props,\n          offsetTop = _this$props.offsetTop,\n          targetOffset = _this$props.targetOffset;\n\n      _this.setCurrentActiveLink(link);\n\n      var container = _this.getContainer();\n\n      var scrollTop = getScroll(container, true);\n      var sharpLinkMatch = sharpMatcherRegx.exec(link);\n\n      if (!sharpLinkMatch) {\n        return;\n      }\n\n      var targetElement = document.getElementById(sharpLinkMatch[1]);\n\n      if (!targetElement) {\n        return;\n      }\n\n      var eleOffsetTop = getOffsetTop(targetElement, container);\n      var y = scrollTop + eleOffsetTop;\n      y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n      _this.animating = true;\n      scrollTo(y, {\n        callback: function callback() {\n          _this.animating = false;\n        },\n        getContainer: _this.getContainer\n      });\n    };\n\n    _this.saveInkNode = function (node) {\n      _this.inkNode = node;\n    };\n\n    _this.setCurrentActiveLink = function (link) {\n      var activeLink = _this.state.activeLink;\n      var _this$props2 = _this.props,\n          onChange = _this$props2.onChange,\n          getCurrentAnchor = _this$props2.getCurrentAnchor;\n\n      if (activeLink === link) {\n        return;\n      } // https://github.com/ant-design/ant-design/issues/30584\n\n\n      _this.setState({\n        activeLink: typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link\n      });\n\n      onChange === null || onChange === void 0 ? void 0 : onChange(link);\n    };\n\n    _this.handleScroll = function () {\n      if (_this.animating) {\n        return;\n      }\n\n      var _this$props3 = _this.props,\n          offsetTop = _this$props3.offsetTop,\n          bounds = _this$props3.bounds,\n          targetOffset = _this$props3.targetOffset;\n\n      var currentActiveLink = _this.getCurrentAnchor(targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n\n      _this.setCurrentActiveLink(currentActiveLink);\n    };\n\n    _this.updateInk = function () {\n      var _assertThisInitialize = _assertThisInitialized(_this),\n          prefixCls = _assertThisInitialize.prefixCls,\n          wrapperRef = _assertThisInitialize.wrapperRef;\n\n      var anchorNode = wrapperRef.current;\n      var linkNode = anchorNode === null || anchorNode === void 0 ? void 0 : anchorNode.getElementsByClassName(\"\".concat(prefixCls, \"-link-title-active\"))[0];\n\n      if (linkNode) {\n        _this.inkNode.style.top = \"\".concat(linkNode.offsetTop + linkNode.clientHeight / 2 - 4.5, \"px\");\n      }\n    };\n\n    _this.getMemoizedContextValue = memoizeOne(function (link, onClickFn) {\n      return {\n        registerLink: _this.registerLink,\n        unregisterLink: _this.unregisterLink,\n        scrollTo: _this.handleScrollTo,\n        activeLink: link,\n        onClick: onClickFn\n      };\n    });\n    return _this;\n  }\n\n  _createClass(Anchor, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.scrollContainer = this.getContainer();\n      this.scrollEvent = addEventListener(this.scrollContainer, 'scroll', this.handleScroll);\n      this.handleScroll();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (this.scrollEvent) {\n        var currentContainer = this.getContainer();\n\n        if (this.scrollContainer !== currentContainer) {\n          this.scrollContainer = currentContainer;\n          this.scrollEvent.remove();\n          this.scrollEvent = addEventListener(this.scrollContainer, 'scroll', this.handleScroll);\n          this.handleScroll();\n        }\n      }\n\n      this.updateInk();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.scrollEvent) {\n        this.scrollEvent.remove();\n      }\n    }\n  }, {\n    key: \"getCurrentAnchor\",\n    value: function getCurrentAnchor() {\n      var offsetTop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var bounds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n      var linkSections = [];\n      var container = this.getContainer();\n      this.links.forEach(function (link) {\n        var sharpLinkMatch = sharpMatcherRegx.exec(link.toString());\n\n        if (!sharpLinkMatch) {\n          return;\n        }\n\n        var target = document.getElementById(sharpLinkMatch[1]);\n\n        if (target) {\n          var top = getOffsetTop(target, container);\n\n          if (top < offsetTop + bounds) {\n            linkSections.push({\n              link: link,\n              top: top\n            });\n          }\n        }\n      });\n\n      if (linkSections.length) {\n        var maxSection = linkSections.reduce(function (prev, curr) {\n          return curr.top > prev.top ? curr : prev;\n        });\n        return maxSection.link;\n      }\n\n      return '';\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var direction = this.context.direction;\n      var _this$props4 = this.props,\n          prefixCls = _this$props4.anchorPrefixCls,\n          _this$props4$classNam = _this$props4.className,\n          className = _this$props4$classNam === void 0 ? '' : _this$props4$classNam,\n          style = _this$props4.style,\n          offsetTop = _this$props4.offsetTop,\n          affix = _this$props4.affix,\n          showInkInFixed = _this$props4.showInkInFixed,\n          children = _this$props4.children,\n          onClick = _this$props4.onClick;\n      var activeLink = this.state.activeLink; // To support old version react.\n      // Have to add prefixCls on the instance.\n      // https://github.com/facebook/react/issues/12397\n\n      this.prefixCls = prefixCls;\n      var inkClass = classNames(\"\".concat(prefixCls, \"-ink-ball\"), {\n        visible: activeLink\n      });\n      var wrapperClass = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n      var anchorClass = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-fixed\"), !affix && !showInkInFixed));\n\n      var wrapperStyle = _extends({\n        maxHeight: offsetTop ? \"calc(100vh - \".concat(offsetTop, \"px)\") : '100vh'\n      }, style);\n\n      var anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.wrapperRef,\n        className: wrapperClass,\n        style: wrapperStyle\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: anchorClass\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-ink\")\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: inkClass,\n        ref: this.saveInkNode\n      })), children));\n      var contextValue = this.getMemoizedContextValue(activeLink, onClick);\n      return /*#__PURE__*/React.createElement(AnchorContext.Provider, {\n        value: contextValue\n      }, !affix ? anchorContent : /*#__PURE__*/React.createElement(Affix, {\n        offsetTop: offsetTop,\n        target: this.getContainer\n      }, anchorContent));\n    }\n  }]);\n\n  return Anchor;\n}(React.Component);\n\nAnchor.defaultProps = {\n  affix: true,\n  showInkInFixed: false\n};\nAnchor.contextType = ConfigContext;\nvar AnchorFC = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var customizePrefixCls = props.prefixCls;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var anchorPrefixCls = getPrefixCls('anchor', customizePrefixCls);\n\n  var anchorProps = _extends(_extends({}, props), {\n    anchorPrefixCls: anchorPrefixCls\n  });\n\n  return /*#__PURE__*/React.createElement(Anchor, _extends({}, anchorProps, {\n    ref: ref\n  }));\n});\nexport default AnchorFC;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,aAAa,MAAM,WAAW;AAErC,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,OAAOC,MAAM;AACf;AAEA,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACxC,IAAI,CAACD,OAAO,CAACE,cAAc,CAAC,CAAC,CAACC,MAAM,EAAE;IACpC,OAAO,CAAC;EACV;EAEA,IAAIC,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;EAE1C,IAAID,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,MAAM,EAAE;IAC7B,IAAIN,SAAS,KAAKH,MAAM,EAAE;MACxBG,SAAS,GAAGD,OAAO,CAACQ,aAAa,CAACC,eAAe;MACjD,OAAOL,IAAI,CAACM,GAAG,GAAGT,SAAS,CAACU,SAAS;IACvC;IAEA,OAAOP,IAAI,CAACM,GAAG,GAAGT,SAAS,CAACI,qBAAqB,CAAC,CAAC,CAACK,GAAG;EACzD;EAEA,OAAON,IAAI,CAACM,GAAG;AACjB;AAEA,IAAIE,gBAAgB,GAAG,YAAY;AAEnC,IAAIC,MAAM,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACpD5B,SAAS,CAAC2B,MAAM,EAAEC,gBAAgB,CAAC;EAEnC,IAAIC,MAAM,GAAG5B,YAAY,CAAC0B,MAAM,CAAC;EAEjC,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IAETjC,eAAe,CAAC,IAAI,EAAE8B,MAAM,CAAC;IAE7BG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrCF,KAAK,CAACG,KAAK,GAAG;MACZC,UAAU,EAAE;IACd,CAAC;IACDJ,KAAK,CAACK,UAAU,GAAG,aAAajC,KAAK,CAACkC,SAAS,CAAC,CAAC;IACjDN,KAAK,CAACO,KAAK,GAAG,EAAE,CAAC,CAAC;;IAElBP,KAAK,CAACQ,YAAY,GAAG,UAAUC,IAAI,EAAE;MACnC,IAAI,CAACT,KAAK,CAACO,KAAK,CAACG,QAAQ,CAACD,IAAI,CAAC,EAAE;QAC/BT,KAAK,CAACO,KAAK,CAACI,IAAI,CAACF,IAAI,CAAC;MACxB;IACF,CAAC;IAEDT,KAAK,CAACY,cAAc,GAAG,UAAUH,IAAI,EAAE;MACrC,IAAII,KAAK,GAAGb,KAAK,CAACO,KAAK,CAACO,OAAO,CAACL,IAAI,CAAC;MAErC,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBb,KAAK,CAACO,KAAK,CAACQ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9B;IACF,CAAC;IAEDb,KAAK,CAACgB,YAAY,GAAG,YAAY;MAC/B,IAAIC,kBAAkB,GAAGjB,KAAK,CAACkB,OAAO,CAACD,kBAAkB;MACzD,IAAID,YAAY,GAAGhB,KAAK,CAACmB,KAAK,CAACH,YAAY;MAC3C,IAAII,OAAO,GAAGJ,YAAY,IAAIC,kBAAkB,IAAIpC,mBAAmB;MACvE,OAAOuC,OAAO,CAAC,CAAC;IAClB,CAAC;IAEDpB,KAAK,CAACqB,cAAc,GAAG,UAAUZ,IAAI,EAAE;MACrC,IAAIa,WAAW,GAAGtB,KAAK,CAACmB,KAAK;QACzBI,SAAS,GAAGD,WAAW,CAACC,SAAS;QACjCC,YAAY,GAAGF,WAAW,CAACE,YAAY;MAE3CxB,KAAK,CAACyB,oBAAoB,CAAChB,IAAI,CAAC;MAEhC,IAAIxB,SAAS,GAAGe,KAAK,CAACgB,YAAY,CAAC,CAAC;MAEpC,IAAIU,SAAS,GAAG/C,SAAS,CAACM,SAAS,EAAE,IAAI,CAAC;MAC1C,IAAI0C,cAAc,GAAG/B,gBAAgB,CAACgC,IAAI,CAACnB,IAAI,CAAC;MAEhD,IAAI,CAACkB,cAAc,EAAE;QACnB;MACF;MAEA,IAAIE,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAACJ,cAAc,CAAC,CAAC,CAAC,CAAC;MAE9D,IAAI,CAACE,aAAa,EAAE;QAClB;MACF;MAEA,IAAIG,YAAY,GAAGjD,YAAY,CAAC8C,aAAa,EAAE5C,SAAS,CAAC;MACzD,IAAIgD,CAAC,GAAGP,SAAS,GAAGM,YAAY;MAChCC,CAAC,IAAIT,YAAY,KAAKU,SAAS,GAAGV,YAAY,GAAGD,SAAS,IAAI,CAAC;MAC/DvB,KAAK,CAACmC,SAAS,GAAG,IAAI;MACtBzD,QAAQ,CAACuD,CAAC,EAAE;QACVG,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC5BpC,KAAK,CAACmC,SAAS,GAAG,KAAK;QACzB,CAAC;QACDnB,YAAY,EAAEhB,KAAK,CAACgB;MACtB,CAAC,CAAC;IACJ,CAAC;IAEDhB,KAAK,CAACqC,WAAW,GAAG,UAAUC,IAAI,EAAE;MAClCtC,KAAK,CAACuC,OAAO,GAAGD,IAAI;IACtB,CAAC;IAEDtC,KAAK,CAACyB,oBAAoB,GAAG,UAAUhB,IAAI,EAAE;MAC3C,IAAIL,UAAU,GAAGJ,KAAK,CAACG,KAAK,CAACC,UAAU;MACvC,IAAIoC,YAAY,GAAGxC,KAAK,CAACmB,KAAK;QAC1BsB,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,gBAAgB,GAAGF,YAAY,CAACE,gBAAgB;MAEpD,IAAItC,UAAU,KAAKK,IAAI,EAAE;QACvB;MACF,CAAC,CAAC;;MAGFT,KAAK,CAAC2C,QAAQ,CAAC;QACbvC,UAAU,EAAE,OAAOsC,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACjC,IAAI,CAAC,GAAGA;MAChF,CAAC,CAAC;MAEFgC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAChC,IAAI,CAAC;IACpE,CAAC;IAEDT,KAAK,CAAC4C,YAAY,GAAG,YAAY;MAC/B,IAAI5C,KAAK,CAACmC,SAAS,EAAE;QACnB;MACF;MAEA,IAAIU,YAAY,GAAG7C,KAAK,CAACmB,KAAK;QAC1BI,SAAS,GAAGsB,YAAY,CAACtB,SAAS;QAClCuB,MAAM,GAAGD,YAAY,CAACC,MAAM;QAC5BtB,YAAY,GAAGqB,YAAY,CAACrB,YAAY;MAE5C,IAAIuB,iBAAiB,GAAG/C,KAAK,CAAC0C,gBAAgB,CAAClB,YAAY,KAAKU,SAAS,GAAGV,YAAY,GAAGD,SAAS,IAAI,CAAC,EAAEuB,MAAM,CAAC;MAElH9C,KAAK,CAACyB,oBAAoB,CAACsB,iBAAiB,CAAC;IAC/C,CAAC;IAED/C,KAAK,CAACgD,SAAS,GAAG,YAAY;MAC5B,IAAIC,qBAAqB,GAAGhF,sBAAsB,CAAC+B,KAAK,CAAC;QACrDkD,SAAS,GAAGD,qBAAqB,CAACC,SAAS;QAC3C7C,UAAU,GAAG4C,qBAAqB,CAAC5C,UAAU;MAEjD,IAAI8C,UAAU,GAAG9C,UAAU,CAAC+C,OAAO;MACnC,IAAIC,QAAQ,GAAGF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,sBAAsB,CAAC,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;MAEvJ,IAAIG,QAAQ,EAAE;QACZrD,KAAK,CAACuC,OAAO,CAACiB,KAAK,CAAC9D,GAAG,GAAG,EAAE,CAAC6D,MAAM,CAACF,QAAQ,CAAC9B,SAAS,GAAG8B,QAAQ,CAACI,YAAY,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;MACjG;IACF,CAAC;IAEDzD,KAAK,CAAC0D,uBAAuB,GAAGpF,UAAU,CAAC,UAAUmC,IAAI,EAAEkD,SAAS,EAAE;MACpE,OAAO;QACLnD,YAAY,EAAER,KAAK,CAACQ,YAAY;QAChCI,cAAc,EAAEZ,KAAK,CAACY,cAAc;QACpClC,QAAQ,EAAEsB,KAAK,CAACqB,cAAc;QAC9BjB,UAAU,EAAEK,IAAI;QAChBmD,OAAO,EAAED;MACX,CAAC;IACH,CAAC,CAAC;IACF,OAAO3D,KAAK;EACd;EAEAhC,YAAY,CAAC6B,MAAM,EAAE,CAAC;IACpBgE,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,eAAe,GAAG,IAAI,CAAChD,YAAY,CAAC,CAAC;MAC1C,IAAI,CAACiD,WAAW,GAAG1F,gBAAgB,CAAC,IAAI,CAACyF,eAAe,EAAE,QAAQ,EAAE,IAAI,CAACpB,YAAY,CAAC;MACtF,IAAI,CAACA,YAAY,CAAC,CAAC;IACrB;EACF,CAAC,EAAE;IACDiB,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASI,kBAAkBA,CAAA,EAAG;MACnC,IAAI,IAAI,CAACD,WAAW,EAAE;QACpB,IAAIE,gBAAgB,GAAG,IAAI,CAACnD,YAAY,CAAC,CAAC;QAE1C,IAAI,IAAI,CAACgD,eAAe,KAAKG,gBAAgB,EAAE;UAC7C,IAAI,CAACH,eAAe,GAAGG,gBAAgB;UACvC,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC;UACzB,IAAI,CAACH,WAAW,GAAG1F,gBAAgB,CAAC,IAAI,CAACyF,eAAe,EAAE,QAAQ,EAAE,IAAI,CAACpB,YAAY,CAAC;UACtF,IAAI,CAACA,YAAY,CAAC,CAAC;QACrB;MACF;MAEA,IAAI,CAACI,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASO,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACJ,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACG,MAAM,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,SAASpB,gBAAgBA,CAAA,EAAG;MACjC,IAAInB,SAAS,GAAGrB,SAAS,CAACf,MAAM,GAAG,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKgC,SAAS,GAAGhC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MACrF,IAAI4C,MAAM,GAAG5C,SAAS,CAACf,MAAM,GAAG,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKgC,SAAS,GAAGhC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MAClF,IAAIoE,YAAY,GAAG,EAAE;MACrB,IAAIrF,SAAS,GAAG,IAAI,CAAC+B,YAAY,CAAC,CAAC;MACnC,IAAI,CAACT,KAAK,CAACgE,OAAO,CAAC,UAAU9D,IAAI,EAAE;QACjC,IAAIkB,cAAc,GAAG/B,gBAAgB,CAACgC,IAAI,CAACnB,IAAI,CAAC+D,QAAQ,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC7C,cAAc,EAAE;UACnB;QACF;QAEA,IAAI8C,MAAM,GAAG3C,QAAQ,CAACC,cAAc,CAACJ,cAAc,CAAC,CAAC,CAAC,CAAC;QAEvD,IAAI8C,MAAM,EAAE;UACV,IAAI/E,GAAG,GAAGX,YAAY,CAAC0F,MAAM,EAAExF,SAAS,CAAC;UAEzC,IAAIS,GAAG,GAAG6B,SAAS,GAAGuB,MAAM,EAAE;YAC5BwB,YAAY,CAAC3D,IAAI,CAAC;cAChBF,IAAI,EAAEA,IAAI;cACVf,GAAG,EAAEA;YACP,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MAEF,IAAI4E,YAAY,CAACnF,MAAM,EAAE;QACvB,IAAIuF,UAAU,GAAGJ,YAAY,CAACK,MAAM,CAAC,UAAUC,IAAI,EAAEC,IAAI,EAAE;UACzD,OAAOA,IAAI,CAACnF,GAAG,GAAGkF,IAAI,CAAClF,GAAG,GAAGmF,IAAI,GAAGD,IAAI;QAC1C,CAAC,CAAC;QACF,OAAOF,UAAU,CAACjE,IAAI;MACxB;MAEA,OAAO,EAAE;IACX;EACF,CAAC,EAAE;IACDoD,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASgB,MAAMA,CAAA,EAAG;MACvB,IAAIC,SAAS,GAAG,IAAI,CAAC7D,OAAO,CAAC6D,SAAS;MACtC,IAAIC,YAAY,GAAG,IAAI,CAAC7D,KAAK;QACzB+B,SAAS,GAAG8B,YAAY,CAACC,eAAe;QACxCC,qBAAqB,GAAGF,YAAY,CAACG,SAAS;QAC9CA,SAAS,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QACzE1B,KAAK,GAAGwB,YAAY,CAACxB,KAAK;QAC1BjC,SAAS,GAAGyD,YAAY,CAACzD,SAAS;QAClC6D,KAAK,GAAGJ,YAAY,CAACI,KAAK;QAC1BC,cAAc,GAAGL,YAAY,CAACK,cAAc;QAC5CC,QAAQ,GAAGN,YAAY,CAACM,QAAQ;QAChC1B,OAAO,GAAGoB,YAAY,CAACpB,OAAO;MAClC,IAAIxD,UAAU,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU,CAAC,CAAC;MACxC;MACA;;MAEA,IAAI,CAAC8C,SAAS,GAAGA,SAAS;MAC1B,IAAIqC,QAAQ,GAAGlH,UAAU,CAAC,EAAE,CAACkF,MAAM,CAACL,SAAS,EAAE,WAAW,CAAC,EAAE;QAC3DsC,OAAO,EAAEpF;MACX,CAAC,CAAC;MACF,IAAIqF,YAAY,GAAGpH,UAAU,CAAC,EAAE,CAACkF,MAAM,CAACL,SAAS,EAAE,UAAU,CAAC,EAAEpF,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyF,MAAM,CAACL,SAAS,EAAE,MAAM,CAAC,EAAE6B,SAAS,KAAK,KAAK,CAAC,EAAEI,SAAS,CAAC;MAClJ,IAAIO,WAAW,GAAGrH,UAAU,CAAC6E,SAAS,EAAEpF,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyF,MAAM,CAACL,SAAS,EAAE,QAAQ,CAAC,EAAE,CAACkC,KAAK,IAAI,CAACC,cAAc,CAAC,CAAC;MAEvH,IAAIM,YAAY,GAAG9H,QAAQ,CAAC;QAC1B+H,SAAS,EAAErE,SAAS,GAAG,eAAe,CAACgC,MAAM,CAAChC,SAAS,EAAE,KAAK,CAAC,GAAG;MACpE,CAAC,EAAEiC,KAAK,CAAC;MAET,IAAIqC,aAAa,GAAG,aAAazH,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;QAC1DC,GAAG,EAAE,IAAI,CAAC1F,UAAU;QACpB8E,SAAS,EAAEM,YAAY;QACvBjC,KAAK,EAAEmC;MACT,CAAC,EAAE,aAAavH,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;QACzCX,SAAS,EAAEO;MACb,CAAC,EAAE,aAAatH,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;QACzCX,SAAS,EAAE,EAAE,CAAC5B,MAAM,CAACL,SAAS,EAAE,MAAM;MACxC,CAAC,EAAE,aAAa9E,KAAK,CAAC0H,aAAa,CAAC,MAAM,EAAE;QAC1CX,SAAS,EAAEI,QAAQ;QACnBQ,GAAG,EAAE,IAAI,CAAC1D;MACZ,CAAC,CAAC,CAAC,EAAEiD,QAAQ,CAAC,CAAC;MACf,IAAIU,YAAY,GAAG,IAAI,CAACtC,uBAAuB,CAACtD,UAAU,EAAEwD,OAAO,CAAC;MACpE,OAAO,aAAaxF,KAAK,CAAC0H,aAAa,CAAClH,aAAa,CAACqH,QAAQ,EAAE;QAC9DnC,KAAK,EAAEkC;MACT,CAAC,EAAE,CAACZ,KAAK,GAAGS,aAAa,GAAG,aAAazH,KAAK,CAAC0H,aAAa,CAACtH,KAAK,EAAE;QAClE+C,SAAS,EAAEA,SAAS;QACpBkD,MAAM,EAAE,IAAI,CAACzD;MACf,CAAC,EAAE6E,aAAa,CAAC,CAAC;IACpB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOhG,MAAM;AACf,CAAC,CAACzB,KAAK,CAAC8H,SAAS,CAAC;AAElBrG,MAAM,CAACsG,YAAY,GAAG;EACpBf,KAAK,EAAE,IAAI;EACXC,cAAc,EAAE;AAClB,CAAC;AACDxF,MAAM,CAACuG,WAAW,GAAG3H,aAAa;AAClC,IAAI4H,QAAQ,GAAG,aAAajI,KAAK,CAACkI,UAAU,CAAC,UAAUnF,KAAK,EAAE4E,GAAG,EAAE;EACjE,IAAIQ,kBAAkB,GAAGpF,KAAK,CAAC+B,SAAS;EAExC,IAAIsD,iBAAiB,GAAGpI,KAAK,CAACqI,UAAU,CAAChI,aAAa,CAAC;IACnDiI,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIzB,eAAe,GAAGyB,YAAY,CAAC,QAAQ,EAAEH,kBAAkB,CAAC;EAEhE,IAAII,WAAW,GAAG9I,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsD,KAAK,CAAC,EAAE;IAC9C8D,eAAe,EAAEA;EACnB,CAAC,CAAC;EAEF,OAAO,aAAa7G,KAAK,CAAC0H,aAAa,CAACjG,MAAM,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAE8I,WAAW,EAAE;IACxEZ,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAeM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}