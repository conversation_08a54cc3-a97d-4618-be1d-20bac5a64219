"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseDirective = void 0;
const brace_patterns_js_1 = require("./brace-patterns.js");
/**
 * Parse a "directive", the bit that follows the `#` character
 * on a TestPoint line.
 */
const parseDirective = (line) => {
    if (!line.trim())
        return false;
    line = line
        .replace(brace_patterns_js_1.OPEN_BRACE_EOL, '')
        .trim()
        .replace(/^duration_ms ([0-9.]+)$/, 'time=$1ms');
    const time = line.match(/^time=((?:[1-9][0-9]*|0)(?:\.[0-9]+)?)(ms|s)$/i);
    const t = time?.[1];
    const s = time?.[2];
    if (typeof t === 'string') {
        let n = +t;
        if (s === 's') {
            // JS does weird things with floats.  Round it off a bit.
            n *= 1000000;
            n = Math.round(n);
            n /= 1000;
        }
        return ['time', n];
    }
    const ts = line.match(/^(todo|skip)(?:\S*)\b(.*)$/i);
    const type = ts?.[1]?.toLowerCase();
    const msg = ts?.[2]?.trim();
    if (!type)
        return false;
    // we know at this point it must be either 'todo' or 'skip',
    // in unknown upper/lower case
    return [type, msg || true];
};
exports.parseDirective = parseDirective;
//# sourceMappingURL=parse-directive.js.map