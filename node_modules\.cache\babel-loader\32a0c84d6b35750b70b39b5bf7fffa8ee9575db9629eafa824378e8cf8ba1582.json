{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\modificaGuiComposition.jsx\";\nimport React, { Fragment, Component } from 'react';\nimport { Checkbox } from 'primereact/checkbox';\nimport { But<PERSON> } from 'primereact/button';\nimport { <PERSON>nti } from '../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport menu from '../components/generalizzazioni/menu.json';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass ModificaGUIComposition extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      views: [],\n      results: null\n    };\n    this.onStateChange = this.onStateChange.bind(this);\n    this.defineChecbox = this.defineChecbox.bind(this);\n    this.Invia = this.Invia.bind(this);\n  }\n  async componentDidMount() {\n    var url = 'inhibition/?idUser=' + this.props.results.id;\n    await APIRequest('GET', url).then(res => {\n      if (res.data !== '') {\n        var guiMenu = menu[this.props.results.role.toLowerCase()];\n        if (Object.keys(res.data.inhibition).length < Object.keys(guiMenu).length) {\n          var keys = Object.keys(guiMenu).filter(el => !Object.keys(res.data.inhibition).includes(el));\n          keys.forEach(element => {\n            res.data.inhibition[element] = guiMenu[element];\n          });\n        }\n        this.setState({\n          results: res.data\n        });\n      } else {\n        var gui = [];\n        Object.entries(menu).forEach(items => {\n          if (items[0] === this.props.results.role.toLowerCase()) {\n            Object.entries(items[1]).forEach(el => {\n              if (el[0] === 'Ordini' || el[0] === 'Approvvigionamento') {\n                Object.entries(el[1]).forEach(it => {\n                  if (it[0] === 'listinoProdotti' || it[0] === 'CreaOrdine') {\n                    it[1].previsioneAcquisti = {\n                      visibility: 'false'\n                    };\n                    it[1].previsioneAcquistiCli = {\n                      visibility: 'false'\n                    };\n                  }\n                });\n              }\n            });\n            gui.inhibition = items[1];\n            gui.userId = this.props.results.id;\n          }\n        });\n        this.setState({\n          results: gui\n        });\n      }\n    });\n    stopLoading();\n\n    /*\n    It's a way to add a button to the modal footer.\n    The button is added to the modal footer, but it's not visible.\n    The button is visible only when the modal is opened.\n    The button is hidden when the modal is closed.\n     */\n    var saveGUIBtn = document.getElementById('saveGUImod');\n    if (saveGUIBtn !== undefined) {\n      var footerModal = document.querySelector(\".confirmBtn\");\n      if (saveGUIBtn !== null) {\n        footerModal.parentNode.insertBefore(saveGUIBtn, footerModal.nextSibling);\n      }\n    }\n    this.defineChecbox();\n  }\n  componentDidUpdate() {\n    let bodyDisabled = document.getElementsByClassName('gui-area-head');\n    var filter = Object.values(bodyDisabled).filter(element => element.children[0].children[1].ariaChecked !== 'true');\n    filter.forEach(el => {\n      el.nextSibling.classList.add('body-disabled');\n    });\n  }\n  onStateChange(e, element) {\n    var arr = [];\n    let bodyDisabled = document.getElementsByClassName('gui-area-head');\n    var trova = Object.values(bodyDisabled).find(items => items.innerText === e.target.name);\n    bodyDisabled = trova;\n    if (element.visibility === \"true\") {\n      element.visibility = \"false\";\n      bodyDisabled.nextSibling.classList.add('body-disabled');\n    } else {\n      element.visibility = \"true\";\n      bodyDisabled.nextSibling.classList.remove('body-disabled');\n    }\n    this.setState({\n      views: arr\n    });\n    this.defineChecbox();\n  }\n  onStateChange2(e, element) {\n    var arr = [];\n    if (element.visibility === \"true\") {\n      element.visibility = \"false\";\n    } else {\n      element.visibility = \"true\";\n    }\n    this.setState({\n      views: arr\n    });\n    this.defineChecbox();\n  }\n  defineChecbox() {\n    var arr = [];\n    if (this.state.results.inhibition !== undefined) {\n      arr.push(Object.entries(this.state.results.inhibition).map((element, index) => {\n        return /*#__PURE__*/_jsxDEV(Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row px-3 py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 gui-father border\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-12 gui-area-head py-2 d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                    inputId: element[0],\n                    name: element[0],\n                    value: element[1].visibility,\n                    onChange: e => this.onStateChange(e, element[1]),\n                    checked: element[1].visibility !== 'false'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"ml-3 mb-0\",\n                    htmlFor: element[0],\n                    children: element[0]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-sitemap opacity-1 ml-auto\",\n                    style: {\n                      'fontSize': '1.5em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-12 gui-area-body py-3 d-flex align-content-start flex-wrap\",\n                  children: Object.entries(element[1]).map((item, base) => {\n                    if (item[0] !== 'visibility' && item[0] !== 'iconMain') {\n                      return /*#__PURE__*/_jsxDEV(Fragment, {\n                        children: /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"gui-sons d-flex align-items-center mb-3 mr-3 px-3 py-1\",\n                            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                              inputId: item[0],\n                              name: item[0],\n                              value: item[1].visibility,\n                              onChange: e => this.onStateChange2(e, item[1]),\n                              checked: item[1].visibility !== 'false'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 136,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                              className: \"ml-2 mb-0\",\n                              htmlFor: item[0],\n                              children: item[0]\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 137,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 135,\n                            columnNumber: 69\n                          }, this), (item[0] === 'listinoProdotti' || item[0] === 'CreaOrdine') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: Object.entries(item[1]).map((it, key) => {\n                              if (it[0] === 'previsioneAcquisti' || it[0] === 'previsioneAcquistiCli') {\n                                return /*#__PURE__*/_jsxDEV(Fragment, {\n                                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"gui-sons d-flex align-items-center mb-3 mr-3 px-3 py-1\",\n                                    children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                                      inputId: it[0],\n                                      name: it[0],\n                                      value: it[1].visibility,\n                                      onChange: e => this.onStateChange2(e, it[1]),\n                                      checked: it[1].visibility !== 'false'\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 146,\n                                      columnNumber: 97\n                                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                      className: \"ml-2 mb-0\",\n                                      htmlFor: it[0],\n                                      children: it[0]\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 147,\n                                      columnNumber: 97\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 145,\n                                    columnNumber: 93\n                                  }, this)\n                                }, key, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 144,\n                                  columnNumber: 89\n                                }, this);\n                              }\n                              return /*#__PURE__*/_jsxDEV(\"span\", {}, key, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 152,\n                                columnNumber: 88\n                              }, this);\n                            })\n                          }, void 0, false)]\n                        }, void 0, true)\n                      }, base, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 133,\n                        columnNumber: 61\n                      }, this);\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"span\", {}, base, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 60\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 29\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 25\n        }, this);\n      }));\n    } else {\n      arr.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"GUInotAvailable\",\n          className: \"col-12 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"big\", {\n              children: Costanti.GUInotAvailable\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this));\n    }\n    this.setState({\n      views: arr\n    });\n  }\n  async Invia() {\n    var url = 'inhibition/?idUser=' + this.state.results.userId;\n    var corpo = {\n      inhibition: this.state.results.inhibition\n    };\n    //Chiamata axios per la modifica della GUI composition\n    await APIRequest('POST', url, corpo).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"La GUI è stata reimpostata con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile reimpostare la GUI. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  render() {\n    var _this$state$results;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modalBody\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this), this.state.views.map((item, index) => {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: item\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 29\n        }, this);\n      }), ((_this$state$results = this.state.results) === null || _this$state$results === void 0 ? void 0 : _this$state$results.inhibition) !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 d-flex justify-content-end w-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '220px'\n          },\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            id: \"saveGUImod\",\n            className: \"p-button-text\",\n            onClick: this.Invia,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-check mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 33\n            }, this), Costanti.Conferma]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default ModificaGUIComposition;", "map": {"version": 3, "names": ["React", "Fragment", "Component", "Checkbox", "<PERSON><PERSON>", "<PERSON><PERSON>", "Toast", "APIRequest", "stopLoading", "menu", "jsxDEV", "_jsxDEV", "_Fragment", "ModificaGUIComposition", "constructor", "props", "state", "views", "results", "onStateChange", "bind", "defineChecbox", "Invia", "componentDidMount", "url", "id", "then", "res", "data", "guiMenu", "role", "toLowerCase", "Object", "keys", "inhibition", "length", "filter", "el", "includes", "for<PERSON>ach", "element", "setState", "gui", "entries", "items", "it", "previsioneAcquisti", "visibility", "previsioneAcquistiCli", "userId", "saveGUIBtn", "document", "getElementById", "undefined", "footerModal", "querySelector", "parentNode", "insertBefore", "nextS<PERSON>ling", "componentDidUpdate", "bodyDisabled", "getElementsByClassName", "values", "children", "ariaChe<PERSON>", "classList", "add", "e", "arr", "trova", "find", "innerText", "target", "name", "remove", "onStateChange2", "push", "map", "index", "className", "inputId", "value", "onChange", "checked", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "style", "item", "base", "key", "GUInotAvailable", "corpo", "console", "log", "toast", "show", "severity", "summary", "detail", "life", "setTimeout", "window", "location", "reload", "catch", "_e$response", "_e$response2", "concat", "response", "message", "render", "_this$state$results", "ref", "max<PERSON><PERSON><PERSON>", "onClick", "Conferma"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/modificaGuiComposition.jsx"], "sourcesContent": ["import React, { Fragment, Component } from 'react';\nimport { Checkbox } from 'primereact/checkbox';\nimport { <PERSON><PERSON> } from 'primereact/button';\nimport { <PERSON><PERSON> } from '../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport menu from '../components/generalizzazioni/menu.json';\n\nclass ModificaGUIComposition extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            views: [],\n            results: null\n        }\n        this.onStateChange = this.onStateChange.bind(this);\n        this.defineChecbox = this.defineChecbox.bind(this);\n        this.Invia = this.Invia.bind(this);\n    }\n    async componentDidMount() {\n\n        var url = 'inhibition/?idUser=' + this.props.results.id\n        await APIRequest('GET', url)\n            .then(res => {\n                if (res.data !== '') {\n                    var guiMenu = menu[this.props.results.role.toLowerCase()]\n                    if (Object.keys(res.data.inhibition).length < Object.keys(guiMenu).length) {\n                        var keys = Object.keys(guiMenu).filter(el => !Object.keys(res.data.inhibition).includes(el))\n                        keys.forEach(element => {\n                            res.data.inhibition[element] = guiMenu[element]\n                        })\n                    }\n                    this.setState({\n                        results: res.data\n                    })\n                } else {\n                    var gui = []\n                    Object.entries(menu).forEach(items => {\n                        if (items[0] === this.props.results.role.toLowerCase()) {\n                            Object.entries(items[1]).forEach(el => {\n                                if(el[0] === 'Ordini' || el[0] === 'Approvvigionamento'){\n                                    Object.entries(el[1]).forEach(it => {\n                                        if(it[0] === 'listinoProdotti' || it[0] === 'CreaOrdine'){\n                                            it[1].previsioneAcquisti = {visibility: 'false'}\n                                            it[1].previsioneAcquistiCli = {visibility: 'false'}\n                                        }\n                                    })\n                                }\n                            })\n                            gui.inhibition = items[1]\n                            gui.userId = this.props.results.id\n                        }\n                    })\n                    this.setState({\n                        results: gui\n                    })\n                }\n            })\n        stopLoading()\n\n        /*\n        It's a way to add a button to the modal footer.\n        The button is added to the modal footer, but it's not visible.\n        The button is visible only when the modal is opened.\n        The button is hidden when the modal is closed.\n         */\n        var saveGUIBtn = document.getElementById('saveGUImod');\n        if (saveGUIBtn !== undefined) {\n            var footerModal = document.querySelector(\".confirmBtn\")\n            if (saveGUIBtn !== null) {\n                footerModal.parentNode.insertBefore(saveGUIBtn, footerModal.nextSibling)\n            }\n        }\n        this.defineChecbox()\n    }\n    componentDidUpdate() {\n        let bodyDisabled = document.getElementsByClassName('gui-area-head');\n        var filter = Object.values(bodyDisabled).filter(element => element.children[0].children[1].ariaChecked !== 'true')\n        filter.forEach(el => {\n            el.nextSibling.classList.add('body-disabled')\n        })\n    }\n    onStateChange(e, element) {\n        var arr = [];\n        let bodyDisabled = document.getElementsByClassName('gui-area-head');\n        var trova = Object.values(bodyDisabled).find(items => items.innerText === e.target.name)\n        bodyDisabled = trova\n        if (element.visibility === \"true\") {\n            element.visibility = \"false\";\n            bodyDisabled.nextSibling.classList.add('body-disabled')\n        } else {\n            element.visibility = \"true\";\n            bodyDisabled.nextSibling.classList.remove('body-disabled')\n        }\n        this.setState({\n            views: arr\n        })\n        this.defineChecbox()\n    }\n    onStateChange2(e, element) {\n        var arr = [];\n        if (element.visibility === \"true\") {\n            element.visibility = \"false\";\n        } else {\n            element.visibility = \"true\";\n        }\n        this.setState({\n            views: arr\n        })\n        this.defineChecbox()\n    }\n    defineChecbox() {\n        var arr = [];\n        if (this.state.results.inhibition !== undefined) {\n            arr.push(\n                Object.entries(this.state.results.inhibition).map((element, index) => {\n                    return (\n                        <Fragment key={index}>\n                            <div className='row px-3 py-3'>\n                                <div className='col-12 gui-father border'>\n                                    <div className='row'>\n                                        <div className='col-12 gui-area-head py-2 d-flex align-items-center'>\n                                            <Checkbox inputId={element[0]} name={element[0]} value={element[1].visibility} onChange={(e) => this.onStateChange(e, element[1])} checked={element[1].visibility !== 'false'} />\n                                            <label className='ml-3 mb-0' htmlFor={element[0]}>{element[0]}</label>\n                                            <i className=\"pi pi-sitemap opacity-1 ml-auto\" style={{ 'fontSize': '1.5em' }}></i>\n                                        </div>\n                                        <div className='col-12 gui-area-body py-3 d-flex align-content-start flex-wrap'>\n                                            {\n                                                Object.entries(element[1]).map((item, base) => {\n                                                    if (item[0] !== 'visibility' && item[0] !== 'iconMain') {\n                                                        return (\n                                                            <Fragment key={base}>\n                                                                <>\n                                                                    <div className='gui-sons d-flex align-items-center mb-3 mr-3 px-3 py-1'>\n                                                                        <Checkbox inputId={item[0]} name={item[0]} value={item[1].visibility} onChange={(e) => this.onStateChange2(e, item[1])} checked={item[1].visibility !== 'false'} />\n                                                                        <label className='ml-2 mb-0' htmlFor={item[0]}>{item[0]}</label>\n                                                                    </div>\n                                                                    {(item[0] === 'listinoProdotti' || item[0] === 'CreaOrdine') &&\n                                                                        <>\n                                                                            {Object.entries(item[1]).map((it, key) => {\n                                                                                if (it[0] === 'previsioneAcquisti' || it[0] === 'previsioneAcquistiCli') {\n                                                                                    return (\n                                                                                        <Fragment key={key}>\n                                                                                            <div className='gui-sons d-flex align-items-center mb-3 mr-3 px-3 py-1'>\n                                                                                                <Checkbox inputId={it[0]} name={it[0]} value={it[1].visibility} onChange={(e) => this.onStateChange2(e, it[1])} checked={it[1].visibility !== 'false'} />\n                                                                                                <label className='ml-2 mb-0' htmlFor={it[0]}>{it[0]}</label>\n                                                                                            </div>\n                                                                                        </Fragment>\n                                                                                    )\n                                                                                }\n                                                                                return <span key={key}></span>\n                                                                            })\n                                                                            }\n                                                                        </>\n                                                                    }\n                                                                </>\n                                                            </Fragment>\n                                                        )\n                                                    }\n                                                    return <span key={base}></span>\n                                                })\n                                            }\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </Fragment>\n                    )\n                })\n            )\n        } else {\n            arr.push(\n                <div className='row'>\n                    <div id='GUInotAvailable' className='col-12 text-center'>\n                        <p className='mb-0'><big>{Costanti.GUInotAvailable}</big></p>\n                    </div>\n                </div>\n            )\n        }\n        this.setState({\n            views: arr\n        });\n    }\n    async Invia() {\n        var url = 'inhibition/?idUser=' + this.state.results.userId\n        var corpo = {\n            inhibition: this.state.results.inhibition\n        }\n        //Chiamata axios per la modifica della GUI composition\n        await APIRequest('POST', url, corpo)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"La GUI è stata reimpostata con successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile reimpostare la GUI. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n\n    }\n    render() {\n        return (\n            <div className=\"modalBody\" >\n                < Toast ref={(el) => this.toast = el} />\n                {\n                    this.state.views.map((item, index) => {\n                        return (\n                            <div key={index} >\n                                {item}\n                            </div>\n                        );\n                    })\n                }\n                {this.state.results?.inhibition !== undefined &&\n                    <div className='col-12 d-flex justify-content-end w-100' >\n                        <div style={{ maxWidth: '220px' }} className='mt-4'>\n                            <Button\n                                id=\"saveGUImod\"\n                                className=\"p-button-text\"\n                                onClick={this.Invia}\n                            >\n                                <i className='pi pi-check mr-2'></i>\n                                {Costanti.Conferma}\n                            </Button>\n                        </div>\n                    </div>\n                }\n            </div >\n        )\n    }\n}\nexport default ModificaGUIComposition;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,WAAW,QAAQ,4CAA4C;AACxE,OAAOC,IAAI,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAV,QAAA,IAAAW,SAAA;AAE5D,MAAMC,sBAAsB,SAASX,SAAS,CAAC;EAC3CY,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACE,KAAK,GAAG,IAAI,CAACA,KAAK,CAACF,IAAI,CAAC,IAAI,CAAC;EACtC;EACA,MAAMG,iBAAiBA,CAAA,EAAG;IAEtB,IAAIC,GAAG,GAAG,qBAAqB,GAAG,IAAI,CAACT,KAAK,CAACG,OAAO,CAACO,EAAE;IACvD,MAAMlB,UAAU,CAAC,KAAK,EAAEiB,GAAG,CAAC,CACvBE,IAAI,CAACC,GAAG,IAAI;MACT,IAAIA,GAAG,CAACC,IAAI,KAAK,EAAE,EAAE;QACjB,IAAIC,OAAO,GAAGpB,IAAI,CAAC,IAAI,CAACM,KAAK,CAACG,OAAO,CAACY,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;QACzD,IAAIC,MAAM,CAACC,IAAI,CAACN,GAAG,CAACC,IAAI,CAACM,UAAU,CAAC,CAACC,MAAM,GAAGH,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACM,MAAM,EAAE;UACvE,IAAIF,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACO,MAAM,CAACC,EAAE,IAAI,CAACL,MAAM,CAACC,IAAI,CAACN,GAAG,CAACC,IAAI,CAACM,UAAU,CAAC,CAACI,QAAQ,CAACD,EAAE,CAAC,CAAC;UAC5FJ,IAAI,CAACM,OAAO,CAACC,OAAO,IAAI;YACpBb,GAAG,CAACC,IAAI,CAACM,UAAU,CAACM,OAAO,CAAC,GAAGX,OAAO,CAACW,OAAO,CAAC;UACnD,CAAC,CAAC;QACN;QACA,IAAI,CAACC,QAAQ,CAAC;UACVvB,OAAO,EAAES,GAAG,CAACC;QACjB,CAAC,CAAC;MACN,CAAC,MAAM;QACH,IAAIc,GAAG,GAAG,EAAE;QACZV,MAAM,CAACW,OAAO,CAAClC,IAAI,CAAC,CAAC8B,OAAO,CAACK,KAAK,IAAI;UAClC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC7B,KAAK,CAACG,OAAO,CAACY,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;YACpDC,MAAM,CAACW,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,OAAO,CAACF,EAAE,IAAI;cACnC,IAAGA,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,oBAAoB,EAAC;gBACpDL,MAAM,CAACW,OAAO,CAACN,EAAE,CAAC,CAAC,CAAC,CAAC,CAACE,OAAO,CAACM,EAAE,IAAI;kBAChC,IAAGA,EAAE,CAAC,CAAC,CAAC,KAAK,iBAAiB,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,YAAY,EAAC;oBACrDA,EAAE,CAAC,CAAC,CAAC,CAACC,kBAAkB,GAAG;sBAACC,UAAU,EAAE;oBAAO,CAAC;oBAChDF,EAAE,CAAC,CAAC,CAAC,CAACG,qBAAqB,GAAG;sBAACD,UAAU,EAAE;oBAAO,CAAC;kBACvD;gBACJ,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;YACFL,GAAG,CAACR,UAAU,GAAGU,KAAK,CAAC,CAAC,CAAC;YACzBF,GAAG,CAACO,MAAM,GAAG,IAAI,CAAClC,KAAK,CAACG,OAAO,CAACO,EAAE;UACtC;QACJ,CAAC,CAAC;QACF,IAAI,CAACgB,QAAQ,CAAC;UACVvB,OAAO,EAAEwB;QACb,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACNlC,WAAW,CAAC,CAAC;;IAEb;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI0C,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;IACtD,IAAIF,UAAU,KAAKG,SAAS,EAAE;MAC1B,IAAIC,WAAW,GAAGH,QAAQ,CAACI,aAAa,CAAC,aAAa,CAAC;MACvD,IAAIL,UAAU,KAAK,IAAI,EAAE;QACrBI,WAAW,CAACE,UAAU,CAACC,YAAY,CAACP,UAAU,EAAEI,WAAW,CAACI,WAAW,CAAC;MAC5E;IACJ;IACA,IAAI,CAACrC,aAAa,CAAC,CAAC;EACxB;EACAsC,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,YAAY,GAAGT,QAAQ,CAACU,sBAAsB,CAAC,eAAe,CAAC;IACnE,IAAIzB,MAAM,GAAGJ,MAAM,CAAC8B,MAAM,CAACF,YAAY,CAAC,CAACxB,MAAM,CAACI,OAAO,IAAIA,OAAO,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,KAAK,MAAM,CAAC;IAClH5B,MAAM,CAACG,OAAO,CAACF,EAAE,IAAI;MACjBA,EAAE,CAACqB,WAAW,CAACO,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IACjD,CAAC,CAAC;EACN;EACA/C,aAAaA,CAACgD,CAAC,EAAE3B,OAAO,EAAE;IACtB,IAAI4B,GAAG,GAAG,EAAE;IACZ,IAAIR,YAAY,GAAGT,QAAQ,CAACU,sBAAsB,CAAC,eAAe,CAAC;IACnE,IAAIQ,KAAK,GAAGrC,MAAM,CAAC8B,MAAM,CAACF,YAAY,CAAC,CAACU,IAAI,CAAC1B,KAAK,IAAIA,KAAK,CAAC2B,SAAS,KAAKJ,CAAC,CAACK,MAAM,CAACC,IAAI,CAAC;IACxFb,YAAY,GAAGS,KAAK;IACpB,IAAI7B,OAAO,CAACO,UAAU,KAAK,MAAM,EAAE;MAC/BP,OAAO,CAACO,UAAU,GAAG,OAAO;MAC5Ba,YAAY,CAACF,WAAW,CAACO,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAC3D,CAAC,MAAM;MACH1B,OAAO,CAACO,UAAU,GAAG,MAAM;MAC3Ba,YAAY,CAACF,WAAW,CAACO,SAAS,CAACS,MAAM,CAAC,eAAe,CAAC;IAC9D;IACA,IAAI,CAACjC,QAAQ,CAAC;MACVxB,KAAK,EAAEmD;IACX,CAAC,CAAC;IACF,IAAI,CAAC/C,aAAa,CAAC,CAAC;EACxB;EACAsD,cAAcA,CAACR,CAAC,EAAE3B,OAAO,EAAE;IACvB,IAAI4B,GAAG,GAAG,EAAE;IACZ,IAAI5B,OAAO,CAACO,UAAU,KAAK,MAAM,EAAE;MAC/BP,OAAO,CAACO,UAAU,GAAG,OAAO;IAChC,CAAC,MAAM;MACHP,OAAO,CAACO,UAAU,GAAG,MAAM;IAC/B;IACA,IAAI,CAACN,QAAQ,CAAC;MACVxB,KAAK,EAAEmD;IACX,CAAC,CAAC;IACF,IAAI,CAAC/C,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI+C,GAAG,GAAG,EAAE;IACZ,IAAI,IAAI,CAACpD,KAAK,CAACE,OAAO,CAACgB,UAAU,KAAKmB,SAAS,EAAE;MAC7Ce,GAAG,CAACQ,IAAI,CACJ5C,MAAM,CAACW,OAAO,CAAC,IAAI,CAAC3B,KAAK,CAACE,OAAO,CAACgB,UAAU,CAAC,CAAC2C,GAAG,CAAC,CAACrC,OAAO,EAAEsC,KAAK,KAAK;QAClE,oBACInE,OAAA,CAACV,QAAQ;UAAA8D,QAAA,eACLpD,OAAA;YAAKoE,SAAS,EAAC,eAAe;YAAAhB,QAAA,eAC1BpD,OAAA;cAAKoE,SAAS,EAAC,0BAA0B;cAAAhB,QAAA,eACrCpD,OAAA;gBAAKoE,SAAS,EAAC,KAAK;gBAAAhB,QAAA,gBAChBpD,OAAA;kBAAKoE,SAAS,EAAC,qDAAqD;kBAAAhB,QAAA,gBAChEpD,OAAA,CAACR,QAAQ;oBAAC6E,OAAO,EAAExC,OAAO,CAAC,CAAC,CAAE;oBAACiC,IAAI,EAAEjC,OAAO,CAAC,CAAC,CAAE;oBAACyC,KAAK,EAAEzC,OAAO,CAAC,CAAC,CAAC,CAACO,UAAW;oBAACmC,QAAQ,EAAGf,CAAC,IAAK,IAAI,CAAChD,aAAa,CAACgD,CAAC,EAAE3B,OAAO,CAAC,CAAC,CAAC,CAAE;oBAAC2C,OAAO,EAAE3C,OAAO,CAAC,CAAC,CAAC,CAACO,UAAU,KAAK;kBAAQ;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjL5E,OAAA;oBAAOoE,SAAS,EAAC,WAAW;oBAACS,OAAO,EAAEhD,OAAO,CAAC,CAAC,CAAE;oBAAAuB,QAAA,EAAEvB,OAAO,CAAC,CAAC;kBAAC;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtE5E,OAAA;oBAAGoE,SAAS,EAAC,iCAAiC;oBAACU,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAQ;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACN5E,OAAA;kBAAKoE,SAAS,EAAC,gEAAgE;kBAAAhB,QAAA,EAEvE/B,MAAM,CAACW,OAAO,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC,CAACqC,GAAG,CAAC,CAACa,IAAI,EAAEC,IAAI,KAAK;oBAC3C,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,YAAY,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;sBACpD,oBACI/E,OAAA,CAACV,QAAQ;wBAAA8D,QAAA,eACLpD,OAAA,CAAAC,SAAA;0BAAAmD,QAAA,gBACIpD,OAAA;4BAAKoE,SAAS,EAAC,wDAAwD;4BAAAhB,QAAA,gBACnEpD,OAAA,CAACR,QAAQ;8BAAC6E,OAAO,EAAEU,IAAI,CAAC,CAAC,CAAE;8BAACjB,IAAI,EAAEiB,IAAI,CAAC,CAAC,CAAE;8BAACT,KAAK,EAAES,IAAI,CAAC,CAAC,CAAC,CAAC3C,UAAW;8BAACmC,QAAQ,EAAGf,CAAC,IAAK,IAAI,CAACQ,cAAc,CAACR,CAAC,EAAEuB,IAAI,CAAC,CAAC,CAAC,CAAE;8BAACP,OAAO,EAAEO,IAAI,CAAC,CAAC,CAAC,CAAC3C,UAAU,KAAK;4BAAQ;8BAAAqC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACnK5E,OAAA;8BAAOoE,SAAS,EAAC,WAAW;8BAACS,OAAO,EAAEE,IAAI,CAAC,CAAC,CAAE;8BAAA3B,QAAA,EAAE2B,IAAI,CAAC,CAAC;4BAAC;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/D,CAAC,EACL,CAACG,IAAI,CAAC,CAAC,CAAC,KAAK,iBAAiB,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,YAAY,kBACvD/E,OAAA,CAAAC,SAAA;4BAAAmD,QAAA,EACK/B,MAAM,CAACW,OAAO,CAAC+C,IAAI,CAAC,CAAC,CAAC,CAAC,CAACb,GAAG,CAAC,CAAChC,EAAE,EAAE+C,GAAG,KAAK;8BACtC,IAAI/C,EAAE,CAAC,CAAC,CAAC,KAAK,oBAAoB,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,uBAAuB,EAAE;gCACrE,oBACIlC,OAAA,CAACV,QAAQ;kCAAA8D,QAAA,eACLpD,OAAA;oCAAKoE,SAAS,EAAC,wDAAwD;oCAAAhB,QAAA,gBACnEpD,OAAA,CAACR,QAAQ;sCAAC6E,OAAO,EAAEnC,EAAE,CAAC,CAAC,CAAE;sCAAC4B,IAAI,EAAE5B,EAAE,CAAC,CAAC,CAAE;sCAACoC,KAAK,EAAEpC,EAAE,CAAC,CAAC,CAAC,CAACE,UAAW;sCAACmC,QAAQ,EAAGf,CAAC,IAAK,IAAI,CAACQ,cAAc,CAACR,CAAC,EAAEtB,EAAE,CAAC,CAAC,CAAC,CAAE;sCAACsC,OAAO,EAAEtC,EAAE,CAAC,CAAC,CAAC,CAACE,UAAU,KAAK;oCAAQ;sCAAAqC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC,eACzJ5E,OAAA;sCAAOoE,SAAS,EAAC,WAAW;sCAACS,OAAO,EAAE3C,EAAE,CAAC,CAAC,CAAE;sCAAAkB,QAAA,EAAElB,EAAE,CAAC,CAAC;oCAAC;sCAAAuC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC3D;gCAAC,GAJKK,GAAG;kCAAAR,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAKR,CAAC;8BAEnB;8BACA,oBAAO5E,OAAA,aAAWiF,GAAG;gCAAAR,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAClC,CAAC;0BAAC,gBAEJ,CAAC;wBAAA,eAET;sBAAC,GAxBQI,IAAI;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAyBT,CAAC;oBAEnB;oBACA,oBAAO5E,OAAA,aAAWgF,IAAI;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBACnC,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GAhDKT,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiDV,CAAC;MAEnB,CAAC,CACL,CAAC;IACL,CAAC,MAAM;MACHnB,GAAG,CAACQ,IAAI,cACJjE,OAAA;QAAKoE,SAAS,EAAC,KAAK;QAAAhB,QAAA,eAChBpD,OAAA;UAAKc,EAAE,EAAC,iBAAiB;UAACsD,SAAS,EAAC,oBAAoB;UAAAhB,QAAA,eACpDpD,OAAA;YAAGoE,SAAS,EAAC,MAAM;YAAAhB,QAAA,eAACpD,OAAA;cAAAoD,QAAA,EAAM1D,QAAQ,CAACwF;YAAe;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACT,CAAC;IACL;IACA,IAAI,CAAC9C,QAAQ,CAAC;MACVxB,KAAK,EAAEmD;IACX,CAAC,CAAC;EACN;EACA,MAAM9C,KAAKA,CAAA,EAAG;IACV,IAAIE,GAAG,GAAG,qBAAqB,GAAG,IAAI,CAACR,KAAK,CAACE,OAAO,CAAC+B,MAAM;IAC3D,IAAI6C,KAAK,GAAG;MACR5D,UAAU,EAAE,IAAI,CAAClB,KAAK,CAACE,OAAO,CAACgB;IACnC,CAAC;IACD;IACA,MAAM3B,UAAU,CAAC,MAAM,EAAEiB,GAAG,EAAEsE,KAAK,CAAC,CAC/BpE,IAAI,CAACC,GAAG,IAAI;MACToE,OAAO,CAACC,GAAG,CAACrE,GAAG,CAACC,IAAI,CAAC;MACrB,IAAI,CAACqE,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,yCAAyC;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MAC1HC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAACC,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAAyC,WAAA,EAAAC,YAAA;MACZd,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,oEAAAS,MAAA,CAAiE,EAAAF,WAAA,GAAAzC,CAAC,CAAC4C,QAAQ,cAAAH,WAAA,uBAAVA,WAAA,CAAYhF,IAAI,MAAKyB,SAAS,IAAAwD,YAAA,GAAG1C,CAAC,CAAC4C,QAAQ,cAAAF,YAAA,uBAAVA,YAAA,CAAYjF,IAAI,GAAGuC,CAAC,CAAC6C,OAAO,CAAE;QAAEV,IAAI,EAAE;MAAK,CAAC,CAAC;IAC1N,CAAC,CAAC;EAEV;EACAW,MAAMA,CAAA,EAAG;IAAA,IAAAC,mBAAA;IACL,oBACIvG,OAAA;MAAKoE,SAAS,EAAC,WAAW;MAAAhB,QAAA,gBACtBpD,OAAA,CAAEL,KAAK;QAAC6G,GAAG,EAAG9E,EAAE,IAAK,IAAI,CAAC4D,KAAK,GAAG5D;MAAG;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEpC,IAAI,CAACvE,KAAK,CAACC,KAAK,CAAC4D,GAAG,CAAC,CAACa,IAAI,EAAEZ,KAAK,KAAK;QAClC,oBACInE,OAAA;UAAAoD,QAAA,EACK2B;QAAI,GADCZ,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAEd,CAAC,CAAC,EAEL,EAAA2B,mBAAA,OAAI,CAAClG,KAAK,CAACE,OAAO,cAAAgG,mBAAA,uBAAlBA,mBAAA,CAAoBhF,UAAU,MAAKmB,SAAS,iBACzC1C,OAAA;QAAKoE,SAAS,EAAC,yCAAyC;QAAAhB,QAAA,eACpDpD,OAAA;UAAK8E,KAAK,EAAE;YAAE2B,QAAQ,EAAE;UAAQ,CAAE;UAACrC,SAAS,EAAC,MAAM;UAAAhB,QAAA,eAC/CpD,OAAA,CAACP,MAAM;YACHqB,EAAE,EAAC,YAAY;YACfsD,SAAS,EAAC,eAAe;YACzBsC,OAAO,EAAE,IAAI,CAAC/F,KAAM;YAAAyC,QAAA,gBAEpBpD,OAAA;cAAGoE,SAAS,EAAC;YAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACnClF,QAAQ,CAACiH,QAAQ;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAER,CAAC;EAEf;AACJ;AACA,eAAe1E,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}