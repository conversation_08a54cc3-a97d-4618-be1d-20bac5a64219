{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"initialI18nStore\", \"initialLanguage\"];\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nimport React from 'react';\nimport { useSSR } from './useSSR';\nimport { composeInitialProps } from './context';\nimport { getDisplayName } from './utils';\nexport function withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      var initialI18nStore = _ref.initialI18nStore,\n        initialLanguage = _ref.initialLanguage,\n        rest = _objectWithoutProperties(_ref, _excluded);\n      useSSR(initialI18nStore, initialLanguage);\n      return React.createElement(WrappedComponent, _objectSpread({}, rest));\n    }\n    I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n    I18nextWithSSR.displayName = \"withI18nextSSR(\".concat(getDisplayName(WrappedComponent), \")\");\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}", "map": {"version": 3, "names": ["_defineProperty", "_objectWithoutProperties", "_excluded", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "React", "useSSR", "composeInitialProps", "getDisplayName", "withSSR", "Extend", "WrappedComponent", "I18nextWithSSR", "_ref", "initialI18nStore", "initialLanguage", "rest", "createElement", "getInitialProps", "displayName", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/withSSR.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"initialI18nStore\", \"initialLanguage\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport React from 'react';\nimport { useSSR } from './useSSR';\nimport { composeInitialProps } from './context';\nimport { getDisplayName } from './utils';\nexport function withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      var initialI18nStore = _ref.initialI18nStore,\n          initialLanguage = _ref.initialLanguage,\n          rest = _objectWithoutProperties(_ref, _excluded);\n\n      useSSR(initialI18nStore, initialLanguage);\n      return React.createElement(WrappedComponent, _objectSpread({}, rest));\n    }\n\n    I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n    I18nextWithSSR.displayName = \"withI18nextSSR(\".concat(getDisplayName(WrappedComponent), \")\");\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uCAAuC;AACnE,OAAOC,wBAAwB,MAAM,gDAAgD;AACrF,IAAIC,SAAS,GAAG,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;AAEvD,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEvB,eAAe,CAACiB,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACiB,yBAAyB,EAAE;MAAEjB,MAAM,CAACkB,gBAAgB,CAACR,MAAM,EAAEV,MAAM,CAACiB,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACmB,cAAc,CAACT,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,OAAOU,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,mBAAmB,QAAQ,WAAW;AAC/C,SAASC,cAAc,QAAQ,SAAS;AACxC,OAAO,SAASC,OAAOA,CAAA,EAAG;EACxB,OAAO,SAASC,MAAMA,CAACC,gBAAgB,EAAE;IACvC,SAASC,cAAcA,CAACC,IAAI,EAAE;MAC5B,IAAIC,gBAAgB,GAAGD,IAAI,CAACC,gBAAgB;QACxCC,eAAe,GAAGF,IAAI,CAACE,eAAe;QACtCC,IAAI,GAAGrC,wBAAwB,CAACkC,IAAI,EAAEjC,SAAS,CAAC;MAEpD0B,MAAM,CAACQ,gBAAgB,EAAEC,eAAe,CAAC;MACzC,OAAOV,KAAK,CAACY,aAAa,CAACN,gBAAgB,EAAEjB,aAAa,CAAC,CAAC,CAAC,EAAEsB,IAAI,CAAC,CAAC;IACvE;IAEAJ,cAAc,CAACM,eAAe,GAAGX,mBAAmB,CAACI,gBAAgB,CAAC;IACtEC,cAAc,CAACO,WAAW,GAAG,iBAAiB,CAACC,MAAM,CAACZ,cAAc,CAACG,gBAAgB,CAAC,EAAE,GAAG,CAAC;IAC5FC,cAAc,CAACD,gBAAgB,GAAGA,gBAAgB;IAClD,OAAOC,cAAc;EACvB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}