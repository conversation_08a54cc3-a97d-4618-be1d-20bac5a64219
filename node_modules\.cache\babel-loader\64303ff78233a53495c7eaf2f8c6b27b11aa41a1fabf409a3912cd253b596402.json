{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nexport default function omit(obj, fields) {\n  var clone = _objectSpread({}, obj);\n  if (Array.isArray(fields)) {\n    fields.forEach(function (key) {\n      delete clone[key];\n    });\n  }\n  return clone;\n}", "map": {"version": 3, "names": ["_objectSpread", "omit", "obj", "fields", "clone", "Array", "isArray", "for<PERSON>ach", "key"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/omit.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nexport default function omit(obj, fields) {\n  var clone = _objectSpread({}, obj);\n\n  if (Array.isArray(fields)) {\n    fields.forEach(function (key) {\n      delete clone[key];\n    });\n  }\n\n  return clone;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,eAAe,SAASC,IAAIA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACxC,IAAIC,KAAK,GAAGJ,aAAa,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC;EAElC,IAAIG,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IACzBA,MAAM,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC5B,OAAOJ,KAAK,CAACI,GAAG,CAAC;IACnB,CAAC,CAAC;EACJ;EAEA,OAAOJ,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}