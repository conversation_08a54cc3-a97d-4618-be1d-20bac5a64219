{"version": 3, "sources": ["../src/index.ts", "../src/context/index.ts", "../src/context/status.ts", "../src/context/set.ts", "../src/context/cookie.ts", "../src/context/body.ts", "../src/utils/internal/jsonParse.ts", "../src/utils/internal/isObject.ts", "../src/utils/internal/mergeRight.ts", "../src/context/json.ts", "../src/context/data.ts", "../src/context/extensions.ts", "../src/context/delay.ts", "../src/context/errors.ts", "../src/context/fetch.ts", "../src/context/text.ts", "../src/context/xml.ts", "../src/setupWorker/setupWorker.ts", "../src/setupWorker/start/createStartHandler.ts", "../src/setupWorker/start/utils/getWorkerInstance.ts", "../src/setupWorker/start/utils/getWorkerByRegistration.ts", "../src/utils/url/getAbsoluteWorkerUrl.ts", "../src/utils/internal/devUtils.ts", "../src/setupWorker/start/utils/printStartMessage.ts", "../src/setupWorker/start/utils/enableMocking.ts", "../src/setupWorker/start/utils/createMessageChannel.ts", "../src/utils/NetworkError.ts", "../src/utils/request/parseWorkerRequest.ts", "../src/utils/request/MockedRequest.ts", "../src/utils/request/getRequestCookies.ts", "../src/utils/internal/parseMultipartData.ts", "../src/utils/request/parseBody.ts", "../src/utils/internal/isStringEqual.ts", "../src/utils/handleRequest.ts", "../src/utils/getResponse.ts", "../src/utils/request/onUnhandledRequest.ts", "../src/utils/internal/parseGraphQLRequest.ts", "../src/utils/request/getPublicUrlFromRequest.ts", "../src/utils/logging/getStatusCodeColor.ts", "../src/utils/logging/getTimestamp.ts", "../src/utils/logging/prepareRequest.ts", "../src/utils/logging/prepareResponse.ts", "../src/utils/matching/matchRequestUrl.ts", "../src/utils/url/cleanUrl.ts", "../src/utils/url/isAbsoluteUrl.ts", "../src/utils/url/getAbsoluteUrl.ts", "../src/utils/matching/normalizePath.ts", "../src/handlers/RequestHandler.ts", "../src/response.ts", "../src/utils/internal/compose.ts", "../src/utils/internal/getCallFrame.ts", "../src/utils/internal/isIterable.ts", "../src/handlers/RestHandler.ts", "../src/context/field.ts", "../src/utils/internal/tryCatch.ts", "../src/handlers/GraphQLHandler.ts", "../src/utils/request/readResponseCookies.ts", "../src/utils/logging/serializeResponse.ts", "../src/setupWorker/start/createRequestListener.ts", "../src/utils/internal/requestIntegrityCheck.ts", "../src/utils/deferNetworkRequestsUntil.ts", "../src/setupWorker/start/createResponseListener.ts", "../src/setupWorker/start/utils/validateWorkerScope.ts", "../src/setupWorker/stop/utils/printStopMessage.ts", "../src/setupWorker/stop/createStop.ts", "../src/utils/internal/requestHandlerUtils.ts", "../src/setupWorker/start/utils/prepareStartHandler.ts", "../src/setupWorker/start/createFallbackRequestListener.ts", "../src/utils/request/createResponseFromIsomorphicResponse.ts", "../src/setupWorker/start/createFallbackStart.ts", "../src/setupWorker/stop/createFallbackStop.ts", "../src/utils/internal/pipeEvents.ts", "../src/utils/internal/toReadonlyArray.ts", "../src/rest.ts", "../src/graphql.ts"], "sourcesContent": ["import * as context from './context'\nexport { context }\n\nexport { setupWorker } from './setupWorker/setupWorker'\nexport {\n  response,\n  defaultResponse,\n  createResponseComposition,\n} from './response'\n\n/* Request handlers */\nexport { RequestHandler, defaultContext } from './handlers/RequestHandler'\nexport { rest } from './rest'\nexport { RestHandler, RESTMethods, restContext } from './handlers/RestHandler'\nexport { graphql } from './graphql'\nexport { GraphQLHandler, graphqlContext } from './handlers/GraphQLHandler'\n\n/* Utils */\nexport { matchRequestUrl } from './utils/matching/matchRequestUrl'\nexport { compose } from './utils/internal/compose'\nexport * from './utils/handleRequest'\nexport { cleanUrl } from './utils/url/cleanUrl'\n\n/**\n * Type definitions.\n */\nexport type { SetupWorkerApi, StartOptions } from './setupWorker/glossary'\nexport type { SharedOptions } from './sharedOptions'\n\nexport * from './utils/request/MockedRequest'\nexport type {\n  ResponseResolver,\n  ResponseResolverReturnType,\n  AsyncResponseResolverReturnType,\n  DefaultBodyType,\n  DefaultRequestMultipartBody,\n} from './handlers/RequestHandler'\n\nexport type {\n  MockedResponse,\n  ResponseTransformer,\n  ResponseComposition,\n  ResponseCompositionOptions,\n  ResponseFunction,\n} from './response'\n\nexport type {\n  RestRequest,\n  RestContext,\n  RequestQuery,\n  ParsedRestRequest,\n} from './handlers/RestHandler'\n\nexport type {\n  GraphQLContext,\n  GraphQLVariables,\n  GraphQLRequest,\n  GraphQLRequestBody,\n  GraphQLJsonRequestBody,\n} from './handlers/GraphQLHandler'\n\nexport type { Path, PathParams, Match } from './utils/matching/matchRequestUrl'\nexport type { DelayMode } from './context/delay'\nexport { ParsedGraphQLRequest } from './utils/internal/parseGraphQLRequest'\n", "export { status } from './status'\nexport { set } from './set'\nexport { cookie } from './cookie'\nexport { body } from './body'\nexport { data } from './data'\nexport { extensions } from './extensions'\nexport { delay } from './delay'\nexport { errors } from './errors'\nexport { fetch } from './fetch'\nexport { json } from './json'\nexport { text } from './text'\nexport { xml } from './xml'\n", "import statuses from 'statuses/codes.json'\nimport { ResponseTransformer } from '../response'\n\n/**\n * Sets a response status code and text.\n * @example\n * res(ctx.status(301))\n * res(ctx.status(400, 'Custom status text'))\n * @see {@link https://mswjs.io/docs/api/context/status `ctx.status()`}\n */\nexport const status = (\n  statusCode: number,\n  statusText?: string,\n): ResponseTransformer => {\n  return (res) => {\n    res.status = statusCode\n    res.statusText =\n      statusText || statuses[String(statusCode) as keyof typeof statuses]\n\n    return res\n  }\n}\n", "import { objectToHeaders } from 'headers-polyfill'\nimport { ResponseTransformer } from '../response'\n\nexport type HeadersObject<KeyType extends string = string> = Record<\n  KeyType,\n  string | string[]\n>\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name\n */\nexport type ForbiddenHeaderNames =\n  | 'cookie'\n  | 'cookie2'\n  | 'set-cookie'\n  | 'set-cookie2'\n\nexport type ForbiddenHeaderError<HeaderName extends string> =\n  `SafeResponseHeader: the '${HeaderName}' header cannot be set on the response. Please use the 'ctx.cookie()' function instead.`\n\n/**\n * Sets one or multiple response headers.\n * @example\n * ctx.set('Content-Type', 'text/plain')\n * ctx.set({\n *   'Accept': 'application/javascript',\n *   'Content-Type': \"text/plain\"\n * })\n * @see {@link https://mswjs.io/docs/api/context/set `ctx.set()`}\n */\nexport function set<N extends string | HeadersObject>(\n  ...args: N extends string\n    ? Lowercase<N> extends ForbiddenHeaderNames\n      ? [ForbiddenHeaderError<N>]\n      : [N, string]\n    : N extends HeadersObject<infer CookieName>\n    ? Lowercase<CookieName> extends ForbiddenHeaderNames\n      ? [ForbiddenHeaderError<CookieName>]\n      : [N]\n    : [N]\n): ResponseTransformer {\n  return (res) => {\n    const [name, value] = args\n\n    if (typeof name === 'string') {\n      res.headers.append(name, value as string)\n    } else {\n      const headers = objectToHeaders(name)\n      headers.forEach((value, name) => {\n        res.headers.append(name, value)\n      })\n    }\n\n    return res\n  }\n}\n", "import * as cookieUtils from 'cookie'\nimport { ResponseTransformer } from '../response'\n\n/**\n * Sets a given cookie on the mocked response.\n * @example res(ctx.cookie('name', 'value'))\n */\nexport const cookie = (\n  name: string,\n  value: string,\n  options?: cookieUtils.CookieSerializeOptions,\n): ResponseTransformer => {\n  return (res) => {\n    const serializedCookie = cookieUtils.serialize(name, value, options)\n    res.headers.append('Set-Cookie', serializedCookie)\n\n    if (typeof document !== 'undefined') {\n      document.cookie = serializedCookie\n    }\n\n    return res\n  }\n}\n", "import { ResponseTransformer } from '../response'\n\n/**\n * Sets a raw response body. Does not append any `Content-Type` headers.\n * @example\n * res(ctx.body('Successful response'))\n * res(ctx.body(JSON.stringify({ key: 'value' })))\n * @see {@link https://mswjs.io/docs/api/context/body `ctx.body()`}\n */\nexport const body = <\n  BodyType extends string | Blob | BufferSource | ReadableStream | FormData,\n>(\n  value: BodyType,\n): ResponseTransformer<BodyType> => {\n  return (res) => {\n    res.body = value\n    return res\n  }\n}\n", "/**\n * Parses a given value into a JSON.\n * Does not throw an exception on an invalid JSON string.\n */\nexport function jsonParse<ValueType extends Record<string, any>>(\n  value: any,\n): ValueType | undefined {\n  try {\n    return JSON.parse(value)\n  } catch (error) {\n    return undefined\n  }\n}\n", "/**\n * Determines if the given value is an object.\n */\nexport function isObject(value: any): boolean {\n  return value != null && typeof value === 'object' && !Array.isArray(value)\n}\n", "import { isObject } from './isObject'\n\n/**\n * Deeply merges two given objects with the right one\n * having a priority during property assignment.\n */\nexport function mergeRight(\n  left: Record<string, any>,\n  right: Record<string, any>,\n) {\n  return Object.entries(right).reduce((result, [key, rightValue]) => {\n    const leftValue = result[key]\n\n    if (Array.isArray(leftValue) && Array.isArray(rightValue)) {\n      result[key] = leftValue.concat(rightValue)\n      return result\n    }\n\n    if (isObject(leftValue) && isObject(rightValue)) {\n      result[key] = mergeRight(leftValue, rightValue)\n      return result\n    }\n\n    result[key] = rightValue\n    return result\n  }, Object.assign({}, left))\n}\n", "import { ResponseTransformer } from '../response'\n\n/**\n * Sets the given value as the JSON body of the response.\n * Appends a `Content-Type: application/json` header on the\n * mocked response.\n * @example\n * res(ctx.json('Some string'))\n * res(ctx.json({ key: 'value' }))\n * res(ctx.json([1, '2', false, { ok: true }]))\n * @see {@link https://mswjs.io/docs/api/context/json `ctx.json()`}\n */\nexport const json = <BodyTypeJSON>(\n  body: BodyTypeJSON,\n): ResponseTransformer<BodyTypeJSON> => {\n  return (res) => {\n    res.headers.set('Content-Type', 'application/json')\n    res.body = JSON.stringify(body) as any\n\n    return res\n  }\n}\n", "import { jsonParse } from '../utils/internal/jsonParse'\nimport { mergeRight } from '../utils/internal/mergeRight'\nimport { json } from './json'\nimport { GraphQLPayloadContext } from '../typeUtils'\n\n/**\n * Sets a given payload as a GraphQL response body.\n * @example\n * res(ctx.data({ user: { firstName: 'John' }}))\n * @see {@link https://mswjs.io/docs/api/context/data `ctx.data()`}\n */\nexport const data: GraphQLPayloadContext<Record<string, unknown>> = (\n  payload,\n) => {\n  return (res) => {\n    const prevBody = jsonParse(res.body) || {}\n    const nextBody = mergeRight(prevBody, { data: payload })\n\n    return json(nextBody)(res)\n  }\n}\n", "import { jsonParse } from '../utils/internal/jsonParse'\nimport { mergeRight } from '../utils/internal/mergeRight'\nimport { json } from './json'\nimport { GraphQLPayloadContext } from '../typeUtils'\n\n/**\n * Sets the GraphQL extensions on a given response.\n * @example\n * res(ctx.extensions({ tracing: { version: 1 }}))\n * @see {@link https://mswjs.io/docs/api/context/extensions `ctx.extensions()`}\n */\nexport const extensions: GraphQLPayloadContext<Record<string, unknown>> = (\n  payload,\n) => {\n  return (res) => {\n    const prevBody = jsonParse(res.body) || {}\n    const nextBody = mergeRight(prevBody, { extensions: payload })\n    return json(nextBody)(res)\n  }\n}\n", "import { isNodeProcess } from 'is-node-process'\nimport { ResponseTransformer } from '../response'\n\nexport const SET_TIMEOUT_MAX_ALLOWED_INT = 2147483647\nexport const MIN_SERVER_RESPONSE_TIME = 100\nexport const MAX_SERVER_RESPONSE_TIME = 400\nexport const NODE_SERVER_RESPONSE_TIME = 5\n\nconst getRandomServerResponseTime = () => {\n  if (isNodeProcess()) {\n    return NODE_SERVER_RESPONSE_TIME\n  }\n\n  return Math.floor(\n    Math.random() * (MAX_SERVER_RESPONSE_TIME - MIN_SERVER_RESPONSE_TIME) +\n      MIN_SERVER_RESPONSE_TIME,\n  )\n}\n\nexport type DelayMode = 'real' | 'infinite'\n\n/**\n * Delays the response by the given duration (ms).\n * @example\n * res(ctx.delay(1200)) // delay response by 1200ms\n * res(ctx.delay()) // emulate realistic server response time\n * res(ctx.delay('infinite')) // delay response infinitely\n * @see {@link https://mswjs.io/docs/api/context/delay `ctx.delay()`}\n */\nexport const delay = (\n  durationOrMode?: DelayMode | number,\n): ResponseTransformer => {\n  return (res) => {\n    let delayTime: number\n\n    if (typeof durationOrMode === 'string') {\n      switch (durationOrMode) {\n        case 'infinite': {\n          // Using `Infinity` as a delay value executes the response timeout immediately.\n          // Instead, use the maximum allowed integer for `setTimeout`.\n          delayTime = SET_TIMEOUT_MAX_ALLOWED_INT\n          break\n        }\n        case 'real': {\n          delayTime = getRandomServerResponseTime()\n          break\n        }\n        default: {\n          throw new Error(\n            `Failed to delay a response: unknown delay mode \"${durationOrMode}\". Please make sure you provide one of the supported modes (\"real\", \"infinite\") or a number to \"ctx.delay\".`,\n          )\n        }\n      }\n    } else if (typeof durationOrMode === 'undefined') {\n      // Use random realistic server response time when no explicit delay duration was provided.\n      delayTime = getRandomServerResponseTime()\n    } else {\n      // Guard against passing values like `Infinity` or `Number.MAX_VALUE`\n      // as the response delay duration. They don't produce the result you may expect.\n      if (durationOrMode > SET_TIMEOUT_MAX_ALLOWED_INT) {\n        throw new Error(\n          `Failed to delay a response: provided delay duration (${durationOrMode}) exceeds the maximum allowed duration for \"setTimeout\" (${SET_TIMEOUT_MAX_ALLOWED_INT}). This will cause the response to be returned immediately. Please use a number within the allowed range to delay the response by exact duration, or consider the \"infinite\" delay mode to delay the response indefinitely.`,\n        )\n      }\n\n      delayTime = durationOrMode\n    }\n\n    res.delay = delayTime\n    return res\n  }\n}\n", "import type { GraphQLError } from 'graphql'\nimport { ResponseTransformer } from '../response'\nimport { jsonParse } from '../utils/internal/jsonParse'\nimport { mergeRight } from '../utils/internal/mergeRight'\nimport { json } from './json'\n\n/**\n * Sets a given list of GraphQL errors on the mocked response.\n * @example res(ctx.errors([{ message: 'Unauthorized' }]))\n * @see {@link https://mswjs.io/docs/api/context/errors}\n */\nexport const errors = <\n  ErrorsType extends readonly Partial<GraphQLError>[] | null | undefined,\n>(\n  errorsList: ErrorsType,\n): ResponseTransformer<string> => {\n  return (res) => {\n    if (errorsList == null) {\n      return res\n    }\n\n    const prevBody = jsonParse(res.body) || {}\n    const nextBody = mergeRight(prevBody, { errors: errorsList })\n\n    return json(nextBody)(res as any) as any\n  }\n}\n", "import { isNodeProcess } from 'is-node-process'\nimport { Headers } from 'headers-polyfill'\nimport { MockedRequest } from '../utils/request/MockedRequest'\n\nconst useFetch: (input: RequestInfo, init?: RequestInit) => Promise<Response> =\n  isNodeProcess() ? require('node-fetch') : window.fetch\n\nexport const augmentRequestInit = (requestInit: RequestInit): RequestInit => {\n  const headers = new Headers(requestInit.headers)\n  headers.set('x-msw-bypass', 'true')\n\n  return {\n    ...requestInit,\n    headers: headers.all(),\n  }\n}\n\nconst createFetchRequestParameters = (input: MockedRequest): RequestInit => {\n  const { body, method } = input\n  const requestParameters: RequestInit = {\n    ...input,\n    body: undefined,\n  }\n\n  if (['GET', 'HEAD'].includes(method)) {\n    return requestParameters\n  }\n\n  if (\n    typeof body === 'object' ||\n    typeof body === 'number' ||\n    typeof body === 'boolean'\n  ) {\n    requestParameters.body = JSON.stringify(body)\n  } else {\n    requestParameters.body = body\n  }\n\n  return requestParameters\n}\n\n/**\n * Performs a bypassed request inside a request handler.\n * @example\n * const originalResponse = await ctx.fetch(req)\n * @see {@link https://mswjs.io/docs/api/context/fetch `ctx.fetch()`}\n */\nexport const fetch = (\n  input: string | MockedRequest,\n  requestInit: RequestInit = {},\n): Promise<Response> => {\n  if (typeof input === 'string') {\n    return useFetch(input, augmentRequestInit(requestInit))\n  }\n\n  const requestParameters = createFetchRequestParameters(input)\n  const derivedRequestInit = augmentRequestInit(requestParameters)\n\n  return useFetch(input.url.href, derivedRequestInit)\n}\n", "import { ResponseTransformer } from '../response'\n\n/**\n * Sets a textual response body. Appends a `Content-Type: text/plain`\n * header on the mocked response.\n * @example res(ctx.text('Successful response'))\n * @see {@link https://mswjs.io/docs/api/context/text `ctx.text()`}\n */\nexport const text = <BodyType extends string>(\n  body: BodyType,\n): ResponseTransformer<BodyType> => {\n  return (res) => {\n    res.headers.set('Content-Type', 'text/plain')\n    res.body = body\n    return res\n  }\n}\n", "import { ResponseTransformer } from '../response'\n\n/**\n * Sets an XML response body. Appends a `Content-Type: text/xml` header\n * on the mocked response.\n * @example\n * res(ctx.xml('<node key=\"value\">Content</node>'))\n * @see {@link https://mswjs.io/docs/api/context/xml `ctx.xml()`}\n */\nexport const xml = <BodyType extends string>(\n  body: BodyType,\n): ResponseTransformer<BodyType> => {\n  return (res) => {\n    res.headers.set('Content-Type', 'text/xml')\n    res.body = body\n    return res\n  }\n}\n", "import { isNodeProcess } from 'is-node-process'\nimport { StrictEventEmitter } from 'strict-event-emitter'\nimport {\n  SetupWorkerInternalContext,\n  SetupWorkerApi,\n  ServiceWorkerIncomingEventsMap,\n  WorkerLifecycleEventsMap,\n} from './glossary'\nimport { createStartHandler } from './start/createStartHandler'\nimport { createStop } from './stop/createStop'\nimport * as requestHandlerUtils from '../utils/internal/requestHandlerUtils'\nimport { ServiceWorkerMessage } from './start/utils/createMessageChannel'\nimport { RequestHandler } from '../handlers/RequestHandler'\nimport { RestHandler } from '../handlers/RestHandler'\nimport { prepareStartHandler } from './start/utils/prepareStartHandler'\nimport { createFallbackStart } from './start/createFallbackStart'\nimport { createFallbackStop } from './stop/createFallbackStop'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { pipeEvents } from '../utils/internal/pipeEvents'\nimport { toReadonlyArray } from '../utils/internal/toReadonlyArray'\n\ninterface Listener {\n  target: EventTarget\n  eventType: string\n  callback: EventListener\n}\n\n// Declare the list of event handlers on the module's scope\n// so it persists between Fash refreshes of the application's code.\nlet listeners: Listener[] = []\n\n/**\n * Creates a new mock Service Worker registration\n * with the given request handlers.\n * @param {RequestHandler[]} requestHandlers List of request handlers\n * @see {@link https://mswjs.io/docs/api/setup-worker `setupWorker`}\n */\nexport function setupWorker(\n  ...requestHandlers: RequestHandler[]\n): SetupWorkerApi {\n  requestHandlers.forEach((handler) => {\n    if (Array.isArray(handler))\n      throw new Error(\n        devUtils.formatMessage(\n          'Failed to call \"setupWorker\" given an Array of request handlers (setupWorker([a, b])), expected to receive each handler individually: setupWorker(a, b).',\n        ),\n      )\n  })\n\n  // Error when attempting to run this function in a Node.js environment.\n  if (isNodeProcess()) {\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to execute `setupWorker` in a non-browser environment. Consider using `setupServer` for Node.js environment instead.',\n      ),\n    )\n  }\n\n  const emitter = new StrictEventEmitter<WorkerLifecycleEventsMap>()\n  const publicEmitter = new StrictEventEmitter<WorkerLifecycleEventsMap>()\n  pipeEvents(emitter, publicEmitter)\n\n  const context: SetupWorkerInternalContext = {\n    // Mocking is not considered enabled until the worker\n    // signals back the successful activation event.\n    isMockingEnabled: false,\n    startOptions: undefined,\n    worker: null,\n    registration: null,\n    requestHandlers: [...requestHandlers],\n    emitter,\n    workerChannel: {\n      on(eventType, callback) {\n        context.events.addListener(\n          navigator.serviceWorker,\n          'message',\n          (event: MessageEvent) => {\n            // Avoid messages broadcasted from unrelated workers.\n            if (event.source !== context.worker) {\n              return\n            }\n\n            const message = event.data as ServiceWorkerMessage<\n              typeof eventType,\n              any\n            >\n\n            if (!message) {\n              return\n            }\n\n            if (message.type === eventType) {\n              callback(event, message)\n            }\n          },\n        )\n      },\n      send(type) {\n        context.worker?.postMessage(type)\n      },\n    },\n    events: {\n      addListener(\n        target: EventTarget,\n        eventType: string,\n        callback: EventListener,\n      ) {\n        target.addEventListener(eventType, callback)\n        listeners.push({ eventType, target, callback })\n\n        return () => {\n          target.removeEventListener(eventType, callback)\n        }\n      },\n      removeAllListeners() {\n        for (const { target, eventType, callback } of listeners) {\n          target.removeEventListener(eventType, callback)\n        }\n        listeners = []\n      },\n      once(eventType) {\n        const bindings: Array<() => void> = []\n\n        return new Promise<\n          ServiceWorkerMessage<\n            typeof eventType,\n            ServiceWorkerIncomingEventsMap[typeof eventType]\n          >\n        >((resolve, reject) => {\n          const handleIncomingMessage = (event: MessageEvent) => {\n            try {\n              const message = event.data\n\n              if (message.type === eventType) {\n                resolve(message)\n              }\n            } catch (error) {\n              reject(error)\n            }\n          }\n\n          bindings.push(\n            context.events.addListener(\n              navigator.serviceWorker,\n              'message',\n              handleIncomingMessage,\n            ),\n            context.events.addListener(\n              navigator.serviceWorker,\n              'messageerror',\n              reject,\n            ),\n          )\n        }).finally(() => {\n          bindings.forEach((unbind) => unbind())\n        })\n      },\n    },\n    useFallbackMode:\n      !('serviceWorker' in navigator) || location.protocol === 'file:',\n  }\n\n  const startHandler = context.useFallbackMode\n    ? createFallbackStart(context)\n    : createStartHandler(context)\n  const stopHandler = context.useFallbackMode\n    ? createFallbackStop(context)\n    : createStop(context)\n\n  return {\n    start: prepareStartHandler(startHandler, context),\n    stop() {\n      context.events.removeAllListeners()\n      context.emitter.removeAllListeners()\n      publicEmitter.removeAllListeners()\n      stopHandler()\n    },\n\n    use(...handlers) {\n      requestHandlerUtils.use(context.requestHandlers, ...handlers)\n    },\n\n    restoreHandlers() {\n      requestHandlerUtils.restoreHandlers(context.requestHandlers)\n    },\n\n    resetHandlers(...nextHandlers) {\n      context.requestHandlers = requestHandlerUtils.resetHandlers(\n        requestHandlers,\n        ...nextHandlers,\n      )\n    },\n\n    listHandlers() {\n      return toReadonlyArray(context.requestHandlers)\n    },\n\n    printHandlers() {\n      const handlers = this.listHandlers()\n\n      handlers.forEach((handler) => {\n        const { header, callFrame } = handler.info\n        const pragma = handler.info.hasOwnProperty('operationType')\n          ? '[graphql]'\n          : '[rest]'\n\n        console.groupCollapsed(`${pragma} ${header}`)\n\n        if (callFrame) {\n          console.log(`Declaration: ${callFrame}`)\n        }\n\n        console.log('Handler:', handler)\n\n        if (handler instanceof RestHandler) {\n          console.log(\n            'Match:',\n            `https://mswjs.io/repl?path=${handler.info.path}`,\n          )\n        }\n\n        console.groupEnd()\n      })\n    },\n\n    events: {\n      on(...args) {\n        return publicEmitter.on(...args)\n      },\n      removeListener(...args) {\n        return publicEmitter.removeListener(...args)\n      },\n      removeAllListeners(...args) {\n        return publicEmitter.removeAllListeners(...args)\n      },\n    },\n  }\n}\n", "import { until } from '@open-draft/until'\nimport { getWorkerInstance } from './utils/getWorkerInstance'\nimport { enableMocking } from './utils/enableMocking'\nimport { SetupWorkerInternalContext, StartHandler } from '../glossary'\nimport { createRequestListener } from './createRequestListener'\nimport { requestIntegrityCheck } from '../../utils/internal/requestIntegrityCheck'\nimport { deferNetworkRequestsUntil } from '../../utils/deferNetworkRequestsUntil'\nimport { createResponseListener } from './createResponseListener'\nimport { validateWorkerScope } from './utils/validateWorkerScope'\nimport { devUtils } from '../../utils/internal/devUtils'\n\nexport const createStartHandler = (\n  context: SetupWorkerInternalContext,\n): StartHandler => {\n  return function start(options, customOptions) {\n    const startWorkerInstance = async () => {\n      // Remove all previously existing event listeners.\n      // This way none of the listeners persists between Fast refresh\n      // of the application's code.\n      context.events.removeAllListeners()\n\n      // Handle requests signaled by the worker.\n      context.workerChannel.on(\n        'REQUEST',\n        createRequestListener(context, options),\n      )\n\n      // Handle responses signaled by the worker.\n      context.workerChannel.on('RESPONSE', createResponseListener(context))\n\n      const instance = await getWorkerInstance(\n        options.serviceWorker.url,\n        options.serviceWorker.options,\n        options.findWorker,\n      )\n\n      const [worker, registration] = instance\n\n      if (!worker) {\n        const missingWorkerMessage = customOptions?.findWorker\n          ? devUtils.formatMessage(\n              `Failed to locate the Service Worker registration using a custom \"findWorker\" predicate.\n\nPlease ensure that the custom predicate properly locates the Service Worker registration at \"%s\".\nMore details: https://mswjs.io/docs/api/setup-worker/start#findworker\n`,\n              options.serviceWorker.url,\n            )\n          : devUtils.formatMessage(\n              `Failed to locate the Service Worker registration.\n\nThis most likely means that the worker script URL \"%s\" cannot resolve against the actual public hostname (%s). This may happen if your application runs behind a proxy, or has a dynamic hostname.\n\nPlease consider using a custom \"serviceWorker.url\" option to point to the actual worker script location, or a custom \"findWorker\" option to resolve the Service Worker registration manually. More details: https://mswjs.io/docs/api/setup-worker/start`,\n              options.serviceWorker.url,\n              location.host,\n            )\n\n        throw new Error(missingWorkerMessage)\n      }\n\n      context.worker = worker\n      context.registration = registration\n\n      context.events.addListener(window, 'beforeunload', () => {\n        if (worker.state !== 'redundant') {\n          // Notify the Service Worker that this client has closed.\n          // Internally, it's similar to disabling the mocking, only\n          // client close event has a handler that self-terminates\n          // the Service Worker when there are no open clients.\n          context.workerChannel.send('CLIENT_CLOSED')\n        }\n        // Make sure we're always clearing the interval - there are reports that not doing this can\n        // cause memory leaks in headless browser environments.\n        window.clearInterval(context.keepAliveInterval)\n      })\n\n      // Check if the active Service Worker is the latest published one\n      const [integrityError] = await until(() =>\n        requestIntegrityCheck(context, worker),\n      )\n\n      if (integrityError) {\n        devUtils.error(`\\\nDetected outdated Service Worker: ${integrityError.message}\n\nThe mocking is still enabled, but it's highly recommended that you update your Service Worker by running:\n\n$ npx msw init <PUBLIC_DIR>\n\nThis is necessary to ensure that the Service Worker is in sync with the library to guarantee its stability.\nIf this message still persists after updating, please report an issue: https://github.com/open-draft/msw/issues\\\n      `)\n      }\n\n      context.keepAliveInterval = window.setInterval(\n        () => context.workerChannel.send('KEEPALIVE_REQUEST'),\n        5000,\n      )\n\n      // Warn the user when loading the page that lies outside\n      // of the worker's scope.\n      validateWorkerScope(registration, context.startOptions)\n\n      return registration\n    }\n\n    const workerRegistration = startWorkerInstance().then(\n      async (registration) => {\n        const pendingInstance = registration.installing || registration.waiting\n\n        // Wait until the worker is activated.\n        // Assume the worker is already activated if there's no pending registration\n        // (i.e. when reloading the page after a successful activation).\n        if (pendingInstance) {\n          await new Promise<void>((resolve) => {\n            pendingInstance.addEventListener('statechange', () => {\n              if (pendingInstance.state === 'activated') {\n                return resolve()\n              }\n            })\n          })\n        }\n\n        // Print the activation message only after the worker has been activated.\n        await enableMocking(context, options).catch((error) => {\n          throw new Error(`Failed to enable mocking: ${error?.message}`)\n        })\n\n        return registration\n      },\n    )\n\n    // Defer any network requests until the Service Worker instance is ready.\n    // This prevents a race condition between the Service Worker registration\n    // and application's runtime requests (i.e. requests on mount).\n    if (options.waitUntilReady) {\n      deferNetworkRequestsUntil(workerRegistration)\n    }\n\n    return workerRegistration\n  }\n}\n", "import { until } from '@open-draft/until'\nimport { getWorkerByRegistration } from './getWorkerByRegistration'\nimport { ServiceWorkerInstanceTuple, FindWorker } from '../../glossary'\nimport { getAbsoluteWorkerUrl } from '../../../utils/url/getAbsoluteWorkerUrl'\nimport { devUtils } from '../../../utils/internal/devUtils'\n\n/**\n * Returns an active Service Worker instance.\n * When not found, registers a new Service Worker.\n */\nexport const getWorkerInstance = async (\n  url: string,\n  options: RegistrationOptions = {},\n  findWorker: FindWorker,\n): Promise<ServiceWorkerInstanceTuple> => {\n  // Resolve the absolute Service Worker URL.\n  const absoluteWorkerUrl = getAbsoluteWorkerUrl(url)\n\n  const mockRegistrations = await navigator.serviceWorker\n    .getRegistrations()\n    .then((registrations) =>\n      registrations.filter((registration) =>\n        getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker),\n      ),\n    )\n  if (!navigator.serviceWorker.controller && mockRegistrations.length > 0) {\n    // Reload the page when it has associated workers, but no active controller.\n    // The absence of a controller can mean either:\n    // - page has no Service Worker associated with it\n    // - page has been hard-reloaded and its workers won't be used until the next reload.\n    // Since we've checked that there are registrations associated with this page,\n    // at this point we are sure it's hard reload that falls into this clause.\n    location.reload()\n  }\n\n  const [existingRegistration] = mockRegistrations\n\n  if (existingRegistration) {\n    // When the Service Worker is registered, update it and return the reference.\n    return existingRegistration.update().then(() => {\n      return [\n        getWorkerByRegistration(\n          existingRegistration,\n          absoluteWorkerUrl,\n          findWorker,\n        ),\n        existingRegistration,\n      ]\n    })\n  }\n\n  // When the Service Worker wasn't found, register it anew and return the reference.\n  const [error, instance] = await until<ServiceWorkerInstanceTuple>(\n    async () => {\n      const registration = await navigator.serviceWorker.register(url, options)\n      return [\n        // Compare existing worker registration by its worker URL,\n        // to prevent irrelevant workers to resolve here (such as Codesandbox worker).\n        getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker),\n        registration,\n      ]\n    },\n  )\n\n  // Handle Service Worker registration errors.\n  if (error) {\n    const isWorkerMissing = error.message.includes('(404)')\n\n    // Produce a custom error message when given a non-existing Service Worker url.\n    // Suggest developers to check their setup.\n    if (isWorkerMissing) {\n      const scopeUrl = new URL(options?.scope || '/', location.href)\n\n      throw new Error(\n        devUtils.formatMessage(`\\\nFailed to register a Service Worker for scope ('${scopeUrl.href}') with script ('${absoluteWorkerUrl}'): Service Worker script does not exist at the given path.\n\nDid you forget to run \"npx msw init <PUBLIC_DIR>\"?\n\nLearn more about creating the Service Worker script: https://mswjs.io/docs/cli/init`),\n      )\n    }\n\n    // Fallback error message for any other registration errors.\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to register the Service Worker:\\n\\n%s',\n        error.message,\n      ),\n    )\n  }\n\n  return instance\n}\n", "import { FindWorker } from '../../glossary'\n\n/**\n * Attempts to resolve a Service Worker instance from a given registration,\n * regardless of its state (active, installing, waiting).\n */\nexport const getWorkerByRegistration = (\n  registration: ServiceWorkerRegistration,\n  absoluteWorkerUrl: string,\n  findWorker: FindWorker,\n): ServiceWorker | null => {\n  const allStates = [\n    registration.active,\n    registration.installing,\n    registration.waiting,\n  ]\n  const existingStates = allStates.filter(Boolean) as ServiceWorker[]\n  const mockWorker = existingStates.find((worker) => {\n    return findWorker(worker.scriptURL, absoluteWorkerUrl)\n  })\n\n  return mockWorker || null\n}\n", "/**\n * Returns an absolute Service Worker URL based on the given\n * relative URL (known during the registration).\n */\nexport function getAbsoluteWorkerUrl(relativeUrl: string): string {\n  return new URL(relativeUrl, location.origin).href\n}\n", "import { format } from 'outvariant'\n\nconst LIBRARY_PREFIX = '[MSW]'\n\n/**\n * Formats a given message by appending the library's prefix string.\n */\nfunction formatMessage(message: string, ...positionals: any[]): string {\n  const interpolatedMessage = format(message, ...positionals)\n  return `${LIBRARY_PREFIX} ${interpolatedMessage}`\n}\n\n/**\n * Prints a library-specific warning.\n */\nfunction warn(message: string, ...positionals: any[]): void {\n  console.warn(formatMessage(message, ...positionals))\n}\n\n/**\n * Prints a library-specific error.\n */\nfunction error(message: string, ...positionals: any[]): void {\n  console.error(formatMessage(message, ...positionals))\n}\n\nexport const devUtils = {\n  formatMessage,\n  warn,\n  error,\n}\n", "import { devUtils } from '../../../utils/internal/devUtils'\n\nexport interface PrintStartMessageArgs {\n  quiet?: boolean\n  message?: string\n  workerUrl?: string\n  workerScope?: string\n}\n\n/**\n * Prints a worker activation message in the browser's console.\n */\nexport function printStartMessage(args: PrintStartMessageArgs = {}) {\n  if (args.quiet) {\n    return\n  }\n\n  const message = args.message || 'Mocking enabled.'\n\n  console.groupCollapsed(\n    `%c${devUtils.formatMessage(message)}`,\n    'color:orangered;font-weight:bold;',\n  )\n  console.log(\n    '%cDocumentation: %chttps://mswjs.io/docs',\n    'font-weight:bold',\n    'font-weight:normal',\n  )\n  console.log('Found an issue? https://github.com/mswjs/msw/issues')\n\n  if (args.workerUrl) {\n    console.log('Worker script URL:', args.workerUrl)\n  }\n\n  if (args.workerScope) {\n    console.log('Worker scope:', args.workerScope)\n  }\n\n  console.groupEnd()\n}\n", "import { devUtils } from '../../../utils/internal/devUtils'\nimport { StartOptions, SetupWorkerInternalContext } from '../../glossary'\nimport { printStartMessage } from './printStartMessage'\n\n/**\n * Signals the worker to enable the interception of requests.\n */\nexport async function enableMocking(\n  context: SetupWorkerInternalContext,\n  options: StartOptions,\n) {\n  context.workerChannel.send('MOCK_ACTIVATE')\n  await context.events.once('MOCKING_ENABLED')\n\n  // Warn the developer on multiple \"worker.start()\" calls.\n  // While this will not affect the worker in any way,\n  // it likely indicates an issue with the developer's code.\n  if (context.isMockingEnabled) {\n    devUtils.warn(\n      `Found a redundant \"worker.start()\" call. Note that starting the worker while mocking is already enabled will have no effect. Consider removing this \"worker.start()\" call.`,\n    )\n    return\n  }\n\n  context.isMockingEnabled = true\n\n  printStartMessage({\n    quiet: options.quiet,\n    workerScope: context.registration?.scope,\n    workerUrl: context.worker?.scriptURL,\n  })\n}\n", "import {\n  SerializedResponse,\n  ServiceWorkerIncomingEventsMap,\n} from '../../glossary'\n\nexport interface ServiceWorkerMessage<\n  EventType extends keyof ServiceWorkerIncomingEventsMap,\n  EventPayload,\n> {\n  type: EventType\n  payload: EventPayload\n}\n\ninterface WorkerChannelEventsMap {\n  MOCK_RESPONSE: [data: SerializedResponse<any>, body?: [<PERSON><PERSON><PERSON><PERSON>uff<PERSON>]]\n  NOT_FOUND: []\n  NETWORK_ERROR: [data: { name: string; message: string }]\n}\n\nexport class WorkerChannel {\n  constructor(private readonly port: MessagePort) {}\n\n  public postMessage<Event extends keyof WorkerChannelEventsMap>(\n    event: Event,\n    ...rest: WorkerChannelEventsMap[Event]\n  ): void {\n    const [data, transfer] = rest\n    this.port.postMessage({ type: event, data }, { transfer })\n  }\n}\n", "export class NetworkError extends Error {\n  constructor(message: string) {\n    super(message)\n    this.name = 'NetworkError'\n  }\n}\n", "import { encodeBuffer } from '@mswjs/interceptors'\nimport { Headers } from 'headers-polyfill'\nimport { ServiceWorkerIncomingRequest } from '../../setupWorker/glossary'\nimport { MockedRequest } from './MockedRequest'\n\n/**\n * Converts a given request received from the Service Worker\n * into a `MockedRequest` instance.\n */\nexport function parseWorkerRequest(\n  rawRequest: ServiceWorkerIncomingRequest,\n): MockedRequest {\n  const url = new URL(rawRequest.url)\n  const headers = new Headers(rawRequest.headers)\n\n  return new MockedRequest(url, {\n    ...rawRequest,\n    body: encodeBuffer(rawRequest.body || ''),\n    headers,\n  })\n}\n", "import * as cookieUtils from 'cookie'\nimport { store } from '@mswjs/cookies'\nimport { IsomorphicRequest, RequestInit } from '@mswjs/interceptors'\nimport { decodeBuffer } from '@mswjs/interceptors/lib/utils/bufferUtils'\nimport { Headers } from 'headers-polyfill'\nimport { DefaultBodyType } from '../../handlers/RequestHandler'\nimport { MockedResponse } from '../../response'\nimport { getRequestCookies } from './getRequestCookies'\nimport { parseBody } from './parseBody'\nimport { isStringEqual } from '../internal/isStringEqual'\n\nexport type RequestCache =\n  | 'default'\n  | 'no-store'\n  | 'reload'\n  | 'no-cache'\n  | 'force-cache'\n  | 'only-if-cached'\n\nexport type RequestMode = 'navigate' | 'same-origin' | 'no-cors' | 'cors'\n\nexport type RequestRedirect = 'follow' | 'error' | 'manual'\n\nexport type RequestDestination =\n  | ''\n  | 'audio'\n  | 'audioworklet'\n  | 'document'\n  | 'embed'\n  | 'font'\n  | 'frame'\n  | 'iframe'\n  | 'image'\n  | 'manifest'\n  | 'object'\n  | 'paintworklet'\n  | 'report'\n  | 'script'\n  | 'sharedworker'\n  | 'style'\n  | 'track'\n  | 'video'\n  | 'xslt'\n  | 'worker'\n\nexport type RequestPriority = 'high' | 'low' | 'auto'\n\nexport type RequestReferrerPolicy =\n  | ''\n  | 'no-referrer'\n  | 'no-referrer-when-downgrade'\n  | 'origin'\n  | 'origin-when-cross-origin'\n  | 'same-origin'\n  | 'strict-origin'\n  | 'strict-origin-when-cross-origin'\n  | 'unsafe-url'\n\nexport interface MockedRequestInit extends RequestInit {\n  id?: string\n  cache?: RequestCache\n  redirect?: RequestRedirect\n  integrity?: string\n  keepalive?: boolean\n  mode?: RequestMode\n  priority?: RequestPriority\n  destination?: RequestDestination\n  referrer?: string\n  referrerPolicy?: RequestReferrerPolicy\n  cookies?: Record<string, string>\n}\n\nexport class MockedRequest<\n  RequestBody extends DefaultBodyType = DefaultBodyType,\n> extends IsomorphicRequest {\n  public readonly cache: RequestCache\n  public readonly cookies: Record<string, string>\n  public readonly destination: RequestDestination\n  public readonly integrity: string\n  public readonly keepalive: boolean\n  public readonly mode: RequestMode\n  public readonly priority: RequestPriority\n  public readonly redirect: RequestRedirect\n  public readonly referrer: string\n  public readonly referrerPolicy: RequestReferrerPolicy\n\n  constructor(url: URL, init: MockedRequestInit = {}) {\n    super(url, init)\n    if (init.id) {\n      this.id = init.id\n    }\n    this.cache = init.cache || 'default'\n    this.destination = init.destination || ''\n    this.integrity = init.integrity || ''\n    this.keepalive = init.keepalive || false\n    this.mode = init.mode || 'cors'\n    this.priority = init.priority || 'auto'\n    this.redirect = init.redirect || 'follow'\n    this.referrer = init.referrer || ''\n    this.referrerPolicy = init.referrerPolicy || 'no-referrer'\n    this.cookies = init.cookies || this.getCookies()\n  }\n\n  /**\n   * Get parsed request body. The type is inferred from the content type.\n   *\n   * @deprecated - Use `req.text()`, `req.json()` or `req.arrayBuffer()`\n   * to read the request body as a plain text, JSON, or ArrayBuffer.\n   */\n  public get body(): RequestBody {\n    const text = decodeBuffer(this['_body'])\n\n    /**\n     * @deprecated https://github.com/mswjs/msw/issues/1318\n     * @fixme Remove this assumption and let the users read\n     * request body explicitly using \".json()\"/\".text()\"/\".arrayBuffer()\".\n     */\n    // Parse the request's body based on the \"Content-Type\" header.\n    const body = parseBody(text, this.headers)\n\n    if (isStringEqual(this.method, 'GET') && body === '') {\n      return undefined as RequestBody\n    }\n\n    return body as RequestBody\n  }\n\n  /**\n   * Bypass the intercepted request.\n   * This will make a call to the actual endpoint requested.\n   */\n  public passthrough(): MockedResponse<null> {\n    return {\n      // Constructing a dummy \"101 Continue\" mocked response\n      // to keep the return type of the resolver consistent.\n      status: 101,\n      statusText: 'Continue',\n      headers: new Headers(),\n      body: null,\n      // Setting \"passthrough\" to true will signal the response pipeline\n      // to perform this intercepted request as-is.\n      passthrough: true,\n      once: false,\n    }\n  }\n\n  private getCookies(): Record<string, string> {\n    // Parse the cookies passed in the original request \"cookie\" header.\n    const requestCookiesString = this.headers.get('cookie')\n    const ownCookies = requestCookiesString\n      ? cookieUtils.parse(requestCookiesString)\n      : {}\n\n    store.hydrate()\n\n    const cookiesFromStore = Array.from(\n      store.get({ ...this, url: this.url.href })?.entries(),\n    ).reduce((cookies, [name, { value }]) => {\n      return Object.assign(cookies, { [name.trim()]: value })\n    }, {})\n\n    // Get existing document cookies that are applicable\n    // to this request based on its \"credentials\" policy.\n    const cookiesFromDocument = getRequestCookies(this)\n\n    const forwardedCookies = {\n      ...cookiesFromDocument,\n      ...cookiesFromStore,\n    }\n\n    for (const [name, value] of Object.entries(forwardedCookies)) {\n      this.headers.append('cookie', `${name}=${value}`)\n    }\n\n    return {\n      ...forwardedCookies,\n      ...ownCookies,\n    }\n  }\n}\n", "import * as cookieUtils from 'cookie'\nimport { MockedRequest } from './MockedRequest'\n\nfunction getAllCookies() {\n  return cookieUtils.parse(document.cookie)\n}\n\n/**\n * Returns relevant document cookies based on the request `credentials` option.\n */\nexport function getRequestCookies(request: MockedRequest) {\n  /**\n   * @note No cookies persist on the document in Node.js: no document.\n   */\n  if (typeof document === 'undefined' || typeof location === 'undefined') {\n    return {}\n  }\n\n  switch (request.credentials) {\n    case 'same-origin': {\n      // Return document cookies only when requested a resource\n      // from the same origin as the current document.\n      return location.origin === request.url.origin ? getAllCookies() : {}\n    }\n\n    case 'include': {\n      // Return all document cookies.\n      return getAllCookies()\n    }\n\n    default: {\n      return {}\n    }\n  }\n}\n", "import { stringToHeaders } from 'headers-polyfill'\nimport { DefaultRequestMultipartBody } from '../../handlers/RequestHandler'\n\ninterface ParsedContentHeaders {\n  name: string\n  filename?: string\n  contentType: string\n}\n\ninterface ContentDispositionDirective {\n  [key: string]: string | undefined\n  name: string\n  filename?: string\n  'form-data': string\n}\n\nfunction parseContentHeaders(headersString: string): ParsedContentHeaders {\n  const headers = stringToHeaders(headersString)\n  const contentType = headers.get('content-type') || 'text/plain'\n  const disposition = headers.get('content-disposition')\n\n  if (!disposition) {\n    throw new Error('\"Content-Disposition\" header is required.')\n  }\n\n  const directives = disposition.split(';').reduce((acc, chunk) => {\n    const [name, ...rest] = chunk.trim().split('=')\n    acc[name] = rest.join('=')\n    return acc\n  }, {} as ContentDispositionDirective)\n\n  const name = directives.name?.slice(1, -1)\n  const filename = directives.filename?.slice(1, -1)\n\n  return {\n    name,\n    filename,\n    contentType,\n  }\n}\n\n/**\n * Parses a given string as a multipart/form-data.\n * Does not throw an exception on an invalid multipart string.\n */\nexport function parseMultipartData<T extends DefaultRequestMultipartBody>(\n  data: string,\n  headers?: Headers,\n): T | undefined {\n  const contentType = headers?.get('content-type')\n\n  if (!contentType) {\n    return undefined\n  }\n\n  const [, ...directives] = contentType.split(/; */)\n  const boundary = directives\n    .filter((d) => d.startsWith('boundary='))\n    .map((s) => s.replace(/^boundary=/, ''))[0]\n\n  if (!boundary) {\n    return undefined\n  }\n\n  const boundaryRegExp = new RegExp(`--+${boundary}`)\n  const fields = data\n    .split(boundaryRegExp)\n    .filter((chunk) => chunk.startsWith('\\r\\n') && chunk.endsWith('\\r\\n'))\n    .map((chunk) => chunk.trimStart().replace(/\\r\\n$/, ''))\n\n  if (!fields.length) {\n    return undefined\n  }\n\n  const parsedBody: DefaultRequestMultipartBody = {}\n\n  try {\n    for (const field of fields) {\n      const [contentHeaders, ...rest] = field.split('\\r\\n\\r\\n')\n      const contentBody = rest.join('\\r\\n\\r\\n')\n      const { contentType, filename, name } =\n        parseContentHeaders(contentHeaders)\n\n      const value =\n        filename === undefined\n          ? contentBody\n          : new File([contentBody], filename, { type: contentType })\n\n      const parsedValue = parsedBody[name]\n\n      if (parsedValue === undefined) {\n        parsedBody[name] = value\n      } else if (Array.isArray(parsedValue)) {\n        parsedBody[name] = [...parsedValue, value]\n      } else {\n        parsedBody[name] = [parsedValue, value]\n      }\n    }\n\n    return parsedBody as T\n  } catch (error) {\n    return undefined\n  }\n}\n", "import { jsonParse } from '../internal/jsonParse'\nimport { parseMultipartData } from '../internal/parseMultipartData'\nimport { MockedRequest } from './MockedRequest'\n\n/**\n * Parses a given request/response body based on the \"Content-Type\" header.\n */\nexport function parseBody(body?: MockedRequest['body'], headers?: Headers) {\n  // Return whatever falsey body value is given.\n  if (!body) {\n    return body\n  }\n\n  const contentType = headers?.get('content-type')?.toLowerCase() || ''\n\n  // If the body has a Multipart Content-Type\n  // parse it into an object.\n  const hasMultipartContent = contentType.startsWith('multipart/form-data')\n  if (hasMultipartContent && typeof body !== 'object') {\n    return parseMultipartData(body.toString(), headers) || body\n  }\n\n  // If the intercepted request's body has a JSON Content-Type\n  // parse it into an object.\n  const hasJsonContent = contentType.includes('json')\n\n  if (hasJsonContent && typeof body !== 'object') {\n    return jsonParse(body.toString()) || body\n  }\n\n  // Otherwise leave as-is.\n  return body\n}\n", "/**\n * Performs a case-insensitive comparison of two given strings.\n */\nexport function isStringEqual(actual: string, expected: string): boolean {\n  return actual.toLowerCase() === expected.toLowerCase()\n}\n", "import { until } from '@open-draft/until'\nimport { StrictEventEmitter } from 'strict-event-emitter'\nimport { RequestHandler } from '../handlers/RequestHandler'\nimport { ServerLifecycleEventsMap } from '../node/glossary'\nimport { MockedResponse } from '../response'\nimport { SharedOptions } from '../sharedOptions'\nimport { RequiredDeep } from '../typeUtils'\nimport { ResponseLookupResult, getResponse } from './getResponse'\nimport { devUtils } from './internal/devUtils'\nimport { MockedRequest } from './request/MockedRequest'\nimport { onUnhandledRequest } from './request/onUnhandledRequest'\nimport { readResponseCookies } from './request/readResponseCookies'\n\nexport interface HandleRequestOptions<ResponseType> {\n  /**\n   * Options for the response resolution process.\n   */\n  resolutionContext?: {\n    baseUrl?: string\n  }\n\n  /**\n   * Transforms a `MockedResponse` instance returned from a handler\n   * to a response instance supported by the lower tooling (i.e. interceptors).\n   */\n  transformResponse?(response: MockedResponse<string>): ResponseType\n\n  /**\n   * Invoked whenever a request is performed as-is.\n   */\n  onPassthroughResponse?(request: MockedRequest): void\n\n  /**\n   * Invoked when the mocked response is ready to be sent.\n   */\n  onMockedResponse?(\n    response: ResponseType,\n    handler: RequiredDeep<ResponseLookupResult>,\n  ): void\n}\n\nexport async function handleRequest<\n  ResponseType extends Record<string, any> = MockedResponse<string>,\n>(\n  request: MockedRequest,\n  handlers: RequestHandler[],\n  options: RequiredDeep<SharedOptions>,\n  emitter: StrictEventEmitter<ServerLifecycleEventsMap>,\n  handleRequestOptions?: HandleRequestOptions<ResponseType>,\n): Promise<ResponseType | undefined> {\n  emitter.emit('request:start', request)\n\n  // Perform bypassed requests (i.e. issued via \"ctx.fetch\") as-is.\n  if (request.headers.get('x-msw-bypass') === 'true') {\n    emitter.emit('request:end', request)\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Resolve a mocked response from the list of request handlers.\n  const [lookupError, lookupResult] = await until(() => {\n    return getResponse(\n      request,\n      handlers,\n      handleRequestOptions?.resolutionContext,\n    )\n  })\n\n  if (lookupError) {\n    // Allow developers to react to unhandled exceptions in request handlers.\n    emitter.emit('unhandledException', lookupError, request)\n    throw lookupError\n  }\n\n  const { handler, response } = lookupResult\n\n  // When there's no handler for the request, consider it unhandled.\n  // Allow the developer to react to such cases.\n  if (!handler) {\n    onUnhandledRequest(request, handlers, options.onUnhandledRequest)\n    emitter.emit('request:unhandled', request)\n    emitter.emit('request:end', request)\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // When the handled request returned no mocked response, warn the developer,\n  // as it may be an oversight on their part. Perform the request as-is.\n  if (!response) {\n    devUtils.warn(\n      `\\\nExpected response resolver to return a mocked response Object, but got %s. The original response is going to be used instead.\\\n\\n\n  \\u2022 %s\n    %s\\\n`,\n      response,\n      handler.info.header,\n      handler.info.callFrame,\n    )\n\n    emitter.emit('request:end', request)\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // When the developer explicitly returned \"req.passthrough()\" do not warn them.\n  // Perform the request as-is.\n  if (response.passthrough) {\n    emitter.emit('request:end', request)\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Store all the received response cookies in the virtual cookie store.\n  readResponseCookies(request, response)\n\n  emitter.emit('request:match', request)\n\n  const requiredLookupResult =\n    lookupResult as RequiredDeep<ResponseLookupResult>\n\n  const transformedResponse =\n    handleRequestOptions?.transformResponse?.(response) ||\n    (response as any as ResponseType)\n\n  handleRequestOptions?.onMockedResponse?.(\n    transformedResponse,\n    requiredLookupResult,\n  )\n\n  emitter.emit('request:end', request)\n\n  return transformedResponse\n}\n", "import { MockedResponse } from '../response'\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  RequestHandlerExecutionResult,\n} from '../handlers/RequestHandler'\nimport { MockedRequest } from './request/MockedRequest'\n\nexport interface ResponseLookupResult {\n  handler?: RequestHandler\n  publicRequest?: any\n  parsedRequest?: any\n  response?: MockedResponse\n}\n\nexport interface ResponseResolutionContext {\n  baseUrl?: string\n}\n\n/**\n * Returns a mocked response for a given request using following request handlers.\n */\nexport const getResponse = async <\n  Request extends MockedRequest,\n  <PERSON><PERSON> extends RequestHandler[],\n>(\n  request: Request,\n  handlers: Handler,\n  resolutionContext?: ResponseResolutionContext,\n): Promise<ResponseLookupResult> => {\n  const relevantHandlers = handlers.filter((handler) => {\n    return handler.test(request, resolutionContext)\n  })\n\n  if (relevantHandlers.length === 0) {\n    return {\n      handler: undefined,\n      response: undefined,\n    }\n  }\n\n  const result = await relevantHandlers.reduce<\n    Promise<RequestHandlerExecutionResult<any> | null>\n  >(async (executionResult, handler) => {\n    const previousResults = await executionResult\n\n    if (!!previousResults?.response) {\n      return executionResult\n    }\n\n    const result = await handler.run(request, resolutionContext)\n\n    if (result === null || result.handler.shouldSkip) {\n      return null\n    }\n\n    if (!result.response) {\n      return {\n        request: result.request,\n        handler: result.handler,\n        response: undefined,\n        parsedResult: result.parsedResult,\n      }\n    }\n\n    if (result.response.once) {\n      handler.markAsSkipped(true)\n    }\n\n    return result\n  }, Promise.resolve(null))\n\n  // Although reducing a list of relevant request handlers, it's possible\n  // that in the end there will be no handler associted with the request\n  // (i.e. if relevant handlers are fall-through).\n  if (!result) {\n    return {\n      handler: undefined,\n      response: undefined,\n    }\n  }\n\n  return {\n    handler: result.handler,\n    publicRequest: result.request,\n    parsedRequest: result.parsedResult,\n    response: result.response,\n  }\n}\n", "import getStringMatchScore from 'js-le<PERSON>shtein'\nimport {\n  ParsedGraphQLQuery,\n  parseGraphQLRequest,\n} from '../internal/parseGraphQLRequest'\nimport { getPublicUrlFromRequest } from './getPublicUrlFromRequest'\nimport { isStringEqual } from '../internal/isStringEqual'\nimport { RestHandler } from '../../handlers/RestHandler'\nimport { GraphQLHandler } from '../../handlers/GraphQLHandler'\nimport { RequestHandler } from '../../handlers/RequestHandler'\nimport { tryCatch } from '../internal/tryCatch'\nimport { devUtils } from '../internal/devUtils'\nimport { MockedRequest } from './MockedRequest'\n\nconst MAX_MATCH_SCORE = 3\nconst MAX_SUGGESTION_COUNT = 4\nconst TYPE_MATCH_DELTA = 0.5\n\nexport interface UnhandledRequestPrint {\n  warning(): void\n  error(): void\n}\n\nexport type UnhandledRequestCallback = (\n  request: MockedRequest,\n  print: UnhandledRequestPrint,\n) => void\n\nexport type UnhandledRequestStrategy =\n  | 'bypass'\n  | 'warn'\n  | 'error'\n  | UnhandledRequestCallback\n\ninterface RequestHandlerGroups {\n  rest: RestHandler[]\n  graphql: GraphQLHandler[]\n}\n\nfunction groupHandlersByType(handlers: RequestHandler[]): RequestHandlerGroups {\n  return handlers.reduce<RequestHandlerGroups>(\n    (groups, handler) => {\n      if (handler instanceof RestHandler) {\n        groups.rest.push(handler)\n      }\n\n      if (handler instanceof GraphQLHandler) {\n        groups.graphql.push(handler)\n      }\n\n      return groups\n    },\n    {\n      rest: [],\n      graphql: [],\n    },\n  )\n}\n\ntype RequestHandlerSuggestion = [number, RequestHandler]\n\ntype ScoreGetterFn<RequestHandlerType extends RequestHandler> = (\n  request: MockedRequest,\n  handler: RequestHandlerType,\n) => number\n\nfunction getRestHandlerScore(): ScoreGetterFn<RestHandler> {\n  return (request, handler) => {\n    const { path, method } = handler.info\n\n    if (path instanceof RegExp || method instanceof RegExp) {\n      return Infinity\n    }\n\n    const hasSameMethod = isStringEqual(request.method, method)\n\n    // Always treat a handler with the same method as a more similar one.\n    const methodScoreDelta = hasSameMethod ? TYPE_MATCH_DELTA : 0\n    const requestPublicUrl = getPublicUrlFromRequest(request)\n    const score = getStringMatchScore(requestPublicUrl, path)\n\n    return score - methodScoreDelta\n  }\n}\n\nfunction getGraphQLHandlerScore(\n  parsedQuery: ParsedGraphQLQuery,\n): ScoreGetterFn<GraphQLHandler> {\n  return (_, handler) => {\n    if (typeof parsedQuery.operationName === 'undefined') {\n      return Infinity\n    }\n\n    const { operationType, operationName } = handler.info\n\n    if (typeof operationName !== 'string') {\n      return Infinity\n    }\n\n    const hasSameOperationType = parsedQuery.operationType === operationType\n    // Always treat a handler with the same operation type as a more similar one.\n    const operationTypeScoreDelta = hasSameOperationType ? TYPE_MATCH_DELTA : 0\n    const score = getStringMatchScore(parsedQuery.operationName, operationName)\n\n    return score - operationTypeScoreDelta\n  }\n}\n\nfunction getSuggestedHandler(\n  request: MockedRequest,\n  handlers: RestHandler[] | GraphQLHandler[],\n  getScore: ScoreGetterFn<RestHandler> | ScoreGetterFn<GraphQLHandler>,\n): RequestHandler[] {\n  const suggestedHandlers = (handlers as RequestHandler[])\n    .reduce<RequestHandlerSuggestion[]>((suggestions, handler) => {\n      const score = getScore(request, handler as any)\n      return suggestions.concat([[score, handler]])\n    }, [])\n    .sort(([leftScore], [rightScore]) => leftScore - rightScore)\n    .filter(([score]) => score <= MAX_MATCH_SCORE)\n    .slice(0, MAX_SUGGESTION_COUNT)\n    .map(([, handler]) => handler)\n\n  return suggestedHandlers\n}\n\nfunction getSuggestedHandlersMessage(handlers: RequestHandler[]) {\n  if (handlers.length > 1) {\n    return `\\\nDid you mean to request one of the following resources instead?\n\n${handlers.map((handler) => `  • ${handler.info.header}`).join('\\n')}`\n  }\n\n  return `Did you mean to request \"${handlers[0].info.header}\" instead?`\n}\n\nexport function onUnhandledRequest(\n  request: MockedRequest,\n  handlers: RequestHandler[],\n  strategy: UnhandledRequestStrategy = 'warn',\n): void {\n  const parsedGraphQLQuery = tryCatch(() => parseGraphQLRequest(request))\n\n  function generateHandlerSuggestion(): string {\n    /**\n     * @note Ignore exceptions during GraphQL request parsing because at this point\n     * we cannot assume the unhandled request is a valid GraphQL request.\n     * If the GraphQL parsing fails, just don't treat it as a GraphQL request.\n     */\n    const handlerGroups = groupHandlersByType(handlers)\n    const relevantHandlers = parsedGraphQLQuery\n      ? handlerGroups.graphql\n      : handlerGroups.rest\n\n    const suggestedHandlers = getSuggestedHandler(\n      request,\n      relevantHandlers,\n      parsedGraphQLQuery\n        ? getGraphQLHandlerScore(parsedGraphQLQuery)\n        : getRestHandlerScore(),\n    )\n\n    return suggestedHandlers.length > 0\n      ? getSuggestedHandlersMessage(suggestedHandlers)\n      : ''\n  }\n\n  function generateUnhandledRequestMessage(): string {\n    const publicUrl = getPublicUrlFromRequest(request)\n    const requestHeader = parsedGraphQLQuery\n      ? `${parsedGraphQLQuery.operationType} ${parsedGraphQLQuery.operationName} (${request.method} ${publicUrl})`\n      : `${request.method} ${publicUrl}`\n    const handlerSuggestion = generateHandlerSuggestion()\n\n    const messageTemplate = [\n      `captured a request without a matching request handler:`,\n      `  \\u2022 ${requestHeader}`,\n      handlerSuggestion,\n      `\\\nIf you still wish to intercept this unhandled request, please create a request handler for it.\nRead more: https://mswjs.io/docs/getting-started/mocks\\\n`,\n    ].filter(Boolean)\n    return messageTemplate.join('\\n\\n')\n  }\n\n  function applyStrategy(strategy: UnhandledRequestStrategy) {\n    // Generate handler suggestions only when applying the strategy.\n    // This saves bandwidth for scenarios when developers opt-out\n    // from the default unhandled request handling strategy.\n    const message = generateUnhandledRequestMessage()\n\n    switch (strategy) {\n      case 'error': {\n        // Print a developer-friendly error.\n        devUtils.error('Error: %s', message)\n\n        // Throw an exception to halt request processing and not perform the original request.\n        throw new Error(\n          devUtils.formatMessage(\n            'Cannot bypass a request when using the \"error\" strategy for the \"onUnhandledRequest\" option.',\n          ),\n        )\n      }\n\n      case 'warn': {\n        devUtils.warn('Warning: %s', message)\n        break\n      }\n\n      case 'bypass':\n        break\n\n      default:\n        throw new Error(\n          devUtils.formatMessage(\n            'Failed to react to an unhandled request: unknown strategy \"%s\". Please provide one of the supported strategies (\"bypass\", \"warn\", \"error\") or a custom callback function as the value of the \"onUnhandledRequest\" option.',\n            strategy,\n          ),\n        )\n    }\n  }\n\n  if (typeof strategy === 'function') {\n    strategy(request, {\n      warning: applyStrategy.bind(null, 'warn'),\n      error: applyStrategy.bind(null, 'error'),\n    })\n    return\n  }\n\n  applyStrategy(strategy)\n}\n", "import type {\n  DocumentNode,\n  OperationDefinitionNode,\n  OperationTypeNode,\n} from 'graphql'\nimport { parse } from 'graphql'\nimport { GraphQLVariables } from '../../handlers/GraphQLHandler'\nimport { getPublicUrlFromRequest } from '../request/getPublicUrlFromRequest'\nimport { MockedRequest } from '../request/MockedRequest'\nimport { devUtils } from './devUtils'\nimport { jsonParse } from './jsonParse'\n\ninterface GraphQLInput {\n  query: string | null\n  variables?: GraphQLVariables\n}\n\nexport interface ParsedGraphQLQuery {\n  operationType: OperationTypeNode\n  operationName?: string\n}\n\nexport type ParsedGraphQLRequest<\n  VariablesType extends GraphQLVariables = GraphQLVariables,\n> =\n  | (ParsedGraphQLQuery & {\n      variables?: VariablesType\n    })\n  | undefined\n\nexport function parseDocumentNode(node: DocumentNode): ParsedGraphQLQuery {\n  const operationDef = node.definitions.find((def) => {\n    return def.kind === 'OperationDefinition'\n  }) as OperationDefinitionNode\n\n  return {\n    operationType: operationDef?.operation,\n    operationName: operationDef?.name?.value,\n  }\n}\n\nfunction parseQuery(query: string): ParsedGraphQLQuery | Error {\n  try {\n    const ast = parse(query)\n    return parseDocumentNode(ast)\n  } catch (error) {\n    return error as Error\n  }\n}\n\nexport type GraphQLParsedOperationsMap = Record<string, string[]>\nexport type GraphQLMultipartRequestBody = {\n  operations: string\n  map?: string\n} & {\n  [fileName: string]: File\n}\n\nfunction extractMultipartVariables<VariablesType extends GraphQLVariables>(\n  variables: VariablesType,\n  map: GraphQLParsedOperationsMap,\n  files: Record<string, File>,\n) {\n  const operations = { variables }\n  for (const [key, pathArray] of Object.entries(map)) {\n    if (!(key in files)) {\n      throw new Error(`Given files do not have a key '${key}' .`)\n    }\n\n    for (const dotPath of pathArray) {\n      const [lastPath, ...reversedPaths] = dotPath.split('.').reverse()\n      const paths = reversedPaths.reverse()\n      let target: Record<string, any> = operations\n\n      for (const path of paths) {\n        if (!(path in target)) {\n          throw new Error(`Property '${paths}' is not in operations.`)\n        }\n\n        target = target[path]\n      }\n\n      target[lastPath] = files[key]\n    }\n  }\n  return operations.variables\n}\n\nfunction getGraphQLInput(request: MockedRequest<any>): GraphQLInput | null {\n  switch (request.method) {\n    case 'GET': {\n      const query = request.url.searchParams.get('query')\n      const variables = request.url.searchParams.get('variables') || ''\n\n      return {\n        query,\n        variables: jsonParse(variables),\n      }\n    }\n\n    case 'POST': {\n      if (request.body?.query) {\n        const { query, variables } = request.body\n\n        return {\n          query,\n          variables,\n        }\n      }\n\n      // Handle multipart body operations.\n      if (request.body?.operations) {\n        const { operations, map, ...files } =\n          request.body as GraphQLMultipartRequestBody\n        const parsedOperations =\n          jsonParse<{ query?: string; variables?: GraphQLVariables }>(\n            operations,\n          ) || {}\n\n        if (!parsedOperations.query) {\n          return null\n        }\n\n        const parsedMap = jsonParse<GraphQLParsedOperationsMap>(map || '') || {}\n        const variables = parsedOperations.variables\n          ? extractMultipartVariables(\n              parsedOperations.variables,\n              parsedMap,\n              files,\n            )\n          : {}\n\n        return {\n          query: parsedOperations.query,\n          variables,\n        }\n      }\n    }\n\n    default:\n      return null\n  }\n}\n\n/**\n * Determines if a given request can be considered a GraphQL request.\n * Does not parse the query and does not guarantee its validity.\n */\nexport function parseGraphQLRequest(\n  request: MockedRequest<any>,\n): ParsedGraphQLRequest {\n  const input = getGraphQLInput(request)\n\n  if (!input || !input.query) {\n    return undefined\n  }\n\n  const { query, variables } = input\n  const parsedResult = parseQuery(query)\n\n  if (parsedResult instanceof Error) {\n    const requestPublicUrl = getPublicUrlFromRequest(request)\n\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to intercept a GraphQL request to \"%s %s\": cannot parse query. See the error message from the parser below.\\n\\n%s',\n        request.method,\n        requestPublicUrl,\n        parsedResult.message,\n      ),\n    )\n  }\n\n  return {\n    operationType: parsedResult.operationType,\n    operationName: parsedResult.operationName,\n    variables,\n  }\n}\n", "import { MockedRequest } from './MockedRequest'\n\n/**\n * Returns a relative URL if the given request URL is relative to the current origin.\n * Otherwise returns an absolute URL.\n */\nexport const getPublicUrlFromRequest = (request: MockedRequest) => {\n  return request.referrer.startsWith(request.url.origin)\n    ? request.url.pathname\n    : new URL(\n        request.url.pathname,\n        `${request.url.protocol}//${request.url.host}`,\n      ).href\n}\n", "export enum StatusCodeColor {\n  Success = '#69AB32',\n  Warning = '#F0BB4B',\n  Danger = '#E95F5D',\n}\n\n/**\n * Returns a HEX color for a given response status code number.\n */\nexport function getStatusCodeColor(status: number): StatusCodeColor {\n  if (status < 300) {\n    return StatusCodeColor.Success\n  }\n\n  if (status < 400) {\n    return StatusCodeColor.Warning\n  }\n\n  return StatusCodeColor.Danger\n}\n", "/**\n * Returns a timestamp string in a \"HH:MM:SS\" format.\n */\nexport function getTimestamp(): string {\n  const now = new Date()\n\n  return [now.getHours(), now.getMinutes(), now.getSeconds()]\n    .map(String)\n    .map((chunk) => chunk.slice(0, 2))\n    .map((chunk) => chunk.padStart(2, '0'))\n    .join(':')\n}\n", "import type { DefaultBodyType } from '../../handlers/RequestHandler.js'\nimport type { MockedRequest } from '../request/MockedRequest.js'\n\nexport interface LoggedRequest {\n  id: string\n  url: URL\n  method: string\n  headers: Record<string, string>\n  cookies: Record<string, string>\n  body: DefaultBodyType\n}\n\n/**\n * Formats a mocked request for introspection in browser's console.\n */\nexport function prepareRequest(request: MockedRequest): LoggedRequest {\n  return {\n    ...request,\n    body: request.body,\n    headers: request.headers.all(),\n  }\n}\n", "import { objectToHeaders } from 'headers-polyfill'\nimport { SerializedResponse } from '../../setupWorker/glossary'\nimport { parseBody } from '../request/parseBody'\n\n/**\n * Formats a mocked response for introspection in the browser's console.\n */\nexport function prepareResponse(res: SerializedResponse<any>) {\n  const responseHeaders = objectToHeaders(res.headers)\n\n  return {\n    ...res,\n    // Parse a response JSON body for preview in the logs\n    body: parseBody(res.body, responseHeaders),\n  }\n}\n", "import { match } from 'path-to-regexp'\nimport { getCleanUrl } from '@mswjs/interceptors/lib/utils/getCleanUrl'\nimport { normalizePath } from './normalizePath'\n\nexport type Path = string | RegExp\nexport type PathParams<KeyType extends keyof any = string> = {\n  [ParamName in KeyType]: string | ReadonlyArray<string>\n}\n\nexport interface Match {\n  matches: boolean\n  params?: PathParams\n}\n\n/**\n * Coerce a path supported by MSW into a path\n * supported by \"path-to-regexp\".\n */\nexport function coercePath(path: string): string {\n  return (\n    path\n      /**\n       * Replace wildcards (\"*\") with unnamed capturing groups\n       * because \"path-to-regexp\" doesn't support wildcards.\n       * Ignore path parameter' modifiers (i.e. \":name*\").\n       */\n      .replace(\n        /([:a-zA-Z_-]*)(\\*{1,2})+/g,\n        (_, parameterName: string | undefined, wildcard: string) => {\n          const expression = '(.*)'\n\n          if (!parameterName) {\n            return expression\n          }\n\n          return parameterName.startsWith(':')\n            ? `${parameterName}${wildcard}`\n            : `${parameterName}${expression}`\n        },\n      )\n      /**\n       * Escape the port so that \"path-to-regexp\" can match\n       * absolute URLs including port numbers.\n       */\n      .replace(/([^\\/])(:)(?=\\d+)/, '$1\\\\$2')\n      /**\n       * Escape the protocol so that \"path-to-regexp\" could match\n       * absolute URL.\n       * @see https://github.com/pillarjs/path-to-regexp/issues/259\n       */\n      .replace(/^([^\\/]+)(:)(?=\\/\\/)/, '$1\\\\$2')\n  )\n}\n\n/**\n * Returns the result of matching given request URL against a mask.\n */\nexport function matchRequestUrl(url: URL, path: Path, baseUrl?: string): Match {\n  const normalizedPath = normalizePath(path, baseUrl)\n  const cleanPath =\n    typeof normalizedPath === 'string'\n      ? coercePath(normalizedPath)\n      : normalizedPath\n\n  const cleanUrl = getCleanUrl(url)\n  const result = match(cleanPath, { decode: decodeURIComponent })(cleanUrl)\n  const params = (result && (result.params as PathParams)) || {}\n\n  return {\n    matches: result !== false,\n    params,\n  }\n}\n", "const REDUNDANT_CHARACTERS_EXP = /[\\?|#].*$/g\n\nexport function getSearchParams(path: string) {\n  return new URL(`/${path}`, 'http://localhost').searchParams\n}\n\n/**\n * Removes query parameters and hashes from a given URL string.\n */\nexport function cleanUrl(path: string): string {\n  return path.replace(REDUNDANT_CHARACTERS_EXP, '')\n}\n", "/**\n * Determines if the given URL string is an absolute URL.\n */\nexport function isAbsoluteUrl(url: string): boolean {\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url)\n}\n", "import { isAbsoluteUrl } from './isAbsoluteUrl'\n\n/**\n * Returns an absolute URL based on the given path.\n */\nexport function getAbsoluteUrl(path: string, baseUrl?: string): string {\n  // already absolute URL\n  if (isAbsoluteUrl(path)) {\n    return path\n  }\n\n  // Ignore path with pattern start with *\n  if (path.startsWith('*')) {\n    return path\n  }\n\n  // Resolve a relative request URL against a given custom \"baseUrl\"\n  // or the document baseURI (in the case of browser/browser-like environments).\n  const origin =\n    baseUrl || (typeof document !== 'undefined' && document.baseURI)\n\n  return origin\n    ? // Encode and decode the path to preserve escaped characters.\n      decodeURI(new URL(encodeURI(path), origin).href)\n    : path\n}\n", "import type { Path } from './matchRequestUrl'\nimport { cleanUrl } from '../url/cleanUrl'\nimport { getAbsoluteUrl } from '../url/getAbsoluteUrl'\n\n/**\n * Normalizes a given request handler path:\n * - Preserves RegExp.\n * - Removes query parameters and hashes.\n * - Rebases relative URLs against the \"baseUrl\" or the current location.\n * - Preserves relative URLs in Node.js, unless specified otherwise.\n */\nexport function normalizePath(path: Path, baseUrl?: string): Path {\n  // RegExp paths do not need normalization.\n  if (path instanceof RegExp) {\n    return path\n  }\n\n  const maybeAbsoluteUrl = getAbsoluteUrl(path, baseUrl)\n\n  return cleanUrl(maybeAbsoluteUrl)\n}\n", "import { Headers } from 'headers-polyfill'\nimport {\n  MaybePromise,\n  MockedResponse,\n  response,\n  ResponseComposition,\n} from '../response'\nimport { getCallFrame } from '../utils/internal/getCallFrame'\nimport { isIterable } from '../utils/internal/isIterable'\nimport { status } from '../context/status'\nimport { set } from '../context/set'\nimport { delay } from '../context/delay'\nimport { fetch } from '../context/fetch'\nimport { ResponseResolutionContext } from '../utils/getResponse'\nimport { SerializedResponse } from '../setupWorker/glossary'\nimport { MockedRequest } from '../utils/request/MockedRequest'\n\nexport type DefaultContext = {\n  status: typeof status\n  set: typeof set\n  delay: typeof delay\n  fetch: typeof fetch\n}\n\nexport const defaultContext: DefaultContext = {\n  status,\n  set,\n  delay,\n  fetch,\n}\n\nexport type DefaultRequestMultipartBody = Record<\n  string,\n  string | File | (string | File)[]\n>\n\nexport type DefaultBodyType =\n  | Record<string, any>\n  | DefaultRequestMultipartBody\n  | string\n  | number\n  | boolean\n  | null\n  | undefined\n\nexport interface RequestHandlerDefaultInfo {\n  header: string\n}\n\nexport interface RequestHandlerInternalInfo {\n  callFrame?: string\n}\n\ntype ContextMap = Record<string, (...args: any[]) => any>\n\nexport type ResponseResolverReturnType<ReturnType> =\n  | ReturnType\n  | undefined\n  | void\n\nexport type MaybeAsyncResponseResolverReturnType<ReturnType> = MaybePromise<\n  ResponseResolverReturnType<ReturnType>\n>\n\nexport type AsyncResponseResolverReturnType<ReturnType> =\n  | MaybeAsyncResponseResolverReturnType<ReturnType>\n  | Generator<\n      MaybeAsyncResponseResolverReturnType<ReturnType>,\n      MaybeAsyncResponseResolverReturnType<ReturnType>,\n      MaybeAsyncResponseResolverReturnType<ReturnType>\n    >\n\nexport type ResponseResolver<\n  RequestType = MockedRequest,\n  ContextType = typeof defaultContext,\n  BodyType extends DefaultBodyType = any,\n> = (\n  req: RequestType,\n  res: ResponseComposition<BodyType>,\n  context: ContextType,\n) => AsyncResponseResolverReturnType<MockedResponse<BodyType>>\n\nexport interface RequestHandlerOptions<HandlerInfo> {\n  info: HandlerInfo\n  resolver: ResponseResolver<any, any>\n  ctx?: ContextMap\n}\n\nexport interface RequestHandlerExecutionResult<PublicRequestType> {\n  handler: RequestHandler\n  parsedResult: any\n  request: PublicRequestType\n  response?: MockedResponse\n}\n\nexport abstract class RequestHandler<\n  HandlerInfo extends RequestHandlerDefaultInfo = RequestHandlerDefaultInfo,\n  Request extends MockedRequest = MockedRequest,\n  ParsedResult = any,\n  PublicRequest extends MockedRequest = Request,\n> {\n  public info: HandlerInfo & RequestHandlerInternalInfo\n  public shouldSkip: boolean\n\n  private ctx: ContextMap\n  private resolverGenerator?: Generator<\n    MaybeAsyncResponseResolverReturnType<any>,\n    MaybeAsyncResponseResolverReturnType<any>,\n    MaybeAsyncResponseResolverReturnType<any>\n  >\n  private resolverGeneratorResult?: MaybeAsyncResponseResolverReturnType<any>\n\n  protected resolver: ResponseResolver<any, any>\n\n  constructor(options: RequestHandlerOptions<HandlerInfo>) {\n    this.shouldSkip = false\n    this.ctx = options.ctx || defaultContext\n    this.resolver = options.resolver\n\n    const callFrame = getCallFrame(new Error())\n\n    this.info = {\n      ...options.info,\n      callFrame,\n    }\n  }\n\n  /**\n   * Determine if the captured request should be mocked.\n   */\n  abstract predicate(\n    request: MockedRequest,\n    parsedResult: ParsedResult,\n    resolutionContext?: ResponseResolutionContext,\n  ): boolean\n\n  /**\n   * Print out the successfully handled request.\n   */\n  abstract log(\n    request: Request,\n    response: SerializedResponse<any>,\n    parsedResult: ParsedResult,\n  ): void\n\n  /**\n   * Parse the captured request to extract additional information from it.\n   * Parsed result is then exposed to other methods of this request handler.\n   */\n  parse(\n    _request: MockedRequest,\n    _resolutionContext?: ResponseResolutionContext,\n  ): ParsedResult {\n    return null as any\n  }\n\n  /**\n   * Test if this handler matches the given request.\n   */\n  public test(\n    request: MockedRequest,\n    resolutionContext?: ResponseResolutionContext,\n  ): boolean {\n    return this.predicate(\n      request,\n      this.parse(request, resolutionContext),\n      resolutionContext,\n    )\n  }\n\n  /**\n   * Derive the publicly exposed request (`req`) instance of the response resolver\n   * from the captured request and its parsed result.\n   */\n  protected getPublicRequest(\n    request: MockedRequest,\n    _parsedResult: ParsedResult,\n  ) {\n    return request as PublicRequest\n  }\n\n  public markAsSkipped(shouldSkip = true) {\n    this.shouldSkip = shouldSkip\n  }\n\n  /**\n   * Execute this request handler and produce a mocked response\n   * using the given resolver function.\n   */\n  public async run(\n    request: MockedRequest,\n    resolutionContext?: ResponseResolutionContext,\n  ): Promise<RequestHandlerExecutionResult<PublicRequest> | null> {\n    if (this.shouldSkip) {\n      return null\n    }\n\n    const parsedResult = this.parse(request, resolutionContext)\n    const shouldIntercept = this.predicate(\n      request,\n      parsedResult,\n      resolutionContext,\n    )\n\n    if (!shouldIntercept) {\n      return null\n    }\n\n    const publicRequest = this.getPublicRequest(request, parsedResult)\n\n    // Create a response extraction wrapper around the resolver\n    // since it can be both an async function and a generator.\n    const executeResolver = this.wrapResolver(this.resolver)\n    const mockedResponse = await executeResolver(\n      publicRequest,\n      response,\n      this.ctx,\n    )\n\n    return this.createExecutionResult(\n      parsedResult,\n      publicRequest,\n      mockedResponse,\n    )\n  }\n\n  private wrapResolver(\n    resolver: ResponseResolver<any, any>,\n  ): ResponseResolver<AsyncResponseResolverReturnType<any>, any> {\n    return async (req, res, ctx) => {\n      const result = this.resolverGenerator || (await resolver(req, res, ctx))\n\n      if (isIterable<AsyncResponseResolverReturnType<any>>(result)) {\n        const { value, done } = result[Symbol.iterator]().next()\n        const nextResponse = await value\n\n        // If the generator is done and there is no next value,\n        // return the previous generator's value.\n        if (!nextResponse && done) {\n          return this.resolverGeneratorResult\n        }\n\n        if (!this.resolverGenerator) {\n          this.resolverGenerator = result\n        }\n\n        this.resolverGeneratorResult = nextResponse\n        return nextResponse\n      }\n\n      return result\n    }\n  }\n\n  private createExecutionResult(\n    parsedResult: ParsedResult,\n    request: PublicRequest,\n    response: any,\n  ): RequestHandlerExecutionResult<PublicRequest> {\n    return {\n      handler: this,\n      parsedResult: parsedResult || null,\n      request,\n      response: response || null,\n    }\n  }\n}\n\n/**\n * Bypass this intercepted request.\n * This will make a call to the actual endpoint requested.\n */\nexport function passthrough(): MockedResponse<null> {\n  // Constructing a dummy \"101 Continue\" mocked response\n  // to keep the return type of the resolver consistent.\n  return {\n    status: 101,\n    statusText: 'Continue',\n    headers: new Headers(),\n    body: null,\n    // Setting \"passthrough\" to true will signal the response pipeline\n    // to perform this intercepted request as-is.\n    passthrough: true,\n    once: false,\n  }\n}\n", "import { Headers } from 'headers-polyfill'\nimport { DefaultBodyType } from './handlers/RequestHandler'\nimport { compose } from './utils/internal/compose'\nimport { NetworkError } from './utils/NetworkError'\n\nexport type MaybePromise<ValueType = any> = ValueType | Promise<ValueType>\n\n/**\n * Internal representation of a mocked response instance.\n */\nexport interface MockedResponse<BodyType extends DefaultBodyType = any> {\n  body: BodyType\n  status: number\n  statusText: string\n  headers: Headers\n  once: boolean\n  passthrough: boolean\n  delay?: number\n}\n\nexport type ResponseTransformer<\n  BodyType extends TransformerBodyType = any,\n  TransformerBodyType extends DefaultBodyType = any,\n> = (\n  res: MockedResponse<TransformerBodyType>,\n) => MaybePromise<MockedResponse<BodyType>>\n\nexport type ResponseFunction<BodyType extends DefaultBodyType = any> = (\n  ...transformers: ResponseTransformer<BodyType>[]\n) => MaybePromise<MockedResponse<BodyType>>\n\nexport type ResponseComposition<BodyType extends DefaultBodyType = any> =\n  ResponseFunction<BodyType> & {\n    /**\n     * Respond using a given mocked response to the first captured request.\n     * Does not affect any subsequent captured requests.\n     */\n    once: ResponseFunction<BodyType>\n    networkError: (message: string) => void\n  }\n\nexport const defaultResponse: Omit<MockedResponse, 'headers'> = {\n  status: 200,\n  statusText: 'OK',\n  body: null,\n  delay: 0,\n  once: false,\n  passthrough: false,\n}\n\nexport type ResponseCompositionOptions<BodyType> = {\n  defaultTransformers?: ResponseTransformer<BodyType>[]\n  mockedResponseOverrides?: Partial<MockedResponse>\n}\n\nexport const defaultResponseTransformers: ResponseTransformer<any>[] = []\n\nexport function createResponseComposition<BodyType extends DefaultBodyType>(\n  responseOverrides?: Partial<MockedResponse<BodyType>>,\n  defaultTransformers: ResponseTransformer<BodyType>[] = defaultResponseTransformers,\n): ResponseFunction {\n  return async (...transformers) => {\n    const initialResponse: MockedResponse = Object.assign(\n      {},\n      defaultResponse,\n      {\n        headers: new Headers({\n          'x-powered-by': 'msw',\n        }),\n      },\n      responseOverrides,\n    )\n\n    const resolvedTransformers = [\n      ...defaultTransformers,\n      ...transformers,\n    ].filter(Boolean)\n\n    const resolvedResponse =\n      resolvedTransformers.length > 0\n        ? compose(...resolvedTransformers)(initialResponse)\n        : initialResponse\n\n    return resolvedResponse\n  }\n}\n\nexport const response = Object.assign(createResponseComposition(), {\n  once: createResponseComposition({ once: true }),\n  networkError(message: string) {\n    throw new NetworkError(message)\n  },\n})\n", "type ArityOneFunction = (arg: any) => any\n\ntype LengthOfTuple<Tuple extends any[]> = <PERSON>ple extends { length: infer L }\n  ? L\n  : never\n\ntype DropFirstInTuple<Tuple extends any[]> = ((...args: <PERSON>ple) => any) extends (\n  arg: any,\n  ...rest: infer LastArg\n) => any\n  ? LastArg\n  : Tuple\n\ntype LastInTuple<Tuple extends any[]> = Tuple[LengthOfTuple<\n  DropFirstInTuple<Tuple>\n>]\n\ntype FirstFnParameterType<Functions extends ArityOneFunction[]> = Parameters<\n  LastInTuple<Functions>\n>[any]\n\ntype LastFnParameterType<Functions extends ArityOneFunction[]> = ReturnType<\n  Functions[0]\n>\n\n/**\n * Composes a given list of functions into a new function that\n * executes from right to left.\n */\nexport function compose<\n  Functions extends ArityOneFunction[],\n  LeftReturnType extends FirstFnParameterType<Functions>,\n  RightReturnType extends LastFnParameterType<Functions>,\n>(\n  ...fns: Functions\n): (\n  ...args: [LeftReturnType] extends [never] ? never[] : [LeftReturnType]\n) => RightReturnType {\n  return (...args) => {\n    return fns.reduceRight((leftFn: any, rightFn) => {\n      return leftFn instanceof Promise\n        ? Promise.resolve(leftFn).then(rightFn)\n        : rightFn(leftFn)\n    }, args[0])\n  }\n}\n", "// Ignore the source files traces for local testing.\nconst SOURCE_FRAME = /[\\/\\\\]msw[\\/\\\\]src[\\/\\\\](.+)/\n\nconst BUILD_FRAME =\n  /(node_modules)?[\\/\\\\]lib[\\/\\\\](umd|esm|iief|cjs)[\\/\\\\]|^[^\\/\\\\]*$/\n\n/**\n * Return the stack trace frame of a function's invocation.\n */\nexport function getCallFrame(error: Error) {\n  // In <IE11, new Error may return an undefined stack\n  const stack = error.stack\n\n  if (!stack) {\n    return\n  }\n\n  const frames: string[] = stack.split('\\n').slice(1)\n\n  // Get the first frame that doesn't reference the library's internal trace.\n  // Assume that frame is the invocation frame.\n  const declarationFrame = frames.find((frame) => {\n    return !(SOURCE_FRAME.test(frame) || BUILD_FRAME.test(frame))\n  })\n\n  if (!declarationFrame) {\n    return\n  }\n\n  // Extract file reference from the stack frame.\n  const declarationPath = declarationFrame\n    .replace(/\\s*at [^()]*\\(([^)]+)\\)/, '$1')\n    .replace(/^@/, '')\n  return declarationPath\n}\n", "/**\n * Determines if the given function is an iterator.\n */\nexport function isIterable<IteratorType>(\n  fn: any,\n): fn is Generator<IteratorType, IteratorType, IteratorType> {\n  if (!fn) {\n    return false\n  }\n\n  return typeof (fn as Generator<unknown>)[Symbol.iterator] == 'function'\n}\n", "import { body, cookie, json, text, xml } from '../context'\nimport type { SerializedResponse } from '../setupWorker/glossary'\nimport { ResponseResolutionContext } from '../utils/getResponse'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { isStringEqual } from '../utils/internal/isStringEqual'\nimport { getStatusCodeColor } from '../utils/logging/getStatusCodeColor'\nimport { getTimestamp } from '../utils/logging/getTimestamp'\nimport { prepareRequest } from '../utils/logging/prepareRequest'\nimport { prepareResponse } from '../utils/logging/prepareResponse'\nimport {\n  Match,\n  matchRequestUrl,\n  Path,\n  PathParams,\n} from '../utils/matching/matchRequestUrl'\nimport { getPublicUrlFromRequest } from '../utils/request/getPublicUrlFromRequest'\nimport { MockedRequest } from '../utils/request/MockedRequest'\nimport { cleanUrl, getSearchParams } from '../utils/url/cleanUrl'\nimport {\n  DefaultBodyType,\n  defaultContext,\n  DefaultContext,\n  RequestHandler,\n  RequestHandlerDefaultInfo,\n  ResponseResolver,\n} from './RequestHandler'\n\ntype RestHandlerMethod = string | RegExp\n\nexport interface RestHandlerInfo extends RequestHandlerDefaultInfo {\n  method: RestHandlerMethod\n  path: Path\n}\n\nexport enum RESTMethods {\n  HEAD = 'HEAD',\n  GET = 'GET',\n  POST = 'POST',\n  PUT = 'PUT',\n  PATCH = 'PATCH',\n  OPTIONS = 'OPTIONS',\n  DELETE = 'DELETE',\n}\n\n// Declaring a context interface infers\n// JSDoc description of the referenced utils.\nexport type RestContext = DefaultContext & {\n  cookie: typeof cookie\n  text: typeof text\n  body: typeof body\n  json: typeof json\n  xml: typeof xml\n}\n\nexport const restContext: RestContext = {\n  ...defaultContext,\n  cookie,\n  body,\n  text,\n  json,\n  xml,\n}\n\nexport type RequestQuery = {\n  [queryName: string]: string\n}\n\nexport type ParsedRestRequest = Match\n\nexport class RestRequest<\n  RequestBody extends DefaultBodyType = DefaultBodyType,\n  RequestParams extends PathParams = PathParams,\n> extends MockedRequest<RequestBody> {\n  constructor(\n    request: MockedRequest<RequestBody>,\n    public readonly params: RequestParams,\n  ) {\n    super(request.url, {\n      ...request,\n      /**\n       * @deprecated https://github.com/mswjs/msw/issues/1318\n       * @note Use internal request body buffer as the body init\n       * because \"request.body\" is a getter that will trigger\n       * request body parsing at this step.\n       */\n      body: request['_body'],\n    })\n    this.id = request.id\n  }\n}\n\n/**\n * Request handler for REST API requests.\n * Provides request matching based on method and URL.\n */\nexport class RestHandler<\n  RequestType extends MockedRequest<DefaultBodyType> = MockedRequest<DefaultBodyType>,\n> extends RequestHandler<\n  RestHandlerInfo,\n  RequestType,\n  ParsedRestRequest,\n  RestRequest<\n    RequestType extends MockedRequest<infer RequestBodyType>\n      ? RequestBodyType\n      : any,\n    PathParams\n  >\n> {\n  constructor(\n    method: RestHandlerMethod,\n    path: Path,\n    resolver: ResponseResolver<any, any>,\n  ) {\n    super({\n      info: {\n        header: `${method} ${path}`,\n        path,\n        method,\n      },\n      ctx: restContext,\n      resolver,\n    })\n\n    this.checkRedundantQueryParameters()\n  }\n\n  private checkRedundantQueryParameters() {\n    const { method, path } = this.info\n\n    if (path instanceof RegExp) {\n      return\n    }\n\n    const url = cleanUrl(path)\n\n    // Bypass request handler URLs that have no redundant characters.\n    if (url === path) {\n      return\n    }\n\n    const searchParams = getSearchParams(path)\n    const queryParams: string[] = []\n\n    searchParams.forEach((_, paramName) => {\n      queryParams.push(paramName)\n    })\n\n    devUtils.warn(\n      `Found a redundant usage of query parameters in the request handler URL for \"${method} ${path}\". Please match against a path instead and access query parameters in the response resolver function using \"req.url.searchParams\".`,\n    )\n  }\n\n  parse(request: RequestType, resolutionContext?: ResponseResolutionContext) {\n    return matchRequestUrl(\n      request.url,\n      this.info.path,\n      resolutionContext?.baseUrl,\n    )\n  }\n\n  protected getPublicRequest(\n    request: RequestType,\n    parsedResult: ParsedRestRequest,\n  ): RestRequest<any, PathParams> {\n    return new RestRequest(request, parsedResult.params || {})\n  }\n\n  predicate(request: RequestType, parsedResult: ParsedRestRequest) {\n    const matchesMethod =\n      this.info.method instanceof RegExp\n        ? this.info.method.test(request.method)\n        : isStringEqual(this.info.method, request.method)\n\n    return matchesMethod && parsedResult.matches\n  }\n\n  log(request: RequestType, response: SerializedResponse<any>) {\n    const publicUrl = getPublicUrlFromRequest(request)\n    const loggedRequest = prepareRequest(request)\n    const loggedResponse = prepareResponse(response)\n    const statusColor = getStatusCodeColor(response.status)\n\n    console.groupCollapsed(\n      devUtils.formatMessage('%s %s %s (%c%s%c)'),\n      getTimestamp(),\n      request.method,\n      publicUrl,\n      `color:${statusColor}`,\n      `${response.status} ${response.statusText}`,\n      'color:inherit',\n    )\n    console.log('Request', loggedRequest)\n    console.log('Handler:', this)\n    console.log('Response', loggedResponse)\n    console.groupEnd()\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { ResponseTransformer } from '../response'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { jsonParse } from '../utils/internal/jsonParse'\nimport { mergeRight } from '../utils/internal/mergeRight'\nimport { json } from './json'\n\ntype ForbiddenFieldNames = '' | 'data' | 'errors' | 'extensions'\n\n/**\n * Set a custom field on the GraphQL mocked response.\n * @example res(ctx.fields('customField', value))\n * @see {@link https://mswjs.io/docs/api/context/field}\n */\nexport const field = <FieldNameType extends string, FieldValueType>(\n  fieldName: FieldNameType extends ForbiddenFieldNames ? never : FieldNameType,\n  fieldValue: FieldValueType,\n): ResponseTransformer<string> => {\n  return (res) => {\n    validateFieldName(fieldName)\n\n    const prevBody = jsonParse(res.body) || {}\n    const nextBody = mergeRight(prevBody, { [fieldName]: fieldValue })\n\n    return json(nextBody)(res as any) as any\n  }\n}\n\nfunction validateFieldName(fieldName: string) {\n  invariant(\n    fieldName.trim() !== '',\n    devUtils.formatMessage(\n      'Failed to set a custom field on a GraphQL response: field name cannot be empty.',\n    ),\n  )\n\n  invariant(\n    fieldName !== 'data',\n    devUtils.formatMessage(\n      'Failed to set a custom \"%s\" field on a mocked GraphQL response: forbidden field name. Did you mean to call \"ctx.data()\" instead?',\n      fieldName,\n    ),\n  )\n\n  invariant(\n    fieldName !== 'errors',\n    devUtils.formatMessage(\n      'Failed to set a custom \"%s\" field on a mocked GraphQL response: forbidden field name. Did you mean to call \"ctx.errors()\" instead?',\n      fieldName,\n    ),\n  )\n\n  invariant(\n    fieldName !== 'extensions',\n    devUtils.formatMessage(\n      'Failed to set a custom \"%s\" field on a mocked GraphQL response: forbidden field name. Did you mean to call \"ctx.extensions()\" instead?',\n      fieldName,\n    ),\n  )\n}\n", "export function tryCatch<Fn extends (...args: any[]) => any>(\n  fn: Fn,\n  onException?: (error: Error) => void,\n): ReturnType<Fn> | undefined {\n  try {\n    const result = fn()\n    return result\n  } catch (error) {\n    onException?.(error as Error)\n  }\n}\n", "import type { DocumentNode, OperationTypeNode } from 'graphql'\nimport { SerializedResponse } from '../setupWorker/glossary'\nimport { data } from '../context/data'\nimport { extensions } from '../context/extensions'\nimport { errors } from '../context/errors'\nimport { field } from '../context/field'\nimport { GraphQLPayloadContext } from '../typeUtils'\nimport { cookie } from '../context/cookie'\nimport {\n  defaultContext,\n  DefaultContext,\n  RequestHandler,\n  RequestHandlerDefaultInfo,\n  ResponseResolver,\n} from './RequestHandler'\nimport { getTimestamp } from '../utils/logging/getTimestamp'\nimport { getStatusCodeColor } from '../utils/logging/getStatusCodeColor'\nimport { prepareRequest } from '../utils/logging/prepareRequest'\nimport { prepareResponse } from '../utils/logging/prepareResponse'\nimport { matchRequestUrl, Path } from '../utils/matching/matchRequestUrl'\nimport {\n  ParsedGraphQLRequest,\n  GraphQLMultipartRequestBody,\n  parseGraphQLRequest,\n  parseDocumentNode,\n} from '../utils/internal/parseGraphQLRequest'\nimport { getPublicUrlFromRequest } from '../utils/request/getPublicUrlFromRequest'\nimport { tryCatch } from '../utils/internal/tryCatch'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { MockedRequest } from '../utils/request/MockedRequest'\n\nexport type ExpectedOperationTypeNode = OperationTypeNode | 'all'\nexport type GraphQLHandlerNameSelector = DocumentNode | RegExp | string\n\n// GraphQL related context should contain utility functions\n// useful for GraphQL. Functions like `xml()` bear no value\n// in the GraphQL universe.\nexport type GraphQLContext<QueryType extends Record<string, unknown>> =\n  DefaultContext & {\n    data: GraphQLPayloadContext<QueryType>\n    extensions: GraphQLPayloadContext<QueryType>\n    errors: typeof errors\n    cookie: typeof cookie\n    field: typeof field\n  }\n\nexport const graphqlContext: GraphQLContext<any> = {\n  ...defaultContext,\n  data,\n  extensions,\n  errors,\n  cookie,\n  field,\n}\n\nexport type GraphQLVariables = Record<string, any>\n\nexport interface GraphQLHandlerInfo extends RequestHandlerDefaultInfo {\n  operationType: ExpectedOperationTypeNode\n  operationName: GraphQLHandlerNameSelector\n}\n\nexport type GraphQLRequestBody<VariablesType extends GraphQLVariables> =\n  | GraphQLJsonRequestBody<VariablesType>\n  | GraphQLMultipartRequestBody\n  | Record<string, any>\n  | undefined\n\nexport interface GraphQLJsonRequestBody<Variables extends GraphQLVariables> {\n  query: string\n  variables?: Variables\n}\n\nexport function isDocumentNode(\n  value: DocumentNode | any,\n): value is DocumentNode {\n  if (value == null) {\n    return false\n  }\n\n  return typeof value === 'object' && 'kind' in value && 'definitions' in value\n}\n\nexport class GraphQLRequest<\n  Variables extends GraphQLVariables,\n> extends MockedRequest<GraphQLRequestBody<Variables>> {\n  constructor(request: MockedRequest, public readonly variables: Variables) {\n    super(request.url, {\n      ...request,\n      /**\n       * TODO(https://github.com/mswjs/msw/issues/1318): Cleanup\n       */\n      body: request['_body'],\n    })\n  }\n}\n\nexport class GraphQLHandler<\n  Request extends GraphQLRequest<any> = GraphQLRequest<any>,\n> extends RequestHandler<\n  GraphQLHandlerInfo,\n  Request,\n  ParsedGraphQLRequest | null,\n  GraphQLRequest<any>\n> {\n  private endpoint: Path\n\n  constructor(\n    operationType: ExpectedOperationTypeNode,\n    operationName: GraphQLHandlerNameSelector,\n    endpoint: Path,\n    resolver: ResponseResolver<any, any>,\n  ) {\n    let resolvedOperationName = operationName\n\n    if (isDocumentNode(operationName)) {\n      const parsedNode = parseDocumentNode(operationName)\n\n      if (parsedNode.operationType !== operationType) {\n        throw new Error(\n          `Failed to create a GraphQL handler: provided a DocumentNode with a mismatched operation type (expected \"${operationType}\", but got \"${parsedNode.operationType}\").`,\n        )\n      }\n\n      if (!parsedNode.operationName) {\n        throw new Error(\n          `Failed to create a GraphQL handler: provided a DocumentNode with no operation name.`,\n        )\n      }\n\n      resolvedOperationName = parsedNode.operationName\n    }\n\n    const header =\n      operationType === 'all'\n        ? `${operationType} (origin: ${endpoint.toString()})`\n        : `${operationType} ${resolvedOperationName} (origin: ${endpoint.toString()})`\n\n    super({\n      info: {\n        header,\n        operationType,\n        operationName: resolvedOperationName,\n      },\n      ctx: graphqlContext,\n      resolver,\n    })\n\n    this.endpoint = endpoint\n  }\n\n  parse(request: MockedRequest) {\n    return tryCatch(\n      () => parseGraphQLRequest(request),\n      (error) => console.error(error.message),\n    )\n  }\n\n  protected getPublicRequest(\n    request: Request,\n    parsedResult: ParsedGraphQLRequest,\n  ): GraphQLRequest<any> {\n    return new GraphQLRequest(request, parsedResult?.variables || {})\n  }\n\n  predicate(request: MockedRequest, parsedResult: ParsedGraphQLRequest) {\n    if (!parsedResult) {\n      return false\n    }\n\n    if (!parsedResult.operationName && this.info.operationType !== 'all') {\n      const publicUrl = getPublicUrlFromRequest(request)\n      devUtils.warn(`\\\nFailed to intercept a GraphQL request at \"${request.method} ${publicUrl}\": anonymous GraphQL operations are not supported.\n\nConsider naming this operation or using \"graphql.operation\" request handler to intercept GraphQL requests regardless of their operation name/type. Read more: https://mswjs.io/docs/api/graphql/operation\\\n      `)\n      return false\n    }\n\n    const hasMatchingUrl = matchRequestUrl(request.url, this.endpoint)\n    const hasMatchingOperationType =\n      this.info.operationType === 'all' ||\n      parsedResult.operationType === this.info.operationType\n\n    const hasMatchingOperationName =\n      this.info.operationName instanceof RegExp\n        ? this.info.operationName.test(parsedResult.operationName || '')\n        : parsedResult.operationName === this.info.operationName\n\n    return (\n      hasMatchingUrl.matches &&\n      hasMatchingOperationType &&\n      hasMatchingOperationName\n    )\n  }\n\n  log(\n    request: Request,\n    response: SerializedResponse<any>,\n    parsedRequest: ParsedGraphQLRequest,\n  ) {\n    const loggedRequest = prepareRequest(request)\n    const loggedResponse = prepareResponse(response)\n    const statusColor = getStatusCodeColor(response.status)\n    const requestInfo = parsedRequest?.operationName\n      ? `${parsedRequest?.operationType} ${parsedRequest?.operationName}`\n      : `anonymous ${parsedRequest?.operationType}`\n\n    console.groupCollapsed(\n      devUtils.formatMessage('%s %s (%c%s%c)'),\n      getTimestamp(),\n      `${requestInfo}`,\n      `color:${statusColor}`,\n      `${response.status} ${response.statusText}`,\n      'color:inherit',\n    )\n    console.log('Request:', loggedRequest)\n    console.log('Handler:', this)\n    console.log('Response:', loggedResponse)\n    console.groupEnd()\n  }\n}\n", "import { store } from '@mswjs/cookies'\nimport { MockedResponse } from '../../response'\nimport { MockedRequest } from './MockedRequest'\n\nexport function readResponseCookies(\n  request: MockedRequest,\n  response: MockedResponse,\n) {\n  store.add({ ...request, url: request.url.toString() }, response)\n  store.persist()\n}\n", "import { flattenHeadersObject, headersToObject } from 'headers-polyfill'\nimport type { SerializedResponse } from '../../setupWorker/glossary'\n\nexport function serializeResponse(source: Response): SerializedResponse<any> {\n  return {\n    status: source.status,\n    statusText: source.statusText,\n    headers: flattenHeadersObject(headersToObject(source.headers)),\n    body: source.body,\n  }\n}\n", "import {\n  StartOptions,\n  SerializedResponse,\n  SetupWorkerInternalContext,\n  ServiceWorkerIncomingEventsMap,\n} from '../glossary'\nimport {\n  ServiceWorkerMessage,\n  WorkerChannel,\n} from './utils/createMessageChannel'\nimport { NetworkError } from '../../utils/NetworkError'\nimport { parseWorkerRequest } from '../../utils/request/parseWorkerRequest'\nimport { handleRequest } from '../../utils/handleRequest'\nimport { RequiredDeep } from '../../typeUtils'\nimport { MockedResponse } from '../../response'\nimport { devUtils } from '../../utils/internal/devUtils'\nimport { serializeResponse } from '../../utils/logging/serializeResponse'\n\nexport const createRequestListener = (\n  context: SetupWorkerInternalContext,\n  options: RequiredDeep<StartOptions>,\n) => {\n  return async (\n    event: MessageEvent,\n    message: ServiceWorkerMessage<\n      'REQUEST',\n      ServiceWorkerIncomingEventsMap['REQUEST']\n    >,\n  ) => {\n    const messageChannel = new WorkerChannel(event.ports[0])\n    const request = parseWorkerRequest(message.payload)\n\n    try {\n      await handleRequest<SerializedResponse>(\n        request,\n        context.requestHandlers,\n        options,\n        context.emitter,\n        {\n          transformResponse,\n          onPassthroughResponse() {\n            messageChannel.postMessage('NOT_FOUND')\n          },\n          async onMockedResponse(\n            response,\n            { handler, publicRequest, parsedRequest },\n          ) {\n            if (response.body instanceof ReadableStream) {\n              throw new Error(\n                devUtils.formatMessage(\n                  'Failed to construct a mocked response with a \"ReadableStream\" body: mocked streams are not supported. Follow https://github.com/mswjs/msw/issues/1336 for more details.',\n                ),\n              )\n            }\n\n            const responseInstance = new Response(response.body, response)\n            const responseBodyBuffer = await responseInstance.arrayBuffer()\n\n            // If the mocked response has no body, keep it that way.\n            // Sending an empty \"ArrayBuffer\" to the worker will cause\n            // the worker constructing \"new Response(new ArrayBuffer(0))\"\n            // which will throw on responses that must have no body (i.e. 204).\n            const responseBody =\n              response.body == null ? null : responseBodyBuffer\n\n            messageChannel.postMessage(\n              'MOCK_RESPONSE',\n              {\n                ...response,\n                body: responseBody,\n              },\n              [responseBodyBuffer],\n            )\n\n            if (!options.quiet) {\n              context.emitter.once('response:mocked', (response) => {\n                handler.log(\n                  publicRequest,\n                  serializeResponse(response),\n                  parsedRequest,\n                )\n              })\n            }\n          },\n        },\n      )\n    } catch (error) {\n      if (error instanceof NetworkError) {\n        // Treat emulated network error differently,\n        // as it is an intended exception in a request handler.\n        messageChannel.postMessage('NETWORK_ERROR', {\n          name: error.name,\n          message: error.message,\n        })\n\n        return\n      }\n\n      if (error instanceof Error) {\n        devUtils.error(\n          `Uncaught exception in the request handler for \"%s %s\":\n\n%s\n\nThis exception has been gracefully handled as a 500 response, however, it's strongly recommended to resolve this error, as it indicates a mistake in your code. If you wish to mock an error response, please see this guide: https://mswjs.io/docs/recipes/mocking-error-responses`,\n          request.method,\n          request.url,\n          error.stack ?? error,\n        )\n\n        // Treat all other exceptions in a request handler as unintended,\n        // alerting that there is a problem that needs fixing.\n        messageChannel.postMessage('MOCK_RESPONSE', {\n          status: 500,\n          statusText: 'Request Handler Error',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            name: error.name,\n            message: error.message,\n            stack: error.stack,\n          }),\n        })\n      }\n    }\n  }\n}\n\nfunction transformResponse(\n  response: MockedResponse<string>,\n): SerializedResponse<string> {\n  return {\n    status: response.status,\n    statusText: response.statusText,\n    headers: response.headers.all(),\n    body: response.body,\n    delay: response.delay,\n  }\n}\n", "import { SetupWorkerInternalContext } from '../../setupWorker/glossary'\n\nexport async function requestIntegrityCheck(\n  context: SetupWorkerInternalContext,\n  serviceWorker: ServiceWorker,\n): Promise<ServiceWorker> {\n  // Signal Service Worker to report back its integrity\n  context.workerChannel.send('INTEGRITY_CHECK_REQUEST')\n\n  const { payload: actualChecksum } = await context.events.once(\n    'INTEGRITY_CHECK_RESPONSE',\n  )\n\n  // Compare the response from the Service Worker and the\n  // global variable set during the build.\n  if (actualChecksum !== SERVICE_WORKER_CHECKSUM) {\n    throw new Error(\n      `Currently active Service Worker (${actualChecksum}) is behind the latest published one (${SERVICE_WORKER_CHECKSUM}).`,\n    )\n  }\n\n  return serviceWorker\n}\n", "import { until } from '@open-draft/until'\n\n/**\n * Intercepts and defers any requests on the page\n * until the Service Worker instance is ready.\n * Must only be used in a browser.\n */\nexport function deferNetworkRequestsUntil(predicatePromise: Promise<any>) {\n  // Defer any `XMLHttpRequest` requests until the Service Worker is ready.\n  const originalXhrSend = window.XMLHttpRequest.prototype.send\n  window.XMLHttpRequest.prototype.send = function (\n    ...args: Parameters<XMLHttpRequest['send']>\n  ) {\n    // Keep this function synchronous to comply with `XMLHttpRequest.prototype.send`,\n    // because that method is always synchronous.\n    until(() => predicatePromise).then(() => {\n      window.XMLHttpRequest.prototype.send = originalXhrSend\n      this.send(...args)\n    })\n  }\n\n  // Defer any `fetch` requests until the Service Worker is ready.\n  const originalFetch = window.fetch\n  window.fetch = async (...args) => {\n    await until(() => predicatePromise)\n    window.fetch = originalFetch\n    return window.fetch(...args)\n  }\n}\n", "import {\n  ServiceWorkerIncomingEventsMap,\n  SetupWorkerInternalContext,\n} from '../../setupWorker/glossary'\nimport { ServiceWorkerMessage } from './utils/createMessageChannel'\n\nexport function createResponseListener(context: SetupWorkerInternalContext) {\n  return (\n    _: MessageEvent,\n    message: ServiceWorkerMessage<\n      'RESPONSE',\n      ServiceWorkerIncomingEventsMap['RESPONSE']\n    >,\n  ) => {\n    const { payload: responseJson } = message\n\n    /**\n     * CORS requests with `mode: \"no-cors\"` result in \"opaque\" responses.\n     * That kind of responses cannot be manipulated in JavaScript due\n     * to the security considerations.\n     * @see https://fetch.spec.whatwg.org/#concept-filtered-response-opaque\n     * @see https://github.com/mswjs/msw/issues/529\n     */\n    if (responseJson.type?.includes('opaque')) {\n      return\n    }\n\n    const response = new Response(responseJson.body || null, responseJson)\n    const isMockedResponse = response.headers.get('x-powered-by') === 'msw'\n\n    if (isMockedResponse) {\n      context.emitter.emit('response:mocked', response, responseJson.requestId)\n    } else {\n      context.emitter.emit('response:bypass', response, responseJson.requestId)\n    }\n  }\n}\n", "import { devUtils } from '../../../utils/internal/devUtils'\nimport { StartOptions } from '../../glossary'\n\nexport function validateWorkerScope(\n  registration: ServiceWorkerRegistration,\n  options?: StartOptions,\n): void {\n  if (!options?.quiet && !location.href.startsWith(registration.scope)) {\n    devUtils.warn(\n      `\\\nCannot intercept requests on this page because it's outside of the worker's scope (\"${registration.scope}\"). If you wish to mock API requests on this page, you must resolve this scope issue.\n\n- (Recommended) Register the worker at the root level (\"/\") of your application.\n- Set the \"Service-Worker-Allowed\" response header to allow out-of-scope workers.\\\n`,\n    )\n  }\n}\n", "import { devUtils } from '../../../utils/internal/devUtils'\n\nexport function printStopMessage(args: { quiet?: boolean } = {}): void {\n  if (args.quiet) {\n    return\n  }\n\n  console.log(\n    `%c${devUtils.formatMessage('Mocking disabled.')}`,\n    'color:orangered;font-weight:bold;',\n  )\n}\n", "import { devUtils } from '../../utils/internal/devUtils'\nimport { SetupWorkerInternalContext, StopHandler } from '../glossary'\nimport { printStopMessage } from './utils/printStopMessage'\n\nexport const createStop = (\n  context: SetupWorkerInternalContext,\n): StopHandler => {\n  return function stop() {\n    // Warn developers calling \"worker.stop()\" more times than necessary.\n    // This likely indicates a mistake in their code.\n    if (!context.isMockingEnabled) {\n      devUtils.warn(\n        'Found a redundant \"worker.stop()\" call. Note that stopping the worker while mocking already stopped has no effect. Consider removing this \"worker.stop()\" call.',\n      )\n      return\n    }\n\n    /**\n     * Signal the Service Worker to disable mocking for this client.\n     * Use this an an explicit way to stop the mocking, while preserving\n     * the worker-client relation. Does not affect the worker's lifecycle.\n     */\n    context.workerChannel.send('MOCK_DEACTIVATE')\n    context.isMockingEnabled = false\n    window.clearInterval(context.keepAliveInterval)\n\n    printStopMessage({ quiet: context.startOptions?.quiet })\n  }\n}\n", "import { RequestHandler } from '../../handlers/RequestHandler'\n\nexport function use(\n  currentHandlers: RequestHandler[],\n  ...handlers: RequestHandler[]\n): void {\n  currentHandlers.unshift(...handlers)\n}\n\nexport function restoreHandlers(handlers: RequestHandler[]): void {\n  handlers.forEach((handler) => {\n    handler.markAsSkipped(false)\n  })\n}\n\nexport function resetHandlers(\n  initialHandlers: RequestHandler[],\n  ...nextHandlers: RequestHandler[]\n) {\n  return nextHandlers.length > 0 ? [...nextHandlers] : [...initialHandlers]\n}\n", "import { RequiredDeep } from '../../../typeUtils'\nimport { mergeRight } from '../../../utils/internal/mergeRight'\nimport {\n  SetupWorkerApi,\n  SetupWorkerInternalContext,\n  StartHandler,\n  StartOptions,\n} from '../../glossary'\n\nexport const DEFAULT_START_OPTIONS: RequiredDeep<StartOptions> = {\n  serviceWorker: {\n    url: '/mockServiceWorker.js',\n    options: null as any,\n  },\n  quiet: false,\n  waitUntilReady: true,\n  onUnhandledRequest: 'warn',\n  findWorker(scriptURL, mockServiceWorkerUrl) {\n    return scriptURL === mockServiceWorkerUrl\n  },\n}\n\n/**\n * Returns resolved worker start options, merging the default options\n * with the given custom options.\n */\nexport function resolveStartOptions(\n  initialOptions?: StartOptions,\n): RequiredDeep<StartOptions> {\n  return mergeRight(\n    DEFAULT_START_OPTIONS,\n    initialOptions || {},\n  ) as RequiredDeep<StartOptions>\n}\n\nexport function prepareStartHandler(\n  handler: <PERSON>H<PERSON><PERSON>,\n  context: SetupWorkerInternalContext,\n): SetupWorkerApi['start'] {\n  return (initialOptions) => {\n    context.startOptions = resolveStartOptions(initialOptions)\n    return handler(context.startOptions, initialOptions || {})\n  }\n}\n", "import {\n  Inter<PERSON>,\n  BatchInterceptor,\n  HttpRequestEventMap,\n} from '@mswjs/interceptors'\nimport { FetchInterceptor } from '@mswjs/interceptors/lib/interceptors/fetch'\nimport { XMLHttpRequestInterceptor } from '@mswjs/interceptors/lib/interceptors/XMLHttpRequest'\nimport {\n  SerializedResponse,\n  SetupWorkerInternalContext,\n  StartOptions,\n} from '../glossary'\nimport type { RequiredDeep } from '../../typeUtils'\nimport { handleRequest } from '../../utils/handleRequest'\nimport { MockedRequest } from '../../utils/request/MockedRequest'\nimport { serializeResponse } from '../../utils/logging/serializeResponse'\nimport { createResponseFromIsomorphicResponse } from '../../utils/request/createResponseFromIsomorphicResponse'\n\nexport function createFallbackRequestListener(\n  context: SetupWorkerInternalContext,\n  options: RequiredDeep<StartOptions>,\n): Interceptor<HttpRequestEventMap> {\n  const interceptor = new BatchInterceptor({\n    name: 'fallback',\n    interceptors: [new FetchInterceptor(), new XMLHttpRequestInterceptor()],\n  })\n\n  interceptor.on('request', async (request) => {\n    const mockedRequest = new MockedRequest(request.url, {\n      ...request,\n      body: await request.arrayBuffer(),\n    })\n\n    const response = await handleRequest<SerializedResponse>(\n      mockedRequest,\n      context.requestHandlers,\n      options,\n      context.emitter,\n      {\n        transformResponse(response) {\n          return {\n            status: response.status,\n            statusText: response.statusText,\n            headers: response.headers.all(),\n            body: response.body,\n            delay: response.delay,\n          }\n        },\n        onMockedResponse(_, { handler, publicRequest, parsedRequest }) {\n          if (!options.quiet) {\n            context.emitter.once('response:mocked', (response) => {\n              handler.log(\n                publicRequest,\n                serializeResponse(response),\n                parsedRequest,\n              )\n            })\n          }\n        },\n      },\n    )\n\n    if (response) {\n      request.respondWith(response)\n    }\n  })\n\n  interceptor.on('response', (request, response) => {\n    if (!request.id) {\n      return\n    }\n\n    const browserResponse = createResponseFromIsomorphicResponse(response)\n\n    if (response.headers.get('x-powered-by') === 'msw') {\n      context.emitter.emit('response:mocked', browserResponse, request.id)\n    } else {\n      context.emitter.emit('response:bypass', browserResponse, request.id)\n    }\n  })\n\n  interceptor.apply()\n\n  return interceptor\n}\n", "import { encodeBuffer, IsomorphicResponse } from '@mswjs/interceptors'\n\nconst noop = () => {\n  throw new Error('Not implemented')\n}\n\nexport function createResponseFromIsomorphicResponse(\n  response: IsomorphicResponse,\n): Response {\n  return {\n    ...response,\n    ok: response.status >= 200 && response.status < 300,\n    url: '',\n    type: 'default',\n    status: response.status,\n    statusText: response.statusText,\n    headers: response.headers,\n    body: new ReadableStream(),\n    redirected: response.headers.get('Location') != null,\n    async text() {\n      return response.body || ''\n    },\n    async json() {\n      return JSON.parse(response.body || '')\n    },\n    async arrayBuffer() {\n      return encodeBuffer(response.body || '')\n    },\n    bodyUsed: false,\n    formData: noop,\n    blob: noop,\n    clone: noop,\n  }\n}\n", "import { createFallbackRequestListener } from './createFallbackRequestListener'\nimport { SetupWorkerInternalContext, StartHandler } from '../glossary'\nimport { printStartMessage } from './utils/printStartMessage'\n\nexport function createFallbackStart(\n  context: SetupWorkerInternalContext,\n): StartHandler {\n  return async function start(options) {\n    context.fallbackInterceptor = createFallbackRequestListener(\n      context,\n      options,\n    )\n\n    printStartMessage({\n      message: 'Mocking enabled (fallback mode).',\n      quiet: options.quiet,\n    })\n\n    return undefined\n  }\n}\n", "import { SetupWorkerInternalContext, StopHandler } from '../glossary'\nimport { printStopMessage } from './utils/printStopMessage'\n\nexport function createFallbackStop(\n  context: SetupWorkerInternalContext,\n): StopHandler {\n  return function stop() {\n    context.fallbackInterceptor?.dispose()\n    printStopMessage({ quiet: context.startOptions?.quiet })\n  }\n}\n", "import { EventEmitter } from 'stream'\n\n/**\n * Pipes all emitted events from one emitter to another.\n */\nexport function pipeEvents(\n  source: EventEmitter,\n  destination: EventEmitter,\n): void {\n  const rawEmit = source.emit\n\n  // @ts-ignore\n  if (rawEmit._isPiped) {\n    return\n  }\n\n  source.emit = function (event, ...data) {\n    destination.emit(event, ...data)\n    return rawEmit.call(this, event, ...data)\n  }\n\n  // @ts-ignore\n  source.emit._isPiped = true\n}\n", "/**\n * Creates an immutable copy of the given array.\n */\nexport function toReadonlyArray<T>(source: Array<T>): ReadonlyArray<T> {\n  const clone = [...source] as Array<T>\n  Object.freeze(clone)\n  return clone\n}\n", "import { DefaultBodyType, ResponseResolver } from './handlers/RequestHandler'\nimport {\n  RESTMethods,\n  RestContext,\n  RestHandler,\n  RestRequest,\n} from './handlers/RestHandler'\nimport { Path, PathParams } from './utils/matching/matchRequestUrl'\n\nfunction createRestHandler<Method extends RESTMethods | RegExp>(\n  method: Method,\n) {\n  return <\n    RequestBodyType extends DefaultBodyType = DefaultBodyType,\n    <PERSON>ms extends PathParams<keyof Params> = PathParams,\n    ResponseBody extends DefaultBodyType = DefaultBodyType,\n  >(\n    path: Path,\n    resolver: ResponseResolver<\n      RestRequest<\n        Method extends RESTMethods.HEAD | RESTMethods.GET\n          ? never\n          : RequestBodyType,\n        Params\n      >,\n      RestContext,\n      ResponseBody\n    >,\n  ) => {\n    return new RestHandler(method, path, resolver)\n  }\n}\n\nexport const rest = {\n  all: createRestHandler(/.+/),\n  head: createRestHandler(RESTMethods.HEAD),\n  get: createRestHandler(RESTMethods.GET),\n  post: createRestHandler(RESTMethods.POST),\n  put: createRestHandler(RESTMethods.PUT),\n  delete: createRestHandler(RESTMethods.DELETE),\n  patch: createRestHandler(RESTMethods.PATCH),\n  options: createRestHandler(RESTMethods.OPTIONS),\n}\n", "import type { DocumentNode, OperationTypeNode } from 'graphql'\nimport { ResponseResolver } from './handlers/RequestHandler'\nimport {\n  GraphQLHandler,\n  GraphQLContext,\n  GraphQLRequest,\n  GraphQLVariables,\n  ExpectedOperationTypeNode,\n  GraphQLHandlerNameSelector,\n} from './handlers/GraphQLHandler'\nimport { Path } from './utils/matching/matchRequestUrl'\n\nexport interface TypedDocumentNode<\n  Result = { [key: string]: any },\n  Variables = { [key: string]: any },\n> extends DocumentNode {\n  __apiType?: (variables: Variables) => Result\n  __resultType?: Result\n  __variablesType?: Variables\n}\n\nfunction createScopedGraphQLHandler(\n  operationType: ExpectedOperationTypeNode,\n  url: Path,\n) {\n  return <\n    Query extends Record<string, any>,\n    Variables extends GraphQLVariables = GraphQLVariables,\n  >(\n    operationName:\n      | GraphQLHandlerNameSelector\n      | DocumentNode\n      | TypedDocumentNode<Query, Variables>,\n    resolver: ResponseResolver<\n      GraphQLRequest<Variables>,\n      GraphQLContext<Query>\n    >,\n  ) => {\n    return new GraphQLHandler<GraphQLRequest<Variables>>(\n      operationType,\n      operationName,\n      url,\n      resolver,\n    )\n  }\n}\n\nfunction createGraphQLOperationHandler(url: Path) {\n  return <\n    Query extends Record<string, any>,\n    Variables extends GraphQLVariables = GraphQLVariables,\n  >(\n    resolver: ResponseResolver<\n      GraphQLRequest<Variables>,\n      GraphQLContext<Query>\n    >,\n  ) => {\n    return new GraphQLHandler<GraphQLRequest<Variables>>(\n      'all',\n      new RegExp('.*'),\n      url,\n      resolver,\n    )\n  }\n}\n\nconst standardGraphQLHandlers = {\n  /**\n   * Captures any GraphQL operation, regardless of its name, under the current scope.\n   * @example\n   * graphql.operation((req, res, ctx) => {\n   *   return res(ctx.data({ name: 'John' }))\n   * })\n   * @see {@link https://mswjs.io/docs/api/graphql/operation `graphql.operation()`}\n   */\n  operation: createGraphQLOperationHandler('*'),\n\n  /**\n   * Captures a GraphQL query by a given name.\n   * @example\n   * graphql.query('GetUser', (req, res, ctx) => {\n   *   return res(ctx.data({ user: { name: 'John' } }))\n   * })\n   * @see {@link https://mswjs.io/docs/api/graphql/query `graphql.query()`}\n   */\n  query: createScopedGraphQLHandler('query' as OperationTypeNode, '*'),\n\n  /**\n   * Captures a GraphQL mutation by a given name.\n   * @example\n   * graphql.mutation('SavePost', (req, res, ctx) => {\n   *   return res(ctx.data({ post: { id: 'abc-123' } }))\n   * })\n   * @see {@link https://mswjs.io/docs/api/graphql/mutation `graphql.mutation()`}\n   */\n  mutation: createScopedGraphQLHandler('mutation' as OperationTypeNode, '*'),\n}\n\nfunction createGraphQLLink(url: Path): typeof standardGraphQLHandlers {\n  return {\n    operation: createGraphQLOperationHandler(url),\n    query: createScopedGraphQLHandler('query' as OperationTypeNode, url),\n    mutation: createScopedGraphQLHandler('mutation' as OperationTypeNode, url),\n  }\n}\n\nexport const graphql = {\n  ...standardGraphQLHandlers,\n  link: createGraphQLLink,\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,mBAAqB;AAUd,IAAM,SAAS,CACpB,YACA,eACwB;AACxB,SAAO,CAAC,QAAQ;AACd,QAAI,SAAS;AACb,QAAI,aACF,cAAc,qBAAS,OAAO,UAAU;AAE1C,WAAO;AAAA,EACT;AACF;;;ACrBA,8BAAgC;AA8BzB,gBACF,MASkB;AACrB,SAAO,CAAC,QAAQ;AACd,UAAM,CAAC,MAAM,SAAS;AAEtB,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,QAAQ,OAAO,MAAM,KAAe;AAAA,IAC1C,OAAO;AACL,YAAM,UAAU,6CAAgB,IAAI;AACpC,cAAQ,QAAQ,CAAC,QAAO,UAAS;AAC/B,YAAI,QAAQ,OAAO,OAAM,MAAK;AAAA,MAChC,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;;;ACvDA,kBAA6B;AAOtB,IAAM,SAAS,CACpB,MACA,OACA,YACwB;AACxB,SAAO,CAAC,QAAQ;AACd,UAAM,mBAAmB,AAAY,sBAAU,MAAM,OAAO,OAAO;AACnE,QAAI,QAAQ,OAAO,cAAc,gBAAgB;AAEjD,QAAI,OAAO,aAAa,aAAa;AACnC,eAAS,SAAS;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AACF;;;ACbO,IAAM,OAAO,CAGlB,UACkC;AAClC,SAAO,CAAC,QAAQ;AACd,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;;;ACdO,mBACL,OACuB;AACvB,MAAI;AACF,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB,SAAS,QAAP;AACA,WAAO;AAAA,EACT;AACF;;;ACTO,kBAAkB,OAAqB;AAC5C,SAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK;AAC3E;;;ACCO,oBACL,MACA,OACA;AACA,SAAO,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AACjE,UAAM,YAAY,OAAO;AAEzB,QAAI,MAAM,QAAQ,SAAS,KAAK,MAAM,QAAQ,UAAU,GAAG;AACzD,aAAO,OAAO,UAAU,OAAO,UAAU;AACzC,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,SAAS,KAAK,SAAS,UAAU,GAAG;AAC/C,aAAO,OAAO,WAAW,WAAW,UAAU;AAC9C,aAAO;AAAA,IACT;AAEA,WAAO,OAAO;AACd,WAAO;AAAA,EACT,GAAG,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC;AAC5B;;;ACdO,IAAM,OAAO,CAClB,UACsC;AACtC,SAAO,CAAC,QAAQ;AACd,QAAI,QAAQ,IAAI,gBAAgB,kBAAkB;AAClD,QAAI,OAAO,KAAK,UAAU,KAAI;AAE9B,WAAO;AAAA,EACT;AACF;;;ACVO,IAAM,OAAuD,CAClE,YACG;AACH,SAAO,CAAC,QAAQ;AACd,UAAM,WAAW,UAAU,IAAI,IAAI,KAAK,CAAC;AACzC,UAAM,WAAW,WAAW,UAAU,EAAE,MAAM,QAAQ,CAAC;AAEvD,WAAO,KAAK,QAAQ,EAAE,GAAG;AAAA,EAC3B;AACF;;;ACTO,IAAM,aAA6D,CACxE,YACG;AACH,SAAO,CAAC,QAAQ;AACd,UAAM,WAAW,UAAU,IAAI,IAAI,KAAK,CAAC;AACzC,UAAM,WAAW,WAAW,UAAU,EAAE,YAAY,QAAQ,CAAC;AAC7D,WAAO,KAAK,QAAQ,EAAE,GAAG;AAAA,EAC3B;AACF;;;ACnBA,6BAA8B;AAGvB,IAAM,8BAA8B;AACpC,IAAM,2BAA2B;AACjC,IAAM,2BAA2B;AACjC,IAAM,4BAA4B;AAEzC,IAAM,8BAA8B,MAAM;AACxC,MAAI,0CAAc,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MACV,KAAK,OAAO,IAAK,4BAA2B,4BAC1C,wBACJ;AACF;AAYO,IAAM,QAAQ,CACnB,mBACwB;AACxB,SAAO,CAAC,QAAQ;AACd,QAAI;AAEJ,QAAI,OAAO,mBAAmB,UAAU;AACtC,cAAQ;AAAA,aACD,YAAY;AAGf,sBAAY;AACZ;AAAA,QACF;AAAA,aACK,QAAQ;AACX,sBAAY,4BAA4B;AACxC;AAAA,QACF;AAAA,iBACS;AACP,gBAAM,IAAI,MACR,mDAAmD,2HACrD;AAAA,QACF;AAAA;AAAA,IAEJ,WAAW,OAAO,mBAAmB,aAAa;AAEhD,kBAAY,4BAA4B;AAAA,IAC1C,OAAO;AAGL,UAAI,iBAAiB,6BAA6B;AAChD,cAAM,IAAI,MACR,wDAAwD,0EAA0E,wPACpI;AAAA,MACF;AAEA,kBAAY;AAAA,IACd;AAEA,QAAI,QAAQ;AACZ,WAAO;AAAA,EACT;AACF;;;AC5DO,IAAM,SAAS,CAGpB,eACgC;AAChC,SAAO,CAAC,QAAQ;AACd,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,UAAU,IAAI,IAAI,KAAK,CAAC;AACzC,UAAM,WAAW,WAAW,UAAU,EAAE,QAAQ,WAAW,CAAC;AAE5D,WAAO,KAAK,QAAQ,EAAE,GAAU;AAAA,EAClC;AACF;;;AC1BA,8BAA8B;AAC9B,+BAAwB;AAGxB,IAAM,WACJ,2CAAc,IAAI,QAAQ,gBAAgB,OAAO;AAE5C,IAAM,qBAAqB,CAAC,gBAA0C;AAC3E,QAAM,UAAU,IAAI,iCAAQ,YAAY,OAAO;AAC/C,UAAQ,IAAI,gBAAgB,MAAM;AAElC,SAAO,iCACF,cADE;AAAA,IAEL,SAAS,QAAQ,IAAI;AAAA,EACvB;AACF;AAEA,IAAM,+BAA+B,CAAC,UAAsC;AAC1E,QAAM,EAAE,aAAM,WAAW;AACzB,QAAM,oBAAiC,iCAClC,QADkC;AAAA,IAErC,MAAM;AAAA,EACR;AAEA,MAAI,CAAC,OAAO,MAAM,EAAE,SAAS,MAAM,GAAG;AACpC,WAAO;AAAA,EACT;AAEA,MACE,OAAO,UAAS,YAChB,OAAO,UAAS,YAChB,OAAO,UAAS,WAChB;AACA,sBAAkB,OAAO,KAAK,UAAU,KAAI;AAAA,EAC9C,OAAO;AACL,sBAAkB,OAAO;AAAA,EAC3B;AAEA,SAAO;AACT;AAQO,IAAM,QAAQ,CACnB,OACA,cAA2B,CAAC,MACN;AACtB,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,SAAS,OAAO,mBAAmB,WAAW,CAAC;AAAA,EACxD;AAEA,QAAM,oBAAoB,6BAA6B,KAAK;AAC5D,QAAM,qBAAqB,mBAAmB,iBAAiB;AAE/D,SAAO,SAAS,MAAM,IAAI,MAAM,kBAAkB;AACpD;;;ACnDO,IAAM,OAAO,CAClB,UACkC;AAClC,SAAO,CAAC,QAAQ;AACd,QAAI,QAAQ,IAAI,gBAAgB,YAAY;AAC5C,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;;;ACPO,IAAM,MAAM,CACjB,UACkC;AAClC,SAAO,CAAC,QAAQ;AACd,QAAI,QAAQ,IAAI,gBAAgB,UAAU;AAC1C,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;;;ACjBA,8BAA8B;AAC9B,kCAAmC;;;ACDnC,oBAAsB;;;ACAtB,mBAAsB;;;ACMf,IAAM,0BAA0B,CACrC,cACA,mBACA,eACyB;AACzB,QAAM,YAAY;AAAA,IAChB,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,EACf;AACA,QAAM,iBAAiB,UAAU,OAAO,OAAO;AAC/C,QAAM,aAAa,eAAe,KAAK,CAAC,WAAW;AACjD,WAAO,WAAW,OAAO,WAAW,iBAAiB;AAAA,EACvD,CAAC;AAED,SAAO,cAAc;AACvB;;;AClBO,8BAA8B,aAA6B;AAChE,SAAO,IAAI,IAAI,aAAa,SAAS,MAAM,EAAE;AAC/C;;;ACNA,wBAAuB;AAEvB,IAAM,iBAAiB;AAKvB,uBAAuB,YAAoB,aAA4B;AACrE,QAAM,sBAAsB,8BAAO,SAAS,GAAG,WAAW;AAC1D,SAAO,GAAG,kBAAkB;AAC9B;AAKA,cAAc,YAAoB,aAA0B;AAC1D,UAAQ,KAAK,cAAc,SAAS,GAAG,WAAW,CAAC;AACrD;AAKA,eAAe,YAAoB,aAA0B;AAC3D,UAAQ,MAAM,cAAc,SAAS,GAAG,WAAW,CAAC;AACtD;AAEO,IAAM,WAAW;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF;;;AHpBO,IAAM,oBAAoB,OAC/B,KACA,UAA+B,CAAC,GAChC,eACwC;AAExC,QAAM,oBAAoB,qBAAqB,GAAG;AAElD,QAAM,oBAAoB,MAAM,UAAU,cACvC,iBAAiB,EACjB,KAAK,CAAC,kBACL,cAAc,OAAO,CAAC,iBACpB,wBAAwB,cAAc,mBAAmB,UAAU,CACrE,CACF;AACF,MAAI,CAAC,UAAU,cAAc,cAAc,kBAAkB,SAAS,GAAG;AAOvE,aAAS,OAAO;AAAA,EAClB;AAEA,QAAM,CAAC,wBAAwB;AAE/B,MAAI,sBAAsB;AAExB,WAAO,qBAAqB,OAAO,EAAE,KAAK,MAAM;AAC9C,aAAO;AAAA,QACL,wBACE,sBACA,mBACA,UACF;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAM,CAAC,QAAO,YAAY,MAAM,wBAC9B,YAAY;AACV,UAAM,eAAe,MAAM,UAAU,cAAc,SAAS,KAAK,OAAO;AACxE,WAAO;AAAA,MAGL,wBAAwB,cAAc,mBAAmB,UAAU;AAAA,MACnE;AAAA,IACF;AAAA,EACF,CACF;AAGA,MAAI,QAAO;AACT,UAAM,kBAAkB,OAAM,QAAQ,SAAS,OAAO;AAItD,QAAI,iBAAiB;AACnB,YAAM,WAAW,IAAI,IAAI,oCAAS,UAAS,KAAK,SAAS,IAAI;AAE7D,YAAM,IAAI,MACR,SAAS,cAAc,mDACmB,SAAS,wBAAwB;AAAA;AAAA;AAAA;AAAA,oFAIC,CAC9E;AAAA,IACF;AAGA,UAAM,IAAI,MACR,SAAS,cACP,gDACA,OAAM,OACR,CACF;AAAA,EACF;AAEA,SAAO;AACT;;;AIjFO,2BAA2B,OAA8B,CAAC,GAAG;AAClE,MAAI,KAAK,OAAO;AACd;AAAA,EACF;AAEA,QAAM,UAAU,KAAK,WAAW;AAEhC,UAAQ,eACN,KAAK,SAAS,cAAc,OAAO,KACnC,mCACF;AACA,UAAQ,IACN,4CACA,oBACA,oBACF;AACA,UAAQ,IAAI,qDAAqD;AAEjE,MAAI,KAAK,WAAW;AAClB,YAAQ,IAAI,sBAAsB,KAAK,SAAS;AAAA,EAClD;AAEA,MAAI,KAAK,aAAa;AACpB,YAAQ,IAAI,iBAAiB,KAAK,WAAW;AAAA,EAC/C;AAEA,UAAQ,SAAS;AACnB;;;AChCA,6BACE,SACA,SACA;AAVF;AAWE,UAAQ,cAAc,KAAK,eAAe;AAC1C,QAAM,QAAQ,OAAO,KAAK,iBAAiB;AAK3C,MAAI,QAAQ,kBAAkB;AAC5B,aAAS,KACP,4KACF;AACA;AAAA,EACF;AAEA,UAAQ,mBAAmB;AAE3B,oBAAkB;AAAA,IAChB,OAAO,QAAQ;AAAA,IACf,aAAa,cAAQ,iBAAR,mBAAsB;AAAA,IACnC,WAAW,cAAQ,WAAR,mBAAgB;AAAA,EAC7B,CAAC;AACH;;;ACZO,0BAAoB;AAAA,EACzB,YAA6B,MAAmB;AAAnB;AAAA,EAAoB;AAAA,EAE1C,YACL,UACG,OACG;AACN,UAAM,CAAC,OAAM,YAAY;AACzB,SAAK,KAAK,YAAY,EAAE,MAAM,OAAO,YAAK,GAAG,EAAE,SAAS,CAAC;AAAA,EAC3D;AACF;;;AC7BO,iCAA2B,MAAM;AAAA,EACtC,YAAY,SAAiB;AAC3B,UAAM,OAAO;AACb,SAAK,OAAO;AAAA,EACd;AACF;;;ACLA,2BAA6B;AAC7B,+BAAwB;;;ACDxB,mBAA6B;AAC7B,qBAAsB;AACtB,0BAA+C;AAC/C,yBAA6B;AAC7B,+BAAwB;;;ACJxB,mBAA6B;AAG7B,yBAAyB;AACvB,SAAO,AAAY,mBAAM,SAAS,MAAM;AAC1C;AAKO,2BAA2B,SAAwB;AAIxD,MAAI,OAAO,aAAa,eAAe,OAAO,aAAa,aAAa;AACtE,WAAO,CAAC;AAAA,EACV;AAEA,UAAQ,QAAQ;AAAA,SACT,eAAe;AAGlB,aAAO,SAAS,WAAW,QAAQ,IAAI,SAAS,cAAc,IAAI,CAAC;AAAA,IACrE;AAAA,SAEK,WAAW;AAEd,aAAO,cAAc;AAAA,IACvB;AAAA,aAES;AACP,aAAO,CAAC;AAAA,IACV;AAAA;AAEJ;;;AClCA,+BAAgC;AAgBhC,6BAA6B,eAA6C;AAhB1E;AAiBE,QAAM,UAAU,8CAAgB,aAAa;AAC7C,QAAM,cAAc,QAAQ,IAAI,cAAc,KAAK;AACnD,QAAM,cAAc,QAAQ,IAAI,qBAAqB;AAErD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC7D;AAEA,QAAM,aAAa,YAAY,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,UAAU;AAC/D,UAAM,CAAC,UAAS,SAAQ,MAAM,KAAK,EAAE,MAAM,GAAG;AAC9C,QAAI,SAAQ,MAAK,KAAK,GAAG;AACzB,WAAO;AAAA,EACT,GAAG,CAAC,CAAgC;AAEpC,QAAM,OAAO,iBAAW,SAAX,mBAAiB,MAAM,GAAG;AACvC,QAAM,WAAW,iBAAW,aAAX,mBAAqB,MAAM,GAAG;AAE/C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMO,4BACL,OACA,SACe;AACf,QAAM,cAAc,mCAAS,IAAI;AAEjC,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,CAAC,KAAK,cAAc,YAAY,MAAM,KAAK;AACjD,QAAM,WAAW,WACd,OAAO,CAAC,MAAM,EAAE,WAAW,WAAW,CAAC,EACvC,IAAI,CAAC,MAAM,EAAE,QAAQ,cAAc,EAAE,CAAC,EAAE;AAE3C,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB,IAAI,OAAO,MAAM,UAAU;AAClD,QAAM,SAAS,MACZ,MAAM,cAAc,EACpB,OAAO,CAAC,UAAU,MAAM,WAAW,MAAM,KAAK,MAAM,SAAS,MAAM,CAAC,EACpE,IAAI,CAAC,UAAU,MAAM,UAAU,EAAE,QAAQ,SAAS,EAAE,CAAC;AAExD,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,aAA0C,CAAC;AAEjD,MAAI;AACF,eAAW,UAAS,QAAQ;AAC1B,YAAM,CAAC,mBAAmB,SAAQ,OAAM,MAAM,UAAU;AACxD,YAAM,cAAc,MAAK,KAAK,UAAU;AACxC,YAAM,EAAE,2BAAa,UAAU,SAC7B,oBAAoB,cAAc;AAEpC,YAAM,QACJ,aAAa,SACT,cACA,IAAI,KAAK,CAAC,WAAW,GAAG,UAAU,EAAE,MAAM,aAAY,CAAC;AAE7D,YAAM,cAAc,WAAW;AAE/B,UAAI,gBAAgB,QAAW;AAC7B,mBAAW,QAAQ;AAAA,MACrB,WAAW,MAAM,QAAQ,WAAW,GAAG;AACrC,mBAAW,QAAQ,CAAC,GAAG,aAAa,KAAK;AAAA,MAC3C,OAAO;AACL,mBAAW,QAAQ,CAAC,aAAa,KAAK;AAAA,MACxC;AAAA,IACF;AAEA,WAAO;AAAA,EACT,SAAS,QAAP;AACA,WAAO;AAAA,EACT;AACF;;;AChGO,mBAAmB,OAA8B,SAAmB;AAP3E;AASE,MAAI,CAAC,OAAM;AACT,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,0CAAS,IAAI,oBAAb,mBAA8B,kBAAiB;AAInE,QAAM,sBAAsB,YAAY,WAAW,qBAAqB;AACxE,MAAI,uBAAuB,OAAO,UAAS,UAAU;AACnD,WAAO,mBAAmB,MAAK,SAAS,GAAG,OAAO,KAAK;AAAA,EACzD;AAIA,QAAM,iBAAiB,YAAY,SAAS,MAAM;AAElD,MAAI,kBAAkB,OAAO,UAAS,UAAU;AAC9C,WAAO,UAAU,MAAK,SAAS,CAAC,KAAK;AAAA,EACvC;AAGA,SAAO;AACT;;;AC7BO,uBAAuB,QAAgB,UAA2B;AACvE,SAAO,OAAO,YAAY,MAAM,SAAS,YAAY;AACvD;;;AJmEO,kCAEG,sCAAkB;AAAA,EAY1B,YAAY,KAAU,OAA0B,CAAC,GAAG;AAClD,UAAM,KAAK,IAAI;AACf,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK;AAAA,IACjB;AACA,SAAK,QAAQ,KAAK,SAAS;AAC3B,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,UAAU,KAAK,WAAW,KAAK,WAAW;AAAA,EACjD;AAAA,MAQW,OAAoB;AAC7B,UAAM,QAAO,qCAAa,KAAK,QAAQ;AAQvC,UAAM,QAAO,UAAU,OAAM,KAAK,OAAO;AAEzC,QAAI,cAAc,KAAK,QAAQ,KAAK,KAAK,UAAS,IAAI;AACpD,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAMO,cAAoC;AACzC,WAAO;AAAA,MAGL,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS,IAAI,iCAAQ;AAAA,MACrB,MAAM;AAAA,MAGN,aAAa;AAAA,MACb,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEQ,aAAqC;AAlJ/C;AAoJI,UAAM,uBAAuB,KAAK,QAAQ,IAAI,QAAQ;AACtD,UAAM,aAAa,uBACf,AAAY,mBAAM,oBAAoB,IACtC,CAAC;AAEL,yBAAM,QAAQ;AAEd,UAAM,mBAAmB,MAAM,KAC7B,2BAAM,IAAI,iCAAK,OAAL,EAAW,KAAK,KAAK,IAAI,KAAK,EAAC,MAAzC,mBAA4C,SAC9C,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa;AACvC,aAAO,OAAO,OAAO,SAAS,GAAG,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,IACxD,GAAG,CAAC,CAAC;AAIL,UAAM,sBAAsB,kBAAkB,IAAI;AAElD,UAAM,mBAAmB,kCACpB,sBACA;AAGL,eAAW,CAAC,MAAM,UAAU,OAAO,QAAQ,gBAAgB,GAAG;AAC5D,WAAK,QAAQ,OAAO,UAAU,GAAG,QAAQ,OAAO;AAAA,IAClD;AAEA,WAAO,kCACF,mBACA;AAAA,EAEP;AACF;;;AD1KO,4BACL,YACe;AACf,QAAM,MAAM,IAAI,IAAI,WAAW,GAAG;AAClC,QAAM,UAAU,IAAI,iCAAQ,WAAW,OAAO;AAE9C,SAAO,IAAI,cAAc,KAAK,iCACzB,aADyB;AAAA,IAE5B,MAAM,uCAAa,WAAW,QAAQ,EAAE;AAAA,IACxC;AAAA,EACF,EAAC;AACH;;;AMpBA,oBAAsB;;;ACqBf,IAAM,cAAc,OAIzB,SACA,UACA,sBACkC;AAClC,QAAM,mBAAmB,SAAS,OAAO,CAAC,YAAY;AACpD,WAAO,QAAQ,KAAK,SAAS,iBAAiB;AAAA,EAChD,CAAC;AAED,MAAI,iBAAiB,WAAW,GAAG;AACjC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EACF;AAEA,QAAM,SAAS,MAAM,iBAAiB,OAEpC,OAAO,iBAAiB,YAAY;AACpC,UAAM,kBAAkB,MAAM;AAE9B,QAAI,CAAC,CAAC,oDAAiB,WAAU;AAC/B,aAAO;AAAA,IACT;AAEA,UAAM,UAAS,MAAM,QAAQ,IAAI,SAAS,iBAAiB;AAE3D,QAAI,YAAW,QAAQ,QAAO,QAAQ,YAAY;AAChD,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,QAAO,UAAU;AACpB,aAAO;AAAA,QACL,SAAS,QAAO;AAAA,QAChB,SAAS,QAAO;AAAA,QAChB,UAAU;AAAA,QACV,cAAc,QAAO;AAAA,MACvB;AAAA,IACF;AAEA,QAAI,QAAO,SAAS,MAAM;AACxB,cAAQ,cAAc,IAAI;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT,GAAG,QAAQ,QAAQ,IAAI,CAAC;AAKxB,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EACF;AAEA,SAAO;AAAA,IACL,SAAS,OAAO;AAAA,IAChB,eAAe,OAAO;AAAA,IACtB,eAAe,OAAO;AAAA,IACtB,UAAU,OAAO;AAAA,EACnB;AACF;;;ACvFA,4BAAgC;;;ACKhC,qBAAsB;;;ACCf,IAAM,0BAA0B,CAAC,YAA2B;AACjE,SAAO,QAAQ,SAAS,WAAW,QAAQ,IAAI,MAAM,IACjD,QAAQ,IAAI,WACZ,IAAI,IACF,QAAQ,IAAI,UACZ,GAAG,QAAQ,IAAI,aAAa,QAAQ,IAAI,MAC1C,EAAE;AACR;;;ADiBO,2BAA2B,MAAwC;AA9B1E;AA+BE,QAAM,eAAe,KAAK,YAAY,KAAK,CAAC,QAAQ;AAClD,WAAO,IAAI,SAAS;AAAA,EACtB,CAAC;AAED,SAAO;AAAA,IACL,eAAe,6CAAc;AAAA,IAC7B,eAAe,mDAAc,SAAd,mBAAoB;AAAA,EACrC;AACF;AAEA,oBAAoB,OAA2C;AAC7D,MAAI;AACF,UAAM,MAAM,0BAAM,KAAK;AACvB,WAAO,kBAAkB,GAAG;AAAA,EAC9B,SAAS,QAAP;AACA,WAAO;AAAA,EACT;AACF;AAUA,mCACE,WACA,KACA,OACA;AACA,QAAM,aAAa,EAAE,UAAU;AAC/B,aAAW,CAAC,KAAK,cAAc,OAAO,QAAQ,GAAG,GAAG;AAClD,QAAI,CAAE,QAAO,QAAQ;AACnB,YAAM,IAAI,MAAM,kCAAkC,QAAQ;AAAA,IAC5D;AAEA,eAAW,WAAW,WAAW;AAC/B,YAAM,CAAC,aAAa,iBAAiB,QAAQ,MAAM,GAAG,EAAE,QAAQ;AAChE,YAAM,QAAQ,cAAc,QAAQ;AACpC,UAAI,SAA8B;AAElC,iBAAW,QAAQ,OAAO;AACxB,YAAI,CAAE,SAAQ,SAAS;AACrB,gBAAM,IAAI,MAAM,aAAa,8BAA8B;AAAA,QAC7D;AAEA,iBAAS,OAAO;AAAA,MAClB;AAEA,aAAO,YAAY,MAAM;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,WAAW;AACpB;AAEA,yBAAyB,SAAkD;AAxF3E;AAyFE,UAAQ,QAAQ;AAAA,SACT,OAAO;AACV,YAAM,QAAQ,QAAQ,IAAI,aAAa,IAAI,OAAO;AAClD,YAAM,YAAY,QAAQ,IAAI,aAAa,IAAI,WAAW,KAAK;AAE/D,aAAO;AAAA,QACL;AAAA,QACA,WAAW,UAAU,SAAS;AAAA,MAChC;AAAA,IACF;AAAA,SAEK,QAAQ;AACX,UAAI,cAAQ,SAAR,mBAAc,OAAO;AACvB,cAAM,EAAE,OAAO,cAAc,QAAQ;AAErC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAGA,UAAI,cAAQ,SAAR,mBAAc,YAAY;AAC5B,cACE,aAAQ,MADF,cAAY,QAClB,IAD0B,kBAC1B,IAD0B,CAApB,cAAY;AAEpB,cAAM,mBACJ,UACE,UACF,KAAK,CAAC;AAER,YAAI,CAAC,iBAAiB,OAAO;AAC3B,iBAAO;AAAA,QACT;AAEA,cAAM,YAAY,UAAsC,OAAO,EAAE,KAAK,CAAC;AACvE,cAAM,YAAY,iBAAiB,YAC/B,0BACE,iBAAiB,WACjB,WACA,KACF,IACA,CAAC;AAEL,eAAO;AAAA,UACL,OAAO,iBAAiB;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAGE,aAAO;AAAA;AAEb;AAMO,6BACL,SACsB;AACtB,QAAM,QAAQ,gBAAgB,OAAO;AAErC,MAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,OAAO,cAAc;AAC7B,QAAM,eAAe,WAAW,KAAK;AAErC,MAAI,wBAAwB,OAAO;AACjC,UAAM,mBAAmB,wBAAwB,OAAO;AAExD,UAAM,IAAI,MACR,SAAS,cACP,4HACA,QAAQ,QACR,kBACA,aAAa,OACf,CACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,eAAe,aAAa;AAAA,IAC5B,eAAe,aAAa;AAAA,IAC5B;AAAA,EACF;AACF;;;AEzKO,4BAA4B,SAAiC;AAClE,MAAI,UAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,UAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AChBO,wBAAgC;AACrC,QAAM,MAAM,IAAI,KAAK;AAErB,SAAO,CAAC,IAAI,SAAS,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,CAAC,EACvD,IAAI,MAAM,EACV,IAAI,CAAC,UAAU,MAAM,MAAM,GAAG,CAAC,CAAC,EAChC,IAAI,CAAC,UAAU,MAAM,SAAS,GAAG,GAAG,CAAC,EACrC,KAAK,GAAG;AACb;;;ACIO,wBAAwB,SAAuC;AACpE,SAAO,iCACF,UADE;AAAA,IAEL,MAAM,QAAQ;AAAA,IACd,SAAS,QAAQ,QAAQ,IAAI;AAAA,EAC/B;AACF;;;ACrBA,+BAAgC;AAOzB,yBAAyB,KAA8B;AAC5D,QAAM,kBAAkB,8CAAgB,IAAI,OAAO;AAEnD,SAAO,iCACF,MADE;AAAA,IAGL,MAAM,UAAU,IAAI,MAAM,eAAe;AAAA,EAC3C;AACF;;;ACfA,4BAAsB;AACtB,yBAA4B;;;ACD5B,IAAM,2BAA2B;AAE1B,yBAAyB,MAAc;AAC5C,SAAO,IAAI,IAAI,IAAI,QAAQ,kBAAkB,EAAE;AACjD;AAKO,kBAAkB,MAAsB;AAC7C,SAAO,KAAK,QAAQ,0BAA0B,EAAE;AAClD;;;ACRO,uBAAuB,KAAsB;AAClD,SAAO,gCAAgC,KAAK,GAAG;AACjD;;;ACAO,wBAAwB,MAAc,SAA0B;AAErE,MAAI,cAAc,IAAI,GAAG;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,KAAK,WAAW,GAAG,GAAG;AACxB,WAAO;AAAA,EACT;AAIA,QAAM,SACJ,WAAY,OAAO,aAAa,eAAe,SAAS;AAE1D,SAAO,SAEH,UAAU,IAAI,IAAI,UAAU,IAAI,GAAG,MAAM,EAAE,IAAI,IAC/C;AACN;;;ACdO,uBAAuB,MAAY,SAAwB;AAEhE,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB,eAAe,MAAM,OAAO;AAErD,SAAO,SAAS,gBAAgB;AAClC;;;AJFO,oBAAoB,MAAsB;AAC/C,SACE,KAMG,QACC,6BACA,CAAC,GAAG,eAAmC,aAAqB;AAC1D,UAAM,aAAa;AAEnB,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AAEA,WAAO,cAAc,WAAW,GAAG,IAC/B,GAAG,gBAAgB,aACnB,GAAG,gBAAgB;AAAA,EACzB,CACF,EAKC,QAAQ,qBAAqB,QAAQ,EAMrC,QAAQ,wBAAwB,QAAQ;AAE/C;AAKO,yBAAyB,KAAU,MAAY,SAAyB;AAC7E,QAAM,iBAAiB,cAAc,MAAM,OAAO;AAClD,QAAM,YACJ,OAAO,mBAAmB,WACtB,WAAW,cAAc,IACzB;AAEN,QAAM,YAAW,oCAAY,GAAG;AAChC,QAAM,SAAS,iCAAM,WAAW,EAAE,QAAQ,mBAAmB,CAAC,EAAE,SAAQ;AACxE,QAAM,SAAU,UAAW,OAAO,UAA0B,CAAC;AAE7D,SAAO;AAAA,IACL,SAAS,WAAW;AAAA,IACpB;AAAA,EACF;AACF;;;AKxEA,+BAAwB;;;ACAxB,+BAAwB;;;AC6BjB,oBAKF,KAGgB;AACnB,SAAO,IAAI,SAAS;AAClB,WAAO,IAAI,YAAY,CAAC,QAAa,YAAY;AAC/C,aAAO,kBAAkB,UACrB,QAAQ,QAAQ,MAAM,EAAE,KAAK,OAAO,IACpC,QAAQ,MAAM;AAAA,IACpB,GAAG,KAAK,EAAE;AAAA,EACZ;AACF;;;ADJO,IAAM,kBAAmD;AAAA,EAC9D,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,aAAa;AACf;AAOO,IAAM,8BAA0D,CAAC;AAEjE,mCACL,mBACA,sBAAuD,6BACrC;AAClB,SAAO,UAAU,iBAAiB;AAChC,UAAM,kBAAkC,OAAO,OAC7C,CAAC,GACD,iBACA;AAAA,MACE,SAAS,IAAI,iCAAQ;AAAA,QACnB,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,GACA,iBACF;AAEA,UAAM,uBAAuB;AAAA,MAC3B,GAAG;AAAA,MACH,GAAG;AAAA,IACL,EAAE,OAAO,OAAO;AAEhB,UAAM,mBACJ,qBAAqB,SAAS,IAC1B,QAAQ,GAAG,oBAAoB,EAAE,eAAe,IAChD;AAEN,WAAO;AAAA,EACT;AACF;AAEO,IAAM,WAAW,OAAO,OAAO,0BAA0B,GAAG;AAAA,EACjE,MAAM,0BAA0B,EAAE,MAAM,KAAK,CAAC;AAAA,EAC9C,aAAa,SAAiB;AAC5B,UAAM,IAAI,aAAa,OAAO;AAAA,EAChC;AACF,CAAC;;;AE3FD,IAAM,eAAe;AAErB,IAAM,cACJ;AAKK,sBAAsB,QAAc;AAEzC,QAAM,QAAQ,OAAM;AAEpB,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAEA,QAAM,SAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,CAAC;AAIlD,QAAM,mBAAmB,OAAO,KAAK,CAAC,UAAU;AAC9C,WAAO,CAAE,cAAa,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK;AAAA,EAC7D,CAAC;AAED,MAAI,CAAC,kBAAkB;AACrB;AAAA,EACF;AAGA,QAAM,kBAAkB,iBACrB,QAAQ,2BAA2B,IAAI,EACvC,QAAQ,MAAM,EAAE;AACnB,SAAO;AACT;;;AC/BO,oBACL,IAC2D;AAC3D,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AAEA,SAAO,OAAQ,GAA0B,OAAO,aAAa;AAC/D;;;AJaO,IAAM,iBAAiC;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAkEO,2BAKL;AAAA,EAcA,YAAY,SAA6C;AACvD,SAAK,aAAa;AAClB,SAAK,MAAM,QAAQ,OAAO;AAC1B,SAAK,WAAW,QAAQ;AAExB,UAAM,YAAY,aAAa,IAAI,MAAM,CAAC;AAE1C,SAAK,OAAO,iCACP,QAAQ,OADD;AAAA,MAEV;AAAA,IACF;AAAA,EACF;AAAA,EAwBA,MACE,UACA,oBACc;AACd,WAAO;AAAA,EACT;AAAA,EAKO,KACL,SACA,mBACS;AACT,WAAO,KAAK,UACV,SACA,KAAK,MAAM,SAAS,iBAAiB,GACrC,iBACF;AAAA,EACF;AAAA,EAMU,iBACR,SACA,eACA;AACA,WAAO;AAAA,EACT;AAAA,EAEO,cAAc,aAAa,MAAM;AACtC,SAAK,aAAa;AAAA,EACpB;AAAA,QAMa,IACX,SACA,mBAC8D;AAC9D,QAAI,KAAK,YAAY;AACnB,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,KAAK,MAAM,SAAS,iBAAiB;AAC1D,UAAM,kBAAkB,KAAK,UAC3B,SACA,cACA,iBACF;AAEA,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,gBAAgB,KAAK,iBAAiB,SAAS,YAAY;AAIjE,UAAM,kBAAkB,KAAK,aAAa,KAAK,QAAQ;AACvD,UAAM,iBAAiB,MAAM,gBAC3B,eACA,UACA,KAAK,GACP;AAEA,WAAO,KAAK,sBACV,cACA,eACA,cACF;AAAA,EACF;AAAA,EAEQ,aACN,UAC6D;AAC7D,WAAO,OAAO,KAAK,KAAK,QAAQ;AAC9B,YAAM,SAAS,KAAK,qBAAsB,MAAM,SAAS,KAAK,KAAK,GAAG;AAEtE,UAAI,WAAiD,MAAM,GAAG;AAC5D,cAAM,EAAE,OAAO,SAAS,OAAO,OAAO,UAAU,EAAE,KAAK;AACvD,cAAM,eAAe,MAAM;AAI3B,YAAI,CAAC,gBAAgB,MAAM;AACzB,iBAAO,KAAK;AAAA,QACd;AAEA,YAAI,CAAC,KAAK,mBAAmB;AAC3B,eAAK,oBAAoB;AAAA,QAC3B;AAEA,aAAK,0BAA0B;AAC/B,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEQ,sBACN,cACA,SACA,WAC8C;AAC9C,WAAO;AAAA,MACL,SAAS;AAAA,MACT,cAAc,gBAAgB;AAAA,MAC9B;AAAA,MACA,UAAU,aAAY;AAAA,IACxB;AAAA,EACF;AACF;;;AKxOO,IAAK,cAAL,kBAAK,iBAAL;AACL,yBAAO;AACP,wBAAM;AACN,yBAAO;AACP,wBAAM;AACN,0BAAQ;AACR,4BAAU;AACV,2BAAS;AAPC;AAAA;AAoBL,IAAM,cAA2B,iCACnC,iBADmC;AAAA,EAEtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAQO,gCAGG,cAA2B;AAAA,EACnC,YACE,SACgB,QAChB;AACA,UAAM,QAAQ,KAAK,iCACd,UADc;AAAA,MAQjB,MAAM,QAAQ;AAAA,IAChB,EAAC;AAXe;AAYhB,SAAK,KAAK,QAAQ;AAAA,EACpB;AACF;AAMO,gCAEG,eAUR;AAAA,EACA,YACE,QACA,MACA,UACA;AACA,UAAM;AAAA,MACJ,MAAM;AAAA,QACJ,QAAQ,GAAG,UAAU;AAAA,QACrB;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AAED,SAAK,8BAA8B;AAAA,EACrC;AAAA,EAEQ,gCAAgC;AACtC,UAAM,EAAE,QAAQ,SAAS,KAAK;AAE9B,QAAI,gBAAgB,QAAQ;AAC1B;AAAA,IACF;AAEA,UAAM,MAAM,SAAS,IAAI;AAGzB,QAAI,QAAQ,MAAM;AAChB;AAAA,IACF;AAEA,UAAM,eAAe,gBAAgB,IAAI;AACzC,UAAM,cAAwB,CAAC;AAE/B,iBAAa,QAAQ,CAAC,GAAG,cAAc;AACrC,kBAAY,KAAK,SAAS;AAAA,IAC5B,CAAC;AAED,aAAS,KACP,+EAA+E,UAAU,wIAC3F;AAAA,EACF;AAAA,EAEA,MAAM,SAAsB,mBAA+C;AACzE,WAAO,gBACL,QAAQ,KACR,KAAK,KAAK,MACV,uDAAmB,OACrB;AAAA,EACF;AAAA,EAEU,iBACR,SACA,cAC8B;AAC9B,WAAO,IAAI,YAAY,SAAS,aAAa,UAAU,CAAC,CAAC;AAAA,EAC3D;AAAA,EAEA,UAAU,SAAsB,cAAiC;AAC/D,UAAM,gBACJ,KAAK,KAAK,kBAAkB,SACxB,KAAK,KAAK,OAAO,KAAK,QAAQ,MAAM,IACpC,cAAc,KAAK,KAAK,QAAQ,QAAQ,MAAM;AAEpD,WAAO,iBAAiB,aAAa;AAAA,EACvC;AAAA,EAEA,IAAI,SAAsB,WAAmC;AAC3D,UAAM,YAAY,wBAAwB,OAAO;AACjD,UAAM,gBAAgB,eAAe,OAAO;AAC5C,UAAM,iBAAiB,gBAAgB,SAAQ;AAC/C,UAAM,cAAc,mBAAmB,UAAS,MAAM;AAEtD,YAAQ,eACN,SAAS,cAAc,mBAAmB,GAC1C,aAAa,GACb,QAAQ,QACR,WACA,SAAS,eACT,GAAG,UAAS,UAAU,UAAS,cAC/B,eACF;AACA,YAAQ,IAAI,WAAW,aAAa;AACpC,YAAQ,IAAI,YAAY,IAAI;AAC5B,YAAQ,IAAI,YAAY,cAAc;AACtC,YAAQ,SAAS;AAAA,EACnB;AACF;;;ACpMA,yBAA0B;AAcnB,IAAM,QAAQ,CACnB,WACA,eACgC;AAChC,SAAO,CAAC,QAAQ;AACd,sBAAkB,SAAS;AAE3B,UAAM,WAAW,UAAU,IAAI,IAAI,KAAK,CAAC;AACzC,UAAM,WAAW,WAAW,UAAU,GAAG,YAAY,WAAW,CAAC;AAEjE,WAAO,KAAK,QAAQ,EAAE,GAAU;AAAA,EAClC;AACF;AAEA,2BAA2B,WAAmB;AAC5C,oCACE,UAAU,KAAK,MAAM,IACrB,SAAS,cACP,iFACF,CACF;AAEA,oCACE,cAAc,QACd,SAAS,cACP,oIACA,SACF,CACF;AAEA,oCACE,cAAc,UACd,SAAS,cACP,sIACA,SACF,CACF;AAEA,oCACE,cAAc,cACd,SAAS,cACP,0IACA,SACF,CACF;AACF;;;AC3DO,kBACL,IACA,aAC4B;AAC5B,MAAI;AACF,UAAM,SAAS,GAAG;AAClB,WAAO;AAAA,EACT,SAAS,QAAP;AACA,+CAAc;AAAA,EAChB;AACF;;;ACoCO,IAAM,iBAAsC,iCAC9C,iBAD8C;AAAA,EAEjD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAoBO,wBACL,OACuB;AACvB,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,UAAU,YAAY,UAAU,SAAS,iBAAiB;AAC1E;AAEO,mCAEG,cAA6C;AAAA,EACrD,YAAY,SAAwC,WAAsB;AACxE,UAAM,QAAQ,KAAK,iCACd,UADc;AAAA,MAKjB,MAAM,QAAQ;AAAA,IAChB,EAAC;AAPiD;AAAA,EAQpD;AACF;AAEO,mCAEG,eAKR;AAAA,EAGA,YACE,eACA,eACA,UACA,UACA;AACA,QAAI,wBAAwB;AAE5B,QAAI,eAAe,aAAa,GAAG;AACjC,YAAM,aAAa,kBAAkB,aAAa;AAElD,UAAI,WAAW,kBAAkB,eAAe;AAC9C,cAAM,IAAI,MACR,2GAA2G,4BAA4B,WAAW,kBACpJ;AAAA,MACF;AAEA,UAAI,CAAC,WAAW,eAAe;AAC7B,cAAM,IAAI,MACR,qFACF;AAAA,MACF;AAEA,8BAAwB,WAAW;AAAA,IACrC;AAEA,UAAM,SACJ,kBAAkB,QACd,GAAG,0BAA0B,SAAS,SAAS,OAC/C,GAAG,iBAAiB,kCAAkC,SAAS,SAAS;AAE9E,UAAM;AAAA,MACJ,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AAED,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,MAAM,SAAwB;AAC5B,WAAO,SACL,MAAM,oBAAoB,OAAO,GACjC,CAAC,WAAU,QAAQ,MAAM,OAAM,OAAO,CACxC;AAAA,EACF;AAAA,EAEU,iBACR,SACA,cACqB;AACrB,WAAO,IAAI,eAAe,SAAS,8CAAc,cAAa,CAAC,CAAC;AAAA,EAClE;AAAA,EAEA,UAAU,SAAwB,cAAoC;AACpE,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,aAAa,iBAAiB,KAAK,KAAK,kBAAkB,OAAO;AACpE,YAAM,YAAY,wBAAwB,OAAO;AACjD,eAAS,KAAK,6CACwB,QAAQ,UAAU;AAAA;AAAA,gNAGvD;AACD,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,gBAAgB,QAAQ,KAAK,KAAK,QAAQ;AACjE,UAAM,2BACJ,KAAK,KAAK,kBAAkB,SAC5B,aAAa,kBAAkB,KAAK,KAAK;AAE3C,UAAM,2BACJ,KAAK,KAAK,yBAAyB,SAC/B,KAAK,KAAK,cAAc,KAAK,aAAa,iBAAiB,EAAE,IAC7D,aAAa,kBAAkB,KAAK,KAAK;AAE/C,WACE,eAAe,WACf,4BACA;AAAA,EAEJ;AAAA,EAEA,IACE,SACA,WACA,eACA;AACA,UAAM,gBAAgB,eAAe,OAAO;AAC5C,UAAM,iBAAiB,gBAAgB,SAAQ;AAC/C,UAAM,cAAc,mBAAmB,UAAS,MAAM;AACtD,UAAM,cAAc,gDAAe,iBAC/B,GAAG,+CAAe,iBAAiB,+CAAe,kBAClD,aAAa,+CAAe;AAEhC,YAAQ,eACN,SAAS,cAAc,gBAAgB,GACvC,aAAa,GACb,GAAG,eACH,SAAS,eACT,GAAG,UAAS,UAAU,UAAS,cAC/B,eACF;AACA,YAAQ,IAAI,YAAY,aAAa;AACrC,YAAQ,IAAI,YAAY,IAAI;AAC5B,YAAQ,IAAI,aAAa,cAAc;AACvC,YAAQ,SAAS;AAAA,EACnB;AACF;;;ApBhNA,IAAM,kBAAkB;AACxB,IAAM,uBAAuB;AAC7B,IAAM,mBAAmB;AAuBzB,6BAA6B,UAAkD;AAC7E,SAAO,SAAS,OACd,CAAC,QAAQ,YAAY;AACnB,QAAI,mBAAmB,aAAa;AAClC,aAAO,KAAK,KAAK,OAAO;AAAA,IAC1B;AAEA,QAAI,mBAAmB,gBAAgB;AACrC,aAAO,QAAQ,KAAK,OAAO;AAAA,IAC7B;AAEA,WAAO;AAAA,EACT,GACA;AAAA,IACE,MAAM,CAAC;AAAA,IACP,SAAS,CAAC;AAAA,EACZ,CACF;AACF;AASA,+BAA2D;AACzD,SAAO,CAAC,SAAS,YAAY;AAC3B,UAAM,EAAE,MAAM,WAAW,QAAQ;AAEjC,QAAI,gBAAgB,UAAU,kBAAkB,QAAQ;AACtD,aAAO;AAAA,IACT;AAEA,UAAM,gBAAgB,cAAc,QAAQ,QAAQ,MAAM;AAG1D,UAAM,mBAAmB,gBAAgB,mBAAmB;AAC5D,UAAM,mBAAmB,wBAAwB,OAAO;AACxD,UAAM,QAAQ,mCAAoB,kBAAkB,IAAI;AAExD,WAAO,QAAQ;AAAA,EACjB;AACF;AAEA,gCACE,aAC+B;AAC/B,SAAO,CAAC,GAAG,YAAY;AACrB,QAAI,OAAO,YAAY,kBAAkB,aAAa;AACpD,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,eAAe,kBAAkB,QAAQ;AAEjD,QAAI,OAAO,kBAAkB,UAAU;AACrC,aAAO;AAAA,IACT;AAEA,UAAM,uBAAuB,YAAY,kBAAkB;AAE3D,UAAM,0BAA0B,uBAAuB,mBAAmB;AAC1E,UAAM,QAAQ,mCAAoB,YAAY,eAAe,aAAa;AAE1E,WAAO,QAAQ;AAAA,EACjB;AACF;AAEA,6BACE,SACA,UACA,UACkB;AAClB,QAAM,oBAAqB,SACxB,OAAmC,CAAC,aAAa,YAAY;AAC5D,UAAM,QAAQ,SAAS,SAAS,OAAc;AAC9C,WAAO,YAAY,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC;AAAA,EAC9C,GAAG,CAAC,CAAC,EACJ,KAAK,CAAC,CAAC,YAAY,CAAC,gBAAgB,YAAY,UAAU,EAC1D,OAAO,CAAC,CAAC,WAAW,SAAS,eAAe,EAC5C,MAAM,GAAG,oBAAoB,EAC7B,IAAI,CAAC,CAAC,EAAE,aAAa,OAAO;AAE/B,SAAO;AACT;AAEA,qCAAqC,UAA4B;AAC/D,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO;AAAA;AAAA,EAGT,SAAS,IAAI,CAAC,YAAY,YAAO,QAAQ,KAAK,QAAQ,EAAE,KAAK,IAAI;AAAA,EACjE;AAEA,SAAO,4BAA4B,SAAS,GAAG,KAAK;AACtD;AAEO,4BACL,SACA,UACA,WAAqC,QAC/B;AACN,QAAM,qBAAqB,SAAS,MAAM,oBAAoB,OAAO,CAAC;AAEtE,uCAA6C;AAM3C,UAAM,gBAAgB,oBAAoB,QAAQ;AAClD,UAAM,mBAAmB,qBACrB,cAAc,UACd,cAAc;AAElB,UAAM,oBAAoB,oBACxB,SACA,kBACA,qBACI,uBAAuB,kBAAkB,IACzC,oBAAoB,CAC1B;AAEA,WAAO,kBAAkB,SAAS,IAC9B,4BAA4B,iBAAiB,IAC7C;AAAA,EACN;AAEA,6CAAmD;AACjD,UAAM,YAAY,wBAAwB,OAAO;AACjD,UAAM,gBAAgB,qBAClB,GAAG,mBAAmB,iBAAiB,mBAAmB,kBAAkB,QAAQ,UAAU,eAC9F,GAAG,QAAQ,UAAU;AACzB,UAAM,oBAAoB,0BAA0B;AAEpD,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA;AAAA,IAIF,EAAE,OAAO,OAAO;AAChB,WAAO,gBAAgB,KAAK,MAAM;AAAA,EACpC;AAEA,yBAAuB,WAAoC;AAIzD,UAAM,UAAU,gCAAgC;AAEhD,YAAQ;AAAA,WACD,SAAS;AAEZ,iBAAS,MAAM,aAAa,OAAO;AAGnC,cAAM,IAAI,MACR,SAAS,cACP,8FACF,CACF;AAAA,MACF;AAAA,WAEK,QAAQ;AACX,iBAAS,KAAK,eAAe,OAAO;AACpC;AAAA,MACF;AAAA,WAEK;AACH;AAAA;AAGA,cAAM,IAAI,MACR,SAAS,cACP,6NACA,SACF,CACF;AAAA;AAAA,EAEN;AAEA,MAAI,OAAO,aAAa,YAAY;AAClC,aAAS,SAAS;AAAA,MAChB,SAAS,cAAc,KAAK,MAAM,MAAM;AAAA,MACxC,OAAO,cAAc,KAAK,MAAM,OAAO;AAAA,IACzC,CAAC;AACD;AAAA,EACF;AAEA,gBAAc,QAAQ;AACxB;;;AqBzOA,sBAAsB;AAIf,6BACL,SACA,WACA;AACA,wBAAM,IAAI,iCAAK,UAAL,EAAc,KAAK,QAAQ,IAAI,SAAS,EAAE,IAAG,SAAQ;AAC/D,wBAAM,QAAQ;AAChB;;;AvB+BA,6BAGE,SACA,UACA,SACA,SACA,sBACmC;AAjDrC;AAkDE,UAAQ,KAAK,iBAAiB,OAAO;AAGrC,MAAI,QAAQ,QAAQ,IAAI,cAAc,MAAM,QAAQ;AAClD,YAAQ,KAAK,eAAe,OAAO;AACnC,uEAAsB,0BAAtB,8CAA8C;AAC9C;AAAA,EACF;AAGA,QAAM,CAAC,aAAa,gBAAgB,MAAM,yBAAM,MAAM;AACpD,WAAO,YACL,SACA,UACA,6DAAsB,iBACxB;AAAA,EACF,CAAC;AAED,MAAI,aAAa;AAEf,YAAQ,KAAK,sBAAsB,aAAa,OAAO;AACvD,UAAM;AAAA,EACR;AAEA,QAAM,EAAE,SAAS,wBAAa;AAI9B,MAAI,CAAC,SAAS;AACZ,uBAAmB,SAAS,UAAU,QAAQ,kBAAkB;AAChE,YAAQ,KAAK,qBAAqB,OAAO;AACzC,YAAQ,KAAK,eAAe,OAAO;AACnC,uEAAsB,0BAAtB,8CAA8C;AAC9C;AAAA,EACF;AAIA,MAAI,CAAC,WAAU;AACb,aAAS,KACP;AAAA;AAAA;AAAA,SAMA,WACA,QAAQ,KAAK,QACb,QAAQ,KAAK,SACf;AAEA,YAAQ,KAAK,eAAe,OAAO;AACnC,uEAAsB,0BAAtB,8CAA8C;AAC9C;AAAA,EACF;AAIA,MAAI,UAAS,aAAa;AACxB,YAAQ,KAAK,eAAe,OAAO;AACnC,uEAAsB,0BAAtB,8CAA8C;AAC9C;AAAA,EACF;AAGA,sBAAoB,SAAS,SAAQ;AAErC,UAAQ,KAAK,iBAAiB,OAAO;AAErC,QAAM,uBACJ;AAEF,QAAM,sBACJ,oEAAsB,sBAAtB,8CAA0C,eACzC;AAEH,qEAAsB,qBAAtB,8CACE,qBACA;AAGF,UAAQ,KAAK,eAAe,OAAO;AAEnC,SAAO;AACT;;;AwBtIA,+BAAsD;AAG/C,2BAA2B,QAA2C;AAC3E,SAAO;AAAA,IACL,QAAQ,OAAO;AAAA,IACf,YAAY,OAAO;AAAA,IACnB,SAAS,mDAAqB,8CAAgB,OAAO,OAAO,CAAC;AAAA,IAC7D,MAAM,OAAO;AAAA,EACf;AACF;;;ACQO,IAAM,wBAAwB,CACnC,SACA,YACG;AACH,SAAO,OACL,OACA,YAIG;AA5BP;AA6BI,UAAM,iBAAiB,IAAI,cAAc,MAAM,MAAM,EAAE;AACvD,UAAM,UAAU,mBAAmB,QAAQ,OAAO;AAElD,QAAI;AACF,YAAM,cACJ,SACA,QAAQ,iBACR,SACA,QAAQ,SACR;AAAA,QACE;AAAA,QACA,wBAAwB;AACtB,yBAAe,YAAY,WAAW;AAAA,QACxC;AAAA,cACM,iBACJ,WACA,EAAE,SAAS,eAAe,iBAC1B;AACA,cAAI,UAAS,gBAAgB,gBAAgB;AAC3C,kBAAM,IAAI,MACR,SAAS,cACP,yKACF,CACF;AAAA,UACF;AAEA,gBAAM,mBAAmB,IAAI,SAAS,UAAS,MAAM,SAAQ;AAC7D,gBAAM,qBAAqB,MAAM,iBAAiB,YAAY;AAM9D,gBAAM,eACJ,UAAS,QAAQ,OAAO,OAAO;AAEjC,yBAAe,YACb,iBACA,iCACK,YADL;AAAA,YAEE,MAAM;AAAA,UACR,IACA,CAAC,kBAAkB,CACrB;AAEA,cAAI,CAAC,QAAQ,OAAO;AAClB,oBAAQ,QAAQ,KAAK,mBAAmB,CAAC,cAAa;AACpD,sBAAQ,IACN,eACA,kBAAkB,SAAQ,GAC1B,aACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CACF;AAAA,IACF,SAAS,QAAP;AACA,UAAI,kBAAiB,cAAc;AAGjC,uBAAe,YAAY,iBAAiB;AAAA,UAC1C,MAAM,OAAM;AAAA,UACZ,SAAS,OAAM;AAAA,QACjB,CAAC;AAED;AAAA,MACF;AAEA,UAAI,kBAAiB,OAAO;AAC1B,iBAAS,MACP;AAAA;AAAA;AAAA;AAAA,sRAKA,QAAQ,QACR,QAAQ,KACR,aAAM,UAAN,YAAe,MACjB;AAIA,uBAAe,YAAY,iBAAiB;AAAA,UAC1C,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACP,gBAAgB;AAAA,UAClB;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,MAAM,OAAM;AAAA,YACZ,SAAS,OAAM;AAAA,YACf,OAAO,OAAM;AAAA,UACf,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,2BACE,WAC4B;AAC5B,SAAO;AAAA,IACL,QAAQ,UAAS;AAAA,IACjB,YAAY,UAAS;AAAA,IACrB,SAAS,UAAS,QAAQ,IAAI;AAAA,IAC9B,MAAM,UAAS;AAAA,IACf,OAAO,UAAS;AAAA,EAClB;AACF;;;ACzIA,qCACE,SACA,eACwB;AAExB,UAAQ,cAAc,KAAK,yBAAyB;AAEpD,QAAM,EAAE,SAAS,mBAAmB,MAAM,QAAQ,OAAO,KACvD,0BACF;AAIA,MAAI,mBAAmB,oCAAyB;AAC9C,UAAM,IAAI,MACR,oCAAoC,uDAAuD,sCAC7F;AAAA,EACF;AAEA,SAAO;AACT;;;ACtBA,oBAAsB;AAOf,mCAAmC,kBAAgC;AAExE,QAAM,kBAAkB,OAAO,eAAe,UAAU;AACxD,SAAO,eAAe,UAAU,OAAO,YAClC,MACH;AAGA,6BAAM,MAAM,gBAAgB,EAAE,KAAK,MAAM;AACvC,aAAO,eAAe,UAAU,OAAO;AACvC,WAAK,KAAK,GAAG,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AAGA,QAAM,gBAAgB,OAAO;AAC7B,SAAO,QAAQ,UAAU,SAAS;AAChC,UAAM,yBAAM,MAAM,gBAAgB;AAClC,WAAO,QAAQ;AACf,WAAO,OAAO,MAAM,GAAG,IAAI;AAAA,EAC7B;AACF;;;ACtBO,gCAAgC,SAAqC;AAC1E,SAAO,CACL,GACA,YAIG;AAbP;AAcI,UAAM,EAAE,SAAS,iBAAiB;AASlC,QAAI,mBAAa,SAAb,mBAAmB,SAAS,WAAW;AACzC;AAAA,IACF;AAEA,UAAM,YAAW,IAAI,SAAS,aAAa,QAAQ,MAAM,YAAY;AACrE,UAAM,mBAAmB,UAAS,QAAQ,IAAI,cAAc,MAAM;AAElE,QAAI,kBAAkB;AACpB,cAAQ,QAAQ,KAAK,mBAAmB,WAAU,aAAa,SAAS;AAAA,IAC1E,OAAO;AACL,cAAQ,QAAQ,KAAK,mBAAmB,WAAU,aAAa,SAAS;AAAA,IAC1E;AAAA,EACF;AACF;;;ACjCO,6BACL,cACA,SACM;AACN,MAAI,CAAC,oCAAS,UAAS,CAAC,SAAS,KAAK,WAAW,aAAa,KAAK,GAAG;AACpE,aAAS,KACP,uFACgF,aAAa;AAAA;AAAA;AAAA,kFAK/F;AAAA,EACF;AACF;;;A5CNO,IAAM,qBAAqB,CAChC,YACiB;AACjB,SAAO,eAAe,SAAS,eAAe;AAC5C,UAAM,sBAAsB,YAAY;AAItC,cAAQ,OAAO,mBAAmB;AAGlC,cAAQ,cAAc,GACpB,WACA,sBAAsB,SAAS,OAAO,CACxC;AAGA,cAAQ,cAAc,GAAG,YAAY,uBAAuB,OAAO,CAAC;AAEpE,YAAM,WAAW,MAAM,kBACrB,QAAQ,cAAc,KACtB,QAAQ,cAAc,SACtB,QAAQ,UACV;AAEA,YAAM,CAAC,QAAQ,gBAAgB;AAE/B,UAAI,CAAC,QAAQ;AACX,cAAM,uBAAuB,gDAAe,cACxC,SAAS,cACP;AAAA;AAAA;AAAA;AAAA,GAKA,QAAQ,cAAc,GACxB,IACA,SAAS,cACP;AAAA;AAAA;AAAA;AAAA,2PAKA,QAAQ,cAAc,KACtB,SAAS,IACX;AAEJ,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AAEA,cAAQ,SAAS;AACjB,cAAQ,eAAe;AAEvB,cAAQ,OAAO,YAAY,QAAQ,gBAAgB,MAAM;AACvD,YAAI,OAAO,UAAU,aAAa;AAKhC,kBAAQ,cAAc,KAAK,eAAe;AAAA,QAC5C;AAGA,eAAO,cAAc,QAAQ,iBAAiB;AAAA,MAChD,CAAC;AAGD,YAAM,CAAC,kBAAkB,MAAM,yBAAM,MACnC,sBAAsB,SAAS,MAAM,CACvC;AAEA,UAAI,gBAAgB;AAClB,iBAAS,MAAM,qCACa,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sHAQ5C;AAAA,MACD;AAEA,cAAQ,oBAAoB,OAAO,YACjC,MAAM,QAAQ,cAAc,KAAK,mBAAmB,GACpD,GACF;AAIA,0BAAoB,cAAc,QAAQ,YAAY;AAEtD,aAAO;AAAA,IACT;AAEA,UAAM,qBAAqB,oBAAoB,EAAE,KAC/C,OAAO,iBAAiB;AACtB,YAAM,kBAAkB,aAAa,cAAc,aAAa;AAKhE,UAAI,iBAAiB;AACnB,cAAM,IAAI,QAAc,CAAC,YAAY;AACnC,0BAAgB,iBAAiB,eAAe,MAAM;AACpD,gBAAI,gBAAgB,UAAU,aAAa;AACzC,qBAAO,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAGA,YAAM,cAAc,SAAS,OAAO,EAAE,MAAM,CAAC,WAAU;AACrD,cAAM,IAAI,MAAM,6BAA6B,iCAAO,SAAS;AAAA,MAC/D,CAAC;AAED,aAAO;AAAA,IACT,CACF;AAKA,QAAI,QAAQ,gBAAgB;AAC1B,gCAA0B,kBAAkB;AAAA,IAC9C;AAEA,WAAO;AAAA,EACT;AACF;;;A6C5IO,0BAA0B,OAA4B,CAAC,GAAS;AACrE,MAAI,KAAK,OAAO;AACd;AAAA,EACF;AAEA,UAAQ,IACN,KAAK,SAAS,cAAc,mBAAmB,KAC/C,mCACF;AACF;;;ACPO,IAAM,aAAa,CACxB,YACgB;AAChB,SAAO,gBAAgB;AAPzB;AAUI,QAAI,CAAC,QAAQ,kBAAkB;AAC7B,eAAS,KACP,iKACF;AACA;AAAA,IACF;AAOA,YAAQ,cAAc,KAAK,iBAAiB;AAC5C,YAAQ,mBAAmB;AAC3B,WAAO,cAAc,QAAQ,iBAAiB;AAE9C,qBAAiB,EAAE,OAAO,cAAQ,iBAAR,mBAAsB,MAAM,CAAC;AAAA,EACzD;AACF;;;AC1BO,aACL,oBACG,UACG;AACN,kBAAgB,QAAQ,GAAG,QAAQ;AACrC;AAEO,yBAAyB,UAAkC;AAChE,WAAS,QAAQ,CAAC,YAAY;AAC5B,YAAQ,cAAc,KAAK;AAAA,EAC7B,CAAC;AACH;AAEO,uBACL,oBACG,cACH;AACA,SAAO,aAAa,SAAS,IAAI,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,eAAe;AAC1E;;;ACXO,IAAM,wBAAoD;AAAA,EAC/D,eAAe;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,WAAW,WAAW,sBAAsB;AAC1C,WAAO,cAAc;AAAA,EACvB;AACF;AAMO,6BACL,gBAC4B;AAC5B,SAAO,WACL,uBACA,kBAAkB,CAAC,CACrB;AACF;AAEO,6BACL,SACA,SACyB;AACzB,SAAO,CAAC,mBAAmB;AACzB,YAAQ,eAAe,oBAAoB,cAAc;AACzD,WAAO,QAAQ,QAAQ,cAAc,kBAAkB,CAAC,CAAC;AAAA,EAC3D;AACF;;;AC3CA,2BAIO;AACP,oBAAiC;AACjC,4BAA0C;;;ACN1C,2BAAiD;AAEjD,IAAM,OAAO,MAAM;AACjB,QAAM,IAAI,MAAM,iBAAiB;AACnC;AAEO,8CACL,WACU;AACV,SAAO,iCACF,YADE;AAAA,IAEL,IAAI,UAAS,UAAU,OAAO,UAAS,SAAS;AAAA,IAChD,KAAK;AAAA,IACL,MAAM;AAAA,IACN,QAAQ,UAAS;AAAA,IACjB,YAAY,UAAS;AAAA,IACrB,SAAS,UAAS;AAAA,IAClB,MAAM,IAAI,eAAe;AAAA,IACzB,YAAY,UAAS,QAAQ,IAAI,UAAU,KAAK;AAAA,UAC1C,OAAO;AACX,aAAO,UAAS,QAAQ;AAAA,IAC1B;AAAA,UACM,OAAO;AACX,aAAO,KAAK,MAAM,UAAS,QAAQ,EAAE;AAAA,IACvC;AAAA,UACM,cAAc;AAClB,aAAO,uCAAa,UAAS,QAAQ,EAAE;AAAA,IACzC;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AACF;;;ADfO,uCACL,SACA,SACkC;AAClC,QAAM,cAAc,IAAI,sCAAiB;AAAA,IACvC,MAAM;AAAA,IACN,cAAc,CAAC,IAAI,+BAAiB,GAAG,IAAI,gDAA0B,CAAC;AAAA,EACxE,CAAC;AAED,cAAY,GAAG,WAAW,OAAO,YAAY;AAC3C,UAAM,gBAAgB,IAAI,cAAc,QAAQ,KAAK,iCAChD,UADgD;AAAA,MAEnD,MAAM,MAAM,QAAQ,YAAY;AAAA,IAClC,EAAC;AAED,UAAM,YAAW,MAAM,cACrB,eACA,QAAQ,iBACR,SACA,QAAQ,SACR;AAAA,MACE,kBAAkB,WAAU;AAC1B,eAAO;AAAA,UACL,QAAQ,UAAS;AAAA,UACjB,YAAY,UAAS;AAAA,UACrB,SAAS,UAAS,QAAQ,IAAI;AAAA,UAC9B,MAAM,UAAS;AAAA,UACf,OAAO,UAAS;AAAA,QAClB;AAAA,MACF;AAAA,MACA,iBAAiB,GAAG,EAAE,SAAS,eAAe,iBAAiB;AAC7D,YAAI,CAAC,QAAQ,OAAO;AAClB,kBAAQ,QAAQ,KAAK,mBAAmB,CAAC,cAAa;AACpD,oBAAQ,IACN,eACA,kBAAkB,SAAQ,GAC1B,aACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CACF;AAEA,QAAI,WAAU;AACZ,cAAQ,YAAY,SAAQ;AAAA,IAC9B;AAAA,EACF,CAAC;AAED,cAAY,GAAG,YAAY,CAAC,SAAS,cAAa;AAChD,QAAI,CAAC,QAAQ,IAAI;AACf;AAAA,IACF;AAEA,UAAM,kBAAkB,qCAAqC,SAAQ;AAErE,QAAI,UAAS,QAAQ,IAAI,cAAc,MAAM,OAAO;AAClD,cAAQ,QAAQ,KAAK,mBAAmB,iBAAiB,QAAQ,EAAE;AAAA,IACrE,OAAO;AACL,cAAQ,QAAQ,KAAK,mBAAmB,iBAAiB,QAAQ,EAAE;AAAA,IACrE;AAAA,EACF,CAAC;AAED,cAAY,MAAM;AAElB,SAAO;AACT;;;AEhFO,6BACL,SACc;AACd,SAAO,qBAAqB,SAAS;AACnC,YAAQ,sBAAsB,8BAC5B,SACA,OACF;AAEA,sBAAkB;AAAA,MAChB,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,EACT;AACF;;;ACjBO,4BACL,SACa;AACb,SAAO,gBAAgB;AANzB;AAOI,kBAAQ,wBAAR,mBAA6B;AAC7B,qBAAiB,EAAE,OAAO,cAAQ,iBAAR,mBAAsB,MAAM,CAAC;AAAA,EACzD;AACF;;;ACLO,oBACL,QACA,aACM;AACN,QAAM,UAAU,OAAO;AAGvB,MAAI,QAAQ,UAAU;AACpB;AAAA,EACF;AAEA,SAAO,OAAO,SAAU,UAAU,OAAM;AACtC,gBAAY,KAAK,OAAO,GAAG,KAAI;AAC/B,WAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAI;AAAA,EAC1C;AAGA,SAAO,KAAK,WAAW;AACzB;;;ACpBO,yBAA4B,QAAoC;AACrE,QAAM,QAAQ,CAAC,GAAG,MAAM;AACxB,SAAO,OAAO,KAAK;AACnB,SAAO;AACT;;;AvDsBA,IAAI,YAAwB,CAAC;AAQtB,wBACF,iBACa;AAChB,kBAAgB,QAAQ,CAAC,YAAY;AACnC,QAAI,MAAM,QAAQ,OAAO;AACvB,YAAM,IAAI,MACR,SAAS,cACP,0JACF,CACF;AAAA,EACJ,CAAC;AAGD,MAAI,2CAAc,GAAG;AACnB,UAAM,IAAI,MACR,SAAS,cACP,6HACF,CACF;AAAA,EACF;AAEA,QAAM,UAAU,IAAI,+CAA6C;AACjE,QAAM,gBAAgB,IAAI,+CAA6C;AACvE,aAAW,SAAS,aAAa;AAEjC,QAAM,UAAsC;AAAA,IAG1C,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,iBAAiB,CAAC,GAAG,eAAe;AAAA,IACpC;AAAA,IACA,eAAe;AAAA,MACb,GAAG,WAAW,UAAU;AACtB,gBAAQ,OAAO,YACb,UAAU,eACV,WACA,CAAC,UAAwB;AAEvB,cAAI,MAAM,WAAW,QAAQ,QAAQ;AACnC;AAAA,UACF;AAEA,gBAAM,UAAU,MAAM;AAKtB,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AAEA,cAAI,QAAQ,SAAS,WAAW;AAC9B,qBAAS,OAAO,OAAO;AAAA,UACzB;AAAA,QACF,CACF;AAAA,MACF;AAAA,MACA,KAAK,MAAM;AAjGjB;AAkGQ,sBAAQ,WAAR,mBAAgB,YAAY;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YACE,QACA,WACA,UACA;AACA,eAAO,iBAAiB,WAAW,QAAQ;AAC3C,kBAAU,KAAK,EAAE,WAAW,QAAQ,SAAS,CAAC;AAE9C,eAAO,MAAM;AACX,iBAAO,oBAAoB,WAAW,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,MACA,qBAAqB;AACnB,mBAAW,EAAE,QAAQ,WAAW,cAAc,WAAW;AACvD,iBAAO,oBAAoB,WAAW,QAAQ;AAAA,QAChD;AACA,oBAAY,CAAC;AAAA,MACf;AAAA,MACA,KAAK,WAAW;AACd,cAAM,WAA8B,CAAC;AAErC,eAAO,IAAI,QAKT,CAAC,SAAS,WAAW;AACrB,gBAAM,wBAAwB,CAAC,UAAwB;AACrD,gBAAI;AACF,oBAAM,UAAU,MAAM;AAEtB,kBAAI,QAAQ,SAAS,WAAW;AAC9B,wBAAQ,OAAO;AAAA,cACjB;AAAA,YACF,SAAS,QAAP;AACA,qBAAO,MAAK;AAAA,YACd;AAAA,UACF;AAEA,mBAAS,KACP,QAAQ,OAAO,YACb,UAAU,eACV,WACA,qBACF,GACA,QAAQ,OAAO,YACb,UAAU,eACV,gBACA,MACF,CACF;AAAA,QACF,CAAC,EAAE,QAAQ,MAAM;AACf,mBAAS,QAAQ,CAAC,WAAW,OAAO,CAAC;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,iBACE,CAAE,oBAAmB,cAAc,SAAS,aAAa;AAAA,EAC7D;AAEA,QAAM,eAAe,QAAQ,kBACzB,oBAAoB,OAAO,IAC3B,mBAAmB,OAAO;AAC9B,QAAM,cAAc,QAAQ,kBACxB,mBAAmB,OAAO,IAC1B,WAAW,OAAO;AAEtB,SAAO;AAAA,IACL,OAAO,oBAAoB,cAAc,OAAO;AAAA,IAChD,OAAO;AACL,cAAQ,OAAO,mBAAmB;AAClC,cAAQ,QAAQ,mBAAmB;AACnC,oBAAc,mBAAmB;AACjC,kBAAY;AAAA,IACd;AAAA,IAEA,OAAO,UAAU;AACf,MAAoB,IAAI,QAAQ,iBAAiB,GAAG,QAAQ;AAAA,IAC9D;AAAA,IAEA,kBAAkB;AAChB,MAAoB,gBAAgB,QAAQ,eAAe;AAAA,IAC7D;AAAA,IAEA,iBAAiB,cAAc;AAC7B,cAAQ,kBAAkB,AAAoB,cAC5C,iBACA,GAAG,YACL;AAAA,IACF;AAAA,IAEA,eAAe;AACb,aAAO,gBAAgB,QAAQ,eAAe;AAAA,IAChD;AAAA,IAEA,gBAAgB;AACd,YAAM,WAAW,KAAK,aAAa;AAEnC,eAAS,QAAQ,CAAC,YAAY;AAC5B,cAAM,EAAE,QAAQ,cAAc,QAAQ;AACtC,cAAM,SAAS,QAAQ,KAAK,eAAe,eAAe,IACtD,cACA;AAEJ,gBAAQ,eAAe,GAAG,UAAU,QAAQ;AAE5C,YAAI,WAAW;AACb,kBAAQ,IAAI,gBAAgB,WAAW;AAAA,QACzC;AAEA,gBAAQ,IAAI,YAAY,OAAO;AAE/B,YAAI,mBAAmB,aAAa;AAClC,kBAAQ,IACN,UACA,8BAA8B,QAAQ,KAAK,MAC7C;AAAA,QACF;AAEA,gBAAQ,SAAS;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,IAEA,QAAQ;AAAA,MACN,MAAM,MAAM;AACV,eAAO,cAAc,GAAG,GAAG,IAAI;AAAA,MACjC;AAAA,MACA,kBAAkB,MAAM;AACtB,eAAO,cAAc,eAAe,GAAG,IAAI;AAAA,MAC7C;AAAA,MACA,sBAAsB,MAAM;AAC1B,eAAO,cAAc,mBAAmB,GAAG,IAAI;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACF;;;AwDpOA,2BACE,QACA;AACA,SAAO,CAKL,MACA,aAUG;AACH,WAAO,IAAI,YAAY,QAAQ,MAAM,QAAQ;AAAA,EAC/C;AACF;AAEO,IAAM,OAAO;AAAA,EAClB,KAAK,kBAAkB,IAAI;AAAA,EAC3B,MAAM,kBAAkB,iBAAgB;AAAA,EACxC,KAAK,kBAAkB,eAAe;AAAA,EACtC,MAAM,kBAAkB,iBAAgB;AAAA,EACxC,KAAK,kBAAkB,eAAe;AAAA,EACtC,QAAQ,kBAAkB,qBAAkB;AAAA,EAC5C,OAAO,kBAAkB,mBAAiB;AAAA,EAC1C,SAAS,kBAAkB,uBAAmB;AAChD;;;ACrBA,oCACE,eACA,KACA;AACA,SAAO,CAIL,eAIA,aAIG;AACH,WAAO,IAAI,eACT,eACA,eACA,KACA,QACF;AAAA,EACF;AACF;AAEA,uCAAuC,KAAW;AAChD,SAAO,CAIL,aAIG;AACH,WAAO,IAAI,eACT,OACA,IAAI,OAAO,IAAI,GACf,KACA,QACF;AAAA,EACF;AACF;AAEA,IAAM,0BAA0B;AAAA,EAS9B,WAAW,8BAA8B,GAAG;AAAA,EAU5C,OAAO,2BAA2B,SAA8B,GAAG;AAAA,EAUnE,UAAU,2BAA2B,YAAiC,GAAG;AAC3E;AAEA,2BAA2B,KAA2C;AACpE,SAAO;AAAA,IACL,WAAW,8BAA8B,GAAG;AAAA,IAC5C,OAAO,2BAA2B,SAA8B,GAAG;AAAA,IACnE,UAAU,2BAA2B,YAAiC,GAAG;AAAA,EAC3E;AACF;AAEO,IAAM,UAAU,iCAClB,0BADkB;AAAA,EAErB,MAAM;AACR;", "names": []}