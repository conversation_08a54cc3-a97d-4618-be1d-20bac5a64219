{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useId from \"rc-util/es/hooks/useId\";\nimport contains from \"rc-util/es/Dom/contains\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Mask from './Mask';\nimport { getMotionName } from '../util';\nimport Content from './Content';\nexport default function Dialog(props) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dialog' : _props$prefixCls,\n    zIndex = props.zIndex,\n    _props$visible = props.visible,\n    visible = _props$visible === void 0 ? false : _props$visible,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    _props$focusTriggerAf = props.focusTriggerAfterClose,\n    focusTriggerAfterClose = _props$focusTriggerAf === void 0 ? true : _props$focusTriggerAf,\n    scrollLocker = props.scrollLocker,\n    title = props.title,\n    wrapStyle = props.wrapStyle,\n    wrapClassName = props.wrapClassName,\n    wrapProps = props.wrapProps,\n    onClose = props.onClose,\n    afterClose = props.afterClose,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    _props$closable = props.closable,\n    closable = _props$closable === void 0 ? true : _props$closable,\n    _props$mask = props.mask,\n    mask = _props$mask === void 0 ? true : _props$mask,\n    maskTransitionName = props.maskTransitionName,\n    maskAnimation = props.maskAnimation,\n    _props$maskClosable = props.maskClosable,\n    maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n    maskStyle = props.maskStyle,\n    maskProps = props.maskProps,\n    rootClassName = props.rootClassName;\n  var lastOutSideActiveElementRef = useRef();\n  var wrapperRef = useRef();\n  var contentRef = useRef();\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1]; // ========================== Init ==========================\n\n  var ariaId = useId(); // ========================= Events =========================\n\n  function onDialogVisibleChanged(newVisible) {\n    if (newVisible) {\n      // Try to focus\n      if (!contains(wrapperRef.current, document.activeElement)) {\n        var _contentRef$current;\n        lastOutSideActiveElementRef.current = document.activeElement;\n        (_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 ? void 0 : _contentRef$current.focus();\n      }\n    } else {\n      // Clean up scroll bar & focus back\n      setAnimatedVisible(false);\n      if (mask && lastOutSideActiveElementRef.current && focusTriggerAfterClose) {\n        try {\n          lastOutSideActiveElementRef.current.focus({\n            preventScroll: true\n          });\n        } catch (e) {// Do nothing\n        }\n        lastOutSideActiveElementRef.current = null;\n      } // Trigger afterClose only when change visible from true to false\n\n      if (animatedVisible) {\n        afterClose === null || afterClose === void 0 ? void 0 : afterClose();\n      }\n    }\n  }\n  function onInternalClose(e) {\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n  } // >>> Content\n\n  var contentClickRef = useRef(false);\n  var contentTimeoutRef = useRef(); // We need record content click incase content popup out of dialog\n\n  var onContentMouseDown = function onContentMouseDown() {\n    clearTimeout(contentTimeoutRef.current);\n    contentClickRef.current = true;\n  };\n  var onContentMouseUp = function onContentMouseUp() {\n    contentTimeoutRef.current = setTimeout(function () {\n      contentClickRef.current = false;\n    });\n  }; // >>> Wrapper\n  // Close only when element not on dialog\n\n  var onWrapperClick = null;\n  if (maskClosable) {\n    onWrapperClick = function onWrapperClick(e) {\n      if (contentClickRef.current) {\n        contentClickRef.current = false;\n      } else if (wrapperRef.current === e.target) {\n        onInternalClose(e);\n      }\n    };\n  }\n  function onWrapperKeyDown(e) {\n    if (keyboard && e.keyCode === KeyCode.ESC) {\n      e.stopPropagation();\n      onInternalClose(e);\n      return;\n    } // keep focus inside dialog\n\n    if (visible) {\n      if (e.keyCode === KeyCode.TAB) {\n        contentRef.current.changeActive(!e.shiftKey);\n      }\n    }\n  } // ========================= Effect =========================\n\n  useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n    return function () {};\n  }, [visible]); // Remove direct should also check the scroll bar update\n\n  useEffect(function () {\n    return function () {\n      clearTimeout(contentTimeoutRef.current);\n    };\n  }, []);\n  useEffect(function () {\n    if (animatedVisible) {\n      scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.lock();\n      return scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.unLock;\n    }\n    return function () {};\n  }, [animatedVisible, scrollLocker]); // ========================= Render =========================\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-root\"), rootClassName)\n  }, pickAttrs(props, {\n    data: true\n  })), /*#__PURE__*/React.createElement(Mask, {\n    prefixCls: prefixCls,\n    visible: mask && visible,\n    motionName: getMotionName(prefixCls, maskTransitionName, maskAnimation),\n    style: _objectSpread({\n      zIndex: zIndex\n    }, maskStyle),\n    maskProps: maskProps\n  }), /*#__PURE__*/React.createElement(\"div\", _extends({\n    tabIndex: -1,\n    onKeyDown: onWrapperKeyDown,\n    className: classNames(\"\".concat(prefixCls, \"-wrap\"), wrapClassName),\n    ref: wrapperRef,\n    onClick: onWrapperClick,\n    \"aria-labelledby\": title ? ariaId : null,\n    style: _objectSpread(_objectSpread({\n      zIndex: zIndex\n    }, wrapStyle), {}, {\n      display: !animatedVisible ? 'none' : null\n    })\n  }, wrapProps), /*#__PURE__*/React.createElement(Content, _extends({}, props, {\n    onMouseDown: onContentMouseDown,\n    onMouseUp: onContentMouseUp,\n    ref: contentRef,\n    closable: closable,\n    ariaId: ariaId,\n    prefixCls: prefixCls,\n    visible: visible,\n    onClose: onInternalClose,\n    onVisibleChanged: onDialogVisibleChanged,\n    motionName: getMotionName(prefixCls, transitionName, animation)\n  }))));\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "React", "useRef", "useEffect", "classNames", "KeyCode", "useId", "contains", "pickAttrs", "Mask", "getMotionName", "Content", "Dialog", "props", "_props$prefixCls", "prefixCls", "zIndex", "_props$visible", "visible", "_props$keyboard", "keyboard", "_props$focusTriggerAf", "focusTriggerAfterClose", "scrollLocker", "title", "wrapStyle", "wrapClassName", "wrapProps", "onClose", "afterClose", "transitionName", "animation", "_props$closable", "closable", "_props$mask", "mask", "maskTransitionName", "maskAnimation", "_props$maskClosable", "maskClosable", "maskStyle", "maskProps", "rootClassName", "lastOutSideActiveElementRef", "wrapperRef", "contentRef", "_React$useState", "useState", "_React$useState2", "animatedVisible", "setAnimatedVisible", "ariaId", "onDialogVisibleChanged", "newVisible", "current", "document", "activeElement", "_contentRef$current", "focus", "preventScroll", "e", "onInternalClose", "contentClickRef", "contentTimeoutRef", "onContentMouseDown", "clearTimeout", "onContentMouseUp", "setTimeout", "onWrapperClick", "target", "onWrapperKeyDown", "keyCode", "ESC", "stopPropagation", "TAB", "changeActive", "shift<PERSON>ey", "lock", "unLock", "createElement", "className", "concat", "data", "motionName", "style", "tabIndex", "onKeyDown", "ref", "onClick", "display", "onMouseDown", "onMouseUp", "onVisibleChanged"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-dialog/es/Dialog/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useId from \"rc-util/es/hooks/useId\";\nimport contains from \"rc-util/es/Dom/contains\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Mask from './Mask';\nimport { getMotionName } from '../util';\nimport Content from './Content';\nexport default function Dialog(props) {\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-dialog' : _props$prefixCls,\n      zIndex = props.zIndex,\n      _props$visible = props.visible,\n      visible = _props$visible === void 0 ? false : _props$visible,\n      _props$keyboard = props.keyboard,\n      keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n      _props$focusTriggerAf = props.focusTriggerAfterClose,\n      focusTriggerAfterClose = _props$focusTriggerAf === void 0 ? true : _props$focusTriggerAf,\n      scrollLocker = props.scrollLocker,\n      title = props.title,\n      wrapStyle = props.wrapStyle,\n      wrapClassName = props.wrapClassName,\n      wrapProps = props.wrapProps,\n      onClose = props.onClose,\n      afterClose = props.afterClose,\n      transitionName = props.transitionName,\n      animation = props.animation,\n      _props$closable = props.closable,\n      closable = _props$closable === void 0 ? true : _props$closable,\n      _props$mask = props.mask,\n      mask = _props$mask === void 0 ? true : _props$mask,\n      maskTransitionName = props.maskTransitionName,\n      maskAnimation = props.maskAnimation,\n      _props$maskClosable = props.maskClosable,\n      maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n      maskStyle = props.maskStyle,\n      maskProps = props.maskProps,\n      rootClassName = props.rootClassName;\n  var lastOutSideActiveElementRef = useRef();\n  var wrapperRef = useRef();\n  var contentRef = useRef();\n\n  var _React$useState = React.useState(visible),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      animatedVisible = _React$useState2[0],\n      setAnimatedVisible = _React$useState2[1]; // ========================== Init ==========================\n\n\n  var ariaId = useId(); // ========================= Events =========================\n\n  function onDialogVisibleChanged(newVisible) {\n    if (newVisible) {\n      // Try to focus\n      if (!contains(wrapperRef.current, document.activeElement)) {\n        var _contentRef$current;\n\n        lastOutSideActiveElementRef.current = document.activeElement;\n        (_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 ? void 0 : _contentRef$current.focus();\n      }\n    } else {\n      // Clean up scroll bar & focus back\n      setAnimatedVisible(false);\n\n      if (mask && lastOutSideActiveElementRef.current && focusTriggerAfterClose) {\n        try {\n          lastOutSideActiveElementRef.current.focus({\n            preventScroll: true\n          });\n        } catch (e) {// Do nothing\n        }\n\n        lastOutSideActiveElementRef.current = null;\n      } // Trigger afterClose only when change visible from true to false\n\n\n      if (animatedVisible) {\n        afterClose === null || afterClose === void 0 ? void 0 : afterClose();\n      }\n    }\n  }\n\n  function onInternalClose(e) {\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n  } // >>> Content\n\n\n  var contentClickRef = useRef(false);\n  var contentTimeoutRef = useRef(); // We need record content click incase content popup out of dialog\n\n  var onContentMouseDown = function onContentMouseDown() {\n    clearTimeout(contentTimeoutRef.current);\n    contentClickRef.current = true;\n  };\n\n  var onContentMouseUp = function onContentMouseUp() {\n    contentTimeoutRef.current = setTimeout(function () {\n      contentClickRef.current = false;\n    });\n  }; // >>> Wrapper\n  // Close only when element not on dialog\n\n\n  var onWrapperClick = null;\n\n  if (maskClosable) {\n    onWrapperClick = function onWrapperClick(e) {\n      if (contentClickRef.current) {\n        contentClickRef.current = false;\n      } else if (wrapperRef.current === e.target) {\n        onInternalClose(e);\n      }\n    };\n  }\n\n  function onWrapperKeyDown(e) {\n    if (keyboard && e.keyCode === KeyCode.ESC) {\n      e.stopPropagation();\n      onInternalClose(e);\n      return;\n    } // keep focus inside dialog\n\n\n    if (visible) {\n      if (e.keyCode === KeyCode.TAB) {\n        contentRef.current.changeActive(!e.shiftKey);\n      }\n    }\n  } // ========================= Effect =========================\n\n\n  useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n\n    return function () {};\n  }, [visible]); // Remove direct should also check the scroll bar update\n\n  useEffect(function () {\n    return function () {\n      clearTimeout(contentTimeoutRef.current);\n    };\n  }, []);\n  useEffect(function () {\n    if (animatedVisible) {\n      scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.lock();\n      return scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.unLock;\n    }\n\n    return function () {};\n  }, [animatedVisible, scrollLocker]); // ========================= Render =========================\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-root\"), rootClassName)\n  }, pickAttrs(props, {\n    data: true\n  })), /*#__PURE__*/React.createElement(Mask, {\n    prefixCls: prefixCls,\n    visible: mask && visible,\n    motionName: getMotionName(prefixCls, maskTransitionName, maskAnimation),\n    style: _objectSpread({\n      zIndex: zIndex\n    }, maskStyle),\n    maskProps: maskProps\n  }), /*#__PURE__*/React.createElement(\"div\", _extends({\n    tabIndex: -1,\n    onKeyDown: onWrapperKeyDown,\n    className: classNames(\"\".concat(prefixCls, \"-wrap\"), wrapClassName),\n    ref: wrapperRef,\n    onClick: onWrapperClick,\n    \"aria-labelledby\": title ? ariaId : null,\n    style: _objectSpread(_objectSpread({\n      zIndex: zIndex\n    }, wrapStyle), {}, {\n      display: !animatedVisible ? 'none' : null\n    })\n  }, wrapProps), /*#__PURE__*/React.createElement(Content, _extends({}, props, {\n    onMouseDown: onContentMouseDown,\n    onMouseUp: onContentMouseUp,\n    ref: contentRef,\n    closable: closable,\n    ariaId: ariaId,\n    prefixCls: prefixCls,\n    visible: visible,\n    onClose: onInternalClose,\n    onVisibleChanged: onDialogVisibleChanged,\n    motionName: getMotionName(prefixCls, transitionName, animation)\n  }))));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,OAAO,MAAM,WAAW;AAC/B,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EACpC,IAAIC,gBAAgB,GAAGD,KAAK,CAACE,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,cAAc,GAAGJ,KAAK,CAACK,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,cAAc;IAC5DE,eAAe,GAAGN,KAAK,CAACO,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,qBAAqB,GAAGR,KAAK,CAACS,sBAAsB;IACpDA,sBAAsB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACxFE,YAAY,GAAGV,KAAK,CAACU,YAAY;IACjCC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,aAAa,GAAGb,KAAK,CAACa,aAAa;IACnCC,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BC,OAAO,GAAGf,KAAK,CAACe,OAAO;IACvBC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,cAAc,GAAGjB,KAAK,CAACiB,cAAc;IACrCC,SAAS,GAAGlB,KAAK,CAACkB,SAAS;IAC3BC,eAAe,GAAGnB,KAAK,CAACoB,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,WAAW,GAAGrB,KAAK,CAACsB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,WAAW;IAClDE,kBAAkB,GAAGvB,KAAK,CAACuB,kBAAkB;IAC7CC,aAAa,GAAGxB,KAAK,CAACwB,aAAa;IACnCC,mBAAmB,GAAGzB,KAAK,CAAC0B,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;IAC1EE,SAAS,GAAG3B,KAAK,CAAC2B,SAAS;IAC3BC,SAAS,GAAG5B,KAAK,CAAC4B,SAAS;IAC3BC,aAAa,GAAG7B,KAAK,CAAC6B,aAAa;EACvC,IAAIC,2BAA2B,GAAGzC,MAAM,CAAC,CAAC;EAC1C,IAAI0C,UAAU,GAAG1C,MAAM,CAAC,CAAC;EACzB,IAAI2C,UAAU,GAAG3C,MAAM,CAAC,CAAC;EAEzB,IAAI4C,eAAe,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC7B,OAAO,CAAC;IACzC8B,gBAAgB,GAAGhD,cAAc,CAAC8C,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG9C,IAAIG,MAAM,GAAG7C,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEtB,SAAS8C,sBAAsBA,CAACC,UAAU,EAAE;IAC1C,IAAIA,UAAU,EAAE;MACd;MACA,IAAI,CAAC9C,QAAQ,CAACqC,UAAU,CAACU,OAAO,EAAEC,QAAQ,CAACC,aAAa,CAAC,EAAE;QACzD,IAAIC,mBAAmB;QAEvBd,2BAA2B,CAACW,OAAO,GAAGC,QAAQ,CAACC,aAAa;QAC5D,CAACC,mBAAmB,GAAGZ,UAAU,CAACS,OAAO,MAAM,IAAI,IAAIG,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,KAAK,CAAC,CAAC;MAC9H;IACF,CAAC,MAAM;MACL;MACAR,kBAAkB,CAAC,KAAK,CAAC;MAEzB,IAAIf,IAAI,IAAIQ,2BAA2B,CAACW,OAAO,IAAIhC,sBAAsB,EAAE;QACzE,IAAI;UACFqB,2BAA2B,CAACW,OAAO,CAACI,KAAK,CAAC;YACxCC,aAAa,EAAE;UACjB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;QAAA;QAGbjB,2BAA2B,CAACW,OAAO,GAAG,IAAI;MAC5C,CAAC,CAAC;;MAGF,IAAIL,eAAe,EAAE;QACnBpB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC;MACtE;IACF;EACF;EAEA,SAASgC,eAAeA,CAACD,CAAC,EAAE;IAC1BhC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgC,CAAC,CAAC;EAC9D,CAAC,CAAC;;EAGF,IAAIE,eAAe,GAAG5D,MAAM,CAAC,KAAK,CAAC;EACnC,IAAI6D,iBAAiB,GAAG7D,MAAM,CAAC,CAAC,CAAC,CAAC;;EAElC,IAAI8D,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrDC,YAAY,CAACF,iBAAiB,CAACT,OAAO,CAAC;IACvCQ,eAAe,CAACR,OAAO,GAAG,IAAI;EAChC,CAAC;EAED,IAAIY,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDH,iBAAiB,CAACT,OAAO,GAAGa,UAAU,CAAC,YAAY;MACjDL,eAAe,CAACR,OAAO,GAAG,KAAK;IACjC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;EACH;;EAGA,IAAIc,cAAc,GAAG,IAAI;EAEzB,IAAI7B,YAAY,EAAE;IAChB6B,cAAc,GAAG,SAASA,cAAcA,CAACR,CAAC,EAAE;MAC1C,IAAIE,eAAe,CAACR,OAAO,EAAE;QAC3BQ,eAAe,CAACR,OAAO,GAAG,KAAK;MACjC,CAAC,MAAM,IAAIV,UAAU,CAACU,OAAO,KAAKM,CAAC,CAACS,MAAM,EAAE;QAC1CR,eAAe,CAACD,CAAC,CAAC;MACpB;IACF,CAAC;EACH;EAEA,SAASU,gBAAgBA,CAACV,CAAC,EAAE;IAC3B,IAAIxC,QAAQ,IAAIwC,CAAC,CAACW,OAAO,KAAKlE,OAAO,CAACmE,GAAG,EAAE;MACzCZ,CAAC,CAACa,eAAe,CAAC,CAAC;MACnBZ,eAAe,CAACD,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;;IAGF,IAAI1C,OAAO,EAAE;MACX,IAAI0C,CAAC,CAACW,OAAO,KAAKlE,OAAO,CAACqE,GAAG,EAAE;QAC7B7B,UAAU,CAACS,OAAO,CAACqB,YAAY,CAAC,CAACf,CAAC,CAACgB,QAAQ,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;;EAGFzE,SAAS,CAAC,YAAY;IACpB,IAAIe,OAAO,EAAE;MACXgC,kBAAkB,CAAC,IAAI,CAAC;IAC1B;IAEA,OAAO,YAAY,CAAC,CAAC;EACvB,CAAC,EAAE,CAAChC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEff,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjB8D,YAAY,CAACF,iBAAiB,CAACT,OAAO,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACNnD,SAAS,CAAC,YAAY;IACpB,IAAI8C,eAAe,EAAE;MACnB1B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACsD,IAAI,CAAC,CAAC;MAC/E,OAAOtD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACuD,MAAM;IACxF;IAEA,OAAO,YAAY,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC7B,eAAe,EAAE1B,YAAY,CAAC,CAAC,CAAC,CAAC;;EAErC,OAAO,aAAatB,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAEjF,QAAQ,CAAC;IACtDkF,SAAS,EAAE5E,UAAU,CAAC,EAAE,CAAC6E,MAAM,CAAClE,SAAS,EAAE,OAAO,CAAC,EAAE2B,aAAa;EACpE,CAAC,EAAElC,SAAS,CAACK,KAAK,EAAE;IAClBqE,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,EAAE,aAAajF,KAAK,CAAC8E,aAAa,CAACtE,IAAI,EAAE;IAC1CM,SAAS,EAAEA,SAAS;IACpBG,OAAO,EAAEiB,IAAI,IAAIjB,OAAO;IACxBiE,UAAU,EAAEzE,aAAa,CAACK,SAAS,EAAEqB,kBAAkB,EAAEC,aAAa,CAAC;IACvE+C,KAAK,EAAErF,aAAa,CAAC;MACnBiB,MAAM,EAAEA;IACV,CAAC,EAAEwB,SAAS,CAAC;IACbC,SAAS,EAAEA;EACb,CAAC,CAAC,EAAE,aAAaxC,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAEjF,QAAQ,CAAC;IACnDuF,QAAQ,EAAE,CAAC,CAAC;IACZC,SAAS,EAAEhB,gBAAgB;IAC3BU,SAAS,EAAE5E,UAAU,CAAC,EAAE,CAAC6E,MAAM,CAAClE,SAAS,EAAE,OAAO,CAAC,EAAEW,aAAa,CAAC;IACnE6D,GAAG,EAAE3C,UAAU;IACf4C,OAAO,EAAEpB,cAAc;IACvB,iBAAiB,EAAE5C,KAAK,GAAG2B,MAAM,GAAG,IAAI;IACxCiC,KAAK,EAAErF,aAAa,CAACA,aAAa,CAAC;MACjCiB,MAAM,EAAEA;IACV,CAAC,EAAES,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACjBgE,OAAO,EAAE,CAACxC,eAAe,GAAG,MAAM,GAAG;IACvC,CAAC;EACH,CAAC,EAAEtB,SAAS,CAAC,EAAE,aAAa1B,KAAK,CAAC8E,aAAa,CAACpE,OAAO,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;IAC3E6E,WAAW,EAAE1B,kBAAkB;IAC/B2B,SAAS,EAAEzB,gBAAgB;IAC3BqB,GAAG,EAAE1C,UAAU;IACfZ,QAAQ,EAAEA,QAAQ;IAClBkB,MAAM,EAAEA,MAAM;IACdpC,SAAS,EAAEA,SAAS;IACpBG,OAAO,EAAEA,OAAO;IAChBU,OAAO,EAAEiC,eAAe;IACxB+B,gBAAgB,EAAExC,sBAAsB;IACxC+B,UAAU,EAAEzE,aAAa,CAACK,SAAS,EAAEe,cAAc,EAAEC,SAAS;EAChE,CAAC,CAAC,CAAC,CAAC,CAAC;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}