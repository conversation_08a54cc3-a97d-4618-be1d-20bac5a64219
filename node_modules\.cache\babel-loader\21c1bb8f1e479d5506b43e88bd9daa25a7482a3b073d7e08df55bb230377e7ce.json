{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nmodule.exports = utils.isStandardBrowserEnv() ?\n// Standard browser envs have full support of the APIs needed to test\n// whether the request URL is of the same origin as current location.\nfunction standardBrowserEnv() {\n  var msie = /(msie|trident)/i.test(navigator.userAgent);\n  var urlParsingNode = document.createElement('a');\n  var originURL;\n\n  /**\n  * Parse a URL to discover it's components\n  *\n  * @param {String} url The URL to be parsed\n  * @returns {Object}\n  */\n  function resolveURL(url) {\n    var href = url;\n    if (msie) {\n      // IE needs attribute set twice to normalize properties\n      urlParsingNode.setAttribute('href', href);\n      href = urlParsingNode.href;\n    }\n    urlParsingNode.setAttribute('href', href);\n\n    // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n    return {\n      href: urlParsingNode.href,\n      protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n      host: urlParsingNode.host,\n      search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n      hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n      hostname: urlParsingNode.hostname,\n      port: urlParsingNode.port,\n      pathname: urlParsingNode.pathname.charAt(0) === '/' ? urlParsingNode.pathname : '/' + urlParsingNode.pathname\n    };\n  }\n  originURL = resolveURL(window.location.href);\n\n  /**\n  * Determine if a URL shares the same origin as the current location\n  *\n  * @param {String} requestURL The URL to test\n  * @returns {boolean} True if URL shares the same origin, otherwise false\n  */\n  return function isURLSameOrigin(requestURL) {\n    var parsed = utils.isString(requestURL) ? resolveURL(requestURL) : requestURL;\n    return parsed.protocol === originURL.protocol && parsed.host === originURL.host;\n  };\n}() :\n// Non standard browser envs (web workers, react-native) lack needed support.\nfunction nonStandardBrowserEnv() {\n  return function isURLSameOrigin() {\n    return true;\n  };\n}();", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "isStandardBrowserEnv", "standardBrowserEnv", "msie", "test", "navigator", "userAgent", "urlParsingNode", "document", "createElement", "originURL", "resolveURL", "url", "href", "setAttribute", "protocol", "replace", "host", "search", "hash", "hostname", "port", "pathname", "char<PERSON>t", "window", "location", "isURLSameOrigin", "requestURL", "parsed", "isString", "nonStandardBrowserEnv"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/axios/lib/helpers/isURLSameOrigin.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs have full support of the APIs needed to test\n  // whether the request URL is of the same origin as current location.\n    (function standardBrowserEnv() {\n      var msie = /(msie|trident)/i.test(navigator.userAgent);\n      var urlParsingNode = document.createElement('a');\n      var originURL;\n\n      /**\n    * Parse a URL to discover it's components\n    *\n    * @param {String} url The URL to be parsed\n    * @returns {Object}\n    */\n      function resolveURL(url) {\n        var href = url;\n\n        if (msie) {\n        // IE needs attribute set twice to normalize properties\n          urlParsingNode.setAttribute('href', href);\n          href = urlParsingNode.href;\n        }\n\n        urlParsingNode.setAttribute('href', href);\n\n        // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n        return {\n          href: urlParsingNode.href,\n          protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n          host: urlParsingNode.host,\n          search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n          hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n          hostname: urlParsingNode.hostname,\n          port: urlParsingNode.port,\n          pathname: (urlParsingNode.pathname.charAt(0) === '/') ?\n            urlParsingNode.pathname :\n            '/' + urlParsingNode.pathname\n        };\n      }\n\n      originURL = resolveURL(window.location.href);\n\n      /**\n    * Determine if a URL shares the same origin as the current location\n    *\n    * @param {String} requestURL The URL to test\n    * @returns {boolean} True if URL shares the same origin, otherwise false\n    */\n      return function isURLSameOrigin(requestURL) {\n        var parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;\n        return (parsed.protocol === originURL.protocol &&\n            parsed.host === originURL.host);\n      };\n    })() :\n\n  // Non standard browser envs (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return function isURLSameOrigin() {\n        return true;\n      };\n    })()\n);\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjCC,MAAM,CAACC,OAAO,GACZH,KAAK,CAACI,oBAAoB,CAAC,CAAC;AAE5B;AACA;AACG,SAASC,kBAAkBA,CAAA,EAAG;EAC7B,IAAIC,IAAI,GAAG,iBAAiB,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EACtD,IAAIC,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EAChD,IAAIC,SAAS;;EAEb;AACN;AACA;AACA;AACA;AACA;EACM,SAASC,UAAUA,CAACC,GAAG,EAAE;IACvB,IAAIC,IAAI,GAAGD,GAAG;IAEd,IAAIT,IAAI,EAAE;MACV;MACEI,cAAc,CAACO,YAAY,CAAC,MAAM,EAAED,IAAI,CAAC;MACzCA,IAAI,GAAGN,cAAc,CAACM,IAAI;IAC5B;IAEAN,cAAc,CAACO,YAAY,CAAC,MAAM,EAAED,IAAI,CAAC;;IAEzC;IACA,OAAO;MACLA,IAAI,EAAEN,cAAc,CAACM,IAAI;MACzBE,QAAQ,EAAER,cAAc,CAACQ,QAAQ,GAAGR,cAAc,CAACQ,QAAQ,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE;MAClFC,IAAI,EAAEV,cAAc,CAACU,IAAI;MACzBC,MAAM,EAAEX,cAAc,CAACW,MAAM,GAAGX,cAAc,CAACW,MAAM,CAACF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE;MAC7EG,IAAI,EAAEZ,cAAc,CAACY,IAAI,GAAGZ,cAAc,CAACY,IAAI,CAACH,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE;MACtEI,QAAQ,EAAEb,cAAc,CAACa,QAAQ;MACjCC,IAAI,EAAEd,cAAc,CAACc,IAAI;MACzBC,QAAQ,EAAGf,cAAc,CAACe,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAClDhB,cAAc,CAACe,QAAQ,GACvB,GAAG,GAAGf,cAAc,CAACe;IACzB,CAAC;EACH;EAEAZ,SAAS,GAAGC,UAAU,CAACa,MAAM,CAACC,QAAQ,CAACZ,IAAI,CAAC;;EAE5C;AACN;AACA;AACA;AACA;AACA;EACM,OAAO,SAASa,eAAeA,CAACC,UAAU,EAAE;IAC1C,IAAIC,MAAM,GAAI/B,KAAK,CAACgC,QAAQ,CAACF,UAAU,CAAC,GAAIhB,UAAU,CAACgB,UAAU,CAAC,GAAGA,UAAU;IAC/E,OAAQC,MAAM,CAACb,QAAQ,KAAKL,SAAS,CAACK,QAAQ,IAC1Ca,MAAM,CAACX,IAAI,KAAKP,SAAS,CAACO,IAAI;EACpC,CAAC;AACH,CAAC,CAAE,CAAC;AAEN;AACG,SAASa,qBAAqBA,CAAA,EAAG;EAChC,OAAO,SAASJ,eAAeA,CAAA,EAAG;IAChC,OAAO,IAAI;EACb,CAAC;AACH,CAAC,CAAE,CACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}