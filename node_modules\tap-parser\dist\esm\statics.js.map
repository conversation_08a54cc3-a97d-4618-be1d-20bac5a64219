{"version": 3, "file": "statics.js", "sourceRoot": "", "sources": ["../../src/statics.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,IAAI,MAAM,iBAAiB,CAAA;AAElC,OAAO,EAAE,MAAM,EAAiB,MAAM,YAAY,CAAA;AAClD,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAA;AACjC,OAAO,IAAI,MAAM,UAAU,CAAA;AAC3B,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAA;AAE1D,0BAA0B;AAC1B,MAAM,KAAK,GAAG,GAAoC,EAAE;IAClD,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAA;IAC7B,EAAE,CAAC,OAAO,GAAG,CAAC,CAAA;IACd,OAAO,EAAE,CAAA;AACX,CAAC,CAAA;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,CACnB,GAAW,EACX,OAAsB,EACZ,EAAE;IACZ,MAAM,EAAE,IAAI,GAAG,KAAK,EAAE,GAAG,OAAO,CAAA;IAChC,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;IACjE,IAAI,IAAI;QAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;IAC5D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAA;IAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACnC,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,EAAE,GAAG,KAAK,EAAE,CAAA;QAClB,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;YACxB,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAA;YACvB,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAA;YACb,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;QACF,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,GAAG,CAAC,OAAO;gBACd,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YAC1D,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAA;QAChC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACf,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CACvB,GAAa,EACb,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,EACnC,EAAE;IACV,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;IAC9B,OAAO,CACL,GAAG;QACH,GAAG;aACA,GAAG,CAAC,IAAI,CAAC,EAAE;YACV,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChB,KAAK,OAAO;oBACV,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;oBAC9B,OAAO,CACL,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE;wBAC1B,IAAI;wBACJ,MAAM,EAAE,EAAE;wBACV,EAAE;qBACH,CAAC;wBACF,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE;4BACtB,IAAI;4BACJ,MAAM,EAAE,MAAM;4BACd,EAAE;yBACH,CAAC,CACH,CAAA;gBAEH,KAAK,SAAS;oBACZ,OAAO,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;gBAExC,KAAK,MAAM;oBACT,IAAI,IAAI,EAAE,CAAC;wBACT,IAAI,MAAM,KAAK,EAAE;4BAAE,OAAO,EAAE,CAAA;wBAC5B,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAA;wBACjB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,OAAO,GAAG,CAAC,CAAA;oBAC9B,CAAC;oBACD,OAAO,CACL,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK;wBACb,IAAI;wBACJ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG;wBACX,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACrD,IAAI,CACL,CAAA;gBAEH,KAAK,QAAQ;oBACX,OAAO,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;gBAE3D,KAAK,SAAS;oBACZ,OAAO,CACL,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CACzD,CAAA;gBAEH,KAAK,QAAQ;oBACX,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;oBACnB,IAAI,IAAI,EAAE,CAAC;wBACT,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAA;wBACb,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAA;oBACzB,CAAC;oBACD,OAAO,CACL,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;wBACtB,IAAI;wBACJ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC5B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BACT,KAAK;gCACL,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BACjD,CAAC,CAAC,EAAE,CAAC;wBACL,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BACT,SAAS;gCACT,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BAChD,CAAC,CAAC,EAAE,CAAC;wBACL,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BACT,SAAS;gCACT,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BAChD,CAAC,CAAC,EAAE,CAAC;wBACL,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC9C,IAAI;wBACJ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BACT,WAAW;gCACX,IAAI;qCACD,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;qCACnB,KAAK,CAAC,IAAI,CAAC;qCACX,IAAI,CAAC,MAAM,CAAC;qCACZ,IAAI,EAAE;gCACT,WAAW;4BACb,CAAC,CAAC,EAAE,CAAC,CACN,CAAA;gBAEH,KAAK,OAAO,CAAC;gBACb,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;YAClB,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC;aACR,KAAK,CAAC,IAAI,CAAC;aACX,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;aAChB,IAAI,EAAE;QACT,IAAI,CACL,CAAA;AACH,CAAC,CAAA", "sourcesContent": ["/**\n * Static `parse()` and `stringify()` methods\n */\nimport etoa from 'events-to-array'\ntype EventLog = ReturnType<typeof etoa>\nimport { Parser, ParserOptions } from './index.js'\nimport { esc } from './escape.js'\nimport yaml from 'tap-yaml'\nimport { SPACE_OPEN_BRACE_EOL } from './brace-patterns.js'\n\n// used in flattening mode\nconst getId = (): { (): number; current: number } => {\n  const id = () => id.current++\n  id.current = 1\n  return id\n}\n\n/**\n * Parse a TAP text stream into a log of all the events encountered\n */\nexport const parse = (\n  str: string,\n  options: ParserOptions,\n): EventLog => {\n  const { flat = false } = options\n  const ignore = ['line', 'pass', 'fail', 'todo', 'skip', 'result']\n  if (flat) ignore.push('assert', 'child', 'plan', 'complete')\n  const parser = new Parser(options)\n  const events = etoa(parser, ignore)\n  if (flat) {\n    const id = getId()\n    parser.on('result', res => {\n      res.name = res.fullname\n      res.id = id()\n      events.push(['assert', res])\n    })\n    parser.on('complete', res => {\n      if (!res.bailout)\n        events.push(['plan', { end: id.current - 1, start: 1 }])\n      events.push(['complete', res])\n    })\n  }\n\n  parser.end(str)\n  return events\n}\n\n/**\n * Turn an EventLog from {@link tap-parser!statics.parse} into a TAP string\n */\nexport const stringify = (\n  msg: EventLog,\n  { flat = false, indent = '', id = getId() },\n): string => {\n  const ind = flat ? '' : indent\n  return (\n    ind +\n    msg\n      .map(item => {\n        switch (item[0]) {\n          case 'child':\n            const comment = item[1][0]\n            const child = item[1].slice(1)\n            return (\n              Parser.stringify([comment], {\n                flat,\n                indent: '',\n                id,\n              }) +\n              Parser.stringify(child, {\n                flat,\n                indent: '    ',\n                id,\n              })\n            )\n\n          case 'version':\n            return 'TAP version ' + item[1] + '\\n'\n\n          case 'plan':\n            if (flat) {\n              if (indent !== '') return ''\n              item[1].start = 1\n              item[1].end = id.current - 1\n            }\n            return (\n              item[1].start +\n              '..' +\n              item[1].end +\n              (item[1].comment ? ' # ' + esc(item[1].comment) : '') +\n              '\\n'\n            )\n\n          case 'pragma':\n            return 'pragma ' + (item[2] ? '+' : '-') + item[1] + '\\n'\n\n          case 'bailout':\n            return (\n              'Bail out!' + (item[1] ? ' ' + esc(item[1]) : '') + '\\n'\n            )\n\n          case 'assert':\n            const res = item[1]\n            if (flat) {\n              res.id = id()\n              res.name = res.fullname\n            }\n            return (\n              (res.ok ? '' : 'not ') +\n              'ok' +\n              (res.id ? ' ' + res.id : '') +\n              (res.name ?\n                ' - ' +\n                esc(res.name).replace(SPACE_OPEN_BRACE_EOL, '')\n              : '') +\n              (res.skip ?\n                ' # SKIP' +\n                (res.skip === true ? '' : ' ' + esc(res.skip))\n              : '') +\n              (res.todo ?\n                ' # TODO' +\n                (res.todo === true ? '' : ' ' + esc(res.todo))\n              : '') +\n              (res.time ? ' # time=' + res.time + 'ms' : '') +\n              '\\n' +\n              (res.diag ?\n                '  ---\\n  ' +\n                yaml\n                  .stringify(res.diag)\n                  .split('\\n')\n                  .join('\\n  ')\n                  .trim() +\n                '\\n  ...\\n'\n              : '')\n            )\n\n          case 'extra':\n          case 'comment':\n            return item[1]\n        }\n      })\n      .join('')\n      .split('\\n')\n      .join('\\n' + ind)\n      .trim() +\n    '\\n'\n  )\n}\n"]}