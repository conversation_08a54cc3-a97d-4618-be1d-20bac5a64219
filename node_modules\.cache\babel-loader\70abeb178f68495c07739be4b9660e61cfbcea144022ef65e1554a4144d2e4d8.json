{"ast": null, "code": "var $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({\n  global: true\n}, {\n  globalThis: global\n});", "map": {"version": 3, "names": ["$", "require", "global", "globalThis"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/modules/es.global-this.js"], "sourcesContent": ["var $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({ global: true }, {\n  globalThis: global\n});\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACtC,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;;AAE3C;AACA;AACAD,CAAC,CAAC;EAAEE,MAAM,EAAE;AAAK,CAAC,EAAE;EAClBC,UAAU,EAAED;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}