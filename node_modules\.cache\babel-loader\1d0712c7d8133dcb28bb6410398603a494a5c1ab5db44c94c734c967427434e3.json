{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"noData\", \"columns\", \"flattenColumns\", \"colWidths\", \"columCount\", \"stickyOffsets\", \"direction\", \"fixHeader\", \"stickyTopOffset\", \"stickyBottomOffset\", \"stickyClassName\", \"onScroll\", \"maxContentScroll\", \"children\"];\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport classNames from 'classnames';\nimport { fillRef } from \"rc-util/es/ref\";\nimport ColGroup from '../ColGroup';\nimport TableContext from '../context/TableContext';\nfunction useColumnWidth(colWidths, columCount) {\n  return useMemo(function () {\n    var cloneColumns = [];\n    for (var i = 0; i < columCount; i += 1) {\n      var val = colWidths[i];\n      if (val !== undefined) {\n        cloneColumns[i] = val;\n      } else {\n        return null;\n      }\n    }\n    return cloneColumns;\n  }, [colWidths.join('_'), columCount]);\n}\nvar FixedHolder = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var className = _ref.className,\n    noData = _ref.noData,\n    columns = _ref.columns,\n    flattenColumns = _ref.flattenColumns,\n    colWidths = _ref.colWidths,\n    columCount = _ref.columCount,\n    stickyOffsets = _ref.stickyOffsets,\n    direction = _ref.direction,\n    fixHeader = _ref.fixHeader,\n    stickyTopOffset = _ref.stickyTopOffset,\n    stickyBottomOffset = _ref.stickyBottomOffset,\n    stickyClassName = _ref.stickyClassName,\n    onScroll = _ref.onScroll,\n    maxContentScroll = _ref.maxContentScroll,\n    children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(TableContext),\n    prefixCls = _React$useContext.prefixCls,\n    scrollbarSize = _React$useContext.scrollbarSize,\n    isSticky = _React$useContext.isSticky;\n  var combinationScrollBarSize = isSticky && !fixHeader ? 0 : scrollbarSize; // Pass wheel to scroll event\n\n  var scrollRef = React.useRef(null);\n  var setScrollRef = React.useCallback(function (element) {\n    fillRef(ref, element);\n    fillRef(scrollRef, element);\n  }, []);\n  React.useEffect(function () {\n    var _scrollRef$current;\n    function onWheel(e) {\n      var currentTarget = e.currentTarget,\n        deltaX = e.deltaX;\n      if (deltaX) {\n        onScroll({\n          currentTarget: currentTarget,\n          scrollLeft: currentTarget.scrollLeft + deltaX\n        });\n        e.preventDefault();\n      }\n    }\n    (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.addEventListener('wheel', onWheel);\n    return function () {\n      var _scrollRef$current2;\n      (_scrollRef$current2 = scrollRef.current) === null || _scrollRef$current2 === void 0 ? void 0 : _scrollRef$current2.removeEventListener('wheel', onWheel);\n    };\n  }, []); // Check if all flattenColumns has width\n\n  var allFlattenColumnsWithWidth = React.useMemo(function () {\n    return flattenColumns.every(function (column) {\n      return column.width >= 0;\n    });\n  }, [flattenColumns]); // Add scrollbar column\n\n  var lastColumn = flattenColumns[flattenColumns.length - 1];\n  var ScrollBarColumn = {\n    fixed: lastColumn ? lastColumn.fixed : null,\n    scrollbar: true,\n    onHeaderCell: function onHeaderCell() {\n      return {\n        className: \"\".concat(prefixCls, \"-cell-scrollbar\")\n      };\n    }\n  };\n  var columnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(columns), [ScrollBarColumn]) : columns;\n  }, [combinationScrollBarSize, columns]);\n  var flattenColumnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(flattenColumns), [ScrollBarColumn]) : flattenColumns;\n  }, [combinationScrollBarSize, flattenColumns]); // Calculate the sticky offsets\n\n  var headerStickyOffsets = useMemo(function () {\n    var right = stickyOffsets.right,\n      left = stickyOffsets.left;\n    return _objectSpread(_objectSpread({}, stickyOffsets), {}, {\n      left: direction === 'rtl' ? [].concat(_toConsumableArray(left.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]) : left,\n      right: direction === 'rtl' ? right : [].concat(_toConsumableArray(right.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]),\n      isSticky: isSticky\n    });\n  }, [combinationScrollBarSize, stickyOffsets, isSticky]);\n  var mergedColumnWidth = useColumnWidth(colWidths, columCount);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: _objectSpread({\n      overflow: 'hidden'\n    }, isSticky ? {\n      top: stickyTopOffset,\n      bottom: stickyBottomOffset\n    } : {}),\n    ref: setScrollRef,\n    className: classNames(className, _defineProperty({}, stickyClassName, !!stickyClassName))\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    style: {\n      tableLayout: 'fixed',\n      visibility: noData || mergedColumnWidth ? null : 'hidden'\n    }\n  }, (!noData || !maxContentScroll || allFlattenColumnsWithWidth) && /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: mergedColumnWidth ? [].concat(_toConsumableArray(mergedColumnWidth), [combinationScrollBarSize]) : [],\n    columCount: columCount + 1,\n    columns: flattenColumnsWithScrollbar\n  }), children(_objectSpread(_objectSpread({}, props), {}, {\n    stickyOffsets: headerStickyOffsets,\n    columns: columnsWithScrollbar,\n    flattenColumns: flattenColumnsWithScrollbar\n  }))));\n});\nFixedHolder.displayName = 'FixedHolder';\nexport default FixedHolder;", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_toConsumableArray", "_objectWithoutProperties", "_excluded", "React", "useMemo", "classNames", "fillRef", "ColGroup", "TableContext", "useColumnWidth", "col<PERSON><PERSON><PERSON>", "columCount", "cloneColumns", "i", "val", "undefined", "join", "FixedHolder", "forwardRef", "_ref", "ref", "className", "noData", "columns", "flattenColumns", "stickyOffsets", "direction", "fixHeader", "stickyTopOffset", "stickyBottomOffset", "stickyClassName", "onScroll", "maxContentScroll", "children", "props", "_React$useContext", "useContext", "prefixCls", "scrollbarSize", "isSticky", "combinationScrollBarSize", "scrollRef", "useRef", "setScrollRef", "useCallback", "element", "useEffect", "_scrollRef$current", "onWheel", "e", "currentTarget", "deltaX", "scrollLeft", "preventDefault", "current", "addEventListener", "_scrollRef$current2", "removeEventListener", "allFlattenColumnsWithWidth", "every", "column", "width", "lastColumn", "length", "ScrollBarColumn", "fixed", "scrollbar", "onHeaderCell", "concat", "columnsWithScrollbar", "flattenColumnsWithScrollbar", "headerStickyOffsets", "right", "left", "map", "mergedColumnWidth", "createElement", "style", "overflow", "top", "bottom", "tableLayout", "visibility", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/FixedHolder/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"noData\", \"columns\", \"flattenColumns\", \"colWidths\", \"columCount\", \"stickyOffsets\", \"direction\", \"fixHeader\", \"stickyTopOffset\", \"stickyBottomOffset\", \"stickyClassName\", \"onScroll\", \"maxContentScroll\", \"children\"];\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport classNames from 'classnames';\nimport { fillRef } from \"rc-util/es/ref\";\nimport ColGroup from '../ColGroup';\nimport TableContext from '../context/TableContext';\n\nfunction useColumnWidth(colWidths, columCount) {\n  return useMemo(function () {\n    var cloneColumns = [];\n\n    for (var i = 0; i < columCount; i += 1) {\n      var val = colWidths[i];\n\n      if (val !== undefined) {\n        cloneColumns[i] = val;\n      } else {\n        return null;\n      }\n    }\n\n    return cloneColumns;\n  }, [colWidths.join('_'), columCount]);\n}\n\nvar FixedHolder = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var className = _ref.className,\n      noData = _ref.noData,\n      columns = _ref.columns,\n      flattenColumns = _ref.flattenColumns,\n      colWidths = _ref.colWidths,\n      columCount = _ref.columCount,\n      stickyOffsets = _ref.stickyOffsets,\n      direction = _ref.direction,\n      fixHeader = _ref.fixHeader,\n      stickyTopOffset = _ref.stickyTopOffset,\n      stickyBottomOffset = _ref.stickyBottomOffset,\n      stickyClassName = _ref.stickyClassName,\n      onScroll = _ref.onScroll,\n      maxContentScroll = _ref.maxContentScroll,\n      children = _ref.children,\n      props = _objectWithoutProperties(_ref, _excluded);\n\n  var _React$useContext = React.useContext(TableContext),\n      prefixCls = _React$useContext.prefixCls,\n      scrollbarSize = _React$useContext.scrollbarSize,\n      isSticky = _React$useContext.isSticky;\n\n  var combinationScrollBarSize = isSticky && !fixHeader ? 0 : scrollbarSize; // Pass wheel to scroll event\n\n  var scrollRef = React.useRef(null);\n  var setScrollRef = React.useCallback(function (element) {\n    fillRef(ref, element);\n    fillRef(scrollRef, element);\n  }, []);\n  React.useEffect(function () {\n    var _scrollRef$current;\n\n    function onWheel(e) {\n      var currentTarget = e.currentTarget,\n          deltaX = e.deltaX;\n\n      if (deltaX) {\n        onScroll({\n          currentTarget: currentTarget,\n          scrollLeft: currentTarget.scrollLeft + deltaX\n        });\n        e.preventDefault();\n      }\n    }\n\n    (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.addEventListener('wheel', onWheel);\n    return function () {\n      var _scrollRef$current2;\n\n      (_scrollRef$current2 = scrollRef.current) === null || _scrollRef$current2 === void 0 ? void 0 : _scrollRef$current2.removeEventListener('wheel', onWheel);\n    };\n  }, []); // Check if all flattenColumns has width\n\n  var allFlattenColumnsWithWidth = React.useMemo(function () {\n    return flattenColumns.every(function (column) {\n      return column.width >= 0;\n    });\n  }, [flattenColumns]); // Add scrollbar column\n\n  var lastColumn = flattenColumns[flattenColumns.length - 1];\n  var ScrollBarColumn = {\n    fixed: lastColumn ? lastColumn.fixed : null,\n    scrollbar: true,\n    onHeaderCell: function onHeaderCell() {\n      return {\n        className: \"\".concat(prefixCls, \"-cell-scrollbar\")\n      };\n    }\n  };\n  var columnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(columns), [ScrollBarColumn]) : columns;\n  }, [combinationScrollBarSize, columns]);\n  var flattenColumnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(flattenColumns), [ScrollBarColumn]) : flattenColumns;\n  }, [combinationScrollBarSize, flattenColumns]); // Calculate the sticky offsets\n\n  var headerStickyOffsets = useMemo(function () {\n    var right = stickyOffsets.right,\n        left = stickyOffsets.left;\n    return _objectSpread(_objectSpread({}, stickyOffsets), {}, {\n      left: direction === 'rtl' ? [].concat(_toConsumableArray(left.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]) : left,\n      right: direction === 'rtl' ? right : [].concat(_toConsumableArray(right.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]),\n      isSticky: isSticky\n    });\n  }, [combinationScrollBarSize, stickyOffsets, isSticky]);\n  var mergedColumnWidth = useColumnWidth(colWidths, columCount);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: _objectSpread({\n      overflow: 'hidden'\n    }, isSticky ? {\n      top: stickyTopOffset,\n      bottom: stickyBottomOffset\n    } : {}),\n    ref: setScrollRef,\n    className: classNames(className, _defineProperty({}, stickyClassName, !!stickyClassName))\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    style: {\n      tableLayout: 'fixed',\n      visibility: noData || mergedColumnWidth ? null : 'hidden'\n    }\n  }, (!noData || !maxContentScroll || allFlattenColumnsWithWidth) && /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: mergedColumnWidth ? [].concat(_toConsumableArray(mergedColumnWidth), [combinationScrollBarSize]) : [],\n    columCount: columCount + 1,\n    columns: flattenColumnsWithScrollbar\n  }), children(_objectSpread(_objectSpread({}, props), {}, {\n    stickyOffsets: headerStickyOffsets,\n    columns: columnsWithScrollbar,\n    flattenColumns: flattenColumnsWithScrollbar\n  }))));\n});\nFixedHolder.displayName = 'FixedHolder';\nexport default FixedHolder;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,UAAU,EAAE,kBAAkB,EAAE,UAAU,CAAC;AAClP,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,YAAY,MAAM,yBAAyB;AAElD,SAASC,cAAcA,CAACC,SAAS,EAAEC,UAAU,EAAE;EAC7C,OAAOP,OAAO,CAAC,YAAY;IACzB,IAAIQ,YAAY,GAAG,EAAE;IAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,IAAI,CAAC,EAAE;MACtC,IAAIC,GAAG,GAAGJ,SAAS,CAACG,CAAC,CAAC;MAEtB,IAAIC,GAAG,KAAKC,SAAS,EAAE;QACrBH,YAAY,CAACC,CAAC,CAAC,GAAGC,GAAG;MACvB,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IAEA,OAAOF,YAAY;EACrB,CAAC,EAAE,CAACF,SAAS,CAACM,IAAI,CAAC,GAAG,CAAC,EAAEL,UAAU,CAAC,CAAC;AACvC;AAEA,IAAIM,WAAW,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EACnE,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;IACtBC,cAAc,GAAGL,IAAI,CAACK,cAAc;IACpCd,SAAS,GAAGS,IAAI,CAACT,SAAS;IAC1BC,UAAU,GAAGQ,IAAI,CAACR,UAAU;IAC5Bc,aAAa,GAAGN,IAAI,CAACM,aAAa;IAClCC,SAAS,GAAGP,IAAI,CAACO,SAAS;IAC1BC,SAAS,GAAGR,IAAI,CAACQ,SAAS;IAC1BC,eAAe,GAAGT,IAAI,CAACS,eAAe;IACtCC,kBAAkB,GAAGV,IAAI,CAACU,kBAAkB;IAC5CC,eAAe,GAAGX,IAAI,CAACW,eAAe;IACtCC,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;IACxBC,gBAAgB,GAAGb,IAAI,CAACa,gBAAgB;IACxCC,QAAQ,GAAGd,IAAI,CAACc,QAAQ;IACxBC,KAAK,GAAGjC,wBAAwB,CAACkB,IAAI,EAAEjB,SAAS,CAAC;EAErD,IAAIiC,iBAAiB,GAAGhC,KAAK,CAACiC,UAAU,CAAC5B,YAAY,CAAC;IAClD6B,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,aAAa,GAAGH,iBAAiB,CAACG,aAAa;IAC/CC,QAAQ,GAAGJ,iBAAiB,CAACI,QAAQ;EAEzC,IAAIC,wBAAwB,GAAGD,QAAQ,IAAI,CAACZ,SAAS,GAAG,CAAC,GAAGW,aAAa,CAAC,CAAC;;EAE3E,IAAIG,SAAS,GAAGtC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EAClC,IAAIC,YAAY,GAAGxC,KAAK,CAACyC,WAAW,CAAC,UAAUC,OAAO,EAAE;IACtDvC,OAAO,CAACc,GAAG,EAAEyB,OAAO,CAAC;IACrBvC,OAAO,CAACmC,SAAS,EAAEI,OAAO,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EACN1C,KAAK,CAAC2C,SAAS,CAAC,YAAY;IAC1B,IAAIC,kBAAkB;IAEtB,SAASC,OAAOA,CAACC,CAAC,EAAE;MAClB,IAAIC,aAAa,GAAGD,CAAC,CAACC,aAAa;QAC/BC,MAAM,GAAGF,CAAC,CAACE,MAAM;MAErB,IAAIA,MAAM,EAAE;QACVpB,QAAQ,CAAC;UACPmB,aAAa,EAAEA,aAAa;UAC5BE,UAAU,EAAEF,aAAa,CAACE,UAAU,GAAGD;QACzC,CAAC,CAAC;QACFF,CAAC,CAACI,cAAc,CAAC,CAAC;MACpB;IACF;IAEA,CAACN,kBAAkB,GAAGN,SAAS,CAACa,OAAO,MAAM,IAAI,IAAIP,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACQ,gBAAgB,CAAC,OAAO,EAAEP,OAAO,CAAC;IACnJ,OAAO,YAAY;MACjB,IAAIQ,mBAAmB;MAEvB,CAACA,mBAAmB,GAAGf,SAAS,CAACa,OAAO,MAAM,IAAI,IAAIE,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,mBAAmB,CAAC,OAAO,EAAET,OAAO,CAAC;IAC3J,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIU,0BAA0B,GAAGvD,KAAK,CAACC,OAAO,CAAC,YAAY;IACzD,OAAOoB,cAAc,CAACmC,KAAK,CAAC,UAAUC,MAAM,EAAE;MAC5C,OAAOA,MAAM,CAACC,KAAK,IAAI,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEtB,IAAIsC,UAAU,GAAGtC,cAAc,CAACA,cAAc,CAACuC,MAAM,GAAG,CAAC,CAAC;EAC1D,IAAIC,eAAe,GAAG;IACpBC,KAAK,EAAEH,UAAU,GAAGA,UAAU,CAACG,KAAK,GAAG,IAAI;IAC3CC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;MACpC,OAAO;QACL9C,SAAS,EAAE,EAAE,CAAC+C,MAAM,CAAC/B,SAAS,EAAE,iBAAiB;MACnD,CAAC;IACH;EACF,CAAC;EACD,IAAIgC,oBAAoB,GAAGjE,OAAO,CAAC,YAAY;IAC7C,OAAOoC,wBAAwB,GAAG,EAAE,CAAC4B,MAAM,CAACpE,kBAAkB,CAACuB,OAAO,CAAC,EAAE,CAACyC,eAAe,CAAC,CAAC,GAAGzC,OAAO;EACvG,CAAC,EAAE,CAACiB,wBAAwB,EAAEjB,OAAO,CAAC,CAAC;EACvC,IAAI+C,2BAA2B,GAAGlE,OAAO,CAAC,YAAY;IACpD,OAAOoC,wBAAwB,GAAG,EAAE,CAAC4B,MAAM,CAACpE,kBAAkB,CAACwB,cAAc,CAAC,EAAE,CAACwC,eAAe,CAAC,CAAC,GAAGxC,cAAc;EACrH,CAAC,EAAE,CAACgB,wBAAwB,EAAEhB,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAI+C,mBAAmB,GAAGnE,OAAO,CAAC,YAAY;IAC5C,IAAIoE,KAAK,GAAG/C,aAAa,CAAC+C,KAAK;MAC3BC,IAAI,GAAGhD,aAAa,CAACgD,IAAI;IAC7B,OAAO1E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzDgD,IAAI,EAAE/C,SAAS,KAAK,KAAK,GAAG,EAAE,CAAC0C,MAAM,CAACpE,kBAAkB,CAACyE,IAAI,CAACC,GAAG,CAAC,UAAUb,KAAK,EAAE;QACjF,OAAOA,KAAK,GAAGrB,wBAAwB;MACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGiC,IAAI;MAChBD,KAAK,EAAE9C,SAAS,KAAK,KAAK,GAAG8C,KAAK,GAAG,EAAE,CAACJ,MAAM,CAACpE,kBAAkB,CAACwE,KAAK,CAACE,GAAG,CAAC,UAAUb,KAAK,EAAE;QAC3F,OAAOA,KAAK,GAAGrB,wBAAwB;MACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACTD,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACC,wBAAwB,EAAEf,aAAa,EAAEc,QAAQ,CAAC,CAAC;EACvD,IAAIoC,iBAAiB,GAAGlE,cAAc,CAACC,SAAS,EAAEC,UAAU,CAAC;EAC7D,OAAO,aAAaR,KAAK,CAACyE,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAE9E,aAAa,CAAC;MACnB+E,QAAQ,EAAE;IACZ,CAAC,EAAEvC,QAAQ,GAAG;MACZwC,GAAG,EAAEnD,eAAe;MACpBoD,MAAM,EAAEnD;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;IACPT,GAAG,EAAEuB,YAAY;IACjBtB,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEvB,eAAe,CAAC,CAAC,CAAC,EAAEgC,eAAe,EAAE,CAAC,CAACA,eAAe,CAAC;EAC1F,CAAC,EAAE,aAAa3B,KAAK,CAACyE,aAAa,CAAC,OAAO,EAAE;IAC3CC,KAAK,EAAE;MACLI,WAAW,EAAE,OAAO;MACpBC,UAAU,EAAE5D,MAAM,IAAIqD,iBAAiB,GAAG,IAAI,GAAG;IACnD;EACF,CAAC,EAAE,CAAC,CAACrD,MAAM,IAAI,CAACU,gBAAgB,IAAI0B,0BAA0B,KAAK,aAAavD,KAAK,CAACyE,aAAa,CAACrE,QAAQ,EAAE;IAC5GG,SAAS,EAAEiE,iBAAiB,GAAG,EAAE,CAACP,MAAM,CAACpE,kBAAkB,CAAC2E,iBAAiB,CAAC,EAAE,CAACnC,wBAAwB,CAAC,CAAC,GAAG,EAAE;IAChH7B,UAAU,EAAEA,UAAU,GAAG,CAAC;IAC1BY,OAAO,EAAE+C;EACX,CAAC,CAAC,EAAErC,QAAQ,CAAClC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACvDT,aAAa,EAAE8C,mBAAmB;IAClChD,OAAO,EAAE8C,oBAAoB;IAC7B7C,cAAc,EAAE8C;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACFrD,WAAW,CAACkE,WAAW,GAAG,aAAa;AACvC,eAAelE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}