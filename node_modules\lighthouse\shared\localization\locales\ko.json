{"core/audits/accessibility/accesskeys.js | description": {"message": "액세스 키를 사용하면 사용자가 페이지의 특정 부분에 신속하게 포커스를 맞출 수 있습니다. 정상적으로 탐색하려면 모든 액세스 키가 고유해야 합니다. [액세스 키에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` 값이 고유하지 않음"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` 값이 고유합니다"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "각 ARIA `role`은 `aria-*` 속성으로 구성된 특정 하위 세트를 지원합니다. 이 두 가지가 일치하지 않으면 `aria-*` 속성이 유효하지 않게 됩니다. [ARIA 속성을 역할에 일치시키는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` 속성이 역할과 일치하지 않음"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` 속성이 역할과 일치함"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "요소에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 요소를 사용할 수 없게 됩니다. [명령어 요소의 접근성을 높이는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link` 및 `menuitem` 요소에 접근성을 위한 이름이 없음"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link` 및 `menuitem` 요소에 접근성을 위한 이름이 있음"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "스크린 리더와 같은 보조 기술은 `aria-hidden=\"true\"`가 문서 `<body>`에 설정된 경우 일관성 없게 작동합니다. [`aria-hidden`이 문서 본문에 미치는 영향 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]`이(가) 문서 `<body>`에 있음"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]`이(가) 문서 `<body>`에 없음"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "`[aria-hidden=\"true\"]` 요소 내 포커스 가능한 하위 요소는 스크린 리더와 같은 보조 기술 사용자가 상호작용 가능한 요소를 사용하지 못하게 합니다. [`aria-hidden`이 포커스 가능 요소에 어떤 영향을 미치는지 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` 요소에 포커스할 수 있는 하위 요소가 있음"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` 요소에 포커스할 수 있는 하위 요소가 없음"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "입력란에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 입력란을 사용할 수 없게 됩니다. [입력란 라벨에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA 입력란에 접근 가능한 이름이 없음"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA 입력란에 접근 가능한 이름이 있음"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "측정기 요소에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 요소를 사용할 수 없게 됩니다. [`meter` 요소의 이름을 지정하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` 요소에 접근성을 위한 이름이 없음"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` 요소에 접근성을 위한 이름이 있음"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "`progressbar` 요소에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 요소를 사용할 수 없게 됩니다. [`progressbar` 요소에 라벨을 지정하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` 요소에 접근성을 위한 이름이 없음"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` 요소에 접근성을 위한 이름이 있음"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "일부 ARIA 역할에는 스크린 리더에 관한 요소의 상태를 설명하는 필수 속성이 있습니다. [역할 및 필수 속성에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`에 필요한 `[aria-*]` 속성이 일부 포함되지 않음"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`에 필요한 모든 `[aria-*]` 속성이 있음"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "일부 ARIA 상위 역할은 의도한 접근성 기능을 실행하려면 특정 하위 역할을 포함하고 있어야 합니다. [역할 및 필수 하위 요소에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "하위 요소에 특정 `[role]`을(를) 포함해야 하는 ARIA `[role]` 지원 요소에 일부 또는 전체 하위 요소가 누락되었습니다."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "하위 요소에 특정 `[role]`을(를) 포함해야 하는 ARIA `[role]` 지원 요소에 필요한 모든 하위 요소가 있습니다."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "일부 ARIA 하위 역할은 의도한 접근성 기능을 올바르게 실행하려면 특정 상위 역할에 포함되어 있어야 합니다. [ARIA 역할 및 필수 상위 요소에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`이(가) 필수 상위 요소에 포함되지 않음"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`이(가) 필수 상위 요소에 포함됨"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA 역할에서 의도한 접근성 기능을 실행하려면 유효한 값을 포함하고 있어야 합니다. [유효한 ARIA 역할에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` 값이 유효하지 않음"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` 값이 유효함"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "전환 버튼 입력란에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 입력란을 사용할 수 없게 됩니다. [전환 버튼 입력란에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA 토글 입력란에 접근 가능한 이름이 없음"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA 토글 입력란에 접근 가능한 이름이 있음"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "팁 요소에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 요소를 사용할 수 없게 됩니다. [`tooltip` 요소의 이름을 지정하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` 요소에 접근성을 위한 이름이 없음"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` 요소에 접근성을 위한 이름이 있음"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "`treeitem` 요소에 액세스 가능한 이름이 없는 경우 스크린 리더가 일반적인 이름으로 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 요소를 사용할 수 없게 됩니다. [`treeitem` 요소 라벨 지정에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` 요소에 접근성을 위한 이름이 없음"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` 요소에 접근성을 위한 이름이 있음"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "스크린 리더와 같은 보조 기술은 잘못된 값이 있는 ARIA 속성을 해석하지 못합니다. [ARIA 속성의 유효한 값에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` 속성에 유효한 값이 없음"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` 속성에 유효한 값이 있음"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "스크린 리더와 같은 보조 기술은 잘못된 이름이 있는 ARIA 속성을 해석하지 못합니다. [유효한 ARIA 속성에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` 속성이 유효하지 않거나 맞춤법 오류가 있음"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` 속성이 유효하며 맞춤법 오류가 없음"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "통과하지 못한 요소"}, "core/audits/accessibility/button-name.js | description": {"message": "버튼에 액세스 가능한 이름이 없는 경우 스크린 리더가 '버튼'이라고만 읽기 때문에 스크린 리더에 의존하는 사용자는 해당 버튼을 사용할 수 없게 됩니다. [버튼 접근성을 높이는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "버튼에 접근 가능한 이름이 없습니다"}, "core/audits/accessibility/button-name.js | title": {"message": "버튼에 접근 가능한 이름이 있습니다"}, "core/audits/accessibility/bypass.js | description": {"message": "반복적인 콘텐츠를 건너뛸 방법을 추가하면 키보드 사용자가 페이지를 더 효율적으로 탐색할 수 있습니다. [우회 차단에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "페이지에 제목, 링크 건너뛰기, 랜드마크 영역이 포함되어 있지 않습니다"}, "core/audits/accessibility/bypass.js | title": {"message": "페이지에 제목, 링크 건너뛰기, 랜드마크 영역이 포함되어 있습니다"}, "core/audits/accessibility/color-contrast.js | description": {"message": "많은 사용자가 저대비 텍스트를 읽는 데 어려움을 겪거나 전혀 읽지 못합니다. [충분한 색상 대비를 제공하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "백그라운드 및 포그라운드 색상의 대비율이 충분하지 않습니다"}, "core/audits/accessibility/color-contrast.js | title": {"message": "백그라운드 및 포그라운드 색상의 대비율이 충분합니다"}, "core/audits/accessibility/definition-list.js | description": {"message": "정의 목록이 올바르게 표시되지 않으면 스크린 리더에서 혼란스럽거나 정확하지 않은 내용을 말할 수 있습니다. [정의 목록을 올바르게 구성하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`에 순서가 올바른 `<dt>` 및 `<dd>` 그룹, `<script>`, `<template>` 또는 `<div>` 요소만 포함되어 있지 않음"}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`에 순서가 올바른 `<dt>` 및 `<dd>` 그룹, `<script>`, `<template>` 또는 `<div>` 요소만 포함되어 있음"}, "core/audits/accessibility/dlitem.js | description": {"message": "스크린 리더에서 정의 목록 항목(`<dt>` 및 `<dd>`)을 올바르게 읽으려면 정의 목록 항목이 상위 `<dl>` 요소에 래핑되어 있어야 합니다. [정의 목록을 올바르게 구성하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "정의 목록 항목이 `<dl>` 요소에서 래핑되어 있지 않음"}, "core/audits/accessibility/dlitem.js | title": {"message": "정의 목록 항목이 `<dl>` 요소에서 래핑되어 있음"}, "core/audits/accessibility/document-title.js | description": {"message": "제목을 사용하면 스크린 리더 사용자에게는 페이지의 개요를 제공할 수 있습니다. 검색엔진 사용자는 제목으로 페이지가 자신의 검색어와 관련되어 있는지 여부를 판단합니다. [문서 제목에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "문서에 `<title>` 요소가 없음"}, "core/audits/accessibility/document-title.js | title": {"message": "문서에 `<title>` 요소가 있음"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "포커스 가능한 모든 요소가 보조 기술에 올바르게 표시되려면 고유한 `id`가 있어야 합니다. [중복된 `id`를 해결하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "포커스할 수 있는 활성 요소에 중복되는 `[id]` 속성이 있음"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "포커스할 수 있는 활성 요소에 중복되는 `[id]` 속성이 없음"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "중복되는 ARIA ID 값이 없어야 보조 기술이 다른 인스턴스를 놓치지 않습니다. [중복 ARIA ID를 수정하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "중복되는 ARIA ID가 있음"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "중복되는 ARIA ID가 없음"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "라벨이 여러 개인 양식 입력란은 첫 번째, 마지막 또는 모든 라벨을 사용하는 스크린 리더와 같은 보조 기술에서 잘못 읽을 수 있습니다. [양식 라벨 사용 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "양식 입력란에 라벨이 여러 개 있음"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "양식 입력란에 라벨이 여러 개가 아님"}, "core/audits/accessibility/frame-title.js | description": {"message": "스크린 리더 사용자는 프레임 콘텐츠를 설명해 주는 프레임 제목을 사용합니다. [프레임 제목에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` 또는 `<iframe>` 요소에 제목이 없음"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` 또는 `<iframe>` 요소에 제목이 있음"}, "core/audits/accessibility/heading-order.js | description": {"message": "레벨을 건너뛰지 않고 제대로 정렬된 제목은 페이지의 시맨틱 구조를 포함하여, 보조 기술을 사용할 때 더 쉽게 이동하고 이해하는 데 도움이 됩니다. [제목 순서 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "제목 요소가 내림차순으로 표시되지 않음"}, "core/audits/accessibility/heading-order.js | title": {"message": "제목 요소가 내림차순으로 표시됨"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "페이지에서 `lang` 속성을 지정하지 않는 경우 스크린 리더는 사용자가 스크린 리더를 설정할 때 선택한 기본 언어로 페이지가 작성되어 있다고 가정합니다. 페이지가 기본 언어로 작성되어 있지 않으면 스크린 리더에서 페이지에 있는 텍스트를 제대로 읽어줄 수 없습니다. [`lang` 속성에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` 요소에 `[lang]` 속성이 없음"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` 요소에 `[lang]` 속성이 있음"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "유효한 [BCP 47 언어](https://www.w3.org/International/questions/qa-choosing-language-tags#question)를 지정하면 스크린 리더에서 텍스트를 올바르게 읽는 데 도움이 됩니다. [`lang` 속성을 사용하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` 요소의 `[lang]` 속성에 유효한 값이 없음"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` 요소에 `[lang]` 속성의 유효한 값이 있음"}, "core/audits/accessibility/image-alt.js | description": {"message": "정보 요소는 짧아야 하며 설명을 제공하는 대체 텍스트를 목표로 해야 합니다. 장식 요소는 Alt 속성이 비어 있는 경우 무시될 수 있습니다. [`alt` 속성에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "이미지 요소에 `[alt]` 속성 없음"}, "core/audits/accessibility/image-alt.js | title": {"message": "이미지 요소에 `[alt]` 속성이 있음"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "이미지가 `<input>` 버튼으로 사용되는 경우 대체 텍스트를 제공하면 스크린 리더 사용자가 버튼의 목적을 쉽게 이해하는 데 도움이 됩니다. [입력 이미지 대체 텍스트에 관해 알아보기](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` 요소에 `[alt]` 텍스트가 없음"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` 요소에 `[alt]` 텍스트가 있음"}, "core/audits/accessibility/label.js | description": {"message": "라벨을 사용하면 스크린 리더와 같은 보조 기술에서 양식 컨트롤을 올바르게 읽을 수 있습니다. [양식 요소 라벨에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "양식 요소에 관련 라벨이 포함되어 있지 않습니다"}, "core/audits/accessibility/label.js | title": {"message": "양식 요소에 관련 라벨이 포함되어 있습니다"}, "core/audits/accessibility/link-name.js | description": {"message": "인식하기 쉽고, 고유하고, 초점을 맞추기 쉬운 링크 텍스트(및 이미지가 링크로 사용되는 경우 이미지의 대체 텍스트)를 사용하면 스크린 리더 사용자의 탐색 환경을 개선할 수 있습니다. [링크 접근성을 높이는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "링크에 인식 가능한 이름이 포함되어 있지 않습니다"}, "core/audits/accessibility/link-name.js | title": {"message": "링크에 인식 가능한 이름이 포함되어 있습니다"}, "core/audits/accessibility/list.js | description": {"message": "스크린 리더에는 목록을 읽는 특정 방식이 있습니다. 목록 구조를 적절히 작성하면 스크린 리더 출력에 도움이 됩니다. [적절한 목록 구조에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "목록에 `<li>` 요소와 스크립트 지원 요소(`<script>` 및 `<template>`)만 포함되지 않음"}, "core/audits/accessibility/list.js | title": {"message": "목록에 `<li>` 요소와 요소 지원 스크립트(`<script>` 및 `<template>`)만 포함됨"}, "core/audits/accessibility/listitem.js | description": {"message": "스크린 리더에서 목록 항목(`<li>`)을 올바르게 읽으려면 목록 항목이 상위 `<ul>`, `<ol>` 또는 `<menu>`에 포함되어 있어야 합니다. [적절한 목록 구조에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "목록 항목(`<li>`)이 `<ul>`, `<ol>` 또는 `<menu>` 상위 요소 내에 포함되지 않음"}, "core/audits/accessibility/listitem.js | title": {"message": "목록 항목(`<li>`)이 `<ul>`, `<ol>` 또는 `<menu>` 상위 요소 내에 포함되어 있음"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "사용자는 페이지가 자동으로 새로고침된다고 예상하지 못하기 때문에 페이지가 자동으로 새로고침되면 초점이 다시 페이지 상단에 맞춰집니다. 이로 인해 불쾌하거나 혼란스러운 상황이 발생할 수 있습니다. [새로고침 메타 태그에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "문서에서 `<meta http-equiv=\"refresh\">` 사용됨"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "문서에서 `<meta http-equiv=\"refresh\">`이(가) 사용되지 않음"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "확대/축소를 사용 중지하면 저시력으로 인해 웹페이지의 콘텐츠를 제대로 확인하기 위해 화면 확대를 사용하는 사용자에게 문제가 될 수 있습니다. [viewport meta 태그에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]`이(가) `<meta name=\"viewport\">` 요소에서 사용되거나 `[maximum-scale]` 속성이 5보다 작음"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]`은(는) `<meta name=\"viewport\">` 요소에 사용되지 않으며 `[maximum-scale]` 속성이 5보다 작지 않음"}, "core/audits/accessibility/object-alt.js | description": {"message": "스크린 리더는 텍스트가 아닌 콘텐츠를 번역할 수 없습니다. `<object>` 요소에 대체 텍스트를 추가하면 스크린 리더에서 사용자에게 텍스트의 의미를 전달하는 데 도움이 됩니다. [`object` 요소의 대체 텍스트에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` 요소에 대체 텍스트가 없음"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` 요소에 대체 텍스트가 있음"}, "core/audits/accessibility/tabindex.js | description": {"message": "0보다 큰 값은 명시적인 탐색 순서를 나타냅니다. 기술적으로는 유효하나 보조 기술에 의존하는 사용자에게 불편한 환경이 생기는 경우가 많습니다. [`tabindex` 속성에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "일부 요소의 `[tabindex]` 값이 0보다 큼"}, "core/audits/accessibility/tabindex.js | title": {"message": "`[tabindex]` 값이 0보다 큰 요소가 없음"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "스크린 리더에는 표를 좀 더 쉽게 탐색하는 기능이 있습니다. `[headers]` 속성을 사용하는 `<td>` 셀이 동일한 표에 있는 다른 셀만 참조하게 하면 스크린 리더 사용자 환경을 개선할 수 있습니다. [`headers` 속성에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`[headers]` 속성을 사용하는 `<table>` 요소의 셀은 동일한 테이블 내에 없는 요소 `id`만 참조합니다."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`[headers]` 속성을 사용하는 `<table>` 요소의 셀이 동일한 테이블 내의 테이블 셀을 참조합니다."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "스크린 리더에는 표를 좀 더 쉽게 탐색하는 기능이 있습니다. 표 헤더에서 항상 셀 세트 일부를 참조하게 하면 스크린 리더 사용자 환경이 개선될 수 있습니다. [표 헤더에 관해 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` 요소와 `[role=\"columnheader\"/\"rowheader\"]` 지원 요소가 설명하는 데이터 셀이 해당 요소에 포함되지 않음"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` 요소와 `[role=\"columnheader\"/\"rowheader\"]` 지원 요소가 설명하는 데이터 셀이 해당 요소에 포함됨"}, "core/audits/accessibility/valid-lang.js | description": {"message": "요소에 유효한 [BCP 47 언어](https://www.w3.org/International/questions/qa-choosing-language-tags#question)를 지정하면 스크린 리더에서 텍스트를 올바르게 읽는 데 도움이 됩니다. [`lang` 속성을 사용하는 방법 알아보기](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` 속성에 유효한 값이 없음"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` 속성에 유효한 값이 있음"}, "core/audits/accessibility/video-caption.js | description": {"message": "동영상에 자막이 제공되면 청각장애인이나 난청이 있는 사용자가 동영상의 정보에 더 쉽게 접근할 수 있습니다. [동영상 자막 자세히 알아보기](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` 요소에 `[kind=\"captions\"]` 지원 `<track>` 요소가 포함되지 않음"}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` 요소에 `[kind=\"captions\"]` 지원 `<track>` 요소가 포함됨"}, "core/audits/autocomplete.js | columnCurrent": {"message": "현재 값"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "추천 토큰"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete`를 사용하면 사용자가 양식을 빨리 제출할 수 있습니다. 사용자가 더 쉽게 입력할 수 있도록 `autocomplete` 속성에 유효한 값을 지정하여 기능을 사용 설정하세요. [양식의 `autocomplete` 자세히 알아보기](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` 요소에 올바른 `autocomplete` 속성이 없음"}, "core/audits/autocomplete.js | manualReview": {"message": "직접 검토가 필요함"}, "core/audits/autocomplete.js | reviewOrder": {"message": "토큰 순서 검토"}, "core/audits/autocomplete.js | title": {"message": "`<input>` 구성요소가 `autocomplete`를 올바르게 사용함"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` 토큰: {snippet}에서 '{token}' 이름이 유효하지 않습니다."}, "core/audits/autocomplete.js | warningOrder": {"message": "{snippet}에서 '{tokens}' 토큰의 순서를 검토하세요."}, "core/audits/bf-cache.js | actionableFailureType": {"message": "조치 실행 가능"}, "core/audits/bf-cache.js | description": {"message": "대부분의 탐색은 이전 페이지로 이동하거나 다시 원래 페이지로 돌아오는 방식으로 이루어집니다. 뒤로-앞으로 캐시(bfcache)로 이러한 돌아가기 탐색의 속도를 높일 수 있습니다. [bfcache에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{실패 이유 1개}other{실패 이유 #개}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "실패 이유"}, "core/audits/bf-cache.js | failureTitle": {"message": "페이지에서 뒤로-앞으로 캐시 복원이 차단됨"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "실패 유형"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "조치를 취할 수 없음"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "브라우저 지원 보류"}, "core/audits/bf-cache.js | title": {"message": "페이지에서 뒤로-앞으로 캐시 복원을 차단하지 않음"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome 확장 프로그램이 이 페이지의 로드 성능에 부정적인 영향을 미쳤습니다. 시크릿 모드나 확장 프로그램이 없는 Chrome 프로필에서 페이지를 검사해 보세요."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "스크립트 평가"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "스크립트 파싱"}, "core/audits/bootup-time.js | columnTotal": {"message": "총 CPU 시간"}, "core/audits/bootup-time.js | description": {"message": "JS 파싱, 컴파일, 실행에 소요되는 시간을 줄여 보세요. 용량이 적은 JS 페이로드를 제공하면 도움이 될 수 있습니다. [자바스크립트 실행 시간을 줄이는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "자바스크립트 실행 시간 단축"}, "core/audits/bootup-time.js | title": {"message": "자바스크립트 실행 시간"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "번들에서 큰 중복 자바스크립트 모듈을 삭제하여 네트워크 활동이 소비하는 불필요한 바이트 수를 줄입니다. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "자바스크립트 번들에서 중복 모듈 삭제"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "대용량의 GIF는 애니메이션 콘텐츠를 전달하는 데 비효율적입니다. 애니메이션에는 MPEG4/WebM 동영상을, 정적인 이미지에는 GIF 대신 PNG/WebP를 사용하여 네트워크 용량을 절약하세요. [효율적인 동영상 형식에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "애니메이션 콘텐츠에 동영상 형식 사용하기"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill 및 변환을 통해 레거시 브라우저에서 새로운 자바스크립트 기능을 사용할 수 있습니다. 하지만 최신 브라우저에서는 대부분 필요하지 않습니다. 번들로 포함된 자바스크립트의 경우, module/nomodule 기능 감지를 사용하는 최신 스크립트 배포 전략을 채택하여 최신 브라우저에 적용되는 코드를 줄이고 레거시 브라우저 지원을 유지할 수 있습니다. [최신 자바스크립트 사용 방법 알아보기](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "레거시 JavaScript를 최신 브라우저에 제공하지 않기"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "WebP 및 AVIF와 같은 이미지 형식은 PNG나 JPEG보다 압축률이 높기 때문에 다운로드가 빠르고 데이터 소비량도 적습니다. [최신 이미지 형식에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "차세대 형식을 사용해 이미지 제공하기"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "중요한 리소스의 로드가 모두 완료된 후에는 오프스크린 및 숨겨진 이미지를 지연 로드함으로써 상호작용 시작 시간을 줄이는 것이 좋습니다. [오프스크린 이미지 지연 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "오프스크린 이미지 지연하기"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "리소스가 페이지의 첫 페인트를 차단하고 있습니다. 중요한 JS/CSS를 인라인으로 전달하고 중요하지 않은 모든 JS/Style을 지연하는 것이 좋습니다. [렌더링 차단 리소스를 제거하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "렌더링 차단 리소스 제거하기"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "네트워크 페이로드가 커지면 사용자에게 실제 비용 부담이 되며 로드 시간이 길어질 수 있습니다. [페이로드 크기를 줄이는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "총 크기: {totalBytes, number, bytes}KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "네트워크 페이로드가 커지지 않도록 관리하기"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "대규모 네트워크 페이로드 방지하기"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS 파일을 축소하면 네트워크 페이로드 크기를 줄일 수 있습니다. [CSS를 축소하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS 축소하기"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "자바스크립트 파일을 축소하면 페이로드 크기와 스크립트 파싱 시간을 줄일 수 있습니다. [자바스크립트를 축소하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "자바스크립트 줄이기"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "스타일시트에서 사용되지 않는 규칙을 줄이고 스크롤 없이 볼 수 있는 콘텐츠의 경우 사용되지 않는 CSS의 로딩을 지연시켜 네트워크 활동에 소비되는 용량을 줄이세요. [사용되지 않는 CSS를 줄이는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "사용하지 않는 CSS 줄이기"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "사용되지 않는 자바스크립트를 줄이고 스크립트가 필요할 때까지 로딩을 지연시켜 네트워크 활동에 소비되는 용량을 줄이세요. [사용되지 않는 자바스크립트를 줄이는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "사용하지 않는 자바스크립트 줄이기"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "캐시 수명이 길면 페이지를 반복해서 방문하는 속도가 빨라질 수 있습니다. [효율적인 캐시 정책에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{리소스 1개 발견됨}other{리소스 #개 발견됨}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "효율적인 캐시 정책을 사용하여 정적인 애셋 제공하기"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "정적인 애셋에 효율적인 캐시 정책 사용하기"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "최적화된 이미지는 빠르게 로드되며 모바일 데이터를 적게 소비합니다. [효율적으로 이미지를 인코딩하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "효율적으로 이미지 인코딩하기"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "실제 크기"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "표시된 크기"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "이미지가 표시된 크기보다 큽니다"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "이미지가 표시된 크기에 적합합니다"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "적절한 크기의 이미지를 게재하여 모바일 데이터를 절약하고 로드 시간을 단축하세요. [이미지 크기를 조정하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "이미지 크기 적절하게 설정하기"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "네트워크 사용 총량을 최소화하려면 텍스트 기반 리소스를 압축(gzip, deflate, brotli)하여 제공해야 합니다. [텍스트 압축 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "텍스트 압축 사용"}, "core/audits/content-width.js | description": {"message": "앱 콘텐츠의 너비가 표시 영역의 너비와 일치하지 않을 경우 앱이 휴대기기 화면에 최적화되지 않을 수 있습니다. [표시 영역에 맞게 콘텐츠 크기를 조절하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "{innerWidth}px의 표시 영역 크기는 {outerWidth}px인 창 크기와 일치하지 않습니다."}, "core/audits/content-width.js | failureTitle": {"message": "콘텐츠의 크기가 표시 영역에 알맞지 않음"}, "core/audits/content-width.js | title": {"message": "콘텐츠의 크기가 표시 영역에 알맞음"}, "core/audits/critical-request-chains.js | description": {"message": "다음의 크리티컬 요청 체인은 로드 시 우선순위가 높은 리소스를 보여줍니다. 체인의 길이를 줄이고, 리소스의 다운로드 크기를 줄이거나 불필요한 리소스의 다운로드를 지연하여 페이지 로드 속도를 높이는 것이 좋습니다. [크리티컬 요청 체이닝을 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1개 체인 발견됨}other{#개 체인 발견됨}}"}, "core/audits/critical-request-chains.js | title": {"message": "중요 요청 체이닝 차단"}, "core/audits/csp-xss.js | columnDirective": {"message": "명령어"}, "core/audits/csp-xss.js | columnSeverity": {"message": "심각도"}, "core/audits/csp-xss.js | description": {"message": "강력한 콘텐츠 보안 정책(CSP)을 사용하면 교차 사이트 스크립팅(XSS) 공격을 받을 위험이 크게 줄어듭니다. [CSP를 사용하여 XSS를 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "구문"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "페이지에 <meta> 태그에서 정의된 CSP가 포함되어 있습니다. CSP를 HTTP 헤더로 이동하거나 HTTP 헤더에서 다른 엄격한 CSP를 정의하는 것이 좋습니다."}, "core/audits/csp-xss.js | noCsp": {"message": "시행 모드에 CSP가 없습니다."}, "core/audits/csp-xss.js | title": {"message": "CSP가 XSS 공격에 효과적인지 확인"}, "core/audits/deprecations.js | columnDeprecate": {"message": "지원 중단/경고"}, "core/audits/deprecations.js | columnLine": {"message": "행"}, "core/audits/deprecations.js | description": {"message": "이후에 지원 중단된 API는 브라우저에서 삭제됩니다. [지원 중단된 API에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{경고 1개가 발견됨}other{경고 #개가 발견됨}}"}, "core/audits/deprecations.js | failureTitle": {"message": "지원 중단된 API 사용"}, "core/audits/deprecations.js | title": {"message": "지원 중단 API 사용하지 않기"}, "core/audits/dobetterweb/charset.js | description": {"message": "문자 인코딩 선언이 필요합니다. HTML의 첫 1,024바이트 또는 Content-Type HTTP 응답 헤더에서 `<meta>` 태그를 사용하여 선언할 수 있습니다. [문자 인코딩 선언에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "문자 집합 선언이 없거나 HTML에서 너무 뒤에 있음"}, "core/audits/dobetterweb/charset.js | title": {"message": "문자 집합을 제대로 정의함"}, "core/audits/dobetterweb/doctype.js | description": {"message": "doctype을 지정하면 브라우저가 쿼크 모드로 전환할 수 없습니다. [doctype 선언 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE 이름은 문자열 `html`이어야 합니다."}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "문서에 `limited-quirks-mode`를 트리거하는 `doctype`이(가) 포함되어 있습니다."}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "문서에는 Doctype이 포함되어 있어야 합니다."}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId가 빈 문자열일 것으로 예상됨"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId가 빈 문자열일 것으로 예상됨"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "문서에 `quirks-mode`를 트리거하는 `doctype`이(가) 포함되어 있습니다."}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "페이지에 HTML Doctype이 없으므로 쿼크 모드가 트리거됨"}, "core/audits/dobetterweb/doctype.js | title": {"message": "페이지에 HTML Doctype 있음"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "통계"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "값"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "DOM이 크면 메모리 사용량이 늘어나고 [스타일 계산](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) 시간이 길어질 수 있으며 큰 비용이 드는 [레이아웃 리플로우](https://developers.google.com/speed/articles/reflow)가 발생할 수 있습니다. [과도한 DOM 크기를 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{요소 1개}other{요소 #개}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "과도한 DOM 크기 지양하기"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "최대 DOM 깊이"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "총 DOM 요소 개수"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "최대 하위 요소"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "과도한 DOM 크기 지양하기"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "사용자가 컨텍스트 없이 위치 정보를 요청한 사이트를 신뢰할 수 없거나 이로 인해 혼란스러운 상태입니다. 대신 사용자 작업 요청 입력을 고려해 보세요. [위치정보 권한에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "페이지 로드 시 위치정보 권한 요청"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "페이지 로드 시 위치정보 권한 요청 방지하기"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "문제 유형"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome Devtools의 `Issues` 패널에 로그된 문제는 아직 해결되지 않은 문제들입니다. 이러한 문제는 네트워크 요청 실패, 충분하지 않은 보안 제어를 비롯한 기타 브라우저 문제로 인해 발생할 수 있습니다. 각 문제에 관한 자세한 내용은 Chrome DevTools의 문제 패널에서 확인하세요."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "문제가 Chrome Devtools의 `Issues` 패널에 로그됨"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "교차 도메인 정책에 의해 차단됨"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "광고에서 과도한 리소스 사용"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome Devtools의 `Issues` 패널에 문제 없음"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "버전"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "페이지에서 감지된 모든 프런트엔드 자바스크립트 라이브러리입니다. [자바스크립트 라이브러리 감지 진단 감사에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "감지된 JavaScript 라이브러리"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "연결이 느린 사용자의 경우 `document.write()`에서 동적으로 삽입된 외부 스크립트로 인해 페이지 로드가 몇십 초까지 지연될 수 있습니다. [document.write()를 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` 피하기"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` 지양하기"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "사용자가 컨텍스트 없이 알림 전송을 요청한 사이트를 신뢰할 수 없거나 이로 인해 혼란스러운 상태입니다. 대신 사용자 동작에 대한 요청 입력을 고려해 보세요. [책임감 있는 방식으로 알림 권한을 받는 방법 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "페이지 로드 시 알림 권한 요청"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "페이지 로드 시 알림 권한 요청 방지하기"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "프로토콜"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2는 HTTP/1.1에 비해 바이너리 헤더, 멀티플렉싱 등의 다양한 이점을 제공합니다. [HTTP/2에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{HTTP/2를 통해 게재되지 않는 요청 1건}other{HTTP/2를 통해 게재되지 않는 요청 #건}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2를 사용하세요"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "터치 및 휠 이벤트 리스너를 `passive`로 표시하면 페이지 스크롤 성능을 개선할 수 있습니다. [패시브 이벤트 리스너 채택에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "스크롤 성능 개선에 패시브 리스너를 사용하지 않음"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "스크롤 성능 개선에 패시브 리스너 사용"}, "core/audits/errors-in-console.js | description": {"message": "콘솔에 로그된 오류는 해결되지 않은 문제를 의미합니다. 네트워크 요청 실패를 비롯한 기타 브라우저 문제로 인해 발생할 수 있습니다. [콘솔 진단 감사에서 이 오류에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "브라우저 오류가 콘솔에 로그됨"}, "core/audits/errors-in-console.js | title": {"message": "콘솔에 로그된 브라우저 오류 없음"}, "core/audits/font-display.js | description": {"message": "`font-display` CSS 기능을 활용하면 웹폰트를 로드하는 동안 사용자가 텍스트를 볼 수 있습니다. [`font-display`에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "웹폰트가 로드되는 동안 텍스트가 계속 표시되는지 확인하기"}, "core/audits/font-display.js | title": {"message": "웹폰트가 로드되는 동안 모든 텍스트가 계속 표시됩니다"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse에서 원본 {fontOrigin}의 `font-display` 값을 자동으로 확인할 수 없습니다.}other{Lighthouse에서 원본 {fontOrigin}의 `font-display` 값을 자동으로 확인할 수 없습니다.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "가로세로 비율(실제)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "가로세로 비율(표시됨)"}, "core/audits/image-aspect-ratio.js | description": {"message": "이미지 표시 측정기준은 원래 가로세로 비율과 일치해야 합니다. [이미지 가로세로 비율에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "이미지를 올바르지 않은 가로세로 비율로 표시"}, "core/audits/image-aspect-ratio.js | title": {"message": "이미지를 올바른 가로세로 비율로 표시"}, "core/audits/image-size-responsive.js | columnActual": {"message": "실제 크기"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "표시된 크기"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "예상 크기"}, "core/audits/image-size-responsive.js | description": {"message": "이미지의 본래 크기가 표시 크기와 픽셀 비율에 비례해야 이미지가 가장 선명하게 보입니다. [반응형 이미지를 제공하는 방법 알아보기](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "이미지가 낮은 해상도로 제공됨"}, "core/audits/image-size-responsive.js | title": {"message": "이미지가 적절한 해상도로 제공됨"}, "core/audits/installable-manifest.js | already-installed": {"message": "앱이 이미 설치되어 있습니다."}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "매니페스트에서 필요한 아이콘을 다운로드할 수 없음"}, "core/audits/installable-manifest.js | columnValue": {"message": "실패 이유"}, "core/audits/installable-manifest.js | description": {"message": "서비스 워커는 앱에서 오프라인, 홈 화면에 추가, 푸시 알림 등 다양한 프로그레시브 웹 앱 기능을 사용할 수 있도록 하는 기술입니다. 적절한 서비스 워커와 매니페스트를 구현할 경우 브라우저는 사용자에게 홈 화면에 앱을 추가하라는 메시지를 사전에 표시할 수 있으며 이렇게 하면 참여도가 높아질 수 있습니다. [매니페스트 설치 가능 요건에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{이유: 1개}other{이유: #개}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "웹 앱 매니페스트 또는 서비스 워커가 설치 가능 요건을 충족하지 않음"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play 스토어 앱 URL 및 Play 스토어 ID가 일치하지 않습니다."}, "core/audits/installable-manifest.js | in-incognito": {"message": "페이지가 시크릿 창에 로드되었습니다."}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "매니페스트 'display' 속성은 'standalone', 'fullscreen', 'minimal-ui' 중 하나여야 합니다."}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "매니페스트에 'display_override' 입력란이 포함되어 있으며 첫 번째 지원되는 디스플레이 모드는 'standalone', 'fullscreen', 'minimal-ui' 중 하나여야 합니다."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "매니페스트를 가져올 수 없거나 비어있거나 파싱할 수 없습니다."}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "매니페스트를 가져오는 중에 매니페스트 URL이 변경되었습니다."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "매니페스트에 'name' 또는 'short_name' 필드가 포함되어 있지 않습니다."}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "매니페스트에 적절한 아이콘이 포함되어 있지 않습니다. 아이콘은 최소 {value0}px 크기의 PNG, SVG 또는 WebP 형식으로 sizes 속성이 설정되어야 하며 purpose 속성이 설정된 경우 'any'를 포함해야 합니다."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "제공된 아이콘 중 최소 {value0}px인 PNG, SVG, WebP 형식의 정사각형이며, purpose 속성이 설정되어 있지 않거나 'any'로 설정되어 있는 아이콘이 없습니다."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "다운로드된 아이콘이 비어 있거나 손상됨"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "제공된 Play 스토어 ID 없음"}, "core/audits/installable-manifest.js | no-manifest": {"message": "페이지에 매니페스트 <link> URL이 없습니다."}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "일치하는 서비스 워커가 감지되지 않았습니다. 페이지를 새로고침하거나 현재 페이지에 관한 서비스 워커의 범위가 범위와 매니페스트의 시작 URL을 포괄하는지 확인하세요."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "매니페스트에 'start_url' 필드가 없는 서비스 워크를 확인할 수 없습니다."}, "core/audits/installable-manifest.js | noErrorId": {"message": "설치 가능 오류 ID ‘{errorId}’을(를) 인식할 수 없습니다."}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "페이지가 보안이 설정된 출처에서 제공되지 않았습니다."}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "메인 프레임에 페이지가 로드되지 않았습니다."}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "페이지가 오프라인에서 작동하지 않습니다."}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA가 제거되었으며 설치 가능 여부 확인이 재설정되었습니다."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "지정된 애플리케이션 플랫폼이 Android에서 지원되지 않습니다."}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "매니페스트에서 다음과 같이 지정합니다. prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications는 Android의 Chrome 베타 및 공개 버전 채널에서만 지원됩니다."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse에서 서비스 워커의 유무를 판단할 수 없습니다. 최신 버전의 Chrome을 사용해 시도해 보세요."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Android에서 지원되지 않는 매니페스트 URL 스키마({scheme})입니다."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "잘못된 매니페스트 시작 URL입니다."}, "core/audits/installable-manifest.js | title": {"message": "웹 앱 매니페스트 및 서비스 워커가 설치 가능 요건을 충족함"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "매니페스트의 URL에 사용자 이름, 비밀번호 또는 포트가 포함되어 있습니다."}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "페이지가 오프라인에서 작동하지 않습니다. Chrome 93 안정화 버전이 2021년 8월 출시된 이후에는 이 페이지가 설치 가능한 것으로 간주되지 않을 것입니다."}, "core/audits/is-on-https.js | allowed": {"message": "허용됨"}, "core/audits/is-on-https.js | blocked": {"message": "차단됨"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "안전하지 않은 URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "해결 요청"}, "core/audits/is-on-https.js | description": {"message": "민감한 정보를 다루지 않는 사이트를 비롯해 모든 사이트는 HTTPS로 보호해야 합니다. 또한 [혼합 콘텐츠](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content)도 피해야 합니다. 혼합 콘텐츠에서는 초기 요청은 HTTPS로 전송되지만 일부 리소스가 HTTP를 통해 로드됩니다. HTTPS는 침입자가 앱과 사용자 사이의 통신을 조작하거나 통신에 대해 패시브 리스너를 사용하지 못하도록 방지하며 HTTP/2와 여러 신규 웹 플랫폼 API의 필수 요건이기도 합니다. [HTTPS에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{안전하지 않은 요청 1건 검색됨}other{안전하지 않은 요청 #건 검색됨}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "HTTPS 사용하지 않음"}, "core/audits/is-on-https.js | title": {"message": "HTTPS 사용"}, "core/audits/is-on-https.js | upgraded": {"message": "HTTPS로 자동 업그레이드됨"}, "core/audits/is-on-https.js | warning": {"message": "허용됨(경고 포함)"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "표시 영역에 페인팅된 가장 큰 콘텐츠 포함 요소입니다. [최대 콘텐츠 렌더링 시간 요소에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "콘텐츠가 포함된 최대 페인트 요소"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS에 기여한 양"}, "core/audits/layout-shift-elements.js | description": {"message": "DOM 요소가 페이지 CLS의 대부분을 차지합니다. [CLS 개선 방법 알아보기](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "대규모 레이아웃 변경 피하기"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "스크롤 없이 볼 수 있는 이미지가 지연 로드되면 페이지 수명 주기 후반에 렌더링되므로 최대 콘텐츠 렌더링 시간이 늘어날 수 있습니다. [최적의 지연 로드에 관해 자세히 알아보기](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "최대 콘텐츠 렌더링 시간 이미지가 지연 로드되었습니다"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "최대 콘텐츠 렌더링 시간 이미지가 지연 로드되지 않았습니다"}, "core/audits/long-tasks.js | description": {"message": "기본 스레드의 가장 긴 작업을 열거합니다. 입력 지연을 가장 많이 유발하는 작업을 찾을 때 용이합니다. [긴 기본 스레드 작업을 방지하는 방법 알아보기](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{긴 작업 #개 발견}other{긴 작업 #개 발견}}"}, "core/audits/long-tasks.js | title": {"message": "긴 기본 스레드 작업 피하기"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "카테고리"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "JS 파싱, 컴파일, 실행에 소요되는 시간을 줄여 보세요. 용량이 적은 JS 페이로드를 제공하면 도움이 될 수 있습니다. [기본 스레드 작업을 최소화하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "기본 스레드 작업 최소화하기"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "기본 스레드 작업 최소화하기"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "최대한 많은 사용자가 이용할 수 있으려면 사이트가 모든 주요 브라우저에서 작동해야 합니다. [교차 브라우저 호환성에 관해 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "사이트가 다양한 브라우저에서 작동함"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "페이지를 소셜 미디어에 공유하려면 개별 페이지가 URL을 통해 딥 링크로 연결될 수 있어야 하며 페이지별로 URL이 달라야 합니다. [딥 링크 제공에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "페이지마다 URL이 있음"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "느린 네트워크에서도 탭할 때 전환이 빠르게 느껴져야 합니다. 이 전환 경험이야말로 사용자의 성능 인식에 중요한 영향을 미칩니다. [페이지 전환 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "페이지 전환 시 네트워크에서 막히는 느낌이 들지 않음"}, "core/audits/maskable-icon.js | description": {"message": "마스크 가능한 아이콘은 앱을 기기에 설치할 때 레터박스 없이 이미지가 도형 전체를 채울 수 있게 합니다. [마스크 가능한 매니페스트 아이콘에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "매니페스트에 마스크 가능한 아이콘이 없음"}, "core/audits/maskable-icon.js | title": {"message": "매니페스트에 마스크 가능한 아이콘이 있음"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "레이아웃 변경 횟수는 표시 영역 안에 보이는 요소의 이동을 측정합니다. [레이아웃 변경 횟수 측정항목에 관해 자세히 알아보기](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interaction to Next Paint(다음 페인트와의 상호작용)은 페이지 응답성, 즉 페이지가 사용자 입력에 시각적으로 반응하는 데 걸리는 시간을 측정합니다. [Interaction to Next Paint(다음 페인트와의 상호작용) 측정항목에 관해 자세히 알아보기](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "콘텐츠가 포함된 첫 페인트는 첫 번째 텍스트 또는 이미지가 표시되는 시간을 나타냅니다. [콘텐츠가 포함된 첫 페인트 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "유의미한 첫 페인트는 페이지의 기본 콘텐츠가 표시되는 경우를 측정합니다. [유의미한 첫 페인트 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "상호작용 시작 시간은 페이지와 완전히 상호작용할 수 있게 될 때까지 걸리는 시간입니다. [상호작용 시작 시간 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "최대 콘텐츠 렌더링 시간은 최대 텍스트 또는 이미지가 표시되는 시간을 나타냅니다. [최대 콘텐츠 렌더링 시간 측정항목 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "사용자가 경험할 수 있는 최초 입력 반응 시간의 최댓값은 가장 긴 작업의 길이입니다. [최초 입력 반응 시간 최댓값 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "속도 색인은 페이지 콘텐츠가 얼마나 빨리 표시되는지 보여줍니다. [속도 색인 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "FCP와 상호작용 시간 사이의 모든 시간의 합으로, 작업 지속 시간이 50ms를 넘으면 밀리초 단위로 표현됩니다. [총 차단 시간 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "네트워크 왕복 시간(RTT)이 성능에 큰 영향을 줍니다. 출발지로의 RTT가 크면 서버가 사용자에게 가까울 때 성능이 향상될 수 있음을 나타냅니다. [왕복 시간에 관해 자세히 알아보기](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "네트워크 왕복 시간"}, "core/audits/network-server-latency.js | description": {"message": "서버 지연 시간은 웹 성능에 영향을 줄 수 있습니다. 출발지의 서버 지연 시간이 길면 서버에 과부하가 걸렸거나 백엔드 성능이 낮음을 나타냅니다. [서버 응답 시간에 관해 자세히 알아보기](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "서버 백엔드 지연 시간"}, "core/audits/no-unload-listeners.js | description": {"message": "`unload` 이벤트는 안정적으로 실행되지 않으며 이 이벤트를 수신 대기하면 '뒤로-앞으로 캐시'와 같은 브라우저 최적화 기능을 사용하지 못할 수 있습니다. `pagehide` 또는 `visibilitychange` 이벤트를 대신 사용하세요. [이벤트 리스너 로드 취소에 관해 자세히 알아보기](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "`unload` 리스너 등록"}, "core/audits/no-unload-listeners.js | title": {"message": "`unload` 이벤트 리스너를 사용하지 않음"}, "core/audits/non-composited-animations.js | description": {"message": "합성 작업을 거치지 않은 애니메이션은 품질이 나쁘고 CLS를 높일 수 있습니다. [합성 작업을 거치지 않은 애니메이션을 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{애니메이션 요소 #개 발견됨}other{애니메이션 요소 #개 발견됨}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "필터 관련 속성으로 인해 픽셀이 움직일 수 있음"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "타겟에 호환되지 않는 다른 애니메이션이 있음"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "효과에 'replace'가 아닌 합성 모드가 있음"}, "core/audits/non-composited-animations.js | title": {"message": "합성 작업을 거치지 않은 애니메이션 지양하기"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "변환 관련 속성은 박스 크기에 의존합니다."}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{지원되지 않는 CSS 속성: {properties}}other{지원되지 않는 CSS 속성: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "지원되지 않는 타이밍 매개변수가 효과에 포함됨"}, "core/audits/performance-budget.js | description": {"message": "네트워크 요청의 양과 크기는 제공된 성능 예산에 따라 설정된 목표치 아래로 유지하세요. [성능 예산에 관해 자세히 알아보기](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{요청 1건}other{요청 #건}}"}, "core/audits/performance-budget.js | title": {"message": "성능 예산"}, "core/audits/preload-fonts.js | description": {"message": "신규 방문자가 사용할 수 있도록 `optional` 글꼴을 미리 로드하세요 [글꼴 미리 로드에 관해 자세히 알아보기](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "`font-display: optional`을 사용하는 글꼴이 미리 로드되지 않음"}, "core/audits/preload-fonts.js | title": {"message": "`font-display: optional`을 사용하는 글꼴이 미리 로드됨"}, "core/audits/prioritize-lcp-image.js | description": {"message": "LCP(최대 콘텐츠 렌더링 시간) 요소가 페이지에 동적으로 추가되는 경우 LCP를 개선하기 위해 이미지를 미리 로드해야 합니다. [LCP 요소 미리 로드에 관해 자세히 알아보기](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "콘텐츠가 포함된 최대 페인트 이미지 미리 로드"}, "core/audits/redirects.js | description": {"message": "리디렉션을 사용하면 페이지가 로드되기 전 추가적인 지연이 발생합니다. [페이지 리디렉션을 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "여러 차례의 페이지 리디렉션 피하기"}, "core/audits/resource-summary.js | description": {"message": "페이지 리소스의 수량과 크기와 관련해 예산을 설정하려면 budget.json 파일을 추가하세요. [성능 예산에 관해 자세히 알아보기](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{요청 1개 • {byteCount, number, bytes}KiB}other{요청 #개 • {byteCount, number, bytes}KiB}}"}, "core/audits/resource-summary.js | title": {"message": "요청 수는 낮게, 전송 크기는 작게 유지하기"}, "core/audits/seo/canonical.js | description": {"message": "표준 링크는 검색결과에 어떤 URL을 표시할지 알려줍니다. [표준 링크에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "여러 개의 URL({urlList})이 충돌됨"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "잘못된 URL({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "다른 `hreflang` 위치({url})를 가리킵니다."}, "core/audits/seo/canonical.js | explanationRelative": {"message": "절대 URL이 아님({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "콘텐츠 페이지가 아닌 도메인의 루트 URL(홈페이지)을 가리킴"}, "core/audits/seo/canonical.js | failureTitle": {"message": "문서에 유효한 `rel=canonical` 없음"}, "core/audits/seo/canonical.js | title": {"message": "문서에 유효한 `rel=canonical` 있음"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "크롤링할 수 없는 링크"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "검색엔진에서 링크에 대해 `href` 속성을 사용하여 웹사이트를 크롤링할 수 있습니다. 앵커 요소의 `href` 속성이 적절한 대상에 연결되어 사이트에서 더 많은 페이지가 검색되도록 하세요. [링크를 크롤링할 수 있게 만드는 방법 알아보기](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "링크를 크롤링할 수 없음"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "링크를 크롤링할 수 있음"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "읽을 수 없는 추가 텍스트"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "글꼴 크기"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "페이지 텍스트 비율(%)"}, "core/audits/seo/font-size.js | columnSelector": {"message": "선택기"}, "core/audits/seo/font-size.js | description": {"message": "12px보다 글꼴 크기가 작으면 읽기 어렵기 때문에 모바일 방문자가 '손가락으로 확대'해야만 읽을 수 있습니다. 페이지 텍스트의 60% 이상을 12px 이상으로 유지하도록 노력하세요. [읽을 수 있는 글꼴 크기에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} 읽기 쉬운 텍스트"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "모바일 화면에 최적화된 표시 영역 메타 태그가 없어서 텍스트를 알아볼 수 없음"}, "core/audits/seo/font-size.js | failureTitle": {"message": "문서에서 읽기 쉬운 글꼴 크기를 사용하지 않음"}, "core/audits/seo/font-size.js | legibleText": {"message": "읽기 쉬운 텍스트"}, "core/audits/seo/font-size.js | title": {"message": "문서가 읽기 쉬운 글꼴 크기를 사용함"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang 링크는 검색엔진에 특정 언어나 지역에서 페이지의 어떤 버전을 검색결과로 표시해야 할지 알려줍니다. [`hreflang`에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "문서에 유효한 `hreflang` 없음"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "상대적인 href 값"}, "core/audits/seo/hreflang.js | title": {"message": "문서에 유효한 `hreflang` 있음"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "예상치 못한 언어 코드"}, "core/audits/seo/http-status-code.js | description": {"message": "실패한 HTTP 상태 코드가 있는 페이지는 제대로 색인이 생성되지 않을 수 있습니다. [HTTP 상태 코드에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "페이지에 실패한 HTTP 상태 코드가 있음"}, "core/audits/seo/http-status-code.js | title": {"message": "페이지에 성공적인 HTTP 상태 코드가 있음"}, "core/audits/seo/is-crawlable.js | description": {"message": "검색엔진이 페이지를 크롤링할 권한이 없으면 페이지를 검색결과에 포함할 수 없습니다. [크롤러 지시어 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "페이지의 색인 생성이 차단됨"}, "core/audits/seo/is-crawlable.js | title": {"message": "페이지의 색인 생성이 차단되지 않음"}, "core/audits/seo/link-text.js | description": {"message": "설명적인 링크 텍스트는 검색엔진에서 콘텐츠를 이해하는 데 도움을 줍니다. [링크 접근성을 높이는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{링크 1개 찾음}other{링크 #개 찾음}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "링크에 설명 텍스트가 없음"}, "core/audits/seo/link-text.js | title": {"message": "링크에 설명 텍스트가 있음"}, "core/audits/seo/manual/structured-data.js | description": {"message": "[구조화된 데이터용 테스트 도구](https://search.google.com/structured-data/testing-tool/)와 [구조화된 데이터 Linter](http://linter.structured-data.org/)를 실행하여 구조화된 데이터를 검증합니다. [구조화된 데이터에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "구조화된 데이터가 유효함"}, "core/audits/seo/meta-description.js | description": {"message": "검색결과에 페이지 콘텐츠를 간략하게 요약하기 위한 메타 설명이 포함될 수 있습니다. [메타 설명 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "설명 텍스트가 비어 있습니다."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "문서에 메타 설명이 없음"}, "core/audits/seo/meta-description.js | title": {"message": "문서에 메타 설명이 있음"}, "core/audits/seo/plugins.js | description": {"message": "검색엔진은 플러그인 콘텐츠의 색인을 생성할 수 없고 플러그인을 제한하거나 지원하지 않는 기기도 많습니다. [플러그인 방지 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "문서가 플러그인을 사용함"}, "core/audits/seo/plugins.js | title": {"message": "문서에서 플러그인을 사용할 수 없음"}, "core/audits/seo/robots-txt.js | description": {"message": "robots.txt 파일 형식이 잘못된 경우 크롤러가 웹사이트를 어떻게 크롤링하고 색인을 생성해야 할지 파악하지 못할 수 있습니다. [robots.txt에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt 요청에 반환된 HTTP 상태: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{오류 1개 발견}other{오류 #개 발견}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse에서 robots.txt 파일을 다운로드할 수 없음"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt가 유효하지 않음"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt가 유효함"}, "core/audits/seo/tap-targets.js | description": {"message": "버튼이나 링크 같은 상호작용 요소는 충분히 커야 하며(48 x 48px), 주변에 충분한 공간이 있고 다른 요소와 겹치지 않아 쉽게 탭할 수 있어야 합니다. [탭 타겟에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} 탭 타겟 크기가 적절함"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "모바일 화면에 최적화된 표시 영역 메타 태그가 없어서 탭 타겟이 너무 작음"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "탭 타겟 크기가 적절하지 않음"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "타겟이 중복됨"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "탭 타겟"}, "core/audits/seo/tap-targets.js | title": {"message": "탭 타겟의 크기가 적절함"}, "core/audits/server-response-time.js | description": {"message": "다른 모든 요청이 기본 문서의 영향을 받으므로 기본 문서에 대한 서버 응답 시간을 짧게 유지하세요. [Time to First Byte(첫 바이트까지의 시간) 측정항목에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "루트 문서에 {timeInMs, number, milliseconds} ms 소요됨"}, "core/audits/server-response-time.js | failureTitle": {"message": "초기 서버 응답 시간 단축"}, "core/audits/server-response-time.js | title": {"message": "초기 서버 응답 시간 짧음"}, "core/audits/service-worker.js | description": {"message": "서비스 워커는 앱에서 오프라인, 홈 화면에 추가, 푸시 알림 등 다양한 프로그레시브 웹 앱 기능을 사용할 수 있도록 설정하는 기술입니다. [서비스 워커에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "이 페이지는 서비스 워커로 인해 제어되지만 매니페스트가 유효한 JSON으로 파싱하는 데 실패했으므로 `start_url`을(를) 찾지 못했습니다."}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "이 페이지는 서비스 워커로 인해 제어되지만 `start_url`({startUrl})이(가) 서비스 워커의 범위({scopeUrl})에 있지 않습니다."}, "core/audits/service-worker.js | explanationNoManifest": {"message": "이 페이지는 서비스 워커로 인해 제어되지만 가져온 매니페스트가 없으므로 `start_url`을(를) 찾지 못했습니다."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "이 원본에 하나 이상의 서비스 워커가 있지만 페이지({pageUrl})가 범위 내에 있지 않습니다."}, "core/audits/service-worker.js | failureTitle": {"message": "페이지와 `start_url`을(를) 제어하는 서비스 워커를 등록하지 않음"}, "core/audits/service-worker.js | title": {"message": "페이지와 `start_url`을(를) 제어하는 서비스 워커를 등록함"}, "core/audits/splash-screen.js | description": {"message": "테마 스플래시 화면을 사용하면 사용자가 홈 화면에서 앱을 실행했을 때 고품질의 환경을 경험할 수 있습니다. [스플래시 화면에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "맞춤 스플래시 화면에 맞게 구성되지 않음"}, "core/audits/splash-screen.js | title": {"message": "맞춤 스플래시 화면에 맞게 구성됨"}, "core/audits/themed-omnibox.js | description": {"message": "브라우저 주소 표시줄에는 사이트에 맞는 테마를 설정할 수 있습니다. [주소 표시줄 테마 설정에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "주소 표시줄의 테마 색상을 설정하지 않음"}, "core/audits/themed-omnibox.js | title": {"message": "주소 표시줄의 테마 색상을 설정함"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName}(고객 성공 사례)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName}(마케팅)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName}(소셜)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName}(동영상)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "제품"}, "core/audits/third-party-facades.js | description": {"message": "일부 서드 파티 임베딩은 지연 로드될 수 있습니다. 필요할 때까지 퍼사드로 대체해 보세요. [퍼사드로 서드 파티를 지연하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{대체 퍼사드 #개 사용 가능}other{대체 퍼사드 #개 사용 가능}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "일부 타사 리소스는 퍼사드와 함께 지연 로드될 수 있음"}, "core/audits/third-party-facades.js | title": {"message": "퍼사드로 타사 리소스 지연 로드"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "타사"}, "core/audits/third-party-summary.js | description": {"message": "서드 파티 코드는 로드 성능에 크게 영향을 미칠 수 있습니다. 페이지에서 먼저 로딩을 끝낸 후 중복되는 서드 파티 공급업체의 수를 제한하고 서드 파티 코드를 로드해 보세요. [서드 파티 영향을 최소화하는 방법 알아보기](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "타사 코드가 {timeInMs, number, milliseconds} ms 동안 기본 스레드를 차단했습니다."}, "core/audits/third-party-summary.js | failureTitle": {"message": "타사 코드의 영향을 줄임"}, "core/audits/third-party-summary.js | title": {"message": "타사 사용량 최소화"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "측정값"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "측정항목"}, "core/audits/timing-budget.js | description": {"message": "시기 예산을 설정하면 사이트 실적을 확인하는 데 도움이 됩니다. 성능 기준에 맞는 사이트는 빠르게 로드되고 사용자 입력 이벤트에 빠르게 응답합니다. [성능 예산에 관해 자세히 알아보기](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "시기 예산"}, "core/audits/unsized-images.js | description": {"message": "이미지 요소에 명시적인 너비 및 높이를 설정하여 레이아웃 변경 횟수를 줄이고 누적 레이아웃 변경을 개선합니다. [이미지 크기 설정 방법 알아보기](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "이미지 요소에 `width` 및 `height`가 명시되어 있지 않습니다"}, "core/audits/unsized-images.js | title": {"message": "이미지 요소에 `width` 및 `height`가 명시되어 있습니다"}, "core/audits/user-timings.js | columnType": {"message": "유형"}, "core/audits/user-timings.js | description": {"message": "앱에서 User Timing API를 사용하여 중요 사용자 경험이 이루어지는 동안의 실제 성능을 측정하세요. [사용자 시간 표시에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{사용자 시간 1회}other{사용자 시간 #회}}"}, "core/audits/user-timings.js | title": {"message": "사용자 타이밍 표시 및 측정 값"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "'{security<PERSON><PERSON>in}'에 `<link rel=preconnect>` 링크가 있으나 브라우저에서 사용되지 않았습니다. `crossorigin` 속성을 올바르게 사용하고 있는지 확인하세요."}, "core/audits/uses-rel-preconnect.js | description": {"message": "`preconnect` 또는 `dns-prefetch` 리소스 힌트를 추가하여 중요한 서드 파티 원본에 대한 조기 연결을 수립하는 것이 좋습니다. [필수 원본에 사전 연결하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "필수 원본 미리 연결하기"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "`<link rel=preconnect>` 연결이 3개 이상 발견되었습니다. 사전 연결 링크는 가장 중요한 원본에 한해 조금만 사용해야 합니다."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "'{security<PERSON><PERSON>in}'에 `<link rel=preconnect>` 링크가 있으나 브라우저에서 사용되지 않았습니다. 페이지에서 확실하게 요청하는 중요 출처에만 `preconnect`를 사용합니다."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "'{preloadURL}'에 미리 로드된 `<link>` 링크가 있으나 브라우저에서 사용되지 않았습니다. `crossorigin` 속성을 올바르게 사용하고 있는지 확인하세요."}, "core/audits/uses-rel-preload.js | description": {"message": "`<link rel=preload>`를 사용하여 현재 요청되는 리소스를 나중에 페이지 로드에 가져올 때 우선순위를 정하는 것이 좋습니다. [중요한 요청을 미리 로드하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "중요한 요청을 미리 로드하기"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "맵 URL"}, "core/audits/valid-source-maps.js | description": {"message": "소스 맵은 축소된 코드를 원본 소스 코드로 변환합니다. 이는 프로덕션 단계에서 개발자의 디버깅에 도움이 됩니다. 또한 Lighthouse에서 더 많은 정보를 확인할 수 있습니다. 소스 맵을 배포하여 이러한 이점을 활용해 보세요. [소스 맵 자세히 알아보기](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "큰 자사 자바스크립트에 소스 맵이 누락됨"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "큰 자바스크립트 파일에 소스 맵이 없음"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{경고: `.sourcesContent`에 항목 1개 누락}other{경고: `.sourcesContent`에 항목 #개 누락}}"}, "core/audits/valid-source-maps.js | title": {"message": "페이지에 유효한 소스 맵이 있음"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">`는 앱을 모바일 화면 크기에 최적화할 뿐만 아니라 [사용자 입력 300밀리초 지연](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)도 방지합니다. [viewport meta 태그 사용에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">` 태그 없음"}, "core/audits/viewport.js | failureTitle": {"message": "`width` 또는 `initial-scale`이(가) 포함된 `<meta name=\"viewport\">` 태그가 없음"}, "core/audits/viewport.js | title": {"message": "`width` 또는 `initial-scale`이(가) 포함된 `<meta name=\"viewport\">` 태그가 있음"}, "core/audits/work-during-interaction.js | description": {"message": "Interaction to Next Paint(다음 페인트와의 상호작용) 측정 중에 발생하는 스레드 차단 작업입니다. [Interaction to Next Paint(다음 페인트와의 상호작용) 측정항목에 관해 자세히 알아보기](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "'{interactionType}' 이벤트에 {timeInMs, number, milliseconds}ms 소요"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "이벤트 타겟"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "주요 상호작용 중 작업 최소화"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "입력 지연"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "표시 지연"}, "core/audits/work-during-interaction.js | processingTime": {"message": "처리 시간"}, "core/audits/work-during-interaction.js | title": {"message": "주요 상호작용 중 작업 최소화"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "애플리케이션의 ARIA 사용을 개선하도록 추천된 사항입니다. 이를 통해 스크린 리더와 같은 지원 기술을 사용하는 사용자의 환경을 개선할 수 있습니다."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "오디오 및 비디오의 대체 콘텐츠를 제공하도록 추천된 사항입니다. 이를 통해 청각 장애나 시각 장애가 있는 사용자의 환경을 개선할 수 있습니다."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "오디오 및 동영상"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "일반적인 접근성 권장사항을 강조표시합니다."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "권장사항"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "이 검사에서는 [웹 앱의 접근성을 개선](https://developer.chrome.com/docs/lighthouse/accessibility/)하도록 추천된 사항을 강조합니다. 접근성 문제의 일부만 자동으로 감지되므로 직접 테스트하는 것도 좋습니다."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "자동화된 테스트 도구가 처리할 수 없는 영역을 다루는 항목입니다. [접근성 검토 실시](https://web.dev/how-to-review/)에 관한 가이드에서 자세히 알아보세요."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "접근성"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "콘텐츠의 가독성을 개선할 추천 내용입니다."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "대비"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "다른 언어를 사용하는 사용자가 콘텐츠를 더 쉽게 이해할 수 있게 개선하도록 추천된 사항입니다."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "다국어화 및 현지화"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "애플리케이션 컨트롤의 의미 해석을 개선하도록 추천된 사항입니다. 이를 통해 스크린 리더와 같은 지원 기술을 사용하는 사용자의 환경을 개선할 수 있습니다."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "이름 및 라벨"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "애플리케이션에서 키보드 탐색을 개선하도록 추천된 사항입니다."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "탐색"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "다음은 스크린 리더와 같은 보조 기술을 사용하여 표나 목록 데이터를 읽는 사용자의 경험을 개선할 수 있는 기회입니다."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "표와 목록"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "브라우저 호환성"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "권장사항"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "일반"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "신뢰와 안전"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "사용자 환경"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "성능 예산은 사이트 성능의 표준을 설정합니다."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "예산"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "애플리케이션 성능과 관련된 추가 정보입니다. 이러한 숫자는 성능 점수에 [직접적인 영향](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)을 미치지 않습니다."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "진단"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "성능에서 가장 중요한 측면은 픽셀이 화면에 렌더링되는 속도입니다. 주요 측정항목: 최초 만족 페인트, 최초 유의미 페인트"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "최초 페인트 개선"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "이러한 권장사항은 페이지를 더 빠르게 로드하는 데 도움이 될 수 있습니다. 성능 점수에는 [직접적인 영향](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)을 미치지 않습니다."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "추천"}, "core/config/default-config.js | metricGroupTitle": {"message": "측정항목"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "페이지가 빠르게 반응하고 가능한 한 빨리 사용할 수 있는 준비가 되도록 전반적인 로드 환경을 강화하세요. 주요 측정항목: 상호작용 시간, 속도 색인"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "전반적인 개선사항"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "성능"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "프로그레시브 웹 앱의 요소를 확인하는 검사입니다. [좋은 프로그레시브 웹 앱의 특징 알아보기](https://web.dev/pwa-checklist/)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "이 검사는 기준 [PWA 체크리스트](https://web.dev/pwa-checklist/)의 필수 항목이지만 Lighthouse에서 자동으로 확인되지는 않습니다. 점수에 영향을 미치지는 않지만 직접 확인하는 것이 중요합니다."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "설치 가능"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA 최적화됨"}, "core/config/default-config.js | seoCategoryDescription": {"message": "페이지가 기본적인 검색엔진 최적화 권고를 따르는지 확인하는 검사입니다. Lighthouse가 여기에서 고려하지는 않지만 검색 순위에 영향을 줄 수 있는 요소(예: [코어 웹 바이탈](https://web.dev/learn-core-web-vitals/) 실적)는 이 외에도 많이 있습니다. [Google 검색 Essentials에 관해 자세히 알아보기](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "사이트에서 이러한 추가 검증 도구를 실행하여 추가적인 검색엔진 최적화 권장사항을 확인합니다."}, "core/config/default-config.js | seoCategoryTitle": {"message": "검색엔진 최적화"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "크롤러가 앱 콘텐츠를 효과적으로 파악할 수 있도록 HTML 형식을 지정하세요."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "콘텐츠 권장사항"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "검색결과에 표시하려면 크롤러가 앱에 액세스할 수 있어야 합니다."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "크롤링 및 색인 생성"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "사용자가 콘텐츠를 읽기 위해 페이지를 확대하거나 축소할 필요가 없도록 모바일 친화적인 페이지를 지원하세요. [페이지를 모바일 친화적으로 만드는 방법 알아보기](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "모바일 친화적"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "테스트된 기기의 CPU가 Lighthouse의 예상보다 느린 것으로 보입니다. 이는 성능 점수에 부정적인 영향을 줄 수 있습니다. [적절한 CPU 감속 배율기 보정](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)에 관해 자세히 알아보세요."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "테스트 URL({requested})이 {final}(으)로 리디렉션되었기 때문에 페이지가 예상대로 로드되지 않을 수 있습니다. 두 번째 URL을 직접 테스트해 보세요."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "페이지 로드가 너무 느려 시간 제한 내에 완료되지 않았습니다. 결과가 불완전할 수 있습니다."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "브라우저 캐시 삭제 시간이 초과되었습니다. 이 페이지를 다시 감사한 후 문제가 지속되면 버그를 신고하세요."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{{locations}에 저장된 데이터가 로드 성능에 영향을 줄 수 있습니다. 시크릿 창에서 페이지를 감사하여 이러한 리소스가 점수에 영향을 주지 않도록 하세요.}other{{locations}에 저장된 데이터가 로드 성능에 영향을 줄 수 있습니다. 시크릿 창에서 페이지를 감사하여 이러한 리소스가 점수에 영향을 주지 않도록 하세요.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "원본 데이터 삭제 시간이 초과되었습니다. 이 페이지를 다시 감사한 후 문제가 지속되면 버그를 신고하세요."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "GET 요청을 통해 로드된 페이지만 뒤로-앞으로 캐시를 사용할 수 있습니다."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "상태 코드가 2XX인 페이지만 캐시될 수 있습니다."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome이 캐시된 페이지에서 자바스크립트 실행 시도를 감지했습니다."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "AppBanner를 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "플래그에서 뒤로-앞으로 캐시가 사용 중지되었습니다. chrome://flags/#back-forward-cache로 이동하여 뒤로-앞으로 캐시를 사용 설정하세요. 캐시는 기기의 로컬에 저장됩니다."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "명령줄에 의해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "메모리가 충분하지 못하여 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "권한 대리자가 뒤로-앞으로 캐시를 지원하지 않습니다."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "프리렌더러에 의해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "페이지에 등록된 리스너가 포함된 BroadcastChannel 인스턴스가 있어 페이지를 캐시할 수 없습니다."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "cache-control:no-store 헤더가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "캐시가 의도적으로 삭제되었습니다."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "다른 페이지를 캐시하기 위해 페이지가 캐시에서 삭제되었습니다."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "플러그인을 포함하고 있는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "FileChooser API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "File System Access API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "미디어 기기 디스패처를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "페이지에서 나갈 때 미디어 플레이어가 재생 중이었습니다."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "MediaSession API를 사용하며 재생 상태를 설정한 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "MediaSession API를 사용하며 작업 핸들러를 설정한 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "스크린 리더로 인해 뒤로/앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "SecurityHandler를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Serial API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "WebAuthetication API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "WebBluetooth API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "WebUSB API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "전용 worker 또는 Worklet을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "사용자가 문서에서 나갈 때까지 문서 로드가 완료되지 않았습니다."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "페이지에서 나갈 때 앱 배너가 표시되어 있었습니다."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "페이지에서 나갈 때 Chrome 비밀번호 관리자가 표시되어 있었습니다."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "페이지에서 나갈 때 DOM 디스틸레이션이 진행 중이었습니다."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "페이지에서 나갈 때 DOM Distiller 뷰어가 표시되어 있었습니다."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "메시징 API를 사용하는 확장 프로그램으로 인해 뒤로/앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "장시간 연결이 포함된 확장 프로그램은 연결을 해제해야 뒤로/앞으로 캐시에 저장될 수 있습니다."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "장기 연결이 포함된 확장 프로그램에서 뒤로/앞으로 캐시 내 프레임으로 메시지를 전송하려 했습니다."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "확장 프로그램으로 인해 뒤로/앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "페이지에서 나갈 때 양식 다시 제출과 같은 모달 대화상자 또는 http 인증 대화상자가 표시되었습니다."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "페이지에서 나갈 때 오프라인 페이지가 표시되었습니다."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "페이지에서 나갈 때 메모리 부족 알림 표시줄이 표시되어 있었습니다."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "페이지에서 나갈 때 권한 요청이 있었습니다."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "페이지에서 나갈 때 팝업 차단기가 표시되어 있었습니다."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "페이지에서 나갈 때 세이프 브라우징 세부정보가 표시되었습니다."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "세이프 브라우징에서 이 페이지를 악성으로 간주하여 팝업을 차단했습니다."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "페이지가 뒤로-앞으로 캐시에 저장된 상태에서 서비스 워커가 활성화되었습니다."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "문서 오류로 인해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "FencedFrame을 사용하는 페이지는 bfcache에 저장할 수 없습니다."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "다른 페이지를 캐시하기 위해 페이지가 캐시에서 삭제되었습니다."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "미디어 스트림 액세스 권한을 부여한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "포털을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "IdleManager를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "IndexedDB가 연결된 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "사용 불가능한 API가 사용되었습니다."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "확장 프로그램에 의해 자바스크립트가 삽입된 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "확장 프로그램에 의해 스타일시트가 삽입된 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | internalError": {"message": "내부 오류입니다."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "연결 유지 요청으로 인해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "키보드 잠금을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | loading": {"message": "사용자가 페이지에서 나갈 때까지 페이지 로드가 완료되지 않았습니다."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "기본 리소스에 cache-control:no-cache가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "기본 리소스에 cache-control:no-store가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "뒤로-앞으로 캐시에서 페이지를 복원하기 전에 탐색이 취소되었습니다."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "활성 네트워크에 연결되는 동안 페이지에서 지나치게 많은 데이터를 수신하여 페이지가 캐시에서 삭제되었습니다. Chrome은 캐시된 페이지에서 수신할 수 있는 데이터의 양을 제한합니다."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch() 또는 XHR이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "활성 네트워크 요청에 리디렉션이 발생하여 페이지가 뒤로-앞으로 캐시에서 삭제되었습니다."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "네트워크 연결 시간이 너무 길어 페이지가 캐시에서 삭제되었습니다. Chrome은 캐시된 페이지에서 데이터를 수신할 수 있는 시간을 제한합니다."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "유효한 응답 헤드가 없는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "기본 프레임이 아닌 다른 프레임에서 탐색이 이루어졌습니다."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "indexedDB 트랜잭션이 진행 중인 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "가져오기 네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "XHR 네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "PaymentManager를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "PIP 모드를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | portal": {"message": "포털을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | printing": {"message": "인쇄 UI를 표시하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "페이지가 '`window.open()`'을 사용하여 열렸고 다른 탭에 해당 페이지 참조가 있거나, 페이지에 창이 열려 있습니다."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "뒤로-앞으로 캐시에 저장된 페이지의 렌더러 프로세스가 다운되었습니다."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "뒤로-앞으로 캐시에 저장된 페이지의 렌더러 프로세스가 중단되었습니다."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "오디오 캡처 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "센서 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "백그라운드 동기화 또는 가져오기 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "MIDI 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "알림 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "저장용량 액세스를 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "동영상 캡처 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "URL 스킴이 HTTP/HTTPS인 페이지만 캐시될 수 있습니다."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "페이지가 뒤로-앞으로 캐시에 저장된 상태에서 서비스 워커에 의해 사용되었습니다."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "서비스 워커가 뒤로-앞으로 캐시에 추가된 페이지에 `MessageEvent` 속성을 전송하려고 했습니다."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "페이지가 뒤로-앞으로 캐시에 저장된 상태에서 ServiceWorker가 등록 취소되었습니다."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "서비스 워커 활성화로 인하여 페이지가 뒤로-앞으로 캐시에서 삭제되었습니다."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome이 다시 시작되면서 뒤로-앞으로 캐시 항목이 삭제되었습니다."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "SharedWorker를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "SpeechRecognizer를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "SpeechSynthesis를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "페이지의 iframe이 탐색을 시작했으나 완료되지 않았습니다."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "하위 리소스에 cache-control:no-cache가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "하위 리소스에 cache-control:no-store가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | timeout": {"message": "페이지가 뒤로-앞으로 캐시에서 최대 시간을 초과하여 만료되었습니다."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "페이지의 뒤로-앞으로 캐시 시작이 타임아웃되었습니다(pagehide 핸들러의 장시간 실행 때문일 가능성이 큼)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "페이지 기본 프레임에 로드 취소 핸들러가 있습니다."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "페이지 하위 프레임에 로드 취소 핸들러가 있습니다."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "브라우저에서 사용자 에이전트 재정의 헤더를 변경했습니다."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "동영상 녹화 또는 오디오 녹음 액세스를 부여한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "WebDatabase를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webHID": {"message": "WebHID를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "WebLock을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "WebNfc를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "WebOTPService를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "WebRTC가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | webShare": {"message": "WebShare를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "WebSocket이 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "WebTransport가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "core/lib/bf-cache-strings.js | webXR": {"message": "WebXR을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "기존 브라우저와의 하위 호환성을 위하여 https: 및 http: URL 스킴('strict-dynamic'을 지원하는 브라우저에서 무시됨)을 추가하는 것이 좋습니다."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener는 CSP3부터 지원 중단되었습니다. 대신 Cross-Origin-Opener-Policy 헤더를 사용해 주세요."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer는 CSP2부터 지원 중단되었습니다. 대신 Referrer-Policy 헤더를 사용해 주시기 바랍니다."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss는 CSP2부터 지원 중단되었습니다. 대신 X-XSS-Protection 헤더를 사용해 주시기 바랍니다."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "base-uri가 없으면 삽입된 <base> 태그로 인해 모든 상대 URL(예: 스크립트)에 대한 기본 URL이 공격자가 관리하는 도메인으로 설정될 수 있습니다. base-uri를 'none' 또는 'self'로 설정하는 것을 고려하세요."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "object-src가 누락되면 안전하지 않은 스크립트를 실행하는 플러그인이 삽입될 수 있습니다. 가능하면 object-src를 'none'으로 설정해 보세요."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src 명령어가 누락되었습니다. 이렇게 되면 안전하지 않은 스크립트의 실행이 허용됩니다."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "세미콜론 사용을 잊어버리셨나요? {keyword}은(는) 키워드가 아닌 명령어인 것 같습니다."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "nonce는 base64 문자 집합을 사용해야 합니다."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "nonce는 8자 이상이어야 합니다."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "이 지시어에서는 플레인 URL 스킴({keyword})을 사용하지 않는 것이 좋습니다. 플레인 URL 스킴을 사용하면 안전하지 않은 도메인에서 스크립트를 가져올 수 있습니다."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "이 지시어에서는 플레인 와일드 카드({keyword})를 사용하지 않는 것이 좋습니다. 플레인 와일드카드를 사용하면 안전하지 않은 도메인에서 스크립트를 가져올 수 있습니다."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "보고 도착지는 report-to 명령어로만 구성할 수 있습니다. 이 명령어는 Chromium 기반 브라우저에서만 지원되므로 report-uri 명령어도 사용하는 것이 좋습니다."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "보고 도착지를 구성하는 CSP가 없습니다. 이렇게 되면 시간이 지날수록 CSP를 유지 관리하고 손상 여부를 모니터링하기가 어려워집니다."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "호스트 허용 목록을 우회하는 경우가 많아질 수 있습니다. 필요하면 'strict-dynamic'과 함께 CSP nonce 또는 hash를 대신 사용하는 것이 좋습니다."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "알 수 없는 CSP 명령어입니다."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword}은(는) 잘못된 키워드인 것 같습니다."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "'unsafe-inline'은 안전하지 않은 인페이지 스크립트 및 이벤트 핸들러의 실행을 허용합니다. CSP nonce 또는 hash를 사용해 스크립트를 개별적으로 허용하는 것이 좋습니다."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "기존 브라우저와의 하위 호환성을 위하여 'unsafe-inline(nonce/hash를 지원하는 브라우저에서 무시됨)'을 추가하는 것이 좋습니다."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "CORS `Access-Control-Allow-Headers` 처리 시 와일드 카드 기호(*)를 사용하면 승인되지 않습니다."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "URL에 삭제된 공백 `(n|r|t)` 문자 및 '미만' 문자(`<`)가 포함된 리소스 요청이 차단되었습니다. 이러한 리소스를 로드하려면 요소 속성 값과 같은 위치에서 줄바꿈을 삭제하고 '미만' 문자를 인코딩하세요."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()`는 지원 중단되었습니다. 대신 표준화된 API인 Navigation Timing 2를 사용하세요."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()`는 사용 중지되었습니다. 대신 표준화된 API인 Paint Timing을 사용하세요."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()`는 지원 중단되었습니다. 대신 `nextHopProtocol`이 추가된 표준화된 API인 Navigation Timing 2를 사용하세요."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "`(0|r|n)` 문자를 포함하는 쿠키는 잘리는 대신 거부됩니다."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "`document.domain` 설정을 통한 동일 출처 정책의 해제는 지원 중단되었으며 기본적으로 사용 중지될 예정입니다. 이 지원 중단 경고는 `document.domain` 설정에 의해 사용 설정된 교차 출처 액세스에 적용됩니다."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "교차 출처 iframe에서의 {PH1} 트리거는 지원 중단되었으며 앞으로 삭제될 예정입니다."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "기본 Cast 통합을 사용 중지하려면 `-internal-media-controls-overlay-cast-button` 선택기 대신 `disableRemotePlayback` 속성을 사용해야 합니다."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1}은 지원 중단되었습니다. 대신 {PH2}를 사용하세요."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "지원 중단 문제 메시지의 예시 번역문입니다."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "`document.domain` 설정을 통한 동일 출처 정책의 해제는 지원 중단되었으며 기본적으로 사용 중지될 예정입니다. 이 기능을 계속 사용하려면 문서 및 프레임의 HTTP 응답과 함께 `Origin-Agent-Cluster: ?0` 헤더를 전송하여 오리진 키 에이전트 클러스터를 선택 해제하세요. 자세한 내용은 다음 페이지를 참고하세요. https://developer.chrome.com/blog/immutable-document-domain/"}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path`는 지원 중단되었으며 삭제될 예정입니다. 대신 `Event.composedPath()`를 사용하세요."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT` 헤더는 지원 중단되었으며 삭제될 예정입니다. Chrome에서는 2018년 4월 30일 이후 발행되었으며 공개적으로 신뢰할 수 있는 모든 인증서에 대해 인증서 투명성을 요구합니다."}, "core/lib/deprecations-strings.js | feature": {"message": "자세한 내용은 기능 상태 페이지에서 확인하세요."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` 및 `watchPosition()`은 안전하지 않은 출처에서 더 이상 작동하지 않습니다. 이 기능을 사용하려면 애플리케이션을 HTTPS와 같은 안전한 출처로 전환하는 것을 고려해야 합니다. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "안전하지 않은 출처의 `getCurrentPosition()` 및 `watchPosition()`은 지원 중단되었습니다. 이 기능을 사용하려면 애플리케이션을 HTTPS와 같은 안전한 출처로 전환하는 것을 고려해야 합니다. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()`는 안전하지 않은 출처에서 더 이상 작동하지 않습니다. 이 기능을 사용하려면 애플리케이션을 HTTPS와 같은 안전한 출처로 전환하는 것을 고려해야 합니다. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate`는 지원 중단되었습니다. 대신 `RTCPeerConnectionIceErrorEvent.address` 또는 `RTCPeerConnectionIceErrorEvent.port`를 사용하세요."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "`canmakepayment` 서비스 워커 이벤트의 판매자 원본 및 임의 데이터인 `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`가 지원 중단되었으며 삭제될 예정입니다."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "웹사이트에서 사용자 권한이 지정된 네트워크 위치로 인해 해당 웹사이트만 액세스 가능한 네트워크의 하위 리소스를 요청했습니다. 이러한 요청은 비공개 기기 및 서버를 인터넷에 노출시켜 크로스 사이트 요청 위조(CSRF) 공격 또는 정보 유출의 위험을 높입니다. 위험을 완화하기 위해 Chrome은 안전하지 않은 컨텍스트에서 시작된 비공개 하위 리소스에 대한 요청을 사용 중지하고 차단하기 시작합니다."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "`.css` 파일 확장자로 끝나지 않는 CSS는 `file:` URL에서 로드할 수 없습니다."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "사양 변경으로 인해 `SourceBuffer.abort()`를 사용하여 `remove()`의 비동기 범위 삭제를 취소하는 기능이 지원 중단되었습니다. 향후 지원이 삭제될 예정입니다. 대신 `updateend` 이벤트를 수신 대기해야 합니다. `abort()`는 비동기 미디어 추가 항목을 취소하거나 파서 상태를 초기화하는 데만 사용하도록 만들어졌습니다."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "사양 변경으로 인해, `MediaSource.duration`을 버퍼링된 코드 프레임의 최고 타임스탬프보다 낮게 설정하는 기능이 지원 중단되었습니다. 버퍼링된 미디어의 잘린 부분을 암시적으로 삭제하는 기능에 대한 지원은 앞으로 삭제될 예정입니다. `newDuration < oldDuration`의 경우 대신 모든 `sourceBuffers`에서 명시적 `remove(newDuration, oldDuration)`을 수행해야 합니다."}, "core/lib/deprecations-strings.js | milestone": {"message": "이 변경사항은 주 버전 번호 {milestone}에 적용됩니다."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "sysex가 `MIDIOptions`에 지정되어 있지 않은 경우에도 사용할 수 있도록 웹 MIDI가 권한을 요청합니다."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "안전하지 않은 출처의 Notification API는 더 이상 사용할 수 없습니다. 애플리케이션을 HTTPS와 같이 안전한 출처로 전환해 보세요. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "더 이상 교차 출처 iframe에서 Notification API 권한을 요청할 수 없습니다. 최상위 프레임에서 권한을 요청하거나 대신 새로운 창을 여는 방법을 고려해야 합니다."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "파트너가 지원 중단된 (D)TLS 버전을 협상합니다. 파트너에게 확인하여 이를 해결하세요."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "비보안 컨텍스트의 WebSQL은 지원 중단되었으며 곧 삭제됩니다. Web Storage 또는 Indexed Database를 사용하세요."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "img, video, canvas 태그에 `overflow: visible`을 지정하면 요소 경계 외부에 시각적 콘텐츠가 생성될 수 있습니다. 다음을 참고하세요. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments`가 지원 중단되었습니다. 대신 결제 핸들러에 just-in-time (JIT) 설치를 사용하세요."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest` 호출이 콘텐츠 보안 정책(CSP) `connect-src` 지시어를 우회했습니다. 이 우회 지원은 중단되었습니다. `PaymentRequest` API(`supportedMethods` 입력란)의 결제 수단 식별자를 CSP `connect-src` 지시어에 추가하세요."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent`는 지원 중단되었습니다. 대신 표준화된 `navigator.storage`를 사용하세요."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<picture>` 상위 요소를 지닌 `<source src>`가 잘못되었으므로 무시됩니다. 대신 `<source srcset>`를 사용하세요."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo`는 지원 중단되었습니다. 대신 표준화된 `navigator.storage`를 사용하세요."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "삽입된 사용자 인증 정보(예: `**********************/`)가 URL에 포함된 하위 리소스 요청이 차단되었습니다."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "`DtlsSrtpKeyAgreement` 제약 조건은 삭제되었습니다. 이 제약 조건에 `false` 값을 지정했으며 이는 삭제된 `SDES key negotiation` 메서드를 사용하기 위한 시도로 해석될 수 있습니다. 해당 기능은 삭제되었습니다. 대신 `DTLS key negotiation`을 지원하는 서비스를 사용하세요."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "`DtlsSrtpKeyAgreement` 제약 조건은 삭제되었습니다. 이 제약 조건에 `true` 값을 지정했으며 이는 아무런 영향을 미치지 않습니다. 정돈을 위해 이 제약 조건을 삭제할 수 있습니다."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP`가 감지되었습니다. 이 `Session Description Protocol` 버전은 더 이상 지원되지 않습니다. 대신 `Unified Plan SDP`를 사용하세요."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`{sdpSemantics:plan-b}`로 `RTCPeerConnection`을 구성할 때 사용되는 `Plan B SDP semantics`는 `Session Description Protocol`의 기존 비표준 버전으로, 웹 플랫폼에서 완전히 삭제되었습니다. `IS_FUCHSIA`로 빌드할 때는 여전히 사용 가능하지만 최대한 빨리 삭제할 예정입니다. 사용을 중지해 주시기 바랍니다. 상태는 https://crbug.com/1302249 페이지에서 확인하세요."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` 옵션은 지원 중단되었으며 삭제될 예정입니다."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer`에서는 교차 출처 분리를 요구합니다. 자세한 내용은 다음 페이지를 참고하세요. https://developer.chrome.com/blog/enabling-shared-array-buffer/"}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "사용자 활성화를 포함하지 않은 `speechSynthesis.speak()`는 지원 중단되었으며 삭제될 예정입니다."}, "core/lib/deprecations-strings.js | title": {"message": "지원 중단된 기능이 사용됨"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "`SharedArrayBuffer`를 계속 사용하려면 확장 프로그램에서 교차 출처 분리를 사용해야 합니다. 다음 페이지를 참고하세요. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/"}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1}은 공급업체마다 다릅니다. 대신 표준 {PH2}을 사용하세요."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16은 `XMLHttpRequest`의 response.json에서 지원되지 않습니다."}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "최종 사용자 환경에 부정적인 영향을 미치므로 기본 스레드의 동기식 `XMLHttpRequest`가 지원 중단되었습니다. 추가 지원이 필요한 경우 다음 페이지를 참고하세요. https://xhr.spec.whatwg.org/"}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()`은 지원 중단되었습니다. 대신 `isSessionSupported()`를 사용하고 결정된 불리언 값을 확인하세요."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "기본 스레드 차단 시간"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "캐시 TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "설명"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "시간"}, "core/lib/i18n/i18n.js | columnElement": {"message": "요소"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "통과하지 못한 요소"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "위치"}, "core/lib/i18n/i18n.js | columnName": {"message": "이름"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "예산 초과"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "요청"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "리소스 크기"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "리소스 유형"}, "core/lib/i18n/i18n.js | columnSize": {"message": "크기"}, "core/lib/i18n/i18n.js | columnSource": {"message": "소스"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "시작 시간"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "소요 시간"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "전송 크기"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "가능한 절감 효과"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "가능한 절감 효과"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "절감 가능치: {wastedBytes, number, bytes}KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{요소 1개 발견됨}other{요소 #개 발견됨}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "절감 가능치: {wastedMs, number, milliseconds} 밀리초"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "문서"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "최초 유의미 페인트"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "글꼴"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "이미지"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint(다음 페인트와의 상호작용)"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "높음"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "낮음"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "보통"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "최대 첫 입력 지연 예상 시간"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "미디어"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} 밀리초"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "기타"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "기타 리소스"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "스크립트"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} 초"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "스타일시트"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "타사"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "합계"}, "core/lib/lh-error.js | badTraceRecording": {"message": "페이지 로드 중 처리한 추적을 기록하던 중 문제가 발생했습니다. Lighthouse를 다시 실행하세요. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "초기 디버거 프로토콜 연결 대기 시간이 초과되었습니다."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome이 페이지 로드 중 스크린샷을 수집하지 않았습니다. 페이지에 표시된 콘텐츠가 있는지 확인한 다음 Lighthouse를 다시 실행해 보세요. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS 서버에서 제공된 도메인을 해결하지 못했습니다."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "필수 {artifactName} 수집기에 다음 오류가 발생했습니다. {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Chrome 내부 오류가 발생했습니다. Chrome을 다시 시작한 다음 Lighthouse를 다시 실행해 주세요."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "필수 {artifactName} 수집기가 실행되지 않았습니다."}, "core/lib/lh-error.js | noFcp": {"message": "페이지에서 콘텐츠를 페인트하지 않았습니다. 로드 중 포그라운드에서 브라우저 창이 실행되었는지 확인 후 다시 시도해 보세요. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "최대 콘텐츠 렌더링 시간(LCP)에 해당하는 콘텐츠가 페이지에 표시되지 않았습니다. 페이지에 유효한 LCP 요소가 있는지 확인한 후 다시 시도하세요. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "입력한 페이지가 HTML이 아닙니다(MIME {mimeType} 유형으로 게시됨)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "이 Chrome 버전은 너무 오래되어 '{featureName}'을(를) 지원할 수 없습니다. 전체 결과를 보려면 최신 버전을 사용하세요."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse에서 사용자가 요청한 페이지를 안정적으로 로드하지 못했습니다. 올바른 URL을 테스트하고 있으며 서버에서 모든 요청에 적절하게 응답하고 있는지 확인하세요."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "페이지 응답이 중지되었기 때문에 Lighthouse에서 사용자가 요청한 URL을 안정적으로 로드하지 못했습니다."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "제공한 URL에 유효한 보안 인증서가 포함되어 있지 않습니다. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome에서 전면 광고가 있는 페이지를 로드하지 못하도록 했습니다. 올바른 URL로 테스트하고 있으며 서버가 모든 요청에 적절하게 응답하고 있는지 확인하세요."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse에서 사용자가 요청한 페이지를 안정적으로 로드하지 못했습니다. 올바른 URL로 테스트하고 있으며 서버가 모든 요청에 적절하게 응답하고 있는지 확인하세요. (세부정보: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse에서 사용자가 요청한 페이지를 안정적으로 로드하지 못했습니다. 올바른 URL로 테스트하고 있으며 서버가 모든 요청에 적절하게 응답하고 있는지 확인하세요. (상태 코드: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "페이지 로드에 시간이 너무 오래 걸립니다. 보고서의 추천에 따라 페이지 로드 시간을 줄인 다음 Lighthouse를 다시 실행해 보세요. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools 프로토콜 응답 대기가 할당된 시간을 초과했습니다. (메서드: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "리소스 콘텐츠 가져오기 시간이 할당된 시간을 초과했습니다"}, "core/lib/lh-error.js | urlInvalid": {"message": "사용자가 제공한 URL이 잘못되었습니다."}, "core/lib/navigation-error.js | warningXhtml": {"message": "페이지 MIME 유형이 XHTML입니다. Lighthouse는 이 문서 유형을 명시적으로 지원하지 않습니다."}, "core/user-flow.js | defaultFlowName": {"message": "사용자 플로우({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "탐색 보고서({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "스냅샷 보고서({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "기간 보고서({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "모든 보고서"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "카테고리"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "접근성"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "권장사항"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "성능"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "프로그레시브 웹 앱"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "검색엔진 최적화"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "데스크톱"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Lighthouse 플로우 보고서 이해"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "플로우 이해"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "탐색 보고서 사용 용도…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "스냅샷 보고서 사용 용도…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "기간 보고서 사용 용도…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Lighthouse 성능 점수를 받습니다."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "최대 콘텐츠 렌더링 시간 및 속도 색인과 같은 페이지 로드 성능 측정항목을 측정합니다."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "프로그레시브 웹 앱 기능을 평가합니다."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "단일 페이지 애플리케이션 또는 복잡한 양식에서 접근성 문제를 찾습니다."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "상호작용 뒤에 숨겨진 메뉴 및 UI 요소 관련 권장사항을 평가합니다."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "일련의 상호작용에서 레이아웃 이동 및 자바스크립트 실행 시간을 측정합니다."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "장기 지속되는 페이지 및 단일 페이지 애플리케이션 관련 경험을 개선할 수 있는 성능 기회를 탐색합니다."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "가장 큰 효과"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{정보 감사 {numInformative}개}other{정보 감사 {numInformative}개}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "모바일"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "페이지 로드"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "탐색 보고서는 기존 Lighthouse 보고서와 완전히 동일하게 단일 페이지 로드를 분석합니다."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "탐색 보고서"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{탐색 보고서 {numNavigation}개}other{탐색 보고서 {numNavigation}개}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{통과 가능한 감사 {numPassableAudits}개}other{통과 가능한 감사 {numPassableAudits}개}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{통과한 감사 {numPassed}개}other{통과한 감사 {numPassed}개}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "평균"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "오류"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "나쁨"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "좋음"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "저장"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "캡처된 페이지 상태"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "스냅샷 보고서는 특정 상태의 페이지, 특히 사용자 상호작용 후 페이지를 분석합니다."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "스냅샷 보고서"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{스냅샷 보고서 {numSnapshot}개}other{스냅샷 보고서 {numSnapshot}개}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "요약"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "사용자 상호작용"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "기간 보고서는 일반적으로 사용자 상호작용을 포함하는 임의 기간을 분석합니다."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "기간 보고서"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{기간 보고서 {numTimespan}개}other{기간 보고서 {numTimespan}개}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse 사용자 플로우 보고서"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "애니메이션 콘텐츠의 경우 [`amp-anim`](https://amp.dev/documentation/components/amp-anim/)을 사용하여 콘텐츠가 화면에 표시되지 않을 때 CPU 사용량을 최소화하세요."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "WebP 형식의 모든 [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 구성요소를 표시하면서 다른 브라우저의 적절한 대체 텍스트를 지정해 보세요. [자세히 알아보기](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "이미지를 자동으로 지연 로드하는 [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)를 사용하고 있는지 확인하세요. [자세히 알아보기](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[AMP 최적화 도구](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)와 같은 도구를 사용하여 [서버 측에서 AMP 레이아웃을 렌더링](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)합니다."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "[AMP 문서](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/)를 참고하여 모든 스타일이 지원되는지 확인하세요."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 구성요소는 [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) 속성을 지원하여 화면 크기를 바탕으로 어떤 이미지 애셋을 사용할지 지정합니다. [자세히 알아보기](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "너무 큰 목록을 렌더링하는 경우 구성요소 개발자 키트(CDK)로 가상 스크롤을 고려해 보세요. [자세히 알아보기](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "[경로 수준 코드 분할](https://web.dev/route-level-code-splitting-in-angular/)을 적용하여 자바스크립트 번들의 크기를 최소화하세요. 또한 [Angular 서비스 워커](https://web.dev/precaching-with-the-angular-service-worker/)로 애셋을 미리 캐시해 보세요."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Angular CLI를 사용하는 경우 빌드가 프로덕션 모드에서 생성되었는지 확인하세요. [자세히 알아보기](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Angular CLI를 사용하는 경우 소스 맵을 프로덕션 빌드에 포함하여 번들을 검사하세요. [자세히 알아보기](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "미리 경로를 로드하여 탐색 속도를 높입니다. [자세히 알아보기](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "구성요소 개발자 키트(CDK)에서 `BreakpointObserver` 유틸리티를 사용하여 이미지 중단점을 관리해 보세요. [자세히 알아보기](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "GIF를 HTML5 동영상으로 삽입할 수 있게 해 주는 서비스에 GIF를 업로드해 보세요."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "테마에서 맞춤 글꼴을 정의할 때 `@font-display`를 지정합니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "사이트에서 [Convert 이미지 스타일로 WebP 이미지 형식](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)을 구성해 보세요."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "이미지를 지연 로드할 수 있는 [Drupal 모듈](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search)을 설치하세요. 이러한 모듈은 오프스크린 이미지를 지연하여 성능을 개선해 줍니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "모듈을 사용하여 중요 CSS 및 자바스크립트를 인라인으로 표시해 보세요. 또는 [고급 CSS/JS 집계](https://www.drupal.org/project/advagg) 모듈과 같은 자바스크립트를 통해 애셋을 비동기식으로 로드할 수 있습니다. 이 모듈에서 제공하는 최적화로 인해 사용 중인 사이트가 제대로 작동하지 않을 수 있으므로 코드를 변경해야 할 가능성이 큽니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "테마, 모듈, 서버 사양은 모두 서버 응답 시간에 영향을 미칩니다. 더욱 최적화된 테마를 찾고, 최적화 모듈을 신중하게 선택하고, 서버를 업그레이드해 보세요. 호스팅 서버에서는 PHP opcode 캐싱 및 데이터베이스 쿼리 시간을 단축하기 위한 Redis나 Memcached 등의 메모리 캐싱을 사용하고, 페이지를 더 빠르게 준비하기 위해 최적화된 애플리케이션 로직을 사용해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "페이지에서 로드되는 이미지의 크기를 줄이려면 [반응형 이미지 스타일](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)을 사용해 보세요. 페이지에 여러 콘텐츠 항목을 표시하는 데 Views를 사용하고 있다면 페이지로 나누기를 구현하여 주어진 페이지에 표시되는 콘텐츠 항목의 수를 제한하는 것이 좋습니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "'Administration(관리) » Configuration(구성) » Development(개발)' 페이지에서 'Aggregate CSS files(CSS 파일 집계)'를 사용 설정했는지 확인합니다. [추가 모듈](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search)을 통해 고급 집계 옵션을 구성하면 CSS 스타일을 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "'Administration(관리) » Configuration(구성) » Development(개발)' 페이지에서 'Aggregate JavaScript files(자바스크립트 파일 집계)'를 사용 설정했는지 확인합니다. [추가 모듈](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search)을 통해 고급 집계 옵션을 구성하면 자바스크립트 애셋을 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "미사용 CSS 규칙을 삭제하는 방안을 고려하고, 관련 페이지 또는 페이지의 구성요소에 필요한 Drupal 라이브러리만 연결합니다. 자세한 정보는 [Drupal 문서 링크](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)를 참고하세요. 연결된 라이브러리 중 불필요한 CSS를 추가하는 라이브러리를 확인하려면 Chrome DevTools의 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행해 보세요. Drupal 사이트에서 CSS 집계가 사용 중지되어 있으면 스타일시트의 URL에서 원인이 되는 테마 또는 모듈을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 많이 보이는 스타일시트가 여러 개 있는 테마 또는 모듈을 찾아보세요. 테마 또는 모듈은 페이지에서 실제 사용되는 스타일시트만 대기열에 포함해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "미사용 자바스크립트 애셋을 삭제하는 방안을 고려하고, 관련 페이지 또는 페이지의 구성요소에 필요한 Drupal 라이브러리만 연결하세요. 자세한 정보는 [Drupal 문서 링크](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)를 참고하세요. 연결된 라이브러리 중 불필요한 자바스크립트를 추가하는 라이브러리를 확인하려면 Chrome DevTools의 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행해 보세요. Drupal 사이트에서 자바스크립트 집계가 사용 중지된 경우 스크립트의 URL에서 원인이 되는 테마 또는 모듈을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 많이 보이는 스크립트가 여러 개 있는 테마 또는 모듈을 찾아보세요. 테마 또는 모듈은 페이지에서 실제 사용되는 스크립트만 대기열에 포함해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "'Administration(관리) » Configuration(구성) » Development(개발)' 페이지에서 'Browser and proxy cache maximum age(브라우저 및 프록시 캐시 최대 기간)'를 설정합니다. [Drupal 캐시 및 성능 최적화](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)에 관해 읽어 보세요."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "품질은 유지하면서 사이트를 통해 업로드하는 이미지의 크기를 자동으로 최적화하고 줄여 주는 [모듈](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search)을 사용해 보세요. 또한 사이트에서 렌더링된 모든 이미지에 Drupal(Drupal 8 이상에서 사용 가능)에서 제공한 네이티브 [반응형 이미지 스타일](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)을 사용 중인지도 확인합니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "사용자 에이전트 리소스 힌트 기능을 제공하는 [모듈](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search)을 설치하고 구성하여 사전 연결 또는 DNS 미리 가져오기 리소스 힌트를 추가할 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Drupal(Drupal 8 이상에서 사용 가능)에서 제공한 네이티브 [반응형 이미지 스타일](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)을 사용 중인지 확인합니다. 보기 모드, 보기 또는 WYSIWYG 편집기를 통해 업로드된 이미지로 이미지 필드를 렌더링할 경우 반응형 이미지 스타일을 사용합니다."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Optimize Fonts`를 사용 설정하여 `font-display` CSS 기능을 자동으로 활용하고 웹폰트를 로드하는 중에 사용자가 텍스트를 볼 수 있게 하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Next-Gen Formats`를 사용 설정하여 이미지를 WebP로 전환하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Lazy Load Images`를 사용 설정하여 필요할 때까지 화면 밖 이미지 로드를 연기하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Critical CSS` 및 `Script Delay`를 사용 설정하여 중요하지 않은 JS/CSS를 연기하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "[Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching)을 사용하여 전 세계 네트워크에서 콘텐츠를 캐시하고 첫 바이트 소요 시간을 개선하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Minify CSS`를 사용 설정하여 CSS를 자동으로 축소하고 네트워크 페이로드 크기를 줄이세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Minify Javascript`를 사용 설정하여 JS를 자동으로 축소하여 네트워크 페이로드 크기를 줄이세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Remove Unused CSS`를 사용 설정하여 이 문제를 해결하도록 도와주세요. 사이트의 각 페이지에 실제로 사용되는 CSS 클래스를 식별하고 나머지를 삭제하여 파일 크기를 작게 유지합니다."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Efficient Static Cache Policy`를 사용 설정하여 고정 애셋의 캐싱 헤더에서 권장 값을 설정하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Next-Gen Formats`를 사용 설정하여 이미지를 WebP로 전환하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Pre-Connect Origins`를 사용 설정하여 `preconnect` 리소스 힌트를 자동으로 추가하고 중요한 서드 파티 원본에 관한 조기 연결을 수립하세요."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Preload Fonts` 및 `Preload Background Images`를 사용 설정하여 `preload` 링크를 추가하고 현재 페이지 로드에서 나중에 요청되는 리소스 가져오기의 우선순위를 지정합니다."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "[Ezoic Leap](https://pubdash.ezoic.com/speed)을 사용하고 `Resize Images`를 사용 설정하여 기기에 적절한 크기로 이미지 크기를 조절하고 네트워크 페이로드 크기를 줄이세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "GIF를 HTML5 동영상으로 삽입할 수 있게 해 주는 서비스에 GIF를 업로드해 보세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "업로드한 이미지를 최적화된 양식으로 자동 변환해주는 [플러그인](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) 또는 서비스를 사용하는 것이 좋습니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "[지연 로드 Joomla 플러그인](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading)을 설치하면 모든 오프스크린 이미지를 지연하거나 이 기능을 제공하는 템플릿으로 전환할 수 있습니다. Joomla 4.0부터 모든 새 이미지가 [자동으로](https://github.com/joomla/joomla-cms/pull/30748) 코어로부터 `loading` 속성을 받게 됩니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "[중요한 애셋을 인라인으로 로드](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)하거나 [덜 중요한 리소스를 지연](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)할 수 있게 도와주는 다양한 Joomla 플러그인이 있습니다. 이러한 플러그인에서 제공하는 최적화로 인해 사용 중인 템플릿 또는 플러그인의 기능이 제대로 작동하지 않을 수 있으므로 신중하게 테스트해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "템플릿, 확장 프로그램, 서버 사양은 모두 서버 응답 시간에 영향을 미칩니다. 더욱 최적화된 템플릿을 찾고, 최적화 확장 프로그램을 신중하게 선택하고, 서버를 업그레이드해 보세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "도움말 카테고리에 발췌문을 표시하거나(예: 더 읽어 보기 링크 사용), 특정 페이지에 표시되는 도움말의 개수를 줄이거나, 길이가 긴 게시물을 여러 페이지로 나누거나, 플러그인을 사용하여 댓글을 지연 로드해 보세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "다양한 [Jo<PERSON><PERSON> 확장 프로그램](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)을 사용해 스타일을 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다. 이 기능을 제공하는 템플릿도 있습니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "다양한 [Jo<PERSON><PERSON> 확장 프로그램](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)을 사용해 스크립트를 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다. 이 기능을 제공하는 템플릿도 있습니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "페이지에서 사용되지 않는 CSS를 로드하는 [Joomla 확장 프로그램](https://extensions.joomla.org/) 개수를 줄이거나 전환하는 것이 좋습니다. 불필요한 CSS를 추가하는 확장 프로그램을 확인하려면 Chrome DevTools의 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행해 보세요. 스타일시트의 URL에서 원인이 되는 테마 또는 플러그인을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 많이 보이는 스타일시트가 여러 개 있는 플러그인을 찾아보세요. 플러그인은 페이지에서 실제 사용되는 스타일시트만 대기열에 포함해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "페이지에서 사용되지 않는 자바스크립트를 로드하는 [Joomla 확장 프로그램](https://extensions.joomla.org/) 개수를 줄이거나 전환하는 것이 좋습니다. 불필요한 JS를 추가하는 플러그인을 확인하려면 Chrome DevTools의 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행해 보세요. 스크립트의 URL에서 원인이 되는 확장 프로그램을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 많이 보이는 스크립트가 여러 개 있는 확장 프로그램을 찾아보세요. 확장 프로그램은 페이지에서 실제 사용되는 스크립트만 대기열에 포함해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[Joomla 브라우저 캐싱](https://docs.joomla.org/Cache)에 관해 자세히 알아보세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "품질은 유지하면서 이미지를 압축해 주는 [이미지 최적화 플러그인](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)을 사용해 보세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "콘텐츠에 반응형 이미지를 사용하려면 [반응형 이미지 플러그인](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)을 사용해 보세요."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Joomla(System(시스템) > Global configuration(전역 구성) > Server(서버))에서 Gzip 페이지 압축을 사용 설정하여 텍스트 압축을 사용할 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "자바스크립트 애셋을 번들로 구성하지 않는 경우 [baler](https://github.com/magento/baler)를 사용해 보세요."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Magento의 내장 [자바스크립트 번들 구성 및 축소](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)를 사용 중지하고 대신 [baler](https://github.com/magento/baler/)를 사용하세요."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[맞춤 글꼴을 정의](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)할 때 `@font-display`을(를) 지정합니다."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "[Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp)에서 새 이미지 형식을 활용하는 다양한 타사 확장 프로그램을 검색해 보세요."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "제품과 카탈로그 템플릿을 수정하여 웹 플랫폼의 [지연 로드](https://web.dev/native-lazy-loading) 기능을 활용해 보세요."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Magento의 [Varnish 통합](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)을 사용합니다."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "스토어의 개발자 설정에서 'CSS 파일 축소' 옵션을 사용 설정하세요. [자세히 알아보기](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "[Terser](https://www.npmjs.com/package/terser)를 사용하여 정적 콘텐츠 배포에서 모든 자바스크립트 애셋을 축소하고 내장 축소 기능을 사용 중지합니다."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Magento의 내장 [자바스크립트 번들 구성](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)을 사용 중지합니다."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "[Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image)에서 이미지를 최적화하는 다양한 타사 확장 프로그램을 검색해 보세요."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "[테마의 레이아웃을 수정](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)하여 사전 연결 또는 DNS 프리페치 리소스 힌트를 추가할 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "[테마의 레이아웃을 수정](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)하여 `<link rel=preload>` 태그를 추가할 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "`<img>` 대신 `next/image` 구성요소를 사용해 이미지 형식을 자동으로 최적화하세요. [자세히 알아보기](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "`<img>` 대신 `next/image` 구성요소를 사용해 이미지 로드가 자동으로 지연되게 하세요. [자세히 알아보기](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "`next/image` 구성요소를 사용하고 '우선순위'를 True로 설정하여 LCP 이미지를 미리 로드하세요. [자세히 알아보기](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "`next/script` 구성요소를 사용하여 중요하지 않은 타사 스크립트의 로드를 지연하세요. [자세히 알아보기](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "`next/image` 구성요소를 사용해 이미지 크기가 항상 적절하게 조정되도록 하세요. [자세히 알아보기](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "`Next.js` 구성에 `PurgeCSS`를 설정하여 스타일시트에서 사용되지 않는 규칙을 삭제해 보세요. [자세히 알아보기](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "`Webpack Bundle Analyzer`를 사용하여 사용되지 않는 자바스크립트 코드를 감지하세요. [자세히 알아보기](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "`Next.js Analytics`를 사용하여 앱의 실제 성능을 측정해 보세요. [자세히 알아보기](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "변경 불가능한 애셋 및 `Server-side Rendered`(SSR) 페이지의 캐싱을 구성하세요. [자세히 알아보기](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "`<img>` 대신 `next/image` 구성요소를 사용해 이미지 품질을 조정하세요. [자세히 알아보기](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "`next/image` 구성요소를 사용해 적절한 `sizes`를 설정하세요. [자세히 알아보기](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Next.js 서버에서 압축 기능을 사용하세요. [자세히 알아보기](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "`nuxt/image` 구성요소를 사용해 `format=\"webp\"` 형식을 설정하세요. [자세히 알아보기](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "`nuxt/image` 구성요소를 사용해 오프스크린 이미지에 대해 `loading=\"lazy\"`를 설정하세요. [자세히 알아보기](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "`nuxt/image` 구성요소를 사용해 LCP 이미지의 `preload`를 지정하세요. [자세히 알아보기](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "`nuxt/image` 구성요소를 사용해 명시적인 `width` 및 `height`를 지정하세요. [자세히 알아보기](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "`nuxt/image` 구성요소를 사용해 적절한 `quality`을 설정하세요. [자세히 알아보기](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "`nuxt/image` 구성요소를 사용해 적절한 `sizes`를 설정하세요. [자세히 알아보기](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "빠른 웹페이지 로딩을 위해서는 [애니메이션 GIF를 동영상으로 대체](https://web.dev/replace-gifs-with-videos/)하고 [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) 또는 [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder)과 같은 최신 파일 형식을 사용하여 압축 효율성을 현재 최첨단으로 여겨지는 동영상 코덱 VP9 대비 30% 이상 높여 보세요."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "업로드한 이미지를 최적화된 형식으로 자동 변환해 주는 [플러그인](https://octobercms.com/plugins?search=image) 또는 서비스를 사용해 보세요. [WebP 무손실 이미지](https://developers.google.com/speed/webp)는 동등한 SSIM 품질 색인에서 PNG 이미지보다 26% 더 작고, JPEG 이미지보다 25~34% 더 작습니다. 고려해 볼 만한 또 다른 차세대 이미지 형식으로는 [AVIF](https://jakearchibald.com/2020/avif-has-landed/)가 있습니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "[이미지 지연 로드 플러그인](https://octobercms.com/plugins?search=lazy)을 설치하면 모든 오프스크린 이미지를 지연하거나 이 기능을 제공하는 테마로 전환할 수 있습니다. [AMP 플러그인](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) 사용도 고려해 보세요."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "[중요한 애셋을 인라인하게 로드](https://octobercms.com/plugins?search=css)할 수 있도록 도와주는 다양한 플러그인이 있습니다. 이러한 플러그인으로 인해 다른 플러그인이 작동하지 않을 수 있으니 신중하게 테스트해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "테마, 플러그인, 서버 사양은 모두 서버 응답 시간을 길어지게 만듭니다. 더욱 최적화된 테마를 찾고, 최적화 플러그인을 신중하게 선택하고, 서버를 업그레이드해 보세요. October CMS는 또한 개발자가 [`Queues`](https://octobercms.com/docs/services/queues)을(를) 사용해 이메일 전송과 같이 시간이 많이 소모되는 작업 처리를 지연하도록 도와줍니다. 이는 웹 요청 속도를 크기 높여줍니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "게시물 목록에 발췌문을 표시하거나(예: `show more` 사용), 특정 웹페이지에 표시되는 게시물의 개수를 줄이거나, 길이가 긴 게시물을 여러 웹페이지로 나누거나, 플러그인을 사용하여 댓글을 지연 로드하는 것이 좋습니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "다양한 [플러그인](https://octobercms.com/plugins?search=css)을 사용해 스타일을 연결, 축소, 압축함으로써 웹사이트 속도를 높일 수 있습니다. 빌드 프로세스로 축소 작업을 미리 실행하면 개발 속도를 높일 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "다양한 [플러그인](https://octobercms.com/plugins?search=javascript)을 사용해 스크립트를 연결, 축소, 압축함으로써 웹사이트 속도를 높일 수 있습니다. 빌드 프로세스로 축소 작업을 미리 실행하면 개발 속도를 높일 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "웹사이트에서 사용되지 않는 CSS를 로드하는 [플러그인](https://octobercms.com/plugins)을 검토해 보세요. 불필요한 CSS를 추가하는 플러그인을 확인하려면 Chrome Devtools에서 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행하세요. 스타일시트 URL에서 원인이 되는 테마 또는 플러그인을 확인하세요. 코드 범위에 빨간색이 많이 보이는 스타일시트가 여러 개 있는 플러그인을 찾아보세요. 플러그인은 웹페이지에서 실제 사용되는 스타일시트만 추가해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "웹페이지에서 사용되지 않은 자바스크립트를 로드하는 [플러그인](https://octobercms.com/plugins?search=javascript)을 검토해 보세요. 불필요한 자바스크립트를 추가시키는 플러그인을 확인하려면 Chrome Devtools에서 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행하세요. 스크립트의 URL에서 이러한 현상을 유발하는 테마나 플러그인을 확인하세요. 코드 적용 범위에 빨간색이 많이 보이는 스크립트가 여러 개 있는 플러그인을 찾아보세요. 플러그인은 웹페이지에서 실제 사용되는 스크립트만 추가해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[HTTP 캐시를 이용한 불필요한 네트워크 요청 예방](https://web.dev/http-cache/#caching-checklist)에 관해 알아보세요. 다양한 [플러그인](https://octobercms.com/plugins?search=Caching)을 사용하여 캐싱 속도를 높일 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "품질은 유지하면서 이미지를 압축해 주는 [이미지 최적화 플러그인](https://octobercms.com/plugins?search=image)을 사용해 보세요."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "필수 이미지 크기를 사용할 수 있도록 미디어 매니저에서 직접 이미지를 업로드하세요. [크기 조절 필터](https://octobercms.com/docs/markup/filter-resize) 또는 [이미지 크기 조절 플러그인](https://octobercms.com/plugins?search=image)을 사용하여 최적의 이미지 크기를 사용하는지 확인하세요."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "웹 서버 구성에서 텍스트 압축을 사용 설정하세요."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "페이지에서 많은 반복 구성요소를 렌더링하는 경우 `react-window`와 같은 '윈도윙' 라이브러리를 사용하여 생성된 DOM 노드의 수를 최소화해 보세요. [자세히 알아보기](https://web.dev/virtualize-long-lists-react-window/) 또한 [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) 또는 [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo)를 사용하여 불필요한 재렌더링을 최소화하고 `Effect` 후크를 사용하는 경우 특정 종속 항목이 변경될 때까지만 [효과를 건너뛰어](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) 런타임 성능을 개선하세요."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "React Router를 사용하는 경우 [경로 탐색](https://reacttraining.com/react-router/web/api/Redirect)의 `<Redirect>` 구성요소 사용량을 축소합니다."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "서버 측에서 React 구성요소를 렌더링하는 경우 `renderToPipeableStream()` 또는 `renderToStaticNodeStream()`을(를) 사용하여 클라이언트가 모든 마크업이 아닌 마크업의 서로 다른 부분을 받아서 하이드레이션할 수 있도록 허용해 보세요. [자세히 알아보기](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "빌드 시스템에서 CSS 파일을 자동으로 축소하는 경우 애플리케이션의 프로덕션 빌드를 배포하고 있는지 확인하세요. 이는 React 개발자 도구 확장 프로그램으로 확인할 수 있습니다. [자세히 알아보기](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "빌드 시스템에서 JS 파일을 자동으로 축소하는 경우 애플리케이션의 프로덕션 빌드를 배포하고 있는지 확인하세요. 이는 React 개발자 도구 확장 프로그램으로 확인할 수 있습니다. [자세히 알아보기](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "서버 측에서 렌더링하지 않는 경우 `React.lazy()`(으)로 [자바스크립트 번들을 분할](https://web.dev/code-splitting-suspense/)해 보세요. 또는 [로드 가능한 구성요소](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)와 같은 타사 라이브러리를 사용하여 코드를 분할하세요."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Profiler API를 활용하는 React 개발자 도구 프로파일러를 사용하여 구성요소의 렌더링 성능을 측정합니다. [자세히 알아보기](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "GIF를 HTML5 동영상으로 삽입할 수 있게 해 주는 서비스에 GIF를 업로드해 보세요."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "지원되는 경우 어디서나 [Performance Lab](https://wordpress.org/plugins/performance-lab/) 플러그인을 사용하여 업로드된 JPEG 이미지를 WebP로 자동 변환해 보세요."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "[지연 로드 WordPress 플러그인](https://wordpress.org/plugins/search/lazy+load/)을 설치하면 화면에 모든 오프스크린 이미지를 지연하거나 이 기능을 제공하는 테마로 전환할 수 있습니다. [AMP 플러그인](https://wordpress.org/plugins/amp/) 사용도 고려해 보세요."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "[중요한 애셋을 인라인](https://wordpress.org/plugins/search/critical+css/)하거나 [덜 중요한 리소스를 지연](https://wordpress.org/plugins/search/defer+css+javascript/)할 수 있게 도와주는 다양한 WordPress 플러그인이 있습니다. 이러한 플러그인에서 제공하는 최적화로 인해 내가 사용하는 테마 또는 플러그인의 기능이 깨질 수 있으므로 코드를 변경해야 할 가능성이 큽니다."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "테마, 플러그인, 서버 사양은 모두 서버 응답 시간을 길어지게 만듭니다. 보다 최적화된 테마를 찾고, 최적화 플러그인을 신중하게 선택하고, 서버 업그레이드를 고려해 보세요."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "게시물 목록에 발췌문을 표시하거나(예: 더 많은 태그 사용), 특정 페이지에 표시되는 게시물의 개수를 줄이거나, 길이가 긴 게시물을 여러 페이지로 나누거나, 플러그인을 사용하여 댓글을 지연 로드하는 것이 좋습니다."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "다양한 [WordPress 플러그인](https://wordpress.org/plugins/search/minify+css/)을 사용해 스타일을 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다. 또한 가능한 경우 빌드 프로세스를 사용하여 축소 작업을 미리 실행할 수도 있습니다."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "다양한 [WordPress 플러그인](https://wordpress.org/plugins/search/minify+javascript/)을 사용해 스크립트를 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다. 또한 가능한 경우 빌드 프로세스를 사용하여 축소 작업을 미리 실행할 수도 있습니다."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "페이지에서 사용되지 않는 CSS를 로드하는 [WordPress 플러그인](https://wordpress.org/plugins/) 개수를 줄이거나 전환하는 것이 좋습니다. 과도한 CSS를 부가하는 플러그인이 무엇인지 확인하려면 Chrome DevTools의 [코드 범위](https://developer.chrome.com/docs/devtools/coverage/)를 실행해 보세요. 스타일시트의 URL에서 관련된 테마 또는 플러그인을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 길게 표시된 스타일시트가 있는 플러그인을 찾아보세요. 실제로 페이지에서 사용되는 플러그인은 대기열에 하나의 스타일시트만 포함해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "페이지에서 사용되지 않는 JavaScript를 로드하는 [WordPress 플러그인](https://wordpress.org/plugins/) 개수를 줄이거나 전환하는 것이 좋습니다. 과도한 JS를 부가하는 플러그인이 무엇인지 확인하려면 Chrome DevTools의 [코드 범위](https://developer.chrome.com/docs/devtools/coverage/)를 실행해 보세요. 스크립트의 URL에서 관련 테마 또는 플러그인을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 길게 표시된 스크립트가 있는 플러그인을 찾아보세요. 실제로 페이지에서 사용되는 플러그인은 대기열에 하나의 스크립트만 포함해야 합니다."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "[WordPress 브라우저 캐싱](https://wordpress.org/support/article/optimization/#browser-caching)에 관해 자세히 알아보세요."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "품질은 그대로 유지하면서 이미지를 압축해주는 [이미지 최적화 WordPress 플러그인](https://wordpress.org/plugins/search/optimize+images/)을 사용해 보세요."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "필수 이미지 크기를 사용할 수 있도록 [미디어 라이브러리](https://wordpress.org/support/article/media-library-screen/)에서 직접 이미지를 업로드하세요. 그런 다음 미디어 라이브러리에서 이미지를 삽입하거나 이미지 위젯을 사용하여 최적의 이미지 크기를 사용하는지 확인하세요(반응형 중단점용 이미지 포함). 이미지 크기가 용도에 적합하지 않다면 `Full Size` 이미지는 사용하지 않는 것이 좋습니다. [자세히 알아보기](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "웹 서버 구성에서 텍스트 압축을 사용 설정할 수 있습니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "이미지를 WebP로 변환하려면 'WP Rocket'의 Image Optimization(이미지 최적화) 탭에서 'Imagify'를 사용 설정하세요."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "이 권장사항을 수정하려면 WP Rocket의 [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images)를 사용 설정하세요. 이 기능은 방문자가 페이지를 아래로 스크롤해서 실제로 확인해야 할 때까지 이미지 로드를 지연시킵니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "'WP Rocket'의 [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css)(사용하지 않는 CSS 삭제) 및 [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred)(지연된 자바스크립트 로드)를 사용 설정하여 이 권장사항을 해결하세요. 이러한 기능은 CSS 및 자바스크립트 파일을 각각 최적화하여 페이지 렌더링을 차단하지 않도록 합니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "이 문제를 해결하려면 'WP Rocket'의 [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine)(CSS 파일 압축)를 사용 설정하세요. 파일 크기가 작아지고 다운로드 속도가 빨라지도록 사이트 CSS 파일의 모든 공백 및 댓글이 삭제됩니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "이 문제를 해결하려면 'WP Rocket'의 [Minify JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine)(자바스크립트 파일 압축)를 사용 설정하세요. 크기가 작아지고 다운로드 속도가 빨라지도록 자바스크립트 파일의 공백 및 댓글이 삭제됩니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "이 문제를 해결하려면 'WP Rocket'의 [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css)(사용되지 않는 CSS 삭제)를 사용 설정하세요. 각 페이지에 사용된 CSS만 유지하고 사용되지 않는 모든 CSS와 스타일시트를 삭제하여 페이지 크기를 줄입니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "이 문제를 해결하려면 'WP Rocket'에서 [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution)(자바스크립트 실행 지연)을 사용 설정하세요. 사용자가 상호작용할 때까지 스크립트 실행을 지연하여 페이지 로드를 개선합니다. 사이트에 iframe이 있는 경우 WP Rocket에서 [LazyLoad for iframes and videos ](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos)(iframe 및 동영상용 LazyLoad) 및 [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)(YouTube iframe을 미리보기 이미지로 대체)를 사용할 수도 있습니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "이미지를 압축하려면 'WP Rocket'의 Image Optimization(이미지 최적화) 탭에서 'Imagify'를 사용 설정하고 Bulk Optimization(일괄 최적화)을 실행하세요."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "'WP Rocket'에서 [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests)(DNS 요청 미리 가져오기)를 사용하여 'dns-prefetch'를 추가하고 외부 도메인과의 연결을 가속화합니다. 또한 'WP Rocket'은 [Google Fonts domain](https://docs.wp-rocket.me/article/1312-optimize-google-fonts)(Google Fonts 도메인) 및 [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)(CDN 사용 설정) 기능을 통해 추가된 CNAME에 'preconnect'를 자동으로 추가합니다."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "글꼴 관련 문제를 해결하려면 'WP Rocket'의 [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css)(사용되지 않는 CSS 삭제)를 사용 설정하세요. 사이트의 주요 글꼴은 우선적으로 미리 로드됩니다."}, "report/renderer/report-utils.js | calculatorLink": {"message": "계산기 보기"}, "report/renderer/report-utils.js | collapseView": {"message": "뷰 접기"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "초기 탐색"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "최상 경로 최대 지연 시간:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSON 복사"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "어두운 테마 전환"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "인쇄 확장됨"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "인쇄 요약"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Gist로 저장"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "HTML로 저장"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "JSON으로 저장"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "뷰어에서 열기"}, "report/renderer/report-utils.js | errorLabel": {"message": "오류!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "보고 오류: 감사 정보 없음"}, "report/renderer/report-utils.js | expandView": {"message": "뷰 펼치기"}, "report/renderer/report-utils.js | footerIssue": {"message": "문제 신고"}, "report/renderer/report-utils.js | hide": {"message": "숨기기"}, "report/renderer/report-utils.js | labDataTitle": {"message": "실험실 데이터"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "에뮬레이션된 모바일 네트워크에서 분석한 현재 페이지의 [Lighthouse](https://developers.google.com/web/tools/lighthouse/) 결과입니다. 값은 추정치이며 달라질 수 있습니다."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "직접 확인해야 하는 추가 항목"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "해당 사항 없음"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "추천"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "예상 절감치"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "통과한 감사"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "초기 페이지 로드"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "맞춤형 제한"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "에뮬레이션된 데스크톱"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "에뮬레이션 없음"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe 버전"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "제한되지 않은 CPU/메모리 성능"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU 제한"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "기기"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "네트워크 제한"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "화면 에뮬레이션"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "사용자 에이전트(네트워크)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "단일 페이지 로드"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "여러 세션을 요약하는 필드 데이터와는 반대로 이 데이터는 단일 페이지 로드에서 가져옵니다."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "느린 4G 제한"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "알 수 없음"}, "report/renderer/report-utils.js | show": {"message": "표시"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "다음과 관련된 감사 표시:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "스니펫 접기"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "스니펫 펼치기"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "타사 리소스 표시"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "런타임 환경을 통해 제공"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Lighthouse 실행에 영향을 미치는 문제가 발생했습니다."}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "값은 추정치이며 달라질 수 있습니다. 이러한 측정항목에서 [성능 점수가 직접 계산됩니다](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "출처 흔적 보기"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "흔적 보기"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "트리맵 보기"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "감사를 통과했으나 경고를 받음"}, "report/renderer/report-utils.js | warningHeader": {"message": "경고: "}, "treemap/app/src/util.js | allLabel": {"message": "전체"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "모든 스크립트"}, "treemap/app/src/util.js | coverageColumnName": {"message": "범위"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "중복 모듈"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "리소스 바이트"}, "treemap/app/src/util.js | tableColumnName": {"message": "이름"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "표 전환"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "사용하지 않은 바이트"}}