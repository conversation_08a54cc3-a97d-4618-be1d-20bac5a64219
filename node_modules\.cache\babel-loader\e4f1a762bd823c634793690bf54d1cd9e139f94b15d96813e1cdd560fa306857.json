{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\movimentazioneInUscita.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ResponsableJobsUscita - operazioni sulle lavorazioni in uscita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaOperatore from \"./selezionaOperatore\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass MovimentazioneInUscita extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      var url = 'documents?idWarehouses=' + e.value + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      globalFilter: null,\n      deleteResultDialog: false,\n      loading: true,\n      selectedWarehouse: null,\n      selectedRetailer: null,\n      selectedDocuments: null,\n      displayed: false,\n      clienti: null,\n      idEmployee: 0,\n      totalRecords: 0,\n      opMag: '',\n      mex: '',\n      firstName: '',\n      address: '',\n      indFatt: '',\n      search: '',\n      param: '?idWarehouses=',\n      param2: '&idRetailer=',\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.retailers = [];\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.opMag = [];\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog3: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'retailers/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.retailers.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = '';\n    if (this.state.selectedDocuments) {\n      message = \"Trasmissione multipla documenti\";\n    } else {\n      message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\"\n      }).format(new Date(result.documentDate));\n    }\n    var opMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        var taskAss = 0;\n        taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n        opMag.push({\n          label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n          value: element.idemployee\n        });\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: this.state.selectedDocuments ? this.state.selectedDocuments : _objectSpread({}, result),\n      resultDialog: true,\n      opMag: opMag,\n      mex: message\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        param: '?idWarehouses='\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        param: '?idWarehouses='\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            event\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog3: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 7\n    }, this);\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 39\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 50\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.MovimentazioneInUscita\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 51\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton2: true,\n          actionExtraButton2: this.editResult,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 31\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          fileNames: \"DocUscita\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          documento: this.state.result,\n          result: this.state.results3,\n          results: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter3,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 34\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog4,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 34\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 87\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 123\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 34\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 128\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedRetailer,\n          options: this.retailers,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona cliente\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default MovimentazioneInUscita;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "Toast", "<PERSON><PERSON>", "APIRequest", "Dialog", "Dropdown", "JoyrideGen", "Print", "Sidebar", "Nav", "CustomDataTable", "SelezionaOperatore", "VisualizzaDocumenti", "jsxDEV", "_jsxDEV", "MovimentazioneInUscita", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "url", "state", "<PERSON><PERSON><PERSON><PERSON>", "param2", "code", "lazyParams", "rows", "page", "window", "sessionStorage", "setItem", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$tasks", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "documentDate", "documentBodies", "tasks", "erpSync", "push", "results", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "results2", "results3", "results4", "result", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "globalFilter", "deleteResultDialog", "selectedDocuments", "displayed", "clienti", "idEmployee", "opMag", "mex", "address", "indFatt", "search", "param", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "_element$tasks2", "_e$response3", "_e$response4", "retailers", "loadLazyTimeout", "warehouse", "editR<PERSON>ult", "bind", "hideDialog", "visualizzaDett", "hidevisualizzaDett", "onPage", "onSort", "onFilter", "reset", "resetDesc", "<PERSON><PERSON><PERSON><PERSON>", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "JSON", "parse", "getItem", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "documentBody", "task", "_e$response1", "_e$response10", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "submitted", "_element$tasks4", "_e$response11", "_e$response12", "_element$tasks5", "_e$response13", "_e$response14", "event", "clearTimeout", "setTimeout", "_element$tasks6", "_e$response15", "_e$response16", "Math", "random", "field", "_element$tasks7", "_e$response17", "_e$response18", "loadLazyData", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "doc", "resultDialogFooter3", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "Operatore", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "icon", "handler", "assegnaLavorazione", "status2", "filterDnone", "ref", "el", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "assegnaLavorazioni", "disabledExtraButton2", "length", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "fileNames", "visible", "modal", "footer", "onHide", "DocAll", "draggable", "orders", "Primadiproseguire", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/movimentazioneInUscita.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ResponsableJobsUscita - operazioni sulle lavorazioni in uscita\n*\n*/\nimport React, { Component } from \"react\";\nimport { <PERSON><PERSON> } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaOperatore from \"./selezionaOperatore\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\n\nclass MovimentazioneInUscita extends Component {\n  //Stato iniziale elementi tabella\n  emptyResult = {\n    id: null,\n    firstName: \"\",\n    referente: \"\",\n    deliveryDestination: \"\",\n    orderDate: \"\",\n    deliveryDate: \"\",\n    termsPayment: \"\",\n    paymentStatus: \"\",\n    status: \"\",\n  };\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      globalFilter: null,\n      deleteResultDialog: false,\n      loading: true,\n      selectedWarehouse: null,\n      selectedRetailer: null,\n      selectedDocuments: null,\n      displayed: false,\n      clienti: null,\n      idEmployee: 0,\n      totalRecords: 0,\n      opMag: '',\n      mex: '',\n      firstName: '',\n      address: '',\n      indFatt: '',\n      search: '',\n      param: '?idWarehouses=',\n      param2: '&idRetailer=',\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': { value: '', matchMode: 'contains' },\n          'type': { value: '', matchMode: 'contains' },\n          'documentDate': { value: '', matchMode: 'contains' },\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer.idRegistry.firstName,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    };\n    this.retailers = []\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.opMag = [];\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({ selectedWarehouse: idWarehouse });\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer.idRegistry.firstName,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    } else {\n      this.setState({ resultDialog3: true, displayed: true })\n    }\n    await APIRequest(\"GET\", \"warehouses/\")\n      .then((res) => {\n        for (var entry of res.data) {\n          this.warehouse.push({\n            name: entry.warehouseName,\n            value: entry.id\n          })\n        }\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n    await APIRequest('GET', 'retailers/')\n      .then(res => {\n        res.data.forEach(element => {\n          if (element && element.idRegistry) {\n            var x = {\n              name: element.idRegistry.firstName || 'Nome non disponibile',\n              code: element.id || 0\n            }\n            this.retailers.push(x)\n          }\n        })\n      }).catch((e) => {\n        console.log(e)\n      })\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\")\n      .then((res) => {\n        this.setState({\n          results4: res.data,\n        });\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n  }\n  /* Seleziono il magazzino per la get sui documenti */\n  onWarehouseSelect = async (e) => {\n    this.setState({ selectedWarehouse: e.value });\n    var url = 'documents?idWarehouses=' + e.value + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    window.sessionStorage.setItem(\"idWarehouse\", e.value);\n    await APIRequest(\"GET\", url)\n      .then((res) => {\n        var documento = []\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: element.tasks?.status\n          }\n          documento.push(x)\n        })\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n          loading: false\n        });\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id\n    var documentBody = []\n    var task = []\n    await APIRequest(\"GET\", url)\n      .then((res) => {\n        documentBody = res.data.documentBodies\n        result.documentBodies = res.data.documentBodies\n        task = res.data\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n    var message =\n      \"Documento numero: \" +\n      result.number +\n      \" del \" +\n      new Intl.DateTimeFormat(\"it-IT\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\",\n      }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter((val) => val.id === result.id),\n      results3: documentBody,\n      mex: message,\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false,\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = ''\n    if (this.state.selectedDocuments) {\n      message = \"Trasmissione multipla documenti\"\n    } else {\n      message =\n        \"Documento numero: \" +\n        result.number +\n        \" del \" +\n        new Intl.DateTimeFormat(\"it-IT\", {\n          day: \"2-digit\",\n          month: \"2-digit\",\n          year: \"numeric\",\n        }).format(new Date(result.documentDate));\n    }\n    var opMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach((element) => {\n        var taskAss = 0\n        taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n        opMag.push({\n          label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n          value: element.idemployee,\n        });\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: this.state.selectedDocuments ? this.state.selectedDocuments : { ...result },\n      resultDialog: true,\n      opMag: opMag,\n      mex: message,\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false,\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', param: '?idWarehouses=' });\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer.idRegistry.firstName,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', param: '?idWarehouses=' });\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer.idRegistry.firstName,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }\n  }\n  onPage(event) {\n    this.setState({ loading: true });\n\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer.idRegistry.firstName,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: event,\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({ loading: true });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer.idRegistry.firstName,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { ...this.state.lazyParams, event },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }, Math.random() * 1000 + 250);\n  }\n\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({ lazyParams: event }, this.loadLazyData);\n  }\n  selectionHandler(e) {\n    this.setState({ selectedDocuments: e.value })\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog3: false\n      })\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000,\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    })\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    })\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = (\n      <React.Fragment>\n        <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = (\n      <React.Fragment>\n        <div className=\"row pt-2\">\n          <div className=\"col-12\">\n            <div className=\"d-flex justify-content-end\">\n              <Button\n                className=\"p-button-text closeModal\"\n                onClick={this.hidevisualizzaDett}\n              >\n                {\" \"}\n                {Costanti.Chiudi}{\" \"}\n              </Button>\n              <Print\n                documento={this.state.result}\n                results3={this.state.results3}\n                mex={this.state.mex}\n                doc={true}\n              />\n            </div>\n          </div>\n        </div>\n      </React.Fragment>\n    );\n    const resultDialogFooter3 = (\n      <React.Fragment>\n        <div className='d-flex justify-content-end align-items-center'>\n          <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n        </div>\n      </React.Fragment>\n    );\n    const fields = [\n      { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n      {\n        field: \"number\",\n        header: Costanti.NDoc,\n        body: \"nDoc\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"type\",\n        header: Costanti.type,\n        body: \"typeDoc\",\n        showHeader: true,\n      },\n      {\n        field: \"retailer\",\n        header: Costanti.cliente,\n        body: \"retailer\",\n        showHeader: true,\n      },\n      {\n        field: \"documentDate\",\n        header: Costanti.DataDoc,\n        body: \"documentDate\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"tasks.operator.idUser.username\",\n        header: Costanti.Operatore,\n        body: \"operator\",\n        showHeader: true,\n      },\n      {\n        field: \"tasks.status\",\n        header: Costanti.StatoTask,\n        body: \"assigned\",\n        showHeader: true,\n      },\n      {\n        field: \"erpSync\",\n        header: \"ERP Sync\",\n        body: \"erpSync\",\n        sortable: true,\n        showHeader: true,\n      }\n    ];\n    const actionFields = [\n      { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n      { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n    ];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n    }\n    return (\n      <div className=\"datatable-responsive-demo wrapper\">\n        {/* Il componente Toast permette di creare e visualizzare messaggi */}\n        <Toast ref={(el) => (this.toast = el)} />\n        {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n        <Nav />\n        <div className=\"col-12 px-0 solid-head\">\n          <h1>{Costanti.MovimentazioneInUscita}</h1>\n        </div>\n        {this.state.selectedWarehouse !== null &&\n          <div className='activeFilterContainer p-2'>\n            <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n              <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                <div className='d-flex justify-content-center align-items-center'>\n                  <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                  <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                </div>\n              </li>\n            </ul>\n          </div>\n        }\n        <div className=\"card\">\n          {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n          <CustomDataTable\n            ref={(el) => (this.dt = el)}\n            value={this.state.results}\n            fields={fields}\n            loading={this.state.loading}\n            dataKey=\"id\"\n            lazy\n            filterDisplay=\"row\"\n            paginator\n            onPage={this.onPage}\n            first={this.state.lazyParams.first}\n            totalRecords={this.state.totalRecords}\n            rows={this.state.lazyParams.rows}\n            rowsPerPageOptions={[10, 20, 50]}\n            actionsColumn={actionFields}\n            autoLayout={true}\n            showExportCsvButton={true}\n            selectionMode=\"checkbox\"\n            cellSelection={true}\n            onCellSelect={this.visualizzaDett}\n            selection={this.state.selectedDocuments}\n            onSelectionChange={(e) => this.selectionHandler(e)}\n            showExtraButton2={true}\n            actionExtraButton2={this.editResult}\n            labelExtraButton2={Costanti.assegnaLavorazioni}\n            disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n            showExtraButton={true}\n            actionExtraButton={this.openFilter}\n            labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n            tooltip='Filtri'\n            onSort={this.onSort}\n            sortField={this.state.lazyParams.sortField}\n            sortOrder={this.state.lazyParams.sortOrder}\n            onFilter={this.onFilter}\n            filters={this.state.lazyParams.filters}\n            fileNames=\"DocUscita\"\n          />\n        </div>\n        {/* Struttura dialogo per la modifica */}\n        <Dialog\n          visible={this.state.resultDialog}\n          header={this.state.mex}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter}\n          onHide={this.hideDialog}\n        >\n          <SelezionaOperatore result={this.state.result} opMag={this.opMag} />\n        </Dialog>\n        {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n        <Dialog\n          visible={this.state.resultDialog2}\n          header={Costanti.DocAll}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter2}\n          onHide={this.hidevisualizzaDett}\n          draggable={false}\n        >\n          <VisualizzaDocumenti\n            documento={this.state.result}\n            result={this.state.results3}\n            results={this.state.result}\n            orders={true}\n          />\n        </Dialog>\n        <Dialog visible={this.state.resultDialog3} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter3}>\n          {this.state.displayed &&\n            <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n          }\n          <div className='d-flex justify-content-center flex-column pb-3'>\n            <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n            <hr></hr>\n            <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n          </div>\n        </Dialog>\n        <Sidebar visible={this.state.resultDialog4} position='left' onHide={this.closeFilter}>\n          <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n            <i className=\"pi pi-chevron-right mr-2\"></i>\n            <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n            <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n          </div>\n          <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n            <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n            <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n          </div>\n          <hr></hr>\n          <Dropdown className='w-100' value={this.state.selectedRetailer} options={this.retailers} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n        </Sidebar>\n      </div>\n    );\n  }\n}\n\nexport default MovimentazioneInUscita;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,uDAAuD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExF,MAAMC,sBAAsB,SAAShB,SAAS,CAAC;EAa7CiB,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAbd;IAAA,KACAC,WAAW,GAAG;MACZC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACV,CAAC;IAoMD;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC/B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7C,IAAIC,GAAG,GAAG,yBAAyB,GAAGJ,CAAC,CAACG,KAAK,IAAI,IAAI,CAACE,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAC1PC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEd,CAAC,CAACG,KAAK,CAAC;MACrD,MAAM7B,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,cAAA;UACpC,IAAIC,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAAwB,cAAA,GAAED,OAAO,CAACU,KAAK,cAAAT,cAAA,uBAAbA,cAAA,CAAexB;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAyC,WAAA,EAAAC,YAAA;QACZC,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAAzC,CAAC,CAACmD,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYvB,IAAI,MAAKkC,SAAS,IAAAV,YAAA,GAAG1C,CAAC,CAACmD,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC;IAvOC,IAAI,CAACjD,KAAK,GAAG;MACX6B,OAAO,EAAE,IAAI;MACbqB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAACrE,WAAW;MACxBsE,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE,KAAK;MACzBzB,OAAO,EAAE,IAAI;MACbrC,iBAAiB,EAAE,IAAI;MACvBI,gBAAgB,EAAE,IAAI;MACtB2D,iBAAiB,EAAE,IAAI;MACvBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;MACbjC,YAAY,EAAE,CAAC;MACfkC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACP/E,SAAS,EAAE,EAAE;MACbgF,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,gBAAgB;MACvBnE,MAAM,EAAE,cAAc;MACtBE,UAAU,EAAE;QACV4B,KAAK,EAAE,CAAC;QACR3B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPgE,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACP,QAAQ,EAAE;YAAE1E,KAAK,EAAE,EAAE;YAAE2E,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE3E,KAAK,EAAE,EAAE;YAAE2E,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE3E,KAAK,EAAE,EAAE;YAAE2E,SAAS,EAAE;UAAW;QACrD;MACF;IACF,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAM/E,CAAC,IAAI;MAC1B,IAAI,CAACC,QAAQ,CAAC;QACZsC,OAAO,EAAE,IAAI;QACbkC,MAAM,EAAEzE,CAAC,CAACG,KAAK,CAAC6E,IAAI;QACpB1E,gBAAgB,EAAEN,CAAC,CAACG;MACtB,CAAC,CAAC;MAEF,IAAIC,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACqE,KAAK,GAAG,IAAI,CAACrE,KAAK,CAACH,iBAAiB,GAAG,IAAI,CAACG,KAAK,CAACE,MAAM,GAAGP,CAAC,CAACG,KAAK,CAACK,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAClN,MAAMrC,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4D,eAAA;UACpC,IAAI1D,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAAmF,eAAA,GAAE5D,OAAO,CAACU,KAAK,cAAAkD,eAAA,uBAAbA,eAAA,CAAenF;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAkF,YAAA,EAAAC,YAAA;QACZxC,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAgC,YAAA,GAAAlF,CAAC,CAACmD,QAAQ,cAAA+B,YAAA,uBAAVA,YAAA,CAAYhE,IAAI,MAAKkC,SAAS,IAAA+B,YAAA,GAAGnF,CAAC,CAACmD,QAAQ,cAAAgC,YAAA,uBAAVA,YAAA,CAAYjE,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC8B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACjB,KAAK,GAAG,EAAE;IACf,IAAI,CAACkB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACE,cAAc,GAAG,IAAI,CAACA,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACzF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACyF,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACK,MAAM,GAAG,IAAI,CAACA,MAAM,CAACL,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACM,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACN,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACO,KAAK,GAAG,IAAI,CAACA,KAAK,CAACP,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACA,SAAS,CAACR,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACT,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACU,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACV,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACW,UAAU,GAAG,IAAI,CAACA,UAAU,CAACX,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACY,WAAW,GAAG,IAAI,CAACA,WAAW,CAACZ,IAAI,CAAC,IAAI,CAAC;EAChD;EACA;EACA,MAAMa,iBAAiBA,CAAA,EAAG;IACxB,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC5F,MAAM,CAACC,cAAc,CAAC4F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC7C,IAAIlG,GAAG,GAAG,yBAAyB,GAAGkG,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACjG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEoG;MAAY,CAAC,CAAC;MACjD,MAAMhI,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAqF,eAAA;UACpC,IAAInF,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAA4G,eAAA,GAAErF,OAAO,CAACU,KAAK,cAAA2E,eAAA,uBAAbA,eAAA,CAAe5G;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAA2G,YAAA,EAAAC,YAAA;QACZjE,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyD,YAAA,GAAA3G,CAAC,CAACmD,QAAQ,cAAAwD,YAAA,uBAAVA,YAAA,CAAYzF,IAAI,MAAKkC,SAAS,IAAAwD,YAAA,GAAG5G,CAAC,CAACmD,QAAQ,cAAAyD,YAAA,uBAAVA,YAAA,CAAY1F,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAI,CAACrD,QAAQ,CAAC;QAAE4D,aAAa,EAAE,IAAI;QAAEK,SAAS,EAAE;MAAK,CAAC,CAAC;IACzD;IACA,MAAM5F,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACnCyC,IAAI,CAAEC,GAAG,IAAK;MACb,KAAK,IAAI6F,KAAK,IAAI7F,GAAG,CAACE,IAAI,EAAE;QAC1B,IAAI,CAACoE,SAAS,CAACrD,IAAI,CAAC;UAClB+C,IAAI,EAAE6B,KAAK,CAACC,aAAa;UACzB3G,KAAK,EAAE0G,KAAK,CAACvH;QACf,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACDkD,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAA+G,YAAA,EAAAC,YAAA;MACZrE,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAA6D,YAAA,GAAA/G,CAAC,CAACmD,QAAQ,cAAA4D,YAAA,uBAAVA,YAAA,CAAY7F,IAAI,MAAKkC,SAAS,IAAA4D,YAAA,GAAGhH,CAAC,CAACmD,QAAQ,cAAA6D,YAAA,uBAAVA,YAAA,CAAY9F,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,MAAMhF,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAClCyC,IAAI,CAACC,GAAG,IAAI;MACXA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAIA,OAAO,IAAIA,OAAO,CAACO,UAAU,EAAE;UACjC,IAAIL,CAAC,GAAG;YACNyD,IAAI,EAAE3D,OAAO,CAACO,UAAU,CAACrC,SAAS,IAAI,sBAAsB;YAC5DiB,IAAI,EAAEa,OAAO,CAAC/B,EAAE,IAAI;UACtB,CAAC;UACD,IAAI,CAAC8F,SAAS,CAACnD,IAAI,CAACV,CAAC,CAAC;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAACiB,KAAK,CAAExC,CAAC,IAAK;MACd2C,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;IAChB,CAAC,CAAC;IACJ,MAAM1B,UAAU,CAAC,KAAK,EAAE,4CAA4C,CAAC,CAClEyC,IAAI,CAAEC,GAAG,IAAK;MACb,IAAI,CAACf,QAAQ,CAAC;QACZwD,QAAQ,EAAEzC,GAAG,CAACE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC,CACDsB,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAAiH,YAAA,EAAAC,YAAA;MACZvE,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAA+D,YAAA,GAAAjH,CAAC,CAACmD,QAAQ,cAAA8D,YAAA,uBAAVA,YAAA,CAAY/F,IAAI,MAAKkC,SAAS,IAAA8D,YAAA,GAAGlH,CAAC,CAACmD,QAAQ,cAAA+D,YAAA,uBAAVA,YAAA,CAAYhG,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAwCA;EACA,MAAMoC,cAAcA,CAAChC,MAAM,EAAE;IAC3B,IAAItD,GAAG,GAAG,2BAA2B,GAAGsD,MAAM,CAACpE,EAAE;IACjD,IAAI6H,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAM9I,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;MACbmG,YAAY,GAAGnG,GAAG,CAACE,IAAI,CAACY,cAAc;MACtC4B,MAAM,CAAC5B,cAAc,GAAGd,GAAG,CAACE,IAAI,CAACY,cAAc;MAC/CsF,IAAI,GAAGpG,GAAG,CAACE,IAAI;IACjB,CAAC,CAAC,CACDsB,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAAqH,YAAA,EAAAC,aAAA;MACZ3E,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAmE,YAAA,GAAArH,CAAC,CAACmD,QAAQ,cAAAkE,YAAA,uBAAVA,YAAA,CAAYnG,IAAI,MAAKkC,SAAS,IAAAkE,aAAA,GAAGtH,CAAC,CAACmD,QAAQ,cAAAmE,aAAA,uBAAVA,aAAA,CAAYpG,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QACxJC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,IAAID,OAAO,GACT,oBAAoB,GACpBK,MAAM,CAAClC,MAAM,GACb,OAAO,GACP,IAAI+F,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC/BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACnE,MAAM,CAAC7B,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC5B,QAAQ,CAAC;MACZ2D,aAAa,EAAE,IAAI;MACnBF,MAAM,EAAE0D,IAAI;MACZ7D,QAAQ,EAAE,IAAI,CAAClD,KAAK,CAAC6B,OAAO,CAAC4F,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACzI,EAAE,KAAKoE,MAAM,CAACpE,EAAE,CAAC;MAClEkE,QAAQ,EAAE2D,YAAY;MACtB7C,GAAG,EAAEjB;IACP,CAAC,CAAC;EACJ;EACA;EACAsC,kBAAkBA,CAACjC,MAAM,EAAE;IACzB,IAAI,CAACzD,QAAQ,CAAC;MACZyD,MAAM,EAAEA,MAAM;MACdE,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACA2B,UAAUA,CAAC7B,MAAM,EAAE;IACjB,IAAIL,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAChD,KAAK,CAAC4D,iBAAiB,EAAE;MAChCZ,OAAO,GAAG,iCAAiC;IAC7C,CAAC,MAAM;MACLA,OAAO,GACL,oBAAoB,GACpBK,MAAM,CAAClC,MAAM,GACb,OAAO,GACP,IAAI+F,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAC/BC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACnE,MAAM,CAAC7B,YAAY,CAAC,CAAC;IAC5C;IACA,IAAIwC,KAAK,GAAG,EAAE;IACd,IAAI,IAAI,CAAChE,KAAK,CAACoD,QAAQ,KAAK,EAAE,EAAE;MAC9B,IAAI,CAACpD,KAAK,CAACoD,QAAQ,CAACrC,OAAO,CAAEC,OAAO,IAAK;QACvC,IAAI2G,OAAO,GAAG,CAAC;QACfA,OAAO,GAAGC,QAAQ,CAAC5G,OAAO,CAAC6G,WAAW,CAAC,GAAGD,QAAQ,CAAC5G,OAAO,CAAC8G,YAAY,CAAC,GAAGF,QAAQ,CAAC5G,OAAO,CAAC+G,aAAa,CAAC;QAC1G/D,KAAK,CAACpC,IAAI,CAAC;UACToG,KAAK,EAAEhH,OAAO,CAACiH,UAAU,GAAG,GAAG,GAAGjH,OAAO,CAACkH,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;UAC1F7H,KAAK,EAAEkB,OAAO,CAACmH;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAACnE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACpE,QAAQ,CAAC;MACZyD,MAAM,EAAE,IAAI,CAACrD,KAAK,CAAC4D,iBAAiB,GAAG,IAAI,CAAC5D,KAAK,CAAC4D,iBAAiB,GAAAwE,aAAA,KAAQ/E,MAAM,CAAE;MACnFC,YAAY,EAAE,IAAI;MAClBU,KAAK,EAAEA,KAAK;MACZC,GAAG,EAAEjB;IACP,CAAC,CAAC;EACJ;EACA;EACAoC,UAAUA,CAAA,EAAG;IACX,IAAI,CAACxF,QAAQ,CAAC;MACZyI,SAAS,EAAE,KAAK;MAChB/E,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EACA;EACA,MAAMoC,KAAKA,CAAA,EAAG;IACZ,IAAIO,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC5F,MAAM,CAACC,cAAc,CAAC4F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC7C,IAAIlG,GAAG,GAAG,yBAAyB,GAAGkG,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACjG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEoG,WAAW;QAAE/D,OAAO,EAAE,IAAI;QAAEkC,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAiB,CAAC,CAAC;MACrG,MAAMpG,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAsH,eAAA;UACpC,IAAIpH,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAA6I,eAAA,GAAEtH,OAAO,CAACU,KAAK,cAAA4G,eAAA,uBAAbA,eAAA,CAAe7I;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAA4I,aAAA,EAAAC,aAAA;QACZlG,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA0F,aAAA,GAAA5I,CAAC,CAACmD,QAAQ,cAAAyF,aAAA,uBAAVA,aAAA,CAAY1H,IAAI,MAAKkC,SAAS,IAAAyF,aAAA,GAAG7I,CAAC,CAACmD,QAAQ,cAAA0F,aAAA,uBAAVA,aAAA,CAAY3H,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN;EACF;EACA;EACA,MAAM0C,SAASA,CAAA,EAAG;IAChB,IAAIM,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC5F,MAAM,CAACC,cAAc,CAAC4F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC7C,IAAIlG,GAAG,GAAG,yBAAyB,GAAGkG,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACjG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEoG,WAAW;QAAE/D,OAAO,EAAE,IAAI;QAAEkC,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAiB,CAAC,CAAC;MACrG,MAAMpG,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyH,eAAA;UACpC,IAAIvH,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAAgJ,eAAA,GAAEzH,OAAO,CAACU,KAAK,cAAA+G,eAAA,uBAAbA,eAAA,CAAehJ;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAA+I,aAAA,EAAAC,aAAA;QACZrG,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA6F,aAAA,GAAA/I,CAAC,CAACmD,QAAQ,cAAA4F,aAAA,uBAAVA,aAAA,CAAY7H,IAAI,MAAKkC,SAAS,IAAA4F,aAAA,GAAGhJ,CAAC,CAACmD,QAAQ,cAAA6F,aAAA,uBAAVA,aAAA,CAAY9H,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN;EACF;EACAsC,MAAMA,CAACqD,KAAK,EAAE;IACZ,IAAI,CAAChJ,QAAQ,CAAC;MAAEsC,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAAC8C,eAAe,EAAE;MACxB6D,YAAY,CAAC,IAAI,CAAC7D,eAAe,CAAC;IACpC;IACA,IAAI,CAACA,eAAe,GAAG8D,UAAU,CAAC,YAAY;MAC5C,IAAI/I,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACqE,KAAK,GAAG,IAAI,CAACrE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAGyI,KAAK,CAACvI,IAAI,GAAG,QAAQ,GAAGuI,KAAK,CAACtI,IAAI;MACpP,MAAMrC,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA+H,eAAA;UACpC,IAAI7H,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAAsJ,eAAA,GAAE/H,OAAO,CAACU,KAAK,cAAAqH,eAAA,uBAAbA,eAAA,CAAetJ;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAEwI,KAAK;UACjB1G,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAqJ,aAAA,EAAAC,aAAA;QACZ3G,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAmG,aAAA,GAAArJ,CAAC,CAACmD,QAAQ,cAAAkG,aAAA,uBAAVA,aAAA,CAAYnI,IAAI,MAAKkC,SAAS,IAAAkG,aAAA,GAAGtJ,CAAC,CAACmD,QAAQ,cAAAmG,aAAA,uBAAVA,aAAA,CAAYpI,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,EAAEiG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAChC;EACA3D,MAAMA,CAACoD,KAAK,EAAE;IACZ,IAAI,CAAChJ,QAAQ,CAAC;MAAEsC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIkH,KAAK,GAAGR,KAAK,CAACtE,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGsE,KAAK,CAACtE,SAAS;IAChG,IAAI,IAAI,CAACU,eAAe,EAAE;MACxB6D,YAAY,CAAC,IAAI,CAAC7D,eAAe,CAAC;IACpC;IACA,IAAI,CAACA,eAAe,GAAG8D,UAAU,CAAC,YAAY;MAC5C,IAAI/I,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACqE,KAAK,GAAG,IAAI,CAACrE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI,GAAG,SAAS,GAAG8I,KAAK,GAAG,WAAW,IAAIR,KAAK,CAACrE,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAMtG,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAqI,eAAA;UACpC,IAAInI,CAAC,GAAG;YACNjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,cAAc,EAAET,OAAO,CAACS,cAAc;YACtCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxBlC,MAAM,GAAA4J,eAAA,GAAErI,OAAO,CAACU,KAAK,cAAA2H,eAAA,uBAAbA,eAAA,CAAe5J;UACzB,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACZiC,OAAO,EAAEjB,SAAS;UAClBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAAgI,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACpI,KAAK,CAACI,UAAU;YAAEwI;UAAK,EAAE;UAC/C1G,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAA2J,aAAA,EAAAC,aAAA;QACZjH,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyG,aAAA,GAAA3J,CAAC,CAACmD,QAAQ,cAAAwG,aAAA,uBAAVA,aAAA,CAAYzI,IAAI,MAAKkC,SAAS,IAAAwG,aAAA,GAAG5J,CAAC,CAACmD,QAAQ,cAAAyG,aAAA,uBAAVA,aAAA,CAAY1I,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,EAAEiG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAChC;EAEA1D,QAAQA,CAACmD,KAAK,EAAE;IACdA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAChJ,QAAQ,CAAC;MAAEQ,UAAU,EAAEwI;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EACzD;EACA5D,gBAAgBA,CAACjG,CAAC,EAAE;IAClB,IAAI,CAACC,QAAQ,CAAC;MAAEgE,iBAAiB,EAAEjE,CAAC,CAACG;IAAM,CAAC,CAAC;EAC/C;EACA+F,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC7F,KAAK,CAACH,iBAAiB,KAAK,IAAI,EAAE;MACzC,IAAI,CAACD,QAAQ,CAAC;QACZ4D,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChB,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF;EACA6C,UAAUA,CAAA,EAAG;IACX,IAAI,CAAClG,QAAQ,CAAC;MACZ6D,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACAsC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACnG,QAAQ,CAAC;MACZ6D,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACAgG,MAAMA,CAAA,EAAG;IACP;IACA,MAAMC,kBAAkB,gBACtB9K,OAAA,CAAChB,KAAK,CAAC+L,QAAQ;MAAAC,QAAA,eACbhL,OAAA,CAACd,MAAM;QAAC+L,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC1E,UAAW;QAAAwE,QAAA,GACnE,GAAG,EACH5L,QAAQ,CAAC+L,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAMC,mBAAmB,gBACvBxL,OAAA,CAAChB,KAAK,CAAC+L,QAAQ;MAAAC,QAAA,eACbhL,OAAA;QAAKiL,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvBhL,OAAA;UAAKiL,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACrBhL,OAAA;YAAKiL,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzChL,OAAA,CAACd,MAAM;cACL+L,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAACxE,kBAAmB;cAAAsE,QAAA,GAEhC,GAAG,EACH5L,QAAQ,CAAC+L,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACTvL,OAAA,CAACP,KAAK;cACJuC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAACqD,MAAO;cAC7BF,QAAQ,EAAE,IAAI,CAACnD,KAAK,CAACmD,QAAS;cAC9Bc,GAAG,EAAE,IAAI,CAACjE,KAAK,CAACiE,GAAI;cACpBoG,GAAG,EAAE;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACjB;IACD,MAAMG,mBAAmB,gBACvB1L,OAAA,CAAChB,KAAK,CAAC+L,QAAQ;MAAAC,QAAA,eACbhL,OAAA;QAAKiL,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC5DhL,OAAA,CAACd,MAAM;UAAC+L,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACjE,iBAAkB;UAAA+D,QAAA,GAAE,GAAC,EAAC5L,QAAQ,CAAC+L,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACjB;IACD,MAAMI,MAAM,GAAG,CACb;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACEtB,KAAK,EAAE,QAAQ;MACfuB,MAAM,EAAE3M,QAAQ,CAAC4M,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACE3B,KAAK,EAAE,MAAM;MACbuB,MAAM,EAAE3M,QAAQ,CAACoD,IAAI;MACrByJ,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IACd,CAAC,EACD;MACE3B,KAAK,EAAE,UAAU;MACjBuB,MAAM,EAAE3M,QAAQ,CAACgN,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IACd,CAAC,EACD;MACE3B,KAAK,EAAE,cAAc;MACrBuB,MAAM,EAAE3M,QAAQ,CAACiN,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACE3B,KAAK,EAAE,gCAAgC;MACvCuB,MAAM,EAAE3M,QAAQ,CAACkN,SAAS;MAC1BL,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IACd,CAAC,EACD;MACE3B,KAAK,EAAE,cAAc;MACrBuB,MAAM,EAAE3M,QAAQ,CAACmN,SAAS;MAC1BN,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IACd,CAAC,EACD;MACE3B,KAAK,EAAE,SAAS;MAChBuB,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,CACF;IACD,MAAMK,YAAY,GAAG,CACnB;MAAEzG,IAAI,EAAE3G,QAAQ,CAACqN,OAAO;MAAEC,IAAI,eAAE1M,OAAA;QAAGiL,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEoB,OAAO,EAAE,IAAI,CAAClG;IAAe,CAAC,EAC3F;MAAEV,IAAI,EAAE3G,QAAQ,CAACwN,kBAAkB;MAAEF,IAAI,eAAE1M,OAAA;QAAGiL,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEoB,OAAO,EAAE,IAAI,CAACrG,UAAU;MAAEzF,MAAM,EAAE,QAAQ;MAAEgM,OAAO,EAAE;IAAU,CAAC,CAChJ;IACD,IAAIC,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAAC1L,KAAK,CAACoE,MAAM,KAAK,EAAE,EAAE;MAC5BsH,WAAW,GAAG,gCAAgC;IAChD,CAAC,MAAM;MACLA,WAAW,GAAG,uCAAuC;IACvD;IACA,oBACE9M,OAAA;MAAKiL,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAEhDhL,OAAA,CAACb,KAAK;QAAC4N,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACpJ,KAAK,GAAGoJ;MAAI;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzCvL,OAAA,CAACL,GAAG;QAAAyL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPvL,OAAA;QAAKiL,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrChL,OAAA;UAAAgL,QAAA,EAAK5L,QAAQ,CAACa;QAAsB;UAAAmL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,EACL,IAAI,CAACnK,KAAK,CAACH,iBAAiB,KAAK,IAAI,iBACpCjB,OAAA;QAAKiL,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACxChL,OAAA;UAAIiL,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACxEhL,OAAA;YAAIiL,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACnEhL,OAAA;cAAKiL,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC/DhL,OAAA;gBAAIiL,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAAChL,OAAA;kBAAGiL,SAAS,EAAC,iBAAiB;kBAACgC,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAACnM,QAAQ,CAAC8N,SAAS,EAAC,GAAC;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5HvL,OAAA,CAACT,QAAQ;gBAAC0L,SAAS,EAAC,QAAQ;gBAAC/J,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;gBAACkM,OAAO,EAAE,IAAI,CAAC9G,SAAU;gBAAC+G,QAAQ,EAAE,IAAI,CAACtM,iBAAkB;gBAACuM,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACzE,MAAM;gBAAC0E,QAAQ,EAAC;cAAM;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERvL,OAAA;QAAKiL,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEnBhL,OAAA,CAACJ,eAAe;UACdmN,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACQ,EAAE,GAAGR,EAAI;UAC5B9L,KAAK,EAAE,IAAI,CAACE,KAAK,CAAC6B,OAAQ;UAC1B0I,MAAM,EAAEA,MAAO;UACfrI,OAAO,EAAE,IAAI,CAAClC,KAAK,CAACkC,OAAQ;UAC5BmK,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTjH,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBvD,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAM;UACnCF,YAAY,EAAE,IAAI,CAAC9B,KAAK,CAAC8B,YAAa;UACtCzB,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAK;UACjCoM,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEtB,YAAa;UAC5BuB,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BpC,aAAa,EAAC,UAAU;UACxBqC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAACzH,cAAe;UAClC0H,SAAS,EAAE,IAAI,CAAC/M,KAAK,CAAC4D,iBAAkB;UACxCoJ,iBAAiB,EAAGrN,CAAC,IAAK,IAAI,CAACiG,gBAAgB,CAACjG,CAAC,CAAE;UACnDsN,gBAAgB,EAAE,IAAK;UACvBC,kBAAkB,EAAE,IAAI,CAAChI,UAAW;UACpCiI,iBAAiB,EAAEnP,QAAQ,CAACoP,kBAAmB;UAC/CC,oBAAoB,EAAE,CAAC,IAAI,CAACrN,KAAK,CAAC4D,iBAAiB,IAAI,CAAC,IAAI,CAAC5D,KAAK,CAAC4D,iBAAiB,CAAC0J,MAAO;UAC5FC,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAAC1H,UAAW;UACnC2H,gBAAgB,eAAE7O,OAAA;YAAUiL,SAAS,EAAC,MAAM;YAAClF,IAAI,EAAC;UAAgB;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/EuD,OAAO,EAAC,QAAQ;UAChBlI,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBlB,SAAS,EAAE,IAAI,CAACtE,KAAK,CAACI,UAAU,CAACkE,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACvE,KAAK,CAACI,UAAU,CAACmE,SAAU;UAC3CkB,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBjB,OAAO,EAAE,IAAI,CAACxE,KAAK,CAACI,UAAU,CAACoE,OAAQ;UACvCmJ,SAAS,EAAC;QAAW;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvL,OAAA,CAACV,MAAM;QACL0P,OAAO,EAAE,IAAI,CAAC5N,KAAK,CAACsD,YAAa;QACjCqH,MAAM,EAAE,IAAI,CAAC3K,KAAK,CAACiE,GAAI;QACvB4J,KAAK;QACLhE,SAAS,EAAC,kBAAkB;QAC5BiE,MAAM,EAAEpE,kBAAmB;QAC3BqE,MAAM,EAAE,IAAI,CAAC3I,UAAW;QAAAwE,QAAA,eAExBhL,OAAA,CAACH,kBAAkB;UAAC4E,MAAM,EAAE,IAAI,CAACrD,KAAK,CAACqD,MAAO;UAACW,KAAK,EAAE,IAAI,CAACA;QAAM;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAETvL,OAAA,CAACV,MAAM;QACL0P,OAAO,EAAE,IAAI,CAAC5N,KAAK,CAACuD,aAAc;QAClCoH,MAAM,EAAE3M,QAAQ,CAACgQ,MAAO;QACxBH,KAAK;QACLhE,SAAS,EAAC,kBAAkB;QAC5BiE,MAAM,EAAE1D,mBAAoB;QAC5B2D,MAAM,EAAE,IAAI,CAACzI,kBAAmB;QAChC2I,SAAS,EAAE,KAAM;QAAArE,QAAA,eAEjBhL,OAAA,CAACF,mBAAmB;UAClBkC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAACqD,MAAO;UAC7BA,MAAM,EAAE,IAAI,CAACrD,KAAK,CAACmD,QAAS;UAC5BtB,OAAO,EAAE,IAAI,CAAC7B,KAAK,CAACqD,MAAO;UAC3B6K,MAAM,EAAE;QAAK;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACTvL,OAAA,CAACV,MAAM;QAAC0P,OAAO,EAAE,IAAI,CAAC5N,KAAK,CAACwD,aAAc;QAACmH,MAAM,EAAE3M,QAAQ,CAACmQ,iBAAkB;QAACN,KAAK;QAAChE,SAAS,EAAC,kBAAkB;QAACkE,MAAM,EAAE,IAAI,CAAClI,iBAAkB;QAACiI,MAAM,EAAExD,mBAAoB;QAAAV,QAAA,GAC3K,IAAI,CAAC5J,KAAK,CAAC6D,SAAS,iBACnBjF,OAAA,CAACR,UAAU;UAACgQ,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE9FvL,OAAA;UAAKiL,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC7DhL,OAAA;YAAIiL,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAChL,OAAA;cAAGiL,SAAS,EAAC,iBAAiB;cAACgC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACnM,QAAQ,CAAC8N,SAAS;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHvL,OAAA;YAAAoL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvL,OAAA,CAACT,QAAQ;YAAC0L,SAAS,EAAC,QAAQ;YAAC/J,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;YAACkM,OAAO,EAAE,IAAI,CAAC9G,SAAU;YAAC+G,QAAQ,EAAE,IAAI,CAACtM,iBAAkB;YAACuM,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACzE,MAAM;YAAC0E,QAAQ,EAAC;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACTvL,OAAA,CAACN,OAAO;QAACsP,OAAO,EAAE,IAAI,CAAC5N,KAAK,CAACyD,aAAc;QAAC8K,QAAQ,EAAC,MAAM;QAACR,MAAM,EAAE,IAAI,CAAChI,WAAY;QAAA6D,QAAA,gBACnFhL,OAAA;UAAKK,EAAE,EAAC,cAAc;UAAC4K,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACvKhL,OAAA;YAAGiL,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CvL,OAAA;YAAIiL,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAChL,OAAA;cAAGiL,SAAS,EAAC,mBAAmB;cAACgC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACnM,QAAQ,CAACwQ,MAAM;UAAA;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/GvL,OAAA,CAACd,MAAM;YAACmB,EAAE,EAAC,iBAAiB;YAAC4K,SAAS,EAAE6B,WAAY;YAAC5B,OAAO,EAAE,IAAI,CAACpE,KAAM;YAAAkE,QAAA,gBAAChL,OAAA;cAAGiL,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAAvL,OAAA;cAAAgL,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtI,CAAC,eACNvL,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAAC4K,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBACjEhL,OAAA;YAAIiL,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAChL,OAAA;cAAGiL,SAAS,EAAC,mBAAmB;cAACgC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACnM,QAAQ,CAACwQ,MAAM;UAAA;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/GvL,OAAA,CAACd,MAAM;YAACmB,EAAE,EAAC,kBAAkB;YAAC4K,SAAS,EAAE6B,WAAY;YAAC5B,OAAO,EAAE,IAAI,CAACnE,SAAU;YAAAiE,QAAA,gBAAChL,OAAA;cAAGiL,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAAvL,OAAA;cAAAgL,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC,eACNvL,OAAA;UAAAoL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvL,OAAA,CAACT,QAAQ;UAAC0L,SAAS,EAAC,OAAO;UAAC/J,KAAK,EAAE,IAAI,CAACE,KAAK,CAACC,gBAAiB;UAAC8L,OAAO,EAAE,IAAI,CAAChH,SAAU;UAACiH,QAAQ,EAAE,IAAI,CAACtH,SAAU;UAACuH,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,mBAAmB;UAACzE,MAAM;UAAC0E,QAAQ,EAAC;QAAM;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;AACF;AAEA,eAAetL,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}