{"ast": null, "code": "import * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nexport var HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS'; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\nvar warningFunc = function warningFunc() {\n  warning(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nexport default Context;", "map": {"version": 3, "names": ["React", "warning", "HOOK_MARK", "warningFunc", "Context", "createContext", "getFieldValue", "getFieldsValue", "getFieldError", "getFieldWarning", "getFieldsError", "isFieldsTouched", "isFieldTouched", "isFieldValidating", "isFieldsValidating", "resetFields", "setFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "submit", "getInternalHooks", "dispatch", "initEntityValue", "registerField", "useSubscribe", "setInitialValues", "destroyForm", "setCallbacks", "registerWatch", "getFields", "setValidateMessages", "setPreserve", "getInitialValue"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/FieldContext.js"], "sourcesContent": ["import * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nexport var HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS'; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\nvar warningFunc = function warningFunc() {\n  warning(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\n\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nexport default Context;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,IAAIC,SAAS,GAAG,wBAAwB,CAAC,CAAC;;AAEjD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvCF,OAAO,CAAC,KAAK,EAAE,uEAAuE,CAAC;AACzF,CAAC;AAED,IAAIG,OAAO,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC;EAC7CC,aAAa,EAAEH,WAAW;EAC1BI,cAAc,EAAEJ,WAAW;EAC3BK,aAAa,EAAEL,WAAW;EAC1BM,eAAe,EAAEN,WAAW;EAC5BO,cAAc,EAAEP,WAAW;EAC3BQ,eAAe,EAAER,WAAW;EAC5BS,cAAc,EAAET,WAAW;EAC3BU,iBAAiB,EAAEV,WAAW;EAC9BW,kBAAkB,EAAEX,WAAW;EAC/BY,WAAW,EAAEZ,WAAW;EACxBa,SAAS,EAAEb,WAAW;EACtBc,cAAc,EAAEd,WAAW;EAC3Be,cAAc,EAAEf,WAAW;EAC3BgB,MAAM,EAAEhB,WAAW;EACnBiB,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5CjB,WAAW,CAAC,CAAC;IACb,OAAO;MACLkB,QAAQ,EAAElB,WAAW;MACrBmB,eAAe,EAAEnB,WAAW;MAC5BoB,aAAa,EAAEpB,WAAW;MAC1BqB,YAAY,EAAErB,WAAW;MACzBsB,gBAAgB,EAAEtB,WAAW;MAC7BuB,WAAW,EAAEvB,WAAW;MACxBwB,YAAY,EAAExB,WAAW;MACzByB,aAAa,EAAEzB,WAAW;MAC1B0B,SAAS,EAAE1B,WAAW;MACtB2B,mBAAmB,EAAE3B,WAAW;MAChC4B,WAAW,EAAE5B,WAAW;MACxB6B,eAAe,EAAE7B;IACnB,CAAC;EACH;AACF,CAAC,CAAC;AACF,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}