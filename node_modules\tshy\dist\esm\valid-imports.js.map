{"version": 3, "file": "valid-imports.js", "sourceRoot": "", "sources": ["../../src/valid-imports.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,WAAW,CAAA;AAE5B,OAAO,mBAAmB,MAAM,4BAA4B,CAAA;AAE5D,qCAAqC;AACrC,eAAe,CAAC,GAAY,EAAE,EAAE;IAC9B,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAA;IACvB,IAAI,OAAO,KAAK,SAAS;QAAE,OAAO,IAAI,CAAA;IACtC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC1D,IAAI,CACF,0DAA0D;YACxD,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CACpC,CAAA;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IAED,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1D,IAAI,CAAC,oCAAoC,GAAG,CAAC,CAAC,CAAA;YAC9C,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,QAAQ;YAAE,SAAQ;QACnC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5B,IAAI,CACF,2BAA2B,CAAC,yBAAyB;gBACnD,mDAAmD;gBACnD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CACpB,CAAA;YACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["import fail from './fail.js'\nimport { Package } from './types.js'\nimport validExternalExport from './valid-external-export.js'\n\n// validate the package.imports field\nexport default (pkg: Package) => {\n  const { imports } = pkg\n  if (imports === undefined) return true\n  if (Array.isArray(imports) || typeof imports !== 'object') {\n    fail(\n      'invalid imports object, must be Record<string, Import>, ' +\n        `got: ${JSON.stringify(imports)}`,\n    )\n    return process.exit(1)\n  }\n\n  for (const [i, v] of Object.entries(imports)) {\n    if (!i.startsWith('#') || i === '#' || i.startsWith('#/')) {\n      fail('invalid imports module specifier: ' + i)\n      return process.exit(1)\n    }\n    if (typeof v === 'string') continue\n    if (!validExternalExport(v)) {\n      fail(\n        `unbuilt package.imports ${i} must not be in ./src, ` +\n          'and imports in ./src must be string values. got: ' +\n          JSON.stringify(v),\n      )\n      return process.exit(1)\n    }\n  }\n  return true\n}\n"]}