# Piano Testing Automatico - Winet E-Procurement

## 🎯 Obiettivi del Testing

### 1. **Validazione Funzionalità per Ruolo**
- Verificare che ogni ruolo acceda solo alle funzionalità autorizzate
- Testare workflow completi per ogni tipologia utente
- Validare integrità dati tra ruoli interconnessi

### 2. **Preparazione Suddivisione Prodotti**
- Testare isolamento componenti per prodotti separati
- Verificare compatibilità componenti condivisi
- Validare sistema di licenze e permessi

### 3. **Qualità e Performance**
- Garantire stabilità applicazione
- Ottimizzare performance per ogni configurazione
- Prevenire regressioni durante sviluppo

## 🏗️ Architettura Testing

### Stack Tecnologico Testing
```json
{
  "unitTesting": "Jest + React Testing Library",
  "integrationTesting": "Jest + MSW (Mock Service Worker)",
  "e2eTesting": "Cypress",
  "visualTesting": "Storybook + Chromatic",
  "performanceTesting": "Lighthouse CI",
  "apiTesting": "Postman/Newman",
  "loadTesting": "Artillery.js"
}
```

### Struttura Directory Testing
```
tests/
├── unit/                    # Test unitari
│   ├── components/         # Test componenti
│   ├── utils/             # Test utility
│   └── hooks/             # Test custom hooks
├── integration/            # Test integrazione
│   ├── workflows/         # Test workflow completi
│   ├── api/              # Test API integration
│   └── roles/            # Test specifici per ruolo
├── e2e/                   # Test end-to-end
│   ├── user-journeys/    # Journey per ruolo
│   ├── cross-role/       # Test inter-ruolo
│   └── products/         # Test prodotti separati
├── performance/           # Test performance
├── fixtures/             # Dati di test
├── mocks/               # Mock API e servizi
└── utils/               # Utility testing
```

## 🧪 TESTING PER RUOLO UTENTE

### 1. 👨‍💼 Testing AMMINISTRATORE

#### Unit Tests
```javascript
// tests/unit/admin/DashboardAmministratore.test.js
describe('Dashboard Amministratore', () => {
  test('should render admin dashboard with correct widgets', () => {
    render(<DashboardAmministratore />);
    expect(screen.getByText('Gestione Utenti')).toBeInTheDocument();
    expect(screen.getByText('Gestione Corporate')).toBeInTheDocument();
    expect(screen.getByText('Generatore Documenti')).toBeInTheDocument();
  });

  test('should show system statistics', async () => {
    const mockStats = { users: 150, activeUsers: 120, errors: 5 };
    mockAPI.get('/analytics/admin').mockResolvedValue({ data: mockStats });
    
    render(<DashboardAmministratore />);
    
    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument(); // Total users
      expect(screen.getByText('120')).toBeInTheDocument(); // Active users
    });
  });
});
```

#### Integration Tests
```javascript
// tests/integration/admin/user-management.test.js
describe('Admin User Management Workflow', () => {
  test('should create, edit, and delete user', async () => {
    // Setup
    const newUser = {
      email: '<EMAIL>',
      role: 'DISTRIBUTORE',
      firstName: 'Test',
      lastName: 'User'
    };

    // Create user
    await userEvent.click(screen.getByText('Aggiungi Utente'));
    await userEvent.type(screen.getByLabelText('Email'), newUser.email);
    await userEvent.selectOptions(screen.getByLabelText('Ruolo'), newUser.role);
    await userEvent.click(screen.getByText('Salva'));

    // Verify creation
    await waitFor(() => {
      expect(screen.getByText(newUser.email)).toBeInTheDocument();
    });

    // Edit user
    await userEvent.click(screen.getByTestId(`edit-${newUser.email}`));
    await userEvent.clear(screen.getByLabelText('Nome'));
    await userEvent.type(screen.getByLabelText('Nome'), 'Updated Name');
    await userEvent.click(screen.getByText('Salva'));

    // Verify edit
    await waitFor(() => {
      expect(screen.getByText('Updated Name')).toBeInTheDocument();
    });

    // Delete user
    await userEvent.click(screen.getByTestId(`delete-${newUser.email}`));
    await userEvent.click(screen.getByText('Conferma'));

    // Verify deletion
    await waitFor(() => {
      expect(screen.queryByText(newUser.email)).not.toBeInTheDocument();
    });
  });
});
```

#### E2E Tests
```javascript
// tests/e2e/admin/admin-journey.cy.js
describe('Admin Complete Journey', () => {
  beforeEach(() => {
    cy.loginAs('admin');
  });

  it('should manage complete admin workflow', () => {
    // Navigate to dashboard
    cy.visit('/admin/dashboard');
    cy.get('[data-testid="admin-dashboard"]').should('be.visible');

    // Check system health
    cy.get('[data-testid="system-health"]').should('contain', 'Healthy');

    // Manage users
    cy.get('[data-testid="gestione-utenti"]').click();
    cy.url().should('include', '/admin/gestioneUtenti');

    // Create new user
    cy.get('[data-testid="add-user-btn"]').click();
    cy.get('[data-testid="user-form"]').should('be.visible');
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('select[name="role"]').select('DISTRIBUTORE');
    cy.get('input[name="firstName"]').type('New');
    cy.get('input[name="lastName"]').type('User');
    
    cy.get('[data-testid="save-user-btn"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');

    // Verify user in list
    cy.get('[data-testid="users-table"]').should('contain', '<EMAIL>');
  });
});
```

### 2. 🏢 Testing DISTRIBUTORE

#### Unit Tests
```javascript
// tests/unit/distributore/GestioneMagazzini.test.js
describe('Gestione Magazzini', () => {
  test('should display warehouses list', async () => {
    const mockWarehouses = [
      { id: 1, name: 'Magazzino Centrale', city: 'Milano' },
      { id: 2, name: 'Magazzino Sud', city: 'Napoli' }
    ];
    
    mockAPI.get('/warehouses').mockResolvedValue({ data: mockWarehouses });
    
    render(<GestioneMagazzini />);
    
    await waitFor(() => {
      expect(screen.getByText('Magazzino Centrale')).toBeInTheDocument();
      expect(screen.getByText('Magazzino Sud')).toBeInTheDocument();
    });
  });

  test('should handle warehouse creation', async () => {
    const newWarehouse = {
      name: 'Nuovo Magazzino',
      address: 'Via Test 123',
      city: 'Roma'
    };

    mockAPI.post('/warehouses').mockResolvedValue({ data: { id: 3, ...newWarehouse } });

    render(<GestioneMagazzini />);
    
    await userEvent.click(screen.getByText('Aggiungi Magazzino'));
    await userEvent.type(screen.getByLabelText('Nome'), newWarehouse.name);
    await userEvent.type(screen.getByLabelText('Indirizzo'), newWarehouse.address);
    await userEvent.type(screen.getByLabelText('Città'), newWarehouse.city);
    await userEvent.click(screen.getByText('Salva'));

    await waitFor(() => {
      expect(mockAPI.post).toHaveBeenCalledWith('/warehouses', newWarehouse);
    });
  });
});
```

#### Integration Tests
```javascript
// tests/integration/distributore/inventory-management.test.js
describe('Inventory Management Workflow', () => {
  test('should manage complete inventory cycle', async () => {
    // Mock data
    const product = { id: 1, description: 'Vino Rosso', brand: 'Test Brand' };
    const warehouse = { id: 1, name: 'Magazzino Centrale' };
    
    mockAPI.get('/products').mockResolvedValue({ data: [product] });
    mockAPI.get('/warehouses').mockResolvedValue({ data: [warehouse] });
    mockAPI.get('/inventory').mockResolvedValue({ data: [] });

    render(<UfficioVendite />);

    // Add product to inventory
    await userEvent.click(screen.getByText('Aggiungi Prodotto'));
    await userEvent.selectOptions(screen.getByLabelText('Prodotto'), '1');
    await userEvent.selectOptions(screen.getByLabelText('Magazzino'), '1');
    await userEvent.type(screen.getByLabelText('Quantità'), '100');
    await userEvent.click(screen.getByText('Aggiungi'));

    // Verify inventory update
    await waitFor(() => {
      expect(mockAPI.post).toHaveBeenCalledWith('/inventory', {
        productId: 1,
        warehouseId: 1,
        quantity: 100
      });
    });

    // Check stock levels
    mockAPI.get('/inventory').mockResolvedValue({ 
      data: [{ productId: 1, warehouseId: 1, quantity: 100, available: 100 }] 
    });

    await userEvent.click(screen.getByText('Aggiorna'));
    
    await waitFor(() => {
      expect(screen.getByText('100')).toBeInTheDocument();
    });
  });
});
```

### 3. 🏪 Testing PDV (Marketplace)

#### Unit Tests
```javascript
// tests/unit/pdv/Marketplace.test.js
describe('Marketplace Component', () => {
  test('should display products grid', async () => {
    const mockProducts = [
      { id: 1, description: 'Prodotto 1', price: 10.50 },
      { id: 2, description: 'Prodotto 2', price: 15.75 }
    ];

    mockAPI.get('/products').mockResolvedValue({ data: mockProducts });

    render(<Marketplace />);

    await waitFor(() => {
      expect(screen.getByText('Prodotto 1')).toBeInTheDocument();
      expect(screen.getByText('€10.50')).toBeInTheDocument();
      expect(screen.getByText('Prodotto 2')).toBeInTheDocument();
      expect(screen.getByText('€15.75')).toBeInTheDocument();
    });
  });

  test('should add product to cart', async () => {
    const product = { id: 1, description: 'Test Product', price: 20.00 };
    
    render(
      <Provider store={store}>
        <Marketplace />
      </Provider>
    );

    await userEvent.click(screen.getByTestId(`add-to-cart-${product.id}`));
    await userEvent.type(screen.getByLabelText('Quantità'), '5');
    await userEvent.click(screen.getByText('Aggiungi al Carrello'));

    // Verify Redux state
    const state = store.getState();
    expect(state.cart.items).toHaveLength(1);
    expect(state.cart.items[0]).toMatchObject({
      productId: product.id,
      quantity: 5,
      unitPrice: product.price
    });
  });
});
```

#### E2E Tests
```javascript
// tests/e2e/pdv/shopping-journey.cy.js
describe('PDV Shopping Journey', () => {
  beforeEach(() => {
    cy.loginAs('pdv');
  });

  it('should complete full shopping workflow', () => {
    // Navigate to marketplace
    cy.visit('/pdv/listinoProdotti');
    cy.get('[data-testid="marketplace"]').should('be.visible');

    // Search for product
    cy.get('[data-testid="search-input"]').type('Vino Rosso');
    cy.get('[data-testid="search-btn"]').click();

    // Add product to cart
    cy.get('[data-testid="product-card"]').first().within(() => {
      cy.get('[data-testid="add-to-cart-btn"]').click();
    });

    cy.get('[data-testid="quantity-input"]').clear().type('6');
    cy.get('[data-testid="confirm-add-btn"]').click();

    // Verify cart badge
    cy.get('[data-testid="cart-badge"]').should('contain', '1');

    // Go to cart
    cy.get('[data-testid="cart-link"]').click();
    cy.url().should('include', '/carrello');

    // Verify cart contents
    cy.get('[data-testid="cart-items"]').should('contain', 'Vino Rosso');
    cy.get('[data-testid="cart-quantity"]').should('contain', '6');

    // Proceed to checkout
    cy.get('[data-testid="checkout-btn"]').click();
    cy.get('[data-testid="order-summary"]').should('be.visible');

    // Complete order
    cy.get('[data-testid="confirm-order-btn"]').click();
    cy.get('[data-testid="success-message"]').should('contain', 'Ordine creato con successo');
  });
});
```

## 🔄 TESTING CROSS-ROLE E WORKFLOW

### Integration Testing tra Ruoli
```javascript
// tests/integration/cross-role/order-workflow.test.js
describe('Complete Order Workflow', () => {
  test('should handle order from PDV to Distributore', async () => {
    // PDV creates order
    const order = {
      customerId: 123,
      items: [
        { productId: 1, quantity: 12, unitPrice: 15.50 }
      ]
    };

    // Mock PDV order creation
    mockAPI.post('/orders').mockResolvedValue({ 
      data: { id: 1001, ...order, status: 'PENDING' } 
    });

    // PDV side
    render(<PDVOrderForm />);
    // ... PDV order creation logic

    // Distributore receives order
    mockAPI.get('/orders').mockResolvedValue({ 
      data: [{ id: 1001, ...order, status: 'PENDING' }] 
    });

    render(<DistributoreOrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.getByText('PENDING')).toBeInTheDocument();
    });

    // Distributore approves order
    await userEvent.click(screen.getByTestId('approve-order-1001'));
    
    expect(mockAPI.patch).toHaveBeenCalledWith('/orders/1001', { 
      status: 'APPROVED' 
    });
  });
});
```

## 🏗️ TESTING SUDDIVISIONE PRODOTTI

### Feature Flag Testing
```javascript
// tests/unit/products/feature-flags.test.js
describe('Product Feature Flags', () => {
  test('should show only MARKETPLACE features for PDV-only product', () => {
    const mockLicense = {
      product: 'MARKETPLACE',
      roles: ['ADMIN', 'PDV'],
      features: ['marketplace', 'cart', 'orders']
    };

    render(
      <LicenseProvider license={mockLicense}>
        <App />
      </LicenseProvider>
    );

    // Should show PDV features
    expect(screen.getByText('Marketplace')).toBeInTheDocument();
    expect(screen.getByText('Carrello')).toBeInTheDocument();

    // Should NOT show Distributore features
    expect(screen.queryByText('Gestione Magazzini')).not.toBeInTheDocument();
    expect(screen.queryByText('Ufficio Vendite')).not.toBeInTheDocument();
  });

  test('should show ENTERPRISE features for full license', () => {
    const mockLicense = {
      product: 'ENTERPRISE',
      roles: ['ADMIN', 'DISTRIBUTORE', 'PDV', 'AGENTE'],
      features: ['all']
    };

    render(
      <LicenseProvider license={mockLicense}>
        <App />
      </LicenseProvider>
    );

    // Should show all features
    expect(screen.getByText('Gestione Magazzini')).toBeInTheDocument();
    expect(screen.getByText('Ufficio Vendite')).toBeInTheDocument();
    expect(screen.getByText('Marketplace')).toBeInTheDocument();
    expect(screen.getByText('Gestione Agenti')).toBeInTheDocument();
  });
});
```

### Bundle Size Testing
```javascript
// tests/performance/bundle-analysis.test.js
describe('Bundle Size Analysis', () => {
  test('MARKETPLACE bundle should be smaller than ENTERPRISE', async () => {
    const marketplaceBundleSize = await getBundleSize('MARKETPLACE');
    const enterpriseBundleSize = await getBundleSize('ENTERPRISE');

    expect(marketplaceBundleSize).toBeLessThan(enterpriseBundleSize * 0.4);
  });

  test('should not include unused components in product bundles', async () => {
    const marketplaceBundle = await getBundleContents('MARKETPLACE');
    
    // Should NOT include Distributore components
    expect(marketplaceBundle).not.toContain('GestioneMagazzini');
    expect(marketplaceBundle).not.toContain('UfficioVendite');
    
    // Should include PDV components
    expect(marketplaceBundle).toContain('Marketplace');
    expect(marketplaceBundle).toContain('Cart');
  });
});
```

## 📊 PERFORMANCE TESTING

### Load Testing per Ruolo
```javascript
// tests/performance/load-testing.js
import { check } from 'k6';
import http from 'k6/http';

export let options = {
  scenarios: {
    pdv_marketplace: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 50 },
        { duration: '5m', target: 100 },
        { duration: '2m', target: 0 },
      ],
    },
    distributore_dashboard: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 20 },
        { duration: '5m', target: 40 },
        { duration: '2m', target: 0 },
      ],
    },
  },
};

export default function () {
  // Test PDV marketplace
  let response = http.get('http://localhost:3000/pdv/listinoProdotti');
  check(response, {
    'PDV marketplace loads in <2s': (r) => r.timings.duration < 2000,
    'PDV status is 200': (r) => r.status === 200,
  });

  // Test Distributore dashboard
  response = http.get('http://localhost:3000/distributore/dashboard');
  check(response, {
    'Distributore dashboard loads in <3s': (r) => r.timings.duration < 3000,
    'Distributore status is 200': (r) => r.status === 200,
  });
}
```

## 🚀 IMPLEMENTAZIONE TESTING

### Setup Testing Environment
```bash
# Install testing dependencies
npm install --save-dev \
  @testing-library/react \
  @testing-library/jest-dom \
  @testing-library/user-event \
  cypress \
  msw \
  artillery \
  lighthouse-ci

# Setup test scripts
npm run test:unit          # Jest unit tests
npm run test:integration   # Integration tests
npm run test:e2e          # Cypress E2E tests
npm run test:performance  # Performance tests
npm run test:all          # All tests
```

### CI/CD Integration
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '14'
      - run: npm ci
      - run: npm run test:unit

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: cypress-io/github-action@v2
        with:
          start: npm start
          wait-on: 'http://localhost:3000'

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - run: npm ci
      - run: npm run build
      - run: npm run test:performance
```

### Test Coverage Goals
- **Unit Tests**: >90% coverage
- **Integration Tests**: Tutti i workflow critici
- **E2E Tests**: User journey completi per ogni ruolo
- **Performance Tests**: <2s load time, <100MB memory usage
- **Cross-browser**: Chrome, Firefox, Safari, Edge

Questo piano di testing garantirà la qualità del sistema e faciliterà la suddivisione in prodotti separati mantenendo la stabilità e le performance. Vuoi che proceda con l'implementazione di qualche sezione specifica del testing?
