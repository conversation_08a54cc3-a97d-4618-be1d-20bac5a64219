{"version": 3, "file": "fail.js", "sourceRoot": "", "sources": ["../../src/fail.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,eAAe,CAAC,OAAe,EAAE,EAAU,EAAE,EAAE;IAC7C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;IACtC,IAAI,EAAE;QAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;IACjC,OAAO,CAAC,KAAK,EAAE,CAAA;AACjB,CAAC,CAAA", "sourcesContent": ["import chalk from 'chalk'\nimport * as console from './console.js'\nexport default (message: string, er?: Error) => {\n  console.error(chalk.red.bold(message))\n  if (er) console.error(er.message)\n  console.print()\n}\n"]}