/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import { TAP } from '@tapjs/core';
export { Base, BaseOpts, Counts, Extra, Lists, Minimal, Spawn, SpawnEvents, SpawnOpts, Stdin, StdinOpts, TapBaseEvents, TapFile, TapFileEvents, TapFileOpts, TapPlugin, TestBase, TestBaseEvents, TestBaseOpts, Worker, WorkerEvents, WorkerOpts, } from '@tapjs/core';
export { Test } from '@tapjs/test';
export type { TestOpts } from '@tapjs/test';
export type { TAP };
export { after, afterEach, bailout, before, beforeEach, comment, doesNotThrow, emits, end, error, fail, has, hasStrict, match, matchOnly, matchOnlyStrict, matchStrict, not, notHas, notHasStrict, notMatch, notMatchOnly, notMatchOnlyStrict, notMatchStrict, notOk, notSame, ok, only, pass, plan, pragma, same, skip, strictNotSame, test, throws, timeout, todo, type, mockRequire, mockImport, createMock, intercept, captureFn, capture, testdir, fixture, matchSnapshot, stdin, stdinOnly, spawn, worker, };
export declare const t: TAP;
declare const bailout: (message?: string | undefined) => void, comment: (...args: any[]) => void, end: () => TAP, fail: (...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, pass: (...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, plan: (n: number, comment?: string | undefined) => void, pragma: (set: {
    [k: string]: boolean;
}) => void, skip: {
    (name: string, extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (name: string, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
}, stdinOnly: <T extends import("@tapjs/core").BaseOpts>(extra?: (T & {
    tapStream?: import("stream").Readable | import("minipass").Minipass<string | Buffer, import("minipass").Minipass.ContiguousData, import("minipass").Minipass.Events<string | Buffer>> | undefined;
}) | undefined) => void, test: {
    (name: string, extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (name: string, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
}, timeout: (options?: {
    expired?: string | undefined;
    signal?: NodeJS.Signals | null | undefined;
} | undefined) => void, todo: {
    (name: string, extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (name: string, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
    (cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts> & import("@tapjs/test").BuiltPlugins>;
};
/**
 * If the property exists, use it, otherwise treat it as a known undefined
 */
export type Maybe<T, K extends symbol | number | string> = K extends keyof T ? T : T & {
    [k in K]: undefined;
};
/**
 * Type to make TypeScript ok with accessing an unknown property,
 * and just treating it as undefined if the plugin isn't loaded.
 */
export type MaybePlugin<P extends (...a: any[]) => any> = Maybe<TAP, keyof ReturnType<P>>;
declare const only: {
    (name: string, extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>>;
    (name: string, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>>;
    (extra: import("@tapjs/test").TestOpts, cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>>;
    (cb: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>) => any): import("@tapjs/core").PromiseWithSubtest<import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>>;
};
declare const after: (fn: () => any) => void;
declare const before: (fn: () => any) => void;
declare const afterEach: (fn: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>) => any) => void;
declare const beforeEach: (fn: (t: import("@tapjs/test").Test<import("@tapjs/test").BuiltPlugins, import("@tapjs/test").TestOpts>) => any) => void;
declare const ok: (obj: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notOk: (obj: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, not: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, type: (obj: any, klass: string | Function, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, same: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notSame: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, strictNotSame: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, has: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notHas: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, hasStrict: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notHasStrict: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, match: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notMatch: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, matchOnly: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notMatchOnly: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, matchStrict: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notMatchStrict: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, matchOnlyStrict: (found: any, wanted: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, notMatchOnlyStrict: (found: any, doNotWant: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean, throws: (fn: Function | (() => any), ...[wanted, msg, extra]: import("@tapjs/asserts").ThrowsArgs) => boolean | Error, doesNotThrow: (fn: Function | (() => any), ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean | Error, emits: (emitter: import("events")<[never]> | EventTarget, event: string, ...[msg, extra]: import("@tapjs/core").MessageExtra) => Promise<void>, error: (er: unknown, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean;
declare const mockRequire: <T = any>(module: string, mocks?: Record<string, any> | undefined) => T, mockImport: <T = any>(module: string, mocks?: Record<string, any> | undefined) => Promise<T>, createMock: <B extends any[] | {
    [k: string]: any;
    [k: number]: any;
    [k: symbol]: any;
}, O extends any[] | {
    [k: string]: any;
}>(bases: B, overrides: O) => import("@tapjs/mock").MockedObject<B, O>;
declare const captureFn: <F extends (this: any, ...a: any[]) => any>(original: F) => F & {
    calls: import("@tapjs/intercept").CaptureResult<F>[];
    args: () => import("@tapjs/intercept").TupleUnion<import("@tapjs/intercept").FilterUnknown<import("@tapjs/intercept").OverloadParamsTuple<F>>>[];
}, capture: <T extends {}, M extends import("@tapjs/intercept").Methods<T>>(obj: T, method: M, impl?: ((this: T, ...a: any[]) => any) | undefined) => import("@tapjs/intercept").CaptureResultsMethod<T, M, T[M]>, intercept: <T extends object>(obj: T, prop: keyof T, desc?: PropertyDescriptor | undefined, strictMode?: boolean | undefined) => import("@tapjs/intercept").InterceptResultsMethod;
declare const matchSnapshot: (found: any, ...[msg, extra]: import("@tapjs/core").MessageExtra) => boolean;
declare const spawn: {
    (cmd: string): import("@tapjs/spawn").PromiseWithSpawn;
    (cmd: string, options: import("@tapjs/spawn").SpawnOpts, name?: string | undefined): import("@tapjs/spawn").PromiseWithSpawn;
    (cmd: string, args: string | string[], name?: string | undefined): import("@tapjs/spawn").PromiseWithSpawn;
    (cmd: string, args: string | string[], options: import("@tapjs/spawn").SpawnOpts, name?: string | undefined): import("@tapjs/spawn").PromiseWithSpawn;
};
declare const stdin: {
    (): import("@tapjs/stdin").PromiseWithStdin;
    (name: string): import("@tapjs/stdin").PromiseWithStdin;
    (name: string, extra: import("@tapjs/core").StdinOpts): import("@tapjs/stdin").PromiseWithStdin;
    (extra: import("@tapjs/core").StdinOpts): import("@tapjs/stdin").PromiseWithStdin;
};
declare const worker: {
    (filename: string): import("@tapjs/worker").PromiseWithWorker;
    (filename: string, name?: string | undefined): import("@tapjs/worker").PromiseWithWorker;
    (filename: string, options: import("@tapjs/core").WorkerOpts, name?: string | undefined): import("@tapjs/worker").PromiseWithWorker;
};
declare const testdir: (content?: import("@tapjs/fixture").FixtureDirContent | undefined) => string, fixture: <T extends import("@tapjs/fixture").FixtureType>(type: T, content: import("@tapjs/fixture").FixtureContent<T>) => import("@tapjs/fixture").Fixture<T>;
//# sourceMappingURL=main.d.ts.map