import { Parser } from './index.js';
import { Plan } from './plan.js';
/**
 * An indication that a violation of the TAP specification has occurred
 *
 * This can indicate a test point that exceeds the plan, a test point
 * encountered after a trailing plan, or in the case of `pragma +strict`,
 * any non-TAP data.
 */
export type TapError = Result | {
    tapError: string;
    [k: string]: any;
};
/**
 * A representation of a TestPoint result, with diagnostics if present.
 */
export declare class Result {
    ok: boolean;
    name: string;
    id: number;
    buffered: boolean;
    tapError: string | null;
    skip: boolean | string;
    todo: boolean | string;
    previous: Result | null;
    plan: Plan | null;
    diag: any | null;
    time: number | null;
    fullname: string;
    closingTestPoint: boolean;
    constructor(parsed: string[], parser: Parser);
}
//# sourceMappingURL=result.d.ts.map