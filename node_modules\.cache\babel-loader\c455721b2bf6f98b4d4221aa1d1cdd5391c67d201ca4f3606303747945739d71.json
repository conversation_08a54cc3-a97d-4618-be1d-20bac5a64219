{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\gestioneConsegne.jsx\";\n/*\n *\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneConsegne - visualizzazione e assegnazione dei documenti relativi alle consegne\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaAutista from \"../logistica/selezionaAutista\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\nimport ModificaStato from \"../../aggiunta_dati/modificaStato\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport \"../../css/DataTableDemo.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass AssegnaConsegne extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      description: \"\",\n      createAt: \"\",\n      updateAt: \"\",\n      isValid: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value,\n        loading: true\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      var url = 'documents?idWarehouses=' + e.value + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: [],\n      results3: [],\n      results4: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      resultDialog5: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: false,\n      dateFilter: null,\n      autisti: [],\n      idEmployee: 0,\n      mex: \"\",\n      firstName: \"\",\n      address: \"\",\n      indFatt: \"\",\n      selectedWarehouse: null,\n      displayed: false,\n      respLog: '',\n      search: '',\n      value: null,\n      value2: null,\n      selectedDocuments: null,\n      clienti: null,\n      param: '?idWarehouses=',\n      totalRecords: 0,\n      selectedRetailer: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n      var param = '?idRetailer=';\n      var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n      if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n        var url = 'documents' + param + e.value.code + '&idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url).then(res => {\n          var documento = [];\n          res.data.documents.forEach(element => {\n            var _element$tasks2;\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n              note: element.note,\n              erpSync: element.erpSync,\n              totalPayed: new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(parseFloat(element.totalPayed)),\n              total: new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(parseFloat(element.totalTaxed))\n            };\n            documento.push(x);\n          });\n          this.setState({\n            results: documento,\n            results2: documento,\n            totalRecords: res.data.totalCount,\n            param: param,\n            lazyParams: {\n              first: this.state.lazyParams.first,\n              rows: this.state.lazyParams.rows,\n              page: this.state.lazyParams.page,\n              pageCount: res.data.totalCount / this.state.lazyParams.rows\n            },\n            loading: false\n          });\n        }).catch(e => {\n          var _e$response3, _e$response4;\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    };\n    this.retailers = [];\n    this.autista = [];\n    this.warehouse = [];\n    this.loadLazyTimeout = null;\n    //Dichiarazione funzioni e metodi\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.modifica = this.modifica.bind(this);\n    this.hideDialogModifica = this.hideDialogModifica.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.filterDate = this.filterDate.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.creaFATACC = this.creaFATACC.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog5: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"employees/?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli autisti. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'retailers/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.retailers.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizza dettagli\n  hidevisualizzaDett() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var autisti = [];\n    var respLog = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'AUTISTA' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_assigned);\n          autisti.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'LOGISTICA' && result.tasks === null) {\n          var tasks = 0;\n          tasks = parseInt(element.task_create) + parseInt(element.task_assigned);\n          respLog.push({\n            label: element.first_name + ' (task assegnate: ' + tasks + ')',\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.autista = autisti;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog: true,\n      autisti: autisti,\n      respLog: respLog,\n      mex: message\n    });\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var autisti = [];\n    var respLog = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          /* if (element.idUser.role === 'AUTISTA') {\n              autisti.push({\n                  label: element.idUser.idRegistry.firstName,\n                  value: element.idUser.idEmployee.id,\n              });\n          } else */\n          if (element.role === 'LOGISTICA') {\n            var tasks = 0;\n            tasks = parseInt(element.task_create) + parseInt(element.task_assigned);\n            respLog.push({\n              label: element.first_name + ' (task assegnate: ' + tasks + ')',\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.autista = autisti;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog: true,\n        autisti: autisti,\n        respLog: respLog,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null);\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'AUTISTA') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_assigned);\n                autisti.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              } /*  else if (element.idUser.role === 'LOGISTICA') {\n                   respLog.push({\n                       label: element.idUser.idRegistry.firstName,\n                       value: element.idUser.idEmployee.id,\n                   });\n                } */\n            });\n          }\n          this.autista = autisti;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog: true,\n            autisti: autisti,\n            respLog: respLog,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di autisti assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'AUTISTA') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_assigned);\n              autisti.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            } /*  else if (element.idUser.role === 'LOGISTICA') {\n                 respLog.push({\n                     label: element.idUser.idRegistry.firstName,\n                     value: element.idUser.idEmployee.id,\n                 });\n              } */\n          });\n        }\n        this.autista = autisti;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog: true,\n          autisti: autisti,\n          respLog: respLog,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  modifica(result) {\n    if (result.tasks !== null) {\n      delete result.status;\n      this.setState({\n        result,\n        resultDialog3: true\n      });\n    } else {\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"E' necessario assegnare la lavorazione prima di poterne modificare lo stato\",\n        life: 3000\n      });\n    }\n  }\n  hideDialogModifica() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        param: '?idWarehouses='\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        param: '?idWarehouses='\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  filterDate(e, key) {\n    var filter = [];\n    if (key === 'dataConsegna') {\n      filter = this.state.results2.filter(el => new Date(el.deliveryDate).toLocaleDateString() === new Date(e.value).toLocaleDateString());\n      this.setState({\n        value: e.value,\n        results: filter,\n        value2: null\n      });\n    } else {\n      filter = this.state.results2.filter(el => new Date(el.documentDate).toLocaleDateString() === new Date(e.value).toLocaleDateString());\n      this.setState({\n        value2: e.value,\n        results: filter,\n        value: null\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n      if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n        var url = this.state.param !== '?idWarehouses=' ? 'documents' + this.state.param + this.state.selectedRetailer.code + '&idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + event.rows + '&skip=' + event.page : 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + event.rows + '&skip=' + event.page;\n        await APIRequest(\"GET\", url).then(res => {\n          var documento = [];\n          res.data.documents.forEach(element => {\n            var _element$tasks6;\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status,\n              note: element.note,\n              erpSync: element.erpSync,\n              totalPayed: new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(parseFloat(element.totalPayed)),\n              total: new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(parseFloat(element.totalTaxed))\n            };\n            documento.push(x);\n          });\n          this.setState({\n            results: documento,\n            results2: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: event,\n            loading: false\n          });\n        }).catch(e => {\n          var _e$response15, _e$response16;\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  selectionChangeHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  async creaFATACC(result) {\n    if (result.tasks !== null) {\n      var _result$idDocumentHea;\n      let tipi = [];\n      let url = '';\n      if (((_result$idDocumentHea = result.idDocumentHeadOrig) === null || _result$idDocumentHea === void 0 ? void 0 : _result$idDocumentHea.length) > 0) {\n        for (var x = 0; x < result.idDocumentHeadOrig.length; x++) {\n          url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest;\n          await APIRequest(\"GET\", url).then(res => {\n            console.log(res.data);\n            tipi.push(res.data.type);\n          }).catch(e => {\n            console.log(e);\n          });\n        }\n        var find = tipi.find(el => el === 'CLI-FATACC');\n        if (find !== undefined) {\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Il documento è già associato ad una task di tipo CLI-FATACC\",\n            life: 3000\n          });\n        } else {\n          url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-FATACC\";\n          //Chiamata axios per la creazione del documento\n          await APIRequest('POST', url).then(res => {\n            console.log(res.data);\n            this.toast.show({\n              severity: 'success',\n              summary: 'Ottimo',\n              detail: \"Il documento è stato inserito con successo\",\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          }).catch(e => {\n            var _e$response19, _e$response20;\n            console.log(e);\n            this.toast.show({\n              severity: 'error',\n              summary: 'Siamo spiacenti',\n              detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n              life: 3000\n            });\n          });\n        }\n      } else {\n        url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-FATACC\";\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', url).then(res => {\n          console.log(res.data);\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il documento è stato inserito con successo\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response21, _e$response22;\n          console.log(e);\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response21 = e.response) === null || _e$response21 === void 0 ? void 0 : _e$response21.data) !== undefined ? (_e$response22 = e.response) === null || _e$response22 === void 0 ? void 0 : _e$response22.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario che il documento abbia una task associata in stato delivered per creare la CLI-FATACC\",\n        life: 3000\n      });\n    }\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog5: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 806,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              firstName: this.state.firstName,\n              address: this.state.address,\n              indFatt: this.state.indFatt,\n              mex: this.state.mex,\n              doc: true,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialogModifica,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter4 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"deliveryDate\",\n      header: Costanti.DCons,\n      body: \"deliveryDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.manager.idUser.username\",\n      header: Costanti.Responsabile,\n      showHeader: true\n    }, {\n      field: \"operator\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"status\",\n      header: Costanti.Stato,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"total\",\n      header: Costanti.Tot,\n      showHeader: true\n    }, {\n      field: \"totalPayed\",\n      header: Costanti.TotPag,\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-plus-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 929,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'assigned'\n    }, {\n      name: Costanti.CambiaStato,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 49\n      }, this),\n      handler: this.modifica\n    }, {\n      name: Costanti.CreaFATACC,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-euro\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 48\n      }, this),\n      handler: this.creaFATACC,\n      status: 'delivered'\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneConsegne\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 946,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\",\n                emptyMessage: \"Nessun elemento disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 950,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 949,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionChangeHandler(e),\n          showExtraButton2: true,\n          actionExtraButton2: this.assegnaLavorazioni,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Ordini\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaAutista, {\n          result: this.state.result,\n          autista: this.autista,\n          respLog: this.state.respLog,\n          distributore: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          documento: this.state.result,\n          result: this.state.results3,\n          results: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1013,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.CambiaStato,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialogModifica,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field\",\n          children: /*#__PURE__*/_jsxDEV(ModificaStato, {\n            result: this.state.result,\n            results: this.state.results\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1038,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog5,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter4,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\",\n            emptyMessage: \"Nessun elemento disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog4,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1054,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1056,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1053,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1058,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedRetailer,\n          options: this.retailers,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona cliente\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1052,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default AssegnaConsegne;", "map": {"version": 3, "names": ["React", "Component", "Nav", "CustomDataTable", "SelezionaAutista", "VisualizzaDocumenti", "ModificaStato", "Print", "JoyrideGen", "Dropdown", "Toast", "<PERSON><PERSON>", "Dialog", "Sidebar", "<PERSON><PERSON>", "APIRequest", "jsxDEV", "_jsxDEV", "AssegnaConsegne", "constructor", "props", "emptyResult", "id", "description", "createAt", "updateAt", "<PERSON><PERSON><PERSON><PERSON>", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "loading", "window", "sessionStorage", "setItem", "url", "state", "lazyParams", "rows", "page", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$tasks", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "firstName", "idSupplying", "documentDate", "deliveryDate", "documentBodies", "tasks", "status", "note", "erpSync", "totalPayed", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "parseFloat", "total", "totalTaxed", "push", "results", "results2", "totalRecords", "totalCount", "first", "pageCount", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "results3", "results4", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "resultDialog5", "submitted", "result", "globalFilter", "dateFilter", "autisti", "idEmployee", "mex", "address", "indFatt", "displayed", "respLog", "search", "value2", "selectedDocuments", "clienti", "param", "<PERSON><PERSON><PERSON><PERSON>", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "idWarehouse", "JSON", "parse", "getItem", "code", "_element$tasks2", "_e$response3", "_e$response4", "retailers", "au<PERSON>", "warehouse", "loadLazyTimeout", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "modifica", "hideDialogModifica", "reset", "resetDesc", "filterDate", "assegnaLavorazioni", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onPage", "onSort", "onFilter", "creaFATACC", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "documentBody", "task", "_e$response1", "_e$response10", "DateTimeFormat", "day", "month", "year", "Date", "role", "taskAss", "parseInt", "task_create", "task_assigned", "label", "first_name", "last_name", "idemployee", "_objectSpread", "filter", "el", "length", "FilterOp", "operator", "_element$tasks4", "_e$response11", "_e$response12", "_element$tasks5", "_e$response13", "_e$response14", "key", "toLocaleDateString", "event", "clearTimeout", "setTimeout", "_element$tasks6", "_e$response15", "_e$response16", "Math", "random", "field", "_element$tasks7", "_e$response17", "_e$response18", "loadLazyData", "_result$idDocumentHea", "tipi", "idDocumentHeadOrig", "idDocDest", "find", "idDocument", "location", "reload", "_e$response19", "_e$response20", "_e$response21", "_e$response22", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "doc", "disabled", "resultDialogFooter3", "resultDialogFooter4", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "DCons", "Responsabile", "Operatore", "Stato", "<PERSON><PERSON>", "TotPag", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "icon", "handler", "assegnaLavorazione", "CambiaStato", "CreaFATACC", "filterDnone", "ref", "gestioneConsegne", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "emptyMessage", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "disabledExtraButton2", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "classInputSearch", "fileNames", "visible", "modal", "footer", "onHide", "distributore", "DocAll", "draggable", "orders", "Primadiproseguire", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/gestioneConsegne.jsx"], "sourcesContent": ["/*\n *\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneConsegne - visualizzazione e assegnazione dei documenti relativi alle consegne\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaAutista from \"../logistica/selezionaAutista\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\nimport ModificaStato from \"../../aggiunta_dati/modificaStato\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport \"../../css/DataTableDemo.css\";\n\nclass AssegnaConsegne extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        description: \"\",\n        createAt: \"\",\n        updateAt: \"\",\n        isValid: \"\",\n    };\n    constructor(props) {\n        super(props);\n        //Dichiarazione variabili di scena\n        this.state = {\n            results: null,\n            results2: [],\n            results3: [],\n            results4: [],\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            resultDialog5: false,\n            submitted: false,\n            result: this.emptyResult,\n            globalFilter: null,\n            loading: false,\n            dateFilter: null,\n            autisti: [],\n            idEmployee: 0,\n            mex: \"\",\n            firstName: \"\",\n            address: \"\",\n            indFatt: \"\",\n            selectedWarehouse: null,\n            displayed: false,\n            respLog: '',\n            search: '',\n            value: null,\n            value2: null,\n            selectedDocuments: null,\n            clienti: null,\n            param: '?idWarehouses=',\n            totalRecords: 0,\n            selectedRetailer: null,\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedRetailer: e.value\n            });\n\n            var param = '?idRetailer='\n\n            var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n            if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n                var url = 'documents' + param + e.value.code + '&idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n                await APIRequest(\"GET\", url)\n                    .then((res) => {\n                        var documento = []\n                        res.data.documents.forEach(element => {\n                            var x = {\n                                id: element.id,\n                                number: element.number,\n                                type: element.type,\n                                retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                                documentDate: element.documentDate,\n                                deliveryDate: element.deliveryDate,\n                                documentBodies: element.documentBodies,\n                                tasks: element.tasks,\n                                status: element.tasks?.status,\n                                note: element.note,\n                                erpSync: element.erpSync,\n                                totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                                total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                            }\n                            documento.push(x)\n                        })\n                        this.setState({\n                            results: documento,\n                            results2: documento,\n                            totalRecords: res.data.totalCount,\n                            param: param,\n                            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                            loading: false\n                        });\n                    })\n                    .catch((e) => {\n                        console.log(e);\n                        this.toast.show({\n                            severity: \"error\",\n                            summary: \"Siamo spiacenti\",\n                            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                            life: 3000,\n                        });\n                    });\n            }\n        };\n        this.retailers = []\n        this.autista = [];\n        this.warehouse = [];\n        this.loadLazyTimeout = null;\n        //Dichiarazione funzioni e metodi\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.modifica = this.modifica.bind(this);\n        this.hideDialogModifica = this.hideDialogModifica.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.filterDate = this.filterDate.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.creaFATACC = this.creaFATACC.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog5: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"employees/?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli autisti. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest('GET', 'retailers/')\n            .then(res => {\n                res.data.forEach(element => {\n                    if (element && element.idRegistry) {\n                        var x = {\n                            name: element.idRegistry.firstName || 'Nome non disponibile',\n                            code: element.id || 0\n                        }\n                        this.retailers.push(x)\n                    }\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value, loading: true });\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        var url = 'documents?idWarehouses=' + e.value + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        deliveryDate: element.deliveryDate,\n                        documentBodies: element.documentBodies,\n                        tasks: element.tasks,\n                        status: element.tasks?.status,\n                        note: element.note,\n                        erpSync: element.erpSync,\n                        totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                        total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results2: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizza dettagli\n    hidevisualizzaDett() {\n        this.setState({\n            resultDialog2: false,\n        });\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            submitted: false,\n            resultDialog: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var autisti = [];\n        var respLog = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'AUTISTA' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_assigned)\n                    autisti.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'LOGISTICA' && result.tasks === null) {\n                    var tasks = 0\n                    tasks = parseInt(element.task_create) + parseInt(element.task_assigned)\n                    respLog.push({\n                        label: element.first_name + ' (task assegnate: ' + tasks + ')',\n                        value: element.idemployee,\n                    });\n                }\n            })\n        }\n        this.autista = autisti;\n        this.setState({\n            result: { ...result },\n            resultDialog: true,\n            autisti: autisti,\n            respLog: respLog,\n            mex: message,\n        });\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var autisti = [];\n        var respLog = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    /* if (element.idUser.role === 'AUTISTA') {\n                        autisti.push({\n                            label: element.idUser.idRegistry.firstName,\n                            value: element.idUser.idEmployee.id,\n                        });\n                    } else */\n                    if (element.role === 'LOGISTICA') {\n                        var tasks = 0\n                        tasks = parseInt(element.task_create) + parseInt(element.task_assigned)\n                        respLog.push({\n                            label: element.first_name + ' (task assegnate: ' + tasks + ')',\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.autista = autisti;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog: true,\n                autisti: autisti,\n                respLog: respLog,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'AUTISTA') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_assigned)\n                                autisti.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }/*  else if (element.idUser.role === 'LOGISTICA') {\n                                respLog.push({\n                                    label: element.idUser.idRegistry.firstName,\n                                    value: element.idUser.idEmployee.id,\n                                });\n                            } */\n                        })\n                    }\n                    this.autista = autisti;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog: true,\n                        autisti: autisti,\n                        respLog: respLog,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di autisti assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'AUTISTA') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_assigned)\n                            autisti.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }/*  else if (element.idUser.role === 'LOGISTICA') {\n                            respLog.push({\n                                label: element.idUser.idRegistry.firstName,\n                                value: element.idUser.idEmployee.id,\n                            });\n                        } */\n                    })\n                }\n                this.autista = autisti;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog: true,\n                    autisti: autisti,\n                    respLog: respLog,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n    modifica(result) {\n        if (result.tasks !== null) {\n            delete result.status\n            this.setState({\n                result,\n                resultDialog3: true\n            })\n        } else {\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: \"E' necessario assegnare la lavorazione prima di poterne modificare lo stato\",\n                life: 3000,\n            });\n        }\n    }\n    hideDialogModifica() {\n        this.setState({\n            resultDialog3: false\n        })\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', param: '?idWarehouses=' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', param: '?idWarehouses=' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    filterDate(e, key) {\n        var filter = []\n        if (key === 'dataConsegna') {\n            filter = this.state.results2.filter(el => new Date(el.deliveryDate).toLocaleDateString() === new Date(e.value).toLocaleDateString())\n            this.setState({ value: e.value, results: filter, value2: null })\n        } else {\n            filter = this.state.results2.filter(el => new Date(el.documentDate).toLocaleDateString() === new Date(e.value).toLocaleDateString())\n            this.setState({ value2: e.value, results: filter, value: null })\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n            if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n\n                var url = this.state.param !== '?idWarehouses=' ? 'documents' + this.state.param + this.state.selectedRetailer.code + '&idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + event.rows + '&skip=' + event.page : 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + event.rows + '&skip=' + event.page;\n                await APIRequest(\"GET\", url)\n                    .then((res) => {\n                        var documento = []\n                        res.data.documents.forEach(element => {\n                            var x = {\n                                id: element.id,\n                                number: element.number,\n                                type: element.type,\n                                retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                                documentDate: element.documentDate,\n                                deliveryDate: element.deliveryDate,\n                                documentBodies: element.documentBodies,\n                                tasks: element.tasks,\n                                status: element.tasks?.status,\n                                note: element.note,\n                                erpSync: element.erpSync,\n                                totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                                total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                            }\n                            documento.push(x)\n                        })\n                        this.setState({\n                            results: documento,\n                            results2: documento,\n                            totalRecords: res.data.totalCount,\n                            lazyParams: event,\n                            loading: false\n                        });\n                    })\n                    .catch((e) => {\n                        console.log(e);\n                        this.toast.show({\n                            severity: \"error\",\n                            summary: \"Siamo spiacenti\",\n                            detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                            life: 3000,\n                        });\n                    });\n            }\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,FOR-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer ? element.idRetailer.idRegistry.firstName : element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    selectionChangeHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n    async creaFATACC(result) {\n        if (result.tasks !== null) {\n            let tipi = []\n            let url = ''\n            if (result.idDocumentHeadOrig?.length > 0) {\n                for (var x = 0; x < result.idDocumentHeadOrig.length; x++) {\n                    url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest\n                    await APIRequest(\"GET\", url)\n                        .then((res) => {\n                            console.log(res.data)\n                            tipi.push(res.data.type)\n                        })\n                        .catch((e) => {\n                            console.log(e);\n                        });\n                }\n                var find = tipi.find(el => el === 'CLI-FATACC')\n                if (find !== undefined) {\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Il documento è già associato ad una task di tipo CLI-FATACC\", life: 3000 });\n                } else {\n                    url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-FATACC\"\n                    //Chiamata axios per la creazione del documento\n                    await APIRequest('POST', url)\n                        .then(res => {\n                            console.log(res.data);\n                            this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.log(e)\n                            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        })\n                }\n            } else {\n                url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-FATACC\"\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', url)\n                    .then(res => {\n                        console.log(res.data);\n                        this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario che il documento abbia una task associata in stato delivered per creare la CLI-FATACC\", life: 3000 });\n        }\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog5: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog4: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog4: false\n        })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                firstName={this.state.firstName}\n                                address={this.state.address}\n                                indFatt={this.state.indFatt}\n                                mex={this.state.mex}\n                                doc={true}\n                                disabled={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialogModifica}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const resultDialogFooter4 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"retailer\",\n                header: Costanti.cliente,\n                body: \"retailer\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"deliveryDate\",\n                header: Costanti.DCons,\n                body: \"deliveryDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.manager.idUser.username\",\n                header: Costanti.Responsabile,\n                showHeader: true,\n            },\n            {\n                field: \"operator\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"status\",\n                header: Costanti.Stato,\n                body: \"assigned\",\n                showHeader: true,\n            },\n            {\n                field: \"total\",\n                header: Costanti.Tot,\n                showHeader: true,\n            },\n            {\n                field: \"totalPayed\",\n                header: Costanti.TotPag,\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: \"ERP Sync\",\n                body: \"erpSync\",\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-plus-circle\" />, handler: this.editResult, status: 'assigned' },\n            { name: Costanti.CambiaStato, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.modifica },\n            { name: Costanti.CreaFATACC, icon: <i className=\"pi pi-euro\" />, handler: this.creaFATACC, status: 'delivered' },\n        ];\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => (this.toast = el)} />\n                {/* Il componente NavLogistica contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.gestioneConsegne}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => (this.dt = el)}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionChangeHandler(e)}\n                        showExtraButton2={true}\n                        actionExtraButton2={this.assegnaLavorazioni}\n                        labelExtraButton2={Costanti.assegnaLavorazioni}\n                        disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Ordini\"\n                    />\n                </div>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaAutista result={this.state.result} autista={this.autista} respLog={this.state.respLog} distributore={true} />\n                </Dialog>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter2}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        documento={this.state.result}\n                        result={this.state.results3}\n                        results={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={Costanti.CambiaStato}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialogModifica}\n                >\n                    <div className=\"p-field\">\n                        <ModificaStato result={this.state.result} results={this.state.results} />\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog5} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter4}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog4} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedRetailer} options={this.retailers} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default AssegnaConsegne;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,eAAe,SAASjB,SAAS,CAAC;EASpCkB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IAVJ;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACb,CAAC;IA+ND;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG,KAAK;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5DC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEP,CAAC,CAACG,KAAK,CAAC;MACrD,IAAIK,GAAG,GAAG,yBAAyB,GAAGR,CAAC,CAACG,KAAK,GAAG,qCAAqC,GAAG,IAAI,CAACM,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MAC1J,MAAMzB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;YACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;YAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;YACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;YACpBC,MAAM,GAAAb,cAAA,GAAED,OAAO,CAACa,KAAK,cAAAZ,cAAA,uBAAbA,cAAA,CAAea,MAAM;YAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;YAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;UACjJ,CAAC;UACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV8C,OAAO,EAAEhC,SAAS;UAClBiC,QAAQ,EAAEjC,SAAS;UACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;UACjCxC,UAAU,EAAE;YAAEyC,KAAK,EAAE,IAAI,CAAC1C,KAAK,CAACC,UAAU,CAACyC,KAAK;YAAExC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEwC,SAAS,EAAEtC,GAAG,CAACE,IAAI,CAACkC,UAAU,GAAG,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;QAAA,IAAAsD,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;QACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAT,WAAA,GAAAtD,CAAC,CAACgE,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYtC,IAAI,MAAKiD,SAAS,IAAAV,YAAA,GAAGvD,CAAC,CAACgE,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYvC,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IAtQG,IAAI,CAAC1D,KAAK,GAAG;MACTsC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,EAAE;MACZoB,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,IAAI,CAACnF,WAAW;MACxBoF,YAAY,EAAE,IAAI;MAClBzE,OAAO,EAAE,KAAK;MACd0E,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC;MACbC,GAAG,EAAE,EAAE;MACPtD,SAAS,EAAE,EAAE;MACbuD,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXjF,iBAAiB,EAAE,IAAI;MACvBkF,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVnF,KAAK,EAAE,IAAI;MACXoF,MAAM,EAAE,IAAI;MACZC,iBAAiB,EAAE,IAAI;MACvBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,gBAAgB;MACvBzC,YAAY,EAAE,CAAC;MACf0C,gBAAgB,EAAE,IAAI;MACtBjF,UAAU,EAAE;QACRyC,KAAK,EAAE,CAAC;QACRxC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPgF,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAE3F,KAAK,EAAE,EAAE;YAAE4F,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE5F,KAAK,EAAE,EAAE;YAAE4F,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE5F,KAAK,EAAE,EAAE;YAAE4F,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAMhG,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACVG,OAAO,EAAE,IAAI;QACbkF,MAAM,EAAEtF,CAAC,CAACG,KAAK,CAAC8F,IAAI;QACpBN,gBAAgB,EAAE3F,CAAC,CAACG;MACxB,CAAC,CAAC;MAEF,IAAIuF,KAAK,GAAG,cAAc;MAE1B,IAAIQ,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC/F,MAAM,CAACC,cAAc,CAAC+F,OAAO,CAAC,aAAa,CAAC,CAAC;MAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjC,SAAS,EAAE;QACxE,IAAIzD,GAAG,GAAG,WAAW,GAAGkF,KAAK,GAAG1F,CAAC,CAACG,KAAK,CAACmG,IAAI,GAAG,gBAAgB,GAAGJ,WAAW,GAAG,qCAAqC,GAAG,IAAI,CAACzF,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;QAC1L,MAAMzB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;UACX,IAAIC,SAAS,GAAG,EAAE;UAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;YAAA,IAAAoF,eAAA;YAClC,IAAIlF,CAAC,GAAG;cACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;cACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;cACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;cAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;cACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;cAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;cAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;cACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;cACpBC,MAAM,GAAAsE,eAAA,GAAEpF,OAAO,CAACa,KAAK,cAAAuE,eAAA,uBAAbA,eAAA,CAAetE,MAAM;cAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;cAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;cACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;cACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;YACjJ,CAAC;YACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;UACrB,CAAC,CAAC;UACF,IAAI,CAACpB,QAAQ,CAAC;YACV8C,OAAO,EAAEhC,SAAS;YAClBiC,QAAQ,EAAEjC,SAAS;YACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;YACjCwC,KAAK,EAAEA,KAAK;YACZhF,UAAU,EAAE;cAAEyC,KAAK,EAAE,IAAI,CAAC1C,KAAK,CAACC,UAAU,CAACyC,KAAK;cAAExC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;cAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;cAAEwC,SAAS,EAAEtC,GAAG,CAACE,IAAI,CAACkC,UAAU,GAAG,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACC;YAAM,CAAC;YACpLP,OAAO,EAAE;UACb,CAAC,CAAC;QACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;UAAA,IAAAwG,YAAA,EAAAC,YAAA;UACVjD,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;UACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyC,YAAA,GAAAxG,CAAC,CAACgE,QAAQ,cAAAwC,YAAA,uBAAVA,YAAA,CAAYxF,IAAI,MAAKiD,SAAS,IAAAwC,YAAA,GAAGzG,CAAC,CAACgE,QAAQ,cAAAyC,YAAA,uBAAVA,YAAA,CAAYzF,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;YACxJC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,CAAC;MACV;IACJ,CAAC;IACD,IAAI,CAACuC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAAChH,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACgH,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACJ,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACK,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACL,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACM,KAAK,GAAG,IAAI,CAACA,KAAK,CAACN,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACO,SAAS,GAAG,IAAI,CAACA,SAAS,CAACP,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACR,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACT,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACU,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACV,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACX,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACY,MAAM,GAAG,IAAI,CAACA,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACb,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACc,UAAU,GAAG,IAAI,CAACA,UAAU,CAACd,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACe,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACf,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACgB,UAAU,GAAG,IAAI,CAACA,UAAU,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACiB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACjB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA;EACA,MAAMkB,iBAAiBA,CAAA,EAAG;IACtB,IAAI/B,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC/F,MAAM,CAACC,cAAc,CAAC+F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjC,SAAS,EAAE;MACxE,IAAIzD,GAAG,GAAG,yBAAyB,GAAG0F,WAAW,GAAG,qCAAqC,GAAG,IAAI,CAACzF,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MAC9J,IAAI,CAACX,QAAQ,CAAC;QAAEC,iBAAiB,EAAEgG;MAAY,CAAC,CAAC;MACjD,MAAM/G,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA+G,eAAA;UAClC,IAAI7G,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;YACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;YAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;YACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;YACpBC,MAAM,GAAAiG,eAAA,GAAE/G,OAAO,CAACa,KAAK,cAAAkG,eAAA,uBAAbA,eAAA,CAAejG,MAAM;YAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;YAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;UACjJ,CAAC;UACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV8C,OAAO,EAAEhC,SAAS;UAClBiC,QAAQ,EAAEjC,SAAS;UACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;UACjCxC,UAAU,EAAE;YAAEyC,KAAK,EAAE,IAAI,CAAC1C,KAAK,CAACC,UAAU,CAACyC,KAAK;YAAExC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEwC,SAAS,EAAEtC,GAAG,CAACE,IAAI,CAACkC,UAAU,GAAG,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;QAAA,IAAAmI,YAAA,EAAAC,YAAA;QACV5E,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;QACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAoE,YAAA,GAAAnI,CAAC,CAACgE,QAAQ,cAAAmE,YAAA,uBAAVA,YAAA,CAAYnH,IAAI,MAAKiD,SAAS,IAAAmE,YAAA,GAAGpI,CAAC,CAACgE,QAAQ,cAAAoE,YAAA,uBAAVA,YAAA,CAAYpH,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAAClE,QAAQ,CAAC;QAAEyE,aAAa,EAAE,IAAI;QAAEU,SAAS,EAAE;MAAK,CAAC,CAAC;IAC3D;IACA,MAAMjG,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC0B,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIuH,KAAK,IAAIvH,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAAC4F,SAAS,CAAC9D,IAAI,CAAC;UAChBmD,IAAI,EAAEoC,KAAK,CAACC,aAAa;UACzBnI,KAAK,EAAEkI,KAAK,CAAC3I;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACD2D,KAAK,CAAErD,CAAC,IAAK;MAAA,IAAAuI,YAAA,EAAAC,YAAA;MACVhF,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;MACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAwE,YAAA,GAAAvI,CAAC,CAACgE,QAAQ,cAAAuE,YAAA,uBAAVA,YAAA,CAAYvH,IAAI,MAAKiD,SAAS,IAAAuE,YAAA,GAAGxI,CAAC,CAACgE,QAAQ,cAAAwE,YAAA,uBAAVA,YAAA,CAAYxH,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMhF,UAAU,CAAC,KAAK,EAAE,iCAAiC,CAAC,CACrD0B,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACb,QAAQ,CAAC;QACVoE,QAAQ,EAAEvD,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDqC,KAAK,CAAErD,CAAC,IAAK;MAAA,IAAAyI,YAAA,EAAAC,YAAA;MACVlF,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;MACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAA0E,YAAA,GAAAzI,CAAC,CAACgE,QAAQ,cAAAyE,YAAA,uBAAVA,YAAA,CAAYzH,IAAI,MAAKiD,SAAS,IAAAyE,YAAA,GAAG1I,CAAC,CAACgE,QAAQ,cAAA0E,YAAA,uBAAVA,YAAA,CAAY1H,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMhF,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC0B,IAAI,CAACC,GAAG,IAAI;MACTA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,IAAIA,OAAO,CAACO,UAAU,EAAE;UAC/B,IAAIL,CAAC,GAAG;YACJ4E,IAAI,EAAE9E,OAAO,CAACO,UAAU,CAACC,SAAS,IAAI,sBAAsB;YAC5D2E,IAAI,EAAEnF,OAAO,CAACzB,EAAE,IAAI;UACxB,CAAC;UACD,IAAI,CAACgH,SAAS,CAAC5D,IAAI,CAACzB,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC,CAACgC,KAAK,CAAErD,CAAC,IAAK;MACZwD,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;IAClB,CAAC,CAAC;EACV;EA6CA;EACA,MAAM8G,cAAcA,CAAClC,MAAM,EAAE;IACzB,IAAIpE,GAAG,GAAG,2BAA2B,GAAGoE,MAAM,CAAClF,EAAE;IACjD,IAAIiJ,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMzJ,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;MACX6H,YAAY,GAAG7H,GAAG,CAACE,IAAI,CAACe,cAAc;MACtC6C,MAAM,CAAC7C,cAAc,GAAGjB,GAAG,CAACE,IAAI,CAACe,cAAc;MAC/C6G,IAAI,GAAG9H,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDqC,KAAK,CAAErD,CAAC,IAAK;MAAA,IAAA6I,YAAA,EAAAC,aAAA;MACVtF,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;MACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8E,YAAA,GAAA7I,CAAC,CAACgE,QAAQ,cAAA6E,YAAA,uBAAVA,YAAA,CAAY7H,IAAI,MAAKiD,SAAS,IAAA6E,aAAA,GAAG9I,CAAC,CAACgE,QAAQ,cAAA8E,aAAA,uBAAVA,aAAA,CAAY9H,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBU,MAAM,CAACtD,MAAM,GACb,OAAO,GACP,IAAIe,IAAI,CAAC0G,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACxG,MAAM,CAAC,IAAIyG,IAAI,CAACvE,MAAM,CAAC/C,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAC5B,QAAQ,CAAC;MACVsE,aAAa,EAAE,IAAI;MACnBK,MAAM,EAAEgE,IAAI;MACZxE,QAAQ,EAAEuE,YAAY;MACtB1D,GAAG,EAAEf;IACT,CAAC,CAAC;EACN;EACA;EACA8C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC/G,QAAQ,CAAC;MACVsE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA2C,UAAUA,CAAA,EAAG;IACT,IAAI,CAACjH,QAAQ,CAAC;MACV0E,SAAS,EAAE,KAAK;MAChBL,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACA2C,UAAUA,CAACrC,MAAM,EAAE;IACf,IAAIV,OAAO,GACP,oBAAoB,GACpBU,MAAM,CAACtD,MAAM,GACb,OAAO,GACP,IAAIe,IAAI,CAAC0G,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACxG,MAAM,CAAC,IAAIyG,IAAI,CAACvE,MAAM,CAAC/C,YAAY,CAAC,CAAC;IAC5C,IAAIkD,OAAO,GAAG,EAAE;IAChB,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC5E,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAACnD,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAACiI,IAAI,KAAK,SAAS,IAAIxE,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;UACrD,IAAIqH,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAACnI,OAAO,CAACoI,WAAW,CAAC,GAAGD,QAAQ,CAACnI,OAAO,CAACqI,aAAa,CAAC;UACzEzE,OAAO,CAACjC,IAAI,CAAC;YACT2G,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGN,OAAO,GAAG,GAAG;YAC1FlJ,KAAK,EAAEgB,OAAO,CAACyI;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAIzI,OAAO,CAACiI,IAAI,KAAK,WAAW,IAAIxE,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;UAC9D,IAAIA,KAAK,GAAG,CAAC;UACbA,KAAK,GAAGsH,QAAQ,CAACnI,OAAO,CAACoI,WAAW,CAAC,GAAGD,QAAQ,CAACnI,OAAO,CAACqI,aAAa,CAAC;UACvEnE,OAAO,CAACvC,IAAI,CAAC;YACT2G,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,oBAAoB,GAAG1H,KAAK,GAAG,GAAG;YAC9D7B,KAAK,EAAEgB,OAAO,CAACyI;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACjD,OAAO,GAAG5B,OAAO;IACtB,IAAI,CAAC9E,QAAQ,CAAC;MACV2E,MAAM,EAAAiF,aAAA,KAAOjF,MAAM,CAAE;MACrBN,YAAY,EAAE,IAAI;MAClBS,OAAO,EAAEA,OAAO;MAChBM,OAAO,EAAEA,OAAO;MAChBJ,GAAG,EAAEf;IACT,CAAC,CAAC;EACN;EACAsD,kBAAkBA,CAAA,EAAG;IACjB,IAAItD,OAAO,GACP,iCAAiC;IACrC,IAAIa,OAAO,GAAG,EAAE;IAChB,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAIyE,MAAM,GAAG,IAAI,CAACrJ,KAAK,CAAC+E,iBAAiB,CAACsE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC/H,KAAK,KAAK,IAAI,CAAC;IACzE,IAAI8H,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAACvJ,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAACnD,OAAO,CAAEC,OAAO,IAAK;UACrC;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAIA,OAAO,CAACiI,IAAI,KAAK,WAAW,EAAE;YAC9B,IAAIpH,KAAK,GAAG,CAAC;YACbA,KAAK,GAAGsH,QAAQ,CAACnI,OAAO,CAACoI,WAAW,CAAC,GAAGD,QAAQ,CAACnI,OAAO,CAACqI,aAAa,CAAC;YACvEnE,OAAO,CAACvC,IAAI,CAAC;cACT2G,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,oBAAoB,GAAG1H,KAAK,GAAG,GAAG;cAC9D7B,KAAK,EAAEgB,OAAO,CAACyI;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACjD,OAAO,GAAG5B,OAAO;MACtB,IAAI,CAAC9E,QAAQ,CAAC;QACV2E,MAAM,EAAE,IAAI,CAACnE,KAAK,CAAC+E,iBAAiB;QACpClB,YAAY,EAAE,IAAI;QAClBS,OAAO,EAAEA,OAAO;QAChBM,OAAO,EAAEA,OAAO;QAChBJ,GAAG,EAAEf;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAI4F,MAAM,CAACE,MAAM,KAAK,IAAI,CAACvJ,KAAK,CAAC+E,iBAAiB,CAACwE,MAAM,EAAE;MAC9D,IAAIC,QAAQ,GAAG,IAAI,CAACxJ,KAAK,CAAC+E,iBAAiB,CAACsE,MAAM,CAAC3I,OAAO,IAAIA,OAAO,CAACa,KAAK,CAACkI,QAAQ,KAAK,IAAI,CAAC;MAC9F,IAAID,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIC,QAAQ,CAACD,MAAM,KAAK,IAAI,CAACvJ,KAAK,CAAC+E,iBAAiB,CAACwE,MAAM,EAAE;UACzD,IAAI,IAAI,CAACvJ,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAACnD,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAACiI,IAAI,KAAK,SAAS,EAAE;gBAC5B,IAAIC,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAACnI,OAAO,CAACoI,WAAW,CAAC,GAAGD,QAAQ,CAACnI,OAAO,CAACqI,aAAa,CAAC;gBACzEzE,OAAO,CAACjC,IAAI,CAAC;kBACT2G,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGN,OAAO,GAAG,GAAG;kBAC1FlJ,KAAK,EAAEgB,OAAO,CAACyI;gBACnB,CAAC,CAAC;cACN,CAAC;AAC7B;AACA;AACA;AACA;AACA;YACwB,CAAC,CAAC;UACN;UACA,IAAI,CAACjD,OAAO,GAAG5B,OAAO;UACtB,IAAI,CAAC9E,QAAQ,CAAC;YACV2E,MAAM,EAAE,IAAI,CAACnE,KAAK,CAAC+E,iBAAiB;YACpClB,YAAY,EAAE,IAAI;YAClBS,OAAO,EAAEA,OAAO;YAChBM,OAAO,EAAEA,OAAO;YAChBJ,GAAG,EAAEf;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,sIAAsI;YAC9IK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1D,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAACnD,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACiI,IAAI,KAAK,SAAS,EAAE;cAC5B,IAAIC,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAACnI,OAAO,CAACoI,WAAW,CAAC,GAAGD,QAAQ,CAACnI,OAAO,CAACqI,aAAa,CAAC;cACzEzE,OAAO,CAACjC,IAAI,CAAC;gBACT2G,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGN,OAAO,GAAG,GAAG;gBAC1FlJ,KAAK,EAAEgB,OAAO,CAACyI;cACnB,CAAC,CAAC;YACN,CAAC;AACzB;AACA;AACA;AACA;AACA;UACoB,CAAC,CAAC;QACN;QACA,IAAI,CAACjD,OAAO,GAAG5B,OAAO;QACtB,IAAI,CAAC9E,QAAQ,CAAC;UACV2E,MAAM,EAAE,IAAI,CAACnE,KAAK,CAAC+E,iBAAiB;UACpClB,YAAY,EAAE,IAAI;UAClBS,OAAO,EAAEA,OAAO;UAChBM,OAAO,EAAEA,OAAO;UAChBJ,GAAG,EAAEf;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAgD,QAAQA,CAACvC,MAAM,EAAE;IACb,IAAIA,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;MACvB,OAAO4C,MAAM,CAAC3C,MAAM;MACpB,IAAI,CAAChC,QAAQ,CAAC;QACV2E,MAAM;QACNJ,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACd,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,6EAA6E;QACrFK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAiD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACnH,QAAQ,CAAC;MACVuE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA,MAAM6C,KAAKA,CAAA,EAAG;IACV,IAAInB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC/F,MAAM,CAACC,cAAc,CAAC+F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjC,SAAS,EAAE;MACxE,IAAIzD,GAAG,GAAG,yBAAyB,GAAG0F,WAAW,GAAG,qCAAqC,GAAG,IAAI,CAACzF,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MAC9J,IAAI,CAACX,QAAQ,CAAC;QAAEC,iBAAiB,EAAEgG,WAAW;QAAE9F,OAAO,EAAE,IAAI;QAAEkF,MAAM,EAAE,EAAE;QAAEI,KAAK,EAAE;MAAiB,CAAC,CAAC;MACrG,MAAMvG,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAgJ,eAAA;UAClC,IAAI9I,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;YACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;YAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;YACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;YACpBC,MAAM,GAAAkI,eAAA,GAAEhJ,OAAO,CAACa,KAAK,cAAAmI,eAAA,uBAAbA,eAAA,CAAelI,MAAM;YAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;YAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;UACjJ,CAAC;UACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV8C,OAAO,EAAEhC,SAAS;UAClBiC,QAAQ,EAAEjC,SAAS;UACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;UACjCxC,UAAU,EAAE;YAAEyC,KAAK,EAAE,IAAI,CAAC1C,KAAK,CAACC,UAAU,CAACyC,KAAK;YAAExC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEwC,SAAS,EAAEtC,GAAG,CAACE,IAAI,CAACkC,UAAU,GAAG,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;QAAA,IAAAoK,aAAA,EAAAC,aAAA;QACV7G,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;QACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAqG,aAAA,GAAApK,CAAC,CAACgE,QAAQ,cAAAoG,aAAA,uBAAVA,aAAA,CAAYpJ,IAAI,MAAKiD,SAAS,IAAAoG,aAAA,GAAGrK,CAAC,CAACgE,QAAQ,cAAAqG,aAAA,uBAAVA,aAAA,CAAYrJ,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAMmD,SAASA,CAAA,EAAG;IACd,IAAIpB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC/F,MAAM,CAACC,cAAc,CAAC+F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjC,SAAS,EAAE;MACxE,IAAIzD,GAAG,GAAG,yBAAyB,GAAG0F,WAAW,GAAG,qCAAqC,GAAG,IAAI,CAACzF,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MAC9J,IAAI,CAACX,QAAQ,CAAC;QAAEC,iBAAiB,EAAEgG,WAAW;QAAE9F,OAAO,EAAE,IAAI;QAAEkF,MAAM,EAAE,EAAE;QAAEI,KAAK,EAAE;MAAiB,CAAC,CAAC;MACrG,MAAMvG,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAmJ,eAAA;UAClC,IAAIjJ,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;YACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;YAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;YACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;YACpBC,MAAM,GAAAqI,eAAA,GAAEnJ,OAAO,CAACa,KAAK,cAAAsI,eAAA,uBAAbA,eAAA,CAAerI,MAAM;YAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;YAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;UACjJ,CAAC;UACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV8C,OAAO,EAAEhC,SAAS;UAClBiC,QAAQ,EAAEjC,SAAS;UACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;UACjCxC,UAAU,EAAE;YAAEyC,KAAK,EAAE,IAAI,CAAC1C,KAAK,CAACC,UAAU,CAACyC,KAAK;YAAExC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEwC,SAAS,EAAEtC,GAAG,CAACE,IAAI,CAACkC,UAAU,GAAG,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;QAAA,IAAAuK,aAAA,EAAAC,aAAA;QACVhH,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;QACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAwG,aAAA,GAAAvK,CAAC,CAACgE,QAAQ,cAAAuG,aAAA,uBAAVA,aAAA,CAAYvJ,IAAI,MAAKiD,SAAS,IAAAuG,aAAA,GAAGxK,CAAC,CAACgE,QAAQ,cAAAwG,aAAA,uBAAVA,aAAA,CAAYxJ,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACAoD,UAAUA,CAACvH,CAAC,EAAEyK,GAAG,EAAE;IACf,IAAIX,MAAM,GAAG,EAAE;IACf,IAAIW,GAAG,KAAK,cAAc,EAAE;MACxBX,MAAM,GAAG,IAAI,CAACrJ,KAAK,CAACuC,QAAQ,CAAC8G,MAAM,CAACC,EAAE,IAAI,IAAIZ,IAAI,CAACY,EAAE,CAACjI,YAAY,CAAC,CAAC4I,kBAAkB,CAAC,CAAC,KAAK,IAAIvB,IAAI,CAACnJ,CAAC,CAACG,KAAK,CAAC,CAACuK,kBAAkB,CAAC,CAAC,CAAC;MACpI,IAAI,CAACzK,QAAQ,CAAC;QAAEE,KAAK,EAAEH,CAAC,CAACG,KAAK;QAAE4C,OAAO,EAAE+G,MAAM;QAAEvE,MAAM,EAAE;MAAK,CAAC,CAAC;IACpE,CAAC,MAAM;MACHuE,MAAM,GAAG,IAAI,CAACrJ,KAAK,CAACuC,QAAQ,CAAC8G,MAAM,CAACC,EAAE,IAAI,IAAIZ,IAAI,CAACY,EAAE,CAAClI,YAAY,CAAC,CAAC6I,kBAAkB,CAAC,CAAC,KAAK,IAAIvB,IAAI,CAACnJ,CAAC,CAACG,KAAK,CAAC,CAACuK,kBAAkB,CAAC,CAAC,CAAC;MACpI,IAAI,CAACzK,QAAQ,CAAC;QAAEsF,MAAM,EAAEvF,CAAC,CAACG,KAAK;QAAE4C,OAAO,EAAE+G,MAAM;QAAE3J,KAAK,EAAE;MAAK,CAAC,CAAC;IACpE;EACJ;EACAuH,MAAMA,CAACiD,KAAK,EAAE;IACV,IAAI,CAAC1K,QAAQ,CAAC;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAACyG,eAAe,EAAE;MACtB+D,YAAY,CAAC,IAAI,CAAC/D,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGgE,UAAU,CAAC,YAAY;MAC1C,IAAI3E,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC/F,MAAM,CAACC,cAAc,CAAC+F,OAAO,CAAC,aAAa,CAAC,CAAC;MAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjC,SAAS,EAAE;QAExE,IAAIzD,GAAG,GAAG,IAAI,CAACC,KAAK,CAACiF,KAAK,KAAK,gBAAgB,GAAG,WAAW,GAAG,IAAI,CAACjF,KAAK,CAACiF,KAAK,GAAG,IAAI,CAACjF,KAAK,CAACkF,gBAAgB,CAACW,IAAI,GAAG,gBAAgB,GAAGJ,WAAW,GAAG,qCAAqC,GAAGyE,KAAK,CAAChK,IAAI,GAAG,QAAQ,GAAGgK,KAAK,CAAC/J,IAAI,GAAG,WAAW,GAAG,IAAI,CAACH,KAAK,CAACiF,KAAK,GAAG,IAAI,CAACjF,KAAK,CAACP,iBAAiB,GAAG,qCAAqC,GAAGyK,KAAK,CAAChK,IAAI,GAAG,QAAQ,GAAGgK,KAAK,CAAC/J,IAAI;QAC9W,MAAMzB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;UACX,IAAIC,SAAS,GAAG,EAAE;UAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;YAAA,IAAA2J,eAAA;YAClC,IAAIzJ,CAAC,GAAG;cACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;cACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;cACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;cAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;cACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;cAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;cAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;cACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;cACpBC,MAAM,GAAA6I,eAAA,GAAE3J,OAAO,CAACa,KAAK,cAAA8I,eAAA,uBAAbA,eAAA,CAAe7I,MAAM;cAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;cAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;cACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;cACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;YACjJ,CAAC;YACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;UACrB,CAAC,CAAC;UACF,IAAI,CAACpB,QAAQ,CAAC;YACV8C,OAAO,EAAEhC,SAAS;YAClBiC,QAAQ,EAAEjC,SAAS;YACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;YACjCxC,UAAU,EAAEiK,KAAK;YACjBvK,OAAO,EAAE;UACb,CAAC,CAAC;QACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;UAAA,IAAA+K,aAAA,EAAAC,aAAA;UACVxH,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;UACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAgH,aAAA,GAAA/K,CAAC,CAACgE,QAAQ,cAAA+G,aAAA,uBAAVA,aAAA,CAAY/J,IAAI,MAAKiD,SAAS,IAAA+G,aAAA,GAAGhL,CAAC,CAACgE,QAAQ,cAAAgH,aAAA,uBAAVA,aAAA,CAAYhK,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;YACrJC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,CAAC;MACV;IACJ,CAAC,EAAE8G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAvD,MAAMA,CAACgD,KAAK,EAAE;IACV,IAAI,CAAC1K,QAAQ,CAAC;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI+K,KAAK,GAAGR,KAAK,CAAC/E,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAG+E,KAAK,CAAC/E,SAAS;IAChG,IAAI,IAAI,CAACiB,eAAe,EAAE;MACtB+D,YAAY,CAAC,IAAI,CAAC/D,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGgE,UAAU,CAAC,YAAY;MAC1C,IAAIrK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACiF,KAAK,GAAG,IAAI,CAACjF,KAAK,CAACP,iBAAiB,GAAG,qCAAqC,GAAG,IAAI,CAACO,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI,GAAG,SAAS,GAAGuK,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAC9E,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjQ,MAAM1G,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAiK,eAAA;UAClC,IAAI/J,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS,GAAGR,OAAO,CAACS,WAAW,CAACF,UAAU,CAACC,SAAS;YACjHE,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,YAAY,EAAEX,OAAO,CAACW,YAAY;YAClCC,cAAc,EAAEZ,OAAO,CAACY,cAAc;YACtCC,KAAK,EAAEb,OAAO,CAACa,KAAK;YACpBC,MAAM,GAAAmJ,eAAA,GAAEjK,OAAO,CAACa,KAAK,cAAAoJ,eAAA,uBAAbA,eAAA,CAAenJ,MAAM;YAC7BC,IAAI,EAAEf,OAAO,CAACe,IAAI;YAClBC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAACiB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACxB,OAAO,CAAC0B,UAAU,CAAC;UACjJ,CAAC;UACD9B,SAAS,CAAC+B,IAAI,CAACzB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV8C,OAAO,EAAEhC,SAAS;UAClBiC,QAAQ,EAAEjC,SAAS;UACnBkC,YAAY,EAAEnC,GAAG,CAACE,IAAI,CAACkC,UAAU;UACjCxC,UAAU,EAAAmJ,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACpJ,KAAK,CAACC,UAAU;YAAEkF,SAAS,EAAE+E,KAAK,CAAC/E,SAAS;YAAEC,SAAS,EAAE8E,KAAK,CAAC9E;UAAS,EAAE;UAChGzF,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAErD,CAAC,IAAK;QAAA,IAAAqL,aAAA,EAAAC,aAAA;QACV9H,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;QACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAsH,aAAA,GAAArL,CAAC,CAACgE,QAAQ,cAAAqH,aAAA,uBAAVA,aAAA,CAAYrK,IAAI,MAAKiD,SAAS,IAAAqH,aAAA,GAAGtL,CAAC,CAACgE,QAAQ,cAAAsH,aAAA,uBAAVA,aAAA,CAAYtK,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE8G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAtD,QAAQA,CAAC+C,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC1K,QAAQ,CAAC;MAAES,UAAU,EAAEiK;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EAC3D;EACA9D,sBAAsBA,CAACzH,CAAC,EAAE;IACtB,IAAI,CAACC,QAAQ,CAAC;MAAEuF,iBAAiB,EAAExF,CAAC,CAACG;IAAM,CAAC,CAAC;EACjD;EACA,MAAM0H,UAAUA,CAACjD,MAAM,EAAE;IACrB,IAAIA,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;MAAA,IAAAwJ,qBAAA;MACvB,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIjL,GAAG,GAAG,EAAE;MACZ,IAAI,EAAAgL,qBAAA,GAAA5G,MAAM,CAAC8G,kBAAkB,cAAAF,qBAAA,uBAAzBA,qBAAA,CAA2BxB,MAAM,IAAG,CAAC,EAAE;QACvC,KAAK,IAAI3I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,MAAM,CAAC8G,kBAAkB,CAAC1B,MAAM,EAAE3I,CAAC,EAAE,EAAE;UACvDb,GAAG,GAAG,2BAA2B,GAAGoE,MAAM,CAAC8G,kBAAkB,CAACrK,CAAC,CAAC,CAACsK,SAAS;UAC1E,MAAMxM,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;YACX0C,OAAO,CAACC,GAAG,CAAC3C,GAAG,CAACE,IAAI,CAAC;YACrByK,IAAI,CAAC3I,IAAI,CAAChC,GAAG,CAACE,IAAI,CAACO,IAAI,CAAC;UAC5B,CAAC,CAAC,CACD8B,KAAK,CAAErD,CAAC,IAAK;YACVwD,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;UAClB,CAAC,CAAC;QACV;QACA,IAAI4L,IAAI,GAAGH,IAAI,CAACG,IAAI,CAAC7B,EAAE,IAAIA,EAAE,KAAK,YAAY,CAAC;QAC/C,IAAI6B,IAAI,KAAK3H,SAAS,EAAE;UACpB,IAAI,CAACP,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,EAAE,6DAA6D;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;QACzJ,CAAC,MAAM;UACH3D,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACP,iBAAiB,GAAG,cAAc,GAAG0E,MAAM,CAAClF,EAAE,GAAG,cAAc,GAAGkF,MAAM,CAAC5C,KAAK,CAAC6J,UAAU,CAACpK,UAAU,CAAC/B,EAAE,GAAG,kCAAkC;UAC1L;UACA,MAAMP,UAAU,CAAC,MAAM,EAAEqB,GAAG,CAAC,CACxBK,IAAI,CAACC,GAAG,IAAI;YACT0C,OAAO,CAACC,GAAG,CAAC3C,GAAG,CAACE,IAAI,CAAC;YACrB,IAAI,CAAC0C,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,SAAS;cAAEC,OAAO,EAAE,QAAQ;cAAEC,MAAM,EAAE,4CAA4C;cAAEK,IAAI,EAAE;YAAK,CAAC,CAAC;YAC7H0G,UAAU,CAAC,MAAM;cACbxK,MAAM,CAACyL,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC,CAAC1I,KAAK,CAAErD,CAAC,IAAK;YAAA,IAAAgM,aAAA,EAAAC,aAAA;YACZzI,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;YACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,iBAAiB;cAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAiI,aAAA,GAAAhM,CAAC,CAACgE,QAAQ,cAAAgI,aAAA,uBAAVA,aAAA,CAAYhL,IAAI,MAAKiD,SAAS,IAAAgI,aAAA,GAAGjM,CAAC,CAACgE,QAAQ,cAAAiI,aAAA,uBAAVA,aAAA,CAAYjL,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;UAC/N,CAAC,CAAC;QACV;MACJ,CAAC,MAAM;QACH3D,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACP,iBAAiB,GAAG,cAAc,GAAG0E,MAAM,CAAClF,EAAE,GAAG,cAAc,GAAGkF,MAAM,CAAC5C,KAAK,CAAC6J,UAAU,CAACpK,UAAU,CAAC/B,EAAE,GAAG,kCAAkC;QAC1L;QACA,MAAMP,UAAU,CAAC,MAAM,EAAEqB,GAAG,CAAC,CACxBK,IAAI,CAACC,GAAG,IAAI;UACT0C,OAAO,CAACC,GAAG,CAAC3C,GAAG,CAACE,IAAI,CAAC;UACrB,IAAI,CAAC0C,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,4CAA4C;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7H0G,UAAU,CAAC,MAAM;YACbxK,MAAM,CAACyL,QAAQ,CAACC,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAC1I,KAAK,CAAErD,CAAC,IAAK;UAAA,IAAAkM,aAAA,EAAAC,aAAA;UACZ3I,OAAO,CAACC,GAAG,CAACzD,CAAC,CAAC;UACd,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAmI,aAAA,GAAAlM,CAAC,CAACgE,QAAQ,cAAAkI,aAAA,uBAAVA,aAAA,CAAYlL,IAAI,MAAKiD,SAAS,IAAAkI,aAAA,GAAGnM,CAAC,CAACgE,QAAQ,cAAAmI,aAAA,uBAAVA,aAAA,CAAYnL,IAAI,GAAGhB,CAAC,CAACkE,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/N,CAAC,CAAC;MACV;IACJ,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,qGAAqG;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;IACjM;EACJ;EACA2D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACrH,KAAK,CAACP,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVyE,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAAChB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA4D,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC9H,QAAQ,CAAC;MACVwE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAuD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/H,QAAQ,CAAC;MACVwE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA2H,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBhN,OAAA,CAACjB,KAAK,CAACkO,QAAQ;MAAAC,QAAA,eACXlN,OAAA,CAACN,MAAM;QAACyN,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACvF,UAAW;QAAAqF,QAAA,GACjE,GAAG,EACHrN,QAAQ,CAACwN,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMC,mBAAmB,gBACrB1N,OAAA,CAACjB,KAAK,CAACkO,QAAQ;MAAAC,QAAA,eACXlN,OAAA;QAAKmN,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBlN,OAAA;UAAKmN,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBlN,OAAA;YAAKmN,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvClN,OAAA,CAACN,MAAM;cACHyN,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAACzF,kBAAmB;cAAAuF,QAAA,GAEhC,GAAG,EACHrN,QAAQ,CAACwN,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTzN,OAAA,CAACV,KAAK;cACFoC,SAAS,EAAE,IAAI,CAACN,KAAK,CAACmE,MAAO;cAC7BR,QAAQ,EAAE,IAAI,CAAC3D,KAAK,CAAC2D,QAAS;cAC9BzC,SAAS,EAAE,IAAI,CAAClB,KAAK,CAACkB,SAAU;cAChCuD,OAAO,EAAE,IAAI,CAACzE,KAAK,CAACyE,OAAQ;cAC5BC,OAAO,EAAE,IAAI,CAAC1E,KAAK,CAAC0E,OAAQ;cAC5BF,GAAG,EAAE,IAAI,CAACxE,KAAK,CAACwE,GAAI;cACpB+H,GAAG,EAAE,IAAK;cACVC,QAAQ,EAAE;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMI,mBAAmB,gBACrB7N,OAAA,CAACjB,KAAK,CAACkO,QAAQ;MAAAC,QAAA,eACXlN,OAAA,CAACN,MAAM;QAACyN,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACrF,kBAAmB;QAAAmF,QAAA,GACzE,GAAG,EACHrN,QAAQ,CAACwN,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMK,mBAAmB,gBACrB9N,OAAA,CAACjB,KAAK,CAACkO,QAAQ;MAAAC,QAAA,eACXlN,OAAA;QAAKmN,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DlN,OAAA,CAACN,MAAM;UAACyN,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAAC3E,iBAAkB;UAAAyE,QAAA,GAAE,GAAC,EAACrN,QAAQ,CAACwN,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAMM,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACIpC,KAAK,EAAE,QAAQ;MACfqC,MAAM,EAAEtO,QAAQ,CAACuO,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,MAAM;MACbqC,MAAM,EAAEtO,QAAQ,CAACqC,IAAI;MACrBoM,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,UAAU;MACjBqC,MAAM,EAAEtO,QAAQ,CAAC2O,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,cAAc;MACrBqC,MAAM,EAAEtO,QAAQ,CAAC4O,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,cAAc;MACrBqC,MAAM,EAAEtO,QAAQ,CAAC6O,KAAK;MACtBL,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,+BAA+B;MACtCqC,MAAM,EAAEtO,QAAQ,CAAC8O,YAAY;MAC7BJ,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,UAAU;MACjBqC,MAAM,EAAEtO,QAAQ,CAAC+O,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,QAAQ;MACfqC,MAAM,EAAEtO,QAAQ,CAACgP,KAAK;MACtBR,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,OAAO;MACdqC,MAAM,EAAEtO,QAAQ,CAACiP,GAAG;MACpBP,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,YAAY;MACnBqC,MAAM,EAAEtO,QAAQ,CAACkP,MAAM;MACvBR,UAAU,EAAE;IAChB,CAAC,EACD;MACIzC,KAAK,EAAE,SAAS;MAChBqC,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMS,YAAY,GAAG,CACjB;MAAEpI,IAAI,EAAE/G,QAAQ,CAACoP,OAAO;MAAEC,IAAI,eAAElP,OAAA;QAAGmN,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC1H;IAAe,CAAC,EAC3F;MAAEb,IAAI,EAAE/G,QAAQ,CAACuP,kBAAkB;MAAEF,IAAI,eAAElP,OAAA;QAAGmN,SAAS,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAACvH,UAAU;MAAEhF,MAAM,EAAE;IAAW,CAAC,EAC9H;MAAEgE,IAAI,EAAE/G,QAAQ,CAACwP,WAAW;MAAEH,IAAI,eAAElP,OAAA;QAAGmN,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAACrH;IAAS,CAAC,EAChG;MAAElB,IAAI,EAAE/G,QAAQ,CAACyP,UAAU;MAAEJ,IAAI,eAAElP,OAAA;QAAGmN,SAAS,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC3G,UAAU;MAAE5F,MAAM,EAAE;IAAY,CAAC,CACnH;IACD,IAAI2M,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACnO,KAAK,CAAC6E,MAAM,KAAK,EAAE,IAAI,IAAI,CAAC7E,KAAK,CAACN,KAAK,KAAK,IAAI,IAAI,IAAI,CAACM,KAAK,CAAC8E,MAAM,KAAK,IAAI,EAAE;MACrFqJ,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACIvP,OAAA;MAAKmN,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9ClN,OAAA,CAACP,KAAK;QAAC+P,GAAG,EAAG9E,EAAE,IAAM,IAAI,CAACrG,KAAK,GAAGqG;MAAI;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzCzN,OAAA,CAACf,GAAG;QAAAqO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPzN,OAAA;QAAKmN,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnClN,OAAA;UAAAkN,QAAA,EAAKrN,QAAQ,CAAC4P;QAAgB;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,EACL,IAAI,CAACrM,KAAK,CAACP,iBAAiB,KAAK,IAAI,iBAClCb,OAAA;QAAKmN,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtClN,OAAA;UAAImN,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtElN,OAAA;YAAImN,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjElN,OAAA;cAAKmN,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DlN,OAAA;gBAAImN,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAAClN,OAAA;kBAAGmN,SAAS,EAAC,iBAAiB;kBAACjK,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC5N,QAAQ,CAAC6P,SAAS,EAAC,GAAC;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5HzN,OAAA,CAACR,QAAQ;gBAAC2N,SAAS,EAAC,QAAQ;gBAACrM,KAAK,EAAE,IAAI,CAACM,KAAK,CAACP,iBAAkB;gBAAC8O,OAAO,EAAE,IAAI,CAACpI,SAAU;gBAACqI,QAAQ,EAAE,IAAI,CAAClP,iBAAkB;gBAACmP,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACrF,MAAM;gBAACsF,QAAQ,EAAC,MAAM;gBAACC,YAAY,EAAC;cAA6B;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEVzN,OAAA;QAAKmN,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBlN,OAAA,CAACd,eAAe;UACZsQ,GAAG,EAAG9E,EAAE,IAAM,IAAI,CAACuF,EAAE,GAAGvF,EAAI;UAC5B5J,KAAK,EAAE,IAAI,CAACM,KAAK,CAACsC,OAAQ;UAC1BqK,MAAM,EAAEA,MAAO;UACfhN,OAAO,EAAE,IAAI,CAACK,KAAK,CAACL,OAAQ;UAC5BmP,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACThI,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBvE,KAAK,EAAE,IAAI,CAAC1C,KAAK,CAACC,UAAU,CAACyC,KAAM;UACnCF,YAAY,EAAE,IAAI,CAACxC,KAAK,CAACwC,YAAa;UACtCtC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAK;UACjCgP,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEvB,YAAa;UAC5BwB,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BzC,aAAa,EAAC,UAAU;UACxB0C,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAAClJ,cAAe;UAClCmJ,SAAS,EAAE,IAAI,CAACxP,KAAK,CAAC+E,iBAAkB;UACxC0K,iBAAiB,EAAGlQ,CAAC,IAAK,IAAI,CAACyH,sBAAsB,CAACzH,CAAC,CAAE;UACzDmQ,gBAAgB,EAAE,IAAK;UACvBC,kBAAkB,EAAE,IAAI,CAAC5I,kBAAmB;UAC5C6I,iBAAiB,EAAEnR,QAAQ,CAACsI,kBAAmB;UAC/C8I,oBAAoB,EAAE,CAAC,IAAI,CAAC7P,KAAK,CAAC+E,iBAAiB,IAAI,CAAC,IAAI,CAAC/E,KAAK,CAAC+E,iBAAiB,CAACwE,MAAO;UAC5FuG,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAACzI,UAAW;UACnC0I,gBAAgB,eAAEpR,OAAA;YAAUmN,SAAS,EAAC,MAAM;YAACvG,IAAI,EAAC;UAAgB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/E4D,OAAO,EAAC,QAAQ;UAChB/I,MAAM,EAAE,IAAI,CAACA,MAAO;UACpB/B,SAAS,EAAE,IAAI,CAACnF,KAAK,CAACC,UAAU,CAACkF,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACpF,KAAK,CAACC,UAAU,CAACmF,SAAU;UAC3C+B,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxB9B,OAAO,EAAE,IAAI,CAACrF,KAAK,CAACC,UAAU,CAACoF,OAAQ;UACvC6K,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAQ;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENzN,OAAA,CAACL,MAAM;QACH6R,OAAO,EAAE,IAAI,CAACpQ,KAAK,CAAC6D,YAAa;QACjCkJ,MAAM,EAAE,IAAI,CAAC/M,KAAK,CAACwE,GAAI;QACvB6L,KAAK;QACLtE,SAAS,EAAC,kBAAkB;QAC5BuE,MAAM,EAAE1E,kBAAmB;QAC3B2E,MAAM,EAAE,IAAI,CAAC9J,UAAW;QAAAqF,QAAA,eAExBlN,OAAA,CAACb,gBAAgB;UAACoG,MAAM,EAAE,IAAI,CAACnE,KAAK,CAACmE,MAAO;UAAC+B,OAAO,EAAE,IAAI,CAACA,OAAQ;UAACtB,OAAO,EAAE,IAAI,CAAC5E,KAAK,CAAC4E,OAAQ;UAAC4L,YAAY,EAAE;QAAK;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnH,CAAC,eAETzN,OAAA,CAACL,MAAM;QACH6R,OAAO,EAAE,IAAI,CAACpQ,KAAK,CAAC8D,aAAc;QAClCiJ,MAAM,EAAEtO,QAAQ,CAACgS,MAAO;QACxBJ,KAAK;QACLtE,SAAS,EAAC,kBAAkB;QAC5BuE,MAAM,EAAEhE,mBAAoB;QAC5BiE,MAAM,EAAE,IAAI,CAAChK,kBAAmB;QAChCmK,SAAS,EAAE,KAAM;QAAA5E,QAAA,eAEjBlN,OAAA,CAACZ,mBAAmB;UAChBsC,SAAS,EAAE,IAAI,CAACN,KAAK,CAACmE,MAAO;UAC7BA,MAAM,EAAE,IAAI,CAACnE,KAAK,CAAC2D,QAAS;UAC5BrB,OAAO,EAAE,IAAI,CAACtC,KAAK,CAACmE,MAAO;UAC3BwM,MAAM,EAAE;QAAK;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAETzN,OAAA,CAACL,MAAM;QACH6R,OAAO,EAAE,IAAI,CAACpQ,KAAK,CAAC+D,aAAc;QAClCgJ,MAAM,EAAEtO,QAAQ,CAACwP,WAAY;QAC7BoC,KAAK;QACLtE,SAAS,EAAC,kBAAkB;QAC5BuE,MAAM,EAAE7D,mBAAoB;QAC5B8D,MAAM,EAAE,IAAI,CAAC5J,kBAAmB;QAAAmF,QAAA,eAEhClN,OAAA;UAAKmN,SAAS,EAAC,SAAS;UAAAD,QAAA,eACpBlN,OAAA,CAACX,aAAa;YAACkG,MAAM,EAAE,IAAI,CAACnE,KAAK,CAACmE,MAAO;YAAC7B,OAAO,EAAE,IAAI,CAACtC,KAAK,CAACsC;UAAQ;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACTzN,OAAA,CAACL,MAAM;QAAC6R,OAAO,EAAE,IAAI,CAACpQ,KAAK,CAACiE,aAAc;QAAC8I,MAAM,EAAEtO,QAAQ,CAACmS,iBAAkB;QAACP,KAAK;QAACtE,SAAS,EAAC,kBAAkB;QAACwE,MAAM,EAAE,IAAI,CAAClJ,iBAAkB;QAACiJ,MAAM,EAAE5D,mBAAoB;QAAAZ,QAAA,GACzK,IAAI,CAAC9L,KAAK,CAAC2E,SAAS,iBACjB/F,OAAA,CAACT,UAAU;UAAC0S,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhGzN,OAAA;UAAKmN,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DlN,OAAA;YAAImN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAClN,OAAA;cAAGmN,SAAS,EAAC,iBAAiB;cAACjK,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5N,QAAQ,CAAC6P,SAAS;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHzN,OAAA;YAAAsN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzN,OAAA,CAACR,QAAQ;YAAC2N,SAAS,EAAC,QAAQ;YAACrM,KAAK,EAAE,IAAI,CAACM,KAAK,CAACP,iBAAkB;YAAC8O,OAAO,EAAE,IAAI,CAACpI,SAAU;YAACqI,QAAQ,EAAE,IAAI,CAAClP,iBAAkB;YAACmP,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACrF,MAAM;YAACsF,QAAQ,EAAC,MAAM;YAACC,YAAY,EAAC;UAA6B;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACTzN,OAAA,CAACJ,OAAO;QAAC4R,OAAO,EAAE,IAAI,CAACpQ,KAAK,CAACgE,aAAc;QAACgN,QAAQ,EAAC,MAAM;QAACT,MAAM,EAAE,IAAI,CAAChJ,WAAY;QAAAuE,QAAA,gBACjFlN,OAAA;UAAKK,EAAE,EAAC,cAAc;UAAC8M,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKlN,OAAA;YAAGmN,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CzN,OAAA;YAAImN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAClN,OAAA;cAAGmN,SAAS,EAAC,mBAAmB;cAACjK,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5N,QAAQ,CAACwS,MAAM;UAAA;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/GzN,OAAA,CAACN,MAAM;YAACW,EAAE,EAAC,iBAAiB;YAAC8M,SAAS,EAAEoC,WAAY;YAACnC,OAAO,EAAE,IAAI,CAACpF,KAAM;YAAAkF,QAAA,gBAAClN,OAAA;cAAGmN,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAAzN,OAAA;cAAAkN,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACNzN,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAAC8M,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DlN,OAAA;YAAImN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAClN,OAAA;cAAGmN,SAAS,EAAC,mBAAmB;cAACjK,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5N,QAAQ,CAACwS,MAAM;UAAA;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/GzN,OAAA,CAACN,MAAM;YAACW,EAAE,EAAC,kBAAkB;YAAC8M,SAAS,EAAEoC,WAAY;YAACnC,OAAO,EAAE,IAAI,CAACnF,SAAU;YAAAiF,QAAA,gBAAClN,OAAA;cAAGmN,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAAzN,OAAA;cAAAkN,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACNzN,OAAA;UAAAsN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzN,OAAA,CAACR,QAAQ;UAAC2N,SAAS,EAAC,OAAO;UAACrM,KAAK,EAAE,IAAI,CAACM,KAAK,CAACkF,gBAAiB;UAACqJ,OAAO,EAAE,IAAI,CAACtI,SAAU;UAACuI,QAAQ,EAAE,IAAI,CAACjJ,SAAU;UAACkJ,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,mBAAmB;UAACrF,MAAM;UAACsF,QAAQ,EAAC;QAAM;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAexN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}