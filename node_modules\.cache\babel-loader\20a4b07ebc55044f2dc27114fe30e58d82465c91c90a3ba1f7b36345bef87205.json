{"ast": null, "code": "/*!\n * JavaScript Cookie v2.2.1\n * https://github.com/js-cookie/js-cookie\n *\n * Copyright 2006, 2015 <PERSON> Brack\n * Released under the MIT license\n */\n;\n(function (factory) {\n  var registeredInModuleLoader;\n  if (typeof define === 'function' && define.amd) {\n    define(factory);\n    registeredInModuleLoader = true;\n  }\n  if (typeof exports === 'object') {\n    module.exports = factory();\n    registeredInModuleLoader = true;\n  }\n  if (!registeredInModuleLoader) {\n    var OldCookies = window.Cookies;\n    var api = window.Cookies = factory();\n    api.noConflict = function () {\n      window.Cookies = OldCookies;\n      return api;\n    };\n  }\n})(function () {\n  function extend() {\n    var i = 0;\n    var result = {};\n    for (; i < arguments.length; i++) {\n      var attributes = arguments[i];\n      for (var key in attributes) {\n        result[key] = attributes[key];\n      }\n    }\n    return result;\n  }\n  function decode(s) {\n    return s.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n  }\n  function init(converter) {\n    function api() {}\n    function set(key, value, attributes) {\n      if (typeof document === 'undefined') {\n        return;\n      }\n      attributes = extend({\n        path: '/'\n      }, api.defaults, attributes);\n      if (typeof attributes.expires === 'number') {\n        attributes.expires = new Date(new Date() * 1 + attributes.expires * 864e+5);\n      }\n\n      // We're using \"expires\" because \"max-age\" is not supported by IE\n      attributes.expires = attributes.expires ? attributes.expires.toUTCString() : '';\n      try {\n        var result = JSON.stringify(value);\n        if (/^[\\{\\[]/.test(result)) {\n          value = result;\n        }\n      } catch (e) {}\n      value = converter.write ? converter.write(value, key) : encodeURIComponent(String(value)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent);\n      key = encodeURIComponent(String(key)).replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent).replace(/[\\(\\)]/g, escape);\n      var stringifiedAttributes = '';\n      for (var attributeName in attributes) {\n        if (!attributes[attributeName]) {\n          continue;\n        }\n        stringifiedAttributes += '; ' + attributeName;\n        if (attributes[attributeName] === true) {\n          continue;\n        }\n\n        // Considers RFC 6265 section 5.2:\n        // ...\n        // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n        //     character:\n        // Consume the characters of the unparsed-attributes up to,\n        // not including, the first %x3B (\";\") character.\n        // ...\n        stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n      }\n      return document.cookie = key + '=' + value + stringifiedAttributes;\n    }\n    function get(key, json) {\n      if (typeof document === 'undefined') {\n        return;\n      }\n      var jar = {};\n      // To prevent the for loop in the first place assign an empty array\n      // in case there are no cookies at all.\n      var cookies = document.cookie ? document.cookie.split('; ') : [];\n      var i = 0;\n      for (; i < cookies.length; i++) {\n        var parts = cookies[i].split('=');\n        var cookie = parts.slice(1).join('=');\n        if (!json && cookie.charAt(0) === '\"') {\n          cookie = cookie.slice(1, -1);\n        }\n        try {\n          var name = decode(parts[0]);\n          cookie = (converter.read || converter)(cookie, name) || decode(cookie);\n          if (json) {\n            try {\n              cookie = JSON.parse(cookie);\n            } catch (e) {}\n          }\n          jar[name] = cookie;\n          if (key === name) {\n            break;\n          }\n        } catch (e) {}\n      }\n      return key ? jar[key] : jar;\n    }\n    api.set = set;\n    api.get = function (key) {\n      return get(key, false /* read as raw */);\n    };\n    api.getJSON = function (key) {\n      return get(key, true /* read as json */);\n    };\n    api.remove = function (key, attributes) {\n      set(key, '', extend(attributes, {\n        expires: -1\n      }));\n    };\n    api.defaults = {};\n    api.withConverter = init;\n    return api;\n  }\n  return init(function () {});\n});", "map": {"version": 3, "names": ["factory", "registeredInModuleLoader", "define", "amd", "exports", "module", "OldCookies", "window", "Cookies", "api", "noConflict", "extend", "i", "result", "arguments", "length", "attributes", "key", "decode", "s", "replace", "decodeURIComponent", "init", "converter", "set", "value", "document", "path", "defaults", "expires", "Date", "toUTCString", "JSON", "stringify", "test", "e", "write", "encodeURIComponent", "String", "escape", "stringifiedAttributes", "attributeName", "split", "cookie", "get", "json", "jar", "cookies", "parts", "slice", "join", "char<PERSON>t", "name", "read", "parse", "getJSON", "remove", "withConverter"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/js-cookie/src/js.cookie.js"], "sourcesContent": ["/*!\n * JavaScript Cookie v2.2.1\n * https://github.com/js-cookie/js-cookie\n *\n * Copyright 2006, 2015 <PERSON> & <PERSON> Brack\n * Released under the MIT license\n */\n;(function (factory) {\n\tvar registeredInModuleLoader;\n\tif (typeof define === 'function' && define.amd) {\n\t\tdefine(factory);\n\t\tregisteredInModuleLoader = true;\n\t}\n\tif (typeof exports === 'object') {\n\t\tmodule.exports = factory();\n\t\tregisteredInModuleLoader = true;\n\t}\n\tif (!registeredInModuleLoader) {\n\t\tvar OldCookies = window.Cookies;\n\t\tvar api = window.Cookies = factory();\n\t\tapi.noConflict = function () {\n\t\t\twindow.Cookies = OldCookies;\n\t\t\treturn api;\n\t\t};\n\t}\n}(function () {\n\tfunction extend () {\n\t\tvar i = 0;\n\t\tvar result = {};\n\t\tfor (; i < arguments.length; i++) {\n\t\t\tvar attributes = arguments[ i ];\n\t\t\tfor (var key in attributes) {\n\t\t\t\tresult[key] = attributes[key];\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n\n\tfunction decode (s) {\n\t\treturn s.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n\t}\n\n\tfunction init (converter) {\n\t\tfunction api() {}\n\n\t\tfunction set (key, value, attributes) {\n\t\t\tif (typeof document === 'undefined') {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tattributes = extend({\n\t\t\t\tpath: '/'\n\t\t\t}, api.defaults, attributes);\n\n\t\t\tif (typeof attributes.expires === 'number') {\n\t\t\t\tattributes.expires = new Date(new Date() * 1 + attributes.expires * 864e+5);\n\t\t\t}\n\n\t\t\t// We're using \"expires\" because \"max-age\" is not supported by IE\n\t\t\tattributes.expires = attributes.expires ? attributes.expires.toUTCString() : '';\n\n\t\t\ttry {\n\t\t\t\tvar result = JSON.stringify(value);\n\t\t\t\tif (/^[\\{\\[]/.test(result)) {\n\t\t\t\t\tvalue = result;\n\t\t\t\t}\n\t\t\t} catch (e) {}\n\n\t\t\tvalue = converter.write ?\n\t\t\t\tconverter.write(value, key) :\n\t\t\t\tencodeURIComponent(String(value))\n\t\t\t\t\t.replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent);\n\n\t\t\tkey = encodeURIComponent(String(key))\n\t\t\t\t.replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent)\n\t\t\t\t.replace(/[\\(\\)]/g, escape);\n\n\t\t\tvar stringifiedAttributes = '';\n\t\t\tfor (var attributeName in attributes) {\n\t\t\t\tif (!attributes[attributeName]) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tstringifiedAttributes += '; ' + attributeName;\n\t\t\t\tif (attributes[attributeName] === true) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Considers RFC 6265 section 5.2:\n\t\t\t\t// ...\n\t\t\t\t// 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n\t\t\t\t//     character:\n\t\t\t\t// Consume the characters of the unparsed-attributes up to,\n\t\t\t\t// not including, the first %x3B (\";\") character.\n\t\t\t\t// ...\n\t\t\t\tstringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n\t\t\t}\n\n\t\t\treturn (document.cookie = key + '=' + value + stringifiedAttributes);\n\t\t}\n\n\t\tfunction get (key, json) {\n\t\t\tif (typeof document === 'undefined') {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar jar = {};\n\t\t\t// To prevent the for loop in the first place assign an empty array\n\t\t\t// in case there are no cookies at all.\n\t\t\tvar cookies = document.cookie ? document.cookie.split('; ') : [];\n\t\t\tvar i = 0;\n\n\t\t\tfor (; i < cookies.length; i++) {\n\t\t\t\tvar parts = cookies[i].split('=');\n\t\t\t\tvar cookie = parts.slice(1).join('=');\n\n\t\t\t\tif (!json && cookie.charAt(0) === '\"') {\n\t\t\t\t\tcookie = cookie.slice(1, -1);\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tvar name = decode(parts[0]);\n\t\t\t\t\tcookie = (converter.read || converter)(cookie, name) ||\n\t\t\t\t\t\tdecode(cookie);\n\n\t\t\t\t\tif (json) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tcookie = JSON.parse(cookie);\n\t\t\t\t\t\t} catch (e) {}\n\t\t\t\t\t}\n\n\t\t\t\t\tjar[name] = cookie;\n\n\t\t\t\t\tif (key === name) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {}\n\t\t\t}\n\n\t\t\treturn key ? jar[key] : jar;\n\t\t}\n\n\t\tapi.set = set;\n\t\tapi.get = function (key) {\n\t\t\treturn get(key, false /* read as raw */);\n\t\t};\n\t\tapi.getJSON = function (key) {\n\t\t\treturn get(key, true /* read as json */);\n\t\t};\n\t\tapi.remove = function (key, attributes) {\n\t\t\tset(key, '', extend(attributes, {\n\t\t\t\texpires: -1\n\t\t\t}));\n\t\t};\n\n\t\tapi.defaults = {};\n\n\t\tapi.withConverter = init;\n\n\t\treturn api;\n\t}\n\n\treturn init(function () {});\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAE,WAAUA,OAAO,EAAE;EACpB,IAAIC,wBAAwB;EAC5B,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC/CD,MAAM,CAACF,OAAO,CAAC;IACfC,wBAAwB,GAAG,IAAI;EAChC;EACA,IAAI,OAAOG,OAAO,KAAK,QAAQ,EAAE;IAChCC,MAAM,CAACD,OAAO,GAAGJ,OAAO,CAAC,CAAC;IAC1BC,wBAAwB,GAAG,IAAI;EAChC;EACA,IAAI,CAACA,wBAAwB,EAAE;IAC9B,IAAIK,UAAU,GAAGC,MAAM,CAACC,OAAO;IAC/B,IAAIC,GAAG,GAAGF,MAAM,CAACC,OAAO,GAAGR,OAAO,CAAC,CAAC;IACpCS,GAAG,CAACC,UAAU,GAAG,YAAY;MAC5BH,MAAM,CAACC,OAAO,GAAGF,UAAU;MAC3B,OAAOG,GAAG;IACX,CAAC;EACF;AACD,CAAC,EAAC,YAAY;EACb,SAASE,MAAMA,CAAA,EAAI;IAClB,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,OAAOD,CAAC,GAAGE,SAAS,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;MACjC,IAAII,UAAU,GAAGF,SAAS,CAAEF,CAAC,CAAE;MAC/B,KAAK,IAAIK,GAAG,IAAID,UAAU,EAAE;QAC3BH,MAAM,CAACI,GAAG,CAAC,GAAGD,UAAU,CAACC,GAAG,CAAC;MAC9B;IACD;IACA,OAAOJ,MAAM;EACd;EAEA,SAASK,MAAMA,CAAEC,CAAC,EAAE;IACnB,OAAOA,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAEC,kBAAkB,CAAC;EACzD;EAEA,SAASC,IAAIA,CAAEC,SAAS,EAAE;IACzB,SAASd,GAAGA,CAAA,EAAG,CAAC;IAEhB,SAASe,GAAGA,CAAEP,GAAG,EAAEQ,KAAK,EAAET,UAAU,EAAE;MACrC,IAAI,OAAOU,QAAQ,KAAK,WAAW,EAAE;QACpC;MACD;MAEAV,UAAU,GAAGL,MAAM,CAAC;QACnBgB,IAAI,EAAE;MACP,CAAC,EAAElB,GAAG,CAACmB,QAAQ,EAAEZ,UAAU,CAAC;MAE5B,IAAI,OAAOA,UAAU,CAACa,OAAO,KAAK,QAAQ,EAAE;QAC3Cb,UAAU,CAACa,OAAO,GAAG,IAAIC,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,GAAG,CAAC,GAAGd,UAAU,CAACa,OAAO,GAAG,MAAM,CAAC;MAC5E;;MAEA;MACAb,UAAU,CAACa,OAAO,GAAGb,UAAU,CAACa,OAAO,GAAGb,UAAU,CAACa,OAAO,CAACE,WAAW,CAAC,CAAC,GAAG,EAAE;MAE/E,IAAI;QACH,IAAIlB,MAAM,GAAGmB,IAAI,CAACC,SAAS,CAACR,KAAK,CAAC;QAClC,IAAI,SAAS,CAACS,IAAI,CAACrB,MAAM,CAAC,EAAE;UAC3BY,KAAK,GAAGZ,MAAM;QACf;MACD,CAAC,CAAC,OAAOsB,CAAC,EAAE,CAAC;MAEbV,KAAK,GAAGF,SAAS,CAACa,KAAK,GACtBb,SAAS,CAACa,KAAK,CAACX,KAAK,EAAER,GAAG,CAAC,GAC3BoB,kBAAkB,CAACC,MAAM,CAACb,KAAK,CAAC,CAAC,CAC/BL,OAAO,CAAC,2DAA2D,EAAEC,kBAAkB,CAAC;MAE3FJ,GAAG,GAAGoB,kBAAkB,CAACC,MAAM,CAACrB,GAAG,CAAC,CAAC,CACnCG,OAAO,CAAC,0BAA0B,EAAEC,kBAAkB,CAAC,CACvDD,OAAO,CAAC,SAAS,EAAEmB,MAAM,CAAC;MAE5B,IAAIC,qBAAqB,GAAG,EAAE;MAC9B,KAAK,IAAIC,aAAa,IAAIzB,UAAU,EAAE;QACrC,IAAI,CAACA,UAAU,CAACyB,aAAa,CAAC,EAAE;UAC/B;QACD;QACAD,qBAAqB,IAAI,IAAI,GAAGC,aAAa;QAC7C,IAAIzB,UAAU,CAACyB,aAAa,CAAC,KAAK,IAAI,EAAE;UACvC;QACD;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACAD,qBAAqB,IAAI,GAAG,GAAGxB,UAAU,CAACyB,aAAa,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACvE;MAEA,OAAQhB,QAAQ,CAACiB,MAAM,GAAG1B,GAAG,GAAG,GAAG,GAAGQ,KAAK,GAAGe,qBAAqB;IACpE;IAEA,SAASI,GAAGA,CAAE3B,GAAG,EAAE4B,IAAI,EAAE;MACxB,IAAI,OAAOnB,QAAQ,KAAK,WAAW,EAAE;QACpC;MACD;MAEA,IAAIoB,GAAG,GAAG,CAAC,CAAC;MACZ;MACA;MACA,IAAIC,OAAO,GAAGrB,QAAQ,CAACiB,MAAM,GAAGjB,QAAQ,CAACiB,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;MAChE,IAAI9B,CAAC,GAAG,CAAC;MAET,OAAOA,CAAC,GAAGmC,OAAO,CAAChC,MAAM,EAAEH,CAAC,EAAE,EAAE;QAC/B,IAAIoC,KAAK,GAAGD,OAAO,CAACnC,CAAC,CAAC,CAAC8B,KAAK,CAAC,GAAG,CAAC;QACjC,IAAIC,MAAM,GAAGK,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAErC,IAAI,CAACL,IAAI,IAAIF,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACtCR,MAAM,GAAGA,MAAM,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B;QAEA,IAAI;UACH,IAAIG,IAAI,GAAGlC,MAAM,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC;UAC3BL,MAAM,GAAG,CAACpB,SAAS,CAAC8B,IAAI,IAAI9B,SAAS,EAAEoB,MAAM,EAAES,IAAI,CAAC,IACnDlC,MAAM,CAACyB,MAAM,CAAC;UAEf,IAAIE,IAAI,EAAE;YACT,IAAI;cACHF,MAAM,GAAGX,IAAI,CAACsB,KAAK,CAACX,MAAM,CAAC;YAC5B,CAAC,CAAC,OAAOR,CAAC,EAAE,CAAC;UACd;UAEAW,GAAG,CAACM,IAAI,CAAC,GAAGT,MAAM;UAElB,IAAI1B,GAAG,KAAKmC,IAAI,EAAE;YACjB;UACD;QACD,CAAC,CAAC,OAAOjB,CAAC,EAAE,CAAC;MACd;MAEA,OAAOlB,GAAG,GAAG6B,GAAG,CAAC7B,GAAG,CAAC,GAAG6B,GAAG;IAC5B;IAEArC,GAAG,CAACe,GAAG,GAAGA,GAAG;IACbf,GAAG,CAACmC,GAAG,GAAG,UAAU3B,GAAG,EAAE;MACxB,OAAO2B,GAAG,CAAC3B,GAAG,EAAE,KAAK,CAAC,iBAAiB,CAAC;IACzC,CAAC;IACDR,GAAG,CAAC8C,OAAO,GAAG,UAAUtC,GAAG,EAAE;MAC5B,OAAO2B,GAAG,CAAC3B,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC;IACzC,CAAC;IACDR,GAAG,CAAC+C,MAAM,GAAG,UAAUvC,GAAG,EAAED,UAAU,EAAE;MACvCQ,GAAG,CAACP,GAAG,EAAE,EAAE,EAAEN,MAAM,CAACK,UAAU,EAAE;QAC/Ba,OAAO,EAAE,CAAC;MACX,CAAC,CAAC,CAAC;IACJ,CAAC;IAEDpB,GAAG,CAACmB,QAAQ,GAAG,CAAC,CAAC;IAEjBnB,GAAG,CAACgD,aAAa,GAAGnC,IAAI;IAExB,OAAOb,GAAG;EACX;EAEA,OAAOa,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AAC5B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}