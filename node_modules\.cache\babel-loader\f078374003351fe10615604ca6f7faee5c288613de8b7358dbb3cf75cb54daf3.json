{"ast": null, "code": "import devWarning from '../_util/devWarning';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n  if (progress > 100) {\n    return 100;\n  }\n  return progress;\n}\nexport function getSuccessPercent(_ref) {\n  var success = _ref.success,\n    successPercent = _ref.successPercent;\n  var percent = successPercent;\n  /** @deprecated Use `percent` instead */\n\n  if (success && 'progress' in success) {\n    devWarning(false, 'Progress', '`success.progress` is deprecated. Please use `success.percent` instead.');\n    percent = success.progress;\n  }\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n  return percent;\n}", "map": {"version": 3, "names": ["dev<PERSON><PERSON><PERSON>", "validProgress", "progress", "getSuccessPercent", "_ref", "success", "successPercent", "percent"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/progress/utils.js"], "sourcesContent": ["import devWarning from '../_util/devWarning';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n\n  if (progress > 100) {\n    return 100;\n  }\n\n  return progress;\n}\nexport function getSuccessPercent(_ref) {\n  var success = _ref.success,\n      successPercent = _ref.successPercent;\n  var percent = successPercent;\n  /** @deprecated Use `percent` instead */\n\n  if (success && 'progress' in success) {\n    devWarning(false, 'Progress', '`success.progress` is deprecated. Please use `success.percent` instead.');\n    percent = success.progress;\n  }\n\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n\n  return percent;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,qBAAqB;AAC5C,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAE;EACtC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAC7B,OAAO,CAAC;EACV;EAEA,IAAIA,QAAQ,GAAG,GAAG,EAAE;IAClB,OAAO,GAAG;EACZ;EAEA,OAAOA,QAAQ;AACjB;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACtBC,cAAc,GAAGF,IAAI,CAACE,cAAc;EACxC,IAAIC,OAAO,GAAGD,cAAc;EAC5B;;EAEA,IAAID,OAAO,IAAI,UAAU,IAAIA,OAAO,EAAE;IACpCL,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,yEAAyE,CAAC;IACxGO,OAAO,GAAGF,OAAO,CAACH,QAAQ;EAC5B;EAEA,IAAIG,OAAO,IAAI,SAAS,IAAIA,OAAO,EAAE;IACnCE,OAAO,GAAGF,OAAO,CAACE,OAAO;EAC3B;EAEA,OAAOA,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}