{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\movimentazioneInIngresso.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ResponsableJobsIngresso - operazioni sulle lavorazioni in entrata\n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaOperatore from \"./selezionaOperatore\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass MovimentazioneInIngresso extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      var url = 'documents?idWarehouses=' + e.value + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying, _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying = element.idSupplying) === null || _element$idSupplying === void 0 ? void 0 : _element$idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      globalFilter: null,\n      deleteResultDialog: false,\n      idEmployee: 0,\n      opMag: \"\",\n      mex: \"\",\n      firstName: \"\",\n      address: \"\",\n      indFatt: \"\",\n      loading: true,\n      selectedWarehouse: null,\n      displayed: false,\n      totalRecords: 0,\n      search: '',\n      param: '?idWarehouses=',\n      param2: '&idSupplying=',\n      selectedSupplyer: null,\n      selectedDocuments: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedSupplyer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying2, _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying2 = element.idSupplying) === null || _element$idSupplying2 === void 0 ? void 0 : _element$idSupplying2.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.supplyer = [];\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.opMag = [];\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying3, _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying3 = element.idSupplying) === null || _element$idSupplying3 === void 0 ? void 0 : _element$idSupplying3.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog3: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'supplying/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.supplyer.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = '';\n    if (this.state.selectedDocuments) {\n      message = \"Trasmissione multipla documenti\";\n    } else {\n      message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\"\n      }).format(new Date(result.documentDate));\n    }\n    var opMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        var taskAss = 0;\n        taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n        opMag.push({\n          label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n          value: element.idemployee\n        });\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: this.state.selectedDocuments ? this.state.selectedDocuments : _objectSpread({}, result),\n      resultDialog: true,\n      opMag: opMag,\n      mex: message\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false\n    });\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying4, _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying4 = element.idSupplying) === null || _element$idSupplying4 === void 0 ? void 0 : _element$idSupplying4.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying5, _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying5 = element.idSupplying) === null || _element$idSupplying5 === void 0 ? void 0 : _element$idSupplying5.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            event\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying6, _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying6 = element.idSupplying) === null || _element$idSupplying6 === void 0 ? void 0 : _element$idSupplying6.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying7, _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying7 = element.idSupplying) === null || _element$idSupplying7 === void 0 ? void 0 : _element$idSupplying7.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog3: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 7\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this);\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 7\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"supplying\",\n      header: Costanti.Fornitore,\n      body: \"supplying\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"deliveryDate\",\n      header: Costanti.DCons,\n      body: \"deliveryDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 39\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 50\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.MovimentazioneInIngresso\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 672,\n        columnNumber: 9\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 51\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton2: true,\n          actionExtraButton2: this.editResult,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 31\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          showExportCsvButton: true,\n          fileNames: \"DocIngresso\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field\",\n          children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n            result: this.state.result,\n            opMag: this.opMag\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          documento: this.state.result,\n          result: this.state.results3,\n          results: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter3,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 34\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog4,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 34\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 87\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 123\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 34\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 128\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedSupplyer,\n          options: this.supplyer,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona fornitore\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 767,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default MovimentazioneInIngresso;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "Toast", "<PERSON><PERSON>", "APIRequest", "Dialog", "Dropdown", "JoyrideGen", "Print", "Sidebar", "Nav", "CustomDataTable", "SelezionaOperatore", "VisualizzaDocumenti", "jsxDEV", "_jsxDEV", "MovimentazioneInIngresso", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "url", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "param2", "code", "lazyParams", "rows", "page", "window", "sessionStorage", "setItem", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$idSupplying", "_element$tasks", "x", "number", "type", "supplying", "idSupplying", "idRegistry", "documentDate", "documentBodies", "tasks", "erpSync", "push", "results", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "results2", "results3", "results4", "result", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "globalFilter", "deleteResultDialog", "idEmployee", "opMag", "mex", "address", "indFatt", "displayed", "search", "param", "selectedDocuments", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "_element$idSupplying2", "_element$tasks2", "results5", "_e$response3", "_e$response4", "supplyer", "loadLazyTimeout", "warehouse", "editR<PERSON>ult", "bind", "hideDialog", "visualizzaDett", "hidevisualizzaDett", "onPage", "onSort", "onFilter", "reset", "resetDesc", "<PERSON><PERSON><PERSON><PERSON>", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "JSON", "parse", "getItem", "_element$idSupplying3", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "documentBody", "task", "_e$response1", "_e$response10", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "submitted", "event", "clearTimeout", "setTimeout", "_element$idSupplying4", "_element$tasks4", "_e$response11", "_e$response12", "Math", "random", "field", "_element$idSupplying5", "_element$tasks5", "_e$response13", "_e$response14", "loadLazyData", "_element$idSupplying6", "_element$tasks6", "_e$response15", "_e$response16", "_element$idSupplying7", "_element$tasks7", "_e$response17", "_e$response18", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "doc", "resultDialogFooter3", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "Fornitore", "DataDoc", "DCons", "Operatore", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "icon", "handler", "assegnaLavorazione", "status2", "filterDnone", "ref", "el", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "assegnaLavorazioni", "disabledExtraButton2", "length", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "showExportCsvButton", "fileNames", "visible", "modal", "footer", "onHide", "DocAll", "draggable", "orders", "Primadiproseguire", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/movimentazioneInIngresso.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ResponsableJobsIngresso - operazioni sulle lavorazioni in entrata\n*\n*/\nimport React, { Component } from \"react\";\nimport { <PERSON><PERSON> } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaOperatore from \"./selezionaOperatore\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\n\nclass MovimentazioneInIngresso extends Component {\n  //Stato iniziale elementi tabella\n  emptyResult = {\n    id: null,\n    firstName: \"\",\n    referente: \"\",\n    deliveryDestination: \"\",\n    orderDate: \"\",\n    deliveryDate: \"\",\n    termsPayment: \"\",\n    paymentStatus: \"\",\n    status: \"\",\n  };\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      globalFilter: null,\n      deleteResultDialog: false,\n      idEmployee: 0,\n      opMag: \"\",\n      mex: \"\",\n      firstName: \"\",\n      address: \"\",\n      indFatt: \"\",\n      loading: true,\n      selectedWarehouse: null,\n      displayed: false,\n      totalRecords: 0,\n      search: '',\n      param: '?idWarehouses=',\n      param2: '&idSupplying=',\n      selectedSupplyer: null,\n      selectedDocuments: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': { value: '', matchMode: 'contains' },\n          'type': { value: '', matchMode: 'contains' },\n          'documentDate': { value: '', matchMode: 'contains' },\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedSupplyer: e.value\n      });\n\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: element.idSupplying?.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            results5: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    };\n    this.supplyer = []\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.opMag = [];\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({ selectedWarehouse: idWarehouse });\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: element.idSupplying?.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    } else {\n      this.setState({ resultDialog3: true, displayed: true })\n    }\n    await APIRequest(\"GET\", \"warehouses/\")\n      .then((res) => {\n        for (var entry of res.data) {\n          this.warehouse.push({\n            name: entry.warehouseName,\n            value: entry.id\n          })\n        }\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\")\n      .then((res) => {\n        this.setState({\n          results4: res.data,\n        });\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n    await APIRequest('GET', 'supplying/')\n      .then(res => {\n        res.data.forEach(element => {\n          if (element && element.idRegistry) {\n            var x = {\n              name: element.idRegistry.firstName || 'Nome non disponibile',\n              code: element.id || 0\n            }\n            this.supplyer.push(x)\n          }\n        })\n      }).catch((e) => {\n        console.log(e)\n      })\n  }\n  /* Seleziono il magazzino per la get sui documenti */\n  onWarehouseSelect = async (e) => {\n    this.setState({ selectedWarehouse: e.value });\n    var url = 'documents?idWarehouses=' + e.value + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    window.sessionStorage.setItem(\"idWarehouse\", e.value);\n    await APIRequest(\"GET\", url)\n      .then((res) => {\n        var documento = []\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: element.idSupplying?.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: element.tasks?.status\n          }\n          documento.push(x)\n        })\n        this.setState({\n          results: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n          loading: false\n        });\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id\n    var documentBody = []\n    var task = []\n    await APIRequest(\"GET\", url)\n      .then((res) => {\n        documentBody = res.data.documentBodies\n        result.documentBodies = res.data.documentBodies\n        task = res.data\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n    var message =\n      \"Documento numero: \" +\n      result.number +\n      \" del \" +\n      new Intl.DateTimeFormat(\"it-IT\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\",\n      }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter((val) => val.id === result.id),\n      results3: documentBody,\n      mex: message,\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false,\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = ''\n    if (this.state.selectedDocuments) {\n      message = \"Trasmissione multipla documenti\"\n    } else {\n      message =\n        \"Documento numero: \" +\n        result.number +\n        \" del \" +\n        new Intl.DateTimeFormat(\"it-IT\", {\n          day: \"2-digit\",\n          month: \"2-digit\",\n          year: \"numeric\",\n        }).format(new Date(result.documentDate));\n    }\n\n    var opMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach((element) => {\n        var taskAss = 0\n        taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n        opMag.push({\n          label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n          value: element.idemployee,\n        });\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: this.state.selectedDocuments ? this.state.selectedDocuments : { ...result },\n      resultDialog: true,\n      opMag: opMag,\n      mex: message,\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false,\n    });\n  }\n  onPage(event) {\n    this.setState({ loading: true });\n\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: element.idSupplying?.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: event,\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({ loading: true });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: element.idSupplying?.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { ...this.state.lazyParams, event },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }, Math.random() * 1000 + 250);\n  }\n\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({ lazyParams: event }, this.loadLazyData);\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', selectedSupplyer: null });\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: element.idSupplying?.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            results5: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', selectedSupplyer: null });\n      await APIRequest(\"GET\", url)\n        .then((res) => {\n          var documento = []\n          res.data.documents.forEach(element => {\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: element.idSupplying?.idRegistry.firstName,\n              documentDate: element.documentDate,\n              deliveryDate: element.deliveryDate,\n              documentBodies: element.documentBodies,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: element.tasks?.status\n            }\n            documento.push(x)\n          })\n          this.setState({\n            results: documento,\n            results5: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n            loading: false\n          });\n        })\n        .catch((e) => {\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n            life: 3000,\n          });\n        });\n    }\n  }\n  selectionHandler(e) {\n    this.setState({ selectedDocuments: e.value })\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog3: false\n      })\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000,\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    })\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    })\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = (\n      <React.Fragment>\n        <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = (\n      <React.Fragment>\n        <div className=\"row pt-2\">\n          <div className=\"col-12\">\n            <div className=\"d-flex justify-content-end\">\n              <Button\n                className=\"p-button-text closeModal\"\n                onClick={this.hidevisualizzaDett}\n              >\n                {\" \"}\n                {Costanti.Chiudi}{\" \"}\n              </Button>\n              <Print\n                documento={this.state.result}\n                results3={this.state.results3}\n                mex={this.state.mex}\n                doc={true}\n              />\n            </div>\n          </div>\n        </div>\n      </React.Fragment>\n    );\n    const resultDialogFooter3 = (\n      <React.Fragment>\n        <div className='d-flex justify-content-end align-items-center'>\n          <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n        </div>\n      </React.Fragment>\n    );\n    const fields = [\n      { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n      {\n        field: \"number\",\n        header: Costanti.NDoc,\n        body: \"nDoc\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"supplying\",\n        header: Costanti.Fornitore,\n        body: \"supplying\",\n        showHeader: true,\n      },\n      {\n        field: \"documentDate\",\n        header: Costanti.DataDoc,\n        body: \"documentDate\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"deliveryDate\",\n        header: Costanti.DCons,\n        body: \"deliveryDate\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"tasks.operator.idUser.username\",\n        header: Costanti.Operatore,\n        body: \"operator\",\n        showHeader: true,\n      },\n      {\n        field: \"tasks.status\",\n        header: Costanti.StatoTask,\n        body: \"assigned\",\n        showHeader: true,\n      },\n      {\n        field: \"erpSync\",\n        header: \"ERP Sync\",\n        body: \"erpSync\",\n        sortable: true,\n        showHeader: true,\n      }\n    ];\n    const actionFields = [\n      { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n      { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n    ];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n    }\n    return (\n      <div className=\"datatable-responsive-demo wrapper\">\n        {/* Il componente Toast permette di creare e visualizzare messaggi */}\n        <Toast ref={(el) => (this.toast = el)} />\n        {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n        <Nav />\n        <div className=\"col-12 px-0 solid-head\">\n          <h1>{Costanti.MovimentazioneInIngresso}</h1>\n        </div>\n        {this.state.selectedWarehouse !== null &&\n          <div className='activeFilterContainer p-2'>\n            <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n              <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                <div className='d-flex justify-content-center align-items-center'>\n                  <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                  <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                </div>\n              </li>\n            </ul>\n          </div>\n        }\n        <div className=\"card\">\n          {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n          <CustomDataTable\n            ref={(el) => (this.dt = el)}\n            value={this.state.results}\n            fields={fields}\n            loading={this.state.loading}\n            dataKey=\"id\"\n            lazy\n            filterDisplay=\"row\"\n            paginator\n            onPage={this.onPage}\n            first={this.state.lazyParams.first}\n            totalRecords={this.state.totalRecords}\n            rows={this.state.lazyParams.rows}\n            rowsPerPageOptions={[10, 20, 50]}\n            actionsColumn={actionFields}\n            autoLayout={true}\n            selectionMode=\"checkbox\"\n            cellSelection={true}\n            onCellSelect={this.visualizzaDett}\n            selection={this.state.selectedDocuments}\n            onSelectionChange={(e) => this.selectionHandler(e)}\n            showExtraButton2={true}\n            actionExtraButton2={this.editResult}\n            labelExtraButton2={Costanti.assegnaLavorazioni}\n            disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n            showExtraButton={true}\n            actionExtraButton={this.openFilter}\n            labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n            tooltip='Filtri'\n            onSort={this.onSort}\n            sortField={this.state.lazyParams.sortField}\n            sortOrder={this.state.lazyParams.sortOrder}\n            onFilter={this.onFilter}\n            filters={this.state.lazyParams.filters}\n            showExportCsvButton={true}\n            fileNames=\"DocIngresso\"\n          />\n        </div>\n        {/* Struttura dialogo per la modifica */}\n        <Dialog\n          visible={this.state.resultDialog}\n          header={this.state.mex}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter}\n          onHide={this.hideDialog}\n        >\n          <div className=\"p-field\">\n            <SelezionaOperatore result={this.state.result} opMag={this.opMag} />\n          </div>\n        </Dialog>\n        {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n        <Dialog\n          visible={this.state.resultDialog2}\n          header={Costanti.DocAll}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter2}\n          onHide={this.hidevisualizzaDett}\n          draggable={false}\n        >\n          <VisualizzaDocumenti\n            documento={this.state.result}\n            result={this.state.results3}\n            results={this.state.result}\n            orders={true}\n          />\n        </Dialog>\n        <Dialog visible={this.state.resultDialog3} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter3}>\n          {this.state.displayed &&\n            <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n          }\n          <div className='d-flex justify-content-center flex-column pb-3'>\n            <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n            <hr></hr>\n            <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n          </div>\n        </Dialog>\n        <Sidebar visible={this.state.resultDialog4} position='left' onHide={this.closeFilter}>\n          <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n            <i className=\"pi pi-chevron-right mr-2\"></i>\n            <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n            <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n          </div>\n          <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n            <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n            <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n          </div>\n          <hr></hr>\n          <Dropdown className='w-100' value={this.state.selectedSupplyer} options={this.supplyer} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona fornitore\" filter filterBy=\"name\" />\n        </Sidebar>\n      </div >\n    );\n  }\n}\n\nexport default MovimentazioneInIngresso;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,uDAAuD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExF,MAAMC,wBAAwB,SAAShB,SAAS,CAAC;EAa/CiB,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAbd;IAAA,KACAC,WAAW,GAAG;MACZC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACV,CAAC;IAsMD;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC/B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7C,IAAIC,GAAG,GAAG,yBAAyB,GAAGJ,CAAC,CAACG,KAAK,IAAI,IAAI,CAACE,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAC1PC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEd,CAAC,CAACG,KAAK,CAAC;MACrD,MAAM7B,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,oBAAA,EAAAC,cAAA;UACpC,IAAIC,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAL,oBAAA,GAAED,OAAO,CAACO,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBO,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAyB,cAAA,GAAEF,OAAO,CAACW,KAAK,cAAAT,cAAA,uBAAbA,cAAA,CAAezB;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA0C,WAAA,EAAAC,YAAA;QACZC,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAA1C,CAAC,CAACoD,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYxB,IAAI,MAAKmC,SAAS,IAAAV,YAAA,GAAG3C,CAAC,CAACoD,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYzB,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC;IA1OC,IAAI,CAAClD,KAAK,GAAG;MACX8B,OAAO,EAAE,IAAI;MACbqB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAACtE,WAAW;MACxBuE,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE,KAAK;MACzBC,UAAU,EAAE,CAAC;MACbC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACP7E,SAAS,EAAE,EAAE;MACb8E,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACX9B,OAAO,EAAE,IAAI;MACbtC,iBAAiB,EAAE,IAAI;MACvBqE,SAAS,EAAE,KAAK;MAChBnC,YAAY,EAAE,CAAC;MACfoC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,gBAAgB;MACvBlE,MAAM,EAAE,eAAe;MACvBD,gBAAgB,EAAE,IAAI;MACtBoE,iBAAiB,EAAE,IAAI;MACvBjE,UAAU,EAAE;QACV6B,KAAK,EAAE,CAAC;QACR5B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPgE,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACP,QAAQ,EAAE;YAAE1E,KAAK,EAAE,EAAE;YAAE2E,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE3E,KAAK,EAAE,EAAE;YAAE2E,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE3E,KAAK,EAAE,EAAE;YAAE2E,SAAS,EAAE;UAAW;QACrD;MACF;IACF,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAM/E,CAAC,IAAI;MAC1B,IAAI,CAACC,QAAQ,CAAC;QACZuC,OAAO,EAAE,IAAI;QACbgC,MAAM,EAAExE,CAAC,CAACG,KAAK,CAAC6E,IAAI;QACpB1E,gBAAgB,EAAEN,CAAC,CAACG;MACtB,CAAC,CAAC;MAEF,IAAIC,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACoE,KAAK,GAAG,IAAI,CAACpE,KAAK,CAACH,iBAAiB,GAAG,IAAI,CAACG,KAAK,CAACE,MAAM,GAAGP,CAAC,CAACG,KAAK,CAACK,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAClN,MAAMrC,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4D,qBAAA,EAAAC,eAAA;UACpC,IAAI1D,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAsD,qBAAA,GAAE5D,OAAO,CAACO,WAAW,cAAAqD,qBAAA,uBAAnBA,qBAAA,CAAqBpD,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAoF,eAAA,GAAE7D,OAAO,CAACW,KAAK,cAAAkD,eAAA,uBAAbA,eAAA,CAAepF;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBkE,QAAQ,EAAElE,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAoF,YAAA,EAAAC,YAAA;QACZzC,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAiC,YAAA,GAAApF,CAAC,CAACoD,QAAQ,cAAAgC,YAAA,uBAAVA,YAAA,CAAYlE,IAAI,MAAKmC,SAAS,IAAAgC,YAAA,GAAGrF,CAAC,CAACoD,QAAQ,cAAAiC,YAAA,uBAAVA,YAAA,CAAYnE,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC+B,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACrB,KAAK,GAAG,EAAE;IACf,IAAI,CAACsB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACE,cAAc,GAAG,IAAI,CAACA,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAAC3F,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC2F,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACK,MAAM,GAAG,IAAI,CAACA,MAAM,CAACL,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACM,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACN,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACO,KAAK,GAAG,IAAI,CAACA,KAAK,CAACP,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACA,SAAS,CAACR,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACT,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACU,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACV,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACW,UAAU,GAAG,IAAI,CAACA,UAAU,CAACX,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACY,WAAW,GAAG,IAAI,CAACA,WAAW,CAACZ,IAAI,CAAC,IAAI,CAAC;EAChD;EACA;EACA,MAAMa,iBAAiBA,CAAA,EAAG;IACxB,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC9F,MAAM,CAACC,cAAc,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC7C,IAAIpG,GAAG,GAAG,yBAAyB,GAAGoG,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACnG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEsG;MAAY,CAAC,CAAC;MACjD,MAAMlI,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAuF,qBAAA,EAAAC,eAAA;UACpC,IAAIrF,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAiF,qBAAA,GAAEvF,OAAO,CAACO,WAAW,cAAAgF,qBAAA,uBAAnBA,qBAAA,CAAqB/E,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA+G,eAAA,GAAExF,OAAO,CAACW,KAAK,cAAA6E,eAAA,uBAAbA,eAAA,CAAe/G;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA8G,YAAA,EAAAC,YAAA;QACZnE,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA2D,YAAA,GAAA9G,CAAC,CAACoD,QAAQ,cAAA0D,YAAA,uBAAVA,YAAA,CAAY5F,IAAI,MAAKmC,SAAS,IAAA0D,YAAA,GAAG/G,CAAC,CAACoD,QAAQ,cAAA2D,YAAA,uBAAVA,YAAA,CAAY7F,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAI,CAACtD,QAAQ,CAAC;QAAE6D,aAAa,EAAE,IAAI;QAAES,SAAS,EAAE;MAAK,CAAC,CAAC;IACzD;IACA,MAAMjG,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACnCyC,IAAI,CAAEC,GAAG,IAAK;MACb,KAAK,IAAIgG,KAAK,IAAIhG,GAAG,CAACE,IAAI,EAAE;QAC1B,IAAI,CAACsE,SAAS,CAACtD,IAAI,CAAC;UAClB8C,IAAI,EAAEgC,KAAK,CAACC,aAAa;UACzB9G,KAAK,EAAE6G,KAAK,CAAC1H;QACf,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACDmD,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAkH,YAAA,EAAAC,YAAA;MACZvE,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAA+D,YAAA,GAAAlH,CAAC,CAACoD,QAAQ,cAAA8D,YAAA,uBAAVA,YAAA,CAAYhG,IAAI,MAAKmC,SAAS,IAAA8D,YAAA,GAAGnH,CAAC,CAACoD,QAAQ,cAAA+D,YAAA,uBAAVA,YAAA,CAAYjG,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,MAAMjF,UAAU,CAAC,KAAK,EAAE,4CAA4C,CAAC,CAClEyC,IAAI,CAAEC,GAAG,IAAK;MACb,IAAI,CAACf,QAAQ,CAAC;QACZyD,QAAQ,EAAE1C,GAAG,CAACE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC,CACDuB,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAoH,YAAA,EAAAC,YAAA;MACZzE,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAAiE,YAAA,GAAApH,CAAC,CAACoD,QAAQ,cAAAgE,YAAA,uBAAVA,YAAA,CAAYlG,IAAI,MAAKmC,SAAS,IAAAgE,YAAA,GAAGrH,CAAC,CAACoD,QAAQ,cAAAiE,YAAA,uBAAVA,YAAA,CAAYnG,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,MAAMjF,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAClCyC,IAAI,CAACC,GAAG,IAAI;MACXA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAIA,OAAO,IAAIA,OAAO,CAACQ,UAAU,EAAE;UACjC,IAAIL,CAAC,GAAG;YACNwD,IAAI,EAAE3D,OAAO,CAACQ,UAAU,CAACtC,SAAS,IAAI,sBAAsB;YAC5DiB,IAAI,EAAEa,OAAO,CAAC/B,EAAE,IAAI;UACtB,CAAC;UACD,IAAI,CAACgG,QAAQ,CAACpD,IAAI,CAACV,CAAC,CAAC;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAACiB,KAAK,CAAEzC,CAAC,IAAK;MACd4C,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EAyCA;EACA,MAAM4F,cAAcA,CAACjC,MAAM,EAAE;IAC3B,IAAIvD,GAAG,GAAG,2BAA2B,GAAGuD,MAAM,CAACrE,EAAE;IACjD,IAAIgI,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMjJ,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;MACbsG,YAAY,GAAGtG,GAAG,CAACE,IAAI,CAACa,cAAc;MACtC4B,MAAM,CAAC5B,cAAc,GAAGf,GAAG,CAACE,IAAI,CAACa,cAAc;MAC/CwF,IAAI,GAAGvG,GAAG,CAACE,IAAI;IACjB,CAAC,CAAC,CACDuB,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAwH,YAAA,EAAAC,aAAA;MACZ7E,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAqE,YAAA,GAAAxH,CAAC,CAACoD,QAAQ,cAAAoE,YAAA,uBAAVA,YAAA,CAAYtG,IAAI,MAAKmC,SAAS,IAAAoE,aAAA,GAAGzH,CAAC,CAACoD,QAAQ,cAAAqE,aAAA,uBAAVA,aAAA,CAAYvG,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QACxJC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,IAAID,OAAO,GACT,oBAAoB,GACpBK,MAAM,CAAClC,MAAM,GACb,OAAO,GACP,IAAIiG,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC/BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACrE,MAAM,CAAC7B,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC7B,QAAQ,CAAC;MACZ4D,aAAa,EAAE,IAAI;MACnBF,MAAM,EAAE4D,IAAI;MACZ/D,QAAQ,EAAE,IAAI,CAACnD,KAAK,CAAC8B,OAAO,CAAC8F,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAC5I,EAAE,KAAKqE,MAAM,CAACrE,EAAE,CAAC;MAClEmE,QAAQ,EAAE6D,YAAY;MACtBlD,GAAG,EAAEd;IACP,CAAC,CAAC;EACJ;EACA;EACAuC,kBAAkBA,CAAClC,MAAM,EAAE;IACzB,IAAI,CAAC1D,QAAQ,CAAC;MACZ0D,MAAM,EAAEA,MAAM;MACdE,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACA4B,UAAUA,CAAC9B,MAAM,EAAE;IACjB,IAAIL,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACjD,KAAK,CAACqE,iBAAiB,EAAE;MAChCpB,OAAO,GAAG,iCAAiC;IAC7C,CAAC,MAAM;MACLA,OAAO,GACL,oBAAoB,GACpBK,MAAM,CAAClC,MAAM,GACb,OAAO,GACP,IAAIiG,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAC/BC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACrE,MAAM,CAAC7B,YAAY,CAAC,CAAC;IAC5C;IAEA,IAAIqC,KAAK,GAAG,EAAE;IACd,IAAI,IAAI,CAAC9D,KAAK,CAACqD,QAAQ,KAAK,EAAE,EAAE;MAC9B,IAAI,CAACrD,KAAK,CAACqD,QAAQ,CAACtC,OAAO,CAAEC,OAAO,IAAK;QACvC,IAAI8G,OAAO,GAAG,CAAC;QACfA,OAAO,GAAGC,QAAQ,CAAC/G,OAAO,CAACgH,WAAW,CAAC,GAAGD,QAAQ,CAAC/G,OAAO,CAACiH,YAAY,CAAC,GAAGF,QAAQ,CAAC/G,OAAO,CAACkH,aAAa,CAAC;QAC1GpE,KAAK,CAACjC,IAAI,CAAC;UACTsG,KAAK,EAAEnH,OAAO,CAACoH,UAAU,GAAG,GAAG,GAAGpH,OAAO,CAACqH,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;UAC1FhI,KAAK,EAAEkB,OAAO,CAACsH;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAACxE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClE,QAAQ,CAAC;MACZ0D,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACqE,iBAAiB,GAAG,IAAI,CAACrE,KAAK,CAACqE,iBAAiB,GAAAkE,aAAA,KAAQjF,MAAM,CAAE;MACnFC,YAAY,EAAE,IAAI;MAClBO,KAAK,EAAEA,KAAK;MACZC,GAAG,EAAEd;IACP,CAAC,CAAC;EACJ;EACA;EACAqC,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC1F,QAAQ,CAAC;MACZ4I,SAAS,EAAE,KAAK;MAChBjF,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EACAkC,MAAMA,CAACgD,KAAK,EAAE;IACZ,IAAI,CAAC7I,QAAQ,CAAC;MAAEuC,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAAC+C,eAAe,EAAE;MACxBwD,YAAY,CAAC,IAAI,CAACxD,eAAe,CAAC;IACpC;IACA,IAAI,CAACA,eAAe,GAAGyD,UAAU,CAAC,YAAY;MAC5C,IAAI5I,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACoE,KAAK,GAAG,IAAI,CAACpE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAGsI,KAAK,CAACpI,IAAI,GAAG,QAAQ,GAAGoI,KAAK,CAACnI,IAAI;MACpP,MAAMrC,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4H,qBAAA,EAAAC,eAAA;UACpC,IAAI1H,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAsH,qBAAA,GAAE5H,OAAO,CAACO,WAAW,cAAAqH,qBAAA,uBAAnBA,qBAAA,CAAqBpH,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAoJ,eAAA,GAAE7H,OAAO,CAACW,KAAK,cAAAkH,eAAA,uBAAbA,eAAA,CAAepJ;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAEqI,KAAK;UACjBtG,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAmJ,aAAA,EAAAC,aAAA;QACZxG,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAgG,aAAA,GAAAnJ,CAAC,CAACoD,QAAQ,cAAA+F,aAAA,uBAAVA,aAAA,CAAYjI,IAAI,MAAKmC,SAAS,IAAA+F,aAAA,GAAGpJ,CAAC,CAACoD,QAAQ,cAAAgG,aAAA,uBAAVA,aAAA,CAAYlI,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,EAAE8F,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAChC;EACAvD,MAAMA,CAAC+C,KAAK,EAAE;IACZ,IAAI,CAAC7I,QAAQ,CAAC;MAAEuC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI+G,KAAK,GAAGT,KAAK,CAACnE,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGmE,KAAK,CAACnE,SAAS;IAChG,IAAI,IAAI,CAACY,eAAe,EAAE;MACxBwD,YAAY,CAAC,IAAI,CAACxD,eAAe,CAAC;IACpC;IACA,IAAI,CAACA,eAAe,GAAGyD,UAAU,CAAC,YAAY;MAC5C,IAAI5I,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACoE,KAAK,GAAG,IAAI,CAACpE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI,GAAG,SAAS,GAAG4I,KAAK,GAAG,WAAW,IAAIT,KAAK,CAAClE,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAMtG,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAmI,qBAAA,EAAAC,eAAA;UACpC,IAAIjI,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA6H,qBAAA,GAAEnI,OAAO,CAACO,WAAW,cAAA4H,qBAAA,uBAAnBA,qBAAA,CAAqB3H,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA2J,eAAA,GAAEpI,OAAO,CAACW,KAAK,cAAAyH,eAAA,uBAAbA,eAAA,CAAe3J;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAAmI,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACvI,KAAK,CAACI,UAAU;YAAEqI;UAAK,EAAE;UAC/CtG,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA0J,aAAA,EAAAC,aAAA;QACZ/G,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAuG,aAAA,GAAA1J,CAAC,CAACoD,QAAQ,cAAAsG,aAAA,uBAAVA,aAAA,CAAYxI,IAAI,MAAKmC,SAAS,IAAAsG,aAAA,GAAG3J,CAAC,CAACoD,QAAQ,cAAAuG,aAAA,uBAAVA,aAAA,CAAYzI,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,EAAE8F,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAChC;EAEAtD,QAAQA,CAAC8C,KAAK,EAAE;IACdA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC7I,QAAQ,CAAC;MAAEQ,UAAU,EAAEqI;IAAM,CAAC,EAAE,IAAI,CAACc,YAAY,CAAC;EACzD;EACA;EACA,MAAM3D,KAAKA,CAAA,EAAG;IACZ,IAAIO,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC9F,MAAM,CAACC,cAAc,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKnD,SAAS,EAAE;MAC1E,IAAIjD,GAAG,GAAG,yBAAyB,GAAGoG,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACnG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEsG,WAAW;QAAEhE,OAAO,EAAE,IAAI;QAAEgC,MAAM,EAAE,EAAE;QAAElE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpG,MAAMhC,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAwI,qBAAA,EAAAC,eAAA;UACpC,IAAItI,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAkI,qBAAA,GAAExI,OAAO,CAACO,WAAW,cAAAiI,qBAAA,uBAAnBA,qBAAA,CAAqBhI,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAgK,eAAA,GAAEzI,OAAO,CAACW,KAAK,cAAA8H,eAAA,uBAAbA,eAAA,CAAehK;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBkE,QAAQ,EAAElE,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA+J,aAAA,EAAAC,aAAA;QACZpH,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA4G,aAAA,GAAA/J,CAAC,CAACoD,QAAQ,cAAA2G,aAAA,uBAAVA,aAAA,CAAY7I,IAAI,MAAKmC,SAAS,IAAA2G,aAAA,GAAGhK,CAAC,CAACoD,QAAQ,cAAA4G,aAAA,uBAAVA,aAAA,CAAY9I,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN;EACF;EACA;EACA,MAAM2C,SAASA,CAAA,EAAG;IAChB,IAAIM,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC9F,MAAM,CAACC,cAAc,CAAC8F,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKnD,SAAS,EAAE;MAC1E,IAAIjD,GAAG,GAAG,yBAAyB,GAAGoG,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACnG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEsG,WAAW;QAAEhE,OAAO,EAAE,IAAI;QAAEgC,MAAM,EAAE,EAAE;QAAElE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpG,MAAMhC,UAAU,CAAC,KAAK,EAAE8B,GAAG,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4I,qBAAA,EAAAC,eAAA;UACpC,IAAI1I,CAAC,GAAG;YACNlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAsI,qBAAA,GAAE5I,OAAO,CAACO,WAAW,cAAAqI,qBAAA,uBAAnBA,qBAAA,CAAqBpI,UAAU,CAACtC,SAAS;YACpDuC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCnC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCoC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAoK,eAAA,GAAE7I,OAAO,CAACW,KAAK,cAAAkI,eAAA,uBAAbA,eAAA,CAAepK;UACzB,CAAC;UACDmB,SAAS,CAACiB,IAAI,CAACV,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACZkC,OAAO,EAAElB,SAAS;UAClBkE,QAAQ,EAAElE,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAmK,aAAA,EAAAC,aAAA;QACZxH,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACdC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAgH,aAAA,GAAAnK,CAAC,CAACoD,QAAQ,cAAA+G,aAAA,uBAAVA,aAAA,CAAYjJ,IAAI,MAAKmC,SAAS,IAAA+G,aAAA,GAAGpK,CAAC,CAACoD,QAAQ,cAAAgH,aAAA,uBAAVA,aAAA,CAAYlJ,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACN;EACF;EACA4C,gBAAgBA,CAACnG,CAAC,EAAE;IAClB,IAAI,CAACC,QAAQ,CAAC;MAAEyE,iBAAiB,EAAE1E,CAAC,CAACG;IAAM,CAAC,CAAC;EAC/C;EACAiG,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC/F,KAAK,CAACH,iBAAiB,KAAK,IAAI,EAAE;MACzC,IAAI,CAACD,QAAQ,CAAC;QACZ6D,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChB,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF;EACA8C,UAAUA,CAAA,EAAG;IACX,IAAI,CAACpG,QAAQ,CAAC;MACZ8D,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACAuC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACrG,QAAQ,CAAC;MACZ8D,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACAsG,MAAMA,CAAA,EAAG;IACP;IACA,MAAMC,kBAAkB,gBACtBrL,OAAA,CAAChB,KAAK,CAACsM,QAAQ;MAAAC,QAAA,eACbvL,OAAA,CAACd,MAAM;QAACsM,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC/E,UAAW;QAAA6E,QAAA,GACnE,GAAG,EACHnM,QAAQ,CAACsM,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAMC,mBAAmB,gBACvB/L,OAAA,CAAChB,KAAK,CAACsM,QAAQ;MAAAC,QAAA,eACbvL,OAAA;QAAKwL,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvBvL,OAAA;UAAKwL,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACrBvL,OAAA;YAAKwL,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzCvL,OAAA,CAACd,MAAM;cACLsM,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAC7E,kBAAmB;cAAA2E,QAAA,GAEhC,GAAG,EACHnM,QAAQ,CAACsM,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACT9L,OAAA,CAACP,KAAK;cACJuC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAACsD,MAAO;cAC7BF,QAAQ,EAAE,IAAI,CAACpD,KAAK,CAACoD,QAAS;cAC9BW,GAAG,EAAE,IAAI,CAAC/D,KAAK,CAAC+D,GAAI;cACpB6G,GAAG,EAAE;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACjB;IACD,MAAMG,mBAAmB,gBACvBjM,OAAA,CAAChB,KAAK,CAACsM,QAAQ;MAAAC,QAAA,eACbvL,OAAA;QAAKwL,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC5DvL,OAAA,CAACd,MAAM;UAACsM,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACtE,iBAAkB;UAAAoE,QAAA,GAAE,GAAC,EAACnM,QAAQ,CAACsM,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACjB;IACD,MAAMI,MAAM,GAAG,CACb;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACE/B,KAAK,EAAE,QAAQ;MACfgC,MAAM,EAAElN,QAAQ,CAACmN,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEpC,KAAK,EAAE,WAAW;MAClBgC,MAAM,EAAElN,QAAQ,CAACuN,SAAS;MAC1BH,IAAI,EAAE,WAAW;MACjBE,UAAU,EAAE;IACd,CAAC,EACD;MACEpC,KAAK,EAAE,cAAc;MACrBgC,MAAM,EAAElN,QAAQ,CAACwN,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEpC,KAAK,EAAE,cAAc;MACrBgC,MAAM,EAAElN,QAAQ,CAACyN,KAAK;MACtBL,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEpC,KAAK,EAAE,gCAAgC;MACvCgC,MAAM,EAAElN,QAAQ,CAAC0N,SAAS;MAC1BN,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IACd,CAAC,EACD;MACEpC,KAAK,EAAE,cAAc;MACrBgC,MAAM,EAAElN,QAAQ,CAAC2N,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IACd,CAAC,EACD;MACEpC,KAAK,EAAE,SAAS;MAChBgC,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,CACF;IACD,MAAMM,YAAY,GAAG,CACnB;MAAEjH,IAAI,EAAE3G,QAAQ,CAAC6N,OAAO;MAAEC,IAAI,eAAElN,OAAA;QAAGwL,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEqB,OAAO,EAAE,IAAI,CAACxG;IAAe,CAAC,EAC3F;MAAEZ,IAAI,EAAE3G,QAAQ,CAACgO,kBAAkB;MAAEF,IAAI,eAAElN,OAAA;QAAGwL,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEqB,OAAO,EAAE,IAAI,CAAC3G,UAAU;MAAE3F,MAAM,EAAE,QAAQ;MAAEwM,OAAO,EAAE;IAAU,CAAC,CAChJ;IACD,IAAIC,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAAClM,KAAK,CAACmE,MAAM,KAAK,EAAE,EAAE;MAC5B+H,WAAW,GAAG,gCAAgC;IAChD,CAAC,MAAM;MACLA,WAAW,GAAG,uCAAuC;IACvD;IACA,oBACEtN,OAAA;MAAKwL,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAEhDvL,OAAA,CAACb,KAAK;QAACoO,GAAG,EAAGC,EAAE,IAAM,IAAI,CAAC3J,KAAK,GAAG2J;MAAI;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzC9L,OAAA,CAACL,GAAG;QAAAgM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP9L,OAAA;QAAKwL,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrCvL,OAAA;UAAAuL,QAAA,EAAKnM,QAAQ,CAACa;QAAwB;UAAA0L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EACL,IAAI,CAAC1K,KAAK,CAACH,iBAAiB,KAAK,IAAI,iBACpCjB,OAAA;QAAKwL,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACxCvL,OAAA;UAAIwL,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACxEvL,OAAA;YAAIwL,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACnEvL,OAAA;cAAKwL,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC/DvL,OAAA;gBAAIwL,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACvL,OAAA;kBAAGwL,SAAS,EAAC,iBAAiB;kBAACiC,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC1M,QAAQ,CAACsO,SAAS,EAAC,GAAC;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H9L,OAAA,CAACT,QAAQ;gBAACiM,SAAS,EAAC,QAAQ;gBAACtK,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;gBAAC0M,OAAO,EAAE,IAAI,CAACpH,SAAU;gBAACqH,QAAQ,EAAE,IAAI,CAAC9M,iBAAkB;gBAAC+M,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAAC9E,MAAM;gBAAC+E,QAAQ,EAAC;cAAM;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAER9L,OAAA;QAAKwL,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEnBvL,OAAA,CAACJ,eAAe;UACd2N,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACQ,EAAE,GAAGR,EAAI;UAC5BtM,KAAK,EAAE,IAAI,CAACE,KAAK,CAAC8B,OAAQ;UAC1BgJ,MAAM,EAAEA,MAAO;UACf3I,OAAO,EAAE,IAAI,CAACnC,KAAK,CAACmC,OAAQ;UAC5B0K,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTvH,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBxD,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAM;UACnCF,YAAY,EAAE,IAAI,CAAC/B,KAAK,CAAC+B,YAAa;UACtC1B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAK;UACjC4M,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEtB,YAAa;UAC5BuB,UAAU,EAAE,IAAK;UACjBpC,aAAa,EAAC,UAAU;UACxBqC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAAC9H,cAAe;UAClC+H,SAAS,EAAE,IAAI,CAACtN,KAAK,CAACqE,iBAAkB;UACxCkJ,iBAAiB,EAAG5N,CAAC,IAAK,IAAI,CAACmG,gBAAgB,CAACnG,CAAC,CAAE;UACnD6N,gBAAgB,EAAE,IAAK;UACvBC,kBAAkB,EAAE,IAAI,CAACrI,UAAW;UACpCsI,iBAAiB,EAAE1P,QAAQ,CAAC2P,kBAAmB;UAC/CC,oBAAoB,EAAE,CAAC,IAAI,CAAC5N,KAAK,CAACqE,iBAAiB,IAAI,CAAC,IAAI,CAACrE,KAAK,CAACqE,iBAAiB,CAACwJ,MAAO;UAC5FC,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAAC/H,UAAW;UACnCgI,gBAAgB,eAAEpP,OAAA;YAAUwL,SAAS,EAAC,MAAM;YAACzF,IAAI,EAAC;UAAgB;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/EuD,OAAO,EAAC,QAAQ;UAChBvI,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBpB,SAAS,EAAE,IAAI,CAACtE,KAAK,CAACI,UAAU,CAACkE,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACvE,KAAK,CAACI,UAAU,CAACmE,SAAU;UAC3CoB,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBnB,OAAO,EAAE,IAAI,CAACxE,KAAK,CAACI,UAAU,CAACoE,OAAQ;UACvC0J,mBAAmB,EAAE,IAAK;UAC1BC,SAAS,EAAC;QAAa;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9L,OAAA,CAACV,MAAM;QACLkQ,OAAO,EAAE,IAAI,CAACpO,KAAK,CAACuD,YAAa;QACjC2H,MAAM,EAAE,IAAI,CAAClL,KAAK,CAAC+D,GAAI;QACvBsK,KAAK;QACLjE,SAAS,EAAC,kBAAkB;QAC5BkE,MAAM,EAAErE,kBAAmB;QAC3BsE,MAAM,EAAE,IAAI,CAACjJ,UAAW;QAAA6E,QAAA,eAExBvL,OAAA;UAAKwL,SAAS,EAAC,SAAS;UAAAD,QAAA,eACtBvL,OAAA,CAACH,kBAAkB;YAAC6E,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACsD,MAAO;YAACQ,KAAK,EAAE,IAAI,CAACA;UAAM;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAET9L,OAAA,CAACV,MAAM;QACLkQ,OAAO,EAAE,IAAI,CAACpO,KAAK,CAACwD,aAAc;QAClC0H,MAAM,EAAElN,QAAQ,CAACwQ,MAAO;QACxBH,KAAK;QACLjE,SAAS,EAAC,kBAAkB;QAC5BkE,MAAM,EAAE3D,mBAAoB;QAC5B4D,MAAM,EAAE,IAAI,CAAC/I,kBAAmB;QAChCiJ,SAAS,EAAE,KAAM;QAAAtE,QAAA,eAEjBvL,OAAA,CAACF,mBAAmB;UAClBkC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAACsD,MAAO;UAC7BA,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACoD,QAAS;UAC5BtB,OAAO,EAAE,IAAI,CAAC9B,KAAK,CAACsD,MAAO;UAC3BoL,MAAM,EAAE;QAAK;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACT9L,OAAA,CAACV,MAAM;QAACkQ,OAAO,EAAE,IAAI,CAACpO,KAAK,CAACyD,aAAc;QAACyH,MAAM,EAAElN,QAAQ,CAAC2Q,iBAAkB;QAACN,KAAK;QAACjE,SAAS,EAAC,kBAAkB;QAACmE,MAAM,EAAE,IAAI,CAACxI,iBAAkB;QAACuI,MAAM,EAAEzD,mBAAoB;QAAAV,QAAA,GAC3K,IAAI,CAACnK,KAAK,CAACkE,SAAS,iBACnBtF,OAAA,CAACR,UAAU;UAACwQ,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE9F9L,OAAA;UAAKwL,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC7DvL,OAAA;YAAIwL,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvL,OAAA;cAAGwL,SAAS,EAAC,iBAAiB;cAACiC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC1M,QAAQ,CAACsO,SAAS;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH9L,OAAA;YAAA2L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9L,OAAA,CAACT,QAAQ;YAACiM,SAAS,EAAC,QAAQ;YAACtK,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;YAAC0M,OAAO,EAAE,IAAI,CAACpH,SAAU;YAACqH,QAAQ,EAAE,IAAI,CAAC9M,iBAAkB;YAAC+M,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAAC9E,MAAM;YAAC+E,QAAQ,EAAC;UAAM;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACT9L,OAAA,CAACN,OAAO;QAAC8P,OAAO,EAAE,IAAI,CAACpO,KAAK,CAAC0D,aAAc;QAACqL,QAAQ,EAAC,MAAM;QAACR,MAAM,EAAE,IAAI,CAACtI,WAAY;QAAAkE,QAAA,gBACnFvL,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACmL,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACvKvL,OAAA;YAAGwL,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C9L,OAAA;YAAIwL,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvL,OAAA;cAAGwL,SAAS,EAAC,mBAAmB;cAACiC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC1M,QAAQ,CAACgR,MAAM;UAAA;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9L,OAAA,CAACd,MAAM;YAACmB,EAAE,EAAC,iBAAiB;YAACmL,SAAS,EAAE8B,WAAY;YAAC7B,OAAO,EAAE,IAAI,CAACzE,KAAM;YAAAuE,QAAA,gBAACvL,OAAA;cAAGwL,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9L,OAAA;cAAAuL,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtI,CAAC,eACN9L,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACmL,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBACjEvL,OAAA;YAAIwL,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvL,OAAA;cAAGwL,SAAS,EAAC,mBAAmB;cAACiC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC1M,QAAQ,CAACgR,MAAM;UAAA;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9L,OAAA,CAACd,MAAM;YAACmB,EAAE,EAAC,kBAAkB;YAACmL,SAAS,EAAE8B,WAAY;YAAC7B,OAAO,EAAE,IAAI,CAACxE,SAAU;YAAAsE,QAAA,gBAACvL,OAAA;cAAGwL,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9L,OAAA;cAAAuL,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC,eACN9L,OAAA;UAAA2L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9L,OAAA,CAACT,QAAQ;UAACiM,SAAS,EAAC,OAAO;UAACtK,KAAK,EAAE,IAAI,CAACE,KAAK,CAACC,gBAAiB;UAACsM,OAAO,EAAE,IAAI,CAACtH,QAAS;UAACuH,QAAQ,EAAE,IAAI,CAAC9H,SAAU;UAAC+H,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,qBAAqB;UAAC9E,MAAM;UAAC+E,QAAQ,EAAC;QAAM;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEX;AACF;AAEA,eAAe7L,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}