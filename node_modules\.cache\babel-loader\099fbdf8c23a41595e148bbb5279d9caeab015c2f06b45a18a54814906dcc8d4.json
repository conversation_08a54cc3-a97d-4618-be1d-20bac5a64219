{"ast": null, "code": "import * as React from 'react';\nexport default function useSelectTriggerControl(elements, open, triggerOpen) {\n  var propsRef = React.useRef(null);\n  propsRef.current = {\n    open: open,\n    triggerOpen: triggerOpen\n  };\n  React.useEffect(function () {\n    function onGlobalMouseDown(event) {\n      var target = event.target;\n      if (target.shadowRoot && event.composed) {\n        target = event.composedPath()[0] || target;\n      }\n      if (propsRef.current.open && elements().filter(function (element) {\n        return element;\n      }).every(function (element) {\n        return !element.contains(target) && element !== target;\n      })) {\n        // Should trigger close\n        propsRef.current.triggerOpen(false);\n      }\n    }\n    window.addEventListener('mousedown', onGlobalMouseDown);\n    return function () {\n      return window.removeEventListener('mousedown', onGlobalMouseDown);\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["React", "useSelectTriggerControl", "elements", "open", "triggerOpen", "propsRef", "useRef", "current", "useEffect", "onGlobalMouseDown", "event", "target", "shadowRoot", "composed", "<PERSON><PERSON><PERSON>", "filter", "element", "every", "contains", "window", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/hooks/useSelectTriggerControl.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useSelectTriggerControl(elements, open, triggerOpen) {\n  var propsRef = React.useRef(null);\n  propsRef.current = {\n    open: open,\n    triggerOpen: triggerOpen\n  };\n  React.useEffect(function () {\n    function onGlobalMouseDown(event) {\n      var target = event.target;\n\n      if (target.shadowRoot && event.composed) {\n        target = event.composedPath()[0] || target;\n      }\n\n      if (propsRef.current.open && elements().filter(function (element) {\n        return element;\n      }).every(function (element) {\n        return !element.contains(target) && element !== target;\n      })) {\n        // Should trigger close\n        propsRef.current.triggerOpen(false);\n      }\n    }\n\n    window.addEventListener('mousedown', onGlobalMouseDown);\n    return function () {\n      return window.removeEventListener('mousedown', onGlobalMouseDown);\n    };\n  }, []);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,uBAAuBA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,WAAW,EAAE;EAC3E,IAAIC,QAAQ,GAAGL,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EACjCD,QAAQ,CAACE,OAAO,GAAG;IACjBJ,IAAI,EAAEA,IAAI;IACVC,WAAW,EAAEA;EACf,CAAC;EACDJ,KAAK,CAACQ,SAAS,CAAC,YAAY;IAC1B,SAASC,iBAAiBA,CAACC,KAAK,EAAE;MAChC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;MAEzB,IAAIA,MAAM,CAACC,UAAU,IAAIF,KAAK,CAACG,QAAQ,EAAE;QACvCF,MAAM,GAAGD,KAAK,CAACI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIH,MAAM;MAC5C;MAEA,IAAIN,QAAQ,CAACE,OAAO,CAACJ,IAAI,IAAID,QAAQ,CAAC,CAAC,CAACa,MAAM,CAAC,UAAUC,OAAO,EAAE;QAChE,OAAOA,OAAO;MAChB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUD,OAAO,EAAE;QAC1B,OAAO,CAACA,OAAO,CAACE,QAAQ,CAACP,MAAM,CAAC,IAAIK,OAAO,KAAKL,MAAM;MACxD,CAAC,CAAC,EAAE;QACF;QACAN,QAAQ,CAACE,OAAO,CAACH,WAAW,CAAC,KAAK,CAAC;MACrC;IACF;IAEAe,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEX,iBAAiB,CAAC;IACvD,OAAO,YAAY;MACjB,OAAOU,MAAM,CAACE,mBAAmB,CAAC,WAAW,EAAEZ,iBAAiB,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}