{"ast": null, "code": "import * as React from 'react';\nvar BodyContext = /*#__PURE__*/React.createContext(null);\nexport default BodyContext;", "map": {"version": 3, "names": ["React", "BodyContext", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/context/BodyContext.js"], "sourcesContent": ["import * as React from 'react';\nvar BodyContext = /*#__PURE__*/React.createContext(null);\nexport default BodyContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACxD,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}