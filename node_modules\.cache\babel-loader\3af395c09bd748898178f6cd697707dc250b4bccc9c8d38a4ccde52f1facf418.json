{"ast": null, "code": "var NATIVE_BIND = require('../internals/function-bind-native');\nvar FunctionPrototype = Function.prototype;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\nvar uncurryThis = NATIVE_BIND && bind.bind(call, call);\nmodule.exports = NATIVE_BIND ? function (fn) {\n  return fn && uncurryThis(fn);\n} : function (fn) {\n  return fn && function () {\n    return call.apply(fn, arguments);\n  };\n};", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "FunctionPrototype", "Function", "prototype", "bind", "call", "uncurryThis", "module", "exports", "fn", "apply", "arguments"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/function-uncurry-this.js"], "sourcesContent": ["var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\nvar uncurryThis = NATIVE_BIND && bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? function (fn) {\n  return fn && uncurryThis(fn);\n} : function (fn) {\n  return fn && function () {\n    return call.apply(fn, arguments);\n  };\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAE9D,IAAIC,iBAAiB,GAAGC,QAAQ,CAACC,SAAS;AAC1C,IAAIC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;AACjC,IAAIC,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;AACjC,IAAIC,WAAW,GAAGP,WAAW,IAAIK,IAAI,CAACA,IAAI,CAACC,IAAI,EAAEA,IAAI,CAAC;AAEtDE,MAAM,CAACC,OAAO,GAAGT,WAAW,GAAG,UAAUU,EAAE,EAAE;EAC3C,OAAOA,EAAE,IAAIH,WAAW,CAACG,EAAE,CAAC;AAC9B,CAAC,GAAG,UAAUA,EAAE,EAAE;EAChB,OAAOA,EAAE,IAAI,YAAY;IACvB,OAAOJ,IAAI,CAACK,KAAK,CAACD,EAAE,EAAEE,SAAS,CAAC;EAClC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}