{"ast": null, "code": "module.exports = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};", "map": {"version": 3, "names": ["module", "exports", "Array", "isArray", "arr", "Object", "prototype", "toString", "call"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/isarray/index.js"], "sourcesContent": ["module.exports = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,KAAK,CAACC,OAAO,IAAI,UAAUC,GAAG,EAAE;EAC/C,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC,IAAI,gBAAgB;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}