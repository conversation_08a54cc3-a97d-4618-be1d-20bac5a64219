/**
* Winet e-procurement GUI
* 2020 - Viniexport.com (C)
*
* AggiungiFornitore - operazioni sull'aggiunta fornitore
*
*/
import React, { useState, useRef, useEffect } from 'react';
import classNames from 'classnames/bind';
import { InputText } from 'primereact/inputtext';
import { Costanti } from '../components/traduttore/const';
import { APIRequest } from '../components/generalizzazioni/apireq';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { confirmDialog } from 'primereact/confirmdialog';
import { Form, Field } from 'react-final-form';
import { Dropdown } from 'primereact/dropdown';
import '../css/modale.css';

const AggiungiFornitore = () => {
    //Dichiarazione delle constanti per il salvataggio dei valori inseriti
    const [resultsFirstName, setResultsFirstName] = useState(null);
    const [resultsLastName, setResultsLastName] = useState(null);
    const [resultsEmail, setResultsEmail] = useState(null);
    const [resultsTel, setResultsTel] = useState(null);
    const [resultsMobile, setResultsMobile] = useState(null);
    const [resultsCF, setResultsCF] = useState(null);
    const [resultsAddress, setResultsAddress] = useState(null);
    const [resultsCity, setResultsCity] = useState(null);
    const [resultsCAP, setResultsCAP] = useState(null);
    const [resultsPM, setResultsPM] = useState(null);
    const [classButton, setClassButton] = useState('p-button saveList justify-content-center float-right ionicon mx-0 w-50')
    const [classButton2, setClassButton2] = useState('p-button saveList justify-content-center float-right ionicon mx-0 w-50 d-none')
    const [completa, setCompleta] = useState([])
    const [modPag, setModPag] = useState(null);
    const toast = useRef(null);
    useEffect(() => {
        async function renderString() {
            //Chiamata axios per la visualizzazione dei registry
            await APIRequest('GET', 'paymentmethods/')
                .then(res => {
                    var pm = []
                    res.data.forEach(element => {
                        var x = {
                            name: element.description,
                            code: element.description
                        }
                        pm.push(x)
                    });
                    setModPag(pm)
                }).catch((e) => {
                    console.log(e)
                })
        }
        renderString();
    }, []);
    /* Valorizzo i campi inputText */
    const corpo = {
        firstName: resultsFirstName,
        lastName: resultsLastName,
        email: resultsEmail,
        telnum: resultsTel,
        cellnum: resultsMobile,
        pIva: resultsCF,
        address: resultsAddress,
        city: resultsCity,
        cap: resultsCAP,
        paymentMetod: resultsPM?.name
    }
    // Funzione asincrona di creazione registry e supplying
    const Invia = async (e) => {
        var complete = []
        //Chiamata axios per la creazione del registry
        await APIRequest('POST', 'registry/', corpo)
            .then(async res => {
                console.log(res.data);
                complete = {
                    idRegistry: res.data.id
                }
                //Chiamata axios per la creazione del supplying in caso di corretta creazione registry
                await APIRequest('POST', 'supplying/', complete)
                    .then(res => {
                        console.log(res.data);
                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: "Il fornitore è stato inserito con successo", life: 3000 });
                        setTimeout(() => {
                            window.location.reload()
                        }, 3000)
                    }).catch((e) => {
                        console.log(e)
                        toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il fornitore. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });
                    })
            }).catch(async (e) => {
                console.log(e)
                setCompleta({
                    idRegistry: e.response.data?.id
                })
                var data = e.response.data
                confirmDialog({
                    message: Costanti.RegAlrEx,
                    header: Costanti.Attenzione,
                    icon: 'pi pi-exclamation-triangle',
                    acceptLabel: Costanti.acceptLabel,
                    rejectLabel: "No",
                    accept: (e) => confirm(e, data),
                    reject: decline
                });
            })
    };
    /* Funzione di modifica registry ed aggiunta fornitore */
    const Invia2 = async (e) => {
        var body = {
            firstName: corpo.firstName,
            lastName: corpo.lastName,
            email: corpo.email,
            tel: corpo.cellnum + '/' + corpo.telnum,
            pIva: corpo.pIva,
            address: corpo.address,
            city: corpo.city,
            cap: corpo.cap,
            paymentMetod: corpo.paymentMetod
        }
        var complete = {
            idRegistry: completa.idRegistry
        }
        var url = 'registry/?idRegistry=' + completa.idRegistry
        await APIRequest('PUT', url, body)
            .then(async res => {
                console.log(res.data);
                //Chiamata axios per la creazione del retailer sulla riuscita modifica registry
                await APIRequest('POST', 'supplying/', complete)
                    .then(res => {
                        console.log(res.data);
                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: "Anagrafica modificata e fornitore inserito con successo", life: 3000 });
                        setTimeout(() => {
                            window.location.reload()
                        }, 3000)
                    }).catch((e) => {
                        console.error('❌ Errore creazione fornitore:', e);

                        // Gestione specifica degli errori
                        const errorStatus = e.response?.status;
                        const errorData = e.response?.data;
                        const errorMessage = e.response?.data?.message || e.message;

                        let userMessage = '';
                        let summary = 'Errore';

                        if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||
                            errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {
                            // Violazione unique constraint
                            summary = '⚠️ Fornitore già presente';
                            userMessage = 'Questo fornitore è già registrato nel sistema. Verificare i dati inseriti.';
                        } else if (errorStatus === 400) {
                            // Errori di validazione
                            summary = '📝 Dati non validi';
                            userMessage = `Dati inseriti non validi: ${errorMessage}`;
                        } else if (errorStatus === 500) {
                            // Errore server
                            summary = '🔧 Errore del server';
                            userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto.';
                        } else if (!errorStatus) {
                            // Errore di rete
                            summary = '🌐 Errore di connessione';
                            userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
                        } else {
                            // Altri errori
                            summary = '❌ Errore imprevisto';
                            userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
                        }

                        toast.current.show({
                            severity: 'error',
                            summary: summary,
                            detail: userMessage,
                            life: 6000
                        });

                        // Log dettagliato per debugging
                        console.error('Dettagli errore fornitore:', {
                            status: errorStatus,
                            data: errorData,
                            message: errorMessage,
                            fullError: e
                        });
                    })
            }).catch((e) => {
                console.error('❌ Errore modifica anagrafica per fornitore:', e);

                // Gestione specifica degli errori
                const errorStatus = e.response?.status;
                const errorData = e.response?.data;
                const errorMessage = e.response?.data?.message || e.message;

                let userMessage = '';
                let summary = 'Errore';

                if (errorStatus === 404) {
                    // Anagrafica non trovata
                    summary = '🔍 Anagrafica non trovata';
                    userMessage = 'L\'anagrafica selezionata non è stata trovata nel sistema.';
                } else if (errorStatus === 400) {
                    // Errori di validazione
                    summary = '📝 Dati non validi';
                    userMessage = `Dati inseriti non validi: ${errorMessage}`;
                } else if (errorStatus === 500) {
                    // Errore server
                    summary = '🔧 Errore del server';
                    userMessage = 'Si è verificato un errore interno del server durante la modifica dell\'anagrafica.';
                } else if (!errorStatus) {
                    // Errore di rete
                    summary = '🌐 Errore di connessione';
                    userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
                } else {
                    // Altri errori
                    summary = '❌ Errore imprevisto';
                    userMessage = `Si è verificato un errore imprevisto durante la modifica dell'anagrafica: ${errorMessage}`;
                }

                toast.current.show({
                    severity: 'error',
                    summary: summary,
                    detail: userMessage,
                    life: 6000
                });

                // Log dettagliato per debugging
                console.error('Dettagli errore modifica anagrafica:', {
                    status: errorStatus,
                    data: errorData,
                    message: errorMessage,
                    fullError: e
                });
            })
    }
    /* Cambio il valore dei campi con l'anagrafica trovata in registry */
    const confirm = (e, data) => {
        toast.current.show({ severity: 'warn', summary: 'Attenzione !', detail: "I campi sono stati riempiti con l'anagrafica a nostra disposizione, è possibile modificarli o cliccare su salva per procedere con la registrazione", life: 3000 });
        setResultsFirstName(data.firstName)
        setResultsLastName(data.lastName)
        setResultsEmail(data.email)
        setResultsTel(data.tel.split('/')[0])
        setResultsMobile(data.tel.split('/')[1])
        setResultsCF(data.pIva)
        setResultsAddress(data.address)
        setResultsCity(data.city)
        setResultsCAP(data.cap)
        var paymantMethod = modPag.find(el=>el.name === data.paymantMethod)
        setResultsPM(paymantMethod !== undefined ? paymantMethod : data.paymentMetod)
        setClassButton('p-button saveList justify-content-center float-right ionicon mx-0 w-50 d-none')
        setClassButton2('p-button saveList justify-content-center float-right ionicon mx-0 w-50')
    }
    /* Messaggio in caso di mancata conferma */
    const decline = () => {
        toast.current.show({ severity: 'warn', summary: 'Attenzione !', detail: "Inserisci una nuova anagrafica e riprova", life: 3000 });
    }

    const validate = (data) => {
        let errors = {};

        if (!data.firstName) {
            errors.firstName = Costanti.NomeObb/* 'Name is required.' */;
        }

        if (!data.lastName) {
            errors.lastName = Costanti.CognObb;
        }

        if (!data.email) {
            errors.email = Costanti.EmailObb;
        }
        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(data.email)) {
            errors.email = Costanti.EmailNoVal;
        }

        if (!data.telnum) {
            errors.telnum = Costanti.TelObb;
        }

        if (!data.cellnum) {
            errors.cellnum = Costanti.CelObb;
        }

        if (!data.pIva) {
            errors.pIva = Costanti.pIvaObb;
        }

        if (!data.address) {
            errors.address = Costanti.IndObb;
        }

        if (!data.city) {
            errors.city = Costanti.CityObb;
        }

        if (!data.cap) {
            errors.cap = Costanti.CapObb;
        }

        if (!data.paymentMetod) {
            errors.paymentMetod = Costanti.paymentMetodObb;
        }

        return errors;
    }

    const isFormFieldValid = (meta) => !!(meta.touched && meta.error);
    const getFormErrorMessage = (meta) => {
        return isFormFieldValid(meta) && <small className="p-error">{meta.error}</small>;
    };
    return (
        <div className="modalBody">
            <Toast ref={toast} />
            <Form onSubmit={Invia} initialValues={{ firstName: resultsFirstName, lastName: resultsLastName, email: resultsEmail, telnum: resultsTel, cellnum: resultsMobile, pIva: resultsCF, address: resultsAddress, city: resultsCity, cap: resultsCAP, paymentMetod: resultsPM }} validate={validate} render={({ handleSubmit }) => (
                <form onSubmit={handleSubmit} className="p-fluid">
                    <div className='row'>
                        <Field name="firstName" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label p-input-icon-right">
                                    <i className="pi pi-envelope" />
                                    <InputText value={resultsFirstName} id="firstName" {...input} onChange={(e) => setResultsFirstName(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="firstName" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Nome}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="lastName" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText value={resultsLastName} id="lastName" {...input} onChange={(e) => setResultsLastName(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="lastName" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Cognome}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="email" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText value={resultsEmail} id="email" {...input} onChange={(e) => setResultsEmail(e.target.value)} type="email" keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="email" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Email}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="telnum" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText type="tel" value={resultsTel} id="telnum" {...input} onChange={(e) => setResultsTel(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="telnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Tel}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="cellnum" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText type="tel" value={resultsMobile} id="cellnum" {...input} onChange={(e) => setResultsMobile(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="cellnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Cell}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="pIva" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText value={resultsCF} id="pIva" {...input} onChange={(e) => setResultsCF(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="pIva" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.pIva}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="address" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText value={resultsAddress} id="address" {...input} onChange={(e) => setResultsAddress(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="address" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Indirizzo}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="city" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText value={resultsCity} id="city" {...input} onChange={(e) => setResultsCity(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="city" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Città}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="cap" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                    <InputText value={resultsCAP} id="cap" {...input} onChange={(e) => setResultsCAP(e.target.value)} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                    <label htmlFor="cap" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.CodPost}*</label>
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                        <Field name="paymentMetod" render={({ input, meta }) => (
                            <div className="p-field col-6">
                                <span className="p-float-label">
                                <Dropdown className='w-100' value={resultsPM} options={modPag} onChange={(e) => setResultsPM(e.target.value)} optionLabel="name" placeholder="Seleziona metodo di pagamento" filter filterBy='name' />
                                </span>
                                {getFormErrorMessage(meta)}
                            </div>
                        )} />
                    </div>
                    <div className="buttonForm">
                        {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}
                        <Button type="submit" id="user" className={classButton} > {Costanti.salva} </Button>
                    </div>
                </form>
            )} />
            <div className="d-flex justify-content-center mb-2 mt-4">
                {/* Secondo bottone che si attiva quando l'anagrafica corrisponde ad una già esistente in registry */}
                <Button id="invia" className={classButton2} onClick={(e) => Invia2(e)}>{Costanti.salva}</Button>
            </div>
        </div>
    );
}

export default AggiungiFornitore;
