{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FileOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FileOutlined = function FileOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FileOutlinedSvg\n  }));\n};\nFileOutlined.displayName = 'FileOutlined';\nexport default /*#__PURE__*/React.forwardRef(FileOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "FileOutlinedSvg", "AntdIcon", "FileOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons/es/icons/FileOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FileOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileOutlined\";\nimport AntdIcon from '../components/AntdIcon';\n\nvar FileOutlined = function FileOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FileOutlinedSvg\n  }));\n};\n\nFileOutlined.displayName = 'FileOutlined';\nexport default /*#__PURE__*/React.forwardRef(FileOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAE7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AAEDE,YAAY,CAACK,WAAW,GAAG,cAAc;AACzC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}