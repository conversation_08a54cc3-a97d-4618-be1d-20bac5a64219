{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport TreeNode from './TreeNode';\nimport { getTreeNodeProps } from './utils/treeUtil';\nimport { TreeContext } from './contextTypes';\nvar MotionTreeNode = function MotionTreeNode(_ref, ref) {\n  var className = _ref.className,\n    style = _ref.style,\n    motion = _ref.motion,\n    motionNodes = _ref.motionNodes,\n    motionType = _ref.motionType,\n    onOriginMotionStart = _ref.onMotionStart,\n    onOriginMotionEnd = _ref.onMotionEnd,\n    active = _ref.active,\n    treeNodeRequiredProps = _ref.treeNodeRequiredProps,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useContext = React.useContext(TreeContext),\n    prefixCls = _React$useContext.prefixCls;\n  var motionedRef = React.useRef(false);\n  var onMotionEnd = function onMotionEnd() {\n    if (!motionedRef.current) {\n      onOriginMotionEnd();\n    }\n    motionedRef.current = true;\n  };\n  useEffect(function () {\n    if (motionNodes && motionType === 'hide' && visible) {\n      setVisible(false);\n    }\n  }, [motionNodes]);\n  useEffect(function () {\n    // Trigger motion only when patched\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n    return function () {\n      if (motionNodes) {\n        onMotionEnd();\n      }\n    };\n  }, []);\n  if (motionNodes) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onAppearEnd: onMotionEnd,\n      onLeaveEnd: onMotionEnd\n    }), function (_ref2, motionRef) {\n      var motionClassName = _ref2.className,\n        motionStyle = _ref2.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: motionRef,\n        className: classNames(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = _extends({}, treeNode.data),\n          title = treeNode.title,\n          key = treeNode.key,\n          isStart = treeNode.isStart,\n          isEnd = treeNode.isEnd;\n        delete restProps.children;\n        var treeNodeProps = getTreeNodeProps(key, treeNodeRequiredProps);\n        return /*#__PURE__*/React.createElement(TreeNode, _extends({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n  return /*#__PURE__*/React.createElement(TreeNode, _extends({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n};\nMotionTreeNode.displayName = 'MotionTreeNode';\nvar RefMotionTreeNode = /*#__PURE__*/React.forwardRef(MotionTreeNode);\nexport default RefMotionTreeNode;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "useEffect", "classNames", "CSSMotion", "TreeNode", "getTreeNodeProps", "TreeContext", "MotionTreeNode", "_ref", "ref", "className", "style", "motion", "motionNodes", "motionType", "onOriginMotionStart", "onMotionStart", "onOriginMotionEnd", "onMotionEnd", "active", "treeNodeRequiredProps", "props", "_React$useState", "useState", "_React$useState2", "visible", "setVisible", "_React$useContext", "useContext", "prefixCls", "motionedRef", "useRef", "current", "createElement", "motionAppear", "onAppearEnd", "onLeaveEnd", "_ref2", "motionRef", "motionClassName", "motionStyle", "concat", "map", "treeNode", "restProps", "data", "title", "key", "isStart", "isEnd", "children", "treeNodeProps", "domRef", "displayName", "RefMotionTreeNode", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree/es/MotionTreeNode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport TreeNode from './TreeNode';\nimport { getTreeNodeProps } from './utils/treeUtil';\nimport { TreeContext } from './contextTypes';\n\nvar MotionTreeNode = function MotionTreeNode(_ref, ref) {\n  var className = _ref.className,\n      style = _ref.style,\n      motion = _ref.motion,\n      motionNodes = _ref.motionNodes,\n      motionType = _ref.motionType,\n      onOriginMotionStart = _ref.onMotionStart,\n      onOriginMotionEnd = _ref.onMotionEnd,\n      active = _ref.active,\n      treeNodeRequiredProps = _ref.treeNodeRequiredProps,\n      props = _objectWithoutProperties(_ref, _excluded);\n\n  var _React$useState = React.useState(true),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      visible = _React$useState2[0],\n      setVisible = _React$useState2[1];\n\n  var _React$useContext = React.useContext(TreeContext),\n      prefixCls = _React$useContext.prefixCls;\n\n  var motionedRef = React.useRef(false);\n\n  var onMotionEnd = function onMotionEnd() {\n    if (!motionedRef.current) {\n      onOriginMotionEnd();\n    }\n\n    motionedRef.current = true;\n  };\n\n  useEffect(function () {\n    if (motionNodes && motionType === 'hide' && visible) {\n      setVisible(false);\n    }\n  }, [motionNodes]);\n  useEffect(function () {\n    // Trigger motion only when patched\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n\n    return function () {\n      if (motionNodes) {\n        onMotionEnd();\n      }\n    };\n  }, []);\n\n  if (motionNodes) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onAppearEnd: onMotionEnd,\n      onLeaveEnd: onMotionEnd\n    }), function (_ref2, motionRef) {\n      var motionClassName = _ref2.className,\n          motionStyle = _ref2.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: motionRef,\n        className: classNames(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = _extends({}, treeNode.data),\n            title = treeNode.title,\n            key = treeNode.key,\n            isStart = treeNode.isStart,\n            isEnd = treeNode.isEnd;\n\n        delete restProps.children;\n        var treeNodeProps = getTreeNodeProps(key, treeNodeRequiredProps);\n        return /*#__PURE__*/React.createElement(TreeNode, _extends({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n\n  return /*#__PURE__*/React.createElement(TreeNode, _extends({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n};\n\nMotionTreeNode.displayName = 'MotionTreeNode';\nvar RefMotionTreeNode = /*#__PURE__*/React.forwardRef(MotionTreeNode);\nexport default RefMotionTreeNode;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,uBAAuB,CAAC;AAChJ,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACtD,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,WAAW,GAAGL,IAAI,CAACK,WAAW;IAC9BC,UAAU,GAAGN,IAAI,CAACM,UAAU;IAC5BC,mBAAmB,GAAGP,IAAI,CAACQ,aAAa;IACxCC,iBAAiB,GAAGT,IAAI,CAACU,WAAW;IACpCC,MAAM,GAAGX,IAAI,CAACW,MAAM;IACpBC,qBAAqB,GAAGZ,IAAI,CAACY,qBAAqB;IAClDC,KAAK,GAAGvB,wBAAwB,CAACU,IAAI,EAAET,SAAS,CAAC;EAErD,IAAIuB,eAAe,GAAGtB,KAAK,CAACuB,QAAQ,CAAC,IAAI,CAAC;IACtCC,gBAAgB,GAAG3B,cAAc,CAACyB,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIG,iBAAiB,GAAG3B,KAAK,CAAC4B,UAAU,CAACtB,WAAW,CAAC;IACjDuB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EAE3C,IAAIC,WAAW,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,KAAK,CAAC;EAErC,IAAIb,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAI,CAACY,WAAW,CAACE,OAAO,EAAE;MACxBf,iBAAiB,CAAC,CAAC;IACrB;IAEAa,WAAW,CAACE,OAAO,GAAG,IAAI;EAC5B,CAAC;EAED/B,SAAS,CAAC,YAAY;IACpB,IAAIY,WAAW,IAAIC,UAAU,KAAK,MAAM,IAAIW,OAAO,EAAE;MACnDC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;EACjBZ,SAAS,CAAC,YAAY;IACpB;IACA,IAAIY,WAAW,EAAE;MACfE,mBAAmB,CAAC,CAAC;IACvB;IAEA,OAAO,YAAY;MACjB,IAAIF,WAAW,EAAE;QACfK,WAAW,CAAC,CAAC;MACf;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIL,WAAW,EAAE;IACf,OAAO,aAAab,KAAK,CAACiC,aAAa,CAAC9B,SAAS,EAAEP,QAAQ,CAAC;MAC1Da,GAAG,EAAEA,GAAG;MACRgB,OAAO,EAAEA;IACX,CAAC,EAAEb,MAAM,EAAE;MACTsB,YAAY,EAAEpB,UAAU,KAAK,MAAM;MACnCqB,WAAW,EAAEjB,WAAW;MACxBkB,UAAU,EAAElB;IACd,CAAC,CAAC,EAAE,UAAUmB,KAAK,EAAEC,SAAS,EAAE;MAC9B,IAAIC,eAAe,GAAGF,KAAK,CAAC3B,SAAS;QACjC8B,WAAW,GAAGH,KAAK,CAAC1B,KAAK;MAC7B,OAAO,aAAaX,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAE;QAC7CxB,GAAG,EAAE6B,SAAS;QACd5B,SAAS,EAAER,UAAU,CAAC,EAAE,CAACuC,MAAM,CAACZ,SAAS,EAAE,kBAAkB,CAAC,EAAEU,eAAe,CAAC;QAChF5B,KAAK,EAAE6B;MACT,CAAC,EAAE3B,WAAW,CAAC6B,GAAG,CAAC,UAAUC,QAAQ,EAAE;QACrC,IAAIC,SAAS,GAAGhD,QAAQ,CAAC,CAAC,CAAC,EAAE+C,QAAQ,CAACE,IAAI,CAAC;UACvCC,KAAK,GAAGH,QAAQ,CAACG,KAAK;UACtBC,GAAG,GAAGJ,QAAQ,CAACI,GAAG;UAClBC,OAAO,GAAGL,QAAQ,CAACK,OAAO;UAC1BC,KAAK,GAAGN,QAAQ,CAACM,KAAK;QAE1B,OAAOL,SAAS,CAACM,QAAQ;QACzB,IAAIC,aAAa,GAAG9C,gBAAgB,CAAC0C,GAAG,EAAE3B,qBAAqB,CAAC;QAChE,OAAO,aAAapB,KAAK,CAACiC,aAAa,CAAC7B,QAAQ,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEgD,SAAS,EAAEO,aAAa,EAAE;UACvFL,KAAK,EAAEA,KAAK;UACZ3B,MAAM,EAAEA,MAAM;UACd0B,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBE,GAAG,EAAEA,GAAG;UACRC,OAAO,EAAEA,OAAO;UAChBC,KAAK,EAAEA;QACT,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEA,OAAO,aAAajD,KAAK,CAACiC,aAAa,CAAC7B,QAAQ,EAAER,QAAQ,CAAC;IACzDwD,MAAM,EAAE3C,GAAG;IACXC,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA;EACT,CAAC,EAAEU,KAAK,EAAE;IACRF,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL,CAAC;AAEDZ,cAAc,CAAC8C,WAAW,GAAG,gBAAgB;AAC7C,IAAIC,iBAAiB,GAAG,aAAatD,KAAK,CAACuD,UAAU,CAAChD,cAAc,CAAC;AACrE,eAAe+C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}