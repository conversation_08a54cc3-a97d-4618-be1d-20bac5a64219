{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\dashboardRespMagazzino.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardResponsabileMagazzino - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport Dashboard from \"../../components/navigation/dashboard\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DashboardRespMagazzino extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album mt-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {\n            dashResp: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DashboardRespMagazzino;", "map": {"version": 3, "names": ["React", "Component", "Dashboard", "Nav", "jsxDEV", "_jsxDEV", "DashboardRespMagazzino", "constructor", "props", "state", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dashResp"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/dashboardRespMagazzino.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardResponsabileMagazzino - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport Dashboard from \"../../components/navigation/dashboard\";\nimport Nav from \"../../components/navigation/Nav\";\n\nclass DashboardRespMagazzino extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {};\n    }\n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <div className=\"album mt-5\">\n                    <div className=\"container\">\n                        <Dashboard dashResp={true} />\n                    </div>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default DashboardRespMagazzino;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,SAAS,MAAM,uCAAuC;AAC7D,OAAOC,GAAG,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,sBAAsB,SAASL,SAAS,CAAC;EAC3CM,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,MAAMA,CAAA,EAAG;IACL,oBACIL,OAAA;MAAKM,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BP,OAAA,CAACF,GAAG;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPX,OAAA;QAAKM,SAAS,EAAC,YAAY;QAAAC,QAAA,eACvBP,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBP,OAAA,CAACH,SAAS;YAACe,QAAQ,EAAE;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAeV,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}