{"version": 3, "file": "tsconfig.js", "sourceRoot": "", "sources": ["../../src/tsconfig.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AACnC,OAAO,EACL,UAAU,EACV,WAAW,EACX,UAAU,EACV,aAAa,GACd,MAAM,SAAS,CAAA;AAChB,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;AACnC,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAA;AACtC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAEvC,uEAAuE;AACvE,OAAO,MAAM,MAAM,aAAa,CAAA;AAChC,OAAO,SAAS,MAAM,gBAAgB,CAAA;AACtC,OAAO,2BAA2B,MAAM,qCAAqC,CAAA;AAC7E,OAAO,oBAAoB,MAAM,6BAA6B,CAAA;AAE9D,MAAM,EACJ,QAAQ,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,EAC9B,WAAW,GAAG,EAAE,EAChB,gBAAgB,GAAG,EAAE,EACrB,OAAO,GAAG,EAAE,GACb,GAAG,MAAM,CAAA;AAEV,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CACjC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CACpC,CAAA;AAED,MAAM,WAAW,GAAwB;IACvC,eAAe,EAAE;QACf,WAAW,EAAE,IAAI;QACjB,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;QACrB,gCAAgC,EAAE,IAAI;QACtC,aAAa,EAAE,IAAI;QACnB,GAAG,EAAE,OAAO;QACZ,MAAM,EAAE,UAAU;QAClB,gBAAgB,EAAE,UAAU;QAC5B,wBAAwB,EAAE,IAAI;QAC9B,iBAAiB,EAAE,IAAI;QACvB,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,QAAQ;KACjB;CACF,CAAA;AAED,MAAM,KAAK,GAAG,GAAwB,EAAE,CAAC,CAAC;IACxC,OAAO,EACL,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC;QAC5B,kBAAkB;QACpB,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC;IAC9B,eAAe,EAAE;QACf,MAAM,EACJ,oBAAoB,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;YACnD,QAAQ;YACV,CAAC,CAAC,SAAS;QACb,OAAO,EAAE,QAAQ;QACjB,MAAM,EAAE,UAAU;QAClB,gBAAgB,EAAE,UAAU;KAC7B;CACF,CAAC,CAAA;AAEF,MAAM,QAAQ,GAAG,CAAC,OAAe,EAAuB,EAAE;IACxD,MAAM,OAAO,GAAG;QACd,GAAG,eAAe;QAClB,iBAAiB;QACjB,qBAAqB;KACtB,CAAA;IACD,KAAK,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,KAAK,OAAO;YAAE,SAAQ;QAC3B,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAC/B,CAAC;IACH,CAAC;IACD,OAAO;QACL,OAAO,EAAE,cAAc;QACvB,OAAO,EAAE;YACP,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB;SACnB;QACD,OAAO;QACP,eAAe,EAAE;YACf,MAAM,EACJ,iBAAiB;gBACjB,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC;SAC7C;KACF,CAAA;AACH,CAAC,CAAA;AAED,MAAM,GAAG,GAAG,CAAC,OAAe,EAAuB,EAAE;IACnD,MAAM,OAAO,GAAa;QACxB,GAAG,eAAe;QAClB,qBAAqB;KACtB,CAAA;IACD,KAAK,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,KAAK,OAAO;YAAE,SAAQ;QAC3B,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;QAC9C,CAAC;IACH,CAAC;IACD,OAAO;QACL,OAAO,EAAE,cAAc;QACvB,OAAO,EAAE;YACP,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB;SACnB;QACD,OAAO;QACP,eAAe,EAAE;YACf,MAAM,EAAE,iBAAiB,GAAG,OAAO;SACpC;KACF,CAAA;AACH,CAAC,CAAA;AAED,UAAU,CAAC,OAAO,CAAC,CAAA;AACnB,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,IAAyB,EAAE,EAAE,CAC9D,aAAa,CACX,SAAS,IAAI,OAAO,EACpB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CACrC,CAAA;AAEH,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,CAAA;AAC1D,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;IACjE,OAAO,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;IAChD,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;AACzC,CAAC;KAAM,CAAC;IACN,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC;QAAE,2BAA2B,EAAE,CAAA;IACtD,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;AAC/C,CAAC;AACD,KAAK,MAAM,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;IACrC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;AACjC,CAAC;AACD,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;AAC7B,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;IAClC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IACxC,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE,CAAC;QACjC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7B,CAAC;AACH,CAAC;AACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7B,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;IAC9B,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC;QAC5B,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;AACH,CAAC", "sourcesContent": ["import chalk from 'chalk'\nimport { mkdirpSync } from 'mkdirp'\nimport {\n  existsSync,\n  readdirSync,\n  unlinkSync,\n  writeFileSync,\n} from 'node:fs'\nimport { resolve } from 'node:path'\nimport { join } from 'node:path/posix'\nimport * as console from './console.js'\n\n// the commonjs build needs to exclude anything that will be polyfilled\nimport config from './config.js'\nimport polyfills from './polyfills.js'\nimport preventVerbatimModuleSyntax from './prevent-verbatim-module-syntax.js'\nimport readTypescriptConfig from './read-typescript-config.js'\n\nconst {\n  dialects = ['esm', 'commonjs'],\n  esmDialects = [],\n  commonjsDialects = [],\n  exclude = [],\n} = config\n\nconst relativeExclude = exclude.map(\n  e => `../${e.replace(/^\\.\\//, '')}`,\n)\n\nconst recommended: Record<string, any> = {\n  compilerOptions: {\n    declaration: true,\n    declarationMap: true,\n    esModuleInterop: true,\n    forceConsistentCasingInFileNames: true,\n    inlineSources: true,\n    jsx: 'react',\n    module: 'nodenext',\n    moduleResolution: 'nodenext',\n    noUncheckedIndexedAccess: true,\n    resolveJsonModule: true,\n    skipLibCheck: true,\n    sourceMap: true,\n    strict: true,\n    target: 'es2022',\n  },\n}\n\nconst build = (): Record<string, any> => ({\n  extends:\n    config.project === undefined ?\n      '../tsconfig.json'\n    : join('..', config.project),\n  compilerOptions: {\n    target:\n      readTypescriptConfig().options.target === undefined ?\n        'es2022'\n      : undefined,\n    rootDir: '../src',\n    module: 'nodenext',\n    moduleResolution: 'nodenext',\n  },\n})\n\nconst commonjs = (dialect: string): Record<string, any> => {\n  const exclude = [\n    ...relativeExclude,\n    '../src/**/*.mts',\n    '../src/package.json',\n  ]\n  for (const [d, pf] of polyfills) {\n    if (d === dialect) continue\n    for (const f of pf.map.keys()) {\n      exclude.push(`../${join(f)}`)\n    }\n  }\n  return {\n    extends: './build.json',\n    include: [\n      '../src/**/*.ts',\n      '../src/**/*.cts',\n      '../src/**/*.tsx',\n      '../src/**/*.json',\n    ],\n    exclude,\n    compilerOptions: {\n      outDir:\n        '../.tshy-build/' +\n        (dialect === 'cjs' ? 'commonjs' : dialect),\n    },\n  }\n}\n\nconst esm = (dialect: string): Record<string, any> => {\n  const exclude: string[] = [\n    ...relativeExclude,\n    '../src/package.json',\n  ]\n  for (const [d, pf] of polyfills) {\n    if (d === dialect) continue\n    for (const f of pf.map.keys()) {\n      exclude.push(`../${f.replace(/^\\.\\//, '')}`)\n    }\n  }\n  return {\n    extends: './build.json',\n    include: [\n      '../src/**/*.ts',\n      '../src/**/*.mts',\n      '../src/**/*.tsx',\n      '../src/**/*.json',\n    ],\n    exclude,\n    compilerOptions: {\n      outDir: '../.tshy-build/' + dialect,\n    },\n  }\n}\n\nmkdirpSync('.tshy')\nconst writeConfig = (name: string, data: Record<string, any>) =>\n  writeFileSync(\n    `.tshy/${name}.json`,\n    JSON.stringify(data, null, 2) + '\\n',\n  )\n\nconsole.debug(chalk.cyan.dim('writing tsconfig files...'))\nif (config.project === undefined && !existsSync('tsconfig.json')) {\n  console.debug('using recommended tsconfig.json')\n  writeConfig('../tsconfig', recommended)\n} else {\n  if (dialects.length > 1) preventVerbatimModuleSyntax()\n  console.debug('using existing tsconfig.json')\n}\nfor (const f of readdirSync('.tshy')) {\n  unlinkSync(resolve('.tshy', f))\n}\nwriteConfig('build', build())\nif (dialects.includes('commonjs')) {\n  writeConfig('commonjs', commonjs('cjs'))\n  for (const d of commonjsDialects) {\n    writeConfig(d, commonjs(d))\n  }\n}\nif (dialects.includes('esm')) {\n  writeConfig('esm', esm('esm'))\n  for (const d of esmDialects) {\n    writeConfig(d, esm(d))\n  }\n}\n"]}