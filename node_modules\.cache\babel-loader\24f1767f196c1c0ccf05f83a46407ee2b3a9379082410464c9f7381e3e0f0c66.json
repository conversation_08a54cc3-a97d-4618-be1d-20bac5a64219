{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { List } from 'rc-field-form';\nimport devWarning from '../_util/devWarning';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemPrefixContext } from './context';\nvar FormList = function FormList(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    children = _a.children,\n    props = __rest(_a, [\"prefixCls\", \"children\"]);\n  devWarning(!!props.name, 'Form.List', 'Miss `name` prop.');\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('form', customizePrefixCls);\n  var contextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      status: 'error'\n    };\n  }, [prefixCls]);\n  return /*#__PURE__*/React.createElement(List, props, function (fields, operation, meta) {\n    return /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n      value: contextValue\n    }, children(fields.map(function (field) {\n      return _extends(_extends({}, field), {\n        fieldKey: field.key\n      });\n    }), operation, {\n      errors: meta.errors,\n      warnings: meta.warnings\n    }));\n  });\n};\nexport default FormList;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "List", "dev<PERSON><PERSON><PERSON>", "ConfigContext", "FormItemPrefixContext", "FormList", "_a", "customizePrefixCls", "prefixCls", "children", "props", "name", "_React$useContext", "useContext", "getPrefixCls", "contextValue", "useMemo", "status", "createElement", "fields", "operation", "meta", "Provider", "value", "map", "field", "<PERSON><PERSON><PERSON>", "key", "errors", "warnings"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/FormList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport { List } from 'rc-field-form';\nimport devWarning from '../_util/devWarning';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemPrefixContext } from './context';\n\nvar FormList = function FormList(_a) {\n  var customizePrefixCls = _a.prefixCls,\n      children = _a.children,\n      props = __rest(_a, [\"prefixCls\", \"children\"]);\n\n  devWarning(!!props.name, 'Form.List', 'Miss `name` prop.');\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('form', customizePrefixCls);\n  var contextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      status: 'error'\n    };\n  }, [prefixCls]);\n  return /*#__PURE__*/React.createElement(List, props, function (fields, operation, meta) {\n    return /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n      value: contextValue\n    }, children(fields.map(function (field) {\n      return _extends(_extends({}, field), {\n        fieldKey: field.key\n      });\n    }), operation, {\n      errors: meta.errors,\n      warnings: meta.warnings\n    }));\n  });\n};\n\nexport default FormList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AAEzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,eAAe;AACpC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,qBAAqB,QAAQ,WAAW;AAEjD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,EAAE,EAAE;EACnC,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACjCC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;IACtBC,KAAK,GAAGxB,MAAM,CAACoB,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EAEjDJ,UAAU,CAAC,CAAC,CAACQ,KAAK,CAACC,IAAI,EAAE,WAAW,EAAE,mBAAmB,CAAC;EAE1D,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACV,aAAa,CAAC;IACnDW,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIN,SAAS,GAAGM,YAAY,CAAC,MAAM,EAAEP,kBAAkB,CAAC;EACxD,IAAIQ,YAAY,GAAGf,KAAK,CAACgB,OAAO,CAAC,YAAY;IAC3C,OAAO;MACLR,SAAS,EAAEA,SAAS;MACpBS,MAAM,EAAE;IACV,CAAC;EACH,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EACf,OAAO,aAAaR,KAAK,CAACkB,aAAa,CAACjB,IAAI,EAAES,KAAK,EAAE,UAAUS,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAE;IACtF,OAAO,aAAarB,KAAK,CAACkB,aAAa,CAACd,qBAAqB,CAACkB,QAAQ,EAAE;MACtEC,KAAK,EAAER;IACT,CAAC,EAAEN,QAAQ,CAACU,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;MACtC,OAAOxC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,CAAC,EAAE;QACnCC,QAAQ,EAAED,KAAK,CAACE;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC,EAAEP,SAAS,EAAE;MACbQ,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBC,QAAQ,EAAER,IAAI,CAACQ;IACjB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AAED,eAAexB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}