{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { SpaceContext } from '.';\nexport default function Item(_ref) {\n  var className = _ref.className,\n    direction = _ref.direction,\n    index = _ref.index,\n    marginDirection = _ref.marginDirection,\n    children = _ref.children,\n    split = _ref.split,\n    wrap = _ref.wrap;\n  var _React$useContext = React.useContext(SpaceContext),\n    horizontalSize = _React$useContext.horizontalSize,\n    verticalSize = _React$useContext.verticalSize,\n    latestIndex = _React$useContext.latestIndex,\n    supportFlexGap = _React$useContext.supportFlexGap;\n  var style = {};\n  if (!supportFlexGap) {\n    if (direction === 'vertical') {\n      if (index < latestIndex) {\n        style = {\n          marginBottom: horizontalSize / (split ? 2 : 1)\n        };\n      }\n    } else {\n      style = _extends(_extends({}, index < latestIndex && _defineProperty({}, marginDirection, horizontalSize / (split ? 2 : 1))), wrap && {\n        paddingBottom: verticalSize\n      });\n    }\n  }\n  if (children === null || children === undefined) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, children), index < latestIndex && split && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(className, \"-split\"),\n    style: style\n  }, split));\n}", "map": {"version": 3, "names": ["_defineProperty", "_extends", "React", "SpaceContext", "<PERSON><PERSON>", "_ref", "className", "direction", "index", "marginDirection", "children", "split", "wrap", "_React$useContext", "useContext", "horizontalSize", "verticalSize", "latestIndex", "supportFlexGap", "style", "marginBottom", "paddingBottom", "undefined", "createElement", "Fragment", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/space/Item.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { SpaceContext } from '.';\nexport default function Item(_ref) {\n  var className = _ref.className,\n      direction = _ref.direction,\n      index = _ref.index,\n      marginDirection = _ref.marginDirection,\n      children = _ref.children,\n      split = _ref.split,\n      wrap = _ref.wrap;\n\n  var _React$useContext = React.useContext(SpaceContext),\n      horizontalSize = _React$useContext.horizontalSize,\n      verticalSize = _React$useContext.verticalSize,\n      latestIndex = _React$useContext.latestIndex,\n      supportFlexGap = _React$useContext.supportFlexGap;\n\n  var style = {};\n\n  if (!supportFlexGap) {\n    if (direction === 'vertical') {\n      if (index < latestIndex) {\n        style = {\n          marginBottom: horizontalSize / (split ? 2 : 1)\n        };\n      }\n    } else {\n      style = _extends(_extends({}, index < latestIndex && _defineProperty({}, marginDirection, horizontalSize / (split ? 2 : 1))), wrap && {\n        paddingBottom: verticalSize\n      });\n    }\n  }\n\n  if (children === null || children === undefined) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, children), index < latestIndex && split && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(className, \"-split\"),\n    style: style\n  }, split));\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,GAAG;AAChC,eAAe,SAASC,IAAIA,CAACC,IAAI,EAAE;EACjC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,eAAe,GAAGJ,IAAI,CAACI,eAAe;IACtCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,IAAI,GAAGP,IAAI,CAACO,IAAI;EAEpB,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACX,YAAY,CAAC;IAClDY,cAAc,GAAGF,iBAAiB,CAACE,cAAc;IACjDC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;IAC7CC,WAAW,GAAGJ,iBAAiB,CAACI,WAAW;IAC3CC,cAAc,GAAGL,iBAAiB,CAACK,cAAc;EAErD,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,IAAI,CAACD,cAAc,EAAE;IACnB,IAAIX,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAIC,KAAK,GAAGS,WAAW,EAAE;QACvBE,KAAK,GAAG;UACNC,YAAY,EAAEL,cAAc,IAAIJ,KAAK,GAAG,CAAC,GAAG,CAAC;QAC/C,CAAC;MACH;IACF,CAAC,MAAM;MACLQ,KAAK,GAAGlB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,GAAGS,WAAW,IAAIjB,eAAe,CAAC,CAAC,CAAC,EAAES,eAAe,EAAEM,cAAc,IAAIJ,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEC,IAAI,IAAI;QACpIS,aAAa,EAAEL;MACjB,CAAC,CAAC;IACJ;EACF;EAEA,IAAIN,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKY,SAAS,EAAE;IAC/C,OAAO,IAAI;EACb;EAEA,OAAO,aAAapB,KAAK,CAACqB,aAAa,CAACrB,KAAK,CAACsB,QAAQ,EAAE,IAAI,EAAE,aAAatB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACpGjB,SAAS,EAAEA,SAAS;IACpBa,KAAK,EAAEA;EACT,CAAC,EAAET,QAAQ,CAAC,EAAEF,KAAK,GAAGS,WAAW,IAAIN,KAAK,IAAI,aAAaT,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;IACrFjB,SAAS,EAAE,EAAE,CAACmB,MAAM,CAACnB,SAAS,EAAE,QAAQ,CAAC;IACzCa,KAAK,EAAEA;EACT,CAAC,EAAER,KAAK,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}