{"version": 3, "file": "SemanticResourceAttributes.js", "sourceRoot": "", "sources": ["../../../src/resource/SemanticResourceAttributes.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,iHAAiH;AACpG,QAAA,0BAA0B,GAAG;IACxC;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;;;OAIG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;;;OAIG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;;;OAIG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;;;OAIG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;;;OAIG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;;;OAIG;IACH,SAAS,EAAE,WAAW;IAEtB;;;;OAIG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;;;OAIG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;;;OAIG;IACH,SAAS,EAAE,WAAW;IAEtB;;;;;;;;;;;;;;;;;MAiBE;IACF,OAAO,EAAE,SAAS;IAElB;;;;;;;;;;;;MAYE;IACF,YAAY,EAAE,cAAc;IAE5B;;;;OAIG;IACH,aAAa,EAAE,eAAe;IAE9B;;;;OAIG;IACH,eAAe,EAAE,iBAAiB;IAElC;;OAEG;IACH,OAAO,EAAE,SAAS;IAElB;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,eAAe,EAAE,iBAAiB;IAElC;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,eAAe,EAAE,iBAAiB;IAElC;;OAEG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;OAEG;IACH,OAAO,EAAE,SAAS;IAElB;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,OAAO,EAAE,SAAS;IAElB;;OAEG;IACH,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,eAAe,EAAE,iBAAiB;IAElC;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;;;OAIG;IACH,YAAY,EAAE,cAAc;IAE5B;;;;OAIG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;;;OAIG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,eAAe,EAAE,iBAAiB;IAElC;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;CAC/C,CAAC;AAEW,QAAA,mBAAmB,GAAG;IACjC,qBAAqB;IACrB,aAAa,EAAE,eAAe;IAC9B,2BAA2B;IAC3B,GAAG,EAAE,KAAK;IACV,uBAAuB;IACvB,KAAK,EAAE,OAAO;IACd,6BAA6B;IAC7B,GAAG,EAAE,KAAK;CACF,CAAC;AAIE,QAAA,mBAAmB,GAAG;IACjC,6CAA6C;IAC7C,iBAAiB,EAAE,mBAAmB;IACtC,sCAAsC;IACtC,gBAAgB,EAAE,kBAAkB;IACpC,iCAAiC;IACjC,OAAO,EAAE,SAAS;IAClB,qCAAqC;IACrC,OAAO,EAAE,SAAS;IAClB,sCAAsC;IACtC,OAAO,EAAE,SAAS;IAClB,kBAAkB;IAClB,UAAU,EAAE,YAAY;IACxB,6BAA6B;IAC7B,qBAAqB,EAAE,uBAAuB;IAC9C,8BAA8B;IAC9B,QAAQ,EAAE,UAAU;IACpB,iCAAiC;IACjC,yBAAyB,EAAE,2BAA2B;IACtD,gCAAgC;IAChC,SAAS,EAAE,WAAW;IACtB,uBAAuB;IACvB,eAAe,EAAE,iBAAiB;IAClC,yBAAyB;IACzB,iBAAiB,EAAE,mBAAmB;IACtC,yCAAyC;IACzC,kBAAkB,EAAE,oBAAoB;IACxC,wBAAwB;IACxB,aAAa,EAAE,eAAe;IAC9B,4CAA4C;IAC5C,qBAAqB,EAAE,uBAAuB;IAC9C,oCAAoC;IACpC,mBAAmB,EAAE,qBAAqB;IAC1C,qCAAqC;IACrC,cAAc,EAAE,gBAAgB;CACxB,CAAC;AAIE,QAAA,sBAAsB,GAAG;IACpC,WAAW;IACX,GAAG,EAAE,KAAK;IACV,eAAe;IACf,OAAO,EAAE,SAAS;CACV,CAAC;AAIE,QAAA,cAAc,GAAG;IAC5B,aAAa;IACb,KAAK,EAAE,OAAO;IACd,aAAa;IACb,KAAK,EAAE,OAAO;IACd,aAAa;IACb,KAAK,EAAE,OAAO;IACd,eAAe;IACf,IAAI,EAAE,MAAM;IACZ,sBAAsB;IACtB,KAAK,EAAE,OAAO;IACd,sBAAsB;IACtB,KAAK,EAAE,OAAO;IACd,kBAAkB;IAClB,GAAG,EAAE,KAAK;CACF,CAAC;AAIE,QAAA,YAAY,GAAG;IAC1B,yBAAyB;IACzB,OAAO,EAAE,SAAS;IAClB,aAAa;IACb,KAAK,EAAE,OAAO;IACd,oBAAoB;IACpB,MAAM,EAAE,QAAQ;IAChB,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,qBAAqB;IACrB,YAAY,EAAE,cAAc;IAC5B,oCAAoC;IACpC,IAAI,EAAE,MAAM;IACZ,4CAA4C;IAC5C,GAAG,EAAE,KAAK;IACV,sBAAsB;IACtB,OAAO,EAAE,SAAS;IAClB,gBAAgB;IAChB,IAAI,EAAE,MAAM;CACJ,CAAC;AAGE,QAAA,0BAA0B,GAAG;IACxC,WAAW;IACX,GAAG,EAAE,KAAK;IACV,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,UAAU;IACV,EAAE,EAAE,IAAI;IACR,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,WAAW;IACX,GAAG,EAAE,KAAK;IACV,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,aAAa;IACb,KAAK,EAAE,OAAO;CACN,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexport const SemanticResourceAttributes = {\n  /**\n   * Name of the cloud provider.\n   */\n  CLOUD_PROVIDER: 'cloud.provider',\n\n  /**\n   * The cloud account ID the resource is assigned to.\n   */\n  CLOUD_ACCOUNT_ID: 'cloud.account.id',\n\n  /**\n   * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n   */\n  CLOUD_REGION: 'cloud.region',\n\n  /**\n   * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n   *\n   * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n   */\n  CLOUD_AVAILABILITY_ZONE: 'cloud.availability_zone',\n\n  /**\n   * The cloud platform in use.\n   *\n   * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n   */\n  CLOUD_PLATFORM: 'cloud.platform',\n\n  /**\n   * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n   */\n  AWS_ECS_CONTAINER_ARN: 'aws.ecs.container.arn',\n\n  /**\n   * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n   */\n  AWS_ECS_CLUSTER_ARN: 'aws.ecs.cluster.arn',\n\n  /**\n   * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n   */\n  AWS_ECS_LAUNCHTYPE: 'aws.ecs.launchtype',\n\n  /**\n   * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n   */\n  AWS_ECS_TASK_ARN: 'aws.ecs.task.arn',\n\n  /**\n   * The task definition family this task definition is a member of.\n   */\n  AWS_ECS_TASK_FAMILY: 'aws.ecs.task.family',\n\n  /**\n   * The revision for this task definition.\n   */\n  AWS_ECS_TASK_REVISION: 'aws.ecs.task.revision',\n\n  /**\n   * The ARN of an EKS cluster.\n   */\n  AWS_EKS_CLUSTER_ARN: 'aws.eks.cluster.arn',\n\n  /**\n   * The name(s) of the AWS log group(s) an application is writing to.\n   *\n   * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n   */\n  AWS_LOG_GROUP_NAMES: 'aws.log.group.names',\n\n  /**\n   * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n   *\n   * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n   */\n  AWS_LOG_GROUP_ARNS: 'aws.log.group.arns',\n\n  /**\n   * The name(s) of the AWS log stream(s) an application is writing to.\n   */\n  AWS_LOG_STREAM_NAMES: 'aws.log.stream.names',\n\n  /**\n   * The ARN(s) of the AWS log stream(s).\n   *\n   * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n   */\n  AWS_LOG_STREAM_ARNS: 'aws.log.stream.arns',\n\n  /**\n   * Container name.\n   */\n  CONTAINER_NAME: 'container.name',\n\n  /**\n   * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n   */\n  CONTAINER_ID: 'container.id',\n\n  /**\n   * The container runtime managing this container.\n   */\n  CONTAINER_RUNTIME: 'container.runtime',\n\n  /**\n   * Name of the image the container was built on.\n   */\n  CONTAINER_IMAGE_NAME: 'container.image.name',\n\n  /**\n   * Container image tag.\n   */\n  CONTAINER_IMAGE_TAG: 'container.image.tag',\n\n  /**\n   * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n   */\n  DEPLOYMENT_ENVIRONMENT: 'deployment.environment',\n\n  /**\n   * A unique identifier representing the device.\n   *\n   * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n   */\n  DEVICE_ID: 'device.id',\n\n  /**\n   * The model identifier for the device.\n   *\n   * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n   */\n  DEVICE_MODEL_IDENTIFIER: 'device.model.identifier',\n\n  /**\n   * The marketing name for the device model.\n   *\n   * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n   */\n  DEVICE_MODEL_NAME: 'device.model.name',\n\n  /**\n   * The name of the single function that this runtime instance executes.\n   *\n   * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n   */\n  FAAS_NAME: 'faas.name',\n\n  /**\n  * The unique ID of the single function that this runtime instance executes.\n  *\n  * Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n  */\n  FAAS_ID: 'faas.id',\n\n  /**\n  * The immutable version of the function being executed.\n  *\n  * Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n  */\n  FAAS_VERSION: 'faas.version',\n\n  /**\n   * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n   *\n   * Note: * **AWS Lambda:** Use the (full) log stream name.\n   */\n  FAAS_INSTANCE: 'faas.instance',\n\n  /**\n   * The amount of memory available to the serverless function in MiB.\n   *\n   * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n   */\n  FAAS_MAX_MEMORY: 'faas.max_memory',\n\n  /**\n   * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n   */\n  HOST_ID: 'host.id',\n\n  /**\n   * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n   */\n  HOST_NAME: 'host.name',\n\n  /**\n   * Type of host. For Cloud, this must be the machine type.\n   */\n  HOST_TYPE: 'host.type',\n\n  /**\n   * The CPU architecture the host system is running on.\n   */\n  HOST_ARCH: 'host.arch',\n\n  /**\n   * Name of the VM image or OS install the host was instantiated from.\n   */\n  HOST_IMAGE_NAME: 'host.image.name',\n\n  /**\n   * VM image ID. For Cloud, this value is from the provider.\n   */\n  HOST_IMAGE_ID: 'host.image.id',\n\n  /**\n   * The version string of the VM image as defined in [Version SpanAttributes](README.md#version-attributes).\n   */\n  HOST_IMAGE_VERSION: 'host.image.version',\n\n  /**\n   * The name of the cluster.\n   */\n  K8S_CLUSTER_NAME: 'k8s.cluster.name',\n\n  /**\n   * The name of the Node.\n   */\n  K8S_NODE_NAME: 'k8s.node.name',\n\n  /**\n   * The UID of the Node.\n   */\n  K8S_NODE_UID: 'k8s.node.uid',\n\n  /**\n   * The name of the namespace that the pod is running in.\n   */\n  K8S_NAMESPACE_NAME: 'k8s.namespace.name',\n\n  /**\n   * The UID of the Pod.\n   */\n  K8S_POD_UID: 'k8s.pod.uid',\n\n  /**\n   * The name of the Pod.\n   */\n  K8S_POD_NAME: 'k8s.pod.name',\n\n  /**\n   * The name of the Container in a Pod template.\n   */\n  K8S_CONTAINER_NAME: 'k8s.container.name',\n\n  /**\n   * The UID of the ReplicaSet.\n   */\n  K8S_REPLICASET_UID: 'k8s.replicaset.uid',\n\n  /**\n   * The name of the ReplicaSet.\n   */\n  K8S_REPLICASET_NAME: 'k8s.replicaset.name',\n\n  /**\n   * The UID of the Deployment.\n   */\n  K8S_DEPLOYMENT_UID: 'k8s.deployment.uid',\n\n  /**\n   * The name of the Deployment.\n   */\n  K8S_DEPLOYMENT_NAME: 'k8s.deployment.name',\n\n  /**\n   * The UID of the StatefulSet.\n   */\n  K8S_STATEFULSET_UID: 'k8s.statefulset.uid',\n\n  /**\n   * The name of the StatefulSet.\n   */\n  K8S_STATEFULSET_NAME: 'k8s.statefulset.name',\n\n  /**\n   * The UID of the DaemonSet.\n   */\n  K8S_DAEMONSET_UID: 'k8s.daemonset.uid',\n\n  /**\n   * The name of the DaemonSet.\n   */\n  K8S_DAEMONSET_NAME: 'k8s.daemonset.name',\n\n  /**\n   * The UID of the Job.\n   */\n  K8S_JOB_UID: 'k8s.job.uid',\n\n  /**\n   * The name of the Job.\n   */\n  K8S_JOB_NAME: 'k8s.job.name',\n\n  /**\n   * The UID of the CronJob.\n   */\n  K8S_CRONJOB_UID: 'k8s.cronjob.uid',\n\n  /**\n   * The name of the CronJob.\n   */\n  K8S_CRONJOB_NAME: 'k8s.cronjob.name',\n\n  /**\n   * The operating system type.\n   */\n  OS_TYPE: 'os.type',\n\n  /**\n   * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n   */\n  OS_DESCRIPTION: 'os.description',\n\n  /**\n   * Human readable operating system name.\n   */\n  OS_NAME: 'os.name',\n\n  /**\n   * The version string of the operating system as defined in [Version SpanAttributes](../../resource/semantic_conventions/README.md#version-attributes).\n   */\n  OS_VERSION: 'os.version',\n\n  /**\n   * Process identifier (PID).\n   */\n  PROCESS_PID: 'process.pid',\n\n  /**\n   * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_NAME: 'process.executable.name',\n\n  /**\n   * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_PATH: 'process.executable.path',\n\n  /**\n   * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n   */\n  PROCESS_COMMAND: 'process.command',\n\n  /**\n   * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n   */\n  PROCESS_COMMAND_LINE: 'process.command_line',\n\n  /**\n   * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n   */\n  PROCESS_COMMAND_ARGS: 'process.command_args',\n\n  /**\n   * The username of the user that owns the process.\n   */\n  PROCESS_OWNER: 'process.owner',\n\n  /**\n   * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n   */\n  PROCESS_RUNTIME_NAME: 'process.runtime.name',\n\n  /**\n   * The version of the runtime of this process, as returned by the runtime without modification.\n   */\n  PROCESS_RUNTIME_VERSION: 'process.runtime.version',\n\n  /**\n   * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n   */\n  PROCESS_RUNTIME_DESCRIPTION: 'process.runtime.description',\n\n  /**\n   * Logical name of the service.\n   *\n   * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n   */\n  SERVICE_NAME: 'service.name',\n\n  /**\n   * A namespace for `service.name`.\n   *\n   * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n   */\n  SERVICE_NAMESPACE: 'service.namespace',\n\n  /**\n   * The string ID of the service instance.\n   *\n   * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n   */\n  SERVICE_INSTANCE_ID: 'service.instance.id',\n\n  /**\n   * The version string of the service API or implementation.\n   */\n  SERVICE_VERSION: 'service.version',\n\n  /**\n   * The name of the telemetry SDK as defined above.\n   */\n  TELEMETRY_SDK_NAME: 'telemetry.sdk.name',\n\n  /**\n   * The language of the telemetry SDK.\n   */\n  TELEMETRY_SDK_LANGUAGE: 'telemetry.sdk.language',\n\n  /**\n   * The version string of the telemetry SDK.\n   */\n  TELEMETRY_SDK_VERSION: 'telemetry.sdk.version',\n\n  /**\n   * The version string of the auto instrumentation agent, if used.\n   */\n  TELEMETRY_AUTO_VERSION: 'telemetry.auto.version',\n\n  /**\n   * The name of the web engine.\n   */\n  WEBENGINE_NAME: 'webengine.name',\n\n  /**\n   * The version of the web engine.\n   */\n  WEBENGINE_VERSION: 'webengine.version',\n\n  /**\n   * Additional description of the web engine (e.g. detailed version and edition information).\n   */\n  WEBENGINE_DESCRIPTION: 'webengine.description',\n};\n\nexport const CloudProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud',\n  /** Amazon Web Services. */\n  AWS: 'aws',\n  /** Microsoft Azure. */\n  AZURE: 'azure',\n  /** Google Cloud Platform. */\n  GCP: 'gcp',\n} as const;\nexport type CloudProviderValues =\n  (typeof CloudProviderValues)[keyof typeof CloudProviderValues];\n\nexport const CloudPlatformValues = {\n  /** Alibaba Cloud Elastic Compute Service. */\n  ALIBABA_CLOUD_ECS: 'alibaba_cloud_ecs',\n  /** Alibaba Cloud Function Compute. */\n  ALIBABA_CLOUD_FC: 'alibaba_cloud_fc',\n  /** AWS Elastic Compute Cloud. */\n  AWS_EC2: 'aws_ec2',\n  /** AWS Elastic Container Service. */\n  AWS_ECS: 'aws_ecs',\n  /** AWS Elastic Kubernetes Service. */\n  AWS_EKS: 'aws_eks',\n  /** AWS Lambda. */\n  AWS_LAMBDA: 'aws_lambda',\n  /** AWS Elastic Beanstalk. */\n  AWS_ELASTIC_BEANSTALK: 'aws_elastic_beanstalk',\n  /** Azure Virtual Machines. */\n  AZURE_VM: 'azure_vm',\n  /** Azure Container Instances. */\n  AZURE_CONTAINER_INSTANCES: 'azure_container_instances',\n  /** Azure Kubernetes Service. */\n  AZURE_AKS: 'azure_aks',\n  /** Azure Functions. */\n  AZURE_FUNCTIONS: 'azure_functions',\n  /** Azure App Service. */\n  AZURE_APP_SERVICE: 'azure_app_service',\n  /** Google Cloud Compute Engine (GCE). */\n  GCP_COMPUTE_ENGINE: 'gcp_compute_engine',\n  /** Google Cloud Run. */\n  GCP_CLOUD_RUN: 'gcp_cloud_run',\n  /** Google Cloud Kubernetes Engine (GKE). */\n  GCP_KUBERNETES_ENGINE: 'gcp_kubernetes_engine',\n  /** Google Cloud Functions (GCF). */\n  GCP_CLOUD_FUNCTIONS: 'gcp_cloud_functions',\n  /** Google Cloud App Engine (GAE). */\n  GCP_APP_ENGINE: 'gcp_app_engine',\n} as const;\nexport type CloudPlatformValues =\n  (typeof CloudPlatformValues)[keyof typeof CloudPlatformValues];\n\nexport const AwsEcsLaunchtypeValues = {\n  /** ec2. */\n  EC2: 'ec2',\n  /** fargate. */\n  FARGATE: 'fargate',\n} as const;\nexport type AwsEcsLaunchtypeValues =\n  (typeof AwsEcsLaunchtypeValues)[keyof typeof AwsEcsLaunchtypeValues];\n\nexport const HostArchValues = {\n  /** AMD64. */\n  AMD64: 'amd64',\n  /** ARM32. */\n  ARM32: 'arm32',\n  /** ARM64. */\n  ARM64: 'arm64',\n  /** Itanium. */\n  IA64: 'ia64',\n  /** 32-bit PowerPC. */\n  PPC32: 'ppc32',\n  /** 64-bit PowerPC. */\n  PPC64: 'ppc64',\n  /** 32-bit x86. */\n  X86: 'x86',\n} as const;\nexport type HostArchValues =\n  (typeof HostArchValues)[keyof typeof HostArchValues];\n\nexport const OsTypeValues = {\n  /** Microsoft Windows. */\n  WINDOWS: 'windows',\n  /** Linux. */\n  LINUX: 'linux',\n  /** Apple Darwin. */\n  DARWIN: 'darwin',\n  /** FreeBSD. */\n  FREEBSD: 'freebsd',\n  /** NetBSD. */\n  NETBSD: 'netbsd',\n  /** OpenBSD. */\n  OPENBSD: 'openbsd',\n  /** DragonFly BSD. */\n  DRAGONFLYBSD: 'dragonflybsd',\n  /** HP-UX (Hewlett Packard Unix). */\n  HPUX: 'hpux',\n  /** AIX (Advanced Interactive eXecutive). */\n  AIX: 'aix',\n  /** Oracle Solaris. */\n  SOLARIS: 'solaris',\n  /** IBM z/OS. */\n  Z_OS: 'z_os',\n} as const;\nexport type OsTypeValues = (typeof OsTypeValues)[keyof typeof OsTypeValues];\n\nexport const TelemetrySdkLanguageValues = {\n  /** cpp. */\n  CPP: 'cpp',\n  /** dotnet. */\n  DOTNET: 'dotnet',\n  /** erlang. */\n  ERLANG: 'erlang',\n  /** go. */\n  GO: 'go',\n  /** java. */\n  JAVA: 'java',\n  /** nodejs. */\n  NODEJS: 'nodejs',\n  /** php. */\n  PHP: 'php',\n  /** python. */\n  PYTHON: 'python',\n  /** ruby. */\n  RUBY: 'ruby',\n  /** webjs. */\n  WEBJS: 'webjs',\n} as const;\nexport type TelemetrySdkLanguageValues =\n  (typeof TelemetrySdkLanguageValues)[keyof typeof TelemetrySdkLanguageValues];\n"]}