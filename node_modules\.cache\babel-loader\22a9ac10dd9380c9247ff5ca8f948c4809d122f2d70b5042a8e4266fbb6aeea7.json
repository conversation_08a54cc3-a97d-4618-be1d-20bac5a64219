{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ProductReducer - metodo reducers per operazioni sui prodotti nel carrello \n*\n*/\nimport { GET_NUMBERS_PRODUCT } from \"../actions/types\";\nconst reducers = function () {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    product: JSON.parse(localStorage.getItem(\"Prodotti\") || \"[]\")\n  };\n  let action = arguments.length > 1 ? arguments[1] : undefined;\n  //Caso di aggiunta prodotto\n  switch (action.type) {\n    //Lettura numero prodotti\n    case GET_NUMBERS_PRODUCT:\n      return _objectSpread({}, state);\n    default:\n      return state;\n  }\n};\nexport default reducers;", "map": {"version": 3, "names": ["GET_NUMBERS_PRODUCT", "reducers", "state", "arguments", "length", "undefined", "product", "JSON", "parse", "localStorage", "getItem", "action", "type", "_objectSpread"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/carrello/reducers/productReducer.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ProductReducer - metodo reducers per operazioni sui prodotti nel carrello \n*\n*/\nimport { GET_NUMBERS_PRODUCT } from \"../actions/types\";\n\nconst reducers = (state = { product: JSON.parse(localStorage.getItem(\"Prodotti\") || \"[]\") }, action) => {\n\n\n    //Caso di aggiunta prodotto\n    switch (action.type) {\n\n        //Lettura numero prodotti\n        case GET_NUMBERS_PRODUCT:\n\n            return {\n                ...state\n            }\n\n        default:\n            return state;\n    }\n\n}\nexport default reducers;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,kBAAkB;AAEtD,MAAMC,QAAQ,GAAG,SAAAA,CAAA,EAAuF;EAAA,IAAtFC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAAEG,OAAO,EAAEC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;EAAE,CAAC;EAAA,IAAEC,MAAM,GAAAR,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAG/F;EACA,QAAQM,MAAM,CAACC,IAAI;IAEf;IACA,KAAKZ,mBAAmB;MAEpB,OAAAa,aAAA,KACOX,KAAK;IAGhB;MACI,OAAOA,KAAK;EACpB;AAEJ,CAAC;AACD,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}