{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ConfigContext = exports.ConfigConsumer = void 0;\nexports.withConfigConsumer = withConfigConsumer;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _renderEmpty = _interopRequireDefault(require(\"./renderEmpty\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffixCls, customizePrefixCls) {\n  if (customizePrefixCls) return customizePrefixCls;\n  return suffixCls ? \"ant-\".concat(suffixCls) : 'ant';\n};\nvar ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  renderEmpty: _renderEmpty[\"default\"]\n});\nexports.ConfigContext = ConfigContext;\nvar ConfigConsumer = ConfigContext.Consumer;\n/** @deprecated Use hooks instead. This is a legacy function */\n\nexports.ConfigConsumer = ConfigConsumer;\nfunction withConfigConsumer(config) {\n  return function withConfigConsumerFunc(Component) {\n    // Wrap with ConfigConsumer. Since we need compatible with react 15, be care when using ref methods\n    var SFC = function SFC(props) {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (configProps) {\n        var basicPrefixCls = config.prefixCls;\n        var getPrefixCls = configProps.getPrefixCls;\n        var customizePrefixCls = props.prefixCls;\n        var prefixCls = getPrefixCls(basicPrefixCls, customizePrefixCls);\n        return /*#__PURE__*/React.createElement(Component, (0, _extends2[\"default\"])({}, configProps, props, {\n          prefixCls: prefixCls\n        }));\n      });\n    };\n    var cons = Component.constructor;\n    var name = cons && cons.displayName || Component.name || 'Component';\n    SFC.displayName = \"withConfigConsumer(\".concat(name, \")\");\n    return SFC;\n  };\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "ConfigContext", "ConfigConsumer", "withConfigConsumer", "_extends2", "React", "_interopRequireWildcard", "_renderEmpty", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "defaultGetPrefixCls", "suffixCls", "customizePrefixCls", "concat", "createContext", "getPrefixCls", "renderEmpty", "Consumer", "config", "withConfigConsumerFunc", "Component", "SFC", "props", "createElement", "configProps", "basicPrefixCls", "prefixCls", "cons", "constructor", "name", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/config-provider/context.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ConfigContext = exports.ConfigConsumer = void 0;\nexports.withConfigConsumer = withConfigConsumer;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _renderEmpty = _interopRequireDefault(require(\"./renderEmpty\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffixCls, customizePrefixCls) {\n  if (customizePrefixCls) return customizePrefixCls;\n  return suffixCls ? \"ant-\".concat(suffixCls) : 'ant';\n};\n\nvar ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  renderEmpty: _renderEmpty[\"default\"]\n});\nexports.ConfigContext = ConfigContext;\nvar ConfigConsumer = ConfigContext.Consumer;\n/** @deprecated Use hooks instead. This is a legacy function */\n\nexports.ConfigConsumer = ConfigConsumer;\n\nfunction withConfigConsumer(config) {\n  return function withConfigConsumerFunc(Component) {\n    // Wrap with ConfigConsumer. Since we need compatible with react 15, be care when using ref methods\n    var SFC = function SFC(props) {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (configProps) {\n        var basicPrefixCls = config.prefixCls;\n        var getPrefixCls = configProps.getPrefixCls;\n        var customizePrefixCls = props.prefixCls;\n        var prefixCls = getPrefixCls(basicPrefixCls, customizePrefixCls);\n        return /*#__PURE__*/React.createElement(Component, (0, _extends2[\"default\"])({}, configProps, props, {\n          prefixCls: prefixCls\n        }));\n      });\n    };\n\n    var cons = Component.constructor;\n    var name = cons && cons.displayName || Component.name || 'Component';\n    SFC.displayName = \"withConfigConsumer(\".concat(name, \")\");\n    return SFC;\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,aAAa,GAAGF,OAAO,CAACG,cAAc,GAAG,KAAK,CAAC;AACvDH,OAAO,CAACI,kBAAkB,GAAGA,kBAAkB;AAE/C,IAAIC,SAAS,GAAGV,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIU,KAAK,GAAGC,uBAAuB,CAACX,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIY,YAAY,GAAGb,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEnE,SAASa,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASH,uBAAuBA,CAACO,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIjB,OAAO,CAACiB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGtB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACuB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIxB,MAAM,CAACyB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGtB,MAAM,CAACuB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE7B,MAAM,CAACC,cAAc,CAACoB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIS,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,SAAS,EAAEC,kBAAkB,EAAE;EACpF,IAAIA,kBAAkB,EAAE,OAAOA,kBAAkB;EACjD,OAAOD,SAAS,GAAG,MAAM,CAACE,MAAM,CAACF,SAAS,CAAC,GAAG,KAAK;AACrD,CAAC;AAED,IAAI3B,aAAa,GAAG,aAAaI,KAAK,CAAC0B,aAAa,CAAC;EACnD;EACAC,YAAY,EAAEL,mBAAmB;EACjCM,WAAW,EAAE1B,YAAY,CAAC,SAAS;AACrC,CAAC,CAAC;AACFR,OAAO,CAACE,aAAa,GAAGA,aAAa;AACrC,IAAIC,cAAc,GAAGD,aAAa,CAACiC,QAAQ;AAC3C;;AAEAnC,OAAO,CAACG,cAAc,GAAGA,cAAc;AAEvC,SAASC,kBAAkBA,CAACgC,MAAM,EAAE;EAClC,OAAO,SAASC,sBAAsBA,CAACC,SAAS,EAAE;IAChD;IACA,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,KAAK,EAAE;MAC5B,OAAO,aAAalC,KAAK,CAACmC,aAAa,CAACtC,cAAc,EAAE,IAAI,EAAE,UAAUuC,WAAW,EAAE;QACnF,IAAIC,cAAc,GAAGP,MAAM,CAACQ,SAAS;QACrC,IAAIX,YAAY,GAAGS,WAAW,CAACT,YAAY;QAC3C,IAAIH,kBAAkB,GAAGU,KAAK,CAACI,SAAS;QACxC,IAAIA,SAAS,GAAGX,YAAY,CAACU,cAAc,EAAEb,kBAAkB,CAAC;QAChE,OAAO,aAAaxB,KAAK,CAACmC,aAAa,CAACH,SAAS,EAAE,CAAC,CAAC,EAAEjC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEqC,WAAW,EAAEF,KAAK,EAAE;UACnGI,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC;IAED,IAAIC,IAAI,GAAGP,SAAS,CAACQ,WAAW;IAChC,IAAIC,IAAI,GAAGF,IAAI,IAAIA,IAAI,CAACG,WAAW,IAAIV,SAAS,CAACS,IAAI,IAAI,WAAW;IACpER,GAAG,CAACS,WAAW,GAAG,qBAAqB,CAACjB,MAAM,CAACgB,IAAI,EAAE,GAAG,CAAC;IACzD,OAAOR,GAAG;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}