{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\confrontoDispQta.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ConfrontoDisponibilitàQuantità - operazioni di confronto sulle quantità rispetto alle disponibilità\n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { InputNumber } from \"primereact/inputnumber\";\nimport { confirmDialog } from \"primereact/confirmdialog\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Tooltip } from \"primereact/tooltip\";\nimport { InputText } from \"primereact/inputtext\";\nimport { stopLoading } from \"../../components/generalizzazioni/stopLoading\";\nimport { resp_magGestioneLavorazioni } from \"../../components/route\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ConfrontoDispQta extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: '',\n      externalCode: '',\n      brand: '',\n      nationality: '',\n      region: '',\n      format: '',\n      family: '',\n      subfamily: '',\n      group: '',\n      subgroup: '',\n      deposit: '',\n      productsPackagings: []\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      documento: null,\n      titolo: '',\n      globalFilter: null,\n      result: this.emptyResult,\n      loading: true,\n      addProdDialog: false,\n      selectedResults: null,\n      externalSys: false,\n      blockButton: false\n    };\n    this.onRowEditComplete = this.onRowEditComplete.bind(this);\n    this.modifica = this.modifica.bind(this);\n    this.modificaDoc = this.modificaDoc.bind(this);\n    this.save = this.save.bind(this);\n    this.openNew = this.openNew.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.addProd = this.addProd.bind(this);\n    this.cambiaStato = this.cambiaStato.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var doc = [];\n    let document = JSON.parse(localStorage.getItem(\"datiComodo\"));\n    var url = \"tasks/?idTask=\" + document.id;\n    await APIRequest('GET', 'externalsystems/').then(res => {\n      var check = res.data.find(el => el.externalSistemName === \"Alyante\");\n      if (check !== undefined) {\n        this.setState({\n          externalSys: true\n        });\n      }\n    }).catch(e => {\n      console.log(e);\n    });\n    await APIRequest(\"GET\", url).then(res => {\n      res.data[0].idDocument.documentBodies.forEach(element => {\n        var _element$idProductsPa, _element$idProductsPa2, _element$idProductsPa3, _element$idProductsPa4, _element$idProductsPa5, _element$idProductsPa6, _element$idProductsPa7;\n        if (element.idProductsPackaging !== null) {\n          element.eanCode = element.idProductsPackaging.eanCode;\n        }\n        element.externalCode = (_element$idProductsPa = element.idProductsPackaging) === null || _element$idProductsPa === void 0 ? void 0 : _element$idProductsPa.idProduct.externalCode;\n        var x = {\n          id: element.externalCode,\n          docBodyId: element.id,\n          description: (_element$idProductsPa2 = element.idProductsPackaging) === null || _element$idProductsPa2 === void 0 ? void 0 : _element$idProductsPa2.idProduct.description,\n          eanCode: element.eanCode,\n          lotto: element.lotto,\n          scadenza: element.scadenza,\n          qtaOrd: element.colliPreventivo,\n          qtaPrep: element.colliConsuntivo,\n          unitMeasure: element.idProductsPackaging.unitMeasure + ' x ' + element.idProductsPackaging.pcsXPackage,\n          giacenza: ((_element$idProductsPa3 = element.idProductsPackaging) === null || _element$idProductsPa3 === void 0 ? void 0 : _element$idProductsPa3.idProduct.productsAvailabilities.length) > 0 ? ((_element$idProductsPa4 = element.idProductsPackaging) === null || _element$idProductsPa4 === void 0 ? void 0 : (_element$idProductsPa5 = _element$idProductsPa4.idProduct.productsAvailabilities[0]) === null || _element$idProductsPa5 === void 0 ? void 0 : _element$idProductsPa5.availability) - ((_element$idProductsPa6 = element.idProductsPackaging) === null || _element$idProductsPa6 === void 0 ? void 0 : (_element$idProductsPa7 = _element$idProductsPa6.idProduct.productsAvailabilities[0]) === null || _element$idProductsPa7 === void 0 ? void 0 : _element$idProductsPa7.committedCustomer) : 'Non disponibile'\n        };\n        if (res.data[0].idDocument.type === 'INVENTORY') {\n          var _element$idProductsPa8;\n          x.id = (_element$idProductsPa8 = element.idProductsPackaging) === null || _element$idProductsPa8 === void 0 ? void 0 : _element$idProductsPa8.idProduct.externalCode;\n        }\n        doc.push(x);\n      });\n      this.setState({\n        documento: res.data[0],\n        results: doc,\n        loading: false,\n        titolo: 'Documento n.' + document.idDocument.number + ' del ' + new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        }).format(new Date(document.idDocument.documentDate))\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lavorazione. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  componentDidUpdate() {\n    setTimeout(() => {\n      var _this$state, _this$state$documento, _this$state$documento2, _this$state$documento3;\n      if (((_this$state = this.state) === null || _this$state === void 0 ? void 0 : (_this$state$documento = _this$state.documento) === null || _this$state$documento === void 0 ? void 0 : _this$state$documento.status) === 'counted' && ((_this$state$documento2 = this.state.documento) === null || _this$state$documento2 === void 0 ? void 0 : (_this$state$documento3 = _this$state$documento2.idDocument) === null || _this$state$documento3 === void 0 ? void 0 : _this$state$documento3.type) !== 'INVENTORY') {\n        var tableRow = document.getElementsByClassName('p-datatable-tbody');\n        Object.entries(tableRow[0].rows).forEach(element => {\n          element[1].classList.add('tableRow');\n        });\n        var findClass = document.getElementsByClassName('tableRow');\n        Object.entries(findClass).forEach(item => {\n          var find = this.state.documento.idDocument.documentBodies.find((el, index) => {\n            var _el$idProductsPackagi;\n            return ((_el$idProductsPackagi = el.idProductsPackaging) === null || _el$idProductsPackagi === void 0 ? void 0 : _el$idProductsPackagi.idProduct.externalCode) === item[1].innerText.split(\"\\t\")[0] && index === item[1].rowIndex - 1;\n          });\n          if (find !== undefined) {\n            if (find.colliConsuntivo === find.colliPreventivo) {\n              item[1].classList.add('matchData');\n            } else {\n              item[1].classList.add('misMatchData');\n            }\n          }\n        });\n      }\n    }, 50);\n  }\n  async save(e) {\n    this.setState({\n      blockButton: true\n    });\n    var tasks = {\n      task: this.state.documento\n    };\n    await APIRequest('PUT', 'tasks', tasks).then(res => {\n      console.log(res.data);\n      this.setState({\n        blockButton: false\n      });\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: \"Il documento è modificato\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = resp_magGestioneLavorazioni;\n      }, 3000);\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.setState({\n        blockButton: false\n      });\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = resp_magGestioneLavorazioni;\n      }, 3000);\n    });\n  }\n  async creaDoc(e) {\n    this.setState({\n      blockButton: true\n    });\n    var tasks = {\n      task: this.state.documento\n    };\n    if (this.state.documento.idDocument.type === 'INVENTORY') {\n      tasks.task.status = 'inventoried';\n      await APIRequest('PUT', 'tasks', tasks).then(res => {\n        console.log(res.data);\n        this.setState({\n          blockButton: false\n        });\n        this.toast.show({\n          severity: 'success',\n          summary: 'Ottimo !',\n          detail: \"Il documento è passato in stato inventoriato\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = resp_magGestioneLavorazioni;\n        }, 3000);\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.setState({\n          blockButton: false\n        });\n        this.toast.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = resp_magGestioneLavorazioni;\n        }, 3000);\n      });\n    } else if (this.state.documento.idDocument.type === 'PERDITA') {\n      tasks.task.status = 'prepared';\n      await APIRequest('PUT', 'tasks', tasks).then(res => {\n        console.log(res.data);\n        this.setState({\n          blockButton: false\n        });\n        this.toast.show({\n          severity: 'success',\n          summary: 'Ottimo !',\n          detail: \"Il documento è passato in stato preparato\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = resp_magGestioneLavorazioni;\n        }, 3000);\n      }).catch(e => {\n        var _e$response7, _e$response8;\n        console.log(e);\n        this.setState({\n          blockButton: false\n        });\n        this.toast.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = resp_magGestioneLavorazioni;\n        }, 3000);\n      });\n    }\n  }\n  modifica(e, key, options) {\n    let result = options.value[options.rowIndex];\n    result.qtaPrep = e.value;\n    this.setState({\n      result,\n      index: options.rowIndex\n    });\n  }\n  chiediConferma() {\n    this.setState({\n      blockButton: true\n    });\n    confirmDialog({\n      message: \"L'azione modificherà lo stato della lavorazione in preparato e sarà irreversibile continuare?\",\n      header: 'Attenzione',\n      icon: 'pi pi-exclamation-triangle',\n      acceptLabel: \"Si\",\n      rejectLabel: \"No\",\n      accept: () => this.cambiaStato(),\n      reject: () => this.setState({\n        blockButton: false\n      }),\n      onHide: () => this.setState({\n        blockButton: false\n      })\n    });\n  }\n  async cambiaStato() {\n    this.setState({\n      blockButton: true\n    });\n    var tasks = {\n      task: this.state.documento\n    };\n    tasks.task.status = 'prepared';\n    await APIRequest('PUT', 'tasks', tasks).then(res => {\n      console.log(res.data);\n      this.setState({\n        blockButton: false\n      });\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: \"Il documento è passato in stato preparato\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = resp_magGestioneLavorazioni;\n      }, 3000);\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.setState({\n        blockButton: false\n      });\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = resp_magGestioneLavorazioni;\n      }, 3000);\n    });\n  }\n  /* Modifico quantità del prodotto */\n  onRowEditComplete(e) {\n    let result = this.state.result;\n    /*#__PURE__*/_jsxDEV(\"span\", {}, result, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this);\n  }\n  /* Dropdown con selettore di formato per la modifica del moltiplicatore e del formato */\n  qtaConsuntivaEditor(options) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.value[options.rowIndex].qtaPrep,\n      onValueChange: (e, key) => this.modifica(e, key = 'qtaPrep', options) /* options.editorCallback(e.target.value) */\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 16\n    }, this);\n  }\n  async modificaDoc() {\n    let result = this.state.result;\n    var find = this.state.documento.idDocument.documentBodies.find(el => el.id === result.docBodyId);\n    find.colliConsuntivo = result.qtaPrep;\n  }\n  async openNew() {\n    var _this$state$documento4;\n    this.setState({\n      addProdDialog: true\n    });\n    var url = '';\n    var prodotti = [];\n    /* Reperisco i prodotti all'interno del listino retailer */\n    url = 'pricelistretailer/?idRetailer=' + ((_this$state$documento4 = this.state.documento.idDocument.idRetailer) === null || _this$state$documento4 === void 0 ? void 0 : _this$state$documento4.id);\n    await APIRequest('GET', url).then(async res => {\n      if (res.data !== '') {\n        prodotti = res.data.idPriceList2.priceListProducts;\n      } else {\n        var _this$state$documento5;\n        /* Reperisco i prodotti all'interno del listino affiliato nel caso non sia presente quello del retailer */\n        url = 'pricelistaffiliate/?idAffiliate=' + ((_this$state$documento5 = this.state.documento.idDocument.idRetailer) === null || _this$state$documento5 === void 0 ? void 0 : _this$state$documento5.idAffiliate);\n        await APIRequest('GET', url).then(res => {\n          prodotti = res.data.idPriceList2.priceListProducts;\n        }).catch(e => {\n          console.log(e);\n        });\n      }\n      var prod = [];\n      prodotti.forEach(element => {\n        element.idProduct2.price = element.price;\n        prod.push(element.idProduct2);\n      });\n      this.setState({\n        results2: prod\n      });\n      stopLoading();\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  hideDialog() {\n    this.setState({\n      addProdDialog: false\n    });\n  }\n  tooltipMex() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".pi-info-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"p-text-center p-text-bold\",\n        children: [Costanti.ProdAggInDoc, \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-info-circle\",\n          \"data-pr-tooltip\": \"Dalla tabella sottostante, seleziona i prodotti che vuoi aggiungere al documento.\",\n          \"data-pr-position\": \"top\",\n          \"data-pr-at\": \"top\",\n          \"data-pr-my\": \"bottom-5\",\n          style: {\n            fontSize: '1.4rem',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 83\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 13\n    }, this);\n  }\n  addProd() {\n    if (this.state.selectedResults !== null && this.state.selectedResults.length > 0) {\n      var filter = this.state.selectedResults.filter(el => el.productsPackagings.length > 1);\n      if (filter.length === 0) {\n        this.state.selectedResults.forEach(element => {\n          var x = {\n            colliConsuntivo: 1,\n            colliPreventivo: 0,\n            eanCode: element.productsPackagings[0].eanCode,\n            externalCode: element.externalCode,\n            idProductsPackaging: element.productsPackagings[0],\n            total: element.price * element.productsPackagings[0].pcsXPackage,\n            totalTaxed: element.price * element.productsPackagings[0].pcsXPackage + element.price * element.productsPackagings[0].pcsXPackage * element.iva / 100\n          };\n          this.state.documento.idDocument.documentBodies.push(x);\n        });\n        this.setState({\n          blockButton: true\n        });\n        var tasks = {\n          task: this.state.documento\n        };\n        APIRequest('PUT', 'tasks', tasks).then(res => {\n          console.log(res.data);\n          this.setState({\n            blockButton: false\n          });\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo !',\n            detail: \"Il documento è modificato\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response1, _e$response10;\n          console.log(e);\n          this.setState({\n            blockButton: false\n          });\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        });\n      } else {\n        this.toast.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"E' necessario selezionare i formati dei prodotti per procedere all'aggiunta\",\n          life: 3000\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario selezionare dei prodotti e inserirne i formati per procedere all'aggiunta\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    var _this$state$documento6, _this$state$documento7, _this$state$documento8, _this$state$documento9, _this$state$documento0, _this$state$documento1, _this$state$documento10, _this$state$documento11, _this$state$documento12, _this$state$documento13, _this$state$documento14, _this$state$documento15, _this$state$documento16, _this$state$documento17, _this$state$documento18, _this$state$documento19;\n    const header = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-rows\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-input-icon-left d-block mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputText, {\n              className: \"w-100\",\n              type: \"search\",\n              onInput: e => this.setState({\n                globalFilter: e.target.value\n              }),\n              placeholder: \"Cerca...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 21\n        }, this), ((_this$state$documento6 = this.state.documento) === null || _this$state$documento6 === void 0 ? void 0 : _this$state$documento6.status) !== 'canceled' && ((_this$state$documento7 = this.state.documento) === null || _this$state$documento7 === void 0 ? void 0 : _this$state$documento7.status) !== 'prepared' && ((_this$state$documento8 = this.state.documento) === null || _this$state$documento8 === void 0 ? void 0 : _this$state$documento8.status) !== 'inventoried' && ((_this$state$documento9 = this.state.documento) === null || _this$state$documento9 === void 0 ? void 0 : _this$state$documento9.idDocument.type) !== 'PERDITA' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end w-100\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button mx-0 justify-content-center\",\n              onClick: () => this.openNew(),\n              children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-plus-circle mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 123\n              }, this), \" \", Costanti.AggProd, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 13\n    }, this);\n    /* Footer dialogo di aggiunta prodotti */\n    const productDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        label: Costanti.Chiudi,\n        className: \"p-button-text\",\n        onClick: this.hideDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: 'externalCode',\n      header: Costanti.exCode,\n      body: 'externalCode',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'description',\n      header: Costanti.Prodotto,\n      body: 'description',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'productsAvailabilities.physicalStock',\n      header: Costanti.GiacenzaFisica,\n      body: 'physicalStock',\n      showHeader: true\n    }, {\n      field: 'productsAvailabilities.committedCustomer',\n      header: Costanti.ImpegnataCliente,\n      body: 'committedCustomer',\n      showHeader: true\n    }, {\n      field: 'productsAvailabilities.availability',\n      header: Costanti.QtaDisp,\n      body: 'productsAvailabilities',\n      showHeader: true\n    }, {\n      field: 'package',\n      header: Costanti.Formato,\n      body: 'selectFormato',\n      showHeader: true\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: [((_this$state$documento0 = this.state.documento) === null || _this$state$documento0 === void 0 ? void 0 : _this$state$documento0.idDocument.type) !== 'INVENTORY' && ((_this$state$documento1 = this.state.documento) === null || _this$state$documento1 === void 0 ? void 0 : _this$state$documento1.idDocument.type) !== 'PERDITA' && /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"m-0\",\n          children: [Costanti.MercUsc, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-center d-block subtitle\",\n            children: this.state.titolo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 25\n        }, this), ((_this$state$documento10 = this.state.documento) === null || _this$state$documento10 === void 0 ? void 0 : _this$state$documento10.idDocument.type) === 'INVENTORY' && /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"m-0\",\n          children: [Costanti.DocInventario, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-center d-block subtitle\",\n            children: this.state.titolo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 25\n        }, this), ((_this$state$documento11 = this.state.documento) === null || _this$state$documento11 === void 0 ? void 0 : _this$state$documento11.idDocument.type) === 'PERDITA' && /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"m-0\",\n          children: [Costanti.DocumentiDiRottura, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-center d-block subtitle\",\n            children: this.state.titolo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"datatable-responsive-demo wrapper\",\n        children: /*#__PURE__*/_jsxDEV(DataTable, {\n          ref: el => this.dt = el,\n          className: \"p-datatable-responsive-demo\",\n          dataKey: \"id\",\n          autoLayout: \"true\",\n          value: this.state.results,\n          editMode: \"row\",\n          onRowEditComplete: this.onRowEditComplete,\n          onRowEditSave: this.modificaDoc,\n          header: header,\n          globalFilter: this.state.globalFilter,\n          csvSeparator: \";\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            field: \"id\",\n            header: Costanti.CodProd,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"description\",\n            header: Costanti.Nome,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"unitMeasure\",\n            header: Costanti.Formato,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"eanCode\",\n            header: Costanti.eanCode,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 25\n          }, this), this.state.externalSys && /*#__PURE__*/_jsxDEV(Column, {\n            field: \"giacenza\",\n            header: Costanti.QtaDisp,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 32\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"qtaOrd\",\n            header: Costanti.qtaPreventiva,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"qtaPrep\",\n            header: Costanti.qtaConsuntiva,\n            editor: options => this.qtaConsuntivaEditor(options),\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            rowEditor: true,\n            headerStyle: {\n              width: '10%',\n              minWidth: '8rem'\n            },\n            bodyStyle: {\n              textAlign: 'center'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 17\n      }, this), ((_this$state$documento12 = this.state.documento) === null || _this$state$documento12 === void 0 ? void 0 : _this$state$documento12.idDocument.type) !== 'CLI-ORDINE' && ((_this$state$documento13 = this.state.documento) === null || _this$state$documento13 === void 0 ? void 0 : _this$state$documento13.status) !== 'canceled' && ((_this$state$documento14 = this.state.documento) === null || _this$state$documento14 === void 0 ? void 0 : _this$state$documento14.status) !== 'inventoried' && ((_this$state$documento15 = this.state.documento) === null || _this$state$documento15 === void 0 ? void 0 : _this$state$documento15.status) !== 'prepared' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          label: Costanti.ApprovaIcon,\n          onClick: e => this.creaDoc(e),\n          disabled: this.state.blockButton\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          className: \"mx-3\",\n          label: Costanti.RespingiIcon,\n          onClick: this.respingiJobs\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 21\n      }, this), ((_this$state$documento16 = this.state.documento) === null || _this$state$documento16 === void 0 ? void 0 : _this$state$documento16.status) === 'create' && ((_this$state$documento17 = this.state.documento) === null || _this$state$documento17 === void 0 ? void 0 : _this$state$documento17.idDocument.type) === 'CLI-ORDINE' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center my-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          label: Costanti.salva,\n          onClick: e => this.save(e),\n          disabled: this.state.blockButton\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 21\n      }, this), ((_this$state$documento18 = this.state.documento) === null || _this$state$documento18 === void 0 ? void 0 : _this$state$documento18.status) === 'counted' && ((_this$state$documento19 = this.state.documento) === null || _this$state$documento19 === void 0 ? void 0 : _this$state$documento19.idDocument.type) === 'CLI-ORDINE' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center my-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          label: Costanti.PassaAPreparato,\n          onClick: e => this.chiediConferma(e),\n          disabled: this.state.blockButton\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.addProdDialog,\n        header: this.tooltipMex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: productDialogFooter,\n        onHide: this.hideDialog,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"datatable-responsive-demo wrapper my-4\",\n            children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n              value: this.state.results2,\n              fields: fields,\n              dataKey: \"id\",\n              paginator: true,\n              rows: 5,\n              rowsPerPageOptions: [5, 10, 20, 50],\n              selection: this.state.selectedResults,\n              onSelectionChange: e => this.setState({\n                selectedResults: e.value\n              }),\n              responsiveLayout: \"scroll\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center my-3\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: \"w-auto\",\n              onClick: this.addProd,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-plus-circle mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 80\n              }, this), Costanti.Aggiungi]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default ConfrontoDispQta;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "Toast", "<PERSON><PERSON>", "APIRequest", "DataTable", "Column", "InputNumber", "confirmDialog", "Dialog", "<PERSON><PERSON><PERSON>", "InputText", "stopLoading", "resp_magGestioneLavorazioni", "CustomDataTable", "Caricamento", "Nav", "jsxDEV", "_jsxDEV", "ConfrontoDispQta", "constructor", "props", "emptyResult", "id", "externalCode", "brand", "nationality", "region", "format", "family", "subfamily", "group", "subgroup", "deposit", "productsPackagings", "state", "results", "results2", "documento", "titolo", "globalFilter", "result", "loading", "addProdDialog", "selectedResults", "externalSys", "blockButton", "onRowEditComplete", "bind", "modifica", "modificaDoc", "save", "openNew", "hideDialog", "addProd", "cambiaStato", "componentDidMount", "doc", "document", "JSON", "parse", "localStorage", "getItem", "url", "then", "res", "check", "data", "find", "el", "externalSistemName", "undefined", "setState", "catch", "e", "console", "log", "idDocument", "documentBodies", "for<PERSON>ach", "element", "_element$idProductsPa", "_element$idProductsPa2", "_element$idProductsPa3", "_element$idProductsPa4", "_element$idProductsPa5", "_element$idProductsPa6", "_element$idProductsPa7", "idProductsPackaging", "eanCode", "idProduct", "x", "docBodyId", "description", "lotto", "scadenza", "qtaOrd", "colliPreventivo", "qtaPrep", "colliConsuntivo", "unitMeasure", "pcsXPackage", "giac<PERSON>za", "productsAvailabilities", "length", "availability", "committedCustomer", "type", "_element$idProductsPa8", "push", "number", "Intl", "DateTimeFormat", "day", "month", "year", "Date", "documentDate", "_e$response", "_e$response2", "toast", "show", "severity", "summary", "detail", "concat", "response", "message", "life", "componentDidUpdate", "setTimeout", "_this$state", "_this$state$documento", "_this$state$documento2", "_this$state$documento3", "status", "tableRow", "getElementsByClassName", "Object", "entries", "rows", "classList", "add", "findClass", "item", "index", "_el$idProductsPackagi", "innerText", "split", "rowIndex", "tasks", "task", "window", "location", "pathname", "_e$response3", "_e$response4", "creaDoc", "_e$response5", "_e$response6", "_e$response7", "_e$response8", "key", "options", "value", "chiediConferma", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "reject", "onHide", "_e$response9", "_e$response0", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "qtaConsuntivaEditor", "onValueChange", "_this$state$documento4", "prodotti", "idRetailer", "idPriceList2", "priceListProducts", "_this$state$documento5", "idAffiliate", "prod", "idProduct2", "price", "tooltipMex", "children", "target", "className", "ProdAggInDoc", "style", "fontSize", "cursor", "filter", "total", "totalTaxed", "iva", "reload", "_e$response1", "_e$response10", "render", "_this$state$documento6", "_this$state$documento7", "_this$state$documento8", "_this$state$documento9", "_this$state$documento0", "_this$state$documento1", "_this$state$documento10", "_this$state$documento11", "_this$state$documento12", "_this$state$documento13", "_this$state$documento14", "_this$state$documento15", "_this$state$documento16", "_this$state$documento17", "_this$state$documento18", "_this$state$documento19", "onInput", "placeholder", "onClick", "<PERSON>gg<PERSON><PERSON>", "productDialogFooter", "Fragment", "label", "<PERSON><PERSON>", "fields", "selectionMode", "headerStyle", "width", "field", "exCode", "body", "sortable", "showHeader", "<PERSON><PERSON><PERSON>", "GiacenzaFisica", "ImpegnataCliente", "QtaDisp", "Formato", "ref", "MercUsc", "DocInventario", "DocumentiDiRottura", "dt", "dataKey", "autoLayout", "editMode", "onRowEditSave", "csvSeparator", "CodProd", "Nome", "qtaPreventiva", "qtaConsuntiva", "editor", "rowEditor", "min<PERSON><PERSON><PERSON>", "bodyStyle", "textAlign", "ApprovaIcon", "disabled", "RespingiIcon", "respingiJobs", "salva", "PassaAPreparato", "visible", "modal", "footer", "paginator", "rowsPerPageOptions", "selection", "onSelectionChange", "responsiveLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/confrontoDispQta.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ConfrontoDisponibilitàQuantità - operazioni di confronto sulle quantità rispetto alle disponibilità\n*\n*/\nimport React, { Component } from \"react\";\nimport { But<PERSON> } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { InputNumber } from \"primereact/inputnumber\";\nimport { confirmDialog } from \"primereact/confirmdialog\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Tooltip } from \"primereact/tooltip\";\nimport { InputText } from \"primereact/inputtext\";\nimport { stopLoading } from \"../../components/generalizzazioni/stopLoading\";\nimport { resp_magGestioneLavorazioni } from \"../../components/route\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\nimport Nav from \"../../components/navigation/Nav\";\n\nclass ConfrontoDispQta extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: '',\n        externalCode: '',\n        brand: '',\n        nationality: '',\n        region: '',\n        format: '',\n        family: '',\n        subfamily: '',\n        group: '',\n        subgroup: '',\n        deposit: '',\n        productsPackagings: []\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            documento: null,\n            titolo: '',\n            globalFilter: null,\n            result: this.emptyResult,\n            loading: true,\n            addProdDialog: false,\n            selectedResults: null,\n            externalSys: false,\n            blockButton: false\n        }\n        this.onRowEditComplete = this.onRowEditComplete.bind(this);\n        this.modifica = this.modifica.bind(this);\n        this.modificaDoc = this.modificaDoc.bind(this);\n        this.save = this.save.bind(this);\n        this.openNew = this.openNew.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.addProd = this.addProd.bind(this);\n        this.cambiaStato = this.cambiaStato.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var doc = []\n        let document = JSON.parse(localStorage.getItem(\"datiComodo\"));\n        var url = \"tasks/?idTask=\" + document.id\n        await APIRequest('GET', 'externalsystems/')\n            .then(res => {\n                var check = res.data.find(el => el.externalSistemName === \"Alyante\")\n                if (check !== undefined) {\n                    this.setState({\n                        externalSys: true\n                    })\n                }\n            }).catch((e) => {\n                console.log(e);\n            })\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                res.data[0].idDocument.documentBodies.forEach(element => {\n                    if (element.idProductsPackaging !== null) {\n                        element.eanCode = element.idProductsPackaging.eanCode;\n                    }\n                    element.externalCode = element.idProductsPackaging?.idProduct.externalCode;\n                    var x = {\n                        id: element.externalCode,\n                        docBodyId: element.id,\n                        description: element.idProductsPackaging?.idProduct.description,\n                        eanCode: element.eanCode,\n                        lotto: element.lotto,\n                        scadenza: element.scadenza,\n                        qtaOrd: element.colliPreventivo,\n                        qtaPrep: element.colliConsuntivo,\n                        unitMeasure: element.idProductsPackaging.unitMeasure + ' x ' + element.idProductsPackaging.pcsXPackage,\n                        giacenza: element.idProductsPackaging?.idProduct.productsAvailabilities.length > 0 ? element.idProductsPackaging?.idProduct.productsAvailabilities[0]?.availability - element.idProductsPackaging?.idProduct.productsAvailabilities[0]?.committedCustomer : 'Non disponibile'\n                    }\n                    if (res.data[0].idDocument.type === 'INVENTORY') {\n                        x.id = element.idProductsPackaging?.idProduct.externalCode\n                    }\n                    doc.push(x)\n                })\n                this.setState({\n                    documento: res.data[0],\n                    results: doc,\n                    loading: false,\n                    titolo: 'Documento n.' + document.idDocument.number + ' del ' + new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(document.idDocument.documentDate))\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lavorazione. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    componentDidUpdate() {\n        setTimeout(() => {\n            if (this.state?.documento?.status === 'counted' && this.state.documento?.idDocument?.type !== 'INVENTORY') {\n                var tableRow = document.getElementsByClassName('p-datatable-tbody')\n                Object.entries(tableRow[0].rows).forEach(element => {\n                    element[1].classList.add('tableRow');\n                })\n                var findClass = document.getElementsByClassName('tableRow')\n                Object.entries(findClass).forEach(item => {\n                    var find = this.state.documento.idDocument.documentBodies.find((el, index) => el.idProductsPackaging?.idProduct.externalCode === item[1].innerText.split(\"\\t\")[0] && index === item[1].rowIndex - 1)\n                    if (find !== undefined) {\n                        if (find.colliConsuntivo === find.colliPreventivo) {\n                            item[1].classList.add('matchData')\n                        } else {\n                            item[1].classList.add('misMatchData')\n                        }\n                    }\n                })\n            }\n        }, 50)\n    }\n    async save(e) {\n        this.setState({ blockButton: true })\n        var tasks = {\n            task: this.state.documento\n        }\n        await APIRequest('PUT', 'tasks', tasks)\n            .then(res => {\n                console.log(res.data);\n                this.setState({ blockButton: false })\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è modificato\", life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = resp_magGestioneLavorazioni;\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.setState({ blockButton: false })\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = resp_magGestioneLavorazioni;\n                }, 3000)\n            })\n    }\n    async creaDoc(e) {\n        this.setState({ blockButton: true })\n        var tasks = {\n            task: this.state.documento\n        }\n        if (this.state.documento.idDocument.type === 'INVENTORY') {\n            tasks.task.status = 'inventoried'\n            await APIRequest('PUT', 'tasks', tasks)\n                .then(res => {\n                    console.log(res.data);\n                    this.setState({ blockButton: false })\n                    this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è passato in stato inventoriato\", life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = resp_magGestioneLavorazioni;\n                    }, 3000)\n                }).catch((e) => {\n                    console.log(e)\n                    this.setState({ blockButton: false })\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = resp_magGestioneLavorazioni;\n                    }, 3000)\n                })\n        }else if(this.state.documento.idDocument.type === 'PERDITA'){\n            tasks.task.status = 'prepared'\n            await APIRequest('PUT', 'tasks', tasks)\n                .then(res => {\n                    console.log(res.data);\n                    this.setState({ blockButton: false })\n                    this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è passato in stato preparato\", life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = resp_magGestioneLavorazioni;\n                    }, 3000)\n                }).catch((e) => {\n                    console.log(e)\n                    this.setState({ blockButton: false })\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = resp_magGestioneLavorazioni;\n                    }, 3000)\n                })\n        }\n    }\n    modifica(e, key, options) {\n        let result = options.value[options.rowIndex];\n        result.qtaPrep = e.value\n        this.setState({\n            result,\n            index: options.rowIndex\n        })\n    }\n    chiediConferma() {\n        this.setState({ blockButton: true })\n        confirmDialog({\n            message: \"L'azione modificherà lo stato della lavorazione in preparato e sarà irreversibile continuare?\",\n            header: 'Attenzione',\n            icon: 'pi pi-exclamation-triangle',\n            acceptLabel: \"Si\",\n            rejectLabel: \"No\",\n            accept: () => this.cambiaStato(),\n            reject: () => this.setState({ blockButton: false }),\n            onHide: () => this.setState({ blockButton: false })\n        });\n    }\n    async cambiaStato() {\n        this.setState({ blockButton: true })\n        var tasks = {\n            task: this.state.documento\n        }\n        tasks.task.status = 'prepared'\n        await APIRequest('PUT', 'tasks', tasks)\n            .then(res => {\n                console.log(res.data);\n                this.setState({ blockButton: false })\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è passato in stato preparato\", life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = resp_magGestioneLavorazioni;\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.setState({ blockButton: false })\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = resp_magGestioneLavorazioni;\n                }, 3000)\n            })\n    }\n    /* Modifico quantità del prodotto */\n    onRowEditComplete(e) {\n        let result = this.state.result;\n        <span key={result}></span>\n    }\n    /* Dropdown con selettore di formato per la modifica del moltiplicatore e del formato */\n    qtaConsuntivaEditor(options) {\n        return <InputNumber value={options.value[options.rowIndex].qtaPrep} onValueChange={(e, key) => this.modifica(e, key = 'qtaPrep', options)/* options.editorCallback(e.target.value) */} />;\n    }\n    async modificaDoc() {\n        let result = this.state.result;\n        var find = this.state.documento.idDocument.documentBodies.find(el => el.id === result.docBodyId)\n        find.colliConsuntivo = result.qtaPrep\n    }\n    async openNew() {\n        this.setState({\n            addProdDialog: true\n        })\n        var url = ''\n        var prodotti = []\n        /* Reperisco i prodotti all'interno del listino retailer */\n        url = 'pricelistretailer/?idRetailer=' + this.state.documento.idDocument.idRetailer?.id\n        await APIRequest('GET', url)\n            .then(async res => {\n                if (res.data !== '') {\n                    prodotti = res.data.idPriceList2.priceListProducts;\n                } else {\n                    /* Reperisco i prodotti all'interno del listino affiliato nel caso non sia presente quello del retailer */\n                    url = 'pricelistaffiliate/?idAffiliate=' + this.state.documento.idDocument.idRetailer?.idAffiliate\n                    await APIRequest('GET', url)\n                        .then(res => {\n                            prodotti = res.data.idPriceList2.priceListProducts;\n                        }).catch((e) => {\n                            console.log(e)\n                        })\n\n                }\n                var prod = []\n                prodotti.forEach(element => {\n                    element.idProduct2.price = element.price\n                    prod.push(element.idProduct2)\n                })\n                this.setState({\n                    results2: prod\n                })\n                stopLoading()\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    hideDialog() {\n        this.setState({\n            addProdDialog: false\n        })\n    }\n    tooltipMex() {\n        return (\n            <div>\n                <Tooltip target=\".pi-info-circle\" />\n                <h3 className=\"p-text-center p-text-bold\">{Costanti.ProdAggInDoc} <i className=\"pi pi-info-circle\" data-pr-tooltip=\"Dalla tabella sottostante, seleziona i prodotti che vuoi aggiungere al documento.\" data-pr-position=\"top\" data-pr-at=\"top\" data-pr-my=\"bottom-5\" style={{ fontSize: '1.4rem', cursor: 'pointer' }}></i></h3>\n            </div>\n        )\n    }\n    addProd() {\n        if (this.state.selectedResults !== null && this.state.selectedResults.length > 0) {\n            var filter = this.state.selectedResults.filter(el => el.productsPackagings.length > 1)\n            if (filter.length === 0) {\n                this.state.selectedResults.forEach(element => {\n                    var x = {\n                        colliConsuntivo: 1,\n                        colliPreventivo: 0,\n                        eanCode: element.productsPackagings[0].eanCode,\n                        externalCode: element.externalCode,\n                        idProductsPackaging: element.productsPackagings[0],\n                        total: element.price * element.productsPackagings[0].pcsXPackage,\n                        totalTaxed: element.price * element.productsPackagings[0].pcsXPackage + element.price * element.productsPackagings[0].pcsXPackage * element.iva / 100\n                    }\n                    this.state.documento.idDocument.documentBodies.push(x)\n                })\n                this.setState({ blockButton: true })\n                var tasks = {\n                    task: this.state.documento\n                }\n                APIRequest('PUT', 'tasks', tasks)\n                    .then(res => {\n                        console.log(res.data);\n                        this.setState({ blockButton: false })\n                        this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è modificato\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.setState({ blockButton: false })\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    })\n            } else {\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario selezionare i formati dei prodotti per procedere all'aggiunta\", life: 3000 });\n            }\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario selezionare dei prodotti e inserirne i formati per procedere all'aggiunta\", life: 3000 });\n        }\n    }\n    render() {\n        const header = (\n            <div className=\"container-rows\">\n                <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                    <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                        <span className=\"p-input-icon-left d-block mx-auto\">\n                            <i className=\"pi pi-search mr-2\" />\n                            <InputText className=\"w-100\" type=\"search\" onInput={(e) => this.setState({ globalFilter: e.target.value })} placeholder=\"Cerca...\" />\n                        </span>\n                    </div>\n                    {this.state.documento?.status !== 'canceled' && this.state.documento?.status !== 'prepared' && this.state.documento?.status !== 'inventoried' && this.state.documento?.idDocument.type !== 'PERDITA' &&\n                        <div className='col-12 col-md-6 mb-3 mb-sm-0'>\n                            <div className=\"d-flex justify-content-end w-100\">\n                                <Button className=\"p-button mx-0 justify-content-center\" onClick={() => this.openNew()} > <i className=\"pi pi-plus-circle mr-2\"></i> {Costanti.AggProd} </Button>\n                            </div>\n                        </div>\n                    }\n\n                </div>\n            </div>\n        );\n        /* Footer dialogo di aggiunta prodotti */\n        const productDialogFooter = (\n            <React.Fragment>\n                <Button label={Costanti.Chiudi} className=\"p-button-text\" onClick={this.hideDialog} />\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            { field: 'externalCode', header: Costanti.exCode, body: 'externalCode', sortable: true, showHeader: true },\n            { field: 'description', header: Costanti.Prodotto, body: 'description', sortable: true, showHeader: true },\n            { field: 'productsAvailabilities.physicalStock', header: Costanti.GiacenzaFisica, body: 'physicalStock', showHeader: true },\n            { field: 'productsAvailabilities.committedCustomer', header: Costanti.ImpegnataCliente, body: 'committedCustomer', showHeader: true },\n            { field: 'productsAvailabilities.availability', header: Costanti.QtaDisp, body: 'productsAvailabilities', showHeader: true },\n            { field: 'package', header: Costanti.Formato, body: 'selectFormato', showHeader: true }\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                <Toast ref={(el) => this.toast = el} />\n                <div>\n                    <Nav />\n                </div>\n                <div className=\"col-12 px-0 solid-head\">\n                    {this.state.documento?.idDocument.type !== 'INVENTORY' && this.state.documento?.idDocument.type !== 'PERDITA' &&\n                        <h1 className=\"m-0\">{Costanti.MercUsc}\n                            <span className=\"text-center d-block subtitle\">{this.state.titolo}</span>\n                        </h1>\n                    }\n                    {this.state.documento?.idDocument.type === 'INVENTORY' &&\n                        <h1 className=\"m-0\">{Costanti.DocInventario}\n                            <span className=\"text-center d-block subtitle\">{this.state.titolo}</span>\n                        </h1>\n                    }\n                    {this.state.documento?.idDocument.type === 'PERDITA' &&\n                    <h1 className=\"m-0\">{Costanti.DocumentiDiRottura}\n                            <span className=\"text-center d-block subtitle\">{this.state.titolo}</span>\n                        </h1>\n                    }\n                </div>\n                <div className=\"datatable-responsive-demo wrapper\">\n                    <DataTable ref={(el) => this.dt = el}\n                        className=\"p-datatable-responsive-demo\"\n                        dataKey=\"id\" autoLayout=\"true\"\n                        value={this.state.results}\n                        editMode=\"row\"\n                        onRowEditComplete={this.onRowEditComplete}\n                        onRowEditSave={this.modificaDoc}\n                        header={header}\n                        globalFilter={this.state.globalFilter}\n                        csvSeparator=\";\"\n                    >\n                        <Column field=\"id\" header={Costanti.CodProd} sortable ></Column>\n                        <Column field=\"description\" header={Costanti.Nome} sortable ></Column>\n                        <Column field=\"unitMeasure\" header={Costanti.Formato} sortable ></Column>\n                        <Column field=\"eanCode\" header={Costanti.eanCode} sortable ></Column>\n                        {this.state.externalSys\n                            && <Column field=\"giacenza\" header={Costanti.QtaDisp} sortable ></Column>\n                        }\n                        <Column field=\"qtaOrd\" header={Costanti.qtaPreventiva} sortable ></Column>\n                        <Column field=\"qtaPrep\" header={Costanti.qtaConsuntiva} editor={(options) => this.qtaConsuntivaEditor(options)} sortable ></Column>\n                        <Column rowEditor headerStyle={{ width: '10%', minWidth: '8rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                    </DataTable>\n                </div>\n                {this.state.documento?.idDocument.type !== 'CLI-ORDINE' && this.state.documento?.status !== 'canceled' && this.state.documento?.status !== 'inventoried' && this.state.documento?.status !== 'prepared' &&\n                    <div className=\"d-flex justify-content-end mt-3\">\n                        <Button label={Costanti.ApprovaIcon} onClick={(e) => this.creaDoc(e)} disabled={this.state.blockButton} />\n                        <Button className=\"mx-3\" label={Costanti.RespingiIcon} onClick={this.respingiJobs} />\n                    </div>\n                }\n                {\n                    this.state.documento?.status === 'create' && this.state.documento?.idDocument.type === 'CLI-ORDINE' &&\n                    <div className=\"d-flex justify-content-center my-3\">\n                        <Button label={Costanti.salva} onClick={(e) => this.save(e)} disabled={this.state.blockButton} />\n                        {/* <Button className=\"mx-3\" label={Costanti.RespingiIcon} onClick={this.respingiJobs} /> */}\n                    </div>\n                }\n                {\n                    this.state.documento?.status === 'counted' && this.state.documento?.idDocument.type === 'CLI-ORDINE' &&\n                    <div className=\"d-flex justify-content-center my-3\">\n                        <Button label={Costanti.PassaAPreparato} onClick={(e) => this.chiediConferma(e)} disabled={this.state.blockButton} />\n                    </div>\n                }\n                <Dialog visible={this.state.addProdDialog} header={this.tooltipMex} modal className=\"p-fluid modalBox\" footer={productDialogFooter} onHide={this.hideDialog}>\n                    <Caricamento />\n                    <div className=\"col-12\">\n                        <div className=\"datatable-responsive-demo wrapper my-4\">\n                            <CustomDataTable\n                                value={this.state.results2}\n                                fields={fields}\n                                dataKey=\"id\"\n                                paginator\n                                rows={5}\n                                rowsPerPageOptions={[5, 10, 20, 50]}\n                                selection={this.state.selectedResults}\n                                onSelectionChange={(e) => this.setState({ selectedResults: e.value })}\n                                responsiveLayout=\"scroll\"\n                            />\n                        </div>\n                        <div className=\"d-flex justify-content-center my-3\">\n                            <Button className=\"w-auto\" onClick={this.addProd} ><i className=\"pi pi-plus-circle mr-2\"></i>{Costanti.Aggiungi}</Button>\n                        </div>\n                    </div>\n                </Dialog>\n            </div >\n        )\n    }\n}\n\nexport default ConfrontoDispQta;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,GAAG,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,gBAAgB,SAASnB,SAAS,CAAC;EAgBrCoB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAhBhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,EAAE;MACNC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,kBAAkB,EAAE;IACxB,CAAC;IAGG,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,IAAI;MAClBC,MAAM,EAAE,IAAI,CAACnB,WAAW;MACxBoB,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE;IACjB,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,CAACH,IAAI,CAAC,IAAI,CAAC;IAChC,IAAI,CAACI,OAAO,GAAG,IAAI,CAACA,OAAO,CAACJ,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACK,UAAU,GAAG,IAAI,CAACA,UAAU,CAACL,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACM,OAAO,GAAG,IAAI,CAACA,OAAO,CAACN,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACO,WAAW,GAAG,IAAI,CAACA,WAAW,CAACP,IAAI,CAAC,IAAI,CAAC;EAClD;EACA;EACA,MAAMQ,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC7D,IAAIC,GAAG,GAAG,gBAAgB,GAAGL,QAAQ,CAACnC,EAAE;IACxC,MAAMnB,UAAU,CAAC,KAAK,EAAE,kBAAkB,CAAC,CACtC4D,IAAI,CAACC,GAAG,IAAI;MACT,IAAIC,KAAK,GAAGD,GAAG,CAACE,IAAI,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,kBAAkB,KAAK,SAAS,CAAC;MACpE,IAAIJ,KAAK,KAAKK,SAAS,EAAE;QACrB,IAAI,CAACC,QAAQ,CAAC;UACV3B,WAAW,EAAE;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CAAC4B,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,MAAMtE,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBC,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAACU,UAAU,CAACC,cAAc,CAACC,OAAO,CAACC,OAAO,IAAI;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACrD,IAAIP,OAAO,CAACQ,mBAAmB,KAAK,IAAI,EAAE;UACtCR,OAAO,CAACS,OAAO,GAAGT,OAAO,CAACQ,mBAAmB,CAACC,OAAO;QACzD;QACAT,OAAO,CAACxD,YAAY,IAAAyD,qBAAA,GAAGD,OAAO,CAACQ,mBAAmB,cAAAP,qBAAA,uBAA3BA,qBAAA,CAA6BS,SAAS,CAAClE,YAAY;QAC1E,IAAImE,CAAC,GAAG;UACJpE,EAAE,EAAEyD,OAAO,CAACxD,YAAY;UACxBoE,SAAS,EAAEZ,OAAO,CAACzD,EAAE;UACrBsE,WAAW,GAAAX,sBAAA,GAAEF,OAAO,CAACQ,mBAAmB,cAAAN,sBAAA,uBAA3BA,sBAAA,CAA6BQ,SAAS,CAACG,WAAW;UAC/DJ,OAAO,EAAET,OAAO,CAACS,OAAO;UACxBK,KAAK,EAAEd,OAAO,CAACc,KAAK;UACpBC,QAAQ,EAAEf,OAAO,CAACe,QAAQ;UAC1BC,MAAM,EAAEhB,OAAO,CAACiB,eAAe;UAC/BC,OAAO,EAAElB,OAAO,CAACmB,eAAe;UAChCC,WAAW,EAAEpB,OAAO,CAACQ,mBAAmB,CAACY,WAAW,GAAG,KAAK,GAAGpB,OAAO,CAACQ,mBAAmB,CAACa,WAAW;UACtGC,QAAQ,EAAE,EAAAnB,sBAAA,GAAAH,OAAO,CAACQ,mBAAmB,cAAAL,sBAAA,uBAA3BA,sBAAA,CAA6BO,SAAS,CAACa,sBAAsB,CAACC,MAAM,IAAG,CAAC,GAAG,EAAApB,sBAAA,GAAAJ,OAAO,CAACQ,mBAAmB,cAAAJ,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BM,SAAS,CAACa,sBAAsB,CAAC,CAAC,CAAC,cAAAlB,sBAAA,uBAAhEA,sBAAA,CAAkEoB,YAAY,MAAAnB,sBAAA,GAAGN,OAAO,CAACQ,mBAAmB,cAAAF,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BI,SAAS,CAACa,sBAAsB,CAAC,CAAC,CAAC,cAAAhB,sBAAA,uBAAhEA,sBAAA,CAAkEmB,iBAAiB,IAAG;QAChQ,CAAC;QACD,IAAIzC,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAACU,UAAU,CAAC8B,IAAI,KAAK,WAAW,EAAE;UAAA,IAAAC,sBAAA;UAC7CjB,CAAC,CAACpE,EAAE,IAAAqF,sBAAA,GAAG5B,OAAO,CAACQ,mBAAmB,cAAAoB,sBAAA,uBAA3BA,sBAAA,CAA6BlB,SAAS,CAAClE,YAAY;QAC9D;QACAiC,GAAG,CAACoD,IAAI,CAAClB,CAAC,CAAC;MACf,CAAC,CAAC;MACF,IAAI,CAACnB,QAAQ,CAAC;QACVlC,SAAS,EAAE2B,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC;QACtB/B,OAAO,EAAEqB,GAAG;QACZf,OAAO,EAAE,KAAK;QACdH,MAAM,EAAE,cAAc,GAAGmB,QAAQ,CAACmB,UAAU,CAACiC,MAAM,GAAG,OAAO,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC,CAACvF,MAAM,CAAC,IAAIwF,IAAI,CAAC1D,QAAQ,CAACmB,UAAU,CAACwC,YAAY,CAAC;MAC7M,CAAC,CAAC;IACN,CAAC,CAAC,CACD5C,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAA4C,WAAA,EAAAC,YAAA;MACV5C,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,6EAAAC,MAAA,CAA0E,EAAAP,WAAA,GAAA5C,CAAC,CAACoD,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAYnD,IAAI,MAAKI,SAAS,IAAAgD,YAAA,GAAG7C,CAAC,CAACoD,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAYpD,IAAI,GAAGO,CAAC,CAACqD,OAAO,CAAE;QAC/IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAC,kBAAkBA,CAAA,EAAG;IACjBC,UAAU,CAAC,MAAM;MAAA,IAAAC,WAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACb,IAAI,EAAAH,WAAA,OAAI,CAAChG,KAAK,cAAAgG,WAAA,wBAAAC,qBAAA,GAAVD,WAAA,CAAY7F,SAAS,cAAA8F,qBAAA,uBAArBA,qBAAA,CAAuBG,MAAM,MAAK,SAAS,IAAI,EAAAF,sBAAA,OAAI,CAAClG,KAAK,CAACG,SAAS,cAAA+F,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBxD,UAAU,cAAAyD,sBAAA,uBAAhCA,sBAAA,CAAkC3B,IAAI,MAAK,WAAW,EAAE;QACvG,IAAI6B,QAAQ,GAAG9E,QAAQ,CAAC+E,sBAAsB,CAAC,mBAAmB,CAAC;QACnEC,MAAM,CAACC,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC7D,OAAO,CAACC,OAAO,IAAI;UAChDA,OAAO,CAAC,CAAC,CAAC,CAAC6D,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QACxC,CAAC,CAAC;QACF,IAAIC,SAAS,GAAGrF,QAAQ,CAAC+E,sBAAsB,CAAC,UAAU,CAAC;QAC3DC,MAAM,CAACC,OAAO,CAACI,SAAS,CAAC,CAAChE,OAAO,CAACiE,IAAI,IAAI;UACtC,IAAI5E,IAAI,GAAG,IAAI,CAACjC,KAAK,CAACG,SAAS,CAACuC,UAAU,CAACC,cAAc,CAACV,IAAI,CAAC,CAACC,EAAE,EAAE4E,KAAK;YAAA,IAAAC,qBAAA;YAAA,OAAK,EAAAA,qBAAA,GAAA7E,EAAE,CAACmB,mBAAmB,cAAA0D,qBAAA,uBAAtBA,qBAAA,CAAwBxD,SAAS,CAAClE,YAAY,MAAKwH,IAAI,CAAC,CAAC,CAAC,CAACG,SAAS,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIH,KAAK,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACK,QAAQ,GAAG,CAAC;UAAA,EAAC;UACpM,IAAIjF,IAAI,KAAKG,SAAS,EAAE;YACpB,IAAIH,IAAI,CAAC+B,eAAe,KAAK/B,IAAI,CAAC6B,eAAe,EAAE;cAC/C+C,IAAI,CAAC,CAAC,CAAC,CAACH,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;YACtC,CAAC,MAAM;cACHE,IAAI,CAAC,CAAC,CAAC,CAACH,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;YACzC;UACJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAAE,EAAE,CAAC;EACV;EACA,MAAM3F,IAAIA,CAACuB,CAAC,EAAE;IACV,IAAI,CAACF,QAAQ,CAAC;MAAE1B,WAAW,EAAE;IAAK,CAAC,CAAC;IACpC,IAAIwG,KAAK,GAAG;MACRC,IAAI,EAAE,IAAI,CAACpH,KAAK,CAACG;IACrB,CAAC;IACD,MAAMlC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEkJ,KAAK,CAAC,CAClCtF,IAAI,CAACC,GAAG,IAAI;MACTU,OAAO,CAACC,GAAG,CAACX,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACK,QAAQ,CAAC;QAAE1B,WAAW,EAAE;MAAM,CAAC,CAAC;MACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,2BAA2B;QAAEI,IAAI,EAAE;MAAK,CAAC,CAAC;MAC9GE,UAAU,CAAC,MAAM;QACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC4D,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAiF,YAAA,EAAAC,YAAA;MACZjF,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACF,QAAQ,CAAC;QAAE1B,WAAW,EAAE;MAAM,CAAC,CAAC;MACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAA8B,YAAA,GAAAjF,CAAC,CAACoD,QAAQ,cAAA6B,YAAA,uBAAVA,YAAA,CAAYxF,IAAI,MAAKI,SAAS,IAAAqF,YAAA,GAAGlF,CAAC,CAACoD,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAYzF,IAAI,GAAGO,CAAC,CAACqD,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACnOE,UAAU,CAAC,MAAM;QACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC;EACV;EACA,MAAMgJ,OAAOA,CAACnF,CAAC,EAAE;IACb,IAAI,CAACF,QAAQ,CAAC;MAAE1B,WAAW,EAAE;IAAK,CAAC,CAAC;IACpC,IAAIwG,KAAK,GAAG;MACRC,IAAI,EAAE,IAAI,CAACpH,KAAK,CAACG;IACrB,CAAC;IACD,IAAI,IAAI,CAACH,KAAK,CAACG,SAAS,CAACuC,UAAU,CAAC8B,IAAI,KAAK,WAAW,EAAE;MACtD2C,KAAK,CAACC,IAAI,CAAChB,MAAM,GAAG,aAAa;MACjC,MAAMnI,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEkJ,KAAK,CAAC,CAClCtF,IAAI,CAACC,GAAG,IAAI;QACTU,OAAO,CAACC,GAAG,CAACX,GAAG,CAACE,IAAI,CAAC;QACrB,IAAI,CAACK,QAAQ,CAAC;UAAE1B,WAAW,EAAE;QAAM,CAAC,CAAC;QACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAEC,OAAO,EAAE,UAAU;UAAEC,MAAM,EAAE,8CAA8C;UAAEI,IAAI,EAAE;QAAK,CAAC,CAAC;QACjIE,UAAU,CAAC,MAAM;UACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;QAC1D,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAAC4D,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAoF,YAAA,EAAAC,YAAA;QACZpF,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACF,QAAQ,CAAC;UAAE1B,WAAW,EAAE;QAAM,CAAC,CAAC;QACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAAiC,YAAA,GAAApF,CAAC,CAACoD,QAAQ,cAAAgC,YAAA,uBAAVA,YAAA,CAAY3F,IAAI,MAAKI,SAAS,IAAAwF,YAAA,GAAGrF,CAAC,CAACoD,QAAQ,cAAAiC,YAAA,uBAAVA,YAAA,CAAY5F,IAAI,GAAGO,CAAC,CAACqD,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QACnOE,UAAU,CAAC,MAAM;UACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;QAC1D,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV,CAAC,MAAK,IAAG,IAAI,CAACsB,KAAK,CAACG,SAAS,CAACuC,UAAU,CAAC8B,IAAI,KAAK,SAAS,EAAC;MACxD2C,KAAK,CAACC,IAAI,CAAChB,MAAM,GAAG,UAAU;MAC9B,MAAMnI,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEkJ,KAAK,CAAC,CAClCtF,IAAI,CAACC,GAAG,IAAI;QACTU,OAAO,CAACC,GAAG,CAACX,GAAG,CAACE,IAAI,CAAC;QACrB,IAAI,CAACK,QAAQ,CAAC;UAAE1B,WAAW,EAAE;QAAM,CAAC,CAAC;QACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAEC,OAAO,EAAE,UAAU;UAAEC,MAAM,EAAE,2CAA2C;UAAEI,IAAI,EAAE;QAAK,CAAC,CAAC;QAC9HE,UAAU,CAAC,MAAM;UACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;QAC1D,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAAC4D,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAsF,YAAA,EAAAC,YAAA;QACZtF,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACF,QAAQ,CAAC;UAAE1B,WAAW,EAAE;QAAM,CAAC,CAAC;QACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAAmC,YAAA,GAAAtF,CAAC,CAACoD,QAAQ,cAAAkC,YAAA,uBAAVA,YAAA,CAAY7F,IAAI,MAAKI,SAAS,IAAA0F,YAAA,GAAGvF,CAAC,CAACoD,QAAQ,cAAAmC,YAAA,uBAAVA,YAAA,CAAY9F,IAAI,GAAGO,CAAC,CAACqD,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QACnOE,UAAU,CAAC,MAAM;UACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;QAC1D,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV;EACJ;EACAoC,QAAQA,CAACyB,CAAC,EAAEwF,GAAG,EAAEC,OAAO,EAAE;IACtB,IAAI1H,MAAM,GAAG0H,OAAO,CAACC,KAAK,CAACD,OAAO,CAACd,QAAQ,CAAC;IAC5C5G,MAAM,CAACyD,OAAO,GAAGxB,CAAC,CAAC0F,KAAK;IACxB,IAAI,CAAC5F,QAAQ,CAAC;MACV/B,MAAM;MACNwG,KAAK,EAAEkB,OAAO,CAACd;IACnB,CAAC,CAAC;EACN;EACAgB,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC7F,QAAQ,CAAC;MAAE1B,WAAW,EAAE;IAAK,CAAC,CAAC;IACpCtC,aAAa,CAAC;MACVuH,OAAO,EAAE,+FAA+F;MACxGuC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE,4BAA4B;MAClCC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACnH,WAAW,CAAC,CAAC;MAChCoH,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACnG,QAAQ,CAAC;QAAE1B,WAAW,EAAE;MAAM,CAAC,CAAC;MACnD8H,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACpG,QAAQ,CAAC;QAAE1B,WAAW,EAAE;MAAM,CAAC;IACtD,CAAC,CAAC;EACN;EACA,MAAMS,WAAWA,CAAA,EAAG;IAChB,IAAI,CAACiB,QAAQ,CAAC;MAAE1B,WAAW,EAAE;IAAK,CAAC,CAAC;IACpC,IAAIwG,KAAK,GAAG;MACRC,IAAI,EAAE,IAAI,CAACpH,KAAK,CAACG;IACrB,CAAC;IACDgH,KAAK,CAACC,IAAI,CAAChB,MAAM,GAAG,UAAU;IAC9B,MAAMnI,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEkJ,KAAK,CAAC,CAClCtF,IAAI,CAACC,GAAG,IAAI;MACTU,OAAO,CAACC,GAAG,CAACX,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACK,QAAQ,CAAC;QAAE1B,WAAW,EAAE;MAAM,CAAC,CAAC;MACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,2CAA2C;QAAEI,IAAI,EAAE;MAAK,CAAC,CAAC;MAC9HE,UAAU,CAAC,MAAM;QACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC4D,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAmG,YAAA,EAAAC,YAAA;MACZnG,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACF,QAAQ,CAAC;QAAE1B,WAAW,EAAE;MAAM,CAAC,CAAC;MACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAAgD,YAAA,GAAAnG,CAAC,CAACoD,QAAQ,cAAA+C,YAAA,uBAAVA,YAAA,CAAY1G,IAAI,MAAKI,SAAS,IAAAuG,YAAA,GAAGpG,CAAC,CAACoD,QAAQ,cAAAgD,YAAA,uBAAVA,YAAA,CAAY3G,IAAI,GAAGO,CAAC,CAACqD,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACnOE,UAAU,CAAC,MAAM;QACbsB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7I,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC;EACV;EACA;EACAkC,iBAAiBA,CAAC2B,CAAC,EAAE;IACjB,IAAIjC,MAAM,GAAG,IAAI,CAACN,KAAK,CAACM,MAAM;IAC9B,aAAAvB,OAAA,aAAWuB,MAAM;MAAAsI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAC9B;EACA;EACAC,mBAAmBA,CAAChB,OAAO,EAAE;IACzB,oBAAOjJ,OAAA,CAACX,WAAW;MAAC6J,KAAK,EAAED,OAAO,CAACC,KAAK,CAACD,OAAO,CAACd,QAAQ,CAAC,CAACnD,OAAQ;MAACkF,aAAa,EAAEA,CAAC1G,CAAC,EAAEwF,GAAG,KAAK,IAAI,CAACjH,QAAQ,CAACyB,CAAC,EAAEwF,GAAG,GAAG,SAAS,EAAEC,OAAO,CAAC;IAA6C;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7L;EACA,MAAMhI,WAAWA,CAAA,EAAG;IAChB,IAAIT,MAAM,GAAG,IAAI,CAACN,KAAK,CAACM,MAAM;IAC9B,IAAI2B,IAAI,GAAG,IAAI,CAACjC,KAAK,CAACG,SAAS,CAACuC,UAAU,CAACC,cAAc,CAACV,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC9C,EAAE,KAAKkB,MAAM,CAACmD,SAAS,CAAC;IAChGxB,IAAI,CAAC+B,eAAe,GAAG1D,MAAM,CAACyD,OAAO;EACzC;EACA,MAAM9C,OAAOA,CAAA,EAAG;IAAA,IAAAiI,sBAAA;IACZ,IAAI,CAAC7G,QAAQ,CAAC;MACV7B,aAAa,EAAE;IACnB,CAAC,CAAC;IACF,IAAIoB,GAAG,GAAG,EAAE;IACZ,IAAIuH,QAAQ,GAAG,EAAE;IACjB;IACAvH,GAAG,GAAG,gCAAgC,KAAAsH,sBAAA,GAAG,IAAI,CAAClJ,KAAK,CAACG,SAAS,CAACuC,UAAU,CAAC0G,UAAU,cAAAF,sBAAA,uBAA1CA,sBAAA,CAA4C9J,EAAE;IACvF,MAAMnB,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBC,IAAI,CAAC,MAAMC,GAAG,IAAI;MACf,IAAIA,GAAG,CAACE,IAAI,KAAK,EAAE,EAAE;QACjBmH,QAAQ,GAAGrH,GAAG,CAACE,IAAI,CAACqH,YAAY,CAACC,iBAAiB;MACtD,CAAC,MAAM;QAAA,IAAAC,sBAAA;QACH;QACA3H,GAAG,GAAG,kCAAkC,KAAA2H,sBAAA,GAAG,IAAI,CAACvJ,KAAK,CAACG,SAAS,CAACuC,UAAU,CAAC0G,UAAU,cAAAG,sBAAA,uBAA1CA,sBAAA,CAA4CC,WAAW;QAClG,MAAMvL,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;UACTqH,QAAQ,GAAGrH,GAAG,CAACE,IAAI,CAACqH,YAAY,CAACC,iBAAiB;QACtD,CAAC,CAAC,CAAChH,KAAK,CAAEC,CAAC,IAAK;UACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QAClB,CAAC,CAAC;MAEV;MACA,IAAIkH,IAAI,GAAG,EAAE;MACbN,QAAQ,CAACvG,OAAO,CAACC,OAAO,IAAI;QACxBA,OAAO,CAAC6G,UAAU,CAACC,KAAK,GAAG9G,OAAO,CAAC8G,KAAK;QACxCF,IAAI,CAAC/E,IAAI,CAAC7B,OAAO,CAAC6G,UAAU,CAAC;MACjC,CAAC,CAAC;MACF,IAAI,CAACrH,QAAQ,CAAC;QACVnC,QAAQ,EAAEuJ;MACd,CAAC,CAAC;MACFhL,WAAW,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC6D,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;EACV;EACArB,UAAUA,CAAA,EAAG;IACT,IAAI,CAACmB,QAAQ,CAAC;MACV7B,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAoJ,UAAUA,CAAA,EAAG;IACT,oBACI7K,OAAA;MAAA8K,QAAA,gBACI9K,OAAA,CAACR,OAAO;QAACuL,MAAM,EAAC;MAAiB;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpChK,OAAA;QAAIgL,SAAS,EAAC,2BAA2B;QAAAF,QAAA,GAAE7L,QAAQ,CAACgM,YAAY,EAAC,GAAC,eAAAjL,OAAA;UAAGgL,SAAS,EAAC,mBAAmB;UAAC,mBAAgB,mFAAmF;UAAC,oBAAiB,KAAK;UAAC,cAAW,KAAK;UAAC,cAAW,UAAU;UAACE,KAAK,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE;UAAU;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/T,CAAC;EAEd;EACA5H,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACnB,KAAK,CAACS,eAAe,KAAK,IAAI,IAAI,IAAI,CAACT,KAAK,CAACS,eAAe,CAAC4D,MAAM,GAAG,CAAC,EAAE;MAC9E,IAAI+F,MAAM,GAAG,IAAI,CAACpK,KAAK,CAACS,eAAe,CAAC2J,MAAM,CAAClI,EAAE,IAAIA,EAAE,CAACnC,kBAAkB,CAACsE,MAAM,GAAG,CAAC,CAAC;MACtF,IAAI+F,MAAM,CAAC/F,MAAM,KAAK,CAAC,EAAE;QACrB,IAAI,CAACrE,KAAK,CAACS,eAAe,CAACmC,OAAO,CAACC,OAAO,IAAI;UAC1C,IAAIW,CAAC,GAAG;YACJQ,eAAe,EAAE,CAAC;YAClBF,eAAe,EAAE,CAAC;YAClBR,OAAO,EAAET,OAAO,CAAC9C,kBAAkB,CAAC,CAAC,CAAC,CAACuD,OAAO;YAC9CjE,YAAY,EAAEwD,OAAO,CAACxD,YAAY;YAClCgE,mBAAmB,EAAER,OAAO,CAAC9C,kBAAkB,CAAC,CAAC,CAAC;YAClDsK,KAAK,EAAExH,OAAO,CAAC8G,KAAK,GAAG9G,OAAO,CAAC9C,kBAAkB,CAAC,CAAC,CAAC,CAACmE,WAAW;YAChEoG,UAAU,EAAEzH,OAAO,CAAC8G,KAAK,GAAG9G,OAAO,CAAC9C,kBAAkB,CAAC,CAAC,CAAC,CAACmE,WAAW,GAAGrB,OAAO,CAAC8G,KAAK,GAAG9G,OAAO,CAAC9C,kBAAkB,CAAC,CAAC,CAAC,CAACmE,WAAW,GAAGrB,OAAO,CAAC0H,GAAG,GAAG;UACtJ,CAAC;UACD,IAAI,CAACvK,KAAK,CAACG,SAAS,CAACuC,UAAU,CAACC,cAAc,CAAC+B,IAAI,CAAClB,CAAC,CAAC;QAC1D,CAAC,CAAC;QACF,IAAI,CAACnB,QAAQ,CAAC;UAAE1B,WAAW,EAAE;QAAK,CAAC,CAAC;QACpC,IAAIwG,KAAK,GAAG;UACRC,IAAI,EAAE,IAAI,CAACpH,KAAK,CAACG;QACrB,CAAC;QACDlC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEkJ,KAAK,CAAC,CAC5BtF,IAAI,CAACC,GAAG,IAAI;UACTU,OAAO,CAACC,GAAG,CAACX,GAAG,CAACE,IAAI,CAAC;UACrB,IAAI,CAACK,QAAQ,CAAC;YAAE1B,WAAW,EAAE;UAAM,CAAC,CAAC;UACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,UAAU;YAAEC,MAAM,EAAE,2BAA2B;YAAEI,IAAI,EAAE;UAAK,CAAC,CAAC;UAC9GE,UAAU,CAAC,MAAM;YACbsB,MAAM,CAACC,QAAQ,CAACkD,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAClI,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAkI,YAAA,EAAAC,aAAA;UACZlI,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;UACd,IAAI,CAACF,QAAQ,CAAC;YAAE1B,WAAW,EAAE;UAAM,CAAC,CAAC;UACrC,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAA+E,YAAA,GAAAlI,CAAC,CAACoD,QAAQ,cAAA8E,YAAA,uBAAVA,YAAA,CAAYzI,IAAI,MAAKI,SAAS,IAAAsI,aAAA,GAAGnI,CAAC,CAACoD,QAAQ,cAAA+E,aAAA,uBAAVA,aAAA,CAAY1I,IAAI,GAAGO,CAAC,CAACqD,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UACnOE,UAAU,CAAC,MAAM;YACbsB,MAAM,CAACC,QAAQ,CAACkD,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC;MACV,CAAC,MAAM;QACH,IAAI,CAACnF,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,EAAE,6EAA6E;UAAEI,IAAI,EAAE;QAAK,CAAC,CAAC;MACzK;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,yFAAyF;QAAEI,IAAI,EAAE;MAAK,CAAC,CAAC;IACrL;EACJ;EACA8E,MAAMA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IACL,MAAMxD,MAAM,gBACRpJ,OAAA;MAAKgL,SAAS,EAAC,gBAAgB;MAAAF,QAAA,eAC3B9K,OAAA;QAAKgL,SAAS,EAAC,2DAA2D;QAAAF,QAAA,gBACtE9K,OAAA;UAAKgL,SAAS,EAAC,8BAA8B;UAAAF,QAAA,eACzC9K,OAAA;YAAMgL,SAAS,EAAC,mCAAmC;YAAAF,QAAA,gBAC/C9K,OAAA;cAAGgL,SAAS,EAAC;YAAmB;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnChK,OAAA,CAACP,SAAS;cAACuL,SAAS,EAAC,OAAO;cAACvF,IAAI,EAAC,QAAQ;cAACoH,OAAO,EAAGrJ,CAAC,IAAK,IAAI,CAACF,QAAQ,CAAC;gBAAEhC,YAAY,EAAEkC,CAAC,CAACuH,MAAM,CAAC7B;cAAM,CAAC,CAAE;cAAC4D,WAAW,EAAC;YAAU;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL,EAAA6B,sBAAA,OAAI,CAAC5K,KAAK,CAACG,SAAS,cAAAyK,sBAAA,uBAApBA,sBAAA,CAAsBxE,MAAM,MAAK,UAAU,IAAI,EAAAyE,sBAAA,OAAI,CAAC7K,KAAK,CAACG,SAAS,cAAA0K,sBAAA,uBAApBA,sBAAA,CAAsBzE,MAAM,MAAK,UAAU,IAAI,EAAA0E,sBAAA,OAAI,CAAC9K,KAAK,CAACG,SAAS,cAAA2K,sBAAA,uBAApBA,sBAAA,CAAsB1E,MAAM,MAAK,aAAa,IAAI,EAAA2E,sBAAA,OAAI,CAAC/K,KAAK,CAACG,SAAS,cAAA4K,sBAAA,uBAApBA,sBAAA,CAAsBrI,UAAU,CAAC8B,IAAI,MAAK,SAAS,iBAChMzF,OAAA;UAAKgL,SAAS,EAAC,8BAA8B;UAAAF,QAAA,eACzC9K,OAAA;YAAKgL,SAAS,EAAC,kCAAkC;YAAAF,QAAA,eAC7C9K,OAAA,CAACjB,MAAM;cAACiM,SAAS,EAAC,sCAAsC;cAAC+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC7K,OAAO,CAAC,CAAE;cAAA4I,QAAA,GAAE,GAAC,eAAA9K,OAAA;gBAAGgL,SAAS,EAAC;cAAwB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,KAAC,EAAC/K,QAAQ,CAAC+N,OAAO,EAAC,GAAC;YAAA;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD;IACA,MAAMiD,mBAAmB,gBACrBjN,OAAA,CAACnB,KAAK,CAACqO,QAAQ;MAAApC,QAAA,eACX9K,OAAA,CAACjB,MAAM;QAACoO,KAAK,EAAElO,QAAQ,CAACmO,MAAO;QAACpC,SAAS,EAAC,eAAe;QAAC+B,OAAO,EAAE,IAAI,CAAC5K;MAAW;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACnB;IACD,MAAMqD,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MAAEC,KAAK,EAAE,cAAc;MAAErE,MAAM,EAAEnK,QAAQ,CAACyO,MAAM;MAAEC,IAAI,EAAE,cAAc;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC1G;MAAEJ,KAAK,EAAE,aAAa;MAAErE,MAAM,EAAEnK,QAAQ,CAAC6O,QAAQ;MAAEH,IAAI,EAAE,aAAa;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC1G;MAAEJ,KAAK,EAAE,sCAAsC;MAAErE,MAAM,EAAEnK,QAAQ,CAAC8O,cAAc;MAAEJ,IAAI,EAAE,eAAe;MAAEE,UAAU,EAAE;IAAK,CAAC,EAC3H;MAAEJ,KAAK,EAAE,0CAA0C;MAAErE,MAAM,EAAEnK,QAAQ,CAAC+O,gBAAgB;MAAEL,IAAI,EAAE,mBAAmB;MAAEE,UAAU,EAAE;IAAK,CAAC,EACrI;MAAEJ,KAAK,EAAE,qCAAqC;MAAErE,MAAM,EAAEnK,QAAQ,CAACgP,OAAO;MAAEN,IAAI,EAAE,wBAAwB;MAAEE,UAAU,EAAE;IAAK,CAAC,EAC5H;MAAEJ,KAAK,EAAE,SAAS;MAAErE,MAAM,EAAEnK,QAAQ,CAACiP,OAAO;MAAEP,IAAI,EAAE,eAAe;MAAEE,UAAU,EAAE;IAAK,CAAC,CAC1F;IACD,oBACI7N,OAAA;MAAKgL,SAAS,EAAC,mCAAmC;MAAAF,QAAA,gBAC9C9K,OAAA,CAAChB,KAAK;QAACmP,GAAG,EAAGhL,EAAE,IAAK,IAAI,CAACmD,KAAK,GAAGnD;MAAG;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvChK,OAAA;QAAA8K,QAAA,eACI9K,OAAA,CAACF,GAAG;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNhK,OAAA;QAAKgL,SAAS,EAAC,wBAAwB;QAAAF,QAAA,GAClC,EAAAmB,sBAAA,OAAI,CAAChL,KAAK,CAACG,SAAS,cAAA6K,sBAAA,uBAApBA,sBAAA,CAAsBtI,UAAU,CAAC8B,IAAI,MAAK,WAAW,IAAI,EAAAyG,sBAAA,OAAI,CAACjL,KAAK,CAACG,SAAS,cAAA8K,sBAAA,uBAApBA,sBAAA,CAAsBvI,UAAU,CAAC8B,IAAI,MAAK,SAAS,iBACzGzF,OAAA;UAAIgL,SAAS,EAAC,KAAK;UAAAF,QAAA,GAAE7L,QAAQ,CAACmP,OAAO,eACjCpO,OAAA;YAAMgL,SAAS,EAAC,8BAA8B;YAAAF,QAAA,EAAE,IAAI,CAAC7J,KAAK,CAACI;UAAM;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,EAER,EAAAmC,uBAAA,OAAI,CAAClL,KAAK,CAACG,SAAS,cAAA+K,uBAAA,uBAApBA,uBAAA,CAAsBxI,UAAU,CAAC8B,IAAI,MAAK,WAAW,iBAClDzF,OAAA;UAAIgL,SAAS,EAAC,KAAK;UAAAF,QAAA,GAAE7L,QAAQ,CAACoP,aAAa,eACvCrO,OAAA;YAAMgL,SAAS,EAAC,8BAA8B;YAAAF,QAAA,EAAE,IAAI,CAAC7J,KAAK,CAACI;UAAM;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,EAER,EAAAoC,uBAAA,OAAI,CAACnL,KAAK,CAACG,SAAS,cAAAgL,uBAAA,uBAApBA,uBAAA,CAAsBzI,UAAU,CAAC8B,IAAI,MAAK,SAAS,iBACpDzF,OAAA;UAAIgL,SAAS,EAAC,KAAK;UAAAF,QAAA,GAAE7L,QAAQ,CAACqP,kBAAkB,eACxCtO,OAAA;YAAMgL,SAAS,EAAC,8BAA8B;YAAAF,QAAA,EAAE,IAAI,CAAC7J,KAAK,CAACI;UAAM;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,eACNhK,OAAA;QAAKgL,SAAS,EAAC,mCAAmC;QAAAF,QAAA,eAC9C9K,OAAA,CAACb,SAAS;UAACgP,GAAG,EAAGhL,EAAE,IAAK,IAAI,CAACoL,EAAE,GAAGpL,EAAG;UACjC6H,SAAS,EAAC,6BAA6B;UACvCwD,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAC9BvF,KAAK,EAAE,IAAI,CAACjI,KAAK,CAACC,OAAQ;UAC1BwN,QAAQ,EAAC,KAAK;UACd7M,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;UAC1C8M,aAAa,EAAE,IAAI,CAAC3M,WAAY;UAChCoH,MAAM,EAAEA,MAAO;UACf9H,YAAY,EAAE,IAAI,CAACL,KAAK,CAACK,YAAa;UACtCsN,YAAY,EAAC,GAAG;UAAA9D,QAAA,gBAEhB9K,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,IAAI;YAACrE,MAAM,EAAEnK,QAAQ,CAAC4P,OAAQ;YAACjB,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAChEhK,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,aAAa;YAACrE,MAAM,EAAEnK,QAAQ,CAAC6P,IAAK;YAAClB,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACtEhK,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,aAAa;YAACrE,MAAM,EAAEnK,QAAQ,CAACiP,OAAQ;YAACN,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACzEhK,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,SAAS;YAACrE,MAAM,EAAEnK,QAAQ,CAACsF,OAAQ;YAACqJ,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACpE,IAAI,CAAC/I,KAAK,CAACU,WAAW,iBAChB3B,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,UAAU;YAACrE,MAAM,EAAEnK,QAAQ,CAACgP,OAAQ;YAACL,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAE7EhK,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,QAAQ;YAACrE,MAAM,EAAEnK,QAAQ,CAAC8P,aAAc;YAACnB,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC1EhK,OAAA,CAACZ,MAAM;YAACqO,KAAK,EAAC,SAAS;YAACrE,MAAM,EAAEnK,QAAQ,CAAC+P,aAAc;YAACC,MAAM,EAAGhG,OAAO,IAAK,IAAI,CAACgB,mBAAmB,CAAChB,OAAO,CAAE;YAAC2E,QAAQ;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACnIhK,OAAA,CAACZ,MAAM;YAAC8P,SAAS;YAAC3B,WAAW,EAAE;cAAEC,KAAK,EAAE,KAAK;cAAE2B,QAAQ,EAAE;YAAO,CAAE;YAACC,SAAS,EAAE;cAAEC,SAAS,EAAE;YAAS;UAAE;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,EACL,EAAAqC,uBAAA,OAAI,CAACpL,KAAK,CAACG,SAAS,cAAAiL,uBAAA,uBAApBA,uBAAA,CAAsB1I,UAAU,CAAC8B,IAAI,MAAK,YAAY,IAAI,EAAA6G,uBAAA,OAAI,CAACrL,KAAK,CAACG,SAAS,cAAAkL,uBAAA,uBAApBA,uBAAA,CAAsBjF,MAAM,MAAK,UAAU,IAAI,EAAAkF,uBAAA,OAAI,CAACtL,KAAK,CAACG,SAAS,cAAAmL,uBAAA,uBAApBA,uBAAA,CAAsBlF,MAAM,MAAK,aAAa,IAAI,EAAAmF,uBAAA,OAAI,CAACvL,KAAK,CAACG,SAAS,cAAAoL,uBAAA,uBAApBA,uBAAA,CAAsBnF,MAAM,MAAK,UAAU,iBACnMrH,OAAA;QAAKgL,SAAS,EAAC,iCAAiC;QAAAF,QAAA,gBAC5C9K,OAAA,CAACjB,MAAM;UAACoO,KAAK,EAAElO,QAAQ,CAACqQ,WAAY;UAACvC,OAAO,EAAGvJ,CAAC,IAAK,IAAI,CAACmF,OAAO,CAACnF,CAAC,CAAE;UAAC+L,QAAQ,EAAE,IAAI,CAACtO,KAAK,CAACW;QAAY;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1GhK,OAAA,CAACjB,MAAM;UAACiM,SAAS,EAAC,MAAM;UAACmC,KAAK,EAAElO,QAAQ,CAACuQ,YAAa;UAACzC,OAAO,EAAE,IAAI,CAAC0C;QAAa;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,EAGN,EAAAyC,uBAAA,OAAI,CAACxL,KAAK,CAACG,SAAS,cAAAqL,uBAAA,uBAApBA,uBAAA,CAAsBpF,MAAM,MAAK,QAAQ,IAAI,EAAAqF,uBAAA,OAAI,CAACzL,KAAK,CAACG,SAAS,cAAAsL,uBAAA,uBAApBA,uBAAA,CAAsB/I,UAAU,CAAC8B,IAAI,MAAK,YAAY,iBACnGzF,OAAA;QAAKgL,SAAS,EAAC,oCAAoC;QAAAF,QAAA,eAC/C9K,OAAA,CAACjB,MAAM;UAACoO,KAAK,EAAElO,QAAQ,CAACyQ,KAAM;UAAC3C,OAAO,EAAGvJ,CAAC,IAAK,IAAI,CAACvB,IAAI,CAACuB,CAAC,CAAE;UAAC+L,QAAQ,EAAE,IAAI,CAACtO,KAAK,CAACW;QAAY;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhG,CAAC,EAGN,EAAA2C,uBAAA,OAAI,CAAC1L,KAAK,CAACG,SAAS,cAAAuL,uBAAA,uBAApBA,uBAAA,CAAsBtF,MAAM,MAAK,SAAS,IAAI,EAAAuF,uBAAA,OAAI,CAAC3L,KAAK,CAACG,SAAS,cAAAwL,uBAAA,uBAApBA,uBAAA,CAAsBjJ,UAAU,CAAC8B,IAAI,MAAK,YAAY,iBACpGzF,OAAA;QAAKgL,SAAS,EAAC,oCAAoC;QAAAF,QAAA,eAC/C9K,OAAA,CAACjB,MAAM;UAACoO,KAAK,EAAElO,QAAQ,CAAC0Q,eAAgB;UAAC5C,OAAO,EAAGvJ,CAAC,IAAK,IAAI,CAAC2F,cAAc,CAAC3F,CAAC,CAAE;UAAC+L,QAAQ,EAAE,IAAI,CAACtO,KAAK,CAACW;QAAY;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpH,CAAC,eAEVhK,OAAA,CAACT,MAAM;QAACqQ,OAAO,EAAE,IAAI,CAAC3O,KAAK,CAACQ,aAAc;QAAC2H,MAAM,EAAE,IAAI,CAACyB,UAAW;QAACgF,KAAK;QAAC7E,SAAS,EAAC,kBAAkB;QAAC8E,MAAM,EAAE7C,mBAAoB;QAACvD,MAAM,EAAE,IAAI,CAACvH,UAAW;QAAA2I,QAAA,gBACxJ9K,OAAA,CAACH,WAAW;UAAAgK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfhK,OAAA;UAAKgL,SAAS,EAAC,QAAQ;UAAAF,QAAA,gBACnB9K,OAAA;YAAKgL,SAAS,EAAC,wCAAwC;YAAAF,QAAA,eACnD9K,OAAA,CAACJ,eAAe;cACZsJ,KAAK,EAAE,IAAI,CAACjI,KAAK,CAACE,QAAS;cAC3BkM,MAAM,EAAEA,MAAO;cACfmB,OAAO,EAAC,IAAI;cACZuB,SAAS;cACTrI,IAAI,EAAE,CAAE;cACRsI,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;cACpCC,SAAS,EAAE,IAAI,CAAChP,KAAK,CAACS,eAAgB;cACtCwO,iBAAiB,EAAG1M,CAAC,IAAK,IAAI,CAACF,QAAQ,CAAC;gBAAE5B,eAAe,EAAE8B,CAAC,CAAC0F;cAAM,CAAC,CAAE;cACtEiH,gBAAgB,EAAC;YAAQ;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNhK,OAAA;YAAKgL,SAAS,EAAC,oCAAoC;YAAAF,QAAA,eAC/C9K,OAAA,CAACjB,MAAM;cAACiM,SAAS,EAAC,QAAQ;cAAC+B,OAAO,EAAE,IAAI,CAAC3K,OAAQ;cAAA0I,QAAA,gBAAE9K,OAAA;gBAAGgL,SAAS,EAAC;cAAwB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAAC/K,QAAQ,CAACmR,QAAQ;YAAA;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEf;AACJ;AAEA,eAAe/J,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}