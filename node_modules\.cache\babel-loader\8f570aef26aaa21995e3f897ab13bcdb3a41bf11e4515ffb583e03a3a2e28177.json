{"ast": null, "code": "'use strict';\n\nvar compose = require('redux').compose;\nexports.__esModule = true;\nexports.composeWithDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return undefined;\n  if (typeof arguments[0] === 'object') return compose;\n  return compose.apply(null, arguments);\n};\nexports.devToolsEnhancer = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop) {\n    return noop;\n  };\n};", "map": {"version": 3, "names": ["compose", "require", "exports", "__esModule", "composeWithDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION_COMPOSE__", "arguments", "length", "undefined", "apply", "devToolsEnhancer", "__REDUX_DEVTOOLS_EXTENSION__", "noop"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/redux-devtools-extension/index.js"], "sourcesContent": ["'use strict';\n\nvar compose = require('redux').compose;\n\nexports.__esModule = true;\nexports.composeWithDevTools =\n  typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\n    ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\n    : function () {\n        if (arguments.length === 0) return undefined;\n        if (typeof arguments[0] === 'object') return compose;\n        return compose.apply(null, arguments);\n      };\n\nexports.devToolsEnhancer =\n  typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__\n    ? window.__REDUX_DEVTOOLS_EXTENSION__\n    : function () {\n        return function (noop) {\n          return noop;\n        };\n      };\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,OAAO,CAAC,CAACD,OAAO;AAEtCE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,mBAAmB,GACzB,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,oCAAoC,GACxED,MAAM,CAACC,oCAAoC,GAC3C,YAAY;EACV,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOC,SAAS;EAC5C,IAAI,OAAOF,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAOP,OAAO;EACpD,OAAOA,OAAO,CAACU,KAAK,CAAC,IAAI,EAAEH,SAAS,CAAC;AACvC,CAAC;AAEPL,OAAO,CAACS,gBAAgB,GACtB,OAAON,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACO,4BAA4B,GAChEP,MAAM,CAACO,4BAA4B,GACnC,YAAY;EACV,OAAO,UAAUC,IAAI,EAAE;IACrB,OAAOA,IAAI;EACb,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}