{"version": 3, "file": "escape.js", "sourceRoot": "", "sources": ["../../src/escape.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,<PERSON>AM,CA<PERSON>,MAAM,GAAG,GAAG,CAAC,GAAW,EAAU,EAAE,CACzC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,CAAA;AAExD;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,GAAW,EAAU,EAAE,CAC3C,GAAG;KACA,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC;KAC5B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;KACpB,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;KACxB,IAAI,EAAE,CAAA", "sourcesContent": ["/**\n * turn \\ into \\\\ and # into \\#, for stringifying back to TAP\n */\nexport const esc = (str: string): string =>\n  str.replace(/\\\\/g, '\\\\\\\\').replace(/#/g, '\\\\#').trim()\n\n/**\n * turn \\\\ into \\ and \\# into #, for parsing TAP into JS\n */\nexport const unesc = (str: string): string =>\n  str\n    .replace(/(\\\\\\\\)/g, '\\u0000')\n    .replace(/\\\\#/g, '#')\n    .replace(/\\u0000/g, '\\\\')\n    .trim()\n"]}