{"version": 3, "file": "bins.js", "sourceRoot": "", "sources": ["../../src/bins.ts"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,IAAI,CAAA;AAC9B,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AAC9B,OAAO,GAAG,MAAM,cAAc,CAAA;AAC9B,eAAe,GAAG,EAAE;IAClB,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;IACnB,IAAI,CAAC,GAAG;QAAE,OAAM;IAChB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;IAChC,CAAC;SAAM,CAAC;QACN,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;AACH,CAAC,CAAA", "sourcesContent": ["// chmod bins after build\nimport { chmodSync } from 'fs'\nimport { resolve } from 'path'\nimport pkg from './package.js'\nexport default () => {\n  const { bin } = pkg\n  if (!bin) return\n  if (typeof bin === 'string') {\n    chmodSync(resolve(bin), 0o755)\n  } else {\n    for (const v of Object.values(bin)) {\n      chmodSync(resolve(v), 0o755)\n    }\n  }\n}\n"]}