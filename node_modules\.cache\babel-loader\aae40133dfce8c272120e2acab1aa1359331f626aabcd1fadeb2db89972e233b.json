{"ast": null, "code": "import bindActionCreators from '../utils/bindActionCreators';\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapDispatchToPropsIsFunction(mapDispatchToProps) {\n  return typeof mapDispatchToProps === 'function' ? wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps') : undefined;\n}\nexport function whenMapDispatchToPropsIsMissing(mapDispatchToProps) {\n  return !mapDispatchToProps ? wrapMapToPropsConstant(function (dispatch) {\n    return {\n      dispatch: dispatch\n    };\n  }) : undefined;\n}\nexport function whenMapDispatchToPropsIsObject(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object' ? wrapMapToPropsConstant(function (dispatch) {\n    return bindActionCreators(mapDispatchToProps, dispatch);\n  }) : undefined;\n}\nexport default [whenMapDispatchToPropsIsFunction, whenMapDispatchToPropsIsMissing, whenMapDispatchToPropsIsObject];", "map": {"version": 3, "names": ["bindActionCreators", "wrapMapToPropsConstant", "wrapMapToPropsFunc", "whenMapDispatchToPropsIsFunction", "mapDispatchToProps", "undefined", "whenMapDispatchToPropsIsMissing", "dispatch", "whenMapDispatchToPropsIsObject"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-redux/es/connect/mapDispatchToProps.js"], "sourcesContent": ["import bindActionCreators from '../utils/bindActionCreators';\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapDispatchToPropsIsFunction(mapDispatchToProps) {\n  return typeof mapDispatchToProps === 'function' ? wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps') : undefined;\n}\nexport function whenMapDispatchToPropsIsMissing(mapDispatchToProps) {\n  return !mapDispatchToProps ? wrapMapToPropsConstant(function (dispatch) {\n    return {\n      dispatch: dispatch\n    };\n  }) : undefined;\n}\nexport function whenMapDispatchToPropsIsObject(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object' ? wrapMapToPropsConstant(function (dispatch) {\n    return bindActionCreators(mapDispatchToProps, dispatch);\n  }) : undefined;\n}\nexport default [whenMapDispatchToPropsIsFunction, whenMapDispatchToPropsIsMissing, whenMapDispatchToPropsIsObject];"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,6BAA6B;AAC5D,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC7E,OAAO,SAASC,gCAAgCA,CAACC,kBAAkB,EAAE;EACnE,OAAO,OAAOA,kBAAkB,KAAK,UAAU,GAAGF,kBAAkB,CAACE,kBAAkB,EAAE,oBAAoB,CAAC,GAAGC,SAAS;AAC5H;AACA,OAAO,SAASC,+BAA+BA,CAACF,kBAAkB,EAAE;EAClE,OAAO,CAACA,kBAAkB,GAAGH,sBAAsB,CAAC,UAAUM,QAAQ,EAAE;IACtE,OAAO;MACLA,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,GAAGF,SAAS;AAChB;AACA,OAAO,SAASG,8BAA8BA,CAACJ,kBAAkB,EAAE;EACjE,OAAOA,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,GAAGH,sBAAsB,CAAC,UAAUM,QAAQ,EAAE;IAC/G,OAAOP,kBAAkB,CAACI,kBAAkB,EAAEG,QAAQ,CAAC;EACzD,CAAC,CAAC,GAAGF,SAAS;AAChB;AACA,eAAe,CAACF,gCAAgC,EAAEG,+BAA+B,EAAEE,8BAA8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}