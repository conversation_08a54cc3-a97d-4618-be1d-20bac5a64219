{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nvar uuid = 0;\n/** Is client side and not jsdom */\n\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && canUseDom();\n/** Get unique id for accessibility usage */\n\nexport function getUUID() {\n  var retId; // Test never reach\n\n  /* istanbul ignore if */\n\n  if (isBrowserClient) {\n    retId = uuid;\n    uuid += 1;\n  } else {\n    retId = 'TEST_OR_SSR';\n  }\n  return retId;\n}\nexport default function useId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    setInnerId(\"rc_select_\".concat(getUUID()));\n  }, []);\n  return id || innerId;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "canUseDom", "uuid", "isBrowserClient", "process", "env", "NODE_ENV", "getUUID", "retId", "useId", "id", "_React$useState", "useState", "_React$useState2", "innerId", "setInnerId", "useEffect", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/hooks/useId.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nvar uuid = 0;\n/** Is client side and not jsdom */\n\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && canUseDom();\n/** Get unique id for accessibility usage */\n\nexport function getUUID() {\n  var retId; // Test never reach\n\n  /* istanbul ignore if */\n\n  if (isBrowserClient) {\n    retId = uuid;\n    uuid += 1;\n  } else {\n    retId = 'TEST_OR_SSR';\n  }\n\n  return retId;\n}\nexport default function useId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState(),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      innerId = _React$useState2[0],\n      setInnerId = _React$useState2[1];\n\n  React.useEffect(function () {\n    setInnerId(\"rc_select_\".concat(getUUID()));\n  }, []);\n  return id || innerId;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B;AAChD,IAAIC,IAAI,GAAG,CAAC;AACZ;;AAEA,OAAO,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIL,SAAS,CAAC,CAAC;AAC3E;;AAEA,OAAO,SAASM,OAAOA,CAAA,EAAG;EACxB,IAAIC,KAAK,CAAC,CAAC;;EAEX;;EAEA,IAAIL,eAAe,EAAE;IACnBK,KAAK,GAAGN,IAAI;IACZA,IAAI,IAAI,CAAC;EACX,CAAC,MAAM;IACLM,KAAK,GAAG,aAAa;EACvB;EAEA,OAAOA,KAAK;AACd;AACA,eAAe,SAASC,KAAKA,CAACC,EAAE,EAAE;EAChC;EACA,IAAIC,eAAe,GAAGX,KAAK,CAACY,QAAQ,CAAC,CAAC;IAClCC,gBAAgB,GAAGd,cAAc,CAACY,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpCb,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1BD,UAAU,CAAC,YAAY,CAACE,MAAM,CAACV,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EACN,OAAOG,EAAE,IAAII,OAAO;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}