{"version": 3, "file": "index-cjs.cjs", "sourceRoot": "", "sources": ["../../src/index-cjs.cts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;GAWG;AACH,YAAY;AACZ,iDAAkC;AAClC,YAAY;AACZ,uCAAkC;AAElC,yEAAyE;AACzE,yEAAyE;AACzE,uEAAuE;AACvE,wEAAwE;AACxE,0EAA0E;AAC1E,sCAAsC;AACtC,EAAE;AACF,qEAAqE;AACrE,oEAAoE;AACpE,oCAAoC;AACpC,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;IAC/C,MAAM,CAAC,cAAc,CAAC,WAAC,EAAE,GAAG,EAAE;QAC5B,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG;QACd,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAA;AACJ,CAAC;AACD,MAAM,CAAC,cAAc,CAAC,WAAC,EAAE,SAAS,EAAE;IAClC,GAAG,EAAE,GAAG,EAAE,CAAC,WAAC;IACZ,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACnB,CAAC,CAAA;AAsCF,iBAAS,WAAuB,CAAA", "sourcesContent": ["/**\n * The main tap export, CJS style\n *\n * The only different between this and the ESM export is that, because a\n * CJS default export cannot co-exist with exported types, we have to make\n * the types available as a global namespace. Which, isn't exactly the most\n * elegant thing in the world, since it can conflict with any other module\n * that defines a `tap` global namespace, but at least it's common enough\n * that it doesn't read as too strange or unintuitive.\n *\n * @module\n */\n//@ts-ignore\nimport * as items from './main.js'\n//@ts-ignore\nimport { t, TAP } from './main.js'\n\n// this has to be done in a somewhat tricky way, because Test objects are\n// actually proxies, in order to host all of their plugins. Those proxies\n// are necessarily clever about providing methods that are bound to the\n// appropriate plugin object, so we have to define them here as getters.\n// This is all to prevent the need for `const t = require('tap').default`,\n// which just offends me esthetically.\n//\n// The unfortunate side effect of this hybrid approach with a default\n// export is that TS does not provide a way to export both types AND\n// a default defined with `export =`\nfor (const [key, val] of Object.entries(items)) {\n  Object.defineProperty(t, key, {\n    get: () => val,\n    enumerable: true,\n    configurable: true,\n  })\n}\nObject.defineProperty(t, 'default', {\n  get: () => t,\n  enumerable: true,\n  configurable: true,\n})\n\n// Can't export types along with a default export, so this conventional hack\n// Just dump em into the global in the 'tap' namespace.\ndeclare global {\n  /**\n   * All exported types from the `@tapjs/core` module are exported\n   * here into the global `tap` namespace.\n   */\n  namespace tap {\n    export type Base = items.Base\n    export type BaseOpts = items.BaseOpts\n    export type Counts = items.Counts\n    export type Extra = items.Extra\n    export type Lists = items.Lists\n    export type Minimal = items.Minimal\n    export type Spawn = items.Spawn\n    export type SpawnEvents = items.SpawnEvents\n    export type SpawnOpts = items.SpawnOpts\n    export type Stdin = items.Stdin\n    export type StdinOpts = items.StdinOpts\n    export type TapFile = items.TapFile\n    export type TapFileEvents = items.TapFileEvents\n    export type TapFileOpts = items.TapFileOpts\n    export type TapBaseEvents = items.TapBaseEvents\n    export type TestBase = items.TestBase\n    export type TestBaseEvents = items.TestBaseEvents\n    export type TestBaseOpts = items.TestBaseOpts\n    export type Worker = items.Worker\n    export type WorkerEvents = items.WorkerEvents\n    export type WorkerOpts = items.WorkerOpts\n\n    export type TapPlugin<\n      T extends Object,\n      O extends unknown = unknown,\n    > = items.TapPlugin<T, O>\n  }\n}\nexport = t as TAP & typeof items\n"]}