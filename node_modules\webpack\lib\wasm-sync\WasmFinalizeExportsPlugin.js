/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/

"use strict";

const formatLocation = require("../formatLocation");
const UnsupportedWebAssemblyFeatureError = require("./UnsupportedWebAssemblyFeatureError");

/** @typedef {import("../Compiler")} Compiler */

class WasmFinalizeExportsPlugin {
	/**
	 * Apply the plugin
	 * @param {Compiler} compiler the compiler instance
	 * @returns {void}
	 */
	apply(compiler) {
		compiler.hooks.compilation.tap("WasmFinalizeExportsPlugin", compilation => {
			compilation.hooks.finishModules.tap(
				"WasmFinalizeExportsPlugin",
				modules => {
					for (const module of modules) {
						// 1. if a WebAssembly module
						if (module.type.startsWith("webassembly") === true) {
							const jsIncompatibleExports =
								module.buildMeta.jsIncompatibleExports;

							if (jsIncompatibleExports === undefined) {
								continue;
							}

							for (const connection of compilation.moduleGraph.getIncomingConnections(
								module
							)) {
								// 2. is active and referenced by a non-WebAssembly module
								if (
									connection.isTargetActive(undefined) &&
									connection.originModule.type.startsWith("webassembly") ===
										false
								) {
									const referencedExports =
										compilation.getDependencyReferencedExports(
											connection.dependency,
											undefined
										);

									for (const info of referencedExports) {
										const names = Array.isArray(info) ? info : info.name;
										if (names.length === 0) continue;
										const name = names[0];
										if (typeof name === "object") continue;
										// 3. and uses a func with an incompatible JS signature
										if (
											Object.prototype.hasOwnProperty.call(
												jsIncompatibleExports,
												name
											)
										) {
											// 4. error
											const error = new UnsupportedWebAssemblyFeatureError(
												`Export "${name}" with ${jsIncompatibleExports[name]} can only be used for direct wasm to wasm dependencies\n` +
													`It's used from ${connection.originModule.readableIdentifier(
														compilation.requestShortener
													)} at ${formatLocation(connection.dependency.loc)}.`
											);
											error.module = module;
											compilation.errors.push(error);
										}
									}
								}
							}
						}
					}
				}
			);
		});
	}
}

module.exports = WasmFinalizeExportsPlugin;
