{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useState from './useState';\n/**\n * Similar to `useState` but will use props value if provided.\n * Note that internal use rc-util `useState` hook.\n */\n\nexport default function useMergedState(defaultStateValue, option) {\n  var _ref = option || {},\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    onChange = _ref.onChange,\n    postState = _ref.postState;\n  var _useState = useState(function () {\n      if (value !== undefined) {\n        return value;\n      }\n      if (defaultValue !== undefined) {\n        return typeof defaultValue === 'function' ? defaultValue() : defaultValue;\n      }\n      return typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    innerValue = _useState2[0],\n    setInnerValue = _useState2[1];\n  var mergedValue = value !== undefined ? value : innerValue;\n  if (postState) {\n    mergedValue = postState(mergedValue);\n  } // setState\n\n  var onChangeRef = React.useRef(onChange);\n  onChangeRef.current = onChange;\n  var triggerChange = React.useCallback(function (newValue, ignoreDestroy) {\n    setInnerValue(newValue, ignoreDestroy);\n    if (mergedValue !== newValue && onChangeRef.current) {\n      onChangeRef.current(newValue, mergedValue);\n    }\n  }, [mergedValue, onChangeRef]); // Effect of reset value to `undefined`\n\n  var prevValueRef = React.useRef(value);\n  React.useEffect(function () {\n    if (value === undefined && value !== prevValueRef.current) {\n      setInnerValue(value);\n    }\n    prevValueRef.current = value;\n  }, [value]);\n  return [mergedValue, triggerChange];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useState", "useMergedState", "defaultStateValue", "option", "_ref", "defaultValue", "value", "onChange", "postState", "_useState", "undefined", "_useState2", "innerValue", "setInnerValue", "mergedValue", "onChangeRef", "useRef", "current", "trigger<PERSON>hange", "useCallback", "newValue", "<PERSON><PERSON><PERSON><PERSON>", "prevValueRef", "useEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/hooks/useMergedState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useState from './useState';\n/**\n * Similar to `useState` but will use props value if provided.\n * Note that internal use rc-util `useState` hook.\n */\n\nexport default function useMergedState(defaultStateValue, option) {\n  var _ref = option || {},\n      defaultValue = _ref.defaultValue,\n      value = _ref.value,\n      onChange = _ref.onChange,\n      postState = _ref.postState;\n\n  var _useState = useState(function () {\n    if (value !== undefined) {\n      return value;\n    }\n\n    if (defaultValue !== undefined) {\n      return typeof defaultValue === 'function' ? defaultValue() : defaultValue;\n    }\n\n    return typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      innerValue = _useState2[0],\n      setInnerValue = _useState2[1];\n\n  var mergedValue = value !== undefined ? value : innerValue;\n\n  if (postState) {\n    mergedValue = postState(mergedValue);\n  } // setState\n\n\n  var onChangeRef = React.useRef(onChange);\n  onChangeRef.current = onChange;\n  var triggerChange = React.useCallback(function (newValue, ignoreDestroy) {\n    setInnerValue(newValue, ignoreDestroy);\n\n    if (mergedValue !== newValue && onChangeRef.current) {\n      onChangeRef.current(newValue, mergedValue);\n    }\n  }, [mergedValue, onChangeRef]); // Effect of reset value to `undefined`\n\n  var prevValueRef = React.useRef(value);\n  React.useEffect(function () {\n    if (value === undefined && value !== prevValueRef.current) {\n      setInnerValue(value);\n    }\n\n    prevValueRef.current = value;\n  }, [value]);\n  return [mergedValue, triggerChange];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,YAAY;AACjC;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,iBAAiB,EAAEC,MAAM,EAAE;EAChE,IAAIC,IAAI,GAAGD,MAAM,IAAI,CAAC,CAAC;IACnBE,YAAY,GAAGD,IAAI,CAACC,YAAY;IAChCC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;EAE9B,IAAIC,SAAS,GAAGT,QAAQ,CAAC,YAAY;MACnC,IAAIM,KAAK,KAAKI,SAAS,EAAE;QACvB,OAAOJ,KAAK;MACd;MAEA,IAAID,YAAY,KAAKK,SAAS,EAAE;QAC9B,OAAO,OAAOL,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY;MAC3E;MAEA,OAAO,OAAOH,iBAAiB,KAAK,UAAU,GAAGA,iBAAiB,CAAC,CAAC,GAAGA,iBAAiB;IAC1F,CAAC,CAAC;IACES,UAAU,GAAGb,cAAc,CAACW,SAAS,EAAE,CAAC,CAAC;IACzCG,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEjC,IAAIG,WAAW,GAAGR,KAAK,KAAKI,SAAS,GAAGJ,KAAK,GAAGM,UAAU;EAE1D,IAAIJ,SAAS,EAAE;IACbM,WAAW,GAAGN,SAAS,CAACM,WAAW,CAAC;EACtC,CAAC,CAAC;;EAGF,IAAIC,WAAW,GAAGhB,KAAK,CAACiB,MAAM,CAACT,QAAQ,CAAC;EACxCQ,WAAW,CAACE,OAAO,GAAGV,QAAQ;EAC9B,IAAIW,aAAa,GAAGnB,KAAK,CAACoB,WAAW,CAAC,UAAUC,QAAQ,EAAEC,aAAa,EAAE;IACvER,aAAa,CAACO,QAAQ,EAAEC,aAAa,CAAC;IAEtC,IAAIP,WAAW,KAAKM,QAAQ,IAAIL,WAAW,CAACE,OAAO,EAAE;MACnDF,WAAW,CAACE,OAAO,CAACG,QAAQ,EAAEN,WAAW,CAAC;IAC5C;EACF,CAAC,EAAE,CAACA,WAAW,EAAEC,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIO,YAAY,GAAGvB,KAAK,CAACiB,MAAM,CAACV,KAAK,CAAC;EACtCP,KAAK,CAACwB,SAAS,CAAC,YAAY;IAC1B,IAAIjB,KAAK,KAAKI,SAAS,IAAIJ,KAAK,KAAKgB,YAAY,CAACL,OAAO,EAAE;MACzDJ,aAAa,CAACP,KAAK,CAAC;IACtB;IAEAgB,YAAY,CAACL,OAAO,GAAGX,KAAK;EAC9B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAO,CAACQ,WAAW,EAAEI,aAAa,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}