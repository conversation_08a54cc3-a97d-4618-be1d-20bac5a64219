{"core/audits/accessibility/accesskeys.js | description": {"message": "Клавишите за достъп дават възможност на потребителите бързо да преместят фокуса към определена част от страницата. За правилно навигиране всеки клавиш за достъп трябва да е уникален. [Научете повече за клавишите за достъп](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Някои стойности на `[accesskey]` не са уникални"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Стойностите за `[accesskey]` са уникални"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Всеки елемент `role` на ARIA поддържа конкретен поднабор от атрибути `aria-*`. При несъответствие атрибутите `aria-*` ще станат невалидни. [Научете как да зададете подходящи атрибути на ARIA за всяка роля](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Някои атрибути `[aria-*]` не съответстват на ролите си"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибутите `[aria-*]` съответстват на ролите си"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Когато даден елемент няма достъпно име, екранните четци ще произнасят за него общо име и той ще бъде неизползваем за потребителите, разчитащи на тази технология. [Научете как да направите по-достъпни елементите, свързани с команди](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Елементите `button`, `link` и `menuitem` нямат достъпни имена"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Елементите `button`, `link` и `menuitem` имат достъпни имена"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Помощните технологии, например екранни четци, работят непоследователно, когато за `<body>` за документа е зададено `aria-hidden=\"true\"`. [Научете как `aria-hidden` влияе върху основния текст на документа](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "За `<body>` за документа е зададено `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "За `<body>` за документа не е зададено `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Дъщерните елементи в елемент `[aria-hidden=\"true\"]`, които могат да получават фокуса, не позволяват тези интерактивни елементи да бъдат достъпни за потребителите на помощни технологии, например екранни четци. [Научете как `aria-hidden` влияе на елементите, които могат да се откроят](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Елементите `[aria-hidden=\"true\"]` съдържат дъщерни елементи, които могат да получат фокуса"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Елементите `[aria-hidden=\"true\"]` не съдържат дъщерни елементи, които могат да получат фокуса"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Когато дадено поле за въвеждане няма достъпно име, екранните четци ще произнасят за него общо име и съответно то ще бъде неизползваемо за потребителите, разчитащи на тази технология. [Научете повече за етикетите на полетата за въвеждане](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Полетата за въвеждане за ARIA нямат достъпни имена"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Полетата за въвеждане за ARIA са с достъпни имена"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Когато елемент за отчитане няма достъпно име, екранните четци ще произнасят за него общо име и съответно той ще бъде неизползваем за потребителите, разчитащи на тази технология. [Научете как да наименувате елементите за `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA елементите `meter` нямат достъпни имена"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA елементите `meter` имат достъпни имена"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Когато елемент `progressbar` няма достъпно име, екранните четци ще произнасят за него общо име и съответно той ще бъде неизползваем за потребителите, разчитащи на тази технология. [Научете как да обозначавате елементите `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA елементите `progressbar` нямат достъпни имена"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA елементите `progressbar` имат достъпни имена"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Някои роли на ARIA имат задължителни атрибути, от които екранните четци получават описание на състоянието на съответния елемент. [Научете повече за ролите и задължителните атрибути](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Някои елементи `[role]` нямат всички задължителни атрибути `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Елементите `[role]` имат всички задължителни атрибути `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Някои родителски роли на ARIA трябва да съдържат конкретни дъщерни роли, за да изпълняват функциите за достъпност, за които са предназначени. [Научете повече за ролите и задължителните дъщерни елементи](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Някои елементи с ARIA роля `[role]`, за които се изисква дъщерните им елементи да включват конкретна роля `[role]`, не съдържат някои или всички такива задължителни дъщерни елементи."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Елементите с ARIA роля `[role]`, за които се изисква дъщерните им елементи да включват конкретна роля `[role]`, съдържат всички задължителни дъщерни елементи."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Някои дъщерни роли на ARIA трябва да се съдържат в конкретни родителски роли, за да изпълняват правилно функциите за достъпност, за които са предназначени. [Научете повече за ролите на ARIA и задължителния родителски елемент](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Някои елементи `[role]` не се съдържат в задължителния за тях родителски елемент"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Елементите `[role]` се съдържат в задължителния за тях родителски елемент"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Ролите на ARIA трябва да имат валидни стойности, за да изпълняват функциите за достъпност, за които са предназначени. [Научете повече за валидните роли на ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Някои стойности на `[role]` не са валидни"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Стойностите на `[role]` са валидни"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Когато дадено поле за превключване няма достъпно име, екранните четци ще произнасят за него общо име и то ще бъде неизползваемо за потребителите, разчитащи на тази технология. [Научете повече за полетата за превключване](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Полетата за превключване за ARIA нямат достъпни имена"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Полетата за превключване за ARIA са с достъпни имена"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Когато елемент за подсказка няма достъпно име, екранните четци ще произнасят за него общо име и съответно той ще бъде неизползваем за потребителите, разчитащи на тази технология. [Научете как да наименувате елементите за `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA елементите `tooltip` нямат достъпни имена"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA елементите `tooltip` имат достъпни имена"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Когато елемент `treeitem` няма достъпно име, екранните четци ще произнасят за него общо име и съответно той ще бъде неизползваем за потребителите, разчитащи на тази технология. [Научете повече за обозначаването на елементи `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA елементите `treeitem` нямат достъпни имена"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA елементите `treeitem` имат достъпни имена"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Помощните технологии, като например екранни четци, не могат да интерпретират атрибути на ARIA с невалидни стойности. [Научете повече за валидните стойности за атрибути на ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Някои атрибути `[aria-*]` нямат валидни стойности"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Атрибутите `[aria-*]` имат валидни стойности"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Помощните технологии, като например екранни четци, не могат да интерпретират атрибути на ARIA с невалидни имена. [Научете повече за валидните атрибути на ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Някои атрибути `[aria-*]` не са валидни или са изписани неправилно"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибутите `[aria-*]` са валидни и са изписани правилно"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Елементи с грешки"}, "core/audits/accessibility/button-name.js | description": {"message": "Когато даден бутон няма достъпно име, той ще бъде прочитан като „бутон“ от екранните четци и съответно ще бъде неизползваем за потребителите им. [Научете как да направите бутоните по-достъпни](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Бутоните нямат достъпни имена"}, "core/audits/accessibility/button-name.js | title": {"message": "Бутоните имат достъпни имена"}, "core/audits/accessibility/bypass.js | description": {"message": "Добавянето на начини за заобикаляне на повтарящото се съдържание дава възможност на потребителите, използващи клавиатура, да навигират по-ефективно в страницата. [Научете повече за заобикалянето на блокове](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Страницата не съдържа заглавие, връзка за пропускане или участък с ориентир"}, "core/audits/accessibility/bypass.js | title": {"message": "Страницата съдържа заглавие, връзка за пропускане или участък с ориентир"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Четенето на текст с нисък контраст е трудно или невъзможно за много потребители. [Научете как да осигурите достатъчно контраст между цветовете](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Коефициентът на контрастност между цветовете на заден и преден план не е достатъчно голям."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Коефициентът на контрастност между цветовете на заден и преден план е достатъчно голям"}, "core/audits/accessibility/definition-list.js | description": {"message": "Когато списъците с определения не са маркирани правилно, екранните четци може да предоставят объркваща или неточна информация. [Научете как да структурирате правилно тези списъци](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Елементите `<dl>` не съдържат само правилно подредени групи `<dt>` и `<dd>` и елементи `<script>`, `<template>` или `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Елементите `<dl>` съдържат само правилно подредени групи `<dt>` и `<dd>` и елементи `<script>`, `<template>` или `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Списъчните елементи за определение (`<dt>` и `<dd>`) трябва да бъдат обвити в родителски елемент `<dl>`, за да бъдат прочетени правилно от екранните четци. [Научете как да структурирате правилно списъците с определения](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Някои списъчни елементи за определение не са обвити в елементи `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Списъчните елементи за определение са обвити в елементи `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Заглавието дава възможност на потребителите на екранни четци да добият обща представа за страницата, а потребителите на търсещи машини разчитат на него в голяма степен, за да определят дали страницата е подходяща за търсенето им. [Научете повече за заглавията на документи](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Документът няма елемент `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Документът има елемент `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Всички елементи, които могат да получат фокуса, трябва да са с уникален `id`, за да могат помощните технологии да работят с тях. [Научете как да коригирате дублиращите се `id`](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Атрибутите `[id]` на активните елементи, които могат да получат фокуса, не са уникални"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Атрибутите `[id]` на активните елементи, които могат да получат фокуса, са уникални"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Стойността на атрибута за ARIA трябва да е уникална, за да се предотврати пропускането на други екземпляри от страна на помощните технологии. [Научете как да коригирате дублиращи се идентификатори за ARIA](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Идентификаторите за ARIA не са уникални"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Идентификаторите за ARIA са уникални"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Когато полетата във формуляри имат няколко етикета, е възможно да бъдат произнесени объркващо от помощните технологии, например екранни четци, които използват първия, последния или всички етикети. [Научете как да използвате етикети за формуляри](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Полета във формуляр са с повече от един етикет"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Нито едно поле във формуляр няма повече от един етикет"}, "core/audits/accessibility/frame-title.js | description": {"message": "Потребителите на екранни четци очакват заглавието на рамката да описва съдържанието ѝ. [Научете повече за заглавията на рамки](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Някои елементи `<frame>` или `<iframe>` нямат заглавие"}, "core/audits/accessibility/frame-title.js | title": {"message": "Елементите `<frame>` или `<iframe>` имат заглавие"}, "core/audits/accessibility/heading-order.js | description": {"message": "Правилно подредените заглавия без пропускане на нива предават семантичната структура на страницата и улесняват разбирането ѝ и навигацията в нея с помощни технологии. [Научете повече за реда на заглавията](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Заглавните елементи не са в последователен низходящ ред"}, "core/audits/accessibility/heading-order.js | title": {"message": "Заглавните елементи са в последователен низходящ ред"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ако за дадена страница не е посочен атрибут `lang`, екранният четец приема, че тя е написана на стандартния език, който потребителят е избрал при настройването му. Ако страницата всъщност не е на стандартния език, екранният четец може да не прочете текста ѝ правилно. [Научете повече за атрибута `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Елементът `<html>` няма атрибут `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Елементът `<html>` има атрибут `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Посочването на валиден [език по BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) помага на екранните четци да четат текста правилно. [Научете как да използвате атрибута `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Елементът `<html>` няма валидна стойност за атрибута `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Елементът `<html>` има валидна стойност за атрибута `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "Информативните елементи трябва да имат кратък, описателен алтернативен текст. При декоративните елементи атрибутът alt може да бъде оставен без стойност. [Научете повече за атрибута `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Някои графични елементи нямат атрибути `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Графичните елементи имат атрибути `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Когато за бутон от тип `<input>` се използва изображение, предоставянето на алтернативен текст помага на потребителите на екранни четци да разберат за какво служи бутонът. [Научете повече за алтернативния текст на изображение за елемент input](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Някои елементи `<input type=\"image\">` нямат алтернативен текст (`[alt]`)"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Елементите `<input type=\"image\">` имат алтернативен текст (`[alt]`)"}, "core/audits/accessibility/label.js | description": {"message": "Етикетите дават възможност на помощните технологии, като например екранни четци, да четат правилно контролите във формуляри. [Научете повече за етикетите на елементи във формуляри](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Някои елементи на формуляра нямат свързани етикети"}, "core/audits/accessibility/label.js | title": {"message": "Елементите на формуляра имат свързани етикети"}, "core/audits/accessibility/link-name.js | description": {"message": "Текстът на връзките (и алтернативният текст за изображения, когато се използват за връзки), който е различим, уникален и дава възможност фокусът да бъде поставен върху него, подобрява навигирането за потребителите на екранни четци. [Научете как да направите връзките достъпни](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Някои връзки нямат отличително име"}, "core/audits/accessibility/link-name.js | title": {"message": "Връзките имат отличителни имена"}, "core/audits/accessibility/list.js | description": {"message": "Екранните четци съобщават съдържанието на списъците по специфичен начин. Правилното структуриране на списъците улеснява четенето им от екранните четци. [Научете повече за правилното структуриране](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Някои списъци не съдържат само елементи `<li>` и елементи за поддръжка на скриптове (`<script>` и `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Списъците съдържат само елементи `<li>` и елементи за поддръжка на скриптове (`<script>` и `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Екранните четци изискват списъчните елементи (`<li>`) да се съдържат в родителски елемент `<ul>`, `<ol>` или `<menu>`, за да бъдат прочетени правилно. [Научете повече за правилното структуриране на списъци](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Някои списъчни елементи (`<li>`) не се съдържат в родителски елементи `<ul>`, `<ol>` или `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Някои списъчни елементи (`<li>`) се съдържат в родителски елементи `<ul>`, `<ol>` или `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Потребителите не очакват страницата да се опресни автоматично и ако това се случи, фокусът ще бъде върнат в горната ѝ част. Това може да бъде дразнещо или объркващо за потребителите. [Научете повече за мета маркера refresh](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Документът използва `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Документът не използва `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Невъзможността за промяна на мащаба създава проблем за потребителите със слабо зрение, които разчитат на увеличението на екрана, за да виждат добре съдържанието на уеб страниците. [Научете повече за мета маркера viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` се използва в елемента `<meta name=\"viewport\">` или стойността на атрибута `[maximum-scale]` е по-малка от 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` не се използва в елемента `<meta name=\"viewport\">` и стойността на атрибута `[maximum-scale]` не е по-малка от 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Екранните четци не могат да интерпретират нетекстово съдържание. Добавянето на алтернативен текст към елементите `<object>` помага на екранните четци да предават смисъла им на потребителите. [Научете повече за алтернативния текст за елементи `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` елемента нямат алтернативен текст"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` елемента имат алтернативен текст"}, "core/audits/accessibility/tabindex.js | description": {"message": "Ако стойността е по-голяма от 0, значи се използва изричен ред на навигиране. Въпреки че е технически валидно, това често създава неудобства за потребителите, които разчитат на помощни технологии. [Научете повече за атрибута `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Някои елементи имат атрибут `[tabindex]` със стойност, по-голяма от 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Никой от елементите няма атрибут `[tabindex]` със стойност, по-голяма от 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Екранните четци имат функции за улесняване на навигирането в таблици. Когато клетките от типа `<td>`, използващи атрибута `[headers]`, сочат само към други клетки от същата таблица, това може да подобри практическата работа за потребителите на екранни четци. [Научете повече за атрибута `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Някои клетки в елемент `<table>`, които използват атрибута `[headers]`, сочат към елемент `id`, който не бе намерен в същата таблица."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Клетките в елемент `<table>`, които използват атрибута `[headers]`, сочат към клетки от същата таблица."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Екранните четци имат функции за улесняване на навигирането в таблици. Когато всички заглавки в таблицата сочат към някакъв набор от клетки, това може да подобри практическата работа за потребителите на екранни четци. [Научете повече за заглавките на таблиците](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Някои елементи `<th>` и елементи с `[role=\"columnheader\"/\"rowheader\"]` нямат клетки с данни, за които служат като описание."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Елементите `<th>` и тези с `[role=\"columnheader\"/\"rowheader\"]` имат клетки с данни, за които служат като описание."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Посочването на валиден [език по BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) за елементите дава възможност на екранните четци да произнасят текста правилно. [Научете как да използвате атрибута `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Някои атрибути `[lang]` нямат валидна стойност"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Атрибутите `[lang]` имат валидна стойност"}, "core/audits/accessibility/video-caption.js | description": {"message": "Наличието на надпис за даден видеоклип улеснява достъпа до съответната информация за потребителите със слухови увреждания. [Научете повече за надписите за видеоклипове](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Някои елементи `<video>` не съдържат елемент `<track>` с `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Елементите `<video>` съдържат елемент `<track>` с `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Текуща стойност"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Предложено означение"}, "core/audits/autocomplete.js | description": {"message": "Функцията за `autocomplete` помага на потребителите по-бързо да изпращат формуляри. За да ги улесните, препоръчваме да я активирате, като зададете валидна стойност за атрибута `autocomplete`. [Научете повече за `autocomplete` във формулярите](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Елементите от тип `<input>` нямат правилните атрибути за `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Изисква се преглед от човек"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Прегледайте подредбата на означенията"}, "core/audits/autocomplete.js | title": {"message": "Елементите от типа `<input>` правилно използват функцията за `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Невалидни означения за `autocomplete` ({token}) в {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Прегледайте подредбата на означенията {tokens} в {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "С възможност за действие"}, "core/audits/bf-cache.js | description": {"message": "Голяма част от навигацията представлява връщане назад към предишна страница или повторно преминаване напред. Кешът за назад/напред (bfcache) може да ускори този тип навигиране. [Научете повече за bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 причина за проблема}other{# причини за проблема}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Причина за проблема"}, "core/audits/bf-cache.js | failureTitle": {"message": "Страницата предотврати възстановяването на кеша за назад/напред"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Тип проблем"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Няма възможност за действие"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Предстояща поддръжка на браузъра"}, "core/audits/bf-cache.js | title": {"message": "Страницата не предотврати възстановяването на кеша за назад/напред"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Зареждането на тази страница се забавя от разширения за Chrome. Опитайте да я проверите в режим „инкогнито“ или от потребителски профил в Chrome без инсталирани разширения."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Проверка на скрипта"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Синтактичен анализ на скрипта"}, "core/audits/bootup-time.js | columnTotal": {"message": "Общо процесорно време"}, "core/audits/bootup-time.js | description": {"message": "Препоръчваме да намалите времето, прекарвано в синтактичен анализ, компилиране и изпълнение на JS. Използването на JS ресурси с по-малък размер може да помогне за това. [Научете как да намалите времето за изпълнение на JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Намалете времето за изпълнение на JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Време за изпълнение на JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Премахнете големите дублиращи се JavaScript модули от пакетите, за да намалите ненужния пренос на данни в мрежата. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Премахнете дублиращите се модули в JavaScript пакетите"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Големите GIF файлове не са ефективни за показване на анимирано съдържание. Вместо това препоръчваме да използвате видеоклипове във формат MPEG4/WebM за анимации и PNG/WebP за статични изображения, за да намалите преноса на данни. [Научете повече за ефективните видеоформати](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Използвайте видеоформати за анимираното съдържание"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Полифиловете и трансформациите дават възможност на старите браузъри да използват нови функции на JavaScript, но в голяма степен не са необходими за съвременните браузъри. За JavaScript пакета си използвайте актуална стратегия за внедряване на скриптове посредством откриване на функции за модулни/немодулни скриптове, за да намалите количеството код, изпращан до новите браузъри, и същевременно да запазите поддръжката за старите такива. [Научете как да използвате съвременен JavaScript](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Избягвайте показването на остарял JavaScript в съвременни браузъри"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Графичните формати, като например WebP и AVIF, често предоставят по-добро компресиране от PNG или JPEG, което означава по-бързи изтегляния и по-малко потребление на данни. [Научете повече за съвременните графични формати](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Използвайте съвременни формати за показване на изображения"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "За да намалите времето до интерактивност, препоръчваме скритите изображения и тези извън видимата част на екрана да се зареждат след всички критични ресурси. [Научете как да отложите зареждането на изображенията извън видимата част на екрана](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Отложете зареждането на изображенията извън видимата част на екрана"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ресурси блокират първото изобразяване на страницата ви. Препоръчваме да вградите критичните JS/CSS елементи и да отложите зареждането на всички некритични стилове или JS код. [Научете как да премахвате ресурси, блокиращи изобразяването](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Елиминирайте ресурсите, които блокират изобразяването"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Мрежовите ресурси с голям размер струват пари на потребителите и са тясно свързани с бавното зареждане. [Научете как да намалите размера на ресурсите](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Общият размер бе {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Не използвайте мрежови ресурси с голям размер"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Не се използват мрежови ресурси с голям размер"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Минимизирането на файловете със CSS може да намали размера на мрежовите ресурси. [Научете как да минимизирате CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Минимизирайте CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Минимизирането на файловете с JavaScript може да намали размера на ресурсите и времето за синтактичен анализ на скрипта. [Научете как да минимизирате JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Минимизирайте JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Намалете ненужните правила от стиловите листове и отложете зареждането на CSS кода, който не се използва за съдържанието на видимата на екрана част от страницата, за да намалите преноса на данни през мрежата. [Научете как да намалите неизползвания CSS код](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Намалете неизползваните CSS стилове"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Намалете неизползвания JavaScript и отложете зареждането на скриптове, докато не станат нужни, за да намалите преноса на данни през мрежата. [Научете как да намалите неизползвания JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Намалете неизползвания JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Продължителното съхраняване в кеша може да ускори повторните посещения на страницата ви. [Научете повече за ефективните правила за кеша](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Намерен е 1 ресурс}other{Намерени са # ресурса}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Използвайте ефективни правила за кеша, за да улесните показването на статичните активи"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Използват се ефективни правила за кеширане на статичните активи"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимизираните изображения се зареждат по-бързо и използват по-малко мобилни данни. [Научете как да кодирате ефективно изображенията](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Кодирайте изображенията ефективно"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Действителни размери"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Показани размери"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Изображенията бяха по-големи от размера за показване"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Изображенията бяха подходящи за размера за показване"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Показвайте правилно оразмерени изображения, за да пестите мобилни данни и да ускорите зареждането. [Научете как да оразмерявате изображенията](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Оразмерете изображенията правилно"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "При показването на текстови ресурси трябва да се използва компресиране (gzip, deflate или brotli), за да се намали общият пренос на данни. [Научете повече за компресирането на текста](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Активирайте компресирането на текста"}, "core/audits/content-width.js | description": {"message": "Ако ширината на съдържанието на приложението ви не съответства на тази на прозоречния изглед, приложението ви може да не е оптимизирано за мобилни екрани. [Научете как да оразмерите съдържанието за прозоречния изглед](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Размерът на прозоречния изглед ({innerWidth} пкс) не съответства на размера на прозореца ({outerWidth} пкс)."}, "core/audits/content-width.js | failureTitle": {"message": "Съдържанието не е оразмерено правилно за прозоречния изглед"}, "core/audits/content-width.js | title": {"message": "Съдържанието е оразмерено правилно за прозоречния изглед"}, "core/audits/critical-request-chains.js | description": {"message": "Веригите от критични заявки по-долу ви показват кои ресурси се зареждат с висок приоритет. За да ускорите зареждането на страницата, препоръчваме да скъсите веригите, да намалите размера за изтегляне на ресурсите или да отложите изтеглянето на ненужните от тях. [Научете как да избягвате верижните последователности от критични заявки](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Намерена е 1 верига}other{Намерени са # вериги}}"}, "core/audits/critical-request-chains.js | title": {"message": "Избягвайте верижни последователности от критични заявки"}, "core/audits/csp-xss.js | columnDirective": {"message": "Директива"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Сериозност"}, "core/audits/csp-xss.js | description": {"message": "Строгите правила за сигурност на съдържанието (CSP) значително намаляват риска от атаки чрез изпълняване на скриптове между сайтове (XSS). [Научете как да използвате CSP за предотвратяване на XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Синтактична грешка"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Страницата съдържа CSP, дефинирани в <meta> маркер. Добре е да ги преместите в HTTP заглавка или да дефинирате други строги CSP в HTTP заглавка."}, "core/audits/csp-xss.js | noCsp": {"message": "Няма намерени CSP в режим на налагане"}, "core/audits/csp-xss.js | title": {"message": "Осигуряване на ефективност на CSP срещу XSS атаки"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Оттегляне/предупреждение"}, "core/audits/deprecations.js | columnLine": {"message": "Ред"}, "core/audits/deprecations.js | description": {"message": "Оттеглените приложни програмни интерфейси (API) след време ще бъдат премахнати от браузъра. [Научете повече за тях](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Открито бе 1 предупреждение}other{Открити бяха # предупреждения}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Използва оттеглени приложни програмни интерфейси (API)"}, "core/audits/deprecations.js | title": {"message": "Избягва оттеглени API"}, "core/audits/dobetterweb/charset.js | description": {"message": "Трябва да декларирате система за кодиране на знаците. За целта можете да използвате маркер `<meta>` в първите 1024 байта на HTML кода или в заглавката Content-Type за HTTP отговор. [Научете повече за декларирането на кодиране на знаците](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Декларацията на набор от знаци липсва или е дефинирана твърде късно в HTML кода"}, "core/audits/dobetterweb/charset.js | title": {"message": "Правилно дефиниран набор от знаци"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Посочването на doctype не позволява на браузъра да премине в режим на обратна съвместимост. [Научете повече за декларирането на doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Името за doctype трябва да е низът `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Документът съдържа декларация `doctype`, която задейства `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Документът трябва да съдържа doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "За полето publicId се очакваше да бъде празен низ"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "За полето systemId се очакваше да бъде празен низ"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Документът съдържа декларация `doctype`, която задейства `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "На страницата липсва doctype на HTML, което задейства режим на обратна съвместимост"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Страницата съдържа doctype на HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистически данни"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Стойност"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Големият размер на DOM води до използване на повече памет, удължаване на [стиловите изчисления](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) и забавяне поради [преоформяне](https://developers.google.com/speed/articles/reflow). [Научете как да избягвате прекалено големия размер на DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 елемент}other{# елемента}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Не използвайте DOM с твърде голям размер"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимална дълбочина на DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Общ брой елементи в DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Максимален брой дъщерни елементи"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Не се използва DOM с твърде голям размер"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Потребителите се объркват или нямат доверие на сайтове, които искат да узнаят местоположението им без контекст. Вместо това бихте могли да обвържете заявката към действие на потребителя. [Научете повече за разрешението за геолокация](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Иска разрешение за геолокация при зареждането на страницата"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Избягва да иска разрешение за геолокация при зареждането на страницата"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Тип на проблема"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Проблемите, регистрирани в панела `Issues` на Chrome DevTools, указват наличието на неотстранени неизправности. Те може да се дължат на неуспешни заявки в мрежата, недостатъчни контроли за сигурност и други проблеми с браузъра. Отворете панела „Проблеми“ в Chrome DevTools, за да видите повече подробности за всеки проблем."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "В панела `Issues` на Chrome DevTools бяха регистрирани проблеми"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Блокирано от правило за външни източници"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Рекламите използват много ресурси"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Няма проблеми в панела `Issues` на Chrome DevTools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Версия"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Всички библиотеки на JavaScript за предния слой, открити на страницата. [Научете повече за диагностичната проверка за откриване на библиотеки на JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Открити библиотеки на JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "За потребителите с бавни връзки външните скриптове, вмъквани динамично чрез `document.write()`, могат да забавят зареждането на страницата с десетки секунди. [Научете как да избягвате document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Избягвайте `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Избягва `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Потребителите се объркват или нямат доверие на сайтове, които искат да изпращат известия без контекст. Вместо това бихте могли да обвържете заявката към жестове на потребителя. [Научете повече за отговорното получаване на разрешение за известия](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Иска разрешение за известяване при зареждането на страницата"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Избягва да иска разрешение за известяване при зареждането на страницата"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 предлага много предимства спрямо HTTP/1.1, включително заглавки в двоичен формат и мултиплексиране. [Научете повече за HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 заявка не е обслужена през HTTP/2}other{# заявки не са обслужени през HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Използвайте HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "За да подобрите ефективността на страницата си при превъртане, бихте могли да означите като `passive` приемателите си на събития, свързани с докосване и с колелцето на мишката. [Научете повече за използването на пасивни приематели на събития](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Не използва пасивни приематели на събития за подобряване на ефективността при превъртане"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Използва пасивни приематели на събития за подобряване на ефективността при превъртане"}, "core/audits/errors-in-console.js | description": {"message": "Грешките, записани в конзолата, показват нерешени проблеми. Те може да се дължат на неуспешни заявки за мрежата и други проблеми в браузъра. [Научете повече за тези грешки, като прегледате данните от диагностичната проверка в конзолата](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "В конзолата бяха записани грешки в браузъра"}, "core/audits/errors-in-console.js | title": {"message": "В конзолата не бяха записани грешки в браузъра"}, "core/audits/font-display.js | description": {"message": "Използвайте функцията `font-display` на CSS, така че текстът да е видим за потребителите, докато уеб шрифтовете се зареждат. [Научете повече за `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Уверете се, че текстът остава видим при зареждането на уеб шрифтовете"}, "core/audits/font-display.js | title": {"message": "Целият текст остава видим при зареждането на уеб шрифтовете"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse не успя да провери автоматично стойността на `font-display` за източника {fontOrigin}.}other{Lighthouse не успя да провери автоматично стойностите на `font-display` за източника {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Съотношение (действително)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Съотношение (показвано)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Размерите за показване на изображението трябва да съответстват на естествения формат. [Научете повече за формата на изображенията](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Показва изображения с неправилно съотношение"}, "core/audits/image-aspect-ratio.js | title": {"message": "Показва изображенията с правилно съотношение"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Действителен размер"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Показван размер"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Очакван размер"}, "core/audits/image-size-responsive.js | description": {"message": "Естествените размери на изображенията трябва да са пропорционални на размера на екрана и пикселното съотношение, за да се постигне максимално ясно изобразяване. [Научете как да предоставяте адаптивни изображения](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Изображенията се показват с ниска разделителна способност"}, "core/audits/image-size-responsive.js | title": {"message": "Изображенията се показват с подходяща разделителна способност"}, "core/audits/installable-manifest.js | already-installed": {"message": "Приложението вече е инсталирано"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Изисквана икона от манифеста не бе изтеглена"}, "core/audits/installable-manifest.js | columnValue": {"message": "Причина за проблема"}, "core/audits/installable-manifest.js | description": {"message": "Service worker е технологията, която дава възможност на приложението ви да използва много от функциите на прогресивните уеб приложения (PWA), като например работа офлайн, добавяне към началния екран и насочени известия. Правилното внедряване на файл service worker и манифест дава възможност на браузърите проактивно да подканват потребителите да добавят приложението ви към началния екран, което може да повиши ангажираността. [Научете повече за изискванията за възможност за инсталиране, свързани с манифеста](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 причина}other{# причини}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Манифестът или файлът service worker на уеб приложението не отговарят на изискванията за възможност за инсталиране"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL адресът на приложението в Google Play Магазин и идентификаторът за Google Play Магазин не съвпадат"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Страницата се зарежда в прозорец в режим „инкогнито“"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Стойността на свойството display в манифеста трябва да е standalone, fullscreen или minimal-ui"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Манифестът съдържа поле display_override и първият поддържан режим на екрана трябва да е standalone, fullscreen или minimal-ui"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Манифестът не бе извлечен, не съдържа нищо или не бе анализиран синтактично"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "По време на извличането на манифеста URL адресът му бе променен."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Манифестът не съдържа поле name или short_name"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Манифестът не съдържа подходяща икона – изисква се файл във формат PNG, SVG или WebP с поне {value0} пкс, атрибутът за размери трябва да е зададен, а атрибутът за предназначение, ако е зададен, трябва да включва стойността any."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Никоя от предоставените икони не е квадрат с размери поне {value0} пиксела във формат PNG, SVG или WebP, за който атрибутът за предназначение не е зададен или е със стойност any"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Изтегленият файл за икона бе празен или повреден"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Не е предоставен идентификатор за Google Play Магазин"}, "core/audits/installable-manifest.js | no-manifest": {"message": "В страницата не е зададен URL адрес на манифеста чрез маркера <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Не бе открит съответстващ файл service worker. Може да се наложи да презаредите страницата или да се уверите, че зададените в манифеста обхват и URL адрес за стартиране попадат в обхвата на service worker за текущата страница."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Файлът service worker не бе проверен, тъй като за него липсваше поле start_url в манифеста"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Идентификаторът на грешката, свързана с възможността за инсталиране, {errorId} не е разпознат"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Страницата не се показва от защитен източник"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Страницата не се зарежда в основната рамка"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Страницата не работи офлайн"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA бе деинсталирано и проверките на възможността за инсталиране се нулират."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Посочената платформа за приложения не се поддържа от Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "В манифеста е посочено prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications се поддържа само в бета- и стабилния канал на Chrome за Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse не успя да установи дали е налице service worker. Опитайте с по-нова версия на Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Схемата ({scheme}) на URL адреса на манифеста не се поддържа под Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "URL адресът за стартиране в манифеста не е валиден"}, "core/audits/installable-manifest.js | title": {"message": "Манифестът и файлът service worker на уеб приложението отговарят на изискванията за възможност за инсталиране"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "В манифеста има URL адрес, който съдържа потребителско име, парола или порт"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Страницата не работи офлайн. Тя няма да бъде считана за страница с възможност за инсталиране след пускането на Chrome 93 в стабилния канал през август 2021 г."}, "core/audits/is-on-https.js | allowed": {"message": "Разрешено"}, "core/audits/is-on-https.js | blocked": {"message": "Блокирано"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Несигурен URL адрес"}, "core/audits/is-on-https.js | columnResolution": {"message": "Действия спрямо заявката"}, "core/audits/is-on-https.js | description": {"message": "Всички сайтове трябва да бъдат защитени с HTTPS, дори онези, които не работят с поверителни данни. Това включва избягването на [смесено съдържание](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), при което някои ресурси се зареждат през HTTP, въпреки че първоначалната заявка се предава през HTTPS. HTTPS не позволява на външни лица да променят или подслушват комуникацията между приложението ви и потребителите и е задължително условие за HTTP/2 и множество нови приложни програмни интерфейси (API) за платформи в мрежата. [Научете повече за HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Установена е 1 незащитена заявка}other{Установени са # незащитени заявки}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Не използва HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Използва HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Автоматично надстроено до HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Разрешено с предупреждение"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Този елемент е най-голямото съдържание, изобразено в прозоречния изглед. [Научете повече за елемента, рендериран при изобразяване на най-голямото съдържание (LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Елемент, рендериран при изобразяване на най-голямото съдържание (LCP)"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Прино<PERSON> към CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Тези елементи в DOM имат най-голям принос за CLS на страницата. [Научете как да подобрите CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Избягвайте големи промени в оформлението"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Забавено заредените изображения на видимата на екрана част от страницата се рендерират на по-късен етап от жизнения ѝ цикъл. Това може да доведе до забавяне при изобразяване на най-голямото съдържание (LCP). [Научете повече за оптималното забавяне при зареждане](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Изображението, рендерирано при изобразяване на най-голямото съдържание (LPC), бе заредено със забавяне"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Изображението, рендерирано при изобразяване на най-голямото съдържание (LPC), бе заредено без забавяне"}, "core/audits/long-tasks.js | description": {"message": "Посочва най-продължителните задачи в основната нишка и ви помага да идентифицирате главните причинители на забавяне при взаимодействие. [Научете как да избягвате времеемките задачи в основната нишка](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Бе открита # продължителна задача}other{Бяха открити # продължителни задачи}}"}, "core/audits/long-tasks.js | title": {"message": "Избягвайте продължителните задачи в основната нишка"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категория"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Препоръчваме да намалите времето, прекарвано в синтактичен анализ, компилиране и изпълнение на JS. Използването на JS ресурси с по-малък размер може да помогне за това. [Научете как да сведете до минимум работата по основната нишка](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Сведете до минимум работата по основната нишка"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Работата по основната нишка е сведена до минимум"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "За да достигнат до възможно най-много потребители, сайтовете трябва да работят във всички основни браузъри. [Научете повече за съвместимостта с различни браузъри](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Сайтът работи в различни браузъри"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Отделните страници трябва да могат да се свързват пряко чрез URL адрес, а URL адресите трябва да са уникални, за да се даде възможност за споделянето им в социалните медии. [Научете повече за предоставянето на преки връзки](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Всяка страница има URL адрес"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Дори когато мрежата е бавна, преходите при докосване на различни елементи трябва да са бързи – така у потребителите се създава усещане за добра ефективност. [Научете повече за преходите между страниците](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Преходите между страниците не създават усещане за забавяне на мрежата"}, "core/audits/maskable-icon.js | description": {"message": "Използването на адаптивна икона гарантира, че изображението ще запълни цялата фигура, а не само част от нея, когато приложението бъде инсталирано на устройство. [Научете повече за адаптивните икони в манифеста](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Манифестът не съдържа адаптивна икона"}, "core/audits/maskable-icon.js | title": {"message": "Манифестът съдържа адаптивна икона"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Показателят „Кумулативни структурни промени (CLS)“ измерва движението на видимите елементи в прозоречния изглед. [Научете повече за този показател](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Показателят „Изобразяване след взаимодействие“ измерва колко време е необходимо на страницата, за да реагира визуално след входящи данни от действие на потребител. [Научете повече за този показател](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Показателят „Първо изобразяване на съдържание (FCP)“ указва след колко време се изобразява първият текстов или графичен елемент. [Научете повече за този показател](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Показателят „Първо значимо изобразяване“ измерва времето, за което основното съдържание на страницата става видимо. [Научете повече за този показател](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Времето до интерактивност показва след колко време страницата става напълно интерактивна. [Научете повече за този показател](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "„Изобразяване на най-голямото съдържание (LCP)“ показва времето, когато се изобразяват най-големите текст или изображение. [Научете повече за този показател](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Максималното потенциално забавяне при първото взаимодействие на потребителите е продължителността на най-времеемката задача. [Научете повече за този показател](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Индексът на скоростта показва колко бързо се постига визуална завършеност на страницата. [Научете повече за този показател](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Сумата в милисекунди от всички периоди от време между FCP и „Време до интерактивност“, когато задачата е траела над 50 мсек. [Научете повече за показателя „Общо време на блокиране“](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Времето за осъществяване на двупосочна комуникация в мрежата оказва голямо влияние върху ефективността. Ако двупосочната комуникация с източника отнема дълго време, това означава, че разположени по-близо до потребителя сървъри биха подобрили ефективността. [Научете повече за времето за двупосочна комуникация](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Време за двупосочна комуникация в мрежата"}, "core/audits/network-server-latency.js | description": {"message": "Забавянията на сървъра могат да повлияят на ефективността на уебсайта. Голямото забавяне при източника указва, че сървърът е претоварен или задният слой не работи достатъчно ефективно. [Научете повече за времето за реакция на сървъра](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Забавяния в задния слой на сървъра"}, "core/audits/no-unload-listeners.js | description": {"message": "Събитието `unload` не се задейства надеждно и приемането му може да попречи на оптимизациите на браузъра, като например Back-Forward Cache. Вместо това използвайте събитията `pagehide` или `visibilitychange`. [Научете повече за приемателите на събития unload](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Регистрира приемател за `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Избягва приематели за събития `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Анимации<PERSON><PERSON>, които не са комбинирани, могат да са с лошо качество и да увеличат стойността на показателя CLS. [Научете как да ги избягвате](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Бе открит # анимиран елемент}other{Бяха открити # анимирани елемента}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Различните варианти на собствеността filter може да преместват пикселите"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Целевата анимация съдържа друга, която е несъвместима"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Ефектът съдържа комбиниран режим, различен от replace"}, "core/audits/non-composited-animations.js | title": {"message": "Избягване на анимации, които не са комбинирани"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Различните варианти на собствеността transform зависят от размера на елемента"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Неподдържана собственост на CSS: {properties}}other{Неподдържани собствености на CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Ефектът съдържа неподдържани параметри за време"}, "core/audits/performance-budget.js | description": {"message": "Поддържайте количеството и обема на мрежовите заявки под целевите стойности в посочените лимити за показатели с въздействие върху ефективността. [Научете повече за тези лимити](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 заявка}other{# заявки}}"}, "core/audits/performance-budget.js | title": {"message": "Бюджет за ефективността"}, "core/audits/preload-fonts.js | description": {"message": "Заредете предварително шрифтовете със свойство `optional`, за да могат да се ползват от новите посетители. [Научете повече за предварителното зареждане на шрифтове](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "Шрифтовете, които използват собствеността `font-display: optional`, не са заредени предварително"}, "core/audits/preload-fonts.js | title": {"message": "Шрифтовете, които използват собствеността `font-display: optional`, са заредени предварително"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ако най-големият елемент се добавя динамично към страницата, трябва да заредите предварително изображението, за да подобрите LCP. [Научете повече за предварителното зареждане на най-големите елементи](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Предварително зареждане на изображението, рендерирано при изобразяване на най-голямото съдържание (LPC)"}, "core/audits/redirects.js | description": {"message": "Пренасочванията водят до допълнително забавяне на зареждането на страницата. [Научете как да ги избягвате](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Не използвайте пренасочвания през няколко страници"}, "core/audits/resource-summary.js | description": {"message": "За да определите лимити за количеството и размера на ресурсите на страницата, добавете файл budget.json. [Научете повече за лимитите за показатели с въздействие върху ефективността](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 заявка • {byteCount, number, bytes} KiB}other{# заявки • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Поддържайте малък брой заявки и неголям обем на прехвърляните данни"}, "core/audits/seo/canonical.js | description": {"message": "Каноничните връзки указват кой URL адрес да се показва в резултатите от търсенето. [Научете повече за каноничните връзки](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Множество несъвместими URL адреси ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Невалиден URL адрес ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Сочи към местоположение с друг атрибут `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Не е абсолютен URL адрес ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Води до основния URL адрес (началната страница) на домейна вместо до еквивалентна страница със съдържание"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Документът няма валидна връзка от тип `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Документът има валиден атрибут `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Връзка без възможност за обхождане"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Търсещите машини може да използват атрибутите `href` във връзките, за да обхождат съответните уебсайтове. Атрибутът `href` на елементите anchor трябва да води към подходящо местоназначение, за да се даде възможност за откриване на повече страници от сайта. [Научете как да позволите обхождането на връзките](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Обхождането на връзките не е възможно"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Обхождането на връзките е възможно"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Още нечетлив текст"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Размер на шрифта"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% от текста на страницата"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Селектор"}, "core/audits/seo/font-size.js | description": {"message": "Шрифтовете с размер под 12 пиксела са твърде малки и се налага посетителите от мобилни устройства да увеличат мащаба с разтваряне на пръсти, за да прочетат текста. Старайте се над 60% от текста на страницата да е с размер поне 12 пиксела. [Научете повече за четливите размери на шрифта](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} от текста е четлив"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Текстът не е четлив, тъй като няма мета маркер viewport, оптимизиран за мобилни екрани."}, "core/audits/seo/font-size.js | failureTitle": {"message": "В документа не се използва шрифт с четлив размер"}, "core/audits/seo/font-size.js | legibleText": {"message": "Четлив текст"}, "core/audits/seo/font-size.js | title": {"message": "В документа се използва шрифт с четлив размер"}, "core/audits/seo/hreflang.js | description": {"message": "Връзките от типа hreflang указват на търсещите машини коя версия на страницата да бъде включена в резултатите от търсенето за даден език или регион. [Научете повече за `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Документът няма валиден атрибут `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Относителна стойност на href"}, "core/audits/seo/hreflang.js | title": {"message": "Документът има валиден атрибут `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Неочакван код на език"}, "core/audits/seo/http-status-code.js | description": {"message": "Страниците с невалиден HTTP код на състоянието може да не бъдат индексирани правилно. [Научете повече за HTTP кодовете на състоянието](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Страницата има невалиден HTTP код на състоянието"}, "core/audits/seo/http-status-code.js | title": {"message": "Страницата има валиден HTTP код на състоянието"}, "core/audits/seo/is-crawlable.js | description": {"message": "Търсещите машини не могат да включат страниците ви в резултатите от търсенето, ако нямат разрешение за обхождането им. [Научете повече за директивите за роботи](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Индексирането на страницата е блокирано"}, "core/audits/seo/is-crawlable.js | title": {"message": "Индексирането на страницата не е блокирано"}, "core/audits/seo/link-text.js | description": {"message": "Описателният текст на връзките помага на търсещите машини да разберат съдържанието ви. [Научете как да направите връзките по-достъпни](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Открита е 1 връзка}other{Открити са # връзки}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Текстът на връзките не е описателен"}, "core/audits/seo/link-text.js | title": {"message": "Текстът на връзките е описателен"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Стартирайте [инструмента за тестване на структурирани данни](https://search.google.com/structured-data/testing-tool/) и [анализатора на структурирани данни](http://linter.structured-data.org/), за да проверите структурираните данни. [Научете повече за тези данни](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Структурираните данни са валидни"}, "core/audits/seo/meta-description.js | description": {"message": "Мета описанията може да бъдат включени в резултатите от търсенето, за да се предостави сбито обобщение на съдържанието на страницата. [Научете повече за мета описанието](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Липсва текст на описанието."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Документът няма мета описание"}, "core/audits/seo/meta-description.js | title": {"message": "Документът има мета описание"}, "core/audits/seo/plugins.js | description": {"message": "Търсещите машини не могат да индексират съдържание с приставки. Много устройства ограничават приставките или не ги поддържат. [Научете повече за избягването на приставки](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "В документа се използват приставки"}, "core/audits/seo/plugins.js | title": {"message": "Използването на приставки се избягва в документа"}, "core/audits/seo/robots-txt.js | description": {"message": "Ако файлът ви robots.txt не е форматиран правилно, роботите може да не могат да разберат как искате да бъде обходен или индексиран уебсайтът ви. [Научете повече за robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "При заявката за robots.txt бе върнат следният HTTP код на състоянието: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Открита е 1 грешка}other{Открити са # грешки}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse не успя да изтегли файла robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Файлът robots.txt не е валиден"}, "core/audits/seo/robots-txt.js | title": {"message": "Файлът robots.txt е валиден"}, "core/audits/seo/tap-targets.js | description": {"message": "Интерактивните елементи, като бутони и връзки, трябва да са достатъчно големи (48 x 48 пиксела) и с достатъчно пространство около тях, за да се докосват лесно, без да се застъпват с други елементи. [Научете повече за целевите зони за докосване](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} от целевите зони за докосване са оразмерени правилно"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Целевите зони за докосване са твърде малки, тъй като няма мета маркер viewport, оптимизиран за мобилни екрани"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Целевите зони за докосване не са оразмерени правилно"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Припокриваща се целева зона"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Целева зона за докосване"}, "core/audits/seo/tap-targets.js | title": {"message": "Целевите зони за докосване са оразмерени правилно"}, "core/audits/server-response-time.js | description": {"message": "Времето за реакция на сървъра за главния документ трябва да бъде кратко, тъй като всички други заявки зависят от него. [Научете повече за показателя „Време до първия байт“](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "За основния документ бяха необходими {timeInMs, number, milliseconds} мсек"}, "core/audits/server-response-time.js | failureTitle": {"message": "Намалете началното време за реакция на сървъра"}, "core/audits/server-response-time.js | title": {"message": "Началното време за реакция на сървъра бе кратко"}, "core/audits/service-worker.js | description": {"message": "Service worker е технологията, която дава възможност на приложението ви да използва много от функциите на прогресивните уеб приложения (PWA), като например работа офлайн, добавяне към началния екран и насочени известия. [Научете повече за service worker](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Тази страница е контролир<PERSON><PERSON> от service worker, но не бе намерен параметър `start_url`, тъй като при синтактичния анализ бе установено, че манифестът не е във валиден формат JSON"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Тази страница се контролира от файл service worker, но `start_url` ({startUrl}) не е в обхвата му ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Тази страница се контролира от service worker, но не бе намерен параметър `start_url`, тъй като не бе извлечен манифест."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Този източник има един или повече файлове service worker, но страницата ({pageUrl}) не е в обхвата им."}, "core/audits/service-worker.js | failureTitle": {"message": "Няма регистриран service worker, който контролира страницата и `start_url`"}, "core/audits/service-worker.js | title": {"message": "Регистриран е service worker, който контролира страницата и `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Тематичният първоначален екран гарантира висококачествена практическа работа, когато потребителите стартират приложението ви от началния екран. [Научете повече за първоначалните екрани](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Няма персонализиран първоначален екран"}, "core/audits/splash-screen.js | title": {"message": "Има персонализиран първоначален екран"}, "core/audits/themed-omnibox.js | description": {"message": "Адресната лента на браузъра може да бъде тематична, за да съответства на сайта ви. [Научете повече за задаването на тема за нея](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Не е зададен тематичен цвят за адресната лента."}, "core/audits/themed-omnibox.js | title": {"message": "Зададен е тематичен цвят за адресната лента."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (поддръжка на клиенти)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (маркетинг)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (социални контакти)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (видеосъдържание)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Продукт"}, "core/audits/third-party-facades.js | description": {"message": "Зареждането на някои вградени елементи на трети страни може да бъде забавено. Бихте могли да ги замените с фасада, докато не станат необходими. [Научете как да отложите зареждането на елементи на трети страни чрез фасада](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Налице е # алтернативна фасада}other{Налице са # алтернативни фасади}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Зареждането на някои ресурси на трети страни може да бъде забавено с помощта на фасада"}, "core/audits/third-party-facades.js | title": {"message": "Забавете зареждането на ресурсите на трети страни с помощта на фасади"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Трета страна"}, "core/audits/third-party-summary.js | description": {"message": "Кодът от трети страни може сериозно да повлияе върху скоростта на зареждане. Ограничете броя на излишните доставчици трети страни и опитайте да зареждате кода от трети страни, след като основното зареждане на страницата ви е приключило. [Научете как да сведете до минимум въздействието от трети страни](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Код от трети страни блокира основната нишка за {timeInMs, number, milliseconds} мсек"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Намалете влиянието на кода от трети страни"}, "core/audits/third-party-summary.js | title": {"message": "Сведете до минимум използването на код на трети страни"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Измерване"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Показател"}, "core/audits/timing-budget.js | description": {"message": "Задайте времеви лимит, за да наблюдавате ефективността на сайта си. Ефективните сайтове се зареждат за кратко време и реагират бързо при входящи данни от действие на потребител. [Научете повече за лимитите за показатели с въздействие върху ефективността](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Времеви бюджет"}, "core/audits/unsized-images.js | description": {"message": "Задайте изрично широчина и височина за елементите на изображенията, за да намалите промените в оформлението и подобрите CLS. [Научете как да задавате размери за изображенията](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Елементите на изображенията не са с изрично посочени `width` и `height`"}, "core/audits/unsized-images.js | title": {"message": "Елементите на изображенията са с изрично посочени `width` и `height`"}, "core/audits/user-timings.js | columnType": {"message": "Тип"}, "core/audits/user-timings.js | description": {"message": "Препоръчваме да използвате API за разбивка на потребителските времена за приложението си, за да измервате действителната му ефективност по време на ключови аспекти от практическата работа на потребителите. [Научете повече за клеймата за времена на потребителите](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 потребителско време}other{# потребителски времена}}"}, "core/audits/user-timings.js | title": {"message": "Точки и измервания в разбивката на потребителските времена"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Намерен бе елемент `<link rel=preconnect>` за {securityO<PERSON>in}, който обаче не бе използван от браузъра. Проверете дали използвате правилно атрибута `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Препоръчваме да добавите подсказки `preconnect` или `dns-prefetch` за ресурсите с цел ранно установяване на връзка с важни източници от трети страни. [Научете как да се свържете предварително с необходимите източници](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Осигурете предварително свързване с необходимите източници"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Бяха намерени повече от 2 връзки за `<link rel=preconnect>`. Те трябва да се използват пестеливо и само за най-важните източници."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Намерен бе елемент `<link rel=preconnect>` за {securityOrigin}, който обаче не бе използван от браузъра. Прилагайте `preconnect` само за важни източници, за които е сигурно, че страницата ще изпрати заявка."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Намерен бе елемент `<link>` за предварително зареждане за {preloadURL}, който обаче не бе използван от браузъра. Проверете дали използвате правилно атрибута `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Препоръчваме да използвате `<link rel=preload>`, за да укажете по-ранно извличане на ресурсите, които понастоящем се заявяват на по-късен етап от зареждането на страницата. [Научете как да зареждате предварително ключовите заявки](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Задайте ключовите заявки да се зареждат предварително"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL адрес на карта"}, "core/audits/valid-source-maps.js | description": {"message": "Картите на изходния код преобразуват минимизирания код в оригиналния му вид. Това помага на програмистите да отстраняват грешки в стандартния канал. Също така Lighthouse може да предоставя допълнителни статистически данни. Препоръчваме да внедрите карти на изходния код, за да се възползвате от тези предимства. [Научете повече за картите](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Липсват карти на изходния код за големи файлове на JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "В голям файл на JavaScript липсва карта на изходния код"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Внимание: Липсва 1 елемент в атрибута `.sourcesContent`}other{Внимание: Липсват # елемента в атрибута `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Страницата има валидни карти на изходния код"}, "core/audits/viewport.js | description": {"message": "Мета маркерът `<meta name=\"viewport\">` не само оптимизира приложението ви за различни размери екрани на мобилни устройства, но и предотвратява [забавянето от 300 милисекунди за входящите данни от действие на потребител](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Научете повече за използването на мета маркера viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Не бе намерен маркер `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Няма маркер `<meta name=\"viewport\">` с атрибут `width` или `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Има маркер `<meta name=\"viewport\">` с атрибут `width` или `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Това са блокиращите нишката операции, извършвани в периода между взаимодействието и следващото изобразяване. [Научете повече за показателя „Изобразяване след взаимодействие“](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "Събитието {interactionType} отне {timeInMs, number, milliseconds} мсек"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Цел на събитието"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Свеждане до минимум на работата по време на ключово взаимодействие"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Забавяне след входящи данни от действие"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Забавяне на представянето"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Време за обработка"}, "core/audits/work-during-interaction.js | title": {"message": "Работата по време на ключово взаимодействие е сведена до минимум"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Това са възможности за подобряване на използването на ARIA в приложението ви. Така може да подобрите практическата работа за потребителите на помощни технологии, като например екранни четци."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Това са възможности да предоставите алтернативно съдържание за аудио- и видеоелементите. Така може да подобрите практическата работа за потребители със слухови или зрителни нарушения."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Аудио и видео"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Тези елементи открояват често използвани най-добри практики за достъпност."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Най-добри практики"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Тези проверки открояват възможности за [подобряване на достъпността на уеб приложението ви](https://developer.chrome.com/docs/lighthouse/accessibility/). Само определени проблеми с достъпността могат да бъдат открити автоматично. Затова ръчното тестване също е препоръчително."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Тези проверки покриват области, които са извън обхвата на автоматичните инструменти за тестване. Научете повече в ръководството ни за [извършване на преглед на достъпността](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Достъпност"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Това са възможности за подобряване на четливостта на съдържанието ви."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контраст"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Това са възможности да направите съдържанието си по-разбираемо за потребителите, използващи други езици."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Интернационализация и локализация"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Това са възможности за подобряване на семантиката на контролите в приложението ви. Така може да подобрите практическата работа за потребителите на помощни технологии, като например екранни четци."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Имена и етикети"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Това са възможности за подобряване на навигирането с клавиатура в приложението ви."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навигация"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Това са възможности да улесните четенето на данни в таблици или списъци посредством помощни технологии, като например екранни четци."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Таблици и списъци"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Съвместимост с браузъри"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Най-добри практики"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Об<PERSON>и"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Доверие и безопасност"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Практическа работа на потребителите"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Бюджетите за ефективността задават стандарти за ефективността на сайта ви."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Бюджети"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Повече информация за ефективността на приложението ви. Тези стойности не се [отразяват директно](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) върху рейтинга за ефективността."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Диагностика"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Най-критичният аспект на ефективността е времето, за което пикселите се изобразяват на екрана. Ключови показатели: първо изобразяване на съдържание, първо значимо изобразяване"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Подобрения, свързани с първото изобразяване"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Тези предложения може да ускорят зареждането на страницата ви. Те не се [отразяват директно](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) върху рейтинга за ефективността."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Възможности"}, "core/config/default-config.js | metricGroupTitle": {"message": "Показатели"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Подобрете зареждането като цяло, така че страницата да реагира бързо и да е готова за използване възможно най-скоро. Ключови показатели: време до интерактивност, индекс на скоростта"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Цялостни подобрения"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Ефективност"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Тези проверки са свързани с аспектите на прогресивните уеб приложения. [Научете с какво се отличават добрите такива приложения](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Тези проверки са задължителни според отправния [контролен списък за PWA](https://web.dev/pwa-checklist/), но не се извършват автоматично от Lighthouse. Те не се отразяват на резултата ви, но е важно да ги потвърдите ръчно."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Възможност за инсталиране"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Оптимизиране за PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Тези проверки показват дали страницата ви следва съвета за основно оптимизиране за търсещи машини. Има много допълнителни фактори, които тук не са взети под внимание от Lighthouse. Те може да повлияят на класирането на резултатите от търсенето, включително на ефективността според [основните показатели за мрежата](https://web.dev/learn-core-web-vitals/). [Научете повече за основните елементи на Google Търсене](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Стартирайте тези допълнителни инструменти на сайта си, за да проверите дали е съобразен с други най-добри практики за SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Форматирайте HTML кода си по начин, който дава възможност на роботите да разберат по-добре съдържанието на приложението ви."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Най-добри практики за съдържанието"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "За да се показва приложението ви в резултатите от търсенето, роботите се нуждаят от достъп до него."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Обхождане и индексиране"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Уверете се, че страниците ви са удобни за мобилни устройства, така че да не е необходимо потребителите да събират пръсти или да увеличават мащаба, за да прочетат съдържанието. [Научете как да направите страниците удобни за мобилни устройства](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Удобство за мобилни устройства"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Изглежда, че процесорът на тестваното устройство е по-бавен от очакваното от Lighthouse. Това може да повлияе отрицателно на резултата за ефективността. Научете повече за [калибрирането на подходящ множител за забавянето на процесора](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Възможно е страницата да не се зарежда нормално, тъй като тестовият ви URL адрес ({requested}) бе пренасочен към {final}. Тествайте директно втория URL адрес."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Страницата се зареждаше твърде бавно и зареждането не завърши в определеното време. Резултатите може да са непълни."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Времето за изчакване при изчистването на кеша на браузъра изтече. Проверете тази страница отново и подайте сигнал за програмна грешка, ако проблемът не бъде отстранен."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Възможно е в следното място да се съхраняват данни, които засягат ефективността при зареждане: {locations}. Проверете тази страница в прозорец в режим „инкогнито“, така че тези ресурси да не влияят върху резултатите.}other{Възможно е в следните места да се съхраняват данни, които засягат ефективността при зареждане: {locations}. Проверете тази страница в прозорец в режим „инкогнито“, така че тези ресурси да не влияят върху резултатите.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Времето за изчакване при изчистването на данните за източника изтече. Проверете тази страница отново и подайте сигнал за програмна грешка, ако проблемът не бъде отстранен."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "На условията за кеша за назад/напред отговарят само страниците, заредени чрез заявка GET."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Могат да бъдат кеширани само страници с код на състоянието 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome откри опит за изпълнение на JavaScript от кеша."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Страни<PERSON>и<PERSON><PERSON>, които са заявили AppBanner, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Кешът за назад/напред е деактивиран чрез флагове. За да го активирате локално на това устройство, отворете chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Кешът за назад/напред е деактивиран от командния ред."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Кешът за назад/напред е деактивиран поради недостатъчно памет."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Кешът за назад/напред не се поддържа от делегата."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Кешът за назад/напред е деактивиран за предварителното рендериране."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Страницата не може да бъде кеширана, защото има екземпляр на BroadcastChannel с регистрирани приематели."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Страниците със заглавка cache-control:no-store не могат да бъдат добавени към кеша за назад/напред."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Кешът бе изчистен преднамерено."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Страницата бе извадена от кеша, за да може да бъде кеширана друга страница."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Страниците с приставки понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Страниците, които използват FileChooser API, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Страниците, които използват API за достъп до файловата система, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Страниците, които използват диспечера на носители, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "При излизането от страницата работеше медиен плейър."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Страниците, които използват MediaSession API и задават състояние на възпроизвеждане, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Страниците, които използват MediaSession API и задават манипулатори за действия, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Кешът за назад/напред е деактивиран заради екранния четец."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Страни<PERSON>и<PERSON><PERSON>, които използват SecurityHandler, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Страниците, които използват Serial API, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Страниците, които използват WebAuthetication API, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Страниците, които използват WebBluetooth API, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Страниците, които използват WebUSB API, не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Страници<PERSON>е, които използват worker или worklet само за себе си, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Документът не бе зареден напълно, преди потребителят да излезе от него."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "При излизането от страницата бе показан банер за приложение."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "При излизането от страницата работеше мениджърът на паролите в Chrome."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "При излизането от страницата се създаваше неин сбит вариант чрез DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "При излизането от страницата бе показан визуализаторът на DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Кешът за назад/напред е деактивиран заради разширения, използващи API за съобщения."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Разширенията с постоянна връзка трябва да я прекратят преди използване на кеша за назад/напред."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Разширения с постоянна връзка се опитаха да изпратят съобщения до рамки в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Кешът за назад/напред е деактивиран заради разширения."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "При излизането от страницата бе показан модален диалогов прозорец, например за повторно изпращане на формуляр или за въвеждане на парола за HTTP удостоверяване."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "При излизането от страницата бе показана офлайн версията ѝ."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "При излизането от страницата бе показана лентата за недостиг на памет."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "При излизането от страницата имаше заявки за разрешения."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "При излизането от страницата работеше инструмент за блокиране на изскачащи прозорци."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "При излизането от страницата бяха показани подробностите от Безопасно сърфиране."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Безопасно сърфиране сметна тази страница за злоупотребяваща и блокира изскачащия прозорец."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Активи<PERSON><PERSON><PERSON> бе service worker, докато страницата бе в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Кешът за назад/напред е деактивиран поради грешка в документа."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Страниците, които използват FencedFrames, не могат да се съхраняват в bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Страницата бе извадена от кеша, за да може да бъде кеширана друга страница."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Страниците, на които е предоставен достъп до мултимедийните потоци, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Страниците, които използват портали, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Страници<PERSON><PERSON>, които използват IdleManager, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Страниците, които имат отворена връзка към IndexedDB, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Използвани са неотговарящи на условията API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Страниците, в които JavaScript е вмъкнат от разширение, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Страниците, в които стиловият лист е вмъкнат от разширение, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Вътрешна грешка."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Кешът за назад/напред е деактивиран поради заявка за проверка на връзката."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Страниците, които използват заключване на клавиатурата, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | loading": {"message": "Страницата не бе заредена напълно, преди потребителят да излезе от нея."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Страниците, чийто основен ресурс съдържа cache-control:no-cache, не могат да бъдат добавени в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Страниците, чийто основен ресурс съдържа cache-control:no-store, не могат да бъдат добавени в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Навигацията бе анулирана, преди страницата да бъде възстановена от кеша за назад/напред."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Страницата бе извадена от кеша, защото активна мрежова връзка получи твърде много данни. Chrome ограничава количеството данни, които може да получава дадена страница, докато е кеширана."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Страниците с изпълняващи се fetch() или XHR, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Страницата бе извадена от кеша за назад/напред, защото активна мрежова заявка включваше пренасочване."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Страницата бе извадена от кеша, защото мрежова връзка бе отворена твърде дълго. Chrome ограничава продължителността на времето, през което дадена страница може да получава данни, докато е кеширана."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Страниците, които нямат валидна заглавка на отговора, не могат да бъдат добавени към кеша за назад/напред."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Навигацията бе извършена в рамка, различна от основната."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Страниците с изпълняващи се индексирани транзакции в база от данни, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Страниците с изпълняваща се мрежова заявка понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Страниците с изпълняваща се мрежова заявка за извличане понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Страниците с изпълняваща се мрежова заявка понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Страниците с изпълняваща се XHR мрежова заявка понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Страни<PERSON>и<PERSON><PERSON>, които използват PaymentManager, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Страниците, които използват картина в картината, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | portal": {"message": "Страниците, които използват портали, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | printing": {"message": "Страниците, които показват потребителския интерфейс за отпечатване, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Страницата бе отворена чрез `window.open()` и друг раздел препраща към нея. Или страницата отвори прозорец."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Процесът за рендериране на страницата в кеша за назад/напред претърпя срив."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Процесът за рендериране на страницата в кеша за назад/напред бе прекратен."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Страниците, които са поискали разрешение за запис на аудио, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Страниците, които са поискали разрешение за достъп до сензорите, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Страниците, които са поискали разрешение за извличане или синхронизиране на заден план, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Страниците, които са поискали разрешение за достъп до MIDI, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Страниците, които са поискали разрешение за достъп до известия, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Страниците, които са поискали достъп до хранилището, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Страниците, които са поискали разрешение за запис на видео, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Могат да бъдат кеширани само страници, чиято схема на URL адреса е HTTP или HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Страницата бе заявена от service worker, докато е в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Service worker опита да изпрати `MessageEvent` до страницата в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Регистрацията на ServiceWorker бе прекратена, докато страница бе в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Страницата бе извадена от кеша за назад/напред поради активиране на service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome се рестартира и изчисти записите в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Страници<PERSON>е, които използват SharedWorker, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Страници<PERSON>е, които използват SpeechRecognizer, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Страниците, които използват SpeechSynthesis, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Вложена рамка в страницата започна навигиране, което не завърши."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Страниците, чийто подресурс съдържа cache-control:no-cache, не могат да бъдат добавени в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Страниците, чийто подресурс съдържа cache-control:no-store, не могат да бъдат добавени в кеша за назад/напред."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Престоят на страницата в кеша за назад/напред надхвърли максималното време и тя бе означена като изтекла."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Времето за изчакване на страницата да бъде поставена в кеша за назад/напред изтече (вероятно поради изпълняващи се продължително манипулатори за скриване на страницата)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "В главната рамка на страницата има манипулатор за премахване."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "В подрамка на страницата има манипулатор за премахване."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Браузърът промени заглавката за замяна на потребителския агент."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Страниците, на които е предоставен достъп за запис на видео или аудио, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Страниците, които използват WebDatabase, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Страниците, които използват WebHID, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Страниците, които използват WebLocks, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Страниците, които използват WebNfc, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Страниците, които използват WebOTPService, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Страниците с WebRTC не могат да бъдат добавени към кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Страниците, които използват WebShare, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Страниците с WebSocket не могат да бъдат добавени към кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Страниците с WebTransport не могат да бъдат добавени към кеша за назад/напред."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Страниците, които използват WebXR, понастоящем не отговарят на условията за кеша за назад/напред."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Обмислете дали да не добавите схемите на URL адреси https: и http: URL (пренебрегвани от браузърите, поддържащи strict-dynamic), за да се обезпечи обратна съвместимост с по-стари браузъри."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener е оттеглено от CSP3 насам. Моля, вместо това използвайте заглавката Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer е оттеглено от CSP2 насам. Вместо това използвайте заглавката Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss е оттеглено от CSP2 насам. Вместо това използвайте заглавката X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Липсата на base-uri дава възможност за инжектиране на маркери <base> с цел задаване на базовия URL адрес за всички относителни URL адреси (напр. скриптове), така че да бъде от домейн, контролиран от хакера. Обмислете дали да не зададете base-uri на none или self."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Липсата на object-src разрешава инжектиране на приставки, които изпълняват опасни скриптове. Препоръчваме да зададете object-src на none, ако е възможно."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Липсва директива script-src. Това може да позволи изпълнението на опасни скриптове."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Може би сте забравили точка и запетая? Изглежда, че {keyword} е директива, а не ключова дума."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "За nonce трябва да се използва набора от знаци base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonce трябва да са с дължина поне 8 знака."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Избягвайте употребата на неформатирани схеми на URL адреси ({keyword}) в тази директива. Те разрешават извличането на скриптове от опасни домейни."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Избягвайте употребата на неформатирани заместващи знаци ({keyword}) в тази директива. Те разрешават извличането на скриптове от опасни домейни."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Дестинацията за отчитане се конфигурира само през директивата report-to. Тази директива се поддържа само в браузъри, базирани на Chromium, затова се препоръчва да използвате и директивата report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Няма CSP с конфигурирана дестинация за отчитане. Това затруднява поддръжката на CSP в течение на времето и наблюдението за проблеми при функционирането."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Списъците с разрешения при хоста често може да бъдат заобиколени. Обмислете дали вместо това да не използвате nonce или хешове в CSP заедно със strict-dynamic, ако е необходимо."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Неизвестна CSP директива."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "Изглежда, че {keyword} е невалидна ключова дума."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "unsafe-inline позволява изпълнението на опасни скриптове в страницата и манипулатори на събитието. Обмислете дали да не използвате nonce или хешове в CSP, за да разрешавате скриптове поотделно."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Обмислете дали да не добавите unsafe-inline (пренебрегва се от браузъри, които поддържат nonce и хешове), за да се обезпечи обратна съвместимост с по-стари браузъри."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Заместващият знак (*) няма да обхваща заглавката Authorization при обработване на `Access-Control-Allow-Headers` в CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Заявките за ресурси, чиито URL адреси съдържат премахнатите знаци за интервал (`(n|r|t)`) и знака за по-малко (`<`), са блокирани. За да се заредят тези ресурси, премахнете новите редове и кодирайте знаците за по-малко, използвани например в стойности на атрибути за елементи."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "Приложният програмен интерфейс `chrome.loadTimes()` е оттеглен. Вместо него използвайте стандартизирания API Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "Приложният програмен интерфейс `chrome.loadTimes()` е оттеглен. Вместо него използвайте стандартизирания API Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "Приложният програмен интерфейс `chrome.loadTimes()` е оттеглен. Вместо него използвайте стандартизирания API `nextHopProtocol` в Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "„Бисквитките“, съдържа<PERSON>и знак `(0|r|n)`, ще бъдат отхвърляни вместо скъсявани."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Възможността за отмяна на правилото за един и същ източник чрез задаване на `document.domain` е оттеглена и ще бъде деактивирана по подразбиране. Това предупреждение за оттегляне се отнася за достъпа от външни източници, активиран чрез настройката `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Възможността за задействане на {PH1} от вложени рамки от външни източници е оттеглена и ще бъде премахната в бъдеще."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "За деактивиране на стандартната интеграция на функцията за предаване трябва да се използва атрибутът `disableRemotePlayback`, а не селекторът `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "Приложният програмен интерфейс {PH1} е оттеглен. Вместо него използвайте {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Това е пример на преведено съобщение за оттегляне."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Възможността за отмяна на правилото за един и същ източник чрез задаване на `document.domain` е оттеглена и ще бъде деактивирана по подразбиране. За да продължите да използвате тази функция, откажете се от групите агенти с ключ източник, като изпратите заглавка `Origin-Agent-Cluster: ?0` заедно с HTTP отговора за документа и рамките. За повече подробности вижте https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "Приложният програмен интерфейс `Event.path` е оттеглен и ще бъде премахнат. Вместо него използвайте `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Заглавката `Expect-CT` е оттеглена и ще бъде премахната. Chrome изисква Прозрачност на сертификатите за всички публично надеждни сертификати, издадени след 30 април 2018 г."}, "core/lib/deprecations-strings.js | feature": {"message": "Проверете страницата за състояние на функцията за още подробности."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` и `watchPosition()` вече не работят за незащитени източници. За да използвате тази функция, препоръчваме да прехвърлите приложението си към защитен източник, като например HTTPS. За повече подробности вижте https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` и `watchPosition()` са оттеглени за незащитени източници. За да използвате тази функция, препоръчваме да прехвърлите приложението си към защитен източник, като например HTTPS. За повече подробности вижте https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` вече не работи за незащитени източници. За да използвате тази функция, препоръчваме да прехвърлите приложението си към защитен източник, като например HTTPS. За повече подробности вижте https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "Полето `RTCPeerConnectionIceErrorEvent.hostCandidate` е оттеглено. Вместо него използвайте `RTCPeerConnectionIceErrorEvent.address` или `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Източникът на търговеца и произволни данни от събитието `canmakepayment` на service worker са оттеглени и ще бъдат премахнати: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Уебсайтът заяви подресурс от мрежа, до която успя да осъществи достъп само заради специалните разрешения на потребителите в нея. Тези заявки разкриват в интернет информация за непублични устройства и сървъри. Това повишава риска от атаки чрез фалшифициране на заявки от друг сайт (CSRF) и/или от изтичане на информация. За да намали този риск, Chrome оттегля инициираните от незащитен контекст заявки до непублични подресурси и ще започне да ги блокира."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS не може да се зареди от URL адреси със схема `file:`, освен ако не завършват с файловото разширение `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Възможността за използване на `SourceBuffer.abort()` за прекратяване на асинхронното премахване на периоди чрез `remove()` е оттеглена поради промяна на спецификацията. Поддръжката ще бъде премахната в бъдеще. Вместо това трябва да използвате приемател за събитието `updateend`. Функцията `abort()` е предназначена за прекратяване само на асинхронно добавяне на мултимедия или нулиране на състоянието на инструмента за синтактичен анализ."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Възможността за задаване на стойност за `MediaSource.duration`, по-малка от най-голямото клеймо за дата и час за представяне на буферирани кодирани рамки, е оттеглена поради промяна на спецификацията. Поддръжката за неявно премахване на скъсена буферирана мултимедия ще бъде премахната в бъдеще. Вместо това трябва да извършвате изрично премахване чрез `remove(newDuration, oldDuration)` за всички елементи `sourceBuffers`, за които `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Тази промяна ще влезе в сила с важния етап {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Уеб MIDI ще иска разрешение за използване на специални системни съобщения дори ако опцията sysex не е посочена в `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "API за известия вече не може да се използва от незащитени източници. Препоръчваме да прехвърлите приложението си към защитен източник, като например HTTPS. За повече подробности вижте https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Разрешението за достъп до API за известия вече не може да се заявява от вложени рамки от външни източници. Препоръчваме да го заявявате от рамка от първо ниво или да отваряте нов прозорец."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Партньорът ви договаря връзка през остаряла версия на (D)TLS. Помолете го да отстрани проблема."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "Възможността за използване на WebSQL в незащитен контекст е оттеглена и скоро ще бъде премахната. Моля, използвайте уеб хранилище или индексирана база от данни."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Посочването на `overflow: visible` в маркери img, video и canvas може да доведе до показване на визуално съдържание извън границите на елемента. Вижте https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "Приложният програмен интерфейс `paymentManager.instruments` е оттеглен. Вместо него използвайте функцията за навременно инсталиране на инструменти за обработване на плащания."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Начинът, по който извиквате `PaymentRequest`, заобикаля директивата `connect-src` на правилата за сигурност на съдържанието (CSP). Тази възможност за заобикаляне е оттеглена. От `PaymentRequest` API (в полето `supportedMethods`) добавете идентификатора на начина на плащане към директивата `connect-src` на CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "Типът `StorageType.persistent` е оттеглен. Вместо него използвайте стандартизирания `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "Комбинацията от `<source src>` и родителски елемент `<picture>` е невалидна и следователно бе пренебрегната. Вместо това използвайте `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "Приложният програмен интерфейс `window.webkitStorageInfo` е оттеглен. Вместо него използвайте стандартизирания `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Заявките за подресурси, чиито URL адреси съдържат вградени идентификационни данни (напр. `**********************/`), са блокирани."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Ограничението `DtlsSrtpKeyAgreement` е премахнато. Посочихте за него стойност `false`, която се тълкува като опит за използване на оттегления метод `SDES key negotiation`. Тази функционалност е премахната. Вместо нея използвайте услуга, която поддържа `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Ограничението `DtlsSrtpKeyAgreement` е премахнато. Посочихте за него стойност `true`, която няма ефект, но можете да премахнете това ограничение с цел опростяване."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Установено е използване на `Complex Plan B SDP`. Тази версия на `Session Description Protocol` вече не се поддържа. Вместо нея използвайте `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "Версията `Plan B SDP semantics`, която се използва за изграждане на връзка от типа `RTCPeerConnection` с `{sdpSemantics:plan-b}`, е предишна, нестандартна версия на `Session Description Protocol`, която е изтрита за постоянно от уеб платформата. Тя все още е налична при създаване на връзки чрез `IS_FUCHSIA`, но планираме да я изтрием възможно най-скоро. Спрете да разчитате на нея. За информация относно състоянието вижте https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Опцията `rtcpMuxPolicy` е оттеглена и ще бъде премахната."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` ще изисква изолиране от външни източници. За повече подробности вижте https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Възможността за използване на `speechSynthesis.speak()` без активиране от потребител е оттеглена и ще бъде премахната."}, "core/lib/deprecations-strings.js | title": {"message": "Използвана е оттеглена функция"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Разширенията трябва да се включат в изолирането от външни източници, за да продължат да използват `SharedArrayBuffer`. Вижте https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "Методът {PH1} е за конкретен доставчик. Вместо него използвайте стандартния {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 не се поддържа от JSON отговора в `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Възможността за използване на синхронни обекти `XMLHttpRequest` в основната нишка е оттеглена, тъй като влошава практическата работа на крайните потребители. За повече помощ посетете https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "Методът `supportsSession()` е оттеглен. Вместо него използвайте `isSessionSupported()` и проверете получената логическа стойност."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Време на блокиране на основната нишка"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Време на валидност на кеша"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Описание"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Продължителност"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Елемент"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Елементи с грешки"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Местоположение"}, "core/lib/i18n/i18n.js | columnName": {"message": "Име"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Надхвърля бюджета"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Заявки"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Размер на ресурса"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурс"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Размер"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Източник"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Начален час"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Прекарано време"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Размер на прехвърлянето"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL адрес"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенциална икономия"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенциална икономия"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Потенциално спестяване на {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Бе открит 1 елемент}other{Бяха открити # елемента}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенциално спестяване на {wastedMs, number, milliseconds} мсек"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Първо значимо изобразяване"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON>ри<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Изображение"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Изобразяване след взаимодействие"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Високо"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Ниско"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Средно"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Макс. потенц. забавяне при 1. взаимодействие"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Мултимедия"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} мсек"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Друго"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Други ресурси"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Скрипт"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} сек"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Стилов лист"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Трети страни"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Общо"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Нещо се обърка при записването на трасирането за зареждането на страницата ви. Моля, стартирайте отново Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Времето за изчакване изтече при първоначалното свързване с протокола за инструмента за откриване и отстраняване на грешки."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome не събра екранни снимки при зареждането на страницата. Моля, уверете се, че на нея има видимо съдържание, и опитайте отново да стартирате Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS сървърите не можаха да преобразуват предоставения домейн."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "В задължителния механизъм за събиране на {artifactName} възникна грешка: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Възникна вътрешна грешка в Chrome. Моля, рестартирайте браузъра и опитайте отново да стартирате Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Задължителният механизъм за събиране на {artifactName} не се изпълни."}, "core/lib/lh-error.js | noFcp": {"message": "На страницата не бе изобразено съдържание. Уверете се, че прозорецът на браузъра е на преден план при зареждането, и опитайте отново. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "На страницата не бе изобразено съдържанието, считано за най-голямо (LCP). Уверете се, че тя има валиден LCP елемент, и опитайте отново. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Предоставената страница не е в HTML формат (дефинирана от тип MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Тази версия на Chrome е твърде стара и не поддържа „{featureName}“. Използвайте по-нова, за да видите пълните резултати."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse не успя надеждно да зареди заявената от вас страница. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse не успя надеждно да зареди заявения от вас URL адрес, тъй като страницата спря да реагира."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Посоченият от вас URL адрес няма валиден сертификат за сигурност. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome не допусна зареждане на страница със заставка. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse не успя надеждно да зареди заявената от вас страница. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки. (Подробности: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse не успя надеждно да зареди заявената от вас страница. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки. (Код на състоянието: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Зареждането на страницата бе твърде бавно. Моля, използвайте посочените в отчета възможности за ускоряване на зареждането ѝ, след което опитайте отново да стартирате Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Предвиденото време за изчакване на отговор от протокола DevTools бе превишено. (Метод: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Предвиденото време за извличане на съдържанието на ресурсите бе превишено"}, "core/lib/lh-error.js | urlInvalid": {"message": "Предоставеният от вас URL адрес изглежда невалиден."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Типът MIME на страницата е XHTML: Lighthouse не поддържа изрично този тип документ"}, "core/user-flow.js | defaultFlowName": {"message": "Потребителска навигация ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Отчет за навигирането ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Отчет за моментната снимка ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Отчет за периода от време ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Всички отчети"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Категории"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Достъпност"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Най-добри практики"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Ефективност"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Прогресивно уеб приложение (PWA)"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Настолни компютри"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Тълкуване на отчета на Lighthouse за навигацията"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Информация за навигацията"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Използване на отчетите за навигирането за..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Използвайте отчетите за моментната снимка за..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Използване на отчетите за периода от време за..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Получаване на резултат за ефективността от Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Измерване на показатели за ефективността при зареждане на страниците, като например изобразяване на най-голямото съдържание (LCP) и индекс на скоростта."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Тестване на възможностите на прогресивни уеб приложения."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Намиране на проблеми с достъпността в приложения от една страница и сложни формуляри."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Анализ на най-добрите практики, свързани с взаимодействията с менюта и елементи на ПИ."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Измерване на структурните промени и времето за изпълнение на JavaScript за поредица от взаимодействия."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Откриване на възможности за подобряване на ефективността на продължително отворените страници и приложенията от една страница."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "С най-голямо въздействие"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} информативна проверка}other{{numInformative} информативни проверки}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Мобилни устройства"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Зареждане на страницата"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Отчетите за навигацията анализират зареждането на отделни страници, точно както първоначалните отчети на Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Отчет за навигирането"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} отчет за навигирането}other{{numNavigation} отчета за навигирането}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} проверка, която може да бъде премината успешно}other{{numPassableAudits} проверки, които могат да бъдат преминати успешно}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} успешна проверка}other{{numPassed} успешни проверки}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Средна"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Грешка"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Лоша"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Добра"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Запазване"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Моментно състояние на страницата"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Отчетите за моментната снимка анализират страницата в определено състояние, обикновено след потребителски взаимодействия."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Отчет за моментното състояние"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} отчет за моментната снимка}other{{numSnapshot} отчета за моментната снимка}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Обобщена информация"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Потребителски взаимодействия"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Отчетите за периода от време анализират произволен времеви интервал, който обикновено съдържа потребителски взаимодействия."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Отчет за периода от време"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} отчет за периода от време}other{{numTimespan} отчета за периода от време}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Отчет на Lighthouse за потребителската навигация"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "При анимирано съдържание използвайте маркера [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), за да намалите използването на процесора, когато съдържанието е извън екрана."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Бихте могли да изобразявате всички компоненти от типа [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) във формати WebP, като посочвате подходящи резервни варианти за другите браузъри. [Научете повече](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Проверете дали използвате маркера [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) за изображенията с цел автоматично забавяне на зареждането. [Научете повече](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Използвайте инструменти като [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer), за да [рендерирате оформления за AMP върху сървъра](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Разгледайте [документацията на AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/), за да се уверите, че се поддържат всички стилове."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Елементът [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) поддържа атрибута [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/), за да се посочи кои графични активи да се използват в зависимост от размера на екрана. [Научете повече](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Ако се рендерират много големи списъци, бихте могли да използвате виртуално превъртане с Component Dev Kit (CDK). [Научете повече](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Приложете [разделяне на кода на ниво маршрут](https://web.dev/route-level-code-splitting-in-angular/), за да намалите размера на пакетите си с JavaScript. Също така бихте могли да кеширате предварително активи със [service worker на Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Ако използвате Angular CLI, компилациите трябва да бъдат генерирани в стандартен режим. [Научете повече](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Ако използвате Angular CLI, включете в стандартната версия карти на изходния код, за да могат да се проверяват пакетите. [Научете повече](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Зареждайте маршрутите предварително, за да ускорите навигацията. [Научете повече](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "За да управлявате граничните точки за изображенията, бихте могли да използвате инструмента `BreakpointObserver` в Component Dev Kit (CDK). [Научете повече](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Добре е да качите GIF файла си в услуга, която ще даде възможност да бъде вграден като видеоклип с HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Посочете стойности за `@font-display`, когато дефинирате персонализирани шрифтове в темата си."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Можете да конфигурирате на сайта си [графичните формати WebP със стил за ограничаване на възможността за преобразуване](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Инсталирайте [модул на Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), който поддържа забавено зареждане на изображенията. Тези модули дават възможност за отлагане на зареждането на изображенията извън видимата част на екрана, за да се подобри ефективността."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Добре е да използвате модул за вграждане на важния CSS и JavaScript код или за потенциално асинхронно зареждане на активите чрез JavaScript, като например модула [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Имайте предвид, че оптимизациите, които той извършва, може да възпрепятстват работата на сайта ви, така че вероятно ще се наложи да промените кода."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Времето за реакция на сървъра зависи от спецификациите му, темите и модулите. Добре е да намерите по-оптимизирана тема, внимателно да изберете модул за оптимизиране и/или да надстроите сървъра си. Сървърите ви за хостинг трябва да използват функциите на PHP за кеширане на операционните кодове и кеширане в паметта чрез услуги като Redis или Memcached, за да се намали времето за подаване на заявки към базата от данни, както и оптимизирана логика за приложенията, така че страниците да се подготвят по-бързо."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Бихте могли да използвате [стилове за адаптивни изображения](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), за да намалите размера на графичните файлове, зареждани на страницата ви. Ако си служите с изгледи, за да показвате няколко елемента със съдържание на една страница, можете да внедрите функция за страниране, за да ограничите броя елементи, които да се извеждат на страница."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Трябва да активирате опцията Aggregate CSS files на страницата Administration » Configuration » Development. Можете също да конфигурирате по-разширени опции за агрегиране чрез [допълнителни модули](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search), за да подобрите скоростта на сайта си чрез обединяване, минимизиране и компресиране на CSS стиловете."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Трябва да активирате опцията Aggregate JavaScript files на страницата Administration » Configuration » Development. Можете също да конфигурирате по-разширени опции за агрегиране чрез [допълнителни модули](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search), за да подобрите скоростта на сайта си чрез обединяване, минимизиране и компресиране на JavaScript активите."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Бихте могли да премахнете неизползваните правила на CSS и да добавите само необходимите библиотеки на Drupal към съответната страница или компонент в нея. За подробности отворете връзката към [документацията на Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). За да откриете включените библиотеки, които добавят ненужен CSS код, стартирайте инструмента за [покритие на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Можете да намерите проблемните тема/модул в URL адреса на листа със стилове, когато агрегирането на CSS е деактивирано в сайта ви на Drupal. Търсете теми/модули с много листове със стилове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден лист със стилове трябва да бъде поставен в опашката на тема/модул само ако действително се използва в страницата."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Бихте могли да премахнете неизползваните активи на JavaScript и да добавите само необходимите библиотеки на Drupal към съответната страница или компонент в нея. За подробности отворете връзката към [документацията на Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). За да откриете включените библиотеки, които добавят ненужен JavaScript код, стартирайте инструмента за [покритие на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Можете да намерите проблемните тема/модул в URL адреса на скрипта, когато агрегирането на JavaScript е деактивирано в сайта ви на Drupal. Търсете теми/модули с много скриптове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден скрипт трябва да бъде поставен в опашката на тема/модул само ако действително се използва в страницата."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Задайте опцият<PERSON> Browser and proxy cache maximum age на страницата Administration » Configuration » Development. Прочетете за [настройките на Drupal за кеширане и оптимизиране с цел повишаване на ефективността](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Бихте могли да използвате [модул](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), който автоматично оптимизира и намалява размера на качваните чрез сайта изображения, като същевременно запазва качеството им. Също така трябва да използвате стандартните [стилове за адаптивни изображения](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), предоставени от Drupal (налични в Drupal 8 и по-нови версии), за всички графични файлове, рендерирани на сайта."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Можете да добавите подсказки за ресурси preconnect или dns-prefetch, като инсталирате и конфигурирате [модул](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), който дава възможност за използване на такива подсказки за потребителски агенти."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Трябва да използвате стандартните [стилове за адаптивни изображения](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), предоставени от Drupal (налични в Drupal 8 и по-нови версии). Прилагайте тези стилове при рендериране на графични полета посредством режими на преглед, изгледи или изображения, качени чрез WYSIWYG редактора."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Optimize Fonts`, за да се възползвате автоматично от функцията `font-display` на CSS, така че текстът да е видим за потребителите, докато уеб шрифтовете се зареждат."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Next-Gen Formats`, за да преобразувате изображенията в WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Lazy Load Images`, така че изображенията извън екрана да не се зареждат, преди да е необходимо."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Critical CSS` и `Script Delay` за отлагане на зареждането на некритичните JS/CSS елементи."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Използвайте [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching), за да кеширате съдържанието си в световната ни мрежа и да подобрите показателя за времето до първия байт."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Minify CSS`, така че CSS елементите да се минимизират автоматично с цел намаляване на размера на мрежовите ресурси."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Minify Javascript`, така че JS елементите да се минимизират автоматично с цел намаляване на размера на мрежовите ресурси."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Remove Unused CSS`, за да помогнете за решаването на този проблем. Ще бъдат идентифицирани CSS класовете, които действително се използват в отделните страници на сайта ви, а останалите ще бъдат премахнати с цел намаляване на размера на файловете."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Efficient Static Cache Policy`, за да зададете препоръчителни стойности в заглавката за кеширане за статичните активи."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Next-Gen Formats`, за да преобразувате изображенията в WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Pre-Connect Origins`, така че автоматично да се добавят подсказки `preconnect` за ресурсите с цел ранно установяване на връзка с важни източници от трети страни."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Preload Fonts` и `Preload Background Images`, за да добавите връзки от типа `preload` с цел приоритизиране на извличането на ресурсите, които понастоящем се заявяват на по-късен етап от зареждането на страницата."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Използвайте [Ezoic Leap](https://pubdash.ezoic.com/speed) и активирайте `Resize Images` с цел преоразмеряване на изображенията до размер, подходящ за конкретно устройство, за да намалите необходимите мрежови ресурси."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Добре е да качите GIF файла си в услуга, която ще даде възможност да бъде вграден като видеоклип с HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Добре е да използвате [приставка](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) или услуга за автоматично преобразуване на качените изображения в оптималния формат."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Инсталирайте [приставка за Joomla за забавено зареждане](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), която дава възможност за отлагане на зареждането на изображенията извън видимата част на екрана, или преминете към шаблон с такава функционалност. В Joomla 4.0 и следващи версии всички нови изображения [автоматично](https://github.com/joomla/joomla-cms/pull/30748) ще получават атрибута `loading` от основния код."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Има различни приставки за Joomla, с чиято помощ можете [да вградите важни активи](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) или [да отложите зареждането на не толкова важни ресурси](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Имайте предвид, че оптимизациите, извършвани чрез тези приставки, може да възпрепятстват работата на функциите на шаблоните или приставките ви, така че ще се наложи да ги тествате обстойно."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Времето за реакция на сървъра зависи от спецификациите му, шаблоните и разширенията. Добре е да намерите по-оптимизиран шаблон, внимателно да изберете разширение за оптимизиране и/или да надстроите сървъра си."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Обмислете възможността да показвате извадки в категориите за статии (напр. чрез връзка „научете повече“), да намалите броя на статиите, извеждани на дадена страница, да разделите дългите публикации на няколко страници или да използвате приставка, която да забави зареждането на коментарите."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Има различни [разширения з<PERSON> Joom<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), които могат да подобрят скоростта на сайта ви чрез обединяване, минимизиране и компресиране на CSS стиловете. Има и шаблони, които предоставят тази функционалност."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Има различни [разширения з<PERSON> Joom<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), които могат да подобрят скоростта на сайта ви чрез обединяване, минимизиране и компресиране на скриптовете. Има и шаблони, които предоставят тази функционалност."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Добре е да намалите броя на [разширенията за Joomla](https://extensions.joomla.org/), които зареждат неизползван CSS код в страницата ви. За да откриете разширенията, които добавят ненужен CSS код, стартирайте инструмента за [покритие на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Можете да намерите проблемната тема/приставка в URL адреса на листа със стилове. Търсете приставки с много листове със стилове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден лист със стилове трябва да бъде поставен в опашката на приставка само ако действително се използва в страницата."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Добре е да намалите броя на [разширенията за Joomla](https://extensions.joomla.org/), които зареждат неизползван JavaScript код в страницата ви. За да откриете приставките, които добавят ненужен JS код, стартирайте инструмента за [покритие на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Можете да намерите проблемното разширение в URL адреса на скрипта. Търсете разширения с много скриптове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден скрипт трябва да бъде поставен в опашката на разширение само ако действително се използва в страницата."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Прочетете за [възможностите на Joomla за кеширане в браузъра](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Добре е да използвате [приставка за оптимизиране на изображенията](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), която компресира графичните ви файлове, като същевременно запазва качеството им."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Добре е да използвате [приставка за адаптивни изображения](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), за да можете да ползвате такива в съдържанието си."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Можете да активирате компресирането на текста, като включите компресирането на страниците с Gzip в Joomla (System > Global configuration > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Ако не пакетирате активите си на JavaScript, бихте могли да използвате [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Деактивирайте вградените в Magento функции за [пакетиране и минимизиране на JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), като вместо тях бихте могли да използвате [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Посочете `@font-display`, когато [дефинирате персонализирани шрифтове](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Потърсете [пазара на Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) за разширения от трети страни, позволяващи работа с по-нови графични формати."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Бихте могли да промените шаблоните си за продукти и каталози, за да използват функцията за [отложено зареждане](https://web.dev/native-lazy-loading) в уеб платформата."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Използвайте [интегрирането на Magento с Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Активирайте опцията „Minify CSS Files“ в настройките за програмисти в магазина. [Научете повече](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Използвайте [Terser](https://www.npmjs.com/package/terser) за минимизиране на всички активи на JavaScript от разполагането на статично съдържание и деактивирайте вградената функция за целта."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Деактивирайте вграденото в Magento [пакетиране на JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Потърсете [пазара на Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) за разширения от трети страни за оптимизиране на изображения."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Можете да добавите подсказки за ресурси preconnect или dns-prefetch, като [промените оформлението на дадената тема](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Можете да добавите маркери `<link rel=preload>`, като [промените оформлението на дадената тема](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Използвайте компонента `next/image` вместо `<img>` с цел автоматично оптимизиране на графичния формат. [Научете повече](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Използвайте компонента `next/image` вместо `<img>` с цел автоматично забавяне на зареждането на изображенията. [Научете повече](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Използвайте компонента `next/image` и задайте true за priority с цел предварително зареждане на най-голямото изображение (LCP). [Научете повече](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Използвайте компонента `next/script`, за да отложите зареждането на некритични скриптове от трети страни. [Научете повече](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Използвайте компонента `next/image`, така че изображенията винаги да се оразмеряват правилно. [Научете повече](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Добре е да настроите `PurgeCSS` в конфигурацията с `Next.js`, за да премахнете неизползваните правила от стиловите листове. [Научете повече](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Използвайте `Webpack Bundle Analyzer`, за да откриете неизползван JavaScript код. [Научете повече](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)."}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Добре е да използвате `Next.js <PERSON><PERSON><PERSON>`, за да измервате действителната ефективност на приложението си. [Научете повече](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Конфигурирайте кеширането на непроменливите активи и SSR (`Server-side Rendered`) страници. [Научете повече](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Използвайте компонента `next/image` вместо `<img>`, за да коригирате качеството на изображенията. [Научете повече](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Използвайте компонента `next/image`, за да зададете подходяща стойност за `sizes`. [Научете повече](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Активирайте компресирането в Next.js сървъра си. [Научете повече](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Използвайте компонента `nuxt/image` и задайте `format=\"webp\"`. [Научете повече](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Използвайте компонента `nuxt/image` и задайте `loading=\"lazy\"` за изображенията извън видимата част на екрана. [Научете повече](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Използвайте компонента `nuxt/image` и посочете `preload` за най-голямото изображение (LCP). [Научете повече](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Използвайте компонента `nuxt/image` и посочете изрична стойност за `width` и `height`. [Научете повече](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Използвайте компонента `nuxt/image` и задайте подходяща стойност за `quality`. [Научете повече](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Използвайте компонента `nuxt/image` и задайте подходяща стойност за `sizes`. [Научете повече](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Заменете анимираните GIF файлове с видеосъдържание](https://web.dev/replace-gifs-with-videos/), за да ускорите зареждането на уеб страницата, и помислете за използването на съвременни файлови формати, като [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) или [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), за да подобрите ефективността при компресиране с повече от 30% спрямо най-модерния понастоящем видеокодек VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Добре е да използвате [приставка](https://octobercms.com/plugins?search=image) или услуга за автоматично преобразуване на качените изображения в оптималния формат. Размерът на [графичните файлове в беззагубния формат WebP](https://developers.google.com/speed/webp) е с 26% по-малък в сравнение с PNG и с 25 – 34% по-малък от този на съответните JPEG изображения с еквивалентен SSIM индекс за качество. Друг препоръчителен графичен формат от следващо поколение е [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Бихте могли да инсталирате [приставка за забавено зареждане](https://octobercms.com/plugins?search=lazy), която дава възможност за отлагане на зареждането на изображенията извън видимата част на екрана, или да преминете към тема с такава функционалност. Можете също да използвате [приставката за AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Има много приставки, с чиято помощ [да вградите важни активи](https://octobercms.com/plugins?search=css). Тези приставки може да възпрепятстват работата на други, така че е добре да ги тествате обстойно."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Времето за реакция на сървъра зависи от спецификациите му, темите и приставките. Добре е да намерите по-оптимизирана тема, внимателно да изберете приставка за оптимизиране и/или да надстроите сървъра. Системата за управление на съдържанието October също така дава възможност на програмистите да използват [`Queues`](https://octobercms.com/docs/services/queues), за да отлагат обработването на времеемки задачи, като например изпращане на имейли. Това драстично повишава скоростта на заявките в мрежата."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Бихте могли да показвате извадки в списъците с публикации (напр. чрез бутон `show more`), да намалите броя на публикациите, извеждани на дадена уеб страница, да разделите дългите публикации на няколко страници или да използвате приставка, която да забави зареждането на коментарите."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Има много [приставки](https://octobercms.com/plugins?search=css), които могат да подобрят скоростта на уебсайта чрез обединяване, минимизиране и компресиране на стиловете. Предварителното минимизиране посредством компилиране може да ускори процеса на разработване."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Има много [приставки](https://octobercms.com/plugins?search=javascript), които могат да подобрят скоростта на уебсайта чрез обединяване, минимизиране и компресиране на скриптовете. Предварителното минимизиране посредством компилиране може да ускори процеса на разработване."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Добре е да прегледате [приставките](https://octobercms.com/plugins), които зареждат неизползван CSS код в уебсайта. За да откриете тези, които добавят ненужен CSS код, стартирайте инструмента за [обхват на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Намерете проблемната тема/приставка в URL адреса на стиловия лист. Търсете приставки с много стилови листове, за които преобладава червеният цвят в диаграмата на инструмента. Дадена приставка трябва да добавя стилов лист само ако той действително се използва в уеб страницата."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Добре е да прегледате [приставките](https://octobercms.com/plugins?search=javascript), които зареждат неизползван JavaScript код в уеб страницата. За да откриете тези, които добавят ненужен JavaScript код, стартирайте инструмента за [обхват на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Намерете проблемната тема/приставка в URL адреса на скрипта. Търсете приставки с много скриптове, за които преобладава червеният цвят в диаграмата на инструмента. Дадена приставка трябва да добавя скрипт само ако той действително се използва в уеб страницата."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Прочетете как [да избегнете ненужните заявки в мрежата с помощта на HTTP кеширане](https://web.dev/http-cache/#caching-checklist). Има много [приставки](https://octobercms.com/plugins?search=Caching), които могат да се използват за ускоряване на кеширането."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Добре е да използвате [приставка за оптимизиране на изображенията](https://octobercms.com/plugins?search=image), която компресира графичните файлове, като същевременно запазва качеството им."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Качвайте изображенията директно в мултимедийния мениджър, за да разполагате с графични файлове с необходимите размери. Добре е да използвате [филтъра за преоразмеряване](https://octobercms.com/docs/markup/filter-resize) или [приставка за целта](https://octobercms.com/plugins?search=image), така че изображенията да бъдат с оптимални размери."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Активирайте компресирането на текста в конфигурацията на уеб сървъра."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Бихте могли да използвате библиотека за виртуализиране, като `react-window`, за да сведете до минимум броя създавани възли в DOM, ако на страницата се рендерират множество повтарящи се елементи. [Научете повече](https://web.dev/virtualize-long-lists-react-window/). Също така намалете ненужните повторни рендерирания чрез [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) или [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) и [пропускайте ефекти](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), докато не се променят определени зависимости, ако използвате „кукичката“ `Effect` за повишаване на ефективността при изпълнение."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Ако използвате React Router, сведете до минимум употребата на компонента `<Redirect>` за [навигация по маршрути](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Ако компоненти в React се рендерират върху сървъра, бихте могли да използвате `renderToPipeableStream()` или `renderToStaticNodeStream()`, за да дадете възможност на клиентската програма да получава и хидратира различни части от кода за маркиране, вместо целия наведнъж. [Научете повече](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Ако системата ви за компилиране автоматично минимизира CSS файловете, уверете се, че внедрявате стандартната версия на приложението си. Можете да проверите това с разширението React Developer Tools. [Научете повече](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ако системата ви за компилиране автоматично минимизира JavaScript файловете, уверете се, че внедрявате стандартната версия на приложението си. Можете да проверите това с разширението React Developer Tools. [Научете повече](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Ако не извършвате рендериране върху сървъра, [разделете пакетите си с JavaScript](https://web.dev/code-splitting-suspense/) с `React.lazy()`. В противен случай разделете кода чрез библиотека на трета страна, напр. [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Използвайте инструмента за анализ в React DevTools, който използва съответното API, за да измерите ефективността на компонентите си при рендериране. [Научете повече](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Добре е да качите GIF файла си в услуга, която ще даде възможност да бъде вграден като видеоклип с HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Добре е да използвате приставката [Performance Lab](https://wordpress.org/plugins/performance-lab/) за автоматично преобразуване на качените JPEG изображения в WebP, където това се поддържа."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Инсталирайте [приставка за WordPress за забавено зареждане](https://wordpress.org/plugins/search/lazy+load/), която дава възможност за отлагане на зареждането на изображенията извън видимата част на екрана, или преминете към тема с такава функционалност. Можете също да използвате [приставката за AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Има различни приставки за WordPress, с чиято помощ можете [да вградите важни активи](https://wordpress.org/plugins/search/critical+css/) или [да отложите зареждането на не толкова важни ресурси](https://wordpress.org/plugins/search/defer+css+javascript/). Имайте предвид, че оптимизациите, извършвани чрез тези приставки, може да възпрепятстват работата на функциите на темата ви или други приставки, така че вероятно ще се наложи да промените кода."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Времето за реакция на сървъра зависи от спецификациите му, темите и приставките. Добре е да намерите по-оптимизирана тема, внимателно да изберете приставка за оптимизиране и/или да надстроите сървъра си."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Обмислете възможността да показвате извадки в списъците си с публикации (напр. чрез маркера more), да намалите броя на публикациите, извеждани на дадена страница, да разделите дългите публикации на няколко страници или да използвате приставка, която да забави зареждането на коментарите."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Има различни [приставки за WordPress](https://wordpress.org/plugins/search/minify+css/), които могат да подобрят скоростта на сайта ви чрез обединяване, минимизиране и компресиране на стиловете. Добре е също при възможност да използвате компилиране, за да извършите това минимизиране предварително."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Има различни [приставки за WordPress](https://wordpress.org/plugins/search/minify+javascript/), които могат да подобрят скоростта на сайта ви чрез обединяване, минимизиране и компресиране на скриптовете. Добре е също при възможност да използвате компилиране, за да извършите това минимизиране предварително."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Добре е да намалите броя на [приставките за WordPress](https://wordpress.org/plugins/), които зареждат неизползван CSS код в страницата ви, или да ги замените с други. За да откриете приставките, които добавят ненужен CSS код, стартирайте инструмента за [покритие на кода](https://developer.chrome.com/docs/devtools/coverage/) в Chrome DevTools. Можете да намерите проблемната тема/приставка в URL адреса на листа със стилове. Търсете приставки с много листове със стилове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден лист със стилове трябва да бъде поставен в опашката на приставка само ако действително се използва в страницата."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Добре е да намалите броя на [приставките за WordPress](https://wordpress.org/plugins/), които зареждат неизползван JavaScript код в страницата ви, или да ги замените с други. За да откриете приставките, които добавят ненужен JS код, стартирайте инструмента за [покритие на кода](https://developer.chrome.com/docs/devtools/coverage/) в Chrome DevTools. Можете да намерите проблемната тема/приставка в URL адреса на скрипта. Търсете приставки с много скриптове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден скрипт трябва да бъде поставен в опашката на приставка само ако действително се използва в страницата."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Прочетете за [кеширането в браузъра при WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Добре е да използвате [приставка за WordPress за оптимизиране на изображенията](https://wordpress.org/plugins/search/optimize+images/), която компресира графичните ви файлове, като същевременно запазва качеството им."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Качвайте изображенията директно чрез [мултимедийната библиотека](https://wordpress.org/support/article/media-library-screen/), за да разполагате с графични файлове с необходимите размери, и след това ги вмъквайте от библиотеката или използвайте приспособлението за изображения, така че да се използват файловете с оптимални размери (включително за адаптивните гранични точки). Избягвайте използването на пълноразмерни изображения (`Full Size`), освен ако размерите са подходящи за съответното предназначение. [Научете повече](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Можете да активирате компресирането на текста в конфигурацията на уеб сървъра си."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "За да преобразувате изображенията си в WebP, активирайте Imagify от раздела в WP Rocket за оптимизиране на изображенията."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "За да изпълните тази препоръка, активирайте функцията за [забавено зареждане](https://docs.wp-rocket.me/article/1141-lazyload-for-images) в WP Rocket. Тя забавя зареждането на изображенията, докато посетителят не превърти страницата надолу дотолкова, че те действително да трябва да се покажат."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "За да изпълните тази препоръка, активирайте функцията за [премахване на неизползвания CSS код](https://docs.wp-rocket.me/article/1529-remove-unused-css) и тази за [отложено зареждане на JavaScript](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) в WP Rocket. Тези функции ще оптимизират съответно файловете със CSS и JavaScript, за да не блокират рендерирането на страницата ви."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "За да отстраните този проблем, активирайте функцията за [минимизиране на файловете със CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) в WP Rocket. Всички интервали и коментари във файловете със CSS на сайта ви ще бъдат премахнати, за да се намали файловият размер и да се ускори изтеглянето."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "За да отстраните този проблем, активирайте функцията за [минимизиране на файловете с JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) в WP Rocket. Интервалите и коментарите ще бъдат премахнати от файловете с JavaScript, за да се намали файловият размер и да се ускори изтеглянето."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "За да отстраните този проблем, активирайте функцията за [премахване на неизползвания CSS код](https://docs.wp-rocket.me/article/1529-remove-unused-css) в WP Rocket. Това намалява размера на страницата, като премахва CSS кода и стиловите листове, които не се използват, и същевременно запазва само използвания CSS код за всяка страница."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "За да отстраните този проблем, активирайте функцията за [забавяне на изпълнението на JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) в WP Rocket. Това ще ускори зареждането на страницата ви, като забави изпълнението на скриптовете, докато потребителят не взаимодейства с тях. Ако в сайта ви има вложени рамки, можете да използвате функцията на WP Rocket за [забавено зареждане на вложени рамки и видеоклипове](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos), както и тази за [замяна на вложената рамка на YouTube с изображение за визуализация](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "За да компресирате изображенията си, активирайте Imagify от раздела в WP Rocket за оптимизирането им и стартирайте функцията Bulk Optimization."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Използвайте функцията за [предварително извличане на DNS заявки](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) в WP Rocket, за да добавите dns-prefetch и да ускорите връзката с външните домейни. Освен това WP Rocket автоматично добавя preconnect към [домейна в Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts), както и към всички записи CNAME, добавени чрез функцията за [активиране на CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "За да отстраните този проблем с шрифтовете, активирайте функцията за [премахване на неизползвания CSS код](https://docs.wp-rocket.me/article/1529-remove-unused-css) в WP Rocket. Най-важните за сайта ви шрифтове ще се зареждат предварително с приоритет."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Вижте калкулатора."}, "report/renderer/report-utils.js | collapseView": {"message": "Свиване на изгледа"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Първоначална навигация"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Максимално забавяне в критичния път:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Копиране на JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Превключване на тъмната тема"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Отпечатване на пълен отчет"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Отпечатване на обобщен отчет"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Запазване като Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Запазване като HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Запазване като JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Отваряне във визуализатора"}, "report/renderer/report-utils.js | errorLabel": {"message": "Грешка!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Грешка в отчета: няма информация за проверката"}, "report/renderer/report-utils.js | expandView": {"message": "Разгъване на изгледа"}, "report/renderer/report-utils.js | footerIssue": {"message": "Добавяне на проблем"}, "report/renderer/report-utils.js | hide": {"message": "Скриване"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Данни от контролиран тест"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Анализът с [Lighthouse](https://developers.google.com/web/tools/lighthouse/) на текущата страница бе извършен през емулирана мобилна мрежа. Стойностите са приблизителни и може да варират."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Допълнителни елементи, които да проверите ръчно"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Не е приложимо"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Възможност"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Прогнозна икономия"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Успешно преминати проверки"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Първоначално зареждане на страницата"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Персонализирано ограничаване на потока на данни"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Емули<PERSON><PERSON><PERSON> компютър"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Без емулация"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Версия на Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Неограничена мощност на процесора/паметта"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Ограничаване на процесора"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Устройство"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Ограничаване на мрежата"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Емулация на екрана"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Потребителски агент (мрежа)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "При едно зареждане на страницата"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Тези данни са генерирани от едно зареждане на страницата за разлика от данните от реалното ползване, които обобщават голям брой сесии."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ограничаване до бавна 4G връзка"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Неизвестно"}, "report/renderer/report-utils.js | show": {"message": "Показване"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Показване на проверките, уместни за:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Свиване на фрагмента"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Разгъване на фрагмента"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Показване на ресурсите от трети страни"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Предоставено от средата"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Възникнаха проблеми при изготвянето на този отчет от Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Стойностите са приблизителни и може да варират. [Рейтингът за ефективността се изчислява](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) директно от тези показатели."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Преглед на първоначалното трасиране"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Преглед на трасирането"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Преглед на дървовидната карта"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Проверките бяха преминати успешно, но има предупреждения"}, "report/renderer/report-utils.js | warningHeader": {"message": "Предупреждения: "}, "treemap/app/src/util.js | allLabel": {"message": "Всички"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Всички скриптове"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Об<PERSON><PERSON><PERSON>т"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Дублиращи се модули"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Байтове на ресурсите"}, "treemap/app/src/util.js | tableColumnName": {"message": "Име"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Превключване на таблицата"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Неизползвани байтове"}}