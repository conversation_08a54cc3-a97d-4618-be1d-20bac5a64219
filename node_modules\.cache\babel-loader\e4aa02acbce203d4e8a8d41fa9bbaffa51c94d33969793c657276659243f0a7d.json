{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport { isValidElement, cloneElement } from '../../_util/reactNode';\nexport default function renderSwitcherIcon(prefixCls, switcherIcon, showLine, treeNodeProps) {\n  var isLeaf = treeNodeProps.isLeaf,\n    expanded = treeNodeProps.expanded,\n    loading = treeNodeProps.loading;\n  if (loading) {\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-loading-icon\")\n    });\n  }\n  var showLeafIcon;\n  if (showLine && _typeof(showLine) === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n  if (isLeaf) {\n    if (showLine) {\n      if (_typeof(showLine) === 'object' && !showLeafIcon) {\n        return /*#__PURE__*/React.createElement(\"span\", {\n          className: \"\".concat(prefixCls, \"-switcher-leaf-line\")\n        });\n      }\n      return /*#__PURE__*/React.createElement(FileOutlined, {\n        className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n      });\n    }\n    return null;\n  }\n  var switcherCls = \"\".concat(prefixCls, \"-switcher-icon\");\n  var switcher = typeof switcherIcon === 'function' ? switcherIcon({\n    expanded: !!expanded\n  }) : switcherIcon;\n  if (isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n  if (switcher) {\n    return switcher;\n  }\n  if (showLine) {\n    return expanded ? /*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    }) : /*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    });\n  }\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n}", "map": {"version": 3, "names": ["_typeof", "React", "classNames", "LoadingOutlined", "FileOutlined", "MinusSquareOutlined", "PlusSquareOutlined", "CaretDownFilled", "isValidElement", "cloneElement", "renderSwitcherIcon", "prefixCls", "switcherIcon", "showLine", "treeNodeProps", "<PERSON><PERSON><PERSON><PERSON>", "expanded", "loading", "createElement", "className", "concat", "showLeafIcon", "switcherCls", "switcher", "props"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/tree/utils/iconUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport { isValidElement, cloneElement } from '../../_util/reactNode';\nexport default function renderSwitcherIcon(prefixCls, switcherIcon, showLine, treeNodeProps) {\n  var isLeaf = treeNodeProps.isLeaf,\n      expanded = treeNodeProps.expanded,\n      loading = treeNodeProps.loading;\n\n  if (loading) {\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-loading-icon\")\n    });\n  }\n\n  var showLeafIcon;\n\n  if (showLine && _typeof(showLine) === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n\n  if (isLeaf) {\n    if (showLine) {\n      if (_typeof(showLine) === 'object' && !showLeafIcon) {\n        return /*#__PURE__*/React.createElement(\"span\", {\n          className: \"\".concat(prefixCls, \"-switcher-leaf-line\")\n        });\n      }\n\n      return /*#__PURE__*/React.createElement(FileOutlined, {\n        className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n      });\n    }\n\n    return null;\n  }\n\n  var switcherCls = \"\".concat(prefixCls, \"-switcher-icon\");\n  var switcher = typeof switcherIcon === 'function' ? switcherIcon({\n    expanded: !!expanded\n  }) : switcherIcon;\n\n  if (isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n\n  if (switcher) {\n    return switcher;\n  }\n\n  if (showLine) {\n    return expanded ? /*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    }) : /*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    });\n  }\n\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,mBAAmB,MAAM,gDAAgD;AAChF,OAAOC,kBAAkB,MAAM,+CAA+C;AAC9E,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,cAAc,EAAEC,YAAY,QAAQ,uBAAuB;AACpE,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EAC3F,IAAIC,MAAM,GAAGD,aAAa,CAACC,MAAM;IAC7BC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;IACjCC,OAAO,GAAGH,aAAa,CAACG,OAAO;EAEnC,IAAIA,OAAO,EAAE;IACX,OAAO,aAAahB,KAAK,CAACiB,aAAa,CAACf,eAAe,EAAE;MACvDgB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,wBAAwB;IAC1D,CAAC,CAAC;EACJ;EAEA,IAAIU,YAAY;EAEhB,IAAIR,QAAQ,IAAIb,OAAO,CAACa,QAAQ,CAAC,KAAK,QAAQ,EAAE;IAC9CQ,YAAY,GAAGR,QAAQ,CAACQ,YAAY;EACtC;EAEA,IAAIN,MAAM,EAAE;IACV,IAAIF,QAAQ,EAAE;MACZ,IAAIb,OAAO,CAACa,QAAQ,CAAC,KAAK,QAAQ,IAAI,CAACQ,YAAY,EAAE;QACnD,OAAO,aAAapB,KAAK,CAACiB,aAAa,CAAC,MAAM,EAAE;UAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;QACvD,CAAC,CAAC;MACJ;MAEA,OAAO,aAAaV,KAAK,CAACiB,aAAa,CAACd,YAAY,EAAE;QACpDe,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;MACvD,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI;EACb;EAEA,IAAIW,WAAW,GAAG,EAAE,CAACF,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC;EACxD,IAAIY,QAAQ,GAAG,OAAOX,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC;IAC/DI,QAAQ,EAAE,CAAC,CAACA;EACd,CAAC,CAAC,GAAGJ,YAAY;EAEjB,IAAIJ,cAAc,CAACe,QAAQ,CAAC,EAAE;IAC5B,OAAOd,YAAY,CAACc,QAAQ,EAAE;MAC5BJ,SAAS,EAAEjB,UAAU,CAACqB,QAAQ,CAACC,KAAK,CAACL,SAAS,IAAI,EAAE,EAAEG,WAAW;IACnE,CAAC,CAAC;EACJ;EAEA,IAAIC,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;EAEA,IAAIV,QAAQ,EAAE;IACZ,OAAOG,QAAQ,GAAG,aAAaf,KAAK,CAACiB,aAAa,CAACb,mBAAmB,EAAE;MACtEc,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;IACvD,CAAC,CAAC,GAAG,aAAaV,KAAK,CAACiB,aAAa,CAACZ,kBAAkB,EAAE;MACxDa,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;IACvD,CAAC,CAAC;EACJ;EAEA,OAAO,aAAaV,KAAK,CAACiB,aAAa,CAACX,eAAe,EAAE;IACvDY,SAAS,EAAEG;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}