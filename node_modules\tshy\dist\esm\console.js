// only print the logs if it fails, or if TSHY_VERBOSE is set
let verbose = parseInt(process.env.TSHY_VERBOSE || '0');
const errors = [];
export const error = (...a) => {
    if (verbose >= 1)
        console.error(...a);
    else
        errors.push(a);
};
export const debug = (...a) => {
    if (verbose >= 2)
        console.error(...a);
    else
        errors.push(a);
};
// we only print stdout on success anyway
export const log = (...a) => {
    if (verbose >= 1)
        console.log(...a);
};
export const print = () => {
    for (const a of errors) {
        console.error(...a);
    }
    errors.length = 0;
};
//# sourceMappingURL=console.js.map