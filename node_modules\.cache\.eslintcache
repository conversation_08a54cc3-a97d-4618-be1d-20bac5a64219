[{"C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\index.js": "1", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\App.js": "2", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\traduttore\\i18n.jsx": "3", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\PublicRoute.js": "4", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\Login.js": "5", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\PrivateRoute.js": "6", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\Home.js": "7", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\forgotPwd.jsx": "8", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\registrati.jsx": "9", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\GestionePDVAutonoma.jsx": "10", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\BackendTestButton.jsx": "11", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiListino.jsx": "12", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\store.jsx": "13", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\apireq.jsx": "14", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\associaListinoPV.jsx": "15", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiProdottiListino.jsx": "16", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaProdOrdine.jsx": "17", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocInevasi.jsx": "18", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\gestioneClientiAgente.jsx": "19", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\gestioneOrdiniAgenti.jsx": "20", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\listinoAgente.jsx": "21", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\dettagliClienti.jsx": "22", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\dettagliOrdineAgente.jsx": "23", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\icone fisse\\goToTopOfPage.jsx": "24", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\completaOrdine.jsx": "25", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\ordineDiretto.jsx": "26", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\gestioneDocumenti.jsx": "27", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\gestioneOrdiniPDV.jsx": "28", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\riepilogoOrdinePDV.jsx": "29", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestionePVendita.jsx": "30", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneAffiliati.jsx": "31", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneOPLogistica.jsx": "32", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneProdotti.jsx": "33", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneListini.jsx": "34", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\ufficioVendite.jsx": "35", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\ufficioAcquisti.jsx": "36", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneMagazzinieri.jsx": "37", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\visualizzaListiniAssociati.jsx": "38", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneClienti.jsx": "39", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneLogisticaOrdini.jsx": "40", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneMagazzini.jsx": "41", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneUtentiDistributore.jsx": "42", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneDocInevasi.jsx": "43", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneAgenti.jsx": "44", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneConsegne.jsx": "45", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneScorte.jsx": "46", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\previsioneAcquisti.jsx": "47", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\alyante.jsx": "48", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\storicoFornitori.jsx": "49", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\marketplaceUffAcq.jsx": "50", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneAutisti.jsx": "51", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneFornitori.jsx": "52", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\listiniAcquisto.jsx": "53", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\fornitoreAffiliato.jsx": "54", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\giacenzeProdotti.jsx": "55", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\gestioneScorte.jsx": "56", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\riepilogoApprovvigionamento.jsx": "57", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\magazzinoContoTerzi.jsx": "58", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\creaOrdine.jsx": "59", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\visualizzaOrdini.jsx": "60", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\visualizzaListini.jsx": "61", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\gestioneDocumenti.jsx": "62", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\approvvigionamento.jsx": "63", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\riepilogoOrdineAff.jsx": "64", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\gestionePuntiVendita.jsx": "65", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\DashboardAmministratore.jsx": "66", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\logistica\\gestioneLogistica.jsx": "67", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\autista\\gestioneConsegne.jsx": "68", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\autista\\riepilogoConsegna.jsx": "69", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\dashboardRespMagazzino.jsx": "70", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\GestioneUtenti.jsx": "71", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\confrontoDispQta.jsx": "72", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\movimentazioneInIngresso.jsx": "73", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\controlloLottiEScadenze.jsx": "74", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\composizioneMagazzino.jsx": "75", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\movimentazioneInUscita.jsx": "76", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\inventario.jsx": "77", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneLavorazioni.jsx": "78", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneOperatori.jsx": "79", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneProdotti.jsx": "80", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneProdPos.jsx": "81", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestionePuntiVendita.jsx": "82", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneProdotti.jsx": "83", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\dashboardPDV\\dashboardPDV.jsx": "84", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\dashboard\\dashboardDistributore.jsx": "85", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\dashboard\\dashboardAffiliato.jsx": "86", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\dashboardAgenti\\dashboardAgente.jsx": "87", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\listino\\listinoPDV.jsx": "88", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneScorte.jsx": "89", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneMagazzini.jsx": "90", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneClienti.jsx": "91", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\ufficioVendite.jsx": "92", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\ufficioAcquisti.jsx": "93", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\marketplaceUffAcq.jsx": "94", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneFornitori.jsx": "95", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\listiniAcquisto.jsx": "96", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneConsegne.jsx": "97", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\controlloLottiEScadenze.jsx": "98", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\dashboard\\dashboardChain.jsx": "99", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\confrontoDispQta.jsx": "100", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneAutisti.jsx": "101", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneMagazzinieri.jsx": "102", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneOPLogistica.jsx": "103", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\composizioneMagazzino.jsx": "104", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\messaggi.jsx": "105", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\newPwd.jsx": "106", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\handleError.jsx": "107", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\scaricoMagRing.jsx": "108", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\caricoMagRing.jsx": "109", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\listinoRing.jsx": "110", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\gestioneScorte.jsx": "111", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\dashboard\\dashboardRing.jsx": "112", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\accountSettings.jsx": "113", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\route.jsx": "114", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\ordinatoRing.jsx": "115", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\controlloLottiEScadenze.jsx": "116", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\composizioneMagazzino.jsx": "117", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\scaricoMagazzino.jsx": "118", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneDocumentiDiRottura.jsx": "119", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\confrontoDispQta.jsx": "120", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\GestioneCorporates.jsx": "121", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneDocumentiDiReso.jsx": "122", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\footer\\footer.jsx": "123", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\freePriceList\\freePriceList.jsx": "124", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\movimentazioni.jsx": "125", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\GeneratoreDocumenti.jsx": "126", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\traduttore\\const.jsx": "127", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\customDataTable.jsx": "128", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\navigation\\Nav.js": "129", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\utils\\index.js": "130", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\utils\\caricamento.jsx": "131", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiProdInListinoPDV.jsx": "132", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\footer\\joyride.jsx": "133", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\menuItem.jsx": "134", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\aggiunta file\\scaricaCSVProva.jsx": "135", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiPDVAff.jsx": "136", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\visualizzaDocumenti.jsx": "137", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioOrdine.jsx": "138", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\navIcon.jsx": "139", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\print\\templateOrderPrint.jsx": "140", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\reducers\\index.jsx": "141", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\marketplace\\marketplace.jsx": "142", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiPV.jsx": "143", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utentePDV.jsx": "144", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\visualizzaPDV.jsx": "145", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAffiliati.jsx": "146", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteOPLogistica.jsx": "147", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteAffiliato.jsx": "148", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiOPLogistica.jsx": "149", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiProdotti.jsx": "150", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\allineaImgProd.jsx": "151", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\associaAffiliatoAlListino.jsx": "152", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiMagazziniere.jsx": "153", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\associaListino.jsx": "154", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAnagrafica.jsx": "155", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiMagazzino.jsx": "156", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaGuiComposition.jsx": "157", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaPassword.jsx": "158", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\avviaConversazione.jsx": "159", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteAgente.jsx": "160", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAgenti.jsx": "161", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteMagazziniere.jsx": "162", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAutisti.jsx": "163", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaStato.jsx": "164", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\marketplace\\carrello.jsx": "165", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\logistica\\selezionaAutista.jsx": "166", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\selezionaOperatore.jsx": "167", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioDocumento.jsx": "168", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\aggiunta file\\aggiungiCSV.jsx": "169", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteAutista.jsx": "170", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAccordiFornitore.jsx": "171", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiFornitore.jsx": "172", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiunfiPdfAccordi.jsx": "173", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiFornitoreAffiliato.jsx": "174", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiContoTerzi.jsx": "175", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\resources\\Logos.js": "176", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiUtente.jsx": "177", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiCommposizioneMagazzino.jsx": "178", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocInventario.jsx": "179", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\posizionaProdotto.jsx": "180", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\aggiunta file\\aggiungiCSVOrdini.jsx": "181", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\legendaStatiTask.jsx": "182", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\bannerWelcome.jsx": "183", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\mappa.jsx": "184", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\tabellaOrdiniGiornalieri.jsx": "185", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\navigation\\dashboard.jsx": "186", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\stopLoading.jsx": "187", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\tabellaTopFlop.jsx": "188", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\aggiunta file\\aggiungiCSV.jsx": "189", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\aggiunta file\\scaricaCSVProva.jsx": "190", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\logo.jsx": "191", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\bannerProd2.jsx": "192", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\bannerProd.jsx": "193", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\chartHorizontal.jsx": "194", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\bannerUtili.jsx": "195", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocRottura.jsx": "196", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocReso.jsx": "197", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\dataOra.jsx": "198", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\overlayPanelGen.jsx": "199", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\menuSettings.jsx": "200", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\traduttore\\traduttore.jsx": "201", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\menuMobile.jsx": "202", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\listino\\cartIcon.jsx": "203", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioProdImg.jsx": "204", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\actions\\getAction.jsx": "205", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\marketplace\\quantitàConsigliate.jsx": "206", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\scrollToElement.jsx": "207", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\reducers\\productReducer.jsx": "208", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\uniqueElements\\onlyUniqueNameStatus.jsx": "209", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\constants.jsx": "210", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioProdUffAcquisti.jsx": "211", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\actions\\types.jsx": "212"}, {"size": 961, "mtime": 1750070341686, "results": "213", "hashOfConfig": "214"}, {"size": 33848, "mtime": 1751498833731, "results": "215", "hashOfConfig": "214"}, {"size": 1335, "mtime": 1750070355232, "results": "216", "hashOfConfig": "214"}, {"size": 442, "mtime": 1750070352282, "results": "217", "hashOfConfig": "214"}, {"size": 4121, "mtime": 1751497854454, "results": "218", "hashOfConfig": "214"}, {"size": 454, "mtime": 1750070352219, "results": "219", "hashOfConfig": "214"}, {"size": 3336, "mtime": 1751500983299, "results": "220", "hashOfConfig": "214"}, {"size": 4974, "mtime": 1750070352525, "results": "221", "hashOfConfig": "214"}, {"size": 23426, "mtime": 1752671261294, "results": "222", "hashOfConfig": "214"}, {"size": 7152, "mtime": 1751192341555, "results": "223", "hashOfConfig": "214"}, {"size": 6333, "mtime": 1751545328141, "results": "224", "hashOfConfig": "214"}, {"size": 20151, "mtime": 1751486800767, "results": "225", "hashOfConfig": "214"}, {"size": 481, "mtime": 1750070344730, "results": "226", "hashOfConfig": "214"}, {"size": 9335, "mtime": 1752682168420, "results": "227", "hashOfConfig": "214"}, {"size": 55338, "mtime": 1750070343659, "results": "228", "hashOfConfig": "214"}, {"size": 43914, "mtime": 1751486709778, "results": "229", "hashOfConfig": "214"}, {"size": 41092, "mtime": 1750070343902, "results": "230", "hashOfConfig": "214"}, {"size": 5772, "mtime": 1751487736723, "results": "231", "hashOfConfig": "214"}, {"size": 8525, "mtime": 1750767168361, "results": "232", "hashOfConfig": "214"}, {"size": 25726, "mtime": 1750070346236, "results": "233", "hashOfConfig": "214"}, {"size": 3552, "mtime": 1750070346301, "results": "234", "hashOfConfig": "214"}, {"size": 11884, "mtime": 1750070346069, "results": "235", "hashOfConfig": "214"}, {"size": 14235, "mtime": 1751486638930, "results": "236", "hashOfConfig": "214"}, {"size": 1711, "mtime": 1750084868221, "results": "237", "hashOfConfig": "214"}, {"size": 3206, "mtime": 1750070346011, "results": "238", "hashOfConfig": "214"}, {"size": 5911, "mtime": 1750070346466, "results": "239", "hashOfConfig": "214"}, {"size": 18974, "mtime": 1750070344555, "results": "240", "hashOfConfig": "214"}, {"size": 22669, "mtime": 1750070344611, "results": "241", "hashOfConfig": "214"}, {"size": 2656, "mtime": 1750070344669, "results": "242", "hashOfConfig": "214"}, {"size": 11176, "mtime": 1750070349352, "results": "243", "hashOfConfig": "214"}, {"size": 11963, "mtime": 1750070348419, "results": "244", "hashOfConfig": "214"}, {"size": 8970, "mtime": 1750070349285, "results": "245", "hashOfConfig": "214"}, {"size": 51033, "mtime": 1751487446051, "results": "246", "hashOfConfig": "214"}, {"size": 13220, "mtime": 1750070349039, "results": "247", "hashOfConfig": "214"}, {"size": 47188, "mtime": 1751491040488, "results": "248", "hashOfConfig": "214"}, {"size": 63493, "mtime": 1751491026306, "results": "249", "hashOfConfig": "214"}, {"size": 13322, "mtime": 1750070349229, "results": "250", "hashOfConfig": "214"}, {"size": 10441, "mtime": 1750070350169, "results": "251", "hashOfConfig": "214"}, {"size": 20158, "mtime": 1752670657590, "results": "252", "hashOfConfig": "214"}, {"size": 24957, "mtime": 1750070349116, "results": "253", "hashOfConfig": "214"}, {"size": 5575, "mtime": 1750070349169, "results": "254", "hashOfConfig": "214"}, {"size": 8629, "mtime": 1750070349552, "results": "255", "hashOfConfig": "214"}, {"size": 25730, "mtime": 1751486866606, "results": "256", "hashOfConfig": "214"}, {"size": 11654, "mtime": 1750070348486, "results": "257", "hashOfConfig": "214"}, {"size": 52847, "mtime": 1751487931449, "results": "258", "hashOfConfig": "214"}, {"size": 25344, "mtime": 1750070349486, "results": "259", "hashOfConfig": "214"}, {"size": 26148, "mtime": 1750070349871, "results": "260", "hashOfConfig": "214"}, {"size": 151239, "mtime": 1750070348288, "results": "261", "hashOfConfig": "214"}, {"size": 60110, "mtime": 1750070349957, "results": "262", "hashOfConfig": "214"}, {"size": 1272, "mtime": 1750070349810, "results": "263", "hashOfConfig": "214"}, {"size": 9268, "mtime": 1750070348558, "results": "264", "hashOfConfig": "214"}, {"size": 41458, "mtime": 1750070348969, "results": "265", "hashOfConfig": "214"}, {"size": 38653, "mtime": 1750070349688, "results": "266", "hashOfConfig": "214"}, {"size": 9057, "mtime": 1750070348352, "results": "267", "hashOfConfig": "214"}, {"size": 10053, "mtime": 1750070349611, "results": "268", "hashOfConfig": "214"}, {"size": 8831, "mtime": 1750070345519, "results": "269", "hashOfConfig": "214"}, {"size": 33245, "mtime": 1750070345702, "results": "270", "hashOfConfig": "214"}, {"size": 14073, "mtime": 1750070349752, "results": "271", "hashOfConfig": "214"}, {"size": 3935, "mtime": 1750070345352, "results": "272", "hashOfConfig": "214"}, {"size": 24886, "mtime": 1750070345869, "results": "273", "hashOfConfig": "214"}, {"size": 2869, "mtime": 1750070345819, "results": "274", "hashOfConfig": "214"}, {"size": 24237, "mtime": 1750070345402, "results": "275", "hashOfConfig": "214"}, {"size": 18280, "mtime": 1751486737933, "results": "276", "hashOfConfig": "214"}, {"size": 3033, "mtime": 1750070345753, "results": "277", "hashOfConfig": "214"}, {"size": 12464, "mtime": 1750070345462, "results": "278", "hashOfConfig": "214"}, {"size": 16855, "mtime": 1750070346585, "results": "279", "hashOfConfig": "214"}, {"size": 24416, "mtime": 1750070350478, "results": "280", "hashOfConfig": "214"}, {"size": 7814, "mtime": 1750070346836, "results": "281", "hashOfConfig": "214"}, {"size": 26329, "mtime": 1750070346896, "results": "282", "hashOfConfig": "214"}, {"size": 772, "mtime": 1750070350827, "results": "283", "hashOfConfig": "214"}, {"size": 7204, "mtime": 1750070346769, "results": "284", "hashOfConfig": "214"}, {"size": 25426, "mtime": 1750070350686, "results": "285", "hashOfConfig": "214"}, {"size": 29891, "mtime": 1751487969381, "results": "286", "hashOfConfig": "214"}, {"size": 25383, "mtime": 1750070350769, "results": "287", "hashOfConfig": "214"}, {"size": 19022, "mtime": 1750070350629, "results": "288", "hashOfConfig": "214"}, {"size": 29278, "mtime": 1751487498585, "results": "289", "hashOfConfig": "214"}, {"size": 28051, "mtime": 1751486766725, "results": "290", "hashOfConfig": "214"}, {"size": 20339, "mtime": 1750070350899, "results": "291", "hashOfConfig": "214"}, {"size": 5221, "mtime": 1750070350962, "results": "292", "hashOfConfig": "214"}, {"size": 19347, "mtime": 1750070351123, "results": "293", "hashOfConfig": "214"}, {"size": 24815, "mtime": 1750070351043, "results": "294", "hashOfConfig": "214"}, {"size": 13645, "mtime": 1752671414336, "results": "295", "hashOfConfig": "214"}, {"size": 25807, "mtime": 1750070347519, "results": "296", "hashOfConfig": "214"}, {"size": 2497, "mtime": 1750070345041, "results": "297", "hashOfConfig": "214"}, {"size": 2749, "mtime": 1750070350406, "results": "298", "hashOfConfig": "214"}, {"size": 2586, "mtime": 1750070345948, "results": "299", "hashOfConfig": "214"}, {"size": 3231, "mtime": 1750070346525, "results": "300", "hashOfConfig": "214"}, {"size": 6971, "mtime": 1750070345169, "results": "301", "hashOfConfig": "214"}, {"size": 11361, "mtime": 1750070347636, "results": "302", "hashOfConfig": "214"}, {"size": 5585, "mtime": 1750070347353, "results": "303", "hashOfConfig": "214"}, {"size": 18758, "mtime": 1750070347170, "results": "304", "hashOfConfig": "214"}, {"size": 51790, "mtime": 1751487638087, "results": "305", "hashOfConfig": "214"}, {"size": 55550, "mtime": 1751488058013, "results": "306", "hashOfConfig": "214"}, {"size": 1272, "mtime": 1750070347779, "results": "307", "hashOfConfig": "214"}, {"size": 24491, "mtime": 1750070347301, "results": "308", "hashOfConfig": "214"}, {"size": 37299, "mtime": 1750070347702, "results": "309", "hashOfConfig": "214"}, {"size": 47786, "mtime": 1751488095791, "results": "310", "hashOfConfig": "214"}, {"size": 24287, "mtime": 1750070347069, "results": "311", "hashOfConfig": "214"}, {"size": 1542, "mtime": 1751487223253, "results": "312", "hashOfConfig": "214"}, {"size": 22423, "mtime": 1750070347002, "results": "313", "hashOfConfig": "214"}, {"size": 9278, "mtime": 1750070347125, "results": "314", "hashOfConfig": "214"}, {"size": 13332, "mtime": 1750070347415, "results": "315", "hashOfConfig": "214"}, {"size": 8980, "mtime": 1750070347469, "results": "316", "hashOfConfig": "214"}, {"size": 19109, "mtime": 1750070346952, "results": "317", "hashOfConfig": "214"}, {"size": 1081, "mtime": 1750070346352, "results": "318", "hashOfConfig": "214"}, {"size": 9059, "mtime": 1750070352788, "results": "319", "hashOfConfig": "214"}, {"size": 1650, "mtime": 1750070352585, "results": "320", "hashOfConfig": "214"}, {"size": 40003, "mtime": 1751491488591, "results": "321", "hashOfConfig": "214"}, {"size": 43495, "mtime": 1751485644314, "results": "322", "hashOfConfig": "214"}, {"size": 3610, "mtime": 1751486614346, "results": "323", "hashOfConfig": "214"}, {"size": 17859, "mtime": 1751485681212, "results": "324", "hashOfConfig": "214"}, {"size": 933, "mtime": 1750070352008, "results": "325", "hashOfConfig": "214"}, {"size": 32284, "mtime": 1751485604051, "results": "326", "hashOfConfig": "214"}, {"size": 9023, "mtime": 1750938103814, "results": "327", "hashOfConfig": "214"}, {"size": 24109, "mtime": 1751486075638, "results": "328", "hashOfConfig": "214"}, {"size": 24204, "mtime": 1750070351638, "results": "329", "hashOfConfig": "214"}, {"size": 14423, "mtime": 1751486093241, "results": "330", "hashOfConfig": "214"}, {"size": 11111, "mtime": 1751486142272, "results": "331", "hashOfConfig": "214"}, {"size": 34889, "mtime": 1750070348912, "results": "332", "hashOfConfig": "214"}, {"size": 22372, "mtime": 1750070351569, "results": "333", "hashOfConfig": "214"}, {"size": 7562, "mtime": 1750070346719, "results": "334", "hashOfConfig": "214"}, {"size": 34887, "mtime": 1750070348823, "results": "335", "hashOfConfig": "214"}, {"size": 734, "mtime": 1750070352952, "results": "336", "hashOfConfig": "214"}, {"size": 2880, "mtime": 1750070353066, "results": "337", "hashOfConfig": "214"}, {"size": 14703, "mtime": 1750070345653, "results": "338", "hashOfConfig": "214"}, {"size": 17247, "mtime": 1750070346657, "results": "339", "hashOfConfig": "214"}, {"size": 77177, "mtime": 1750070355161, "results": "340", "hashOfConfig": "214"}, {"size": 85715, "mtime": 1751490930416, "results": "341", "hashOfConfig": "214"}, {"size": 3783, "mtime": 1751191879838, "results": "342", "hashOfConfig": "214"}, {"size": 4710, "mtime": 1751499933923, "results": "343", "hashOfConfig": "214"}, {"size": 748, "mtime": 1750070358960, "results": "344", "hashOfConfig": "214"}, {"size": 6697, "mtime": 1750070343227, "results": "345", "hashOfConfig": "214"}, {"size": 1355, "mtime": 1750070352997, "results": "346", "hashOfConfig": "214"}, {"size": 1707, "mtime": 1750070352730, "results": "347", "hashOfConfig": "214"}, {"size": 6273, "mtime": 1750070350331, "results": "348", "hashOfConfig": "214"}, {"size": 26868, "mtime": 1750770042802, "results": "349", "hashOfConfig": "214"}, {"size": 10000, "mtime": 1750070353985, "results": "350", "hashOfConfig": "214"}, {"size": 8852, "mtime": 1750070353330, "results": "351", "hashOfConfig": "214"}, {"size": 1820, "mtime": 1750070346402, "results": "352", "hashOfConfig": "214"}, {"size": 20167, "mtime": 1750070355011, "results": "353", "hashOfConfig": "214"}, {"size": 288, "mtime": 1750070344913, "results": "354", "hashOfConfig": "214"}, {"size": 174227, "mtime": 1751493874068, "results": "355", "hashOfConfig": "214"}, {"size": 4849, "mtime": 1750070343179, "results": "356", "hashOfConfig": "214"}, {"size": 15599, "mtime": 1750070344402, "results": "357", "hashOfConfig": "214"}, {"size": 6219, "mtime": 1750070344461, "results": "358", "hashOfConfig": "214"}, {"size": 3170, "mtime": 1750070342038, "results": "359", "hashOfConfig": "214"}, {"size": 12418, "mtime": 1750070344336, "results": "360", "hashOfConfig": "214"}, {"size": 12490, "mtime": 1750070344073, "results": "361", "hashOfConfig": "214"}, {"size": 10573, "mtime": 1750070343054, "results": "362", "hashOfConfig": "214"}, {"size": 24828, "mtime": 1751491669086, "results": "363", "hashOfConfig": "214"}, {"size": 4120, "mtime": 1750070343469, "results": "364", "hashOfConfig": "214"}, {"size": 5933, "mtime": 1750070343529, "results": "365", "hashOfConfig": "214"}, {"size": 11972, "mtime": 1750070342920, "results": "366", "hashOfConfig": "214"}, {"size": 5905, "mtime": 1750070343591, "results": "367", "hashOfConfig": "214"}, {"size": 29685, "mtime": 1752682629246, "results": "368", "hashOfConfig": "214"}, {"size": 4407, "mtime": 1750070342998, "results": "369", "hashOfConfig": "214"}, {"size": 12138, "mtime": 1750070343790, "results": "370", "hashOfConfig": "214"}, {"size": 6087, "mtime": 1750070343847, "results": "371", "hashOfConfig": "214"}, {"size": 8267, "mtime": 1750070343729, "results": "372", "hashOfConfig": "214"}, {"size": 12346, "mtime": 1750070344136, "results": "373", "hashOfConfig": "214"}, {"size": 3134, "mtime": 1750070342097, "results": "374", "hashOfConfig": "214"}, {"size": 13224, "mtime": 1750070344278, "results": "375", "hashOfConfig": "214"}, {"size": 10571, "mtime": 1750070342202, "results": "376", "hashOfConfig": "214"}, {"size": 10950, "mtime": 1750070343956, "results": "377", "hashOfConfig": "214"}, {"size": 139207, "mtime": 1750070354159, "results": "378", "hashOfConfig": "214"}, {"size": 7228, "mtime": 1750070350554, "results": "379", "hashOfConfig": "214"}, {"size": 12158, "mtime": 1750070351385, "results": "380", "hashOfConfig": "214"}, {"size": 11103, "mtime": 1750070353283, "results": "381", "hashOfConfig": "214"}, {"size": 18299, "mtime": 1752671525348, "results": "382", "hashOfConfig": "214"}, {"size": 12354, "mtime": 1750070344202, "results": "383", "hashOfConfig": "214"}, {"size": 9364, "mtime": 1750070341971, "results": "384", "hashOfConfig": "214"}, {"size": 22245, "mtime": 1752671310624, "results": "385", "hashOfConfig": "214"}, {"size": 4122, "mtime": 1750070341902, "results": "386", "hashOfConfig": "214"}, {"size": 4775, "mtime": 1750070342802, "results": "387", "hashOfConfig": "214"}, {"size": 4728, "mtime": 1750070342362, "results": "388", "hashOfConfig": "214"}, {"size": 1251, "mtime": 1750070358789, "results": "389", "hashOfConfig": "214"}, {"size": 22928, "mtime": 1750070343403, "results": "390", "hashOfConfig": "214"}, {"size": 4972, "mtime": 1750070342286, "results": "391", "hashOfConfig": "214"}, {"size": 4609, "mtime": 1750070342552, "results": "392", "hashOfConfig": "214"}, {"size": 12895, "mtime": 1750070344015, "results": "393", "hashOfConfig": "214"}, {"size": 4527, "mtime": 1750070350290, "results": "394", "hashOfConfig": "214"}, {"size": 7290, "mtime": 1750070353469, "results": "395", "hashOfConfig": "214"}, {"size": 2931, "mtime": 1751486587703, "results": "396", "hashOfConfig": "214"}, {"size": 5085, "mtime": 1750070345586, "results": "397", "hashOfConfig": "214"}, {"size": 3005, "mtime": 1750070353935, "results": "398", "hashOfConfig": "214"}, {"size": 12888, "mtime": 1750161792233, "results": "399", "hashOfConfig": "214"}, {"size": 222, "mtime": 1750070353886, "results": "400", "hashOfConfig": "214"}, {"size": 4914, "mtime": 1750070354660, "results": "401", "hashOfConfig": "214"}, {"size": 13579, "mtime": 1752671373217, "results": "402", "hashOfConfig": "214"}, {"size": 6273, "mtime": 1750070348119, "results": "403", "hashOfConfig": "214"}, {"size": 1407, "mtime": 1751486057501, "results": "404", "hashOfConfig": "214"}, {"size": 7169, "mtime": 1750070354467, "results": "405", "hashOfConfig": "214"}, {"size": 6977, "mtime": 1750070354402, "results": "406", "hashOfConfig": "214"}, {"size": 4348, "mtime": 1750070354596, "results": "407", "hashOfConfig": "214"}, {"size": 13329, "mtime": 1750070354536, "results": "408", "hashOfConfig": "214"}, {"size": 7550, "mtime": 1750070342688, "results": "409", "hashOfConfig": "214"}, {"size": 17797, "mtime": 1750070342628, "results": "410", "hashOfConfig": "214"}, {"size": 608, "mtime": 1750070352457, "results": "411", "hashOfConfig": "214"}, {"size": 4483, "mtime": 1750070353728, "results": "412", "hashOfConfig": "214"}, {"size": 1774, "mtime": 1750070353620, "results": "413", "hashOfConfig": "214"}, {"size": 2496, "mtime": 1750070355286, "results": "414", "hashOfConfig": "214"}, {"size": 1505, "mtime": 1750070353568, "results": "415", "hashOfConfig": "214"}, {"size": 8675, "mtime": 1751485788789, "results": "416", "hashOfConfig": "214"}, {"size": 5742, "mtime": 1750070353378, "results": "417", "hashOfConfig": "214"}, {"size": 1436, "mtime": 1750070344802, "results": "418", "hashOfConfig": "214"}, {"size": 6243, "mtime": 1750070354326, "results": "419", "hashOfConfig": "214"}, {"size": 339, "mtime": 1750070353835, "results": "420", "hashOfConfig": "214"}, {"size": 573, "mtime": 1750070344969, "results": "421", "hashOfConfig": "214"}, {"size": 742, "mtime": 1750070354731, "results": "422", "hashOfConfig": "214"}, {"size": 271, "mtime": 1750070345286, "results": "423", "hashOfConfig": "214"}, {"size": 15878, "mtime": 1750070353419, "results": "424", "hashOfConfig": "214"}, {"size": 167, "mtime": 1750070344852, "results": "425", "hashOfConfig": "214"}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dr6we4", {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\index.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\App.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\traduttore\\i18n.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\PublicRoute.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\PrivateRoute.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\Home.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\forgotPwd.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\registrati.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\GestionePDVAutonoma.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\BackendTestButton.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiListino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\store.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\apireq.jsx", ["1062", "1063", "1064"], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\associaListinoPV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiProdottiListino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaProdOrdine.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocInevasi.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\gestioneClientiAgente.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\gestioneOrdiniAgenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\listinoAgente.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\dettagliClienti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\dettagliOrdineAgente.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\icone fisse\\goToTopOfPage.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\completaOrdine.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\ordineDiretto.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\gestioneDocumenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\gestioneOrdiniPDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\riepilogoOrdinePDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestionePVendita.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneAffiliati.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneOPLogistica.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneProdotti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneListini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\ufficioVendite.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\ufficioAcquisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneMagazzinieri.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\visualizzaListiniAssociati.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneClienti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneLogisticaOrdini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneMagazzini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneUtentiDistributore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneDocInevasi.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneAgenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneConsegne.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneScorte.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\previsioneAcquisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\alyante.jsx", [], ["1065"], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\storicoFornitori.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\marketplaceUffAcq.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneAutisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneFornitori.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\listiniAcquisto.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\fornitoreAffiliato.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\giacenzeProdotti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\gestioneScorte.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\riepilogoApprovvigionamento.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\magazzinoContoTerzi.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\creaOrdine.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\visualizzaOrdini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\visualizzaListini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\gestioneDocumenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\approvvigionamento.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\riepilogoOrdineAff.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\gestionePuntiVendita.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\DashboardAmministratore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\logistica\\gestioneLogistica.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\autista\\gestioneConsegne.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\autista\\riepilogoConsegna.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\dashboardRespMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\GestioneUtenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\confrontoDispQta.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\movimentazioneInIngresso.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\controlloLottiEScadenze.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\composizioneMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\movimentazioneInUscita.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\inventario.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneLavorazioni.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneOperatori.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneProdotti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\gestioneProdPos.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestionePuntiVendita.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneProdotti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\dashboardPDV\\dashboardPDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\dashboard\\dashboardDistributore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\dashboard\\dashboardAffiliato.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\dashboardAgenti\\dashboardAgente.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\listino\\listinoPDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneScorte.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneMagazzini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneClienti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\ufficioVendite.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\ufficioAcquisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\marketplaceUffAcq.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneFornitori.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\listiniAcquisto.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneConsegne.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\controlloLottiEScadenze.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\dashboard\\dashboardChain.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\confrontoDispQta.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneAutisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneMagazzinieri.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\gestioneOPLogistica.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\composizioneMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\messaggi.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\newPwd.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\handleError.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\scaricoMagRing.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\caricoMagRing.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\listinoRing.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\gestioneScorte.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\dashboard\\dashboardRing.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\accountSettings.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\route.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\ordinatoRing.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\controlloLottiEScadenze.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\composizioneMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\scaricoMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneDocumentiDiRottura.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\ring\\confrontoDispQta.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\GestioneCorporates.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\gestioneDocumentiDiReso.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\footer\\footer.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\freePriceList\\freePriceList.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\movimentazioni.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\amministratore\\GeneratoreDocumenti.jsx", ["1066"], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\traduttore\\const.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\customDataTable.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\navigation\\Nav.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\utils\\index.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\utils\\caricamento.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiProdInListinoPDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\footer\\joyride.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\menuItem.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\aggiunta file\\scaricaCSVProva.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiPDVAff.jsx", ["1067", "1068", "1069"], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\visualizzaDocumenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioOrdine.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\agenti\\navIcon.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\print\\templateOrderPrint.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\reducers\\index.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\marketplace\\marketplace.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiPV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utentePDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\visualizzaPDV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAffiliati.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteOPLogistica.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteAffiliato.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiOPLogistica.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiProdotti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\allineaImgProd.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\associaAffiliatoAlListino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiMagazziniere.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\associaListino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAnagrafica.jsx", ["1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087"], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaGuiComposition.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaPassword.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\avviaConversazione.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteAgente.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAgenti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteMagazziniere.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAutisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\modificaStato.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\marketplace\\carrello.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\logistica\\selezionaAutista.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\respMagazzino\\selezionaOperatore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioDocumento.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\aggiunta file\\aggiungiCSV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\utenteAutista.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiAccordiFornitore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiFornitore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiunfiPdfAccordi.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiFornitoreAffiliato.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiContoTerzi.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\resources\\Logos.js", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiUtente.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiCommposizioneMagazzino.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocInventario.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\posizionaProdotto.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\distributore\\aggiunta file\\aggiungiCSVOrdini.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\legendaStatiTask.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\bannerWelcome.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\mappa.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\tabellaOrdiniGiornalieri.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\navigation\\dashboard.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\stopLoading.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\tabellaTopFlop.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\aggiunta file\\aggiungiCSV.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\chain\\aggiunta file\\scaricaCSVProva.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\logo.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\bannerProd2.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\bannerProd.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\chartHorizontal.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\statistiche\\bannerUtili.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocRottura.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\aggiunta_dati\\aggiungiDocReso.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\dataOra.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\overlayPanelGen.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\menuSettings.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\traduttore\\traduttore.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\menuMobile.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\listino\\cartIcon.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioProdImg.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\actions\\getAction.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\marketplace\\quantitàConsigliate.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\scrollToElement.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\reducers\\productReducer.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\uniqueElements\\onlyUniqueNameStatus.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\affiliato\\constants.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\components\\generalizzazioni\\dettaglioProdUffAcquisti.jsx", [], [], "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\src\\common\\PDV\\carrello\\actions\\types.jsx", [], [], {"ruleId": "1088", "severity": 1, "message": "1089", "line": 61, "column": 5, "nodeType": "1090", "messageId": "1091", "endLine": 61, "endColumn": 20}, {"ruleId": "1088", "severity": 1, "message": "1092", "line": 121, "column": 7, "nodeType": "1090", "messageId": "1091", "endLine": 121, "endColumn": 28}, {"ruleId": "1088", "severity": 1, "message": "1093", "line": 126, "column": 7, "nodeType": "1090", "messageId": "1091", "endLine": 126, "endColumn": 28}, {"ruleId": "1094", "severity": 1, "message": "1095", "line": 34, "column": 1, "nodeType": "1096", "messageId": "1097", "endLine": 34, "endColumn": 99, "suppressions": "1098"}, {"ruleId": "1088", "severity": 1, "message": "1099", "line": 15, "column": 8, "nodeType": "1090", "messageId": "1091", "endLine": 15, "endColumn": 23}, {"ruleId": "1088", "severity": 1, "message": "1100", "line": 37, "column": 11, "nodeType": "1090", "messageId": "1091", "endLine": 37, "endColumn": 18}, {"ruleId": "1101", "severity": 1, "message": "1102", "line": 104, "column": 11, "nodeType": "1103", "endLine": 232, "endColumn": 6, "suggestions": "1104"}, {"ruleId": "1101", "severity": 1, "message": "1105", "line": 246, "column": 11, "nodeType": "1103", "endLine": 308, "endColumn": 6, "suggestions": "1106"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 103, "column": 38, "nodeType": "1109", "messageId": "1110", "endLine": 103, "endColumn": 39, "suggestions": "1111"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 103, "column": 40, "nodeType": "1109", "messageId": "1110", "endLine": 103, "endColumn": 41, "suggestions": "1113"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 108, "column": 38, "nodeType": "1109", "messageId": "1110", "endLine": 108, "endColumn": 39, "suggestions": "1114"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 108, "column": 40, "nodeType": "1109", "messageId": "1110", "endLine": 108, "endColumn": 41, "suggestions": "1115"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 509, "column": 49, "nodeType": "1109", "messageId": "1110", "endLine": 509, "endColumn": 50, "suggestions": "1116"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 509, "column": 51, "nodeType": "1109", "messageId": "1110", "endLine": 509, "endColumn": 52, "suggestions": "1117"}, {"ruleId": "1107", "severity": 1, "message": "1118", "line": 509, "column": 53, "nodeType": "1109", "messageId": "1110", "endLine": 509, "endColumn": 54, "suggestions": "1119"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 512, "column": 65, "nodeType": "1109", "messageId": "1110", "endLine": 512, "endColumn": 66, "suggestions": "1120"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 512, "column": 67, "nodeType": "1109", "messageId": "1110", "endLine": 512, "endColumn": 68, "suggestions": "1121"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 515, "column": 47, "nodeType": "1109", "messageId": "1110", "endLine": 515, "endColumn": 48, "suggestions": "1122"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 515, "column": 49, "nodeType": "1109", "messageId": "1110", "endLine": 515, "endColumn": 50, "suggestions": "1123"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 529, "column": 49, "nodeType": "1109", "messageId": "1110", "endLine": 529, "endColumn": 50, "suggestions": "1124"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 529, "column": 51, "nodeType": "1109", "messageId": "1110", "endLine": 529, "endColumn": 52, "suggestions": "1125"}, {"ruleId": "1107", "severity": 1, "message": "1118", "line": 529, "column": 53, "nodeType": "1109", "messageId": "1110", "endLine": 529, "endColumn": 54, "suggestions": "1126"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 532, "column": 65, "nodeType": "1109", "messageId": "1110", "endLine": 532, "endColumn": 66, "suggestions": "1127"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 532, "column": 67, "nodeType": "1109", "messageId": "1110", "endLine": 532, "endColumn": 68, "suggestions": "1128"}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 535, "column": 47, "nodeType": "1109", "messageId": "1110", "endLine": 535, "endColumn": 48, "suggestions": "1129"}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 535, "column": 49, "nodeType": "1109", "messageId": "1110", "endLine": 535, "endColumn": 50, "suggestions": "1130"}, "no-unused-vars", "'lastHealthCheck' is assigned a value but never used.", "Identifier", "unusedVar", "'createStatusIndicator' is assigned a value but never used.", "'updateStatusIndicator' is assigned a value but never used.", "no-extend-native", "String prototype is read only, properties should not be added.", "AssignmentExpression", "unexpected", ["1131"], "'ScaricaCSVProva' is defined but never used.", "'formRef' is assigned a value but never used.", "react-hooks/exhaustive-deps", "The 'Invia' function makes the dependencies of useEffect Hook (at line 244) change on every render. To fix this, wrap the definition of 'Invia' in its own useCallback() Hook.", "VariableDeclarator", ["1132"], "The 'Invia2' function makes the dependencies of useEffect Hook (at line 320) change on every render. To fix this, wrap the definition of 'Invia2' in its own useCallback() Hook.", ["1133"], "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["1134", "1135"], "Unnecessary escape character: \\).", ["1136", "1137"], ["1138", "1139"], ["1140", "1141"], ["1142", "1143"], ["1144", "1145"], "Unnecessary escape character: \\+.", ["1146", "1147"], ["1148", "1149"], ["1150", "1151"], ["1152", "1153"], ["1154", "1155"], ["1156", "1157"], ["1158", "1159"], ["1160", "1161"], ["1162", "1163"], ["1164", "1165"], ["1166", "1167"], ["1168", "1169"], {"kind": "1170", "justification": "1171"}, {"desc": "1172", "fix": "1173"}, {"desc": "1174", "fix": "1175"}, {"messageId": "1176", "fix": "1177", "desc": "1178"}, {"messageId": "1179", "fix": "1180", "desc": "1181"}, {"messageId": "1176", "fix": "1182", "desc": "1178"}, {"messageId": "1179", "fix": "1183", "desc": "1181"}, {"messageId": "1176", "fix": "1184", "desc": "1178"}, {"messageId": "1179", "fix": "1185", "desc": "1181"}, {"messageId": "1176", "fix": "1186", "desc": "1178"}, {"messageId": "1179", "fix": "1187", "desc": "1181"}, {"messageId": "1176", "fix": "1188", "desc": "1178"}, {"messageId": "1179", "fix": "1189", "desc": "1181"}, {"messageId": "1176", "fix": "1190", "desc": "1178"}, {"messageId": "1179", "fix": "1191", "desc": "1181"}, {"messageId": "1176", "fix": "1192", "desc": "1178"}, {"messageId": "1179", "fix": "1193", "desc": "1181"}, {"messageId": "1176", "fix": "1194", "desc": "1178"}, {"messageId": "1179", "fix": "1195", "desc": "1181"}, {"messageId": "1176", "fix": "1196", "desc": "1178"}, {"messageId": "1179", "fix": "1197", "desc": "1181"}, {"messageId": "1176", "fix": "1198", "desc": "1178"}, {"messageId": "1179", "fix": "1199", "desc": "1181"}, {"messageId": "1176", "fix": "1200", "desc": "1178"}, {"messageId": "1179", "fix": "1201", "desc": "1181"}, {"messageId": "1176", "fix": "1202", "desc": "1178"}, {"messageId": "1179", "fix": "1203", "desc": "1181"}, {"messageId": "1176", "fix": "1204", "desc": "1178"}, {"messageId": "1179", "fix": "1205", "desc": "1181"}, {"messageId": "1176", "fix": "1206", "desc": "1178"}, {"messageId": "1179", "fix": "1207", "desc": "1181"}, {"messageId": "1176", "fix": "1208", "desc": "1178"}, {"messageId": "1179", "fix": "1209", "desc": "1181"}, {"messageId": "1176", "fix": "1210", "desc": "1178"}, {"messageId": "1179", "fix": "1211", "desc": "1181"}, {"messageId": "1176", "fix": "1212", "desc": "1178"}, {"messageId": "1179", "fix": "1213", "desc": "1181"}, {"messageId": "1176", "fix": "1214", "desc": "1178"}, {"messageId": "1179", "fix": "1215", "desc": "1181"}, "directive", "", "Wrap the definition of 'Invia' in its own useCallback() Hook.", {"range": "1216", "text": "1217"}, "Wrap the definition of 'Invia2' in its own useCallback() Hook.", {"range": "1218", "text": "1219"}, "removeEscape", {"range": "1220", "text": "1171"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1221", "text": "1222"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1223", "text": "1171"}, {"range": "1224", "text": "1222"}, {"range": "1225", "text": "1171"}, {"range": "1226", "text": "1222"}, {"range": "1227", "text": "1171"}, {"range": "1228", "text": "1222"}, {"range": "1229", "text": "1171"}, {"range": "1230", "text": "1222"}, {"range": "1231", "text": "1171"}, {"range": "1232", "text": "1222"}, {"range": "1233", "text": "1171"}, {"range": "1234", "text": "1222"}, {"range": "1235", "text": "1171"}, {"range": "1236", "text": "1222"}, {"range": "1237", "text": "1171"}, {"range": "1238", "text": "1222"}, {"range": "1239", "text": "1171"}, {"range": "1240", "text": "1222"}, {"range": "1241", "text": "1171"}, {"range": "1242", "text": "1222"}, {"range": "1243", "text": "1171"}, {"range": "1244", "text": "1222"}, {"range": "1245", "text": "1171"}, {"range": "1246", "text": "1222"}, {"range": "1247", "text": "1171"}, {"range": "1248", "text": "1222"}, {"range": "1249", "text": "1171"}, {"range": "1250", "text": "1222"}, {"range": "1251", "text": "1171"}, {"range": "1252", "text": "1222"}, {"range": "1253", "text": "1171"}, {"range": "1254", "text": "1222"}, {"range": "1255", "text": "1171"}, {"range": "1256", "text": "1222"}, [4610, 10490], "useCallback(async (e) => {\n        try {\n            console.log('🚀 Inizio creazione anagrafica per', userRole);\n\n            var idAff = 0\n            try {\n                idAff = JSON.parse(localStorage.getItem(\"userid\"))\n                console.log('👤 Dati utente:', idAff);\n            } catch (error) {\n                console.error('❌ Errore parsing dati utente:', error);\n                toast.current.show({\n                    severity: 'error',\n                    summary: 'Errore Sessione',\n                    detail: 'Dati di sessione corrotti. Rieffettua il login.',\n                    life: 5000\n                });\n                return;\n            }\n\n            if (!idAff) {\n                toast.current.show({\n                    severity: 'warn',\n                    summary: 'Sessione Scaduta',\n                    detail: 'Nessun dato utente trovato. Effettua il login.',\n                    life: 5000\n                });\n                return;\n            }\n\n            // Validazione semplificata - il backend gestisce l'associazione automaticamente\n            console.log('🔍 Ruolo utente:', idAff.role);\n            console.log('⚠️ Struttura completa dati utente:', JSON.stringify(idAff, null, 2));\n\n            // Verifica che il ruolo sia compatibile (AGENTE o AFFILIATO)\n            const allowedRoles = ['AGENTE', 'AFFILIATO', 'DISTRIBUTORE'];\n            if (idAff.role && !allowedRoles.includes(idAff.role)) {\n                toast.current.show({\n                    severity: 'warn',\n                    summary: 'Accesso Negato',\n                    detail: `Il ruolo \"${idAff.role}\" non è autorizzato a creare anagrafiche. Ruoli consentiti: ${allowedRoles.join(', ')}`,\n                    life: 7000\n                });\n                return;\n            }\n\n            console.log('✅ Utente autorizzato per la creazione anagrafica');\n\n            var complete = []\n            console.log('📝 Dati da inviare per registry:', corpo);\n\n            //Chiamata axios per la creazione del registry\n            await APIRequest('POST', 'registry/', corpo)\n                .then(async res => {\n                    console.log('✅ Registry creato con successo:', res.data);\n                    complete = {\n                        idRegistry: res.data.id\n                        // idAffiliate rimosso - il backend lo assegna automaticamente\n                    }\n                    console.log('🔗 Dati per creazione retailer:', complete);\n\n                    //Chiamata axios per la creazione del retailer in caso di corretta creazione registry\n                    await APIRequest('POST', 'retailers/', complete)\n                        .then(res => {\n                            console.log('✅ Retailer creato con successo:', res.data);\n                            toast.current.show({\n                                severity: 'success',\n                                summary: 'Ottimo',\n                                detail: `${entityNameCapitalized} è stato inserito con successo`,\n                                life: 3000\n                            });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.error('❌ Errore creazione retailer:', e);\n                            toast.current.show({\n                                severity: 'error',\n                                summary: 'Errore Creazione Retailer',\n                                detail: `Non è stato possibile associare il ${entityName}. Errore: ${e.response?.data?.message || e.message}`,\n                                life: 5000\n                            });\n                        })\n            }).catch(async (e) => {\n                console.log(e)\n\n                // Verifica se è un errore di P.IVA duplicata\n                if (e.response?.status === 409 || e.response?.data?.id) {\n                    // P.IVA già esistente - mostra Toast di avviso e dialog di conferma\n                    toast.current.show({\n                        severity: 'warn',\n                        summary: 'Attenzione!',\n                        detail: 'P.IVA già esistente nel sistema. Vuoi utilizzare i dati esistenti?',\n                        life: 5000\n                    });\n\n                    setCompleta({\n                        idRegistry: e.response.data?.id\n                        // idAffiliate rimosso - il backend lo assegna automaticamente\n                    })\n                    var data = e.response.data\n                    confirmDialog({\n                        message: \"L'anagrafica con questa P.IVA esiste già nel sistema. Vuoi utilizzare i dati esistenti e creare il \" + entityName + \"?\",\n                        header: Costanti.Attenzione,\n                        icon: 'pi pi-exclamation-triangle',\n                        acceptLabel: \"Sì, utilizza\",\n                        rejectLabel: \"No, annulla\",\n                        accept: (e) => confirm(e, data),\n                        reject: decline\n                    });\n                } else {\n                    // Altri errori\n                    toast.current.show({\n                        severity: 'error',\n                        summary: 'Errore',\n                        detail: `Errore nella creazione dell'anagrafica: ${e.response?.data?.message || e.message}`,\n                        life: 5000\n                    });\n                }\n            })\n        } catch (error) {\n            console.error('💥 Errore generale nella creazione:', error);\n            toast.current.show({\n                severity: 'error',\n                summary: 'Errore Imprevisto',\n                detail: 'Si è verificato un errore imprevisto. Riprova.',\n                life: 5000\n            });\n        }\n    })", [10907, 13973], "useCallback(async (e) => {\n        try {\n            console.log('🔄 Inizio aggiornamento anagrafica esistente');\n\n            var body = {\n                firstName: corpo.firstName,\n                lastName: corpo.lastName,\n                email: corpo.email,\n                tel: corpo.cellnum + '/' + corpo.telnum,\n                pIva: corpo.pIva,\n                address: corpo.address,\n                city: corpo.city,\n                cap: corpo.cap,\n                paymentMetod: corpo.paymentMetod\n            }\n            console.log('📝 Dati per aggiornamento registry:', body);\n            console.log('🔗 Dati completa per retailer:', completa);\n\n            var url = 'registry/?idRegistry=' + completa.idRegistry\n            await APIRequest('PUT', url, body)\n                .then(async res => {\n                    console.log('✅ Registry aggiornato con successo:', res.data);\n                    //Chiamata axios per la creazione del retailer sulla riuscita modifica registry\n                    await APIRequest('POST', 'retailers/', completa)\n                        .then(res => {\n                            console.log('✅ Retailer creato con successo (da anagrafica esistente):', res.data);\n                            toast.current.show({\n                                severity: 'success',\n                                summary: 'Ottimo',\n                                detail: `Anagrafica modificata e ${entityName} inserito con successo`,\n                                life: 3000\n                            });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.error('❌ Errore creazione retailer (da anagrafica esistente):', e);\n                            toast.current.show({\n                                severity: 'error',\n                                summary: 'Errore Creazione Retailer',\n                                detail: `Non è stato possibile aggiungere il ${entityName}. Errore: ${e.response?.data?.message || e.message}`,\n                                life: 5000\n                            });\n                        })\n                }).catch((e) => {\n                    console.error('❌ Errore aggiornamento registry:', e);\n                    toast.current.show({\n                        severity: 'error',\n                        summary: 'Errore Aggiornamento',\n                        detail: `Non è stato possibile aggiornare l'anagrafica. Errore: ${e.response?.data?.message || e.message}`,\n                        life: 5000\n                    });\n                })\n        } catch (error) {\n            console.error('💥 Errore generale nell\\'aggiornamento:', error);\n            toast.current.show({\n                severity: 'error',\n                summary: 'Errore Imprevisto',\n                detail: 'Si è verificato un errore imprevisto nell\\'aggiornamento. Riprova.',\n                life: 5000\n            });\n        }\n    })", [4322, 4323], [4322, 4322], "\\", [4324, 4325], [4324, 4324], [4539, 4540], [4539, 4539], [4541, 4542], [4541, 4541], [25381, 25382], [25381, 25381], [25383, 25384], [25383, 25383], [25385, 25386], [25385, 25385], [25568, 25569], [25568, 25568], [25570, 25571], [25570, 25570], [25715, 25716], [25715, 25715], [25717, 25718], [25717, 25717], [26463, 26464], [26463, 26463], [26465, 26466], [26465, 26465], [26467, 26468], [26467, 26467], [26645, 26646], [26645, 26645], [26647, 26648], [26647, 26647], [26792, 26793], [26792, 26792], [26794, 26795], [26794, 26794]]