{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\nimport * as React from 'react';\nimport { useState, useMemo, useCallback } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Item from './Item';\nimport { useBatchFrameState } from './hooks/useBatchFrameState';\nimport RawItem from './RawItem';\nexport var OverflowContext = /*#__PURE__*/React.createContext(null);\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var createUseState = useBatchFrameState();\n  var fullySSR = ssr === 'full';\n  var _createUseState = createUseState(null),\n    _createUseState2 = _slicedToArray(_createUseState, 2),\n    containerWidth = _createUseState2[0],\n    setContainerWidth = _createUseState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _createUseState3 = createUseState(new Map()),\n    _createUseState4 = _slicedToArray(_createUseState3, 2),\n    itemWidths = _createUseState4[0],\n    setItemWidths = _createUseState4[1];\n  var _createUseState5 = createUseState(0),\n    _createUseState6 = _slicedToArray(_createUseState5, 2),\n    prevRestWidth = _createUseState6[0],\n    setPrevRestWidth = _createUseState6[1];\n  var _createUseState7 = createUseState(0),\n    _createUseState8 = _slicedToArray(_createUseState7, 2),\n    restWidth = _createUseState8[0],\n    setRestWidth = _createUseState8[1];\n  var _createUseState9 = createUseState(0),\n    _createUseState10 = _slicedToArray(_createUseState9, 2),\n    suffixWidth = _createUseState10[0],\n    setSuffixWidth = _createUseState10[1];\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = React.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\"); // Always use the max width to avoid blink\n\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth); // ================================= Data =================================\n\n  var isResponsive = data.length && maxCount === RESPONSIVE;\n  var invalidate = maxCount === INVALIDATE;\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n\n  var showRest = isResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = useMemo(function () {\n    var items = data;\n    if (isResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, isResponsive]);\n  var omittedItems = useMemo(function () {\n    if (isResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, isResponsive, mergedDisplayCount]); // ================================= Item =================================\n\n  var getKey = useCallback(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = useCallback(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, notReady) {\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(count);\n    }\n  } // ================================= Size =================================\n\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  } // ================================ Effect ================================\n\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  useLayoutEffect(function () {\n    if (mergedContainerWidth && mergedRestWidth && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1; // When data count change to 0, reset this since not loop will reach\n\n      if (!len) {\n        updateDisplayCount(0);\n        setSuffixFixedStart(null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i); // Fully will always render\n\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        } // Break since data not ready\n\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, true);\n          break;\n        } // Find best match\n\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex);\n          setSuffixFixedStart(null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1);\n          setSuffixFixedStart(totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]); // ================================ Render ================================\n\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && isResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: isResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  }; // >>>>> Choice render fun by `renderRawItem`\n\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      key: key,\n      value: _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  }; // >>>>> Rest node\n\n  var restNode;\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  if (!renderRawRest) {\n    var mergedRenderRest = renderRest || defaultRenderRest;\n    restNode = /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  } else if (renderRawRest) {\n    restNode = /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      value: _objectSpread(_objectSpread({}, itemSharedProps), restContextProps)\n    }, renderRawRest(omittedItems));\n  }\n  var overflowNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  if (isResponsive) {\n    overflowNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onOverflowResize\n    }, overflowNode);\n  }\n  return overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/React.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = RawItem;\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE; // Convert to generic type\n\nexport default ForwardOverflow;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "useState", "useMemo", "useCallback", "classNames", "ResizeObserver", "useLayoutEffect", "<PERSON><PERSON>", "useBatchFrameState", "RawItem", "OverflowContext", "createContext", "RESPONSIVE", "INVALIDATE", "defaultRenderRest", "omittedItems", "concat", "length", "Overflow", "props", "ref", "_props$prefixCls", "prefixCls", "_props$data", "data", "renderItem", "renderRawItem", "itemKey", "_props$itemWidth", "itemWidth", "ssr", "style", "className", "maxCount", "renderRest", "renderRawRest", "suffix", "_props$component", "component", "Component", "itemComponent", "onVisibleChange", "restProps", "createUseState", "fullySSR", "_createUseState", "_createUseState2", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mergedContainerWidth", "_createUseState3", "Map", "_createUseState4", "itemWidths", "setItemWidths", "_createUseState5", "_createUseState6", "prevRestWidth", "setPrevRestWidth", "_createUseState7", "_createUseState8", "restWidth", "setRestWidth", "_createUseState9", "_createUseState10", "suffixWidth", "setSuffixWidth", "_useState", "_useState2", "suffixFixedStart", "setSuffixFixedStart", "_useState3", "_useState4", "displayCount", "setDisplayCount", "mergedDisplayCount", "Number", "MAX_SAFE_INTEGER", "_useState5", "_useState6", "restReady", "setRestReady", "itemPrefixCls", "mergedRestWidth", "Math", "max", "isResponsive", "invalidate", "showRest", "mergedData", "items", "slice", "min", "<PERSON><PERSON><PERSON>", "item", "index", "_ref", "mergedRenderItem", "updateDisplayCount", "count", "notReady", "onOverflowResize", "_", "element", "clientWidth", "registerSize", "key", "width", "origin", "clone", "delete", "set", "registerOverflowSize", "registerSuffixSize", "getItemWidth", "get", "totalWidth", "len", "lastIndex", "i", "currentItemWidth", "undefined", "displayRest", "suffixStyle", "position", "left", "top", "itemSharedProps", "responsive", "internalRenderItemNode", "createElement", "Provider", "value", "order", "display", "restNode", "restContextProps", "mergedRenderRest", "overflowNode", "map", "onResize", "ForwardOverflow", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-overflow/es/Overflow.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\nimport * as React from 'react';\nimport { useState, useMemo, useCallback } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Item from './Item';\nimport { useBatchFrameState } from './hooks/useBatchFrameState';\nimport RawItem from './RawItem';\nexport var OverflowContext = /*#__PURE__*/React.createContext(null);\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\n\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\n\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n      _props$data = props.data,\n      data = _props$data === void 0 ? [] : _props$data,\n      renderItem = props.renderItem,\n      renderRawItem = props.renderRawItem,\n      itemKey = props.itemKey,\n      _props$itemWidth = props.itemWidth,\n      itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n      ssr = props.ssr,\n      style = props.style,\n      className = props.className,\n      maxCount = props.maxCount,\n      renderRest = props.renderRest,\n      renderRawRest = props.renderRawRest,\n      suffix = props.suffix,\n      _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      itemComponent = props.itemComponent,\n      onVisibleChange = props.onVisibleChange,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var createUseState = useBatchFrameState();\n  var fullySSR = ssr === 'full';\n\n  var _createUseState = createUseState(null),\n      _createUseState2 = _slicedToArray(_createUseState, 2),\n      containerWidth = _createUseState2[0],\n      setContainerWidth = _createUseState2[1];\n\n  var mergedContainerWidth = containerWidth || 0;\n\n  var _createUseState3 = createUseState(new Map()),\n      _createUseState4 = _slicedToArray(_createUseState3, 2),\n      itemWidths = _createUseState4[0],\n      setItemWidths = _createUseState4[1];\n\n  var _createUseState5 = createUseState(0),\n      _createUseState6 = _slicedToArray(_createUseState5, 2),\n      prevRestWidth = _createUseState6[0],\n      setPrevRestWidth = _createUseState6[1];\n\n  var _createUseState7 = createUseState(0),\n      _createUseState8 = _slicedToArray(_createUseState7, 2),\n      restWidth = _createUseState8[0],\n      setRestWidth = _createUseState8[1];\n\n  var _createUseState9 = createUseState(0),\n      _createUseState10 = _slicedToArray(_createUseState9, 2),\n      suffixWidth = _createUseState10[0],\n      setSuffixWidth = _createUseState10[1];\n\n  var _useState = useState(null),\n      _useState2 = _slicedToArray(_useState, 2),\n      suffixFixedStart = _useState2[0],\n      setSuffixFixedStart = _useState2[1];\n\n  var _useState3 = useState(null),\n      _useState4 = _slicedToArray(_useState3, 2),\n      displayCount = _useState4[0],\n      setDisplayCount = _useState4[1];\n\n  var mergedDisplayCount = React.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n\n  var _useState5 = useState(false),\n      _useState6 = _slicedToArray(_useState5, 2),\n      restReady = _useState6[0],\n      setRestReady = _useState6[1];\n\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\"); // Always use the max width to avoid blink\n\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth); // ================================= Data =================================\n\n  var isResponsive = data.length && maxCount === RESPONSIVE;\n  var invalidate = maxCount === INVALIDATE;\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n\n  var showRest = isResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = useMemo(function () {\n    var items = data;\n\n    if (isResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, isResponsive]);\n  var omittedItems = useMemo(function () {\n    if (isResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n\n    return data.slice(mergedData.length);\n  }, [data, mergedData, isResponsive, mergedDisplayCount]); // ================================= Item =================================\n\n  var getKey = useCallback(function (item, index) {\n    var _ref;\n\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = useCallback(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n\n  function updateDisplayCount(count, notReady) {\n    setDisplayCount(count);\n\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(count);\n    }\n  } // ================================= Size =================================\n\n\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n\n      return clone;\n    });\n  }\n\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  } // ================================ Effect ================================\n\n\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n\n  useLayoutEffect(function () {\n    if (mergedContainerWidth && mergedRestWidth && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1; // When data count change to 0, reset this since not loop will reach\n\n      if (!len) {\n        updateDisplayCount(0);\n        setSuffixFixedStart(null);\n        return;\n      }\n\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i); // Fully will always render\n\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        } // Break since data not ready\n\n\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, true);\n          break;\n        } // Find best match\n\n\n        totalWidth += currentItemWidth;\n\n        if ( // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth || // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex);\n          setSuffixFixedStart(null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1);\n          setSuffixFixedStart(totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]); // ================================ Render ================================\n\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n\n  if (suffixFixedStart !== null && isResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: isResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  }; // >>>>> Choice render fun by `renderRawItem`\n\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      key: key,\n      value: _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  }; // >>>>> Rest node\n\n  var restNode;\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n\n  if (!renderRawRest) {\n    var mergedRenderRest = renderRest || defaultRenderRest;\n    restNode = /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  } else if (renderRawRest) {\n    restNode = /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      value: _objectSpread(_objectSpread({}, itemSharedProps), restContextProps)\n    }, renderRawRest(omittedItems));\n  }\n\n  var overflowNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n\n  if (isResponsive) {\n    overflowNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onOverflowResize\n    }, overflowNode);\n  }\n\n  return overflowNode;\n}\n\nvar ForwardOverflow = /*#__PURE__*/React.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = RawItem;\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE; // Convert to generic type\n\nexport default ForwardOverflow;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,iBAAiB,CAAC;AAC/N,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACtD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAO,IAAIC,eAAe,GAAG,aAAaV,KAAK,CAACW,aAAa,CAAC,IAAI,CAAC;AACnE,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,UAAU,GAAG,YAAY;AAE7B,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EACvC,OAAO,IAAI,CAACC,MAAM,CAACD,YAAY,CAACE,MAAM,EAAE,MAAM,CAAC;AACjD;AAEA,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC5B,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,gBAAgB;IAC1EE,WAAW,GAAGJ,KAAK,CAACK,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,WAAW;IAChDE,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,gBAAgB,GAAGT,KAAK,CAACU,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,GAAG,GAAGX,KAAK,CAACW,GAAG;IACfC,KAAK,GAAGZ,KAAK,CAACY,KAAK;IACnBC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,UAAU,GAAGf,KAAK,CAACe,UAAU;IAC7BC,aAAa,GAAGhB,KAAK,CAACgB,aAAa;IACnCC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,gBAAgB,GAAGlB,KAAK,CAACmB,SAAS;IAClCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEG,aAAa,GAAGrB,KAAK,CAACqB,aAAa;IACnCC,eAAe,GAAGtB,KAAK,CAACsB,eAAe;IACvCC,SAAS,GAAG5C,wBAAwB,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EAE1D,IAAI4C,cAAc,GAAGnC,kBAAkB,CAAC,CAAC;EACzC,IAAIoC,QAAQ,GAAGd,GAAG,KAAK,MAAM;EAE7B,IAAIe,eAAe,GAAGF,cAAc,CAAC,IAAI,CAAC;IACtCG,gBAAgB,GAAGjD,cAAc,CAACgD,eAAe,EAAE,CAAC,CAAC;IACrDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,oBAAoB,GAAGF,cAAc,IAAI,CAAC;EAE9C,IAAIG,gBAAgB,GAAGP,cAAc,CAAC,IAAIQ,GAAG,CAAC,CAAC,CAAC;IAC5CC,gBAAgB,GAAGvD,cAAc,CAACqD,gBAAgB,EAAE,CAAC,CAAC;IACtDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvC,IAAIG,gBAAgB,GAAGZ,cAAc,CAAC,CAAC,CAAC;IACpCa,gBAAgB,GAAG3D,cAAc,CAAC0D,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE1C,IAAIG,gBAAgB,GAAGhB,cAAc,CAAC,CAAC,CAAC;IACpCiB,gBAAgB,GAAG/D,cAAc,CAAC8D,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,gBAAgB,GAAGpB,cAAc,CAAC,CAAC,CAAC;IACpCqB,iBAAiB,GAAGnE,cAAc,CAACkE,gBAAgB,EAAE,CAAC,CAAC;IACvDE,WAAW,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAClCE,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAEzC,IAAIG,SAAS,GAAGlE,QAAQ,CAAC,IAAI,CAAC;IAC1BmE,UAAU,GAAGvE,cAAc,CAACsE,SAAS,EAAE,CAAC,CAAC;IACzCE,gBAAgB,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChCE,mBAAmB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEvC,IAAIG,UAAU,GAAGtE,QAAQ,CAAC,IAAI,CAAC;IAC3BuE,UAAU,GAAG3E,cAAc,CAAC0E,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,kBAAkB,GAAG3E,KAAK,CAACE,OAAO,CAAC,YAAY;IACjD,IAAIuE,YAAY,KAAK,IAAI,IAAI7B,QAAQ,EAAE;MACrC,OAAOgC,MAAM,CAACC,gBAAgB;IAChC;IAEA,OAAOJ,YAAY,IAAI,CAAC;EAC1B,CAAC,EAAE,CAACA,YAAY,EAAE1B,cAAc,CAAC,CAAC;EAElC,IAAI+B,UAAU,GAAG7E,QAAQ,CAAC,KAAK,CAAC;IAC5B8E,UAAU,GAAGlF,cAAc,CAACiF,UAAU,EAAE,CAAC,CAAC;IAC1CE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAIG,aAAa,GAAG,EAAE,CAAClE,MAAM,CAACM,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;;EAEnD,IAAI6D,eAAe,GAAGC,IAAI,CAACC,GAAG,CAAC5B,aAAa,EAAEI,SAAS,CAAC,CAAC,CAAC;;EAE1D,IAAIyB,YAAY,GAAG9D,IAAI,CAACP,MAAM,IAAIgB,QAAQ,KAAKrB,UAAU;EACzD,IAAI2E,UAAU,GAAGtD,QAAQ,KAAKpB,UAAU;EACxC;AACF;AACA;;EAEE,IAAI2E,QAAQ,GAAGF,YAAY,IAAI,OAAOrD,QAAQ,KAAK,QAAQ,IAAIT,IAAI,CAACP,MAAM,GAAGgB,QAAQ;EACrF,IAAIwD,UAAU,GAAGvF,OAAO,CAAC,YAAY;IACnC,IAAIwF,KAAK,GAAGlE,IAAI;IAEhB,IAAI8D,YAAY,EAAE;MAChB,IAAIvC,cAAc,KAAK,IAAI,IAAIH,QAAQ,EAAE;QACvC8C,KAAK,GAAGlE,IAAI;MACd,CAAC,MAAM;QACLkE,KAAK,GAAGlE,IAAI,CAACmE,KAAK,CAAC,CAAC,EAAEP,IAAI,CAACQ,GAAG,CAACpE,IAAI,CAACP,MAAM,EAAEgC,oBAAoB,GAAGpB,SAAS,CAAC,CAAC;MAChF;IACF,CAAC,MAAM,IAAI,OAAOI,QAAQ,KAAK,QAAQ,EAAE;MACvCyD,KAAK,GAAGlE,IAAI,CAACmE,KAAK,CAAC,CAAC,EAAE1D,QAAQ,CAAC;IACjC;IAEA,OAAOyD,KAAK;EACd,CAAC,EAAE,CAAClE,IAAI,EAAEK,SAAS,EAAEkB,cAAc,EAAEd,QAAQ,EAAEqD,YAAY,CAAC,CAAC;EAC7D,IAAIvE,YAAY,GAAGb,OAAO,CAAC,YAAY;IACrC,IAAIoF,YAAY,EAAE;MAChB,OAAO9D,IAAI,CAACmE,KAAK,CAAChB,kBAAkB,GAAG,CAAC,CAAC;IAC3C;IAEA,OAAOnD,IAAI,CAACmE,KAAK,CAACF,UAAU,CAACxE,MAAM,CAAC;EACtC,CAAC,EAAE,CAACO,IAAI,EAAEiE,UAAU,EAAEH,YAAY,EAAEX,kBAAkB,CAAC,CAAC,CAAC,CAAC;;EAE1D,IAAIkB,MAAM,GAAG1F,WAAW,CAAC,UAAU2F,IAAI,EAAEC,KAAK,EAAE;IAC9C,IAAIC,IAAI;IAER,IAAI,OAAOrE,OAAO,KAAK,UAAU,EAAE;MACjC,OAAOA,OAAO,CAACmE,IAAI,CAAC;IACtB;IAEA,OAAO,CAACE,IAAI,GAAGrE,OAAO,KAAKmE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACnE,OAAO,CAAC,CAAC,MAAM,IAAI,IAAIqE,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGD,KAAK;EACnI,CAAC,EAAE,CAACpE,OAAO,CAAC,CAAC;EACb,IAAIsE,gBAAgB,GAAG9F,WAAW,CAACsB,UAAU,IAAI,UAAUqE,IAAI,EAAE;IAC/D,OAAOA,IAAI;EACb,CAAC,EAAE,CAACrE,UAAU,CAAC,CAAC;EAEhB,SAASyE,kBAAkBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3C1B,eAAe,CAACyB,KAAK,CAAC;IAEtB,IAAI,CAACC,QAAQ,EAAE;MACbnB,YAAY,CAACkB,KAAK,GAAG3E,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;MACrCwB,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC0D,KAAK,CAAC;IAC1F;EACF,CAAC,CAAC;;EAGF,SAASE,gBAAgBA,CAACC,CAAC,EAAEC,OAAO,EAAE;IACpCvD,iBAAiB,CAACuD,OAAO,CAACC,WAAW,CAAC;EACxC;EAEA,SAASC,YAAYA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAChCrD,aAAa,CAAC,UAAUsD,MAAM,EAAE;MAC9B,IAAIC,KAAK,GAAG,IAAI1D,GAAG,CAACyD,MAAM,CAAC;MAE3B,IAAID,KAAK,KAAK,IAAI,EAAE;QAClBE,KAAK,CAACC,MAAM,CAACJ,GAAG,CAAC;MACnB,CAAC,MAAM;QACLG,KAAK,CAACE,GAAG,CAACL,GAAG,EAAEC,KAAK,CAAC;MACvB;MAEA,OAAOE,KAAK;IACd,CAAC,CAAC;EACJ;EAEA,SAASG,oBAAoBA,CAACV,CAAC,EAAEK,KAAK,EAAE;IACtC7C,YAAY,CAAC6C,KAAK,CAAC;IACnBjD,gBAAgB,CAACG,SAAS,CAAC;EAC7B;EAEA,SAASoD,kBAAkBA,CAACX,CAAC,EAAEK,KAAK,EAAE;IACpCzC,cAAc,CAACyC,KAAK,CAAC;EACvB,CAAC,CAAC;;EAGF,SAASO,YAAYA,CAACnB,KAAK,EAAE;IAC3B,OAAO1C,UAAU,CAAC8D,GAAG,CAACtB,MAAM,CAACJ,UAAU,CAACM,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;EACzD;EAEAzF,eAAe,CAAC,YAAY;IAC1B,IAAI2C,oBAAoB,IAAIkC,eAAe,IAAIM,UAAU,EAAE;MACzD,IAAI2B,UAAU,GAAGnD,WAAW;MAC5B,IAAIoD,GAAG,GAAG5B,UAAU,CAACxE,MAAM;MAC3B,IAAIqG,SAAS,GAAGD,GAAG,GAAG,CAAC,CAAC,CAAC;;MAEzB,IAAI,CAACA,GAAG,EAAE;QACRnB,kBAAkB,CAAC,CAAC,CAAC;QACrB5B,mBAAmB,CAAC,IAAI,CAAC;QACzB;MACF;MAEA,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAIC,gBAAgB,GAAGN,YAAY,CAACK,CAAC,CAAC,CAAC,CAAC;;QAExC,IAAI3E,QAAQ,EAAE;UACZ4E,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC;QAC1C,CAAC,CAAC;;QAGF,IAAIA,gBAAgB,KAAKC,SAAS,EAAE;UAClCvB,kBAAkB,CAACqB,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;UAC/B;QACF,CAAC,CAAC;;QAGFH,UAAU,IAAII,gBAAgB;QAE9B;QAAK;QACLF,SAAS,KAAK,CAAC,IAAIF,UAAU,IAAInE,oBAAoB;QAAI;QACzDsE,CAAC,KAAKD,SAAS,GAAG,CAAC,IAAIF,UAAU,GAAGF,YAAY,CAACI,SAAS,CAAC,IAAIrE,oBAAoB,EAAE;UACnF;UACAiD,kBAAkB,CAACoB,SAAS,CAAC;UAC7BhD,mBAAmB,CAAC,IAAI,CAAC;UACzB;QACF,CAAC,MAAM,IAAI8C,UAAU,GAAGjC,eAAe,GAAGlC,oBAAoB,EAAE;UAC9D;UACAiD,kBAAkB,CAACqB,CAAC,GAAG,CAAC,CAAC;UACzBjD,mBAAmB,CAAC8C,UAAU,GAAGI,gBAAgB,GAAGvD,WAAW,GAAGJ,SAAS,CAAC;UAC5E;QACF;MACF;MAEA,IAAIzB,MAAM,IAAI8E,YAAY,CAAC,CAAC,CAAC,GAAGjD,WAAW,GAAGhB,oBAAoB,EAAE;QAClEqB,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACrB,oBAAoB,EAAEI,UAAU,EAAEQ,SAAS,EAAEI,WAAW,EAAE4B,MAAM,EAAEJ,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEpF,IAAIiC,WAAW,GAAG1C,SAAS,IAAI,CAAC,CAACjE,YAAY,CAACE,MAAM;EACpD,IAAI0G,WAAW,GAAG,CAAC,CAAC;EAEpB,IAAItD,gBAAgB,KAAK,IAAI,IAAIiB,YAAY,EAAE;IAC7CqC,WAAW,GAAG;MACZC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAExD,gBAAgB;MACtByD,GAAG,EAAE;IACP,CAAC;EACH;EAEA,IAAIC,eAAe,GAAG;IACpBzG,SAAS,EAAE4D,aAAa;IACxB8C,UAAU,EAAE1C,YAAY;IACxBhD,SAAS,EAAEE,aAAa;IACxB+C,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC;;EAEH,IAAI0C,sBAAsB,GAAGvG,aAAa,GAAG,UAAUoE,IAAI,EAAEC,KAAK,EAAE;IAClE,IAAIW,GAAG,GAAGb,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC7B,OAAO,aAAa/F,KAAK,CAACkI,aAAa,CAACxH,eAAe,CAACyH,QAAQ,EAAE;MAChEzB,GAAG,EAAEA,GAAG;MACR0B,KAAK,EAAExI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmI,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DM,KAAK,EAAEtC,KAAK;QACZD,IAAI,EAAEA,IAAI;QACVnE,OAAO,EAAE+E,GAAG;QACZD,YAAY,EAAEA,YAAY;QAC1B6B,OAAO,EAAEvC,KAAK,IAAIpB;MACpB,CAAC;IACH,CAAC,EAAEjD,aAAa,CAACoE,IAAI,EAAEC,KAAK,CAAC,CAAC;EAChC,CAAC,GAAG,UAAUD,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIW,GAAG,GAAGb,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC7B,OAAO,aAAa/F,KAAK,CAACkI,aAAa,CAAC3H,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEoI,eAAe,EAAE;MAC1EM,KAAK,EAAEtC,KAAK;MACZW,GAAG,EAAEA,GAAG;MACRZ,IAAI,EAAEA,IAAI;MACVrE,UAAU,EAAEwE,gBAAgB;MAC5BtE,OAAO,EAAE+E,GAAG;MACZD,YAAY,EAAEA,YAAY;MAC1B6B,OAAO,EAAEvC,KAAK,IAAIpB;IACpB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;;EAEH,IAAI4D,QAAQ;EACZ,IAAIC,gBAAgB,GAAG;IACrBH,KAAK,EAAEX,WAAW,GAAG/C,kBAAkB,GAAGC,MAAM,CAACC,gBAAgB;IACjE7C,SAAS,EAAE,EAAE,CAAChB,MAAM,CAACkE,aAAa,EAAE,OAAO,CAAC;IAC5CuB,YAAY,EAAEO,oBAAoB;IAClCsB,OAAO,EAAEZ;EACX,CAAC;EAED,IAAI,CAACvF,aAAa,EAAE;IAClB,IAAIsG,gBAAgB,GAAGvG,UAAU,IAAIpB,iBAAiB;IACtDyH,QAAQ,GAAG,aAAavI,KAAK,CAACkI,aAAa,CAAC3H,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEoI,eAAe,EAAES,gBAAgB,CAAC,EAAE,OAAOC,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAAC1H,YAAY,CAAC,GAAG0H,gBAAgB,CAAC;EAChM,CAAC,MAAM,IAAItG,aAAa,EAAE;IACxBoG,QAAQ,GAAG,aAAavI,KAAK,CAACkI,aAAa,CAACxH,eAAe,CAACyH,QAAQ,EAAE;MACpEC,KAAK,EAAExI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmI,eAAe,CAAC,EAAES,gBAAgB;IAC3E,CAAC,EAAErG,aAAa,CAACpB,YAAY,CAAC,CAAC;EACjC;EAEA,IAAI2H,YAAY,GAAG,aAAa1I,KAAK,CAACkI,aAAa,CAAC3F,SAAS,EAAE5C,QAAQ,CAAC;IACtEqC,SAAS,EAAE5B,UAAU,CAAC,CAACmF,UAAU,IAAIjE,SAAS,EAAEU,SAAS,CAAC;IAC1DD,KAAK,EAAEA,KAAK;IACZX,GAAG,EAAEA;EACP,CAAC,EAAEsB,SAAS,CAAC,EAAE+C,UAAU,CAACkD,GAAG,CAACV,sBAAsB,CAAC,EAAEzC,QAAQ,GAAG+C,QAAQ,GAAG,IAAI,EAAEnG,MAAM,IAAI,aAAapC,KAAK,CAACkI,aAAa,CAAC3H,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEoI,eAAe,EAAE;IAChKM,KAAK,EAAE1D,kBAAkB;IACzB3C,SAAS,EAAE,EAAE,CAAChB,MAAM,CAACkE,aAAa,EAAE,SAAS,CAAC;IAC9CuB,YAAY,EAAEQ,kBAAkB;IAChCqB,OAAO,EAAE,IAAI;IACbvG,KAAK,EAAE4F;EACT,CAAC,CAAC,EAAEvF,MAAM,CAAC,CAAC;EAEZ,IAAIkD,YAAY,EAAE;IAChBoD,YAAY,GAAG,aAAa1I,KAAK,CAACkI,aAAa,CAAC7H,cAAc,EAAE;MAC9DuI,QAAQ,EAAEvC;IACZ,CAAC,EAAEqC,YAAY,CAAC;EAClB;EAEA,OAAOA,YAAY;AACrB;AAEA,IAAIG,eAAe,GAAG,aAAa7I,KAAK,CAAC8I,UAAU,CAAC5H,QAAQ,CAAC;AAC7D2H,eAAe,CAACE,WAAW,GAAG,UAAU;AACxCF,eAAe,CAACtI,IAAI,GAAGE,OAAO;AAC9BoI,eAAe,CAACjI,UAAU,GAAGA,UAAU;AACvCiI,eAAe,CAAChI,UAAU,GAAGA,UAAU,CAAC,CAAC;;AAEzC,eAAegI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}