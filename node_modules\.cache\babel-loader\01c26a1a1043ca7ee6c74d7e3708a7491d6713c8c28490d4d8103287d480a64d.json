{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport defaultLocaleData from './default';\nimport LocaleContext from './context';\nvar LocaleReceiver = /*#__PURE__*/function (_React$Component) {\n  _inherits(LocaleReceiver, _React$Component);\n  var _super = _createSuper(LocaleReceiver);\n  function LocaleReceiver() {\n    _classCallCheck(this, LocaleReceiver);\n    return _super.apply(this, arguments);\n  }\n  _createClass(LocaleReceiver, [{\n    key: \"getLocale\",\n    value: function getLocale() {\n      var _this$props = this.props,\n        componentName = _this$props.componentName,\n        defaultLocale = _this$props.defaultLocale;\n      var locale = defaultLocale || defaultLocaleData[componentName !== null && componentName !== void 0 ? componentName : 'global'];\n      var antLocale = this.context;\n      var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n      return _extends(_extends({}, locale instanceof Function ? locale() : locale), localeFromContext || {});\n    }\n  }, {\n    key: \"getLocaleCode\",\n    value: function getLocaleCode() {\n      var antLocale = this.context;\n      var localeCode = antLocale && antLocale.locale; // Had use LocaleProvide but didn't set locale\n\n      if (antLocale && antLocale.exist && !localeCode) {\n        return defaultLocaleData.locale;\n      }\n      return localeCode;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return this.props.children(this.getLocale(), this.getLocaleCode(), this.context);\n    }\n  }]);\n  return LocaleReceiver;\n}(React.Component);\nexport { LocaleReceiver as default };\nLocaleReceiver.defaultProps = {\n  componentName: 'global'\n};\nLocaleReceiver.contextType = LocaleContext;\nexport function useLocaleReceiver(componentName, defaultLocale) {\n  var antLocale = React.useContext(LocaleContext);\n  var componentLocale = React.useMemo(function () {\n    var locale = defaultLocale || defaultLocaleData[componentName || 'global'];\n    var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n    return _extends(_extends({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  return [componentLocale];\n}", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "defaultLocaleData", "LocaleContext", "LocaleReceiver", "_React$Component", "_super", "apply", "arguments", "key", "value", "getLocale", "_this$props", "props", "componentName", "defaultLocale", "locale", "antLocale", "context", "localeFromContext", "Function", "getLocaleCode", "localeCode", "exist", "render", "children", "Component", "default", "defaultProps", "contextType", "useLocaleReceiver", "useContext", "componentLocale", "useMemo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/locale-provider/LocaleReceiver.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport defaultLocaleData from './default';\nimport LocaleContext from './context';\n\nvar LocaleReceiver = /*#__PURE__*/function (_React$Component) {\n  _inherits(LocaleReceiver, _React$Component);\n\n  var _super = _createSuper(LocaleReceiver);\n\n  function LocaleReceiver() {\n    _classCallCheck(this, LocaleReceiver);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(LocaleReceiver, [{\n    key: \"getLocale\",\n    value: function getLocale() {\n      var _this$props = this.props,\n          componentName = _this$props.componentName,\n          defaultLocale = _this$props.defaultLocale;\n      var locale = defaultLocale || defaultLocaleData[componentName !== null && componentName !== void 0 ? componentName : 'global'];\n      var antLocale = this.context;\n      var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n      return _extends(_extends({}, locale instanceof Function ? locale() : locale), localeFromContext || {});\n    }\n  }, {\n    key: \"getLocaleCode\",\n    value: function getLocaleCode() {\n      var antLocale = this.context;\n      var localeCode = antLocale && antLocale.locale; // Had use LocaleProvide but didn't set locale\n\n      if (antLocale && antLocale.exist && !localeCode) {\n        return defaultLocaleData.locale;\n      }\n\n      return localeCode;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return this.props.children(this.getLocale(), this.getLocaleCode(), this.context);\n    }\n  }]);\n\n  return LocaleReceiver;\n}(React.Component);\n\nexport { LocaleReceiver as default };\nLocaleReceiver.defaultProps = {\n  componentName: 'global'\n};\nLocaleReceiver.contextType = LocaleContext;\nexport function useLocaleReceiver(componentName, defaultLocale) {\n  var antLocale = React.useContext(LocaleContext);\n  var componentLocale = React.useMemo(function () {\n    var locale = defaultLocale || defaultLocaleData[componentName || 'global'];\n    var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n    return _extends(_extends({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  return [componentLocale];\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,WAAW;AACzC,OAAOC,aAAa,MAAM,WAAW;AAErC,IAAIC,cAAc,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC5DN,SAAS,CAACK,cAAc,EAAEC,gBAAgB,CAAC;EAE3C,IAAIC,MAAM,GAAGN,YAAY,CAACI,cAAc,CAAC;EAEzC,SAASA,cAAcA,CAAA,EAAG;IACxBP,eAAe,CAAC,IAAI,EAAEO,cAAc,CAAC;IAErC,OAAOE,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtC;EAEAV,YAAY,CAACM,cAAc,EAAE,CAAC;IAC5BK,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASC,SAASA,CAAA,EAAG;MAC1B,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QACxBC,aAAa,GAAGF,WAAW,CAACE,aAAa;QACzCC,aAAa,GAAGH,WAAW,CAACG,aAAa;MAC7C,IAAIC,MAAM,GAAGD,aAAa,IAAIb,iBAAiB,CAACY,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,QAAQ,CAAC;MAC9H,IAAIG,SAAS,GAAG,IAAI,CAACC,OAAO;MAC5B,IAAIC,iBAAiB,GAAGL,aAAa,IAAIG,SAAS,GAAGA,SAAS,CAACH,aAAa,CAAC,GAAG,CAAC,CAAC;MAClF,OAAOlB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoB,MAAM,YAAYI,QAAQ,GAAGJ,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEG,iBAAiB,IAAI,CAAC,CAAC,CAAC;IACxG;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,SAASW,aAAaA,CAAA,EAAG;MAC9B,IAAIJ,SAAS,GAAG,IAAI,CAACC,OAAO;MAC5B,IAAII,UAAU,GAAGL,SAAS,IAAIA,SAAS,CAACD,MAAM,CAAC,CAAC;;MAEhD,IAAIC,SAAS,IAAIA,SAAS,CAACM,KAAK,IAAI,CAACD,UAAU,EAAE;QAC/C,OAAOpB,iBAAiB,CAACc,MAAM;MACjC;MAEA,OAAOM,UAAU;IACnB;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASc,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACX,KAAK,CAACY,QAAQ,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC,EAAE,IAAI,CAACU,aAAa,CAAC,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;IAClF;EACF,CAAC,CAAC,CAAC;EAEH,OAAOd,cAAc;AACvB,CAAC,CAACH,KAAK,CAACyB,SAAS,CAAC;AAElB,SAAStB,cAAc,IAAIuB,OAAO;AAClCvB,cAAc,CAACwB,YAAY,GAAG;EAC5Bd,aAAa,EAAE;AACjB,CAAC;AACDV,cAAc,CAACyB,WAAW,GAAG1B,aAAa;AAC1C,OAAO,SAAS2B,iBAAiBA,CAAChB,aAAa,EAAEC,aAAa,EAAE;EAC9D,IAAIE,SAAS,GAAGhB,KAAK,CAAC8B,UAAU,CAAC5B,aAAa,CAAC;EAC/C,IAAI6B,eAAe,GAAG/B,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC9C,IAAIjB,MAAM,GAAGD,aAAa,IAAIb,iBAAiB,CAACY,aAAa,IAAI,QAAQ,CAAC;IAC1E,IAAIK,iBAAiB,GAAGL,aAAa,IAAIG,SAAS,GAAGA,SAAS,CAACH,aAAa,CAAC,GAAG,CAAC,CAAC;IAClF,OAAOlB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAOoB,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEG,iBAAiB,IAAI,CAAC,CAAC,CAAC;EAC1G,CAAC,EAAE,CAACL,aAAa,EAAEC,aAAa,EAAEE,SAAS,CAAC,CAAC;EAC7C,OAAO,CAACe,eAAe,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}