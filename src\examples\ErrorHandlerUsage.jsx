/**
 * Esempio di utilizzo dell'ErrorHandler nei componenti
 * Questo file mostra come integrare la gestione degli errori migliorata
 */

import React, { useState, useRef } from 'react';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { APIRequest } from '../components/generalizzazioni/apireq';
import ErrorHandler from '../utils/errorHandler';

const ExampleComponent = () => {
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        pIva: ''
    });
    const [errors, setErrors] = useState({});
    const toast = useRef(null);

    // Validazione frontend usando ErrorHandler
    const validateForm = () => {
        const newErrors = {};
        
        // Validazione campi obbligatori
        const firstNameError = ErrorHandler.validators.required(formData.firstName, 'Nome');
        if (firstNameError) newErrors.firstName = firstNameError;
        
        const lastNameError = ErrorHandler.validators.required(formData.lastName, 'Cognome');
        if (lastNameError) newErrors.lastName = lastNameError;
        
        const pivaError = ErrorHandler.validators.required(formData.pIva, 'Partita IVA') ||
                         ErrorHandler.validators.partitaIva(formData.pIva);
        if (pivaError) newErrors.pIva = pivaError;
        
        // Validazione email (opzionale ma se presente deve essere valida)
        if (formData.email) {
            const emailError = ErrorHandler.validators.email(formData.email);
            if (emailError) newErrors.email = emailError;
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async () => {
        // Validazione frontend
        if (!validateForm()) {
            toast.current.show({
                severity: 'warn',
                summary: '⚠️ Dati non validi',
                detail: 'Correggere i campi evidenziati in rosso',
                life: 4000
            });
            return;
        }

        try {
            // Chiamata API
            const response = await APIRequest('POST', 'registry/', formData);
            
            // Successo
            toast.current.show({
                severity: 'success',
                summary: '✅ Successo',
                detail: 'Anagrafica creata con successo',
                life: 3000
            });
            
            // Reset form
            setFormData({ firstName: '', lastName: '', email: '', pIva: '' });
            setErrors({});
            
        } catch (error) {
            // Gestione errori usando ErrorHandler
            ErrorHandler.showError(toast, error, 'creazione anagrafica');
        }
    };

    return (
        <div className="p-4">
            <Toast ref={toast} />
            
            <h3>Esempio Gestione Errori Migliorata</h3>
            
            <div className="p-grid p-fluid">
                <div className="p-col-12 p-md-6">
                    <label>Nome *</label>
                    <InputText
                        value={formData.firstName}
                        onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                        className={errors.firstName ? 'p-invalid' : ''}
                        placeholder="Inserisci nome"
                    />
                    {errors.firstName && <small className="p-error">{errors.firstName}</small>}
                </div>
                
                <div className="p-col-12 p-md-6">
                    <label>Cognome *</label>
                    <InputText
                        value={formData.lastName}
                        onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                        className={errors.lastName ? 'p-invalid' : ''}
                        placeholder="Inserisci cognome"
                    />
                    {errors.lastName && <small className="p-error">{errors.lastName}</small>}
                </div>
                
                <div className="p-col-12 p-md-6">
                    <label>Email</label>
                    <InputText
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className={errors.email ? 'p-invalid' : ''}
                        placeholder="Inserisci email"
                    />
                    {errors.email && <small className="p-error">{errors.email}</small>}
                </div>
                
                <div className="p-col-12 p-md-6">
                    <label>Partita IVA *</label>
                    <InputText
                        value={formData.pIva}
                        onChange={(e) => setFormData({...formData, pIva: e.target.value})}
                        className={errors.pIva ? 'p-invalid' : ''}
                        placeholder="11 cifre"
                        maxLength={11}
                    />
                    {errors.pIva && <small className="p-error">{errors.pIva}</small>}
                </div>
                
                <div className="p-col-12">
                    <Button 
                        label="Salva Anagrafica" 
                        onClick={handleSubmit}
                        className="p-button-primary"
                    />
                </div>
            </div>
        </div>
    );
};

export default ExampleComponent;

/**
 * COME INTEGRARE ERRORHANDLER IN COMPONENTI ESISTENTI:
 * 
 * 1. Importare ErrorHandler:
 *    import ErrorHandler from '../utils/errorHandler';
 * 
 * 2. Sostituire il catch generico:
 *    .catch((e) => {
 *        ErrorHandler.showError(toast, e, 'operazione specifica');
 *    })
 * 
 * 3. Utilizzare i validatori:
 *    const errors = [];
 *    const nameError = ErrorHandler.validators.required(name, 'Nome');
 *    if (nameError) errors.push(nameError);
 * 
 * 4. Per errori personalizzati:
 *    const errorInfo = ErrorHandler.parseError(error, 'contesto');
 *    toast.current.show(errorInfo);
 */
