{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\ring\\\\caricoMagRing.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioAcquisti - operazioni sull'ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Button } from 'primereact/button';\nimport { Dialog } from 'primereact/dialog';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { InputText } from 'primereact/inputtext';\nimport { distributore, ringControlloLottiEScadenze } from '../../components/route';\nimport { Toast } from 'primereact/toast';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass CaricoMagRing extends Component {\n  constructor(props) {\n    var _JSON$parse$warehouse, _JSON$parse$warehouse2;\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    this.onRowSelect = result => {\n      var idFOROrd = [];\n      this.state.results.forEach(element => {\n        if (element.id === result.id) {\n          idFOROrd = element;\n        }\n      });\n      localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n      window.location.pathname = ringControlloLottiEScadenze;\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      selectedWarehouse: ((_JSON$parse$warehouse = JSON.parse(localStorage.getItem('user') || '{}').warehousesCross) === null || _JSON$parse$warehouse === void 0 ? void 0 : (_JSON$parse$warehouse2 = _JSON$parse$warehouse[0]) === null || _JSON$parse$warehouse2 === void 0 ? void 0 : _JSON$parse$warehouse2.idWarehouse) || null,\n      selectedDocuments: null,\n      loading: true,\n      resultDialog2: false,\n      displayed: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      value: null,\n      value2: null,\n      totalRecords: 0,\n      search: '',\n      selectedMail: '',\n      param: '?idWarehouses=',\n      param2: '&idSupplying=',\n      selectedSupplyer: null,\n      deleteResultDialog: null,\n      role: localStorage.getItem('role'),\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedSupplyer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying, _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying = element.idSupplying) === null || _element$idSupplying === void 0 ? void 0 : _element$idSupplying.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.warehouse = [];\n    this.loadLazyTimeout = null;\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.sendMail = this.sendMail.bind(this);\n    this.hideDialogSendMail = this.hideDialogSendMail.bind(this);\n    this.senderMail = this.senderMail.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.editDocResult = this.editDocResult.bind(this);\n    this.onRowSelect = this.onRowSelect.bind(this);\n  }\n  async componentDidMount() {\n    var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var _element$idSupplying2, _element$tasks2;\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          supplying: (_element$idSupplying2 = element.idSupplying) === null || _element$idSupplying2 === void 0 ? void 0 : _element$idSupplying2.idRegistry.firstName,\n          idSupplying: element.idSupplying,\n          documentDate: element.documentDate,\n          documentBodies: element.documentBodies,\n          deliveryDate: element.deliveryDate,\n          tasks: element.tasks,\n          erpSync: element.erpSync,\n          status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n          idLogEmail: element.idLogEmail,\n          total: element.total,\n          totalTaxed: element.totalTaxed\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"employees/?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?documentType=FOR-ORDINE&idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare il documento. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    var respMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'OP_MAG' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n          opMag.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n          respMag.push({\n            label: element.first_name + ' ' + element.last_name,\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog3: true,\n      opMag: opMag,\n      mex: message,\n      respMag: respMag\n    });\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var opMag = [];\n    var respMag = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          if (element.role === 'RESP_MAG') {\n            respMag.push({\n              label: element.first_name + ' ' + element.last_name,\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.opMag = opMag;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog3: true,\n        opMag: opMag,\n        respMag: respMag,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null);\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'OP_MAG') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n                opMag.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              }\n            });\n          }\n          this.opMag = opMag;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'OP_MAG') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n              opMag.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            }\n          });\n        }\n        this.opMag = opMag;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog3: true,\n          opMag: opMag,\n          respMag: respMag,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  sendMail(result) {\n    var _result$idSupplying, _result$idSupplying2;\n    this.setState({\n      result,\n      resultDialog4: true,\n      selectedMail: ((_result$idSupplying = result.idSupplying) === null || _result$idSupplying === void 0 ? void 0 : _result$idSupplying.idRegistry.email) !== null ? (_result$idSupplying2 = result.idSupplying) === null || _result$idSupplying2 === void 0 ? void 0 : _result$idSupplying2.idRegistry.email : ''\n    });\n  }\n  hideDialogSendMail() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  async senderMail() {\n    var url = 'email/sendfororders?idDocument=' + this.state.result.id + '&emailSuppliyng=' + this.state.selectedMail;\n    await APIRequest(\"GET\", url).then(res => {\n      var _this$toast;\n      console.log(res.data);\n      (_this$toast = this.toast) === null || _this$toast === void 0 ? void 0 : _this$toast.show({\n        severity: \"success\",\n        summary: \"Ottimo !\",\n        detail: \"L'email è stata inviata con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _this$toast2, _e$response9, _e$response0;\n      console.log(e);\n      (_this$toast2 = this.toast) === null || _this$toast2 === void 0 ? void 0 : _this$toast2.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile inviare l'email. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  async editDocResult(result) {\n    if (result.tasks === null && !result.erpSync) {\n      await APIRequest(\"GET\", \"documents?documentType=FOR-ORDINE&idDocumentHead=\".concat(result.id)).then(res => {\n        console.log(res.data);\n        var products = [];\n        var totProd = 0;\n        var total = 0;\n        res.data.documentBodies.forEach(el => {\n          totProd += el.colliPreventivo;\n          var find = el.idProductsPackaging.idProduct.supplyingProducts.find(obj => obj.idSupplying.id === res.data.idSupplying.id);\n          if (find !== undefined) {\n            products.push(_objectSpread(_objectSpread({}, find), {}, {\n              quantity: el.colliPreventivo * el.idProductsPackaging.pcsXPackage,\n              moltiplicatore: el.idProductsPackaging.pcsXPackage,\n              initPrice: find.sell_in,\n              qtaIns: el.colliPreventivo,\n              idProduct2: el.idProductsPackaging.idProduct\n            }));\n          }\n        });\n        var iva = res.data.total === res.data.totalTaxed ? true : false;\n        total = new Intl.NumberFormat(\"it-IT\", {\n          style: \"currency\",\n          currency: \"EUR\",\n          maximumFractionDigits: 6\n        }).format(products.map(item => item.quantity * item.sell_in).reduce((prev, curr) => prev + curr, 0));\n        localStorage.setItem(\"Prodotti\", JSON.stringify(products));\n        localStorage.setItem(\"Cart\", JSON.stringify(products));\n        localStorage.setItem(\"DatiConsegna\", JSON.stringify({\n          idRegistry: res.data.idSupplying.idRegistry,\n          note: res.data.note,\n          termsPayment: res.data.termsPayment,\n          deliveryDate: res.data.deliveryDate,\n          iva: iva\n        }));\n        window.sessionStorage.setItem(\"idDocument\", JSON.stringify({\n          id: result.id,\n          number: result.number,\n          documentDate: result.documentDate\n        }));\n        window.sessionStorage.setItem(\"idSupplier\", res.data.idSupplying.id);\n        window.sessionStorage.setItem(\"Carrello\", totProd);\n        window.sessionStorage.setItem(\"totCart\", total);\n        window.sessionStorage.setItem('idWarehouse', JSON.stringify({\n          name: \"\".concat(res.data.idWarehouses.warehouseName, \" \").concat(res.data.idWarehouses.address, \" \").concat(res.data.idWarehouses.citta, \" (\").concat(res.data.idWarehouses.prov, \"), \").concat(res.data.idWarehouses.cap),\n          code: res.data.idWarehouses.id\n        }));\n        window.location.pathname = '/distributore/Ordina';\n      }).catch(e => {\n        var _e$response1, _e$response10;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non siamo riusciti a reperire il documento da modificare. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Per modificare il documento \\xE8 necessario che non ci sia una task associata e che il documento non sia stato gi\\xE0 sincronizzato sull'ERP\",\n        life: 3000\n      });\n    }\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    this.setState({\n      loading: true,\n      search: '',\n      selectedSupplyer: null\n    });\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var _element$idSupplying3, _element$tasks3;\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          supplying: (_element$idSupplying3 = element.idSupplying) === null || _element$idSupplying3 === void 0 ? void 0 : _element$idSupplying3.idRegistry.firstName,\n          idSupplying: element.idSupplying,\n          documentDate: element.documentDate,\n          documentBodies: element.documentBodies,\n          deliveryDate: element.deliveryDate,\n          tasks: element.tasks,\n          erpSync: element.erpSync,\n          status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n          idLogEmail: element.idLogEmail,\n          total: element.total,\n          totalTaxed: element.totalTaxed\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response11, _e$response12;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    this.setState({\n      loading: true,\n      search: '',\n      selectedSupplyer: null\n    });\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var _element$idSupplying4, _element$tasks4;\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          supplying: (_element$idSupplying4 = element.idSupplying) === null || _element$idSupplying4 === void 0 ? void 0 : _element$idSupplying4.idRegistry.firstName,\n          idSupplying: element.idSupplying,\n          documentDate: element.documentDate,\n          documentBodies: element.documentBodies,\n          deliveryDate: element.deliveryDate,\n          tasks: element.tasks,\n          erpSync: element.erpSync,\n          status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n          idLogEmail: element.idLogEmail,\n          total: element.total,\n          totalTaxed: element.totalTaxed\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response13, _e$response14;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying5, _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying5 = element.idSupplying) === null || _element$idSupplying5 === void 0 ? void 0 : _element$idSupplying5.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying6, _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying6 = element.idSupplying) === null || _element$idSupplying6 === void 0 ? void 0 : _element$idSupplying6.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n      window.location.reload();\n    }).catch(e => {\n      var _e$response19, _e$response20;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter4 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.senderMail,\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-send mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 21\n        }, this), Costanti.Invia, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialogSendMail,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 13\n    }, this);\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"supplying\",\n      header: Costanti.Fornitore,\n      body: \"supplying\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"deliveryDate\",\n      header: Costanti.DCons,\n      body: \"deliveryDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.manager.idUser.username\",\n      header: Costanti.Responsabile,\n      body: \"manager\",\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"idLogEmail\",\n      header: Costanti.mail,\n      body: \"idLogEmail\",\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.inviaMail,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 47\n      }, this),\n      handler: this.sendMail\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 841,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }, {\n      name: Costanti.ModificaTask,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-pencil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 50\n      }, this),\n      handler: this.onRowSelect\n    }, {\n      name: Costanti.ModificaDoc,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-pencil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 49\n      }, this),\n      handler: this.editDocResult\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 45\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DocumentiApprovvigionamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton: this.state.role === distributore ? true : false,\n          actionExtraButton: this.assegnaLavorazioni,\n          labelExtraButton: Costanti.assegnaLavorazioni,\n          disabledExtraButton: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Acquisti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          result: this.state.results3,\n          results: this.state.result,\n          documento: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag,\n          respMag: this.state.respMag,\n          distributore: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 910,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog4,\n        header: Costanti.inviaMail,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter4,\n        onHide: this.hideDialogSendMail,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: Costanti.SendMailFornMex\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(InputText, {\n              value: this.state.selectedMail,\n              onChange: e => this.setState({\n                selectedMail: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: [\"* \", Costanti.MailSepVirg]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 17\n      }, this), this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n        title: \"Prima di procedere\",\n        content: \"Seleziona un magazzino \",\n        target: \".selWar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default CaricoMagRing;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "<PERSON><PERSON>", "Dialog", "JoyrideGen", "InputText", "distributore", "ringControlloLottiEScadenze", "Toast", "Print", "SelezionaOperatore", "VisualizzaDocumenti", "Nav", "CustomDataTable", "jsxDEV", "_jsxDEV", "CaricoMagRing", "constructor", "props", "_JSON$parse$warehouse", "_JSON$parse$warehouse2", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onRowSelect", "result", "idFOROrd", "state", "results", "for<PERSON>ach", "element", "localStorage", "setItem", "JSON", "stringify", "window", "location", "pathname", "results2", "results3", "results4", "results5", "selectedWarehouse", "parse", "getItem", "warehousesCross", "idWarehouse", "selectedDocuments", "loading", "resultDialog2", "displayed", "resultDialog3", "resultDialog4", "opMag", "respMag", "mex", "value", "value2", "totalRecords", "search", "selectedMail", "param", "param2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteResultDialog", "role", "lazyParams", "first", "rows", "page", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "e", "setState", "name", "url", "code", "then", "res", "documento", "data", "documents", "_element$idSupplying", "_element$tasks", "x", "number", "type", "supplying", "idSupplying", "idRegistry", "documentDate", "documentBodies", "tasks", "erpSync", "idLogEmail", "total", "totalTaxed", "push", "totalCount", "pageCount", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "warehouse", "loadLazyTimeout", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "reset", "resetDesc", "assegnaLavorazioni", "onPage", "onSort", "onFilter", "sendMail", "hideDialogSendMail", "senderMail", "<PERSON><PERSON><PERSON><PERSON>", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "editDocResult", "componentDidMount", "_element$idSupplying2", "_element$tasks2", "_e$response3", "_e$response4", "_e$response5", "_e$response6", "documentBody", "task", "_e$response7", "_e$response8", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "el", "length", "FilterOp", "operator", "_result$idSupplying", "_result$idSupplying2", "email", "_this$toast", "setTimeout", "reload", "_this$toast2", "_e$response9", "_e$response0", "products", "totProd", "colliPreventivo", "find", "idProductsPackaging", "idProduct", "supplyingProducts", "obj", "quantity", "pcsXPackage", "moltiplicatore", "initPrice", "sell_in", "qtaIns", "idProduct2", "iva", "NumberFormat", "style", "currency", "maximumFractionDigits", "map", "item", "reduce", "prev", "curr", "note", "sessionStorage", "idWarehouses", "warehouseName", "address", "citta", "prov", "cap", "_e$response1", "_e$response10", "_element$idSupplying3", "_element$tasks3", "_e$response11", "_e$response12", "_element$idSupplying4", "_element$tasks4", "_e$response13", "_e$response14", "event", "clearTimeout", "_element$idSupplying5", "_element$tasks5", "_e$response15", "_e$response16", "Math", "random", "field", "_element$idSupplying6", "_element$tasks6", "_e$response17", "_e$response18", "loadLazyData", "_e$response19", "_e$response20", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "doc", "resultDialogFooter3", "resultDialogFooter4", "Invia", "deleteResultDialogFooter", "icon", "Si", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "Fornitore", "DataDoc", "DCons", "Responsabile", "Operatore", "StatoTask", "mail", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "inviaMail", "assegnaLavorazione", "status2", "ModificaTask", "ModificaDoc", "Elimina", "ref", "DocumentiApprovvigionamento", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton", "actionExtraButton", "labelExtraButton", "disabledExtraButton", "classInputSearch", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "SendMailFornMex", "onChange", "target", "MailSepVirg", "Conferma", "fontSize", "ResDeleteDoc", "title", "content"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/ring/caricoMagRing.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioAcquisti - operazioni sull'ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Button } from 'primereact/button';\nimport { Dialog } from 'primereact/dialog';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { InputText } from 'primereact/inputtext';\nimport { distributore, ringControlloLottiEScadenze } from '../../components/route';\nimport { Toast } from 'primereact/toast';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\n\nclass CaricoMagRing extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            selectedWarehouse: JSON.parse(localStorage.getItem('user') || '{}').warehousesCross?.[0]?.idWarehouse || null,\n            selectedDocuments: null,\n            loading: true,\n            resultDialog2: false,\n            displayed: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            value: null,\n            value2: null,\n            totalRecords: 0,\n            search: '',\n            selectedMail: '',\n            param: '?idWarehouses=',\n            param2: '&idSupplying=',\n            selectedSupplyer: null,\n            deleteResultDialog: null,\n            role: localStorage.getItem('role'),\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedSupplyer: e.value\n            });\n\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.warehouse = [];\n        this.loadLazyTimeout = null;\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.sendMail = this.sendMail.bind(this);\n        this.hideDialogSendMail = this.hideDialogSendMail.bind(this);\n        this.senderMail = this.senderMail.bind(this);\n        this.selectionHandler = this.selectionHandler.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.editDocResult = this.editDocResult.bind(this);\n        this.onRowSelect = this.onRowSelect.bind(this);\n    }\n    async componentDidMount() {\n        var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        supplying: element.idSupplying?.idRegistry.firstName,\n                        idSupplying: element.idSupplying,\n                        documentDate: element.documentDate,\n                        documentBodies: element.documentBodies,\n                        deliveryDate: element.deliveryDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idLogEmail: element.idLogEmail,\n                        total: element.total,\n                        totalTaxed: element.totalTaxed\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"employees/?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?documentType=FOR-ORDINE&idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        var respMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'OP_MAG' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                    opMag.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n                    respMag.push({\n                        label: element.first_name + ' ' + element.last_name,\n                        value: element.idemployee,\n                    });\n                }\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog3: true,\n            opMag: opMag,\n            mex: message,\n            respMag: respMag\n        });\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var opMag = [];\n        var respMag = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    if (element.role === 'RESP_MAG') {\n                        respMag.push({\n                            label: element.first_name + ' ' + element.last_name,\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.opMag = opMag;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog3: true,\n                opMag: opMag,\n                respMag: respMag,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'OP_MAG') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                                opMag.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }\n                        })\n                    }\n                    this.opMag = opMag;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog3: true,\n                        opMag: opMag,\n                        respMag: respMag,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'OP_MAG') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                            opMag.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }\n                    })\n                }\n                this.opMag = opMag;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog3: true,\n                    opMag: opMag,\n                    respMag: respMag,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    sendMail(result) {\n        this.setState({\n            result,\n            resultDialog4: true,\n            selectedMail: result.idSupplying?.idRegistry.email !== null ? result.idSupplying?.idRegistry.email : ''\n        });\n    }\n    hideDialogSendMail() {\n        this.setState({\n            resultDialog4: false,\n        });\n    }\n    async senderMail() {\n        var url = 'email/sendfororders?idDocument=' + this.state.result.id + '&emailSuppliyng=' + this.state.selectedMail\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                console.log(res.data)\n                this.toast?.show({\n                    severity: \"success\",\n                    summary: \"Ottimo !\",\n                    detail: \"L'email è stata inviata con successo\",\n                    life: 3000,\n                });\n                setTimeout(() => {\n                    window.location.reload();\n                }, 3000)\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast?.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile inviare l'email. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    async editDocResult(result) {\n        if (result.tasks === null && !result.erpSync) {\n            await APIRequest(\"GET\", `documents?documentType=FOR-ORDINE&idDocumentHead=${result.id}`)\n                .then((res) => {\n                    console.log(res.data)\n                    var products = []\n                    var totProd = 0\n                    var total = 0\n                    res.data.documentBodies.forEach(el => {\n                        totProd += el.colliPreventivo\n                        var find = el.idProductsPackaging.idProduct.supplyingProducts.find(obj => obj.idSupplying.id === res.data.idSupplying.id)\n                        if (find !== undefined) {\n                            products.push({ ...find, quantity: el.colliPreventivo * el.idProductsPackaging.pcsXPackage, moltiplicatore: el.idProductsPackaging.pcsXPackage, initPrice: find.sell_in, qtaIns: el.colliPreventivo, idProduct2: el.idProductsPackaging.idProduct })\n                        }\n                    })\n                    var iva = res.data.total === res.data.totalTaxed ? true : false\n                    total = new Intl.NumberFormat(\"it-IT\", {\n                        style: \"currency\",\n                        currency: \"EUR\",\n                        maximumFractionDigits: 6\n                    }).format(products.map(item => item.quantity * item.sell_in).reduce((prev, curr) => prev + curr, 0))\n                    localStorage.setItem(\"Prodotti\", JSON.stringify(products));\n                    localStorage.setItem(\"Cart\", JSON.stringify(products));\n                    localStorage.setItem(\"DatiConsegna\", JSON.stringify({ idRegistry: res.data.idSupplying.idRegistry, note: res.data.note, termsPayment: res.data.termsPayment, deliveryDate: res.data.deliveryDate, iva: iva }))\n                    window.sessionStorage.setItem(\"idDocument\", JSON.stringify({ id: result.id, number: result.number, documentDate: result.documentDate }))\n                    window.sessionStorage.setItem(\"idSupplier\", res.data.idSupplying.id)\n                    window.sessionStorage.setItem(\"Carrello\", totProd)\n                    window.sessionStorage.setItem(\"totCart\", total)\n                    window.sessionStorage.setItem('idWarehouse', JSON.stringify({ name: `${res.data.idWarehouses.warehouseName} ${res.data.idWarehouses.address} ${res.data.idWarehouses.citta} (${res.data.idWarehouses.prov}), ${res.data.idWarehouses.cap}`, code: res.data.idWarehouses.id }))\n                    window.location.pathname = '/distributore/Ordina'\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non siamo riusciti a reperire il documento da modificare. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: `Per modificare il documento è necessario che non ci sia una task associata e che il documento non sia stato già sincronizzato sull'ERP`,\n                life: 3000,\n            });\n        }\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        this.setState({ loading: true, search: '', selectedSupplyer: null });\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        supplying: element.idSupplying?.idRegistry.firstName,\n                        idSupplying: element.idSupplying,\n                        documentDate: element.documentDate,\n                        documentBodies: element.documentBodies,\n                        deliveryDate: element.deliveryDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idLogEmail: element.idLogEmail,\n                        total: element.total,\n                        totalTaxed: element.totalTaxed\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        this.setState({ loading: true, search: '', selectedSupplyer: null });\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        supplying: element.idSupplying?.idRegistry.firstName,\n                        idSupplying: element.idSupplying,\n                        documentDate: element.documentDate,\n                        documentBodies: element.documentBodies,\n                        deliveryDate: element.deliveryDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idLogEmail: element.idLogEmail,\n                        total: element.total,\n                        totalTaxed: element.totalTaxed\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    selectionHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n                window.location.reload();\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false,\n        });\n    }\n    onRowSelect = (result) => {\n        var idFOROrd = []\n        this.state.results.forEach(element => {\n            if (element.id === result.id) {\n                idFOROrd = element\n            }\n        })\n        localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n        window.location.pathname = ringControlloLottiEScadenze;\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter4 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.senderMail}>\n                    {\" \"}\n                    <i className='pi pi-send mr-2'></i>{Costanti.Invia}{\" \"}\n                </Button>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialogSendMail}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"supplying\",\n                header: Costanti.Fornitore,\n                body: \"supplying\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"deliveryDate\",\n                header: Costanti.DCons,\n                body: \"deliveryDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.manager.idUser.username\",\n                header: Costanti.Responsabile,\n                body: \"manager\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.operator.idUser.username\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.status\",\n                header: Costanti.StatoTask,\n                body: \"assigned\",\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: \"ERP Sync\",\n                body: \"erpSync\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"idLogEmail\",\n                header: Costanti.mail,\n                body: \"idLogEmail\",\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.inviaMail, icon: <i className=\"pi pi-send\" />, handler: this.sendMail },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n            { name: Costanti.ModificaTask, icon: <i className=\"pi pi-pencil\" />, handler: this.onRowSelect },\n            { name: Costanti.ModificaDoc, icon: <i className=\"pi pi-pencil\" />, handler: this.editDocResult },\n            { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DocumentiApprovvigionamento}</h1>\n                </div>\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionHandler(e)}\n                        showExtraButton={this.state.role === distributore ? true : false}\n                        actionExtraButton={this.assegnaLavorazioni}\n                        labelExtraButton={Costanti.assegnaLavorazioni}\n                        disabledExtraButton={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Acquisti\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        result={this.state.results3}\n                        results={this.state.result}\n                        documento={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaOperatore result={this.state.result} opMag={this.opMag} respMag={this.state.respMag} distributore={true} />\n                </Dialog>\n                <Dialog\n                    visible={this.state.resultDialog4}\n                    header={Costanti.inviaMail}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter4}\n                    onHide={this.hideDialogSendMail}\n                >\n                    <div className='row'>\n                        <div className='col-12'><span>{Costanti.SendMailFornMex}</span></div>\n                        <div className='col-12'>\n                            <InputText value={this.state.selectedMail} onChange={(e) => this.setState({ selectedMail: e.target.value })} />\n                        </div>\n                        <div className='col-12'><span className='text-danger'>* {Costanti.MailSepVirg}</span></div>\n                    </div>\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                {this.state.displayed &&\n                    <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                }\n            </div>\n        );\n    }\n}\n\nexport default CaricoMagRing;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,YAAY,EAAEC,2BAA2B,QAAQ,wBAAwB;AAClF,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,KAAK,QAAQ,2CAA2C;AACjE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAa,SAASjB,SAAS,CAAC;EAalCkB,WAAWA,CAACC,KAAK,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACf,KAAK,CAACF,KAAK,CAAC;IAbhB;IAAA,KACAG,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAAA,KA8pBDC,WAAW,GAAIC,MAAM,IAAK;MACtB,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIA,OAAO,CAACf,EAAE,KAAKU,MAAM,CAACV,EAAE,EAAE;UAC1BW,QAAQ,GAAGI,OAAO;QACtB;MACJ,CAAC,CAAC;MACFC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAAC,CAAC;MAC5DS,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGrC,2BAA2B;IAC1D,CAAC;IApqBG,IAAI,CAAC2B,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbU,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,EAAA9B,qBAAA,GAAAqB,IAAI,CAACU,KAAK,CAACZ,YAAY,CAACa,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAACC,eAAe,cAAAjC,qBAAA,wBAAAC,sBAAA,GAAhED,qBAAA,CAAmE,CAAC,CAAC,cAAAC,sBAAA,uBAArEA,sBAAA,CAAuEiC,WAAW,KAAI,IAAI;MAC7GC,iBAAiB,EAAE,IAAI;MACvBC,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACP9B,MAAM,EAAE,IAAI,CAACX,WAAW;MACxB0C,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,gBAAgB;MACvBC,MAAM,EAAE,eAAe;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,IAAI,EAAElC,YAAY,CAACa,OAAO,CAAC,MAAM,CAAC;MAClCsB,UAAU,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAEhB,KAAK,EAAE,EAAE;YAAEiB,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAEjB,KAAK,EAAE,EAAE;YAAEiB,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAEjB,KAAK,EAAE,EAAE;YAAEiB,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAMC,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACV5B,OAAO,EAAE,IAAI;QACbW,MAAM,EAAEgB,CAAC,CAACnB,KAAK,CAACqB,IAAI;QACpBd,gBAAgB,EAAEY,CAAC,CAACnB;MACxB,CAAC,CAAC;MAEF,IAAIsB,GAAG,GAAG,WAAW,GAAG,IAAI,CAACnD,KAAK,CAACkC,KAAK,GAAG,IAAI,CAAClC,KAAK,CAACe,iBAAiB,GAAG,IAAI,CAACf,KAAK,CAACmC,MAAM,GAAGa,CAAC,CAACnB,KAAK,CAACuB,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACpD,KAAK,CAACuC,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACG,IAAI;MAClN,MAAM3E,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACvD,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAuD,oBAAA,EAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJxE,EAAE,EAAEe,OAAO,CAACf,EAAE;YACdyE,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;YACtBC,IAAI,EAAE3D,OAAO,CAAC2D,IAAI;YAClBC,SAAS,GAAAL,oBAAA,GAAEvD,OAAO,CAAC6D,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBO,UAAU,CAAC5E,SAAS;YACpD2E,WAAW,EAAE7D,OAAO,CAAC6D,WAAW;YAChCE,YAAY,EAAE/D,OAAO,CAAC+D,YAAY;YAClCC,cAAc,EAAEhE,OAAO,CAACgE,cAAc;YACtC1E,YAAY,EAAEU,OAAO,CAACV,YAAY;YAClC2E,KAAK,EAAEjE,OAAO,CAACiE,KAAK;YACpBC,OAAO,EAAElE,OAAO,CAACkE,OAAO;YACxBzE,MAAM,GAAA+D,cAAA,GAAExD,OAAO,CAACiE,KAAK,cAAAT,cAAA,uBAAbA,cAAA,CAAe/D,MAAM;YAC7B0E,UAAU,EAAEnE,OAAO,CAACmE,UAAU;YAC9BC,KAAK,EAAEpE,OAAO,CAACoE,KAAK;YACpBC,UAAU,EAAErE,OAAO,CAACqE;UACxB,CAAC;UACDjB,SAAS,CAACkB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACX,QAAQ,CAAC;UACVhD,OAAO,EAAEsD,SAAS;UAClBzC,QAAQ,EAAEyC,SAAS;UACnBxB,YAAY,EAAEuB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjCnC,UAAU,EAAE;YAAEC,KAAK,EAAE,IAAI,CAACxC,KAAK,CAACuC,UAAU,CAACC,KAAK;YAAEC,IAAI,EAAE,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACE,IAAI;YAAEC,IAAI,EAAE,IAAI,CAAC1C,KAAK,CAACuC,UAAU,CAACG,IAAI;YAAEiC,SAAS,EAAErB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC1E,KAAK,CAACuC,UAAU,CAACE;UAAM,CAAC;UACpLpB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDuD,KAAK,CAAE5B,CAAC,IAAK;QAAA,IAAA6B,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;QACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAA7B,CAAC,CAACuC,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYrB,IAAI,MAAKgC,SAAS,IAAAV,YAAA,GAAG9B,CAAC,CAACuC,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYtB,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACN,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACR,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACT,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACU,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACV,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACW,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACX,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACY,UAAU,GAAG,IAAI,CAACA,UAAU,CAACZ,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACa,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACb,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACc,YAAY,GAAG,IAAI,CAACA,YAAY,CAACd,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACe,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACf,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACgB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACjB,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACjG,WAAW,GAAG,IAAI,CAACA,WAAW,CAACiG,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMkB,iBAAiBA,CAAA,EAAG;IACtB,IAAI7D,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACnD,KAAK,CAACe,iBAAiB,GAAG,gCAAgC,GAAG,IAAI,CAACf,KAAK,CAACuC,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACG,IAAI;IAC1K,MAAM3E,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACvD,OAAO,CAACC,OAAO,IAAI;QAAA,IAAA8G,qBAAA,EAAAC,eAAA;QAClC,IAAItD,CAAC,GAAG;UACJxE,EAAE,EAAEe,OAAO,CAACf,EAAE;UACdyE,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;UACtBC,IAAI,EAAE3D,OAAO,CAAC2D,IAAI;UAClBC,SAAS,GAAAkD,qBAAA,GAAE9G,OAAO,CAAC6D,WAAW,cAAAiD,qBAAA,uBAAnBA,qBAAA,CAAqBhD,UAAU,CAAC5E,SAAS;UACpD2E,WAAW,EAAE7D,OAAO,CAAC6D,WAAW;UAChCE,YAAY,EAAE/D,OAAO,CAAC+D,YAAY;UAClCC,cAAc,EAAEhE,OAAO,CAACgE,cAAc;UACtC1E,YAAY,EAAEU,OAAO,CAACV,YAAY;UAClC2E,KAAK,EAAEjE,OAAO,CAACiE,KAAK;UACpBC,OAAO,EAAElE,OAAO,CAACkE,OAAO;UACxBzE,MAAM,GAAAsH,eAAA,GAAE/G,OAAO,CAACiE,KAAK,cAAA8C,eAAA,uBAAbA,eAAA,CAAetH,MAAM;UAC7B0E,UAAU,EAAEnE,OAAO,CAACmE,UAAU;UAC9BC,KAAK,EAAEpE,OAAO,CAACoE,KAAK;UACpBC,UAAU,EAAErE,OAAO,CAACqE;QACxB,CAAC;QACDjB,SAAS,CAACkB,IAAI,CAACb,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACX,QAAQ,CAAC;QACVhD,OAAO,EAAEsD,SAAS;QAClBzC,QAAQ,EAAEyC,SAAS;QACnBxB,YAAY,EAAEuB,GAAG,CAACE,IAAI,CAACkB,UAAU;QACjCnC,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAACxC,KAAK,CAACuC,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAAC1C,KAAK,CAACuC,UAAU,CAACG,IAAI;UAAEiC,SAAS,EAAErB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC1E,KAAK,CAACuC,UAAU,CAACE;QAAM,CAAC;QACpLpB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDuD,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAmE,YAAA,EAAAC,YAAA;MACVrC,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA6B,YAAA,GAAAnE,CAAC,CAACuC,QAAQ,cAAA4B,YAAA,uBAAVA,YAAA,CAAY3D,IAAI,MAAKgC,SAAS,IAAA4B,YAAA,GAAGpE,CAAC,CAACuC,QAAQ,cAAA6B,YAAA,uBAAVA,YAAA,CAAY5D,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAM3H,UAAU,CAAC,KAAK,EAAE,iCAAiC,CAAC,CACrDsF,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACL,QAAQ,CAAC;QACVpC,QAAQ,EAAEyC,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDoB,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAqE,YAAA,EAAAC,YAAA;MACVvC,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAA+B,YAAA,GAAArE,CAAC,CAACuC,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAY7D,IAAI,MAAKgC,SAAS,IAAA8B,YAAA,GAAGtE,CAAC,CAACuC,QAAQ,cAAA+B,YAAA,uBAAVA,YAAA,CAAY9D,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACA,MAAMG,cAAcA,CAAC/F,MAAM,EAAE;IACzB,IAAIqD,GAAG,GAAG,mDAAmD,GAAGrD,MAAM,CAACV,EAAE;IACzE,IAAImI,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMzJ,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACXiE,YAAY,GAAGjE,GAAG,CAACE,IAAI,CAACW,cAAc;MACtCrE,MAAM,CAACqE,cAAc,GAAGb,GAAG,CAACE,IAAI,CAACW,cAAc;MAC/CqD,IAAI,GAAGlE,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDoB,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAyE,YAAA,EAAAC,YAAA;MACV3C,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,2EAAAC,MAAA,CAAwE,EAAAmC,YAAA,GAAAzE,CAAC,CAACuC,QAAQ,cAAAkC,YAAA,uBAAVA,YAAA,CAAYjE,IAAI,MAAKgC,SAAS,IAAAkC,YAAA,GAAG1E,CAAC,CAACuC,QAAQ,cAAAmC,YAAA,uBAAVA,YAAA,CAAYlE,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QAC7IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpB3F,MAAM,CAAC+D,MAAM,GACb,OAAO,GACP,IAAI8D,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACnI,MAAM,CAACoE,YAAY,CAAC,CAAC;IAC5C,IAAI,CAACjB,QAAQ,CAAC;MACV3B,aAAa,EAAE,IAAI;MACnBxB,MAAM,EAAE0H,IAAI;MACZ7G,QAAQ,EAAE,IAAI,CAACX,KAAK,CAACC,OAAO,CAACiI,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAC/I,EAAE,KAAKU,MAAM,CAACV,EAAE,CAAC;MAClEwB,QAAQ,EAAE2G,YAAY;MACtB3F,GAAG,EAAE6D;IACT,CAAC,CAAC;EACN;EACA;EACAM,kBAAkBA,CAACjG,MAAM,EAAE;IACvB,IAAI,CAACmD,QAAQ,CAAC;MACVnD,MAAM,EAAEA,MAAM;MACdwB,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA0E,UAAUA,CAAClG,MAAM,EAAE;IACf,IAAI2F,OAAO,GACP,oBAAoB,GACpB3F,MAAM,CAAC+D,MAAM,GACb,OAAO,GACP,IAAI8D,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACnI,MAAM,CAACoE,YAAY,CAAC,CAAC;IAC5C,IAAIxC,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC3B,KAAK,CAACa,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAACb,KAAK,CAACa,QAAQ,CAACX,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAACmC,IAAI,KAAK,QAAQ,IAAIxC,MAAM,CAACsE,KAAK,KAAK,IAAI,EAAE;UACpD,IAAIgE,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAAClI,OAAO,CAACmI,WAAW,CAAC,GAAGD,QAAQ,CAAClI,OAAO,CAACoI,YAAY,CAAC,GAAGF,QAAQ,CAAClI,OAAO,CAACqI,aAAa,CAAC;UAC1G9G,KAAK,CAAC+C,IAAI,CAAC;YACPgE,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;YAC1FvG,KAAK,EAAE1B,OAAO,CAACyI;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAIzI,OAAO,CAACmC,IAAI,KAAK,UAAU,IAAIxC,MAAM,CAACsE,KAAK,KAAK,IAAI,EAAE;UAC7DzC,OAAO,CAAC8C,IAAI,CAAC;YACTgE,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS;YACnD9G,KAAK,EAAE1B,OAAO,CAACyI;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAClH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuB,QAAQ,CAAC;MACVnD,MAAM,EAAA+I,aAAA,KAAO/I,MAAM,CAAE;MACrB0B,aAAa,EAAE,IAAI;MACnBE,KAAK,EAAEA,KAAK;MACZE,GAAG,EAAE6D,OAAO;MACZ9D,OAAO,EAAEA;IACb,CAAC,CAAC;EACN;EACAyE,kBAAkBA,CAAA,EAAG;IACjB,IAAIX,OAAO,GACP,iCAAiC;IACrC,IAAI/D,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIuG,MAAM,GAAG,IAAI,CAAClI,KAAK,CAACoB,iBAAiB,CAAC8G,MAAM,CAACY,EAAE,IAAIA,EAAE,CAAC1E,KAAK,KAAK,IAAI,CAAC;IACzE,IAAI8D,MAAM,CAACa,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAAC/I,KAAK,CAACa,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAACb,KAAK,CAACa,QAAQ,CAACX,OAAO,CAAEC,OAAO,IAAK;UACrC,IAAIA,OAAO,CAACmC,IAAI,KAAK,UAAU,EAAE;YAC7BX,OAAO,CAAC8C,IAAI,CAAC;cACTgE,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS;cACnD9G,KAAK,EAAE1B,OAAO,CAACyI;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAAClH,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACuB,QAAQ,CAAC;QACVnD,MAAM,EAAE,IAAI,CAACE,KAAK,CAACoB,iBAAiB;QACpCI,aAAa,EAAE,IAAI;QACnBE,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA,OAAO;QAChBC,GAAG,EAAE6D;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAIyC,MAAM,CAACa,MAAM,KAAK,IAAI,CAAC/I,KAAK,CAACoB,iBAAiB,CAAC2H,MAAM,EAAE;MAC9D,IAAIC,QAAQ,GAAG,IAAI,CAAChJ,KAAK,CAACoB,iBAAiB,CAAC8G,MAAM,CAAC/H,OAAO,IAAIA,OAAO,CAACiE,KAAK,CAAC6E,QAAQ,KAAK,IAAI,CAAC;MAC9F,IAAID,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIC,QAAQ,CAACD,MAAM,KAAK,IAAI,CAAC/I,KAAK,CAACoB,iBAAiB,CAAC2H,MAAM,EAAE;UACzD,IAAI,IAAI,CAAC/I,KAAK,CAACa,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAACb,KAAK,CAACa,QAAQ,CAACX,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAACmC,IAAI,KAAK,QAAQ,EAAE;gBAC3B,IAAI8F,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAAClI,OAAO,CAACmI,WAAW,CAAC,GAAGD,QAAQ,CAAClI,OAAO,CAACoI,YAAY,CAAC,GAAGF,QAAQ,CAAClI,OAAO,CAACqI,aAAa,CAAC;gBAC1G9G,KAAK,CAAC+C,IAAI,CAAC;kBACPgE,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;kBAC1FvG,KAAK,EAAE1B,OAAO,CAACyI;gBACnB,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA,IAAI,CAAClH,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACuB,QAAQ,CAAC;YACVnD,MAAM,EAAE,IAAI,CAACE,KAAK,CAACoB,iBAAiB;YACpCI,aAAa,EAAE,IAAI;YACnBE,KAAK,EAAEA,KAAK;YACZC,OAAO,EAAEA,OAAO;YAChBC,GAAG,EAAE6D;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,qJAAqJ;YAC7JK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1F,KAAK,CAACa,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAACb,KAAK,CAACa,QAAQ,CAACX,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACmC,IAAI,KAAK,QAAQ,EAAE;cAC3B,IAAI8F,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAAClI,OAAO,CAACmI,WAAW,CAAC,GAAGD,QAAQ,CAAClI,OAAO,CAACoI,YAAY,CAAC,GAAGF,QAAQ,CAAClI,OAAO,CAACqI,aAAa,CAAC;cAC1G9G,KAAK,CAAC+C,IAAI,CAAC;gBACPgE,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;gBAC1FvG,KAAK,EAAE1B,OAAO,CAACyI;cACnB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAAClH,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACuB,QAAQ,CAAC;UACVnD,MAAM,EAAE,IAAI,CAACE,KAAK,CAACoB,iBAAiB;UACpCI,aAAa,EAAE,IAAI;UACnBE,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,GAAG,EAAE6D;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACAO,UAAUA,CAAA,EAAG;IACT,IAAI,CAAChD,QAAQ,CAAC;MACVzB,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAgF,QAAQA,CAAC1G,MAAM,EAAE;IAAA,IAAAoJ,mBAAA,EAAAC,oBAAA;IACb,IAAI,CAAClG,QAAQ,CAAC;MACVnD,MAAM;MACN2B,aAAa,EAAE,IAAI;MACnBQ,YAAY,EAAE,EAAAiH,mBAAA,GAAApJ,MAAM,CAACkE,WAAW,cAAAkF,mBAAA,uBAAlBA,mBAAA,CAAoBjF,UAAU,CAACmF,KAAK,MAAK,IAAI,IAAAD,oBAAA,GAAGrJ,MAAM,CAACkE,WAAW,cAAAmF,oBAAA,uBAAlBA,oBAAA,CAAoBlF,UAAU,CAACmF,KAAK,GAAG;IACzG,CAAC,CAAC;EACN;EACA3C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACxD,QAAQ,CAAC;MACVxB,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA,MAAMiF,UAAUA,CAAA,EAAG;IACf,IAAIvD,GAAG,GAAG,iCAAiC,GAAG,IAAI,CAACnD,KAAK,CAACF,MAAM,CAACV,EAAE,GAAG,kBAAkB,GAAG,IAAI,CAACY,KAAK,CAACiC,YAAY;IACjH,MAAMlE,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MAAA,IAAA+F,WAAA;MACXtE,OAAO,CAACC,GAAG,CAAC1B,GAAG,CAACE,IAAI,CAAC;MACrB,CAAA6F,WAAA,OAAI,CAACpE,KAAK,cAAAoE,WAAA,uBAAVA,WAAA,CAAYnE,IAAI,CAAC;QACbC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,sCAAsC;QAC9CK,IAAI,EAAE;MACV,CAAC,CAAC;MACF4D,UAAU,CAAC,MAAM;QACb9I,MAAM,CAACC,QAAQ,CAAC8I,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CACD3E,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAwG,YAAA,EAAAC,YAAA,EAAAC,YAAA;MACV3E,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,CAAAwG,YAAA,OAAI,CAACvE,KAAK,cAAAuE,YAAA,uBAAVA,YAAA,CAAYtE,IAAI,CAAC;QACbC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,iEAAAC,MAAA,CAA8D,EAAAmE,YAAA,GAAAzG,CAAC,CAACuC,QAAQ,cAAAkE,YAAA,uBAAVA,YAAA,CAAYjG,IAAI,MAAKgC,SAAS,IAAAkE,YAAA,GAAG1G,CAAC,CAACuC,QAAQ,cAAAmE,YAAA,uBAAVA,YAAA,CAAYlG,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QACnIC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA,MAAMqB,aAAaA,CAACjH,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACsE,KAAK,KAAK,IAAI,IAAI,CAACtE,MAAM,CAACuE,OAAO,EAAE;MAC1C,MAAMtG,UAAU,CAAC,KAAK,sDAAAuH,MAAA,CAAsDxF,MAAM,CAACV,EAAE,CAAE,CAAC,CACnFiE,IAAI,CAAEC,GAAG,IAAK;QACXyB,OAAO,CAACC,GAAG,CAAC1B,GAAG,CAACE,IAAI,CAAC;QACrB,IAAImG,QAAQ,GAAG,EAAE;QACjB,IAAIC,OAAO,GAAG,CAAC;QACf,IAAIrF,KAAK,GAAG,CAAC;QACbjB,GAAG,CAACE,IAAI,CAACW,cAAc,CAACjE,OAAO,CAAC4I,EAAE,IAAI;UAClCc,OAAO,IAAId,EAAE,CAACe,eAAe;UAC7B,IAAIC,IAAI,GAAGhB,EAAE,CAACiB,mBAAmB,CAACC,SAAS,CAACC,iBAAiB,CAACH,IAAI,CAACI,GAAG,IAAIA,GAAG,CAAClG,WAAW,CAAC5E,EAAE,KAAKkE,GAAG,CAACE,IAAI,CAACQ,WAAW,CAAC5E,EAAE,CAAC;UACzH,IAAI0K,IAAI,KAAKtE,SAAS,EAAE;YACpBmE,QAAQ,CAAClF,IAAI,CAAAoE,aAAA,CAAAA,aAAA,KAAMiB,IAAI;cAAEK,QAAQ,EAAErB,EAAE,CAACe,eAAe,GAAGf,EAAE,CAACiB,mBAAmB,CAACK,WAAW;cAAEC,cAAc,EAAEvB,EAAE,CAACiB,mBAAmB,CAACK,WAAW;cAAEE,SAAS,EAAER,IAAI,CAACS,OAAO;cAAEC,MAAM,EAAE1B,EAAE,CAACe,eAAe;cAAEY,UAAU,EAAE3B,EAAE,CAACiB,mBAAmB,CAACC;YAAS,EAAE,CAAC;UACxP;QACJ,CAAC,CAAC;QACF,IAAIU,GAAG,GAAGpH,GAAG,CAACE,IAAI,CAACe,KAAK,KAAKjB,GAAG,CAACE,IAAI,CAACgB,UAAU,GAAG,IAAI,GAAG,KAAK;QAC/DD,KAAK,GAAG,IAAIoD,IAAI,CAACgD,YAAY,CAAC,OAAO,EAAE;UACnCC,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,KAAK;UACfC,qBAAqB,EAAE;QAC3B,CAAC,CAAC,CAAC9C,MAAM,CAAC2B,QAAQ,CAACoB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACb,QAAQ,GAAGa,IAAI,CAACT,OAAO,CAAC,CAACU,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC,CAAC;QACpG/K,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACoJ,QAAQ,CAAC,CAAC;QAC1DvJ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACoJ,QAAQ,CAAC,CAAC;QACtDvJ,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE0D,UAAU,EAAEX,GAAG,CAACE,IAAI,CAACQ,WAAW,CAACC,UAAU;UAAEmH,IAAI,EAAE9H,GAAG,CAACE,IAAI,CAAC4H,IAAI;UAAE1L,YAAY,EAAE4D,GAAG,CAACE,IAAI,CAAC9D,YAAY;UAAED,YAAY,EAAE6D,GAAG,CAACE,IAAI,CAAC/D,YAAY;UAAEiL,GAAG,EAAEA;QAAI,CAAC,CAAC,CAAC;QAC9MlK,MAAM,CAAC6K,cAAc,CAAChL,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEnB,EAAE,EAAEU,MAAM,CAACV,EAAE;UAAEyE,MAAM,EAAE/D,MAAM,CAAC+D,MAAM;UAAEK,YAAY,EAAEpE,MAAM,CAACoE;QAAa,CAAC,CAAC,CAAC;QACxI1D,MAAM,CAAC6K,cAAc,CAAChL,OAAO,CAAC,YAAY,EAAEiD,GAAG,CAACE,IAAI,CAACQ,WAAW,CAAC5E,EAAE,CAAC;QACpEoB,MAAM,CAAC6K,cAAc,CAAChL,OAAO,CAAC,UAAU,EAAEuJ,OAAO,CAAC;QAClDpJ,MAAM,CAAC6K,cAAc,CAAChL,OAAO,CAAC,SAAS,EAAEkE,KAAK,CAAC;QAC/C/D,MAAM,CAAC6K,cAAc,CAAChL,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE2C,IAAI,KAAAoC,MAAA,CAAKhC,GAAG,CAACE,IAAI,CAAC8H,YAAY,CAACC,aAAa,OAAAjG,MAAA,CAAIhC,GAAG,CAACE,IAAI,CAAC8H,YAAY,CAACE,OAAO,OAAAlG,MAAA,CAAIhC,GAAG,CAACE,IAAI,CAAC8H,YAAY,CAACG,KAAK,QAAAnG,MAAA,CAAKhC,GAAG,CAACE,IAAI,CAAC8H,YAAY,CAACI,IAAI,SAAApG,MAAA,CAAMhC,GAAG,CAACE,IAAI,CAAC8H,YAAY,CAACK,GAAG,CAAE;UAAEvI,IAAI,EAAEE,GAAG,CAACE,IAAI,CAAC8H,YAAY,CAAClM;QAAG,CAAC,CAAC,CAAC;QAC9QoB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG,sBAAsB;MACrD,CAAC,CAAC,CACDkE,KAAK,CAAE5B,CAAC,IAAK;QAAA,IAAA4I,YAAA,EAAAC,aAAA;QACV9G,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;QACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,iFAAAC,MAAA,CAAiF,EAAAsG,YAAA,GAAA5I,CAAC,CAACuC,QAAQ,cAAAqG,YAAA,uBAAVA,YAAA,CAAYpI,IAAI,MAAKgC,SAAS,IAAAqG,aAAA,GAAG7I,CAAC,CAACuC,QAAQ,cAAAsG,aAAA,uBAAVA,aAAA,CAAYrI,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;UACtJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,gJAA0I;QAChJK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACA,MAAMQ,KAAKA,CAAA,EAAG;IACV,IAAI/C,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACnD,KAAK,CAACe,iBAAiB,GAAG,gCAAgC,GAAG,IAAI,CAACf,KAAK,CAACuC,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACG,IAAI;IAC1K,IAAI,CAACO,QAAQ,CAAC;MAAE5B,OAAO,EAAE,IAAI;MAAEW,MAAM,EAAE,EAAE;MAAEI,gBAAgB,EAAE;IAAK,CAAC,CAAC;IACpE,MAAMrE,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACvD,OAAO,CAACC,OAAO,IAAI;QAAA,IAAA2L,qBAAA,EAAAC,eAAA;QAClC,IAAInI,CAAC,GAAG;UACJxE,EAAE,EAAEe,OAAO,CAACf,EAAE;UACdyE,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;UACtBC,IAAI,EAAE3D,OAAO,CAAC2D,IAAI;UAClBC,SAAS,GAAA+H,qBAAA,GAAE3L,OAAO,CAAC6D,WAAW,cAAA8H,qBAAA,uBAAnBA,qBAAA,CAAqB7H,UAAU,CAAC5E,SAAS;UACpD2E,WAAW,EAAE7D,OAAO,CAAC6D,WAAW;UAChCE,YAAY,EAAE/D,OAAO,CAAC+D,YAAY;UAClCC,cAAc,EAAEhE,OAAO,CAACgE,cAAc;UACtC1E,YAAY,EAAEU,OAAO,CAACV,YAAY;UAClC2E,KAAK,EAAEjE,OAAO,CAACiE,KAAK;UACpBC,OAAO,EAAElE,OAAO,CAACkE,OAAO;UACxBzE,MAAM,GAAAmM,eAAA,GAAE5L,OAAO,CAACiE,KAAK,cAAA2H,eAAA,uBAAbA,eAAA,CAAenM,MAAM;UAC7B0E,UAAU,EAAEnE,OAAO,CAACmE,UAAU;UAC9BC,KAAK,EAAEpE,OAAO,CAACoE,KAAK;UACpBC,UAAU,EAAErE,OAAO,CAACqE;QACxB,CAAC;QACDjB,SAAS,CAACkB,IAAI,CAACb,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACX,QAAQ,CAAC;QACVhD,OAAO,EAAEsD,SAAS;QAClBzC,QAAQ,EAAEyC,SAAS;QACnBxB,YAAY,EAAEuB,GAAG,CAACE,IAAI,CAACkB,UAAU;QACjCnC,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAACxC,KAAK,CAACuC,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAAC1C,KAAK,CAACuC,UAAU,CAACG,IAAI;UAAEiC,SAAS,EAAErB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC1E,KAAK,CAACuC,UAAU,CAACE;QAAM,CAAC;QACpLpB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDuD,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAgJ,aAAA,EAAAC,aAAA;MACVlH,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA0G,aAAA,GAAAhJ,CAAC,CAACuC,QAAQ,cAAAyG,aAAA,uBAAVA,aAAA,CAAYxI,IAAI,MAAKgC,SAAS,IAAAyG,aAAA,GAAGjJ,CAAC,CAACuC,QAAQ,cAAA0G,aAAA,uBAAVA,aAAA,CAAYzI,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACA,MAAMS,SAASA,CAAA,EAAG;IACd,IAAIhD,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACnD,KAAK,CAACe,iBAAiB,GAAG,gCAAgC,GAAG,IAAI,CAACf,KAAK,CAACuC,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACG,IAAI;IAC1K,IAAI,CAACO,QAAQ,CAAC;MAAE5B,OAAO,EAAE,IAAI;MAAEW,MAAM,EAAE,EAAE;MAAEI,gBAAgB,EAAE;IAAK,CAAC,CAAC;IACpE,MAAMrE,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACvD,OAAO,CAACC,OAAO,IAAI;QAAA,IAAA+L,qBAAA,EAAAC,eAAA;QAClC,IAAIvI,CAAC,GAAG;UACJxE,EAAE,EAAEe,OAAO,CAACf,EAAE;UACdyE,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;UACtBC,IAAI,EAAE3D,OAAO,CAAC2D,IAAI;UAClBC,SAAS,GAAAmI,qBAAA,GAAE/L,OAAO,CAAC6D,WAAW,cAAAkI,qBAAA,uBAAnBA,qBAAA,CAAqBjI,UAAU,CAAC5E,SAAS;UACpD2E,WAAW,EAAE7D,OAAO,CAAC6D,WAAW;UAChCE,YAAY,EAAE/D,OAAO,CAAC+D,YAAY;UAClCC,cAAc,EAAEhE,OAAO,CAACgE,cAAc;UACtC1E,YAAY,EAAEU,OAAO,CAACV,YAAY;UAClC2E,KAAK,EAAEjE,OAAO,CAACiE,KAAK;UACpBC,OAAO,EAAElE,OAAO,CAACkE,OAAO;UACxBzE,MAAM,GAAAuM,eAAA,GAAEhM,OAAO,CAACiE,KAAK,cAAA+H,eAAA,uBAAbA,eAAA,CAAevM,MAAM;UAC7B0E,UAAU,EAAEnE,OAAO,CAACmE,UAAU;UAC9BC,KAAK,EAAEpE,OAAO,CAACoE,KAAK;UACpBC,UAAU,EAAErE,OAAO,CAACqE;QACxB,CAAC;QACDjB,SAAS,CAACkB,IAAI,CAACb,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACX,QAAQ,CAAC;QACVhD,OAAO,EAAEsD,SAAS;QAClBzC,QAAQ,EAAEyC,SAAS;QACnBxB,YAAY,EAAEuB,GAAG,CAACE,IAAI,CAACkB,UAAU;QACjCnC,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAACxC,KAAK,CAACuC,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAAC1C,KAAK,CAACuC,UAAU,CAACG,IAAI;UAAEiC,SAAS,EAAErB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC1E,KAAK,CAACuC,UAAU,CAACE;QAAM,CAAC;QACpLpB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDuD,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAoJ,aAAA,EAAAC,aAAA;MACVtH,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8G,aAAA,GAAApJ,CAAC,CAACuC,QAAQ,cAAA6G,aAAA,uBAAVA,aAAA,CAAY5I,IAAI,MAAKgC,SAAS,IAAA6G,aAAA,GAAGrJ,CAAC,CAACuC,QAAQ,cAAA8G,aAAA,uBAAVA,aAAA,CAAY7I,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAW,MAAMA,CAACiG,KAAK,EAAE;IACV,IAAI,CAACrJ,QAAQ,CAAC;MAAE5B,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAACuE,eAAe,EAAE;MACtB2G,YAAY,CAAC,IAAI,CAAC3G,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAG0D,UAAU,CAAC,YAAY;MAC1C,IAAInG,GAAG,GAAG,WAAW,GAAG,IAAI,CAACnD,KAAK,CAACkC,KAAK,GAAG,IAAI,CAAClC,KAAK,CAACe,iBAAiB,IAAI,IAAI,CAACf,KAAK,CAACoC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACpC,KAAK,CAACmC,MAAM,GAAG,IAAI,CAACnC,KAAK,CAACoC,gBAAgB,CAACgB,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAGkJ,KAAK,CAAC7J,IAAI,GAAG,QAAQ,GAAG6J,KAAK,CAAC5J,IAAI;MACpP,MAAM3E,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACvD,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAqM,qBAAA,EAAAC,eAAA;UAClC,IAAI7I,CAAC,GAAG;YACJxE,EAAE,EAAEe,OAAO,CAACf,EAAE;YACdyE,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;YACtBC,IAAI,EAAE3D,OAAO,CAAC2D,IAAI;YAClBC,SAAS,GAAAyI,qBAAA,GAAErM,OAAO,CAAC6D,WAAW,cAAAwI,qBAAA,uBAAnBA,qBAAA,CAAqBvI,UAAU,CAAC5E,SAAS;YACpD2E,WAAW,EAAE7D,OAAO,CAAC6D,WAAW;YAChCE,YAAY,EAAE/D,OAAO,CAAC+D,YAAY;YAClCC,cAAc,EAAEhE,OAAO,CAACgE,cAAc;YACtC1E,YAAY,EAAEU,OAAO,CAACV,YAAY;YAClC2E,KAAK,EAAEjE,OAAO,CAACiE,KAAK;YACpBC,OAAO,EAAElE,OAAO,CAACkE,OAAO;YACxBzE,MAAM,GAAA6M,eAAA,GAAEtM,OAAO,CAACiE,KAAK,cAAAqI,eAAA,uBAAbA,eAAA,CAAe7M,MAAM;YAC7B0E,UAAU,EAAEnE,OAAO,CAACmE,UAAU;YAC9BC,KAAK,EAAEpE,OAAO,CAACoE,KAAK;YACpBC,UAAU,EAAErE,OAAO,CAACqE;UACxB,CAAC;UACDjB,SAAS,CAACkB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACX,QAAQ,CAAC;UACVhD,OAAO,EAAEsD,SAAS;UAClBzC,QAAQ,EAAEyC,SAAS;UACnBxB,YAAY,EAAEuB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjCnC,UAAU,EAAE+J,KAAK;UACjBjL,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDuD,KAAK,CAAE5B,CAAC,IAAK;QAAA,IAAA0J,aAAA,EAAAC,aAAA;QACV5H,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;QACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAoH,aAAA,GAAA1J,CAAC,CAACuC,QAAQ,cAAAmH,aAAA,uBAAVA,aAAA,CAAYlJ,IAAI,MAAKgC,SAAS,IAAAmH,aAAA,GAAG3J,CAAC,CAACuC,QAAQ,cAAAoH,aAAA,uBAAVA,aAAA,CAAYnJ,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEkH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAvG,MAAMA,CAACgG,KAAK,EAAE;IACV,IAAI,CAACrJ,QAAQ,CAAC;MAAE5B,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIyL,KAAK,GAAGR,KAAK,CAAC3J,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAG2J,KAAK,CAAC3J,SAAS;IAChG,IAAI,IAAI,CAACiD,eAAe,EAAE;MACtB2G,YAAY,CAAC,IAAI,CAAC3G,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAG0D,UAAU,CAAC,YAAY;MAC1C,IAAInG,GAAG,GAAG,WAAW,GAAG,IAAI,CAACnD,KAAK,CAACkC,KAAK,GAAG,IAAI,CAAClC,KAAK,CAACe,iBAAiB,IAAI,IAAI,CAACf,KAAK,CAACoC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACpC,KAAK,CAACmC,MAAM,GAAG,IAAI,CAACnC,KAAK,CAACoC,gBAAgB,CAACgB,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACpD,KAAK,CAACuC,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACG,IAAI,GAAG,SAAS,GAAGoK,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAC1J,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAM7E,UAAU,CAAC,KAAK,EAAEoF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACvD,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4M,qBAAA,EAAAC,eAAA;UAClC,IAAIpJ,CAAC,GAAG;YACJxE,EAAE,EAAEe,OAAO,CAACf,EAAE;YACdyE,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;YACtBC,IAAI,EAAE3D,OAAO,CAAC2D,IAAI;YAClBC,SAAS,GAAAgJ,qBAAA,GAAE5M,OAAO,CAAC6D,WAAW,cAAA+I,qBAAA,uBAAnBA,qBAAA,CAAqB9I,UAAU,CAAC5E,SAAS;YACpD2E,WAAW,EAAE7D,OAAO,CAAC6D,WAAW;YAChCE,YAAY,EAAE/D,OAAO,CAAC+D,YAAY;YAClCC,cAAc,EAAEhE,OAAO,CAACgE,cAAc;YACtC1E,YAAY,EAAEU,OAAO,CAACV,YAAY;YAClC2E,KAAK,EAAEjE,OAAO,CAACiE,KAAK;YACpBC,OAAO,EAAElE,OAAO,CAACkE,OAAO;YACxBzE,MAAM,GAAAoN,eAAA,GAAE7M,OAAO,CAACiE,KAAK,cAAA4I,eAAA,uBAAbA,eAAA,CAAepN,MAAM;YAC7B0E,UAAU,EAAEnE,OAAO,CAACmE,UAAU;YAC9BC,KAAK,EAAEpE,OAAO,CAACoE,KAAK;YACpBC,UAAU,EAAErE,OAAO,CAACqE;UACxB,CAAC;UACDjB,SAAS,CAACkB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACX,QAAQ,CAAC;UACVhD,OAAO,EAAEsD,SAAS;UAClBzC,QAAQ,EAAEyC,SAAS;UACnBxB,YAAY,EAAEuB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjCnC,UAAU,EAAAsG,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAC7I,KAAK,CAACuC,UAAU;YAAEI,SAAS,EAAE2J,KAAK,CAAC3J,SAAS;YAAEC,SAAS,EAAE0J,KAAK,CAAC1J;UAAS,EAAE;UAChGvB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDuD,KAAK,CAAE5B,CAAC,IAAK;QAAA,IAAAiK,aAAA,EAAAC,aAAA;QACVnI,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;QACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA2H,aAAA,GAAAjK,CAAC,CAACuC,QAAQ,cAAA0H,aAAA,uBAAVA,aAAA,CAAYzJ,IAAI,MAAKgC,SAAS,IAAA0H,aAAA,GAAGlK,CAAC,CAACuC,QAAQ,cAAA2H,aAAA,uBAAVA,aAAA,CAAY1J,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEkH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAtG,QAAQA,CAAC+F,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAACrJ,QAAQ,CAAC;MAAEV,UAAU,EAAE+J;IAAM,CAAC,EAAE,IAAI,CAACa,YAAY,CAAC;EAC3D;EACAxG,gBAAgBA,CAAC3D,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAE7B,iBAAiB,EAAE4B,CAAC,CAACnB;IAAM,CAAC,CAAC;EACjD;EACA;EACAiF,mBAAmBA,CAAChH,MAAM,EAAE;IACxB,IAAI,CAACmD,QAAQ,CAAC;MACVnD,MAAM;MACNuC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAMuE,YAAYA,CAAA,EAAG;IACjB,IAAI3G,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAACiI,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAAC/I,EAAE,KAAK,IAAI,CAACY,KAAK,CAACF,MAAM,CAACV,EAC1C,CAAC;IACD,IAAI,CAAC6D,QAAQ,CAAC;MACVhD,OAAO;MACPoC,kBAAkB,EAAE,KAAK;MACzBvC,MAAM,EAAE,IAAI,CAACX;IACjB,CAAC,CAAC;IACF,IAAIgE,GAAG,GAAG,4BAA4B,GAAG,IAAI,CAACnD,KAAK,CAACF,MAAM,CAACV,EAAE;IAC7D,MAAMrB,UAAU,CAAC,QAAQ,EAAEoF,GAAG,CAAC,CAC1BE,IAAI,CAACC,GAAG,IAAI;MACTyB,OAAO,CAACC,GAAG,CAAC1B,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACyB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,kCAAkC;QAC1CK,IAAI,EAAE;MACV,CAAC,CAAC;MACFlF,MAAM,CAACC,QAAQ,CAAC8I,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC3E,KAAK,CAAE5B,CAAC,IAAK;MAAA,IAAAoK,aAAA,EAAAC,aAAA;MACZtI,OAAO,CAACC,GAAG,CAAChC,CAAC,CAAC;MACd,IAAI,CAACiC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,wEAAAC,MAAA,CAAqE,EAAA8H,aAAA,GAAApK,CAAC,CAACuC,QAAQ,cAAA6H,aAAA,uBAAVA,aAAA,CAAY5J,IAAI,MAAKgC,SAAS,IAAA6H,aAAA,GAAGrK,CAAC,CAACuC,QAAQ,cAAA8H,aAAA,uBAAVA,aAAA,CAAY7J,IAAI,GAAGR,CAAC,CAACyC,OAAO,CAAE;QAC1IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAmB,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC5D,QAAQ,CAAC;MACVZ,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EAWAiL,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpB1O,OAAA,CAACjB,KAAK,CAAC4P,QAAQ;MAAAC,QAAA,eACX5O,OAAA;QAAK6O,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrB5O,OAAA;UAAK6O,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnB5O,OAAA;YAAK6O,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvC5O,OAAA,CAACb,MAAM;cACH0P,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAC5H,kBAAmB;cAAA0H,QAAA,GAEhC,GAAG,EACH3P,QAAQ,CAAC8P,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTnP,OAAA,CAACN,KAAK;cACFgF,SAAS,EAAE,IAAI,CAACvD,KAAK,CAACF,MAAO;cAC7Bc,QAAQ,EAAE,IAAI,CAACZ,KAAK,CAACY,QAAS;cAC9BgB,GAAG,EAAE,IAAI,CAAC5B,KAAK,CAAC4B,GAAI;cACpBqM,GAAG,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAME,mBAAmB,gBACrBrP,OAAA,CAACjB,KAAK,CAAC4P,QAAQ;MAAAC,QAAA,eACX5O,OAAA,CAACb,MAAM;QAAC0P,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC1H,UAAW;QAAAwH,QAAA,GACjE,GAAG,EACH3P,QAAQ,CAAC8P,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMG,mBAAmB,gBACrBtP,OAAA,CAACjB,KAAK,CAAC4P,QAAQ;MAAAC,QAAA,gBACX5O,OAAA,CAACb,MAAM;QAAC0P,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACjH,UAAW;QAAA+G,QAAA,GACjE,GAAG,eACJ5O,OAAA;UAAG6O,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAAClQ,QAAQ,CAACsQ,KAAK,EAAE,GAAG;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACTnP,OAAA,CAACb,MAAM;QAAC0P,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAClH,kBAAmB;QAAAgH,QAAA,GACzE,GAAG,EACH3P,QAAQ,CAAC8P,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMK,wBAAwB,gBAC1BxP,OAAA,CAACjB,KAAK,CAAC4P,QAAQ;MAAAC,QAAA,gBACX5O,OAAA,CAACb,MAAM;QACHyK,KAAK,EAAC,IAAI;QACV6F,IAAI,EAAC,aAAa;QAClBZ,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAAC9G;MAAuB;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFnP,OAAA,CAACb,MAAM;QAAC0P,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC/G,YAAa;QAAA6G,QAAA,GACxD,GAAG,EACH3P,QAAQ,CAACyQ,EAAE,EAAE,GAAG;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMQ,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACI7B,KAAK,EAAE,QAAQ;MACf8B,MAAM,EAAE9Q,QAAQ,CAAC+Q,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,WAAW;MAClB8B,MAAM,EAAE9Q,QAAQ,CAACmR,SAAS;MAC1BH,IAAI,EAAE,WAAW;MACjBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,cAAc;MACrB8B,MAAM,EAAE9Q,QAAQ,CAACoR,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,cAAc;MACrB8B,MAAM,EAAE9Q,QAAQ,CAACqR,KAAK;MACtBL,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,+BAA+B;MACtC8B,MAAM,EAAE9Q,QAAQ,CAACsR,YAAY;MAC7BN,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,gCAAgC;MACvC8B,MAAM,EAAE9Q,QAAQ,CAACuR,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,cAAc;MACrB8B,MAAM,EAAE9Q,QAAQ,CAACwR,SAAS;MAC1BR,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,SAAS;MAChB8B,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIlC,KAAK,EAAE,YAAY;MACnB8B,MAAM,EAAE9Q,QAAQ,CAACyR,IAAI;MACrBT,IAAI,EAAE,YAAY;MAClBE,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMQ,YAAY,GAAG,CACjB;MAAEtM,IAAI,EAAEpF,QAAQ,CAAC2R,OAAO;MAAEnB,IAAI,eAAEzP,OAAA;QAAG6O,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC7J;IAAe,CAAC,EAC3F;MAAE3C,IAAI,EAAEpF,QAAQ,CAAC6R,SAAS;MAAErB,IAAI,eAAEzP,OAAA;QAAG6O,SAAS,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAClJ;IAAS,CAAC,EACxF;MAAEtD,IAAI,EAAEpF,QAAQ,CAAC8R,kBAAkB;MAAEtB,IAAI,eAAEzP,OAAA;QAAG6O,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC1J,UAAU;MAAEpG,MAAM,EAAE,QAAQ;MAAEiQ,OAAO,EAAE;IAAU,CAAC,EAC/I;MAAE3M,IAAI,EAAEpF,QAAQ,CAACgS,YAAY;MAAExB,IAAI,eAAEzP,OAAA;QAAG6O,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC7P;IAAY,CAAC,EAChG;MAAEqD,IAAI,EAAEpF,QAAQ,CAACiS,WAAW;MAAEzB,IAAI,eAAEzP,OAAA;QAAG6O,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC3I;IAAc,CAAC,EACjG;MAAE7D,IAAI,EAAEpF,QAAQ,CAACkS,OAAO;MAAE1B,IAAI,eAAEzP,OAAA;QAAG6O,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC5I;IAAoB,CAAC,CACrG;IACD,oBACIjI,OAAA;MAAK6O,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9C5O,OAAA,CAACP,KAAK;QAAC2R,GAAG,EAAGnH,EAAE,IAAK,IAAI,CAAC7D,KAAK,GAAG6D;MAAG;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCnP,OAAA,CAACH,GAAG;QAAAmP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPnP,OAAA;QAAK6O,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnC5O,OAAA;UAAA4O,QAAA,EAAK3P,QAAQ,CAACoS;QAA2B;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACNnP,OAAA;QAAK6O,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjB5O,OAAA,CAACF,eAAe;UACZsR,GAAG,EAAGnH,EAAE,IAAK,IAAI,CAACqH,EAAE,GAAGrH,EAAG;UAC1BjH,KAAK,EAAE,IAAI,CAAC7B,KAAK,CAACC,OAAQ;UAC1BoB,OAAO,EAAE,IAAI,CAACrB,KAAK,CAACqB,OAAQ;UAC5BmN,MAAM,EAAEA,MAAO;UACf4B,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTlK,MAAM,EAAE,IAAI,CAACA,MAAO;UACpB7D,KAAK,EAAE,IAAI,CAACxC,KAAK,CAACuC,UAAU,CAACC,KAAM;UACnCT,YAAY,EAAE,IAAI,CAAC/B,KAAK,CAAC+B,YAAa;UACtCU,IAAI,EAAE,IAAI,CAACzC,KAAK,CAACuC,UAAU,CAACE,IAAK;UACjC+N,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEjB,YAAa;UAC5BkB,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BlC,aAAa,EAAC,UAAU;UACxBmC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAAChL,cAAe;UAClCiL,SAAS,EAAE,IAAI,CAAC9Q,KAAK,CAACoB,iBAAkB;UACxC2P,iBAAiB,EAAG/N,CAAC,IAAK,IAAI,CAAC2D,gBAAgB,CAAC3D,CAAC,CAAE;UACnDgO,eAAe,EAAE,IAAI,CAAChR,KAAK,CAACsC,IAAI,KAAKlE,YAAY,GAAG,IAAI,GAAG,KAAM;UACjE6S,iBAAiB,EAAE,IAAI,CAAC7K,kBAAmB;UAC3C8K,gBAAgB,EAAEpT,QAAQ,CAACsI,kBAAmB;UAC9C+K,mBAAmB,EAAE,CAAC,IAAI,CAACnR,KAAK,CAACoB,iBAAiB,IAAI,CAAC,IAAI,CAACpB,KAAK,CAACoB,iBAAiB,CAAC2H,MAAO;UAC3FzC,MAAM,EAAE,IAAI,CAACA,MAAO;UACpB3D,SAAS,EAAE,IAAI,CAAC3C,KAAK,CAACuC,UAAU,CAACI,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACuC,UAAU,CAACK,SAAU;UAC3C2D,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxB1D,OAAO,EAAE,IAAI,CAAC7C,KAAK,CAACuC,UAAU,CAACM,OAAQ;UACvCuO,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAU;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnP,OAAA,CAACZ,MAAM;QACHqT,OAAO,EAAE,IAAI,CAACtR,KAAK,CAACsB,aAAc;QAClCsN,MAAM,EAAE9Q,QAAQ,CAACyT,MAAO;QACxBC,KAAK;QACL9D,SAAS,EAAC,kBAAkB;QAC5B+D,MAAM,EAAElE,kBAAmB;QAC3BmE,MAAM,EAAE,IAAI,CAAC3L,kBAAmB;QAChC4L,SAAS,EAAE,KAAM;QAAAlE,QAAA,eAEjB5O,OAAA,CAACJ,mBAAmB;UAChBqB,MAAM,EAAE,IAAI,CAACE,KAAK,CAACY,QAAS;UAC5BX,OAAO,EAAE,IAAI,CAACD,KAAK,CAACF,MAAO;UAC3ByD,SAAS,EAAE,IAAI,CAACvD,KAAK,CAACF,MAAO;UAC7B8R,MAAM,EAAE;QAAK;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAETnP,OAAA,CAACZ,MAAM;QACHqT,OAAO,EAAE,IAAI,CAACtR,KAAK,CAACwB,aAAc;QAClCoN,MAAM,EAAE,IAAI,CAAC5O,KAAK,CAAC4B,GAAI;QACvB4P,KAAK;QACL9D,SAAS,EAAC,kBAAkB;QAC5B+D,MAAM,EAAEvD,mBAAoB;QAC5BwD,MAAM,EAAE,IAAI,CAACzL,UAAW;QAAAwH,QAAA,eAExB5O,OAAA,CAACL,kBAAkB;UAACsB,MAAM,EAAE,IAAI,CAACE,KAAK,CAACF,MAAO;UAAC4B,KAAK,EAAE,IAAI,CAACA,KAAM;UAACC,OAAO,EAAE,IAAI,CAAC3B,KAAK,CAAC2B,OAAQ;UAACvD,YAAY,EAAE;QAAK;UAAAyP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC,eACTnP,OAAA,CAACZ,MAAM;QACHqT,OAAO,EAAE,IAAI,CAACtR,KAAK,CAACyB,aAAc;QAClCmN,MAAM,EAAE9Q,QAAQ,CAAC6R,SAAU;QAC3B6B,KAAK;QACL9D,SAAS,EAAC,kBAAkB;QAC5B+D,MAAM,EAAEtD,mBAAoB;QAC5BuD,MAAM,EAAE,IAAI,CAACjL,kBAAmB;QAAAgH,QAAA,eAEhC5O,OAAA;UAAK6O,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAChB5O,OAAA;YAAK6O,SAAS,EAAC,QAAQ;YAAAD,QAAA,eAAC5O,OAAA;cAAA4O,QAAA,EAAO3P,QAAQ,CAAC+T;YAAe;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrEnP,OAAA;YAAK6O,SAAS,EAAC,QAAQ;YAAAD,QAAA,eACnB5O,OAAA,CAACV,SAAS;cAAC0D,KAAK,EAAE,IAAI,CAAC7B,KAAK,CAACiC,YAAa;cAAC6P,QAAQ,EAAG9O,CAAC,IAAK,IAAI,CAACC,QAAQ,CAAC;gBAAEhB,YAAY,EAAEe,CAAC,CAAC+O,MAAM,CAAClQ;cAAM,CAAC;YAAE;cAAAgM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,eACNnP,OAAA;YAAK6O,SAAS,EAAC,QAAQ;YAAAD,QAAA,eAAC5O,OAAA;cAAM6O,SAAS,EAAC,aAAa;cAAAD,QAAA,GAAC,IAAE,EAAC3P,QAAQ,CAACkU,WAAW;YAAA;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETnP,OAAA,CAACZ,MAAM;QACHqT,OAAO,EAAE,IAAI,CAACtR,KAAK,CAACqC,kBAAmB;QACvCuM,MAAM,EAAE9Q,QAAQ,CAACmU,QAAS;QAC1BT,KAAK;QACLC,MAAM,EAAEpD,wBAAyB;QACjCqD,MAAM,EAAE,IAAI,CAAC7K,sBAAuB;QAAA4G,QAAA,eAEpC5O,OAAA;UAAK6O,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjC5O,OAAA;YACI6O,SAAS,EAAC,mCAAmC;YAC7C9C,KAAK,EAAE;cAAEsH,QAAQ,EAAE;YAAO;UAAE;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAAChO,KAAK,CAACF,MAAM,iBACdjB,OAAA;YAAA4O,QAAA,GACK3P,QAAQ,CAACqU,YAAY,EAAC,GAAC,eAAAtT,OAAA;cAAA4O,QAAA,GAAI,IAAI,CAACzN,KAAK,CAACF,MAAM,CAAC+D,MAAM,EAAC,GAAC;YAAA;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EACR,IAAI,CAAChO,KAAK,CAACuB,SAAS,iBACjB1C,OAAA,CAACX,UAAU;QAACkU,KAAK,EAAC,oBAAoB;QAACC,OAAO,EAAC,yBAAyB;QAACN,MAAM,EAAC;MAAS;QAAAlE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE/F,CAAC;EAEd;AACJ;AAEA,eAAelP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}