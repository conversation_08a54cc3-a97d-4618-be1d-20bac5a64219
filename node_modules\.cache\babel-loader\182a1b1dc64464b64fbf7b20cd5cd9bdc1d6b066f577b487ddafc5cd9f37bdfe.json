{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../util';\nvar ASCEND = 'ascend';\nvar DESCEND = 'descend';\nfunction getMultiplePriority(column) {\n  if (_typeof(column.sorter) === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n}\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && _typeof(sorter) === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n}\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\nfunction collectSortStates(columns, init, pos) {\n  var sortStates = [];\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column: column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n  (columns || []).forEach(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var newColumn = column;\n    if (newColumn.sorter) {\n      var sortDirections = newColumn.sortDirections || defaultSortDirections;\n      var showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var sorterState = sorterStates.find(function (_ref) {\n        var key = _ref.key;\n        return key === columnKey;\n      });\n      var sorterOrder = sorterState ? sorterState.sortOrder : null;\n      var nextSortOrder = nextSortDirection(sortDirections, sorterOrder);\n      var upNode = sortDirections.includes(ASCEND) && /*#__PURE__*/React.createElement(CaretUpOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-up\"), {\n          active: sorterOrder === ASCEND\n        })\n      });\n      var downNode = sortDirections.includes(DESCEND) && /*#__PURE__*/React.createElement(CaretDownOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-down\"), {\n          active: sorterOrder === DESCEND\n        })\n      });\n      var _ref2 = tableLocale || {},\n        cancelSort = _ref2.cancelSort,\n        triggerAsc = _ref2.triggerAsc,\n        triggerDesc = _ref2.triggerDesc;\n      var sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      var tooltipProps = _typeof(showSorterTooltip) === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = _extends(_extends({}, newColumn), {\n        className: classNames(newColumn.className, _defineProperty({}, \"\".concat(prefixCls, \"-column-sort\"), sorterOrder)),\n        title: function title(renderProps) {\n          var renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-column-sorters\")\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-title\")\n          }, renderColumnTitle(column.title, renderProps)), /*#__PURE__*/React.createElement(\"span\", {\n            className: classNames(\"\".concat(prefixCls, \"-column-sorter\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-sorter-full\"), !!(upNode && downNode)))\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-sorter-inner\")\n          }, upNode, downNode)));\n          return showSorterTooltip ? /*#__PURE__*/React.createElement(Tooltip, tooltipProps, renderSortTitle) : renderSortTitle;\n        },\n        onHeaderCell: function onHeaderCell(col) {\n          var cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          var originOnClick = cell.onClick;\n          var originOKeyDown = cell.onKeyDown;\n          cell.onClick = function (event) {\n            triggerSorter({\n              column: column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = function (event) {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column: column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          }; // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n\n          if (sorterOrder) {\n            if (sorterOrder === 'ascend') {\n              cell['aria-sort'] = 'ascending';\n            } else {\n              cell['aria-sort'] = 'descending';\n            }\n          }\n          cell.className = classNames(cell.className, \"\".concat(prefixCls, \"-column-has-sorters\"));\n          cell.tabIndex = 0;\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction stateToInfo(sorterStates) {\n  var column = sorterStates.column,\n    sortOrder = sorterStates.sortOrder;\n  return {\n    column: column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\nfunction generateSorterInfo(sorterStates) {\n  var list = sorterStates.filter(function (_ref3) {\n    var sortOrder = _ref3.sortOrder;\n    return sortOrder;\n  }).map(stateToInfo); // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n\n  if (list.length === 0 && sorterStates.length) {\n    return _extends(_extends({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n  return list;\n}\nexport function getSortData(data, sortStates, childrenColumnName) {\n  var innerSorterStates = sortStates.slice().sort(function (a, b) {\n    return b.multiplePriority - a.multiplePriority;\n  });\n  var cloneData = data.slice();\n  var runningSorters = innerSorterStates.filter(function (_ref4) {\n    var sorter = _ref4.column.sorter,\n      sortOrder = _ref4.sortOrder;\n    return getSortFunction(sorter) && sortOrder;\n  }); // Skip if no sorter needed\n\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort(function (record1, record2) {\n    for (var i = 0; i < runningSorters.length; i += 1) {\n      var sorterState = runningSorters[i];\n      var sorter = sorterState.column.sorter,\n        sortOrder = sorterState.sortOrder;\n      var compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        var compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(function (record) {\n    var subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return _extends(_extends({}, record), _defineProperty({}, childrenColumnName, getSortData(subRecords, sortStates, childrenColumnName)));\n    }\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref5) {\n  var prefixCls = _ref5.prefixCls,\n    mergedColumns = _ref5.mergedColumns,\n    onSorterChange = _ref5.onSorterChange,\n    sortDirections = _ref5.sortDirections,\n    tableLocale = _ref5.tableLocale,\n    showSorterTooltip = _ref5.showSorterTooltip;\n  var _React$useState = React.useState(collectSortStates(mergedColumns, true)),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    sortStates = _React$useState2[0],\n    setSortStates = _React$useState2[1];\n  var mergedSorterStates = React.useMemo(function () {\n    var validate = true;\n    var collectedStates = collectSortStates(mergedColumns, false); // Return if not controlled\n\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n    var validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(_extends(_extends({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    var multipleMode = null;\n    collectedStates.forEach(function (state) {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]); // Get render columns title required props\n\n  var columnTitleSorterProps = React.useMemo(function () {\n    var sortColumns = mergedSorterStates.map(function (_ref6) {\n      var column = _ref6.column,\n        sortOrder = _ref6.sortOrder;\n      return {\n        column: column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns: sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n  function triggerSorter(sortState) {\n    var newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(function (_ref7) {\n        var key = _ref7.key;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  };\n  var getSorters = function getSorters() {\n    return generateSorterInfo(mergedSorterStates);\n  };\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}", "map": {"version": 3, "names": ["_slicedToArray", "_defineProperty", "_extends", "_toConsumableArray", "_typeof", "React", "classNames", "CaretDownOutlined", "CaretUpOutlined", "KeyCode", "<PERSON><PERSON><PERSON>", "getColumnKey", "getColumnPos", "renderColumnTitle", "ASCEND", "DESCEND", "getMultiplePriority", "column", "sorter", "multiple", "getSortFunction", "compare", "nextSortDirection", "sortDirections", "current", "indexOf", "collectSortStates", "columns", "init", "pos", "sortStates", "pushState", "columnPos", "push", "key", "multiplePriority", "sortOrder", "for<PERSON>ach", "index", "children", "concat", "defaultSortOrder", "injectSorter", "prefixCls", "sorterStates", "triggerSorter", "defaultSortDirections", "tableLocale", "tableShowSorterTooltip", "map", "newColumn", "showSorterTooltip", "undefined", "column<PERSON>ey", "sorterState", "find", "_ref", "sorterOrder", "nextSortOrder", "upNode", "includes", "createElement", "className", "active", "downNode", "_ref2", "cancelSort", "triggerAsc", "triggerDesc", "sortTip", "tooltipProps", "title", "renderProps", "renderSortTitle", "onHeaderCell", "col", "cell", "originOnClick", "onClick", "originOKeyDown", "onKeyDown", "event", "keyCode", "ENTER", "tabIndex", "stateToInfo", "order", "field", "dataIndex", "generateSorterInfo", "list", "filter", "_ref3", "length", "getSortData", "data", "childrenColumnName", "innerSorterStates", "slice", "sort", "a", "b", "cloneData", "running<PERSON><PERSON><PERSON>", "_ref4", "record1", "record2", "i", "compareFn", "compareResult", "record", "subRecords", "use<PERSON>ilter<PERSON><PERSON>er", "_ref5", "mergedColumns", "onSorterChange", "_React$useState", "useState", "_React$useState2", "setSortStates", "mergedSorterStates", "useMemo", "validate", "collectedStates", "validateStates", "patchStates", "state", "multipleMode", "columnTitleSorterProps", "sortColumns", "_ref6", "sortColumn", "sortState", "newSorterStates", "_ref7", "transformColumns", "innerColumns", "getSorters"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/hooks/useSorter.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../util';\nvar ASCEND = 'ascend';\nvar DESCEND = 'descend';\n\nfunction getMultiplePriority(column) {\n  if (_typeof(column.sorter) === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n\n  return false;\n}\n\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n\n  if (sorter && _typeof(sorter) === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n\n  return false;\n}\n\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\n\nfunction collectSortStates(columns, init, pos) {\n  var sortStates = [];\n\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column: column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n\n  (columns || []).forEach(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\n\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var newColumn = column;\n\n    if (newColumn.sorter) {\n      var sortDirections = newColumn.sortDirections || defaultSortDirections;\n      var showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var sorterState = sorterStates.find(function (_ref) {\n        var key = _ref.key;\n        return key === columnKey;\n      });\n      var sorterOrder = sorterState ? sorterState.sortOrder : null;\n      var nextSortOrder = nextSortDirection(sortDirections, sorterOrder);\n      var upNode = sortDirections.includes(ASCEND) && /*#__PURE__*/React.createElement(CaretUpOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-up\"), {\n          active: sorterOrder === ASCEND\n        })\n      });\n      var downNode = sortDirections.includes(DESCEND) && /*#__PURE__*/React.createElement(CaretDownOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-down\"), {\n          active: sorterOrder === DESCEND\n        })\n      });\n\n      var _ref2 = tableLocale || {},\n          cancelSort = _ref2.cancelSort,\n          triggerAsc = _ref2.triggerAsc,\n          triggerDesc = _ref2.triggerDesc;\n\n      var sortTip = cancelSort;\n\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n\n      var tooltipProps = _typeof(showSorterTooltip) === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = _extends(_extends({}, newColumn), {\n        className: classNames(newColumn.className, _defineProperty({}, \"\".concat(prefixCls, \"-column-sort\"), sorterOrder)),\n        title: function title(renderProps) {\n          var renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-column-sorters\")\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-title\")\n          }, renderColumnTitle(column.title, renderProps)), /*#__PURE__*/React.createElement(\"span\", {\n            className: classNames(\"\".concat(prefixCls, \"-column-sorter\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-sorter-full\"), !!(upNode && downNode)))\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-sorter-inner\")\n          }, upNode, downNode)));\n          return showSorterTooltip ? /*#__PURE__*/React.createElement(Tooltip, tooltipProps, renderSortTitle) : renderSortTitle;\n        },\n        onHeaderCell: function onHeaderCell(col) {\n          var cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          var originOnClick = cell.onClick;\n          var originOKeyDown = cell.onKeyDown;\n\n          cell.onClick = function (event) {\n            triggerSorter({\n              column: column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n\n          cell.onKeyDown = function (event) {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column: column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          }; // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n\n\n          if (sorterOrder) {\n            if (sorterOrder === 'ascend') {\n              cell['aria-sort'] = 'ascending';\n            } else {\n              cell['aria-sort'] = 'descending';\n            }\n          }\n\n          cell.className = classNames(cell.className, \"\".concat(prefixCls, \"-column-has-sorters\"));\n          cell.tabIndex = 0;\n          return cell;\n        }\n      });\n    }\n\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n\n    return newColumn;\n  });\n}\n\nfunction stateToInfo(sorterStates) {\n  var column = sorterStates.column,\n      sortOrder = sorterStates.sortOrder;\n  return {\n    column: column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\n\nfunction generateSorterInfo(sorterStates) {\n  var list = sorterStates.filter(function (_ref3) {\n    var sortOrder = _ref3.sortOrder;\n    return sortOrder;\n  }).map(stateToInfo); // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n\n  if (list.length === 0 && sorterStates.length) {\n    return _extends(_extends({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n\n  return list;\n}\n\nexport function getSortData(data, sortStates, childrenColumnName) {\n  var innerSorterStates = sortStates.slice().sort(function (a, b) {\n    return b.multiplePriority - a.multiplePriority;\n  });\n  var cloneData = data.slice();\n  var runningSorters = innerSorterStates.filter(function (_ref4) {\n    var sorter = _ref4.column.sorter,\n        sortOrder = _ref4.sortOrder;\n    return getSortFunction(sorter) && sortOrder;\n  }); // Skip if no sorter needed\n\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n\n  return cloneData.sort(function (record1, record2) {\n    for (var i = 0; i < runningSorters.length; i += 1) {\n      var sorterState = runningSorters[i];\n      var sorter = sorterState.column.sorter,\n          sortOrder = sorterState.sortOrder;\n      var compareFn = getSortFunction(sorter);\n\n      if (compareFn && sortOrder) {\n        var compareResult = compareFn(record1, record2, sortOrder);\n\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n\n    return 0;\n  }).map(function (record) {\n    var subRecords = record[childrenColumnName];\n\n    if (subRecords) {\n      return _extends(_extends({}, record), _defineProperty({}, childrenColumnName, getSortData(subRecords, sortStates, childrenColumnName)));\n    }\n\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref5) {\n  var prefixCls = _ref5.prefixCls,\n      mergedColumns = _ref5.mergedColumns,\n      onSorterChange = _ref5.onSorterChange,\n      sortDirections = _ref5.sortDirections,\n      tableLocale = _ref5.tableLocale,\n      showSorterTooltip = _ref5.showSorterTooltip;\n\n  var _React$useState = React.useState(collectSortStates(mergedColumns, true)),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      sortStates = _React$useState2[0],\n      setSortStates = _React$useState2[1];\n\n  var mergedSorterStates = React.useMemo(function () {\n    var validate = true;\n    var collectedStates = collectSortStates(mergedColumns, false); // Return if not controlled\n\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n\n    var validateStates = [];\n\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(_extends(_extends({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n\n    var multipleMode = null;\n    collectedStates.forEach(function (state) {\n      if (multipleMode === null) {\n        patchStates(state);\n\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]); // Get render columns title required props\n\n  var columnTitleSorterProps = React.useMemo(function () {\n    var sortColumns = mergedSorterStates.map(function (_ref6) {\n      var column = _ref6.column,\n          sortOrder = _ref6.sortOrder;\n      return {\n        column: column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns: sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n\n  function triggerSorter(sortState) {\n    var newSorterStates;\n\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(function (_ref7) {\n        var key = _ref7.key;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  };\n\n  var getSorters = function getSorters() {\n    return generateSorterInfo(mergedSorterStates);\n  };\n\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,SAAS;AACvE,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,OAAO,GAAG,SAAS;AAEvB,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnC,IAAIb,OAAO,CAACa,MAAM,CAACC,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACC,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACrF,OAAOF,MAAM,CAACC,MAAM,CAACC,QAAQ;EAC/B;EAEA,OAAO,KAAK;AACd;AAEA,SAASC,eAAeA,CAACF,MAAM,EAAE;EAC/B,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM;EACf;EAEA,IAAIA,MAAM,IAAId,OAAO,CAACc,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACG,OAAO,EAAE;IAC5D,OAAOH,MAAM,CAACG,OAAO;EACvB;EAEA,OAAO,KAAK;AACd;AAEA,SAASC,iBAAiBA,CAACC,cAAc,EAAEC,OAAO,EAAE;EAClD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOD,cAAc,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAOA,cAAc,CAACA,cAAc,CAACE,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5D;AAEA,SAASE,iBAAiBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAC7C,IAAIC,UAAU,GAAG,EAAE;EAEnB,SAASC,SAASA,CAACd,MAAM,EAAEe,SAAS,EAAE;IACpCF,UAAU,CAACG,IAAI,CAAC;MACdhB,MAAM,EAAEA,MAAM;MACdiB,GAAG,EAAEvB,YAAY,CAACM,MAAM,EAAEe,SAAS,CAAC;MACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;MAC7CmB,SAAS,EAAEnB,MAAM,CAACmB;IACpB,CAAC,CAAC;EACJ;EAEA,CAACT,OAAO,IAAI,EAAE,EAAEU,OAAO,CAAC,UAAUpB,MAAM,EAAEqB,KAAK,EAAE;IAC/C,IAAIN,SAAS,GAAGpB,YAAY,CAAC0B,KAAK,EAAET,GAAG,CAAC;IAExC,IAAIZ,MAAM,CAACsB,QAAQ,EAAE;MACnB,IAAI,WAAW,IAAItB,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B;MAEAF,UAAU,GAAG,EAAE,CAACU,MAAM,CAACrC,kBAAkB,CAAC2B,UAAU,CAAC,EAAE3B,kBAAkB,CAACuB,iBAAiB,CAACT,MAAM,CAACsB,QAAQ,EAAEX,IAAI,EAAEI,SAAS,CAAC,CAAC,CAAC;IACjI,CAAC,MAAM,IAAIf,MAAM,CAACC,MAAM,EAAE;MACxB,IAAI,WAAW,IAAID,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B,CAAC,MAAM,IAAIJ,IAAI,IAAIX,MAAM,CAACwB,gBAAgB,EAAE;QAC1C;QACAX,UAAU,CAACG,IAAI,CAAC;UACdhB,MAAM,EAAEA,MAAM;UACdiB,GAAG,EAAEvB,YAAY,CAACM,MAAM,EAAEe,SAAS,CAAC;UACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;UAC7CmB,SAAS,EAAEnB,MAAM,CAACwB;QACpB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EACF,OAAOX,UAAU;AACnB;AAEA,SAASY,YAAYA,CAACC,SAAS,EAAEhB,OAAO,EAAEiB,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEnB,GAAG,EAAE;EACtI,OAAO,CAACF,OAAO,IAAI,EAAE,EAAEsB,GAAG,CAAC,UAAUhC,MAAM,EAAEqB,KAAK,EAAE;IAClD,IAAIN,SAAS,GAAGpB,YAAY,CAAC0B,KAAK,EAAET,GAAG,CAAC;IACxC,IAAIqB,SAAS,GAAGjC,MAAM;IAEtB,IAAIiC,SAAS,CAAChC,MAAM,EAAE;MACpB,IAAIK,cAAc,GAAG2B,SAAS,CAAC3B,cAAc,IAAIuB,qBAAqB;MACtE,IAAIK,iBAAiB,GAAGD,SAAS,CAACC,iBAAiB,KAAKC,SAAS,GAAGJ,sBAAsB,GAAGE,SAAS,CAACC,iBAAiB;MACxH,IAAIE,SAAS,GAAG1C,YAAY,CAACuC,SAAS,EAAElB,SAAS,CAAC;MAClD,IAAIsB,WAAW,GAAGV,YAAY,CAACW,IAAI,CAAC,UAAUC,IAAI,EAAE;QAClD,IAAItB,GAAG,GAAGsB,IAAI,CAACtB,GAAG;QAClB,OAAOA,GAAG,KAAKmB,SAAS;MAC1B,CAAC,CAAC;MACF,IAAII,WAAW,GAAGH,WAAW,GAAGA,WAAW,CAAClB,SAAS,GAAG,IAAI;MAC5D,IAAIsB,aAAa,GAAGpC,iBAAiB,CAACC,cAAc,EAAEkC,WAAW,CAAC;MAClE,IAAIE,MAAM,GAAGpC,cAAc,CAACqC,QAAQ,CAAC9C,MAAM,CAAC,IAAI,aAAaT,KAAK,CAACwD,aAAa,CAACrD,eAAe,EAAE;QAChGsD,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACkC,MAAM,CAACG,SAAS,EAAE,mBAAmB,CAAC,EAAE;UAC/DoB,MAAM,EAAEN,WAAW,KAAK3C;QAC1B,CAAC;MACH,CAAC,CAAC;MACF,IAAIkD,QAAQ,GAAGzC,cAAc,CAACqC,QAAQ,CAAC7C,OAAO,CAAC,IAAI,aAAaV,KAAK,CAACwD,aAAa,CAACtD,iBAAiB,EAAE;QACrGuD,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACkC,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,EAAE;UACjEoB,MAAM,EAAEN,WAAW,KAAK1C;QAC1B,CAAC;MACH,CAAC,CAAC;MAEF,IAAIkD,KAAK,GAAGlB,WAAW,IAAI,CAAC,CAAC;QACzBmB,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;QAC7BC,WAAW,GAAGH,KAAK,CAACG,WAAW;MAEnC,IAAIC,OAAO,GAAGH,UAAU;MAExB,IAAIR,aAAa,KAAK3C,OAAO,EAAE;QAC7BsD,OAAO,GAAGD,WAAW;MACvB,CAAC,MAAM,IAAIV,aAAa,KAAK5C,MAAM,EAAE;QACnCuD,OAAO,GAAGF,UAAU;MACtB;MAEA,IAAIG,YAAY,GAAGlE,OAAO,CAAC+C,iBAAiB,CAAC,KAAK,QAAQ,GAAGA,iBAAiB,GAAG;QAC/EoB,KAAK,EAAEF;MACT,CAAC;MACDnB,SAAS,GAAGhD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgD,SAAS,CAAC,EAAE;QAC5CY,SAAS,EAAExD,UAAU,CAAC4C,SAAS,CAACY,SAAS,EAAE7D,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuC,MAAM,CAACG,SAAS,EAAE,cAAc,CAAC,EAAEc,WAAW,CAAC,CAAC;QAClHc,KAAK,EAAE,SAASA,KAAKA,CAACC,WAAW,EAAE;UACjC,IAAIC,eAAe,GAAG,aAAapE,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE;YAC5DC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,iBAAiB;UACnD,CAAC,EAAE,aAAatC,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;YAC1CC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,eAAe;UACjD,CAAC,EAAE9B,iBAAiB,CAACI,MAAM,CAACsD,KAAK,EAAEC,WAAW,CAAC,CAAC,EAAE,aAAanE,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;YACzFC,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACkC,MAAM,CAACG,SAAS,EAAE,gBAAgB,CAAC,EAAE1C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuC,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,EAAE,CAAC,EAAEgB,MAAM,IAAIK,QAAQ,CAAC,CAAC;UACxJ,CAAC,EAAE,aAAa3D,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;YAC1CC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,sBAAsB;UACxD,CAAC,EAAEgB,MAAM,EAAEK,QAAQ,CAAC,CAAC,CAAC;UACtB,OAAOb,iBAAiB,GAAG,aAAa9C,KAAK,CAACwD,aAAa,CAACnD,OAAO,EAAE4D,YAAY,EAAEG,eAAe,CAAC,GAAGA,eAAe;QACvH,CAAC;QACDC,YAAY,EAAE,SAASA,YAAYA,CAACC,GAAG,EAAE;UACvC,IAAIC,IAAI,GAAG3D,MAAM,CAACyD,YAAY,IAAIzD,MAAM,CAACyD,YAAY,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;UAChE,IAAIE,aAAa,GAAGD,IAAI,CAACE,OAAO;UAChC,IAAIC,cAAc,GAAGH,IAAI,CAACI,SAAS;UAEnCJ,IAAI,CAACE,OAAO,GAAG,UAAUG,KAAK,EAAE;YAC9BpC,aAAa,CAAC;cACZ5B,MAAM,EAAEA,MAAM;cACdiB,GAAG,EAAEmB,SAAS;cACdjB,SAAS,EAAEsB,aAAa;cACxBvB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;YAC9C,CAAC,CAAC;YACF4D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,KAAK,CAAC;UACpF,CAAC;UAEDL,IAAI,CAACI,SAAS,GAAG,UAAUC,KAAK,EAAE;YAChC,IAAIA,KAAK,CAACC,OAAO,KAAKzE,OAAO,CAAC0E,KAAK,EAAE;cACnCtC,aAAa,CAAC;gBACZ5B,MAAM,EAAEA,MAAM;gBACdiB,GAAG,EAAEmB,SAAS;gBACdjB,SAAS,EAAEsB,aAAa;gBACxBvB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;cAC9C,CAAC,CAAC;cACF8D,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,KAAK,CAAC;YACvF;UACF,CAAC,CAAC,CAAC;;UAGH,IAAIxB,WAAW,EAAE;YACf,IAAIA,WAAW,KAAK,QAAQ,EAAE;cAC5BmB,IAAI,CAAC,WAAW,CAAC,GAAG,WAAW;YACjC,CAAC,MAAM;cACLA,IAAI,CAAC,WAAW,CAAC,GAAG,YAAY;YAClC;UACF;UAEAA,IAAI,CAACd,SAAS,GAAGxD,UAAU,CAACsE,IAAI,CAACd,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,CAAC;UACxFiC,IAAI,CAACQ,QAAQ,GAAG,CAAC;UACjB,OAAOR,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,UAAU,IAAI1B,SAAS,EAAE;MAC3BA,SAAS,GAAGhD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgD,SAAS,CAAC,EAAE;QAC5CX,QAAQ,EAAEG,YAAY,CAACC,SAAS,EAAEO,SAAS,CAACX,QAAQ,EAAEK,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEhB,SAAS;MAC1J,CAAC,CAAC;IACJ;IAEA,OAAOkB,SAAS;EAClB,CAAC,CAAC;AACJ;AAEA,SAASmC,WAAWA,CAACzC,YAAY,EAAE;EACjC,IAAI3B,MAAM,GAAG2B,YAAY,CAAC3B,MAAM;IAC5BmB,SAAS,GAAGQ,YAAY,CAACR,SAAS;EACtC,OAAO;IACLnB,MAAM,EAAEA,MAAM;IACdqE,KAAK,EAAElD,SAAS;IAChBmD,KAAK,EAAEtE,MAAM,CAACuE,SAAS;IACvBnC,SAAS,EAAEpC,MAAM,CAACiB;EACpB,CAAC;AACH;AAEA,SAASuD,kBAAkBA,CAAC7C,YAAY,EAAE;EACxC,IAAI8C,IAAI,GAAG9C,YAAY,CAAC+C,MAAM,CAAC,UAAUC,KAAK,EAAE;IAC9C,IAAIxD,SAAS,GAAGwD,KAAK,CAACxD,SAAS;IAC/B,OAAOA,SAAS;EAClB,CAAC,CAAC,CAACa,GAAG,CAACoC,WAAW,CAAC,CAAC,CAAC;EACrB;;EAEA,IAAIK,IAAI,CAACG,MAAM,KAAK,CAAC,IAAIjD,YAAY,CAACiD,MAAM,EAAE;IAC5C,OAAO3F,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmF,WAAW,CAACzC,YAAY,CAACA,YAAY,CAACiD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAChF5E,MAAM,EAAEmC;IACV,CAAC,CAAC;EACJ;EAEA,IAAIsC,IAAI,CAACG,MAAM,IAAI,CAAC,EAAE;IACpB,OAAOH,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACtB;EAEA,OAAOA,IAAI;AACb;AAEA,OAAO,SAASI,WAAWA,CAACC,IAAI,EAAEjE,UAAU,EAAEkE,kBAAkB,EAAE;EAChE,IAAIC,iBAAiB,GAAGnE,UAAU,CAACoE,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC9D,OAAOA,CAAC,CAAClE,gBAAgB,GAAGiE,CAAC,CAACjE,gBAAgB;EAChD,CAAC,CAAC;EACF,IAAImE,SAAS,GAAGP,IAAI,CAACG,KAAK,CAAC,CAAC;EAC5B,IAAIK,cAAc,GAAGN,iBAAiB,CAACN,MAAM,CAAC,UAAUa,KAAK,EAAE;IAC7D,IAAItF,MAAM,GAAGsF,KAAK,CAACvF,MAAM,CAACC,MAAM;MAC5BkB,SAAS,GAAGoE,KAAK,CAACpE,SAAS;IAC/B,OAAOhB,eAAe,CAACF,MAAM,CAAC,IAAIkB,SAAS;EAC7C,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAI,CAACmE,cAAc,CAACV,MAAM,EAAE;IAC1B,OAAOS,SAAS;EAClB;EAEA,OAAOA,SAAS,CAACH,IAAI,CAAC,UAAUM,OAAO,EAAEC,OAAO,EAAE;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACV,MAAM,EAAEc,CAAC,IAAI,CAAC,EAAE;MACjD,IAAIrD,WAAW,GAAGiD,cAAc,CAACI,CAAC,CAAC;MACnC,IAAIzF,MAAM,GAAGoC,WAAW,CAACrC,MAAM,CAACC,MAAM;QAClCkB,SAAS,GAAGkB,WAAW,CAAClB,SAAS;MACrC,IAAIwE,SAAS,GAAGxF,eAAe,CAACF,MAAM,CAAC;MAEvC,IAAI0F,SAAS,IAAIxE,SAAS,EAAE;QAC1B,IAAIyE,aAAa,GAAGD,SAAS,CAACH,OAAO,EAAEC,OAAO,EAAEtE,SAAS,CAAC;QAE1D,IAAIyE,aAAa,KAAK,CAAC,EAAE;UACvB,OAAOzE,SAAS,KAAKtB,MAAM,GAAG+F,aAAa,GAAG,CAACA,aAAa;QAC9D;MACF;IACF;IAEA,OAAO,CAAC;EACV,CAAC,CAAC,CAAC5D,GAAG,CAAC,UAAU6D,MAAM,EAAE;IACvB,IAAIC,UAAU,GAAGD,MAAM,CAACd,kBAAkB,CAAC;IAE3C,IAAIe,UAAU,EAAE;MACd,OAAO7G,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4G,MAAM,CAAC,EAAE7G,eAAe,CAAC,CAAC,CAAC,EAAE+F,kBAAkB,EAAEF,WAAW,CAACiB,UAAU,EAAEjF,UAAU,EAAEkE,kBAAkB,CAAC,CAAC,CAAC;IACzI;IAEA,OAAOc,MAAM;EACf,CAAC,CAAC;AACJ;AACA,eAAe,SAASE,eAAeA,CAACC,KAAK,EAAE;EAC7C,IAAItE,SAAS,GAAGsE,KAAK,CAACtE,SAAS;IAC3BuE,aAAa,GAAGD,KAAK,CAACC,aAAa;IACnCC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrC5F,cAAc,GAAG0F,KAAK,CAAC1F,cAAc;IACrCwB,WAAW,GAAGkE,KAAK,CAAClE,WAAW;IAC/BI,iBAAiB,GAAG8D,KAAK,CAAC9D,iBAAiB;EAE/C,IAAIiE,eAAe,GAAG/G,KAAK,CAACgH,QAAQ,CAAC3F,iBAAiB,CAACwF,aAAa,EAAE,IAAI,CAAC,CAAC;IACxEI,gBAAgB,GAAGtH,cAAc,CAACoH,eAAe,EAAE,CAAC,CAAC;IACrDtF,UAAU,GAAGwF,gBAAgB,CAAC,CAAC,CAAC;IAChCC,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAEvC,IAAIE,kBAAkB,GAAGnH,KAAK,CAACoH,OAAO,CAAC,YAAY;IACjD,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,eAAe,GAAGjG,iBAAiB,CAACwF,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,IAAI,CAACS,eAAe,CAAC9B,MAAM,EAAE;MAC3B,OAAO/D,UAAU;IACnB;IAEA,IAAI8F,cAAc,GAAG,EAAE;IAEvB,SAASC,WAAWA,CAACC,KAAK,EAAE;MAC1B,IAAIJ,QAAQ,EAAE;QACZE,cAAc,CAAC3F,IAAI,CAAC6F,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLF,cAAc,CAAC3F,IAAI,CAAC/B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4H,KAAK,CAAC,EAAE;UAChD1F,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;MACL;IACF;IAEA,IAAI2F,YAAY,GAAG,IAAI;IACvBJ,eAAe,CAACtF,OAAO,CAAC,UAAUyF,KAAK,EAAE;MACvC,IAAIC,YAAY,KAAK,IAAI,EAAE;QACzBF,WAAW,CAACC,KAAK,CAAC;QAElB,IAAIA,KAAK,CAAC1F,SAAS,EAAE;UACnB,IAAI0F,KAAK,CAAC3F,gBAAgB,KAAK,KAAK,EAAE;YACpCuF,QAAQ,GAAG,KAAK;UAClB,CAAC,MAAM;YACLK,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC,MAAM,IAAIA,YAAY,IAAID,KAAK,CAAC3F,gBAAgB,KAAK,KAAK,EAAE;QAC3D0F,WAAW,CAACC,KAAK,CAAC;MACpB,CAAC,MAAM;QACLJ,QAAQ,GAAG,KAAK;QAChBG,WAAW,CAACC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAACV,aAAa,EAAEpF,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEjC,IAAIkG,sBAAsB,GAAG3H,KAAK,CAACoH,OAAO,CAAC,YAAY;IACrD,IAAIQ,WAAW,GAAGT,kBAAkB,CAACvE,GAAG,CAAC,UAAUiF,KAAK,EAAE;MACxD,IAAIjH,MAAM,GAAGiH,KAAK,CAACjH,MAAM;QACrBmB,SAAS,GAAG8F,KAAK,CAAC9F,SAAS;MAC/B,OAAO;QACLnB,MAAM,EAAEA,MAAM;QACdqE,KAAK,EAAElD;MACT,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACL6F,WAAW,EAAEA,WAAW;MACxB;MACAE,UAAU,EAAEF,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,CAAChH,MAAM;MACnDmB,SAAS,EAAE6F,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,CAAC3C;IAC9C,CAAC;EACH,CAAC,EAAE,CAACkC,kBAAkB,CAAC,CAAC;EAExB,SAAS3E,aAAaA,CAACuF,SAAS,EAAE;IAChC,IAAIC,eAAe;IAEnB,IAAID,SAAS,CAACjG,gBAAgB,KAAK,KAAK,IAAI,CAACqF,kBAAkB,CAAC3B,MAAM,IAAI2B,kBAAkB,CAAC,CAAC,CAAC,CAACrF,gBAAgB,KAAK,KAAK,EAAE;MAC1HkG,eAAe,GAAG,CAACD,SAAS,CAAC;IAC/B,CAAC,MAAM;MACLC,eAAe,GAAG,EAAE,CAAC7F,MAAM,CAACrC,kBAAkB,CAACqH,kBAAkB,CAAC7B,MAAM,CAAC,UAAU2C,KAAK,EAAE;QACxF,IAAIpG,GAAG,GAAGoG,KAAK,CAACpG,GAAG;QACnB,OAAOA,GAAG,KAAKkG,SAAS,CAAClG,GAAG;MAC9B,CAAC,CAAC,CAAC,EAAE,CAACkG,SAAS,CAAC,CAAC;IACnB;IAEAb,aAAa,CAACc,eAAe,CAAC;IAC9BlB,cAAc,CAAC1B,kBAAkB,CAAC4C,eAAe,CAAC,EAAEA,eAAe,CAAC;EACtE;EAEA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,YAAY,EAAE;IAC7D,OAAO9F,YAAY,CAACC,SAAS,EAAE6F,YAAY,EAAEhB,kBAAkB,EAAE3E,aAAa,EAAEtB,cAAc,EAAEwB,WAAW,EAAEI,iBAAiB,CAAC;EACjI,CAAC;EAED,IAAIsF,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,OAAOhD,kBAAkB,CAAC+B,kBAAkB,CAAC;EAC/C,CAAC;EAED,OAAO,CAACe,gBAAgB,EAAEf,kBAAkB,EAAEQ,sBAAsB,EAAES,UAAU,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}