{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\ring\\\\listinoRing.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Marketplace - visualizzazione listini lato punto vendita\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Caricamento from '../../utils/caricamento';\nimport Nav from '../../components/navigation/Nav';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport '../../css/DataViewDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MarketplaceRing = () => {\n  _s();\n  const [results, setResults] = useState(null);\n  const emptyValue = null;\n  const [loading, setLoading] = useState(true);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    let user = null;\n    let warehouseId = null;\n    try {\n      const userData = localStorage.getItem(\"user\");\n      if (userData && userData.trim() !== '') {\n        var _user, _user$idRegistry, _user$idRegistry$reta, _user$idRegistry$reta2, _user$idRegistry$reta3, _user$idRegistry$reta4, _user$idRegistry$reta5, _user$idRegistry$reta6, _user$idRegistry$reta7;\n        user = JSON.parse(userData);\n        warehouseId = (_user = user) === null || _user === void 0 ? void 0 : (_user$idRegistry = _user.idRegistry) === null || _user$idRegistry === void 0 ? void 0 : (_user$idRegistry$reta = _user$idRegistry.retailers) === null || _user$idRegistry$reta === void 0 ? void 0 : (_user$idRegistry$reta2 = _user$idRegistry$reta.idAffiliate2) === null || _user$idRegistry$reta2 === void 0 ? void 0 : (_user$idRegistry$reta3 = _user$idRegistry$reta2.idRegistry2) === null || _user$idRegistry$reta3 === void 0 ? void 0 : (_user$idRegistry$reta4 = _user$idRegistry$reta3.users) === null || _user$idRegistry$reta4 === void 0 ? void 0 : (_user$idRegistry$reta5 = _user$idRegistry$reta4[0]) === null || _user$idRegistry$reta5 === void 0 ? void 0 : (_user$idRegistry$reta6 = _user$idRegistry$reta5.warehousesCross) === null || _user$idRegistry$reta6 === void 0 ? void 0 : (_user$idRegistry$reta7 = _user$idRegistry$reta6[0]) === null || _user$idRegistry$reta7 === void 0 ? void 0 : _user$idRegistry$reta7.idWarehouse;\n      }\n    } catch (error) {\n      console.warn('Failed to parse user data in listinoRing:', error);\n    }\n    async function fetchData() {\n      if (!warehouseId) {\n        console.warn('No warehouse ID available for listinoRing');\n        return;\n      }\n      await APIRequest(\"GET\", \"productsposition?idWarehouse=\".concat(warehouseId)).then(res => {\n        var products = [];\n        res.data.forEach(element => {\n          var x = _objectSpread(_objectSpread({}, element), {}, {\n            id: element.idProductsPackaging.id,\n            idProduct: element.idProductsPackaging.idProduct.id,\n            idProduct2: element.idProductsPackaging.idProduct,\n            price: '0,00',\n            visibility: true\n          });\n          products.push(x);\n        });\n        window.sessionStorage.setItem('documentType', 'Logistica');\n        setResults(products);\n        setLoading(false);\n      }).catch(e => {\n        console.log(e);\n        setLoading(false);\n        if (toast.current !== null) {\n          var _e$response, _e$response2;\n          toast.current.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n            life: 3000\n          });\n        } else {\n          var _e$response3, _e$response4;\n          alert(\"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message));\n        }\n      });\n    }\n    fetchData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dataview-demo creaOrdine\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dataview-demo creaOrdine\",\n    children: [/*#__PURE__*/_jsxDEV(Nav, {\n      disabled: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MarketplaceGen, {\n      results: emptyValue,\n      results2: results,\n      results3: emptyValue,\n      results4: results,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 9\n  }, this);\n};\n_s(MarketplaceRing, \"2Au/69ph4pn78+sUJxsz1TQdNHg=\");\n_c = MarketplaceRing;\nexport default MarketplaceRing;\nvar _c;\n$RefreshReg$(_c, \"MarketplaceRing\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "MarketplaceGen", "Caricamento", "Nav", "APIRequest", "Toast", "jsxDEV", "_jsxDEV", "MarketplaceRing", "_s", "results", "setResults", "emptyValue", "loading", "setLoading", "toast", "user", "warehouseId", "userData", "localStorage", "getItem", "trim", "_user", "_user$idRegistry", "_user$idRegistry$reta", "_user$idRegistry$reta2", "_user$idRegistry$reta3", "_user$idRegistry$reta4", "_user$idRegistry$reta5", "_user$idRegistry$reta6", "_user$idRegistry$reta7", "JSON", "parse", "idRegistry", "retailers", "idAffiliate2", "idRegistry2", "users", "warehousesCross", "idWarehouse", "error", "console", "warn", "fetchData", "concat", "then", "res", "products", "data", "for<PERSON>ach", "element", "x", "_objectSpread", "id", "idProductsPackaging", "idProduct", "idProduct2", "price", "visibility", "push", "window", "sessionStorage", "setItem", "catch", "e", "log", "current", "_e$response", "_e$response2", "show", "severity", "summary", "detail", "response", "undefined", "message", "life", "_e$response3", "_e$response4", "alert", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "results2", "results3", "results4", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/ring/listinoRing.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Marketplace - visualizzazione listini lato punto vendita\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Caricamento from '../../utils/caricamento';\nimport Nav from '../../components/navigation/Nav';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport '../../css/DataViewDemo.css';\n\nconst MarketplaceRing = () => {\n    const [results, setResults] = useState(null)\n    const emptyValue = null\n    const [loading, setLoading] = useState(true)\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        let user = null\n        let warehouseId = null\n\n        try {\n            const userData = localStorage.getItem(\"user\")\n            if (userData && userData.trim() !== '') {\n                user = JSON.parse(userData)\n                warehouseId = user?.idRegistry?.retailers?.idAffiliate2?.idRegistry2?.users?.[0]?.warehousesCross?.[0]?.idWarehouse\n            }\n        } catch (error) {\n            console.warn('Failed to parse user data in listinoRing:', error)\n        }\n\n        async function fetchData() {\n            if (!warehouseId) {\n                console.warn('No warehouse ID available for listinoRing')\n                return\n            }\n\n            await APIRequest(\"GET\", `productsposition?idWarehouse=${warehouseId}`)\n                .then(res => {\n                    var products = []\n                    res.data.forEach(element => {\n                        var x = {\n                            ...element,\n                            id: element.idProductsPackaging.id,\n                            idProduct: element.idProductsPackaging.idProduct.id,\n                            idProduct2: element.idProductsPackaging.idProduct,\n                            price: '0,00',\n                            visibility: true\n                        }\n\n                        products.push(x)\n                    })\n                    window.sessionStorage.setItem('documentType', 'Logistica')\n                    setResults(products)\n                    setLoading(false)\n                }).catch((e) => {\n                    console.log(e)\n                    setLoading(false)\n                    if (toast.current !== null) {\n                        toast.current.show({\n                            severity: \"error\",\n                            summary: \"Siamo spiacenti\",\n                            detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                            life: 3000,\n                        });\n                    } else {\n                        alert(`Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`)\n                    }\n                })\n        }\n        fetchData()\n    }, []);\n    if (loading) {\n        return <div className=\"dataview-demo creaOrdine\">\n            <Toast ref={toast} />\n            <Caricamento />\n        </div>\n    }\n    return (\n        <div className=\"dataview-demo creaOrdine\">\n            <Nav disabled={false} />\n            <MarketplaceGen results={emptyValue} results2={results} results3={emptyValue} results4={results} loading={loading} />\n        </div>\n    )\n}\n\nexport default MarketplaceRing;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,cAAc,MAAM,2DAA2D;AACtF,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,GAAG,MAAM,iCAAiC;AACjD,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMY,UAAU,GAAG,IAAI;EACvB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMe,KAAK,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZ,IAAIkB,IAAI,GAAG,IAAI;IACf,IAAIC,WAAW,GAAG,IAAI;IAEtB,IAAI;MACA,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAC,KAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACpCd,IAAI,GAAGe,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAC;QAC3BD,WAAW,IAAAK,KAAA,GAAGN,IAAI,cAAAM,KAAA,wBAAAC,gBAAA,GAAJD,KAAA,CAAMW,UAAU,cAAAV,gBAAA,wBAAAC,qBAAA,GAAhBD,gBAAA,CAAkBW,SAAS,cAAAV,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BW,YAAY,cAAAV,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CW,WAAW,cAAAV,sBAAA,wBAAAC,sBAAA,GAAtDD,sBAAA,CAAwDW,KAAK,cAAAV,sBAAA,wBAAAC,sBAAA,GAA7DD,sBAAA,CAAgE,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAlED,sBAAA,CAAoEU,eAAe,cAAAT,sBAAA,wBAAAC,sBAAA,GAAnFD,sBAAA,CAAsF,CAAC,CAAC,cAAAC,sBAAA,uBAAxFA,sBAAA,CAA0FS,WAAW;MACvH;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEF,KAAK,CAAC;IACpE;IAEA,eAAeG,SAASA,CAAA,EAAG;MACvB,IAAI,CAAC1B,WAAW,EAAE;QACdwB,OAAO,CAACC,IAAI,CAAC,2CAA2C,CAAC;QACzD;MACJ;MAEA,MAAMtC,UAAU,CAAC,KAAK,kCAAAwC,MAAA,CAAkC3B,WAAW,CAAE,CAAC,CACjE4B,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,QAAQ,GAAG,EAAE;QACjBD,GAAG,CAACE,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAAC,aAAA,CAAAA,aAAA,KACEF,OAAO;YACVG,EAAE,EAAEH,OAAO,CAACI,mBAAmB,CAACD,EAAE;YAClCE,SAAS,EAAEL,OAAO,CAACI,mBAAmB,CAACC,SAAS,CAACF,EAAE;YACnDG,UAAU,EAAEN,OAAO,CAACI,mBAAmB,CAACC,SAAS;YACjDE,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE;UAAI,EACnB;UAEDX,QAAQ,CAACY,IAAI,CAACR,CAAC,CAAC;QACpB,CAAC,CAAC;QACFS,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,EAAE,WAAW,CAAC;QAC1DnD,UAAU,CAACoC,QAAQ,CAAC;QACpBjC,UAAU,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,CAACiD,KAAK,CAAEC,CAAC,IAAK;QACZvB,OAAO,CAACwB,GAAG,CAACD,CAAC,CAAC;QACdlD,UAAU,CAAC,KAAK,CAAC;QACjB,IAAIC,KAAK,CAACmD,OAAO,KAAK,IAAI,EAAE;UAAA,IAAAC,WAAA,EAAAC,YAAA;UACxBrD,KAAK,CAACmD,OAAO,CAACG,IAAI,CAAC;YACfC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,4FAAA5B,MAAA,CAAyF,EAAAuB,WAAA,GAAAH,CAAC,CAACS,QAAQ,cAAAN,WAAA,uBAAVA,WAAA,CAAYnB,IAAI,MAAK0B,SAAS,IAAAN,YAAA,GAAGJ,CAAC,CAACS,QAAQ,cAAAL,YAAA,uBAAVA,YAAA,CAAYpB,IAAI,GAAGgB,CAAC,CAACW,OAAO,CAAE;YAC9JC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,MAAM;UAAA,IAAAC,YAAA,EAAAC,YAAA;UACHC,KAAK,2FAAAnC,MAAA,CAAwF,EAAAiC,YAAA,GAAAb,CAAC,CAACS,QAAQ,cAAAI,YAAA,uBAAVA,YAAA,CAAY7B,IAAI,MAAK0B,SAAS,IAAAI,YAAA,GAAGd,CAAC,CAACS,QAAQ,cAAAK,YAAA,uBAAVA,YAAA,CAAY9B,IAAI,GAAGgB,CAAC,CAACW,OAAO,CAAE,CAAC;QACjK;MACJ,CAAC,CAAC;IACV;IACAhC,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAI9B,OAAO,EAAE;IACT,oBAAON,OAAA;MAAKyE,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAC5C1E,OAAA,CAACF,KAAK;QAAC6E,GAAG,EAAEnE;MAAM;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB/E,OAAA,CAACL,WAAW;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EACV;EACA,oBACI/E,OAAA;IAAKyE,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACrC1E,OAAA,CAACJ,GAAG;MAACoF,QAAQ,EAAE;IAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxB/E,OAAA,CAACN,cAAc;MAACS,OAAO,EAAEE,UAAW;MAAC4E,QAAQ,EAAE9E,OAAQ;MAAC+E,QAAQ,EAAE7E,UAAW;MAAC8E,QAAQ,EAAEhF,OAAQ;MAACG,OAAO,EAAEA;IAAQ;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpH,CAAC;AAEd,CAAC;AAAA7E,EAAA,CAzEKD,eAAe;AAAAmF,EAAA,GAAfnF,eAAe;AA2ErB,eAAeA,eAAe;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}