{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\utenteAffiliato.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiUtenteAffiliato - operazioni sull'aggiunta utente affiliato\n*\n*/\nimport React, { useState, useEffect, Fragment, useRef } from 'react';\nimport classNames from 'classnames/bind';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dialog } from 'primereact/dialog';\nimport { Divider } from 'primereact/divider';\nimport { Form, Field } from 'react-final-form';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Password } from 'primereact/password';\nimport { scrollTo } from '../components/generalizzazioni/scrollToElement';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { affiliato } from '../components/route';\nimport '../css/modale.css';\nimport '../css/FormDemo.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UtenteAffiliato = () => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState([]);\n  const [showMessage, setShowMessage] = useState(false);\n  const [openForm, setOpenForm] = useState('card d-none');\n  const [formData, setFormData] = useState({});\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function trovaRisultato() {\n      var url = 'registry/?id=' + localStorage.getItem(\"datiComodo\");\n      await APIRequest('GET', url).then(res => {\n        setResults(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, []);\n  if (results.length === 0) {\n    return null;\n  }\n  /* Validazione elementi inseriti */\n  const validate = data => {\n    let errors = {};\n    if (!data.email) {\n      errors.email = Costanti.EmailObb;\n    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$/i.test(data.email)) {\n      errors.email = Costanti.EmailNoVal;\n    }\n    if (!data.password) {\n      errors.password = Costanti.PassObb;\n    }\n    if (!data.confirmPassword) {\n      errors.confirmPassword = Costanti.ConfPassObb;\n    } else if (data.confirmPassword !== data.password) {\n      errors.confirmPassword = Costanti.PassValid;\n    }\n    return errors;\n  };\n  /* Chiamata axios per aggiunta utente affiliato */\n  const onSubmit = (data, form) => {\n    setFormData(data);\n    let contenuto = {\n      username: data.email,\n      password: data.password,\n      role: affiliato,\n      idRegistry: localStorage.getItem(\"datiComodo\")\n    };\n    APIRequest('POST', 'user/', contenuto).then(res => {\n      console.log(res.data);\n      setShowMessage(true);\n      localStorage.setItem(\"datiComodo\", 0);\n      form.restart();\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Ci dispiace',\n        detail: \"Il nome utente inserito \\xE8 gi\\xE0 in uso. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  /* Apro o chiudo il form di aggiunta a seconda dello stato del bottone */\n  const openCloseForm = () => {\n    if (openForm === 'card d-none') {\n      setOpenForm('card');\n    } else {\n      setOpenForm('card d-none');\n    }\n  };\n  /* Scrolling to the form element. */\n  const scrollElement = document.getElementById('createUserAccountForm');\n  scrollTo({\n    scrollElement: scrollElement,\n    block: 'start'\n  });\n  const isFormFieldValid = meta => !!(meta.touched && meta.error);\n  const getFormErrorMessage = meta => {\n    return isFormFieldValid(meta) && /*#__PURE__*/_jsxDEV(\"small\", {\n      className: \"p-error\",\n      children: meta.error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 42\n    }, this);\n  };\n  const dialogFooter = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-d-flex p-jc-center\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      label: \"OK\",\n      className: \"p-button-text\",\n      autoFocus: true,\n      onClick: () => setShowMessage(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 26\n  }, this);\n  const passwordHeader = /*#__PURE__*/_jsxDEV(\"h6\", {\n    children: Costanti.SelectPass\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 28\n  }, this);\n  const passwordFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"p-mt-2\",\n      children: Costanti.PassDevCont\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"p-pl-2 p-ml-2 p-mt-0\",\n      style: {\n        lineHeight: '1.5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Minuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Maiuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.AlmUnNum\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Alm8Car\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-demo\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-fluid p-grid\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-field p-col-12 pb-0 mb-0 p-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"list-group-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-credit-card mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 65\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.pIva\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 107\n                }, this), \": \", results.pIva]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"list-group-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-id-card mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 65\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.rSociale\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 103\n                }, this), \": \", results.firstName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this), results.users.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-8 d-none d-sm-block\",\n              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                className: \"ml-3\",\n                children: [Costanti.NomeUtente, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-4 d-none d-sm-block\",\n              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                children: [Costanti.Ruolo, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"dett-aff\",\n            children: results.users.map((element, index) => {\n              // Return the element. Also pass key     \n              return /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: element.role === 'AFFILIATO' && /*#__PURE__*/_jsxDEV(Fragment, {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"row\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"col-12 col-sm-8 mb-2 mb-sm-0\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"d-block d-sm-none\",\n                            children: [Costanti.NomeUtente, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 157,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-break\",\n                            children: element.username\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 158,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 156,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"col-12 col-sm-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"d-block d-sm-none\",\n                            children: [Costanti.Ruolo, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 161,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-break\",\n                            children: element.role\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 162,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 160,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 155,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 53\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button mx-auto w-auto px-4 justify-content-center mb-4\",\n        onClick: () => openCloseForm(),\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-user-plus mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 128\n        }, this), \" \", Costanti.AggUser, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      visible: showMessage,\n      onHide: () => setShowMessage(false),\n      position: \"top\",\n      footer: dialogFooter,\n      showHeader: false,\n      breakpoints: {\n        '960px': '80vw'\n      },\n      style: {\n        width: '30vw'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-check-circle\",\n          style: {\n            fontSize: '5rem',\n            color: 'var(--green-500)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          children: Costanti.RegSucc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            lineHeight: 1.5,\n            textIndent: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this), \" \", Costanti.GiaReg, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 49\n          }, this), \" \", Costanti.ConEmail, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: formData.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 77\n          }, this), \" .\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-d-flex p-jc-center px-0 px-md-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: openForm,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: onSubmit,\n          initialValues: {\n            email: '',\n            password: ''\n          },\n          validate: validate,\n          render: _ref => {\n            let {\n              handleSubmit\n            } = _ref;\n            return /*#__PURE__*/_jsxDEV(\"form\", {\n              id: \"createUserAccountForm\",\n              onSubmit: handleSubmit,\n              className: \"p-fluid\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-center w-100 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.CreaUtenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 68\n                }, this), \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-chevron-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 107\n                }, this), \" \", Costanti.Affiliato]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Field, {\n                name: \"email\",\n                render: _ref2 => {\n                  let {\n                    input,\n                    meta\n                  } = _ref2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-float-label p-input-icon-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"pi pi-envelope\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(InputText, _objectSpread(_objectSpread({\n                        id: \"email\"\n                      }, input), {}, {\n                        className: classNames({\n                          'p-invalid': isFormFieldValid(meta)\n                        })\n                      }), void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 199,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"email\",\n                        className: classNames({\n                          'p-error': isFormFieldValid(meta)\n                        }),\n                        children: [Costanti.NomeUtente, \"*\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 41\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 37\n                    }, this), getFormErrorMessage(meta)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 33\n                  }, this);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Field, {\n                name: \"password\",\n                render: _ref3 => {\n                  let {\n                    input,\n                    meta\n                  } = _ref3;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-float-label\",\n                      children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                        id: \"password\"\n                      }, input), {}, {\n                        toggleMask: true,\n                        className: classNames({\n                          'p-invalid': isFormFieldValid(meta)\n                        }),\n                        header: passwordHeader,\n                        footer: passwordFooter\n                      }), void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"password\",\n                        className: classNames({\n                          'p-error': isFormFieldValid(meta)\n                        }),\n                        children: \"Password*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 41\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 37\n                    }, this), getFormErrorMessage(meta)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 33\n                  }, this);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Field, {\n                name: \"confirmPassword\",\n                render: _ref4 => {\n                  let {\n                    input,\n                    meta\n                  } = _ref4;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-float-label\",\n                      children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                        id: \"confirmPassword\"\n                      }, input), {}, {\n                        className: classNames({\n                          'p-invalid': isFormFieldValid(meta)\n                        }),\n                        toggleMask: true,\n                        feedback: false\n                      }), void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"confirmPassword\",\n                        className: classNames({\n                          'p-error': isFormFieldValid(meta)\n                        }),\n                        children: [Costanti.Conferma, \" password*\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 41\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 37\n                    }, this), getFormErrorMessage(meta)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 33\n                  }, this);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"buttonForm\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  id: \"user\",\n                  className: \"mx-0 mt-3 d-flex justify-content-center w-auto px-5\",\n                  children: [\" \", Costanti.salva, \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 33\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 25\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 9\n  }, this);\n};\n_s(UtenteAffiliato, \"bLQi3wHug6//zdZ3qK/eOv/B5+w=\");\n_c = UtenteAffiliato;\nexport default UtenteAffiliato;\nvar _c;\n$RefreshReg$(_c, \"UtenteAffiliato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "useRef", "classNames", "<PERSON><PERSON>", "InputText", "Dialog", "Divider", "Form", "Field", "<PERSON><PERSON>", "APIRequest", "Toast", "Password", "scrollTo", "stopLoading", "affiliato", "jsxDEV", "_jsxDEV", "_Fragment", "UtenteAffiliato", "_s", "results", "setResults", "showMessage", "setShowMessage", "openForm", "setOpenForm", "formData", "setFormData", "toast", "trovaRisultato", "url", "localStorage", "getItem", "then", "res", "data", "catch", "e", "console", "log", "length", "validate", "errors", "email", "<PERSON>ail<PERSON>bb", "test", "EmailNoVal", "password", "PassObb", "confirmPassword", "ConfPassObb", "PassValid", "onSubmit", "form", "contenuto", "username", "role", "idRegistry", "setItem", "restart", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "openCloseForm", "scrollElement", "document", "getElementById", "block", "isFormFieldValid", "meta", "touched", "error", "getFormErrorMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dialogFooter", "label", "autoFocus", "onClick", "passwordHeader", "SelectPass", "passwordFooter", "PassDevCont", "style", "lineHeight", "Minus<PERSON>", "<PERSON><PERSON><PERSON>", "AlmUnNum", "Alm8Car", "ref", "pIva", "rSociale", "firstName", "users", "NomeUtente", "<PERSON><PERSON><PERSON>", "map", "element", "index", "AggUser", "visible", "onHide", "position", "footer", "showHeader", "breakpoints", "width", "fontSize", "color", "RegSucc", "textIndent", "GiaReg", "ConEmail", "initialValues", "render", "_ref", "handleSubmit", "id", "CreaUtenza", "Affiliato", "name", "_ref2", "input", "_objectSpread", "htmlFor", "_ref3", "toggleMask", "header", "_ref4", "feedback", "Conferma", "type", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/utenteAffiliato.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiUtenteAffiliato - operazioni sull'aggiunta utente affiliato\n*\n*/\nimport React, { useState, useEffect, Fragment, useRef } from 'react';\nimport classNames from 'classnames/bind';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dialog } from 'primereact/dialog';\nimport { Divider } from 'primereact/divider';\nimport { Form, Field } from 'react-final-form';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Password } from 'primereact/password';\nimport { scrollTo } from '../components/generalizzazioni/scrollToElement';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { affiliato } from '../components/route';\nimport '../css/modale.css';\nimport '../css/FormDemo.css';\n\nconst UtenteAffiliato = () => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState([]);\n    const [showMessage, setShowMessage] = useState(false);\n    const [openForm, setOpenForm] = useState('card d-none')\n    const [formData, setFormData] = useState({});\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function trovaRisultato() {\n            var url = 'registry/?id=' + localStorage.getItem(\"datiComodo\")\n            await APIRequest('GET', url)\n                .then(res => {\n                    setResults(res.data);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, []);\n    if (results.length === 0) {\n        return null;\n    }\n    /* Validazione elementi inseriti */\n    const validate = (data) => {\n        let errors = {};\n        if (!data.email) {\n            errors.email = Costanti.EmailObb;\n        }\n        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$/i.test(data.email)) {\n            errors.email = Costanti.EmailNoVal;\n        }\n        if (!data.password) {\n            errors.password = Costanti.PassObb;\n        }\n        if (!data.confirmPassword) {\n            errors.confirmPassword = Costanti.ConfPassObb;\n        }\n        else if (data.confirmPassword !== data.password) {\n            errors.confirmPassword = Costanti.PassValid;\n        }\n        return errors;\n    };\n    /* Chiamata axios per aggiunta utente affiliato */\n    const onSubmit = (data, form) => {\n        setFormData(data);\n        let contenuto = {\n            username: data.email,\n            password: data.password,\n            role: affiliato,\n            idRegistry: localStorage.getItem(\"datiComodo\")\n        }\n        APIRequest('POST', 'user/', contenuto)\n            .then(res => {\n                console.log(res.data);\n                setShowMessage(true);\n                localStorage.setItem(\"datiComodo\", 0);\n                form.restart();\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch(e => {\n                console.log(e);\n                toast.current.show({ severity: 'error', summary: 'Ci dispiace', detail: `Il nome utente inserito è già in uso. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    };\n    /* Apro o chiudo il form di aggiunta a seconda dello stato del bottone */\n    const openCloseForm = () => {\n        if (openForm === 'card d-none') {\n            setOpenForm('card')\n        } else {\n            setOpenForm('card d-none')\n        }\n    }\n    /* Scrolling to the form element. */\n    const scrollElement = document.getElementById('createUserAccountForm');\n    scrollTo({ scrollElement: scrollElement, block: 'start' });\n\n    const isFormFieldValid = (meta) => !!(meta.touched && meta.error);\n    const getFormErrorMessage = (meta) => {\n        return isFormFieldValid(meta) && <small className=\"p-error\">{meta.error}</small>;\n    };\n    const dialogFooter = <div className=\"p-d-flex p-jc-center\"><Button label=\"OK\" className=\"p-button-text\" autoFocus onClick={() => setShowMessage(false)} /></div>;\n    const passwordHeader = <h6>{Costanti.SelectPass}</h6>;\n    const passwordFooter = (\n        <React.Fragment>\n            <Divider />\n            <p className=\"p-mt-2\">{Costanti.PassDevCont}</p>\n            <ul className=\"p-pl-2 p-ml-2 p-mt-0\" style={{ lineHeight: '1.5' }}>\n                <li>{Costanti.Minuscola}</li>\n                <li>{Costanti.Maiuscola}</li>\n                <li>{Costanti.AlmUnNum}</li>\n                <li>{Costanti.Alm8Car}</li>\n            </ul>\n        </React.Fragment>\n    );\n    return (\n        <div className=\"form-demo\">\n            <Toast ref={toast} />\n            <div className=\"p-fluid p-grid\">\n                <div className=\"p-field p-col-12 pb-0 mb-0 p-md-4\">\n                    <div className='row'>\n                        <div className=\"col-12 mb-0\">\n                            <ul className=\"list-group\">\n                                <li className=\"list-group-item\"><i className=\"pi pi-credit-card mr-3\"></i><strong>{Costanti.pIva}</strong>: {results.pIva}</li>\n                                <li className=\"list-group-item\"><i className=\"pi pi-id-card mr-3\"></i><strong>{Costanti.rSociale}</strong>: {results.firstName}</li>\n                            </ul>\n                            <hr className='my-4' />\n                        </div>\n                    </div>\n                    {results.users.length > 0 &&\n                        <>\n                            <div className=\"row\">\n                                <div className=\"col-8 d-none d-sm-block\">\n                                    <b className=\"ml-3\">{Costanti.NomeUtente}:</b>\n                                </div>\n                                <div className=\"col-4 d-none d-sm-block\">\n                                    <b>{Costanti.Ruolo}:</b>\n                                </div>\n                            </div>\n                            <ul className=\"dett-aff\" >\n                                {results.users.map((element, index) => {\n                                    // Return the element. Also pass key     \n                                    return (\n                                        <>\n                                            {element.role === 'AFFILIATO' &&\n                                                <Fragment key={index}>\n                                                    <div>\n                                                        <li>\n                                                            <div className=\"row\">\n                                                                <div className=\"col-12 col-sm-8 mb-2 mb-sm-0\">\n                                                                    <strong className='d-block d-sm-none'>{Costanti.NomeUtente}:</strong>\n                                                                    <span className='text-break'>{element.username}</span>\n                                                                </div>\n                                                                <div className=\"col-12 col-sm-4\">\n                                                                    <strong className='d-block d-sm-none'>{Costanti.Ruolo}:</strong>\n                                                                    <span className='text-break'>{element.role}</span>\n                                                                </div>\n                                                            </div>\n                                                        </li>\n                                                    </div>\n                                                </Fragment>\n                                            }\n                                        </>\n                                    )\n                                })\n                                }\n                            </ul>\n                        </>\n                    }\n                </div>\n            </div>\n            <div className=\"d-flex\">\n                <Button className=\"p-button mx-auto w-auto px-4 justify-content-center mb-4\" onClick={() => openCloseForm()} > <i className=\"pi pi-user-plus mr-2\"></i> {Costanti.AggUser} </Button>\n            </div>\n            <Dialog visible={showMessage} onHide={() => setShowMessage(false)} position=\"top\" footer={dialogFooter} showHeader={false} breakpoints={{ '960px': '80vw' }} style={{ width: '30vw' }}>\n                <div className=\"p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3\">\n                    <i className=\"pi pi-check-circle\" style={{ fontSize: '5rem', color: 'var(--green-500)' }}></i>\n                    <h5>{Costanti.RegSucc}</h5>\n                    <p style={{ lineHeight: 1.5, textIndent: '1rem' }}>\n                        <br /> {Costanti.GiaReg}<br /> {Costanti.ConEmail}: <b>{formData.email}</b> .\n                    </p>\n                </div>\n            </Dialog>\n            <div className=\"p-d-flex p-jc-center px-0 px-md-3\">\n                <div className={openForm}>\n                    <Form onSubmit={onSubmit} initialValues={{ email: '', password: '' }} validate={validate} render={({ handleSubmit }) => (\n                        <form id='createUserAccountForm' onSubmit={handleSubmit} className=\"p-fluid\">\n                            <h5 className='text-center w-100 mb-4'><strong>{Costanti.CreaUtenza}</strong> <i className=\"pi pi-chevron-right\"></i> {Costanti.Affiliato}</h5>\n                            <Field name=\"email\" render={({ input, meta }) => (\n                                <div className=\"p-field\">\n                                    <span className=\"p-float-label p-input-icon-right\">\n                                        <i className=\"pi pi-envelope\" />\n                                        <InputText id=\"email\" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />\n                                        <label htmlFor=\"email\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.NomeUtente}*</label>\n                                    </span>\n                                    {getFormErrorMessage(meta)}\n                                </div>\n                            )} />\n                            <Field name=\"password\" render={({ input, meta }) => (\n                                <div className=\"p-field\">\n                                    <span className=\"p-float-label\">\n                                        <Password id=\"password\" {...input} toggleMask className={classNames({ 'p-invalid': isFormFieldValid(meta) })} header={passwordHeader} footer={passwordFooter} />\n                                        <label htmlFor=\"password\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>Password*</label>\n                                    </span>\n                                    {getFormErrorMessage(meta)}\n                                </div>\n                            )} />\n                            <Field name=\"confirmPassword\" render={({ input, meta }) => (\n                                <div className=\"p-field\">\n                                    <span className=\"p-float-label\">\n                                        <Password id=\"confirmPassword\" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} toggleMask feedback={false} />\n                                        <label htmlFor=\"confirmPassword\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Conferma} password*</label>\n                                    </span>\n                                    {getFormErrorMessage(meta)}\n                                </div>\n                            )} />\n                            <div className=\"buttonForm\">\n                                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                                <Button type=\"submit\" id=\"user\" className=\"mx-0 mt-3 d-flex justify-content-center w-auto px-5\" > {Costanti.salva} </Button>\n                            </div>\n                        </form>\n                    )} />\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default UtenteAffiliato;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACpE,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,IAAI,EAAEC,KAAK,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,gDAAgD;AACzE,SAASC,WAAW,QAAQ,4CAA4C;AACxE,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,mBAAmB;AAC1B,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAjB,QAAA,IAAAkB,SAAA;AAE7B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,aAAa,CAAC;EACvD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM+B,KAAK,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAF,SAAS,CAAC,MAAM;IACZ,eAAe+B,cAAcA,CAAA,EAAG;MAC5B,IAAIC,GAAG,GAAG,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC9D,MAAMvB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBG,IAAI,CAACC,GAAG,IAAI;QACTb,UAAU,CAACa,GAAG,CAACC,IAAI,CAAC;MACxB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACNxB,WAAW,CAAC,CAAC;IACjB;IACAgB,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIT,OAAO,CAACoB,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACf;EACA;EACA,MAAMC,QAAQ,GAAIN,IAAI,IAAK;IACvB,IAAIO,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,CAACP,IAAI,CAACQ,KAAK,EAAE;MACbD,MAAM,CAACC,KAAK,GAAGnC,QAAQ,CAACoC,QAAQ;IACpC,CAAC,MACI,IAAI,CAAC,2CAA2C,CAACC,IAAI,CAACV,IAAI,CAACQ,KAAK,CAAC,EAAE;MACpED,MAAM,CAACC,KAAK,GAAGnC,QAAQ,CAACsC,UAAU;IACtC;IACA,IAAI,CAACX,IAAI,CAACY,QAAQ,EAAE;MAChBL,MAAM,CAACK,QAAQ,GAAGvC,QAAQ,CAACwC,OAAO;IACtC;IACA,IAAI,CAACb,IAAI,CAACc,eAAe,EAAE;MACvBP,MAAM,CAACO,eAAe,GAAGzC,QAAQ,CAAC0C,WAAW;IACjD,CAAC,MACI,IAAIf,IAAI,CAACc,eAAe,KAAKd,IAAI,CAACY,QAAQ,EAAE;MAC7CL,MAAM,CAACO,eAAe,GAAGzC,QAAQ,CAAC2C,SAAS;IAC/C;IACA,OAAOT,MAAM;EACjB,CAAC;EACD;EACA,MAAMU,QAAQ,GAAGA,CAACjB,IAAI,EAAEkB,IAAI,KAAK;IAC7B1B,WAAW,CAACQ,IAAI,CAAC;IACjB,IAAImB,SAAS,GAAG;MACZC,QAAQ,EAAEpB,IAAI,CAACQ,KAAK;MACpBI,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;MACvBS,IAAI,EAAE1C,SAAS;MACf2C,UAAU,EAAE1B,YAAY,CAACC,OAAO,CAAC,YAAY;IACjD,CAAC;IACDvB,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE6C,SAAS,CAAC,CACjCrB,IAAI,CAACC,GAAG,IAAI;MACTI,OAAO,CAACC,GAAG,CAACL,GAAG,CAACC,IAAI,CAAC;MACrBZ,cAAc,CAAC,IAAI,CAAC;MACpBQ,YAAY,CAAC2B,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MACrCL,IAAI,CAACM,OAAO,CAAC,CAAC;MACdC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC3B,KAAK,CAACC,CAAC,IAAI;MAAA,IAAA2B,WAAA,EAAAC,YAAA;MACV3B,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdT,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,aAAa;QAAEC,MAAM,mEAAAC,MAAA,CAA6D,EAAAP,WAAA,GAAA3B,CAAC,CAACmC,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAY7B,IAAI,MAAKsC,SAAS,IAAAR,YAAA,GAAG5B,CAAC,CAACmC,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAY9B,IAAI,GAAGE,CAAC,CAACqC,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACrN,CAAC,CAAC;EACV,CAAC;EACD;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIpD,QAAQ,KAAK,aAAa,EAAE;MAC5BC,WAAW,CAAC,MAAM,CAAC;IACvB,CAAC,MAAM;MACHA,WAAW,CAAC,aAAa,CAAC;IAC9B;EACJ,CAAC;EACD;EACA,MAAMoD,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;EACtEnE,QAAQ,CAAC;IAAEiE,aAAa,EAAEA,aAAa;IAAEG,KAAK,EAAE;EAAQ,CAAC,CAAC;EAE1D,MAAMC,gBAAgB,GAAIC,IAAI,IAAK,CAAC,EAAEA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACE,KAAK,CAAC;EACjE,MAAMC,mBAAmB,GAAIH,IAAI,IAAK;IAClC,OAAOD,gBAAgB,CAACC,IAAI,CAAC,iBAAIlE,OAAA;MAAOsE,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAEL,IAAI,CAACE;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACpF,CAAC;EACD,MAAMC,YAAY,gBAAG5E,OAAA;IAAKsE,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eAACvE,OAAA,CAACd,MAAM;MAAC2F,KAAK,EAAC,IAAI;MAACP,SAAS,EAAC,eAAe;MAACQ,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAAC,KAAK;IAAE;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAChK,MAAMK,cAAc,gBAAGhF,OAAA;IAAAuE,QAAA,EAAK/E,QAAQ,CAACyF;EAAU;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrD,MAAMO,cAAc,gBAChBlF,OAAA,CAACpB,KAAK,CAACG,QAAQ;IAAAwF,QAAA,gBACXvE,OAAA,CAACX,OAAO;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX3E,OAAA;MAAGsE,SAAS,EAAC,QAAQ;MAAAC,QAAA,EAAE/E,QAAQ,CAAC2F;IAAW;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChD3E,OAAA;MAAIsE,SAAS,EAAC,sBAAsB;MAACc,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAd,QAAA,gBAC9DvE,OAAA;QAAAuE,QAAA,EAAK/E,QAAQ,CAAC8F;MAAS;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7B3E,OAAA;QAAAuE,QAAA,EAAK/E,QAAQ,CAAC+F;MAAS;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7B3E,OAAA;QAAAuE,QAAA,EAAK/E,QAAQ,CAACgG;MAAQ;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5B3E,OAAA;QAAAuE,QAAA,EAAK/E,QAAQ,CAACiG;MAAO;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CACnB;EACD,oBACI3E,OAAA;IAAKsE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBvE,OAAA,CAACN,KAAK;MAACgG,GAAG,EAAE9E;IAAM;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB3E,OAAA;MAAKsE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BvE,OAAA;QAAKsE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CvE,OAAA;UAAKsE,SAAS,EAAC,KAAK;UAAAC,QAAA,eAChBvE,OAAA;YAAKsE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBvE,OAAA;cAAIsE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACtBvE,OAAA;gBAAIsE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAACvE,OAAA;kBAAGsE,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA3E,OAAA;kBAAAuE,QAAA,EAAS/E,QAAQ,CAACmG;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAACvE,OAAO,CAACuF,IAAI;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/H3E,OAAA;gBAAIsE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAACvE,OAAA;kBAAGsE,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA3E,OAAA;kBAAAuE,QAAA,EAAS/E,QAAQ,CAACoG;gBAAQ;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAACvE,OAAO,CAACyF,SAAS;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpI,CAAC,eACL3E,OAAA;cAAIsE,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLvE,OAAO,CAAC0F,KAAK,CAACtE,MAAM,GAAG,CAAC,iBACrBxB,OAAA,CAAAC,SAAA;UAAAsE,QAAA,gBACIvE,OAAA;YAAKsE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAChBvE,OAAA;cAAKsE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACpCvE,OAAA;gBAAGsE,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAE/E,QAAQ,CAACuG,UAAU,EAAC,GAAC;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACN3E,OAAA;cAAKsE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACpCvE,OAAA;gBAAAuE,QAAA,GAAI/E,QAAQ,CAACwG,KAAK,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN3E,OAAA;YAAIsE,SAAS,EAAC,UAAU;YAAAC,QAAA,EACnBnE,OAAO,CAAC0F,KAAK,CAACG,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;cACnC;cACA,oBACInG,OAAA,CAAAC,SAAA;gBAAAsE,QAAA,EACK2B,OAAO,CAAC1D,IAAI,KAAK,WAAW,iBACzBxC,OAAA,CAACjB,QAAQ;kBAAAwF,QAAA,eACLvE,OAAA;oBAAAuE,QAAA,eACIvE,OAAA;sBAAAuE,QAAA,eACIvE,OAAA;wBAAKsE,SAAS,EAAC,KAAK;wBAAAC,QAAA,gBAChBvE,OAAA;0BAAKsE,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBACzCvE,OAAA;4BAAQsE,SAAS,EAAC,mBAAmB;4BAAAC,QAAA,GAAE/E,QAAQ,CAACuG,UAAU,EAAC,GAAC;0BAAA;4BAAAvB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACrE3E,OAAA;4BAAMsE,SAAS,EAAC,YAAY;4BAAAC,QAAA,EAAE2B,OAAO,CAAC3D;0BAAQ;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC,eACN3E,OAAA;0BAAKsE,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBAC5BvE,OAAA;4BAAQsE,SAAS,EAAC,mBAAmB;4BAAAC,QAAA,GAAE/E,QAAQ,CAACwG,KAAK,EAAC,GAAC;0BAAA;4BAAAxB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChE3E,OAAA;4BAAMsE,SAAS,EAAC,YAAY;4BAAAC,QAAA,EAAE2B,OAAO,CAAC1D;0BAAI;4BAAAgC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GAdKwB,KAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAeV;cAAC,gBAEjB,CAAC;YAEX,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC;QAAA,eACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN3E,OAAA;MAAKsE,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACnBvE,OAAA,CAACd,MAAM;QAACoF,SAAS,EAAC,0DAA0D;QAACS,OAAO,EAAEA,CAAA,KAAMnB,aAAa,CAAC,CAAE;QAAAW,QAAA,GAAE,GAAC,eAAAvE,OAAA;UAAGsE,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAAC4G,OAAO,EAAC,GAAC;MAAA;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnL,CAAC,eACN3E,OAAA,CAACZ,MAAM;MAACiH,OAAO,EAAE/F,WAAY;MAACgG,MAAM,EAAEA,CAAA,KAAM/F,cAAc,CAAC,KAAK,CAAE;MAACgG,QAAQ,EAAC,KAAK;MAACC,MAAM,EAAE5B,YAAa;MAAC6B,UAAU,EAAE,KAAM;MAACC,WAAW,EAAE;QAAE,OAAO,EAAE;MAAO,CAAE;MAACtB,KAAK,EAAE;QAAEuB,KAAK,EAAE;MAAO,CAAE;MAAApC,QAAA,eAClLvE,OAAA;QAAKsE,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBACzDvE,OAAA;UAAGsE,SAAS,EAAC,oBAAoB;UAACc,KAAK,EAAE;YAAEwB,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAmB;QAAE;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9F3E,OAAA;UAAAuE,QAAA,EAAK/E,QAAQ,CAACsH;QAAO;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3B3E,OAAA;UAAGoF,KAAK,EAAE;YAAEC,UAAU,EAAE,GAAG;YAAE0B,UAAU,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC9CvE,OAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,KAAC,EAACnF,QAAQ,CAACwH,MAAM,eAAChH,OAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,KAAC,EAACnF,QAAQ,CAACyH,QAAQ,EAAC,IAAE,eAAAjH,OAAA;YAAAuE,QAAA,EAAI7D,QAAQ,CAACiB;UAAK;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,MAC/E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACT3E,OAAA;MAAKsE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC9CvE,OAAA;QAAKsE,SAAS,EAAE9D,QAAS;QAAA+D,QAAA,eACrBvE,OAAA,CAACV,IAAI;UAAC8C,QAAQ,EAAEA,QAAS;UAAC8E,aAAa,EAAE;YAAEvF,KAAK,EAAE,EAAE;YAAEI,QAAQ,EAAE;UAAG,CAAE;UAACN,QAAQ,EAAEA,QAAS;UAAC0F,MAAM,EAAEC,IAAA;YAAA,IAAC;cAAEC;YAAa,CAAC,GAAAD,IAAA;YAAA,oBAC/GpH,OAAA;cAAMsH,EAAE,EAAC,uBAAuB;cAAClF,QAAQ,EAAEiF,YAAa;cAAC/C,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACxEvE,OAAA;gBAAIsE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBAACvE,OAAA;kBAAAuE,QAAA,EAAS/E,QAAQ,CAAC+H;gBAAU;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,KAAC,eAAA3E,OAAA;kBAAGsE,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAACgI,SAAS;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/I3E,OAAA,CAACT,KAAK;gBAACkI,IAAI,EAAC,OAAO;gBAACN,MAAM,EAAEO,KAAA;kBAAA,IAAC;oBAAEC,KAAK;oBAAEzD;kBAAK,CAAC,GAAAwD,KAAA;kBAAA,oBACxC1H,OAAA;oBAAKsE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACpBvE,OAAA;sBAAMsE,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC9CvE,OAAA;wBAAGsE,SAAS,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChC3E,OAAA,CAACb,SAAS,EAAAyI,aAAA,CAAAA,aAAA;wBAACN,EAAE,EAAC;sBAAO,GAAKK,KAAK;wBAAErD,SAAS,EAAErF,UAAU,CAAC;0BAAE,WAAW,EAAEgF,gBAAgB,CAACC,IAAI;wBAAE,CAAC;sBAAE;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnG3E,OAAA;wBAAO6H,OAAO,EAAC,OAAO;wBAACvD,SAAS,EAAErF,UAAU,CAAC;0BAAE,SAAS,EAAEgF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAAK,QAAA,GAAE/E,QAAQ,CAACuG,UAAU,EAAC,GAAC;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACL3E,OAAA,CAACT,KAAK;gBAACkI,IAAI,EAAC,UAAU;gBAACN,MAAM,EAAEW,KAAA;kBAAA,IAAC;oBAAEH,KAAK;oBAAEzD;kBAAK,CAAC,GAAA4D,KAAA;kBAAA,oBAC3C9H,OAAA;oBAAKsE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACpBvE,OAAA;sBAAMsE,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC3BvE,OAAA,CAACL,QAAQ,EAAAiI,aAAA,CAAAA,aAAA;wBAACN,EAAE,EAAC;sBAAU,GAAKK,KAAK;wBAAEI,UAAU;wBAACzD,SAAS,EAAErF,UAAU,CAAC;0BAAE,WAAW,EAAEgF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAC8D,MAAM,EAAEhD,cAAe;wBAACwB,MAAM,EAAEtB;sBAAe;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChK3E,OAAA;wBAAO6H,OAAO,EAAC,UAAU;wBAACvD,SAAS,EAAErF,UAAU,CAAC;0BAAE,SAAS,EAAEgF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAAK,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvG,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACL3E,OAAA,CAACT,KAAK;gBAACkI,IAAI,EAAC,iBAAiB;gBAACN,MAAM,EAAEc,KAAA;kBAAA,IAAC;oBAAEN,KAAK;oBAAEzD;kBAAK,CAAC,GAAA+D,KAAA;kBAAA,oBAClDjI,OAAA;oBAAKsE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACpBvE,OAAA;sBAAMsE,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC3BvE,OAAA,CAACL,QAAQ,EAAAiI,aAAA,CAAAA,aAAA;wBAACN,EAAE,EAAC;sBAAiB,GAAKK,KAAK;wBAAErD,SAAS,EAAErF,UAAU,CAAC;0BAAE,WAAW,EAAEgF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAC6D,UAAU;wBAACG,QAAQ,EAAE;sBAAM;wBAAA1D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxI3E,OAAA;wBAAO6H,OAAO,EAAC,iBAAiB;wBAACvD,SAAS,EAAErF,UAAU,CAAC;0BAAE,SAAS,EAAEgF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAAK,QAAA,GAAE/E,QAAQ,CAAC2I,QAAQ,EAAC,YAAU;sBAAA;wBAAA3D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClI,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACL3E,OAAA;gBAAKsE,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAEvBvE,OAAA,CAACd,MAAM;kBAACkJ,IAAI,EAAC,QAAQ;kBAACd,EAAE,EAAC,MAAM;kBAAChD,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,GAAE,GAAC,EAAC/E,QAAQ,CAAC6I,KAAK,EAAC,GAAC;gBAAA;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAxE,EAAA,CAhNKD,eAAe;AAAAoI,EAAA,GAAfpI,eAAe;AAkNrB,eAAeA,eAAe;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}