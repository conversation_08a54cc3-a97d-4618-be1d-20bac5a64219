{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport RcTabs, { TabPane } from 'rc-tabs';\nimport classNames from 'classnames';\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport PlusOutlined from \"@ant-design/icons/es/icons/PlusOutlined\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport devWarning from '../_util/devWarning';\nimport { ConfigContext } from '../config-provider';\nimport SizeContext from '../config-provider/SizeContext';\nfunction Tabs(_a) {\n  var type = _a.type,\n    className = _a.className,\n    propSize = _a.size,\n    _onEdit = _a.onEdit,\n    hideAdd = _a.hideAdd,\n    centered = _a.centered,\n    addIcon = _a.addIcon,\n    props = __rest(_a, [\"type\", \"className\", \"size\", \"onEdit\", \"hideAdd\", \"centered\", \"addIcon\"]);\n  var customizePrefixCls = props.prefixCls,\n    _props$moreIcon = props.moreIcon,\n    moreIcon = _props$moreIcon === void 0 ? /*#__PURE__*/React.createElement(EllipsisOutlined, null) : _props$moreIcon;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('tabs', customizePrefixCls);\n  var editable;\n  if (type === 'editable-card') {\n    editable = {\n      onEdit: function onEdit(editType, _ref) {\n        var key = _ref.key,\n          event = _ref.event;\n        _onEdit === null || _onEdit === void 0 ? void 0 : _onEdit(editType === 'add' ? event : key, editType);\n      },\n      removeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null),\n      addIcon: addIcon || /*#__PURE__*/React.createElement(PlusOutlined, null),\n      showAdd: hideAdd !== true\n    };\n  }\n  var rootPrefixCls = getPrefixCls();\n  devWarning(!('onPrevClick' in props) && !('onNextClick' in props), 'Tabs', '`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead.');\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (contextSize) {\n    var _classNames;\n    var size = propSize !== undefined ? propSize : contextSize;\n    return /*#__PURE__*/React.createElement(RcTabs, _extends({\n      direction: direction,\n      moreTransitionName: \"\".concat(rootPrefixCls, \"-slide-up\")\n    }, props, {\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-card\"), ['card', 'editable-card'].includes(type)), _defineProperty(_classNames, \"\".concat(prefixCls, \"-editable-card\"), type === 'editable-card'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-centered\"), centered), _classNames), className),\n      editable: editable,\n      moreIcon: moreIcon,\n      prefixCls: prefixCls\n    }));\n  });\n}\nTabs.TabPane = TabPane;\nexport default Tabs;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "RcTabs", "TabPane", "classNames", "EllipsisOutlined", "PlusOutlined", "CloseOutlined", "dev<PERSON><PERSON><PERSON>", "ConfigContext", "SizeContext", "Tabs", "_a", "type", "className", "propSize", "size", "_onEdit", "onEdit", "<PERSON><PERSON><PERSON>", "centered", "addIcon", "props", "customizePrefixCls", "prefixCls", "_props$moreIcon", "moreIcon", "createElement", "_React$useContext", "useContext", "getPrefixCls", "direction", "editable", "editType", "_ref", "key", "event", "removeIcon", "showAdd", "rootPrefixCls", "Consumer", "contextSize", "_classNames", "undefined", "moreTransitionName", "concat", "includes"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/tabs/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport RcTabs, { TabPane } from 'rc-tabs';\nimport classNames from 'classnames';\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport PlusOutlined from \"@ant-design/icons/es/icons/PlusOutlined\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport devWarning from '../_util/devWarning';\nimport { ConfigContext } from '../config-provider';\nimport SizeContext from '../config-provider/SizeContext';\n\nfunction Tabs(_a) {\n  var type = _a.type,\n      className = _a.className,\n      propSize = _a.size,\n      _onEdit = _a.onEdit,\n      hideAdd = _a.hideAdd,\n      centered = _a.centered,\n      addIcon = _a.addIcon,\n      props = __rest(_a, [\"type\", \"className\", \"size\", \"onEdit\", \"hideAdd\", \"centered\", \"addIcon\"]);\n\n  var customizePrefixCls = props.prefixCls,\n      _props$moreIcon = props.moreIcon,\n      moreIcon = _props$moreIcon === void 0 ? /*#__PURE__*/React.createElement(EllipsisOutlined, null) : _props$moreIcon;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var prefixCls = getPrefixCls('tabs', customizePrefixCls);\n  var editable;\n\n  if (type === 'editable-card') {\n    editable = {\n      onEdit: function onEdit(editType, _ref) {\n        var key = _ref.key,\n            event = _ref.event;\n        _onEdit === null || _onEdit === void 0 ? void 0 : _onEdit(editType === 'add' ? event : key, editType);\n      },\n      removeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null),\n      addIcon: addIcon || /*#__PURE__*/React.createElement(PlusOutlined, null),\n      showAdd: hideAdd !== true\n    };\n  }\n\n  var rootPrefixCls = getPrefixCls();\n  devWarning(!('onPrevClick' in props) && !('onNextClick' in props), 'Tabs', '`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead.');\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (contextSize) {\n    var _classNames;\n\n    var size = propSize !== undefined ? propSize : contextSize;\n    return /*#__PURE__*/React.createElement(RcTabs, _extends({\n      direction: direction,\n      moreTransitionName: \"\".concat(rootPrefixCls, \"-slide-up\")\n    }, props, {\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-card\"), ['card', 'editable-card'].includes(type)), _defineProperty(_classNames, \"\".concat(prefixCls, \"-editable-card\"), type === 'editable-card'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-centered\"), centered), _classNames), className),\n      editable: editable,\n      moreIcon: moreIcon,\n      prefixCls: prefixCls\n    }));\n  });\n}\n\nTabs.TabPane = TabPane;\nexport default Tabs;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AAEvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,IAAIC,OAAO,QAAQ,SAAS;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,WAAW,MAAM,gCAAgC;AAExD,SAASC,IAAIA,CAACC,EAAE,EAAE;EAChB,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;IACdC,SAAS,GAAGF,EAAE,CAACE,SAAS;IACxBC,QAAQ,GAAGH,EAAE,CAACI,IAAI;IAClBC,OAAO,GAAGL,EAAE,CAACM,MAAM;IACnBC,OAAO,GAAGP,EAAE,CAACO,OAAO;IACpBC,QAAQ,GAAGR,EAAE,CAACQ,QAAQ;IACtBC,OAAO,GAAGT,EAAE,CAACS,OAAO;IACpBC,KAAK,GAAGnC,MAAM,CAACyB,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EAEjG,IAAIW,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACpCC,eAAe,GAAGH,KAAK,CAACI,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,aAAaxB,KAAK,CAAC0B,aAAa,CAACtB,gBAAgB,EAAE,IAAI,CAAC,GAAGoB,eAAe;EAEtH,IAAIG,iBAAiB,GAAG3B,KAAK,CAAC4B,UAAU,CAACpB,aAAa,CAAC;IACnDqB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIP,SAAS,GAAGM,YAAY,CAAC,MAAM,EAAEP,kBAAkB,CAAC;EACxD,IAAIS,QAAQ;EAEZ,IAAInB,IAAI,KAAK,eAAe,EAAE;IAC5BmB,QAAQ,GAAG;MACTd,MAAM,EAAE,SAASA,MAAMA,CAACe,QAAQ,EAAEC,IAAI,EAAE;QACtC,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;UACdC,KAAK,GAAGF,IAAI,CAACE,KAAK;QACtBnB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,QAAQ,KAAK,KAAK,GAAGG,KAAK,GAAGD,GAAG,EAAEF,QAAQ,CAAC;MACvG,CAAC;MACDI,UAAU,EAAE,aAAapC,KAAK,CAAC0B,aAAa,CAACpB,aAAa,EAAE,IAAI,CAAC;MACjEc,OAAO,EAAEA,OAAO,IAAI,aAAapB,KAAK,CAAC0B,aAAa,CAACrB,YAAY,EAAE,IAAI,CAAC;MACxEgC,OAAO,EAAEnB,OAAO,KAAK;IACvB,CAAC;EACH;EAEA,IAAIoB,aAAa,GAAGT,YAAY,CAAC,CAAC;EAClCtB,UAAU,CAAC,EAAE,aAAa,IAAIc,KAAK,CAAC,IAAI,EAAE,aAAa,IAAIA,KAAK,CAAC,EAAE,MAAM,EAAE,qFAAqF,CAAC;EACjK,OAAO,aAAarB,KAAK,CAAC0B,aAAa,CAACjB,WAAW,CAAC8B,QAAQ,EAAE,IAAI,EAAE,UAAUC,WAAW,EAAE;IACzF,IAAIC,WAAW;IAEf,IAAI1B,IAAI,GAAGD,QAAQ,KAAK4B,SAAS,GAAG5B,QAAQ,GAAG0B,WAAW;IAC1D,OAAO,aAAaxC,KAAK,CAAC0B,aAAa,CAACzB,MAAM,EAAEjB,QAAQ,CAAC;MACvD8C,SAAS,EAAEA,SAAS;MACpBa,kBAAkB,EAAE,EAAE,CAACC,MAAM,CAACN,aAAa,EAAE,WAAW;IAC1D,CAAC,EAAEjB,KAAK,EAAE;MACRR,SAAS,EAAEV,UAAU,EAAEsC,WAAW,GAAG,CAAC,CAAC,EAAExD,eAAe,CAACwD,WAAW,EAAE,EAAE,CAACG,MAAM,CAACrB,SAAS,EAAE,GAAG,CAAC,CAACqB,MAAM,CAAC7B,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAE9B,eAAe,CAACwD,WAAW,EAAE,EAAE,CAACG,MAAM,CAACrB,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAACsB,QAAQ,CAACjC,IAAI,CAAC,CAAC,EAAE3B,eAAe,CAACwD,WAAW,EAAE,EAAE,CAACG,MAAM,CAACrB,SAAS,EAAE,gBAAgB,CAAC,EAAEX,IAAI,KAAK,eAAe,CAAC,EAAE3B,eAAe,CAACwD,WAAW,EAAE,EAAE,CAACG,MAAM,CAACrB,SAAS,EAAE,WAAW,CAAC,EAAEJ,QAAQ,CAAC,EAAEsB,WAAW,GAAG5B,SAAS,CAAC;MAC/ZkB,QAAQ,EAAEA,QAAQ;MAClBN,QAAQ,EAAEA,QAAQ;MAClBF,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AAEAb,IAAI,CAACR,OAAO,GAAGA,OAAO;AACtB,eAAeQ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}