{"ast": null, "code": "import { useContext } from 'react';\nimport { FormContext } from '../context';\nexport default function useFormInstance() {\n  var _useContext = useContext(FormContext),\n    form = _useContext.form;\n  return form;\n}", "map": {"version": 3, "names": ["useContext", "FormContext", "useFormInstance", "_useContext", "form"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/hooks/useFormInstance.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { FormContext } from '../context';\nexport default function useFormInstance() {\n  var _useContext = useContext(FormContext),\n      form = _useContext.form;\n\n  return form;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,WAAW,QAAQ,YAAY;AACxC,eAAe,SAASC,eAAeA,CAAA,EAAG;EACxC,IAAIC,WAAW,GAAGH,UAAU,CAACC,WAAW,CAAC;IACrCG,IAAI,GAAGD,WAAW,CAACC,IAAI;EAE3B,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}