{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\allineaImgProd.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AllineaImmaginiProdotti - operazioni sull'aggiunta allinea immagini dei prodotti\n*\n*/\nimport React, { /* useEffect, */useRef, useState } from \"react\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { APIRequest } from \"../components/generalizzazioni/apireq\";\nimport { Button } from \"primereact/button\";\nimport { Costanti } from \"../components/traduttore/const\";\nimport { Toast } from \"primereact/toast\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AllineaImgProd = props => {\n  _s();\n  /* Dichiarazione constanti per settaggio dati */\n  const [selectecommerce, setSelectedecommerce] = useState(null);\n  /* const [selectexSys, setSelectedsetExSys] = useState([]);\n  const [exSystem, setexSystem] = useState([]) */\n  const toast = useRef(null);\n  const ecommerce = [{\n    name: 'MrWine',\n    code: 'MrWine'\n  }, {\n    name: 'AlcolicArtigianale',\n    code: 'AlcolicaArtigianale'\n  }];\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  /* useEffect(() => {\n      var exSys = []\n      async function fetchData() {\n          /* Reperisco sistemi esterni */ /*\n                                          await APIRequest('GET', 'externalsystems/')\n                                          .then(res => {\n                                          res.data.forEach(element => {\n                                          var x = {\n                                          code: element.id,\n                                          name: element.externalSistemName\n                                          }\n                                          exSys.push(x)\n                                          })\n                                          setexSystem(exSys)\n                                          }).catch((e) => {\n                                          console.log(e);\n                                          toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: 'Non è stato possibile visualizzare i sistemi esterni', life: 3000 });\n                                          })\n                                          }\n                                          fetchData()\n                                          }, []); */\n  /* Salvo l'ecommerce selezionato */\n  const onecommerceChange = e => {\n    setSelectedecommerce(e.value);\n  };\n  /* Salvo il sistema esterno selezionato */\n  /* const onexsysChange = (e) => {\n      setSelectedsetExSys(e.value)\n  } */\n  /* Chiamata asincrona per reperire l'immagine dagli ecommerce se esiste */\n  const Invia = async () => {\n    var url = '';\n    if (props.result.externalCode !== undefined) {\n      url = 'multimedia/importImage?idProdExternalCode=' + props.result.externalCode + '&ecommerce=' + selectecommerce.code;\n    } else {\n      url = 'multimedia/importImage?ecommerce=' + selectecommerce.code;\n    }\n    toast.current.show({\n      severity: 'success',\n      summary: 'Ottimo',\n      detail: \"La richiesta di aggiornamento è stata presa in carico attendere l'esito della richiesta prima di effettuare nuove operazioni\",\n      life: 3000\n    });\n    await APIRequest('GET', url).then(res => {\n      console.log(res.data);\n      toast.current.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"La richiesta di aggiornamento ha avuto successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile prendere in carico la richiesta. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-4 mx-3 my-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(Dropdown, {\n          value: selectecommerce,\n          options: ecommerce,\n          onChange: onecommerceChange,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona E-Commerce\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mb-2\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        style: {\n          width: '10%'\n        },\n        onClick: Invia,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"pi pi-save mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 67\n        }, this), Costanti.Invia]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 9\n  }, this);\n};\n_s(AllineaImgProd, \"4KYsxQZp4tWlBK6arWncDQtzFeE=\");\n_c = AllineaImgProd;\nvar _c;\n$RefreshReg$(_c, \"AllineaImgProd\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "Dropdown", "APIRequest", "<PERSON><PERSON>", "<PERSON><PERSON>", "Toast", "jsxDEV", "_jsxDEV", "AllineaImgProd", "props", "_s", "selectecommerce", "setSelectedecommerce", "toast", "ecommerce", "name", "code", "onecommerceChange", "e", "value", "Invia", "url", "result", "externalCode", "undefined", "current", "show", "severity", "summary", "detail", "life", "then", "res", "console", "log", "data", "setTimeout", "window", "location", "reload", "catch", "_e$response", "_e$response2", "concat", "response", "message", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "options", "onChange", "optionLabel", "placeholder", "style", "width", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/allineaImgProd.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AllineaImmaginiProdotti - operazioni sull'aggiunta allinea immagini dei prodotti\n*\n*/\nimport React, { /* useEffect, */ useRef, useState } from \"react\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { APIRequest } from \"../components/generalizzazioni/apireq\";\nimport { Button } from \"primereact/button\";\nimport { Costanti } from \"../components/traduttore/const\";\nimport { Toast } from \"primereact/toast\";\n\nexport const AllineaImgProd = (props) => {\n    /* Dichiarazione constanti per settaggio dati */\n    const [selectecommerce, setSelectedecommerce] = useState(null);\n    /* const [selectexSys, setSelectedsetExSys] = useState([]);\n    const [exSystem, setexSystem] = useState([]) */\n    const toast = useRef(null);\n    const ecommerce = [\n        { name: '<PERSON><PERSON><PERSON>', code: '<PERSON><PERSON><PERSON>' },\n        { name: 'AlcolicArtigianale', code: 'AlcolicaArtigianale' }\n    ];\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    /* useEffect(() => {\n        var exSys = []\n        async function fetchData() {\n            /* Reperisco sistemi esterni *//*\n    await APIRequest('GET', 'externalsystems/')\n        .then(res => {\n            res.data.forEach(element => {\n                var x = {\n                    code: element.id,\n                    name: element.externalSistemName\n                }\n                exSys.push(x)\n            })\n            setexSystem(exSys)\n        }).catch((e) => {\n            console.log(e);\n            toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: 'Non è stato possibile visualizzare i sistemi esterni', life: 3000 });\n        })\n}\nfetchData()\n}, []); */\n    /* Salvo l'ecommerce selezionato */\n    const onecommerceChange = (e) => {\n        setSelectedecommerce(e.value);\n    }\n    /* Salvo il sistema esterno selezionato */\n    /* const onexsysChange = (e) => {\n        setSelectedsetExSys(e.value)\n    } */\n    /* Chiamata asincrona per reperire l'immagine dagli ecommerce se esiste */\n    const Invia = async () => {\n        var url = ''\n        if (props.result.externalCode !== undefined) {\n            url = 'multimedia/importImage?idProdExternalCode=' + props.result.externalCode + '&ecommerce=' + selectecommerce.code\n        } else {\n            url = 'multimedia/importImage?ecommerce=' + selectecommerce.code\n        }\n        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"La richiesta di aggiornamento è stata presa in carico attendere l'esito della richiesta prima di effettuare nuove operazioni\", life: 3000 });\n        await APIRequest('GET', url)\n            .then(res => {\n                console.log(res.data);\n                toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"La richiesta di aggiornamento ha avuto successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile prendere in carico la richiesta. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n    return (\n        <div className=\"card\">\n            <Toast ref={toast} />\n            <div className=\"row mt-4 mx-3 my-3\">\n                <div className=\"col-12\">\n                    <Dropdown value={selectecommerce} options={ecommerce} onChange={onecommerceChange} optionLabel=\"name\" placeholder=\"Seleziona E-Commerce\" />\n                </div>\n                {/* <div className=\"col-6\">\n                    <Dropdown value={selectexSys} options={exSystem} onChange={onexsysChange} optionLabel=\"name\" placeholder=\"Seleziona sistema esterno\" />\n                </div> */}\n            </div>\n            <div className=\"d-flex justify-content-center mb-2\">\n                <Button style={{ width: '10%' }} onClick={Invia} ><span className='pi pi-save mr-2' />{Costanti.Invia}</Button>\n            </div>\n        </div>\n    )\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAI,gBAAiBC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChE,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,KAAK,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,OAAO,MAAMC,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACrC;EACA,MAAM,CAACC,eAAe,EAAEC,oBAAoB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC9D;AACJ;EACI,MAAMa,KAAK,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMe,SAAS,GAAG,CACd;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,EAClC;IAAED,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAE;EAAsB,CAAC,CAC9D;EACD;EACA;AACJ;AACA;AACA,yCAHI,CAGuC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC7BN,oBAAoB,CAACM,CAAC,CAACC,KAAK,CAAC;EACjC,CAAC;EACD;EACA;AACJ;AACA;EACI;EACA,MAAMC,KAAK,GAAG,MAAAA,CAAA,KAAY;IACtB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIZ,KAAK,CAACa,MAAM,CAACC,YAAY,KAAKC,SAAS,EAAE;MACzCH,GAAG,GAAG,4CAA4C,GAAGZ,KAAK,CAACa,MAAM,CAACC,YAAY,GAAG,aAAa,GAAGZ,eAAe,CAACK,IAAI;IACzH,CAAC,MAAM;MACHK,GAAG,GAAG,mCAAmC,GAAGV,eAAe,CAACK,IAAI;IACpE;IACAH,KAAK,CAACY,OAAO,CAACC,IAAI,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEC,OAAO,EAAE,QAAQ;MAAEC,MAAM,EAAE,8HAA8H;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC;IAClN,MAAM5B,UAAU,CAAC,KAAK,EAAEmB,GAAG,CAAC,CACvBU,IAAI,CAACC,GAAG,IAAI;MACTC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACG,IAAI,CAAC;MACrBtB,KAAK,CAACY,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,iDAAiD;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACrIM,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAACC,KAAK,CAAEtB,CAAC,IAAK;MAAA,IAAAuB,WAAA,EAAAC,YAAA;MACZT,OAAO,CAACC,GAAG,CAAChB,CAAC,CAAC;MACdL,KAAK,CAACY,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,iFAAAc,MAAA,CAA8E,EAAAF,WAAA,GAAAvB,CAAC,CAAC0B,QAAQ,cAAAH,WAAA,uBAAVA,WAAA,CAAYN,IAAI,MAAKX,SAAS,IAAAkB,YAAA,GAAGxB,CAAC,CAAC0B,QAAQ,cAAAF,YAAA,uBAAVA,YAAA,CAAYP,IAAI,GAAGjB,CAAC,CAAC2B,OAAO,CAAE;QAAEf,IAAI,EAAE;MAAK,CAAC,CAAC;IAC1O,CAAC,CAAC;EACV,CAAC;EACD,oBACIvB,OAAA;IAAKuC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACjBxC,OAAA,CAACF,KAAK;MAAC2C,GAAG,EAAEnC;IAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB7C,OAAA;MAAKuC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eAC/BxC,OAAA;QAAKuC,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACnBxC,OAAA,CAACN,QAAQ;UAACkB,KAAK,EAAER,eAAgB;UAAC0C,OAAO,EAAEvC,SAAU;UAACwC,QAAQ,EAAErC,iBAAkB;UAACsC,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC;QAAsB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1I;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIL,CAAC,eACN7C,OAAA;MAAKuC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eAC/CxC,OAAA,CAACJ,MAAM;QAACsD,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAM,CAAE;QAACC,OAAO,EAAEvC,KAAM;QAAA2B,QAAA,gBAAExC,OAAA;UAAMuC,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAAChD,QAAQ,CAACgB,KAAK;MAAA;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9G,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA1C,EAAA,CA7EYF,cAAc;AAAAoD,EAAA,GAAdpD,cAAc;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}