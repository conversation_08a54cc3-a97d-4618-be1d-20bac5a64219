{"ast": null, "code": "import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default (function () {\n  var nextFrameRef = React.useRef(null);\n  function cancelNextFrame() {\n    raf.cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = raf(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});", "map": {"version": 3, "names": ["React", "raf", "nextFrameRef", "useRef", "cancelNextFrame", "cancel", "current", "next<PERSON><PERSON><PERSON>", "callback", "delay", "arguments", "length", "undefined", "nextFrameId", "isCanceled", "useEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/hooks/useNextFrame.js"], "sourcesContent": ["import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default (function () {\n  var nextFrameRef = React.useRef(null);\n\n  function cancelNextFrame() {\n    raf.cancel(nextFrameRef.current);\n  }\n\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = raf(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,gBAAgB,YAAY;EAC1B,IAAIC,YAAY,GAAGF,KAAK,CAACG,MAAM,CAAC,IAAI,CAAC;EAErC,SAASC,eAAeA,CAAA,EAAG;IACzBH,GAAG,CAACI,MAAM,CAACH,YAAY,CAACI,OAAO,CAAC;EAClC;EAEA,SAASC,SAASA,CAACC,QAAQ,EAAE;IAC3B,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjFN,eAAe,CAAC,CAAC;IACjB,IAAIS,WAAW,GAAGZ,GAAG,CAAC,YAAY;MAChC,IAAIQ,KAAK,IAAI,CAAC,EAAE;QACdD,QAAQ,CAAC;UACPM,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;YAChC,OAAOD,WAAW,KAAKX,YAAY,CAACI,OAAO;UAC7C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLC,SAAS,CAACC,QAAQ,EAAEC,KAAK,GAAG,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IACFP,YAAY,CAACI,OAAO,GAAGO,WAAW;EACpC;EAEAb,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBX,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACG,SAAS,EAAEH,eAAe,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}