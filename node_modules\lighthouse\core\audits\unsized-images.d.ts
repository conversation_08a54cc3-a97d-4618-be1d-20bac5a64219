export default UnsizedImages;
declare class UnsizedImages extends Audit {
    /**
     * An img size attribute prevents layout shifts if it is a non-negative integer (incl zero!).
     * @url https://html.spec.whatwg.org/multipage/embedded-content-other.html#dimension-attributes
     * @param {string | null} attrValue
     * @return {boolean}
     */
    static doesHtmlAttrProvideExplicitSize(attrValue: string | null): boolean;
    /**
     * An img css size property prevents layout shifts if it is defined, not empty, and not equal to 'auto'.
     * @param {string | null} property
     * @return {boolean}
     */
    static isCssPropExplicitlySet(property: string | null): boolean;
    /**
     * Images are considered sized if they have defined & valid values.
     * @param {LH.Artifacts.ImageElement} image
     * @return {boolean}
     */
    static isSizedImage(image: LH.Artifacts.ImageElement): boolean;
    /**
     * @param {LH.Artifacts.ImageElement} image
     * @return {boolean}
     */
    static isNonNetworkSvg(image: LH.Artifacts.ImageElement): boolean;
    /**
     * @param {LH.Artifacts} artifacts
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=unsized-images.d.ts.map