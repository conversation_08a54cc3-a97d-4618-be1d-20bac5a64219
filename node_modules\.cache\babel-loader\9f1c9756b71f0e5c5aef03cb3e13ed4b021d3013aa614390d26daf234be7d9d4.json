{"ast": null, "code": "import React, { Component } from 'react';\nimport { ObjectUtils, classNames } from 'primereact/core';\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n  return _assertThisInitialized(self);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar VirtualScroller = /*#__PURE__*/function (_Component) {\n  _inherits(VirtualScroller, _Component);\n  var _super = _createSuper(VirtualScroller);\n  function VirtualScroller(props) {\n    var _this;\n    _classCallCheck(this, VirtualScroller);\n    _this = _super.call(this, props);\n    var isBoth = _this.isBoth();\n    _this.state = {\n      first: isBoth ? {\n        rows: 0,\n        cols: 0\n      } : 0,\n      last: isBoth ? {\n        rows: 0,\n        cols: 0\n      } : 0,\n      numItemsInViewport: isBoth ? {\n        rows: 0,\n        cols: 0\n      } : 0,\n      numToleratedItems: props.numToleratedItems,\n      loading: false\n    };\n    _this.onScroll = _this.onScroll.bind(_assertThisInitialized(_this));\n    _this.lastScrollPos = isBoth ? {\n      top: 0,\n      left: 0\n    } : 0;\n    return _this;\n  }\n  _createClass(VirtualScroller, [{\n    key: \"scrollTo\",\n    value: function scrollTo(options) {\n      this.element && this.element.scrollTo(options);\n    }\n  }, {\n    key: \"scrollToIndex\",\n    value: function scrollToIndex(index) {\n      var _this2 = this;\n      var behavior = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'auto';\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var first = this.state.first;\n      var itemSize = this.props.itemSize;\n      var contentPadding = this.getContentPadding();\n      var calculateFirst = function calculateFirst() {\n        var _index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        return _index <= _this2.state.numToleratedItems ? 0 : _index;\n      };\n      var calculateCoord = function calculateCoord(_first, _size, _padding) {\n        return _first * _size + _padding;\n      };\n      var scrollTo = function scrollTo() {\n        var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n        return _this2.scrollTo({\n          left: left,\n          top: top,\n          behavior: behavior\n        });\n      };\n      if (isBoth) {\n        var newFirst = {\n          rows: calculateFirst(index[0]),\n          cols: calculateFirst(index[1])\n        };\n        (newFirst.rows !== first.rows || newFirst.cols !== first.cols) && scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPadding.left), calculateCoord(newFirst.rows, itemSize[0], contentPadding.top));\n      } else {\n        var _newFirst = calculateFirst(index);\n        if (_newFirst !== first) {\n          isHorizontal ? scrollTo(calculateCoord(_newFirst, itemSize, contentPadding.left), 0) : scrollTo(0, calculateCoord(_newFirst, itemSize, contentPadding.top));\n        }\n      }\n    }\n  }, {\n    key: \"scrollInView\",\n    value: function scrollInView(index, to) {\n      var _this3 = this;\n      var behavior = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'auto';\n      if (to) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var _this$getRenderedRang = this.getRenderedRange(),\n          first = _this$getRenderedRang.first,\n          viewport = _this$getRenderedRang.viewport;\n        var itemSize = this.props.itemSize;\n        var scrollTo = function scrollTo() {\n          var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          return _this3.scrollTo({\n            left: left,\n            top: top,\n            behavior: behavior\n          });\n        };\n        var isToStart = to === 'to-start';\n        var isToEnd = to === 'to-end';\n        if (isToStart) {\n          if (isBoth) {\n            if (viewport.first.rows - first.rows > index[0]) {\n              scrollTo(viewport.first.cols * itemSize, (viewport.first.rows - 1) * itemSize);\n            } else if (viewport.first.cols - first.cols > index[1]) {\n              scrollTo((viewport.first.cols - 1) * itemSize, viewport.first.rows * itemSize);\n            }\n          } else {\n            if (viewport.first - first > index) {\n              var pos = (viewport.first - 1) * itemSize;\n              isHorizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n            }\n          }\n        } else if (isToEnd) {\n          if (isBoth) {\n            if (viewport.last.rows - first.rows <= index[0] + 1) {\n              scrollTo(viewport.first.cols * itemSize, (viewport.first.rows + 1) * itemSize);\n            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n              scrollTo((viewport.first.cols + 1) * itemSize, viewport.first.rows * itemSize);\n            }\n          } else {\n            if (viewport.last - first <= index + 1) {\n              var _pos2 = (viewport.first + 1) * itemSize;\n              isHorizontal ? scrollTo(_pos2, 0) : scrollTo(0, _pos2);\n            }\n          }\n        }\n      } else {\n        this.scrollToIndex(index, behavior);\n      }\n    }\n  }, {\n    key: \"getRenderedRange\",\n    value: function getRenderedRange() {\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var _this$state = this.state,\n        first = _this$state.first,\n        last = _this$state.last,\n        numItemsInViewport = _this$state.numItemsInViewport;\n      var itemSize = this.props.itemSize;\n      var calculateFirstInViewport = function calculateFirstInViewport(_pos, _size) {\n        return Math.floor(_pos / (_size || _pos));\n      };\n      var firstInViewport = first;\n      var lastInViewport = 0;\n      if (this.element) {\n        var scrollTop = this.element.scrollTop;\n        var scrollLeft = this.element.scrollLeft;\n        if (isBoth) {\n          firstInViewport = {\n            rows: calculateFirstInViewport(scrollTop, itemSize[0]),\n            cols: calculateFirstInViewport(scrollLeft, itemSize[1])\n          };\n          lastInViewport = {\n            rows: firstInViewport.rows + numItemsInViewport.rows,\n            cols: firstInViewport.cols + numItemsInViewport.cols\n          };\n        } else {\n          var scrollPos = isHorizontal ? scrollLeft : scrollTop;\n          firstInViewport = calculateFirstInViewport(scrollPos, itemSize);\n          lastInViewport = firstInViewport + numItemsInViewport;\n        }\n      }\n      return {\n        first: first,\n        last: last,\n        viewport: {\n          first: firstInViewport,\n          last: lastInViewport\n        }\n      };\n    }\n  }, {\n    key: \"isHorizontal\",\n    value: function isHorizontal() {\n      return this.props.orientation === 'horizontal';\n    }\n  }, {\n    key: \"isBoth\",\n    value: function isBoth() {\n      return this.props.orientation === 'both';\n    }\n  }, {\n    key: \"calculateOptions\",\n    value: function calculateOptions() {\n      var _this4 = this;\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var first = this.state.first;\n      var itemSize = this.props.itemSize;\n      var contentPadding = this.getContentPadding();\n      var contentWidth = this.element ? this.element.offsetWidth - contentPadding.left : 0;\n      var contentHeight = this.element ? this.element.offsetHeight - contentPadding.top : 0;\n      var calculateNumItemsInViewport = function calculateNumItemsInViewport(_contentSize, _itemSize) {\n        return Math.ceil(_contentSize / (_itemSize || _contentSize));\n      };\n      var numItemsInViewport = isBoth ? {\n        rows: calculateNumItemsInViewport(contentHeight, itemSize[0]),\n        cols: calculateNumItemsInViewport(contentWidth, itemSize[1])\n      } : calculateNumItemsInViewport(isHorizontal ? contentWidth : contentHeight, itemSize);\n      var numToleratedItems = this.state.numToleratedItems || Math.ceil((isBoth ? numItemsInViewport.rows : numItemsInViewport) / 2);\n      var calculateLast = function calculateLast(_first, _num, _isCols) {\n        return _this4.getLast(_first + _num + (_first < numToleratedItems ? 2 : 3) * numToleratedItems, _isCols);\n      };\n      var last = isBoth ? {\n        rows: calculateLast(first.rows, numItemsInViewport.rows),\n        cols: calculateLast(first.cols, numItemsInViewport.cols, true)\n      } : calculateLast(first, numItemsInViewport);\n      var state = {\n        numItemsInViewport: numItemsInViewport,\n        last: last,\n        numToleratedItems: numToleratedItems\n      };\n      if (this.props.showLoader) {\n        state['loaderArr'] = Array.from({\n          length: isBoth ? numItemsInViewport.rows : numItemsInViewport\n        });\n      }\n      this.setState(state, function () {\n        if (_this4.props.lazy) {\n          _this4.props.onLazyLoad && _this4.props.onLazyLoad({\n            first: _this4.state.first,\n            last: _this4.state.last\n          });\n        }\n      });\n    }\n  }, {\n    key: \"getLast\",\n    value: function getLast(last, isCols) {\n      return this.props.items ? Math.min(isCols ? this.props.items[0].length : this.props.items.length, last) : 0;\n    }\n  }, {\n    key: \"getContentPadding\",\n    value: function getContentPadding() {\n      if (this.content) {\n        var style = getComputedStyle(this.content);\n        var left = parseInt(style.paddingLeft, 10);\n        var right = parseInt(style.paddingRight, 10);\n        var top = parseInt(style.paddingTop, 10);\n        var bottom = parseInt(style.paddingBottom, 10);\n        return {\n          left: left,\n          right: right,\n          top: top,\n          bottom: bottom,\n          x: left + right,\n          y: top + bottom\n        };\n      }\n      return {\n        left: 0,\n        right: 0,\n        top: 0,\n        bottom: 0,\n        x: 0,\n        y: 0\n      };\n    }\n  }, {\n    key: \"setSize\",\n    value: function setSize() {\n      var _this5 = this;\n      if (this.element) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var parentElement = this.element.parentElement;\n        var width = this.props.scrollWidth || \"\".concat(this.element.offsetWidth || parentElement.offsetWidth, \"px\");\n        var height = this.props.scrollHeight || \"\".concat(this.element.offsetHeight || parentElement.offsetHeight, \"px\");\n        var setProp = function setProp(_name, _value) {\n          return _this5.element.style[_name] = _value;\n        };\n        if (isBoth) {\n          setProp('height', height);\n          setProp('width', width);\n        } else {\n          isHorizontal ? setProp('width', width) : setProp('height', height);\n        }\n      }\n    }\n  }, {\n    key: \"setSpacerSize\",\n    value: function setSpacerSize() {\n      var _this6 = this;\n      var items = this.props.items;\n      if (this.spacer && items) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var itemSize = this.props.itemSize;\n        var contentPadding = this.getContentPadding();\n        var setProp = function setProp(_name, _value, _size) {\n          var _padding = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n          return _this6.spacer.style[_name] = (_value || []).length * _size + _padding + 'px';\n        };\n        if (isBoth) {\n          setProp('height', items[0], itemSize[0], contentPadding.y);\n          setProp('width', items[1], itemSize[1], contentPadding.x);\n        } else {\n          isHorizontal ? setProp('width', items, itemSize, contentPadding.x) : setProp('height', items, itemSize, contentPadding.y);\n        }\n      }\n    }\n  }, {\n    key: \"setContentPosition\",\n    value: function setContentPosition(pos) {\n      var _this7 = this;\n      if (this.content) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var first = pos ? pos.first : this.state.first;\n        var itemSize = this.props.itemSize;\n        var calculateTranslateVal = function calculateTranslateVal(_first, _size) {\n          return _first * _size;\n        };\n        var setTransform = function setTransform() {\n          var _x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var _y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          return _this7.content.style.transform = \"translate3d(\".concat(_x, \"px, \").concat(_y, \"px, 0)\");\n        };\n        if (isBoth) {\n          setTransform(calculateTranslateVal(first.cols, itemSize[1]), calculateTranslateVal(first.rows, itemSize[0]));\n        } else {\n          var translateVal = calculateTranslateVal(first, itemSize);\n          isHorizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n        }\n      }\n    }\n  }, {\n    key: \"onScrollPositionChange\",\n    value: function onScrollPositionChange(event) {\n      var _this8 = this;\n      var target = event.target;\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var _this$state2 = this.state,\n        first = _this$state2.first,\n        last = _this$state2.last,\n        numItemsInViewport = _this$state2.numItemsInViewport,\n        numToleratedItems = _this$state2.numToleratedItems;\n      var itemSize = this.props.itemSize;\n      var contentPadding = this.getContentPadding();\n      var calculateScrollPos = function calculateScrollPos(_pos, _padding) {\n        return _pos ? _pos > _padding ? _pos - _padding : _pos : 0;\n      };\n      var calculateCurrentIndex = function calculateCurrentIndex(_pos, _size) {\n        return Math.floor(_pos / (_size || _pos));\n      };\n      var calculateTriggerIndex = function calculateTriggerIndex(_currentIndex, _first, _last, _num, _isScrollDownOrRight) {\n        return _currentIndex <= numToleratedItems ? numToleratedItems : _isScrollDownOrRight ? _last - _num - numToleratedItems : _first + numToleratedItems - 1;\n      };\n      var calculateFirst = function calculateFirst(_currentIndex, _triggerIndex, _first, _last, _num, _isScrollDownOrRight) {\n        if (_currentIndex <= numToleratedItems) return 0;else return _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - numToleratedItems : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * numToleratedItems;\n      };\n      var calculateLast = function calculateLast(_currentIndex, _first, _last, _num, _isCols) {\n        var lastValue = _first + _num + 2 * numToleratedItems;\n        if (_currentIndex >= numToleratedItems) {\n          lastValue += numToleratedItems + 1;\n        }\n        return _this8.getLast(lastValue, _isCols);\n      };\n      var scrollTop = calculateScrollPos(target.scrollTop, contentPadding.top);\n      var scrollLeft = calculateScrollPos(target.scrollLeft, contentPadding.left);\n      var newFirst = 0;\n      var newLast = last;\n      var isRangeChanged = false;\n      if (isBoth) {\n        var isScrollDown = this.lastScrollPos.top <= scrollTop;\n        var isScrollRight = this.lastScrollPos.left <= scrollLeft;\n        var currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, itemSize[1])\n        };\n        var triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, first.rows, last.rows, numItemsInViewport.rows, isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, first.cols, last.cols, numItemsInViewport.cols, isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, first.rows, last.rows, numItemsInViewport.rows, isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, first.cols, last.cols, numItemsInViewport.cols, isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, last.rows, numItemsInViewport.rows),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, last.cols, numItemsInViewport.cols, true)\n        };\n        isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols || newLast.rows !== last.rows || newLast.cols !== last.cols;\n        this.lastScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      } else {\n        var scrollPos = isHorizontal ? scrollLeft : scrollTop;\n        var isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n        var _currentIndex2 = calculateCurrentIndex(scrollPos, itemSize);\n        var _triggerIndex2 = calculateTriggerIndex(_currentIndex2, first, last, numItemsInViewport, isScrollDownOrRight);\n        newFirst = calculateFirst(_currentIndex2, _triggerIndex2, first, last, numItemsInViewport, isScrollDownOrRight);\n        newLast = calculateLast(_currentIndex2, newFirst, last, numItemsInViewport);\n        isRangeChanged = newFirst !== first || newLast !== last;\n        this.lastScrollPos = scrollPos;\n      }\n      return {\n        first: newFirst,\n        last: newLast,\n        isRangeChanged: isRangeChanged\n      };\n    }\n  }, {\n    key: \"onScrollChange\",\n    value: function onScrollChange(event) {\n      var _this9 = this;\n      var _this$onScrollPositio = this.onScrollPositionChange(event),\n        first = _this$onScrollPositio.first,\n        last = _this$onScrollPositio.last,\n        isRangeChanged = _this$onScrollPositio.isRangeChanged;\n      if (isRangeChanged) {\n        var newState = {\n          first: first,\n          last: last\n        };\n        this.setContentPosition(newState);\n        if (this.props.lazy) {\n          this.props.onLazyLoad && this.props.onLazyLoad(newState);\n        }\n        this.setState(newState, function () {\n          _this9.props.onScrollIndexChange && _this9.props.onScrollIndexChange(newState);\n        });\n      }\n    }\n  }, {\n    key: \"onScroll\",\n    value: function onScroll(event) {\n      var _this10 = this;\n      this.props.onScroll && this.props.onScroll(event);\n      if (this.props.delay) {\n        if (this.scrollTimeout) {\n          clearTimeout(this.scrollTimeout);\n        }\n        if (!this.state.loading && this.props.showLoader) {\n          var _this$onScrollPositio2 = this.onScrollPositionChange(event),\n            changed = _this$onScrollPositio2.isRangeChanged;\n          changed && this.setState({\n            loading: true\n          });\n        }\n        this.scrollTimeout = setTimeout(function () {\n          _this10.onScrollChange(event);\n          if (_this10.state.loading && _this10.props.showLoader && !_this10.props.lazy) {\n            _this10.setState({\n              loading: false\n            });\n          }\n        }, this.props.delay);\n      } else {\n        this.onScrollChange(event);\n      }\n    }\n  }, {\n    key: \"getOptions\",\n    value: function getOptions(index, count) {\n      return {\n        index: index,\n        count: count,\n        first: index === 0,\n        last: index === count - 1,\n        even: index % 2 === 0,\n        odd: index % 2 !== 0,\n        props: this.props\n      };\n    }\n  }, {\n    key: \"init\",\n    value: function init() {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.init();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (prevProps.itemSize !== this.props.itemSize || !prevProps.items || prevProps.items.length !== (this.props.items || []).length) {\n        this.init();\n      }\n      if (this.props.lazy && prevProps.loading !== this.props.loading && this.state.loading !== this.props.loading) {\n        this.setState({\n          loading: this.props.loading\n        });\n      }\n      if (prevProps.orientation !== this.props.orientation) {\n        this.lastScrollPos = this.isBoth() ? {\n          top: 0,\n          left: 0\n        } : 0;\n      }\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(item, index, count, passedItem) {\n      var options = this.getOptions(index, count);\n      var content = ObjectUtils.getJSXElement(this.props.itemTemplate, passedItem || item, options);\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: index\n      }, content);\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this11 = this;\n      var items = this.props.items;\n      if (items && !this.state.loading) {\n        var isBoth = this.isBoth();\n        var _this$state3 = this.state,\n          first = _this$state3.first,\n          last = _this$state3.last;\n        var count = items.length;\n        if (isBoth) {\n          return items.slice(first.rows, last.rows).map(function (item, i) {\n            var items = item.slice(first.cols, last.cols);\n            var index = first.rows + i;\n            return _this11.renderItem(item, index, count, items);\n          });\n        } else {\n          return items.slice(first, last).map(function (item, i) {\n            var index = first + i;\n            return _this11.renderItem(item, index, count);\n          });\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"renderLoaderItem\",\n    value: function renderLoaderItem(index, count, extOptions) {\n      var options = _objectSpread(_objectSpread({}, this.getOptions(index, count)), extOptions || {});\n      var content = ObjectUtils.getJSXElement(this.props.loadingTemplate, options);\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: index\n      }, content);\n    }\n  }, {\n    key: \"renderLoader\",\n    value: function renderLoader() {\n      var _this12 = this;\n      if (this.state.loading) {\n        var className = classNames('p-virtualscroller-loader', {\n          'p-component-overlay': !this.props.loadingTemplate\n        });\n        var content = /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-virtualscroller-loading-icon pi pi-spinner pi-spin\"\n        });\n        if (this.props.loadingTemplate) {\n          var isBoth = this.isBoth();\n          var numItemsInViewport = this.state.numItemsInViewport;\n          var length = isBoth ? numItemsInViewport.rows : numItemsInViewport;\n          content = this.state.loaderArr.map(function (_, index) {\n            return _this12.renderLoaderItem(index, length, isBoth && {\n              numCols: numItemsInViewport.cols\n            });\n          });\n        }\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: className\n        }, content);\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContent\",\n    value: function renderContent() {\n      var _this13 = this;\n      var items = this.renderItems();\n      var content = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-virtualscroller-content\",\n        ref: function ref(el) {\n          return _this13.content = el;\n        }\n      }, items);\n      if (this.props.contentTemplate) {\n        var _this$state4 = this.state,\n          loading = _this$state4.loading,\n          first = _this$state4.first,\n          last = _this$state4.last;\n        var defaultOptions = {\n          className: 'p-virtualscroller-content',\n          ref: function ref(el) {\n            return _this13.content = el;\n          },\n          children: items,\n          element: content,\n          props: this.props,\n          loading: loading,\n          first: first,\n          last: last\n        };\n        return ObjectUtils.getJSXElement(this.props.contentTemplate, defaultOptions);\n      }\n      return content;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this14 = this;\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var className = classNames('p-virtualscroller', {\n        'p-both-scroll': isBoth,\n        'p-horizontal-scroll': isHorizontal\n      }, this.props.className);\n      var loader = this.renderLoader();\n      var content = this.renderContent();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this14.element = el;\n        },\n        className: className,\n        tabIndex: 0,\n        style: this.props.style,\n        onScroll: this.onScroll\n      }, content, /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this14.spacer = el;\n        },\n        className: \"p-virtualscroller-spacer\"\n      }), loader);\n    }\n  }]);\n  return VirtualScroller;\n}(Component);\n_defineProperty(VirtualScroller, \"defaultProps\", {\n  id: null,\n  style: null,\n  className: null,\n  items: null,\n  itemSize: 0,\n  scrollHeight: null,\n  scrollWidth: null,\n  orientation: 'vertical',\n  numToleratedItems: null,\n  delay: 0,\n  lazy: false,\n  showLoader: false,\n  loadingTemplate: null,\n  itemTemplate: null,\n  contentTemplate: null,\n  onScroll: null,\n  onScrollIndexChange: null,\n  onLazyLoad: null\n});\nexport { VirtualScroller };", "map": {"version": 3, "names": ["React", "Component", "ObjectUtils", "classNames", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "constructor", "value", "_typeof", "obj", "Symbol", "iterator", "_possibleConstructorReturn", "call", "_getPrototypeOf", "getPrototypeOf", "_defineProperty", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread", "arguments", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "e", "VirtualScroller", "_Component", "_super", "_this", "isBoth", "state", "first", "rows", "cols", "last", "numItemsInViewport", "numToleratedItems", "loading", "onScroll", "bind", "lastScrollPos", "top", "left", "scrollTo", "options", "element", "scrollToIndex", "index", "_this2", "behavior", "undefined", "isHorizontal", "itemSize", "contentPadding", "getContentPadding", "calculateFirst", "_index", "calculateCoord", "_first", "_size", "_padding", "newFirst", "_new<PERSON><PERSON>t", "scrollInView", "to", "_this3", "_this$getRenderedRang", "getRenderedRange", "viewport", "isToStart", "isToEnd", "pos", "_pos2", "_this$state", "calculateFirstInViewport", "_pos", "Math", "floor", "firstInViewport", "lastInViewport", "scrollTop", "scrollLeft", "scrollPos", "orientation", "calculateOptions", "_this4", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "_itemSize", "ceil", "calculateLast", "_num", "_isCols", "getLast", "<PERSON><PERSON><PERSON><PERSON>", "Array", "from", "setState", "lazy", "onLazyLoad", "isCols", "items", "min", "content", "style", "getComputedStyle", "parseInt", "paddingLeft", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "setSize", "_this5", "parentElement", "width", "scrollWidth", "concat", "height", "scrollHeight", "setProp", "_name", "_value", "setSpacerSize", "_this6", "spacer", "setContentPosition", "_this7", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "_this8", "_this$state2", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "isRangeChanged", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "_currentIndex2", "_triggerIndex2", "onScrollChange", "_this9", "_this$onScrollPositio", "newState", "onScrollIndexChange", "_this10", "delay", "scrollTimeout", "clearTimeout", "_this$onScrollPositio2", "changed", "setTimeout", "getOptions", "count", "even", "odd", "init", "componentDidMount", "componentDidUpdate", "prevProps", "prevState", "renderItem", "item", "passedItem", "getJSXElement", "itemTemplate", "createElement", "Fragment", "renderItems", "_this11", "_this$state3", "slice", "map", "renderLoaderItem", "extOptions", "loadingTemplate", "renderLoader", "_this12", "className", "loaderArr", "_", "numCols", "renderContent", "_this13", "ref", "el", "contentTemplate", "_this$state4", "defaultOptions", "children", "render", "_this14", "loader", "tabIndex", "id"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/primereact/virtualscroller/virtualscroller.esm.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport { ObjectUtils, classNames } from 'primereact/core';\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nvar VirtualScroller = /*#__PURE__*/function (_Component) {\n  _inherits(VirtualScroller, _Component);\n\n  var _super = _createSuper(VirtualScroller);\n\n  function VirtualScroller(props) {\n    var _this;\n\n    _classCallCheck(this, VirtualScroller);\n\n    _this = _super.call(this, props);\n\n    var isBoth = _this.isBoth();\n\n    _this.state = {\n      first: isBoth ? {\n        rows: 0,\n        cols: 0\n      } : 0,\n      last: isBoth ? {\n        rows: 0,\n        cols: 0\n      } : 0,\n      numItemsInViewport: isBoth ? {\n        rows: 0,\n        cols: 0\n      } : 0,\n      numToleratedItems: props.numToleratedItems,\n      loading: false\n    };\n    _this.onScroll = _this.onScroll.bind(_assertThisInitialized(_this));\n    _this.lastScrollPos = isBoth ? {\n      top: 0,\n      left: 0\n    } : 0;\n    return _this;\n  }\n\n  _createClass(VirtualScroller, [{\n    key: \"scrollTo\",\n    value: function scrollTo(options) {\n      this.element && this.element.scrollTo(options);\n    }\n  }, {\n    key: \"scrollToIndex\",\n    value: function scrollToIndex(index) {\n      var _this2 = this;\n\n      var behavior = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'auto';\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var first = this.state.first;\n      var itemSize = this.props.itemSize;\n      var contentPadding = this.getContentPadding();\n\n      var calculateFirst = function calculateFirst() {\n        var _index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n        return _index <= _this2.state.numToleratedItems ? 0 : _index;\n      };\n\n      var calculateCoord = function calculateCoord(_first, _size, _padding) {\n        return _first * _size + _padding;\n      };\n\n      var scrollTo = function scrollTo() {\n        var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n        return _this2.scrollTo({\n          left: left,\n          top: top,\n          behavior: behavior\n        });\n      };\n\n      if (isBoth) {\n        var newFirst = {\n          rows: calculateFirst(index[0]),\n          cols: calculateFirst(index[1])\n        };\n        (newFirst.rows !== first.rows || newFirst.cols !== first.cols) && scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPadding.left), calculateCoord(newFirst.rows, itemSize[0], contentPadding.top));\n      } else {\n        var _newFirst = calculateFirst(index);\n\n        if (_newFirst !== first) {\n          isHorizontal ? scrollTo(calculateCoord(_newFirst, itemSize, contentPadding.left), 0) : scrollTo(0, calculateCoord(_newFirst, itemSize, contentPadding.top));\n        }\n      }\n    }\n  }, {\n    key: \"scrollInView\",\n    value: function scrollInView(index, to) {\n      var _this3 = this;\n\n      var behavior = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'auto';\n\n      if (to) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n\n        var _this$getRenderedRang = this.getRenderedRange(),\n            first = _this$getRenderedRang.first,\n            viewport = _this$getRenderedRang.viewport;\n\n        var itemSize = this.props.itemSize;\n\n        var scrollTo = function scrollTo() {\n          var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n          var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n          return _this3.scrollTo({\n            left: left,\n            top: top,\n            behavior: behavior\n          });\n        };\n\n        var isToStart = to === 'to-start';\n        var isToEnd = to === 'to-end';\n\n        if (isToStart) {\n          if (isBoth) {\n            if (viewport.first.rows - first.rows > index[0]) {\n              scrollTo(viewport.first.cols * itemSize, (viewport.first.rows - 1) * itemSize);\n            } else if (viewport.first.cols - first.cols > index[1]) {\n              scrollTo((viewport.first.cols - 1) * itemSize, viewport.first.rows * itemSize);\n            }\n          } else {\n            if (viewport.first - first > index) {\n              var pos = (viewport.first - 1) * itemSize;\n              isHorizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n            }\n          }\n        } else if (isToEnd) {\n          if (isBoth) {\n            if (viewport.last.rows - first.rows <= index[0] + 1) {\n              scrollTo(viewport.first.cols * itemSize, (viewport.first.rows + 1) * itemSize);\n            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n              scrollTo((viewport.first.cols + 1) * itemSize, viewport.first.rows * itemSize);\n            }\n          } else {\n            if (viewport.last - first <= index + 1) {\n              var _pos2 = (viewport.first + 1) * itemSize;\n\n              isHorizontal ? scrollTo(_pos2, 0) : scrollTo(0, _pos2);\n            }\n          }\n        }\n      } else {\n        this.scrollToIndex(index, behavior);\n      }\n    }\n  }, {\n    key: \"getRenderedRange\",\n    value: function getRenderedRange() {\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var _this$state = this.state,\n          first = _this$state.first,\n          last = _this$state.last,\n          numItemsInViewport = _this$state.numItemsInViewport;\n      var itemSize = this.props.itemSize;\n\n      var calculateFirstInViewport = function calculateFirstInViewport(_pos, _size) {\n        return Math.floor(_pos / (_size || _pos));\n      };\n\n      var firstInViewport = first;\n      var lastInViewport = 0;\n\n      if (this.element) {\n        var scrollTop = this.element.scrollTop;\n        var scrollLeft = this.element.scrollLeft;\n\n        if (isBoth) {\n          firstInViewport = {\n            rows: calculateFirstInViewport(scrollTop, itemSize[0]),\n            cols: calculateFirstInViewport(scrollLeft, itemSize[1])\n          };\n          lastInViewport = {\n            rows: firstInViewport.rows + numItemsInViewport.rows,\n            cols: firstInViewport.cols + numItemsInViewport.cols\n          };\n        } else {\n          var scrollPos = isHorizontal ? scrollLeft : scrollTop;\n          firstInViewport = calculateFirstInViewport(scrollPos, itemSize);\n          lastInViewport = firstInViewport + numItemsInViewport;\n        }\n      }\n\n      return {\n        first: first,\n        last: last,\n        viewport: {\n          first: firstInViewport,\n          last: lastInViewport\n        }\n      };\n    }\n  }, {\n    key: \"isHorizontal\",\n    value: function isHorizontal() {\n      return this.props.orientation === 'horizontal';\n    }\n  }, {\n    key: \"isBoth\",\n    value: function isBoth() {\n      return this.props.orientation === 'both';\n    }\n  }, {\n    key: \"calculateOptions\",\n    value: function calculateOptions() {\n      var _this4 = this;\n\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var first = this.state.first;\n      var itemSize = this.props.itemSize;\n      var contentPadding = this.getContentPadding();\n      var contentWidth = this.element ? this.element.offsetWidth - contentPadding.left : 0;\n      var contentHeight = this.element ? this.element.offsetHeight - contentPadding.top : 0;\n\n      var calculateNumItemsInViewport = function calculateNumItemsInViewport(_contentSize, _itemSize) {\n        return Math.ceil(_contentSize / (_itemSize || _contentSize));\n      };\n\n      var numItemsInViewport = isBoth ? {\n        rows: calculateNumItemsInViewport(contentHeight, itemSize[0]),\n        cols: calculateNumItemsInViewport(contentWidth, itemSize[1])\n      } : calculateNumItemsInViewport(isHorizontal ? contentWidth : contentHeight, itemSize);\n      var numToleratedItems = this.state.numToleratedItems || Math.ceil((isBoth ? numItemsInViewport.rows : numItemsInViewport) / 2);\n\n      var calculateLast = function calculateLast(_first, _num, _isCols) {\n        return _this4.getLast(_first + _num + (_first < numToleratedItems ? 2 : 3) * numToleratedItems, _isCols);\n      };\n\n      var last = isBoth ? {\n        rows: calculateLast(first.rows, numItemsInViewport.rows),\n        cols: calculateLast(first.cols, numItemsInViewport.cols, true)\n      } : calculateLast(first, numItemsInViewport);\n      var state = {\n        numItemsInViewport: numItemsInViewport,\n        last: last,\n        numToleratedItems: numToleratedItems\n      };\n\n      if (this.props.showLoader) {\n        state['loaderArr'] = Array.from({\n          length: isBoth ? numItemsInViewport.rows : numItemsInViewport\n        });\n      }\n\n      this.setState(state, function () {\n        if (_this4.props.lazy) {\n          _this4.props.onLazyLoad && _this4.props.onLazyLoad({\n            first: _this4.state.first,\n            last: _this4.state.last\n          });\n        }\n      });\n    }\n  }, {\n    key: \"getLast\",\n    value: function getLast(last, isCols) {\n      return this.props.items ? Math.min(isCols ? this.props.items[0].length : this.props.items.length, last) : 0;\n    }\n  }, {\n    key: \"getContentPadding\",\n    value: function getContentPadding() {\n      if (this.content) {\n        var style = getComputedStyle(this.content);\n        var left = parseInt(style.paddingLeft, 10);\n        var right = parseInt(style.paddingRight, 10);\n        var top = parseInt(style.paddingTop, 10);\n        var bottom = parseInt(style.paddingBottom, 10);\n        return {\n          left: left,\n          right: right,\n          top: top,\n          bottom: bottom,\n          x: left + right,\n          y: top + bottom\n        };\n      }\n\n      return {\n        left: 0,\n        right: 0,\n        top: 0,\n        bottom: 0,\n        x: 0,\n        y: 0\n      };\n    }\n  }, {\n    key: \"setSize\",\n    value: function setSize() {\n      var _this5 = this;\n\n      if (this.element) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var parentElement = this.element.parentElement;\n        var width = this.props.scrollWidth || \"\".concat(this.element.offsetWidth || parentElement.offsetWidth, \"px\");\n        var height = this.props.scrollHeight || \"\".concat(this.element.offsetHeight || parentElement.offsetHeight, \"px\");\n\n        var setProp = function setProp(_name, _value) {\n          return _this5.element.style[_name] = _value;\n        };\n\n        if (isBoth) {\n          setProp('height', height);\n          setProp('width', width);\n        } else {\n          isHorizontal ? setProp('width', width) : setProp('height', height);\n        }\n      }\n    }\n  }, {\n    key: \"setSpacerSize\",\n    value: function setSpacerSize() {\n      var _this6 = this;\n\n      var items = this.props.items;\n\n      if (this.spacer && items) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var itemSize = this.props.itemSize;\n        var contentPadding = this.getContentPadding();\n\n        var setProp = function setProp(_name, _value, _size) {\n          var _padding = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n\n          return _this6.spacer.style[_name] = (_value || []).length * _size + _padding + 'px';\n        };\n\n        if (isBoth) {\n          setProp('height', items[0], itemSize[0], contentPadding.y);\n          setProp('width', items[1], itemSize[1], contentPadding.x);\n        } else {\n          isHorizontal ? setProp('width', items, itemSize, contentPadding.x) : setProp('height', items, itemSize, contentPadding.y);\n        }\n      }\n    }\n  }, {\n    key: \"setContentPosition\",\n    value: function setContentPosition(pos) {\n      var _this7 = this;\n\n      if (this.content) {\n        var isBoth = this.isBoth();\n        var isHorizontal = this.isHorizontal();\n        var first = pos ? pos.first : this.state.first;\n        var itemSize = this.props.itemSize;\n\n        var calculateTranslateVal = function calculateTranslateVal(_first, _size) {\n          return _first * _size;\n        };\n\n        var setTransform = function setTransform() {\n          var _x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n          var _y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n          return _this7.content.style.transform = \"translate3d(\".concat(_x, \"px, \").concat(_y, \"px, 0)\");\n        };\n\n        if (isBoth) {\n          setTransform(calculateTranslateVal(first.cols, itemSize[1]), calculateTranslateVal(first.rows, itemSize[0]));\n        } else {\n          var translateVal = calculateTranslateVal(first, itemSize);\n          isHorizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n        }\n      }\n    }\n  }, {\n    key: \"onScrollPositionChange\",\n    value: function onScrollPositionChange(event) {\n      var _this8 = this;\n\n      var target = event.target;\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var _this$state2 = this.state,\n          first = _this$state2.first,\n          last = _this$state2.last,\n          numItemsInViewport = _this$state2.numItemsInViewport,\n          numToleratedItems = _this$state2.numToleratedItems;\n      var itemSize = this.props.itemSize;\n      var contentPadding = this.getContentPadding();\n\n      var calculateScrollPos = function calculateScrollPos(_pos, _padding) {\n        return _pos ? _pos > _padding ? _pos - _padding : _pos : 0;\n      };\n\n      var calculateCurrentIndex = function calculateCurrentIndex(_pos, _size) {\n        return Math.floor(_pos / (_size || _pos));\n      };\n\n      var calculateTriggerIndex = function calculateTriggerIndex(_currentIndex, _first, _last, _num, _isScrollDownOrRight) {\n        return _currentIndex <= numToleratedItems ? numToleratedItems : _isScrollDownOrRight ? _last - _num - numToleratedItems : _first + numToleratedItems - 1;\n      };\n\n      var calculateFirst = function calculateFirst(_currentIndex, _triggerIndex, _first, _last, _num, _isScrollDownOrRight) {\n        if (_currentIndex <= numToleratedItems) return 0;else return _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - numToleratedItems : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * numToleratedItems;\n      };\n\n      var calculateLast = function calculateLast(_currentIndex, _first, _last, _num, _isCols) {\n        var lastValue = _first + _num + 2 * numToleratedItems;\n\n        if (_currentIndex >= numToleratedItems) {\n          lastValue += numToleratedItems + 1;\n        }\n\n        return _this8.getLast(lastValue, _isCols);\n      };\n\n      var scrollTop = calculateScrollPos(target.scrollTop, contentPadding.top);\n      var scrollLeft = calculateScrollPos(target.scrollLeft, contentPadding.left);\n      var newFirst = 0;\n      var newLast = last;\n      var isRangeChanged = false;\n\n      if (isBoth) {\n        var isScrollDown = this.lastScrollPos.top <= scrollTop;\n        var isScrollRight = this.lastScrollPos.left <= scrollLeft;\n        var currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, itemSize[1])\n        };\n        var triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, first.rows, last.rows, numItemsInViewport.rows, isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, first.cols, last.cols, numItemsInViewport.cols, isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, first.rows, last.rows, numItemsInViewport.rows, isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, first.cols, last.cols, numItemsInViewport.cols, isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, last.rows, numItemsInViewport.rows),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, last.cols, numItemsInViewport.cols, true)\n        };\n        isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols || newLast.rows !== last.rows || newLast.cols !== last.cols;\n        this.lastScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      } else {\n        var scrollPos = isHorizontal ? scrollLeft : scrollTop;\n        var isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n\n        var _currentIndex2 = calculateCurrentIndex(scrollPos, itemSize);\n\n        var _triggerIndex2 = calculateTriggerIndex(_currentIndex2, first, last, numItemsInViewport, isScrollDownOrRight);\n\n        newFirst = calculateFirst(_currentIndex2, _triggerIndex2, first, last, numItemsInViewport, isScrollDownOrRight);\n        newLast = calculateLast(_currentIndex2, newFirst, last, numItemsInViewport);\n        isRangeChanged = newFirst !== first || newLast !== last;\n        this.lastScrollPos = scrollPos;\n      }\n\n      return {\n        first: newFirst,\n        last: newLast,\n        isRangeChanged: isRangeChanged\n      };\n    }\n  }, {\n    key: \"onScrollChange\",\n    value: function onScrollChange(event) {\n      var _this9 = this;\n\n      var _this$onScrollPositio = this.onScrollPositionChange(event),\n          first = _this$onScrollPositio.first,\n          last = _this$onScrollPositio.last,\n          isRangeChanged = _this$onScrollPositio.isRangeChanged;\n\n      if (isRangeChanged) {\n        var newState = {\n          first: first,\n          last: last\n        };\n        this.setContentPosition(newState);\n\n        if (this.props.lazy) {\n          this.props.onLazyLoad && this.props.onLazyLoad(newState);\n        }\n\n        this.setState(newState, function () {\n          _this9.props.onScrollIndexChange && _this9.props.onScrollIndexChange(newState);\n        });\n      }\n    }\n  }, {\n    key: \"onScroll\",\n    value: function onScroll(event) {\n      var _this10 = this;\n\n      this.props.onScroll && this.props.onScroll(event);\n\n      if (this.props.delay) {\n        if (this.scrollTimeout) {\n          clearTimeout(this.scrollTimeout);\n        }\n\n        if (!this.state.loading && this.props.showLoader) {\n          var _this$onScrollPositio2 = this.onScrollPositionChange(event),\n              changed = _this$onScrollPositio2.isRangeChanged;\n\n          changed && this.setState({\n            loading: true\n          });\n        }\n\n        this.scrollTimeout = setTimeout(function () {\n          _this10.onScrollChange(event);\n\n          if (_this10.state.loading && _this10.props.showLoader && !_this10.props.lazy) {\n            _this10.setState({\n              loading: false\n            });\n          }\n        }, this.props.delay);\n      } else {\n        this.onScrollChange(event);\n      }\n    }\n  }, {\n    key: \"getOptions\",\n    value: function getOptions(index, count) {\n      return {\n        index: index,\n        count: count,\n        first: index === 0,\n        last: index === count - 1,\n        even: index % 2 === 0,\n        odd: index % 2 !== 0,\n        props: this.props\n      };\n    }\n  }, {\n    key: \"init\",\n    value: function init() {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.init();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (prevProps.itemSize !== this.props.itemSize || !prevProps.items || prevProps.items.length !== (this.props.items || []).length) {\n        this.init();\n      }\n\n      if (this.props.lazy && prevProps.loading !== this.props.loading && this.state.loading !== this.props.loading) {\n        this.setState({\n          loading: this.props.loading\n        });\n      }\n\n      if (prevProps.orientation !== this.props.orientation) {\n        this.lastScrollPos = this.isBoth() ? {\n          top: 0,\n          left: 0\n        } : 0;\n      }\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(item, index, count, passedItem) {\n      var options = this.getOptions(index, count);\n      var content = ObjectUtils.getJSXElement(this.props.itemTemplate, passedItem || item, options);\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: index\n      }, content);\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this11 = this;\n\n      var items = this.props.items;\n\n      if (items && !this.state.loading) {\n        var isBoth = this.isBoth();\n        var _this$state3 = this.state,\n            first = _this$state3.first,\n            last = _this$state3.last;\n        var count = items.length;\n\n        if (isBoth) {\n          return items.slice(first.rows, last.rows).map(function (item, i) {\n            var items = item.slice(first.cols, last.cols);\n            var index = first.rows + i;\n            return _this11.renderItem(item, index, count, items);\n          });\n        } else {\n          return items.slice(first, last).map(function (item, i) {\n            var index = first + i;\n            return _this11.renderItem(item, index, count);\n          });\n        }\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderLoaderItem\",\n    value: function renderLoaderItem(index, count, extOptions) {\n      var options = _objectSpread(_objectSpread({}, this.getOptions(index, count)), extOptions || {});\n\n      var content = ObjectUtils.getJSXElement(this.props.loadingTemplate, options);\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: index\n      }, content);\n    }\n  }, {\n    key: \"renderLoader\",\n    value: function renderLoader() {\n      var _this12 = this;\n\n      if (this.state.loading) {\n        var className = classNames('p-virtualscroller-loader', {\n          'p-component-overlay': !this.props.loadingTemplate\n        });\n        var content = /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-virtualscroller-loading-icon pi pi-spinner pi-spin\"\n        });\n\n        if (this.props.loadingTemplate) {\n          var isBoth = this.isBoth();\n          var numItemsInViewport = this.state.numItemsInViewport;\n          var length = isBoth ? numItemsInViewport.rows : numItemsInViewport;\n          content = this.state.loaderArr.map(function (_, index) {\n            return _this12.renderLoaderItem(index, length, isBoth && {\n              numCols: numItemsInViewport.cols\n            });\n          });\n        }\n\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: className\n        }, content);\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderContent\",\n    value: function renderContent() {\n      var _this13 = this;\n\n      var items = this.renderItems();\n      var content = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-virtualscroller-content\",\n        ref: function ref(el) {\n          return _this13.content = el;\n        }\n      }, items);\n\n      if (this.props.contentTemplate) {\n        var _this$state4 = this.state,\n            loading = _this$state4.loading,\n            first = _this$state4.first,\n            last = _this$state4.last;\n        var defaultOptions = {\n          className: 'p-virtualscroller-content',\n          ref: function ref(el) {\n            return _this13.content = el;\n          },\n          children: items,\n          element: content,\n          props: this.props,\n          loading: loading,\n          first: first,\n          last: last\n        };\n        return ObjectUtils.getJSXElement(this.props.contentTemplate, defaultOptions);\n      }\n\n      return content;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this14 = this;\n\n      var isBoth = this.isBoth();\n      var isHorizontal = this.isHorizontal();\n      var className = classNames('p-virtualscroller', {\n        'p-both-scroll': isBoth,\n        'p-horizontal-scroll': isHorizontal\n      }, this.props.className);\n      var loader = this.renderLoader();\n      var content = this.renderContent();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this14.element = el;\n        },\n        className: className,\n        tabIndex: 0,\n        style: this.props.style,\n        onScroll: this.onScroll\n      }, content, /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this14.spacer = el;\n        },\n        className: \"p-virtualscroller-spacer\"\n      }), loader);\n    }\n  }]);\n\n  return VirtualScroller;\n}(Component);\n\n_defineProperty(VirtualScroller, \"defaultProps\", {\n  id: null,\n  style: null,\n  className: null,\n  items: null,\n  itemSize: 0,\n  scrollHeight: null,\n  scrollWidth: null,\n  orientation: 'vertical',\n  numToleratedItems: null,\n  delay: 0,\n  lazy: false,\n  showLoader: false,\n  loadingTemplate: null,\n  itemTemplate: null,\n  contentTemplate: null,\n  onScroll: null,\n  onScrollIndexChange: null,\n  onLazyLoad: null\n});\n\nexport { VirtualScroller };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,UAAU,QAAQ,iBAAiB;AAEzD,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAC3D;AACF;AAEA,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAC5D,OAAOhB,WAAW;AACpB;AAEA,SAASkB,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7BF,eAAe,GAAGV,MAAM,CAACa,cAAc,IAAI,SAASH,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACxED,CAAC,CAACG,SAAS,GAAGF,CAAC;IACf,OAAOD,CAAC;EACV,CAAC;EAED,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B;AAEA,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAI3B,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEA0B,QAAQ,CAACV,SAAS,GAAGN,MAAM,CAACkB,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACX,SAAS,EAAE;IACrEa,WAAW,EAAE;MACXC,KAAK,EAAEJ,QAAQ;MACfjB,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAImB,UAAU,EAAEP,eAAe,CAACM,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASI,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACH,WAAW,KAAKI,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACjB,SAAS,GAAG,QAAQ,GAAG,OAAOgB,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASG,0BAA0BA,CAACjB,IAAI,EAAEkB,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAKL,OAAO,CAACK,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACtE,OAAOA,IAAI;EACb;EAEA,OAAOnB,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASmB,eAAeA,CAAChB,CAAC,EAAE;EAC1BgB,eAAe,GAAG3B,MAAM,CAACa,cAAc,GAAGb,MAAM,CAAC4B,cAAc,GAAG,SAASD,eAAeA,CAAChB,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAId,MAAM,CAAC4B,cAAc,CAACjB,CAAC,CAAC;EAChD,CAAC;EACD,OAAOgB,eAAe,CAAChB,CAAC,CAAC;AAC3B;AAEA,SAASkB,eAAeA,CAACP,GAAG,EAAEpB,GAAG,EAAEkB,KAAK,EAAE;EACxC,IAAIlB,GAAG,IAAIoB,GAAG,EAAE;IACdtB,MAAM,CAACC,cAAc,CAACqB,GAAG,EAAEpB,GAAG,EAAE;MAC9BkB,KAAK,EAAEA,KAAK;MACZvB,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLuB,GAAG,CAACpB,GAAG,CAAC,GAAGkB,KAAK;EAClB;EAEA,OAAOE,GAAG;AACZ;AAEA,SAASQ,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGjC,MAAM,CAACiC,IAAI,CAACF,MAAM,CAAC;EAAE,IAAI/B,MAAM,CAACkC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGnC,MAAM,CAACkC,qBAAqB,CAACH,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOrC,MAAM,CAACsC,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACxC,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEoC,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAExV,SAASQ,aAAaA,CAACjD,MAAM,EAAE;EAAE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,SAAS,CAAC/C,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIiD,MAAM,GAAGD,SAAS,CAAChD,CAAC,CAAC,IAAI,IAAI,GAAGgD,SAAS,CAAChD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEoC,OAAO,CAAC9B,MAAM,CAAC2C,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAU1C,GAAG,EAAE;QAAE2B,eAAe,CAACrC,MAAM,EAAEU,GAAG,EAAEyC,MAAM,CAACzC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIF,MAAM,CAAC6C,yBAAyB,EAAE;MAAE7C,MAAM,CAAC8C,gBAAgB,CAACtD,MAAM,EAAEQ,MAAM,CAAC6C,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAEb,OAAO,CAAC9B,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU1C,GAAG,EAAE;QAAEF,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,GAAG,EAAEF,MAAM,CAACsC,wBAAwB,CAACK,MAAM,EAAEzC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAOV,MAAM;AAAE;AAErhB,SAASuD,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGzB,eAAe,CAACqB,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAG3B,eAAe,CAAC,IAAI,CAAC,CAACR,WAAW;MAAEkC,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEV,SAAS,EAAEY,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAACZ,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;IAAE;IAAE,OAAOjB,0BAA0B,CAAC,IAAI,EAAE4B,MAAM,CAAC;EAAE,CAAC;AAAE;AAExa,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOK,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACrD,SAAS,CAACsD,OAAO,CAAClC,IAAI,CAAC6B,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,IAAIC,eAAe,GAAG,aAAa,UAAUC,UAAU,EAAE;EACvDhD,SAAS,CAAC+C,eAAe,EAAEC,UAAU,CAAC;EAEtC,IAAIC,MAAM,GAAGjB,YAAY,CAACe,eAAe,CAAC;EAE1C,SAASA,eAAeA,CAACrE,KAAK,EAAE;IAC9B,IAAIwE,KAAK;IAET9E,eAAe,CAAC,IAAI,EAAE2E,eAAe,CAAC;IAEtCG,KAAK,GAAGD,MAAM,CAACtC,IAAI,CAAC,IAAI,EAAEjC,KAAK,CAAC;IAEhC,IAAIyE,MAAM,GAAGD,KAAK,CAACC,MAAM,CAAC,CAAC;IAE3BD,KAAK,CAACE,KAAK,GAAG;MACZC,KAAK,EAAEF,MAAM,GAAG;QACdG,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE;MACR,CAAC,GAAG,CAAC;MACLC,IAAI,EAAEL,MAAM,GAAG;QACbG,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE;MACR,CAAC,GAAG,CAAC;MACLE,kBAAkB,EAAEN,MAAM,GAAG;QAC3BG,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE;MACR,CAAC,GAAG,CAAC;MACLG,iBAAiB,EAAEhF,KAAK,CAACgF,iBAAiB;MAC1CC,OAAO,EAAE;IACX,CAAC;IACDT,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACC,IAAI,CAACrE,sBAAsB,CAAC0D,KAAK,CAAC,CAAC;IACnEA,KAAK,CAACY,aAAa,GAAGX,MAAM,GAAG;MAC7BY,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;IACL,OAAOd,KAAK;EACd;EAEA9D,YAAY,CAAC2D,eAAe,EAAE,CAAC;IAC7B5D,GAAG,EAAE,UAAU;IACfkB,KAAK,EAAE,SAAS4D,QAAQA,CAACC,OAAO,EAAE;MAChC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACF,QAAQ,CAACC,OAAO,CAAC;IAChD;EACF,CAAC,EAAE;IACD/E,GAAG,EAAE,eAAe;IACpBkB,KAAK,EAAE,SAAS+D,aAAaA,CAACC,KAAK,EAAE;MACnC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,QAAQ,GAAG5C,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;MACzF,IAAIwB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;MAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;MACtC,IAAIpB,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK;MAC5B,IAAIqB,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;MAClC,IAAIC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAE7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC7C,IAAIC,MAAM,GAAGnD,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAElF,OAAOmD,MAAM,IAAIR,MAAM,CAAClB,KAAK,CAACM,iBAAiB,GAAG,CAAC,GAAGoB,MAAM;MAC9D,CAAC;MAED,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE;QACpE,OAAOF,MAAM,GAAGC,KAAK,GAAGC,QAAQ;MAClC,CAAC;MAED,IAAIjB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;QACjC,IAAID,IAAI,GAAGrC,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAChF,IAAIoC,GAAG,GAAGpC,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/E,OAAO2C,MAAM,CAACL,QAAQ,CAAC;UACrBD,IAAI,EAAEA,IAAI;UACVD,GAAG,EAAEA,GAAG;UACRQ,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ,CAAC;MAED,IAAIpB,MAAM,EAAE;QACV,IAAIgC,QAAQ,GAAG;UACb7B,IAAI,EAAEuB,cAAc,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC;UAC9Bd,IAAI,EAAEsB,cAAc,CAACR,KAAK,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD,CAACc,QAAQ,CAAC7B,IAAI,KAAKD,KAAK,CAACC,IAAI,IAAI6B,QAAQ,CAAC5B,IAAI,KAAKF,KAAK,CAACE,IAAI,KAAKU,QAAQ,CAACc,cAAc,CAACI,QAAQ,CAAC5B,IAAI,EAAEmB,QAAQ,CAAC,CAAC,CAAC,EAAEC,cAAc,CAACX,IAAI,CAAC,EAAEe,cAAc,CAACI,QAAQ,CAAC7B,IAAI,EAAEoB,QAAQ,CAAC,CAAC,CAAC,EAAEC,cAAc,CAACZ,GAAG,CAAC,CAAC;MAC7M,CAAC,MAAM;QACL,IAAIqB,SAAS,GAAGP,cAAc,CAACR,KAAK,CAAC;QAErC,IAAIe,SAAS,KAAK/B,KAAK,EAAE;UACvBoB,YAAY,GAAGR,QAAQ,CAACc,cAAc,CAACK,SAAS,EAAEV,QAAQ,EAAEC,cAAc,CAACX,IAAI,CAAC,EAAE,CAAC,CAAC,GAAGC,QAAQ,CAAC,CAAC,EAAEc,cAAc,CAACK,SAAS,EAAEV,QAAQ,EAAEC,cAAc,CAACZ,GAAG,CAAC,CAAC;QAC7J;MACF;IACF;EACF,CAAC,EAAE;IACD5E,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAASgF,YAAYA,CAAChB,KAAK,EAAEiB,EAAE,EAAE;MACtC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIhB,QAAQ,GAAG5C,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;MAEzF,IAAI2D,EAAE,EAAE;QACN,IAAInC,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;QAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;QAEtC,IAAIe,qBAAqB,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;UAC/CpC,KAAK,GAAGmC,qBAAqB,CAACnC,KAAK;UACnCqC,QAAQ,GAAGF,qBAAqB,CAACE,QAAQ;QAE7C,IAAIhB,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;QAElC,IAAIT,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;UACjC,IAAID,IAAI,GAAGrC,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;UAChF,IAAIoC,GAAG,GAAGpC,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;UAC/E,OAAO4D,MAAM,CAACtB,QAAQ,CAAC;YACrBD,IAAI,EAAEA,IAAI;YACVD,GAAG,EAAEA,GAAG;YACRQ,QAAQ,EAAEA;UACZ,CAAC,CAAC;QACJ,CAAC;QAED,IAAIoB,SAAS,GAAGL,EAAE,KAAK,UAAU;QACjC,IAAIM,OAAO,GAAGN,EAAE,KAAK,QAAQ;QAE7B,IAAIK,SAAS,EAAE;UACb,IAAIxC,MAAM,EAAE;YACV,IAAIuC,QAAQ,CAACrC,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,GAAGe,KAAK,CAAC,CAAC,CAAC,EAAE;cAC/CJ,QAAQ,CAACyB,QAAQ,CAACrC,KAAK,CAACE,IAAI,GAAGmB,QAAQ,EAAE,CAACgB,QAAQ,CAACrC,KAAK,CAACC,IAAI,GAAG,CAAC,IAAIoB,QAAQ,CAAC;YAChF,CAAC,MAAM,IAAIgB,QAAQ,CAACrC,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,GAAGc,KAAK,CAAC,CAAC,CAAC,EAAE;cACtDJ,QAAQ,CAAC,CAACyB,QAAQ,CAACrC,KAAK,CAACE,IAAI,GAAG,CAAC,IAAImB,QAAQ,EAAEgB,QAAQ,CAACrC,KAAK,CAACC,IAAI,GAAGoB,QAAQ,CAAC;YAChF;UACF,CAAC,MAAM;YACL,IAAIgB,QAAQ,CAACrC,KAAK,GAAGA,KAAK,GAAGgB,KAAK,EAAE;cAClC,IAAIwB,GAAG,GAAG,CAACH,QAAQ,CAACrC,KAAK,GAAG,CAAC,IAAIqB,QAAQ;cACzCD,YAAY,GAAGR,QAAQ,CAAC4B,GAAG,EAAE,CAAC,CAAC,GAAG5B,QAAQ,CAAC,CAAC,EAAE4B,GAAG,CAAC;YACpD;UACF;QACF,CAAC,MAAM,IAAID,OAAO,EAAE;UAClB,IAAIzC,MAAM,EAAE;YACV,IAAIuC,QAAQ,CAAClC,IAAI,CAACF,IAAI,GAAGD,KAAK,CAACC,IAAI,IAAIe,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;cACnDJ,QAAQ,CAACyB,QAAQ,CAACrC,KAAK,CAACE,IAAI,GAAGmB,QAAQ,EAAE,CAACgB,QAAQ,CAACrC,KAAK,CAACC,IAAI,GAAG,CAAC,IAAIoB,QAAQ,CAAC;YAChF,CAAC,MAAM,IAAIgB,QAAQ,CAAClC,IAAI,CAACD,IAAI,GAAGF,KAAK,CAACE,IAAI,IAAIc,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;cAC1DJ,QAAQ,CAAC,CAACyB,QAAQ,CAACrC,KAAK,CAACE,IAAI,GAAG,CAAC,IAAImB,QAAQ,EAAEgB,QAAQ,CAACrC,KAAK,CAACC,IAAI,GAAGoB,QAAQ,CAAC;YAChF;UACF,CAAC,MAAM;YACL,IAAIgB,QAAQ,CAAClC,IAAI,GAAGH,KAAK,IAAIgB,KAAK,GAAG,CAAC,EAAE;cACtC,IAAIyB,KAAK,GAAG,CAACJ,QAAQ,CAACrC,KAAK,GAAG,CAAC,IAAIqB,QAAQ;cAE3CD,YAAY,GAAGR,QAAQ,CAAC6B,KAAK,EAAE,CAAC,CAAC,GAAG7B,QAAQ,CAAC,CAAC,EAAE6B,KAAK,CAAC;YACxD;UACF;QACF;MACF,CAAC,MAAM;QACL,IAAI,CAAC1B,aAAa,CAACC,KAAK,EAAEE,QAAQ,CAAC;MACrC;IACF;EACF,CAAC,EAAE;IACDpF,GAAG,EAAE,kBAAkB;IACvBkB,KAAK,EAAE,SAASoF,gBAAgBA,CAAA,EAAG;MACjC,IAAItC,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;MAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;MACtC,IAAIsB,WAAW,GAAG,IAAI,CAAC3C,KAAK;QACxBC,KAAK,GAAG0C,WAAW,CAAC1C,KAAK;QACzBG,IAAI,GAAGuC,WAAW,CAACvC,IAAI;QACvBC,kBAAkB,GAAGsC,WAAW,CAACtC,kBAAkB;MACvD,IAAIiB,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;MAElC,IAAIsB,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAEhB,KAAK,EAAE;QAC5E,OAAOiB,IAAI,CAACC,KAAK,CAACF,IAAI,IAAIhB,KAAK,IAAIgB,IAAI,CAAC,CAAC;MAC3C,CAAC;MAED,IAAIG,eAAe,GAAG/C,KAAK;MAC3B,IAAIgD,cAAc,GAAG,CAAC;MAEtB,IAAI,IAAI,CAAClC,OAAO,EAAE;QAChB,IAAImC,SAAS,GAAG,IAAI,CAACnC,OAAO,CAACmC,SAAS;QACtC,IAAIC,UAAU,GAAG,IAAI,CAACpC,OAAO,CAACoC,UAAU;QAExC,IAAIpD,MAAM,EAAE;UACViD,eAAe,GAAG;YAChB9C,IAAI,EAAE0C,wBAAwB,CAACM,SAAS,EAAE5B,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtDnB,IAAI,EAAEyC,wBAAwB,CAACO,UAAU,EAAE7B,QAAQ,CAAC,CAAC,CAAC;UACxD,CAAC;UACD2B,cAAc,GAAG;YACf/C,IAAI,EAAE8C,eAAe,CAAC9C,IAAI,GAAGG,kBAAkB,CAACH,IAAI;YACpDC,IAAI,EAAE6C,eAAe,CAAC7C,IAAI,GAAGE,kBAAkB,CAACF;UAClD,CAAC;QACH,CAAC,MAAM;UACL,IAAIiD,SAAS,GAAG/B,YAAY,GAAG8B,UAAU,GAAGD,SAAS;UACrDF,eAAe,GAAGJ,wBAAwB,CAACQ,SAAS,EAAE9B,QAAQ,CAAC;UAC/D2B,cAAc,GAAGD,eAAe,GAAG3C,kBAAkB;QACvD;MACF;MAEA,OAAO;QACLJ,KAAK,EAAEA,KAAK;QACZG,IAAI,EAAEA,IAAI;QACVkC,QAAQ,EAAE;UACRrC,KAAK,EAAE+C,eAAe;UACtB5C,IAAI,EAAE6C;QACR;MACF,CAAC;IACH;EACF,CAAC,EAAE;IACDlH,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAASoE,YAAYA,CAAA,EAAG;MAC7B,OAAO,IAAI,CAAC/F,KAAK,CAAC+H,WAAW,KAAK,YAAY;IAChD;EACF,CAAC,EAAE;IACDtH,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAAS8C,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACzE,KAAK,CAAC+H,WAAW,KAAK,MAAM;IAC1C;EACF,CAAC,EAAE;IACDtH,GAAG,EAAE,kBAAkB;IACvBkB,KAAK,EAAE,SAASqG,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIxD,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;MAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;MACtC,IAAIpB,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK;MAC5B,IAAIqB,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;MAClC,IAAIC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC7C,IAAIgC,YAAY,GAAG,IAAI,CAACzC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC0C,WAAW,GAAGlC,cAAc,CAACX,IAAI,GAAG,CAAC;MACpF,IAAI8C,aAAa,GAAG,IAAI,CAAC3C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4C,YAAY,GAAGpC,cAAc,CAACZ,GAAG,GAAG,CAAC;MAErF,IAAIiD,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,YAAY,EAAEC,SAAS,EAAE;QAC9F,OAAOhB,IAAI,CAACiB,IAAI,CAACF,YAAY,IAAIC,SAAS,IAAID,YAAY,CAAC,CAAC;MAC9D,CAAC;MAED,IAAIxD,kBAAkB,GAAGN,MAAM,GAAG;QAChCG,IAAI,EAAE0D,2BAA2B,CAACF,aAAa,EAAEpC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7DnB,IAAI,EAAEyD,2BAA2B,CAACJ,YAAY,EAAElC,QAAQ,CAAC,CAAC,CAAC;MAC7D,CAAC,GAAGsC,2BAA2B,CAACvC,YAAY,GAAGmC,YAAY,GAAGE,aAAa,EAAEpC,QAAQ,CAAC;MACtF,IAAIhB,iBAAiB,GAAG,IAAI,CAACN,KAAK,CAACM,iBAAiB,IAAIwC,IAAI,CAACiB,IAAI,CAAC,CAAChE,MAAM,GAAGM,kBAAkB,CAACH,IAAI,GAAGG,kBAAkB,IAAI,CAAC,CAAC;MAE9H,IAAI2D,aAAa,GAAG,SAASA,aAAaA,CAACpC,MAAM,EAAEqC,IAAI,EAAEC,OAAO,EAAE;QAChE,OAAOX,MAAM,CAACY,OAAO,CAACvC,MAAM,GAAGqC,IAAI,GAAG,CAACrC,MAAM,GAAGtB,iBAAiB,GAAG,CAAC,GAAG,CAAC,IAAIA,iBAAiB,EAAE4D,OAAO,CAAC;MAC1G,CAAC;MAED,IAAI9D,IAAI,GAAGL,MAAM,GAAG;QAClBG,IAAI,EAAE8D,aAAa,CAAC/D,KAAK,CAACC,IAAI,EAAEG,kBAAkB,CAACH,IAAI,CAAC;QACxDC,IAAI,EAAE6D,aAAa,CAAC/D,KAAK,CAACE,IAAI,EAAEE,kBAAkB,CAACF,IAAI,EAAE,IAAI;MAC/D,CAAC,GAAG6D,aAAa,CAAC/D,KAAK,EAAEI,kBAAkB,CAAC;MAC5C,IAAIL,KAAK,GAAG;QACVK,kBAAkB,EAAEA,kBAAkB;QACtCD,IAAI,EAAEA,IAAI;QACVE,iBAAiB,EAAEA;MACrB,CAAC;MAED,IAAI,IAAI,CAAChF,KAAK,CAAC8I,UAAU,EAAE;QACzBpE,KAAK,CAAC,WAAW,CAAC,GAAGqE,KAAK,CAACC,IAAI,CAAC;UAC9B9I,MAAM,EAAEuE,MAAM,GAAGM,kBAAkB,CAACH,IAAI,GAAGG;QAC7C,CAAC,CAAC;MACJ;MAEA,IAAI,CAACkE,QAAQ,CAACvE,KAAK,EAAE,YAAY;QAC/B,IAAIuD,MAAM,CAACjI,KAAK,CAACkJ,IAAI,EAAE;UACrBjB,MAAM,CAACjI,KAAK,CAACmJ,UAAU,IAAIlB,MAAM,CAACjI,KAAK,CAACmJ,UAAU,CAAC;YACjDxE,KAAK,EAAEsD,MAAM,CAACvD,KAAK,CAACC,KAAK;YACzBG,IAAI,EAAEmD,MAAM,CAACvD,KAAK,CAACI;UACrB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,SAAS;IACdkB,KAAK,EAAE,SAASkH,OAAOA,CAAC/D,IAAI,EAAEsE,MAAM,EAAE;MACpC,OAAO,IAAI,CAACpJ,KAAK,CAACqJ,KAAK,GAAG7B,IAAI,CAAC8B,GAAG,CAACF,MAAM,GAAG,IAAI,CAACpJ,KAAK,CAACqJ,KAAK,CAAC,CAAC,CAAC,CAACnJ,MAAM,GAAG,IAAI,CAACF,KAAK,CAACqJ,KAAK,CAACnJ,MAAM,EAAE4E,IAAI,CAAC,GAAG,CAAC;IAC7G;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,mBAAmB;IACxBkB,KAAK,EAAE,SAASuE,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACqD,OAAO,EAAE;QAChB,IAAIC,KAAK,GAAGC,gBAAgB,CAAC,IAAI,CAACF,OAAO,CAAC;QAC1C,IAAIjE,IAAI,GAAGoE,QAAQ,CAACF,KAAK,CAACG,WAAW,EAAE,EAAE,CAAC;QAC1C,IAAIC,KAAK,GAAGF,QAAQ,CAACF,KAAK,CAACK,YAAY,EAAE,EAAE,CAAC;QAC5C,IAAIxE,GAAG,GAAGqE,QAAQ,CAACF,KAAK,CAACM,UAAU,EAAE,EAAE,CAAC;QACxC,IAAIC,MAAM,GAAGL,QAAQ,CAACF,KAAK,CAACQ,aAAa,EAAE,EAAE,CAAC;QAC9C,OAAO;UACL1E,IAAI,EAAEA,IAAI;UACVsE,KAAK,EAAEA,KAAK;UACZvE,GAAG,EAAEA,GAAG;UACR0E,MAAM,EAAEA,MAAM;UACdE,CAAC,EAAE3E,IAAI,GAAGsE,KAAK;UACfM,CAAC,EAAE7E,GAAG,GAAG0E;QACX,CAAC;MACH;MAEA,OAAO;QACLzE,IAAI,EAAE,CAAC;QACPsE,KAAK,EAAE,CAAC;QACRvE,GAAG,EAAE,CAAC;QACN0E,MAAM,EAAE,CAAC;QACTE,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;IACH;EACF,CAAC,EAAE;IACDzJ,GAAG,EAAE,SAAS;IACdkB,KAAK,EAAE,SAASwI,OAAOA,CAAA,EAAG;MACxB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAC3E,OAAO,EAAE;QAChB,IAAIhB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;QAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;QACtC,IAAIsE,aAAa,GAAG,IAAI,CAAC5E,OAAO,CAAC4E,aAAa;QAC9C,IAAIC,KAAK,GAAG,IAAI,CAACtK,KAAK,CAACuK,WAAW,IAAI,EAAE,CAACC,MAAM,CAAC,IAAI,CAAC/E,OAAO,CAAC0C,WAAW,IAAIkC,aAAa,CAAClC,WAAW,EAAE,IAAI,CAAC;QAC5G,IAAIsC,MAAM,GAAG,IAAI,CAACzK,KAAK,CAAC0K,YAAY,IAAI,EAAE,CAACF,MAAM,CAAC,IAAI,CAAC/E,OAAO,CAAC4C,YAAY,IAAIgC,aAAa,CAAChC,YAAY,EAAE,IAAI,CAAC;QAEhH,IAAIsC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;UAC5C,OAAOT,MAAM,CAAC3E,OAAO,CAAC+D,KAAK,CAACoB,KAAK,CAAC,GAAGC,MAAM;QAC7C,CAAC;QAED,IAAIpG,MAAM,EAAE;UACVkG,OAAO,CAAC,QAAQ,EAAEF,MAAM,CAAC;UACzBE,OAAO,CAAC,OAAO,EAAEL,KAAK,CAAC;QACzB,CAAC,MAAM;UACLvE,YAAY,GAAG4E,OAAO,CAAC,OAAO,EAAEL,KAAK,CAAC,GAAGK,OAAO,CAAC,QAAQ,EAAEF,MAAM,CAAC;QACpE;MACF;IACF;EACF,CAAC,EAAE;IACDhK,GAAG,EAAE,eAAe;IACpBkB,KAAK,EAAE,SAASmJ,aAAaA,CAAA,EAAG;MAC9B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI1B,KAAK,GAAG,IAAI,CAACrJ,KAAK,CAACqJ,KAAK;MAE5B,IAAI,IAAI,CAAC2B,MAAM,IAAI3B,KAAK,EAAE;QACxB,IAAI5E,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;QAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;QACtC,IAAIC,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;QAClC,IAAIC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAE7C,IAAIyE,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAEtE,KAAK,EAAE;UACnD,IAAIC,QAAQ,GAAGvD,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;UAEpF,OAAO8H,MAAM,CAACC,MAAM,CAACxB,KAAK,CAACoB,KAAK,CAAC,GAAG,CAACC,MAAM,IAAI,EAAE,EAAE3K,MAAM,GAAGqG,KAAK,GAAGC,QAAQ,GAAG,IAAI;QACrF,CAAC;QAED,IAAI/B,MAAM,EAAE;UACVkG,OAAO,CAAC,QAAQ,EAAEtB,KAAK,CAAC,CAAC,CAAC,EAAErD,QAAQ,CAAC,CAAC,CAAC,EAAEC,cAAc,CAACiE,CAAC,CAAC;UAC1DS,OAAO,CAAC,OAAO,EAAEtB,KAAK,CAAC,CAAC,CAAC,EAAErD,QAAQ,CAAC,CAAC,CAAC,EAAEC,cAAc,CAACgE,CAAC,CAAC;QAC3D,CAAC,MAAM;UACLlE,YAAY,GAAG4E,OAAO,CAAC,OAAO,EAAEtB,KAAK,EAAErD,QAAQ,EAAEC,cAAc,CAACgE,CAAC,CAAC,GAAGU,OAAO,CAAC,QAAQ,EAAEtB,KAAK,EAAErD,QAAQ,EAAEC,cAAc,CAACiE,CAAC,CAAC;QAC3H;MACF;IACF;EACF,CAAC,EAAE;IACDzJ,GAAG,EAAE,oBAAoB;IACzBkB,KAAK,EAAE,SAASsJ,kBAAkBA,CAAC9D,GAAG,EAAE;MACtC,IAAI+D,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAC3B,OAAO,EAAE;QAChB,IAAI9E,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;QAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;QACtC,IAAIpB,KAAK,GAAGwC,GAAG,GAAGA,GAAG,CAACxC,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK;QAC9C,IAAIqB,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;QAElC,IAAImF,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC7E,MAAM,EAAEC,KAAK,EAAE;UACxE,OAAOD,MAAM,GAAGC,KAAK;QACvB,CAAC;QAED,IAAI6E,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;UACzC,IAAIC,EAAE,GAAGpI,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;UAE9E,IAAIqI,EAAE,GAAGrI,SAAS,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,SAAS,CAAC,CAAC,CAAC,KAAK6C,SAAS,GAAG7C,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;UAE9E,OAAOiI,MAAM,CAAC3B,OAAO,CAACC,KAAK,CAAC+B,SAAS,GAAG,cAAc,CAACf,MAAM,CAACa,EAAE,EAAE,MAAM,CAAC,CAACb,MAAM,CAACc,EAAE,EAAE,QAAQ,CAAC;QAChG,CAAC;QAED,IAAI7G,MAAM,EAAE;UACV2G,YAAY,CAACD,qBAAqB,CAACxG,KAAK,CAACE,IAAI,EAAEmB,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEmF,qBAAqB,CAACxG,KAAK,CAACC,IAAI,EAAEoB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9G,CAAC,MAAM;UACL,IAAIwF,YAAY,GAAGL,qBAAqB,CAACxG,KAAK,EAAEqB,QAAQ,CAAC;UACzDD,YAAY,GAAGqF,YAAY,CAACI,YAAY,EAAE,CAAC,CAAC,GAAGJ,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC;QAC9E;MACF;IACF;EACF,CAAC,EAAE;IACD/K,GAAG,EAAE,wBAAwB;IAC7BkB,KAAK,EAAE,SAAS8J,sBAAsBA,CAACC,KAAK,EAAE;MAC5C,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI5L,MAAM,GAAG2L,KAAK,CAAC3L,MAAM;MACzB,IAAI0E,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;MAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;MACtC,IAAI6F,YAAY,GAAG,IAAI,CAAClH,KAAK;QACzBC,KAAK,GAAGiH,YAAY,CAACjH,KAAK;QAC1BG,IAAI,GAAG8G,YAAY,CAAC9G,IAAI;QACxBC,kBAAkB,GAAG6G,YAAY,CAAC7G,kBAAkB;QACpDC,iBAAiB,GAAG4G,YAAY,CAAC5G,iBAAiB;MACtD,IAAIgB,QAAQ,GAAG,IAAI,CAAChG,KAAK,CAACgG,QAAQ;MAClC,IAAIC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAE7C,IAAI2F,kBAAkB,GAAG,SAASA,kBAAkBA,CAACtE,IAAI,EAAEf,QAAQ,EAAE;QACnE,OAAOe,IAAI,GAAGA,IAAI,GAAGf,QAAQ,GAAGe,IAAI,GAAGf,QAAQ,GAAGe,IAAI,GAAG,CAAC;MAC5D,CAAC;MAED,IAAIuE,qBAAqB,GAAG,SAASA,qBAAqBA,CAACvE,IAAI,EAAEhB,KAAK,EAAE;QACtE,OAAOiB,IAAI,CAACC,KAAK,CAACF,IAAI,IAAIhB,KAAK,IAAIgB,IAAI,CAAC,CAAC;MAC3C,CAAC;MAED,IAAIwE,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,aAAa,EAAE1F,MAAM,EAAE2F,KAAK,EAAEtD,IAAI,EAAEuD,oBAAoB,EAAE;QACnH,OAAOF,aAAa,IAAIhH,iBAAiB,GAAGA,iBAAiB,GAAGkH,oBAAoB,GAAGD,KAAK,GAAGtD,IAAI,GAAG3D,iBAAiB,GAAGsB,MAAM,GAAGtB,iBAAiB,GAAG,CAAC;MAC1J,CAAC;MAED,IAAImB,cAAc,GAAG,SAASA,cAAcA,CAAC6F,aAAa,EAAEG,aAAa,EAAE7F,MAAM,EAAE2F,KAAK,EAAEtD,IAAI,EAAEuD,oBAAoB,EAAE;QACpH,IAAIF,aAAa,IAAIhH,iBAAiB,EAAE,OAAO,CAAC,CAAC,KAAK,OAAOkH,oBAAoB,GAAGF,aAAa,GAAGG,aAAa,GAAG7F,MAAM,GAAG0F,aAAa,GAAGhH,iBAAiB,GAAGgH,aAAa,GAAGG,aAAa,GAAG7F,MAAM,GAAG0F,aAAa,GAAG,CAAC,GAAGhH,iBAAiB;MACjP,CAAC;MAED,IAAI0D,aAAa,GAAG,SAASA,aAAaA,CAACsD,aAAa,EAAE1F,MAAM,EAAE2F,KAAK,EAAEtD,IAAI,EAAEC,OAAO,EAAE;QACtF,IAAIwD,SAAS,GAAG9F,MAAM,GAAGqC,IAAI,GAAG,CAAC,GAAG3D,iBAAiB;QAErD,IAAIgH,aAAa,IAAIhH,iBAAiB,EAAE;UACtCoH,SAAS,IAAIpH,iBAAiB,GAAG,CAAC;QACpC;QAEA,OAAO2G,MAAM,CAAC9C,OAAO,CAACuD,SAAS,EAAExD,OAAO,CAAC;MAC3C,CAAC;MAED,IAAIhB,SAAS,GAAGiE,kBAAkB,CAAC9L,MAAM,CAAC6H,SAAS,EAAE3B,cAAc,CAACZ,GAAG,CAAC;MACxE,IAAIwC,UAAU,GAAGgE,kBAAkB,CAAC9L,MAAM,CAAC8H,UAAU,EAAE5B,cAAc,CAACX,IAAI,CAAC;MAC3E,IAAImB,QAAQ,GAAG,CAAC;MAChB,IAAI4F,OAAO,GAAGvH,IAAI;MAClB,IAAIwH,cAAc,GAAG,KAAK;MAE1B,IAAI7H,MAAM,EAAE;QACV,IAAI8H,YAAY,GAAG,IAAI,CAACnH,aAAa,CAACC,GAAG,IAAIuC,SAAS;QACtD,IAAI4E,aAAa,GAAG,IAAI,CAACpH,aAAa,CAACE,IAAI,IAAIuC,UAAU;QACzD,IAAI4E,YAAY,GAAG;UACjB7H,IAAI,EAAEkH,qBAAqB,CAAClE,SAAS,EAAE5B,QAAQ,CAAC,CAAC,CAAC,CAAC;UACnDnB,IAAI,EAAEiH,qBAAqB,CAACjE,UAAU,EAAE7B,QAAQ,CAAC,CAAC,CAAC;QACrD,CAAC;QACD,IAAI0G,YAAY,GAAG;UACjB9H,IAAI,EAAEmH,qBAAqB,CAACU,YAAY,CAAC7H,IAAI,EAAED,KAAK,CAACC,IAAI,EAAEE,IAAI,CAACF,IAAI,EAAEG,kBAAkB,CAACH,IAAI,EAAE2H,YAAY,CAAC;UAC5G1H,IAAI,EAAEkH,qBAAqB,CAACU,YAAY,CAAC5H,IAAI,EAAEF,KAAK,CAACE,IAAI,EAAEC,IAAI,CAACD,IAAI,EAAEE,kBAAkB,CAACF,IAAI,EAAE2H,aAAa;QAC9G,CAAC;QACD/F,QAAQ,GAAG;UACT7B,IAAI,EAAEuB,cAAc,CAACsG,YAAY,CAAC7H,IAAI,EAAE8H,YAAY,CAAC9H,IAAI,EAAED,KAAK,CAACC,IAAI,EAAEE,IAAI,CAACF,IAAI,EAAEG,kBAAkB,CAACH,IAAI,EAAE2H,YAAY,CAAC;UACxH1H,IAAI,EAAEsB,cAAc,CAACsG,YAAY,CAAC5H,IAAI,EAAE6H,YAAY,CAAC7H,IAAI,EAAEF,KAAK,CAACE,IAAI,EAAEC,IAAI,CAACD,IAAI,EAAEE,kBAAkB,CAACF,IAAI,EAAE2H,aAAa;QAC1H,CAAC;QACDH,OAAO,GAAG;UACRzH,IAAI,EAAE8D,aAAa,CAAC+D,YAAY,CAAC7H,IAAI,EAAE6B,QAAQ,CAAC7B,IAAI,EAAEE,IAAI,CAACF,IAAI,EAAEG,kBAAkB,CAACH,IAAI,CAAC;UACzFC,IAAI,EAAE6D,aAAa,CAAC+D,YAAY,CAAC5H,IAAI,EAAE4B,QAAQ,CAAC5B,IAAI,EAAEC,IAAI,CAACD,IAAI,EAAEE,kBAAkB,CAACF,IAAI,EAAE,IAAI;QAChG,CAAC;QACDyH,cAAc,GAAG7F,QAAQ,CAAC7B,IAAI,KAAKD,KAAK,CAACC,IAAI,IAAI6B,QAAQ,CAAC5B,IAAI,KAAKF,KAAK,CAACE,IAAI,IAAIwH,OAAO,CAACzH,IAAI,KAAKE,IAAI,CAACF,IAAI,IAAIyH,OAAO,CAACxH,IAAI,KAAKC,IAAI,CAACD,IAAI;QACzI,IAAI,CAACO,aAAa,GAAG;UACnBC,GAAG,EAAEuC,SAAS;UACdtC,IAAI,EAAEuC;QACR,CAAC;MACH,CAAC,MAAM;QACL,IAAIC,SAAS,GAAG/B,YAAY,GAAG8B,UAAU,GAAGD,SAAS;QACrD,IAAI+E,mBAAmB,GAAG,IAAI,CAACvH,aAAa,IAAI0C,SAAS;QAEzD,IAAI8E,cAAc,GAAGd,qBAAqB,CAAChE,SAAS,EAAE9B,QAAQ,CAAC;QAE/D,IAAI6G,cAAc,GAAGd,qBAAqB,CAACa,cAAc,EAAEjI,KAAK,EAAEG,IAAI,EAAEC,kBAAkB,EAAE4H,mBAAmB,CAAC;QAEhHlG,QAAQ,GAAGN,cAAc,CAACyG,cAAc,EAAEC,cAAc,EAAElI,KAAK,EAAEG,IAAI,EAAEC,kBAAkB,EAAE4H,mBAAmB,CAAC;QAC/GN,OAAO,GAAG3D,aAAa,CAACkE,cAAc,EAAEnG,QAAQ,EAAE3B,IAAI,EAAEC,kBAAkB,CAAC;QAC3EuH,cAAc,GAAG7F,QAAQ,KAAK9B,KAAK,IAAI0H,OAAO,KAAKvH,IAAI;QACvD,IAAI,CAACM,aAAa,GAAG0C,SAAS;MAChC;MAEA,OAAO;QACLnD,KAAK,EAAE8B,QAAQ;QACf3B,IAAI,EAAEuH,OAAO;QACbC,cAAc,EAAEA;MAClB,CAAC;IACH;EACF,CAAC,EAAE;IACD7L,GAAG,EAAE,gBAAgB;IACrBkB,KAAK,EAAE,SAASmL,cAAcA,CAACpB,KAAK,EAAE;MACpC,IAAIqB,MAAM,GAAG,IAAI;MAEjB,IAAIC,qBAAqB,GAAG,IAAI,CAACvB,sBAAsB,CAACC,KAAK,CAAC;QAC1D/G,KAAK,GAAGqI,qBAAqB,CAACrI,KAAK;QACnCG,IAAI,GAAGkI,qBAAqB,CAAClI,IAAI;QACjCwH,cAAc,GAAGU,qBAAqB,CAACV,cAAc;MAEzD,IAAIA,cAAc,EAAE;QAClB,IAAIW,QAAQ,GAAG;UACbtI,KAAK,EAAEA,KAAK;UACZG,IAAI,EAAEA;QACR,CAAC;QACD,IAAI,CAACmG,kBAAkB,CAACgC,QAAQ,CAAC;QAEjC,IAAI,IAAI,CAACjN,KAAK,CAACkJ,IAAI,EAAE;UACnB,IAAI,CAAClJ,KAAK,CAACmJ,UAAU,IAAI,IAAI,CAACnJ,KAAK,CAACmJ,UAAU,CAAC8D,QAAQ,CAAC;QAC1D;QAEA,IAAI,CAAChE,QAAQ,CAACgE,QAAQ,EAAE,YAAY;UAClCF,MAAM,CAAC/M,KAAK,CAACkN,mBAAmB,IAAIH,MAAM,CAAC/M,KAAK,CAACkN,mBAAmB,CAACD,QAAQ,CAAC;QAChF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDxM,GAAG,EAAE,UAAU;IACfkB,KAAK,EAAE,SAASuD,QAAQA,CAACwG,KAAK,EAAE;MAC9B,IAAIyB,OAAO,GAAG,IAAI;MAElB,IAAI,CAACnN,KAAK,CAACkF,QAAQ,IAAI,IAAI,CAAClF,KAAK,CAACkF,QAAQ,CAACwG,KAAK,CAAC;MAEjD,IAAI,IAAI,CAAC1L,KAAK,CAACoN,KAAK,EAAE;QACpB,IAAI,IAAI,CAACC,aAAa,EAAE;UACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;QAClC;QAEA,IAAI,CAAC,IAAI,CAAC3I,KAAK,CAACO,OAAO,IAAI,IAAI,CAACjF,KAAK,CAAC8I,UAAU,EAAE;UAChD,IAAIyE,sBAAsB,GAAG,IAAI,CAAC9B,sBAAsB,CAACC,KAAK,CAAC;YAC3D8B,OAAO,GAAGD,sBAAsB,CAACjB,cAAc;UAEnDkB,OAAO,IAAI,IAAI,CAACvE,QAAQ,CAAC;YACvBhE,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QAEA,IAAI,CAACoI,aAAa,GAAGI,UAAU,CAAC,YAAY;UAC1CN,OAAO,CAACL,cAAc,CAACpB,KAAK,CAAC;UAE7B,IAAIyB,OAAO,CAACzI,KAAK,CAACO,OAAO,IAAIkI,OAAO,CAACnN,KAAK,CAAC8I,UAAU,IAAI,CAACqE,OAAO,CAACnN,KAAK,CAACkJ,IAAI,EAAE;YAC5EiE,OAAO,CAAClE,QAAQ,CAAC;cACfhE,OAAO,EAAE;YACX,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,IAAI,CAACjF,KAAK,CAACoN,KAAK,CAAC;MACtB,CAAC,MAAM;QACL,IAAI,CAACN,cAAc,CAACpB,KAAK,CAAC;MAC5B;IACF;EACF,CAAC,EAAE;IACDjL,GAAG,EAAE,YAAY;IACjBkB,KAAK,EAAE,SAAS+L,UAAUA,CAAC/H,KAAK,EAAEgI,KAAK,EAAE;MACvC,OAAO;QACLhI,KAAK,EAAEA,KAAK;QACZgI,KAAK,EAAEA,KAAK;QACZhJ,KAAK,EAAEgB,KAAK,KAAK,CAAC;QAClBb,IAAI,EAAEa,KAAK,KAAKgI,KAAK,GAAG,CAAC;QACzBC,IAAI,EAAEjI,KAAK,GAAG,CAAC,KAAK,CAAC;QACrBkI,GAAG,EAAElI,KAAK,GAAG,CAAC,KAAK,CAAC;QACpB3F,KAAK,EAAE,IAAI,CAACA;MACd,CAAC;IACH;EACF,CAAC,EAAE;IACDS,GAAG,EAAE,MAAM;IACXkB,KAAK,EAAE,SAASmM,IAAIA,CAAA,EAAG;MACrB,IAAI,CAAC3D,OAAO,CAAC,CAAC;MACd,IAAI,CAACnC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC8C,aAAa,CAAC,CAAC;IACtB;EACF,CAAC,EAAE;IACDrK,GAAG,EAAE,mBAAmB;IACxBkB,KAAK,EAAE,SAASoM,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACD,IAAI,CAAC,CAAC;IACb;EACF,CAAC,EAAE;IACDrN,GAAG,EAAE,oBAAoB;IACzBkB,KAAK,EAAE,SAASqM,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MACvD,IAAID,SAAS,CAACjI,QAAQ,KAAK,IAAI,CAAChG,KAAK,CAACgG,QAAQ,IAAI,CAACiI,SAAS,CAAC5E,KAAK,IAAI4E,SAAS,CAAC5E,KAAK,CAACnJ,MAAM,KAAK,CAAC,IAAI,CAACF,KAAK,CAACqJ,KAAK,IAAI,EAAE,EAAEnJ,MAAM,EAAE;QAChI,IAAI,CAAC4N,IAAI,CAAC,CAAC;MACb;MAEA,IAAI,IAAI,CAAC9N,KAAK,CAACkJ,IAAI,IAAI+E,SAAS,CAAChJ,OAAO,KAAK,IAAI,CAACjF,KAAK,CAACiF,OAAO,IAAI,IAAI,CAACP,KAAK,CAACO,OAAO,KAAK,IAAI,CAACjF,KAAK,CAACiF,OAAO,EAAE;QAC5G,IAAI,CAACgE,QAAQ,CAAC;UACZhE,OAAO,EAAE,IAAI,CAACjF,KAAK,CAACiF;QACtB,CAAC,CAAC;MACJ;MAEA,IAAIgJ,SAAS,CAAClG,WAAW,KAAK,IAAI,CAAC/H,KAAK,CAAC+H,WAAW,EAAE;QACpD,IAAI,CAAC3C,aAAa,GAAG,IAAI,CAACX,MAAM,CAAC,CAAC,GAAG;UACnCY,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE;QACR,CAAC,GAAG,CAAC;MACP;IACF;EACF,CAAC,EAAE;IACD7E,GAAG,EAAE,YAAY;IACjBkB,KAAK,EAAE,SAASwM,UAAUA,CAACC,IAAI,EAAEzI,KAAK,EAAEgI,KAAK,EAAEU,UAAU,EAAE;MACzD,IAAI7I,OAAO,GAAG,IAAI,CAACkI,UAAU,CAAC/H,KAAK,EAAEgI,KAAK,CAAC;MAC3C,IAAIpE,OAAO,GAAG/J,WAAW,CAAC8O,aAAa,CAAC,IAAI,CAACtO,KAAK,CAACuO,YAAY,EAAEF,UAAU,IAAID,IAAI,EAAE5I,OAAO,CAAC;MAC7F,OAAO,aAAalG,KAAK,CAACkP,aAAa,CAAClP,KAAK,CAACmP,QAAQ,EAAE;QACtDhO,GAAG,EAAEkF;MACP,CAAC,EAAE4D,OAAO,CAAC;IACb;EACF,CAAC,EAAE;IACD9I,GAAG,EAAE,aAAa;IAClBkB,KAAK,EAAE,SAAS+M,WAAWA,CAAA,EAAG;MAC5B,IAAIC,OAAO,GAAG,IAAI;MAElB,IAAItF,KAAK,GAAG,IAAI,CAACrJ,KAAK,CAACqJ,KAAK;MAE5B,IAAIA,KAAK,IAAI,CAAC,IAAI,CAAC3E,KAAK,CAACO,OAAO,EAAE;QAChC,IAAIR,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;QAC1B,IAAImK,YAAY,GAAG,IAAI,CAAClK,KAAK;UACzBC,KAAK,GAAGiK,YAAY,CAACjK,KAAK;UAC1BG,IAAI,GAAG8J,YAAY,CAAC9J,IAAI;QAC5B,IAAI6I,KAAK,GAAGtE,KAAK,CAACnJ,MAAM;QAExB,IAAIuE,MAAM,EAAE;UACV,OAAO4E,KAAK,CAACwF,KAAK,CAAClK,KAAK,CAACC,IAAI,EAAEE,IAAI,CAACF,IAAI,CAAC,CAACkK,GAAG,CAAC,UAAUV,IAAI,EAAEnO,CAAC,EAAE;YAC/D,IAAIoJ,KAAK,GAAG+E,IAAI,CAACS,KAAK,CAAClK,KAAK,CAACE,IAAI,EAAEC,IAAI,CAACD,IAAI,CAAC;YAC7C,IAAIc,KAAK,GAAGhB,KAAK,CAACC,IAAI,GAAG3E,CAAC;YAC1B,OAAO0O,OAAO,CAACR,UAAU,CAACC,IAAI,EAAEzI,KAAK,EAAEgI,KAAK,EAAEtE,KAAK,CAAC;UACtD,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,OAAOA,KAAK,CAACwF,KAAK,CAAClK,KAAK,EAAEG,IAAI,CAAC,CAACgK,GAAG,CAAC,UAAUV,IAAI,EAAEnO,CAAC,EAAE;YACrD,IAAI0F,KAAK,GAAGhB,KAAK,GAAG1E,CAAC;YACrB,OAAO0O,OAAO,CAACR,UAAU,CAACC,IAAI,EAAEzI,KAAK,EAAEgI,KAAK,CAAC;UAC/C,CAAC,CAAC;QACJ;MACF;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDlN,GAAG,EAAE,kBAAkB;IACvBkB,KAAK,EAAE,SAASoN,gBAAgBA,CAACpJ,KAAK,EAAEgI,KAAK,EAAEqB,UAAU,EAAE;MACzD,IAAIxJ,OAAO,GAAGxC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0K,UAAU,CAAC/H,KAAK,EAAEgI,KAAK,CAAC,CAAC,EAAEqB,UAAU,IAAI,CAAC,CAAC,CAAC;MAE/F,IAAIzF,OAAO,GAAG/J,WAAW,CAAC8O,aAAa,CAAC,IAAI,CAACtO,KAAK,CAACiP,eAAe,EAAEzJ,OAAO,CAAC;MAC5E,OAAO,aAAalG,KAAK,CAACkP,aAAa,CAAClP,KAAK,CAACmP,QAAQ,EAAE;QACtDhO,GAAG,EAAEkF;MACP,CAAC,EAAE4D,OAAO,CAAC;IACb;EACF,CAAC,EAAE;IACD9I,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAASuN,YAAYA,CAAA,EAAG;MAC7B,IAAIC,OAAO,GAAG,IAAI;MAElB,IAAI,IAAI,CAACzK,KAAK,CAACO,OAAO,EAAE;QACtB,IAAImK,SAAS,GAAG3P,UAAU,CAAC,0BAA0B,EAAE;UACrD,qBAAqB,EAAE,CAAC,IAAI,CAACO,KAAK,CAACiP;QACrC,CAAC,CAAC;QACF,IAAI1F,OAAO,GAAG,aAAajK,KAAK,CAACkP,aAAa,CAAC,GAAG,EAAE;UAClDY,SAAS,EAAE;QACb,CAAC,CAAC;QAEF,IAAI,IAAI,CAACpP,KAAK,CAACiP,eAAe,EAAE;UAC9B,IAAIxK,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;UAC1B,IAAIM,kBAAkB,GAAG,IAAI,CAACL,KAAK,CAACK,kBAAkB;UACtD,IAAI7E,MAAM,GAAGuE,MAAM,GAAGM,kBAAkB,CAACH,IAAI,GAAGG,kBAAkB;UAClEwE,OAAO,GAAG,IAAI,CAAC7E,KAAK,CAAC2K,SAAS,CAACP,GAAG,CAAC,UAAUQ,CAAC,EAAE3J,KAAK,EAAE;YACrD,OAAOwJ,OAAO,CAACJ,gBAAgB,CAACpJ,KAAK,EAAEzF,MAAM,EAAEuE,MAAM,IAAI;cACvD8K,OAAO,EAAExK,kBAAkB,CAACF;YAC9B,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;QAEA,OAAO,aAAavF,KAAK,CAACkP,aAAa,CAAC,KAAK,EAAE;UAC7CY,SAAS,EAAEA;QACb,CAAC,EAAE7F,OAAO,CAAC;MACb;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD9I,GAAG,EAAE,eAAe;IACpBkB,KAAK,EAAE,SAAS6N,aAAaA,CAAA,EAAG;MAC9B,IAAIC,OAAO,GAAG,IAAI;MAElB,IAAIpG,KAAK,GAAG,IAAI,CAACqF,WAAW,CAAC,CAAC;MAC9B,IAAInF,OAAO,GAAG,aAAajK,KAAK,CAACkP,aAAa,CAAC,KAAK,EAAE;QACpDY,SAAS,EAAE,2BAA2B;QACtCM,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOF,OAAO,CAAClG,OAAO,GAAGoG,EAAE;QAC7B;MACF,CAAC,EAAEtG,KAAK,CAAC;MAET,IAAI,IAAI,CAACrJ,KAAK,CAAC4P,eAAe,EAAE;QAC9B,IAAIC,YAAY,GAAG,IAAI,CAACnL,KAAK;UACzBO,OAAO,GAAG4K,YAAY,CAAC5K,OAAO;UAC9BN,KAAK,GAAGkL,YAAY,CAAClL,KAAK;UAC1BG,IAAI,GAAG+K,YAAY,CAAC/K,IAAI;QAC5B,IAAIgL,cAAc,GAAG;UACnBV,SAAS,EAAE,2BAA2B;UACtCM,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;YACpB,OAAOF,OAAO,CAAClG,OAAO,GAAGoG,EAAE;UAC7B,CAAC;UACDI,QAAQ,EAAE1G,KAAK;UACf5D,OAAO,EAAE8D,OAAO;UAChBvJ,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBiF,OAAO,EAAEA,OAAO;UAChBN,KAAK,EAAEA,KAAK;UACZG,IAAI,EAAEA;QACR,CAAC;QACD,OAAOtF,WAAW,CAAC8O,aAAa,CAAC,IAAI,CAACtO,KAAK,CAAC4P,eAAe,EAAEE,cAAc,CAAC;MAC9E;MAEA,OAAOvG,OAAO;IAChB;EACF,CAAC,EAAE;IACD9I,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAASqO,MAAMA,CAAA,EAAG;MACvB,IAAIC,OAAO,GAAG,IAAI;MAElB,IAAIxL,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;MAC1B,IAAIsB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;MACtC,IAAIqJ,SAAS,GAAG3P,UAAU,CAAC,mBAAmB,EAAE;QAC9C,eAAe,EAAEgF,MAAM;QACvB,qBAAqB,EAAEsB;MACzB,CAAC,EAAE,IAAI,CAAC/F,KAAK,CAACoP,SAAS,CAAC;MACxB,IAAIc,MAAM,GAAG,IAAI,CAAChB,YAAY,CAAC,CAAC;MAChC,IAAI3F,OAAO,GAAG,IAAI,CAACiG,aAAa,CAAC,CAAC;MAClC,OAAO,aAAalQ,KAAK,CAACkP,aAAa,CAAC,KAAK,EAAE;QAC7CkB,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOM,OAAO,CAACxK,OAAO,GAAGkK,EAAE;QAC7B,CAAC;QACDP,SAAS,EAAEA,SAAS;QACpBe,QAAQ,EAAE,CAAC;QACX3G,KAAK,EAAE,IAAI,CAACxJ,KAAK,CAACwJ,KAAK;QACvBtE,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAEqE,OAAO,EAAE,aAAajK,KAAK,CAACkP,aAAa,CAAC,KAAK,EAAE;QAClDkB,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOM,OAAO,CAACjF,MAAM,GAAG2E,EAAE;QAC5B,CAAC;QACDP,SAAS,EAAE;MACb,CAAC,CAAC,EAAEc,MAAM,CAAC;IACb;EACF,CAAC,CAAC,CAAC;EAEH,OAAO7L,eAAe;AACxB,CAAC,CAAC9E,SAAS,CAAC;AAEZ6C,eAAe,CAACiC,eAAe,EAAE,cAAc,EAAE;EAC/C+L,EAAE,EAAE,IAAI;EACR5G,KAAK,EAAE,IAAI;EACX4F,SAAS,EAAE,IAAI;EACf/F,KAAK,EAAE,IAAI;EACXrD,QAAQ,EAAE,CAAC;EACX0E,YAAY,EAAE,IAAI;EAClBH,WAAW,EAAE,IAAI;EACjBxC,WAAW,EAAE,UAAU;EACvB/C,iBAAiB,EAAE,IAAI;EACvBoI,KAAK,EAAE,CAAC;EACRlE,IAAI,EAAE,KAAK;EACXJ,UAAU,EAAE,KAAK;EACjBmG,eAAe,EAAE,IAAI;EACrBV,YAAY,EAAE,IAAI;EAClBqB,eAAe,EAAE,IAAI;EACrB1K,QAAQ,EAAE,IAAI;EACdgI,mBAAmB,EAAE,IAAI;EACzB/D,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,SAAS9E,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}