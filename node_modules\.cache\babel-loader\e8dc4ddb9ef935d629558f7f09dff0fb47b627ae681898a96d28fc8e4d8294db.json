{"ast": null, "code": "/**\n * Easy to set element style, return previous style\n * IE browser compatible(IE browser doesn't merge overflow style, need to set it separately)\n * https://github.com/ant-design/ant-design/issues/19393\n *\n */\nfunction setStyle(style) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!style) {\n    return {};\n  }\n  var _options$element = options.element,\n    element = _options$element === void 0 ? document.body : _options$element;\n  var oldStyle = {};\n  var styleKeys = Object.keys(style); // IE browser compatible\n\n  styleKeys.forEach(function (key) {\n    oldStyle[key] = element.style[key];\n  });\n  styleKeys.forEach(function (key) {\n    element.style[key] = style[key];\n  });\n  return oldStyle;\n}\nexport default setStyle;", "map": {"version": 3, "names": ["setStyle", "style", "options", "arguments", "length", "undefined", "_options$element", "element", "document", "body", "oldStyle", "styleKeys", "Object", "keys", "for<PERSON>ach", "key"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/setStyle.js"], "sourcesContent": ["/**\n * Easy to set element style, return previous style\n * IE browser compatible(IE browser doesn't merge overflow style, need to set it separately)\n * https://github.com/ant-design/ant-design/issues/19393\n *\n */\nfunction setStyle(style) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (!style) {\n    return {};\n  }\n\n  var _options$element = options.element,\n      element = _options$element === void 0 ? document.body : _options$element;\n  var oldStyle = {};\n  var styleKeys = Object.keys(style); // IE browser compatible\n\n  styleKeys.forEach(function (key) {\n    oldStyle[key] = element.style[key];\n  });\n  styleKeys.forEach(function (key) {\n    element.style[key] = style[key];\n  });\n  return oldStyle;\n}\n\nexport default setStyle;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpF,IAAI,CAACF,KAAK,EAAE;IACV,OAAO,CAAC,CAAC;EACX;EAEA,IAAIK,gBAAgB,GAAGJ,OAAO,CAACK,OAAO;IAClCA,OAAO,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAGE,QAAQ,CAACC,IAAI,GAAGH,gBAAgB;EAC5E,IAAII,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACZ,KAAK,CAAC,CAAC,CAAC;;EAEpCU,SAAS,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC/BL,QAAQ,CAACK,GAAG,CAAC,GAAGR,OAAO,CAACN,KAAK,CAACc,GAAG,CAAC;EACpC,CAAC,CAAC;EACFJ,SAAS,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC/BR,OAAO,CAACN,KAAK,CAACc,GAAG,CAAC,GAAGd,KAAK,CAACc,GAAG,CAAC;EACjC,CAAC,CAAC;EACF,OAAOL,QAAQ;AACjB;AAEA,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}