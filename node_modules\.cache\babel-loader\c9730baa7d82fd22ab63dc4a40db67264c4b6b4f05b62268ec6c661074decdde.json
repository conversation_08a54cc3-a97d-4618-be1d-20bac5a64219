{"ast": null, "code": "import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\nimport { compareNumbers, compareValues, getIterables, includesOrEqualsTo, nested } from './helpers';\nexport default function treeChanges(previousData, data) {\n  if ([previousData, data].some(is.nullOrUndefined)) {\n    throw new Error('Missing required parameters');\n  }\n  if (![previousData, data].every(function (d) {\n    return is.plainObject(d) || is.array(d);\n  })) {\n    throw new Error('Expected plain objects or array');\n  }\n  var added = function (key, value) {\n    try {\n      return compareValues(previousData, data, {\n        key: key,\n        type: 'added',\n        value: value\n      });\n    } catch (_a) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  var changed = function (key, actual, previous) {\n    try {\n      var left = nested(previousData, key);\n      var right = nested(data, key);\n      var hasActual = is.defined(actual);\n      var hasPrevious = is.defined(previous);\n      if (hasActual || hasPrevious) {\n        var leftComparator = hasPrevious ? includesOrEqualsTo(previous, left) : !includesOrEqualsTo(actual, left);\n        var rightComparator = includesOrEqualsTo(actual, right);\n        return leftComparator && rightComparator;\n      }\n      if ([left, right].every(is.array) || [left, right].every(is.plainObject)) {\n        return !equal(left, right);\n      }\n      return left !== right;\n    } catch (_a) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  var changedFrom = function (key, previous, actual) {\n    if (!is.defined(key)) {\n      return false;\n    }\n    try {\n      var left = nested(previousData, key);\n      var right = nested(data, key);\n      var hasActual = is.defined(actual);\n      return includesOrEqualsTo(previous, left) && (hasActual ? includesOrEqualsTo(actual, right) : !hasActual);\n    } catch (_a) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  /**\n   * @deprecated\n   * Use \"changed\" instead\n   */\n  var changedTo = function (key, actual) {\n    if (!is.defined(key)) {\n      return false;\n    }\n    /* istanbul ignore next */\n    if (process.env.NODE_ENV === 'development') {\n      // eslint-disable-next-line no-console\n      console.warn('`changedTo` is deprecated! Replace it with `change`');\n    }\n    return changed(key, actual);\n  };\n  var decreased = function (key, actual, previous) {\n    if (!is.defined(key)) {\n      return false;\n    }\n    try {\n      return compareNumbers(previousData, data, {\n        key: key,\n        actual: actual,\n        previous: previous,\n        type: 'decreased'\n      });\n    } catch (_a) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  var emptied = function (key) {\n    try {\n      var _a = getIterables(previousData, data, {\n          key: key\n        }),\n        left = _a[0],\n        right = _a[1];\n      return !!left.length && !right.length;\n    } catch (_b) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  var filled = function (key) {\n    try {\n      var _a = getIterables(previousData, data, {\n          key: key\n        }),\n        left = _a[0],\n        right = _a[1];\n      return !left.length && !!right.length;\n    } catch (_b) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  var increased = function (key, actual, previous) {\n    if (!is.defined(key)) {\n      return false;\n    }\n    try {\n      return compareNumbers(previousData, data, {\n        key: key,\n        actual: actual,\n        previous: previous,\n        type: 'increased'\n      });\n    } catch (_a) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  var removed = function (key, value) {\n    try {\n      return compareValues(previousData, data, {\n        key: key,\n        type: 'removed',\n        value: value\n      });\n    } catch (_a) {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n  return {\n    added: added,\n    changed: changed,\n    changedFrom: changedFrom,\n    changedTo: changedTo,\n    decreased: decreased,\n    emptied: emptied,\n    filled: filled,\n    increased: increased,\n    removed: removed\n  };\n}\nexport * from './types';", "map": {"version": 3, "names": ["equal", "is", "compareNumbers", "compareValues", "getIterables", "includesOrEqualsTo", "nested", "treeChanges", "previousData", "data", "some", "nullOrUndefined", "Error", "every", "d", "plainObject", "array", "added", "key", "value", "type", "_a", "changed", "actual", "previous", "left", "right", "hasActual", "defined", "has<PERSON>revious", "leftComparator", "rightComparator", "changedFrom", "changedTo", "process", "env", "NODE_ENV", "console", "warn", "decreased", "emptied", "length", "_b", "filled", "increased", "removed"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\tree-changes\\src\\index.ts"], "sourcesContent": ["import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\n\nimport { compareNumbers, compareValues, getIterables, includesOrEqualsTo, nested } from './helpers';\nimport { Data, KeyType, TreeChanges, Value } from './types';\n\nexport default function treeChanges<P extends Data, D extends Data, K = KeyType<P, D>>(\n  previousData: P,\n  data: D,\n): TreeChanges<K> {\n  if ([previousData, data].some(is.nullOrUndefined)) {\n    throw new Error('Missing required parameters');\n  }\n\n  if (![previousData, data].every(d => is.plainObject(d) || is.array(d))) {\n    throw new Error('Expected plain objects or array');\n  }\n\n  const added = (key?: K, value?: Value): boolean => {\n    try {\n      return compareValues<K>(previousData, data, { key, type: 'added', value });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const changed = (key?: K | string, actual?: Value, previous?: Value): boolean => {\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is.defined(actual);\n      const hasPrevious = is.defined(previous);\n\n      if (hasActual || hasPrevious) {\n        const leftComparator = hasPrevious\n          ? includesOrEqualsTo(previous, left)\n          : !includesOrEqualsTo(actual, left);\n        const rightComparator = includesOrEqualsTo(actual, right);\n\n        return leftComparator && rightComparator;\n      }\n\n      if ([left, right].every(is.array) || [left, right].every(is.plainObject)) {\n        return !equal(left, right);\n      }\n\n      return left !== right;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const changedFrom = (key: K | string, previous: Value, actual?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is.defined(actual);\n\n      return (\n        includesOrEqualsTo(previous, left) &&\n        (hasActual ? includesOrEqualsTo(actual, right) : !hasActual)\n      );\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  /**\n   * @deprecated\n   * Use \"changed\" instead\n   */\n  const changedTo = (key: K | string, actual: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    /* istanbul ignore next */\n    if (process.env.NODE_ENV === 'development') {\n      // eslint-disable-next-line no-console\n      console.warn('`changedTo` is deprecated! Replace it with `change`');\n    }\n\n    return changed(key, actual);\n  };\n\n  const decreased = (key: K, actual?: Value, previous?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      return compareNumbers<K>(previousData, data, { key, actual, previous, type: 'decreased' });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const emptied = (key?: K): boolean => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n\n      return !!left.length && !right.length;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const filled = (key?: K): boolean => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n\n      return !left.length && !!right.length;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const increased = (key: K, actual?: Value, previous?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      return compareNumbers<K>(previousData, data, { key, actual, previous, type: 'increased' });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const removed = (key?: K, value?: Value): boolean => {\n    try {\n      return compareValues<K>(previousData, data, { key, type: 'removed', value });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  return { added, changed, changedFrom, changedTo, decreased, emptied, filled, increased, removed };\n}\n\nexport * from './types';\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,EAAE,MAAM,SAAS;AAExB,SAASC,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,MAAM,QAAQ,WAAW;AAGnG,eAAc,SAAUC,WAAWA,CACjCC,YAAe,EACfC,IAAO;EAEP,IAAI,CAACD,YAAY,EAAEC,IAAI,CAAC,CAACC,IAAI,CAACT,EAAE,CAACU,eAAe,CAAC,EAAE;IACjD,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;;EAGhD,IAAI,CAAC,CAACJ,YAAY,EAAEC,IAAI,CAAC,CAACI,KAAK,CAAC,UAAAC,CAAC;IAAI,OAAAb,EAAE,CAACc,WAAW,CAACD,CAAC,CAAC,IAAIb,EAAE,CAACe,KAAK,CAACF,CAAC,CAAC;EAAhC,CAAgC,CAAC,EAAE;IACtE,MAAM,IAAIF,KAAK,CAAC,iCAAiC,CAAC;;EAGpD,IAAMK,KAAK,GAAG,SAAAA,CAACC,GAAO,EAAEC,KAAa;IACnC,IAAI;MACF,OAAOhB,aAAa,CAAIK,YAAY,EAAEC,IAAI,EAAE;QAAES,GAAG,EAAAA,GAAA;QAAEE,IAAI,EAAE,OAAO;QAAED,KAAK,EAAAA;MAAA,CAAE,CAAC;KAC3E,CAAC,OAAAE,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,IAAMC,OAAO,GAAG,SAAAA,CAACJ,GAAgB,EAAEK,MAAc,EAAEC,QAAgB;IACjE,IAAI;MACF,IAAMC,IAAI,GAAGnB,MAAM,CAACE,YAAY,EAAEU,GAAG,CAAC;MACtC,IAAMQ,KAAK,GAAGpB,MAAM,CAACG,IAAI,EAAES,GAAG,CAAC;MAC/B,IAAMS,SAAS,GAAG1B,EAAE,CAAC2B,OAAO,CAACL,MAAM,CAAC;MACpC,IAAMM,WAAW,GAAG5B,EAAE,CAAC2B,OAAO,CAACJ,QAAQ,CAAC;MAExC,IAAIG,SAAS,IAAIE,WAAW,EAAE;QAC5B,IAAMC,cAAc,GAAGD,WAAW,GAC9BxB,kBAAkB,CAACmB,QAAQ,EAAEC,IAAI,CAAC,GAClC,CAACpB,kBAAkB,CAACkB,MAAM,EAAEE,IAAI,CAAC;QACrC,IAAMM,eAAe,GAAG1B,kBAAkB,CAACkB,MAAM,EAAEG,KAAK,CAAC;QAEzD,OAAOI,cAAc,IAAIC,eAAe;;MAG1C,IAAI,CAACN,IAAI,EAAEC,KAAK,CAAC,CAACb,KAAK,CAACZ,EAAE,CAACe,KAAK,CAAC,IAAI,CAACS,IAAI,EAAEC,KAAK,CAAC,CAACb,KAAK,CAACZ,EAAE,CAACc,WAAW,CAAC,EAAE;QACxE,OAAO,CAACf,KAAK,CAACyB,IAAI,EAAEC,KAAK,CAAC;;MAG5B,OAAOD,IAAI,KAAKC,KAAK;KACtB,CAAC,OAAAL,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,IAAMW,WAAW,GAAG,SAAAA,CAACd,GAAe,EAAEM,QAAe,EAAED,MAAc;IACnE,IAAI,CAACtB,EAAE,CAAC2B,OAAO,CAACV,GAAG,CAAC,EAAE;MACpB,OAAO,KAAK;;IAGd,IAAI;MACF,IAAMO,IAAI,GAAGnB,MAAM,CAACE,YAAY,EAAEU,GAAG,CAAC;MACtC,IAAMQ,KAAK,GAAGpB,MAAM,CAACG,IAAI,EAAES,GAAG,CAAC;MAC/B,IAAMS,SAAS,GAAG1B,EAAE,CAAC2B,OAAO,CAACL,MAAM,CAAC;MAEpC,OACElB,kBAAkB,CAACmB,QAAQ,EAAEC,IAAI,CAAC,KACjCE,SAAS,GAAGtB,kBAAkB,CAACkB,MAAM,EAAEG,KAAK,CAAC,GAAG,CAACC,SAAS,CAAC;KAE/D,CAAC,OAAAN,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED;;;;EAIA,IAAMY,SAAS,GAAG,SAAAA,CAACf,GAAe,EAAEK,MAAa;IAC/C,IAAI,CAACtB,EAAE,CAAC2B,OAAO,CAACV,GAAG,CAAC,EAAE;MACpB,OAAO,KAAK;;IAGd;IACA,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1C;MACAC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;;IAGrE,OAAOhB,OAAO,CAACJ,GAAG,EAAEK,MAAM,CAAC;EAC7B,CAAC;EAED,IAAMgB,SAAS,GAAG,SAAAA,CAACrB,GAAM,EAAEK,MAAc,EAAEC,QAAgB;IACzD,IAAI,CAACvB,EAAE,CAAC2B,OAAO,CAACV,GAAG,CAAC,EAAE;MACpB,OAAO,KAAK;;IAGd,IAAI;MACF,OAAOhB,cAAc,CAAIM,YAAY,EAAEC,IAAI,EAAE;QAAES,GAAG,EAAAA,GAAA;QAAEK,MAAM,EAAAA,MAAA;QAAEC,QAAQ,EAAAA,QAAA;QAAEJ,IAAI,EAAE;MAAW,CAAE,CAAC;KAC3F,CAAC,OAAAC,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,IAAMmB,OAAO,GAAG,SAAAA,CAACtB,GAAO;IACtB,IAAI;MACI,IAAAG,EAAA,GAAgBjB,YAAY,CAACI,YAAY,EAAEC,IAAI,EAAE;UAAES,GAAG,EAAAA;QAAA,CAAE,CAAC;QAAxDO,IAAI,GAAAJ,EAAA;QAAEK,KAAK,GAAAL,EAAA,GAA6C;MAE/D,OAAO,CAAC,CAACI,IAAI,CAACgB,MAAM,IAAI,CAACf,KAAK,CAACe,MAAM;KACtC,CAAC,OAAAC,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,IAAMC,MAAM,GAAG,SAAAA,CAACzB,GAAO;IACrB,IAAI;MACI,IAAAG,EAAA,GAAgBjB,YAAY,CAACI,YAAY,EAAEC,IAAI,EAAE;UAAES,GAAG,EAAAA;QAAA,CAAE,CAAC;QAAxDO,IAAI,GAAAJ,EAAA;QAAEK,KAAK,GAAAL,EAAA,GAA6C;MAE/D,OAAO,CAACI,IAAI,CAACgB,MAAM,IAAI,CAAC,CAACf,KAAK,CAACe,MAAM;KACtC,CAAC,OAAAC,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,IAAME,SAAS,GAAG,SAAAA,CAAC1B,GAAM,EAAEK,MAAc,EAAEC,QAAgB;IACzD,IAAI,CAACvB,EAAE,CAAC2B,OAAO,CAACV,GAAG,CAAC,EAAE;MACpB,OAAO,KAAK;;IAGd,IAAI;MACF,OAAOhB,cAAc,CAAIM,YAAY,EAAEC,IAAI,EAAE;QAAES,GAAG,EAAAA,GAAA;QAAEK,MAAM,EAAAA,MAAA;QAAEC,QAAQ,EAAAA,QAAA;QAAEJ,IAAI,EAAE;MAAW,CAAE,CAAC;KAC3F,CAAC,OAAAC,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,IAAMwB,OAAO,GAAG,SAAAA,CAAC3B,GAAO,EAAEC,KAAa;IACrC,IAAI;MACF,OAAOhB,aAAa,CAAIK,YAAY,EAAEC,IAAI,EAAE;QAAES,GAAG,EAAAA,GAAA;QAAEE,IAAI,EAAE,SAAS;QAAED,KAAK,EAAAA;MAAA,CAAE,CAAC;KAC7E,CAAC,OAAAE,EAAA,EAAM;MACN;MACA,OAAO,KAAK;;EAEhB,CAAC;EAED,OAAO;IAAEJ,KAAK,EAAAA,KAAA;IAAEK,OAAO,EAAAA,OAAA;IAAEU,WAAW,EAAAA,WAAA;IAAEC,SAAS,EAAAA,SAAA;IAAEM,SAAS,EAAAA,SAAA;IAAEC,OAAO,EAAAA,OAAA;IAAEG,MAAM,EAAAA,MAAA;IAAEC,SAAS,EAAAA,SAAA;IAAEC,OAAO,EAAAA;EAAA,CAAE;AACnG;AAEA,cAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}