{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nvar MIN_SIZE = 20;\nfunction getPageY(e) {\n  return 'touches' in e ? e.touches[0].pageY : e.pageY;\n}\nvar ScrollBar = /*#__PURE__*/function (_React$Component) {\n  _inherits(ScrollBar, _React$Component);\n  var _super = _createSuper(ScrollBar);\n  function ScrollBar() {\n    var _this;\n    _classCallCheck(this, ScrollBar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.moveRaf = null;\n    _this.scrollbarRef = /*#__PURE__*/React.createRef();\n    _this.thumbRef = /*#__PURE__*/React.createRef();\n    _this.visibleTimeout = null;\n    _this.state = {\n      dragging: false,\n      pageY: null,\n      startTop: null,\n      visible: false\n    };\n    _this.delayHidden = function () {\n      clearTimeout(_this.visibleTimeout);\n      _this.setState({\n        visible: true\n      });\n      _this.visibleTimeout = setTimeout(function () {\n        _this.setState({\n          visible: false\n        });\n      }, 2000);\n    };\n    _this.onScrollbarTouchStart = function (e) {\n      e.preventDefault();\n    };\n    _this.onContainerMouseDown = function (e) {\n      e.stopPropagation();\n      e.preventDefault();\n    };\n    _this.patchEvents = function () {\n      window.addEventListener('mousemove', _this.onMouseMove);\n      window.addEventListener('mouseup', _this.onMouseUp);\n      _this.thumbRef.current.addEventListener('touchmove', _this.onMouseMove);\n      _this.thumbRef.current.addEventListener('touchend', _this.onMouseUp);\n    };\n    _this.removeEvents = function () {\n      var _this$scrollbarRef$cu;\n      window.removeEventListener('mousemove', _this.onMouseMove);\n      window.removeEventListener('mouseup', _this.onMouseUp);\n      (_this$scrollbarRef$cu = _this.scrollbarRef.current) === null || _this$scrollbarRef$cu === void 0 ? void 0 : _this$scrollbarRef$cu.removeEventListener('touchstart', _this.onScrollbarTouchStart);\n      if (_this.thumbRef.current) {\n        _this.thumbRef.current.removeEventListener('touchstart', _this.onMouseDown);\n        _this.thumbRef.current.removeEventListener('touchmove', _this.onMouseMove);\n        _this.thumbRef.current.removeEventListener('touchend', _this.onMouseUp);\n      }\n      raf.cancel(_this.moveRaf);\n    };\n    _this.onMouseDown = function (e) {\n      var onStartMove = _this.props.onStartMove;\n      _this.setState({\n        dragging: true,\n        pageY: getPageY(e),\n        startTop: _this.getTop()\n      });\n      onStartMove();\n      _this.patchEvents();\n      e.stopPropagation();\n      e.preventDefault();\n    };\n    _this.onMouseMove = function (e) {\n      var _this$state = _this.state,\n        dragging = _this$state.dragging,\n        pageY = _this$state.pageY,\n        startTop = _this$state.startTop;\n      var onScroll = _this.props.onScroll;\n      raf.cancel(_this.moveRaf);\n      if (dragging) {\n        var offsetY = getPageY(e) - pageY;\n        var newTop = startTop + offsetY;\n        var enableScrollRange = _this.getEnableScrollRange();\n        var enableHeightRange = _this.getEnableHeightRange();\n        var ptg = enableHeightRange ? newTop / enableHeightRange : 0;\n        var newScrollTop = Math.ceil(ptg * enableScrollRange);\n        _this.moveRaf = raf(function () {\n          onScroll(newScrollTop);\n        });\n      }\n    };\n    _this.onMouseUp = function () {\n      var onStopMove = _this.props.onStopMove;\n      _this.setState({\n        dragging: false\n      });\n      onStopMove();\n      _this.removeEvents();\n    };\n    _this.getSpinHeight = function () {\n      var _this$props = _this.props,\n        height = _this$props.height,\n        count = _this$props.count;\n      var baseHeight = height / count * 10;\n      baseHeight = Math.max(baseHeight, MIN_SIZE);\n      baseHeight = Math.min(baseHeight, height / 2);\n      return Math.floor(baseHeight);\n    };\n    _this.getEnableScrollRange = function () {\n      var _this$props2 = _this.props,\n        scrollHeight = _this$props2.scrollHeight,\n        height = _this$props2.height;\n      return scrollHeight - height || 0;\n    };\n    _this.getEnableHeightRange = function () {\n      var height = _this.props.height;\n      var spinHeight = _this.getSpinHeight();\n      return height - spinHeight || 0;\n    };\n    _this.getTop = function () {\n      var scrollTop = _this.props.scrollTop;\n      var enableScrollRange = _this.getEnableScrollRange();\n      var enableHeightRange = _this.getEnableHeightRange();\n      if (scrollTop === 0 || enableScrollRange === 0) {\n        return 0;\n      }\n      var ptg = scrollTop / enableScrollRange;\n      return ptg * enableHeightRange;\n    };\n    _this.showScroll = function () {\n      var _this$props3 = _this.props,\n        height = _this$props3.height,\n        scrollHeight = _this$props3.scrollHeight;\n      return scrollHeight > height;\n    };\n    return _this;\n  }\n  _createClass(ScrollBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.scrollbarRef.current.addEventListener('touchstart', this.onScrollbarTouchStart);\n      this.thumbRef.current.addEventListener('touchstart', this.onMouseDown);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.scrollTop !== this.props.scrollTop) {\n        this.delayHidden();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.removeEvents();\n      clearTimeout(this.visibleTimeout);\n    }\n  }, {\n    key: \"render\",\n    value:\n    // ====================== Render =======================\n    function render() {\n      var _this$state2 = this.state,\n        dragging = _this$state2.dragging,\n        visible = _this$state2.visible;\n      var prefixCls = this.props.prefixCls;\n      var spinHeight = this.getSpinHeight();\n      var top = this.getTop();\n      var canScroll = this.showScroll();\n      var mergedVisible = canScroll && visible;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.scrollbarRef,\n        className: classNames(\"\".concat(prefixCls, \"-scrollbar\"), _defineProperty({}, \"\".concat(prefixCls, \"-scrollbar-show\"), canScroll)),\n        style: {\n          width: 8,\n          top: 0,\n          bottom: 0,\n          right: 0,\n          position: 'absolute',\n          display: mergedVisible ? null : 'none'\n        },\n        onMouseDown: this.onContainerMouseDown,\n        onMouseMove: this.delayHidden\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.thumbRef,\n        className: classNames(\"\".concat(prefixCls, \"-scrollbar-thumb\"), _defineProperty({}, \"\".concat(prefixCls, \"-scrollbar-thumb-moving\"), dragging)),\n        style: {\n          width: '100%',\n          height: spinHeight,\n          top: top,\n          left: 0,\n          position: 'absolute',\n          background: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 99,\n          cursor: 'pointer',\n          userSelect: 'none'\n        },\n        onMouseDown: this.onMouseDown\n      }));\n    }\n  }]);\n  return ScrollBar;\n}(React.Component);\nexport { ScrollBar as default };", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "arguments", "apply", "_possibleConstructorReturn", "self", "call", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "React", "classNames", "raf", "MIN_SIZE", "getPageY", "touches", "pageY", "<PERSON><PERSON>Bar", "_React$Component", "_super", "_this", "_len", "args", "Array", "_key", "concat", "moveRaf", "scrollbarRef", "createRef", "thumbRef", "visibleTimeout", "state", "dragging", "startTop", "visible", "delayHidden", "clearTimeout", "setState", "setTimeout", "onScrollbarTouchStart", "preventDefault", "onContainerMouseDown", "stopPropagation", "patchEvents", "window", "addEventListener", "onMouseMove", "onMouseUp", "current", "removeEvents", "_this$scrollbarRef$cu", "removeEventListener", "onMouseDown", "cancel", "onStartMove", "getTop", "_this$state", "onScroll", "offsetY", "newTop", "enableScrollRange", "getEnableScrollRange", "enableHeightRange", "getEnableHeightRange", "ptg", "newScrollTop", "Math", "ceil", "onStopMove", "getSpinHeight", "_this$props", "height", "count", "baseHeight", "max", "min", "floor", "_this$props2", "scrollHeight", "spinHeight", "scrollTop", "showScroll", "_this$props3", "componentDidMount", "componentDidUpdate", "prevProps", "componentWillUnmount", "render", "_this$state2", "prefixCls", "top", "canScroll", "mergedVisible", "createElement", "ref", "className", "style", "width", "bottom", "right", "position", "display", "left", "background", "borderRadius", "cursor", "userSelect", "Component", "default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-virtual-list/es/ScrollBar.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nvar MIN_SIZE = 20;\n\nfunction getPageY(e) {\n  return 'touches' in e ? e.touches[0].pageY : e.pageY;\n}\n\nvar ScrollBar = /*#__PURE__*/function (_React$Component) {\n  _inherits(ScrollBar, _React$Component);\n\n  var _super = _createSuper(ScrollBar);\n\n  function ScrollBar() {\n    var _this;\n\n    _classCallCheck(this, ScrollBar);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.moveRaf = null;\n    _this.scrollbarRef = /*#__PURE__*/React.createRef();\n    _this.thumbRef = /*#__PURE__*/React.createRef();\n    _this.visibleTimeout = null;\n    _this.state = {\n      dragging: false,\n      pageY: null,\n      startTop: null,\n      visible: false\n    };\n\n    _this.delayHidden = function () {\n      clearTimeout(_this.visibleTimeout);\n\n      _this.setState({\n        visible: true\n      });\n\n      _this.visibleTimeout = setTimeout(function () {\n        _this.setState({\n          visible: false\n        });\n      }, 2000);\n    };\n\n    _this.onScrollbarTouchStart = function (e) {\n      e.preventDefault();\n    };\n\n    _this.onContainerMouseDown = function (e) {\n      e.stopPropagation();\n      e.preventDefault();\n    };\n\n    _this.patchEvents = function () {\n      window.addEventListener('mousemove', _this.onMouseMove);\n      window.addEventListener('mouseup', _this.onMouseUp);\n\n      _this.thumbRef.current.addEventListener('touchmove', _this.onMouseMove);\n\n      _this.thumbRef.current.addEventListener('touchend', _this.onMouseUp);\n    };\n\n    _this.removeEvents = function () {\n      var _this$scrollbarRef$cu;\n\n      window.removeEventListener('mousemove', _this.onMouseMove);\n      window.removeEventListener('mouseup', _this.onMouseUp);\n      (_this$scrollbarRef$cu = _this.scrollbarRef.current) === null || _this$scrollbarRef$cu === void 0 ? void 0 : _this$scrollbarRef$cu.removeEventListener('touchstart', _this.onScrollbarTouchStart);\n\n      if (_this.thumbRef.current) {\n        _this.thumbRef.current.removeEventListener('touchstart', _this.onMouseDown);\n\n        _this.thumbRef.current.removeEventListener('touchmove', _this.onMouseMove);\n\n        _this.thumbRef.current.removeEventListener('touchend', _this.onMouseUp);\n      }\n\n      raf.cancel(_this.moveRaf);\n    };\n\n    _this.onMouseDown = function (e) {\n      var onStartMove = _this.props.onStartMove;\n\n      _this.setState({\n        dragging: true,\n        pageY: getPageY(e),\n        startTop: _this.getTop()\n      });\n\n      onStartMove();\n\n      _this.patchEvents();\n\n      e.stopPropagation();\n      e.preventDefault();\n    };\n\n    _this.onMouseMove = function (e) {\n      var _this$state = _this.state,\n          dragging = _this$state.dragging,\n          pageY = _this$state.pageY,\n          startTop = _this$state.startTop;\n      var onScroll = _this.props.onScroll;\n      raf.cancel(_this.moveRaf);\n\n      if (dragging) {\n        var offsetY = getPageY(e) - pageY;\n        var newTop = startTop + offsetY;\n\n        var enableScrollRange = _this.getEnableScrollRange();\n\n        var enableHeightRange = _this.getEnableHeightRange();\n\n        var ptg = enableHeightRange ? newTop / enableHeightRange : 0;\n        var newScrollTop = Math.ceil(ptg * enableScrollRange);\n        _this.moveRaf = raf(function () {\n          onScroll(newScrollTop);\n        });\n      }\n    };\n\n    _this.onMouseUp = function () {\n      var onStopMove = _this.props.onStopMove;\n\n      _this.setState({\n        dragging: false\n      });\n\n      onStopMove();\n\n      _this.removeEvents();\n    };\n\n    _this.getSpinHeight = function () {\n      var _this$props = _this.props,\n          height = _this$props.height,\n          count = _this$props.count;\n      var baseHeight = height / count * 10;\n      baseHeight = Math.max(baseHeight, MIN_SIZE);\n      baseHeight = Math.min(baseHeight, height / 2);\n      return Math.floor(baseHeight);\n    };\n\n    _this.getEnableScrollRange = function () {\n      var _this$props2 = _this.props,\n          scrollHeight = _this$props2.scrollHeight,\n          height = _this$props2.height;\n      return scrollHeight - height || 0;\n    };\n\n    _this.getEnableHeightRange = function () {\n      var height = _this.props.height;\n\n      var spinHeight = _this.getSpinHeight();\n\n      return height - spinHeight || 0;\n    };\n\n    _this.getTop = function () {\n      var scrollTop = _this.props.scrollTop;\n\n      var enableScrollRange = _this.getEnableScrollRange();\n\n      var enableHeightRange = _this.getEnableHeightRange();\n\n      if (scrollTop === 0 || enableScrollRange === 0) {\n        return 0;\n      }\n\n      var ptg = scrollTop / enableScrollRange;\n      return ptg * enableHeightRange;\n    };\n\n    _this.showScroll = function () {\n      var _this$props3 = _this.props,\n          height = _this$props3.height,\n          scrollHeight = _this$props3.scrollHeight;\n      return scrollHeight > height;\n    };\n\n    return _this;\n  }\n\n  _createClass(ScrollBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.scrollbarRef.current.addEventListener('touchstart', this.onScrollbarTouchStart);\n      this.thumbRef.current.addEventListener('touchstart', this.onMouseDown);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.scrollTop !== this.props.scrollTop) {\n        this.delayHidden();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.removeEvents();\n      clearTimeout(this.visibleTimeout);\n    }\n  }, {\n    key: \"render\",\n    value: // ====================== Render =======================\n    function render() {\n      var _this$state2 = this.state,\n          dragging = _this$state2.dragging,\n          visible = _this$state2.visible;\n      var prefixCls = this.props.prefixCls;\n      var spinHeight = this.getSpinHeight();\n      var top = this.getTop();\n      var canScroll = this.showScroll();\n      var mergedVisible = canScroll && visible;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.scrollbarRef,\n        className: classNames(\"\".concat(prefixCls, \"-scrollbar\"), _defineProperty({}, \"\".concat(prefixCls, \"-scrollbar-show\"), canScroll)),\n        style: {\n          width: 8,\n          top: 0,\n          bottom: 0,\n          right: 0,\n          position: 'absolute',\n          display: mergedVisible ? null : 'none'\n        },\n        onMouseDown: this.onContainerMouseDown,\n        onMouseMove: this.delayHidden\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.thumbRef,\n        className: classNames(\"\".concat(prefixCls, \"-scrollbar-thumb\"), _defineProperty({}, \"\".concat(prefixCls, \"-scrollbar-thumb-moving\"), dragging)),\n        style: {\n          width: '100%',\n          height: spinHeight,\n          top: top,\n          left: 0,\n          position: 'absolute',\n          background: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 99,\n          cursor: 'pointer',\n          userSelect: 'none'\n        },\n        onMouseDown: this.onMouseDown\n      }));\n    }\n  }]);\n\n  return ScrollBar;\n}(React.Component);\n\nexport { ScrollBar as default };"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAE/U,SAASK,eAAeA,CAACL,GAAG,EAAEM,GAAG,EAAEC,KAAK,EAAE;EAAE,IAAID,GAAG,IAAIN,GAAG,EAAE;IAAEQ,MAAM,CAACC,cAAc,CAACT,GAAG,EAAEM,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEG,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEZ,GAAG,CAACM,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOP,GAAG;AAAE;AAEhN,SAASa,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACZ,UAAU,GAAGY,UAAU,CAACZ,UAAU,IAAI,KAAK;IAAEY,UAAU,CAACX,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIW,UAAU,EAAEA,UAAU,CAACV,QAAQ,GAAG,IAAI;IAAEJ,MAAM,CAACC,cAAc,CAACS,MAAM,EAAEI,UAAU,CAAChB,GAAG,EAAEgB,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASC,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACX,SAAS,EAAEoB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEjB,MAAM,CAACC,cAAc,CAACM,WAAW,EAAE,WAAW,EAAE;IAAEH,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOG,WAAW;AAAE;AAE5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAACvB,SAAS,GAAGI,MAAM,CAACqB,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACxB,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEI,KAAK,EAAEoB,QAAQ;MAAEf,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEH,MAAM,CAACC,cAAc,CAACkB,QAAQ,EAAE,WAAW,EAAE;IAAEf,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIgB,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;AAAE;AAEnc,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGtB,MAAM,CAACyB,cAAc,IAAI,SAASH,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AAEzK,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACtC,WAAW;MAAEuC,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEM,SAAS,EAAEH,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAACO,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IAAE;IAAE,OAAOE,0BAA0B,CAAC,IAAI,EAAEN,MAAM,CAAC;EAAE,CAAC;AAAE;AAExa,SAASM,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnD,OAAO,CAACmD,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIlC,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOmC,sBAAsB,CAACF,IAAI,CAAC;AAAE;AAE/R,SAASE,sBAAsBA,CAACF,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIG,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAErK,SAASX,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACQ,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACnD,SAAS,CAACoD,OAAO,CAACN,IAAI,CAACN,OAAO,CAACC,SAAS,CAACU,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AAExU,SAAShB,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAGjC,MAAM,CAACyB,cAAc,GAAGzB,MAAM,CAACkD,cAAc,GAAG,SAASjB,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAI1B,MAAM,CAACkD,cAAc,CAAC3B,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AAE5M,OAAO,KAAK4B,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,IAAIC,QAAQ,GAAG,EAAE;AAEjB,SAASC,QAAQA,CAACN,CAAC,EAAE;EACnB,OAAO,SAAS,IAAIA,CAAC,GAAGA,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGR,CAAC,CAACQ,KAAK;AACtD;AAEA,IAAIC,SAAS,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACvDzC,SAAS,CAACwC,SAAS,EAAEC,gBAAgB,CAAC;EAEtC,IAAIC,MAAM,GAAGjC,YAAY,CAAC+B,SAAS,CAAC;EAEpC,SAASA,SAASA,CAAA,EAAG;IACnB,IAAIG,KAAK;IAETxD,eAAe,CAAC,IAAI,EAAEqD,SAAS,CAAC;IAEhC,KAAK,IAAII,IAAI,GAAGxB,SAAS,CAACzB,MAAM,EAAEkD,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAG3B,SAAS,CAAC2B,IAAI,CAAC;IAC9B;IAEAJ,KAAK,GAAGD,MAAM,CAAClB,IAAI,CAACH,KAAK,CAACqB,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDF,KAAK,CAACM,OAAO,GAAG,IAAI;IACpBN,KAAK,CAACO,YAAY,GAAG,aAAajB,KAAK,CAACkB,SAAS,CAAC,CAAC;IACnDR,KAAK,CAACS,QAAQ,GAAG,aAAanB,KAAK,CAACkB,SAAS,CAAC,CAAC;IAC/CR,KAAK,CAACU,cAAc,GAAG,IAAI;IAC3BV,KAAK,CAACW,KAAK,GAAG;MACZC,QAAQ,EAAE,KAAK;MACfhB,KAAK,EAAE,IAAI;MACXiB,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;IACX,CAAC;IAEDd,KAAK,CAACe,WAAW,GAAG,YAAY;MAC9BC,YAAY,CAAChB,KAAK,CAACU,cAAc,CAAC;MAElCV,KAAK,CAACiB,QAAQ,CAAC;QACbH,OAAO,EAAE;MACX,CAAC,CAAC;MAEFd,KAAK,CAACU,cAAc,GAAGQ,UAAU,CAAC,YAAY;QAC5ClB,KAAK,CAACiB,QAAQ,CAAC;UACbH,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDd,KAAK,CAACmB,qBAAqB,GAAG,UAAU/B,CAAC,EAAE;MACzCA,CAAC,CAACgC,cAAc,CAAC,CAAC;IACpB,CAAC;IAEDpB,KAAK,CAACqB,oBAAoB,GAAG,UAAUjC,CAAC,EAAE;MACxCA,CAAC,CAACkC,eAAe,CAAC,CAAC;MACnBlC,CAAC,CAACgC,cAAc,CAAC,CAAC;IACpB,CAAC;IAEDpB,KAAK,CAACuB,WAAW,GAAG,YAAY;MAC9BC,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEzB,KAAK,CAAC0B,WAAW,CAAC;MACvDF,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEzB,KAAK,CAAC2B,SAAS,CAAC;MAEnD3B,KAAK,CAACS,QAAQ,CAACmB,OAAO,CAACH,gBAAgB,CAAC,WAAW,EAAEzB,KAAK,CAAC0B,WAAW,CAAC;MAEvE1B,KAAK,CAACS,QAAQ,CAACmB,OAAO,CAACH,gBAAgB,CAAC,UAAU,EAAEzB,KAAK,CAAC2B,SAAS,CAAC;IACtE,CAAC;IAED3B,KAAK,CAAC6B,YAAY,GAAG,YAAY;MAC/B,IAAIC,qBAAqB;MAEzBN,MAAM,CAACO,mBAAmB,CAAC,WAAW,EAAE/B,KAAK,CAAC0B,WAAW,CAAC;MAC1DF,MAAM,CAACO,mBAAmB,CAAC,SAAS,EAAE/B,KAAK,CAAC2B,SAAS,CAAC;MACtD,CAACG,qBAAqB,GAAG9B,KAAK,CAACO,YAAY,CAACqB,OAAO,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,mBAAmB,CAAC,YAAY,EAAE/B,KAAK,CAACmB,qBAAqB,CAAC;MAEjM,IAAInB,KAAK,CAACS,QAAQ,CAACmB,OAAO,EAAE;QAC1B5B,KAAK,CAACS,QAAQ,CAACmB,OAAO,CAACG,mBAAmB,CAAC,YAAY,EAAE/B,KAAK,CAACgC,WAAW,CAAC;QAE3EhC,KAAK,CAACS,QAAQ,CAACmB,OAAO,CAACG,mBAAmB,CAAC,WAAW,EAAE/B,KAAK,CAAC0B,WAAW,CAAC;QAE1E1B,KAAK,CAACS,QAAQ,CAACmB,OAAO,CAACG,mBAAmB,CAAC,UAAU,EAAE/B,KAAK,CAAC2B,SAAS,CAAC;MACzE;MAEAnC,GAAG,CAACyC,MAAM,CAACjC,KAAK,CAACM,OAAO,CAAC;IAC3B,CAAC;IAEDN,KAAK,CAACgC,WAAW,GAAG,UAAU5C,CAAC,EAAE;MAC/B,IAAI8C,WAAW,GAAGlC,KAAK,CAAClD,KAAK,CAACoF,WAAW;MAEzClC,KAAK,CAACiB,QAAQ,CAAC;QACbL,QAAQ,EAAE,IAAI;QACdhB,KAAK,EAAEF,QAAQ,CAACN,CAAC,CAAC;QAClByB,QAAQ,EAAEb,KAAK,CAACmC,MAAM,CAAC;MACzB,CAAC,CAAC;MAEFD,WAAW,CAAC,CAAC;MAEblC,KAAK,CAACuB,WAAW,CAAC,CAAC;MAEnBnC,CAAC,CAACkC,eAAe,CAAC,CAAC;MACnBlC,CAAC,CAACgC,cAAc,CAAC,CAAC;IACpB,CAAC;IAEDpB,KAAK,CAAC0B,WAAW,GAAG,UAAUtC,CAAC,EAAE;MAC/B,IAAIgD,WAAW,GAAGpC,KAAK,CAACW,KAAK;QACzBC,QAAQ,GAAGwB,WAAW,CAACxB,QAAQ;QAC/BhB,KAAK,GAAGwC,WAAW,CAACxC,KAAK;QACzBiB,QAAQ,GAAGuB,WAAW,CAACvB,QAAQ;MACnC,IAAIwB,QAAQ,GAAGrC,KAAK,CAAClD,KAAK,CAACuF,QAAQ;MACnC7C,GAAG,CAACyC,MAAM,CAACjC,KAAK,CAACM,OAAO,CAAC;MAEzB,IAAIM,QAAQ,EAAE;QACZ,IAAI0B,OAAO,GAAG5C,QAAQ,CAACN,CAAC,CAAC,GAAGQ,KAAK;QACjC,IAAI2C,MAAM,GAAG1B,QAAQ,GAAGyB,OAAO;QAE/B,IAAIE,iBAAiB,GAAGxC,KAAK,CAACyC,oBAAoB,CAAC,CAAC;QAEpD,IAAIC,iBAAiB,GAAG1C,KAAK,CAAC2C,oBAAoB,CAAC,CAAC;QAEpD,IAAIC,GAAG,GAAGF,iBAAiB,GAAGH,MAAM,GAAGG,iBAAiB,GAAG,CAAC;QAC5D,IAAIG,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACH,GAAG,GAAGJ,iBAAiB,CAAC;QACrDxC,KAAK,CAACM,OAAO,GAAGd,GAAG,CAAC,YAAY;UAC9B6C,QAAQ,CAACQ,YAAY,CAAC;QACxB,CAAC,CAAC;MACJ;IACF,CAAC;IAED7C,KAAK,CAAC2B,SAAS,GAAG,YAAY;MAC5B,IAAIqB,UAAU,GAAGhD,KAAK,CAAClD,KAAK,CAACkG,UAAU;MAEvChD,KAAK,CAACiB,QAAQ,CAAC;QACbL,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFoC,UAAU,CAAC,CAAC;MAEZhD,KAAK,CAAC6B,YAAY,CAAC,CAAC;IACtB,CAAC;IAED7B,KAAK,CAACiD,aAAa,GAAG,YAAY;MAChC,IAAIC,WAAW,GAAGlD,KAAK,CAAClD,KAAK;QACzBqG,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,KAAK,GAAGF,WAAW,CAACE,KAAK;MAC7B,IAAIC,UAAU,GAAGF,MAAM,GAAGC,KAAK,GAAG,EAAE;MACpCC,UAAU,GAAGP,IAAI,CAACQ,GAAG,CAACD,UAAU,EAAE5D,QAAQ,CAAC;MAC3C4D,UAAU,GAAGP,IAAI,CAACS,GAAG,CAACF,UAAU,EAAEF,MAAM,GAAG,CAAC,CAAC;MAC7C,OAAOL,IAAI,CAACU,KAAK,CAACH,UAAU,CAAC;IAC/B,CAAC;IAEDrD,KAAK,CAACyC,oBAAoB,GAAG,YAAY;MACvC,IAAIgB,YAAY,GAAGzD,KAAK,CAAClD,KAAK;QAC1B4G,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxCP,MAAM,GAAGM,YAAY,CAACN,MAAM;MAChC,OAAOO,YAAY,GAAGP,MAAM,IAAI,CAAC;IACnC,CAAC;IAEDnD,KAAK,CAAC2C,oBAAoB,GAAG,YAAY;MACvC,IAAIQ,MAAM,GAAGnD,KAAK,CAAClD,KAAK,CAACqG,MAAM;MAE/B,IAAIQ,UAAU,GAAG3D,KAAK,CAACiD,aAAa,CAAC,CAAC;MAEtC,OAAOE,MAAM,GAAGQ,UAAU,IAAI,CAAC;IACjC,CAAC;IAED3D,KAAK,CAACmC,MAAM,GAAG,YAAY;MACzB,IAAIyB,SAAS,GAAG5D,KAAK,CAAClD,KAAK,CAAC8G,SAAS;MAErC,IAAIpB,iBAAiB,GAAGxC,KAAK,CAACyC,oBAAoB,CAAC,CAAC;MAEpD,IAAIC,iBAAiB,GAAG1C,KAAK,CAAC2C,oBAAoB,CAAC,CAAC;MAEpD,IAAIiB,SAAS,KAAK,CAAC,IAAIpB,iBAAiB,KAAK,CAAC,EAAE;QAC9C,OAAO,CAAC;MACV;MAEA,IAAII,GAAG,GAAGgB,SAAS,GAAGpB,iBAAiB;MACvC,OAAOI,GAAG,GAAGF,iBAAiB;IAChC,CAAC;IAED1C,KAAK,CAAC6D,UAAU,GAAG,YAAY;MAC7B,IAAIC,YAAY,GAAG9D,KAAK,CAAClD,KAAK;QAC1BqG,MAAM,GAAGW,YAAY,CAACX,MAAM;QAC5BO,YAAY,GAAGI,YAAY,CAACJ,YAAY;MAC5C,OAAOA,YAAY,GAAGP,MAAM;IAC9B,CAAC;IAED,OAAOnD,KAAK;EACd;EAEA9C,YAAY,CAAC2C,SAAS,EAAE,CAAC;IACvB5D,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAAS6H,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACxD,YAAY,CAACqB,OAAO,CAACH,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACN,qBAAqB,CAAC;MACpF,IAAI,CAACV,QAAQ,CAACmB,OAAO,CAACH,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACO,WAAW,CAAC;IACxE;EACF,CAAC,EAAE;IACD/F,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAAS8H,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIA,SAAS,CAACL,SAAS,KAAK,IAAI,CAAC9G,KAAK,CAAC8G,SAAS,EAAE;QAChD,IAAI,CAAC7C,WAAW,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASgI,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACrC,YAAY,CAAC,CAAC;MACnBb,YAAY,CAAC,IAAI,CAACN,cAAc,CAAC;IACnC;EACF,CAAC,EAAE;IACDzE,GAAG,EAAE,QAAQ;IACbC,KAAK;IAAE;IACP,SAASiI,MAAMA,CAAA,EAAG;MAChB,IAAIC,YAAY,GAAG,IAAI,CAACzD,KAAK;QACzBC,QAAQ,GAAGwD,YAAY,CAACxD,QAAQ;QAChCE,OAAO,GAAGsD,YAAY,CAACtD,OAAO;MAClC,IAAIuD,SAAS,GAAG,IAAI,CAACvH,KAAK,CAACuH,SAAS;MACpC,IAAIV,UAAU,GAAG,IAAI,CAACV,aAAa,CAAC,CAAC;MACrC,IAAIqB,GAAG,GAAG,IAAI,CAACnC,MAAM,CAAC,CAAC;MACvB,IAAIoC,SAAS,GAAG,IAAI,CAACV,UAAU,CAAC,CAAC;MACjC,IAAIW,aAAa,GAAGD,SAAS,IAAIzD,OAAO;MACxC,OAAO,aAAaxB,KAAK,CAACmF,aAAa,CAAC,KAAK,EAAE;QAC7CC,GAAG,EAAE,IAAI,CAACnE,YAAY;QACtBoE,SAAS,EAAEpF,UAAU,CAAC,EAAE,CAACc,MAAM,CAACgE,SAAS,EAAE,YAAY,CAAC,EAAErI,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqE,MAAM,CAACgE,SAAS,EAAE,iBAAiB,CAAC,EAAEE,SAAS,CAAC,CAAC;QAClIK,KAAK,EAAE;UACLC,KAAK,EAAE,CAAC;UACRP,GAAG,EAAE,CAAC;UACNQ,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAE,UAAU;UACpBC,OAAO,EAAET,aAAa,GAAG,IAAI,GAAG;QAClC,CAAC;QACDxC,WAAW,EAAE,IAAI,CAACX,oBAAoB;QACtCK,WAAW,EAAE,IAAI,CAACX;MACpB,CAAC,EAAE,aAAazB,KAAK,CAACmF,aAAa,CAAC,KAAK,EAAE;QACzCC,GAAG,EAAE,IAAI,CAACjE,QAAQ;QAClBkE,SAAS,EAAEpF,UAAU,CAAC,EAAE,CAACc,MAAM,CAACgE,SAAS,EAAE,kBAAkB,CAAC,EAAErI,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqE,MAAM,CAACgE,SAAS,EAAE,yBAAyB,CAAC,EAAEzD,QAAQ,CAAC,CAAC;QAC/IgE,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACb1B,MAAM,EAAEQ,UAAU;UAClBW,GAAG,EAAEA,GAAG;UACRY,IAAI,EAAE,CAAC;UACPF,QAAQ,EAAE,UAAU;UACpBG,UAAU,EAAE,oBAAoB;UAChCC,YAAY,EAAE,EAAE;UAChBC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE;QACd,CAAC;QACDtD,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAOnC,SAAS;AAClB,CAAC,CAACP,KAAK,CAACiG,SAAS,CAAC;AAElB,SAAS1F,SAAS,IAAI2F,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}