{"ast": null, "code": "import React from 'react';\nimport CSSMotion from 'rc-motion';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nvar getCollapsedWidth = function getCollapsedWidth() {\n  return {\n    width: 0,\n    opacity: 0,\n    transform: 'scale(0)'\n  };\n};\nvar getRealWidth = function getRealWidth(node) {\n  return {\n    width: node.scrollWidth,\n    opacity: 1,\n    transform: 'scale(1)'\n  };\n};\nvar LoadingIcon = function LoadingIcon(_ref) {\n  var prefixCls = _ref.prefixCls,\n    loading = _ref.loading,\n    existIcon = _ref.existIcon;\n  var visible = !!loading;\n  if (existIcon) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-loading-icon\")\n    }, /*#__PURE__*/React.createElement(LoadingOutlined, null));\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible // We do not really use this motionName\n    ,\n\n    motionName: \"\".concat(prefixCls, \"-loading-icon-motion\"),\n    removeOnLeave: true,\n    onAppearStart: getCollapsedWidth,\n    onAppearActive: getRealWidth,\n    onEnterStart: getCollapsedWidth,\n    onEnterActive: getRealWidth,\n    onLeaveStart: getRealWidth,\n    onLeaveActive: getCollapsedWidth\n  }, function (_ref2, ref) {\n    var className = _ref2.className,\n      style = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-loading-icon\"),\n      style: style,\n      ref: ref\n    }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: className\n    }));\n  });\n};\nexport default LoadingIcon;", "map": {"version": 3, "names": ["React", "CSSMotion", "LoadingOutlined", "getCollapsedWidth", "width", "opacity", "transform", "getRealWidth", "node", "scrollWidth", "LoadingIcon", "_ref", "prefixCls", "loading", "existIcon", "visible", "createElement", "className", "concat", "motionName", "removeOnLeave", "onAppearStart", "onAppearActive", "onEnterStart", "onEnterActive", "onLeaveStart", "onLeaveActive", "_ref2", "ref", "style"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/button/LoadingIcon.js"], "sourcesContent": ["import React from 'react';\nimport CSSMotion from 'rc-motion';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\n\nvar getCollapsedWidth = function getCollapsedWidth() {\n  return {\n    width: 0,\n    opacity: 0,\n    transform: 'scale(0)'\n  };\n};\n\nvar getRealWidth = function getRealWidth(node) {\n  return {\n    width: node.scrollWidth,\n    opacity: 1,\n    transform: 'scale(1)'\n  };\n};\n\nvar LoadingIcon = function LoadingIcon(_ref) {\n  var prefixCls = _ref.prefixCls,\n      loading = _ref.loading,\n      existIcon = _ref.existIcon;\n  var visible = !!loading;\n\n  if (existIcon) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-loading-icon\")\n    }, /*#__PURE__*/React.createElement(LoadingOutlined, null));\n  }\n\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible // We do not really use this motionName\n    ,\n    motionName: \"\".concat(prefixCls, \"-loading-icon-motion\"),\n    removeOnLeave: true,\n    onAppearStart: getCollapsedWidth,\n    onAppearActive: getRealWidth,\n    onEnterStart: getCollapsedWidth,\n    onEnterActive: getRealWidth,\n    onLeaveStart: getRealWidth,\n    onLeaveActive: getCollapsedWidth\n  }, function (_ref2, ref) {\n    var className = _ref2.className,\n        style = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-loading-icon\"),\n      style: style,\n      ref: ref\n    }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: className\n    }));\n  });\n};\n\nexport default LoadingIcon;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,eAAe,MAAM,4CAA4C;AAExE,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;EACnD,OAAO;IACLC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACb,CAAC;AACH,CAAC;AAED,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,OAAO;IACLJ,KAAK,EAAEI,IAAI,CAACC,WAAW;IACvBJ,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACb,CAAC;AACH,CAAC;AAED,IAAII,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,SAAS,GAAGH,IAAI,CAACG,SAAS;EAC9B,IAAIC,OAAO,GAAG,CAAC,CAACF,OAAO;EAEvB,IAAIC,SAAS,EAAE;IACb,OAAO,aAAad,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,eAAe;IACjD,CAAC,EAAE,aAAaZ,KAAK,CAACgB,aAAa,CAACd,eAAe,EAAE,IAAI,CAAC,CAAC;EAC7D;EAEA,OAAO,aAAaF,KAAK,CAACgB,aAAa,CAACf,SAAS,EAAE;IACjDc,OAAO,EAAEA,OAAO,CAAC;IAAA;;IAEjBI,UAAU,EAAE,EAAE,CAACD,MAAM,CAACN,SAAS,EAAE,sBAAsB,CAAC;IACxDQ,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAElB,iBAAiB;IAChCmB,cAAc,EAAEf,YAAY;IAC5BgB,YAAY,EAAEpB,iBAAiB;IAC/BqB,aAAa,EAAEjB,YAAY;IAC3BkB,YAAY,EAAElB,YAAY;IAC1BmB,aAAa,EAAEvB;EACjB,CAAC,EAAE,UAAUwB,KAAK,EAAEC,GAAG,EAAE;IACvB,IAAIX,SAAS,GAAGU,KAAK,CAACV,SAAS;MAC3BY,KAAK,GAAGF,KAAK,CAACE,KAAK;IACvB,OAAO,aAAa7B,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,eAAe,CAAC;MAChDiB,KAAK,EAAEA,KAAK;MACZD,GAAG,EAAEA;IACP,CAAC,EAAE,aAAa5B,KAAK,CAACgB,aAAa,CAACd,eAAe,EAAE;MACnDe,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AAED,eAAeP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}