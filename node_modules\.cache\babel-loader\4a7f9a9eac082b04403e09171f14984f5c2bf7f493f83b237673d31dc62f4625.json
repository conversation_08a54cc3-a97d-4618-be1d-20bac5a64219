{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/button';\nfunction isThenable(thing) {\n  return !!(thing && !!thing.then);\n}\nvar ActionButton = function ActionButton(props) {\n  var clickedRef = React.useRef(false);\n  var ref = React.useRef();\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loading = _useState2[0],\n    setLoading = _useState2[1];\n  React.useEffect(function () {\n    var timeoutId;\n    if (props.autoFocus) {\n      var $this = ref.current;\n      timeoutId = setTimeout(function () {\n        return $this.focus();\n      });\n    }\n    return function () {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n  var handlePromiseOnOk = function handlePromiseOnOk(returnValueOfOnOk) {\n    var close = props.close;\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n    setLoading(true);\n    returnValueOfOnOk.then(function () {\n      setLoading(false, true);\n      close.apply(void 0, arguments);\n      clickedRef.current = false;\n    }, function (e) {\n      // Emit error when catch promise reject\n      // eslint-disable-next-line no-console\n      console.error(e); // See: https://github.com/ant-design/ant-design/issues/6183\n\n      setLoading(false, true);\n      clickedRef.current = false;\n    });\n  };\n  var onClick = function onClick(e) {\n    var actionFn = props.actionFn,\n      close = props.close;\n    if (clickedRef.current) {\n      return;\n    }\n    clickedRef.current = true;\n    if (!actionFn) {\n      close();\n      return;\n    }\n    var returnValueOfOnOk;\n    if (props.emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n      if (props.quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        close(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close); // https://github.com/ant-design/ant-design/issues/23358\n\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n      if (!returnValueOfOnOk) {\n        close();\n        return;\n      }\n    }\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n  var type = props.type,\n    children = props.children,\n    prefixCls = props.prefixCls,\n    buttonProps = props.buttonProps;\n  return /*#__PURE__*/React.createElement(Button, _extends({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: ref\n  }), children);\n};\nexport default ActionButton;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "useState", "<PERSON><PERSON>", "convertLegacyProps", "isThenable", "thing", "then", "ActionButton", "props", "clickedRef", "useRef", "ref", "_useState", "_useState2", "loading", "setLoading", "useEffect", "timeoutId", "autoFocus", "$this", "current", "setTimeout", "focus", "clearTimeout", "handlePromiseOnOk", "returnValueOfOnOk", "close", "apply", "arguments", "e", "console", "error", "onClick", "actionFn", "emitEvent", "quitOnNullishReturnValue", "length", "type", "children", "prefixCls", "buttonProps", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/ActionButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/button';\n\nfunction isThenable(thing) {\n  return !!(thing && !!thing.then);\n}\n\nvar ActionButton = function ActionButton(props) {\n  var clickedRef = React.useRef(false);\n  var ref = React.useRef();\n\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      loading = _useState2[0],\n      setLoading = _useState2[1];\n\n  React.useEffect(function () {\n    var timeoutId;\n\n    if (props.autoFocus) {\n      var $this = ref.current;\n      timeoutId = setTimeout(function () {\n        return $this.focus();\n      });\n    }\n\n    return function () {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n\n  var handlePromiseOnOk = function handlePromiseOnOk(returnValueOfOnOk) {\n    var close = props.close;\n\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n\n    setLoading(true);\n    returnValueOfOnOk.then(function () {\n      setLoading(false, true);\n      close.apply(void 0, arguments);\n      clickedRef.current = false;\n    }, function (e) {\n      // Emit error when catch promise reject\n      // eslint-disable-next-line no-console\n      console.error(e); // See: https://github.com/ant-design/ant-design/issues/6183\n\n      setLoading(false, true);\n      clickedRef.current = false;\n    });\n  };\n\n  var onClick = function onClick(e) {\n    var actionFn = props.actionFn,\n        close = props.close;\n\n    if (clickedRef.current) {\n      return;\n    }\n\n    clickedRef.current = true;\n\n    if (!actionFn) {\n      close();\n      return;\n    }\n\n    var returnValueOfOnOk;\n\n    if (props.emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n\n      if (props.quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        close(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close); // https://github.com/ant-design/ant-design/issues/23358\n\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n\n      if (!returnValueOfOnOk) {\n        close();\n        return;\n      }\n    }\n\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n\n  var type = props.type,\n      children = props.children,\n      prefixCls = props.prefixCls,\n      buttonProps = props.buttonProps;\n  return /*#__PURE__*/React.createElement(Button, _extends({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: ref\n  }), children);\n};\n\nexport default ActionButton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAAC,EAAEA,KAAK,IAAI,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC;AAClC;AAEA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC9C,IAAIC,UAAU,GAAGT,KAAK,CAACU,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIC,GAAG,GAAGX,KAAK,CAACU,MAAM,CAAC,CAAC;EAExB,IAAIE,SAAS,GAAGX,QAAQ,CAAC,KAAK,CAAC;IAC3BY,UAAU,GAAGd,cAAc,CAACa,SAAS,EAAE,CAAC,CAAC;IACzCE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE9Bb,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1B,IAAIC,SAAS;IAEb,IAAIT,KAAK,CAACU,SAAS,EAAE;MACnB,IAAIC,KAAK,GAAGR,GAAG,CAACS,OAAO;MACvBH,SAAS,GAAGI,UAAU,CAAC,YAAY;QACjC,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;IAEA,OAAO,YAAY;MACjB,IAAIL,SAAS,EAAE;QACbM,YAAY,CAACN,SAAS,CAAC;MACzB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIO,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,iBAAiB,EAAE;IACpE,IAAIC,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IAEvB,IAAI,CAACtB,UAAU,CAACqB,iBAAiB,CAAC,EAAE;MAClC;IACF;IAEAV,UAAU,CAAC,IAAI,CAAC;IAChBU,iBAAiB,CAACnB,IAAI,CAAC,YAAY;MACjCS,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;MACvBW,KAAK,CAACC,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAC9BnB,UAAU,CAACW,OAAO,GAAG,KAAK;IAC5B,CAAC,EAAE,UAAUS,CAAC,EAAE;MACd;MACA;MACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC,CAAC,CAAC;;MAElBd,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;MACvBN,UAAU,CAACW,OAAO,GAAG,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,IAAIY,OAAO,GAAG,SAASA,OAAOA,CAACH,CAAC,EAAE;IAChC,IAAII,QAAQ,GAAGzB,KAAK,CAACyB,QAAQ;MACzBP,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IAEvB,IAAIjB,UAAU,CAACW,OAAO,EAAE;MACtB;IACF;IAEAX,UAAU,CAACW,OAAO,GAAG,IAAI;IAEzB,IAAI,CAACa,QAAQ,EAAE;MACbP,KAAK,CAAC,CAAC;MACP;IACF;IAEA,IAAID,iBAAiB;IAErB,IAAIjB,KAAK,CAAC0B,SAAS,EAAE;MACnBT,iBAAiB,GAAGQ,QAAQ,CAACJ,CAAC,CAAC;MAE/B,IAAIrB,KAAK,CAAC2B,wBAAwB,IAAI,CAAC/B,UAAU,CAACqB,iBAAiB,CAAC,EAAE;QACpEhB,UAAU,CAACW,OAAO,GAAG,KAAK;QAC1BM,KAAK,CAACG,CAAC,CAAC;QACR;MACF;IACF,CAAC,MAAM,IAAII,QAAQ,CAACG,MAAM,EAAE;MAC1BX,iBAAiB,GAAGQ,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC;;MAErCjB,UAAU,CAACW,OAAO,GAAG,KAAK;IAC5B,CAAC,MAAM;MACLK,iBAAiB,GAAGQ,QAAQ,CAAC,CAAC;MAE9B,IAAI,CAACR,iBAAiB,EAAE;QACtBC,KAAK,CAAC,CAAC;QACP;MACF;IACF;IAEAF,iBAAiB,CAACC,iBAAiB,CAAC;EACtC,CAAC;EAED,IAAIY,IAAI,GAAG7B,KAAK,CAAC6B,IAAI;IACjBC,QAAQ,GAAG9B,KAAK,CAAC8B,QAAQ;IACzBC,SAAS,GAAG/B,KAAK,CAAC+B,SAAS;IAC3BC,WAAW,GAAGhC,KAAK,CAACgC,WAAW;EACnC,OAAO,aAAaxC,KAAK,CAACyC,aAAa,CAACvC,MAAM,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEK,kBAAkB,CAACkC,IAAI,CAAC,EAAE;IACrFL,OAAO,EAAEA,OAAO;IAChBlB,OAAO,EAAEA,OAAO;IAChByB,SAAS,EAAEA;EACb,CAAC,EAAEC,WAAW,EAAE;IACd7B,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE2B,QAAQ,CAAC;AACf,CAAC;AAED,eAAe/B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}