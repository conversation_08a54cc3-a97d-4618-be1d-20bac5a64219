{"ast": null, "code": "import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context';\nexport function I18nextProvider(_ref) {\n  var i18n = _ref.i18n,\n    defaultNS = _ref.defaultNS,\n    children = _ref.children;\n  var value = useMemo(function () {\n    return {\n      i18n: i18n,\n      defaultNS: defaultNS\n    };\n  }, [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value: value\n  }, children);\n}", "map": {"version": 3, "names": ["createElement", "useMemo", "I18nContext", "I18nextProvider", "_ref", "i18n", "defaultNS", "children", "value", "Provider"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/I18nextProvider.js"], "sourcesContent": ["import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context';\nexport function I18nextProvider(_ref) {\n  var i18n = _ref.i18n,\n      defaultNS = _ref.defaultNS,\n      children = _ref.children;\n  var value = useMemo(function () {\n    return {\n      i18n: i18n,\n      defaultNS: defaultNS\n    };\n  }, [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value: value\n  }, children);\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,OAAO,QAAQ,OAAO;AAC9C,SAASC,WAAW,QAAQ,WAAW;AACvC,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIC,KAAK,GAAGP,OAAO,CAAC,YAAY;IAC9B,OAAO;MACLI,IAAI,EAAEA,IAAI;MACVC,SAAS,EAAEA;IACb,CAAC;EACH,CAAC,EAAE,CAACD,IAAI,EAAEC,SAAS,CAAC,CAAC;EACrB,OAAON,aAAa,CAACE,WAAW,CAACO,QAAQ,EAAE;IACzCD,KAAK,EAAEA;EACT,CAAC,EAAED,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}