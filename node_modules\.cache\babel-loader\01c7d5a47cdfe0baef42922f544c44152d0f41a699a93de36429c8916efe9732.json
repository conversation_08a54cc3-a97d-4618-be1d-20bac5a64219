{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"showSearch\", \"tagRender\", \"direction\", \"omitDomProps\", \"displayValues\", \"onDisplayValuesChange\", \"emptyOptions\", \"notFoundContent\", \"onClear\", \"mode\", \"disabled\", \"loading\", \"getInputElement\", \"getRawInputElement\", \"open\", \"defaultOpen\", \"onDropdownVisibleChange\", \"activeValue\", \"onActiveValueChange\", \"activeDescendantId\", \"searchValue\", \"onSearch\", \"onSearchSplit\", \"tokenSeparators\", \"allowClear\", \"showArrow\", \"inputIcon\", \"clearIcon\", \"OptionList\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"placement\", \"getPopupContainer\", \"showAction\", \"onFocus\", \"onBlur\", \"onKeyUp\", \"onKeyDown\", \"onMouseDown\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { getSeparatedContent } from './utils/valueUtil';\nimport SelectTrigger from './SelectTrigger';\nimport Selector from './Selector';\nimport useSelectTriggerControl from './hooks/useSelectTriggerControl';\nimport useDelayReset from './hooks/useDelayReset';\nimport TransBtn from './TransBtn';\nimport useLock from './hooks/useLock';\nimport { BaseSelectContext } from './hooks/useBaseProps';\nvar DEFAULT_OMIT_PROPS = ['value', 'onChange', 'removeIcon', 'placeholder', 'autoFocus', 'maxTagCount', 'maxTagTextLength', 'maxTagPlaceholder', 'choiceTransitionName', 'onInputKeyDown', 'onPopupScroll', 'tabIndex'];\nexport function isMultiple(mode) {\n  return mode === 'tags' || mode === 'multiple';\n}\nvar BaseSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _customizeRawInputEle, _classNames2;\n  var id = props.id,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    showSearch = props.showSearch,\n    tagRender = props.tagRender,\n    direction = props.direction,\n    omitDomProps = props.omitDomProps,\n    displayValues = props.displayValues,\n    onDisplayValuesChange = props.onDisplayValuesChange,\n    emptyOptions = props.emptyOptions,\n    _props$notFoundConten = props.notFoundContent,\n    notFoundContent = _props$notFoundConten === void 0 ? 'Not Found' : _props$notFoundConten,\n    onClear = props.onClear,\n    mode = props.mode,\n    disabled = props.disabled,\n    loading = props.loading,\n    getInputElement = props.getInputElement,\n    getRawInputElement = props.getRawInputElement,\n    open = props.open,\n    defaultOpen = props.defaultOpen,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    activeValue = props.activeValue,\n    onActiveValueChange = props.onActiveValueChange,\n    activeDescendantId = props.activeDescendantId,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    onSearchSplit = props.onSearchSplit,\n    tokenSeparators = props.tokenSeparators,\n    allowClear = props.allowClear,\n    showArrow = props.showArrow,\n    inputIcon = props.inputIcon,\n    clearIcon = props.clearIcon,\n    OptionList = props.OptionList,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    placement = props.placement,\n    getPopupContainer = props.getPopupContainer,\n    _props$showAction = props.showAction,\n    showAction = _props$showAction === void 0 ? [] : _props$showAction,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyUp = props.onKeyUp,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    restProps = _objectWithoutProperties(props, _excluded); // ============================== MISC ==============================\n\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = (showSearch !== undefined ? showSearch : multiple) || mode === 'combobox';\n  var domProps = _objectSpread({}, restProps);\n  DEFAULT_OMIT_PROPS.forEach(function (propName) {\n    delete domProps[propName];\n  });\n  omitDomProps === null || omitDomProps === void 0 ? void 0 : omitDomProps.forEach(function (propName) {\n    delete domProps[propName];\n  }); // ============================= Mobile =============================\n\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mobile = _React$useState2[0],\n    setMobile = _React$useState2[1];\n  React.useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []); // ============================== Refs ==============================\n\n  var containerRef = React.useRef(null);\n  var selectorDomRef = React.useRef(null);\n  var triggerRef = React.useRef(null);\n  var selectorRef = React.useRef(null);\n  var listRef = React.useRef(null);\n  /** Used for component focused management */\n\n  var _useDelayReset = useDelayReset(),\n    _useDelayReset2 = _slicedToArray(_useDelayReset, 3),\n    mockFocused = _useDelayReset2[0],\n    setMockFocused = _useDelayReset2[1],\n    cancelSetMockFocused = _useDelayReset2[2]; // =========================== Imperative ===========================\n\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current, _selectorRef$current2;\n    return {\n      focus: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.focus,\n      blur: (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 ? void 0 : _selectorRef$current2.blur,\n      scrollTo: function scrollTo(arg) {\n        var _listRef$current;\n        return (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(arg);\n      }\n    };\n  }); // ========================== Search Value ==========================\n\n  var mergedSearchValue = React.useMemo(function () {\n    var _displayValues$;\n    if (mode !== 'combobox') {\n      return searchValue;\n    }\n    var val = (_displayValues$ = displayValues[0]) === null || _displayValues$ === void 0 ? void 0 : _displayValues$.value;\n    return typeof val === 'string' || typeof val === 'number' ? String(val) : '';\n  }, [searchValue, mode, displayValues]); // ========================== Custom Input ==========================\n  // Only works in `combobox`\n\n  var customizeInputElement = mode === 'combobox' && typeof getInputElement === 'function' && getInputElement() || null; // Used for customize replacement for `rc-cascader`\n\n  var customizeRawInputElement = typeof getRawInputElement === 'function' && getRawInputElement();\n  var customizeRawInputRef = useComposeRef(selectorDomRef, customizeRawInputElement === null || customizeRawInputElement === void 0 ? void 0 : (_customizeRawInputEle = customizeRawInputElement.props) === null || _customizeRawInputEle === void 0 ? void 0 : _customizeRawInputEle.ref); // ============================== Open ==============================\n\n  var _useMergedState = useMergedState(undefined, {\n      defaultValue: defaultOpen,\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    innerOpen = _useMergedState2[0],\n    setInnerOpen = _useMergedState2[1];\n  var mergedOpen = innerOpen; // Not trigger `open` in `combobox` when `notFoundContent` is empty\n\n  var emptyListContent = !notFoundContent && emptyOptions;\n  if (disabled || emptyListContent && mergedOpen && mode === 'combobox') {\n    mergedOpen = false;\n  }\n  var triggerOpen = emptyListContent ? false : mergedOpen;\n  var onToggleOpen = React.useCallback(function (newOpen) {\n    var nextOpen = newOpen !== undefined ? newOpen : !mergedOpen;\n    if (mergedOpen !== nextOpen && !disabled) {\n      setInnerOpen(nextOpen);\n      onDropdownVisibleChange === null || onDropdownVisibleChange === void 0 ? void 0 : onDropdownVisibleChange(nextOpen);\n    }\n  }, [disabled, mergedOpen, setInnerOpen, onDropdownVisibleChange]); // ============================= Search =============================\n\n  var tokenWithEnter = React.useMemo(function () {\n    return (tokenSeparators || []).some(function (tokenSeparator) {\n      return ['\\n', '\\r\\n'].includes(tokenSeparator);\n    });\n  }, [tokenSeparators]);\n  var onInternalSearch = function onInternalSearch(searchText, fromTyping, isCompositing) {\n    var ret = true;\n    var newSearchText = searchText;\n    onActiveValueChange === null || onActiveValueChange === void 0 ? void 0 : onActiveValueChange(null); // Check if match the `tokenSeparators`\n\n    var patchLabels = isCompositing ? null : getSeparatedContent(searchText, tokenSeparators); // Ignore combobox since it's not split-able\n\n    if (mode !== 'combobox' && patchLabels) {\n      newSearchText = '';\n      onSearchSplit === null || onSearchSplit === void 0 ? void 0 : onSearchSplit(patchLabels); // Should close when paste finish\n\n      onToggleOpen(false); // Tell Selector that break next actions\n\n      ret = false;\n    }\n    if (onSearch && mergedSearchValue !== newSearchText) {\n      onSearch(newSearchText, {\n        source: fromTyping ? 'typing' : 'effect'\n      });\n    }\n    return ret;\n  }; // Only triggered when menu is closed & mode is tags\n  // If menu is open, OptionList will take charge\n  // If mode isn't tags, press enter is not meaningful when you can't see any option\n\n  var onInternalSearchSubmit = function onInternalSearchSubmit(searchText) {\n    // prevent empty tags from appearing when you click the Enter button\n    if (!searchText || !searchText.trim()) {\n      return;\n    }\n    onSearch(searchText, {\n      source: 'submit'\n    });\n  }; // Close will clean up single mode search text\n\n  React.useEffect(function () {\n    if (!mergedOpen && !multiple && mode !== 'combobox') {\n      onInternalSearch('', false, false);\n    }\n  }, [mergedOpen]); // ============================ Disabled ============================\n  // Close dropdown & remove focus state when disabled change\n\n  React.useEffect(function () {\n    if (innerOpen && disabled) {\n      setInnerOpen(false);\n    }\n    if (disabled) {\n      setMockFocused(false);\n    }\n  }, [disabled]); // ============================ Keyboard ============================\n\n  /**\n   * We record input value here to check if can press to clean up by backspace\n   * - null: Key is not down, this is reset by key up\n   * - true: Search text is empty when first time backspace down\n   * - false: Search text is not empty when first time backspace down\n   */\n\n  var _useLock = useLock(),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getClearLock = _useLock2[0],\n    setClearLock = _useLock2[1]; // KeyDown\n\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var clearLock = getClearLock();\n    var which = event.which;\n    if (which === KeyCode.ENTER) {\n      // Do not submit form when type in the input\n      if (mode !== 'combobox') {\n        event.preventDefault();\n      } // We only manage open state here, close logic should handle by list component\n\n      if (!mergedOpen) {\n        onToggleOpen(true);\n      }\n    }\n    setClearLock(!!mergedSearchValue); // Remove value by `backspace`\n\n    if (which === KeyCode.BACKSPACE && !clearLock && multiple && !mergedSearchValue && displayValues.length) {\n      var cloneDisplayValues = _toConsumableArray(displayValues);\n      var removedDisplayValue = null;\n      for (var i = cloneDisplayValues.length - 1; i >= 0; i -= 1) {\n        var current = cloneDisplayValues[i];\n        if (!current.disabled) {\n          cloneDisplayValues.splice(i, 1);\n          removedDisplayValue = current;\n          break;\n        }\n      }\n      if (removedDisplayValue) {\n        onDisplayValuesChange(cloneDisplayValues, {\n          type: 'remove',\n          values: [removedDisplayValue]\n        });\n      }\n    }\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n    if (mergedOpen && listRef.current) {\n      var _listRef$current2;\n      (_listRef$current2 = listRef.current).onKeyDown.apply(_listRef$current2, [event].concat(rest));\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown.apply(void 0, [event].concat(rest));\n  }; // KeyUp\n\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      rest[_key2 - 1] = arguments[_key2];\n    }\n    if (mergedOpen && listRef.current) {\n      var _listRef$current3;\n      (_listRef$current3 = listRef.current).onKeyUp.apply(_listRef$current3, [event].concat(rest));\n    }\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp.apply(void 0, [event].concat(rest));\n  }; // ============================ Selector ============================\n\n  var onSelectorRemove = function onSelectorRemove(val) {\n    var newValues = displayValues.filter(function (i) {\n      return i !== val;\n    });\n    onDisplayValuesChange(newValues, {\n      type: 'remove',\n      values: [val]\n    });\n  }; // ========================== Focus / Blur ==========================\n\n  /** Record real focus status */\n\n  var focusRef = React.useRef(false);\n  var onContainerFocus = function onContainerFocus() {\n    setMockFocused(true);\n    if (!disabled) {\n      if (onFocus && !focusRef.current) {\n        onFocus.apply(void 0, arguments);\n      } // `showAction` should handle `focus` if set\n\n      if (showAction.includes('focus')) {\n        onToggleOpen(true);\n      }\n    }\n    focusRef.current = true;\n  };\n  var onContainerBlur = function onContainerBlur() {\n    setMockFocused(false, function () {\n      focusRef.current = false;\n      onToggleOpen(false);\n    });\n    if (disabled) {\n      return;\n    }\n    if (mergedSearchValue) {\n      // `tags` mode should move `searchValue` into values\n      if (mode === 'tags') {\n        onSearch(mergedSearchValue, {\n          source: 'submit'\n        });\n      } else if (mode === 'multiple') {\n        // `multiple` mode only clean the search value but not trigger event\n        onSearch('', {\n          source: 'blur'\n        });\n      }\n    }\n    if (onBlur) {\n      onBlur.apply(void 0, arguments);\n    }\n  }; // Give focus back of Select\n\n  var activeTimeoutIds = [];\n  React.useEffect(function () {\n    return function () {\n      activeTimeoutIds.forEach(function (timeoutId) {\n        return clearTimeout(timeoutId);\n      });\n      activeTimeoutIds.splice(0, activeTimeoutIds.length);\n    };\n  }, []);\n  var onInternalMouseDown = function onInternalMouseDown(event) {\n    var _triggerRef$current;\n    var target = event.target;\n    var popupElement = (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.getPopupElement(); // We should give focus back to selector if clicked item is not focusable\n\n    if (popupElement && popupElement.contains(target)) {\n      var timeoutId = setTimeout(function () {\n        var index = activeTimeoutIds.indexOf(timeoutId);\n        if (index !== -1) {\n          activeTimeoutIds.splice(index, 1);\n        }\n        cancelSetMockFocused();\n        if (!mobile && !popupElement.contains(document.activeElement)) {\n          var _selectorRef$current3;\n          (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 ? void 0 : _selectorRef$current3.focus();\n        }\n      });\n      activeTimeoutIds.push(timeoutId);\n    }\n    for (var _len3 = arguments.length, restArgs = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      restArgs[_key3 - 1] = arguments[_key3];\n    }\n    onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown.apply(void 0, [event].concat(restArgs));\n  }; // ============================ Dropdown ============================\n\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    containerWidth = _React$useState4[0],\n    setContainerWidth = _React$useState4[1];\n  var _React$useState5 = React.useState({}),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    forceUpdate = _React$useState6[1]; // We need force update here since popup dom is render async\n\n  function onPopupMouseEnter() {\n    forceUpdate({});\n  }\n  useLayoutEffect(function () {\n    if (triggerOpen) {\n      var _containerRef$current;\n      var newWidth = Math.ceil((_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.offsetWidth);\n      if (containerWidth !== newWidth && !Number.isNaN(newWidth)) {\n        setContainerWidth(newWidth);\n      }\n    }\n  }, [triggerOpen]); // Used for raw custom input trigger\n\n  var onTriggerVisibleChange;\n  if (customizeRawInputElement) {\n    onTriggerVisibleChange = function onTriggerVisibleChange(newOpen) {\n      onToggleOpen(newOpen);\n    };\n  } // Close when click on non-select element\n\n  useSelectTriggerControl(function () {\n    var _triggerRef$current2;\n    return [containerRef.current, (_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : _triggerRef$current2.getPopupElement()];\n  }, triggerOpen, onToggleOpen); // ============================ Context =============================\n\n  var baseSelectContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, props), {}, {\n      notFoundContent: notFoundContent,\n      open: mergedOpen,\n      triggerOpen: triggerOpen,\n      id: id,\n      showSearch: mergedShowSearch,\n      multiple: multiple,\n      toggleOpen: onToggleOpen\n    });\n  }, [props, notFoundContent, triggerOpen, mergedOpen, id, mergedShowSearch, multiple, onToggleOpen]); // ==================================================================\n  // ==                            Render                            ==\n  // ==================================================================\n  // ============================= Arrow ==============================\n\n  var mergedShowArrow = showArrow !== undefined ? showArrow : loading || !multiple && mode !== 'combobox';\n  var arrowNode;\n  if (mergedShowArrow) {\n    arrowNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: classNames(\"\".concat(prefixCls, \"-arrow\"), _defineProperty({}, \"\".concat(prefixCls, \"-arrow-loading\"), loading)),\n      customizeIcon: inputIcon,\n      customizeIconProps: {\n        loading: loading,\n        searchValue: mergedSearchValue,\n        open: mergedOpen,\n        focused: mockFocused,\n        showSearch: mergedShowSearch\n      }\n    });\n  } // ============================= Clear ==============================\n\n  var clearNode;\n  var onClearMouseDown = function onClearMouseDown() {\n    onClear === null || onClear === void 0 ? void 0 : onClear();\n    onDisplayValuesChange([], {\n      type: 'clear',\n      values: displayValues\n    });\n    onInternalSearch('', false, false);\n  };\n  if (!disabled && allowClear && (displayValues.length || mergedSearchValue)) {\n    clearNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: clearIcon\n    }, \"\\xD7\");\n  } // =========================== OptionList ===========================\n\n  var optionList = /*#__PURE__*/React.createElement(OptionList, {\n    ref: listRef\n  }); // ============================= Select =============================\n\n  var mergedClassName = classNames(prefixCls, className, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-focused\"), mockFocused), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-multiple\"), multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-single\"), !multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-allow-clear\"), allowClear), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-arrow\"), mergedShowArrow), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-loading\"), loading), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-open\"), mergedOpen), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-customize-input\"), customizeInputElement), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-search\"), mergedShowSearch), _classNames2)); // >>> Selector\n\n  var selectorNode = /*#__PURE__*/React.createElement(SelectTrigger, {\n    ref: triggerRef,\n    disabled: disabled,\n    prefixCls: prefixCls,\n    visible: triggerOpen,\n    popupElement: optionList,\n    containerWidth: containerWidth,\n    animation: animation,\n    transitionName: transitionName,\n    dropdownStyle: dropdownStyle,\n    dropdownClassName: dropdownClassName,\n    direction: direction,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n    dropdownRender: dropdownRender,\n    dropdownAlign: dropdownAlign,\n    placement: placement,\n    getPopupContainer: getPopupContainer,\n    empty: emptyOptions,\n    getTriggerDOMNode: function getTriggerDOMNode() {\n      return selectorDomRef.current;\n    },\n    onPopupVisibleChange: onTriggerVisibleChange,\n    onPopupMouseEnter: onPopupMouseEnter\n  }, customizeRawInputElement ? /*#__PURE__*/React.cloneElement(customizeRawInputElement, {\n    ref: customizeRawInputRef\n  }) : /*#__PURE__*/React.createElement(Selector, _extends({}, props, {\n    domRef: selectorDomRef,\n    prefixCls: prefixCls,\n    inputElement: customizeInputElement,\n    ref: selectorRef,\n    id: id,\n    showSearch: mergedShowSearch,\n    mode: mode,\n    activeDescendantId: activeDescendantId,\n    tagRender: tagRender,\n    values: displayValues,\n    open: mergedOpen,\n    onToggleOpen: onToggleOpen,\n    activeValue: activeValue,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSubmit: onInternalSearchSubmit,\n    onRemove: onSelectorRemove,\n    tokenWithEnter: tokenWithEnter\n  }))); // >>> Render\n\n  var renderNode; // Render raw\n\n  if (customizeRawInputElement) {\n    renderNode = selectorNode;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: mergedClassName\n    }, domProps, {\n      ref: containerRef,\n      onMouseDown: onInternalMouseDown,\n      onKeyDown: onInternalKeyDown,\n      onKeyUp: onInternalKeyUp,\n      onFocus: onContainerFocus,\n      onBlur: onContainerBlur\n    }), mockFocused && !mergedOpen && /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        width: 0,\n        height: 0,\n        display: 'flex',\n        overflow: 'hidden',\n        opacity: 0\n      },\n      \"aria-live\": \"polite\"\n    }, \"\".concat(displayValues.map(function (_ref) {\n      var label = _ref.label,\n        value = _ref.value;\n      return ['number', 'string'].includes(_typeof(label)) ? label : value;\n    }).join(', '))), selectorNode, arrowNode, clearNode);\n  }\n  return /*#__PURE__*/React.createElement(BaseSelectContext.Provider, {\n    value: baseSelectContext\n  }, renderNode);\n}); // Set display name for dev\n\nif (process.env.NODE_ENV !== 'production') {\n  BaseSelect.displayName = 'BaseSelect';\n}\nexport default BaseSelect;", "map": {"version": 3, "names": ["_typeof", "_extends", "_defineProperty", "_toConsumableArray", "_slicedToArray", "_objectSpread", "_objectWithoutProperties", "_excluded", "React", "classNames", "KeyCode", "isMobile", "useComposeRef", "useMergedState", "useLayoutEffect", "getSeparatedContent", "SelectTrigger", "Selector", "useSelectTriggerControl", "useDelayReset", "TransBtn", "useLock", "BaseSelectContext", "DEFAULT_OMIT_PROPS", "isMultiple", "mode", "BaseSelect", "forwardRef", "props", "ref", "_customizeRawInputEle", "_classNames2", "id", "prefixCls", "className", "showSearch", "tagRender", "direction", "omitDomProps", "displayValues", "onDisplayValuesChange", "emptyOptions", "_props$notFoundConten", "notFoundContent", "onClear", "disabled", "loading", "getInputElement", "getRawInputElement", "open", "defaultOpen", "onDropdownVisibleChange", "activeValue", "onActiveValueChange", "activeDescendantId", "searchValue", "onSearch", "onSearchSplit", "tokenSeparators", "allowClear", "showArrow", "inputIcon", "clearIcon", "OptionList", "animation", "transitionName", "dropdownStyle", "dropdownClassName", "dropdownMatchSelectWidth", "dropdownRender", "dropdownAlign", "placement", "getPopupContainer", "_props$showAction", "showAction", "onFocus", "onBlur", "onKeyUp", "onKeyDown", "onMouseDown", "restProps", "multiple", "mergedShowSearch", "undefined", "domProps", "for<PERSON>ach", "propName", "_React$useState", "useState", "_React$useState2", "mobile", "setMobile", "useEffect", "containerRef", "useRef", "selectorDomRef", "triggerRef", "selectorRef", "listRef", "_useDelayReset", "_useDelayReset2", "mockFocused", "setMockFocused", "cancelSetMockFocused", "useImperativeHandle", "_selectorRef$current", "_selectorRef$current2", "focus", "current", "blur", "scrollTo", "arg", "_listRef$current", "mergedSearchValue", "useMemo", "_displayValues$", "val", "value", "String", "customizeInputElement", "customizeRawInputElement", "customizeRawInputRef", "_useMergedState", "defaultValue", "_useMergedState2", "innerOpen", "setInnerOpen", "mergedOpen", "emptyListContent", "triggerOpen", "onToggleOpen", "useCallback", "newOpen", "nextOpen", "tokenWithEnter", "some", "tokenSeparator", "includes", "onInternalSearch", "searchText", "fromTyping", "isCompositing", "ret", "newSearchText", "patchLabels", "source", "onInternalSearchSubmit", "trim", "_useLock", "_useLock2", "getClearLock", "setClearLock", "onInternalKeyDown", "event", "clearLock", "which", "ENTER", "preventDefault", "BACKSPACE", "length", "cloneDisplayValues", "removedDisplayValue", "i", "splice", "type", "values", "_len", "arguments", "rest", "Array", "_key", "_listRef$current2", "apply", "concat", "onInternalKeyUp", "_len2", "_key2", "_listRef$current3", "onSelectorRemove", "newValues", "filter", "focusRef", "onContainerFocus", "onContainerBlur", "activeTimeoutIds", "timeoutId", "clearTimeout", "onInternalMouseDown", "_triggerRef$current", "target", "popupElement", "getPopupElement", "contains", "setTimeout", "index", "indexOf", "document", "activeElement", "_selectorRef$current3", "push", "_len3", "restArgs", "_key3", "_React$useState3", "_React$useState4", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_React$useState5", "_React$useState6", "forceUpdate", "onPopupMouseEnter", "_containerRef$current", "newWidth", "Math", "ceil", "offsetWidth", "Number", "isNaN", "onTriggerVisibleChange", "_triggerRef$current2", "baseSelectContext", "toggle<PERSON><PERSON>", "mergedShowArrow", "arrowNode", "createElement", "customizeIcon", "customizeIconProps", "focused", "clearNode", "onClearMouseDown", "optionList", "mergedClassName", "selectorNode", "visible", "empty", "getTriggerDOMNode", "onPopupVisibleChange", "cloneElement", "domRef", "inputElement", "onSearchSubmit", "onRemove", "renderNode", "style", "width", "height", "display", "overflow", "opacity", "map", "_ref", "label", "join", "Provider", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/BaseSelect.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"showSearch\", \"tagRender\", \"direction\", \"omitDomProps\", \"displayValues\", \"onDisplayValuesChange\", \"emptyOptions\", \"notFoundContent\", \"onClear\", \"mode\", \"disabled\", \"loading\", \"getInputElement\", \"getRawInputElement\", \"open\", \"defaultOpen\", \"onDropdownVisibleChange\", \"activeValue\", \"onActiveValueChange\", \"activeDescendantId\", \"searchValue\", \"onSearch\", \"onSearchSplit\", \"tokenSeparators\", \"allowClear\", \"showArrow\", \"inputIcon\", \"clearIcon\", \"OptionList\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"placement\", \"getPopupContainer\", \"showAction\", \"onFocus\", \"onBlur\", \"onKeyUp\", \"onKeyDown\", \"onMouseDown\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { getSeparatedContent } from './utils/valueUtil';\nimport SelectTrigger from './SelectTrigger';\nimport Selector from './Selector';\nimport useSelectTriggerControl from './hooks/useSelectTriggerControl';\nimport useDelayReset from './hooks/useDelayReset';\nimport TransBtn from './TransBtn';\nimport useLock from './hooks/useLock';\nimport { BaseSelectContext } from './hooks/useBaseProps';\nvar DEFAULT_OMIT_PROPS = ['value', 'onChange', 'removeIcon', 'placeholder', 'autoFocus', 'maxTagCount', 'maxTagTextLength', 'maxTagPlaceholder', 'choiceTransitionName', 'onInputKeyDown', 'onPopupScroll', 'tabIndex'];\nexport function isMultiple(mode) {\n  return mode === 'tags' || mode === 'multiple';\n}\nvar BaseSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _customizeRawInputEle, _classNames2;\n\n  var id = props.id,\n      prefixCls = props.prefixCls,\n      className = props.className,\n      showSearch = props.showSearch,\n      tagRender = props.tagRender,\n      direction = props.direction,\n      omitDomProps = props.omitDomProps,\n      displayValues = props.displayValues,\n      onDisplayValuesChange = props.onDisplayValuesChange,\n      emptyOptions = props.emptyOptions,\n      _props$notFoundConten = props.notFoundContent,\n      notFoundContent = _props$notFoundConten === void 0 ? 'Not Found' : _props$notFoundConten,\n      onClear = props.onClear,\n      mode = props.mode,\n      disabled = props.disabled,\n      loading = props.loading,\n      getInputElement = props.getInputElement,\n      getRawInputElement = props.getRawInputElement,\n      open = props.open,\n      defaultOpen = props.defaultOpen,\n      onDropdownVisibleChange = props.onDropdownVisibleChange,\n      activeValue = props.activeValue,\n      onActiveValueChange = props.onActiveValueChange,\n      activeDescendantId = props.activeDescendantId,\n      searchValue = props.searchValue,\n      onSearch = props.onSearch,\n      onSearchSplit = props.onSearchSplit,\n      tokenSeparators = props.tokenSeparators,\n      allowClear = props.allowClear,\n      showArrow = props.showArrow,\n      inputIcon = props.inputIcon,\n      clearIcon = props.clearIcon,\n      OptionList = props.OptionList,\n      animation = props.animation,\n      transitionName = props.transitionName,\n      dropdownStyle = props.dropdownStyle,\n      dropdownClassName = props.dropdownClassName,\n      dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n      dropdownRender = props.dropdownRender,\n      dropdownAlign = props.dropdownAlign,\n      placement = props.placement,\n      getPopupContainer = props.getPopupContainer,\n      _props$showAction = props.showAction,\n      showAction = _props$showAction === void 0 ? [] : _props$showAction,\n      onFocus = props.onFocus,\n      onBlur = props.onBlur,\n      onKeyUp = props.onKeyUp,\n      onKeyDown = props.onKeyDown,\n      onMouseDown = props.onMouseDown,\n      restProps = _objectWithoutProperties(props, _excluded); // ============================== MISC ==============================\n\n\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = (showSearch !== undefined ? showSearch : multiple) || mode === 'combobox';\n\n  var domProps = _objectSpread({}, restProps);\n\n  DEFAULT_OMIT_PROPS.forEach(function (propName) {\n    delete domProps[propName];\n  });\n  omitDomProps === null || omitDomProps === void 0 ? void 0 : omitDomProps.forEach(function (propName) {\n    delete domProps[propName];\n  }); // ============================= Mobile =============================\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      mobile = _React$useState2[0],\n      setMobile = _React$useState2[1];\n\n  React.useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []); // ============================== Refs ==============================\n\n  var containerRef = React.useRef(null);\n  var selectorDomRef = React.useRef(null);\n  var triggerRef = React.useRef(null);\n  var selectorRef = React.useRef(null);\n  var listRef = React.useRef(null);\n  /** Used for component focused management */\n\n  var _useDelayReset = useDelayReset(),\n      _useDelayReset2 = _slicedToArray(_useDelayReset, 3),\n      mockFocused = _useDelayReset2[0],\n      setMockFocused = _useDelayReset2[1],\n      cancelSetMockFocused = _useDelayReset2[2]; // =========================== Imperative ===========================\n\n\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current, _selectorRef$current2;\n\n    return {\n      focus: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.focus,\n      blur: (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 ? void 0 : _selectorRef$current2.blur,\n      scrollTo: function scrollTo(arg) {\n        var _listRef$current;\n\n        return (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(arg);\n      }\n    };\n  }); // ========================== Search Value ==========================\n\n  var mergedSearchValue = React.useMemo(function () {\n    var _displayValues$;\n\n    if (mode !== 'combobox') {\n      return searchValue;\n    }\n\n    var val = (_displayValues$ = displayValues[0]) === null || _displayValues$ === void 0 ? void 0 : _displayValues$.value;\n    return typeof val === 'string' || typeof val === 'number' ? String(val) : '';\n  }, [searchValue, mode, displayValues]); // ========================== Custom Input ==========================\n  // Only works in `combobox`\n\n  var customizeInputElement = mode === 'combobox' && typeof getInputElement === 'function' && getInputElement() || null; // Used for customize replacement for `rc-cascader`\n\n  var customizeRawInputElement = typeof getRawInputElement === 'function' && getRawInputElement();\n  var customizeRawInputRef = useComposeRef(selectorDomRef, customizeRawInputElement === null || customizeRawInputElement === void 0 ? void 0 : (_customizeRawInputEle = customizeRawInputElement.props) === null || _customizeRawInputEle === void 0 ? void 0 : _customizeRawInputEle.ref); // ============================== Open ==============================\n\n  var _useMergedState = useMergedState(undefined, {\n    defaultValue: defaultOpen,\n    value: open\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      innerOpen = _useMergedState2[0],\n      setInnerOpen = _useMergedState2[1];\n\n  var mergedOpen = innerOpen; // Not trigger `open` in `combobox` when `notFoundContent` is empty\n\n  var emptyListContent = !notFoundContent && emptyOptions;\n\n  if (disabled || emptyListContent && mergedOpen && mode === 'combobox') {\n    mergedOpen = false;\n  }\n\n  var triggerOpen = emptyListContent ? false : mergedOpen;\n  var onToggleOpen = React.useCallback(function (newOpen) {\n    var nextOpen = newOpen !== undefined ? newOpen : !mergedOpen;\n\n    if (mergedOpen !== nextOpen && !disabled) {\n      setInnerOpen(nextOpen);\n      onDropdownVisibleChange === null || onDropdownVisibleChange === void 0 ? void 0 : onDropdownVisibleChange(nextOpen);\n    }\n  }, [disabled, mergedOpen, setInnerOpen, onDropdownVisibleChange]); // ============================= Search =============================\n\n  var tokenWithEnter = React.useMemo(function () {\n    return (tokenSeparators || []).some(function (tokenSeparator) {\n      return ['\\n', '\\r\\n'].includes(tokenSeparator);\n    });\n  }, [tokenSeparators]);\n\n  var onInternalSearch = function onInternalSearch(searchText, fromTyping, isCompositing) {\n    var ret = true;\n    var newSearchText = searchText;\n    onActiveValueChange === null || onActiveValueChange === void 0 ? void 0 : onActiveValueChange(null); // Check if match the `tokenSeparators`\n\n    var patchLabels = isCompositing ? null : getSeparatedContent(searchText, tokenSeparators); // Ignore combobox since it's not split-able\n\n    if (mode !== 'combobox' && patchLabels) {\n      newSearchText = '';\n      onSearchSplit === null || onSearchSplit === void 0 ? void 0 : onSearchSplit(patchLabels); // Should close when paste finish\n\n      onToggleOpen(false); // Tell Selector that break next actions\n\n      ret = false;\n    }\n\n    if (onSearch && mergedSearchValue !== newSearchText) {\n      onSearch(newSearchText, {\n        source: fromTyping ? 'typing' : 'effect'\n      });\n    }\n\n    return ret;\n  }; // Only triggered when menu is closed & mode is tags\n  // If menu is open, OptionList will take charge\n  // If mode isn't tags, press enter is not meaningful when you can't see any option\n\n\n  var onInternalSearchSubmit = function onInternalSearchSubmit(searchText) {\n    // prevent empty tags from appearing when you click the Enter button\n    if (!searchText || !searchText.trim()) {\n      return;\n    }\n\n    onSearch(searchText, {\n      source: 'submit'\n    });\n  }; // Close will clean up single mode search text\n\n\n  React.useEffect(function () {\n    if (!mergedOpen && !multiple && mode !== 'combobox') {\n      onInternalSearch('', false, false);\n    }\n  }, [mergedOpen]); // ============================ Disabled ============================\n  // Close dropdown & remove focus state when disabled change\n\n  React.useEffect(function () {\n    if (innerOpen && disabled) {\n      setInnerOpen(false);\n    }\n\n    if (disabled) {\n      setMockFocused(false);\n    }\n  }, [disabled]); // ============================ Keyboard ============================\n\n  /**\n   * We record input value here to check if can press to clean up by backspace\n   * - null: Key is not down, this is reset by key up\n   * - true: Search text is empty when first time backspace down\n   * - false: Search text is not empty when first time backspace down\n   */\n\n  var _useLock = useLock(),\n      _useLock2 = _slicedToArray(_useLock, 2),\n      getClearLock = _useLock2[0],\n      setClearLock = _useLock2[1]; // KeyDown\n\n\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var clearLock = getClearLock();\n    var which = event.which;\n\n    if (which === KeyCode.ENTER) {\n      // Do not submit form when type in the input\n      if (mode !== 'combobox') {\n        event.preventDefault();\n      } // We only manage open state here, close logic should handle by list component\n\n\n      if (!mergedOpen) {\n        onToggleOpen(true);\n      }\n    }\n\n    setClearLock(!!mergedSearchValue); // Remove value by `backspace`\n\n    if (which === KeyCode.BACKSPACE && !clearLock && multiple && !mergedSearchValue && displayValues.length) {\n      var cloneDisplayValues = _toConsumableArray(displayValues);\n\n      var removedDisplayValue = null;\n\n      for (var i = cloneDisplayValues.length - 1; i >= 0; i -= 1) {\n        var current = cloneDisplayValues[i];\n\n        if (!current.disabled) {\n          cloneDisplayValues.splice(i, 1);\n          removedDisplayValue = current;\n          break;\n        }\n      }\n\n      if (removedDisplayValue) {\n        onDisplayValuesChange(cloneDisplayValues, {\n          type: 'remove',\n          values: [removedDisplayValue]\n        });\n      }\n    }\n\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n\n    if (mergedOpen && listRef.current) {\n      var _listRef$current2;\n\n      (_listRef$current2 = listRef.current).onKeyDown.apply(_listRef$current2, [event].concat(rest));\n    }\n\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown.apply(void 0, [event].concat(rest));\n  }; // KeyUp\n\n\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      rest[_key2 - 1] = arguments[_key2];\n    }\n\n    if (mergedOpen && listRef.current) {\n      var _listRef$current3;\n\n      (_listRef$current3 = listRef.current).onKeyUp.apply(_listRef$current3, [event].concat(rest));\n    }\n\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp.apply(void 0, [event].concat(rest));\n  }; // ============================ Selector ============================\n\n\n  var onSelectorRemove = function onSelectorRemove(val) {\n    var newValues = displayValues.filter(function (i) {\n      return i !== val;\n    });\n    onDisplayValuesChange(newValues, {\n      type: 'remove',\n      values: [val]\n    });\n  }; // ========================== Focus / Blur ==========================\n\n  /** Record real focus status */\n\n\n  var focusRef = React.useRef(false);\n\n  var onContainerFocus = function onContainerFocus() {\n    setMockFocused(true);\n\n    if (!disabled) {\n      if (onFocus && !focusRef.current) {\n        onFocus.apply(void 0, arguments);\n      } // `showAction` should handle `focus` if set\n\n\n      if (showAction.includes('focus')) {\n        onToggleOpen(true);\n      }\n    }\n\n    focusRef.current = true;\n  };\n\n  var onContainerBlur = function onContainerBlur() {\n    setMockFocused(false, function () {\n      focusRef.current = false;\n      onToggleOpen(false);\n    });\n\n    if (disabled) {\n      return;\n    }\n\n    if (mergedSearchValue) {\n      // `tags` mode should move `searchValue` into values\n      if (mode === 'tags') {\n        onSearch(mergedSearchValue, {\n          source: 'submit'\n        });\n      } else if (mode === 'multiple') {\n        // `multiple` mode only clean the search value but not trigger event\n        onSearch('', {\n          source: 'blur'\n        });\n      }\n    }\n\n    if (onBlur) {\n      onBlur.apply(void 0, arguments);\n    }\n  }; // Give focus back of Select\n\n\n  var activeTimeoutIds = [];\n  React.useEffect(function () {\n    return function () {\n      activeTimeoutIds.forEach(function (timeoutId) {\n        return clearTimeout(timeoutId);\n      });\n      activeTimeoutIds.splice(0, activeTimeoutIds.length);\n    };\n  }, []);\n\n  var onInternalMouseDown = function onInternalMouseDown(event) {\n    var _triggerRef$current;\n\n    var target = event.target;\n    var popupElement = (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.getPopupElement(); // We should give focus back to selector if clicked item is not focusable\n\n    if (popupElement && popupElement.contains(target)) {\n      var timeoutId = setTimeout(function () {\n        var index = activeTimeoutIds.indexOf(timeoutId);\n\n        if (index !== -1) {\n          activeTimeoutIds.splice(index, 1);\n        }\n\n        cancelSetMockFocused();\n\n        if (!mobile && !popupElement.contains(document.activeElement)) {\n          var _selectorRef$current3;\n\n          (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 ? void 0 : _selectorRef$current3.focus();\n        }\n      });\n      activeTimeoutIds.push(timeoutId);\n    }\n\n    for (var _len3 = arguments.length, restArgs = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      restArgs[_key3 - 1] = arguments[_key3];\n    }\n\n    onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown.apply(void 0, [event].concat(restArgs));\n  }; // ============================ Dropdown ============================\n\n\n  var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      containerWidth = _React$useState4[0],\n      setContainerWidth = _React$useState4[1];\n\n  var _React$useState5 = React.useState({}),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      forceUpdate = _React$useState6[1]; // We need force update here since popup dom is render async\n\n\n  function onPopupMouseEnter() {\n    forceUpdate({});\n  }\n\n  useLayoutEffect(function () {\n    if (triggerOpen) {\n      var _containerRef$current;\n\n      var newWidth = Math.ceil((_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.offsetWidth);\n\n      if (containerWidth !== newWidth && !Number.isNaN(newWidth)) {\n        setContainerWidth(newWidth);\n      }\n    }\n  }, [triggerOpen]); // Used for raw custom input trigger\n\n  var onTriggerVisibleChange;\n\n  if (customizeRawInputElement) {\n    onTriggerVisibleChange = function onTriggerVisibleChange(newOpen) {\n      onToggleOpen(newOpen);\n    };\n  } // Close when click on non-select element\n\n\n  useSelectTriggerControl(function () {\n    var _triggerRef$current2;\n\n    return [containerRef.current, (_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : _triggerRef$current2.getPopupElement()];\n  }, triggerOpen, onToggleOpen); // ============================ Context =============================\n\n  var baseSelectContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, props), {}, {\n      notFoundContent: notFoundContent,\n      open: mergedOpen,\n      triggerOpen: triggerOpen,\n      id: id,\n      showSearch: mergedShowSearch,\n      multiple: multiple,\n      toggleOpen: onToggleOpen\n    });\n  }, [props, notFoundContent, triggerOpen, mergedOpen, id, mergedShowSearch, multiple, onToggleOpen]); // ==================================================================\n  // ==                            Render                            ==\n  // ==================================================================\n  // ============================= Arrow ==============================\n\n  var mergedShowArrow = showArrow !== undefined ? showArrow : loading || !multiple && mode !== 'combobox';\n  var arrowNode;\n\n  if (mergedShowArrow) {\n    arrowNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: classNames(\"\".concat(prefixCls, \"-arrow\"), _defineProperty({}, \"\".concat(prefixCls, \"-arrow-loading\"), loading)),\n      customizeIcon: inputIcon,\n      customizeIconProps: {\n        loading: loading,\n        searchValue: mergedSearchValue,\n        open: mergedOpen,\n        focused: mockFocused,\n        showSearch: mergedShowSearch\n      }\n    });\n  } // ============================= Clear ==============================\n\n\n  var clearNode;\n\n  var onClearMouseDown = function onClearMouseDown() {\n    onClear === null || onClear === void 0 ? void 0 : onClear();\n    onDisplayValuesChange([], {\n      type: 'clear',\n      values: displayValues\n    });\n    onInternalSearch('', false, false);\n  };\n\n  if (!disabled && allowClear && (displayValues.length || mergedSearchValue)) {\n    clearNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: clearIcon\n    }, \"\\xD7\");\n  } // =========================== OptionList ===========================\n\n\n  var optionList = /*#__PURE__*/React.createElement(OptionList, {\n    ref: listRef\n  }); // ============================= Select =============================\n\n  var mergedClassName = classNames(prefixCls, className, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-focused\"), mockFocused), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-multiple\"), multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-single\"), !multiple), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-allow-clear\"), allowClear), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-arrow\"), mergedShowArrow), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-loading\"), loading), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-open\"), mergedOpen), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-customize-input\"), customizeInputElement), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-show-search\"), mergedShowSearch), _classNames2)); // >>> Selector\n\n  var selectorNode = /*#__PURE__*/React.createElement(SelectTrigger, {\n    ref: triggerRef,\n    disabled: disabled,\n    prefixCls: prefixCls,\n    visible: triggerOpen,\n    popupElement: optionList,\n    containerWidth: containerWidth,\n    animation: animation,\n    transitionName: transitionName,\n    dropdownStyle: dropdownStyle,\n    dropdownClassName: dropdownClassName,\n    direction: direction,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n    dropdownRender: dropdownRender,\n    dropdownAlign: dropdownAlign,\n    placement: placement,\n    getPopupContainer: getPopupContainer,\n    empty: emptyOptions,\n    getTriggerDOMNode: function getTriggerDOMNode() {\n      return selectorDomRef.current;\n    },\n    onPopupVisibleChange: onTriggerVisibleChange,\n    onPopupMouseEnter: onPopupMouseEnter\n  }, customizeRawInputElement ? /*#__PURE__*/React.cloneElement(customizeRawInputElement, {\n    ref: customizeRawInputRef\n  }) : /*#__PURE__*/React.createElement(Selector, _extends({}, props, {\n    domRef: selectorDomRef,\n    prefixCls: prefixCls,\n    inputElement: customizeInputElement,\n    ref: selectorRef,\n    id: id,\n    showSearch: mergedShowSearch,\n    mode: mode,\n    activeDescendantId: activeDescendantId,\n    tagRender: tagRender,\n    values: displayValues,\n    open: mergedOpen,\n    onToggleOpen: onToggleOpen,\n    activeValue: activeValue,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSubmit: onInternalSearchSubmit,\n    onRemove: onSelectorRemove,\n    tokenWithEnter: tokenWithEnter\n  }))); // >>> Render\n\n  var renderNode; // Render raw\n\n  if (customizeRawInputElement) {\n    renderNode = selectorNode;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: mergedClassName\n    }, domProps, {\n      ref: containerRef,\n      onMouseDown: onInternalMouseDown,\n      onKeyDown: onInternalKeyDown,\n      onKeyUp: onInternalKeyUp,\n      onFocus: onContainerFocus,\n      onBlur: onContainerBlur\n    }), mockFocused && !mergedOpen && /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        width: 0,\n        height: 0,\n        display: 'flex',\n        overflow: 'hidden',\n        opacity: 0\n      },\n      \"aria-live\": \"polite\"\n    }, \"\".concat(displayValues.map(function (_ref) {\n      var label = _ref.label,\n          value = _ref.value;\n      return ['number', 'string'].includes(_typeof(label)) ? label : value;\n    }).join(', '))), selectorNode, arrowNode, clearNode);\n  }\n\n  return /*#__PURE__*/React.createElement(BaseSelectContext.Provider, {\n    value: baseSelectContext\n  }, renderNode);\n}); // Set display name for dev\n\nif (process.env.NODE_ENV !== 'production') {\n  BaseSelect.displayName = 'BaseSelect';\n}\n\nexport default BaseSelect;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,eAAe,EAAE,uBAAuB,EAAE,cAAc,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,EAAE,aAAa,EAAE,yBAAyB,EAAE,aAAa,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,EAAE,mBAAmB,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC;AACxuB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,IAAIC,kBAAkB,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,eAAe,EAAE,UAAU,CAAC;AACvN,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU;AAC/C;AACA,IAAIC,UAAU,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,qBAAqB,EAAEC,YAAY;EAEvC,IAAIC,EAAE,GAAGJ,KAAK,CAACI,EAAE;IACbC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,YAAY,GAAGV,KAAK,CAACU,YAAY;IACjCC,aAAa,GAAGX,KAAK,CAACW,aAAa;IACnCC,qBAAqB,GAAGZ,KAAK,CAACY,qBAAqB;IACnDC,YAAY,GAAGb,KAAK,CAACa,YAAY;IACjCC,qBAAqB,GAAGd,KAAK,CAACe,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,qBAAqB;IACxFE,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBnB,IAAI,GAAGG,KAAK,CAACH,IAAI;IACjBoB,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,eAAe,GAAGnB,KAAK,CAACmB,eAAe;IACvCC,kBAAkB,GAAGpB,KAAK,CAACoB,kBAAkB;IAC7CC,IAAI,GAAGrB,KAAK,CAACqB,IAAI;IACjBC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,uBAAuB,GAAGvB,KAAK,CAACuB,uBAAuB;IACvDC,WAAW,GAAGxB,KAAK,CAACwB,WAAW;IAC/BC,mBAAmB,GAAGzB,KAAK,CAACyB,mBAAmB;IAC/CC,kBAAkB,GAAG1B,KAAK,CAAC0B,kBAAkB;IAC7CC,WAAW,GAAG3B,KAAK,CAAC2B,WAAW;IAC/BC,QAAQ,GAAG5B,KAAK,CAAC4B,QAAQ;IACzBC,aAAa,GAAG7B,KAAK,CAAC6B,aAAa;IACnCC,eAAe,GAAG9B,KAAK,CAAC8B,eAAe;IACvCC,UAAU,GAAG/B,KAAK,CAAC+B,UAAU;IAC7BC,SAAS,GAAGhC,KAAK,CAACgC,SAAS;IAC3BC,SAAS,GAAGjC,KAAK,CAACiC,SAAS;IAC3BC,SAAS,GAAGlC,KAAK,CAACkC,SAAS;IAC3BC,UAAU,GAAGnC,KAAK,CAACmC,UAAU;IAC7BC,SAAS,GAAGpC,KAAK,CAACoC,SAAS;IAC3BC,cAAc,GAAGrC,KAAK,CAACqC,cAAc;IACrCC,aAAa,GAAGtC,KAAK,CAACsC,aAAa;IACnCC,iBAAiB,GAAGvC,KAAK,CAACuC,iBAAiB;IAC3CC,wBAAwB,GAAGxC,KAAK,CAACwC,wBAAwB;IACzDC,cAAc,GAAGzC,KAAK,CAACyC,cAAc;IACrCC,aAAa,GAAG1C,KAAK,CAAC0C,aAAa;IACnCC,SAAS,GAAG3C,KAAK,CAAC2C,SAAS;IAC3BC,iBAAiB,GAAG5C,KAAK,CAAC4C,iBAAiB;IAC3CC,iBAAiB,GAAG7C,KAAK,CAAC8C,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,iBAAiB;IAClEE,OAAO,GAAG/C,KAAK,CAAC+C,OAAO;IACvBC,MAAM,GAAGhD,KAAK,CAACgD,MAAM;IACrBC,OAAO,GAAGjD,KAAK,CAACiD,OAAO;IACvBC,SAAS,GAAGlD,KAAK,CAACkD,SAAS;IAC3BC,WAAW,GAAGnD,KAAK,CAACmD,WAAW;IAC/BC,SAAS,GAAG1E,wBAAwB,CAACsB,KAAK,EAAErB,SAAS,CAAC,CAAC,CAAC;;EAG5D,IAAI0E,QAAQ,GAAGzD,UAAU,CAACC,IAAI,CAAC;EAC/B,IAAIyD,gBAAgB,GAAG,CAAC/C,UAAU,KAAKgD,SAAS,GAAGhD,UAAU,GAAG8C,QAAQ,KAAKxD,IAAI,KAAK,UAAU;EAEhG,IAAI2D,QAAQ,GAAG/E,aAAa,CAAC,CAAC,CAAC,EAAE2E,SAAS,CAAC;EAE3CzD,kBAAkB,CAAC8D,OAAO,CAAC,UAAUC,QAAQ,EAAE;IAC7C,OAAOF,QAAQ,CAACE,QAAQ,CAAC;EAC3B,CAAC,CAAC;EACFhD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC+C,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACnG,OAAOF,QAAQ,CAACE,QAAQ,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,eAAe,GAAG/E,KAAK,CAACgF,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAGrF,cAAc,CAACmF,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEnCjF,KAAK,CAACoF,SAAS,CAAC,YAAY;IAC1B;IACAD,SAAS,CAAChF,QAAQ,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIkF,YAAY,GAAGrF,KAAK,CAACsF,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIC,cAAc,GAAGvF,KAAK,CAACsF,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIE,UAAU,GAAGxF,KAAK,CAACsF,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIG,WAAW,GAAGzF,KAAK,CAACsF,MAAM,CAAC,IAAI,CAAC;EACpC,IAAII,OAAO,GAAG1F,KAAK,CAACsF,MAAM,CAAC,IAAI,CAAC;EAChC;;EAEA,IAAIK,cAAc,GAAGhF,aAAa,CAAC,CAAC;IAChCiF,eAAe,GAAGhG,cAAc,CAAC+F,cAAc,EAAE,CAAC,CAAC;IACnDE,WAAW,GAAGD,eAAe,CAAC,CAAC,CAAC;IAChCE,cAAc,GAAGF,eAAe,CAAC,CAAC,CAAC;IACnCG,oBAAoB,GAAGH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/C5F,KAAK,CAACgG,mBAAmB,CAAC3E,GAAG,EAAE,YAAY;IACzC,IAAI4E,oBAAoB,EAAEC,qBAAqB;IAE/C,OAAO;MACLC,KAAK,EAAE,CAACF,oBAAoB,GAAGR,WAAW,CAACW,OAAO,MAAM,IAAI,IAAIH,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACE,KAAK;MACrIE,IAAI,EAAE,CAACH,qBAAqB,GAAGT,WAAW,CAACW,OAAO,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,IAAI;MACtIC,QAAQ,EAAE,SAASA,QAAQA,CAACC,GAAG,EAAE;QAC/B,IAAIC,gBAAgB;QAEpB,OAAO,CAACA,gBAAgB,GAAGd,OAAO,CAACU,OAAO,MAAM,IAAI,IAAII,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACF,QAAQ,CAACC,GAAG,CAAC;MAC/H;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIE,iBAAiB,GAAGzG,KAAK,CAAC0G,OAAO,CAAC,YAAY;IAChD,IAAIC,eAAe;IAEnB,IAAI1F,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO8B,WAAW;IACpB;IAEA,IAAI6D,GAAG,GAAG,CAACD,eAAe,GAAG5E,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI4E,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACE,KAAK;IACtH,OAAO,OAAOD,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAGE,MAAM,CAACF,GAAG,CAAC,GAAG,EAAE;EAC9E,CAAC,EAAE,CAAC7D,WAAW,EAAE9B,IAAI,EAAEc,aAAa,CAAC,CAAC,CAAC,CAAC;EACxC;;EAEA,IAAIgF,qBAAqB,GAAG9F,IAAI,KAAK,UAAU,IAAI,OAAOsB,eAAe,KAAK,UAAU,IAAIA,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;;EAEvH,IAAIyE,wBAAwB,GAAG,OAAOxE,kBAAkB,KAAK,UAAU,IAAIA,kBAAkB,CAAC,CAAC;EAC/F,IAAIyE,oBAAoB,GAAG7G,aAAa,CAACmF,cAAc,EAAEyB,wBAAwB,KAAK,IAAI,IAAIA,wBAAwB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC1F,qBAAqB,GAAG0F,wBAAwB,CAAC5F,KAAK,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,GAAG,CAAC,CAAC,CAAC;;EAE1R,IAAI6F,eAAe,GAAG7G,cAAc,CAACsE,SAAS,EAAE;MAC9CwC,YAAY,EAAEzE,WAAW;MACzBmE,KAAK,EAAEpE;IACT,CAAC,CAAC;IACE2E,gBAAgB,GAAGxH,cAAc,CAACsH,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,UAAU,GAAGF,SAAS,CAAC,CAAC;;EAE5B,IAAIG,gBAAgB,GAAG,CAACrF,eAAe,IAAIF,YAAY;EAEvD,IAAII,QAAQ,IAAImF,gBAAgB,IAAID,UAAU,IAAItG,IAAI,KAAK,UAAU,EAAE;IACrEsG,UAAU,GAAG,KAAK;EACpB;EAEA,IAAIE,WAAW,GAAGD,gBAAgB,GAAG,KAAK,GAAGD,UAAU;EACvD,IAAIG,YAAY,GAAG1H,KAAK,CAAC2H,WAAW,CAAC,UAAUC,OAAO,EAAE;IACtD,IAAIC,QAAQ,GAAGD,OAAO,KAAKjD,SAAS,GAAGiD,OAAO,GAAG,CAACL,UAAU;IAE5D,IAAIA,UAAU,KAAKM,QAAQ,IAAI,CAACxF,QAAQ,EAAE;MACxCiF,YAAY,CAACO,QAAQ,CAAC;MACtBlF,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACkF,QAAQ,CAAC;IACrH;EACF,CAAC,EAAE,CAACxF,QAAQ,EAAEkF,UAAU,EAAED,YAAY,EAAE3E,uBAAuB,CAAC,CAAC,CAAC,CAAC;;EAEnE,IAAImF,cAAc,GAAG9H,KAAK,CAAC0G,OAAO,CAAC,YAAY;IAC7C,OAAO,CAACxD,eAAe,IAAI,EAAE,EAAE6E,IAAI,CAAC,UAAUC,cAAc,EAAE;MAC5D,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,cAAc,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9E,eAAe,CAAC,CAAC;EAErB,IAAIgF,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAE;IACtF,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,aAAa,GAAGJ,UAAU;IAC9BtF,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;IAErG,IAAI2F,WAAW,GAAGH,aAAa,GAAG,IAAI,GAAG9H,mBAAmB,CAAC4H,UAAU,EAAEjF,eAAe,CAAC,CAAC,CAAC;;IAE3F,IAAIjC,IAAI,KAAK,UAAU,IAAIuH,WAAW,EAAE;MACtCD,aAAa,GAAG,EAAE;MAClBtF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACuF,WAAW,CAAC,CAAC,CAAC;;MAE1Fd,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;;MAErBY,GAAG,GAAG,KAAK;IACb;IAEA,IAAItF,QAAQ,IAAIyD,iBAAiB,KAAK8B,aAAa,EAAE;MACnDvF,QAAQ,CAACuF,aAAa,EAAE;QACtBE,MAAM,EAAEL,UAAU,GAAG,QAAQ,GAAG;MAClC,CAAC,CAAC;IACJ;IAEA,OAAOE,GAAG;EACZ,CAAC,CAAC,CAAC;EACH;EACA;;EAGA,IAAII,sBAAsB,GAAG,SAASA,sBAAsBA,CAACP,UAAU,EAAE;IACvE;IACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAE;MACrC;IACF;IAEA3F,QAAQ,CAACmF,UAAU,EAAE;MACnBM,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGHzI,KAAK,CAACoF,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACmC,UAAU,IAAI,CAAC9C,QAAQ,IAAIxD,IAAI,KAAK,UAAU,EAAE;MACnDiH,gBAAgB,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAACX,UAAU,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEAvH,KAAK,CAACoF,SAAS,CAAC,YAAY;IAC1B,IAAIiC,SAAS,IAAIhF,QAAQ,EAAE;MACzBiF,YAAY,CAAC,KAAK,CAAC;IACrB;IAEA,IAAIjF,QAAQ,EAAE;MACZyD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACzD,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB;AACF;AACA;AACA;AACA;AACA;;EAEE,IAAIuG,QAAQ,GAAG/H,OAAO,CAAC,CAAC;IACpBgI,SAAS,GAAGjJ,cAAc,CAACgJ,QAAQ,EAAE,CAAC,CAAC;IACvCE,YAAY,GAAGD,SAAS,CAAC,CAAC,CAAC;IAC3BE,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGjC,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAIC,SAAS,GAAGJ,YAAY,CAAC,CAAC;IAC9B,IAAIK,KAAK,GAAGF,KAAK,CAACE,KAAK;IAEvB,IAAIA,KAAK,KAAKjJ,OAAO,CAACkJ,KAAK,EAAE;MAC3B;MACA,IAAInI,IAAI,KAAK,UAAU,EAAE;QACvBgI,KAAK,CAACI,cAAc,CAAC,CAAC;MACxB,CAAC,CAAC;;MAGF,IAAI,CAAC9B,UAAU,EAAE;QACfG,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;IAEAqB,YAAY,CAAC,CAAC,CAACtC,iBAAiB,CAAC,CAAC,CAAC;;IAEnC,IAAI0C,KAAK,KAAKjJ,OAAO,CAACoJ,SAAS,IAAI,CAACJ,SAAS,IAAIzE,QAAQ,IAAI,CAACgC,iBAAiB,IAAI1E,aAAa,CAACwH,MAAM,EAAE;MACvG,IAAIC,kBAAkB,GAAG7J,kBAAkB,CAACoC,aAAa,CAAC;MAE1D,IAAI0H,mBAAmB,GAAG,IAAI;MAE9B,KAAK,IAAIC,CAAC,GAAGF,kBAAkB,CAACD,MAAM,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAItD,OAAO,GAAGoD,kBAAkB,CAACE,CAAC,CAAC;QAEnC,IAAI,CAACtD,OAAO,CAAC/D,QAAQ,EAAE;UACrBmH,kBAAkB,CAACG,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UAC/BD,mBAAmB,GAAGrD,OAAO;UAC7B;QACF;MACF;MAEA,IAAIqD,mBAAmB,EAAE;QACvBzH,qBAAqB,CAACwH,kBAAkB,EAAE;UACxCI,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,CAACJ,mBAAmB;QAC9B,CAAC,CAAC;MACJ;IACF;IAEA,KAAK,IAAIK,IAAI,GAAGC,SAAS,CAACR,MAAM,EAAES,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAClC;IAEA,IAAI3C,UAAU,IAAI7B,OAAO,CAACU,OAAO,EAAE;MACjC,IAAI+D,iBAAiB;MAErB,CAACA,iBAAiB,GAAGzE,OAAO,CAACU,OAAO,EAAE9B,SAAS,CAAC8F,KAAK,CAACD,iBAAiB,EAAE,CAAClB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;IAChG;IAEA1F,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC8F,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;EACrG,CAAC,CAAC,CAAC;;EAGH,IAAIM,eAAe,GAAG,SAASA,eAAeA,CAACrB,KAAK,EAAE;IACpD,KAAK,IAAIsB,KAAK,GAAGR,SAAS,CAACR,MAAM,EAAES,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MACjHR,IAAI,CAACQ,KAAK,GAAG,CAAC,CAAC,GAAGT,SAAS,CAACS,KAAK,CAAC;IACpC;IAEA,IAAIjD,UAAU,IAAI7B,OAAO,CAACU,OAAO,EAAE;MACjC,IAAIqE,iBAAiB;MAErB,CAACA,iBAAiB,GAAG/E,OAAO,CAACU,OAAO,EAAE/B,OAAO,CAAC+F,KAAK,CAACK,iBAAiB,EAAE,CAACxB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;IAC9F;IAEA3F,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC+F,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnB,KAAK,CAAC,CAACoB,MAAM,CAACL,IAAI,CAAC,CAAC;EAC/F,CAAC,CAAC,CAAC;;EAGH,IAAIU,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC9D,GAAG,EAAE;IACpD,IAAI+D,SAAS,GAAG5I,aAAa,CAAC6I,MAAM,CAAC,UAAUlB,CAAC,EAAE;MAChD,OAAOA,CAAC,KAAK9C,GAAG;IAClB,CAAC,CAAC;IACF5E,qBAAqB,CAAC2I,SAAS,EAAE;MAC/Bf,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,CAACjD,GAAG;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAEH;;EAGA,IAAIiE,QAAQ,GAAG7K,KAAK,CAACsF,MAAM,CAAC,KAAK,CAAC;EAElC,IAAIwF,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDhF,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI,CAACzD,QAAQ,EAAE;MACb,IAAI8B,OAAO,IAAI,CAAC0G,QAAQ,CAACzE,OAAO,EAAE;QAChCjC,OAAO,CAACiG,KAAK,CAAC,KAAK,CAAC,EAAEL,SAAS,CAAC;MAClC,CAAC,CAAC;;MAGF,IAAI7F,UAAU,CAAC+D,QAAQ,CAAC,OAAO,CAAC,EAAE;QAChCP,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;IAEAmD,QAAQ,CAACzE,OAAO,GAAG,IAAI;EACzB,CAAC;EAED,IAAI2E,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CjF,cAAc,CAAC,KAAK,EAAE,YAAY;MAChC+E,QAAQ,CAACzE,OAAO,GAAG,KAAK;MACxBsB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;IAEF,IAAIrF,QAAQ,EAAE;MACZ;IACF;IAEA,IAAIoE,iBAAiB,EAAE;MACrB;MACA,IAAIxF,IAAI,KAAK,MAAM,EAAE;QACnB+B,QAAQ,CAACyD,iBAAiB,EAAE;UAC1BgC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIxH,IAAI,KAAK,UAAU,EAAE;QAC9B;QACA+B,QAAQ,CAAC,EAAE,EAAE;UACXyF,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;IAEA,IAAIrE,MAAM,EAAE;MACVA,MAAM,CAACgG,KAAK,CAAC,KAAK,CAAC,EAAEL,SAAS,CAAC;IACjC;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIiB,gBAAgB,GAAG,EAAE;EACzBhL,KAAK,CAACoF,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB4F,gBAAgB,CAACnG,OAAO,CAAC,UAAUoG,SAAS,EAAE;QAC5C,OAAOC,YAAY,CAACD,SAAS,CAAC;MAChC,CAAC,CAAC;MACFD,gBAAgB,CAACrB,MAAM,CAAC,CAAC,EAAEqB,gBAAgB,CAACzB,MAAM,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI4B,mBAAmB,GAAG,SAASA,mBAAmBA,CAAClC,KAAK,EAAE;IAC5D,IAAImC,mBAAmB;IAEvB,IAAIC,MAAM,GAAGpC,KAAK,CAACoC,MAAM;IACzB,IAAIC,YAAY,GAAG,CAACF,mBAAmB,GAAG5F,UAAU,CAACY,OAAO,MAAM,IAAI,IAAIgF,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC;;IAE3J,IAAID,YAAY,IAAIA,YAAY,CAACE,QAAQ,CAACH,MAAM,CAAC,EAAE;MACjD,IAAIJ,SAAS,GAAGQ,UAAU,CAAC,YAAY;QACrC,IAAIC,KAAK,GAAGV,gBAAgB,CAACW,OAAO,CAACV,SAAS,CAAC;QAE/C,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBV,gBAAgB,CAACrB,MAAM,CAAC+B,KAAK,EAAE,CAAC,CAAC;QACnC;QAEA3F,oBAAoB,CAAC,CAAC;QAEtB,IAAI,CAACb,MAAM,IAAI,CAACoG,YAAY,CAACE,QAAQ,CAACI,QAAQ,CAACC,aAAa,CAAC,EAAE;UAC7D,IAAIC,qBAAqB;UAEzB,CAACA,qBAAqB,GAAGrG,WAAW,CAACW,OAAO,MAAM,IAAI,IAAI0F,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC3F,KAAK,CAAC,CAAC;QACrI;MACF,CAAC,CAAC;MACF6E,gBAAgB,CAACe,IAAI,CAACd,SAAS,CAAC;IAClC;IAEA,KAAK,IAAIe,KAAK,GAAGjC,SAAS,CAACR,MAAM,EAAE0C,QAAQ,GAAG,IAAIhC,KAAK,CAAC+B,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;MACrHD,QAAQ,CAACC,KAAK,GAAG,CAAC,CAAC,GAAGnC,SAAS,CAACmC,KAAK,CAAC;IACxC;IAEA3H,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6F,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnB,KAAK,CAAC,CAACoB,MAAM,CAAC4B,QAAQ,CAAC,CAAC;EAC/G,CAAC,CAAC,CAAC;;EAGH,IAAIE,gBAAgB,GAAGnM,KAAK,CAACgF,QAAQ,CAAC,IAAI,CAAC;IACvCoH,gBAAgB,GAAGxM,cAAc,CAACuM,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,gBAAgB,GAAGvM,KAAK,CAACgF,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrCwH,gBAAgB,GAAG5M,cAAc,CAAC2M,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGvC,SAASE,iBAAiBA,CAAA,EAAG;IAC3BD,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB;EAEAnM,eAAe,CAAC,YAAY;IAC1B,IAAImH,WAAW,EAAE;MACf,IAAIkF,qBAAqB;MAEzB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACH,qBAAqB,GAAGtH,YAAY,CAACe,OAAO,MAAM,IAAI,IAAIuG,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,WAAW,CAAC;MAElK,IAAIV,cAAc,KAAKO,QAAQ,IAAI,CAACI,MAAM,CAACC,KAAK,CAACL,QAAQ,CAAC,EAAE;QAC1DN,iBAAiB,CAACM,QAAQ,CAAC;MAC7B;IACF;EACF,CAAC,EAAE,CAACnF,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEnB,IAAIyF,sBAAsB;EAE1B,IAAIlG,wBAAwB,EAAE;IAC5BkG,sBAAsB,GAAG,SAASA,sBAAsBA,CAACtF,OAAO,EAAE;MAChEF,YAAY,CAACE,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,CAAC;;EAGFlH,uBAAuB,CAAC,YAAY;IAClC,IAAIyM,oBAAoB;IAExB,OAAO,CAAC9H,YAAY,CAACe,OAAO,EAAE,CAAC+G,oBAAoB,GAAG3H,UAAU,CAACY,OAAO,MAAM,IAAI,IAAI+G,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAC5B,eAAe,CAAC,CAAC,CAAC;EAC1K,CAAC,EAAE9D,WAAW,EAAEC,YAAY,CAAC,CAAC,CAAC;;EAE/B,IAAI0F,iBAAiB,GAAGpN,KAAK,CAAC0G,OAAO,CAAC,YAAY;IAChD,OAAO7G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDe,eAAe,EAAEA,eAAe;MAChCM,IAAI,EAAE8E,UAAU;MAChBE,WAAW,EAAEA,WAAW;MACxBjG,EAAE,EAAEA,EAAE;MACNG,UAAU,EAAE+C,gBAAgB;MAC5BD,QAAQ,EAAEA,QAAQ;MAClB4I,UAAU,EAAE3F;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtG,KAAK,EAAEe,eAAe,EAAEsF,WAAW,EAAEF,UAAU,EAAE/F,EAAE,EAAEkD,gBAAgB,EAAED,QAAQ,EAAEiD,YAAY,CAAC,CAAC,CAAC,CAAC;EACrG;EACA;EACA;;EAEA,IAAI4F,eAAe,GAAGlK,SAAS,KAAKuB,SAAS,GAAGvB,SAAS,GAAGd,OAAO,IAAI,CAACmC,QAAQ,IAAIxD,IAAI,KAAK,UAAU;EACvG,IAAIsM,SAAS;EAEb,IAAID,eAAe,EAAE;IACnBC,SAAS,GAAG,aAAavN,KAAK,CAACwN,aAAa,CAAC5M,QAAQ,EAAE;MACrDc,SAAS,EAAEzB,UAAU,CAAC,EAAE,CAACoK,MAAM,CAAC5I,SAAS,EAAE,QAAQ,CAAC,EAAE/B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2K,MAAM,CAAC5I,SAAS,EAAE,gBAAgB,CAAC,EAAEa,OAAO,CAAC,CAAC;MAC3HmL,aAAa,EAAEpK,SAAS;MACxBqK,kBAAkB,EAAE;QAClBpL,OAAO,EAAEA,OAAO;QAChBS,WAAW,EAAE0D,iBAAiB;QAC9BhE,IAAI,EAAE8E,UAAU;QAChBoG,OAAO,EAAE9H,WAAW;QACpBlE,UAAU,EAAE+C;MACd;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIkJ,SAAS;EAEb,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDzL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC3DJ,qBAAqB,CAAC,EAAE,EAAE;MACxB4H,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE9H;IACV,CAAC,CAAC;IACFmG,gBAAgB,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;EACpC,CAAC;EAED,IAAI,CAAC7F,QAAQ,IAAIc,UAAU,KAAKpB,aAAa,CAACwH,MAAM,IAAI9C,iBAAiB,CAAC,EAAE;IAC1EmH,SAAS,GAAG,aAAa5N,KAAK,CAACwN,aAAa,CAAC5M,QAAQ,EAAE;MACrDc,SAAS,EAAE,EAAE,CAAC2I,MAAM,CAAC5I,SAAS,EAAE,QAAQ,CAAC;MACzC8C,WAAW,EAAEsJ,gBAAgB;MAC7BJ,aAAa,EAAEnK;IACjB,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC,CAAC;;EAGF,IAAIwK,UAAU,GAAG,aAAa9N,KAAK,CAACwN,aAAa,CAACjK,UAAU,EAAE;IAC5DlC,GAAG,EAAEqE;EACP,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIqI,eAAe,GAAG9N,UAAU,CAACwB,SAAS,EAAEC,SAAS,GAAGH,YAAY,GAAG,CAAC,CAAC,EAAE7B,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,UAAU,CAAC,EAAEoE,WAAW,CAAC,EAAEnG,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,WAAW,CAAC,EAAEgD,QAAQ,CAAC,EAAE/E,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,SAAS,CAAC,EAAE,CAACgD,QAAQ,CAAC,EAAE/E,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,cAAc,CAAC,EAAE0B,UAAU,CAAC,EAAEzD,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,aAAa,CAAC,EAAE6L,eAAe,CAAC,EAAE5N,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,WAAW,CAAC,EAAEY,QAAQ,CAAC,EAAE3C,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,UAAU,CAAC,EAAEa,OAAO,CAAC,EAAE5C,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,OAAO,CAAC,EAAE8F,UAAU,CAAC,EAAE7H,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,kBAAkB,CAAC,EAAEsF,qBAAqB,CAAC,EAAErH,eAAe,CAAC6B,YAAY,EAAE,EAAE,CAAC8I,MAAM,CAAC5I,SAAS,EAAE,cAAc,CAAC,EAAEiD,gBAAgB,CAAC,EAAEnD,YAAY,CAAC,CAAC,CAAC,CAAC;;EAE73B,IAAIyM,YAAY,GAAG,aAAahO,KAAK,CAACwN,aAAa,CAAChN,aAAa,EAAE;IACjEa,GAAG,EAAEmE,UAAU;IACfnD,QAAQ,EAAEA,QAAQ;IAClBZ,SAAS,EAAEA,SAAS;IACpBwM,OAAO,EAAExG,WAAW;IACpB6D,YAAY,EAAEwC,UAAU;IACxBzB,cAAc,EAAEA,cAAc;IAC9B7I,SAAS,EAAEA,SAAS;IACpBC,cAAc,EAAEA,cAAc;IAC9BC,aAAa,EAAEA,aAAa;IAC5BC,iBAAiB,EAAEA,iBAAiB;IACpC9B,SAAS,EAAEA,SAAS;IACpB+B,wBAAwB,EAAEA,wBAAwB;IAClDC,cAAc,EAAEA,cAAc;IAC9BC,aAAa,EAAEA,aAAa;IAC5BC,SAAS,EAAEA,SAAS;IACpBC,iBAAiB,EAAEA,iBAAiB;IACpCkK,KAAK,EAAEjM,YAAY;IACnBkM,iBAAiB,EAAE,SAASA,iBAAiBA,CAAA,EAAG;MAC9C,OAAO5I,cAAc,CAACa,OAAO;IAC/B,CAAC;IACDgI,oBAAoB,EAAElB,sBAAsB;IAC5CR,iBAAiB,EAAEA;EACrB,CAAC,EAAE1F,wBAAwB,GAAG,aAAahH,KAAK,CAACqO,YAAY,CAACrH,wBAAwB,EAAE;IACtF3F,GAAG,EAAE4F;EACP,CAAC,CAAC,GAAG,aAAajH,KAAK,CAACwN,aAAa,CAAC/M,QAAQ,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IAClEkN,MAAM,EAAE/I,cAAc;IACtB9D,SAAS,EAAEA,SAAS;IACpB8M,YAAY,EAAExH,qBAAqB;IACnC1F,GAAG,EAAEoE,WAAW;IAChBjE,EAAE,EAAEA,EAAE;IACNG,UAAU,EAAE+C,gBAAgB;IAC5BzD,IAAI,EAAEA,IAAI;IACV6B,kBAAkB,EAAEA,kBAAkB;IACtClB,SAAS,EAAEA,SAAS;IACpBiI,MAAM,EAAE9H,aAAa;IACrBU,IAAI,EAAE8E,UAAU;IAChBG,YAAY,EAAEA,YAAY;IAC1B9E,WAAW,EAAEA,WAAW;IACxBG,WAAW,EAAE0D,iBAAiB;IAC9BzD,QAAQ,EAAEkF,gBAAgB;IAC1BsG,cAAc,EAAE9F,sBAAsB;IACtC+F,QAAQ,EAAE/D,gBAAgB;IAC1B5C,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEN,IAAI4G,UAAU,CAAC,CAAC;;EAEhB,IAAI1H,wBAAwB,EAAE;IAC5B0H,UAAU,GAAGV,YAAY;EAC3B,CAAC,MAAM;IACLU,UAAU,GAAG,aAAa1O,KAAK,CAACwN,aAAa,CAAC,KAAK,EAAE/N,QAAQ,CAAC;MAC5DiC,SAAS,EAAEqM;IACb,CAAC,EAAEnJ,QAAQ,EAAE;MACXvD,GAAG,EAAEgE,YAAY;MACjBd,WAAW,EAAE4G,mBAAmB;MAChC7G,SAAS,EAAE0E,iBAAiB;MAC5B3E,OAAO,EAAEiG,eAAe;MACxBnG,OAAO,EAAE2G,gBAAgB;MACzB1G,MAAM,EAAE2G;IACV,CAAC,CAAC,EAAElF,WAAW,IAAI,CAAC0B,UAAU,IAAI,aAAavH,KAAK,CAACwN,aAAa,CAAC,MAAM,EAAE;MACzEmB,KAAK,EAAE;QACLC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAE;MACX,CAAC;MACD,WAAW,EAAE;IACf,CAAC,EAAE,EAAE,CAAC3E,MAAM,CAACtI,aAAa,CAACkN,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC7C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBtI,KAAK,GAAGqI,IAAI,CAACrI,KAAK;MACtB,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACoB,QAAQ,CAACzI,OAAO,CAAC2P,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAGtI,KAAK;IACtE,CAAC,CAAC,CAACuI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEpB,YAAY,EAAET,SAAS,EAAEK,SAAS,CAAC;EACtD;EAEA,OAAO,aAAa5N,KAAK,CAACwN,aAAa,CAAC1M,iBAAiB,CAACuO,QAAQ,EAAE;IAClExI,KAAK,EAAEuG;EACT,CAAC,EAAEsB,UAAU,CAAC;AAChB,CAAC,CAAC,CAAC,CAAC;;AAEJ,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCtO,UAAU,CAACuO,WAAW,GAAG,YAAY;AACvC;AAEA,eAAevO,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}