export default SplashScreen;
/**
 * @fileoverview
 * Audits if a page is configured for a custom splash screen when launched
 * https://github.com/GoogleChrome/lighthouse/issues/24
 *
 * Requirements:
 *   * manifest is not empty
 *   * manifest has a valid name
 *   * manifest has a valid background_color
 *   * manifest has a valid theme_color
 *   * manifest contains icon that's a png and size >= 512px
 */
declare class SplashScreen extends MultiCheckAudit {
    /**
     * @param {LH.Artifacts.ManifestValues} manifestValues
     * @param {Array<string>} failures
     */
    static assessManifest(manifestValues: LH.Artifacts.ManifestValues, failures: Array<string>): void;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<{failures: Array<string>, manifestValues: LH.Artifacts.ManifestValues}>}
     */
    static audit_(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<{
        failures: Array<string>;
        manifestValues: LH.Artifacts.ManifestValues;
    }>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
import MultiCheckAudit from "./multi-check-audit.js";
import { ManifestValues } from "../computed/manifest-values.js";
//# sourceMappingURL=splash-screen.d.ts.map