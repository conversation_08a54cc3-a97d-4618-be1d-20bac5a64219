{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useMemo;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction useMemo(getValue, condition, shouldUpdate) {\n  var cacheRef = React.useRef({});\n  if (!('value' in cacheRef.current) || shouldUpdate(cacheRef.current.condition, condition)) {\n    cacheRef.current.value = getValue();\n    cacheRef.current.condition = condition;\n  }\n  return cacheRef.current.value;\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "value", "default", "useMemo", "React", "getValue", "condition", "shouldUpdate", "cacheRef", "useRef", "current"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/lib/hooks/useMemo.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useMemo;\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nfunction useMemo(getValue, condition, shouldUpdate) {\n  var cacheRef = React.useRef({});\n\n  if (!('value' in cacheRef.current) || shouldUpdate(cacheRef.current.condition, condition)) {\n    cacheRef.current.value = getValue();\n    cacheRef.current.condition = condition;\n  }\n\n  return cacheRef.current.value;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,OAAO;AAEzB,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,SAASM,OAAOA,CAACE,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAClD,IAAIC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EAE/B,IAAI,EAAE,OAAO,IAAID,QAAQ,CAACE,OAAO,CAAC,IAAIH,YAAY,CAACC,QAAQ,CAACE,OAAO,CAACJ,SAAS,EAAEA,SAAS,CAAC,EAAE;IACzFE,QAAQ,CAACE,OAAO,CAACT,KAAK,GAAGI,QAAQ,CAAC,CAAC;IACnCG,QAAQ,CAACE,OAAO,CAACJ,SAAS,GAAGA,SAAS;EACxC;EAEA,OAAOE,QAAQ,CAACE,OAAO,CAACT,KAAK;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}