{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar Element = function Element(props) {\n  var _classNames, _classNames2;\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    size = props.size,\n    shape = props.shape;\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var shapeCls = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-circle\"), shape === 'circle'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-square\"), shape === 'square'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-round\"), shape === 'round'), _classNames2));\n  var sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\")\n  } : {};\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: _extends(_extends({}, sizeStyle), style)\n  });\n};\nexport default Element;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "classNames", "Element", "props", "_classNames", "_classNames2", "prefixCls", "className", "style", "size", "shape", "sizeCls", "concat", "shapeCls", "sizeStyle", "width", "height", "lineHeight", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Element.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\n\nvar Element = function Element(props) {\n  var _classNames, _classNames2;\n\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style,\n      size = props.size,\n      shape = props.shape;\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var shapeCls = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-circle\"), shape === 'circle'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-square\"), shape === 'square'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-round\"), shape === 'round'), _classNames2));\n  var sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\")\n  } : {};\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: _extends(_extends({}, sizeStyle), style)\n  });\n};\n\nexport default Element;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AAEnC,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,WAAW,EAAEC,YAAY;EAE7B,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,KAAK,GAAGP,KAAK,CAACO,KAAK;EACvB,IAAIC,OAAO,GAAGV,UAAU,EAAEG,WAAW,GAAG,CAAC,CAAC,EAAEL,eAAe,CAACK,WAAW,EAAE,EAAE,CAACQ,MAAM,CAACN,SAAS,EAAE,KAAK,CAAC,EAAEG,IAAI,KAAK,OAAO,CAAC,EAAEV,eAAe,CAACK,WAAW,EAAE,EAAE,CAACQ,MAAM,CAACN,SAAS,EAAE,KAAK,CAAC,EAAEG,IAAI,KAAK,OAAO,CAAC,EAAEL,WAAW,CAAC,CAAC;EACnN,IAAIS,QAAQ,GAAGZ,UAAU,EAAEI,YAAY,GAAG,CAAC,CAAC,EAAEN,eAAe,CAACM,YAAY,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,SAAS,CAAC,EAAEI,KAAK,KAAK,QAAQ,CAAC,EAAEX,eAAe,CAACM,YAAY,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,SAAS,CAAC,EAAEI,KAAK,KAAK,QAAQ,CAAC,EAAEX,eAAe,CAACM,YAAY,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,QAAQ,CAAC,EAAEI,KAAK,KAAK,OAAO,CAAC,EAAEL,YAAY,CAAC,CAAC;EACtT,IAAIS,SAAS,GAAG,OAAOL,IAAI,KAAK,QAAQ,GAAG;IACzCM,KAAK,EAAEN,IAAI;IACXO,MAAM,EAAEP,IAAI;IACZQ,UAAU,EAAE,EAAE,CAACL,MAAM,CAACH,IAAI,EAAE,IAAI;EAClC,CAAC,GAAG,CAAC,CAAC;EACN,OAAO,aAAaT,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;IAC9CX,SAAS,EAAEN,UAAU,CAACK,SAAS,EAAEK,OAAO,EAAEE,QAAQ,EAAEN,SAAS,CAAC;IAC9DC,KAAK,EAAEV,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgB,SAAS,CAAC,EAAEN,KAAK;EAChD,CAAC,CAAC;AACJ,CAAC;AAED,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}