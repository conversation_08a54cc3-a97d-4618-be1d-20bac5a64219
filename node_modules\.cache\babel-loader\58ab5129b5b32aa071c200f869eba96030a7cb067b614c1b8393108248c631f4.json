{"ast": null, "code": "// eslint-disable-next-line import/prefer-default-export\nexport function easeInOutCubic(t, b, c, d) {\n  var cc = c - b;\n  t /= d / 2;\n  if (t < 1) {\n    return cc / 2 * t * t * t + b;\n  } // eslint-disable-next-line no-return-assign\n\n  return cc / 2 * ((t -= 2) * t * t + 2) + b;\n}", "map": {"version": 3, "names": ["easeInOutCubic", "t", "b", "c", "d", "cc"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/easings.js"], "sourcesContent": ["// eslint-disable-next-line import/prefer-default-export\nexport function easeInOutCubic(t, b, c, d) {\n  var cc = c - b;\n  t /= d / 2;\n\n  if (t < 1) {\n    return cc / 2 * t * t * t + b;\n  } // eslint-disable-next-line no-return-assign\n\n\n  return cc / 2 * ((t -= 2) * t * t + 2) + b;\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAIC,EAAE,GAAGF,CAAC,GAAGD,CAAC;EACdD,CAAC,IAAIG,CAAC,GAAG,CAAC;EAEV,IAAIH,CAAC,GAAG,CAAC,EAAE;IACT,OAAOI,EAAE,GAAG,CAAC,GAAGJ,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGC,CAAC;EAC/B,CAAC,CAAC;;EAGF,OAAOG,EAAE,GAAG,CAAC,IAAI,CAACJ,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}