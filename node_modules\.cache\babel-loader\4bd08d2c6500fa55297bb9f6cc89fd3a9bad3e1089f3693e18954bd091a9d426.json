{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nimport React from 'react';\nvar defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true\n};\nvar i18nInstance;\nexport var I18nContext = React.createContext();\nexport function setDefaults() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  defaultOptions = _objectSpread(_objectSpread({}, defaultOptions), options);\n}\nexport function getDefaults() {\n  return defaultOptions;\n}\nexport var ReportNamespaces = function () {\n  function ReportNamespaces() {\n    _classCallCheck(this, ReportNamespaces);\n    this.usedNamespaces = {};\n  }\n  _createClass(ReportNamespaces, [{\n    key: \"addUsedNamespaces\",\n    value: function addUsedNamespaces(namespaces) {\n      var _this = this;\n      namespaces.forEach(function (ns) {\n        if (!_this.usedNamespaces[ns]) _this.usedNamespaces[ns] = true;\n      });\n    }\n  }, {\n    key: \"getUsedNamespaces\",\n    value: function getUsedNamespaces() {\n      return Object.keys(this.usedNamespaces);\n    }\n  }]);\n  return ReportNamespaces;\n}();\nexport function setI18n(instance) {\n  i18nInstance = instance;\n}\nexport function getI18n() {\n  return i18nInstance;\n}\nexport var initReactI18next = {\n  type: '3rdParty',\n  init: function init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};\nexport function composeInitialProps(ForComponent) {\n  return function (ctx) {\n    return new Promise(function (resolve) {\n      var i18nInitialProps = getInitialProps();\n      if (ForComponent.getInitialProps) {\n        ForComponent.getInitialProps(ctx).then(function (componentsInitialProps) {\n          resolve(_objectSpread(_objectSpread({}, componentsInitialProps), i18nInitialProps));\n        });\n      } else {\n        resolve(i18nInitialProps);\n      }\n    });\n  };\n}\nexport function getInitialProps() {\n  var i18n = getI18n();\n  var namespaces = i18n.reportNamespaces ? i18n.reportNamespaces.getUsedNamespaces() : [];\n  var ret = {};\n  var initialI18nStore = {};\n  i18n.languages.forEach(function (l) {\n    initialI18nStore[l] = {};\n    namespaces.forEach(function (ns) {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n}", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "React", "defaultOptions", "bindI18n", "bindI18nStore", "transEmptyNodeValue", "transSupportBasicHtmlNodes", "transWrapTextNodes", "transKeepBasicHtmlNodesFor", "useSuspense", "i18nInstance", "I18nContext", "createContext", "setDefaults", "options", "undefined", "getDefaults", "ReportNamespaces", "usedNamespaces", "value", "addUsedNamespaces", "namespaces", "_this", "ns", "getUsedNamespaces", "setI18n", "instance", "getI18n", "initReactI18next", "type", "init", "react", "composeInitialProps", "ForComponent", "ctx", "Promise", "resolve", "i18nInitialProps", "getInitialProps", "then", "componentsInitialProps", "i18n", "reportNamespaces", "ret", "initialI18nStore", "languages", "l", "getResourceBundle", "initialLanguage", "language"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/context.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport React from 'react';\nvar defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true\n};\nvar i18nInstance;\nexport var I18nContext = React.createContext();\nexport function setDefaults() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  defaultOptions = _objectSpread(_objectSpread({}, defaultOptions), options);\n}\nexport function getDefaults() {\n  return defaultOptions;\n}\nexport var ReportNamespaces = function () {\n  function ReportNamespaces() {\n    _classCallCheck(this, ReportNamespaces);\n\n    this.usedNamespaces = {};\n  }\n\n  _createClass(ReportNamespaces, [{\n    key: \"addUsedNamespaces\",\n    value: function addUsedNamespaces(namespaces) {\n      var _this = this;\n\n      namespaces.forEach(function (ns) {\n        if (!_this.usedNamespaces[ns]) _this.usedNamespaces[ns] = true;\n      });\n    }\n  }, {\n    key: \"getUsedNamespaces\",\n    value: function getUsedNamespaces() {\n      return Object.keys(this.usedNamespaces);\n    }\n  }]);\n\n  return ReportNamespaces;\n}();\nexport function setI18n(instance) {\n  i18nInstance = instance;\n}\nexport function getI18n() {\n  return i18nInstance;\n}\nexport var initReactI18next = {\n  type: '3rdParty',\n  init: function init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};\nexport function composeInitialProps(ForComponent) {\n  return function (ctx) {\n    return new Promise(function (resolve) {\n      var i18nInitialProps = getInitialProps();\n\n      if (ForComponent.getInitialProps) {\n        ForComponent.getInitialProps(ctx).then(function (componentsInitialProps) {\n          resolve(_objectSpread(_objectSpread({}, componentsInitialProps), i18nInitialProps));\n        });\n      } else {\n        resolve(i18nInitialProps);\n      }\n    });\n  };\n}\nexport function getInitialProps() {\n  var i18n = getI18n();\n  var namespaces = i18n.reportNamespaces ? i18n.reportNamespaces.getUsedNamespaces() : [];\n  var ret = {};\n  var initialI18nStore = {};\n  i18n.languages.forEach(function (l) {\n    initialI18nStore[l] = {};\n    namespaces.forEach(function (ns) {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uCAAuC;AACnE,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,eAAe,MAAM,uCAAuC;AAEnE,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAErB,eAAe,CAACe,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACiB,yBAAyB,EAAE;MAAEjB,MAAM,CAACkB,gBAAgB,CAACR,MAAM,EAAEV,MAAM,CAACiB,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACmB,cAAc,CAACT,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,OAAOU,KAAK,MAAM,OAAO;AACzB,IAAIC,cAAc,GAAG;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,aAAa,EAAE,EAAE;EACjBC,mBAAmB,EAAE,EAAE;EACvBC,0BAA0B,EAAE,IAAI;EAChCC,kBAAkB,EAAE,EAAE;EACtBC,0BAA0B,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC;EACtDC,WAAW,EAAE;AACf,CAAC;AACD,IAAIC,YAAY;AAChB,OAAO,IAAIC,WAAW,GAAGV,KAAK,CAACW,aAAa,CAAC,CAAC;AAC9C,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,IAAIC,OAAO,GAAGrB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsB,SAAS,GAAGtB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpFS,cAAc,GAAGZ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEY,cAAc,CAAC,EAAEY,OAAO,CAAC;AAC5E;AACA,OAAO,SAASE,WAAWA,CAAA,EAAG;EAC5B,OAAOd,cAAc;AACvB;AACA,OAAO,IAAIe,gBAAgB,GAAG,YAAY;EACxC,SAASA,gBAAgBA,CAAA,EAAG;IAC1B3C,eAAe,CAAC,IAAI,EAAE2C,gBAAgB,CAAC;IAEvC,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;EAC1B;EAEA3C,YAAY,CAAC0C,gBAAgB,EAAE,CAAC;IAC9BpB,GAAG,EAAE,mBAAmB;IACxBsB,KAAK,EAAE,SAASC,iBAAiBA,CAACC,UAAU,EAAE;MAC5C,IAAIC,KAAK,GAAG,IAAI;MAEhBD,UAAU,CAACzB,OAAO,CAAC,UAAU2B,EAAE,EAAE;QAC/B,IAAI,CAACD,KAAK,CAACJ,cAAc,CAACK,EAAE,CAAC,EAAED,KAAK,CAACJ,cAAc,CAACK,EAAE,CAAC,GAAG,IAAI;MAChE,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,mBAAmB;IACxBsB,KAAK,EAAE,SAASK,iBAAiBA,CAAA,EAAG;MAClC,OAAO3C,MAAM,CAACD,IAAI,CAAC,IAAI,CAACsC,cAAc,CAAC;IACzC;EACF,CAAC,CAAC,CAAC;EAEH,OAAOD,gBAAgB;AACzB,CAAC,CAAC,CAAC;AACH,OAAO,SAASQ,OAAOA,CAACC,QAAQ,EAAE;EAChChB,YAAY,GAAGgB,QAAQ;AACzB;AACA,OAAO,SAASC,OAAOA,CAAA,EAAG;EACxB,OAAOjB,YAAY;AACrB;AACA,OAAO,IAAIkB,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAASA,IAAIA,CAACJ,QAAQ,EAAE;IAC5Bb,WAAW,CAACa,QAAQ,CAACZ,OAAO,CAACiB,KAAK,CAAC;IACnCN,OAAO,CAACC,QAAQ,CAAC;EACnB;AACF,CAAC;AACD,OAAO,SAASM,mBAAmBA,CAACC,YAAY,EAAE;EAChD,OAAO,UAAUC,GAAG,EAAE;IACpB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,IAAIC,gBAAgB,GAAGC,eAAe,CAAC,CAAC;MAExC,IAAIL,YAAY,CAACK,eAAe,EAAE;QAChCL,YAAY,CAACK,eAAe,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC,UAAUC,sBAAsB,EAAE;UACvEJ,OAAO,CAAC9C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkD,sBAAsB,CAAC,EAAEH,gBAAgB,CAAC,CAAC;QACrF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLD,OAAO,CAACC,gBAAgB,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;AACH;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,IAAIG,IAAI,GAAGd,OAAO,CAAC,CAAC;EACpB,IAAIN,UAAU,GAAGoB,IAAI,CAACC,gBAAgB,GAAGD,IAAI,CAACC,gBAAgB,CAAClB,iBAAiB,CAAC,CAAC,GAAG,EAAE;EACvF,IAAImB,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzBH,IAAI,CAACI,SAAS,CAACjD,OAAO,CAAC,UAAUkD,CAAC,EAAE;IAClCF,gBAAgB,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;IACxBzB,UAAU,CAACzB,OAAO,CAAC,UAAU2B,EAAE,EAAE;MAC/BqB,gBAAgB,CAACE,CAAC,CAAC,CAACvB,EAAE,CAAC,GAAGkB,IAAI,CAACM,iBAAiB,CAACD,CAAC,EAAEvB,EAAE,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ,CAAC,CAAC;EACFoB,GAAG,CAACC,gBAAgB,GAAGA,gBAAgB;EACvCD,GAAG,CAACK,eAAe,GAAGP,IAAI,CAACQ,QAAQ;EACnC,OAAON,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}