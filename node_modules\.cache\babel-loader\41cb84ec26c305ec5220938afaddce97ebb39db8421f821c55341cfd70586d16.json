{"ast": null, "code": "export function onlyUniqueNameStatus(value) {\n  var resArr = [];\n  value.filter(item => {\n    var i = resArr.findIndex(x => x.idProduct !== undefined ? x.idProduct === item.idProduct : x.id === item.id);\n    if (i <= -1) {\n      resArr.push(item);\n    }\n    return null;\n  });\n  return resArr;\n}\nexport function onlyUniqueCondizioneCorrelatiOmaggio(value) {\n  var resArr = [];\n  value.filter(item => {\n    var i = resArr.findIndex(x => x.condizione === item.condizione && x.correlati.length === item.correlati.length && Object.keys(x.omaggio)[0] === Object.keys(item.omaggio)[0]);\n    if (i <= -1) {\n      resArr.push(item);\n    }\n    return null;\n  });\n  return resArr;\n}", "map": {"version": 3, "names": ["onlyUniqueNameStatus", "value", "resArr", "filter", "item", "i", "findIndex", "x", "idProduct", "undefined", "id", "push", "onlyUniqueCondizioneCorrelatiOmaggio", "condizione", "correlati", "length", "Object", "keys", "omaggio"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/uniqueElements/onlyUniqueNameStatus.jsx"], "sourcesContent": ["export function onlyUniqueNameStatus(value) {\n    var resArr = [];\n    value.filter((item) => {\n        var i = resArr.findIndex(x => (x.idProduct !== undefined ? x.idProduct === item.idProduct : x.id === item.id));\n        if (i <= -1) {\n            resArr.push(item);\n        }\n        return null;\n    });\n    return resArr;\n}\n\n\nexport function onlyUniqueCondizioneCorrelatiOmaggio(value) {\n    var resArr = [];\n    value.filter((item) => {\n        var i = resArr.findIndex(x => (x.condizione === item.condizione && x.correlati.length === item.correlati.length && Object.keys(x.omaggio)[0] === Object.keys(item.omaggio)[0]));\n        if (i <= -1) {\n            resArr.push(item);\n        }\n        return null;\n    });\n    return resArr;\n}"], "mappings": "AAAA,OAAO,SAASA,oBAAoBA,CAACC,KAAK,EAAE;EACxC,IAAIC,MAAM,GAAG,EAAE;EACfD,KAAK,CAACE,MAAM,CAAEC,IAAI,IAAK;IACnB,IAAIC,CAAC,GAAGH,MAAM,CAACI,SAAS,CAACC,CAAC,IAAKA,CAAC,CAACC,SAAS,KAAKC,SAAS,GAAGF,CAAC,CAACC,SAAS,KAAKJ,IAAI,CAACI,SAAS,GAAGD,CAAC,CAACG,EAAE,KAAKN,IAAI,CAACM,EAAG,CAAC;IAC9G,IAAIL,CAAC,IAAI,CAAC,CAAC,EAAE;MACTH,MAAM,CAACS,IAAI,CAACP,IAAI,CAAC;IACrB;IACA,OAAO,IAAI;EACf,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AAGA,OAAO,SAASU,oCAAoCA,CAACX,KAAK,EAAE;EACxD,IAAIC,MAAM,GAAG,EAAE;EACfD,KAAK,CAACE,MAAM,CAAEC,IAAI,IAAK;IACnB,IAAIC,CAAC,GAAGH,MAAM,CAACI,SAAS,CAACC,CAAC,IAAKA,CAAC,CAACM,UAAU,KAAKT,IAAI,CAACS,UAAU,IAAIN,CAAC,CAACO,SAAS,CAACC,MAAM,KAAKX,IAAI,CAACU,SAAS,CAACC,MAAM,IAAIC,MAAM,CAACC,IAAI,CAACV,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAAC,KAAKF,MAAM,CAACC,IAAI,CAACb,IAAI,CAACc,OAAO,CAAC,CAAC,CAAC,CAAE,CAAC;IAC/K,IAAIb,CAAC,IAAI,CAAC,CAAC,EAAE;MACTH,MAAM,CAACS,IAAI,CAACP,IAAI,CAAC;IACrB;IACA,OAAO,IAAI;EACf,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}