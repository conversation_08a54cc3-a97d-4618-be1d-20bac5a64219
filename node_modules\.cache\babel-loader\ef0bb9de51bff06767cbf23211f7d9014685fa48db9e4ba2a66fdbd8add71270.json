{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport Col from '../grid/col';\nimport { FormContext } from './context';\nimport { useLocaleReceiver } from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport Tooltip from '../tooltip';\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n  if (_typeof(tooltip) === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nvar FormItemLabel = function FormItemLabel(_ref) {\n  var prefixCls = _ref.prefixCls,\n    label = _ref.label,\n    htmlFor = _ref.htmlFor,\n    labelCol = _ref.labelCol,\n    labelAlign = _ref.labelAlign,\n    colon = _ref.colon,\n    required = _ref.required,\n    requiredMark = _ref.requiredMark,\n    tooltip = _ref.tooltip;\n  var _useLocaleReceiver = useLocaleReceiver('Form'),\n    _useLocaleReceiver2 = _slicedToArray(_useLocaleReceiver, 1),\n    formLocale = _useLocaleReceiver2[0];\n  if (!label) return null;\n  return /*#__PURE__*/React.createElement(FormContext.Consumer, {\n    key: \"label\"\n  }, function (_ref2) {\n    var _classNames2;\n    var vertical = _ref2.vertical,\n      contextLabelAlign = _ref2.labelAlign,\n      contextLabelCol = _ref2.labelCol,\n      labelWrap = _ref2.labelWrap,\n      contextColon = _ref2.colon;\n    var _a;\n    var mergedLabelCol = labelCol || contextLabelCol || {};\n    var mergedLabelAlign = labelAlign || contextLabelAlign;\n    var labelClsBasic = \"\".concat(prefixCls, \"-item-label\");\n    var labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && \"\".concat(labelClsBasic, \"-left\"), mergedLabelCol.className, _defineProperty({}, \"\".concat(labelClsBasic, \"-wrap\"), !!labelWrap));\n    var labelChildren = label; // Keep label is original where there should have no colon\n\n    var computedColon = colon === true || contextColon !== false && colon !== false;\n    var haveColon = computedColon && !vertical; // Remove duplicated user input colon\n\n    if (haveColon && typeof label === 'string' && label.trim() !== '') {\n      labelChildren = label.replace(/[:|：]\\s*$/, '');\n    } // Tooltip\n\n    var tooltipProps = toTooltipProps(tooltip);\n    if (tooltipProps) {\n      var _tooltipProps$icon = tooltipProps.icon,\n        icon = _tooltipProps$icon === void 0 ? /*#__PURE__*/React.createElement(QuestionCircleOutlined, null) : _tooltipProps$icon,\n        restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n      var tooltipNode = /*#__PURE__*/React.createElement(Tooltip, restTooltipProps, /*#__PURE__*/React.cloneElement(icon, {\n        className: \"\".concat(prefixCls, \"-item-tooltip\"),\n        title: ''\n      }));\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n    } // Add required mark if optional\n\n    if (requiredMark === 'optional' && !required) {\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-optional\"),\n        title: \"\"\n      }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n    }\n    var labelClassName = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required\"), required), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required-mark-optional\"), requiredMark === 'optional'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-no-colon\"), !computedColon), _classNames2));\n    return /*#__PURE__*/React.createElement(Col, _extends({}, mergedLabelCol, {\n      className: labelColClassName\n    }), /*#__PURE__*/React.createElement(\"label\", {\n      htmlFor: htmlFor,\n      className: labelClassName,\n      title: typeof label === 'string' ? label : ''\n    }, labelChildren));\n  });\n};\nexport default FormItemLabel;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "QuestionCircleOutlined", "Col", "FormContext", "useLocaleReceiver", "defaultLocale", "<PERSON><PERSON><PERSON>", "toTooltipProps", "tooltip", "isValidElement", "title", "FormItemLabel", "_ref", "prefixCls", "label", "htmlFor", "labelCol", "labelAlign", "colon", "required", "requiredMark", "_useLocaleReceiver", "_useLocaleReceiver2", "formLocale", "createElement", "Consumer", "key", "_ref2", "_classNames2", "vertical", "contextLabelAlign", "contextLabelCol", "labelWrap", "contextColon", "_a", "mergedLabelCol", "mergedLabelAlign", "labelClsBasic", "concat", "labelColClassName", "className", "labelChildren", "computedColon", "haveColon", "trim", "replace", "tooltipProps", "_tooltipProps$icon", "icon", "restTooltipProps", "tooltipNode", "cloneElement", "Fragment", "optional", "Form", "labelClassName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/FormItemLabel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport Col from '../grid/col';\nimport { FormContext } from './context';\nimport { useLocaleReceiver } from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport Tooltip from '../tooltip';\n\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n\n  if (_typeof(tooltip) === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n\n  return {\n    title: tooltip\n  };\n}\n\nvar FormItemLabel = function FormItemLabel(_ref) {\n  var prefixCls = _ref.prefixCls,\n      label = _ref.label,\n      htmlFor = _ref.htmlFor,\n      labelCol = _ref.labelCol,\n      labelAlign = _ref.labelAlign,\n      colon = _ref.colon,\n      required = _ref.required,\n      requiredMark = _ref.requiredMark,\n      tooltip = _ref.tooltip;\n\n  var _useLocaleReceiver = useLocaleReceiver('Form'),\n      _useLocaleReceiver2 = _slicedToArray(_useLocaleReceiver, 1),\n      formLocale = _useLocaleReceiver2[0];\n\n  if (!label) return null;\n  return /*#__PURE__*/React.createElement(FormContext.Consumer, {\n    key: \"label\"\n  }, function (_ref2) {\n    var _classNames2;\n\n    var vertical = _ref2.vertical,\n        contextLabelAlign = _ref2.labelAlign,\n        contextLabelCol = _ref2.labelCol,\n        labelWrap = _ref2.labelWrap,\n        contextColon = _ref2.colon;\n\n    var _a;\n\n    var mergedLabelCol = labelCol || contextLabelCol || {};\n    var mergedLabelAlign = labelAlign || contextLabelAlign;\n    var labelClsBasic = \"\".concat(prefixCls, \"-item-label\");\n    var labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && \"\".concat(labelClsBasic, \"-left\"), mergedLabelCol.className, _defineProperty({}, \"\".concat(labelClsBasic, \"-wrap\"), !!labelWrap));\n    var labelChildren = label; // Keep label is original where there should have no colon\n\n    var computedColon = colon === true || contextColon !== false && colon !== false;\n    var haveColon = computedColon && !vertical; // Remove duplicated user input colon\n\n    if (haveColon && typeof label === 'string' && label.trim() !== '') {\n      labelChildren = label.replace(/[:|：]\\s*$/, '');\n    } // Tooltip\n\n\n    var tooltipProps = toTooltipProps(tooltip);\n\n    if (tooltipProps) {\n      var _tooltipProps$icon = tooltipProps.icon,\n          icon = _tooltipProps$icon === void 0 ? /*#__PURE__*/React.createElement(QuestionCircleOutlined, null) : _tooltipProps$icon,\n          restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n\n      var tooltipNode = /*#__PURE__*/React.createElement(Tooltip, restTooltipProps, /*#__PURE__*/React.cloneElement(icon, {\n        className: \"\".concat(prefixCls, \"-item-tooltip\"),\n        title: ''\n      }));\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n    } // Add required mark if optional\n\n\n    if (requiredMark === 'optional' && !required) {\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-optional\"),\n        title: \"\"\n      }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n    }\n\n    var labelClassName = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required\"), required), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required-mark-optional\"), requiredMark === 'optional'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-no-colon\"), !computedColon), _classNames2));\n    return /*#__PURE__*/React.createElement(Col, _extends({}, mergedLabelCol, {\n      className: labelColClassName\n    }), /*#__PURE__*/React.createElement(\"label\", {\n      htmlFor: htmlFor,\n      className: labelClassName,\n      title: typeof label === 'string' ? label : ''\n    }, labelChildren));\n  });\n};\n\nexport default FormItemLabel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AAEvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,sBAAsB,MAAM,mDAAmD;AACtF,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,OAAO,MAAM,YAAY;AAEhC,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,IAAIxB,OAAO,CAACwB,OAAO,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAaT,KAAK,CAACU,cAAc,CAACD,OAAO,CAAC,EAAE;IACjF,OAAOA,OAAO;EAChB;EAEA,OAAO;IACLE,KAAK,EAAEF;EACT,CAAC;AACH;AAEA,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC/C,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,UAAU,GAAGL,IAAI,CAACK,UAAU;IAC5BC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCZ,OAAO,GAAGI,IAAI,CAACJ,OAAO;EAE1B,IAAIa,kBAAkB,GAAGjB,iBAAiB,CAAC,MAAM,CAAC;IAC9CkB,mBAAmB,GAAGvC,cAAc,CAACsC,kBAAkB,EAAE,CAAC,CAAC;IAC3DE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;EAEvC,IAAI,CAACR,KAAK,EAAE,OAAO,IAAI;EACvB,OAAO,aAAaf,KAAK,CAACyB,aAAa,CAACrB,WAAW,CAACsB,QAAQ,EAAE;IAC5DC,GAAG,EAAE;EACP,CAAC,EAAE,UAAUC,KAAK,EAAE;IAClB,IAAIC,YAAY;IAEhB,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;MACzBC,iBAAiB,GAAGH,KAAK,CAACV,UAAU;MACpCc,eAAe,GAAGJ,KAAK,CAACX,QAAQ;MAChCgB,SAAS,GAAGL,KAAK,CAACK,SAAS;MAC3BC,YAAY,GAAGN,KAAK,CAACT,KAAK;IAE9B,IAAIgB,EAAE;IAEN,IAAIC,cAAc,GAAGnB,QAAQ,IAAIe,eAAe,IAAI,CAAC,CAAC;IACtD,IAAIK,gBAAgB,GAAGnB,UAAU,IAAIa,iBAAiB;IACtD,IAAIO,aAAa,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,aAAa,CAAC;IACvD,IAAI0B,iBAAiB,GAAGvC,UAAU,CAACqC,aAAa,EAAED,gBAAgB,KAAK,MAAM,IAAI,EAAE,CAACE,MAAM,CAACD,aAAa,EAAE,OAAO,CAAC,EAAEF,cAAc,CAACK,SAAS,EAAE1D,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwD,MAAM,CAACD,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC,CAACL,SAAS,CAAC,CAAC;IAClN,IAAIS,aAAa,GAAG3B,KAAK,CAAC,CAAC;;IAE3B,IAAI4B,aAAa,GAAGxB,KAAK,KAAK,IAAI,IAAIe,YAAY,KAAK,KAAK,IAAIf,KAAK,KAAK,KAAK;IAC/E,IAAIyB,SAAS,GAAGD,aAAa,IAAI,CAACb,QAAQ,CAAC,CAAC;;IAE5C,IAAIc,SAAS,IAAI,OAAO7B,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC8B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEH,aAAa,GAAG3B,KAAK,CAAC+B,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IAChD,CAAC,CAAC;;IAGF,IAAIC,YAAY,GAAGvC,cAAc,CAACC,OAAO,CAAC;IAE1C,IAAIsC,YAAY,EAAE;MAChB,IAAIC,kBAAkB,GAAGD,YAAY,CAACE,IAAI;QACtCA,IAAI,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,aAAahD,KAAK,CAACyB,aAAa,CAACvB,sBAAsB,EAAE,IAAI,CAAC,GAAG8C,kBAAkB;QAC1HE,gBAAgB,GAAGhE,MAAM,CAAC6D,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC;MAErD,IAAII,WAAW,GAAG,aAAanD,KAAK,CAACyB,aAAa,CAAClB,OAAO,EAAE2C,gBAAgB,EAAE,aAAalD,KAAK,CAACoD,YAAY,CAACH,IAAI,EAAE;QAClHR,SAAS,EAAE,EAAE,CAACF,MAAM,CAACzB,SAAS,EAAE,eAAe,CAAC;QAChDH,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;MACH+B,aAAa,GAAG,aAAa1C,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAACqD,QAAQ,EAAE,IAAI,EAAEX,aAAa,EAAES,WAAW,CAAC;IACpG,CAAC,CAAC;;IAGF,IAAI9B,YAAY,KAAK,UAAU,IAAI,CAACD,QAAQ,EAAE;MAC5CsB,aAAa,GAAG,aAAa1C,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAACqD,QAAQ,EAAE,IAAI,EAAEX,aAAa,EAAE,aAAa1C,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE;QAC7HgB,SAAS,EAAE,EAAE,CAACF,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC;QACjDH,KAAK,EAAE;MACT,CAAC,EAAE,CAACa,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC8B,QAAQ,MAAM,CAACnB,EAAE,GAAG7B,aAAa,CAACiD,IAAI,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,QAAQ,CAAC,CAAC,CAAC;IACpK;IAEA,IAAIE,cAAc,GAAGvD,UAAU,EAAE4B,YAAY,GAAG,CAAC,CAAC,EAAE9C,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACU,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC,EAAEM,QAAQ,CAAC,EAAErC,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACU,MAAM,CAACzB,SAAS,EAAE,8BAA8B,CAAC,EAAEO,YAAY,KAAK,UAAU,CAAC,EAAEtC,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACU,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAAC6B,aAAa,CAAC,EAAEd,YAAY,CAAC,CAAC;IAC5V,OAAO,aAAa7B,KAAK,CAACyB,aAAa,CAACtB,GAAG,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEsD,cAAc,EAAE;MACxEK,SAAS,EAAED;IACb,CAAC,CAAC,EAAE,aAAaxC,KAAK,CAACyB,aAAa,CAAC,OAAO,EAAE;MAC5CT,OAAO,EAAEA,OAAO;MAChByB,SAAS,EAAEe,cAAc;MACzB7C,KAAK,EAAE,OAAOI,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;IAC7C,CAAC,EAAE2B,aAAa,CAAC,CAAC;EACpB,CAAC,CAAC;AACJ,CAAC;AAED,eAAe9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}