{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\statistiche\\\\chartHorizontal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useEffect } from \"react\";\nimport { Chart } from 'primereact/chart';\nimport { APIRequest } from \"../apireq\";\nimport { Costanti } from \"../../traduttore/const\";\nimport { SelectButton } from 'primereact/selectbutton';\nimport { affiliato } from \"../../route\";\nimport { stopLoading } from \"../stopLoading\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ChartHorizontal = props => {\n  _s();\n  const [basicData, setBasicData] = useState([]);\n  const [value1, setValue1] = useState({\n    name: Costanti.AnnCorr,\n    code: 'ref=current&type=year&qty=1'\n  });\n  const options = [{\n    name: Costanti.AnnCorr,\n    code: 'ref=current&type=year&qty=1'\n  }, {\n    name: Costanti.UltDodMes,\n    code: 'ref=last&type=year&qty=1'\n  }, {\n    name: Costanti.UltTreMes,\n    code: 'ref=last&type=month&qty=3'\n  }];\n  useEffect(() => {\n    async function trovaRisultato() {\n      await APIRequest('GET', 'statistic/ordersaffiliatestatistic?ref=current&type=year&qty=1').then(res => {\n        var date = [];\n        var dati = [];\n        var label = [];\n        res.data.forEach(element => {\n          date.push(element.orderDate);\n          dati.push(parseFloat(element.total));\n          label.push('');\n        });\n        setBasicData({\n          labels: date,\n          datasets: [{\n            label: 'Fatturato',\n            backgroundColor: '#8EB6DC',\n            data: dati\n          }]\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    trovaRisultato();\n    if (window.localStorage.getItem('role') === affiliato) {\n      stopLoading();\n    }\n  }, []);\n  const getLightTheme = () => {\n    let horizontalOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: .8,\n      plugins: {\n        legend: {\n          labels: {\n            color: '#495057'\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        },\n        y: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        }\n      }\n    };\n    return {\n      horizontalOptions\n    };\n  };\n  const cambiaDati = async e => {\n    setValue1(e.value);\n    var url = 'statistic/ordersaffiliatestatistic?' + e.value.code;\n    await APIRequest('GET', url).then(res => {\n      var date = [];\n      var dati = [];\n      var label = [];\n      res.data.forEach(element => {\n        date.push(new Date(element.orderDate).toString() !== 'Invalid Date' ? new Date(element.orderDate).toLocaleDateString() : element.orderDate);\n        dati.push(parseFloat(element.total));\n        label.push('');\n      });\n      setBasicData({\n        labels: date,\n        datasets: [{\n          label: 'Fatturato',\n          backgroundColor: '#8EB6DC',\n          data: dati\n        }]\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  };\n  const {\n    horizontalOptions\n  } = getLightTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-3 col-lg-2 mb-3\",\n      children: /*#__PURE__*/_jsxDEV(SelectButton, {\n        value: value1,\n        options: options,\n        optionLabel: 'name',\n        onChange: e => cambiaDati(e),\n        className: \"selectTrend d-flex flex-column\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-9 col-lg-10\",\n      children: /*#__PURE__*/_jsxDEV(Chart, {\n        type: \"bar\",\n        data: basicData,\n        options: horizontalOptions\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 9\n  }, this);\n};\n_s(ChartHorizontal, \"KmoMpAk3ERb+3qEMrpmaaaKzNy8=\");\n_c = ChartHorizontal;\nvar _c;\n$RefreshReg$(_c, \"ChartHorizontal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Chart", "APIRequest", "<PERSON><PERSON>", "SelectButton", "affiliato", "stopLoading", "jsxDEV", "_jsxDEV", "ChartHorizontal", "props", "_s", "basicData", "setBasicData", "value1", "setValue1", "name", "<PERSON><PERSON><PERSON><PERSON>", "code", "options", "UltDodMes", "UltTreMes", "trovaRisultato", "then", "res", "date", "dati", "label", "data", "for<PERSON>ach", "element", "push", "orderDate", "parseFloat", "total", "labels", "datasets", "backgroundColor", "catch", "e", "console", "log", "window", "localStorage", "getItem", "getLightTheme", "horizontalOptions", "maintainAspectRatio", "aspectRatio", "plugins", "legend", "color", "scales", "x", "ticks", "grid", "y", "cambiaDati", "value", "url", "Date", "toString", "toLocaleDateString", "className", "children", "optionLabel", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/statistiche/chartHorizontal.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useEffect } from \"react\";\nimport { Chart } from 'primereact/chart';\nimport { APIRequest } from \"../apireq\";\nimport { <PERSON><PERSON> } from \"../../traduttore/const\";\nimport { SelectButton } from 'primereact/selectbutton';\nimport { affiliato } from \"../../route\";\nimport { stopLoading } from \"../stopLoading\";\n\nexport const ChartHorizontal = (props) => {\n    const [basicData, setBasicData] = useState([]);\n    const [value1, setValue1] = useState({ name: Costanti.AnnCorr, code: 'ref=current&type=year&qty=1' });\n    const options = [\n        { name: Costanti.AnnCorr, code: 'ref=current&type=year&qty=1' },\n        { name: Costanti.UltDodMes, code: 'ref=last&type=year&qty=1' },\n        { name: Costanti.UltTreMes, code: 'ref=last&type=month&qty=3' }\n    ]\n    useEffect(() => {\n        async function trovaRisultato() {\n            await APIRequest('GET', 'statistic/ordersaffiliatestatistic?ref=current&type=year&qty=1')\n                .then(res => {\n                    var date = [];\n                    var dati = []\n                    var label = []\n                    res.data.forEach((element) => {\n                        date.push(element.orderDate)\n                        dati.push(parseFloat(element.total))\n                        label.push('')\n                    })\n                    setBasicData({\n                        labels: date,\n                        datasets: [\n                            {\n                                label: 'Fatturato',\n                                backgroundColor: '#8EB6DC',\n                                data: dati\n                            }\n                        ]\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        trovaRisultato();\n        if (window.localStorage.getItem('role') === affiliato) {\n            stopLoading()\n        }\n    }, [])\n    const getLightTheme = () => {\n        let horizontalOptions = {\n            maintainAspectRatio: false,\n            aspectRatio: .8,\n            plugins: {\n                legend: {\n                    labels: {\n                        color: '#495057'\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                }\n            }\n        };\n        return {\n            horizontalOptions\n        }\n    }\n    const cambiaDati = async (e) => {\n        setValue1(e.value)\n        var url = 'statistic/ordersaffiliatestatistic?' + e.value.code\n        await APIRequest('GET', url)\n            .then(res => {\n                var date = [];\n                var dati = []\n                var label = []\n                res.data.forEach((element) => {\n                    date.push(new Date(element.orderDate).toString() !== 'Invalid Date' ? new Date(element.orderDate).toLocaleDateString() : element.orderDate)\n                    dati.push(parseFloat(element.total))\n                    label.push('')\n                })\n                setBasicData({\n                    labels: date,\n                    datasets: [\n                        {\n                            label: 'Fatturato',\n                            backgroundColor: '#8EB6DC',\n                            data: dati\n                        }\n                    ]\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    const { horizontalOptions } = getLightTheme();\n    return (\n        <div className=\"row\">\n            <div className=\"col-12 col-md-3 col-lg-2 mb-3\">\n                <SelectButton value={value1} options={options} optionLabel={'name'} onChange={(e) => cambiaDati(e)} className='selectTrend d-flex flex-column' />\n            </div>\n            <div className=\"col-12 col-md-9 col-lg-10\">\n                <Chart type=\"bar\" data={basicData} options={horizontalOptions} />\n            </div>\n        </div>\n    )\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,OAAO,MAAMC,eAAe,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC;IAAEiB,IAAI,EAAEb,QAAQ,CAACc,OAAO;IAAEC,IAAI,EAAE;EAA8B,CAAC,CAAC;EACrG,MAAMC,OAAO,GAAG,CACZ;IAAEH,IAAI,EAAEb,QAAQ,CAACc,OAAO;IAAEC,IAAI,EAAE;EAA8B,CAAC,EAC/D;IAAEF,IAAI,EAAEb,QAAQ,CAACiB,SAAS;IAAEF,IAAI,EAAE;EAA2B,CAAC,EAC9D;IAAEF,IAAI,EAAEb,QAAQ,CAACkB,SAAS;IAAEH,IAAI,EAAE;EAA4B,CAAC,CAClE;EACDlB,SAAS,CAAC,MAAM;IACZ,eAAesB,cAAcA,CAAA,EAAG;MAC5B,MAAMpB,UAAU,CAAC,KAAK,EAAE,gEAAgE,CAAC,CACpFqB,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,IAAI,GAAG,EAAE;QACb,IAAIC,IAAI,GAAG,EAAE;QACb,IAAIC,KAAK,GAAG,EAAE;QACdH,GAAG,CAACI,IAAI,CAACC,OAAO,CAAEC,OAAO,IAAK;UAC1BL,IAAI,CAACM,IAAI,CAACD,OAAO,CAACE,SAAS,CAAC;UAC5BN,IAAI,CAACK,IAAI,CAACE,UAAU,CAACH,OAAO,CAACI,KAAK,CAAC,CAAC;UACpCP,KAAK,CAACI,IAAI,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC;QACFlB,YAAY,CAAC;UACTsB,MAAM,EAAEV,IAAI;UACZW,QAAQ,EAAE,CACN;YACIT,KAAK,EAAE,WAAW;YAClBU,eAAe,EAAE,SAAS;YAC1BT,IAAI,EAAEF;UACV,CAAC;QAET,CAAC,CAAC;MACN,CAAC,CAAC,CAACY,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACAjB,cAAc,CAAC,CAAC;IAChB,IAAIoB,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAKvC,SAAS,EAAE;MACnDC,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMuC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIC,iBAAiB,GAAG;MACpBC,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJf,MAAM,EAAE;YACJgB,KAAK,EAAE;UACX;QACJ;MACJ,CAAC;MACDC,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAE;UACX,CAAC;UACDI,IAAI,EAAE;YACFJ,KAAK,EAAE;UACX;QACJ,CAAC;QACDK,CAAC,EAAE;UACCF,KAAK,EAAE;YACHH,KAAK,EAAE;UACX,CAAC;UACDI,IAAI,EAAE;YACFJ,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC;IACD,OAAO;MACHL;IACJ,CAAC;EACL,CAAC;EACD,MAAMW,UAAU,GAAG,MAAOlB,CAAC,IAAK;IAC5BxB,SAAS,CAACwB,CAAC,CAACmB,KAAK,CAAC;IAClB,IAAIC,GAAG,GAAG,qCAAqC,GAAGpB,CAAC,CAACmB,KAAK,CAACxC,IAAI;IAC9D,MAAMhB,UAAU,CAAC,KAAK,EAAEyD,GAAG,CAAC,CACvBpC,IAAI,CAACC,GAAG,IAAI;MACT,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,KAAK,GAAG,EAAE;MACdH,GAAG,CAACI,IAAI,CAACC,OAAO,CAAEC,OAAO,IAAK;QAC1BL,IAAI,CAACM,IAAI,CAAC,IAAI6B,IAAI,CAAC9B,OAAO,CAACE,SAAS,CAAC,CAAC6B,QAAQ,CAAC,CAAC,KAAK,cAAc,GAAG,IAAID,IAAI,CAAC9B,OAAO,CAACE,SAAS,CAAC,CAAC8B,kBAAkB,CAAC,CAAC,GAAGhC,OAAO,CAACE,SAAS,CAAC;QAC3IN,IAAI,CAACK,IAAI,CAACE,UAAU,CAACH,OAAO,CAACI,KAAK,CAAC,CAAC;QACpCP,KAAK,CAACI,IAAI,CAAC,EAAE,CAAC;MAClB,CAAC,CAAC;MACFlB,YAAY,CAAC;QACTsB,MAAM,EAAEV,IAAI;QACZW,QAAQ,EAAE,CACN;UACIT,KAAK,EAAE,WAAW;UAClBU,eAAe,EAAE,SAAS;UAC1BT,IAAI,EAAEF;QACV,CAAC;MAET,CAAC,CAAC;IACN,CAAC,CAAC,CAACY,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;EACV,CAAC;EACD,MAAM;IAAEO;EAAkB,CAAC,GAAGD,aAAa,CAAC,CAAC;EAC7C,oBACIrC,OAAA;IAAKuD,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAChBxD,OAAA;MAAKuD,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC1CxD,OAAA,CAACJ,YAAY;QAACsD,KAAK,EAAE5C,MAAO;QAACK,OAAO,EAAEA,OAAQ;QAAC8C,WAAW,EAAE,MAAO;QAACC,QAAQ,EAAG3B,CAAC,IAAKkB,UAAU,CAAClB,CAAC,CAAE;QAACwB,SAAS,EAAC;MAAgC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChJ,CAAC,eACN9D,OAAA;MAAKuD,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACtCxD,OAAA,CAACP,KAAK;QAACsE,IAAI,EAAC,KAAK;QAAC3C,IAAI,EAAEhB,SAAU;QAACO,OAAO,EAAE2B;MAAkB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA3D,EAAA,CA/GYF,eAAe;AAAA+D,EAAA,GAAf/D,eAAe;AAAA,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}