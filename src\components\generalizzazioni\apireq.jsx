import axios from "axios";
import { error } from "../route";

// Backend Endpoints Configuration
const vercelBackend = 'https://ep-backend-zeta.vercel.app'  // Primary staging backend
const localBackend = 'http://localhost:3001'  // Local backend
const localBackendAlt = 'http://localhost:3002'  // Alternative local backend

// Smart backend selection with fallback
let currentBackend = null;

// Initialize currentBackend immediately
const initializeBackend = () => {
  if (process.env.REACT_APP_API_URL) {
    currentBackend = process.env.REACT_APP_API_URL;
  } else {
    currentBackend = process.env.NODE_ENV === 'production' ? vercelBackend : localBackend;
  }

  return currentBackend;
};

// Initialize on module load
initializeBackend();

const getBaseURL = () => {
  // Return current backend or initialize if needed
  return currentBackend || initializeBackend();
};

export const baseURL = getBaseURL();

// Debug logging
console.log('🔧 API Configuration:');
console.log('📍 Base URL:', baseURL);
console.log('🌍 Environment:', process.env.NODE_ENV);
console.log('🔗 API URL Override:', process.env.REACT_APP_API_URL);

// Proxy configuration for different environments
export const baseProxy = (() => {
  // For Vercel backend, no proxy needed
  if (baseURL.includes('vercel.app')) {
    return '';
  }
  // For production domains, use /api/ prefix
  if (window.location.href.includes('eprocurement.winet') ||
      window.location.href.includes('eprocurement.tmselezioni')) {
    return '/api/';
  }
  // For local development, no prefix
  return '';
})();

// Debug logging for proxy
console.log('🔀 Proxy Configuration:', baseProxy);
console.log('🌐 Window Location:', window.location.href);
console.log('🎯 Final API Endpoint:', baseURL + baseProxy);

// Backend Health Check
let backendHealthStatus = 'unknown';
let lastHealthCheck = null;

const checkBackendHealth = async (showToast = false) => {
  const backendsToTry = [
    localBackend,     // Try local first (3001)
    localBackendAlt,  // Then alternative local (3002)
    vercelBackend     // Then staging as fallback
  ];

  for (const backend of backendsToTry) {
    try {
      console.log(`🔍 Checking backend health: ${backend}`);
      const response = await fetch(backend + '/health', {
        method: 'GET',
        timeout: 3000
      });

      if (response.ok) {
        // Update current backend if different
        if (currentBackend !== backend) {
          console.log(`🔄 Switching backend from ${currentBackend} to ${backend}`);
          currentBackend = backend;
        }

        backendHealthStatus = 'online';
        console.log(`✅ Backend is online: ${backend}`);

        if (showToast && process.env.REACT_APP_DEBUG === 'true') {
          showBackendNotification('success', '✅ Backend Connected',
            `Successfully connected to ${backend}`);
        }

        lastHealthCheck = new Date();
        return; // Exit on first successful connection
      }
    } catch (error) {
      console.log(`❌ Backend ${backend} failed: ${error.message}`);
    }
  }

  // All backends failed
  backendHealthStatus = 'offline';
  console.warn('⚠️ All backends failed');

  if (showToast && process.env.REACT_APP_DEBUG === 'true') {
    showBackendNotification('error', '❌ All Backends Offline',
      'Cannot connect to any backend. Please check your connection.');
  }

  lastHealthCheck = new Date();
};

const showBackendNotification = (type, title, message) => {
  // Solo log in console, nessuna notifica visiva
  if (process.env.REACT_APP_DEBUG === 'true') {
    console.log(`${type === 'success' ? '✅' : '❌'} ${title}: ${message}`);
  }
};

// Indicatori di stato rimossi - gestiti dal BackendTestButton
const createStatusIndicator = () => {
  // Non creare più indicatori fissi
  return null;
};

const updateStatusIndicator = (status, url) => {
  // Non aggiornare più indicatori fissi
  // Il BackendTestButton gestisce già lo stato
};

// Check backend health on load (silenzioso)
setTimeout(() => {
  checkBackendHealth(false); // false = nessun toast
}, 1000);

/* Google reCaptcha v2 Site Key */
const gKeyLocal = "6LfpdiIeAAAAACLH86utUFKqupLyW7ZcQZJb0N03" // localhost
const gKeyColl = "6LdiNykeAAAAACcZzR_bhiiTC7eePzj7lMrsRANx" // collaudo
const gKeyProd = "6LekRnkeAAAAAJlPYR3cIG8NrFs5BNf-P3SQa4OU" // produzione
export const SITE_KEY = window.location.href.includes('eprocurement.winet') ? gKeyProd : (window.location.href.includes('centralunit.viniexport') ? gKeyColl : gKeyLocal) ;

export const APIRequest = (method, path, data) => {

  return new Promise((resolve, reject) => {
    if (typeof method === "string" && typeof path === "string") {

      // Get current backend URL (dynamic) with safe fallback
      const currentURL = currentBackend || baseURL || 'http://localhost:3002';
      const safeProxy = baseProxy || '';

      // Simple and safe URL construction
      const parts = [
        currentURL.replace(/\/$/, ''), // Remove trailing slash
        safeProxy.replace(/^\/|\/$/g, ''), // Remove leading/trailing slashes
        path.replace(/^\//, '') // Remove leading slash
      ].filter(part => part.length > 0); // Remove empty parts

      const fullURL = parts.join('/');

      // Log solo in modalità debug
      if (process.env.REACT_APP_DEBUG === 'true') {
        console.log('🔗 API Request:', method, fullURL);
      }

      // Validate URL before making request
      if (!fullURL || fullURL === 'nullundefinedauth/') {
        console.error('❌ Invalid URL constructed:', fullURL);
        throw new Error('Invalid API URL constructed');
      }

      axios({
        timeout: 60 * 60 * 1000,
        method: method,
        url: fullURL,
        data: data,
        headers: {
          auth: localStorage.login_token,
          accept: "application/json",
          "Content-Type": "application/json"
        },

      })
        .then((res) => {
          var newToken = res.headers.token !== undefined ? res.headers.token : (res.data.token !== undefined ? res.data.token : localStorage.getItem("login_token"));
          if (newToken !== "") {
            localStorage.setItem("login_token", newToken);
          }
          resolve(res);
        })
        .catch((e) => {
          // Enhanced error logging - solo in modalità debug
          if (process.env.REACT_APP_DEBUG === 'true') {
            console.error('🚨 API Request Error:', {
              url: baseURL + baseProxy + path,
              method: method,
              error: e,
              response: e.response,
              status: e.response?.status,
              data: e.response?.data,
              message: e.message
            });
          }

          // Check if this is a connection error (backend offline)
          if (!e.response || e.code === 'NETWORK_ERROR' || e.message.includes('Network Error')) {
            if (backendHealthStatus !== 'offline') {
              backendHealthStatus = 'offline';
              // Log silenzioso per connessione persa
              console.warn('⚠️ Backend connection lost:', currentURL);

              // Try to switch to a working backend
              checkBackendHealth(false).then(() => {
                // If we found a working backend, retry the request
                if (backendHealthStatus === 'online' && currentBackend !== currentURL) {
                  if (process.env.REACT_APP_DEBUG === 'true') {
                    console.log('🔄 Retrying request with new backend:', currentBackend);
                  }
                  // Retry the same request with new backend
                  APIRequest(method, path, data).then(resolve).catch(reject);
                  return;
                }
              });
            }
          }

          if (e.response !== undefined) {
            if (
              e.response.data === "invalid password" ||
              e.response.data === "username not found"
            ) {
              alert(e.response.data);
            } else if (e.response.status === 401) {
              localStorage.setItem("login_token", "");
              setTimeout(() => {
                window.location.pathname = error;
              }, 4000);
            } else if (e.response.status === 500) {
              window.sessionStorage.setItem(
                "Error",
                JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data)
              );
              window.sessionStorage.setItem("CodeError", e.response.status);
              setTimeout(() => {
                window.location.pathname = error;
              }, 4000);
            }
          } else {
            if (e.response !== undefined) {
              window.sessionStorage.setItem(
                "Error",
                JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data)
              );
              window.sessionStorage.setItem("CodeError", e.response.status);
              setTimeout(() => {
                window.location.pathname = error;
              }, 4000);
            }
          }
          reject(e);
        });
    }
  });
};
