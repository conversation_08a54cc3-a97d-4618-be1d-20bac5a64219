export { NetworkAnalysisComputed as NetworkAnalysis };
declare const NetworkAnalysisComputed: typeof NetworkAnalysis & {
    request: (dependencies: import("../index.js").DevtoolsLog, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../index.js").Artifacts.NetworkAnalysis>;
};
declare class NetworkAnalysis {
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} records
     * @return {LH.Util.StrictOmit<LH.Artifacts.NetworkAnalysis, 'throughput'>}
     */
    static computeRTTAndServerResponseTime(records: Array<LH.Artifacts.NetworkRequest>): LH.Util.StrictOmit<LH.Artifacts.NetworkAnalysis, 'throughput'>;
    /**
     * @param {LH.DevtoolsLog} devtoolsLog
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Artifacts.NetworkAnalysis>}
     */
    static compute_(devtoolsLog: import("../index.js").DevtoolsLog, context: LH.Artifacts.ComputedContext): Promise<LH.Artifacts.NetworkAnalysis>;
}
//# sourceMappingURL=network-analysis.d.ts.map