{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"prefixCls\", \"style\", \"className\", \"children\", \"direction\", \"type\", \"labelPlacement\", \"iconPrefix\", \"status\", \"size\", \"current\", \"progressDot\", \"stepIcon\", \"initial\", \"icons\", \"onChange\"];\n\n/* eslint react/no-did-mount-set-state: 0, react/prop-types: 0 */\nimport React, { cloneElement } from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport classNames from 'classnames';\nimport Step from './Step';\nvar Steps = /*#__PURE__*/function (_React$Component) {\n  _inherits(Steps, _React$Component);\n  var _super = _createSuper(Steps);\n  function Steps() {\n    var _this;\n    _classCallCheck(this, Steps);\n    _this = _super.apply(this, arguments);\n    _this.onStepClick = function (next) {\n      var _this$props = _this.props,\n        onChange = _this$props.onChange,\n        current = _this$props.current;\n      if (onChange && current !== next) {\n        onChange(next);\n      }\n    };\n    return _this;\n  }\n  _createClass(Steps, [{\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n        _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        _this$props2$style = _this$props2.style,\n        style = _this$props2$style === void 0 ? {} : _this$props2$style,\n        className = _this$props2.className,\n        children = _this$props2.children,\n        direction = _this$props2.direction,\n        type = _this$props2.type,\n        labelPlacement = _this$props2.labelPlacement,\n        iconPrefix = _this$props2.iconPrefix,\n        status = _this$props2.status,\n        size = _this$props2.size,\n        current = _this$props2.current,\n        progressDot = _this$props2.progressDot,\n        stepIcon = _this$props2.stepIcon,\n        initial = _this$props2.initial,\n        icons = _this$props2.icons,\n        onChange = _this$props2.onChange,\n        restProps = _objectWithoutProperties(_this$props2, _excluded);\n      var isNav = type === 'navigation';\n      var adjustedLabelPlacement = progressDot ? 'vertical' : labelPlacement;\n      var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label-\").concat(adjustedLabelPlacement), direction === 'horizontal'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dot\"), !!progressDot), _defineProperty(_classNames, \"\".concat(prefixCls, \"-navigation\"), isNav), _classNames));\n      return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n        className: classString,\n        style: style\n      }, restProps), toArray(children).map(function (child, index) {\n        var stepNumber = initial + index;\n        var childProps = _objectSpread({\n          stepNumber: \"\".concat(stepNumber + 1),\n          stepIndex: stepNumber,\n          key: stepNumber,\n          prefixCls: prefixCls,\n          iconPrefix: iconPrefix,\n          wrapperStyle: style,\n          progressDot: progressDot,\n          stepIcon: stepIcon,\n          icons: icons,\n          onStepClick: onChange && _this2.onStepClick\n        }, child.props); // fix tail color\n\n        if (status === 'error' && index === current - 1) {\n          childProps.className = \"\".concat(prefixCls, \"-next-error\");\n        }\n        if (!child.props.status) {\n          if (stepNumber === current) {\n            childProps.status = status;\n          } else if (stepNumber < current) {\n            childProps.status = 'finish';\n          } else {\n            childProps.status = 'wait';\n          }\n        }\n        childProps.active = stepNumber === current;\n        return /*#__PURE__*/cloneElement(child, childProps);\n      }));\n    }\n  }]);\n  return Steps;\n}(React.Component);\nexport { Steps as default };\nSteps.Step = Step;\nSteps.defaultProps = {\n  type: 'default',\n  prefixCls: 'rc-steps',\n  iconPrefix: 'rc',\n  direction: 'horizontal',\n  labelPlacement: 'horizontal',\n  initial: 0,\n  current: 0,\n  status: 'process',\n  size: '',\n  progressDot: false\n};", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_objectWithoutProperties", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "_excluded", "React", "cloneElement", "toArray", "classNames", "Step", "Steps", "_React$Component", "_super", "_this", "apply", "arguments", "onStepClick", "next", "_this$props", "props", "onChange", "current", "key", "value", "render", "_classNames", "_this2", "_this$props2", "prefixCls", "_this$props2$style", "style", "className", "children", "direction", "type", "labelPlacement", "iconPrefix", "status", "size", "progressDot", "stepIcon", "initial", "icons", "restProps", "isNav", "adjustedLabelPlacement", "classString", "concat", "createElement", "Object", "assign", "map", "child", "index", "<PERSON><PERSON><PERSON><PERSON>", "childProps", "stepIndex", "wrapperStyle", "active", "Component", "default", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-steps/es/Steps.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"prefixCls\", \"style\", \"className\", \"children\", \"direction\", \"type\", \"labelPlacement\", \"iconPrefix\", \"status\", \"size\", \"current\", \"progressDot\", \"stepIcon\", \"initial\", \"icons\", \"onChange\"];\n\n/* eslint react/no-did-mount-set-state: 0, react/prop-types: 0 */\nimport React, { cloneElement } from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport classNames from 'classnames';\nimport Step from './Step';\n\nvar Steps = /*#__PURE__*/function (_React$Component) {\n  _inherits(Steps, _React$Component);\n\n  var _super = _createSuper(Steps);\n\n  function Steps() {\n    var _this;\n\n    _classCallCheck(this, Steps);\n\n    _this = _super.apply(this, arguments);\n\n    _this.onStepClick = function (next) {\n      var _this$props = _this.props,\n          onChange = _this$props.onChange,\n          current = _this$props.current;\n\n      if (onChange && current !== next) {\n        onChange(next);\n      }\n    };\n\n    return _this;\n  }\n\n  _createClass(Steps, [{\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n          _this2 = this;\n\n      var _this$props2 = this.props,\n          prefixCls = _this$props2.prefixCls,\n          _this$props2$style = _this$props2.style,\n          style = _this$props2$style === void 0 ? {} : _this$props2$style,\n          className = _this$props2.className,\n          children = _this$props2.children,\n          direction = _this$props2.direction,\n          type = _this$props2.type,\n          labelPlacement = _this$props2.labelPlacement,\n          iconPrefix = _this$props2.iconPrefix,\n          status = _this$props2.status,\n          size = _this$props2.size,\n          current = _this$props2.current,\n          progressDot = _this$props2.progressDot,\n          stepIcon = _this$props2.stepIcon,\n          initial = _this$props2.initial,\n          icons = _this$props2.icons,\n          onChange = _this$props2.onChange,\n          restProps = _objectWithoutProperties(_this$props2, _excluded);\n\n      var isNav = type === 'navigation';\n      var adjustedLabelPlacement = progressDot ? 'vertical' : labelPlacement;\n      var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label-\").concat(adjustedLabelPlacement), direction === 'horizontal'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dot\"), !!progressDot), _defineProperty(_classNames, \"\".concat(prefixCls, \"-navigation\"), isNav), _classNames));\n      return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n        className: classString,\n        style: style\n      }, restProps), toArray(children).map(function (child, index) {\n        var stepNumber = initial + index;\n\n        var childProps = _objectSpread({\n          stepNumber: \"\".concat(stepNumber + 1),\n          stepIndex: stepNumber,\n          key: stepNumber,\n          prefixCls: prefixCls,\n          iconPrefix: iconPrefix,\n          wrapperStyle: style,\n          progressDot: progressDot,\n          stepIcon: stepIcon,\n          icons: icons,\n          onStepClick: onChange && _this2.onStepClick\n        }, child.props); // fix tail color\n\n\n        if (status === 'error' && index === current - 1) {\n          childProps.className = \"\".concat(prefixCls, \"-next-error\");\n        }\n\n        if (!child.props.status) {\n          if (stepNumber === current) {\n            childProps.status = status;\n          } else if (stepNumber < current) {\n            childProps.status = 'finish';\n          } else {\n            childProps.status = 'wait';\n          }\n        }\n\n        childProps.active = stepNumber === current;\n        return /*#__PURE__*/cloneElement(child, childProps);\n      }));\n    }\n  }]);\n\n  return Steps;\n}(React.Component);\n\nexport { Steps as default };\nSteps.Step = Step;\nSteps.defaultProps = {\n  type: 'default',\n  prefixCls: 'rc-steps',\n  iconPrefix: 'rc',\n  direction: 'horizontal',\n  labelPlacement: 'horizontal',\n  initial: 0,\n  current: 0,\n  status: 'process',\n  size: '',\n  progressDot: false\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC;;AAE5M;AACA,OAAOC,KAAK,IAAIC,YAAY,QAAQ,OAAO;AAC3C,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,QAAQ;AAEzB,IAAIC,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnDT,SAAS,CAACQ,KAAK,EAAEC,gBAAgB,CAAC;EAElC,IAAIC,MAAM,GAAGT,YAAY,CAACO,KAAK,CAAC;EAEhC,SAASA,KAAKA,CAAA,EAAG;IACf,IAAIG,KAAK;IAETb,eAAe,CAAC,IAAI,EAAEU,KAAK,CAAC;IAE5BG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAErCF,KAAK,CAACG,WAAW,GAAG,UAAUC,IAAI,EAAE;MAClC,IAAIC,WAAW,GAAGL,KAAK,CAACM,KAAK;QACzBC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QAC/BC,OAAO,GAAGH,WAAW,CAACG,OAAO;MAEjC,IAAID,QAAQ,IAAIC,OAAO,KAAKJ,IAAI,EAAE;QAChCG,QAAQ,CAACH,IAAI,CAAC;MAChB;IACF,CAAC;IAED,OAAOJ,KAAK;EACd;EAEAZ,YAAY,CAACS,KAAK,EAAE,CAAC;IACnBY,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;QACXC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAACR,KAAK;QACzBS,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,kBAAkB,GAAGF,YAAY,CAACG,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;QAC/DE,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,QAAQ,GAAGL,YAAY,CAACK,QAAQ;QAChCC,SAAS,GAAGN,YAAY,CAACM,SAAS;QAClCC,IAAI,GAAGP,YAAY,CAACO,IAAI;QACxBC,cAAc,GAAGR,YAAY,CAACQ,cAAc;QAC5CC,UAAU,GAAGT,YAAY,CAACS,UAAU;QACpCC,MAAM,GAAGV,YAAY,CAACU,MAAM;QAC5BC,IAAI,GAAGX,YAAY,CAACW,IAAI;QACxBjB,OAAO,GAAGM,YAAY,CAACN,OAAO;QAC9BkB,WAAW,GAAGZ,YAAY,CAACY,WAAW;QACtCC,QAAQ,GAAGb,YAAY,CAACa,QAAQ;QAChCC,OAAO,GAAGd,YAAY,CAACc,OAAO;QAC9BC,KAAK,GAAGf,YAAY,CAACe,KAAK;QAC1BtB,QAAQ,GAAGO,YAAY,CAACP,QAAQ;QAChCuB,SAAS,GAAG5C,wBAAwB,CAAC4B,YAAY,EAAEvB,SAAS,CAAC;MAEjE,IAAIwC,KAAK,GAAGV,IAAI,KAAK,YAAY;MACjC,IAAIW,sBAAsB,GAAGN,WAAW,GAAG,UAAU,GAAGJ,cAAc;MACtE,IAAIW,WAAW,GAAGtC,UAAU,CAACoB,SAAS,EAAE,EAAE,CAACmB,MAAM,CAACnB,SAAS,EAAE,GAAG,CAAC,CAACmB,MAAM,CAACd,SAAS,CAAC,EAAEF,SAAS,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAE3B,eAAe,CAAC2B,WAAW,EAAE,EAAE,CAACsB,MAAM,CAACnB,SAAS,EAAE,GAAG,CAAC,CAACmB,MAAM,CAACT,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAExC,eAAe,CAAC2B,WAAW,EAAE,EAAE,CAACsB,MAAM,CAACnB,SAAS,EAAE,SAAS,CAAC,CAACmB,MAAM,CAACF,sBAAsB,CAAC,EAAEZ,SAAS,KAAK,YAAY,CAAC,EAAEnC,eAAe,CAAC2B,WAAW,EAAE,EAAE,CAACsB,MAAM,CAACnB,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC,CAACW,WAAW,CAAC,EAAEzC,eAAe,CAAC2B,WAAW,EAAE,EAAE,CAACsB,MAAM,CAACnB,SAAS,EAAE,aAAa,CAAC,EAAEgB,KAAK,CAAC,EAAEnB,WAAW,CAAC,CAAC;MAC3d,OAAO,aAAapB,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;QAC3DnB,SAAS,EAAEe,WAAW;QACtBhB,KAAK,EAAEA;MACT,CAAC,EAAEa,SAAS,CAAC,EAAEpC,OAAO,CAACyB,QAAQ,CAAC,CAACmB,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;QAC3D,IAAIC,UAAU,GAAGb,OAAO,GAAGY,KAAK;QAEhC,IAAIE,UAAU,GAAG1D,aAAa,CAAC;UAC7ByD,UAAU,EAAE,EAAE,CAACP,MAAM,CAACO,UAAU,GAAG,CAAC,CAAC;UACrCE,SAAS,EAAEF,UAAU;UACrBhC,GAAG,EAAEgC,UAAU;UACf1B,SAAS,EAAEA,SAAS;UACpBQ,UAAU,EAAEA,UAAU;UACtBqB,YAAY,EAAE3B,KAAK;UACnBS,WAAW,EAAEA,WAAW;UACxBC,QAAQ,EAAEA,QAAQ;UAClBE,KAAK,EAAEA,KAAK;UACZ1B,WAAW,EAAEI,QAAQ,IAAIM,MAAM,CAACV;QAClC,CAAC,EAAEoC,KAAK,CAACjC,KAAK,CAAC,CAAC,CAAC;;QAGjB,IAAIkB,MAAM,KAAK,OAAO,IAAIgB,KAAK,KAAKhC,OAAO,GAAG,CAAC,EAAE;UAC/CkC,UAAU,CAACxB,SAAS,GAAG,EAAE,CAACgB,MAAM,CAACnB,SAAS,EAAE,aAAa,CAAC;QAC5D;QAEA,IAAI,CAACwB,KAAK,CAACjC,KAAK,CAACkB,MAAM,EAAE;UACvB,IAAIiB,UAAU,KAAKjC,OAAO,EAAE;YAC1BkC,UAAU,CAAClB,MAAM,GAAGA,MAAM;UAC5B,CAAC,MAAM,IAAIiB,UAAU,GAAGjC,OAAO,EAAE;YAC/BkC,UAAU,CAAClB,MAAM,GAAG,QAAQ;UAC9B,CAAC,MAAM;YACLkB,UAAU,CAAClB,MAAM,GAAG,MAAM;UAC5B;QACF;QAEAkB,UAAU,CAACG,MAAM,GAAGJ,UAAU,KAAKjC,OAAO;QAC1C,OAAO,aAAaf,YAAY,CAAC8C,KAAK,EAAEG,UAAU,CAAC;MACrD,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAO7C,KAAK;AACd,CAAC,CAACL,KAAK,CAACsD,SAAS,CAAC;AAElB,SAASjD,KAAK,IAAIkD,OAAO;AACzBlD,KAAK,CAACD,IAAI,GAAGA,IAAI;AACjBC,KAAK,CAACmD,YAAY,GAAG;EACnB3B,IAAI,EAAE,SAAS;EACfN,SAAS,EAAE,UAAU;EACrBQ,UAAU,EAAE,IAAI;EAChBH,SAAS,EAAE,YAAY;EACvBE,cAAc,EAAE,YAAY;EAC5BM,OAAO,EAAE,CAAC;EACVpB,OAAO,EAAE,CAAC;EACVgB,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE,EAAE;EACRC,WAAW,EAAE;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}