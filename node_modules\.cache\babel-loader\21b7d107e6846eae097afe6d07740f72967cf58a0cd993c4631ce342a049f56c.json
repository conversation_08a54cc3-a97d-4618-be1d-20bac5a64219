{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\ufficioAcquisti.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioAcquisti - operazioni sull'ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Button } from 'primereact/button';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { InputText } from 'primereact/inputtext';\nimport { chain, chainControlloLottiEScadenze } from '../../components/route';\nimport { Toast } from 'primereact/toast';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport { Sidebar } from 'primereact/sidebar';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DocumentiAcquisti extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      var url = 'documents?idWarehouses=' + e.value + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying, _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying = element.idSupplying) === null || _element$idSupplying === void 0 ? void 0 : _element$idSupplying.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.onRowSelect = result => {\n      if (result.tasks !== null && result.status !== 'positioned') {\n        var idFOROrd = [];\n        this.state.results.forEach(element => {\n          if (element.id === result.id) {\n            idFOROrd = element;\n          }\n        });\n        localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n        window.location.pathname = chainControlloLottiEScadenze;\n      } else {\n        this.toast.show({\n          severity: \"warn\",\n          summary: \"Attenzione\",\n          detail: \"Verificare se è presente una task per questo documento e che la stessa non sia in stato positioned prima di procedere\",\n          life: 3000\n        });\n      }\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      selectedWarehouse: null,\n      selectedDocuments: null,\n      loading: true,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      resultDialog5: false,\n      displayed: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      value: null,\n      value2: null,\n      totalRecords: 0,\n      search: '',\n      selectedMail: '',\n      param: '?idWarehouses=',\n      param2: '&idSupplying=',\n      selectedSupplyer: null,\n      deleteResultDialog: null,\n      role: localStorage.getItem('role'),\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedSupplyer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying2, _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying2 = element.idSupplying) === null || _element$idSupplying2 === void 0 ? void 0 : _element$idSupplying2.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.supplyer = [];\n    this.warehouse = [];\n    this.loadLazyTimeout = null;\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.sendMail = this.sendMail.bind(this);\n    this.hideDialogSendMail = this.hideDialogSendMail.bind(this);\n    this.senderMail = this.senderMail.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.editDocResult = this.editDocResult.bind(this);\n    this.onRowSelect = this.onRowSelect.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      this.setState({\n        selectedWarehouse: idWarehouse.code ? idWarehouse.code : idWarehouse\n      });\n      var url = 'documents?idWarehouses=' + (idWarehouse.code ? idWarehouse.code : idWarehouse) + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying3, _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying3 = element.idSupplying) === null || _element$idSupplying3 === void 0 ? void 0 : _element$idSupplying3.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName + ' ' + entry.address + ' ' + entry.citta + ' (' + entry.prov + '), ' + entry.cap,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"employees/?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'supplying/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.supplyer.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?documentType=FOR-ORDINE&idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare il documento. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    var respMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'OP_MAG' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n          opMag.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n          respMag.push({\n            label: element.first_name + ' ' + element.last_name,\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog3: true,\n      opMag: opMag,\n      mex: message,\n      respMag: respMag\n    });\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var opMag = [];\n    var respMag = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          if (element.role === 'RESP_MAG') {\n            respMag.push({\n              label: element.first_name + ' ' + element.last_name,\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.opMag = opMag;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog3: true,\n        opMag: opMag,\n        respMag: respMag,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null);\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'OP_MAG') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n                opMag.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              }\n            });\n          }\n          this.opMag = opMag;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'OP_MAG') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n              opMag.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            }\n          });\n        }\n        this.opMag = opMag;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog3: true,\n          opMag: opMag,\n          respMag: respMag,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  sendMail(result) {\n    var _result$idSupplying, _result$idSupplying2;\n    this.setState({\n      result,\n      resultDialog4: true,\n      selectedMail: ((_result$idSupplying = result.idSupplying) === null || _result$idSupplying === void 0 ? void 0 : _result$idSupplying.idRegistry.email) !== null ? (_result$idSupplying2 = result.idSupplying) === null || _result$idSupplying2 === void 0 ? void 0 : _result$idSupplying2.idRegistry.email : ''\n    });\n  }\n  hideDialogSendMail() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  async senderMail() {\n    var url = 'email/sendfororders?idDocument=' + this.state.result.id + '&emailSuppliyng=' + this.state.selectedMail;\n    await APIRequest(\"GET\", url).then(res => {\n      var _this$toast;\n      console.log(res.data);\n      (_this$toast = this.toast) === null || _this$toast === void 0 ? void 0 : _this$toast.show({\n        severity: \"success\",\n        summary: \"Ottimo !\",\n        detail: \"L'email è stata inviata con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _this$toast2, _e$response11, _e$response12;\n      console.log(e);\n      (_this$toast2 = this.toast) === null || _this$toast2 === void 0 ? void 0 : _this$toast2.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile inviare l'email. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  async editDocResult(result) {\n    if (result.tasks === null && !result.erpSync) {\n      await APIRequest(\"GET\", \"documents?documentType=FOR-ORDINE&idDocumentHead=\".concat(result.id)).then(res => {\n        console.log(res.data);\n        var products = [];\n        var totProd = 0;\n        var total = 0;\n        res.data.documentBodies.forEach(el => {\n          totProd += el.colliPreventivo;\n          var find = el.idProductsPackaging.idProduct.supplyingProducts.find(obj => obj.idSupplying.id === res.data.idSupplying.id);\n          if (find !== undefined) {\n            products.push(_objectSpread(_objectSpread({}, find), {}, {\n              quantity: el.colliPreventivo * el.idProductsPackaging.pcsXPackage,\n              moltiplicatore: el.idProductsPackaging.pcsXPackage,\n              initPrice: find.sell_in,\n              qtaIns: el.colliPreventivo,\n              idProduct2: el.idProductsPackaging.idProduct\n            }));\n          }\n        });\n        var iva = res.data.total === res.data.totalTaxed ? true : false;\n        total = new Intl.NumberFormat(\"it-IT\", {\n          style: \"currency\",\n          currency: \"EUR\",\n          maximumFractionDigits: 6\n        }).format(products.map(item => item.quantity * item.sell_in).reduce((prev, curr) => prev + curr, 0));\n        localStorage.setItem(\"Prodotti\", JSON.stringify(products));\n        localStorage.setItem(\"Cart\", JSON.stringify(products));\n        localStorage.setItem(\"DatiConsegna\", JSON.stringify({\n          idRegistry: res.data.idSupplying.idRegistry,\n          note: res.data.note,\n          termsPayment: res.data.termsPayment,\n          deliveryDate: res.data.deliveryDate,\n          iva: iva\n        }));\n        window.sessionStorage.setItem(\"idDocument\", JSON.stringify({\n          id: result.id,\n          number: result.number,\n          documentDate: result.documentDate\n        }));\n        window.sessionStorage.setItem(\"idSupplier\", res.data.idSupplying.id);\n        window.sessionStorage.setItem(\"Carrello\", totProd);\n        window.sessionStorage.setItem(\"totCart\", total);\n        window.sessionStorage.setItem('idWarehouse', JSON.stringify({\n          name: \"\".concat(res.data.idWarehouses.warehouseName, \" \").concat(res.data.idWarehouses.address, \" \").concat(res.data.idWarehouses.citta, \" (\").concat(res.data.idWarehouses.prov, \"), \").concat(res.data.idWarehouses.cap),\n          code: res.data.idWarehouses.id\n        }));\n        window.location.pathname = '/chain/Ordina';\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non siamo riusciti a reperire il documento da modificare. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Per modificare il documento \\xE8 necessario che non ci sia una task associata e che il documento non sia stato gi\\xE0 sincronizzato sull'ERP\",\n        life: 3000\n      });\n    }\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse;\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying4, _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying4 = element.idSupplying) === null || _element$idSupplying4 === void 0 ? void 0 : _element$idSupplying4.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying5, _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying5 = element.idSupplying) === null || _element$idSupplying5 === void 0 ? void 0 : _element$idSupplying5.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying6, _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying6 = element.idSupplying) === null || _element$idSupplying6 === void 0 ? void 0 : _element$idSupplying6.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response19, _e$response20;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying7, _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying7 = element.idSupplying) === null || _element$idSupplying7 === void 0 ? void 0 : _element$idSupplying7.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response21, _e$response22;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response21 = e.response) === null || _e$response21 === void 0 ? void 0 : _e$response21.data) !== undefined ? (_e$response22 = e.response) === null || _e$response22 === void 0 ? void 0 : _e$response22.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n      window.location.reload();\n    }).catch(e => {\n      var _e$response23, _e$response24;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response23 = e.response) === null || _e$response23 === void 0 ? void 0 : _e$response23.data) !== undefined ? (_e$response24 = e.response) === null || _e$response24 === void 0 ? void 0 : _e$response24.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog5: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog5: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 875,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 883,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter4 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.senderMail,\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-send mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 21\n        }, this), Costanti.Invia, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialogSendMail,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 13\n    }, this);\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 905,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 911,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 904,\n      columnNumber: 13\n    }, this);\n    var fields = [];\n    if (this.state.role === chain) {\n      fields = [{\n        selectionMode: \"multiple\",\n        headerStyle: {\n          width: \"3em\"\n        }\n      }, {\n        field: \"number\",\n        header: Costanti.NDoc,\n        body: \"nDoc\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"supplying\",\n        header: Costanti.Fornitore,\n        body: \"supplying\",\n        showHeader: true\n      }, {\n        field: \"documentDate\",\n        header: Costanti.DataDoc,\n        body: \"documentDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"deliveryDate\",\n        header: Costanti.DCons,\n        body: \"deliveryDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"tasks.manager.idUser.username\",\n        header: Costanti.Responsabile,\n        body: \"manager\",\n        showHeader: true\n      }, {\n        field: \"tasks.operator.idUser.username\",\n        header: Costanti.Operatore,\n        body: \"operator\",\n        showHeader: true\n      }, {\n        field: \"tasks.status\",\n        header: Costanti.StatoTask,\n        body: \"assigned\",\n        showHeader: true\n      }, {\n        field: \"erpSync\",\n        header: \"ERP Sync\",\n        body: \"erpSync\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"idLogEmail\",\n        header: Costanti.mail,\n        body: \"idLogEmail\",\n        showHeader: true\n      }];\n    } else {\n      fields = [{\n        field: \"number\",\n        header: Costanti.NDoc,\n        body: \"nDoc\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"supplying\",\n        header: Costanti.Fornitore,\n        body: \"supplying\",\n        showHeader: true\n      }, {\n        field: \"documentDate\",\n        header: Costanti.DataDoc,\n        body: \"documentDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"deliveryDate\",\n        header: Costanti.DCons,\n        body: \"deliveryDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"tasks.status\",\n        header: Costanti.StatoTask,\n        body: \"assigned\",\n        showHeader: true\n      }, {\n        field: \"idLogEmail\",\n        header: Costanti.mail,\n        body: \"idLogEmail\",\n        showHeader: true\n      }];\n    }\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.inviaMail,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 47\n      }, this),\n      handler: this.sendMail\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }, {\n      name: Costanti.ModificaTask,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-pencil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1027,\n        columnNumber: 50\n      }, this),\n      handler: this.onRowSelect\n    }, {\n      name: Costanti.ModificaDoc,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-pencil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1028,\n        columnNumber: 49\n      }, this),\n      handler: this.editDocResult\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1029,\n        columnNumber: 45\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1041,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DocumentiOrdineAcquisto\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1048,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton2: this.state.role === chain ? true : false,\n          actionExtraButton2: this.assegnaLavorazioni,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1089,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Acquisti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1061,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          result: this.state.results3,\n          results: this.state.result,\n          documento: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1110,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1101,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag,\n          respMag: this.state.respMag,\n          chain: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1126,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog4,\n        header: Costanti.inviaMail,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter4,\n        onHide: this.hideDialogSendMail,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: Costanti.SendMailFornMex\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1137,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(InputText, {\n              value: this.state.selectedMail,\n              onChange: e => this.setState({\n                selectedMail: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1139,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1138,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: [\"* \", Costanti.MailSepVirg]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1141,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1136,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1128,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1152,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter2,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1166,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1170,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1170,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1172,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1169,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1164,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog5,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1178,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1178,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1179,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1179,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1176,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1182,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1183,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1181,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1185,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedSupplyer,\n          options: this.supplyer,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona fornitore\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1175,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1039,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DocumentiAcquisti;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "<PERSON><PERSON>", "Dialog", "Dropdown", "JoyrideGen", "InputText", "chain", "chainControlloLottiEScadenze", "Toast", "Print", "Sidebar", "SelezionaOperatore", "VisualizzaDocumenti", "Nav", "CustomDataTable", "jsxDEV", "_jsxDEV", "DocumentiAcquisti", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "window", "sessionStorage", "setItem", "url", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "param2", "code", "lazyParams", "rows", "page", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$idSupplying", "_element$tasks", "x", "number", "type", "supplying", "idSupplying", "idRegistry", "documentDate", "documentBodies", "tasks", "erpSync", "idLogEmail", "total", "totalTaxed", "push", "results", "results5", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "onRowSelect", "result", "idFOROrd", "localStorage", "JSON", "stringify", "location", "pathname", "results2", "results3", "results4", "selectedDocuments", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "resultDialog5", "displayed", "opMag", "respMag", "mex", "value2", "search", "selectedMail", "param", "deleteResultDialog", "role", "getItem", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "_element$idSupplying2", "_element$tasks2", "_e$response3", "_e$response4", "supplyer", "warehouse", "loadLazyTimeout", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "reset", "resetDesc", "assegnaLavorazioni", "onPage", "onSort", "onFilter", "sendMail", "hideDialogSendMail", "senderMail", "<PERSON><PERSON><PERSON><PERSON>", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "editDocResult", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "parse", "_element$idSupplying3", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "address", "citta", "prov", "cap", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "documentBody", "task", "_e$response1", "_e$response10", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "el", "length", "FilterOp", "operator", "_result$idSupplying", "_result$idSupplying2", "email", "_this$toast", "setTimeout", "reload", "_this$toast2", "_e$response11", "_e$response12", "products", "totProd", "colliPreventivo", "find", "idProductsPackaging", "idProduct", "supplyingProducts", "obj", "quantity", "pcsXPackage", "moltiplicatore", "initPrice", "sell_in", "qtaIns", "idProduct2", "iva", "NumberFormat", "style", "currency", "maximumFractionDigits", "map", "item", "reduce", "prev", "curr", "note", "idWarehouses", "_e$response13", "_e$response14", "_element$idSupplying4", "_element$tasks4", "_e$response15", "_e$response16", "_element$idSupplying5", "_element$tasks5", "_e$response17", "_e$response18", "event", "clearTimeout", "_element$idSupplying6", "_element$tasks6", "_e$response19", "_e$response20", "Math", "random", "field", "_element$idSupplying7", "_element$tasks7", "_e$response21", "_e$response22", "loadLazyData", "_e$response23", "_e$response24", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "doc", "resultDialogFooter2", "resultDialogFooter3", "resultDialogFooter4", "Invia", "deleteResultDialogFooter", "icon", "Si", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "Fornitore", "DataDoc", "DCons", "Responsabile", "Operatore", "StatoTask", "mail", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "inviaMail", "assegnaLavorazione", "status2", "ModificaTask", "ModificaDoc", "Elimina", "filterDnone", "ref", "DocumentiOrdineAcquisto", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "disabledExtraButton2", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "classInputSearch", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "SendMailFornMex", "target", "MailSepVirg", "Conferma", "fontSize", "ResDeleteDoc", "Primadiproseguire", "title", "content", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/ufficioAcquisti.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioAcquisti - operazioni sull'ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Button } from 'primereact/button';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { InputText } from 'primereact/inputtext';\nimport { chain, chainControlloLottiEScadenze } from '../../components/route';\nimport { Toast } from 'primereact/toast';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport { Sidebar } from 'primereact/sidebar';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\n\nclass DocumentiAcquisti extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            selectedWarehouse: null,\n            selectedDocuments: null,\n            loading: true,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            resultDialog5: false,\n            displayed: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            value: null,\n            value2: null,\n            totalRecords: 0,\n            search: '',\n            selectedMail: '',\n            param: '?idWarehouses=',\n            param2: '&idSupplying=',\n            selectedSupplyer: null,\n            deleteResultDialog: null,\n            role: localStorage.getItem('role'),\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedSupplyer: e.value\n            });\n\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.supplyer = []\n        this.warehouse = [];\n        this.loadLazyTimeout = null;\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.sendMail = this.sendMail.bind(this);\n        this.hideDialogSendMail = this.hideDialogSendMail.bind(this);\n        this.senderMail = this.senderMail.bind(this);\n        this.selectionHandler = this.selectionHandler.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.editDocResult = this.editDocResult.bind(this);\n        this.onRowSelect = this.onRowSelect.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            this.setState({ selectedWarehouse: idWarehouse.code ? idWarehouse.code : idWarehouse });\n            var url = 'documents?idWarehouses=' + (idWarehouse.code ? idWarehouse.code : idWarehouse) + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName + ' ' + entry.address + ' ' + entry.citta + ' (' + entry.prov + '), ' + entry.cap,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"employees/?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest('GET', 'supplying/')\n            .then(res => {\n                res.data.forEach(element => {\n                    if (element && element.idRegistry) {\n                        var x = {\n                            name: element.idRegistry.firstName || 'Nome non disponibile',\n                            code: element.id || 0\n                        }\n                        this.supplyer.push(x)\n                    }\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value });\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        var url = 'documents?idWarehouses=' + e.value + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        supplying: element.idSupplying?.idRegistry.firstName,\n                        idSupplying: element.idSupplying,\n                        documentDate: element.documentDate,\n                        documentBodies: element.documentBodies,\n                        deliveryDate: element.deliveryDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idLogEmail: element.idLogEmail,\n                        total: element.total,\n                        totalTaxed: element.totalTaxed\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?documentType=FOR-ORDINE&idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        var respMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'OP_MAG' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                    opMag.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n                    respMag.push({\n                        label: element.first_name + ' ' + element.last_name,\n                        value: element.idemployee,\n                    });\n                }\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog3: true,\n            opMag: opMag,\n            mex: message,\n            respMag: respMag\n        });\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var opMag = [];\n        var respMag = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    if (element.role === 'RESP_MAG') {\n                        respMag.push({\n                            label: element.first_name + ' ' + element.last_name,\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.opMag = opMag;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog3: true,\n                opMag: opMag,\n                respMag: respMag,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'OP_MAG') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                                opMag.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }\n                        })\n                    }\n                    this.opMag = opMag;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog3: true,\n                        opMag: opMag,\n                        respMag: respMag,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'OP_MAG') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                            opMag.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }\n                    })\n                }\n                this.opMag = opMag;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog3: true,\n                    opMag: opMag,\n                    respMag: respMag,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    sendMail(result) {\n        this.setState({\n            result,\n            resultDialog4: true,\n            selectedMail: result.idSupplying?.idRegistry.email !== null ? result.idSupplying?.idRegistry.email : ''\n        });\n    }\n    hideDialogSendMail() {\n        this.setState({\n            resultDialog4: false,\n        });\n    }\n    async senderMail() {\n        var url = 'email/sendfororders?idDocument=' + this.state.result.id + '&emailSuppliyng=' + this.state.selectedMail\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                console.log(res.data)\n                this.toast?.show({\n                    severity: \"success\",\n                    summary: \"Ottimo !\",\n                    detail: \"L'email è stata inviata con successo\",\n                    life: 3000,\n                });\n                setTimeout(() => {\n                    window.location.reload();\n                }, 3000)\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast?.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile inviare l'email. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    async editDocResult(result) {\n        if (result.tasks === null && !result.erpSync) {\n            await APIRequest(\"GET\", `documents?documentType=FOR-ORDINE&idDocumentHead=${result.id}`)\n                .then((res) => {\n                    console.log(res.data)\n                    var products = []\n                    var totProd = 0\n                    var total = 0\n                    res.data.documentBodies.forEach(el => {\n                        totProd += el.colliPreventivo\n                        var find = el.idProductsPackaging.idProduct.supplyingProducts.find(obj => obj.idSupplying.id === res.data.idSupplying.id)\n                        if (find !== undefined) {\n                            products.push({ ...find, quantity: el.colliPreventivo * el.idProductsPackaging.pcsXPackage, moltiplicatore: el.idProductsPackaging.pcsXPackage, initPrice: find.sell_in, qtaIns: el.colliPreventivo, idProduct2: el.idProductsPackaging.idProduct })\n                        }\n                    })\n                    var iva = res.data.total === res.data.totalTaxed ? true : false\n                    total = new Intl.NumberFormat(\"it-IT\", {\n                        style: \"currency\",\n                        currency: \"EUR\",\n                        maximumFractionDigits: 6\n                    }).format(products.map(item => item.quantity * item.sell_in).reduce((prev, curr) => prev + curr, 0))\n                    localStorage.setItem(\"Prodotti\", JSON.stringify(products));\n                    localStorage.setItem(\"Cart\", JSON.stringify(products));\n                    localStorage.setItem(\"DatiConsegna\", JSON.stringify({ idRegistry: res.data.idSupplying.idRegistry, note: res.data.note, termsPayment: res.data.termsPayment, deliveryDate: res.data.deliveryDate, iva: iva }))\n                    window.sessionStorage.setItem(\"idDocument\", JSON.stringify({ id: result.id, number: result.number, documentDate: result.documentDate }))\n                    window.sessionStorage.setItem(\"idSupplier\", res.data.idSupplying.id)\n                    window.sessionStorage.setItem(\"Carrello\", totProd)\n                    window.sessionStorage.setItem(\"totCart\", total)\n                    window.sessionStorage.setItem('idWarehouse', JSON.stringify({ name: `${res.data.idWarehouses.warehouseName} ${res.data.idWarehouses.address} ${res.data.idWarehouses.citta} (${res.data.idWarehouses.prov}), ${res.data.idWarehouses.cap}`, code: res.data.idWarehouses.id }))\n                    window.location.pathname = '/chain/Ordina'\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non siamo riusciti a reperire il documento da modificare. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: `Per modificare il documento è necessario che non ci sia una task associata e che il documento non sia stato già sincronizzato sull'ERP`,\n                life: 3000,\n            });\n        }\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', selectedSupplyer: null });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', selectedSupplyer: null });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    selectionHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n                window.location.reload();\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false,\n        });\n    }\n    onRowSelect = (result) => {\n        if (result.tasks !== null && result.status !== 'positioned') {\n            var idFOROrd = []\n            this.state.results.forEach(element => {\n                if (element.id === result.id) {\n                    idFOROrd = element\n                }\n            })\n            localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n            window.location.pathname = chainControlloLottiEScadenze;\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione\",\n                detail: \"Verificare se è presente una task per questo documento e che la stessa non sia in stato positioned prima di procedere\",\n                life: 3000,\n            });\n        }\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog5: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog5: false\n        })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter4 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.senderMail}>\n                    {\" \"}\n                    <i className='pi pi-send mr-2'></i>{Costanti.Invia}{\" \"}\n                </Button>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialogSendMail}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        var fields = []\n        if (this.state.role === chain) {\n            fields = [\n                { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n                {\n                    field: \"number\",\n                    header: Costanti.NDoc,\n                    body: \"nDoc\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"supplying\",\n                    header: Costanti.Fornitore,\n                    body: \"supplying\",\n                    showHeader: true,\n                },\n                {\n                    field: \"documentDate\",\n                    header: Costanti.DataDoc,\n                    body: \"documentDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"deliveryDate\",\n                    header: Costanti.DCons,\n                    body: \"deliveryDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.manager.idUser.username\",\n                    header: Costanti.Responsabile,\n                    body: \"manager\",\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.operator.idUser.username\",\n                    header: Costanti.Operatore,\n                    body: \"operator\",\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.status\",\n                    header: Costanti.StatoTask,\n                    body: \"assigned\",\n                    showHeader: true,\n                },\n                {\n                    field: \"erpSync\",\n                    header: \"ERP Sync\",\n                    body: \"erpSync\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"idLogEmail\",\n                    header: Costanti.mail,\n                    body: \"idLogEmail\",\n                    showHeader: true,\n                }\n            ];\n        } else {\n            fields = [\n                {\n                    field: \"number\",\n                    header: Costanti.NDoc,\n                    body: \"nDoc\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"supplying\",\n                    header: Costanti.Fornitore,\n                    body: \"supplying\",\n                    showHeader: true,\n                },\n                {\n                    field: \"documentDate\",\n                    header: Costanti.DataDoc,\n                    body: \"documentDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"deliveryDate\",\n                    header: Costanti.DCons,\n                    body: \"deliveryDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.status\",\n                    header: Costanti.StatoTask,\n                    body: \"assigned\",\n                    showHeader: true,\n                },\n                {\n                    field: \"idLogEmail\",\n                    header: Costanti.mail,\n                    body: \"idLogEmail\",\n                    showHeader: true,\n                }\n            ];\n        }\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.inviaMail, icon: <i className=\"pi pi-send\" />, handler: this.sendMail },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n            { name: Costanti.ModificaTask, icon: <i className=\"pi pi-pencil\" />, handler: this.onRowSelect },\n            { name: Costanti.ModificaDoc, icon: <i className=\"pi pi-pencil\" />, handler: this.editDocResult },\n            { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n        ];\n\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '') {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DocumentiOrdineAcquisto}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionHandler(e)}\n                        showExtraButton2={this.state.role === chain ? true : false}\n                        actionExtraButton2={this.assegnaLavorazioni}\n                        labelExtraButton2={Costanti.assegnaLavorazioni}\n                        disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Acquisti\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        result={this.state.results3}\n                        results={this.state.result}\n                        documento={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaOperatore result={this.state.result} opMag={this.opMag} respMag={this.state.respMag} chain={true} />\n                </Dialog>\n                <Dialog\n                    visible={this.state.resultDialog4}\n                    header={Costanti.inviaMail}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter4}\n                    onHide={this.hideDialogSendMail}\n                >\n                    <div className='row'>\n                        <div className='col-12'><span>{Costanti.SendMailFornMex}</span></div>\n                        <div className='col-12'>\n                            <InputText value={this.state.selectedMail} onChange={(e) => this.setState({ selectedMail: e.target.value })} />\n                        </div>\n                        <div className='col-12'><span className='text-danger'>* {Costanti.MailSepVirg}</span></div>\n                    </div>\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter2}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog5} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedSupplyer} options={this.supplyer} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona fornitore\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default DocumentiAcquisti;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,KAAK,EAAEC,4BAA4B,QAAQ,wBAAwB;AAC5E,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,iBAAiB,SAASnB,SAAS,CAAC;EAatCoB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAwND;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7CC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEN,CAAC,CAACG,KAAK,CAAC;MACrD,IAAII,GAAG,GAAG,yBAAyB,GAAGP,CAAC,CAACG,KAAK,IAAI,IAAI,CAACK,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAC1P,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,oBAAA,EAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAL,oBAAA,GAAED,OAAO,CAACO,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBO,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAyB,cAAA,GAAEF,OAAO,CAACW,KAAK,cAAAT,cAAA,uBAAbA,cAAA,CAAezB,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA8C,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAA9C,CAAC,CAACwD,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAY5B,IAAI,MAAKuC,SAAS,IAAAV,YAAA,GAAG/C,CAAC,CAACwD,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAY7B,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IAAA,KA4fDC,WAAW,GAAIC,MAAM,IAAK;MACtB,IAAIA,MAAM,CAAC7B,KAAK,KAAK,IAAI,IAAI6B,MAAM,CAAC/D,MAAM,KAAK,YAAY,EAAE;QACzD,IAAIgE,QAAQ,GAAG,EAAE;QACjB,IAAI,CAACtD,KAAK,CAAC8B,OAAO,CAAClB,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIA,OAAO,CAAC/B,EAAE,KAAKuE,MAAM,CAACvE,EAAE,EAAE;YAC1BwE,QAAQ,GAAGzC,OAAO;UACtB;QACJ,CAAC,CAAC;QACF0C,YAAY,CAACzD,OAAO,CAAC,YAAY,EAAE0D,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;QAC5D1D,MAAM,CAAC8D,QAAQ,CAACC,QAAQ,GAAG3F,4BAA4B;MAC3D,CAAC,MAAM;QACH,IAAI,CAAC0E,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,uHAAuH;UAC/HK,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ,CAAC;IA/wBG,IAAI,CAACnD,KAAK,GAAG;MACT8B,OAAO,EAAE,IAAI;MACb8B,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACd/B,QAAQ,EAAE,IAAI;MACdrC,iBAAiB,EAAE,IAAI;MACvBqE,iBAAiB,EAAE,IAAI;MACvB3B,OAAO,EAAE,IAAI;MACb4B,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPnB,MAAM,EAAE,IAAI,CAACxE,WAAW;MACxBc,KAAK,EAAE,IAAI;MACX8E,MAAM,EAAE,IAAI;MACZzC,YAAY,EAAE,CAAC;MACf0C,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,gBAAgB;MACvB1E,MAAM,EAAE,eAAe;MACvBD,gBAAgB,EAAE,IAAI;MACtB4E,kBAAkB,EAAE,IAAI;MACxBC,IAAI,EAAEvB,YAAY,CAACwB,OAAO,CAAC,MAAM,CAAC;MAClC3E,UAAU,EAAE;QACR8B,KAAK,EAAE,CAAC;QACR7B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACP0E,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAEvF,KAAK,EAAE,EAAE;YAAEwF,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAExF,KAAK,EAAE,EAAE;YAAEwF,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAExF,KAAK,EAAE,EAAE;YAAEwF,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAM5F,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACV2C,OAAO,EAAE,IAAI;QACbsC,MAAM,EAAElF,CAAC,CAACG,KAAK,CAAC0F,IAAI;QACpBpF,gBAAgB,EAAET,CAAC,CAACG;MACxB,CAAC,CAAC;MAEF,IAAII,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAAC4E,KAAK,GAAG,IAAI,CAAC5E,KAAK,CAACN,iBAAiB,GAAG,IAAI,CAACM,KAAK,CAACE,MAAM,GAAGV,CAAC,CAACG,KAAK,CAACQ,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAClN,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyE,qBAAA,EAAAC,eAAA;UAClC,IAAIvE,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAmE,qBAAA,GAAEzE,OAAO,CAACO,WAAW,cAAAkE,qBAAA,uBAAnBA,qBAAA,CAAqBjE,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAiG,eAAA,GAAE1E,OAAO,CAACW,KAAK,cAAA+D,eAAA,uBAAbA,eAAA,CAAejG,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAgG,YAAA,EAAAC,YAAA;QACVjD,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyC,YAAA,GAAAhG,CAAC,CAACwD,QAAQ,cAAAwC,YAAA,uBAAVA,YAAA,CAAY9E,IAAI,MAAKuC,SAAS,IAAAwC,YAAA,GAAGjG,CAAC,CAACwD,QAAQ,cAAAyC,YAAA,uBAAVA,YAAA,CAAY/E,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACuC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACvG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACuG,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACN,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACR,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACT,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACU,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACV,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACW,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACX,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACY,UAAU,GAAG,IAAI,CAACA,UAAU,CAACZ,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACa,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACb,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACc,YAAY,GAAG,IAAI,CAACA,YAAY,CAACd,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACe,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACf,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACgB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACjB,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAAC1C,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC0C,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACkB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAClB,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACnB,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACoB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMqB,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,WAAW,GAAG5D,IAAI,CAAC6D,KAAK,CAACzH,MAAM,CAACC,cAAc,CAACkF,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIqC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKnE,SAAS,EAAE;MACxE,IAAI,CAACxD,QAAQ,CAAC;QAAEC,iBAAiB,EAAE0H,WAAW,CAACjH,IAAI,GAAGiH,WAAW,CAACjH,IAAI,GAAGiH;MAAY,CAAC,CAAC;MACvF,IAAIrH,GAAG,GAAG,yBAAyB,IAAIqH,WAAW,CAACjH,IAAI,GAAGiH,WAAW,CAACjH,IAAI,GAAGiH,WAAW,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACpH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACjM,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyG,qBAAA,EAAAC,eAAA;UAClC,IAAIvG,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAmG,qBAAA,GAAEzG,OAAO,CAACO,WAAW,cAAAkG,qBAAA,uBAAnBA,qBAAA,CAAqBjG,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAiI,eAAA,GAAE1G,OAAO,CAACW,KAAK,cAAA+F,eAAA,uBAAbA,eAAA,CAAejI,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAgI,YAAA,EAAAC,YAAA;QACVjF,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyE,YAAA,GAAAhI,CAAC,CAACwD,QAAQ,cAAAwE,YAAA,uBAAVA,YAAA,CAAY9G,IAAI,MAAKuC,SAAS,IAAAwE,YAAA,GAAGjI,CAAC,CAACwD,QAAQ,cAAAyE,YAAA,uBAAVA,YAAA,CAAY/G,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAAC1D,QAAQ,CAAC;QAAEuE,YAAY,EAAE,IAAI;QAAEK,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;IACA,MAAM5G,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC8C,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIkH,KAAK,IAAIlH,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAACiF,SAAS,CAAC9D,IAAI,CAAC;UAChBwD,IAAI,EAAEqC,KAAK,CAACC,aAAa,GAAG,GAAG,GAAGD,KAAK,CAACE,OAAO,GAAG,GAAG,GAAGF,KAAK,CAACG,KAAK,GAAG,IAAI,GAAGH,KAAK,CAACI,IAAI,GAAG,KAAK,GAAGJ,KAAK,CAACK,GAAG;UAC3GpI,KAAK,EAAE+H,KAAK,CAAC5I;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDuD,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAAwI,YAAA,EAAAC,YAAA;MACVzF,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAiF,YAAA,GAAAxI,CAAC,CAACwD,QAAQ,cAAAgF,YAAA,uBAAVA,YAAA,CAAYtH,IAAI,MAAKuC,SAAS,IAAAgF,YAAA,GAAGzI,CAAC,CAACwD,QAAQ,cAAAiF,YAAA,uBAAVA,YAAA,CAAYvH,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAM1F,UAAU,CAAC,KAAK,EAAE,iCAAiC,CAAC,CACrD8C,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACf,QAAQ,CAAC;QACVqE,QAAQ,EAAEtD,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACD2B,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAA0I,YAAA,EAAAC,YAAA;MACV3F,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAAmF,YAAA,GAAA1I,CAAC,CAACwD,QAAQ,cAAAkF,YAAA,uBAAVA,YAAA,CAAYxH,IAAI,MAAKuC,SAAS,IAAAkF,YAAA,GAAG3I,CAAC,CAACwD,QAAQ,cAAAmF,YAAA,uBAAVA,YAAA,CAAYzH,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAM1F,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC8C,IAAI,CAACC,GAAG,IAAI;MACTA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,IAAIA,OAAO,CAACQ,UAAU,EAAE;UAC/B,IAAIL,CAAC,GAAG;YACJqE,IAAI,EAAExE,OAAO,CAACQ,UAAU,CAACtC,SAAS,IAAI,sBAAsB;YAC5DoB,IAAI,EAAEU,OAAO,CAAC/B,EAAE,IAAI;UACxB,CAAC;UACD,IAAI,CAAC4G,QAAQ,CAAC7D,IAAI,CAACb,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC,CAACqB,KAAK,CAAE7C,CAAC,IAAK;MACZgD,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;IAClB,CAAC,CAAC;EACV;EA8CA;EACA,MAAMqG,cAAcA,CAACxC,MAAM,EAAE;IACzB,IAAItD,GAAG,GAAG,mDAAmD,GAAGsD,MAAM,CAACvE,EAAE;IACzE,IAAIsJ,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAM5K,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;MACX4H,YAAY,GAAG5H,GAAG,CAACE,IAAI,CAACa,cAAc;MACtC8B,MAAM,CAAC9B,cAAc,GAAGf,GAAG,CAACE,IAAI,CAACa,cAAc;MAC/C8G,IAAI,GAAG7H,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACD2B,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAA8I,YAAA,EAAAC,aAAA;MACV/F,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,2EAAAC,MAAA,CAAwE,EAAAuF,YAAA,GAAA9I,CAAC,CAACwD,QAAQ,cAAAsF,YAAA,uBAAVA,YAAA,CAAY5H,IAAI,MAAKuC,SAAS,IAAAsF,aAAA,GAAG/I,CAAC,CAACwD,QAAQ,cAAAuF,aAAA,uBAAVA,aAAA,CAAY7H,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QAC7IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBG,MAAM,CAACpC,MAAM,GACb,OAAO,GACP,IAAIuH,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACzF,MAAM,CAAC/B,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAC7B,QAAQ,CAAC;MACVwE,aAAa,EAAE,IAAI;MACnBZ,MAAM,EAAEgF,IAAI;MACZzE,QAAQ,EAAE,IAAI,CAAC5D,KAAK,CAAC8B,OAAO,CAACiH,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAClK,EAAE,KAAKuE,MAAM,CAACvE,EAAE,CAAC;MAClE+E,QAAQ,EAAEuE,YAAY;MACtB5D,GAAG,EAAEtB;IACT,CAAC,CAAC;EACN;EACA;EACA6C,kBAAkBA,CAAC1C,MAAM,EAAE;IACvB,IAAI,CAAC5D,QAAQ,CAAC;MACV4D,MAAM,EAAEA,MAAM;MACdY,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA+B,UAAUA,CAAC3C,MAAM,EAAE;IACf,IAAIH,OAAO,GACP,oBAAoB,GACpBG,MAAM,CAACpC,MAAM,GACb,OAAO,GACP,IAAIuH,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACzF,MAAM,CAAC/B,YAAY,CAAC,CAAC;IAC5C,IAAIgD,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACvE,KAAK,CAAC8D,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAAC9D,KAAK,CAAC8D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAACiE,IAAI,KAAK,QAAQ,IAAIzB,MAAM,CAAC7B,KAAK,KAAK,IAAI,EAAE;UACpD,IAAIyH,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAACrI,OAAO,CAACsI,WAAW,CAAC,GAAGD,QAAQ,CAACrI,OAAO,CAACuI,YAAY,CAAC,GAAGF,QAAQ,CAACrI,OAAO,CAACwI,aAAa,CAAC;UAC1G/E,KAAK,CAACzC,IAAI,CAAC;YACPyH,KAAK,EAAEzI,OAAO,CAAC0I,UAAU,GAAG,GAAG,GAAG1I,OAAO,CAAC2I,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;YAC1FtJ,KAAK,EAAEkB,OAAO,CAAC4I;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAI5I,OAAO,CAACiE,IAAI,KAAK,UAAU,IAAIzB,MAAM,CAAC7B,KAAK,KAAK,IAAI,EAAE;UAC7D+C,OAAO,CAAC1C,IAAI,CAAC;YACTyH,KAAK,EAAEzI,OAAO,CAAC0I,UAAU,GAAG,GAAG,GAAG1I,OAAO,CAAC2I,SAAS;YACnD7J,KAAK,EAAEkB,OAAO,CAAC4I;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACnF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC7E,QAAQ,CAAC;MACV4D,MAAM,EAAAqG,aAAA,KAAOrG,MAAM,CAAE;MACrBa,aAAa,EAAE,IAAI;MACnBI,KAAK,EAAEA,KAAK;MACZE,GAAG,EAAEtB,OAAO;MACZqB,OAAO,EAAEA;IACb,CAAC,CAAC;EACN;EACA6B,kBAAkBA,CAAA,EAAG;IACjB,IAAIlD,OAAO,GACP,iCAAiC;IACrC,IAAIoB,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIwE,MAAM,GAAG,IAAI,CAAC/I,KAAK,CAAC+D,iBAAiB,CAACgF,MAAM,CAACY,EAAE,IAAIA,EAAE,CAACnI,KAAK,KAAK,IAAI,CAAC;IACzE,IAAIuH,MAAM,CAACa,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAAC5J,KAAK,CAAC8D,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAAC9D,KAAK,CAAC8D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;UACrC,IAAIA,OAAO,CAACiE,IAAI,KAAK,UAAU,EAAE;YAC7BP,OAAO,CAAC1C,IAAI,CAAC;cACTyH,KAAK,EAAEzI,OAAO,CAAC0I,UAAU,GAAG,GAAG,GAAG1I,OAAO,CAAC2I,SAAS;cACnD7J,KAAK,EAAEkB,OAAO,CAAC4I;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACnF,KAAK,GAAGA,KAAK;MAClB,IAAI,CAAC7E,QAAQ,CAAC;QACV4D,MAAM,EAAE,IAAI,CAACrD,KAAK,CAAC+D,iBAAiB;QACpCG,aAAa,EAAE,IAAI;QACnBI,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA,OAAO;QAChBC,GAAG,EAAEtB;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAI6F,MAAM,CAACa,MAAM,KAAK,IAAI,CAAC5J,KAAK,CAAC+D,iBAAiB,CAAC6F,MAAM,EAAE;MAC9D,IAAIC,QAAQ,GAAG,IAAI,CAAC7J,KAAK,CAAC+D,iBAAiB,CAACgF,MAAM,CAAClI,OAAO,IAAIA,OAAO,CAACW,KAAK,CAACsI,QAAQ,KAAK,IAAI,CAAC;MAC9F,IAAID,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIC,QAAQ,CAACD,MAAM,KAAK,IAAI,CAAC5J,KAAK,CAAC+D,iBAAiB,CAAC6F,MAAM,EAAE;UACzD,IAAI,IAAI,CAAC5J,KAAK,CAAC8D,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAAC9D,KAAK,CAAC8D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAACiE,IAAI,KAAK,QAAQ,EAAE;gBAC3B,IAAImE,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAACrI,OAAO,CAACsI,WAAW,CAAC,GAAGD,QAAQ,CAACrI,OAAO,CAACuI,YAAY,CAAC,GAAGF,QAAQ,CAACrI,OAAO,CAACwI,aAAa,CAAC;gBAC1G/E,KAAK,CAACzC,IAAI,CAAC;kBACPyH,KAAK,EAAEzI,OAAO,CAAC0I,UAAU,GAAG,GAAG,GAAG1I,OAAO,CAAC2I,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;kBAC1FtJ,KAAK,EAAEkB,OAAO,CAAC4I;gBACnB,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA,IAAI,CAACnF,KAAK,GAAGA,KAAK;UAClB,IAAI,CAAC7E,QAAQ,CAAC;YACV4D,MAAM,EAAE,IAAI,CAACrD,KAAK,CAAC+D,iBAAiB;YACpCG,aAAa,EAAE,IAAI;YACnBI,KAAK,EAAEA,KAAK;YACZC,OAAO,EAAEA,OAAO;YAChBC,GAAG,EAAEtB;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,qJAAqJ;YAC7JK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAACnD,KAAK,CAAC8D,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAAC9D,KAAK,CAAC8D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACiE,IAAI,KAAK,QAAQ,EAAE;cAC3B,IAAImE,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAACrI,OAAO,CAACsI,WAAW,CAAC,GAAGD,QAAQ,CAACrI,OAAO,CAACuI,YAAY,CAAC,GAAGF,QAAQ,CAACrI,OAAO,CAACwI,aAAa,CAAC;cAC1G/E,KAAK,CAACzC,IAAI,CAAC;gBACPyH,KAAK,EAAEzI,OAAO,CAAC0I,UAAU,GAAG,GAAG,GAAG1I,OAAO,CAAC2I,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;gBAC1FtJ,KAAK,EAAEkB,OAAO,CAAC4I;cACnB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAACnF,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC7E,QAAQ,CAAC;UACV4D,MAAM,EAAE,IAAI,CAACrD,KAAK,CAAC+D,iBAAiB;UACpCG,aAAa,EAAE,IAAI;UACnBI,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,GAAG,EAAEtB;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACA8C,UAAUA,CAAA,EAAG;IACT,IAAI,CAACxG,QAAQ,CAAC;MACVyE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAsC,QAAQA,CAACnD,MAAM,EAAE;IAAA,IAAA0G,mBAAA,EAAAC,oBAAA;IACb,IAAI,CAACvK,QAAQ,CAAC;MACV4D,MAAM;MACNc,aAAa,EAAE,IAAI;MACnBQ,YAAY,EAAE,EAAAoF,mBAAA,GAAA1G,MAAM,CAACjC,WAAW,cAAA2I,mBAAA,uBAAlBA,mBAAA,CAAoB1I,UAAU,CAAC4I,KAAK,MAAK,IAAI,IAAAD,oBAAA,GAAG3G,MAAM,CAACjC,WAAW,cAAA4I,oBAAA,uBAAlBA,oBAAA,CAAoB3I,UAAU,CAAC4I,KAAK,GAAG;IACzG,CAAC,CAAC;EACN;EACAxD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAChH,QAAQ,CAAC;MACV0E,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA,MAAMuC,UAAUA,CAAA,EAAG;IACf,IAAI3G,GAAG,GAAG,iCAAiC,GAAG,IAAI,CAACC,KAAK,CAACqD,MAAM,CAACvE,EAAE,GAAG,kBAAkB,GAAG,IAAI,CAACkB,KAAK,CAAC2E,YAAY;IACjH,MAAMlH,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;MAAA,IAAA0J,WAAA;MACX1H,OAAO,CAACC,GAAG,CAACjC,GAAG,CAACE,IAAI,CAAC;MACrB,CAAAwJ,WAAA,OAAI,CAACxH,KAAK,cAAAwH,WAAA,uBAAVA,WAAA,CAAYvH,IAAI,CAAC;QACbC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,sCAAsC;QAC9CK,IAAI,EAAE;MACV,CAAC,CAAC;MACFgH,UAAU,CAAC,MAAM;QACbvK,MAAM,CAAC8D,QAAQ,CAAC0G,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CACD/H,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAA6K,YAAA,EAAAC,aAAA,EAAAC,aAAA;MACV/H,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,CAAA6K,YAAA,OAAI,CAAC3H,KAAK,cAAA2H,YAAA,uBAAVA,YAAA,CAAY1H,IAAI,CAAC;QACbC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,iEAAAC,MAAA,CAA8D,EAAAuH,aAAA,GAAA9K,CAAC,CAACwD,QAAQ,cAAAsH,aAAA,uBAAVA,aAAA,CAAY5J,IAAI,MAAKuC,SAAS,IAAAsH,aAAA,GAAG/K,CAAC,CAACwD,QAAQ,cAAAuH,aAAA,uBAAVA,aAAA,CAAY7J,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QACnIC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA,MAAM4D,aAAaA,CAAC1D,MAAM,EAAE;IACxB,IAAIA,MAAM,CAAC7B,KAAK,KAAK,IAAI,IAAI,CAAC6B,MAAM,CAAC5B,OAAO,EAAE;MAC1C,MAAMhE,UAAU,CAAC,KAAK,sDAAAsF,MAAA,CAAsDM,MAAM,CAACvE,EAAE,CAAE,CAAC,CACnFyB,IAAI,CAAEC,GAAG,IAAK;QACXgC,OAAO,CAACC,GAAG,CAACjC,GAAG,CAACE,IAAI,CAAC;QACrB,IAAI8J,QAAQ,GAAG,EAAE;QACjB,IAAIC,OAAO,GAAG,CAAC;QACf,IAAI9I,KAAK,GAAG,CAAC;QACbnB,GAAG,CAACE,IAAI,CAACa,cAAc,CAACX,OAAO,CAAC+I,EAAE,IAAI;UAClCc,OAAO,IAAId,EAAE,CAACe,eAAe;UAC7B,IAAIC,IAAI,GAAGhB,EAAE,CAACiB,mBAAmB,CAACC,SAAS,CAACC,iBAAiB,CAACH,IAAI,CAACI,GAAG,IAAIA,GAAG,CAAC3J,WAAW,CAACtC,EAAE,KAAK0B,GAAG,CAACE,IAAI,CAACU,WAAW,CAACtC,EAAE,CAAC;UACzH,IAAI6L,IAAI,KAAK1H,SAAS,EAAE;YACpBuH,QAAQ,CAAC3I,IAAI,CAAA6H,aAAA,CAAAA,aAAA,KAAMiB,IAAI;cAAEK,QAAQ,EAAErB,EAAE,CAACe,eAAe,GAAGf,EAAE,CAACiB,mBAAmB,CAACK,WAAW;cAAEC,cAAc,EAAEvB,EAAE,CAACiB,mBAAmB,CAACK,WAAW;cAAEE,SAAS,EAAER,IAAI,CAACS,OAAO;cAAEC,MAAM,EAAE1B,EAAE,CAACe,eAAe;cAAEY,UAAU,EAAE3B,EAAE,CAACiB,mBAAmB,CAACC;YAAS,EAAE,CAAC;UACxP;QACJ,CAAC,CAAC;QACF,IAAIU,GAAG,GAAG/K,GAAG,CAACE,IAAI,CAACiB,KAAK,KAAKnB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,GAAG,KAAK;QAC/DD,KAAK,GAAG,IAAI6G,IAAI,CAACgD,YAAY,CAAC,OAAO,EAAE;UACnCC,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,KAAK;UACfC,qBAAqB,EAAE;QAC3B,CAAC,CAAC,CAAC9C,MAAM,CAAC2B,QAAQ,CAACoB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACb,QAAQ,GAAGa,IAAI,CAACT,OAAO,CAAC,CAACU,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC,CAAC;QACpGzI,YAAY,CAACzD,OAAO,CAAC,UAAU,EAAE0D,IAAI,CAACC,SAAS,CAAC+G,QAAQ,CAAC,CAAC;QAC1DjH,YAAY,CAACzD,OAAO,CAAC,MAAM,EAAE0D,IAAI,CAACC,SAAS,CAAC+G,QAAQ,CAAC,CAAC;QACtDjH,YAAY,CAACzD,OAAO,CAAC,cAAc,EAAE0D,IAAI,CAACC,SAAS,CAAC;UAAEpC,UAAU,EAAEb,GAAG,CAACE,IAAI,CAACU,WAAW,CAACC,UAAU;UAAE4K,IAAI,EAAEzL,GAAG,CAACE,IAAI,CAACuL,IAAI;UAAE7M,YAAY,EAAEoB,GAAG,CAACE,IAAI,CAACtB,YAAY;UAAED,YAAY,EAAEqB,GAAG,CAACE,IAAI,CAACvB,YAAY;UAAEoM,GAAG,EAAEA;QAAI,CAAC,CAAC,CAAC;QAC9M3L,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,EAAE0D,IAAI,CAACC,SAAS,CAAC;UAAE3E,EAAE,EAAEuE,MAAM,CAACvE,EAAE;UAAEmC,MAAM,EAAEoC,MAAM,CAACpC,MAAM;UAAEK,YAAY,EAAE+B,MAAM,CAAC/B;QAAa,CAAC,CAAC,CAAC;QACxI1B,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,EAAEU,GAAG,CAACE,IAAI,CAACU,WAAW,CAACtC,EAAE,CAAC;QACpEc,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAE2K,OAAO,CAAC;QAClD7K,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,SAAS,EAAE6B,KAAK,CAAC;QAC/C/B,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAE0D,IAAI,CAACC,SAAS,CAAC;UAAE4B,IAAI,KAAAtC,MAAA,CAAKvC,GAAG,CAACE,IAAI,CAACwL,YAAY,CAACvE,aAAa,OAAA5E,MAAA,CAAIvC,GAAG,CAACE,IAAI,CAACwL,YAAY,CAACtE,OAAO,OAAA7E,MAAA,CAAIvC,GAAG,CAACE,IAAI,CAACwL,YAAY,CAACrE,KAAK,QAAA9E,MAAA,CAAKvC,GAAG,CAACE,IAAI,CAACwL,YAAY,CAACpE,IAAI,SAAA/E,MAAA,CAAMvC,GAAG,CAACE,IAAI,CAACwL,YAAY,CAACnE,GAAG,CAAE;UAAE5H,IAAI,EAAEK,GAAG,CAACE,IAAI,CAACwL,YAAY,CAACpN;QAAG,CAAC,CAAC,CAAC;QAC9Qc,MAAM,CAAC8D,QAAQ,CAACC,QAAQ,GAAG,eAAe;MAC9C,CAAC,CAAC,CACDtB,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA2M,aAAA,EAAAC,aAAA;QACV5J,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,iFAAAC,MAAA,CAAiF,EAAAoJ,aAAA,GAAA3M,CAAC,CAACwD,QAAQ,cAAAmJ,aAAA,uBAAVA,aAAA,CAAYzL,IAAI,MAAKuC,SAAS,IAAAmJ,aAAA,GAAG5M,CAAC,CAACwD,QAAQ,cAAAoJ,aAAA,uBAAVA,aAAA,CAAY1L,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACtJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,gJAA0I;QAChJK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACA,MAAM+C,KAAKA,CAAA,EAAG;IACV,IAAIkB,WAAW,GAAG5D,IAAI,CAAC6D,KAAK,CAACzH,MAAM,CAACC,cAAc,CAACkF,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIqC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKnE,SAAS,EAAE;MACxEmE,WAAW,GAAGA,WAAW,CAACjH,IAAI,KAAK8C,SAAS,GAAGmE,WAAW,CAACjH,IAAI,GAAGiH,WAAW;MAC7E,IAAIrH,GAAG,GAAG,yBAAyB,GAAGqH,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACpH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACb,QAAQ,CAAC;QAAEC,iBAAiB,EAAE0H,WAAW;QAAEhF,OAAO,EAAE,IAAI;QAAEsC,MAAM,EAAE,EAAE;QAAEzE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpG,MAAMxC,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAwL,qBAAA,EAAAC,eAAA;UAClC,IAAItL,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAkL,qBAAA,GAAExL,OAAO,CAACO,WAAW,cAAAiL,qBAAA,uBAAnBA,qBAAA,CAAqBhL,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAgN,eAAA,GAAEzL,OAAO,CAACW,KAAK,cAAA8K,eAAA,uBAAbA,eAAA,CAAehN,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA+M,aAAA,EAAAC,aAAA;QACVhK,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAwJ,aAAA,GAAA/M,CAAC,CAACwD,QAAQ,cAAAuJ,aAAA,uBAAVA,aAAA,CAAY7L,IAAI,MAAKuC,SAAS,IAAAuJ,aAAA,GAAGhN,CAAC,CAACwD,QAAQ,cAAAwJ,aAAA,uBAAVA,aAAA,CAAY9L,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAMgD,SAASA,CAAA,EAAG;IACd,IAAIiB,WAAW,GAAG5D,IAAI,CAAC6D,KAAK,CAACzH,MAAM,CAACC,cAAc,CAACkF,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIqC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKnE,SAAS,EAAE;MACxE,IAAIlD,GAAG,GAAG,yBAAyB,GAAGqH,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAACpH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACb,QAAQ,CAAC;QAAEC,iBAAiB,EAAE0H,WAAW;QAAEhF,OAAO,EAAE,IAAI;QAAEsC,MAAM,EAAE,EAAE;QAAEzE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpG,MAAMxC,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4L,qBAAA,EAAAC,eAAA;UAClC,IAAI1L,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAsL,qBAAA,GAAE5L,OAAO,CAACO,WAAW,cAAAqL,qBAAA,uBAAnBA,qBAAA,CAAqBpL,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAoN,eAAA,GAAE7L,OAAO,CAACW,KAAK,cAAAkL,eAAA,uBAAbA,eAAA,CAAepN,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAmN,aAAA,EAAAC,aAAA;QACVpK,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA4J,aAAA,GAAAnN,CAAC,CAACwD,QAAQ,cAAA2J,aAAA,uBAAVA,aAAA,CAAYjM,IAAI,MAAKuC,SAAS,IAAA2J,aAAA,GAAGpN,CAAC,CAACwD,QAAQ,cAAA4J,aAAA,uBAAVA,aAAA,CAAYlM,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACAkD,MAAMA,CAACwG,KAAK,EAAE;IACV,IAAI,CAACpN,QAAQ,CAAC;MAAE2C,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAACwD,eAAe,EAAE;MACtBkH,YAAY,CAAC,IAAI,CAAClH,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGuE,UAAU,CAAC,YAAY;MAC1C,IAAIpK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAAC4E,KAAK,GAAG,IAAI,CAAC5E,KAAK,CAACN,iBAAiB,IAAI,IAAI,CAACM,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG0M,KAAK,CAACxM,IAAI,GAAG,QAAQ,GAAGwM,KAAK,CAACvM,IAAI;MACpP,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAkM,qBAAA,EAAAC,eAAA;UAClC,IAAIhM,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA4L,qBAAA,GAAElM,OAAO,CAACO,WAAW,cAAA2L,qBAAA,uBAAnBA,qBAAA,CAAqB1L,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA0N,eAAA,GAAEnM,OAAO,CAACW,KAAK,cAAAwL,eAAA,uBAAbA,eAAA,CAAe1N,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAEyM,KAAK;UACjBzK,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAyN,aAAA,EAAAC,aAAA;QACV1K,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAkK,aAAA,GAAAzN,CAAC,CAACwD,QAAQ,cAAAiK,aAAA,uBAAVA,aAAA,CAAYvM,IAAI,MAAKuC,SAAS,IAAAiK,aAAA,GAAG1N,CAAC,CAACwD,QAAQ,cAAAkK,aAAA,uBAAVA,aAAA,CAAYxM,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEgK,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA9G,MAAMA,CAACuG,KAAK,EAAE;IACV,IAAI,CAACpN,QAAQ,CAAC;MAAE2C,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIiL,KAAK,GAAGR,KAAK,CAAC7H,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAG6H,KAAK,CAAC7H,SAAS;IAChG,IAAI,IAAI,CAACY,eAAe,EAAE;MACtBkH,YAAY,CAAC,IAAI,CAAClH,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGuE,UAAU,CAAC,YAAY;MAC1C,IAAIpK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAAC4E,KAAK,GAAG,IAAI,CAAC5E,KAAK,CAACN,iBAAiB,IAAI,IAAI,CAACM,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI,GAAG,SAAS,GAAG+M,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAC5H,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAMxH,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyM,qBAAA,EAAAC,eAAA;UAClC,IAAIvM,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAmM,qBAAA,GAAEzM,OAAO,CAACO,WAAW,cAAAkM,qBAAA,uBAAnBA,qBAAA,CAAqBjM,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAiO,eAAA,GAAE1M,OAAO,CAACW,KAAK,cAAA+L,eAAA,uBAAbA,eAAA,CAAejO,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAAsJ,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAC1J,KAAK,CAACI,UAAU;YAAE4E,SAAS,EAAE6H,KAAK,CAAC7H,SAAS;YAAEC,SAAS,EAAE4H,KAAK,CAAC5H;UAAS,EAAE;UAChG7C,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAgO,aAAA,EAAAC,aAAA;QACVjL,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyK,aAAA,GAAAhO,CAAC,CAACwD,QAAQ,cAAAwK,aAAA,uBAAVA,aAAA,CAAY9M,IAAI,MAAKuC,SAAS,IAAAwK,aAAA,GAAGjO,CAAC,CAACwD,QAAQ,cAAAyK,aAAA,uBAAVA,aAAA,CAAY/M,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEgK,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EAEA7G,QAAQA,CAACsG,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAACpN,QAAQ,CAAC;MAAEW,UAAU,EAAEyM;IAAM,CAAC,EAAE,IAAI,CAACa,YAAY,CAAC;EAC3D;EACA/G,gBAAgBA,CAACnH,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAEsE,iBAAiB,EAAEvE,CAAC,CAACG;IAAM,CAAC,CAAC;EACjD;EACA;EACAmH,mBAAmBA,CAACzD,MAAM,EAAE;IACxB,IAAI,CAAC5D,QAAQ,CAAC;MACV4D,MAAM;MACNwB,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAM+B,YAAYA,CAAA,EAAG;IACjB,IAAI9E,OAAO,GAAG,IAAI,CAAC9B,KAAK,CAAC8B,OAAO,CAACiH,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAAClK,EAAE,KAAK,IAAI,CAACkB,KAAK,CAACqD,MAAM,CAACvE,EAC1C,CAAC;IACD,IAAI,CAACW,QAAQ,CAAC;MACVqC,OAAO;MACP+C,kBAAkB,EAAE,KAAK;MACzBxB,MAAM,EAAE,IAAI,CAACxE;IACjB,CAAC,CAAC;IACF,IAAIkB,GAAG,GAAG,4BAA4B,GAAG,IAAI,CAACC,KAAK,CAACqD,MAAM,CAACvE,EAAE;IAC7D,MAAMrB,UAAU,CAAC,QAAQ,EAAEsC,GAAG,CAAC,CAC1BQ,IAAI,CAACC,GAAG,IAAI;MACTgC,OAAO,CAACC,GAAG,CAACjC,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACgC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,kCAAkC;QAC1CK,IAAI,EAAE;MACV,CAAC,CAAC;MACFvD,MAAM,CAAC8D,QAAQ,CAAC0G,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC/H,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAAmO,aAAA,EAAAC,aAAA;MACZpL,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,wEAAAC,MAAA,CAAqE,EAAA4K,aAAA,GAAAnO,CAAC,CAACwD,QAAQ,cAAA2K,aAAA,uBAAVA,aAAA,CAAYjN,IAAI,MAAKuC,SAAS,IAAA2K,aAAA,GAAGpO,CAAC,CAACwD,QAAQ,cAAA4K,aAAA,uBAAVA,aAAA,CAAYlN,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QAC1IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA0D,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACpH,QAAQ,CAAC;MACVoF,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EAoBAmC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAChH,KAAK,CAACN,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVuE,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACtB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA8D,UAAUA,CAAA,EAAG;IACT,IAAI,CAACxH,QAAQ,CAAC;MACV2E,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA8C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzH,QAAQ,CAAC;MACV2E,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAyJ,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBrP,OAAA,CAACnB,KAAK,CAACyQ,QAAQ;MAAAC,QAAA,eACXvP,OAAA;QAAKwP,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBvP,OAAA;UAAKwP,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBvP,OAAA;YAAKwP,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvCvP,OAAA,CAACf,MAAM;cACHuQ,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAACnI,kBAAmB;cAAAiI,QAAA,GAEhC,GAAG,EACHxQ,QAAQ,CAAC2Q,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACT9P,OAAA,CAACP,KAAK;cACFuC,SAAS,EAAE,IAAI,CAACT,KAAK,CAACqD,MAAO;cAC7BQ,QAAQ,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,QAAS;cAC9BW,GAAG,EAAE,IAAI,CAACxE,KAAK,CAACwE,GAAI;cACpBgK,GAAG,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAME,mBAAmB,gBACrBhQ,OAAA,CAACnB,KAAK,CAACyQ,QAAQ;MAAAC,QAAA,eACXvP,OAAA;QAAKwP,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DvP,OAAA,CAACf,MAAM;UAACuQ,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAAClH,iBAAkB;UAAAgH,QAAA,GAAE,GAAC,EAACxQ,QAAQ,CAAC2Q,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMG,mBAAmB,gBACrBjQ,OAAA,CAACnB,KAAK,CAACyQ,QAAQ;MAAAC,QAAA,eACXvP,OAAA,CAACf,MAAM;QAACuQ,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACjI,UAAW;QAAA+H,QAAA,GACjE,GAAG,EACHxQ,QAAQ,CAAC2Q,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMI,mBAAmB,gBACrBlQ,OAAA,CAACnB,KAAK,CAACyQ,QAAQ;MAAAC,QAAA,gBACXvP,OAAA,CAACf,MAAM;QAACuQ,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACxH,UAAW;QAAAsH,QAAA,GACjE,GAAG,eACJvP,OAAA;UAAGwP,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAAC/Q,QAAQ,CAACoR,KAAK,EAAE,GAAG;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACT9P,OAAA,CAACf,MAAM;QAACuQ,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACzH,kBAAmB;QAAAuH,QAAA,GACzE,GAAG,EACHxQ,QAAQ,CAAC2Q,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMM,wBAAwB,gBAC1BpQ,OAAA,CAACnB,KAAK,CAACyQ,QAAQ;MAAAC,QAAA,gBACXvP,OAAA,CAACf,MAAM;QACH4L,KAAK,EAAC,IAAI;QACVwF,IAAI,EAAC,aAAa;QAClBb,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAACrH;MAAuB;QAAAuH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACF9P,OAAA,CAACf,MAAM;QAACuQ,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACtH,YAAa;QAAAoH,QAAA,GACxD,GAAG,EACHxQ,QAAQ,CAACuR,EAAE,EAAE,GAAG;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,IAAIS,MAAM,GAAG,EAAE;IACf,IAAI,IAAI,CAAChP,KAAK,CAAC8E,IAAI,KAAK/G,KAAK,EAAE;MAC3BiR,MAAM,GAAG,CACL;QAAEC,aAAa,EAAE,UAAU;QAAEC,WAAW,EAAE;UAAEC,KAAK,EAAE;QAAM;MAAE,CAAC,EAC5D;QACI9B,KAAK,EAAE,QAAQ;QACf+B,MAAM,EAAE5R,QAAQ,CAAC6R,IAAI;QACrBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,WAAW;QAClB+B,MAAM,EAAE5R,QAAQ,CAACiS,SAAS;QAC1BH,IAAI,EAAE,WAAW;QACjBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5R,QAAQ,CAACkS,OAAO;QACxBJ,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5R,QAAQ,CAACmS,KAAK;QACtBL,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,+BAA+B;QACtC+B,MAAM,EAAE5R,QAAQ,CAACoS,YAAY;QAC7BN,IAAI,EAAE,SAAS;QACfE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,gCAAgC;QACvC+B,MAAM,EAAE5R,QAAQ,CAACqS,SAAS;QAC1BP,IAAI,EAAE,UAAU;QAChBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5R,QAAQ,CAACsS,SAAS;QAC1BR,IAAI,EAAE,UAAU;QAChBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,SAAS;QAChB+B,MAAM,EAAE,UAAU;QAClBE,IAAI,EAAE,SAAS;QACfC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,YAAY;QACnB+B,MAAM,EAAE5R,QAAQ,CAACuS,IAAI;QACrBT,IAAI,EAAE,YAAY;QAClBE,UAAU,EAAE;MAChB,CAAC,CACJ;IACL,CAAC,MAAM;MACHR,MAAM,GAAG,CACL;QACI3B,KAAK,EAAE,QAAQ;QACf+B,MAAM,EAAE5R,QAAQ,CAAC6R,IAAI;QACrBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,WAAW;QAClB+B,MAAM,EAAE5R,QAAQ,CAACiS,SAAS;QAC1BH,IAAI,EAAE,WAAW;QACjBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5R,QAAQ,CAACkS,OAAO;QACxBJ,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5R,QAAQ,CAACmS,KAAK;QACtBL,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5R,QAAQ,CAACsS,SAAS;QAC1BR,IAAI,EAAE,UAAU;QAChBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,YAAY;QACnB+B,MAAM,EAAE5R,QAAQ,CAACuS,IAAI;QACrBT,IAAI,EAAE,YAAY;QAClBE,UAAU,EAAE;MAChB,CAAC,CACJ;IACL;IACA,MAAMQ,YAAY,GAAG,CACjB;MAAE3K,IAAI,EAAE7H,QAAQ,CAACyS,OAAO;MAAEnB,IAAI,eAAErQ,OAAA;QAAGwP,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE2B,OAAO,EAAE,IAAI,CAACrK;IAAe,CAAC,EAC3F;MAAER,IAAI,EAAE7H,QAAQ,CAAC2S,SAAS;MAAErB,IAAI,eAAErQ,OAAA;QAAGwP,SAAS,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE2B,OAAO,EAAE,IAAI,CAAC1J;IAAS,CAAC,EACxF;MAAEnB,IAAI,EAAE7H,QAAQ,CAAC4S,kBAAkB;MAAEtB,IAAI,eAAErQ,OAAA;QAAGwP,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE2B,OAAO,EAAE,IAAI,CAAClK,UAAU;MAAE1G,MAAM,EAAE,QAAQ;MAAE+Q,OAAO,EAAE;IAAU,CAAC,EAC/I;MAAEhL,IAAI,EAAE7H,QAAQ,CAAC8S,YAAY;MAAExB,IAAI,eAAErQ,OAAA;QAAGwP,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE2B,OAAO,EAAE,IAAI,CAAC9M;IAAY,CAAC,EAChG;MAAEiC,IAAI,EAAE7H,QAAQ,CAAC+S,WAAW;MAAEzB,IAAI,eAAErQ,OAAA;QAAGwP,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE2B,OAAO,EAAE,IAAI,CAACnJ;IAAc,CAAC,EACjG;MAAE1B,IAAI,EAAE7H,QAAQ,CAACgT,OAAO;MAAE1B,IAAI,eAAErQ,OAAA;QAAGwP,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE2B,OAAO,EAAE,IAAI,CAACpJ;IAAoB,CAAC,CACrG;IAED,IAAI2J,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACzQ,KAAK,CAAC0E,MAAM,KAAK,EAAE,EAAE;MAC1B+L,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACIhS,OAAA;MAAKwP,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CvP,OAAA,CAACR,KAAK;QAACyS,GAAG,EAAG/G,EAAE,IAAK,IAAI,CAACjH,KAAK,GAAGiH;MAAG;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvC9P,OAAA,CAACH,GAAG;QAAA8P,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP9P,OAAA;QAAKwP,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCvP,OAAA;UAAAuP,QAAA,EAAKxQ,QAAQ,CAACmT;QAAuB;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,EACL,IAAI,CAACvO,KAAK,CAACN,iBAAiB,KAAK,IAAI,iBAClCjB,OAAA;QAAKwP,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCvP,OAAA;UAAIwP,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtEvP,OAAA;YAAIwP,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjEvP,OAAA;cAAKwP,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DvP,OAAA;gBAAIwP,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACvP,OAAA;kBAAGwP,SAAS,EAAC,iBAAiB;kBAACxC,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC/Q,QAAQ,CAACoT,SAAS,EAAC,GAAC;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H9P,OAAA,CAACb,QAAQ;gBAACqQ,SAAS,EAAC,QAAQ;gBAACtO,KAAK,EAAE,IAAI,CAACK,KAAK,CAACN,iBAAkB;gBAACmR,OAAO,EAAE,IAAI,CAAClL,SAAU;gBAACmL,QAAQ,EAAE,IAAI,CAACvR,iBAAkB;gBAACwR,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACjI,MAAM;gBAACkI,QAAQ,EAAC;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV9P,OAAA;QAAKwP,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBvP,OAAA,CAACF,eAAe;UACZmS,GAAG,EAAG/G,EAAE,IAAK,IAAI,CAACuH,EAAE,GAAGvH,EAAG;UAC1BhK,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC8B,OAAQ;UAC1BM,OAAO,EAAE,IAAI,CAACpC,KAAK,CAACoC,OAAQ;UAC5B4M,MAAM,EAAEA,MAAO;UACfmC,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTjL,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBnE,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAM;UACnCF,YAAY,EAAE,IAAI,CAAChC,KAAK,CAACgC,YAAa;UACtC3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAK;UACjCkR,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAExB,YAAa;UAC5ByB,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BzC,aAAa,EAAC,UAAU;UACxB0C,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAAC/L,cAAe;UAClCgM,SAAS,EAAE,IAAI,CAAC7R,KAAK,CAAC+D,iBAAkB;UACxC+N,iBAAiB,EAAGtS,CAAC,IAAK,IAAI,CAACmH,gBAAgB,CAACnH,CAAC,CAAE;UACnDuS,gBAAgB,EAAE,IAAI,CAAC/R,KAAK,CAAC8E,IAAI,KAAK/G,KAAK,GAAG,IAAI,GAAG,KAAM;UAC3DiU,kBAAkB,EAAE,IAAI,CAAC5L,kBAAmB;UAC5C6L,iBAAiB,EAAEzU,QAAQ,CAAC4I,kBAAmB;UAC/C8L,oBAAoB,EAAE,CAAC,IAAI,CAAClS,KAAK,CAAC+D,iBAAiB,IAAI,CAAC,IAAI,CAAC/D,KAAK,CAAC+D,iBAAiB,CAAC6F,MAAO;UAC5FuI,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAACnL,UAAW;UACnCoL,gBAAgB,eAAE5T,OAAA;YAAUwP,SAAS,EAAC,MAAM;YAAC5I,IAAI,EAAC;UAAgB;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/E+D,OAAO,EAAC,QAAQ;UAChBhM,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBtB,SAAS,EAAE,IAAI,CAAChF,KAAK,CAACI,UAAU,CAAC4E,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACjF,KAAK,CAACI,UAAU,CAAC6E,SAAU;UAC3CsB,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBrB,OAAO,EAAE,IAAI,CAAClF,KAAK,CAACI,UAAU,CAAC8E,OAAQ;UACvCqN,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAU;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9P,OAAA,CAACd,MAAM;QACH8U,OAAO,EAAE,IAAI,CAACzS,KAAK,CAACiE,aAAc;QAClCmL,MAAM,EAAE5R,QAAQ,CAACkV,MAAO;QACxBC,KAAK;QACL1E,SAAS,EAAC,kBAAkB;QAC5B2E,MAAM,EAAE9E,kBAAmB;QAC3B+E,MAAM,EAAE,IAAI,CAAC9M,kBAAmB;QAChC+M,SAAS,EAAE,KAAM;QAAA9E,QAAA,eAEjBvP,OAAA,CAACJ,mBAAmB;UAChBgF,MAAM,EAAE,IAAI,CAACrD,KAAK,CAAC6D,QAAS;UAC5B/B,OAAO,EAAE,IAAI,CAAC9B,KAAK,CAACqD,MAAO;UAC3B5C,SAAS,EAAE,IAAI,CAACT,KAAK,CAACqD,MAAO;UAC7B0P,MAAM,EAAE;QAAK;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAET9P,OAAA,CAACd,MAAM;QACH8U,OAAO,EAAE,IAAI,CAACzS,KAAK,CAACkE,aAAc;QAClCkL,MAAM,EAAE,IAAI,CAACpP,KAAK,CAACwE,GAAI;QACvBmO,KAAK;QACL1E,SAAS,EAAC,kBAAkB;QAC5B2E,MAAM,EAAElE,mBAAoB;QAC5BmE,MAAM,EAAE,IAAI,CAAC5M,UAAW;QAAA+H,QAAA,eAExBvP,OAAA,CAACL,kBAAkB;UAACiF,MAAM,EAAE,IAAI,CAACrD,KAAK,CAACqD,MAAO;UAACiB,KAAK,EAAE,IAAI,CAACA,KAAM;UAACC,OAAO,EAAE,IAAI,CAACvE,KAAK,CAACuE,OAAQ;UAACxG,KAAK,EAAE;QAAK;UAAAqQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eACT9P,OAAA,CAACd,MAAM;QACH8U,OAAO,EAAE,IAAI,CAACzS,KAAK,CAACmE,aAAc;QAClCiL,MAAM,EAAE5R,QAAQ,CAAC2S,SAAU;QAC3BwC,KAAK;QACL1E,SAAS,EAAC,kBAAkB;QAC5B2E,MAAM,EAAEjE,mBAAoB;QAC5BkE,MAAM,EAAE,IAAI,CAACpM,kBAAmB;QAAAuH,QAAA,eAEhCvP,OAAA;UAAKwP,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAChBvP,OAAA;YAAKwP,SAAS,EAAC,QAAQ;YAAAD,QAAA,eAACvP,OAAA;cAAAuP,QAAA,EAAOxQ,QAAQ,CAACwV;YAAe;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrE9P,OAAA;YAAKwP,SAAS,EAAC,QAAQ;YAAAD,QAAA,eACnBvP,OAAA,CAACX,SAAS;cAAC6B,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC2E,YAAa;cAACmM,QAAQ,EAAGtR,CAAC,IAAK,IAAI,CAACC,QAAQ,CAAC;gBAAEkF,YAAY,EAAEnF,CAAC,CAACyT,MAAM,CAACtT;cAAM,CAAC;YAAE;cAAAyO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,eACN9P,OAAA;YAAKwP,SAAS,EAAC,QAAQ;YAAAD,QAAA,eAACvP,OAAA;cAAMwP,SAAS,EAAC,aAAa;cAAAD,QAAA,GAAC,IAAE,EAACxQ,QAAQ,CAAC0V,WAAW;YAAA;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAET9P,OAAA,CAACd,MAAM;QACH8U,OAAO,EAAE,IAAI,CAACzS,KAAK,CAAC6E,kBAAmB;QACvCuK,MAAM,EAAE5R,QAAQ,CAAC2V,QAAS;QAC1BR,KAAK;QACLC,MAAM,EAAE/D,wBAAyB;QACjCgE,MAAM,EAAE,IAAI,CAAChM,sBAAuB;QAAAmH,QAAA,eAEpCvP,OAAA;UAAKwP,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCvP,OAAA;YACIwP,SAAS,EAAC,mCAAmC;YAC7CxC,KAAK,EAAE;cAAE2H,QAAQ,EAAE;YAAO;UAAE;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAACvO,KAAK,CAACqD,MAAM,iBACd5E,OAAA;YAAAuP,QAAA,GACKxQ,QAAQ,CAAC6V,YAAY,EAAC,GAAC,eAAA5U,OAAA;cAAAuP,QAAA,GAAI,IAAI,CAAChO,KAAK,CAACqD,MAAM,CAACpC,MAAM,EAAC,GAAC;YAAA;cAAAmN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT9P,OAAA,CAACd,MAAM;QAAC8U,OAAO,EAAE,IAAI,CAACzS,KAAK,CAACgE,YAAa;QAACoL,MAAM,EAAE5R,QAAQ,CAAC8V,iBAAkB;QAACX,KAAK;QAAC1E,SAAS,EAAC,kBAAkB;QAAC4E,MAAM,EAAE,IAAI,CAAC7L,iBAAkB;QAAC4L,MAAM,EAAEnE,mBAAoB;QAAAT,QAAA,GACxK,IAAI,CAAChO,KAAK,CAACqE,SAAS,iBACjB5F,OAAA,CAACZ,UAAU;UAAC0V,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACP,MAAM,EAAC;QAAS;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhG9P,OAAA;UAAKwP,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DvP,OAAA;YAAIwP,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvP,OAAA;cAAGwP,SAAS,EAAC,iBAAiB;cAACxC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC/Q,QAAQ,CAACoT,SAAS;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH9P,OAAA;YAAA2P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9P,OAAA,CAACb,QAAQ;YAACqQ,SAAS,EAAC,QAAQ;YAACtO,KAAK,EAAE,IAAI,CAACK,KAAK,CAACN,iBAAkB;YAACmR,OAAO,EAAE,IAAI,CAAClL,SAAU;YAACmL,QAAQ,EAAE,IAAI,CAACvR,iBAAkB;YAACwR,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACjI,MAAM;YAACkI,QAAQ,EAAC;UAAM;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT9P,OAAA,CAACN,OAAO;QAACsU,OAAO,EAAE,IAAI,CAACzS,KAAK,CAACoE,aAAc;QAACqP,QAAQ,EAAC,MAAM;QAACZ,MAAM,EAAE,IAAI,CAAC3L,WAAY;QAAA8G,QAAA,gBACjFvP,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACmP,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKvP,OAAA;YAAGwP,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C9P,OAAA;YAAIwP,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvP,OAAA;cAAGwP,SAAS,EAAC,mBAAmB;cAACxC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC/Q,QAAQ,CAACkW,MAAM;UAAA;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9P,OAAA,CAACf,MAAM;YAACoB,EAAE,EAAC,iBAAiB;YAACmP,SAAS,EAAEwC,WAAY;YAACvC,OAAO,EAAE,IAAI,CAAChI,KAAM;YAAA8H,QAAA,gBAACvP,OAAA;cAAGwP,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9P,OAAA;cAAAuP,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN9P,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACmP,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DvP,OAAA;YAAIwP,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvP,OAAA;cAAGwP,SAAS,EAAC,mBAAmB;cAACxC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC/Q,QAAQ,CAACkW,MAAM;UAAA;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9P,OAAA,CAACf,MAAM;YAACoB,EAAE,EAAC,kBAAkB;YAACmP,SAAS,EAAEwC,WAAY;YAACvC,OAAO,EAAE,IAAI,CAAC/H,SAAU;YAAA6H,QAAA,gBAACvP,OAAA;cAAGwP,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9P,OAAA;cAAAuP,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN9P,OAAA;UAAA2P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9P,OAAA,CAACb,QAAQ;UAACqQ,SAAS,EAAC,OAAO;UAACtO,KAAK,EAAE,IAAI,CAACK,KAAK,CAACC,gBAAiB;UAAC4Q,OAAO,EAAE,IAAI,CAACnL,QAAS;UAACoL,QAAQ,EAAE,IAAI,CAAC1L,SAAU;UAAC2L,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,qBAAqB;UAACjI,MAAM;UAACkI,QAAQ,EAAC;QAAM;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe7P,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}