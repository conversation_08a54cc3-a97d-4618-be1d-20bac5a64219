{"name": "@cypress/vue2", "version": "0.0.0-development", "description": "Browser-based Component Testing for Vue.js@2 with Cypress.io ✌️🌲", "main": "dist/cypress-vue2.cjs.js", "scripts": {"typecheck": "tsc --noEmit", "build": "rimraf dist && yarn rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "build-prod": "yarn build", "test": "echo \"Tests for @cypress/vue2 are run from system-tests\"", "watch": "yarn build --watch --watch.exclude ./dist/**/*"}, "dependencies": {"@vue/test-utils": "^1.1.3"}, "devDependencies": {"@cypress/mount-utils": "0.0.0-development", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-replace": "^2.3.1", "tslib": "^2.1.0", "typescript": "^4.7.4", "vue": "2.6.12"}, "peerDependencies": {"cypress": ">=4.5.0", "vue": "^2.0.0"}, "files": ["dist/**/*", "src/**/*.js"], "engines": {"node": ">=8"}, "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/vue/#readme", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Fvue&template=1-bug-report.md&title=", "keywords": ["cypress", "vue"], "unpkg": "dist/cypress-vue2.browser.js", "module": "dist/cypress-vue2.esm-bundler.js", "publishConfig": {"access": "public"}}