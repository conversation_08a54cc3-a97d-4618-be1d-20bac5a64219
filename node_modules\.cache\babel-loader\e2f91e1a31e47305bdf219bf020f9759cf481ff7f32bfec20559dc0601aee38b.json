{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n/* eslint-disable no-param-reassign */\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useScrollTo(containerRef, data, heights, itemHeight, getKey, collectHeight, syncScrollTop, triggerFlash) {\n  var scrollRef = React.useRef();\n  return function (arg) {\n    // When not argument provided, we think dev may want to show the scrollbar\n    if (arg === null || arg === undefined) {\n      triggerFlash();\n      return;\n    } // Normal scroll logic\n\n    raf.cancel(scrollRef.current);\n    if (typeof arg === 'number') {\n      syncScrollTop(arg);\n    } else if (arg && _typeof(arg) === 'object') {\n      var index;\n      var align = arg.align;\n      if ('index' in arg) {\n        index = arg.index;\n      } else {\n        index = data.findIndex(function (item) {\n          return getKey(item) === arg.key;\n        });\n      }\n      var _arg$offset = arg.offset,\n        offset = _arg$offset === void 0 ? 0 : _arg$offset; // We will retry 3 times in case dynamic height shaking\n\n      var syncScroll = function syncScroll(times, targetAlign) {\n        if (times < 0 || !containerRef.current) return;\n        var height = containerRef.current.clientHeight;\n        var needCollectHeight = false;\n        var newTargetAlign = targetAlign; // Go to next frame if height not exist\n\n        if (height) {\n          var mergedAlign = targetAlign || align; // Get top & bottom\n\n          var stackTop = 0;\n          var itemTop = 0;\n          var itemBottom = 0;\n          var maxLen = Math.min(data.length, index);\n          for (var i = 0; i <= maxLen; i += 1) {\n            var key = getKey(data[i]);\n            itemTop = stackTop;\n            var cacheHeight = heights.get(key);\n            itemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n            stackTop = itemBottom;\n            if (i === index && cacheHeight === undefined) {\n              needCollectHeight = true;\n            }\n          } // Scroll to\n\n          var targetTop = null;\n          switch (mergedAlign) {\n            case 'top':\n              targetTop = itemTop - offset;\n              break;\n            case 'bottom':\n              targetTop = itemBottom - height + offset;\n              break;\n            default:\n              {\n                var scrollTop = containerRef.current.scrollTop;\n                var scrollBottom = scrollTop + height;\n                if (itemTop < scrollTop) {\n                  newTargetAlign = 'top';\n                } else if (itemBottom > scrollBottom) {\n                  newTargetAlign = 'bottom';\n                }\n              }\n          }\n          if (targetTop !== null && targetTop !== containerRef.current.scrollTop) {\n            syncScrollTop(targetTop);\n          }\n        } // We will retry since element may not sync height as it described\n\n        scrollRef.current = raf(function () {\n          if (needCollectHeight) {\n            collectHeight();\n          }\n          syncScroll(times - 1, newTargetAlign);\n        });\n      };\n      syncScroll(3);\n    }\n  };\n}", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "React", "raf", "useScrollTo", "containerRef", "data", "heights", "itemHeight", "<PERSON><PERSON><PERSON>", "collectHeight", "syncScrollTop", "triggerFlash", "scrollRef", "useRef", "arg", "undefined", "cancel", "current", "index", "align", "findIndex", "item", "key", "_arg$offset", "offset", "syncScroll", "times", "targetAlign", "height", "clientHeight", "needCollectHeight", "newTargetAlign", "mergedAlign", "stackTop", "itemTop", "itemBottom", "maxLen", "Math", "min", "length", "i", "cacheHeight", "get", "targetTop", "scrollTop", "scrollBottom"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-virtual-list/es/hooks/useScrollTo.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\n/* eslint-disable no-param-reassign */\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useScrollTo(containerRef, data, heights, itemHeight, getKey, collectHeight, syncScrollTop, triggerFlash) {\n  var scrollRef = React.useRef();\n  return function (arg) {\n    // When not argument provided, we think dev may want to show the scrollbar\n    if (arg === null || arg === undefined) {\n      triggerFlash();\n      return;\n    } // Normal scroll logic\n\n\n    raf.cancel(scrollRef.current);\n\n    if (typeof arg === 'number') {\n      syncScrollTop(arg);\n    } else if (arg && _typeof(arg) === 'object') {\n      var index;\n      var align = arg.align;\n\n      if ('index' in arg) {\n        index = arg.index;\n      } else {\n        index = data.findIndex(function (item) {\n          return getKey(item) === arg.key;\n        });\n      }\n\n      var _arg$offset = arg.offset,\n          offset = _arg$offset === void 0 ? 0 : _arg$offset; // We will retry 3 times in case dynamic height shaking\n\n      var syncScroll = function syncScroll(times, targetAlign) {\n        if (times < 0 || !containerRef.current) return;\n        var height = containerRef.current.clientHeight;\n        var needCollectHeight = false;\n        var newTargetAlign = targetAlign; // Go to next frame if height not exist\n\n        if (height) {\n          var mergedAlign = targetAlign || align; // Get top & bottom\n\n          var stackTop = 0;\n          var itemTop = 0;\n          var itemBottom = 0;\n          var maxLen = Math.min(data.length, index);\n\n          for (var i = 0; i <= maxLen; i += 1) {\n            var key = getKey(data[i]);\n            itemTop = stackTop;\n            var cacheHeight = heights.get(key);\n            itemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n            stackTop = itemBottom;\n\n            if (i === index && cacheHeight === undefined) {\n              needCollectHeight = true;\n            }\n          } // Scroll to\n\n\n          var targetTop = null;\n\n          switch (mergedAlign) {\n            case 'top':\n              targetTop = itemTop - offset;\n              break;\n\n            case 'bottom':\n              targetTop = itemBottom - height + offset;\n              break;\n\n            default:\n              {\n                var scrollTop = containerRef.current.scrollTop;\n                var scrollBottom = scrollTop + height;\n\n                if (itemTop < scrollTop) {\n                  newTargetAlign = 'top';\n                } else if (itemBottom > scrollBottom) {\n                  newTargetAlign = 'bottom';\n                }\n              }\n          }\n\n          if (targetTop !== null && targetTop !== containerRef.current.scrollTop) {\n            syncScrollTop(targetTop);\n          }\n        } // We will retry since element may not sync height as it described\n\n\n        scrollRef.current = raf(function () {\n          if (needCollectHeight) {\n            collectHeight();\n          }\n\n          syncScroll(times - 1, newTargetAlign);\n        });\n      };\n\n      syncScroll(3);\n    }\n  };\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;;AAE/U;AACA,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,WAAWA,CAACC,YAAY,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAE;EAC/H,IAAIC,SAAS,GAAGX,KAAK,CAACY,MAAM,CAAC,CAAC;EAC9B,OAAO,UAAUC,GAAG,EAAE;IACpB;IACA,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,EAAE;MACrCJ,YAAY,CAAC,CAAC;MACd;IACF,CAAC,CAAC;;IAGFT,GAAG,CAACc,MAAM,CAACJ,SAAS,CAACK,OAAO,CAAC;IAE7B,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;MAC3BJ,aAAa,CAACI,GAAG,CAAC;IACpB,CAAC,MAAM,IAAIA,GAAG,IAAInB,OAAO,CAACmB,GAAG,CAAC,KAAK,QAAQ,EAAE;MAC3C,IAAII,KAAK;MACT,IAAIC,KAAK,GAAGL,GAAG,CAACK,KAAK;MAErB,IAAI,OAAO,IAAIL,GAAG,EAAE;QAClBI,KAAK,GAAGJ,GAAG,CAACI,KAAK;MACnB,CAAC,MAAM;QACLA,KAAK,GAAGb,IAAI,CAACe,SAAS,CAAC,UAAUC,IAAI,EAAE;UACrC,OAAOb,MAAM,CAACa,IAAI,CAAC,KAAKP,GAAG,CAACQ,GAAG;QACjC,CAAC,CAAC;MACJ;MAEA,IAAIC,WAAW,GAAGT,GAAG,CAACU,MAAM;QACxBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW,CAAC,CAAC;;MAEvD,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEC,WAAW,EAAE;QACvD,IAAID,KAAK,GAAG,CAAC,IAAI,CAACtB,YAAY,CAACa,OAAO,EAAE;QACxC,IAAIW,MAAM,GAAGxB,YAAY,CAACa,OAAO,CAACY,YAAY;QAC9C,IAAIC,iBAAiB,GAAG,KAAK;QAC7B,IAAIC,cAAc,GAAGJ,WAAW,CAAC,CAAC;;QAElC,IAAIC,MAAM,EAAE;UACV,IAAII,WAAW,GAAGL,WAAW,IAAIR,KAAK,CAAC,CAAC;;UAExC,IAAIc,QAAQ,GAAG,CAAC;UAChB,IAAIC,OAAO,GAAG,CAAC;UACf,IAAIC,UAAU,GAAG,CAAC;UAClB,IAAIC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACjC,IAAI,CAACkC,MAAM,EAAErB,KAAK,CAAC;UAEzC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;YACnC,IAAIlB,GAAG,GAAGd,MAAM,CAACH,IAAI,CAACmC,CAAC,CAAC,CAAC;YACzBN,OAAO,GAAGD,QAAQ;YAClB,IAAIQ,WAAW,GAAGnC,OAAO,CAACoC,GAAG,CAACpB,GAAG,CAAC;YAClCa,UAAU,GAAGD,OAAO,IAAIO,WAAW,KAAK1B,SAAS,GAAGR,UAAU,GAAGkC,WAAW,CAAC;YAC7ER,QAAQ,GAAGE,UAAU;YAErB,IAAIK,CAAC,KAAKtB,KAAK,IAAIuB,WAAW,KAAK1B,SAAS,EAAE;cAC5Ce,iBAAiB,GAAG,IAAI;YAC1B;UACF,CAAC,CAAC;;UAGF,IAAIa,SAAS,GAAG,IAAI;UAEpB,QAAQX,WAAW;YACjB,KAAK,KAAK;cACRW,SAAS,GAAGT,OAAO,GAAGV,MAAM;cAC5B;YAEF,KAAK,QAAQ;cACXmB,SAAS,GAAGR,UAAU,GAAGP,MAAM,GAAGJ,MAAM;cACxC;YAEF;cACE;gBACE,IAAIoB,SAAS,GAAGxC,YAAY,CAACa,OAAO,CAAC2B,SAAS;gBAC9C,IAAIC,YAAY,GAAGD,SAAS,GAAGhB,MAAM;gBAErC,IAAIM,OAAO,GAAGU,SAAS,EAAE;kBACvBb,cAAc,GAAG,KAAK;gBACxB,CAAC,MAAM,IAAII,UAAU,GAAGU,YAAY,EAAE;kBACpCd,cAAc,GAAG,QAAQ;gBAC3B;cACF;UACJ;UAEA,IAAIY,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKvC,YAAY,CAACa,OAAO,CAAC2B,SAAS,EAAE;YACtElC,aAAa,CAACiC,SAAS,CAAC;UAC1B;QACF,CAAC,CAAC;;QAGF/B,SAAS,CAACK,OAAO,GAAGf,GAAG,CAAC,YAAY;UAClC,IAAI4B,iBAAiB,EAAE;YACrBrB,aAAa,CAAC,CAAC;UACjB;UAEAgB,UAAU,CAACC,KAAK,GAAG,CAAC,EAAEK,cAAc,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC;MAEDN,UAAU,CAAC,CAAC,CAAC;IACf;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}