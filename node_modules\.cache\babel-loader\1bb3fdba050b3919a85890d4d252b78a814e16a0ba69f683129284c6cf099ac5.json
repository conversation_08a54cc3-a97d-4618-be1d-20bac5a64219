{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\"; // It's safe to use `useLayoutEffect` but the warning is annoying\n\nvar useIsomorphicLayoutEffect = canUseDom() ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "canUseDom", "useIsomorphicLayoutEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\"; // It's safe to use `useLayoutEffect` but the warning is annoying\n\nvar useIsomorphicLayoutEffect = canUseDom() ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,0BAA0B,CAAC,CAAC;;AAElD,IAAIC,yBAAyB,GAAGD,SAAS,CAAC,CAAC,GAAGD,eAAe,GAAGD,SAAS;AACzE,eAAeG,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}