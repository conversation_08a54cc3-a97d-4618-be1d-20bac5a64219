{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nvar vendorPrefix;\nvar jsCssMap = {\n  Webkit: '-webkit-',\n  Moz: '-moz-',\n  // IE did it wrong again ...\n  ms: '-ms-',\n  O: '-o-'\n};\nfunction getVendorPrefix() {\n  if (vendorPrefix !== undefined) {\n    return vendorPrefix;\n  }\n  vendorPrefix = '';\n  var style = document.createElement('p').style;\n  var testProp = 'Transform';\n  for (var key in jsCssMap) {\n    if (key + testProp in style) {\n      vendorPrefix = key;\n    }\n  }\n  return vendorPrefix;\n}\nfunction getTransitionName() {\n  return getVendorPrefix() ? \"\".concat(getVendorPrefix(), \"TransitionProperty\") : 'transitionProperty';\n}\nfunction getTransformName() {\n  return getVendorPrefix() ? \"\".concat(getVendorPrefix(), \"Transform\") : 'transform';\n}\nfunction setTransitionProperty(node, value) {\n  var name = getTransitionName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transitionProperty') {\n      node.style.transitionProperty = value;\n    }\n  }\n}\nfunction setTransform(node, value) {\n  var name = getTransformName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transform') {\n      node.style.transform = value;\n    }\n  }\n}\nfunction getTransitionProperty(node) {\n  return node.style.transitionProperty || node.style[getTransitionName()];\n}\nfunction getTransformXY(node) {\n  var style = window.getComputedStyle(node, null);\n  var transform = style.getPropertyValue('transform') || style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    var matrix = transform.replace(/[^0-9\\-.,]/g, '').split(',');\n    return {\n      x: parseFloat(matrix[12] || matrix[4], 0),\n      y: parseFloat(matrix[13] || matrix[5], 0)\n    };\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n}\nvar matrix2d = /matrix\\((.*)\\)/;\nvar matrix3d = /matrix3d\\((.*)\\)/;\nfunction setTransformXY(node, xy) {\n  var style = window.getComputedStyle(node, null);\n  var transform = style.getPropertyValue('transform') || style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    var arr;\n    var match2d = transform.match(matrix2d);\n    if (match2d) {\n      match2d = match2d[1];\n      arr = match2d.split(',').map(function (item) {\n        return parseFloat(item, 10);\n      });\n      arr[4] = xy.x;\n      arr[5] = xy.y;\n      setTransform(node, \"matrix(\".concat(arr.join(','), \")\"));\n    } else {\n      var match3d = transform.match(matrix3d)[1];\n      arr = match3d.split(',').map(function (item) {\n        return parseFloat(item, 10);\n      });\n      arr[12] = xy.x;\n      arr[13] = xy.y;\n      setTransform(node, \"matrix3d(\".concat(arr.join(','), \")\"));\n    }\n  } else {\n    setTransform(node, \"translateX(\".concat(xy.x, \"px) translateY(\").concat(xy.y, \"px) translateZ(0)\"));\n  }\n}\nvar RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\nvar getComputedStyleX; // https://stackoverflow.com/a/3485654/3040605\n\nfunction forceRelayout(elem) {\n  var originalStyle = elem.style.display;\n  elem.style.display = 'none';\n  elem.offsetHeight; // eslint-disable-line\n\n  elem.style.display = originalStyle;\n}\nfunction css(el, name, v) {\n  var value = v;\n  if (_typeof(name) === 'object') {\n    for (var i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value = \"\".concat(value, \"px\");\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\nfunction getClientPosition(elem) {\n  var box;\n  var x;\n  var y;\n  var doc = elem.ownerDocument;\n  var body = doc.body;\n  var docElem = doc && doc.documentElement; // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n\n  box = elem.getBoundingClientRect(); // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = Math.floor(box.left);\n  y = Math.floor(box.top); // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n  return {\n    left: x,\n    top: y\n  };\n}\nfunction getScroll(w, top) {\n  var ret = w[\"page\".concat(top ? 'Y' : 'X', \"Offset\")];\n  var method = \"scroll\".concat(top ? 'Top' : 'Left');\n  if (typeof ret !== 'number') {\n    var d = w.document; // ie6,7,8 standard mode\n\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\nfunction getOffset(el) {\n  var pos = getClientPosition(el);\n  var doc = el.ownerDocument;\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\n\nfunction isWindow(obj) {\n  // must use == for ie8\n\n  /* eslint eqeqeq:0 */\n  return obj !== null && obj !== undefined && obj == obj.window;\n}\nfunction getDocument(node) {\n  if (isWindow(node)) {\n    return node.document;\n  }\n  if (node.nodeType === 9) {\n    return node;\n  }\n  return node.ownerDocument;\n}\nfunction _getComputedStyle(elem, name, cs) {\n  var computedStyle = cs;\n  var val = '';\n  var d = getDocument(elem);\n  computedStyle = computedStyle || d.defaultView.getComputedStyle(elem, null); // https://github.com/kissyteam/kissy/issues/61\n\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n  return val;\n}\nvar _RE_NUM_NO_PX = new RegExp(\"^(\".concat(RE_NUM, \")(?!px)[a-z%]+$\"), 'i');\nvar RE_POS = /^(top|right|bottom|left)$/;\nvar CURRENT_STYLE = 'currentStyle';\nvar RUNTIME_STYLE = 'runtimeStyle';\nvar LEFT = 'left';\nvar PX = 'px';\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  var ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name]; // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    var style = elem.style;\n    var left = style[LEFT];\n    var rsLeft = elem[RUNTIME_STYLE][LEFT]; // prevent flashing of content\n\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT]; // Put in the new values to get a computed value out\n\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX; // Revert the changed values\n\n    style[LEFT] = left;\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle ? _getComputedStyle : _getComputedStyleIE;\n}\nfunction getOffsetDirection(dir, option) {\n  if (dir === 'left') {\n    return option.useCssRight ? 'right' : dir;\n  }\n  return option.useCssBottom ? 'bottom' : dir;\n}\nfunction oppositeOffsetDirection(dir) {\n  if (dir === 'left') {\n    return 'right';\n  } else if (dir === 'right') {\n    return 'left';\n  } else if (dir === 'top') {\n    return 'bottom';\n  } else if (dir === 'bottom') {\n    return 'top';\n  }\n} // 设置 elem 相对 elem.ownerDocument 的坐标\n\nfunction setLeftTop(elem, offset, option) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n  var presetH = -999;\n  var presetV = -999;\n  var horizontalProperty = getOffsetDirection('left', option);\n  var verticalProperty = getOffsetDirection('top', option);\n  var oppositeHorizontalProperty = oppositeOffsetDirection(horizontalProperty);\n  var oppositeVerticalProperty = oppositeOffsetDirection(verticalProperty);\n  if (horizontalProperty !== 'left') {\n    presetH = 999;\n  }\n  if (verticalProperty !== 'top') {\n    presetV = 999;\n  }\n  var originalTransition = '';\n  var originalOffset = getOffset(elem);\n  if ('left' in offset || 'top' in offset) {\n    originalTransition = getTransitionProperty(elem) || '';\n    setTransitionProperty(elem, 'none');\n  }\n  if ('left' in offset) {\n    elem.style[oppositeHorizontalProperty] = '';\n    elem.style[horizontalProperty] = \"\".concat(presetH, \"px\");\n  }\n  if ('top' in offset) {\n    elem.style[oppositeVerticalProperty] = '';\n    elem.style[verticalProperty] = \"\".concat(presetV, \"px\");\n  } // force relayout\n\n  forceRelayout(elem);\n  var old = getOffset(elem);\n  var originalStyle = {};\n  for (var key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      var dir = getOffsetDirection(key, option);\n      var preset = key === 'left' ? presetH : presetV;\n      var off = originalOffset[key] - old[key];\n      if (dir === key) {\n        originalStyle[dir] = preset + off;\n      } else {\n        originalStyle[dir] = preset - off;\n      }\n    }\n  }\n  css(elem, originalStyle); // force relayout\n\n  forceRelayout(elem);\n  if ('left' in offset || 'top' in offset) {\n    setTransitionProperty(elem, originalTransition);\n  }\n  var ret = {};\n  for (var _key in offset) {\n    if (offset.hasOwnProperty(_key)) {\n      var _dir = getOffsetDirection(_key, option);\n      var _off = offset[_key] - originalOffset[_key];\n      if (_key === _dir) {\n        ret[_dir] = originalStyle[_dir] + _off;\n      } else {\n        ret[_dir] = originalStyle[_dir] - _off;\n      }\n    }\n  }\n  css(elem, ret);\n}\nfunction setTransform$1(elem, offset) {\n  var originalOffset = getOffset(elem);\n  var originalXY = getTransformXY(elem);\n  var resultXY = {\n    x: originalXY.x,\n    y: originalXY.y\n  };\n  if ('left' in offset) {\n    resultXY.x = originalXY.x + offset.left - originalOffset.left;\n  }\n  if ('top' in offset) {\n    resultXY.y = originalXY.y + offset.top - originalOffset.top;\n  }\n  setTransformXY(elem, resultXY);\n}\nfunction setOffset(elem, offset, option) {\n  if (option.ignoreShake) {\n    var oriOffset = getOffset(elem);\n    var oLeft = oriOffset.left.toFixed(0);\n    var oTop = oriOffset.top.toFixed(0);\n    var tLeft = offset.left.toFixed(0);\n    var tTop = offset.top.toFixed(0);\n    if (oLeft === tLeft && oTop === tTop) {\n      return;\n    }\n  }\n  if (option.useCssRight || option.useCssBottom) {\n    setLeftTop(elem, offset, option);\n  } else if (option.useCssTransform && getTransformName() in document.body.style) {\n    setTransform$1(elem, offset);\n  } else {\n    setLeftTop(elem, offset, option);\n  }\n}\nfunction each(arr, fn) {\n  for (var i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\nvar BOX_MODELS = ['margin', 'border', 'padding'];\nvar CONTENT_INDEX = -1;\nvar PADDING_INDEX = 2;\nvar BORDER_INDEX = 1;\nvar MARGIN_INDEX = 0;\nfunction swap(elem, options, callback) {\n  var old = {};\n  var style = elem.style;\n  var name; // Remember the old values, and insert the new ones\n\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n  callback.call(elem); // Revert the old values\n\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\nfunction getPBMWidth(elem, props, which) {\n  var value = 0;\n  var prop;\n  var j;\n  var i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        var cssProp = void 0;\n        if (prop === 'border') {\n          cssProp = \"\".concat(prop).concat(which[i], \"Width\");\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\nvar domUtils = {\n  getParent: function getParent(element) {\n    var parent = element;\n    do {\n      if (parent.nodeType === 11 && parent.host) {\n        parent = parent.host;\n      } else {\n        parent = parent.parentNode;\n      }\n    } while (parent && parent.nodeType !== 1 && parent.nodeType !== 9);\n    return parent;\n  }\n};\neach(['Width', 'Height'], function (name) {\n  domUtils[\"doc\".concat(name)] = function (refWin) {\n    var d = refWin.document;\n    return Math.max(\n    // firefox chrome documentElement.scrollHeight< body.scrollHeight\n    // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n    d.documentElement[\"scroll\".concat(name)],\n    // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n    d.body[\"scroll\".concat(name)], domUtils[\"viewport\".concat(name)](d));\n  };\n  domUtils[\"viewport\".concat(name)] = function (win) {\n    // pc browser includes scrollbar in window.innerWidth\n    var prop = \"client\".concat(name);\n    var doc = win.document;\n    var body = doc.body;\n    var documentElement = doc.documentElement;\n    var documentElementProp = documentElement[prop]; // 标准模式取 documentElement\n    // backcompat 取 body\n\n    return doc.compatMode === 'CSS1Compat' && documentElementProp || body && body[prop] || documentElementProp;\n  };\n});\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\n\nfunction getWH(elem, name, ex) {\n  var extra = ex;\n  if (isWindow(elem)) {\n    return name === 'width' ? domUtils.viewportWidth(elem) : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width' ? domUtils.docWidth(elem) : domUtils.docHeight(elem);\n  }\n  var which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  var borderBoxValue = name === 'width' ? Math.floor(elem.getBoundingClientRect().width) : Math.floor(elem.getBoundingClientRect().height);\n  var isBorderBox = isBorderBoxFn(elem);\n  var cssBoxValue = 0;\n  if (borderBoxValue === null || borderBoxValue === undefined || borderBoxValue <= 0) {\n    borderBoxValue = undefined; // Fall back to computed then un computed css if necessary\n\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (cssBoxValue === null || cssBoxValue === undefined || Number(cssBoxValue) < 0) {\n      cssBoxValue = elem.style[name] || 0;\n    } // Normalize '', auto, and prepare for extra\n\n    cssBoxValue = parseFloat(cssBoxValue) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  var borderBoxValueOrIsBorderBox = borderBoxValue !== undefined || isBorderBox;\n  var val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which);\n    }\n    return cssBoxValue;\n  } else if (borderBoxValueOrIsBorderBox) {\n    if (extra === BORDER_INDEX) {\n      return val;\n    }\n    return val + (extra === PADDING_INDEX ? -getPBMWidth(elem, ['border'], which) : getPBMWidth(elem, ['margin'], which));\n  }\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which);\n}\nvar cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block'\n}; // fix #119 : https://github.com/kissyteam/kissy/issues/119\n\nfunction getWHIgnoreDisplay() {\n  for (var _len = arguments.length, args = new Array(_len), _key2 = 0; _key2 < _len; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  var val;\n  var elem = args[0]; // in case elem is window\n  // elem.offsetWidth === undefined\n\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, function () {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\neach(['width', 'height'], function (name) {\n  var first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[\"outer\".concat(first)] = function (el, includeMargin) {\n    return el && getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX);\n  };\n  var which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  domUtils[name] = function (elem, v) {\n    var val = v;\n    if (val !== undefined) {\n      if (elem) {\n        var isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\nfunction mix(to, from) {\n  for (var i in from) {\n    if (from.hasOwnProperty(i)) {\n      to[i] = from[i];\n    }\n  }\n  return to;\n}\nvar utils = {\n  getWindow: function getWindow(node) {\n    if (node && node.document && node.setTimeout) {\n      return node;\n    }\n    var doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  getDocument: getDocument,\n  offset: function offset(el, value, option) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value, option || {});\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow: isWindow,\n  each: each,\n  css: css,\n  clone: function clone(obj) {\n    var i;\n    var ret = {};\n    for (i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    var overflow = obj.overflow;\n    if (overflow) {\n      for (i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  mix: mix,\n  getWindowScrollLeft: function getWindowScrollLeft(w) {\n    return getScrollLeft(w);\n  },\n  getWindowScrollTop: function getWindowScrollTop(w) {\n    return getScrollTop(w);\n  },\n  merge: function merge() {\n    var ret = {};\n    for (var i = 0; i < arguments.length; i++) {\n      utils.mix(ret, i < 0 || arguments.length <= i ? undefined : arguments[i]);\n    }\n    return ret;\n  },\n  viewportWidth: 0,\n  viewportHeight: 0\n};\nmix(utils, domUtils);\n\n/**\n * 得到会导致元素显示不全的祖先元素\n */\n\nvar getParent = utils.getParent;\nfunction getOffsetParent(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return null;\n  } // ie 这个也不是完全可行\n\n  /*\n   <div style=\"width: 50px;height: 100px;overflow: hidden\">\n   <div style=\"width: 50px;height: 100px;position: relative;\" id=\"d6\">\n   元素 6 高 100px 宽 50px<br/>\n   </div>\n   </div>\n   */\n  // element.offsetParent does the right thing in ie7 and below. Return parent with layout!\n  //  In other browsers it only includes elements with position absolute, relative or\n  // fixed, not elements with overflow set to auto or scroll.\n  //        if (UA.ie && ieMode < 8) {\n  //            return element.offsetParent;\n  //        }\n  // 统一的 offsetParent 方法\n\n  var doc = utils.getDocument(element);\n  var body = doc.body;\n  var parent;\n  var positionStyle = utils.css(element, 'position');\n  var skipStatic = positionStyle === 'fixed' || positionStyle === 'absolute';\n  if (!skipStatic) {\n    return element.nodeName.toLowerCase() === 'html' ? null : getParent(element);\n  }\n  for (parent = getParent(element); parent && parent !== body && parent.nodeType !== 9; parent = getParent(parent)) {\n    positionStyle = utils.css(parent, 'position');\n    if (positionStyle !== 'static') {\n      return parent;\n    }\n  }\n  return null;\n}\nvar getParent$1 = utils.getParent;\nfunction isAncestorFixed(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return false;\n  }\n  var doc = utils.getDocument(element);\n  var body = doc.body;\n  var parent = null;\n  for (parent = getParent$1(element);\n  // 修复元素位于 document.documentElement 下导致崩溃问题\n  parent && parent !== body && parent !== doc; parent = getParent$1(parent)) {\n    var positionStyle = utils.css(parent, 'position');\n    if (positionStyle === 'fixed') {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * 获得元素的显示部分的区域\n */\n\nfunction getVisibleRectForElement(element, alwaysByViewport) {\n  var visibleRect = {\n    left: 0,\n    right: Infinity,\n    top: 0,\n    bottom: Infinity\n  };\n  var el = getOffsetParent(element);\n  var doc = utils.getDocument(element);\n  var win = doc.defaultView || doc.parentWindow;\n  var body = doc.body;\n  var documentElement = doc.documentElement; // Determine the size of the visible rect by climbing the dom accounting for\n  // all scrollable containers.\n\n  while (el) {\n    // clientWidth is zero for inline block elements in ie.\n    if ((navigator.userAgent.indexOf('MSIE') === -1 || el.clientWidth !== 0) &&\n    // body may have overflow set on it, yet we still get the entire\n    // viewport. In some browsers, el.offsetParent may be\n    // document.documentElement, so check for that too.\n    el !== body && el !== documentElement && utils.css(el, 'overflow') !== 'visible') {\n      var pos = utils.offset(el); // add border\n\n      pos.left += el.clientLeft;\n      pos.top += el.clientTop;\n      visibleRect.top = Math.max(visibleRect.top, pos.top);\n      visibleRect.right = Math.min(visibleRect.right,\n      // consider area without scrollBar\n      pos.left + el.clientWidth);\n      visibleRect.bottom = Math.min(visibleRect.bottom, pos.top + el.clientHeight);\n      visibleRect.left = Math.max(visibleRect.left, pos.left);\n    } else if (el === body || el === documentElement) {\n      break;\n    }\n    el = getOffsetParent(el);\n  } // Set element position to fixed\n  // make sure absolute element itself don't affect it's visible area\n  // https://github.com/ant-design/ant-design/issues/7601\n\n  var originalPosition = null;\n  if (!utils.isWindow(element) && element.nodeType !== 9) {\n    originalPosition = element.style.position;\n    var position = utils.css(element, 'position');\n    if (position === 'absolute') {\n      element.style.position = 'fixed';\n    }\n  }\n  var scrollX = utils.getWindowScrollLeft(win);\n  var scrollY = utils.getWindowScrollTop(win);\n  var viewportWidth = utils.viewportWidth(win);\n  var viewportHeight = utils.viewportHeight(win);\n  var documentWidth = documentElement.scrollWidth;\n  var documentHeight = documentElement.scrollHeight; // scrollXXX on html is sync with body which means overflow: hidden on body gets wrong scrollXXX.\n  // We should cut this ourself.\n\n  var bodyStyle = window.getComputedStyle(body);\n  if (bodyStyle.overflowX === 'hidden') {\n    documentWidth = win.innerWidth;\n  }\n  if (bodyStyle.overflowY === 'hidden') {\n    documentHeight = win.innerHeight;\n  } // Reset element position after calculate the visible area\n\n  if (element.style) {\n    element.style.position = originalPosition;\n  }\n  if (alwaysByViewport || isAncestorFixed(element)) {\n    // Clip by viewport's size.\n    visibleRect.left = Math.max(visibleRect.left, scrollX);\n    visibleRect.top = Math.max(visibleRect.top, scrollY);\n    visibleRect.right = Math.min(visibleRect.right, scrollX + viewportWidth);\n    visibleRect.bottom = Math.min(visibleRect.bottom, scrollY + viewportHeight);\n  } else {\n    // Clip by document's size.\n    var maxVisibleWidth = Math.max(documentWidth, scrollX + viewportWidth);\n    visibleRect.right = Math.min(visibleRect.right, maxVisibleWidth);\n    var maxVisibleHeight = Math.max(documentHeight, scrollY + viewportHeight);\n    visibleRect.bottom = Math.min(visibleRect.bottom, maxVisibleHeight);\n  }\n  return visibleRect.top >= 0 && visibleRect.left >= 0 && visibleRect.bottom > visibleRect.top && visibleRect.right > visibleRect.left ? visibleRect : null;\n}\nfunction adjustForViewport(elFuturePos, elRegion, visibleRect, overflow) {\n  var pos = utils.clone(elFuturePos);\n  var size = {\n    width: elRegion.width,\n    height: elRegion.height\n  };\n  if (overflow.adjustX && pos.left < visibleRect.left) {\n    pos.left = visibleRect.left;\n  } // Left edge inside and right edge outside viewport, try to resize it.\n\n  if (overflow.resizeWidth && pos.left >= visibleRect.left && pos.left + size.width > visibleRect.right) {\n    size.width -= pos.left + size.width - visibleRect.right;\n  } // Right edge outside viewport, try to move it.\n\n  if (overflow.adjustX && pos.left + size.width > visibleRect.right) {\n    // 保证左边界和可视区域左边界对齐\n    pos.left = Math.max(visibleRect.right - size.width, visibleRect.left);\n  } // Top edge outside viewport, try to move it.\n\n  if (overflow.adjustY && pos.top < visibleRect.top) {\n    pos.top = visibleRect.top;\n  } // Top edge inside and bottom edge outside viewport, try to resize it.\n\n  if (overflow.resizeHeight && pos.top >= visibleRect.top && pos.top + size.height > visibleRect.bottom) {\n    size.height -= pos.top + size.height - visibleRect.bottom;\n  } // Bottom edge outside viewport, try to move it.\n\n  if (overflow.adjustY && pos.top + size.height > visibleRect.bottom) {\n    // 保证上边界和可视区域上边界对齐\n    pos.top = Math.max(visibleRect.bottom - size.height, visibleRect.top);\n  }\n  return utils.mix(pos, size);\n}\nfunction getRegion(node) {\n  var offset;\n  var w;\n  var h;\n  if (!utils.isWindow(node) && node.nodeType !== 9) {\n    offset = utils.offset(node);\n    w = utils.outerWidth(node);\n    h = utils.outerHeight(node);\n  } else {\n    var win = utils.getWindow(node);\n    offset = {\n      left: utils.getWindowScrollLeft(win),\n      top: utils.getWindowScrollTop(win)\n    };\n    w = utils.viewportWidth(win);\n    h = utils.viewportHeight(win);\n  }\n  offset.width = w;\n  offset.height = h;\n  return offset;\n}\n\n/**\n * 获取 node 上的 align 对齐点 相对于页面的坐标\n */\nfunction getAlignOffset(region, align) {\n  var V = align.charAt(0);\n  var H = align.charAt(1);\n  var w = region.width;\n  var h = region.height;\n  var x = region.left;\n  var y = region.top;\n  if (V === 'c') {\n    y += h / 2;\n  } else if (V === 'b') {\n    y += h;\n  }\n  if (H === 'c') {\n    x += w / 2;\n  } else if (H === 'r') {\n    x += w;\n  }\n  return {\n    left: x,\n    top: y\n  };\n}\nfunction getElFuturePos(elRegion, refNodeRegion, points, offset, targetOffset) {\n  var p1 = getAlignOffset(refNodeRegion, points[1]);\n  var p2 = getAlignOffset(elRegion, points[0]);\n  var diff = [p2.left - p1.left, p2.top - p1.top];\n  return {\n    left: Math.round(elRegion.left - diff[0] + offset[0] - targetOffset[0]),\n    top: Math.round(elRegion.top - diff[1] + offset[1] - targetOffset[1])\n  };\n}\n\n/**\n * align dom node flexibly\n * <AUTHOR>\n */\n\nfunction isFailX(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.left < visibleRect.left || elFuturePos.left + elRegion.width > visibleRect.right;\n}\nfunction isFailY(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.top < visibleRect.top || elFuturePos.top + elRegion.height > visibleRect.bottom;\n}\nfunction isCompleteFailX(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.left > visibleRect.right || elFuturePos.left + elRegion.width < visibleRect.left;\n}\nfunction isCompleteFailY(elFuturePos, elRegion, visibleRect) {\n  return elFuturePos.top > visibleRect.bottom || elFuturePos.top + elRegion.height < visibleRect.top;\n}\nfunction flip(points, reg, map) {\n  var ret = [];\n  utils.each(points, function (p) {\n    ret.push(p.replace(reg, function (m) {\n      return map[m];\n    }));\n  });\n  return ret;\n}\nfunction flipOffset(offset, index) {\n  offset[index] = -offset[index];\n  return offset;\n}\nfunction convertOffset(str, offsetLen) {\n  var n;\n  if (/%$/.test(str)) {\n    n = parseInt(str.substring(0, str.length - 1), 10) / 100 * offsetLen;\n  } else {\n    n = parseInt(str, 10);\n  }\n  return n || 0;\n}\nfunction normalizeOffset(offset, el) {\n  offset[0] = convertOffset(offset[0], el.width);\n  offset[1] = convertOffset(offset[1], el.height);\n}\n/**\n * @param el\n * @param tgtRegion 参照节点所占的区域: { left, top, width, height }\n * @param align\n */\n\nfunction doAlign(el, tgtRegion, align, isTgtRegionVisible) {\n  var points = align.points;\n  var offset = align.offset || [0, 0];\n  var targetOffset = align.targetOffset || [0, 0];\n  var overflow = align.overflow;\n  var source = align.source || el;\n  offset = [].concat(offset);\n  targetOffset = [].concat(targetOffset);\n  overflow = overflow || {};\n  var newOverflowCfg = {};\n  var fail = 0;\n  var alwaysByViewport = !!(overflow && overflow.alwaysByViewport); // 当前节点可以被放置的显示区域\n\n  var visibleRect = getVisibleRectForElement(source, alwaysByViewport); // 当前节点所占的区域, left/top/width/height\n\n  var elRegion = getRegion(source); // 将 offset 转换成数值，支持百分比\n\n  normalizeOffset(offset, elRegion);\n  normalizeOffset(targetOffset, tgtRegion); // 当前节点将要被放置的位置\n\n  var elFuturePos = getElFuturePos(elRegion, tgtRegion, points, offset, targetOffset); // 当前节点将要所处的区域\n\n  var newElRegion = utils.merge(elRegion, elFuturePos); // 如果可视区域不能完全放置当前节点时允许调整\n\n  if (visibleRect && (overflow.adjustX || overflow.adjustY) && isTgtRegionVisible) {\n    if (overflow.adjustX) {\n      // 如果横向不能放下\n      if (isFailX(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        var newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l'\n        }); // 偏移量也反下\n\n        var newOffset = flipOffset(offset, 0);\n        var newTargetOffset = flipOffset(targetOffset, 0);\n        var newElFuturePos = getElFuturePos(elRegion, tgtRegion, newPoints, newOffset, newTargetOffset);\n        if (!isCompleteFailX(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n    if (overflow.adjustY) {\n      // 如果纵向不能放下\n      if (isFailY(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        var _newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't'\n        }); // 偏移量也反下\n\n        var _newOffset = flipOffset(offset, 1);\n        var _newTargetOffset = flipOffset(targetOffset, 1);\n        var _newElFuturePos = getElFuturePos(elRegion, tgtRegion, _newPoints, _newOffset, _newTargetOffset);\n        if (!isCompleteFailY(_newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = _newPoints;\n          offset = _newOffset;\n          targetOffset = _newTargetOffset;\n        }\n      }\n    } // 如果失败，重新计算当前节点将要被放置的位置\n\n    if (fail) {\n      elFuturePos = getElFuturePos(elRegion, tgtRegion, points, offset, targetOffset);\n      utils.mix(newElRegion, elFuturePos);\n    }\n    var isStillFailX = isFailX(elFuturePos, elRegion, visibleRect);\n    var isStillFailY = isFailY(elFuturePos, elRegion, visibleRect); // 检查反下后的位置是否可以放下了，如果仍然放不下：\n    // 1. 复原修改过的定位参数\n\n    if (isStillFailX || isStillFailY) {\n      var _newPoints2 = points; // 重置对应部分的翻转逻辑\n\n      if (isStillFailX) {\n        _newPoints2 = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l'\n        });\n      }\n      if (isStillFailY) {\n        _newPoints2 = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't'\n        });\n      }\n      points = _newPoints2;\n      offset = align.offset || [0, 0];\n      targetOffset = align.targetOffset || [0, 0];\n    } // 2. 只有指定了可以调整当前方向才调整\n\n    newOverflowCfg.adjustX = overflow.adjustX && isStillFailX;\n    newOverflowCfg.adjustY = overflow.adjustY && isStillFailY; // 确实要调整，甚至可能会调整高度宽度\n\n    if (newOverflowCfg.adjustX || newOverflowCfg.adjustY) {\n      newElRegion = adjustForViewport(elFuturePos, elRegion, visibleRect, newOverflowCfg);\n    }\n  } // need judge to in case set fixed with in css on height auto element\n\n  if (newElRegion.width !== elRegion.width) {\n    utils.css(source, 'width', utils.width(source) + newElRegion.width - elRegion.width);\n  }\n  if (newElRegion.height !== elRegion.height) {\n    utils.css(source, 'height', utils.height(source) + newElRegion.height - elRegion.height);\n  } // https://github.com/kissyteam/kissy/issues/190\n  // 相对于屏幕位置没变，而 left/top 变了\n  // 例如 <div 'relative'><el absolute></div>\n\n  utils.offset(source, {\n    left: newElRegion.left,\n    top: newElRegion.top\n  }, {\n    useCssRight: align.useCssRight,\n    useCssBottom: align.useCssBottom,\n    useCssTransform: align.useCssTransform,\n    ignoreShake: align.ignoreShake\n  });\n  return {\n    points: points,\n    offset: offset,\n    targetOffset: targetOffset,\n    overflow: newOverflowCfg\n  };\n}\n/**\n *  2012-04-26 <EMAIL>\n *   - 优化智能对齐算法\n *   - 慎用 resizeXX\n *\n *  2011-07-13 <EMAIL> note:\n *   - 增加智能对齐，以及大小调整选项\n **/\n\nfunction isOutOfVisibleRect(target, alwaysByViewport) {\n  var visibleRect = getVisibleRectForElement(target, alwaysByViewport);\n  var targetRegion = getRegion(target);\n  return !visibleRect || targetRegion.left + targetRegion.width <= visibleRect.left || targetRegion.top + targetRegion.height <= visibleRect.top || targetRegion.left >= visibleRect.right || targetRegion.top >= visibleRect.bottom;\n}\nfunction alignElement(el, refNode, align) {\n  var target = align.target || refNode;\n  var refNodeRegion = getRegion(target);\n  var isTargetNotOutOfVisible = !isOutOfVisibleRect(target, align.overflow && align.overflow.alwaysByViewport);\n  return doAlign(el, refNodeRegion, align, isTargetNotOutOfVisible);\n}\nalignElement.__getOffsetParent = getOffsetParent;\nalignElement.__getVisibleRectForElement = getVisibleRectForElement;\n\n/**\n * `tgtPoint`: { pageX, pageY } or { clientX, clientY }.\n * If client position provided, will internal convert to page position.\n */\n\nfunction alignPoint(el, tgtPoint, align) {\n  var pageX;\n  var pageY;\n  var doc = utils.getDocument(el);\n  var win = doc.defaultView || doc.parentWindow;\n  var scrollX = utils.getWindowScrollLeft(win);\n  var scrollY = utils.getWindowScrollTop(win);\n  var viewportWidth = utils.viewportWidth(win);\n  var viewportHeight = utils.viewportHeight(win);\n  if ('pageX' in tgtPoint) {\n    pageX = tgtPoint.pageX;\n  } else {\n    pageX = scrollX + tgtPoint.clientX;\n  }\n  if ('pageY' in tgtPoint) {\n    pageY = tgtPoint.pageY;\n  } else {\n    pageY = scrollY + tgtPoint.clientY;\n  }\n  var tgtRegion = {\n    left: pageX,\n    top: pageY,\n    width: 0,\n    height: 0\n  };\n  var pointInView = pageX >= 0 && pageX <= scrollX + viewportWidth && pageY >= 0 && pageY <= scrollY + viewportHeight; // Provide default target point\n\n  var points = [align.points[0], 'cc'];\n  return doAlign(el, tgtRegion, _objectSpread2(_objectSpread2({}, align), {}, {\n    points: points\n  }), pointInView);\n}\nexport default alignElement;\nexport { alignElement, alignPoint };", "map": {"version": 3, "names": ["vendorPrefix", "jsCssMap", "Webkit", "<PERSON><PERSON>", "ms", "O", "getVendorPrefix", "undefined", "style", "document", "createElement", "testProp", "key", "getTransitionName", "concat", "getTransformName", "setTransitionProperty", "node", "value", "name", "transitionProperty", "setTransform", "transform", "getTransitionProperty", "getTransformXY", "window", "getComputedStyle", "getPropertyValue", "matrix", "replace", "split", "x", "parseFloat", "y", "matrix2d", "matrix3d", "setTransformXY", "xy", "arr", "match2d", "match", "map", "item", "join", "match3d", "RE_NUM", "source", "getComputedStyleX", "forceRelayout", "elem", "originalStyle", "display", "offsetHeight", "css", "el", "v", "_typeof", "i", "hasOwnProperty", "getClientPosition", "box", "doc", "ownerDocument", "body", "doc<PERSON><PERSON>", "documentElement", "getBoundingClientRect", "Math", "floor", "left", "top", "clientLeft", "clientTop", "getScroll", "w", "ret", "method", "d", "getScrollLeft", "getScrollTop", "getOffset", "pos", "defaultView", "parentWindow", "isWindow", "obj", "getDocument", "nodeType", "_getComputedStyle", "cs", "computedStyle", "val", "_RE_NUM_NO_PX", "RegExp", "RE_POS", "CURRENT_STYLE", "RUNTIME_STYLE", "LEFT", "PX", "_getComputedStyleIE", "test", "rsLeft", "pixelLeft", "getOffsetDirection", "dir", "option", "useCssRight", "useCssBottom", "oppositeOffsetDirection", "setLeftTop", "offset", "position", "presetH", "presetV", "horizontalProperty", "verticalProperty", "oppositeHorizontalProperty", "oppositeVerticalProperty", "originalTransition", "originalOffset", "old", "preset", "off", "_key", "_dir", "_off", "setTransform$1", "originalXY", "resultXY", "setOffset", "ignoreShake", "oriOffset", "oLeft", "toFixed", "oTop", "tLeft", "tTop", "useCssTransform", "each", "fn", "length", "isBorderBoxFn", "BOX_MODELS", "CONTENT_INDEX", "PADDING_INDEX", "BORDER_INDEX", "MARGIN_INDEX", "swap", "options", "callback", "call", "getPBMWidth", "props", "which", "prop", "j", "cssProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getParent", "element", "parent", "host", "parentNode", "refWin", "max", "win", "documentElementProp", "compatMode", "getWH", "ex", "extra", "viewportWidth", "viewportHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "borderBoxValue", "width", "height", "isBorderBox", "cssBoxValue", "Number", "borderBoxValueOrIsBorderBox", "slice", "cssShow", "visibility", "getWHIgnoreDisplay", "_len", "arguments", "args", "Array", "_key2", "offsetWidth", "apply", "first", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "mix", "to", "from", "utils", "getWindow", "setTimeout", "clone", "overflow", "getWindowScrollLeft", "getWindowScrollTop", "merge", "getOffsetParent", "positionStyle", "skipStatic", "nodeName", "toLowerCase", "getParent$1", "isAncestorFixed", "getVisibleRectForElement", "alwaysByViewport", "visibleRect", "right", "Infinity", "bottom", "navigator", "userAgent", "indexOf", "clientWidth", "min", "clientHeight", "originalPosition", "scrollX", "scrollY", "documentWidth", "scrollWidth", "documentHeight", "scrollHeight", "bodyStyle", "overflowX", "innerWidth", "overflowY", "innerHeight", "maxVisibleWidth", "maxVisibleHeight", "adjustForViewport", "elFuturePos", "elRegion", "size", "adjustX", "resizeWidth", "adjustY", "resizeHeight", "getRegion", "h", "outerWidth", "outerHeight", "getAlignOffset", "region", "align", "V", "H", "getElFuturePos", "refNodeRegion", "points", "targetOffset", "p1", "p2", "diff", "round", "isFailX", "isFailY", "isCompleteFailX", "isCompleteFailY", "flip", "reg", "p", "push", "m", "flipOffset", "index", "convertOffset", "str", "offsetLen", "n", "parseInt", "substring", "normalizeOffset", "doAlign", "tgtRegion", "isTgtRegionVisible", "newOverflowCfg", "fail", "newElRegion", "newPoints", "l", "r", "newOffset", "newTargetOffset", "newElFuturePos", "_newPoints", "t", "b", "_newOffset", "_newTargetOffset", "_newElFuturePos", "isStillFailX", "isStillFailY", "_newPoints2", "isOutOfVisibleRect", "target", "targetRegion", "alignElement", "refNode", "isTargetNotOutOfVisible", "__getOffsetParent", "__getVisibleRectForElement", "alignPoint", "tgtPoint", "pageX", "pageY", "clientX", "clientY", "pointInView", "_objectSpread2"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\propertyUtils.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\utils.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\getOffsetParent.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\isAncestorFixed.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\getVisibleRectForElement.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\adjustForViewport.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\getRegion.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\getAlignOffset.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\getElFuturePos.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\align\\align.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\align\\alignElement.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\src\\align\\alignPoint.js"], "sourcesContent": ["let vendorPrefix;\n\nconst jsCssMap = {\n  Webkit: '-webkit-',\n  Moz: '-moz-',\n  // IE did it wrong again ...\n  ms: '-ms-',\n  O: '-o-',\n};\n\nfunction getVendorPrefix() {\n  if (vendorPrefix !== undefined) {\n    return vendorPrefix;\n  }\n  vendorPrefix = '';\n  const style = document.createElement('p').style;\n  const testProp = 'Transform';\n  for (const key in jsCssMap) {\n    if (key + testProp in style) {\n      vendorPrefix = key;\n    }\n  }\n  return vendorPrefix;\n}\n\nfunction getTransitionName() {\n  return getVendorPrefix()\n    ? `${getVendorPrefix()}TransitionProperty`\n    : 'transitionProperty';\n}\n\nexport function getTransformName() {\n  return getVendorPrefix() ? `${getVendorPrefix()}Transform` : 'transform';\n}\n\nexport function setTransitionProperty(node, value) {\n  const name = getTransitionName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transitionProperty') {\n      node.style.transitionProperty = value;\n    }\n  }\n}\n\nfunction setTransform(node, value) {\n  const name = getTransformName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transform') {\n      node.style.transform = value;\n    }\n  }\n}\n\nexport function getTransitionProperty(node) {\n  return node.style.transitionProperty || node.style[getTransitionName()];\n}\n\nexport function getTransformXY(node) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    const matrix = transform.replace(/[^0-9\\-.,]/g, '').split(',');\n    return {\n      x: parseFloat(matrix[12] || matrix[4], 0),\n      y: parseFloat(matrix[13] || matrix[5], 0),\n    };\n  }\n  return {\n    x: 0,\n    y: 0,\n  };\n}\n\nconst matrix2d = /matrix\\((.*)\\)/;\nconst matrix3d = /matrix3d\\((.*)\\)/;\n\nexport function setTransformXY(node, xy) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    let arr;\n    let match2d = transform.match(matrix2d);\n    if (match2d) {\n      match2d = match2d[1];\n      arr = match2d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[4] = xy.x;\n      arr[5] = xy.y;\n      setTransform(node, `matrix(${arr.join(',')})`);\n    } else {\n      const match3d = transform.match(matrix3d)[1];\n      arr = match3d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[12] = xy.x;\n      arr[13] = xy.y;\n      setTransform(node, `matrix3d(${arr.join(',')})`);\n    }\n  } else {\n    setTransform(\n      node,\n      `translateX(${xy.x}px) translateY(${xy.y}px) translateZ(0)`,\n    );\n  }\n}\n", "import {\n  setTransitionProperty,\n  getTransitionProperty,\n  getTransformXY,\n  setTransformXY,\n  getTransformName,\n} from './propertyUtils';\n\nconst RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\n\nlet getComputedStyleX;\n\n// https://stackoverflow.com/a/3485654/3040605\nfunction forceRelayout(elem) {\n  const originalStyle = elem.style.display;\n  elem.style.display = 'none';\n  elem.offsetHeight; // eslint-disable-line\n  elem.style.display = originalStyle;\n}\n\nfunction css(el, name, v) {\n  let value = v;\n  if (typeof name === 'object') {\n    for (const i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value = `${value}px`;\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\n\nfunction getClientPosition(elem) {\n  let box;\n  let x;\n  let y;\n  const doc = elem.ownerDocument;\n  const body = doc.body;\n  const docElem = doc && doc.documentElement;\n  // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n  box = elem.getBoundingClientRect();\n\n  // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = Math.floor(box.left);\n  y = Math.floor(box.top);\n\n  // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nfunction getScroll(w, top) {\n  let ret = w[`page${top ? 'Y' : 'X'}Offset`];\n  const method = `scroll${top ? 'Top' : 'Left'}`;\n  if (typeof ret !== 'number') {\n    const d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\n\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\n\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\n\nfunction getOffset(el) {\n  const pos = getClientPosition(el);\n  const doc = el.ownerDocument;\n  const w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\nfunction isWindow(obj) {\n  // must use == for ie8\n  /* eslint eqeqeq:0 */\n  return obj !== null && obj !== undefined && obj == obj.window;\n}\n\nfunction getDocument(node) {\n  if (isWindow(node)) {\n    return node.document;\n  }\n  if (node.nodeType === 9) {\n    return node;\n  }\n  return node.ownerDocument;\n}\n\nfunction _getComputedStyle(elem, name, cs) {\n  let computedStyle = cs;\n  let val = '';\n  const d = getDocument(elem);\n  computedStyle = computedStyle || d.defaultView.getComputedStyle(elem, null);\n\n  // https://github.com/kissyteam/kissy/issues/61\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n\n  return val;\n}\n\nconst _RE_NUM_NO_PX = new RegExp(`^(${RE_NUM})(?!px)[a-z%]+$`, 'i');\nconst RE_POS = /^(top|right|bottom|left)$/;\nconst CURRENT_STYLE = 'currentStyle';\nconst RUNTIME_STYLE = 'runtimeStyle';\nconst LEFT = 'left';\nconst PX = 'px';\n\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  let ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name];\n\n  // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    const style = elem.style;\n    const left = style[LEFT];\n    const rsLeft = elem[RUNTIME_STYLE][LEFT];\n\n    // prevent flashing of content\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT];\n\n    // Put in the new values to get a computed value out\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX;\n\n    // Revert the changed values\n    style[LEFT] = left;\n\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\n\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle\n    ? _getComputedStyle\n    : _getComputedStyleIE;\n}\n\nfunction getOffsetDirection(dir, option) {\n  if (dir === 'left') {\n    return option.useCssRight ? 'right' : dir;\n  }\n  return option.useCssBottom ? 'bottom' : dir;\n}\n\nfunction oppositeOffsetDirection(dir) {\n  if (dir === 'left') {\n    return 'right';\n  } else if (dir === 'right') {\n    return 'left';\n  } else if (dir === 'top') {\n    return 'bottom';\n  } else if (dir === 'bottom') {\n    return 'top';\n  }\n}\n\n// 设置 elem 相对 elem.ownerDocument 的坐标\nfunction setLeftTop(elem, offset, option) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n  let presetH = -999;\n  let presetV = -999;\n  const horizontalProperty = getOffsetDirection('left', option);\n  const verticalProperty = getOffsetDirection('top', option);\n  const oppositeHorizontalProperty = oppositeOffsetDirection(\n    horizontalProperty,\n  );\n  const oppositeVerticalProperty = oppositeOffsetDirection(verticalProperty);\n\n  if (horizontalProperty !== 'left') {\n    presetH = 999;\n  }\n\n  if (verticalProperty !== 'top') {\n    presetV = 999;\n  }\n  let originalTransition = '';\n  const originalOffset = getOffset(elem);\n  if ('left' in offset || 'top' in offset) {\n    originalTransition = getTransitionProperty(elem) || '';\n    setTransitionProperty(elem, 'none');\n  }\n  if ('left' in offset) {\n    elem.style[oppositeHorizontalProperty] = '';\n    elem.style[horizontalProperty] = `${presetH}px`;\n  }\n  if ('top' in offset) {\n    elem.style[oppositeVerticalProperty] = '';\n    elem.style[verticalProperty] = `${presetV}px`;\n  }\n  // force relayout\n  forceRelayout(elem);\n  const old = getOffset(elem);\n  const originalStyle = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const preset = key === 'left' ? presetH : presetV;\n      const off = originalOffset[key] - old[key];\n      if (dir === key) {\n        originalStyle[dir] = preset + off;\n      } else {\n        originalStyle[dir] = preset - off;\n      }\n    }\n  }\n  css(elem, originalStyle);\n  // force relayout\n  forceRelayout(elem);\n  if ('left' in offset || 'top' in offset) {\n    setTransitionProperty(elem, originalTransition);\n  }\n  const ret = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const off = offset[key] - originalOffset[key];\n      if (key === dir) {\n        ret[dir] = originalStyle[dir] + off;\n      } else {\n        ret[dir] = originalStyle[dir] - off;\n      }\n    }\n  }\n  css(elem, ret);\n}\n\nfunction setTransform(elem, offset) {\n  const originalOffset = getOffset(elem);\n  const originalXY = getTransformXY(elem);\n  const resultXY = { x: originalXY.x, y: originalXY.y };\n  if ('left' in offset) {\n    resultXY.x = originalXY.x + offset.left - originalOffset.left;\n  }\n  if ('top' in offset) {\n    resultXY.y = originalXY.y + offset.top - originalOffset.top;\n  }\n  setTransformXY(elem, resultXY);\n}\n\nfunction setOffset(elem, offset, option) {\n  if (option.ignoreShake) {\n    const oriOffset = getOffset(elem);\n\n    const oLeft = oriOffset.left.toFixed(0);\n    const oTop = oriOffset.top.toFixed(0);\n    const tLeft = offset.left.toFixed(0);\n    const tTop = offset.top.toFixed(0);\n\n    if (oLeft === tLeft && oTop === tTop) {\n      return;\n    }\n  }\n\n  if (option.useCssRight || option.useCssBottom) {\n    setLeftTop(elem, offset, option);\n  } else if (\n    option.useCssTransform &&\n    getTransformName() in document.body.style\n  ) {\n    setTransform(elem, offset, option);\n  } else {\n    setLeftTop(elem, offset, option);\n  }\n}\n\nfunction each(arr, fn) {\n  for (let i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\n\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\n\nconst BOX_MODELS = ['margin', 'border', 'padding'];\nconst CONTENT_INDEX = -1;\nconst PADDING_INDEX = 2;\nconst BORDER_INDEX = 1;\nconst MARGIN_INDEX = 0;\n\nfunction swap(elem, options, callback) {\n  const old = {};\n  const style = elem.style;\n  let name;\n\n  // Remember the old values, and insert the new ones\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n\n  callback.call(elem);\n\n  // Revert the old values\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\n\nfunction getPBMWidth(elem, props, which) {\n  let value = 0;\n  let prop;\n  let j;\n  let i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        let cssProp;\n        if (prop === 'border') {\n          cssProp = `${prop}${which[i]}Width`;\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\n\nconst domUtils = {\n  getParent(element) {\n    let parent = element;\n    do {\n      if (parent.nodeType === 11 && parent.host) {\n        parent = parent.host;\n      } else {\n        parent = parent.parentNode;\n      }\n    } while (parent && parent.nodeType !== 1 && parent.nodeType !== 9);\n    return parent;\n  },\n};\n\neach(['Width', 'Height'], name => {\n  domUtils[`doc${name}`] = refWin => {\n    const d = refWin.document;\n    return Math.max(\n      // firefox chrome documentElement.scrollHeight< body.scrollHeight\n      // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n      d.documentElement[`scroll${name}`],\n      // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n      d.body[`scroll${name}`],\n      domUtils[`viewport${name}`](d),\n    );\n  };\n\n  domUtils[`viewport${name}`] = win => {\n    // pc browser includes scrollbar in window.innerWidth\n    const prop = `client${name}`;\n    const doc = win.document;\n    const body = doc.body;\n    const documentElement = doc.documentElement;\n    const documentElementProp = documentElement[prop];\n    // 标准模式取 documentElement\n    // backcompat 取 body\n    return (\n      (doc.compatMode === 'CSS1Compat' && documentElementProp) ||\n      (body && body[prop]) ||\n      documentElementProp\n    );\n  };\n});\n\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\nfunction getWH(elem, name, ex) {\n  let extra = ex;\n  if (isWindow(elem)) {\n    return name === 'width'\n      ? domUtils.viewportWidth(elem)\n      : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width'\n      ? domUtils.docWidth(elem)\n      : domUtils.docHeight(elem);\n  }\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  let borderBoxValue =\n    name === 'width'\n      ? Math.floor(elem.getBoundingClientRect().width)\n      : Math.floor(elem.getBoundingClientRect().height);\n  const isBorderBox = isBorderBoxFn(elem);\n  let cssBoxValue = 0;\n  if (\n    borderBoxValue === null ||\n    borderBoxValue === undefined ||\n    borderBoxValue <= 0\n  ) {\n    borderBoxValue = undefined;\n    // Fall back to computed then un computed css if necessary\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (\n      cssBoxValue === null ||\n      cssBoxValue === undefined ||\n      Number(cssBoxValue) < 0\n    ) {\n      cssBoxValue = elem.style[name] || 0;\n    }\n    // Normalize '', auto, and prepare for extra\n    cssBoxValue = parseFloat(cssBoxValue) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  const borderBoxValueOrIsBorderBox =\n    borderBoxValue !== undefined || isBorderBox;\n  const val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which);\n    }\n    return cssBoxValue;\n  } else if (borderBoxValueOrIsBorderBox) {\n    if (extra === BORDER_INDEX) {\n      return val;\n    }\n    return (\n      val +\n      (extra === PADDING_INDEX\n        ? -getPBMWidth(elem, ['border'], which)\n        : getPBMWidth(elem, ['margin'], which))\n    );\n  }\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which);\n}\n\nconst cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block',\n};\n\n// fix #119 : https://github.com/kissyteam/kissy/issues/119\nfunction getWHIgnoreDisplay(...args) {\n  let val;\n  const elem = args[0];\n  // in case elem is window\n  // elem.offsetWidth === undefined\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, () => {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\n\neach(['width', 'height'], name => {\n  const first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[`outer${first}`] = (el, includeMargin) => {\n    return (\n      el &&\n      getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX)\n    );\n  };\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n\n  domUtils[name] = (elem, v) => {\n    let val = v;\n    if (val !== undefined) {\n      if (elem) {\n        const isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\n\nfunction mix(to, from) {\n  for (const i in from) {\n    if (from.hasOwnProperty(i)) {\n      to[i] = from[i];\n    }\n  }\n  return to;\n}\n\nconst utils = {\n  getWindow(node) {\n    if (node && node.document && node.setTimeout) {\n      return node;\n    }\n    const doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  getDocument,\n  offset(el, value, option) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value, option || {});\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow,\n  each,\n  css,\n  clone(obj) {\n    let i;\n    const ret = {};\n    for (i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    const overflow = obj.overflow;\n    if (overflow) {\n      for (i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  mix,\n  getWindowScrollLeft(w) {\n    return getScrollLeft(w);\n  },\n  getWindowScrollTop(w) {\n    return getScrollTop(w);\n  },\n  merge(...args) {\n    const ret = {};\n    for (let i = 0; i < args.length; i++) {\n      utils.mix(ret, args[i]);\n    }\n    return ret;\n  },\n  viewportWidth: 0,\n  viewportHeight: 0,\n};\n\nmix(utils, domUtils);\n\nexport default utils;\n", "import utils from './utils';\n\n/**\n * 得到会导致元素显示不全的祖先元素\n */\nconst { getParent } = utils;\n\nfunction getOffsetParent(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return null;\n  }\n  // ie 这个也不是完全可行\n  /*\n   <div style=\"width: 50px;height: 100px;overflow: hidden\">\n   <div style=\"width: 50px;height: 100px;position: relative;\" id=\"d6\">\n   元素 6 高 100px 宽 50px<br/>\n   </div>\n   </div>\n   */\n  // element.offsetParent does the right thing in ie7 and below. Return parent with layout!\n  //  In other browsers it only includes elements with position absolute, relative or\n  // fixed, not elements with overflow set to auto or scroll.\n  //        if (UA.ie && ieMode < 8) {\n  //            return element.offsetParent;\n  //        }\n  // 统一的 offsetParent 方法\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent;\n  let positionStyle = utils.css(element, 'position');\n  const skipStatic = positionStyle === 'fixed' || positionStyle === 'absolute';\n\n  if (!skipStatic) {\n    return element.nodeName.toLowerCase() === 'html'\n      ? null\n      : getParent(element);\n  }\n\n  for (\n    parent = getParent(element);\n    parent && parent !== body && parent.nodeType !== 9;\n    parent = getParent(parent)\n  ) {\n    positionStyle = utils.css(parent, 'position');\n    if (positionStyle !== 'static') {\n      return parent;\n    }\n  }\n  return null;\n}\n\nexport default getOffsetParent;\n", "import utils from './utils';\n\nconst { getParent } = utils;\n\nexport default function isAncestorFixed(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return false;\n  }\n\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent = null;\n  for (\n    parent = getParent(element);\n    // 修复元素位于 document.documentElement 下导致崩溃问题\n    parent && parent !== body && parent !== doc;\n    parent = getParent(parent)\n  ) {\n    const positionStyle = utils.css(parent, 'position');\n    if (positionStyle === 'fixed') {\n      return true;\n    }\n  }\n  return false;\n}\n", "import utils from './utils';\nimport getOffsetParent from './getOffsetParent';\nimport isAncestorFixed from './isAncestorFixed';\n\n/**\n * 获得元素的显示部分的区域\n */\nfunction getVisibleRectForElement(element, alwaysByViewport) {\n  const visibleRect = {\n    left: 0,\n    right: Infinity,\n    top: 0,\n    bottom: Infinity,\n  };\n  let el = getOffsetParent(element);\n  const doc = utils.getDocument(element);\n  const win = doc.defaultView || doc.parentWindow;\n  const body = doc.body;\n  const documentElement = doc.documentElement;\n\n  // Determine the size of the visible rect by climbing the dom accounting for\n  // all scrollable containers.\n  while (el) {\n    // clientWidth is zero for inline block elements in ie.\n    if (\n      (navigator.userAgent.indexOf('MSIE') === -1 || el.clientWidth !== 0) &&\n      // body may have overflow set on it, yet we still get the entire\n      // viewport. In some browsers, el.offsetParent may be\n      // document.documentElement, so check for that too.\n      (el !== body &&\n        el !== documentElement &&\n        utils.css(el, 'overflow') !== 'visible')\n    ) {\n      const pos = utils.offset(el);\n      // add border\n      pos.left += el.clientLeft;\n      pos.top += el.clientTop;\n      visibleRect.top = Math.max(visibleRect.top, pos.top);\n      visibleRect.right = Math.min(\n        visibleRect.right,\n        // consider area without scrollBar\n        pos.left + el.clientWidth,\n      );\n      visibleRect.bottom = Math.min(\n        visibleRect.bottom,\n        pos.top + el.clientHeight,\n      );\n      visibleRect.left = Math.max(visibleRect.left, pos.left);\n    } else if (el === body || el === documentElement) {\n      break;\n    }\n    el = getOffsetParent(el);\n  }\n\n  // Set element position to fixed\n  // make sure absolute element itself don't affect it's visible area\n  // https://github.com/ant-design/ant-design/issues/7601\n  let originalPosition = null;\n  if (!utils.isWindow(element) && element.nodeType !== 9) {\n    originalPosition = element.style.position;\n    const position = utils.css(element, 'position');\n    if (position === 'absolute') {\n      element.style.position = 'fixed';\n    }\n  }\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n  let documentWidth = documentElement.scrollWidth;\n  let documentHeight = documentElement.scrollHeight;\n\n  // scrollXXX on html is sync with body which means overflow: hidden on body gets wrong scrollXXX.\n  // We should cut this ourself.\n  const bodyStyle = window.getComputedStyle(body);\n  if (bodyStyle.overflowX === 'hidden') {\n    documentWidth = win.innerWidth;\n  }\n  if (bodyStyle.overflowY === 'hidden') {\n    documentHeight = win.innerHeight;\n  }\n\n  // Reset element position after calculate the visible area\n  if (element.style) {\n    element.style.position = originalPosition;\n  }\n\n  if (alwaysByViewport || isAncestorFixed(element)) {\n    // Clip by viewport's size.\n    visibleRect.left = Math.max(visibleRect.left, scrollX);\n    visibleRect.top = Math.max(visibleRect.top, scrollY);\n    visibleRect.right = Math.min(visibleRect.right, scrollX + viewportWidth);\n    visibleRect.bottom = Math.min(visibleRect.bottom, scrollY + viewportHeight);\n  } else {\n    // Clip by document's size.\n    const maxVisibleWidth = Math.max(documentWidth, scrollX + viewportWidth);\n    visibleRect.right = Math.min(visibleRect.right, maxVisibleWidth);\n\n    const maxVisibleHeight = Math.max(documentHeight, scrollY + viewportHeight);\n    visibleRect.bottom = Math.min(visibleRect.bottom, maxVisibleHeight);\n  }\n\n  return visibleRect.top >= 0 &&\n    visibleRect.left >= 0 &&\n    visibleRect.bottom > visibleRect.top &&\n    visibleRect.right > visibleRect.left\n    ? visibleRect\n    : null;\n}\n\nexport default getVisibleRectForElement;\n", "import utils from './utils';\n\nfunction adjustForViewport(elFuturePos, elRegion, visibleRect, overflow) {\n  const pos = utils.clone(elFuturePos);\n  const size = {\n    width: elRegion.width,\n    height: elRegion.height,\n  };\n\n  if (overflow.adjustX && pos.left < visibleRect.left) {\n    pos.left = visibleRect.left;\n  }\n\n  // Left edge inside and right edge outside viewport, try to resize it.\n  if (\n    overflow.resizeWidth &&\n    pos.left >= visibleRect.left &&\n    pos.left + size.width > visibleRect.right\n  ) {\n    size.width -= pos.left + size.width - visibleRect.right;\n  }\n\n  // Right edge outside viewport, try to move it.\n  if (overflow.adjustX && pos.left + size.width > visibleRect.right) {\n    // 保证左边界和可视区域左边界对齐\n    pos.left = Math.max(visibleRect.right - size.width, visibleRect.left);\n  }\n\n  // Top edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top < visibleRect.top) {\n    pos.top = visibleRect.top;\n  }\n\n  // Top edge inside and bottom edge outside viewport, try to resize it.\n  if (\n    overflow.resizeHeight &&\n    pos.top >= visibleRect.top &&\n    pos.top + size.height > visibleRect.bottom\n  ) {\n    size.height -= pos.top + size.height - visibleRect.bottom;\n  }\n\n  // Bottom edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top + size.height > visibleRect.bottom) {\n    // 保证上边界和可视区域上边界对齐\n    pos.top = Math.max(visibleRect.bottom - size.height, visibleRect.top);\n  }\n\n  return utils.mix(pos, size);\n}\n\nexport default adjustForViewport;\n", "import utils from './utils';\n\nfunction getRegion(node) {\n  let offset;\n  let w;\n  let h;\n  if (!utils.isWindow(node) && node.nodeType !== 9) {\n    offset = utils.offset(node);\n    w = utils.outerWidth(node);\n    h = utils.outerHeight(node);\n  } else {\n    const win = utils.getWindow(node);\n    offset = {\n      left: utils.getWindowScrollLeft(win),\n      top: utils.getWindowScrollTop(win),\n    };\n    w = utils.viewportWidth(win);\n    h = utils.viewportHeight(win);\n  }\n  offset.width = w;\n  offset.height = h;\n  return offset;\n}\n\nexport default getRegion;\n", "/**\n * 获取 node 上的 align 对齐点 相对于页面的坐标\n */\n\nfunction getAlignOffset(region, align) {\n  const V = align.charAt(0);\n  const H = align.charAt(1);\n  const w = region.width;\n  const h = region.height;\n\n  let x = region.left;\n  let y = region.top;\n\n  if (V === 'c') {\n    y += h / 2;\n  } else if (V === 'b') {\n    y += h;\n  }\n\n  if (H === 'c') {\n    x += w / 2;\n  } else if (H === 'r') {\n    x += w;\n  }\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nexport default getAlignOffset;\n", "import getAlignOffset from './getAlignOffset';\n\nfunction getElFuturePos(elRegion, refNodeRegion, points, offset, targetOffset) {\n  const p1 = getAlignOffset(refNodeRegion, points[1]);\n  const p2 = getAlignOffset(elRegion, points[0]);\n  const diff = [p2.left - p1.left, p2.top - p1.top];\n\n  return {\n    left: Math.round(elRegion.left - diff[0] + offset[0] - targetOffset[0]),\n    top: Math.round(elRegion.top - diff[1] + offset[1] - targetOffset[1]),\n  };\n}\n\nexport default getElFuturePos;\n", "/**\n * align dom node flexibly\n * <AUTHOR>\n */\n\nimport utils from '../utils';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport adjustForViewport from '../adjustForViewport';\nimport getRegion from '../getRegion';\nimport getElFuturePos from '../getElFuturePos';\n\n// http://yiminghe.iteye.com/blog/1124720\n\nfunction isFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left < visibleRect.left ||\n    elFuturePos.left + elRegion.width > visibleRect.right\n  );\n}\n\nfunction isFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top < visibleRect.top ||\n    elFuturePos.top + elRegion.height > visibleRect.bottom\n  );\n}\n\nfunction isCompleteFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left > visibleRect.right ||\n    elFuturePos.left + elRegion.width < visibleRect.left\n  );\n}\n\nfunction isCompleteFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top > visibleRect.bottom ||\n    elFuturePos.top + elRegion.height < visibleRect.top\n  );\n}\n\nfunction flip(points, reg, map) {\n  const ret = [];\n  utils.each(points, p => {\n    ret.push(\n      p.replace(reg, m => {\n        return map[m];\n      }),\n    );\n  });\n  return ret;\n}\n\nfunction flipOffset(offset, index) {\n  offset[index] = -offset[index];\n  return offset;\n}\n\nfunction convertOffset(str, offsetLen) {\n  let n;\n  if (/%$/.test(str)) {\n    n = (parseInt(str.substring(0, str.length - 1), 10) / 100) * offsetLen;\n  } else {\n    n = parseInt(str, 10);\n  }\n  return n || 0;\n}\n\nfunction normalizeOffset(offset, el) {\n  offset[0] = convertOffset(offset[0], el.width);\n  offset[1] = convertOffset(offset[1], el.height);\n}\n\n/**\n * @param el\n * @param tgtRegion 参照节点所占的区域: { left, top, width, height }\n * @param align\n */\nfunction doAlign(el, tgtRegion, align, isTgtRegionVisible) {\n  let points = align.points;\n  let offset = align.offset || [0, 0];\n  let targetOffset = align.targetOffset || [0, 0];\n  let overflow = align.overflow;\n  const source = align.source || el;\n  offset = [].concat(offset);\n  targetOffset = [].concat(targetOffset);\n  overflow = overflow || {};\n  const newOverflowCfg = {};\n  let fail = 0;\n  const alwaysByViewport = !!(overflow && overflow.alwaysByViewport);\n  // 当前节点可以被放置的显示区域\n  const visibleRect = getVisibleRectForElement(source, alwaysByViewport);\n  // 当前节点所占的区域, left/top/width/height\n  const elRegion = getRegion(source);\n  // 将 offset 转换成数值，支持百分比\n  normalizeOffset(offset, elRegion);\n  normalizeOffset(targetOffset, tgtRegion);\n  // 当前节点将要被放置的位置\n  let elFuturePos = getElFuturePos(\n    elRegion,\n    tgtRegion,\n    points,\n    offset,\n    targetOffset,\n  );\n  // 当前节点将要所处的区域\n  let newElRegion = utils.merge(elRegion, elFuturePos);\n\n  // 如果可视区域不能完全放置当前节点时允许调整\n  if (\n    visibleRect &&\n    (overflow.adjustX || overflow.adjustY) &&\n    isTgtRegionVisible\n  ) {\n    if (overflow.adjustX) {\n      // 如果横向不能放下\n      if (isFailX(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 0);\n        const newTargetOffset = flipOffset(targetOffset, 0);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailX(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    if (overflow.adjustY) {\n      // 如果纵向不能放下\n      if (isFailY(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 1);\n        const newTargetOffset = flipOffset(targetOffset, 1);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailY(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    // 如果失败，重新计算当前节点将要被放置的位置\n    if (fail) {\n      elFuturePos = getElFuturePos(\n        elRegion,\n        tgtRegion,\n        points,\n        offset,\n        targetOffset,\n      );\n      utils.mix(newElRegion, elFuturePos);\n    }\n    const isStillFailX = isFailX(elFuturePos, elRegion, visibleRect);\n    const isStillFailY = isFailY(elFuturePos, elRegion, visibleRect);\n    // 检查反下后的位置是否可以放下了，如果仍然放不下：\n    // 1. 复原修改过的定位参数\n    if (isStillFailX || isStillFailY) {\n      let newPoints = points;\n\n      // 重置对应部分的翻转逻辑\n      if (isStillFailX) {\n        newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n      }\n      if (isStillFailY) {\n        newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n      }\n\n      points = newPoints;\n\n      offset = align.offset || [0, 0];\n      targetOffset = align.targetOffset || [0, 0];\n    }\n    // 2. 只有指定了可以调整当前方向才调整\n    newOverflowCfg.adjustX = overflow.adjustX && isStillFailX;\n    newOverflowCfg.adjustY = overflow.adjustY && isStillFailY;\n\n    // 确实要调整，甚至可能会调整高度宽度\n    if (newOverflowCfg.adjustX || newOverflowCfg.adjustY) {\n      newElRegion = adjustForViewport(\n        elFuturePos,\n        elRegion,\n        visibleRect,\n        newOverflowCfg,\n      );\n    }\n  }\n\n  // need judge to in case set fixed with in css on height auto element\n  if (newElRegion.width !== elRegion.width) {\n    utils.css(\n      source,\n      'width',\n      utils.width(source) + newElRegion.width - elRegion.width,\n    );\n  }\n\n  if (newElRegion.height !== elRegion.height) {\n    utils.css(\n      source,\n      'height',\n      utils.height(source) + newElRegion.height - elRegion.height,\n    );\n  }\n\n  // https://github.com/kissyteam/kissy/issues/190\n  // 相对于屏幕位置没变，而 left/top 变了\n  // 例如 <div 'relative'><el absolute></div>\n  utils.offset(\n    source,\n    {\n      left: newElRegion.left,\n      top: newElRegion.top,\n    },\n    {\n      useCssRight: align.useCssRight,\n      useCssBottom: align.useCssBottom,\n      useCssTransform: align.useCssTransform,\n      ignoreShake: align.ignoreShake,\n    },\n  );\n\n  return {\n    points,\n    offset,\n    targetOffset,\n    overflow: newOverflowCfg,\n  };\n}\n\nexport default doAlign;\n/**\n *  2012-04-26 <EMAIL>\n *   - 优化智能对齐算法\n *   - 慎用 resizeXX\n *\n *  2011-07-13 <EMAIL> note:\n *   - 增加智能对齐，以及大小调整选项\n **/\n", "import doAlign from './align';\nimport getOffsetParent from '../getOffsetParent';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport getRegion from '../getRegion';\n\nfunction isOutOfVisibleRect(target, alwaysByViewport) {\n  const visibleRect = getVisibleRectForElement(target, alwaysByViewport);\n  const targetRegion = getRegion(target);\n\n  return (\n    !visibleRect ||\n    targetRegion.left + targetRegion.width <= visibleRect.left ||\n    targetRegion.top + targetRegion.height <= visibleRect.top ||\n    targetRegion.left >= visibleRect.right ||\n    targetRegion.top >= visibleRect.bottom\n  );\n}\n\nfunction alignElement(el, refNode, align) {\n  const target = align.target || refNode;\n  const refNodeRegion = getRegion(target);\n\n  const isTargetNotOutOfVisible = !isOutOfVisibleRect(\n    target,\n    align.overflow && align.overflow.alwaysByViewport,\n  );\n\n  return doAlign(el, refNodeRegion, align, isTargetNotOutOfVisible);\n}\n\nalignElement.__getOffsetParent = getOffsetParent;\n\nalignElement.__getVisibleRectForElement = getVisibleRectForElement;\n\nexport default alignElement;\n", "import utils from '../utils';\nimport doAlign from './align';\n\n/**\n * `tgtPoint`: { pageX, pageY } or { clientX, clientY }.\n * If client position provided, will internal convert to page position.\n */\n\nfunction alignPoint(el, tgtPoint, align) {\n  let pageX;\n  let pageY;\n\n  const doc = utils.getDocument(el);\n  const win = doc.defaultView || doc.parentWindow;\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n\n  if ('pageX' in tgtPoint) {\n    pageX = tgtPoint.pageX;\n  } else {\n    pageX = scrollX + tgtPoint.clientX;\n  }\n\n  if ('pageY' in tgtPoint) {\n    pageY = tgtPoint.pageY;\n  } else {\n    pageY = scrollY + tgtPoint.clientY;\n  }\n\n  const tgtRegion = {\n    left: pageX,\n    top: pageY,\n    width: 0,\n    height: 0,\n  };\n\n  const pointInView =\n    pageX >= 0 &&\n    pageX <= scrollX + viewportWidth &&\n    (pageY >= 0 && pageY <= scrollY + viewportHeight);\n\n  // Provide default target point\n  const points = [align.points[0], 'cc'];\n\n  return doAlign(el, tgtRegion, { ...align, points }, pointInView);\n}\n\nexport default alignPoint;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,YAAJ;AAEA,IAAMC,QAAQ,GAAG;EACfC,MAAM,EAAE,UADO;EAEfC,GAAG,EAAE,OAFU;EAGf;EACAC,EAAE,EAAE,MAJW;EAKfC,CAAC,EAAE;AALY,CAAjB;AAQA,SAASC,eAATA,CAAA,EAA2B;EACzB,IAAIN,YAAY,KAAKO,SAArB,EAAgC;IAC9B,OAAOP,YAAP;EACD;EACDA,YAAY,GAAG,EAAf;EACA,IAAMQ,KAAK,GAAGC,QAAQ,CAACC,aAAT,CAAuB,GAAvB,EAA4BF,KAA1C;EACA,IAAMG,QAAQ,GAAG,WAAjB;EACA,KAAK,IAAMC,GAAX,IAAkBX,QAAlB,EAA4B;IAC1B,IAAIW,GAAG,GAAGD,QAAN,IAAkBH,KAAtB,EAA6B;MAC3BR,YAAY,GAAGY,GAAf;IACD;EACF;EACD,OAAOZ,YAAP;AACD;AAED,SAASa,iBAATA,CAAA,EAA6B;EAC3B,OAAOP,eAAe,QAAAQ,MAAA,CACfR,eAAe,EADA,0BAElB,oBAFJ;AAGD;AAEM,SAASS,gBAATA,CAAA,EAA4B;EACjC,OAAOT,eAAe,QAAAQ,MAAA,CAAQR,eAAe,EAAvB,iBAAuC,WAA7D;AACD;AAEM,SAASU,qBAATA,CAA+BC,IAA/B,EAAqCC,KAArC,EAA4C;EACjD,IAAMC,IAAI,GAAGN,iBAAiB,EAA9B;EACA,IAAIM,IAAJ,EAAU;IACRF,IAAI,CAACT,KAAL,CAAWW,IAAX,IAAmBD,KAAnB;IACA,IAAIC,IAAI,KAAK,oBAAb,EAAmC;MACjCF,IAAI,CAACT,KAAL,CAAWY,kBAAX,GAAgCF,KAAhC;IACD;EACF;AACF;AAED,SAASG,YAATA,CAAsBJ,IAAtB,EAA4BC,KAA5B,EAAmC;EACjC,IAAMC,IAAI,GAAGJ,gBAAgB,EAA7B;EACA,IAAII,IAAJ,EAAU;IACRF,IAAI,CAACT,KAAL,CAAWW,IAAX,IAAmBD,KAAnB;IACA,IAAIC,IAAI,KAAK,WAAb,EAA0B;MACxBF,IAAI,CAACT,KAAL,CAAWc,SAAX,GAAuBJ,KAAvB;IACD;EACF;AACF;AAEM,SAASK,qBAATA,CAA+BN,IAA/B,EAAqC;EAC1C,OAAOA,IAAI,CAACT,KAAL,CAAWY,kBAAX,IAAiCH,IAAI,CAACT,KAAL,CAAWK,iBAAiB,EAA5B,CAAxC;AACD;AAEM,SAASW,cAATA,CAAwBP,IAAxB,EAA8B;EACnC,IAAMT,KAAK,GAAGiB,MAAM,CAACC,gBAAP,CAAwBT,IAAxB,EAA8B,IAA9B,CAAd;EACA,IAAMK,SAAS,GACbd,KAAK,CAACmB,gBAAN,CAAuB,WAAvB,KACAnB,KAAK,CAACmB,gBAAN,CAAuBZ,gBAAgB,EAAvC,CAFF;EAGA,IAAIO,SAAS,IAAIA,SAAS,KAAK,MAA/B,EAAuC;IACrC,IAAMM,MAAM,GAAGN,SAAS,CAACO,OAAV,CAAkB,aAAlB,EAAiC,EAAjC,EAAqCC,KAArC,CAA2C,GAA3C,CAAf;IACA,OAAO;MACLC,CAAC,EAAEC,UAAU,CAACJ,MAAM,CAAC,EAAD,CAAN,IAAcA,MAAM,CAAC,CAAD,CAArB,EAA0B,CAA1B,CADR;MAELK,CAAC,EAAED,UAAU,CAACJ,MAAM,CAAC,EAAD,CAAN,IAAcA,MAAM,CAAC,CAAD,CAArB,EAA0B,CAA1B;IAFR,CAAP;EAID;EACD,OAAO;IACLG,CAAC,EAAE,CADE;IAELE,CAAC,EAAE;EAFE,CAAP;AAID;AAED,IAAMC,QAAQ,GAAG,gBAAjB;AACA,IAAMC,QAAQ,GAAG,kBAAjB;AAEO,SAASC,cAATA,CAAwBnB,IAAxB,EAA8BoB,EAA9B,EAAkC;EACvC,IAAM7B,KAAK,GAAGiB,MAAM,CAACC,gBAAP,CAAwBT,IAAxB,EAA8B,IAA9B,CAAd;EACA,IAAMK,SAAS,GACbd,KAAK,CAACmB,gBAAN,CAAuB,WAAvB,KACAnB,KAAK,CAACmB,gBAAN,CAAuBZ,gBAAgB,EAAvC,CAFF;EAGA,IAAIO,SAAS,IAAIA,SAAS,KAAK,MAA/B,EAAuC;IACrC,IAAIgB,GAAJ;IACA,IAAIC,OAAO,GAAGjB,SAAS,CAACkB,KAAV,CAAgBN,QAAhB,CAAd;IACA,IAAIK,OAAJ,EAAa;MACXA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;MACAD,GAAG,GAAGC,OAAO,CAACT,KAAR,CAAc,GAAd,EAAmBW,GAAnB,CAAuB,UAAAC,IAAI,EAAI;QACnC,OAAOV,UAAU,CAACU,IAAD,EAAO,EAAP,CAAjB;MACD,CAFK,CAAN;MAGAJ,GAAG,CAAC,CAAD,CAAH,GAASD,EAAE,CAACN,CAAZ;MACAO,GAAG,CAAC,CAAD,CAAH,GAASD,EAAE,CAACJ,CAAZ;MACAZ,YAAY,CAACJ,IAAD,YAAAH,MAAA,CAAiBwB,GAAG,CAACK,IAAJ,CAAS,GAAT,CAAjB,OAAZ;IACD,CARD,MAQO;MACL,IAAMC,OAAO,GAAGtB,SAAS,CAACkB,KAAV,CAAgBL,QAAhB,EAA0B,CAA1B,CAAhB;MACAG,GAAG,GAAGM,OAAO,CAACd,KAAR,CAAc,GAAd,EAAmBW,GAAnB,CAAuB,UAAAC,IAAI,EAAI;QACnC,OAAOV,UAAU,CAACU,IAAD,EAAO,EAAP,CAAjB;MACD,CAFK,CAAN;MAGAJ,GAAG,CAAC,EAAD,CAAH,GAAUD,EAAE,CAACN,CAAb;MACAO,GAAG,CAAC,EAAD,CAAH,GAAUD,EAAE,CAACJ,CAAb;MACAZ,YAAY,CAACJ,IAAD,cAAAH,MAAA,CAAmBwB,GAAG,CAACK,IAAJ,CAAS,GAAT,CAAnB,OAAZ;IACD;EACF,CApBD,MAoBO;IACLtB,YAAY,CACVJ,IADU,gBAAAH,MAAA,CAEIuB,EAAE,CAACN,CAFP,qBAAAjB,MAAA,CAE0BuB,EAAE,CAACJ,CAF7B,uBAAZ;EAID;AACF;ACvGD,IAAMY,MAAM,GAAG,wCAAwCC,MAAvD;AAEA,IAAIC,iBAAJ;;AAGA,SAASC,aAATA,CAAuBC,IAAvB,EAA6B;EAC3B,IAAMC,aAAa,GAAGD,IAAI,CAACzC,KAAL,CAAW2C,OAAjC;EACAF,IAAI,CAACzC,KAAL,CAAW2C,OAAX,GAAqB,MAArB;EACAF,IAAI,CAACG,YAAL,CAH2B;;EAI3BH,IAAI,CAACzC,KAAL,CAAW2C,OAAX,GAAqBD,aAArB;AACD;AAED,SAASG,GAATA,CAAaC,EAAb,EAAiBnC,IAAjB,EAAuBoC,CAAvB,EAA0B;EACxB,IAAIrC,KAAK,GAAGqC,CAAZ;EACA,IAAIC,OAAA,CAAOrC,IAAP,MAAgB,QAApB,EAA8B;IAC5B,KAAK,IAAMsC,CAAX,IAAgBtC,IAAhB,EAAsB;MACpB,IAAIA,IAAI,CAACuC,cAAL,CAAoBD,CAApB,CAAJ,EAA4B;QAC1BJ,GAAG,CAACC,EAAD,EAAKG,CAAL,EAAQtC,IAAI,CAACsC,CAAD,CAAZ,CAAH;MACD;IACF;IACD,OAAOlD,SAAP;EACD;EACD,IAAI,OAAOW,KAAP,KAAiB,WAArB,EAAkC;IAChC,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;MAC7BA,KAAK,MAAAJ,MAAA,CAAMI,KAAN,OAAL;IACD;IACDoC,EAAE,CAAC9C,KAAH,CAASW,IAAT,IAAiBD,KAAjB;IACA,OAAOX,SAAP;EACD;EACD,OAAOwC,iBAAiB,CAACO,EAAD,EAAKnC,IAAL,CAAxB;AACD;AAED,SAASwC,iBAATA,CAA2BV,IAA3B,EAAiC;EAC/B,IAAIW,GAAJ;EACA,IAAI7B,CAAJ;EACA,IAAIE,CAAJ;EACA,IAAM4B,GAAG,GAAGZ,IAAI,CAACa,aAAjB;EACA,IAAMC,IAAI,GAAGF,GAAG,CAACE,IAAjB;EACA,IAAMC,OAAO,GAAGH,GAAG,IAAIA,GAAG,CAACI,eAA3B,CAN+B;;EAQ/BL,GAAG,GAAGX,IAAI,CAACiB,qBAAL,EAAN,CAR+B;EAW/B;EACA;;EAEAnC,CAAC,GAAGoC,IAAI,CAACC,KAAL,CAAWR,GAAG,CAACS,IAAf,CAAJ;EACApC,CAAC,GAAGkC,IAAI,CAACC,KAAL,CAAWR,GAAG,CAACU,GAAf,CAAJ,CAf+B;EAkB/B;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAvC,CAAC,IAAIiC,OAAO,CAACO,UAAR,IAAsBR,IAAI,CAACQ,UAA3B,IAAyC,CAA9C;EACAtC,CAAC,IAAI+B,OAAO,CAACQ,SAAR,IAAqBT,IAAI,CAACS,SAA1B,IAAuC,CAA5C;EAEA,OAAO;IACLH,IAAI,EAAEtC,CADD;IAELuC,GAAG,EAAErC;EAFA,CAAP;AAID;AAED,SAASwC,SAATA,CAAmBC,CAAnB,EAAsBJ,GAAtB,EAA2B;EACzB,IAAIK,GAAG,GAAGD,CAAC,QAAA5D,MAAA,CAAQwD,GAAG,GAAG,GAAH,GAAS,GAApB,YAAX;EACA,IAAMM,MAAM,YAAA9D,MAAA,CAAYwD,GAAG,GAAG,KAAH,GAAW,MAA1B,CAAZ;EACA,IAAI,OAAOK,GAAP,KAAe,QAAnB,EAA6B;IAC3B,IAAME,CAAC,GAAGH,CAAC,CAACjE,QAAZ,CAD2B;;IAG3BkE,GAAG,GAAGE,CAAC,CAACZ,eAAF,CAAkBW,MAAlB,CAAN;IACA,IAAI,OAAOD,GAAP,KAAe,QAAnB,EAA6B;MAC3B;MACAA,GAAG,GAAGE,CAAC,CAACd,IAAF,CAAOa,MAAP,CAAN;IACD;EACF;EACD,OAAOD,GAAP;AACD;AAED,SAASG,aAATA,CAAuBJ,CAAvB,EAA0B;EACxB,OAAOD,SAAS,CAACC,CAAD,CAAhB;AACD;AAED,SAASK,YAATA,CAAsBL,CAAtB,EAAyB;EACvB,OAAOD,SAAS,CAACC,CAAD,EAAI,IAAJ,CAAhB;AACD;AAED,SAASM,SAATA,CAAmB1B,EAAnB,EAAuB;EACrB,IAAM2B,GAAG,GAAGtB,iBAAiB,CAACL,EAAD,CAA7B;EACA,IAAMO,GAAG,GAAGP,EAAE,CAACQ,aAAf;EACA,IAAMY,CAAC,GAAGb,GAAG,CAACqB,WAAJ,IAAmBrB,GAAG,CAACsB,YAAjC;EACAF,GAAG,CAACZ,IAAJ,IAAYS,aAAa,CAACJ,CAAD,CAAzB;EACAO,GAAG,CAACX,GAAJ,IAAWS,YAAY,CAACL,CAAD,CAAvB;EACA,OAAOO,GAAP;AACD;AAED;AACA;AACA;AACA;;AACA,SAASG,QAATA,CAAkBC,GAAlB,EAAuB;EACrB;;EACA;EACA,OAAOA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAK9E,SAAxB,IAAqC8E,GAAG,IAAIA,GAAG,CAAC5D,MAAvD;AACD;AAED,SAAS6D,WAATA,CAAqBrE,IAArB,EAA2B;EACzB,IAAImE,QAAQ,CAACnE,IAAD,CAAZ,EAAoB;IAClB,OAAOA,IAAI,CAACR,QAAZ;EACD;EACD,IAAIQ,IAAI,CAACsE,QAAL,KAAkB,CAAtB,EAAyB;IACvB,OAAOtE,IAAP;EACD;EACD,OAAOA,IAAI,CAAC6C,aAAZ;AACD;AAED,SAAS0B,iBAATA,CAA2BvC,IAA3B,EAAiC9B,IAAjC,EAAuCsE,EAAvC,EAA2C;EACzC,IAAIC,aAAa,GAAGD,EAApB;EACA,IAAIE,GAAG,GAAG,EAAV;EACA,IAAMd,CAAC,GAAGS,WAAW,CAACrC,IAAD,CAArB;EACAyC,aAAa,GAAGA,aAAa,IAAIb,CAAC,CAACK,WAAF,CAAcxD,gBAAd,CAA+BuB,IAA/B,EAAqC,IAArC,CAAjC,CAJyC;;EAOzC,IAAIyC,aAAJ,EAAmB;IACjBC,GAAG,GAAGD,aAAa,CAAC/D,gBAAd,CAA+BR,IAA/B,KAAwCuE,aAAa,CAACvE,IAAD,CAA3D;EACD;EAED,OAAOwE,GAAP;AACD;AAED,IAAMC,aAAa,GAAG,IAAIC,MAAJ,MAAA/E,MAAA,CAAgB+B,MAAhB,sBAAyC,GAAzC,CAAtB;AACA,IAAMiD,MAAM,GAAG,2BAAf;AACA,IAAMC,aAAa,GAAG,cAAtB;AACA,IAAMC,aAAa,GAAG,cAAtB;AACA,IAAMC,IAAI,GAAG,MAAb;AACA,IAAMC,EAAE,GAAG,IAAX;AAEA,SAASC,mBAATA,CAA6BlD,IAA7B,EAAmC9B,IAAnC,EAAyC;EACvC;EACA;EACA,IAAIwD,GAAG,GAAG1B,IAAI,CAAC8C,aAAD,CAAJ,IAAuB9C,IAAI,CAAC8C,aAAD,CAAJ,CAAoB5E,IAApB,CAAjC,CAHuC;EAMvC;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;;EACA,IAAIyE,aAAa,CAACQ,IAAd,CAAmBzB,GAAnB,KAA2B,CAACmB,MAAM,CAACM,IAAP,CAAYjF,IAAZ,CAAhC,EAAmD;IACjD;IACA,IAAMX,KAAK,GAAGyC,IAAI,CAACzC,KAAnB;IACA,IAAM6D,IAAI,GAAG7D,KAAK,CAACyF,IAAD,CAAlB;IACA,IAAMI,MAAM,GAAGpD,IAAI,CAAC+C,aAAD,CAAJ,CAAoBC,IAApB,CAAf,CAJiD;;IAOjDhD,IAAI,CAAC+C,aAAD,CAAJ,CAAoBC,IAApB,IAA4BhD,IAAI,CAAC8C,aAAD,CAAJ,CAAoBE,IAApB,CAA5B,CAPiD;;IAUjDzF,KAAK,CAACyF,IAAD,CAAL,GAAc9E,IAAI,KAAK,UAAT,GAAsB,KAAtB,GAA8BwD,GAAG,IAAI,CAAnD;IACAA,GAAG,GAAGnE,KAAK,CAAC8F,SAAN,GAAkBJ,EAAxB,CAXiD;;IAcjD1F,KAAK,CAACyF,IAAD,CAAL,GAAc5B,IAAd;IAEApB,IAAI,CAAC+C,aAAD,CAAJ,CAAoBC,IAApB,IAA4BI,MAA5B;EACD;EACD,OAAO1B,GAAG,KAAK,EAAR,GAAa,MAAb,GAAsBA,GAA7B;AACD;AAED,IAAI,OAAOlD,MAAP,KAAkB,WAAtB,EAAmC;EACjCsB,iBAAiB,GAAGtB,MAAM,CAACC,gBAAP,GAChB8D,iBADgB,GAEhBW,mBAFJ;AAGD;AAED,SAASI,kBAATA,CAA4BC,GAA5B,EAAiCC,MAAjC,EAAyC;EACvC,IAAID,GAAG,KAAK,MAAZ,EAAoB;IAClB,OAAOC,MAAM,CAACC,WAAP,GAAqB,OAArB,GAA+BF,GAAtC;EACD;EACD,OAAOC,MAAM,CAACE,YAAP,GAAsB,QAAtB,GAAiCH,GAAxC;AACD;AAED,SAASI,uBAATA,CAAiCJ,GAAjC,EAAsC;EACpC,IAAIA,GAAG,KAAK,MAAZ,EAAoB;IAClB,OAAO,OAAP;EACD,CAFD,MAEO,IAAIA,GAAG,KAAK,OAAZ,EAAqB;IAC1B,OAAO,MAAP;EACD,CAFM,MAEA,IAAIA,GAAG,KAAK,KAAZ,EAAmB;IACxB,OAAO,QAAP;EACD,CAFM,MAEA,IAAIA,GAAG,KAAK,QAAZ,EAAsB;IAC3B,OAAO,KAAP;EACD;AACF;;AAGD,SAASK,UAATA,CAAoB5D,IAApB,EAA0B6D,MAA1B,EAAkCL,MAAlC,EAA0C;EACxC;EACA,IAAIpD,GAAG,CAACJ,IAAD,EAAO,UAAP,CAAH,KAA0B,QAA9B,EAAwC;IACtCA,IAAI,CAACzC,KAAL,CAAWuG,QAAX,GAAsB,UAAtB;EACD;EACD,IAAIC,OAAO,GAAG,CAAC,GAAf;EACA,IAAIC,OAAO,GAAG,CAAC,GAAf;EACA,IAAMC,kBAAkB,GAAGX,kBAAkB,CAAC,MAAD,EAASE,MAAT,CAA7C;EACA,IAAMU,gBAAgB,GAAGZ,kBAAkB,CAAC,KAAD,EAAQE,MAAR,CAA3C;EACA,IAAMW,0BAA0B,GAAGR,uBAAuB,CACxDM,kBADwD,CAA1D;EAGA,IAAMG,wBAAwB,GAAGT,uBAAuB,CAACO,gBAAD,CAAxD;EAEA,IAAID,kBAAkB,KAAK,MAA3B,EAAmC;IACjCF,OAAO,GAAG,GAAV;EACD;EAED,IAAIG,gBAAgB,KAAK,KAAzB,EAAgC;IAC9BF,OAAO,GAAG,GAAV;EACD;EACD,IAAIK,kBAAkB,GAAG,EAAzB;EACA,IAAMC,cAAc,GAAGvC,SAAS,CAAC/B,IAAD,CAAhC;EACA,IAAI,UAAU6D,MAAV,IAAoB,SAASA,MAAjC,EAAyC;IACvCQ,kBAAkB,GAAG/F,qBAAqB,CAAC0B,IAAD,CAArB,IAA+B,EAApD;IACAjC,qBAAqB,CAACiC,IAAD,EAAO,MAAP,CAArB;EACD;EACD,IAAI,UAAU6D,MAAd,EAAsB;IACpB7D,IAAI,CAACzC,KAAL,CAAW4G,0BAAX,IAAyC,EAAzC;IACAnE,IAAI,CAACzC,KAAL,CAAW0G,kBAAX,OAAApG,MAAA,CAAoCkG,OAApC;EACD;EACD,IAAI,SAASF,MAAb,EAAqB;IACnB7D,IAAI,CAACzC,KAAL,CAAW6G,wBAAX,IAAuC,EAAvC;IACApE,IAAI,CAACzC,KAAL,CAAW2G,gBAAX,OAAArG,MAAA,CAAkCmG,OAAlC;EACD,CAlCuC;;EAoCxCjE,aAAa,CAACC,IAAD,CAAb;EACA,IAAMuE,GAAG,GAAGxC,SAAS,CAAC/B,IAAD,CAArB;EACA,IAAMC,aAAa,GAAG,EAAtB;EACA,KAAK,IAAMtC,GAAX,IAAkBkG,MAAlB,EAA0B;IACxB,IAAIA,MAAM,CAACpD,cAAP,CAAsB9C,GAAtB,CAAJ,EAAgC;MAC9B,IAAM4F,GAAG,GAAGD,kBAAkB,CAAC3F,GAAD,EAAM6F,MAAN,CAA9B;MACA,IAAMgB,MAAM,GAAG7G,GAAG,KAAK,MAAR,GAAiBoG,OAAjB,GAA2BC,OAA1C;MACA,IAAMS,GAAG,GAAGH,cAAc,CAAC3G,GAAD,CAAd,GAAsB4G,GAAG,CAAC5G,GAAD,CAArC;MACA,IAAI4F,GAAG,KAAK5F,GAAZ,EAAiB;QACfsC,aAAa,CAACsD,GAAD,CAAb,GAAqBiB,MAAM,GAAGC,GAA9B;MACD,CAFD,MAEO;QACLxE,aAAa,CAACsD,GAAD,CAAb,GAAqBiB,MAAM,GAAGC,GAA9B;MACD;IACF;EACF;EACDrE,GAAG,CAACJ,IAAD,EAAOC,aAAP,CAAH,CAnDwC;;EAqDxCF,aAAa,CAACC,IAAD,CAAb;EACA,IAAI,UAAU6D,MAAV,IAAoB,SAASA,MAAjC,EAAyC;IACvC9F,qBAAqB,CAACiC,IAAD,EAAOqE,kBAAP,CAArB;EACD;EACD,IAAM3C,GAAG,GAAG,EAAZ;EACA,KAAK,IAAMgD,IAAX,IAAkBb,MAAlB,EAA0B;IACxB,IAAIA,MAAM,CAACpD,cAAP,CAAsBiE,IAAtB,CAAJ,EAAgC;MAC9B,IAAMC,IAAG,GAAGrB,kBAAkB,CAACoB,IAAD,EAAMlB,MAAN,CAA9B;MACA,IAAMoB,IAAG,GAAGf,MAAM,CAACa,IAAD,CAAN,GAAcJ,cAAc,CAACI,IAAD,CAAxC;MACA,IAAIA,IAAG,KAAKC,IAAZ,EAAiB;QACfjD,GAAG,CAACiD,IAAD,CAAH,GAAW1E,aAAa,CAAC0E,IAAD,CAAb,GAAqBC,IAAhC;MACD,CAFD,MAEO;QACLlD,GAAG,CAACiD,IAAD,CAAH,GAAW1E,aAAa,CAAC0E,IAAD,CAAb,GAAqBC,IAAhC;MACD;IACF;EACF;EACDxE,GAAG,CAACJ,IAAD,EAAO0B,GAAP,CAAH;AACD;AAED,SAASmD,cAATzG,CAAsB4B,IAAtB,EAA4B6D,MAA5B,EAAoC;EAClC,IAAMS,cAAc,GAAGvC,SAAS,CAAC/B,IAAD,CAAhC;EACA,IAAM8E,UAAU,GAAGvG,cAAc,CAACyB,IAAD,CAAjC;EACA,IAAM+E,QAAQ,GAAG;IAAEjG,CAAC,EAAEgG,UAAU,CAAChG,CAAhB;IAAmBE,CAAC,EAAE8F,UAAU,CAAC9F;EAAjC,CAAjB;EACA,IAAI,UAAU6E,MAAd,EAAsB;IACpBkB,QAAQ,CAACjG,CAAT,GAAagG,UAAU,CAAChG,CAAX,GAAe+E,MAAM,CAACzC,IAAtB,GAA6BkD,cAAc,CAAClD,IAAzD;EACD;EACD,IAAI,SAASyC,MAAb,EAAqB;IACnBkB,QAAQ,CAAC/F,CAAT,GAAa8F,UAAU,CAAC9F,CAAX,GAAe6E,MAAM,CAACxC,GAAtB,GAA4BiD,cAAc,CAACjD,GAAxD;EACD;EACDlC,cAAc,CAACa,IAAD,EAAO+E,QAAP,CAAd;AACD;AAED,SAASC,SAATA,CAAmBhF,IAAnB,EAAyB6D,MAAzB,EAAiCL,MAAjC,EAAyC;EACvC,IAAIA,MAAM,CAACyB,WAAX,EAAwB;IACtB,IAAMC,SAAS,GAAGnD,SAAS,CAAC/B,IAAD,CAA3B;IAEA,IAAMmF,KAAK,GAAGD,SAAS,CAAC9D,IAAV,CAAegE,OAAf,CAAuB,CAAvB,CAAd;IACA,IAAMC,IAAI,GAAGH,SAAS,CAAC7D,GAAV,CAAc+D,OAAd,CAAsB,CAAtB,CAAb;IACA,IAAME,KAAK,GAAGzB,MAAM,CAACzC,IAAP,CAAYgE,OAAZ,CAAoB,CAApB,CAAd;IACA,IAAMG,IAAI,GAAG1B,MAAM,CAACxC,GAAP,CAAW+D,OAAX,CAAmB,CAAnB,CAAb;IAEA,IAAID,KAAK,KAAKG,KAAV,IAAmBD,IAAI,KAAKE,IAAhC,EAAsC;MACpC;IACD;EACF;EAED,IAAI/B,MAAM,CAACC,WAAP,IAAsBD,MAAM,CAACE,YAAjC,EAA+C;IAC7CE,UAAU,CAAC5D,IAAD,EAAO6D,MAAP,EAAeL,MAAf,CAAV;EACD,CAFD,MAEO,IACLA,MAAM,CAACgC,eAAP,IACA1H,gBAAgB,MAAMN,QAAQ,CAACsD,IAAT,CAAcvD,KAF/B,EAGL;IACAsH,cAAY,CAAC7E,IAAD,EAAO6D,MAAP,CAAZ;EACD,CALM,MAKA;IACLD,UAAU,CAAC5D,IAAD,EAAO6D,MAAP,EAAeL,MAAf,CAAV;EACD;AACF;AAED,SAASiC,IAATA,CAAcpG,GAAd,EAAmBqG,EAAnB,EAAuB;EACrB,KAAK,IAAIlF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnB,GAAG,CAACsG,MAAxB,EAAgCnF,CAAC,EAAjC,EAAqC;IACnCkF,EAAE,CAACrG,GAAG,CAACmB,CAAD,CAAJ,CAAF;EACD;AACF;AAED,SAASoF,aAATA,CAAuB5F,IAAvB,EAA6B;EAC3B,OAAOF,iBAAiB,CAACE,IAAD,EAAO,WAAP,CAAjB,KAAyC,YAAhD;AACD;AAED,IAAM6F,UAAU,GAAG,CAAC,QAAD,EAAW,QAAX,EAAqB,SAArB,CAAnB;AACA,IAAMC,aAAa,GAAG,CAAC,CAAvB;AACA,IAAMC,aAAa,GAAG,CAAtB;AACA,IAAMC,YAAY,GAAG,CAArB;AACA,IAAMC,YAAY,GAAG,CAArB;AAEA,SAASC,IAATA,CAAclG,IAAd,EAAoBmG,OAApB,EAA6BC,QAA7B,EAAuC;EACrC,IAAM7B,GAAG,GAAG,EAAZ;EACA,IAAMhH,KAAK,GAAGyC,IAAI,CAACzC,KAAnB;EACA,IAAIW,IAAJ,CAHqC;;EAMrC,KAAKA,IAAL,IAAaiI,OAAb,EAAsB;IACpB,IAAIA,OAAO,CAAC1F,cAAR,CAAuBvC,IAAvB,CAAJ,EAAkC;MAChCqG,GAAG,CAACrG,IAAD,CAAH,GAAYX,KAAK,CAACW,IAAD,CAAjB;MACAX,KAAK,CAACW,IAAD,CAAL,GAAciI,OAAO,CAACjI,IAAD,CAArB;IACD;EACF;EAEDkI,QAAQ,CAACC,IAAT,CAAcrG,IAAd,EAbqC;;EAgBrC,KAAK9B,IAAL,IAAaiI,OAAb,EAAsB;IACpB,IAAIA,OAAO,CAAC1F,cAAR,CAAuBvC,IAAvB,CAAJ,EAAkC;MAChCX,KAAK,CAACW,IAAD,CAAL,GAAcqG,GAAG,CAACrG,IAAD,CAAjB;IACD;EACF;AACF;AAED,SAASoI,WAATA,CAAqBtG,IAArB,EAA2BuG,KAA3B,EAAkCC,KAAlC,EAAyC;EACvC,IAAIvI,KAAK,GAAG,CAAZ;EACA,IAAIwI,IAAJ;EACA,IAAIC,CAAJ;EACA,IAAIlG,CAAJ;EACA,KAAKkG,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGH,KAAK,CAACZ,MAAtB,EAA8Be,CAAC,EAA/B,EAAmC;IACjCD,IAAI,GAAGF,KAAK,CAACG,CAAD,CAAZ;IACA,IAAID,IAAJ,EAAU;MACR,KAAKjG,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGgG,KAAK,CAACb,MAAtB,EAA8BnF,CAAC,EAA/B,EAAmC;QACjC,IAAImG,OAAO,SAAX;QACA,IAAIF,IAAI,KAAK,QAAb,EAAuB;UACrBE,OAAO,MAAA9I,MAAA,CAAM4I,IAAN,EAAA5I,MAAA,CAAa2I,KAAK,CAAChG,CAAD,CAAlB,UAAP;QACD,CAFD,MAEO;UACLmG,OAAO,GAAGF,IAAI,GAAGD,KAAK,CAAChG,CAAD,CAAtB;QACD;QACDvC,KAAK,IAAIc,UAAU,CAACe,iBAAiB,CAACE,IAAD,EAAO2G,OAAP,CAAlB,CAAV,IAAgD,CAAzD;MACD;IACF;EACF;EACD,OAAO1I,KAAP;AACD;AAED,IAAM2I,QAAQ,GAAG;EACfC,SADe,WAAAA,UACLC,OADK,EACI;IACjB,IAAIC,MAAM,GAAGD,OAAb;IACA,GAAG;MACD,IAAIC,MAAM,CAACzE,QAAP,KAAoB,EAApB,IAA0ByE,MAAM,CAACC,IAArC,EAA2C;QACzCD,MAAM,GAAGA,MAAM,CAACC,IAAhB;MACD,CAFD,MAEO;QACLD,MAAM,GAAGA,MAAM,CAACE,UAAhB;MACD;IACF,CAND,QAMSF,MAAM,IAAIA,MAAM,CAACzE,QAAP,KAAoB,CAA9B,IAAmCyE,MAAM,CAACzE,QAAP,KAAoB,CANhE;IAOA,OAAOyE,MAAP;EACD;AAXc,CAAjB;AAcAtB,IAAI,CAAC,CAAC,OAAD,EAAU,QAAV,CAAD,EAAsB,UAAAvH,IAAI,EAAI;EAChC0I,QAAQ,OAAA/I,MAAA,CAAOK,IAAP,EAAR,GAAyB,UAAAgJ,MAAM,EAAI;IACjC,IAAMtF,CAAC,GAAGsF,MAAM,CAAC1J,QAAjB;IACA,OAAO0D,IAAI,CAACiG,GAAL;IAAA;IAEL;IACAvF,CAAC,CAACZ,eAAF,UAAAnD,MAAA,CAA2BK,IAA3B,EAHK;IAAA;IAKL0D,CAAC,CAACd,IAAF,UAAAjD,MAAA,CAAgBK,IAAhB,EALK,EAML0I,QAAQ,YAAA/I,MAAA,CAAYK,IAAZ,EAAR,CAA4B0D,CAA5B,CANK,CAAP;EAQD,CAVD;EAYAgF,QAAQ,YAAA/I,MAAA,CAAYK,IAAZ,EAAR,GAA8B,UAAAkJ,GAAG,EAAI;IACnC;IACA,IAAMX,IAAI,YAAA5I,MAAA,CAAYK,IAAZ,CAAV;IACA,IAAM0C,GAAG,GAAGwG,GAAG,CAAC5J,QAAhB;IACA,IAAMsD,IAAI,GAAGF,GAAG,CAACE,IAAjB;IACA,IAAME,eAAe,GAAGJ,GAAG,CAACI,eAA5B;IACA,IAAMqG,mBAAmB,GAAGrG,eAAe,CAACyF,IAAD,CAA3C,CANmC;IAQnC;;IACA,OACG7F,GAAG,CAAC0G,UAAJ,KAAmB,YAAnB,IAAmCD,mBAApC,IACCvG,IAAI,IAAIA,IAAI,CAAC2F,IAAD,CADb,IAEAY,mBAHF;EAKD,CAdD;AAeD,CA5BG,CAAJ;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASE,KAATA,CAAevH,IAAf,EAAqB9B,IAArB,EAA2BsJ,EAA3B,EAA+B;EAC7B,IAAIC,KAAK,GAAGD,EAAZ;EACA,IAAIrF,QAAQ,CAACnC,IAAD,CAAZ,EAAoB;IAClB,OAAO9B,IAAI,KAAK,OAAT,GACH0I,QAAQ,CAACc,aAAT,CAAuB1H,IAAvB,CADG,GAEH4G,QAAQ,CAACe,cAAT,CAAwB3H,IAAxB,CAFJ;EAGD,CAJD,MAIO,IAAIA,IAAI,CAACsC,QAAL,KAAkB,CAAtB,EAAyB;IAC9B,OAAOpE,IAAI,KAAK,OAAT,GACH0I,QAAQ,CAACgB,QAAT,CAAkB5H,IAAlB,CADG,GAEH4G,QAAQ,CAACiB,SAAT,CAAmB7H,IAAnB,CAFJ;EAGD;EACD,IAAMwG,KAAK,GAAGtI,IAAI,KAAK,OAAT,GAAmB,CAAC,MAAD,EAAS,OAAT,CAAnB,GAAuC,CAAC,KAAD,EAAQ,QAAR,CAArD;EACA,IAAI4J,cAAc,GAChB5J,IAAI,KAAK,OAAT,GACIgD,IAAI,CAACC,KAAL,CAAWnB,IAAI,CAACiB,qBAAL,GAA6B8G,KAAxC,CADJ,GAEI7G,IAAI,CAACC,KAAL,CAAWnB,IAAI,CAACiB,qBAAL,GAA6B+G,MAAxC,CAHN;EAIA,IAAMC,WAAW,GAAGrC,aAAa,CAAC5F,IAAD,CAAjC;EACA,IAAIkI,WAAW,GAAG,CAAlB;EACA,IACEJ,cAAc,KAAK,IAAnB,IACAA,cAAc,KAAKxK,SADnB,IAEAwK,cAAc,IAAI,CAHpB,EAIE;IACAA,cAAc,GAAGxK,SAAjB,CADA;;IAGA4K,WAAW,GAAGpI,iBAAiB,CAACE,IAAD,EAAO9B,IAAP,CAA/B;IACA,IACEgK,WAAW,KAAK,IAAhB,IACAA,WAAW,KAAK5K,SADhB,IAEA6K,MAAM,CAACD,WAAD,CAAN,GAAsB,CAHxB,EAIE;MACAA,WAAW,GAAGlI,IAAI,CAACzC,KAAL,CAAWW,IAAX,KAAoB,CAAlC;IACD,CAVD;;IAYAgK,WAAW,GAAGnJ,UAAU,CAACmJ,WAAD,CAAV,IAA2B,CAAzC;EACD;EACD,IAAIT,KAAK,KAAKnK,SAAd,EAAyB;IACvBmK,KAAK,GAAGQ,WAAW,GAAGjC,YAAH,GAAkBF,aAArC;EACD;EACD,IAAMsC,2BAA2B,GAC/BN,cAAc,KAAKxK,SAAnB,IAAgC2K,WADlC;EAEA,IAAMvF,GAAG,GAAGoF,cAAc,IAAII,WAA9B;EACA,IAAIT,KAAK,KAAK3B,aAAd,EAA6B;IAC3B,IAAIsC,2BAAJ,EAAiC;MAC/B,OAAO1F,GAAG,GAAG4D,WAAW,CAACtG,IAAD,EAAO,CAAC,QAAD,EAAW,SAAX,CAAP,EAA8BwG,KAA9B,CAAxB;IACD;IACD,OAAO0B,WAAP;EACD,CALD,MAKO,IAAIE,2BAAJ,EAAiC;IACtC,IAAIX,KAAK,KAAKzB,YAAd,EAA4B;MAC1B,OAAOtD,GAAP;IACD;IACD,OACEA,GAAG,IACF+E,KAAK,KAAK1B,aAAV,GACG,CAACO,WAAW,CAACtG,IAAD,EAAO,CAAC,QAAD,CAAP,EAAmBwG,KAAnB,CADf,GAEGF,WAAW,CAACtG,IAAD,EAAO,CAAC,QAAD,CAAP,EAAmBwG,KAAnB,CAHZ,CADL;EAMD;EACD,OAAO0B,WAAW,GAAG5B,WAAW,CAACtG,IAAD,EAAO6F,UAAU,CAACwC,KAAX,CAAiBZ,KAAjB,CAAP,EAAgCjB,KAAhC,CAAhC;AACD;AAED,IAAM8B,OAAO,GAAG;EACdxE,QAAQ,EAAE,UADI;EAEdyE,UAAU,EAAE,QAFE;EAGdrI,OAAO,EAAE;AAHK,CAAhB;;AAOA,SAASsI,kBAATA,CAAA,EAAqC;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA/C,MAAA,EAANgD,IAAM,OAAAC,KAAA,CAAAH,IAAA,GAAAI,KAAA,MAAAA,KAAA,GAAAJ,IAAA,EAAAI,KAAA;IAANF,IAAM,CAAAE,KAAA,IAAAH,SAAA,CAAAG,KAAA;EAAA;EACnC,IAAInG,GAAJ;EACA,IAAM1C,IAAI,GAAG2I,IAAI,CAAC,CAAD,CAAjB,CAFmC;EAInC;;EACA,IAAI3I,IAAI,CAAC8I,WAAL,KAAqB,CAAzB,EAA4B;IAC1BpG,GAAG,GAAG6E,KAAK,CAACwB,KAAN,CAAYzL,SAAZ,EAAuBqL,IAAvB,CAAN;EACD,CAFD,MAEO;IACLzC,IAAI,CAAClG,IAAD,EAAOsI,OAAP,EAAgB,YAAM;MACxB5F,GAAG,GAAG6E,KAAK,CAACwB,KAAN,CAAYzL,SAAZ,EAAuBqL,IAAvB,CAAN;IACD,CAFG,CAAJ;EAGD;EACD,OAAOjG,GAAP;AACD;AAED+C,IAAI,CAAC,CAAC,OAAD,EAAU,QAAV,CAAD,EAAsB,UAAAvH,IAAI,EAAI;EAChC,IAAM8K,KAAK,GAAG9K,IAAI,CAAC+K,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+BhL,IAAI,CAACmK,KAAL,CAAW,CAAX,CAA7C;EACAzB,QAAQ,SAAA/I,MAAA,CAASmL,KAAT,EAAR,GAA4B,UAAC3I,EAAD,EAAK8I,aAAL,EAAuB;IACjD,OACE9I,EAAE,IACFmI,kBAAkB,CAACnI,EAAD,EAAKnC,IAAL,EAAWiL,aAAa,GAAGlD,YAAH,GAAkBD,YAA1C,CAFpB;EAID,CALD;EAMA,IAAMQ,KAAK,GAAGtI,IAAI,KAAK,OAAT,GAAmB,CAAC,MAAD,EAAS,OAAT,CAAnB,GAAuC,CAAC,KAAD,EAAQ,QAAR,CAArD;EAEA0I,QAAQ,CAAC1I,IAAD,CAAR,GAAiB,UAAC8B,IAAD,EAAOM,CAAP,EAAa;IAC5B,IAAIoC,GAAG,GAAGpC,CAAV;IACA,IAAIoC,GAAG,KAAKpF,SAAZ,EAAuB;MACrB,IAAI0C,IAAJ,EAAU;QACR,IAAMiI,WAAW,GAAGrC,aAAa,CAAC5F,IAAD,CAAjC;QACA,IAAIiI,WAAJ,EAAiB;UACfvF,GAAG,IAAI4D,WAAW,CAACtG,IAAD,EAAO,CAAC,SAAD,EAAY,QAAZ,CAAP,EAA8BwG,KAA9B,CAAlB;QACD;QACD,OAAOpG,GAAG,CAACJ,IAAD,EAAO9B,IAAP,EAAawE,GAAb,CAAV;MACD;MACD,OAAOpF,SAAP;IACD;IACD,OAAO0C,IAAI,IAAIwI,kBAAkB,CAACxI,IAAD,EAAO9B,IAAP,EAAa4H,aAAb,CAAjC;EACD,CAbD;AAcD,CAxBG,CAAJ;AA0BA,SAASsD,GAATA,CAAaC,EAAb,EAAiBC,IAAjB,EAAuB;EACrB,KAAK,IAAM9I,CAAX,IAAgB8I,IAAhB,EAAsB;IACpB,IAAIA,IAAI,CAAC7I,cAAL,CAAoBD,CAApB,CAAJ,EAA4B;MAC1B6I,EAAE,CAAC7I,CAAD,CAAF,GAAQ8I,IAAI,CAAC9I,CAAD,CAAZ;IACD;EACF;EACD,OAAO6I,EAAP;AACD;AAED,IAAME,KAAK,GAAG;EACZC,SADY,WAAAA,UACFxL,IADE,EACI;IACd,IAAIA,IAAI,IAAIA,IAAI,CAACR,QAAb,IAAyBQ,IAAI,CAACyL,UAAlC,EAA8C;MAC5C,OAAOzL,IAAP;IACD;IACD,IAAM4C,GAAG,GAAG5C,IAAI,CAAC6C,aAAL,IAAsB7C,IAAlC;IACA,OAAO4C,GAAG,CAACqB,WAAJ,IAAmBrB,GAAG,CAACsB,YAA9B;EACD,CAPW;EAQZG,WAAW,EAAXA,WARY;EASZwB,MATY,WAAAA,OASLxD,EATK,EASDpC,KATC,EASMuF,MATN,EASc;IACxB,IAAI,OAAOvF,KAAP,KAAiB,WAArB,EAAkC;MAChC+G,SAAS,CAAC3E,EAAD,EAAKpC,KAAL,EAAYuF,MAAM,IAAI,EAAtB,CAAT;IACD,CAFD,MAEO;MACL,OAAOzB,SAAS,CAAC1B,EAAD,CAAhB;IACD;EACF,CAfW;EAgBZ8B,QAAQ,EAARA,QAhBY;EAiBZsD,IAAI,EAAJA,IAjBY;EAkBZrF,GAAG,EAAHA,GAlBY;EAmBZsJ,KAnBY,WAAAA,MAmBNtH,GAnBM,EAmBD;IACT,IAAI5B,CAAJ;IACA,IAAMkB,GAAG,GAAG,EAAZ;IACA,KAAKlB,CAAL,IAAU4B,GAAV,EAAe;MACb,IAAIA,GAAG,CAAC3B,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;QACzBkB,GAAG,CAAClB,CAAD,CAAH,GAAS4B,GAAG,CAAC5B,CAAD,CAAZ;MACD;IACF;IACD,IAAMmJ,QAAQ,GAAGvH,GAAG,CAACuH,QAArB;IACA,IAAIA,QAAJ,EAAc;MACZ,KAAKnJ,CAAL,IAAU4B,GAAV,EAAe;QACb,IAAIA,GAAG,CAAC3B,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;UACzBkB,GAAG,CAACiI,QAAJ,CAAanJ,CAAb,IAAkB4B,GAAG,CAACuH,QAAJ,CAAanJ,CAAb,CAAlB;QACD;MACF;IACF;IACD,OAAOkB,GAAP;EACD,CApCW;EAqCZ0H,GAAG,EAAHA,GArCY;EAsCZQ,mBAtCY,WAAAA,oBAsCQnI,CAtCR,EAsCW;IACrB,OAAOI,aAAa,CAACJ,CAAD,CAApB;EACD,CAxCW;EAyCZoI,kBAzCY,WAAAA,mBAyCOpI,CAzCP,EAyCU;IACpB,OAAOK,YAAY,CAACL,CAAD,CAAnB;EACD,CA3CW;EA4CZqI,KA5CY,WAAAA,MAAA,EA4CG;IACb,IAAMpI,GAAG,GAAG,EAAZ;IACA,KAAK,IAAIlB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkI,SAAA,CAAK/C,MAAzB,EAAiCnF,CAAC,EAAlC,EAAsC;MACpC+I,KAAK,CAACH,GAAN,CAAU1H,GAAV,EAAoBlB,CAApB,QAAAkI,SAAA,CAAA/C,MAAA,IAAoBnF,CAApB,GAAAlD,SAAA,GAAAoL,SAAA,CAAoBlI,CAApB;IACD;IACD,OAAOkB,GAAP;EACD,CAlDW;EAmDZgG,aAAa,EAAE,CAnDH;EAoDZC,cAAc,EAAE;AApDJ,CAAd;AAuDAyB,GAAG,CAACG,KAAD,EAAQ3C,QAAR,CAAH;;ACxmBA;AACA;AACA;;AACA,IAAQC,SAAR,GAAsB0C,KAAtB,CAAQ1C,SAAR;AAEA,SAASkD,eAATA,CAAyBjD,OAAzB,EAAkC;EAChC,IAAIyC,KAAK,CAACpH,QAAN,CAAe2E,OAAf,KAA2BA,OAAO,CAACxE,QAAR,KAAqB,CAApD,EAAuD;IACrD,OAAO,IAAP;EACD,CAH+B;;EAKhC;AACF;AACA;AACA;AACA;AACA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAM1B,GAAG,GAAG2I,KAAK,CAAClH,WAAN,CAAkByE,OAAlB,CAAZ;EACA,IAAMhG,IAAI,GAAGF,GAAG,CAACE,IAAjB;EACA,IAAIiG,MAAJ;EACA,IAAIiD,aAAa,GAAGT,KAAK,CAACnJ,GAAN,CAAU0G,OAAV,EAAmB,UAAnB,CAApB;EACA,IAAMmD,UAAU,GAAGD,aAAa,KAAK,OAAlB,IAA6BA,aAAa,KAAK,UAAlE;EAEA,IAAI,CAACC,UAAL,EAAiB;IACf,OAAOnD,OAAO,CAACoD,QAAR,CAAiBC,WAAjB,OAAmC,MAAnC,GACH,IADG,GAEHtD,SAAS,CAACC,OAAD,CAFb;EAGD;EAED,KACEC,MAAM,GAAGF,SAAS,CAACC,OAAD,CADpB,EAEEC,MAAM,IAAIA,MAAM,KAAKjG,IAArB,IAA6BiG,MAAM,CAACzE,QAAP,KAAoB,CAFnD,EAGEyE,MAAM,GAAGF,SAAS,CAACE,MAAD,CAHpB,EAIE;IACAiD,aAAa,GAAGT,KAAK,CAACnJ,GAAN,CAAU2G,MAAV,EAAkB,UAAlB,CAAhB;IACA,IAAIiD,aAAa,KAAK,QAAtB,EAAgC;MAC9B,OAAOjD,MAAP;IACD;EACF;EACD,OAAO,IAAP;AACD;AC/CD,IAAQqD,WAAR,GAAsBb,KAAtB,CAAQ1C,SAAR;AAEA,SAAwBwD,eAATA,CAAyBvD,OAAzB,EAAkC;EAC/C,IAAIyC,KAAK,CAACpH,QAAN,CAAe2E,OAAf,KAA2BA,OAAO,CAACxE,QAAR,KAAqB,CAApD,EAAuD;IACrD,OAAO,KAAP;EACD;EAED,IAAM1B,GAAG,GAAG2I,KAAK,CAAClH,WAAN,CAAkByE,OAAlB,CAAZ;EACA,IAAMhG,IAAI,GAAGF,GAAG,CAACE,IAAjB;EACA,IAAIiG,MAAM,GAAG,IAAb;EACA,KACEA,MAAM,GAAGqD,WAAS,CAACtD,OAAD,CADpB;EAAA;EAGEC,MAAM,IAAIA,MAAM,KAAKjG,IAArB,IAA6BiG,MAAM,KAAKnG,GAH1C,EAIEmG,MAAM,GAAGqD,WAAS,CAACrD,MAAD,CAJpB,EAKE;IACA,IAAMiD,aAAa,GAAGT,KAAK,CAACnJ,GAAN,CAAU2G,MAAV,EAAkB,UAAlB,CAAtB;IACA,IAAIiD,aAAa,KAAK,OAAtB,EAA+B;MAC7B,OAAO,IAAP;IACD;EACF;EACD,OAAO,KAAP;AACD;;ACpBD;AACA;AACA;;AACA,SAASM,wBAATA,CAAkCxD,OAAlC,EAA2CyD,gBAA3C,EAA6D;EAC3D,IAAMC,WAAW,GAAG;IAClBpJ,IAAI,EAAE,CADY;IAElBqJ,KAAK,EAAEC,QAFW;IAGlBrJ,GAAG,EAAE,CAHa;IAIlBsJ,MAAM,EAAED;EAJU,CAApB;EAMA,IAAIrK,EAAE,GAAG0J,eAAe,CAACjD,OAAD,CAAxB;EACA,IAAMlG,GAAG,GAAG2I,KAAK,CAAClH,WAAN,CAAkByE,OAAlB,CAAZ;EACA,IAAMM,GAAG,GAAGxG,GAAG,CAACqB,WAAJ,IAAmBrB,GAAG,CAACsB,YAAnC;EACA,IAAMpB,IAAI,GAAGF,GAAG,CAACE,IAAjB;EACA,IAAME,eAAe,GAAGJ,GAAG,CAACI,eAA5B,CAX2D;EAc3D;;EACA,OAAOX,EAAP,EAAW;IACT;IACA,IACE,CAACuK,SAAS,CAACC,SAAV,CAAoBC,OAApB,CAA4B,MAA5B,MAAwC,CAAC,CAAzC,IAA8CzK,EAAE,CAAC0K,WAAH,KAAmB,CAAlE;IAAA;IAEA;IACA;IACC1K,EAAE,KAAKS,IAAP,IACCT,EAAE,KAAKW,eADR,IAECuI,KAAK,CAACnJ,GAAN,CAAUC,EAAV,EAAc,UAAd,MAA8B,SAPlC,EAQE;MACA,IAAM2B,GAAG,GAAGuH,KAAK,CAAC1F,MAAN,CAAaxD,EAAb,CAAZ,CADA;;MAGA2B,GAAG,CAACZ,IAAJ,IAAYf,EAAE,CAACiB,UAAf;MACAU,GAAG,CAACX,GAAJ,IAAWhB,EAAE,CAACkB,SAAd;MACAiJ,WAAW,CAACnJ,GAAZ,GAAkBH,IAAI,CAACiG,GAAL,CAASqD,WAAW,CAACnJ,GAArB,EAA0BW,GAAG,CAACX,GAA9B,CAAlB;MACAmJ,WAAW,CAACC,KAAZ,GAAoBvJ,IAAI,CAAC8J,GAAL,CAClBR,WAAW,CAACC,KADM;MAAA;MAGlBzI,GAAG,CAACZ,IAAJ,GAAWf,EAAE,CAAC0K,WAHI,CAApB;MAKAP,WAAW,CAACG,MAAZ,GAAqBzJ,IAAI,CAAC8J,GAAL,CACnBR,WAAW,CAACG,MADO,EAEnB3I,GAAG,CAACX,GAAJ,GAAUhB,EAAE,CAAC4K,YAFM,CAArB;MAIAT,WAAW,CAACpJ,IAAZ,GAAmBF,IAAI,CAACiG,GAAL,CAASqD,WAAW,CAACpJ,IAArB,EAA2BY,GAAG,CAACZ,IAA/B,CAAnB;IACD,CAxBD,MAwBO,IAAIf,EAAE,KAAKS,IAAP,IAAeT,EAAE,KAAKW,eAA1B,EAA2C;MAChD;IACD;IACDX,EAAE,GAAG0J,eAAe,CAAC1J,EAAD,CAApB;EACD,CA7C0D;EAgD3D;EACA;;EACA,IAAI6K,gBAAgB,GAAG,IAAvB;EACA,IAAI,CAAC3B,KAAK,CAACpH,QAAN,CAAe2E,OAAf,CAAD,IAA4BA,OAAO,CAACxE,QAAR,KAAqB,CAArD,EAAwD;IACtD4I,gBAAgB,GAAGpE,OAAO,CAACvJ,KAAR,CAAcuG,QAAjC;IACA,IAAMA,QAAQ,GAAGyF,KAAK,CAACnJ,GAAN,CAAU0G,OAAV,EAAmB,UAAnB,CAAjB;IACA,IAAIhD,QAAQ,KAAK,UAAjB,EAA6B;MAC3BgD,OAAO,CAACvJ,KAAR,CAAcuG,QAAd,GAAyB,OAAzB;IACD;EACF;EAED,IAAMqH,OAAO,GAAG5B,KAAK,CAACK,mBAAN,CAA0BxC,GAA1B,CAAhB;EACA,IAAMgE,OAAO,GAAG7B,KAAK,CAACM,kBAAN,CAAyBzC,GAAzB,CAAhB;EACA,IAAMM,aAAa,GAAG6B,KAAK,CAAC7B,aAAN,CAAoBN,GAApB,CAAtB;EACA,IAAMO,cAAc,GAAG4B,KAAK,CAAC5B,cAAN,CAAqBP,GAArB,CAAvB;EACA,IAAIiE,aAAa,GAAGrK,eAAe,CAACsK,WAApC;EACA,IAAIC,cAAc,GAAGvK,eAAe,CAACwK,YAArC,CAhE2D;EAmE3D;;EACA,IAAMC,SAAS,GAAGjN,MAAM,CAACC,gBAAP,CAAwBqC,IAAxB,CAAlB;EACA,IAAI2K,SAAS,CAACC,SAAV,KAAwB,QAA5B,EAAsC;IACpCL,aAAa,GAAGjE,GAAG,CAACuE,UAApB;EACD;EACD,IAAIF,SAAS,CAACG,SAAV,KAAwB,QAA5B,EAAsC;IACpCL,cAAc,GAAGnE,GAAG,CAACyE,WAArB;EACD,CA1E0D;;EA6E3D,IAAI/E,OAAO,CAACvJ,KAAZ,EAAmB;IACjBuJ,OAAO,CAACvJ,KAAR,CAAcuG,QAAd,GAAyBoH,gBAAzB;EACD;EAED,IAAIX,gBAAgB,IAAIF,eAAe,CAACvD,OAAD,CAAvC,EAAkD;IAChD;IACA0D,WAAW,CAACpJ,IAAZ,GAAmBF,IAAI,CAACiG,GAAL,CAASqD,WAAW,CAACpJ,IAArB,EAA2B+J,OAA3B,CAAnB;IACAX,WAAW,CAACnJ,GAAZ,GAAkBH,IAAI,CAACiG,GAAL,CAASqD,WAAW,CAACnJ,GAArB,EAA0B+J,OAA1B,CAAlB;IACAZ,WAAW,CAACC,KAAZ,GAAoBvJ,IAAI,CAAC8J,GAAL,CAASR,WAAW,CAACC,KAArB,EAA4BU,OAAO,GAAGzD,aAAtC,CAApB;IACA8C,WAAW,CAACG,MAAZ,GAAqBzJ,IAAI,CAAC8J,GAAL,CAASR,WAAW,CAACG,MAArB,EAA6BS,OAAO,GAAGzD,cAAvC,CAArB;EACD,CAND,MAMO;IACL;IACA,IAAMmE,eAAe,GAAG5K,IAAI,CAACiG,GAAL,CAASkE,aAAT,EAAwBF,OAAO,GAAGzD,aAAlC,CAAxB;IACA8C,WAAW,CAACC,KAAZ,GAAoBvJ,IAAI,CAAC8J,GAAL,CAASR,WAAW,CAACC,KAArB,EAA4BqB,eAA5B,CAApB;IAEA,IAAMC,gBAAgB,GAAG7K,IAAI,CAACiG,GAAL,CAASoE,cAAT,EAAyBH,OAAO,GAAGzD,cAAnC,CAAzB;IACA6C,WAAW,CAACG,MAAZ,GAAqBzJ,IAAI,CAAC8J,GAAL,CAASR,WAAW,CAACG,MAArB,EAA6BoB,gBAA7B,CAArB;EACD;EAED,OAAOvB,WAAW,CAACnJ,GAAZ,IAAmB,CAAnB,IACLmJ,WAAW,CAACpJ,IAAZ,IAAoB,CADf,IAELoJ,WAAW,CAACG,MAAZ,GAAqBH,WAAW,CAACnJ,GAF5B,IAGLmJ,WAAW,CAACC,KAAZ,GAAoBD,WAAW,CAACpJ,IAH3B,GAIHoJ,WAJG,GAKH,IALJ;AAMD;AC3GD,SAASwB,iBAATA,CAA2BC,WAA3B,EAAwCC,QAAxC,EAAkD1B,WAAlD,EAA+Db,QAA/D,EAAyE;EACvE,IAAM3H,GAAG,GAAGuH,KAAK,CAACG,KAAN,CAAYuC,WAAZ,CAAZ;EACA,IAAME,IAAI,GAAG;IACXpE,KAAK,EAAEmE,QAAQ,CAACnE,KADL;IAEXC,MAAM,EAAEkE,QAAQ,CAAClE;EAFN,CAAb;EAKA,IAAI2B,QAAQ,CAACyC,OAAT,IAAoBpK,GAAG,CAACZ,IAAJ,GAAWoJ,WAAW,CAACpJ,IAA/C,EAAqD;IACnDY,GAAG,CAACZ,IAAJ,GAAWoJ,WAAW,CAACpJ,IAAvB;EACD,CATsE;;EAYvE,IACEuI,QAAQ,CAAC0C,WAAT,IACArK,GAAG,CAACZ,IAAJ,IAAYoJ,WAAW,CAACpJ,IADxB,IAEAY,GAAG,CAACZ,IAAJ,GAAW+K,IAAI,CAACpE,KAAhB,GAAwByC,WAAW,CAACC,KAHtC,EAIE;IACA0B,IAAI,CAACpE,KAAL,IAAc/F,GAAG,CAACZ,IAAJ,GAAW+K,IAAI,CAACpE,KAAhB,GAAwByC,WAAW,CAACC,KAAlD;EACD,CAlBsE;;EAqBvE,IAAId,QAAQ,CAACyC,OAAT,IAAoBpK,GAAG,CAACZ,IAAJ,GAAW+K,IAAI,CAACpE,KAAhB,GAAwByC,WAAW,CAACC,KAA5D,EAAmE;IACjE;IACAzI,GAAG,CAACZ,IAAJ,GAAWF,IAAI,CAACiG,GAAL,CAASqD,WAAW,CAACC,KAAZ,GAAoB0B,IAAI,CAACpE,KAAlC,EAAyCyC,WAAW,CAACpJ,IAArD,CAAX;EACD,CAxBsE;;EA2BvE,IAAIuI,QAAQ,CAAC2C,OAAT,IAAoBtK,GAAG,CAACX,GAAJ,GAAUmJ,WAAW,CAACnJ,GAA9C,EAAmD;IACjDW,GAAG,CAACX,GAAJ,GAAUmJ,WAAW,CAACnJ,GAAtB;EACD,CA7BsE;;EAgCvE,IACEsI,QAAQ,CAAC4C,YAAT,IACAvK,GAAG,CAACX,GAAJ,IAAWmJ,WAAW,CAACnJ,GADvB,IAEAW,GAAG,CAACX,GAAJ,GAAU8K,IAAI,CAACnE,MAAf,GAAwBwC,WAAW,CAACG,MAHtC,EAIE;IACAwB,IAAI,CAACnE,MAAL,IAAehG,GAAG,CAACX,GAAJ,GAAU8K,IAAI,CAACnE,MAAf,GAAwBwC,WAAW,CAACG,MAAnD;EACD,CAtCsE;;EAyCvE,IAAIhB,QAAQ,CAAC2C,OAAT,IAAoBtK,GAAG,CAACX,GAAJ,GAAU8K,IAAI,CAACnE,MAAf,GAAwBwC,WAAW,CAACG,MAA5D,EAAoE;IAClE;IACA3I,GAAG,CAACX,GAAJ,GAAUH,IAAI,CAACiG,GAAL,CAASqD,WAAW,CAACG,MAAZ,GAAqBwB,IAAI,CAACnE,MAAnC,EAA2CwC,WAAW,CAACnJ,GAAvD,CAAV;EACD;EAED,OAAOkI,KAAK,CAACH,GAAN,CAAUpH,GAAV,EAAemK,IAAf,CAAP;AACD;AC/CD,SAASK,SAATA,CAAmBxO,IAAnB,EAAyB;EACvB,IAAI6F,MAAJ;EACA,IAAIpC,CAAJ;EACA,IAAIgL,CAAJ;EACA,IAAI,CAAClD,KAAK,CAACpH,QAAN,CAAenE,IAAf,CAAD,IAAyBA,IAAI,CAACsE,QAAL,KAAkB,CAA/C,EAAkD;IAChDuB,MAAM,GAAG0F,KAAK,CAAC1F,MAAN,CAAa7F,IAAb,CAAT;IACAyD,CAAC,GAAG8H,KAAK,CAACmD,UAAN,CAAiB1O,IAAjB,CAAJ;IACAyO,CAAC,GAAGlD,KAAK,CAACoD,WAAN,CAAkB3O,IAAlB,CAAJ;EACD,CAJD,MAIO;IACL,IAAMoJ,GAAG,GAAGmC,KAAK,CAACC,SAAN,CAAgBxL,IAAhB,CAAZ;IACA6F,MAAM,GAAG;MACPzC,IAAI,EAAEmI,KAAK,CAACK,mBAAN,CAA0BxC,GAA1B,CADC;MAEP/F,GAAG,EAAEkI,KAAK,CAACM,kBAAN,CAAyBzC,GAAzB;IAFE,CAAT;IAIA3F,CAAC,GAAG8H,KAAK,CAAC7B,aAAN,CAAoBN,GAApB,CAAJ;IACAqF,CAAC,GAAGlD,KAAK,CAAC5B,cAAN,CAAqBP,GAArB,CAAJ;EACD;EACDvD,MAAM,CAACkE,KAAP,GAAetG,CAAf;EACAoC,MAAM,CAACmE,MAAP,GAAgByE,CAAhB;EACA,OAAO5I,MAAP;AACD;;ACtBD;AACA;AACA;AAEA,SAAS+I,cAATA,CAAwBC,MAAxB,EAAgCC,KAAhC,EAAuC;EACrC,IAAMC,CAAC,GAAGD,KAAK,CAAC7D,MAAN,CAAa,CAAb,CAAV;EACA,IAAM+D,CAAC,GAAGF,KAAK,CAAC7D,MAAN,CAAa,CAAb,CAAV;EACA,IAAMxH,CAAC,GAAGoL,MAAM,CAAC9E,KAAjB;EACA,IAAM0E,CAAC,GAAGI,MAAM,CAAC7E,MAAjB;EAEA,IAAIlJ,CAAC,GAAG+N,MAAM,CAACzL,IAAf;EACA,IAAIpC,CAAC,GAAG6N,MAAM,CAACxL,GAAf;EAEA,IAAI0L,CAAC,KAAK,GAAV,EAAe;IACb/N,CAAC,IAAIyN,CAAC,GAAG,CAAT;EACD,CAFD,MAEO,IAAIM,CAAC,KAAK,GAAV,EAAe;IACpB/N,CAAC,IAAIyN,CAAL;EACD;EAED,IAAIO,CAAC,KAAK,GAAV,EAAe;IACblO,CAAC,IAAI2C,CAAC,GAAG,CAAT;EACD,CAFD,MAEO,IAAIuL,CAAC,KAAK,GAAV,EAAe;IACpBlO,CAAC,IAAI2C,CAAL;EACD;EAED,OAAO;IACLL,IAAI,EAAEtC,CADD;IAELuC,GAAG,EAAErC;EAFA,CAAP;AAID;AC3BD,SAASiO,cAATA,CAAwBf,QAAxB,EAAkCgB,aAAlC,EAAiDC,MAAjD,EAAyDtJ,MAAzD,EAAiEuJ,YAAjE,EAA+E;EAC7E,IAAMC,EAAE,GAAGT,cAAc,CAACM,aAAD,EAAgBC,MAAM,CAAC,CAAD,CAAtB,CAAzB;EACA,IAAMG,EAAE,GAAGV,cAAc,CAACV,QAAD,EAAWiB,MAAM,CAAC,CAAD,CAAjB,CAAzB;EACA,IAAMI,IAAI,GAAG,CAACD,EAAE,CAAClM,IAAH,GAAUiM,EAAE,CAACjM,IAAd,EAAoBkM,EAAE,CAACjM,GAAH,GAASgM,EAAE,CAAChM,GAAhC,CAAb;EAEA,OAAO;IACLD,IAAI,EAAEF,IAAI,CAACsM,KAAL,CAAWtB,QAAQ,CAAC9K,IAAT,GAAgBmM,IAAI,CAAC,CAAD,CAApB,GAA0B1J,MAAM,CAAC,CAAD,CAAhC,GAAsCuJ,YAAY,CAAC,CAAD,CAA7D,CADD;IAEL/L,GAAG,EAAEH,IAAI,CAACsM,KAAL,CAAWtB,QAAQ,CAAC7K,GAAT,GAAekM,IAAI,CAAC,CAAD,CAAnB,GAAyB1J,MAAM,CAAC,CAAD,CAA/B,GAAqCuJ,YAAY,CAAC,CAAD,CAA5D;EAFA,CAAP;AAID;;ACXD;AACA;AACA;AACA;;AAUA,SAASK,OAATA,CAAiBxB,WAAjB,EAA8BC,QAA9B,EAAwC1B,WAAxC,EAAqD;EACnD,OACEyB,WAAW,CAAC7K,IAAZ,GAAmBoJ,WAAW,CAACpJ,IAA/B,IACA6K,WAAW,CAAC7K,IAAZ,GAAmB8K,QAAQ,CAACnE,KAA5B,GAAoCyC,WAAW,CAACC,KAFlD;AAID;AAED,SAASiD,OAATA,CAAiBzB,WAAjB,EAA8BC,QAA9B,EAAwC1B,WAAxC,EAAqD;EACnD,OACEyB,WAAW,CAAC5K,GAAZ,GAAkBmJ,WAAW,CAACnJ,GAA9B,IACA4K,WAAW,CAAC5K,GAAZ,GAAkB6K,QAAQ,CAAClE,MAA3B,GAAoCwC,WAAW,CAACG,MAFlD;AAID;AAED,SAASgD,eAATA,CAAyB1B,WAAzB,EAAsCC,QAAtC,EAAgD1B,WAAhD,EAA6D;EAC3D,OACEyB,WAAW,CAAC7K,IAAZ,GAAmBoJ,WAAW,CAACC,KAA/B,IACAwB,WAAW,CAAC7K,IAAZ,GAAmB8K,QAAQ,CAACnE,KAA5B,GAAoCyC,WAAW,CAACpJ,IAFlD;AAID;AAED,SAASwM,eAATA,CAAyB3B,WAAzB,EAAsCC,QAAtC,EAAgD1B,WAAhD,EAA6D;EAC3D,OACEyB,WAAW,CAAC5K,GAAZ,GAAkBmJ,WAAW,CAACG,MAA9B,IACAsB,WAAW,CAAC5K,GAAZ,GAAkB6K,QAAQ,CAAClE,MAA3B,GAAoCwC,WAAW,CAACnJ,GAFlD;AAID;AAED,SAASwM,IAATA,CAAcV,MAAd,EAAsBW,GAAtB,EAA2BtO,GAA3B,EAAgC;EAC9B,IAAMkC,GAAG,GAAG,EAAZ;EACA6H,KAAK,CAAC9D,IAAN,CAAW0H,MAAX,EAAmB,UAAAY,CAAC,EAAI;IACtBrM,GAAG,CAACsM,IAAJ,CACED,CAAC,CAACnP,OAAF,CAAUkP,GAAV,EAAe,UAAAG,CAAC,EAAI;MAClB,OAAOzO,GAAG,CAACyO,CAAD,CAAV;IACD,CAFD,CADF;EAKD,CAND;EAOA,OAAOvM,GAAP;AACD;AAED,SAASwM,UAATA,CAAoBrK,MAApB,EAA4BsK,KAA5B,EAAmC;EACjCtK,MAAM,CAACsK,KAAD,CAAN,GAAgB,CAACtK,MAAM,CAACsK,KAAD,CAAvB;EACA,OAAOtK,MAAP;AACD;AAED,SAASuK,aAATA,CAAuBC,GAAvB,EAA4BC,SAA5B,EAAuC;EACrC,IAAIC,CAAJ;EACA,IAAI,KAAKpL,IAAL,CAAUkL,GAAV,CAAJ,EAAoB;IAClBE,CAAC,GAAIC,QAAQ,CAACH,GAAG,CAACI,SAAJ,CAAc,CAAd,EAAiBJ,GAAG,CAAC1I,MAAJ,GAAa,CAA9B,CAAD,EAAmC,EAAnC,CAAR,GAAiD,GAAlD,GAAyD2I,SAA7D;EACD,CAFD,MAEO;IACLC,CAAC,GAAGC,QAAQ,CAACH,GAAD,EAAM,EAAN,CAAZ;EACD;EACD,OAAOE,CAAC,IAAI,CAAZ;AACD;AAED,SAASG,eAATA,CAAyB7K,MAAzB,EAAiCxD,EAAjC,EAAqC;EACnCwD,MAAM,CAAC,CAAD,CAAN,GAAYuK,aAAa,CAACvK,MAAM,CAAC,CAAD,CAAP,EAAYxD,EAAE,CAAC0H,KAAf,CAAzB;EACAlE,MAAM,CAAC,CAAD,CAAN,GAAYuK,aAAa,CAACvK,MAAM,CAAC,CAAD,CAAP,EAAYxD,EAAE,CAAC2H,MAAf,CAAzB;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,SAAS2G,OAATA,CAAiBtO,EAAjB,EAAqBuO,SAArB,EAAgC9B,KAAhC,EAAuC+B,kBAAvC,EAA2D;EACzD,IAAI1B,MAAM,GAAGL,KAAK,CAACK,MAAnB;EACA,IAAItJ,MAAM,GAAGiJ,KAAK,CAACjJ,MAAN,IAAgB,CAAC,CAAD,EAAI,CAAJ,CAA7B;EACA,IAAIuJ,YAAY,GAAGN,KAAK,CAACM,YAAN,IAAsB,CAAC,CAAD,EAAI,CAAJ,CAAzC;EACA,IAAIzD,QAAQ,GAAGmD,KAAK,CAACnD,QAArB;EACA,IAAM9J,MAAM,GAAGiN,KAAK,CAACjN,MAAN,IAAgBQ,EAA/B;EACAwD,MAAM,GAAG,GAAGhG,MAAH,CAAUgG,MAAV,CAAT;EACAuJ,YAAY,GAAG,GAAGvP,MAAH,CAAUuP,YAAV,CAAf;EACAzD,QAAQ,GAAGA,QAAQ,IAAI,EAAvB;EACA,IAAMmF,cAAc,GAAG,EAAvB;EACA,IAAIC,IAAI,GAAG,CAAX;EACA,IAAMxE,gBAAgB,GAAG,CAAC,EAAEZ,QAAQ,IAAIA,QAAQ,CAACY,gBAAvB,CAA1B,CAXyD;;EAazD,IAAMC,WAAW,GAAGF,wBAAwB,CAACzK,MAAD,EAAS0K,gBAAT,CAA5C,CAbyD;;EAezD,IAAM2B,QAAQ,GAAGM,SAAS,CAAC3M,MAAD,CAA1B,CAfyD;;EAiBzD6O,eAAe,CAAC7K,MAAD,EAASqI,QAAT,CAAf;EACAwC,eAAe,CAACtB,YAAD,EAAewB,SAAf,CAAf,CAlByD;;EAoBzD,IAAI3C,WAAW,GAAGgB,cAAc,CAC9Bf,QAD8B,EAE9B0C,SAF8B,EAG9BzB,MAH8B,EAI9BtJ,MAJ8B,EAK9BuJ,YAL8B,CAAhC,CApByD;;EA4BzD,IAAI4B,WAAW,GAAGzF,KAAK,CAACO,KAAN,CAAYoC,QAAZ,EAAsBD,WAAtB,CAAlB,CA5ByD;;EA+BzD,IACEzB,WAAW,KACVb,QAAQ,CAACyC,OAAT,IAAoBzC,QAAQ,CAAC2C,OADnB,CAAX,IAEAuC,kBAHF,EAIE;IACA,IAAIlF,QAAQ,CAACyC,OAAb,EAAsB;MACpB;MACA,IAAIqB,OAAO,CAACxB,WAAD,EAAcC,QAAd,EAAwB1B,WAAxB,CAAX,EAAiD;QAC/C;QACA,IAAMyE,SAAS,GAAGpB,IAAI,CAACV,MAAD,EAAS,QAAT,EAAmB;UACvC+B,CAAC,EAAE,GADoC;UAEvCC,CAAC,EAAE;QAFoC,CAAnB,CAAtB,CAF+C;;QAO/C,IAAMC,SAAS,GAAGlB,UAAU,CAACrK,MAAD,EAAS,CAAT,CAA5B;QACA,IAAMwL,eAAe,GAAGnB,UAAU,CAACd,YAAD,EAAe,CAAf,CAAlC;QACA,IAAMkC,cAAc,GAAGrC,cAAc,CACnCf,QADmC,EAEnC0C,SAFmC,EAGnCK,SAHmC,EAInCG,SAJmC,EAKnCC,eALmC,CAArC;QAQA,IAAI,CAAC1B,eAAe,CAAC2B,cAAD,EAAiBpD,QAAjB,EAA2B1B,WAA3B,CAApB,EAA6D;UAC3DuE,IAAI,GAAG,CAAP;UACA5B,MAAM,GAAG8B,SAAT;UACApL,MAAM,GAAGuL,SAAT;UACAhC,YAAY,GAAGiC,eAAf;QACD;MACF;IACF;IAED,IAAI1F,QAAQ,CAAC2C,OAAb,EAAsB;MACpB;MACA,IAAIoB,OAAO,CAACzB,WAAD,EAAcC,QAAd,EAAwB1B,WAAxB,CAAX,EAAiD;QAC/C;QACA,IAAM+E,UAAS,GAAG1B,IAAI,CAACV,MAAD,EAAS,QAAT,EAAmB;UACvCqC,CAAC,EAAE,GADoC;UAEvCC,CAAC,EAAE;QAFoC,CAAnB,CAAtB,CAF+C;;QAO/C,IAAMC,UAAS,GAAGxB,UAAU,CAACrK,MAAD,EAAS,CAAT,CAA5B;QACA,IAAM8L,gBAAe,GAAGzB,UAAU,CAACd,YAAD,EAAe,CAAf,CAAlC;QACA,IAAMwC,eAAc,GAAG3C,cAAc,CACnCf,QADmC,EAEnC0C,SAFmC,EAGnCW,UAHmC,EAInCG,UAJmC,EAKnCC,gBALmC,CAArC;QAQA,IAAI,CAAC/B,eAAe,CAACgC,eAAD,EAAiB1D,QAAjB,EAA2B1B,WAA3B,CAApB,EAA6D;UAC3DuE,IAAI,GAAG,CAAP;UACA5B,MAAM,GAAGoC,UAAT;UACA1L,MAAM,GAAG6L,UAAT;UACAtC,YAAY,GAAGuC,gBAAf;QACD;MACF;IACF,CAvDD;;IA0DA,IAAIZ,IAAJ,EAAU;MACR9C,WAAW,GAAGgB,cAAc,CAC1Bf,QAD0B,EAE1B0C,SAF0B,EAG1BzB,MAH0B,EAI1BtJ,MAJ0B,EAK1BuJ,YAL0B,CAA5B;MAOA7D,KAAK,CAACH,GAAN,CAAU4F,WAAV,EAAuB/C,WAAvB;IACD;IACD,IAAM4D,YAAY,GAAGpC,OAAO,CAACxB,WAAD,EAAcC,QAAd,EAAwB1B,WAAxB,CAA5B;IACA,IAAMsF,YAAY,GAAGpC,OAAO,CAACzB,WAAD,EAAcC,QAAd,EAAwB1B,WAAxB,CAA5B,CArEA;IAuEA;;IACA,IAAIqF,YAAY,IAAIC,YAApB,EAAkC;MAChC,IAAIC,WAAS,GAAG5C,MAAhB,CADgC;;MAIhC,IAAI0C,YAAJ,EAAkB;QAChBE,WAAS,GAAGlC,IAAI,CAACV,MAAD,EAAS,QAAT,EAAmB;UACjC+B,CAAC,EAAE,GAD8B;UAEjCC,CAAC,EAAE;QAF8B,CAAnB,CAAhB;MAID;MACD,IAAIW,YAAJ,EAAkB;QAChBC,WAAS,GAAGlC,IAAI,CAACV,MAAD,EAAS,QAAT,EAAmB;UACjCqC,CAAC,EAAE,GAD8B;UAEjCC,CAAC,EAAE;QAF8B,CAAnB,CAAhB;MAID;MAEDtC,MAAM,GAAG4C,WAAT;MAEAlM,MAAM,GAAGiJ,KAAK,CAACjJ,MAAN,IAAgB,CAAC,CAAD,EAAI,CAAJ,CAAzB;MACAuJ,YAAY,GAAGN,KAAK,CAACM,YAAN,IAAsB,CAAC,CAAD,EAAI,CAAJ,CAArC;IACD,CA7FD;;IA+FA0B,cAAc,CAAC1C,OAAf,GAAyBzC,QAAQ,CAACyC,OAAT,IAAoByD,YAA7C;IACAf,cAAc,CAACxC,OAAf,GAAyB3C,QAAQ,CAAC2C,OAAT,IAAoBwD,YAA7C,CAhGA;;IAmGA,IAAIhB,cAAc,CAAC1C,OAAf,IAA0B0C,cAAc,CAACxC,OAA7C,EAAsD;MACpD0C,WAAW,GAAGhD,iBAAiB,CAC7BC,WAD6B,EAE7BC,QAF6B,EAG7B1B,WAH6B,EAI7BsE,cAJ6B,CAA/B;IAMD;EACF,CA9IwD;;EAiJzD,IAAIE,WAAW,CAACjH,KAAZ,KAAsBmE,QAAQ,CAACnE,KAAnC,EAA0C;IACxCwB,KAAK,CAACnJ,GAAN,CACEP,MADF,EAEE,OAFF,EAGE0J,KAAK,CAACxB,KAAN,CAAYlI,MAAZ,IAAsBmP,WAAW,CAACjH,KAAlC,GAA0CmE,QAAQ,CAACnE,KAHrD;EAKD;EAED,IAAIiH,WAAW,CAAChH,MAAZ,KAAuBkE,QAAQ,CAAClE,MAApC,EAA4C;IAC1CuB,KAAK,CAACnJ,GAAN,CACEP,MADF,EAEE,QAFF,EAGE0J,KAAK,CAACvB,MAAN,CAAanI,MAAb,IAAuBmP,WAAW,CAAChH,MAAnC,GAA4CkE,QAAQ,CAAClE,MAHvD;EAKD,CA/JwD;EAkKzD;EACA;;EACAuB,KAAK,CAAC1F,MAAN,CACEhE,MADF,EAEE;IACEuB,IAAI,EAAE4N,WAAW,CAAC5N,IADpB;IAEEC,GAAG,EAAE2N,WAAW,CAAC3N;EAFnB,CAFF,EAME;IACEoC,WAAW,EAAEqJ,KAAK,CAACrJ,WADrB;IAEEC,YAAY,EAAEoJ,KAAK,CAACpJ,YAFtB;IAGE8B,eAAe,EAAEsH,KAAK,CAACtH,eAHzB;IAIEP,WAAW,EAAE6H,KAAK,CAAC7H;EAJrB,CANF;EAcA,OAAO;IACLkI,MAAM,EAANA,MADK;IAELtJ,MAAM,EAANA,MAFK;IAGLuJ,YAAY,EAAZA,YAHK;IAILzD,QAAQ,EAAEmF;EAJL,CAAP;AAMD;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC3QA,SAASkB,kBAATA,CAA4BC,MAA5B,EAAoC1F,gBAApC,EAAsD;EACpD,IAAMC,WAAW,GAAGF,wBAAwB,CAAC2F,MAAD,EAAS1F,gBAAT,CAA5C;EACA,IAAM2F,YAAY,GAAG1D,SAAS,CAACyD,MAAD,CAA9B;EAEA,OACE,CAACzF,WAAD,IACA0F,YAAY,CAAC9O,IAAb,GAAoB8O,YAAY,CAACnI,KAAjC,IAA0CyC,WAAW,CAACpJ,IADtD,IAEA8O,YAAY,CAAC7O,GAAb,GAAmB6O,YAAY,CAAClI,MAAhC,IAA0CwC,WAAW,CAACnJ,GAFtD,IAGA6O,YAAY,CAAC9O,IAAb,IAAqBoJ,WAAW,CAACC,KAHjC,IAIAyF,YAAY,CAAC7O,GAAb,IAAoBmJ,WAAW,CAACG,MALlC;AAOD;AAED,SAASwF,YAATA,CAAsB9P,EAAtB,EAA0B+P,OAA1B,EAAmCtD,KAAnC,EAA0C;EACxC,IAAMmD,MAAM,GAAGnD,KAAK,CAACmD,MAAN,IAAgBG,OAA/B;EACA,IAAMlD,aAAa,GAAGV,SAAS,CAACyD,MAAD,CAA/B;EAEA,IAAMI,uBAAuB,GAAG,CAACL,kBAAkB,CACjDC,MADiD,EAEjDnD,KAAK,CAACnD,QAAN,IAAkBmD,KAAK,CAACnD,QAAN,CAAeY,gBAFgB,CAAnD;EAKA,OAAOoE,OAAO,CAACtO,EAAD,EAAK6M,aAAL,EAAoBJ,KAApB,EAA2BuD,uBAA3B,CAAd;AACD;AAEDF,YAAY,CAACG,iBAAb,GAAiCvG,eAAjC;AAEAoG,YAAY,CAACI,0BAAb,GAA0CjG,wBAA1C;;AC7BA;AACA;AACA;AACA;;AAEA,SAASkG,UAATA,CAAoBnQ,EAApB,EAAwBoQ,QAAxB,EAAkC3D,KAAlC,EAAyC;EACvC,IAAI4D,KAAJ;EACA,IAAIC,KAAJ;EAEA,IAAM/P,GAAG,GAAG2I,KAAK,CAAClH,WAAN,CAAkBhC,EAAlB,CAAZ;EACA,IAAM+G,GAAG,GAAGxG,GAAG,CAACqB,WAAJ,IAAmBrB,GAAG,CAACsB,YAAnC;EAEA,IAAMiJ,OAAO,GAAG5B,KAAK,CAACK,mBAAN,CAA0BxC,GAA1B,CAAhB;EACA,IAAMgE,OAAO,GAAG7B,KAAK,CAACM,kBAAN,CAAyBzC,GAAzB,CAAhB;EACA,IAAMM,aAAa,GAAG6B,KAAK,CAAC7B,aAAN,CAAoBN,GAApB,CAAtB;EACA,IAAMO,cAAc,GAAG4B,KAAK,CAAC5B,cAAN,CAAqBP,GAArB,CAAvB;EAEA,IAAI,WAAWqJ,QAAf,EAAyB;IACvBC,KAAK,GAAGD,QAAQ,CAACC,KAAjB;EACD,CAFD,MAEO;IACLA,KAAK,GAAGvF,OAAO,GAAGsF,QAAQ,CAACG,OAA3B;EACD;EAED,IAAI,WAAWH,QAAf,EAAyB;IACvBE,KAAK,GAAGF,QAAQ,CAACE,KAAjB;EACD,CAFD,MAEO;IACLA,KAAK,GAAGvF,OAAO,GAAGqF,QAAQ,CAACI,OAA3B;EACD;EAED,IAAMjC,SAAS,GAAG;IAChBxN,IAAI,EAAEsP,KADU;IAEhBrP,GAAG,EAAEsP,KAFW;IAGhB5I,KAAK,EAAE,CAHS;IAIhBC,MAAM,EAAE;EAJQ,CAAlB;EAOA,IAAM8I,WAAW,GACfJ,KAAK,IAAI,CAAT,IACAA,KAAK,IAAIvF,OAAO,GAAGzD,aADnB,IAECiJ,KAAK,IAAI,CAAT,IAAcA,KAAK,IAAIvF,OAAO,GAAGzD,cAHpC,CA/BuC;;EAqCvC,IAAMwF,MAAM,GAAG,CAACL,KAAK,CAACK,MAAN,CAAa,CAAb,CAAD,EAAkB,IAAlB,CAAf;EAEA,OAAOwB,OAAO,CAACtO,EAAD,EAAKuO,SAAL,EAAAmC,cAAA,CAAAA,cAAA,KAAqBjE,KAArB;IAA4BK,MAAM,EAANA;EAA5B,IAAsC2D,WAAtC,CAAd;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}