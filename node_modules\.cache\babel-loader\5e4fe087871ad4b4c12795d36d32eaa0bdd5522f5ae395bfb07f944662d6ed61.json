{"ast": null, "code": "import Skeleton from './Skeleton';\nexport default Skeleton;", "map": {"version": 3, "names": ["Skeleton"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/index.js"], "sourcesContent": ["import Skeleton from './Skeleton';\nexport default Skeleton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}