{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport omit from \"rc-util/es/omit\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport { getPosition, isTreeNode } from '../util';\nexport function getKey(key, pos) {\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  return pos;\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    title = _ref.title,\n    _title = _ref._title,\n    key = _ref.key,\n    children = _ref.children;\n  var mergedTitle = title || 'title';\n  return {\n    title: mergedTitle,\n    _title: _title || [mergedTitle],\n    key: key || 'key',\n    children: children || 'children'\n  };\n}\n/**\n * Warning if TreeNode do not provides key\n */\n\nexport function warningWithoutKey(treeData, fieldNames) {\n  var keys = new Map();\n  function dig(list) {\n    var path = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    (list || []).forEach(function (treeNode) {\n      var key = treeNode[fieldNames.key];\n      var children = treeNode[fieldNames.children];\n      warning(key !== null && key !== undefined, \"Tree node must have a certain key: [\".concat(path).concat(key, \"]\"));\n      var recordKey = String(key);\n      warning(!keys.has(recordKey) || key === null || key === undefined, \"Same 'key' exist in the Tree: \".concat(recordKey));\n      keys.set(recordKey, true);\n      dig(children, \"\".concat(path).concat(recordKey, \" > \"));\n    });\n  }\n  dig(treeData);\n}\n/**\n * Convert `children` of Tree into `treeData` structure.\n */\n\nexport function convertTreeToData(rootNodes) {\n  function dig(node) {\n    var treeNodes = toArray(node);\n    return treeNodes.map(function (treeNode) {\n      // Filter invalidate node\n      if (!isTreeNode(treeNode)) {\n        warning(!treeNode, 'Tree/TreeNode can only accept TreeNode as children.');\n        return null;\n      }\n      var key = treeNode.key;\n      var _treeNode$props = treeNode.props,\n        children = _treeNode$props.children,\n        rest = _objectWithoutProperties(_treeNode$props, _excluded);\n      var dataNode = _objectSpread({\n        key: key\n      }, rest);\n      var parsedChildren = dig(children);\n      if (parsedChildren.length) {\n        dataNode.children = parsedChildren;\n      }\n      return dataNode;\n    }).filter(function (dataNode) {\n      return dataNode;\n    });\n  }\n  return dig(rootNodes);\n}\n/**\n * Flat nest tree data into flatten list. This is used for virtual list render.\n * @param treeNodeList Origin data node list\n * @param expandedKeys\n * need expanded keys, provides `true` means all expanded (used in `rc-tree-select`).\n */\n\nexport function flattenTreeData(treeNodeList, expandedKeys, fieldNames) {\n  var _fillFieldNames = fillFieldNames(fieldNames),\n    fieldTitles = _fillFieldNames._title,\n    fieldKey = _fillFieldNames.key,\n    fieldChildren = _fillFieldNames.children;\n  var expandedKeySet = new Set(expandedKeys === true ? [] : expandedKeys);\n  var flattenList = [];\n  function dig(list) {\n    var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    return list.map(function (treeNode, index) {\n      var pos = getPosition(parent ? parent.pos : '0', index);\n      var mergedKey = getKey(treeNode[fieldKey], pos); // Pick matched title in field title list\n\n      var mergedTitle;\n      for (var i = 0; i < fieldTitles.length; i += 1) {\n        var fieldTitle = fieldTitles[i];\n        if (treeNode[fieldTitle] !== undefined) {\n          mergedTitle = treeNode[fieldTitle];\n          break;\n        }\n      } // Add FlattenDataNode into list\n\n      var flattenNode = _objectSpread(_objectSpread({}, omit(treeNode, [].concat(_toConsumableArray(fieldTitles), [fieldKey, fieldChildren]))), {}, {\n        title: mergedTitle,\n        key: mergedKey,\n        parent: parent,\n        pos: pos,\n        children: null,\n        data: treeNode,\n        isStart: [].concat(_toConsumableArray(parent ? parent.isStart : []), [index === 0]),\n        isEnd: [].concat(_toConsumableArray(parent ? parent.isEnd : []), [index === list.length - 1])\n      });\n      flattenList.push(flattenNode); // Loop treeNode children\n\n      if (expandedKeys === true || expandedKeySet.has(mergedKey)) {\n        flattenNode.children = dig(treeNode[fieldChildren] || [], flattenNode);\n      } else {\n        flattenNode.children = [];\n      }\n      return flattenNode;\n    });\n  }\n  dig(treeNodeList);\n  return flattenList;\n}\n/**\n * Traverse all the data by `treeData`.\n * Please not use it out of the `rc-tree` since we may refactor this code.\n */\n\nexport function traverseDataNodes(dataNodes, callback,\n// To avoid too many params, let use config instead of origin param\nconfig) {\n  var mergedConfig = {};\n  if (_typeof(config) === 'object') {\n    mergedConfig = config;\n  } else {\n    mergedConfig = {\n      externalGetKey: config\n    };\n  }\n  mergedConfig = mergedConfig || {}; // Init config\n\n  var _mergedConfig = mergedConfig,\n    childrenPropName = _mergedConfig.childrenPropName,\n    externalGetKey = _mergedConfig.externalGetKey,\n    fieldNames = _mergedConfig.fieldNames;\n  var _fillFieldNames2 = fillFieldNames(fieldNames),\n    fieldKey = _fillFieldNames2.key,\n    fieldChildren = _fillFieldNames2.children;\n  var mergeChildrenPropName = childrenPropName || fieldChildren; // Get keys\n\n  var syntheticGetKey;\n  if (externalGetKey) {\n    if (typeof externalGetKey === 'string') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return node[externalGetKey];\n      };\n    } else if (typeof externalGetKey === 'function') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return externalGetKey(node);\n      };\n    }\n  } else {\n    syntheticGetKey = function syntheticGetKey(node, pos) {\n      return getKey(node[fieldKey], pos);\n    };\n  } // Process\n\n  function processNode(node, index, parent, pathNodes) {\n    var children = node ? node[mergeChildrenPropName] : dataNodes;\n    var pos = node ? getPosition(parent.pos, index) : '0';\n    var connectNodes = node ? [].concat(_toConsumableArray(pathNodes), [node]) : []; // Process node if is not root\n\n    if (node) {\n      var key = syntheticGetKey(node, pos);\n      var data = {\n        node: node,\n        index: index,\n        pos: pos,\n        key: key,\n        parentPos: parent.node ? parent.pos : null,\n        level: parent.level + 1,\n        nodes: connectNodes\n      };\n      callback(data);\n    } // Process children node\n\n    if (children) {\n      children.forEach(function (subNode, subIndex) {\n        processNode(subNode, subIndex, {\n          node: node,\n          pos: pos,\n          level: parent ? parent.level + 1 : -1\n        }, connectNodes);\n      });\n    }\n  }\n  processNode(null);\n}\n/**\n * Convert `treeData` into entity records.\n */\n\nexport function convertDataToEntities(dataNodes) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    initWrapper = _ref2.initWrapper,\n    processEntity = _ref2.processEntity,\n    onProcessFinished = _ref2.onProcessFinished,\n    externalGetKey = _ref2.externalGetKey,\n    childrenPropName = _ref2.childrenPropName,\n    fieldNames = _ref2.fieldNames;\n  var /** @deprecated Use `config.externalGetKey` instead */\n  legacyExternalGetKey = arguments.length > 2 ? arguments[2] : undefined;\n  // Init config\n  var mergedExternalGetKey = externalGetKey || legacyExternalGetKey;\n  var posEntities = {};\n  var keyEntities = {};\n  var wrapper = {\n    posEntities: posEntities,\n    keyEntities: keyEntities\n  };\n  if (initWrapper) {\n    wrapper = initWrapper(wrapper) || wrapper;\n  }\n  traverseDataNodes(dataNodes, function (item) {\n    var node = item.node,\n      index = item.index,\n      pos = item.pos,\n      key = item.key,\n      parentPos = item.parentPos,\n      level = item.level,\n      nodes = item.nodes;\n    var entity = {\n      node: node,\n      nodes: nodes,\n      index: index,\n      key: key,\n      pos: pos,\n      level: level\n    };\n    var mergedKey = getKey(key, pos);\n    posEntities[pos] = entity;\n    keyEntities[mergedKey] = entity; // Fill children\n\n    entity.parent = posEntities[parentPos];\n    if (entity.parent) {\n      entity.parent.children = entity.parent.children || [];\n      entity.parent.children.push(entity);\n    }\n    if (processEntity) {\n      processEntity(entity, wrapper);\n    }\n  }, {\n    externalGetKey: mergedExternalGetKey,\n    childrenPropName: childrenPropName,\n    fieldNames: fieldNames\n  });\n  if (onProcessFinished) {\n    onProcessFinished(wrapper);\n  }\n  return wrapper;\n}\n/**\n * Get TreeNode props with Tree props.\n */\n\nexport function getTreeNodeProps(key, _ref3) {\n  var expandedKeys = _ref3.expandedKeys,\n    selectedKeys = _ref3.selectedKeys,\n    loadedKeys = _ref3.loadedKeys,\n    loadingKeys = _ref3.loadingKeys,\n    checkedKeys = _ref3.checkedKeys,\n    halfCheckedKeys = _ref3.halfCheckedKeys,\n    dragOverNodeKey = _ref3.dragOverNodeKey,\n    dropPosition = _ref3.dropPosition,\n    keyEntities = _ref3.keyEntities;\n  var entity = keyEntities[key];\n  var treeNodeProps = {\n    eventKey: key,\n    expanded: expandedKeys.indexOf(key) !== -1,\n    selected: selectedKeys.indexOf(key) !== -1,\n    loaded: loadedKeys.indexOf(key) !== -1,\n    loading: loadingKeys.indexOf(key) !== -1,\n    checked: checkedKeys.indexOf(key) !== -1,\n    halfChecked: halfCheckedKeys.indexOf(key) !== -1,\n    pos: String(entity ? entity.pos : ''),\n    // [Legacy] Drag props\n    // Since the interaction of drag is changed, the semantic of the props are\n    // not accuracy, I think it should be finally removed\n    dragOver: dragOverNodeKey === key && dropPosition === 0,\n    dragOverGapTop: dragOverNodeKey === key && dropPosition === -1,\n    dragOverGapBottom: dragOverNodeKey === key && dropPosition === 1\n  };\n  return treeNodeProps;\n}\nexport function convertNodePropsToEventData(props) {\n  var data = props.data,\n    expanded = props.expanded,\n    selected = props.selected,\n    checked = props.checked,\n    loaded = props.loaded,\n    loading = props.loading,\n    halfChecked = props.halfChecked,\n    dragOver = props.dragOver,\n    dragOverGapTop = props.dragOverGapTop,\n    dragOverGapBottom = props.dragOverGapBottom,\n    pos = props.pos,\n    active = props.active,\n    eventKey = props.eventKey;\n  var eventData = _objectSpread(_objectSpread({}, data), {}, {\n    expanded: expanded,\n    selected: selected,\n    checked: checked,\n    loaded: loaded,\n    loading: loading,\n    halfChecked: halfChecked,\n    dragOver: dragOver,\n    dragOverGapTop: dragOverGapTop,\n    dragOverGapBottom: dragOverGapBottom,\n    pos: pos,\n    active: active,\n    key: eventKey\n  });\n  if (!('props' in eventData)) {\n    Object.defineProperty(eventData, 'props', {\n      get: function get() {\n        warning(false, 'Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`.');\n        return props;\n      }\n    });\n  }\n  return eventData;\n}", "map": {"version": 3, "names": ["_typeof", "_toConsumableArray", "_objectSpread", "_objectWithoutProperties", "_excluded", "omit", "toArray", "warning", "getPosition", "isTreeNode", "<PERSON><PERSON><PERSON>", "key", "pos", "undefined", "fillFieldNames", "fieldNames", "_ref", "title", "_title", "children", "mergedTitle", "warningWithoutKey", "treeData", "keys", "Map", "dig", "list", "path", "arguments", "length", "for<PERSON>ach", "treeNode", "concat", "<PERSON><PERSON>ey", "String", "has", "set", "convertTreeToData", "rootNodes", "node", "treeNodes", "map", "_treeNode$props", "props", "rest", "dataNode", "parsed<PERSON><PERSON><PERSON><PERSON>", "filter", "flattenTreeData", "treeNodeList", "expandedKeys", "_fillField<PERSON><PERSON>s", "fieldTitles", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expandedKeySet", "Set", "flattenList", "parent", "index", "mergedKey", "i", "fieldTitle", "flattenNode", "data", "isStart", "isEnd", "push", "traverseDataNodes", "dataNodes", "callback", "config", "mergedConfig", "externalGetKey", "_mergedConfig", "childrenPropName", "_fillFieldNames2", "mergeChildrenPropName", "syntheticGetKey", "processNode", "pathNodes", "connectNodes", "parentPos", "level", "nodes", "subNode", "subIndex", "convertDataToEntities", "_ref2", "initWrapper", "processEntity", "onProcessFinished", "legacyExternalGetKey", "mergedExternalGetKey", "posEntities", "keyEntities", "wrapper", "item", "entity", "getTreeNodeProps", "_ref3", "<PERSON><PERSON><PERSON><PERSON>", "loadedKeys", "loadingKeys", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "dragOverNodeKey", "dropPosition", "treeNodeProps", "eventKey", "expanded", "indexOf", "selected", "loaded", "loading", "checked", "halfChecked", "dragOver", "dragOverGapTop", "dragOverGapBottom", "convertNodePropsToEventData", "active", "eventData", "Object", "defineProperty", "get"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree/es/utils/treeUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport omit from \"rc-util/es/omit\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport { getPosition, isTreeNode } from '../util';\nexport function getKey(key, pos) {\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n\n  return pos;\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n      title = _ref.title,\n      _title = _ref._title,\n      key = _ref.key,\n      children = _ref.children;\n\n  var mergedTitle = title || 'title';\n  return {\n    title: mergedTitle,\n    _title: _title || [mergedTitle],\n    key: key || 'key',\n    children: children || 'children'\n  };\n}\n/**\n * Warning if TreeNode do not provides key\n */\n\nexport function warningWithoutKey(treeData, fieldNames) {\n  var keys = new Map();\n\n  function dig(list) {\n    var path = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    (list || []).forEach(function (treeNode) {\n      var key = treeNode[fieldNames.key];\n      var children = treeNode[fieldNames.children];\n      warning(key !== null && key !== undefined, \"Tree node must have a certain key: [\".concat(path).concat(key, \"]\"));\n      var recordKey = String(key);\n      warning(!keys.has(recordKey) || key === null || key === undefined, \"Same 'key' exist in the Tree: \".concat(recordKey));\n      keys.set(recordKey, true);\n      dig(children, \"\".concat(path).concat(recordKey, \" > \"));\n    });\n  }\n\n  dig(treeData);\n}\n/**\n * Convert `children` of Tree into `treeData` structure.\n */\n\nexport function convertTreeToData(rootNodes) {\n  function dig(node) {\n    var treeNodes = toArray(node);\n    return treeNodes.map(function (treeNode) {\n      // Filter invalidate node\n      if (!isTreeNode(treeNode)) {\n        warning(!treeNode, 'Tree/TreeNode can only accept TreeNode as children.');\n        return null;\n      }\n\n      var key = treeNode.key;\n\n      var _treeNode$props = treeNode.props,\n          children = _treeNode$props.children,\n          rest = _objectWithoutProperties(_treeNode$props, _excluded);\n\n      var dataNode = _objectSpread({\n        key: key\n      }, rest);\n\n      var parsedChildren = dig(children);\n\n      if (parsedChildren.length) {\n        dataNode.children = parsedChildren;\n      }\n\n      return dataNode;\n    }).filter(function (dataNode) {\n      return dataNode;\n    });\n  }\n\n  return dig(rootNodes);\n}\n/**\n * Flat nest tree data into flatten list. This is used for virtual list render.\n * @param treeNodeList Origin data node list\n * @param expandedKeys\n * need expanded keys, provides `true` means all expanded (used in `rc-tree-select`).\n */\n\nexport function flattenTreeData(treeNodeList, expandedKeys, fieldNames) {\n  var _fillFieldNames = fillFieldNames(fieldNames),\n      fieldTitles = _fillFieldNames._title,\n      fieldKey = _fillFieldNames.key,\n      fieldChildren = _fillFieldNames.children;\n\n  var expandedKeySet = new Set(expandedKeys === true ? [] : expandedKeys);\n  var flattenList = [];\n\n  function dig(list) {\n    var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    return list.map(function (treeNode, index) {\n      var pos = getPosition(parent ? parent.pos : '0', index);\n      var mergedKey = getKey(treeNode[fieldKey], pos); // Pick matched title in field title list\n\n      var mergedTitle;\n\n      for (var i = 0; i < fieldTitles.length; i += 1) {\n        var fieldTitle = fieldTitles[i];\n\n        if (treeNode[fieldTitle] !== undefined) {\n          mergedTitle = treeNode[fieldTitle];\n          break;\n        }\n      } // Add FlattenDataNode into list\n\n\n      var flattenNode = _objectSpread(_objectSpread({}, omit(treeNode, [].concat(_toConsumableArray(fieldTitles), [fieldKey, fieldChildren]))), {}, {\n        title: mergedTitle,\n        key: mergedKey,\n        parent: parent,\n        pos: pos,\n        children: null,\n        data: treeNode,\n        isStart: [].concat(_toConsumableArray(parent ? parent.isStart : []), [index === 0]),\n        isEnd: [].concat(_toConsumableArray(parent ? parent.isEnd : []), [index === list.length - 1])\n      });\n\n      flattenList.push(flattenNode); // Loop treeNode children\n\n      if (expandedKeys === true || expandedKeySet.has(mergedKey)) {\n        flattenNode.children = dig(treeNode[fieldChildren] || [], flattenNode);\n      } else {\n        flattenNode.children = [];\n      }\n\n      return flattenNode;\n    });\n  }\n\n  dig(treeNodeList);\n  return flattenList;\n}\n/**\n * Traverse all the data by `treeData`.\n * Please not use it out of the `rc-tree` since we may refactor this code.\n */\n\nexport function traverseDataNodes(dataNodes, callback, // To avoid too many params, let use config instead of origin param\nconfig) {\n  var mergedConfig = {};\n\n  if (_typeof(config) === 'object') {\n    mergedConfig = config;\n  } else {\n    mergedConfig = {\n      externalGetKey: config\n    };\n  }\n\n  mergedConfig = mergedConfig || {}; // Init config\n\n  var _mergedConfig = mergedConfig,\n      childrenPropName = _mergedConfig.childrenPropName,\n      externalGetKey = _mergedConfig.externalGetKey,\n      fieldNames = _mergedConfig.fieldNames;\n\n  var _fillFieldNames2 = fillFieldNames(fieldNames),\n      fieldKey = _fillFieldNames2.key,\n      fieldChildren = _fillFieldNames2.children;\n\n  var mergeChildrenPropName = childrenPropName || fieldChildren; // Get keys\n\n  var syntheticGetKey;\n\n  if (externalGetKey) {\n    if (typeof externalGetKey === 'string') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return node[externalGetKey];\n      };\n    } else if (typeof externalGetKey === 'function') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return externalGetKey(node);\n      };\n    }\n  } else {\n    syntheticGetKey = function syntheticGetKey(node, pos) {\n      return getKey(node[fieldKey], pos);\n    };\n  } // Process\n\n\n  function processNode(node, index, parent, pathNodes) {\n    var children = node ? node[mergeChildrenPropName] : dataNodes;\n    var pos = node ? getPosition(parent.pos, index) : '0';\n    var connectNodes = node ? [].concat(_toConsumableArray(pathNodes), [node]) : []; // Process node if is not root\n\n    if (node) {\n      var key = syntheticGetKey(node, pos);\n      var data = {\n        node: node,\n        index: index,\n        pos: pos,\n        key: key,\n        parentPos: parent.node ? parent.pos : null,\n        level: parent.level + 1,\n        nodes: connectNodes\n      };\n      callback(data);\n    } // Process children node\n\n\n    if (children) {\n      children.forEach(function (subNode, subIndex) {\n        processNode(subNode, subIndex, {\n          node: node,\n          pos: pos,\n          level: parent ? parent.level + 1 : -1\n        }, connectNodes);\n      });\n    }\n  }\n\n  processNode(null);\n}\n/**\n * Convert `treeData` into entity records.\n */\n\nexport function convertDataToEntities(dataNodes) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      initWrapper = _ref2.initWrapper,\n      processEntity = _ref2.processEntity,\n      onProcessFinished = _ref2.onProcessFinished,\n      externalGetKey = _ref2.externalGetKey,\n      childrenPropName = _ref2.childrenPropName,\n      fieldNames = _ref2.fieldNames;\n\n  var\n  /** @deprecated Use `config.externalGetKey` instead */\n  legacyExternalGetKey = arguments.length > 2 ? arguments[2] : undefined;\n  // Init config\n  var mergedExternalGetKey = externalGetKey || legacyExternalGetKey;\n  var posEntities = {};\n  var keyEntities = {};\n  var wrapper = {\n    posEntities: posEntities,\n    keyEntities: keyEntities\n  };\n\n  if (initWrapper) {\n    wrapper = initWrapper(wrapper) || wrapper;\n  }\n\n  traverseDataNodes(dataNodes, function (item) {\n    var node = item.node,\n        index = item.index,\n        pos = item.pos,\n        key = item.key,\n        parentPos = item.parentPos,\n        level = item.level,\n        nodes = item.nodes;\n    var entity = {\n      node: node,\n      nodes: nodes,\n      index: index,\n      key: key,\n      pos: pos,\n      level: level\n    };\n    var mergedKey = getKey(key, pos);\n    posEntities[pos] = entity;\n    keyEntities[mergedKey] = entity; // Fill children\n\n    entity.parent = posEntities[parentPos];\n\n    if (entity.parent) {\n      entity.parent.children = entity.parent.children || [];\n      entity.parent.children.push(entity);\n    }\n\n    if (processEntity) {\n      processEntity(entity, wrapper);\n    }\n  }, {\n    externalGetKey: mergedExternalGetKey,\n    childrenPropName: childrenPropName,\n    fieldNames: fieldNames\n  });\n\n  if (onProcessFinished) {\n    onProcessFinished(wrapper);\n  }\n\n  return wrapper;\n}\n/**\n * Get TreeNode props with Tree props.\n */\n\nexport function getTreeNodeProps(key, _ref3) {\n  var expandedKeys = _ref3.expandedKeys,\n      selectedKeys = _ref3.selectedKeys,\n      loadedKeys = _ref3.loadedKeys,\n      loadingKeys = _ref3.loadingKeys,\n      checkedKeys = _ref3.checkedKeys,\n      halfCheckedKeys = _ref3.halfCheckedKeys,\n      dragOverNodeKey = _ref3.dragOverNodeKey,\n      dropPosition = _ref3.dropPosition,\n      keyEntities = _ref3.keyEntities;\n  var entity = keyEntities[key];\n  var treeNodeProps = {\n    eventKey: key,\n    expanded: expandedKeys.indexOf(key) !== -1,\n    selected: selectedKeys.indexOf(key) !== -1,\n    loaded: loadedKeys.indexOf(key) !== -1,\n    loading: loadingKeys.indexOf(key) !== -1,\n    checked: checkedKeys.indexOf(key) !== -1,\n    halfChecked: halfCheckedKeys.indexOf(key) !== -1,\n    pos: String(entity ? entity.pos : ''),\n    // [Legacy] Drag props\n    // Since the interaction of drag is changed, the semantic of the props are\n    // not accuracy, I think it should be finally removed\n    dragOver: dragOverNodeKey === key && dropPosition === 0,\n    dragOverGapTop: dragOverNodeKey === key && dropPosition === -1,\n    dragOverGapBottom: dragOverNodeKey === key && dropPosition === 1\n  };\n  return treeNodeProps;\n}\nexport function convertNodePropsToEventData(props) {\n  var data = props.data,\n      expanded = props.expanded,\n      selected = props.selected,\n      checked = props.checked,\n      loaded = props.loaded,\n      loading = props.loading,\n      halfChecked = props.halfChecked,\n      dragOver = props.dragOver,\n      dragOverGapTop = props.dragOverGapTop,\n      dragOverGapBottom = props.dragOverGapBottom,\n      pos = props.pos,\n      active = props.active,\n      eventKey = props.eventKey;\n\n  var eventData = _objectSpread(_objectSpread({}, data), {}, {\n    expanded: expanded,\n    selected: selected,\n    checked: checked,\n    loaded: loaded,\n    loading: loading,\n    halfChecked: halfChecked,\n    dragOver: dragOver,\n    dragOverGapTop: dragOverGapTop,\n    dragOverGapBottom: dragOverGapBottom,\n    pos: pos,\n    active: active,\n    key: eventKey\n  });\n\n  if (!('props' in eventData)) {\n    Object.defineProperty(eventData, 'props', {\n      get: function get() {\n        warning(false, 'Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`.');\n        return props;\n      }\n    });\n  }\n\n  return eventData;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,WAAW,EAAEC,UAAU,QAAQ,SAAS;AACjD,OAAO,SAASC,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAC/B,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACrC,OAAOF,GAAG;EACZ;EAEA,OAAOC,GAAG;AACZ;AACA,OAAO,SAASE,cAAcA,CAACC,UAAU,EAAE;EACzC,IAAIC,IAAI,GAAGD,UAAU,IAAI,CAAC,CAAC;IACvBE,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBP,GAAG,GAAGK,IAAI,CAACL,GAAG;IACdQ,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAE5B,IAAIC,WAAW,GAAGH,KAAK,IAAI,OAAO;EAClC,OAAO;IACLA,KAAK,EAAEG,WAAW;IAClBF,MAAM,EAAEA,MAAM,IAAI,CAACE,WAAW,CAAC;IAC/BT,GAAG,EAAEA,GAAG,IAAI,KAAK;IACjBQ,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC;AACH;AACA;AACA;AACA;;AAEA,OAAO,SAASE,iBAAiBA,CAACC,QAAQ,EAAEP,UAAU,EAAE;EACtD,IAAIQ,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;EAEpB,SAASC,GAAGA,CAACC,IAAI,EAAE;IACjB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACjF,CAACF,IAAI,IAAI,EAAE,EAAEI,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACvC,IAAIpB,GAAG,GAAGoB,QAAQ,CAAChB,UAAU,CAACJ,GAAG,CAAC;MAClC,IAAIQ,QAAQ,GAAGY,QAAQ,CAAChB,UAAU,CAACI,QAAQ,CAAC;MAC5CZ,OAAO,CAACI,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE,sCAAsC,CAACmB,MAAM,CAACL,IAAI,CAAC,CAACK,MAAM,CAACrB,GAAG,EAAE,GAAG,CAAC,CAAC;MAChH,IAAIsB,SAAS,GAAGC,MAAM,CAACvB,GAAG,CAAC;MAC3BJ,OAAO,CAAC,CAACgB,IAAI,CAACY,GAAG,CAACF,SAAS,CAAC,IAAItB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE,gCAAgC,CAACmB,MAAM,CAACC,SAAS,CAAC,CAAC;MACtHV,IAAI,CAACa,GAAG,CAACH,SAAS,EAAE,IAAI,CAAC;MACzBR,GAAG,CAACN,QAAQ,EAAE,EAAE,CAACa,MAAM,CAACL,IAAI,CAAC,CAACK,MAAM,CAACC,SAAS,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC,CAAC;EACJ;EAEAR,GAAG,CAACH,QAAQ,CAAC;AACf;AACA;AACA;AACA;;AAEA,OAAO,SAASe,iBAAiBA,CAACC,SAAS,EAAE;EAC3C,SAASb,GAAGA,CAACc,IAAI,EAAE;IACjB,IAAIC,SAAS,GAAGlC,OAAO,CAACiC,IAAI,CAAC;IAC7B,OAAOC,SAAS,CAACC,GAAG,CAAC,UAAUV,QAAQ,EAAE;MACvC;MACA,IAAI,CAACtB,UAAU,CAACsB,QAAQ,CAAC,EAAE;QACzBxB,OAAO,CAAC,CAACwB,QAAQ,EAAE,qDAAqD,CAAC;QACzE,OAAO,IAAI;MACb;MAEA,IAAIpB,GAAG,GAAGoB,QAAQ,CAACpB,GAAG;MAEtB,IAAI+B,eAAe,GAAGX,QAAQ,CAACY,KAAK;QAChCxB,QAAQ,GAAGuB,eAAe,CAACvB,QAAQ;QACnCyB,IAAI,GAAGzC,wBAAwB,CAACuC,eAAe,EAAEtC,SAAS,CAAC;MAE/D,IAAIyC,QAAQ,GAAG3C,aAAa,CAAC;QAC3BS,GAAG,EAAEA;MACP,CAAC,EAAEiC,IAAI,CAAC;MAER,IAAIE,cAAc,GAAGrB,GAAG,CAACN,QAAQ,CAAC;MAElC,IAAI2B,cAAc,CAACjB,MAAM,EAAE;QACzBgB,QAAQ,CAAC1B,QAAQ,GAAG2B,cAAc;MACpC;MAEA,OAAOD,QAAQ;IACjB,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUF,QAAQ,EAAE;MAC5B,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOpB,GAAG,CAACa,SAAS,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASU,eAAeA,CAACC,YAAY,EAAEC,YAAY,EAAEnC,UAAU,EAAE;EACtE,IAAIoC,eAAe,GAAGrC,cAAc,CAACC,UAAU,CAAC;IAC5CqC,WAAW,GAAGD,eAAe,CAACjC,MAAM;IACpCmC,QAAQ,GAAGF,eAAe,CAACxC,GAAG;IAC9B2C,aAAa,GAAGH,eAAe,CAAChC,QAAQ;EAE5C,IAAIoC,cAAc,GAAG,IAAIC,GAAG,CAACN,YAAY,KAAK,IAAI,GAAG,EAAE,GAAGA,YAAY,CAAC;EACvE,IAAIO,WAAW,GAAG,EAAE;EAEpB,SAAShC,GAAGA,CAACC,IAAI,EAAE;IACjB,IAAIgC,MAAM,GAAG9B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACrF,OAAOF,IAAI,CAACe,GAAG,CAAC,UAAUV,QAAQ,EAAE4B,KAAK,EAAE;MACzC,IAAI/C,GAAG,GAAGJ,WAAW,CAACkD,MAAM,GAAGA,MAAM,CAAC9C,GAAG,GAAG,GAAG,EAAE+C,KAAK,CAAC;MACvD,IAAIC,SAAS,GAAGlD,MAAM,CAACqB,QAAQ,CAACsB,QAAQ,CAAC,EAAEzC,GAAG,CAAC,CAAC,CAAC;;MAEjD,IAAIQ,WAAW;MAEf,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,WAAW,CAACvB,MAAM,EAAEgC,CAAC,IAAI,CAAC,EAAE;QAC9C,IAAIC,UAAU,GAAGV,WAAW,CAACS,CAAC,CAAC;QAE/B,IAAI9B,QAAQ,CAAC+B,UAAU,CAAC,KAAKjD,SAAS,EAAE;UACtCO,WAAW,GAAGW,QAAQ,CAAC+B,UAAU,CAAC;UAClC;QACF;MACF,CAAC,CAAC;;MAGF,IAAIC,WAAW,GAAG7D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEG,IAAI,CAAC0B,QAAQ,EAAE,EAAE,CAACC,MAAM,CAAC/B,kBAAkB,CAACmD,WAAW,CAAC,EAAE,CAACC,QAAQ,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5IrC,KAAK,EAAEG,WAAW;QAClBT,GAAG,EAAEiD,SAAS;QACdF,MAAM,EAAEA,MAAM;QACd9C,GAAG,EAAEA,GAAG;QACRO,QAAQ,EAAE,IAAI;QACd6C,IAAI,EAAEjC,QAAQ;QACdkC,OAAO,EAAE,EAAE,CAACjC,MAAM,CAAC/B,kBAAkB,CAACyD,MAAM,GAAGA,MAAM,CAACO,OAAO,GAAG,EAAE,CAAC,EAAE,CAACN,KAAK,KAAK,CAAC,CAAC,CAAC;QACnFO,KAAK,EAAE,EAAE,CAAClC,MAAM,CAAC/B,kBAAkB,CAACyD,MAAM,GAAGA,MAAM,CAACQ,KAAK,GAAG,EAAE,CAAC,EAAE,CAACP,KAAK,KAAKjC,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;MAC9F,CAAC,CAAC;MAEF4B,WAAW,CAACU,IAAI,CAACJ,WAAW,CAAC,CAAC,CAAC;;MAE/B,IAAIb,YAAY,KAAK,IAAI,IAAIK,cAAc,CAACpB,GAAG,CAACyB,SAAS,CAAC,EAAE;QAC1DG,WAAW,CAAC5C,QAAQ,GAAGM,GAAG,CAACM,QAAQ,CAACuB,aAAa,CAAC,IAAI,EAAE,EAAES,WAAW,CAAC;MACxE,CAAC,MAAM;QACLA,WAAW,CAAC5C,QAAQ,GAAG,EAAE;MAC3B;MAEA,OAAO4C,WAAW;IACpB,CAAC,CAAC;EACJ;EAEAtC,GAAG,CAACwB,YAAY,CAAC;EACjB,OAAOQ,WAAW;AACpB;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASW,iBAAiBA,CAACC,SAAS,EAAEC,QAAQ;AAAE;AACvDC,MAAM,EAAE;EACN,IAAIC,YAAY,GAAG,CAAC,CAAC;EAErB,IAAIxE,OAAO,CAACuE,MAAM,CAAC,KAAK,QAAQ,EAAE;IAChCC,YAAY,GAAGD,MAAM;EACvB,CAAC,MAAM;IACLC,YAAY,GAAG;MACbC,cAAc,EAAEF;IAClB,CAAC;EACH;EAEAC,YAAY,GAAGA,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEnC,IAAIE,aAAa,GAAGF,YAAY;IAC5BG,gBAAgB,GAAGD,aAAa,CAACC,gBAAgB;IACjDF,cAAc,GAAGC,aAAa,CAACD,cAAc;IAC7C1D,UAAU,GAAG2D,aAAa,CAAC3D,UAAU;EAEzC,IAAI6D,gBAAgB,GAAG9D,cAAc,CAACC,UAAU,CAAC;IAC7CsC,QAAQ,GAAGuB,gBAAgB,CAACjE,GAAG;IAC/B2C,aAAa,GAAGsB,gBAAgB,CAACzD,QAAQ;EAE7C,IAAI0D,qBAAqB,GAAGF,gBAAgB,IAAIrB,aAAa,CAAC,CAAC;;EAE/D,IAAIwB,eAAe;EAEnB,IAAIL,cAAc,EAAE;IAClB,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;MACtCK,eAAe,GAAG,SAASA,eAAeA,CAACvC,IAAI,EAAE;QAC/C,OAAOA,IAAI,CAACkC,cAAc,CAAC;MAC7B,CAAC;IACH,CAAC,MAAM,IAAI,OAAOA,cAAc,KAAK,UAAU,EAAE;MAC/CK,eAAe,GAAG,SAASA,eAAeA,CAACvC,IAAI,EAAE;QAC/C,OAAOkC,cAAc,CAAClC,IAAI,CAAC;MAC7B,CAAC;IACH;EACF,CAAC,MAAM;IACLuC,eAAe,GAAG,SAASA,eAAeA,CAACvC,IAAI,EAAE3B,GAAG,EAAE;MACpD,OAAOF,MAAM,CAAC6B,IAAI,CAACc,QAAQ,CAAC,EAAEzC,GAAG,CAAC;IACpC,CAAC;EACH,CAAC,CAAC;;EAGF,SAASmE,WAAWA,CAACxC,IAAI,EAAEoB,KAAK,EAAED,MAAM,EAAEsB,SAAS,EAAE;IACnD,IAAI7D,QAAQ,GAAGoB,IAAI,GAAGA,IAAI,CAACsC,qBAAqB,CAAC,GAAGR,SAAS;IAC7D,IAAIzD,GAAG,GAAG2B,IAAI,GAAG/B,WAAW,CAACkD,MAAM,CAAC9C,GAAG,EAAE+C,KAAK,CAAC,GAAG,GAAG;IACrD,IAAIsB,YAAY,GAAG1C,IAAI,GAAG,EAAE,CAACP,MAAM,CAAC/B,kBAAkB,CAAC+E,SAAS,CAAC,EAAE,CAACzC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;;IAEjF,IAAIA,IAAI,EAAE;MACR,IAAI5B,GAAG,GAAGmE,eAAe,CAACvC,IAAI,EAAE3B,GAAG,CAAC;MACpC,IAAIoD,IAAI,GAAG;QACTzB,IAAI,EAAEA,IAAI;QACVoB,KAAK,EAAEA,KAAK;QACZ/C,GAAG,EAAEA,GAAG;QACRD,GAAG,EAAEA,GAAG;QACRuE,SAAS,EAAExB,MAAM,CAACnB,IAAI,GAAGmB,MAAM,CAAC9C,GAAG,GAAG,IAAI;QAC1CuE,KAAK,EAAEzB,MAAM,CAACyB,KAAK,GAAG,CAAC;QACvBC,KAAK,EAAEH;MACT,CAAC;MACDX,QAAQ,CAACN,IAAI,CAAC;IAChB,CAAC,CAAC;;IAGF,IAAI7C,QAAQ,EAAE;MACZA,QAAQ,CAACW,OAAO,CAAC,UAAUuD,OAAO,EAAEC,QAAQ,EAAE;QAC5CP,WAAW,CAACM,OAAO,EAAEC,QAAQ,EAAE;UAC7B/C,IAAI,EAAEA,IAAI;UACV3B,GAAG,EAAEA,GAAG;UACRuE,KAAK,EAAEzB,MAAM,GAAGA,MAAM,CAACyB,KAAK,GAAG,CAAC,GAAG,CAAC;QACtC,CAAC,EAAEF,YAAY,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;EAEAF,WAAW,CAAC,IAAI,CAAC;AACnB;AACA;AACA;AACA;;AAEA,OAAO,SAASQ,qBAAqBA,CAAClB,SAAS,EAAE;EAC/C,IAAImB,KAAK,GAAG5D,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9E6D,WAAW,GAAGD,KAAK,CAACC,WAAW;IAC/BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,iBAAiB,GAAGH,KAAK,CAACG,iBAAiB;IAC3ClB,cAAc,GAAGe,KAAK,CAACf,cAAc;IACrCE,gBAAgB,GAAGa,KAAK,CAACb,gBAAgB;IACzC5D,UAAU,GAAGyE,KAAK,CAACzE,UAAU;EAEjC,IACA;EACA6E,oBAAoB,GAAGhE,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGf,SAAS;EACtE;EACA,IAAIgF,oBAAoB,GAAGpB,cAAc,IAAImB,oBAAoB;EACjE,IAAIE,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,OAAO,GAAG;IACZF,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA;EACf,CAAC;EAED,IAAIN,WAAW,EAAE;IACfO,OAAO,GAAGP,WAAW,CAACO,OAAO,CAAC,IAAIA,OAAO;EAC3C;EAEA5B,iBAAiB,CAACC,SAAS,EAAE,UAAU4B,IAAI,EAAE;IAC3C,IAAI1D,IAAI,GAAG0D,IAAI,CAAC1D,IAAI;MAChBoB,KAAK,GAAGsC,IAAI,CAACtC,KAAK;MAClB/C,GAAG,GAAGqF,IAAI,CAACrF,GAAG;MACdD,GAAG,GAAGsF,IAAI,CAACtF,GAAG;MACduE,SAAS,GAAGe,IAAI,CAACf,SAAS;MAC1BC,KAAK,GAAGc,IAAI,CAACd,KAAK;MAClBC,KAAK,GAAGa,IAAI,CAACb,KAAK;IACtB,IAAIc,MAAM,GAAG;MACX3D,IAAI,EAAEA,IAAI;MACV6C,KAAK,EAAEA,KAAK;MACZzB,KAAK,EAAEA,KAAK;MACZhD,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA,GAAG;MACRuE,KAAK,EAAEA;IACT,CAAC;IACD,IAAIvB,SAAS,GAAGlD,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC;IAChCkF,WAAW,CAAClF,GAAG,CAAC,GAAGsF,MAAM;IACzBH,WAAW,CAACnC,SAAS,CAAC,GAAGsC,MAAM,CAAC,CAAC;;IAEjCA,MAAM,CAACxC,MAAM,GAAGoC,WAAW,CAACZ,SAAS,CAAC;IAEtC,IAAIgB,MAAM,CAACxC,MAAM,EAAE;MACjBwC,MAAM,CAACxC,MAAM,CAACvC,QAAQ,GAAG+E,MAAM,CAACxC,MAAM,CAACvC,QAAQ,IAAI,EAAE;MACrD+E,MAAM,CAACxC,MAAM,CAACvC,QAAQ,CAACgD,IAAI,CAAC+B,MAAM,CAAC;IACrC;IAEA,IAAIR,aAAa,EAAE;MACjBA,aAAa,CAACQ,MAAM,EAAEF,OAAO,CAAC;IAChC;EACF,CAAC,EAAE;IACDvB,cAAc,EAAEoB,oBAAoB;IACpClB,gBAAgB,EAAEA,gBAAgB;IAClC5D,UAAU,EAAEA;EACd,CAAC,CAAC;EAEF,IAAI4E,iBAAiB,EAAE;IACrBA,iBAAiB,CAACK,OAAO,CAAC;EAC5B;EAEA,OAAOA,OAAO;AAChB;AACA;AACA;AACA;;AAEA,OAAO,SAASG,gBAAgBA,CAACxF,GAAG,EAAEyF,KAAK,EAAE;EAC3C,IAAIlD,YAAY,GAAGkD,KAAK,CAAClD,YAAY;IACjCmD,YAAY,GAAGD,KAAK,CAACC,YAAY;IACjCC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,eAAe,GAAGL,KAAK,CAACK,eAAe;IACvCC,eAAe,GAAGN,KAAK,CAACM,eAAe;IACvCC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCZ,WAAW,GAAGK,KAAK,CAACL,WAAW;EACnC,IAAIG,MAAM,GAAGH,WAAW,CAACpF,GAAG,CAAC;EAC7B,IAAIiG,aAAa,GAAG;IAClBC,QAAQ,EAAElG,GAAG;IACbmG,QAAQ,EAAE5D,YAAY,CAAC6D,OAAO,CAACpG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1CqG,QAAQ,EAAEX,YAAY,CAACU,OAAO,CAACpG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1CsG,MAAM,EAAEX,UAAU,CAACS,OAAO,CAACpG,GAAG,CAAC,KAAK,CAAC,CAAC;IACtCuG,OAAO,EAAEX,WAAW,CAACQ,OAAO,CAACpG,GAAG,CAAC,KAAK,CAAC,CAAC;IACxCwG,OAAO,EAAEX,WAAW,CAACO,OAAO,CAACpG,GAAG,CAAC,KAAK,CAAC,CAAC;IACxCyG,WAAW,EAAEX,eAAe,CAACM,OAAO,CAACpG,GAAG,CAAC,KAAK,CAAC,CAAC;IAChDC,GAAG,EAAEsB,MAAM,CAACgE,MAAM,GAAGA,MAAM,CAACtF,GAAG,GAAG,EAAE,CAAC;IACrC;IACA;IACA;IACAyG,QAAQ,EAAEX,eAAe,KAAK/F,GAAG,IAAIgG,YAAY,KAAK,CAAC;IACvDW,cAAc,EAAEZ,eAAe,KAAK/F,GAAG,IAAIgG,YAAY,KAAK,CAAC,CAAC;IAC9DY,iBAAiB,EAAEb,eAAe,KAAK/F,GAAG,IAAIgG,YAAY,KAAK;EACjE,CAAC;EACD,OAAOC,aAAa;AACtB;AACA,OAAO,SAASY,2BAA2BA,CAAC7E,KAAK,EAAE;EACjD,IAAIqB,IAAI,GAAGrB,KAAK,CAACqB,IAAI;IACjB8C,QAAQ,GAAGnE,KAAK,CAACmE,QAAQ;IACzBE,QAAQ,GAAGrE,KAAK,CAACqE,QAAQ;IACzBG,OAAO,GAAGxE,KAAK,CAACwE,OAAO;IACvBF,MAAM,GAAGtE,KAAK,CAACsE,MAAM;IACrBC,OAAO,GAAGvE,KAAK,CAACuE,OAAO;IACvBE,WAAW,GAAGzE,KAAK,CAACyE,WAAW;IAC/BC,QAAQ,GAAG1E,KAAK,CAAC0E,QAAQ;IACzBC,cAAc,GAAG3E,KAAK,CAAC2E,cAAc;IACrCC,iBAAiB,GAAG5E,KAAK,CAAC4E,iBAAiB;IAC3C3G,GAAG,GAAG+B,KAAK,CAAC/B,GAAG;IACf6G,MAAM,GAAG9E,KAAK,CAAC8E,MAAM;IACrBZ,QAAQ,GAAGlE,KAAK,CAACkE,QAAQ;EAE7B,IAAIa,SAAS,GAAGxH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IACzD8C,QAAQ,EAAEA,QAAQ;IAClBE,QAAQ,EAAEA,QAAQ;IAClBG,OAAO,EAAEA,OAAO;IAChBF,MAAM,EAAEA,MAAM;IACdC,OAAO,EAAEA,OAAO;IAChBE,WAAW,EAAEA,WAAW;IACxBC,QAAQ,EAAEA,QAAQ;IAClBC,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpC3G,GAAG,EAAEA,GAAG;IACR6G,MAAM,EAAEA,MAAM;IACd9G,GAAG,EAAEkG;EACP,CAAC,CAAC;EAEF,IAAI,EAAE,OAAO,IAAIa,SAAS,CAAC,EAAE;IAC3BC,MAAM,CAACC,cAAc,CAACF,SAAS,EAAE,OAAO,EAAE;MACxCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClBtH,OAAO,CAAC,KAAK,EAAE,uIAAuI,CAAC;QACvJ,OAAOoC,KAAK;MACd;IACF,CAAC,CAAC;EACJ;EAEA,OAAO+E,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}