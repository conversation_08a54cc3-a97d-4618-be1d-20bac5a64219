{"ast": null, "code": "import * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../../../input';\nvar FilterSearch = function FilterSearch(_ref) {\n  var value = _ref.value,\n    onChange = _ref.onChange,\n    filterSearch = _ref.filterSearch,\n    tablePrefixCls = _ref.tablePrefixCls,\n    locale = _ref.locale;\n  if (!filterSearch) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(tablePrefixCls, \"-filter-dropdown-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null),\n    placeholder: locale.filterSearchPlaceholder,\n    onChange: onChange,\n    value: value // for skip min-width of input\n    ,\n\n    htmlSize: 1,\n    className: \"\".concat(tablePrefixCls, \"-filter-dropdown-search-input\")\n  }));\n};\nexport default FilterSearch;", "map": {"version": 3, "names": ["React", "SearchOutlined", "Input", "FilterSearch", "_ref", "value", "onChange", "filterSearch", "tablePrefixCls", "locale", "createElement", "className", "concat", "prefix", "placeholder", "filterSearchPlaceholder", "htmlSize"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/hooks/useFilter/FilterSearch.js"], "sourcesContent": ["import * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../../../input';\n\nvar FilterSearch = function FilterSearch(_ref) {\n  var value = _ref.value,\n      onChange = _ref.onChange,\n      filterSearch = _ref.filterSearch,\n      tablePrefixCls = _ref.tablePrefixCls,\n      locale = _ref.locale;\n\n  if (!filterSearch) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(tablePrefixCls, \"-filter-dropdown-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null),\n    placeholder: locale.filterSearchPlaceholder,\n    onChange: onChange,\n    value: value // for skip min-width of input\n    ,\n    htmlSize: 1,\n    className: \"\".concat(tablePrefixCls, \"-filter-dropdown-search-input\")\n  }));\n};\n\nexport default FilterSearch;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,KAAK,MAAM,gBAAgB;AAElC,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,YAAY,GAAGH,IAAI,CAACG,YAAY;IAChCC,cAAc,GAAGJ,IAAI,CAACI,cAAc;IACpCC,MAAM,GAAGL,IAAI,CAACK,MAAM;EAExB,IAAI,CAACF,YAAY,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,OAAO,aAAaP,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,cAAc,EAAE,yBAAyB;EAChE,CAAC,EAAE,aAAaR,KAAK,CAACU,aAAa,CAACR,KAAK,EAAE;IACzCW,MAAM,EAAE,aAAab,KAAK,CAACU,aAAa,CAACT,cAAc,EAAE,IAAI,CAAC;IAC9Da,WAAW,EAAEL,MAAM,CAACM,uBAAuB;IAC3CT,QAAQ,EAAEA,QAAQ;IAClBD,KAAK,EAAEA,KAAK,CAAC;IAAA;;IAEbW,QAAQ,EAAE,CAAC;IACXL,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,cAAc,EAAE,+BAA+B;EACtE,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}