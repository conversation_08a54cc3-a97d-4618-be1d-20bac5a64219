{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiAnagrafica.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiAnagrafica = props => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState('');\n  const [results2, setResults2] = useState('');\n  const [results3, setResults3] = useState('');\n  const [results4, setResults4] = useState('');\n  const [results5, setResults5] = useState('');\n  const [results6, setResults6] = useState('');\n  const [results7, setResults7] = useState('');\n  const [results8, setResults8] = useState('');\n  const [results9, setResults9] = useState('');\n  const [results10, setResults10] = useState(null);\n  const [modPag, setModPag] = useState(null);\n  const [isLookingUp, setIsLookingUp] = useState(false);\n  const toast = useRef(null);\n  //Setto le variabili di stato con il valore di props mediante useEffect\n  useEffect(() => {\n    async function renderString() {\n      if (props.result !== undefined) {\n        setResults(props.result.firstName);\n        setResults2(props.result.idRetailer.idRegistry.email);\n        setResults3(props.result.idRetailer.idRegistry.tel);\n        setResults4(props.result.idRetailer.idRegistry.pIva);\n        setResults5(props.result.idRetailer.idRegistry.address);\n        setResults6(props.result.idRetailer.idRegistry.city);\n        setResults7(props.result.idRetailer.idRegistry.cap);\n        setResults8(props.result.idRetailer.idRegistry.lastName);\n        setResults9(props.result.idRetailer.idRegistry.tel);\n        setResults10(props.result.idRetailer.idRegistry.paymentMetod);\n      }\n      //Chiamata axios per la visualizzazione dei registry\n      await APIRequest('GET', 'paymentmethods/').then(res => {\n        var pm = [];\n        res.data.forEach(element => {\n          var x = {\n            name: element.description,\n            code: element.description\n          };\n          pm.push(x);\n        });\n        setModPag(pm);\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    renderString();\n  }, [props.result]);\n  // Funzione di validazione\n  const validateForm = () => {\n    const errors = [];\n\n    // Validazione campi obbligatori\n    if (!results || results.trim() === '') {\n      errors.push('📝 Nome è obbligatorio');\n    }\n    if (!results8 || results8.trim() === '') {\n      errors.push('📝 Cognome è obbligatorio');\n    }\n    if (!results4 || results4.trim() === '') {\n      errors.push('📝 Partita IVA è obbligatoria');\n    }\n\n    // Validazione formato P.IVA (11 cifre)\n    if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n    }\n\n    // Validazione email obbligatoria\n    if (!results2 || results2.trim() === '') {\n      errors.push('📧 Email è obbligatoria');\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n      errors.push('📧 Indirizzo email non valido');\n    }\n\n    // Validazione contatti: almeno uno tra telefono e cellulare\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (!hasTelefono && !hasCellulare) {\n      errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n    }\n\n    // Validazione formato telefono (se presente)\n    if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n      errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n    }\n\n    // Validazione formato cellulare (se presente)\n    if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n      errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n    }\n    return errors;\n  };\n\n  // Funzione per il lookup automatico tramite P.IVA\n  const lookupPIva = async () => {\n    if (!results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ P.IVA non valida',\n        detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',\n        life: 4000\n      });\n      return;\n    }\n    setIsLookingUp(true);\n    try {\n      // Chiamata al backend per il lookup P.IVA\n      const response = await APIRequest('GET', \"registry/lookup-piva/\".concat(results4.replace(/\\s/g, '')));\n      if (response.data) {\n        // Popola automaticamente i campi con i dati trovati\n        const data = response.data;\n        if (data.firstName) setResults(data.firstName);\n        if (data.lastName) setResults8(data.lastName);\n        if (data.email) setResults2(data.email);\n        if (data.tel) setResults3(data.tel);\n        if (data.cellnum) setResults9(data.cellnum);\n        if (data.address) setResults5(data.address);\n        if (data.city) setResults6(data.city);\n        if (data.cap) setResults7(data.cap);\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Dati trovati',\n          detail: 'I dati aziendali sono stati recuperati automaticamente. Verificare e completare le informazioni mancanti.',\n          life: 5000\n        });\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('❌ Errore lookup P.IVA:', error);\n      const errorStatus = (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message;\n      if (errorStatus === 404) {\n        toast.current.show({\n          severity: 'info',\n          summary: '🔍 Nessun risultato',\n          detail: 'Nessuna azienda trovata con questa Partita IVA. Compilare manualmente i campi.',\n          life: 4000\n        });\n      } else if (errorStatus === 503) {\n        toast.current.show({\n          severity: 'warn',\n          summary: '⏱️ Servizio non disponibile',\n          detail: 'Il servizio di lookup P.IVA è temporaneamente non disponibile. Compilare manualmente i campi.',\n          life: 5000\n        });\n      } else {\n        toast.current.show({\n          severity: 'warn',\n          summary: '⚠️ Lookup non disponibile',\n          detail: 'Il servizio di ricerca automatica non è al momento disponibile. Compilare manualmente i campi.',\n          life: 4000\n        });\n      }\n    } finally {\n      setIsLookingUp(false);\n    }\n  };\n  const Invia = async () => {\n    // Validazione frontend\n    const validationErrors = validateForm();\n    if (validationErrors.length > 0) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ Dati mancanti o non validi',\n        detail: validationErrors.join('. '),\n        life: 5000\n      });\n      return;\n    }\n\n    // Verifica che tutti i campi obbligatori siano compilati\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {\n      var corpo = {\n        firstName: results,\n        lastName: results8,\n        email: results2,\n        telnum: results3,\n        cellnum: results9,\n        pIva: results4,\n        address: results5,\n        city: results6,\n        cap: results7,\n        paymentMetod: (results10 === null || results10 === void 0 ? void 0 : results10.name) || ''\n      };\n      //Chiamata axios per la creazione del registry\n      await APIRequest('POST', 'registry/', corpo).then(res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Successo',\n          detail: \"L'anagrafica è stata inserita con successo\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      }).catch(e => {\n        var _e$response, _e$response2, _e$response3, _e$response3$data;\n        console.error('❌ Errore creazione anagrafica:', e);\n\n        // Gestione specifica degli errori\n        const errorStatus = (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status;\n        const errorData = (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data;\n        const errorMessage = ((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : (_e$response3$data = _e$response3.data) === null || _e$response3$data === void 0 ? void 0 : _e$response3$data.message) || e.message;\n        let userMessage = '';\n        let summary = 'Errore';\n        if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') || errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n          // Violazione unique constraint (P.IVA duplicata)\n          summary = '⚠️ Partita IVA già presente';\n          userMessage = \"La Partita IVA \\\"\".concat(results4, \"\\\" \\xE8 gi\\xE0 registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.\");\n        } else if (errorStatus === 400) {\n          // Errori di validazione\n          summary = '📝 Dati non validi';\n          if (errorMessage.toLowerCase().includes('email')) {\n            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n          } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n          } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n            userMessage = 'Il numero di telefono inserito non è valido.';\n          } else {\n            userMessage = \"Dati inseriti non validi: \".concat(errorMessage);\n          }\n        } else if (errorStatus === 422) {\n          // Errori di business logic\n          summary = '🚫 Operazione non consentita';\n          userMessage = \"Impossibile completare l'operazione: \".concat(errorMessage);\n        } else if (errorStatus === 500 || errorStatus === 501) {\n          // Errori del server\n          summary = '🔧 Errore del server';\n          userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n        } else if (errorStatus === 503) {\n          // Servizio non disponibile\n          summary = '⏱️ Servizio temporaneamente non disponibile';\n          userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n        } else if (!e.response) {\n          // Errori di rete\n          summary = '🌐 Errore di connessione';\n          userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n        } else {\n          // Altri errori\n          summary = '❌ Errore imprevisto';\n          userMessage = \"Si \\xE8 verificato un errore imprevisto: \".concat(errorMessage);\n        }\n        toast.current.show({\n          severity: 'error',\n          summary: summary,\n          detail: userMessage,\n          life: 6000\n        });\n\n        // Log dettagliato per debugging\n        console.error('Dettagli errore:', {\n          status: errorStatus,\n          data: errorData,\n          message: errorMessage,\n          fullError: e\n        });\n      });\n    } else {\n      toast.current.show({\n        severity: 'warn',\n        summary: '📝 Campi obbligatori mancanti',\n        detail: \"Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\",\n        life: 6000\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-grid p-fluid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Nome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results,\n            onChange: e => setResults(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci nome (obbligatorio)\",\n            editable: \"true\",\n            className: !results || results.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cognome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 44\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results8,\n            onChange: e => setResults8(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci cognome (obbligatorio)\",\n            editable: \"true\",\n            className: !results8 || results8.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Email, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-envelope\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"email\",\n            value: results2,\n            onChange: e => setResults2(e.target.value),\n            placeholder: \"Inserisci email (obbligatoria)\",\n            editable: \"true\",\n            className: !results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 21\n        }, this), (!results2 || results2.trim() === '') && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Email \\xE8 obbligatoria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 25\n        }, this), results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato email non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Tel, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'orange'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 40\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results3,\n            onChange: e => setResults3(e.target.value),\n            keyfilter: /^[\\d\\s\\-\\(\\)\\+]+$/,\n            placeholder: \"Inserisci telefono fisso\",\n            editable: \"true\",\n            className: results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 21\n        }, this), results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato telefono non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cell, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'orange'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-mobile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results9,\n            onChange: e => setResults9(e.target.value),\n            keyfilter: /^[\\d\\s\\-\\(\\)\\+]+$/,\n            placeholder: \"Inserisci cellulare\",\n            editable: \"true\",\n            className: results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 21\n        }, this), results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato cellulare non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 17\n      }, this), (!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-info-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 29\n          }, this), \" Inserire almeno un numero di telefono (fisso o cellulare)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.pIva, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-credit-card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results4,\n            onChange: e => setResults4(e.target.value),\n            keyfilter: /^[\\d\\s]+$/,\n            placeholder: \"Inserisci partita IVA (11 cifre)\",\n            editable: \"true\",\n            maxLength: 11,\n            className: !results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this), results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"La Partita IVA deve contenere esattamente 11 cifre\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-directions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results5,\n            onChange: e => setResults5(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci indirizzo\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Città\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results6,\n            onChange: e => setResults6(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci citt\\xE0\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.CodPost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results7,\n            onChange: e => setResults7(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci C.A.P.\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Pagamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"w-100\",\n            value: results10,\n            options: modPag,\n            onChange: e => setResults10(e.target.value),\n            optionLabel: \"name\",\n            placeholder: \"Seleziona metodo di pagamento\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mb-2 mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        id: \"invia\",\n        className: \"p-button saveList justify-content-center float-right ionicon mx-0 w-50\",\n        onClick: Invia,\n        children: Costanti.salva\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiAnagrafica, \"pu7zVXq2DzvQ0BELrB3ISwfq5y8=\");\n_c = AggiungiAnagrafica;\nexport default AggiungiAnagrafica;\nvar _c;\n$RefreshReg$(_c, \"AggiungiAnagrafica\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "InputText", "<PERSON><PERSON>", "APIRequest", "Toast", "<PERSON><PERSON>", "Dropdown", "jsxDEV", "_jsxDEV", "AggiungiAnagrafica", "props", "_s", "results", "setResults", "results2", "setResults2", "results3", "setResults3", "results4", "setResults4", "results5", "setResults5", "results6", "setResults6", "results7", "setResults7", "results8", "setResults8", "results9", "setResults9", "results10", "setResults10", "modPag", "setModPag", "isLookingUp", "setIsLookingUp", "toast", "renderString", "result", "undefined", "firstName", "idRetailer", "idRegistry", "email", "tel", "pIva", "address", "city", "cap", "lastName", "paymentMetod", "then", "res", "pm", "data", "for<PERSON>ach", "element", "x", "name", "description", "code", "push", "catch", "e", "console", "log", "validateForm", "errors", "trim", "test", "replace", "hasTelefono", "has<PERSON><PERSON><PERSON><PERSON>", "lookupPIva", "current", "show", "severity", "summary", "detail", "life", "response", "concat", "cellnum", "error", "_error$response", "_error$response2", "_error$response2$data", "errorStatus", "status", "errorMessage", "message", "Invia", "validationErrors", "length", "join", "corpo", "telnum", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "_e$response3", "_e$response3$data", "errorData", "userMessage", "toLowerCase", "includes", "fullError", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Nome", "style", "color", "type", "value", "onChange", "target", "keyfilter", "placeholder", "editable", "Cognome", "Email", "Tel", "Cell", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Pagamento", "options", "optionLabel", "filter", "filterBy", "id", "onClick", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiAnagrafica.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { <PERSON>nti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\n\nconst AggiungiAnagrafica = (props) => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState('');\n    const [results2, setResults2] = useState('');\n    const [results3, setResults3] = useState('');\n    const [results4, setResults4] = useState('');\n    const [results5, setResults5] = useState('');\n    const [results6, setResults6] = useState('');\n    const [results7, setResults7] = useState('');\n    const [results8, setResults8] = useState('');\n    const [results9, setResults9] = useState('');\n    const [results10, setResults10] = useState(null);\n    const [modPag, setModPag] = useState(null);\n    const [isLookingUp, setIsLookingUp] = useState(false);\n    const toast = useRef(null);\n    //Setto le variabili di stato con il valore di props mediante useEffect\n    useEffect(() => {\n        async function renderString() {\n            if (props.result !== undefined) {\n                setResults(props.result.firstName)\n                setResults2(props.result.idRetailer.idRegistry.email)\n                setResults3(props.result.idRetailer.idRegistry.tel)\n                setResults4(props.result.idRetailer.idRegistry.pIva)\n                setResults5(props.result.idRetailer.idRegistry.address)\n                setResults6(props.result.idRetailer.idRegistry.city)\n                setResults7(props.result.idRetailer.idRegistry.cap)\n                setResults8(props.result.idRetailer.idRegistry.lastName)\n                setResults9(props.result.idRetailer.idRegistry.tel)\n                setResults10(props.result.idRetailer.idRegistry.paymentMetod)\n            }\n            //Chiamata axios per la visualizzazione dei registry\n            await APIRequest('GET', 'paymentmethods/')\n                .then(res => {\n                    var pm = []\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.description,\n                            code: element.description\n                        }\n                        pm.push(x)\n                    });\n                    setModPag(pm)\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        renderString();\n    }, [props.result]);\n    // Funzione di validazione\n    const validateForm = () => {\n        const errors = [];\n\n        // Validazione campi obbligatori\n        if (!results || results.trim() === '') {\n            errors.push('📝 Nome è obbligatorio');\n        }\n        if (!results8 || results8.trim() === '') {\n            errors.push('📝 Cognome è obbligatorio');\n        }\n        if (!results4 || results4.trim() === '') {\n            errors.push('📝 Partita IVA è obbligatoria');\n        }\n\n        // Validazione formato P.IVA (11 cifre)\n        if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n        }\n\n        // Validazione email obbligatoria\n        if (!results2 || results2.trim() === '') {\n            errors.push('📧 Email è obbligatoria');\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n            errors.push('📧 Indirizzo email non valido');\n        }\n\n        // Validazione contatti: almeno uno tra telefono e cellulare\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (!hasTelefono && !hasCellulare) {\n            errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n        }\n\n        // Validazione formato telefono (se presente)\n        if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n            errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n        }\n\n        // Validazione formato cellulare (se presente)\n        if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n            errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n        }\n\n        return errors;\n    };\n\n    // Funzione per il lookup automatico tramite P.IVA\n    const lookupPIva = async () => {\n        if (!results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ P.IVA non valida',\n                detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',\n                life: 4000\n            });\n            return;\n        }\n\n        setIsLookingUp(true);\n\n        try {\n            // Chiamata al backend per il lookup P.IVA\n            const response = await APIRequest('GET', `registry/lookup-piva/${results4.replace(/\\s/g, '')}`);\n\n            if (response.data) {\n                // Popola automaticamente i campi con i dati trovati\n                const data = response.data;\n\n                if (data.firstName) setResults(data.firstName);\n                if (data.lastName) setResults8(data.lastName);\n                if (data.email) setResults2(data.email);\n                if (data.tel) setResults3(data.tel);\n                if (data.cellnum) setResults9(data.cellnum);\n                if (data.address) setResults5(data.address);\n                if (data.city) setResults6(data.city);\n                if (data.cap) setResults7(data.cap);\n\n                toast.current.show({\n                    severity: 'success',\n                    summary: '✅ Dati trovati',\n                    detail: 'I dati aziendali sono stati recuperati automaticamente. Verificare e completare le informazioni mancanti.',\n                    life: 5000\n                });\n            }\n        } catch (error) {\n            console.error('❌ Errore lookup P.IVA:', error);\n\n            const errorStatus = error.response?.status;\n            const errorMessage = error.response?.data?.message || error.message;\n\n            if (errorStatus === 404) {\n                toast.current.show({\n                    severity: 'info',\n                    summary: '🔍 Nessun risultato',\n                    detail: 'Nessuna azienda trovata con questa Partita IVA. Compilare manualmente i campi.',\n                    life: 4000\n                });\n            } else if (errorStatus === 503) {\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⏱️ Servizio non disponibile',\n                    detail: 'Il servizio di lookup P.IVA è temporaneamente non disponibile. Compilare manualmente i campi.',\n                    life: 5000\n                });\n            } else {\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⚠️ Lookup non disponibile',\n                    detail: 'Il servizio di ricerca automatica non è al momento disponibile. Compilare manualmente i campi.',\n                    life: 4000\n                });\n            }\n        } finally {\n            setIsLookingUp(false);\n        }\n    };\n\n    const Invia = async () => {\n        // Validazione frontend\n        const validationErrors = validateForm();\n        if (validationErrors.length > 0) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ Dati mancanti o non validi',\n                detail: validationErrors.join('. '),\n                life: 5000\n            });\n            return;\n        }\n\n        // Verifica che tutti i campi obbligatori siano compilati\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {\n            var corpo = {\n                firstName: results,\n                lastName: results8,\n                email: results2,\n                telnum: results3,\n                cellnum: results9,\n                pIva: results4,\n                address: results5,\n                city: results6,\n                cap: results7,\n                paymentMetod: results10?.name || ''\n            }\n            //Chiamata axios per la creazione del registry\n            await APIRequest('POST', 'registry/', corpo)\n                .then(res => {\n                    console.log(res.data);\n                    toast.current.show({\n                        severity: 'success',\n                        summary: '✅ Successo',\n                        detail: \"L'anagrafica è stata inserita con successo\",\n                        life: 3000\n                    });\n                    setTimeout(() => {\n                        window.location.reload()\n                    }, 3000)\n                }).catch((e) => {\n                    console.error('❌ Errore creazione anagrafica:', e);\n\n                    // Gestione specifica degli errori\n                    const errorStatus = e.response?.status;\n                    const errorData = e.response?.data;\n                    const errorMessage = e.response?.data?.message || e.message;\n\n                    let userMessage = '';\n                    let summary = 'Errore';\n\n                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||\n                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n                        // Violazione unique constraint (P.IVA duplicata)\n                        summary = '⚠️ Partita IVA già presente';\n                        userMessage = `La Partita IVA \"${results4}\" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;\n                    } else if (errorStatus === 400) {\n                        // Errori di validazione\n                        summary = '📝 Dati non validi';\n                        if (errorMessage.toLowerCase().includes('email')) {\n                            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n                            userMessage = 'Il numero di telefono inserito non è valido.';\n                        } else {\n                            userMessage = `Dati inseriti non validi: ${errorMessage}`;\n                        }\n                    } else if (errorStatus === 422) {\n                        // Errori di business logic\n                        summary = '🚫 Operazione non consentita';\n                        userMessage = `Impossibile completare l'operazione: ${errorMessage}`;\n                    } else if (errorStatus === 500 || errorStatus === 501) {\n                        // Errori del server\n                        summary = '🔧 Errore del server';\n                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n                    } else if (errorStatus === 503) {\n                        // Servizio non disponibile\n                        summary = '⏱️ Servizio temporaneamente non disponibile';\n                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n                    } else if (!e.response) {\n                        // Errori di rete\n                        summary = '🌐 Errore di connessione';\n                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n                    } else {\n                        // Altri errori\n                        summary = '❌ Errore imprevisto';\n                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;\n                    }\n\n                    toast.current.show({\n                        severity: 'error',\n                        summary: summary,\n                        detail: userMessage,\n                        life: 6000\n                    });\n\n                    // Log dettagliato per debugging\n                    console.error('Dettagli errore:', {\n                        status: errorStatus,\n                        data: errorData,\n                        message: errorMessage,\n                        fullError: e\n                    });\n                })\n        } else {\n            toast.current.show({\n                severity: 'warn',\n                summary: '📝 Campi obbligatori mancanti',\n                detail: \"Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\",\n                life: 6000\n            });\n        }\n    };\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div className=\"p-grid p-fluid\">\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Nome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results}\n                            onChange={(e) => setResults(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci nome (obbligatorio)\"\n                            editable='true'\n                            className={!results || results.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cognome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results8}\n                            onChange={(e) => setResults8(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci cognome (obbligatorio)\"\n                            editable='true'\n                            className={!results8 || results8.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Email} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-envelope\"></i>\n                        </span>\n                        <InputText\n                            type=\"email\"\n                            value={results2}\n                            onChange={(e) => setResults2(e.target.value)}\n                            placeholder=\"Inserisci email (obbligatoria)\"\n                            editable='true'\n                            className={!results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {(!results2 || results2.trim() === '') && (\n                        <small className=\"p-error\">Email è obbligatoria</small>\n                    )}\n                    {results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && (\n                        <small className=\"p-error\">Formato email non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Tel} <span style={{color: 'orange'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-phone\"></i>\n                        </span>\n                        <InputText\n                            type=\"tel\"\n                            value={results3}\n                            onChange={(e) => setResults3(e.target.value)}\n                            keyfilter={/^[\\d\\s\\-\\(\\)\\+]+$/}\n                            placeholder=\"Inserisci telefono fisso\"\n                            editable='true'\n                            className={results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) && (\n                        <small className=\"p-error\">Formato telefono non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cell} <span style={{color: 'orange'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-mobile\"></i>\n                        </span>\n                        <InputText\n                            type=\"tel\"\n                            value={results9}\n                            onChange={(e) => setResults9(e.target.value)}\n                            keyfilter={/^[\\d\\s\\-\\(\\)\\+]+$/}\n                            placeholder=\"Inserisci cellulare\"\n                            editable='true'\n                            className={results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) && (\n                        <small className=\"p-error\">Formato cellulare non valido</small>\n                    )}\n                </div>\n\n                {/* Messaggio informativo per i contatti */}\n                {(!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && (\n                    <div className=\"p-col-12\">\n                        <small className=\"p-error\">\n                            <i className=\"pi pi-info-circle\"></i> Inserire almeno un numero di telefono (fisso o cellulare)\n                        </small>\n                    </div>\n                )}\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.pIva} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-credit-card\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results4}\n                            onChange={(e) => setResults4(e.target.value)}\n                            keyfilter={/^[\\d\\s]+$/}\n                            placeholder=\"Inserisci partita IVA (11 cifre)\"\n                            editable='true'\n                            maxLength={11}\n                            className={!results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && (\n                        <small className=\"p-error\">La Partita IVA deve contenere esattamente 11 cifre</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Indirizzo}</h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-directions\"></i>\n                        </span>\n                        <InputText type='text' value={results5} onChange={(e) => setResults5(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci indirizzo\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Città}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results6} onChange={(e) => setResults6(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci città\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.CodPost}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results7} onChange={(e) => setResults7(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci C.A.P.\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Pagamento}</h6>\n                    <div className=\"p-inputgroup\">\n                        <Dropdown className='w-100' value={results10} options={modPag} onChange={(e) => setResults10(e.target.value)} optionLabel=\"name\" placeholder=\"Seleziona metodo di pagamento\" filter filterBy='name' />\n                    </div>\n                </div>\n            </div>\n            <div className=\"d-flex justify-content-center mb-2 mt-4\">\n                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                <Button id=\"invia\" className=\"p-button saveList justify-content-center float-right ionicon mx-0 w-50\" onClick={Invia}>{Costanti.salva}</Button>\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiAnagrafica;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMsC,KAAK,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAC,SAAS,CAAC,MAAM;IACZ,eAAeqC,YAAYA,CAAA,EAAG;MAC1B,IAAI3B,KAAK,CAAC4B,MAAM,KAAKC,SAAS,EAAE;QAC5B1B,UAAU,CAACH,KAAK,CAAC4B,MAAM,CAACE,SAAS,CAAC;QAClCzB,WAAW,CAACL,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACC,KAAK,CAAC;QACrD1B,WAAW,CAACP,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnDzB,WAAW,CAACT,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACG,IAAI,CAAC;QACpDxB,WAAW,CAACX,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACI,OAAO,CAAC;QACvDvB,WAAW,CAACb,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACK,IAAI,CAAC;QACpDtB,WAAW,CAACf,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACM,GAAG,CAAC;QACnDrB,WAAW,CAACjB,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACO,QAAQ,CAAC;QACxDpB,WAAW,CAACnB,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnDb,YAAY,CAACrB,KAAK,CAAC4B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACQ,YAAY,CAAC;MACjE;MACA;MACA,MAAM/C,UAAU,CAAC,KAAK,EAAE,iBAAiB,CAAC,CACrCgD,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,EAAE,GAAG,EAAE;QACXD,GAAG,CAACE,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAG;YACJC,IAAI,EAAEF,OAAO,CAACG,WAAW;YACzBC,IAAI,EAAEJ,OAAO,CAACG;UAClB,CAAC;UACDN,EAAE,CAACQ,IAAI,CAACJ,CAAC,CAAC;QACd,CAAC,CAAC;QACFxB,SAAS,CAACoB,EAAE,CAAC;MACjB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACA1B,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAClB;EACA,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI,CAACvD,OAAO,IAAIA,OAAO,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnCD,MAAM,CAACN,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,IAAI,CAACnC,QAAQ,IAAIA,QAAQ,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,2BAA2B,CAAC;IAC5C;IACA,IAAI,CAAC3C,QAAQ,IAAIA,QAAQ,CAACkD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,IAAI3C,QAAQ,IAAI,CAAC,UAAU,CAACmD,IAAI,CAACnD,QAAQ,CAACoD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC3DH,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;;IAEA;IACA,IAAI,CAAC/C,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAACQ,IAAI,CAACvD,QAAQ,CAAC,EAAE;MACrDqD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,MAAMU,WAAW,GAAGvD,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAG5C,QAAQ,IAAIA,QAAQ,CAACwC,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAI,CAACG,WAAW,IAAI,CAACC,YAAY,EAAE;MAC/BL,MAAM,CAACN,IAAI,CAAC,8DAA8D,CAAC;IAC/E;;IAEA;IACA,IAAI7C,QAAQ,IAAI,CAAC,uBAAuB,CAACqD,IAAI,CAACrD,QAAQ,CAAC,EAAE;MACrDmD,MAAM,CAACN,IAAI,CAAC,yDAAyD,CAAC;IAC1E;;IAEA;IACA,IAAIjC,QAAQ,IAAI,CAAC,uBAAuB,CAACyC,IAAI,CAACzC,QAAQ,CAAC,EAAE;MACrDuC,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;IAEA,OAAOM,MAAM;EACjB,CAAC;;EAED;EACA,MAAMM,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACvD,QAAQ,IAAI,CAAC,UAAU,CAACmD,IAAI,CAACnD,QAAQ,CAACoD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC5DlC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,iFAAiF;QACzFC,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;IAEA5C,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACA;MACA,MAAM6C,QAAQ,GAAG,MAAM7E,UAAU,CAAC,KAAK,0BAAA8E,MAAA,CAA0B/D,QAAQ,CAACoD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE,CAAC;MAE/F,IAAIU,QAAQ,CAAC1B,IAAI,EAAE;QACf;QACA,MAAMA,IAAI,GAAG0B,QAAQ,CAAC1B,IAAI;QAE1B,IAAIA,IAAI,CAACd,SAAS,EAAE3B,UAAU,CAACyC,IAAI,CAACd,SAAS,CAAC;QAC9C,IAAIc,IAAI,CAACL,QAAQ,EAAEtB,WAAW,CAAC2B,IAAI,CAACL,QAAQ,CAAC;QAC7C,IAAIK,IAAI,CAACX,KAAK,EAAE5B,WAAW,CAACuC,IAAI,CAACX,KAAK,CAAC;QACvC,IAAIW,IAAI,CAACV,GAAG,EAAE3B,WAAW,CAACqC,IAAI,CAACV,GAAG,CAAC;QACnC,IAAIU,IAAI,CAAC4B,OAAO,EAAErD,WAAW,CAACyB,IAAI,CAAC4B,OAAO,CAAC;QAC3C,IAAI5B,IAAI,CAACR,OAAO,EAAEzB,WAAW,CAACiC,IAAI,CAACR,OAAO,CAAC;QAC3C,IAAIQ,IAAI,CAACP,IAAI,EAAExB,WAAW,CAAC+B,IAAI,CAACP,IAAI,CAAC;QACrC,IAAIO,IAAI,CAACN,GAAG,EAAEvB,WAAW,CAAC6B,IAAI,CAACN,GAAG,CAAC;QAEnCZ,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,gBAAgB;UACzBC,MAAM,EAAE,2GAA2G;UACnHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACZtB,OAAO,CAACmB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,MAAMI,WAAW,IAAAH,eAAA,GAAGD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBI,MAAM;MAC1C,MAAMC,YAAY,GAAG,EAAAJ,gBAAA,GAAAF,KAAK,CAACH,QAAQ,cAAAK,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsBI,OAAO,KAAIP,KAAK,CAACO,OAAO;MAEnE,IAAIH,WAAW,KAAK,GAAG,EAAE;QACrBnD,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,qBAAqB;UAC9BC,MAAM,EAAE,gFAAgF;UACxFC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAIQ,WAAW,KAAK,GAAG,EAAE;QAC5BnD,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,6BAA6B;UACtCC,MAAM,EAAE,+FAA+F;UACvGC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM;QACH3C,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,2BAA2B;UACpCC,MAAM,EAAE,gGAAgG;UACxGC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ,CAAC,SAAS;MACN5C,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;EAED,MAAMwD,KAAK,GAAG,MAAAA,CAAA,KAAY;IACtB;IACA,MAAMC,gBAAgB,GAAG1B,YAAY,CAAC,CAAC;IACvC,IAAI0B,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7BzD,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAEc,gBAAgB,CAACE,IAAI,CAAC,IAAI,CAAC;QACnCf,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;;IAEA;IACA,MAAMR,WAAW,GAAGvD,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAG5C,QAAQ,IAAIA,QAAQ,CAACwC,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAIxD,OAAO,IAAIc,QAAQ,IAAIR,QAAQ,IAAIJ,QAAQ,KAAKyD,WAAW,IAAIC,YAAY,CAAC,EAAE;MAC9E,IAAIuB,KAAK,GAAG;QACRvD,SAAS,EAAE5B,OAAO;QAClBqC,QAAQ,EAAEvB,QAAQ;QAClBiB,KAAK,EAAE7B,QAAQ;QACfkF,MAAM,EAAEhF,QAAQ;QAChBkE,OAAO,EAAEtD,QAAQ;QACjBiB,IAAI,EAAE3B,QAAQ;QACd4B,OAAO,EAAE1B,QAAQ;QACjB2B,IAAI,EAAEzB,QAAQ;QACd0B,GAAG,EAAExB,QAAQ;QACb0B,YAAY,EAAE,CAAApB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4B,IAAI,KAAI;MACrC,CAAC;MACD;MACA,MAAMvD,UAAU,CAAC,MAAM,EAAE,WAAW,EAAE4F,KAAK,CAAC,CACvC5C,IAAI,CAACC,GAAG,IAAI;QACTY,OAAO,CAACC,GAAG,CAACb,GAAG,CAACE,IAAI,CAAC;QACrBlB,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,4CAA4C;UACpDC,IAAI,EAAE;QACV,CAAC,CAAC;QACFkB,UAAU,CAAC,MAAM;UACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAACtC,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAsC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA;QACZxC,OAAO,CAACmB,KAAK,CAAC,gCAAgC,EAAEpB,CAAC,CAAC;;QAElD;QACA,MAAMwB,WAAW,IAAAc,WAAA,GAAGtC,CAAC,CAACiB,QAAQ,cAAAqB,WAAA,uBAAVA,WAAA,CAAYb,MAAM;QACtC,MAAMiB,SAAS,IAAAH,YAAA,GAAGvC,CAAC,CAACiB,QAAQ,cAAAsB,YAAA,uBAAVA,YAAA,CAAYhD,IAAI;QAClC,MAAMmC,YAAY,GAAG,EAAAc,YAAA,GAAAxC,CAAC,CAACiB,QAAQ,cAAAuB,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYjD,IAAI,cAAAkD,iBAAA,uBAAhBA,iBAAA,CAAkBd,OAAO,KAAI3B,CAAC,CAAC2B,OAAO;QAE3D,IAAIgB,WAAW,GAAG,EAAE;QACpB,IAAI7B,OAAO,GAAG,QAAQ;QAEtB,IAAIU,WAAW,KAAK,GAAG,IAAIE,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACpEnB,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAInB,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;UACvG;UACA/B,OAAO,GAAG,6BAA6B;UACvC6B,WAAW,uBAAAzB,MAAA,CAAsB/D,QAAQ,sGAA4F;QACzI,CAAC,MAAM,IAAIqE,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAV,OAAO,GAAG,oBAAoB;UAC9B,IAAIY,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC9CF,WAAW,GAAG,kEAAkE;UACpF,CAAC,MAAM,IAAIjB,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAInB,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC1GF,WAAW,GAAG,yEAAyE;UAC3F,CAAC,MAAM,IAAIjB,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAInB,YAAY,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACtGF,WAAW,GAAG,8CAA8C;UAChE,CAAC,MAAM;YACHA,WAAW,gCAAAzB,MAAA,CAAgCQ,YAAY,CAAE;UAC7D;QACJ,CAAC,MAAM,IAAIF,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAV,OAAO,GAAG,8BAA8B;UACxC6B,WAAW,2CAAAzB,MAAA,CAA2CQ,YAAY,CAAE;QACxE,CAAC,MAAM,IAAIF,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;UACnD;UACAV,OAAO,GAAG,sBAAsB;UAChC6B,WAAW,GAAG,gHAAgH;QAClI,CAAC,MAAM,IAAInB,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAV,OAAO,GAAG,6CAA6C;UACvD6B,WAAW,GAAG,8EAA8E;QAChG,CAAC,MAAM,IAAI,CAAC3C,CAAC,CAACiB,QAAQ,EAAE;UACpB;UACAH,OAAO,GAAG,0BAA0B;UACpC6B,WAAW,GAAG,oFAAoF;QACtG,CAAC,MAAM;UACH;UACA7B,OAAO,GAAG,qBAAqB;UAC/B6B,WAAW,+CAAAzB,MAAA,CAA4CQ,YAAY,CAAE;QACzE;QAEArD,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE4B,WAAW;UACnB3B,IAAI,EAAE;QACV,CAAC,CAAC;;QAEF;QACAf,OAAO,CAACmB,KAAK,CAAC,kBAAkB,EAAE;UAC9BK,MAAM,EAAED,WAAW;UACnBjC,IAAI,EAAEmD,SAAS;UACff,OAAO,EAAED,YAAY;UACrBoB,SAAS,EAAE9C;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH3B,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAE,uGAAuG;QAC/GC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ,CAAC;EACD,oBACIvE,OAAA;IAAKsG,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBvG,OAAA,CAACJ,KAAK;MAAC4G,GAAG,EAAE5E;IAAM;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB5G,OAAA;MAAKsG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BvG,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,GAAK7G,QAAQ,CAACmH,IAAI,EAAC,GAAC,eAAA7G,OAAA;YAAM8G,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9D5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACP5G,OAAA,CAACP,SAAS;YACNuH,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE7G,OAAQ;YACf8G,QAAQ,EAAG3D,CAAC,IAAKlD,UAAU,CAACkD,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC5CG,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,+BAA+B;YAC3CC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE,CAAClG,OAAO,IAAIA,OAAO,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,GAAK7G,QAAQ,CAAC6H,OAAO,EAAC,GAAC,eAAAvH,OAAA;YAAM8G,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACP5G,OAAA,CAACP,SAAS;YACNuH,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE/F,QAAS;YAChBgG,QAAQ,EAAG3D,CAAC,IAAKpC,WAAW,CAACoC,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,kCAAkC;YAC9CC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE,CAACpF,QAAQ,IAAIA,QAAQ,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,GAAK7G,QAAQ,CAAC8H,KAAK,EAAC,GAAC,eAAAxH,OAAA;YAAM8G,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/D5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACP5G,OAAA,CAACP,SAAS;YACNuH,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE3G,QAAS;YAChB4G,QAAQ,EAAG3D,CAAC,IAAKhD,WAAW,CAACgD,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC7CI,WAAW,EAAC,gCAAgC;YAC5CC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE,CAAChG,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAACvD,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL,CAAC,CAACtG,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,kBACjC5D,OAAA;UAAOsG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzD,EACAtG,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAACvD,QAAQ,CAAC,iBAC/EN,OAAA;UAAOsG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC7D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,GAAK7G,QAAQ,CAAC+H,GAAG,EAAC,GAAC,eAAAzH,OAAA;YAAM8G,KAAK,EAAE;cAACC,KAAK,EAAE;YAAQ,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACP5G,OAAA,CAACP,SAAS;YACNuH,IAAI,EAAC,KAAK;YACVC,KAAK,EAAEzG,QAAS;YAChB0G,QAAQ,EAAG3D,CAAC,IAAK9C,WAAW,CAAC8C,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE,mBAAoB;YAC/BC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE9F,QAAQ,IAAI,CAAC,uBAAuB,CAACqD,IAAI,CAACrD,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLpG,QAAQ,IAAI,CAAC,uBAAuB,CAACqD,IAAI,CAACrD,QAAQ,CAAC,iBAChDR,OAAA;UAAOsG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,GAAK7G,QAAQ,CAACgI,IAAI,EAAC,GAAC,eAAA1H,OAAA;YAAM8G,KAAK,EAAE;cAACC,KAAK,EAAE;YAAQ,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACP5G,OAAA,CAACP,SAAS;YACNuH,IAAI,EAAC,KAAK;YACVC,KAAK,EAAE7F,QAAS;YAChB8F,QAAQ,EAAG3D,CAAC,IAAKlC,WAAW,CAACkC,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE,mBAAoB;YAC/BC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAElF,QAAQ,IAAI,CAAC,uBAAuB,CAACyC,IAAI,CAACzC,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLxF,QAAQ,IAAI,CAAC,uBAAuB,CAACyC,IAAI,CAACzC,QAAQ,CAAC,iBAChDpB,OAAA;UAAOsG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA4B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACjE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGL,CAAC,CAACpG,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAACxC,QAAQ,IAAIA,QAAQ,CAACwC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,iBAC3E5D,OAAA;QAAKsG,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBvG,OAAA;UAAOsG,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtBvG,OAAA;YAAGsG,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,8DACzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACR,eACD5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,GAAK7G,QAAQ,CAAC2C,IAAI,EAAC,GAAC,eAAArC,OAAA;YAAM8G,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9D5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACP5G,OAAA,CAACP,SAAS;YACNuH,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEvG,QAAS;YAChBwG,QAAQ,EAAG3D,CAAC,IAAK5C,WAAW,CAAC4C,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE,WAAY;YACvBC,WAAW,EAAC,kCAAkC;YAC9CC,QAAQ,EAAC,MAAM;YACfK,SAAS,EAAE,EAAG;YACdrB,SAAS,EAAE,CAAC5F,QAAQ,IAAIA,QAAQ,CAACkD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAACC,IAAI,CAACnD,QAAQ,CAACoD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLlG,QAAQ,IAAI,CAAC,UAAU,CAACmD,IAAI,CAACnD,QAAQ,CAACoD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,iBACtD9D,OAAA;UAAOsG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACvF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,EAAK7G,QAAQ,CAACkI;QAAS;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvG,OAAA;YAAMsG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCvG,OAAA;cAAGsG,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACP5G,OAAA,CAACP,SAAS;YAACuH,IAAI,EAAC,MAAM;YAACC,KAAK,EAAErG,QAAS;YAACsG,QAAQ,EAAG3D,CAAC,IAAK1C,WAAW,CAAC0C,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,qBAAqB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,EAAK7G,QAAQ,CAACmI;QAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzB5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBvG,OAAA,CAACP,SAAS;YAACuH,IAAI,EAAC,MAAM;YAACC,KAAK,EAAEnG,QAAS;YAACoG,QAAQ,EAAG3D,CAAC,IAAKxC,WAAW,CAACwC,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,oBAAiB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,EAAK7G,QAAQ,CAACoI;QAAO;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3B5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBvG,OAAA,CAACP,SAAS;YAACuH,IAAI,EAAC,MAAM;YAACC,KAAK,EAAEjG,QAAS;YAACkG,QAAQ,EAAG3D,CAAC,IAAKtC,WAAW,CAACsC,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,kBAAkB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5G,OAAA;QAAKsG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BvG,OAAA;UAAAuG,QAAA,EAAK7G,QAAQ,CAACqI;QAAS;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B5G,OAAA;UAAKsG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBvG,OAAA,CAACF,QAAQ;YAACwG,SAAS,EAAC,OAAO;YAACW,KAAK,EAAE3F,SAAU;YAAC0G,OAAO,EAAExG,MAAO;YAAC0F,QAAQ,EAAG3D,CAAC,IAAKhC,YAAY,CAACgC,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAACgB,WAAW,EAAC,MAAM;YAACZ,WAAW,EAAC,+BAA+B;YAACa,MAAM;YAACC,QAAQ,EAAC;UAAM;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN5G,OAAA;MAAKsG,SAAS,EAAC,yCAAyC;MAAAC,QAAA,eAEpDvG,OAAA,CAACH,MAAM;QAACuI,EAAE,EAAC,OAAO;QAAC9B,SAAS,EAAC,wEAAwE;QAAC+B,OAAO,EAAElD,KAAM;QAAAoB,QAAA,EAAE7G,QAAQ,CAAC4I;MAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9I,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAzG,EAAA,CAhcKF,kBAAkB;AAAAsI,EAAA,GAAlBtI,kBAAkB;AAkcxB,eAAeA,kBAAkB;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}