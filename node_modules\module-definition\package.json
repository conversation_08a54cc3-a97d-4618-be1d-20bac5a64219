{"name": "module-definition", "version": "5.0.1", "description": "Determines if a file is using a CommonJS or AMD module definition", "main": "index.js", "bin": {"module-definition": "bin/cli.js"}, "files": ["bin/cli.js", "index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/module-definition.git"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (https://mrjoelkemp.com/)", "license": "MIT", "bugs": {"url": "https://github.com/dependents/module-definition/issues"}, "homepage": "https://github.com/dependents/module-definition", "engines": {"node": ">=14"}, "dependencies": {"ast-module-types": "^5.0.0", "node-source-walk": "^6.0.1"}, "devDependencies": {"c8": "^7.13.0", "memfs": "^3.5.1", "mocha": "^10.2.0", "unionfs": "^4.4.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-string-slice": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}