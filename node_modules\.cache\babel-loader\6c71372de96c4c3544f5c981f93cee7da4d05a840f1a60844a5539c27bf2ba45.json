{"ast": null, "code": "import devWarning, { resetWarned } from \"rc-util/es/warning\";\nexport { resetWarned };\nexport default (function (valid, component, message) {\n  devWarning(valid, \"[antd: \".concat(component, \"] \").concat(message)); // StrictMode will inject console which will not throw warning in React 17.\n\n  if (process.env.NODE_ENV === 'test') {\n    resetWarned();\n  }\n});", "map": {"version": 3, "names": ["dev<PERSON><PERSON><PERSON>", "resetWarned", "valid", "component", "message", "concat", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/devWarning.js"], "sourcesContent": ["import devWarning, { resetWarned } from \"rc-util/es/warning\";\nexport { resetWarned };\nexport default (function (valid, component, message) {\n  devWarning(valid, \"[antd: \".concat(component, \"] \").concat(message)); // StrictMode will inject console which will not throw warning in React 17.\n\n  if (process.env.NODE_ENV === 'test') {\n    resetWarned();\n  }\n});"], "mappings": "AAAA,OAAOA,UAAU,IAAIC,WAAW,QAAQ,oBAAoB;AAC5D,SAASA,WAAW;AACpB,gBAAgB,UAAUC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACnDJ,UAAU,CAACE,KAAK,EAAE,SAAS,CAACG,MAAM,CAACF,SAAS,EAAE,IAAI,CAAC,CAACE,MAAM,CAACD,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEtE,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnCP,WAAW,CAAC,CAAC;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}