/**
 * @license Copyright 2022 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

/**
 * @param {LH.Artifacts.Script} script
 * @return {boolean}
 */
function isInline(script) {
  return Boolean(script.startLine || script.startColumn);
}

/**
 * @param {LH.Artifacts.NetworkRequest[]} networkRecords
 * @param {LH.Artifacts.Script} script
 * @return {LH.Artifacts.NetworkRequest|undefined}
 */
function getRequestForScript(networkRecords, script) {
  let networkRequest = networkRecords.find(request => request.url === script.url);
  while (networkRequest?.redirectDestination) {
    networkRequest = networkRequest.redirectDestination;
  }
  return networkRequest;
}

export {
  getRequestForScript,
  isInline,
};
