{"version": 3, "file": "read-typescript-config.js", "sourceRoot": "", "sources": ["../../src/read-typescript-config.ts"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,4DAA4D;AAC5D,aAAa;AACb,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AAC9B,OAAO,EAAE,MAAM,YAAY,CAAA;AAC3B,OAAO,MAAM,MAAM,aAAa,CAAA;AAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,GAAG,CAAA;AAE3B,IAAI,cAAc,GAAqC,SAAS,CAAA;AAChE,eAAe,GAAG,EAAE;IAClB,IAAI,cAAc;QAAE,OAAO,cAAc,CAAA;IACzC,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC,CAAA;IAC7D,MAAM,UAAU,GAAG,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IAC1D,OAAO,CAAC,cAAc,GAAG,EAAE,CAAC,0BAA0B,CACpD,UAAU,CAAC,MAAM,EACjB,EAAE,CAAC,GAAG,EACN,OAAO,CAAC,GAAG,EAAE,CACd,CAAC,CAAA;AACJ,CAAC,CAAA", "sourcesContent": ["// read the actual configuration that tsc is using\n// Note: cannot just use JSON.parse, because ts config files\n// are jsonc.\nimport { resolve } from 'path'\nimport ts from 'typescript'\nimport config from './config.js'\nconst { readFile } = ts.sys\n\nlet parsedTsConfig: ts.ParsedCommandLine | undefined = undefined\nexport default () => {\n  if (parsedTsConfig) return parsedTsConfig\n  const configPath = config.project ?? resolve('tsconfig.json')\n  const readResult = ts.readConfigFile(configPath, readFile)\n  return (parsedTsConfig = ts.parseJsonConfigFileContent(\n    readResult.config,\n    ts.sys,\n    process.cwd(),\n  ))\n}\n"]}