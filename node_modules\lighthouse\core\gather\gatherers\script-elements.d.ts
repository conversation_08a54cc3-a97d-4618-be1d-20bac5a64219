export default ScriptElements;
/**
 * @fileoverview Gets JavaScript file contents.
 */
declare class ScriptElements extends FRGatherer {
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @return {Promise<LH.Artifacts['ScriptElements']>}
     */
    _getArtifact(context: LH.Gatherer.FRTransitionalContext, networkRecords: LH.Artifacts.NetworkRequest[]): Promise<LH.Artifacts['ScriptElements']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<import("../../index.js").Artifacts.ScriptElement[]>;
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<import("../../index.js").Artifacts.ScriptElement[]>;
}
import FRGatherer from "../base-gatherer.js";
import { NetworkRequest } from "../../lib/network-request.js";
//# sourceMappingURL=script-elements.d.ts.map