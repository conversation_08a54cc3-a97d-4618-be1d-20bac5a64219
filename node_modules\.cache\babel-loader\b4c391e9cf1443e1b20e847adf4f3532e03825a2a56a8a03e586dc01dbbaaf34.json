{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\gestioneLavorazioni.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* OpJobsIngresso - operazioni sulle lavorazioni in entrata\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport ModificaStato from \"../../aggiunta_dati/modificaStato\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { PanelMenu } from \"primereact/panelmenu\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { resp_magConfrontoDispQta, resp_magControlloLottiEScadenze } from \"../../components/route\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneLavorazioni extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: '',\n      referente: '',\n      deliveryDestination: '',\n      orderDate: '',\n      deliveryDate: '',\n      termsPayment: '',\n      paymentStatus: '',\n      status: ''\n    };\n    this.onRowSelect = result => {\n      var idFOROrd = [];\n      this.state.results.forEach(element => {\n        if (element.id === result.id) {\n          idFOROrd = element;\n        }\n      });\n      localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n      if (result.type.toLowerCase() === 'for-ordine' || result.type.toLowerCase() === 'reso') {\n        window.location.pathname = resp_magControlloLottiEScadenze;\n      } else {\n        window.location.pathname = resp_magConfrontoDispQta;\n      }\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      selectedRetailer: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      resultDialog2: false,\n      warehouse: [],\n      type: [],\n      cliente: [],\n      status: [],\n      search: '',\n      loading: true\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterClient = e => {\n      this.setState({\n        search: e.value,\n        selectedRetailer: e.value\n      });\n      var finRes = [];\n      var risultato = [];\n      var cliente = [];\n      var data = this.state.results3;\n      var filter = '';\n      if (e.value === 'ALTRO') {\n        filter = null;\n      } else {\n        filter = e.value.trim().toLowerCase();\n      }\n      if (filter === null || filter.length > 0) {\n        data.forEach(element => {\n          if (element.cliente !== null && element.cliente !== '') {\n            cliente.push(element.cliente);\n          } else {\n            if (e.item.value === 'cliente') {\n              finRes = data.filter(element => element.cliente === null || element.cliente === '');\n            }\n          }\n        });\n        var cli = cliente.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        if (cli.length > 0) {\n          risultato = data.filter(element => element.cliente === cli[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (finRes.length > 0) {\n          this.setState({\n            results2: finRes\n          });\n        } else {\n          this.setState({\n            results2: []\n          });\n        }\n      } else {\n        this.setState({\n          results2: this.state.results3\n        });\n      }\n    };\n    this.filterProd = e => {\n      this.setState({\n        search: e.item.label\n      });\n      var type = [];\n      var warehouse = [];\n      var finRes = [];\n      var risultato = [];\n      var status = [];\n      var data = this.state.results3;\n      var filter = '';\n      if (e.item.label === 'ALTRO') {\n        filter = null;\n      } else {\n        filter = e.item.label.trim().toLowerCase();\n      }\n      if (filter === null || filter.length > 0) {\n        data.forEach(element => {\n          if (element.type !== null && element.type !== '') {\n            type.push(element.type);\n          } else {\n            if (e.item.value === 'type') {\n              finRes = data.filter(element => element.type === null || element.type === '');\n            }\n          }\n          if (element.status !== null && element.status !== '') {\n            status.push(element.status);\n          } else {\n            if (e.item.value === 'status') {\n              finRes = data.filter(element => element.type === e.item.dad && (element.status === null || element.status === ''));\n            }\n          }\n          if (element.warehouse !== null && element.warehouse !== '') {\n            warehouse.push(element.warehouse);\n          } else {\n            if (e.item.value === 'warehouse') {\n              finRes = data.filter(element => element.type === e.item.dad && (element.warehouse === null || element.warehouse === ''));\n            }\n          }\n        });\n        var tipo = type.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        var stato = status.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        var magazzino = warehouse.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        if (tipo.length > 0) {\n          risultato = data.filter(element => element.type === tipo[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (stato.length > 0) {\n          risultato = data.filter(element => element.status === stato[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (magazzino.length > 0) {\n          risultato = data.filter(element => element.warehouse === magazzino[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (finRes.length > 0) {\n          this.setState({\n            results2: finRes\n          });\n        } else {\n          this.setState({\n            results2: []\n          });\n        }\n      } else {\n        this.setState({\n          results2: this.state.results3\n        });\n      }\n    };\n    this.items = [{\n      label: Costanti.type,\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.items2 = [{\n      label: Costanti.Stato,\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.items3 = [{\n      label: Costanti.Magazzino,\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.onRowSelect = this.onRowSelect.bind(this);\n    this.modifica = this.modifica.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n    this.defineSort = this.defineSort.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var document = [];\n    await APIRequest(\"GET\", \"tasks/\").then(res => {\n      res.data.forEach(element => {\n        var _element$operator;\n        var x = {\n          id: element.id,\n          number: element.idDocument.number,\n          status: element.status,\n          type: element.idDocument.type,\n          taskDate: element.createAt,\n          createAt: element.idDocument.documentDate,\n          erpSync: element.idDocument.erpSync === false ? 'Non trasmesso' : 'Trasmesso',\n          operator: (_element$operator = element.operator) === null || _element$operator === void 0 ? void 0 : _element$operator.idUser.username,\n          warehouse: element.idDocument.idWarehouses.warehouseName,\n          cliente: element.idDocument.idRetailer !== null ? element.idDocument.idRetailer.idRegistry.firstName : element.idDocument.idSupplying !== null ? element.idDocument.idSupplying.idRegistry.firstName : 'Documento di inventario interno'\n        };\n        document.push(x);\n      });\n      this.setState({\n        results: res.data,\n        results2: document,\n        results3: document,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista delle task. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n    this.defineSort();\n  }\n  /* Definiamo le categorie per il filtraggio  */\n  defineSort() {\n    var warehouse = [];\n    var type = [];\n    var cliente = [];\n    var status = [];\n    if (this.state.results2 !== null) {\n      this.state.results2.forEach(element => {\n        warehouse.push(element.warehouse);\n        type.push(element.type);\n        cliente.push(element.cliente);\n        status.push(element.status);\n      });\n      warehouse = [...new Set(warehouse)].sort();\n      type = [...new Set(type)].sort();\n      status = [...new Set(status)].sort();\n      cliente = [...new Set(cliente)].sort();\n      var elementnull = [];\n      var cli = [];\n      cliente.forEach(element => {\n        var x = {\n          label: element,\n          value: element\n        };\n        cli.push(x);\n      });\n      type.forEach(element => {\n        if (element !== null && element !== '') {\n          this.items[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'type',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items[0].items.push(items);\n      });\n      status.forEach(element => {\n        if (element !== null && element !== '') {\n          this.items2[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'status',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items2[0].items.push(items);\n      });\n      warehouse.forEach(element => {\n        if (element !== null && element !== '') {\n          this.items3[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'warehouse',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items3[0].items.push(items);\n      });\n      this.setState({\n        warehouse: warehouse,\n        type: type,\n        cliente: cli,\n        status: status\n      });\n    }\n  }\n  modifica(result) {\n    this.setState({\n      result,\n      resultDialog: true\n    });\n  }\n  hideDialog() {\n    this.setState({\n      resultDialog: false\n    });\n  }\n  openFilter() {\n    this.setState({\n      resultDialog2: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  reset() {\n    this.setState({\n      results2: this.state.results3,\n      search: '',\n      selectedRetailer: null\n    });\n  }\n  /* Reselt filtro categorie */\n  resetDesc() {\n    this.setState({\n      results2: this.state.results3,\n      search: '',\n      selectedRetailer: null\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"cliente\",\n      header: Costanti.cliente,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"operator\",\n      header: Costanti.Operatore,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"warehouse\",\n      header: Costanti.Magazzino,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"status\",\n      header: Costanti.Stato,\n      body: 'statDoc',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"taskDate\",\n      header: Costanti.DataTask,\n      body: \"taskDate2\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: Costanti.Trasmetti,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"createAt\",\n      header: Costanti.DataDoc,\n      body: 'createAt',\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.ControllaLav,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 50\n      }, this),\n      handler: this.onRowSelect\n    }, {\n      name: Costanti.CambiaStato,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 49\n      }, this),\n      handler: this.modifica,\n      status: 'create',\n      status2: 'counted',\n      status3: 'approved'\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.GestioneLavorazioni\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results2,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          selectionMode: \"single\",\n          cellSelection: true,\n          onCellSelect: this.onRowSelect,\n          responsiveLayout: \"scroll\",\n          fileNames: \"JobsIngresso\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.CambiaStato,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field\",\n          children: /*#__PURE__*/_jsxDEV(ModificaStato, {\n            result: this.state.result,\n            results: this.state.results\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog2,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(PanelMenu, {\n          className: \"panelMenuClass mb-2\",\n          model: this.items\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(PanelMenu, {\n          className: \"panelMenuClass mb-2\",\n          model: this.items2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(PanelMenu, {\n          className: \"panelMenuClass mb-2\",\n          model: this.items3\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 46\n            }, this), Costanti.cliente]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: this.state.selectedRetailer,\n            options: this.state.cliente,\n            onChange: e => this.filterClient(e),\n            optionLabel: \"label\",\n            placeholder: \"Seleziona cliente\",\n            emptyMessage: \"Non ci sono clienti disponibili\",\n            filterBy: \"label\",\n            filter: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default GestioneLavorazioni;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "Nav", "CustomDataTable", "ModificaStato", "Dialog", "<PERSON><PERSON>", "Sidebar", "PanelMenu", "Dropdown", "resp_magConfrontoDispQta", "resp_magControlloLottiEScadenze", "jsxDEV", "_jsxDEV", "GestioneLavorazioni", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onRowSelect", "result", "idFOROrd", "state", "results", "for<PERSON>ach", "element", "localStorage", "setItem", "JSON", "stringify", "type", "toLowerCase", "window", "location", "pathname", "results2", "results3", "<PERSON><PERSON><PERSON><PERSON>", "resultDialog", "resultDialog2", "warehouse", "cliente", "search", "loading", "filterClient", "e", "setState", "value", "finRes", "risultato", "data", "filter", "trim", "length", "push", "item", "cli", "i", "match", "el", "filterProd", "label", "dad", "tipo", "stato", "<PERSON><PERSON><PERSON><PERSON>", "items", "icon", "items2", "Stato", "items3", "<PERSON><PERSON><PERSON><PERSON>", "bind", "modifica", "hideDialog", "openFilter", "closeFilter", "defineSort", "reset", "resetDesc", "componentDidMount", "document", "then", "res", "_element$operator", "x", "number", "idDocument", "taskDate", "createAt", "documentDate", "erpSync", "operator", "idUser", "username", "idWarehouses", "warehouseName", "idRetailer", "idRegistry", "idSupplying", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "Set", "sort", "elementnull", "command", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fields", "field", "header", "NDoc", "body", "sortable", "showHeader", "Operatore", "DataTask", "<PERSON><PERSON><PERSON><PERSON>", "DataDoc", "actionFields", "name", "ControllaLav", "handler", "CambiaStato", "status2", "status3", "filterDnone", "ref", "dt", "dataKey", "paginator", "rows", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "selectionMode", "cellSelection", "onCellSelect", "responsiveLayout", "fileNames", "visible", "modal", "footer", "onHide", "position", "style", "<PERSON><PERSON><PERSON>", "model", "options", "onChange", "optionLabel", "placeholder", "emptyMessage", "filterBy"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/gestioneLavorazioni.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* OpJobsIngresso - operazioni sulle lavorazioni in entrata\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON>nti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport ModificaStato from \"../../aggiunta_dati/modificaStato\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { PanelMenu } from \"primereact/panelmenu\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { resp_magConfrontoDispQta, resp_magControlloLottiEScadenze } from \"../../components/route\";\n\nclass GestioneLavorazioni extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: '',\n        referente: '',\n        deliveryDestination: '',\n        orderDate: '',\n        deliveryDate: '',\n        termsPayment: '',\n        paymentStatus: '',\n        status: ''\n\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            selectedRetailer: null,\n            result: this.emptyResult,\n            resultDialog: false,\n            resultDialog2: false,\n            warehouse: [],\n            type: [],\n            cliente: [],\n            status: [],\n            search: '',\n            loading: true\n        }\n        /* Ricerca elementi per categoria selezionata */\n        this.filterClient = e => {\n            this.setState({\n                search: e.value,\n                selectedRetailer: e.value\n            });\n            var finRes = [];\n            var risultato = [];\n            var cliente = [];\n            var data = this.state.results3;\n            var filter = ''\n            if (e.value === 'ALTRO') {\n                filter = null\n            } else {\n                filter = e.value.trim().toLowerCase();\n            }\n            if (filter === null || filter.length > 0) {\n                data.forEach(element => {\n                    if (element.cliente !== null && element.cliente !== '') {\n                        cliente.push(element.cliente)\n                    } else {\n                        if (e.item.value === 'cliente') {\n                            finRes = data.filter(element => element.cliente === null || element.cliente === '');\n                        }\n                    }\n                })\n                var cli = cliente.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                if (cli.length > 0) {\n                    risultato = data.filter(element => element.cliente === cli[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                } if (finRes.length > 0) {\n                    this.setState({\n                        results2: finRes\n                    })\n                }\n                else {\n                    this.setState({\n                        results2: []\n                    })\n                }\n            } else {\n                this.setState({\n                    results2: this.state.results3\n                })\n            }\n        };\n        this.filterProd = e => {\n            this.setState({\n                search: e.item.label\n            });\n            var type = [];\n            var warehouse = [];\n            var finRes = [];\n            var risultato = [];\n            var status = [];\n            var data = this.state.results3;\n            var filter = ''\n            if (e.item.label === 'ALTRO') {\n                filter = null\n            } else {\n                filter = e.item.label.trim().toLowerCase();\n            }\n            if (filter === null || filter.length > 0) {\n                data.forEach(element => {\n                    if (element.type !== null && element.type !== '') {\n                        type.push(element.type)\n                    } else {\n                        if (e.item.value === 'type') {\n                            finRes = data.filter(element => element.type === null || element.type === '');\n                        }\n                    } if (element.status !== null && element.status !== '') {\n                        status.push(element.status)\n                    } else {\n                        if (e.item.value === 'status') {\n                            finRes = data.filter(element => element.type === e.item.dad && (element.status === null || element.status === ''));\n                        }\n                    }\n                    if (element.warehouse !== null && element.warehouse !== '') {\n                        warehouse.push(element.warehouse)\n                    } else {\n                        if (e.item.value === 'warehouse') {\n                            finRes = data.filter(element => element.type === e.item.dad && (element.warehouse === null || element.warehouse === ''));\n                        }\n                    }\n                })\n                var tipo = type.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                var stato = status.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                var magazzino = warehouse.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                if (tipo.length > 0) {\n                    risultato = data.filter(element => element.type === tipo[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                }\n                if (stato.length > 0) {\n                    risultato = data.filter(element => element.status === stato[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                }\n                if (magazzino.length > 0) {\n                    risultato = data.filter(element => element.warehouse === magazzino[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                }\n                if (finRes.length > 0) {\n                    this.setState({\n                        results2: finRes\n                    })\n                }\n                else {\n                    this.setState({\n                        results2: []\n                    })\n                }\n            } else {\n                this.setState({\n                    results2: this.state.results3\n                })\n            }\n        };\n        this.items = [{\n            label: Costanti.type,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.items2 = [{\n            label: Costanti.Stato,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.items3 = [{\n            label: Costanti.Magazzino,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.onRowSelect = this.onRowSelect.bind(this);\n        this.modifica = this.modifica.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n        this.defineSort = this.defineSort.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var document = [];\n        await APIRequest(\"GET\", \"tasks/\")\n            .then((res) => {\n                res.data.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.idDocument.number,\n                        status: element.status,\n                        type: element.idDocument.type,\n                        taskDate: element.createAt,\n                        createAt: element.idDocument.documentDate,\n                        erpSync: element.idDocument.erpSync === false ? 'Non trasmesso' : 'Trasmesso',\n                        operator: element.operator?.idUser.username,\n                        warehouse: element.idDocument.idWarehouses.warehouseName,\n                        cliente: element.idDocument.idRetailer !== null ? element.idDocument.idRetailer.idRegistry.firstName : element.idDocument.idSupplying !== null ? element.idDocument.idSupplying.idRegistry.firstName : 'Documento di inventario interno'\n                    }\n                    document.push(x);\n                })\n                this.setState({\n                    results: res.data,\n                    results2: document,\n                    results3: document,\n                    loading: false,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista delle task. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        this.defineSort();\n    }\n    /* Definiamo le categorie per il filtraggio  */\n    defineSort() {\n        var warehouse = []\n        var type = []\n        var cliente = []\n        var status = []\n        if (this.state.results2 !== null) {\n            this.state.results2.forEach(element => {\n                warehouse.push(element.warehouse)\n                type.push(element.type)\n                cliente.push(element.cliente)\n                status.push(element.status)\n            })\n            warehouse = [...new Set(warehouse)].sort();\n            type = [...new Set(type)].sort();\n            status = [...new Set(status)].sort();\n            cliente = [...new Set(cliente)].sort();\n            var elementnull = []\n            var cli = []\n            cliente.forEach(element => {\n                var x = { label: element, value: element }\n                cli.push(x)\n            })\n            type.forEach(element => {\n                if (element !== null && element !== '') {\n                    this.items[0].items.push({ label: element, command: (e) => { this.filterProd(e) } })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'type', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items[0].items.push(items)\n            })\n            status.forEach(element => {\n                if (element !== null && element !== '') {\n                    this.items2[0].items.push({ label: element, command: (e) => { this.filterProd(e) } })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'status', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items2[0].items.push(items)\n            })\n            warehouse.forEach(element => {\n                if (element !== null && element !== '') {\n                    this.items3[0].items.push({ label: element, command: (e) => { this.filterProd(e) } })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'warehouse', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items3[0].items.push(items)\n            })\n            this.setState({\n                warehouse: warehouse,\n                type: type,\n                cliente: cli,\n                status: status\n            })\n        }\n    }\n    onRowSelect = (result) => {\n        var idFOROrd = []\n        this.state.results.forEach(element => {\n            if (element.id === result.id) {\n                idFOROrd = element\n            }\n        })\n        localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n        if (result.type.toLowerCase() === 'for-ordine' || result.type.toLowerCase() === 'reso') {\n            window.location.pathname = resp_magControlloLottiEScadenze;\n        } else {\n            window.location.pathname = resp_magConfrontoDispQta;\n        }\n    }\n    modifica(result) {\n        this.setState({\n            result,\n            resultDialog: true\n        })\n    }\n    hideDialog() {\n        this.setState({\n            resultDialog: false\n        })\n    }\n    openFilter() {\n        this.setState({\n            resultDialog2: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog2: false\n        })\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    reset() {\n        this.setState({\n            results2: this.state.results3,\n            search: '',\n            selectedRetailer: null\n        })\n    }\n    /* Reselt filtro categorie */\n    resetDesc() {\n        this.setState({\n            results2: this.state.results3,\n            search: '',\n            selectedRetailer: null\n        })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"cliente\",\n                header: Costanti.cliente,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"operator\",\n                header: Costanti.Operatore,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"warehouse\",\n                header: Costanti.Magazzino,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"status\",\n                header: Costanti.Stato,\n                body: 'statDoc',\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                body: \"typeDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"taskDate\",\n                header: Costanti.DataTask,\n                body: \"taskDate2\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: Costanti.Trasmetti,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"createAt\",\n                header: Costanti.DataDoc,\n                body: 'createAt',\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.ControllaLav, icon: <i className=\"pi pi-search\" />, handler: this.onRowSelect },\n            { name: Costanti.CambiaStato, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.modifica, status: 'create', status2: 'counted', status3: 'approved' },\n        ];\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '') {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.GestioneLavorazioni}</h1>\n                </div>\n                <div className=\"card\">\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results2}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        paginator\n                        rows={20}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        selectionMode=\"single\"\n                        cellSelection={true}\n                        onCellSelect={this.onRowSelect}\n                        responsiveLayout=\"scroll\"\n                        fileNames=\"JobsIngresso\"\n                    />\n                </div>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={Costanti.CambiaStato}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hideDialog}\n                >\n                    <div className=\"p-field\">\n                        <ModificaStato result={this.state.result} results={this.state.results} />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog2} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <PanelMenu className=\"panelMenuClass mb-2\" model={this.items} />\n                    <PanelMenu className=\"panelMenuClass mb-2\" model={this.items2} />\n                    <PanelMenu className=\"panelMenuClass mb-2\" model={this.items3} />\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.cliente}</h5>\n                        <hr></hr>\n                        <Dropdown value={this.state.selectedRetailer} options={this.state.cliente} onChange={(e) => this.filterClient(e)} optionLabel=\"label\" placeholder=\"Seleziona cliente\" emptyMessage=\"Non ci sono clienti disponibili\" filterBy='label' filter />\n                    </div>\n                </Sidebar>\n            </div>\n        )\n    }\n}\n\nexport default GestioneLavorazioni;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,wBAAwB,EAAEC,+BAA+B,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,mBAAmB,SAAShB,SAAS,CAAC;EAcxCiB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAdhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IAEZ,CAAC;IAAA,KAiRDC,WAAW,GAAIC,MAAM,IAAK;MACtB,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIA,OAAO,CAACf,EAAE,KAAKU,MAAM,CAACV,EAAE,EAAE;UAC1BW,QAAQ,GAAGI,OAAO;QACtB;MACJ,CAAC,CAAC;MACFC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAAC,CAAC;MAC5D,IAAID,MAAM,CAACU,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,YAAY,IAAIX,MAAM,CAACU,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,MAAM,EAAE;QACpFC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG/B,+BAA+B;MAC9D,CAAC,MAAM;QACH6B,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGhC,wBAAwB;MACvD;IACJ,CAAC;IA3RG,IAAI,CAACoB,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbY,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE,IAAI;MACtBjB,MAAM,EAAE,IAAI,CAACX,WAAW;MACxB6B,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,EAAE;MACbV,IAAI,EAAE,EAAE;MACRW,OAAO,EAAE,EAAE;MACXvB,MAAM,EAAE,EAAE;MACVwB,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE;IACb,CAAC;IACD;IACA,IAAI,CAACC,YAAY,GAAGC,CAAC,IAAI;MACrB,IAAI,CAACC,QAAQ,CAAC;QACVJ,MAAM,EAAEG,CAAC,CAACE,KAAK;QACfV,gBAAgB,EAAEQ,CAAC,CAACE;MACxB,CAAC,CAAC;MACF,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIR,OAAO,GAAG,EAAE;MAChB,IAAIS,IAAI,GAAG,IAAI,CAAC5B,KAAK,CAACc,QAAQ;MAC9B,IAAIe,MAAM,GAAG,EAAE;MACf,IAAIN,CAAC,CAACE,KAAK,KAAK,OAAO,EAAE;QACrBI,MAAM,GAAG,IAAI;MACjB,CAAC,MAAM;QACHA,MAAM,GAAGN,CAAC,CAACE,KAAK,CAACK,IAAI,CAAC,CAAC,CAACrB,WAAW,CAAC,CAAC;MACzC;MACA,IAAIoB,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QACtCH,IAAI,CAAC1B,OAAO,CAACC,OAAO,IAAI;UACpB,IAAIA,OAAO,CAACgB,OAAO,KAAK,IAAI,IAAIhB,OAAO,CAACgB,OAAO,KAAK,EAAE,EAAE;YACpDA,OAAO,CAACa,IAAI,CAAC7B,OAAO,CAACgB,OAAO,CAAC;UACjC,CAAC,MAAM;YACH,IAAII,CAAC,CAACU,IAAI,CAACR,KAAK,KAAK,SAAS,EAAE;cAC5BC,MAAM,GAAGE,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACgB,OAAO,KAAK,IAAI,IAAIhB,OAAO,CAACgB,OAAO,KAAK,EAAE,CAAC;YACvF;UACJ;QACJ,CAAC,CAAC;QACF,IAAIe,GAAG,GAAGf,OAAO,CAACU,MAAM,CAAC,UAAUM,CAAC,EAAE;UAClC,OAAOA,CAAC,CAAC1B,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIK,GAAG,CAACH,MAAM,GAAG,CAAC,EAAE;UAChBJ,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACgB,OAAO,KAAKe,GAAG,CAAC,CAAC,CAAC,CAAC;UAC9DP,SAAS,CAACzB,OAAO,CAACmC,EAAE,IAAI;YACpBX,MAAM,CAACM,IAAI,CAACK,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QAAE,IAAIX,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;UACrB,IAAI,CAACP,QAAQ,CAAC;YACVX,QAAQ,EAAEa;UACd,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAI,CAACF,QAAQ,CAAC;YACVX,QAAQ,EAAE;UACd,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,CAACW,QAAQ,CAAC;UACVX,QAAQ,EAAE,IAAI,CAACb,KAAK,CAACc;QACzB,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACwB,UAAU,GAAGf,CAAC,IAAI;MACnB,IAAI,CAACC,QAAQ,CAAC;QACVJ,MAAM,EAAEG,CAAC,CAACU,IAAI,CAACM;MACnB,CAAC,CAAC;MACF,IAAI/B,IAAI,GAAG,EAAE;MACb,IAAIU,SAAS,GAAG,EAAE;MAClB,IAAIQ,MAAM,GAAG,EAAE;MACf,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAI/B,MAAM,GAAG,EAAE;MACf,IAAIgC,IAAI,GAAG,IAAI,CAAC5B,KAAK,CAACc,QAAQ;MAC9B,IAAIe,MAAM,GAAG,EAAE;MACf,IAAIN,CAAC,CAACU,IAAI,CAACM,KAAK,KAAK,OAAO,EAAE;QAC1BV,MAAM,GAAG,IAAI;MACjB,CAAC,MAAM;QACHA,MAAM,GAAGN,CAAC,CAACU,IAAI,CAACM,KAAK,CAACT,IAAI,CAAC,CAAC,CAACrB,WAAW,CAAC,CAAC;MAC9C;MACA,IAAIoB,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QACtCH,IAAI,CAAC1B,OAAO,CAACC,OAAO,IAAI;UACpB,IAAIA,OAAO,CAACK,IAAI,KAAK,IAAI,IAAIL,OAAO,CAACK,IAAI,KAAK,EAAE,EAAE;YAC9CA,IAAI,CAACwB,IAAI,CAAC7B,OAAO,CAACK,IAAI,CAAC;UAC3B,CAAC,MAAM;YACH,IAAIe,CAAC,CAACU,IAAI,CAACR,KAAK,KAAK,MAAM,EAAE;cACzBC,MAAM,GAAGE,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACK,IAAI,KAAK,IAAI,IAAIL,OAAO,CAACK,IAAI,KAAK,EAAE,CAAC;YACjF;UACJ;UAAE,IAAIL,OAAO,CAACP,MAAM,KAAK,IAAI,IAAIO,OAAO,CAACP,MAAM,KAAK,EAAE,EAAE;YACpDA,MAAM,CAACoC,IAAI,CAAC7B,OAAO,CAACP,MAAM,CAAC;UAC/B,CAAC,MAAM;YACH,IAAI2B,CAAC,CAACU,IAAI,CAACR,KAAK,KAAK,QAAQ,EAAE;cAC3BC,MAAM,GAAGE,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACK,IAAI,KAAKe,CAAC,CAACU,IAAI,CAACO,GAAG,KAAKrC,OAAO,CAACP,MAAM,KAAK,IAAI,IAAIO,OAAO,CAACP,MAAM,KAAK,EAAE,CAAC,CAAC;YACtH;UACJ;UACA,IAAIO,OAAO,CAACe,SAAS,KAAK,IAAI,IAAIf,OAAO,CAACe,SAAS,KAAK,EAAE,EAAE;YACxDA,SAAS,CAACc,IAAI,CAAC7B,OAAO,CAACe,SAAS,CAAC;UACrC,CAAC,MAAM;YACH,IAAIK,CAAC,CAACU,IAAI,CAACR,KAAK,KAAK,WAAW,EAAE;cAC9BC,MAAM,GAAGE,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACK,IAAI,KAAKe,CAAC,CAACU,IAAI,CAACO,GAAG,KAAKrC,OAAO,CAACe,SAAS,KAAK,IAAI,IAAIf,OAAO,CAACe,SAAS,KAAK,EAAE,CAAC,CAAC;YAC5H;UACJ;QACJ,CAAC,CAAC;QACF,IAAIuB,IAAI,GAAGjC,IAAI,CAACqB,MAAM,CAAC,UAAUM,CAAC,EAAE;UAChC,OAAOA,CAAC,CAAC1B,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIa,KAAK,GAAG9C,MAAM,CAACiC,MAAM,CAAC,UAAUM,CAAC,EAAE;UACnC,OAAOA,CAAC,CAAC1B,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIc,SAAS,GAAGzB,SAAS,CAACW,MAAM,CAAC,UAAUM,CAAC,EAAE;UAC1C,OAAOA,CAAC,CAAC1B,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIY,IAAI,CAACV,MAAM,GAAG,CAAC,EAAE;UACjBJ,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACK,IAAI,KAAKiC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC5Dd,SAAS,CAACzB,OAAO,CAACmC,EAAE,IAAI;YACpBX,MAAM,CAACM,IAAI,CAACK,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QACA,IAAIK,KAAK,CAACX,MAAM,GAAG,CAAC,EAAE;UAClBJ,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACP,MAAM,KAAK8C,KAAK,CAAC,CAAC,CAAC,CAAC;UAC/Df,SAAS,CAACzB,OAAO,CAACmC,EAAE,IAAI;YACpBX,MAAM,CAACM,IAAI,CAACK,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QACA,IAAIM,SAAS,CAACZ,MAAM,GAAG,CAAC,EAAE;UACtBJ,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC1B,OAAO,IAAIA,OAAO,CAACe,SAAS,KAAKyB,SAAS,CAAC,CAAC,CAAC,CAAC;UACtEhB,SAAS,CAACzB,OAAO,CAACmC,EAAE,IAAI;YACpBX,MAAM,CAACM,IAAI,CAACK,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QACA,IAAIX,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;UACnB,IAAI,CAACP,QAAQ,CAAC;YACVX,QAAQ,EAAEa;UACd,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAI,CAACF,QAAQ,CAAC;YACVX,QAAQ,EAAE;UACd,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,CAACW,QAAQ,CAAC;UACVX,QAAQ,EAAE,IAAI,CAACb,KAAK,CAACc;QACzB,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAAC8B,KAAK,GAAG,CAAC;MACVL,KAAK,EAAErE,QAAQ,CAACsC,IAAI;MACpBqC,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACE,MAAM,GAAG,CAAC;MACXP,KAAK,EAAErE,QAAQ,CAAC6E,KAAK;MACrBF,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACI,MAAM,GAAG,CAAC;MACXT,KAAK,EAAErE,QAAQ,CAAC+E,SAAS;MACzBJ,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAAC/C,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,WAAW,GAAG,IAAI,CAACA,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,GAAG,IAAI,CAACA,UAAU,CAACL,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACM,KAAK,GAAG,IAAI,CAACA,KAAK,CAACN,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACO,SAAS,GAAG,IAAI,CAACA,SAAS,CAACP,IAAI,CAAC,IAAI,CAAC;EAC9C;EACA;EACA,MAAMQ,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,QAAQ,GAAG,EAAE;IACjB,MAAMxF,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,CAC5ByF,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACjC,IAAI,CAAC1B,OAAO,CAACC,OAAO,IAAI;QAAA,IAAA2D,iBAAA;QACxB,IAAIC,CAAC,GAAG;UACJ3E,EAAE,EAAEe,OAAO,CAACf,EAAE;UACd4E,MAAM,EAAE7D,OAAO,CAAC8D,UAAU,CAACD,MAAM;UACjCpE,MAAM,EAAEO,OAAO,CAACP,MAAM;UACtBY,IAAI,EAAEL,OAAO,CAAC8D,UAAU,CAACzD,IAAI;UAC7B0D,QAAQ,EAAE/D,OAAO,CAACgE,QAAQ;UAC1BA,QAAQ,EAAEhE,OAAO,CAAC8D,UAAU,CAACG,YAAY;UACzCC,OAAO,EAAElE,OAAO,CAAC8D,UAAU,CAACI,OAAO,KAAK,KAAK,GAAG,eAAe,GAAG,WAAW;UAC7EC,QAAQ,GAAAR,iBAAA,GAAE3D,OAAO,CAACmE,QAAQ,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBS,MAAM,CAACC,QAAQ;UAC3CtD,SAAS,EAAEf,OAAO,CAAC8D,UAAU,CAACQ,YAAY,CAACC,aAAa;UACxDvD,OAAO,EAAEhB,OAAO,CAAC8D,UAAU,CAACU,UAAU,KAAK,IAAI,GAAGxE,OAAO,CAAC8D,UAAU,CAACU,UAAU,CAACC,UAAU,CAACvF,SAAS,GAAGc,OAAO,CAAC8D,UAAU,CAACY,WAAW,KAAK,IAAI,GAAG1E,OAAO,CAAC8D,UAAU,CAACY,WAAW,CAACD,UAAU,CAACvF,SAAS,GAAG;QAC3M,CAAC;QACDsE,QAAQ,CAAC3B,IAAI,CAAC+B,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,IAAI,CAACvC,QAAQ,CAAC;QACVvB,OAAO,EAAE4D,GAAG,CAACjC,IAAI;QACjBf,QAAQ,EAAE8C,QAAQ;QAClB7C,QAAQ,EAAE6C,QAAQ;QAClBtC,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDyD,KAAK,CAAEvD,CAAC,IAAK;MAAA,IAAAwD,WAAA,EAAAC,YAAA;MACVC,OAAO,CAACC,GAAG,CAAC3D,CAAC,CAAC;MACd,IAAI,CAAC4D,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,kFAAAC,MAAA,CAA+E,EAAAT,WAAA,GAAAxD,CAAC,CAACkE,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYnD,IAAI,MAAK8D,SAAS,IAAAV,YAAA,GAAGzD,CAAC,CAACkE,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYpD,IAAI,GAAGL,CAAC,CAACoE,OAAO,CAAE;QACpJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAI,CAACrC,UAAU,CAAC,CAAC;EACrB;EACA;EACAA,UAAUA,CAAA,EAAG;IACT,IAAIrC,SAAS,GAAG,EAAE;IAClB,IAAIV,IAAI,GAAG,EAAE;IACb,IAAIW,OAAO,GAAG,EAAE;IAChB,IAAIvB,MAAM,GAAG,EAAE;IACf,IAAI,IAAI,CAACI,KAAK,CAACa,QAAQ,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACb,KAAK,CAACa,QAAQ,CAACX,OAAO,CAACC,OAAO,IAAI;QACnCe,SAAS,CAACc,IAAI,CAAC7B,OAAO,CAACe,SAAS,CAAC;QACjCV,IAAI,CAACwB,IAAI,CAAC7B,OAAO,CAACK,IAAI,CAAC;QACvBW,OAAO,CAACa,IAAI,CAAC7B,OAAO,CAACgB,OAAO,CAAC;QAC7BvB,MAAM,CAACoC,IAAI,CAAC7B,OAAO,CAACP,MAAM,CAAC;MAC/B,CAAC,CAAC;MACFsB,SAAS,GAAG,CAAC,GAAG,IAAI2E,GAAG,CAAC3E,SAAS,CAAC,CAAC,CAAC4E,IAAI,CAAC,CAAC;MAC1CtF,IAAI,GAAG,CAAC,GAAG,IAAIqF,GAAG,CAACrF,IAAI,CAAC,CAAC,CAACsF,IAAI,CAAC,CAAC;MAChClG,MAAM,GAAG,CAAC,GAAG,IAAIiG,GAAG,CAACjG,MAAM,CAAC,CAAC,CAACkG,IAAI,CAAC,CAAC;MACpC3E,OAAO,GAAG,CAAC,GAAG,IAAI0E,GAAG,CAAC1E,OAAO,CAAC,CAAC,CAAC2E,IAAI,CAAC,CAAC;MACtC,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAI7D,GAAG,GAAG,EAAE;MACZf,OAAO,CAACjB,OAAO,CAACC,OAAO,IAAI;QACvB,IAAI4D,CAAC,GAAG;UAAExB,KAAK,EAAEpC,OAAO;UAAEsB,KAAK,EAAEtB;QAAQ,CAAC;QAC1C+B,GAAG,CAACF,IAAI,CAAC+B,CAAC,CAAC;MACf,CAAC,CAAC;MACFvD,IAAI,CAACN,OAAO,CAACC,OAAO,IAAI;QACpB,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,EAAE,EAAE;UACpC,IAAI,CAACyC,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACZ,IAAI,CAAC;YAAEO,KAAK,EAAEpC,OAAO;YAAE6F,OAAO,EAAGzE,CAAC,IAAK;cAAE,IAAI,CAACe,UAAU,CAACf,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACxF,CAAC,MAAM;UACHwE,WAAW,CAAC/D,IAAI,CAAC;YAAEO,KAAK,EAAE,OAAO;YAAEd,KAAK,EAAE,MAAM;YAAEuE,OAAO,EAAGzE,CAAC,IAAK;cAAE,IAAI,CAACe,UAAU,CAACf,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QAC/F;MACJ,CAAC,CAAC;MACFwE,WAAW,CAAC7F,OAAO,CAAC0C,KAAK,IAAI;QACzB,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACZ,IAAI,CAACY,KAAK,CAAC;MACnC,CAAC,CAAC;MACFhD,MAAM,CAACM,OAAO,CAACC,OAAO,IAAI;QACtB,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,EAAE,EAAE;UACpC,IAAI,CAAC2C,MAAM,CAAC,CAAC,CAAC,CAACF,KAAK,CAACZ,IAAI,CAAC;YAAEO,KAAK,EAAEpC,OAAO;YAAE6F,OAAO,EAAGzE,CAAC,IAAK;cAAE,IAAI,CAACe,UAAU,CAACf,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACzF,CAAC,MAAM;UACHwE,WAAW,CAAC/D,IAAI,CAAC;YAAEO,KAAK,EAAE,OAAO;YAAEd,KAAK,EAAE,QAAQ;YAAEuE,OAAO,EAAGzE,CAAC,IAAK;cAAE,IAAI,CAACe,UAAU,CAACf,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACjG;MACJ,CAAC,CAAC;MACFwE,WAAW,CAAC7F,OAAO,CAAC0C,KAAK,IAAI;QACzB,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACF,KAAK,CAACZ,IAAI,CAACY,KAAK,CAAC;MACpC,CAAC,CAAC;MACF1B,SAAS,CAAChB,OAAO,CAACC,OAAO,IAAI;QACzB,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,EAAE,EAAE;UACpC,IAAI,CAAC6C,MAAM,CAAC,CAAC,CAAC,CAACJ,KAAK,CAACZ,IAAI,CAAC;YAAEO,KAAK,EAAEpC,OAAO;YAAE6F,OAAO,EAAGzE,CAAC,IAAK;cAAE,IAAI,CAACe,UAAU,CAACf,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACzF,CAAC,MAAM;UACHwE,WAAW,CAAC/D,IAAI,CAAC;YAAEO,KAAK,EAAE,OAAO;YAAEd,KAAK,EAAE,WAAW;YAAEuE,OAAO,EAAGzE,CAAC,IAAK;cAAE,IAAI,CAACe,UAAU,CAACf,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACpG;MACJ,CAAC,CAAC;MACFwE,WAAW,CAAC7F,OAAO,CAAC0C,KAAK,IAAI;QACzB,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC,CAACJ,KAAK,CAACZ,IAAI,CAACY,KAAK,CAAC;MACpC,CAAC,CAAC;MACF,IAAI,CAACpB,QAAQ,CAAC;QACVN,SAAS,EAAEA,SAAS;QACpBV,IAAI,EAAEA,IAAI;QACVW,OAAO,EAAEe,GAAG;QACZtC,MAAM,EAAEA;MACZ,CAAC,CAAC;IACN;EACJ;EAeAuD,QAAQA,CAACrD,MAAM,EAAE;IACb,IAAI,CAAC0B,QAAQ,CAAC;MACV1B,MAAM;MACNkB,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAoC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC5B,QAAQ,CAAC;MACVR,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAqC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC7B,QAAQ,CAAC;MACVP,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAqC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,QAAQ,CAAC;MACVP,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACAuC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAChC,QAAQ,CAAC;MACVX,QAAQ,EAAE,IAAI,CAACb,KAAK,CAACc,QAAQ;MAC7BM,MAAM,EAAE,EAAE;MACVL,gBAAgB,EAAE;IACtB,CAAC,CAAC;EACN;EACA;EACA0C,SAASA,CAAA,EAAG;IACR,IAAI,CAACjC,QAAQ,CAAC;MACVX,QAAQ,EAAE,IAAI,CAACb,KAAK,CAACc,QAAQ;MAC7BM,MAAM,EAAE,EAAE;MACVL,gBAAgB,EAAE;IACtB,CAAC,CAAC;EACN;EACAkF,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBnH,OAAA,CAAChB,KAAK,CAACoI,QAAQ;MAAAC,QAAA,eACXrH,OAAA,CAACP,MAAM;QAAC6H,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAClD,UAAW;QAAAgD,QAAA,GACjE,GAAG,EACHlI,QAAQ,CAACqI,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMC,MAAM,GAAG,CACX;MACIC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE5I,QAAQ,CAAC6I,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE5I,QAAQ,CAACiD,OAAO;MACxB8F,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE5I,QAAQ,CAACiJ,SAAS;MAC1BF,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE5I,QAAQ,CAAC+E,SAAS;MAC1BgE,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE5I,QAAQ,CAAC6E,KAAK;MACtBiE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE5I,QAAQ,CAACsC,IAAI;MACrBwG,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE5I,QAAQ,CAACkJ,QAAQ;MACzBJ,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE5I,QAAQ,CAACmJ,SAAS;MAC1BJ,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIL,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE5I,QAAQ,CAACoJ,OAAO;MACxBN,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMK,YAAY,GAAG,CACjB;MAAEC,IAAI,EAAEtJ,QAAQ,CAACuJ,YAAY;MAAE5E,IAAI,eAAE9D,OAAA;QAAGsH,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEe,OAAO,EAAE,IAAI,CAAC7H;IAAY,CAAC,EAChG;MAAE2H,IAAI,EAAEtJ,QAAQ,CAACyJ,WAAW;MAAE9E,IAAI,eAAE9D,OAAA;QAAGsH,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEe,OAAO,EAAE,IAAI,CAACvE,QAAQ;MAAEvD,MAAM,EAAE,QAAQ;MAAEgI,OAAO,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAW,CAAC,CAC9J;IACD,IAAIC,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAAC9H,KAAK,CAACoB,MAAM,KAAK,EAAE,EAAE;MAC1B0G,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACI/I,OAAA;MAAKsH,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CrH,OAAA,CAACd,KAAK;QAAC8J,GAAG,EAAG1F,EAAE,IAAK,IAAI,CAAC8C,KAAK,GAAG9C;MAAG;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvC5H,OAAA,CAACX,GAAG;QAAAoI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP5H,OAAA;QAAKsH,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCrH,OAAA;UAAAqH,QAAA,EAAKlI,QAAQ,CAACc;QAAmB;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACN5H,OAAA;QAAKsH,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBrH,OAAA,CAACV,eAAe;UACZ0J,GAAG,EAAG1F,EAAE,IAAK,IAAI,CAAC2F,EAAE,GAAG3F,EAAG;UAC1BZ,KAAK,EAAE,IAAI,CAACzB,KAAK,CAACa,QAAS;UAC3B+F,MAAM,EAAEA,MAAO;UACfvF,OAAO,EAAE,IAAI,CAACrB,KAAK,CAACqB,OAAQ;UAC5B4G,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEd,YAAa;UAC5Be,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BC,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAACpF,UAAW;UACnCqF,gBAAgB,eAAE3J,OAAA;YAAUsH,SAAS,EAAC,MAAM;YAACmB,IAAI,EAAC;UAAgB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/EgC,OAAO,EAAC,QAAQ;UAChBC,aAAa,EAAC,QAAQ;UACtBC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAACjJ,WAAY;UAC/BkJ,gBAAgB,EAAC,QAAQ;UACzBC,SAAS,EAAC;QAAc;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN5H,OAAA,CAACR,MAAM;QACH0K,OAAO,EAAE,IAAI,CAACjJ,KAAK,CAACgB,YAAa;QACjC8F,MAAM,EAAE5I,QAAQ,CAACyJ,WAAY;QAC7BuB,KAAK;QACL7C,SAAS,EAAC,kBAAkB;QAC5B8C,MAAM,EAAEjD,kBAAmB;QAC3BkD,MAAM,EAAE,IAAI,CAAChG,UAAW;QAAAgD,QAAA,eAExBrH,OAAA;UAAKsH,SAAS,EAAC,SAAS;UAAAD,QAAA,eACpBrH,OAAA,CAACT,aAAa;YAACwB,MAAM,EAAE,IAAI,CAACE,KAAK,CAACF,MAAO;YAACG,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC;UAAQ;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT5H,OAAA,CAACN,OAAO;QAACwK,OAAO,EAAE,IAAI,CAACjJ,KAAK,CAACiB,aAAc;QAACoI,QAAQ,EAAC,MAAM;QAACD,MAAM,EAAE,IAAI,CAAC9F,WAAY;QAAA8C,QAAA,gBACjFrH,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACiH,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKrH,OAAA;YAAGsH,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C5H,OAAA;YAAIsH,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACrH,OAAA;cAAGsH,SAAS,EAAC,mBAAmB;cAACiD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACzI,QAAQ,CAACqL,MAAM;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G5H,OAAA,CAACP,MAAM;YAACY,EAAE,EAAC,iBAAiB;YAACiH,SAAS,EAAEyB,WAAY;YAACxB,OAAO,EAAE,IAAI,CAAC9C,KAAM;YAAA4C,QAAA,gBAACrH,OAAA;cAAGsH,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA5H,OAAA;cAAAqH,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN5H,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACiH,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DrH,OAAA;YAAIsH,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACrH,OAAA;cAAGsH,SAAS,EAAC,mBAAmB;cAACiD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACzI,QAAQ,CAACqL,MAAM;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G5H,OAAA,CAACP,MAAM;YAACY,EAAE,EAAC,kBAAkB;YAACiH,SAAS,EAAEyB,WAAY;YAACxB,OAAO,EAAE,IAAI,CAAC7C,SAAU;YAAA2C,QAAA,gBAACrH,OAAA;cAAGsH,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA5H,OAAA;cAAAqH,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN5H,OAAA;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5H,OAAA,CAACL,SAAS;UAAC2H,SAAS,EAAC,qBAAqB;UAACmD,KAAK,EAAE,IAAI,CAAC5G;QAAM;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChE5H,OAAA,CAACL,SAAS;UAAC2H,SAAS,EAAC,qBAAqB;UAACmD,KAAK,EAAE,IAAI,CAAC1G;QAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE5H,OAAA,CAACL,SAAS;UAAC2H,SAAS,EAAC,qBAAqB;UAACmD,KAAK,EAAE,IAAI,CAACxG;QAAO;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE5H,OAAA;UAAKsH,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DrH,OAAA;YAAIsH,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACrH,OAAA;cAAGsH,SAAS,EAAC,iBAAiB;cAACiD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACzI,QAAQ,CAACiD,OAAO;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9G5H,OAAA;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5H,OAAA,CAACJ,QAAQ;YAAC8C,KAAK,EAAE,IAAI,CAACzB,KAAK,CAACe,gBAAiB;YAAC0I,OAAO,EAAE,IAAI,CAACzJ,KAAK,CAACmB,OAAQ;YAACuI,QAAQ,EAAGnI,CAAC,IAAK,IAAI,CAACD,YAAY,CAACC,CAAC,CAAE;YAACoI,WAAW,EAAC,OAAO;YAACC,WAAW,EAAC,mBAAmB;YAACC,YAAY,EAAC,iCAAiC;YAACC,QAAQ,EAAC,OAAO;YAACjI,MAAM;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9O,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe3H,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}