{"ast": null, "code": "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar symbolFor = Symbol && Symbol['for'];\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n    var description = 'Symbol.' + name;\n    if (NATIVE_SYMBOL && hasOwn(Symbol, name)) {\n      WellKnownSymbolsStore[name] = Symbol[name];\n    } else if (USE_SYMBOL_AS_UID && symbolFor) {\n      WellKnownSymbolsStore[name] = symbolFor(description);\n    } else {\n      WellKnownSymbolsStore[name] = createWellKnownSymbol(description);\n    }\n  }\n  return WellKnownSymbolsStore[name];\n};", "map": {"version": 3, "names": ["global", "require", "shared", "hasOwn", "uid", "NATIVE_SYMBOL", "USE_SYMBOL_AS_UID", "WellKnownSymbolsStore", "Symbol", "symbolFor", "createWellKnownSymbol", "withoutSetter", "module", "exports", "name", "description"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/well-known-symbol.js"], "sourcesContent": ["var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar symbolFor = Symbol && Symbol['for'];\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n    var description = 'Symbol.' + name;\n    if (NATIVE_SYMBOL && hasOwn(Symbol, name)) {\n      WellKnownSymbolsStore[name] = Symbol[name];\n    } else if (USE_SYMBOL_AS_UID && symbolFor) {\n      WellKnownSymbolsStore[name] = symbolFor(description);\n    } else {\n      WellKnownSymbolsStore[name] = createWellKnownSymbol(description);\n    }\n  } return WellKnownSymbolsStore[name];\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIE,MAAM,GAAGF,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIG,GAAG,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACrC,IAAII,aAAa,GAAGJ,OAAO,CAAC,4BAA4B,CAAC;AACzD,IAAIK,iBAAiB,GAAGL,OAAO,CAAC,gCAAgC,CAAC;AAEjE,IAAIM,qBAAqB,GAAGL,MAAM,CAAC,KAAK,CAAC;AACzC,IAAIM,MAAM,GAAGR,MAAM,CAACQ,MAAM;AAC1B,IAAIC,SAAS,GAAGD,MAAM,IAAIA,MAAM,CAAC,KAAK,CAAC;AACvC,IAAIE,qBAAqB,GAAGJ,iBAAiB,GAAGE,MAAM,GAAGA,MAAM,IAAIA,MAAM,CAACG,aAAa,IAAIP,GAAG;AAE9FQ,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAI,CAACX,MAAM,CAACI,qBAAqB,EAAEO,IAAI,CAAC,IAAI,EAAET,aAAa,IAAI,OAAOE,qBAAqB,CAACO,IAAI,CAAC,IAAI,QAAQ,CAAC,EAAE;IAC9G,IAAIC,WAAW,GAAG,SAAS,GAAGD,IAAI;IAClC,IAAIT,aAAa,IAAIF,MAAM,CAACK,MAAM,EAAEM,IAAI,CAAC,EAAE;MACzCP,qBAAqB,CAACO,IAAI,CAAC,GAAGN,MAAM,CAACM,IAAI,CAAC;IAC5C,CAAC,MAAM,IAAIR,iBAAiB,IAAIG,SAAS,EAAE;MACzCF,qBAAqB,CAACO,IAAI,CAAC,GAAGL,SAAS,CAACM,WAAW,CAAC;IACtD,CAAC,MAAM;MACLR,qBAAqB,CAACO,IAAI,CAAC,GAAGJ,qBAAqB,CAACK,WAAW,CAAC;IAClE;EACF;EAAE,OAAOR,qBAAqB,CAACO,IAAI,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}