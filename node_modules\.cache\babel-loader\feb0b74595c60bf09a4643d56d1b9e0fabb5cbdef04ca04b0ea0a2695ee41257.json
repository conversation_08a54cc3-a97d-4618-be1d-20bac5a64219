{"ast": null, "code": "import * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nexport var useLayoutEffect = canUseDom() ? React.useLayoutEffect : React.useEffect;", "map": {"version": 3, "names": ["React", "canUseDom", "useLayoutEffect", "useEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-input-number/es/hooks/useLayoutEffect.js"], "sourcesContent": ["import * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nexport var useLayoutEffect = canUseDom() ? React.useLayoutEffect : React.useEffect;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,IAAIC,eAAe,GAAGD,SAAS,CAAC,CAAC,GAAGD,KAAK,CAACE,eAAe,GAAGF,KAAK,CAACG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}