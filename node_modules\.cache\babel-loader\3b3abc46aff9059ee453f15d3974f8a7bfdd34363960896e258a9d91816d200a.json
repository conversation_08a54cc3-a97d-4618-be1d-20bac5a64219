{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\n\n/* eslint-disable no-lonely-if */\n\n/**\n * Legacy code. Should avoid to use if you are new to import these code.\n */\nimport React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport TreeNode from './TreeNode';\nexport function arrDel(list, value) {\n  if (!list) return [];\n  var clone = list.slice();\n  var index = clone.indexOf(value);\n  if (index >= 0) {\n    clone.splice(index, 1);\n  }\n  return clone;\n}\nexport function arrAdd(list, value) {\n  var clone = (list || []).slice();\n  if (clone.indexOf(value) === -1) {\n    clone.push(value);\n  }\n  return clone;\n}\nexport function posToArr(pos) {\n  return pos.split('-');\n}\nexport function getPosition(level, index) {\n  return \"\".concat(level, \"-\").concat(index);\n}\nexport function isTreeNode(node) {\n  return node && node.type && node.type.isTreeNode;\n}\nexport function getDragChildrenKeys(dragNodeKey, keyEntities) {\n  // not contains self\n  // self for left or right drag\n  var dragChildrenKeys = [];\n  var entity = keyEntities[dragNodeKey];\n  function dig() {\n    var list = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    list.forEach(function (_ref) {\n      var key = _ref.key,\n        children = _ref.children;\n      dragChildrenKeys.push(key);\n      dig(children);\n    });\n  }\n  dig(entity.children);\n  return dragChildrenKeys;\n}\nexport function isLastChild(treeNodeEntity) {\n  if (treeNodeEntity.parent) {\n    var posArr = posToArr(treeNodeEntity.pos);\n    return Number(posArr[posArr.length - 1]) === treeNodeEntity.parent.children.length - 1;\n  }\n  return false;\n}\nexport function isFirstChild(treeNodeEntity) {\n  var posArr = posToArr(treeNodeEntity.pos);\n  return Number(posArr[posArr.length - 1]) === 0;\n} // Only used when drag, not affect SSR.\n\nexport function calcDropPosition(event, dragNode, targetNode, indent, startMousePosition, allowDrop, flattenedNodes, keyEntities, expandKeys, direction) {\n  var _abstractDropNodeEnti;\n  var clientX = event.clientX,\n    clientY = event.clientY;\n  var _event$target$getBoun = event.target.getBoundingClientRect(),\n    top = _event$target$getBoun.top,\n    height = _event$target$getBoun.height; // optional chain for testing\n\n  var horizontalMouseOffset = (direction === 'rtl' ? -1 : 1) * (((startMousePosition === null || startMousePosition === void 0 ? void 0 : startMousePosition.x) || 0) - clientX);\n  var rawDropLevelOffset = (horizontalMouseOffset - 12) / indent; // find abstract drop node by horizontal offset\n\n  var abstractDropNodeEntity = keyEntities[targetNode.props.eventKey];\n  if (clientY < top + height / 2) {\n    // first half, set abstract drop node to previous node\n    var nodeIndex = flattenedNodes.findIndex(function (flattenedNode) {\n      return flattenedNode.key === abstractDropNodeEntity.key;\n    });\n    var prevNodeIndex = nodeIndex <= 0 ? 0 : nodeIndex - 1;\n    var prevNodeKey = flattenedNodes[prevNodeIndex].key;\n    abstractDropNodeEntity = keyEntities[prevNodeKey];\n  }\n  var initialAbstractDropNodeKey = abstractDropNodeEntity.key;\n  var abstractDragOverEntity = abstractDropNodeEntity;\n  var dragOverNodeKey = abstractDropNodeEntity.key;\n  var dropPosition = 0;\n  var dropLevelOffset = 0; // Only allow cross level drop when dragging on a non-expanded node\n\n  if (!expandKeys.includes(initialAbstractDropNodeKey)) {\n    for (var i = 0; i < rawDropLevelOffset; i += 1) {\n      if (isLastChild(abstractDropNodeEntity)) {\n        abstractDropNodeEntity = abstractDropNodeEntity.parent;\n        dropLevelOffset += 1;\n      } else {\n        break;\n      }\n    }\n  }\n  var abstractDragDataNode = dragNode.props.data;\n  var abstractDropDataNode = abstractDropNodeEntity.node;\n  var dropAllowed = true;\n  if (isFirstChild(abstractDropNodeEntity) && abstractDropNodeEntity.level === 0 && clientY < top + height / 2 && allowDrop({\n    dragNode: abstractDragDataNode,\n    dropNode: abstractDropDataNode,\n    dropPosition: -1\n  }) && abstractDropNodeEntity.key === targetNode.props.eventKey) {\n    // first half of first node in first level\n    dropPosition = -1;\n  } else if ((abstractDragOverEntity.children || []).length && expandKeys.includes(dragOverNodeKey)) {\n    // drop on expanded node\n    // only allow drop inside\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 0\n    })) {\n      dropPosition = 0;\n    } else {\n      dropAllowed = false;\n    }\n  } else if (dropLevelOffset === 0) {\n    if (rawDropLevelOffset > -1.5) {\n      // | Node     | <- abstractDropNode\n      // | -^-===== | <- mousePosition\n      // 1. try drop after\n      // 2. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    } else {\n      // | Node     | <- abstractDropNode\n      // | ---==^== | <- mousePosition\n      // whether it has children or doesn't has children\n      // always\n      // 1. try drop inside\n      // 2. try drop after\n      // 3. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 0\n      })) {\n        dropPosition = 0;\n      } else if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    }\n  } else {\n    // | Node1 | <- abstractDropNode\n    //      |  Node2  |\n    // --^--|----=====| <- mousePosition\n    // 1. try insert after Node1\n    // 2. do not allow drop\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 1\n    })) {\n      dropPosition = 1;\n    } else {\n      dropAllowed = false;\n    }\n  }\n  return {\n    dropPosition: dropPosition,\n    dropLevelOffset: dropLevelOffset,\n    dropTargetKey: abstractDropNodeEntity.key,\n    dropTargetPos: abstractDropNodeEntity.pos,\n    dragOverNodeKey: dragOverNodeKey,\n    dropContainerKey: dropPosition === 0 ? null : ((_abstractDropNodeEnti = abstractDropNodeEntity.parent) === null || _abstractDropNodeEnti === void 0 ? void 0 : _abstractDropNodeEnti.key) || null,\n    dropAllowed: dropAllowed\n  };\n}\n/**\n * Return selectedKeys according with multiple prop\n * @param selectedKeys\n * @param props\n * @returns [string]\n */\n\nexport function calcSelectedKeys(selectedKeys, props) {\n  if (!selectedKeys) return undefined;\n  var multiple = props.multiple;\n  if (multiple) {\n    return selectedKeys.slice();\n  }\n  if (selectedKeys.length) {\n    return [selectedKeys[0]];\n  }\n  return selectedKeys;\n}\nvar internalProcessProps = function internalProcessProps(props) {\n  return props;\n};\nexport function convertDataToTree(treeData, processor) {\n  if (!treeData) return [];\n  var _ref2 = processor || {},\n    _ref2$processProps = _ref2.processProps,\n    processProps = _ref2$processProps === void 0 ? internalProcessProps : _ref2$processProps;\n  var list = Array.isArray(treeData) ? treeData : [treeData];\n  return list.map(function (_ref3) {\n    var children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, _excluded);\n    var childrenNodes = convertDataToTree(children, processor);\n    return /*#__PURE__*/React.createElement(TreeNode, _extends({\n      key: props.key\n    }, processProps(props)), childrenNodes);\n  });\n}\n/**\n * Parse `checkedKeys` to { checkedKeys, halfCheckedKeys } style\n */\n\nexport function parseCheckedKeys(keys) {\n  if (!keys) {\n    return null;\n  } // Convert keys to object format\n\n  var keyProps;\n  if (Array.isArray(keys)) {\n    // [Legacy] Follow the api doc\n    keyProps = {\n      checkedKeys: keys,\n      halfCheckedKeys: undefined\n    };\n  } else if (_typeof(keys) === 'object') {\n    keyProps = {\n      checkedKeys: keys.checked || undefined,\n      halfCheckedKeys: keys.halfChecked || undefined\n    };\n  } else {\n    warning(false, '`checkedKeys` is not an array or an object');\n    return null;\n  }\n  return keyProps;\n}\n/**\n * If user use `autoExpandParent` we should get the list of parent node\n * @param keyList\n * @param keyEntities\n */\n\nexport function conductExpandParent(keyList, keyEntities) {\n  var expandedKeys = new Set();\n  function conductUp(key) {\n    if (expandedKeys.has(key)) return;\n    var entity = keyEntities[key];\n    if (!entity) return;\n    expandedKeys.add(key);\n    var parent = entity.parent,\n      node = entity.node;\n    if (node.disabled) return;\n    if (parent) {\n      conductUp(parent.key);\n    }\n  }\n  (keyList || []).forEach(function (key) {\n    conductUp(key);\n  });\n  return _toConsumableArray(expandedKeys);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_typeof", "_extends", "_objectWithoutProperties", "_excluded", "React", "warning", "TreeNode", "arr<PERSON><PERSON>", "list", "value", "clone", "slice", "index", "indexOf", "splice", "arrAdd", "push", "posToArr", "pos", "split", "getPosition", "level", "concat", "isTreeNode", "node", "type", "getDrag<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragNode<PERSON>ey", "keyEntities", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity", "dig", "arguments", "length", "undefined", "for<PERSON>ach", "_ref", "key", "children", "isLastChild", "treeNodeEntity", "parent", "posArr", "Number", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calcDropPosition", "event", "dragNode", "targetNode", "indent", "startMousePosition", "allowDrop", "flattenedNodes", "expandKeys", "direction", "_abstractDropNodeEnti", "clientX", "clientY", "_event$target$getBoun", "target", "getBoundingClientRect", "top", "height", "horizontalMouseOffset", "x", "rawDropLevelOffset", "abstractDropNodeEntity", "props", "eventKey", "nodeIndex", "findIndex", "flattenedNode", "prevNodeIndex", "prevNodeKey", "initialAbstractDropNodeKey", "abstractDragOverEntity", "dragOverNodeKey", "dropPosition", "dropLevelOffset", "includes", "i", "abstractDragDataNode", "data", "abstractDropDataNode", "dropAllowed", "dropNode", "dropTargetKey", "dropTargetPos", "dropContainerKey", "calcSelectedKeys", "<PERSON><PERSON><PERSON><PERSON>", "multiple", "internalProcessProps", "convertDataToTree", "treeData", "processor", "_ref2", "_ref2$processProps", "processProps", "Array", "isArray", "map", "_ref3", "childrenNodes", "createElement", "parseCheckedKeys", "keys", "keyProps", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "checked", "halfChecked", "conductExpandParent", "keyList", "expandedKeys", "Set", "conductUp", "has", "add", "disabled"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree/es/util.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\n\n/* eslint-disable no-lonely-if */\n\n/**\n * Legacy code. Should avoid to use if you are new to import these code.\n */\nimport React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport TreeNode from './TreeNode';\nexport function arrDel(list, value) {\n  if (!list) return [];\n  var clone = list.slice();\n  var index = clone.indexOf(value);\n\n  if (index >= 0) {\n    clone.splice(index, 1);\n  }\n\n  return clone;\n}\nexport function arrAdd(list, value) {\n  var clone = (list || []).slice();\n\n  if (clone.indexOf(value) === -1) {\n    clone.push(value);\n  }\n\n  return clone;\n}\nexport function posToArr(pos) {\n  return pos.split('-');\n}\nexport function getPosition(level, index) {\n  return \"\".concat(level, \"-\").concat(index);\n}\nexport function isTreeNode(node) {\n  return node && node.type && node.type.isTreeNode;\n}\nexport function getDragChildrenKeys(dragNodeKey, keyEntities) {\n  // not contains self\n  // self for left or right drag\n  var dragChildrenKeys = [];\n  var entity = keyEntities[dragNodeKey];\n\n  function dig() {\n    var list = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    list.forEach(function (_ref) {\n      var key = _ref.key,\n          children = _ref.children;\n      dragChildrenKeys.push(key);\n      dig(children);\n    });\n  }\n\n  dig(entity.children);\n  return dragChildrenKeys;\n}\nexport function isLastChild(treeNodeEntity) {\n  if (treeNodeEntity.parent) {\n    var posArr = posToArr(treeNodeEntity.pos);\n    return Number(posArr[posArr.length - 1]) === treeNodeEntity.parent.children.length - 1;\n  }\n\n  return false;\n}\nexport function isFirstChild(treeNodeEntity) {\n  var posArr = posToArr(treeNodeEntity.pos);\n  return Number(posArr[posArr.length - 1]) === 0;\n} // Only used when drag, not affect SSR.\n\nexport function calcDropPosition(event, dragNode, targetNode, indent, startMousePosition, allowDrop, flattenedNodes, keyEntities, expandKeys, direction) {\n  var _abstractDropNodeEnti;\n\n  var clientX = event.clientX,\n      clientY = event.clientY;\n\n  var _event$target$getBoun = event.target.getBoundingClientRect(),\n      top = _event$target$getBoun.top,\n      height = _event$target$getBoun.height; // optional chain for testing\n\n\n  var horizontalMouseOffset = (direction === 'rtl' ? -1 : 1) * (((startMousePosition === null || startMousePosition === void 0 ? void 0 : startMousePosition.x) || 0) - clientX);\n  var rawDropLevelOffset = (horizontalMouseOffset - 12) / indent; // find abstract drop node by horizontal offset\n\n  var abstractDropNodeEntity = keyEntities[targetNode.props.eventKey];\n\n  if (clientY < top + height / 2) {\n    // first half, set abstract drop node to previous node\n    var nodeIndex = flattenedNodes.findIndex(function (flattenedNode) {\n      return flattenedNode.key === abstractDropNodeEntity.key;\n    });\n    var prevNodeIndex = nodeIndex <= 0 ? 0 : nodeIndex - 1;\n    var prevNodeKey = flattenedNodes[prevNodeIndex].key;\n    abstractDropNodeEntity = keyEntities[prevNodeKey];\n  }\n\n  var initialAbstractDropNodeKey = abstractDropNodeEntity.key;\n  var abstractDragOverEntity = abstractDropNodeEntity;\n  var dragOverNodeKey = abstractDropNodeEntity.key;\n  var dropPosition = 0;\n  var dropLevelOffset = 0; // Only allow cross level drop when dragging on a non-expanded node\n\n  if (!expandKeys.includes(initialAbstractDropNodeKey)) {\n    for (var i = 0; i < rawDropLevelOffset; i += 1) {\n      if (isLastChild(abstractDropNodeEntity)) {\n        abstractDropNodeEntity = abstractDropNodeEntity.parent;\n        dropLevelOffset += 1;\n      } else {\n        break;\n      }\n    }\n  }\n\n  var abstractDragDataNode = dragNode.props.data;\n  var abstractDropDataNode = abstractDropNodeEntity.node;\n  var dropAllowed = true;\n\n  if (isFirstChild(abstractDropNodeEntity) && abstractDropNodeEntity.level === 0 && clientY < top + height / 2 && allowDrop({\n    dragNode: abstractDragDataNode,\n    dropNode: abstractDropDataNode,\n    dropPosition: -1\n  }) && abstractDropNodeEntity.key === targetNode.props.eventKey) {\n    // first half of first node in first level\n    dropPosition = -1;\n  } else if ((abstractDragOverEntity.children || []).length && expandKeys.includes(dragOverNodeKey)) {\n    // drop on expanded node\n    // only allow drop inside\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 0\n    })) {\n      dropPosition = 0;\n    } else {\n      dropAllowed = false;\n    }\n  } else if (dropLevelOffset === 0) {\n    if (rawDropLevelOffset > -1.5) {\n      // | Node     | <- abstractDropNode\n      // | -^-===== | <- mousePosition\n      // 1. try drop after\n      // 2. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    } else {\n      // | Node     | <- abstractDropNode\n      // | ---==^== | <- mousePosition\n      // whether it has children or doesn't has children\n      // always\n      // 1. try drop inside\n      // 2. try drop after\n      // 3. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 0\n      })) {\n        dropPosition = 0;\n      } else if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    }\n  } else {\n    // | Node1 | <- abstractDropNode\n    //      |  Node2  |\n    // --^--|----=====| <- mousePosition\n    // 1. try insert after Node1\n    // 2. do not allow drop\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 1\n    })) {\n      dropPosition = 1;\n    } else {\n      dropAllowed = false;\n    }\n  }\n\n  return {\n    dropPosition: dropPosition,\n    dropLevelOffset: dropLevelOffset,\n    dropTargetKey: abstractDropNodeEntity.key,\n    dropTargetPos: abstractDropNodeEntity.pos,\n    dragOverNodeKey: dragOverNodeKey,\n    dropContainerKey: dropPosition === 0 ? null : ((_abstractDropNodeEnti = abstractDropNodeEntity.parent) === null || _abstractDropNodeEnti === void 0 ? void 0 : _abstractDropNodeEnti.key) || null,\n    dropAllowed: dropAllowed\n  };\n}\n/**\n * Return selectedKeys according with multiple prop\n * @param selectedKeys\n * @param props\n * @returns [string]\n */\n\nexport function calcSelectedKeys(selectedKeys, props) {\n  if (!selectedKeys) return undefined;\n  var multiple = props.multiple;\n\n  if (multiple) {\n    return selectedKeys.slice();\n  }\n\n  if (selectedKeys.length) {\n    return [selectedKeys[0]];\n  }\n\n  return selectedKeys;\n}\n\nvar internalProcessProps = function internalProcessProps(props) {\n  return props;\n};\n\nexport function convertDataToTree(treeData, processor) {\n  if (!treeData) return [];\n\n  var _ref2 = processor || {},\n      _ref2$processProps = _ref2.processProps,\n      processProps = _ref2$processProps === void 0 ? internalProcessProps : _ref2$processProps;\n\n  var list = Array.isArray(treeData) ? treeData : [treeData];\n  return list.map(function (_ref3) {\n    var children = _ref3.children,\n        props = _objectWithoutProperties(_ref3, _excluded);\n\n    var childrenNodes = convertDataToTree(children, processor);\n    return /*#__PURE__*/React.createElement(TreeNode, _extends({\n      key: props.key\n    }, processProps(props)), childrenNodes);\n  });\n}\n/**\n * Parse `checkedKeys` to { checkedKeys, halfCheckedKeys } style\n */\n\nexport function parseCheckedKeys(keys) {\n  if (!keys) {\n    return null;\n  } // Convert keys to object format\n\n\n  var keyProps;\n\n  if (Array.isArray(keys)) {\n    // [Legacy] Follow the api doc\n    keyProps = {\n      checkedKeys: keys,\n      halfCheckedKeys: undefined\n    };\n  } else if (_typeof(keys) === 'object') {\n    keyProps = {\n      checkedKeys: keys.checked || undefined,\n      halfCheckedKeys: keys.halfChecked || undefined\n    };\n  } else {\n    warning(false, '`checkedKeys` is not an array or an object');\n    return null;\n  }\n\n  return keyProps;\n}\n/**\n * If user use `autoExpandParent` we should get the list of parent node\n * @param keyList\n * @param keyEntities\n */\n\nexport function conductExpandParent(keyList, keyEntities) {\n  var expandedKeys = new Set();\n\n  function conductUp(key) {\n    if (expandedKeys.has(key)) return;\n    var entity = keyEntities[key];\n    if (!entity) return;\n    expandedKeys.add(key);\n    var parent = entity.parent,\n        node = entity.node;\n    if (node.disabled) return;\n\n    if (parent) {\n      conductUp(parent.key);\n    }\n  }\n\n  (keyList || []).forEach(function (key) {\n    conductUp(key);\n  });\n  return _toConsumableArray(expandedKeys);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;;AAE5B;;AAEA;AACA;AACA;AACA,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIE,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC;EACxB,IAAIC,KAAK,GAAGF,KAAK,CAACG,OAAO,CAACJ,KAAK,CAAC;EAEhC,IAAIG,KAAK,IAAI,CAAC,EAAE;IACdF,KAAK,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EACxB;EAEA,OAAOF,KAAK;AACd;AACA,OAAO,SAASK,MAAMA,CAACP,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAIC,KAAK,GAAG,CAACF,IAAI,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAEhC,IAAID,KAAK,CAACG,OAAO,CAACJ,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAC/BC,KAAK,CAACM,IAAI,CAACP,KAAK,CAAC;EACnB;EAEA,OAAOC,KAAK;AACd;AACA,OAAO,SAASO,QAAQA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;AACvB;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAET,KAAK,EAAE;EACxC,OAAO,EAAE,CAACU,MAAM,CAACD,KAAK,EAAE,GAAG,CAAC,CAACC,MAAM,CAACV,KAAK,CAAC;AAC5C;AACA,OAAO,SAASW,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAID,IAAI,CAACC,IAAI,CAACF,UAAU;AAClD;AACA,OAAO,SAASG,mBAAmBA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC5D;EACA;EACA,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,MAAM,GAAGF,WAAW,CAACD,WAAW,CAAC;EAErC,SAASI,GAAGA,CAAA,EAAG;IACb,IAAIvB,IAAI,GAAGwB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACjFxB,IAAI,CAAC2B,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC3B,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;QACdC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;MAC5BT,gBAAgB,CAACb,IAAI,CAACqB,GAAG,CAAC;MAC1BN,GAAG,CAACO,QAAQ,CAAC;IACf,CAAC,CAAC;EACJ;EAEAP,GAAG,CAACD,MAAM,CAACQ,QAAQ,CAAC;EACpB,OAAOT,gBAAgB;AACzB;AACA,OAAO,SAASU,WAAWA,CAACC,cAAc,EAAE;EAC1C,IAAIA,cAAc,CAACC,MAAM,EAAE;IACzB,IAAIC,MAAM,GAAGzB,QAAQ,CAACuB,cAAc,CAACtB,GAAG,CAAC;IACzC,OAAOyB,MAAM,CAACD,MAAM,CAACA,MAAM,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC,KAAKO,cAAc,CAACC,MAAM,CAACH,QAAQ,CAACL,MAAM,GAAG,CAAC;EACxF;EAEA,OAAO,KAAK;AACd;AACA,OAAO,SAASW,YAAYA,CAACJ,cAAc,EAAE;EAC3C,IAAIE,MAAM,GAAGzB,QAAQ,CAACuB,cAAc,CAACtB,GAAG,CAAC;EACzC,OAAOyB,MAAM,CAACD,MAAM,CAACA,MAAM,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD,CAAC,CAAC;;AAEF,OAAO,SAASY,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,SAAS,EAAEC,cAAc,EAAExB,WAAW,EAAEyB,UAAU,EAAEC,SAAS,EAAE;EACvJ,IAAIC,qBAAqB;EAEzB,IAAIC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,OAAO,GAAGX,KAAK,CAACW,OAAO;EAE3B,IAAIC,qBAAqB,GAAGZ,KAAK,CAACa,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAC5DC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;IAC/BC,MAAM,GAAGJ,qBAAqB,CAACI,MAAM,CAAC,CAAC;;EAG3C,IAAIC,qBAAqB,GAAG,CAACT,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAACJ,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACc,CAAC,KAAK,CAAC,IAAIR,OAAO,CAAC;EAC9K,IAAIS,kBAAkB,GAAG,CAACF,qBAAqB,GAAG,EAAE,IAAId,MAAM,CAAC,CAAC;;EAEhE,IAAIiB,sBAAsB,GAAGtC,WAAW,CAACoB,UAAU,CAACmB,KAAK,CAACC,QAAQ,CAAC;EAEnE,IAAIX,OAAO,GAAGI,GAAG,GAAGC,MAAM,GAAG,CAAC,EAAE;IAC9B;IACA,IAAIO,SAAS,GAAGjB,cAAc,CAACkB,SAAS,CAAC,UAAUC,aAAa,EAAE;MAChE,OAAOA,aAAa,CAAClC,GAAG,KAAK6B,sBAAsB,CAAC7B,GAAG;IACzD,CAAC,CAAC;IACF,IAAImC,aAAa,GAAGH,SAAS,IAAI,CAAC,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC;IACtD,IAAII,WAAW,GAAGrB,cAAc,CAACoB,aAAa,CAAC,CAACnC,GAAG;IACnD6B,sBAAsB,GAAGtC,WAAW,CAAC6C,WAAW,CAAC;EACnD;EAEA,IAAIC,0BAA0B,GAAGR,sBAAsB,CAAC7B,GAAG;EAC3D,IAAIsC,sBAAsB,GAAGT,sBAAsB;EACnD,IAAIU,eAAe,GAAGV,sBAAsB,CAAC7B,GAAG;EAChD,IAAIwC,YAAY,GAAG,CAAC;EACpB,IAAIC,eAAe,GAAG,CAAC,CAAC,CAAC;;EAEzB,IAAI,CAACzB,UAAU,CAAC0B,QAAQ,CAACL,0BAA0B,CAAC,EAAE;IACpD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,kBAAkB,EAAEe,CAAC,IAAI,CAAC,EAAE;MAC9C,IAAIzC,WAAW,CAAC2B,sBAAsB,CAAC,EAAE;QACvCA,sBAAsB,GAAGA,sBAAsB,CAACzB,MAAM;QACtDqC,eAAe,IAAI,CAAC;MACtB,CAAC,MAAM;QACL;MACF;IACF;EACF;EAEA,IAAIG,oBAAoB,GAAGlC,QAAQ,CAACoB,KAAK,CAACe,IAAI;EAC9C,IAAIC,oBAAoB,GAAGjB,sBAAsB,CAAC1C,IAAI;EACtD,IAAI4D,WAAW,GAAG,IAAI;EAEtB,IAAIxC,YAAY,CAACsB,sBAAsB,CAAC,IAAIA,sBAAsB,CAAC7C,KAAK,KAAK,CAAC,IAAIoC,OAAO,GAAGI,GAAG,GAAGC,MAAM,GAAG,CAAC,IAAIX,SAAS,CAAC;IACxHJ,QAAQ,EAAEkC,oBAAoB;IAC9BI,QAAQ,EAAEF,oBAAoB;IAC9BN,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC,IAAIX,sBAAsB,CAAC7B,GAAG,KAAKW,UAAU,CAACmB,KAAK,CAACC,QAAQ,EAAE;IAC9D;IACAS,YAAY,GAAG,CAAC,CAAC;EACnB,CAAC,MAAM,IAAI,CAACF,sBAAsB,CAACrC,QAAQ,IAAI,EAAE,EAAEL,MAAM,IAAIoB,UAAU,CAAC0B,QAAQ,CAACH,eAAe,CAAC,EAAE;IACjG;IACA;IACA,IAAIzB,SAAS,CAAC;MACZJ,QAAQ,EAAEkC,oBAAoB;MAC9BI,QAAQ,EAAEF,oBAAoB;MAC9BN,YAAY,EAAE;IAChB,CAAC,CAAC,EAAE;MACFA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM;MACLO,WAAW,GAAG,KAAK;IACrB;EACF,CAAC,MAAM,IAAIN,eAAe,KAAK,CAAC,EAAE;IAChC,IAAIb,kBAAkB,GAAG,CAAC,GAAG,EAAE;MAC7B;MACA;MACA;MACA;MACA,IAAId,SAAS,CAAC;QACZJ,QAAQ,EAAEkC,oBAAoB;QAC9BI,QAAQ,EAAEF,oBAAoB;QAC9BN,YAAY,EAAE;MAChB,CAAC,CAAC,EAAE;QACFA,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM;QACLO,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIjC,SAAS,CAAC;QACZJ,QAAQ,EAAEkC,oBAAoB;QAC9BI,QAAQ,EAAEF,oBAAoB;QAC9BN,YAAY,EAAE;MAChB,CAAC,CAAC,EAAE;QACFA,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM,IAAI1B,SAAS,CAAC;QACnBJ,QAAQ,EAAEkC,oBAAoB;QAC9BI,QAAQ,EAAEF,oBAAoB;QAC9BN,YAAY,EAAE;MAChB,CAAC,CAAC,EAAE;QACFA,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM;QACLO,WAAW,GAAG,KAAK;MACrB;IACF;EACF,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA;IACA,IAAIjC,SAAS,CAAC;MACZJ,QAAQ,EAAEkC,oBAAoB;MAC9BI,QAAQ,EAAEF,oBAAoB;MAC9BN,YAAY,EAAE;IAChB,CAAC,CAAC,EAAE;MACFA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM;MACLO,WAAW,GAAG,KAAK;IACrB;EACF;EAEA,OAAO;IACLP,YAAY,EAAEA,YAAY;IAC1BC,eAAe,EAAEA,eAAe;IAChCQ,aAAa,EAAEpB,sBAAsB,CAAC7B,GAAG;IACzCkD,aAAa,EAAErB,sBAAsB,CAAChD,GAAG;IACzC0D,eAAe,EAAEA,eAAe;IAChCY,gBAAgB,EAAEX,YAAY,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAACtB,qBAAqB,GAAGW,sBAAsB,CAACzB,MAAM,MAAM,IAAI,IAAIc,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAClB,GAAG,KAAK,IAAI;IACjM+C,WAAW,EAAEA;EACf,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASK,gBAAgBA,CAACC,YAAY,EAAEvB,KAAK,EAAE;EACpD,IAAI,CAACuB,YAAY,EAAE,OAAOxD,SAAS;EACnC,IAAIyD,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ;EAE7B,IAAIA,QAAQ,EAAE;IACZ,OAAOD,YAAY,CAAC/E,KAAK,CAAC,CAAC;EAC7B;EAEA,IAAI+E,YAAY,CAACzD,MAAM,EAAE;IACvB,OAAO,CAACyD,YAAY,CAAC,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAOA,YAAY;AACrB;AAEA,IAAIE,oBAAoB,GAAG,SAASA,oBAAoBA,CAACzB,KAAK,EAAE;EAC9D,OAAOA,KAAK;AACd,CAAC;AAED,OAAO,SAAS0B,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACrD,IAAI,CAACD,QAAQ,EAAE,OAAO,EAAE;EAExB,IAAIE,KAAK,GAAGD,SAAS,IAAI,CAAC,CAAC;IACvBE,kBAAkB,GAAGD,KAAK,CAACE,YAAY;IACvCA,YAAY,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAGL,oBAAoB,GAAGK,kBAAkB;EAE5F,IAAIzF,IAAI,GAAG2F,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EAC1D,OAAOtF,IAAI,CAAC6F,GAAG,CAAC,UAAUC,KAAK,EAAE;IAC/B,IAAIhE,QAAQ,GAAGgE,KAAK,CAAChE,QAAQ;MACzB6B,KAAK,GAAGjE,wBAAwB,CAACoG,KAAK,EAAEnG,SAAS,CAAC;IAEtD,IAAIoG,aAAa,GAAGV,iBAAiB,CAACvD,QAAQ,EAAEyD,SAAS,CAAC;IAC1D,OAAO,aAAa3F,KAAK,CAACoG,aAAa,CAAClG,QAAQ,EAAEL,QAAQ,CAAC;MACzDoC,GAAG,EAAE8B,KAAK,CAAC9B;IACb,CAAC,EAAE6D,YAAY,CAAC/B,KAAK,CAAC,CAAC,EAAEoC,aAAa,CAAC;EACzC,CAAC,CAAC;AACJ;AACA;AACA;AACA;;AAEA,OAAO,SAASE,gBAAgBA,CAACC,IAAI,EAAE;EACrC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIC,QAAQ;EAEZ,IAAIR,KAAK,CAACC,OAAO,CAACM,IAAI,CAAC,EAAE;IACvB;IACAC,QAAQ,GAAG;MACTC,WAAW,EAAEF,IAAI;MACjBG,eAAe,EAAE3E;IACnB,CAAC;EACH,CAAC,MAAM,IAAIlC,OAAO,CAAC0G,IAAI,CAAC,KAAK,QAAQ,EAAE;IACrCC,QAAQ,GAAG;MACTC,WAAW,EAAEF,IAAI,CAACI,OAAO,IAAI5E,SAAS;MACtC2E,eAAe,EAAEH,IAAI,CAACK,WAAW,IAAI7E;IACvC,CAAC;EACH,CAAC,MAAM;IACL7B,OAAO,CAAC,KAAK,EAAE,4CAA4C,CAAC;IAC5D,OAAO,IAAI;EACb;EAEA,OAAOsG,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASK,mBAAmBA,CAACC,OAAO,EAAErF,WAAW,EAAE;EACxD,IAAIsF,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAE5B,SAASC,SAASA,CAAC/E,GAAG,EAAE;IACtB,IAAI6E,YAAY,CAACG,GAAG,CAAChF,GAAG,CAAC,EAAE;IAC3B,IAAIP,MAAM,GAAGF,WAAW,CAACS,GAAG,CAAC;IAC7B,IAAI,CAACP,MAAM,EAAE;IACboF,YAAY,CAACI,GAAG,CAACjF,GAAG,CAAC;IACrB,IAAII,MAAM,GAAGX,MAAM,CAACW,MAAM;MACtBjB,IAAI,GAAGM,MAAM,CAACN,IAAI;IACtB,IAAIA,IAAI,CAAC+F,QAAQ,EAAE;IAEnB,IAAI9E,MAAM,EAAE;MACV2E,SAAS,CAAC3E,MAAM,CAACJ,GAAG,CAAC;IACvB;EACF;EAEA,CAAC4E,OAAO,IAAI,EAAE,EAAE9E,OAAO,CAAC,UAAUE,GAAG,EAAE;IACrC+E,SAAS,CAAC/E,GAAG,CAAC;EAChB,CAAC,CAAC;EACF,OAAOtC,kBAAkB,CAACmH,YAAY,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}