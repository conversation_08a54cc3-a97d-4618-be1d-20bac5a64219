{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: !0\n});\nvar VALIDATOR_ARG_ERROR_MESSAGE = 'The typeValidator argument must be a function with the signature function(props, propName, componentName).',\n  MESSAGE_ARG_ERROR_MESSAGE = 'The error message is optional, but must be a string if provided.',\n  propIsRequired = function propIsRequired(a, b, c, d) {\n    if ('boolean' == typeof a) return a;\n    return 'function' == typeof a ? a(b, c, d) : !(!0 !== !!a) && !!a;\n  },\n  propExists = function propExists(a, b) {\n    return Object.hasOwnProperty.call(a, b);\n  },\n  missingPropError = function missingPropError(a, b, c, d) {\n    return d ? new Error(d) : new Error('Required ' + a[b] + ' `' + b + '`' + (' was not specified in `' + c + '`.'));\n  },\n  guardAgainstInvalidArgTypes = function guardAgainstInvalidArgTypes(a, b) {\n    if ('function' != typeof a) throw new TypeError(VALIDATOR_ARG_ERROR_MESSAGE);\n    if (!!b && 'string' != typeof b) throw new TypeError(MESSAGE_ARG_ERROR_MESSAGE);\n  },\n  isRequiredIf = function isRequiredIf(a, b, c) {\n    return guardAgainstInvalidArgTypes(a, c), function (d, e, f) {\n      for (var _len = arguments.length, g = Array(3 < _len ? _len - 3 : 0), _key = 3; _key < _len; _key++) g[_key - 3] = arguments[_key];\n      return propIsRequired(b, d, e, f) ? propExists(d, e) ? a.apply(void 0, [d, e, f].concat(g)) : missingPropError(d, e, f, c) : a.apply(void 0, [d, e, f].concat(g)); // Is not required, so just run typeValidator.\n    };\n  };\nexports.default = isRequiredIf;", "map": {"version": 3, "names": ["VALIDATOR_ARG_ERROR_MESSAGE", "MESSAGE_ARG_ERROR_MESSAGE", "propIsRequired", "a", "b", "c", "d", "propExists", "Object", "hasOwnProperty", "call", "missingPropError", "Error", "guardAgainstInvalidArgTypes", "TypeError", "isRequiredIf", "e", "f", "_len", "arguments", "length", "g", "Array", "_key", "apply", "concat"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-proptype-conditional-require\\isRequiredIf.js"], "sourcesContent": ["const VALIDATOR_ARG_ERROR_MESSAGE =\n  'The typeValidator argument must be a function ' +\n  'with the signature function(props, propName, componentName).';\n\nconst MESSAGE_ARG_ERROR_MESSAGE =\n  'The error message is optional, but must be a string if provided.';\n\nconst propIsRequired = (condition, props, propName, componentName) => {\n  if (typeof condition === 'boolean') {\n    return condition;\n  } else if (typeof condition === 'function') {\n    return condition(props, propName, componentName);\n  } else if (Boolean(condition) === true) {\n    return Boolean(condition);\n  }\n\n  return false;\n};\n\nconst propExists = (props, propName) => Object.hasOwnProperty.call(props, propName);\n\nconst missingPropError = (props, propName, componentName, message) => {\n  if (message) {\n    return new Error(message);\n  }\n\n  return new Error(\n    `Required ${props[propName]} \\`${propName}\\`` +\n    ` was not specified in \\`${componentName}\\`.`,\n  );\n};\n\nconst guardAgainstInvalidArgTypes = (typeValidator, message) => {\n  if (typeof typeValidator !== 'function') {\n    throw new TypeError(VALIDATOR_ARG_ERROR_MESSAGE);\n  }\n\n  if (Boolean(message) && typeof message !== 'string') {\n    throw new TypeError(MESSAGE_ARG_ERROR_MESSAGE);\n  }\n};\n\nconst isRequiredIf = (typeValidator, condition, message) => {\n  guardAgainstInvalidArgTypes(typeValidator, message);\n\n  return (props, propName, componentName, ...rest) => {\n    if (propIsRequired(condition, props, propName, componentName)) {\n      if (propExists(props, propName)) {\n        return typeValidator(props, propName, componentName, ...rest);\n      }\n\n      return missingPropError(props, propName, componentName, message);\n    }\n\n    // Is not required, so just run typeValidator.\n    return typeValidator(props, propName, componentName, ...rest);\n  };\n};\n\nexport default isRequiredIf;\n"], "mappings": ";;;;;AAAA,IAAMA,2BAAA,+GAAN;EAIMC,yBAAA,GACJ,kEALF;EAOMC,cAAA,GAAiB,SAAjBA,cAAiBA,CAACC,CAAD,EAAYC,CAAZ,EAAmBC,CAAnB,EAA6BC,CAA7B,EAA+C;IACpE,IAAyB,SAArB,WAAOH,CAAX,EACE,OAAOA,CAAP;IAFkE,OAGpC,UAArB,WAAOA,CAHkD,GAI3DA,CAAA,CAAUC,CAAV,EAAiBC,CAAjB,EAA2BC,CAA3B,CAJ2D,KAKzD,SAAQH,CALiD,OAMnDA,CAIlB;EAAA,CAjBD;EAmBMI,UAAA,GAAa,SAAbA,UAAaA,CAACJ,CAAD,EAAQC,CAAR;IAAA,OAAqBI,MAAA,CAAOC,cAAP,CAAsBC,IAAtB,CAA2BP,CAA3B,EAAkCC,CAAlC,CAArB;EAAA,CAnBnB;EAqBMO,gBAAA,GAAmB,SAAnBA,gBAAmBA,CAACR,CAAD,EAAQC,CAAR,EAAkBC,CAAlB,EAAiCC,CAAjC,EAA6C;IAAA,OAChEA,CADgE,GAE3D,IAAIM,KAAJ,CAAUN,CAAV,CAF2D,GAK7D,IAAIM,KAAJ,CACL,cAAYT,CAAA,CAAMC,CAAN,CAAZ,UAAiCA,CAAjC,sCAC2BC,CAD3B,QADK,CAIR;EAAA,CA9BD;EAgCMQ,2BAAA,GAA8B,SAA9BA,2BAA8BA,CAACV,CAAD,EAAgBC,CAAhB,EAA4B;IAC9D,IAA6B,UAAzB,WAAOD,CAAX,EACE,MAAM,IAAIW,SAAJ,CAAcd,2BAAd,CAAN;IAGF,IAAI,EAAQI,CAAR,IAAuC,QAAnB,WAAOA,CAA/B,EACE,MAAM,IAAIU,SAAJ,CAAcb,yBAAd,CAET;EAAA,CAxCD;EA0CMc,YAAA,GAAe,SAAfA,YAAeA,CAACZ,CAAD,EAAgBC,CAAhB,EAA2BC,CAA3B,EAAuC;IAG1D,OAFAQ,2BAAA,CAA4BV,CAA5B,EAA2CE,CAA3C,CAEA,EAAO,UAACC,CAAD,EAAQU,CAAR,EAAkBC,CAAlB,EAA6C;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,CAAS,GAAAC,KAAA,KAAAJ,IAAA,GAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,IAATF,CAAS,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA,OAC9CrB,cAAA,CAAeE,CAAf,EAA0BE,CAA1B,EAAiCU,CAAjC,EAA2CC,CAA3C,CAD8C,GAE5CV,UAAA,CAAWD,CAAX,EAAkBU,CAAlB,CAF4C,GAGvCb,CAAA,CAAAqB,KAAA,UAAclB,CAAd,EAAqBU,CAArB,EAA+BC,CAA/B,EAAAQ,MAAA,CAAiDJ,CAAjD,EAHuC,GAMzCV,gBAAA,CAAiBL,CAAjB,EAAwBU,CAAxB,EAAkCC,CAAlC,EAAiDZ,CAAjD,CANyC,GAU3CF,CAAA,CAAAqB,KAAA,UAAclB,CAAd,EAAqBU,CAArB,EAA+BC,CAA/B,EAAAQ,MAAA,CAAiDJ,CAAjD,EAV2C,CASlD;IAED,CACF;EAAA,CAzDD;kBA2DeN,Y", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}