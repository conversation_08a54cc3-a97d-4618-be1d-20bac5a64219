{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\dataOra.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport class Clock extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      time: new Date().toLocaleString()\n    };\n  }\n  componentDidMount() {\n    this.intervalID = setInterval(() => this.tick(), 1000);\n  }\n  componentWillUnmount() {\n    clearInterval(this.intervalID);\n  }\n  tick() {\n    this.setState({\n      time: new Date().toLocaleString()\n    });\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"App-clock mt-3\",\n      children: this.state.time\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 9\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Clock", "Component", "constructor", "props", "state", "time", "Date", "toLocaleString", "componentDidMount", "intervalID", "setInterval", "tick", "componentWillUnmount", "clearInterval", "setState", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/dataOra.jsx"], "sourcesContent": ["import React from 'react';\n\nexport class Clock extends React.Component {\n    constructor(props) {\n      super(props);\n      this.state = {\n        time: new Date().toLocaleString()\n      };\n    }\n    componentDidMount() {\n      this.intervalID = setInterval(\n        () => this.tick(),\n        1000\n      );\n    }\n    componentWillUnmount() {\n      clearInterval(this.intervalID);\n    }\n    tick() {\n      this.setState({\n        time: new Date().toLocaleString()\n      });\n    }\n    render() {\n      return (\n        <p className=\"App-clock mt-3\">\n          {this.state.time}\n        </p>\n      );\n    }\n  }"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,KAAK,SAASH,KAAK,CAACI,SAAS,CAAC;EACvCC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACXC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC;IAClC,CAAC;EACH;EACAC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,UAAU,GAAGC,WAAW,CAC3B,MAAM,IAAI,CAACC,IAAI,CAAC,CAAC,EACjB,IACF,CAAC;EACH;EACAC,oBAAoBA,CAAA,EAAG;IACrBC,aAAa,CAAC,IAAI,CAACJ,UAAU,CAAC;EAChC;EACAE,IAAIA,CAAA,EAAG;IACL,IAAI,CAACG,QAAQ,CAAC;MACZT,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC;IAClC,CAAC,CAAC;EACJ;EACAQ,MAAMA,CAAA,EAAG;IACP,oBACEhB,OAAA;MAAGiB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC1B,IAAI,CAACb,KAAK,CAACC;IAAI;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAER;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}