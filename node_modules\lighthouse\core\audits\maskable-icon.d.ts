export default MaskableIcon;
/**
 * @fileoverview
 * Audits if a manifest contains at least one icon that is maskable
 *
 * Requirements:
 *    * manifest is not empty
 *    * manifest has valid icons
 *    * at least one of the icons has a purpose of 'maskable'
 */
declare class MaskableIcon extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=maskable-icon.d.ts.map