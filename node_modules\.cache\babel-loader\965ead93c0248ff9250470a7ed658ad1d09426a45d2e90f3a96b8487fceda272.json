{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from \"rc-util/es/PortalWrapper\";\nimport Dialog from './Dialog'; // fix issue #10656\n\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    _props$destroyOnClose = props.destroyOnClose,\n    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n    _afterClose = props.afterClose;\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]); // 渲染在当前 dom 里；\n\n  if (getContainer === false) {\n    return /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n      getOpenCount: function getOpenCount() {\n        return 2;\n      } // 不对 body 做任何操作。。\n    }));\n  } // Destroy on close will remove wrapped div\n\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    visible: visible,\n    forceRender: forceRender,\n    getContainer: getContainer\n  }, function (childProps) {\n    return /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n      destroyOnClose: destroyOnClose,\n      afterClose: function afterClose() {\n        _afterClose === null || _afterClose === void 0 ? void 0 : _afterClose();\n        setAnimatedVisible(false);\n      }\n    }, childProps));\n  });\n};\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "Portal", "Dialog", "DialogWrap", "props", "visible", "getContainer", "forceRender", "_props$destroyOnClose", "destroyOnClose", "_afterClose", "afterClose", "_React$useState", "useState", "_React$useState2", "animatedVisible", "setAnimatedVisible", "useEffect", "createElement", "getOpenCount", "childProps", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-dialog/es/DialogWrap.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from \"rc-util/es/PortalWrapper\";\nimport Dialog from './Dialog'; // fix issue #10656\n\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n      getContainer = props.getContainer,\n      forceRender = props.forceRender,\n      _props$destroyOnClose = props.destroyOnClose,\n      destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n      _afterClose = props.afterClose;\n\n  var _React$useState = React.useState(visible),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      animatedVisible = _React$useState2[0],\n      setAnimatedVisible = _React$useState2[1];\n\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]); // 渲染在当前 dom 里；\n\n  if (getContainer === false) {\n    return /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n      getOpenCount: function getOpenCount() {\n        return 2;\n      } // 不对 body 做任何操作。。\n\n    }));\n  } // Destroy on close will remove wrapped div\n\n\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(Portal, {\n    visible: visible,\n    forceRender: forceRender,\n    getContainer: getContainer\n  }, function (childProps) {\n    return /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n      destroyOnClose: destroyOnClose,\n      afterClose: function afterClose() {\n        _afterClose === null || _afterClose === void 0 ? void 0 : _afterClose();\n        setAnimatedVisible(false);\n      }\n    }, childProps));\n  });\n};\n\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,MAAM,MAAM,UAAU,CAAC,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,qBAAqB,GAAGJ,KAAK,CAACK,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACjFE,WAAW,GAAGN,KAAK,CAACO,UAAU;EAElC,IAAIC,eAAe,GAAGZ,KAAK,CAACa,QAAQ,CAACR,OAAO,CAAC;IACzCS,gBAAgB,GAAGf,cAAc,CAACa,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE5Cd,KAAK,CAACiB,SAAS,CAAC,YAAY;IAC1B,IAAIZ,OAAO,EAAE;MACXW,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,IAAIC,YAAY,KAAK,KAAK,EAAE;IAC1B,OAAO,aAAaN,KAAK,CAACkB,aAAa,CAAChB,MAAM,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;MAClEe,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC,OAAO,CAAC;MACV,CAAC,CAAC;IAEJ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;;EAGF,IAAI,CAACZ,WAAW,IAAIE,cAAc,IAAI,CAACM,eAAe,EAAE;IACtD,OAAO,IAAI;EACb;EAEA,OAAO,aAAaf,KAAK,CAACkB,aAAa,CAACjB,MAAM,EAAE;IAC9CI,OAAO,EAAEA,OAAO;IAChBE,WAAW,EAAEA,WAAW;IACxBD,YAAY,EAAEA;EAChB,CAAC,EAAE,UAAUc,UAAU,EAAE;IACvB,OAAO,aAAapB,KAAK,CAACkB,aAAa,CAAChB,MAAM,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;MAClEK,cAAc,EAAEA,cAAc;MAC9BE,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChCD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;QACvEM,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,EAAEI,UAAU,CAAC,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC;AAEDjB,UAAU,CAACkB,WAAW,GAAG,QAAQ;AACjC,eAAelB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}