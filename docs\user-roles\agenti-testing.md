# Test Manuale - Funzionalità Creazione PDV per AGENTI

## Problemi Identificati e Correzioni Implementate

### 1. ✅ Layout Pulsante Aggiunta Cliente
**Problema**: Il pulsante non era uguale a quello del pannello AFFILIATO
**Correzione**: Implementato split button tramite `CustomDataTable` con `splitButtonClass={true}` e `items={items}`

### 2. ✅ Layout Modale Sovrapposto
**Problema**: Elementi della modale si sovrapponevano (pulsanti duplicati)
**Correzione**:
- R<PERSON><PERSON> footer esterno dal Dialog
- Utilizzata struttura identica al pannello AFFILIATO
- Nessun pulsante duplicato

### 3. ✅ Gestione Errori nella Modale
**Problema**: Errori non visibili (es. P.IVA duplicata)
**Correzione**:
- Aggiunto Toast per P.IVA duplicata con messaggio chiaro
- Migliorato confirmDialog con messaggi specifici per ruolo
- Aggiunto logging dettagliato per debugging

### 4. ✅ Workflow Registry->Retailer
**Problema**: Modale si bloccava dopo creazione registry
**Correzione**:
- Aggiunto try-catch completo
- Logging dettagliato per ogni step
- Gestione errori migliorata
- Validazione dati utente

## Test Manuale da Eseguire

### Pre-requisiti
- [ ] Frontend in esecuzione su http://localhost:3000
- [ ] Backend in esecuzione su http://localhost:3001
- [ ] Database PostgreSQL attivo
- [ ] Login come utente AGENTE

### Test 1: Layout Pulsante e Modale
1. [ ] Accedere alla "Gestione Clienti" come AGENTE
2. [ ] Verificare che il pulsante di aggiunta sia integrato nella toolbar della tabella
3. [ ] Verificare che abbia lo stesso stile del pannello AFFILIATO
4. [ ] Cliccare sul pulsante e verificare che si apra la modale
5. [ ] Verificare che la modale non abbia elementi sovrapposti
6. [ ] Verificare che ci sia un solo set di pulsanti (non duplicati)

### Test 2: Creazione Nuova Anagrafica (Caso Successo)
1. [ ] Aprire la modale di creazione
2. [ ] Compilare tutti i campi obbligatori con dati nuovi:
   - Nome: "Test"
   - Cognome: "Agente"
   - Email: "<EMAIL>"
   - Tel: "0212345678"
   - Cell: "3331234567"
   - P.IVA: "12345678901" (nuova)
   - Indirizzo: "Via Test 123"
   - Città: "Milano"
   - CAP: "20100"
   - Metodo Pagamento: "Contanti"
3. [ ] Cliccare "Salva"
4. [ ] Verificare che appaia il Toast di successo
5. [ ] Verificare che la pagina si ricarichi automaticamente
6. [ ] Verificare che il nuovo cliente appaia nella lista

### Test 3: Gestione P.IVA Duplicata
1. [ ] Aprire la modale di creazione
2. [ ] Compilare i campi con una P.IVA già esistente
3. [ ] Cliccare "Salva"
4. [ ] Verificare che appaia:
   - [ ] Toast di avviso per P.IVA duplicata
   - [ ] Dialog di conferma con messaggio chiaro
5. [ ] Cliccare "Sì, utilizza"
6. [ ] Verificare che i campi si riempiano con i dati esistenti
7. [ ] Verificare che appaia il Toast informativo
8. [ ] Cliccare "Salva" (secondo pulsante)
9. [ ] Verificare che il cliente venga creato con successo

### Test 4: Validazione Campi
1. [ ] Aprire la modale di creazione
2. [ ] Lasciare vuoti i campi obbligatori
3. [ ] Cliccare "Salva"
4. [ ] Verificare che appaiano gli errori di validazione sotto ogni campo
5. [ ] Compilare un campo alla volta e verificare che l'errore scompaia

### Test 5: Gestione Errori di Rete
1. [ ] Fermare temporaneamente il backend
2. [ ] Tentare di creare una nuova anagrafica
3. [ ] Verificare che appaia un Toast di errore chiaro
4. [ ] Riavviare il backend e riprovare

### Test 6: Cancellazione Operazione
1. [ ] Aprire la modale di creazione
2. [ ] Compilare alcuni campi
3. [ ] Cliccare "Annulla"
4. [ ] Verificare che la modale si chiuda senza salvare

## Logging e Debug

### Console Browser
Durante i test, verificare nella console del browser:
- [ ] Log di inizio creazione: "🚀 Inizio creazione anagrafica per AGENTE"
- [ ] Log dati utente: "👤 Dati utente:"
- [ ] Log dati registry: "📝 Dati da inviare per registry:"
- [ ] Log successo registry: "✅ Registry creato con successo:"
- [ ] Log dati retailer: "🔗 Dati per creazione retailer:"
- [ ] Log successo retailer: "✅ Retailer creato con successo:"

### Network Tab
Verificare le chiamate API:
- [ ] POST /registry/ - Creazione anagrafica
- [ ] POST /retailers/ - Associazione a PDV
- [ ] GET /paymentmethods/ - Caricamento metodi pagamento

## Risultati Attesi

### Successo Completo
- ✅ Pulsante integrato correttamente nella toolbar
- ✅ Modale si apre e chiude correttamente
- ✅ Validazione campi funzionante
- ✅ Creazione anagrafica completata
- ✅ Toast di successo visualizzato
- ✅ Lista clienti aggiornata automaticamente

### Gestione Errori
- ✅ P.IVA duplicata gestita con dialog chiaro
- ✅ Errori di rete mostrati con Toast
- ✅ Validazione campi visibile all'utente
- ✅ Logging completo in console per debugging

## Note per il Debug

Se si verificano problemi:
1. Controllare la console del browser per i log dettagliati
2. Verificare il Network tab per errori API
3. Controllare che l'utente sia loggato come AGENTE
4. Verificare che il backend sia raggiungibile
5. Controllare i dati nel localStorage (userid, login_token)

## Checklist Finale

- [ ] Tutti i test passati
- [ ] Nessun errore in console
- [ ] Funzionalità equivalente a quella AFFILIATO
- [ ] UX migliorata con messaggi chiari
- [ ] Workflow completo funzionante
