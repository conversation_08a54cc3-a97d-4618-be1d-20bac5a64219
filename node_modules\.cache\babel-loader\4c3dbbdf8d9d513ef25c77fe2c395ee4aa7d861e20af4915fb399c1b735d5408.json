{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useEffect, useRef } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { addGlobalMouseDownEvent, getTargetFromEvent } from '../utils/uiUtil';\nexport default function usePickerInput(_ref) {\n  var open = _ref.open,\n    value = _ref.value,\n    isClickOutside = _ref.isClickOutside,\n    triggerOpen = _ref.triggerOpen,\n    forwardKeyDown = _ref.forwardKeyDown,\n    _onKeyDown = _ref.onKeyDown,\n    blurToCancel = _ref.blurToCancel,\n    onSubmit = _ref.onSubmit,\n    onCancel = _ref.onCancel,\n    _onFocus = _ref.onFocus,\n    _onBlur = _ref.onBlur;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    typing = _useState2[0],\n    setTyping = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    focused = _useState4[0],\n    setFocused = _useState4[1];\n  /**\n   * We will prevent blur to handle open event when user click outside,\n   * since this will repeat trigger `onOpenChange` event.\n   */\n\n  var preventBlurRef = useRef(false);\n  var valueChangedRef = useRef(false);\n  var preventDefaultRef = useRef(false);\n  var inputProps = {\n    onMouseDown: function onMouseDown() {\n      setTyping(true);\n      triggerOpen(true);\n    },\n    onKeyDown: function onKeyDown(e) {\n      var preventDefault = function preventDefault() {\n        preventDefaultRef.current = true;\n      };\n      _onKeyDown(e, preventDefault);\n      if (preventDefaultRef.current) return;\n      switch (e.which) {\n        case KeyCode.ENTER:\n          {\n            if (!open) {\n              triggerOpen(true);\n            } else if (onSubmit() !== false) {\n              setTyping(true);\n            }\n            e.preventDefault();\n            return;\n          }\n        case KeyCode.TAB:\n          {\n            if (typing && open && !e.shiftKey) {\n              setTyping(false);\n              e.preventDefault();\n            } else if (!typing && open) {\n              if (!forwardKeyDown(e) && e.shiftKey) {\n                setTyping(true);\n                e.preventDefault();\n              }\n            }\n            return;\n          }\n        case KeyCode.ESC:\n          {\n            setTyping(true);\n            onCancel();\n            return;\n          }\n      }\n      if (!open && ![KeyCode.SHIFT].includes(e.which)) {\n        triggerOpen(true);\n      } else if (!typing) {\n        // Let popup panel handle keyboard\n        forwardKeyDown(e);\n      }\n    },\n    onFocus: function onFocus(e) {\n      setTyping(true);\n      setFocused(true);\n      if (_onFocus) {\n        _onFocus(e);\n      }\n    },\n    onBlur: function onBlur(e) {\n      if (preventBlurRef.current || !isClickOutside(document.activeElement)) {\n        preventBlurRef.current = false;\n        return;\n      }\n      if (blurToCancel) {\n        setTimeout(function () {\n          var _document = document,\n            activeElement = _document.activeElement;\n          while (activeElement && activeElement.shadowRoot) {\n            activeElement = activeElement.shadowRoot.activeElement;\n          }\n          if (isClickOutside(activeElement)) {\n            onCancel();\n          }\n        }, 0);\n      } else if (open) {\n        triggerOpen(false);\n        if (valueChangedRef.current) {\n          onSubmit();\n        }\n      }\n      setFocused(false);\n      if (_onBlur) {\n        _onBlur(e);\n      }\n    }\n  }; // check if value changed\n\n  useEffect(function () {\n    valueChangedRef.current = false;\n  }, [open]);\n  useEffect(function () {\n    valueChangedRef.current = true;\n  }, [value]); // Global click handler\n\n  useEffect(function () {\n    return addGlobalMouseDownEvent(function (e) {\n      var target = getTargetFromEvent(e);\n      if (open) {\n        var clickedOutside = isClickOutside(target);\n        if (!clickedOutside) {\n          preventBlurRef.current = true; // Always set back in case `onBlur` prevented by user\n\n          requestAnimationFrame(function () {\n            preventBlurRef.current = false;\n          });\n        } else if (!focused || clickedOutside) {\n          triggerOpen(false);\n        }\n      }\n    });\n  });\n  return [inputProps, {\n    focused: focused,\n    typing: typing\n  }];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useState", "useEffect", "useRef", "KeyCode", "addGlobalMouseDownEvent", "getTargetFromEvent", "usePickerInput", "_ref", "open", "value", "isClickOutside", "triggerOpen", "forwardKeyDown", "_onKeyDown", "onKeyDown", "blurToCancel", "onSubmit", "onCancel", "_onFocus", "onFocus", "_onBlur", "onBlur", "_useState", "_useState2", "typing", "setTyping", "_useState3", "_useState4", "focused", "setFocused", "preventBlurRef", "valueChangedRef", "preventDefaultRef", "inputProps", "onMouseDown", "e", "preventDefault", "current", "which", "ENTER", "TAB", "shift<PERSON>ey", "ESC", "SHIFT", "includes", "document", "activeElement", "setTimeout", "_document", "shadowRoot", "target", "clickedOutside", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-picker/es/hooks/usePickerInput.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useEffect, useRef } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { addGlobalMouseDownEvent, getTargetFromEvent } from '../utils/uiUtil';\nexport default function usePickerInput(_ref) {\n  var open = _ref.open,\n      value = _ref.value,\n      isClickOutside = _ref.isClickOutside,\n      triggerOpen = _ref.triggerOpen,\n      forwardKeyDown = _ref.forwardKeyDown,\n      _onKeyDown = _ref.onKeyDown,\n      blurToCancel = _ref.blurToCancel,\n      onSubmit = _ref.onSubmit,\n      onCancel = _ref.onCancel,\n      _onFocus = _ref.onFocus,\n      _onBlur = _ref.onBlur;\n\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      typing = _useState2[0],\n      setTyping = _useState2[1];\n\n  var _useState3 = useState(false),\n      _useState4 = _slicedToArray(_useState3, 2),\n      focused = _useState4[0],\n      setFocused = _useState4[1];\n  /**\n   * We will prevent blur to handle open event when user click outside,\n   * since this will repeat trigger `onOpenChange` event.\n   */\n\n\n  var preventBlurRef = useRef(false);\n  var valueChangedRef = useRef(false);\n  var preventDefaultRef = useRef(false);\n  var inputProps = {\n    onMouseDown: function onMouseDown() {\n      setTyping(true);\n      triggerOpen(true);\n    },\n    onKeyDown: function onKeyDown(e) {\n      var preventDefault = function preventDefault() {\n        preventDefaultRef.current = true;\n      };\n\n      _onKeyDown(e, preventDefault);\n\n      if (preventDefaultRef.current) return;\n\n      switch (e.which) {\n        case KeyCode.ENTER:\n          {\n            if (!open) {\n              triggerOpen(true);\n            } else if (onSubmit() !== false) {\n              setTyping(true);\n            }\n\n            e.preventDefault();\n            return;\n          }\n\n        case KeyCode.TAB:\n          {\n            if (typing && open && !e.shiftKey) {\n              setTyping(false);\n              e.preventDefault();\n            } else if (!typing && open) {\n              if (!forwardKeyDown(e) && e.shiftKey) {\n                setTyping(true);\n                e.preventDefault();\n              }\n            }\n\n            return;\n          }\n\n        case KeyCode.ESC:\n          {\n            setTyping(true);\n            onCancel();\n            return;\n          }\n      }\n\n      if (!open && ![KeyCode.SHIFT].includes(e.which)) {\n        triggerOpen(true);\n      } else if (!typing) {\n        // Let popup panel handle keyboard\n        forwardKeyDown(e);\n      }\n    },\n    onFocus: function onFocus(e) {\n      setTyping(true);\n      setFocused(true);\n\n      if (_onFocus) {\n        _onFocus(e);\n      }\n    },\n    onBlur: function onBlur(e) {\n      if (preventBlurRef.current || !isClickOutside(document.activeElement)) {\n        preventBlurRef.current = false;\n        return;\n      }\n\n      if (blurToCancel) {\n        setTimeout(function () {\n          var _document = document,\n              activeElement = _document.activeElement;\n\n          while (activeElement && activeElement.shadowRoot) {\n            activeElement = activeElement.shadowRoot.activeElement;\n          }\n\n          if (isClickOutside(activeElement)) {\n            onCancel();\n          }\n        }, 0);\n      } else if (open) {\n        triggerOpen(false);\n\n        if (valueChangedRef.current) {\n          onSubmit();\n        }\n      }\n\n      setFocused(false);\n\n      if (_onBlur) {\n        _onBlur(e);\n      }\n    }\n  }; // check if value changed\n\n  useEffect(function () {\n    valueChangedRef.current = false;\n  }, [open]);\n  useEffect(function () {\n    valueChangedRef.current = true;\n  }, [value]); // Global click handler\n\n  useEffect(function () {\n    return addGlobalMouseDownEvent(function (e) {\n      var target = getTargetFromEvent(e);\n\n      if (open) {\n        var clickedOutside = isClickOutside(target);\n\n        if (!clickedOutside) {\n          preventBlurRef.current = true; // Always set back in case `onBlur` prevented by user\n\n          requestAnimationFrame(function () {\n            preventBlurRef.current = false;\n          });\n        } else if (!focused || clickedOutside) {\n          triggerOpen(false);\n        }\n      }\n    });\n  });\n  return [inputProps, {\n    focused: focused,\n    typing: typing\n  }];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,uBAAuB,EAAEC,kBAAkB,QAAQ,iBAAiB;AAC7E,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3C,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BC,cAAc,GAAGL,IAAI,CAACK,cAAc;IACpCC,UAAU,GAAGN,IAAI,CAACO,SAAS;IAC3BC,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;IACxBC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;IACxBC,QAAQ,GAAGX,IAAI,CAACY,OAAO;IACvBC,OAAO,GAAGb,IAAI,CAACc,MAAM;EAEzB,IAAIC,SAAS,GAAGtB,QAAQ,CAAC,KAAK,CAAC;IAC3BuB,UAAU,GAAGxB,cAAc,CAACuB,SAAS,EAAE,CAAC,CAAC;IACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE7B,IAAIG,UAAU,GAAG1B,QAAQ,CAAC,KAAK,CAAC;IAC5B2B,UAAU,GAAG5B,cAAc,CAAC2B,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9B;AACF;AACA;AACA;;EAGE,IAAIG,cAAc,GAAG5B,MAAM,CAAC,KAAK,CAAC;EAClC,IAAI6B,eAAe,GAAG7B,MAAM,CAAC,KAAK,CAAC;EACnC,IAAI8B,iBAAiB,GAAG9B,MAAM,CAAC,KAAK,CAAC;EACrC,IAAI+B,UAAU,GAAG;IACfC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClCT,SAAS,CAAC,IAAI,CAAC;MACfd,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IACDG,SAAS,EAAE,SAASA,SAASA,CAACqB,CAAC,EAAE;MAC/B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC7CJ,iBAAiB,CAACK,OAAO,GAAG,IAAI;MAClC,CAAC;MAEDxB,UAAU,CAACsB,CAAC,EAAEC,cAAc,CAAC;MAE7B,IAAIJ,iBAAiB,CAACK,OAAO,EAAE;MAE/B,QAAQF,CAAC,CAACG,KAAK;QACb,KAAKnC,OAAO,CAACoC,KAAK;UAChB;YACE,IAAI,CAAC/B,IAAI,EAAE;cACTG,WAAW,CAAC,IAAI,CAAC;YACnB,CAAC,MAAM,IAAIK,QAAQ,CAAC,CAAC,KAAK,KAAK,EAAE;cAC/BS,SAAS,CAAC,IAAI,CAAC;YACjB;YAEAU,CAAC,CAACC,cAAc,CAAC,CAAC;YAClB;UACF;QAEF,KAAKjC,OAAO,CAACqC,GAAG;UACd;YACE,IAAIhB,MAAM,IAAIhB,IAAI,IAAI,CAAC2B,CAAC,CAACM,QAAQ,EAAE;cACjChB,SAAS,CAAC,KAAK,CAAC;cAChBU,CAAC,CAACC,cAAc,CAAC,CAAC;YACpB,CAAC,MAAM,IAAI,CAACZ,MAAM,IAAIhB,IAAI,EAAE;cAC1B,IAAI,CAACI,cAAc,CAACuB,CAAC,CAAC,IAAIA,CAAC,CAACM,QAAQ,EAAE;gBACpChB,SAAS,CAAC,IAAI,CAAC;gBACfU,CAAC,CAACC,cAAc,CAAC,CAAC;cACpB;YACF;YAEA;UACF;QAEF,KAAKjC,OAAO,CAACuC,GAAG;UACd;YACEjB,SAAS,CAAC,IAAI,CAAC;YACfR,QAAQ,CAAC,CAAC;YACV;UACF;MACJ;MAEA,IAAI,CAACT,IAAI,IAAI,CAAC,CAACL,OAAO,CAACwC,KAAK,CAAC,CAACC,QAAQ,CAACT,CAAC,CAACG,KAAK,CAAC,EAAE;QAC/C3B,WAAW,CAAC,IAAI,CAAC;MACnB,CAAC,MAAM,IAAI,CAACa,MAAM,EAAE;QAClB;QACAZ,cAAc,CAACuB,CAAC,CAAC;MACnB;IACF,CAAC;IACDhB,OAAO,EAAE,SAASA,OAAOA,CAACgB,CAAC,EAAE;MAC3BV,SAAS,CAAC,IAAI,CAAC;MACfI,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIX,QAAQ,EAAE;QACZA,QAAQ,CAACiB,CAAC,CAAC;MACb;IACF,CAAC;IACDd,MAAM,EAAE,SAASA,MAAMA,CAACc,CAAC,EAAE;MACzB,IAAIL,cAAc,CAACO,OAAO,IAAI,CAAC3B,cAAc,CAACmC,QAAQ,CAACC,aAAa,CAAC,EAAE;QACrEhB,cAAc,CAACO,OAAO,GAAG,KAAK;QAC9B;MACF;MAEA,IAAItB,YAAY,EAAE;QAChBgC,UAAU,CAAC,YAAY;UACrB,IAAIC,SAAS,GAAGH,QAAQ;YACpBC,aAAa,GAAGE,SAAS,CAACF,aAAa;UAE3C,OAAOA,aAAa,IAAIA,aAAa,CAACG,UAAU,EAAE;YAChDH,aAAa,GAAGA,aAAa,CAACG,UAAU,CAACH,aAAa;UACxD;UAEA,IAAIpC,cAAc,CAACoC,aAAa,CAAC,EAAE;YACjC7B,QAAQ,CAAC,CAAC;UACZ;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM,IAAIT,IAAI,EAAE;QACfG,WAAW,CAAC,KAAK,CAAC;QAElB,IAAIoB,eAAe,CAACM,OAAO,EAAE;UAC3BrB,QAAQ,CAAC,CAAC;QACZ;MACF;MAEAa,UAAU,CAAC,KAAK,CAAC;MAEjB,IAAIT,OAAO,EAAE;QACXA,OAAO,CAACe,CAAC,CAAC;MACZ;IACF;EACF,CAAC,CAAC,CAAC;;EAEHlC,SAAS,CAAC,YAAY;IACpB8B,eAAe,CAACM,OAAO,GAAG,KAAK;EACjC,CAAC,EAAE,CAAC7B,IAAI,CAAC,CAAC;EACVP,SAAS,CAAC,YAAY;IACpB8B,eAAe,CAACM,OAAO,GAAG,IAAI;EAChC,CAAC,EAAE,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEbR,SAAS,CAAC,YAAY;IACpB,OAAOG,uBAAuB,CAAC,UAAU+B,CAAC,EAAE;MAC1C,IAAIe,MAAM,GAAG7C,kBAAkB,CAAC8B,CAAC,CAAC;MAElC,IAAI3B,IAAI,EAAE;QACR,IAAI2C,cAAc,GAAGzC,cAAc,CAACwC,MAAM,CAAC;QAE3C,IAAI,CAACC,cAAc,EAAE;UACnBrB,cAAc,CAACO,OAAO,GAAG,IAAI,CAAC,CAAC;;UAE/Be,qBAAqB,CAAC,YAAY;YAChCtB,cAAc,CAACO,OAAO,GAAG,KAAK;UAChC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,CAACT,OAAO,IAAIuB,cAAc,EAAE;UACrCxC,WAAW,CAAC,KAAK,CAAC;QACpB;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,CAACsB,UAAU,EAAE;IAClBL,OAAO,EAAEA,OAAO;IAChBJ,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}