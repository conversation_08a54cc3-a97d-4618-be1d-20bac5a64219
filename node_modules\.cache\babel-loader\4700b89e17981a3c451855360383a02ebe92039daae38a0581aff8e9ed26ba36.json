{"ast": null, "code": "import { isCheckDisabled } from './valueUtil';\nexport var SHOW_ALL = 'SHOW_ALL';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\nexport function formatStrategyValues(values, strategy, keyEntities, fieldNames) {\n  var valueSet = new Set(values);\n  if (strategy === SHOW_CHILD) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      if (entity && entity.children && entity.children.some(function (_ref) {\n        var node = _ref.node;\n        return valueSet.has(node[fieldNames.value]);\n      }) && entity.children.every(function (_ref2) {\n        var node = _ref2.node;\n        return isCheckDisabled(node) || valueSet.has(node[fieldNames.value]);\n      })) {\n        return false;\n      }\n      return true;\n    });\n  }\n  if (strategy === SHOW_PARENT) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      var parent = entity ? entity.parent : null;\n      if (parent && !isCheckDisabled(parent.node) && valueSet.has(parent.key)) {\n        return false;\n      }\n      return true;\n    });\n  }\n  return values;\n}", "map": {"version": 3, "names": ["isCheckDisabled", "SHOW_ALL", "SHOW_PARENT", "SHOW_CHILD", "formatStrategyValues", "values", "strategy", "keyEntities", "fieldNames", "valueSet", "Set", "filter", "key", "entity", "children", "some", "_ref", "node", "has", "value", "every", "_ref2", "parent"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree-select/es/utils/strategyUtil.js"], "sourcesContent": ["import { isCheckDisabled } from './valueUtil';\nexport var SHOW_ALL = 'SHOW_ALL';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\nexport function formatStrategyValues(values, strategy, keyEntities, fieldNames) {\n  var valueSet = new Set(values);\n\n  if (strategy === SHOW_CHILD) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n\n      if (entity && entity.children && entity.children.some(function (_ref) {\n        var node = _ref.node;\n        return valueSet.has(node[fieldNames.value]);\n      }) && entity.children.every(function (_ref2) {\n        var node = _ref2.node;\n        return isCheckDisabled(node) || valueSet.has(node[fieldNames.value]);\n      })) {\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  if (strategy === SHOW_PARENT) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      var parent = entity ? entity.parent : null;\n\n      if (parent && !isCheckDisabled(parent.node) && valueSet.has(parent.key)) {\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  return values;\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,aAAa;AAC7C,OAAO,IAAIC,QAAQ,GAAG,UAAU;AAChC,OAAO,IAAIC,WAAW,GAAG,aAAa;AACtC,OAAO,IAAIC,UAAU,GAAG,YAAY;AACpC,OAAO,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAE;EAC9E,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAACL,MAAM,CAAC;EAE9B,IAAIC,QAAQ,KAAKH,UAAU,EAAE;IAC3B,OAAOE,MAAM,CAACM,MAAM,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIC,MAAM,GAAGN,WAAW,CAACK,GAAG,CAAC;MAE7B,IAAIC,MAAM,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;QACpE,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;QACpB,OAAOR,QAAQ,CAACS,GAAG,CAACD,IAAI,CAACT,UAAU,CAACW,KAAK,CAAC,CAAC;MAC7C,CAAC,CAAC,IAAIN,MAAM,CAACC,QAAQ,CAACM,KAAK,CAAC,UAAUC,KAAK,EAAE;QAC3C,IAAIJ,IAAI,GAAGI,KAAK,CAACJ,IAAI;QACrB,OAAOjB,eAAe,CAACiB,IAAI,CAAC,IAAIR,QAAQ,CAACS,GAAG,CAACD,IAAI,CAACT,UAAU,CAACW,KAAK,CAAC,CAAC;MACtE,CAAC,CAAC,EAAE;QACF,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA,IAAIb,QAAQ,KAAKJ,WAAW,EAAE;IAC5B,OAAOG,MAAM,CAACM,MAAM,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIC,MAAM,GAAGN,WAAW,CAACK,GAAG,CAAC;MAC7B,IAAIU,MAAM,GAAGT,MAAM,GAAGA,MAAM,CAACS,MAAM,GAAG,IAAI;MAE1C,IAAIA,MAAM,IAAI,CAACtB,eAAe,CAACsB,MAAM,CAACL,IAAI,CAAC,IAAIR,QAAQ,CAACS,GAAG,CAACI,MAAM,CAACV,GAAG,CAAC,EAAE;QACvE,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA,OAAOP,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}