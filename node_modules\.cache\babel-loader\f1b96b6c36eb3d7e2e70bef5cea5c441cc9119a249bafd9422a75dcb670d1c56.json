{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar locale = {\n  placeholder: 'Select time',\n  rangePlaceholder: ['Start time', 'End time']\n};\nvar _default = locale;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "locale", "placeholder", "rangePlaceholder", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/time-picker/locale/en_US.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar locale = {\n  placeholder: 'Select time',\n  rangePlaceholder: ['Start time', 'End time']\n};\nvar _default = locale;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B,IAAIE,MAAM,GAAG;EACXC,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU;AAC7C,CAAC;AACD,IAAIC,QAAQ,GAAGH,MAAM;AACrBF,OAAO,CAAC,SAAS,CAAC,GAAGK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}