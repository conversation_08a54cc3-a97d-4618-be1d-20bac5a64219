{"ast": null, "code": "/* eslint-disable import/prefer-default-export, prefer-destructuring */\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { generate } from '@ant-design/colors';\nimport devWarning from '../_util/devWarning';\nvar dynamicStyleMark = \"-ant-\".concat(Date.now(), \"-\").concat(Math.random());\nexport function getStyle(globalPrefixCls, theme) {\n  var variables = {};\n  var formatColor = function formatColor(color, updater) {\n    var clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n  var fillColor = function fillColor(colorVal, type) {\n    var baseColor = new TinyColor(colorVal);\n    var colorPalettes = generate(baseColor.toRgbString());\n    variables[\"\".concat(type, \"-color\")] = formatColor(baseColor);\n    variables[\"\".concat(type, \"-color-disabled\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-hover\")] = colorPalettes[4];\n    variables[\"\".concat(type, \"-color-active\")] = colorPalettes[7];\n    variables[\"\".concat(type, \"-color-outline\")] = baseColor.clone().setAlpha(0.2).toRgbString();\n    variables[\"\".concat(type, \"-color-deprecated-bg\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-deprecated-border\")] = colorPalettes[3];\n  }; // ================ Primary Color ================\n\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    var primaryColor = new TinyColor(theme.primaryColor);\n    var primaryColors = generate(primaryColor.toRgbString()); // Legacy - We should use semantic naming standard\n\n    primaryColors.forEach(function (color, index) {\n      variables[\"primary-\".concat(index + 1)] = color;\n    }); // Deprecated\n\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, function (c) {\n      return c.lighten(35);\n    });\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, function (c) {\n      return c.lighten(20);\n    });\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, function (c) {\n      return c.tint(20);\n    });\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, function (c) {\n      return c.tint(50);\n    });\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.12);\n    });\n    var primaryActiveColor = new TinyColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.3);\n    });\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, function (c) {\n      return c.darken(2);\n    });\n  } // ================ Success Color ================\n\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  } // ================ Warning Color ================\n\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  } // ================= Error Color =================\n\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  } // ================= Info Color ==================\n\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  } // Convert to css variables\n\n  var cssList = Object.keys(variables).map(function (key) {\n    return \"--\".concat(globalPrefixCls, \"-\").concat(key, \": \").concat(variables[key], \";\");\n  });\n  return \"\\n  :root {\\n    \".concat(cssList.join('\\n'), \"\\n  }\\n  \").trim();\n}\nexport function registerTheme(globalPrefixCls, theme) {\n  var style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, \"\".concat(dynamicStyleMark, \"-dynamic-theme\"));\n  } else {\n    devWarning(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.');\n  }\n}", "map": {"version": 3, "names": ["updateCSS", "canUseDom", "TinyColor", "generate", "dev<PERSON><PERSON><PERSON>", "dynamicStyleMark", "concat", "Date", "now", "Math", "random", "getStyle", "globalPrefixCls", "theme", "variables", "formatColor", "color", "updater", "clone", "toRgbString", "fillColor", "colorVal", "type", "baseColor", "colorPalettes", "<PERSON><PERSON><PERSON><PERSON>", "primaryColor", "primaryColors", "for<PERSON>ach", "index", "c", "lighten", "tint", "get<PERSON><PERSON><PERSON>", "primaryActiveColor", "darken", "successColor", "warningColor", "errorColor", "infoColor", "cssList", "Object", "keys", "map", "key", "join", "trim", "registerTheme", "style"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/config-provider/cssVariables.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export, prefer-destructuring */\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { generate } from '@ant-design/colors';\nimport devWarning from '../_util/devWarning';\nvar dynamicStyleMark = \"-ant-\".concat(Date.now(), \"-\").concat(Math.random());\nexport function getStyle(globalPrefixCls, theme) {\n  var variables = {};\n\n  var formatColor = function formatColor(color, updater) {\n    var clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n\n  var fillColor = function fillColor(colorVal, type) {\n    var baseColor = new TinyColor(colorVal);\n    var colorPalettes = generate(baseColor.toRgbString());\n    variables[\"\".concat(type, \"-color\")] = formatColor(baseColor);\n    variables[\"\".concat(type, \"-color-disabled\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-hover\")] = colorPalettes[4];\n    variables[\"\".concat(type, \"-color-active\")] = colorPalettes[7];\n    variables[\"\".concat(type, \"-color-outline\")] = baseColor.clone().setAlpha(0.2).toRgbString();\n    variables[\"\".concat(type, \"-color-deprecated-bg\")] = colorPalettes[1];\n    variables[\"\".concat(type, \"-color-deprecated-border\")] = colorPalettes[3];\n  }; // ================ Primary Color ================\n\n\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    var primaryColor = new TinyColor(theme.primaryColor);\n    var primaryColors = generate(primaryColor.toRgbString()); // Legacy - We should use semantic naming standard\n\n    primaryColors.forEach(function (color, index) {\n      variables[\"primary-\".concat(index + 1)] = color;\n    }); // Deprecated\n\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, function (c) {\n      return c.lighten(35);\n    });\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, function (c) {\n      return c.lighten(20);\n    });\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, function (c) {\n      return c.tint(20);\n    });\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, function (c) {\n      return c.tint(50);\n    });\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.12);\n    });\n    var primaryActiveColor = new TinyColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, function (c) {\n      return c.setAlpha(c.getAlpha() * 0.3);\n    });\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, function (c) {\n      return c.darken(2);\n    });\n  } // ================ Success Color ================\n\n\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  } // ================ Warning Color ================\n\n\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  } // ================= Error Color =================\n\n\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  } // ================= Info Color ==================\n\n\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  } // Convert to css variables\n\n\n  var cssList = Object.keys(variables).map(function (key) {\n    return \"--\".concat(globalPrefixCls, \"-\").concat(key, \": \").concat(variables[key], \";\");\n  });\n  return \"\\n  :root {\\n    \".concat(cssList.join('\\n'), \"\\n  }\\n  \").trim();\n}\nexport function registerTheme(globalPrefixCls, theme) {\n  var style = getStyle(globalPrefixCls, theme);\n\n  if (canUseDom()) {\n    updateCSS(style, \"\".concat(dynamicStyleMark, \"-dynamic-theme\"));\n  } else {\n    devWarning(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.');\n  }\n}"], "mappings": "AAAA;AACA,SAASA,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,IAAIC,gBAAgB,GAAG,OAAO,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACF,MAAM,CAACG,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;AAC5E,OAAO,SAASC,QAAQA,CAACC,eAAe,EAAEC,KAAK,EAAE;EAC/C,IAAIC,SAAS,GAAG,CAAC,CAAC;EAElB,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACrD,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAAC,CAAC;IACzBA,KAAK,GAAG,CAACD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,KAAK,CAAC,KAAKA,KAAK;IACnF,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC;EAC5B,CAAC;EAED,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,QAAQ,EAAEC,IAAI,EAAE;IACjD,IAAIC,SAAS,GAAG,IAAIrB,SAAS,CAACmB,QAAQ,CAAC;IACvC,IAAIG,aAAa,GAAGrB,QAAQ,CAACoB,SAAS,CAACJ,WAAW,CAAC,CAAC,CAAC;IACrDL,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,QAAQ,CAAC,CAAC,GAAGP,WAAW,CAACQ,SAAS,CAAC;IAC7DT,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,iBAAiB,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IAChEV,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,cAAc,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IAC7DV,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,eAAe,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IAC9DV,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,gBAAgB,CAAC,CAAC,GAAGC,SAAS,CAACL,KAAK,CAAC,CAAC,CAACO,QAAQ,CAAC,GAAG,CAAC,CAACN,WAAW,CAAC,CAAC;IAC5FL,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,sBAAsB,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IACrEV,SAAS,CAAC,EAAE,CAACR,MAAM,CAACgB,IAAI,EAAE,0BAA0B,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;EAC3E,CAAC,CAAC,CAAC;;EAGH,IAAIX,KAAK,CAACa,YAAY,EAAE;IACtBN,SAAS,CAACP,KAAK,CAACa,YAAY,EAAE,SAAS,CAAC;IACxC,IAAIA,YAAY,GAAG,IAAIxB,SAAS,CAACW,KAAK,CAACa,YAAY,CAAC;IACpD,IAAIC,aAAa,GAAGxB,QAAQ,CAACuB,YAAY,CAACP,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1DQ,aAAa,CAACC,OAAO,CAAC,UAAUZ,KAAK,EAAEa,KAAK,EAAE;MAC5Cf,SAAS,CAAC,UAAU,CAACR,MAAM,CAACuB,KAAK,GAAG,CAAC,CAAC,CAAC,GAAGb,KAAK;IACjD,CAAC,CAAC,CAAC,CAAC;;IAEJF,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC;IACFjB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC;IACFjB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;IACFlB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC;IACFlB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAE,UAAUI,CAAC,EAAE;MAClF,OAAOA,CAAC,CAACL,QAAQ,CAACK,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IACxC,CAAC,CAAC;IACF,IAAIC,kBAAkB,GAAG,IAAIhC,SAAS,CAACyB,aAAa,CAAC,CAAC,CAAC,CAAC;IACxDb,SAAS,CAAC,sCAAsC,CAAC,GAAGC,WAAW,CAACmB,kBAAkB,EAAE,UAAUJ,CAAC,EAAE;MAC/F,OAAOA,CAAC,CAACL,QAAQ,CAACK,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;IACvC,CAAC,CAAC;IACFnB,SAAS,CAAC,sCAAsC,CAAC,GAAGC,WAAW,CAACmB,kBAAkB,EAAE,UAAUJ,CAAC,EAAE;MAC/F,OAAOA,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAItB,KAAK,CAACuB,YAAY,EAAE;IACtBhB,SAAS,CAACP,KAAK,CAACuB,YAAY,EAAE,SAAS,CAAC;EAC1C,CAAC,CAAC;;EAGF,IAAIvB,KAAK,CAACwB,YAAY,EAAE;IACtBjB,SAAS,CAACP,KAAK,CAACwB,YAAY,EAAE,SAAS,CAAC;EAC1C,CAAC,CAAC;;EAGF,IAAIxB,KAAK,CAACyB,UAAU,EAAE;IACpBlB,SAAS,CAACP,KAAK,CAACyB,UAAU,EAAE,OAAO,CAAC;EACtC,CAAC,CAAC;;EAGF,IAAIzB,KAAK,CAAC0B,SAAS,EAAE;IACnBnB,SAAS,CAACP,KAAK,CAAC0B,SAAS,EAAE,MAAM,CAAC;EACpC,CAAC,CAAC;;EAGF,IAAIC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC5B,SAAS,CAAC,CAAC6B,GAAG,CAAC,UAAUC,GAAG,EAAE;IACtD,OAAO,IAAI,CAACtC,MAAM,CAACM,eAAe,EAAE,GAAG,CAAC,CAACN,MAAM,CAACsC,GAAG,EAAE,IAAI,CAAC,CAACtC,MAAM,CAACQ,SAAS,CAAC8B,GAAG,CAAC,EAAE,GAAG,CAAC;EACxF,CAAC,CAAC;EACF,OAAO,mBAAmB,CAACtC,MAAM,CAACkC,OAAO,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAACC,IAAI,CAAC,CAAC;AAC3E;AACA,OAAO,SAASC,aAAaA,CAACnC,eAAe,EAAEC,KAAK,EAAE;EACpD,IAAImC,KAAK,GAAGrC,QAAQ,CAACC,eAAe,EAAEC,KAAK,CAAC;EAE5C,IAAIZ,SAAS,CAAC,CAAC,EAAE;IACfD,SAAS,CAACgD,KAAK,EAAE,EAAE,CAAC1C,MAAM,CAACD,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;EACjE,CAAC,MAAM;IACLD,UAAU,CAAC,KAAK,EAAE,gBAAgB,EAAE,sDAAsD,CAAC;EAC7F;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}