{"ast": null, "code": "import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from '../util/motion';\nexport default (function (callback) {\n  var cacheElementRef = useRef(); // Cache callback\n\n  var callbackRef = useRef(callback);\n  callbackRef.current = callback; // Internal motion event handler\n\n  var onInternalMotionEnd = React.useCallback(function (event) {\n    callbackRef.current(event);\n  }, []); // Remove events\n\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  } // Patch events\n\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd); // Save as cache in case dom removed trigger by `motionDeadline`\n\n      cacheElementRef.current = element;\n    }\n  } // Clean up when removed\n\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});", "map": {"version": 3, "names": ["React", "useRef", "animationEndName", "transitionEndName", "callback", "cacheElementRef", "callback<PERSON><PERSON>", "current", "onInternalMotionEnd", "useCallback", "event", "removeMotionEvents", "element", "removeEventListener", "patchMotionEvents", "addEventListener", "useEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/hooks/useDomMotionEvents.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from '../util/motion';\nexport default (function (callback) {\n  var cacheElementRef = useRef(); // Cache callback\n\n  var callbackRef = useRef(callback);\n  callbackRef.current = callback; // Internal motion event handler\n\n  var onInternalMotionEnd = React.useCallback(function (event) {\n    callbackRef.current(event);\n  }, []); // Remove events\n\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  } // Patch events\n\n\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd); // Save as cache in case dom removed trigger by `motionDeadline`\n\n      cacheElementRef.current = element;\n    }\n  } // Clean up when removed\n\n\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,gBAAgB;AACpE,gBAAgB,UAAUC,QAAQ,EAAE;EAClC,IAAIC,eAAe,GAAGJ,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIK,WAAW,GAAGL,MAAM,CAACG,QAAQ,CAAC;EAClCE,WAAW,CAACC,OAAO,GAAGH,QAAQ,CAAC,CAAC;;EAEhC,IAAII,mBAAmB,GAAGR,KAAK,CAACS,WAAW,CAAC,UAAUC,KAAK,EAAE;IAC3DJ,WAAW,CAACC,OAAO,CAACG,KAAK,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,SAASC,kBAAkBA,CAACC,OAAO,EAAE;IACnC,IAAIA,OAAO,EAAE;MACXA,OAAO,CAACC,mBAAmB,CAACV,iBAAiB,EAAEK,mBAAmB,CAAC;MACnEI,OAAO,CAACC,mBAAmB,CAACX,gBAAgB,EAAEM,mBAAmB,CAAC;IACpE;EACF,CAAC,CAAC;;EAGF,SAASM,iBAAiBA,CAACF,OAAO,EAAE;IAClC,IAAIP,eAAe,CAACE,OAAO,IAAIF,eAAe,CAACE,OAAO,KAAKK,OAAO,EAAE;MAClED,kBAAkB,CAACN,eAAe,CAACE,OAAO,CAAC;IAC7C;IAEA,IAAIK,OAAO,IAAIA,OAAO,KAAKP,eAAe,CAACE,OAAO,EAAE;MAClDK,OAAO,CAACG,gBAAgB,CAACZ,iBAAiB,EAAEK,mBAAmB,CAAC;MAChEI,OAAO,CAACG,gBAAgB,CAACb,gBAAgB,EAAEM,mBAAmB,CAAC,CAAC,CAAC;;MAEjEH,eAAe,CAACE,OAAO,GAAGK,OAAO;IACnC;EACF,CAAC,CAAC;;EAGFZ,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBL,kBAAkB,CAACN,eAAe,CAACE,OAAO,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACO,iBAAiB,EAAEH,kBAAkB,CAAC;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}