{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport TimelineItem from './TimelineItem';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nvar Timeline = function Timeline(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$pending = props.pending,\n    pending = _props$pending === void 0 ? null : _props$pending,\n    pendingDot = props.pendingDot,\n    children = props.children,\n    className = props.className,\n    _props$reverse = props.reverse,\n    reverse = _props$reverse === void 0 ? false : _props$reverse,\n    _props$mode = props.mode,\n    mode = _props$mode === void 0 ? '' : _props$mode,\n    restProps = __rest(props, [\"prefixCls\", \"pending\", \"pendingDot\", \"children\", \"className\", \"reverse\", \"mode\"]);\n  var prefixCls = getPrefixCls('timeline', customizePrefixCls);\n  var pendingNode = typeof pending === 'boolean' ? null : pending;\n  var pendingItem = pending ? /*#__PURE__*/React.createElement(TimelineItem, {\n    pending: !!pending,\n    dot: pendingDot || /*#__PURE__*/React.createElement(LoadingOutlined, null)\n  }, pendingNode) : null;\n  var timeLineItems = React.Children.toArray(children);\n  timeLineItems.push(pendingItem);\n  if (reverse) {\n    timeLineItems.reverse();\n  }\n  var getPositionCls = function getPositionCls(ele, idx) {\n    if (mode === 'alternate') {\n      if (ele.props.position === 'right') return \"\".concat(prefixCls, \"-item-right\");\n      if (ele.props.position === 'left') return \"\".concat(prefixCls, \"-item-left\");\n      return idx % 2 === 0 ? \"\".concat(prefixCls, \"-item-left\") : \"\".concat(prefixCls, \"-item-right\");\n    }\n    if (mode === 'left') return \"\".concat(prefixCls, \"-item-left\");\n    if (mode === 'right') return \"\".concat(prefixCls, \"-item-right\");\n    if (ele.props.position === 'right') return \"\".concat(prefixCls, \"-item-right\");\n    return '';\n  }; // Remove falsy items\n\n  var truthyItems = timeLineItems.filter(function (item) {\n    return !!item;\n  });\n  var itemsCount = React.Children.count(truthyItems);\n  var lastCls = \"\".concat(prefixCls, \"-item-last\");\n  var items = React.Children.map(truthyItems, function (ele, idx) {\n    var pendingClass = idx === itemsCount - 2 ? lastCls : '';\n    var readyClass = idx === itemsCount - 1 ? lastCls : '';\n    return cloneElement(ele, {\n      className: classNames([ele.props.className, !reverse && !!pending ? pendingClass : readyClass, getPositionCls(ele, idx)])\n    });\n  });\n  var hasLabelItem = timeLineItems.some(function (item) {\n    var _a;\n    return !!((_a = item === null || item === void 0 ? void 0 : item.props) === null || _a === void 0 ? void 0 : _a.label);\n  });\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-pending\"), !!pending), _defineProperty(_classNames, \"\".concat(prefixCls, \"-reverse\"), !!reverse), _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(mode), !!mode && !hasLabelItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label\"), hasLabelItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({}, restProps, {\n    className: classString\n  }), items);\n};\nTimeline.Item = TimelineItem;\nexport default Timeline;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "LoadingOutlined", "TimelineItem", "ConfigContext", "cloneElement", "Timeline", "props", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$pending", "pending", "pendingDot", "children", "className", "_props$reverse", "reverse", "_props$mode", "mode", "restProps", "pendingNode", "pendingItem", "createElement", "dot", "timeLineItems", "Children", "toArray", "push", "getPositionCls", "ele", "idx", "position", "concat", "truthyItems", "filter", "item", "itemsCount", "count", "lastCls", "items", "map", "pendingClass", "readyClass", "hasLabelItem", "some", "_a", "label", "classString", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/timeline/Timeline.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport TimelineItem from './TimelineItem';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\n\nvar Timeline = function Timeline(props) {\n  var _classNames;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var customizePrefixCls = props.prefixCls,\n      _props$pending = props.pending,\n      pending = _props$pending === void 0 ? null : _props$pending,\n      pendingDot = props.pendingDot,\n      children = props.children,\n      className = props.className,\n      _props$reverse = props.reverse,\n      reverse = _props$reverse === void 0 ? false : _props$reverse,\n      _props$mode = props.mode,\n      mode = _props$mode === void 0 ? '' : _props$mode,\n      restProps = __rest(props, [\"prefixCls\", \"pending\", \"pendingDot\", \"children\", \"className\", \"reverse\", \"mode\"]);\n\n  var prefixCls = getPrefixCls('timeline', customizePrefixCls);\n  var pendingNode = typeof pending === 'boolean' ? null : pending;\n  var pendingItem = pending ? /*#__PURE__*/React.createElement(TimelineItem, {\n    pending: !!pending,\n    dot: pendingDot || /*#__PURE__*/React.createElement(LoadingOutlined, null)\n  }, pendingNode) : null;\n  var timeLineItems = React.Children.toArray(children);\n  timeLineItems.push(pendingItem);\n\n  if (reverse) {\n    timeLineItems.reverse();\n  }\n\n  var getPositionCls = function getPositionCls(ele, idx) {\n    if (mode === 'alternate') {\n      if (ele.props.position === 'right') return \"\".concat(prefixCls, \"-item-right\");\n      if (ele.props.position === 'left') return \"\".concat(prefixCls, \"-item-left\");\n      return idx % 2 === 0 ? \"\".concat(prefixCls, \"-item-left\") : \"\".concat(prefixCls, \"-item-right\");\n    }\n\n    if (mode === 'left') return \"\".concat(prefixCls, \"-item-left\");\n    if (mode === 'right') return \"\".concat(prefixCls, \"-item-right\");\n    if (ele.props.position === 'right') return \"\".concat(prefixCls, \"-item-right\");\n    return '';\n  }; // Remove falsy items\n\n\n  var truthyItems = timeLineItems.filter(function (item) {\n    return !!item;\n  });\n  var itemsCount = React.Children.count(truthyItems);\n  var lastCls = \"\".concat(prefixCls, \"-item-last\");\n  var items = React.Children.map(truthyItems, function (ele, idx) {\n    var pendingClass = idx === itemsCount - 2 ? lastCls : '';\n    var readyClass = idx === itemsCount - 1 ? lastCls : '';\n    return cloneElement(ele, {\n      className: classNames([ele.props.className, !reverse && !!pending ? pendingClass : readyClass, getPositionCls(ele, idx)])\n    });\n  });\n  var hasLabelItem = timeLineItems.some(function (item) {\n    var _a;\n\n    return !!((_a = item === null || item === void 0 ? void 0 : item.props) === null || _a === void 0 ? void 0 : _a.label);\n  });\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-pending\"), !!pending), _defineProperty(_classNames, \"\".concat(prefixCls, \"-reverse\"), !!reverse), _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(mode), !!mode && !hasLabelItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label\"), hasLabelItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({}, restProps, {\n    className: classString\n  }), items);\n};\n\nTimeline.Item = TimelineItem;\nexport default Timeline;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AAEvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AAEjD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,WAAW;EAEf,IAAIC,iBAAiB,GAAGT,KAAK,CAACU,UAAU,CAACN,aAAa,CAAC;IACnDO,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,kBAAkB,GAAGN,KAAK,CAACO,SAAS;IACpCC,cAAc,GAAGR,KAAK,CAACS,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;IAC3DE,UAAU,GAAGV,KAAK,CAACU,UAAU;IAC7BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,cAAc,GAAGb,KAAK,CAACc,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,cAAc;IAC5DE,WAAW,GAAGf,KAAK,CAACgB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,WAAW;IAChDE,SAAS,GAAGtC,MAAM,CAACqB,KAAK,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;EAEjH,IAAIO,SAAS,GAAGH,YAAY,CAAC,UAAU,EAAEE,kBAAkB,CAAC;EAC5D,IAAIY,WAAW,GAAG,OAAOT,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO;EAC/D,IAAIU,WAAW,GAAGV,OAAO,GAAG,aAAahB,KAAK,CAAC2B,aAAa,CAACxB,YAAY,EAAE;IACzEa,OAAO,EAAE,CAAC,CAACA,OAAO;IAClBY,GAAG,EAAEX,UAAU,IAAI,aAAajB,KAAK,CAAC2B,aAAa,CAACzB,eAAe,EAAE,IAAI;EAC3E,CAAC,EAAEuB,WAAW,CAAC,GAAG,IAAI;EACtB,IAAII,aAAa,GAAG7B,KAAK,CAAC8B,QAAQ,CAACC,OAAO,CAACb,QAAQ,CAAC;EACpDW,aAAa,CAACG,IAAI,CAACN,WAAW,CAAC;EAE/B,IAAIL,OAAO,EAAE;IACXQ,aAAa,CAACR,OAAO,CAAC,CAAC;EACzB;EAEA,IAAIY,cAAc,GAAG,SAASA,cAAcA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACrD,IAAIZ,IAAI,KAAK,WAAW,EAAE;MACxB,IAAIW,GAAG,CAAC3B,KAAK,CAAC6B,QAAQ,KAAK,OAAO,EAAE,OAAO,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,aAAa,CAAC;MAC9E,IAAIoB,GAAG,CAAC3B,KAAK,CAAC6B,QAAQ,KAAK,MAAM,EAAE,OAAO,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,YAAY,CAAC;MAC5E,OAAOqB,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAACE,MAAM,CAACvB,SAAS,EAAE,YAAY,CAAC,GAAG,EAAE,CAACuB,MAAM,CAACvB,SAAS,EAAE,aAAa,CAAC;IACjG;IAEA,IAAIS,IAAI,KAAK,MAAM,EAAE,OAAO,EAAE,CAACc,MAAM,CAACvB,SAAS,EAAE,YAAY,CAAC;IAC9D,IAAIS,IAAI,KAAK,OAAO,EAAE,OAAO,EAAE,CAACc,MAAM,CAACvB,SAAS,EAAE,aAAa,CAAC;IAChE,IAAIoB,GAAG,CAAC3B,KAAK,CAAC6B,QAAQ,KAAK,OAAO,EAAE,OAAO,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,aAAa,CAAC;IAC9E,OAAO,EAAE;EACX,CAAC,CAAC,CAAC;;EAGH,IAAIwB,WAAW,GAAGT,aAAa,CAACU,MAAM,CAAC,UAAUC,IAAI,EAAE;IACrD,OAAO,CAAC,CAACA,IAAI;EACf,CAAC,CAAC;EACF,IAAIC,UAAU,GAAGzC,KAAK,CAAC8B,QAAQ,CAACY,KAAK,CAACJ,WAAW,CAAC;EAClD,IAAIK,OAAO,GAAG,EAAE,CAACN,MAAM,CAACvB,SAAS,EAAE,YAAY,CAAC;EAChD,IAAI8B,KAAK,GAAG5C,KAAK,CAAC8B,QAAQ,CAACe,GAAG,CAACP,WAAW,EAAE,UAAUJ,GAAG,EAAEC,GAAG,EAAE;IAC9D,IAAIW,YAAY,GAAGX,GAAG,KAAKM,UAAU,GAAG,CAAC,GAAGE,OAAO,GAAG,EAAE;IACxD,IAAII,UAAU,GAAGZ,GAAG,KAAKM,UAAU,GAAG,CAAC,GAAGE,OAAO,GAAG,EAAE;IACtD,OAAOtC,YAAY,CAAC6B,GAAG,EAAE;MACvBf,SAAS,EAAElB,UAAU,CAAC,CAACiC,GAAG,CAAC3B,KAAK,CAACY,SAAS,EAAE,CAACE,OAAO,IAAI,CAAC,CAACL,OAAO,GAAG8B,YAAY,GAAGC,UAAU,EAAEd,cAAc,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAC;IAC1H,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIa,YAAY,GAAGnB,aAAa,CAACoB,IAAI,CAAC,UAAUT,IAAI,EAAE;IACpD,IAAIU,EAAE;IAEN,OAAO,CAAC,EAAE,CAACA,EAAE,GAAGV,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACjC,KAAK,MAAM,IAAI,IAAI2C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,KAAK,CAAC;EACxH,CAAC,CAAC;EACF,IAAIC,WAAW,GAAGnD,UAAU,CAACa,SAAS,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAEvB,eAAe,CAACuB,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC,CAACE,OAAO,CAAC,EAAE/B,eAAe,CAACuB,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC,CAACO,OAAO,CAAC,EAAEpC,eAAe,CAACuB,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,GAAG,CAAC,CAACuB,MAAM,CAACd,IAAI,CAAC,EAAE,CAAC,CAACA,IAAI,IAAI,CAACyB,YAAY,CAAC,EAAE/D,eAAe,CAACuB,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,QAAQ,CAAC,EAAEkC,YAAY,CAAC,EAAE/D,eAAe,CAACuB,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,MAAM,CAAC,EAAEF,SAAS,KAAK,KAAK,CAAC,EAAEJ,WAAW,GAAGW,SAAS,CAAC;EACre,OAAO,aAAanB,KAAK,CAAC2B,aAAa,CAAC,IAAI,EAAE3C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,SAAS,EAAE;IACpEL,SAAS,EAAEiC;EACb,CAAC,CAAC,EAAER,KAAK,CAAC;AACZ,CAAC;AAEDtC,QAAQ,CAAC+C,IAAI,GAAGlD,YAAY;AAC5B,eAAeG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}