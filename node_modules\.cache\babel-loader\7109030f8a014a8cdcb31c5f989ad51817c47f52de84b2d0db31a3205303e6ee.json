{"ast": null, "code": "import * as React from 'react';\nimport canUseDom from '../Dom/canUseDom';\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\n\nvar useLayoutEffect = process.env.NODE_ENV !== 'test' && canUseDom() ? React.useLayoutEffect : React.useEffect;\nexport default useLayoutEffect;", "map": {"version": 3, "names": ["React", "canUseDom", "useLayoutEffect", "process", "env", "NODE_ENV", "useEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/hooks/useLayoutEffect.js"], "sourcesContent": ["import * as React from 'react';\nimport canUseDom from '../Dom/canUseDom';\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\n\nvar useLayoutEffect = process.env.NODE_ENV !== 'test' && canUseDom() ? React.useLayoutEffect : React.useEffect;\nexport default useLayoutEffect;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,kBAAkB;AACxC;AACA;AACA;;AAEA,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIJ,SAAS,CAAC,CAAC,GAAGD,KAAK,CAACE,eAAe,GAAGF,KAAK,CAACM,SAAS;AAC9G,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}