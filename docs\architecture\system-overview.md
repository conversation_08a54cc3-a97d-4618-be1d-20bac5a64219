# 🏗️ Panoramica Architettura Sistema E-Procurement

Documentazione dell'architettura generale del sistema E-Procurement Frontend.

## 🎯 Architettura Generale

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Servizi       │
│   (React)       │◄──►│   (Node.js)     │◄──►│   Esterni       │
│                 │    │                 │    │                 │
│ • React 18      │    │ • Express       │    │ • VIES (P.IVA)  │
│ • PrimeReact    │    │ • JWT Auth      │    │ • Email Service │
│ • Redux         │    │ • PostgreSQL    │    │ • File Storage  │
│ • Axios         │    │ • Vercel        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Stack Tecnologico

### **Frontend**
- **Framework**: React 18.x
- **UI Library**: PrimeReact + Bootstrap
- **State Management**: Redux + Redux Thunk
- **HTTP Client**: Axios
- **Routing**: React Router DOM
- **Forms**: React Final Form
- **Maps**: Leaflet + React Leaflet
- **Charts**: Chart.js
- **Icons**: PrimeIcons + Ionicons

### **Backend** (Separato)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL
- **Authentication**: JWT
- **Deployment**: Vercel
- **CORS**: Abilitato per frontend

### **Deployment**
- **Frontend**: Vercel (https://ep-frontend-coral.vercel.app/)
- **Backend**: Vercel (https://epbackend-kekl2s5de-vincenzo-2210s-projects.vercel.app)
- **Domain**: eprocurement.tmselezioni.it

## 📁 Struttura Frontend

```
src/
├── components/              # Componenti riutilizzabili
│   ├── generalizzazioni/   # Utility e helper
│   │   ├── apireq.jsx      # Client HTTP personalizzato
│   │   └── ...
│   └── traduttore/         # Costanti e traduzioni
├── common/                 # Componenti per ruoli specifici
│   ├── distributore/       # Pannello distributore
│   ├── chain/             # Pannello chain
│   └── agente/            # Pannello agente
├── aggiunta_dati/         # Form di inserimento
│   └── aggiungiAnagrafica.jsx
├── css/                   # Stili personalizzati
├── img/                   # Immagini e asset
└── utils/                 # Utility functions
```

## 🔐 Autenticazione

### **Flusso di Autenticazione**
1. **Login**: Utente inserisce credenziali
2. **JWT Token**: Backend restituisce token JWT
3. **Storage**: Token salvato in `localStorage.login_token`
4. **Header**: Ogni richiesta include header `auth: {token}`
5. **Refresh**: Token aggiornato automaticamente dalle risposte

### **Gestione Ruoli**
- **Distributore**: Gestione completa anagrafiche
- **Chain**: Gestione anagrafiche limitate
- **Agente**: Creazione autonoma PDV
- **Admin**: Accesso completo sistema

## 🌐 Comunicazione Frontend-Backend

### **Client HTTP Personalizzato** (`apireq.jsx`)
```javascript
// Configurazione automatica URL
const baseURL = process.env.NODE_ENV === 'production' 
  ? 'https://epbackend-...' 
  : 'http://localhost:3001';

// Header automatici
headers: {
  'auth': localStorage.getItem('login_token'),
  'Content-Type': 'application/json',
  'accept': 'application/json'
}
```

### **Gestione Errori**
- **401**: Sessione scaduta → Redirect login
- **403**: Accesso negato → Messaggio permessi
- **500**: Errore server → Redirect pagina errore (bypassato per lookup)
- **Network**: Problemi connessione → Messaggio offline

## 🔍 Funzionalità Principali

### **1. Gestione Anagrafiche**
- **Inserimento**: Form completo con validazione
- **Modifica**: Modale con campi precompilati
- **Ricerca**: Filtri multipli e paginazione
- **Export**: CSV e Excel

### **2. Lookup P.IVA Automatico** (Nuovo)
- **Servizio**: VIES (VAT Information Exchange System)
- **Trigger**: P.IVA di 11 cifre inserita
- **Auto-compilazione**: Nome, indirizzo, città
- **Fallback**: Compilazione manuale sempre disponibile

### **3. Gestione Intelligente Aziende**
- **Rilevamento**: P.IVA + parole chiave nel nome
- **Validazione**: Cognome opzionale per aziende
- **UI Dinamica**: Icone e messaggi specifici

### **4. Operazioni Bulk**
- **Import**: CSV/Excel con validazione
- **Export**: Dati filtrati in vari formati
- **Batch**: Operazioni multiple su selezioni

## 📊 Flusso Dati

### **Inserimento Anagrafica**
```
1. Utente inserisce P.IVA (11 cifre)
2. Frontend → GET /company-lookup?vat=IT{piva}
3. Backend → Servizio VIES
4. VIES → Dati azienda
5. Frontend → Auto-compilazione campi
6. Utente → Verifica e completa
7. Frontend → POST /registry
8. Backend → Salvataggio database
```

### **Gestione Errori**
```
1. Errore API → Intercettazione apireq.jsx
2. Analisi codice → Gestione specifica
3. Logging → Console per debugging
4. Toast → Messaggio utente
5. Fallback → Funzionalità alternative
```

## 🔧 Configurazione Ambiente

### **Development**
```env
REACT_APP_API_URL=http://localhost:3001
REACT_APP_DEBUG=true
REACT_APP_ENVIRONMENT=development
```

### **Production**
```env
REACT_APP_API_URL=https://epbackend-kekl2s5de-vincenzo-2210s-projects.vercel.app
REACT_APP_DEBUG=false
REACT_APP_ENVIRONMENT=production
```

## 🚀 Performance

### **Ottimizzazioni**
- **Code Splitting**: Lazy loading componenti
- **Memoization**: React.memo per componenti pesanti
- **Debouncing**: Ricerche e validazioni
- **Caching**: Risposte API quando appropriato

### **Bundle Size**
- **Main Bundle**: ~2.5MB (gzipped: ~800KB)
- **Vendor**: React, PrimeReact, Leaflet
- **Chunks**: Lazy loading per route

## 📱 Responsive Design

### **Breakpoints**
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### **Componenti Adattivi**
- **Tabelle**: Scroll orizzontale su mobile
- **Form**: Layout colonne responsive
- **Mappe**: Controlli touch-friendly

---

**Ultimo aggiornamento**: 16 Gennaio 2025
**Versione**: 1.0.0
