{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { Circle as RCCircle } from 'rc-progress';\nimport { presetPrimaryColors } from '@ant-design/colors';\nimport classNames from 'classnames';\nimport { validProgress, getSuccessPercent } from './utils';\nfunction getPercentage(_ref) {\n  var percent = _ref.percent,\n    success = _ref.success,\n    successPercent = _ref.successPercent;\n  var realSuccessPercent = validProgress(getSuccessPercent({\n    success: success,\n    successPercent: successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n}\nfunction getStrokeColor(_ref2) {\n  var _ref2$success = _ref2.success,\n    success = _ref2$success === void 0 ? {} : _ref2$success,\n    strokeColor = _ref2.strokeColor;\n  var successColor = success.strokeColor;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n}\nvar Circle = function Circle(props) {\n  var prefixCls = props.prefixCls,\n    width = props.width,\n    strokeWidth = props.strokeWidth,\n    trailColor = props.trailColor,\n    strokeLinecap = props.strokeLinecap,\n    gapPosition = props.gapPosition,\n    gapDegree = props.gapDegree,\n    type = props.type,\n    children = props.children,\n    success = props.success;\n  var circleSize = width || 120;\n  var circleStyle = {\n    width: circleSize,\n    height: circleSize,\n    fontSize: circleSize * 0.15 + 6\n  };\n  var circleWidth = strokeWidth || 6;\n  var gapPos = gapPosition || type === 'dashboard' && 'bottom' || 'top';\n  var getGapDegree = function getGapDegree() {\n    // Support gapDeg = 0 when type = 'dashboard'\n    if (gapDegree || gapDegree === 0) {\n      return gapDegree;\n    }\n    if (type === 'dashboard') {\n      return 75;\n    }\n    return undefined;\n  }; // using className to style stroke color\n\n  var isGradient = Object.prototype.toString.call(props.strokeColor) === '[object Object]';\n  var strokeColor = getStrokeColor({\n    success: success,\n    strokeColor: props.strokeColor\n  });\n  var wrapperClassName = classNames(\"\".concat(prefixCls, \"-inner\"), _defineProperty({}, \"\".concat(prefixCls, \"-circle-gradient\"), isGradient));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: wrapperClassName,\n    style: circleStyle\n  }, /*#__PURE__*/React.createElement(RCCircle, {\n    percent: getPercentage(props),\n    strokeWidth: circleWidth,\n    trailWidth: circleWidth,\n    strokeColor: strokeColor,\n    strokeLinecap: strokeLinecap,\n    trailColor: trailColor,\n    prefixCls: prefixCls,\n    gapDegree: getGapDegree(),\n    gapPosition: gapPos\n  }), children);\n};\nexport default Circle;", "map": {"version": 3, "names": ["_defineProperty", "React", "Circle", "RCCircle", "presetPrimaryColors", "classNames", "validProgress", "getSuccessPercent", "getPercentage", "_ref", "percent", "success", "successPercent", "realSuccessPercent", "getStrokeColor", "_ref2", "_ref2$success", "strokeColor", "successColor", "green", "props", "prefixCls", "width", "strokeWidth", "trailColor", "strokeLinecap", "gapPosition", "gapDegree", "type", "children", "circleSize", "circleStyle", "height", "fontSize", "circleWidth", "gapPos", "getGapDegree", "undefined", "isGradient", "Object", "prototype", "toString", "call", "wrapperClassName", "concat", "createElement", "className", "style", "trailWidth"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/progress/Circle.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { Circle as RCCircle } from 'rc-progress';\nimport { presetPrimaryColors } from '@ant-design/colors';\nimport classNames from 'classnames';\nimport { validProgress, getSuccessPercent } from './utils';\n\nfunction getPercentage(_ref) {\n  var percent = _ref.percent,\n      success = _ref.success,\n      successPercent = _ref.successPercent;\n  var realSuccessPercent = validProgress(getSuccessPercent({\n    success: success,\n    successPercent: successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n}\n\nfunction getStrokeColor(_ref2) {\n  var _ref2$success = _ref2.success,\n      success = _ref2$success === void 0 ? {} : _ref2$success,\n      strokeColor = _ref2.strokeColor;\n  var successColor = success.strokeColor;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n}\n\nvar Circle = function Circle(props) {\n  var prefixCls = props.prefixCls,\n      width = props.width,\n      strokeWidth = props.strokeWidth,\n      trailColor = props.trailColor,\n      strokeLinecap = props.strokeLinecap,\n      gapPosition = props.gapPosition,\n      gapDegree = props.gapDegree,\n      type = props.type,\n      children = props.children,\n      success = props.success;\n  var circleSize = width || 120;\n  var circleStyle = {\n    width: circleSize,\n    height: circleSize,\n    fontSize: circleSize * 0.15 + 6\n  };\n  var circleWidth = strokeWidth || 6;\n  var gapPos = gapPosition || type === 'dashboard' && 'bottom' || 'top';\n\n  var getGapDegree = function getGapDegree() {\n    // Support gapDeg = 0 when type = 'dashboard'\n    if (gapDegree || gapDegree === 0) {\n      return gapDegree;\n    }\n\n    if (type === 'dashboard') {\n      return 75;\n    }\n\n    return undefined;\n  }; // using className to style stroke color\n\n\n  var isGradient = Object.prototype.toString.call(props.strokeColor) === '[object Object]';\n  var strokeColor = getStrokeColor({\n    success: success,\n    strokeColor: props.strokeColor\n  });\n  var wrapperClassName = classNames(\"\".concat(prefixCls, \"-inner\"), _defineProperty({}, \"\".concat(prefixCls, \"-circle-gradient\"), isGradient));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: wrapperClassName,\n    style: circleStyle\n  }, /*#__PURE__*/React.createElement(RCCircle, {\n    percent: getPercentage(props),\n    strokeWidth: circleWidth,\n    trailWidth: circleWidth,\n    strokeColor: strokeColor,\n    strokeLinecap: strokeLinecap,\n    trailColor: trailColor,\n    prefixCls: prefixCls,\n    gapDegree: getGapDegree(),\n    gapPosition: gapPos\n  }), children);\n};\n\nexport default Circle;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,IAAIC,QAAQ,QAAQ,aAAa;AAChD,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,EAAEC,iBAAiB,QAAQ,SAAS;AAE1D,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACtBC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACxC,IAAIC,kBAAkB,GAAGP,aAAa,CAACC,iBAAiB,CAAC;IACvDI,OAAO,EAAEA,OAAO;IAChBC,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;EACH,OAAO,CAACC,kBAAkB,EAAEP,aAAa,CAACA,aAAa,CAACI,OAAO,CAAC,GAAGG,kBAAkB,CAAC,CAAC;AACzF;AAEA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,aAAa,GAAGD,KAAK,CAACJ,OAAO;IAC7BA,OAAO,GAAGK,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;IACvDC,WAAW,GAAGF,KAAK,CAACE,WAAW;EACnC,IAAIC,YAAY,GAAGP,OAAO,CAACM,WAAW;EACtC,OAAO,CAACC,YAAY,IAAId,mBAAmB,CAACe,KAAK,EAAEF,WAAW,IAAI,IAAI,CAAC;AACzE;AAEA,IAAIf,MAAM,GAAG,SAASA,MAAMA,CAACkB,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBlB,OAAO,GAAGS,KAAK,CAACT,OAAO;EAC3B,IAAImB,UAAU,GAAGR,KAAK,IAAI,GAAG;EAC7B,IAAIS,WAAW,GAAG;IAChBT,KAAK,EAAEQ,UAAU;IACjBE,MAAM,EAAEF,UAAU;IAClBG,QAAQ,EAAEH,UAAU,GAAG,IAAI,GAAG;EAChC,CAAC;EACD,IAAII,WAAW,GAAGX,WAAW,IAAI,CAAC;EAClC,IAAIY,MAAM,GAAGT,WAAW,IAAIE,IAAI,KAAK,WAAW,IAAI,QAAQ,IAAI,KAAK;EAErE,IAAIQ,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC;IACA,IAAIT,SAAS,IAAIA,SAAS,KAAK,CAAC,EAAE;MAChC,OAAOA,SAAS;IAClB;IAEA,IAAIC,IAAI,KAAK,WAAW,EAAE;MACxB,OAAO,EAAE;IACX;IAEA,OAAOS,SAAS;EAClB,CAAC,CAAC,CAAC;;EAGH,IAAIC,UAAU,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACtB,KAAK,CAACH,WAAW,CAAC,KAAK,iBAAiB;EACxF,IAAIA,WAAW,GAAGH,cAAc,CAAC;IAC/BH,OAAO,EAAEA,OAAO;IAChBM,WAAW,EAAEG,KAAK,CAACH;EACrB,CAAC,CAAC;EACF,IAAI0B,gBAAgB,GAAGtC,UAAU,CAAC,EAAE,CAACuC,MAAM,CAACvB,SAAS,EAAE,QAAQ,CAAC,EAAErB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4C,MAAM,CAACvB,SAAS,EAAE,kBAAkB,CAAC,EAAEiB,UAAU,CAAC,CAAC;EAC5I,OAAO,aAAarC,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEH,gBAAgB;IAC3BI,KAAK,EAAEhB;EACT,CAAC,EAAE,aAAa9B,KAAK,CAAC4C,aAAa,CAAC1C,QAAQ,EAAE;IAC5CO,OAAO,EAAEF,aAAa,CAACY,KAAK,CAAC;IAC7BG,WAAW,EAAEW,WAAW;IACxBc,UAAU,EAAEd,WAAW;IACvBjB,WAAW,EAAEA,WAAW;IACxBQ,aAAa,EAAEA,aAAa;IAC5BD,UAAU,EAAEA,UAAU;IACtBH,SAAS,EAAEA,SAAS;IACpBM,SAAS,EAAES,YAAY,CAAC,CAAC;IACzBV,WAAW,EAAES;EACf,CAAC,CAAC,EAAEN,QAAQ,CAAC;AACf,CAAC;AAED,eAAe3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}