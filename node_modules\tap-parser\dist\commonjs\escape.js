"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unesc = exports.esc = void 0;
/**
 * turn \ into \\ and # into \#, for stringifying back to TAP
 */
const esc = (str) => str.replace(/\\/g, '\\\\').replace(/#/g, '\\#').trim();
exports.esc = esc;
/**
 * turn \\ into \ and \# into #, for parsing TAP into JS
 */
const unesc = (str) => str
    .replace(/(\\\\)/g, '\u0000')
    .replace(/\\#/g, '#')
    .replace(/\u0000/g, '\\')
    .trim();
exports.unesc = unesc;
//# sourceMappingURL=escape.js.map