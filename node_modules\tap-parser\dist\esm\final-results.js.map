{"version": 3, "file": "final-results.js", "sourceRoot": "", "sources": ["../../src/final-results.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAI3C;;;GAGG;AACH,MAAM,OAAO,YAAY;IACvB,EAAE,CAAS;IACX,KAAK,CAAQ;IACb,IAAI,CAAQ;IACZ,IAAI,CAAQ;IACZ,OAAO,CAAkB;IACzB,IAAI,CAAQ;IACZ,IAAI,CAAQ;IACZ,QAAQ,CAAY;IACpB,IAAI,CAAe;IACnB,MAAM,CAAW;IACjB,IAAI,CAAW;IACf,KAAK,CAAsC;IAC3C,KAAK,CAAsC;IAC3C,YAAY,OAAgB,EAAE,MAAc;QAC1C,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAA;QACnB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,IAAI,KAAK,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAA;QAC5B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAA;QACrD,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACvB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;IAC3B,CAAC;CACF", "sourcesContent": ["import { FinalPlan } from './final-plan.js'\nimport type { <PERSON><PERSON><PERSON> } from './index.js'\nimport type { Result, TapError } from './result.js'\n\n/**\n * The summary results provided in the `complete` event when the TAP\n * stream ends.\n */\nexport class FinalResults {\n  ok: boolean\n  count: number\n  pass: number\n  fail: number\n  bailout: boolean | string\n  todo: number\n  skip: number\n  failures: TapError[]\n  time: number | null\n  passes?: Result[]\n  plan: FinalPlan\n  skips: (Result & { skip: true | string })[]\n  todos: (Result & { todo: true | string })[]\n  constructor(skipAll: boolean, parser: Parser) {\n    this.ok = parser.ok\n    this.count = parser.count\n    this.pass = parser.pass\n    this.fail = parser.fail || 0\n    this.bailout = parser.bailedOut || false\n    this.todo = parser.todo || 0\n    this.skip = skipAll ? parser.count : parser.skip || 0\n    this.plan = new FinalPlan(skipAll, parser)\n    this.failures = parser.failures\n    this.passes = parser.passes || undefined\n    this.time = parser.time\n    this.skips = parser.skips\n    this.todos = parser.todos\n  }\n}\n"]}