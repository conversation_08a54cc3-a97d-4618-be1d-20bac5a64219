{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"component\", \"roles\"];\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\PrivateRoute.js\";\nimport React from 'react';\nimport { Route, Redirect } from 'react-router-dom';\nimport { isLogin } from '../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = _ref => {\n  let {\n      component: Component,\n      roles\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsxDEV(Route, _objectSpread(_objectSpread({}, rest), {}, {\n    render: props => isLogin() ? /*#__PURE__*/_jsxDEV(Component, _objectSpread({}, props), void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(Redirect, {\n      to: {\n        pathname: '/login',\n        state: {\n          from: props.location\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 19\n    }, this)\n  }), void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n_c = PrivateRoute;\nexport default PrivateRoute;\nvar _c;\n$RefreshReg$(_c, \"PrivateRoute\");", "map": {"version": 3, "names": ["React", "Route", "Redirect", "is<PERSON>ogin", "jsxDEV", "_jsxDEV", "PrivateRoute", "_ref", "component", "Component", "roles", "rest", "_objectWithoutProperties", "_excluded", "_objectSpread", "render", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "state", "from", "location", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/PrivateRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Route, Redirect } from 'react-router-dom';\nimport { isLogin } from '../utils';\n\nconst PrivateRoute = ({ component: Component, roles, ...rest }) => {\n    return (\n        <Route {...rest} render={props => (\n            isLogin() ?\n                <Component {...props} />\n                : <Redirect to={{ pathname: '/login', state: { from: props.location } }} />\n        )} />\n    );\n};\n\n\n\nexport default PrivateRoute;"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAClD,SAASC,OAAO,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,YAAY,GAAGC,IAAA,IAA8C;EAAA,IAA7C;MAAEC,SAAS,EAAEC,SAAS;MAAEC;IAAe,CAAC,GAAAH,IAAA;IAANI,IAAI,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EACxD,oBACIR,OAAA,CAACJ,KAAK,EAAAa,aAAA,CAAAA,aAAA,KAAKH,IAAI;IAAEI,MAAM,EAAEC,KAAK,IAC1Bb,OAAO,CAAC,CAAC,gBACLE,OAAA,CAACI,SAAS,EAAAK,aAAA,KAAKE,KAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBACtBf,OAAA,CAACH,QAAQ;MAACmB,EAAE,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;UAAEC,IAAI,EAAER,KAAK,CAACS;QAAS;MAAE;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAChF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAEb,CAAC;AAACM,EAAA,GARIpB,YAAY;AAYlB,eAAeA,YAAY;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}