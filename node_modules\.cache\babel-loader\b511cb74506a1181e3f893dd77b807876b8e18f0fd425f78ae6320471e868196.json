{"ast": null, "code": "export function dataToArray(vars) {\n  if (Array.isArray(vars)) {\n    return vars;\n  }\n  return [vars];\n}\nvar transitionEndObject = {\n  transition: 'transitionend',\n  WebkitTransition: 'webkitTransitionEnd',\n  MozTransition: 'transitionend',\n  OTransition: 'oTransitionEnd otransitionend'\n};\nexport var transitionStr = Object.keys(transitionEndObject).filter(function (key) {\n  if (typeof document === 'undefined') {\n    return false;\n  }\n  var html = document.getElementsByTagName('html')[0];\n  return key in (html ? html.style : {});\n})[0];\nexport var transitionEnd = transitionEndObject[transitionStr];\nexport function addEventListener(target, eventType, callback, options) {\n  if (target.addEventListener) {\n    target.addEventListener(eventType, callback, options);\n  } else if (target.attachEvent) {\n    // tslint:disable-line\n    target.attachEvent(\"on\".concat(eventType), callback); // tslint:disable-line\n  }\n}\nexport function removeEventListener(target, eventType, callback, options) {\n  if (target.removeEventListener) {\n    target.removeEventListener(eventType, callback, options);\n  } else if (target.attachEvent) {\n    // tslint:disable-line\n    target.detachEvent(\"on\".concat(eventType), callback); // tslint:disable-line\n  }\n}\nexport function transformArguments(arg, cb) {\n  var result = typeof arg === 'function' ? arg(cb) : arg;\n  if (Array.isArray(result)) {\n    if (result.length === 2) {\n      return result;\n    }\n    return [result[0], result[1]];\n  }\n  return [result];\n}\nexport var isNumeric = function isNumeric(value) {\n  return !isNaN(parseFloat(value)) && isFinite(value);\n};\nexport var windowIsUndefined = !(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport var getTouchParentScroll = function getTouchParentScroll(root, currentTarget, differX, differY) {\n  if (!currentTarget || currentTarget === document || currentTarget instanceof Document) {\n    return false;\n  } // root 为 drawer-content 设定了 overflow, 判断为 root 的 parent 时结束滚动；\n\n  if (currentTarget === root.parentNode) {\n    return true;\n  }\n  var isY = Math.max(Math.abs(differX), Math.abs(differY)) === Math.abs(differY);\n  var isX = Math.max(Math.abs(differX), Math.abs(differY)) === Math.abs(differX);\n  var scrollY = currentTarget.scrollHeight - currentTarget.clientHeight;\n  var scrollX = currentTarget.scrollWidth - currentTarget.clientWidth;\n  var style = document.defaultView.getComputedStyle(currentTarget);\n  var overflowY = style.overflowY === 'auto' || style.overflowY === 'scroll';\n  var overflowX = style.overflowX === 'auto' || style.overflowX === 'scroll';\n  var y = scrollY && overflowY;\n  var x = scrollX && overflowX;\n  if (isY && (!y || y && (currentTarget.scrollTop >= scrollY && differY < 0 || currentTarget.scrollTop <= 0 && differY > 0)) || isX && (!x || x && (currentTarget.scrollLeft >= scrollX && differX < 0 || currentTarget.scrollLeft <= 0 && differX > 0))) {\n    return getTouchParentScroll(root, currentTarget.parentNode, differX, differY);\n  }\n  return false;\n};", "map": {"version": 3, "names": ["dataToArray", "vars", "Array", "isArray", "transitionEndObject", "transition", "WebkitTransition", "MozTransition", "OTransition", "transitionStr", "Object", "keys", "filter", "key", "document", "html", "getElementsByTagName", "style", "transitionEnd", "addEventListener", "target", "eventType", "callback", "options", "attachEvent", "concat", "removeEventListener", "detachEvent", "transformArguments", "arg", "cb", "result", "length", "isNumeric", "value", "isNaN", "parseFloat", "isFinite", "windowIsUndefined", "window", "createElement", "getTouchParentScroll", "root", "currentTarget", "differX", "differY", "Document", "parentNode", "isY", "Math", "max", "abs", "isX", "scrollY", "scrollHeight", "clientHeight", "scrollX", "scrollWidth", "clientWidth", "defaultView", "getComputedStyle", "overflowY", "overflowX", "y", "x", "scrollTop", "scrollLeft"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-drawer/es/utils.js"], "sourcesContent": ["export function dataToArray(vars) {\n  if (Array.isArray(vars)) {\n    return vars;\n  }\n\n  return [vars];\n}\nvar transitionEndObject = {\n  transition: 'transitionend',\n  WebkitTransition: 'webkitTransitionEnd',\n  MozTransition: 'transitionend',\n  OTransition: 'oTransitionEnd otransitionend'\n};\nexport var transitionStr = Object.keys(transitionEndObject).filter(function (key) {\n  if (typeof document === 'undefined') {\n    return false;\n  }\n\n  var html = document.getElementsByTagName('html')[0];\n  return key in (html ? html.style : {});\n})[0];\nexport var transitionEnd = transitionEndObject[transitionStr];\nexport function addEventListener(target, eventType, callback, options) {\n  if (target.addEventListener) {\n    target.addEventListener(eventType, callback, options);\n  } else if (target.attachEvent) {\n    // tslint:disable-line\n    target.attachEvent(\"on\".concat(eventType), callback); // tslint:disable-line\n  }\n}\nexport function removeEventListener(target, eventType, callback, options) {\n  if (target.removeEventListener) {\n    target.removeEventListener(eventType, callback, options);\n  } else if (target.attachEvent) {\n    // tslint:disable-line\n    target.detachEvent(\"on\".concat(eventType), callback); // tslint:disable-line\n  }\n}\nexport function transformArguments(arg, cb) {\n  var result = typeof arg === 'function' ? arg(cb) : arg;\n\n  if (Array.isArray(result)) {\n    if (result.length === 2) {\n      return result;\n    }\n\n    return [result[0], result[1]];\n  }\n\n  return [result];\n}\nexport var isNumeric = function isNumeric(value) {\n  return !isNaN(parseFloat(value)) && isFinite(value);\n};\nexport var windowIsUndefined = !(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport var getTouchParentScroll = function getTouchParentScroll(root, currentTarget, differX, differY) {\n  if (!currentTarget || currentTarget === document || currentTarget instanceof Document) {\n    return false;\n  } // root 为 drawer-content 设定了 overflow, 判断为 root 的 parent 时结束滚动；\n\n\n  if (currentTarget === root.parentNode) {\n    return true;\n  }\n\n  var isY = Math.max(Math.abs(differX), Math.abs(differY)) === Math.abs(differY);\n  var isX = Math.max(Math.abs(differX), Math.abs(differY)) === Math.abs(differX);\n  var scrollY = currentTarget.scrollHeight - currentTarget.clientHeight;\n  var scrollX = currentTarget.scrollWidth - currentTarget.clientWidth;\n  var style = document.defaultView.getComputedStyle(currentTarget);\n  var overflowY = style.overflowY === 'auto' || style.overflowY === 'scroll';\n  var overflowX = style.overflowX === 'auto' || style.overflowX === 'scroll';\n  var y = scrollY && overflowY;\n  var x = scrollX && overflowX;\n\n  if (isY && (!y || y && (currentTarget.scrollTop >= scrollY && differY < 0 || currentTarget.scrollTop <= 0 && differY > 0)) || isX && (!x || x && (currentTarget.scrollLeft >= scrollX && differX < 0 || currentTarget.scrollLeft <= 0 && differX > 0))) {\n    return getTouchParentScroll(root, currentTarget.parentNode, differX, differY);\n  }\n\n  return false;\n};"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAACC,IAAI,EAAE;EAChC,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI;EACb;EAEA,OAAO,CAACA,IAAI,CAAC;AACf;AACA,IAAIG,mBAAmB,GAAG;EACxBC,UAAU,EAAE,eAAe;EAC3BC,gBAAgB,EAAE,qBAAqB;EACvCC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,IAAIC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACP,mBAAmB,CAAC,CAACQ,MAAM,CAAC,UAAUC,GAAG,EAAE;EAChF,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC,OAAO,KAAK;EACd;EAEA,IAAIC,IAAI,GAAGD,QAAQ,CAACE,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACnD,OAAOH,GAAG,KAAKE,IAAI,GAAGA,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,OAAO,IAAIC,aAAa,GAAGd,mBAAmB,CAACK,aAAa,CAAC;AAC7D,OAAO,SAASU,gBAAgBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrE,IAAIH,MAAM,CAACD,gBAAgB,EAAE;IAC3BC,MAAM,CAACD,gBAAgB,CAACE,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACvD,CAAC,MAAM,IAAIH,MAAM,CAACI,WAAW,EAAE;IAC7B;IACAJ,MAAM,CAACI,WAAW,CAAC,IAAI,CAACC,MAAM,CAACJ,SAAS,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;EACxD;AACF;AACA,OAAO,SAASI,mBAAmBA,CAACN,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACxE,IAAIH,MAAM,CAACM,mBAAmB,EAAE;IAC9BN,MAAM,CAACM,mBAAmB,CAACL,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EAC1D,CAAC,MAAM,IAAIH,MAAM,CAACI,WAAW,EAAE;IAC7B;IACAJ,MAAM,CAACO,WAAW,CAAC,IAAI,CAACF,MAAM,CAACJ,SAAS,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;EACxD;AACF;AACA,OAAO,SAASM,kBAAkBA,CAACC,GAAG,EAAEC,EAAE,EAAE;EAC1C,IAAIC,MAAM,GAAG,OAAOF,GAAG,KAAK,UAAU,GAAGA,GAAG,CAACC,EAAE,CAAC,GAAGD,GAAG;EAEtD,IAAI3B,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,EAAE;IACzB,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOD,MAAM;IACf;IAEA,OAAO,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B;EAEA,OAAO,CAACA,MAAM,CAAC;AACjB;AACA,OAAO,IAAIE,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EAC/C,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC,IAAIG,QAAQ,CAACH,KAAK,CAAC;AACrD,CAAC;AACD,OAAO,IAAII,iBAAiB,GAAG,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACzB,QAAQ,IAAIyB,MAAM,CAACzB,QAAQ,CAAC0B,aAAa,CAAC;AACnH,OAAO,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,IAAI,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACrG,IAAI,CAACF,aAAa,IAAIA,aAAa,KAAK7B,QAAQ,IAAI6B,aAAa,YAAYG,QAAQ,EAAE;IACrF,OAAO,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIH,aAAa,KAAKD,IAAI,CAACK,UAAU,EAAE;IACrC,OAAO,IAAI;EACb;EAEA,IAAIC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACP,OAAO,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACN,OAAO,CAAC,CAAC,KAAKI,IAAI,CAACE,GAAG,CAACN,OAAO,CAAC;EAC9E,IAAIO,GAAG,GAAGH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACP,OAAO,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACN,OAAO,CAAC,CAAC,KAAKI,IAAI,CAACE,GAAG,CAACP,OAAO,CAAC;EAC9E,IAAIS,OAAO,GAAGV,aAAa,CAACW,YAAY,GAAGX,aAAa,CAACY,YAAY;EACrE,IAAIC,OAAO,GAAGb,aAAa,CAACc,WAAW,GAAGd,aAAa,CAACe,WAAW;EACnE,IAAIzC,KAAK,GAAGH,QAAQ,CAAC6C,WAAW,CAACC,gBAAgB,CAACjB,aAAa,CAAC;EAChE,IAAIkB,SAAS,GAAG5C,KAAK,CAAC4C,SAAS,KAAK,MAAM,IAAI5C,KAAK,CAAC4C,SAAS,KAAK,QAAQ;EAC1E,IAAIC,SAAS,GAAG7C,KAAK,CAAC6C,SAAS,KAAK,MAAM,IAAI7C,KAAK,CAAC6C,SAAS,KAAK,QAAQ;EAC1E,IAAIC,CAAC,GAAGV,OAAO,IAAIQ,SAAS;EAC5B,IAAIG,CAAC,GAAGR,OAAO,IAAIM,SAAS;EAE5B,IAAId,GAAG,KAAK,CAACe,CAAC,IAAIA,CAAC,KAAKpB,aAAa,CAACsB,SAAS,IAAIZ,OAAO,IAAIR,OAAO,GAAG,CAAC,IAAIF,aAAa,CAACsB,SAAS,IAAI,CAAC,IAAIpB,OAAO,GAAG,CAAC,CAAC,CAAC,IAAIO,GAAG,KAAK,CAACY,CAAC,IAAIA,CAAC,KAAKrB,aAAa,CAACuB,UAAU,IAAIV,OAAO,IAAIZ,OAAO,GAAG,CAAC,IAAID,aAAa,CAACuB,UAAU,IAAI,CAAC,IAAItB,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE;IACtP,OAAOH,oBAAoB,CAACC,IAAI,EAAEC,aAAa,CAACI,UAAU,EAAEH,OAAO,EAAEC,OAAO,CAAC;EAC/E;EAEA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}