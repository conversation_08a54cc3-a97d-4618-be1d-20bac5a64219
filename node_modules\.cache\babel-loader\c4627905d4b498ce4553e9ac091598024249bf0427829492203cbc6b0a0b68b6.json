{"ast": null, "code": "var isNumeric = function isNumeric(value) {\n  return !isNaN(parseFloat(value)) && isFinite(value);\n};\nexport default isNumeric;", "map": {"version": 3, "names": ["isNumeric", "value", "isNaN", "parseFloat", "isFinite"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/isNumeric.js"], "sourcesContent": ["var isNumeric = function isNumeric(value) {\n  return !isNaN(parseFloat(value)) && isFinite(value);\n};\n\nexport default isNumeric;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC,IAAIG,QAAQ,CAACH,KAAK,CAAC;AACrD,CAAC;AAED,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}