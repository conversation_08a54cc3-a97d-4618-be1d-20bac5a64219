{"ast": null, "code": "var initialState = {\n  animating: false,\n  autoplaying: null,\n  currentDirection: 0,\n  currentLeft: null,\n  currentSlide: 0,\n  direction: 1,\n  dragging: false,\n  edgeDragged: false,\n  initialized: false,\n  lazyLoadedList: [],\n  listHeight: null,\n  listWidth: null,\n  scrolling: false,\n  slideCount: null,\n  slideHeight: null,\n  slideWidth: null,\n  swipeLeft: null,\n  swiped: false,\n  // used by swipeEvent. differentites between touch and swipe.\n  swiping: false,\n  touchObject: {\n    startX: 0,\n    startY: 0,\n    curX: 0,\n    curY: 0\n  },\n  trackStyle: {},\n  trackWidth: 0,\n  targetSlide: 0\n};\nexport default initialState;", "map": {"version": 3, "names": ["initialState", "animating", "autoplaying", "currentDirection", "currentLeft", "currentSlide", "direction", "dragging", "edgeDragged", "initialized", "lazyLoadedList", "listHeight", "listWidth", "scrolling", "slideCount", "slideHeight", "slideWidth", "swipeLeft", "swiped", "swiping", "touchObject", "startX", "startY", "curX", "curY", "trackStyle", "trackWidth", "targetSlide"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/react-slick/es/initial-state.js"], "sourcesContent": ["var initialState = {\n  animating: false,\n  autoplaying: null,\n  currentDirection: 0,\n  currentLeft: null,\n  currentSlide: 0,\n  direction: 1,\n  dragging: false,\n  edgeDragged: false,\n  initialized: false,\n  lazyLoadedList: [],\n  listHeight: null,\n  listWidth: null,\n  scrolling: false,\n  slideCount: null,\n  slideHeight: null,\n  slideWidth: null,\n  swipeLeft: null,\n  swiped: false,\n  // used by swipeEvent. differentites between touch and swipe.\n  swiping: false,\n  touchObject: {\n    startX: 0,\n    startY: 0,\n    curX: 0,\n    curY: 0\n  },\n  trackStyle: {},\n  trackWidth: 0,\n  targetSlide: 0\n};\nexport default initialState;"], "mappings": "AAAA,IAAIA,YAAY,GAAG;EACjBC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,CAAC;EACnBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,KAAK;EACfC,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE,KAAK;EAClBC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,KAAK;EACb;EACAC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;IACXC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE;EACR,CAAC;EACDC,UAAU,EAAE,CAAC,CAAC;EACdC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC;AACD,eAAe3B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}