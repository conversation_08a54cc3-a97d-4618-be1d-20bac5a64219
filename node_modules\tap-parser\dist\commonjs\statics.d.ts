import { ParserOptions } from './index.js';
/**
 * Parse a TAP text stream into a log of all the events encountered
 */
export declare const parse: (str: string, options: ParserOptions) => [event: string, ...data: any[]][];
/**
 * Turn an EventLog from {@link tap-parser!statics.parse} into a TAP string
 */
export declare const stringify: (msg: [event: string, ...data: any[]][], { flat, indent, id }: {
    flat?: boolean | undefined;
    indent?: string | undefined;
    id?: {
        (): number;
        current: number;
    } | undefined;
}) => string;
//# sourceMappingURL=statics.d.ts.map