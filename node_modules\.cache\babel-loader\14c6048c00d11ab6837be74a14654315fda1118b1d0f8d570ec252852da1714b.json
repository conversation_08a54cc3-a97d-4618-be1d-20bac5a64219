{"ast": null, "code": "var asciiSize = require('./_asciiSize'),\n  hasUnicode = require('./_hasUnicode'),\n  unicodeSize = require('./_unicodeSize');\n\n/**\n * Gets the number of symbols in `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the string size.\n */\nfunction stringSize(string) {\n  return hasUnicode(string) ? unicodeSize(string) : asciiSize(string);\n}\nmodule.exports = stringSize;", "map": {"version": 3, "names": ["asciiSize", "require", "hasUnicode", "unicodeSize", "stringSize", "string", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/lodash/_stringSize.js"], "sourcesContent": ["var asciiSize = require('./_asciiSize'),\n    hasUnicode = require('./_hasUnicode'),\n    unicodeSize = require('./_unicodeSize');\n\n/**\n * Gets the number of symbols in `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the string size.\n */\nfunction stringSize(string) {\n  return hasUnicode(string)\n    ? unicodeSize(string)\n    : asciiSize(string);\n}\n\nmodule.exports = stringSize;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOH,UAAU,CAACG,MAAM,CAAC,GACrBF,WAAW,CAACE,MAAM,CAAC,GACnBL,SAAS,CAACK,MAAM,CAAC;AACvB;AAEAC,MAAM,CAACC,OAAO,GAAGH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}