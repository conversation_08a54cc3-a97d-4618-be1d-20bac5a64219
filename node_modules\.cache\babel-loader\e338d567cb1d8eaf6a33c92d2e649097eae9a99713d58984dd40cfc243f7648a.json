{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CascaderContext from '../context';\nexport default function Checkbox(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    checked = _ref.checked,\n    halfChecked = _ref.halfChecked,\n    disabled = _ref.disabled,\n    onClick = _ref.onClick;\n  var _React$useContext = React.useContext(CascaderContext),\n    checkable = _React$useContext.checkable;\n  var customCheckbox = typeof checkable !== 'boolean' ? checkable : null;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(prefixCls), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-indeterminate\"), !checked && halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames)),\n    onClick: onClick\n  }, customCheckbox);\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "CascaderContext", "Checkbox", "_ref", "_classNames", "prefixCls", "checked", "halfChecked", "disabled", "onClick", "_React$useContext", "useContext", "checkable", "customCheckbox", "createElement", "className", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-cascader/es/OptionList/Checkbox.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CascaderContext from '../context';\nexport default function Checkbox(_ref) {\n  var _classNames;\n\n  var prefixCls = _ref.prefixCls,\n      checked = _ref.checked,\n      halfChecked = _ref.halfChecked,\n      disabled = _ref.disabled,\n      onClick = _ref.onClick;\n\n  var _React$useContext = React.useContext(CascaderContext),\n      checkable = _React$useContext.checkable;\n\n  var customCheckbox = typeof checkable !== 'boolean' ? checkable : null;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(prefixCls), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-indeterminate\"), !checked && halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames)),\n    onClick: onClick\n  }, customCheckbox);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,YAAY;AACxC,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,WAAW;EAEf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,OAAO,GAAGN,IAAI,CAACM,OAAO;EAE1B,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACV,eAAe,CAAC;IACrDW,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EAE3C,IAAIC,cAAc,GAAG,OAAOD,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,IAAI;EACtE,OAAO,aAAab,KAAK,CAACe,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEf,UAAU,CAAC,EAAE,CAACgB,MAAM,CAACX,SAAS,CAAC,GAAGD,WAAW,GAAG,CAAC,CAAC,EAAEN,eAAe,CAACM,WAAW,EAAE,EAAE,CAACY,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,EAAEC,OAAO,CAAC,EAAER,eAAe,CAACM,WAAW,EAAE,EAAE,CAACY,MAAM,CAACX,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAACC,OAAO,IAAIC,WAAW,CAAC,EAAET,eAAe,CAACM,WAAW,EAAE,EAAE,CAACY,MAAM,CAACX,SAAS,EAAE,WAAW,CAAC,EAAEG,QAAQ,CAAC,EAAEJ,WAAW,CAAC,CAAC;IAC/TK,OAAO,EAAEA;EACX,CAAC,EAAEI,cAAc,CAAC;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}