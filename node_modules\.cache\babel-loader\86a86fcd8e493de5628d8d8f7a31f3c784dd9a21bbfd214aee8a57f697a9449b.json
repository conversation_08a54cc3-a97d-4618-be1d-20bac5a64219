{"ast": null, "code": "import React, { Component } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ObjectUtils, classNames, CSSTransition, Portal } from 'primereact/core';\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n  return _assertThisInitialized(self);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar Sidebar = /*#__PURE__*/function (_Component) {\n  _inherits(Sidebar, _Component);\n  var _super = _createSuper(Sidebar);\n  function Sidebar(props) {\n    var _this;\n    _classCallCheck(this, Sidebar);\n    _this = _super.call(this, props);\n    _this.onCloseClick = _this.onCloseClick.bind(_assertThisInitialized(_this));\n    _this.onEnter = _this.onEnter.bind(_assertThisInitialized(_this));\n    _this.onEntered = _this.onEntered.bind(_assertThisInitialized(_this));\n    _this.onExit = _this.onExit.bind(_assertThisInitialized(_this));\n    _this.onExited = _this.onExited.bind(_assertThisInitialized(_this));\n    _this.sidebarRef = /*#__PURE__*/React.createRef();\n    return _this;\n  }\n  _createClass(Sidebar, [{\n    key: \"onCloseClick\",\n    value: function onCloseClick(event) {\n      this.props.onHide();\n      event.preventDefault();\n    }\n  }, {\n    key: \"onEnter\",\n    value: function onEnter() {\n      ZIndexUtils.set('modal', this.sidebarRef.current, this.props.baseZIndex);\n      if (this.props.modal) {\n        this.enableModality();\n      }\n    }\n  }, {\n    key: \"onEntered\",\n    value: function onEntered() {\n      if (this.props.closeOnEscape) {\n        this.bindDocumentEscapeListener();\n      }\n      if (this.closeIcon) {\n        this.closeIcon.focus();\n      }\n      this.props.onShow && this.props.onShow();\n    }\n  }, {\n    key: \"onExit\",\n    value: function onExit() {\n      this.unbindMaskClickListener();\n      this.unbindDocumentEscapeListener();\n      if (this.props.modal) {\n        this.disableModality();\n      }\n    }\n  }, {\n    key: \"onExited\",\n    value: function onExited() {\n      ZIndexUtils.clear(this.sidebarRef.current);\n    }\n  }, {\n    key: \"enableModality\",\n    value: function enableModality() {\n      if (!this.mask) {\n        this.mask = document.createElement('div');\n        this.mask.style.zIndex = String(ZIndexUtils.get(this.sidebarRef.current) - 1);\n        var maskClassName = 'p-component-overlay p-component-overlay p-component-overlay-enter';\n        if (this.props.blockScroll) {\n          maskClassName += ' p-sidebar-mask-scrollblocker';\n        }\n        DomHandler.addMultipleClasses(this.mask, maskClassName);\n        if (this.props.dismissable) {\n          this.bindMaskClickListener();\n        }\n        document.body.appendChild(this.mask);\n        if (this.props.blockScroll) {\n          DomHandler.addClass(document.body, 'p-overflow-hidden');\n        }\n      }\n    }\n  }, {\n    key: \"disableModality\",\n    value: function disableModality() {\n      var _this2 = this;\n      if (this.mask) {\n        DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n        this.mask.addEventListener('animationend', function () {\n          _this2.destroyModal();\n        });\n      }\n    }\n  }, {\n    key: \"destroyModal\",\n    value: function destroyModal() {\n      if (this.mask) {\n        this.unbindMaskClickListener();\n        document.body.removeChild(this.mask);\n        DomHandler.removeClass(document.body, 'p-overflow-hidden');\n        this.mask = null;\n      }\n    }\n  }, {\n    key: \"bindDocumentEscapeListener\",\n    value: function bindDocumentEscapeListener() {\n      var _this3 = this;\n      this.documentEscapeListener = function (event) {\n        if (event.which === 27) {\n          if (ZIndexUtils.get(_this3.sidebarRef.current) === ZIndexUtils.getCurrent('modal')) {\n            _this3.onCloseClick(event);\n          }\n        }\n      };\n      document.addEventListener('keydown', this.documentEscapeListener);\n    }\n  }, {\n    key: \"unbindDocumentEscapeListener\",\n    value: function unbindDocumentEscapeListener() {\n      if (this.documentEscapeListener) {\n        document.removeEventListener('keydown', this.documentEscapeListener);\n        this.documentEscapeListener = null;\n      }\n    }\n  }, {\n    key: \"bindMaskClickListener\",\n    value: function bindMaskClickListener() {\n      var _this4 = this;\n      if (!this.maskClickListener) {\n        this.maskClickListener = function (event) {\n          _this4.onCloseClick(event);\n        };\n        this.mask.addEventListener('click', this.maskClickListener);\n      }\n    }\n  }, {\n    key: \"unbindMaskClickListener\",\n    value: function unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.mask.removeEventListener('click', this.maskClickListener);\n        this.maskClickListener = null;\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (this.mask && prevProps.dismissable !== this.props.dismissable) {\n        if (this.props.dismissable) {\n          this.bindMaskClickListener();\n        } else {\n          this.unbindMaskClickListener();\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unbindMaskClickListener();\n      this.disableModality();\n      ZIndexUtils.clear(this.sidebarRef.current);\n    }\n  }, {\n    key: \"renderCloseIcon\",\n    value: function renderCloseIcon() {\n      var _this5 = this;\n      if (this.props.showCloseIcon) {\n        return /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          ref: function ref(el) {\n            return _this5.closeIcon = el;\n          },\n          className: \"p-sidebar-close p-sidebar-icon p-link\",\n          onClick: this.onCloseClick,\n          \"aria-label\": this.props.ariaCloseLabel\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: \"p-sidebar-close-icon pi pi-times\"\n        }), /*#__PURE__*/React.createElement(Ripple, null));\n      }\n      return null;\n    }\n  }, {\n    key: \"renderIcons\",\n    value: function renderIcons() {\n      if (this.props.icons) {\n        return ObjectUtils.getJSXElement(this.props.icons, this.props);\n      }\n      return null;\n    }\n  }, {\n    key: \"renderElement\",\n    value: function renderElement() {\n      var className = classNames('p-sidebar p-component', this.props.className, 'p-sidebar-' + this.props.position, {\n        'p-sidebar-active': this.props.visible,\n        'p-sidebar-full': this.props.fullScreen\n      });\n      var closeIcon = this.renderCloseIcon();\n      var icons = this.renderIcons();\n      var transitionTimeout = {\n        enter: this.props.fullScreen ? 400 : 300,\n        exit: this.props.fullScreen ? 400 : 300\n      };\n      return /*#__PURE__*/React.createElement(CSSTransition, {\n        nodeRef: this.sidebarRef,\n        classNames: \"p-sidebar\",\n        in: this.props.visible,\n        timeout: transitionTimeout,\n        options: this.props.transitionOptions,\n        unmountOnExit: true,\n        onEnter: this.onEnter,\n        onEntered: this.onEntered,\n        onExit: this.onExit,\n        onExited: this.onExited\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.sidebarRef,\n        id: this.props.id,\n        className: className,\n        style: this.props.style,\n        role: \"complementary\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-sidebar-header\"\n      }, icons, closeIcon), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-sidebar-content\"\n      }, this.props.children)));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var element = this.renderElement();\n      return /*#__PURE__*/React.createElement(Portal, {\n        element: element,\n        appendTo: this.props.appendTo\n      });\n    }\n  }]);\n  return Sidebar;\n}(Component);\n_defineProperty(Sidebar, \"defaultProps\", {\n  id: null,\n  style: null,\n  className: null,\n  visible: false,\n  position: 'left',\n  fullScreen: false,\n  blockScroll: false,\n  baseZIndex: 0,\n  dismissable: true,\n  showCloseIcon: true,\n  ariaCloseLabel: 'close',\n  closeOnEscape: true,\n  icons: null,\n  modal: true,\n  appendTo: null,\n  transitionOptions: null,\n  onShow: null,\n  onHide: null\n});\nexport { Sidebar };", "map": {"version": 3, "names": ["React", "Component", "ZIndexUtils", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ObjectUtils", "classNames", "CSSTransition", "Portal", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "constructor", "value", "_typeof", "obj", "Symbol", "iterator", "_possibleConstructorReturn", "call", "_getPrototypeOf", "getPrototypeOf", "_defineProperty", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "arguments", "apply", "sham", "Proxy", "Boolean", "valueOf", "e", "Sidebar", "_Component", "_super", "_this", "onCloseClick", "bind", "onEnter", "onEntered", "onExit", "onExited", "sidebarRef", "createRef", "event", "onHide", "preventDefault", "set", "current", "baseZIndex", "modal", "enableModality", "closeOnEscape", "bindDocumentEscapeListener", "closeIcon", "focus", "onShow", "unbindMaskClickListener", "unbindDocumentEscapeListener", "disableModality", "clear", "mask", "document", "createElement", "style", "zIndex", "String", "get", "maskClassName", "blockScroll", "addMultipleClasses", "dismissable", "bindMaskClickListener", "body", "append<PERSON><PERSON><PERSON>", "addClass", "_this2", "addEventListener", "destroyModal", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "_this3", "documentEscapeListener", "which", "get<PERSON>urrent", "removeEventListener", "_this4", "maskClickListener", "componentDidUpdate", "prevProps", "prevState", "componentWillUnmount", "renderCloseIcon", "_this5", "showCloseIcon", "type", "ref", "el", "className", "onClick", "ariaCloseLabel", "renderIcons", "icons", "getJSXElement", "renderElement", "position", "visible", "fullScreen", "transitionTimeout", "enter", "exit", "nodeRef", "in", "timeout", "options", "transitionOptions", "unmountOnExit", "id", "role", "children", "render", "element", "appendTo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/primereact/sidebar/sidebar.esm.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ObjectUtils, classNames, CSSTransition, Portal } from 'primereact/core';\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nvar Sidebar = /*#__PURE__*/function (_Component) {\n  _inherits(Sidebar, _Component);\n\n  var _super = _createSuper(Sidebar);\n\n  function Sidebar(props) {\n    var _this;\n\n    _classCallCheck(this, Sidebar);\n\n    _this = _super.call(this, props);\n    _this.onCloseClick = _this.onCloseClick.bind(_assertThisInitialized(_this));\n    _this.onEnter = _this.onEnter.bind(_assertThisInitialized(_this));\n    _this.onEntered = _this.onEntered.bind(_assertThisInitialized(_this));\n    _this.onExit = _this.onExit.bind(_assertThisInitialized(_this));\n    _this.onExited = _this.onExited.bind(_assertThisInitialized(_this));\n    _this.sidebarRef = /*#__PURE__*/React.createRef();\n    return _this;\n  }\n\n  _createClass(Sidebar, [{\n    key: \"onCloseClick\",\n    value: function onCloseClick(event) {\n      this.props.onHide();\n      event.preventDefault();\n    }\n  }, {\n    key: \"onEnter\",\n    value: function onEnter() {\n      ZIndexUtils.set('modal', this.sidebarRef.current, this.props.baseZIndex);\n\n      if (this.props.modal) {\n        this.enableModality();\n      }\n    }\n  }, {\n    key: \"onEntered\",\n    value: function onEntered() {\n      if (this.props.closeOnEscape) {\n        this.bindDocumentEscapeListener();\n      }\n\n      if (this.closeIcon) {\n        this.closeIcon.focus();\n      }\n\n      this.props.onShow && this.props.onShow();\n    }\n  }, {\n    key: \"onExit\",\n    value: function onExit() {\n      this.unbindMaskClickListener();\n      this.unbindDocumentEscapeListener();\n\n      if (this.props.modal) {\n        this.disableModality();\n      }\n    }\n  }, {\n    key: \"onExited\",\n    value: function onExited() {\n      ZIndexUtils.clear(this.sidebarRef.current);\n    }\n  }, {\n    key: \"enableModality\",\n    value: function enableModality() {\n      if (!this.mask) {\n        this.mask = document.createElement('div');\n        this.mask.style.zIndex = String(ZIndexUtils.get(this.sidebarRef.current) - 1);\n        var maskClassName = 'p-component-overlay p-component-overlay p-component-overlay-enter';\n\n        if (this.props.blockScroll) {\n          maskClassName += ' p-sidebar-mask-scrollblocker';\n        }\n\n        DomHandler.addMultipleClasses(this.mask, maskClassName);\n\n        if (this.props.dismissable) {\n          this.bindMaskClickListener();\n        }\n\n        document.body.appendChild(this.mask);\n\n        if (this.props.blockScroll) {\n          DomHandler.addClass(document.body, 'p-overflow-hidden');\n        }\n      }\n    }\n  }, {\n    key: \"disableModality\",\n    value: function disableModality() {\n      var _this2 = this;\n\n      if (this.mask) {\n        DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n        this.mask.addEventListener('animationend', function () {\n          _this2.destroyModal();\n        });\n      }\n    }\n  }, {\n    key: \"destroyModal\",\n    value: function destroyModal() {\n      if (this.mask) {\n        this.unbindMaskClickListener();\n        document.body.removeChild(this.mask);\n        DomHandler.removeClass(document.body, 'p-overflow-hidden');\n        this.mask = null;\n      }\n    }\n  }, {\n    key: \"bindDocumentEscapeListener\",\n    value: function bindDocumentEscapeListener() {\n      var _this3 = this;\n\n      this.documentEscapeListener = function (event) {\n        if (event.which === 27) {\n          if (ZIndexUtils.get(_this3.sidebarRef.current) === ZIndexUtils.getCurrent('modal')) {\n            _this3.onCloseClick(event);\n          }\n        }\n      };\n\n      document.addEventListener('keydown', this.documentEscapeListener);\n    }\n  }, {\n    key: \"unbindDocumentEscapeListener\",\n    value: function unbindDocumentEscapeListener() {\n      if (this.documentEscapeListener) {\n        document.removeEventListener('keydown', this.documentEscapeListener);\n        this.documentEscapeListener = null;\n      }\n    }\n  }, {\n    key: \"bindMaskClickListener\",\n    value: function bindMaskClickListener() {\n      var _this4 = this;\n\n      if (!this.maskClickListener) {\n        this.maskClickListener = function (event) {\n          _this4.onCloseClick(event);\n        };\n\n        this.mask.addEventListener('click', this.maskClickListener);\n      }\n    }\n  }, {\n    key: \"unbindMaskClickListener\",\n    value: function unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.mask.removeEventListener('click', this.maskClickListener);\n        this.maskClickListener = null;\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (this.mask && prevProps.dismissable !== this.props.dismissable) {\n        if (this.props.dismissable) {\n          this.bindMaskClickListener();\n        } else {\n          this.unbindMaskClickListener();\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unbindMaskClickListener();\n      this.disableModality();\n      ZIndexUtils.clear(this.sidebarRef.current);\n    }\n  }, {\n    key: \"renderCloseIcon\",\n    value: function renderCloseIcon() {\n      var _this5 = this;\n\n      if (this.props.showCloseIcon) {\n        return /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          ref: function ref(el) {\n            return _this5.closeIcon = el;\n          },\n          className: \"p-sidebar-close p-sidebar-icon p-link\",\n          onClick: this.onCloseClick,\n          \"aria-label\": this.props.ariaCloseLabel\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: \"p-sidebar-close-icon pi pi-times\"\n        }), /*#__PURE__*/React.createElement(Ripple, null));\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderIcons\",\n    value: function renderIcons() {\n      if (this.props.icons) {\n        return ObjectUtils.getJSXElement(this.props.icons, this.props);\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderElement\",\n    value: function renderElement() {\n      var className = classNames('p-sidebar p-component', this.props.className, 'p-sidebar-' + this.props.position, {\n        'p-sidebar-active': this.props.visible,\n        'p-sidebar-full': this.props.fullScreen\n      });\n      var closeIcon = this.renderCloseIcon();\n      var icons = this.renderIcons();\n      var transitionTimeout = {\n        enter: this.props.fullScreen ? 400 : 300,\n        exit: this.props.fullScreen ? 400 : 300\n      };\n      return /*#__PURE__*/React.createElement(CSSTransition, {\n        nodeRef: this.sidebarRef,\n        classNames: \"p-sidebar\",\n        in: this.props.visible,\n        timeout: transitionTimeout,\n        options: this.props.transitionOptions,\n        unmountOnExit: true,\n        onEnter: this.onEnter,\n        onEntered: this.onEntered,\n        onExit: this.onExit,\n        onExited: this.onExited\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.sidebarRef,\n        id: this.props.id,\n        className: className,\n        style: this.props.style,\n        role: \"complementary\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-sidebar-header\"\n      }, icons, closeIcon), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-sidebar-content\"\n      }, this.props.children)));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var element = this.renderElement();\n      return /*#__PURE__*/React.createElement(Portal, {\n        element: element,\n        appendTo: this.props.appendTo\n      });\n    }\n  }]);\n\n  return Sidebar;\n}(Component);\n\n_defineProperty(Sidebar, \"defaultProps\", {\n  id: null,\n  style: null,\n  className: null,\n  visible: false,\n  position: 'left',\n  fullScreen: false,\n  blockScroll: false,\n  baseZIndex: 0,\n  dismissable: true,\n  showCloseIcon: true,\n  ariaCloseLabel: 'close',\n  closeOnEscape: true,\n  icons: null,\n  modal: true,\n  appendTo: null,\n  transitionOptions: null,\n  onShow: null,\n  onHide: null\n});\n\nexport { Sidebar };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AAEjH,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAC3D;AACF;AAEA,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAC5D,OAAOhB,WAAW;AACpB;AAEA,SAASkB,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7BF,eAAe,GAAGV,MAAM,CAACa,cAAc,IAAI,SAASH,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACxED,CAAC,CAACG,SAAS,GAAGF,CAAC;IACf,OAAOD,CAAC;EACV,CAAC;EAED,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B;AAEA,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAI3B,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEA0B,QAAQ,CAACV,SAAS,GAAGN,MAAM,CAACkB,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACX,SAAS,EAAE;IACrEa,WAAW,EAAE;MACXC,KAAK,EAAEJ,QAAQ;MACfjB,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAImB,UAAU,EAAEP,eAAe,CAACM,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASI,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACH,WAAW,KAAKI,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACjB,SAAS,GAAG,QAAQ,GAAG,OAAOgB,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASG,0BAA0BA,CAACjB,IAAI,EAAEkB,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAKL,OAAO,CAACK,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACtE,OAAOA,IAAI;EACb;EAEA,OAAOnB,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASmB,eAAeA,CAAChB,CAAC,EAAE;EAC1BgB,eAAe,GAAG3B,MAAM,CAACa,cAAc,GAAGb,MAAM,CAAC4B,cAAc,GAAG,SAASD,eAAeA,CAAChB,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAId,MAAM,CAAC4B,cAAc,CAACjB,CAAC,CAAC;EAChD,CAAC;EACD,OAAOgB,eAAe,CAAChB,CAAC,CAAC;AAC3B;AAEA,SAASkB,eAAeA,CAACP,GAAG,EAAEpB,GAAG,EAAEkB,KAAK,EAAE;EACxC,IAAIlB,GAAG,IAAIoB,GAAG,EAAE;IACdtB,MAAM,CAACC,cAAc,CAACqB,GAAG,EAAEpB,GAAG,EAAE;MAC9BkB,KAAK,EAAEA,KAAK;MACZvB,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLuB,GAAG,CAACpB,GAAG,CAAC,GAAGkB,KAAK;EAClB;EAEA,OAAOE,GAAG;AACZ;AAEA,SAASQ,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGR,eAAe,CAACI,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAGV,eAAe,CAAC,IAAI,CAAC,CAACR,WAAW;MAAEiB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEK,SAAS,EAAEH,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAACM,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IAAE;IAAE,OAAOf,0BAA0B,CAAC,IAAI,EAAEW,MAAM,CAAC;EAAE,CAAC;AAAE;AAExa,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOK,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACG,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACtC,SAAS,CAACuC,OAAO,CAACnB,IAAI,CAACY,OAAO,CAACC,SAAS,CAACK,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,IAAIC,OAAO,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC/CjC,SAAS,CAACgC,OAAO,EAAEC,UAAU,CAAC;EAE9B,IAAIC,MAAM,GAAGnB,YAAY,CAACiB,OAAO,CAAC;EAElC,SAASA,OAAOA,CAACtD,KAAK,EAAE;IACtB,IAAIyD,KAAK;IAET/D,eAAe,CAAC,IAAI,EAAE4D,OAAO,CAAC;IAE9BG,KAAK,GAAGD,MAAM,CAACvB,IAAI,CAAC,IAAI,EAAEjC,KAAK,CAAC;IAChCyD,KAAK,CAACC,YAAY,GAAGD,KAAK,CAACC,YAAY,CAACC,IAAI,CAAC7C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IAC3EA,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAACD,IAAI,CAAC7C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IACjEA,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACI,SAAS,CAACF,IAAI,CAAC7C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IACrEA,KAAK,CAACK,MAAM,GAAGL,KAAK,CAACK,MAAM,CAACH,IAAI,CAAC7C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IAC/DA,KAAK,CAACM,QAAQ,GAAGN,KAAK,CAACM,QAAQ,CAACJ,IAAI,CAAC7C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IACnEA,KAAK,CAACO,UAAU,GAAG,aAAa/E,KAAK,CAACgF,SAAS,CAAC,CAAC;IACjD,OAAOR,KAAK;EACd;EAEA/C,YAAY,CAAC4C,OAAO,EAAE,CAAC;IACrB7C,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAAS+B,YAAYA,CAACQ,KAAK,EAAE;MAClC,IAAI,CAAClE,KAAK,CAACmE,MAAM,CAAC,CAAC;MACnBD,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD3D,GAAG,EAAE,SAAS;IACdkB,KAAK,EAAE,SAASiC,OAAOA,CAAA,EAAG;MACxBzE,WAAW,CAACkF,GAAG,CAAC,OAAO,EAAE,IAAI,CAACL,UAAU,CAACM,OAAO,EAAE,IAAI,CAACtE,KAAK,CAACuE,UAAU,CAAC;MAExE,IAAI,IAAI,CAACvE,KAAK,CAACwE,KAAK,EAAE;QACpB,IAAI,CAACC,cAAc,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,WAAW;IAChBkB,KAAK,EAAE,SAASkC,SAASA,CAAA,EAAG;MAC1B,IAAI,IAAI,CAAC7D,KAAK,CAAC0E,aAAa,EAAE;QAC5B,IAAI,CAACC,0BAA0B,CAAC,CAAC;MACnC;MAEA,IAAI,IAAI,CAACC,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAC,CAAC;MACxB;MAEA,IAAI,CAAC7E,KAAK,CAAC8E,MAAM,IAAI,IAAI,CAAC9E,KAAK,CAAC8E,MAAM,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAASmC,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACiB,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACC,4BAA4B,CAAC,CAAC;MAEnC,IAAI,IAAI,CAAChF,KAAK,CAACwE,KAAK,EAAE;QACpB,IAAI,CAACS,eAAe,CAAC,CAAC;MACxB;IACF;EACF,CAAC,EAAE;IACDxE,GAAG,EAAE,UAAU;IACfkB,KAAK,EAAE,SAASoC,QAAQA,CAAA,EAAG;MACzB5E,WAAW,CAAC+F,KAAK,CAAC,IAAI,CAAClB,UAAU,CAACM,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE;IACD7D,GAAG,EAAE,gBAAgB;IACrBkB,KAAK,EAAE,SAAS8C,cAAcA,CAAA,EAAG;MAC/B,IAAI,CAAC,IAAI,CAACU,IAAI,EAAE;QACd,IAAI,CAACA,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACzC,IAAI,CAACF,IAAI,CAACG,KAAK,CAACC,MAAM,GAAGC,MAAM,CAACrG,WAAW,CAACsG,GAAG,CAAC,IAAI,CAACzB,UAAU,CAACM,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7E,IAAIoB,aAAa,GAAG,mEAAmE;QAEvF,IAAI,IAAI,CAAC1F,KAAK,CAAC2F,WAAW,EAAE;UAC1BD,aAAa,IAAI,+BAA+B;QAClD;QAEAtG,UAAU,CAACwG,kBAAkB,CAAC,IAAI,CAACT,IAAI,EAAEO,aAAa,CAAC;QAEvD,IAAI,IAAI,CAAC1F,KAAK,CAAC6F,WAAW,EAAE;UAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC9B;QAEAV,QAAQ,CAACW,IAAI,CAACC,WAAW,CAAC,IAAI,CAACb,IAAI,CAAC;QAEpC,IAAI,IAAI,CAACnF,KAAK,CAAC2F,WAAW,EAAE;UAC1BvG,UAAU,CAAC6G,QAAQ,CAACb,QAAQ,CAACW,IAAI,EAAE,mBAAmB,CAAC;QACzD;MACF;IACF;EACF,CAAC,EAAE;IACDtF,GAAG,EAAE,iBAAiB;IACtBkB,KAAK,EAAE,SAASsD,eAAeA,CAAA,EAAG;MAChC,IAAIiB,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACf,IAAI,EAAE;QACb/F,UAAU,CAAC6G,QAAQ,CAAC,IAAI,CAACd,IAAI,EAAE,2BAA2B,CAAC;QAC3D,IAAI,CAACA,IAAI,CAACgB,gBAAgB,CAAC,cAAc,EAAE,YAAY;UACrDD,MAAM,CAACE,YAAY,CAAC,CAAC;QACvB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACD3F,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAASyE,YAAYA,CAAA,EAAG;MAC7B,IAAI,IAAI,CAACjB,IAAI,EAAE;QACb,IAAI,CAACJ,uBAAuB,CAAC,CAAC;QAC9BK,QAAQ,CAACW,IAAI,CAACM,WAAW,CAAC,IAAI,CAAClB,IAAI,CAAC;QACpC/F,UAAU,CAACkH,WAAW,CAAClB,QAAQ,CAACW,IAAI,EAAE,mBAAmB,CAAC;QAC1D,IAAI,CAACZ,IAAI,GAAG,IAAI;MAClB;IACF;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,4BAA4B;IACjCkB,KAAK,EAAE,SAASgD,0BAA0BA,CAAA,EAAG;MAC3C,IAAI4B,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACC,sBAAsB,GAAG,UAAUtC,KAAK,EAAE;QAC7C,IAAIA,KAAK,CAACuC,KAAK,KAAK,EAAE,EAAE;UACtB,IAAItH,WAAW,CAACsG,GAAG,CAACc,MAAM,CAACvC,UAAU,CAACM,OAAO,CAAC,KAAKnF,WAAW,CAACuH,UAAU,CAAC,OAAO,CAAC,EAAE;YAClFH,MAAM,CAAC7C,YAAY,CAACQ,KAAK,CAAC;UAC5B;QACF;MACF,CAAC;MAEDkB,QAAQ,CAACe,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACK,sBAAsB,CAAC;IACnE;EACF,CAAC,EAAE;IACD/F,GAAG,EAAE,8BAA8B;IACnCkB,KAAK,EAAE,SAASqD,4BAA4BA,CAAA,EAAG;MAC7C,IAAI,IAAI,CAACwB,sBAAsB,EAAE;QAC/BpB,QAAQ,CAACuB,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACH,sBAAsB,CAAC;QACpE,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACpC;IACF;EACF,CAAC,EAAE;IACD/F,GAAG,EAAE,uBAAuB;IAC5BkB,KAAK,EAAE,SAASmE,qBAAqBA,CAAA,EAAG;MACtC,IAAIc,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;QAC3B,IAAI,CAACA,iBAAiB,GAAG,UAAU3C,KAAK,EAAE;UACxC0C,MAAM,CAAClD,YAAY,CAACQ,KAAK,CAAC;QAC5B,CAAC;QAED,IAAI,CAACiB,IAAI,CAACgB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACU,iBAAiB,CAAC;MAC7D;IACF;EACF,CAAC,EAAE;IACDpG,GAAG,EAAE,yBAAyB;IAC9BkB,KAAK,EAAE,SAASoD,uBAAuBA,CAAA,EAAG;MACxC,IAAI,IAAI,CAAC8B,iBAAiB,EAAE;QAC1B,IAAI,CAAC1B,IAAI,CAACwB,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACE,iBAAiB,CAAC;QAC9D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC/B;IACF;EACF,CAAC,EAAE;IACDpG,GAAG,EAAE,oBAAoB;IACzBkB,KAAK,EAAE,SAASmF,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MACvD,IAAI,IAAI,CAAC7B,IAAI,IAAI4B,SAAS,CAAClB,WAAW,KAAK,IAAI,CAAC7F,KAAK,CAAC6F,WAAW,EAAE;QACjE,IAAI,IAAI,CAAC7F,KAAK,CAAC6F,WAAW,EAAE;UAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL,IAAI,CAACf,uBAAuB,CAAC,CAAC;QAChC;MACF;IACF;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,sBAAsB;IAC3BkB,KAAK,EAAE,SAASsF,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAClC,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACE,eAAe,CAAC,CAAC;MACtB9F,WAAW,CAAC+F,KAAK,CAAC,IAAI,CAAClB,UAAU,CAACM,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE;IACD7D,GAAG,EAAE,iBAAiB;IACtBkB,KAAK,EAAE,SAASuF,eAAeA,CAAA,EAAG;MAChC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACnH,KAAK,CAACoH,aAAa,EAAE;QAC5B,OAAO,aAAanI,KAAK,CAACoG,aAAa,CAAC,QAAQ,EAAE;UAChDgC,IAAI,EAAE,QAAQ;UACdC,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;YACpB,OAAOJ,MAAM,CAACvC,SAAS,GAAG2C,EAAE;UAC9B,CAAC;UACDC,SAAS,EAAE,uCAAuC;UAClDC,OAAO,EAAE,IAAI,CAAC/D,YAAY;UAC1B,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAAC0H;QAC3B,CAAC,EAAE,aAAazI,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;UAC1CmC,SAAS,EAAE;QACb,CAAC,CAAC,EAAE,aAAavI,KAAK,CAACoG,aAAa,CAAChG,MAAM,EAAE,IAAI,CAAC,CAAC;MACrD;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDoB,GAAG,EAAE,aAAa;IAClBkB,KAAK,EAAE,SAASgG,WAAWA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAAC3H,KAAK,CAAC4H,KAAK,EAAE;QACpB,OAAOtI,WAAW,CAACuI,aAAa,CAAC,IAAI,CAAC7H,KAAK,CAAC4H,KAAK,EAAE,IAAI,CAAC5H,KAAK,CAAC;MAChE;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDS,GAAG,EAAE,eAAe;IACpBkB,KAAK,EAAE,SAASmG,aAAaA,CAAA,EAAG;MAC9B,IAAIN,SAAS,GAAGjI,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAACS,KAAK,CAACwH,SAAS,EAAE,YAAY,GAAG,IAAI,CAACxH,KAAK,CAAC+H,QAAQ,EAAE;QAC5G,kBAAkB,EAAE,IAAI,CAAC/H,KAAK,CAACgI,OAAO;QACtC,gBAAgB,EAAE,IAAI,CAAChI,KAAK,CAACiI;MAC/B,CAAC,CAAC;MACF,IAAIrD,SAAS,GAAG,IAAI,CAACsC,eAAe,CAAC,CAAC;MACtC,IAAIU,KAAK,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MAC9B,IAAIO,iBAAiB,GAAG;QACtBC,KAAK,EAAE,IAAI,CAACnI,KAAK,CAACiI,UAAU,GAAG,GAAG,GAAG,GAAG;QACxCG,IAAI,EAAE,IAAI,CAACpI,KAAK,CAACiI,UAAU,GAAG,GAAG,GAAG;MACtC,CAAC;MACD,OAAO,aAAahJ,KAAK,CAACoG,aAAa,CAAC7F,aAAa,EAAE;QACrD6I,OAAO,EAAE,IAAI,CAACrE,UAAU;QACxBzE,UAAU,EAAE,WAAW;QACvB+I,EAAE,EAAE,IAAI,CAACtI,KAAK,CAACgI,OAAO;QACtBO,OAAO,EAAEL,iBAAiB;QAC1BM,OAAO,EAAE,IAAI,CAACxI,KAAK,CAACyI,iBAAiB;QACrCC,aAAa,EAAE,IAAI;QACnB9E,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAE,aAAa9E,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;QACzCiC,GAAG,EAAE,IAAI,CAACtD,UAAU;QACpB2E,EAAE,EAAE,IAAI,CAAC3I,KAAK,CAAC2I,EAAE;QACjBnB,SAAS,EAAEA,SAAS;QACpBlC,KAAK,EAAE,IAAI,CAACtF,KAAK,CAACsF,KAAK;QACvBsD,IAAI,EAAE;MACR,CAAC,EAAE,aAAa3J,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;QACzCmC,SAAS,EAAE;MACb,CAAC,EAAEI,KAAK,EAAEhD,SAAS,CAAC,EAAE,aAAa3F,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;QAC5DmC,SAAS,EAAE;MACb,CAAC,EAAE,IAAI,CAACxH,KAAK,CAAC6I,QAAQ,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE;IACDpI,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAASmH,MAAMA,CAAA,EAAG;MACvB,IAAIC,OAAO,GAAG,IAAI,CAACjB,aAAa,CAAC,CAAC;MAClC,OAAO,aAAa7I,KAAK,CAACoG,aAAa,CAAC5F,MAAM,EAAE;QAC9CsJ,OAAO,EAAEA,OAAO;QAChBC,QAAQ,EAAE,IAAI,CAAChJ,KAAK,CAACgJ;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EAEH,OAAO1F,OAAO;AAChB,CAAC,CAACpE,SAAS,CAAC;AAEZkD,eAAe,CAACkB,OAAO,EAAE,cAAc,EAAE;EACvCqF,EAAE,EAAE,IAAI;EACRrD,KAAK,EAAE,IAAI;EACXkC,SAAS,EAAE,IAAI;EACfQ,OAAO,EAAE,KAAK;EACdD,QAAQ,EAAE,MAAM;EAChBE,UAAU,EAAE,KAAK;EACjBtC,WAAW,EAAE,KAAK;EAClBpB,UAAU,EAAE,CAAC;EACbsB,WAAW,EAAE,IAAI;EACjBuB,aAAa,EAAE,IAAI;EACnBM,cAAc,EAAE,OAAO;EACvBhD,aAAa,EAAE,IAAI;EACnBkD,KAAK,EAAE,IAAI;EACXpD,KAAK,EAAE,IAAI;EACXwE,QAAQ,EAAE,IAAI;EACdP,iBAAiB,EAAE,IAAI;EACvB3D,MAAM,EAAE,IAAI;EACZX,MAAM,EAAE;AACV,CAAC,CAAC;AAEF,SAASb,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}