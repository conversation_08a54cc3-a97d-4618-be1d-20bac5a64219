{"ast": null, "code": "import * as React from 'react';\nvar SizeContext = /*#__PURE__*/React.createContext(undefined);\nexport var SizeContextProvider = function SizeContextProvider(_ref) {\n  var children = _ref.children,\n    size = _ref.size;\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {\n    return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n      value: size || originSize\n    }, children);\n  });\n};\nexport default SizeContext;", "map": {"version": 3, "names": ["React", "SizeContext", "createContext", "undefined", "SizeContextProvider", "_ref", "children", "size", "createElement", "Consumer", "originSize", "Provider", "value"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/config-provider/SizeContext.js"], "sourcesContent": ["import * as React from 'react';\nvar SizeContext = /*#__PURE__*/React.createContext(undefined);\nexport var SizeContextProvider = function SizeContextProvider(_ref) {\n  var children = _ref.children,\n      size = _ref.size;\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {\n    return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n      value: size || originSize\n    }, children);\n  });\n};\nexport default SizeContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC7D,OAAO,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAClE,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAACP,WAAW,CAACQ,QAAQ,EAAE,IAAI,EAAE,UAAUC,UAAU,EAAE;IACxF,OAAO,aAAaV,KAAK,CAACQ,aAAa,CAACP,WAAW,CAACU,QAAQ,EAAE;MAC5DC,KAAK,EAAEL,IAAI,IAAIG;IACjB,CAAC,EAAEJ,QAAQ,CAAC;EACd,CAAC,CAAC;AACJ,CAAC;AACD,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}