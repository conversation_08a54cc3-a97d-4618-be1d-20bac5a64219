{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\dashboard\\\\dashboardDistributore.jsx\";\n/*\n*\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardDistributore - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport { TabPanel, TabView } from \"primereact/tabview\";\nimport { TabellaTopFlop } from \"../../../components/generalizzazioni/statistiche/tabellaTopFlop\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport OrdiniGiornalieri from \"../../../components/generalizzazioni/tabellaOrdiniGiornalieri\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DashboardDistributore extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      visibility: 'd-none'\n    };\n  }\n  componentDidMount() {\n    var _utente;\n    var utente = [];\n    utente = localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : '';\n    if ((_utente = utente) !== null && _utente !== void 0 && _utente.userGuiInhibition) {\n      var _utente$userGuiInhibi;\n      if (((_utente$userGuiInhibi = utente.userGuiInhibition.inhibition.bannerWelcome) === null || _utente$userGuiInhibi === void 0 ? void 0 : _utente$userGuiInhibi.visibility) === \"true\") {\n        this.setState({\n          visibility: ''\n        });\n      }\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(BannerWelcome, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album py-4\",\n        children: /*#__PURE__*/_jsxDEV(TabView, {\n          className: \"tabview-custom\",\n          children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n            style: {\n              padding: '0px'\n            },\n            header: \"Bacheca\",\n            leftIcon: \"pi pi-th-large mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container mt-3 pb-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card border-0\",\n                children: /*#__PURE__*/_jsxDEV(Dashboard, {\n                  disabled: this.state.disabled\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n            header: \"Trends\",\n            leftIcon: \"pi pi-chart-bar mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container-boxed mt-3 pb-4 px-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-12 col-md-3 col-lg-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-12 col-md-9 col-lg-10\",\n                  children: /*#__PURE__*/_jsxDEV(OrdiniGiornalieri, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 56,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"widget-trends\",\n                children: /*#__PURE__*/_jsxDEV(TabellaTopFlop, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DashboardDistributore;", "map": {"version": 3, "names": ["React", "Component", "BannerWelcome", "TabPanel", "TabView", "TabellaTop<PERSON>lop", "Nav", "Dashboard", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DashboardDistributore", "constructor", "props", "state", "visibility", "componentDidMount", "_utente", "utente", "localStorage", "getItem", "JSON", "parse", "userGuiInhibition", "_utente$userGuiInhibi", "inhibition", "bannerWelcome", "setState", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "header", "leftIcon", "disabled"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/dashboard/dashboardDistributore.jsx"], "sourcesContent": ["/*\n*\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardDistributore - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport { TabPanel, TabView } from \"primereact/tabview\";\nimport { TabellaTopFlop } from \"../../../components/generalizzazioni/statistiche/tabellaTopFlop\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport OrdiniGiornalieri from \"../../../components/generalizzazioni/tabellaOrdiniGiornalieri\";\n\nclass DashboardDistributore extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            visibility: 'd-none'\n        }\n    }\n    componentDidMount() {\n        var utente = []\n        utente = localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : ''\n        if (utente?.userGuiInhibition) {\n            if (utente.userGuiInhibition.inhibition.bannerWelcome?.visibility === \"true\") {\n                this.setState({\n                    visibility: ''\n                })\n            }\n        }\n    }\n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <BannerWelcome />\n                <div className=\"album py-4\">\n                    <TabView className=\"tabview-custom\">\n                        <TabPanel style={{ padding: '0px' }} header=\"Bacheca\" leftIcon=\"pi pi-th-large mr-2\">\n                            <div className=\"container mt-3 pb-0\">\n                                <div className=\"card border-0\">\n                                    <Dashboard disabled={this.state.disabled} />\n                                </div>\n                            </div>\n                        </TabPanel>\n                        <TabPanel header=\"Trends\" leftIcon=\"pi pi-chart-bar mr-2\">\n                            <div className=\"container-boxed mt-3 pb-4 px-4\">\n                                <div className=\"row\">\n                                    <div className=\"col-12 col-md-3 col-lg-1\">\n\n                                    </div>\n                                    <div className=\"col-12 col-md-9 col-lg-10\">\n                                        <OrdiniGiornalieri />\n                                    </div>\n                                </div>\n                                <div className=\"widget-trends\">\n                                    <TabellaTopFlop />\n                                </div>\n                            </div>\n                        </TabPanel>\n                    </TabView>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default DashboardDistributore;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,QAAQ,oDAAoD;AAClF,SAASC,QAAQ,EAAEC,OAAO,QAAQ,oBAAoB;AACtD,SAASC,cAAc,QAAQ,iEAAiE;AAChG,OAAOC,GAAG,MAAM,oCAAoC;AACpD,OAAOC,SAAS,MAAM,0CAA0C;AAChE,OAAOC,iBAAiB,MAAM,+DAA+D;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,MAAMC,qBAAqB,SAASV,SAAS,CAAC;EAC1CW,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,UAAU,EAAE;IAChB,CAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAChB,IAAIC,MAAM,GAAG,EAAE;IACfA,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;IACrF,KAAAH,OAAA,GAAIC,MAAM,cAAAD,OAAA,eAANA,OAAA,CAAQM,iBAAiB,EAAE;MAAA,IAAAC,qBAAA;MAC3B,IAAI,EAAAA,qBAAA,GAAAN,MAAM,CAACK,iBAAiB,CAACE,UAAU,CAACC,aAAa,cAAAF,qBAAA,uBAAjDA,qBAAA,CAAmDT,UAAU,MAAK,MAAM,EAAE;QAC1E,IAAI,CAACY,QAAQ,CAAC;UACVZ,UAAU,EAAE;QAChB,CAAC,CAAC;MACN;IACJ;EACJ;EACAa,MAAMA,CAAA,EAAG;IACL,oBACIlB,OAAA;MAAKmB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BpB,OAAA,CAACJ,GAAG;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPxB,OAAA,CAACR,aAAa;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjBxB,OAAA;QAAKmB,SAAS,EAAC,YAAY;QAAAC,QAAA,eACvBpB,OAAA,CAACN,OAAO;UAACyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC/BpB,OAAA,CAACP,QAAQ;YAACgC,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAM,CAAE;YAACC,MAAM,EAAC,SAAS;YAACC,QAAQ,EAAC,qBAAqB;YAAAR,QAAA,eAChFpB,OAAA;cAAKmB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAChCpB,OAAA;gBAAKmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1BpB,OAAA,CAACH,SAAS;kBAACgC,QAAQ,EAAE,IAAI,CAACzB,KAAK,CAACyB;gBAAS;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACXxB,OAAA,CAACP,QAAQ;YAACkC,MAAM,EAAC,QAAQ;YAACC,QAAQ,EAAC,sBAAsB;YAAAR,QAAA,eACrDpB,OAAA;cAAKmB,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC3CpB,OAAA;gBAAKmB,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAChBpB,OAAA;kBAAKmB,SAAS,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEpC,CAAC,eACNxB,OAAA;kBAAKmB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACtCpB,OAAA,CAACF,iBAAiB;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNxB,OAAA;gBAAKmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1BpB,OAAA,CAACL,cAAc;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAevB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}