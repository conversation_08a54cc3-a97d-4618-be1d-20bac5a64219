export default InspectorIssues;
declare class InspectorIssues extends FRGatherer {
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /** @type {Array<LH.Crdp.Audits.InspectorIssue>} */
    _issues: Array<LH.Crdp.Audits.InspectorIssue>;
    _onIssueAdded: (entry: LH.Crdp.Audits.IssueAddedEvent) => void;
    /**
     * @param {LH.Crdp.Audits.IssueAddedEvent} entry
     */
    onIssueAdded(entry: LH.Crdp.Audits.IssueAddedEvent): void;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {Promise<LH.Artifacts['InspectorIssues']>}
     */
    _getArtifact(networkRecords: Array<LH.Artifacts.NetworkRequest>): Promise<LH.Artifacts['InspectorIssues']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['InspectorIssues']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['InspectorIssues']>;
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Artifacts['InspectorIssues']>}
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Artifacts['InspectorIssues']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=inspector-issues.d.ts.map