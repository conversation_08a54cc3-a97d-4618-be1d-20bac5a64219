/**
 * Winet e-procurement GUI
 * 2020 - Viniexport.com (C)
 *
 * GestionePuntiVendita - operazioni sui punti vendita
 *
 */
 import React, { Component } from "react";
 import { Toast } from "primereact/toast";
 import { Button } from "primereact/button";
 import { Dialog } from "primereact/dialog";
 import { APIRequest } from "../../components/generalizzazioni/apireq";
 import { Costanti } from "../../components/traduttore/const";
 import Nav from "../../components/navigation/Nav";
 import AggiungiPV from "../../aggiunta_dati/aggiungiPV";
 import Caricamento from "../../utils/caricamento";
 import UtentePDV from "../../aggiunta_dati/utentePDV";
 import CustomDataTable from "../../components/customDataTable";
 import "../../css/DataTableDemo.css";
 
 class GestionePDV extends Component {
   //Stato iniziale elementi tabella
   emptyResult = {
     id: null,
     customerName: "",
     address: "",
     pIva: "",
     email: "",
     isValid: "",
     createAt: "",
     updateAt: "",
   };
   constructor(props) {
     super(props);
     //Dichiarazione variabili di scena
     this.state = {
       results: [],
       resultDialog2: false,
       resultDialog3: false,
       deleteResultDialog: false,
       result: this.emptyResult,
       submitted: false,
       globalFilter: null,
       loading: true,
     };
     //Dichiarazione funzioni e metodi
     this.confirmDeleteResult = this.confirmDeleteResult.bind(this);
     this.deleteResult = this.deleteResult.bind(this);
     this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);
     this.aggiungiPV = this.aggiungiPV.bind(this);
     this.hideaggiungiPV = this.hideaggiungiPV.bind(this);
     this.addUser = this.addUser.bind(this);
     this.hideUtentePDV = this.hideUtentePDV.bind(this);
     this.Invia = this.Invia.bind(this);
   }
   //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta
   async componentDidMount(results) {
     await APIRequest("GET", "retailers/")
       .then((res) => {
         for (var entry of res.data) {
           var x = {
             id: entry.id,
             idAffiliate: entry.idAffiliate,
             idAffiliate2: entry.idAffiliate2,
             idRegistry: entry.idRegistry.id,
             firstName: entry.idRegistry.firstName,
             lastName: entry.idRegistry.lastName,
             address: entry.idRegistry.address,
             pIva: entry.idRegistry.pIva,
             email: entry.idRegistry.email,
             cap: entry.idRegistry.cap,
             city: entry.idRegistry.city,
             externalCode: entry.idRegistry.externalCode,
             idAgente: entry.idRegistry.idAgente,
             tel: entry.idRegistry.tel,
             isValid: entry.idRegistry.isValid,
             createdAt: entry.createdAt,
             updateAt: entry.updateAt,
             users: entry.idRegistry.users,
           };
           this.state.results.push(x);
         }
         this.setState((state) => ({ ...state, ...results, loading: false }));
       })
       .catch((e) => {
         console.log(e);
         this.toast.show({
           severity: "error",
           summary: "Siamo spiacenti",
           detail: `Non è stato possibile visualizzare i punti vendita. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,
           life: 3000,
         });
       });
   }
   //Chiusura dialogo eliminazione senza azioni
   hideDeleteResultDialog() {
     this.setState({ deleteResultDialog: false });
   }
   //Apertura dialogo aggiunta
   aggiungiPV() {
     this.setState({
       resultDialog2: true,
     });
   }
   //Chiusura dialogo aggiunta
   hideaggiungiPV() {
     this.setState({
       resultDialog2: false,
     });
   }
   //Apertura dialogo elimina
   confirmDeleteResult(result) {
     this.setState({
       result,
       deleteResultDialog: true,
     });
   }
   //Apertura dialogo aggiunta utente
   addUser(rowData, value) {
     localStorage.setItem("datiComodo", rowData.idRegistry);
     this.setState({
       resultDialog3: true,
     });
   }
   //Chiusura dialogo aggiunta utente
   hideUtentePDV() {
     this.setState({
       resultDialog3: false,
     });
   }
   //Metodo di cancellazione definitivo grazie alla chiamata axios
   async deleteResult() {
     let results = this.state.results.filter(
       (val) => val.id !== this.state.result.id
     );
     this.setState({
       results,
       deleteResultDialog: false,
       result: this.emptyResult,
     });
     let url = "retailers/?id=" + this.state.result.id;
     var res = await APIRequest("DELETE", url);
     console.log(res.data);
     this.toast.show({
       severity: "success",
       summary: "Ottimo",
       detail: "Utente eliminato con successo",
       life: 3000,
     });
     window.location.reload();
   }
   async Invia() {
     var completa = [];
     completa = JSON.parse(localStorage.getItem("datiComodo"));
     await APIRequest("POST", "retailers/", completa)
       .then((res) => {
         console.log(res.data);
         this.toast.show({
           severity: "success",
           summary: "Ottimo",
           detail: "Il punto vendita è stato inserito con successo",
           life: 3000,
         });
         setTimeout(() => {
           window.location.reload();
         }, 3000);
       })
       .catch((e) => {
         console.error('❌ Errore creazione punto vendita:', e);

         // Gestione specifica degli errori
         const errorStatus = e.response?.status;
         const errorData = e.response?.data;
         const errorMessage = e.response?.data?.message || e.message;

         let userMessage = '';
         let summary = 'Errore';

         if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||
             errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {
             // Violazione unique constraint
             summary = '⚠️ Punto vendita già presente';
             userMessage = 'Questo punto vendita è già registrato nel sistema. Verificare i dati inseriti.';
         } else if (errorStatus === 400) {
             // Errori di validazione
             summary = '📝 Dati non validi';
             if (errorMessage.toLowerCase().includes('indirizzo') || errorMessage.toLowerCase().includes('address')) {
                 userMessage = 'L\'indirizzo del punto vendita non è valido. Verificare i dati inseriti.';
             } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('phone')) {
                 userMessage = 'Il numero di telefono non è valido. Verificare il formato.';
             } else {
                 userMessage = `Dati inseriti non validi: ${errorMessage}`;
             }
         } else if (errorStatus === 422) {
             // Errori di business logic
             summary = '🚫 Operazione non consentita';
             userMessage = `Impossibile creare il punto vendita: ${errorMessage}`;
         } else if (errorStatus === 500) {
             // Errore server
             summary = '🔧 Errore del server';
             userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto.';
         } else if (!errorStatus) {
             // Errore di rete
             summary = '🌐 Errore di connessione';
             userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
         } else {
             // Altri errori
             summary = '❌ Errore imprevisto';
             userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
         }

         this.toast.show({
           severity: "error",
           summary: summary,
           detail: userMessage,
           life: 6000,
         });

         // Log dettagliato per debugging
         console.error('Dettagli errore punto vendita:', {
           status: errorStatus,
           data: errorData,
           message: errorMessage,
           fullError: e
         });
       });
   }
   render() {
     //Elementi del footer nelle finestre di dialogo dellaggiunta
     const resultDialogFooter2 = (
       <React.Fragment>
         <Button className="p-button-text" onClick={this.hideaggiungiPV}>
           {" "}
           {Costanti.Chiudi}{" "}
         </Button>
         <Button className="p-button-text" onClick={this.Invia}>
           {" "}
           {Costanti.salva}{" "}
         </Button>
       </React.Fragment>
     );
     //Elementi del footer nelle finestre di dialogo dellaggiunta utente
     const resultDialogFooter3 = (
       <React.Fragment>
         <Button className="p-button-text" onClick={this.hideUtentePDV}>
           {" "}
           {Costanti.Chiudi}{" "}
         </Button>
       </React.Fragment>
     );
     //Elementi di conferma o annullamento del dialogo di cancellazione
     const deleteResultDialogFooter = (
       <React.Fragment>
         <Button
           label="No"
           icon="pi pi-times"
           className="p-button-text"
           onClick={this.hideDeleteResultDialog}
         />
         <Button className="p-button-text" onClick={this.deleteResult}>
           {" "}
           {Costanti.Si}{" "}
         </Button>
       </React.Fragment>
     );
     const fields = [
       {
         field: "firstName",
         header: Costanti.rSociale,
         body: "firstName",
         sortable: true,
         showHeader: true,
       },
       {
         field: "address",
         header: Costanti.Indirizzo,
         body: "address",
         sortable: true,
         showHeader: true,
       },
       {
         field: "city",
         header: Costanti.Città,
         body: "city",
         sortable: true,
         showHeader: true,
       },
       {
         field: "cap",
         header: Costanti.CodPost,
         body: "cap",
         sortable: true,
         showHeader: true,
       },
       {
         field: "pIva",
         header: Costanti.pIva,
         body: "pIva",
         sortable: true,
         showHeader: true,
       },
       {
         field: "tel",
         header: Costanti.Tel,
         body: "tel",
         sortable: true,
         showHeader: true,
       },
       {
         field: "email",
         header: Costanti.Email,
         body: "email",
         sortable: true,
         showHeader: true,
       },
       {
         field: "user",
         header: Costanti.AssUser,
         body: "userBodyTemplate",
         showHeader: true,
       },
     ];
     const actionFields = [
       { name: Costanti.addUser,icon: <i className="pi pi-user-plus" />, handler: this.addUser },
       { name: Costanti.Elimina,icon: <i className="pi pi-trash" />, handler: this.confirmDeleteResult },
     ];
     const items = [
       {
         label: Costanti.AggPV,
         icon: 'pi pi-plus-circle',
         command: () => {
           this.aggiungiPV()
         }
       },
     ]
     return (
       <div className="datatable-responsive-demo wrapper">
         {/* Il componente Toast permette di creare e visualizzare messaggi */}
         <Toast ref={(el) => (this.toast = el)} />
         {/* Il componente Nav contiene l'header ed il menù di navigazione */}
         <Nav />
         <div className="col-12 px-0 solid-head">
           <h1>{Costanti.gestionePuntiVendita}</h1>
         </div>
         <div className="card">
           {/* Componente primereact per la creazione della tabella */}
           <CustomDataTable
             ref={(el) => (this.dt = el)}
             value={this.state.results}
             fields={fields}
             loading={this.state.loading}
             dataKey="id"
             paginator
             rows={20}
             rowsPerPageOptions={[10, 20, 50]}
             actionsColumn={actionFields}
             autoLayout={true}
             splitButtonClass={true}
             items={items}
             selectionMode="single"
             cellSelection={true}
             onCellSelect={this.addUser}
             fileNames="PuntiVendita"
           />
         </div>
         {/* Struttura dialogo per la aggiunta utente */}
         <Dialog
           visible={this.state.resultDialog3}
           header={Costanti.GestUtPDV}
           modal
           className="p-fluid modalBox"
           footer={resultDialogFooter3}
           onHide={this.hideUtentePDV}
         >
           <Caricamento />
           <UtentePDV />
         </Dialog>
         {/* Struttura dialogo per la aggiunta */}
         <Dialog
           visible={this.state.resultDialog2}
           header={Costanti.GestUtPDV}
           modal
           className="p-fluid modalBox"
           footer={resultDialogFooter2}
           onHide={this.hideaggiungiPV}
         >
           <Caricamento />
           <AggiungiPV />
         </Dialog>
         {/* Struttura dialogo per la cancellazione */}
         <Dialog
           visible={this.state.deleteResultDialog}
           header={Costanti.Conferma}
           modal
           footer={deleteResultDialogFooter}
           onHide={this.hideDeleteResultDialog}
         >
           <div className="confirmation-content">
             <i
               className="pi pi-exclamation-triangle p-mr-3"
               style={{ fontSize: "2rem" }}
             />
             {this.state.result && (
               <span>
                 {Costanti.ResDeleteCli} <b>{this.state.result.firstName}?</b>
               </span>
             )}
           </div>
         </Dialog>
       </div>
     );
   }
 }
 
 export default GestionePDV;
 