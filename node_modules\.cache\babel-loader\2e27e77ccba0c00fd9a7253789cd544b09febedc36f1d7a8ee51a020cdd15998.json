{"ast": null, "code": "import ReactDOM from 'react-dom';\nexport default function addEventListenerWrap(target, eventType, cb, option) {\n  /* eslint camelcase: 2 */\n  var callback = ReactDOM.unstable_batchedUpdates ? function run(e) {\n    ReactDOM.unstable_batchedUpdates(cb, e);\n  } : cb;\n  if (target.addEventListener) {\n    target.addEventListener(eventType, callback, option);\n  }\n  return {\n    remove: function remove() {\n      if (target.removeEventListener) {\n        target.removeEventListener(eventType, callback);\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["ReactDOM", "addEventListenerWrap", "target", "eventType", "cb", "option", "callback", "unstable_batchedUpdates", "run", "e", "addEventListener", "remove", "removeEventListener"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/addEventListener.js"], "sourcesContent": ["import ReactDOM from 'react-dom';\nexport default function addEventListenerWrap(target, eventType, cb, option) {\n  /* eslint camelcase: 2 */\n  var callback = ReactDOM.unstable_batchedUpdates ? function run(e) {\n    ReactDOM.unstable_batchedUpdates(cb, e);\n  } : cb;\n\n  if (target.addEventListener) {\n    target.addEventListener(eventType, callback, option);\n  }\n\n  return {\n    remove: function remove() {\n      if (target.removeEventListener) {\n        target.removeEventListener(eventType, callback);\n      }\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,eAAe,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,EAAE,EAAEC,MAAM,EAAE;EAC1E;EACA,IAAIC,QAAQ,GAAGN,QAAQ,CAACO,uBAAuB,GAAG,SAASC,GAAGA,CAACC,CAAC,EAAE;IAChET,QAAQ,CAACO,uBAAuB,CAACH,EAAE,EAAEK,CAAC,CAAC;EACzC,CAAC,GAAGL,EAAE;EAEN,IAAIF,MAAM,CAACQ,gBAAgB,EAAE;IAC3BR,MAAM,CAACQ,gBAAgB,CAACP,SAAS,EAAEG,QAAQ,EAAED,MAAM,CAAC;EACtD;EAEA,OAAO;IACLM,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;MACxB,IAAIT,MAAM,CAACU,mBAAmB,EAAE;QAC9BV,MAAM,CAACU,mBAAmB,CAACT,SAAS,EAAEG,QAAQ,CAAC;MACjD;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}