{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\Home.js\";\nimport React, { Component } from 'react';\nimport { Redirect } from 'react-router-dom';\nimport { logout, isLogin } from '../utils';\nimport { admin, affiliato, affiliatoDashboard, agente, agenteDashboard, autista, autistaGestioneConsegne, basePath, chain, chainDashboard, dashboard, distributore, distributoreDashboard, login, logistica, logisticaGestioneLogistica, pdv, pdvListinoProdotti, respMag, resp_mag, ring, ringDashboard } from './route';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass Home extends Component {\n  constructor(props) {\n    super(props);\n\n    // Clean corrupted localStorage if needed\n    this.handleLogout = () => {\n      logout();\n      this.setState({\n        isLogin: false\n      });\n    };\n    const token = localStorage.getItem('login_token');\n    if (token === 'dsgsdgsd' || token === 'undefined' || token === 'null') {\n      console.log('🧹 Cleaning corrupted localStorage');\n      localStorage.clear();\n    }\n    this.state = {\n      isLogin: isLogin()\n    };\n  }\n  renderSwitch(param) {\n    console.log('renderSwitch called with:', param);\n    console.log('Available roles:', {\n      distributore,\n      affiliato,\n      agente,\n      admin\n    });\n\n    // Handle both original constants and normalized strings\n    switch (param) {\n      case distributore:\n      case 'distributore':\n      case 'DISTRIBUTORE':\n      case 'DISTRIBUTOR':\n        console.log('Matched distributore, redirecting to:', distributoreDashboard);\n        return distributoreDashboard;\n      case affiliato:\n      case 'affiliato':\n      case 'AFFILIATO':\n        return affiliatoDashboard;\n      case agente:\n      case 'agente':\n      case 'AGENTE':\n        return agenteDashboard;\n      case logistica:\n      case 'logistica':\n      case 'LOGISTICA':\n        return logisticaGestioneLogistica;\n      case autista:\n      case 'autista':\n      case 'AUTISTA':\n        return autistaGestioneConsegne;\n      case pdv:\n      case 'pdv':\n      case 'PDV':\n        return pdvListinoProdotti;\n      case admin:\n      case 'admin':\n      case 'ADMIN':\n        return dashboard;\n      case resp_mag:\n      case 'resp_mag':\n      case 'RESP_MAG':\n        return respMag;\n      case chain:\n      case 'chain':\n      case 'CHAIN':\n        return chainDashboard;\n      case ring:\n      case 'ring':\n      case 'RING':\n        return ringDashboard;\n      default:\n        console.log('No match found, using default basePath');\n        return basePath;\n    }\n  }\n  render() {\n    console.log('Home render - isLogin:', this.state.isLogin, 'role:', localStorage.role);\n    if (this.state.isLogin) {\n      const redirectTo = this.renderSwitch(localStorage.role);\n      console.log('Redirecting logged user to:', redirectTo);\n      return /*#__PURE__*/_jsxDEV(Redirect, {\n        to: redirectTo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 20\n      }, this);\n    } else {\n      console.log('Redirecting to login');\n      return /*#__PURE__*/_jsxDEV(Redirect, {\n        to: login\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 20\n      }, this);\n    }\n  }\n}\nexport default Home;", "map": {"version": 3, "names": ["React", "Component", "Redirect", "logout", "is<PERSON>ogin", "admin", "affiliato", "affiliatoDashboard", "agente", "agenteDashboard", "au<PERSON>", "autistaGestioneConsegne", "basePath", "chain", "chainDashboard", "dashboard", "distributore", "distributoreDashboard", "login", "logistica", "logisticaGestioneLogistica", "pdv", "pdvListino<PERSON>i", "respMag", "resp_mag", "ring", "ringDashboard", "jsxDEV", "_jsxDEV", "Home", "constructor", "props", "handleLogout", "setState", "token", "localStorage", "getItem", "console", "log", "clear", "state", "renderSwitch", "param", "render", "role", "redirectTo", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/Home.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport { Redirect } from 'react-router-dom';\nimport { logout, isLogin } from '../utils';\nimport {\n    admin,\n    affiliato,\n    affiliatoDashboard,\n    agente,\n    agenteDashboard,\n    autista,\n    autistaGestioneConsegne,\n    basePath,\n    chain,\n    chainDashboard,\n    dashboard,\n    distributore,\n    distributoreDashboard,\n    login,\n    logistica,\n    logisticaGestioneLogistica,\n    pdv,\n    pdvListinoProdotti,\n    respMag,\n    resp_mag,\n    ring,\n    ringDashboard\n} from './route';\n\nclass Home extends Component {\n\n    constructor(props) {\n        super(props);\n\n        // Clean corrupted localStorage if needed\n        const token = localStorage.getItem('login_token');\n        if (token === 'dsgsdgsd' || token === 'undefined' || token === 'null') {\n            console.log('🧹 Cleaning corrupted localStorage');\n            localStorage.clear();\n        }\n\n        this.state = {\n            isLogin: isLogin(),\n        }\n    }\n\n    handleLogout = () => {\n        logout();\n        this.setState({\n            isLogin: false\n        })\n    }\n\n    renderSwitch(param) {\n        console.log('renderSwitch called with:', param);\n        console.log('Available roles:', { distributore, affiliato, agente, admin });\n\n        // Handle both original constants and normalized strings\n        switch (param) {\n            case distributore:\n            case 'distributore':\n            case 'DISTRIBUTORE':\n            case 'DISTRIBUTOR':\n                console.log('Matched distributore, redirecting to:', distributoreDashboard);\n                return distributoreDashboard;\n            case affiliato:\n            case 'affiliato':\n            case 'AFFILIATO':\n                return affiliatoDashboard;\n            case agente:\n            case 'agente':\n            case 'AGENTE':\n                return agenteDashboard;\n            case logistica:\n            case 'logistica':\n            case 'LOGISTICA':\n                return logisticaGestioneLogistica;\n            case autista:\n            case 'autista':\n            case 'AUTISTA':\n                return autistaGestioneConsegne;\n            case pdv:\n            case 'pdv':\n            case 'PDV':\n                return pdvListinoProdotti;\n            case admin:\n            case 'admin':\n            case 'ADMIN':\n                return dashboard;\n            case resp_mag:\n            case 'resp_mag':\n            case 'RESP_MAG':\n                return respMag;\n            case chain:\n            case 'chain':\n            case 'CHAIN':\n                return chainDashboard;\n            case ring:\n            case 'ring':\n            case 'RING':\n                return ringDashboard;\n            default:\n                console.log('No match found, using default basePath');\n                return basePath;\n        }\n    }\n\n\n    render() {\n        console.log('Home render - isLogin:', this.state.isLogin, 'role:', localStorage.role);\n\n        if (this.state.isLogin) {\n            const redirectTo = this.renderSwitch(localStorage.role);\n            console.log('Redirecting logged user to:', redirectTo);\n            return <Redirect to={redirectTo} />;\n        } else {\n            console.log('Redirecting to login');\n            return <Redirect to={login} />;\n        }\n    }\n}\n\nexport default Home;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,MAAM,EAAEC,OAAO,QAAQ,UAAU;AAC1C,SACIC,KAAK,EACLC,SAAS,EACTC,kBAAkB,EAClBC,MAAM,EACNC,eAAe,EACfC,OAAO,EACPC,uBAAuB,EACvBC,QAAQ,EACRC,KAAK,EACLC,cAAc,EACdC,SAAS,EACTC,YAAY,EACZC,qBAAqB,EACrBC,KAAK,EACLC,SAAS,EACTC,0BAA0B,EAC1BC,GAAG,EACHC,kBAAkB,EAClBC,OAAO,EACPC,QAAQ,EACRC,IAAI,EACJC,aAAa,QACV,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjB,MAAMC,IAAI,SAAS5B,SAAS,CAAC;EAEzB6B,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;;IAEZ;IAAA,KAYJC,YAAY,GAAG,MAAM;MACjB7B,MAAM,CAAC,CAAC;MACR,IAAI,CAAC8B,QAAQ,CAAC;QACV7B,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC;IAhBG,MAAM8B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,MAAM,EAAE;MACnEG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDH,YAAY,CAACI,KAAK,CAAC,CAAC;IACxB;IAEA,IAAI,CAACC,KAAK,GAAG;MACTpC,OAAO,EAAEA,OAAO,CAAC;IACrB,CAAC;EACL;EASAqC,YAAYA,CAACC,KAAK,EAAE;IAChBL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,KAAK,CAAC;IAC/CL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAAEtB,YAAY;MAAEV,SAAS;MAAEE,MAAM;MAAEH;IAAM,CAAC,CAAC;;IAE3E;IACA,QAAQqC,KAAK;MACT,KAAK1B,YAAY;MACjB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,aAAa;QACdqB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAErB,qBAAqB,CAAC;QAC3E,OAAOA,qBAAqB;MAChC,KAAKX,SAAS;MACd,KAAK,WAAW;MAChB,KAAK,WAAW;QACZ,OAAOC,kBAAkB;MAC7B,KAAKC,MAAM;MACX,KAAK,QAAQ;MACb,KAAK,QAAQ;QACT,OAAOC,eAAe;MAC1B,KAAKU,SAAS;MACd,KAAK,WAAW;MAChB,KAAK,WAAW;QACZ,OAAOC,0BAA0B;MACrC,KAAKV,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,SAAS;QACV,OAAOC,uBAAuB;MAClC,KAAKU,GAAG;MACR,KAAK,KAAK;MACV,KAAK,KAAK;QACN,OAAOC,kBAAkB;MAC7B,KAAKjB,KAAK;MACV,KAAK,OAAO;MACZ,KAAK,OAAO;QACR,OAAOU,SAAS;MACpB,KAAKS,QAAQ;MACb,KAAK,UAAU;MACf,KAAK,UAAU;QACX,OAAOD,OAAO;MAClB,KAAKV,KAAK;MACV,KAAK,OAAO;MACZ,KAAK,OAAO;QACR,OAAOC,cAAc;MACzB,KAAKW,IAAI;MACT,KAAK,MAAM;MACX,KAAK,MAAM;QACP,OAAOC,aAAa;MACxB;QACIW,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,OAAO1B,QAAQ;IACvB;EACJ;EAGA+B,MAAMA,CAAA,EAAG;IACLN,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACE,KAAK,CAACpC,OAAO,EAAE,OAAO,EAAE+B,YAAY,CAACS,IAAI,CAAC;IAErF,IAAI,IAAI,CAACJ,KAAK,CAACpC,OAAO,EAAE;MACpB,MAAMyC,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACN,YAAY,CAACS,IAAI,CAAC;MACvDP,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEO,UAAU,CAAC;MACtD,oBAAOjB,OAAA,CAAC1B,QAAQ;QAAC4C,EAAE,EAAED;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACvC,CAAC,MAAM;MACHb,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,oBAAOV,OAAA,CAAC1B,QAAQ;QAAC4C,EAAE,EAAE5B;MAAM;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAClC;EACJ;AACJ;AAEA,eAAerB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}