{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\dashboard\\\\dashboardChain.jsx\";\n/*\n*\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardChain - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DashboardChain extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      visibility: 'd-none'\n    };\n  }\n  componentDidMount() {\n    var utente = null;\n    try {\n      const userData = localStorage.getItem(\"user\");\n      if (userData && userData.trim() !== '') {\n        utente = JSON.parse(userData);\n      }\n    } catch (error) {\n      console.warn('Failed to parse user data in dashboardChain:', error);\n    }\n    if (utente && utente.userGuiInhibition && utente.userGuiInhibition.inhibition) {\n      var _utente$userGuiInhibi;\n      if (((_utente$userGuiInhibi = utente.userGuiInhibition.inhibition.bannerWelcome) === null || _utente$userGuiInhibi === void 0 ? void 0 : _utente$userGuiInhibi.visibility) === \"true\") {\n        this.setState({\n          visibility: ''\n        });\n      }\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(BannerWelcome, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DashboardChain;", "map": {"version": 3, "names": ["React", "Component", "BannerWelcome", "Nav", "Dashboard", "jsxDEV", "_jsxDEV", "DashboardChain", "constructor", "props", "state", "visibility", "componentDidMount", "utente", "userData", "localStorage", "getItem", "trim", "JSON", "parse", "error", "console", "warn", "userGuiInhibition", "inhibition", "_utente$userGuiInhibi", "bannerWelcome", "setState", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/dashboard/dashboardChain.jsx"], "sourcesContent": ["/*\n*\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardChain - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\n\nclass DashboardChain extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            visibility: 'd-none'\n        }\n    }\n    componentDidMount() {\n        var utente = null\n        try {\n            const userData = localStorage.getItem(\"user\")\n            if (userData && userData.trim() !== '') {\n                utente = JSON.parse(userData)\n            }\n        } catch (error) {\n            console.warn('Failed to parse user data in dashboardChain:', error)\n        }\n\n        if (utente && utente.userGuiInhibition && utente.userGuiInhibition.inhibition) {\n            if (utente.userGuiInhibition.inhibition.bannerWelcome?.visibility === \"true\") {\n                this.setState({\n                    visibility: ''\n                })\n            }\n        }\n    }\n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <BannerWelcome />\n                <div className=\"album py-4\">\n                    <div className=\"container\">\n                        <Dashboard />\n                    </div>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default DashboardChain;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,QAAQ,oDAAoD;AAClF,OAAOC,GAAG,MAAM,oCAAoC;AACpD,OAAOC,SAAS,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,cAAc,SAASN,SAAS,CAAC;EACnCO,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,UAAU,EAAE;IAChB,CAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAI;MACA,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACpCJ,MAAM,GAAGK,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACjC;IACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,8CAA8C,EAAEF,KAAK,CAAC;IACvE;IAEA,IAAIP,MAAM,IAAIA,MAAM,CAACU,iBAAiB,IAAIV,MAAM,CAACU,iBAAiB,CAACC,UAAU,EAAE;MAAA,IAAAC,qBAAA;MAC3E,IAAI,EAAAA,qBAAA,GAAAZ,MAAM,CAACU,iBAAiB,CAACC,UAAU,CAACE,aAAa,cAAAD,qBAAA,uBAAjDA,qBAAA,CAAmDd,UAAU,MAAK,MAAM,EAAE;QAC1E,IAAI,CAACgB,QAAQ,CAAC;UACVhB,UAAU,EAAE;QAChB,CAAC,CAAC;MACN;IACJ;EACJ;EACAiB,MAAMA,CAAA,EAAG;IACL,oBACItB,OAAA;MAAKuB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BxB,OAAA,CAACH,GAAG;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP5B,OAAA,CAACJ,aAAa;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjB5B,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,eACvBxB,OAAA;UAAKuB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBxB,OAAA,CAACF,SAAS;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAe3B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}