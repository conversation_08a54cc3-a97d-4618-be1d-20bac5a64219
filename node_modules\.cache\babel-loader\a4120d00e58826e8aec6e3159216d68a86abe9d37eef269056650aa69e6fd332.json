{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport warning from \"rc-util/es/warning\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport classNames from 'classnames';\nimport { TreeContext } from './contextTypes';\nimport { getDragChildrenKeys, parseCheckedKeys, conductExpandParent, calcSelectedKeys, calcDropPosition, arrAdd, arrDel, posToArr } from './util';\nimport { flattenTreeData, convertTreeToData, convertDataToEntities, warningWithoutKey, convertNodePropsToEventData, getTreeNodeProps, fillFieldNames } from './utils/treeUtil';\nimport NodeList, { MOTION_KEY, MotionEntity } from './NodeList';\nimport TreeNode from './TreeNode';\nimport { conductCheck } from './utils/conductUtil';\nimport DropIndicator from './DropIndicator';\nvar MAX_RETRY_TIMES = 10;\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  _inherits(Tree, _React$Component);\n  var _super = _createSuper(Tree);\n  function Tree() {\n    var _this;\n    _classCallCheck(this, Tree);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(_args));\n    _this.destroyed = false;\n    _this.delayedDragEnterLogic = void 0;\n    _this.loadingRetryTimes = {};\n    _this.state = {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      dropContainerKey: null,\n      dropLevelOffset: null,\n      dropTargetPos: null,\n      dropAllowed: true,\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: fillFieldNames()\n    };\n    _this.dragStartMousePosition = null;\n    _this.dragNode = void 0;\n    _this.currentMouseOverDroppableNodeKey = null;\n    _this.listRef = /*#__PURE__*/React.createRef();\n    _this.onNodeDragStart = function (event, node) {\n      var _this$state = _this.state,\n        expandedKeys = _this$state.expandedKeys,\n        keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = node.props.eventKey;\n      _this.dragNode = node;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = arrDel(expandedKeys, eventKey);\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: getDragChildrenKeys(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n      _this.setExpandedKeys(newExpandedKeys);\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 ? void 0 : onDragStart({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    _this.onNodeDragEnter = function (event, node) {\n      var _this$state2 = _this.state,\n        expandedKeys = _this$state2.expandedKeys,\n        keyEntities = _this$state2.keyEntities,\n        dragChildrenKeys = _this$state2.dragChildrenKeys,\n        flattenNodes = _this$state2.flattenNodes,\n        indent = _this$state2.indent;\n      var _this$props = _this.props,\n        onDragEnter = _this$props.onDragEnter,\n        onExpand = _this$props.onExpand,\n        allowDrop = _this$props.allowDrop,\n        direction = _this$props.direction;\n      var _node$props = node.props,\n        pos = _node$props.pos,\n        eventKey = _node$props.eventKey;\n      var _assertThisInitialize = _assertThisInitialized(_this),\n        dragNode = _assertThisInitialize.dragNode; // record the key of node which is latest entered, used in dragleave event.\n\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n      if (!dragNode) {\n        _this.resetDragState();\n        return;\n      }\n      var _calcDropPosition = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition.dropPosition,\n        dropLevelOffset = _calcDropPosition.dropLevelOffset,\n        dropTargetKey = _calcDropPosition.dropTargetKey,\n        dropContainerKey = _calcDropPosition.dropContainerKey,\n        dropTargetPos = _calcDropPosition.dropTargetPos,\n        dropAllowed = _calcDropPosition.dropAllowed,\n        dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n      if (\n      // don't allow drop inside its children\n      dragChildrenKeys.indexOf(dropTargetKey) !== -1 ||\n      // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n        return;\n      } // Side effect for delay drag\n\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n      if (dragNode.props.eventKey !== node.props.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) return;\n          var newExpandedKeys = _toConsumableArray(expandedKeys);\n          var entity = keyEntities[node.props.eventKey];\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = arrAdd(expandedKeys, node.props.eventKey);\n          }\n          if (!('expandedKeys' in _this.props)) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n          onExpand === null || onExpand === void 0 ? void 0 : onExpand(newExpandedKeys, {\n            node: convertNodePropsToEventData(node.props),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      } // Skip if drag node is self\n\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n        return;\n      } // Update drag over node and drag state\n\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n      onDragEnter === null || onDragEnter === void 0 ? void 0 : onDragEnter({\n        event: event,\n        node: convertNodePropsToEventData(node.props),\n        expandedKeys: expandedKeys\n      });\n    };\n    _this.onNodeDragOver = function (event, node) {\n      var _this$state3 = _this.state,\n        dragChildrenKeys = _this$state3.dragChildrenKeys,\n        flattenNodes = _this$state3.flattenNodes,\n        keyEntities = _this$state3.keyEntities,\n        expandedKeys = _this$state3.expandedKeys,\n        indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n        onDragOver = _this$props2.onDragOver,\n        allowDrop = _this$props2.allowDrop,\n        direction = _this$props2.direction;\n      var _assertThisInitialize2 = _assertThisInitialized(_this),\n        dragNode = _assertThisInitialize2.dragNode;\n      if (!dragNode) {\n        return;\n      }\n      var _calcDropPosition2 = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition2.dropPosition,\n        dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n        dropTargetKey = _calcDropPosition2.dropTargetKey,\n        dropContainerKey = _calcDropPosition2.dropContainerKey,\n        dropAllowed = _calcDropPosition2.dropAllowed,\n        dropTargetPos = _calcDropPosition2.dropTargetPos,\n        dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n      if (dragChildrenKeys.indexOf(dropTargetKey) !== -1 || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed caculated by calcDropPosition\n        return;\n      } // Update drag position\n\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n      onDragOver === null || onDragOver === void 0 ? void 0 : onDragOver({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    _this.onNodeDragLeave = function (event, node) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === node.props.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 ? void 0 : onDragLeave({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    _this.onWindowDragEnd = function (event) {\n      _this.onNodeDragEnd(event, null, true);\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    };\n    _this.onNodeDragEnd = function (event, node) {\n      var onDragEnd = _this.props.onDragEnd;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      onDragEnd === null || onDragEnd === void 0 ? void 0 : onDragEnd({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n      _this.dragNode = null;\n    };\n    _this.onNodeDrop = function (event, node) {\n      var _this$getActiveItem;\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n        dragChildrenKeys = _this$state4.dragChildrenKeys,\n        dropPosition = _this$state4.dropPosition,\n        dropTargetKey = _this$state4.dropTargetKey,\n        dropTargetPos = _this$state4.dropTargetPos,\n        dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) return;\n      var onDrop = _this.props.onDrop;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      if (dropTargetKey === null) return;\n      var abstractDropNodeProps = _objectSpread(_objectSpread({}, getTreeNodeProps(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.data.key) === dropTargetKey,\n        data: _this.state.keyEntities[dropTargetKey].node\n      });\n      var dropToChild = dragChildrenKeys.indexOf(dropTargetKey) !== -1;\n      warning(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = posToArr(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: convertNodePropsToEventData(abstractDropNodeProps),\n        dragNode: _this.dragNode ? convertNodePropsToEventData(_this.dragNode.props) : null,\n        dragNodesKeys: [_this.dragNode.props.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 ? void 0 : onDrop(dropResult);\n      }\n      _this.dragNode = null;\n    };\n    _this.cleanDragState = function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    };\n    _this.onNodeClick = function (e, treeNode) {\n      var onClick = _this.props.onClick;\n      onClick === null || onClick === void 0 ? void 0 : onClick(e, treeNode);\n    };\n    _this.onNodeDoubleClick = function (e, treeNode) {\n      var onDoubleClick = _this.props.onDoubleClick;\n      onDoubleClick === null || onDoubleClick === void 0 ? void 0 : onDoubleClick(e, treeNode);\n    };\n    _this.onNodeSelect = function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state5 = _this.state,\n        keyEntities = _this$state5.keyEntities,\n        fieldNames = _this$state5.fieldNames;\n      var _this$props3 = _this.props,\n        onSelect = _this$props3.onSelect,\n        multiple = _this$props3.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected; // Update selected keys\n\n      if (!targetSelected) {\n        selectedKeys = arrDel(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = arrAdd(selectedKeys, key);\n      } // [Legacy] Not found related usage in doc or upper libs\n\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = keyEntities[selectedKey];\n        if (!entity) return null;\n        return entity.node;\n      }).filter(function (node) {\n        return node;\n      });\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    };\n    _this.onNodeCheck = function (e, treeNode, checked) {\n      var _this$state6 = _this.state,\n        keyEntities = _this$state6.keyEntities,\n        oriCheckedKeys = _this$state6.checkedKeys,\n        oriHalfCheckedKeys = _this$state6.halfCheckedKeys;\n      var _this$props4 = _this.props,\n        checkStrictly = _this$props4.checkStrictly,\n        onCheck = _this$props4.onCheck;\n      var key = treeNode.key; // Prepare trigger arguments\n\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n      if (checkStrictly) {\n        var checkedKeys = checked ? arrAdd(oriCheckedKeys, key) : arrDel(oriCheckedKeys, key);\n        var halfCheckedKeys = arrDel(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return keyEntities[checkedKey];\n        }).filter(function (entity) {\n          return entity;\n        }).map(function (entity) {\n          return entity.node;\n        });\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = conductCheck([].concat(_toConsumableArray(oriCheckedKeys), [key]), true, keyEntities),\n          _checkedKeys = _conductCheck.checkedKeys,\n          _halfCheckedKeys = _conductCheck.halfCheckedKeys; // If remove, we do it again to correction\n\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n          var _conductCheck2 = conductCheck(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n        checkedObj = _checkedKeys; // [Legacy] This is used for `rc-tree-select`\n\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = keyEntities[checkedKey];\n          if (!entity) return;\n          var node = entity.node,\n            pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n      onCheck === null || onCheck === void 0 ? void 0 : onCheck(checkedObj, eventObj);\n    };\n    _this.onNodeLoad = function (treeNode) {\n      var key = treeNode.key;\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n            loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n            _ref$loadingKeys = _ref.loadingKeys,\n            loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props5 = _this.props,\n            loadData = _this$props5.loadData,\n            onLoad = _this$props5.onLoad;\n          if (!loadData || loadedKeys.indexOf(key) !== -1 || loadingKeys.indexOf(key) !== -1) {\n            return null;\n          } // Process load data\n\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = arrAdd(currentLoadedKeys, key); // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n\n            onLoad === null || onLoad === void 0 ? void 0 : onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            }); // If exceed max retry times, we give up retry\n\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              warning(false, 'Retry for `loadData` many times but still failed. No more retry.');\n              _this.setUncontrolledState({\n                loadedKeys: arrAdd(currentLoadedKeys, key)\n              });\n              resolve();\n            }\n            reject(e);\n          });\n          return {\n            loadingKeys: arrAdd(loadingKeys, key)\n          };\n        });\n      }); // Not care warning if we ignore this\n\n      loadPromise.catch(function () {});\n      return loadPromise;\n    };\n    _this.onNodeMouseEnter = function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n        event: event,\n        node: node\n      });\n    };\n    _this.onNodeMouseLeave = function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n        event: event,\n        node: node\n      });\n    };\n    _this.onNodeContextMenu = function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    };\n    _this.onFocus = function () {\n      var onFocus = _this.props.onFocus;\n      _this.setState({\n        focused: true\n      });\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus.apply(void 0, args);\n    };\n    _this.onBlur = function () {\n      var onBlur = _this.props.onBlur;\n      _this.setState({\n        focused: false\n      });\n      _this.onActiveChange(null);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur.apply(void 0, args);\n    };\n    _this.getTreeNodeRequiredProps = function () {\n      var _this$state7 = _this.state,\n        expandedKeys = _this$state7.expandedKeys,\n        selectedKeys = _this$state7.selectedKeys,\n        loadedKeys = _this$state7.loadedKeys,\n        loadingKeys = _this$state7.loadingKeys,\n        checkedKeys = _this$state7.checkedKeys,\n        halfCheckedKeys = _this$state7.halfCheckedKeys,\n        dragOverNodeKey = _this$state7.dragOverNodeKey,\n        dropPosition = _this$state7.dropPosition,\n        keyEntities = _this$state7.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    };\n    _this.setExpandedKeys = function (expandedKeys) {\n      var _this$state8 = _this.state,\n        treeData = _this$state8.treeData,\n        fieldNames = _this$state8.fieldNames;\n      var flattenNodes = flattenTreeData(treeData, expandedKeys, fieldNames);\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    };\n    _this.onNodeExpand = function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state9 = _this.state,\n        listChanging = _this$state9.listChanging,\n        fieldNames = _this$state9.fieldNames;\n      var _this$props6 = _this.props,\n        onExpand = _this$props6.onExpand,\n        loadData = _this$props6.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key]; // Do nothing when motion is in progress\n\n      if (listChanging) {\n        return;\n      } // Update selected keys\n\n      var index = expandedKeys.indexOf(key);\n      var targetExpanded = !expanded;\n      warning(expanded && index !== -1 || !expanded && index === -1, 'Expand state not sync with index check');\n      if (targetExpanded) {\n        expandedKeys = arrAdd(expandedKeys, key);\n      } else {\n        expandedKeys = arrDel(expandedKeys, key);\n      }\n      _this.setExpandedKeys(expandedKeys);\n      onExpand === null || onExpand === void 0 ? void 0 : onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      }); // Async Load data\n\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = flattenTreeData(_this.state.treeData, expandedKeys, fieldNames);\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = arrDel(currentExpandedKeys, key);\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    };\n    _this.onListChangeStart = function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    };\n    _this.onListChangeEnd = function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    };\n    _this.onActiveChange = function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var onActiveChange = _this.props.onActiveChange;\n      if (activeKey === newActiveKey) {\n        return;\n      }\n      _this.setState({\n        activeKey: newActiveKey\n      });\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey\n        });\n      }\n      onActiveChange === null || onActiveChange === void 0 ? void 0 : onActiveChange(newActiveKey);\n    };\n    _this.getActiveItem = function () {\n      var _this$state10 = _this.state,\n        activeKey = _this$state10.activeKey,\n        flattenNodes = _this$state10.flattenNodes;\n      if (activeKey === null) {\n        return null;\n      }\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    };\n    _this.offsetActiveKey = function (offset) {\n      var _this$state11 = _this.state,\n        flattenNodes = _this$state11.flattenNodes,\n        activeKey = _this$state11.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      }); // Align with index\n\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n      if (item) {\n        var key = item.key;\n        _this.onActiveChange(key);\n      } else {\n        _this.onActiveChange(null);\n      }\n    };\n    _this.onKeyDown = function (event) {\n      var _this$state12 = _this.state,\n        activeKey = _this$state12.activeKey,\n        expandedKeys = _this$state12.expandedKeys,\n        checkedKeys = _this$state12.checkedKeys;\n      var _this$props7 = _this.props,\n        onKeyDown = _this$props7.onKeyDown,\n        checkable = _this$props7.checkable,\n        selectable = _this$props7.selectable; // >>>>>>>>>> Direction\n\n      switch (event.which) {\n        case KeyCode.UP:\n          {\n            _this.offsetActiveKey(-1);\n            event.preventDefault();\n            break;\n          }\n        case KeyCode.DOWN:\n          {\n            _this.offsetActiveKey(1);\n            event.preventDefault();\n            break;\n          }\n      } // >>>>>>>>>> Expand & Selection\n\n      var activeItem = _this.getActiveItem();\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data.children || []).length;\n        var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n        switch (event.which) {\n          // >>> Expand\n          case KeyCode.LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.data.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].data.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          // Selection\n\n          case KeyCode.ENTER:\n          case KeyCode.SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n              break;\n            }\n        }\n      }\n      onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(event);\n    };\n    _this.setUncontrolledState = function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (name in _this.props) {\n            allPassed = false;\n            return;\n          }\n          needSync = true;\n          newState[name] = state[name];\n        });\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState(_objectSpread(_objectSpread({}, newState), forceState));\n        }\n      }\n    };\n    _this.scrollTo = function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    };\n    return _this;\n  }\n  _createClass(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var activeKey = this.props.activeKey;\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n      var _this$state13 = this.state,\n        focused = _this$state13.focused,\n        flattenNodes = _this$state13.flattenNodes,\n        keyEntities = _this$state13.keyEntities,\n        draggingNodeKey = _this$state13.draggingNodeKey,\n        activeKey = _this$state13.activeKey,\n        dropLevelOffset = _this$state13.dropLevelOffset,\n        dropContainerKey = _this$state13.dropContainerKey,\n        dropTargetKey = _this$state13.dropTargetKey,\n        dropPosition = _this$state13.dropPosition,\n        dragOverNodeKey = _this$state13.dragOverNodeKey,\n        indent = _this$state13.indent;\n      var _this$props8 = this.props,\n        prefixCls = _this$props8.prefixCls,\n        className = _this$props8.className,\n        style = _this$props8.style,\n        showLine = _this$props8.showLine,\n        focusable = _this$props8.focusable,\n        _this$props8$tabIndex = _this$props8.tabIndex,\n        tabIndex = _this$props8$tabIndex === void 0 ? 0 : _this$props8$tabIndex,\n        selectable = _this$props8.selectable,\n        showIcon = _this$props8.showIcon,\n        icon = _this$props8.icon,\n        switcherIcon = _this$props8.switcherIcon,\n        draggable = _this$props8.draggable,\n        checkable = _this$props8.checkable,\n        checkStrictly = _this$props8.checkStrictly,\n        disabled = _this$props8.disabled,\n        motion = _this$props8.motion,\n        loadData = _this$props8.loadData,\n        filterTreeNode = _this$props8.filterTreeNode,\n        height = _this$props8.height,\n        itemHeight = _this$props8.itemHeight,\n        virtual = _this$props8.virtual,\n        titleRender = _this$props8.titleRender,\n        dropIndicatorRender = _this$props8.dropIndicatorRender,\n        onContextMenu = _this$props8.onContextMenu,\n        onScroll = _this$props8.onScroll,\n        direction = _this$props8.direction,\n        rootClassName = _this$props8.rootClassName,\n        rootStyle = _this$props8.rootStyle;\n      var domProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      }); // It's better move to hooks but we just simply keep here\n\n      var draggableConfig;\n      if (draggable) {\n        if (_typeof(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n      return /*#__PURE__*/React.createElement(TreeContext.Provider, {\n        value: {\n          prefixCls: prefixCls,\n          selectable: selectable,\n          showIcon: showIcon,\n          icon: icon,\n          switcherIcon: switcherIcon,\n          draggable: draggableConfig,\n          draggingNodeKey: draggingNodeKey,\n          checkable: checkable,\n          checkStrictly: checkStrictly,\n          disabled: disabled,\n          keyEntities: keyEntities,\n          dropLevelOffset: dropLevelOffset,\n          dropContainerKey: dropContainerKey,\n          dropTargetKey: dropTargetKey,\n          dropPosition: dropPosition,\n          dragOverNodeKey: dragOverNodeKey,\n          indent: indent,\n          direction: direction,\n          dropIndicatorRender: dropIndicatorRender,\n          loadData: loadData,\n          filterTreeNode: filterTreeNode,\n          titleRender: titleRender,\n          onNodeClick: this.onNodeClick,\n          onNodeDoubleClick: this.onNodeDoubleClick,\n          onNodeExpand: this.onNodeExpand,\n          onNodeSelect: this.onNodeSelect,\n          onNodeCheck: this.onNodeCheck,\n          onNodeLoad: this.onNodeLoad,\n          onNodeMouseEnter: this.onNodeMouseEnter,\n          onNodeMouseLeave: this.onNodeMouseLeave,\n          onNodeContextMenu: this.onNodeContextMenu,\n          onNodeDragStart: this.onNodeDragStart,\n          onNodeDragEnter: this.onNodeDragEnter,\n          onNodeDragOver: this.onNodeDragOver,\n          onNodeDragLeave: this.onNodeDragLeave,\n          onNodeDragEnd: this.onNodeDragEnd,\n          onNodeDrop: this.onNodeDrop\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        role: \"tree\",\n        className: classNames(prefixCls, className, rootClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-show-line\"), showLine), _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focused), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null), _classNames)),\n        style: rootStyle\n      }, /*#__PURE__*/React.createElement(NodeList, _extends({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      function needSync(name) {\n        return !prevProps && name in props || prevProps && prevProps[name] !== props[name];\n      } // ================== Tree Node ==================\n\n      var treeData; // fieldNames\n\n      var fieldNames = prevState.fieldNames;\n      if (needSync('fieldNames')) {\n        fieldNames = fillFieldNames(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      } // Check if `treeData` or `children` changed and save into the state.\n\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        warning(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = convertTreeToData(props.children);\n      } // Save flatten nodes info and convert `treeData` into keyEntities\n\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = convertDataToEntities(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = _objectSpread(_defineProperty({}, MOTION_KEY, MotionEntity), entitiesMap.keyEntities); // Warning if treeNode not provide key\n\n        if (process.env.NODE_ENV !== 'production') {\n          warningWithoutKey(treeData, fieldNames);\n        }\n      }\n      var keyEntities = newState.keyEntities || prevState.keyEntities; // ================ expandedKeys =================\n\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = _objectSpread({}, keyEntities);\n        delete cloneKeyEntities[MOTION_KEY];\n        newState.expandedKeys = Object.keys(cloneKeyEntities).map(function (key) {\n          return cloneKeyEntities[key].key;\n        });\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      } // ================ flattenNodes =================\n\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = flattenTreeData(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      } // ================ selectedKeys =================\n\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = calcSelectedKeys(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = calcSelectedKeys(props.defaultSelectedKeys, props);\n        }\n      } // ================= checkedKeys =================\n\n      if (props.checkable) {\n        var checkedKeyEntity;\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n            _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n            checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n            _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n            halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n          if (!props.checkStrictly) {\n            var conductKeys = conductCheck(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      } // ================= loadedKeys ==================\n\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n      return newState;\n    }\n  }]);\n  return Tree;\n}(React.Component);\nTree.defaultProps = {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: DropIndicator,\n  allowDrop: function allowDrop() {\n    return true;\n  }\n};\nTree.TreeNode = TreeNode;\nexport default Tree;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_objectSpread", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "React", "KeyCode", "warning", "pickAttrs", "classNames", "TreeContext", "getDrag<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseCheckedKeys", "conductExpandParent", "calcSelectedKeys", "calcDropPosition", "arrAdd", "arr<PERSON><PERSON>", "posToArr", "flattenTreeData", "convertTreeToData", "convertDataToEntities", "warningWithoutKey", "convertNodePropsToEventData", "getTreeNodeProps", "fillFieldNames", "NodeList", "MOTION_KEY", "MotionEntity", "TreeNode", "conduct<PERSON>heck", "DropIndicator", "MAX_RETRY_TIMES", "Tree", "_React$Component", "_super", "_this", "_len", "arguments", "length", "_args", "Array", "_key", "call", "apply", "concat", "destroyed", "delayedDragEnterLogic", "loadingRetryTimes", "state", "keyEntities", "indent", "<PERSON><PERSON><PERSON><PERSON>", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "loadedKeys", "loadingKeys", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dropTargetKey", "dropPosition", "dropContainerKey", "dropLevelOffset", "dropTargetPos", "dropAllowed", "dragOverNodeKey", "treeData", "flattenNodes", "focused", "active<PERSON><PERSON>", "listChanging", "prevProps", "fieldNames", "dragStartMousePosition", "dragNode", "currentMouseOverDroppableNodeKey", "listRef", "createRef", "onNodeDragStart", "event", "node", "_this$state", "onDragStart", "props", "eventKey", "x", "clientX", "y", "clientY", "newExpandedKeys", "setState", "current", "getIndentWidth", "setExpandedKeys", "window", "addEventListener", "onWindowDragEnd", "onNodeDragEnter", "_this$state2", "_this$props", "onDragEnter", "onExpand", "allowDrop", "direction", "_node$props", "pos", "_assertThisInitialize", "resetDragState", "_calcDropPosition", "indexOf", "Object", "keys", "for<PERSON>ach", "key", "clearTimeout", "persist", "setTimeout", "entity", "children", "expanded", "nativeEvent", "onNodeDragOver", "_this$state3", "_this$props2", "onDragOver", "_assertThisInitialize2", "_calcDropPosition2", "onNodeDragLeave", "currentTarget", "contains", "relatedTarget", "onDragLeave", "onNodeDragEnd", "removeEventListener", "onDragEnd", "cleanDragState", "onNodeDrop", "_this$getActiveItem", "outsideTree", "undefined", "_this$state4", "onDrop", "abstractDropNodeProps", "getTreeNodeRequiredProps", "active", "getActiveItem", "data", "drop<PERSON>oChild", "posArr", "dropResult", "dragNodesKeys", "dropToGap", "Number", "onNodeClick", "e", "treeNode", "onClick", "onNodeDoubleClick", "onDoubleClick", "onNodeSelect", "_this$state5", "_this$props3", "onSelect", "multiple", "selected", "targetSelected", "selectedNodes", "map", "<PERSON><PERSON><PERSON>", "filter", "setUncontrolledState", "onNodeCheck", "checked", "_this$state6", "oriCheckedKeys", "oriHalfCheckedKeys", "_this$props4", "checkStrictly", "onCheck", "checked<PERSON>bj", "eventObj", "halfChecked", "checkedNodes", "<PERSON><PERSON><PERSON>", "_conductCheck", "_checked<PERSON><PERSON><PERSON>", "_half<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keySet", "Set", "delete", "_conductCheck2", "from", "checkedNodesPositions", "push", "onNodeLoad", "loadPromise", "Promise", "resolve", "reject", "_ref", "_ref$loadedKeys", "_ref$loadingKeys", "_this$props5", "loadData", "onLoad", "promise", "then", "currentLoaded<PERSON><PERSON><PERSON>", "new<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prevState", "catch", "onNodeMouseEnter", "onMouseEnter", "onNodeMouseLeave", "onMouseLeave", "onNodeContextMenu", "onRightClick", "preventDefault", "onFocus", "_len2", "args", "_key2", "onBlur", "onActiveChange", "_len3", "_key3", "_this$state7", "_this$state8", "onNodeExpand", "_this$state9", "_this$props6", "index", "targetExpanded", "newFlattenTreeData", "currentExpandedKeys", "expandedKeysToRestore", "onListChangeStart", "onListChangeEnd", "newActiveKey", "scrollTo", "_this$state10", "find", "_ref2", "offsetActiveKey", "offset", "_this$state11", "findIndex", "_ref3", "item", "onKeyDown", "_this$state12", "_this$props7", "checkable", "selectable", "which", "UP", "DOWN", "activeItem", "treeNodeRequiredProps", "expandable", "<PERSON><PERSON><PERSON><PERSON>", "eventNode", "LEFT", "includes", "parent", "RIGHT", "ENTER", "SPACE", "disabled", "disableCheckbox", "atomic", "forceState", "needSync", "allPassed", "newState", "name", "scroll", "value", "componentDidMount", "onUpdated", "componentDidUpdate", "componentWillUnmount", "render", "_classNames", "_this$state13", "_this$props8", "prefixCls", "className", "style", "showLine", "focusable", "_this$props8$tabIndex", "tabIndex", "showIcon", "icon", "switcherIcon", "draggable", "motion", "filterTreeNode", "height", "itemHeight", "virtual", "titleRender", "dropIndicatorRender", "onContextMenu", "onScroll", "rootClassName", "rootStyle", "domProps", "aria", "draggableConfig", "nodeDraggable", "createElement", "Provider", "role", "ref", "dragging", "getDerivedStateFromProps", "entitiesMap", "process", "env", "NODE_ENV", "autoExpandParent", "defaultExpandParent", "defaultExpandAll", "cloneKeyEntities", "defaultExpandedKeys", "defaultSelectedKeys", "checkedKeyEntity", "defaultCheckedKeys", "_checkedKeyEntity", "_checkedKeyEntity$che", "_checkedKeyEntity$hal", "conductKeys", "Component", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree/es/Tree.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport warning from \"rc-util/es/warning\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport classNames from 'classnames';\nimport { TreeContext } from './contextTypes';\nimport { getDragChildrenKeys, parseCheckedKeys, conductExpandParent, calcSelectedKeys, calcDropPosition, arrAdd, arrDel, posToArr } from './util';\nimport { flattenTreeData, convertTreeToData, convertDataToEntities, warningWithoutKey, convertNodePropsToEventData, getTreeNodeProps, fillFieldNames } from './utils/treeUtil';\nimport NodeList, { MOTION_KEY, MotionEntity } from './NodeList';\nimport TreeNode from './TreeNode';\nimport { conductCheck } from './utils/conductUtil';\nimport DropIndicator from './DropIndicator';\nvar MAX_RETRY_TIMES = 10;\n\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  _inherits(Tree, _React$Component);\n\n  var _super = _createSuper(Tree);\n\n  function Tree() {\n    var _this;\n\n    _classCallCheck(this, Tree);\n\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(_args));\n    _this.destroyed = false;\n    _this.delayedDragEnterLogic = void 0;\n    _this.loadingRetryTimes = {};\n    _this.state = {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      dropContainerKey: null,\n      dropLevelOffset: null,\n      dropTargetPos: null,\n      dropAllowed: true,\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: fillFieldNames()\n    };\n    _this.dragStartMousePosition = null;\n    _this.dragNode = void 0;\n    _this.currentMouseOverDroppableNodeKey = null;\n    _this.listRef = /*#__PURE__*/React.createRef();\n\n    _this.onNodeDragStart = function (event, node) {\n      var _this$state = _this.state,\n          expandedKeys = _this$state.expandedKeys,\n          keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = node.props.eventKey;\n      _this.dragNode = node;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = arrDel(expandedKeys, eventKey);\n\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: getDragChildrenKeys(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n\n      _this.setExpandedKeys(newExpandedKeys);\n\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 ? void 0 : onDragStart({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n\n    _this.onNodeDragEnter = function (event, node) {\n      var _this$state2 = _this.state,\n          expandedKeys = _this$state2.expandedKeys,\n          keyEntities = _this$state2.keyEntities,\n          dragChildrenKeys = _this$state2.dragChildrenKeys,\n          flattenNodes = _this$state2.flattenNodes,\n          indent = _this$state2.indent;\n      var _this$props = _this.props,\n          onDragEnter = _this$props.onDragEnter,\n          onExpand = _this$props.onExpand,\n          allowDrop = _this$props.allowDrop,\n          direction = _this$props.direction;\n      var _node$props = node.props,\n          pos = _node$props.pos,\n          eventKey = _node$props.eventKey;\n\n      var _assertThisInitialize = _assertThisInitialized(_this),\n          dragNode = _assertThisInitialize.dragNode; // record the key of node which is latest entered, used in dragleave event.\n\n\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n\n      if (!dragNode) {\n        _this.resetDragState();\n\n        return;\n      }\n\n      var _calcDropPosition = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n          dropPosition = _calcDropPosition.dropPosition,\n          dropLevelOffset = _calcDropPosition.dropLevelOffset,\n          dropTargetKey = _calcDropPosition.dropTargetKey,\n          dropContainerKey = _calcDropPosition.dropContainerKey,\n          dropTargetPos = _calcDropPosition.dropTargetPos,\n          dropAllowed = _calcDropPosition.dropAllowed,\n          dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n\n      if ( // don't allow drop inside its children\n      dragChildrenKeys.indexOf(dropTargetKey) !== -1 || // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n\n        return;\n      } // Side effect for delay drag\n\n\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n\n      if (dragNode.props.eventKey !== node.props.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) return;\n\n          var newExpandedKeys = _toConsumableArray(expandedKeys);\n\n          var entity = keyEntities[node.props.eventKey];\n\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = arrAdd(expandedKeys, node.props.eventKey);\n          }\n\n          if (!('expandedKeys' in _this.props)) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n\n          onExpand === null || onExpand === void 0 ? void 0 : onExpand(newExpandedKeys, {\n            node: convertNodePropsToEventData(node.props),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      } // Skip if drag node is self\n\n\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n\n        return;\n      } // Update drag over node and drag state\n\n\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n\n      onDragEnter === null || onDragEnter === void 0 ? void 0 : onDragEnter({\n        event: event,\n        node: convertNodePropsToEventData(node.props),\n        expandedKeys: expandedKeys\n      });\n    };\n\n    _this.onNodeDragOver = function (event, node) {\n      var _this$state3 = _this.state,\n          dragChildrenKeys = _this$state3.dragChildrenKeys,\n          flattenNodes = _this$state3.flattenNodes,\n          keyEntities = _this$state3.keyEntities,\n          expandedKeys = _this$state3.expandedKeys,\n          indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n          onDragOver = _this$props2.onDragOver,\n          allowDrop = _this$props2.allowDrop,\n          direction = _this$props2.direction;\n\n      var _assertThisInitialize2 = _assertThisInitialized(_this),\n          dragNode = _assertThisInitialize2.dragNode;\n\n      if (!dragNode) {\n        return;\n      }\n\n      var _calcDropPosition2 = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n          dropPosition = _calcDropPosition2.dropPosition,\n          dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n          dropTargetKey = _calcDropPosition2.dropTargetKey,\n          dropContainerKey = _calcDropPosition2.dropContainerKey,\n          dropAllowed = _calcDropPosition2.dropAllowed,\n          dropTargetPos = _calcDropPosition2.dropTargetPos,\n          dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n\n      if (dragChildrenKeys.indexOf(dropTargetKey) !== -1 || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed caculated by calcDropPosition\n        return;\n      } // Update drag position\n\n\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n\n      onDragOver === null || onDragOver === void 0 ? void 0 : onDragOver({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n\n    _this.onNodeDragLeave = function (event, node) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === node.props.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 ? void 0 : onDragLeave({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n\n    _this.onWindowDragEnd = function (event) {\n      _this.onNodeDragEnd(event, null, true);\n\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    };\n\n    _this.onNodeDragEnd = function (event, node) {\n      var onDragEnd = _this.props.onDragEnd;\n\n      _this.setState({\n        dragOverNodeKey: null\n      });\n\n      _this.cleanDragState();\n\n      onDragEnd === null || onDragEnd === void 0 ? void 0 : onDragEnd({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n      _this.dragNode = null;\n    };\n\n    _this.onNodeDrop = function (event, node) {\n      var _this$getActiveItem;\n\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n          dragChildrenKeys = _this$state4.dragChildrenKeys,\n          dropPosition = _this$state4.dropPosition,\n          dropTargetKey = _this$state4.dropTargetKey,\n          dropTargetPos = _this$state4.dropTargetPos,\n          dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) return;\n      var onDrop = _this.props.onDrop;\n\n      _this.setState({\n        dragOverNodeKey: null\n      });\n\n      _this.cleanDragState();\n\n      if (dropTargetKey === null) return;\n\n      var abstractDropNodeProps = _objectSpread(_objectSpread({}, getTreeNodeProps(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.data.key) === dropTargetKey,\n        data: _this.state.keyEntities[dropTargetKey].node\n      });\n\n      var dropToChild = dragChildrenKeys.indexOf(dropTargetKey) !== -1;\n      warning(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = posToArr(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: convertNodePropsToEventData(abstractDropNodeProps),\n        dragNode: _this.dragNode ? convertNodePropsToEventData(_this.dragNode.props) : null,\n        dragNodesKeys: [_this.dragNode.props.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 ? void 0 : onDrop(dropResult);\n      }\n\n      _this.dragNode = null;\n    };\n\n    _this.cleanDragState = function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    };\n\n    _this.onNodeClick = function (e, treeNode) {\n      var onClick = _this.props.onClick;\n      onClick === null || onClick === void 0 ? void 0 : onClick(e, treeNode);\n    };\n\n    _this.onNodeDoubleClick = function (e, treeNode) {\n      var onDoubleClick = _this.props.onDoubleClick;\n      onDoubleClick === null || onDoubleClick === void 0 ? void 0 : onDoubleClick(e, treeNode);\n    };\n\n    _this.onNodeSelect = function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state5 = _this.state,\n          keyEntities = _this$state5.keyEntities,\n          fieldNames = _this$state5.fieldNames;\n      var _this$props3 = _this.props,\n          onSelect = _this$props3.onSelect,\n          multiple = _this$props3.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected; // Update selected keys\n\n      if (!targetSelected) {\n        selectedKeys = arrDel(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = arrAdd(selectedKeys, key);\n      } // [Legacy] Not found related usage in doc or upper libs\n\n\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = keyEntities[selectedKey];\n        if (!entity) return null;\n        return entity.node;\n      }).filter(function (node) {\n        return node;\n      });\n\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    };\n\n    _this.onNodeCheck = function (e, treeNode, checked) {\n      var _this$state6 = _this.state,\n          keyEntities = _this$state6.keyEntities,\n          oriCheckedKeys = _this$state6.checkedKeys,\n          oriHalfCheckedKeys = _this$state6.halfCheckedKeys;\n      var _this$props4 = _this.props,\n          checkStrictly = _this$props4.checkStrictly,\n          onCheck = _this$props4.onCheck;\n      var key = treeNode.key; // Prepare trigger arguments\n\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n\n      if (checkStrictly) {\n        var checkedKeys = checked ? arrAdd(oriCheckedKeys, key) : arrDel(oriCheckedKeys, key);\n        var halfCheckedKeys = arrDel(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return keyEntities[checkedKey];\n        }).filter(function (entity) {\n          return entity;\n        }).map(function (entity) {\n          return entity.node;\n        });\n\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = conductCheck([].concat(_toConsumableArray(oriCheckedKeys), [key]), true, keyEntities),\n            _checkedKeys = _conductCheck.checkedKeys,\n            _halfCheckedKeys = _conductCheck.halfCheckedKeys; // If remove, we do it again to correction\n\n\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n\n          var _conductCheck2 = conductCheck(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n\n        checkedObj = _checkedKeys; // [Legacy] This is used for `rc-tree-select`\n\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = keyEntities[checkedKey];\n          if (!entity) return;\n          var node = entity.node,\n              pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n\n      onCheck === null || onCheck === void 0 ? void 0 : onCheck(checkedObj, eventObj);\n    };\n\n    _this.onNodeLoad = function (treeNode) {\n      var key = treeNode.key;\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n              loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n              _ref$loadingKeys = _ref.loadingKeys,\n              loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props5 = _this.props,\n              loadData = _this$props5.loadData,\n              onLoad = _this$props5.onLoad;\n\n          if (!loadData || loadedKeys.indexOf(key) !== -1 || loadingKeys.indexOf(key) !== -1) {\n            return null;\n          } // Process load data\n\n\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = arrAdd(currentLoadedKeys, key); // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n\n            onLoad === null || onLoad === void 0 ? void 0 : onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            }); // If exceed max retry times, we give up retry\n\n\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              warning(false, 'Retry for `loadData` many times but still failed. No more retry.');\n\n              _this.setUncontrolledState({\n                loadedKeys: arrAdd(currentLoadedKeys, key)\n              });\n\n              resolve();\n            }\n\n            reject(e);\n          });\n          return {\n            loadingKeys: arrAdd(loadingKeys, key)\n          };\n        });\n      }); // Not care warning if we ignore this\n\n      loadPromise.catch(function () {});\n      return loadPromise;\n    };\n\n    _this.onNodeMouseEnter = function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n        event: event,\n        node: node\n      });\n    };\n\n    _this.onNodeMouseLeave = function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n        event: event,\n        node: node\n      });\n    };\n\n    _this.onNodeContextMenu = function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    };\n\n    _this.onFocus = function () {\n      var onFocus = _this.props.onFocus;\n\n      _this.setState({\n        focused: true\n      });\n\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus.apply(void 0, args);\n    };\n\n    _this.onBlur = function () {\n      var onBlur = _this.props.onBlur;\n\n      _this.setState({\n        focused: false\n      });\n\n      _this.onActiveChange(null);\n\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur.apply(void 0, args);\n    };\n\n    _this.getTreeNodeRequiredProps = function () {\n      var _this$state7 = _this.state,\n          expandedKeys = _this$state7.expandedKeys,\n          selectedKeys = _this$state7.selectedKeys,\n          loadedKeys = _this$state7.loadedKeys,\n          loadingKeys = _this$state7.loadingKeys,\n          checkedKeys = _this$state7.checkedKeys,\n          halfCheckedKeys = _this$state7.halfCheckedKeys,\n          dragOverNodeKey = _this$state7.dragOverNodeKey,\n          dropPosition = _this$state7.dropPosition,\n          keyEntities = _this$state7.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    };\n\n    _this.setExpandedKeys = function (expandedKeys) {\n      var _this$state8 = _this.state,\n          treeData = _this$state8.treeData,\n          fieldNames = _this$state8.fieldNames;\n      var flattenNodes = flattenTreeData(treeData, expandedKeys, fieldNames);\n\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    };\n\n    _this.onNodeExpand = function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state9 = _this.state,\n          listChanging = _this$state9.listChanging,\n          fieldNames = _this$state9.fieldNames;\n      var _this$props6 = _this.props,\n          onExpand = _this$props6.onExpand,\n          loadData = _this$props6.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key]; // Do nothing when motion is in progress\n\n      if (listChanging) {\n        return;\n      } // Update selected keys\n\n\n      var index = expandedKeys.indexOf(key);\n      var targetExpanded = !expanded;\n      warning(expanded && index !== -1 || !expanded && index === -1, 'Expand state not sync with index check');\n\n      if (targetExpanded) {\n        expandedKeys = arrAdd(expandedKeys, key);\n      } else {\n        expandedKeys = arrDel(expandedKeys, key);\n      }\n\n      _this.setExpandedKeys(expandedKeys);\n\n      onExpand === null || onExpand === void 0 ? void 0 : onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      }); // Async Load data\n\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = flattenTreeData(_this.state.treeData, expandedKeys, fieldNames);\n\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = arrDel(currentExpandedKeys, key);\n\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    };\n\n    _this.onListChangeStart = function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    };\n\n    _this.onListChangeEnd = function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    };\n\n    _this.onActiveChange = function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var onActiveChange = _this.props.onActiveChange;\n\n      if (activeKey === newActiveKey) {\n        return;\n      }\n\n      _this.setState({\n        activeKey: newActiveKey\n      });\n\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey\n        });\n      }\n\n      onActiveChange === null || onActiveChange === void 0 ? void 0 : onActiveChange(newActiveKey);\n    };\n\n    _this.getActiveItem = function () {\n      var _this$state10 = _this.state,\n          activeKey = _this$state10.activeKey,\n          flattenNodes = _this$state10.flattenNodes;\n\n      if (activeKey === null) {\n        return null;\n      }\n\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    };\n\n    _this.offsetActiveKey = function (offset) {\n      var _this$state11 = _this.state,\n          flattenNodes = _this$state11.flattenNodes,\n          activeKey = _this$state11.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      }); // Align with index\n\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n\n      if (item) {\n        var key = item.key;\n\n        _this.onActiveChange(key);\n      } else {\n        _this.onActiveChange(null);\n      }\n    };\n\n    _this.onKeyDown = function (event) {\n      var _this$state12 = _this.state,\n          activeKey = _this$state12.activeKey,\n          expandedKeys = _this$state12.expandedKeys,\n          checkedKeys = _this$state12.checkedKeys;\n      var _this$props7 = _this.props,\n          onKeyDown = _this$props7.onKeyDown,\n          checkable = _this$props7.checkable,\n          selectable = _this$props7.selectable; // >>>>>>>>>> Direction\n\n      switch (event.which) {\n        case KeyCode.UP:\n          {\n            _this.offsetActiveKey(-1);\n\n            event.preventDefault();\n            break;\n          }\n\n        case KeyCode.DOWN:\n          {\n            _this.offsetActiveKey(1);\n\n            event.preventDefault();\n            break;\n          }\n      } // >>>>>>>>>> Expand & Selection\n\n\n      var activeItem = _this.getActiveItem();\n\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data.children || []).length;\n        var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n\n        switch (event.which) {\n          // >>> Expand\n          case KeyCode.LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.data.key);\n              }\n\n              event.preventDefault();\n              break;\n            }\n\n          case KeyCode.RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].data.key);\n              }\n\n              event.preventDefault();\n              break;\n            }\n          // Selection\n\n          case KeyCode.ENTER:\n          case KeyCode.SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n\n              break;\n            }\n        }\n      }\n\n      onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(event);\n    };\n\n    _this.setUncontrolledState = function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (name in _this.props) {\n            allPassed = false;\n            return;\n          }\n\n          needSync = true;\n          newState[name] = state[name];\n        });\n\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState(_objectSpread(_objectSpread({}, newState), forceState));\n        }\n      }\n    };\n\n    _this.scrollTo = function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    };\n\n    return _this;\n  }\n\n  _createClass(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var activeKey = this.props.activeKey;\n\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n\n      var _this$state13 = this.state,\n          focused = _this$state13.focused,\n          flattenNodes = _this$state13.flattenNodes,\n          keyEntities = _this$state13.keyEntities,\n          draggingNodeKey = _this$state13.draggingNodeKey,\n          activeKey = _this$state13.activeKey,\n          dropLevelOffset = _this$state13.dropLevelOffset,\n          dropContainerKey = _this$state13.dropContainerKey,\n          dropTargetKey = _this$state13.dropTargetKey,\n          dropPosition = _this$state13.dropPosition,\n          dragOverNodeKey = _this$state13.dragOverNodeKey,\n          indent = _this$state13.indent;\n      var _this$props8 = this.props,\n          prefixCls = _this$props8.prefixCls,\n          className = _this$props8.className,\n          style = _this$props8.style,\n          showLine = _this$props8.showLine,\n          focusable = _this$props8.focusable,\n          _this$props8$tabIndex = _this$props8.tabIndex,\n          tabIndex = _this$props8$tabIndex === void 0 ? 0 : _this$props8$tabIndex,\n          selectable = _this$props8.selectable,\n          showIcon = _this$props8.showIcon,\n          icon = _this$props8.icon,\n          switcherIcon = _this$props8.switcherIcon,\n          draggable = _this$props8.draggable,\n          checkable = _this$props8.checkable,\n          checkStrictly = _this$props8.checkStrictly,\n          disabled = _this$props8.disabled,\n          motion = _this$props8.motion,\n          loadData = _this$props8.loadData,\n          filterTreeNode = _this$props8.filterTreeNode,\n          height = _this$props8.height,\n          itemHeight = _this$props8.itemHeight,\n          virtual = _this$props8.virtual,\n          titleRender = _this$props8.titleRender,\n          dropIndicatorRender = _this$props8.dropIndicatorRender,\n          onContextMenu = _this$props8.onContextMenu,\n          onScroll = _this$props8.onScroll,\n          direction = _this$props8.direction,\n          rootClassName = _this$props8.rootClassName,\n          rootStyle = _this$props8.rootStyle;\n      var domProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      }); // It's better move to hooks but we just simply keep here\n\n      var draggableConfig;\n\n      if (draggable) {\n        if (_typeof(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n\n      return /*#__PURE__*/React.createElement(TreeContext.Provider, {\n        value: {\n          prefixCls: prefixCls,\n          selectable: selectable,\n          showIcon: showIcon,\n          icon: icon,\n          switcherIcon: switcherIcon,\n          draggable: draggableConfig,\n          draggingNodeKey: draggingNodeKey,\n          checkable: checkable,\n          checkStrictly: checkStrictly,\n          disabled: disabled,\n          keyEntities: keyEntities,\n          dropLevelOffset: dropLevelOffset,\n          dropContainerKey: dropContainerKey,\n          dropTargetKey: dropTargetKey,\n          dropPosition: dropPosition,\n          dragOverNodeKey: dragOverNodeKey,\n          indent: indent,\n          direction: direction,\n          dropIndicatorRender: dropIndicatorRender,\n          loadData: loadData,\n          filterTreeNode: filterTreeNode,\n          titleRender: titleRender,\n          onNodeClick: this.onNodeClick,\n          onNodeDoubleClick: this.onNodeDoubleClick,\n          onNodeExpand: this.onNodeExpand,\n          onNodeSelect: this.onNodeSelect,\n          onNodeCheck: this.onNodeCheck,\n          onNodeLoad: this.onNodeLoad,\n          onNodeMouseEnter: this.onNodeMouseEnter,\n          onNodeMouseLeave: this.onNodeMouseLeave,\n          onNodeContextMenu: this.onNodeContextMenu,\n          onNodeDragStart: this.onNodeDragStart,\n          onNodeDragEnter: this.onNodeDragEnter,\n          onNodeDragOver: this.onNodeDragOver,\n          onNodeDragLeave: this.onNodeDragLeave,\n          onNodeDragEnd: this.onNodeDragEnd,\n          onNodeDrop: this.onNodeDrop\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        role: \"tree\",\n        className: classNames(prefixCls, className, rootClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-show-line\"), showLine), _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focused), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null), _classNames)),\n        style: rootStyle\n      }, /*#__PURE__*/React.createElement(NodeList, _extends({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n\n      function needSync(name) {\n        return !prevProps && name in props || prevProps && prevProps[name] !== props[name];\n      } // ================== Tree Node ==================\n\n\n      var treeData; // fieldNames\n\n      var fieldNames = prevState.fieldNames;\n\n      if (needSync('fieldNames')) {\n        fieldNames = fillFieldNames(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      } // Check if `treeData` or `children` changed and save into the state.\n\n\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        warning(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = convertTreeToData(props.children);\n      } // Save flatten nodes info and convert `treeData` into keyEntities\n\n\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = convertDataToEntities(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = _objectSpread(_defineProperty({}, MOTION_KEY, MotionEntity), entitiesMap.keyEntities); // Warning if treeNode not provide key\n\n        if (process.env.NODE_ENV !== 'production') {\n          warningWithoutKey(treeData, fieldNames);\n        }\n      }\n\n      var keyEntities = newState.keyEntities || prevState.keyEntities; // ================ expandedKeys =================\n\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = _objectSpread({}, keyEntities);\n\n        delete cloneKeyEntities[MOTION_KEY];\n        newState.expandedKeys = Object.keys(cloneKeyEntities).map(function (key) {\n          return cloneKeyEntities[key].key;\n        });\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      } // ================ flattenNodes =================\n\n\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = flattenTreeData(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      } // ================ selectedKeys =================\n\n\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = calcSelectedKeys(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = calcSelectedKeys(props.defaultSelectedKeys, props);\n        }\n      } // ================= checkedKeys =================\n\n\n      if (props.checkable) {\n        var checkedKeyEntity;\n\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n              _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n              checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n              _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n              halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n\n          if (!props.checkStrictly) {\n            var conductKeys = conductCheck(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      } // ================= loadedKeys ==================\n\n\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n\n      return newState;\n    }\n  }]);\n\n  return Tree;\n}(React.Component);\n\nTree.defaultProps = {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: DropIndicator,\n  allowDrop: function allowDrop() {\n    return true;\n  }\n};\nTree.TreeNode = TreeNode;\nexport default Tree;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAQ;AACjJ,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,2BAA2B,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,kBAAkB;AAC9K,OAAOC,QAAQ,IAAIC,UAAU,EAAEC,YAAY,QAAQ,YAAY;AAC/D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,eAAe,GAAG,EAAE;AAExB,IAAIC,IAAI,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAClD/B,SAAS,CAAC8B,IAAI,EAAEC,gBAAgB,CAAC;EAEjC,IAAIC,MAAM,GAAG/B,YAAY,CAAC6B,IAAI,CAAC;EAE/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IAETpC,eAAe,CAAC,IAAI,EAAEiC,IAAI,CAAC;IAE3B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACxFF,KAAK,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC/B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,KAAK,CAAC,CAAC;IACvDJ,KAAK,CAACU,SAAS,GAAG,KAAK;IACvBV,KAAK,CAACW,qBAAqB,GAAG,KAAK,CAAC;IACpCX,KAAK,CAACY,iBAAiB,GAAG,CAAC,CAAC;IAC5BZ,KAAK,CAACa,KAAK,GAAG;MACZC,WAAW,EAAE,CAAC,CAAC;MACfC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,EAAE;MACpB;MACA;MACA;MACAC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE,IAAI;MACjB;MACA;MACA;MACAC,eAAe,EAAE,IAAI;MACrBC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAEhD,cAAc,CAAC;IAC7B,CAAC;IACDW,KAAK,CAACsC,sBAAsB,GAAG,IAAI;IACnCtC,KAAK,CAACuC,QAAQ,GAAG,KAAK,CAAC;IACvBvC,KAAK,CAACwC,gCAAgC,GAAG,IAAI;IAC7CxC,KAAK,CAACyC,OAAO,GAAG,aAAaxE,KAAK,CAACyE,SAAS,CAAC,CAAC;IAE9C1C,KAAK,CAAC2C,eAAe,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;MAC7C,IAAIC,WAAW,GAAG9C,KAAK,CAACa,KAAK;QACzBQ,YAAY,GAAGyB,WAAW,CAACzB,YAAY;QACvCP,WAAW,GAAGgC,WAAW,CAAChC,WAAW;MACzC,IAAIiC,WAAW,GAAG/C,KAAK,CAACgD,KAAK,CAACD,WAAW;MACzC,IAAIE,QAAQ,GAAGJ,IAAI,CAACG,KAAK,CAACC,QAAQ;MAClCjD,KAAK,CAACuC,QAAQ,GAAGM,IAAI;MACrB7C,KAAK,CAACsC,sBAAsB,GAAG;QAC7BY,CAAC,EAAEN,KAAK,CAACO,OAAO;QAChBC,CAAC,EAAER,KAAK,CAACS;MACX,CAAC;MACD,IAAIC,eAAe,GAAGzE,MAAM,CAACwC,YAAY,EAAE4B,QAAQ,CAAC;MAEpDjD,KAAK,CAACuD,QAAQ,CAAC;QACbjC,eAAe,EAAE2B,QAAQ;QACzB1B,gBAAgB,EAAEhD,mBAAmB,CAAC0E,QAAQ,EAAEnC,WAAW,CAAC;QAC5DC,MAAM,EAAEf,KAAK,CAACyC,OAAO,CAACe,OAAO,CAACC,cAAc,CAAC;MAC/C,CAAC,CAAC;MAEFzD,KAAK,CAAC0D,eAAe,CAACJ,eAAe,CAAC;MAEtCK,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE5D,KAAK,CAAC6D,eAAe,CAAC;MACzDd,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;QACpEH,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAE1D,2BAA2B,CAAC0D,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;IACJ,CAAC;IAEDhD,KAAK,CAAC8D,eAAe,GAAG,UAAUlB,KAAK,EAAEC,IAAI,EAAE;MAC7C,IAAIkB,YAAY,GAAG/D,KAAK,CAACa,KAAK;QAC1BQ,YAAY,GAAG0C,YAAY,CAAC1C,YAAY;QACxCP,WAAW,GAAGiD,YAAY,CAACjD,WAAW;QACtCS,gBAAgB,GAAGwC,YAAY,CAACxC,gBAAgB;QAChDS,YAAY,GAAG+B,YAAY,CAAC/B,YAAY;QACxCjB,MAAM,GAAGgD,YAAY,CAAChD,MAAM;MAChC,IAAIiD,WAAW,GAAGhE,KAAK,CAACgD,KAAK;QACzBiB,WAAW,GAAGD,WAAW,CAACC,WAAW;QACrCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QAC/BC,SAAS,GAAGH,WAAW,CAACG,SAAS;QACjCC,SAAS,GAAGJ,WAAW,CAACI,SAAS;MACrC,IAAIC,WAAW,GAAGxB,IAAI,CAACG,KAAK;QACxBsB,GAAG,GAAGD,WAAW,CAACC,GAAG;QACrBrB,QAAQ,GAAGoB,WAAW,CAACpB,QAAQ;MAEnC,IAAIsB,qBAAqB,GAAGzG,sBAAsB,CAACkC,KAAK,CAAC;QACrDuC,QAAQ,GAAGgC,qBAAqB,CAAChC,QAAQ,CAAC,CAAC;;MAG/C,IAAIvC,KAAK,CAACwC,gCAAgC,KAAKS,QAAQ,EAAE;QACvDjD,KAAK,CAACwC,gCAAgC,GAAGS,QAAQ;MACnD;MAEA,IAAI,CAACV,QAAQ,EAAE;QACbvC,KAAK,CAACwE,cAAc,CAAC,CAAC;QAEtB;MACF;MAEA,IAAIC,iBAAiB,GAAG9F,gBAAgB,CAACiE,KAAK,EAAEL,QAAQ,EAAEM,IAAI,EAAE9B,MAAM,EAAEf,KAAK,CAACsC,sBAAsB,EAAE6B,SAAS,EAAEnC,YAAY,EAAElB,WAAW,EAAEO,YAAY,EAAE+C,SAAS,CAAC;QAChK3C,YAAY,GAAGgD,iBAAiB,CAAChD,YAAY;QAC7CE,eAAe,GAAG8C,iBAAiB,CAAC9C,eAAe;QACnDH,aAAa,GAAGiD,iBAAiB,CAACjD,aAAa;QAC/CE,gBAAgB,GAAG+C,iBAAiB,CAAC/C,gBAAgB;QACrDE,aAAa,GAAG6C,iBAAiB,CAAC7C,aAAa;QAC/CC,WAAW,GAAG4C,iBAAiB,CAAC5C,WAAW;QAC3CC,eAAe,GAAG2C,iBAAiB,CAAC3C,eAAe;MAEvD;MAAK;MACLP,gBAAgB,CAACmD,OAAO,CAAClD,aAAa,CAAC,KAAK,CAAC,CAAC;MAAI;MAClD,CAACK,WAAW,EAAE;QACZ7B,KAAK,CAACwE,cAAc,CAAC,CAAC;QAEtB;MACF,CAAC,CAAC;;MAGF,IAAI,CAACxE,KAAK,CAACW,qBAAqB,EAAE;QAChCX,KAAK,CAACW,qBAAqB,GAAG,CAAC,CAAC;MAClC;MAEAgE,MAAM,CAACC,IAAI,CAAC5E,KAAK,CAACW,qBAAqB,CAAC,CAACkE,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC9DC,YAAY,CAAC/E,KAAK,CAACW,qBAAqB,CAACmE,GAAG,CAAC,CAAC;MAChD,CAAC,CAAC;MAEF,IAAIvC,QAAQ,CAACS,KAAK,CAACC,QAAQ,KAAKJ,IAAI,CAACG,KAAK,CAACC,QAAQ,EAAE;QACnD;QACA;QACA;QACA;QACAL,KAAK,CAACoC,OAAO,CAAC,CAAC;QACfhF,KAAK,CAACW,qBAAqB,CAAC2D,GAAG,CAAC,GAAGX,MAAM,CAACsB,UAAU,CAAC,YAAY;UAC/D,IAAIjF,KAAK,CAACa,KAAK,CAACS,eAAe,KAAK,IAAI,EAAE;UAE1C,IAAIgC,eAAe,GAAG3F,kBAAkB,CAAC0D,YAAY,CAAC;UAEtD,IAAI6D,MAAM,GAAGpE,WAAW,CAAC+B,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAC;UAE7C,IAAIiC,MAAM,IAAI,CAACA,MAAM,CAACC,QAAQ,IAAI,EAAE,EAAEhF,MAAM,EAAE;YAC5CmD,eAAe,GAAG1E,MAAM,CAACyC,YAAY,EAAEwB,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAC;UAC7D;UAEA,IAAI,EAAE,cAAc,IAAIjD,KAAK,CAACgD,KAAK,CAAC,EAAE;YACpChD,KAAK,CAAC0D,eAAe,CAACJ,eAAe,CAAC;UACxC;UAEAY,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACZ,eAAe,EAAE;YAC5ET,IAAI,EAAE1D,2BAA2B,CAAC0D,IAAI,CAACG,KAAK,CAAC;YAC7CoC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAEzC,KAAK,CAACyC;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;;MAGF,IAAI9C,QAAQ,CAACS,KAAK,CAACC,QAAQ,KAAKzB,aAAa,IAAIG,eAAe,KAAK,CAAC,EAAE;QACtE3B,KAAK,CAACwE,cAAc,CAAC,CAAC;QAEtB;MACF,CAAC,CAAC;;MAGFxE,KAAK,CAACuD,QAAQ,CAAC;QACbzB,eAAe,EAAEA,eAAe;QAChCL,YAAY,EAAEA,YAAY;QAC1BE,eAAe,EAAEA,eAAe;QAChCH,aAAa,EAAEA,aAAa;QAC5BE,gBAAgB,EAAEA,gBAAgB;QAClCE,aAAa,EAAEA,aAAa;QAC5BC,WAAW,EAAEA;MACf,CAAC,CAAC;MAEFoC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;QACpErB,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAE1D,2BAA2B,CAAC0D,IAAI,CAACG,KAAK,CAAC;QAC7C3B,YAAY,EAAEA;MAChB,CAAC,CAAC;IACJ,CAAC;IAEDrB,KAAK,CAACsF,cAAc,GAAG,UAAU1C,KAAK,EAAEC,IAAI,EAAE;MAC5C,IAAI0C,YAAY,GAAGvF,KAAK,CAACa,KAAK;QAC1BU,gBAAgB,GAAGgE,YAAY,CAAChE,gBAAgB;QAChDS,YAAY,GAAGuD,YAAY,CAACvD,YAAY;QACxClB,WAAW,GAAGyE,YAAY,CAACzE,WAAW;QACtCO,YAAY,GAAGkE,YAAY,CAAClE,YAAY;QACxCN,MAAM,GAAGwE,YAAY,CAACxE,MAAM;MAChC,IAAIyE,YAAY,GAAGxF,KAAK,CAACgD,KAAK;QAC1ByC,UAAU,GAAGD,YAAY,CAACC,UAAU;QACpCtB,SAAS,GAAGqB,YAAY,CAACrB,SAAS;QAClCC,SAAS,GAAGoB,YAAY,CAACpB,SAAS;MAEtC,IAAIsB,sBAAsB,GAAG5H,sBAAsB,CAACkC,KAAK,CAAC;QACtDuC,QAAQ,GAAGmD,sBAAsB,CAACnD,QAAQ;MAE9C,IAAI,CAACA,QAAQ,EAAE;QACb;MACF;MAEA,IAAIoD,kBAAkB,GAAGhH,gBAAgB,CAACiE,KAAK,EAAEL,QAAQ,EAAEM,IAAI,EAAE9B,MAAM,EAAEf,KAAK,CAACsC,sBAAsB,EAAE6B,SAAS,EAAEnC,YAAY,EAAElB,WAAW,EAAEO,YAAY,EAAE+C,SAAS,CAAC;QACjK3C,YAAY,GAAGkE,kBAAkB,CAAClE,YAAY;QAC9CE,eAAe,GAAGgE,kBAAkB,CAAChE,eAAe;QACpDH,aAAa,GAAGmE,kBAAkB,CAACnE,aAAa;QAChDE,gBAAgB,GAAGiE,kBAAkB,CAACjE,gBAAgB;QACtDG,WAAW,GAAG8D,kBAAkB,CAAC9D,WAAW;QAC5CD,aAAa,GAAG+D,kBAAkB,CAAC/D,aAAa;QAChDE,eAAe,GAAG6D,kBAAkB,CAAC7D,eAAe;MAExD,IAAIP,gBAAgB,CAACmD,OAAO,CAAClD,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAACK,WAAW,EAAE;QAClE;QACA;QACA;MACF,CAAC,CAAC;;MAGF,IAAIU,QAAQ,CAACS,KAAK,CAACC,QAAQ,KAAKzB,aAAa,IAAIG,eAAe,KAAK,CAAC,EAAE;QACtE,IAAI,EAAE3B,KAAK,CAACa,KAAK,CAACY,YAAY,KAAK,IAAI,IAAIzB,KAAK,CAACa,KAAK,CAACc,eAAe,KAAK,IAAI,IAAI3B,KAAK,CAACa,KAAK,CAACW,aAAa,KAAK,IAAI,IAAIxB,KAAK,CAACa,KAAK,CAACa,gBAAgB,KAAK,IAAI,IAAI1B,KAAK,CAACa,KAAK,CAACe,aAAa,KAAK,IAAI,IAAI5B,KAAK,CAACa,KAAK,CAACgB,WAAW,KAAK,KAAK,IAAI7B,KAAK,CAACa,KAAK,CAACiB,eAAe,KAAK,IAAI,CAAC,EAAE;UAClR9B,KAAK,CAACwE,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM,IAAI,EAAE/C,YAAY,KAAKzB,KAAK,CAACa,KAAK,CAACY,YAAY,IAAIE,eAAe,KAAK3B,KAAK,CAACa,KAAK,CAACc,eAAe,IAAIH,aAAa,KAAKxB,KAAK,CAACa,KAAK,CAACW,aAAa,IAAIE,gBAAgB,KAAK1B,KAAK,CAACa,KAAK,CAACa,gBAAgB,IAAIE,aAAa,KAAK5B,KAAK,CAACa,KAAK,CAACe,aAAa,IAAIC,WAAW,KAAK7B,KAAK,CAACa,KAAK,CAACgB,WAAW,IAAIC,eAAe,KAAK9B,KAAK,CAACa,KAAK,CAACiB,eAAe,CAAC,EAAE;QAC3V9B,KAAK,CAACuD,QAAQ,CAAC;UACb9B,YAAY,EAAEA,YAAY;UAC1BE,eAAe,EAAEA,eAAe;UAChCH,aAAa,EAAEA,aAAa;UAC5BE,gBAAgB,EAAEA,gBAAgB;UAClCE,aAAa,EAAEA,aAAa;UAC5BC,WAAW,EAAEA,WAAW;UACxBC,eAAe,EAAEA;QACnB,CAAC,CAAC;MACJ;MAEA2D,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC;QACjE7C,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAE1D,2BAA2B,CAAC0D,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;IACJ,CAAC;IAEDhD,KAAK,CAAC4F,eAAe,GAAG,UAAUhD,KAAK,EAAEC,IAAI,EAAE;MAC7C;MACA;MACA,IAAI7C,KAAK,CAACwC,gCAAgC,KAAKK,IAAI,CAACG,KAAK,CAACC,QAAQ,IAAI,CAACL,KAAK,CAACiD,aAAa,CAACC,QAAQ,CAAClD,KAAK,CAACmD,aAAa,CAAC,EAAE;QACxH/F,KAAK,CAACwE,cAAc,CAAC,CAAC;QAEtBxE,KAAK,CAACwC,gCAAgC,GAAG,IAAI;MAC/C;MAEA,IAAIwD,WAAW,GAAGhG,KAAK,CAACgD,KAAK,CAACgD,WAAW;MACzCA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;QACpEpD,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAE1D,2BAA2B,CAAC0D,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;IACJ,CAAC;IAEDhD,KAAK,CAAC6D,eAAe,GAAG,UAAUjB,KAAK,EAAE;MACvC5C,KAAK,CAACiG,aAAa,CAACrD,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;MAEtCe,MAAM,CAACuC,mBAAmB,CAAC,SAAS,EAAElG,KAAK,CAAC6D,eAAe,CAAC;IAC9D,CAAC;IAED7D,KAAK,CAACiG,aAAa,GAAG,UAAUrD,KAAK,EAAEC,IAAI,EAAE;MAC3C,IAAIsD,SAAS,GAAGnG,KAAK,CAACgD,KAAK,CAACmD,SAAS;MAErCnG,KAAK,CAACuD,QAAQ,CAAC;QACbzB,eAAe,EAAE;MACnB,CAAC,CAAC;MAEF9B,KAAK,CAACoG,cAAc,CAAC,CAAC;MAEtBD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC;QAC9DvD,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAE1D,2BAA2B,CAAC0D,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;MACFhD,KAAK,CAACuC,QAAQ,GAAG,IAAI;IACvB,CAAC;IAEDvC,KAAK,CAACqG,UAAU,GAAG,UAAUzD,KAAK,EAAEC,IAAI,EAAE;MACxC,IAAIyD,mBAAmB;MAEvB,IAAIC,WAAW,GAAGrG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsG,SAAS,GAAGtG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC3F,IAAIuG,YAAY,GAAGzG,KAAK,CAACa,KAAK;QAC1BU,gBAAgB,GAAGkF,YAAY,CAAClF,gBAAgB;QAChDE,YAAY,GAAGgF,YAAY,CAAChF,YAAY;QACxCD,aAAa,GAAGiF,YAAY,CAACjF,aAAa;QAC1CI,aAAa,GAAG6E,YAAY,CAAC7E,aAAa;QAC1CC,WAAW,GAAG4E,YAAY,CAAC5E,WAAW;MAC1C,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI6E,MAAM,GAAG1G,KAAK,CAACgD,KAAK,CAAC0D,MAAM;MAE/B1G,KAAK,CAACuD,QAAQ,CAAC;QACbzB,eAAe,EAAE;MACnB,CAAC,CAAC;MAEF9B,KAAK,CAACoG,cAAc,CAAC,CAAC;MAEtB,IAAI5E,aAAa,KAAK,IAAI,EAAE;MAE5B,IAAImF,qBAAqB,GAAGjJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,gBAAgB,CAACoC,aAAa,EAAExB,KAAK,CAAC4G,wBAAwB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClIC,MAAM,EAAE,CAAC,CAACP,mBAAmB,GAAGtG,KAAK,CAAC8G,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIR,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACS,IAAI,CAACjC,GAAG,MAAMtD,aAAa;QAC5JuF,IAAI,EAAE/G,KAAK,CAACa,KAAK,CAACC,WAAW,CAACU,aAAa,CAAC,CAACqB;MAC/C,CAAC,CAAC;MAEF,IAAImE,WAAW,GAAGzF,gBAAgB,CAACmD,OAAO,CAAClD,aAAa,CAAC,KAAK,CAAC,CAAC;MAChErD,OAAO,CAAC,CAAC6I,WAAW,EAAE,6FAA6F,CAAC;MACpH,IAAIC,MAAM,GAAGnI,QAAQ,CAAC8C,aAAa,CAAC;MACpC,IAAIsF,UAAU,GAAG;QACftE,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAE1D,2BAA2B,CAACwH,qBAAqB,CAAC;QACxDpE,QAAQ,EAAEvC,KAAK,CAACuC,QAAQ,GAAGpD,2BAA2B,CAACa,KAAK,CAACuC,QAAQ,CAACS,KAAK,CAAC,GAAG,IAAI;QACnFmE,aAAa,EAAE,CAACnH,KAAK,CAACuC,QAAQ,CAACS,KAAK,CAACC,QAAQ,CAAC,CAACxC,MAAM,CAACc,gBAAgB,CAAC;QACvE6F,SAAS,EAAE3F,YAAY,KAAK,CAAC;QAC7BA,YAAY,EAAEA,YAAY,GAAG4F,MAAM,CAACJ,MAAM,CAACA,MAAM,CAAC9G,MAAM,GAAG,CAAC,CAAC;MAC/D,CAAC;MAED,IAAI,CAACoG,WAAW,EAAE;QAChBG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,UAAU,CAAC;MACpE;MAEAlH,KAAK,CAACuC,QAAQ,GAAG,IAAI;IACvB,CAAC;IAEDvC,KAAK,CAACoG,cAAc,GAAG,YAAY;MACjC,IAAI9E,eAAe,GAAGtB,KAAK,CAACa,KAAK,CAACS,eAAe;MAEjD,IAAIA,eAAe,KAAK,IAAI,EAAE;QAC5BtB,KAAK,CAACuD,QAAQ,CAAC;UACbjC,eAAe,EAAE,IAAI;UACrBG,YAAY,EAAE,IAAI;UAClBC,gBAAgB,EAAE,IAAI;UACtBF,aAAa,EAAE,IAAI;UACnBG,eAAe,EAAE,IAAI;UACrBE,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;MAEA9B,KAAK,CAACsC,sBAAsB,GAAG,IAAI;MACnCtC,KAAK,CAACwC,gCAAgC,GAAG,IAAI;IAC/C,CAAC;IAEDxC,KAAK,CAACsH,WAAW,GAAG,UAAUC,CAAC,EAAEC,QAAQ,EAAE;MACzC,IAAIC,OAAO,GAAGzH,KAAK,CAACgD,KAAK,CAACyE,OAAO;MACjCA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACF,CAAC,EAAEC,QAAQ,CAAC;IACxE,CAAC;IAEDxH,KAAK,CAAC0H,iBAAiB,GAAG,UAAUH,CAAC,EAAEC,QAAQ,EAAE;MAC/C,IAAIG,aAAa,GAAG3H,KAAK,CAACgD,KAAK,CAAC2E,aAAa;MAC7CA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,CAAC,EAAEC,QAAQ,CAAC;IAC1F,CAAC;IAEDxH,KAAK,CAAC4H,YAAY,GAAG,UAAUL,CAAC,EAAEC,QAAQ,EAAE;MAC1C,IAAIxG,YAAY,GAAGhB,KAAK,CAACa,KAAK,CAACG,YAAY;MAC3C,IAAI6G,YAAY,GAAG7H,KAAK,CAACa,KAAK;QAC1BC,WAAW,GAAG+G,YAAY,CAAC/G,WAAW;QACtCuB,UAAU,GAAGwF,YAAY,CAACxF,UAAU;MACxC,IAAIyF,YAAY,GAAG9H,KAAK,CAACgD,KAAK;QAC1B+E,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;MACpC,IAAIC,QAAQ,GAAGT,QAAQ,CAACS,QAAQ;MAChC,IAAInD,GAAG,GAAG0C,QAAQ,CAACnF,UAAU,CAACyC,GAAG,CAAC;MAClC,IAAIoD,cAAc,GAAG,CAACD,QAAQ,CAAC,CAAC;;MAEhC,IAAI,CAACC,cAAc,EAAE;QACnBlH,YAAY,GAAGnC,MAAM,CAACmC,YAAY,EAAE8D,GAAG,CAAC;MAC1C,CAAC,MAAM,IAAI,CAACkD,QAAQ,EAAE;QACpBhH,YAAY,GAAG,CAAC8D,GAAG,CAAC;MACtB,CAAC,MAAM;QACL9D,YAAY,GAAGpC,MAAM,CAACoC,YAAY,EAAE8D,GAAG,CAAC;MAC1C,CAAC,CAAC;;MAGF,IAAIqD,aAAa,GAAGnH,YAAY,CAACoH,GAAG,CAAC,UAAUC,WAAW,EAAE;QAC1D,IAAInD,MAAM,GAAGpE,WAAW,CAACuH,WAAW,CAAC;QACrC,IAAI,CAACnD,MAAM,EAAE,OAAO,IAAI;QACxB,OAAOA,MAAM,CAACrC,IAAI;MACpB,CAAC,CAAC,CAACyF,MAAM,CAAC,UAAUzF,IAAI,EAAE;QACxB,OAAOA,IAAI;MACb,CAAC,CAAC;MAEF7C,KAAK,CAACuI,oBAAoB,CAAC;QACzBvH,YAAY,EAAEA;MAChB,CAAC,CAAC;MAEF+G,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC/G,YAAY,EAAE;QACzE4B,KAAK,EAAE,QAAQ;QACfqF,QAAQ,EAAEC,cAAc;QACxBrF,IAAI,EAAE2E,QAAQ;QACdW,aAAa,EAAEA,aAAa;QAC5B9C,WAAW,EAAEkC,CAAC,CAAClC;MACjB,CAAC,CAAC;IACJ,CAAC;IAEDrF,KAAK,CAACwI,WAAW,GAAG,UAAUjB,CAAC,EAAEC,QAAQ,EAAEiB,OAAO,EAAE;MAClD,IAAIC,YAAY,GAAG1I,KAAK,CAACa,KAAK;QAC1BC,WAAW,GAAG4H,YAAY,CAAC5H,WAAW;QACtC6H,cAAc,GAAGD,YAAY,CAACzH,WAAW;QACzC2H,kBAAkB,GAAGF,YAAY,CAACxH,eAAe;MACrD,IAAI2H,YAAY,GAAG7I,KAAK,CAACgD,KAAK;QAC1B8F,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAClC,IAAIjE,GAAG,GAAG0C,QAAQ,CAAC1C,GAAG,CAAC,CAAC;;MAExB,IAAIkE,UAAU;MACd,IAAIC,QAAQ,GAAG;QACbrG,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE2E,QAAQ;QACdiB,OAAO,EAAEA,OAAO;QAChBpD,WAAW,EAAEkC,CAAC,CAAClC;MACjB,CAAC;MAED,IAAIyD,aAAa,EAAE;QACjB,IAAI7H,WAAW,GAAGwH,OAAO,GAAG7J,MAAM,CAAC+J,cAAc,EAAE7D,GAAG,CAAC,GAAGjG,MAAM,CAAC8J,cAAc,EAAE7D,GAAG,CAAC;QACrF,IAAI5D,eAAe,GAAGrC,MAAM,CAAC+J,kBAAkB,EAAE9D,GAAG,CAAC;QACrDkE,UAAU,GAAG;UACXP,OAAO,EAAExH,WAAW;UACpBiI,WAAW,EAAEhI;QACf,CAAC;QACD+H,QAAQ,CAACE,YAAY,GAAGlI,WAAW,CAACmH,GAAG,CAAC,UAAUgB,UAAU,EAAE;UAC5D,OAAOtI,WAAW,CAACsI,UAAU,CAAC;QAChC,CAAC,CAAC,CAACd,MAAM,CAAC,UAAUpD,MAAM,EAAE;UAC1B,OAAOA,MAAM;QACf,CAAC,CAAC,CAACkD,GAAG,CAAC,UAAUlD,MAAM,EAAE;UACvB,OAAOA,MAAM,CAACrC,IAAI;QACpB,CAAC,CAAC;QAEF7C,KAAK,CAACuI,oBAAoB,CAAC;UACzBtH,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAIoI,aAAa,GAAG3J,YAAY,CAAC,EAAE,CAACe,MAAM,CAAC9C,kBAAkB,CAACgL,cAAc,CAAC,EAAE,CAAC7D,GAAG,CAAC,CAAC,EAAE,IAAI,EAAEhE,WAAW,CAAC;UACrGwI,YAAY,GAAGD,aAAa,CAACpI,WAAW;UACxCsI,gBAAgB,GAAGF,aAAa,CAACnI,eAAe,CAAC,CAAC;;QAGtD,IAAI,CAACuH,OAAO,EAAE;UACZ,IAAIe,MAAM,GAAG,IAAIC,GAAG,CAACH,YAAY,CAAC;UAClCE,MAAM,CAACE,MAAM,CAAC5E,GAAG,CAAC;UAElB,IAAI6E,cAAc,GAAGjK,YAAY,CAACW,KAAK,CAACuJ,IAAI,CAACJ,MAAM,CAAC,EAAE;YACpDf,OAAO,EAAE,KAAK;YACdvH,eAAe,EAAEqI;UACnB,CAAC,EAAEzI,WAAW,CAAC;UAEfwI,YAAY,GAAGK,cAAc,CAAC1I,WAAW;UACzCsI,gBAAgB,GAAGI,cAAc,CAACzI,eAAe;QACnD;QAEA8H,UAAU,GAAGM,YAAY,CAAC,CAAC;;QAE3BL,QAAQ,CAACE,YAAY,GAAG,EAAE;QAC1BF,QAAQ,CAACY,qBAAqB,GAAG,EAAE;QACnCZ,QAAQ,CAAC/H,eAAe,GAAGqI,gBAAgB;QAE3CD,YAAY,CAACzE,OAAO,CAAC,UAAUuE,UAAU,EAAE;UACzC,IAAIlE,MAAM,GAAGpE,WAAW,CAACsI,UAAU,CAAC;UACpC,IAAI,CAAClE,MAAM,EAAE;UACb,IAAIrC,IAAI,GAAGqC,MAAM,CAACrC,IAAI;YAClByB,GAAG,GAAGY,MAAM,CAACZ,GAAG;UACpB2E,QAAQ,CAACE,YAAY,CAACW,IAAI,CAACjH,IAAI,CAAC;UAChCoG,QAAQ,CAACY,qBAAqB,CAACC,IAAI,CAAC;YAClCjH,IAAI,EAAEA,IAAI;YACVyB,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFtE,KAAK,CAACuI,oBAAoB,CAAC;UACzBtH,WAAW,EAAEqI;QACf,CAAC,EAAE,KAAK,EAAE;UACRpI,eAAe,EAAEqI;QACnB,CAAC,CAAC;MACJ;MAEAR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,UAAU,EAAEC,QAAQ,CAAC;IACjF,CAAC;IAEDjJ,KAAK,CAAC+J,UAAU,GAAG,UAAUvC,QAAQ,EAAE;MACrC,IAAI1C,GAAG,GAAG0C,QAAQ,CAAC1C,GAAG;MACtB,IAAIkF,WAAW,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QACvD;QACAnK,KAAK,CAACuD,QAAQ,CAAC,UAAU6G,IAAI,EAAE;UAC7B,IAAIC,eAAe,GAAGD,IAAI,CAACjJ,UAAU;YACjCA,UAAU,GAAGkJ,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;YAC9DC,gBAAgB,GAAGF,IAAI,CAAChJ,WAAW;YACnCA,WAAW,GAAGkJ,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;UACrE,IAAIC,YAAY,GAAGvK,KAAK,CAACgD,KAAK;YAC1BwH,QAAQ,GAAGD,YAAY,CAACC,QAAQ;YAChCC,MAAM,GAAGF,YAAY,CAACE,MAAM;UAEhC,IAAI,CAACD,QAAQ,IAAIrJ,UAAU,CAACuD,OAAO,CAACI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI1D,WAAW,CAACsD,OAAO,CAACI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAClF,OAAO,IAAI;UACb,CAAC,CAAC;;UAGF,IAAI4F,OAAO,GAAGF,QAAQ,CAAChD,QAAQ,CAAC;UAChCkD,OAAO,CAACC,IAAI,CAAC,YAAY;YACvB,IAAIC,iBAAiB,GAAG5K,KAAK,CAACa,KAAK,CAACM,UAAU;YAC9C,IAAI0J,aAAa,GAAGjM,MAAM,CAACgM,iBAAiB,EAAE9F,GAAG,CAAC,CAAC,CAAC;YACpD;;YAEA2F,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,aAAa,EAAE;cACpEjI,KAAK,EAAE,MAAM;cACbC,IAAI,EAAE2E;YACR,CAAC,CAAC;YAEFxH,KAAK,CAACuI,oBAAoB,CAAC;cACzBpH,UAAU,EAAE0J;YACd,CAAC,CAAC;YAEF7K,KAAK,CAACuD,QAAQ,CAAC,UAAUuH,SAAS,EAAE;cAClC,OAAO;gBACL1J,WAAW,EAAEvC,MAAM,CAACiM,SAAS,CAAC1J,WAAW,EAAE0D,GAAG;cAChD,CAAC;YACH,CAAC,CAAC;YAEFoF,OAAO,CAAC,CAAC;UACX,CAAC,CAAC,CAACa,KAAK,CAAC,UAAUxD,CAAC,EAAE;YACpBvH,KAAK,CAACuD,QAAQ,CAAC,UAAUuH,SAAS,EAAE;cAClC,OAAO;gBACL1J,WAAW,EAAEvC,MAAM,CAACiM,SAAS,CAAC1J,WAAW,EAAE0D,GAAG;cAChD,CAAC;YACH,CAAC,CAAC,CAAC,CAAC;;YAGJ9E,KAAK,CAACY,iBAAiB,CAACkE,GAAG,CAAC,GAAG,CAAC9E,KAAK,CAACY,iBAAiB,CAACkE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YAEtE,IAAI9E,KAAK,CAACY,iBAAiB,CAACkE,GAAG,CAAC,IAAIlF,eAAe,EAAE;cACnD,IAAIgL,iBAAiB,GAAG5K,KAAK,CAACa,KAAK,CAACM,UAAU;cAC9ChD,OAAO,CAAC,KAAK,EAAE,kEAAkE,CAAC;cAElF6B,KAAK,CAACuI,oBAAoB,CAAC;gBACzBpH,UAAU,EAAEvC,MAAM,CAACgM,iBAAiB,EAAE9F,GAAG;cAC3C,CAAC,CAAC;cAEFoF,OAAO,CAAC,CAAC;YACX;YAEAC,MAAM,CAAC5C,CAAC,CAAC;UACX,CAAC,CAAC;UACF,OAAO;YACLnG,WAAW,EAAExC,MAAM,CAACwC,WAAW,EAAE0D,GAAG;UACtC,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC;;MAEJkF,WAAW,CAACe,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;MACjC,OAAOf,WAAW;IACpB,CAAC;IAEDhK,KAAK,CAACgL,gBAAgB,GAAG,UAAUpI,KAAK,EAAEC,IAAI,EAAE;MAC9C,IAAIoI,YAAY,GAAGjL,KAAK,CAACgD,KAAK,CAACiI,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;QACvErI,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC;IAED7C,KAAK,CAACkL,gBAAgB,GAAG,UAAUtI,KAAK,EAAEC,IAAI,EAAE;MAC9C,IAAIsI,YAAY,GAAGnL,KAAK,CAACgD,KAAK,CAACmI,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;QACvEvI,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC;IAED7C,KAAK,CAACoL,iBAAiB,GAAG,UAAUxI,KAAK,EAAEC,IAAI,EAAE;MAC/C,IAAIwI,YAAY,GAAGrL,KAAK,CAACgD,KAAK,CAACqI,YAAY;MAE3C,IAAIA,YAAY,EAAE;QAChBzI,KAAK,CAAC0I,cAAc,CAAC,CAAC;QACtBD,YAAY,CAAC;UACXzI,KAAK,EAAEA,KAAK;UACZC,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC;IAED7C,KAAK,CAACuL,OAAO,GAAG,YAAY;MAC1B,IAAIA,OAAO,GAAGvL,KAAK,CAACgD,KAAK,CAACuI,OAAO;MAEjCvL,KAAK,CAACuD,QAAQ,CAAC;QACbtB,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,KAAK,IAAIuJ,KAAK,GAAGtL,SAAS,CAACC,MAAM,EAAEsL,IAAI,GAAG,IAAIpL,KAAK,CAACmL,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QAC7FD,IAAI,CAACC,KAAK,CAAC,GAAGxL,SAAS,CAACwL,KAAK,CAAC;MAChC;MAEAH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC/K,KAAK,CAAC,KAAK,CAAC,EAAEiL,IAAI,CAAC;IAC/E,CAAC;IAEDzL,KAAK,CAAC2L,MAAM,GAAG,YAAY;MACzB,IAAIA,MAAM,GAAG3L,KAAK,CAACgD,KAAK,CAAC2I,MAAM;MAE/B3L,KAAK,CAACuD,QAAQ,CAAC;QACbtB,OAAO,EAAE;MACX,CAAC,CAAC;MAEFjC,KAAK,CAAC4L,cAAc,CAAC,IAAI,CAAC;MAE1B,KAAK,IAAIC,KAAK,GAAG3L,SAAS,CAACC,MAAM,EAAEsL,IAAI,GAAG,IAAIpL,KAAK,CAACwL,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FL,IAAI,CAACK,KAAK,CAAC,GAAG5L,SAAS,CAAC4L,KAAK,CAAC;MAChC;MAEAH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnL,KAAK,CAAC,KAAK,CAAC,EAAEiL,IAAI,CAAC;IAC5E,CAAC;IAEDzL,KAAK,CAAC4G,wBAAwB,GAAG,YAAY;MAC3C,IAAImF,YAAY,GAAG/L,KAAK,CAACa,KAAK;QAC1BQ,YAAY,GAAG0K,YAAY,CAAC1K,YAAY;QACxCL,YAAY,GAAG+K,YAAY,CAAC/K,YAAY;QACxCG,UAAU,GAAG4K,YAAY,CAAC5K,UAAU;QACpCC,WAAW,GAAG2K,YAAY,CAAC3K,WAAW;QACtCH,WAAW,GAAG8K,YAAY,CAAC9K,WAAW;QACtCC,eAAe,GAAG6K,YAAY,CAAC7K,eAAe;QAC9CY,eAAe,GAAGiK,YAAY,CAACjK,eAAe;QAC9CL,YAAY,GAAGsK,YAAY,CAACtK,YAAY;QACxCX,WAAW,GAAGiL,YAAY,CAACjL,WAAW;MAC1C,OAAO;QACLO,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCL,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCG,UAAU,EAAEA,UAAU,IAAI,EAAE;QAC5BC,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BH,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BC,eAAe,EAAEA,eAAe,IAAI,EAAE;QACtCY,eAAe,EAAEA,eAAe;QAChCL,YAAY,EAAEA,YAAY;QAC1BX,WAAW,EAAEA;MACf,CAAC;IACH,CAAC;IAEDd,KAAK,CAAC0D,eAAe,GAAG,UAAUrC,YAAY,EAAE;MAC9C,IAAI2K,YAAY,GAAGhM,KAAK,CAACa,KAAK;QAC1BkB,QAAQ,GAAGiK,YAAY,CAACjK,QAAQ;QAChCM,UAAU,GAAG2J,YAAY,CAAC3J,UAAU;MACxC,IAAIL,YAAY,GAAGjD,eAAe,CAACgD,QAAQ,EAAEV,YAAY,EAAEgB,UAAU,CAAC;MAEtErC,KAAK,CAACuI,oBAAoB,CAAC;QACzBlH,YAAY,EAAEA,YAAY;QAC1BW,YAAY,EAAEA;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDhC,KAAK,CAACiM,YAAY,GAAG,UAAU1E,CAAC,EAAEC,QAAQ,EAAE;MAC1C,IAAInG,YAAY,GAAGrB,KAAK,CAACa,KAAK,CAACQ,YAAY;MAC3C,IAAI6K,YAAY,GAAGlM,KAAK,CAACa,KAAK;QAC1BsB,YAAY,GAAG+J,YAAY,CAAC/J,YAAY;QACxCE,UAAU,GAAG6J,YAAY,CAAC7J,UAAU;MACxC,IAAI8J,YAAY,GAAGnM,KAAK,CAACgD,KAAK;QAC1BkB,QAAQ,GAAGiI,YAAY,CAACjI,QAAQ;QAChCsG,QAAQ,GAAG2B,YAAY,CAAC3B,QAAQ;MACpC,IAAIpF,QAAQ,GAAGoC,QAAQ,CAACpC,QAAQ;MAChC,IAAIN,GAAG,GAAG0C,QAAQ,CAACnF,UAAU,CAACyC,GAAG,CAAC,CAAC,CAAC;;MAEpC,IAAI3C,YAAY,EAAE;QAChB;MACF,CAAC,CAAC;;MAGF,IAAIiK,KAAK,GAAG/K,YAAY,CAACqD,OAAO,CAACI,GAAG,CAAC;MACrC,IAAIuH,cAAc,GAAG,CAACjH,QAAQ;MAC9BjH,OAAO,CAACiH,QAAQ,IAAIgH,KAAK,KAAK,CAAC,CAAC,IAAI,CAAChH,QAAQ,IAAIgH,KAAK,KAAK,CAAC,CAAC,EAAE,wCAAwC,CAAC;MAExG,IAAIC,cAAc,EAAE;QAClBhL,YAAY,GAAGzC,MAAM,CAACyC,YAAY,EAAEyD,GAAG,CAAC;MAC1C,CAAC,MAAM;QACLzD,YAAY,GAAGxC,MAAM,CAACwC,YAAY,EAAEyD,GAAG,CAAC;MAC1C;MAEA9E,KAAK,CAAC0D,eAAe,CAACrC,YAAY,CAAC;MAEnC6C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC7C,YAAY,EAAE;QACzEwB,IAAI,EAAE2E,QAAQ;QACdpC,QAAQ,EAAEiH,cAAc;QACxBhH,WAAW,EAAEkC,CAAC,CAAClC;MACjB,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIgH,cAAc,IAAI7B,QAAQ,EAAE;QAC9B,IAAIR,WAAW,GAAGhK,KAAK,CAAC+J,UAAU,CAACvC,QAAQ,CAAC;QAE5C,IAAIwC,WAAW,EAAE;UACfA,WAAW,CAACW,IAAI,CAAC,YAAY;YAC3B;YACA,IAAI2B,kBAAkB,GAAGvN,eAAe,CAACiB,KAAK,CAACa,KAAK,CAACkB,QAAQ,EAAEV,YAAY,EAAEgB,UAAU,CAAC;YAExFrC,KAAK,CAACuI,oBAAoB,CAAC;cACzBvG,YAAY,EAAEsK;YAChB,CAAC,CAAC;UACJ,CAAC,CAAC,CAACvB,KAAK,CAAC,YAAY;YACnB,IAAIwB,mBAAmB,GAAGvM,KAAK,CAACa,KAAK,CAACQ,YAAY;YAClD,IAAImL,qBAAqB,GAAG3N,MAAM,CAAC0N,mBAAmB,EAAEzH,GAAG,CAAC;YAE5D9E,KAAK,CAAC0D,eAAe,CAAC8I,qBAAqB,CAAC;UAC9C,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IAEDxM,KAAK,CAACyM,iBAAiB,GAAG,YAAY;MACpCzM,KAAK,CAACuI,oBAAoB,CAAC;QACzBpG,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC;IAEDnC,KAAK,CAAC0M,eAAe,GAAG,YAAY;MAClCzH,UAAU,CAAC,YAAY;QACrBjF,KAAK,CAACuI,oBAAoB,CAAC;UACzBpG,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAEDnC,KAAK,CAAC4L,cAAc,GAAG,UAAUe,YAAY,EAAE;MAC7C,IAAIzK,SAAS,GAAGlC,KAAK,CAACa,KAAK,CAACqB,SAAS;MACrC,IAAI0J,cAAc,GAAG5L,KAAK,CAACgD,KAAK,CAAC4I,cAAc;MAE/C,IAAI1J,SAAS,KAAKyK,YAAY,EAAE;QAC9B;MACF;MAEA3M,KAAK,CAACuD,QAAQ,CAAC;QACbrB,SAAS,EAAEyK;MACb,CAAC,CAAC;MAEF,IAAIA,YAAY,KAAK,IAAI,EAAE;QACzB3M,KAAK,CAAC4M,QAAQ,CAAC;UACb9H,GAAG,EAAE6H;QACP,CAAC,CAAC;MACJ;MAEAf,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACe,YAAY,CAAC;IAC9F,CAAC;IAED3M,KAAK,CAAC8G,aAAa,GAAG,YAAY;MAChC,IAAI+F,aAAa,GAAG7M,KAAK,CAACa,KAAK;QAC3BqB,SAAS,GAAG2K,aAAa,CAAC3K,SAAS;QACnCF,YAAY,GAAG6K,aAAa,CAAC7K,YAAY;MAE7C,IAAIE,SAAS,KAAK,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;MAEA,OAAOF,YAAY,CAAC8K,IAAI,CAAC,UAAUC,KAAK,EAAE;QACxC,IAAIjI,GAAG,GAAGiI,KAAK,CAACjI,GAAG;QACnB,OAAOA,GAAG,KAAK5C,SAAS;MAC1B,CAAC,CAAC,IAAI,IAAI;IACZ,CAAC;IAEDlC,KAAK,CAACgN,eAAe,GAAG,UAAUC,MAAM,EAAE;MACxC,IAAIC,aAAa,GAAGlN,KAAK,CAACa,KAAK;QAC3BmB,YAAY,GAAGkL,aAAa,CAAClL,YAAY;QACzCE,SAAS,GAAGgL,aAAa,CAAChL,SAAS;MACvC,IAAIkK,KAAK,GAAGpK,YAAY,CAACmL,SAAS,CAAC,UAAUC,KAAK,EAAE;QAClD,IAAItI,GAAG,GAAGsI,KAAK,CAACtI,GAAG;QACnB,OAAOA,GAAG,KAAK5C,SAAS;MAC1B,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIkK,KAAK,KAAK,CAAC,CAAC,IAAIa,MAAM,GAAG,CAAC,EAAE;QAC9Bb,KAAK,GAAGpK,YAAY,CAAC7B,MAAM;MAC7B;MAEAiM,KAAK,GAAG,CAACA,KAAK,GAAGa,MAAM,GAAGjL,YAAY,CAAC7B,MAAM,IAAI6B,YAAY,CAAC7B,MAAM;MACpE,IAAIkN,IAAI,GAAGrL,YAAY,CAACoK,KAAK,CAAC;MAE9B,IAAIiB,IAAI,EAAE;QACR,IAAIvI,GAAG,GAAGuI,IAAI,CAACvI,GAAG;QAElB9E,KAAK,CAAC4L,cAAc,CAAC9G,GAAG,CAAC;MAC3B,CAAC,MAAM;QACL9E,KAAK,CAAC4L,cAAc,CAAC,IAAI,CAAC;MAC5B;IACF,CAAC;IAED5L,KAAK,CAACsN,SAAS,GAAG,UAAU1K,KAAK,EAAE;MACjC,IAAI2K,aAAa,GAAGvN,KAAK,CAACa,KAAK;QAC3BqB,SAAS,GAAGqL,aAAa,CAACrL,SAAS;QACnCb,YAAY,GAAGkM,aAAa,CAAClM,YAAY;QACzCJ,WAAW,GAAGsM,aAAa,CAACtM,WAAW;MAC3C,IAAIuM,YAAY,GAAGxN,KAAK,CAACgD,KAAK;QAC1BsK,SAAS,GAAGE,YAAY,CAACF,SAAS;QAClCG,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,UAAU,GAAGF,YAAY,CAACE,UAAU,CAAC,CAAC;;MAE1C,QAAQ9K,KAAK,CAAC+K,KAAK;QACjB,KAAKzP,OAAO,CAAC0P,EAAE;UACb;YACE5N,KAAK,CAACgN,eAAe,CAAC,CAAC,CAAC,CAAC;YAEzBpK,KAAK,CAAC0I,cAAc,CAAC,CAAC;YACtB;UACF;QAEF,KAAKpN,OAAO,CAAC2P,IAAI;UACf;YACE7N,KAAK,CAACgN,eAAe,CAAC,CAAC,CAAC;YAExBpK,KAAK,CAAC0I,cAAc,CAAC,CAAC;YACtB;UACF;MACJ,CAAC,CAAC;;MAGF,IAAIwC,UAAU,GAAG9N,KAAK,CAAC8G,aAAa,CAAC,CAAC;MAEtC,IAAIgH,UAAU,IAAIA,UAAU,CAAC/G,IAAI,EAAE;QACjC,IAAIgH,qBAAqB,GAAG/N,KAAK,CAAC4G,wBAAwB,CAAC,CAAC;QAE5D,IAAIoH,UAAU,GAAGF,UAAU,CAAC/G,IAAI,CAACkH,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,CAACH,UAAU,CAAC/G,IAAI,CAAC5B,QAAQ,IAAI,EAAE,EAAEhF,MAAM;QAC9F,IAAI+N,SAAS,GAAG/O,2BAA2B,CAACzB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,gBAAgB,CAAC8C,SAAS,EAAE6L,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACnIhH,IAAI,EAAE+G,UAAU,CAAC/G,IAAI;UACrBF,MAAM,EAAE;QACV,CAAC,CAAC,CAAC;QAEH,QAAQjE,KAAK,CAAC+K,KAAK;UACjB;UACA,KAAKzP,OAAO,CAACiQ,IAAI;YACf;cACE;cACA,IAAIH,UAAU,IAAI3M,YAAY,CAAC+M,QAAQ,CAAClM,SAAS,CAAC,EAAE;gBAClDlC,KAAK,CAACiM,YAAY,CAAC,CAAC,CAAC,EAAEiC,SAAS,CAAC;cACnC,CAAC,MAAM,IAAIJ,UAAU,CAACO,MAAM,EAAE;gBAC5BrO,KAAK,CAAC4L,cAAc,CAACkC,UAAU,CAACO,MAAM,CAACtH,IAAI,CAACjC,GAAG,CAAC;cAClD;cAEAlC,KAAK,CAAC0I,cAAc,CAAC,CAAC;cACtB;YACF;UAEF,KAAKpN,OAAO,CAACoQ,KAAK;YAChB;cACE;cACA,IAAIN,UAAU,IAAI,CAAC3M,YAAY,CAAC+M,QAAQ,CAAClM,SAAS,CAAC,EAAE;gBACnDlC,KAAK,CAACiM,YAAY,CAAC,CAAC,CAAC,EAAEiC,SAAS,CAAC;cACnC,CAAC,MAAM,IAAIJ,UAAU,CAAC3I,QAAQ,IAAI2I,UAAU,CAAC3I,QAAQ,CAAChF,MAAM,EAAE;gBAC5DH,KAAK,CAAC4L,cAAc,CAACkC,UAAU,CAAC3I,QAAQ,CAAC,CAAC,CAAC,CAAC4B,IAAI,CAACjC,GAAG,CAAC;cACvD;cAEAlC,KAAK,CAAC0I,cAAc,CAAC,CAAC;cACtB;YACF;UACF;;UAEA,KAAKpN,OAAO,CAACqQ,KAAK;UAClB,KAAKrQ,OAAO,CAACsQ,KAAK;YAChB;cACE,IAAIf,SAAS,IAAI,CAACS,SAAS,CAACO,QAAQ,IAAIP,SAAS,CAACT,SAAS,KAAK,KAAK,IAAI,CAACS,SAAS,CAACQ,eAAe,EAAE;gBACnG1O,KAAK,CAACwI,WAAW,CAAC,CAAC,CAAC,EAAE0F,SAAS,EAAE,CAACjN,WAAW,CAACmN,QAAQ,CAAClM,SAAS,CAAC,CAAC;cACpE,CAAC,MAAM,IAAI,CAACuL,SAAS,IAAIC,UAAU,IAAI,CAACQ,SAAS,CAACO,QAAQ,IAAIP,SAAS,CAACR,UAAU,KAAK,KAAK,EAAE;gBAC5F1N,KAAK,CAAC4H,YAAY,CAAC,CAAC,CAAC,EAAEsG,SAAS,CAAC;cACnC;cAEA;YACF;QACJ;MACF;MAEAZ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC1K,KAAK,CAAC;IACxE,CAAC;IAED5C,KAAK,CAACuI,oBAAoB,GAAG,UAAU1H,KAAK,EAAE;MAC5C,IAAI8N,MAAM,GAAGzO,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsG,SAAS,GAAGtG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACtF,IAAI0O,UAAU,GAAG1O,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsG,SAAS,GAAGtG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAEzF,IAAI,CAACF,KAAK,CAACU,SAAS,EAAE;QACpB,IAAImO,QAAQ,GAAG,KAAK;QACpB,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAIC,QAAQ,GAAG,CAAC,CAAC;QACjBpK,MAAM,CAACC,IAAI,CAAC/D,KAAK,CAAC,CAACgE,OAAO,CAAC,UAAUmK,IAAI,EAAE;UACzC,IAAIA,IAAI,IAAIhP,KAAK,CAACgD,KAAK,EAAE;YACvB8L,SAAS,GAAG,KAAK;YACjB;UACF;UAEAD,QAAQ,GAAG,IAAI;UACfE,QAAQ,CAACC,IAAI,CAAC,GAAGnO,KAAK,CAACmO,IAAI,CAAC;QAC9B,CAAC,CAAC;QAEF,IAAIH,QAAQ,KAAK,CAACF,MAAM,IAAIG,SAAS,CAAC,EAAE;UACtC9O,KAAK,CAACuD,QAAQ,CAAC7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqR,QAAQ,CAAC,EAAEH,UAAU,CAAC,CAAC;QACxE;MACF;IACF,CAAC;IAED5O,KAAK,CAAC4M,QAAQ,GAAG,UAAUqC,MAAM,EAAE;MACjCjP,KAAK,CAACyC,OAAO,CAACe,OAAO,CAACoJ,QAAQ,CAACqC,MAAM,CAAC;IACxC,CAAC;IAED,OAAOjP,KAAK;EACd;EAEAnC,YAAY,CAACgC,IAAI,EAAE,CAAC;IAClBiF,GAAG,EAAE,mBAAmB;IACxBoK,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDtK,GAAG,EAAE,oBAAoB;IACzBoK,KAAK,EAAE,SAASG,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACD,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDtK,GAAG,EAAE,WAAW;IAChBoK,KAAK,EAAE,SAASE,SAASA,CAAA,EAAG;MAC1B,IAAIlN,SAAS,GAAG,IAAI,CAACc,KAAK,CAACd,SAAS;MAEpC,IAAIA,SAAS,KAAKsE,SAAS,IAAItE,SAAS,KAAK,IAAI,CAACrB,KAAK,CAACqB,SAAS,EAAE;QACjE,IAAI,CAACqB,QAAQ,CAAC;UACZrB,SAAS,EAAEA;QACb,CAAC,CAAC;QAEF,IAAIA,SAAS,KAAK,IAAI,EAAE;UACtB,IAAI,CAAC0K,QAAQ,CAAC;YACZ9H,GAAG,EAAE5C;UACP,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE;IACD4C,GAAG,EAAE,sBAAsB;IAC3BoK,KAAK,EAAE,SAASI,oBAAoBA,CAAA,EAAG;MACrC3L,MAAM,CAACuC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACrC,eAAe,CAAC;MAC3D,IAAI,CAACnD,SAAS,GAAG,IAAI;IACvB;EACF,CAAC,EAAE;IACDoE,GAAG,EAAE,gBAAgB;IACrBoK,KAAK,EAAE,SAAS1K,cAAcA,CAAA,EAAG;MAC/B,IAAI,CAACjB,QAAQ,CAAC;QACZzB,eAAe,EAAE,IAAI;QACrBL,YAAY,EAAE,IAAI;QAClBE,eAAe,EAAE,IAAI;QACrBH,aAAa,EAAE,IAAI;QACnBE,gBAAgB,EAAE,IAAI;QACtBE,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDiD,GAAG,EAAE,QAAQ;IACboK,KAAK,EAAE,SAASK,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;MAEf,IAAIC,aAAa,GAAG,IAAI,CAAC5O,KAAK;QAC1BoB,OAAO,GAAGwN,aAAa,CAACxN,OAAO;QAC/BD,YAAY,GAAGyN,aAAa,CAACzN,YAAY;QACzClB,WAAW,GAAG2O,aAAa,CAAC3O,WAAW;QACvCQ,eAAe,GAAGmO,aAAa,CAACnO,eAAe;QAC/CY,SAAS,GAAGuN,aAAa,CAACvN,SAAS;QACnCP,eAAe,GAAG8N,aAAa,CAAC9N,eAAe;QAC/CD,gBAAgB,GAAG+N,aAAa,CAAC/N,gBAAgB;QACjDF,aAAa,GAAGiO,aAAa,CAACjO,aAAa;QAC3CC,YAAY,GAAGgO,aAAa,CAAChO,YAAY;QACzCK,eAAe,GAAG2N,aAAa,CAAC3N,eAAe;QAC/Cf,MAAM,GAAG0O,aAAa,CAAC1O,MAAM;MACjC,IAAI2O,YAAY,GAAG,IAAI,CAAC1M,KAAK;QACzB2M,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,KAAK,GAAGH,YAAY,CAACG,KAAK;QAC1BC,QAAQ,GAAGJ,YAAY,CAACI,QAAQ;QAChCC,SAAS,GAAGL,YAAY,CAACK,SAAS;QAClCC,qBAAqB,GAAGN,YAAY,CAACO,QAAQ;QAC7CA,QAAQ,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;QACvEtC,UAAU,GAAGgC,YAAY,CAAChC,UAAU;QACpCwC,QAAQ,GAAGR,YAAY,CAACQ,QAAQ;QAChCC,IAAI,GAAGT,YAAY,CAACS,IAAI;QACxBC,YAAY,GAAGV,YAAY,CAACU,YAAY;QACxCC,SAAS,GAAGX,YAAY,CAACW,SAAS;QAClC5C,SAAS,GAAGiC,YAAY,CAACjC,SAAS;QAClC3E,aAAa,GAAG4G,YAAY,CAAC5G,aAAa;QAC1C2F,QAAQ,GAAGiB,YAAY,CAACjB,QAAQ;QAChC6B,MAAM,GAAGZ,YAAY,CAACY,MAAM;QAC5B9F,QAAQ,GAAGkF,YAAY,CAAClF,QAAQ;QAChC+F,cAAc,GAAGb,YAAY,CAACa,cAAc;QAC5CC,MAAM,GAAGd,YAAY,CAACc,MAAM;QAC5BC,UAAU,GAAGf,YAAY,CAACe,UAAU;QACpCC,OAAO,GAAGhB,YAAY,CAACgB,OAAO;QAC9BC,WAAW,GAAGjB,YAAY,CAACiB,WAAW;QACtCC,mBAAmB,GAAGlB,YAAY,CAACkB,mBAAmB;QACtDC,aAAa,GAAGnB,YAAY,CAACmB,aAAa;QAC1CC,QAAQ,GAAGpB,YAAY,CAACoB,QAAQ;QAChC1M,SAAS,GAAGsL,YAAY,CAACtL,SAAS;QAClC2M,aAAa,GAAGrB,YAAY,CAACqB,aAAa;QAC1CC,SAAS,GAAGtB,YAAY,CAACsB,SAAS;MACtC,IAAIC,QAAQ,GAAG7S,SAAS,CAAC,IAAI,CAAC4E,KAAK,EAAE;QACnCkO,IAAI,EAAE,IAAI;QACVnK,IAAI,EAAE;MACR,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIoK,eAAe;MAEnB,IAAId,SAAS,EAAE;QACb,IAAI5S,OAAO,CAAC4S,SAAS,CAAC,KAAK,QAAQ,EAAE;UACnCc,eAAe,GAAGd,SAAS;QAC7B,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;UAC1Cc,eAAe,GAAG;YAChBC,aAAa,EAAEf;UACjB,CAAC;QACH,CAAC,MAAM;UACLc,eAAe,GAAG,CAAC,CAAC;QACtB;MACF;MAEA,OAAO,aAAalT,KAAK,CAACoT,aAAa,CAAC/S,WAAW,CAACgT,QAAQ,EAAE;QAC5DpC,KAAK,EAAE;UACLS,SAAS,EAAEA,SAAS;UACpBjC,UAAU,EAAEA,UAAU;UACtBwC,QAAQ,EAAEA,QAAQ;UAClBC,IAAI,EAAEA,IAAI;UACVC,YAAY,EAAEA,YAAY;UAC1BC,SAAS,EAAEc,eAAe;UAC1B7P,eAAe,EAAEA,eAAe;UAChCmM,SAAS,EAAEA,SAAS;UACpB3E,aAAa,EAAEA,aAAa;UAC5B2F,QAAQ,EAAEA,QAAQ;UAClB3N,WAAW,EAAEA,WAAW;UACxBa,eAAe,EAAEA,eAAe;UAChCD,gBAAgB,EAAEA,gBAAgB;UAClCF,aAAa,EAAEA,aAAa;UAC5BC,YAAY,EAAEA,YAAY;UAC1BK,eAAe,EAAEA,eAAe;UAChCf,MAAM,EAAEA,MAAM;UACdqD,SAAS,EAAEA,SAAS;UACpBwM,mBAAmB,EAAEA,mBAAmB;UACxCpG,QAAQ,EAAEA,QAAQ;UAClB+F,cAAc,EAAEA,cAAc;UAC9BI,WAAW,EAAEA,WAAW;UACxBrJ,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BI,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;UACzCuE,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BrE,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BY,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BuB,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BiB,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;UACvCE,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;UACvCE,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;UACzCzI,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCmB,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCwB,cAAc,EAAE,IAAI,CAACA,cAAc;UACnCM,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCK,aAAa,EAAE,IAAI,CAACA,aAAa;UACjCI,UAAU,EAAE,IAAI,CAACA;QACnB;MACF,CAAC,EAAE,aAAapI,KAAK,CAACoT,aAAa,CAAC,KAAK,EAAE;QACzCE,IAAI,EAAE,MAAM;QACZ3B,SAAS,EAAEvR,UAAU,CAACsR,SAAS,EAAEC,SAAS,EAAEmB,aAAa,GAAGvB,WAAW,GAAG,CAAC,CAAC,EAAEhS,eAAe,CAACgS,WAAW,EAAE,EAAE,CAAC/O,MAAM,CAACkP,SAAS,EAAE,YAAY,CAAC,EAAEG,QAAQ,CAAC,EAAEtS,eAAe,CAACgS,WAAW,EAAE,EAAE,CAAC/O,MAAM,CAACkP,SAAS,EAAE,UAAU,CAAC,EAAE1N,OAAO,CAAC,EAAEzE,eAAe,CAACgS,WAAW,EAAE,EAAE,CAAC/O,MAAM,CAACkP,SAAS,EAAE,iBAAiB,CAAC,EAAEzN,SAAS,KAAK,IAAI,CAAC,EAAEsN,WAAW,CAAC,CAAC;QAC3UK,KAAK,EAAEmB;MACT,CAAC,EAAE,aAAa/S,KAAK,CAACoT,aAAa,CAAC/R,QAAQ,EAAE/B,QAAQ,CAAC;QACrDiU,GAAG,EAAE,IAAI,CAAC/O,OAAO;QACjBkN,SAAS,EAAEA,SAAS;QACpBE,KAAK,EAAEA,KAAK;QACZ9I,IAAI,EAAE/E,YAAY;QAClByM,QAAQ,EAAEA,QAAQ;QAClBf,UAAU,EAAEA,UAAU;QACtBD,SAAS,EAAE,CAAC,CAACA,SAAS;QACtB6C,MAAM,EAAEA,MAAM;QACdmB,QAAQ,EAAEnQ,eAAe,KAAK,IAAI;QAClCkP,MAAM,EAAEA,MAAM;QACdC,UAAU,EAAEA,UAAU;QACtBC,OAAO,EAAEA,OAAO;QAChBX,SAAS,EAAEA,SAAS;QACpB9N,OAAO,EAAEA,OAAO;QAChBgO,QAAQ,EAAEA,QAAQ;QAClBnC,UAAU,EAAE,IAAI,CAAChH,aAAa,CAAC,CAAC;QAChCyE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBI,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB2B,SAAS,EAAE,IAAI,CAACA,SAAS;QACzB1B,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCa,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCmE,aAAa,EAAEA,aAAa;QAC5BC,QAAQ,EAAEA;MACZ,CAAC,EAAE,IAAI,CAAClK,wBAAwB,CAAC,CAAC,EAAEqK,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClD;EACF,CAAC,CAAC,EAAE,CAAC;IACHnM,GAAG,EAAE,0BAA0B;IAC/BoK,KAAK,EAAE,SAASwC,wBAAwBA,CAAC1O,KAAK,EAAE8H,SAAS,EAAE;MACzD,IAAI1I,SAAS,GAAG0I,SAAS,CAAC1I,SAAS;MACnC,IAAI2M,QAAQ,GAAG;QACb3M,SAAS,EAAEY;MACb,CAAC;MAED,SAAS6L,QAAQA,CAACG,IAAI,EAAE;QACtB,OAAO,CAAC5M,SAAS,IAAI4M,IAAI,IAAIhM,KAAK,IAAIZ,SAAS,IAAIA,SAAS,CAAC4M,IAAI,CAAC,KAAKhM,KAAK,CAACgM,IAAI,CAAC;MACpF,CAAC,CAAC;;MAGF,IAAIjN,QAAQ,CAAC,CAAC;;MAEd,IAAIM,UAAU,GAAGyI,SAAS,CAACzI,UAAU;MAErC,IAAIwM,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1BxM,UAAU,GAAGhD,cAAc,CAAC2D,KAAK,CAACX,UAAU,CAAC;QAC7C0M,QAAQ,CAAC1M,UAAU,GAAGA,UAAU;MAClC,CAAC,CAAC;;MAGF,IAAIwM,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB9M,QAAQ,GAAGiB,KAAK,CAACjB,QAAQ;MAC3B,CAAC,MAAM,IAAI8M,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC/B1Q,OAAO,CAAC,KAAK,EAAE,kEAAkE,CAAC;QAClF4D,QAAQ,GAAG/C,iBAAiB,CAACgE,KAAK,CAACmC,QAAQ,CAAC;MAC9C,CAAC,CAAC;;MAGF,IAAIpD,QAAQ,EAAE;QACZgN,QAAQ,CAAChN,QAAQ,GAAGA,QAAQ;QAC5B,IAAI4P,WAAW,GAAG1S,qBAAqB,CAAC8C,QAAQ,EAAE;UAChDM,UAAU,EAAEA;QACd,CAAC,CAAC;QACF0M,QAAQ,CAACjO,WAAW,GAAGpD,aAAa,CAACF,eAAe,CAAC,CAAC,CAAC,EAAE+B,UAAU,EAAEC,YAAY,CAAC,EAAEmS,WAAW,CAAC7Q,WAAW,CAAC,CAAC,CAAC;;QAE9G,IAAI8Q,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC5S,iBAAiB,CAAC6C,QAAQ,EAAEM,UAAU,CAAC;QACzC;MACF;MAEA,IAAIvB,WAAW,GAAGiO,QAAQ,CAACjO,WAAW,IAAIgK,SAAS,CAAChK,WAAW,CAAC,CAAC;;MAEjE,IAAI+N,QAAQ,CAAC,cAAc,CAAC,IAAIzM,SAAS,IAAIyM,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACzEE,QAAQ,CAAC1N,YAAY,GAAG2B,KAAK,CAAC+O,gBAAgB,IAAI,CAAC3P,SAAS,IAAIY,KAAK,CAACgP,mBAAmB,GAAGvT,mBAAmB,CAACuE,KAAK,CAAC3B,YAAY,EAAEP,WAAW,CAAC,GAAGkC,KAAK,CAAC3B,YAAY;MACvK,CAAC,MAAM,IAAI,CAACe,SAAS,IAAIY,KAAK,CAACiP,gBAAgB,EAAE;QAC/C,IAAIC,gBAAgB,GAAGxU,aAAa,CAAC,CAAC,CAAC,EAAEoD,WAAW,CAAC;QAErD,OAAOoR,gBAAgB,CAAC3S,UAAU,CAAC;QACnCwP,QAAQ,CAAC1N,YAAY,GAAGsD,MAAM,CAACC,IAAI,CAACsN,gBAAgB,CAAC,CAAC9J,GAAG,CAAC,UAAUtD,GAAG,EAAE;UACvE,OAAOoN,gBAAgB,CAACpN,GAAG,CAAC,CAACA,GAAG;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAAC1C,SAAS,IAAIY,KAAK,CAACmP,mBAAmB,EAAE;QAClDpD,QAAQ,CAAC1N,YAAY,GAAG2B,KAAK,CAAC+O,gBAAgB,IAAI/O,KAAK,CAACgP,mBAAmB,GAAGvT,mBAAmB,CAACuE,KAAK,CAACmP,mBAAmB,EAAErR,WAAW,CAAC,GAAGkC,KAAK,CAACmP,mBAAmB;MACvK;MAEA,IAAI,CAACpD,QAAQ,CAAC1N,YAAY,EAAE;QAC1B,OAAO0N,QAAQ,CAAC1N,YAAY;MAC9B,CAAC,CAAC;;MAGF,IAAIU,QAAQ,IAAIgN,QAAQ,CAAC1N,YAAY,EAAE;QACrC,IAAIW,YAAY,GAAGjD,eAAe,CAACgD,QAAQ,IAAI+I,SAAS,CAAC/I,QAAQ,EAAEgN,QAAQ,CAAC1N,YAAY,IAAIyJ,SAAS,CAACzJ,YAAY,EAAEgB,UAAU,CAAC;QAC/H0M,QAAQ,CAAC/M,YAAY,GAAGA,YAAY;MACtC,CAAC,CAAC;;MAGF,IAAIgB,KAAK,CAAC0K,UAAU,EAAE;QACpB,IAAImB,QAAQ,CAAC,cAAc,CAAC,EAAE;UAC5BE,QAAQ,CAAC/N,YAAY,GAAGtC,gBAAgB,CAACsE,KAAK,CAAChC,YAAY,EAAEgC,KAAK,CAAC;QACrE,CAAC,MAAM,IAAI,CAACZ,SAAS,IAAIY,KAAK,CAACoP,mBAAmB,EAAE;UAClDrD,QAAQ,CAAC/N,YAAY,GAAGtC,gBAAgB,CAACsE,KAAK,CAACoP,mBAAmB,EAAEpP,KAAK,CAAC;QAC5E;MACF,CAAC,CAAC;;MAGF,IAAIA,KAAK,CAACyK,SAAS,EAAE;QACnB,IAAI4E,gBAAgB;QAEpB,IAAIxD,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC3BwD,gBAAgB,GAAG7T,gBAAgB,CAACwE,KAAK,CAAC/B,WAAW,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,MAAM,IAAI,CAACmB,SAAS,IAAIY,KAAK,CAACsP,kBAAkB,EAAE;UACjDD,gBAAgB,GAAG7T,gBAAgB,CAACwE,KAAK,CAACsP,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,MAAM,IAAIvQ,QAAQ,EAAE;UACnB;UACAsQ,gBAAgB,GAAG7T,gBAAgB,CAACwE,KAAK,CAAC/B,WAAW,CAAC,IAAI;YACxDA,WAAW,EAAE6J,SAAS,CAAC7J,WAAW;YAClCC,eAAe,EAAE4J,SAAS,CAAC5J;UAC7B,CAAC;QACH;QAEA,IAAImR,gBAAgB,EAAE;UACpB,IAAIE,iBAAiB,GAAGF,gBAAgB;YACpCG,qBAAqB,GAAGD,iBAAiB,CAACtR,WAAW;YACrDA,WAAW,GAAGuR,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;YAC3EC,qBAAqB,GAAGF,iBAAiB,CAACrR,eAAe;YACzDA,eAAe,GAAGuR,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;UAEnF,IAAI,CAACzP,KAAK,CAAC8F,aAAa,EAAE;YACxB,IAAI4J,WAAW,GAAGhT,YAAY,CAACuB,WAAW,EAAE,IAAI,EAAEH,WAAW,CAAC;YAC9DG,WAAW,GAAGyR,WAAW,CAACzR,WAAW;YACrCC,eAAe,GAAGwR,WAAW,CAACxR,eAAe;UAC/C;UAEA6N,QAAQ,CAAC9N,WAAW,GAAGA,WAAW;UAClC8N,QAAQ,CAAC7N,eAAe,GAAGA,eAAe;QAC5C;MACF,CAAC,CAAC;;MAGF,IAAI2N,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1BE,QAAQ,CAAC5N,UAAU,GAAG6B,KAAK,CAAC7B,UAAU;MACxC;MAEA,OAAO4N,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOlP,IAAI;AACb,CAAC,CAAC5B,KAAK,CAAC0U,SAAS,CAAC;AAElB9S,IAAI,CAAC+S,YAAY,GAAG;EAClBjD,SAAS,EAAE,SAAS;EACpBG,QAAQ,EAAE,KAAK;EACfI,QAAQ,EAAE,IAAI;EACdxC,UAAU,EAAE,IAAI;EAChB1F,QAAQ,EAAE,KAAK;EACfyF,SAAS,EAAE,KAAK;EAChBgB,QAAQ,EAAE,KAAK;EACf3F,aAAa,EAAE,KAAK;EACpBuH,SAAS,EAAE,KAAK;EAChB2B,mBAAmB,EAAE,IAAI;EACzBD,gBAAgB,EAAE,KAAK;EACvBE,gBAAgB,EAAE,KAAK;EACvBE,mBAAmB,EAAE,EAAE;EACvBG,kBAAkB,EAAE,EAAE;EACtBF,mBAAmB,EAAE,EAAE;EACvBxB,mBAAmB,EAAEjR,aAAa;EAClCwE,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,IAAI;EACb;AACF,CAAC;AACDtE,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;AACxB,eAAeI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}