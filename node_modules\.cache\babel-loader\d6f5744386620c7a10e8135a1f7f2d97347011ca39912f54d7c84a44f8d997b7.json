{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\utentePDV.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiUtentePuntoVendita - operazioni sull'aggiunta utente punto vendita\n*\n*/\nimport React, { useState, useEffect, Fragment, useRef } from 'react';\nimport classNames from 'classnames/bind';\nimport ModificaPassword from './modificaPassword';\nimport { scrollTo } from '../components/generalizzazioni/scrollToElement';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { pdv, ring } from '../components/route';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dialog } from 'primereact/dialog';\nimport { Divider } from 'primereact/divider';\nimport { Form, Field } from 'react-final-form';\nimport { Password } from 'primereact/password';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\nimport '../css/FormDemo.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UtentePDV = () => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState([]);\n  const [result, setResult] = useState([]);\n  const [visualizza, setVisualizza] = useState(true);\n  const [showMessage, setShowMessage] = useState(false);\n  const [openForm, setOpenForm] = useState('card d-none');\n  const [formData, setFormData] = useState({});\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function trovaRisultato() {\n      var url = 'registry/?id=' + localStorage.getItem(\"datiComodo\");\n      await APIRequest('GET', url).then(res => {\n        setResults(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, []);\n  if (results.length === 0) {\n    return null;\n  }\n  const validate = data => {\n    let errors = {};\n    if (!data.email) {\n      errors.email = Costanti.EmailObb;\n    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$/i.test(data.email)) {\n      errors.email = Costanti.EmailNoVal;\n    }\n    if (!data.password) {\n      errors.password = Costanti.PassObb;\n    }\n    if (!data.confirmPassword) {\n      errors.confirmPassword = Costanti.ConfPassObb;\n    } else if (data.confirmPassword !== data.password) {\n      errors.confirmPassword = Costanti.PassValid;\n    }\n    return errors;\n  };\n  const onSubmit = async (data, form) => {\n    setFormData(data);\n    let contenuto = {\n      username: data.email,\n      password: data.password,\n      role: localStorage.getItem(\"role\") !== 'CHAIN' ? pdv : ring,\n      idRegistry: localStorage.getItem(\"datiComodo\")\n    };\n    APIRequest('POST', 'user/', contenuto).then(res => {\n      console.log(res.data);\n      setShowMessage(true);\n      localStorage.setItem(\"datiComodo\", 0);\n      form.restart();\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Ci dispiace',\n        detail: \"Il nome utente inserito \\xE8 gi\\xE0 in uso. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  const openCloseForm = () => {\n    if (openForm === 'card d-none') {\n      setOpenForm('card');\n    } else {\n      setOpenForm('card d-none');\n    }\n  };\n  /* Scrolling to the form element. */\n  const scrollElement = document.getElementById('createUserAccountForm');\n  scrollTo({\n    scrollElement: scrollElement,\n    block: 'start'\n  });\n  const isFormFieldValid = meta => !!(meta.touched && meta.error);\n  const getFormErrorMessage = meta => {\n    return isFormFieldValid(meta) && /*#__PURE__*/_jsxDEV(\"small\", {\n      className: \"p-error\",\n      children: meta.error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 42\n    }, this);\n  };\n  const dialogFooter = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-d-flex p-jc-center\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      label: \"OK\",\n      className: \"p-button-text\",\n      autoFocus: true,\n      onClick: () => setShowMessage(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 26\n  }, this);\n  const passwordHeader = /*#__PURE__*/_jsxDEV(\"h6\", {\n    children: Costanti.SelectPass\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 28\n  }, this);\n  const passwordFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"p-mt-2\",\n      children: Costanti.PassDevCont\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"p-pl-2 p-ml-2 p-mt-0\",\n      style: {\n        lineHeight: '1.5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Minuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Maiuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.AlmUnNum\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Alm8Car\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 9\n  }, this);\n  const modificaPassword = (e, element) => {\n    setResult(element);\n    setVisualizza(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-demo\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 13\n    }, this), visualizza && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-fluid p-grid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-col-12 pb-0 mb-0 p-md-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-credit-card mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 73\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.pIva\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 115\n                  }, this), \": \", results.pIva]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-id-card mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 73\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.rSociale\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 111\n                  }, this), \": \", results.firstName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"my-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 29\n          }, this), results.users.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6 d-none d-sm-block\",\n                children: /*#__PURE__*/_jsxDEV(\"b\", {\n                  className: \"ml-3\",\n                  children: [Costanti.NomeUtente, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-3 d-none d-sm-block\",\n                children: /*#__PURE__*/_jsxDEV(\"b\", {\n                  children: [Costanti.Ruolo, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-3 d-none d-sm-block\",\n                children: /*#__PURE__*/_jsxDEV(\"b\", {\n                  children: [Costanti.Azioni, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"dett-aff\",\n              children: results.users.map((element, index) => {\n                // Return the element. Also pass key     \n                return /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: (element.role === 'PDV' || element.role === 'RING') && /*#__PURE__*/_jsxDEV(Fragment, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-sm-9\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"row\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"col-12 mb-sm-0 col-sm-8 mb-2 \",\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  className: \"d-block d-sm-none\",\n                                  children: [Costanti.NomeUtente, \":\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 168,\n                                  columnNumber: 85\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"text-break\",\n                                  children: element.username\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 169,\n                                  columnNumber: 85\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 167,\n                                columnNumber: 81\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"col-12 col-sm-4\",\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  className: \"d-block d-sm-none\",\n                                  children: [Costanti.Ruolo, \":\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 172,\n                                  columnNumber: 85\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"text-break\",\n                                  children: element.role\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 173,\n                                  columnNumber: 85\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 171,\n                                columnNumber: 81\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 166,\n                              columnNumber: 77\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 165,\n                            columnNumber: 73\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-sm-3 d-flex align-items-center my-2 mt-sm-0\",\n                            children: /*#__PURE__*/_jsxDEV(Button, {\n                              className: \"justify-content-center btn-sm w-auto\",\n                              onClick: e => modificaPassword(e, element),\n                              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                                className: \"pi pi-pencil size\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 179,\n                                columnNumber: 81\n                              }, this), \" \", /*#__PURE__*/_jsxDEV(\"small\", {\n                                children: Costanti.ModificaPassword\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 179,\n                                columnNumber: 119\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 178,\n                              columnNumber: 77\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 177,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 164,\n                          columnNumber: 69\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 163,\n                        columnNumber: 65\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 61\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 57\n                  }, this)\n                }, void 0, false);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 21\n      }, this), localStorage.getItem(\"role\") !== 'CHAIN' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button mx-auto w-auto px-4 justify-content-center mb-4\",\n          onClick: () => openCloseForm() /* icon=\"pi pi-search-plus\" */,\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-user-plus mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 171\n          }, this), \" \", Costanti.AggUser, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 27\n      }, this) : results.users.length < 1 && localStorage.getItem(\"role\") === 'CHAIN' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button mx-auto w-auto px-4 justify-content-center mb-4\",\n          onClick: () => openCloseForm() /* icon=\"pi pi-search-plus\" */,\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-user-plus mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 175\n          }, this), \" \", Costanti.AggUser, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 33\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 31\n      }, this) : '', /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: showMessage,\n        onHide: () => setShowMessage(false),\n        position: \"top\",\n        footer: dialogFooter,\n        showHeader: false,\n        breakpoints: {\n          '960px': '80vw'\n        },\n        style: {\n          width: '30vw'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-check-circle\",\n            style: {\n              fontSize: '5rem',\n              color: 'var(--green-500)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: Costanti.RegSucc\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              lineHeight: 1.5,\n              textIndent: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 33\n            }, this), \" \", Costanti.GiaReg, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: formData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 58\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 80\n            }, this), \" \", Costanti.ConEmail, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: formData.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 108\n            }, this), \" .\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-d-flex p-jc-center px-0 px-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: openForm,\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: onSubmit,\n            initialValues: {\n              email: '',\n              password: ''\n            },\n            validate: validate,\n            render: _ref => {\n              let {\n                handleSubmit\n              } = _ref;\n              return /*#__PURE__*/_jsxDEV(\"form\", {\n                id: \"createUserAccountForm\",\n                onSubmit: handleSubmit,\n                className: \"p-fluid\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-center w-100 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.CreaUtenza\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 76\n                  }, this), \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-chevron-right\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 115\n                  }, this), \" \", Costanti.PDV]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Field, {\n                  name: \"email\",\n                  render: _ref2 => {\n                    let {\n                      input,\n                      meta\n                    } = _ref2;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-field\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"p-float-label p-input-icon-right\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"pi pi-envelope\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 224,\n                          columnNumber: 49\n                        }, this), /*#__PURE__*/_jsxDEV(InputText, _objectSpread(_objectSpread({\n                          id: \"email\"\n                        }, input), {}, {\n                          className: classNames({\n                            'p-invalid': isFormFieldValid(meta)\n                          })\n                        }), void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 225,\n                          columnNumber: 49\n                        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                          htmlFor: \"email\",\n                          className: classNames({\n                            'p-error': isFormFieldValid(meta)\n                          }),\n                          children: [Costanti.NomeUtente, \"*\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 226,\n                          columnNumber: 49\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 45\n                      }, this), getFormErrorMessage(meta)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 41\n                    }, this);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Field, {\n                  name: \"password\",\n                  render: _ref3 => {\n                    let {\n                      input,\n                      meta\n                    } = _ref3;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-field\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"p-float-label\",\n                        children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                          id: \"password\"\n                        }, input), {}, {\n                          toggleMask: true,\n                          className: classNames({\n                            'p-invalid': isFormFieldValid(meta)\n                          }),\n                          header: passwordHeader,\n                          footer: passwordFooter\n                        }), void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 49\n                        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                          htmlFor: \"password\",\n                          className: classNames({\n                            'p-error': isFormFieldValid(meta)\n                          }),\n                          children: \"Password*\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 235,\n                          columnNumber: 49\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 45\n                      }, this), getFormErrorMessage(meta)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 41\n                    }, this);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Field, {\n                  name: \"confirmPassword\",\n                  render: _ref4 => {\n                    let {\n                      input,\n                      meta\n                    } = _ref4;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-field\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"p-float-label\",\n                        children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                          id: \"confirmPassword\"\n                        }, input), {}, {\n                          className: classNames({\n                            'p-invalid': isFormFieldValid(meta)\n                          }),\n                          toggleMask: true,\n                          feedback: false\n                        }), void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 243,\n                          columnNumber: 49\n                        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                          htmlFor: \"confirmPassword\",\n                          className: classNames({\n                            'p-error': isFormFieldValid(meta)\n                          }),\n                          children: [Costanti.Conferma, \" password*\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 244,\n                          columnNumber: 49\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 45\n                      }, this), getFormErrorMessage(meta)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 41\n                    }, this);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"buttonForm\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"submit\",\n                    id: \"user\",\n                    className: \"mx-0 mt-3 d-flex justify-content-center w-auto px-5\",\n                    children: [\" \", Costanti.salva, \" \"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 33\n              }, this);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true), visualizza === false && /*#__PURE__*/_jsxDEV(ModificaPassword, {\n      results: result\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 9\n  }, this);\n};\n_s(UtentePDV, \"Q8VRe+KVlEMBc5k9krpkFAp/3WM=\");\n_c = UtentePDV;\nexport default UtentePDV;\nvar _c;\n$RefreshReg$(_c, \"UtentePDV\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "useRef", "classNames", "ModificaPassword", "scrollTo", "stopLoading", "pdv", "ring", "<PERSON><PERSON>", "InputText", "Dialog", "Divider", "Form", "Field", "Password", "<PERSON><PERSON>", "APIRequest", "Toast", "jsxDEV", "_jsxDEV", "_Fragment", "UtentePDV", "_s", "results", "setResults", "result", "setResult", "visualizza", "setVisualizza", "showMessage", "setShowMessage", "openForm", "setOpenForm", "formData", "setFormData", "toast", "trovaRisultato", "url", "localStorage", "getItem", "then", "res", "data", "catch", "e", "console", "log", "length", "validate", "errors", "email", "<PERSON>ail<PERSON>bb", "test", "EmailNoVal", "password", "PassObb", "confirmPassword", "ConfPassObb", "PassValid", "onSubmit", "form", "contenuto", "username", "role", "idRegistry", "setItem", "restart", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "openCloseForm", "scrollElement", "document", "getElementById", "block", "isFormFieldValid", "meta", "touched", "error", "getFormErrorMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dialogFooter", "label", "autoFocus", "onClick", "passwordHeader", "SelectPass", "passwordFooter", "PassDevCont", "style", "lineHeight", "Minus<PERSON>", "<PERSON><PERSON><PERSON>", "AlmUnNum", "Alm8Car", "modificaPassword", "element", "ref", "pIva", "rSociale", "firstName", "users", "NomeUtente", "<PERSON><PERSON><PERSON>", "Azioni", "map", "index", "AggUser", "visible", "onHide", "position", "footer", "showHeader", "breakpoints", "width", "fontSize", "color", "RegSucc", "textIndent", "GiaReg", "name", "ConEmail", "initialValues", "render", "_ref", "handleSubmit", "id", "CreaUtenza", "PDV", "_ref2", "input", "_objectSpread", "htmlFor", "_ref3", "toggleMask", "header", "_ref4", "feedback", "Conferma", "type", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/utentePDV.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiUtentePuntoVendita - operazioni sull'aggiunta utente punto vendita\n*\n*/\nimport React, { useState, useEffect, Fragment, useRef } from 'react';\nimport classNames from 'classnames/bind';\nimport ModificaPassword from './modificaPassword';\nimport { scrollTo } from '../components/generalizzazioni/scrollToElement';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { pdv, ring } from '../components/route';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dialog } from 'primereact/dialog';\nimport { Divider } from 'primereact/divider';\nimport { Form, Field } from 'react-final-form';\nimport { Password } from 'primereact/password';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\nimport '../css/FormDemo.css';\n\nconst UtentePDV = () => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState([]);\n    const [result, setResult] = useState([]);\n    const [visualizza, setVisualizza] = useState(true);\n    const [showMessage, setShowMessage] = useState(false);\n    const [openForm, setOpenForm] = useState('card d-none')\n    const [formData, setFormData] = useState({});\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function trovaRisultato() {\n            var url = 'registry/?id=' + localStorage.getItem(\"datiComodo\")\n            await APIRequest('GET', url)\n                .then(res => {\n                    setResults(res.data);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, []);\n    if (results.length === 0) {\n        return null;\n    }\n    const validate = (data) => {\n        let errors = {};\n        if (!data.email) {\n            errors.email = Costanti.EmailObb;\n        }\n        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$/i.test(data.email)) {\n            errors.email = Costanti.EmailNoVal;\n        }\n        if (!data.password) {\n            errors.password = Costanti.PassObb;\n        }\n        if (!data.confirmPassword) {\n            errors.confirmPassword = Costanti.ConfPassObb;\n        }\n        else if (data.confirmPassword !== data.password) {\n            errors.confirmPassword = Costanti.PassValid;\n        }\n        return errors;\n    };\n    const onSubmit = async (data, form) => {\n        setFormData(data);\n        let contenuto = {\n            username: data.email,\n            password: data.password,\n            role: localStorage.getItem(\"role\") !== 'CHAIN' ? pdv : ring,\n            idRegistry: localStorage.getItem(\"datiComodo\")\n        }\n        APIRequest('POST', 'user/', contenuto)\n            .then(res => {\n                console.log(res.data);\n                setShowMessage(true);\n                localStorage.setItem(\"datiComodo\", 0);\n                form.restart();\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch(e => {\n                console.log(e);\n                toast.current.show({ severity: 'error', summary: 'Ci dispiace', detail: `Il nome utente inserito è già in uso. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    };\n    const openCloseForm = () => {\n        if (openForm === 'card d-none') {\n            setOpenForm('card')\n        } else {\n            setOpenForm('card d-none')\n        }\n    }\n    /* Scrolling to the form element. */\n    const scrollElement = document.getElementById('createUserAccountForm');\n    scrollTo({ scrollElement: scrollElement, block: 'start' });\n\n    const isFormFieldValid = (meta) => !!(meta.touched && meta.error);\n    const getFormErrorMessage = (meta) => {\n        return isFormFieldValid(meta) && <small className=\"p-error\">{meta.error}</small>;\n    };\n    const dialogFooter = <div className=\"p-d-flex p-jc-center\"><Button label=\"OK\" className=\"p-button-text\" autoFocus onClick={() => setShowMessage(false)} /></div>;\n    const passwordHeader = <h6>{Costanti.SelectPass}</h6>;\n    const passwordFooter = (\n        <React.Fragment>\n            <Divider />\n            <p className=\"p-mt-2\">{Costanti.PassDevCont}</p>\n            <ul className=\"p-pl-2 p-ml-2 p-mt-0\" style={{ lineHeight: '1.5' }}>\n                <li>{Costanti.Minuscola}</li>\n                <li>{Costanti.Maiuscola}</li>\n                <li>{Costanti.AlmUnNum}</li>\n                <li>{Costanti.Alm8Car}</li>\n            </ul>\n        </React.Fragment>\n    );\n    const modificaPassword = (e, element) => {\n        setResult(element)\n        setVisualizza(false)\n    }\n    return (\n        <div className=\"form-demo\">\n            <Toast ref={toast} />\n            {visualizza &&\n                <>\n                    <div className=\"p-fluid p-grid\">\n                        <div className=\"p-field p-col-12 pb-0 mb-0 p-md-4\">\n                            <div className='row'>\n                                <div className=\"col-12 mb-0\">\n                                    <ul className=\"list-group\">\n                                        <li className=\"list-group-item\"><i className=\"pi pi-credit-card mr-3\"></i><strong>{Costanti.pIva}</strong>: {results.pIva}</li>\n                                        <li className=\"list-group-item\"><i className=\"pi pi-id-card mr-3\"></i><strong>{Costanti.rSociale}</strong>: {results.firstName}</li>\n                                    </ul>\n                                    <hr className='my-4' />\n                                </div>\n                            </div>\n                            {results.users.length > 0 &&\n                                <>\n                                    <div className=\"row\">\n                                        <div className=\"col-6 d-none d-sm-block\">\n                                            <b className=\"ml-3\">{Costanti.NomeUtente}:</b>\n                                        </div>\n                                        <div className=\"col-3 d-none d-sm-block\">\n                                            <b>{Costanti.Ruolo}:</b>\n                                        </div>\n                                        <div className=\"col-3 d-none d-sm-block\">\n                                            <b>{Costanti.Azioni}:</b>\n                                        </div>\n                                    </div>\n                                    <ul className=\"dett-aff\">\n                                        {results.users.map((element, index) => {\n                                            // Return the element. Also pass key     \n                                            return (\n                                                <>\n                                                    {(element.role === 'PDV' || element.role === 'RING') &&\n                                                        <Fragment key={index}>\n                                                            <div>\n                                                                <li>\n                                                                    <div className=\"row\">\n                                                                        <div className=\"col-12 col-sm-9\">\n                                                                            <div className=\"row\">\n                                                                                <div className=\"col-12 mb-sm-0 col-sm-8 mb-2 \">\n                                                                                    <strong className='d-block d-sm-none'>{Costanti.NomeUtente}:</strong>\n                                                                                    <span className='text-break'>{element.username}</span>\n                                                                                </div>\n                                                                                <div className=\"col-12 col-sm-4\">\n                                                                                    <strong className='d-block d-sm-none'>{Costanti.Ruolo}:</strong>\n                                                                                    <span className='text-break'>{element.role}</span>\n                                                                                </div>\n                                                                            </div>\n                                                                        </div>\n                                                                        <div className=\"col-12 col-sm-3 d-flex align-items-center my-2 mt-sm-0\">\n                                                                            <Button className=\"justify-content-center btn-sm w-auto\" onClick={(e) => modificaPassword(e, element)}>\n                                                                                <i className=\"pi pi-pencil size\"></i> <small>{Costanti.ModificaPassword}</small>\n                                                                            </Button>\n                                                                        </div>\n                                                                    </div>\n                                                                </li>\n                                                            </div>\n                                                        </Fragment>\n                                                    }\n                                                </>\n                                            )\n                                        })\n                                        }\n                                    </ul>\n                                </>\n                            }\n                        </div>\n                    </div>\n                    {localStorage.getItem(\"role\") !== 'CHAIN'\n                        ? <div className=\"d-flex\">\n                            <Button className=\"p-button mx-auto w-auto px-4 justify-content-center mb-4\" onClick={() => openCloseForm()} /* icon=\"pi pi-search-plus\" */ > <i className=\"pi pi-user-plus mr-2\"></i> {Costanti.AggUser} </Button>\n                        </div>\n                        : (results.users.length < 1 && localStorage.getItem(\"role\") === 'CHAIN'\n                            ? <div className=\"d-flex\">\n                                <Button className=\"p-button mx-auto w-auto px-4 justify-content-center mb-4\" onClick={() => openCloseForm()} /* icon=\"pi pi-search-plus\" */ > <i className=\"pi pi-user-plus mr-2\"></i> {Costanti.AggUser} </Button>\n                            </div>\n                            : ''\n                            )\n                    }\n                    <Dialog visible={showMessage} onHide={() => setShowMessage(false)} position=\"top\" footer={dialogFooter} showHeader={false} breakpoints={{ '960px': '80vw' }} style={{ width: '30vw' }}>\n                        <div className=\"p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3\">\n                            <i className=\"pi pi-check-circle\" style={{ fontSize: '5rem', color: 'var(--green-500)' }}></i>\n                            <h5>{Costanti.RegSucc}</h5>\n                            <p style={{ lineHeight: 1.5, textIndent: '1rem' }}>\n                                <br /> {Costanti.GiaReg} <b>{formData.name}</b><br /> {Costanti.ConEmail}: <b>{formData.email}</b> .\n                            </p>\n                        </div>\n                    </Dialog>\n                    <div className=\"p-d-flex p-jc-center px-0 px-md-3\">\n                        <div className={openForm}>\n                            <Form onSubmit={onSubmit} initialValues={{ email: '', password: '' }} validate={validate} render={({ handleSubmit }) => (\n                                <form id=\"createUserAccountForm\" onSubmit={handleSubmit} className=\"p-fluid\">\n                                    <h5 className='text-center w-100 mb-4'><strong>{Costanti.CreaUtenza}</strong> <i className=\"pi pi-chevron-right\"></i> {Costanti.PDV}</h5>\n                                    <Field name=\"email\" render={({ input, meta }) => (\n                                        <div className=\"p-field\">\n                                            <span className=\"p-float-label p-input-icon-right\">\n                                                <i className=\"pi pi-envelope\" />\n                                                <InputText id=\"email\" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />\n                                                <label htmlFor=\"email\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.NomeUtente}*</label>\n                                            </span>\n                                            {getFormErrorMessage(meta)}\n                                        </div>\n                                    )} />\n                                    <Field name=\"password\" render={({ input, meta }) => (\n                                        <div className=\"p-field\">\n                                            <span className=\"p-float-label\">\n                                                <Password id=\"password\" {...input} toggleMask className={classNames({ 'p-invalid': isFormFieldValid(meta) })} header={passwordHeader} footer={passwordFooter} />\n                                                <label htmlFor=\"password\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>Password*</label>\n                                            </span>\n                                            {getFormErrorMessage(meta)}\n                                        </div>\n                                    )} />\n                                    <Field name=\"confirmPassword\" render={({ input, meta }) => (\n                                        <div className=\"p-field\">\n                                            <span className=\"p-float-label\">\n                                                <Password id=\"confirmPassword\" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} toggleMask feedback={false} />\n                                                <label htmlFor=\"confirmPassword\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Conferma} password*</label>\n                                            </span>\n                                            {getFormErrorMessage(meta)}\n                                        </div>\n                                    )} />\n                                    <div className=\"buttonForm\">\n                                        {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                                        <Button type=\"submit\" id=\"user\" className=\"mx-0 mt-3 d-flex justify-content-center w-auto px-5\" > {Costanti.salva} </Button>\n                                    </div>\n                                </form>\n                            )} />\n                        </div>\n                    </div>\n                </>\n            }\n            {visualizza === false &&\n                <ModificaPassword results={result} />\n            }\n        </div>\n    );\n}\n\nexport default UtentePDV;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACpE,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,QAAQ,QAAQ,gDAAgD;AACzE,SAASC,WAAW,QAAQ,4CAA4C;AACxE,SAASC,GAAG,EAAEC,IAAI,QAAQ,qBAAqB;AAC/C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,IAAI,EAAEC,KAAK,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,mBAAmB;AAC1B,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAnB,QAAA,IAAAoB,SAAA;AAE7B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,aAAa,CAAC;EACvD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMqC,KAAK,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAF,SAAS,CAAC,MAAM;IACZ,eAAeqC,cAAcA,CAAA,EAAG;MAC5B,IAAIC,GAAG,GAAG,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC9D,MAAMvB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBG,IAAI,CAACC,GAAG,IAAI;QACTjB,UAAU,CAACiB,GAAG,CAACC,IAAI,CAAC;MACxB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACNvC,WAAW,CAAC,CAAC;IACjB;IACA+B,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIb,OAAO,CAACwB,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACf;EACA,MAAMC,QAAQ,GAAIN,IAAI,IAAK;IACvB,IAAIO,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,CAACP,IAAI,CAACQ,KAAK,EAAE;MACbD,MAAM,CAACC,KAAK,GAAGnC,QAAQ,CAACoC,QAAQ;IACpC,CAAC,MACI,IAAI,CAAC,2CAA2C,CAACC,IAAI,CAACV,IAAI,CAACQ,KAAK,CAAC,EAAE;MACpED,MAAM,CAACC,KAAK,GAAGnC,QAAQ,CAACsC,UAAU;IACtC;IACA,IAAI,CAACX,IAAI,CAACY,QAAQ,EAAE;MAChBL,MAAM,CAACK,QAAQ,GAAGvC,QAAQ,CAACwC,OAAO;IACtC;IACA,IAAI,CAACb,IAAI,CAACc,eAAe,EAAE;MACvBP,MAAM,CAACO,eAAe,GAAGzC,QAAQ,CAAC0C,WAAW;IACjD,CAAC,MACI,IAAIf,IAAI,CAACc,eAAe,KAAKd,IAAI,CAACY,QAAQ,EAAE;MAC7CL,MAAM,CAACO,eAAe,GAAGzC,QAAQ,CAAC2C,SAAS;IAC/C;IACA,OAAOT,MAAM;EACjB,CAAC;EACD,MAAMU,QAAQ,GAAG,MAAAA,CAAOjB,IAAI,EAAEkB,IAAI,KAAK;IACnC1B,WAAW,CAACQ,IAAI,CAAC;IACjB,IAAImB,SAAS,GAAG;MACZC,QAAQ,EAAEpB,IAAI,CAACQ,KAAK;MACpBI,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;MACvBS,IAAI,EAAEzB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,OAAO,GAAGjC,GAAG,GAAGC,IAAI;MAC3DyD,UAAU,EAAE1B,YAAY,CAACC,OAAO,CAAC,YAAY;IACjD,CAAC;IACDvB,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE6C,SAAS,CAAC,CACjCrB,IAAI,CAACC,GAAG,IAAI;MACTI,OAAO,CAACC,GAAG,CAACL,GAAG,CAACC,IAAI,CAAC;MACrBZ,cAAc,CAAC,IAAI,CAAC;MACpBQ,YAAY,CAAC2B,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MACrCL,IAAI,CAACM,OAAO,CAAC,CAAC;MACdC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC3B,KAAK,CAACC,CAAC,IAAI;MAAA,IAAA2B,WAAA,EAAAC,YAAA;MACV3B,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdT,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,aAAa;QAAEC,MAAM,mEAAAC,MAAA,CAA6D,EAAAP,WAAA,GAAA3B,CAAC,CAACmC,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAY7B,IAAI,MAAKsC,SAAS,IAAAR,YAAA,GAAG5B,CAAC,CAACmC,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAY9B,IAAI,GAAGE,CAAC,CAACqC,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACrN,CAAC,CAAC;EACV,CAAC;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIpD,QAAQ,KAAK,aAAa,EAAE;MAC5BC,WAAW,CAAC,MAAM,CAAC;IACvB,CAAC,MAAM;MACHA,WAAW,CAAC,aAAa,CAAC;IAC9B;EACJ,CAAC;EACD;EACA,MAAMoD,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;EACtElF,QAAQ,CAAC;IAAEgF,aAAa,EAAEA,aAAa;IAAEG,KAAK,EAAE;EAAQ,CAAC,CAAC;EAE1D,MAAMC,gBAAgB,GAAIC,IAAI,IAAK,CAAC,EAAEA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACE,KAAK,CAAC;EACjE,MAAMC,mBAAmB,GAAIH,IAAI,IAAK;IAClC,OAAOD,gBAAgB,CAACC,IAAI,CAAC,iBAAItE,OAAA;MAAO0E,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAEL,IAAI,CAACE;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACpF,CAAC;EACD,MAAMC,YAAY,gBAAGhF,OAAA;IAAK0E,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eAAC3E,OAAA,CAACX,MAAM;MAAC4F,KAAK,EAAC,IAAI;MAACP,SAAS,EAAC,eAAe;MAACQ,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAAC,KAAK;IAAE;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAChK,MAAMK,cAAc,gBAAGpF,OAAA;IAAA2E,QAAA,EAAK/E,QAAQ,CAACyF;EAAU;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrD,MAAMO,cAAc,gBAChBtF,OAAA,CAACtB,KAAK,CAACG,QAAQ;IAAA8F,QAAA,gBACX3E,OAAA,CAACR,OAAO;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX/E,OAAA;MAAG0E,SAAS,EAAC,QAAQ;MAAAC,QAAA,EAAE/E,QAAQ,CAAC2F;IAAW;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChD/E,OAAA;MAAI0E,SAAS,EAAC,sBAAsB;MAACc,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAd,QAAA,gBAC9D3E,OAAA;QAAA2E,QAAA,EAAK/E,QAAQ,CAAC8F;MAAS;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7B/E,OAAA;QAAA2E,QAAA,EAAK/E,QAAQ,CAAC+F;MAAS;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7B/E,OAAA;QAAA2E,QAAA,EAAK/E,QAAQ,CAACgG;MAAQ;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5B/E,OAAA;QAAA2E,QAAA,EAAK/E,QAAQ,CAACiG;MAAO;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CACnB;EACD,MAAMe,gBAAgB,GAAGA,CAACrE,CAAC,EAAEsE,OAAO,KAAK;IACrCxF,SAAS,CAACwF,OAAO,CAAC;IAClBtF,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,oBACIT,OAAA;IAAK0E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtB3E,OAAA,CAACF,KAAK;MAACkG,GAAG,EAAEhF;IAAM;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACpBvE,UAAU,iBACPR,OAAA,CAAAC,SAAA;MAAA0E,QAAA,gBACI3E,OAAA;QAAK0E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC3B3E,OAAA;UAAK0E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9C3E,OAAA;YAAK0E,SAAS,EAAC,KAAK;YAAAC,QAAA,eAChB3E,OAAA;cAAK0E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB3E,OAAA;gBAAI0E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACtB3E,OAAA;kBAAI0E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAAC3E,OAAA;oBAAG0E,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAA/E,OAAA;oBAAA2E,QAAA,EAAS/E,QAAQ,CAACqG;kBAAI;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC3E,OAAO,CAAC6F,IAAI;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/H/E,OAAA;kBAAI0E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAAC3E,OAAA;oBAAG0E,SAAS,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAA/E,OAAA;oBAAA2E,QAAA,EAAS/E,QAAQ,CAACsG;kBAAQ;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC3E,OAAO,CAAC+F,SAAS;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpI,CAAC,eACL/E,OAAA;gBAAI0E,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACL3E,OAAO,CAACgG,KAAK,CAACxE,MAAM,GAAG,CAAC,iBACrB5B,OAAA,CAAAC,SAAA;YAAA0E,QAAA,gBACI3E,OAAA;cAAK0E,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAChB3E,OAAA;gBAAK0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eACpC3E,OAAA;kBAAG0E,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAE/E,QAAQ,CAACyG,UAAU,EAAC,GAAC;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN/E,OAAA;gBAAK0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eACpC3E,OAAA;kBAAA2E,QAAA,GAAI/E,QAAQ,CAAC0G,KAAK,EAAC,GAAC;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACN/E,OAAA;gBAAK0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eACpC3E,OAAA;kBAAA2E,QAAA,GAAI/E,QAAQ,CAAC2G,MAAM,EAAC,GAAC;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN/E,OAAA;cAAI0E,SAAS,EAAC,UAAU;cAAAC,QAAA,EACnBvE,OAAO,CAACgG,KAAK,CAACI,GAAG,CAAC,CAACT,OAAO,EAAEU,KAAK,KAAK;gBACnC;gBACA,oBACIzG,OAAA,CAAAC,SAAA;kBAAA0E,QAAA,EACK,CAACoB,OAAO,CAACnD,IAAI,KAAK,KAAK,IAAImD,OAAO,CAACnD,IAAI,KAAK,MAAM,kBAC/C5C,OAAA,CAACnB,QAAQ;oBAAA8F,QAAA,eACL3E,OAAA;sBAAA2E,QAAA,eACI3E,OAAA;wBAAA2E,QAAA,eACI3E,OAAA;0BAAK0E,SAAS,EAAC,KAAK;0BAAAC,QAAA,gBAChB3E,OAAA;4BAAK0E,SAAS,EAAC,iBAAiB;4BAAAC,QAAA,eAC5B3E,OAAA;8BAAK0E,SAAS,EAAC,KAAK;8BAAAC,QAAA,gBAChB3E,OAAA;gCAAK0E,SAAS,EAAC,+BAA+B;gCAAAC,QAAA,gBAC1C3E,OAAA;kCAAQ0E,SAAS,EAAC,mBAAmB;kCAAAC,QAAA,GAAE/E,QAAQ,CAACyG,UAAU,EAAC,GAAC;gCAAA;kCAAAzB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,eACrE/E,OAAA;kCAAM0E,SAAS,EAAC,YAAY;kCAAAC,QAAA,EAAEoB,OAAO,CAACpD;gCAAQ;kCAAAiC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrD,CAAC,eACN/E,OAAA;gCAAK0E,SAAS,EAAC,iBAAiB;gCAAAC,QAAA,gBAC5B3E,OAAA;kCAAQ0E,SAAS,EAAC,mBAAmB;kCAAAC,QAAA,GAAE/E,QAAQ,CAAC0G,KAAK,EAAC,GAAC;gCAAA;kCAAA1B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,eAChE/E,OAAA;kCAAM0E,SAAS,EAAC,YAAY;kCAAAC,QAAA,EAAEoB,OAAO,CAACnD;gCAAI;kCAAAgC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACjD,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACN/E,OAAA;4BAAK0E,SAAS,EAAC,wDAAwD;4BAAAC,QAAA,eACnE3E,OAAA,CAACX,MAAM;8BAACqF,SAAS,EAAC,sCAAsC;8BAACS,OAAO,EAAG1D,CAAC,IAAKqE,gBAAgB,CAACrE,CAAC,EAAEsE,OAAO,CAAE;8BAAApB,QAAA,gBAClG3E,OAAA;gCAAG0E,SAAS,EAAC;8BAAmB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,KAAC,eAAA/E,OAAA;gCAAA2E,QAAA,EAAQ/E,QAAQ,CAACZ;8BAAgB;gCAAA4F,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5E;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC,GAvBK0B,KAAK;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwBV;gBAAC,gBAEjB,CAAC;cAEX,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF,CAAC;UAAA,eACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EACL5D,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,OAAO,gBACnCpB,OAAA;QAAK0E,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACrB3E,OAAA,CAACX,MAAM;UAACqF,SAAS,EAAC,0DAA0D;UAACS,OAAO,EAAEA,CAAA,KAAMnB,aAAa,CAAC,CAAE,CAAC;UAAAW,QAAA,GAAgC,GAAC,eAAA3E,OAAA;YAAG0E,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAAC8G,OAAO,EAAC,GAAC;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClN,CAAC,GACH3E,OAAO,CAACgG,KAAK,CAACxE,MAAM,GAAG,CAAC,IAAIT,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,OAAO,gBACjEpB,OAAA;QAAK0E,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACrB3E,OAAA,CAACX,MAAM;UAACqF,SAAS,EAAC,0DAA0D;UAACS,OAAO,EAAEA,CAAA,KAAMnB,aAAa,CAAC,CAAE,CAAC;UAAAW,QAAA,GAAgC,GAAC,eAAA3E,OAAA;YAAG0E,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAAC8G,OAAO,EAAC,GAAC;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClN,CAAC,GACJ,EACD,eAET/E,OAAA,CAACT,MAAM;QAACoH,OAAO,EAAEjG,WAAY;QAACkG,MAAM,EAAEA,CAAA,KAAMjG,cAAc,CAAC,KAAK,CAAE;QAACkG,QAAQ,EAAC,KAAK;QAACC,MAAM,EAAE9B,YAAa;QAAC+B,UAAU,EAAE,KAAM;QAACC,WAAW,EAAE;UAAE,OAAO,EAAE;QAAO,CAAE;QAACxB,KAAK,EAAE;UAAEyB,KAAK,EAAE;QAAO,CAAE;QAAAtC,QAAA,eAClL3E,OAAA;UAAK0E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBACzD3E,OAAA;YAAG0E,SAAS,EAAC,oBAAoB;YAACc,KAAK,EAAE;cAAE0B,QAAQ,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAmB;UAAE;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9F/E,OAAA;YAAA2E,QAAA,EAAK/E,QAAQ,CAACwH;UAAO;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3B/E,OAAA;YAAGwF,KAAK,EAAE;cAAEC,UAAU,EAAE,GAAG;cAAE4B,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,gBAC9C3E,OAAA;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,EAACnF,QAAQ,CAAC0H,MAAM,EAAC,GAAC,eAAAtH,OAAA;cAAA2E,QAAA,EAAI7D,QAAQ,CAACyG;YAAI;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA/E,OAAA;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,EAACnF,QAAQ,CAAC4H,QAAQ,EAAC,IAAE,eAAAxH,OAAA;cAAA2E,QAAA,EAAI7D,QAAQ,CAACiB;YAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,MACtG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT/E,OAAA;QAAK0E,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAC9C3E,OAAA;UAAK0E,SAAS,EAAE9D,QAAS;UAAA+D,QAAA,eACrB3E,OAAA,CAACP,IAAI;YAAC+C,QAAQ,EAAEA,QAAS;YAACiF,aAAa,EAAE;cAAE1F,KAAK,EAAE,EAAE;cAAEI,QAAQ,EAAE;YAAG,CAAE;YAACN,QAAQ,EAAEA,QAAS;YAAC6F,MAAM,EAAEC,IAAA;cAAA,IAAC;gBAAEC;cAAa,CAAC,GAAAD,IAAA;cAAA,oBAC/G3H,OAAA;gBAAM6H,EAAE,EAAC,uBAAuB;gBAACrF,QAAQ,EAAEoF,YAAa;gBAAClD,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACxE3E,OAAA;kBAAI0E,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAAC3E,OAAA;oBAAA2E,QAAA,EAAS/E,QAAQ,CAACkI;kBAAU;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,KAAC,eAAA/E,OAAA;oBAAG0E,SAAS,EAAC;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAACmI,GAAG;gBAAA;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzI/E,OAAA,CAACN,KAAK;kBAAC6H,IAAI,EAAC,OAAO;kBAACG,MAAM,EAAEM,KAAA;oBAAA,IAAC;sBAAEC,KAAK;sBAAE3D;oBAAK,CAAC,GAAA0D,KAAA;oBAAA,oBACxChI,OAAA;sBAAK0E,SAAS,EAAC,SAAS;sBAAAC,QAAA,gBACpB3E,OAAA;wBAAM0E,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC9C3E,OAAA;0BAAG0E,SAAS,EAAC;wBAAgB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChC/E,OAAA,CAACV,SAAS,EAAA4I,aAAA,CAAAA,aAAA;0BAACL,EAAE,EAAC;wBAAO,GAAKI,KAAK;0BAAEvD,SAAS,EAAE3F,UAAU,CAAC;4BAAE,WAAW,EAAEsF,gBAAgB,CAACC,IAAI;0BAAE,CAAC;wBAAE;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACnG/E,OAAA;0BAAOmI,OAAO,EAAC,OAAO;0BAACzD,SAAS,EAAE3F,UAAU,CAAC;4BAAE,SAAS,EAAEsF,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAE;0BAAAK,QAAA,GAAE/E,QAAQ,CAACyG,UAAU,EAAC,GAAC;wBAAA;0BAAAzB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjH,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;oBAAA;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACL/E,OAAA,CAACN,KAAK;kBAAC6H,IAAI,EAAC,UAAU;kBAACG,MAAM,EAAEU,KAAA;oBAAA,IAAC;sBAAEH,KAAK;sBAAE3D;oBAAK,CAAC,GAAA8D,KAAA;oBAAA,oBAC3CpI,OAAA;sBAAK0E,SAAS,EAAC,SAAS;sBAAAC,QAAA,gBACpB3E,OAAA;wBAAM0E,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC3B3E,OAAA,CAACL,QAAQ,EAAAuI,aAAA,CAAAA,aAAA;0BAACL,EAAE,EAAC;wBAAU,GAAKI,KAAK;0BAAEI,UAAU;0BAAC3D,SAAS,EAAE3F,UAAU,CAAC;4BAAE,WAAW,EAAEsF,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAE;0BAACgE,MAAM,EAAElD,cAAe;0BAAC0B,MAAM,EAAExB;wBAAe;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChK/E,OAAA;0BAAOmI,OAAO,EAAC,UAAU;0BAACzD,SAAS,EAAE3F,UAAU,CAAC;4BAAE,SAAS,EAAEsF,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAE;0BAAAK,QAAA,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;oBAAA;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACL/E,OAAA,CAACN,KAAK;kBAAC6H,IAAI,EAAC,iBAAiB;kBAACG,MAAM,EAAEa,KAAA;oBAAA,IAAC;sBAAEN,KAAK;sBAAE3D;oBAAK,CAAC,GAAAiE,KAAA;oBAAA,oBAClDvI,OAAA;sBAAK0E,SAAS,EAAC,SAAS;sBAAAC,QAAA,gBACpB3E,OAAA;wBAAM0E,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC3B3E,OAAA,CAACL,QAAQ,EAAAuI,aAAA,CAAAA,aAAA;0BAACL,EAAE,EAAC;wBAAiB,GAAKI,KAAK;0BAAEvD,SAAS,EAAE3F,UAAU,CAAC;4BAAE,WAAW,EAAEsF,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAE;0BAAC+D,UAAU;0BAACG,QAAQ,EAAE;wBAAM;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACxI/E,OAAA;0BAAOmI,OAAO,EAAC,iBAAiB;0BAACzD,SAAS,EAAE3F,UAAU,CAAC;4BAAE,SAAS,EAAEsF,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAE;0BAAAK,QAAA,GAAE/E,QAAQ,CAAC6I,QAAQ,EAAC,YAAU;wBAAA;0BAAA7D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClI,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;oBAAA;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACL/E,OAAA;kBAAK0E,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAEvB3E,OAAA,CAACX,MAAM;oBAACqJ,IAAI,EAAC,QAAQ;oBAACb,EAAE,EAAC,MAAM;oBAACnD,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,GAAE,GAAC,EAAC/E,QAAQ,CAAC+I,KAAK,EAAC,GAAC;kBAAA;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACR,CAAC,EAENvE,UAAU,KAAK,KAAK,iBACjBR,OAAA,CAAChB,gBAAgB;MAACoB,OAAO,EAAEE;IAAO;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAExC,CAAC;AAEd,CAAC;AAAA5E,EAAA,CA9OKD,SAAS;AAAA0I,EAAA,GAAT1I,SAAS;AAgPf,eAAeA,SAAS;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}