{"ast": null, "code": "import ReactDOM from 'react-dom';\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\n\nexport default function findDOMNode(node) {\n  if (node instanceof HTMLElement) {\n    return node;\n  }\n  return ReactDOM.findDOMNode(node);\n}", "map": {"version": 3, "names": ["ReactDOM", "findDOMNode", "node", "HTMLElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/findDOMNode.js"], "sourcesContent": ["import ReactDOM from 'react-dom';\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\n\nexport default function findDOMNode(node) {\n  if (node instanceof HTMLElement) {\n    return node;\n  }\n\n  return ReactDOM.findDOMNode(node);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC;AACA;AACA;;AAEA,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIA,IAAI,YAAYC,WAAW,EAAE;IAC/B,OAAOD,IAAI;EACb;EAEA,OAAOF,QAAQ,CAACC,WAAW,CAACC,IAAI,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}