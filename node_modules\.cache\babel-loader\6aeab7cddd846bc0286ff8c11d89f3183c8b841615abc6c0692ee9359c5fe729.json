{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BallTriangle = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar BallTriangle = function BallTriangle(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    height: props.height,\n    width: props.width,\n    stroke: props.color,\n    viewBox: \"0 0 57 57\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"translate(1 1)\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"5\",\n    cy: \"50\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cy\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"50;5;50;50\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cx\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"5;27;49;5\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"27\",\n    cy: \"5\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cy\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    from: \"5\",\n    to: \"5\",\n    values: \"5;50;50;5\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cx\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    from: \"27\",\n    to: \"27\",\n    values: \"27;49;5;27\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"49\",\n    cy: \"50\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cy\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"50;50;5;50\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cx\",\n    from: \"49\",\n    to: \"49\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"49;5;27;49\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })))));\n};\nexports.BallTriangle = BallTriangle;\nBallTriangle.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nBallTriangle.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  radius: 5,\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "BallTriangle", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "height", "width", "stroke", "color", "viewBox", "xmlns", "label", "fill", "fillRule", "transform", "strokeWidth", "cx", "cy", "r", "radius", "attributeName", "begin", "dur", "values", "calcMode", "repeatCount", "from", "to", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/BallTriangle.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BallTriangle = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar BallTriangle = function BallTriangle(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    height: props.height,\n    width: props.width,\n    stroke: props.color,\n    viewBox: \"0 0 57 57\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"translate(1 1)\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"5\",\n    cy: \"50\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cy\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"50;5;50;50\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cx\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"5;27;49;5\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"27\",\n    cy: \"5\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cy\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    from: \"5\",\n    to: \"5\",\n    values: \"5;50;50;5\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cx\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    from: \"27\",\n    to: \"27\",\n    values: \"27;49;5;27\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"49\",\n    cy: \"50\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cy\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"50;50;5;50\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"cx\",\n    from: \"49\",\n    to: \"49\",\n    begin: \"0s\",\n    dur: \"2.2s\",\n    values: \"49;5;27;49\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })))));\n};\n\nexports.BallTriangle = BallTriangle;\nBallTriangle.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nBallTriangle.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  radius: 5,\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAE7B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,KAAK,EAAE;EAC9C,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,MAAM,EAAEF,KAAK,CAACE,MAAM;IACpBC,KAAK,EAAEH,KAAK,CAACG,KAAK;IAClBC,MAAM,EAAEJ,KAAK,CAACK,KAAK;IACnBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnC,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDQ,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAahB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDU,SAAS,EAAE,gBAAgB;IAC3BC,WAAW,EAAE;EACf,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDY,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB;EACX,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDgB,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa5B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DgB,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa5B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DY,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,CAAC,EAAEf,KAAK,CAACgB;EACX,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDgB,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXI,IAAI,EAAE,GAAG;IACTC,EAAE,EAAE,GAAG;IACPJ,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa5B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DgB,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXI,IAAI,EAAE,IAAI;IACVC,EAAE,EAAE,IAAI;IACRJ,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa5B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DY,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB;EACX,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDgB,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa5B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DgB,aAAa,EAAE,IAAI;IACnBM,IAAI,EAAE,IAAI;IACVC,EAAE,EAAE,IAAI;IACRN,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AAED/B,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCA,YAAY,CAACgC,SAAS,GAAG;EACvBvB,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAAC6B,SAAS,CAAC,CAAC7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAAC;EACrGzB,KAAK,EAAEN,UAAU,CAAC,SAAS,CAAC,CAAC6B,SAAS,CAAC,CAAC7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAAC;EACpGvB,KAAK,EAAER,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM;EACnCnB,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM;EACnCX,MAAM,EAAEnB,UAAU,CAAC,SAAS,CAAC,CAAC+B;AAChC,CAAC;AACDnC,YAAY,CAACoC,YAAY,GAAG;EAC1B3B,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTE,KAAK,EAAE,OAAO;EACdW,MAAM,EAAE,CAAC;EACTR,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}