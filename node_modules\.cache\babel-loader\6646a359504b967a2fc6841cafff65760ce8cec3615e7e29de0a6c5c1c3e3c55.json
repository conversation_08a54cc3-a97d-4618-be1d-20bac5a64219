{"ast": null, "code": "/**\n* @license nested-property https://github.com/cosmosio/nested-property\n*\n* The MIT License (MIT)\n*\n* Copyright (c) 2014-2015 <PERSON> <<EMAIL>>\n*/\n\"use strict\";\n\nmodule.exports = {\n  set: setNestedProperty,\n  get: getNestedProperty,\n  has: hasNestedProperty,\n  hasOwn: function (object, property, options) {\n    return this.has(object, property, options || {\n      own: true\n    });\n  },\n  isIn: isInNestedProperty\n};\n\n/**\n * Get the property of an object nested in one or more objects\n * given an object such as a.b.c.d = 5, getNestedProperty(a, \"b.c.d\") will return 5.\n * @param {Object} object the object to get the property from\n * @param {String} property the path to the property as a string\n * @returns the object or the the property value if found\n */\nfunction getNestedProperty(object, property) {\n  if (object && typeof object == \"object\") {\n    if (typeof property == \"string\" && property !== \"\") {\n      var split = property.split(\".\");\n      return split.reduce(function (obj, prop) {\n        return obj && obj[prop];\n      }, object);\n    } else if (typeof property == \"number\") {\n      return object[property];\n    } else {\n      return object;\n    }\n  } else {\n    return object;\n  }\n}\n\n/**\n * Tell if a nested object has a given property (or array a given index)\n * given an object such as a.b.c.d = 5, hasNestedProperty(a, \"b.c.d\") will return true.\n * It also returns true if the property is in the prototype chain.\n * @param {Object} object the object to get the property from\n * @param {String} property the path to the property as a string\n * @param {Object} options:\n *  - own: set to reject properties from the prototype\n * @returns true if has (property in object), false otherwise\n */\nfunction hasNestedProperty(object, property, options) {\n  options = options || {};\n  if (object && typeof object == \"object\") {\n    if (typeof property == \"string\" && property !== \"\") {\n      var split = property.split(\".\");\n      return split.reduce(function (obj, prop, idx, array) {\n        if (idx == array.length - 1) {\n          if (options.own) {\n            return !!(obj && obj.hasOwnProperty(prop));\n          } else {\n            return !!(obj !== null && typeof obj == \"object\" && prop in obj);\n          }\n        }\n        return obj && obj[prop];\n      }, object);\n    } else if (typeof property == \"number\") {\n      return property in object;\n    } else {\n      return false;\n    }\n  } else {\n    return false;\n  }\n}\n\n/**\n * Set the property of an object nested in one or more objects\n * If the property doesn't exist, it gets created.\n * @param {Object} object\n * @param {String} property\n * @param value the value to set\n * @returns object if no assignment was made or the value if the assignment was made\n */\nfunction setNestedProperty(object, property, value) {\n  if (object && typeof object == \"object\") {\n    if (typeof property == \"string\" && property !== \"\") {\n      var split = property.split(\".\");\n      return split.reduce(function (obj, prop, idx) {\n        const nextPropIsNumber = Number.isInteger(Number(split[idx + 1]));\n        obj[prop] = obj[prop] || (nextPropIsNumber ? [] : {});\n        if (split.length == idx + 1) {\n          obj[prop] = value;\n        }\n        return obj[prop];\n      }, object);\n    } else if (typeof property == \"number\") {\n      object[property] = value;\n      return object[property];\n    } else {\n      return object;\n    }\n  } else {\n    return object;\n  }\n}\n\n/**\n * Tell if an object is on the path to a nested property\n * If the object is on the path, and the path exists, it returns true, and false otherwise.\n * @param {Object} object to get the nested property from\n * @param {String} property name of the nested property\n * @param {Object} objectInPath the object to check\n * @param {Object} options:\n *  - validPath: return false if the path is invalid, even if the object is in the path\n * @returns {boolean} true if the object is on the path\n */\nfunction isInNestedProperty(object, property, objectInPath, options) {\n  options = options || {};\n  if (object && typeof object == \"object\") {\n    if (typeof property == \"string\" && property !== \"\") {\n      var split = property.split(\".\"),\n        isIn = false,\n        pathExists;\n      pathExists = !!split.reduce(function (obj, prop) {\n        isIn = isIn || obj === objectInPath || !!obj && obj[prop] === objectInPath;\n        return obj && obj[prop];\n      }, object);\n      if (options.validPath) {\n        return isIn && pathExists;\n      } else {\n        return isIn;\n      }\n    } else {\n      return false;\n    }\n  } else {\n    return false;\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "set", "setNestedProperty", "get", "getNestedProperty", "has", "hasNestedProperty", "hasOwn", "object", "property", "options", "own", "isIn", "isInNestedProperty", "split", "reduce", "obj", "prop", "idx", "array", "length", "hasOwnProperty", "value", "nextPropIsNumber", "Number", "isInteger", "objectInPath", "pathExists", "validPath"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-floater/node_modules/nested-property/index.js"], "sourcesContent": ["/**\n* @license nested-property https://github.com/cosmosio/nested-property\n*\n* The MIT License (MIT)\n*\n* Copyright (c) 2014-2015 <PERSON> <<EMAIL>>\n*/\n\"use strict\";\n\nmodule.exports = {\n  set: setNestedProperty,\n  get: getNestedProperty,\n  has: hasNestedProperty,\n  hasOwn: function (object, property, options) {\n      return this.has(object, property, options || {own: true});\n  },\n  isIn: isInNestedProperty\n};\n\n/**\n * Get the property of an object nested in one or more objects\n * given an object such as a.b.c.d = 5, getNestedProperty(a, \"b.c.d\") will return 5.\n * @param {Object} object the object to get the property from\n * @param {String} property the path to the property as a string\n * @returns the object or the the property value if found\n */\nfunction getNestedProperty(object, property) {\n    if (object && typeof object == \"object\") {\n        if (typeof property == \"string\" && property !== \"\") {\n            var split = property.split(\".\");\n            return split.reduce(function (obj, prop) {\n                return obj && obj[prop];\n            }, object);\n        } else if (typeof property == \"number\") {\n            return object[property];\n        } else {\n            return object;\n        }\n    } else {\n        return object;\n    }\n}\n\n/**\n * Tell if a nested object has a given property (or array a given index)\n * given an object such as a.b.c.d = 5, hasNestedProperty(a, \"b.c.d\") will return true.\n * It also returns true if the property is in the prototype chain.\n * @param {Object} object the object to get the property from\n * @param {String} property the path to the property as a string\n * @param {Object} options:\n *  - own: set to reject properties from the prototype\n * @returns true if has (property in object), false otherwise\n */\nfunction hasNestedProperty(object, property, options) {\n    options = options || {};\n\n    if (object && typeof object == \"object\") {\n        if (typeof property == \"string\" && property !== \"\") {\n            var split = property.split(\".\");\n            return split.reduce(function (obj, prop, idx, array) {\n                if (idx == array.length - 1) {\n                    if (options.own) {\n                        return !!(obj && obj.hasOwnProperty(prop));\n                    } else {\n                        return !!(obj !== null && typeof obj == \"object\" && prop in obj);\n                    }\n                }\n                return obj && obj[prop];\n            }, object);\n        } else if (typeof property == \"number\") {\n            return property in object;\n        } else {\n            return false;\n        }\n    } else {\n        return false;\n    }\n}\n\n/**\n * Set the property of an object nested in one or more objects\n * If the property doesn't exist, it gets created.\n * @param {Object} object\n * @param {String} property\n * @param value the value to set\n * @returns object if no assignment was made or the value if the assignment was made\n */\nfunction setNestedProperty(object, property, value) {\n    if (object && typeof object == \"object\") {\n        if (typeof property == \"string\" && property !== \"\") {\n            var split = property.split(\".\");\n            return split.reduce(function (obj, prop, idx) {\n                const nextPropIsNumber = Number.isInteger(Number(split[idx + 1]));\n                \n                obj[prop] = obj[prop] || (nextPropIsNumber ? [] : {})\n                if (split.length == (idx + 1)) {\n                    obj[prop] = value;\n                }\n                return obj[prop];\n            }, object);\n        } else if (typeof property == \"number\") {\n            object[property] = value;\n            return object[property];\n        } else {\n            return object;\n        }\n    } else {\n        return object;\n    }\n}\n\n/**\n * Tell if an object is on the path to a nested property\n * If the object is on the path, and the path exists, it returns true, and false otherwise.\n * @param {Object} object to get the nested property from\n * @param {String} property name of the nested property\n * @param {Object} objectInPath the object to check\n * @param {Object} options:\n *  - validPath: return false if the path is invalid, even if the object is in the path\n * @returns {boolean} true if the object is on the path\n */\nfunction isInNestedProperty(object, property, objectInPath, options) {\n    options = options || {};\n\n    if (object && typeof object == \"object\") {\n        if (typeof property == \"string\" && property !== \"\") {\n            var split = property.split(\".\"),\n                isIn = false,\n                pathExists;\n\n            pathExists = !!split.reduce(function (obj, prop) {\n                isIn = isIn || obj === objectInPath || (!!obj && obj[prop] === objectInPath);\n                return obj && obj[prop];\n            }, object);\n\n            if (options.validPath) {\n                return isIn && pathExists;\n            } else {\n                return isIn;\n            }\n        } else {\n            return false;\n        }\n    } else {\n        return false;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEC,iBAAiB;EACtBC,GAAG,EAAEC,iBAAiB;EACtBC,GAAG,EAAEC,iBAAiB;EACtBC,MAAM,EAAE,SAAAA,CAAUC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACzC,OAAO,IAAI,CAACL,GAAG,CAACG,MAAM,EAAEC,QAAQ,EAAEC,OAAO,IAAI;MAACC,GAAG,EAAE;IAAI,CAAC,CAAC;EAC7D,CAAC;EACDC,IAAI,EAAEC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,iBAAiBA,CAACI,MAAM,EAAEC,QAAQ,EAAE;EACzC,IAAID,MAAM,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;IACrC,IAAI,OAAOC,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAChD,IAAIK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;MAC/B,OAAOA,KAAK,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;QACrC,OAAOD,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;MAC3B,CAAC,EAAET,MAAM,CAAC;IACd,CAAC,MAAM,IAAI,OAAOC,QAAQ,IAAI,QAAQ,EAAE;MACpC,OAAOD,MAAM,CAACC,QAAQ,CAAC;IAC3B,CAAC,MAAM;MACH,OAAOD,MAAM;IACjB;EACJ,CAAC,MAAM;IACH,OAAOA,MAAM;EACjB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,iBAAiBA,CAACE,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAClDA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIF,MAAM,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;IACrC,IAAI,OAAOC,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAChD,IAAIK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;MAC/B,OAAOA,KAAK,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAE;QACjD,IAAID,GAAG,IAAIC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;UACzB,IAAIV,OAAO,CAACC,GAAG,EAAE;YACb,OAAO,CAAC,EAAEK,GAAG,IAAIA,GAAG,CAACK,cAAc,CAACJ,IAAI,CAAC,CAAC;UAC9C,CAAC,MAAM;YACH,OAAO,CAAC,EAAED,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,IAAI,QAAQ,IAAIC,IAAI,IAAID,GAAG,CAAC;UACpE;QACJ;QACA,OAAOA,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;MAC3B,CAAC,EAAET,MAAM,CAAC;IACd,CAAC,MAAM,IAAI,OAAOC,QAAQ,IAAI,QAAQ,EAAE;MACpC,OAAOA,QAAQ,IAAID,MAAM;IAC7B,CAAC,MAAM;MACH,OAAO,KAAK;IAChB;EACJ,CAAC,MAAM;IACH,OAAO,KAAK;EAChB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,iBAAiBA,CAACM,MAAM,EAAEC,QAAQ,EAAEa,KAAK,EAAE;EAChD,IAAId,MAAM,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;IACrC,IAAI,OAAOC,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAChD,IAAIK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;MAC/B,OAAOA,KAAK,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAE;QAC1C,MAAMK,gBAAgB,GAAGC,MAAM,CAACC,SAAS,CAACD,MAAM,CAACV,KAAK,CAACI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAEjEF,GAAG,CAACC,IAAI,CAAC,GAAGD,GAAG,CAACC,IAAI,CAAC,KAAKM,gBAAgB,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACrD,IAAIT,KAAK,CAACM,MAAM,IAAKF,GAAG,GAAG,CAAE,EAAE;UAC3BF,GAAG,CAACC,IAAI,CAAC,GAAGK,KAAK;QACrB;QACA,OAAON,GAAG,CAACC,IAAI,CAAC;MACpB,CAAC,EAAET,MAAM,CAAC;IACd,CAAC,MAAM,IAAI,OAAOC,QAAQ,IAAI,QAAQ,EAAE;MACpCD,MAAM,CAACC,QAAQ,CAAC,GAAGa,KAAK;MACxB,OAAOd,MAAM,CAACC,QAAQ,CAAC;IAC3B,CAAC,MAAM;MACH,OAAOD,MAAM;IACjB;EACJ,CAAC,MAAM;IACH,OAAOA,MAAM;EACjB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,kBAAkBA,CAACL,MAAM,EAAEC,QAAQ,EAAEiB,YAAY,EAAEhB,OAAO,EAAE;EACjEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIF,MAAM,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;IACrC,IAAI,OAAOC,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAChD,IAAIK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;QAC3BF,IAAI,GAAG,KAAK;QACZe,UAAU;MAEdA,UAAU,GAAG,CAAC,CAACb,KAAK,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;QAC7CL,IAAI,GAAGA,IAAI,IAAII,GAAG,KAAKU,YAAY,IAAK,CAAC,CAACV,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,KAAKS,YAAa;QAC5E,OAAOV,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;MAC3B,CAAC,EAAET,MAAM,CAAC;MAEV,IAAIE,OAAO,CAACkB,SAAS,EAAE;QACnB,OAAOhB,IAAI,IAAIe,UAAU;MAC7B,CAAC,MAAM;QACH,OAAOf,IAAI;MACf;IACJ,CAAC,MAAM;MACH,OAAO,KAAK;IAChB;EACJ,CAAC,MAAM;IACH,OAAO,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}