{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiunfiPdfAccordi.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiPDFAccordi - operazioni sull'aggiunta premi fine anno e sconti pagamento\n*\n*/\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { FileUpload } from 'primereact/fileupload';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiPDFAccordi = () => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [file, setFile] = useState([]);\n  const [disabled, setDisabled] = useState([]);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    //Fare una get per vedere se ci sono fià file presenti legati al fornitore\n\n    /* async function trovaRisultato() {\n    await APIRequest('GET', `uploads/documentsimage?idDocument=${idDoc}`)\n        .then(res => {\n            console.log(res.data);\n            setFile(res.data)\n        }).catch((e) => {\n            console.log(e)\n            toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile reperire i documenti per i fornitori selezionato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n        })\n        stopLoading()\n    }\n    trovaRisultato(); */\n    stopLoading();\n  }, []);\n  const uploadFile = e => {\n    console.log(e);\n    var files = [...file];\n    for (var obj of e.files) {\n      files.push(obj);\n    }\n    setFile(files);\n    setDisabled(false);\n  };\n  const Invia = async e => {\n    // Create an object of formData \n    const formData = new FormData();\n    // Update the formData object \n    for (var obj of file) {\n      formData.append(\"image\", obj);\n    }\n    /* var url = 'uploads/documentsimage?idDocument=' + this.state.results[0].idDocument.id\n    await APIRequest('POST', url, formData)\n        .then(res => {\n            console.log(res.data);\n            var imgs = [...this.state.img, ...res.data]\n            this.setState({ selectedFile: [], disabled: true, img: imgs })\n            toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"L'immagine è stata inserita con successo\", life: 3000 });\n        }).catch((e) => {\n            console.log(e)\n            toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere l'immagine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n        }) */\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"inDaBox\",\n      className: \"flex-column\",\n      children: [/*#__PURE__*/_jsxDEV(FileUpload, {\n        name: \"demo[]\",\n        emptyTemplate: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"m-0\",\n          children: \"Trascina gli elementi nella finestra o aggiungili manualmente cliccando su \\\"seleziona\\\".\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 36\n        }, this),\n        onSelect: e => uploadFile(e),\n        className: \"border-0 mb-0 col-12\",\n        chooseLabel: \"Seleziona\",\n        uploadLabel: \"Carica\",\n        cancelOptions: {\n          className: 'd-none'\n        },\n        uploadOptions: {\n          className: 'd-none'\n        },\n        multiple: true,\n        accept: \".pdf\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button mx-auto justify-content-center mt-2\",\n          onClick: Invia,\n          disabled: disabled,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-upload mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 25\n          }, this), Costanti.Carica]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiPDFAccordi, \"o0f1PEUCqfvudRSwg9fBGc4YLoM=\");\n_c = AggiungiPDFAccordi;\nexport default AggiungiPDFAccordi;\nvar _c;\n$RefreshReg$(_c, \"AggiungiPDFAccordi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "stopLoading", "FileUpload", "<PERSON><PERSON>", "<PERSON><PERSON>", "Toast", "jsxDEV", "_jsxDEV", "AggiungiPDFAccordi", "_s", "file", "setFile", "disabled", "setDisabled", "toast", "uploadFile", "e", "console", "log", "files", "obj", "push", "Invia", "formData", "FormData", "append", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "name", "emptyTemplate", "onSelect", "<PERSON><PERSON><PERSON><PERSON>", "uploadLabel", "cancelOptions", "uploadOptions", "multiple", "accept", "onClick", "Carica", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiunfiPdfAccordi.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiPDFAccordi - operazioni sull'aggiunta premi fine anno e sconti pagamento\n*\n*/\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { FileUpload } from 'primereact/fileupload';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\n\nconst AggiungiPDFAccordi = () => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [file, setFile] = useState([]);\n    const [disabled, setDisabled] = useState([]);\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n\n        //Fare una get per vedere se ci sono fià file presenti legati al fornitore\n\n        /* async function trovaRisultato() {\n        await APIRequest('GET', `uploads/documentsimage?idDocument=${idDoc}`)\n            .then(res => {\n                console.log(res.data);\n                setFile(res.data)\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile reperire i documenti per i fornitori selezionato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n            stopLoading()\n        }\n        trovaRisultato(); */\n        stopLoading()\n    }, []);\n    const uploadFile = (e) => {\n        console.log(e)\n        var files = [...file]\n        for (var obj of e.files) {\n            files.push(obj)\n        }\n        setFile(files)\n        setDisabled(false)\n    }\n    const Invia = async (e) => {\n       // Create an object of formData \n        const formData = new FormData();\n        // Update the formData object \n        for (var obj of file) {\n            formData.append(\n                \"image\",\n                obj\n            )\n        }\n        /* var url = 'uploads/documentsimage?idDocument=' + this.state.results[0].idDocument.id\n        await APIRequest('POST', url, formData)\n            .then(res => {\n                console.log(res.data);\n                var imgs = [...this.state.img, ...res.data]\n                this.setState({ selectedFile: [], disabled: true, img: imgs })\n                toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"L'immagine è stata inserita con successo\", life: 3000 });\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere l'immagine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            }) */\n    };\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div id=\"inDaBox\" className='flex-column'>\n                <FileUpload\n                    name=\"demo[]\"\n                    emptyTemplate={<p className=\"m-0\">Trascina gli elementi nella finestra o aggiungili manualmente cliccando su \"seleziona\".</p>}\n                    onSelect={e => uploadFile(e)}\n                    className=\"border-0 mb-0 col-12\"\n                    chooseLabel=\"Seleziona\"\n                    uploadLabel=\"Carica\"\n                    cancelOptions={{ className: 'd-none' }}\n                    uploadOptions={{ className: 'd-none' }}\n                    multiple\n                    accept=\".pdf\"\n                />\n                <div>\n                    <Button\n                        className=\"p-button mx-auto justify-content-center mt-2\"\n                        onClick={Invia}\n                        disabled={disabled}\n                    >\n                        <i className='pi pi-upload mr-2'></i>\n                        {Costanti.Carica}\n                    </Button>\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiPDFAccordi;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,4CAA4C;AACxE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMgB,KAAK,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IAEZ;;IAEA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQE,WAAW,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACN,MAAMc,UAAU,GAAIC,CAAC,IAAK;IACtBC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IACd,IAAIG,KAAK,GAAG,CAAC,GAAGT,IAAI,CAAC;IACrB,KAAK,IAAIU,GAAG,IAAIJ,CAAC,CAACG,KAAK,EAAE;MACrBA,KAAK,CAACE,IAAI,CAACD,GAAG,CAAC;IACnB;IACAT,OAAO,CAACQ,KAAK,CAAC;IACdN,WAAW,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMS,KAAK,GAAG,MAAON,CAAC,IAAK;IACxB;IACC,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/B;IACA,KAAK,IAAIJ,GAAG,IAAIV,IAAI,EAAE;MAClBa,QAAQ,CAACE,MAAM,CACX,OAAO,EACPL,GACJ,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC;EACD,oBACIb,OAAA;IAAKmB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBpB,OAAA,CAACF,KAAK;MAACuB,GAAG,EAAEd;IAAM;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBzB,OAAA;MAAK0B,EAAE,EAAC,SAAS;MAACP,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACrCpB,OAAA,CAACL,UAAU;QACPgC,IAAI,EAAC,QAAQ;QACbC,aAAa,eAAE5B,OAAA;UAAGmB,SAAS,EAAC,KAAK;UAAAC,QAAA,EAAC;QAAuF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAE;QAC9HI,QAAQ,EAAEpB,CAAC,IAAID,UAAU,CAACC,CAAC,CAAE;QAC7BU,SAAS,EAAC,sBAAsB;QAChCW,WAAW,EAAC,WAAW;QACvBC,WAAW,EAAC,QAAQ;QACpBC,aAAa,EAAE;UAAEb,SAAS,EAAE;QAAS,CAAE;QACvCc,aAAa,EAAE;UAAEd,SAAS,EAAE;QAAS,CAAE;QACvCe,QAAQ;QACRC,MAAM,EAAC;MAAM;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACFzB,OAAA;QAAAoB,QAAA,eACIpB,OAAA,CAACJ,MAAM;UACHuB,SAAS,EAAC,8CAA8C;UACxDiB,OAAO,EAAErB,KAAM;UACfV,QAAQ,EAAEA,QAAS;UAAAe,QAAA,gBAEnBpB,OAAA;YAAGmB,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACpC5B,QAAQ,CAACwC,MAAM;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAvB,EAAA,CApFKD,kBAAkB;AAAAqC,EAAA,GAAlBrC,kBAAkB;AAsFxB,eAAeA,kBAAkB;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}