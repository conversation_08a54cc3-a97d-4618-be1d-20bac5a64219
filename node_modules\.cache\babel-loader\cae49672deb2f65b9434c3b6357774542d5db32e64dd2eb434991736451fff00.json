{"ast": null, "code": "import { Chart, registerables } from '../dist/chart.esm';\nChart.register(...registerables);\nexport default Chart;", "map": {"version": 3, "names": ["Chart", "registerables", "register"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/chart.js/auto/auto.esm.js"], "sourcesContent": ["import {Chart, registerables} from '../dist/chart.esm';\n\nChart.register(...registerables);\n\nexport default Chart;\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,aAAa,QAAO,mBAAmB;AAEtDD,KAAK,CAACE,QAAQ,CAAC,GAAGD,aAAa,CAAC;AAEhC,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}