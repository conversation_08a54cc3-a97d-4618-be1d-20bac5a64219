{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { Item } from 'rc-menu';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport classNames from 'classnames';\nimport MenuContext from './MenuContext';\nimport Tooltip from '../tooltip';\nimport { SiderContext } from '../layout/Sider';\nimport { isValidElement, cloneElement } from '../_util/reactNode';\nvar MenuItem = /*#__PURE__*/function (_React$Component) {\n  _inherits(MenuItem, _React$Component);\n  var _super = _createSuper(MenuItem);\n  function MenuItem() {\n    var _this;\n    _classCallCheck(this, MenuItem);\n    _this = _super.apply(this, arguments);\n    _this.renderItem = function (_ref) {\n      var _classNames;\n      var siderCollapsed = _ref.siderCollapsed;\n      var _a;\n      var _this$context = _this.context,\n        prefixCls = _this$context.prefixCls,\n        firstLevel = _this$context.firstLevel,\n        inlineCollapsed = _this$context.inlineCollapsed,\n        direction = _this$context.direction,\n        disableMenuItemTitleTooltip = _this$context.disableMenuItemTitleTooltip;\n      var _this$props = _this.props,\n        className = _this$props.className,\n        children = _this$props.children;\n      var _b = _this.props,\n        title = _b.title,\n        icon = _b.icon,\n        danger = _b.danger,\n        rest = __rest(_b, [\"title\", \"icon\", \"danger\"]);\n      var tooltipTitle = title;\n      if (typeof title === 'undefined') {\n        tooltipTitle = firstLevel ? children : '';\n      } else if (title === false) {\n        tooltipTitle = '';\n      }\n      var tooltipProps = {\n        title: tooltipTitle\n      };\n      if (!siderCollapsed && !inlineCollapsed) {\n        tooltipProps.title = null; // Reset `visible` to fix control mode tooltip display not correct\n        // ref: https://github.com/ant-design/ant-design/issues/16742\n\n        tooltipProps.visible = false;\n      }\n      var childrenLength = toArray(children).length;\n      var returnNode = /*#__PURE__*/React.createElement(Item, _extends({}, rest, {\n        className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-danger\"), danger), _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-only-child\"), (icon ? childrenLength + 1 : childrenLength) === 1), _classNames), className),\n        title: typeof title === 'string' ? title : undefined\n      }), cloneElement(icon, {\n        className: classNames(isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', \"\".concat(prefixCls, \"-item-icon\"))\n      }), _this.renderItemChildren(inlineCollapsed));\n      if (!disableMenuItemTitleTooltip) {\n        returnNode = /*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps, {\n          placement: direction === 'rtl' ? 'left' : 'right',\n          overlayClassName: \"\".concat(prefixCls, \"-inline-collapsed-tooltip\")\n        }), returnNode);\n      }\n      return returnNode;\n    };\n    return _this;\n  }\n  _createClass(MenuItem, [{\n    key: \"renderItemChildren\",\n    value: function renderItemChildren(inlineCollapsed) {\n      var _this$context2 = this.context,\n        prefixCls = _this$context2.prefixCls,\n        firstLevel = _this$context2.firstLevel;\n      var _this$props2 = this.props,\n        icon = _this$props2.icon,\n        children = _this$props2.children;\n      var wrapNode = /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title-content\")\n      }, children); // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n      // ref: https://github.com/ant-design/ant-design/pull/23456\n\n      if (!icon || isValidElement(children) && children.type === 'span') {\n        if (children && inlineCollapsed && firstLevel && typeof children === 'string') {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-inline-collapsed-noicon\")\n          }, children.charAt(0));\n        }\n      }\n      return wrapNode;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(SiderContext.Consumer, null, this.renderItem);\n    }\n  }]);\n  return MenuItem;\n}(React.Component);\nexport { MenuItem as default };\nMenuItem.contextType = MenuContext;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "<PERSON><PERSON>", "toArray", "classNames", "MenuContext", "<PERSON><PERSON><PERSON>", "SiderContext", "isValidElement", "cloneElement", "MenuItem", "_React$Component", "_super", "_this", "apply", "arguments", "renderItem", "_ref", "_classNames", "siderCollapsed", "_a", "_this$context", "context", "prefixCls", "firstLevel", "inlineCollapsed", "direction", "disableMenuItemTitleTooltip", "_this$props", "props", "className", "children", "_b", "title", "icon", "danger", "rest", "tooltipTitle", "tooltipProps", "visible", "<PERSON><PERSON><PERSON><PERSON>", "returnNode", "createElement", "concat", "undefined", "renderItemChildren", "placement", "overlayClassName", "key", "value", "_this$context2", "_this$props2", "wrapNode", "type", "char<PERSON>t", "render", "Consumer", "Component", "default", "contextType"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/menu/MenuItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport { Item } from 'rc-menu';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport classNames from 'classnames';\nimport MenuContext from './MenuContext';\nimport Tooltip from '../tooltip';\nimport { SiderContext } from '../layout/Sider';\nimport { isValidElement, cloneElement } from '../_util/reactNode';\n\nvar MenuItem = /*#__PURE__*/function (_React$Component) {\n  _inherits(MenuItem, _React$Component);\n\n  var _super = _createSuper(MenuItem);\n\n  function MenuItem() {\n    var _this;\n\n    _classCallCheck(this, MenuItem);\n\n    _this = _super.apply(this, arguments);\n\n    _this.renderItem = function (_ref) {\n      var _classNames;\n\n      var siderCollapsed = _ref.siderCollapsed;\n\n      var _a;\n\n      var _this$context = _this.context,\n          prefixCls = _this$context.prefixCls,\n          firstLevel = _this$context.firstLevel,\n          inlineCollapsed = _this$context.inlineCollapsed,\n          direction = _this$context.direction,\n          disableMenuItemTitleTooltip = _this$context.disableMenuItemTitleTooltip;\n      var _this$props = _this.props,\n          className = _this$props.className,\n          children = _this$props.children;\n\n      var _b = _this.props,\n          title = _b.title,\n          icon = _b.icon,\n          danger = _b.danger,\n          rest = __rest(_b, [\"title\", \"icon\", \"danger\"]);\n\n      var tooltipTitle = title;\n\n      if (typeof title === 'undefined') {\n        tooltipTitle = firstLevel ? children : '';\n      } else if (title === false) {\n        tooltipTitle = '';\n      }\n\n      var tooltipProps = {\n        title: tooltipTitle\n      };\n\n      if (!siderCollapsed && !inlineCollapsed) {\n        tooltipProps.title = null; // Reset `visible` to fix control mode tooltip display not correct\n        // ref: https://github.com/ant-design/ant-design/issues/16742\n\n        tooltipProps.visible = false;\n      }\n\n      var childrenLength = toArray(children).length;\n      var returnNode = /*#__PURE__*/React.createElement(Item, _extends({}, rest, {\n        className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-danger\"), danger), _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-only-child\"), (icon ? childrenLength + 1 : childrenLength) === 1), _classNames), className),\n        title: typeof title === 'string' ? title : undefined\n      }), cloneElement(icon, {\n        className: classNames(isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', \"\".concat(prefixCls, \"-item-icon\"))\n      }), _this.renderItemChildren(inlineCollapsed));\n\n      if (!disableMenuItemTitleTooltip) {\n        returnNode = /*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps, {\n          placement: direction === 'rtl' ? 'left' : 'right',\n          overlayClassName: \"\".concat(prefixCls, \"-inline-collapsed-tooltip\")\n        }), returnNode);\n      }\n\n      return returnNode;\n    };\n\n    return _this;\n  }\n\n  _createClass(MenuItem, [{\n    key: \"renderItemChildren\",\n    value: function renderItemChildren(inlineCollapsed) {\n      var _this$context2 = this.context,\n          prefixCls = _this$context2.prefixCls,\n          firstLevel = _this$context2.firstLevel;\n      var _this$props2 = this.props,\n          icon = _this$props2.icon,\n          children = _this$props2.children;\n      var wrapNode = /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title-content\")\n      }, children); // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n      // ref: https://github.com/ant-design/ant-design/pull/23456\n\n      if (!icon || isValidElement(children) && children.type === 'span') {\n        if (children && inlineCollapsed && firstLevel && typeof children === 'string') {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-inline-collapsed-noicon\")\n          }, children.charAt(0));\n        }\n      }\n\n      return wrapNode;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(SiderContext.Consumer, null, this.renderItem);\n    }\n  }]);\n\n  return MenuItem;\n}(React.Component);\n\nexport { MenuItem as default };\nMenuItem.contextType = MenuContext;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AAEjE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,SAAS;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,EAAEC,YAAY,QAAQ,oBAAoB;AAEjE,IAAIC,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACtD1B,SAAS,CAACyB,QAAQ,EAAEC,gBAAgB,CAAC;EAErC,IAAIC,MAAM,GAAG1B,YAAY,CAACwB,QAAQ,CAAC;EAEnC,SAASA,QAAQA,CAAA,EAAG;IAClB,IAAIG,KAAK;IAET9B,eAAe,CAAC,IAAI,EAAE2B,QAAQ,CAAC;IAE/BG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAErCF,KAAK,CAACG,UAAU,GAAG,UAAUC,IAAI,EAAE;MACjC,IAAIC,WAAW;MAEf,IAAIC,cAAc,GAAGF,IAAI,CAACE,cAAc;MAExC,IAAIC,EAAE;MAEN,IAAIC,aAAa,GAAGR,KAAK,CAACS,OAAO;QAC7BC,SAAS,GAAGF,aAAa,CAACE,SAAS;QACnCC,UAAU,GAAGH,aAAa,CAACG,UAAU;QACrCC,eAAe,GAAGJ,aAAa,CAACI,eAAe;QAC/CC,SAAS,GAAGL,aAAa,CAACK,SAAS;QACnCC,2BAA2B,GAAGN,aAAa,CAACM,2BAA2B;MAC3E,IAAIC,WAAW,GAAGf,KAAK,CAACgB,KAAK;QACzBC,SAAS,GAAGF,WAAW,CAACE,SAAS;QACjCC,QAAQ,GAAGH,WAAW,CAACG,QAAQ;MAEnC,IAAIC,EAAE,GAAGnB,KAAK,CAACgB,KAAK;QAChBI,KAAK,GAAGD,EAAE,CAACC,KAAK;QAChBC,IAAI,GAAGF,EAAE,CAACE,IAAI;QACdC,MAAM,GAAGH,EAAE,CAACG,MAAM;QAClBC,IAAI,GAAGjD,MAAM,CAAC6C,EAAE,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;MAElD,IAAIK,YAAY,GAAGJ,KAAK;MAExB,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;QAChCI,YAAY,GAAGb,UAAU,GAAGO,QAAQ,GAAG,EAAE;MAC3C,CAAC,MAAM,IAAIE,KAAK,KAAK,KAAK,EAAE;QAC1BI,YAAY,GAAG,EAAE;MACnB;MAEA,IAAIC,YAAY,GAAG;QACjBL,KAAK,EAAEI;MACT,CAAC;MAED,IAAI,CAAClB,cAAc,IAAI,CAACM,eAAe,EAAE;QACvCa,YAAY,CAACL,KAAK,GAAG,IAAI,CAAC,CAAC;QAC3B;;QAEAK,YAAY,CAACC,OAAO,GAAG,KAAK;MAC9B;MAEA,IAAIC,cAAc,GAAGrC,OAAO,CAAC4B,QAAQ,CAAC,CAAChC,MAAM;MAC7C,IAAI0C,UAAU,GAAG,aAAaxC,KAAK,CAACyC,aAAa,CAACxC,IAAI,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEuD,IAAI,EAAE;QACzEN,SAAS,EAAE1B,UAAU,EAAEc,WAAW,GAAG,CAAC,CAAC,EAAEpC,eAAe,CAACoC,WAAW,EAAE,EAAE,CAACyB,MAAM,CAACpB,SAAS,EAAE,cAAc,CAAC,EAAEY,MAAM,CAAC,EAAErD,eAAe,CAACoC,WAAW,EAAE,EAAE,CAACyB,MAAM,CAACpB,SAAS,EAAE,kBAAkB,CAAC,EAAE,CAACW,IAAI,GAAGM,cAAc,GAAG,CAAC,GAAGA,cAAc,MAAM,CAAC,CAAC,EAAEtB,WAAW,GAAGY,SAAS,CAAC;QACzQG,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGW;MAC7C,CAAC,CAAC,EAAEnC,YAAY,CAACyB,IAAI,EAAE;QACrBJ,SAAS,EAAE1B,UAAU,CAACI,cAAc,CAAC0B,IAAI,CAAC,GAAG,CAACd,EAAE,GAAGc,IAAI,CAACL,KAAK,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,SAAS,GAAG,EAAE,EAAE,EAAE,CAACa,MAAM,CAACpB,SAAS,EAAE,YAAY,CAAC;MAC3J,CAAC,CAAC,EAAEV,KAAK,CAACgC,kBAAkB,CAACpB,eAAe,CAAC,CAAC;MAE9C,IAAI,CAACE,2BAA2B,EAAE;QAChCc,UAAU,GAAG,aAAaxC,KAAK,CAACyC,aAAa,CAACpC,OAAO,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEyD,YAAY,EAAE;UAChFQ,SAAS,EAAEpB,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;UACjDqB,gBAAgB,EAAE,EAAE,CAACJ,MAAM,CAACpB,SAAS,EAAE,2BAA2B;QACpE,CAAC,CAAC,EAAEkB,UAAU,CAAC;MACjB;MAEA,OAAOA,UAAU;IACnB,CAAC;IAED,OAAO5B,KAAK;EACd;EAEA7B,YAAY,CAAC0B,QAAQ,EAAE,CAAC;IACtBsC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASJ,kBAAkBA,CAACpB,eAAe,EAAE;MAClD,IAAIyB,cAAc,GAAG,IAAI,CAAC5B,OAAO;QAC7BC,SAAS,GAAG2B,cAAc,CAAC3B,SAAS;QACpCC,UAAU,GAAG0B,cAAc,CAAC1B,UAAU;MAC1C,IAAI2B,YAAY,GAAG,IAAI,CAACtB,KAAK;QACzBK,IAAI,GAAGiB,YAAY,CAACjB,IAAI;QACxBH,QAAQ,GAAGoB,YAAY,CAACpB,QAAQ;MACpC,IAAIqB,QAAQ,GAAG,aAAanD,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;QACtDZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACpB,SAAS,EAAE,gBAAgB;MAClD,CAAC,EAAEQ,QAAQ,CAAC,CAAC,CAAC;MACd;;MAEA,IAAI,CAACG,IAAI,IAAI1B,cAAc,CAACuB,QAAQ,CAAC,IAAIA,QAAQ,CAACsB,IAAI,KAAK,MAAM,EAAE;QACjE,IAAItB,QAAQ,IAAIN,eAAe,IAAID,UAAU,IAAI,OAAOO,QAAQ,KAAK,QAAQ,EAAE;UAC7E,OAAO,aAAa9B,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAE;YAC7CZ,SAAS,EAAE,EAAE,CAACa,MAAM,CAACpB,SAAS,EAAE,0BAA0B;UAC5D,CAAC,EAAEQ,QAAQ,CAACuB,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB;MACF;MAEA,OAAOF,QAAQ;IACjB;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASM,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAatD,KAAK,CAACyC,aAAa,CAACnC,YAAY,CAACiD,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACxC,UAAU,CAAC;IACvF;EACF,CAAC,CAAC,CAAC;EAEH,OAAON,QAAQ;AACjB,CAAC,CAACT,KAAK,CAACwD,SAAS,CAAC;AAElB,SAAS/C,QAAQ,IAAIgD,OAAO;AAC5BhD,QAAQ,CAACiD,WAAW,GAAGtD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}