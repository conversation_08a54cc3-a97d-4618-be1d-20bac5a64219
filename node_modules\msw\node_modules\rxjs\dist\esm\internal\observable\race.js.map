{"version": 3, "file": "race.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/race.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAGxC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AA6C3E,MAAM,UAAU,IAAI,CAAI,GAAG,OAAsD;IAC/E,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IAElC,OAAO,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAuB,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAI,QAAQ,CAAC,OAA+B,CAAC,CAAC,CAAC;AAC3I,CAAC;AAOD,MAAM,UAAU,QAAQ,CAAI,OAA6B;IACvD,OAAO,CAAC,UAAyB,EAAE,EAAE;QACnC,IAAI,aAAa,GAAmB,EAAE,CAAC;QAMvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,aAAa,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9E,aAAa,CAAC,IAAI,CAChB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAuB,CAAC,CAAC,SAAS,CACnD,wBAAwB,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC7C,IAAI,aAAa,EAAE;oBAGjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC7C,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;qBAC3C;oBACD,aAAa,GAAG,IAAK,CAAC;iBACvB;gBACD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC,CAAC,CACH,CACF,CAAC;SACH;IACH,CAAC,CAAC;AACJ,CAAC"}