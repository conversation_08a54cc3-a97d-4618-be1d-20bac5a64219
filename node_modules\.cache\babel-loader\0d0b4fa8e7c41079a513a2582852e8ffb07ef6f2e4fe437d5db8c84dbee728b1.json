{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nfunction cuttable(node) {\n  var type = _typeof(node);\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  var totalLen = 0;\n  nodeList.forEach(function (node) {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  var currLen = 0;\n  var currentNodeList = [];\n  for (var i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    var node = nodeList[i];\n    var canCut = cuttable(node);\n    var nodeLen = canCut ? String(node).length : 1;\n    var nextLen = currLen + nodeLen; // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n\n    if (nextLen > len) {\n      var restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nvar NONE = 0;\nvar PREPARE = 1;\nvar WALKING = 2;\nvar DONE_WITH_ELLIPSIS = 3;\nvar DONE_WITHOUT_ELLIPSIS = 4;\nvar Ellipsis = function Ellipsis(_ref) {\n  var enabledMeasure = _ref.enabledMeasure,\n    children = _ref.children,\n    text = _ref.text,\n    width = _ref.width,\n    rows = _ref.rows,\n    onEllipsis = _ref.onEllipsis;\n  var _React$useState = React.useState([0, 0, 0]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    cutLength = _React$useState2[0],\n    setCutLength = _React$useState2[1];\n  var _React$useState3 = React.useState(NONE),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    walkingState = _React$useState4[0],\n    setWalkingState = _React$useState4[1];\n  var _cutLength = _slicedToArray(cutLength, 3),\n    startLen = _cutLength[0],\n    midLen = _cutLength[1],\n    endLen = _cutLength[2];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    singleRowHeight = _React$useState6[0],\n    setSingleRowHeight = _React$useState6[1];\n  var singleRowRef = React.useRef(null);\n  var midRowRef = React.useRef(null);\n  var nodeList = React.useMemo(function () {\n    return toArray(text);\n  }, [text]);\n  var totalLen = React.useMemo(function () {\n    return getNodesLen(nodeList);\n  }, [nodeList]);\n  var mergedChildren = React.useMemo(function () {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]); // ======================== Walk ========================\n\n  useIsomorphicLayoutEffect(function () {\n    if (enabledMeasure && width && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(function () {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(function () {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        var midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        var maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          var _midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          var _maxHeight = rows * singleRowHeight;\n          var nextStartLen = startLen;\n          var nextEndLen = endLen; // We reach the last round\n\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (_midHeight <= _maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          var nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]); // ======================= Render =======================\n\n  var measureStyle = {\n    width: width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  var renderMeasure = function renderMeasure(content, ref, style) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      ref: ref,\n      style: _extends({\n        position: 'fixed',\n        display: 'block',\n        left: 0,\n        top: 0,\n        zIndex: -9999,\n        visibility: 'hidden',\n        pointerEvents: 'none'\n      }, style)\n    }, content);\n  };\n  var renderMeasureSlice = function renderMeasureSlice(len, ref) {\n    var sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_typeof", "React", "toArray", "useIsomorphicLayoutEffect", "cuttable", "node", "type", "getNodesLen", "nodeList", "totalLen", "for<PERSON>ach", "String", "length", "sliceNodes", "len", "currLen", "currentNodeList", "i", "canCut", "nodeLen", "nextLen", "restLen", "push", "slice", "NONE", "PREPARE", "WALKING", "DONE_WITH_ELLIPSIS", "DONE_WITHOUT_ELLIPSIS", "El<PERSON><PERSON>", "_ref", "enabledMeasure", "children", "text", "width", "rows", "onEllipsis", "_React$useState", "useState", "_React$useState2", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "_React$useState3", "_React$useState4", "walkingState", "setWalkingState", "_cutLength", "startLen", "midLen", "endLen", "_React$useState5", "_React$useState6", "singleRowHeight", "setSingleRowHeight", "singleRowRef", "useRef", "midRowRef", "useMemo", "mergedChildren", "Math", "ceil", "_a", "current", "offsetHeight", "_b", "midHeight", "maxHeight", "_midHeight", "_maxHeight", "nextStartLen", "nextEndLen", "nextMidLen", "measureStyle", "whiteSpace", "margin", "padding", "renderMeasure", "content", "ref", "style", "createElement", "position", "display", "left", "top", "zIndex", "visibility", "pointerEvents", "renderMeasureSlice", "sliceNodeList", "Fragment", "wordBreak", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Base/Ellipsis.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\n\nfunction cuttable(node) {\n  var type = _typeof(node);\n\n  return type === 'string' || type === 'number';\n}\n\nfunction getNodesLen(nodeList) {\n  var totalLen = 0;\n  nodeList.forEach(function (node) {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\n\nfunction sliceNodes(nodeList, len) {\n  var currLen = 0;\n  var currentNodeList = [];\n\n  for (var i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n\n    var node = nodeList[i];\n    var canCut = cuttable(node);\n    var nodeLen = canCut ? String(node).length : 1;\n    var nextLen = currLen + nodeLen; // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n\n    if (nextLen > len) {\n      var restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n\n  return nodeList;\n}\n\nvar NONE = 0;\nvar PREPARE = 1;\nvar WALKING = 2;\nvar DONE_WITH_ELLIPSIS = 3;\nvar DONE_WITHOUT_ELLIPSIS = 4;\n\nvar Ellipsis = function Ellipsis(_ref) {\n  var enabledMeasure = _ref.enabledMeasure,\n      children = _ref.children,\n      text = _ref.text,\n      width = _ref.width,\n      rows = _ref.rows,\n      onEllipsis = _ref.onEllipsis;\n\n  var _React$useState = React.useState([0, 0, 0]),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      cutLength = _React$useState2[0],\n      setCutLength = _React$useState2[1];\n\n  var _React$useState3 = React.useState(NONE),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      walkingState = _React$useState4[0],\n      setWalkingState = _React$useState4[1];\n\n  var _cutLength = _slicedToArray(cutLength, 3),\n      startLen = _cutLength[0],\n      midLen = _cutLength[1],\n      endLen = _cutLength[2];\n\n  var _React$useState5 = React.useState(0),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      singleRowHeight = _React$useState6[0],\n      setSingleRowHeight = _React$useState6[1];\n\n  var singleRowRef = React.useRef(null);\n  var midRowRef = React.useRef(null);\n  var nodeList = React.useMemo(function () {\n    return toArray(text);\n  }, [text]);\n  var totalLen = React.useMemo(function () {\n    return getNodesLen(nodeList);\n  }, [nodeList]);\n  var mergedChildren = React.useMemo(function () {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]); // ======================== Walk ========================\n\n  useIsomorphicLayoutEffect(function () {\n    if (enabledMeasure && width && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(function () {\n    var _a;\n\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(function () {\n    var _a, _b;\n\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        var midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        var maxHeight = rows * singleRowHeight;\n\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          var _midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n\n          var _maxHeight = rows * singleRowHeight;\n\n          var nextStartLen = startLen;\n          var nextEndLen = endLen; // We reach the last round\n\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (_midHeight <= _maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n\n          var nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]); // ======================= Render =======================\n\n  var measureStyle = {\n    width: width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n\n  var renderMeasure = function renderMeasure(content, ref, style) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      ref: ref,\n      style: _extends({\n        position: 'fixed',\n        display: 'block',\n        left: 0,\n        top: 0,\n        zIndex: -9999,\n        visibility: 'hidden',\n        pointerEvents: 'none'\n      }, style)\n    }, content);\n  };\n\n  var renderMeasureSlice = function renderMeasureSlice(len, ref) {\n    var sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\n\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\n\nexport default Ellipsis;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,yBAAyB,MAAM,kCAAkC;AAExE,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,IAAI,GAAGN,OAAO,CAACK,IAAI,CAAC;EAExB,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;AAC/C;AAEA,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC7B,IAAIC,QAAQ,GAAG,CAAC;EAChBD,QAAQ,CAACE,OAAO,CAAC,UAAUL,IAAI,EAAE;IAC/B,IAAID,QAAQ,CAACC,IAAI,CAAC,EAAE;MAClBI,QAAQ,IAAIE,MAAM,CAACN,IAAI,CAAC,CAACO,MAAM;IACjC,CAAC,MAAM;MACLH,QAAQ,IAAI,CAAC;IACf;EACF,CAAC,CAAC;EACF,OAAOA,QAAQ;AACjB;AAEA,SAASI,UAAUA,CAACL,QAAQ,EAAEM,GAAG,EAAE;EACjC,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,eAAe,GAAG,EAAE;EAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,QAAQ,CAACI,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC3C;IACA,IAAIF,OAAO,KAAKD,GAAG,EAAE;MACnB,OAAOE,eAAe;IACxB;IAEA,IAAIX,IAAI,GAAGG,QAAQ,CAACS,CAAC,CAAC;IACtB,IAAIC,MAAM,GAAGd,QAAQ,CAACC,IAAI,CAAC;IAC3B,IAAIc,OAAO,GAAGD,MAAM,GAAGP,MAAM,CAACN,IAAI,CAAC,CAACO,MAAM,GAAG,CAAC;IAC9C,IAAIQ,OAAO,GAAGL,OAAO,GAAGI,OAAO,CAAC,CAAC;IACjC;;IAEA,IAAIC,OAAO,GAAGN,GAAG,EAAE;MACjB,IAAIO,OAAO,GAAGP,GAAG,GAAGC,OAAO;MAC3BC,eAAe,CAACM,IAAI,CAACX,MAAM,CAACN,IAAI,CAAC,CAACkB,KAAK,CAAC,CAAC,EAAEF,OAAO,CAAC,CAAC;MACpD,OAAOL,eAAe;IACxB;IAEAA,eAAe,CAACM,IAAI,CAACjB,IAAI,CAAC;IAC1BU,OAAO,GAAGK,OAAO;EACnB;EAEA,OAAOZ,QAAQ;AACjB;AAEA,IAAIgB,IAAI,GAAG,CAAC;AACZ,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,kBAAkB,GAAG,CAAC;AAC1B,IAAIC,qBAAqB,GAAG,CAAC;AAE7B,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,cAAc,GAAGD,IAAI,CAACC,cAAc;IACpCC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,IAAI,GAAGL,IAAI,CAACK,IAAI;IAChBC,UAAU,GAAGN,IAAI,CAACM,UAAU;EAEhC,IAAIC,eAAe,GAAGpC,KAAK,CAACqC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3CC,gBAAgB,GAAGxC,cAAc,CAACsC,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,gBAAgB,GAAGzC,KAAK,CAACqC,QAAQ,CAACd,IAAI,CAAC;IACvCmB,gBAAgB,GAAG5C,cAAc,CAAC2C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,IAAIG,UAAU,GAAG/C,cAAc,CAACyC,SAAS,EAAE,CAAC,CAAC;IACzCO,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,MAAM,GAAGF,UAAU,CAAC,CAAC,CAAC;IACtBG,MAAM,GAAGH,UAAU,CAAC,CAAC,CAAC;EAE1B,IAAII,gBAAgB,GAAGjD,KAAK,CAACqC,QAAQ,CAAC,CAAC,CAAC;IACpCa,gBAAgB,GAAGpD,cAAc,CAACmD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE5C,IAAIG,YAAY,GAAGrD,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIC,SAAS,GAAGvD,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EAClC,IAAI/C,QAAQ,GAAGP,KAAK,CAACwD,OAAO,CAAC,YAAY;IACvC,OAAOvD,OAAO,CAAC+B,IAAI,CAAC;EACtB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,IAAIxB,QAAQ,GAAGR,KAAK,CAACwD,OAAO,CAAC,YAAY;IACvC,OAAOlD,WAAW,CAACC,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,IAAIkD,cAAc,GAAGzD,KAAK,CAACwD,OAAO,CAAC,YAAY;IAC7C,IAAI,CAAC1B,cAAc,IAAIa,YAAY,KAAKjB,kBAAkB,EAAE;MAC1D,OAAOK,QAAQ,CAACxB,QAAQ,EAAE,KAAK,CAAC;IAClC;IAEA,OAAOwB,QAAQ,CAACnB,UAAU,CAACL,QAAQ,EAAEwC,MAAM,CAAC,EAAEA,MAAM,GAAGvC,QAAQ,CAAC;EAClE,CAAC,EAAE,CAACsB,cAAc,EAAEa,YAAY,EAAEZ,QAAQ,EAAExB,QAAQ,EAAEwC,MAAM,EAAEvC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE1EN,yBAAyB,CAAC,YAAY;IACpC,IAAI4B,cAAc,IAAIG,KAAK,IAAIzB,QAAQ,EAAE;MACvCoC,eAAe,CAACpB,OAAO,CAAC;MACxBgB,YAAY,CAAC,CAAC,CAAC,EAAEkB,IAAI,CAACC,IAAI,CAACnD,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC;IACtD;EACF,CAAC,EAAE,CAACsB,cAAc,EAAEG,KAAK,EAAED,IAAI,EAAExB,QAAQ,EAAE0B,IAAI,CAAC,CAAC;EACjDhC,yBAAyB,CAAC,YAAY;IACpC,IAAI0D,EAAE;IAEN,IAAIjB,YAAY,KAAKnB,OAAO,EAAE;MAC5B4B,kBAAkB,CAAC,CAAC,CAACQ,EAAE,GAAGP,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,KAAK,CAAC,CAAC;IAC7G;EACF,CAAC,EAAE,CAACnB,YAAY,CAAC,CAAC;EAClBzC,yBAAyB,CAAC,YAAY;IACpC,IAAI0D,EAAE,EAAEG,EAAE;IAEV,IAAIZ,eAAe,EAAE;MACnB,IAAIR,YAAY,KAAKnB,OAAO,EAAE;QAC5B;QACA,IAAIwC,SAAS,GAAG,CAAC,CAACJ,EAAE,GAAGL,SAAS,CAACM,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,KAAK,CAAC;QACpG,IAAIG,SAAS,GAAG/B,IAAI,GAAGiB,eAAe;QAEtC,IAAIa,SAAS,IAAIC,SAAS,EAAE;UAC1BrB,eAAe,CAACjB,qBAAqB,CAAC;UACtCQ,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,MAAM;UACLS,eAAe,CAACnB,OAAO,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIkB,YAAY,KAAKlB,OAAO,EAAE;QACnC,IAAIqB,QAAQ,KAAKE,MAAM,EAAE;UACvB,IAAIkB,UAAU,GAAG,CAAC,CAACH,EAAE,GAAGR,SAAS,CAACM,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,YAAY,KAAK,CAAC;UAErG,IAAIK,UAAU,GAAGjC,IAAI,GAAGiB,eAAe;UAEvC,IAAIiB,YAAY,GAAGtB,QAAQ;UAC3B,IAAIuB,UAAU,GAAGrB,MAAM,CAAC,CAAC;;UAEzB,IAAIF,QAAQ,KAAKE,MAAM,GAAG,CAAC,EAAE;YAC3BqB,UAAU,GAAGvB,QAAQ;UACvB,CAAC,MAAM,IAAIoB,UAAU,IAAIC,UAAU,EAAE;YACnCC,YAAY,GAAGrB,MAAM;UACvB,CAAC,MAAM;YACLsB,UAAU,GAAGtB,MAAM;UACrB;UAEA,IAAIuB,UAAU,GAAGZ,IAAI,CAACC,IAAI,CAAC,CAACS,YAAY,GAAGC,UAAU,IAAI,CAAC,CAAC;UAC3D7B,YAAY,CAAC,CAAC4B,YAAY,EAAEE,UAAU,EAAED,UAAU,CAAC,CAAC;QACtD,CAAC,MAAM;UACLzB,eAAe,CAAClB,kBAAkB,CAAC;UACnCS,UAAU,CAAC,IAAI,CAAC;QAClB;MACF;IACF;EACF,CAAC,EAAE,CAACQ,YAAY,EAAEG,QAAQ,EAAEE,MAAM,EAAEd,IAAI,EAAEiB,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE7D,IAAIoB,YAAY,GAAG;IACjBtC,KAAK,EAAEA,KAAK;IACZuC,UAAU,EAAE,QAAQ;IACpBC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC;EAED,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAC9D,OAAO,aAAa9E,KAAK,CAAC+E,aAAa,CAAC,MAAM,EAAE;MAC9C,aAAa,EAAE,IAAI;MACnBF,GAAG,EAAEA,GAAG;MACRC,KAAK,EAAEjF,QAAQ,CAAC;QACdmF,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,OAAO;QAChBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,CAAC,IAAI;QACbC,UAAU,EAAE,QAAQ;QACpBC,aAAa,EAAE;MACjB,CAAC,EAAER,KAAK;IACV,CAAC,EAAEF,OAAO,CAAC;EACb,CAAC;EAED,IAAIW,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC1E,GAAG,EAAEgE,GAAG,EAAE;IAC7D,IAAIW,aAAa,GAAG5E,UAAU,CAACL,QAAQ,EAAEM,GAAG,CAAC;IAC7C,OAAO8D,aAAa,CAAC5C,QAAQ,CAACyD,aAAa,EAAE,IAAI,CAAC,EAAEX,GAAG,EAAEN,YAAY,CAAC;EACxE,CAAC;EAED,OAAO,aAAavE,KAAK,CAAC+E,aAAa,CAAC/E,KAAK,CAACyF,QAAQ,EAAE,IAAI,EAAEhC,cAAc,EAAE3B,cAAc,IAAIa,YAAY,KAAKjB,kBAAkB,IAAIiB,YAAY,KAAKhB,qBAAqB,IAAI,aAAa3B,KAAK,CAAC+E,aAAa,CAAC/E,KAAK,CAACyF,QAAQ,EAAE,IAAI,EAAEd,aAAa,CAAC,IAAI,EAAEtB,YAAY,EAAE;IACxQqC,SAAS,EAAE,UAAU;IACrBlB,UAAU,EAAE;EACd,CAAC,CAAC,EAAE7B,YAAY,KAAKnB,OAAO,GAAGmD,aAAa,CAAC5C,QAAQ,CAACxB,QAAQ,EAAE,KAAK,CAAC,EAAEgD,SAAS,EAAEgB,YAAY,CAAC,GAAGgB,kBAAkB,CAACxC,MAAM,EAAEQ,SAAS,CAAC,CAAC,CAAC;AAC5I,CAAC;AAED,IAAIoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjE,QAAQ,CAACkE,WAAW,GAAG,UAAU;AACnC;AAEA,eAAelE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}