{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\overlayPanelGen.jsx\";\nimport React, { Component } from 'react';\nimport { OverlayPanel } from 'primereact/overlaypanel';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../traduttore/const';\nimport { Badge } from 'primereact/badge';\nimport { Accordion, AccordionTab } from 'primereact/accordion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport class OverlayPanelGen extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          label: Costanti[this.props.label],\n          onClick: e => this.op.toggle(e),\n          \"aria-haspopup\": true,\n          \"aria-controls\": \"overlay_panel\",\n          className: \"select-product-button\",\n          children: this.props.badge && /*#__PURE__*/_jsxDEV(Badge, {\n            value: this.props.values.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(OverlayPanel, {\n          ref: el => this.op = el,\n          id: \"overlay_panel\",\n          style: {\n            width: '550px'\n          },\n          className: \"overlaypanel-demo\",\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            activeIndex: 0,\n            children: this.props.values.map(el => {\n              return /*#__PURE__*/_jsxDEV(AccordionTab, {\n                header: \"Condizione \".concat(typeof el === 'object' ? el.acquistando : parseInt(el.split('unità')[0].split('Acquistando')[1]), \" unit\\xE0\"),\n                children: typeof el === 'object' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Acquistando \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: el.acquistando\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 36,\n                      columnNumber: 65\n                    }, this), \" unit\\xE0 di\", /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"gui-father my-2 pt-2 border-top\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex flex-row flex-wrap gui-area-body\",\n                        children: el.unità.split(',').map((obj, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"gui-sons d-flex align-items-center mb-1 mr-1 px-3 py-1\",\n                            children: /*#__PURE__*/_jsxDEV(\"small\", {\n                              children: obj\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 41,\n                              columnNumber: 145\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 41,\n                            columnNumber: 73\n                          }, this)\n                        }, index, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 40,\n                          columnNumber: 69\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 38,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 37,\n                      columnNumber: 57\n                    }, this), \"Ricevi \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: el.ricevi\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 46,\n                      columnNumber: 60\n                    }, this), \" unit\\xE0 di\", /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"gui-father my-2 pt-2 border-top\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex flex-row flex-wrap gui-area-body\",\n                        children: el.di[0].split(',').map((object, indice) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"gui-sons d-flex align-items-center mb-1 mr-1 px-3 py-1\",\n                            children: /*#__PURE__*/_jsxDEV(\"small\", {\n                              children: parseInt(object.split(':')[1]) !== -1 ? object : object.split(':')[0]\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 51,\n                              columnNumber: 145\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 51,\n                            columnNumber: 73\n                          }, this)\n                        }, indice, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 50,\n                          columnNumber: 69\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 48,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 47,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 45\n                }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\".concat(el)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 37\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["React", "Component", "OverlayPanel", "<PERSON><PERSON>", "Toast", "<PERSON><PERSON>", "Badge", "Accordion", "AccordionTab", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OverlayPanelGen", "constructor", "props", "state", "render", "children", "ref", "el", "toast", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "type", "label", "onClick", "e", "op", "toggle", "badge", "value", "values", "length", "id", "style", "width", "activeIndex", "map", "header", "concat", "a<PERSON><PERSON><PERSON><PERSON>", "parseInt", "split", "unità", "obj", "index", "rice<PERSON>", "di", "object", "indice"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/overlayPanelGen.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport { OverlayPanel } from 'primereact/overlaypanel';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../traduttore/const';\nimport { Badge } from 'primereact/badge';\nimport { Accordion, AccordionTab } from 'primereact/accordion';\n\nexport class OverlayPanelGen extends Component {\n\n    constructor(props) {\n        super(props);\n        this.state = {\n        };\n    }\n\n    render() {\n        return (\n            <div>\n                <Toast ref={(el) => this.toast = el} />\n\n                <div className=\"card\">\n                    <Button type=\"button\" label={Costanti[this.props.label]} onClick={(e) => this.op.toggle(e)} aria-haspopup aria-controls=\"overlay_panel\" className=\"select-product-button\" >\n                        {this.props.badge &&\n                            <Badge value={this.props.values.length} />\n                        }\n                    </Button>\n                    <OverlayPanel ref={(el) => this.op = el} id=\"overlay_panel\" style={{ width: '550px' }} className=\"overlaypanel-demo\">\n                        <Accordion activeIndex={0}>\n                            {this.props.values.map(el => {\n                                return (\n                                    <AccordionTab header={`Condizione ${typeof (el) === 'object' ? el.acquistando : parseInt(el.split('unità')[0].split('Acquistando')[1])} unità`}>\n                                        {typeof (el) === 'object' ? (\n                                            <div>\n                                                <span>\n                                                    Acquistando <strong>{el.acquistando}</strong> unità di\n                                                        <div className='gui-father my-2 pt-2 border-top'>\n                                                            <div className=\"d-flex flex-row flex-wrap gui-area-body\">\n                                                                {el.unità.split(',').map((obj, index) =>\n                                                                    <React.Fragment key={index}>\n                                                                        <div className='gui-sons d-flex align-items-center mb-1 mr-1 px-3 py-1'><small>{obj}</small></div>\n                                                                    </React.Fragment>\n                                                                )}\n                                                            </div>\n                                                        </div>\n                                                    Ricevi <strong>{el.ricevi}</strong> unità di\n                                                        <div className='gui-father my-2 pt-2 border-top'>\n                                                            <div className=\"d-flex flex-row flex-wrap gui-area-body\">\n                                                                {el.di[0].split(',').map((object, indice) =>\n                                                                    <React.Fragment key={indice}>\n                                                                        <div className='gui-sons d-flex align-items-center mb-1 mr-1 px-3 py-1'><small>{parseInt(object.split(':')[1]) !== -1 ? object : object.split(':')[0]}</small></div>\n                                                                    </React.Fragment>\n                                                                )}\n                                                            </div>\n                                                        </div>\n                                                </span>\n                                            </div>\n                                        ) : (\n                                            <>\n                                                <span>\n                                                    {`${el}`}\n                                                </span>\n                                                <br />\n                                            </>\n                                        )\n                                        }\n                                    </AccordionTab>\n                                )\n                            })}\n                        </Accordion>\n                    </OverlayPanel>\n                </div>\n            </div>\n        )\n    }\n}"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,EAAEC,YAAY,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,OAAO,MAAMC,eAAe,SAASZ,SAAS,CAAC;EAE3Ca,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CACb,CAAC;EACL;EAEAC,MAAMA,CAAA,EAAG;IACL,oBACIP,OAAA;MAAAQ,QAAA,gBACIR,OAAA,CAACN,KAAK;QAACe,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACC,KAAK,GAAGD;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCf,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAR,QAAA,gBACjBR,OAAA,CAACP,MAAM;UAACwB,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAEvB,QAAQ,CAAC,IAAI,CAACU,KAAK,CAACa,KAAK,CAAE;UAACC,OAAO,EAAGC,CAAC,IAAK,IAAI,CAACC,EAAE,CAACC,MAAM,CAACF,CAAC,CAAE;UAAC,qBAAa;UAAC,iBAAc,eAAe;UAACJ,SAAS,EAAC,uBAAuB;UAAAR,QAAA,EACpK,IAAI,CAACH,KAAK,CAACkB,KAAK,iBACbvB,OAAA,CAACJ,KAAK;YAAC4B,KAAK,EAAE,IAAI,CAACnB,KAAK,CAACoB,MAAM,CAACC;UAAO;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1C,CAAC,eACTf,OAAA,CAACR,YAAY;UAACiB,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACW,EAAE,GAAGX,EAAG;UAACiB,EAAE,EAAC,eAAe;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAACb,SAAS,EAAC,mBAAmB;UAAAR,QAAA,eAChHR,OAAA,CAACH,SAAS;YAACiC,WAAW,EAAE,CAAE;YAAAtB,QAAA,EACrB,IAAI,CAACH,KAAK,CAACoB,MAAM,CAACM,GAAG,CAACrB,EAAE,IAAI;cACzB,oBACIV,OAAA,CAACF,YAAY;gBAACkC,MAAM,gBAAAC,MAAA,CAAgB,OAAQvB,EAAG,KAAK,QAAQ,GAAGA,EAAE,CAACwB,WAAW,GAAGC,QAAQ,CAACzB,EAAE,CAAC0B,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,cAAS;gBAAA5B,QAAA,EAC1I,OAAQE,EAAG,KAAK,QAAQ,gBACrBV,OAAA;kBAAAQ,QAAA,eACIR,OAAA;oBAAAQ,QAAA,GAAM,cACU,eAAAR,OAAA;sBAAAQ,QAAA,EAASE,EAAE,CAACwB;oBAAW;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,gBACzC,eAAAf,OAAA;sBAAKgB,SAAS,EAAC,iCAAiC;sBAAAR,QAAA,eAC5CR,OAAA;wBAAKgB,SAAS,EAAC,yCAAyC;wBAAAR,QAAA,EACnDE,EAAE,CAAC2B,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC,CAACL,GAAG,CAAC,CAACO,GAAG,EAAEC,KAAK,kBAChCvC,OAAA,CAACV,KAAK,CAACW,QAAQ;0BAAAO,QAAA,eACXR,OAAA;4BAAKgB,SAAS,EAAC,wDAAwD;4BAAAR,QAAA,eAACR,OAAA;8BAAAQ,QAAA,EAAQ8B;4BAAG;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBAAC,GADjFwB,KAAK;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEV,CACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,WACH,eAAAf,OAAA;sBAAAQ,QAAA,EAASE,EAAE,CAAC8B;oBAAM;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,gBAC/B,eAAAf,OAAA;sBAAKgB,SAAS,EAAC,iCAAiC;sBAAAR,QAAA,eAC5CR,OAAA;wBAAKgB,SAAS,EAAC,yCAAyC;wBAAAR,QAAA,EACnDE,EAAE,CAAC+B,EAAE,CAAC,CAAC,CAAC,CAACL,KAAK,CAAC,GAAG,CAAC,CAACL,GAAG,CAAC,CAACW,MAAM,EAAEC,MAAM,kBACpC3C,OAAA,CAACV,KAAK,CAACW,QAAQ;0BAAAO,QAAA,eACXR,OAAA;4BAAKgB,SAAS,EAAC,wDAAwD;4BAAAR,QAAA,eAACR,OAAA;8BAAAQ,QAAA,EAAQ2B,QAAQ,CAACO,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAGM,MAAM,GAAGA,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;4BAAC;8BAAAxB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBAAC,GADnJ4B,MAAM;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEX,CACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAENf,OAAA,CAAAE,SAAA;kBAAAM,QAAA,gBACIR,OAAA;oBAAAQ,QAAA,KAAAyB,MAAA,CACQvB,EAAE;kBAAA;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACPf,OAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,eACR;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAES,CAAC;YAEvB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}