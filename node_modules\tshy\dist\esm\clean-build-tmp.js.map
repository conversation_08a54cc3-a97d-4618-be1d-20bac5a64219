{"version": 3, "file": "clean-build-tmp.js", "sourceRoot": "", "sources": ["../../src/clean-build-tmp.ts"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,mDAAmD;AACnD,6CAA6C;AAC7C,6DAA6D;AAC7D,kEAAkE;AAClE,uBAAuB;AAEvB,OAAO,EAAE,WAAW,EAAE,MAAM,IAAI,CAAA;AAChC,OAAO,EAAE,KAAK,EAAE,MAAM,MAAM,CAAA;AAC5B,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,oBAAoB,MAAM,6BAA6B,CAAA;AAE9D,MAAM,mBAAmB,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;IACzD,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,EAAE;QAC7C,aAAa,EAAE,IAAI;KACpB,CAAC,CAAA;IACF,IAAI,OAAO,GAA4B,SAAS,CAAA;IAChD,IAAI,CAAC;QACH,OAAO,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAA;IAC/C,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IACV,wBAAwB;IACxB,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,UAAU,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,CAAA;IACtC,CAAC;IACD,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,MAAM,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAA;QACtC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;YACpB,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;YACrC,SAAQ;QACV,CAAC;QACD,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAA;QACrC,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;YACnB,SAAQ;QACV,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,GAAG,GAAG,IAAI,GAAG,GAAG,CAAA;YAChB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;QACrD,CAAC;QAED,MAAM,WAAW,GACf,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC;YAClD,CAAC,CAAC,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC/C,CAAC,CAAC,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC/C,CAAC,CAAC,EAAE,CAAA;QACN,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACrB,IAAI,GAAG,GAAG,IAAI,CAAA;QACd,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;gBACjC,GAAG,GAAG,KAAK,CAAA;gBACX,MAAK;YACP,CAAC;QACH,CAAC;QACD,IAAI,GAAG,EAAE,CAAC;YACR,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAA;YACjD,UAAU,CAAC;gBACT,GAAG,IAAI,IAAI,UAAU,EAAE;gBACvB,GAAG,IAAI,IAAI,UAAU,MAAM;aAC5B,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,eAAe,GAAG,EAAE;IAClB,MAAM,MAAM,GAAG,oBAAoB,EAAE,CAAA;IACrC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;QACxC,OAAO,UAAU,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;IAED,IAAI,UAAU,GAAyB,SAAS,CAAA;IAChD,IAAI,CAAC;QACH,UAAU,GAAG,WAAW,CAAC,mBAAmB,CAAC,CAAA;IAC/C,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IACV,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;QACxB,OAAO,UAAU,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;IAED,kDAAkD;IAClD,KAAK,MAAM,OAAO,IAAI,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;QACjD,IAAI,OAAO,KAAK,OAAO;YAAE,SAAQ;QACjC,mBAAmB,CAAC,GAAG,EAAE,eAAe,OAAO,EAAE,CAAC,CAAA;IACpD,CAAC;AACH,CAAC,CAAA", "sourcesContent": ["// Remove the .tshy-build folder, but ONLY if\n// the \"incremental\" config value is not set, or if\n// it does not contain any tsbuildinfo files.\n// If we are in incremental mode, and have tsbuildinfo files,\n// then find and remove any files here that do not have a matching\n// source file in ./src\n\nimport { readdirSync } from 'fs'\nimport { parse } from 'path'\nimport { rimrafSync } from 'rimraf'\nimport * as console from './console.js'\nimport readTypescriptConfig from './read-typescript-config.js'\n\nconst cleanRemovedOutputs = (path: string, root: string) => {\n  const entries = readdirSync(`${root}/${path}`, {\n    withFileTypes: true,\n  })\n  let sources: Set<string> | undefined = undefined\n  try {\n    sources = new Set(readdirSync(`src/${path}`))\n  } catch {}\n  // directory was removed\n  if (!sources) {\n    return rimrafSync(`${root}/${path}`)\n  }\n  for (const e of entries) {\n    const outputFile = `${path}/${e.name}`\n    if (e.isDirectory()) {\n      cleanRemovedOutputs(outputFile, root)\n      continue\n    }\n    let { ext, name } = parse(outputFile)\n    if (ext === '.map') {\n      continue\n    }\n    if (name.endsWith('.d') && ext.endsWith('ts')) {\n      ext = '.d' + ext\n      name = name.substring(0, name.length - '.d'.length)\n    }\n\n    const inputSearch =\n      ext === '.js' || ext === '.d.ts' ? ['.tsx', '.ts']\n      : ext === '.mjs' || ext === '.d.mts' ? ['.mts']\n      : ext === '.cjs' || ext === '.d.cts' ? ['.cts']\n      : []\n    inputSearch.push(ext)\n    let del = true\n    for (const ext of inputSearch) {\n      if (sources.has(`${name}${ext}`)) {\n        del = false\n        break\n      }\n    }\n    if (del) {\n      console.debug('removing output file', outputFile)\n      rimrafSync([\n        `${root}/${outputFile}`,\n        `${root}/${outputFile}.map`,\n      ])\n    }\n  }\n}\n\nexport default () => {\n  const config = readTypescriptConfig()\n  if (config.options.incremental !== true) {\n    return rimrafSync('.tshy-build')\n  }\n\n  let buildInfos: string[] | undefined = undefined\n  try {\n    buildInfos = readdirSync('.tshy-build/.tshy')\n  } catch {}\n  if (!buildInfos?.length) {\n    return rimrafSync('.tshy-build')\n  }\n\n  // delete anything that has been removed from src.\n  for (const dialect of readdirSync('.tshy-build')) {\n    if (dialect === '.tshy') continue\n    cleanRemovedOutputs('.', `.tshy-build/${dialect}`)\n  }\n}\n"]}