{"name": "tap", "version": "19.2.5", "description": "A Test-Anything-Protocol library for JavaScript", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "bin": {"tap": "dist/esm/run.mjs"}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc --tsconfig tsconfig/esm.json ./src/*.ts"}, "author": "<PERSON> <<EMAIL>> (https://blog.izs.me)", "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/after": "1.1.31", "@tapjs/after-each": "2.0.8", "@tapjs/asserts": "2.0.8", "@tapjs/before": "2.0.8", "@tapjs/before-each": "2.0.8", "@tapjs/chdir": "1.1.4", "@tapjs/core": "2.1.6", "@tapjs/filter": "2.0.8", "@tapjs/fixture": "2.0.8", "@tapjs/intercept": "2.0.8", "@tapjs/mock": "2.1.6", "@tapjs/node-serialize": "2.0.8", "@tapjs/run": "2.1.7", "@tapjs/snapshot": "2.0.8", "@tapjs/spawn": "2.0.8", "@tapjs/stdin": "2.0.8", "@tapjs/test": "2.2.4", "@tapjs/typescript": "1.4.13", "@tapjs/worker": "2.0.8", "resolve-import": "^1.4.5"}, "tap": {"typecheck": false, "coverage-map": "map.js"}, "homepage": "http://www.node-tap.org/", "engines": {"node": "16 >=16.17.0 || 18 >= 18.6.0 || >=20"}, "keywords": ["assert", "tap", "test", "testing", "framework", "cli", "tapjs", "tapjs plugin"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/tapjs.git"}}