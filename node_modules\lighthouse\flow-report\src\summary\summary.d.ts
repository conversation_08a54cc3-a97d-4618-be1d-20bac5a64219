/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
import { FunctionComponent } from 'preact';
/**
 * The div should behave like a JSX <>...</>. This still allows us to identify "rows" with CSS selectors.
 */
declare const SummaryFlowStep: FunctionComponent<{
    lhr: LH.Result;
    label: string;
    hashIndex: number;
}>;
declare const SummaryHeader: FunctionComponent;
declare const Summary: FunctionComponent;
export { SummaryFlowStep, SummaryHeader, Summary, };
//# sourceMappingURL=summary.d.ts.map