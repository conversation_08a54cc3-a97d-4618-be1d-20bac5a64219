/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

import log from 'lighthouse-logger';

import * as i18n from '../../lib/i18n/i18n.js';

/* eslint-disable max-len */
const UIStrings = {
  /**
   * @description A warning that previously-saved data may have affected the measured performance and instructions on how to avoid the problem. "locations" will be a list of possible types of data storage locations, e.g. "IndexedDB",  "Local Storage", or "Web SQL".
   * @example {IndexedDB, Local Storage} locations
   */
  warningData: `{locationCount, plural,
    =1 {There may be stored data affecting loading performance in this location: {locations}. ` +
      `Audit this page in an incognito window to prevent those resources ` +
      `from affecting your scores.}
    other {There may be stored data affecting loading ` +
      `performance in these locations: {locations}. ` +
      `Audit this page in an incognito window to prevent those resources ` +
      `from affecting your scores.}
  }`,
  /** A warning that the data in the browser cache may have affected the measured performance because the operation to clear the browser cache timed out. */
  warningCacheTimeout: 'Clearing the browser cache timed out. Try auditing this page again and file a bug if the issue persists.',
  /** A warning that the data on the page's origin may have affected the measured performance because the operation to clear the origin data timed out. */
  warningOriginDataTimeout: 'Clearing the origin data timed out. Try auditing this page again and file a bug if the issue persists.',
};
/* eslint-enable max-len */

const str_ = i18n.createIcuMessageFn(import.meta.url, UIStrings);


/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {string} url
 * @return {Promise<LH.IcuMessage[]>}
 */
async function clearDataForOrigin(session, url) {
  const status = {msg: 'Cleaning origin data', id: 'lh:storage:clearDataForOrigin'};
  log.time(status);

  const warnings = [];

  const origin = new URL(url).origin;

  // Clear some types of storage.
  // Cookies are not cleared, so the user isn't logged out.
  // indexeddb, websql, and localstorage are not cleared to prevent loss of potentially important data.
  //   https://chromedevtools.github.io/debugger-protocol-viewer/tot/Storage/#type-StorageType
  const typesToClear = [
    // 'cookies',
    'file_systems',
    'shader_cache',
    'service_workers',
    'cache_storage',
  ].join(',');

  // `Storage.clearDataForOrigin` is one of our PROTOCOL_TIMEOUT culprits and this command is also
  // run in the context of PAGE_HUNG to cleanup. We'll keep the timeout low and just warn if it fails.
  session.setNextProtocolTimeout(5000);

  try {
    await session.sendCommand('Storage.clearDataForOrigin', {
      origin: origin,
      storageTypes: typesToClear,
    });
  } catch (err) {
    if (/** @type {LH.LighthouseError} */ (err).code === 'PROTOCOL_TIMEOUT') {
      log.warn('Driver', 'clearDataForOrigin timed out');
      warnings.push(str_(UIStrings.warningOriginDataTimeout));
    } else {
      throw err;
    }
  } finally {
    log.timeEnd(status);
  }

  return warnings;
}

/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {string} url
 * @return {Promise<LH.IcuMessage | undefined>}
 */
async function getImportantStorageWarning(session, url) {
  const usageData = await session.sendCommand('Storage.getUsageAndQuota', {
    origin: url,
  });
  /** @type {Record<string, string>} */
  const storageTypeNames = {
    local_storage: 'Local Storage',
    indexeddb: 'IndexedDB',
    websql: 'Web SQL',
  };
  const locations = usageData.usageBreakdown
    .filter(usage => usage.usage)
    .map(usage => storageTypeNames[usage.storageType] || '')
    .filter(Boolean);
  if (locations.length) {
    // TODO(#11495): Use Intl.ListFormat with Node 12
    return str_(UIStrings.warningData, {
      locations: locations.join(', '),
      locationCount: locations.length,
    });
  }
}


/**
 * Clear the network cache on disk and in memory.
 * @param {LH.Gatherer.FRProtocolSession} session
 * @return {Promise<LH.IcuMessage[]>}
 */
async function clearBrowserCaches(session) {
  const status = {msg: 'Cleaning browser cache', id: 'lh:storage:clearBrowserCaches'};
  log.time(status);

  const warnings = [];

  try {
    // Wipe entire disk cache
    await session.sendCommand('Network.clearBrowserCache');
    // Toggle 'Disable Cache' to evict the memory cache
    await session.sendCommand('Network.setCacheDisabled', {cacheDisabled: true});
    await session.sendCommand('Network.setCacheDisabled', {cacheDisabled: false});
  } catch (err) {
    if (/** @type {LH.LighthouseError} */ (err).code === 'PROTOCOL_TIMEOUT') {
      log.warn('Driver', 'clearBrowserCaches timed out');
      warnings.push(str_(UIStrings.warningCacheTimeout));
    } else {
      throw err;
    }
  } finally {
    log.timeEnd(status);
  }

  return warnings;
}

export {
  clearDataForOrigin,
  clearBrowserCaches,
  getImportantStorageWarning,
  UIStrings,
};
