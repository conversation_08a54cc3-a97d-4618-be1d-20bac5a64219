{"name": "sass-lookup", "version": "5.0.1", "description": "Get the file associated with a Sass import", "main": "index.js", "files": ["bin/cli.js", "index.js"], "bin": {"sass-lookup": "bin/cli.js"}, "scripts": {"lint": "xo", "fix": "xo --fix", "uvu": "uvu test -i fixtures", "test": "npm run lint && npm run uvu", "test:ci": "c8 npm run uvu"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-sass-lookup.git"}, "keywords": ["sass", "lookup", "dependency", "import", "resolution"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-sass-lookup/issues"}, "homepage": "https://github.com/dependents/node-sass-lookup", "engines": {"node": ">=14"}, "dependencies": {"commander": "^10.0.1"}, "devDependencies": {"c8": "^7.13.0", "mock-fs": "^5.2.0", "uvu": "^0.5.6", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}