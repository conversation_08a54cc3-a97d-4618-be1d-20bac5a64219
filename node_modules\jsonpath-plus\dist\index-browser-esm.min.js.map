{"version": 3, "file": "index-browser-esm.min.js", "sources": ["../node_modules/.pnpm/jsep@1.4.0/node_modules/jsep/dist/jsep.js", "../node_modules/.pnpm/@jsep-plugin+regex@1.0.4_jsep@1.4.0/node_modules/@jsep-plugin/regex/dist/index.js", "../node_modules/.pnpm/@jsep-plugin+assignment@1.3.0_jsep@1.4.0/node_modules/@jsep-plugin/assignment/dist/index.js", "../src/Safe-Script.js", "../src/jsonpath.js", "../src/jsonpath-browser.js"], "sourcesContent": ["/**\n * @implements {IHooks}\n */\nclass Hooks {\n\t/**\n\t * @callback HookCallback\n\t * @this {*|Jsep} this\n\t * @param {Jsep} env\n\t * @returns: void\n\t */\n\t/**\n\t * Adds the given callback to the list of callbacks for the given hook.\n\t *\n\t * The callback will be invoked when the hook it is registered for is run.\n\t *\n\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t *\n\t * @param {string|object} name The name of the hook, or an object of callbacks keyed by name\n\t * @param {HookCallback|boolean} callback The callback function which is given environment variables.\n\t * @param {?boolean} [first=false] Will add the hook to the top of the list (defaults to the bottom)\n\t * @public\n\t */\n\tadd(name, callback, first) {\n\t\tif (typeof arguments[0] != 'string') {\n\t\t\t// Multiple hook callbacks, keyed by name\n\t\t\tfor (let name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\t\tthis[name] = this[name] || [];\n\n\t\t\t\tif (callback) {\n\t\t\t\t\tthis[name][first ? 'unshift' : 'push'](callback);\n\t\t\t\t}\n\t\t\t}, this);\n\t\t}\n\t}\n\n\t/**\n\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t *\n\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t *\n\t * @param {string} name The name of the hook.\n\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t * @public\n\t */\n\trun(name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * @implements {IPlugins}\n */\nclass Plugins {\n\tconstructor(jsep) {\n\t\tthis.jsep = jsep;\n\t\tthis.registered = {};\n\t}\n\n\t/**\n\t * @callback PluginSetup\n\t * @this {Jsep} jsep\n\t * @returns: void\n\t */\n\t/**\n\t * Adds the given plugin(s) to the registry\n\t *\n\t * @param {object} plugins\n\t * @param {string} plugins.name The name of the plugin\n\t * @param {PluginSetup} plugins.init The init function\n\t * @public\n\t */\n\tregister(...plugins) {\n\t\tplugins.forEach((plugin) => {\n\t\t\tif (typeof plugin !== 'object' || !plugin.name || !plugin.init) {\n\t\t\t\tthrow new Error('Invalid JSEP plugin format');\n\t\t\t}\n\t\t\tif (this.registered[plugin.name]) {\n\t\t\t\t// already registered. Ignore.\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tplugin.init(this.jsep);\n\t\t\tthis.registered[plugin.name] = plugin;\n\t\t});\n\t}\n}\n\n//     JavaScript Expression Parser (JSEP) 1.4.0\n\nclass Jsep {\n\t/**\n\t * @returns {string}\n\t */\n\tstatic get version() {\n\t\t// To be filled in by the template\n\t\treturn '1.4.0';\n\t}\n\n\t/**\n\t * @returns {string}\n\t */\n\tstatic toString() {\n\t\treturn 'JavaScript Expression Parser (JSEP) v' + Jsep.version;\n\t};\n\n\t// ==================== CONFIG ================================\n\t/**\n\t * @method addUnaryOp\n\t * @param {string} op_name The name of the unary op to add\n\t * @returns {Jsep}\n\t */\n\tstatic addUnaryOp(op_name) {\n\t\tJsep.max_unop_len = Math.max(op_name.length, Jsep.max_unop_len);\n\t\tJsep.unary_ops[op_name] = 1;\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method jsep.addBinaryOp\n\t * @param {string} op_name The name of the binary op to add\n\t * @param {number} precedence The precedence of the binary op (can be a float). Higher number = higher precedence\n\t * @param {boolean} [isRightAssociative=false] whether operator is right-associative\n\t * @returns {Jsep}\n\t */\n\tstatic addBinaryOp(op_name, precedence, isRightAssociative) {\n\t\tJsep.max_binop_len = Math.max(op_name.length, Jsep.max_binop_len);\n\t\tJsep.binary_ops[op_name] = precedence;\n\t\tif (isRightAssociative) {\n\t\t\tJsep.right_associative.add(op_name);\n\t\t}\n\t\telse {\n\t\t\tJsep.right_associative.delete(op_name);\n\t\t}\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method addIdentifierChar\n\t * @param {string} char The additional character to treat as a valid part of an identifier\n\t * @returns {Jsep}\n\t */\n\tstatic addIdentifierChar(char) {\n\t\tJsep.additional_identifier_chars.add(char);\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method addLiteral\n\t * @param {string} literal_name The name of the literal to add\n\t * @param {*} literal_value The value of the literal\n\t * @returns {Jsep}\n\t */\n\tstatic addLiteral(literal_name, literal_value) {\n\t\tJsep.literals[literal_name] = literal_value;\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeUnaryOp\n\t * @param {string} op_name The name of the unary op to remove\n\t * @returns {Jsep}\n\t */\n\tstatic removeUnaryOp(op_name) {\n\t\tdelete Jsep.unary_ops[op_name];\n\t\tif (op_name.length === Jsep.max_unop_len) {\n\t\t\tJsep.max_unop_len = Jsep.getMaxKeyLen(Jsep.unary_ops);\n\t\t}\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeAllUnaryOps\n\t * @returns {Jsep}\n\t */\n\tstatic removeAllUnaryOps() {\n\t\tJsep.unary_ops = {};\n\t\tJsep.max_unop_len = 0;\n\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeIdentifierChar\n\t * @param {string} char The additional character to stop treating as a valid part of an identifier\n\t * @returns {Jsep}\n\t */\n\tstatic removeIdentifierChar(char) {\n\t\tJsep.additional_identifier_chars.delete(char);\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeBinaryOp\n\t * @param {string} op_name The name of the binary op to remove\n\t * @returns {Jsep}\n\t */\n\tstatic removeBinaryOp(op_name) {\n\t\tdelete Jsep.binary_ops[op_name];\n\n\t\tif (op_name.length === Jsep.max_binop_len) {\n\t\t\tJsep.max_binop_len = Jsep.getMaxKeyLen(Jsep.binary_ops);\n\t\t}\n\t\tJsep.right_associative.delete(op_name);\n\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeAllBinaryOps\n\t * @returns {Jsep}\n\t */\n\tstatic removeAllBinaryOps() {\n\t\tJsep.binary_ops = {};\n\t\tJsep.max_binop_len = 0;\n\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeLiteral\n\t * @param {string} literal_name The name of the literal to remove\n\t * @returns {Jsep}\n\t */\n\tstatic removeLiteral(literal_name) {\n\t\tdelete Jsep.literals[literal_name];\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeAllLiterals\n\t * @returns {Jsep}\n\t */\n\tstatic removeAllLiterals() {\n\t\tJsep.literals = {};\n\n\t\treturn Jsep;\n\t}\n\t// ==================== END CONFIG ============================\n\n\n\t/**\n\t * @returns {string}\n\t */\n\tget char() {\n\t\treturn this.expr.charAt(this.index);\n\t}\n\n\t/**\n\t * @returns {number}\n\t */\n\tget code() {\n\t\treturn this.expr.charCodeAt(this.index);\n\t};\n\n\n\t/**\n\t * @param {string} expr a string with the passed in express\n\t * @returns Jsep\n\t */\n\tconstructor(expr) {\n\t\t// `index` stores the character number we are currently at\n\t\t// All of the gobbles below will modify `index` as we move along\n\t\tthis.expr = expr;\n\t\tthis.index = 0;\n\t}\n\n\t/**\n\t * static top-level parser\n\t * @returns {jsep.Expression}\n\t */\n\tstatic parse(expr) {\n\t\treturn (new Jsep(expr)).parse();\n\t}\n\n\t/**\n\t * Get the longest key length of any object\n\t * @param {object} obj\n\t * @returns {number}\n\t */\n\tstatic getMaxKeyLen(obj) {\n\t\treturn Math.max(0, ...Object.keys(obj).map(k => k.length));\n\t}\n\n\t/**\n\t * `ch` is a character code in the next three functions\n\t * @param {number} ch\n\t * @returns {boolean}\n\t */\n\tstatic isDecimalDigit(ch) {\n\t\treturn (ch >= 48 && ch <= 57); // 0...9\n\t}\n\n\t/**\n\t * Returns the precedence of a binary operator or `0` if it isn't a binary operator. Can be float.\n\t * @param {string} op_val\n\t * @returns {number}\n\t */\n\tstatic binaryPrecedence(op_val) {\n\t\treturn Jsep.binary_ops[op_val] || 0;\n\t}\n\n\t/**\n\t * Looks for start of identifier\n\t * @param {number} ch\n\t * @returns {boolean}\n\t */\n\tstatic isIdentifierStart(ch) {\n\t\treturn  (ch >= 65 && ch <= 90) || // A...Z\n\t\t\t(ch >= 97 && ch <= 122) || // a...z\n\t\t\t(ch >= 128 && !Jsep.binary_ops[String.fromCharCode(ch)]) || // any non-ASCII that is not an operator\n\t\t\t(Jsep.additional_identifier_chars.has(String.fromCharCode(ch))); // additional characters\n\t}\n\n\t/**\n\t * @param {number} ch\n\t * @returns {boolean}\n\t */\n\tstatic isIdentifierPart(ch) {\n\t\treturn Jsep.isIdentifierStart(ch) || Jsep.isDecimalDigit(ch);\n\t}\n\n\t/**\n\t * throw error at index of the expression\n\t * @param {string} message\n\t * @throws\n\t */\n\tthrowError(message) {\n\t\tconst error = new Error(message + ' at character ' + this.index);\n\t\terror.index = this.index;\n\t\terror.description = message;\n\t\tthrow error;\n\t}\n\n\t/**\n\t * Run a given hook\n\t * @param {string} name\n\t * @param {jsep.Expression|false} [node]\n\t * @returns {?jsep.Expression}\n\t */\n\trunHook(name, node) {\n\t\tif (Jsep.hooks[name]) {\n\t\t\tconst env = { context: this, node };\n\t\t\tJsep.hooks.run(name, env);\n\t\t\treturn env.node;\n\t\t}\n\t\treturn node;\n\t}\n\n\t/**\n\t * Runs a given hook until one returns a node\n\t * @param {string} name\n\t * @returns {?jsep.Expression}\n\t */\n\tsearchHook(name) {\n\t\tif (Jsep.hooks[name]) {\n\t\t\tconst env = { context: this };\n\t\t\tJsep.hooks[name].find(function (callback) {\n\t\t\t\tcallback.call(env.context, env);\n\t\t\t\treturn env.node;\n\t\t\t});\n\t\t\treturn env.node;\n\t\t}\n\t}\n\n\t/**\n\t * Push `index` up to the next non-space character\n\t */\n\tgobbleSpaces() {\n\t\tlet ch = this.code;\n\t\t// Whitespace\n\t\twhile (ch === Jsep.SPACE_CODE\n\t\t|| ch === Jsep.TAB_CODE\n\t\t|| ch === Jsep.LF_CODE\n\t\t|| ch === Jsep.CR_CODE) {\n\t\t\tch = this.expr.charCodeAt(++this.index);\n\t\t}\n\t\tthis.runHook('gobble-spaces');\n\t}\n\n\t/**\n\t * Top-level method to parse all expressions and returns compound or single node\n\t * @returns {jsep.Expression}\n\t */\n\tparse() {\n\t\tthis.runHook('before-all');\n\t\tconst nodes = this.gobbleExpressions();\n\n\t\t// If there's only one expression just try returning the expression\n\t\tconst node = nodes.length === 1\n\t\t  ? nodes[0]\n\t\t\t: {\n\t\t\t\ttype: Jsep.COMPOUND,\n\t\t\t\tbody: nodes\n\t\t\t};\n\t\treturn this.runHook('after-all', node);\n\t}\n\n\t/**\n\t * top-level parser (but can be reused within as well)\n\t * @param {number} [untilICode]\n\t * @returns {jsep.Expression[]}\n\t */\n\tgobbleExpressions(untilICode) {\n\t\tlet nodes = [], ch_i, node;\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tch_i = this.code;\n\n\t\t\t// Expressions can be separated by semicolons, commas, or just inferred without any\n\t\t\t// separators\n\t\t\tif (ch_i === Jsep.SEMCOL_CODE || ch_i === Jsep.COMMA_CODE) {\n\t\t\t\tthis.index++; // ignore separators\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Try to gobble each expression individually\n\t\t\t\tif (node = this.gobbleExpression()) {\n\t\t\t\t\tnodes.push(node);\n\t\t\t\t\t// If we weren't able to find a binary expression and are out of room, then\n\t\t\t\t\t// the expression passed in probably has too much\n\t\t\t\t}\n\t\t\t\telse if (this.index < this.expr.length) {\n\t\t\t\t\tif (ch_i === untilICode) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tthis.throwError('Unexpected \"' + this.char + '\"');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn nodes;\n\t}\n\n\t/**\n\t * The main parsing function.\n\t * @returns {?jsep.Expression}\n\t */\n\tgobbleExpression() {\n\t\tconst node = this.searchHook('gobble-expression') || this.gobbleBinaryExpression();\n\t\tthis.gobbleSpaces();\n\n\t\treturn this.runHook('after-expression', node);\n\t}\n\n\t/**\n\t * Search for the operation portion of the string (e.g. `+`, `===`)\n\t * Start by taking the longest possible binary operations (3 characters: `===`, `!==`, `>>>`)\n\t * and move down from 3 to 2 to 1 character until a matching binary operation is found\n\t * then, return that binary operation\n\t * @returns {string|boolean}\n\t */\n\tgobbleBinaryOp() {\n\t\tthis.gobbleSpaces();\n\t\tlet to_check = this.expr.substr(this.index, Jsep.max_binop_len);\n\t\tlet tc_len = to_check.length;\n\n\t\twhile (tc_len > 0) {\n\t\t\t// Don't accept a binary op when it is an identifier.\n\t\t\t// Binary ops that start with a identifier-valid character must be followed\n\t\t\t// by a non identifier-part valid character\n\t\t\tif (Jsep.binary_ops.hasOwnProperty(to_check) && (\n\t\t\t\t!Jsep.isIdentifierStart(this.code) ||\n\t\t\t\t(this.index + to_check.length < this.expr.length && !Jsep.isIdentifierPart(this.expr.charCodeAt(this.index + to_check.length)))\n\t\t\t)) {\n\t\t\t\tthis.index += tc_len;\n\t\t\t\treturn to_check;\n\t\t\t}\n\t\t\tto_check = to_check.substr(0, --tc_len);\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * This function is responsible for gobbling an individual expression,\n\t * e.g. `1`, `1+2`, `a+(b*2)-Math.sqrt(2)`\n\t * @returns {?jsep.BinaryExpression}\n\t */\n\tgobbleBinaryExpression() {\n\t\tlet node, biop, prec, stack, biop_info, left, right, i, cur_biop;\n\n\t\t// First, try to get the leftmost thing\n\t\t// Then, check to see if there's a binary operator operating on that leftmost thing\n\t\t// Don't gobbleBinaryOp without a left-hand-side\n\t\tleft = this.gobbleToken();\n\t\tif (!left) {\n\t\t\treturn left;\n\t\t}\n\t\tbiop = this.gobbleBinaryOp();\n\n\t\t// If there wasn't a binary operator, just return the leftmost node\n\t\tif (!biop) {\n\t\t\treturn left;\n\t\t}\n\n\t\t// Otherwise, we need to start a stack to properly place the binary operations in their\n\t\t// precedence structure\n\t\tbiop_info = { value: biop, prec: Jsep.binaryPrecedence(biop), right_a: Jsep.right_associative.has(biop) };\n\n\t\tright = this.gobbleToken();\n\n\t\tif (!right) {\n\t\t\tthis.throwError(\"Expected expression after \" + biop);\n\t\t}\n\n\t\tstack = [left, biop_info, right];\n\n\t\t// Properly deal with precedence using [recursive descent](http://www.engr.mun.ca/~theo/Misc/exp_parsing.htm)\n\t\twhile ((biop = this.gobbleBinaryOp())) {\n\t\t\tprec = Jsep.binaryPrecedence(biop);\n\n\t\t\tif (prec === 0) {\n\t\t\t\tthis.index -= biop.length;\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tbiop_info = { value: biop, prec, right_a: Jsep.right_associative.has(biop) };\n\n\t\t\tcur_biop = biop;\n\n\t\t\t// Reduce: make a binary expression from the three topmost entries.\n\t\t\tconst comparePrev = prev => biop_info.right_a && prev.right_a\n\t\t\t\t? prec > prev.prec\n\t\t\t\t: prec <= prev.prec;\n\t\t\twhile ((stack.length > 2) && comparePrev(stack[stack.length - 2])) {\n\t\t\t\tright = stack.pop();\n\t\t\t\tbiop = stack.pop().value;\n\t\t\t\tleft = stack.pop();\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.BINARY_EXP,\n\t\t\t\t\toperator: biop,\n\t\t\t\t\tleft,\n\t\t\t\t\tright\n\t\t\t\t};\n\t\t\t\tstack.push(node);\n\t\t\t}\n\n\t\t\tnode = this.gobbleToken();\n\n\t\t\tif (!node) {\n\t\t\t\tthis.throwError(\"Expected expression after \" + cur_biop);\n\t\t\t}\n\n\t\t\tstack.push(biop_info, node);\n\t\t}\n\n\t\ti = stack.length - 1;\n\t\tnode = stack[i];\n\n\t\twhile (i > 1) {\n\t\t\tnode = {\n\t\t\t\ttype: Jsep.BINARY_EXP,\n\t\t\t\toperator: stack[i - 1].value,\n\t\t\t\tleft: stack[i - 2],\n\t\t\t\tright: node\n\t\t\t};\n\t\t\ti -= 2;\n\t\t}\n\n\t\treturn node;\n\t}\n\n\t/**\n\t * An individual part of a binary expression:\n\t * e.g. `foo.bar(baz)`, `1`, `\"abc\"`, `(a % 2)` (because it's in parenthesis)\n\t * @returns {boolean|jsep.Expression}\n\t */\n\tgobbleToken() {\n\t\tlet ch, to_check, tc_len, node;\n\n\t\tthis.gobbleSpaces();\n\t\tnode = this.searchHook('gobble-token');\n\t\tif (node) {\n\t\t\treturn this.runHook('after-token', node);\n\t\t}\n\n\t\tch = this.code;\n\n\t\tif (Jsep.isDecimalDigit(ch) || ch === Jsep.PERIOD_CODE) {\n\t\t\t// Char code 46 is a dot `.` which can start off a numeric literal\n\t\t\treturn this.gobbleNumericLiteral();\n\t\t}\n\n\t\tif (ch === Jsep.SQUOTE_CODE || ch === Jsep.DQUOTE_CODE) {\n\t\t\t// Single or double quotes\n\t\t\tnode = this.gobbleStringLiteral();\n\t\t}\n\t\telse if (ch === Jsep.OBRACK_CODE) {\n\t\t\tnode = this.gobbleArray();\n\t\t}\n\t\telse {\n\t\t\tto_check = this.expr.substr(this.index, Jsep.max_unop_len);\n\t\t\ttc_len = to_check.length;\n\n\t\t\twhile (tc_len > 0) {\n\t\t\t\t// Don't accept an unary op when it is an identifier.\n\t\t\t\t// Unary ops that start with a identifier-valid character must be followed\n\t\t\t\t// by a non identifier-part valid character\n\t\t\t\tif (Jsep.unary_ops.hasOwnProperty(to_check) && (\n\t\t\t\t\t!Jsep.isIdentifierStart(this.code) ||\n\t\t\t\t\t(this.index + to_check.length < this.expr.length && !Jsep.isIdentifierPart(this.expr.charCodeAt(this.index + to_check.length)))\n\t\t\t\t)) {\n\t\t\t\t\tthis.index += tc_len;\n\t\t\t\t\tconst argument = this.gobbleToken();\n\t\t\t\t\tif (!argument) {\n\t\t\t\t\t\tthis.throwError('missing unaryOp argument');\n\t\t\t\t\t}\n\t\t\t\t\treturn this.runHook('after-token', {\n\t\t\t\t\t\ttype: Jsep.UNARY_EXP,\n\t\t\t\t\t\toperator: to_check,\n\t\t\t\t\t\targument,\n\t\t\t\t\t\tprefix: true\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tto_check = to_check.substr(0, --tc_len);\n\t\t\t}\n\n\t\t\tif (Jsep.isIdentifierStart(ch)) {\n\t\t\t\tnode = this.gobbleIdentifier();\n\t\t\t\tif (Jsep.literals.hasOwnProperty(node.name)) {\n\t\t\t\t\tnode = {\n\t\t\t\t\t\ttype: Jsep.LITERAL,\n\t\t\t\t\t\tvalue: Jsep.literals[node.name],\n\t\t\t\t\t\traw: node.name,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\telse if (node.name === Jsep.this_str) {\n\t\t\t\t\tnode = { type: Jsep.THIS_EXP };\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (ch === Jsep.OPAREN_CODE) { // open parenthesis\n\t\t\t\tnode = this.gobbleGroup();\n\t\t\t}\n\t\t}\n\n\t\tif (!node) {\n\t\t\treturn this.runHook('after-token', false);\n\t\t}\n\n\t\tnode = this.gobbleTokenProperty(node);\n\t\treturn this.runHook('after-token', node);\n\t}\n\n\t/**\n\t * Gobble properties of of identifiers/strings/arrays/groups.\n\t * e.g. `foo`, `bar.baz`, `foo['bar'].baz`\n\t * It also gobbles function calls:\n\t * e.g. `Math.acos(obj.angle)`\n\t * @param {jsep.Expression} node\n\t * @returns {jsep.Expression}\n\t */\n\tgobbleTokenProperty(node) {\n\t\tthis.gobbleSpaces();\n\n\t\tlet ch = this.code;\n\t\twhile (ch === Jsep.PERIOD_CODE || ch === Jsep.OBRACK_CODE || ch === Jsep.OPAREN_CODE || ch === Jsep.QUMARK_CODE) {\n\t\t\tlet optional;\n\t\t\tif (ch === Jsep.QUMARK_CODE) {\n\t\t\t\tif (this.expr.charCodeAt(this.index + 1) !== Jsep.PERIOD_CODE) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\toptional = true;\n\t\t\t\tthis.index += 2;\n\t\t\t\tthis.gobbleSpaces();\n\t\t\t\tch = this.code;\n\t\t\t}\n\t\t\tthis.index++;\n\n\t\t\tif (ch === Jsep.OBRACK_CODE) {\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.MEMBER_EXP,\n\t\t\t\t\tcomputed: true,\n\t\t\t\t\tobject: node,\n\t\t\t\t\tproperty: this.gobbleExpression()\n\t\t\t\t};\n\t\t\t\tif (!node.property) {\n\t\t\t\t\tthis.throwError('Unexpected \"' + this.char + '\"');\n\t\t\t\t}\n\t\t\t\tthis.gobbleSpaces();\n\t\t\t\tch = this.code;\n\t\t\t\tif (ch !== Jsep.CBRACK_CODE) {\n\t\t\t\t\tthis.throwError('Unclosed [');\n\t\t\t\t}\n\t\t\t\tthis.index++;\n\t\t\t}\n\t\t\telse if (ch === Jsep.OPAREN_CODE) {\n\t\t\t\t// A function call is being made; gobble all the arguments\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.CALL_EXP,\n\t\t\t\t\t'arguments': this.gobbleArguments(Jsep.CPAREN_CODE),\n\t\t\t\t\tcallee: node\n\t\t\t\t};\n\t\t\t}\n\t\t\telse if (ch === Jsep.PERIOD_CODE || optional) {\n\t\t\t\tif (optional) {\n\t\t\t\t\tthis.index--;\n\t\t\t\t}\n\t\t\t\tthis.gobbleSpaces();\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.MEMBER_EXP,\n\t\t\t\t\tcomputed: false,\n\t\t\t\t\tobject: node,\n\t\t\t\t\tproperty: this.gobbleIdentifier(),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (optional) {\n\t\t\t\tnode.optional = true;\n\t\t\t} // else leave undefined for compatibility with esprima\n\n\t\t\tthis.gobbleSpaces();\n\t\t\tch = this.code;\n\t\t}\n\n\t\treturn node;\n\t}\n\n\t/**\n\t * Parse simple numeric literals: `12`, `3.4`, `.5`. Do this by using a string to\n\t * keep track of everything in the numeric literal and then calling `parseFloat` on that string\n\t * @returns {jsep.Literal}\n\t */\n\tgobbleNumericLiteral() {\n\t\tlet number = '', ch, chCode;\n\n\t\twhile (Jsep.isDecimalDigit(this.code)) {\n\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t}\n\n\t\tif (this.code === Jsep.PERIOD_CODE) { // can start with a decimal marker\n\t\t\tnumber += this.expr.charAt(this.index++);\n\n\t\t\twhile (Jsep.isDecimalDigit(this.code)) {\n\t\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\t}\n\t\t}\n\n\t\tch = this.char;\n\n\t\tif (ch === 'e' || ch === 'E') { // exponent marker\n\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\tch = this.char;\n\n\t\t\tif (ch === '+' || ch === '-') { // exponent sign\n\t\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\t}\n\n\t\t\twhile (Jsep.isDecimalDigit(this.code)) { // exponent itself\n\t\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\t}\n\n\t\t\tif (!Jsep.isDecimalDigit(this.expr.charCodeAt(this.index - 1)) ) {\n\t\t\t\tthis.throwError('Expected exponent (' + number + this.char + ')');\n\t\t\t}\n\t\t}\n\n\t\tchCode = this.code;\n\n\t\t// Check to make sure this isn't a variable name that start with a number (123abc)\n\t\tif (Jsep.isIdentifierStart(chCode)) {\n\t\t\tthis.throwError('Variable names cannot start with a number (' +\n\t\t\t\tnumber + this.char + ')');\n\t\t}\n\t\telse if (chCode === Jsep.PERIOD_CODE || (number.length === 1 && number.charCodeAt(0) === Jsep.PERIOD_CODE)) {\n\t\t\tthis.throwError('Unexpected period');\n\t\t}\n\n\t\treturn {\n\t\t\ttype: Jsep.LITERAL,\n\t\t\tvalue: parseFloat(number),\n\t\t\traw: number\n\t\t};\n\t}\n\n\t/**\n\t * Parses a string literal, staring with single or double quotes with basic support for escape codes\n\t * e.g. `\"hello world\"`, `'this is\\nJSEP'`\n\t * @returns {jsep.Literal}\n\t */\n\tgobbleStringLiteral() {\n\t\tlet str = '';\n\t\tconst startIndex = this.index;\n\t\tconst quote = this.expr.charAt(this.index++);\n\t\tlet closed = false;\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tlet ch = this.expr.charAt(this.index++);\n\n\t\t\tif (ch === quote) {\n\t\t\t\tclosed = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\telse if (ch === '\\\\') {\n\t\t\t\t// Check for all of the common escape codes\n\t\t\t\tch = this.expr.charAt(this.index++);\n\n\t\t\t\tswitch (ch) {\n\t\t\t\t\tcase 'n': str += '\\n'; break;\n\t\t\t\t\tcase 'r': str += '\\r'; break;\n\t\t\t\t\tcase 't': str += '\\t'; break;\n\t\t\t\t\tcase 'b': str += '\\b'; break;\n\t\t\t\t\tcase 'f': str += '\\f'; break;\n\t\t\t\t\tcase 'v': str += '\\x0B'; break;\n\t\t\t\t\tdefault : str += ch;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tstr += ch;\n\t\t\t}\n\t\t}\n\n\t\tif (!closed) {\n\t\t\tthis.throwError('Unclosed quote after \"' + str + '\"');\n\t\t}\n\n\t\treturn {\n\t\t\ttype: Jsep.LITERAL,\n\t\t\tvalue: str,\n\t\t\traw: this.expr.substring(startIndex, this.index),\n\t\t};\n\t}\n\n\t/**\n\t * Gobbles only identifiers\n\t * e.g.: `foo`, `_value`, `$x1`\n\t * Also, this function checks if that identifier is a literal:\n\t * (e.g. `true`, `false`, `null`) or `this`\n\t * @returns {jsep.Identifier}\n\t */\n\tgobbleIdentifier() {\n\t\tlet ch = this.code, start = this.index;\n\n\t\tif (Jsep.isIdentifierStart(ch)) {\n\t\t\tthis.index++;\n\t\t}\n\t\telse {\n\t\t\tthis.throwError('Unexpected ' + this.char);\n\t\t}\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tch = this.code;\n\n\t\t\tif (Jsep.isIdentifierPart(ch)) {\n\t\t\t\tthis.index++;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn {\n\t\t\ttype: Jsep.IDENTIFIER,\n\t\t\tname: this.expr.slice(start, this.index),\n\t\t};\n\t}\n\n\t/**\n\t * Gobbles a list of arguments within the context of a function call\n\t * or array literal. This function also assumes that the opening character\n\t * `(` or `[` has already been gobbled, and gobbles expressions and commas\n\t * until the terminator character `)` or `]` is encountered.\n\t * e.g. `foo(bar, baz)`, `my_func()`, or `[bar, baz]`\n\t * @param {number} termination\n\t * @returns {jsep.Expression[]}\n\t */\n\tgobbleArguments(termination) {\n\t\tconst args = [];\n\t\tlet closed = false;\n\t\tlet separator_count = 0;\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tthis.gobbleSpaces();\n\t\t\tlet ch_i = this.code;\n\n\t\t\tif (ch_i === termination) { // done parsing\n\t\t\t\tclosed = true;\n\t\t\t\tthis.index++;\n\n\t\t\t\tif (termination === Jsep.CPAREN_CODE && separator_count && separator_count >= args.length){\n\t\t\t\t\tthis.throwError('Unexpected token ' + String.fromCharCode(termination));\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\telse if (ch_i === Jsep.COMMA_CODE) { // between expressions\n\t\t\t\tthis.index++;\n\t\t\t\tseparator_count++;\n\n\t\t\t\tif (separator_count !== args.length) { // missing argument\n\t\t\t\t\tif (termination === Jsep.CPAREN_CODE) {\n\t\t\t\t\t\tthis.throwError('Unexpected token ,');\n\t\t\t\t\t}\n\t\t\t\t\telse if (termination === Jsep.CBRACK_CODE) {\n\t\t\t\t\t\tfor (let arg = args.length; arg < separator_count; arg++) {\n\t\t\t\t\t\t\targs.push(null);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (args.length !== separator_count && separator_count !== 0) {\n\t\t\t\t// NOTE: `&& separator_count !== 0` allows for either all commas, or all spaces as arguments\n\t\t\t\tthis.throwError('Expected comma');\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconst node = this.gobbleExpression();\n\n\t\t\t\tif (!node || node.type === Jsep.COMPOUND) {\n\t\t\t\t\tthis.throwError('Expected comma');\n\t\t\t\t}\n\n\t\t\t\targs.push(node);\n\t\t\t}\n\t\t}\n\n\t\tif (!closed) {\n\t\t\tthis.throwError('Expected ' + String.fromCharCode(termination));\n\t\t}\n\n\t\treturn args;\n\t}\n\n\t/**\n\t * Responsible for parsing a group of things within parentheses `()`\n\t * that have no identifier in front (so not a function call)\n\t * This function assumes that it needs to gobble the opening parenthesis\n\t * and then tries to gobble everything within that parenthesis, assuming\n\t * that the next thing it should see is the close parenthesis. If not,\n\t * then the expression probably doesn't have a `)`\n\t * @returns {boolean|jsep.Expression}\n\t */\n\tgobbleGroup() {\n\t\tthis.index++;\n\t\tlet nodes = this.gobbleExpressions(Jsep.CPAREN_CODE);\n\t\tif (this.code === Jsep.CPAREN_CODE) {\n\t\t\tthis.index++;\n\t\t\tif (nodes.length === 1) {\n\t\t\t\treturn nodes[0];\n\t\t\t}\n\t\t\telse if (!nodes.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn {\n\t\t\t\t\ttype: Jsep.SEQUENCE_EXP,\n\t\t\t\t\texpressions: nodes,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tthis.throwError('Unclosed (');\n\t\t}\n\t}\n\n\t/**\n\t * Responsible for parsing Array literals `[1, 2, 3]`\n\t * This function assumes that it needs to gobble the opening bracket\n\t * and then tries to gobble the expressions as arguments.\n\t * @returns {jsep.ArrayExpression}\n\t */\n\tgobbleArray() {\n\t\tthis.index++;\n\n\t\treturn {\n\t\t\ttype: Jsep.ARRAY_EXP,\n\t\t\telements: this.gobbleArguments(Jsep.CBRACK_CODE)\n\t\t};\n\t}\n}\n\n// Static fields:\nconst hooks = new Hooks();\nObject.assign(Jsep, {\n\thooks,\n\tplugins: new Plugins(Jsep),\n\n\t// Node Types\n\t// ----------\n\t// This is the full set of types that any JSEP node can be.\n\t// Store them here to save space when minified\n\tCOMPOUND:        'Compound',\n\tSEQUENCE_EXP:    'SequenceExpression',\n\tIDENTIFIER:      'Identifier',\n\tMEMBER_EXP:      'MemberExpression',\n\tLITERAL:         'Literal',\n\tTHIS_EXP:        'ThisExpression',\n\tCALL_EXP:        'CallExpression',\n\tUNARY_EXP:       'UnaryExpression',\n\tBINARY_EXP:      'BinaryExpression',\n\tARRAY_EXP:       'ArrayExpression',\n\n\tTAB_CODE:    9,\n\tLF_CODE:     10,\n\tCR_CODE:     13,\n\tSPACE_CODE:  32,\n\tPERIOD_CODE: 46, // '.'\n\tCOMMA_CODE:  44, // ','\n\tSQUOTE_CODE: 39, // single quote\n\tDQUOTE_CODE: 34, // double quotes\n\tOPAREN_CODE: 40, // (\n\tCPAREN_CODE: 41, // )\n\tOBRACK_CODE: 91, // [\n\tCBRACK_CODE: 93, // ]\n\tQUMARK_CODE: 63, // ?\n\tSEMCOL_CODE: 59, // ;\n\tCOLON_CODE:  58, // :\n\n\n\t// Operations\n\t// ----------\n\t// Use a quickly-accessible map to store all of the unary operators\n\t// Values are set to `1` (it really doesn't matter)\n\tunary_ops: {\n\t\t'-': 1,\n\t\t'!': 1,\n\t\t'~': 1,\n\t\t'+': 1\n\t},\n\n\t// Also use a map for the binary operations but set their values to their\n\t// binary precedence for quick reference (higher number = higher precedence)\n\t// see [Order of operations](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Operator_Precedence)\n\tbinary_ops: {\n\t\t'||': 1, '??': 1,\n\t\t'&&': 2, '|': 3, '^': 4, '&': 5,\n\t\t'==': 6, '!=': 6, '===': 6, '!==': 6,\n\t\t'<': 7, '>': 7, '<=': 7, '>=': 7,\n\t\t'<<': 8, '>>': 8, '>>>': 8,\n\t\t'+': 9, '-': 9,\n\t\t'*': 10, '/': 10, '%': 10,\n\t\t'**': 11,\n\t},\n\n\t// sets specific binary_ops as right-associative\n\tright_associative: new Set(['**']),\n\n\t// Additional valid identifier chars, apart from a-z, A-Z and 0-9 (except on the starting char)\n\tadditional_identifier_chars: new Set(['$', '_']),\n\n\t// Literals\n\t// ----------\n\t// Store the values to return for the various literals we may encounter\n\tliterals: {\n\t\t'true': true,\n\t\t'false': false,\n\t\t'null': null\n\t},\n\n\t// Except for `this`, which is special. This could be changed to something like `'self'` as well\n\tthis_str: 'this',\n});\nJsep.max_unop_len = Jsep.getMaxKeyLen(Jsep.unary_ops);\nJsep.max_binop_len = Jsep.getMaxKeyLen(Jsep.binary_ops);\n\n// Backward Compatibility:\nconst jsep = expr => (new Jsep(expr)).parse();\nconst stdClassProps = Object.getOwnPropertyNames(class Test{});\nObject.getOwnPropertyNames(Jsep)\n\t.filter(prop => !stdClassProps.includes(prop) && jsep[prop] === undefined)\n\t.forEach((m) => {\n\t\tjsep[m] = Jsep[m];\n\t});\njsep.Jsep = Jsep; // allows for const { Jsep } = require('jsep');\n\nconst CONDITIONAL_EXP = 'ConditionalExpression';\n\nvar ternary = {\n\tname: 'ternary',\n\n\tinit(jsep) {\n\t\t// Ternary expression: test ? consequent : alternate\n\t\tjsep.hooks.add('after-expression', function gobbleTernary(env) {\n\t\t\tif (env.node && this.code === jsep.QUMARK_CODE) {\n\t\t\t\tthis.index++;\n\t\t\t\tconst test = env.node;\n\t\t\t\tconst consequent = this.gobbleExpression();\n\n\t\t\t\tif (!consequent) {\n\t\t\t\t\tthis.throwError('Expected expression');\n\t\t\t\t}\n\n\t\t\t\tthis.gobbleSpaces();\n\n\t\t\t\tif (this.code === jsep.COLON_CODE) {\n\t\t\t\t\tthis.index++;\n\t\t\t\t\tconst alternate = this.gobbleExpression();\n\n\t\t\t\t\tif (!alternate) {\n\t\t\t\t\t\tthis.throwError('Expected expression');\n\t\t\t\t\t}\n\t\t\t\t\tenv.node = {\n\t\t\t\t\t\ttype: CONDITIONAL_EXP,\n\t\t\t\t\t\ttest,\n\t\t\t\t\t\tconsequent,\n\t\t\t\t\t\talternate,\n\t\t\t\t\t};\n\n\t\t\t\t\t// check for operators of higher priority than ternary (i.e. assignment)\n\t\t\t\t\t// jsep sets || at 1, and assignment at 0.9, and conditional should be between them\n\t\t\t\t\tif (test.operator && jsep.binary_ops[test.operator] <= 0.9) {\n\t\t\t\t\t\tlet newTest = test;\n\t\t\t\t\t\twhile (newTest.right.operator && jsep.binary_ops[newTest.right.operator] <= 0.9) {\n\t\t\t\t\t\t\tnewTest = newTest.right;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tenv.node.test = newTest.right;\n\t\t\t\t\t\tnewTest.right = env.node;\n\t\t\t\t\t\tenv.node = test;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.throwError('Expected :');\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t},\n};\n\n// Add default plugins:\n\njsep.plugins.register(ternary);\n\nexport { Jsep, jsep as default };\n", "const FSLASH_CODE = 47; // '/'\nconst BSLASH_CODE = 92; // '\\\\'\n\nvar index = {\n\tname: 'regex',\n\n\tinit(jsep) {\n\t\t// Regex literal: /abc123/ig\n\t\tjsep.hooks.add('gobble-token', function gobbleRegexLiteral(env) {\n\t\t\tif (this.code === FSLASH_CODE) {\n\t\t\t\tconst patternIndex = ++this.index;\n\n\t\t\t\tlet inCharSet = false;\n\t\t\t\twhile (this.index < this.expr.length) {\n\t\t\t\t\tif (this.code === FSLASH_CODE && !inCharSet) {\n\t\t\t\t\t\tconst pattern = this.expr.slice(patternIndex, this.index);\n\n\t\t\t\t\t\tlet flags = '';\n\t\t\t\t\t\twhile (++this.index < this.expr.length) {\n\t\t\t\t\t\t\tconst code = this.code;\n\t\t\t\t\t\t\tif ((code >= 97 && code <= 122) // a...z\n\t\t\t\t\t\t\t\t|| (code >= 65 && code <= 90) // A...Z\n\t\t\t\t\t\t\t\t|| (code >= 48 && code <= 57)) { // 0-9\n\t\t\t\t\t\t\t\tflags += this.char;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet value;\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tvalue = new RegExp(pattern, flags);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcatch (e) {\n\t\t\t\t\t\t\tthis.throwError(e.message);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tenv.node = {\n\t\t\t\t\t\t\ttype: jsep.LITERAL,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\traw: this.expr.slice(patternIndex - 1, this.index),\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// allow . [] and () after regex: /regex/.test(a)\n\t\t\t\t\t\tenv.node = this.gobbleTokenProperty(env.node);\n\t\t\t\t\t\treturn env.node;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.code === jsep.OBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = true;\n\t\t\t\t\t}\n\t\t\t\t\telse if (inCharSet && this.code === jsep.CBRACK_CODE) {\n\t\t\t\t\t\tinCharSet = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += this.code === BSLASH_CODE ? 2 : 1;\n\t\t\t\t}\n\t\t\t\tthis.throwError('Unclosed Regex');\n\t\t\t}\n\t\t});\n\t},\n};\n\nexport { index as default };\n", "const PLUS_CODE = 43; // +\nconst MINUS_CODE = 45; // -\n\nconst plugin = {\n\tname: 'assignment',\n\n\tassignmentOperators: new Set([\n\t\t'=',\n\t\t'*=',\n\t\t'**=',\n\t\t'/=',\n\t\t'%=',\n\t\t'+=',\n\t\t'-=',\n\t\t'<<=',\n\t\t'>>=',\n\t\t'>>>=',\n\t\t'&=',\n\t\t'^=',\n\t\t'|=',\n\t\t'||=',\n\t\t'&&=',\n\t\t'??=',\n\t]),\n\tupdateOperators: [PLUS_CODE, MINUS_CODE],\n\tassignmentPrecedence: 0.9,\n\n\tinit(jsep) {\n\t\tconst updateNodeTypes = [jsep.IDENTIFIER, jsep.MEMBER_EXP];\n\t\tplugin.assignmentOperators.forEach(op => jsep.addBinaryOp(op, plugin.assignmentPrecedence, true));\n\n\t\tjsep.hooks.add('gobble-token', function gobbleUpdatePrefix(env) {\n\t\t\tconst code = this.code;\n\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\tthis.index += 2;\n\t\t\t\tenv.node = {\n\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\targument: this.gobbleTokenProperty(this.gobbleIdentifier()),\n\t\t\t\t\tprefix: true,\n\t\t\t\t};\n\t\t\t\tif (!env.node.argument || !updateNodeTypes.includes(env.node.argument.type)) {\n\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-token', function gobbleUpdatePostfix(env) {\n\t\t\tif (env.node) {\n\t\t\t\tconst code = this.code;\n\t\t\t\tif (plugin.updateOperators.some(c => c === code && c === this.expr.charCodeAt(this.index + 1))) {\n\t\t\t\t\tif (!updateNodeTypes.includes(env.node.type)) {\n\t\t\t\t\t\tthis.throwError(`Unexpected ${env.node.operator}`);\n\t\t\t\t\t}\n\t\t\t\t\tthis.index += 2;\n\t\t\t\t\tenv.node = {\n\t\t\t\t\t\ttype: 'UpdateExpression',\n\t\t\t\t\t\toperator: code === PLUS_CODE ? '++' : '--',\n\t\t\t\t\t\targument: env.node,\n\t\t\t\t\t\tprefix: false,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tjsep.hooks.add('after-expression', function gobbleAssignment(env) {\n\t\t\tif (env.node) {\n\t\t\t\t// Note: Binaries can be chained in a single expression to respect\n\t\t\t\t// operator precedence (i.e. a = b = 1 + 2 + 3)\n\t\t\t\t// Update all binary assignment nodes in the tree\n\t\t\t\tupdateBinariesToAssignments(env.node);\n\t\t\t}\n\t\t});\n\n\t\tfunction updateBinariesToAssignments(node) {\n\t\t\tif (plugin.assignmentOperators.has(node.operator)) {\n\t\t\t\tnode.type = 'AssignmentExpression';\n\t\t\t\tupdateBinariesToAssignments(node.left);\n\t\t\t\tupdateBinariesToAssignments(node.right);\n\t\t\t}\n\t\t\telse if (!node.operator) {\n\t\t\t\tObject.values(node).forEach((val) => {\n\t\t\t\t\tif (val && typeof val === 'object') {\n\t\t\t\t\t\tupdateBinariesToAssignments(val);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t},\n};\n\nexport { plugin as default };\n", "/* eslint-disable no-bitwise -- Convenient */\nimport jsep from 'jsep';\nimport jsepRegex from '@jsep-plugin/regex';\nimport jsepAssignment from '@jsep-plugin/assignment';\n\n// register plugins\njsep.plugins.register(jsepRegex, jsepAssignment);\njsep.addUnaryOp('typeof');\njsep.addLiteral('null', null);\njsep.addLiteral('undefined', undefined);\n\nconst BLOCKED_PROTO_PROPERTIES = new Set([\n    'constructor',\n    '__proto__',\n    '__defineGetter__',\n    '__defineSetter__'\n]);\n\nconst SafeEval = {\n    /**\n     * @param {jsep.Expression} ast\n     * @param {Record<string, any>} subs\n     */\n    evalAst (ast, subs) {\n        switch (ast.type) {\n        case 'BinaryExpression':\n        case 'LogicalExpression':\n            return SafeEval.evalBinaryExpression(ast, subs);\n        case 'Compound':\n            return SafeEval.evalCompound(ast, subs);\n        case 'ConditionalExpression':\n            return SafeEval.evalConditionalExpression(ast, subs);\n        case 'Identifier':\n            return SafeEval.evalIdentifier(ast, subs);\n        case 'Literal':\n            return SafeEval.evalLiteral(ast, subs);\n        case 'MemberExpression':\n            return SafeEval.evalMemberExpression(ast, subs);\n        case 'UnaryExpression':\n            return SafeEval.evalUnaryExpression(ast, subs);\n        case 'ArrayExpression':\n            return SafeEval.evalArrayExpression(ast, subs);\n        case 'CallExpression':\n            return SafeEval.evalCallExpression(ast, subs);\n        case 'AssignmentExpression':\n            return SafeEval.evalAssignmentExpression(ast, subs);\n        default:\n            throw SyntaxError('Unexpected expression', ast);\n        }\n    },\n    evalBinaryExpression (ast, subs) {\n        const result = {\n            '||': (a, b) => a || b(),\n            '&&': (a, b) => a && b(),\n            '|': (a, b) => a | b(),\n            '^': (a, b) => a ^ b(),\n            '&': (a, b) => a & b(),\n            // eslint-disable-next-line eqeqeq -- API\n            '==': (a, b) => a == b(),\n            // eslint-disable-next-line eqeqeq -- API\n            '!=': (a, b) => a != b(),\n            '===': (a, b) => a === b(),\n            '!==': (a, b) => a !== b(),\n            '<': (a, b) => a < b(),\n            '>': (a, b) => a > b(),\n            '<=': (a, b) => a <= b(),\n            '>=': (a, b) => a >= b(),\n            '<<': (a, b) => a << b(),\n            '>>': (a, b) => a >> b(),\n            '>>>': (a, b) => a >>> b(),\n            '+': (a, b) => a + b(),\n            '-': (a, b) => a - b(),\n            '*': (a, b) => a * b(),\n            '/': (a, b) => a / b(),\n            '%': (a, b) => a % b()\n        }[ast.operator](\n            SafeEval.evalAst(ast.left, subs),\n            () => SafeEval.evalAst(ast.right, subs)\n        );\n        return result;\n    },\n    evalCompound (ast, subs) {\n        let last;\n        for (let i = 0; i < ast.body.length; i++) {\n            if (\n                ast.body[i].type === 'Identifier' &&\n                ['var', 'let', 'const'].includes(ast.body[i].name) &&\n                ast.body[i + 1] &&\n                ast.body[i + 1].type === 'AssignmentExpression'\n            ) {\n                // var x=2; is detected as\n                // [{Identifier var}, {AssignmentExpression x=2}]\n                // eslint-disable-next-line @stylistic/max-len -- Long\n                // eslint-disable-next-line sonarjs/updated-loop-counter -- Convenient\n                i += 1;\n            }\n            const expr = ast.body[i];\n            last = SafeEval.evalAst(expr, subs);\n        }\n        return last;\n    },\n    evalConditionalExpression (ast, subs) {\n        if (SafeEval.evalAst(ast.test, subs)) {\n            return SafeEval.evalAst(ast.consequent, subs);\n        }\n        return SafeEval.evalAst(ast.alternate, subs);\n    },\n    evalIdentifier (ast, subs) {\n        if (Object.hasOwn(subs, ast.name)) {\n            return subs[ast.name];\n        }\n        throw ReferenceError(`${ast.name} is not defined`);\n    },\n    evalLiteral (ast) {\n        return ast.value;\n    },\n    evalMemberExpression (ast, subs) {\n        const prop = String(\n            // NOTE: `String(value)` throws error when\n            // value has overwritten the toString method to return non-string\n            // i.e. `value = {toString: () => []}`\n            ast.computed\n                ? SafeEval.evalAst(ast.property) // `object[property]`\n                : ast.property.name // `object.property` property is Identifier\n        );\n        const obj = SafeEval.evalAst(ast.object, subs);\n        if (obj === undefined || obj === null) {\n            throw TypeError(\n                `Cannot read properties of ${obj} (reading '${prop}')`\n            );\n        }\n        if (!Object.hasOwn(obj, prop) && BLOCKED_PROTO_PROPERTIES.has(prop)) {\n            throw TypeError(\n                `Cannot read properties of ${obj} (reading '${prop}')`\n            );\n        }\n        const result = obj[prop];\n        if (typeof result === 'function') {\n            return result.bind(obj); // arrow functions aren't affected by bind.\n        }\n        return result;\n    },\n    evalUnaryExpression (ast, subs) {\n        const result = {\n            '-': (a) => -SafeEval.evalAst(a, subs),\n            '!': (a) => !SafeEval.evalAst(a, subs),\n            '~': (a) => ~SafeEval.evalAst(a, subs),\n            // eslint-disable-next-line no-implicit-coercion -- API\n            '+': (a) => +SafeEval.evalAst(a, subs),\n            typeof: (a) => typeof SafeEval.evalAst(a, subs)\n        }[ast.operator](ast.argument);\n        return result;\n    },\n    evalArrayExpression (ast, subs) {\n        return ast.elements.map((el) => SafeEval.evalAst(el, subs));\n    },\n    evalCallExpression (ast, subs) {\n        const args = ast.arguments.map((arg) => SafeEval.evalAst(arg, subs));\n        const func = SafeEval.evalAst(ast.callee, subs);\n        // if (func === Function) {\n        //     throw new Error('Function constructor is disabled');\n        // }\n        return func(...args);\n    },\n    evalAssignmentExpression (ast, subs) {\n        if (ast.left.type !== 'Identifier') {\n            throw SyntaxError('Invalid left-hand side in assignment');\n        }\n        const id = ast.left.name;\n        const value = SafeEval.evalAst(ast.right, subs);\n        subs[id] = value;\n        return subs[id];\n    }\n};\n\n/**\n * A replacement for NodeJS' VM.Script which is also {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP | Content Security Policy} friendly.\n */\nclass SafeScript {\n    /**\n     * @param {string} expr Expression to evaluate\n     */\n    constructor (expr) {\n        this.code = expr;\n        this.ast = jsep(this.code);\n    }\n\n    /**\n     * @param {object} context Object whose items will be added\n     *   to evaluation\n     * @returns {EvaluatedResult} Result of evaluated code\n     */\n    runInNewContext (context) {\n        // `Object.create(null)` creates a prototypeless object\n        const keyMap = Object.assign(Object.create(null), context);\n        return SafeEval.evalAst(this.ast, keyMap);\n    }\n}\n\nexport {SafeScript};\n", "/* eslint-disable camelcase -- Convenient for escaping */\n\nimport {SafeScript} from './Safe-Script.js';\n\n/**\n * @typedef {null|boolean|number|string|object|GenericArray} JSONObject\n */\n\n/**\n * @typedef {any} AnyItem\n */\n\n/**\n * @typedef {any} AnyResult\n */\n\n/**\n * Copies array and then pushes item into it.\n * @param {GenericArray} arr Array to copy and into which to push\n * @param {AnyItem} item Array item to add (to end)\n * @returns {GenericArray} Copy of the original array\n */\nfunction push (arr, item) {\n    arr = arr.slice();\n    arr.push(item);\n    return arr;\n}\n/**\n * Copies array and then unshifts item into it.\n * @param {AnyItem} item Array item to add (to beginning)\n * @param {GenericArray} arr Array to copy and into which to unshift\n * @returns {GenericArray} Copy of the original array\n */\nfunction unshift (item, arr) {\n    arr = arr.slice();\n    arr.unshift(item);\n    return arr;\n}\n\n/**\n * Caught when JSONPath is used without `new` but rethrown if with `new`\n * @extends Error\n */\nclass NewError extends Error {\n    /**\n     * @param {AnyResult} value The evaluated scalar value\n     */\n    constructor (value) {\n        super(\n            'JSONPath should not be called with \"new\" (it prevents return ' +\n            'of (unwrapped) scalar values)'\n        );\n        this.avoidNew = true;\n        this.value = value;\n        this.name = 'NewError';\n    }\n}\n\n/**\n* @typedef {object} ReturnObject\n* @property {string} path\n* @property {JSONObject} value\n* @property {object|GenericArray} parent\n* @property {string} parentProperty\n*/\n\n/**\n* @callback JSONPathCallback\n* @param {string|object} preferredOutput\n* @param {\"value\"|\"property\"} type\n* @param {ReturnObject} fullRetObj\n* @returns {void}\n*/\n\n/**\n* @callback OtherTypeCallback\n* @param {JSONObject} val\n* @param {string} path\n* @param {object|GenericArray} parent\n* @param {string} parentPropName\n* @returns {boolean}\n*/\n\n/**\n * @typedef {any} ContextItem\n */\n\n/**\n * @typedef {any} EvaluatedResult\n */\n\n/**\n* @callback EvalCallback\n* @param {string} code\n* @param {ContextItem} context\n* @returns {EvaluatedResult}\n*/\n\n/**\n * @typedef {typeof SafeScript} EvalClass\n */\n\n/**\n * @typedef {object} JSONPathOptions\n * @property {JSON} json\n * @property {string|string[]} path\n * @property {\"value\"|\"path\"|\"pointer\"|\"parent\"|\"parentProperty\"|\n *   \"all\"} [resultType=\"value\"]\n * @property {boolean} [flatten=false]\n * @property {boolean} [wrap=true]\n * @property {object} [sandbox={}]\n * @property {EvalCallback|EvalClass|'safe'|'native'|\n *   boolean} [eval = 'safe']\n * @property {object|GenericArray|null} [parent=null]\n * @property {string|null} [parentProperty=null]\n * @property {JSONPathCallback} [callback]\n * @property {OtherTypeCallback} [otherTypeCallback] Defaults to\n *   function which throws on encountering `@other`\n * @property {boolean} [autostart=true]\n */\n\n/**\n * @param {string|JSONPathOptions} opts If a string, will be treated as `expr`\n * @param {string} [expr] JSON path to evaluate\n * @param {JSON} [obj] JSON object to evaluate against\n * @param {JSONPathCallback} [callback] Passed 3 arguments: 1) desired payload\n *     per `resultType`, 2) `\"value\"|\"property\"`, 3) Full returned object with\n *     all payloads\n * @param {OtherTypeCallback} [otherTypeCallback] If `@other()` is at the end\n *   of one's query, this will be invoked with the value of the item, its\n *   path, its parent, and its parent's property name, and it should return\n *   a boolean indicating whether the supplied value belongs to the \"other\"\n *   type or not (or it may handle transformations and return `false`).\n * @returns {JSONPath}\n * @class\n */\nfunction JSONPath (opts, expr, obj, callback, otherTypeCallback) {\n    // eslint-disable-next-line no-restricted-syntax -- Allow for pseudo-class\n    if (!(this instanceof JSONPath)) {\n        try {\n            return new JSONPath(opts, expr, obj, callback, otherTypeCallback);\n        } catch (e) {\n            if (!e.avoidNew) {\n                throw e;\n            }\n            return e.value;\n        }\n    }\n\n    if (typeof opts === 'string') {\n        otherTypeCallback = callback;\n        callback = obj;\n        obj = expr;\n        expr = opts;\n        opts = null;\n    }\n    const optObj = opts && typeof opts === 'object';\n    opts = opts || {};\n    this.json = opts.json || obj;\n    this.path = opts.path || expr;\n    this.resultType = opts.resultType || 'value';\n    this.flatten = opts.flatten || false;\n    this.wrap = Object.hasOwn(opts, 'wrap') ? opts.wrap : true;\n    this.sandbox = opts.sandbox || {};\n    this.eval = opts.eval === undefined ? 'safe' : opts.eval;\n    this.ignoreEvalErrors = (typeof opts.ignoreEvalErrors === 'undefined')\n        ? false\n        : opts.ignoreEvalErrors;\n    this.parent = opts.parent || null;\n    this.parentProperty = opts.parentProperty || null;\n    this.callback = opts.callback || callback || null;\n    this.otherTypeCallback = opts.otherTypeCallback ||\n        otherTypeCallback ||\n        function () {\n            throw new TypeError(\n                'You must supply an otherTypeCallback callback option ' +\n                'with the @other() operator.'\n            );\n        };\n\n    if (opts.autostart !== false) {\n        const args = {\n            path: (optObj ? opts.path : expr)\n        };\n        if (!optObj) {\n            args.json = obj;\n        } else if ('json' in opts) {\n            args.json = opts.json;\n        }\n        const ret = this.evaluate(args);\n        if (!ret || typeof ret !== 'object') {\n            throw new NewError(ret);\n        }\n        return ret;\n    }\n}\n\n// PUBLIC METHODS\nJSONPath.prototype.evaluate = function (\n    expr, json, callback, otherTypeCallback\n) {\n    let currParent = this.parent,\n        currParentProperty = this.parentProperty;\n    let {flatten, wrap} = this;\n\n    this.currResultType = this.resultType;\n    this.currEval = this.eval;\n    this.currSandbox = this.sandbox;\n    callback = callback || this.callback;\n    this.currOtherTypeCallback = otherTypeCallback || this.otherTypeCallback;\n\n    json = json || this.json;\n    expr = expr || this.path;\n    if (expr && typeof expr === 'object' && !Array.isArray(expr)) {\n        if (!expr.path && expr.path !== '') {\n            throw new TypeError(\n                'You must supply a \"path\" property when providing an object ' +\n                'argument to JSONPath.evaluate().'\n            );\n        }\n        if (!(Object.hasOwn(expr, 'json'))) {\n            throw new TypeError(\n                'You must supply a \"json\" property when providing an object ' +\n                'argument to JSONPath.evaluate().'\n            );\n        }\n        ({json} = expr);\n        flatten = Object.hasOwn(expr, 'flatten') ? expr.flatten : flatten;\n        this.currResultType = Object.hasOwn(expr, 'resultType')\n            ? expr.resultType\n            : this.currResultType;\n        this.currSandbox = Object.hasOwn(expr, 'sandbox')\n            ? expr.sandbox\n            : this.currSandbox;\n        wrap = Object.hasOwn(expr, 'wrap') ? expr.wrap : wrap;\n        this.currEval = Object.hasOwn(expr, 'eval')\n            ? expr.eval\n            : this.currEval;\n        callback = Object.hasOwn(expr, 'callback') ? expr.callback : callback;\n        this.currOtherTypeCallback = Object.hasOwn(expr, 'otherTypeCallback')\n            ? expr.otherTypeCallback\n            : this.currOtherTypeCallback;\n        currParent = Object.hasOwn(expr, 'parent') ? expr.parent : currParent;\n        currParentProperty = Object.hasOwn(expr, 'parentProperty')\n            ? expr.parentProperty\n            : currParentProperty;\n        expr = expr.path;\n    }\n    currParent = currParent || null;\n    currParentProperty = currParentProperty || null;\n\n    if (Array.isArray(expr)) {\n        expr = JSONPath.toPathString(expr);\n    }\n    if ((!expr && expr !== '') || !json) {\n        return undefined;\n    }\n\n    const exprList = JSONPath.toPathArray(expr);\n    if (exprList[0] === '$' && exprList.length > 1) {\n        exprList.shift();\n    }\n    this._hasParentSelector = null;\n    const result = this\n        ._trace(\n            exprList, json, ['$'], currParent, currParentProperty, callback\n        )\n        .filter(function (ea) {\n            return ea && !ea.isParentSelector;\n        });\n\n    if (!result.length) {\n        return wrap ? [] : undefined;\n    }\n    if (!wrap && result.length === 1 && !result[0].hasArrExpr) {\n        return this._getPreferredOutput(result[0]);\n    }\n    return result.reduce((rslt, ea) => {\n        const valOrPath = this._getPreferredOutput(ea);\n        if (flatten && Array.isArray(valOrPath)) {\n            rslt = rslt.concat(valOrPath);\n        } else {\n            rslt.push(valOrPath);\n        }\n        return rslt;\n    }, []);\n};\n\n// PRIVATE METHODS\n\nJSONPath.prototype._getPreferredOutput = function (ea) {\n    const resultType = this.currResultType;\n    switch (resultType) {\n    case 'all': {\n        const path = Array.isArray(ea.path)\n            ? ea.path\n            : JSONPath.toPathArray(ea.path);\n        ea.pointer = JSONPath.toPointer(path);\n        ea.path = typeof ea.path === 'string'\n            ? ea.path\n            : JSONPath.toPathString(ea.path);\n        return ea;\n    } case 'value': case 'parent': case 'parentProperty':\n        return ea[resultType];\n    case 'path':\n        return JSONPath.toPathString(ea[resultType]);\n    case 'pointer':\n        return JSONPath.toPointer(ea.path);\n    default:\n        throw new TypeError('Unknown result type');\n    }\n};\n\nJSONPath.prototype._handleCallback = function (fullRetObj, callback, type) {\n    if (callback) {\n        const preferredOutput = this._getPreferredOutput(fullRetObj);\n        fullRetObj.path = typeof fullRetObj.path === 'string'\n            ? fullRetObj.path\n            : JSONPath.toPathString(fullRetObj.path);\n        // eslint-disable-next-line n/callback-return -- No need to return\n        callback(preferredOutput, type, fullRetObj);\n    }\n};\n\n/**\n *\n * @param {string} expr\n * @param {JSONObject} val\n * @param {string} path\n * @param {object|GenericArray} parent\n * @param {string} parentPropName\n * @param {JSONPathCallback} callback\n * @param {boolean} hasArrExpr\n * @param {boolean} literalPriority\n * @returns {ReturnObject|ReturnObject[]}\n */\nJSONPath.prototype._trace = function (\n    expr, val, path, parent, parentPropName, callback, hasArrExpr,\n    literalPriority\n) {\n    // No expr to follow? return path and value as the result of\n    //  this trace branch\n    let retObj;\n    if (!expr.length) {\n        retObj = {\n            path,\n            value: val,\n            parent,\n            parentProperty: parentPropName,\n            hasArrExpr\n        };\n        this._handleCallback(retObj, callback, 'value');\n        return retObj;\n    }\n\n    const loc = expr[0], x = expr.slice(1);\n\n    // We need to gather the return value of recursive trace calls in order to\n    // do the parent sel computation.\n    const ret = [];\n    /**\n     *\n     * @param {ReturnObject|ReturnObject[]} elems\n     * @returns {void}\n     */\n    function addRet (elems) {\n        if (Array.isArray(elems)) {\n            // This was causing excessive stack size in Node (with or\n            //  without Babel) against our performance test:\n            //  `ret.push(...elems);`\n            elems.forEach((t) => {\n                ret.push(t);\n            });\n        } else {\n            ret.push(elems);\n        }\n    }\n    if ((typeof loc !== 'string' || literalPriority) && val &&\n        Object.hasOwn(val, loc)\n    ) { // simple case--directly follow property\n        addRet(this._trace(x, val[loc], push(path, loc), val, loc, callback,\n            hasArrExpr));\n    // eslint-disable-next-line unicorn/prefer-switch -- Part of larger `if`\n    } else if (loc === '*') { // all child properties\n        this._walk(val, (m) => {\n            addRet(this._trace(\n                x, val[m], push(path, m), val, m, callback, true, true\n            ));\n        });\n    } else if (loc === '..') { // all descendent parent properties\n        // Check remaining expression with val's immediate children\n        addRet(\n            this._trace(x, val, path, parent, parentPropName, callback,\n                hasArrExpr)\n        );\n        this._walk(val, (m) => {\n            // We don't join m and x here because we only want parents,\n            //   not scalar values\n            if (typeof val[m] === 'object') {\n                // Keep going with recursive descent on val's\n                //   object children\n                addRet(this._trace(\n                    expr.slice(), val[m], push(path, m), val, m, callback, true\n                ));\n            }\n        });\n    // The parent sel computation is handled in the frame above using the\n    // ancestor object of val\n    } else if (loc === '^') {\n        // This is not a final endpoint, so we do not invoke the callback here\n        this._hasParentSelector = true;\n        return {\n            path: path.slice(0, -1),\n            expr: x,\n            isParentSelector: true\n        };\n    } else if (loc === '~') { // property name\n        retObj = {\n            path: push(path, loc),\n            value: parentPropName,\n            parent,\n            parentProperty: null\n        };\n        this._handleCallback(retObj, callback, 'property');\n        return retObj;\n    } else if (loc === '$') { // root only\n        addRet(this._trace(x, val, path, null, null, callback, hasArrExpr));\n    } else if ((/^(-?\\d*):(-?\\d*):?(\\d*)$/u).test(loc)) { // [start:end:step]  Python slice syntax\n        addRet(\n            this._slice(loc, x, val, path, parent, parentPropName, callback)\n        );\n    } else if (loc.indexOf('?(') === 0) { // [?(expr)] (filtering)\n        if (this.currEval === false) {\n            throw new Error('Eval [?(expr)] prevented in JSONPath expression.');\n        }\n        const safeLoc = loc.replace(/^\\?\\((.*?)\\)$/u, '$1');\n        // check for a nested filter expression\n        const nested = (/@.?([^?]*)[['](\\??\\(.*?\\))(?!.\\)\\])[\\]']/gu).exec(safeLoc);\n        if (nested) {\n            // find if there are matches in the nested expression\n            // add them to the result set if there is at least one match\n            this._walk(val, (m) => {\n                const npath = [nested[2]];\n                const nvalue = nested[1]\n                    ? val[m][nested[1]]\n                    : val[m];\n                const filterResults = this._trace(npath, nvalue, path,\n                    parent, parentPropName, callback, true);\n                if (filterResults.length > 0) {\n                    addRet(this._trace(x, val[m], push(path, m), val,\n                        m, callback, true));\n                }\n            });\n        } else {\n            this._walk(val, (m) => {\n                if (this._eval(safeLoc, val[m], m, path, parent,\n                    parentPropName)) {\n                    addRet(this._trace(x, val[m], push(path, m), val, m,\n                        callback, true));\n                }\n            });\n        }\n    } else if (loc[0] === '(') { // [(expr)] (dynamic property/index)\n        if (this.currEval === false) {\n            throw new Error('Eval [(expr)] prevented in JSONPath expression.');\n        }\n        // As this will resolve to a property name (but we don't know it\n        //  yet), property and parent information is relative to the\n        //  parent of the property to which this expression will resolve\n        addRet(this._trace(unshift(\n            this._eval(\n                loc, val, path.at(-1),\n                path.slice(0, -1), parent, parentPropName\n            ),\n            x\n        ), val, path, parent, parentPropName, callback, hasArrExpr));\n    } else if (loc[0] === '@') { // value type: @boolean(), etc.\n        let addType = false;\n        const valueType = loc.slice(1, -2);\n        switch (valueType) {\n        case 'scalar':\n            if (!val || !(['object', 'function'].includes(typeof val))) {\n                addType = true;\n            }\n            break;\n        case 'boolean': case 'string': case 'undefined': case 'function':\n            if (typeof val === valueType) {\n                addType = true;\n            }\n            break;\n        case 'integer':\n            if (Number.isFinite(val) && !(val % 1)) {\n                addType = true;\n            }\n            break;\n        case 'number':\n            if (Number.isFinite(val)) {\n                addType = true;\n            }\n            break;\n        case 'nonFinite':\n            if (typeof val === 'number' && !Number.isFinite(val)) {\n                addType = true;\n            }\n            break;\n        case 'object':\n            if (val && typeof val === valueType) {\n                addType = true;\n            }\n            break;\n        case 'array':\n            if (Array.isArray(val)) {\n                addType = true;\n            }\n            break;\n        case 'other':\n            addType = this.currOtherTypeCallback(\n                val, path, parent, parentPropName\n            );\n            break;\n        case 'null':\n            if (val === null) {\n                addType = true;\n            }\n            break;\n        /* c8 ignore next 2 */\n        default:\n            throw new TypeError('Unknown value type ' + valueType);\n        }\n        if (addType) {\n            retObj = {path, value: val, parent, parentProperty: parentPropName};\n            this._handleCallback(retObj, callback, 'value');\n            return retObj;\n        }\n    // `-escaped property\n    } else if (loc[0] === '`' && val && Object.hasOwn(val, loc.slice(1))) {\n        const locProp = loc.slice(1);\n        addRet(this._trace(\n            x, val[locProp], push(path, locProp), val, locProp, callback,\n            hasArrExpr, true\n        ));\n    } else if (loc.includes(',')) { // [name1,name2,...]\n        const parts = loc.split(',');\n        for (const part of parts) {\n            addRet(this._trace(\n                unshift(part, x), val, path, parent, parentPropName, callback,\n                true\n            ));\n        }\n    // simple case--directly follow property\n    } else if (\n        !literalPriority && val && Object.hasOwn(val, loc)\n    ) {\n        addRet(\n            this._trace(x, val[loc], push(path, loc), val, loc, callback,\n                hasArrExpr, true)\n        );\n    }\n\n    // We check the resulting values for parent selections. For parent\n    // selections we discard the value object and continue the trace with the\n    // current val object\n    if (this._hasParentSelector) {\n        for (let t = 0; t < ret.length; t++) {\n            const rett = ret[t];\n            if (rett && rett.isParentSelector) {\n                const tmp = this._trace(\n                    rett.expr, val, rett.path, parent, parentPropName, callback,\n                    hasArrExpr\n                );\n                if (Array.isArray(tmp)) {\n                    ret[t] = tmp[0];\n                    const tl = tmp.length;\n                    for (let tt = 1; tt < tl; tt++) {\n                        // eslint-disable-next-line @stylistic/max-len -- Long\n                        // eslint-disable-next-line sonarjs/updated-loop-counter -- Convenient\n                        t++;\n                        ret.splice(t, 0, tmp[tt]);\n                    }\n                } else {\n                    ret[t] = tmp;\n                }\n            }\n        }\n    }\n    return ret;\n};\n\nJSONPath.prototype._walk = function (val, f) {\n    if (Array.isArray(val)) {\n        const n = val.length;\n        for (let i = 0; i < n; i++) {\n            f(i);\n        }\n    } else if (val && typeof val === 'object') {\n        Object.keys(val).forEach((m) => {\n            f(m);\n        });\n    }\n};\n\nJSONPath.prototype._slice = function (\n    loc, expr, val, path, parent, parentPropName, callback\n) {\n    if (!Array.isArray(val)) {\n        return undefined;\n    }\n    const len = val.length, parts = loc.split(':'),\n        step = (parts[2] && Number.parseInt(parts[2])) || 1;\n    let start = (parts[0] && Number.parseInt(parts[0])) || 0,\n        end = (parts[1] && Number.parseInt(parts[1])) || len;\n    start = (start < 0) ? Math.max(0, start + len) : Math.min(len, start);\n    end = (end < 0) ? Math.max(0, end + len) : Math.min(len, end);\n    const ret = [];\n    for (let i = start; i < end; i += step) {\n        const tmp = this._trace(\n            unshift(i, expr), val, path, parent, parentPropName, callback, true\n        );\n        // Should only be possible to be an array here since first part of\n        //   ``unshift(i, expr)` passed in above would not be empty, nor `~`,\n        //     nor begin with `@` (as could return objects)\n        // This was causing excessive stack size in Node (with or\n        //  without Babel) against our performance test: `ret.push(...tmp);`\n        tmp.forEach((t) => {\n            ret.push(t);\n        });\n    }\n    return ret;\n};\n\nJSONPath.prototype._eval = function (\n    code, _v, _vname, path, parent, parentPropName\n) {\n    this.currSandbox._$_parentProperty = parentPropName;\n    this.currSandbox._$_parent = parent;\n    this.currSandbox._$_property = _vname;\n    this.currSandbox._$_root = this.json;\n    this.currSandbox._$_v = _v;\n\n    const containsPath = code.includes('@path');\n    if (containsPath) {\n        this.currSandbox._$_path = JSONPath.toPathString(path.concat([_vname]));\n    }\n\n    const scriptCacheKey = this.currEval + 'Script:' + code;\n    if (!JSONPath.cache[scriptCacheKey]) {\n        let script = code\n            .replaceAll('@parentProperty', '_$_parentProperty')\n            .replaceAll('@parent', '_$_parent')\n            .replaceAll('@property', '_$_property')\n            .replaceAll('@root', '_$_root')\n            .replaceAll(/@([.\\s)[])/gu, '_$_v$1');\n        if (containsPath) {\n            script = script.replaceAll('@path', '_$_path');\n        }\n        if (\n            this.currEval === 'safe' ||\n            this.currEval === true ||\n            this.currEval === undefined\n        ) {\n            JSONPath.cache[scriptCacheKey] = new this.safeVm.Script(script);\n        } else if (this.currEval === 'native') {\n            JSONPath.cache[scriptCacheKey] = new this.vm.Script(script);\n        } else if (\n            typeof this.currEval === 'function' &&\n            this.currEval.prototype &&\n            Object.hasOwn(this.currEval.prototype, 'runInNewContext')\n        ) {\n            const CurrEval = this.currEval;\n            JSONPath.cache[scriptCacheKey] = new CurrEval(script);\n        } else if (typeof this.currEval === 'function') {\n            JSONPath.cache[scriptCacheKey] = {\n                runInNewContext: (context) => this.currEval(script, context)\n            };\n        } else {\n            throw new TypeError(`Unknown \"eval\" property \"${this.currEval}\"`);\n        }\n    }\n\n    try {\n        return JSONPath.cache[scriptCacheKey].runInNewContext(this.currSandbox);\n    } catch (e) {\n        if (this.ignoreEvalErrors) {\n            return false;\n        }\n        throw new Error('jsonPath: ' + e.message + ': ' + code);\n    }\n};\n\n// PUBLIC CLASS PROPERTIES AND METHODS\n\n// Could store the cache object itself\nJSONPath.cache = {};\n\n/**\n * @param {string[]} pathArr Array to convert\n * @returns {string} The path string\n */\nJSONPath.toPathString = function (pathArr) {\n    const x = pathArr, n = x.length;\n    let p = '$';\n    for (let i = 1; i < n; i++) {\n        if (!(/^(~|\\^|@.*?\\(\\))$/u).test(x[i])) {\n            p += (/^[0-9*]+$/u).test(x[i]) ? ('[' + x[i] + ']') : (\"['\" + x[i] + \"']\");\n        }\n    }\n    return p;\n};\n\n/**\n * @param {string} pointer JSON Path\n * @returns {string} JSON Pointer\n */\nJSONPath.toPointer = function (pointer) {\n    const x = pointer, n = x.length;\n    let p = '';\n    for (let i = 1; i < n; i++) {\n        if (!(/^(~|\\^|@.*?\\(\\))$/u).test(x[i])) {\n            p += '/' + x[i].toString()\n                .replaceAll('~', '~0')\n                .replaceAll('/', '~1');\n        }\n    }\n    return p;\n};\n\n/**\n * @param {string} expr Expression to convert\n * @returns {string[]}\n */\nJSONPath.toPathArray = function (expr) {\n    const {cache} = JSONPath;\n    if (cache[expr]) {\n        return cache[expr].concat();\n    }\n    const subx = [];\n    const normalized = expr\n        // Properties\n        .replaceAll(\n            /@(?:null|boolean|number|string|integer|undefined|nonFinite|scalar|array|object|function|other)\\(\\)/gu,\n            ';$&;'\n        )\n        // Parenthetical evaluations (filtering and otherwise), directly\n        //   within brackets or single quotes\n        .replaceAll(/[['](\\??\\(.*?\\))[\\]'](?!.\\])/gu, function ($0, $1) {\n            return '[#' + (subx.push($1) - 1) + ']';\n        })\n        // Escape periods and tildes within properties\n        .replaceAll(/\\[['\"]([^'\\]]*)['\"]\\]/gu, function ($0, prop) {\n            return \"['\" + prop\n                .replaceAll('.', '%@%')\n                .replaceAll('~', '%%@@%%') +\n                \"']\";\n        })\n        // Properties operator\n        .replaceAll('~', ';~;')\n        // Split by property boundaries\n        .replaceAll(/['\"]?\\.['\"]?(?![^[]*\\])|\\[['\"]?/gu, ';')\n        // Reinsert periods within properties\n        .replaceAll('%@%', '.')\n        // Reinsert tildes within properties\n        .replaceAll('%%@@%%', '~')\n        // Parent\n        .replaceAll(/(?:;)?(\\^+)(?:;)?/gu, function ($0, ups) {\n            return ';' + ups.split('').join(';') + ';';\n        })\n        // Descendents\n        .replaceAll(/;;;|;;/gu, ';..;')\n        // Remove trailing\n        .replaceAll(/;$|'?\\]|'$/gu, '');\n\n    const exprList = normalized.split(';').map(function (exp) {\n        const match = exp.match(/#(\\d+)/u);\n        return !match || !match[1] ? exp : subx[match[1]];\n    });\n    cache[expr] = exprList;\n    return cache[expr].concat();\n};\n\nJSONPath.prototype.safeVm = {\n    Script: SafeScript\n};\n\nexport {JSONPath};\n", "import {JSONPath} from './jsonpath.js';\n\n/**\n * @typedef {any} ContextItem\n */\n\n/**\n * @typedef {any} EvaluatedResult\n */\n\n/**\n * @callback ConditionCallback\n * @param {ContextItem} item\n * @returns {boolean}\n */\n\n/**\n * Copy items out of one array into another.\n * @param {GenericArray} source Array with items to copy\n * @param {GenericArray} target Array to which to copy\n * @param {ConditionCallback} conditionCb Callback passed the current item;\n *     will move item if evaluates to `true`\n * @returns {void}\n */\nconst moveToAnotherArray = function (source, target, conditionCb) {\n    const il = source.length;\n    for (let i = 0; i < il; i++) {\n        const item = source[i];\n        if (conditionCb(item)) {\n            // eslint-disable-next-line @stylistic/max-len -- Long\n            // eslint-disable-next-line sonarjs/updated-loop-counter -- Convenient\n            target.push(source.splice(i--, 1)[0]);\n        }\n    }\n};\n\n/**\n * In-browser replacement for NodeJS' VM.Script.\n */\nclass Script {\n    /**\n     * @param {string} expr Expression to evaluate\n     */\n    constructor (expr) {\n        this.code = expr;\n    }\n\n    /**\n     * @param {object} context Object whose items will be added\n     *   to evaluation\n     * @returns {EvaluatedResult} Result of evaluated code\n     */\n    runInNewContext (context) {\n        let expr = this.code;\n        const keys = Object.keys(context);\n        const funcs = [];\n        moveToAnotherArray(keys, funcs, (key) => {\n            return typeof context[key] === 'function';\n        });\n        const values = keys.map((vr) => {\n            return context[vr];\n        });\n\n        const funcString = funcs.reduce((s, func) => {\n            let fString = context[func].toString();\n            if (!(/function/u).test(fString)) {\n                fString = 'function ' + fString;\n            }\n            return 'var ' + func + '=' + fString + ';' + s;\n        }, '');\n\n        expr = funcString + expr;\n\n        // Mitigate http://perfectionkills.com/global-eval-what-are-the-options/#new_function\n        if (!(/(['\"])use strict\\1/u).test(expr) && !keys.includes('arguments')) {\n            expr = 'var arguments = undefined;' + expr;\n        }\n\n        // Remove last semi so `return` will be inserted before\n        //  the previous one instead, allowing for the return\n        //  of a bare ending expression\n        expr = expr.replace(/;\\s*$/u, '');\n\n        // Insert `return`\n        const lastStatementEnd = expr.lastIndexOf(';');\n        const code =\n            lastStatementEnd !== -1\n                ? expr.slice(0, lastStatementEnd + 1) +\n                  ' return ' +\n                  expr.slice(lastStatementEnd + 1)\n                : ' return ' + expr;\n\n        // eslint-disable-next-line no-new-func -- User's choice\n        return new Function(...keys, code)(...values);\n    }\n}\n\nJSONPath.prototype.vm = {\n    Script\n};\n\nexport {JSONPath};\n"], "names": ["Jsep", "version", "toString", "addUnaryOp", "op_name", "max_unop_len", "Math", "max", "length", "unary_ops", "addBinaryOp", "precedence", "isRightAssociative", "max_binop_len", "binary_ops", "right_associative", "add", "delete", "addIdentifierChar", "char", "additional_identifier_chars", "addLiteral", "literal_name", "literal_value", "literals", "removeUnaryOp", "getMaxKeyLen", "removeAllUnaryOps", "removeIdentifierChar", "removeBinaryOp", "removeAllBinaryOps", "removeLiteral", "removeAllLiterals", "this", "expr", "char<PERSON>t", "index", "code", "charCodeAt", "constructor", "parse", "obj", "Object", "keys", "map", "k", "isDecimalDigit", "ch", "binaryPrecedence", "op_val", "isIdentifierStart", "String", "fromCharCode", "has", "isIdentifierPart", "throwError", "message", "error", "Error", "description", "runHook", "name", "node", "hooks", "env", "context", "run", "searchHook", "find", "callback", "call", "gobbleSpaces", "SPACE_CODE", "TAB_CODE", "LF_CODE", "CR_CODE", "nodes", "gobbleExpressions", "type", "COMPOUND", "body", "untilICode", "ch_i", "SEMCOL_CODE", "COMMA_CODE", "gobbleExpression", "push", "gobbleBinaryExpression", "gobbleBinaryOp", "to_check", "substr", "tc_len", "hasOwnProperty", "biop", "prec", "stack", "biop_info", "left", "right", "i", "cur_biop", "gobbleToken", "value", "right_a", "comparePrev", "prev", "pop", "BINARY_EXP", "operator", "PERIOD_CODE", "gobbleNumericLiteral", "SQUOTE_CODE", "DQUOTE_CODE", "gobbleString<PERSON>iteral", "OBRACK_CODE", "gobble<PERSON><PERSON><PERSON>", "argument", "UNARY_EXP", "prefix", "gobbleIdentifier", "LITERAL", "raw", "this_str", "THIS_EXP", "OPAREN_CODE", "gobbleGroup", "gobbleTokenProperty", "QUMARK_CODE", "optional", "MEMBER_EXP", "computed", "object", "property", "CBRACK_CODE", "CALL_EXP", "arguments", "gobbleArguments", "CPAREN_CODE", "callee", "chCode", "number", "parseFloat", "str", "startIndex", "quote", "closed", "substring", "start", "IDENTIFIER", "slice", "termination", "args", "separator_count", "arg", "SEQUENCE_EXP", "expressions", "ARRAY_EXP", "elements", "first", "Array", "isArray", "for<PERSON>ach", "assign", "plugins", "jsep", "registered", "register", "_len", "_key", "plugin", "init", "COLON_CODE", "Set", "true", "false", "null", "stdClassProps", "getOwnPropertyNames", "filter", "prop", "includes", "undefined", "m", "ternary", "test", "consequent", "alternate", "newTest", "patternIndex", "inCharSet", "pattern", "flags", "RegExp", "e", "assignmentOperators", "updateOperators", "assignmentPrecedence", "updateNodeTypes", "updateBinariesToAssignments", "values", "val", "op", "some", "c", "jsepRegex", "jsepAssignment", "BLOCKED_PROTO_PROPERTIES", "SafeEval", "evalAst", "ast", "subs", "evalBinaryExpression", "evalCompound", "evalConditionalExpression", "evalIdentifier", "evalLiteral", "evalMemberExpression", "evalUnaryExpression", "evalArrayExpression", "evalCallExpression", "evalAssignmentExpression", "SyntaxError", "||", "a", "b", "&&", "|", "^", "&", "==", "!=", "===", "!==", "<", ">", "<=", ">=", "<<", ">>", ">>>", "+", "-", "*", "/", "%", "last", "hasOwn", "ReferenceError", "TypeError", "result", "bind", "typeof", "el", "func", "id", "arr", "item", "unshift", "NewError", "super", "avoidNew", "JSONPath", "opts", "otherTypeCallback", "optObj", "json", "path", "resultType", "flatten", "wrap", "sandbox", "eval", "ignoreEvalErrors", "parent", "parentProperty", "autostart", "ret", "evaluate", "prototype", "currParent", "currParentProperty", "currResultType", "currEval", "currSandbox", "currOtherTypeCallback", "toPathString", "exprList", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "shift", "_hasParentSelector", "_trace", "ea", "isParentSelector", "hasArrExpr", "reduce", "rslt", "val<PERSON><PERSON><PERSON><PERSON>", "_getPreferredOutput", "concat", "pointer", "toPointer", "_handleCallback", "fullRetObj", "preferredOutput", "parentPropName", "literalPriority", "retObj", "loc", "x", "addRet", "elems", "t", "_walk", "_slice", "indexOf", "safeLoc", "replace", "nested", "exec", "npath", "nvalue", "_eval", "at", "addType", "valueType", "Number", "isFinite", "locProp", "parts", "split", "part", "rett", "tmp", "tl", "tt", "splice", "f", "n", "len", "step", "parseInt", "end", "min", "_v", "_vname", "_$_parentProperty", "_$_parent", "_$_property", "_$_root", "_$_v", "containsPath", "_$_path", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache", "script", "replaceAll", "safeVm", "<PERSON><PERSON><PERSON>", "vm", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "runInNewContext", "pathArr", "p", "subx", "$0", "$1", "ups", "join", "exp", "match", "keyMap", "create", "funcs", "source", "target", "conditionCb", "il", "moveToAnotherArray", "key", "vr", "s", "fString", "lastStatementEnd", "lastIndexOf", "Function"], "mappings": "AAgGA,MAAMA,EAIL,kBAAWC,GAEV,MAAO,OACR,CAKA,eAAOC,GACN,MAAO,wCAA0CF,EAAKC,OACvD,CAQA,iBAAOE,CAAWC,GAGjB,OAFAJ,EAAKK,aAAeC,KAAKC,IAAIH,EAAQI,OAAQR,EAAKK,cAClDL,EAAKS,UAAUL,GAAW,EACnBJ,CACR,CASA,kBAAOU,CAAYN,EAASO,EAAYC,GASvC,OARAZ,EAAKa,cAAgBP,KAAKC,IAAIH,EAAQI,OAAQR,EAAKa,eACnDb,EAAKc,WAAWV,GAAWO,EACvBC,EACHZ,EAAKe,kBAAkBC,IAAIZ,GAG3BJ,EAAKe,kBAAkBE,OAAOb,GAExBJ,CACR,CAOA,wBAAOkB,CAAkBC,GAExB,OADAnB,EAAKoB,4BAA4BJ,IAAIG,GAC9BnB,CACR,CAQA,iBAAOqB,CAAWC,EAAcC,GAE/B,OADAvB,EAAKwB,SAASF,GAAgBC,EACvBvB,CACR,CAOA,oBAAOyB,CAAcrB,GAKpB,cAJOJ,EAAKS,UAAUL,GAClBA,EAAQI,SAAWR,EAAKK,eAC3BL,EAAKK,aAAeL,EAAK0B,aAAa1B,EAAKS,YAErCT,CACR,CAMA,wBAAO2B,GAIN,OAHA3B,EAAKS,UAAY,CAAE,EACnBT,EAAKK,aAAe,EAEbL,CACR,CAOA,2BAAO4B,CAAqBT,GAE3B,OADAnB,EAAKoB,4BAA4BH,OAAOE,GACjCnB,CACR,CAOA,qBAAO6B,CAAezB,GAQrB,cAPOJ,EAAKc,WAAWV,GAEnBA,EAAQI,SAAWR,EAAKa,gBAC3Bb,EAAKa,cAAgBb,EAAK0B,aAAa1B,EAAKc,aAE7Cd,EAAKe,kBAAkBE,OAAOb,GAEvBJ,CACR,CAMA,yBAAO8B,GAIN,OAHA9B,EAAKc,WAAa,CAAE,EACpBd,EAAKa,cAAgB,EAEdb,CACR,CAOA,oBAAO+B,CAAcT,GAEpB,cADOtB,EAAKwB,SAASF,GACdtB,CACR,CAMA,wBAAOgC,GAGN,OAFAhC,EAAKwB,SAAW,CAAE,EAEXxB,CACR,CAOA,QAAImB,GACH,OAAOc,KAAKC,KAAKC,OAAOF,KAAKG,MAC9B,CAKA,QAAIC,GACH,OAAOJ,KAAKC,KAAKI,WAAWL,KAAKG,MAClC,CAOAG,WAAAA,CAAYL,GAGXD,KAAKC,KAAOA,EACZD,KAAKG,MAAQ,CACd,CAMA,YAAOI,CAAMN,GACZ,OAAQ,IAAIlC,EAAKkC,GAAOM,OACzB,CAOA,mBAAOd,CAAae,GACnB,OAAOnC,KAAKC,IAAI,KAAMmC,OAAOC,KAAKF,GAAKG,KAAIC,GAAKA,EAAErC,SACnD,CAOA,qBAAOsC,CAAeC,GACrB,OAAQA,GAAM,IAAMA,GAAM,EAC3B,CAOA,uBAAOC,CAAiBC,GACvB,OAAOjD,EAAKc,WAAWmC,IAAW,CACnC,CAOA,wBAAOC,CAAkBH,GACxB,OAASA,GAAM,IAAMA,GAAM,IACzBA,GAAM,IAAMA,GAAM,KAClBA,GAAM,MAAQ/C,EAAKc,WAAWqC,OAAOC,aAAaL,KAClD/C,EAAKoB,4BAA4BiC,IAAIF,OAAOC,aAAaL,GAC5D,CAMA,uBAAOO,CAAiBP,GACvB,OAAO/C,EAAKkD,kBAAkBH,IAAO/C,EAAK8C,eAAeC,EAC1D,CAOAQ,UAAAA,CAAWC,GACV,MAAMC,EAAQ,IAAIC,MAAMF,EAAU,iBAAmBvB,KAAKG,OAG1D,MAFAqB,EAAMrB,MAAQH,KAAKG,MACnBqB,EAAME,YAAcH,EACdC,CACP,CAQAG,OAAAA,CAAQC,EAAMC,GACb,GAAI9D,EAAK+D,MAAMF,GAAO,CACrB,MAAMG,EAAM,CAAEC,QAAShC,KAAM6B,QAE7B,OADA9D,EAAK+D,MAAMG,IAAIL,EAAMG,GACdA,EAAIF,IACZ,CACA,OAAOA,CACR,CAOAK,UAAAA,CAAWN,GACV,GAAI7D,EAAK+D,MAAMF,GAAO,CACrB,MAAMG,EAAM,CAAEC,QAAShC,MAKvB,OAJAjC,EAAK+D,MAAMF,GAAMO,MAAK,SAAUC,GAE/B,OADAA,EAASC,KAAKN,EAAIC,QAASD,GACpBA,EAAIF,IACZ,IACOE,EAAIF,IACZ,CACD,CAKAS,YAAAA,GACC,IAAIxB,EAAKd,KAAKI,KAEd,KAAOU,IAAO/C,EAAKwE,YAChBzB,IAAO/C,EAAKyE,UACZ1B,IAAO/C,EAAK0E,SACZ3B,IAAO/C,EAAK2E,SACd5B,EAAKd,KAAKC,KAAKI,aAAaL,KAAKG,OAElCH,KAAK2B,QAAQ,gBACd,CAMApB,KAAAA,GACCP,KAAK2B,QAAQ,cACb,MAAMgB,EAAQ3C,KAAK4C,oBAGbf,EAAwB,IAAjBc,EAAMpE,OACfoE,EAAM,GACP,CACDE,KAAM9E,EAAK+E,SACXC,KAAMJ,GAER,OAAO3C,KAAK2B,QAAQ,YAAaE,EAClC,CAOAe,iBAAAA,CAAkBI,GACjB,IAAgBC,EAAMpB,EAAlBc,EAAQ,GAEZ,KAAO3C,KAAKG,MAAQH,KAAKC,KAAK1B,QAK7B,GAJA0E,EAAOjD,KAAKI,KAIR6C,IAASlF,EAAKmF,aAAeD,IAASlF,EAAKoF,WAC9CnD,KAAKG,aAIL,GAAI0B,EAAO7B,KAAKoD,mBACfT,EAAMU,KAAKxB,QAIP,GAAI7B,KAAKG,MAAQH,KAAKC,KAAK1B,OAAQ,CACvC,GAAI0E,IAASD,EACZ,MAEDhD,KAAKsB,WAAW,eAAiBtB,KAAKd,KAAO,IAC9C,CAIF,OAAOyD,CACR,CAMAS,gBAAAA,GACC,MAAMvB,EAAO7B,KAAKkC,WAAW,sBAAwBlC,KAAKsD,yBAG1D,OAFAtD,KAAKsC,eAEEtC,KAAK2B,QAAQ,mBAAoBE,EACzC,CASA0B,cAAAA,GACCvD,KAAKsC,eACL,IAAIkB,EAAWxD,KAAKC,KAAKwD,OAAOzD,KAAKG,MAAOpC,EAAKa,eAC7C8E,EAASF,EAASjF,OAEtB,KAAOmF,EAAS,GAAG,CAIlB,GAAI3F,EAAKc,WAAW8E,eAAeH,MACjCzF,EAAKkD,kBAAkBjB,KAAKI,OAC5BJ,KAAKG,MAAQqD,EAASjF,OAASyB,KAAKC,KAAK1B,SAAWR,EAAKsD,iBAAiBrB,KAAKC,KAAKI,WAAWL,KAAKG,MAAQqD,EAASjF,UAGtH,OADAyB,KAAKG,OAASuD,EACPF,EAERA,EAAWA,EAASC,OAAO,IAAKC,EACjC,CACA,OAAO,CACR,CAOAJ,sBAAAA,GACC,IAAIzB,EAAM+B,EAAMC,EAAMC,EAAOC,EAAWC,EAAMC,EAAOC,EAAGC,EAMxD,GADAH,EAAOhE,KAAKoE,eACPJ,EACJ,OAAOA,EAKR,GAHAJ,EAAO5D,KAAKuD,kBAGPK,EACJ,OAAOI,EAgBR,IAXAD,EAAY,CAAEM,MAAOT,EAAMC,KAAM9F,EAAKgD,iBAAiB6C,GAAOU,QAASvG,EAAKe,kBAAkBsC,IAAIwC,IAElGK,EAAQjE,KAAKoE,cAERH,GACJjE,KAAKsB,WAAW,6BAA+BsC,GAGhDE,EAAQ,CAACE,EAAMD,EAAWE,GAGlBL,EAAO5D,KAAKuD,kBAAmB,CAGtC,GAFAM,EAAO9F,EAAKgD,iBAAiB6C,GAEhB,IAATC,EAAY,CACf7D,KAAKG,OAASyD,EAAKrF,OACnB,KACD,CAEAwF,EAAY,CAAEM,MAAOT,EAAMC,OAAMS,QAASvG,EAAKe,kBAAkBsC,IAAIwC,IAErEO,EAAWP,EAGX,MAAMW,EAAcC,GAAQT,EAAUO,SAAWE,EAAKF,QACnDT,EAAOW,EAAKX,KACZA,GAAQW,EAAKX,KAChB,KAAQC,EAAMvF,OAAS,GAAMgG,EAAYT,EAAMA,EAAMvF,OAAS,KAC7D0F,EAAQH,EAAMW,MACdb,EAAOE,EAAMW,MAAMJ,MACnBL,EAAOF,EAAMW,MACb5C,EAAO,CACNgB,KAAM9E,EAAK2G,WACXC,SAAUf,EACVI,OACAC,SAEDH,EAAMT,KAAKxB,GAGZA,EAAO7B,KAAKoE,cAEPvC,GACJ7B,KAAKsB,WAAW,6BAA+B6C,GAGhDL,EAAMT,KAAKU,EAAWlC,EACvB,CAKA,IAHAqC,EAAIJ,EAAMvF,OAAS,EACnBsD,EAAOiC,EAAMI,GAENA,EAAI,GACVrC,EAAO,CACNgB,KAAM9E,EAAK2G,WACXC,SAAUb,EAAMI,EAAI,GAAGG,MACvBL,KAAMF,EAAMI,EAAI,GAChBD,MAAOpC,GAERqC,GAAK,EAGN,OAAOrC,CACR,CAOAuC,WAAAA,GACC,IAAItD,EAAI0C,EAAUE,EAAQ7B,EAI1B,GAFA7B,KAAKsC,eACLT,EAAO7B,KAAKkC,WAAW,gBACnBL,EACH,OAAO7B,KAAK2B,QAAQ,cAAeE,GAKpC,GAFAf,EAAKd,KAAKI,KAENrC,EAAK8C,eAAeC,IAAOA,IAAO/C,EAAK6G,YAE1C,OAAO5E,KAAK6E,uBAGb,GAAI/D,IAAO/C,EAAK+G,aAAehE,IAAO/C,EAAKgH,YAE1ClD,EAAO7B,KAAKgF,2BAER,GAAIlE,IAAO/C,EAAKkH,YACpBpD,EAAO7B,KAAKkF,kBAER,CAIJ,IAHA1B,EAAWxD,KAAKC,KAAKwD,OAAOzD,KAAKG,MAAOpC,EAAKK,cAC7CsF,EAASF,EAASjF,OAEXmF,EAAS,GAAG,CAIlB,GAAI3F,EAAKS,UAAUmF,eAAeH,MAChCzF,EAAKkD,kBAAkBjB,KAAKI,OAC5BJ,KAAKG,MAAQqD,EAASjF,OAASyB,KAAKC,KAAK1B,SAAWR,EAAKsD,iBAAiBrB,KAAKC,KAAKI,WAAWL,KAAKG,MAAQqD,EAASjF,UACpH,CACFyB,KAAKG,OAASuD,EACd,MAAMyB,EAAWnF,KAAKoE,cAItB,OAHKe,GACJnF,KAAKsB,WAAW,4BAEVtB,KAAK2B,QAAQ,cAAe,CAClCkB,KAAM9E,EAAKqH,UACXT,SAAUnB,EACV2B,WACAE,QAAQ,GAEV,CAEA7B,EAAWA,EAASC,OAAO,IAAKC,EACjC,CAEI3F,EAAKkD,kBAAkBH,IAC1Be,EAAO7B,KAAKsF,mBACRvH,EAAKwB,SAASoE,eAAe9B,EAAKD,MACrCC,EAAO,CACNgB,KAAM9E,EAAKwH,QACXlB,MAAOtG,EAAKwB,SAASsC,EAAKD,MAC1B4D,IAAK3D,EAAKD,MAGHC,EAAKD,OAAS7D,EAAK0H,WAC3B5D,EAAO,CAAEgB,KAAM9E,EAAK2H,YAGb5E,IAAO/C,EAAK4H,cACpB9D,EAAO7B,KAAK4F,cAEd,CAEA,OAAK/D,GAILA,EAAO7B,KAAK6F,oBAAoBhE,GACzB7B,KAAK2B,QAAQ,cAAeE,IAJ3B7B,KAAK2B,QAAQ,eAAe,EAKrC,CAUAkE,mBAAAA,CAAoBhE,GACnB7B,KAAKsC,eAEL,IAAIxB,EAAKd,KAAKI,KACd,KAAOU,IAAO/C,EAAK6G,aAAe9D,IAAO/C,EAAKkH,aAAenE,IAAO/C,EAAK4H,aAAe7E,IAAO/C,EAAK+H,aAAa,CAChH,IAAIC,EACJ,GAAIjF,IAAO/C,EAAK+H,YAAa,CAC5B,GAAI9F,KAAKC,KAAKI,WAAWL,KAAKG,MAAQ,KAAOpC,EAAK6G,YACjD,MAEDmB,GAAW,EACX/F,KAAKG,OAAS,EACdH,KAAKsC,eACLxB,EAAKd,KAAKI,IACX,CACAJ,KAAKG,QAEDW,IAAO/C,EAAKkH,cACfpD,EAAO,CACNgB,KAAM9E,EAAKiI,WACXC,UAAU,EACVC,OAAQrE,EACRsE,SAAUnG,KAAKoD,qBAEN+C,UACTnG,KAAKsB,WAAW,eAAiBtB,KAAKd,KAAO,KAE9Cc,KAAKsC,eACLxB,EAAKd,KAAKI,KACNU,IAAO/C,EAAKqI,aACfpG,KAAKsB,WAAW,cAEjBtB,KAAKG,SAEGW,IAAO/C,EAAK4H,YAEpB9D,EAAO,CACNgB,KAAM9E,EAAKsI,SACXC,UAAatG,KAAKuG,gBAAgBxI,EAAKyI,aACvCC,OAAQ5E,IAGDf,IAAO/C,EAAK6G,aAAemB,KAC/BA,GACH/F,KAAKG,QAENH,KAAKsC,eACLT,EAAO,CACNgB,KAAM9E,EAAKiI,WACXC,UAAU,EACVC,OAAQrE,EACRsE,SAAUnG,KAAKsF,qBAIbS,IACHlE,EAAKkE,UAAW,GAGjB/F,KAAKsC,eACLxB,EAAKd,KAAKI,IACX,CAEA,OAAOyB,CACR,CAOAgD,oBAAAA,GACC,IAAiB/D,EAAI4F,EAAjBC,EAAS,GAEb,KAAO5I,EAAK8C,eAAeb,KAAKI,OAC/BuG,GAAU3G,KAAKC,KAAKC,OAAOF,KAAKG,SAGjC,GAAIH,KAAKI,OAASrC,EAAK6G,YAGtB,IAFA+B,GAAU3G,KAAKC,KAAKC,OAAOF,KAAKG,SAEzBpC,EAAK8C,eAAeb,KAAKI,OAC/BuG,GAAU3G,KAAKC,KAAKC,OAAOF,KAAKG,SAMlC,GAFAW,EAAKd,KAAKd,KAEC,MAAP4B,GAAqB,MAAPA,EAAY,CAQ7B,IAPA6F,GAAU3G,KAAKC,KAAKC,OAAOF,KAAKG,SAChCW,EAAKd,KAAKd,KAEC,MAAP4B,GAAqB,MAAPA,IACjB6F,GAAU3G,KAAKC,KAAKC,OAAOF,KAAKG,UAG1BpC,EAAK8C,eAAeb,KAAKI,OAC/BuG,GAAU3G,KAAKC,KAAKC,OAAOF,KAAKG,SAG5BpC,EAAK8C,eAAeb,KAAKC,KAAKI,WAAWL,KAAKG,MAAQ,KAC1DH,KAAKsB,WAAW,sBAAwBqF,EAAS3G,KAAKd,KAAO,IAE/D,CAaA,OAXAwH,EAAS1G,KAAKI,KAGVrC,EAAKkD,kBAAkByF,GAC1B1G,KAAKsB,WAAW,8CACfqF,EAAS3G,KAAKd,KAAO,MAEdwH,IAAW3I,EAAK6G,aAAkC,IAAlB+B,EAAOpI,QAAgBoI,EAAOtG,WAAW,KAAOtC,EAAK6G,cAC7F5E,KAAKsB,WAAW,qBAGV,CACNuB,KAAM9E,EAAKwH,QACXlB,MAAOuC,WAAWD,GAClBnB,IAAKmB,EAEP,CAOA3B,mBAAAA,GACC,IAAI6B,EAAM,GACV,MAAMC,EAAa9G,KAAKG,MAClB4G,EAAQ/G,KAAKC,KAAKC,OAAOF,KAAKG,SACpC,IAAI6G,GAAS,EAEb,KAAOhH,KAAKG,MAAQH,KAAKC,KAAK1B,QAAQ,CACrC,IAAIuC,EAAKd,KAAKC,KAAKC,OAAOF,KAAKG,SAE/B,GAAIW,IAAOiG,EAAO,CACjBC,GAAS,EACT,KACD,CACK,GAAW,OAAPlG,EAIR,OAFAA,EAAKd,KAAKC,KAAKC,OAAOF,KAAKG,SAEnBW,GACP,IAAK,IAAK+F,GAAO,KAAM,MACvB,IAAK,IAAKA,GAAO,KAAM,MACvB,IAAK,IAAKA,GAAO,KAAM,MACvB,IAAK,IAAKA,GAAO,KAAM,MACvB,IAAK,IAAKA,GAAO,KAAM,MACvB,IAAK,IAAKA,GAAO,KAAQ,MACzB,QAAUA,GAAO/F,OAIlB+F,GAAO/F,CAET,CAMA,OAJKkG,GACJhH,KAAKsB,WAAW,yBAA2BuF,EAAM,KAG3C,CACNhE,KAAM9E,EAAKwH,QACXlB,MAAOwC,EACPrB,IAAKxF,KAAKC,KAAKgH,UAAUH,EAAY9G,KAAKG,OAE5C,CASAmF,gBAAAA,GACC,IAAIxE,EAAKd,KAAKI,KAAM8G,EAAQlH,KAAKG,MASjC,IAPIpC,EAAKkD,kBAAkBH,GAC1Bd,KAAKG,QAGLH,KAAKsB,WAAW,cAAgBtB,KAAKd,MAG/Bc,KAAKG,MAAQH,KAAKC,KAAK1B,SAC7BuC,EAAKd,KAAKI,KAENrC,EAAKsD,iBAAiBP,KACzBd,KAAKG,QAMP,MAAO,CACN0C,KAAM9E,EAAKoJ,WACXvF,KAAM5B,KAAKC,KAAKmH,MAAMF,EAAOlH,KAAKG,OAEpC,CAWAoG,eAAAA,CAAgBc,GACf,MAAMC,EAAO,GACb,IAAIN,GAAS,EACTO,EAAkB,EAEtB,KAAOvH,KAAKG,MAAQH,KAAKC,KAAK1B,QAAQ,CACrCyB,KAAKsC,eACL,IAAIW,EAAOjD,KAAKI,KAEhB,GAAI6C,IAASoE,EAAa,CACzBL,GAAS,EACThH,KAAKG,QAEDkH,IAAgBtJ,EAAKyI,aAAee,GAAmBA,GAAmBD,EAAK/I,QAClFyB,KAAKsB,WAAW,oBAAsBJ,OAAOC,aAAakG,IAG3D,KACD,CACK,GAAIpE,IAASlF,EAAKoF,YAItB,GAHAnD,KAAKG,QACLoH,IAEIA,IAAoBD,EAAK/I,OAC5B,GAAI8I,IAAgBtJ,EAAKyI,YACxBxG,KAAKsB,WAAW,2BAEZ,GAAI+F,IAAgBtJ,EAAKqI,YAC7B,IAAK,IAAIoB,EAAMF,EAAK/I,OAAQiJ,EAAMD,EAAiBC,IAClDF,EAAKjE,KAAK,WAKT,GAAIiE,EAAK/I,SAAWgJ,GAAuC,IAApBA,EAE3CvH,KAAKsB,WAAW,sBAEZ,CACJ,MAAMO,EAAO7B,KAAKoD,mBAEbvB,GAAQA,EAAKgB,OAAS9E,EAAK+E,UAC/B9C,KAAKsB,WAAW,kBAGjBgG,EAAKjE,KAAKxB,EACX,CACD,CAMA,OAJKmF,GACJhH,KAAKsB,WAAW,YAAcJ,OAAOC,aAAakG,IAG5CC,CACR,CAWA1B,WAAAA,GACC5F,KAAKG,QACL,IAAIwC,EAAQ3C,KAAK4C,kBAAkB7E,EAAKyI,aACxC,GAAIxG,KAAKI,OAASrC,EAAKyI,YAEtB,OADAxG,KAAKG,QACgB,IAAjBwC,EAAMpE,OACFoE,EAAM,KAEJA,EAAMpE,QAIR,CACNsE,KAAM9E,EAAK0J,aACXC,YAAa/E,GAKf3C,KAAKsB,WAAW,aAElB,CAQA4D,WAAAA,GAGC,OAFAlF,KAAKG,QAEE,CACN0C,KAAM9E,EAAK4J,UACXC,SAAU5H,KAAKuG,gBAAgBxI,EAAKqI,aAEtC,EAID,MAAMtE,EAAQ,IA58Bd,MAmBC/C,GAAAA,CAAI6C,EAAMQ,EAAUyF,GACnB,GAA2B,iBAAhBvB,UAAU,GAEpB,IAAK,IAAI1E,KAAQ0E,UAAU,GAC1BtG,KAAKjB,IAAI6C,EAAM0E,UAAU,GAAG1E,GAAO0E,UAAU,SAI7CwB,MAAMC,QAAQnG,GAAQA,EAAO,CAACA,IAAOoG,SAAQ,SAAUpG,GACvD5B,KAAK4B,GAAQ5B,KAAK4B,IAAS,GAEvBQ,GACHpC,KAAK4B,GAAMiG,EAAQ,UAAY,QAAQzF,EAExC,GAAEpC,KAEL,CAWAiC,GAAAA,CAAIL,EAAMG,GACT/B,KAAK4B,GAAQ5B,KAAK4B,IAAS,GAC3B5B,KAAK4B,GAAMoG,SAAQ,SAAU5F,GAC5BA,EAASC,KAAKN,GAAOA,EAAIC,QAAUD,EAAIC,QAAUD,EAAKA,EACvD,GACD,GA05BDtB,OAAOwH,OAAOlK,EAAM,CACnB+D,QACAoG,QAAS,IAt5BV,MACC5H,WAAAA,CAAY6H,GACXnI,KAAKmI,KAAOA,EACZnI,KAAKoI,WAAa,CAAE,CACrB,CAeAC,QAAAA,GAAqB,IAAA,IAAAC,EAAAhC,UAAA/H,OAAT2J,EAAOJ,IAAAA,MAAAQ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPL,EAAOK,GAAAjC,UAAAiC,GAClBL,EAAQF,SAASQ,IAChB,GAAsB,iBAAXA,IAAwBA,EAAO5G,OAAS4G,EAAOC,KACzD,MAAM,IAAIhH,MAAM,8BAEbzB,KAAKoI,WAAWI,EAAO5G,QAI3B4G,EAAOC,KAAKzI,KAAKmI,MACjBnI,KAAKoI,WAAWI,EAAO5G,MAAQ4G,EAAM,GAEvC,GAu3BqBzK,GAMrB+E,SAAiB,WACjB2E,aAAiB,qBACjBN,WAAiB,aACjBnB,WAAiB,mBACjBT,QAAiB,UACjBG,SAAiB,iBACjBW,SAAiB,iBACjBjB,UAAiB,kBACjBV,WAAiB,mBACjBiD,UAAiB,kBAEjBnF,SAAa,EACbC,QAAa,GACbC,QAAa,GACbH,WAAa,GACbqC,YAAa,GACbzB,WAAa,GACb2B,YAAa,GACbC,YAAa,GACbY,YAAa,GACba,YAAa,GACbvB,YAAa,GACbmB,YAAa,GACbN,YAAa,GACb5C,YAAa,GACbwF,WAAa,GAOblK,UAAW,CACV,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,GAMNK,WAAY,CACX,KAAM,EAAG,KAAM,EACf,KAAM,EAAG,IAAK,EAAG,IAAK,EAAG,IAAK,EAC9B,KAAM,EAAG,KAAM,EAAG,MAAO,EAAG,MAAO,EACnC,IAAK,EAAG,IAAK,EAAG,KAAM,EAAG,KAAM,EAC/B,KAAM,EAAG,KAAM,EAAG,MAAO,EACzB,IAAK,EAAG,IAAK,EACb,IAAK,GAAI,IAAK,GAAI,IAAK,GACvB,KAAM,IAIPC,kBAAmB,IAAI6J,IAAI,CAAC,OAG5BxJ,4BAA6B,IAAIwJ,IAAI,CAAC,IAAK,MAK3CpJ,SAAU,CACTqJ,MAAQ,EACRC,OAAS,EACTC,KAAQ,MAITrD,SAAU,SAEX1H,EAAKK,aAAeL,EAAK0B,aAAa1B,EAAKS,WAC3CT,EAAKa,cAAgBb,EAAK0B,aAAa1B,EAAKc,YAG5C,MAAMsJ,EAAOlI,GAAS,IAAIlC,EAAKkC,GAAOM,QAChCwI,EAAgBtI,OAAOuI,oBAAoB,SACjDvI,OAAOuI,oBAAoBjL,GACzBkL,QAAOC,IAASH,EAAcI,SAASD,SAAwBE,IAAfjB,EAAKe,KACrDlB,SAASqB,IACTlB,EAAKkB,GAAKtL,EAAKsL,EAAE,IAEnBlB,EAAKpK,KAAOA,EAIZ,IAAIuL,EAAU,CACb1H,KAAM,UAEN6G,IAAAA,CAAKN,GAEJA,EAAKrG,MAAM/C,IAAI,oBAAoB,SAAuBgD,GACzD,GAAIA,EAAIF,MAAQ7B,KAAKI,OAAS+H,EAAKrC,YAAa,CAC/C9F,KAAKG,QACL,MAAMoJ,EAAOxH,EAAIF,KACX2H,EAAaxJ,KAAKoD,mBAQxB,GANKoG,GACJxJ,KAAKsB,WAAW,uBAGjBtB,KAAKsC,eAEDtC,KAAKI,OAAS+H,EAAKO,WAAY,CAClC1I,KAAKG,QACL,MAAMsJ,EAAYzJ,KAAKoD,mBAcvB,GAZKqG,GACJzJ,KAAKsB,WAAW,uBAEjBS,EAAIF,KAAO,CACVgB,KA3BkB,wBA4BlB0G,OACAC,aACAC,aAKGF,EAAK5E,UAAYwD,EAAKtJ,WAAW0K,EAAK5E,WAAa,GAAK,CAC3D,IAAI+E,EAAUH,EACd,KAAOG,EAAQzF,MAAMU,UAAYwD,EAAKtJ,WAAW6K,EAAQzF,MAAMU,WAAa,IAC3E+E,EAAUA,EAAQzF,MAEnBlC,EAAIF,KAAK0H,KAAOG,EAAQzF,MACxByF,EAAQzF,MAAQlC,EAAIF,KACpBE,EAAIF,KAAO0H,CACZ,CACD,MAECvJ,KAAKsB,WAAW,aAElB,CACD,GACD,GAKD6G,EAAKD,QAAQG,SAASiB,GChmCtB,IAAInJ,EAAQ,CACXyB,KAAM,QAEN6G,IAAAA,CAAKN,GAEJA,EAAKrG,MAAM/C,IAAI,gBAAgB,SAA4BgD,GAC1D,GATiB,KASb/B,KAAKI,KAAsB,CAC9B,MAAMuJ,IAAiB3J,KAAKG,MAE5B,IAAIyJ,GAAY,EAChB,KAAO5J,KAAKG,MAAQH,KAAKC,KAAK1B,QAAQ,CACrC,GAde,KAcXyB,KAAKI,OAAyBwJ,EAAW,CAC5C,MAAMC,EAAU7J,KAAKC,KAAKmH,MAAMuC,EAAc3J,KAAKG,OAEnD,IAaIkE,EAbAyF,EAAQ,GACZ,OAAS9J,KAAKG,MAAQH,KAAKC,KAAK1B,QAAQ,CACvC,MAAM6B,EAAOJ,KAAKI,KAClB,KAAKA,GAAQ,IAAMA,GAAQ,KACtBA,GAAQ,IAAMA,GAAQ,IACtBA,GAAQ,IAAMA,GAAQ,IAI1B,MAHA0J,GAAS9J,KAAKd,IAKhB,CAGA,IACCmF,EAAQ,IAAI0F,OAAOF,EAASC,EAC5B,CACD,MAAOE,GACNhK,KAAKsB,WAAW0I,EAAEzI,QACnB,CAUA,OARAQ,EAAIF,KAAO,CACVgB,KAAMsF,EAAK5C,QACXlB,QACAmB,IAAKxF,KAAKC,KAAKmH,MAAMuC,EAAe,EAAG3J,KAAKG,QAI7C4B,EAAIF,KAAO7B,KAAK6F,oBAAoB9D,EAAIF,MACjCE,EAAIF,IACZ,CACI7B,KAAKI,OAAS+H,EAAKlD,YACtB2E,GAAY,EAEJA,GAAa5J,KAAKI,OAAS+H,EAAK/B,cACxCwD,GAAY,GAEb5J,KAAKG,OArDU,KAqDDH,KAAKI,KAAuB,EAAI,CAC/C,CACAJ,KAAKsB,WAAW,iBACjB,CACD,GACD,GC3DD,MAGMkH,EAAS,CACd5G,KAAM,aAENqI,oBAAqB,IAAItB,IAAI,CAC5B,IACA,KACA,MACA,KACA,KACA,KACA,KACA,MACA,MACA,OACA,KACA,KACA,KACA,MACA,MACA,QAEDuB,gBAAiB,CAxBA,GACC,IAwBlBC,qBAAsB,GAEtB1B,IAAAA,CAAKN,GACJ,MAAMiC,EAAkB,CAACjC,EAAKhB,WAAYgB,EAAKnC,YA8C/C,SAASqE,EAA4BxI,GAChC2G,EAAOyB,oBAAoB7I,IAAIS,EAAK8C,WACvC9C,EAAKgB,KAAO,uBACZwH,EAA4BxI,EAAKmC,MACjCqG,EAA4BxI,EAAKoC,QAExBpC,EAAK8C,UACdlE,OAAO6J,OAAOzI,GAAMmG,SAASuC,IACxBA,GAAsB,iBAARA,GACjBF,EAA4BE,EAC7B,GAGH,CA1DA/B,EAAOyB,oBAAoBjC,SAAQwC,GAAMrC,EAAK1J,YAAY+L,EAAIhC,EAAO2B,sBAAsB,KAE3FhC,EAAKrG,MAAM/C,IAAI,gBAAgB,SAA4BgD,GAC1D,MAAM3B,EAAOJ,KAAKI,KACdoI,EAAO0B,gBAAgBO,MAAKC,GAAKA,IAAMtK,GAAQsK,IAAM1K,KAAKC,KAAKI,WAAWL,KAAKG,MAAQ,OAC1FH,KAAKG,OAAS,EACd4B,EAAIF,KAAO,CACVgB,KAAM,mBACN8B,SArCa,KAqCHvE,EAAqB,KAAO,KACtC+E,SAAUnF,KAAK6F,oBAAoB7F,KAAKsF,oBACxCD,QAAQ,GAEJtD,EAAIF,KAAKsD,UAAaiF,EAAgBjB,SAASpH,EAAIF,KAAKsD,SAAStC,OACrE7C,KAAKsB,WAAW,cAAcS,EAAIF,KAAK8C,YAG1C,IAEAwD,EAAKrG,MAAM/C,IAAI,eAAe,SAA6BgD,GAC1D,GAAIA,EAAIF,KAAM,CACb,MAAMzB,EAAOJ,KAAKI,KACdoI,EAAO0B,gBAAgBO,MAAKC,GAAKA,IAAMtK,GAAQsK,IAAM1K,KAAKC,KAAKI,WAAWL,KAAKG,MAAQ,OACrFiK,EAAgBjB,SAASpH,EAAIF,KAAKgB,OACtC7C,KAAKsB,WAAW,cAAcS,EAAIF,KAAK8C,YAExC3E,KAAKG,OAAS,EACd4B,EAAIF,KAAO,CACVgB,KAAM,mBACN8B,SAzDY,KAyDFvE,EAAqB,KAAO,KACtC+E,SAAUpD,EAAIF,KACdwD,QAAQ,GAGX,CACD,IAEA8C,EAAKrG,MAAM/C,IAAI,oBAAoB,SAA0BgD,GACxDA,EAAIF,MAIPwI,EAA4BtI,EAAIF,KAElC,GAgBD,GClFDsG,EAAKD,QAAQG,SAASsC,EAAWC,GACjCzC,EAAKjK,WAAW,UAChBiK,EAAK/I,WAAW,OAAQ,MACxB+I,EAAK/I,WAAW,iBAAagK,GAE7B,MAAMyB,EAA2B,IAAIlC,IAAI,CACrC,cACA,YACA,mBACA,qBAGEmC,EAAW,CAKbC,OAAAA,CAASC,EAAKC,GACV,OAAQD,EAAInI,MACZ,IAAK,mBACL,IAAK,oBACD,OAAOiI,EAASI,qBAAqBF,EAAKC,GAC9C,IAAK,WACD,OAAOH,EAASK,aAAaH,EAAKC,GACtC,IAAK,wBACD,OAAOH,EAASM,0BAA0BJ,EAAKC,GACnD,IAAK,aACD,OAAOH,EAASO,eAAeL,EAAKC,GACxC,IAAK,UACD,OAAOH,EAASQ,YAAYN,EAAKC,GACrC,IAAK,mBACD,OAAOH,EAASS,qBAAqBP,EAAKC,GAC9C,IAAK,kBACD,OAAOH,EAASU,oBAAoBR,EAAKC,GAC7C,IAAK,kBACD,OAAOH,EAASW,oBAAoBT,EAAKC,GAC7C,IAAK,iBACD,OAAOH,EAASY,mBAAmBV,EAAKC,GAC5C,IAAK,uBACD,OAAOH,EAASa,yBAAyBX,EAAKC,GAClD,QACI,MAAMW,YAAY,wBAAyBZ,GAElD,EACDE,qBAAoBA,CAAEF,EAAKC,KACR,CACX,KAAMY,CAACC,EAAGC,IAAMD,GAAKC,IACrB,KAAMC,CAACF,EAAGC,IAAMD,GAAKC,IACrB,IAAKE,CAACH,EAAGC,IAAMD,EAAIC,IACnB,IAAKG,CAACJ,EAAGC,IAAMD,EAAIC,IACnB,IAAKI,CAACL,EAAGC,IAAMD,EAAIC,IAEnB,KAAMK,CAACN,EAAGC,IAAMD,GAAKC,IAErB,KAAMM,CAACP,EAAGC,IAAMD,GAAKC,IACrB,MAAOO,CAACR,EAAGC,IAAMD,IAAMC,IACvB,MAAOQ,CAACT,EAAGC,IAAMD,IAAMC,IACvB,IAAKS,CAACV,EAAGC,IAAMD,EAAIC,IACnB,IAAKU,CAACX,EAAGC,IAAMD,EAAIC,IACnB,KAAMW,CAACZ,EAAGC,IAAMD,GAAKC,IACrB,KAAMY,CAACb,EAAGC,IAAMD,GAAKC,IACrB,KAAMa,CAACd,EAAGC,IAAMD,GAAKC,IACrB,KAAMc,CAACf,EAAGC,IAAMD,GAAKC,IACrB,MAAOe,CAAChB,EAAGC,IAAMD,IAAMC,IACvB,IAAKgB,CAACjB,EAAGC,IAAMD,EAAIC,IACnB,IAAKiB,CAAClB,EAAGC,IAAMD,EAAIC,IACnB,IAAKkB,CAACnB,EAAGC,IAAMD,EAAIC,IACnB,IAAKmB,CAACpB,EAAGC,IAAMD,EAAIC,IACnB,IAAKoB,CAACrB,EAAGC,IAAMD,EAAIC,KACrBf,EAAIrG,UACFmG,EAASC,QAAQC,EAAIhH,KAAMiH,IAC3B,IAAMH,EAASC,QAAQC,EAAI/G,MAAOgH,MAI1CE,YAAAA,CAAcH,EAAKC,GACf,IAAImC,EACJ,IAAK,IAAIlJ,EAAI,EAAGA,EAAI8G,EAAIjI,KAAKxE,OAAQ2F,IAAK,CAEb,eAArB8G,EAAIjI,KAAKmB,GAAGrB,MACZ,CAAC,MAAO,MAAO,SAASsG,SAAS6B,EAAIjI,KAAKmB,GAAGtC,OAC7CoJ,EAAIjI,KAAKmB,EAAI,IACY,yBAAzB8G,EAAIjI,KAAKmB,EAAI,GAAGrB,OAMhBqB,GAAK,GAET,MAAMjE,EAAO+K,EAAIjI,KAAKmB,GACtBkJ,EAAOtC,EAASC,QAAQ9K,EAAMgL,EAClC,CACA,OAAOmC,CACV,EACDhC,0BAAyBA,CAAEJ,EAAKC,IACxBH,EAASC,QAAQC,EAAIzB,KAAM0B,GACpBH,EAASC,QAAQC,EAAIxB,WAAYyB,GAErCH,EAASC,QAAQC,EAAIvB,UAAWwB,GAE3CI,cAAAA,CAAgBL,EAAKC,GACjB,GAAIxK,OAAO4M,OAAOpC,EAAMD,EAAIpJ,MACxB,OAAOqJ,EAAKD,EAAIpJ,MAEpB,MAAM0L,eAAe,GAAGtC,EAAIpJ,sBAC/B,EACD0J,YAAaN,GACFA,EAAI3G,MAEfkH,oBAAAA,CAAsBP,EAAKC,GACvB,MAAM/B,EAAOhI,OAIT8J,EAAI/E,SACE6E,EAASC,QAAQC,EAAI7E,UACrB6E,EAAI7E,SAASvE,MAEjBpB,EAAMsK,EAASC,QAAQC,EAAI9E,OAAQ+E,GACzC,GAAIzK,QACA,MAAM+M,UACF,6BAA6B/M,eAAiB0I,OAGtD,IAAKzI,OAAO4M,OAAO7M,EAAK0I,IAAS2B,EAAyBzJ,IAAI8H,GAC1D,MAAMqE,UACF,6BAA6B/M,eAAiB0I,OAGtD,MAAMsE,EAAShN,EAAI0I,GACnB,MAAsB,mBAAXsE,EACAA,EAAOC,KAAKjN,GAEhBgN,CACV,EACDhC,oBAAmBA,CAAER,EAAKC,KACP,CACX,IAAMa,IAAOhB,EAASC,QAAQe,EAAGb,GACjC,IAAMa,IAAOhB,EAASC,QAAQe,EAAGb,GACjC,IAAMa,IAAOhB,EAASC,QAAQe,EAAGb,GAEjC,IAAMa,IAAOhB,EAASC,QAAQe,EAAGb,GACjCyC,OAAS5B,UAAahB,EAASC,QAAQe,EAAGb,IAC5CD,EAAIrG,UAAUqG,EAAI7F,WAGxBsG,oBAAmBA,CAAET,EAAKC,IACfD,EAAIpD,SAASjH,KAAKgN,GAAO7C,EAASC,QAAQ4C,EAAI1C,KAEzDS,kBAAAA,CAAoBV,EAAKC,GACrB,MAAM3D,EAAO0D,EAAI1E,UAAU3F,KAAK6G,GAAQsD,EAASC,QAAQvD,EAAKyD,KAK9D,OAJaH,EAASC,QAAQC,EAAIvE,OAAQwE,EAInC2C,IAAQtG,EAClB,EACDqE,wBAAAA,CAA0BX,EAAKC,GAC3B,GAAsB,eAAlBD,EAAIhH,KAAKnB,KACT,MAAM+I,YAAY,wCAEtB,MAAMiC,EAAK7C,EAAIhH,KAAKpC,KACdyC,EAAQyG,EAASC,QAAQC,EAAI/G,MAAOgH,GAE1C,OADAA,EAAK4C,GAAMxJ,EACJ4G,EAAK4C,EAChB,GCtJJ,SAASxK,EAAMyK,EAAKC,GAGhB,OAFAD,EAAMA,EAAI1G,SACN/D,KAAK0K,GACFD,CACX,CAOA,SAASE,EAASD,EAAMD,GAGpB,OAFAA,EAAMA,EAAI1G,SACN4G,QAAQD,GACLD,CACX,CAMA,MAAMG,UAAiBxM,MAInBnB,WAAAA,CAAa+D,GACT6J,MACI,8FAGJlO,KAAKmO,UAAW,EAChBnO,KAAKqE,MAAQA,EACbrE,KAAK4B,KAAO,UAChB,EAiFJ,SAASwM,EAAUC,EAAMpO,EAAMO,EAAK4B,EAAUkM,GAE1C,KAAMtO,gBAAgBoO,GAClB,IACI,OAAO,IAAIA,EAASC,EAAMpO,EAAMO,EAAK4B,EAAUkM,EAClD,CAAC,MAAOtE,GACL,IAAKA,EAAEmE,SACH,MAAMnE,EAEV,OAAOA,EAAE3F,KACb,CAGgB,iBAATgK,IACPC,EAAoBlM,EACpBA,EAAW5B,EACXA,EAAMP,EACNA,EAAOoO,EACPA,EAAO,MAEX,MAAME,EAASF,GAAwB,iBAATA,EAwB9B,GAvBAA,EAAOA,GAAQ,CAAE,EACjBrO,KAAKwO,KAAOH,EAAKG,MAAQhO,EACzBR,KAAKyO,KAAOJ,EAAKI,MAAQxO,EACzBD,KAAK0O,WAAaL,EAAKK,YAAc,QACrC1O,KAAK2O,QAAUN,EAAKM,UAAW,EAC/B3O,KAAK4O,MAAOnO,OAAO4M,OAAOgB,EAAM,SAAUA,EAAKO,KAC/C5O,KAAK6O,QAAUR,EAAKQ,SAAW,CAAE,EACjC7O,KAAK8O,UAAqB1F,IAAdiF,EAAKS,KAAqB,OAAST,EAAKS,KACpD9O,KAAK+O,sBAAqD,IAA1BV,EAAKU,kBAE/BV,EAAKU,iBACX/O,KAAKgP,OAASX,EAAKW,QAAU,KAC7BhP,KAAKiP,eAAiBZ,EAAKY,gBAAkB,KAC7CjP,KAAKoC,SAAWiM,EAAKjM,UAAYA,GAAY,KAC7CpC,KAAKsO,kBAAoBD,EAAKC,mBAC1BA,GACA,WACI,MAAM,IAAIf,UACN,mFAGP,GAEkB,IAAnBc,EAAKa,UAAqB,CAC1B,MAAM5H,EAAO,CACTmH,KAAOF,EAASF,EAAKI,KAAOxO,GAE3BsO,EAEM,SAAUF,IACjB/G,EAAKkH,KAAOH,EAAKG,MAFjBlH,EAAKkH,KAAOhO,EAIhB,MAAM2O,EAAMnP,KAAKoP,SAAS9H,GAC1B,IAAK6H,GAAsB,iBAARA,EACf,MAAM,IAAIlB,EAASkB,GAEvB,OAAOA,CACX,CACJ,CAGAf,EAASiB,UAAUD,SAAW,SAC1BnP,EAAMuO,EAAMpM,EAAUkM,GAEtB,IAAIgB,EAAatP,KAAKgP,OAClBO,EAAqBvP,KAAKiP,gBAC1BN,QAACA,EAAOC,KAAEA,GAAQ5O,KAUtB,GARAA,KAAKwP,eAAiBxP,KAAK0O,WAC3B1O,KAAKyP,SAAWzP,KAAK8O,KACrB9O,KAAK0P,YAAc1P,KAAK6O,QACxBzM,EAAWA,GAAYpC,KAAKoC,SAC5BpC,KAAK2P,sBAAwBrB,GAAqBtO,KAAKsO,kBAEvDE,EAAOA,GAAQxO,KAAKwO,MACpBvO,EAAOA,GAAQD,KAAKyO,OACQ,iBAATxO,IAAsB6H,MAAMC,QAAQ9H,GAAO,CAC1D,IAAKA,EAAKwO,MAAsB,KAAdxO,EAAKwO,KACnB,MAAM,IAAIlB,UACN,+FAIR,IAAM9M,OAAO4M,OAAOpN,EAAM,QACtB,MAAM,IAAIsN,UACN,iGAINiB,QAAQvO,GACV0O,EAAUlO,OAAO4M,OAAOpN,EAAM,WAAaA,EAAK0O,QAAUA,EAC1D3O,KAAKwP,eAAiB/O,OAAO4M,OAAOpN,EAAM,cACpCA,EAAKyO,WACL1O,KAAKwP,eACXxP,KAAK0P,YAAcjP,OAAO4M,OAAOpN,EAAM,WACjCA,EAAK4O,QACL7O,KAAK0P,YACXd,EAAOnO,OAAO4M,OAAOpN,EAAM,QAAUA,EAAK2O,KAAOA,EACjD5O,KAAKyP,SAAWhP,OAAO4M,OAAOpN,EAAM,QAC9BA,EAAK6O,KACL9O,KAAKyP,SACXrN,EAAW3B,OAAO4M,OAAOpN,EAAM,YAAcA,EAAKmC,SAAWA,EAC7DpC,KAAK2P,sBAAwBlP,OAAO4M,OAAOpN,EAAM,qBAC3CA,EAAKqO,kBACLtO,KAAK2P,sBACXL,EAAa7O,OAAO4M,OAAOpN,EAAM,UAAYA,EAAK+O,OAASM,EAC3DC,EAAqB9O,OAAO4M,OAAOpN,EAAM,kBACnCA,EAAKgP,eACLM,EACNtP,EAAOA,EAAKwO,IAChB,CAOA,GANAa,EAAaA,GAAc,KAC3BC,EAAqBA,GAAsB,KAEvCzH,MAAMC,QAAQ9H,KACdA,EAAOmO,EAASwB,aAAa3P,KAE3BA,GAAiB,KAATA,IAAiBuO,EAC3B,OAGJ,MAAMqB,EAAWzB,EAAS0B,YAAY7P,GAClB,MAAhB4P,EAAS,IAAcA,EAAStR,OAAS,GACzCsR,EAASE,QAEb/P,KAAKgQ,mBAAqB,KAC1B,MAAMxC,EAASxN,KACViQ,OACGJ,EAAUrB,EAAM,CAAC,KAAMc,EAAYC,EAAoBnN,GAE1D6G,QAAO,SAAUiH,GACd,OAAOA,IAAOA,EAAGC,gBACrB,IAEJ,OAAK3C,EAAOjP,OAGPqQ,GAA0B,IAAlBpB,EAAOjP,QAAiBiP,EAAO,GAAG4C,WAGxC5C,EAAO6C,QAAO,CAACC,EAAMJ,KACxB,MAAMK,EAAYvQ,KAAKwQ,oBAAoBN,GAM3C,OALIvB,GAAW7G,MAAMC,QAAQwI,GACzBD,EAAOA,EAAKG,OAAOF,GAEnBD,EAAKjN,KAAKkN,GAEPD,CAAI,GACZ,IAVQtQ,KAAKwQ,oBAAoBhD,EAAO,IAHhCoB,EAAO,QAAKxF,CAc3B,EAIAgF,EAASiB,UAAUmB,oBAAsB,SAAUN,GAC/C,MAAMxB,EAAa1O,KAAKwP,eACxB,OAAQd,GACR,IAAK,MAAO,CACR,MAAMD,EAAO3G,MAAMC,QAAQmI,EAAGzB,MACxByB,EAAGzB,KACHL,EAAS0B,YAAYI,EAAGzB,MAK9B,OAJAyB,EAAGQ,QAAUtC,EAASuC,UAAUlC,GAChCyB,EAAGzB,KAA0B,iBAAZyB,EAAGzB,KACdyB,EAAGzB,KACHL,EAASwB,aAAaM,EAAGzB,MACxByB,CACX,CAAE,IAAK,QAAS,IAAK,SAAU,IAAK,iBAChC,OAAOA,EAAGxB,GACd,IAAK,OACD,OAAON,EAASwB,aAAaM,EAAGxB,IACpC,IAAK,UACD,OAAON,EAASuC,UAAUT,EAAGzB,MACjC,QACI,MAAM,IAAIlB,UAAU,uBAE5B,EAEAa,EAASiB,UAAUuB,gBAAkB,SAAUC,EAAYzO,EAAUS,GACjE,GAAIT,EAAU,CACV,MAAM0O,EAAkB9Q,KAAKwQ,oBAAoBK,GACjDA,EAAWpC,KAAkC,iBAApBoC,EAAWpC,KAC9BoC,EAAWpC,KACXL,EAASwB,aAAaiB,EAAWpC,MAEvCrM,EAAS0O,EAAiBjO,EAAMgO,EACpC,CACJ,EAcAzC,EAASiB,UAAUY,OAAS,SACxBhQ,EAAMsK,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,EAAUgO,EACnDY,GAIA,IAAIC,EACJ,IAAKhR,EAAK1B,OASN,OARA0S,EAAS,CACLxC,OACApK,MAAOkG,EACPyE,SACAC,eAAgB8B,EAChBX,cAEJpQ,KAAK4Q,gBAAgBK,EAAQ7O,EAAU,SAChC6O,EAGX,MAAMC,EAAMjR,EAAK,GAAIkR,EAAIlR,EAAKmH,MAAM,GAI9B+H,EAAM,GAMZ,SAASiC,EAAQC,GACTvJ,MAAMC,QAAQsJ,GAIdA,EAAMrJ,SAASsJ,IACXnC,EAAI9L,KAAKiO,EAAE,IAGfnC,EAAI9L,KAAKgO,EAEjB,CACA,IAAoB,iBAARH,GAAoBF,IAAoBzG,GAChD9J,OAAO4M,OAAO9C,EAAK2G,GAEnBE,EAAOpR,KAAKiQ,OAAOkB,EAAG5G,EAAI2G,GAAM7N,EAAKoL,EAAMyC,GAAM3G,EAAK2G,EAAK9O,EACvDgO,SAED,GAAY,MAARc,EACPlR,KAAKuR,MAAMhH,GAAMlB,IACb+H,EAAOpR,KAAKiQ,OACRkB,EAAG5G,EAAIlB,GAAIhG,EAAKoL,EAAMpF,GAAIkB,EAAKlB,EAAGjH,GAAU,GAAM,GACpD,SAEH,GAAY,OAAR8O,EAEPE,EACIpR,KAAKiQ,OAAOkB,EAAG5G,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,EAC9CgO,IAERpQ,KAAKuR,MAAMhH,GAAMlB,IAGS,iBAAXkB,EAAIlB,IAGX+H,EAAOpR,KAAKiQ,OACRhQ,EAAKmH,QAASmD,EAAIlB,GAAIhG,EAAKoL,EAAMpF,GAAIkB,EAAKlB,EAAGjH,GAAU,GAE/D,QAID,IAAY,MAAR8O,EAGP,OADAlR,KAAKgQ,oBAAqB,EACnB,CACHvB,KAAMA,EAAKrH,MAAM,GAAI,GACrBnH,KAAMkR,EACNhB,kBAAkB,GAEnB,GAAY,MAARe,EAQP,OAPAD,EAAS,CACLxC,KAAMpL,EAAKoL,EAAMyC,GACjB7M,MAAO0M,EACP/B,SACAC,eAAgB,MAEpBjP,KAAK4Q,gBAAgBK,EAAQ7O,EAAU,YAChC6O,EACJ,GAAY,MAARC,EACPE,EAAOpR,KAAKiQ,OAAOkB,EAAG5G,EAAKkE,EAAM,KAAM,KAAMrM,EAAUgO,SACpD,GAAK,4BAA6B7G,KAAK2H,GAC1CE,EACIpR,KAAKwR,OAAON,EAAKC,EAAG5G,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,SAExD,GAA0B,IAAtB8O,EAAIO,QAAQ,MAAa,CAChC,IAAsB,IAAlBzR,KAAKyP,SACL,MAAM,IAAIhO,MAAM,oDAEpB,MAAMiQ,EAAUR,EAAIS,QAAQ,iBAAkB,MAExCC,EAAU,6CAA8CC,KAAKH,GAC/DE,EAGA5R,KAAKuR,MAAMhH,GAAMlB,IACb,MAAMyI,EAAQ,CAACF,EAAO,IAChBG,EAASH,EAAO,GAChBrH,EAAIlB,GAAGuI,EAAO,IACdrH,EAAIlB,GACYrJ,KAAKiQ,OAAO6B,EAAOC,EAAQtD,EAC7CO,EAAQ+B,EAAgB3O,GAAU,GACpB7D,OAAS,GACvB6S,EAAOpR,KAAKiQ,OAAOkB,EAAG5G,EAAIlB,GAAIhG,EAAKoL,EAAMpF,GAAIkB,EACzClB,EAAGjH,GAAU,GACrB,IAGJpC,KAAKuR,MAAMhH,GAAMlB,IACTrJ,KAAKgS,MAAMN,EAASnH,EAAIlB,GAAIA,EAAGoF,EAAMO,EACrC+B,IACAK,EAAOpR,KAAKiQ,OAAOkB,EAAG5G,EAAIlB,GAAIhG,EAAKoL,EAAMpF,GAAIkB,EAAKlB,EAC9CjH,GAAU,GAClB,GAGX,MAAM,GAAe,MAAX8O,EAAI,GAAY,CACvB,IAAsB,IAAlBlR,KAAKyP,SACL,MAAM,IAAIhO,MAAM,mDAKpB2P,EAAOpR,KAAKiQ,OAAOjC,EACfhO,KAAKgS,MACDd,EAAK3G,EAAKkE,EAAKwD,IAAI,GACnBxD,EAAKrH,MAAM,GAAI,GAAI4H,EAAQ+B,GAE/BI,GACD5G,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,EAAUgO,GACnD,MAAM,GAAe,MAAXc,EAAI,GAAY,CACvB,IAAIgB,GAAU,EACd,MAAMC,EAAYjB,EAAI9J,MAAM,GAAI,GAChC,OAAQ+K,GACR,IAAK,SACI5H,GAAS,CAAC,SAAU,YAAYpB,gBAAgBoB,KACjD2H,GAAU,GAEd,MACJ,IAAK,UAAW,IAAK,SAAU,IAAK,YAAa,IAAK,kBACvC3H,IAAQ4H,IACfD,GAAU,GAEd,MACJ,IAAK,WACGE,OAAOC,SAAS9H,IAAUA,EAAM,IAChC2H,GAAU,GAEd,MACJ,IAAK,SACGE,OAAOC,SAAS9H,KAChB2H,GAAU,GAEd,MACJ,IAAK,YACkB,iBAAR3H,GAAqB6H,OAAOC,SAAS9H,KAC5C2H,GAAU,GAEd,MACJ,IAAK,SACG3H,UAAcA,IAAQ4H,IACtBD,GAAU,GAEd,MACJ,IAAK,QACGpK,MAAMC,QAAQwC,KACd2H,GAAU,GAEd,MACJ,IAAK,QACDA,EAAUlS,KAAK2P,sBACXpF,EAAKkE,EAAMO,EAAQ+B,GAEvB,MACJ,IAAK,OACW,OAARxG,IACA2H,GAAU,GAEd,MAEJ,QACI,MAAM,IAAI3E,UAAU,sBAAwB4E,GAEhD,GAAID,EAGA,OAFAjB,EAAS,CAACxC,OAAMpK,MAAOkG,EAAKyE,SAAQC,eAAgB8B,GACpD/Q,KAAK4Q,gBAAgBK,EAAQ7O,EAAU,SAChC6O,CAGd,MAAM,GAAe,MAAXC,EAAI,IAAc3G,GAAO9J,OAAO4M,OAAO9C,EAAK2G,EAAI9J,MAAM,IAAK,CAClE,MAAMkL,EAAUpB,EAAI9J,MAAM,GAC1BgK,EAAOpR,KAAKiQ,OACRkB,EAAG5G,EAAI+H,GAAUjP,EAAKoL,EAAM6D,GAAU/H,EAAK+H,EAASlQ,EACpDgO,GAAY,GAEnB,MAAM,GAAIc,EAAI/H,SAAS,KAAM,CAC1B,MAAMoJ,EAAQrB,EAAIsB,MAAM,KACxB,IAAK,MAAMC,KAAQF,EACfnB,EAAOpR,KAAKiQ,OACRjC,EAAQyE,EAAMtB,GAAI5G,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,GACrD,GAIZ,MACK4O,GAAmBzG,GAAO9J,OAAO4M,OAAO9C,EAAK2G,IAE9CE,EACIpR,KAAKiQ,OAAOkB,EAAG5G,EAAI2G,GAAM7N,EAAKoL,EAAMyC,GAAM3G,EAAK2G,EAAK9O,EAChDgO,GAAY,GAExB,CAKA,GAAIpQ,KAAKgQ,mBACL,IAAK,IAAIsB,EAAI,EAAGA,EAAInC,EAAI5Q,OAAQ+S,IAAK,CACjC,MAAMoB,EAAOvD,EAAImC,GACjB,GAAIoB,GAAQA,EAAKvC,iBAAkB,CAC/B,MAAMwC,EAAM3S,KAAKiQ,OACbyC,EAAKzS,KAAMsK,EAAKmI,EAAKjE,KAAMO,EAAQ+B,EAAgB3O,EACnDgO,GAEJ,GAAItI,MAAMC,QAAQ4K,GAAM,CACpBxD,EAAImC,GAAKqB,EAAI,GACb,MAAMC,EAAKD,EAAIpU,OACf,IAAK,IAAIsU,EAAK,EAAGA,EAAKD,EAAIC,IAGtBvB,IACAnC,EAAI2D,OAAOxB,EAAG,EAAGqB,EAAIE,GAE7B,MACI1D,EAAImC,GAAKqB,CAEjB,CACJ,CAEJ,OAAOxD,CACX,EAEAf,EAASiB,UAAUkC,MAAQ,SAAUhH,EAAKwI,GACtC,GAAIjL,MAAMC,QAAQwC,GAAM,CACpB,MAAMyI,EAAIzI,EAAIhM,OACd,IAAK,IAAI2F,EAAI,EAAGA,EAAI8O,EAAG9O,IACnB6O,EAAE7O,EAET,MAAUqG,GAAsB,iBAARA,GACrB9J,OAAOC,KAAK6J,GAAKvC,SAASqB,IACtB0J,EAAE1J,EAAE,GAGhB,EAEA+E,EAASiB,UAAUmC,OAAS,SACxBN,EAAKjR,EAAMsK,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,GAE9C,IAAK0F,MAAMC,QAAQwC,GACf,OAEJ,MAAM0I,EAAM1I,EAAIhM,OAAQgU,EAAQrB,EAAIsB,MAAM,KACtCU,EAAQX,EAAM,IAAMH,OAAOe,SAASZ,EAAM,KAAQ,EACtD,IAAIrL,EAASqL,EAAM,IAAMH,OAAOe,SAASZ,EAAM,KAAQ,EACnDa,EAAOb,EAAM,IAAMH,OAAOe,SAASZ,EAAM,KAAQU,EACrD/L,EAASA,EAAQ,EAAK7I,KAAKC,IAAI,EAAG4I,EAAQ+L,GAAO5U,KAAKgV,IAAIJ,EAAK/L,GAC/DkM,EAAOA,EAAM,EAAK/U,KAAKC,IAAI,EAAG8U,EAAMH,GAAO5U,KAAKgV,IAAIJ,EAAKG,GACzD,MAAMjE,EAAM,GACZ,IAAK,IAAIjL,EAAIgD,EAAOhD,EAAIkP,EAAKlP,GAAKgP,EAAM,CACxBlT,KAAKiQ,OACbjC,EAAQ9J,EAAGjE,GAAOsK,EAAKkE,EAAMO,EAAQ+B,EAAgB3O,GAAU,GAO/D4F,SAASsJ,IACTnC,EAAI9L,KAAKiO,EAAE,GAEnB,CACA,OAAOnC,CACX,EAEAf,EAASiB,UAAU2C,MAAQ,SACvB5R,EAAMkT,EAAIC,EAAQ9E,EAAMO,EAAQ+B,GAEhC/Q,KAAK0P,YAAY8D,kBAAoBzC,EACrC/Q,KAAK0P,YAAY+D,UAAYzE,EAC7BhP,KAAK0P,YAAYgE,YAAcH,EAC/BvT,KAAK0P,YAAYiE,QAAU3T,KAAKwO,KAChCxO,KAAK0P,YAAYkE,KAAON,EAExB,MAAMO,EAAezT,EAAK+I,SAAS,SAC/B0K,IACA7T,KAAK0P,YAAYoE,QAAU1F,EAASwB,aAAanB,EAAKgC,OAAO,CAAC8C,MAGlE,MAAMQ,EAAiB/T,KAAKyP,SAAW,UAAYrP,EACnD,IAAKgO,EAAS4F,MAAMD,GAAiB,CACjC,IAAIE,EAAS7T,EACR8T,WAAW,kBAAmB,qBAC9BA,WAAW,UAAW,aACtBA,WAAW,YAAa,eACxBA,WAAW,QAAS,WACpBA,WAAW,eAAgB,UAIhC,GAHIL,IACAI,EAASA,EAAOC,WAAW,QAAS,YAGlB,SAAlBlU,KAAKyP,WACa,IAAlBzP,KAAKyP,eACarG,IAAlBpJ,KAAKyP,SAELrB,EAAS4F,MAAMD,GAAkB,IAAI/T,KAAKmU,OAAOC,OAAOH,QACrD,GAAsB,WAAlBjU,KAAKyP,SACZrB,EAAS4F,MAAMD,GAAkB,IAAI/T,KAAKqU,GAAGD,OAAOH,QACjD,GACsB,mBAAlBjU,KAAKyP,UACZzP,KAAKyP,SAASJ,WACd5O,OAAO4M,OAAOrN,KAAKyP,SAASJ,UAAW,mBACzC,CACE,MAAMiF,EAAWtU,KAAKyP,SACtBrB,EAAS4F,MAAMD,GAAkB,IAAIO,EAASL,EACjD,KAAM,IAA6B,mBAAlBjU,KAAKyP,SAKnB,MAAM,IAAIlC,UAAU,4BAA4BvN,KAAKyP,aAJrDrB,EAAS4F,MAAMD,GAAkB,CAC7BQ,gBAAkBvS,GAAYhC,KAAKyP,SAASwE,EAAQjS,GAI5D,CACJ,CAEA,IACI,OAAOoM,EAAS4F,MAAMD,GAAgBQ,gBAAgBvU,KAAK0P,YAC9D,CAAC,MAAO1F,GACL,GAAIhK,KAAK+O,iBACL,OAAO,EAEX,MAAM,IAAItN,MAAM,aAAeuI,EAAEzI,QAAU,KAAOnB,EACtD,CACJ,EAKAgO,EAAS4F,MAAQ,CAAE,EAMnB5F,EAASwB,aAAe,SAAU4E,GAC9B,MAAMrD,EAAIqD,EAASxB,EAAI7B,EAAE5S,OACzB,IAAIkW,EAAI,IACR,IAAK,IAAIvQ,EAAI,EAAGA,EAAI8O,EAAG9O,IACb,qBAAsBqF,KAAK4H,EAAEjN,MAC/BuQ,GAAM,aAAclL,KAAK4H,EAAEjN,IAAO,IAAMiN,EAAEjN,GAAK,IAAQ,KAAOiN,EAAEjN,GAAK,MAG7E,OAAOuQ,CACX,EAMArG,EAASuC,UAAY,SAAUD,GAC3B,MAAMS,EAAIT,EAASsC,EAAI7B,EAAE5S,OACzB,IAAIkW,EAAI,GACR,IAAK,IAAIvQ,EAAI,EAAGA,EAAI8O,EAAG9O,IACb,qBAAsBqF,KAAK4H,EAAEjN,MAC/BuQ,GAAK,IAAMtD,EAAEjN,GAAGjG,WACXiW,WAAW,IAAK,MAChBA,WAAW,IAAK,OAG7B,OAAOO,CACX,EAMArG,EAAS0B,YAAc,SAAU7P,GAC7B,MAAM+T,MAACA,GAAS5F,EAChB,GAAI4F,EAAM/T,GACN,OAAO+T,EAAM/T,GAAMwQ,SAEvB,MAAMiE,EAAO,GAoCP7E,EAnCa5P,EAEdiU,WACG,uGACA,QAIHA,WAAW,kCAAkC,SAAUS,EAAIC,GACxD,MAAO,MAAQF,EAAKrR,KAAKuR,GAAM,GAAK,GACvC,IAEAV,WAAW,2BAA2B,SAAUS,EAAIzL,GACjD,MAAO,KAAOA,EACTgL,WAAW,IAAK,OAChBA,WAAW,IAAK,UACjB,IACP,IAEAA,WAAW,IAAK,OAEhBA,WAAW,oCAAqC,KAEhDA,WAAW,MAAO,KAElBA,WAAW,SAAU,KAErBA,WAAW,uBAAuB,SAAUS,EAAIE,GAC7C,MAAO,IAAMA,EAAIrC,MAAM,IAAIsC,KAAK,KAAO,GAC1C,IAEAZ,WAAW,WAAY,QAEvBA,WAAW,eAAgB,IAEJ1B,MAAM,KAAK7R,KAAI,SAAUoU,GACjD,MAAMC,EAAQD,EAAIC,MAAM,WACxB,OAAQA,GAAUA,EAAM,GAAWN,EAAKM,EAAM,IAAjBD,CACjC,IAEA,OADAf,EAAM/T,GAAQ4P,EACPmE,EAAM/T,GAAMwQ,QACvB,EAEArC,EAASiB,UAAU8E,OAAS,CACxBC,OD1lBJ,MAII9T,WAAAA,CAAaL,GACTD,KAAKI,KAAOH,EACZD,KAAKgL,IAAM7C,EAAKnI,KAAKI,KACzB,CAOAmU,eAAAA,CAAiBvS,GAEb,MAAMiT,EAASxU,OAAOwH,OAAOxH,OAAOyU,OAAO,MAAOlT,GAClD,OAAO8I,EAASC,QAAQ/K,KAAKgL,IAAKiK,EACtC,IEnGJ7G,EAASiB,UAAUgF,GAAK,CACpBD,OA3DJ,MAII9T,WAAAA,CAAaL,GACTD,KAAKI,KAAOH,CAChB,CAOAsU,eAAAA,CAAiBvS,GACb,IAAI/B,EAAOD,KAAKI,KAChB,MAAMM,EAAOD,OAAOC,KAAKsB,GACnBmT,EAAQ,IA/BK,SAAUC,EAAQC,EAAQC,GACjD,MAAMC,EAAKH,EAAO7W,OAClB,IAAK,IAAI2F,EAAI,EAAGA,EAAIqR,EAAIrR,IAEhBoR,EADSF,EAAOlR,KAIhBmR,EAAOhS,KAAK+R,EAAOtC,OAAO5O,IAAK,GAAG,GAG9C,CAsBQsR,CAAmB9U,EAAMyU,GAAQM,GACE,mBAAjBzT,EAAQyT,KAE1B,MAAMnL,EAAS5J,EAAKC,KAAK+U,GACd1T,EAAQ0T,KAWnBzV,EARmBkV,EAAM9E,QAAO,CAACsF,EAAG/H,KAChC,IAAIgI,EAAU5T,EAAQ4L,GAAM3P,WAI5B,MAHM,YAAasL,KAAKqM,KACpBA,EAAU,YAAcA,GAErB,OAAShI,EAAO,IAAMgI,EAAU,IAAMD,CAAC,GAC/C,IAEiB1V,EAGd,sBAAuBsJ,KAAKtJ,IAAUS,EAAKyI,SAAS,eACtDlJ,EAAO,6BAA+BA,GAM1CA,EAAOA,EAAK0R,QAAQ,SAAU,IAG9B,MAAMkE,EAAmB5V,EAAK6V,YAAY,KACpC1V,GACoB,IAAtByV,EACM5V,EAAKmH,MAAM,EAAGyO,EAAmB,GACjC,WACA5V,EAAKmH,MAAMyO,EAAmB,GAC9B,WAAa5V,EAGvB,OAAO,IAAI8V,YAAYrV,EAAMN,EAAtB,IAA+BkK,EAC1C", "x_google_ignoreList": [0, 1, 2]}