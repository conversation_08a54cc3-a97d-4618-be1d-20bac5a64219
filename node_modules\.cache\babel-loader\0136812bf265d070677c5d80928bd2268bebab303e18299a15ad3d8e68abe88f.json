{"ast": null, "code": "/** This is a placeholder, not real render in dom */\nvar Option = function Option() {\n  return null;\n};\nOption.isSelectOption = true;\nexport default Option;", "map": {"version": 3, "names": ["Option", "isSelectOption"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/Option.js"], "sourcesContent": ["/** This is a placeholder, not real render in dom */\nvar Option = function Option() {\n  return null;\n};\n\nOption.isSelectOption = true;\nexport default Option;"], "mappings": "AAAA;AACA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,OAAO,IAAI;AACb,CAAC;AAEDA,MAAM,CAACC,cAAc,GAAG,IAAI;AAC5B,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}