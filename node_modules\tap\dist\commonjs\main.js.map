{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../src/main.ts"], "names": [], "mappings": ";;;;AAAA,sCAAsC;AAgBtC,oCAuBoB;AAtBlB,4FAAA,IAAI,OAAA;AAEJ,8FAAA,MAAM,OAAA;AAEN,6FAAA,KAAK,OAAA;AACL,+FAAA,OAAO,OAAA;AACP,6FAAA,KAAK,OAAA;AAGL,6FAAA,KAAK,OAAA;AAGL,+FAAA,OAAO,OAAA;AAIP,gGAAA,QAAQ,OAAA;AAGR,8FAAA,MAAM,OAAA;AAIR,oCAAkC;AAAzB,4FAAA,IAAI,OAAA;AA2DA,QAAA,CAAC,GAAQ,IAAA,UAAG,GAAE,CAAA;AAE3B,6CAA6C;AAC7C,oEAAoE;AACpE,EAAE;AACF,mEAAmE;AACnE,wDAAwD;AACxD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,EAAE;AACF,2EAA2E;AAC3E,2EAA2E;AAC3E,oCAAoC;AAEpC,uEAAuE;AACvE,mBAAmB;AACnB,MAAM,EACJ,OAAO,EACP,OAAO,EACP,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,GACL,GAAG,SAAC,CAAA;AAlFH,0BAAO;AAGP,0BAAO;AAGP,kBAAG;AAEH,oBAAI;AAkBJ,oBAAI;AACJ,oBAAI;AACJ,wBAAM;AAEN,oBAAI;AAiBJ,8BAAS;AAfT,oBAAI;AAEJ,0BAAO;AACP,oBAAI;AAgEN,wDAAwD;AACxD,iEAAiE;AACjE,MAAM,EAAE,IAAI,EAAE,GAAG,SAA+B,CAAA;AA5E9C,oBAAI;AA6EN,MAAM,EAAE,KAAK,EAAE,GAAG,SAA8B,CAAA;AAxG9C,sBAAK;AAyGP,MAAM,EAAE,MAAM,EAAE,GAAG,SAA+B,CAAA;AAtGhD,wBAAM;AAuGR,MAAM,EAAE,SAAS,EAAE,GAAG,SAAkC,CAAA;AAzGtD,8BAAS;AA0GX,MAAM,EAAE,UAAU,EAAE,GAAG,SAAmC,CAAA;AAvGxD,gCAAU;AAwGZ,MAAM,EACJ,EAAE,EACF,KAAK,EACL,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,aAAa,EACb,GAAG,EACH,MAAM,EACN,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,MAAM,EACN,YAAY,EACZ,KAAK,EACL,KAAK,GACN,GAAG,SAAgC,CAAA;AA1GlC,gBAAE;AAFF,sBAAK;AAPL,kBAAG;AAqBH,oBAAI;AAPJ,oBAAI;AANJ,0BAAO;AAQP,sCAAa;AAtBb,kBAAG;AAOH,wBAAM;AANN,8BAAS;AAOT,oCAAY;AANZ,sBAAK;AAOL,4BAAQ;AANR,8BAAS;AAOT,oCAAY;AALZ,kCAAW;AAOX,wCAAc;AARd,0CAAe;AAOf,gDAAkB;AAalB,wBAAM;AA7BN,oCAAY;AACZ,sBAAK;AAEL,sBAAK;AA4HP,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,SAE/C,CAAA;AAhGC,kCAAW;AACX,gCAAU;AACV,gCAAU;AA+FZ,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,SAEzC,CAAA;AA/FC,8BAAS;AACT,0BAAO;AAFP,8BAAS;AAiGX,MAAM,EAAE,aAAa,EAAE,GAAG,SAAiC,CAAA;AA5FzD,sCAAa;AA6Ff,MAAM,EAAE,KAAK,EAAE,GAAG,SAA8B,CAAA;AA1F9C,sBAAK;AA2FP,MAAM,EAAE,KAAK,EAAE,GAAG,SAA8B,CAAA;AA7F9C,sBAAK;AA8FP,MAAM,EAAE,MAAM,EAAE,GAAG,SAA+B,CAAA;AA3FhD,wBAAM;AA4FR,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,SAAgC,CAAA;AAlG3D,0BAAO;AACP,0BAAO", "sourcesContent": ["import { TAP, tap } from '@tapjs/core'\n\nimport type { plugin as After } from '@tapjs/after'\nimport type { plugin as AfterEach } from '@tapjs/after-each'\nimport type { plugin as Asserts } from '@tapjs/asserts'\nimport type { plugin as Before } from '@tapjs/before'\nimport type { plugin as BeforeEach } from '@tapjs/before-each'\nimport type { plugin as Filter } from '@tapjs/filter'\nimport type { plugin as Fixture } from '@tapjs/fixture'\nimport type { plugin as Intercept } from '@tapjs/intercept'\nimport type { plugin as Mock } from '@tapjs/mock'\nimport type { plugin as Snapshot } from '@tapjs/snapshot'\nimport type { plugin as Spawn } from '@tapjs/spawn'\nimport type { plugin as Stdin } from '@tapjs/stdin'\nimport type { plugin as Worker } from '@tapjs/worker'\n\nexport {\n  Base,\n  BaseOpts,\n  Counts,\n  Extra,\n  Lists,\n  Minimal,\n  Spawn,\n  SpawnEvents,\n  SpawnOpts,\n  Stdin,\n  StdinOpts,\n  TapBaseEvents,\n  TapFile,\n  TapFileEvents,\n  TapFileOpts,\n  TapPlugin,\n  TestBase,\n  TestBaseEvents,\n  TestBaseOpts,\n  Worker,\n  WorkerEvents,\n  WorkerOpts,\n} from '@tapjs/core'\nexport { Test } from '@tapjs/test'\nexport type { TestOpts } from '@tapjs/test'\nexport type { TAP }\n// export all the bound methods from our internal plugins.\nexport {\n  after,\n  afterEach,\n  bailout,\n  before,\n  beforeEach,\n  comment,\n  doesNotThrow,\n  emits,\n  end,\n  error,\n  fail,\n  has,\n  hasStrict,\n  match,\n  matchOnly,\n  matchOnlyStrict,\n  matchStrict,\n  not,\n  notHas,\n  notHasStrict,\n  notMatch,\n  notMatchOnly,\n  notMatchOnlyStrict,\n  notMatchStrict,\n  notOk,\n  notSame,\n  ok,\n  only,\n  pass,\n  plan,\n  pragma,\n  same,\n  skip,\n  strictNotSame,\n  test,\n  throws,\n  timeout,\n  todo,\n  type,\n  mockRequire,\n  mockImport,\n  createMock,\n  intercept,\n  captureFn,\n  capture,\n  testdir,\n  fixture,\n  matchSnapshot,\n  stdin,\n  stdinOnly,\n  spawn,\n  worker,\n}\n\nexport const t: TAP = tap()\n\n// People really like doing `import { test }`\n// this makes that work by exporting these methods as named exports.\n//\n// All methods on a Test object are bound to the appropriate plugin\n// as the this-context if called without a this context.\n//\n// Technically these types aren't accurate if a plugin is disabled, but\n// that's sort of what you buy into when changing types dynamically.\n//\n// Plugins other than the builtins that are added do not have their methods\n// exported here, because we can't reasonably know what they are, and named\n// exports must be explicitly named.\n\n// Methods provided by the {@link @tapjs/core!test-base.TestBase class,\n// always available\nconst {\n  bailout,\n  comment,\n  end,\n  fail,\n  pass,\n  plan,\n  pragma,\n  skip,\n  stdinOnly,\n  test,\n  timeout,\n  todo,\n} = t\n\n/**\n * If the property exists, use it, otherwise treat it as a known undefined\n */\nexport type Maybe<T, K extends symbol | number | string> =\n  K extends keyof T ? T : T & { [k in K]: undefined }\n\n/**\n * Type to make TypeScript ok with accessing an unknown property,\n * and just treating it as undefined if the plugin isn't loaded.\n */\nexport type MaybePlugin<P extends (...a: any[]) => any> = Maybe<\n  TAP,\n  keyof ReturnType<P>\n>\n\n// conditional exports, only available if plugins loaded\n// it'll just be undefined if that particular plugin is disabled.\nconst { only } = t as MaybePlugin<typeof Filter>\nconst { after } = t as MaybePlugin<typeof After>\nconst { before } = t as MaybePlugin<typeof Before>\nconst { afterEach } = t as MaybePlugin<typeof AfterEach>\nconst { beforeEach } = t as MaybePlugin<typeof BeforeEach>\nconst {\n  ok,\n  notOk,\n  not,\n  type,\n  same,\n  notSame,\n  strictNotSame,\n  has,\n  notHas,\n  hasStrict,\n  notHasStrict,\n  match,\n  notMatch,\n  matchOnly,\n  notMatchOnly,\n  matchStrict,\n  notMatchStrict,\n  matchOnlyStrict,\n  notMatchOnlyStrict,\n  throws,\n  doesNotThrow,\n  emits,\n  error,\n} = t as MaybePlugin<typeof Asserts>\nconst { mockRequire, mockImport, createMock } = t as MaybePlugin<\n  typeof Mock\n>\nconst { captureFn, capture, intercept } = t as MaybePlugin<\n  typeof Intercept\n>\nconst { matchSnapshot } = t as MaybePlugin<typeof Snapshot>\nconst { spawn } = t as MaybePlugin<typeof Spawn>\nconst { stdin } = t as MaybePlugin<typeof Stdin>\nconst { worker } = t as MaybePlugin<typeof Worker>\nconst { testdir, fixture } = t as MaybePlugin<typeof Fixture>\n"]}