{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\aggiunta file\\\\scaricaCSVProva.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ScaricaCSVProva - possibilità di scaricare un CSV di esempio prima di caricarne uno \n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { Costanti } from \"../../../components/traduttore/const\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass ScaricaCSVProva extends Component {\n  constructor(props) {\n    super(props);\n    const defaultFileType = \"csv\";\n    this.fileNames = {\n      csv: \"TMSELEZIONIDataExport.csv\"\n    };\n    this.state = {\n      fileType: defaultFileType,\n      fileDownloadUrl: null,\n      results: null\n    };\n    this.download = this.download.bind(this);\n  }\n  componentDidUpdate() {\n    /* var filter = Object.entries(this.props.results[0]).filter(el => typeof (el[1]) === 'object' && el[1] !== null) //detect element === obj\n    var baseObj = Object.entries(this.props.results[0]).filter(el => typeof (el[1]) !== 'object' || (typeof (el[1]) === 'object' && el[1] === null)) //detect element !== obj\n    console.log(filter, baseObj) \n    if(filter !== undefined){\n        \n    }*/\n    this.fileNames.csv = this.props.fileNames + '.csv';\n  }\n  download(event) {\n    if (this.props.results !== null) {\n      if (this.props.results.length > 0) {\n        event.preventDefault();\n        // Prepare the file\n        let output;\n        // Prepare data:\n        let contents = [];\n        var results = this.props.results;\n        contents.push(Object.keys(results[0]));\n        results.forEach(row => {\n          contents.push(Object.values(row));\n        });\n        output = this.makeCSV(contents);\n        // Download it\n        const blob = new Blob([output]);\n        const fileDownloadUrl = URL.createObjectURL(blob);\n        this.setState({\n          fileDownloadUrl: fileDownloadUrl\n        }, () => {\n          this.dofileDownload.click();\n          URL.revokeObjectURL(fileDownloadUrl); // free up storage--no longer needed.\n          this.setState({\n            fileDownloadUrl: \"\"\n          });\n        });\n      }\n    }\n  }\n  makeCSV(content) {\n    let csv = '';\n    content.forEach(value => {\n      value.forEach((item, i) => {\n        let innerValue = item === null || item === undefined || typeof item === 'object' ? '' : item.toString();\n        /* if (typeof (item) === 'object' && item !== null) {\n            if (item.length > 1) {\n                item.forEach(element => {\n                    Object.entries(element).forEach(obj => {\n                        if (typeof obj[1] !== 'object') {\n                            innerValue += obj[0].concat(': ').concat(obj[1]) + ' ; '\n                        } else {\n                            if (obj[1] !== null) {\n                                Object.entries(obj[1]).forEach(object => {\n                                    innerValue +=  object[0].concat(': ').concat(object[1]) + ' ; '\n                                })\n                            } else {\n                                obj[1] = 'Non disponibile'\n                                Object.entries(obj[1]).forEach(object => {\n                                    innerValue += object[0].concat(': ').concat(object[1]) + ' ; '\n                                })\n                            }\n                        }\n                    })\n                })\n            }  else {\n                Object.entries(item).forEach(obj => {\n                    if (typeof obj[1] !== 'object') {\n                        innerValue += obj[0].concat(': ').concat(obj[1]) + ' ; '\n                    } else {\n                        if (obj[1] !== null) {\n                            Object.entries(obj[1]).forEach(object => {\n                                if (typeof object[1] !== 'object') {\n                                    innerValue += object[0].concat(': ').concat(object[1]) + ' ; '\n                                } else {\n                                    if (object[1] !== null) {\n                                        Object.entries(object[1]).forEach(el => {\n                                            innerValue += el[0].concat(': ').concat(el[1]) + ' ; '\n                                        })\n                                    } else {\n                                        object[1] = 'Non disponibile'\n                                        Object.entries(object[1]).forEach(el => {\n                                            innerValue += el[0].concat(': ').concat(el[1]) + ' ; '\n                                        })\n                                    }\n                                }\n                            })\n                        }\n                    }\n                })\n            } \n        } */\n        let result = innerValue.replace(/\"/g, '\"\"');\n        if (result.search(/(\"|,|\\n)/g) >= 0) {\n          result = '\"' + result + '\"';\n        }\n        if (i > 0) {\n          csv += ';';\n        }\n        csv += result;\n      });\n      csv += '\\n';\n    });\n    return csv;\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        hidden: this.props.hidden,\n        id: \"CSVExport\",\n        className: \"ml-0 justify-content-center\",\n        onClick: this.download,\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: this.props.icon !== undefined ? this.props.icon : 'd-none'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 131\n        }, this), Costanti[this.props.label]]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        className: \"d-none\",\n        download: this.fileNames[this.state.fileType],\n        href: this.state.fileDownloadUrl,\n        ref: e => this.dofileDownload = e,\n        children: \"download it\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true);\n  }\n}\nexport default ScaricaCSVProva;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScaricaCSVProva", "constructor", "props", "defaultFileType", "fileNames", "csv", "state", "fileType", "fileDownloadUrl", "results", "download", "bind", "componentDidUpdate", "event", "length", "preventDefault", "output", "contents", "push", "Object", "keys", "for<PERSON>ach", "row", "values", "makeCSV", "blob", "Blob", "URL", "createObjectURL", "setState", "dofileDownload", "click", "revokeObjectURL", "content", "value", "item", "i", "innerValue", "undefined", "toString", "result", "replace", "search", "render", "children", "hidden", "id", "className", "onClick", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "href", "ref", "e"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/aggiunta file/scaricaCSVProva.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ScaricaCSVProva - possibilità di scaricare un CSV di esempio prima di caricarne uno \n*\n*/\nimport React, { Component } from \"react\";\nimport { <PERSON><PERSON> } from \"primereact/button\";\nimport { <PERSON><PERSON> } from \"../../../components/traduttore/const\";\n\nclass ScaricaCSVProva extends Component {\n    constructor(props) {\n        super(props);\n        const defaultFileType = \"csv\";\n        this.fileNames = {\n            csv: \"TMSELEZIONIDataExport.csv\"\n        }\n        this.state = {\n            fileType: defaultFileType,\n            fileDownloadUrl: null,\n            results: null\n        }\n        this.download = this.download.bind(this);\n    }\n    componentDidUpdate() {\n        /* var filter = Object.entries(this.props.results[0]).filter(el => typeof (el[1]) === 'object' && el[1] !== null) //detect element === obj\n        var baseObj = Object.entries(this.props.results[0]).filter(el => typeof (el[1]) !== 'object' || (typeof (el[1]) === 'object' && el[1] === null)) //detect element !== obj\n        console.log(filter, baseObj) \n        if(filter !== undefined){\n            \n        }*/\n        this.fileNames.csv = this.props.fileNames + '.csv'\n    }\n    download(event) {\n        if (this.props.results !== null) {\n            if (this.props.results.length > 0) {\n                event.preventDefault();\n                // Prepare the file\n                let output;\n                // Prepare data:\n                let contents = [];\n                var results = this.props.results\n                contents.push(Object.keys(results[0]));\n                results.forEach(row => {\n                    contents.push(Object.values(row))\n                });\n                output = this.makeCSV(contents);\n                // Download it\n                const blob = new Blob([output]);\n                const fileDownloadUrl = URL.createObjectURL(blob);\n                this.setState({ fileDownloadUrl: fileDownloadUrl },\n                    () => {\n                        this.dofileDownload.click();\n                        URL.revokeObjectURL(fileDownloadUrl);  // free up storage--no longer needed.\n                        this.setState({ fileDownloadUrl: \"\" })\n                    })\n            }\n        }\n    }\n    makeCSV(content) {\n        let csv = '';\n        content.forEach(value => {\n            value.forEach((item, i) => {\n                let innerValue = item === null || item === undefined || typeof (item) === 'object' ? '' : item.toString();\n                /* if (typeof (item) === 'object' && item !== null) {\n                    if (item.length > 1) {\n                        item.forEach(element => {\n                            Object.entries(element).forEach(obj => {\n                                if (typeof obj[1] !== 'object') {\n                                    innerValue += obj[0].concat(': ').concat(obj[1]) + ' ; '\n                                } else {\n                                    if (obj[1] !== null) {\n                                        Object.entries(obj[1]).forEach(object => {\n                                            innerValue +=  object[0].concat(': ').concat(object[1]) + ' ; '\n                                        })\n                                    } else {\n                                        obj[1] = 'Non disponibile'\n                                        Object.entries(obj[1]).forEach(object => {\n                                            innerValue += object[0].concat(': ').concat(object[1]) + ' ; '\n                                        })\n                                    }\n                                }\n                            })\n                        })\n                    }  else {\n                        Object.entries(item).forEach(obj => {\n                            if (typeof obj[1] !== 'object') {\n                                innerValue += obj[0].concat(': ').concat(obj[1]) + ' ; '\n                            } else {\n                                if (obj[1] !== null) {\n                                    Object.entries(obj[1]).forEach(object => {\n                                        if (typeof object[1] !== 'object') {\n                                            innerValue += object[0].concat(': ').concat(object[1]) + ' ; '\n                                        } else {\n                                            if (object[1] !== null) {\n                                                Object.entries(object[1]).forEach(el => {\n                                                    innerValue += el[0].concat(': ').concat(el[1]) + ' ; '\n                                                })\n                                            } else {\n                                                object[1] = 'Non disponibile'\n                                                Object.entries(object[1]).forEach(el => {\n                                                    innerValue += el[0].concat(': ').concat(el[1]) + ' ; '\n                                                })\n                                            }\n                                        }\n                                    })\n                                }\n                            }\n                        })\n                    } \n                } */\n                let result = innerValue.replace(/\"/g, '\"\"');\n                if (result.search(/(\"|,|\\n)/g) >= 0) {\n                    result = '\"' + result + '\"'\n                }\n                if (i > 0) { csv += ';' }\n                csv += result;\n            })\n            csv += '\\n';\n        })\n        return csv\n    }\n    render() {\n        return (\n            <>\n                <Button hidden={this.props.hidden} id=\"CSVExport\" className=\"ml-0 justify-content-center\" onClick={this.download}><i className={this.props.icon !== undefined ? this.props.icon : 'd-none'}></i>{Costanti[this.props.label]}</Button>\n                <a className=\"d-none\"\n                    download={this.fileNames[this.state.fileType]}\n                    href={this.state.fileDownloadUrl}\n                    ref={e => this.dofileDownload = e}\n                >download it</a>\n            </>\n        )\n    }\n}\n\nexport default ScaricaCSVProva;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhE,MAAMC,eAAe,SAASP,SAAS,CAAC;EACpCQ,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,MAAMC,eAAe,GAAG,KAAK;IAC7B,IAAI,CAACC,SAAS,GAAG;MACbC,GAAG,EAAE;IACT,CAAC;IACD,IAAI,CAACC,KAAK,GAAG;MACTC,QAAQ,EAAEJ,eAAe;MACzBK,eAAe,EAAE,IAAI;MACrBC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;EAC5C;EACAC,kBAAkBA,CAAA,EAAG;IACjB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACR,SAAS,CAACC,GAAG,GAAG,IAAI,CAACH,KAAK,CAACE,SAAS,GAAG,MAAM;EACtD;EACAM,QAAQA,CAACG,KAAK,EAAE;IACZ,IAAI,IAAI,CAACX,KAAK,CAACO,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAACP,KAAK,CAACO,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;QAC/BD,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB;QACA,IAAIC,MAAM;QACV;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIR,OAAO,GAAG,IAAI,CAACP,KAAK,CAACO,OAAO;QAChCQ,QAAQ,CAACC,IAAI,CAACC,MAAM,CAACC,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACtCA,OAAO,CAACY,OAAO,CAACC,GAAG,IAAI;UACnBL,QAAQ,CAACC,IAAI,CAACC,MAAM,CAACI,MAAM,CAACD,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC;QACFN,MAAM,GAAG,IAAI,CAACQ,OAAO,CAACP,QAAQ,CAAC;QAC/B;QACA,MAAMQ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACV,MAAM,CAAC,CAAC;QAC/B,MAAMR,eAAe,GAAGmB,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QACjD,IAAI,CAACI,QAAQ,CAAC;UAAErB,eAAe,EAAEA;QAAgB,CAAC,EAC9C,MAAM;UACF,IAAI,CAACsB,cAAc,CAACC,KAAK,CAAC,CAAC;UAC3BJ,GAAG,CAACK,eAAe,CAACxB,eAAe,CAAC,CAAC,CAAE;UACvC,IAAI,CAACqB,QAAQ,CAAC;YAAErB,eAAe,EAAE;UAAG,CAAC,CAAC;QAC1C,CAAC,CAAC;MACV;IACJ;EACJ;EACAgB,OAAOA,CAACS,OAAO,EAAE;IACb,IAAI5B,GAAG,GAAG,EAAE;IACZ4B,OAAO,CAACZ,OAAO,CAACa,KAAK,IAAI;MACrBA,KAAK,CAACb,OAAO,CAAC,CAACc,IAAI,EAAEC,CAAC,KAAK;QACvB,IAAIC,UAAU,GAAGF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKG,SAAS,IAAI,OAAQH,IAAK,KAAK,QAAQ,GAAG,EAAE,GAAGA,IAAI,CAACI,QAAQ,CAAC,CAAC;QACzG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAIC,MAAM,GAAGH,UAAU,CAACI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;QAC3C,IAAID,MAAM,CAACE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;UACjCF,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,GAAG;QAC/B;QACA,IAAIJ,CAAC,GAAG,CAAC,EAAE;UAAE/B,GAAG,IAAI,GAAG;QAAC;QACxBA,GAAG,IAAImC,MAAM;MACjB,CAAC,CAAC;MACFnC,GAAG,IAAI,IAAI;IACf,CAAC,CAAC;IACF,OAAOA,GAAG;EACd;EACAsC,MAAMA,CAAA,EAAG;IACL,oBACI9C,OAAA,CAAAE,SAAA;MAAA6C,QAAA,gBACI/C,OAAA,CAACH,MAAM;QAACmD,MAAM,EAAE,IAAI,CAAC3C,KAAK,CAAC2C,MAAO;QAACC,EAAE,EAAC,WAAW;QAACC,SAAS,EAAC,6BAA6B;QAACC,OAAO,EAAE,IAAI,CAACtC,QAAS;QAAAkC,QAAA,gBAAC/C,OAAA;UAAGkD,SAAS,EAAE,IAAI,CAAC7C,KAAK,CAAC+C,IAAI,KAAKX,SAAS,GAAG,IAAI,CAACpC,KAAK,CAAC+C,IAAI,GAAG;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAAC1D,QAAQ,CAAC,IAAI,CAACO,KAAK,CAACoD,KAAK,CAAC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACrOxD,OAAA;QAAGkD,SAAS,EAAC,QAAQ;QACjBrC,QAAQ,EAAE,IAAI,CAACN,SAAS,CAAC,IAAI,CAACE,KAAK,CAACC,QAAQ,CAAE;QAC9CgD,IAAI,EAAE,IAAI,CAACjD,KAAK,CAACE,eAAgB;QACjCgD,GAAG,EAAEC,CAAC,IAAI,IAAI,CAAC3B,cAAc,GAAG2B,CAAE;QAAAb,QAAA,EACrC;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA,eAClB,CAAC;EAEX;AACJ;AAEA,eAAerD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}