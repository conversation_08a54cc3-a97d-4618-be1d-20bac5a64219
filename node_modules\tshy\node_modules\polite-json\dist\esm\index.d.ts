/**
 * Copyright 2017 <PERSON>
 * Copyright npm, Inc.
 * Copyright 2023 <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 *
 * ---
 *
 * 'polite-json' is a fork of 'json-parse-even-better-errors',
 * extended and distributed under the terms of the MIT license
 * above.
 *
 * 'json-parse-even-better-errors' is a fork of
 * 'json-parse-better-errors' by Kat Marchán, extended and
 * distributed under the terms of the MIT license above.
 */
/// <reference types="node" resolution-mode="require"/>
export declare class JSONParseError extends SyntaxError {
    code: 'EJSONPARSE';
    cause: Error;
    position: number;
    constructor(er: Error, txt: string, context?: number, caller?: Function | ((...a: any[]) => any));
    get name(): string;
    set name(_: string);
    get [Symbol.toStringTag](): string;
}
export declare const kIndent: unique symbol;
export declare const kNewline: unique symbol;
export type Reviver = (this: any, key: string, value: any) => any;
export type Replacer = ((this: any, key: string, value: any) => any) | (string | number)[] | null;
export type Scalar = string | number | null;
export type JSONResult = {
    [k: string]: JSONResult;
    [kIndent]?: string;
    [kNewline]?: string;
} | (JSONResult[] & {
    [kIndent]?: string;
    [kNewline]?: string;
}) | Scalar;
export declare const parse: (txt: string | Buffer, reviver?: Reviver | null, context?: number) => JSONResult;
export declare const parseNoExceptions: (txt: string | Buffer, reviver?: Reviver) => any;
export declare const stringify: (obj: any, replacer?: Replacer, indent?: string | number) => string;
//# sourceMappingURL=index.d.ts.map