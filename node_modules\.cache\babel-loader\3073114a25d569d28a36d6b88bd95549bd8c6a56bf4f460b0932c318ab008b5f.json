{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\logo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport logo from '../img/aff-placeholder.png';\nimport { baseProxy } from \"./generalizzazioni/apireq\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Logo = props => {\n  _s();\n  const [getUsrRole, setGetUsrRole] = useState([]);\n  const [dataProfile, setDataProfile] = useState([]);\n  useEffect(() => {\n    if (localStorage.getItem(\"role\") !== null) {\n      setGetUsrRole(localStorage.getItem(\"role\"));\n      try {\n        const userid = localStorage.getItem(\"userid\");\n        if (userid && userid.trim() !== '') {\n          setDataProfile(JSON.parse(userid));\n        } else {\n          setDataProfile({});\n        }\n      } catch (error) {\n        console.warn('Failed to parse userid from localStorage:', error);\n        setDataProfile({});\n      }\n    }\n  }, []);\n  const onError = e => {\n    if (e.target.src.includes('png')) {\n      e.target.src = baseProxy + 'asset/logo/' + getUsrRole + \"/\" + dataProfile.id + \".jpeg\";\n    } else if (e.target.src.includes('jpeg')) {\n      e.target.src = logo;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"image-wrapper\",\n    children: /*#__PURE__*/_jsxDEV(\"img\", {\n      src: baseProxy + 'asset/logo/' + getUsrRole + \"/\" + dataProfile.id + \".png\",\n      onError: e => onError(e),\n      alt: \"Logo\",\n      width: props.width ? props.width : \"200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 9\n  }, this);\n};\n_s(Logo, \"fGm0A21RrCKMuwFNV1kiPcoxKPg=\");\n_c = Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "logo", "baseProxy", "jsxDEV", "_jsxDEV", "Logo", "props", "_s", "getUsrRole", "setGetUsrRole", "dataProfile", "setDataProfile", "localStorage", "getItem", "userid", "trim", "JSON", "parse", "error", "console", "warn", "onError", "e", "target", "src", "includes", "id", "className", "children", "alt", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/logo.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport logo from '../img/aff-placeholder.png';\nimport { baseProxy } from \"./generalizzazioni/apireq\";\n\nexport const Logo = (props) => {\n    const [getUsrRole, setGetUsrRole] = useState([])\n    const [dataProfile, setDataProfile] = useState([])\n\n    useEffect(() => {\n        if (localStorage.getItem(\"role\") !== null) {\n            setGetUsrRole(localStorage.getItem(\"role\"))\n            try {\n                const userid = localStorage.getItem(\"userid\")\n                if (userid && userid.trim() !== '') {\n                    setDataProfile(JSON.parse(userid))\n                } else {\n                    setDataProfile({})\n                }\n            } catch (error) {\n                console.warn('Failed to parse userid from localStorage:', error)\n                setDataProfile({})\n            }\n        }\n    }, [])\n\n    const onError = (e) => {\n        if (e.target.src.includes('png')) {\n            e.target.src = baseProxy + 'asset/logo/' + getUsrRole + \"/\" + dataProfile.id + \".jpeg\"\n        } else if (e.target.src.includes('jpeg')) {\n            e.target.src = logo\n        }\n    }\n\n    return (\n        <div className=\"image-wrapper\">\n            <img src={baseProxy + 'asset/logo/' + getUsrRole + \"/\" + dataProfile.id + \".png\"} onError={(e) => onError(e)} alt=\"Logo\" width={props.width ? props.width : \"200\"} />\n        </div>\n    )\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,OAAO,MAAMC,IAAI,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC3B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAElDD,SAAS,CAAC,MAAM;IACZ,IAAIa,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;MACvCJ,aAAa,CAACG,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MAC3C,IAAI;QACA,MAAMC,MAAM,GAAGF,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;QAC7C,IAAIC,MAAM,IAAIA,MAAM,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAChCJ,cAAc,CAACK,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC,CAAC;QACtC,CAAC,MAAM;UACHH,cAAc,CAAC,CAAC,CAAC,CAAC;QACtB;MACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;QACZC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEF,KAAK,CAAC;QAChEP,cAAc,CAAC,CAAC,CAAC,CAAC;MACtB;IACJ;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,OAAO,GAAIC,CAAC,IAAK;IACnB,IAAIA,CAAC,CAACC,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC9BH,CAAC,CAACC,MAAM,CAACC,GAAG,GAAGtB,SAAS,GAAG,aAAa,GAAGM,UAAU,GAAG,GAAG,GAAGE,WAAW,CAACgB,EAAE,GAAG,OAAO;IAC1F,CAAC,MAAM,IAAIJ,CAAC,CAACC,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MACtCH,CAAC,CAACC,MAAM,CAACC,GAAG,GAAGvB,IAAI;IACvB;EACJ,CAAC;EAED,oBACIG,OAAA;IAAKuB,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC1BxB,OAAA;MAAKoB,GAAG,EAAEtB,SAAS,GAAG,aAAa,GAAGM,UAAU,GAAG,GAAG,GAAGE,WAAW,CAACgB,EAAE,GAAG,MAAO;MAACL,OAAO,EAAGC,CAAC,IAAKD,OAAO,CAACC,CAAC,CAAE;MAACO,GAAG,EAAC,MAAM;MAACC,KAAK,EAAExB,KAAK,CAACwB,KAAK,GAAGxB,KAAK,CAACwB,KAAK,GAAG;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpK,CAAC;AAEd,CAAC;AAAA3B,EAAA,CAlCYF,IAAI;AAAA8B,EAAA,GAAJ9B,IAAI;AAAA,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}