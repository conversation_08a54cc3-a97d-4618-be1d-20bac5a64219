{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\freePriceList\\\\freePriceList.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* VisualizzaListini - visualizzazione listini lato affiliato\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Nav from \"../../components/navigation/Nav\";\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Caricamento from '../../utils/caricamento';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport '../../css/header.css';\nimport { basePath } from '../route';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FreePriceList = () => {\n  _s();\n  const [results, setResults] = useState(null);\n  const [results2, setResults2] = useState(null);\n  const [padre, setPadre] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function fetchData() {\n      await APIRequest('GET', 'pricelist/free').then(data => {\n        var datasource = data.data.priceListProducts;\n        setResults(datasource);\n        setResults2(datasource);\n        setLoading(false);\n        setPadre(true);\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        toast.current.show({\n          severity: 'error',\n          summary: 'Attenzione',\n          detail: \"Nessun listino disponibile. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = basePath;\n        }, 3000);\n      });\n    }\n    localStorage.setItem(\"user\", []);\n    localStorage.setItem(\"userid\", []);\n    localStorage.setItem(\"OrdineRecuperato\", '');\n    localStorage.setItem(\"Prodotti\", []);\n    localStorage.setItem(\"Cart\", []);\n    localStorage.setItem(\"PredictionFn\", []);\n    window.sessionStorage.setItem(\"Carrello\", 0);\n    window.sessionStorage.setItem(\"totCart\", \"0,00 €\");\n    localStorage.setItem(\"datiComodo\", 0);\n    localStorage.setItem(\"DatiConsegna\", '');\n    window.sessionStorage.setItem(\"Error\", '');\n    window.sessionStorage.setItem(\"CodeError\", 0);\n    window.sessionStorage.setItem(\"idWarehouse\", 0);\n    window.sessionStorage.setItem(\"PDV\", null);\n    fetchData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dataview-demo\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dataview-demo freePriceList\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {\n      visibility: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MarketplaceGen, {\n      results: results,\n      results2: results2,\n      padre: padre,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 9\n  }, this);\n};\n_s(FreePriceList, \"SEadMYd9Z88rhFgZtMzElDyiuNk=\");\n_c = FreePriceList;\nexport default FreePriceList;\nvar _c;\n$RefreshReg$(_c, \"FreePriceList\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Nav", "MarketplaceGen", "Caricamento", "APIRequest", "Toast", "basePath", "jsxDEV", "_jsxDEV", "FreePriceList", "_s", "results", "setResults", "results2", "setResults2", "padre", "set<PERSON><PERSON>", "loading", "setLoading", "toast", "fetchData", "then", "data", "datasource", "priceListProducts", "catch", "e", "_e$response", "_e$response2", "console", "log", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "setTimeout", "window", "location", "pathname", "localStorage", "setItem", "sessionStorage", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "visibility", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/freePriceList/freePriceList.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* VisualizzaListini - visualizzazione listini lato affiliato\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Nav from \"../../components/navigation/Nav\";\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Caricamento from '../../utils/caricamento';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport '../../css/header.css';\nimport { basePath } from '../route';\n\nconst FreePriceList = () => {\n    const [results, setResults] = useState(null)\n    const [results2, setResults2] = useState(null)\n    const [padre, setPadre] = useState(false)\n    const [loading, setLoading] = useState(true)\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function fetchData() {\n            await APIRequest('GET', 'pricelist/free')\n                .then(data => {\n                    var datasource = data.data.priceListProducts;\n                    setResults(datasource)\n                    setResults2(datasource)\n                    setLoading(false)\n                    setPadre(true)\n                }).catch((e) => {\n                    console.log(e)\n                    toast.current.show({ severity: 'error', summary: 'Attenzione', detail: `Nessun listino disponibile. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = basePath;\n                    }, 3000)\n                })\n        }\n        localStorage.setItem(\"user\", []);\n        localStorage.setItem(\"userid\", []);\n        localStorage.setItem(\"OrdineRecuperato\", '');\n        localStorage.setItem(\"Prodotti\", []);\n        localStorage.setItem(\"Cart\", []);\n        localStorage.setItem(\"PredictionFn\", []);\n        window.sessionStorage.setItem(\"Carrello\", 0);\n        window.sessionStorage.setItem(\"totCart\", \"0,00 €\");\n        localStorage.setItem(\"datiComodo\", 0);\n        localStorage.setItem(\"DatiConsegna\", '');\n        window.sessionStorage.setItem(\"Error\", '');\n        window.sessionStorage.setItem(\"CodeError\", 0);\n        window.sessionStorage.setItem(\"idWarehouse\", 0);\n        window.sessionStorage.setItem(\"PDV\", null)\n        fetchData()\n    }, []);\n    if (loading) {\n        return <div className=\"dataview-demo\">\n            <Toast ref={toast} />\n            <Caricamento />\n        </div>\n    }\n    return (\n        <div className=\"dataview-demo freePriceList\">\n            <Toast ref={toast} />\n            <Nav visibility={false} />\n            <MarketplaceGen results={results} results2={results2} padre={padre} loading={loading} />\n        </div>\n    )\n}\n\nexport default FreePriceList;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,cAAc,MAAM,2DAA2D;AACtF,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,sBAAsB;AAC7B,SAASC,QAAQ,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMmB,KAAK,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZ,eAAesB,SAASA,CAAA,EAAG;MACvB,MAAMhB,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,CACpCiB,IAAI,CAACC,IAAI,IAAI;QACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAACE,iBAAiB;QAC5CZ,UAAU,CAACW,UAAU,CAAC;QACtBT,WAAW,CAACS,UAAU,CAAC;QACvBL,UAAU,CAAC,KAAK,CAAC;QACjBF,QAAQ,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAC,WAAA,EAAAC,YAAA;QACZC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QACdP,KAAK,CAACY,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,YAAY;UAAEC,MAAM,mDAAAC,MAAA,CAAmD,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYL,IAAI,MAAKgB,SAAS,IAAAV,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYN,IAAI,GAAGI,CAAC,CAACa,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QACtMC,UAAU,CAAC,MAAM;UACbC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGtC,QAAQ;QACvC,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV;IACAuC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IAChCD,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAClCD,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAC5CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IACpCD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IAChCD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACxCJ,MAAM,CAACK,cAAc,CAACD,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;IAC5CJ,MAAM,CAACK,cAAc,CAACD,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC;IAClDD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;IACrCD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACxCJ,MAAM,CAACK,cAAc,CAACD,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAC1CJ,MAAM,CAACK,cAAc,CAACD,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAC7CJ,MAAM,CAACK,cAAc,CAACD,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;IAC/CJ,MAAM,CAACK,cAAc,CAACD,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1C1B,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAIH,OAAO,EAAE;IACT,oBAAOT,OAAA;MAAKwC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBACjCzC,OAAA,CAACH,KAAK;QAAC6C,GAAG,EAAE/B;MAAM;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB9C,OAAA,CAACL,WAAW;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EACV;EACA,oBACI9C,OAAA;IAAKwC,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBACxCzC,OAAA,CAACH,KAAK;MAAC6C,GAAG,EAAE/B;IAAM;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB9C,OAAA,CAACP,GAAG;MAACsD,UAAU,EAAE;IAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1B9C,OAAA,CAACN,cAAc;MAACS,OAAO,EAAEA,OAAQ;MAACE,QAAQ,EAAEA,QAAS;MAACE,KAAK,EAAEA,KAAM;MAACE,OAAO,EAAEA;IAAQ;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvF,CAAC;AAEd,CAAC;AAAA5C,EAAA,CArDKD,aAAa;AAAA+C,EAAA,GAAb/C,aAAa;AAuDnB,eAAeA,aAAa;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}