/**
* Winet e-procurement GUI
* 2020 - Viniexport.com (C)
*
* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche
*
*/

import React, { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { <PERSON>nti } from '../components/traduttore/const';
import { APIRequest } from '../components/generalizzazioni/apireq';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import '../css/modale.css';
import { Dropdown } from 'primereact/dropdown';

const AggiungiAnagrafica = (props) => {
    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati
    const [results, setResults] = useState('');
    const [results2, setResults2] = useState('');
    const [results3, setResults3] = useState('');
    const [results4, setResults4] = useState('');
    const [results5, setResults5] = useState('');
    const [results6, setResults6] = useState('');
    const [results7, setResults7] = useState('');
    const [results8, setResults8] = useState('');
    const [results9, setResults9] = useState('');
    const [results10, setResults10] = useState(null);
    const [modPag, setModPag] = useState(null);
    const [isLookingUp, setIsLookingUp] = useState(false);
    const toast = useRef(null);
    //Setto le variabili di stato con il valore di props mediante useEffect
    useEffect(() => {
        async function renderString() {
            if (props.result !== undefined) {
                setResults(props.result.firstName)
                setResults2(props.result.idRetailer.idRegistry.email)
                setResults3(props.result.idRetailer.idRegistry.tel)
                setResults4(props.result.idRetailer.idRegistry.pIva)
                setResults5(props.result.idRetailer.idRegistry.address)
                setResults6(props.result.idRetailer.idRegistry.city)
                setResults7(props.result.idRetailer.idRegistry.cap)
                setResults8(props.result.idRetailer.idRegistry.lastName)
                setResults9(props.result.idRetailer.idRegistry.tel)
                setResults10(props.result.idRetailer.idRegistry.paymentMetod)
            }
            //Chiamata axios per la visualizzazione dei registry
            await APIRequest('GET', 'paymentmethods/')
                .then(res => {
                    var pm = []
                    res.data.forEach(element => {
                        var x = {
                            name: element.description,
                            code: element.description
                        }
                        pm.push(x)
                    });
                    setModPag(pm)
                }).catch((e) => {
                    console.log(e)
                })
        }
        renderString();
    }, [props.result]);
    // Funzione di validazione
    const validateForm = () => {
        const errors = [];

        // Validazione campi obbligatori
        if (!results || results.trim() === '') {
            errors.push('📝 Nome è obbligatorio');
        }
        if (!results8 || results8.trim() === '') {
            errors.push('📝 Cognome è obbligatorio');
        }
        if (!results4 || results4.trim() === '') {
            errors.push('📝 Partita IVA è obbligatoria');
        }

        // Validazione formato P.IVA (11 cifre)
        if (results4 && !/^\d{11}$/.test(results4.replace(/\s/g, ''))) {
            errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');
        }

        // Validazione email obbligatoria
        if (!results2 || results2.trim() === '') {
            errors.push('📧 Email è obbligatoria');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(results2)) {
            errors.push('📧 Indirizzo email non valido');
        }

        // Validazione contatti: almeno uno tra telefono e cellulare
        const hasTelefono = results3 && results3.trim() !== '';
        const hasCellulare = results9 && results9.trim() !== '';

        if (!hasTelefono && !hasCellulare) {
            errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');
        }

        // Validazione formato telefono (se presente)
        if (results3 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results3)) {
            errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');
        }

        // Validazione formato cellulare (se presente)
        if (results9 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results9)) {
            errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');
        }

        return errors;
    };

    // Funzione per il lookup automatico tramite P.IVA
    const lookupPIva = async () => {
        if (!results4 || !/^\d{11}$/.test(results4.replace(/\s/g, ''))) {
            toast.current.show({
                severity: 'warn',
                summary: '⚠️ P.IVA non valida',
                detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',
                life: 4000
            });
            return;
        }

        setIsLookingUp(true);

        try {
            // Chiamata al nuovo endpoint per lookup P.IVA
            const vatNumber = results4.replace(/\s/g, '');
            const response = await APIRequest('GET', `company-lookup?vat=IT${vatNumber}`);

            if (response.data && response.data.company) {
                const data = response.data.company;

                // Popola automaticamente i campi con i dati trovati dal servizio VIES
                if (data.name) setResults(data.name);
                if (data.address) {
                    // Estrae città e indirizzo se forniti insieme
                    const addressParts = data.address.split(',');
                    if (addressParts.length > 1) {
                        setResults5(addressParts[0].trim()); // Indirizzo
                        setResults6(addressParts[addressParts.length - 1].trim()); // Città
                    } else {
                        setResults5(data.address);
                    }
                }
                if (data.city) setResults6(data.city);
                if (data.postalCode) setResults7(data.postalCode);

                toast.current.show({
                    severity: 'success',
                    summary: '✅ Azienda trovata',
                    detail: `Dati recuperati automaticamente per ${data.name}. Verificare e completare le informazioni mancanti.`,
                    life: 5000
                });
            } else {
                throw new Error('Nessun risultato trovato');
            }
        } catch (error) {
            console.error('❌ Errore lookup P.IVA:', error);

            const errorStatus = error.response?.status;
            const errorMessage = error.response?.data?.message || error.message;

            if (errorStatus === 404) {
                toast.current.show({
                    severity: 'info',
                    summary: '🔍 Nessun risultato',
                    detail: 'Nessuna azienda trovata con questa Partita IVA. Compilare manualmente i campi.',
                    life: 4000
                });
            } else if (errorStatus === 503) {
                toast.current.show({
                    severity: 'warn',
                    summary: '⏱️ Servizio non disponibile',
                    detail: 'Il servizio di lookup P.IVA è temporaneamente non disponibile. Compilare manualmente i campi.',
                    life: 5000
                });
            } else {
                toast.current.show({
                    severity: 'warn',
                    summary: '⚠️ Lookup non disponibile',
                    detail: 'Il servizio di ricerca automatica non è al momento disponibile. Compilare manualmente i campi.',
                    life: 4000
                });
            }
        } finally {
            setIsLookingUp(false);
        }
    };

    const Invia = async () => {
        // Validazione frontend
        const validationErrors = validateForm();
        if (validationErrors.length > 0) {
            toast.current.show({
                severity: 'warn',
                summary: '⚠️ Dati mancanti o non validi',
                detail: validationErrors.join('. '),
                life: 5000
            });
            return;
        }

        // Verifica che tutti i campi obbligatori siano compilati
        const hasTelefono = results3 && results3.trim() !== '';
        const hasCellulare = results9 && results9.trim() !== '';

        if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {
            var corpo = {
                firstName: results,
                lastName: results8,
                email: results2,
                telnum: results3,
                cellnum: results9,
                pIva: results4,
                address: results5,
                city: results6,
                cap: results7,
                paymentMetod: results10?.name || ''
            }
            //Chiamata axios per la creazione del registry
            await APIRequest('POST', 'registry/', corpo)
                .then(res => {
                    console.log(res.data);
                    toast.current.show({
                        severity: 'success',
                        summary: '✅ Successo',
                        detail: "L'anagrafica è stata inserita con successo",
                        life: 3000
                    });
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                }).catch((e) => {
                    console.error('❌ Errore creazione anagrafica:', e);

                    // Gestione specifica degli errori
                    const errorStatus = e.response?.status;
                    const errorData = e.response?.data;
                    const errorMessage = e.response?.data?.message || e.message;

                    let userMessage = '';
                    let summary = 'Errore';

                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||
                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {
                        // Violazione unique constraint (P.IVA duplicata)
                        summary = '⚠️ Partita IVA già presente';
                        userMessage = `La Partita IVA "${results4}" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;
                    } else if (errorStatus === 400) {
                        // Errori di validazione
                        summary = '📝 Dati non validi';
                        if (errorMessage.toLowerCase().includes('email')) {
                            userMessage = 'L\'indirizzo email inserito non è valido. Verificare il formato.';
                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {
                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';
                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {
                            userMessage = 'Il numero di telefono inserito non è valido.';
                        } else {
                            userMessage = `Dati inseriti non validi: ${errorMessage}`;
                        }
                    } else if (errorStatus === 422) {
                        // Errori di business logic
                        summary = '🚫 Operazione non consentita';
                        userMessage = `Impossibile completare l'operazione: ${errorMessage}`;
                    } else if (errorStatus === 500 || errorStatus === 501) {
                        // Errori del server
                        summary = '🔧 Errore del server';
                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\'assistenza tecnica.';
                    } else if (errorStatus === 503) {
                        // Servizio non disponibile
                        summary = '⏱️ Servizio temporaneamente non disponibile';
                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';
                    } else if (!e.response) {
                        // Errori di rete
                        summary = '🌐 Errore di connessione';
                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
                    } else {
                        // Altri errori
                        summary = '❌ Errore imprevisto';
                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
                    }

                    toast.current.show({
                        severity: 'error',
                        summary: summary,
                        detail: userMessage,
                        life: 6000
                    });

                    // Log dettagliato per debugging
                    console.error('Dettagli errore:', {
                        status: errorStatus,
                        data: errorData,
                        message: errorMessage,
                        fullError: e
                    });
                })
        } else {
            toast.current.show({
                severity: 'warn',
                summary: '📝 Campi obbligatori mancanti',
                detail: "Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono",
                life: 6000
            });
        }
    };
    return (
        <div className="modalBody">
            <Toast ref={toast} />
            <div className="p-grid p-fluid">
                {/* Sezione Ricerca Automatica P.IVA */}
                <div className="p-col-12">
                    <div className="p-card p-p-3" style={{backgroundColor: '#f8f9fa', border: '1px solid #dee2e6', borderRadius: '8px', marginBottom: '20px'}}>
                        <h5 style={{color: '#495057', marginBottom: '10px'}}>
                            <i className="pi pi-search" style={{marginRight: '8px'}}></i>
                            Ricerca Automatica Dati Azienda
                        </h5>
                        <p style={{color: '#6c757d', fontSize: '14px', marginBottom: '15px'}}>
                            Inserisci la Partita IVA per compilare automaticamente i dati azienda
                        </p>

                        <div className="p-col-12 p-md-6" style={{padding: '0'}}>
                            <h6>{Costanti.pIva} <span style={{color: 'red'}}>*</span></h6>
                            <div className="p-inputgroup">
                                <span className="p-inputgroup-addon">
                                    <i className="pi pi-credit-card"></i>
                                </span>
                                <InputText
                                    type='text'
                                    value={results4}
                                    onChange={(e) => {
                                        const newValue = e.target.value;
                                        setResults4(newValue);

                                        // Auto-lookup quando P.IVA è valida (11 cifre)
                                        if (/^\d{11}$/.test(newValue.replace(/\s/g, ''))) {
                                            // Debounce per evitare troppe chiamate
                                            setTimeout(() => {
                                                if (results4 === newValue) { // Verifica che il valore non sia cambiato
                                                    lookupPIva();
                                                }
                                            }, 500);
                                        }
                                    }}
                                    keyfilter={/^[\d\s]+$/}
                                    placeholder="Inserisci partita IVA (11 cifre) - ricerca automatica"
                                    editable='true'
                                    maxLength={11}
                                    className={!results4 || results4.trim() === '' || !/^\d{11}$/.test(results4.replace(/\s/g, '')) ? 'p-invalid' : ''}
                                />
                                <Button
                                    type="button"
                                    icon={isLookingUp ? "pi pi-spin pi-spinner" : "pi pi-search"}
                                    className="p-button-outlined p-button-secondary"
                                    onClick={lookupPIva}
                                    disabled={isLookingUp || !results4 || !/^\d{11}$/.test(results4.replace(/\s/g, ''))}
                                    tooltip="Cerca automaticamente i dati aziendali"
                                    tooltipOptions={{position: 'top'}}
                                />
                            </div>
                            {results4 && !/^\d{11}$/.test(results4.replace(/\s/g, '')) && (
                                <small className="p-error">La Partita IVA deve contenere esattamente 11 cifre</small>
                            )}
                            <small className="p-text-secondary">
                                <i className="pi pi-info-circle"></i> Inserisci una P.IVA valida (11 cifre) per la ricerca automatica dei dati aziendali
                            </small>
                        </div>
                    </div>
                </div>

                {/* Sezione Dati Obbligatori */}
                <div className="p-col-12">
                    <h5 style={{color: '#495057', marginBottom: '15px'}}>
                        <i className="pi pi-user" style={{marginRight: '8px'}}></i>
                        Dati Obbligatori
                    </h5>
                </div>

                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Nome} <span style={{color: 'red'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-user"></i>
                        </span>
                        <InputText
                            type='text'
                            value={results}
                            onChange={(e) => setResults(e.target.value)}
                            keyfilter={/^[^#<>*!]+$/}
                            placeholder="Inserisci nome (obbligatorio)"
                            editable='true'
                            className={!results || results.trim() === '' ? 'p-invalid' : ''}
                        />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Cognome} <span style={{color: 'red'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-user"></i>
                        </span>
                        <InputText
                            type='text'
                            value={results8}
                            onChange={(e) => setResults8(e.target.value)}
                            keyfilter={/^[^#<>*!]+$/}
                            placeholder="Inserisci cognome (obbligatorio)"
                            editable='true'
                            className={!results8 || results8.trim() === '' ? 'p-invalid' : ''}
                        />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Email} <span style={{color: 'red'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-envelope"></i>
                        </span>
                        <InputText
                            type="email"
                            value={results2}
                            onChange={(e) => setResults2(e.target.value)}
                            placeholder="Inserisci email (obbligatoria)"
                            editable='true'
                            className={!results2 || results2.trim() === '' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(results2) ? 'p-invalid' : ''}
                        />
                    </div>
                    {(!results2 || results2.trim() === '') && (
                        <small className="p-error">Email è obbligatoria</small>
                    )}
                    {results2 && results2.trim() !== '' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(results2) && (
                        <small className="p-error">Formato email non valido</small>
                    )}
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Tel} <span style={{color: 'orange'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-phone"></i>
                        </span>
                        <InputText
                            type="tel"
                            value={results3}
                            onChange={(e) => setResults3(e.target.value)}
                            keyfilter={/^[\d\s\-\(\)\+]+$/}
                            placeholder="Inserisci telefono fisso"
                            editable='true'
                            className={results3 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results3) ? 'p-invalid' : ''}
                        />
                    </div>
                    {results3 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results3) && (
                        <small className="p-error">Formato telefono non valido</small>
                    )}
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Cell} <span style={{color: 'orange'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-mobile"></i>
                        </span>
                        <InputText
                            type="tel"
                            value={results9}
                            onChange={(e) => setResults9(e.target.value)}
                            keyfilter={/^[\d\s\-\(\)\+]+$/}
                            placeholder="Inserisci cellulare"
                            editable='true'
                            className={results9 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results9) ? 'p-invalid' : ''}
                        />
                    </div>
                    {results9 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results9) && (
                        <small className="p-error">Formato cellulare non valido</small>
                    )}
                </div>

                {/* Messaggio informativo per i contatti */}
                {(!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && (
                    <div className="p-col-12">
                        <small className="p-error">
                            <i className="pi pi-info-circle"></i> Inserire almeno un numero di telefono (fisso o cellulare)
                        </small>
                    </div>
                )}

                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Indirizzo}</h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-directions"></i>
                        </span>
                        <InputText type='text' value={results5} onChange={(e) => setResults5(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder="Inserisci indirizzo" editable='true' />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Città}</h6>
                    <div className="p-inputgroup">
                        <InputText type='text' value={results6} onChange={(e) => setResults6(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder="Inserisci città" editable='true' />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.CodPost}</h6>
                    <div className="p-inputgroup">
                        <InputText type='text' value={results7} onChange={(e) => setResults7(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder="Inserisci C.A.P." editable='true' />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Pagamento}</h6>
                    <div className="p-inputgroup">
                        <Dropdown className='w-100' value={results10} options={modPag} onChange={(e) => setResults10(e.target.value)} optionLabel="name" placeholder="Seleziona metodo di pagamento" filter filterBy='name' />
                    </div>
                </div>
            </div>
            <div className="d-flex justify-content-center mb-2 mt-4">
                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}
                <Button id="invia" className="p-button saveList justify-content-center float-right ionicon mx-0 w-50" onClick={Invia}>{Costanti.salva}</Button>
            </div>
        </div>
    );
}

export default AggiungiAnagrafica;
