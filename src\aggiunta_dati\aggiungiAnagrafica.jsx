/**
* Winet e-procurement GUI
* 2020 - Viniexport.com (C)
*
* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche
*
*/

import React, { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { <PERSON>nti } from '../components/traduttore/const';
import { APIRequest } from '../components/generalizzazioni/apireq';
import axios from 'axios';
import { baseURL, baseProxy } from '../components/generalizzazioni/apireq';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import '../css/modale.css';
import { Dropdown } from 'primereact/dropdown';

const AggiungiAnagrafica = (props) => {
    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati
    const [results, setResults] = useState('');
    const [results2, setResults2] = useState('');
    const [results3, setResults3] = useState('');
    const [results4, setResults4] = useState('');
    const [results5, setResults5] = useState('');
    const [results6, setResults6] = useState('');
    const [results7, setResults7] = useState('');
    const [results8, setResults8] = useState('');
    const [results9, setResults9] = useState('');
    const [results10, setResults10] = useState(null);
    const [modPag, setModPag] = useState(null);
    const [isLookingUp, setIsLookingUp] = useState(false);
    const [lookupStatus, setLookupStatus] = useState(null); // 'success', 'error', 'not_found'
    const [isCompanyLookup, setIsCompanyLookup] = useState(false); // true se è stata trovata un'azienda via P.IVA
    const toast = useRef(null);
    //Setto le variabili di stato con il valore di props mediante useEffect
    useEffect(() => {
        async function renderString() {
            if (props.result !== undefined) {
                setResults(props.result.firstName)
                setResults2(props.result.idRetailer.idRegistry.email)
                setResults3(props.result.idRetailer.idRegistry.tel)
                setResults4(props.result.idRetailer.idRegistry.pIva)
                setResults5(props.result.idRetailer.idRegistry.address)
                setResults6(props.result.idRetailer.idRegistry.city)
                setResults7(props.result.idRetailer.idRegistry.cap)
                setResults8(props.result.idRetailer.idRegistry.lastName)
                setResults9(props.result.idRetailer.idRegistry.tel)
                setResults10(props.result.idRetailer.idRegistry.paymentMetod)
            }
            //Chiamata axios per la visualizzazione dei registry
            await APIRequest('GET', 'paymentmethods/')
                .then(res => {
                    var pm = []
                    res.data.forEach(element => {
                        var x = {
                            name: element.description,
                            code: element.description
                        }
                        pm.push(x)
                    });
                    setModPag(pm)
                }).catch((e) => {
                    console.log(e)
                })
        }
        renderString();
    }, [props.result]);
    // Funzione di validazione
    const validateForm = () => {
        const errors = [];

        // Validazione campi obbligatori
        if (!results || results.trim() === '') {
            errors.push('📝 Nome è obbligatorio');
        }
        // Rileva se è un'azienda anche senza lookup (per coerenza con modifica)
        const hasCompanyPIva = results4 && /^\d{11}$/.test(results4.replace(/\s/g, ''));
        const hasCompanyName = results && (
            results.toUpperCase().includes('SRL') ||
            results.toUpperCase().includes('S.R.L.') ||
            results.toUpperCase().includes('SPA') ||
            results.toUpperCase().includes('S.P.A.') ||
            results.toUpperCase().includes('SNCA') ||
            results.toUpperCase().includes('SAS') ||
            results.toUpperCase().includes('SNC') ||
            results.toUpperCase().includes('SOCIETÀ') ||
            results.toUpperCase().includes('COMPANY') ||
            results.toUpperCase().includes('SERVICES') ||
            results.toUpperCase().includes('TRADING') ||
            results.toUpperCase().includes('BROKER') ||
            results.toUpperCase().includes('GROUP') ||
            results.toUpperCase().includes('HOLDING') ||
            results.toUpperCase().includes('CORPORATION') ||
            results.toUpperCase().includes('CORP') ||
            results.toUpperCase().includes('LTD') ||
            results.toUpperCase().includes('LIMITED') ||
            results.toUpperCase().includes('INC') ||
            results.length > 30
        );

        const isDetectedCompany = hasCompanyPIva || hasCompanyName;

        // Cognome obbligatorio solo se non è un lookup aziendale E non è rilevata come azienda
        if (!isCompanyLookup && !isDetectedCompany && (!results8 || results8.trim() === '')) {
            errors.push('📝 Cognome è obbligatorio');
        }
        if (!results4 || results4.trim() === '') {
            errors.push('📝 Partita IVA è obbligatoria');
        }

        // Validazione formato P.IVA (11 cifre)
        if (results4 && !/^\d{11}$/.test(results4.replace(/\s/g, ''))) {
            errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');
        }

        // Validazione email obbligatoria
        if (!results2 || results2.trim() === '') {
            errors.push('📧 Email è obbligatoria');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(results2)) {
            errors.push('📧 Indirizzo email non valido');
        }

        // Validazione contatti: almeno uno tra telefono e cellulare
        const hasTelefono = results3 && results3.trim() !== '';
        const hasCellulare = results9 && results9.trim() !== '';

        if (!hasTelefono && !hasCellulare) {
            errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');
        }

        // Validazione formato telefono (se presente)
        if (results3 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results3)) {
            errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');
        }

        // Validazione formato cellulare (se presente)
        if (results9 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results9)) {
            errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');
        }

        return errors;
    };

    // Funzione per il lookup automatico tramite P.IVA
    const lookupPIva = async () => {
        if (!results4 || !/^\d{11}$/.test(results4.replace(/\s/g, ''))) {
            toast.current.show({
                severity: 'warn',
                summary: '⚠️ P.IVA non valida',
                detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',
                life: 4000
            });
            return;
        }

        setIsLookingUp(true);
        setLookupStatus(null); // Reset status all'inizio della ricerca
        setIsCompanyLookup(false); // Reset flag aziendale

        try {
            // Chiamata diretta per evitare il redirect automatico su errore 500
            const vatNumber = results4.replace(/\s/g, '');

            // Costruisce URL come fa APIRequest
            const cleanURL = baseURL.replace(/\/$/, '');
            const cleanProxy = baseProxy.replace(/^\/|\/$/g, '');
            const cleanPath = `company-lookup?vat=IT${vatNumber}`;

            let fullURL = cleanURL;
            if (cleanProxy) fullURL += '/' + cleanProxy;
            if (cleanPath) fullURL += '/' + cleanPath;

            // Chiamata diretta con axios per controllo completo degli errori
            const response = await axios({
                method: 'GET',
                url: fullURL,
                headers: {
                    auth: localStorage.getItem('login_token'),
                    accept: "application/json",
                    "Content-Type": "application/json"
                },
                timeout: 10000
            });

            if (response.data && response.data.company) {
                const data = response.data.company;

                // Popola automaticamente i campi con i dati trovati dal servizio VIES
                if (data.name) {
                    setResults(data.name); // Nome = Ragione Sociale
                    setResults8(''); // Cognome vuoto per aziende
                    setIsCompanyLookup(true); // Marca come lookup aziendale
                }
                if (data.address) {
                    // Estrae città e indirizzo se forniti insieme
                    const addressParts = data.address.split(',');
                    if (addressParts.length > 1) {
                        setResults5(addressParts[0].trim()); // Indirizzo
                        setResults6(addressParts[addressParts.length - 1].trim()); // Città
                    } else {
                        setResults5(data.address);
                    }
                }
                if (data.city) setResults6(data.city);
                if (data.postalCode) setResults7(data.postalCode);

                setLookupStatus('success');
                toast.current.show({
                    severity: 'success',
                    summary: '✅ Azienda trovata',
                    detail: `Dati recuperati automaticamente per ${data.name}. Il campo "Cognome" non è richiesto per le aziende.`,
                    life: 6000
                });
            } else {
                throw new Error('Nessun risultato trovato');
            }
        } catch (error) {
            console.error('❌ Errore lookup P.IVA:', error);

            // Previeni il refresh della pagina gestendo l'errore localmente
            const errorStatus = error.response?.status;
            const errorData = error.response?.data;
            const errorCode = errorData?.error?.code;

            // Gestione specifica per diversi tipi di errore
            if (errorStatus === 401) {
                setLookupStatus('error');
                toast.current.show({
                    severity: 'warn',
                    summary: '🔐 Sessione scaduta',
                    detail: 'La sessione è scaduta. Ricarica la pagina per effettuare nuovamente il login, oppure continua con la compilazione manuale.',
                    life: 8000
                });
            } else if (errorStatus === 404 || errorCode === 'VAT_NOT_FOUND') {
                setLookupStatus('not_found');
                toast.current.show({
                    severity: 'info',
                    summary: '🔍 Azienda non trovata',
                    detail: 'Nessuna azienda trovata con questa Partita IVA. Puoi continuare con la compilazione manuale dei campi.',
                    life: 6000
                });
            } else if (errorStatus === 503 || errorCode === 'SERVICE_UNAVAILABLE') {
                setLookupStatus('error');
                toast.current.show({
                    severity: 'warn',
                    summary: '⏱️ Servizio temporaneamente non disponibile',
                    detail: 'Il servizio di ricerca P.IVA non è al momento raggiungibile. Puoi continuare con la compilazione manuale.',
                    life: 6000
                });
            } else if (errorStatus === 400 || errorCode === 'INVALID_VAT_FORMAT') {
                setLookupStatus('error');
                toast.current.show({
                    severity: 'error',
                    summary: '❌ Formato P.IVA non valido',
                    detail: 'Il formato della Partita IVA inserita non è corretto. Verificare e riprovare.',
                    life: 5000
                });
            } else if (errorStatus === 500 || errorCode === 'LOOKUP_ERROR') {
                setLookupStatus('error');
                toast.current.show({
                    severity: 'warn',
                    summary: '⚠️ Errore temporaneo',
                    detail: 'Si è verificato un errore temporaneo durante la ricerca. Puoi continuare con la compilazione manuale o riprovare più tardi.',
                    life: 6000
                });
            } else {
                // Errore generico - non bloccare l'utente
                setLookupStatus('error');
                toast.current.show({
                    severity: 'warn',
                    summary: '⚠️ Ricerca non disponibile',
                    detail: 'La ricerca automatica non è al momento disponibile. Puoi continuare con la compilazione manuale dei campi.',
                    life: 5000
                });
            }

            // Non propagare l'errore per evitare il refresh della pagina
            // L'utente può continuare con la compilazione manuale
        } finally {
            setIsLookingUp(false);
        }
    };

    const Invia = async () => {
        // Validazione frontend
        const validationErrors = validateForm();
        if (validationErrors.length > 0) {
            toast.current.show({
                severity: 'warn',
                summary: '⚠️ Dati mancanti o non validi',
                detail: validationErrors.join('. '),
                life: 5000
            });
            return;
        }

        // Verifica che tutti i campi obbligatori siano compilati
        const hasTelefono = results3 && results3.trim() !== '';
        const hasCellulare = results9 && results9.trim() !== '';

        // Validazione: cognome richiesto solo se non è lookup aziendale E non è rilevata come azienda
        const hasCompanyPIva = results4 && /^\d{11}$/.test(results4.replace(/\s/g, ''));
        const hasCompanyName = results && (
            results.toUpperCase().includes('SRL') ||
            results.toUpperCase().includes('S.R.L.') ||
            results.toUpperCase().includes('SPA') ||
            results.toUpperCase().includes('S.P.A.') ||
            results.toUpperCase().includes('SNCA') ||
            results.toUpperCase().includes('SAS') ||
            results.toUpperCase().includes('SNC') ||
            results.toUpperCase().includes('SOCIETÀ') ||
            results.toUpperCase().includes('COMPANY') ||
            results.toUpperCase().includes('SERVICES') ||
            results.toUpperCase().includes('TRADING') ||
            results.toUpperCase().includes('BROKER') ||
            results.toUpperCase().includes('GROUP') ||
            results.toUpperCase().includes('HOLDING') ||
            results.toUpperCase().includes('CORPORATION') ||
            results.toUpperCase().includes('CORP') ||
            results.toUpperCase().includes('LTD') ||
            results.toUpperCase().includes('LIMITED') ||
            results.toUpperCase().includes('INC') ||
            results.length > 30
        );

        const isDetectedCompany = hasCompanyPIva || hasCompanyName;
        const cognomeValido = isCompanyLookup || isDetectedCompany || (results8 && results8.trim() !== '');

        if (results && cognomeValido && results4 && results2 && (hasTelefono || hasCellulare)) {
            var corpo = {
                firstName: results,
                lastName: results8,
                email: results2,
                telnum: results3,
                cellnum: results9,
                pIva: results4,
                address: results5,
                city: results6,
                cap: results7,
                paymentMetod: results10?.name || ''
            }
            //Chiamata axios per la creazione del registry
            await APIRequest('POST', 'registry/', corpo)
                .then(res => {
                    console.log(res.data);
                    toast.current.show({
                        severity: 'success',
                        summary: '✅ Successo',
                        detail: "L'anagrafica è stata inserita con successo",
                        life: 3000
                    });
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                }).catch((e) => {
                    console.error('❌ Errore creazione anagrafica:', e);

                    // Gestione specifica degli errori
                    const errorStatus = e.response?.status;
                    const errorData = e.response?.data;
                    const errorMessage = e.response?.data?.message || e.message;

                    let userMessage = '';
                    let summary = 'Errore';

                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||
                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {
                        // Violazione unique constraint (P.IVA duplicata)
                        summary = '⚠️ Partita IVA già presente';
                        userMessage = `La Partita IVA "${results4}" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;
                    } else if (errorStatus === 400) {
                        // Errori di validazione
                        summary = '📝 Dati non validi';
                        if (errorMessage.toLowerCase().includes('email')) {
                            userMessage = 'L\'indirizzo email inserito non è valido. Verificare il formato.';
                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {
                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';
                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {
                            userMessage = 'Il numero di telefono inserito non è valido.';
                        } else {
                            userMessage = `Dati inseriti non validi: ${errorMessage}`;
                        }
                    } else if (errorStatus === 422) {
                        // Errori di business logic
                        summary = '🚫 Operazione non consentita';
                        userMessage = `Impossibile completare l'operazione: ${errorMessage}`;
                    } else if (errorStatus === 500 || errorStatus === 501) {
                        // Errori del server
                        summary = '🔧 Errore del server';
                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\'assistenza tecnica.';
                    } else if (errorStatus === 503) {
                        // Servizio non disponibile
                        summary = '⏱️ Servizio temporaneamente non disponibile';
                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';
                    } else if (!e.response) {
                        // Errori di rete
                        summary = '🌐 Errore di connessione';
                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
                    } else {
                        // Altri errori
                        summary = '❌ Errore imprevisto';
                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
                    }

                    toast.current.show({
                        severity: 'error',
                        summary: summary,
                        detail: userMessage,
                        life: 6000
                    });

                    // Log dettagliato per debugging
                    console.error('Dettagli errore:', {
                        status: errorStatus,
                        data: errorData,
                        message: errorMessage,
                        fullError: e
                    });
                })
        } else {
            const campiRichiesti = (isCompanyLookup || isDetectedCompany)
                ? "Nome, Partita IVA, Email e almeno un numero di telefono"
                : "Nome, Cognome, Partita IVA, Email e almeno un numero di telefono";

            toast.current.show({
                severity: 'warn',
                summary: '📝 Campi obbligatori mancanti',
                detail: `Compilare tutti i campi obbligatori: ${campiRichiesti}`,
                life: 6000
            });
        }
    };
    return (
        <div className="modalBody">
            <Toast ref={toast} />
            <div className="p-grid p-fluid">
                {/* Sezione Ricerca Automatica P.IVA */}
                <div className="p-col-12">
                    <div className="p-card p-p-3" style={{backgroundColor: '#f8f9fa', border: '1px solid #dee2e6', borderRadius: '8px', marginBottom: '20px'}}>
                        <h5 style={{color: '#495057', marginBottom: '10px'}}>
                            <i className="pi pi-search" style={{marginRight: '8px'}}></i>
                            Ricerca Automatica Dati Azienda
                        </h5>
                        <p style={{color: '#6c757d', fontSize: '14px', marginBottom: '15px'}}>
                            Inserisci la Partita IVA per compilare automaticamente i dati azienda
                        </p>

                        <div className="p-col-12 p-md-6" style={{padding: '0'}}>
                            <h6>{Costanti.pIva} <span style={{color: 'red'}}>*</span></h6>
                            <div className="p-inputgroup">
                                <span className="p-inputgroup-addon">
                                    <i className="pi pi-credit-card"></i>
                                </span>
                                <InputText
                                    type='text'
                                    value={results4}
                                    onChange={(e) => {
                                        const newValue = e.target.value;
                                        setResults4(newValue);

                                        // Reset dello status quando l'utente modifica la P.IVA
                                        if (lookupStatus) {
                                            setLookupStatus(null);
                                        }
                                        // Reset del flag aziendale se era stato impostato
                                        if (isCompanyLookup) {
                                            setIsCompanyLookup(false);
                                        }

                                        // Auto-lookup quando P.IVA è valida (11 cifre)
                                        if (/^\d{11}$/.test(newValue.replace(/\s/g, ''))) {
                                            // Debounce per evitare troppe chiamate
                                            setTimeout(() => {
                                                if (results4 === newValue) { // Verifica che il valore non sia cambiato
                                                    lookupPIva();
                                                }
                                            }, 500);
                                        }
                                    }}
                                    keyfilter={/^[\d\s]+$/}
                                    placeholder="Inserisci partita IVA (11 cifre) - ricerca automatica"
                                    editable='true'
                                    maxLength={11}
                                    className={!results4 || results4.trim() === '' || !/^\d{11}$/.test(results4.replace(/\s/g, '')) ? 'p-invalid' : ''}
                                />
                                <Button
                                    type="button"
                                    icon={isLookingUp ? "pi pi-spin pi-spinner" : "pi pi-search"}
                                    className="p-button-outlined p-button-secondary"
                                    onClick={lookupPIva}
                                    disabled={isLookingUp || !results4 || !/^\d{11}$/.test(results4.replace(/\s/g, ''))}
                                    tooltip="Cerca automaticamente i dati aziendali"
                                    tooltipOptions={{position: 'top'}}
                                />
                            </div>
                            {results4 && !/^\d{11}$/.test(results4.replace(/\s/g, '')) && (
                                <small className="p-error">La Partita IVA deve contenere esattamente 11 cifre</small>
                            )}

                            {/* Indicatori di stato lookup */}
                            {lookupStatus === 'success' && (
                                <small className="p-text-success">
                                    <i className="pi pi-check-circle"></i> Dati aziendali trovati e compilati automaticamente
                                </small>
                            )}
                            {lookupStatus === 'not_found' && (
                                <small className="p-text-warning">
                                    <i className="pi pi-exclamation-triangle"></i> Azienda non trovata - compila manualmente i campi sottostanti
                                </small>
                            )}
                            {lookupStatus === 'error' && (
                                <small className="p-text-warning">
                                    <i className="pi pi-times-circle"></i> Ricerca automatica non disponibile - compila manualmente i campi
                                </small>
                            )}
                            {!lookupStatus && (
                                <small className="p-text-secondary">
                                    <i className="pi pi-info-circle"></i> Inserisci una P.IVA valida (11 cifre) per la ricerca automatica dei dati aziendali
                                </small>
                            )}
                        </div>
                    </div>
                </div>

                {/* Sezione Dati Obbligatori */}
                <div className="p-col-12">
                    <h5 style={{color: '#495057', marginBottom: '15px'}}>
                        <i className="pi pi-user" style={{marginRight: '8px'}}></i>
                        Dati Obbligatori
                    </h5>
                </div>

                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Nome} <span style={{color: 'red'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-user"></i>
                        </span>
                        <InputText
                            type='text'
                            value={results}
                            onChange={(e) => setResults(e.target.value)}
                            keyfilter={/^[^#<>*!]+$/}
                            placeholder="Inserisci nome (obbligatorio)"
                            editable='true'
                            className={!results || results.trim() === '' ? 'p-invalid' : ''}
                        />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    {(() => {
                        // Rileva se è un'azienda anche senza lookup
                        const hasCompanyPIva = results4 && /^\d{11}$/.test(results4.replace(/\s/g, ''));
                        const hasCompanyName = results && (
                            results.toUpperCase().includes('SRL') ||
                            results.toUpperCase().includes('S.R.L.') ||
                            results.toUpperCase().includes('SPA') ||
                            results.toUpperCase().includes('S.P.A.') ||
                            results.toUpperCase().includes('SNCA') ||
                            results.toUpperCase().includes('SAS') ||
                            results.toUpperCase().includes('SNC') ||
                            results.toUpperCase().includes('SOCIETÀ') ||
                            results.toUpperCase().includes('COMPANY') ||
                            results.toUpperCase().includes('SERVICES') ||
                            results.toUpperCase().includes('TRADING') ||
                            results.toUpperCase().includes('BROKER') ||
                            results.toUpperCase().includes('GROUP') ||
                            results.toUpperCase().includes('HOLDING') ||
                            results.toUpperCase().includes('CORPORATION') ||
                            results.toUpperCase().includes('CORP') ||
                            results.toUpperCase().includes('LTD') ||
                            results.toUpperCase().includes('LIMITED') ||
                            results.toUpperCase().includes('INC') ||
                            results.length > 30
                        );

                        const isDetectedCompany = hasCompanyPIva || hasCompanyName;
                        const isAnyCompany = isCompanyLookup || isDetectedCompany;

                        return (
                            <>
                                <h6>
                                    {Costanti.Cognome}
                                    {!isAnyCompany && <span style={{color: 'red'}}>*</span>}
                                    {isAnyCompany && <span style={{color: '#6c757d', fontSize: '12px'}}> (opzionale per aziende)</span>}
                                </h6>
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className={isAnyCompany ? "pi pi-building" : "pi pi-user"}></i>
                                    </span>
                                    <InputText
                                        type='text'
                                        value={results8}
                                        onChange={(e) => setResults8(e.target.value)}
                                        keyfilter={/^[^#<>*!]+$/}
                                        placeholder={isAnyCompany ? "Opzionale per aziende" : "Inserisci cognome (obbligatorio)"}
                                        editable='true'
                                        className={!isAnyCompany && (!results8 || results8.trim() === '') ? 'p-invalid' : ''}
                                    />
                                </div>
                                {isAnyCompany && (
                                    <small className="p-text-secondary">
                                        <i className="pi pi-info-circle"></i> Campo opzionale per le aziende
                                    </small>
                                )}
                            </>
                        );
                    })()}
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Email} <span style={{color: 'red'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-envelope"></i>
                        </span>
                        <InputText
                            type="email"
                            value={results2}
                            onChange={(e) => setResults2(e.target.value)}
                            placeholder="Inserisci email (obbligatoria)"
                            editable='true'
                            className={!results2 || results2.trim() === '' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(results2) ? 'p-invalid' : ''}
                        />
                    </div>
                    {(!results2 || results2.trim() === '') && (
                        <small className="p-error">Email è obbligatoria</small>
                    )}
                    {results2 && results2.trim() !== '' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(results2) && (
                        <small className="p-error">Formato email non valido</small>
                    )}
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Tel} <span style={{color: 'orange'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-phone"></i>
                        </span>
                        <InputText
                            type="tel"
                            value={results3}
                            onChange={(e) => setResults3(e.target.value)}
                            keyfilter={/^[\d\s\-\(\)\+]+$/}
                            placeholder="Inserisci telefono fisso"
                            editable='true'
                            className={results3 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results3) ? 'p-invalid' : ''}
                        />
                    </div>
                    {results3 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results3) && (
                        <small className="p-error">Formato telefono non valido</small>
                    )}
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Cell} <span style={{color: 'orange'}}>*</span></h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-mobile"></i>
                        </span>
                        <InputText
                            type="tel"
                            value={results9}
                            onChange={(e) => setResults9(e.target.value)}
                            keyfilter={/^[\d\s\-\(\)\+]+$/}
                            placeholder="Inserisci cellulare"
                            editable='true'
                            className={results9 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results9) ? 'p-invalid' : ''}
                        />
                    </div>
                    {results9 && !/^\+?[\d\s\-\(\)]{8,}$/.test(results9) && (
                        <small className="p-error">Formato cellulare non valido</small>
                    )}
                </div>

                {/* Messaggio informativo per i contatti */}
                {(!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && (
                    <div className="p-col-12">
                        <small className="p-error">
                            <i className="pi pi-info-circle"></i> Inserire almeno un numero di telefono (fisso o cellulare)
                        </small>
                    </div>
                )}

                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Indirizzo}</h6>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <i className="pi pi-directions"></i>
                        </span>
                        <InputText type='text' value={results5} onChange={(e) => setResults5(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder="Inserisci indirizzo" editable='true' />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Città}</h6>
                    <div className="p-inputgroup">
                        <InputText type='text' value={results6} onChange={(e) => setResults6(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder="Inserisci città" editable='true' />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.CodPost}</h6>
                    <div className="p-inputgroup">
                        <InputText type='text' value={results7} onChange={(e) => setResults7(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder="Inserisci C.A.P." editable='true' />
                    </div>
                </div>
                <div className="p-col-12 p-md-6">
                    <h6>{Costanti.Pagamento}</h6>
                    <div className="p-inputgroup">
                        <Dropdown className='w-100' value={results10} options={modPag} onChange={(e) => setResults10(e.target.value)} optionLabel="name" placeholder="Seleziona metodo di pagamento" filter filterBy='name' />
                    </div>
                </div>
            </div>
            <div className="d-flex justify-content-center mb-2 mt-4">
                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}
                <Button id="invia" className="p-button saveList justify-content-center float-right ionicon mx-0 w-50" onClick={Invia}>{Costanti.salva}</Button>
            </div>
        </div>
    );
}

export default AggiungiAnagrafica;
