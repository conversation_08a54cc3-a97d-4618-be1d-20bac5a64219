{"ast": null, "code": "import Tabs from './Tabs';\nimport TabPane from './TabPanelList/TabPane';\nexport { TabPane };\nexport default Tabs;", "map": {"version": 3, "names": ["Tabs", "TabPane"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/index.js"], "sourcesContent": ["import Tabs from './Tabs';\nimport TabPane from './TabPanelList/TabPane';\nexport { TabPane };\nexport default Tabs;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,SAASA,OAAO;AAChB,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}