{"ast": null, "code": "export function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? \"\".concat(pos, \"-\").concat(index) : \"\".concat(index);\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  return title;\n}", "map": {"version": 3, "names": ["getColumnKey", "column", "defaultKey", "key", "undefined", "dataIndex", "Array", "isArray", "join", "getColumnPos", "index", "pos", "concat", "renderColumnTitle", "title", "props"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/util.js"], "sourcesContent": ["export function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? \"\".concat(pos, \"-\").concat(index) : \"\".concat(index);\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n\n  return title;\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,MAAM,EAAEC,UAAU,EAAE;EAC/C,IAAI,KAAK,IAAID,MAAM,IAAIA,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIH,MAAM,CAACE,GAAG,KAAK,IAAI,EAAE;IACtE,OAAOF,MAAM,CAACE,GAAG;EACnB;EAEA,IAAIF,MAAM,CAACI,SAAS,EAAE;IACpB,OAAOC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACI,SAAS,CAAC,GAAGJ,MAAM,CAACI,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGP,MAAM,CAACI,SAAS;EACxF;EAEA,OAAOH,UAAU;AACnB;AACA,OAAO,SAASO,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvC,OAAOA,GAAG,GAAG,EAAE,CAACC,MAAM,CAACD,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,CAACF,KAAK,CAAC,GAAG,EAAE,CAACE,MAAM,CAACF,KAAK,CAAC;AACnE;AACA,OAAO,SAASG,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC9C,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IAC/B,OAAOA,KAAK,CAACC,KAAK,CAAC;EACrB;EAEA,OAAOD,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}