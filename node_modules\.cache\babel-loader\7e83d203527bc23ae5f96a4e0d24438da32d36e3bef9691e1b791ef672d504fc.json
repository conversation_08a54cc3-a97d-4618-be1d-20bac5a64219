{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\nimport * as React from 'react';\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport warning from \"rc-util/es/warning\";\nimport ResizeObserver from 'rc-resize-observer';\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport ColumnGroup from './sugar/ColumnGroup';\nimport Column from './sugar/Column';\nimport Header from './Header/Header';\nimport TableContext from './context/TableContext';\nimport BodyContext from './context/BodyContext';\nimport Body from './Body';\nimport useColumns from './hooks/useColumns';\nimport { useLayoutState, useTimeoutLock } from './hooks/useFrame';\nimport { getPathValue, mergeObject, validateValue, getColumnsKey } from './utils/valueUtil';\nimport ResizeContext from './context/ResizeContext';\nimport useStickyOffsets from './hooks/useStickyOffsets';\nimport ColGroup from './ColGroup';\nimport { getExpandableProps } from './utils/legacyUtil';\nimport Panel from './Panel';\nimport Footer, { FooterComponents } from './Footer';\nimport { findAllChildrenKeys, renderExpandIcon } from './utils/expandUtil';\nimport { getCellFixedInfo } from './utils/fixUtil';\nimport StickyScrollBar from './stickyScrollBar';\nimport useSticky from './hooks/useSticky';\nimport FixedHolder from './FixedHolder';\nimport Summary from './Footer/Summary';\nimport StickyContext from './context/StickyContext';\nimport ExpandedRowContext from './context/ExpandedRowContext';\nimport { EXPAND_COLUMN } from './constant'; // Used for conditions cache\n\nvar EMPTY_DATA = []; // Used for customize scroll\n\nvar EMPTY_SCROLL_TARGET = {};\nexport var INTERNAL_HOOKS = 'rc-table-internal-hook';\nvar MemoTableContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  if (!shallowEqual(prev.props, next.props)) {\n    return false;\n  } // No additional render when pinged status change.\n  // This is not a bug.\n\n  return prev.pingLeft !== next.pingLeft || prev.pingRight !== next.pingRight;\n});\nfunction Table(props) {\n  var _classNames;\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    rowClassName = props.rowClassName,\n    style = props.style,\n    data = props.data,\n    rowKey = props.rowKey,\n    scroll = props.scroll,\n    tableLayout = props.tableLayout,\n    direction = props.direction,\n    title = props.title,\n    footer = props.footer,\n    summary = props.summary,\n    id = props.id,\n    showHeader = props.showHeader,\n    components = props.components,\n    emptyText = props.emptyText,\n    onRow = props.onRow,\n    onHeaderRow = props.onHeaderRow,\n    internalHooks = props.internalHooks,\n    transformColumns = props.transformColumns,\n    internalRefs = props.internalRefs,\n    sticky = props.sticky;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length; // ===================== Warning ======================\n\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  } // ==================== Customize =====================\n\n  var mergedComponents = React.useMemo(function () {\n    return mergeObject(components, {});\n  }, [components]);\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getPathValue(mergedComponents, path) || defaultComponent;\n  }, [mergedComponents]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      var key = record && record[rowKey];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n      return key;\n    };\n  }, [rowKey]); // ====================== Expand ======================\n\n  var expandableConfig = getExpandableProps(props);\n  var expandIcon = expandableConfig.expandIcon,\n    expandedRowKeys = expandableConfig.expandedRowKeys,\n    defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys,\n    defaultExpandAllRows = expandableConfig.defaultExpandAllRows,\n    expandedRowRender = expandableConfig.expandedRowRender,\n    onExpand = expandableConfig.onExpand,\n    onExpandedRowsChange = expandableConfig.onExpandedRowsChange,\n    expandRowByClick = expandableConfig.expandRowByClick,\n    rowExpandable = expandableConfig.rowExpandable,\n    expandIconColumnIndex = expandableConfig.expandIconColumnIndex,\n    expandedRowClassName = expandableConfig.expandedRowClassName,\n    childrenColumnName = expandableConfig.childrenColumnName,\n    indentSize = expandableConfig.indentSize;\n  var mergedExpandIcon = expandIcon || renderExpandIcon;\n  var mergedChildrenColumnName = childrenColumnName || 'children';\n  var expandableType = React.useMemo(function () {\n    if (expandedRowRender) {\n      return 'row';\n    }\n    /* eslint-disable no-underscore-dangle */\n\n    /**\n     * Fix https://github.com/ant-design/ant-design/issues/21154\n     * This is a workaround to not to break current behavior.\n     * We can remove follow code after final release.\n     *\n     * To other developer:\n     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor\n     */\n\n    if (props.expandable && internalHooks === INTERNAL_HOOKS && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some(function (record) {\n      return record && _typeof(record) === 'object' && record[mergedChildrenColumnName];\n    })) {\n      return 'nest';\n    }\n    /* eslint-enable */\n\n    return false;\n  }, [!!expandedRowRender, mergedData]);\n  var _React$useState = React.useState(function () {\n      if (defaultExpandedRowKeys) {\n        return defaultExpandedRowKeys;\n      }\n      if (defaultExpandAllRows) {\n        return findAllChildrenKeys(mergedData, getRowKey, mergedChildrenColumnName);\n      }\n      return [];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerExpandedKeys = _React$useState2[0],\n    setInnerExpandedKeys = _React$useState2[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    return new Set(expandedRowKeys || innerExpandedKeys || []);\n  }, [expandedRowKeys, innerExpandedKeys]);\n  var onTriggerExpand = React.useCallback(function (record) {\n    var key = getRowKey(record, mergedData.indexOf(record));\n    var newExpandedKeys;\n    var hasKey = mergedExpandedKeys.has(key);\n    if (hasKey) {\n      mergedExpandedKeys.delete(key);\n      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);\n    } else {\n      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);\n    }\n    setInnerExpandedKeys(newExpandedKeys);\n    if (onExpand) {\n      onExpand(!hasKey, record);\n    }\n    if (onExpandedRowsChange) {\n      onExpandedRowsChange(newExpandedKeys);\n    }\n  }, [getRowKey, mergedExpandedKeys, mergedData, onExpand, onExpandedRowsChange]); // Warning if use `expandedRowRender` and nest children in the same time\n\n  if (process.env.NODE_ENV !== 'production' && expandedRowRender && mergedData.some(function (record) {\n    return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);\n  })) {\n    warning(false, '`expandedRowRender` should not use with nested Table');\n  } // ====================== Column ======================\n\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    componentWidth = _React$useState4[0],\n    setComponentWidth = _React$useState4[1];\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n      expandable: !!expandedRowRender,\n      expandedKeys: mergedExpandedKeys,\n      getRowKey: getRowKey,\n      // https://github.com/ant-design/ant-design/issues/23894\n      onTriggerExpand: onTriggerExpand,\n      expandIcon: mergedExpandIcon,\n      expandIconColumnIndex: expandIconColumnIndex,\n      direction: direction\n    }), internalHooks === INTERNAL_HOOKS ? transformColumns : null),\n    _useColumns2 = _slicedToArray(_useColumns, 2),\n    columns = _useColumns2[0],\n    flattenColumns = _useColumns2[1];\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]); // ====================== Scroll ======================\n\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollSummaryRef = React.useRef();\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pingedLeft = _React$useState6[0],\n    setPingedLeft = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    pingedRight = _React$useState8[0],\n    setPingedRight = _React$useState8[1];\n  var _useLayoutState = useLayoutState(new Map()),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    colsWidths = _useLayoutState2[0],\n    updateColsWidths = _useLayoutState2[1]; // Convert map to number width\n\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns.length, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(scroll.x) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref2) {\n    var fixed = _ref2.fixed;\n    return fixed;\n  }); // Sticky\n\n  var stickyRef = React.useRef();\n  var _useSticky = useSticky(sticky, prefixCls),\n    isSticky = _useSticky.isSticky,\n    offsetHeader = _useSticky.offsetHeader,\n    offsetSummary = _useSticky.offsetSummary,\n    offsetScroll = _useSticky.offsetScroll,\n    stickyClassName = _useSticky.stickyClassName,\n    container = _useSticky.container; // Footer (Fix footer must fixed header)\n\n  var summaryNode = summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed; // Scroll\n\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: 'scroll',\n      maxHeight: scroll.y\n    };\n  }\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    }; // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n    scrollTableStyle = {\n      width: (scroll === null || scroll === void 0 ? void 0 : scroll.x) === true ? 'auto' : scroll === null || scroll === void 0 ? void 0 : scroll.x,\n      minWidth: '100%'\n    };\n  }\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    if (isVisible(fullTableRef.current)) {\n      updateColsWidths(function (widths) {\n        if (widths.get(columnKey) !== width) {\n          var newWidths = new Map(widths);\n          newWidths.set(columnKey, width);\n          return newWidths;\n        }\n        return widths;\n      });\n    }\n  }, []);\n  var _useTimeoutLock = useTimeoutLock(null),\n    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n    setScrollTarget = _useTimeoutLock2[0],\n    getScrollTarget = _useTimeoutLock2[1];\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      // eslint-disable-next-line no-param-reassign\n      target.scrollLeft = scrollLeft;\n    }\n  }\n  var onScroll = function onScroll(_ref3) {\n    var currentTarget = _ref3.currentTarget,\n      scrollLeft = _ref3.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n    if (currentTarget) {\n      var scrollWidth = currentTarget.scrollWidth,\n        clientWidth = currentTarget.clientWidth; // There is no space to scroll\n\n      if (scrollWidth === clientWidth) {\n        return;\n      }\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  };\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      onScroll({\n        currentTarget: scrollBodyRef.current\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n  var onFullTableResize = function onFullTableResize(_ref4) {\n    var width = _ref4.width;\n    if (width !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(fullTableRef.current ? fullTableRef.current.offsetWidth : width);\n    }\n  }; // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []); // ===================== Effects ======================\n\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    scrollbarSize = _React$useState10[0],\n    setScrollbarSize = _React$useState10[1];\n  var _React$useState11 = React.useState(true),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    supportSticky = _React$useState12[0],\n    setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first\n\n  React.useEffect(function () {\n    setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []); // ================== INTERNAL HOOKS ==================\n\n  React.useEffect(function () {\n    if (internalHooks === INTERNAL_HOOKS && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  }); // ====================== Render ======================\n\n  var TableComponent = getComponent(['table'], 'table'); // Table layout\n\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    } // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n\n    if (fixColumn) {\n      return (scroll === null || scroll === void 0 ? void 0 : scroll.x) === 'max-content' ? 'auto' : 'fixed';\n    }\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref5) {\n      var ellipsis = _ref5.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode; // Header props\n\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  }; // Empty\n\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n    return emptyText;\n  }, [hasData, emptyText]); // Body\n\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky,\n    expandedKeys: mergedExpandedKeys,\n    rowExpandable: rowExpandable,\n    getRowKey: getRowKey,\n    onRow: onRow,\n    emptyNode: emptyNode,\n    childrenColumnName: mergedChildrenColumnName\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref6) {\n      var width = _ref6.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var customizeScrollBody = getComponent(['body']);\n  if (process.env.NODE_ENV !== 'production' && typeof customizeScrollBody === 'function' && hasData && !fixHeader) {\n    warning(false, '`components.body` with render props is only work on `scroll.y`.');\n  }\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref7, index) {\n        var width = _ref7.width;\n        var colWidth = index === columns.length - 1 ? width - scrollbarSize : width;\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n        warning(false, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, {\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns\n      }, summaryNode)));\n    } // Fixed holder share the props\n\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && scroll.x === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onScroll\n    });\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n    }), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n    }), isSticky && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onScroll,\n      container: container\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, {\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns\n    }, summaryNode)));\n  }\n  var ariaProps = pickAttrs(props, {\n    aria: true,\n    data: true\n  });\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-right\"), pingedRight), _defineProperty(_classNames, \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), _defineProperty(_classNames, \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right'), _classNames)),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, ariaProps), /*#__PURE__*/React.createElement(MemoTableContent, {\n    pingLeft: pingedLeft,\n    pingRight: pingedRight,\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      stickyOffsets: stickyOffsets,\n      mergedExpandedKeys: mergedExpandedKeys\n    })\n  }, title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData))));\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n  var TableContextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: flattenColumns.map(function (_, colIndex) {\n        return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction);\n      }),\n      isSticky: isSticky\n    };\n  }, [prefixCls, getComponent, scrollbarSize, direction, flattenColumns, stickyOffsets, direction, isSticky]);\n  var BodyContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, columnContext), {}, {\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandRowByClick,\n      expandedRowRender: expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandIconColumnIndex,\n      indentSize: indentSize\n    });\n  }, [columnContext, mergedTableLayout, rowClassName, expandedRowClassName, mergedExpandIcon, expandableType, expandRowByClick, expandedRowRender, onTriggerExpand, expandIconColumnIndex, indentSize]);\n  var ExpandedRowContextValue = React.useMemo(function () {\n    return {\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll\n    };\n  }, [componentWidth, fixHeader, fixColumn, horizonScroll]);\n  var ResizeContextValue = React.useMemo(function () {\n    return {\n      onColumnResize: onColumnResize\n    };\n  }, [onColumnResize]);\n  return /*#__PURE__*/React.createElement(StickyContext.Provider, {\n    value: supportSticky\n  }, /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, /*#__PURE__*/React.createElement(BodyContext.Provider, {\n    value: BodyContextValue\n  }, /*#__PURE__*/React.createElement(ExpandedRowContext.Provider, {\n    value: ExpandedRowContextValue\n  }, /*#__PURE__*/React.createElement(ResizeContext.Provider, {\n    value: ResizeContextValue\n  }, fullTable)))));\n}\nTable.EXPAND_COLUMN = EXPAND_COLUMN;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = FooterComponents;\nTable.defaultProps = {\n  rowKey: 'key',\n  prefixCls: 'rc-table',\n  emptyText: function emptyText() {\n    return 'No Data';\n  }\n};\nexport default Table;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_objectSpread", "_toConsumableArray", "_slicedToArray", "_typeof", "React", "isVisible", "pickAttrs", "isStyleSupport", "classNames", "shallowEqual", "warning", "ResizeObserver", "getTargetScrollBarSize", "ColumnGroup", "Column", "Header", "TableContext", "BodyContext", "Body", "useColumns", "useLayoutState", "useTimeoutLock", "getPathValue", "mergeObject", "validate<PERSON><PERSON>ue", "getColumnsKey", "ResizeContext", "useStickyOffsets", "ColGroup", "getExpandableProps", "Panel", "Footer", "FooterComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderExpandIcon", "getCellFixedInfo", "StickyScrollBar", "useSticky", "FixedHolder", "Summary", "StickyContext", "ExpandedRowContext", "EXPAND_COLUMN", "EMPTY_DATA", "EMPTY_SCROLL_TARGET", "INTERNAL_HOOKS", "MemoTableContent", "memo", "_ref", "children", "prev", "next", "props", "pingLeft", "pingRight", "Table", "_classNames", "prefixCls", "className", "rowClassName", "style", "data", "<PERSON><PERSON><PERSON>", "scroll", "tableLayout", "direction", "title", "footer", "summary", "id", "showHeader", "components", "emptyText", "onRow", "onHeaderRow", "internalHooks", "transformColumns", "internalRefs", "sticky", "mergedData", "hasData", "length", "process", "env", "NODE_ENV", "for<PERSON>ach", "name", "undefined", "concat", "mergedComponents", "useMemo", "getComponent", "useCallback", "path", "defaultComponent", "getRowKey", "record", "key", "expandableConfig", "expandIcon", "expandedRowKeys", "defaultExpandedRowKeys", "defaultExpandAllRows", "expandedRowRender", "onExpand", "onExpandedRowsChange", "expandRowByClick", "rowExpandable", "expandIconColumnIndex", "expandedRowClassName", "childrenColumnName", "indentSize", "mergedExpandIcon", "mergedChildrenColumnName", "expandableType", "expandable", "__PARENT_RENDER_ICON__", "some", "_React$useState", "useState", "_React$useState2", "innerExpandedKeys", "setInnerExpandedKeys", "mergedExpandedKeys", "Set", "onTriggerExpand", "indexOf", "newExpandedKeys", "<PERSON><PERSON><PERSON>", "has", "delete", "Array", "isArray", "_React$useState3", "_React$useState4", "componentWidth", "setComponentWidth", "_useColumns", "expandedKeys", "_useColumns2", "columns", "flattenColumns", "columnContext", "fullTableRef", "useRef", "scrollHeaderRef", "scrollBodyRef", "scrollSummaryRef", "_React$useState5", "_React$useState6", "pingedLeft", "setPingedLeft", "_React$useState7", "_React$useState8", "pingedRight", "setPingedRight", "_useLayoutState", "Map", "_useLayoutState2", "colsWidths", "updateColsWidths", "colsKeys", "pureColWidths", "map", "column<PERSON>ey", "get", "col<PERSON><PERSON><PERSON>", "join", "stickyOffsets", "fixHeader", "y", "horizonScroll", "x", "Boolean", "fixed", "fixColumn", "_ref2", "stickyRef", "_useSticky", "isSticky", "offsetHeader", "offsetSummary", "offsetScroll", "stickyClassName", "container", "summaryNode", "fixFooter", "isValidElement", "type", "scrollXStyle", "scrollYStyle", "scrollTableStyle", "overflowY", "maxHeight", "overflowX", "width", "min<PERSON><PERSON><PERSON>", "onColumnResize", "current", "widths", "newWidths", "set", "_useTimeoutLock", "_useTimeoutLock2", "setScrollTarget", "getScrollTarget", "forceScroll", "scrollLeft", "target", "onScroll", "_ref3", "currentTarget", "isRTL", "mergedScrollLeft", "compareTarget", "_stickyRef$current", "setScrollLeft", "scrollWidth", "clientWidth", "triggerOnScroll", "onFullTableResize", "_ref4", "offsetWidth", "mounted", "useEffect", "_React$useState9", "_React$useState10", "scrollbarSize", "setScrollbarSize", "_React$useState11", "_React$useState12", "supportSticky", "setSupportSticky", "body", "TableComponent", "mergedTableLayout", "_ref5", "ellipsis", "groupTableNode", "headerProps", "columCount", "emptyNode", "bodyTable", "createElement", "measureColumnWidth", "bodyColGroup", "_ref6", "customizeScrollBody", "bodyContent", "ref", "_ref7", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "fixedHolderProps", "noData", "maxContentScroll", "Fragment", "stickyTopOffset", "fixedHolderPassProps", "stickyBottomOffset", "ariaProps", "aria", "fullTable", "onResize", "TableContextValue", "fixedInfoList", "_", "colIndex", "BodyContextValue", "ExpandedRowContextValue", "ResizeContextValue", "Provider", "value", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Table.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\nimport * as React from 'react';\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport warning from \"rc-util/es/warning\";\nimport ResizeObserver from 'rc-resize-observer';\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport ColumnGroup from './sugar/ColumnGroup';\nimport Column from './sugar/Column';\nimport Header from './Header/Header';\nimport TableContext from './context/TableContext';\nimport BodyContext from './context/BodyContext';\nimport Body from './Body';\nimport useColumns from './hooks/useColumns';\nimport { useLayoutState, useTimeoutLock } from './hooks/useFrame';\nimport { getPathValue, mergeObject, validateValue, getColumnsKey } from './utils/valueUtil';\nimport ResizeContext from './context/ResizeContext';\nimport useStickyOffsets from './hooks/useStickyOffsets';\nimport ColGroup from './ColGroup';\nimport { getExpandableProps } from './utils/legacyUtil';\nimport Panel from './Panel';\nimport Footer, { FooterComponents } from './Footer';\nimport { findAllChildrenKeys, renderExpandIcon } from './utils/expandUtil';\nimport { getCellFixedInfo } from './utils/fixUtil';\nimport StickyScrollBar from './stickyScrollBar';\nimport useSticky from './hooks/useSticky';\nimport FixedHolder from './FixedHolder';\nimport Summary from './Footer/Summary';\nimport StickyContext from './context/StickyContext';\nimport ExpandedRowContext from './context/ExpandedRowContext';\nimport { EXPAND_COLUMN } from './constant'; // Used for conditions cache\n\nvar EMPTY_DATA = []; // Used for customize scroll\n\nvar EMPTY_SCROLL_TARGET = {};\nexport var INTERNAL_HOOKS = 'rc-table-internal-hook';\nvar MemoTableContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  if (!shallowEqual(prev.props, next.props)) {\n    return false;\n  } // No additional render when pinged status change.\n  // This is not a bug.\n\n\n  return prev.pingLeft !== next.pingLeft || prev.pingRight !== next.pingRight;\n});\n\nfunction Table(props) {\n  var _classNames;\n\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      rowClassName = props.rowClassName,\n      style = props.style,\n      data = props.data,\n      rowKey = props.rowKey,\n      scroll = props.scroll,\n      tableLayout = props.tableLayout,\n      direction = props.direction,\n      title = props.title,\n      footer = props.footer,\n      summary = props.summary,\n      id = props.id,\n      showHeader = props.showHeader,\n      components = props.components,\n      emptyText = props.emptyText,\n      onRow = props.onRow,\n      onHeaderRow = props.onHeaderRow,\n      internalHooks = props.internalHooks,\n      transformColumns = props.transformColumns,\n      internalRefs = props.internalRefs,\n      sticky = props.sticky;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length; // ===================== Warning ======================\n\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  } // ==================== Customize =====================\n\n\n  var mergedComponents = React.useMemo(function () {\n    return mergeObject(components, {});\n  }, [components]);\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getPathValue(mergedComponents, path) || defaultComponent;\n  }, [mergedComponents]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n\n    return function (record) {\n      var key = record && record[rowKey];\n\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n\n      return key;\n    };\n  }, [rowKey]); // ====================== Expand ======================\n\n  var expandableConfig = getExpandableProps(props);\n  var expandIcon = expandableConfig.expandIcon,\n      expandedRowKeys = expandableConfig.expandedRowKeys,\n      defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys,\n      defaultExpandAllRows = expandableConfig.defaultExpandAllRows,\n      expandedRowRender = expandableConfig.expandedRowRender,\n      onExpand = expandableConfig.onExpand,\n      onExpandedRowsChange = expandableConfig.onExpandedRowsChange,\n      expandRowByClick = expandableConfig.expandRowByClick,\n      rowExpandable = expandableConfig.rowExpandable,\n      expandIconColumnIndex = expandableConfig.expandIconColumnIndex,\n      expandedRowClassName = expandableConfig.expandedRowClassName,\n      childrenColumnName = expandableConfig.childrenColumnName,\n      indentSize = expandableConfig.indentSize;\n  var mergedExpandIcon = expandIcon || renderExpandIcon;\n  var mergedChildrenColumnName = childrenColumnName || 'children';\n  var expandableType = React.useMemo(function () {\n    if (expandedRowRender) {\n      return 'row';\n    }\n    /* eslint-disable no-underscore-dangle */\n\n    /**\n     * Fix https://github.com/ant-design/ant-design/issues/21154\n     * This is a workaround to not to break current behavior.\n     * We can remove follow code after final release.\n     *\n     * To other developer:\n     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor\n     */\n\n\n    if (props.expandable && internalHooks === INTERNAL_HOOKS && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some(function (record) {\n      return record && _typeof(record) === 'object' && record[mergedChildrenColumnName];\n    })) {\n      return 'nest';\n    }\n    /* eslint-enable */\n\n\n    return false;\n  }, [!!expandedRowRender, mergedData]);\n\n  var _React$useState = React.useState(function () {\n    if (defaultExpandedRowKeys) {\n      return defaultExpandedRowKeys;\n    }\n\n    if (defaultExpandAllRows) {\n      return findAllChildrenKeys(mergedData, getRowKey, mergedChildrenColumnName);\n    }\n\n    return [];\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      innerExpandedKeys = _React$useState2[0],\n      setInnerExpandedKeys = _React$useState2[1];\n\n  var mergedExpandedKeys = React.useMemo(function () {\n    return new Set(expandedRowKeys || innerExpandedKeys || []);\n  }, [expandedRowKeys, innerExpandedKeys]);\n  var onTriggerExpand = React.useCallback(function (record) {\n    var key = getRowKey(record, mergedData.indexOf(record));\n    var newExpandedKeys;\n    var hasKey = mergedExpandedKeys.has(key);\n\n    if (hasKey) {\n      mergedExpandedKeys.delete(key);\n      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);\n    } else {\n      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);\n    }\n\n    setInnerExpandedKeys(newExpandedKeys);\n\n    if (onExpand) {\n      onExpand(!hasKey, record);\n    }\n\n    if (onExpandedRowsChange) {\n      onExpandedRowsChange(newExpandedKeys);\n    }\n  }, [getRowKey, mergedExpandedKeys, mergedData, onExpand, onExpandedRowsChange]); // Warning if use `expandedRowRender` and nest children in the same time\n\n  if (process.env.NODE_ENV !== 'production' && expandedRowRender && mergedData.some(function (record) {\n    return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);\n  })) {\n    warning(false, '`expandedRowRender` should not use with nested Table');\n  } // ====================== Column ======================\n\n\n  var _React$useState3 = React.useState(0),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      componentWidth = _React$useState4[0],\n      setComponentWidth = _React$useState4[1];\n\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n    expandable: !!expandedRowRender,\n    expandedKeys: mergedExpandedKeys,\n    getRowKey: getRowKey,\n    // https://github.com/ant-design/ant-design/issues/23894\n    onTriggerExpand: onTriggerExpand,\n    expandIcon: mergedExpandIcon,\n    expandIconColumnIndex: expandIconColumnIndex,\n    direction: direction\n  }), internalHooks === INTERNAL_HOOKS ? transformColumns : null),\n      _useColumns2 = _slicedToArray(_useColumns, 2),\n      columns = _useColumns2[0],\n      flattenColumns = _useColumns2[1];\n\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]); // ====================== Scroll ======================\n\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollSummaryRef = React.useRef();\n\n  var _React$useState5 = React.useState(false),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      pingedLeft = _React$useState6[0],\n      setPingedLeft = _React$useState6[1];\n\n  var _React$useState7 = React.useState(false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      pingedRight = _React$useState8[0],\n      setPingedRight = _React$useState8[1];\n\n  var _useLayoutState = useLayoutState(new Map()),\n      _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n      colsWidths = _useLayoutState2[0],\n      updateColsWidths = _useLayoutState2[1]; // Convert map to number width\n\n\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns.length, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(scroll.x) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref2) {\n    var fixed = _ref2.fixed;\n    return fixed;\n  }); // Sticky\n\n  var stickyRef = React.useRef();\n\n  var _useSticky = useSticky(sticky, prefixCls),\n      isSticky = _useSticky.isSticky,\n      offsetHeader = _useSticky.offsetHeader,\n      offsetSummary = _useSticky.offsetSummary,\n      offsetScroll = _useSticky.offsetScroll,\n      stickyClassName = _useSticky.stickyClassName,\n      container = _useSticky.container; // Footer (Fix footer must fixed header)\n\n\n  var summaryNode = summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed; // Scroll\n\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: 'scroll',\n      maxHeight: scroll.y\n    };\n  }\n\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    }; // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n\n    scrollTableStyle = {\n      width: (scroll === null || scroll === void 0 ? void 0 : scroll.x) === true ? 'auto' : scroll === null || scroll === void 0 ? void 0 : scroll.x,\n      minWidth: '100%'\n    };\n  }\n\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    if (isVisible(fullTableRef.current)) {\n      updateColsWidths(function (widths) {\n        if (widths.get(columnKey) !== width) {\n          var newWidths = new Map(widths);\n          newWidths.set(columnKey, width);\n          return newWidths;\n        }\n\n        return widths;\n      });\n    }\n  }, []);\n\n  var _useTimeoutLock = useTimeoutLock(null),\n      _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n      setScrollTarget = _useTimeoutLock2[0],\n      getScrollTarget = _useTimeoutLock2[1];\n\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      // eslint-disable-next-line no-param-reassign\n      target.scrollLeft = scrollLeft;\n    }\n  }\n\n  var onScroll = function onScroll(_ref3) {\n    var currentTarget = _ref3.currentTarget,\n        scrollLeft = _ref3.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n\n    if (currentTarget) {\n      var scrollWidth = currentTarget.scrollWidth,\n          clientWidth = currentTarget.clientWidth; // There is no space to scroll\n\n      if (scrollWidth === clientWidth) {\n        return;\n      }\n\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  };\n\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      onScroll({\n        currentTarget: scrollBodyRef.current\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n\n  var onFullTableResize = function onFullTableResize(_ref4) {\n    var width = _ref4.width;\n\n    if (width !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(fullTableRef.current ? fullTableRef.current.offsetWidth : width);\n    }\n  }; // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n\n\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []); // ===================== Effects ======================\n\n  var _React$useState9 = React.useState(0),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      scrollbarSize = _React$useState10[0],\n      setScrollbarSize = _React$useState10[1];\n\n  var _React$useState11 = React.useState(true),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      supportSticky = _React$useState12[0],\n      setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first\n\n\n  React.useEffect(function () {\n    setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []); // ================== INTERNAL HOOKS ==================\n\n  React.useEffect(function () {\n    if (internalHooks === INTERNAL_HOOKS && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  }); // ====================== Render ======================\n\n  var TableComponent = getComponent(['table'], 'table'); // Table layout\n\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    } // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n\n\n    if (fixColumn) {\n      return (scroll === null || scroll === void 0 ? void 0 : scroll.x) === 'max-content' ? 'auto' : 'fixed';\n    }\n\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref5) {\n      var ellipsis = _ref5.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode; // Header props\n\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  }; // Empty\n\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n\n    return emptyText;\n  }, [hasData, emptyText]); // Body\n\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky,\n    expandedKeys: mergedExpandedKeys,\n    rowExpandable: rowExpandable,\n    getRowKey: getRowKey,\n    onRow: onRow,\n    emptyNode: emptyNode,\n    childrenColumnName: mergedChildrenColumnName\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref6) {\n      var width = _ref6.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var customizeScrollBody = getComponent(['body']);\n\n  if (process.env.NODE_ENV !== 'production' && typeof customizeScrollBody === 'function' && hasData && !fixHeader) {\n    warning(false, '`components.body` with render props is only work on `scroll.y`.');\n  }\n\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref7, index) {\n        var width = _ref7.width;\n        var colWidth = index === columns.length - 1 ? width - scrollbarSize : width;\n\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n\n        warning(false, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, {\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns\n      }, summaryNode)));\n    } // Fixed holder share the props\n\n\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && scroll.x === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onScroll\n    });\n\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n    }), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), function (fixedHolderPassProps) {\n      return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n    }), isSticky && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onScroll,\n      container: container\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, {\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns\n    }, summaryNode)));\n  }\n\n  var ariaProps = pickAttrs(props, {\n    aria: true,\n    data: true\n  });\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-right\"), pingedRight), _defineProperty(_classNames, \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), _defineProperty(_classNames, \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right'), _classNames)),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, ariaProps), /*#__PURE__*/React.createElement(MemoTableContent, {\n    pingLeft: pingedLeft,\n    pingRight: pingedRight,\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      stickyOffsets: stickyOffsets,\n      mergedExpandedKeys: mergedExpandedKeys\n    })\n  }, title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData))));\n\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n\n  var TableContextValue = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: flattenColumns.map(function (_, colIndex) {\n        return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction);\n      }),\n      isSticky: isSticky\n    };\n  }, [prefixCls, getComponent, scrollbarSize, direction, flattenColumns, stickyOffsets, direction, isSticky]);\n  var BodyContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, columnContext), {}, {\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandRowByClick,\n      expandedRowRender: expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandIconColumnIndex,\n      indentSize: indentSize\n    });\n  }, [columnContext, mergedTableLayout, rowClassName, expandedRowClassName, mergedExpandIcon, expandableType, expandRowByClick, expandedRowRender, onTriggerExpand, expandIconColumnIndex, indentSize]);\n  var ExpandedRowContextValue = React.useMemo(function () {\n    return {\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll\n    };\n  }, [componentWidth, fixHeader, fixColumn, horizonScroll]);\n  var ResizeContextValue = React.useMemo(function () {\n    return {\n      onColumnResize: onColumnResize\n    };\n  }, [onColumnResize]);\n  return /*#__PURE__*/React.createElement(StickyContext.Provider, {\n    value: supportSticky\n  }, /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, /*#__PURE__*/React.createElement(BodyContext.Provider, {\n    value: BodyContextValue\n  }, /*#__PURE__*/React.createElement(ExpandedRowContext.Provider, {\n    value: ExpandedRowContextValue\n  }, /*#__PURE__*/React.createElement(ResizeContext.Provider, {\n    value: ResizeContextValue\n  }, fullTable)))));\n}\n\nTable.EXPAND_COLUMN = EXPAND_COLUMN;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = FooterComponents;\nTable.defaultProps = {\n  rowKey: 'key',\n  prefixCls: 'rc-table',\n  emptyText: function emptyText() {\n    return 'No Data';\n  }\n};\nexport default Table;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,SAASC,cAAc,EAAEC,cAAc,QAAQ,kBAAkB;AACjE,SAASC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;AAC3F,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,IAAIC,gBAAgB,QAAQ,UAAU;AACnD,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC1E,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,aAAa,QAAQ,YAAY,CAAC,CAAC;;AAE5C,IAAIC,UAAU,GAAG,EAAE,CAAC,CAAC;;AAErB,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,OAAO,IAAIC,cAAc,GAAG,wBAAwB;AACpD,IAAIC,gBAAgB,GAAG,aAAa1C,KAAK,CAAC2C,IAAI,CAAC,UAAUC,IAAI,EAAE;EAC7D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,IAAI,EAAEC,IAAI,EAAE;EACvB,IAAI,CAAC1C,YAAY,CAACyC,IAAI,CAACE,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC,EAAE;IACzC,OAAO,KAAK;EACd,CAAC,CAAC;EACF;;EAGA,OAAOF,IAAI,CAACG,QAAQ,KAAKF,IAAI,CAACE,QAAQ,IAAIH,IAAI,CAACI,SAAS,KAAKH,IAAI,CAACG,SAAS;AAC7E,CAAC,CAAC;AAEF,SAASC,KAAKA,CAACH,KAAK,EAAE;EACpB,IAAII,WAAW;EAEf,IAAIC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,KAAK,GAAGd,KAAK,CAACc,KAAK;IACnBC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,EAAE,GAAGjB,KAAK,CAACiB,EAAE;IACbC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7BC,SAAS,GAAGpB,KAAK,CAACoB,SAAS;IAC3BC,KAAK,GAAGrB,KAAK,CAACqB,KAAK;IACnBC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,aAAa,GAAGvB,KAAK,CAACuB,aAAa;IACnCC,gBAAgB,GAAGxB,KAAK,CAACwB,gBAAgB;IACzCC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,MAAM,GAAG1B,KAAK,CAAC0B,MAAM;EACzB,IAAIC,UAAU,GAAGlB,IAAI,IAAIlB,UAAU;EACnC,IAAIqC,OAAO,GAAG,CAAC,CAACD,UAAU,CAACE,MAAM,CAAC,CAAC;;EAEnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACnH5E,OAAO,CAAC0C,KAAK,CAACkC,IAAI,CAAC,KAAKC,SAAS,EAAE,GAAG,CAACC,MAAM,CAACF,IAAI,EAAE,2CAA2C,CAAC,CAAC;IACnG,CAAC,CAAC;IACF5E,OAAO,CAAC,EAAE,gBAAgB,IAAI0C,KAAK,CAAC,EAAE,yEAAyE,CAAC;EAClH,CAAC,CAAC;;EAGF,IAAIqC,gBAAgB,GAAGrF,KAAK,CAACsF,OAAO,CAAC,YAAY;IAC/C,OAAOnE,WAAW,CAACgD,UAAU,EAAE,CAAC,CAAC,CAAC;EACpC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAChB,IAAIoB,YAAY,GAAGvF,KAAK,CAACwF,WAAW,CAAC,UAAUC,IAAI,EAAEC,gBAAgB,EAAE;IACrE,OAAOxE,YAAY,CAACmE,gBAAgB,EAAEI,IAAI,CAAC,IAAIC,gBAAgB;EACjE,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EACtB,IAAIM,SAAS,GAAG3F,KAAK,CAACsF,OAAO,CAAC,YAAY;IACxC,IAAI,OAAO5B,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IAEA,OAAO,UAAUkC,MAAM,EAAE;MACvB,IAAIC,GAAG,GAAGD,MAAM,IAAIA,MAAM,CAAClC,MAAM,CAAC;MAElC,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC1E,OAAO,CAACuF,GAAG,KAAKV,SAAS,EAAE,iGAAiG,CAAC;MAC/H;MAEA,OAAOU,GAAG;IACZ,CAAC;EACH,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEd,IAAIoC,gBAAgB,GAAGrE,kBAAkB,CAACuB,KAAK,CAAC;EAChD,IAAI+C,UAAU,GAAGD,gBAAgB,CAACC,UAAU;IACxCC,eAAe,GAAGF,gBAAgB,CAACE,eAAe;IAClDC,sBAAsB,GAAGH,gBAAgB,CAACG,sBAAsB;IAChEC,oBAAoB,GAAGJ,gBAAgB,CAACI,oBAAoB;IAC5DC,iBAAiB,GAAGL,gBAAgB,CAACK,iBAAiB;IACtDC,QAAQ,GAAGN,gBAAgB,CAACM,QAAQ;IACpCC,oBAAoB,GAAGP,gBAAgB,CAACO,oBAAoB;IAC5DC,gBAAgB,GAAGR,gBAAgB,CAACQ,gBAAgB;IACpDC,aAAa,GAAGT,gBAAgB,CAACS,aAAa;IAC9CC,qBAAqB,GAAGV,gBAAgB,CAACU,qBAAqB;IAC9DC,oBAAoB,GAAGX,gBAAgB,CAACW,oBAAoB;IAC5DC,kBAAkB,GAAGZ,gBAAgB,CAACY,kBAAkB;IACxDC,UAAU,GAAGb,gBAAgB,CAACa,UAAU;EAC5C,IAAIC,gBAAgB,GAAGb,UAAU,IAAIjE,gBAAgB;EACrD,IAAI+E,wBAAwB,GAAGH,kBAAkB,IAAI,UAAU;EAC/D,IAAII,cAAc,GAAG9G,KAAK,CAACsF,OAAO,CAAC,YAAY;IAC7C,IAAIa,iBAAiB,EAAE;MACrB,OAAO,KAAK;IACd;IACA;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI,IAAInD,KAAK,CAAC+D,UAAU,IAAIxC,aAAa,KAAK9B,cAAc,IAAIO,KAAK,CAAC+D,UAAU,CAACC,sBAAsB,IAAIrC,UAAU,CAACsC,IAAI,CAAC,UAAUrB,MAAM,EAAE;MACvI,OAAOA,MAAM,IAAI7F,OAAO,CAAC6F,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACiB,wBAAwB,CAAC;IACnF,CAAC,CAAC,EAAE;MACF,OAAO,MAAM;IACf;IACA;;IAGA,OAAO,KAAK;EACd,CAAC,EAAE,CAAC,CAAC,CAACV,iBAAiB,EAAExB,UAAU,CAAC,CAAC;EAErC,IAAIuC,eAAe,GAAGlH,KAAK,CAACmH,QAAQ,CAAC,YAAY;MAC/C,IAAIlB,sBAAsB,EAAE;QAC1B,OAAOA,sBAAsB;MAC/B;MAEA,IAAIC,oBAAoB,EAAE;QACxB,OAAOrE,mBAAmB,CAAC8C,UAAU,EAAEgB,SAAS,EAAEkB,wBAAwB,CAAC;MAC7E;MAEA,OAAO,EAAE;IACX,CAAC,CAAC;IACEO,gBAAgB,GAAGtH,cAAc,CAACoH,eAAe,EAAE,CAAC,CAAC;IACrDG,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE9C,IAAIG,kBAAkB,GAAGvH,KAAK,CAACsF,OAAO,CAAC,YAAY;IACjD,OAAO,IAAIkC,GAAG,CAACxB,eAAe,IAAIqB,iBAAiB,IAAI,EAAE,CAAC;EAC5D,CAAC,EAAE,CAACrB,eAAe,EAAEqB,iBAAiB,CAAC,CAAC;EACxC,IAAII,eAAe,GAAGzH,KAAK,CAACwF,WAAW,CAAC,UAAUI,MAAM,EAAE;IACxD,IAAIC,GAAG,GAAGF,SAAS,CAACC,MAAM,EAAEjB,UAAU,CAAC+C,OAAO,CAAC9B,MAAM,CAAC,CAAC;IACvD,IAAI+B,eAAe;IACnB,IAAIC,MAAM,GAAGL,kBAAkB,CAACM,GAAG,CAAChC,GAAG,CAAC;IAExC,IAAI+B,MAAM,EAAE;MACVL,kBAAkB,CAACO,MAAM,CAACjC,GAAG,CAAC;MAC9B8B,eAAe,GAAG9H,kBAAkB,CAAC0H,kBAAkB,CAAC;IAC1D,CAAC,MAAM;MACLI,eAAe,GAAG,EAAE,CAACvC,MAAM,CAACvF,kBAAkB,CAAC0H,kBAAkB,CAAC,EAAE,CAAC1B,GAAG,CAAC,CAAC;IAC5E;IAEAyB,oBAAoB,CAACK,eAAe,CAAC;IAErC,IAAIvB,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAACwB,MAAM,EAAEhC,MAAM,CAAC;IAC3B;IAEA,IAAIS,oBAAoB,EAAE;MACxBA,oBAAoB,CAACsB,eAAe,CAAC;IACvC;EACF,CAAC,EAAE,CAAChC,SAAS,EAAE4B,kBAAkB,EAAE5C,UAAU,EAAEyB,QAAQ,EAAEC,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAEjF,IAAIvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAImB,iBAAiB,IAAIxB,UAAU,CAACsC,IAAI,CAAC,UAAUrB,MAAM,EAAE;IAClG,OAAOmC,KAAK,CAACC,OAAO,CAACpC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACiB,wBAAwB,CAAC,CAAC;EACxG,CAAC,CAAC,EAAE;IACFvG,OAAO,CAAC,KAAK,EAAE,sDAAsD,CAAC;EACxE,CAAC,CAAC;;EAGF,IAAI2H,gBAAgB,GAAGjI,KAAK,CAACmH,QAAQ,CAAC,CAAC,CAAC;IACpCe,gBAAgB,GAAGpI,cAAc,CAACmI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,WAAW,GAAGtH,UAAU,CAACnB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,KAAK,CAAC,EAAE8C,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MACxGiB,UAAU,EAAE,CAAC,CAACZ,iBAAiB;MAC/BmC,YAAY,EAAEf,kBAAkB;MAChC5B,SAAS,EAAEA,SAAS;MACpB;MACA8B,eAAe,EAAEA,eAAe;MAChC1B,UAAU,EAAEa,gBAAgB;MAC5BJ,qBAAqB,EAAEA,qBAAqB;MAC5C3C,SAAS,EAAEA;IACb,CAAC,CAAC,EAAEU,aAAa,KAAK9B,cAAc,GAAG+B,gBAAgB,GAAG,IAAI,CAAC;IAC3D+D,YAAY,GAAGzI,cAAc,CAACuI,WAAW,EAAE,CAAC,CAAC;IAC7CG,OAAO,GAAGD,YAAY,CAAC,CAAC,CAAC;IACzBE,cAAc,GAAGF,YAAY,CAAC,CAAC,CAAC;EAEpC,IAAIG,aAAa,GAAG1I,KAAK,CAACsF,OAAO,CAAC,YAAY;IAC5C,OAAO;MACLkD,OAAO,EAAEA,OAAO;MAChBC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACD,OAAO,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIE,YAAY,GAAG3I,KAAK,CAAC4I,MAAM,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAG7I,KAAK,CAAC4I,MAAM,CAAC,CAAC;EACpC,IAAIE,aAAa,GAAG9I,KAAK,CAAC4I,MAAM,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAG/I,KAAK,CAAC4I,MAAM,CAAC,CAAC;EAErC,IAAII,gBAAgB,GAAGhJ,KAAK,CAACmH,QAAQ,CAAC,KAAK,CAAC;IACxC8B,gBAAgB,GAAGnJ,cAAc,CAACkJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvC,IAAIG,gBAAgB,GAAGpJ,KAAK,CAACmH,QAAQ,CAAC,KAAK,CAAC;IACxCkC,gBAAgB,GAAGvJ,cAAc,CAACsJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,eAAe,GAAGxI,cAAc,CAAC,IAAIyI,GAAG,CAAC,CAAC,CAAC;IAC3CC,gBAAgB,GAAG5J,cAAc,CAAC0J,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAIG,QAAQ,GAAGxI,aAAa,CAACoH,cAAc,CAAC;EAC5C,IAAIqB,aAAa,GAAGD,QAAQ,CAACE,GAAG,CAAC,UAAUC,SAAS,EAAE;IACpD,OAAOL,UAAU,CAACM,GAAG,CAACD,SAAS,CAAC;EAClC,CAAC,CAAC;EACF,IAAIE,SAAS,GAAGlK,KAAK,CAACsF,OAAO,CAAC,YAAY;IACxC,OAAOwE,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAIC,aAAa,GAAG7I,gBAAgB,CAAC2I,SAAS,EAAEzB,cAAc,CAAC5D,MAAM,EAAEhB,SAAS,CAAC;EACjF,IAAIwG,SAAS,GAAG1G,MAAM,IAAIvC,aAAa,CAACuC,MAAM,CAAC2G,CAAC,CAAC;EACjD,IAAIC,aAAa,GAAG5G,MAAM,IAAIvC,aAAa,CAACuC,MAAM,CAAC6G,CAAC,CAAC,IAAIC,OAAO,CAAC3E,gBAAgB,CAAC4E,KAAK,CAAC;EACxF,IAAIC,SAAS,GAAGJ,aAAa,IAAI9B,cAAc,CAACxB,IAAI,CAAC,UAAU2D,KAAK,EAAE;IACpE,IAAIF,KAAK,GAAGE,KAAK,CAACF,KAAK;IACvB,OAAOA,KAAK;EACd,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIG,SAAS,GAAG7K,KAAK,CAAC4I,MAAM,CAAC,CAAC;EAE9B,IAAIkC,UAAU,GAAG7I,SAAS,CAACyC,MAAM,EAAErB,SAAS,CAAC;IACzC0H,QAAQ,GAAGD,UAAU,CAACC,QAAQ;IAC9BC,YAAY,GAAGF,UAAU,CAACE,YAAY;IACtCC,aAAa,GAAGH,UAAU,CAACG,aAAa;IACxCC,YAAY,GAAGJ,UAAU,CAACI,YAAY;IACtCC,eAAe,GAAGL,UAAU,CAACK,eAAe;IAC5CC,SAAS,GAAGN,UAAU,CAACM,SAAS,CAAC,CAAC;;EAGtC,IAAIC,WAAW,GAAGrH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,UAAU,CAAC;EACvF,IAAI2G,SAAS,GAAG,CAACjB,SAAS,IAAIU,QAAQ,KAAK,aAAa/K,KAAK,CAACuL,cAAc,CAACF,WAAW,CAAC,IAAIA,WAAW,CAACG,IAAI,KAAKrJ,OAAO,IAAIkJ,WAAW,CAACrI,KAAK,CAAC0H,KAAK,CAAC,CAAC;;EAEtJ,IAAIe,YAAY;EAChB,IAAIC,YAAY;EAChB,IAAIC,gBAAgB;EAEpB,IAAItB,SAAS,EAAE;IACbqB,YAAY,GAAG;MACbE,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAElI,MAAM,CAAC2G;IACpB,CAAC;EACH;EAEA,IAAIC,aAAa,EAAE;IACjBkB,YAAY,GAAG;MACbK,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;IACH;IACA;;IAEA,IAAI,CAACzB,SAAS,EAAE;MACdqB,YAAY,GAAG;QACbE,SAAS,EAAE;MACb,CAAC;IACH;IAEAD,gBAAgB,GAAG;MACjBI,KAAK,EAAE,CAACpI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6G,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG7G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6G,CAAC;MAC9IwB,QAAQ,EAAE;IACZ,CAAC;EACH;EAEA,IAAIC,cAAc,GAAGjM,KAAK,CAACwF,WAAW,CAAC,UAAUwE,SAAS,EAAE+B,KAAK,EAAE;IACjE,IAAI9L,SAAS,CAAC0I,YAAY,CAACuD,OAAO,CAAC,EAAE;MACnCtC,gBAAgB,CAAC,UAAUuC,MAAM,EAAE;QACjC,IAAIA,MAAM,CAAClC,GAAG,CAACD,SAAS,CAAC,KAAK+B,KAAK,EAAE;UACnC,IAAIK,SAAS,GAAG,IAAI3C,GAAG,CAAC0C,MAAM,CAAC;UAC/BC,SAAS,CAACC,GAAG,CAACrC,SAAS,EAAE+B,KAAK,CAAC;UAC/B,OAAOK,SAAS;QAClB;QAEA,OAAOD,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIG,eAAe,GAAGrL,cAAc,CAAC,IAAI,CAAC;IACtCsL,gBAAgB,GAAGzM,cAAc,CAACwM,eAAe,EAAE,CAAC,CAAC;IACrDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,SAASG,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACvC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAACD,UAAU,CAAC;IACpB,CAAC,MAAM,IAAIC,MAAM,CAACD,UAAU,KAAKA,UAAU,EAAE;MAC3C;MACAC,MAAM,CAACD,UAAU,GAAGA,UAAU;IAChC;EACF;EAEA,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;MACnCJ,UAAU,GAAGG,KAAK,CAACH,UAAU;IACjC,IAAIK,KAAK,GAAGnJ,SAAS,KAAK,KAAK;IAC/B,IAAIoJ,gBAAgB,GAAG,OAAON,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGI,aAAa,CAACJ,UAAU;IAC7F,IAAIO,aAAa,GAAGH,aAAa,IAAIvK,mBAAmB;IAExD,IAAI,CAACiK,eAAe,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,KAAKS,aAAa,EAAE;MAC7D,IAAIC,kBAAkB;MAEtBX,eAAe,CAACU,aAAa,CAAC;MAC9BR,WAAW,CAACO,gBAAgB,EAAEpE,eAAe,CAACqD,OAAO,CAAC;MACtDQ,WAAW,CAACO,gBAAgB,EAAEnE,aAAa,CAACoD,OAAO,CAAC;MACpDQ,WAAW,CAACO,gBAAgB,EAAElE,gBAAgB,CAACmD,OAAO,CAAC;MACvDQ,WAAW,CAACO,gBAAgB,EAAE,CAACE,kBAAkB,GAAGtC,SAAS,CAACqB,OAAO,MAAM,IAAI,IAAIiB,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,aAAa,CAAC;IAC/J;IAEA,IAAIL,aAAa,EAAE;MACjB,IAAIM,WAAW,GAAGN,aAAa,CAACM,WAAW;QACvCC,WAAW,GAAGP,aAAa,CAACO,WAAW,CAAC,CAAC;;MAE7C,IAAID,WAAW,KAAKC,WAAW,EAAE;QAC/B;MACF;MAEA,IAAIN,KAAK,EAAE;QACT7D,aAAa,CAAC,CAAC8D,gBAAgB,GAAGI,WAAW,GAAGC,WAAW,CAAC;QAC5D/D,cAAc,CAAC,CAAC0D,gBAAgB,GAAG,CAAC,CAAC;MACvC,CAAC,MAAM;QACL9D,aAAa,CAAC8D,gBAAgB,GAAG,CAAC,CAAC;QACnC1D,cAAc,CAAC0D,gBAAgB,GAAGI,WAAW,GAAGC,WAAW,CAAC;MAC9D;IACF;EACF,CAAC;EAED,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIhD,aAAa,IAAIzB,aAAa,CAACoD,OAAO,EAAE;MAC1CW,QAAQ,CAAC;QACPE,aAAa,EAAEjE,aAAa,CAACoD;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL/C,aAAa,CAAC,KAAK,CAAC;MACpBI,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,IAAIiE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAI1B,KAAK,GAAG0B,KAAK,CAAC1B,KAAK;IAEvB,IAAIA,KAAK,KAAK5D,cAAc,EAAE;MAC5BoF,eAAe,CAAC,CAAC;MACjBnF,iBAAiB,CAACO,YAAY,CAACuD,OAAO,GAAGvD,YAAY,CAACuD,OAAO,CAACwB,WAAW,GAAG3B,KAAK,CAAC;IACpF;EACF,CAAC,CAAC,CAAC;;EAGH,IAAI4B,OAAO,GAAG3N,KAAK,CAAC4I,MAAM,CAAC,KAAK,CAAC;EACjC5I,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1B;IACA;IACA,IAAID,OAAO,CAACzB,OAAO,EAAE;MACnBqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChD,aAAa,EAAE9G,IAAI,EAAE+E,OAAO,CAAC3D,MAAM,CAAC,CAAC;EACzC7E,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1BD,OAAO,CAACzB,OAAO,GAAG,IAAI;EACxB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAI2B,gBAAgB,GAAG7N,KAAK,CAACmH,QAAQ,CAAC,CAAC,CAAC;IACpC2G,iBAAiB,GAAGhO,cAAc,CAAC+N,gBAAgB,EAAE,CAAC,CAAC;IACvDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE3C,IAAIG,iBAAiB,GAAGjO,KAAK,CAACmH,QAAQ,CAAC,IAAI,CAAC;IACxC+G,iBAAiB,GAAGpO,cAAc,CAACmO,iBAAiB,EAAE,CAAC,CAAC;IACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG7ClO,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1BI,gBAAgB,CAACxN,sBAAsB,CAACsI,aAAa,CAACoD,OAAO,CAAC,CAACH,KAAK,CAAC;IACrEqC,gBAAgB,CAACjO,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERH,KAAK,CAAC4N,SAAS,CAAC,YAAY;IAC1B,IAAIrJ,aAAa,KAAK9B,cAAc,IAAIgC,YAAY,EAAE;MACpDA,YAAY,CAAC4J,IAAI,CAACnC,OAAO,GAAGpD,aAAa,CAACoD,OAAO;IACnD;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIoC,cAAc,GAAG/I,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;;EAEvD,IAAIgJ,iBAAiB,GAAGvO,KAAK,CAACsF,OAAO,CAAC,YAAY;IAChD,IAAI1B,WAAW,EAAE;MACf,OAAOA,WAAW;IACpB,CAAC,CAAC;IACF;IACA;;IAGA,IAAI+G,SAAS,EAAE;MACb,OAAO,CAAChH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6G,CAAC,MAAM,aAAa,GAAG,MAAM,GAAG,OAAO;IACxG;IAEA,IAAIH,SAAS,IAAIU,QAAQ,IAAItC,cAAc,CAACxB,IAAI,CAAC,UAAUuH,KAAK,EAAE;MAChE,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EAAE;MACF,OAAO,OAAO;IAChB;IAEA,OAAO,MAAM;EACf,CAAC,EAAE,CAACpE,SAAS,EAAEM,SAAS,EAAElC,cAAc,EAAE7E,WAAW,EAAEmH,QAAQ,CAAC,CAAC;EACjE,IAAI2D,cAAc,CAAC,CAAC;;EAEpB,IAAIC,WAAW,GAAG;IAChBzE,SAAS,EAAEA,SAAS;IACpB0E,UAAU,EAAEnG,cAAc,CAAC5D,MAAM;IACjCuF,aAAa,EAAEA,aAAa;IAC5B9F,WAAW,EAAEA,WAAW;IACxB+F,SAAS,EAAEA,SAAS;IACpB1G,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;;EAEH,IAAIkL,SAAS,GAAG7O,KAAK,CAACsF,OAAO,CAAC,YAAY;IACxC,IAAIV,OAAO,EAAE;MACX,OAAO,IAAI;IACb;IAEA,IAAI,OAAOR,SAAS,KAAK,UAAU,EAAE;MACnC,OAAOA,SAAS,CAAC,CAAC;IACpB;IAEA,OAAOA,SAAS;EAClB,CAAC,EAAE,CAACQ,OAAO,EAAER,SAAS,CAAC,CAAC,CAAC,CAAC;;EAE1B,IAAI0K,SAAS,GAAG,aAAa9O,KAAK,CAAC+O,aAAa,CAACjO,IAAI,EAAE;IACrD2C,IAAI,EAAEkB,UAAU;IAChBqK,kBAAkB,EAAE3E,SAAS,IAAIE,aAAa,IAAIQ,QAAQ;IAC1DzC,YAAY,EAAEf,kBAAkB;IAChChB,aAAa,EAAEA,aAAa;IAC5BZ,SAAS,EAAEA,SAAS;IACpBtB,KAAK,EAAEA,KAAK;IACZwK,SAAS,EAAEA,SAAS;IACpBnI,kBAAkB,EAAEG;EACtB,CAAC,CAAC;EACF,IAAIoI,YAAY,GAAG,aAAajP,KAAK,CAAC+O,aAAa,CAACvN,QAAQ,EAAE;IAC5D0I,SAAS,EAAEzB,cAAc,CAACsB,GAAG,CAAC,UAAUmF,KAAK,EAAE;MAC7C,IAAInD,KAAK,GAAGmD,KAAK,CAACnD,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACFvD,OAAO,EAAEC;EACX,CAAC,CAAC;EACF,IAAI0G,mBAAmB,GAAG5J,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;EAEhD,IAAIT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOmK,mBAAmB,KAAK,UAAU,IAAIvK,OAAO,IAAI,CAACyF,SAAS,EAAE;IAC/G/J,OAAO,CAAC,KAAK,EAAE,iEAAiE,CAAC;EACnF;EAEA,IAAI+J,SAAS,IAAIU,QAAQ,EAAE;IACzB;IACA,IAAIqE,WAAW;IAEf,IAAI,OAAOD,mBAAmB,KAAK,UAAU,EAAE;MAC7CC,WAAW,GAAGD,mBAAmB,CAACxK,UAAU,EAAE;QAC5CoJ,aAAa,EAAEA,aAAa;QAC5BsB,GAAG,EAAEvG,aAAa;QAClB+D,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF8B,WAAW,CAACzE,SAAS,GAAGzB,cAAc,CAACsB,GAAG,CAAC,UAAUuF,KAAK,EAAEC,KAAK,EAAE;QACjE,IAAIxD,KAAK,GAAGuD,KAAK,CAACvD,KAAK;QACvB,IAAIyD,QAAQ,GAAGD,KAAK,KAAK/G,OAAO,CAAC3D,MAAM,GAAG,CAAC,GAAGkH,KAAK,GAAGgC,aAAa,GAAGhC,KAAK;QAE3E,IAAI,OAAOyD,QAAQ,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC,EAAE;UAC3D,OAAOA,QAAQ;QACjB;QAEAlP,OAAO,CAAC,KAAK,EAAE,8FAA8F,CAAC;QAC9G,OAAO,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL8O,WAAW,GAAG,aAAapP,KAAK,CAAC+O,aAAa,CAAC,KAAK,EAAE;QACpDvL,KAAK,EAAE5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6L,YAAY,CAAC,EAAEC,YAAY,CAAC;QACnEmB,QAAQ,EAAEA,QAAQ;QAClBwC,GAAG,EAAEvG,aAAa;QAClBxF,SAAS,EAAElD,UAAU,CAAC,EAAE,CAACgF,MAAM,CAAC/B,SAAS,EAAE,OAAO,CAAC;MACrD,CAAC,EAAE,aAAarD,KAAK,CAAC+O,aAAa,CAACT,cAAc,EAAE;QAClD9K,KAAK,EAAE5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+L,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5D/H,WAAW,EAAE2K;QACf,CAAC;MACH,CAAC,EAAEU,YAAY,EAAEH,SAAS,EAAE,CAACxD,SAAS,IAAID,WAAW,IAAI,aAAarL,KAAK,CAAC+O,aAAa,CAACpN,MAAM,EAAE;QAChGyI,aAAa,EAAEA,aAAa;QAC5B3B,cAAc,EAAEA;MAClB,CAAC,EAAE4C,WAAW,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC;;IAGF,IAAIsE,gBAAgB,GAAG/P,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC/DgQ,MAAM,EAAE,CAACjL,UAAU,CAACE,MAAM;MAC1BgL,gBAAgB,EAAEtF,aAAa,IAAI5G,MAAM,CAAC6G,CAAC,KAAK;IAClD,CAAC,EAAEmE,WAAW,CAAC,EAAEjG,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACnC7E,SAAS,EAAEA,SAAS;MACpBsH,eAAe,EAAEA,eAAe;MAChC0B,QAAQ,EAAEA;IACZ,CAAC,CAAC;IAEF6B,cAAc,GAAG,aAAa1O,KAAK,CAAC+O,aAAa,CAAC/O,KAAK,CAAC8P,QAAQ,EAAE,IAAI,EAAE5L,UAAU,KAAK,KAAK,IAAI,aAAalE,KAAK,CAAC+O,aAAa,CAAC7M,WAAW,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAEgQ,gBAAgB,EAAE;MAC3KI,eAAe,EAAE/E,YAAY;MAC7B1H,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,SAAS,CAAC;MAC1CgM,GAAG,EAAExG;IACP,CAAC,CAAC,EAAE,UAAUmH,oBAAoB,EAAE;MAClC,OAAO,aAAahQ,KAAK,CAAC+O,aAAa,CAAC/O,KAAK,CAAC8P,QAAQ,EAAE,IAAI,EAAE,aAAa9P,KAAK,CAAC+O,aAAa,CAACpO,MAAM,EAAEqP,oBAAoB,CAAC,EAAE1E,SAAS,KAAK,KAAK,IAAI,aAAatL,KAAK,CAAC+O,aAAa,CAACpN,MAAM,EAAEqO,oBAAoB,EAAE3E,WAAW,CAAC,CAAC;IACnO,CAAC,CAAC,EAAE+D,WAAW,EAAE9D,SAAS,IAAIA,SAAS,KAAK,KAAK,IAAI,aAAatL,KAAK,CAAC+O,aAAa,CAAC7M,WAAW,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAEgQ,gBAAgB,EAAE;MAChIM,kBAAkB,EAAEhF,aAAa;MACjC3H,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC;MAC3CgM,GAAG,EAAEtG;IACP,CAAC,CAAC,EAAE,UAAUiH,oBAAoB,EAAE;MAClC,OAAO,aAAahQ,KAAK,CAAC+O,aAAa,CAACpN,MAAM,EAAEqO,oBAAoB,EAAE3E,WAAW,CAAC;IACpF,CAAC,CAAC,EAAEN,QAAQ,IAAI,aAAa/K,KAAK,CAAC+O,aAAa,CAAC/M,eAAe,EAAE;MAChEqN,GAAG,EAAExE,SAAS;MACdK,YAAY,EAAEA,YAAY;MAC1BpC,aAAa,EAAEA,aAAa;MAC5B+D,QAAQ,EAAEA,QAAQ;MAClBzB,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL;IACAsD,cAAc,GAAG,aAAa1O,KAAK,CAAC+O,aAAa,CAAC,KAAK,EAAE;MACvDvL,KAAK,EAAE5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6L,YAAY,CAAC,EAAEC,YAAY,CAAC;MACnEpI,SAAS,EAAElD,UAAU,CAAC,EAAE,CAACgF,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC,CAAC;MACvDwJ,QAAQ,EAAEA,QAAQ;MAClBwC,GAAG,EAAEvG;IACP,CAAC,EAAE,aAAa9I,KAAK,CAAC+O,aAAa,CAACT,cAAc,EAAE;MAClD9K,KAAK,EAAE5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+L,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5D/H,WAAW,EAAE2K;MACf,CAAC;IACH,CAAC,EAAEU,YAAY,EAAE/K,UAAU,KAAK,KAAK,IAAI,aAAalE,KAAK,CAAC+O,aAAa,CAACpO,MAAM,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAEgP,WAAW,EAAEjG,aAAa,CAAC,CAAC,EAAEoG,SAAS,EAAEzD,WAAW,IAAI,aAAarL,KAAK,CAAC+O,aAAa,CAACpN,MAAM,EAAE;MAC9LyI,aAAa,EAAEA,aAAa;MAC5B3B,cAAc,EAAEA;IAClB,CAAC,EAAE4C,WAAW,CAAC,CAAC,CAAC;EACnB;EAEA,IAAI6E,SAAS,GAAGhQ,SAAS,CAAC8C,KAAK,EAAE;IAC/BmN,IAAI,EAAE,IAAI;IACV1M,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI2M,SAAS,GAAG,aAAapQ,KAAK,CAAC+O,aAAa,CAAC,KAAK,EAAEpP,QAAQ,CAAC;IAC/D2D,SAAS,EAAElD,UAAU,CAACiD,SAAS,EAAEC,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAE1D,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,MAAM,CAAC,EAAEQ,SAAS,KAAK,KAAK,CAAC,EAAEnE,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,YAAY,CAAC,EAAE6F,UAAU,CAAC,EAAExJ,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,aAAa,CAAC,EAAEiG,WAAW,CAAC,EAAE5J,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEO,WAAW,KAAK,OAAO,CAAC,EAAElE,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEgH,SAAS,CAAC,EAAE3K,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEsH,SAAS,CAAC,EAAEjL,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,oBAAoB,CAAC,EAAEkH,aAAa,CAAC,EAAE7K,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEoF,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,CAACiC,KAAK,CAAC,EAAEhL,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC/B,SAAS,EAAE,gBAAgB,CAAC,EAAEoF,cAAc,CAACA,cAAc,CAAC5D,MAAM,GAAG,CAAC,CAAC,IAAI4D,cAAc,CAACA,cAAc,CAAC5D,MAAM,GAAG,CAAC,CAAC,CAAC6F,KAAK,KAAK,OAAO,CAAC,EAAEtH,WAAW,CAAC,CAAC;IACr7BI,KAAK,EAAEA,KAAK;IACZS,EAAE,EAAEA,EAAE;IACNoL,GAAG,EAAE1G;EACP,CAAC,EAAEuH,SAAS,CAAC,EAAE,aAAalQ,KAAK,CAAC+O,aAAa,CAACrM,gBAAgB,EAAE;IAChEO,QAAQ,EAAEiG,UAAU;IACpBhG,SAAS,EAAEoG,WAAW;IACtBtG,KAAK,EAAEpD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDoH,aAAa,EAAEA,aAAa;MAC5B7C,kBAAkB,EAAEA;IACtB,CAAC;EACH,CAAC,EAAEzD,KAAK,IAAI,aAAa9D,KAAK,CAAC+O,aAAa,CAACrN,KAAK,EAAE;IAClD4B,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAES,KAAK,CAACa,UAAU,CAAC,CAAC,EAAE,aAAa3E,KAAK,CAAC+O,aAAa,CAAC,KAAK,EAAE;IAC7DzL,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAEqL,cAAc,CAAC,EAAE3K,MAAM,IAAI,aAAa/D,KAAK,CAAC+O,aAAa,CAACrN,KAAK,EAAE;IACpE4B,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAAC/B,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEU,MAAM,CAACY,UAAU,CAAC,CAAC,CAAC,CAAC;EAExB,IAAI4F,aAAa,EAAE;IACjB6F,SAAS,GAAG,aAAapQ,KAAK,CAAC+O,aAAa,CAACxO,cAAc,EAAE;MAC3D8P,QAAQ,EAAE7C;IACZ,CAAC,EAAE4C,SAAS,CAAC;EACf;EAEA,IAAIE,iBAAiB,GAAGtQ,KAAK,CAACsF,OAAO,CAAC,YAAY;IAChD,OAAO;MACLjC,SAAS,EAAEA,SAAS;MACpBkC,YAAY,EAAEA,YAAY;MAC1BwI,aAAa,EAAEA,aAAa;MAC5BlK,SAAS,EAAEA,SAAS;MACpB0M,aAAa,EAAE9H,cAAc,CAACsB,GAAG,CAAC,UAAUyG,CAAC,EAAEC,QAAQ,EAAE;QACvD,OAAO1O,gBAAgB,CAAC0O,QAAQ,EAAEA,QAAQ,EAAEhI,cAAc,EAAE2B,aAAa,EAAEvG,SAAS,CAAC;MACvF,CAAC,CAAC;MACFkH,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,EAAE,CAAC1H,SAAS,EAAEkC,YAAY,EAAEwI,aAAa,EAAElK,SAAS,EAAE4E,cAAc,EAAE2B,aAAa,EAAEvG,SAAS,EAAEkH,QAAQ,CAAC,CAAC;EAC3G,IAAI2F,gBAAgB,GAAG1Q,KAAK,CAACsF,OAAO,CAAC,YAAY;IAC/C,OAAO1F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8I,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzD9E,WAAW,EAAE2K,iBAAiB;MAC9BhL,YAAY,EAAEA,YAAY;MAC1BkD,oBAAoB,EAAEA,oBAAoB;MAC1CV,UAAU,EAAEa,gBAAgB;MAC5BE,cAAc,EAAEA,cAAc;MAC9BR,gBAAgB,EAAEA,gBAAgB;MAClCH,iBAAiB,EAAEA,iBAAiB;MACpCsB,eAAe,EAAEA,eAAe;MAChCjB,qBAAqB,EAAEA,qBAAqB;MAC5CG,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC+B,aAAa,EAAE6F,iBAAiB,EAAEhL,YAAY,EAAEkD,oBAAoB,EAAEG,gBAAgB,EAAEE,cAAc,EAAER,gBAAgB,EAAEH,iBAAiB,EAAEsB,eAAe,EAAEjB,qBAAqB,EAAEG,UAAU,CAAC,CAAC;EACrM,IAAIgK,uBAAuB,GAAG3Q,KAAK,CAACsF,OAAO,CAAC,YAAY;IACtD,OAAO;MACL6C,cAAc,EAAEA,cAAc;MAC9BkC,SAAS,EAAEA,SAAS;MACpBM,SAAS,EAAEA,SAAS;MACpBJ,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC,EAAE,CAACpC,cAAc,EAAEkC,SAAS,EAAEM,SAAS,EAAEJ,aAAa,CAAC,CAAC;EACzD,IAAIqG,kBAAkB,GAAG5Q,KAAK,CAACsF,OAAO,CAAC,YAAY;IACjD,OAAO;MACL2G,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpB,OAAO,aAAajM,KAAK,CAAC+O,aAAa,CAAC3M,aAAa,CAACyO,QAAQ,EAAE;IAC9DC,KAAK,EAAE3C;EACT,CAAC,EAAE,aAAanO,KAAK,CAAC+O,aAAa,CAACnO,YAAY,CAACiQ,QAAQ,EAAE;IACzDC,KAAK,EAAER;EACT,CAAC,EAAE,aAAatQ,KAAK,CAAC+O,aAAa,CAAClO,WAAW,CAACgQ,QAAQ,EAAE;IACxDC,KAAK,EAAEJ;EACT,CAAC,EAAE,aAAa1Q,KAAK,CAAC+O,aAAa,CAAC1M,kBAAkB,CAACwO,QAAQ,EAAE;IAC/DC,KAAK,EAAEH;EACT,CAAC,EAAE,aAAa3Q,KAAK,CAAC+O,aAAa,CAACzN,aAAa,CAACuP,QAAQ,EAAE;IAC1DC,KAAK,EAAEF;EACT,CAAC,EAAER,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;AAEAjN,KAAK,CAACb,aAAa,GAAGA,aAAa;AACnCa,KAAK,CAACzC,MAAM,GAAGA,MAAM;AACrByC,KAAK,CAAC1C,WAAW,GAAGA,WAAW;AAC/B0C,KAAK,CAAChB,OAAO,GAAGP,gBAAgB;AAChCuB,KAAK,CAAC4N,YAAY,GAAG;EACnBrN,MAAM,EAAE,KAAK;EACbL,SAAS,EAAE,UAAU;EACrBe,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,SAAS;EAClB;AACF,CAAC;AACD,eAAejB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}