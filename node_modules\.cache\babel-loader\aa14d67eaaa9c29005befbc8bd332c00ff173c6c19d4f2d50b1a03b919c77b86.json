{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiDocInventario.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Dropdown } from 'primereact/dropdown';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggiungiDocIventario = props => {\n  _s();\n  const [results, setResults] = useState([]);\n  const [magazzino, setMagazzino] = useState(null);\n  const [displayed, setDisplayed] = useState(true);\n  const toast = useRef(null);\n  useEffect(() => {\n    async function trovaRisultato() {\n      await APIRequest(\"GET\", \"warehouses/\").then(res => {\n        var magazzini = [];\n        res.data.forEach(element => {\n          var x = {\n            name: element.warehouseName,\n            code: element.id\n          };\n          magazzini.push(x);\n        });\n        setResults(magazzini);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, [props]);\n  const selMag = async e => {\n    setMagazzino(e.target.value);\n    var url = 'productsposition/?idWarehouse=' + e.target.value.code;\n    //Chiamata axios per la visualizzazione dei registry\n    await APIRequest('GET', url).then(async res => {\n      res.data.forEach(el => {\n        el.colliPreventivo = el.colli;\n        delete el.colli;\n        delete el.id;\n      });\n      var body = {\n        type: 'INVENTORY',\n        documentDate: new Date(),\n        rowBody: res.data\n      };\n      let url = \"documents/?idWarehouses=\" + props.selectedWarehouse;\n      //Chiamata axios per la creazione del documento\n      await APIRequest('POST', url, body).then(res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: 'Ottimo',\n          detail: \"Il documento è stato inserito con successo\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        toast.current.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  };\n  const crea = async e => {\n    var body = {\n      type: 'INVENTORY',\n      documentDate: new Date(),\n      rowBody: []\n    };\n    let url = \"documents/?idWarehouses=\" + props.selectedWarehouse;\n    //Chiamata axios per la creazione del documento\n    await APIRequest('POST', url, body).then(res => {\n      console.log(res.data);\n      toast.current.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"Il documento è stato inserito con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this), displayed && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-6\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: e => crea(e),\n          children: \"Documento vuoto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-6\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: e => setDisplayed(false),\n          children: \"Documento con prodotti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 17\n    }, this), displayed === false && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(Dropdown, {\n        value: magazzino,\n        options: results,\n        onChange: e => selMag(e),\n        optionLabel: \"name\",\n        placeholder: \"Selecziona magazzino\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiDocIventario, \"TIAIizmae9/g3NKL3JOgp4Lp1qY=\");\n_c = AggiungiDocIventario;\nvar _c;\n$RefreshReg$(_c, \"AggiungiDocIventario\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Toast", "<PERSON><PERSON>", "APIRequest", "Dropdown", "stopLoading", "jsxDEV", "_jsxDEV", "AggiungiDocIventario", "props", "_s", "results", "setResults", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayed", "setDisplayed", "toast", "trovaRisultato", "then", "res", "<PERSON><PERSON><PERSON><PERSON>", "data", "for<PERSON>ach", "element", "x", "name", "warehouseName", "code", "id", "push", "catch", "e", "console", "log", "selMag", "target", "value", "url", "el", "colliPreventivo", "colli", "body", "type", "documentDate", "Date", "rowBody", "selectedWarehouse", "current", "show", "severity", "summary", "detail", "life", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "concat", "response", "undefined", "message", "crea", "_e$response3", "_e$response4", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "options", "onChange", "optionLabel", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiDocInventario.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Dropdown } from 'primereact/dropdown';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\n\nexport const AggiungiDocIventario = (props) => {\n    const [results, setResults] = useState([]);\n    const [magazzino, setMagazzino] = useState(null)\n    const [displayed, setDisplayed] = useState(true);\n    const toast = useRef(null);\n\n    useEffect(() => {\n        async function trovaRisultato() {\n            await APIRequest(\"GET\", \"warehouses/\")\n                .then(res => {\n                    var magazzini = []\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.warehouseName,\n                            code: element.id\n                        }\n                        magazzini.push(x)\n                    })\n                    setResults(magazzini);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, [props])\n\n    const selMag = async (e) => {\n        setMagazzino(e.target.value)\n        var url = 'productsposition/?idWarehouse=' + e.target.value.code\n        //Chiamata axios per la visualizzazione dei registry\n        await APIRequest('GET', url)\n            .then(async res => {\n                res.data.forEach(el => {\n                    el.colliPreventivo = el.colli\n                    delete el.colli\n                    delete el.id\n                })\n                var body = {\n                    type: 'INVENTORY',\n                    documentDate: new Date(),\n                    rowBody: res.data\n                }\n                let url = \"documents/?idWarehouses=\" + props.selectedWarehouse\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', url, body)\n                    .then(res => {\n                        console.log(res.data);\n                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n\n    const crea = async (e) => {\n        var body = {\n            type: 'INVENTORY',\n            documentDate: new Date(),\n            rowBody: []\n        }\n        let url = \"documents/?idWarehouses=\" + props.selectedWarehouse\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', url, body)\n            .then(res => {\n                console.log(res.data);\n                toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            {displayed &&\n                <div className='row'>\n                    <div className='col-6'>\n                        <Button onClick={(e) => crea(e)}>Documento vuoto</Button>\n                    </div>\n                    <div className='col-6'>\n                        <Button onClick={(e) => setDisplayed(false)}>Documento con prodotti</Button>\n                    </div>\n                </div>\n            }\n            {displayed === false &&\n                <div>\n                    <Dropdown value={magazzino} options={results} onChange={(e) => selMag(e)} optionLabel=\"name\" placeholder=\"Selecziona magazzino\" />\n                </div>\n            }\n        </div>\n    )\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,WAAW,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,OAAO,MAAMC,oBAAoB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC3C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMiB,KAAK,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAE1BD,SAAS,CAAC,MAAM;IACZ,eAAeoB,cAAcA,CAAA,EAAG;MAC5B,MAAMf,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCgB,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAG;YACJC,IAAI,EAAEF,OAAO,CAACG,aAAa;YAC3BC,IAAI,EAAEJ,OAAO,CAACK;UAClB,CAAC;UACDR,SAAS,CAACS,IAAI,CAACL,CAAC,CAAC;QACrB,CAAC,CAAC;QACFb,UAAU,CAACS,SAAS,CAAC;MACzB,CAAC,CAAC,CAACU,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACN3B,WAAW,CAAC,CAAC;IACjB;IACAa,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACT,KAAK,CAAC,CAAC;EAEX,MAAM0B,MAAM,GAAG,MAAOH,CAAC,IAAK;IACxBlB,YAAY,CAACkB,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC;IAC5B,IAAIC,GAAG,GAAG,gCAAgC,GAAGN,CAAC,CAACI,MAAM,CAACC,KAAK,CAACT,IAAI;IAChE;IACA,MAAMzB,UAAU,CAAC,KAAK,EAAEmC,GAAG,CAAC,CACvBnB,IAAI,CAAC,MAAMC,GAAG,IAAI;MACfA,GAAG,CAACE,IAAI,CAACC,OAAO,CAACgB,EAAE,IAAI;QACnBA,EAAE,CAACC,eAAe,GAAGD,EAAE,CAACE,KAAK;QAC7B,OAAOF,EAAE,CAACE,KAAK;QACf,OAAOF,EAAE,CAACV,EAAE;MAChB,CAAC,CAAC;MACF,IAAIa,IAAI,GAAG;QACPC,IAAI,EAAE,WAAW;QACjBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC;QACxBC,OAAO,EAAE1B,GAAG,CAACE;MACjB,CAAC;MACD,IAAIgB,GAAG,GAAG,0BAA0B,GAAG7B,KAAK,CAACsC,iBAAiB;MAC9D;MACA,MAAM5C,UAAU,CAAC,MAAM,EAAEmC,GAAG,EAAEI,IAAI,CAAC,CAC9BvB,IAAI,CAACC,GAAG,IAAI;QACTa,OAAO,CAACC,GAAG,CAACd,GAAG,CAACE,IAAI,CAAC;QACrBL,KAAK,CAAC+B,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAEC,OAAO,EAAE,QAAQ;UAAEC,MAAM,EAAE,4CAA4C;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QAChIC,UAAU,CAAC,MAAM;UACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAAC1B,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAA0B,WAAA,EAAAC,YAAA;QACZ1B,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACdf,KAAK,CAAC+B,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,yEAAAQ,MAAA,CAAsE,EAAAF,WAAA,GAAA1B,CAAC,CAAC6B,QAAQ,cAAAH,WAAA,uBAAVA,WAAA,CAAYpC,IAAI,MAAKwC,SAAS,IAAAH,YAAA,GAAG3B,CAAC,CAAC6B,QAAQ,cAAAF,YAAA,uBAAVA,YAAA,CAAYrC,IAAI,GAAGU,CAAC,CAAC+B,OAAO,CAAE;UAAEV,IAAI,EAAE;QAAK,CAAC,CAAC;MAClO,CAAC,CAAC;IACV,CAAC,CAAC,CAACtB,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;EACV,CAAC;EAED,MAAMgC,IAAI,GAAG,MAAOhC,CAAC,IAAK;IACtB,IAAIU,IAAI,GAAG;MACPC,IAAI,EAAE,WAAW;MACjBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC;MACxBC,OAAO,EAAE;IACb,CAAC;IACD,IAAIR,GAAG,GAAG,0BAA0B,GAAG7B,KAAK,CAACsC,iBAAiB;IAC9D;IACA,MAAM5C,UAAU,CAAC,MAAM,EAAEmC,GAAG,EAAEI,IAAI,CAAC,CAC9BvB,IAAI,CAACC,GAAG,IAAI;MACTa,OAAO,CAACC,GAAG,CAACd,GAAG,CAACE,IAAI,CAAC;MACrBL,KAAK,CAAC+B,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,4CAA4C;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MAChIC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC1B,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAiC,YAAA,EAAAC,YAAA;MACZjC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdf,KAAK,CAAC+B,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,yEAAAQ,MAAA,CAAsE,EAAAK,YAAA,GAAAjC,CAAC,CAAC6B,QAAQ,cAAAI,YAAA,uBAAVA,YAAA,CAAY3C,IAAI,MAAKwC,SAAS,IAAAI,YAAA,GAAGlC,CAAC,CAAC6B,QAAQ,cAAAK,YAAA,uBAAVA,YAAA,CAAY5C,IAAI,GAAGU,CAAC,CAAC+B,OAAO,CAAE;QAAEV,IAAI,EAAE;MAAK,CAAC,CAAC;IAClO,CAAC,CAAC;EACV,CAAC;EAED,oBACI9C,OAAA;IAAK4D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtB7D,OAAA,CAACN,KAAK;MAACoE,GAAG,EAAEpD;IAAM;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACpB1D,SAAS,iBACNR,OAAA;MAAK4D,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChB7D,OAAA;QAAK4D,SAAS,EAAC,OAAO;QAAAC,QAAA,eAClB7D,OAAA,CAACL,MAAM;UAACwE,OAAO,EAAG1C,CAAC,IAAKgC,IAAI,CAAChC,CAAC,CAAE;UAAAoC,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACNlE,OAAA;QAAK4D,SAAS,EAAC,OAAO;QAAAC,QAAA,eAClB7D,OAAA,CAACL,MAAM;UAACwE,OAAO,EAAG1C,CAAC,IAAKhB,YAAY,CAAC,KAAK,CAAE;UAAAoD,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAET1D,SAAS,KAAK,KAAK,iBAChBR,OAAA;MAAA6D,QAAA,eACI7D,OAAA,CAACH,QAAQ;QAACiC,KAAK,EAAExB,SAAU;QAAC8D,OAAO,EAAEhE,OAAQ;QAACiE,QAAQ,EAAG5C,CAAC,IAAKG,MAAM,CAACH,CAAC,CAAE;QAAC6C,WAAW,EAAC,MAAM;QAACC,WAAW,EAAC;MAAsB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAET,CAAC;AAEd,CAAC;AAAA/D,EAAA,CAtGYF,oBAAoB;AAAAuE,EAAA,GAApBvE,oBAAoB;AAAA,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}