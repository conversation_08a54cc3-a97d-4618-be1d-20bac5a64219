{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nexport var LayoutContext = /*#__PURE__*/React.createContext({\n  siderHook: {\n    addSider: function addSider() {\n      return null;\n    },\n    removeSider: function removeSider() {\n      return null;\n    }\n  }\n});\nfunction generator(_ref) {\n  var suffixCls = _ref.suffixCls,\n    tagName = _ref.tagName,\n    displayName = _ref.displayName;\n  return function (BasicComponent) {\n    var Adapter = /*#__PURE__*/React.forwardRef(function (props, ref) {\n      var _React$useContext = React.useContext(ConfigContext),\n        getPrefixCls = _React$useContext.getPrefixCls;\n      var customizePrefixCls = props.prefixCls;\n      var prefixCls = getPrefixCls(suffixCls, customizePrefixCls);\n      return /*#__PURE__*/React.createElement(BasicComponent, _extends({\n        ref: ref,\n        prefixCls: prefixCls,\n        tagName: tagName\n      }, props));\n    });\n    Adapter.displayName = displayName;\n    return Adapter;\n  };\n}\nvar Basic = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    tagName = props.tagName,\n    others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"tagName\"]);\n  var classString = classNames(prefixCls, className);\n  return /*#__PURE__*/React.createElement(tagName, _extends(_extends({\n    className: classString\n  }, others), {\n    ref: ref\n  }), children);\n});\nvar BasicLayout = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    direction = _React$useContext2.direction;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    siders = _React$useState2[0],\n    setSiders = _React$useState2[1];\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    hasSider = props.hasSider,\n    Tag = props.tagName,\n    others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"hasSider\", \"tagName\"]);\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-sider\"), typeof hasSider === 'boolean' ? hasSider : siders.length > 0), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var contextValue = React.useMemo(function () {\n    return {\n      siderHook: {\n        addSider: function addSider(id) {\n          setSiders(function (prev) {\n            return [].concat(_toConsumableArray(prev), [id]);\n          });\n        },\n        removeSider: function removeSider(id) {\n          setSiders(function (prev) {\n            return prev.filter(function (currentId) {\n              return currentId !== id;\n            });\n          });\n        }\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: ref,\n    className: classString\n  }, others), children));\n});\nvar Layout = generator({\n  suffixCls: 'layout',\n  tagName: 'section',\n  displayName: 'Layout'\n})(BasicLayout);\nvar Header = generator({\n  suffixCls: 'layout-header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nvar Footer = generator({\n  suffixCls: 'layout-footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nvar Content = generator({\n  suffixCls: 'layout-content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Header, Footer, Content };\nexport default Layout;", "map": {"version": 3, "names": ["_toConsumableArray", "_defineProperty", "_slicedToArray", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ConfigContext", "LayoutContext", "createContext", "<PERSON>r<PERSON><PERSON>", "addSider", "removeSider", "generator", "_ref", "suffixCls", "tagName", "displayName", "BasicComponent", "Adapter", "forwardRef", "props", "ref", "_React$useContext", "useContext", "getPrefixCls", "customizePrefixCls", "prefixCls", "createElement", "Basic", "className", "children", "others", "classString", "BasicLayout", "_classNames", "_React$useContext2", "direction", "_React$useState", "useState", "_React$useState2", "siders", "setSiders", "hasSider", "Tag", "concat", "contextValue", "useMemo", "id", "prev", "filter", "currentId", "Provider", "value", "Layout", "Header", "Footer", "Content"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/layout/layout.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nexport var LayoutContext = /*#__PURE__*/React.createContext({\n  siderHook: {\n    addSider: function addSider() {\n      return null;\n    },\n    removeSider: function removeSider() {\n      return null;\n    }\n  }\n});\n\nfunction generator(_ref) {\n  var suffixCls = _ref.suffixCls,\n      tagName = _ref.tagName,\n      displayName = _ref.displayName;\n  return function (BasicComponent) {\n    var Adapter = /*#__PURE__*/React.forwardRef(function (props, ref) {\n      var _React$useContext = React.useContext(ConfigContext),\n          getPrefixCls = _React$useContext.getPrefixCls;\n\n      var customizePrefixCls = props.prefixCls;\n      var prefixCls = getPrefixCls(suffixCls, customizePrefixCls);\n      return /*#__PURE__*/React.createElement(BasicComponent, _extends({\n        ref: ref,\n        prefixCls: prefixCls,\n        tagName: tagName\n      }, props));\n    });\n    Adapter.displayName = displayName;\n    return Adapter;\n  };\n}\n\nvar Basic = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      children = props.children,\n      tagName = props.tagName,\n      others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"tagName\"]);\n\n  var classString = classNames(prefixCls, className);\n  return /*#__PURE__*/React.createElement(tagName, _extends(_extends({\n    className: classString\n  }, others), {\n    ref: ref\n  }), children);\n});\nvar BasicLayout = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n\n  var _React$useContext2 = React.useContext(ConfigContext),\n      direction = _React$useContext2.direction;\n\n  var _React$useState = React.useState([]),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      siders = _React$useState2[0],\n      setSiders = _React$useState2[1];\n\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      children = props.children,\n      hasSider = props.hasSider,\n      Tag = props.tagName,\n      others = __rest(props, [\"prefixCls\", \"className\", \"children\", \"hasSider\", \"tagName\"]);\n\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-sider\"), typeof hasSider === 'boolean' ? hasSider : siders.length > 0), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var contextValue = React.useMemo(function () {\n    return {\n      siderHook: {\n        addSider: function addSider(id) {\n          setSiders(function (prev) {\n            return [].concat(_toConsumableArray(prev), [id]);\n          });\n        },\n        removeSider: function removeSider(id) {\n          setSiders(function (prev) {\n            return prev.filter(function (currentId) {\n              return currentId !== id;\n            });\n          });\n        }\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: ref,\n    className: classString\n  }, others), children));\n});\nvar Layout = generator({\n  suffixCls: 'layout',\n  tagName: 'section',\n  displayName: 'Layout'\n})(BasicLayout);\nvar Header = generator({\n  suffixCls: 'layout-header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nvar Footer = generator({\n  suffixCls: 'layout-footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nvar Content = generator({\n  suffixCls: 'layout-content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Header, Footer, Content };\nexport default Layout;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AAEzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,IAAIC,aAAa,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC;EAC1DC,SAAS,EAAE;IACTC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;MAC5B,OAAO,IAAI;IACb,CAAC;IACDC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClC,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC;AAEF,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,WAAW,GAAGH,IAAI,CAACG,WAAW;EAClC,OAAO,UAAUC,cAAc,EAAE;IAC/B,IAAIC,OAAO,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAChE,IAAIC,iBAAiB,GAAGlB,KAAK,CAACmB,UAAU,CAACjB,aAAa,CAAC;QACnDkB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;MAEjD,IAAIC,kBAAkB,GAAGL,KAAK,CAACM,SAAS;MACxC,IAAIA,SAAS,GAAGF,YAAY,CAACV,SAAS,EAAEW,kBAAkB,CAAC;MAC3D,OAAO,aAAarB,KAAK,CAACuB,aAAa,CAACV,cAAc,EAAE5B,QAAQ,CAAC;QAC/DgC,GAAG,EAAEA,GAAG;QACRK,SAAS,EAAEA,SAAS;QACpBX,OAAO,EAAEA;MACX,CAAC,EAAEK,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IACFF,OAAO,CAACF,WAAW,GAAGA,WAAW;IACjC,OAAOE,OAAO;EAChB,CAAC;AACH;AAEA,IAAIU,KAAK,GAAG,aAAaxB,KAAK,CAACe,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIK,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BG,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBf,OAAO,GAAGK,KAAK,CAACL,OAAO;IACvBgB,MAAM,GAAGzC,MAAM,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EAE7E,IAAIY,WAAW,GAAG3B,UAAU,CAACqB,SAAS,EAAEG,SAAS,CAAC;EAClD,OAAO,aAAazB,KAAK,CAACuB,aAAa,CAACZ,OAAO,EAAE1B,QAAQ,CAACA,QAAQ,CAAC;IACjEwC,SAAS,EAAEG;EACb,CAAC,EAAED,MAAM,CAAC,EAAE;IACVV,GAAG,EAAEA;EACP,CAAC,CAAC,EAAES,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,IAAIG,WAAW,GAAG,aAAa7B,KAAK,CAACe,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIa,WAAW;EAEf,IAAIC,kBAAkB,GAAG/B,KAAK,CAACmB,UAAU,CAACjB,aAAa,CAAC;IACpD8B,SAAS,GAAGD,kBAAkB,CAACC,SAAS;EAE5C,IAAIC,eAAe,GAAGjC,KAAK,CAACkC,QAAQ,CAAC,EAAE,CAAC;IACpCC,gBAAgB,GAAGnD,cAAc,CAACiD,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEnC,IAAIb,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BG,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBY,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ;IACzBC,GAAG,GAAGvB,KAAK,CAACL,OAAO;IACnBgB,MAAM,GAAGzC,MAAM,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EAEzF,IAAIY,WAAW,GAAG3B,UAAU,CAACqB,SAAS,GAAGQ,WAAW,GAAG,CAAC,CAAC,EAAE/C,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACU,MAAM,CAAClB,SAAS,EAAE,YAAY,CAAC,EAAE,OAAOgB,QAAQ,KAAK,SAAS,GAAGA,QAAQ,GAAGF,MAAM,CAACtC,MAAM,GAAG,CAAC,CAAC,EAAEf,eAAe,CAAC+C,WAAW,EAAE,EAAE,CAACU,MAAM,CAAClB,SAAS,EAAE,MAAM,CAAC,EAAEU,SAAS,KAAK,KAAK,CAAC,EAAEF,WAAW,GAAGL,SAAS,CAAC;EACpS,IAAIgB,YAAY,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,YAAY;IAC3C,OAAO;MACLrC,SAAS,EAAE;QACTC,QAAQ,EAAE,SAASA,QAAQA,CAACqC,EAAE,EAAE;UAC9BN,SAAS,CAAC,UAAUO,IAAI,EAAE;YACxB,OAAO,EAAE,CAACJ,MAAM,CAAC1D,kBAAkB,CAAC8D,IAAI,CAAC,EAAE,CAACD,EAAE,CAAC,CAAC;UAClD,CAAC,CAAC;QACJ,CAAC;QACDpC,WAAW,EAAE,SAASA,WAAWA,CAACoC,EAAE,EAAE;UACpCN,SAAS,CAAC,UAAUO,IAAI,EAAE;YACxB,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUC,SAAS,EAAE;cACtC,OAAOA,SAAS,KAAKH,EAAE;YACzB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa3C,KAAK,CAACuB,aAAa,CAACpB,aAAa,CAAC4C,QAAQ,EAAE;IAC9DC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAazC,KAAK,CAACuB,aAAa,CAACgB,GAAG,EAAEtD,QAAQ,CAAC;IAChDgC,GAAG,EAAEA,GAAG;IACRQ,SAAS,EAAEG;EACb,CAAC,EAAED,MAAM,CAAC,EAAED,QAAQ,CAAC,CAAC;AACxB,CAAC,CAAC;AACF,IAAIuB,MAAM,GAAGzC,SAAS,CAAC;EACrBE,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACiB,WAAW,CAAC;AACf,IAAIqB,MAAM,GAAG1C,SAAS,CAAC;EACrBE,SAAS,EAAE,eAAe;EAC1BC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACY,KAAK,CAAC;AACT,IAAI2B,MAAM,GAAG3C,SAAS,CAAC;EACrBE,SAAS,EAAE,eAAe;EAC1BC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACY,KAAK,CAAC;AACT,IAAI4B,OAAO,GAAG5C,SAAS,CAAC;EACtBE,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE;AACf,CAAC,CAAC,CAACY,KAAK,CAAC;AACT,SAAS0B,MAAM,EAAEC,MAAM,EAAEC,OAAO;AAChC,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}