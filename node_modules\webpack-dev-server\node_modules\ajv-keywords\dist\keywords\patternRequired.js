"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const patternRequired_1 = __importDefault(require("../definitions/patternRequired"));
const patternRequired = (ajv) => ajv.addKeyword((0, patternRequired_1.default)());
exports.default = patternRequired;
module.exports = patternRequired;
//# sourceMappingURL=patternRequired.js.map