{"version": 3, "file": "valid-extra-dialects.js", "sourceRoot": "", "sources": ["../../src/valid-extra-dialects.ts"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,OAAO,IAAI,MAAM,WAAW,CAAA;AAG5B,MAAM,YAAY,GAAG,CACnB,CAAuB,EACvB,CAAuB,EACP,EAAE;IAClB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAAE,OAAO,KAAK,CAAA;IAC1B,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;QACnB,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;YAAE,OAAO,EAAE,CAAA;IAC/B,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED,MAAM,oBAAoB,GAAG,CAC3B,CAAW,EACX,KAAoC,EACpC,EAAE;IACF,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAClB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,KAAK,sCAAsC,CAAC,EAAE,CAAC,CAAA;YACvD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QACD,IACE,CAAC,KAAK,UAAU;YAChB,CAAC,KAAK,KAAK;YACX,CAAC,KAAK,KAAK;YACX,CAAC,KAAK,SAAS;YACf,CAAC,KAAK,QAAQ;YACd,CAAC,KAAK,MAAM;YACZ,CAAC,KAAK,QAAQ;YACd,CAAC,KAAK,SAAS,EACf,CAAC;YACD,IAAI,CACF,QAAQ,KAAK,6BAA6B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC9D,CAAA;YACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,eAAe,CAAC,EACd,gBAAgB,EAChB,WAAW,EACX,cAAc,GACH,EAAE,EAAE;IACf,IACE,gBAAgB,KAAK,SAAS;QAC9B,WAAW,KAAK,SAAS;QACzB,cAAc,KAAK,SAAS,EAC5B,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IACD,IACE,gBAAgB;QAChB,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,UAAU,CAAC,EACnD,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAI,WAAW,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC;QAC7D,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IACE,cAAc;QACd,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,CAAC,EAC/C,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IACD,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI;QACjC;YACE,kBAAkB;YAClB,aAAa;YACb,gBAAgB;YAChB,WAAW;SACZ;QACD;YACE,kBAAkB;YAClB,gBAAgB;YAChB,gBAAgB;YAChB,cAAc;SACf;QACD,CAAC,aAAa,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,CAAC;KACtD,EAAE,CAAC;QACX,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAClC,IAAI,CAAC,OAAO;YAAE,SAAQ;QACtB,IAAI,CACF,GAAG,KAAK,QAAQ,KAAK,0BAA0B,OAAO,gBAAgB,CACvE,CAAA;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["// validate esmDialects and commonjsDialects\nimport fail from './fail.js'\nimport { TshyConfig } from './types.js'\n\nconst arrayOverlap = (\n  a: string[] | undefined,\n  b: string[] | undefined,\n): string | false => {\n  if (!a || !b) return false\n  for (const av of a) {\n    if (b.includes(av)) return av\n  }\n  return false\n}\n\nconst validExtraDialectSet = (\n  e: string[],\n  which: 'commonjs' | 'esm' | 'source',\n) => {\n  for (const d of e) {\n    if (typeof d !== 'string') {\n      fail(`${which} must be an array of strings, got: ${d}`)\n      return process.exit(1)\n    }\n    if (\n      d === 'commonjs' ||\n      d === 'cjs' ||\n      d === 'esm' ||\n      d === 'require' ||\n      d === 'import' ||\n      d === 'node' ||\n      d === 'source' ||\n      d === 'default'\n    ) {\n      fail(\n        `tshy.${which}Dialects must not contain ${JSON.stringify(d)}`,\n      )\n      return process.exit(1)\n    }\n  }\n  return true\n}\n\nexport default ({\n  commonjsDialects,\n  esmDialects,\n  sourceDialects,\n}: TshyConfig) => {\n  if (\n    commonjsDialects === undefined &&\n    esmDialects === undefined &&\n    sourceDialects === undefined\n  ) {\n    return true\n  }\n  if (\n    commonjsDialects &&\n    !validExtraDialectSet(commonjsDialects, 'commonjs')\n  ) {\n    return false\n  }\n  if (esmDialects && !validExtraDialectSet(esmDialects, 'esm')) {\n    return false\n  }\n  if (\n    sourceDialects &&\n    !validExtraDialectSet(sourceDialects, 'source')\n  ) {\n    return false\n  }\n  for (const [aname, bname, a, b] of [\n    [\n      'commonjsDialects',\n      'esmDialects',\n      commonjsDialects,\n      esmDialects,\n    ],\n    [\n      'commonjsDialects',\n      'sourceDialects',\n      commonjsDialects,\n      sourceDialects,\n    ],\n    ['esmDialects', 'sourceDialects', esmDialects, sourceDialects],\n  ] as const) {\n    const overlap = arrayOverlap(a, b)\n    if (!overlap) continue\n    fail(\n      `${aname} and ${bname} must be unique, found ${overlap} in both lists`,\n    )\n    return process.exit(1)\n  }\n  return true\n}\n"]}