{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.warning = warning;\nexports.isIconDefinition = isIconDefinition;\nexports.normalizeAttrs = normalizeAttrs;\nexports.generate = generate;\nexports.getSecondaryColor = getSecondaryColor;\nexports.normalizeTwoToneColors = normalizeTwoToneColors;\nexports.useInsertStyles = exports.iconStyles = exports.svgBaseProps = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\nvar _colors = require(\"@ant-design/colors\");\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _warning = _interopRequireDefault(require(\"rc-util/lib/warning\"));\nvar _dynamicCSS = require(\"rc-util/lib/Dom/dynamicCSS\");\nvar _Context = _interopRequireDefault(require(\"./components/Context\"));\nfunction warning(valid, message) {\n  (0, _warning.default)(valid, \"[@ant-design/icons] \".concat(message));\n}\nfunction isIconDefinition(target) {\n  return (0, _typeof2.default)(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && ((0, _typeof2.default)(target.icon) === 'object' || typeof target.icon === 'function');\n}\nfunction normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        acc[key] = val;\n    }\n    return acc;\n  }, {});\n}\nfunction generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/_react.default.createElement(node.tag, (0, _objectSpread2.default)({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/_react.default.createElement(node.tag, (0, _objectSpread2.default)((0, _objectSpread2.default)({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nfunction getSecondaryColor(primaryColor) {\n  // choose the second color\n  return (0, _colors.generate)(primaryColor)[0];\n}\nfunction normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n} // These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\n\nvar svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexports.svgBaseProps = svgBaseProps;\nvar iconStyles = \"\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexports.iconStyles = iconStyles;\nvar useInsertStyles = function useInsertStyles() {\n  var styleStr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : iconStyles;\n  var _useContext = (0, _react.useContext)(_Context.default),\n    csp = _useContext.csp;\n  (0, _react.useEffect)(function () {\n    (0, _dynamicCSS.updateCSS)(styleStr, '@ant-design-icons', {\n      prepend: true,\n      csp: csp\n    });\n  }, []);\n};\nexports.useInsertStyles = useInsertStyles;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "warning", "isIconDefinition", "normalizeAttrs", "generate", "getSecondaryColor", "normalizeTwoToneColors", "useInsertStyles", "iconStyles", "svgBaseProps", "_objectSpread2", "_typeof2", "_colors", "_react", "_warning", "_dynamicCSS", "_Context", "valid", "message", "default", "concat", "target", "name", "theme", "icon", "attrs", "arguments", "length", "undefined", "keys", "reduce", "acc", "key", "val", "className", "class", "node", "rootProps", "createElement", "tag", "children", "map", "child", "index", "primaryColor", "twoToneColor", "Array", "isArray", "width", "height", "fill", "focusable", "styleStr", "_useContext", "useContext", "csp", "useEffect", "updateCSS", "prepend"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons/lib/utils.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.warning = warning;\nexports.isIconDefinition = isIconDefinition;\nexports.normalizeAttrs = normalizeAttrs;\nexports.generate = generate;\nexports.getSecondaryColor = getSecondaryColor;\nexports.normalizeTwoToneColors = normalizeTwoToneColors;\nexports.useInsertStyles = exports.iconStyles = exports.svgBaseProps = void 0;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\n\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\n\nvar _colors = require(\"@ant-design/colors\");\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _warning = _interopRequireDefault(require(\"rc-util/lib/warning\"));\n\nvar _dynamicCSS = require(\"rc-util/lib/Dom/dynamicCSS\");\n\nvar _Context = _interopRequireDefault(require(\"./components/Context\"));\n\nfunction warning(valid, message) {\n  (0, _warning.default)(valid, \"[@ant-design/icons] \".concat(message));\n}\n\nfunction isIconDefinition(target) {\n  return (0, _typeof2.default)(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && ((0, _typeof2.default)(target.icon) === 'object' || typeof target.icon === 'function');\n}\n\nfunction normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n\n      default:\n        acc[key] = val;\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/_react.default.createElement(node.tag, (0, _objectSpread2.default)({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n\n  return /*#__PURE__*/_react.default.createElement(node.tag, (0, _objectSpread2.default)((0, _objectSpread2.default)({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\n\nfunction getSecondaryColor(primaryColor) {\n  // choose the second color\n  return (0, _colors.generate)(primaryColor)[0];\n}\n\nfunction normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n} // These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\n\n\nvar svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexports.svgBaseProps = svgBaseProps;\nvar iconStyles = \"\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexports.iconStyles = iconStyles;\n\nvar useInsertStyles = function useInsertStyles() {\n  var styleStr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : iconStyles;\n\n  var _useContext = (0, _react.useContext)(_Context.default),\n      csp = _useContext.csp;\n\n  (0, _react.useEffect)(function () {\n    (0, _dynamicCSS.updateCSS)(styleStr, '@ant-design-icons', {\n      prepend: true,\n      csp: csp\n    });\n  }, []);\n};\n\nexports.useInsertStyles = useInsertStyles;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGA,OAAO;AACzBF,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3CH,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvCJ,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3BL,OAAO,CAACM,iBAAiB,GAAGA,iBAAiB;AAC7CN,OAAO,CAACO,sBAAsB,GAAGA,sBAAsB;AACvDP,OAAO,CAACQ,eAAe,GAAGR,OAAO,CAACS,UAAU,GAAGT,OAAO,CAACU,YAAY,GAAG,KAAK,CAAC;AAE5E,IAAIC,cAAc,GAAGd,sBAAsB,CAACD,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE5F,IAAIgB,QAAQ,GAAGf,sBAAsB,CAACD,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAE/E,IAAIiB,OAAO,GAAGjB,OAAO,CAAC,oBAAoB,CAAC;AAE3C,IAAIkB,MAAM,GAAGnB,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAImB,QAAQ,GAAGlB,sBAAsB,CAACD,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAErE,IAAIoB,WAAW,GAAGpB,OAAO,CAAC,4BAA4B,CAAC;AAEvD,IAAIqB,QAAQ,GAAGpB,sBAAsB,CAACD,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAEtE,SAASM,OAAOA,CAACgB,KAAK,EAAEC,OAAO,EAAE;EAC/B,CAAC,CAAC,EAAEJ,QAAQ,CAACK,OAAO,EAAEF,KAAK,EAAE,sBAAsB,CAACG,MAAM,CAACF,OAAO,CAAC,CAAC;AACtE;AAEA,SAAShB,gBAAgBA,CAACmB,MAAM,EAAE;EAChC,OAAO,CAAC,CAAC,EAAEV,QAAQ,CAACQ,OAAO,EAAEE,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACE,KAAK,KAAK,QAAQ,KAAK,CAAC,CAAC,EAAEZ,QAAQ,CAACQ,OAAO,EAAEE,MAAM,CAACG,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOH,MAAM,CAACG,IAAI,KAAK,UAAU,CAAC;AACpN;AAEA,SAASrB,cAAcA,CAAA,EAAG;EACxB,IAAIsB,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,OAAO7B,MAAM,CAACgC,IAAI,CAACJ,KAAK,CAAC,CAACK,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACnD,IAAIC,GAAG,GAAGR,KAAK,CAACO,GAAG,CAAC;IAEpB,QAAQA,GAAG;MACT,KAAK,OAAO;QACVD,GAAG,CAACG,SAAS,GAAGD,GAAG;QACnB,OAAOF,GAAG,CAACI,KAAK;QAChB;MAEF;QACEJ,GAAG,CAACC,GAAG,CAAC,GAAGC,GAAG;IAClB;IAEA,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,SAAS3B,QAAQA,CAACgC,IAAI,EAAEJ,GAAG,EAAEK,SAAS,EAAE;EACtC,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,aAAaxB,MAAM,CAACM,OAAO,CAACmB,aAAa,CAACF,IAAI,CAACG,GAAG,EAAE,CAAC,CAAC,EAAE7B,cAAc,CAACS,OAAO,EAAE;MACrFa,GAAG,EAAEA;IACP,CAAC,EAAE7B,cAAc,CAACiC,IAAI,CAACX,KAAK,CAAC,CAAC,EAAE,CAACW,IAAI,CAACI,QAAQ,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MAChF,OAAOvC,QAAQ,CAACsC,KAAK,EAAE,EAAE,CAACtB,MAAM,CAACY,GAAG,EAAE,GAAG,CAAC,CAACZ,MAAM,CAACgB,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAACnB,MAAM,CAACuB,KAAK,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;EACL;EAEA,OAAO,aAAa9B,MAAM,CAACM,OAAO,CAACmB,aAAa,CAACF,IAAI,CAACG,GAAG,EAAE,CAAC,CAAC,EAAE7B,cAAc,CAACS,OAAO,EAAE,CAAC,CAAC,EAAET,cAAc,CAACS,OAAO,EAAE;IACjHa,GAAG,EAAEA;EACP,CAAC,EAAE7B,cAAc,CAACiC,IAAI,CAACX,KAAK,CAAC,CAAC,EAAEY,SAAS,CAAC,EAAE,CAACD,IAAI,CAACI,QAAQ,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC5F,OAAOvC,QAAQ,CAACsC,KAAK,EAAE,EAAE,CAACtB,MAAM,CAACY,GAAG,EAAE,GAAG,CAAC,CAACZ,MAAM,CAACgB,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAACnB,MAAM,CAACuB,KAAK,CAAC,CAAC;EACjF,CAAC,CAAC,CAAC;AACL;AAEA,SAAStC,iBAAiBA,CAACuC,YAAY,EAAE;EACvC;EACA,OAAO,CAAC,CAAC,EAAEhC,OAAO,CAACR,QAAQ,EAAEwC,YAAY,CAAC,CAAC,CAAC,CAAC;AAC/C;AAEA,SAAStC,sBAAsBA,CAACuC,YAAY,EAAE;EAC5C,IAAI,CAACA,YAAY,EAAE;IACjB,OAAO,EAAE;EACX;EAEA,OAAOC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;AACpE,CAAC,CAAC;AACF;;AAGA,IAAIpC,YAAY,GAAG;EACjBuC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,cAAc;EACpB,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE;AACb,CAAC;AACDpD,OAAO,CAACU,YAAY,GAAGA,YAAY;AACnC,IAAID,UAAU,GAAG,w7BAAw7B;AACz8BT,OAAO,CAACS,UAAU,GAAGA,UAAU;AAE/B,IAAID,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;EAC/C,IAAI6C,QAAQ,GAAG1B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGlB,UAAU;EAE7F,IAAI6C,WAAW,GAAG,CAAC,CAAC,EAAExC,MAAM,CAACyC,UAAU,EAAEtC,QAAQ,CAACG,OAAO,CAAC;IACtDoC,GAAG,GAAGF,WAAW,CAACE,GAAG;EAEzB,CAAC,CAAC,EAAE1C,MAAM,CAAC2C,SAAS,EAAE,YAAY;IAChC,CAAC,CAAC,EAAEzC,WAAW,CAAC0C,SAAS,EAAEL,QAAQ,EAAE,mBAAmB,EAAE;MACxDM,OAAO,EAAE,IAAI;MACbH,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AAEDxD,OAAO,CAACQ,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}