{"core/audits/accessibility/accesskeys.js | description": {"message": "Les tecles d'accés permeten als usuaris posar el focus ràpidament en una part de la pàgina. Perquè puguin navegar correctament, cada tecla d'accés ha de ser única. [Obtén més informació sobre les tecles d'accés](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Els valors de l'atribut `[accesskey]` no són únics"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Els valors de l'atribut `[accesskey]` són únics"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Cada element `role` d'ARIA admet un subconjunt específic d'atributs `aria-*`. <PERSON> no coincideixen, els atributs `aria-*` queden invalidats. [Obtén informació sobre com pots relacionar els atributs ARIA amb les seves funcions](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Els atributs `[aria-*]` no coincideixen amb les seves funcions"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Els atributs `[aria-*]` coincideixen amb les seves funcions"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Si un element no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que queda inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén informació sobre com pots fer que els elements de les ordres siguin més accessibles](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Els elements `button`, `link` i `menuitem` no tenen noms accessibles"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Els elements `button`, `link` i `menuitem` tenen noms accessibles"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Les tecnologies assistencials, com ara els lectors de pantalla, funcionen de manera incongruent quan s'estableix `aria-hidden=\"true\"` en el document `<body>`. [Obtén informació sobre com `aria-hidden` afecta el cos del document](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` es troba al document `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` no es troba al document `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Els descendents enfocables en un element `[aria-hidden=\"true\"]` impedeixen que els elements interactius estiguin a l'abast dels usuaris de tecnologies assistencials, com ara els lectors de pantalla. [Obtén informació sobre com `aria-hidden` afecta els elements enfocables](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Els elements `[aria-hidden=\"true\"]` contenen descendents en què es pot posar el focus"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Els elements `[aria-hidden=\"true\"]` no contenen cap descendent en què es pugui posar el focus"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Si un camp d'entrada no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que resulta inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén més informació sobre les etiquetes dels camps d'entrada](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Els camps d'entrada ARIA no tenen noms accessibles"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Els camps d'entrada ARIA tenen noms accessibles"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Si un element mesurador no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que resulta inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén informació sobre com pots posar nom als elements `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Els elements `meter` d'ARIA no tenen noms accessibles"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Els elements `meter` d'ARIA tenen noms accessibles"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Si un element del tipus `progressbar` no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que resulta inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén informació sobre com pots etiquetar elements `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Els elements `progressbar` d'ARIA no tenen noms accessibles"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Els elements `progressbar` d'ARIA tenen noms accessibles"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Algunes funcions d'ARIA tenen atributs obligatoris que descriuen l'estat de l'element als lectors de pantalla. [Obtén més informació sobre les funcions i els atributs obligatoris](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Els atributs `[role]` no tenen tots els atributs `[aria-*]` obligatoris"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Els elements amb l'atribut `[role]` tenen tots els atributs `[aria-*]` obligatoris"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Algunes funcions dels elements ARIA superiors han d'incloure funcions concretes dels elements secundaris perquè puguin dur a terme les funcions d'accessibilitat per a les quals s'han dissenyat. [Obtén més informació sobre les funcions i els elements secundaris obligatoris](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Els elements amb un `[role]` ARIA que requereixen que els elements secundaris continguin un atribut `[role]` específic no tenen alguns o tots els elements secundaris requerits."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Els elements amb un ARIA `[role]` que requereixen que els elements secundaris continguin un atribut `[role]` específic tenen tots els elements secundaris requerits."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Les funcions de determinats elements superiors han d'incloure algunes funcions dels elements ARIA secundaris perquè puguin dur a terme les funcions d'accessibilitat per a les quals s'han dissenyat. [Obtén més informació sobre les funcions d'ARIA i l'element superior obligatori](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Els atributs `[role]` no s'inclouen a l'element superior obligatori"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Els atributs `[role]` s'inclouen a l'element superior obligatori"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Les funcions dels elements ARIA han de tenir valors vàlids perquè puguin dur a terme les funcions d'accessibilitat per a les quals s'han dissenyat. [Obtén més informació sobre les funcions d'ARIA vàlides](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Els valors de l'atribut `[role]` no són vàlids"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Els valors de l'atribut `[role]` s<PERSON> vàlids"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Si un camp de commutació no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que resulta inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén més informació sobre els camps de commutació](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Els camps de commutació ARIA no tenen noms accessibles"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Els camps de commutació ARIA tenen noms accessibles"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Si un element de descripció emergent no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que resulta inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén informació sobre com pots posar nom als elements `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Els elements `tooltip` d'ARIA no tenen noms accessibles"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Els elements `tooltip` d'ARIA tenen noms accessibles"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Si un element del tipus `treeitem` no té un nom accessible, els lectors de pantalla l'enuncien amb un nom genèric, de manera que resulta inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén més informació sobre com pots etiquetar els elements `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Els elements `treeitem` d'ARIA no tenen noms accessibles"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Els elements `treeitem` d'ARIA tenen noms accessibles"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Les tecnologies assistencials, com ara els lectors de pantalla, no poden interpretar els atributs ARIA que tenen valors no vàlids. [Obtén més informació sobre els valors dels atributs ARIA vàlids](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Els atributs `[aria-*]` no tenen valors vàlids"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Els atributs `[aria-*]` tenen valors vàlids"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Les tecnologies assistencials, com ara els lectors de pantalla, no poden interpretar els atributs ARIA que tenen noms no vàlids. [Obtén més informació sobre els atributs d'ARIA vàlids](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Els atributs `[aria-*]` no són vàlids o estan mal escrits"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Els atributs `[aria-*]` són vàlids i estan ben escrits"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elements amb errors"}, "core/audits/accessibility/button-name.js | description": {"message": "Si un botó no té un nom accessible, els lectors de pantalla l'enuncien com a \"botó\", de manera que queda inservible per als usuaris que depenen d'aquesta tecnologia. [Obtén informació sobre com pots fer que els botons siguin més accessibles](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Els botons no tenen un nom accessible"}, "core/audits/accessibility/button-name.js | title": {"message": "Els botons tenen noms accessibles"}, "core/audits/accessibility/bypass.js | description": {"message": "Si s'afegeixen maneres d'ometre el contingut repetitiu, els usuaris de teclat poden navegar per la pàgina de manera més eficient. [Obtén més informació sobre com pots ometre el contingut repetitiu](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "La pàgina no conté un encapçalament, un enllaç d'omissió o una regió de referència"}, "core/audits/accessibility/bypass.js | title": {"message": "La pàgina conté un encapçalament, un enllaç d'omissió o una regió de referència"}, "core/audits/accessibility/color-contrast.js | description": {"message": "El text amb contrast baix resulta difícil o impossible de llegir per a molts usuaris. [Obtén informació sobre com pots proporcionar un contrast de color suficient](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "La relació de contrast dels colors de primer i de segon pla no és suficient."}, "core/audits/accessibility/color-contrast.js | title": {"message": "La relació de contrast dels colors de primer i de segon pla és suficient"}, "core/audits/accessibility/definition-list.js | description": {"message": "Si el marcatge de les llistes de definició no és correcte, és possible que els lectors de pantalla sonin de manera confusa o inexacta. [Obtén informació sobre com pots estructurar correctament les llistes de definicions](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Els `<dl>` no contenen només grups de `<dt>` i `<dd>` ordenats correctament, o bé elements `<script>`, `<template>` o `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Els `<dl>` contenen només grups de `<dt>` i `<dd>` ordenats correctament, o bé elements `<script>`, `<template>` o `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Els elements de la llista de definicions (`<dt>` i `<dd>`) han d'estar tancats dins d'un element `<dl>` superior per garantir que els lectors de pantalla els puguin enunciar correctament. [Obtén informació sobre com pots estructurar correctament les llistes de definicions](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Els elements de la llista de definicions estan tancats entre elements `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Els elements de la llista de definicions estan tancats entre elements `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "El títol proporciona un resum de la pàgina als usuaris de lectors de pantalla. A més, els usuaris de motors de cerca depenen en gran mesura d'aquest títol per determinar si una pàgina és rellevant per a la seva cerca. [Obtén més informació sobre els títols dels documents](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "El document no té cap element `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "El document té un element `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Tots els elements enfocables han de tenir un `id` únic per garantir que les tecnologies assistencials els puguin veure. [Obtén informació sobre com pots corregir els `id` duplicats](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Els atributs `[id]` en elements actius en què es pot posar el focus no són únics"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Els atributs `[id]` en elements actius en què es pot posar el focus són únics"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "El valor d'un identificador ARIA ha de ser únic per impedir que les tecnologies assistencials passin per alt altres instàncies. [Obtén informació sobre com pots corregir els identificadors ARIA duplicats](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Els identificadors ARIA no són únics"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Els identificadors ARIA són únics"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Els camps de formulari amb diverses etiquetes poden fer que les tecnologies assistencials, com ara els lectors de pantalla, els enunciïn de manera confusa, ja que poden utilitzar la primera etiqueta, la darrera o totes. [Obtén informació sobre com pots utilitzar etiquetes de formulari](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Els camps de formulari tenen diverses etiquetes"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Cap camp de formulari té diverses etiquetes"}, "core/audits/accessibility/frame-title.js | description": {"message": "Els usuaris de lectors de pantalla depenen dels títols dels marcs perquè en descriguin el contingut. [Obtén més informació sobre els títols dels marcs](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Els elements `<frame>` o `<iframe>` no tenen títol"}, "core/audits/accessibility/frame-title.js | title": {"message": "Els elements `<frame>` o `<iframe>` tenen un títol"}, "core/audits/accessibility/heading-order.js | description": {"message": "Els títols ordenats adequadament sense ometre nivells transmeten l'estructura semàntica de la pàgina, la qual cosa facilita la navegació i la comprensió quan s'utilitzen tecnologies assistencials. [Obtén més informació sobre l'ordre dels títols](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Els elements del títol no estan en un ordre seqüencial descendent"}, "core/audits/accessibility/heading-order.js | title": {"message": "Els elements del títol es mostren en un ordre seqüencial descendent"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Si en una pàgina no s'especifica un atribut `lang`, els lectors de pantalla suposen que la pàgina està escrita en l'idioma predeterminat que l'usuari ha triat en configurar el lector de pantalla. Si està escrita en un altre idioma, és possible que el lector de pantalla no n'enunciï el text correctament. [Obtén més informació sobre l'atribut `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "L'element `<html>` no té un atribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "L'element `<html>` té un atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Si especifiques un [idioma vàlid d'acord amb l'estàndard BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), facilites que els lectors de pantalla enunciïn el text correctament. [Obtén informació sobre com pots utilitzar l'atribut `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "L'element `<html>` no té un valor vàlid per a l'atribut `[lang]` corresponent."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "L'element `<html>` té un valor vàlid per a l'atribut `[lang]` corresponent"}, "core/audits/accessibility/image-alt.js | description": {"message": "Els elements informatius han d'utilitzar text alternatiu que sigui breu i descriptiu. Els elements decoratius es poden ignorar amb un atribut alt buit. [Obtén més informació sobre l'atribut `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Els elements d'imatge no tenen atributs `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Els elements d'imatge tenen atributs `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Si s'utilitza una imatge per al botó `<input>`, el text alternatiu pot ajudar els usuaris dels lectors de pantalla a entendre la funció del botó. [Obtén informació sobre el text alternatiu de la imatge d'entrada](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Els elements `<input type=\"image\">` no tenen text `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Els elements `<input type=\"image\">` tenen text `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "Les etiquetes garanteixen que les tecnologies assistencials, com ara els lectors de pantalla, puguin enunciar correctament els controls dels formularis. [Obtén més informació sobre les etiquetes d'elements de formulari](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Els elements de formulari no tenen etiquetes associades"}, "core/audits/accessibility/label.js | title": {"message": "Els elements de formulari tenen etiquetes associades"}, "core/audits/accessibility/link-name.js | description": {"message": "Si el text dels enllaços (així com el text alternatiu per a les imatges, quan s'utilitzen com a enllaços) és discernible, únic i enfocable, millora l'experiència de navegació per als usuaris de lectors de pantalla. [Obtén informació sobre com pots fer que els enllaços siguin accessibles](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Els enllaços no tenen noms que es puguin distingir"}, "core/audits/accessibility/link-name.js | title": {"message": "Els enllaços tenen noms que es poden distingir"}, "core/audits/accessibility/list.js | description": {"message": "Els lectors de pantalla tenen una manera específica d'enunciar les llistes. Estructurar-les correctament millora la manera com sonen els lectors de pantalla. [Obtén més informació sobre l'estructura adequada de les llistes](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Les llistes no contenen només elements`<li>` i elements que admeten scripts (`<script>` i `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Les llistes contenen només elements `<li>` i elements que admeten scripts (`<script>` i `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Els lectors de pantalla requereixen que els elements de llista (`<li>`) estiguin inclosos dins d'un element `<ul>`, `<ol>` o `<menu>` superior per poder enunciar-los correctament. [Obtén més informació sobre l'estructura adequada de les llistes](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Alguns elements de la llista (`<li>`) no estan inclosos entre elements superiors `<ul>`, `<ol>` o `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Alguns elements de llista (`<li>`) estan inclosos entre elements superiors `<ul>`, `<ol>` o `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Els usuaris no esperen que una pàgina s'actualitzi automàticament. En fer-ho, el focus torna a la part superior de la pàgina i els usuaris es poden sentir frustrats i confosos. [Obtén més informació sobre la metaetiqueta d'actualització](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "El document utilitza la metaetiqueta `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "El document no utilitza `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Desactivar el zoom pot ser un problema per als usuaris amb visió reduïda que necessiten ampliar la pantalla per veure correctament el contingut d'una pàgina web. [Obtén més informació sobre la metaetiqueta de finestra gràfica](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "L'atribut `[user-scalable=\"no\"]` s'utilitza a l'element `<meta name=\"viewport\">` o l'atribut `[maximum-scale]` és inferior a 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "L'atribut `[user-scalable=\"no\"]` no s'utilitza a l'element `<meta name=\"viewport\">` i l'atribut `[maximum-scale]` no és inferior a 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Els lectors de pantalla no poden traduir contingut que no sigui text. Si afegeixes text alternatiu als elements `<object>`, ajudes els lectors de pantalla a transmetre el significat als usuaris. [Obtén més informació sobre el text alternatiu per a elements `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Els elements `<object>` no tenen text alternatiu"}, "core/audits/accessibility/object-alt.js | title": {"message": "Els elements `<object>` tenen text alternatiu"}, "core/audits/accessibility/tabindex.js | description": {"message": "Un valor superior a 0 implica una ordenació explícita de navegació. Tècnicament és vàlid, però sol causar experiències frustrants per als usuaris que depenen de les tecnologies assistencials. [Obtén més informació sobre l'atribut `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Alguns elements tenen un valor `[tabindex]` superior a 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Cap element no té un valor `[tabindex]` superior a 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Els lectors de pantalla inclouen funcions perquè sigui més fàcil navegar per les taules. Assegura't que les cel·les `<td>` que fan servir l'atribut `[headers]` només facin referència a altres cel·les de la mateixa taula. Això pot millorar l'experiència dels usuaris de lectors de pantalla. [Obtén més informació sobre l'atribut `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Les cel·les d'un element `<table>` que fan servir l'atribut `[headers]` fan referència a un element `id` que no és a la mateixa taula."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Les cel·les d'un element `<table>` que fan servir l'atribut `[headers]` fan referència a cel·les de taula incloses a la mateixa taula."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Els lectors de pantalla inclouen funcions perquè sigui més fàcil navegar per les taules. Assegura't que les capçaleres de les taules sempre facin referència a un conjunt de cel·les. Això pot millorar l'experiència dels usuaris de lectors de pantalla. [Obtén més informació sobre les capçaleres de taules](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Els elements `<th>` i els que inclouen l'atribut `[role=\"columnheader\"/\"rowheader\"]` no tenen les cel·les de dades que descriuen."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Els elements `<th>` i els que inclouen l'atribut `[role=\"columnheader\"/\"rowheader\"]` tenen les cel·les de dades que descriuen."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Si especifiques un [idioma vàlid d'acord amb l'estàndard BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) als elements, permets que els lectors de pantalla enunciïn el text correctament. [Obtén informació sobre com pots utilitzar l'atribut `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Els atributs `[lang]` no tenen un valor vàlid"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Els atributs `[lang]` tenen un valor vàlid"}, "core/audits/accessibility/video-caption.js | description": {"message": "Si un vídeo ofereix subtítols, permet que els usuaris sords o amb discapacitat auditiva accedeixin a la informació més fàcilment. [Obtén més informació sobre els subtítols dels vídeos](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Els elements `<video>` no contenen cap element `<track>` amb `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Els elements `<video>` contenen un element `<track>` amb `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Valor actual"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Testimoni suggerit"}, "core/audits/autocomplete.js | description": {"message": "L'atribut `autocomplete` ajuda els usuaris a emplenar formularis més ràpidament. Per reduir l'esforç dels usuaris, planteja't activar l'atribut `autocomplete` establint-lo en un valor vàlid. [Més informació sobre `autocomplete` als formularis](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Els elements `<input>` no tenen els atributs correctes per a `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Requereix una revisió manual"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Revisa l'ordre dels testimonis"}, "core/audits/autocomplete.js | title": {"message": "Els elements `<input>` utilitzen correctament `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Testimonis de `autocomplete`: \"{token}\" no és vàlid a {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Revisa l'ordre dels testimonis: \"{tokens}\" a {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Hi ha accions possibles"}, "core/audits/bf-cache.js | description": {"message": "Moltes navegacions es duen a terme tornant a una pàgina anterior o tornant a la pàgina següent. La memòria cau endavant/enrere (bfcache) pot accelerar aquestes navegacions de retorn. [Més informació sobre bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motiu de l'error}other{# motius de l'error}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "<PERSON><PERSON><PERSON> l'error"}, "core/audits/bf-cache.js | failureTitle": {"message": "La pàgina ha impedit la restauració de la memòria cau endavant/enrere"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Tipus d'error"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "No hi ha cap acció possible"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Compatibilitat del navegador pendent"}, "core/audits/bf-cache.js | title": {"message": "La pàgina no ha impedit la restauració de la memòria cau endavant/enrere"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Les extensions de Chrome han afectat negativament el rendiment de càrrega de la pàgina. Audita la pàgina en mode d'incògnit o des d'un perfil de Chrome sense extensions."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Avaluació de scripts"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Anàlisi de scripts"}, "core/audits/bootup-time.js | columnTotal": {"message": "Temps total de la CPU"}, "core/audits/bootup-time.js | description": {"message": "Et recomanem que redueixis el temps dedicat a analitzar, compilar i executar JavaScript. Et pot ajudar utilitzar càrregues útils de JavaScript més petites. [Obtén informació sobre com pots reduir el temps d'execució de JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Redueix el temps d'execució de JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Temps d'execució de JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Suprimeix els mòduls de JavaScript grans i duplicats dels paquets per reduir el consum innecessari de bytes durant l'activitat de la xarxa. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Suprimeix els mòduls duplicats als paquets de JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Els GIF grans no són eficients per publicar contingut animat. A fi d'estalviar bytes a la xarxa, pots substituir els GIF per vídeos MPEG4/WebM en el cas de les animacions i per PNG/WebP en el cas de les imatges estàtiques. [Més informació sobre els formats de vídeo eficients](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Utilitza formats de vídeo per al contingut animat"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Els polyfills i les transformacions permeten als navegadors heretats fer servir funcions de JavaScript noves. No obstant això, molts no són necessaris per als navegadors moderns. Per al teu JavaScript empaquetat, adopta una estratègia de desenvolupament de scripts moderna utilitzant la detecció de la funció mòdul / no mòdul per reduir la quantitat de codi enviat als navegadors moderns, mantenint al mateix temps la compatibilitat amb els navegadors heretats. [Informació sobre com pots utilitzar el JavaScript modern](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> utilitzar JavaScript heretat als navegadors moderns"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Els formats d'imatge com WebP i AVIF solen proporcionar una millor compressió que els formats PNG o JPEG. D'aquesta manera, les baixades són més ràpides i el consum de dades és menor. [Obtén més informació sobre els formats d'imatge moderns](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Publica imatges en format d'última generació"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Et recomanem que utilitzis la càrrega lenta de les imatges amagades i fora de pantalla un cop s'acabin de carregar tots els recursos essencials a fi de reduir el temps fins que és interactiva. [Obtén informació sobre com pots ajornar les imatges fora de pantalla](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "A<PERSON>rna les imatges fora de pantalla"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Els recursos estan bloquejant la primera renderització de la pàgina. Et recomanem que publiquis els fitxers JavaScript o CSS inserits que siguin essencials i ajornis tots els estils i els fitxers JavaScript que no ho siguin. [Obtén informació sobre com pots suprimir els recursos que bloquegen la renderització](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimina els recursos que bloquegen la renderització"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Si la càrrega útil de la xarxa és molt gran, els usuaris consumeixen més dades mòbils i els temps de càrrega són més llargs. [Obtén informació sobre com pots reduir les mides de les càrregues útils](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Mida total: {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> útil<PERSON> de xarxa enormes"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "<PERSON><PERSON><PERSON> útil<PERSON> de xarxa enormes"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Reduir els fitxers CSS pot disminuir les mides de càrrega útil a la xarxa. [Obtén informació sobre com pots reduir el CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Redueix els CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Reduir els fitxers JavaScript pot disminuir les mides de càrrega útil i els temps d'anàlisi de scripts. [Obtén informació sobre com pots reduir JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Redueix els fitxers JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Redueix el nombre de regles no utilitzades dels fulls d'estil i ajorna els CSS que no es facin servir per al contingut de la meitat superior de la pàgina a fi de rebaixar el consum de bytes durant l'activitat de la xarxa. [Obtén informació sobre com pots reduir el nombre de CSS no utilitzats](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Redueix el nombre de fitxers CSS no utilitzats"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Redueix el nombre de fitxers JavaScript no utilitzats i ajorna la càrrega dels scripts fins que siguin necessaris a fi de rebaixar el consum de bytes durant l'activitat de la xarxa. [Obtén informació sobre com pots reduir el nombre de fitxers JavaScript no utilitzats](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Redueix el nombre de fitxers JavaScript no utilitzats"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Si la memòria cau té una vida llarga, es poden accelerar les visites repetides a la teva pàgina. [Obtén més informació sobre les polítiques de memòria cau eficient](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{S'ha trobat 1 recurs}other{S'han trobat # recursos}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Publica recursos estàtics amb una política de memòria cau eficient"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Utilitza una política de memòria cau eficient per als recursos estàtics"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Les imatges optimitzades es carreguen més ràpidament i utilitzen menys dades mòbils. [Obtén informació sobre com pots codificar imatges de manera eficient](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifica les imatges amb eficiència"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensions reals"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensions mostrades"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Les imatges eren més grans que la mida mostrada"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Les imatges eren adequades per a la mida mostrada"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Publica imatges amb la mida correcta per estalviar dades mòbils i millorar el temps de càrrega. [Obtén informació sobre com pots definir la mida de les imatges](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Adapta la mida de les imatges"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Els recursos basats en text s'han de publicar comprimits (gzip, deflate o brotli) per minimitzar el total de bytes a la xarxa. [Obtén més informació sobre la compressió de text](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Activa la compressió de text"}, "core/audits/content-width.js | description": {"message": "Si l'amplada del contingut de l'aplicació no coincideix amb l'amplada de la finestra gràfica, és possible que l'aplicació no s'optimitzi per a pantalles de dispositius mòbils. [Obtén informació sobre com pots ajustar la mida del contingut de la finestra gràfica](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "La mida de la finestra gràfica ({innerWidth} píxels) no coincideix amb la mida de la finestra ({outerWidth} píxels)."}, "core/audits/content-width.js | failureTitle": {"message": "El contingut no té la mida correcta per a la finestra gràfica"}, "core/audits/content-width.js | title": {"message": "El contingut té la mida correcta per a la finestra gràfica"}, "core/audits/critical-request-chains.js | description": {"message": "Les cadenes de sol·licituds essencials de sota et mostren quins recursos es carreguen amb prioritat alta. Et recomanem que escurcis les cadenes, redueixis la mida de baixada dels recursos o ajornis la baixada de recursos innecessaris per millorar la càrrega de les pàgines. [Obtén informació sobre com pots evitar encadenar sol·licituds essencials](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{S'ha trobat 1 cadena}other{S'han trobat # cadenes}}"}, "core/audits/critical-request-chains.js | title": {"message": "No encadenis sol·licituds importants"}, "core/audits/csp-xss.js | columnDirective": {"message": "Directiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravetat"}, "core/audits/csp-xss.js | description": {"message": "Una política de seguretat del contingut (CSP) sòlida redueix significativament el risc de patir atacs d'injecció indirecta de scripts (XSS). [Obtén informació sobre com pots utilitzar una CSP per evitar una XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaxi"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "La pàgina conté una CSP definida en una etiqueta <meta>. Pots moure la CSP a una capçalera HTTP o definir-ne una altra d'estricta en una capçalera HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "No s'ha trobat cap CSP en mode de compliment"}, "core/audits/csp-xss.js | title": {"message": "Comprova que la CSP sigui efectiva contra atacs XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Discontinuació/Advertiment"}, "core/audits/deprecations.js | columnLine": {"message": "Lín<PERSON>"}, "core/audits/deprecations.js | description": {"message": "Les API obsoletes s'acabaran suprimint del navegador. [Obtén més informació sobre les API obsoletes](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{S'ha trobat 1 advertiment}other{S'han trobat # advertiments}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Utilitza API obsoletes"}, "core/audits/deprecations.js | title": {"message": "Evita les API obsoletes"}, "core/audits/dobetterweb/charset.js | description": {"message": "Cal una declaració de codificació de caràcters. Es pot fer amb una etiqueta `<meta>` als primers 1.024 bytes de l'HTML o a la capçalera de resposta HTTP de tipus contingut. [Obtén més informació sobre com pots declarar la codificació de caràcters](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Falta la declaració del conjunt de caràcters o es produeix massa tard a l'HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Defineix el conjunt de caràcters correctament"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Especificar un tipus de document impedeix que el navegador canviï a mode Quirks. [Obtén més informació sobre la declaració del tipus de document](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "El nom del tipus de document ha de ser la cadena `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "El document conté un `doctype` que activa `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "El document ha de contenir un tipus de document"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Està previst que el camp publicId sigui una cadena buida"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Està previst que el camp systemId sigui una cadena buida"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "El document conté un `doctype` que activa `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "La pàgina no té el tipus de document HTML i, per tant, activa el mode Quirks"}, "core/audits/dobetterweb/doctype.js | title": {"message": "La pàgina té el tipus de document HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estadística"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Un DOM gran augmentarà l'ús de memòria, provocarà [càlculs d'estil](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) més llargs i produirà [reinicis de reflux del disseny](https://developers.google.com/speed/articles/reflow) costosos. [Obtén informació sobre com pots evitar una mida de DOM massa gran](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{Un element}other{# elements}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evita una mida de DOM excessiva"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profunditat màxima de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total d'elements de DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Nombre màxim d'elements secundaris"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Evita una mida excessiva de DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Els usuaris reaccionen amb desconfiança i desconcert davant dels llocs web que els sol·liciten la ubicació sense context. Et recomanem que vinculis la sol·licitud a una acció de l'usuari. [Obtén més informació sobre el permís de geolocalització](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Sol·licita el permís de geolocalització en carregar la pàgina"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita sol·licitar el permís de geolocalització en carregar la pàgina"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Tipus de problema"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Els problemes registrats al tauler `Issues` de Chrome DevTools indiquen que hi ha problemes sense resoldre. Poden provenir d'errors de sol·licitud de la xarxa, de controls de seguretat insuficients i d'altres problemes del navegador. Obre el tauler Issues de Chrome DevTools per obtenir més informació sobre cada problema."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "S'han registrat problemes al tauler `Issues` de Chrome Devtools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloquejat per una política de diversos orígens"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Els anuncis utilitzen molts recursos"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "No s'han detectat problemes al tauler `Issues` de Chrome Devtools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Totes les biblioteques front end de JavaScript detectades a la pàgina. [Obtén més informació sobre aquesta auditoria de diagnòstic per a la detecció de biblioteques de JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Biblioteques de JavaScript detectades"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Per als usuaris amb connexions lentes, els scripts externs injectats dinàmicament mitjançant `document.write()` poden retardar la càrrega de la pàgina unes desenes de segon. [Obtén informació sobre com pots evitar document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Els usuaris reaccionen amb desconfiança i desconcert davant dels llocs web que sol·liciten enviar notificacions sense context. Et recomanem que vinculis la sol·licitud als gestos de l'usuari. [Obtén més informació sobre com pots aconseguir de manera responsable permís per rebre notificacions](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Sol·licita el permís de notificació en carregar la pàgina"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita sol·licitar el permís de notificació en carregar la pàgina"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ofereix molts avantatges respecte a HTTP/1.1, com ara capçaleres binàries i multiplexatge. [Obtén més informació sobre HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{No s'ha atès 1 sol·licitud mitjançant HTTP/2}other{No s'han atès # sol·licituds mitjançant HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Utilitza HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Per millorar el rendiment del desplaçament per la pàgina, et recomanem que estableixis els detectors d'esdeveniments de toc i de roda de desplaçament en `passive`. [Obtén més informació sobre com pots adoptar detectors d'esdeveniments passius](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "No utilitza detectors passius per millorar el rendiment del desplaçament"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Utilitza detectors passius per millorar el rendiment del desplaçament"}, "core/audits/errors-in-console.js | description": {"message": "Els errors registrats a la consola indiquen problemes pendents de resoldre. Poden provenir d'errors de sol·licitud de la xarxa i d'altres problemes del navegador. [Més informació sobre aquests errors en l'auditoria de diagnòstic de la consola](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Els errors del navegador s'han registrat a la consola"}, "core/audits/errors-in-console.js | title": {"message": "No s'ha registrat cap error del navegador a la consola"}, "core/audits/font-display.js | description": {"message": "Aprofita la funció CSS `font-display` per assegurar-te que els usuaris puguin veure el text mentre es carreguen les fonts web. [Obtén més informació sobre `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Assegura't que el text continuï visible durant la càrrega de les fonts web"}, "core/audits/font-display.js | title": {"message": "Tot el text continua visible durant les càrregues de les fonts web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse no ha pogut comprovar automàticament el valor `font-display` de l'URL d'origen {fontOrigin}.}other{Lighthouse no ha pogut comprovar automàticament els valors `font-display` de l'URL d'origen {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON> (real)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON>'aspecte (mostrada)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Les dimensions de la visualització d'imatges han de coincidir amb la relació d'aspecte natural. [Obtén més informació sobre la relació d'aspecte de les imatges](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Mostra les imatges amb una relació d'aspecte incorrecta"}, "core/audits/image-aspect-ratio.js | title": {"message": "Mostra les imatges amb una relació d'aspecte correcta"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Mida real"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Mida mostrada"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Mida prevista"}, "core/audits/image-size-responsive.js | description": {"message": "Les dimensions naturals de les imatges han de ser proporcionals a la mida de visualització i la relació de píxels per maximitzar la claredat de les imatges. [Obtén informació sobre com pots proporcionar imatges responsives](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Utilitza imatges amb una resolució baixa"}, "core/audits/image-size-responsive.js | title": {"message": "Utilitza imatges amb una resolució adequada"}, "core/audits/installable-manifest.js | already-installed": {"message": "L'aplicació ja està instal·lada"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "No s'ha pogut baixar una icona necessària del fitxer de manifest"}, "core/audits/installable-manifest.js | columnValue": {"message": "<PERSON><PERSON><PERSON> l'error"}, "core/audits/installable-manifest.js | description": {"message": "El Service Worker és la tecnologia que fa possible que la teva aplicació utilitzi moltes funcions d'aplicació web progressiva, com ara funcionar sense connexió, poder afegir-se a la pantalla d'inici i mostrar notificacions automàtiques. Amb un Service Worker adequat i implementacions del fitxer de manifest, els navegadors poden demanar als usuaris de manera proactiva que afegeixin la teva aplicació a la pantalla d'inici, cosa que permet obtenir més interaccions. [Obtén més informació sobre els requisits d'instal·labilitat del fitxer de manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motiu}other{# motius}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "El fitxer de manifest de l'aplicació web o el Service Worker no compleixen els requisits d'instal·lació"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "L'identificador i l'URL de l'aplicació Play Store no coincideixen"}, "core/audits/installable-manifest.js | in-incognito": {"message": "La pàgina es carrega en una finestra d'incògnit"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "La propietat \"display\" del fitxer de manifest ha de ser \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "El fitxer de manifest conté el camp \"display_override\" i el primer mode de visualització admès ha de ser \"standalone\", \"fullscreen\" o \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "El fitxer de manifest no s'ha pogut obtenir, és buit o no s'ha pogut analitzar"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "L'URL del fitxer de manifest ha canviat mentre s'estava recollint el fitxer de manifest."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "El fitxer de manifest no conté cap camp \"name\" o \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "El manifest no conté cap icona adequada. Es requereix un format PNG, SVG o WebP de com a mínim {value0} píxels, l'atribut \"sizes\" ha d'estar establert i l'atribut \"purpose\", si s'estableix, ha d'incloure \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "No s'ha proporcionat cap icona que tingui com a mínim {value0} píxels quadrats en format PNG, SVG o WebP i que tingui l'atribut \"purpose\" sense establir o definit en \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "La icona baixada era buida o estava malmesa"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "No s'ha proporcionat cap identificador de Play Store"}, "core/audits/installable-manifest.js | no-manifest": {"message": "La pàgina no té cap URL <link> del fitxer de manifest"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "No s'ha detectat cap Service Worker coincident. Pot ser que hagis de tornar a carregar la pàgina o comprovar que l'abast del Service Worker per a la pàgina actual englobi l'abast i l'URL d'inici del fitxer de manifest."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "No s'ha pogut comprovar el Service Worker perquè el fitxer de manifest no té cap camp \"start_url\""}, "core/audits/installable-manifest.js | noErrorId": {"message": "No es reconeix l'identificador d'error d'instal·lació \"{errorId}\""}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "La pàgina no es publica des d'un origen segur"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "La pàgina no es carrega al marc principal"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "La pàgina no funciona sense connexió"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "S'ha desinstal·lat la PWA i s'estan restablint les comprovacions d'instal·labilitat."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "La plataforma de l'aplicació especificada no s'admet a Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "El fitxer de manifest especifica prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications només s'admet a Chrome beta i als canals estables a Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse no ha pogut determinar si hi havia un Service Worker. Prova-ho amb una versió més recent de Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "L'esquema d'URL del manifest ({scheme}) no s'admet a Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "L'URL d'inici del manifest no és vàlid"}, "core/audits/installable-manifest.js | title": {"message": "El fitxer de manifest de l'aplicació web i el Service Worker no compleixen els requisits d'instal·lació"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Un URL del fitxer de manifest conté un nom d'usuari, una contrasenya o un port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "La pàgina no funciona sense connexió. A partir de Chrome 93, versió estable d'agost de 2021, es considerarà que la pàgina no es pot instal·lar."}, "core/audits/is-on-https.js | allowed": {"message": "Permès"}, "core/audits/is-on-https.js | blocked": {"message": "Bloquejat"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL no segur"}, "core/audits/is-on-https.js | columnResolution": {"message": "Resolució de sol·licituds"}, "core/audits/is-on-https.js | description": {"message": "Tots els llocs web haurien d'estar protegits amb HTTPS, fins i tot els que no gestionen dades sensibles. Això inclou evitar el [contingut mixt](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), en què alguns recursos es carreguen per HTTP encara que la sol·licitud inicial es faci per HTTPS. L'HTTPS evita que intrusos manipulin o detectin passivament les comunicacions entre la teva aplicació i els usuaris, i és un requisit previ per a HTTP/2 i per a moltes API de plataforma web noves. [Obtén més informació sobre HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{S'ha trobat 1 sol·licitud no segura}other{S'han trobat # sol·licituds no segures}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "No utilitza HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Utilitza HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Actualitzat automàticament a HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Permès amb un advertiment"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Aquest és l'element més gran amb contingut que s'ha renderitzat a la finestra gràfica. [Més informació sobre la renderització de l'element més gran amb contingut](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element de renderització de l'element més gran amb contingut"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Contribució al CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Aquests són els elements de DOM que més contribueixen al CLS de la pàgina. [Informació sobre com es pot millorar el CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "<PERSON><PERSON><PERSON> els canvis de disseny importants"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Les imatges de la meitat superior de la pàgina que es carreguen lentament es renderitzen més tard al cicle de vida de la pàgina, cosa que pot retardar la renderització de l'element més gran amb contingut. [Obtén més informació sobre la càrrega lenta òptima](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "La imatge de renderització de l'element més gran amb contingut s'ha carregat lentament"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "La imatge de renderització de l'element més gran amb contingut no s'ha carregat lentament"}, "core/audits/long-tasks.js | description": {"message": "Enumera les tasques més llargues del fil principal, cosa que resulta útil per identificar què contribueix més al retard de les interaccions. [Informació sobre com pots evitar les tasques llargues al fil principal](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# tasca llarga trobada}other{# tasques llargues trobades}}"}, "core/audits/long-tasks.js | title": {"message": "Evita les tasques llargues del fil principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoria"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Et recomanem que redueixis el temps dedicat a analitzar, compilar i executar JavaScript. Et pot ajudar utilitzar càrregues útils de JavaScript més petites. [Informació sobre com pots minimitzar el treball al fil principal](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimitza el treball al fil principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimitza el treball al fil principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Per arribar al major nombre d'usuaris possible, els llocs web han de funcionar en tots els navegadors principals. [Obtén informació sobre la compatibilitat entre navegadors](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "El lloc web funciona en diversos navegadors"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Comprova que l'URL de cada pàgina sigui un enllaç profund i únic per poder compartir-lo als mitjans socials. [Obtén més informació sobre com pots proporcionar enllaços profunds](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada pàgina té un URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Les transicions en navegar per l'aplicació han de ser àgils, fins i tot en xarxes lentes. És una experiència clau en la percepció del rendiment per part de l'usuari. [Obtén més informació sobre les transicions de pàgines](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "No sembla que les transicions entre pàgines es bloquegin a la xarxa"}, "core/audits/maskable-icon.js | description": {"message": "Una icona adaptativa garanteix que la imatge omple tota la forma sense adquirir el format de bústia en instal·lar l'aplicació en un dispositiu. [Obtén informació sobre les icones de manifest adaptatives](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "El manifest no té cap icona adaptativa"}, "core/audits/maskable-icon.js | title": {"message": "El manifest té una icona adaptativa"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "El canvi de disseny acumulatiu mesura el moviment d'elements visibles a la finestra gràfica. [Obtén més informació sobre la mètrica Canvi de disseny acumulatiu](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "La mètrica Interacció amb la renderització següent mesura la capacitat de resposta de la pàgina; és a dir, el temps que tarda la pàgina a respondre visiblement a l'entrada de l'usuari. [Obtén més informació sobre la mètrica Interacció amb la renderització següent](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "La mètrica Primera renderització de contingut marca el moment en què es renderitza el primer text o la primera imatge. [Obtén més informació sobre la mètrica Primera renderització de contingut](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "La mètrica Primera renderització significativa mesura el moment en què el contingut principal d'una pàgina és visible. [Obtén més informació sobre la mètrica Primera renderització significativa](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "La mètrica Temps fins que és interactiva és el que tarda la pàgina a fer-se completament interactiva. [Obtén més informació sobre la mètrica Temps fins que és interactiva](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "La renderització de l'element més gran amb contingut marca el moment en què es renderitza el text o la imatge més gran. [Més informació sobre la mètrica Renderització de l'element més gran amb contingut](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "El retard potencial màxim respecte a la primera interacció que els usuaris es poden trobar és la durada de la tasca més llarga. [Obtén més informació sobre el retard potencial màxim respecte a la primera interacció](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "L'índex de velocitat mostra la rapidesa amb què s'emplena el contingut d'una pàgina. [Obtén més informació sobre la mètrica Índex de velocitat](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Suma de tots els períodes de temps entre l'FCP i el Temps fins que és interactiva, quan la durada de la tasca ha superat els 50 ms, expressada en mil·lisegons. [Obtén més informació sobre la mètrica Temps de bloqueig total](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Els temps d'anada i tornada a la xarxa afecten considerablement el rendiment. Si el temps d'anada i tornada a un origen és llarg, indica que els servidors de més a prop de l'usuari podrien millorar el rendiment. [Obtén més informació sobre el temps d'anada i tornada](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Temps d'anada i tornada a la xarxa"}, "core/audits/network-server-latency.js | description": {"message": "Les latències del servidor poden afectar el rendiment web. Si són llargues en un origen, indiquen que el servidor està sobrecarregat o que té un rendiment baix de backend. [Obtén més informació sobre el temps de resposta del servidor](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Latències dorsals del servidor"}, "core/audits/no-unload-listeners.js | description": {"message": "L'esdeveniment `unload` no es pot activar amb fiabilitat i detectar-lo pot impedir optimitzacions del navegador, com ara la memòria cau endavant/enrere. Substitueix-lo pels esdeveniments `pagehide` o `visibilitychange`. [Més informació sobre com es poden baixar els detectors d'esdeveniments](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registra un detector de `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Evita detectors d'esdeveniments `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Les animacions que no són compostes poden ser lentes i augmentar el canvi de disseny acumulatiu. [Informació sobre com pots evitar les animacions no compostes](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{S'ha trobat # element animat}other{S'han trobat # elements animats}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "És possible que la propietat relacionada amb \"filter\" mogui píxels"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "L'objectiu té una altra animació que no s'admet"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "L'efecte té un mode compost que no és \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Evita les animacions que no siguin compostes"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "La propietat relacionada amb \"transform\" depèn de la mida del quadre"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{La propietat CSS no s'admet: {properties}}other{Les propietats CSS no s'admeten: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "L'efecte té paràmetres de temps no admesos"}, "core/audits/performance-budget.js | description": {"message": "Manté la quantitat i la mida de les sol·licituds de xarxa ajustades als objectius establerts al pressupost de rendiment que s'ha proporcionat. [Obtén més informació sobre els pressupostos de rendiment](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 sol·licitud}other{# sol·licituds}}"}, "core/audits/performance-budget.js | title": {"message": "Pressupost de rendiment"}, "core/audits/preload-fonts.js | description": {"message": "Precarrega les fonts `optional` perquè els visitants nous puguin utilitzar-les. [Més informació sobre la precàrrega de fonts](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Les fonts amb `font-display: optional` no estan precarregades"}, "core/audits/preload-fonts.js | title": {"message": "Les fonts amnb `font-display: optional` estan precarregades"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Si l'element LCP s'afegeix dinàmicament a la pàgina, has de precarregar la imatge per poder millorar l'LCP. [Obtén més informació sobre com es poden precarregar els elements LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Precarrega la imatge de renderització de l'element més gran amb contingut"}, "core/audits/redirects.js | description": {"message": "La mètrica Redireccions introdueix retards addicionals abans de poder carregar la pàgina. [Obtén informació sobre com pots evitar les redireccions de pàgina](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evita les redireccions múltiples a pàgines"}, "core/audits/resource-summary.js | description": {"message": "Per definir els pressupostos de la quantitat i la mida dels recursos de la pàgina, afegeix un fitxer budget.json. [Obtén més informació sobre els pressupostos de rendiment](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 sol·licitud • {byteCount, number, bytes} KiB}other{# sol·licituds • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Mantén els recomptes de les sol·licituds baixos i les mides de les transferències petites"}, "core/audits/seo/canonical.js | description": {"message": "Els enllaços canònics suggereixen quins URL s'han de mostrar als resultats de la cerca. [Obtén més informació sobre els enllaços canònics](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Hi ha diversos URL en conflicte ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL no vàlid ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Dirigeix a una altra ubicació de tipus \"`hreflang`\" ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "No és un URL absolut ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Apunta a l'URL arrel (la pàgina d'inici) del domini en lloc d'apuntar a una pàgina equivalent de contingut"}, "core/audits/seo/canonical.js | failureTitle": {"message": "El document no té un valor `rel=canonical` vàlid"}, "core/audits/seo/canonical.js | title": {"message": "El document té un valor `rel=canonical` vàlid"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Enllaç que no es pot rastrejar"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "És possible que els motors de cerca utilitzin atributs `href` als enllaços per rastrejar llocs web. Assegura't que l'atribut `href` dels elements d'ancoratge enllaci a la destinació adequada. D'aquesta manera, es podran detectar més pàgines del lloc web. [Informació sobre com pots fer que els enllaços es puguin rastrejar](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Els enllaços no es poden rastrejar"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Els enllaços es poden rastrejar"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Text il·legible addicional"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Cos de font"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% del text de pàgina"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Els cossos de font inferiors als 12 píxels són massa petits i obliguen els usuaris de mòbils a \"pinçar per fer zoom\" i poder llegir el text. Intenta que la mida de més del 60% del text de la pàgina sigui igual o superior als 12 píxels. [Obtén més informació sobre els cossos de font llegibles](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "El {decimalProportion, number, extendedPercent} del text és llegible"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "El text és il·legible perquè no hi ha cap metaetiqueta de finestra gràfica optimitzada per a pantalles de dispositius mòbils."}, "core/audits/seo/font-size.js | failureTitle": {"message": "El document no utilitza lletres amb mides llegibles"}, "core/audits/seo/font-size.js | legibleText": {"message": "Text llegible"}, "core/audits/seo/font-size.js | title": {"message": "El document utilitza lletres amb mides llegibles"}, "core/audits/seo/hreflang.js | description": {"message": "Els enllaços de tipus \"hreflang\" informen els motors de cerca de quina versió d'una pàgina han d'incloure als resultats de cerca per a una regió o un idioma concrets. [Obtén més informació sobre `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "El document no té un valor `hreflang` vàlid"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valor href relatiu"}, "core/audits/seo/hreflang.js | title": {"message": "El document té un valor `hreflang` vàlid"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Codi d'idioma inesperat"}, "core/audits/seo/http-status-code.js | description": {"message": "És possible que les pàgines amb codi d'estat HTTP incorrecte no s'indexin correctament. [Obtén més informació sobre els codis d'estat HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "El codi d'estat HTTP de la pàgina no és correcte"}, "core/audits/seo/http-status-code.js | title": {"message": "El codi d'estat HTTP de la pàgina és correcte"}, "core/audits/seo/is-crawlable.js | description": {"message": "Si els motors de cerca no tenen permís per rastrejar les teves pàgines, no les poden incloure als resultats de cerca. [Obtén més informació sobre les directives de rastrejadors](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "La pàgina està configurada per bloquejar la indexació"}, "core/audits/seo/is-crawlable.js | title": {"message": "La indexació no està bloquejada en aquesta pàgina"}, "core/audits/seo/link-text.js | description": {"message": "El text descriptiu dels enllaços ajuda els motors de cerca a entendre el contingut. [Obtén informació sobre com pots fer que els enllaços siguin més accessibles](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{S'ha trobat 1 enllaç}other{S'han trobat # enllaços}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Els enllaços no tenen text descriptiu"}, "core/audits/seo/link-text.js | title": {"message": "Els enllaços tenen text descriptiu"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Executa l'[eina de proves de dades estructurades](https://search.google.com/structured-data/testing-tool/) i l'[eina Structured Data Linter](http://linter.structured-data.org/) per validar aquest tipus de dades. [Obtén més informació sobre les dades estructurades](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Les dades estructurades són vàlides"}, "core/audits/seo/meta-description.js | description": {"message": "És possible que s'incloguin metadescripcions als resultats de la cerca per resumir breument el contingut de la pàgina. [Obtén més informació sobre la metadescripció](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "El text de la descripció és buit."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "El document no té cap metadescripció"}, "core/audits/seo/meta-description.js | title": {"message": "El document té una metadescripció"}, "core/audits/seo/plugins.js | description": {"message": "Els motors de cerca no poden indexar el contingut dels connectors. A més, molts dispositius restringeixen o no admeten connectors. [Obtén més informació sobre com pots evitar els connectors](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "El document utilitza connectors"}, "core/audits/seo/plugins.js | title": {"message": "El document evita els connectors"}, "core/audits/seo/robots-txt.js | description": {"message": "Si el format del fitxer robots.txt no és correcte, és possible que els rastrejadors no puguin entendre com vols que rastregin o indexin el lloc web. [Obtén més informació sobre robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "La sol·licitud del fitxer robots.txt ha tornat l'estat HTTP següent: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{S'ha trobat 1 error}other{S'han trobat # errors}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse no ha pogut baixar el fitxer robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "El fitxer robots.txt no és vàlid"}, "core/audits/seo/robots-txt.js | title": {"message": "El fitxer robots.txt és vàlid"}, "core/audits/seo/tap-targets.js | description": {"message": "Els elements interactius, com ara els botons i els enllaços, han de ser prou grans (48 x 48 píxels) i han de tenir prou espai al voltant perquè els usuaris els puguin tocar sense que se superposin a altres elements. [Obtén més informació sobre els objectius tàctils](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "El {decimalProportion, number, percent} de les mides dels objectius tàctils és correcte"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Els elements tàctils són massa petits perquè no hi ha cap metaetiqueta de finestra gràfica optimitzada per a pantalles de mòbil"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "La mida dels elements tàctils no és correcta"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Objectiu superposat"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Element tàctil"}, "core/audits/seo/tap-targets.js | title": {"message": "La mida dels elements tàctils és correcta"}, "core/audits/server-response-time.js | description": {"message": "Fes que el temps de resposta del servidor per al document principal sigui breu, ja que la resta de sol·licituds en depenen. [Obtén més informació sobre la mètrica Temps fins al primer byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "El document arrel ha tardat {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Redueix el temps inicial de resposta del servidor"}, "core/audits/server-response-time.js | title": {"message": "El temps inicial de resposta del servidor ha estat breu"}, "core/audits/service-worker.js | description": {"message": "El Service Worker és la tecnologia que fa possible que la teva aplicació utilitzi moltes funcions d'aplicació web progressiva, com ara funcionar sense connexió, poder afegir-se a la pantalla d'inici i mostrar notificacions automàtiques. [Obtén més informació sobre els Service Workers](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Tot i que un Service Worker controla aquesta pàgina, no s'ha trobat cap atribut `start_url` perquè el fitxer de manifest no s'ha pogut analitzar com a format JSON vàlid"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Tot i que un Service Worker controla aquesta pàgina, l'atribut `start_url` ({startUrl}) no és a l'abast del Service Worker ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Tot i que un Service Worker controla aquesta pàgina, no s'ha trobat cap `start_url` perquè no s'ha obtingut cap fitxer de manifest."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Aquest origen té un Service Worker o més, però la pàgina ({pageUrl}) està fora de l'abast."}, "core/audits/service-worker.js | failureTitle": {"message": "No registra cap Service Worker que controli la pàgina i l'atribut `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registra un Service Worker que controla la pàgina i l'atribut `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Utilitzar una pantalla inicial temàtica garanteix una experiència d'alta qualitat quan els usuaris inicien l'aplicació des de la pantalla d'inici. [Obtén més informació sobre les pantalles inicials](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "No està configurat per a una pantalla inicial personalitzada"}, "core/audits/splash-screen.js | title": {"message": "Està configurat per a una pantalla inicial personalitzada"}, "core/audits/themed-omnibox.js | description": {"message": "Es pot aplicar un tema a la barra d'adreces del navegador perquè faci joc amb el teu lloc web. [Obtén més informació sobre com pots tematitzar la barra d'adreces](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "No estableix un color temàtic per a la barra d'adreces."}, "core/audits/themed-omnibox.js | title": {"message": "Estableix un color temàtic per a la barra d'adreces."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (èxit del client)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (màrqueting)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (vídeo)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Producte"}, "core/audits/third-party-facades.js | description": {"message": "Pot ser que algunes insercions de tercers es carreguin lentament. Pots substituir-les per una façana fins que s'hagin de fer servir. [Obtén informació sobre com pots ajornar tercers amb una façana](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternativa de façana disponible}other{# alternatives de façana disponibles}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Pot ser que alguns recursos de tercers es carreguin lentament amb una façana"}, "core/audits/third-party-facades.js | title": {"message": "Carrega lentament recursos externs amb façanes"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Tercers"}, "core/audits/third-party-summary.js | description": {"message": "El codi de tercers pot afectar significativament el rendiment de la càrrega. Limita el nombre de proveïdors externs redundants i prova de carregar codi de tercers quan la càrrega principal de la pàgina ha finalitzat. [Obtén informació sobre com pots minimitzar l'impacte extern](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "El codi de tercers ha bloquejat el fil principal durant {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Redueix l'impacte del codi de tercers"}, "core/audits/third-party-summary.js | title": {"message": "Redueix l'ús de tercers"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Mesurament"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Mè<PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Defineix un pressupost de temps perquè t'ajudi a fer un seguiment del rendiment del teu lloc web. Els llocs web que funcionen bé es carreguen ràpidament i responen de pressa als esdeveniments d'entrada dels usuaris. [Obtén més informació sobre els pressupostos de rendiment](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Pressupost de temps"}, "core/audits/unsized-images.js | description": {"message": "Als elements d'imatge, defineix una amplada i una alçada explícites per reduir els canvis de disseny i millorar el CLS. [Informació sobre com pots definir les dimensions d'imatge](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Els elements d'imatge no tenen una `width` ni una `height` explícites."}, "core/audits/unsized-images.js | title": {"message": "Els elements d'imatge tenen una `width` i una `height` explícites"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Et recomanem que utilitzis l'API Temps d'usuari amb la teva aplicació per mesurar-ne el rendiment al món real durant experiències clau dels usuaris. [Obtén més informació sobre les marques de Temps d'usuari](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 temps d'usuari}other{# temps d'usuari}}"}, "core/audits/user-timings.js | title": {"message": "Marques i mesures de Temps d'usuari"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "S'ha trobat un element `<link rel=preconnect>` per a \"{securityOrigin}\", però el navegador no l'ha utilitzat. Comprova que estiguis utilitzant correctament l'atribut `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Et recomanem que afegeixis suggeriments de recursos `preconnect` o `dns-prefetch` per establir connexions anticipades a orígens importants de tercers. [Obtén informació sobre com pots connectar-te prèviament als orígens obligatoris](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Connecta't prèviament als orígens necessaris"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "S'han trobat més de 2 connexions `<link rel=preconnect>`. S'han d'utilitzar amb moderació i només per als orígens més importants."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "S'ha trobat un element `<link rel=preconnect>` per a \"{securityOrigin}\", però el navegador no l'ha utilitzat. Utilitza `preconnect` només per a orígens importants que la pàgina sol·licitarà de segur."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "S'ha trobat un element `<link>` de precàrrega per a \"{preloadURL}\", però el navegador no l'ha utilitzat. Comprova que estiguis utilitzant correctament l'atribut `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Et recomanem que utilitzis `<link rel=preload>` per prioritzar l'obtenció de recursos que en aquests moments se sol·liciten en un moment posterior de la càrrega de la pàgina. [Obtén informació sobre com pots precarregar les sol·licituds clau](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Precarrega les sol·licituds de clau"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL del mapa"}, "core/audits/valid-source-maps.js | description": {"message": "Els mapes d'origen tradueixen el codi reduït al codi font original. Això ajuda els desenvolupadors a depurar durant la producció. A més, Lighthouse pot proporcionar estadístiques addicionals. Planteja't implementar mapes d'origen per treure partit d'aquests avantatges. [Obtén més informació sobre els mapes d'origen](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Falten mapes d'origen per a un fitxer JavaScript propi gran"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Un fitxer gran de JavaScript no té un mapa d'origen"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Advertiment: falta 1 element a `.sourcesContent`}other{Advertiment: falten # elements a `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "La pàgina té mapes d'origen vàlids"}, "core/audits/viewport.js | description": {"message": "Una metaetiqueta `<meta name=\"viewport\">` no només optimitza la teva aplicació per a mides de pantalla de mòbil, sinó que també evita [que l'entrada d'usuari es retardi 300 mil·lisegons](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Obtén més informació sobre com pots utilitzar la metaetiqueta de finestra gràfica](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "No s'ha trobat cap etiqueta `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "No té una etiqueta `<meta name=\"viewport\">` amb l'atribut `width` o `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Té una etiqueta `<meta name=\"viewport\">` amb l'atribut `width` o `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Aquest és el treball de bloqueig de fils que es produeix durant el mesurament de la interacció amb la renderització següent. [Obtén més informació sobre la mètrica Interacció amb la renderització següent](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "S'han dedicat {timeInMs, number, milliseconds} ms a l'esdeveniment \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Objectiu de l'esdeveniment"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimitza el treball durant la interacció amb una tecla"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Retard respecte a la interacció"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Retard en la presentació"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Temps de processament"}, "core/audits/work-during-interaction.js | title": {"message": "Minimitza el treball durant la interacció amb una tecla"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Aquí tens recomanacions per millorar l'ús dels elements d'ARIA a l'aplicació. També poden millorar l'experiència dels usuaris de tecnologia assistencial (per exemple, de lectors de pantalla)."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Aquí tens recomanacions per proporcionar contingut alternatiu d'àudio i vídeo. També poden millorar l'experiència dels usuaris amb discapacitat auditiva o visual."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Àudio i vídeo"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Aquests elements destaquen les pràctiques recomanades més habituals pel que fa a l'accessibilitat."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Pràctiques recomanades"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Aquestes comprovacions destaquen les oportunitats per [millorar l'accessibilitat de l'aplicació web](https://developer.chrome.com/docs/lighthouse/accessibility/). Només poden detectar un subconjunt de problemes d'accessibilitat automàticament, de manera que et recomanem que també hi facis proves manuals."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Aquests elements tracten àrees que les eines de proves automatitzades no poden cobrir. Obtén més informació a la nostra guia sobre [com es duen a terme ressenyes d'accessibilitat](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Accessibilitat"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Aquí tens idees per millorar la llegibilitat del contingut."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Aquí tens recomanacions per millorar la interpretació del contingut per part dels usuaris de diferents configuracions regionals."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalització i localització"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Aquí tens recomanacions per millorar la semàntica dels controls a l'aplicació. També poden millorar l'experiència dels usuaris de tecnologia assistencial (per exemple, de lectors de pantalla)."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Noms i etiquetes"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Aquí tens recomanacions per millorar la navegació amb el teclat a l'aplicació."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegació"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Aquí tens recomanacions per millorar la lectura de dades de taules o llistes utilitzant la tecnologia assistencial (per exemple, lectors de pantalla)."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON> i llistes"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilitat amb els navegadors"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Pràctiques recomanades"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "General"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Confiança i seguretat"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Experiència d'usuari"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Els pressupostos de rendiment estableixen uns estàndards per al rendiment del teu lloc web."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Pressupostos"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Més informació sobre el rendiment de la teva aplicació. Aquests números no [afecten directament](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) el resultat del rendiment."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "L'aspecte més crític del rendiment és la velocitat amb què es renderitzen els píxels en pantalla. Mètriques clau: Primera renderització de contigut, Primera renderització significativa"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Millores de la primera renderització"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Aquests suggeriments poden ajudar la pàgina a carregar-se més de pressa. No [afecten directament](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) el resultat del rendiment."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunitats"}, "core/config/default-config.js | metricGroupTitle": {"message": "Mètriques"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Millora l'experiència general de càrrega, de manera que la pàgina respongui i estigui preparada per utilitzar-se al més aviat possible. Mètriques clau: Temps fins que és interactiva, Índex de velocitat"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Millores generals"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Rendiment"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Aquestes validacions verifiquen els aspectes d'una aplicació web progressiva. [Obtén informació sobre les característiques d'una bona aplicació web progressiva](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "La [llista de comprovació de referència per a aplicacions web progressives](https://web.dev/pwa-checklist/) requereix aquestes validacions, però Lighthouse no les verifica automàticament. Tot i que no afecten la teva puntuació, és important que les verifiquis manualment."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Es pot instal·lar"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimitzat per a PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Aquestes comprovacions garanteixen que la pàgina segueixi les recomanacions d'optimització bàsica en cercadors. Hi ha molts factors addicionals que Lighthouse no puntua aquí i que poden afectar la classificació a les cerques, inclòs el rendiment a [Dad<PERSON> vitals web principals](https://web.dev/learn-core-web-vitals/). [Obtén més informació sobre Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Executa aquestes validacions addicionals al lloc web per comprovar les pràctiques addicionals recomanades per a SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Dona a l'HTML un format que permeti als rastrejadors entendre millor el contingut de l'aplicació."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Pràctiques recomanades pel que fa al contingut"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Perquè l'aplicació es mostri als resultats de cerca, els rastrejadors necessiten tenir-hi accés."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastreig i indexació"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Comprova que les pàgines estiguin adaptades per a mòbils, de manera que els usuaris no hagin de pinçar o ampliar les pàgines de contingut per poder llegir-les. [Obtén més informació sobre com pots adaptar les pàgines per a mòbils](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Adaptació per a mòbils"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Sembla que el dispositiu provat té una CPU més lenta que la que espera Lighthouse. Això pot repercutir negativament en el teu resultat del rendiment. Obtén més informació sobre com pots [calibrar un multiplicador de retenció de la CPU adequat](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "És possible que la pàgina no s'estigui carregant com s'esperava perquè s'ha redirigit el teu URL de prova ({requested}) a {final}. Intenta provar el segon URL directament."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "La pàgina s'ha carregat molt lentament i ha sobrepassat el límit de temps. És possible que els resultats siguin incomplets."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "S'ha esgotat el temps d'espera per esborrar la memòria cau del navegador. Prova de tornar a auditar aquesta pàgina i, si el problema continua, informa de l'error."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Pot ser que hi hagi dades emmagatzemades que afectin el rendiment de càrrega en aquesta ubicació: {locations}. Audita aquesta pàgina en una finestra d'incògnit per impedir que aquests recursos afectin els teus resultats.}other{Pot ser que hi hagi dades emmagatzemades que afectin el rendiment de càrrega en aquestes ubicacions: {locations}. Audita aquesta pàgina en una finestra d'incògnit per impedir que aquests recursos afectin els teus resultats.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "S'ha esgotat el temps d'espera per esborrar les dades d'origen. Prova de tornar a auditar aquesta pàgina i, si el problema continua, informa de l'error."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Només les pàgines carregades mitjançant una sol·licitud GET són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Només es poden desar a la memòria cau les pàgines amb un codi d'estat 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome ha detectat un intent d'executar JavaScript mentre era a la memòria cau."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Actualment, les pàgines que han sol·licitat un AppBanner no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Les marques han desactivat la memòria cau endavant/enrere. Visita chrome://flags/#back-forward-cache per activar-la de manera local en aquest dispositiu."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "La línia d'ordres ha desactivat la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "La memòria cau endavant/enrere està desactivada a causa de memòria insuficient."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "El delegat no admet la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "La memòria cau endavant/enrere s'ha desactivat per al prerenderitzador."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "La pàgina no es pot desar a la memòria cau perquè té una instància BroadcastChannel amb detectors registrats."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Les pàgines amb una capçalera cache-control:no-store no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "La memòria cau s'ha esborrat intencionadament."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "La pàgina s'ha tret de la memòria cau perquè s'hi pugui desar una altra pàgina."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Actualment, les pàgines que contenen connectors no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Les pàgines que utilitzen l'API FileChooser no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Les pàgines que utilitzen l'API File System Access no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Les pàgines que utilitzen Media Device Dispatcher no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Un reproductor multimèdia estava reproduint contingut en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Les pàgines que utilitzen l'API MediaSession i tenen configurat un estat de reproducció no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Les pàgines que utilitzen l'API MediaSession i tenen configurats gestors d'acció no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "La memòria cau endavant/enrere està desactivada a causa d'un lector de pantalla."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Les pàgines que utilitzen SecurityHandler no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Les pàgines que utilitzen l'API Serial no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Les pàgines que utilitzen l'API WebAuthentication no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Les pàgines que utilitzen l'API WebBluetooth no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Les pàgines que utilitzen l'API WebUSB no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Actualment, les pàgines que utilitzen un worker o un worklet especial no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "El document no s'havia acabat de carregar abans de sortir-ne."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Hi havia el bàner d'aplicacions en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Hi havia el gestor de contrasenyes de Chrome en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Hi havia en curs un procés de DOM Distiller en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Hi havia el lector de DOM Distiller en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "La memòria cau endavant/enrere està desactivada perquè hi ha extensions que utilitzen l'API de missatges."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Les extensions amb una connexió de llarga durada haurien de tancar la connexió abans d'entrar a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Les extensions amb una connexió de llarga durada provaven d'enviar missatges a marcs de la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "La memòria cau endavant/enrere està desactivada a causa de les extensions."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Es mostrava un quadre de diàleg modal (per exemple, de reenviament d'un formulari o de contrasenya HTTP) en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Es mostrava la pàgina sense connexió en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Hi havia la barra d'intervenció de memòria insuficient en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Hi havia sol·licituds de permisos en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Hi havia un bloquejador de finestres emergents en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Es mostraven detalls de Navegació segura en sortir de la pàgina."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Navegació segura ha considerat que aquesta pàgina era abusiva i ha bloquejat la finestra emergent."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "S'ha activat un service worker mentre la pàgina era a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "La memòria cau endavant/enrere està desactivada a causa d'un error del document."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Les pàgines que fan servir <PERSON> no es poden emmagatzemar a bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "La pàgina s'ha tret de la memòria cau perquè s'hi pugui desar una altra pàgina."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Actualment, les pàgines que han donat accés de transmissió de contingut multimèdia no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Actualment, les pàgines que utilitzen portals no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Actualment, les pàgines que utilitzen IdleManager no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Actualment, les pàgines que tenen una connexió IndexedDB oberta no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "S'han utilitzat API no aptes."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Actualment, les pàgines que s'estan injectant a JavaScript mitjançant extensions no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Actualment, les pàgines en què s'injecta JavaScript mitjançant extensions no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON> intern."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "La memòria cau endavant/enrere està desactivada a causa d'una sol·licitud de keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Actualment, les pàgines que utilitzen el bloqueig de teclat no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | loading": {"message": "La pàgina no s'havia acabat de carregar abans de sortir-ne."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Les pàgines el recurs principal de les quals té cache-control:no-cache no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Les pàgines el recurs principal de les quals té cache-control:no-store no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "La navegació s'ha cancel·lat abans que la pàgina es pogués restaurar de la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "La pàgina s'ha tret de la memòria cau perquè una connexió a la xarxa activa ha rebut massa dades. Chrome limita la quantitat de dades que pot rebre una pàgina mentre està desada a la memòria cau."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Actualment, les pàgines que tenen un fetch() o un XHR en trànsit no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "La pàgina s'ha tret de la memòria cau endavant/enrere perquè una sol·licitud a la xarxa activa incloïa una redirecció."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "La pàgina s'ha tret de la memòria cau perquè una connexió a la xarxa ha estat oberta durant massa temps. Chrome limita la quantitat de temps durant el qual una pàgina pot rebre dades mentre està desada a la memòria cau."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Les pàgines que no tenen una capçalera de resposta vàlida no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "La navegació ha tingut lloc en un marc diferent del marc principal."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Actualment, les pàgines amb transaccions de base de dades indexades en curs no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Actualment, les pàgines amb una sol·licitud de xarxa en trànsit no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Actualment, les pàgines amb una sol·licitud de xarxa de recollida en trànsit no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Actualment, les pàgines amb una sol·licitud de xarxa en trànsit no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Actualment, les pàgines amb una sol·licitud de xarxa XHR en trànsit no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Actualment, les pàgines que utilitzen PaymentManager no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Actualment, les pàgines que utilitzen pantalla en pantalla no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | portal": {"message": "Actualment, les pàgines que utilitzen portals no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | printing": {"message": "Actualment, les pàgines que mostren la IU d'impressió no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "La pàgina s'ha obert amb `window.open()` i una altra pestanya hi fa referència o la pàgina ha obert una finestra."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "El procés renderitzador de la pàgina a la memòria cau endavant/enrere ha fallat."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "El procés renderitzador de la pàgina s'ha cancel·lat a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Actualment, les pàgines que han sol·licitat permisos de captura d'àudio no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Actualment, les pàgines que han sol·licitat permisos de sensor no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Actualment, les pàgines que han sol·licitat permisos de recollida o sincronització en segon pla no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Actualment, les pàgines que han sol·licitat permisos MIDI no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Actualment, les pàgines que han sol·licitat permisos de notificacions no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Actualment, les pàgines que han sol·licitat accés d'emmagatzematge no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Actualment, les pàgines que han sol·licitat permisos de captura de vídeo no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Només es poden desar a la memòria cau les pàgines amb un esquema d'URL HTTP o HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Un service worker ha reclamat la pàgina mentre era a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Un service worker ha provat d'enviar un `MessageEvent` a la pàgina que és a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "S'ha cancel·lat el registre de Service Worker mentre una pàgina era a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "La pàgina s'ha tret de la memòria cau endavant/enrere a causa de l'activació d'un service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "S'ha reiniciat Chrome i s'han esborrat les entrades de la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Actualment, les pàgines que utilitzen SharedWorker no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Actualment, les pàgines que utilitzen SpeechRecognizer no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Actualment, les pàgines que utilitzen SpeechSynthesis no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Un iframe de la pàgina ha començat una navegació que no s'ha completat."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Les pàgines el subrecurs de les quals té cache-control:no-cache no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Les pàgines el subrecurs de les quals té cache-control:no-store no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | timeout": {"message": "La pàgina ha excedit el temps màxim a la memòria cau endavant/enrere i ha caducat."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "La pàgina ha esgotat el temps d'espera en accedir a la memòria cau endavant/enrere (probablement a causa de gestors page-hide de llarga durada)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "La pàgina té un gestor d'unload al marc principal."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "La pàgina té un gestor d'unload en un submarc."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "El navegador ha canviat la capçalera de substitució de l'agent d'usuari."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Actualment, les pàgines que han donat accés per gravar vídeo o àudio no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Actualment, les pàgines que utilitzen WebDatabase no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Actualment, les pàgines que utilitzen WebHID no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Actualment, les pàgines que utilitzen WebLocks no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Actualment, les pàgines que utilitzen WebNfc no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Actualment, les pàgines que utilitzen WebOTPService no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Les pàgines amb WebRTC no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Actualment, les pàgines que utilitzen WebShare no són aptes per a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Les pàgines amb WebSocket no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Les pàgines amb WebTransport no poden accedir a la memòria cau endavant/enrere."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Actualment, les pàgines que utilitzen WebXR no són aptes per a la memòria cau endavant/enrere."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Pots optar per afegir esquemes d'URL https: o http: (els navegadors que admeten \"strict-dynamic\" els ignoren) perquè sigui retrocompatible amb navegadors més antics."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "La directiva \"disown-opener\" està obsoleta des de la CSP3. Com a alternativa, utilitza la capçalera Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "La directiva \"referrer\" està obsoleta des de la CSP2. Com a alternativa, utilitza la capçalera Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "La directiva \"reflected-xss\" està obsoleta des de la CSP2. Com a alternativa, utilitza la capçalera X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Si no hi ha una directiva \"base-uri\", es poden injectar etiquetes <base> per establir l'URL de base de tots els URL relatius (per exemple, scripts) en un domini controlat per un atacant. Pots optar per establir \"base-uri\" en \"none\" o \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Si falta \"object-src\", es podran injectar connectors que executin scripts no segurs. Mira si pots establir \"object-src\" en \"none\"."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Falta la directiva \"script-src\". Això pot permetre l'execució de scripts no segurs."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Se t'ha oblidat el punt i coma? Semb<PERSON> que \"{keyword}\" és una directiva i no una paraula clau."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Els nonces han d'utilitzar el codi de caràcters base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Els nonces han de tenir 8 caràcters com a mínim."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "<PERSON><PERSON>ta utilitzar esquemes d'URL senzills ({keyword}) en aquesta directiva. Els esquemes d'URL senzills permeten obtenir scripts d'un domini no segur."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "<PERSON><PERSON>ta utilitzar comodins senzills ({keyword}) en aquesta directiva. Els comodins senzills permeten obtenir scripts d'un domini no seguir."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "La destinació d'informes només es pot configurar mitjançant la directiva \"report-to\". Aquesta directiva només s'admet en navegadors basats en Chromium i, per tant, també es recomana utilitzar una directiva \"report-uri\"."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "No hi ha cap CSP que ofereixi una destinació d'informes. Això fa que sigui més difícil mantenir la CSP al llarg del temps i monitorar les vulnerabilitats."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Les llistes d'amfitrions permesos sovint es poden ometre. Pots utilitzar nonces o valors resum de la CSP en el seu lloc, a més de \"strict-dynamic\", si cal."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Directiva CSP desconeguda."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "\"{keyword}\" sembla una paraula clau no vàlida."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "La directiva \"unsafe-inline\" permet l'execució de scripts in-page i gestors d'esdeveniments no segurs. Pots utilitzar nonces o valors resum de la CSP per permetre els scripts individualment."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Pots optar per afegir \"unsafe-inline\" (els navegadors que admeten nonces o valors resum l'ignoren) perquè sigui retrocompatible amb navegadors més antics."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "El símbol del comodí (*) no cobrirà l'autorització a la gestió de la capçalera `Access-Control-Allow-Headers` a CORS."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Les sol·licituds de recursos amb URL que contenien tant caràcters d'espai en blanc (`(n|r|t)`) com caràcters d'\"inferior a\" (`<`) suprimits estan bloquejades. Suprimeix les línies noves i codifica els caràcters d'\"inferior a\" de llocs com els valors d'atribut d'element per carregar aquests recursos."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` està obsoleta. Substitueix-la per l'API estandarditzada Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "La funció `chrome.loadTimes()` està obsoleta. Substitueix-la per l'API estandarditzada: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` està obsoleta. Substitueix-la per l'API estandarditzada: `nextHopProtocol` a Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Les galetes que continguin un caràcter `(0|r|n)` es rebutjaran en lloc de truncar-se."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "El relaxament de la política del mateix origen establint `document.domain` està obsolet i es desactivarà de manera predeterminada. Aquest advertiment de discontinuació és per a un accés d'altres orígens activat establint `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "L'opció d'activar {PH1} des d'iframes d'altres orígens està obsoleta i se suprimirà en el futur."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "S'ha d'utilitzar l'atribut `disableRemotePlayback` per desactivar la integració de Cast predeterminada en lloc de fer servir el selector `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} està obsoleta. Substitueix-la per {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "<PERSON><PERSON><PERSON> és un exemple d'un missatge traduït referent a un problema de discontinuació."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "El relaxament de la política del mateix origen establint `document.domain` està obsolet i es desactivarà de manera predeterminada. Per continuar utilitzant aquesta funció, cancel·la els clústers d'agent amb clau d'origen enviant una capçalera `Origin-Agent-Cluster: ?0` juntament amb la resposta HTTP per al document i els marcs. Consulta https://developer.chrome.com/blog/immutable-document-domain/ per obtenir més informació."}, "core/lib/deprecations-strings.js | eventPath": {"message": "La funció `Event.path` està obsoleta i se suprimirà. Substitueix-la per `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "La capçalera `Expect-CT` està obsoleta i se suprimirà. Chrome requereix Transparència de certificats per a tots els certificats considerats de confiança públicament que s'hagin emès després del 30 d'abril de 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Consulta la pàgina d'estat de la funció per obtenir informació més detallada."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` i `watchPosition()` ja no funcionen en orígens no segurs. Per utilitzar aquesta funció, t'has de plantejar canviar l'aplicació a un origen segur, com ara HTTPS. Consulta https://goo.gle/chrome-insecure-origins per obtenir més informació."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` i `watchPosition()` estan obsoletes en orígens no segurs. Per utilitzar aquesta funció, t'has de plantejar canviar l'aplicació a un origen segur, com ara HTTPS. Consulta https://goo.gle/chrome-insecure-origins per obtenir més informació."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ja no funciona en orígens no segurs. Per utilitzar aquesta funció, t'has de plantejar canviar l'aplicació a un origen segur, com ara HTTPS. Consulta https://goo.gle/chrome-insecure-origins per obtenir més informació."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "La funció `RTCPeerConnectionIceErrorEvent.hostCandidate` està obsoleta. Substitueix-la per `RTCPeerConnectionIceErrorEvent.address` o per `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "L'origen del comerciant i les dades arbitràries de l'esdeveniment `canmakepayment` del Service Worker estan obsolets i se suprimiran: `topOrigin`, `paymentRequestOrigin`, `methodData` i `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "El lloc web ha sol·licitat un subrecurs des d'una xarxa a la qual només ha pogut accedir gràcies a la posició privilegiada a la xarxa dels seus usuaris. Aquestes sol·licituds exposen els dispositius i els servidors que no són públics a Internet, amb la qual cosa augmenta el risc de patir una fuita d'informació o un atac de falsificació de sol·licituds entre llocs web (CSRF). Per reduir aquests riscos, Chrome discontinua les sol·licituds a subrecursos que no són públics quan s'inicien des de contexts no segurs i comença a bloquejar-los."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "El CSS no es pot carregar des dels URL `file:`, tret que acabin amb l'extensió de fitxer `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "L'opció d'utilitzar `SourceBuffer.abort()` per anul·lar la supressió de l'interval asíncron duta a terme per `remove()` s'ha discontinuat a causa del canvi d'especificació. Es deixarà d'admetre en el futur. Hauràs d'escoltar l'esdeveniment `updateend`. `abort()` s'ha dissenyat només per anul·lar l'addició d'un mitjà asíncron o per restablir l'estat de l'analitzador."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "En configurar `MediaSource.duration` per sota de la marca de temps de presentació més alta de qualsevol marc amb codi emmagatzemat a la memòria intermèdia, està obsoleta a causa del canvi d'especificació. La supressió implícita de mitjans truncats emmagatzemats a la memòria intermèdia es deixarà d'admetre en el futur. En lloc seu, hauràs d'executar el codi `remove(newDuration, oldDuration)` explícit en totes les instàncies de `sourceBuffers` en què `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Aquest canvi es farà efectiu quan s'arribi a {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI demanarà permís d'ús fins i tot si el missatge exclusiu del sistema no s'especifica a `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Pot ser que l'API Notification ja no es pugui utilitzar des d'orígens no segurs. Planteja't canviar l'aplicació a un origen segur, com ara HTTPS. Consulta https://goo.gle/chrome-insecure-origins per obtenir més informació."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Pot ser que el permís per a l'API Notification ja no se sol·liciti des d'un iframe d'altres orígens. T'hauries de plantejar sol·licitar permís des d'un marc de nivell superior o bé obrir una finestra nova."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "El teu partner està negociant una versió de (D)TLS obsoleta. Demana al teu partner que ho corregeixi."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL està obsolet en contextos no segurs i se suprimirà aviat. Utilitza Web Storage o Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Si s'especifica `overflow: visible` a les etiquetes img, video i canvas, pot ser que produeixin contingut visual fora dels límits de l'element. Consulta https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "L'API `paymentManager.instruments` està obsoleta. Utilitza la instal·lació Just-In-Time per als gestors de pagaments."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "La crida a `PaymentRequest` ha omès la directiva `connect-src` de la política de seguretat del contingut (CSP). Aquesta omissió està obsoleta. Afegeix l'identificador de la forma de pagament de l'API `PaymentRequest` (al camp `supportedMethods`) a la directiva `connect-src` de la CSP."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "La funció `StorageType.persistent` està obsoleta. Substitueix-la per l'API `navigator.storage` estandarditzada."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "L'element `<source src>` amb un atribut superior `<picture>` no és vàlid i s'ignorarà. Substitueix-lo per `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "La funció `window.webkitStorageInfo` està obsoleta. Substitueix-la per l'API `navigator.storage` estandarditzada."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Les sol·licituds de subrecursos amb URL que contenen credencials inserides (per exemple, `**********************/`) estan bloque<PERSON>."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "La restricció `DtlsSrtpKeyAgreement` s'ha suprimit. Has especificat un valor `false` per a aquesta restricció, la qual cosa s'interpreta com un intent d'utilitzar el mètode `SDES key negotiation` suprimit. Aquesta funcionalitat s'ha suprimit; substitueix-la per un servei que admeti `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "La restricció `DtlsSrtpKeyAgreement` s'ha suprimit. Has especificat un valor `true` per a aquesta restricció, la qual cosa no ha tingut cap efecte, però pots suprimir aquesta restricció per organitzar-te millor."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "S'ha detectat `Complex Plan B SDP`. Aquest dialecte de `Session Description Protocol` ja no s'admet. Substitueix-lo per `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "El protocol `Plan B SDP semantics`, utilitzat per construir una interfície `RTCPeerConnection` amb el format `{sdpSemantics:plan-b}`, és una versió no estàndard heretada del protocol `Session Description Protocol` que s'ha suprimit permanentment de la plataforma web. Continuarà estant disponible en compilar amb `IS_FUCHSIA`, però tenim previst suprimir-lo al més aviat possible. Deixa de dependre'n. Per veure'n l'estat, consulta https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "L'opció `rtcpMuxPolicy` està obsoleta i se suprimirà."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` requerirà l'aïllament d'altres orígens. Consulta https://developer.chrome.com/blog/enabling-shared-array-buffer/ per obtenir més informació."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` sense l'activació de l'usuari està obsoleta i se suprimirà."}, "core/lib/deprecations-strings.js | title": {"message": "S'ha utilitzat una funció obsoleta"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Les extensions han d'activar l'aïllament d'altres orígens per continuar utilitzant `SharedArrayBuffer`. Consulta https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "La funció {PH1} és específica de cada proveïdor. Substitueix-la per la funció {PH2} estàndard."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "La resposta JSON no admet UTF-16 a `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "L'API `XMLHttpRequest` síncrona al fil principal està obsoleta a causa dels seus efectes perjudicials en l'experiència de l'usuari final. Per obtenir més ajuda, consulta https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "La funció `supportsSession()` està obsoleta. Substitueix-la per `isSessionSupported()` i comprova el valor booleà resolt."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Temps de bloqueig del fil principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL de la memòria cau"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elements amb errors"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Ubicació"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nom"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Per sobre del pressupost"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Sol·licituds"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Mida del recurs"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipus de recurs"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Mida"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Font"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Hora d'inici"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Temps invertit"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Mida de la transferència"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Possible estalvi"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Possible estalvi"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Possible estalvi de {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element trobat}other{# elements trobats}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Possible estalvi de {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Primera renderització significativa"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON> de ll<PERSON>ra"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Imatge"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interacció amb la renderització següent"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Alta"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Baixa"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Retard potencial màxim respecte a la primera interacció"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Multimèdia"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Altres"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Altres recursos"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Full d'estil"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Tercers"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Hi ha hagut un problema en gravar la traça sobre la càrrega de la pàgina. Torna a executar Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "S'ha esgotat el temps d'espera de la connexió inicial del protocol de depuració."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome no ha recollit cap captura de pantalla mentre es carregava la pàgina. Comprova que hi hagi contingut visible a la pàgina i, a continuació, prova d'executar Lighthouse de nou. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Els servidors DNS no han pogut resoldre el domini proporcionat."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "El recopilador del recurs {artifactName} requerit ha detectat un error: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "S'ha produït un error intern de Chrome. Reinicia el navegador i prova d'executar Lighthouse de nou."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "El recopilador obligatori {artifactName} no s'ha executat."}, "core/lib/lh-error.js | noFcp": {"message": "La pàgina no ha renderitzat contingut. Assegura't de mantenir la finestra del navegador en primer pla durant la càrrega i torna-ho a provar. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "La pàgina no mostrava contingut que es pogués qualificar de renderització de l'element més gran amb contingut (LCP). Assegura't que la pàgina tingui un element LCP vàlid i torna-ho a provar. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "La pàgina proporcionada no és HTML (s'ha publicat com a tipus MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Aquesta versió de Chrome és massa antiga per admetre \"{featureName}\". Utilitza una versió més recent per veure els resultats complets."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse no ha pogut carregar de manera fiable la pàgina que has sol·licitat. Assegura't que estiguis fent la prova de l'URL correcte i que el servidor estigui responent correctament a totes les sol·licituds."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Com que la pàgina ha deixat de respondre, Lighthouse no ha pogut carregar de manera fiable l'URL que has sol·licitat."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "L'URL que has proporcionat no té un certificat de seguretat vàlid. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome ha evitat la càrrega de la pàgina amb una pantalla intersticial. Assegura't que estiguis fent la prova de l'URL correcte i que el servidor estigui responent correctament a totes les sol·licituds."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse no ha pogut carregar de manera fiable la pàgina que has sol·licitat. Assegura't que estiguis fent la prova de l'URL correcte i que el servidor estigui responent correctament a totes les sol·licituds. (Detalls: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse no ha pogut carregar de manera fiable la pàgina que has sol·licitat. Assegura't que estiguis fent la prova de l'URL correcte i que el servidor estigui responent correctament a totes les sol·licituds. (Codi d'estat: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "La pàgina ha tardat massa temps a carregar-se. Segueix les opcions indicades a l'informe per reduir el temps de càrrega de la pàgina i, a continuació, prova d'executar Lighthouse de nou. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "S'ha superat el temps assignat per rebre una resposta del protocol de DevTools. (Mètode: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "S'ha superat el temps assignat per obtenir el contingut dels recursos"}, "core/lib/lh-error.js | urlInvalid": {"message": "Sembla que l'URL que has proporcionat no és vàlid."}, "core/lib/navigation-error.js | warningXhtml": {"message": "El tipus MIME de la pàgina és XHTML: Lighthouse no admet explícitament aquest tipus de document"}, "core/user-flow.js | defaultFlowName": {"message": "Flux d'usuaris ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Informe de navegació ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Informe d'una instantània ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Informe d'un període de temps ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "<PERSON><PERSON> els informes"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categories"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Accessibilitat"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Pràctiques recomanades"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Rendiment"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Aplicació web progressiva"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Escriptori"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Explicació de l'informe de fluxos de Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Explicació dels fluxos"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Utilitza els informes de navegació per..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "<PERSON><PERSON><PERSON><PERSON> els informes d'una instantània per..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "<PERSON><PERSON><PERSON><PERSON> els informes d'un període de temps per..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obtén una puntuació del rendiment de Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Mesura les mètriques de rendiment de la càrrega de pàgines, com ara la renderització de l'element més gran amb contingut i l'índex de velocitat."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Avalua les funcions d'una aplicació web progressiva."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Cerca problemes d'accessibilitat en aplicacions d'una sola pàgina o en formularis complexos."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Avalua les pràctiques recomanades dels menús i dels elements de la interfície d'usuari amagats darrere de la interacció."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Me<PERSON>ra els canvis de disseny i el temps d'execució de JavaScript en una sèrie d'interaccions."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Descobreix oportunitats de rendiment per millorar l'experiència en pàgines de llarga durada i en aplicacions d'una sola pàgina."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON> impacte"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} auditoria informativa}other{{numInformative} auditories informatives}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mòbil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Càrrega de la pàgina"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Els informes de navegació analitzen la càrrega d'una sola pàgina, exactament igual que els informes originals de Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Informe de navegació"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} informe de navegació}other{{numNavigation} informes de navegació}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} auditoria aprovable}other{{numPassableAudits} auditories aprovables}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} auditoria aprovada}other{{numPassed} auditories aprovades}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Normal"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Error"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Deficient"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Estat capturat de la pàgina"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Els informes d'una instantània analitzen la pàgina en un estat concret, normalment després de les interaccions dels usuaris."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Informe d'una instantània"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} informe d'una instantània}other{{numSnapshot} informes d'una instantània}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Resum"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interaccions dels usuaris"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Els informes d'un període de temps analitzen un període de temps arbitrari, que sol contenir interaccions dels usuaris."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Informe d'un període de temps"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} informe d'un període de temps}other{{numTimespan} informes d'un període de temps}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Informe del flux d'usuaris de Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "En el cas de contingut animat, fes servir [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) per minimitzar l'ús de la CPU quan el contingut és fora de la pantalla."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Pots mostrar tots els components [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) en formats WebP i, al mateix temps, especificar una alternativa adequada per a altres navegadors. [Obtén més informació](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Comprova que estiguis utilitzant [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) perquè les imatges es carreguin automàticament de manera lenta. [Obtén més informació](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Fes servir eines com ara [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) per [renderitzar dissenys d'AMP des del servidor](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consulta la [documentaci<PERSON> d'AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) per assegurar-te que s'admeten tots els estils."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "El component [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) admet l'atribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) per especificar quins recursos d'imatge s'utilitzaran en funció de la mida de la pantalla. [Obtén més informació](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Si s'estan renderitzant llistes molt llargues, pots optar per un desplaçament virtual amb el Component Dev Kit (CDK). [Obtén més informació](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Aplica la [divisió de codi a la ruta](https://web.dev/route-level-code-splitting-in-angular/) per minimitzar la mida dels paquets de JavaScript. També pots optar per capturar prèviament els recursos amb el [Service Worker d'Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Si utilitzes Angular CLI, comprova que les compilacions es generin en el mode de producció. [Obtén més informació](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Si utilitzes Angular CLI, inclou mapes d'origen a la compilació de producció per inspeccionar els teus paquets. [Obtén més informació](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Precarrega rutes amb antelació per accelerar la navegació. [Obtén més informació](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Pots optar per fer servir la utilitat `BreakpointObserver` del Component Dev Kit (CDK) per gestionar els punts de ruptura de la imatge. [Obtén més informació](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Pots penjar el GIF en un servei que permeti inserir-lo com un vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Especifica `@font-display` quan defineixis tipus de lletra personalitzats al teu tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Planteja't la possibilitat de configurar [formats d'imatge WebP amb un estil d'imatge Convert](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) al teu lloc web."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instal·la [un mòdul de Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) que pugui carregar les imatges de forma lenta. Aquests mòduls ofereixen la possibilitat d'ajornar les imatges fora de la pantalla per millorar el rendiment."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Pots utilitzar un mòdul per inserir fitxers JavaScript i CSS essencials o per carregar recursos de manera asíncrona mitjançant JavaScript, com el mòdul [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Tingues en compte que les optimitzacions que proporciona aquest mòdul poden afectar el teu lloc web, de manera que és probable que hagis de fer canvis al codi."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Els temes, els mòduls i les especificacions del servidor repercuteixen en el temps de resposta del servidor. Pots buscar un tema més optimitzat, seleccionar amb cura un mòdul d'optimització o actualitzar el servidor. Els servidors d'allotjament haurien d'utilitzar l'opció de desar a la memòria cau del codi d'operacions PHP, l'opció de desar a la memòria cau per reduir els temps de consulta de la base de dades, com ara Redis o Memcached, així com la lògica de l'aplicació optimitzada per preparar les pàgines més ràpidament."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Pots utilitzar [estils d'imatges responsives](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) per reduir la mida de les imatges que carregues a la pàgina. Si utilitzes Views per mostrar diversos elements de contingut en una pàgina, pots implementar paginació per limitar el nombre d'elements de contingut que es mostren en una pàgina determinada."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Comprova que hagis activat Aggregate CSS files (Fitxers CSS agregats) a la pàgina Administration > Configuration > Development (Administració > Configuraci<PERSON> > Desenvolupament). També pots configurar opcions d'agregació més avançades mitjançant [mòduls addicionals](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) per accelerar el teu lloc web. Per fer-ho, concatenen, redueixen i comprimeixen els estils de CSS."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Comprova que hagis activat Aggregate JavaScript files (Fitxers JavaScript agregats) a la pàgina Administration > Configuration > Development (Administració > Configuraci<PERSON> > Desenvolupament). També pots configurar opcions d'agregació més avançades mitjançant [mòduls addicionals](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) per accelerar el teu lloc web. Per fer-ho, concatenen, redueixen i comprimeixen els recursos de JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Pots suprimir les regles de CSS no utilitzades i adjuntar només les biblioteques de Drupal necessàries a la pàgina o al component rellevants d'una pàgina. Per obtenir-ne informació, segueix l'[enllaç a la documentació de Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Per identificar les biblioteques adjuntes que afegeixen fitxers CSS externs, prova d'executar la [cobertura de codi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) a Chrome DevTools. Pots identificar el tema o el mòdul responsable a partir de l'URL del full d'estil quan l'agregació de CSS estigui desactivada al teu lloc web de Drupal. Posa atenció als temes o els mòduls que tinguin molts fulls d'estil a la llista amb molt de vermell a la cobertura de codi. Un tema o mòdul només hauria de tenir un full d'estil a la cua si es fa servir a la pàgina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Pots suprimir els recursos de JavaScript no utilitzats i adjuntar només les biblioteques de Drupal necessàries a la pàgina o al component rellevants d'una pàgina. Per obtenir-ne informació, segueix l'[enllaç a la documentació de Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Per identificar les biblioteques adjuntes que afegeixen fitxers JavaScript externs, prova d'executar la [cobertura de codi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) a Chrome DevTools. Pots identificar el tema o el mòdul responsable a partir de l'URL de l'script quan l'agregació de JavaScript estigui desactivada al teu lloc web de Drupal. Posa atenció als temes o als mòduls que tinguin molts scripts a la llista amb molt de vermell a la cobertura de codi. Un tema o mòdul només hauria de tenir un script a la cua si es fa servir a la pàgina."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Defineix l'opció Browser and proxy cache maximum age (Antiguitat màxima de la memòria cau del navegador i del servidor intermediari) a la pàgina Administration > Configuration > Development (Administració > Configuraci<PERSON> > Desenvolupament). Obtén informació sobre [l'optimització per a un bon rendiment i la memòria cau de Drupal](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Pots utilitzar [un mòdul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) que optimitzi i redueixi automàticament la mida de les imatges penjades a través del lloc web sense perdre qualitat. A més, comprova que estiguis utilitzant els [estils d'imatges responsives](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) natius proporcionats per Drupal (disponibles a Drupal 8 i en versions posteriors) en totes les imatges renderitzades al lloc web."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Es poden afegir suggeriments de recursos amb connexió prèvia o recollida prèvia de DNS instal·lant i configurant [un mòdul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) que proporcioni elements per a les suggerències dels recursos d'agent d'usuari."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Comprova que estiguis utilitzant els [estils d'imatges responsives](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) natius proporcionats per Drupal (disponibles a Drupal 8 i en versions posteriors). Utilitza els estils d'imatges responsives per renderitzar camps d'imatge mitjançant modes de visualització, visualitzacions o l'editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "<PERSON><PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Optimize Fonts` per aprofitar automàticament la funció CSS `font-display` per assegurar-te que els usuaris poden veure el text mentre els tipus de lletra per a llocs web es carreguen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Next-Gen Formats` per convertir imatges a WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Utilitza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Lazy Load Images` per ajornar la càrrega d'imatges fora de pantalla fins que es necessitin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "U<PERSON>itza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Critical CSS` i `Script Delay` per ajornar la càrrega de JS i CSS que no siguin essencials."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Utilitza [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) per desar el teu contingut a la memòria cau mitjançant la nostra xarxa internacional a fi de millorar la mètrica Temps fins al primer byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Utilitza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Minify CSS` per reduir automàticament el teu CSS i, al seu torn, la mida de càrrega útil de la xarxa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Utilitza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Minify Javascript` per reduir automàticament el teu JS i, al seu torn, la mida de càrrega útil de la xarxa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Utilitza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Remove Unused CSS` perquè t'ajudi amb aquest problema. Identificarà les classes CSS que s'utilitzen realment en cada pàgina del teu lloc web i suprimirà les que no s'utilitzin per mantenir la mida reduïda del fitxer."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "U<PERSON>itza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Efficient Static Cache Policy` per establir els valors recomanats al títol de la memòria cau per als recursos estàtics."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Next-Gen Formats` per convertir imatges a WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "<PERSON><PERSON>itza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Pre-Connect Origins` per afegir automàticament suggeriments de recursos `preconnect` per establir connexions anticipades en orígens importants de tercers."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Utilitza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Preload Fonts` i `Preload Background Images` per afegir enllaços de `preload` per prioritzar l'obtenció de recursos que se sol·liciten més tard a la càrrega de la pàgina."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Utilitza [Ezoic Leap](https://pubdash.ezoic.com/speed) i activa `Resize Images` per canviar la mida de les imatges perquè s'adapti a cada dispositiu a fi de reduir les mides de càrrega útil de la xarxa."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Pots penjar el GIF en un servei que permeti inserir-lo com un vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Pots fer servir un [connector](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) o un servei que converteixi automàticament les imatges penjades als formats òptims."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instal·la un [connector de Joomla de càrrega lenta](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) que t'ofereixi la possibilitat d'ajornar les imatges fora de pantalla o bé canvia a una plantilla que t'ofereixi aquesta funcionalitat. A partir de Joomla 4.0, totes les imatges noves obtenen [automàticament](https://github.com/joomla/joomla-cms/pull/30748) l'atribut `loading` del nucli."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Hi ha diversos connectors de Joomla que et poden ajudar a [inserir recursos essencials](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) o a [ajornar els recursos menys importants](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Tingues en compte que les optimitzacions que proporcionen aquests connectors poden afectar les funcions de les plantilles o dels connectors, de manera que hi hauràs de fer proves minucioses."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Les plantilles, les extensions i les especificacions del servidor repercuteixen en el temps de resposta del servidor. Pots buscar una plantilla més optimitzada, seleccionar amb cura una extensió d'optimització o actualitzar el servidor."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Pots mostrar extractes a les categories d'articles (per exemple, amb l'enllaç de més informació), reduir el nombre d'articles que es mostren en una pàgina concreta, tallar les publicacions llargues en diverses pàgines o fer servir un connector per als comentaris de càrrega lenta."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Hi ha diverses [extensions de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que poden accelerar el teu lloc web. Per fer-ho, concatenen, redueixen i comprimeixen els estils. També hi ha plantilles que ofereixen aquesta funcionalitat."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Hi ha diverses [extensions de Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que poden accelerar el teu lloc web. Per fer-ho, concatenen, redueixen i comprimeixen els scripts. També hi ha plantilles que ofereixen aquesta funcionalitat."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Pots reduir o canviar el nombre d'[extensions de Joomla](https://extensions.joomla.org/) que carreguen fitxers CSS no utilitzats a la pàgina. Per identificar les extensions que afegeixen fitxers CSS externs, prova d'executar la [cobertura de codi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) a Chrome DevTools. Pots identificar el tema o el connector responsable a partir de l'URL del full d'estil. Posa atenció als connectors que tinguin molts fulls d'estil a la llista amb molt de vermell a la cobertura de codi. Un connector només hauria de tenir un full d'estil a la cua si es fa servir a la pàgina."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Pots reduir o canviar el nombre d'[extensions de Joomla](https://extensions.joomla.org/) que carreguen fitxers JavaScript no utilitzats a la pàgina. Per identificar els connectors que afegeixen fitxers JavaScript externs, prova d'executar la [cobertura de codi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) a Chrome DevTools. Pots identificar l'extensió responsable a partir de l'URL de l'script. Posa atenció a les extensions que tinguin molts scripts a la llista amb molt de vermell a la cobertura de codi. Una extensió només hauria de tenir un script a la cua si es fa servir a la pàgina."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Obtén informació sobre la [memòria cau del navegador a Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Pots utilitzar un [connector d'optimització d'imatges](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) per comprimir les imatges sense perdre qualitat."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Pots utilitzar un [connector d'imatges responsives](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) per fer servir aquest tipus d'imatges al teu contingut."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Pots activar la compressió de text activant la compressió de pàgines amb Gzip a Joomla (System > Global configuration > Server [Sistema > Configuració global > Servidor])."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Si no estàs agrupant els recursos de JavaScript, pots optar per utilitzar un [empaquetador](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Desactiva la [minimització i l'agrupació de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrades de Magento i planteja't la possibilitat d'utilitzar l'[empaquetador](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Especifica `@font-display` en [definir tipus de lletra personalitzats](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Pots optar per cercar diverses extensions de tercers a [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) per utilitzar formats d'imatge més nous."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Pots optar per modificar les plantilles de producte i de catàleg perquè utilitzin la funció de [càrrega diferida](https://web.dev/native-lazy-loading) de la plataforma web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Fes servir la [integració amb Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Activa l'opció \"Redueix els fitxers CSS\" a la configuració del desenvolupador de la botiga. [Obtén més informació](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Fes servir [Terser](https://www.npmjs.com/package/terser) per reduir tots els recursos de JavaScript provinents de la implementació de contingut estàtic i desactiva la funció de minimització integrada."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Desactiva l'[agrupació de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrada de Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Pots optar per cercar diverses extensions de tercers a [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) per optimitzar les imatges."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Es poden afegir suggeriments de recursos amb connexió prèvia o recollida prèvia de DNS [modificant el disseny d'un tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Es poden afegir etiquetes `<link rel=preload>` [modificant el disseny dels temes](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Utilitza el component `next/image` (i no `<img>`) perquè el format d'imatge s'optimitzi automàticament. [Obtén més informació](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Utilitza el component `next/image` (i no `<img>`) perquè les imatges es carreguin lentament de manera automàtica. [Obtén més informació](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Utilitza el component `next/image` i estableix \"priority\" en \"True\" per precarregar la imatge LCP. [Obtén més informació](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Utilitza el component `next/script` per ajornar la càrrega de scripts de tercers que no siguin essencials. [Obtén més informació](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Utilitza el component `next/image` per assegurar-te que les imatges tinguin sempre la mida adequada. [Obtén més informació](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Pots establir `PurgeCSS` a la configuració de `Next.js` per suprimir regles sense utilitzar dels fulls d'estil. [Obtén més informació](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Utilitza `Webpack Bundle Analyzer` per detectar codi JavaScript sense utilitzar. [Més informació](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON>ts utilitzar `Next.js Analytics` per mesurar el rendiment de la teva aplicació al món real. [Obtén més informació](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configura l'emmagatzematge a la memòria cau per a recursos invariables i pàgines `Server-side Rendered` (SSR). [Obtén més informació](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Utilitza el component `next/image` (i no `<img>`) per ajustar la qualitat d'imatge. [Obtén més informació](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Utilitza el component `next/image` per establir les `sizes` adequades. [Obtén més informació](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Activa la compressió al teu servidor de Next.js. [Obtén més informació](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Utilitza el component `nuxt/image` i estableix `format=\"webp\"`. [Obtén més informació](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Utilitza el component `nuxt/image` i estableix `loading=\"lazy\"` per a imatges fora de la pantalla. [Obtén més informació](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Utilitza el component `nuxt/image` i especifica `preload` per a la imatge LCP. [Obtén més informació](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Utilitza el component `nuxt/image` i especifica `width` i `height` de manera explícita. [Obtén més informació](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Utilitza el component `nuxt/image` i estableix la `quality` adequada. [Obtén més informació](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Utilitza el component `nuxt/image` i estableix les `sizes` adequades. [Obtén més informació](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Suprimeix els GIF animats amb vídeo](https://web.dev/replace-gifs-with-videos/) per carregar les pàgines web més ràpidament i planteja't l'opció d'utilitzar formats de fitxer moderns, com ara [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) o [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), per millorar l'eficàcia de compressió en més d'un 30% respecte al còdec de vídeo actual més avançat, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Pots fer servir un [connector](https://octobercms.com/plugins?search=image) o un servei que converteixi automàticament les imatges penjades als formats òptims. [Les imatges WebP sense pèrdua](https://developers.google.com/speed/webp) són un 26% més petites en comparació amb les imatges PNG i entre un 25 i un 34% més petites que les imatges JPEG comparables a l'índex de qualitat SSIM equivalent. Un altre format d'imatge d'última generació que cal tenir en compte és [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Pots instal·lar un [connector de càrrega lenta d'imatges](https://octobercms.com/plugins?search=lazy) que t'ofereixi la possibilitat d'ajornar les imatges fora de pantalla o bé canviar a un tema que t'ofereixi aquesta funcionalitat. També pots fer servir [el connector AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Hi ha molts connectors que ajuden a [inserir recursos essencials](https://octobercms.com/plugins?search=css). Aquests connectors, però, poden malmetre altres connectors; et recomanem que els provis a fons."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Els temes, els connectors i les especificacions del servidor contribueixen al temps de resposta del servidor. Pots buscar un tema més optimitzat, seleccionar amb cura un connector d'optimització o actualitzar el servidor. El CMS d'October també permet als desenvolupadors fer servir [`Queues`](https://octobercms.com/docs/services/queues) per ajornar el processament d'una tasca llarga, com ara enviar un correu electrònic. Això accelera enormement les sol·licituds web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Pots mostrar extractes a les llistes de publicacions (per exemple, utilitzar un botó `show more`), reduir el nombre de publicacions que es mostren en una pàgina web determinada, dividir les publicacions llargues en diverses pàgines web o utilitzar un connector per carregar lentament els comentaris."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Hi ha molts [connectors](https://octobercms.com/plugins?search=css) que poden accelerar un lloc web mitjançant la concatenació, minimització i compressió dels estils. Utilitzar un procés de compilació per dur a terme aquesta minimització de manera anticipada pot accelerar el desenvolupament."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Hi ha molts [connectors](https://octobercms.com/plugins?search=javascript) que poden accelerar un lloc web mitjançant la concatenació, minimització i compressió dels scripts. Utilitzar un procés de compilació per dur a terme aquesta minimització de manera anticipada pot accelerar el desenvolupament."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Pots revisar els [connectors](https://octobercms.com/plugins) que carreguen fitxers CSS que no s'utilitzen al lloc web. Per identificar aquest tipus de connectors, executa la [cobertura del codi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) a Chrome DevTools. Identifica el tema o connector responsable de l'URL del full d'estil. Cerca connectors que tinguin molts fulls d'estil i molt de vermell a la cobertura del codi. Un connector només hauria d'afegir un full d'estil si aquest full d'estil s'utilitza a la pàgina web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Pots revisar els [connectors](https://octobercms.com/plugins?search=javascript) que carreguen fitxers JavaScript que no s'utilitzen a la pàgina web. Per identificar aquest tipus de connectors, executa la [cobertura del codi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) a Chrome DevTools. Identifica el tema o connector responsable de l'URL de l'script. Cerca connectors que tinguin molts scripts i molt de vermell a la cobertura del codi. Un connector només hauria d'afegir un script si aquest script s'utilitza a la pàgina web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Informa't sobre com pots [evitar sol·licituds de xarxa innecessàries amb la memòria cau HTTP](https://web.dev/http-cache/#caching-checklist). Hi ha molts [connectors](https://octobercms.com/plugins?search=Caching) que es poden utilitzar per accelerar l'emmagatzematge a la memòria cau."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Pots utilitzar un [connector d'optimització d'imatges](https://octobercms.com/plugins?search=image) per comprimir les imatges sense perdre qualitat."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Penja imatges directament al gestor multimèdia per assegurar-te que estan disponibles les mides d'imatge necessàries. Pots utilitzar el [filtre per canviar de mida](https://octobercms.com/docs/markup/filter-resize) o un [connector per canviar la mida de la imatge](https://octobercms.com/plugins?search=image) per assegurar-te que s'utilitzen les mides d'imatge òptimes."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Activa la compressió de text a la configuració del servidor web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Pots utilitzar una biblioteca d'enfinestrament, com ara `react-window`, per minimitzar el nombre de nodes DOM que es crea en renderitzar molts elements repetits a la pàgina. [Obtén més informació](https://web.dev/virtualize-long-lists-react-window/). Si estàs utilitzant el hook `Effect` per millorar el rendiment en temps d'execució, també pots [ometre els efectes](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) només fins que certes dependències canviïn i minimitzar les renderitzacions repetides innecessàries utilitzant [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) o [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo)."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Si estàs utilitzant un encaminador de React, redueix l'ús del component `<Redirect>` a les [navegacions per rutes](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Si estàs renderitzant qualsevol component de React des del servidor, pots optar per utilitzar `renderToPipeableStream()` o `renderToStaticNodeStream()` per permetre que el client rebi i hidrati diferents parts de l'etiquetatge en lloc de fer-ho amb tots a la vegada. [Obtén més informació](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Si el sistema de compilació redueix automàticament els fitxers CSS, comprova que estiguis implementant la compilació de producció de l'aplicació. Pots comprovar-ho amb l'extensió React Developer Tools. [Obtén més informació](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Si el sistema de compilació redueix automàticament els fitxers JavaScript, comprova que estiguis implementant la compilació de producció de l'aplicació. Pots comprovar-ho amb l'extensió React Developer Tools. [Obtén més informació](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Si no estàs renderitzant des del servidor, [divideix els paquets de JavaScript](https://web.dev/code-splitting-suspense/) amb `React.lazy()`. En cas contrari, divideix el codi fent servir una biblioteca de tercers, com ara [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Fes servir l'analitzador de rendiment de DevTools per a React, que utilitza l'API de l'analitzador de rendiment per mesurar el resultat de la renderització dels components. [Obtén més informació](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Pots penjar el GIF en un servei que permeti inserir-lo com un vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Planteja't utilitzar el connector [Performance Lab](https://wordpress.org/plugins/performance-lab/) per convertir automàticament les imatges JPEG que has penjat en WebP, sempre que s'admeti."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instal·la un [connector de WordPress de càrrega diferida](https://wordpress.org/plugins/search/lazy+load/) que t'ofereixi la possibilitat d'ajornar les imatges fora de pantalla o canviar a un tema que t'ofereixi aquesta funcionalitat. També pots fer servir [el connector AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Hi ha diversos connectors de WordPress que et poden ajudar a [inserir recursos essencials](https://wordpress.org/plugins/search/critical+css/) o a [ajornar els recursos menys importants](https://wordpress.org/plugins/search/defer+css+javascript/). Tingues en compte que les optimitzacions que proporcionen aquests connectors poden afectar les funcions del tema o dels connectors, de manera que és possible que hagis de fer canvis al codi."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Els temes, els connectors i les especificacions del servidor repercuteixen en el temps de resposta del servidor. Pots buscar un tema més optimitzat, seleccionar amb cura un connector d'optimització o actualitzar el servidor."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Pots mostrar extractes a les llistes de publicacions (per exemple, amb l'etiqueta més), reduir el nombre de publicacions que es mostren en una pàgina concreta, tallar les publicacions llargues en diverses pàgines o fer servir un connector per als comentaris de càrrega diferida."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Hi ha diversos [connectors de WordPress](https://wordpress.org/plugins/search/minify+css/) que poden accelerar el teu lloc web. Per fer-ho, concatenen, redueixen i comprimeixen els estils. També et recomanem que utilitzis un procés de compilació per fer aquesta minimització de manera anticipada, si és possible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Hi ha diversos [connectors de WordPress](https://wordpress.org/plugins/search/minify+javascript/) que poden accelerar el teu lloc web. Per fer-ho, concatenen, redueixen i comprimeixen els scripts. També et recomanem que utilitzis un procés de compilació per fer aquesta minimització de manera anticipada, si és possible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Pots reduir o canviar el nombre de [connectors de WordPress](https://wordpress.org/plugins/) que carreguen fitxers CSS no utilitzats a la pàgina. Per identificar els connectors que afegeixen fitxers CSS externs, prova d'executar la [cobertura de codi](https://developer.chrome.com/docs/devtools/coverage/) a Chrome DevTools. Pots identificar el tema o el connector responsable a partir de l'URL del full d'estil. Posa atenció als connectors que tinguin molts fulls d'estil a la llista amb molt de vermell a la cobertura de codi. Un connector només hauria de tenir un full d'estil a la cua si es fa servir a la pàgina."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Pots reduir o canviar el nombre de [connectors de WordPress](https://wordpress.org/plugins/) que carreguen fitxers JavaScript no utilitzats a la pàgina. Per identificar els connectors que afegeixen fitxers JavaScript externs, prova d'executar la [cobertura de codi](https://developer.chrome.com/docs/devtools/coverage/) a Chrome DevTools. Pots identificar el tema o el connector responsable a partir de l'URL de l'script. Posa atenció als connectors que tinguin molts scripts a la llista amb molt de vermell a la cobertura de codi. Un connector només hauria de tenir un script a la cua si es fa servir a la pàgina."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Obtén informació sobre la [memòria cau del navegador a WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Pots utilitzar un [connector de WordPress d'optimització d'imatges](https://wordpress.org/plugins/search/optimize+images/) per comprimir les imatges sense perdre qualitat."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Penja imatges directament mitjançant la [biblioteca multimèdia](https://wordpress.org/support/article/media-library-screen/) per garantir que les mides de la imatge necessàries estiguin disponibles i, a continuació, insereix-les des de la biblioteca multimèdia o fes servir el widget per garantir que es fan servir les mides de la imatge òptimes (incloses les dels punts de ruptura responsius). Evita utilitzar imatges de `Full Size`, tret que les dimensions siguin les adequades per a l'ús que se'n farà. [Obtén més informació](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Pots activar la compressió de text a la configuració del servidor web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Activa Imagify a la pestanya Optimització d'imatges de WP Rocket per convertir les teves imatges a WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Activa [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) a WP Rocket per resoldre aquesta recomanació. Aquesta funció retarda la càrrega de les imatges fins que el visitant es desplaça cap avall per la pàgina i cal que les vegi."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Activa la funció [Remove Unused CSS (\"Suprimeix els CSS no utilitzats\")](https://docs.wp-rocket.me/article/1529-remove-unused-css) i [Load JavaScript deferred (\"Carrega JavaScript en diferit\")](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) a WP Rocket per resoldre aquesta recomanació. Aquestes funcions optimitzaran respectivament els fitxers CSS i JavaScript perquè no bloquegin la renderització de la pàgina."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Activa [Redueix els fitxers CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) a WP Rocket per solucionar aquest problema. Se suprimiran els espais i els comentaris dels fitxers CSS del lloc web per reduir la mida del fitxer i accelerar-ne la baixada."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Activa [Minify JavaScript files (\"Redueix els fitxers JavaScript\")](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) a WP Rocket per solucionar aquest problema. Els espais i els comentaris buits se suprimiran dels fitxers JavaScript per reduir-ne la mida i accelerar-ne la baixada."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Activa la funció [Remove Unused CSS (\"Suprimeix els CSS no utilitzats\")](https://docs.wp-rocket.me/article/1529-remove-unused-css) a WP Rocket per corregir aquest problema. Redueix la mida de la pàgina suprimint tots els CSS i els fulls d'estil que no s'utilitzen i conserva només els CSS que s'utilitzin per a cada pàgina."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Activa la funció [Delay JavaScript execution (\"Retarda l'execució de JavaScript\")](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) a WP Rocket per solucionar aquest problema. Això millorarà la càrrega de la pàgina retardant l'execució dels scripts fins que l'usuari hi interactuï. Si el teu lloc web té iframes, també pots utilitzar [LazyLoad per a iframes i vídeos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) de WP Rocket i [substituir l'iframe de YouTube per una imatge de previsualització](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Activa Imagify a la pestanya Optimització d'imatges de WP Rocket i executa Optimització massiva per comprimir imatges."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Utilitza la funció [Prefetch DNS Requests (\"Recull prèviament sol·licituds de DNS\")](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) a WP Rocket per afegir \"dns-prefetch\" i accelerar la connexió amb dominis externs. A més, WP Rocket afegeix automàticament \"preconnect\" al [domini de Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) i qualsevol CNAME que s'hagi afegit mitjançant la funció [Enable CDN (\"Activa la CDN\")](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Per corregir aquest problema per a les fonts, activa [Remove Unused CSS (\"Suprimeix els CSS no utilitzats\")](https://docs.wp-rocket.me/article/1529-remove-unused-css) a WP Rocket. Les fonts més importants del lloc web es precarregaran amb prioritat."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Consulta la calculadora."}, "report/renderer/report-utils.js | collapseView": {"message": "Replega la visualització"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navegació inicial"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latència de camí crítica màxima:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copia l'objecte JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Commuta el tema fosc"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Imprimeix desplegat"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Re<PERSON><PERSON>'<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Desa com a Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Desa com a HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Desa com a JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Obre al visualitzador"}, "report/renderer/report-utils.js | errorLabel": {"message": "Error"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Error de l'informe: no hi ha informació d'auditoria"}, "report/renderer/report-utils.js | expandView": {"message": "Desplega la visualització"}, "report/renderer/report-utils.js | footerIssue": {"message": "Informa d'un problema"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Dades de laboratori"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Anà<PERSON>i amb [Lighthouse](https://developers.google.com/web/tools/lighthouse/) de la pàgina actual mitjançant una xarxa mòbil emulada. Els valors són estimacions i poden variar."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Elements addicionals per comprovar manualment"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "No aplicable"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Oportunitat"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Estalvi estimat"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Auditories aprovades"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Càrrega de la pàgina inicial"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitació <PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Escriptori emulat"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Sense emulació"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Vers<PERSON><PERSON> d'axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Potència de la CPU/memòria no limitada"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitació de CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispositiu"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitació de xarxa"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulació de la pantalla"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Agent d'usuari (xarxa)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Càrrega de la pàgina única"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Aquestes dades s'han extret d'una única càrrega de la pàgina, a diferència de les dades de camps que resumeixen moltes sessions."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitació 4G lenta"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Desconegut"}, "report/renderer/report-utils.js | show": {"message": "Mostra"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Mostra auditories relacionades amb:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Replega el fragment"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Desplega el fragment"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Mostra els recursos de tercers"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Proporcionada per l'entorn"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Hi ha hagut problemes que afecten aquesta execució de Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Els valors són estimacions i poden variar. El [resultat del rendiment es calcula](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directament a partir d'aquestes mètriques."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Mostra la traça original"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Mostra la traça"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Mostra un mapa d'arbre"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Auditories aprovades però amb advertiments"}, "report/renderer/report-utils.js | warningHeader": {"message": "Advertiments: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON><PERSON> els scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Cobertura"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON>ò<PERSON><PERSON> duplicats"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bytes del recurs"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nom"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Commuta la taula"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Bytes no utilitzats"}}