{"ast": null, "code": "/** This is a placeholder, not real render in dom */\nvar TreeNode = function TreeNode() {\n  return null;\n};\nexport default TreeNode;", "map": {"version": 3, "names": ["TreeNode"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree-select/es/TreeNode.js"], "sourcesContent": ["/** This is a placeholder, not real render in dom */\nvar TreeNode = function TreeNode() {\n  return null;\n};\n\nexport default TreeNode;"], "mappings": "AAAA;AACA,IAAIA,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAO,IAAI;AACb,CAAC;AAED,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}