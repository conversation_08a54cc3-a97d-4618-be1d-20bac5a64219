/**
 * Script di test per il sistema di monitoraggio del backend
 * 
 * Questo script testa il comportamento del BackendTestButton
 * in diverse condizioni di connettività
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🧪 Test del Sistema di Monitoraggio Backend\n');

// Funzione per modificare temporaneamente il file .env
function setDebugMode(enabled) {
    const envPath = '.env';
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    if (envContent.includes('REACT_APP_DEBUG=')) {
        envContent = envContent.replace(
            /REACT_APP_DEBUG=.*/,
            `REACT_APP_DEBUG=${enabled}`
        );
    } else {
        envContent += `\nREACT_APP_DEBUG=${enabled}\n`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log(`✅ Modalità debug ${enabled ? 'attivata' : 'disattivata'}`);
}

// Funzione per verificare se il backend è in esecuzione
function checkBackend() {
    try {
        const response = execSync('curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/health', 
            { encoding: 'utf8', timeout: 5000 });
        return response.trim() === '200';
    } catch (error) {
        return false;
    }
}

// Test 1: Verifica stato attuale
console.log('📋 Test 1: Verifica stato attuale del backend');
const backendOnline = checkBackend();
console.log(`Backend status: ${backendOnline ? '🟢 Online' : '🔴 Offline'}`);

if (backendOnline) {
    console.log('✅ Il pulsante di test dovrebbe essere NASCOSTO');
    console.log('✅ I log dovrebbero essere MINIMI in console');
} else {
    console.log('🔴 Il pulsante di test dovrebbe essere VISIBILE con animazione');
    console.log('⚠️ Dovrebbero esserci warning in console');
}

console.log('\n📋 Test 2: Modalità Debug');

// Test 2: Modalità debug ON
console.log('🔧 Attivazione modalità debug...');
setDebugMode(true);
console.log('✅ Modalità debug attivata');
console.log('📝 Ora dovresti vedere log dettagliati in console del browser');
console.log('🧪 Le notifiche toast dovrebbero essere visibili');

setTimeout(() => {
    // Test 3: Modalità debug OFF
    console.log('\n📋 Test 3: Modalità produzione');
    console.log('🔧 Disattivazione modalità debug...');
    setDebugMode(false);
    console.log('✅ Modalità debug disattivata');
    console.log('🔇 I log dovrebbero essere ridotti al minimo');
    console.log('🚫 Le notifiche toast non dovrebbero apparire');
    
    console.log('\n📋 Test completati!');
    console.log('\n🎯 Risultati attesi:');
    console.log('1. Pulsante nascosto quando backend online');
    console.log('2. Pulsante visibile e animato quando backend offline');
    console.log('3. Log dettagliati solo in modalità debug');
    console.log('4. Monitoraggio automatico ogni 30 secondi');
    
    console.log('\n🔍 Per verificare:');
    console.log('1. Apri http://localhost:3000');
    console.log('2. Apri Developer Tools (F12)');
    console.log('3. Guarda la console per i log');
    console.log('4. Controlla la presenza/assenza del pulsante in basso a destra');
    
    console.log('\n💡 Per testare il comportamento offline:');
    console.log('1. Ferma il backend (se in esecuzione)');
    console.log('2. Ricarica la pagina');
    console.log('3. Osserva il pulsante che appare con animazione');
    
}, 2000);

// Informazioni aggiuntive
console.log('\n📚 Documentazione completa: BACKEND_MONITORING_IMPROVEMENTS.md');
console.log('🔧 Configurazione: .env file');
console.log('🎨 Stili: src/App.css (animazione pulse)');
console.log('⚙️ Logica: src/components/BackendTestButton.jsx');
