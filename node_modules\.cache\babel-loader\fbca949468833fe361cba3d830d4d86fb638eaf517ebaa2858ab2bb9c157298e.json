{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useMemo, useRef } from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nimport ScrollNumber from './ScrollNumber';\nimport Ribbon from './Ribbon';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nimport { isPresetColor } from './utils';\nvar Badge = function Badge(_a) {\n  var _classNames, _classNames2;\n  var customizePrefixCls = _a.prefixCls,\n    customizeScrollNumberPrefixCls = _a.scrollNumberPrefixCls,\n    children = _a.children,\n    status = _a.status,\n    text = _a.text,\n    color = _a.color,\n    _a$count = _a.count,\n    count = _a$count === void 0 ? null : _a$count,\n    _a$overflowCount = _a.overflowCount,\n    overflowCount = _a$overflowCount === void 0 ? 99 : _a$overflowCount,\n    _a$dot = _a.dot,\n    dot = _a$dot === void 0 ? false : _a$dot,\n    _a$size = _a.size,\n    size = _a$size === void 0 ? 'default' : _a$size,\n    title = _a.title,\n    offset = _a.offset,\n    style = _a.style,\n    className = _a.className,\n    _a$showZero = _a.showZero,\n    showZero = _a$showZero === void 0 ? false : _a$showZero,\n    restProps = __rest(_a, [\"prefixCls\", \"scrollNumberPrefixCls\", \"children\", \"status\", \"text\", \"color\", \"count\", \"overflowCount\", \"dot\", \"size\", \"title\", \"offset\", \"style\", \"className\", \"showZero\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('badge', customizePrefixCls); // ================================ Misc ================================\n\n  var numberedDisplayCount = count > overflowCount ? \"\".concat(overflowCount, \"+\") : count;\n  var hasStatus = status !== null && status !== undefined || color !== null && color !== undefined;\n  var isZero = numberedDisplayCount === '0' || numberedDisplayCount === 0;\n  var showAsDot = dot && !isZero;\n  var mergedCount = showAsDot ? '' : numberedDisplayCount;\n  var isHidden = useMemo(function () {\n    var isEmpty = mergedCount === null || mergedCount === undefined || mergedCount === '';\n    return (isEmpty || isZero && !showZero) && !showAsDot;\n  }, [mergedCount, isZero, showZero, showAsDot]); // Count should be cache in case hidden change it\n\n  var countRef = useRef(count);\n  if (!isHidden) {\n    countRef.current = count;\n  }\n  var livingCount = countRef.current; // We need cache count since remove motion should not change count display\n\n  var displayCountRef = useRef(mergedCount);\n  if (!isHidden) {\n    displayCountRef.current = mergedCount;\n  }\n  var displayCount = displayCountRef.current; // We will cache the dot status to avoid shaking on leaved motion\n\n  var isDotRef = useRef(showAsDot);\n  if (!isHidden) {\n    isDotRef.current = showAsDot;\n  } // =============================== Styles ===============================\n\n  var mergedStyle = useMemo(function () {\n    if (!offset) {\n      return _extends({}, style);\n    }\n    var offsetStyle = {\n      marginTop: offset[1]\n    };\n    if (direction === 'rtl') {\n      offsetStyle.left = parseInt(offset[0], 10);\n    } else {\n      offsetStyle.right = -parseInt(offset[0], 10);\n    }\n    return _extends(_extends({}, offsetStyle), style);\n  }, [direction, offset, style]); // =============================== Render ===============================\n  // >>> Title\n\n  var titleNode = title !== null && title !== void 0 ? title : typeof livingCount === 'string' || typeof livingCount === 'number' ? livingCount : undefined; // >>> Status Text\n\n  var statusTextNode = isHidden || !text ? null : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-status-text\")\n  }, text); // >>> Display Component\n\n  var displayNode = !livingCount || _typeof(livingCount) !== 'object' ? undefined : cloneElement(livingCount, function (oriProps) {\n    return {\n      style: _extends(_extends({}, mergedStyle), oriProps.style)\n    };\n  }); // Shared styles\n\n  var statusCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-dot\"), hasStatus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames));\n  var statusStyle = {};\n  if (color && !isPresetColor(color)) {\n    statusStyle.background = color;\n  }\n  var badgeClassName = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-status\"), hasStatus), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-not-a-wrapper\"), !children), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2), className); // <Badge status=\"success\" />\n\n  if (!children && hasStatus) {\n    var statusTextColor = mergedStyle.color;\n    return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n      className: badgeClassName,\n      style: mergedStyle\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: statusCls,\n      style: statusStyle\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        color: statusTextColor\n      },\n      className: \"\".concat(prefixCls, \"-status-text\")\n    }, text));\n  } // <Badge status=\"success\" count={<Icon type=\"xxx\" />}></Badge>\n\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n    className: badgeClassName\n  }), children, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !isHidden,\n    motionName: \"\".concat(prefixCls, \"-zoom\"),\n    motionAppear: false,\n    motionDeadline: 1000\n  }, function (_ref) {\n    var _classNames3;\n    var motionClassName = _ref.className;\n    var scrollNumberPrefixCls = getPrefixCls('scroll-number', customizeScrollNumberPrefixCls);\n    var isDot = isDotRef.current;\n    var scrollNumberCls = classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-dot\"), isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count\"), !isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count-sm\"), size === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-multiple-words\"), !isDot && displayCount && displayCount.toString().length > 1), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames3));\n    var scrollNumberStyle = _extends({}, mergedStyle);\n    if (color && !isPresetColor(color)) {\n      scrollNumberStyle = scrollNumberStyle || {};\n      scrollNumberStyle.background = color;\n    }\n    return /*#__PURE__*/React.createElement(ScrollNumber, {\n      prefixCls: scrollNumberPrefixCls,\n      show: !isHidden,\n      motionClassName: motionClassName,\n      className: scrollNumberCls,\n      count: displayCount,\n      title: titleNode,\n      style: scrollNumberStyle,\n      key: \"scrollNumber\"\n    }, displayNode);\n  }), statusTextNode);\n};\nBadge.Ribbon = Ribbon;\nexport default Badge;", "map": {"version": 3, "names": ["_defineProperty", "_typeof", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useMemo", "useRef", "CSSMotion", "classNames", "ScrollNumber", "Ribbon", "ConfigContext", "cloneElement", "isPresetColor", "Badge", "_a", "_classNames", "_classNames2", "customizePrefixCls", "prefixCls", "customizeScrollNumberPrefixCls", "scrollNumberPrefixCls", "children", "status", "text", "color", "_a$count", "count", "_a$overflowCount", "overflowCount", "_a$dot", "dot", "_a$size", "size", "title", "offset", "style", "className", "_a$showZero", "showZero", "restProps", "_React$useContext", "useContext", "getPrefixCls", "direction", "numberedDisplayCount", "concat", "hasStatus", "undefined", "isZero", "showAsDot", "mergedCount", "isHidden", "isEmpty", "countRef", "current", "livingCount", "displayCountRef", "displayCount", "isDotRef", "mergedStyle", "offsetStyle", "marginTop", "left", "parseInt", "right", "titleNode", "statusTextNode", "createElement", "displayNode", "oriProps", "statusCls", "statusStyle", "background", "badgeClassName", "statusTextColor", "visible", "motionName", "motionAppear", "motionDeadline", "_ref", "_classNames3", "motionClassName", "isDot", "scrollNumberCls", "toString", "scrollNumberStyle", "show", "key"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/badge/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport { useMemo, useRef } from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nimport ScrollNumber from './ScrollNumber';\nimport Ribbon from './Ribbon';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nimport { isPresetColor } from './utils';\n\nvar Badge = function Badge(_a) {\n  var _classNames, _classNames2;\n\n  var customizePrefixCls = _a.prefixCls,\n      customizeScrollNumberPrefixCls = _a.scrollNumberPrefixCls,\n      children = _a.children,\n      status = _a.status,\n      text = _a.text,\n      color = _a.color,\n      _a$count = _a.count,\n      count = _a$count === void 0 ? null : _a$count,\n      _a$overflowCount = _a.overflowCount,\n      overflowCount = _a$overflowCount === void 0 ? 99 : _a$overflowCount,\n      _a$dot = _a.dot,\n      dot = _a$dot === void 0 ? false : _a$dot,\n      _a$size = _a.size,\n      size = _a$size === void 0 ? 'default' : _a$size,\n      title = _a.title,\n      offset = _a.offset,\n      style = _a.style,\n      className = _a.className,\n      _a$showZero = _a.showZero,\n      showZero = _a$showZero === void 0 ? false : _a$showZero,\n      restProps = __rest(_a, [\"prefixCls\", \"scrollNumberPrefixCls\", \"children\", \"status\", \"text\", \"color\", \"count\", \"overflowCount\", \"dot\", \"size\", \"title\", \"offset\", \"style\", \"className\", \"showZero\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var prefixCls = getPrefixCls('badge', customizePrefixCls); // ================================ Misc ================================\n\n  var numberedDisplayCount = count > overflowCount ? \"\".concat(overflowCount, \"+\") : count;\n  var hasStatus = status !== null && status !== undefined || color !== null && color !== undefined;\n  var isZero = numberedDisplayCount === '0' || numberedDisplayCount === 0;\n  var showAsDot = dot && !isZero;\n  var mergedCount = showAsDot ? '' : numberedDisplayCount;\n  var isHidden = useMemo(function () {\n    var isEmpty = mergedCount === null || mergedCount === undefined || mergedCount === '';\n    return (isEmpty || isZero && !showZero) && !showAsDot;\n  }, [mergedCount, isZero, showZero, showAsDot]); // Count should be cache in case hidden change it\n\n  var countRef = useRef(count);\n\n  if (!isHidden) {\n    countRef.current = count;\n  }\n\n  var livingCount = countRef.current; // We need cache count since remove motion should not change count display\n\n  var displayCountRef = useRef(mergedCount);\n\n  if (!isHidden) {\n    displayCountRef.current = mergedCount;\n  }\n\n  var displayCount = displayCountRef.current; // We will cache the dot status to avoid shaking on leaved motion\n\n  var isDotRef = useRef(showAsDot);\n\n  if (!isHidden) {\n    isDotRef.current = showAsDot;\n  } // =============================== Styles ===============================\n\n\n  var mergedStyle = useMemo(function () {\n    if (!offset) {\n      return _extends({}, style);\n    }\n\n    var offsetStyle = {\n      marginTop: offset[1]\n    };\n\n    if (direction === 'rtl') {\n      offsetStyle.left = parseInt(offset[0], 10);\n    } else {\n      offsetStyle.right = -parseInt(offset[0], 10);\n    }\n\n    return _extends(_extends({}, offsetStyle), style);\n  }, [direction, offset, style]); // =============================== Render ===============================\n  // >>> Title\n\n  var titleNode = title !== null && title !== void 0 ? title : typeof livingCount === 'string' || typeof livingCount === 'number' ? livingCount : undefined; // >>> Status Text\n\n  var statusTextNode = isHidden || !text ? null : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-status-text\")\n  }, text); // >>> Display Component\n\n  var displayNode = !livingCount || _typeof(livingCount) !== 'object' ? undefined : cloneElement(livingCount, function (oriProps) {\n    return {\n      style: _extends(_extends({}, mergedStyle), oriProps.style)\n    };\n  }); // Shared styles\n\n  var statusCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-dot\"), hasStatus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames));\n  var statusStyle = {};\n\n  if (color && !isPresetColor(color)) {\n    statusStyle.background = color;\n  }\n\n  var badgeClassName = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-status\"), hasStatus), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-not-a-wrapper\"), !children), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2), className); // <Badge status=\"success\" />\n\n  if (!children && hasStatus) {\n    var statusTextColor = mergedStyle.color;\n    return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n      className: badgeClassName,\n      style: mergedStyle\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: statusCls,\n      style: statusStyle\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        color: statusTextColor\n      },\n      className: \"\".concat(prefixCls, \"-status-text\")\n    }, text));\n  } // <Badge status=\"success\" count={<Icon type=\"xxx\" />}></Badge>\n\n\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n    className: badgeClassName\n  }), children, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !isHidden,\n    motionName: \"\".concat(prefixCls, \"-zoom\"),\n    motionAppear: false,\n    motionDeadline: 1000\n  }, function (_ref) {\n    var _classNames3;\n\n    var motionClassName = _ref.className;\n    var scrollNumberPrefixCls = getPrefixCls('scroll-number', customizeScrollNumberPrefixCls);\n    var isDot = isDotRef.current;\n    var scrollNumberCls = classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-dot\"), isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count\"), !isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count-sm\"), size === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-multiple-words\"), !isDot && displayCount && displayCount.toString().length > 1), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames3));\n\n    var scrollNumberStyle = _extends({}, mergedStyle);\n\n    if (color && !isPresetColor(color)) {\n      scrollNumberStyle = scrollNumberStyle || {};\n      scrollNumberStyle.background = color;\n    }\n\n    return /*#__PURE__*/React.createElement(ScrollNumber, {\n      prefixCls: scrollNumberPrefixCls,\n      show: !isHidden,\n      motionClassName: motionClassName,\n      className: scrollNumberCls,\n      count: displayCount,\n      title: titleNode,\n      style: scrollNumberStyle,\n      key: \"scrollNumber\"\n    }, displayNode);\n  }), statusTextNode);\n};\n\nBadge.Ribbon = Ribbon;\nexport default Badge;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AAEzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,SAAS;AAEvC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,EAAE,EAAE;EAC7B,IAAIC,WAAW,EAAEC,YAAY;EAE7B,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACjCC,8BAA8B,GAAGL,EAAE,CAACM,qBAAqB;IACzDC,QAAQ,GAAGP,EAAE,CAACO,QAAQ;IACtBC,MAAM,GAAGR,EAAE,CAACQ,MAAM;IAClBC,IAAI,GAAGT,EAAE,CAACS,IAAI;IACdC,KAAK,GAAGV,EAAE,CAACU,KAAK;IAChBC,QAAQ,GAAGX,EAAE,CAACY,KAAK;IACnBA,KAAK,GAAGD,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ;IAC7CE,gBAAgB,GAAGb,EAAE,CAACc,aAAa;IACnCA,aAAa,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IACnEE,MAAM,GAAGf,EAAE,CAACgB,GAAG;IACfA,GAAG,GAAGD,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,MAAM;IACxCE,OAAO,GAAGjB,EAAE,CAACkB,IAAI;IACjBA,IAAI,GAAGD,OAAO,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,OAAO;IAC/CE,KAAK,GAAGnB,EAAE,CAACmB,KAAK;IAChBC,MAAM,GAAGpB,EAAE,CAACoB,MAAM;IAClBC,KAAK,GAAGrB,EAAE,CAACqB,KAAK;IAChBC,SAAS,GAAGtB,EAAE,CAACsB,SAAS;IACxBC,WAAW,GAAGvB,EAAE,CAACwB,QAAQ;IACzBA,QAAQ,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACvDE,SAAS,GAAGlD,MAAM,CAACyB,EAAE,EAAE,CAAC,WAAW,EAAE,uBAAuB,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EAEvM,IAAI0B,iBAAiB,GAAGrC,KAAK,CAACsC,UAAU,CAAC/B,aAAa,CAAC;IACnDgC,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIzB,SAAS,GAAGwB,YAAY,CAAC,OAAO,EAAEzB,kBAAkB,CAAC,CAAC,CAAC;;EAE3D,IAAI2B,oBAAoB,GAAGlB,KAAK,GAAGE,aAAa,GAAG,EAAE,CAACiB,MAAM,CAACjB,aAAa,EAAE,GAAG,CAAC,GAAGF,KAAK;EACxF,IAAIoB,SAAS,GAAGxB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKyB,SAAS,IAAIvB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKuB,SAAS;EAChG,IAAIC,MAAM,GAAGJ,oBAAoB,KAAK,GAAG,IAAIA,oBAAoB,KAAK,CAAC;EACvE,IAAIK,SAAS,GAAGnB,GAAG,IAAI,CAACkB,MAAM;EAC9B,IAAIE,WAAW,GAAGD,SAAS,GAAG,EAAE,GAAGL,oBAAoB;EACvD,IAAIO,QAAQ,GAAG/C,OAAO,CAAC,YAAY;IACjC,IAAIgD,OAAO,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKH,SAAS,IAAIG,WAAW,KAAK,EAAE;IACrF,OAAO,CAACE,OAAO,IAAIJ,MAAM,IAAI,CAACV,QAAQ,KAAK,CAACW,SAAS;EACvD,CAAC,EAAE,CAACC,WAAW,EAAEF,MAAM,EAAEV,QAAQ,EAAEW,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAII,QAAQ,GAAGhD,MAAM,CAACqB,KAAK,CAAC;EAE5B,IAAI,CAACyB,QAAQ,EAAE;IACbE,QAAQ,CAACC,OAAO,GAAG5B,KAAK;EAC1B;EAEA,IAAI6B,WAAW,GAAGF,QAAQ,CAACC,OAAO,CAAC,CAAC;;EAEpC,IAAIE,eAAe,GAAGnD,MAAM,CAAC6C,WAAW,CAAC;EAEzC,IAAI,CAACC,QAAQ,EAAE;IACbK,eAAe,CAACF,OAAO,GAAGJ,WAAW;EACvC;EAEA,IAAIO,YAAY,GAAGD,eAAe,CAACF,OAAO,CAAC,CAAC;;EAE5C,IAAII,QAAQ,GAAGrD,MAAM,CAAC4C,SAAS,CAAC;EAEhC,IAAI,CAACE,QAAQ,EAAE;IACbO,QAAQ,CAACJ,OAAO,GAAGL,SAAS;EAC9B,CAAC,CAAC;;EAGF,IAAIU,WAAW,GAAGvD,OAAO,CAAC,YAAY;IACpC,IAAI,CAAC8B,MAAM,EAAE;MACX,OAAO9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,KAAK,CAAC;IAC5B;IAEA,IAAIyB,WAAW,GAAG;MAChBC,SAAS,EAAE3B,MAAM,CAAC,CAAC;IACrB,CAAC;IAED,IAAIS,SAAS,KAAK,KAAK,EAAE;MACvBiB,WAAW,CAACE,IAAI,GAAGC,QAAQ,CAAC7B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5C,CAAC,MAAM;MACL0B,WAAW,CAACI,KAAK,GAAG,CAACD,QAAQ,CAAC7B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9C;IAEA,OAAO9C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwE,WAAW,CAAC,EAAEzB,KAAK,CAAC;EACnD,CAAC,EAAE,CAACQ,SAAS,EAAET,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,IAAI8B,SAAS,GAAGhC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,OAAOsB,WAAW,KAAK,QAAQ,IAAI,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGR,SAAS,CAAC,CAAC;;EAE3J,IAAImB,cAAc,GAAGf,QAAQ,IAAI,CAAC5B,IAAI,GAAG,IAAI,GAAG,aAAapB,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAE;IACvF/B,SAAS,EAAE,EAAE,CAACS,MAAM,CAAC3B,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEK,IAAI,CAAC,CAAC,CAAC;;EAEV,IAAI6C,WAAW,GAAG,CAACb,WAAW,IAAIpE,OAAO,CAACoE,WAAW,CAAC,KAAK,QAAQ,GAAGR,SAAS,GAAGpC,YAAY,CAAC4C,WAAW,EAAE,UAAUc,QAAQ,EAAE;IAC9H,OAAO;MACLlC,KAAK,EAAE/C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuE,WAAW,CAAC,EAAEU,QAAQ,CAAClC,KAAK;IAC3D,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAImC,SAAS,GAAG/D,UAAU,EAAEQ,WAAW,GAAG,CAAC,CAAC,EAAE7B,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAAC8B,MAAM,CAAC3B,SAAS,EAAE,aAAa,CAAC,EAAE4B,SAAS,CAAC,EAAE5D,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAAC8B,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACvB,MAAM,CAAC,EAAE,CAAC,CAACA,MAAM,CAAC,EAAEpC,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAAC8B,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACrB,KAAK,CAAC,EAAEZ,aAAa,CAACY,KAAK,CAAC,CAAC,EAAET,WAAW,CAAC,CAAC;EACtU,IAAIwD,WAAW,GAAG,CAAC,CAAC;EAEpB,IAAI/C,KAAK,IAAI,CAACZ,aAAa,CAACY,KAAK,CAAC,EAAE;IAClC+C,WAAW,CAACC,UAAU,GAAGhD,KAAK;EAChC;EAEA,IAAIiD,cAAc,GAAGlE,UAAU,CAACW,SAAS,GAAGF,YAAY,GAAG,CAAC,CAAC,EAAE9B,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC6B,MAAM,CAAC3B,SAAS,EAAE,SAAS,CAAC,EAAE4B,SAAS,CAAC,EAAE5D,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC6B,MAAM,CAAC3B,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAACG,QAAQ,CAAC,EAAEnC,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC6B,MAAM,CAAC3B,SAAS,EAAE,MAAM,CAAC,EAAEyB,SAAS,KAAK,KAAK,CAAC,EAAE3B,YAAY,GAAGoB,SAAS,CAAC,CAAC,CAAC;;EAEzU,IAAI,CAACf,QAAQ,IAAIyB,SAAS,EAAE;IAC1B,IAAI4B,eAAe,GAAGf,WAAW,CAACnC,KAAK;IACvC,OAAO,aAAarB,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAEmD,SAAS,EAAE;MACtEH,SAAS,EAAEqC,cAAc;MACzBtC,KAAK,EAAEwB;IACT,CAAC,CAAC,EAAE,aAAaxD,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAE;MAC3C/B,SAAS,EAAEkC,SAAS;MACpBnC,KAAK,EAAEoC;IACT,CAAC,CAAC,EAAE,aAAapE,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAE;MAC3ChC,KAAK,EAAE;QACLX,KAAK,EAAEkD;MACT,CAAC;MACDtC,SAAS,EAAE,EAAE,CAACS,MAAM,CAAC3B,SAAS,EAAE,cAAc;IAChD,CAAC,EAAEK,IAAI,CAAC,CAAC;EACX,CAAC,CAAC;;EAGF,OAAO,aAAapB,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAEmD,SAAS,EAAE;IACtEH,SAAS,EAAEqC;EACb,CAAC,CAAC,EAAEpD,QAAQ,EAAE,aAAalB,KAAK,CAACgE,aAAa,CAAC7D,SAAS,EAAE;IACxDqE,OAAO,EAAE,CAACxB,QAAQ;IAClByB,UAAU,EAAE,EAAE,CAAC/B,MAAM,CAAC3B,SAAS,EAAE,OAAO,CAAC;IACzC2D,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE;EAClB,CAAC,EAAE,UAAUC,IAAI,EAAE;IACjB,IAAIC,YAAY;IAEhB,IAAIC,eAAe,GAAGF,IAAI,CAAC3C,SAAS;IACpC,IAAIhB,qBAAqB,GAAGsB,YAAY,CAAC,eAAe,EAAEvB,8BAA8B,CAAC;IACzF,IAAI+D,KAAK,GAAGxB,QAAQ,CAACJ,OAAO;IAC5B,IAAI6B,eAAe,GAAG5E,UAAU,EAAEyE,YAAY,GAAG,CAAC,CAAC,EAAE9F,eAAe,CAAC8F,YAAY,EAAE,EAAE,CAACnC,MAAM,CAAC3B,SAAS,EAAE,MAAM,CAAC,EAAEgE,KAAK,CAAC,EAAEhG,eAAe,CAAC8F,YAAY,EAAE,EAAE,CAACnC,MAAM,CAAC3B,SAAS,EAAE,QAAQ,CAAC,EAAE,CAACgE,KAAK,CAAC,EAAEhG,eAAe,CAAC8F,YAAY,EAAE,EAAE,CAACnC,MAAM,CAAC3B,SAAS,EAAE,WAAW,CAAC,EAAEc,IAAI,KAAK,OAAO,CAAC,EAAE9C,eAAe,CAAC8F,YAAY,EAAE,EAAE,CAACnC,MAAM,CAAC3B,SAAS,EAAE,iBAAiB,CAAC,EAAE,CAACgE,KAAK,IAAIzB,YAAY,IAAIA,YAAY,CAAC2B,QAAQ,CAAC,CAAC,CAACnF,MAAM,GAAG,CAAC,CAAC,EAAEf,eAAe,CAAC8F,YAAY,EAAE,EAAE,CAACnC,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACvB,MAAM,CAAC,EAAE,CAAC,CAACA,MAAM,CAAC,EAAEpC,eAAe,CAAC8F,YAAY,EAAE,EAAE,CAACnC,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACrB,KAAK,CAAC,EAAEZ,aAAa,CAACY,KAAK,CAAC,CAAC,EAAEwD,YAAY,CAAC,CAAC;IAEvmB,IAAIK,iBAAiB,GAAGjG,QAAQ,CAAC,CAAC,CAAC,EAAEuE,WAAW,CAAC;IAEjD,IAAInC,KAAK,IAAI,CAACZ,aAAa,CAACY,KAAK,CAAC,EAAE;MAClC6D,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,CAAC;MAC3CA,iBAAiB,CAACb,UAAU,GAAGhD,KAAK;IACtC;IAEA,OAAO,aAAarB,KAAK,CAACgE,aAAa,CAAC3D,YAAY,EAAE;MACpDU,SAAS,EAAEE,qBAAqB;MAChCkE,IAAI,EAAE,CAACnC,QAAQ;MACf8B,eAAe,EAAEA,eAAe;MAChC7C,SAAS,EAAE+C,eAAe;MAC1BzD,KAAK,EAAE+B,YAAY;MACnBxB,KAAK,EAAEgC,SAAS;MAChB9B,KAAK,EAAEkD,iBAAiB;MACxBE,GAAG,EAAE;IACP,CAAC,EAAEnB,WAAW,CAAC;EACjB,CAAC,CAAC,EAAEF,cAAc,CAAC;AACrB,CAAC;AAEDrD,KAAK,CAACJ,MAAM,GAAGA,MAAM;AACrB,eAAeI,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}