{"ast": null, "code": "import * as React from 'react';\nvar TableContext = /*#__PURE__*/React.createContext(null);\nexport default TableContext;", "map": {"version": 3, "names": ["React", "TableContext", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/context/TableContext.js"], "sourcesContent": ["import * as React from 'react';\nvar TableContext = /*#__PURE__*/React.createContext(null);\nexport default TableContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACzD,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}