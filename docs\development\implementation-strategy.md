# Strategia Implementazione Suddivisione Prodotti

## 🎯 Obiettivo Strategico

Trasformare il monolite **Winet E-Procurement** in una suite di **8 prodotti vendibili separatamente**, mantenendo:
- **Codebase unificata** per facilità manutenzione
- **Architettura modulare** per deployment flessibile
- **Sistema di licenze** per controllo funzionalità
- **Performance ottimizzate** per ogni prodotto

## 🏗️ ARCHITETTURA TECNICA SUDDIVISIONE

### 1. **Sistema di Configurazione Prodotti**

#### Product Configuration Manager
```javascript
// src/config/products.js
export const PRODUCTS = {
  ENTERPRISE: {
    name: 'Winet Enterprise',
    roles: ['ADMIN', 'DISTRIBUTORE', 'AFFILIATO', 'AGENTE', 'PDV', 'RESP_MAG', 'OP_MAG', 'AUTISTA', 'LOGISTICA', 'CHAIN', 'RING'],
    features: ['all'],
    modules: ['warehouse', 'sales', 'purchasing', 'logistics', 'marketplace', 'network', 'analytics'],
    pricing: { tier: 'enterprise', basePrice: 15000 }
  },
  
  DISTRIBUTOR: {
    name: 'Winet Distributor',
    roles: ['ADMIN', 'DISTRIBUTORE', 'AGENTE', 'AUTISTA', 'RESP_MAG', 'OP_MAG'],
    features: ['warehouse', 'sales', 'purchasing', 'logistics', 'products'],
    modules: ['warehouse', 'sales', 'purchasing', 'logistics'],
    pricing: { tier: 'professional', basePrice: 8000 }
  },
  
  AFFILIATE: {
    name: 'Winet Affiliate',
    roles: ['ADMIN', 'AFFILIATO', 'PDV'],
    features: ['orders', 'products', 'documents', 'messaging'],
    modules: ['orders', 'products', 'messaging'],
    pricing: { tier: 'business', basePrice: 3000 }
  },
  
  MARKETPLACE: {
    name: 'Winet Marketplace',
    roles: ['ADMIN', 'PDV'],
    features: ['marketplace', 'cart', 'orders', 'documents'],
    modules: ['marketplace', 'cart'],
    pricing: { tier: 'starter', basePrice: 1500 }
  },
  
  WAREHOUSE: {
    name: 'Winet Warehouse',
    roles: ['ADMIN', 'RESP_MAG', 'OP_MAG', 'AUTISTA'],
    features: ['inventory', 'movements', 'logistics', 'products'],
    modules: ['warehouse', 'logistics'],
    pricing: { tier: 'professional', basePrice: 4000 }
  },
  
  SALES: {
    name: 'Winet Sales',
    roles: ['ADMIN', 'AGENTE'],
    features: ['customers', 'orders', 'products', 'documents'],
    modules: ['sales', 'customers'],
    pricing: { tier: 'business', basePrice: 2000 }
  },
  
  NETWORK: {
    name: 'Winet Network',
    roles: ['ADMIN', 'CHAIN', 'RING'],
    features: ['network', 'distribution', 'coordination'],
    modules: ['network', 'distribution'],
    pricing: { tier: 'professional', basePrice: 6000 }
  },
  
  LOGISTICS: {
    name: 'Winet Logistics',
    roles: ['ADMIN', 'LOGISTICA', 'AUTISTA'],
    features: ['deliveries', 'tracking', 'routes'],
    modules: ['logistics', 'tracking'],
    pricing: { tier: 'business', basePrice: 1000 }
  }
};
```

#### License Manager
```javascript
// src/utils/licenseManager.js
class LicenseManager {
  constructor() {
    this.currentLicense = this.loadLicense();
  }

  loadLicense() {
    // Load from localStorage, API, or environment
    const licenseKey = localStorage.getItem('license_key') || process.env.REACT_APP_LICENSE_KEY;
    return this.validateLicense(licenseKey);
  }

  validateLicense(licenseKey) {
    // Validate license with backend
    // Return product configuration
    try {
      const decoded = this.decodeLicense(licenseKey);
      return PRODUCTS[decoded.product] || PRODUCTS.MARKETPLACE;
    } catch (error) {
      console.warn('Invalid license, defaulting to MARKETPLACE');
      return PRODUCTS.MARKETPLACE;
    }
  }

  hasRole(role) {
    return this.currentLicense.roles.includes(role);
  }

  hasFeature(feature) {
    return this.currentLicense.features.includes(feature) || 
           this.currentLicense.features.includes('all');
  }

  hasModule(module) {
    return this.currentLicense.modules.includes(module);
  }

  getAvailableRoutes() {
    return routes.filter(route => 
      this.hasRole(route.role) && this.hasFeature(route.feature)
    );
  }
}

export const licenseManager = new LicenseManager();
```

### 2. **Sistema di Routing Condizionale**

#### Protected Route con License Check
```javascript
// src/components/ProtectedRoute.jsx
import { licenseManager } from '../utils/licenseManager';

const ProtectedRoute = ({ component: Component, role, feature, ...rest }) => {
  return (
    <Route
      {...rest}
      render={props => {
        // Check authentication
        if (!isLogin()) {
          return <Redirect to="/login" />;
        }

        // Check role permission
        if (role && !licenseManager.hasRole(role)) {
          return <Redirect to="/unauthorized" />;
        }

        // Check feature permission
        if (feature && !licenseManager.hasFeature(feature)) {
          return <Redirect to="/feature-not-available" />;
        }

        return <Component {...props} />;
      }}
    />
  );
};
```

#### Dynamic Menu Generation
```javascript
// src/components/navigation/DynamicMenu.jsx
const DynamicMenu = () => {
  const [menuItems, setMenuItems] = useState([]);

  useEffect(() => {
    const availableMenuItems = generateMenuForLicense(licenseManager.currentLicense);
    setMenuItems(availableMenuItems);
  }, []);

  const generateMenuForLicense = (license) => {
    const baseMenu = menu[localStorage.getItem('role')?.toLowerCase()];
    
    return Object.entries(baseMenu).filter(([key, section]) => {
      // Filter sections based on license features
      const sectionFeatures = getSectionFeatures(key);
      return sectionFeatures.some(feature => license.features.includes(feature) || license.features.includes('all'));
    });
  };

  return (
    <nav className="dynamic-menu">
      {menuItems.map(([key, section]) => (
        <MenuSection key={key} section={section} />
      ))}
    </nav>
  );
};
```

### 3. **Build System Multi-Prodotto**

#### Webpack Configuration
```javascript
// webpack.config.js
const path = require('path');
const { DefinePlugin } = require('webpack');

module.exports = (env) => {
  const product = env.PRODUCT || 'ENTERPRISE';
  const productConfig = require(`./src/config/products.js`).PRODUCTS[product];

  return {
    entry: './src/index.js',
    output: {
      path: path.resolve(__dirname, `dist/${product.toLowerCase()}`),
      filename: '[name].[contenthash].js',
    },
    plugins: [
      new DefinePlugin({
        'process.env.REACT_APP_PRODUCT': JSON.stringify(product),
        'process.env.REACT_APP_PRODUCT_CONFIG': JSON.stringify(productConfig),
      }),
    ],
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          // Common chunks for all products
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          // Product-specific chunks
          [product.toLowerCase()]: {
            test: new RegExp(`[\\/]src[\\/].*${product.toLowerCase()}[\\/]`),
            name: product.toLowerCase(),
            chunks: 'all',
          },
        },
      },
    },
  };
};
```

#### Build Scripts
```json
{
  "scripts": {
    "build:enterprise": "PRODUCT=ENTERPRISE npm run build",
    "build:distributor": "PRODUCT=DISTRIBUTOR npm run build",
    "build:affiliate": "PRODUCT=AFFILIATE npm run build",
    "build:marketplace": "PRODUCT=MARKETPLACE npm run build",
    "build:warehouse": "PRODUCT=WAREHOUSE npm run build",
    "build:sales": "PRODUCT=SALES npm run build",
    "build:network": "PRODUCT=NETWORK npm run build",
    "build:logistics": "PRODUCT=LOGISTICS npm run build",
    "build:all": "npm run build:enterprise && npm run build:distributor && npm run build:affiliate && npm run build:marketplace && npm run build:warehouse && npm run build:sales && npm run build:network && npm run build:logistics"
  }
}
```

### 4. **Component Lazy Loading per Prodotto**

#### Dynamic Component Loading
```javascript
// src/utils/componentLoader.js
import { lazy } from 'react';
import { licenseManager } from './licenseManager';

const componentMap = {
  // Distributore components
  DashboardDistributore: () => import('../common/distributore/dashboard/dashboardDistributore'),
  GestioneMagazzini: () => import('../common/distributore/gestioneMagazzini'),
  UfficioVendite: () => import('../common/distributore/ufficioVendite'),
  
  // PDV components
  DashboardPDV: () => import('../common/pdv/dashboardPDV'),
  Marketplace: () => import('../components/generalizzazioni/marketplace/marketplace'),
  
  // Admin components
  DashboardAmministratore: () => import('../common/amministratore/DashboardAmministratore'),
  GestioneUtenti: () => import('../common/amministratore/gestioneUtenti'),
  
  // Add all other components...
};

export const loadComponent = (componentName) => {
  // Check if component is available in current license
  const componentFeature = getComponentFeature(componentName);
  
  if (!licenseManager.hasFeature(componentFeature)) {
    return lazy(() => import('../components/FeatureNotAvailable'));
  }

  return lazy(componentMap[componentName]);
};

const getComponentFeature = (componentName) => {
  const featureMap = {
    DashboardDistributore: 'warehouse',
    GestioneMagazzini: 'warehouse',
    UfficioVendite: 'sales',
    Marketplace: 'marketplace',
    DashboardPDV: 'marketplace',
    // Map all components to features...
  };
  
  return featureMap[componentName] || 'basic';
};
```

### 5. **Docker Multi-Stage per Prodotti**

#### Dockerfile Multi-Product
```dockerfile
# Dockerfile.multi-product
FROM node:14 AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM base AS build-enterprise
COPY . .
RUN npm run build:enterprise

FROM base AS build-marketplace
COPY . .
RUN npm run build:marketplace

FROM base AS build-distributor
COPY . .
RUN npm run build:distributor

# Runtime stage
FROM nginx:alpine AS runtime
ARG PRODUCT=enterprise
COPY --from=build-${PRODUCT} /app/dist/${PRODUCT} /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose per Prodotti
```yaml
# docker-compose.products.yml
version: '3.8'

services:
  winet-enterprise:
    build:
      context: .
      dockerfile: Dockerfile.multi-product
      args:
        PRODUCT: enterprise
    ports:
      - "3001:80"
    environment:
      - REACT_APP_PRODUCT=ENTERPRISE

  winet-marketplace:
    build:
      context: .
      dockerfile: Dockerfile.multi-product
      args:
        PRODUCT: marketplace
    ports:
      - "3002:80"
    environment:
      - REACT_APP_PRODUCT=MARKETPLACE

  winet-distributor:
    build:
      context: .
      dockerfile: Dockerfile.multi-product
      args:
        PRODUCT: distributor
    ports:
      - "3003:80"
    environment:
      - REACT_APP_PRODUCT=DISTRIBUTOR
```

### 6. **Sistema di Feature Flags**

#### Feature Flag Manager
```javascript
// src/utils/featureFlags.js
class FeatureFlagManager {
  constructor() {
    this.flags = this.loadFeatureFlags();
  }

  loadFeatureFlags() {
    const product = process.env.REACT_APP_PRODUCT || 'ENTERPRISE';
    const productConfig = PRODUCTS[product];
    
    return {
      // Core features
      hasWarehouse: productConfig.features.includes('warehouse') || productConfig.features.includes('all'),
      hasSales: productConfig.features.includes('sales') || productConfig.features.includes('all'),
      hasMarketplace: productConfig.features.includes('marketplace') || productConfig.features.includes('all'),
      hasLogistics: productConfig.features.includes('logistics') || productConfig.features.includes('all'),
      hasNetwork: productConfig.features.includes('network') || productConfig.features.includes('all'),
      
      // Advanced features
      hasAnalytics: productConfig.features.includes('analytics') || productConfig.features.includes('all'),
      hasExternalSystems: productConfig.features.includes('external') || productConfig.features.includes('all'),
      hasMultiWarehouse: productConfig.features.includes('multi-warehouse') || productConfig.features.includes('all'),
    };
  }

  isEnabled(flag) {
    return this.flags[flag] || false;
  }
}

export const featureFlags = new FeatureFlagManager();
```

#### Feature Flag Component
```javascript
// src/components/FeatureFlag.jsx
const FeatureFlag = ({ feature, children, fallback = null }) => {
  const isEnabled = featureFlags.isEnabled(feature);
  
  return isEnabled ? children : fallback;
};

// Usage example
<FeatureFlag feature="hasWarehouse">
  <GestioneMagazzini />
</FeatureFlag>

<FeatureFlag 
  feature="hasAnalytics" 
  fallback={<div>Analytics not available in your plan</div>}
>
  <AdvancedAnalytics />
</FeatureFlag>
```

### 7. **API Endpoint Filtering**

#### API Request Interceptor
```javascript
// src/utils/apiInterceptor.js
import { licenseManager } from './licenseManager';

const restrictedEndpoints = {
  warehouse: ['/warehouses', '/inventory', '/movements'],
  sales: ['/orders', '/customers', '/agents'],
  analytics: ['/analytics', '/reports'],
  external: ['/externalsystems'],
};

export const apiInterceptor = (config) => {
  const endpoint = config.url;
  
  // Check if endpoint is restricted
  for (const [feature, endpoints] of Object.entries(restrictedEndpoints)) {
    if (endpoints.some(ep => endpoint.includes(ep))) {
      if (!licenseManager.hasFeature(feature)) {
        throw new Error(`Feature '${feature}' not available in your license`);
      }
    }
  }
  
  return config;
};

// Apply interceptor
axios.interceptors.request.use(apiInterceptor);
```

### 8. **Deployment Strategy**

#### CI/CD Pipeline per Prodotti
```yaml
# .github/workflows/deploy-products.yml
name: Deploy Products

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        product: [enterprise, distributor, affiliate, marketplace, warehouse, sales, network, logistics]
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '14'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build product
        run: npm run build:${{ matrix.product }}
        env:
          REACT_APP_PRODUCT: ${{ matrix.product }}
      
      - name: Build Docker image
        run: |
          docker build -t winet-${{ matrix.product }}:latest \
            --build-arg PRODUCT=${{ matrix.product }} \
            -f Dockerfile.multi-product .
      
      - name: Deploy to registry
        run: |
          docker tag winet-${{ matrix.product }}:latest \
            registry.company.com/winet-${{ matrix.product }}:${{ github.sha }}
          docker push registry.company.com/winet-${{ matrix.product }}:${{ github.sha }}
```

### 9. **Monitoring e Analytics per Prodotto**

#### Product Usage Analytics
```javascript
// src/utils/productAnalytics.js
class ProductAnalytics {
  constructor() {
    this.product = process.env.REACT_APP_PRODUCT;
    this.userId = localStorage.getItem('userid');
  }

  trackFeatureUsage(feature, action) {
    const event = {
      product: this.product,
      feature,
      action,
      userId: this.userId,
      timestamp: new Date().toISOString(),
    };

    // Send to analytics service
    this.sendAnalytics('feature_usage', event);
  }

  trackPerformance(metric, value) {
    const event = {
      product: this.product,
      metric,
      value,
      timestamp: new Date().toISOString(),
    };

    this.sendAnalytics('performance', event);
  }

  sendAnalytics(type, data) {
    // Send to analytics backend
    fetch('/api/analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type, data }),
    });
  }
}

export const productAnalytics = new ProductAnalytics();
```

## 🚀 PIANO DI IMPLEMENTAZIONE

### Fase 1: Preparazione (2-3 settimane)
1. **Setup sistema licenze**: Implementare LicenseManager
2. **Configurazione prodotti**: Definire PRODUCTS configuration
3. **Feature flags**: Implementare FeatureFlagManager
4. **Testing framework**: Setup testing per prodotti

### Fase 2: Refactoring (4-6 settimane)
1. **Component isolation**: Separare componenti per feature
2. **Route protection**: Implementare ProtectedRoute con licenze
3. **Dynamic loading**: Lazy loading condizionale
4. **API filtering**: Interceptor per endpoint

### Fase 3: Build System (2-3 settimane)
1. **Webpack configuration**: Multi-product builds
2. **Docker setup**: Multi-stage builds
3. **CI/CD pipeline**: Deployment automatico
4. **Bundle optimization**: Ottimizzazione per prodotto

### Fase 4: Testing e Validazione (3-4 settimane)
1. **Unit testing**: Test per ogni prodotto
2. **Integration testing**: Test cross-prodotto
3. **Performance testing**: Ottimizzazione bundle
4. **User acceptance testing**: Validazione funzionalità

### Fase 5: Deployment (1-2 settimane)
1. **Production deployment**: Deploy prodotti separati
2. **Monitoring setup**: Analytics per prodotto
3. **Documentation**: Guide per ogni prodotto
4. **Training**: Formazione team vendite

## 📊 METRICHE DI SUCCESSO

### Metriche Tecniche
- **Bundle size reduction**: 40-60% per prodotto specifico
- **Load time improvement**: <2s per tutti i prodotti
- **Memory usage**: <100MB per prodotto
- **Test coverage**: >90% per tutti i prodotti

### Metriche Business
- **Time to market**: Riduzione 50% per nuovi prodotti
- **Customer acquisition**: +200% con prodotti specifici
- **Revenue growth**: +150% con pricing differenziato
- **Support efficiency**: +80% con documentazione specifica

## ✅ IMPLEMENTAZIONE COMPLETATA

### 🎯 **Sistema di Feature Flags Implementato**

#### 📁 **File Creati**
- `src/config/products.js` - Configurazione prodotti e feature
- `src/utils/licenseManager.js` - Gestione licenze e permessi
- `src/components/FeatureFlag.jsx` - Component per feature flags
- `src/components/ProtectedRoute.jsx` - Route protette
- `src/components/DynamicMenu.jsx` - Menu dinamico
- `src/components/ConditionalDashboard.jsx` - Dashboard condizionale

#### 🧪 **Testing Completato**
- `src/utils/__tests__/licenseManager.test.js` - Test license manager
- Bug fix in `goToTopOfPage.jsx` per compatibilità testing
- Tutti i test passano ✅

#### 🔧 **Funzionalità Implementate**

##### 1. **Configurazione Prodotti**
```javascript
// 5 Prodotti Core + 3 Add-on
STARTER (€1.500-2.500) - PDV base
BUSINESS (€3.000-5.000) - Affiliati
PROFESSIONAL (€6.000-10.000) - Distributori
ENTERPRISE (€12.000-18.000) - Grandi distributori
NETWORK (€8.000-15.000) - Catene

// Add-on Modules
ANALYTICS_PRO (+€2.000-3.000)
INTEGRATIONS_PACK (+€1.500-2.500)
LOGISTICS_PRO (+€1.000-2.000)
```

##### 2. **Sistema di Licenze**
```javascript
// Verifica feature
licenseManager.hasFeature('marketplace')

// Verifica ruolo
licenseManager.hasRole('DISTRIBUTORE')

// Verifica accesso route
licenseManager.canAccessRoute('/distributore/dashboard')

// Verifica limiti
licenseManager.checkLimits('maxUsers', 10)
```

##### 3. **Component Condizionali**
```jsx
// Feature Flag Component
<FeatureFlag feature="warehouse_management">
  <GestioneMagazzini />
</FeatureFlag>

// Protected Route
<ProtectedRoute
  component={Dashboard}
  feature="sales_management"
  role="DISTRIBUTORE"
/>

// HOC Pattern
const ProtectedComponent = withFeatureFlag(
  MyComponent,
  'analytics_advanced'
);
```

##### 4. **Menu e Dashboard Dinamici**
- Menu che si adatta alle feature disponibili
- Dashboard con widget condizionali
- Suggerimenti di upgrade integrati
- Badge e indicatori di stato

#### 📊 **Strategia di Suddivisione Intelligente**

##### 🔴 **Core Features (Non Suddivisibili)**
- Autenticazione e autorizzazione
- UI framework base
- Gestione utenti base
- Comunicazione API

##### 🟡 **Semi-Core Features (Condivisibili)**
- Gestione prodotti (base vs avanzata)
- Sistema ordini (creazione vs workflow)
- Gestione documenti (visualizzazione vs generazione)

##### 🟢 **Auxiliary Features (Modularizzabili)**
- Analytics avanzate
- Integrazioni esterne
- Logistica avanzata
- Gestione rete

#### 🚀 **Vantaggi Implementazione**

##### ✅ **Tecnici**
- **Codebase unificata**: Un solo codice per tutti i prodotti
- **Feature flags dinamiche**: Abilitazione/disabilitazione runtime
- **Testing modulare**: Test specifici per ogni configurazione
- **Deployment flessibile**: Container specifici per prodotto

##### ✅ **Commerciali**
- **Entry point basso**: Da €1.500 vs €15.000 originale
- **Upselling naturale**: Crescita graduale del cliente
- **Add-on revenue**: Ricavi aggiuntivi modulari
- **Market coverage**: Tutti i segmenti coperti

##### ✅ **Operativi**
- **Manutenzione semplificata**: Un solo codebase
- **Time to market**: Nuovi prodotti rapidamente deployabili
- **Personalizzazione**: Add-on per esigenze specifiche
- **Feedback loop**: Miglioramenti basati su utilizzo reale

#### 🎯 **Prossimi Passi**

##### 1. **Backend di Sviluppo** (Priorità Alta)
- Setup Docker container con database di test
- API mock per sviluppo isolato
- Seed data per testing completo

##### 2. **Build System Multi-Prodotto** (Priorità Media)
- Webpack configuration per prodotti specifici
- Docker multi-stage builds
- CI/CD pipeline per deployment automatico

##### 3. **Testing Avanzato** (Priorità Media)
- E2E testing con Cypress
- Performance testing per prodotto
- Integration testing cross-prodotto

##### 4. **Commercializzazione** (Priorità Bassa)
- Materiali marketing per ogni prodotto
- Pricing definitivo e packaging
- Training team vendite

### 📈 **Potenziale di Mercato Rivisto**

#### 🎯 **Segmentazione Intelligente**
- **Starter**: 100-200 clienti × €1.500-2.500 = €150K-500K
- **Business**: 50-100 clienti × €3.000-5.000 = €150K-500K
- **Professional**: 20-30 clienti × €6.000-10.000 = €120K-300K
- **Enterprise**: 5-10 clienti × €12.000-18.000 = €60K-180K
- **Network**: 10-20 clienti × €8.000-15.000 = €80K-300K
- **Add-ons**: 30% clienti × €1.000-3.000 = €150K-450K

**Totale Potenziale**: €710K - €2.23M/anno

#### 🔄 **Strategia di Crescita**
1. **Fase 1**: Lancio STARTER e BUSINESS (6 mesi)
2. **Fase 2**: Introduzione PROFESSIONAL (3 mesi)
3. **Fase 3**: Lancio ENTERPRISE e NETWORK (3 mesi)
4. **Fase 4**: Add-on modules e personalizzazioni (ongoing)

Questa strategia garantisce una transizione graduale e sicura dal monolite ai prodotti separati, mantenendo la qualità e le performance mentre si massimizza il potenziale commerciale.
