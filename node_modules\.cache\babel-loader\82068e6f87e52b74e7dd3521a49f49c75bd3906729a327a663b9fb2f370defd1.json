{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport warning from \"rc-util/es/warning\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport PickerTrigger from './PickerTrigger';\nimport PickerPanel from './PickerPanel';\nimport usePickerInput from './hooks/usePickerInput';\nimport getDataOrAriaProps, { toArray, getValue, updateValues } from './utils/miscUtil';\nimport { getDefaultFormat, getInputSize, elementsContains } from './utils/uiUtil';\nimport PanelContext from './PanelContext';\nimport { isEqual, getClosingViewDate, isSameDate, isSameWeek, isSameQuarter, formatValue, parseValue } from './utils/dateUtil';\nimport useValueTexts from './hooks/useValueTexts';\nimport useTextValueMapping from './hooks/useTextValueMapping';\nimport RangeContext from './RangeContext';\nimport useRangeDisabled from './hooks/useRangeDisabled';\nimport getExtraFooter from './utils/getExtraFooter';\nimport getRanges from './utils/getRanges';\nimport useRangeViewDates from './hooks/useRangeViewDates';\nimport useHoverValue from './hooks/useHoverValue';\nimport { legacyPropsWarning } from './utils/warnUtil';\nfunction reorderValues(values, generateConfig) {\n  if (values && values[0] && values[1] && generateConfig.isAfter(values[0], values[1])) {\n    return [values[1], values[0]];\n  }\n  return values;\n}\nfunction canValueTrigger(value, index, disabled, allowEmpty) {\n  if (value) {\n    return true;\n  }\n  if (allowEmpty && allowEmpty[index]) {\n    return true;\n  }\n  if (disabled[(index + 1) % 2]) {\n    return true;\n  }\n  return false;\n}\nfunction InnerRangePicker(props) {\n  var _classNames2, _classNames3, _classNames4;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-picker' : _props$prefixCls,\n    id = props.id,\n    style = props.style,\n    className = props.className,\n    popupStyle = props.popupStyle,\n    dropdownClassName = props.dropdownClassName,\n    transitionName = props.transitionName,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    placeholder = props.placeholder,\n    autoFocus = props.autoFocus,\n    disabled = props.disabled,\n    format = props.format,\n    _props$picker = props.picker,\n    picker = _props$picker === void 0 ? 'date' : _props$picker,\n    showTime = props.showTime,\n    use12Hours = props.use12Hours,\n    _props$separator = props.separator,\n    separator = _props$separator === void 0 ? '~' : _props$separator,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    defaultPickerValue = props.defaultPickerValue,\n    open = props.open,\n    defaultOpen = props.defaultOpen,\n    disabledDate = props.disabledDate,\n    _disabledTime = props.disabledTime,\n    dateRender = props.dateRender,\n    panelRender = props.panelRender,\n    ranges = props.ranges,\n    allowEmpty = props.allowEmpty,\n    allowClear = props.allowClear,\n    suffixIcon = props.suffixIcon,\n    clearIcon = props.clearIcon,\n    pickerRef = props.pickerRef,\n    inputReadOnly = props.inputReadOnly,\n    mode = props.mode,\n    renderExtraFooter = props.renderExtraFooter,\n    onChange = props.onChange,\n    onOpenChange = props.onOpenChange,\n    onPanelChange = props.onPanelChange,\n    onCalendarChange = props.onCalendarChange,\n    _onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    _onOk = props.onOk,\n    _onKeyDown = props.onKeyDown,\n    components = props.components,\n    order = props.order,\n    direction = props.direction,\n    activePickerIndex = props.activePickerIndex,\n    _props$autoComplete = props.autoComplete,\n    autoComplete = _props$autoComplete === void 0 ? 'off' : _props$autoComplete;\n  var needConfirmButton = picker === 'date' && !!showTime || picker === 'time'; // We record opened status here in case repeat open with picker\n\n  var openRecordsRef = useRef({});\n  var containerRef = useRef(null);\n  var panelDivRef = useRef(null);\n  var startInputDivRef = useRef(null);\n  var endInputDivRef = useRef(null);\n  var separatorRef = useRef(null);\n  var startInputRef = useRef(null);\n  var endInputRef = useRef(null);\n  var arrowRef = useRef(null); // ============================ Warning ============================\n\n  if (process.env.NODE_ENV !== 'production') {\n    legacyPropsWarning(props);\n  } // ============================= Misc ==============================\n\n  var formatList = toArray(getDefaultFormat(format, picker, showTime, use12Hours)); // Active picker\n\n  var _useMergedState = useMergedState(0, {\n      value: activePickerIndex\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedActivePickerIndex = _useMergedState2[0],\n    setMergedActivePickerIndex = _useMergedState2[1]; // Operation ref\n\n  var operationRef = useRef(null);\n  var mergedDisabled = React.useMemo(function () {\n    if (Array.isArray(disabled)) {\n      return disabled;\n    }\n    return [disabled || false, disabled || false];\n  }, [disabled]); // ============================= Value =============================\n\n  var _useMergedState3 = useMergedState(null, {\n      value: value,\n      defaultValue: defaultValue,\n      postState: function postState(values) {\n        return picker === 'time' && !order ? values : reorderValues(values, generateConfig);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedValue = _useMergedState4[0],\n    setInnerValue = _useMergedState4[1]; // =========================== View Date ===========================\n  // Config view panel\n\n  var _useRangeViewDates = useRangeViewDates({\n      values: mergedValue,\n      picker: picker,\n      defaultDates: defaultPickerValue,\n      generateConfig: generateConfig\n    }),\n    _useRangeViewDates2 = _slicedToArray(_useRangeViewDates, 2),\n    getViewDate = _useRangeViewDates2[0],\n    setViewDate = _useRangeViewDates2[1]; // ========================= Select Values =========================\n\n  var _useMergedState5 = useMergedState(mergedValue, {\n      postState: function postState(values) {\n        var postValues = values;\n        if (mergedDisabled[0] && mergedDisabled[1]) {\n          return postValues;\n        } // Fill disabled unit\n\n        for (var i = 0; i < 2; i += 1) {\n          if (mergedDisabled[i] && !getValue(postValues, i) && !getValue(allowEmpty, i)) {\n            postValues = updateValues(postValues, generateConfig.getNow(), i);\n          }\n        }\n        return postValues;\n      }\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    selectedValue = _useMergedState6[0],\n    setSelectedValue = _useMergedState6[1]; // ============================= Modes =============================\n\n  var _useMergedState7 = useMergedState([picker, picker], {\n      value: mode\n    }),\n    _useMergedState8 = _slicedToArray(_useMergedState7, 2),\n    mergedModes = _useMergedState8[0],\n    setInnerModes = _useMergedState8[1];\n  useEffect(function () {\n    setInnerModes([picker, picker]);\n  }, [picker]);\n  var triggerModesChange = function triggerModesChange(modes, values) {\n    setInnerModes(modes);\n    if (onPanelChange) {\n      onPanelChange(values, modes);\n    }\n  }; // ========================= Disable Date ==========================\n\n  var _useRangeDisabled = useRangeDisabled({\n      picker: picker,\n      selectedValue: selectedValue,\n      locale: locale,\n      disabled: mergedDisabled,\n      disabledDate: disabledDate,\n      generateConfig: generateConfig\n    }, openRecordsRef.current[1], openRecordsRef.current[0]),\n    _useRangeDisabled2 = _slicedToArray(_useRangeDisabled, 2),\n    disabledStartDate = _useRangeDisabled2[0],\n    disabledEndDate = _useRangeDisabled2[1]; // ============================= Open ==============================\n\n  var _useMergedState9 = useMergedState(false, {\n      value: open,\n      defaultValue: defaultOpen,\n      postState: function postState(postOpen) {\n        return mergedDisabled[mergedActivePickerIndex] ? false : postOpen;\n      },\n      onChange: function onChange(newOpen) {\n        if (onOpenChange) {\n          onOpenChange(newOpen);\n        }\n        if (!newOpen && operationRef.current && operationRef.current.onClose) {\n          operationRef.current.onClose();\n        }\n      }\n    }),\n    _useMergedState10 = _slicedToArray(_useMergedState9, 2),\n    mergedOpen = _useMergedState10[0],\n    triggerInnerOpen = _useMergedState10[1];\n  var startOpen = mergedOpen && mergedActivePickerIndex === 0;\n  var endOpen = mergedOpen && mergedActivePickerIndex === 1; // ============================= Popup =============================\n  // Popup min width\n\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    popupMinWidth = _useState2[0],\n    setPopupMinWidth = _useState2[1];\n  useEffect(function () {\n    if (!mergedOpen && containerRef.current) {\n      setPopupMinWidth(containerRef.current.offsetWidth);\n    }\n  }, [mergedOpen]); // ============================ Trigger ============================\n\n  var triggerRef = React.useRef();\n  function _triggerOpen(newOpen, index) {\n    if (newOpen) {\n      clearTimeout(triggerRef.current);\n      openRecordsRef.current[index] = true;\n      setMergedActivePickerIndex(index);\n      triggerInnerOpen(newOpen); // Open to reset view date\n\n      if (!mergedOpen) {\n        setViewDate(null, index);\n      }\n    } else if (mergedActivePickerIndex === index) {\n      triggerInnerOpen(newOpen); // Clean up async\n      // This makes ref not quick refresh in case user open another input with blur trigger\n\n      var openRecords = openRecordsRef.current;\n      triggerRef.current = setTimeout(function () {\n        if (openRecords === openRecordsRef.current) {\n          openRecordsRef.current = {};\n        }\n      });\n    }\n  }\n  function triggerOpenAndFocus(index) {\n    _triggerOpen(true, index); // Use setTimeout to make sure panel DOM exists\n\n    setTimeout(function () {\n      var inputRef = [startInputRef, endInputRef][index];\n      if (inputRef.current) {\n        inputRef.current.focus();\n      }\n    }, 0);\n  }\n  function triggerChange(newValue, sourceIndex) {\n    var values = newValue;\n    var startValue = getValue(values, 0);\n    var endValue = getValue(values, 1); // >>>>> Format start & end values\n\n    if (startValue && endValue && generateConfig.isAfter(startValue, endValue)) {\n      if (\n      // WeekPicker only compare week\n      picker === 'week' && !isSameWeek(generateConfig, locale.locale, startValue, endValue) ||\n      // QuotaPicker only compare week\n      picker === 'quarter' && !isSameQuarter(generateConfig, startValue, endValue) ||\n      // Other non-TimePicker compare date\n      picker !== 'week' && picker !== 'quarter' && picker !== 'time' && !isSameDate(generateConfig, startValue, endValue)) {\n        // Clean up end date when start date is after end date\n        if (sourceIndex === 0) {\n          values = [startValue, null];\n          endValue = null;\n        } else {\n          startValue = null;\n          values = [null, endValue];\n        } // Clean up cache since invalidate\n\n        openRecordsRef.current = _defineProperty({}, sourceIndex, true);\n      } else if (picker !== 'time' || order !== false) {\n        // Reorder when in same date\n        values = reorderValues(values, generateConfig);\n      }\n    }\n    setSelectedValue(values);\n    var startStr = values && values[0] ? formatValue(values[0], {\n      generateConfig: generateConfig,\n      locale: locale,\n      format: formatList[0]\n    }) : '';\n    var endStr = values && values[1] ? formatValue(values[1], {\n      generateConfig: generateConfig,\n      locale: locale,\n      format: formatList[0]\n    }) : '';\n    if (onCalendarChange) {\n      var info = {\n        range: sourceIndex === 0 ? 'start' : 'end'\n      };\n      onCalendarChange(values, [startStr, endStr], info);\n    } // >>>>> Trigger `onChange` event\n\n    var canStartValueTrigger = canValueTrigger(startValue, 0, mergedDisabled, allowEmpty);\n    var canEndValueTrigger = canValueTrigger(endValue, 1, mergedDisabled, allowEmpty);\n    var canTrigger = values === null || canStartValueTrigger && canEndValueTrigger;\n    if (canTrigger) {\n      // Trigger onChange only when value is validate\n      setInnerValue(values);\n      if (onChange && (!isEqual(generateConfig, getValue(mergedValue, 0), startValue) || !isEqual(generateConfig, getValue(mergedValue, 1), endValue))) {\n        onChange(values, [startStr, endStr]);\n      }\n    } // >>>>> Open picker when\n    // Always open another picker if possible\n\n    var nextOpenIndex = null;\n    if (sourceIndex === 0 && !mergedDisabled[1]) {\n      nextOpenIndex = 1;\n    } else if (sourceIndex === 1 && !mergedDisabled[0]) {\n      nextOpenIndex = 0;\n    }\n    if (nextOpenIndex !== null && nextOpenIndex !== mergedActivePickerIndex && (!openRecordsRef.current[nextOpenIndex] || !getValue(values, nextOpenIndex)) && getValue(values, sourceIndex)) {\n      // Delay to focus to avoid input blur trigger expired selectedValues\n      triggerOpenAndFocus(nextOpenIndex);\n    } else {\n      _triggerOpen(false, sourceIndex);\n    }\n  }\n  var forwardKeyDown = function forwardKeyDown(e) {\n    if (mergedOpen && operationRef.current && operationRef.current.onKeyDown) {\n      // Let popup panel handle keyboard\n      return operationRef.current.onKeyDown(e);\n    }\n    /* istanbul ignore next */\n\n    /* eslint-disable no-lone-blocks */\n\n    {\n      warning(false, 'Picker not correct forward KeyDown operation. Please help to fire issue about this.');\n      return false;\n    }\n  }; // ============================= Text ==============================\n\n  var sharedTextHooksProps = {\n    formatList: formatList,\n    generateConfig: generateConfig,\n    locale: locale\n  };\n  var _useValueTexts = useValueTexts(getValue(selectedValue, 0), sharedTextHooksProps),\n    _useValueTexts2 = _slicedToArray(_useValueTexts, 2),\n    startValueTexts = _useValueTexts2[0],\n    firstStartValueText = _useValueTexts2[1];\n  var _useValueTexts3 = useValueTexts(getValue(selectedValue, 1), sharedTextHooksProps),\n    _useValueTexts4 = _slicedToArray(_useValueTexts3, 2),\n    endValueTexts = _useValueTexts4[0],\n    firstEndValueText = _useValueTexts4[1];\n  var _onTextChange = function onTextChange(newText, index) {\n    var inputDate = parseValue(newText, {\n      locale: locale,\n      formatList: formatList,\n      generateConfig: generateConfig\n    });\n    var disabledFunc = index === 0 ? disabledStartDate : disabledEndDate;\n    if (inputDate && !disabledFunc(inputDate)) {\n      setSelectedValue(updateValues(selectedValue, inputDate, index));\n      setViewDate(inputDate, index);\n    }\n  };\n  var _useTextValueMapping = useTextValueMapping({\n      valueTexts: startValueTexts,\n      onTextChange: function onTextChange(newText) {\n        return _onTextChange(newText, 0);\n      }\n    }),\n    _useTextValueMapping2 = _slicedToArray(_useTextValueMapping, 3),\n    startText = _useTextValueMapping2[0],\n    triggerStartTextChange = _useTextValueMapping2[1],\n    resetStartText = _useTextValueMapping2[2];\n  var _useTextValueMapping3 = useTextValueMapping({\n      valueTexts: endValueTexts,\n      onTextChange: function onTextChange(newText) {\n        return _onTextChange(newText, 1);\n      }\n    }),\n    _useTextValueMapping4 = _slicedToArray(_useTextValueMapping3, 3),\n    endText = _useTextValueMapping4[0],\n    triggerEndTextChange = _useTextValueMapping4[1],\n    resetEndText = _useTextValueMapping4[2];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    rangeHoverValue = _useState4[0],\n    setRangeHoverValue = _useState4[1]; // ========================== Hover Range ==========================\n\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    hoverRangedValue = _useState6[0],\n    setHoverRangedValue = _useState6[1];\n  var _useHoverValue = useHoverValue(startText, {\n      formatList: formatList,\n      generateConfig: generateConfig,\n      locale: locale\n    }),\n    _useHoverValue2 = _slicedToArray(_useHoverValue, 3),\n    startHoverValue = _useHoverValue2[0],\n    onStartEnter = _useHoverValue2[1],\n    onStartLeave = _useHoverValue2[2];\n  var _useHoverValue3 = useHoverValue(endText, {\n      formatList: formatList,\n      generateConfig: generateConfig,\n      locale: locale\n    }),\n    _useHoverValue4 = _slicedToArray(_useHoverValue3, 3),\n    endHoverValue = _useHoverValue4[0],\n    onEndEnter = _useHoverValue4[1],\n    onEndLeave = _useHoverValue4[2];\n  var onDateMouseEnter = function onDateMouseEnter(date) {\n    setHoverRangedValue(updateValues(selectedValue, date, mergedActivePickerIndex));\n    if (mergedActivePickerIndex === 0) {\n      onStartEnter(date);\n    } else {\n      onEndEnter(date);\n    }\n  };\n  var onDateMouseLeave = function onDateMouseLeave() {\n    setHoverRangedValue(updateValues(selectedValue, null, mergedActivePickerIndex));\n    if (mergedActivePickerIndex === 0) {\n      onStartLeave();\n    } else {\n      onEndLeave();\n    }\n  }; // ============================= Input =============================\n\n  var getSharedInputHookProps = function getSharedInputHookProps(index, resetText) {\n    return {\n      blurToCancel: needConfirmButton,\n      forwardKeyDown: forwardKeyDown,\n      onBlur: onBlur,\n      isClickOutside: function isClickOutside(target) {\n        return !elementsContains([panelDivRef.current, startInputDivRef.current, endInputDivRef.current, containerRef.current], target);\n      },\n      onFocus: function onFocus(e) {\n        setMergedActivePickerIndex(index);\n        if (_onFocus) {\n          _onFocus(e);\n        }\n      },\n      triggerOpen: function triggerOpen(newOpen) {\n        _triggerOpen(newOpen, index);\n      },\n      onSubmit: function onSubmit() {\n        if (\n        // When user typing disabledDate with keyboard and enter, this value will be empty\n        !selectedValue ||\n        // Normal disabled check\n        disabledDate && disabledDate(selectedValue[index])) {\n          return false;\n        }\n        triggerChange(selectedValue, index);\n        resetText();\n      },\n      onCancel: function onCancel() {\n        _triggerOpen(false, index);\n        setSelectedValue(mergedValue);\n        resetText();\n      }\n    };\n  };\n  var _usePickerInput = usePickerInput(_objectSpread(_objectSpread({}, getSharedInputHookProps(0, resetStartText)), {}, {\n      open: startOpen,\n      value: startText,\n      onKeyDown: function onKeyDown(e, preventDefault) {\n        _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);\n      }\n    })),\n    _usePickerInput2 = _slicedToArray(_usePickerInput, 2),\n    startInputProps = _usePickerInput2[0],\n    _usePickerInput2$ = _usePickerInput2[1],\n    startFocused = _usePickerInput2$.focused,\n    startTyping = _usePickerInput2$.typing;\n  var _usePickerInput3 = usePickerInput(_objectSpread(_objectSpread({}, getSharedInputHookProps(1, resetEndText)), {}, {\n      open: endOpen,\n      value: endText,\n      onKeyDown: function onKeyDown(e, preventDefault) {\n        _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);\n      }\n    })),\n    _usePickerInput4 = _slicedToArray(_usePickerInput3, 2),\n    endInputProps = _usePickerInput4[0],\n    _usePickerInput4$ = _usePickerInput4[1],\n    endFocused = _usePickerInput4$.focused,\n    endTyping = _usePickerInput4$.typing; // ========================== Click Picker ==========================\n\n  var onPickerClick = function onPickerClick(e) {\n    // When click inside the picker & outside the picker's input elements\n    // the panel should still be opened\n    if (onClick) {\n      onClick(e);\n    }\n    if (!mergedOpen && !startInputRef.current.contains(e.target) && !endInputRef.current.contains(e.target)) {\n      if (!mergedDisabled[0]) {\n        triggerOpenAndFocus(0);\n      } else if (!mergedDisabled[1]) {\n        triggerOpenAndFocus(1);\n      }\n    }\n  };\n  var onPickerMouseDown = function onPickerMouseDown(e) {\n    // shouldn't affect input elements if picker is active\n    if (onMouseDown) {\n      onMouseDown(e);\n    }\n    if (mergedOpen && (startFocused || endFocused) && !startInputRef.current.contains(e.target) && !endInputRef.current.contains(e.target)) {\n      e.preventDefault();\n    }\n  }; // ============================= Sync ==============================\n  // Close should sync back with text value\n\n  var startStr = mergedValue && mergedValue[0] ? formatValue(mergedValue[0], {\n    locale: locale,\n    format: 'YYYYMMDDHHmmss',\n    generateConfig: generateConfig\n  }) : '';\n  var endStr = mergedValue && mergedValue[1] ? formatValue(mergedValue[1], {\n    locale: locale,\n    format: 'YYYYMMDDHHmmss',\n    generateConfig: generateConfig\n  }) : '';\n  useEffect(function () {\n    if (!mergedOpen) {\n      setSelectedValue(mergedValue);\n      if (!startValueTexts.length || startValueTexts[0] === '') {\n        triggerStartTextChange('');\n      } else if (firstStartValueText !== startText) {\n        resetStartText();\n      }\n      if (!endValueTexts.length || endValueTexts[0] === '') {\n        triggerEndTextChange('');\n      } else if (firstEndValueText !== endText) {\n        resetEndText();\n      }\n    }\n  }, [mergedOpen, startValueTexts, endValueTexts]); // Sync innerValue with control mode\n\n  useEffect(function () {\n    setSelectedValue(mergedValue);\n  }, [startStr, endStr]); // ============================ Warning ============================\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (value && Array.isArray(disabled) && (getValue(disabled, 0) && !getValue(value, 0) || getValue(disabled, 1) && !getValue(value, 1))) {\n      warning(false, '`disabled` should not set with empty `value`. You should set `allowEmpty` or `value` instead.');\n    }\n  } // ============================ Private ============================\n\n  if (pickerRef) {\n    pickerRef.current = {\n      focus: function focus() {\n        if (startInputRef.current) {\n          startInputRef.current.focus();\n        }\n      },\n      blur: function blur() {\n        if (startInputRef.current) {\n          startInputRef.current.blur();\n        }\n        if (endInputRef.current) {\n          endInputRef.current.blur();\n        }\n      }\n    };\n  } // ============================ Ranges =============================\n\n  var rangeLabels = Object.keys(ranges || {});\n  var rangeList = rangeLabels.map(function (label) {\n    var range = ranges[label];\n    var newValues = typeof range === 'function' ? range() : range;\n    return {\n      label: label,\n      onClick: function onClick() {\n        triggerChange(newValues, null);\n        _triggerOpen(false, mergedActivePickerIndex);\n      },\n      onMouseEnter: function onMouseEnter() {\n        setRangeHoverValue(newValues);\n      },\n      onMouseLeave: function onMouseLeave() {\n        setRangeHoverValue(null);\n      }\n    };\n  }); // ============================= Panel =============================\n\n  function renderPanel() {\n    var panelPosition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var panelProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var panelHoverRangedValue = null;\n    if (mergedOpen && hoverRangedValue && hoverRangedValue[0] && hoverRangedValue[1] && generateConfig.isAfter(hoverRangedValue[1], hoverRangedValue[0])) {\n      panelHoverRangedValue = hoverRangedValue;\n    }\n    var panelShowTime = showTime;\n    if (showTime && _typeof(showTime) === 'object' && showTime.defaultValue) {\n      var timeDefaultValues = showTime.defaultValue;\n      panelShowTime = _objectSpread(_objectSpread({}, showTime), {}, {\n        defaultValue: getValue(timeDefaultValues, mergedActivePickerIndex) || undefined\n      });\n    }\n    var panelDateRender = null;\n    if (dateRender) {\n      panelDateRender = function panelDateRender(date, today) {\n        return dateRender(date, today, {\n          range: mergedActivePickerIndex ? 'end' : 'start'\n        });\n      };\n    }\n    return /*#__PURE__*/React.createElement(RangeContext.Provider, {\n      value: {\n        inRange: true,\n        panelPosition: panelPosition,\n        rangedValue: rangeHoverValue || selectedValue,\n        hoverRangedValue: panelHoverRangedValue\n      }\n    }, /*#__PURE__*/React.createElement(PickerPanel, _extends({}, props, panelProps, {\n      dateRender: panelDateRender,\n      showTime: panelShowTime,\n      mode: mergedModes[mergedActivePickerIndex],\n      generateConfig: generateConfig,\n      style: undefined,\n      direction: direction,\n      disabledDate: mergedActivePickerIndex === 0 ? disabledStartDate : disabledEndDate,\n      disabledTime: function disabledTime(date) {\n        if (_disabledTime) {\n          return _disabledTime(date, mergedActivePickerIndex === 0 ? 'start' : 'end');\n        }\n        return false;\n      },\n      className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-panel-focused\"), mergedActivePickerIndex === 0 ? !startTyping : !endTyping)),\n      value: getValue(selectedValue, mergedActivePickerIndex),\n      locale: locale,\n      tabIndex: -1,\n      onPanelChange: function onPanelChange(date, newMode) {\n        // clear hover value when panel change\n        if (mergedActivePickerIndex === 0) {\n          onStartLeave(true);\n        }\n        if (mergedActivePickerIndex === 1) {\n          onEndLeave(true);\n        }\n        triggerModesChange(updateValues(mergedModes, newMode, mergedActivePickerIndex), updateValues(selectedValue, date, mergedActivePickerIndex));\n        var viewDate = date;\n        if (panelPosition === 'right' && mergedModes[mergedActivePickerIndex] === newMode) {\n          viewDate = getClosingViewDate(viewDate, newMode, generateConfig, -1);\n        }\n        setViewDate(viewDate, mergedActivePickerIndex);\n      },\n      onOk: null,\n      onSelect: undefined,\n      onChange: undefined,\n      defaultValue: mergedActivePickerIndex === 0 ? getValue(selectedValue, 1) : getValue(selectedValue, 0)\n    })));\n  }\n  var arrowLeft = 0;\n  var panelLeft = 0;\n  if (mergedActivePickerIndex && startInputDivRef.current && separatorRef.current && panelDivRef.current) {\n    // Arrow offset\n    arrowLeft = startInputDivRef.current.offsetWidth + separatorRef.current.offsetWidth; // If panelWidth - arrowWidth - arrowMarginLeft < arrowLeft, panel should move to right side.\n    // If offsetLeft > arrowLeft, arrow position is absolutely right, because arrowLeft is not calculated with arrow margin.\n\n    if (panelDivRef.current.offsetWidth && arrowRef.current.offsetWidth && arrowLeft > panelDivRef.current.offsetWidth - arrowRef.current.offsetWidth - (direction === 'rtl' || arrowRef.current.offsetLeft > arrowLeft ? 0 : arrowRef.current.offsetLeft)) {\n      panelLeft = arrowLeft;\n    }\n  }\n  var arrowPositionStyle = direction === 'rtl' ? {\n    right: arrowLeft\n  } : {\n    left: arrowLeft\n  };\n  function renderPanels() {\n    var panels;\n    var extraNode = getExtraFooter(prefixCls, mergedModes[mergedActivePickerIndex], renderExtraFooter);\n    var rangesNode = getRanges({\n      prefixCls: prefixCls,\n      components: components,\n      needConfirmButton: needConfirmButton,\n      okDisabled: !getValue(selectedValue, mergedActivePickerIndex) || disabledDate && disabledDate(selectedValue[mergedActivePickerIndex]),\n      locale: locale,\n      rangeList: rangeList,\n      onOk: function onOk() {\n        if (getValue(selectedValue, mergedActivePickerIndex)) {\n          // triggerChangeOld(selectedValue);\n          triggerChange(selectedValue, mergedActivePickerIndex);\n          if (_onOk) {\n            _onOk(selectedValue);\n          }\n        }\n      }\n    });\n    if (picker !== 'time' && !showTime) {\n      var viewDate = getViewDate(mergedActivePickerIndex);\n      var nextViewDate = getClosingViewDate(viewDate, picker, generateConfig);\n      var currentMode = mergedModes[mergedActivePickerIndex];\n      var showDoublePanel = currentMode === picker;\n      var leftPanel = renderPanel(showDoublePanel ? 'left' : false, {\n        pickerValue: viewDate,\n        onPickerValueChange: function onPickerValueChange(newViewDate) {\n          setViewDate(newViewDate, mergedActivePickerIndex);\n        }\n      });\n      var rightPanel = renderPanel('right', {\n        pickerValue: nextViewDate,\n        onPickerValueChange: function onPickerValueChange(newViewDate) {\n          setViewDate(getClosingViewDate(newViewDate, picker, generateConfig, -1), mergedActivePickerIndex);\n        }\n      });\n      if (direction === 'rtl') {\n        panels = /*#__PURE__*/React.createElement(React.Fragment, null, rightPanel, showDoublePanel && leftPanel);\n      } else {\n        panels = /*#__PURE__*/React.createElement(React.Fragment, null, leftPanel, showDoublePanel && rightPanel);\n      }\n    } else {\n      panels = renderPanel();\n    }\n    var mergedNodes = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-panels\")\n    }, panels), (extraNode || rangesNode) && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, extraNode, rangesNode));\n    if (panelRender) {\n      mergedNodes = panelRender(mergedNodes);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-panel-container\"),\n      style: {\n        marginLeft: panelLeft\n      },\n      ref: panelDivRef,\n      onMouseDown: function onMouseDown(e) {\n        e.preventDefault();\n      }\n    }, mergedNodes);\n  }\n  var rangePanel = /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-range-wrapper\"), \"\".concat(prefixCls, \"-\").concat(picker, \"-range-wrapper\")),\n    style: {\n      minWidth: popupMinWidth\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: arrowRef,\n    className: \"\".concat(prefixCls, \"-range-arrow\"),\n    style: arrowPositionStyle\n  }), renderPanels()); // ============================= Icons =============================\n\n  var suffixNode;\n  if (suffixIcon) {\n    suffixNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-suffix\")\n    }, suffixIcon);\n  }\n  var clearNode;\n  if (allowClear && (getValue(mergedValue, 0) && !mergedDisabled[0] || getValue(mergedValue, 1) && !mergedDisabled[1])) {\n    clearNode = /*#__PURE__*/React.createElement(\"span\", {\n      onMouseDown: function onMouseDown(e) {\n        e.preventDefault();\n        e.stopPropagation();\n      },\n      onMouseUp: function onMouseUp(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        var values = mergedValue;\n        if (!mergedDisabled[0]) {\n          values = updateValues(values, null, 0);\n        }\n        if (!mergedDisabled[1]) {\n          values = updateValues(values, null, 1);\n        }\n        triggerChange(values, null);\n        _triggerOpen(false, mergedActivePickerIndex);\n      },\n      className: \"\".concat(prefixCls, \"-clear\")\n    }, clearIcon || /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-clear-btn\")\n    }));\n  }\n  var inputSharedProps = {\n    size: getInputSize(picker, formatList[0], generateConfig)\n  };\n  var activeBarLeft = 0;\n  var activeBarWidth = 0;\n  if (startInputDivRef.current && endInputDivRef.current && separatorRef.current) {\n    if (mergedActivePickerIndex === 0) {\n      activeBarWidth = startInputDivRef.current.offsetWidth;\n    } else {\n      activeBarLeft = arrowLeft;\n      activeBarWidth = endInputDivRef.current.offsetWidth;\n    }\n  }\n  var activeBarPositionStyle = direction === 'rtl' ? {\n    right: activeBarLeft\n  } : {\n    left: activeBarLeft\n  }; // ============================ Return =============================\n\n  var onContextSelect = function onContextSelect(date, type) {\n    var values = updateValues(selectedValue, date, mergedActivePickerIndex);\n    if (type === 'submit' || type !== 'key' && !needConfirmButton) {\n      // triggerChange will also update selected values\n      triggerChange(values, mergedActivePickerIndex); // clear hover value style\n\n      if (mergedActivePickerIndex === 0) {\n        onStartLeave();\n      } else {\n        onEndLeave();\n      }\n    } else {\n      setSelectedValue(values);\n    }\n  };\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: {\n      operationRef: operationRef,\n      hideHeader: picker === 'time',\n      onDateMouseEnter: onDateMouseEnter,\n      onDateMouseLeave: onDateMouseLeave,\n      hideRanges: true,\n      onSelect: onContextSelect,\n      open: mergedOpen\n    }\n  }, /*#__PURE__*/React.createElement(PickerTrigger, {\n    visible: mergedOpen,\n    popupElement: rangePanel,\n    popupStyle: popupStyle,\n    prefixCls: prefixCls,\n    dropdownClassName: dropdownClassName,\n    dropdownAlign: dropdownAlign,\n    getPopupContainer: getPopupContainer,\n    transitionName: transitionName,\n    range: true,\n    direction: direction\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: containerRef,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-range\"), className, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), mergedDisabled[0] && mergedDisabled[1]), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-focused\"), mergedActivePickerIndex === 0 ? startFocused : endFocused), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2)),\n    style: style,\n    onClick: onPickerClick,\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onMouseDown: onPickerMouseDown,\n    onMouseUp: onMouseUp\n  }, getDataOrAriaProps(props)), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-input\"), (_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-input-active\"), mergedActivePickerIndex === 0), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-input-placeholder\"), !!startHoverValue), _classNames3)),\n    ref: startInputDivRef\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    id: id,\n    disabled: mergedDisabled[0],\n    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !startTyping,\n    value: startHoverValue || startText,\n    onChange: function onChange(e) {\n      triggerStartTextChange(e.target.value);\n    },\n    autoFocus: autoFocus,\n    placeholder: getValue(placeholder, 0) || '',\n    ref: startInputRef\n  }, startInputProps, inputSharedProps, {\n    autoComplete: autoComplete\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-range-separator\"),\n    ref: separatorRef\n  }, separator), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-input\"), (_classNames4 = {}, _defineProperty(_classNames4, \"\".concat(prefixCls, \"-input-active\"), mergedActivePickerIndex === 1), _defineProperty(_classNames4, \"\".concat(prefixCls, \"-input-placeholder\"), !!endHoverValue), _classNames4)),\n    ref: endInputDivRef\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    disabled: mergedDisabled[1],\n    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !endTyping,\n    value: endHoverValue || endText,\n    onChange: function onChange(e) {\n      triggerEndTextChange(e.target.value);\n    },\n    placeholder: getValue(placeholder, 1) || '',\n    ref: endInputRef\n  }, endInputProps, inputSharedProps, {\n    autoComplete: autoComplete\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-active-bar\"),\n    style: _objectSpread(_objectSpread({}, activeBarPositionStyle), {}, {\n      width: activeBarWidth,\n      position: 'absolute'\n    })\n  }), suffixNode, clearNode)));\n} // Wrap with class component to enable pass generic with instance method\n\nvar RangePicker = /*#__PURE__*/function (_React$Component) {\n  _inherits(RangePicker, _React$Component);\n  var _super = _createSuper(RangePicker);\n  function RangePicker() {\n    var _this;\n    _classCallCheck(this, RangePicker);\n    _this = _super.apply(this, arguments);\n    _this.pickerRef = /*#__PURE__*/React.createRef();\n    _this.focus = function () {\n      if (_this.pickerRef.current) {\n        _this.pickerRef.current.focus();\n      }\n    };\n    _this.blur = function () {\n      if (_this.pickerRef.current) {\n        _this.pickerRef.current.blur();\n      }\n    };\n    return _this;\n  }\n  _createClass(RangePicker, [{\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(InnerRangePicker, _extends({}, this.props, {\n        pickerRef: this.pickerRef\n      }));\n    }\n  }]);\n  return RangePicker;\n}(React.Component);\nexport default RangePicker;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "_extends", "_typeof", "_objectSpread", "_defineProperty", "_slicedToArray", "React", "useRef", "useEffect", "useState", "classNames", "warning", "useMergedState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePickerInput", "getDataOrAriaProps", "toArray", "getValue", "updateValues", "getDefaultFormat", "getInputSize", "elementsContains", "PanelContext", "isEqual", "getClosingViewDate", "isSameDate", "isSameWeek", "isSameQuarter", "formatValue", "parseValue", "useValueTexts", "useTextValueMapping", "RangeContext", "useRangeDisabled", "getExtraFooter", "getRanges", "useRangeViewDates", "useHoverValue", "legacyPropsWarning", "reorderValues", "values", "generateConfig", "isAfter", "canValueTrigger", "value", "index", "disabled", "allowEmpty", "InnerRangePicker", "props", "_classNames2", "_classNames3", "_classNames4", "_props$prefixCls", "prefixCls", "id", "style", "className", "popupStyle", "dropdownClassName", "transitionName", "dropdownAlign", "getPopupContainer", "locale", "placeholder", "autoFocus", "format", "_props$picker", "picker", "showTime", "use12Hours", "_props$separator", "separator", "defaultValue", "defaultPickerValue", "open", "defaultOpen", "disabledDate", "_disabledTime", "disabledTime", "dateRender", "panelRender", "ranges", "allowClear", "suffixIcon", "clearIcon", "pickerRef", "inputReadOnly", "mode", "renderExtraFooter", "onChange", "onOpenChange", "onPanelChange", "onCalendarChange", "_onFocus", "onFocus", "onBlur", "onMouseDown", "onMouseUp", "onMouseEnter", "onMouseLeave", "onClick", "_onOk", "onOk", "_onKeyDown", "onKeyDown", "components", "order", "direction", "activePickerIndex", "_props$autoComplete", "autoComplete", "needConfirmButton", "openRecordsRef", "containerRef", "panelDivRef", "startInputDivRef", "endInputDivRef", "separatorRef", "startInputRef", "endInputRef", "arrowRef", "process", "env", "NODE_ENV", "formatList", "_useMergedState", "_useMergedState2", "mergedActivePickerIndex", "setMergedActivePickerIndex", "operationRef", "mergedDisabled", "useMemo", "Array", "isArray", "_useMergedState3", "postState", "_useMergedState4", "mergedValue", "setInnerValue", "_useRangeViewDates", "defaultDates", "_useRangeViewDates2", "getViewDate", "setViewDate", "_useMergedState5", "postV<PERSON><PERSON>", "i", "getNow", "_useMergedState6", "selected<PERSON><PERSON><PERSON>", "setSelectedValue", "_useMergedState7", "_useMergedState8", "mergedModes", "setInnerModes", "triggerModesChange", "modes", "_useRangeDisabled", "current", "_useRangeDisabled2", "disabledStartDate", "disabledEndDate", "_useMergedState9", "postOpen", "newOpen", "onClose", "_useMergedState10", "mergedOpen", "triggerInnerOpen", "startOpen", "endOpen", "_useState", "_useState2", "popup<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON>op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "triggerRef", "_triggerOpen", "clearTimeout", "openRecords", "setTimeout", "triggerOpenAndFocus", "inputRef", "focus", "trigger<PERSON>hange", "newValue", "sourceIndex", "startValue", "endValue", "startStr", "endStr", "info", "range", "canStartValueTrigger", "canEndValueTrigger", "canTrigger", "nextOpenIndex", "forwardKeyDown", "e", "sharedTextHooksProps", "_useValueTexts", "_useValueTexts2", "startValueTexts", "firstStartValueText", "_useValueTexts3", "_useValueTexts4", "endValueTexts", "firstEndValueText", "_onTextChange", "onTextChange", "newText", "inputDate", "disabledFunc", "_useTextValueMapping", "valueTexts", "_useTextValueMapping2", "startText", "triggerStartTextChange", "resetStartText", "_useTextValueMapping3", "_useTextValueMapping4", "endText", "triggerEndTextChange", "resetEndText", "_useState3", "_useState4", "rangeHoverValue", "setRangeHoverValue", "_useState5", "_useState6", "hoverRangedValue", "setHoverRangedValue", "_useHoverValue", "_useHoverValue2", "startHoverValue", "onStartEnter", "onStartLeave", "_useHoverValue3", "_useHoverValue4", "endHoverValue", "onEndEnter", "onEndLeave", "onDateMouseEnter", "date", "onDateMouseLeave", "getSharedInputHookProps", "resetText", "blurToCancel", "isClickOutside", "target", "triggerOpen", "onSubmit", "onCancel", "_usePickerInput", "preventDefault", "_usePickerInput2", "startInputProps", "_usePickerInput2$", "startFocused", "focused", "startTyping", "typing", "_usePickerInput3", "_usePickerInput4", "endInputProps", "_usePickerInput4$", "endFocused", "endTyping", "onPickerClick", "contains", "onPickerMouseDown", "length", "blur", "rangeLabels", "Object", "keys", "rangeList", "map", "label", "newValues", "renderPanel", "panelPosition", "arguments", "undefined", "panelProps", "panelHoverRangedValue", "panelShowTime", "timeDefaultValues", "panelDateRender", "today", "createElement", "Provider", "inRange", "rangedValue", "concat", "tabIndex", "newMode", "viewDate", "onSelect", "arrowLeft", "panelLeft", "offsetLeft", "arrowPositionStyle", "right", "left", "renderPanels", "panels", "extraNode", "rangesNode", "okDisabled", "nextViewDate", "currentMode", "showDoublePanel", "leftPanel", "picker<PERSON><PERSON><PERSON>", "onPickerValueChange", "newViewDate", "rightPanel", "Fragment", "mergedNodes", "marginLeft", "ref", "rangePanel", "min<PERSON><PERSON><PERSON>", "suffixNode", "clearNode", "stopPropagation", "inputSharedProps", "size", "activeBarLeft", "activeBarWidth", "activeBarPositionStyle", "onContextSelect", "type", "<PERSON><PERSON>ead<PERSON>", "hide<PERSON><PERSON><PERSON>", "visible", "popupElement", "readOnly", "width", "position", "RangePicker", "_React$Component", "_super", "_this", "apply", "createRef", "key", "render", "Component"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-picker/es/RangePicker.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport warning from \"rc-util/es/warning\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport PickerTrigger from './PickerTrigger';\nimport PickerPanel from './PickerPanel';\nimport usePickerInput from './hooks/usePickerInput';\nimport getDataOrAriaProps, { toArray, getValue, updateValues } from './utils/miscUtil';\nimport { getDefaultFormat, getInputSize, elementsContains } from './utils/uiUtil';\nimport PanelContext from './PanelContext';\nimport { isEqual, getClosingViewDate, isSameDate, isSameWeek, isSameQuarter, formatValue, parseValue } from './utils/dateUtil';\nimport useValueTexts from './hooks/useValueTexts';\nimport useTextValueMapping from './hooks/useTextValueMapping';\nimport RangeContext from './RangeContext';\nimport useRangeDisabled from './hooks/useRangeDisabled';\nimport getExtraFooter from './utils/getExtraFooter';\nimport getRanges from './utils/getRanges';\nimport useRangeViewDates from './hooks/useRangeViewDates';\nimport useHoverValue from './hooks/useHoverValue';\nimport { legacyPropsWarning } from './utils/warnUtil';\n\nfunction reorderValues(values, generateConfig) {\n  if (values && values[0] && values[1] && generateConfig.isAfter(values[0], values[1])) {\n    return [values[1], values[0]];\n  }\n\n  return values;\n}\n\nfunction canValueTrigger(value, index, disabled, allowEmpty) {\n  if (value) {\n    return true;\n  }\n\n  if (allowEmpty && allowEmpty[index]) {\n    return true;\n  }\n\n  if (disabled[(index + 1) % 2]) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction InnerRangePicker(props) {\n  var _classNames2, _classNames3, _classNames4;\n\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-picker' : _props$prefixCls,\n      id = props.id,\n      style = props.style,\n      className = props.className,\n      popupStyle = props.popupStyle,\n      dropdownClassName = props.dropdownClassName,\n      transitionName = props.transitionName,\n      dropdownAlign = props.dropdownAlign,\n      getPopupContainer = props.getPopupContainer,\n      generateConfig = props.generateConfig,\n      locale = props.locale,\n      placeholder = props.placeholder,\n      autoFocus = props.autoFocus,\n      disabled = props.disabled,\n      format = props.format,\n      _props$picker = props.picker,\n      picker = _props$picker === void 0 ? 'date' : _props$picker,\n      showTime = props.showTime,\n      use12Hours = props.use12Hours,\n      _props$separator = props.separator,\n      separator = _props$separator === void 0 ? '~' : _props$separator,\n      value = props.value,\n      defaultValue = props.defaultValue,\n      defaultPickerValue = props.defaultPickerValue,\n      open = props.open,\n      defaultOpen = props.defaultOpen,\n      disabledDate = props.disabledDate,\n      _disabledTime = props.disabledTime,\n      dateRender = props.dateRender,\n      panelRender = props.panelRender,\n      ranges = props.ranges,\n      allowEmpty = props.allowEmpty,\n      allowClear = props.allowClear,\n      suffixIcon = props.suffixIcon,\n      clearIcon = props.clearIcon,\n      pickerRef = props.pickerRef,\n      inputReadOnly = props.inputReadOnly,\n      mode = props.mode,\n      renderExtraFooter = props.renderExtraFooter,\n      onChange = props.onChange,\n      onOpenChange = props.onOpenChange,\n      onPanelChange = props.onPanelChange,\n      onCalendarChange = props.onCalendarChange,\n      _onFocus = props.onFocus,\n      onBlur = props.onBlur,\n      onMouseDown = props.onMouseDown,\n      onMouseUp = props.onMouseUp,\n      onMouseEnter = props.onMouseEnter,\n      onMouseLeave = props.onMouseLeave,\n      onClick = props.onClick,\n      _onOk = props.onOk,\n      _onKeyDown = props.onKeyDown,\n      components = props.components,\n      order = props.order,\n      direction = props.direction,\n      activePickerIndex = props.activePickerIndex,\n      _props$autoComplete = props.autoComplete,\n      autoComplete = _props$autoComplete === void 0 ? 'off' : _props$autoComplete;\n  var needConfirmButton = picker === 'date' && !!showTime || picker === 'time'; // We record opened status here in case repeat open with picker\n\n  var openRecordsRef = useRef({});\n  var containerRef = useRef(null);\n  var panelDivRef = useRef(null);\n  var startInputDivRef = useRef(null);\n  var endInputDivRef = useRef(null);\n  var separatorRef = useRef(null);\n  var startInputRef = useRef(null);\n  var endInputRef = useRef(null);\n  var arrowRef = useRef(null); // ============================ Warning ============================\n\n  if (process.env.NODE_ENV !== 'production') {\n    legacyPropsWarning(props);\n  } // ============================= Misc ==============================\n\n\n  var formatList = toArray(getDefaultFormat(format, picker, showTime, use12Hours)); // Active picker\n\n  var _useMergedState = useMergedState(0, {\n    value: activePickerIndex\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedActivePickerIndex = _useMergedState2[0],\n      setMergedActivePickerIndex = _useMergedState2[1]; // Operation ref\n\n\n  var operationRef = useRef(null);\n  var mergedDisabled = React.useMemo(function () {\n    if (Array.isArray(disabled)) {\n      return disabled;\n    }\n\n    return [disabled || false, disabled || false];\n  }, [disabled]); // ============================= Value =============================\n\n  var _useMergedState3 = useMergedState(null, {\n    value: value,\n    defaultValue: defaultValue,\n    postState: function postState(values) {\n      return picker === 'time' && !order ? values : reorderValues(values, generateConfig);\n    }\n  }),\n      _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n      mergedValue = _useMergedState4[0],\n      setInnerValue = _useMergedState4[1]; // =========================== View Date ===========================\n  // Config view panel\n\n\n  var _useRangeViewDates = useRangeViewDates({\n    values: mergedValue,\n    picker: picker,\n    defaultDates: defaultPickerValue,\n    generateConfig: generateConfig\n  }),\n      _useRangeViewDates2 = _slicedToArray(_useRangeViewDates, 2),\n      getViewDate = _useRangeViewDates2[0],\n      setViewDate = _useRangeViewDates2[1]; // ========================= Select Values =========================\n\n\n  var _useMergedState5 = useMergedState(mergedValue, {\n    postState: function postState(values) {\n      var postValues = values;\n\n      if (mergedDisabled[0] && mergedDisabled[1]) {\n        return postValues;\n      } // Fill disabled unit\n\n\n      for (var i = 0; i < 2; i += 1) {\n        if (mergedDisabled[i] && !getValue(postValues, i) && !getValue(allowEmpty, i)) {\n          postValues = updateValues(postValues, generateConfig.getNow(), i);\n        }\n      }\n\n      return postValues;\n    }\n  }),\n      _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n      selectedValue = _useMergedState6[0],\n      setSelectedValue = _useMergedState6[1]; // ============================= Modes =============================\n\n\n  var _useMergedState7 = useMergedState([picker, picker], {\n    value: mode\n  }),\n      _useMergedState8 = _slicedToArray(_useMergedState7, 2),\n      mergedModes = _useMergedState8[0],\n      setInnerModes = _useMergedState8[1];\n\n  useEffect(function () {\n    setInnerModes([picker, picker]);\n  }, [picker]);\n\n  var triggerModesChange = function triggerModesChange(modes, values) {\n    setInnerModes(modes);\n\n    if (onPanelChange) {\n      onPanelChange(values, modes);\n    }\n  }; // ========================= Disable Date ==========================\n\n\n  var _useRangeDisabled = useRangeDisabled({\n    picker: picker,\n    selectedValue: selectedValue,\n    locale: locale,\n    disabled: mergedDisabled,\n    disabledDate: disabledDate,\n    generateConfig: generateConfig\n  }, openRecordsRef.current[1], openRecordsRef.current[0]),\n      _useRangeDisabled2 = _slicedToArray(_useRangeDisabled, 2),\n      disabledStartDate = _useRangeDisabled2[0],\n      disabledEndDate = _useRangeDisabled2[1]; // ============================= Open ==============================\n\n\n  var _useMergedState9 = useMergedState(false, {\n    value: open,\n    defaultValue: defaultOpen,\n    postState: function postState(postOpen) {\n      return mergedDisabled[mergedActivePickerIndex] ? false : postOpen;\n    },\n    onChange: function onChange(newOpen) {\n      if (onOpenChange) {\n        onOpenChange(newOpen);\n      }\n\n      if (!newOpen && operationRef.current && operationRef.current.onClose) {\n        operationRef.current.onClose();\n      }\n    }\n  }),\n      _useMergedState10 = _slicedToArray(_useMergedState9, 2),\n      mergedOpen = _useMergedState10[0],\n      triggerInnerOpen = _useMergedState10[1];\n\n  var startOpen = mergedOpen && mergedActivePickerIndex === 0;\n  var endOpen = mergedOpen && mergedActivePickerIndex === 1; // ============================= Popup =============================\n  // Popup min width\n\n  var _useState = useState(0),\n      _useState2 = _slicedToArray(_useState, 2),\n      popupMinWidth = _useState2[0],\n      setPopupMinWidth = _useState2[1];\n\n  useEffect(function () {\n    if (!mergedOpen && containerRef.current) {\n      setPopupMinWidth(containerRef.current.offsetWidth);\n    }\n  }, [mergedOpen]); // ============================ Trigger ============================\n\n  var triggerRef = React.useRef();\n\n  function _triggerOpen(newOpen, index) {\n    if (newOpen) {\n      clearTimeout(triggerRef.current);\n      openRecordsRef.current[index] = true;\n      setMergedActivePickerIndex(index);\n      triggerInnerOpen(newOpen); // Open to reset view date\n\n      if (!mergedOpen) {\n        setViewDate(null, index);\n      }\n    } else if (mergedActivePickerIndex === index) {\n      triggerInnerOpen(newOpen); // Clean up async\n      // This makes ref not quick refresh in case user open another input with blur trigger\n\n      var openRecords = openRecordsRef.current;\n      triggerRef.current = setTimeout(function () {\n        if (openRecords === openRecordsRef.current) {\n          openRecordsRef.current = {};\n        }\n      });\n    }\n  }\n\n  function triggerOpenAndFocus(index) {\n    _triggerOpen(true, index); // Use setTimeout to make sure panel DOM exists\n\n\n    setTimeout(function () {\n      var inputRef = [startInputRef, endInputRef][index];\n\n      if (inputRef.current) {\n        inputRef.current.focus();\n      }\n    }, 0);\n  }\n\n  function triggerChange(newValue, sourceIndex) {\n    var values = newValue;\n    var startValue = getValue(values, 0);\n    var endValue = getValue(values, 1); // >>>>> Format start & end values\n\n    if (startValue && endValue && generateConfig.isAfter(startValue, endValue)) {\n      if ( // WeekPicker only compare week\n      picker === 'week' && !isSameWeek(generateConfig, locale.locale, startValue, endValue) || // QuotaPicker only compare week\n      picker === 'quarter' && !isSameQuarter(generateConfig, startValue, endValue) || // Other non-TimePicker compare date\n      picker !== 'week' && picker !== 'quarter' && picker !== 'time' && !isSameDate(generateConfig, startValue, endValue)) {\n        // Clean up end date when start date is after end date\n        if (sourceIndex === 0) {\n          values = [startValue, null];\n          endValue = null;\n        } else {\n          startValue = null;\n          values = [null, endValue];\n        } // Clean up cache since invalidate\n\n\n        openRecordsRef.current = _defineProperty({}, sourceIndex, true);\n      } else if (picker !== 'time' || order !== false) {\n        // Reorder when in same date\n        values = reorderValues(values, generateConfig);\n      }\n    }\n\n    setSelectedValue(values);\n    var startStr = values && values[0] ? formatValue(values[0], {\n      generateConfig: generateConfig,\n      locale: locale,\n      format: formatList[0]\n    }) : '';\n    var endStr = values && values[1] ? formatValue(values[1], {\n      generateConfig: generateConfig,\n      locale: locale,\n      format: formatList[0]\n    }) : '';\n\n    if (onCalendarChange) {\n      var info = {\n        range: sourceIndex === 0 ? 'start' : 'end'\n      };\n      onCalendarChange(values, [startStr, endStr], info);\n    } // >>>>> Trigger `onChange` event\n\n\n    var canStartValueTrigger = canValueTrigger(startValue, 0, mergedDisabled, allowEmpty);\n    var canEndValueTrigger = canValueTrigger(endValue, 1, mergedDisabled, allowEmpty);\n    var canTrigger = values === null || canStartValueTrigger && canEndValueTrigger;\n\n    if (canTrigger) {\n      // Trigger onChange only when value is validate\n      setInnerValue(values);\n\n      if (onChange && (!isEqual(generateConfig, getValue(mergedValue, 0), startValue) || !isEqual(generateConfig, getValue(mergedValue, 1), endValue))) {\n        onChange(values, [startStr, endStr]);\n      }\n    } // >>>>> Open picker when\n    // Always open another picker if possible\n\n\n    var nextOpenIndex = null;\n\n    if (sourceIndex === 0 && !mergedDisabled[1]) {\n      nextOpenIndex = 1;\n    } else if (sourceIndex === 1 && !mergedDisabled[0]) {\n      nextOpenIndex = 0;\n    }\n\n    if (nextOpenIndex !== null && nextOpenIndex !== mergedActivePickerIndex && (!openRecordsRef.current[nextOpenIndex] || !getValue(values, nextOpenIndex)) && getValue(values, sourceIndex)) {\n      // Delay to focus to avoid input blur trigger expired selectedValues\n      triggerOpenAndFocus(nextOpenIndex);\n    } else {\n      _triggerOpen(false, sourceIndex);\n    }\n  }\n\n  var forwardKeyDown = function forwardKeyDown(e) {\n    if (mergedOpen && operationRef.current && operationRef.current.onKeyDown) {\n      // Let popup panel handle keyboard\n      return operationRef.current.onKeyDown(e);\n    }\n    /* istanbul ignore next */\n\n    /* eslint-disable no-lone-blocks */\n\n\n    {\n      warning(false, 'Picker not correct forward KeyDown operation. Please help to fire issue about this.');\n      return false;\n    }\n  }; // ============================= Text ==============================\n\n\n  var sharedTextHooksProps = {\n    formatList: formatList,\n    generateConfig: generateConfig,\n    locale: locale\n  };\n\n  var _useValueTexts = useValueTexts(getValue(selectedValue, 0), sharedTextHooksProps),\n      _useValueTexts2 = _slicedToArray(_useValueTexts, 2),\n      startValueTexts = _useValueTexts2[0],\n      firstStartValueText = _useValueTexts2[1];\n\n  var _useValueTexts3 = useValueTexts(getValue(selectedValue, 1), sharedTextHooksProps),\n      _useValueTexts4 = _slicedToArray(_useValueTexts3, 2),\n      endValueTexts = _useValueTexts4[0],\n      firstEndValueText = _useValueTexts4[1];\n\n  var _onTextChange = function onTextChange(newText, index) {\n    var inputDate = parseValue(newText, {\n      locale: locale,\n      formatList: formatList,\n      generateConfig: generateConfig\n    });\n    var disabledFunc = index === 0 ? disabledStartDate : disabledEndDate;\n\n    if (inputDate && !disabledFunc(inputDate)) {\n      setSelectedValue(updateValues(selectedValue, inputDate, index));\n      setViewDate(inputDate, index);\n    }\n  };\n\n  var _useTextValueMapping = useTextValueMapping({\n    valueTexts: startValueTexts,\n    onTextChange: function onTextChange(newText) {\n      return _onTextChange(newText, 0);\n    }\n  }),\n      _useTextValueMapping2 = _slicedToArray(_useTextValueMapping, 3),\n      startText = _useTextValueMapping2[0],\n      triggerStartTextChange = _useTextValueMapping2[1],\n      resetStartText = _useTextValueMapping2[2];\n\n  var _useTextValueMapping3 = useTextValueMapping({\n    valueTexts: endValueTexts,\n    onTextChange: function onTextChange(newText) {\n      return _onTextChange(newText, 1);\n    }\n  }),\n      _useTextValueMapping4 = _slicedToArray(_useTextValueMapping3, 3),\n      endText = _useTextValueMapping4[0],\n      triggerEndTextChange = _useTextValueMapping4[1],\n      resetEndText = _useTextValueMapping4[2];\n\n  var _useState3 = useState(null),\n      _useState4 = _slicedToArray(_useState3, 2),\n      rangeHoverValue = _useState4[0],\n      setRangeHoverValue = _useState4[1]; // ========================== Hover Range ==========================\n\n\n  var _useState5 = useState(null),\n      _useState6 = _slicedToArray(_useState5, 2),\n      hoverRangedValue = _useState6[0],\n      setHoverRangedValue = _useState6[1];\n\n  var _useHoverValue = useHoverValue(startText, {\n    formatList: formatList,\n    generateConfig: generateConfig,\n    locale: locale\n  }),\n      _useHoverValue2 = _slicedToArray(_useHoverValue, 3),\n      startHoverValue = _useHoverValue2[0],\n      onStartEnter = _useHoverValue2[1],\n      onStartLeave = _useHoverValue2[2];\n\n  var _useHoverValue3 = useHoverValue(endText, {\n    formatList: formatList,\n    generateConfig: generateConfig,\n    locale: locale\n  }),\n      _useHoverValue4 = _slicedToArray(_useHoverValue3, 3),\n      endHoverValue = _useHoverValue4[0],\n      onEndEnter = _useHoverValue4[1],\n      onEndLeave = _useHoverValue4[2];\n\n  var onDateMouseEnter = function onDateMouseEnter(date) {\n    setHoverRangedValue(updateValues(selectedValue, date, mergedActivePickerIndex));\n\n    if (mergedActivePickerIndex === 0) {\n      onStartEnter(date);\n    } else {\n      onEndEnter(date);\n    }\n  };\n\n  var onDateMouseLeave = function onDateMouseLeave() {\n    setHoverRangedValue(updateValues(selectedValue, null, mergedActivePickerIndex));\n\n    if (mergedActivePickerIndex === 0) {\n      onStartLeave();\n    } else {\n      onEndLeave();\n    }\n  }; // ============================= Input =============================\n\n\n  var getSharedInputHookProps = function getSharedInputHookProps(index, resetText) {\n    return {\n      blurToCancel: needConfirmButton,\n      forwardKeyDown: forwardKeyDown,\n      onBlur: onBlur,\n      isClickOutside: function isClickOutside(target) {\n        return !elementsContains([panelDivRef.current, startInputDivRef.current, endInputDivRef.current, containerRef.current], target);\n      },\n      onFocus: function onFocus(e) {\n        setMergedActivePickerIndex(index);\n\n        if (_onFocus) {\n          _onFocus(e);\n        }\n      },\n      triggerOpen: function triggerOpen(newOpen) {\n        _triggerOpen(newOpen, index);\n      },\n      onSubmit: function onSubmit() {\n        if ( // When user typing disabledDate with keyboard and enter, this value will be empty\n        !selectedValue || // Normal disabled check\n        disabledDate && disabledDate(selectedValue[index])) {\n          return false;\n        }\n\n        triggerChange(selectedValue, index);\n        resetText();\n      },\n      onCancel: function onCancel() {\n        _triggerOpen(false, index);\n\n        setSelectedValue(mergedValue);\n        resetText();\n      }\n    };\n  };\n\n  var _usePickerInput = usePickerInput(_objectSpread(_objectSpread({}, getSharedInputHookProps(0, resetStartText)), {}, {\n    open: startOpen,\n    value: startText,\n    onKeyDown: function onKeyDown(e, preventDefault) {\n      _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);\n    }\n  })),\n      _usePickerInput2 = _slicedToArray(_usePickerInput, 2),\n      startInputProps = _usePickerInput2[0],\n      _usePickerInput2$ = _usePickerInput2[1],\n      startFocused = _usePickerInput2$.focused,\n      startTyping = _usePickerInput2$.typing;\n\n  var _usePickerInput3 = usePickerInput(_objectSpread(_objectSpread({}, getSharedInputHookProps(1, resetEndText)), {}, {\n    open: endOpen,\n    value: endText,\n    onKeyDown: function onKeyDown(e, preventDefault) {\n      _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);\n    }\n  })),\n      _usePickerInput4 = _slicedToArray(_usePickerInput3, 2),\n      endInputProps = _usePickerInput4[0],\n      _usePickerInput4$ = _usePickerInput4[1],\n      endFocused = _usePickerInput4$.focused,\n      endTyping = _usePickerInput4$.typing; // ========================== Click Picker ==========================\n\n\n  var onPickerClick = function onPickerClick(e) {\n    // When click inside the picker & outside the picker's input elements\n    // the panel should still be opened\n    if (onClick) {\n      onClick(e);\n    }\n\n    if (!mergedOpen && !startInputRef.current.contains(e.target) && !endInputRef.current.contains(e.target)) {\n      if (!mergedDisabled[0]) {\n        triggerOpenAndFocus(0);\n      } else if (!mergedDisabled[1]) {\n        triggerOpenAndFocus(1);\n      }\n    }\n  };\n\n  var onPickerMouseDown = function onPickerMouseDown(e) {\n    // shouldn't affect input elements if picker is active\n    if (onMouseDown) {\n      onMouseDown(e);\n    }\n\n    if (mergedOpen && (startFocused || endFocused) && !startInputRef.current.contains(e.target) && !endInputRef.current.contains(e.target)) {\n      e.preventDefault();\n    }\n  }; // ============================= Sync ==============================\n  // Close should sync back with text value\n\n\n  var startStr = mergedValue && mergedValue[0] ? formatValue(mergedValue[0], {\n    locale: locale,\n    format: 'YYYYMMDDHHmmss',\n    generateConfig: generateConfig\n  }) : '';\n  var endStr = mergedValue && mergedValue[1] ? formatValue(mergedValue[1], {\n    locale: locale,\n    format: 'YYYYMMDDHHmmss',\n    generateConfig: generateConfig\n  }) : '';\n  useEffect(function () {\n    if (!mergedOpen) {\n      setSelectedValue(mergedValue);\n\n      if (!startValueTexts.length || startValueTexts[0] === '') {\n        triggerStartTextChange('');\n      } else if (firstStartValueText !== startText) {\n        resetStartText();\n      }\n\n      if (!endValueTexts.length || endValueTexts[0] === '') {\n        triggerEndTextChange('');\n      } else if (firstEndValueText !== endText) {\n        resetEndText();\n      }\n    }\n  }, [mergedOpen, startValueTexts, endValueTexts]); // Sync innerValue with control mode\n\n  useEffect(function () {\n    setSelectedValue(mergedValue);\n  }, [startStr, endStr]); // ============================ Warning ============================\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (value && Array.isArray(disabled) && (getValue(disabled, 0) && !getValue(value, 0) || getValue(disabled, 1) && !getValue(value, 1))) {\n      warning(false, '`disabled` should not set with empty `value`. You should set `allowEmpty` or `value` instead.');\n    }\n  } // ============================ Private ============================\n\n\n  if (pickerRef) {\n    pickerRef.current = {\n      focus: function focus() {\n        if (startInputRef.current) {\n          startInputRef.current.focus();\n        }\n      },\n      blur: function blur() {\n        if (startInputRef.current) {\n          startInputRef.current.blur();\n        }\n\n        if (endInputRef.current) {\n          endInputRef.current.blur();\n        }\n      }\n    };\n  } // ============================ Ranges =============================\n\n\n  var rangeLabels = Object.keys(ranges || {});\n  var rangeList = rangeLabels.map(function (label) {\n    var range = ranges[label];\n    var newValues = typeof range === 'function' ? range() : range;\n    return {\n      label: label,\n      onClick: function onClick() {\n        triggerChange(newValues, null);\n\n        _triggerOpen(false, mergedActivePickerIndex);\n      },\n      onMouseEnter: function onMouseEnter() {\n        setRangeHoverValue(newValues);\n      },\n      onMouseLeave: function onMouseLeave() {\n        setRangeHoverValue(null);\n      }\n    };\n  }); // ============================= Panel =============================\n\n  function renderPanel() {\n    var panelPosition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var panelProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var panelHoverRangedValue = null;\n\n    if (mergedOpen && hoverRangedValue && hoverRangedValue[0] && hoverRangedValue[1] && generateConfig.isAfter(hoverRangedValue[1], hoverRangedValue[0])) {\n      panelHoverRangedValue = hoverRangedValue;\n    }\n\n    var panelShowTime = showTime;\n\n    if (showTime && _typeof(showTime) === 'object' && showTime.defaultValue) {\n      var timeDefaultValues = showTime.defaultValue;\n      panelShowTime = _objectSpread(_objectSpread({}, showTime), {}, {\n        defaultValue: getValue(timeDefaultValues, mergedActivePickerIndex) || undefined\n      });\n    }\n\n    var panelDateRender = null;\n\n    if (dateRender) {\n      panelDateRender = function panelDateRender(date, today) {\n        return dateRender(date, today, {\n          range: mergedActivePickerIndex ? 'end' : 'start'\n        });\n      };\n    }\n\n    return /*#__PURE__*/React.createElement(RangeContext.Provider, {\n      value: {\n        inRange: true,\n        panelPosition: panelPosition,\n        rangedValue: rangeHoverValue || selectedValue,\n        hoverRangedValue: panelHoverRangedValue\n      }\n    }, /*#__PURE__*/React.createElement(PickerPanel, _extends({}, props, panelProps, {\n      dateRender: panelDateRender,\n      showTime: panelShowTime,\n      mode: mergedModes[mergedActivePickerIndex],\n      generateConfig: generateConfig,\n      style: undefined,\n      direction: direction,\n      disabledDate: mergedActivePickerIndex === 0 ? disabledStartDate : disabledEndDate,\n      disabledTime: function disabledTime(date) {\n        if (_disabledTime) {\n          return _disabledTime(date, mergedActivePickerIndex === 0 ? 'start' : 'end');\n        }\n\n        return false;\n      },\n      className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-panel-focused\"), mergedActivePickerIndex === 0 ? !startTyping : !endTyping)),\n      value: getValue(selectedValue, mergedActivePickerIndex),\n      locale: locale,\n      tabIndex: -1,\n      onPanelChange: function onPanelChange(date, newMode) {\n        // clear hover value when panel change\n        if (mergedActivePickerIndex === 0) {\n          onStartLeave(true);\n        }\n\n        if (mergedActivePickerIndex === 1) {\n          onEndLeave(true);\n        }\n\n        triggerModesChange(updateValues(mergedModes, newMode, mergedActivePickerIndex), updateValues(selectedValue, date, mergedActivePickerIndex));\n        var viewDate = date;\n\n        if (panelPosition === 'right' && mergedModes[mergedActivePickerIndex] === newMode) {\n          viewDate = getClosingViewDate(viewDate, newMode, generateConfig, -1);\n        }\n\n        setViewDate(viewDate, mergedActivePickerIndex);\n      },\n      onOk: null,\n      onSelect: undefined,\n      onChange: undefined,\n      defaultValue: mergedActivePickerIndex === 0 ? getValue(selectedValue, 1) : getValue(selectedValue, 0)\n    })));\n  }\n\n  var arrowLeft = 0;\n  var panelLeft = 0;\n\n  if (mergedActivePickerIndex && startInputDivRef.current && separatorRef.current && panelDivRef.current) {\n    // Arrow offset\n    arrowLeft = startInputDivRef.current.offsetWidth + separatorRef.current.offsetWidth; // If panelWidth - arrowWidth - arrowMarginLeft < arrowLeft, panel should move to right side.\n    // If offsetLeft > arrowLeft, arrow position is absolutely right, because arrowLeft is not calculated with arrow margin.\n\n    if (panelDivRef.current.offsetWidth && arrowRef.current.offsetWidth && arrowLeft > panelDivRef.current.offsetWidth - arrowRef.current.offsetWidth - (direction === 'rtl' || arrowRef.current.offsetLeft > arrowLeft ? 0 : arrowRef.current.offsetLeft)) {\n      panelLeft = arrowLeft;\n    }\n  }\n\n  var arrowPositionStyle = direction === 'rtl' ? {\n    right: arrowLeft\n  } : {\n    left: arrowLeft\n  };\n\n  function renderPanels() {\n    var panels;\n    var extraNode = getExtraFooter(prefixCls, mergedModes[mergedActivePickerIndex], renderExtraFooter);\n    var rangesNode = getRanges({\n      prefixCls: prefixCls,\n      components: components,\n      needConfirmButton: needConfirmButton,\n      okDisabled: !getValue(selectedValue, mergedActivePickerIndex) || disabledDate && disabledDate(selectedValue[mergedActivePickerIndex]),\n      locale: locale,\n      rangeList: rangeList,\n      onOk: function onOk() {\n        if (getValue(selectedValue, mergedActivePickerIndex)) {\n          // triggerChangeOld(selectedValue);\n          triggerChange(selectedValue, mergedActivePickerIndex);\n\n          if (_onOk) {\n            _onOk(selectedValue);\n          }\n        }\n      }\n    });\n\n    if (picker !== 'time' && !showTime) {\n      var viewDate = getViewDate(mergedActivePickerIndex);\n      var nextViewDate = getClosingViewDate(viewDate, picker, generateConfig);\n      var currentMode = mergedModes[mergedActivePickerIndex];\n      var showDoublePanel = currentMode === picker;\n      var leftPanel = renderPanel(showDoublePanel ? 'left' : false, {\n        pickerValue: viewDate,\n        onPickerValueChange: function onPickerValueChange(newViewDate) {\n          setViewDate(newViewDate, mergedActivePickerIndex);\n        }\n      });\n      var rightPanel = renderPanel('right', {\n        pickerValue: nextViewDate,\n        onPickerValueChange: function onPickerValueChange(newViewDate) {\n          setViewDate(getClosingViewDate(newViewDate, picker, generateConfig, -1), mergedActivePickerIndex);\n        }\n      });\n\n      if (direction === 'rtl') {\n        panels = /*#__PURE__*/React.createElement(React.Fragment, null, rightPanel, showDoublePanel && leftPanel);\n      } else {\n        panels = /*#__PURE__*/React.createElement(React.Fragment, null, leftPanel, showDoublePanel && rightPanel);\n      }\n    } else {\n      panels = renderPanel();\n    }\n\n    var mergedNodes = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-panels\")\n    }, panels), (extraNode || rangesNode) && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, extraNode, rangesNode));\n\n    if (panelRender) {\n      mergedNodes = panelRender(mergedNodes);\n    }\n\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-panel-container\"),\n      style: {\n        marginLeft: panelLeft\n      },\n      ref: panelDivRef,\n      onMouseDown: function onMouseDown(e) {\n        e.preventDefault();\n      }\n    }, mergedNodes);\n  }\n\n  var rangePanel = /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-range-wrapper\"), \"\".concat(prefixCls, \"-\").concat(picker, \"-range-wrapper\")),\n    style: {\n      minWidth: popupMinWidth\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: arrowRef,\n    className: \"\".concat(prefixCls, \"-range-arrow\"),\n    style: arrowPositionStyle\n  }), renderPanels()); // ============================= Icons =============================\n\n  var suffixNode;\n\n  if (suffixIcon) {\n    suffixNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-suffix\")\n    }, suffixIcon);\n  }\n\n  var clearNode;\n\n  if (allowClear && (getValue(mergedValue, 0) && !mergedDisabled[0] || getValue(mergedValue, 1) && !mergedDisabled[1])) {\n    clearNode = /*#__PURE__*/React.createElement(\"span\", {\n      onMouseDown: function onMouseDown(e) {\n        e.preventDefault();\n        e.stopPropagation();\n      },\n      onMouseUp: function onMouseUp(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        var values = mergedValue;\n\n        if (!mergedDisabled[0]) {\n          values = updateValues(values, null, 0);\n        }\n\n        if (!mergedDisabled[1]) {\n          values = updateValues(values, null, 1);\n        }\n\n        triggerChange(values, null);\n\n        _triggerOpen(false, mergedActivePickerIndex);\n      },\n      className: \"\".concat(prefixCls, \"-clear\")\n    }, clearIcon || /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-clear-btn\")\n    }));\n  }\n\n  var inputSharedProps = {\n    size: getInputSize(picker, formatList[0], generateConfig)\n  };\n  var activeBarLeft = 0;\n  var activeBarWidth = 0;\n\n  if (startInputDivRef.current && endInputDivRef.current && separatorRef.current) {\n    if (mergedActivePickerIndex === 0) {\n      activeBarWidth = startInputDivRef.current.offsetWidth;\n    } else {\n      activeBarLeft = arrowLeft;\n      activeBarWidth = endInputDivRef.current.offsetWidth;\n    }\n  }\n\n  var activeBarPositionStyle = direction === 'rtl' ? {\n    right: activeBarLeft\n  } : {\n    left: activeBarLeft\n  }; // ============================ Return =============================\n\n  var onContextSelect = function onContextSelect(date, type) {\n    var values = updateValues(selectedValue, date, mergedActivePickerIndex);\n\n    if (type === 'submit' || type !== 'key' && !needConfirmButton) {\n      // triggerChange will also update selected values\n      triggerChange(values, mergedActivePickerIndex); // clear hover value style\n\n      if (mergedActivePickerIndex === 0) {\n        onStartLeave();\n      } else {\n        onEndLeave();\n      }\n    } else {\n      setSelectedValue(values);\n    }\n  };\n\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: {\n      operationRef: operationRef,\n      hideHeader: picker === 'time',\n      onDateMouseEnter: onDateMouseEnter,\n      onDateMouseLeave: onDateMouseLeave,\n      hideRanges: true,\n      onSelect: onContextSelect,\n      open: mergedOpen\n    }\n  }, /*#__PURE__*/React.createElement(PickerTrigger, {\n    visible: mergedOpen,\n    popupElement: rangePanel,\n    popupStyle: popupStyle,\n    prefixCls: prefixCls,\n    dropdownClassName: dropdownClassName,\n    dropdownAlign: dropdownAlign,\n    getPopupContainer: getPopupContainer,\n    transitionName: transitionName,\n    range: true,\n    direction: direction\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: containerRef,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-range\"), className, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), mergedDisabled[0] && mergedDisabled[1]), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-focused\"), mergedActivePickerIndex === 0 ? startFocused : endFocused), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2)),\n    style: style,\n    onClick: onPickerClick,\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onMouseDown: onPickerMouseDown,\n    onMouseUp: onMouseUp\n  }, getDataOrAriaProps(props)), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-input\"), (_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-input-active\"), mergedActivePickerIndex === 0), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-input-placeholder\"), !!startHoverValue), _classNames3)),\n    ref: startInputDivRef\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    id: id,\n    disabled: mergedDisabled[0],\n    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !startTyping,\n    value: startHoverValue || startText,\n    onChange: function onChange(e) {\n      triggerStartTextChange(e.target.value);\n    },\n    autoFocus: autoFocus,\n    placeholder: getValue(placeholder, 0) || '',\n    ref: startInputRef\n  }, startInputProps, inputSharedProps, {\n    autoComplete: autoComplete\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-range-separator\"),\n    ref: separatorRef\n  }, separator), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-input\"), (_classNames4 = {}, _defineProperty(_classNames4, \"\".concat(prefixCls, \"-input-active\"), mergedActivePickerIndex === 1), _defineProperty(_classNames4, \"\".concat(prefixCls, \"-input-placeholder\"), !!endHoverValue), _classNames4)),\n    ref: endInputDivRef\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    disabled: mergedDisabled[1],\n    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !endTyping,\n    value: endHoverValue || endText,\n    onChange: function onChange(e) {\n      triggerEndTextChange(e.target.value);\n    },\n    placeholder: getValue(placeholder, 1) || '',\n    ref: endInputRef\n  }, endInputProps, inputSharedProps, {\n    autoComplete: autoComplete\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-active-bar\"),\n    style: _objectSpread(_objectSpread({}, activeBarPositionStyle), {}, {\n      width: activeBarWidth,\n      position: 'absolute'\n    })\n  }), suffixNode, clearNode)));\n} // Wrap with class component to enable pass generic with instance method\n\n\nvar RangePicker = /*#__PURE__*/function (_React$Component) {\n  _inherits(RangePicker, _React$Component);\n\n  var _super = _createSuper(RangePicker);\n\n  function RangePicker() {\n    var _this;\n\n    _classCallCheck(this, RangePicker);\n\n    _this = _super.apply(this, arguments);\n    _this.pickerRef = /*#__PURE__*/React.createRef();\n\n    _this.focus = function () {\n      if (_this.pickerRef.current) {\n        _this.pickerRef.current.focus();\n      }\n    };\n\n    _this.blur = function () {\n      if (_this.pickerRef.current) {\n        _this.pickerRef.current.blur();\n      }\n    };\n\n    return _this;\n  }\n\n  _createClass(RangePicker, [{\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(InnerRangePicker, _extends({}, this.props, {\n        pickerRef: this.pickerRef\n      }));\n    }\n  }]);\n\n  return RangePicker;\n}(React.Component);\n\nexport default RangePicker;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,kBAAkB,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,kBAAkB;AACtF,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,gBAAgB;AACjF,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,QAAQ,kBAAkB;AAC9H,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,aAAaA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAC7C,IAAID,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIC,cAAc,CAACC,OAAO,CAACF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IACpF,OAAO,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B;EAEA,OAAOA,MAAM;AACf;AAEA,SAASG,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC3D,IAAIH,KAAK,EAAE;IACT,OAAO,IAAI;EACb;EAEA,IAAIG,UAAU,IAAIA,UAAU,CAACF,KAAK,CAAC,EAAE;IACnC,OAAO,IAAI;EACb;EAEA,IAAIC,QAAQ,CAAC,CAACD,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;IAC7B,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAEA,SAASG,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,IAAIC,YAAY,EAAEC,YAAY,EAAEC,YAAY;EAE5C,IAAIC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,EAAE,GAAGN,KAAK,CAACM,EAAE;IACbC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,UAAU,GAAGT,KAAK,CAACS,UAAU;IAC7BC,iBAAiB,GAAGV,KAAK,CAACU,iBAAiB;IAC3CC,cAAc,GAAGX,KAAK,CAACW,cAAc;IACrCC,aAAa,GAAGZ,KAAK,CAACY,aAAa;IACnCC,iBAAiB,GAAGb,KAAK,CAACa,iBAAiB;IAC3CrB,cAAc,GAAGQ,KAAK,CAACR,cAAc;IACrCsB,MAAM,GAAGd,KAAK,CAACc,MAAM;IACrBC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BnB,QAAQ,GAAGG,KAAK,CAACH,QAAQ;IACzBoB,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,aAAa,GAAGlB,KAAK,CAACmB,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,aAAa;IAC1DE,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,UAAU,GAAGrB,KAAK,CAACqB,UAAU;IAC7BC,gBAAgB,GAAGtB,KAAK,CAACuB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,gBAAgB;IAChE3B,KAAK,GAAGK,KAAK,CAACL,KAAK;IACnB6B,YAAY,GAAGxB,KAAK,CAACwB,YAAY;IACjCC,kBAAkB,GAAGzB,KAAK,CAACyB,kBAAkB;IAC7CC,IAAI,GAAG1B,KAAK,CAAC0B,IAAI;IACjBC,WAAW,GAAG3B,KAAK,CAAC2B,WAAW;IAC/BC,YAAY,GAAG5B,KAAK,CAAC4B,YAAY;IACjCC,aAAa,GAAG7B,KAAK,CAAC8B,YAAY;IAClCC,UAAU,GAAG/B,KAAK,CAAC+B,UAAU;IAC7BC,WAAW,GAAGhC,KAAK,CAACgC,WAAW;IAC/BC,MAAM,GAAGjC,KAAK,CAACiC,MAAM;IACrBnC,UAAU,GAAGE,KAAK,CAACF,UAAU;IAC7BoC,UAAU,GAAGlC,KAAK,CAACkC,UAAU;IAC7BC,UAAU,GAAGnC,KAAK,CAACmC,UAAU;IAC7BC,SAAS,GAAGpC,KAAK,CAACoC,SAAS;IAC3BC,SAAS,GAAGrC,KAAK,CAACqC,SAAS;IAC3BC,aAAa,GAAGtC,KAAK,CAACsC,aAAa;IACnCC,IAAI,GAAGvC,KAAK,CAACuC,IAAI;IACjBC,iBAAiB,GAAGxC,KAAK,CAACwC,iBAAiB;IAC3CC,QAAQ,GAAGzC,KAAK,CAACyC,QAAQ;IACzBC,YAAY,GAAG1C,KAAK,CAAC0C,YAAY;IACjCC,aAAa,GAAG3C,KAAK,CAAC2C,aAAa;IACnCC,gBAAgB,GAAG5C,KAAK,CAAC4C,gBAAgB;IACzCC,QAAQ,GAAG7C,KAAK,CAAC8C,OAAO;IACxBC,MAAM,GAAG/C,KAAK,CAAC+C,MAAM;IACrBC,WAAW,GAAGhD,KAAK,CAACgD,WAAW;IAC/BC,SAAS,GAAGjD,KAAK,CAACiD,SAAS;IAC3BC,YAAY,GAAGlD,KAAK,CAACkD,YAAY;IACjCC,YAAY,GAAGnD,KAAK,CAACmD,YAAY;IACjCC,OAAO,GAAGpD,KAAK,CAACoD,OAAO;IACvBC,KAAK,GAAGrD,KAAK,CAACsD,IAAI;IAClBC,UAAU,GAAGvD,KAAK,CAACwD,SAAS;IAC5BC,UAAU,GAAGzD,KAAK,CAACyD,UAAU;IAC7BC,KAAK,GAAG1D,KAAK,CAAC0D,KAAK;IACnBC,SAAS,GAAG3D,KAAK,CAAC2D,SAAS;IAC3BC,iBAAiB,GAAG5D,KAAK,CAAC4D,iBAAiB;IAC3CC,mBAAmB,GAAG7D,KAAK,CAAC8D,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;EAC/E,IAAIE,iBAAiB,GAAG5C,MAAM,KAAK,MAAM,IAAI,CAAC,CAACC,QAAQ,IAAID,MAAM,KAAK,MAAM,CAAC,CAAC;;EAE9E,IAAI6C,cAAc,GAAG3G,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAI4G,YAAY,GAAG5G,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAI6G,WAAW,GAAG7G,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAI8G,gBAAgB,GAAG9G,MAAM,CAAC,IAAI,CAAC;EACnC,IAAI+G,cAAc,GAAG/G,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIgH,YAAY,GAAGhH,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAIiH,aAAa,GAAGjH,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIkH,WAAW,GAAGlH,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAImH,QAAQ,GAAGnH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE7B,IAAIoH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCtF,kBAAkB,CAACW,KAAK,CAAC;EAC3B,CAAC,CAAC;;EAGF,IAAI4E,UAAU,GAAG7G,OAAO,CAACG,gBAAgB,CAAC+C,MAAM,EAAEE,MAAM,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElF,IAAIwD,eAAe,GAAGnH,cAAc,CAAC,CAAC,EAAE;MACtCiC,KAAK,EAAEiE;IACT,CAAC,CAAC;IACEkB,gBAAgB,GAAG3H,cAAc,CAAC0H,eAAe,EAAE,CAAC,CAAC;IACrDE,uBAAuB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7CE,0BAA0B,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGtD,IAAIG,YAAY,GAAG5H,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAI6H,cAAc,GAAG9H,KAAK,CAAC+H,OAAO,CAAC,YAAY;IAC7C,IAAIC,KAAK,CAACC,OAAO,CAACxF,QAAQ,CAAC,EAAE;MAC3B,OAAOA,QAAQ;IACjB;IAEA,OAAO,CAACA,QAAQ,IAAI,KAAK,EAAEA,QAAQ,IAAI,KAAK,CAAC;EAC/C,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAIyF,gBAAgB,GAAG5H,cAAc,CAAC,IAAI,EAAE;MAC1CiC,KAAK,EAAEA,KAAK;MACZ6B,YAAY,EAAEA,YAAY;MAC1B+D,SAAS,EAAE,SAASA,SAASA,CAAChG,MAAM,EAAE;QACpC,OAAO4B,MAAM,KAAK,MAAM,IAAI,CAACuC,KAAK,GAAGnE,MAAM,GAAGD,aAAa,CAACC,MAAM,EAAEC,cAAc,CAAC;MACrF;IACF,CAAC,CAAC;IACEgG,gBAAgB,GAAGrI,cAAc,CAACmI,gBAAgB,EAAE,CAAC,CAAC;IACtDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC;;EAGA,IAAIG,kBAAkB,GAAGxG,iBAAiB,CAAC;MACzCI,MAAM,EAAEkG,WAAW;MACnBtE,MAAM,EAAEA,MAAM;MACdyE,YAAY,EAAEnE,kBAAkB;MAChCjC,cAAc,EAAEA;IAClB,CAAC,CAAC;IACEqG,mBAAmB,GAAG1I,cAAc,CAACwI,kBAAkB,EAAE,CAAC,CAAC;IAC3DG,WAAW,GAAGD,mBAAmB,CAAC,CAAC,CAAC;IACpCE,WAAW,GAAGF,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG1C,IAAIG,gBAAgB,GAAGtI,cAAc,CAAC+H,WAAW,EAAE;MACjDF,SAAS,EAAE,SAASA,SAASA,CAAChG,MAAM,EAAE;QACpC,IAAI0G,UAAU,GAAG1G,MAAM;QAEvB,IAAI2F,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;UAC1C,OAAOe,UAAU;QACnB,CAAC,CAAC;;QAGF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAC7B,IAAIhB,cAAc,CAACgB,CAAC,CAAC,IAAI,CAAClI,QAAQ,CAACiI,UAAU,EAAEC,CAAC,CAAC,IAAI,CAAClI,QAAQ,CAAC8B,UAAU,EAAEoG,CAAC,CAAC,EAAE;YAC7ED,UAAU,GAAGhI,YAAY,CAACgI,UAAU,EAAEzG,cAAc,CAAC2G,MAAM,CAAC,CAAC,EAAED,CAAC,CAAC;UACnE;QACF;QAEA,OAAOD,UAAU;MACnB;IACF,CAAC,CAAC;IACEG,gBAAgB,GAAGjJ,cAAc,CAAC6I,gBAAgB,EAAE,CAAC,CAAC;IACtDK,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAIG,gBAAgB,GAAG7I,cAAc,CAAC,CAACyD,MAAM,EAAEA,MAAM,CAAC,EAAE;MACtDxB,KAAK,EAAE4C;IACT,CAAC,CAAC;IACEiE,gBAAgB,GAAGrJ,cAAc,CAACoJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvClJ,SAAS,CAAC,YAAY;IACpBoJ,aAAa,CAAC,CAACvF,MAAM,EAAEA,MAAM,CAAC,CAAC;EACjC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEZ,IAAIwF,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAErH,MAAM,EAAE;IAClEmH,aAAa,CAACE,KAAK,CAAC;IAEpB,IAAIjE,aAAa,EAAE;MACjBA,aAAa,CAACpD,MAAM,EAAEqH,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIC,iBAAiB,GAAG7H,gBAAgB,CAAC;MACvCmC,MAAM,EAAEA,MAAM;MACdkF,aAAa,EAAEA,aAAa;MAC5BvF,MAAM,EAAEA,MAAM;MACdjB,QAAQ,EAAEqF,cAAc;MACxBtD,YAAY,EAAEA,YAAY;MAC1BpC,cAAc,EAAEA;IAClB,CAAC,EAAEwE,cAAc,CAAC8C,OAAO,CAAC,CAAC,CAAC,EAAE9C,cAAc,CAAC8C,OAAO,CAAC,CAAC,CAAC,CAAC;IACpDC,kBAAkB,GAAG5J,cAAc,CAAC0J,iBAAiB,EAAE,CAAC,CAAC;IACzDG,iBAAiB,GAAGD,kBAAkB,CAAC,CAAC,CAAC;IACzCE,eAAe,GAAGF,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG7C,IAAIG,gBAAgB,GAAGxJ,cAAc,CAAC,KAAK,EAAE;MAC3CiC,KAAK,EAAE+B,IAAI;MACXF,YAAY,EAAEG,WAAW;MACzB4D,SAAS,EAAE,SAASA,SAASA,CAAC4B,QAAQ,EAAE;QACtC,OAAOjC,cAAc,CAACH,uBAAuB,CAAC,GAAG,KAAK,GAAGoC,QAAQ;MACnE,CAAC;MACD1E,QAAQ,EAAE,SAASA,QAAQA,CAAC2E,OAAO,EAAE;QACnC,IAAI1E,YAAY,EAAE;UAChBA,YAAY,CAAC0E,OAAO,CAAC;QACvB;QAEA,IAAI,CAACA,OAAO,IAAInC,YAAY,CAAC6B,OAAO,IAAI7B,YAAY,CAAC6B,OAAO,CAACO,OAAO,EAAE;UACpEpC,YAAY,CAAC6B,OAAO,CAACO,OAAO,CAAC,CAAC;QAChC;MACF;IACF,CAAC,CAAC;IACEC,iBAAiB,GAAGnK,cAAc,CAAC+J,gBAAgB,EAAE,CAAC,CAAC;IACvDK,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE3C,IAAIG,SAAS,GAAGF,UAAU,IAAIxC,uBAAuB,KAAK,CAAC;EAC3D,IAAI2C,OAAO,GAAGH,UAAU,IAAIxC,uBAAuB,KAAK,CAAC,CAAC,CAAC;EAC3D;;EAEA,IAAI4C,SAAS,GAAGpK,QAAQ,CAAC,CAAC,CAAC;IACvBqK,UAAU,GAAGzK,cAAc,CAACwK,SAAS,EAAE,CAAC,CAAC;IACzCE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEpCtK,SAAS,CAAC,YAAY;IACpB,IAAI,CAACiK,UAAU,IAAItD,YAAY,CAAC6C,OAAO,EAAE;MACvCgB,gBAAgB,CAAC7D,YAAY,CAAC6C,OAAO,CAACiB,WAAW,CAAC;IACpD;EACF,CAAC,EAAE,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB,IAAIS,UAAU,GAAG5K,KAAK,CAACC,MAAM,CAAC,CAAC;EAE/B,SAAS4K,YAAYA,CAACb,OAAO,EAAExH,KAAK,EAAE;IACpC,IAAIwH,OAAO,EAAE;MACXc,YAAY,CAACF,UAAU,CAAClB,OAAO,CAAC;MAChC9C,cAAc,CAAC8C,OAAO,CAAClH,KAAK,CAAC,GAAG,IAAI;MACpCoF,0BAA0B,CAACpF,KAAK,CAAC;MACjC4H,gBAAgB,CAACJ,OAAO,CAAC,CAAC,CAAC;;MAE3B,IAAI,CAACG,UAAU,EAAE;QACfxB,WAAW,CAAC,IAAI,EAAEnG,KAAK,CAAC;MAC1B;IACF,CAAC,MAAM,IAAImF,uBAAuB,KAAKnF,KAAK,EAAE;MAC5C4H,gBAAgB,CAACJ,OAAO,CAAC,CAAC,CAAC;MAC3B;;MAEA,IAAIe,WAAW,GAAGnE,cAAc,CAAC8C,OAAO;MACxCkB,UAAU,CAAClB,OAAO,GAAGsB,UAAU,CAAC,YAAY;QAC1C,IAAID,WAAW,KAAKnE,cAAc,CAAC8C,OAAO,EAAE;UAC1C9C,cAAc,CAAC8C,OAAO,GAAG,CAAC,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ;EACF;EAEA,SAASuB,mBAAmBA,CAACzI,KAAK,EAAE;IAClCqI,YAAY,CAAC,IAAI,EAAErI,KAAK,CAAC,CAAC,CAAC;;IAG3BwI,UAAU,CAAC,YAAY;MACrB,IAAIE,QAAQ,GAAG,CAAChE,aAAa,EAAEC,WAAW,CAAC,CAAC3E,KAAK,CAAC;MAElD,IAAI0I,QAAQ,CAACxB,OAAO,EAAE;QACpBwB,QAAQ,CAACxB,OAAO,CAACyB,KAAK,CAAC,CAAC;MAC1B;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEA,SAASC,aAAaA,CAACC,QAAQ,EAAEC,WAAW,EAAE;IAC5C,IAAInJ,MAAM,GAAGkJ,QAAQ;IACrB,IAAIE,UAAU,GAAG3K,QAAQ,CAACuB,MAAM,EAAE,CAAC,CAAC;IACpC,IAAIqJ,QAAQ,GAAG5K,QAAQ,CAACuB,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEpC,IAAIoJ,UAAU,IAAIC,QAAQ,IAAIpJ,cAAc,CAACC,OAAO,CAACkJ,UAAU,EAAEC,QAAQ,CAAC,EAAE;MAC1E;MAAK;MACLzH,MAAM,KAAK,MAAM,IAAI,CAAC1C,UAAU,CAACe,cAAc,EAAEsB,MAAM,CAACA,MAAM,EAAE6H,UAAU,EAAEC,QAAQ,CAAC;MAAI;MACzFzH,MAAM,KAAK,SAAS,IAAI,CAACzC,aAAa,CAACc,cAAc,EAAEmJ,UAAU,EAAEC,QAAQ,CAAC;MAAI;MAChFzH,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,MAAM,IAAI,CAAC3C,UAAU,CAACgB,cAAc,EAAEmJ,UAAU,EAAEC,QAAQ,CAAC,EAAE;QACnH;QACA,IAAIF,WAAW,KAAK,CAAC,EAAE;UACrBnJ,MAAM,GAAG,CAACoJ,UAAU,EAAE,IAAI,CAAC;UAC3BC,QAAQ,GAAG,IAAI;QACjB,CAAC,MAAM;UACLD,UAAU,GAAG,IAAI;UACjBpJ,MAAM,GAAG,CAAC,IAAI,EAAEqJ,QAAQ,CAAC;QAC3B,CAAC,CAAC;;QAGF5E,cAAc,CAAC8C,OAAO,GAAG5J,eAAe,CAAC,CAAC,CAAC,EAAEwL,WAAW,EAAE,IAAI,CAAC;MACjE,CAAC,MAAM,IAAIvH,MAAM,KAAK,MAAM,IAAIuC,KAAK,KAAK,KAAK,EAAE;QAC/C;QACAnE,MAAM,GAAGD,aAAa,CAACC,MAAM,EAAEC,cAAc,CAAC;MAChD;IACF;IAEA8G,gBAAgB,CAAC/G,MAAM,CAAC;IACxB,IAAIsJ,QAAQ,GAAGtJ,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAGZ,WAAW,CAACY,MAAM,CAAC,CAAC,CAAC,EAAE;MAC1DC,cAAc,EAAEA,cAAc;MAC9BsB,MAAM,EAAEA,MAAM;MACdG,MAAM,EAAE2D,UAAU,CAAC,CAAC;IACtB,CAAC,CAAC,GAAG,EAAE;IACP,IAAIkE,MAAM,GAAGvJ,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAGZ,WAAW,CAACY,MAAM,CAAC,CAAC,CAAC,EAAE;MACxDC,cAAc,EAAEA,cAAc;MAC9BsB,MAAM,EAAEA,MAAM;MACdG,MAAM,EAAE2D,UAAU,CAAC,CAAC;IACtB,CAAC,CAAC,GAAG,EAAE;IAEP,IAAIhC,gBAAgB,EAAE;MACpB,IAAImG,IAAI,GAAG;QACTC,KAAK,EAAEN,WAAW,KAAK,CAAC,GAAG,OAAO,GAAG;MACvC,CAAC;MACD9F,gBAAgB,CAACrD,MAAM,EAAE,CAACsJ,QAAQ,EAAEC,MAAM,CAAC,EAAEC,IAAI,CAAC;IACpD,CAAC,CAAC;;IAGF,IAAIE,oBAAoB,GAAGvJ,eAAe,CAACiJ,UAAU,EAAE,CAAC,EAAEzD,cAAc,EAAEpF,UAAU,CAAC;IACrF,IAAIoJ,kBAAkB,GAAGxJ,eAAe,CAACkJ,QAAQ,EAAE,CAAC,EAAE1D,cAAc,EAAEpF,UAAU,CAAC;IACjF,IAAIqJ,UAAU,GAAG5J,MAAM,KAAK,IAAI,IAAI0J,oBAAoB,IAAIC,kBAAkB;IAE9E,IAAIC,UAAU,EAAE;MACd;MACAzD,aAAa,CAACnG,MAAM,CAAC;MAErB,IAAIkD,QAAQ,KAAK,CAACnE,OAAO,CAACkB,cAAc,EAAExB,QAAQ,CAACyH,WAAW,EAAE,CAAC,CAAC,EAAEkD,UAAU,CAAC,IAAI,CAACrK,OAAO,CAACkB,cAAc,EAAExB,QAAQ,CAACyH,WAAW,EAAE,CAAC,CAAC,EAAEmD,QAAQ,CAAC,CAAC,EAAE;QAChJnG,QAAQ,CAAClD,MAAM,EAAE,CAACsJ,QAAQ,EAAEC,MAAM,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACF;;IAGA,IAAIM,aAAa,GAAG,IAAI;IAExB,IAAIV,WAAW,KAAK,CAAC,IAAI,CAACxD,cAAc,CAAC,CAAC,CAAC,EAAE;MAC3CkE,aAAa,GAAG,CAAC;IACnB,CAAC,MAAM,IAAIV,WAAW,KAAK,CAAC,IAAI,CAACxD,cAAc,CAAC,CAAC,CAAC,EAAE;MAClDkE,aAAa,GAAG,CAAC;IACnB;IAEA,IAAIA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKrE,uBAAuB,KAAK,CAACf,cAAc,CAAC8C,OAAO,CAACsC,aAAa,CAAC,IAAI,CAACpL,QAAQ,CAACuB,MAAM,EAAE6J,aAAa,CAAC,CAAC,IAAIpL,QAAQ,CAACuB,MAAM,EAAEmJ,WAAW,CAAC,EAAE;MACxL;MACAL,mBAAmB,CAACe,aAAa,CAAC;IACpC,CAAC,MAAM;MACLnB,YAAY,CAAC,KAAK,EAAES,WAAW,CAAC;IAClC;EACF;EAEA,IAAIW,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAE;IAC9C,IAAI/B,UAAU,IAAItC,YAAY,CAAC6B,OAAO,IAAI7B,YAAY,CAAC6B,OAAO,CAACtD,SAAS,EAAE;MACxE;MACA,OAAOyB,YAAY,CAAC6B,OAAO,CAACtD,SAAS,CAAC8F,CAAC,CAAC;IAC1C;IACA;;IAEA;;IAGA;MACE7L,OAAO,CAAC,KAAK,EAAE,qFAAqF,CAAC;MACrG,OAAO,KAAK;IACd;EACF,CAAC,CAAC,CAAC;;EAGH,IAAI8L,oBAAoB,GAAG;IACzB3E,UAAU,EAAEA,UAAU;IACtBpF,cAAc,EAAEA,cAAc;IAC9BsB,MAAM,EAAEA;EACV,CAAC;EAED,IAAI0I,cAAc,GAAG3K,aAAa,CAACb,QAAQ,CAACqI,aAAa,EAAE,CAAC,CAAC,EAAEkD,oBAAoB,CAAC;IAChFE,eAAe,GAAGtM,cAAc,CAACqM,cAAc,EAAE,CAAC,CAAC;IACnDE,eAAe,GAAGD,eAAe,CAAC,CAAC,CAAC;IACpCE,mBAAmB,GAAGF,eAAe,CAAC,CAAC,CAAC;EAE5C,IAAIG,eAAe,GAAG/K,aAAa,CAACb,QAAQ,CAACqI,aAAa,EAAE,CAAC,CAAC,EAAEkD,oBAAoB,CAAC;IACjFM,eAAe,GAAG1M,cAAc,CAACyM,eAAe,EAAE,CAAC,CAAC;IACpDE,aAAa,GAAGD,eAAe,CAAC,CAAC,CAAC;IAClCE,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC;EAE1C,IAAIG,aAAa,GAAG,SAASC,YAAYA,CAACC,OAAO,EAAEtK,KAAK,EAAE;IACxD,IAAIuK,SAAS,GAAGvL,UAAU,CAACsL,OAAO,EAAE;MAClCpJ,MAAM,EAAEA,MAAM;MACd8D,UAAU,EAAEA,UAAU;MACtBpF,cAAc,EAAEA;IAClB,CAAC,CAAC;IACF,IAAI4K,YAAY,GAAGxK,KAAK,KAAK,CAAC,GAAGoH,iBAAiB,GAAGC,eAAe;IAEpE,IAAIkD,SAAS,IAAI,CAACC,YAAY,CAACD,SAAS,CAAC,EAAE;MACzC7D,gBAAgB,CAACrI,YAAY,CAACoI,aAAa,EAAE8D,SAAS,EAAEvK,KAAK,CAAC,CAAC;MAC/DmG,WAAW,CAACoE,SAAS,EAAEvK,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,IAAIyK,oBAAoB,GAAGvL,mBAAmB,CAAC;MAC7CwL,UAAU,EAAEZ,eAAe;MAC3BO,YAAY,EAAE,SAASA,YAAYA,CAACC,OAAO,EAAE;QAC3C,OAAOF,aAAa,CAACE,OAAO,EAAE,CAAC,CAAC;MAClC;IACF,CAAC,CAAC;IACEK,qBAAqB,GAAGpN,cAAc,CAACkN,oBAAoB,EAAE,CAAC,CAAC;IAC/DG,SAAS,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IACpCE,sBAAsB,GAAGF,qBAAqB,CAAC,CAAC,CAAC;IACjDG,cAAc,GAAGH,qBAAqB,CAAC,CAAC,CAAC;EAE7C,IAAII,qBAAqB,GAAG7L,mBAAmB,CAAC;MAC9CwL,UAAU,EAAER,aAAa;MACzBG,YAAY,EAAE,SAASA,YAAYA,CAACC,OAAO,EAAE;QAC3C,OAAOF,aAAa,CAACE,OAAO,EAAE,CAAC,CAAC;MAClC;IACF,CAAC,CAAC;IACEU,qBAAqB,GAAGzN,cAAc,CAACwN,qBAAqB,EAAE,CAAC,CAAC;IAChEE,OAAO,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IAClCE,oBAAoB,GAAGF,qBAAqB,CAAC,CAAC,CAAC;IAC/CG,YAAY,GAAGH,qBAAqB,CAAC,CAAC,CAAC;EAE3C,IAAII,UAAU,GAAGzN,QAAQ,CAAC,IAAI,CAAC;IAC3B0N,UAAU,GAAG9N,cAAc,CAAC6N,UAAU,EAAE,CAAC,CAAC;IAC1CE,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGxC,IAAIG,UAAU,GAAG7N,QAAQ,CAAC,IAAI,CAAC;IAC3B8N,UAAU,GAAGlO,cAAc,CAACiO,UAAU,EAAE,CAAC,CAAC;IAC1CE,gBAAgB,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChCE,mBAAmB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEvC,IAAIG,cAAc,GAAGpM,aAAa,CAACoL,SAAS,EAAE;MAC5C5F,UAAU,EAAEA,UAAU;MACtBpF,cAAc,EAAEA,cAAc;MAC9BsB,MAAM,EAAEA;IACV,CAAC,CAAC;IACE2K,eAAe,GAAGtO,cAAc,CAACqO,cAAc,EAAE,CAAC,CAAC;IACnDE,eAAe,GAAGD,eAAe,CAAC,CAAC,CAAC;IACpCE,YAAY,GAAGF,eAAe,CAAC,CAAC,CAAC;IACjCG,YAAY,GAAGH,eAAe,CAAC,CAAC,CAAC;EAErC,IAAII,eAAe,GAAGzM,aAAa,CAACyL,OAAO,EAAE;MAC3CjG,UAAU,EAAEA,UAAU;MACtBpF,cAAc,EAAEA,cAAc;MAC9BsB,MAAM,EAAEA;IACV,CAAC,CAAC;IACEgL,eAAe,GAAG3O,cAAc,CAAC0O,eAAe,EAAE,CAAC,CAAC;IACpDE,aAAa,GAAGD,eAAe,CAAC,CAAC,CAAC;IAClCE,UAAU,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC/BG,UAAU,GAAGH,eAAe,CAAC,CAAC,CAAC;EAEnC,IAAII,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACrDZ,mBAAmB,CAACtN,YAAY,CAACoI,aAAa,EAAE8F,IAAI,EAAEpH,uBAAuB,CAAC,CAAC;IAE/E,IAAIA,uBAAuB,KAAK,CAAC,EAAE;MACjC4G,YAAY,CAACQ,IAAI,CAAC;IACpB,CAAC,MAAM;MACLH,UAAU,CAACG,IAAI,CAAC;IAClB;EACF,CAAC;EAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDb,mBAAmB,CAACtN,YAAY,CAACoI,aAAa,EAAE,IAAI,EAAEtB,uBAAuB,CAAC,CAAC;IAE/E,IAAIA,uBAAuB,KAAK,CAAC,EAAE;MACjC6G,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLK,UAAU,CAAC,CAAC;IACd;EACF,CAAC,CAAC,CAAC;;EAGH,IAAII,uBAAuB,GAAG,SAASA,uBAAuBA,CAACzM,KAAK,EAAE0M,SAAS,EAAE;IAC/E,OAAO;MACLC,YAAY,EAAExI,iBAAiB;MAC/BsF,cAAc,EAAEA,cAAc;MAC9BtG,MAAM,EAAEA,MAAM;MACdyJ,cAAc,EAAE,SAASA,cAAcA,CAACC,MAAM,EAAE;QAC9C,OAAO,CAACrO,gBAAgB,CAAC,CAAC8F,WAAW,CAAC4C,OAAO,EAAE3C,gBAAgB,CAAC2C,OAAO,EAAE1C,cAAc,CAAC0C,OAAO,EAAE7C,YAAY,CAAC6C,OAAO,CAAC,EAAE2F,MAAM,CAAC;MACjI,CAAC;MACD3J,OAAO,EAAE,SAASA,OAAOA,CAACwG,CAAC,EAAE;QAC3BtE,0BAA0B,CAACpF,KAAK,CAAC;QAEjC,IAAIiD,QAAQ,EAAE;UACZA,QAAQ,CAACyG,CAAC,CAAC;QACb;MACF,CAAC;MACDoD,WAAW,EAAE,SAASA,WAAWA,CAACtF,OAAO,EAAE;QACzCa,YAAY,CAACb,OAAO,EAAExH,KAAK,CAAC;MAC9B,CAAC;MACD+M,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5B;QAAK;QACL,CAACtG,aAAa;QAAI;QAClBzE,YAAY,IAAIA,YAAY,CAACyE,aAAa,CAACzG,KAAK,CAAC,CAAC,EAAE;UAClD,OAAO,KAAK;QACd;QAEA4I,aAAa,CAACnC,aAAa,EAAEzG,KAAK,CAAC;QACnC0M,SAAS,CAAC,CAAC;MACb,CAAC;MACDM,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5B3E,YAAY,CAAC,KAAK,EAAErI,KAAK,CAAC;QAE1B0G,gBAAgB,CAACb,WAAW,CAAC;QAC7B6G,SAAS,CAAC,CAAC;MACb;IACF,CAAC;EACH,CAAC;EAED,IAAIO,eAAe,GAAGhP,cAAc,CAACZ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoP,uBAAuB,CAAC,CAAC,EAAE3B,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACpHhJ,IAAI,EAAE+F,SAAS;MACf9H,KAAK,EAAE6K,SAAS;MAChBhH,SAAS,EAAE,SAASA,SAASA,CAAC8F,CAAC,EAAEwD,cAAc,EAAE;QAC/CvJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC+F,CAAC,EAAEwD,cAAc,CAAC;MACvF;IACF,CAAC,CAAC,CAAC;IACCC,gBAAgB,GAAG5P,cAAc,CAAC0P,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACvCG,YAAY,GAAGD,iBAAiB,CAACE,OAAO;IACxCC,WAAW,GAAGH,iBAAiB,CAACI,MAAM;EAE1C,IAAIC,gBAAgB,GAAGzP,cAAc,CAACZ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoP,uBAAuB,CAAC,CAAC,EAAEtB,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACnHrJ,IAAI,EAAEgG,OAAO;MACb/H,KAAK,EAAEkL,OAAO;MACdrH,SAAS,EAAE,SAASA,SAASA,CAAC8F,CAAC,EAAEwD,cAAc,EAAE;QAC/CvJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC+F,CAAC,EAAEwD,cAAc,CAAC;MACvF;IACF,CAAC,CAAC,CAAC;IACCS,gBAAgB,GAAGpQ,cAAc,CAACmQ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACvCG,UAAU,GAAGD,iBAAiB,CAACN,OAAO;IACtCQ,SAAS,GAAGF,iBAAiB,CAACJ,MAAM,CAAC,CAAC;;EAG1C,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACtE,CAAC,EAAE;IAC5C;IACA;IACA,IAAIlG,OAAO,EAAE;MACXA,OAAO,CAACkG,CAAC,CAAC;IACZ;IAEA,IAAI,CAAC/B,UAAU,IAAI,CAACjD,aAAa,CAACwC,OAAO,CAAC+G,QAAQ,CAACvE,CAAC,CAACmD,MAAM,CAAC,IAAI,CAAClI,WAAW,CAACuC,OAAO,CAAC+G,QAAQ,CAACvE,CAAC,CAACmD,MAAM,CAAC,EAAE;MACvG,IAAI,CAACvH,cAAc,CAAC,CAAC,CAAC,EAAE;QACtBmD,mBAAmB,CAAC,CAAC,CAAC;MACxB,CAAC,MAAM,IAAI,CAACnD,cAAc,CAAC,CAAC,CAAC,EAAE;QAC7BmD,mBAAmB,CAAC,CAAC,CAAC;MACxB;IACF;EACF,CAAC;EAED,IAAIyF,iBAAiB,GAAG,SAASA,iBAAiBA,CAACxE,CAAC,EAAE;IACpD;IACA,IAAItG,WAAW,EAAE;MACfA,WAAW,CAACsG,CAAC,CAAC;IAChB;IAEA,IAAI/B,UAAU,KAAK2F,YAAY,IAAIQ,UAAU,CAAC,IAAI,CAACpJ,aAAa,CAACwC,OAAO,CAAC+G,QAAQ,CAACvE,CAAC,CAACmD,MAAM,CAAC,IAAI,CAAClI,WAAW,CAACuC,OAAO,CAAC+G,QAAQ,CAACvE,CAAC,CAACmD,MAAM,CAAC,EAAE;MACtInD,CAAC,CAACwD,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,CAAC,CAAC;EACH;;EAGA,IAAIjE,QAAQ,GAAGpD,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,GAAG9G,WAAW,CAAC8G,WAAW,CAAC,CAAC,CAAC,EAAE;IACzE3E,MAAM,EAAEA,MAAM;IACdG,MAAM,EAAE,gBAAgB;IACxBzB,cAAc,EAAEA;EAClB,CAAC,CAAC,GAAG,EAAE;EACP,IAAIsJ,MAAM,GAAGrD,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,GAAG9G,WAAW,CAAC8G,WAAW,CAAC,CAAC,CAAC,EAAE;IACvE3E,MAAM,EAAEA,MAAM;IACdG,MAAM,EAAE,gBAAgB;IACxBzB,cAAc,EAAEA;EAClB,CAAC,CAAC,GAAG,EAAE;EACPlC,SAAS,CAAC,YAAY;IACpB,IAAI,CAACiK,UAAU,EAAE;MACfjB,gBAAgB,CAACb,WAAW,CAAC;MAE7B,IAAI,CAACiE,eAAe,CAACqE,MAAM,IAAIrE,eAAe,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QACxDe,sBAAsB,CAAC,EAAE,CAAC;MAC5B,CAAC,MAAM,IAAId,mBAAmB,KAAKa,SAAS,EAAE;QAC5CE,cAAc,CAAC,CAAC;MAClB;MAEA,IAAI,CAACZ,aAAa,CAACiE,MAAM,IAAIjE,aAAa,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QACpDgB,oBAAoB,CAAC,EAAE,CAAC;MAC1B,CAAC,MAAM,IAAIf,iBAAiB,KAAKc,OAAO,EAAE;QACxCE,YAAY,CAAC,CAAC;MAChB;IACF;EACF,CAAC,EAAE,CAACxD,UAAU,EAAEmC,eAAe,EAAEI,aAAa,CAAC,CAAC,CAAC,CAAC;;EAElDxM,SAAS,CAAC,YAAY;IACpBgJ,gBAAgB,CAACb,WAAW,CAAC;EAC/B,CAAC,EAAE,CAACoD,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAExB,IAAIrE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIhF,KAAK,IAAIyF,KAAK,CAACC,OAAO,CAACxF,QAAQ,CAAC,KAAK7B,QAAQ,CAAC6B,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC7B,QAAQ,CAAC2B,KAAK,EAAE,CAAC,CAAC,IAAI3B,QAAQ,CAAC6B,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC7B,QAAQ,CAAC2B,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;MACtIlC,OAAO,CAAC,KAAK,EAAE,+FAA+F,CAAC;IACjH;EACF,CAAC,CAAC;;EAGF,IAAI4E,SAAS,EAAE;IACbA,SAAS,CAACyE,OAAO,GAAG;MAClByB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB,IAAIjE,aAAa,CAACwC,OAAO,EAAE;UACzBxC,aAAa,CAACwC,OAAO,CAACyB,KAAK,CAAC,CAAC;QAC/B;MACF,CAAC;MACDyF,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAI1J,aAAa,CAACwC,OAAO,EAAE;UACzBxC,aAAa,CAACwC,OAAO,CAACkH,IAAI,CAAC,CAAC;QAC9B;QAEA,IAAIzJ,WAAW,CAACuC,OAAO,EAAE;UACvBvC,WAAW,CAACuC,OAAO,CAACkH,IAAI,CAAC,CAAC;QAC5B;MACF;IACF,CAAC;EACH,CAAC,CAAC;;EAGF,IAAIC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAClM,MAAM,IAAI,CAAC,CAAC,CAAC;EAC3C,IAAImM,SAAS,GAAGH,WAAW,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAE;IAC/C,IAAItF,KAAK,GAAG/G,MAAM,CAACqM,KAAK,CAAC;IACzB,IAAIC,SAAS,GAAG,OAAOvF,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC7D,OAAO;MACLsF,KAAK,EAAEA,KAAK;MACZlL,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1BoF,aAAa,CAAC+F,SAAS,EAAE,IAAI,CAAC;QAE9BtG,YAAY,CAAC,KAAK,EAAElD,uBAAuB,CAAC;MAC9C,CAAC;MACD7B,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpCiI,kBAAkB,CAACoD,SAAS,CAAC;MAC/B,CAAC;MACDpL,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpCgI,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,SAASqD,WAAWA,CAAA,EAAG;IACrB,IAAIC,aAAa,GAAGC,SAAS,CAACX,MAAM,GAAG,CAAC,IAAIW,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC7F,IAAIE,UAAU,GAAGF,SAAS,CAACX,MAAM,GAAG,CAAC,IAAIW,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvF,IAAIG,qBAAqB,GAAG,IAAI;IAEhC,IAAItH,UAAU,IAAI+D,gBAAgB,IAAIA,gBAAgB,CAAC,CAAC,CAAC,IAAIA,gBAAgB,CAAC,CAAC,CAAC,IAAI9L,cAAc,CAACC,OAAO,CAAC6L,gBAAgB,CAAC,CAAC,CAAC,EAAEA,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE;MACpJuD,qBAAqB,GAAGvD,gBAAgB;IAC1C;IAEA,IAAIwD,aAAa,GAAG1N,QAAQ;IAE5B,IAAIA,QAAQ,IAAIpE,OAAO,CAACoE,QAAQ,CAAC,KAAK,QAAQ,IAAIA,QAAQ,CAACI,YAAY,EAAE;MACvE,IAAIuN,iBAAiB,GAAG3N,QAAQ,CAACI,YAAY;MAC7CsN,aAAa,GAAG7R,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7DI,YAAY,EAAExD,QAAQ,CAAC+Q,iBAAiB,EAAEhK,uBAAuB,CAAC,IAAI4J;MACxE,CAAC,CAAC;IACJ;IAEA,IAAIK,eAAe,GAAG,IAAI;IAE1B,IAAIjN,UAAU,EAAE;MACdiN,eAAe,GAAG,SAASA,eAAeA,CAAC7C,IAAI,EAAE8C,KAAK,EAAE;QACtD,OAAOlN,UAAU,CAACoK,IAAI,EAAE8C,KAAK,EAAE;UAC7BjG,KAAK,EAAEjE,uBAAuB,GAAG,KAAK,GAAG;QAC3C,CAAC,CAAC;MACJ,CAAC;IACH;IAEA,OAAO,aAAa3H,KAAK,CAAC8R,aAAa,CAACnQ,YAAY,CAACoQ,QAAQ,EAAE;MAC7DxP,KAAK,EAAE;QACLyP,OAAO,EAAE,IAAI;QACbX,aAAa,EAAEA,aAAa;QAC5BY,WAAW,EAAEnE,eAAe,IAAI7E,aAAa;QAC7CiF,gBAAgB,EAAEuD;MACpB;IACF,CAAC,EAAE,aAAazR,KAAK,CAAC8R,aAAa,CAACtR,WAAW,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEiD,KAAK,EAAE4O,UAAU,EAAE;MAC/E7M,UAAU,EAAEiN,eAAe;MAC3B5N,QAAQ,EAAE0N,aAAa;MACvBvM,IAAI,EAAEkE,WAAW,CAAC1B,uBAAuB,CAAC;MAC1CvF,cAAc,EAAEA,cAAc;MAC9Be,KAAK,EAAEoO,SAAS;MAChBhL,SAAS,EAAEA,SAAS;MACpB/B,YAAY,EAAEmD,uBAAuB,KAAK,CAAC,GAAGiC,iBAAiB,GAAGC,eAAe;MACjFnF,YAAY,EAAE,SAASA,YAAYA,CAACqK,IAAI,EAAE;QACxC,IAAItK,aAAa,EAAE;UACjB,OAAOA,aAAa,CAACsK,IAAI,EAAEpH,uBAAuB,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;QAC7E;QAEA,OAAO,KAAK;MACd,CAAC;MACDvE,SAAS,EAAEhD,UAAU,CAACN,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoS,MAAM,CAACjP,SAAS,EAAE,gBAAgB,CAAC,EAAE0E,uBAAuB,KAAK,CAAC,GAAG,CAACqI,WAAW,GAAG,CAACO,SAAS,CAAC,CAAC;MAC7IhO,KAAK,EAAE3B,QAAQ,CAACqI,aAAa,EAAEtB,uBAAuB,CAAC;MACvDjE,MAAM,EAAEA,MAAM;MACdyO,QAAQ,EAAE,CAAC,CAAC;MACZ5M,aAAa,EAAE,SAASA,aAAaA,CAACwJ,IAAI,EAAEqD,OAAO,EAAE;QACnD;QACA,IAAIzK,uBAAuB,KAAK,CAAC,EAAE;UACjC6G,YAAY,CAAC,IAAI,CAAC;QACpB;QAEA,IAAI7G,uBAAuB,KAAK,CAAC,EAAE;UACjCkH,UAAU,CAAC,IAAI,CAAC;QAClB;QAEAtF,kBAAkB,CAAC1I,YAAY,CAACwI,WAAW,EAAE+I,OAAO,EAAEzK,uBAAuB,CAAC,EAAE9G,YAAY,CAACoI,aAAa,EAAE8F,IAAI,EAAEpH,uBAAuB,CAAC,CAAC;QAC3I,IAAI0K,QAAQ,GAAGtD,IAAI;QAEnB,IAAIsC,aAAa,KAAK,OAAO,IAAIhI,WAAW,CAAC1B,uBAAuB,CAAC,KAAKyK,OAAO,EAAE;UACjFC,QAAQ,GAAGlR,kBAAkB,CAACkR,QAAQ,EAAED,OAAO,EAAEhQ,cAAc,EAAE,CAAC,CAAC,CAAC;QACtE;QAEAuG,WAAW,CAAC0J,QAAQ,EAAE1K,uBAAuB,CAAC;MAChD,CAAC;MACDzB,IAAI,EAAE,IAAI;MACVoM,QAAQ,EAAEf,SAAS;MACnBlM,QAAQ,EAAEkM,SAAS;MACnBnN,YAAY,EAAEuD,uBAAuB,KAAK,CAAC,GAAG/G,QAAQ,CAACqI,aAAa,EAAE,CAAC,CAAC,GAAGrI,QAAQ,CAACqI,aAAa,EAAE,CAAC;IACtG,CAAC,CAAC,CAAC,CAAC;EACN;EAEA,IAAIsJ,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,CAAC;EAEjB,IAAI7K,uBAAuB,IAAIZ,gBAAgB,CAAC2C,OAAO,IAAIzC,YAAY,CAACyC,OAAO,IAAI5C,WAAW,CAAC4C,OAAO,EAAE;IACtG;IACA6I,SAAS,GAAGxL,gBAAgB,CAAC2C,OAAO,CAACiB,WAAW,GAAG1D,YAAY,CAACyC,OAAO,CAACiB,WAAW,CAAC,CAAC;IACrF;;IAEA,IAAI7D,WAAW,CAAC4C,OAAO,CAACiB,WAAW,IAAIvD,QAAQ,CAACsC,OAAO,CAACiB,WAAW,IAAI4H,SAAS,GAAGzL,WAAW,CAAC4C,OAAO,CAACiB,WAAW,GAAGvD,QAAQ,CAACsC,OAAO,CAACiB,WAAW,IAAIpE,SAAS,KAAK,KAAK,IAAIa,QAAQ,CAACsC,OAAO,CAAC+I,UAAU,GAAGF,SAAS,GAAG,CAAC,GAAGnL,QAAQ,CAACsC,OAAO,CAAC+I,UAAU,CAAC,EAAE;MACtPD,SAAS,GAAGD,SAAS;IACvB;EACF;EAEA,IAAIG,kBAAkB,GAAGnM,SAAS,KAAK,KAAK,GAAG;IAC7CoM,KAAK,EAAEJ;EACT,CAAC,GAAG;IACFK,IAAI,EAAEL;EACR,CAAC;EAED,SAASM,YAAYA,CAAA,EAAG;IACtB,IAAIC,MAAM;IACV,IAAIC,SAAS,GAAGlR,cAAc,CAACoB,SAAS,EAAEoG,WAAW,CAAC1B,uBAAuB,CAAC,EAAEvC,iBAAiB,CAAC;IAClG,IAAI4N,UAAU,GAAGlR,SAAS,CAAC;MACzBmB,SAAS,EAAEA,SAAS;MACpBoD,UAAU,EAAEA,UAAU;MACtBM,iBAAiB,EAAEA,iBAAiB;MACpCsM,UAAU,EAAE,CAACrS,QAAQ,CAACqI,aAAa,EAAEtB,uBAAuB,CAAC,IAAInD,YAAY,IAAIA,YAAY,CAACyE,aAAa,CAACtB,uBAAuB,CAAC,CAAC;MACrIjE,MAAM,EAAEA,MAAM;MACdsN,SAAS,EAAEA,SAAS;MACpB9K,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAItF,QAAQ,CAACqI,aAAa,EAAEtB,uBAAuB,CAAC,EAAE;UACpD;UACAyD,aAAa,CAACnC,aAAa,EAAEtB,uBAAuB,CAAC;UAErD,IAAI1B,KAAK,EAAE;YACTA,KAAK,CAACgD,aAAa,CAAC;UACtB;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAIlF,MAAM,KAAK,MAAM,IAAI,CAACC,QAAQ,EAAE;MAClC,IAAIqO,QAAQ,GAAG3J,WAAW,CAACf,uBAAuB,CAAC;MACnD,IAAIuL,YAAY,GAAG/R,kBAAkB,CAACkR,QAAQ,EAAEtO,MAAM,EAAE3B,cAAc,CAAC;MACvE,IAAI+Q,WAAW,GAAG9J,WAAW,CAAC1B,uBAAuB,CAAC;MACtD,IAAIyL,eAAe,GAAGD,WAAW,KAAKpP,MAAM;MAC5C,IAAIsP,SAAS,GAAGjC,WAAW,CAACgC,eAAe,GAAG,MAAM,GAAG,KAAK,EAAE;QAC5DE,WAAW,EAAEjB,QAAQ;QACrBkB,mBAAmB,EAAE,SAASA,mBAAmBA,CAACC,WAAW,EAAE;UAC7D7K,WAAW,CAAC6K,WAAW,EAAE7L,uBAAuB,CAAC;QACnD;MACF,CAAC,CAAC;MACF,IAAI8L,UAAU,GAAGrC,WAAW,CAAC,OAAO,EAAE;QACpCkC,WAAW,EAAEJ,YAAY;QACzBK,mBAAmB,EAAE,SAASA,mBAAmBA,CAACC,WAAW,EAAE;UAC7D7K,WAAW,CAACxH,kBAAkB,CAACqS,WAAW,EAAEzP,MAAM,EAAE3B,cAAc,EAAE,CAAC,CAAC,CAAC,EAAEuF,uBAAuB,CAAC;QACnG;MACF,CAAC,CAAC;MAEF,IAAIpB,SAAS,KAAK,KAAK,EAAE;QACvBuM,MAAM,GAAG,aAAa9S,KAAK,CAAC8R,aAAa,CAAC9R,KAAK,CAAC0T,QAAQ,EAAE,IAAI,EAAED,UAAU,EAAEL,eAAe,IAAIC,SAAS,CAAC;MAC3G,CAAC,MAAM;QACLP,MAAM,GAAG,aAAa9S,KAAK,CAAC8R,aAAa,CAAC9R,KAAK,CAAC0T,QAAQ,EAAE,IAAI,EAAEL,SAAS,EAAED,eAAe,IAAIK,UAAU,CAAC;MAC3G;IACF,CAAC,MAAM;MACLX,MAAM,GAAG1B,WAAW,CAAC,CAAC;IACxB;IAEA,IAAIuC,WAAW,GAAG,aAAa3T,KAAK,CAAC8R,aAAa,CAAC9R,KAAK,CAAC0T,QAAQ,EAAE,IAAI,EAAE,aAAa1T,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;MAC/G1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE6P,MAAM,CAAC,EAAE,CAACC,SAAS,IAAIC,UAAU,KAAK,aAAahT,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;MAC/E1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE8P,SAAS,EAAEC,UAAU,CAAC,CAAC;IAE1B,IAAIpO,WAAW,EAAE;MACf+O,WAAW,GAAG/O,WAAW,CAAC+O,WAAW,CAAC;IACxC;IAEA,OAAO,aAAa3T,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;MAC7C1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,kBAAkB,CAAC;MACnDE,KAAK,EAAE;QACLyQ,UAAU,EAAEpB;MACd,CAAC;MACDqB,GAAG,EAAE/M,WAAW;MAChBlB,WAAW,EAAE,SAASA,WAAWA,CAACsG,CAAC,EAAE;QACnCA,CAAC,CAACwD,cAAc,CAAC,CAAC;MACpB;IACF,CAAC,EAAEiE,WAAW,CAAC;EACjB;EAEA,IAAIG,UAAU,GAAG,aAAa9T,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;IACvD1O,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAAC8R,MAAM,CAACjP,SAAS,EAAE,gBAAgB,CAAC,EAAE,EAAE,CAACiP,MAAM,CAACjP,SAAS,EAAE,GAAG,CAAC,CAACiP,MAAM,CAACnO,MAAM,EAAE,gBAAgB,CAAC,CAAC;IACzHZ,KAAK,EAAE;MACL4Q,QAAQ,EAAEtJ;IACZ;EACF,CAAC,EAAE,aAAazK,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;IACzC+B,GAAG,EAAEzM,QAAQ;IACbhE,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,cAAc,CAAC;IAC/CE,KAAK,EAAEuP;EACT,CAAC,CAAC,EAAEG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErB,IAAImB,UAAU;EAEd,IAAIjP,UAAU,EAAE;IACdiP,UAAU,GAAG,aAAahU,KAAK,CAAC8R,aAAa,CAAC,MAAM,EAAE;MACpD1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE8B,UAAU,CAAC;EAChB;EAEA,IAAIkP,SAAS;EAEb,IAAInP,UAAU,KAAKlE,QAAQ,CAACyH,WAAW,EAAE,CAAC,CAAC,IAAI,CAACP,cAAc,CAAC,CAAC,CAAC,IAAIlH,QAAQ,CAACyH,WAAW,EAAE,CAAC,CAAC,IAAI,CAACP,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;IACpHmM,SAAS,GAAG,aAAajU,KAAK,CAAC8R,aAAa,CAAC,MAAM,EAAE;MACnDlM,WAAW,EAAE,SAASA,WAAWA,CAACsG,CAAC,EAAE;QACnCA,CAAC,CAACwD,cAAc,CAAC,CAAC;QAClBxD,CAAC,CAACgI,eAAe,CAAC,CAAC;MACrB,CAAC;MACDrO,SAAS,EAAE,SAASA,SAASA,CAACqG,CAAC,EAAE;QAC/BA,CAAC,CAACwD,cAAc,CAAC,CAAC;QAClBxD,CAAC,CAACgI,eAAe,CAAC,CAAC;QACnB,IAAI/R,MAAM,GAAGkG,WAAW;QAExB,IAAI,CAACP,cAAc,CAAC,CAAC,CAAC,EAAE;UACtB3F,MAAM,GAAGtB,YAAY,CAACsB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACxC;QAEA,IAAI,CAAC2F,cAAc,CAAC,CAAC,CAAC,EAAE;UACtB3F,MAAM,GAAGtB,YAAY,CAACsB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACxC;QAEAiJ,aAAa,CAACjJ,MAAM,EAAE,IAAI,CAAC;QAE3B0I,YAAY,CAAC,KAAK,EAAElD,uBAAuB,CAAC;MAC9C,CAAC;MACDvE,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,QAAQ;IAC1C,CAAC,EAAE+B,SAAS,IAAI,aAAahF,KAAK,CAAC8R,aAAa,CAAC,MAAM,EAAE;MACvD1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,YAAY;IAC9C,CAAC,CAAC,CAAC;EACL;EAEA,IAAIkR,gBAAgB,GAAG;IACrBC,IAAI,EAAErT,YAAY,CAACgD,MAAM,EAAEyD,UAAU,CAAC,CAAC,CAAC,EAAEpF,cAAc;EAC1D,CAAC;EACD,IAAIiS,aAAa,GAAG,CAAC;EACrB,IAAIC,cAAc,GAAG,CAAC;EAEtB,IAAIvN,gBAAgB,CAAC2C,OAAO,IAAI1C,cAAc,CAAC0C,OAAO,IAAIzC,YAAY,CAACyC,OAAO,EAAE;IAC9E,IAAI/B,uBAAuB,KAAK,CAAC,EAAE;MACjC2M,cAAc,GAAGvN,gBAAgB,CAAC2C,OAAO,CAACiB,WAAW;IACvD,CAAC,MAAM;MACL0J,aAAa,GAAG9B,SAAS;MACzB+B,cAAc,GAAGtN,cAAc,CAAC0C,OAAO,CAACiB,WAAW;IACrD;EACF;EAEA,IAAI4J,sBAAsB,GAAGhO,SAAS,KAAK,KAAK,GAAG;IACjDoM,KAAK,EAAE0B;EACT,CAAC,GAAG;IACFzB,IAAI,EAAEyB;EACR,CAAC,CAAC,CAAC;;EAEH,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACzF,IAAI,EAAE0F,IAAI,EAAE;IACzD,IAAItS,MAAM,GAAGtB,YAAY,CAACoI,aAAa,EAAE8F,IAAI,EAAEpH,uBAAuB,CAAC;IAEvE,IAAI8M,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,KAAK,IAAI,CAAC9N,iBAAiB,EAAE;MAC7D;MACAyE,aAAa,CAACjJ,MAAM,EAAEwF,uBAAuB,CAAC,CAAC,CAAC;;MAEhD,IAAIA,uBAAuB,KAAK,CAAC,EAAE;QACjC6G,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLK,UAAU,CAAC,CAAC;MACd;IACF,CAAC,MAAM;MACL3F,gBAAgB,CAAC/G,MAAM,CAAC;IAC1B;EACF,CAAC;EAED,OAAO,aAAanC,KAAK,CAAC8R,aAAa,CAAC7Q,YAAY,CAAC8Q,QAAQ,EAAE;IAC7DxP,KAAK,EAAE;MACLsF,YAAY,EAAEA,YAAY;MAC1B6M,UAAU,EAAE3Q,MAAM,KAAK,MAAM;MAC7B+K,gBAAgB,EAAEA,gBAAgB;MAClCE,gBAAgB,EAAEA,gBAAgB;MAClC2F,UAAU,EAAE,IAAI;MAChBrC,QAAQ,EAAEkC,eAAe;MACzBlQ,IAAI,EAAE6F;IACR;EACF,CAAC,EAAE,aAAanK,KAAK,CAAC8R,aAAa,CAACvR,aAAa,EAAE;IACjDqU,OAAO,EAAEzK,UAAU;IACnB0K,YAAY,EAAEf,UAAU;IACxBzQ,UAAU,EAAEA,UAAU;IACtBJ,SAAS,EAAEA,SAAS;IACpBK,iBAAiB,EAAEA,iBAAiB;IACpCE,aAAa,EAAEA,aAAa;IAC5BC,iBAAiB,EAAEA,iBAAiB;IACpCF,cAAc,EAAEA,cAAc;IAC9BqI,KAAK,EAAE,IAAI;IACXrF,SAAS,EAAEA;EACb,CAAC,EAAE,aAAavG,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAEnS,QAAQ,CAAC;IAClDkU,GAAG,EAAEhN,YAAY;IACjBzD,SAAS,EAAEhD,UAAU,CAAC6C,SAAS,EAAE,EAAE,CAACiP,MAAM,CAACjP,SAAS,EAAE,QAAQ,CAAC,EAAEG,SAAS,GAAGP,YAAY,GAAG,CAAC,CAAC,EAAE/C,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACqP,MAAM,CAACjP,SAAS,EAAE,WAAW,CAAC,EAAE6E,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEhI,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACqP,MAAM,CAACjP,SAAS,EAAE,UAAU,CAAC,EAAE0E,uBAAuB,KAAK,CAAC,GAAGmI,YAAY,GAAGQ,UAAU,CAAC,EAAExQ,eAAe,CAAC+C,YAAY,EAAE,EAAE,CAACqP,MAAM,CAACjP,SAAS,EAAE,MAAM,CAAC,EAAEsD,SAAS,KAAK,KAAK,CAAC,EAAE1D,YAAY,CAAC,CAAC;IACtaM,KAAK,EAAEA,KAAK;IACZ6C,OAAO,EAAEwK,aAAa;IACtB1K,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BH,WAAW,EAAE8K,iBAAiB;IAC9B7K,SAAS,EAAEA;EACb,CAAC,EAAEnF,kBAAkB,CAACkC,KAAK,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;IACrE1O,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAAC8R,MAAM,CAACjP,SAAS,EAAE,QAAQ,CAAC,GAAGH,YAAY,GAAG,CAAC,CAAC,EAAEhD,eAAe,CAACgD,YAAY,EAAE,EAAE,CAACoP,MAAM,CAACjP,SAAS,EAAE,eAAe,CAAC,EAAE0E,uBAAuB,KAAK,CAAC,CAAC,EAAE7H,eAAe,CAACgD,YAAY,EAAE,EAAE,CAACoP,MAAM,CAACjP,SAAS,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAACqL,eAAe,CAAC,EAAExL,YAAY,CAAC,CAAC;IAC3R+Q,GAAG,EAAE9M;EACP,CAAC,EAAE,aAAa/G,KAAK,CAAC8R,aAAa,CAAC,OAAO,EAAEnS,QAAQ,CAAC;IACpDuD,EAAE,EAAEA,EAAE;IACNT,QAAQ,EAAEqF,cAAc,CAAC,CAAC,CAAC;IAC3BgN,QAAQ,EAAE5P,aAAa,IAAI,OAAOsC,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,IAAI,CAACwI,WAAW;IAC9EzN,KAAK,EAAE+L,eAAe,IAAIlB,SAAS;IACnC/H,QAAQ,EAAE,SAASA,QAAQA,CAAC6G,CAAC,EAAE;MAC7BmB,sBAAsB,CAACnB,CAAC,CAACmD,MAAM,CAAC9M,KAAK,CAAC;IACxC,CAAC;IACDqB,SAAS,EAAEA,SAAS;IACpBD,WAAW,EAAE/C,QAAQ,CAAC+C,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE;IAC3CkQ,GAAG,EAAE3M;EACP,CAAC,EAAE0I,eAAe,EAAEuE,gBAAgB,EAAE;IACpCzN,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa1G,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;IAC5C1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,kBAAkB,CAAC;IACnD4Q,GAAG,EAAE5M;EACP,CAAC,EAAE9C,SAAS,CAAC,EAAE,aAAanE,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;IACrD1O,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAAC8R,MAAM,CAACjP,SAAS,EAAE,QAAQ,CAAC,GAAGF,YAAY,GAAG,CAAC,CAAC,EAAEjD,eAAe,CAACiD,YAAY,EAAE,EAAE,CAACmP,MAAM,CAACjP,SAAS,EAAE,eAAe,CAAC,EAAE0E,uBAAuB,KAAK,CAAC,CAAC,EAAE7H,eAAe,CAACiD,YAAY,EAAE,EAAE,CAACmP,MAAM,CAACjP,SAAS,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC0L,aAAa,CAAC,EAAE5L,YAAY,CAAC,CAAC;IACzR8Q,GAAG,EAAE7M;EACP,CAAC,EAAE,aAAahH,KAAK,CAAC8R,aAAa,CAAC,OAAO,EAAEnS,QAAQ,CAAC;IACpD8C,QAAQ,EAAEqF,cAAc,CAAC,CAAC,CAAC;IAC3BgN,QAAQ,EAAE5P,aAAa,IAAI,OAAOsC,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,IAAI,CAAC+I,SAAS;IAC5EhO,KAAK,EAAEoM,aAAa,IAAIlB,OAAO;IAC/BpI,QAAQ,EAAE,SAASA,QAAQA,CAAC6G,CAAC,EAAE;MAC7BwB,oBAAoB,CAACxB,CAAC,CAACmD,MAAM,CAAC9M,KAAK,CAAC;IACtC,CAAC;IACDoB,WAAW,EAAE/C,QAAQ,CAAC+C,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE;IAC3CkQ,GAAG,EAAE1M;EACP,CAAC,EAAEiJ,aAAa,EAAE+D,gBAAgB,EAAE;IAClCzN,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa1G,KAAK,CAAC8R,aAAa,CAAC,KAAK,EAAE;IAC5C1O,SAAS,EAAE,EAAE,CAAC8O,MAAM,CAACjP,SAAS,EAAE,aAAa,CAAC;IAC9CE,KAAK,EAAEtD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0U,sBAAsB,CAAC,EAAE,CAAC,CAAC,EAAE;MAClEQ,KAAK,EAAET,cAAc;MACrBU,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC,CAAC,EAAEhB,UAAU,EAAEC,SAAS,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;;AAGF,IAAIgB,WAAW,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACzDzV,SAAS,CAACwV,WAAW,EAAEC,gBAAgB,CAAC;EAExC,IAAIC,MAAM,GAAGzV,YAAY,CAACuV,WAAW,CAAC;EAEtC,SAASA,WAAWA,CAAA,EAAG;IACrB,IAAIG,KAAK;IAET7V,eAAe,CAAC,IAAI,EAAE0V,WAAW,CAAC;IAElCG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAE/D,SAAS,CAAC;IACrC8D,KAAK,CAACnQ,SAAS,GAAG,aAAajF,KAAK,CAACsV,SAAS,CAAC,CAAC;IAEhDF,KAAK,CAACjK,KAAK,GAAG,YAAY;MACxB,IAAIiK,KAAK,CAACnQ,SAAS,CAACyE,OAAO,EAAE;QAC3B0L,KAAK,CAACnQ,SAAS,CAACyE,OAAO,CAACyB,KAAK,CAAC,CAAC;MACjC;IACF,CAAC;IAEDiK,KAAK,CAACxE,IAAI,GAAG,YAAY;MACvB,IAAIwE,KAAK,CAACnQ,SAAS,CAACyE,OAAO,EAAE;QAC3B0L,KAAK,CAACnQ,SAAS,CAACyE,OAAO,CAACkH,IAAI,CAAC,CAAC;MAChC;IACF,CAAC;IAED,OAAOwE,KAAK;EACd;EAEA5V,YAAY,CAACyV,WAAW,EAAE,CAAC;IACzBM,GAAG,EAAE,QAAQ;IACbhT,KAAK,EAAE,SAASiT,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAaxV,KAAK,CAAC8R,aAAa,CAACnP,gBAAgB,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiD,KAAK,EAAE;QACjFqC,SAAS,EAAE,IAAI,CAACA;MAClB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAOgQ,WAAW;AACpB,CAAC,CAACjV,KAAK,CAACyV,SAAS,CAAC;AAElB,eAAeR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}