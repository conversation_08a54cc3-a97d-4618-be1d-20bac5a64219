export type Driver = import('./driver.js').Driver;
export type ArbitraryEqualityMap = import('../../lib/arbitrary-equality-map.js').ArbitraryEqualityMap;
/**
 * Each entry in each gatherer result array is the output of a gatherer phase:
 * `beforePass`, `pass`, and `afterPass`. Flattened into an `LH.Artifacts` in
 * `collectArtifacts`.
 */
export type GathererResults = Record<keyof LH.GathererArtifacts, Array<LH.Gatherer.PhaseResult>>;
export type GathererResultsEntries = Array<[keyof GathererResults, GathererResults[keyof GathererResults]]>;
/** @typedef {import('./driver.js').Driver} Driver */
/** @typedef {import('../../lib/arbitrary-equality-map.js').ArbitraryEqualityMap} ArbitraryEqualityMap */
/**
 * Each entry in each gatherer result array is the output of a gatherer phase:
 * `beforePass`, `pass`, and `afterPass`. Flattened into an `LH.Artifacts` in
 * `collectArtifacts`.
 * @typedef {Record<keyof LH.GathererArtifacts, Array<LH.Gatherer.PhaseResult>>} GathererResults
 */
/** @typedef {Array<[keyof GathererResults, GathererResults[keyof GathererResults]]>} GathererResultsEntries */
/**
 * Class that drives browser to load the page and runs gatherer lifecycle hooks.
 */
export class GatherRunner {
    /**
     * Loads about:blank and waits there briefly. Since a Page.reload command does
     * not let a service worker take over, we navigate away and then come back to
     * reload. We do not `waitForLoad` on about:blank since a page load event is
     * never fired on it.
     * @param {Driver} driver
     * @param {string=} url
     * @return {Promise<void>}
     */
    static loadBlank(driver: Driver, url?: string | undefined): Promise<void>;
    /**
     * Loads options.url with specified options. If the main document URL
     * redirects, options.url will be updated accordingly. As such, options.url
     * will always represent the post-redirected URL. options.requestedUrl is the
     * pre-redirect starting URL. If the navigation errors with "expected" errors such as
     * NO_FCP, a `navigationError` is returned.
     * @param {Driver} driver
     * @param {LH.Gatherer.PassContext} passContext
     * @return {Promise<{navigationError?: LH.LighthouseError}>}
     */
    static loadPage(driver: Driver, passContext: LH.Gatherer.PassContext): Promise<{
        navigationError?: LH.LighthouseError;
    }>;
    /**
     * Rejects if any open tabs would share a service worker with the target URL.
     * This includes the target tab, so navigation to something like about:blank
     * should be done before calling.
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {string} pageUrl
     * @return {Promise<void>}
     */
    static assertNoSameOriginServiceWorkerClients(session: LH.Gatherer.FRProtocolSession, pageUrl: string): Promise<void>;
    /**
     * @param {Driver} driver
     * @param {{requestedUrl: string, settings: LH.Config.Settings}} options
     * @return {Promise<void>}
     */
    static setupDriver(driver: Driver, options: {
        requestedUrl: string;
        settings: LH.Config.Settings;
    }): Promise<void>;
    /**
     * Reset browser state where needed and release the connection.
     * @param {Driver} driver
     * @param {{requestedUrl: string, settings: LH.Config.Settings}} options
     * @return {Promise<void>}
     */
    static disposeDriver(driver: Driver, options: {
        requestedUrl: string;
        settings: LH.Config.Settings;
    }): Promise<void>;
    /**
     * Beging recording devtoolsLog and trace (if requested).
     * @param {LH.Gatherer.PassContext} passContext
     * @return {Promise<void>}
     */
    static beginRecording(passContext: LH.Gatherer.PassContext): Promise<void>;
    /**
     * End recording devtoolsLog and trace (if requested), returning an
     * `LH.Gatherer.LoadData` with the recorded data.
     * @param {LH.Gatherer.PassContext} passContext
     * @return {Promise<LH.Gatherer.LoadData>}
     */
    static endRecording(passContext: LH.Gatherer.PassContext): Promise<LH.Gatherer.LoadData>;
    /**
     * Run beforePass() on gatherers.
     * @param {LH.Gatherer.PassContext} passContext
     * @param {Partial<GathererResults>} gathererResults
     * @return {Promise<void>}
     */
    static beforePass(passContext: LH.Gatherer.PassContext, gathererResults: Partial<GathererResults>): Promise<void>;
    /**
     * Run pass() on gatherers.
     * @param {LH.Gatherer.PassContext} passContext
     * @param {Partial<GathererResults>} gathererResults
     * @return {Promise<void>}
     */
    static pass(passContext: LH.Gatherer.PassContext, gathererResults: Partial<GathererResults>): Promise<void>;
    /**
     * Run afterPass() on gatherers.
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @param {Partial<GathererResults>} gathererResults
     * @return {Promise<void>}
     */
    static afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData, gathererResults: Partial<GathererResults>): Promise<void>;
    /**
     * Takes the results of each gatherer phase for each gatherer and uses the
     * last produced value (that's not undefined) as the artifact for that
     * gatherer. If an error was rejected from a gatherer phase,
     * uses that error object as the artifact instead.
     * @param {Partial<GathererResults>} gathererResults
     * @return {Promise<{artifacts: Partial<LH.GathererArtifacts>}>}
     */
    static collectArtifacts(gathererResults: Partial<GathererResults>): Promise<{
        artifacts: Partial<LH.GathererArtifacts>;
    }>;
    /**
     * Return an initialized but mostly empty set of base artifacts, to be
     * populated as the run continues.
     * @param {{driver: Driver, requestedUrl: string, settings: LH.Config.Settings}} options
     * @return {Promise<LH.BaseArtifacts>}
     */
    static initializeBaseArtifacts(options: {
        driver: Driver;
        requestedUrl: string;
        settings: LH.Config.Settings;
    }): Promise<LH.BaseArtifacts>;
    /**
     * Populates the important base artifacts from a fully loaded test page.
     * Currently must be run before `start-url` gatherer so that `WebAppManifest`
     * will be available to it.
     * @param {LH.Gatherer.PassContext} passContext
     */
    static populateBaseArtifacts(passContext: LH.Gatherer.PassContext): Promise<void>;
    /**
     * @param {Array<LH.Config.Pass>} passConfigs
     * @param {{driver: Driver, requestedUrl: string, settings: LH.Config.Settings, computedCache: Map<string, ArbitraryEqualityMap>}} options
     * @return {Promise<LH.Artifacts>}
     */
    static run(passConfigs: Array<LH.Config.Pass>, options: {
        driver: Driver;
        requestedUrl: string;
        settings: LH.Config.Settings;
        computedCache: Map<string, ArbitraryEqualityMap>;
    }): Promise<LH.Artifacts>;
    /**
     * Save the devtoolsLog and trace (if applicable) to baseArtifacts.
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @param {string} passName
     */
    static _addLoadDataToBaseArtifacts(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData, passName: string): void;
    /**
     * Starting from about:blank, load the page and run gatherers for this pass.
     * @param {LH.Gatherer.PassContext} passContext
     * @return {Promise<{artifacts: Partial<LH.GathererArtifacts>, pageLoadError?: LH.LighthouseError}>}
     */
    static runPass(passContext: LH.Gatherer.PassContext): Promise<{
        artifacts: Partial<LH.GathererArtifacts>;
        pageLoadError?: import("../../lib/lh-error.js").LighthouseError | undefined;
    }>;
}
//# sourceMappingURL=gather-runner.d.ts.map