{"ast": null, "code": "import { Col } from '../grid';\nexport default Col;", "map": {"version": 3, "names": ["Col"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/col/index.js"], "sourcesContent": ["import { Col } from '../grid';\nexport default Col;"], "mappings": "AAAA,SAASA,GAAG,QAAQ,SAAS;AAC7B,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}