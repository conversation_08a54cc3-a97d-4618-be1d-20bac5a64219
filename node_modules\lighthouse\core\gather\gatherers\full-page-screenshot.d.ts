export default FullPageScreenshot;
declare class FullPageScreenshot extends FR<PERSON>atherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {{height: number, width: number, mobile: boolean}} deviceMetrics
     */
    _resizeViewport(context: LH.Gatherer.FRTransitionalContext, deviceMetrics: {
        height: number;
        width: number;
        mobile: boolean;
    }): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Result.FullPageScreenshot['screenshot']>}
     */
    _takeScreenshot(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Result.FullPageScreenshot['screenshot']>;
    /**
     * Gatherers can collect details about DOM nodes, including their position on the page.
     * Layout shifts occuring after a gatherer runs can cause these positions to be incorrect,
     * resulting in a poor experience for element screenshots.
     * `getNodeDetails` maintains a collection of DOM objects in the page, which we can iterate
     * to re-collect the bounding client rectangle.
     * @see pageFunctions.getNodeDetails
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Result.FullPageScreenshot['nodes']>}
     */
    _resolveNodes(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Result.FullPageScreenshot['nodes']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['FullPageScreenshot']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['FullPageScreenshot']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=full-page-screenshot.d.ts.map