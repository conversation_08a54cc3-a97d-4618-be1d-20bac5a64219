{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport * as React from 'react';\nimport { useRef, useImperativeHandle, forwardRef } from 'react';\nimport Trigger from 'rc-trigger';\nimport { placements } from './placements';\nimport Content from './Content';\nvar Tooltip = function Tooltip(props, ref) {\n  var overlayClassName = props.overlayClassName,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    _props$mouseEnterDela = props.mouseEnterDelay,\n    mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,\n    _props$mouseLeaveDela = props.mouseLeaveDelay,\n    mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n    overlayStyle = props.overlayStyle,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    afterVisibleChange = props.afterVisibleChange,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    motion = props.motion,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$align = props.align,\n    align = _props$align === void 0 ? {} : _props$align,\n    _props$destroyTooltip = props.destroyTooltipOnHide,\n    destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,\n    defaultVisible = props.defaultVisible,\n    getTooltipContainer = props.getTooltipContainer,\n    overlayInnerStyle = props.overlayInnerStyle,\n    restProps = _objectWithoutProperties(props, [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"children\", \"onVisibleChange\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"motion\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\", \"getTooltipContainer\", \"overlayInnerStyle\"]);\n  var domRef = useRef(null);\n  useImperativeHandle(ref, function () {\n    return domRef.current;\n  });\n  var extraProps = _objectSpread({}, restProps);\n  if ('visible' in props) {\n    extraProps.popupVisible = props.visible;\n  }\n  var getPopupElement = function getPopupElement() {\n    var _props$arrowContent = props.arrowContent,\n      arrowContent = _props$arrowContent === void 0 ? null : _props$arrowContent,\n      overlay = props.overlay,\n      id = props.id;\n    return [/*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-arrow\"),\n      key: \"arrow\"\n    }, arrowContent), /*#__PURE__*/React.createElement(Content, {\n      key: \"content\",\n      prefixCls: prefixCls,\n      id: id,\n      overlay: overlay,\n      overlayInnerStyle: overlayInnerStyle\n    })];\n  };\n  var destroyTooltip = false;\n  var autoDestroy = false;\n  if (typeof destroyTooltipOnHide === 'boolean') {\n    destroyTooltip = destroyTooltipOnHide;\n  } else if (destroyTooltipOnHide && _typeof(destroyTooltipOnHide) === 'object') {\n    var keepParent = destroyTooltipOnHide.keepParent;\n    destroyTooltip = keepParent === true;\n    autoDestroy = keepParent === false;\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    popupClassName: overlayClassName,\n    prefixCls: prefixCls,\n    popup: getPopupElement,\n    action: trigger,\n    builtinPlacements: placements,\n    popupPlacement: placement,\n    ref: domRef,\n    popupAlign: align,\n    getPopupContainer: getTooltipContainer,\n    onPopupVisibleChange: onVisibleChange,\n    afterPopupVisibleChange: afterVisibleChange,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupMotion: motion,\n    defaultPopupVisible: defaultVisible,\n    destroyPopupOnHide: destroyTooltip,\n    autoDestroy: autoDestroy,\n    mouseLeaveDelay: mouseLeaveDelay,\n    popupStyle: overlayStyle,\n    mouseEnterDelay: mouseEnterDelay\n  }, extraProps), children);\n};\nexport default /*#__PURE__*/forwardRef(Tooltip);", "map": {"version": 3, "names": ["_extends", "_typeof", "_objectSpread", "_objectWithoutProperties", "React", "useRef", "useImperativeHandle", "forwardRef", "<PERSON><PERSON>", "placements", "Content", "<PERSON><PERSON><PERSON>", "props", "ref", "overlayClassName", "_props$trigger", "trigger", "_props$mouseEnterDela", "mouseEnterDelay", "_props$mouseLeaveDela", "mouseLeaveDelay", "overlayStyle", "_props$prefixCls", "prefixCls", "children", "onVisibleChange", "afterVisibleChange", "transitionName", "animation", "motion", "_props$placement", "placement", "_props$align", "align", "_props$destroyTooltip", "destroyTooltipOnHide", "defaultVisible", "getTooltipContainer", "overlayInnerStyle", "restProps", "domRef", "current", "extraProps", "popupVisible", "visible", "getPopupElement", "_props$arrowContent", "arrow<PERSON>ontent", "overlay", "id", "createElement", "className", "concat", "key", "destroyTooltip", "autoDestroy", "keepParent", "popupClassName", "popup", "action", "builtinPlacements", "popupPlacement", "popupAlign", "getPopupContainer", "onPopupVisibleChange", "afterPopupVisibleChange", "popupTransitionName", "popupAnimation", "popupMotion", "defaultPopupVisible", "destroyPopupOnHide", "popupStyle"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tooltip/es/Tooltip.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport * as React from 'react';\nimport { useRef, useImperativeHandle, forwardRef } from 'react';\nimport Trigger from 'rc-trigger';\nimport { placements } from './placements';\nimport Content from './Content';\n\nvar Tooltip = function Tooltip(props, ref) {\n  var overlayClassName = props.overlayClassName,\n      _props$trigger = props.trigger,\n      trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n      _props$mouseEnterDela = props.mouseEnterDelay,\n      mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,\n      _props$mouseLeaveDela = props.mouseLeaveDelay,\n      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n      overlayStyle = props.overlayStyle,\n      _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,\n      children = props.children,\n      onVisibleChange = props.onVisibleChange,\n      afterVisibleChange = props.afterVisibleChange,\n      transitionName = props.transitionName,\n      animation = props.animation,\n      motion = props.motion,\n      _props$placement = props.placement,\n      placement = _props$placement === void 0 ? 'right' : _props$placement,\n      _props$align = props.align,\n      align = _props$align === void 0 ? {} : _props$align,\n      _props$destroyTooltip = props.destroyTooltipOnHide,\n      destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,\n      defaultVisible = props.defaultVisible,\n      getTooltipContainer = props.getTooltipContainer,\n      overlayInnerStyle = props.overlayInnerStyle,\n      restProps = _objectWithoutProperties(props, [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"children\", \"onVisibleChange\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"motion\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\", \"getTooltipContainer\", \"overlayInnerStyle\"]);\n\n  var domRef = useRef(null);\n  useImperativeHandle(ref, function () {\n    return domRef.current;\n  });\n\n  var extraProps = _objectSpread({}, restProps);\n\n  if ('visible' in props) {\n    extraProps.popupVisible = props.visible;\n  }\n\n  var getPopupElement = function getPopupElement() {\n    var _props$arrowContent = props.arrowContent,\n        arrowContent = _props$arrowContent === void 0 ? null : _props$arrowContent,\n        overlay = props.overlay,\n        id = props.id;\n    return [/*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-arrow\"),\n      key: \"arrow\"\n    }, arrowContent), /*#__PURE__*/React.createElement(Content, {\n      key: \"content\",\n      prefixCls: prefixCls,\n      id: id,\n      overlay: overlay,\n      overlayInnerStyle: overlayInnerStyle\n    })];\n  };\n\n  var destroyTooltip = false;\n  var autoDestroy = false;\n\n  if (typeof destroyTooltipOnHide === 'boolean') {\n    destroyTooltip = destroyTooltipOnHide;\n  } else if (destroyTooltipOnHide && _typeof(destroyTooltipOnHide) === 'object') {\n    var keepParent = destroyTooltipOnHide.keepParent;\n    destroyTooltip = keepParent === true;\n    autoDestroy = keepParent === false;\n  }\n\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    popupClassName: overlayClassName,\n    prefixCls: prefixCls,\n    popup: getPopupElement,\n    action: trigger,\n    builtinPlacements: placements,\n    popupPlacement: placement,\n    ref: domRef,\n    popupAlign: align,\n    getPopupContainer: getTooltipContainer,\n    onPopupVisibleChange: onVisibleChange,\n    afterPopupVisibleChange: afterVisibleChange,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupMotion: motion,\n    defaultPopupVisible: defaultVisible,\n    destroyPopupOnHide: destroyTooltip,\n    autoDestroy: autoDestroy,\n    mouseLeaveDelay: mouseLeaveDelay,\n    popupStyle: overlayStyle,\n    mouseEnterDelay: mouseEnterDelay\n  }, extraProps), children);\n};\n\nexport default /*#__PURE__*/forwardRef(Tooltip);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,OAAO;AAC/D,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,OAAO,MAAM,WAAW;AAE/B,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzC,IAAIC,gBAAgB,GAAGF,KAAK,CAACE,gBAAgB;IACzCC,cAAc,GAAGH,KAAK,CAACI,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGA,cAAc;IAChEE,qBAAqB,GAAGL,KAAK,CAACM,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAC9EE,qBAAqB,GAAGP,KAAK,CAACQ,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IAChFE,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,gBAAgB,GAAGV,KAAK,CAACW,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEE,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,eAAe,GAAGb,KAAK,CAACa,eAAe;IACvCC,kBAAkB,GAAGd,KAAK,CAACc,kBAAkB;IAC7CC,cAAc,GAAGf,KAAK,CAACe,cAAc;IACrCC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,gBAAgB,GAAGlB,KAAK,CAACmB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,gBAAgB;IACpEE,YAAY,GAAGpB,KAAK,CAACqB,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,qBAAqB,GAAGtB,KAAK,CAACuB,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACvFE,cAAc,GAAGxB,KAAK,CAACwB,cAAc;IACrCC,mBAAmB,GAAGzB,KAAK,CAACyB,mBAAmB;IAC/CC,iBAAiB,GAAG1B,KAAK,CAAC0B,iBAAiB;IAC3CC,SAAS,GAAGpC,wBAAwB,CAACS,KAAK,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;EAE7V,IAAI4B,MAAM,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACzBC,mBAAmB,CAACO,GAAG,EAAE,YAAY;IACnC,OAAO2B,MAAM,CAACC,OAAO;EACvB,CAAC,CAAC;EAEF,IAAIC,UAAU,GAAGxC,aAAa,CAAC,CAAC,CAAC,EAAEqC,SAAS,CAAC;EAE7C,IAAI,SAAS,IAAI3B,KAAK,EAAE;IACtB8B,UAAU,CAACC,YAAY,GAAG/B,KAAK,CAACgC,OAAO;EACzC;EAEA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIC,mBAAmB,GAAGlC,KAAK,CAACmC,YAAY;MACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;MAC1EE,OAAO,GAAGpC,KAAK,CAACoC,OAAO;MACvBC,EAAE,GAAGrC,KAAK,CAACqC,EAAE;IACjB,OAAO,CAAC,aAAa7C,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAE;MAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC7B,SAAS,EAAE,QAAQ,CAAC;MACzC8B,GAAG,EAAE;IACP,CAAC,EAAEN,YAAY,CAAC,EAAE,aAAa3C,KAAK,CAAC8C,aAAa,CAACxC,OAAO,EAAE;MAC1D2C,GAAG,EAAE,SAAS;MACd9B,SAAS,EAAEA,SAAS;MACpB0B,EAAE,EAAEA,EAAE;MACND,OAAO,EAAEA,OAAO;MAChBV,iBAAiB,EAAEA;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAIgB,cAAc,GAAG,KAAK;EAC1B,IAAIC,WAAW,GAAG,KAAK;EAEvB,IAAI,OAAOpB,oBAAoB,KAAK,SAAS,EAAE;IAC7CmB,cAAc,GAAGnB,oBAAoB;EACvC,CAAC,MAAM,IAAIA,oBAAoB,IAAIlC,OAAO,CAACkC,oBAAoB,CAAC,KAAK,QAAQ,EAAE;IAC7E,IAAIqB,UAAU,GAAGrB,oBAAoB,CAACqB,UAAU;IAChDF,cAAc,GAAGE,UAAU,KAAK,IAAI;IACpCD,WAAW,GAAGC,UAAU,KAAK,KAAK;EACpC;EAEA,OAAO,aAAapD,KAAK,CAAC8C,aAAa,CAAC1C,OAAO,EAAER,QAAQ,CAAC;IACxDyD,cAAc,EAAE3C,gBAAgB;IAChCS,SAAS,EAAEA,SAAS;IACpBmC,KAAK,EAAEb,eAAe;IACtBc,MAAM,EAAE3C,OAAO;IACf4C,iBAAiB,EAAEnD,UAAU;IAC7BoD,cAAc,EAAE9B,SAAS;IACzBlB,GAAG,EAAE2B,MAAM;IACXsB,UAAU,EAAE7B,KAAK;IACjB8B,iBAAiB,EAAE1B,mBAAmB;IACtC2B,oBAAoB,EAAEvC,eAAe;IACrCwC,uBAAuB,EAAEvC,kBAAkB;IAC3CwC,mBAAmB,EAAEvC,cAAc;IACnCwC,cAAc,EAAEvC,SAAS;IACzBwC,WAAW,EAAEvC,MAAM;IACnBwC,mBAAmB,EAAEjC,cAAc;IACnCkC,kBAAkB,EAAEhB,cAAc;IAClCC,WAAW,EAAEA,WAAW;IACxBnC,eAAe,EAAEA,eAAe;IAChCmD,UAAU,EAAElD,YAAY;IACxBH,eAAe,EAAEA;EACnB,CAAC,EAAEwB,UAAU,CAAC,EAAElB,QAAQ,CAAC;AAC3B,CAAC;AAED,eAAe,aAAajB,UAAU,CAACI,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}