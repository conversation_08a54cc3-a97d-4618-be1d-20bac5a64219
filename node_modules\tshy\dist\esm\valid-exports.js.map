{"version": 3, "file": "valid-exports.js", "sourceRoot": "", "sources": ["../../src/valid-exports.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,cAAc,CAAA;AACjC,OAAO,IAAI,MAAM,WAAW,CAAA;AAE5B,OAAO,mBAAmB,MAAM,4BAA4B,CAAA;AAE5D,eAAe,CACb,CAAM,EAC0C,EAAE;IAClD,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAAE,OAAO,KAAK,CAAA;IACjE,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,IAAI,CACF,yDAAyD,GAAG,EAAE,CAC/D,CAAA;YACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QAED,mEAAmE;QACnE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACpB,SAAQ;QACV,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,IAAI,CACF,gBAAgB,GAAG,4CAA4C;gBAC7D,eAAe,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CACvC,CAAA;YACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QAED,0DAA0D;QAC1D,0CAA0C;QAC1C,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CACF,gBAAgB,GAAG,yCAAyC;gBAC1D,4CAA4C;gBAC5C,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAChC,CAAA;YACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QAED,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;IACd,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["import addDot from './add-dot.js'\nimport fail from './fail.js'\nimport { TshyConfig } from './types.js'\nimport validExternalExport from './valid-external-export.js'\n\nexport default (\n  e: any,\n): e is Exclude<TshyConfig['exports'], undefined> => {\n  if (!e || typeof e !== 'object' || Array.isArray(e)) return false\n  for (const [sub, exp] of Object.entries(e)) {\n    if (sub !== '.' && !sub.startsWith('./')) {\n      fail(\n        `tshy.exports key must be \".\" or start with \"./\", got: ${sub}`,\n      )\n      return process.exit(1)\n    }\n\n    // just a module. either a built export, or a simple unbuilt export\n    if (typeof exp === 'string') {\n      e[sub] = addDot(exp)\n      continue\n    }\n\n    if (typeof exp !== 'object') {\n      fail(\n        `tshy.exports ${sub} value must be valid package.json exports ` +\n          `value, got: ${JSON.stringify(exp)}`,\n      )\n      return process.exit(1)\n    }\n\n    // can be any valid external export, but the resolution of\n    // import and require must NOT be in ./src\n    if (!validExternalExport(exp)) {\n      fail(\n        `tshy.exports ${sub} unbuilt exports must not be in ./src, ` +\n          `and exports in src must be string values. ` +\n          `got: ${JSON.stringify(exp)}`,\n      )\n      return process.exit(1)\n    }\n\n    e[sub] = exp\n  }\n  return true\n}\n"]}