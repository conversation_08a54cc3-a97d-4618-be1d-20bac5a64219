{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nvar calcThumbStyle = function calcThumbStyle(targetElement) {\n  return targetElement ? {\n    left: targetElement.offsetLeft,\n    width: targetElement.clientWidth\n  } : null;\n};\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n    containerRef = props.containerRef,\n    value = props.value,\n    getValueIndex = props.getValueIndex,\n    motionName = props.motionName,\n    onMotionStart = props.onMotionStart,\n    onMotionEnd = props.onMotionEnd;\n  var thumbRef = React.useRef(null);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1]; // =========================== Effect ===========================\n\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return ele;\n  };\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevStyle = _React$useState4[0],\n    setPrevStyle = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    nextStyle = _React$useState6[0],\n    setNextStyle = _React$useState6[1];\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev);\n      var calcNextStyle = calcThumbStyle(next);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]); // =========================== Motion ===========================\n\n  var onAppearStart = function onAppearStart() {\n    return {\n      transform: \"translateX(var(--thumb-start-left))\",\n      width: \"var(--thumb-start-width)\"\n    };\n  };\n  var onAppearActive = function onAppearActive() {\n    return {\n      transform: \"translateX(var(--thumb-active-left))\",\n      width: \"var(--thumb-active-width)\"\n    };\n  };\n  var onAppearEnd = function onAppearEnd() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  }; // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onAppearEnd: onAppearEnd\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left),\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left),\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width)\n    }); // It's little ugly which should be refactor when @umi/test update to latest jsdom\n\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _objectSpread({}, motionProps));\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "React", "CSSMotion", "classNames", "useLayoutEffect", "composeRef", "calcThumbStyle", "targetElement", "left", "offsetLeft", "width", "clientWidth", "toPX", "value", "undefined", "concat", "MotionThumb", "props", "prefixCls", "containerRef", "getValueIndex", "motionName", "onMotionStart", "onMotionEnd", "thumbRef", "useRef", "_React$useState", "useState", "_React$useState2", "prevValue", "setPrevValue", "findValueElement", "val", "_containerRef$current", "index", "ele", "current", "querySelectorAll", "_React$useState3", "_React$useState4", "prevStyle", "setPrevStyle", "_React$useState5", "_React$useState6", "nextStyle", "setNextStyle", "prev", "next", "calcPrevStyle", "calcNextStyle", "onAppearStart", "transform", "onAppearActive", "onAppearEnd", "createElement", "visible", "motionAppear", "_ref", "ref", "motionClassName", "className", "motionStyle", "style", "mergedStyle", "motionProps", "process", "env", "NODE_ENV", "JSON", "stringify"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-segmented/es/MotionThumb.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\n\nvar calcThumbStyle = function calcThumbStyle(targetElement) {\n  return targetElement ? {\n    left: targetElement.offsetLeft,\n    width: targetElement.clientWidth\n  } : null;\n};\n\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\n\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n      containerRef = props.containerRef,\n      value = props.value,\n      getValueIndex = props.getValueIndex,\n      motionName = props.motionName,\n      onMotionStart = props.onMotionStart,\n      onMotionEnd = props.onMotionEnd;\n  var thumbRef = React.useRef(null);\n\n  var _React$useState = React.useState(value),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      prevValue = _React$useState2[0],\n      setPrevValue = _React$useState2[1]; // =========================== Effect ===========================\n\n\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return ele;\n  };\n\n  var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      prevStyle = _React$useState4[0],\n      setPrevStyle = _React$useState4[1];\n\n  var _React$useState5 = React.useState(null),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      nextStyle = _React$useState6[0],\n      setNextStyle = _React$useState6[1];\n\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev);\n      var calcNextStyle = calcThumbStyle(next);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]); // =========================== Motion ===========================\n\n  var onAppearStart = function onAppearStart() {\n    return {\n      transform: \"translateX(var(--thumb-start-left))\",\n      width: \"var(--thumb-start-width)\"\n    };\n  };\n\n  var onAppearActive = function onAppearActive() {\n    return {\n      transform: \"translateX(var(--thumb-active-left))\",\n      width: \"var(--thumb-active-width)\"\n    };\n  };\n\n  var onAppearEnd = function onAppearEnd() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  }; // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n\n\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onAppearEnd: onAppearEnd\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left),\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left),\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width)\n    }); // It's little ugly which should be refactor when @umi/test update to latest jsdom\n\n\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n\n    return /*#__PURE__*/React.createElement(\"div\", _objectSpread({}, motionProps));\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAE3C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,aAAa,EAAE;EAC1D,OAAOA,aAAa,GAAG;IACrBC,IAAI,EAAED,aAAa,CAACE,UAAU;IAC9BC,KAAK,EAAEH,aAAa,CAACI;EACvB,CAAC,GAAG,IAAI;AACV,CAAC;AAED,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAKC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACF,KAAK,EAAE,IAAI,CAAC,GAAGC,SAAS;AACjE,CAAC;AAED,eAAe,SAASE,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCN,KAAK,GAAGI,KAAK,CAACJ,KAAK;IACnBO,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,WAAW,GAAGN,KAAK,CAACM,WAAW;EACnC,IAAIC,QAAQ,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAEjC,IAAIC,eAAe,GAAGzB,KAAK,CAAC0B,QAAQ,CAACd,KAAK,CAAC;IACvCe,gBAAgB,GAAG5B,cAAc,CAAC0B,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGxC,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,GAAG,EAAE;IACpD,IAAIC,qBAAqB;IAEzB,IAAIC,KAAK,GAAGd,aAAa,CAACY,GAAG,CAAC;IAC9B,IAAIG,GAAG,GAAG,CAACF,qBAAqB,GAAGd,YAAY,CAACiB,OAAO,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,gBAAgB,CAAC,GAAG,CAACtB,MAAM,CAACG,SAAS,EAAE,OAAO,CAAC,CAAC,CAACgB,KAAK,CAAC;IAC9L,OAAOC,GAAG;EACZ,CAAC;EAED,IAAIG,gBAAgB,GAAGrC,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAAC;IACvCY,gBAAgB,GAAGvC,cAAc,CAACsC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,gBAAgB,GAAGzC,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAAC;IACvCgB,gBAAgB,GAAG3C,cAAc,CAAC0C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtCvC,eAAe,CAAC,YAAY;IAC1B,IAAIyB,SAAS,KAAKhB,KAAK,EAAE;MACvB,IAAIiC,IAAI,GAAGf,gBAAgB,CAACF,SAAS,CAAC;MACtC,IAAIkB,IAAI,GAAGhB,gBAAgB,CAAClB,KAAK,CAAC;MAClC,IAAImC,aAAa,GAAG1C,cAAc,CAACwC,IAAI,CAAC;MACxC,IAAIG,aAAa,GAAG3C,cAAc,CAACyC,IAAI,CAAC;MACxCjB,YAAY,CAACjB,KAAK,CAAC;MACnB4B,YAAY,CAACO,aAAa,CAAC;MAC3BH,YAAY,CAACI,aAAa,CAAC;MAE3B,IAAIH,IAAI,IAAIC,IAAI,EAAE;QAChBzB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLC,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,EAAE,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEb,IAAIqC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,OAAO;MACLC,SAAS,EAAE,qCAAqC;MAChDzC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EAED,IAAI0C,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAO;MACLD,SAAS,EAAE,sCAAsC;MACjDzC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EAED,IAAI2C,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCZ,YAAY,CAAC,IAAI,CAAC;IAClBI,YAAY,CAAC,IAAI,CAAC;IAClBtB,WAAW,CAAC,CAAC;EACf,CAAC,CAAC,CAAC;EACH;;EAGA,IAAI,CAACiB,SAAS,IAAI,CAACI,SAAS,EAAE;IAC5B,OAAO,IAAI;EACb;EAEA,OAAO,aAAa3C,KAAK,CAACqD,aAAa,CAACpD,SAAS,EAAE;IACjDqD,OAAO,EAAE,IAAI;IACblC,UAAU,EAAEA,UAAU;IACtBmC,YAAY,EAAE,IAAI;IAClBN,aAAa,EAAEA,aAAa;IAC5BE,cAAc,EAAEA,cAAc;IAC9BC,WAAW,EAAEA;EACf,CAAC,EAAE,UAAUI,IAAI,EAAEC,GAAG,EAAE;IACtB,IAAIC,eAAe,GAAGF,IAAI,CAACG,SAAS;MAChCC,WAAW,GAAGJ,IAAI,CAACK,KAAK;IAE5B,IAAIC,WAAW,GAAGhE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MAClE,oBAAoB,EAAEjD,IAAI,CAAC4B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAChC,IAAI,CAAC;MAChG,qBAAqB,EAAEI,IAAI,CAAC4B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC9B,KAAK,CAAC;MAClG,qBAAqB,EAAEE,IAAI,CAACgC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACpC,IAAI,CAAC;MACjG,sBAAsB,EAAEI,IAAI,CAACgC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAClC,KAAK;IACpG,CAAC,CAAC,CAAC,CAAC;;IAGJ,IAAIsD,WAAW,GAAG;MAChBN,GAAG,EAAErD,UAAU,CAACmB,QAAQ,EAAEkC,GAAG,CAAC;MAC9BI,KAAK,EAAEC,WAAW;MAClBH,SAAS,EAAEzD,UAAU,CAAC,EAAE,CAACY,MAAM,CAACG,SAAS,EAAE,QAAQ,CAAC,EAAEyC,eAAe;IACvE,CAAC;IAED,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCH,WAAW,CAAC,iBAAiB,CAAC,GAAGI,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC;IAC9D;IAEA,OAAO,aAAa9D,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAEvD,aAAa,CAAC,CAAC,CAAC,EAAEiE,WAAW,CAAC,CAAC;EAChF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}