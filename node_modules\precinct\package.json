{"name": "precinct", "version": "11.0.5", "description": "Unleash the detectives", "main": "index.js", "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "bin": {"precinct": "bin/cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-precinct.git"}, "keywords": ["modules", "amd", "commonjs", "es6", "sass", "less", "detective", "dependencies"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-precinct/issues"}, "engines": {"node": "^14.14.0 || >=16.0.0"}, "files": ["bin/cli.js", "index.js"], "homepage": "https://github.com/dependents/node-precinct", "dependencies": {"@dependents/detective-less": "^4.1.0", "commander": "^10.0.1", "detective-amd": "^5.0.2", "detective-cjs": "^5.0.1", "detective-es6": "^4.0.1", "detective-postcss": "^6.1.3", "detective-sass": "^5.0.3", "detective-scss": "^4.0.3", "detective-stylus": "^4.0.0", "detective-typescript": "^11.1.0", "module-definition": "^5.0.1", "node-source-walk": "^6.0.2"}, "devDependencies": {"c8": "^7.13.0", "eslint": "^8.41.0", "mocha": "^10.2.0", "rewire": "^6.0.0", "sinon": "^15.1.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}