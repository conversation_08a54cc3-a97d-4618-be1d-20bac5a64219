{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\navigation\\\\Nav.js\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Nav - nav generalizzato \n*\n*/\nimport React, { Component } from 'react';\nimport logo from '../../img/tm_logo-01.svg';\nimport Menu from '../generalizzazioni/menuSettings';\nimport MenuMobile from '../generalizzazioni/menuMobile';\nimport CartIcon from '../../common/PDV/listino/cartIcon';\nimport { Link as NavLink } from 'react-router-dom';\nimport { Traduttore } from '../../components/traduttore/traduttore';\n// import NotificationCenter from '../notifications/NotificationCenter.jsx'; // TODO: Attivare dopo test\nimport { Clock } from '../../components/dataOra';\nimport { getNumbers } from '../../common/PDV/carrello/actions/getAction';\nimport { connect } from 'react-redux';\nimport { basePath, pdv, pdvDashboard } from '../route';\nimport '../../css/nav.css';\nimport 'primeicons/primeicons.css';\nimport 'primereact/resources/themes/saga-blue/theme.css';\nimport 'primereact/resources/primereact.css';\nimport 'primeflex/primeflex.css';\nimport '../../css/header.css';\nimport '../../css/frontend.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass Nav extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      display: 'd-none',\n      url: ''\n    };\n  }\n  componentDidMount() {\n    if (localStorage.getItem('role') === pdv) {\n      this.setState({\n        url: pdvDashboard\n      });\n    } else {\n      this.setState({\n        url: basePath\n      });\n    }\n    if (this.props.disabled === false) {\n      this.setState({\n        display: ''\n      });\n    } else if (this.props.disabled === true) {\n      this.setState({\n        display: ''\n      });\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row debugBar mx-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 d-flex justify-content-center userRoleBar\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"Role\",\n            children: [\"\\u2022\\u2022\\u2022 \", localStorage.getItem(\"role\"), \" \\u2022\\u2022\\u2022\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 87\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"navbar navbar-dark d-block sticky-top px-3 py-0 py-md-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 col-sm-4 col-md-3 col-lg-2 col-xl-2 d-flex justify-content-center justify-content-sm-start align-items-center my-3 my-sm-0\",\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              className: \"navbar-brand w-100 mx-0 p-0\",\n              to: this.state.url,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: logo,\n                onError: e => e.target.src = logo,\n                alt: \"Winet e-procurement logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 col-sm-8 col-md-9 col-lg-10 col-xl-10 d-block d-sm-flex justify-content-end align-self-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loginBox justify-content-end\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-none d-md-block\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Traduttore, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 33\n              }, this), this.props.visibility !== false && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(MenuMobile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: this.state.display,\n                children: /*#__PURE__*/_jsxDEV(CartIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true);\n  }\n}\nconst mapStateToProps = state => ({\n  productProps: state.productState\n});\nexport default connect(mapStateToProps, {\n  getNumbers\n})(Nav);", "map": {"version": 3, "names": ["React", "Component", "logo", "<PERSON><PERSON>", "MenuMobile", "CartIcon", "Link", "NavLink", "<PERSON><PERSON><PERSON><PERSON>", "Clock", "getNumbers", "connect", "basePath", "pdv", "pdvDashboard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Nav", "constructor", "props", "state", "display", "url", "componentDidMount", "localStorage", "getItem", "setState", "disabled", "render", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "onError", "e", "target", "alt", "visibility", "mapStateToProps", "productProps", "productState"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/navigation/Nav.js"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Nav - nav generalizzato \n*\n*/\nimport React, { Component } from 'react';\nimport logo from '../../img/tm_logo-01.svg'\nimport Menu from '../generalizzazioni/menuSettings';\nimport MenuMobile from '../generalizzazioni/menuMobile';\nimport CartIcon from '../../common/PDV/listino/cartIcon';\nimport { Link as NavLink } from 'react-router-dom';\nimport { Traduttore } from '../../components/traduttore/traduttore';\n// import NotificationCenter from '../notifications/NotificationCenter.jsx'; // TODO: Attivare dopo test\nimport { Clock } from '../../components/dataOra';\nimport { getNumbers } from '../../common/PDV/carrello/actions/getAction';\nimport { connect } from 'react-redux';\nimport {\n    basePath,\n    pdv,\n    pdvDashboard\n} from '../route';\nimport '../../css/nav.css';\nimport 'primeicons/primeicons.css';\nimport 'primereact/resources/themes/saga-blue/theme.css';\nimport 'primereact/resources/primereact.css';\nimport 'primeflex/primeflex.css';\nimport '../../css/header.css';\nimport '../../css/frontend.css';\n\n\nclass Nav extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            display: 'd-none',\n            url: ''\n        }\n    }\n    componentDidMount() {\n        if (localStorage.getItem('role') === pdv) {\n            this.setState({\n                url: pdvDashboard\n            })\n        } else {\n            this.setState({\n                url: basePath\n            })\n        }\n        if (this.props.disabled === false) {\n            this.setState({\n                display: '',\n            })\n        } else if (this.props.disabled === true) {\n            this.setState({\n                display: '',\n            })\n        }\n    }\n    render() {\n        return (\n            <>\n                <div className='row debugBar mx-0'>\n                    <div className='col-12 d-flex justify-content-center userRoleBar'><span className=\"Role\">••• {localStorage.getItem(\"role\")} •••</span></div>\n                </div>\n                <nav className=\"navbar navbar-dark d-block sticky-top px-3 py-0 py-md-2\">\n                    <div className=\"row\">\n                        <div className=\"col-6 col-sm-4 col-md-3 col-lg-2 col-xl-2 d-flex justify-content-center justify-content-sm-start align-items-center my-3 my-sm-0\">\n                            <NavLink className=\"navbar-brand w-100 mx-0 p-0\" to={this.state.url}>\n                                <img src={logo} onError={(e) => e.target.src = logo} alt=\"Winet e-procurement logo\" />\n                            </NavLink>\n                        </div>\n                        <div className=\"col-6 col-sm-8 col-md-9 col-lg-10 col-xl-10 d-block d-sm-flex justify-content-end align-self-center\">\n                            <div className=\"loginBox justify-content-end\">\n                                <div className=\"d-none d-md-block\">\n                                    <Clock />\n                                </div>\n                                <Traduttore />\n                                {this.props.visibility !== false &&\n                                    <>\n                                        <Menu />\n                                        <MenuMobile />\n                                    </>\n                                }\n                                <div className={this.state.display}>\n                                    <CartIcon />\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </nav>\n            </>\n        );\n    }\n}\n\nconst mapStateToProps = state => ({\n    productProps: state.productState\n})\n\nexport default connect(mapStateToProps, { getNumbers })(Nav);"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,kCAAkC;AACnD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,SAASC,IAAI,IAAIC,OAAO,QAAQ,kBAAkB;AAClD,SAASC,UAAU,QAAQ,wCAAwC;AACnE;AACA,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,UAAU,QAAQ,6CAA6C;AACxE,SAASC,OAAO,QAAQ,aAAa;AACrC,SACIC,QAAQ,EACRC,GAAG,EACHC,YAAY,QACT,UAAU;AACjB,OAAO,mBAAmB;AAC1B,OAAO,2BAA2B;AAClC,OAAO,iDAAiD;AACxD,OAAO,qCAAqC;AAC5C,OAAO,yBAAyB;AAChC,OAAO,sBAAsB;AAC7B,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGhC,MAAMC,GAAG,SAASlB,SAAS,CAAC;EACxBmB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,QAAQ;MACjBC,GAAG,EAAE;IACT,CAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAKd,GAAG,EAAE;MACtC,IAAI,CAACe,QAAQ,CAAC;QACVJ,GAAG,EAAEV;MACT,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACc,QAAQ,CAAC;QACVJ,GAAG,EAAEZ;MACT,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACS,KAAK,CAACQ,QAAQ,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACD,QAAQ,CAAC;QACVL,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,MAAM,IAAI,IAAI,CAACF,KAAK,CAACQ,QAAQ,KAAK,IAAI,EAAE;MACrC,IAAI,CAACD,QAAQ,CAAC;QACVL,OAAO,EAAE;MACb,CAAC,CAAC;IACN;EACJ;EACAO,MAAMA,CAAA,EAAG;IACL,oBACId,OAAA,CAAAE,SAAA;MAAAa,QAAA,gBACIf,OAAA;QAAKgB,SAAS,EAAC,mBAAmB;QAAAD,QAAA,eAC9Bf,OAAA;UAAKgB,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAACf,OAAA;YAAMgB,SAAS,EAAC,MAAM;YAAAD,QAAA,GAAC,qBAAI,EAACL,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,EAAC,qBAAI;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3I,CAAC,eACNpB,OAAA;QAAKgB,SAAS,EAAC,yDAAyD;QAAAD,QAAA,eACpEf,OAAA;UAAKgB,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAChBf,OAAA;YAAKgB,SAAS,EAAC,kIAAkI;YAAAD,QAAA,eAC7If,OAAA,CAACT,OAAO;cAACyB,SAAS,EAAC,6BAA6B;cAACK,EAAE,EAAE,IAAI,CAACf,KAAK,CAACE,GAAI;cAAAO,QAAA,eAChEf,OAAA;gBAAKsB,GAAG,EAAEpC,IAAK;gBAACqC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGpC,IAAK;gBAACwC,GAAG,EAAC;cAA0B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpB,OAAA;YAAKgB,SAAS,EAAC,qGAAqG;YAAAD,QAAA,eAChHf,OAAA;cAAKgB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,gBACzCf,OAAA;gBAAKgB,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,eAC9Bf,OAAA,CAACP,KAAK;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACNpB,OAAA,CAACR,UAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACb,IAAI,CAACf,KAAK,CAACsB,UAAU,KAAK,KAAK,iBAC5B3B,OAAA,CAAAE,SAAA;gBAAAa,QAAA,gBACIf,OAAA,CAACb,IAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACRpB,OAAA,CAACZ,UAAU;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,eAChB,CAAC,eAEPpB,OAAA;gBAAKgB,SAAS,EAAE,IAAI,CAACV,KAAK,CAACC,OAAQ;gBAAAQ,QAAA,eAC/Bf,OAAA,CAACX,QAAQ;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACR,CAAC;EAEX;AACJ;AAEA,MAAMQ,eAAe,GAAGtB,KAAK,KAAK;EAC9BuB,YAAY,EAAEvB,KAAK,CAACwB;AACxB,CAAC,CAAC;AAEF,eAAenC,OAAO,CAACiC,eAAe,EAAE;EAAElC;AAAW,CAAC,CAAC,CAACS,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}