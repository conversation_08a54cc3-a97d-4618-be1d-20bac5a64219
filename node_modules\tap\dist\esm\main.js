import { tap } from '@tapjs/core';
export { Base, Counts, Lists, Minimal, Spawn, Stdin, TapFile, TestBase, Worker, } from '@tapjs/core';
export { Test } from '@tapjs/test';
// export all the bound methods from our internal plugins.
export { after, afterEach, bailout, before, beforeEach, comment, doesNotThrow, emits, end, error, fail, has, hasStrict, match, matchOnly, matchOnlyStrict, matchStrict, not, notHas, notHasStrict, notMatch, notMatchOnly, notMatchOnlyStrict, notMatchStrict, notOk, notSame, ok, only, pass, plan, pragma, same, skip, strictNotSame, test, throws, timeout, todo, type, mockRequire, mockImport, createMock, intercept, captureFn, capture, testdir, fixture, matchSnapshot, stdin, stdinOnly, spawn, worker, };
export const t = tap();
// People really like doing `import { test }`
// this makes that work by exporting these methods as named exports.
//
// All methods on a Test object are bound to the appropriate plugin
// as the this-context if called without a this context.
//
// Technically these types aren't accurate if a plugin is disabled, but
// that's sort of what you buy into when changing types dynamically.
//
// Plugins other than the builtins that are added do not have their methods
// exported here, because we can't reasonably know what they are, and named
// exports must be explicitly named.
// Methods provided by the {@link @tapjs/core!test-base.TestBase class,
// always available
const { bailout, comment, end, fail, pass, plan, pragma, skip, stdinOnly, test, timeout, todo, } = t;
// conditional exports, only available if plugins loaded
// it'll just be undefined if that particular plugin is disabled.
const { only } = t;
const { after } = t;
const { before } = t;
const { afterEach } = t;
const { beforeEach } = t;
const { ok, notOk, not, type, same, notSame, strictNotSame, has, notHas, hasStrict, notHasStrict, match, notMatch, matchOnly, notMatchOnly, matchStrict, notMatchStrict, matchOnlyStrict, notMatchOnlyStrict, throws, doesNotThrow, emits, error, } = t;
const { mockRequire, mockImport, createMock } = t;
const { captureFn, capture, intercept } = t;
const { matchSnapshot } = t;
const { spawn } = t;
const { stdin } = t;
const { worker } = t;
const { testdir, fixture } = t;
//# sourceMappingURL=main.js.map