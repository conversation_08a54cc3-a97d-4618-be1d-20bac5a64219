"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const deepProperties_1 = __importDefault(require("../definitions/deepProperties"));
const deepProperties = (ajv, opts) => ajv.addKeyword((0, deepProperties_1.default)(opts));
exports.default = deepProperties;
module.exports = deepProperties;
//# sourceMappingURL=deepProperties.js.map