{"ast": null, "code": "export var getRenderPropValue = function getRenderPropValue(propValue) {\n  if (!propValue) {\n    return null;\n  }\n  var isRenderFunction = typeof propValue === 'function';\n  if (isRenderFunction) {\n    return propValue();\n  }\n  return propValue;\n};", "map": {"version": 3, "names": ["getRenderPropValue", "propValue", "isRenderFunction"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/getRenderPropValue.js"], "sourcesContent": ["export var getRenderPropValue = function getRenderPropValue(propValue) {\n  if (!propValue) {\n    return null;\n  }\n\n  var isRenderFunction = typeof propValue === 'function';\n\n  if (isRenderFunction) {\n    return propValue();\n  }\n\n  return propValue;\n};"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAE;EACrE,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIC,gBAAgB,GAAG,OAAOD,SAAS,KAAK,UAAU;EAEtD,IAAIC,gBAAgB,EAAE;IACpB,OAAOD,SAAS,CAAC,CAAC;EACpB;EAEA,OAAOA,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}