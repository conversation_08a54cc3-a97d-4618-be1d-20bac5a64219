{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\is-lite\\src\\types.ts"], "sourcesContent": ["export type Class<T = unknown> = new (...arguments_: any[]) => T;\nexport type PlainObject = Record<number | string | symbol, unknown>;\n\nexport type Primitive = null | undefined | string | number | boolean | symbol | bigint;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}