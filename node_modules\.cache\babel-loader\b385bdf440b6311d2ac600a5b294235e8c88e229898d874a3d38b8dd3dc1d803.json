{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\ring\\\\dashboard\\\\dashboardRing.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardPDV - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DashboardRING extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"album\",\n          children: /*#__PURE__*/_jsxDEV(BannerWelcome, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mt-5\",\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DashboardRING;", "map": {"version": 3, "names": ["React", "Component", "BannerWelcome", "Dashboard", "Nav", "jsxDEV", "_jsxDEV", "DashboardRING", "constructor", "props", "state", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/ring/dashboard/dashboardRing.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardPDV - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport Nav from \"../../../components/navigation/Nav\";\n\nclass DashboardRING extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n        }\n    }\n\n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <div className=\"album\">\n                    <div className=\"album\">\n                        <BannerWelcome />\n                    </div>\n                    <div className=\"container mt-5\" >\n                        <Dashboard />\n                    </div>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default DashboardRING;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,QAAQ,oDAAoD;AAClF,OAAOC,SAAS,MAAM,0CAA0C;AAChE,OAAOC,GAAG,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,aAAa,SAASN,SAAS,CAAC;EAClCO,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CACb,CAAC;EACL;EAEAC,MAAMA,CAAA,EAAG;IACL,oBACIL,OAAA;MAAKM,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BP,OAAA,CAACF,GAAG;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPX,OAAA;QAAKM,SAAS,EAAC,OAAO;QAAAC,QAAA,gBAClBP,OAAA;UAAKM,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClBP,OAAA,CAACJ,aAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNX,OAAA;UAAKM,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3BP,OAAA,CAACH,SAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAeV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}