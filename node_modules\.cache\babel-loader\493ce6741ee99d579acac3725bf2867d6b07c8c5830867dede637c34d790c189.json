{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"name\"];\nimport toChildrenArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport FieldContext, { HOOK_MARK } from './FieldContext';\nimport { toArray } from './utils/typeUtil';\nimport { validateRules } from './utils/validateUtil';\nimport { containsNamePath, defaultGetValueFromEvent, getNamePath, getValue } from './utils/valueUtil';\nvar EMPTY_ERRORS = [];\nfunction requireUpdate(shouldUpdate, prev, next, prevValue, nextValue, info) {\n  if (typeof shouldUpdate === 'function') {\n    return shouldUpdate(prev, next, 'source' in info ? {\n      source: info.source\n    } : {});\n  }\n  return prevValue !== nextValue;\n} // We use Class instead of Hooks here since it will cost much code by using Hooks.\n\nvar Field = /*#__PURE__*/function (_React$Component) {\n  _inherits(Field, _React$Component);\n  var _super = _createSuper(Field);\n\n  /**\n   * Follow state should not management in State since it will async update by React.\n   * This makes first render of form can not get correct state value.\n   */\n\n  /**\n   * Mark when touched & validated. Currently only used for `dependencies`.\n   * Note that we do not think field with `initialValue` is dirty\n   * but this will be by `isFieldDirty` func.\n   */\n  // ============================== Subscriptions ==============================\n  function Field(props) {\n    var _this;\n    _classCallCheck(this, Field);\n    _this = _super.call(this, props); // Register on init\n\n    _this.state = {\n      resetCount: 0\n    };\n    _this.cancelRegisterFunc = null;\n    _this.mounted = false;\n    _this.touched = false;\n    _this.dirty = false;\n    _this.validatePromise = null;\n    _this.prevValidating = void 0;\n    _this.errors = EMPTY_ERRORS;\n    _this.warnings = EMPTY_ERRORS;\n    _this.cancelRegister = function () {\n      var _this$props = _this.props,\n        preserve = _this$props.preserve,\n        isListField = _this$props.isListField,\n        name = _this$props.name;\n      if (_this.cancelRegisterFunc) {\n        _this.cancelRegisterFunc(isListField, preserve, getNamePath(name));\n      }\n      _this.cancelRegisterFunc = null;\n    };\n    _this.getNamePath = function () {\n      var _this$props2 = _this.props,\n        name = _this$props2.name,\n        fieldContext = _this$props2.fieldContext;\n      var _fieldContext$prefixN = fieldContext.prefixName,\n        prefixName = _fieldContext$prefixN === void 0 ? [] : _fieldContext$prefixN;\n      return name !== undefined ? [].concat(_toConsumableArray(prefixName), _toConsumableArray(name)) : [];\n    };\n    _this.getRules = function () {\n      var _this$props3 = _this.props,\n        _this$props3$rules = _this$props3.rules,\n        rules = _this$props3$rules === void 0 ? [] : _this$props3$rules,\n        fieldContext = _this$props3.fieldContext;\n      return rules.map(function (rule) {\n        if (typeof rule === 'function') {\n          return rule(fieldContext);\n        }\n        return rule;\n      });\n    };\n    _this.refresh = function () {\n      if (!_this.mounted) return;\n      /**\n       * Clean up current node.\n       */\n\n      _this.setState(function (_ref) {\n        var resetCount = _ref.resetCount;\n        return {\n          resetCount: resetCount + 1\n        };\n      });\n    };\n    _this.triggerMetaEvent = function (destroy) {\n      var onMetaChange = _this.props.onMetaChange;\n      onMetaChange === null || onMetaChange === void 0 ? void 0 : onMetaChange(_objectSpread(_objectSpread({}, _this.getMeta()), {}, {\n        destroy: destroy\n      }));\n    };\n    _this.onStoreChange = function (prevStore, namePathList, info) {\n      var _this$props4 = _this.props,\n        shouldUpdate = _this$props4.shouldUpdate,\n        _this$props4$dependen = _this$props4.dependencies,\n        dependencies = _this$props4$dependen === void 0 ? [] : _this$props4$dependen,\n        onReset = _this$props4.onReset;\n      var store = info.store;\n      var namePath = _this.getNamePath();\n      var prevValue = _this.getValue(prevStore);\n      var curValue = _this.getValue(store);\n      var namePathMatch = namePathList && containsNamePath(namePathList, namePath); // `setFieldsValue` is a quick access to update related status\n\n      if (info.type === 'valueUpdate' && info.source === 'external' && prevValue !== curValue) {\n        _this.touched = true;\n        _this.dirty = true;\n        _this.validatePromise = null;\n        _this.errors = EMPTY_ERRORS;\n        _this.warnings = EMPTY_ERRORS;\n        _this.triggerMetaEvent();\n      }\n      switch (info.type) {\n        case 'reset':\n          if (!namePathList || namePathMatch) {\n            // Clean up state\n            _this.touched = false;\n            _this.dirty = false;\n            _this.validatePromise = null;\n            _this.errors = EMPTY_ERRORS;\n            _this.warnings = EMPTY_ERRORS;\n            _this.triggerMetaEvent();\n            onReset === null || onReset === void 0 ? void 0 : onReset();\n            _this.refresh();\n            return;\n          }\n          break;\n\n        /**\n         * In case field with `preserve = false` nest deps like:\n         * - A = 1 => show B\n         * - B = 1 => show C\n         * - Reset A, need clean B, C\n         */\n\n        case 'remove':\n          {\n            if (shouldUpdate) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'setField':\n          {\n            if (namePathMatch) {\n              var data = info.data;\n              if ('touched' in data) {\n                _this.touched = data.touched;\n              }\n              if ('validating' in data && !('originRCField' in data)) {\n                _this.validatePromise = data.validating ? Promise.resolve([]) : null;\n              }\n              if ('errors' in data) {\n                _this.errors = data.errors || EMPTY_ERRORS;\n              }\n              if ('warnings' in data) {\n                _this.warnings = data.warnings || EMPTY_ERRORS;\n              }\n              _this.dirty = true;\n              _this.triggerMetaEvent();\n              _this.reRender();\n              return;\n            } // Handle update by `setField` with `shouldUpdate`\n\n            if (shouldUpdate && !namePath.length && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'dependenciesUpdate':\n          {\n            /**\n             * Trigger when marked `dependencies` updated. Related fields will all update\n             */\n            var dependencyList = dependencies.map(getNamePath); // No need for `namePathMath` check and `shouldUpdate` check, since `valueUpdate` will be\n            // emitted earlier and they will work there\n            // If set it may cause unnecessary twice rerendering\n\n            if (dependencyList.some(function (dependency) {\n              return containsNamePath(info.relatedFields, dependency);\n            })) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        default:\n          // 1. If `namePath` exists in `namePathList`, means it's related value and should update\n          //      For example <List name=\"list\"><Field name={['list', 0]}></List>\n          //      If `namePathList` is [['list']] (List value update), Field should be updated\n          //      If `namePathList` is [['list', 0]] (Field value update), List shouldn't be updated\n          // 2.\n          //   2.1 If `dependencies` is set, `name` is not set and `shouldUpdate` is not set,\n          //       don't use `shouldUpdate`. `dependencies` is view as a shortcut if `shouldUpdate`\n          //       is not provided\n          //   2.2 If `shouldUpdate` provided, use customize logic to update the field\n          //       else to check if value changed\n          if (namePathMatch || (!dependencies.length || namePath.length || shouldUpdate) && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n            _this.reRender();\n            return;\n          }\n          break;\n      }\n      if (shouldUpdate === true) {\n        _this.reRender();\n      }\n    };\n    _this.validateRules = function (options) {\n      // We should fixed namePath & value to avoid developer change then by form function\n      var namePath = _this.getNamePath();\n      var currentValue = _this.getValue(); // Force change to async to avoid rule OOD under renderProps field\n\n      var rootPromise = Promise.resolve().then(function () {\n        if (!_this.mounted) {\n          return [];\n        }\n        var _this$props5 = _this.props,\n          _this$props5$validate = _this$props5.validateFirst,\n          validateFirst = _this$props5$validate === void 0 ? false : _this$props5$validate,\n          messageVariables = _this$props5.messageVariables;\n        var _ref2 = options || {},\n          triggerName = _ref2.triggerName;\n        var filteredRules = _this.getRules();\n        if (triggerName) {\n          filteredRules = filteredRules.filter(function (rule) {\n            var validateTrigger = rule.validateTrigger;\n            if (!validateTrigger) {\n              return true;\n            }\n            var triggerList = toArray(validateTrigger);\n            return triggerList.includes(triggerName);\n          });\n        }\n        var promise = validateRules(namePath, currentValue, filteredRules, options, validateFirst, messageVariables);\n        promise.catch(function (e) {\n          return e;\n        }).then(function () {\n          var ruleErrors = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : EMPTY_ERRORS;\n          if (_this.validatePromise === rootPromise) {\n            _this.validatePromise = null; // Get errors & warnings\n\n            var nextErrors = [];\n            var nextWarnings = [];\n            ruleErrors.forEach(function (_ref3) {\n              var warningOnly = _ref3.rule.warningOnly,\n                _ref3$errors = _ref3.errors,\n                errors = _ref3$errors === void 0 ? EMPTY_ERRORS : _ref3$errors;\n              if (warningOnly) {\n                nextWarnings.push.apply(nextWarnings, _toConsumableArray(errors));\n              } else {\n                nextErrors.push.apply(nextErrors, _toConsumableArray(errors));\n              }\n            });\n            _this.errors = nextErrors;\n            _this.warnings = nextWarnings;\n            _this.triggerMetaEvent();\n            _this.reRender();\n          }\n        });\n        return promise;\n      });\n      _this.validatePromise = rootPromise;\n      _this.dirty = true;\n      _this.errors = EMPTY_ERRORS;\n      _this.warnings = EMPTY_ERRORS;\n      _this.triggerMetaEvent(); // Force trigger re-render since we need sync renderProps with new meta\n\n      _this.reRender();\n      return rootPromise;\n    };\n    _this.isFieldValidating = function () {\n      return !!_this.validatePromise;\n    };\n    _this.isFieldTouched = function () {\n      return _this.touched;\n    };\n    _this.isFieldDirty = function () {\n      // Touched or validate or has initialValue\n      if (_this.dirty || _this.props.initialValue !== undefined) {\n        return true;\n      } // Form set initialValue\n\n      var fieldContext = _this.props.fieldContext;\n      var _fieldContext$getInte = fieldContext.getInternalHooks(HOOK_MARK),\n        getInitialValue = _fieldContext$getInte.getInitialValue;\n      if (getInitialValue(_this.getNamePath()) !== undefined) {\n        return true;\n      }\n      return false;\n    };\n    _this.getErrors = function () {\n      return _this.errors;\n    };\n    _this.getWarnings = function () {\n      return _this.warnings;\n    };\n    _this.isListField = function () {\n      return _this.props.isListField;\n    };\n    _this.isList = function () {\n      return _this.props.isList;\n    };\n    _this.isPreserve = function () {\n      return _this.props.preserve;\n    };\n    _this.getMeta = function () {\n      // Make error & validating in cache to save perf\n      _this.prevValidating = _this.isFieldValidating();\n      var meta = {\n        touched: _this.isFieldTouched(),\n        validating: _this.prevValidating,\n        errors: _this.errors,\n        warnings: _this.warnings,\n        name: _this.getNamePath()\n      };\n      return meta;\n    };\n    _this.getOnlyChild = function (children) {\n      // Support render props\n      if (typeof children === 'function') {\n        var meta = _this.getMeta();\n        return _objectSpread(_objectSpread({}, _this.getOnlyChild(children(_this.getControlled(), meta, _this.props.fieldContext))), {}, {\n          isFunction: true\n        });\n      } // Filed element only\n\n      var childList = toChildrenArray(children);\n      if (childList.length !== 1 || ! /*#__PURE__*/React.isValidElement(childList[0])) {\n        return {\n          child: childList,\n          isFunction: false\n        };\n      }\n      return {\n        child: childList[0],\n        isFunction: false\n      };\n    };\n    _this.getValue = function (store) {\n      var getFieldsValue = _this.props.fieldContext.getFieldsValue;\n      var namePath = _this.getNamePath();\n      return getValue(store || getFieldsValue(true), namePath);\n    };\n    _this.getControlled = function () {\n      var childProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _this$props6 = _this.props,\n        trigger = _this$props6.trigger,\n        validateTrigger = _this$props6.validateTrigger,\n        getValueFromEvent = _this$props6.getValueFromEvent,\n        normalize = _this$props6.normalize,\n        valuePropName = _this$props6.valuePropName,\n        getValueProps = _this$props6.getValueProps,\n        fieldContext = _this$props6.fieldContext;\n      var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : fieldContext.validateTrigger;\n      var namePath = _this.getNamePath();\n      var getInternalHooks = fieldContext.getInternalHooks,\n        getFieldsValue = fieldContext.getFieldsValue;\n      var _getInternalHooks = getInternalHooks(HOOK_MARK),\n        dispatch = _getInternalHooks.dispatch;\n      var value = _this.getValue();\n      var mergedGetValueProps = getValueProps || function (val) {\n        return _defineProperty({}, valuePropName, val);\n      }; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n      var originTriggerFunc = childProps[trigger];\n      var control = _objectSpread(_objectSpread({}, childProps), mergedGetValueProps(value)); // Add trigger\n\n      control[trigger] = function () {\n        // Mark as touched\n        _this.touched = true;\n        _this.dirty = true;\n        _this.triggerMetaEvent();\n        var newValue;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (getValueFromEvent) {\n          newValue = getValueFromEvent.apply(void 0, args);\n        } else {\n          newValue = defaultGetValueFromEvent.apply(void 0, [valuePropName].concat(args));\n        }\n        if (normalize) {\n          newValue = normalize(newValue, value, getFieldsValue(true));\n        }\n        dispatch({\n          type: 'updateValue',\n          namePath: namePath,\n          value: newValue\n        });\n        if (originTriggerFunc) {\n          originTriggerFunc.apply(void 0, args);\n        }\n      }; // Add validateTrigger\n\n      var validateTriggerList = toArray(mergedValidateTrigger || []);\n      validateTriggerList.forEach(function (triggerName) {\n        // Wrap additional function of component, so that we can get latest value from store\n        var originTrigger = control[triggerName];\n        control[triggerName] = function () {\n          if (originTrigger) {\n            originTrigger.apply(void 0, arguments);\n          } // Always use latest rules\n\n          var rules = _this.props.rules;\n          if (rules && rules.length) {\n            // We dispatch validate to root,\n            // since it will update related data with other field with same name\n            dispatch({\n              type: 'validateField',\n              namePath: namePath,\n              triggerName: triggerName\n            });\n          }\n        };\n      });\n      return control;\n    };\n    if (props.fieldContext) {\n      var getInternalHooks = props.fieldContext.getInternalHooks;\n      var _getInternalHooks2 = getInternalHooks(HOOK_MARK),\n        initEntityValue = _getInternalHooks2.initEntityValue;\n      initEntityValue(_assertThisInitialized(_this));\n    }\n    return _this;\n  }\n  _createClass(Field, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n        shouldUpdate = _this$props7.shouldUpdate,\n        fieldContext = _this$props7.fieldContext;\n      this.mounted = true; // Register on init\n\n      if (fieldContext) {\n        var getInternalHooks = fieldContext.getInternalHooks;\n        var _getInternalHooks3 = getInternalHooks(HOOK_MARK),\n          registerField = _getInternalHooks3.registerField;\n        this.cancelRegisterFunc = registerField(this);\n      } // One more render for component in case fields not ready\n\n      if (shouldUpdate === true) {\n        this.reRender();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.cancelRegister();\n      this.triggerMetaEvent(true);\n      this.mounted = false;\n    }\n  }, {\n    key: \"reRender\",\n    value: function reRender() {\n      if (!this.mounted) return;\n      this.forceUpdate();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var resetCount = this.state.resetCount;\n      var children = this.props.children;\n      var _this$getOnlyChild = this.getOnlyChild(children),\n        child = _this$getOnlyChild.child,\n        isFunction = _this$getOnlyChild.isFunction; // Not need to `cloneElement` since user can handle this in render function self\n\n      var returnChildNode;\n      if (isFunction) {\n        returnChildNode = child;\n      } else if (/*#__PURE__*/React.isValidElement(child)) {\n        returnChildNode = /*#__PURE__*/React.cloneElement(child, this.getControlled(child.props));\n      } else {\n        warning(!child, '`children` of Field is not validate ReactElement.');\n        returnChildNode = child;\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: resetCount\n      }, returnChildNode);\n    }\n  }]);\n  return Field;\n}(React.Component);\nField.contextType = FieldContext;\nField.defaultProps = {\n  trigger: 'onChange',\n  valuePropName: 'value'\n};\nfunction WrapperField(_ref5) {\n  var name = _ref5.name,\n    restProps = _objectWithoutProperties(_ref5, _excluded);\n  var fieldContext = React.useContext(FieldContext);\n  var namePath = name !== undefined ? getNamePath(name) : undefined;\n  var key = 'keep';\n  if (!restProps.isListField) {\n    key = \"_\".concat((namePath || []).join('_'));\n  } // Warning if it's a directly list field.\n  // We can still support multiple level field preserve.\n\n  if (process.env.NODE_ENV !== 'production' && restProps.preserve === false && restProps.isListField && namePath.length <= 1) {\n    warning(false, '`preserve` should not apply on Form.List fields.');\n  }\n  return /*#__PURE__*/React.createElement(Field, _extends({\n    key: key,\n    name: namePath\n  }, restProps, {\n    fieldContext: fieldContext\n  }));\n}\nexport default WrapperField;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_defineProperty", "_objectSpread", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_excluded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warning", "React", "FieldContext", "HOOK_MARK", "toArray", "validateRules", "containsNamePath", "defaultGetValueFromEvent", "getNamePath", "getValue", "EMPTY_ERRORS", "requireUpdate", "shouldUpdate", "prev", "next", "prevValue", "nextValue", "info", "source", "Field", "_React$Component", "_super", "props", "_this", "call", "state", "resetCount", "cancelRegisterFunc", "mounted", "touched", "dirty", "validatePromise", "prevValidating", "errors", "warnings", "cancelRegister", "_this$props", "preserve", "isListField", "name", "_this$props2", "fieldContext", "_fieldContext$prefixN", "prefixName", "undefined", "concat", "getRules", "_this$props3", "_this$props3$rules", "rules", "map", "rule", "refresh", "setState", "_ref", "triggerMetaEvent", "destroy", "onMetaChange", "getMeta", "onStoreChange", "prevStore", "namePathList", "_this$props4", "_this$props4$dependen", "dependencies", "onReset", "store", "namePath", "curValue", "namePathMatch", "type", "reRender", "data", "validating", "Promise", "resolve", "length", "dependencyList", "some", "dependency", "relatedFields", "options", "currentValue", "rootPromise", "then", "_this$props5", "_this$props5$validate", "validate<PERSON><PERSON><PERSON>", "messageVariables", "_ref2", "triggerName", "filteredRules", "filter", "validate<PERSON><PERSON>ger", "triggerList", "includes", "promise", "catch", "e", "ruleErrors", "arguments", "nextErrors", "nextWarnings", "for<PERSON>ach", "_ref3", "warningOnly", "_ref3$errors", "push", "apply", "isFieldValidating", "isFieldTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialValue", "_fieldContext$getInte", "getInternalHooks", "getInitialValue", "getErrors", "getWarnings", "isList", "isPreserve", "meta", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "getControlled", "isFunction", "childList", "isValidElement", "child", "getFieldsValue", "childProps", "_this$props6", "trigger", "getValueFromEvent", "normalize", "valuePropName", "getValueProps", "mergedValidateTrigger", "_getInternalHooks", "dispatch", "value", "mergedGetValueProps", "val", "originTriggerFunc", "control", "newValue", "_len", "args", "Array", "_key", "validateTriggerList", "originTrigger", "_getInternalHooks2", "initEntityValue", "key", "componentDidMount", "_this$props7", "_getInternalHooks3", "registerField", "componentWillUnmount", "forceUpdate", "render", "_this$getOnlyChild", "returnChildNode", "cloneElement", "createElement", "Fragment", "Component", "contextType", "defaultProps", "WrapperField", "_ref5", "restProps", "useContext", "join", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/Field.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"name\"];\nimport toChildrenArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport FieldContext, { HOOK_MARK } from './FieldContext';\nimport { toArray } from './utils/typeUtil';\nimport { validateRules } from './utils/validateUtil';\nimport { containsNamePath, defaultGetValueFromEvent, getNamePath, getValue } from './utils/valueUtil';\nvar EMPTY_ERRORS = [];\n\nfunction requireUpdate(shouldUpdate, prev, next, prevValue, nextValue, info) {\n  if (typeof shouldUpdate === 'function') {\n    return shouldUpdate(prev, next, 'source' in info ? {\n      source: info.source\n    } : {});\n  }\n\n  return prevValue !== nextValue;\n} // We use Class instead of Hooks here since it will cost much code by using Hooks.\n\n\nvar Field = /*#__PURE__*/function (_React$Component) {\n  _inherits(Field, _React$Component);\n\n  var _super = _createSuper(Field);\n\n  /**\n   * Follow state should not management in State since it will async update by React.\n   * This makes first render of form can not get correct state value.\n   */\n\n  /**\n   * Mark when touched & validated. Currently only used for `dependencies`.\n   * Note that we do not think field with `initialValue` is dirty\n   * but this will be by `isFieldDirty` func.\n   */\n  // ============================== Subscriptions ==============================\n  function Field(props) {\n    var _this;\n\n    _classCallCheck(this, Field);\n\n    _this = _super.call(this, props); // Register on init\n\n    _this.state = {\n      resetCount: 0\n    };\n    _this.cancelRegisterFunc = null;\n    _this.mounted = false;\n    _this.touched = false;\n    _this.dirty = false;\n    _this.validatePromise = null;\n    _this.prevValidating = void 0;\n    _this.errors = EMPTY_ERRORS;\n    _this.warnings = EMPTY_ERRORS;\n\n    _this.cancelRegister = function () {\n      var _this$props = _this.props,\n          preserve = _this$props.preserve,\n          isListField = _this$props.isListField,\n          name = _this$props.name;\n\n      if (_this.cancelRegisterFunc) {\n        _this.cancelRegisterFunc(isListField, preserve, getNamePath(name));\n      }\n\n      _this.cancelRegisterFunc = null;\n    };\n\n    _this.getNamePath = function () {\n      var _this$props2 = _this.props,\n          name = _this$props2.name,\n          fieldContext = _this$props2.fieldContext;\n      var _fieldContext$prefixN = fieldContext.prefixName,\n          prefixName = _fieldContext$prefixN === void 0 ? [] : _fieldContext$prefixN;\n      return name !== undefined ? [].concat(_toConsumableArray(prefixName), _toConsumableArray(name)) : [];\n    };\n\n    _this.getRules = function () {\n      var _this$props3 = _this.props,\n          _this$props3$rules = _this$props3.rules,\n          rules = _this$props3$rules === void 0 ? [] : _this$props3$rules,\n          fieldContext = _this$props3.fieldContext;\n      return rules.map(function (rule) {\n        if (typeof rule === 'function') {\n          return rule(fieldContext);\n        }\n\n        return rule;\n      });\n    };\n\n    _this.refresh = function () {\n      if (!_this.mounted) return;\n      /**\n       * Clean up current node.\n       */\n\n      _this.setState(function (_ref) {\n        var resetCount = _ref.resetCount;\n        return {\n          resetCount: resetCount + 1\n        };\n      });\n    };\n\n    _this.triggerMetaEvent = function (destroy) {\n      var onMetaChange = _this.props.onMetaChange;\n      onMetaChange === null || onMetaChange === void 0 ? void 0 : onMetaChange(_objectSpread(_objectSpread({}, _this.getMeta()), {}, {\n        destroy: destroy\n      }));\n    };\n\n    _this.onStoreChange = function (prevStore, namePathList, info) {\n      var _this$props4 = _this.props,\n          shouldUpdate = _this$props4.shouldUpdate,\n          _this$props4$dependen = _this$props4.dependencies,\n          dependencies = _this$props4$dependen === void 0 ? [] : _this$props4$dependen,\n          onReset = _this$props4.onReset;\n      var store = info.store;\n\n      var namePath = _this.getNamePath();\n\n      var prevValue = _this.getValue(prevStore);\n\n      var curValue = _this.getValue(store);\n\n      var namePathMatch = namePathList && containsNamePath(namePathList, namePath); // `setFieldsValue` is a quick access to update related status\n\n      if (info.type === 'valueUpdate' && info.source === 'external' && prevValue !== curValue) {\n        _this.touched = true;\n        _this.dirty = true;\n        _this.validatePromise = null;\n        _this.errors = EMPTY_ERRORS;\n        _this.warnings = EMPTY_ERRORS;\n\n        _this.triggerMetaEvent();\n      }\n\n      switch (info.type) {\n        case 'reset':\n          if (!namePathList || namePathMatch) {\n            // Clean up state\n            _this.touched = false;\n            _this.dirty = false;\n            _this.validatePromise = null;\n            _this.errors = EMPTY_ERRORS;\n            _this.warnings = EMPTY_ERRORS;\n\n            _this.triggerMetaEvent();\n\n            onReset === null || onReset === void 0 ? void 0 : onReset();\n\n            _this.refresh();\n\n            return;\n          }\n\n          break;\n\n        /**\n         * In case field with `preserve = false` nest deps like:\n         * - A = 1 => show B\n         * - B = 1 => show C\n         * - Reset A, need clean B, C\n         */\n\n        case 'remove':\n          {\n            if (shouldUpdate) {\n              _this.reRender();\n\n              return;\n            }\n\n            break;\n          }\n\n        case 'setField':\n          {\n            if (namePathMatch) {\n              var data = info.data;\n\n              if ('touched' in data) {\n                _this.touched = data.touched;\n              }\n\n              if ('validating' in data && !('originRCField' in data)) {\n                _this.validatePromise = data.validating ? Promise.resolve([]) : null;\n              }\n\n              if ('errors' in data) {\n                _this.errors = data.errors || EMPTY_ERRORS;\n              }\n\n              if ('warnings' in data) {\n                _this.warnings = data.warnings || EMPTY_ERRORS;\n              }\n\n              _this.dirty = true;\n\n              _this.triggerMetaEvent();\n\n              _this.reRender();\n\n              return;\n            } // Handle update by `setField` with `shouldUpdate`\n\n\n            if (shouldUpdate && !namePath.length && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n\n              return;\n            }\n\n            break;\n          }\n\n        case 'dependenciesUpdate':\n          {\n            /**\n             * Trigger when marked `dependencies` updated. Related fields will all update\n             */\n            var dependencyList = dependencies.map(getNamePath); // No need for `namePathMath` check and `shouldUpdate` check, since `valueUpdate` will be\n            // emitted earlier and they will work there\n            // If set it may cause unnecessary twice rerendering\n\n            if (dependencyList.some(function (dependency) {\n              return containsNamePath(info.relatedFields, dependency);\n            })) {\n              _this.reRender();\n\n              return;\n            }\n\n            break;\n          }\n\n        default:\n          // 1. If `namePath` exists in `namePathList`, means it's related value and should update\n          //      For example <List name=\"list\"><Field name={['list', 0]}></List>\n          //      If `namePathList` is [['list']] (List value update), Field should be updated\n          //      If `namePathList` is [['list', 0]] (Field value update), List shouldn't be updated\n          // 2.\n          //   2.1 If `dependencies` is set, `name` is not set and `shouldUpdate` is not set,\n          //       don't use `shouldUpdate`. `dependencies` is view as a shortcut if `shouldUpdate`\n          //       is not provided\n          //   2.2 If `shouldUpdate` provided, use customize logic to update the field\n          //       else to check if value changed\n          if (namePathMatch || (!dependencies.length || namePath.length || shouldUpdate) && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n            _this.reRender();\n\n            return;\n          }\n\n          break;\n      }\n\n      if (shouldUpdate === true) {\n        _this.reRender();\n      }\n    };\n\n    _this.validateRules = function (options) {\n      // We should fixed namePath & value to avoid developer change then by form function\n      var namePath = _this.getNamePath();\n\n      var currentValue = _this.getValue(); // Force change to async to avoid rule OOD under renderProps field\n\n\n      var rootPromise = Promise.resolve().then(function () {\n        if (!_this.mounted) {\n          return [];\n        }\n\n        var _this$props5 = _this.props,\n            _this$props5$validate = _this$props5.validateFirst,\n            validateFirst = _this$props5$validate === void 0 ? false : _this$props5$validate,\n            messageVariables = _this$props5.messageVariables;\n\n        var _ref2 = options || {},\n            triggerName = _ref2.triggerName;\n\n        var filteredRules = _this.getRules();\n\n        if (triggerName) {\n          filteredRules = filteredRules.filter(function (rule) {\n            var validateTrigger = rule.validateTrigger;\n\n            if (!validateTrigger) {\n              return true;\n            }\n\n            var triggerList = toArray(validateTrigger);\n            return triggerList.includes(triggerName);\n          });\n        }\n\n        var promise = validateRules(namePath, currentValue, filteredRules, options, validateFirst, messageVariables);\n        promise.catch(function (e) {\n          return e;\n        }).then(function () {\n          var ruleErrors = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : EMPTY_ERRORS;\n\n          if (_this.validatePromise === rootPromise) {\n            _this.validatePromise = null; // Get errors & warnings\n\n            var nextErrors = [];\n            var nextWarnings = [];\n            ruleErrors.forEach(function (_ref3) {\n              var warningOnly = _ref3.rule.warningOnly,\n                  _ref3$errors = _ref3.errors,\n                  errors = _ref3$errors === void 0 ? EMPTY_ERRORS : _ref3$errors;\n\n              if (warningOnly) {\n                nextWarnings.push.apply(nextWarnings, _toConsumableArray(errors));\n              } else {\n                nextErrors.push.apply(nextErrors, _toConsumableArray(errors));\n              }\n            });\n            _this.errors = nextErrors;\n            _this.warnings = nextWarnings;\n\n            _this.triggerMetaEvent();\n\n            _this.reRender();\n          }\n        });\n        return promise;\n      });\n      _this.validatePromise = rootPromise;\n      _this.dirty = true;\n      _this.errors = EMPTY_ERRORS;\n      _this.warnings = EMPTY_ERRORS;\n\n      _this.triggerMetaEvent(); // Force trigger re-render since we need sync renderProps with new meta\n\n\n      _this.reRender();\n\n      return rootPromise;\n    };\n\n    _this.isFieldValidating = function () {\n      return !!_this.validatePromise;\n    };\n\n    _this.isFieldTouched = function () {\n      return _this.touched;\n    };\n\n    _this.isFieldDirty = function () {\n      // Touched or validate or has initialValue\n      if (_this.dirty || _this.props.initialValue !== undefined) {\n        return true;\n      } // Form set initialValue\n\n\n      var fieldContext = _this.props.fieldContext;\n\n      var _fieldContext$getInte = fieldContext.getInternalHooks(HOOK_MARK),\n          getInitialValue = _fieldContext$getInte.getInitialValue;\n\n      if (getInitialValue(_this.getNamePath()) !== undefined) {\n        return true;\n      }\n\n      return false;\n    };\n\n    _this.getErrors = function () {\n      return _this.errors;\n    };\n\n    _this.getWarnings = function () {\n      return _this.warnings;\n    };\n\n    _this.isListField = function () {\n      return _this.props.isListField;\n    };\n\n    _this.isList = function () {\n      return _this.props.isList;\n    };\n\n    _this.isPreserve = function () {\n      return _this.props.preserve;\n    };\n\n    _this.getMeta = function () {\n      // Make error & validating in cache to save perf\n      _this.prevValidating = _this.isFieldValidating();\n      var meta = {\n        touched: _this.isFieldTouched(),\n        validating: _this.prevValidating,\n        errors: _this.errors,\n        warnings: _this.warnings,\n        name: _this.getNamePath()\n      };\n      return meta;\n    };\n\n    _this.getOnlyChild = function (children) {\n      // Support render props\n      if (typeof children === 'function') {\n        var meta = _this.getMeta();\n\n        return _objectSpread(_objectSpread({}, _this.getOnlyChild(children(_this.getControlled(), meta, _this.props.fieldContext))), {}, {\n          isFunction: true\n        });\n      } // Filed element only\n\n\n      var childList = toChildrenArray(children);\n\n      if (childList.length !== 1 || ! /*#__PURE__*/React.isValidElement(childList[0])) {\n        return {\n          child: childList,\n          isFunction: false\n        };\n      }\n\n      return {\n        child: childList[0],\n        isFunction: false\n      };\n    };\n\n    _this.getValue = function (store) {\n      var getFieldsValue = _this.props.fieldContext.getFieldsValue;\n\n      var namePath = _this.getNamePath();\n\n      return getValue(store || getFieldsValue(true), namePath);\n    };\n\n    _this.getControlled = function () {\n      var childProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _this$props6 = _this.props,\n          trigger = _this$props6.trigger,\n          validateTrigger = _this$props6.validateTrigger,\n          getValueFromEvent = _this$props6.getValueFromEvent,\n          normalize = _this$props6.normalize,\n          valuePropName = _this$props6.valuePropName,\n          getValueProps = _this$props6.getValueProps,\n          fieldContext = _this$props6.fieldContext;\n      var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : fieldContext.validateTrigger;\n\n      var namePath = _this.getNamePath();\n\n      var getInternalHooks = fieldContext.getInternalHooks,\n          getFieldsValue = fieldContext.getFieldsValue;\n\n      var _getInternalHooks = getInternalHooks(HOOK_MARK),\n          dispatch = _getInternalHooks.dispatch;\n\n      var value = _this.getValue();\n\n      var mergedGetValueProps = getValueProps || function (val) {\n        return _defineProperty({}, valuePropName, val);\n      }; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n      var originTriggerFunc = childProps[trigger];\n\n      var control = _objectSpread(_objectSpread({}, childProps), mergedGetValueProps(value)); // Add trigger\n\n\n      control[trigger] = function () {\n        // Mark as touched\n        _this.touched = true;\n        _this.dirty = true;\n\n        _this.triggerMetaEvent();\n\n        var newValue;\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        if (getValueFromEvent) {\n          newValue = getValueFromEvent.apply(void 0, args);\n        } else {\n          newValue = defaultGetValueFromEvent.apply(void 0, [valuePropName].concat(args));\n        }\n\n        if (normalize) {\n          newValue = normalize(newValue, value, getFieldsValue(true));\n        }\n\n        dispatch({\n          type: 'updateValue',\n          namePath: namePath,\n          value: newValue\n        });\n\n        if (originTriggerFunc) {\n          originTriggerFunc.apply(void 0, args);\n        }\n      }; // Add validateTrigger\n\n\n      var validateTriggerList = toArray(mergedValidateTrigger || []);\n      validateTriggerList.forEach(function (triggerName) {\n        // Wrap additional function of component, so that we can get latest value from store\n        var originTrigger = control[triggerName];\n\n        control[triggerName] = function () {\n          if (originTrigger) {\n            originTrigger.apply(void 0, arguments);\n          } // Always use latest rules\n\n\n          var rules = _this.props.rules;\n\n          if (rules && rules.length) {\n            // We dispatch validate to root,\n            // since it will update related data with other field with same name\n            dispatch({\n              type: 'validateField',\n              namePath: namePath,\n              triggerName: triggerName\n            });\n          }\n        };\n      });\n      return control;\n    };\n\n    if (props.fieldContext) {\n      var getInternalHooks = props.fieldContext.getInternalHooks;\n\n      var _getInternalHooks2 = getInternalHooks(HOOK_MARK),\n          initEntityValue = _getInternalHooks2.initEntityValue;\n\n      initEntityValue(_assertThisInitialized(_this));\n    }\n\n    return _this;\n  }\n\n  _createClass(Field, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n          shouldUpdate = _this$props7.shouldUpdate,\n          fieldContext = _this$props7.fieldContext;\n      this.mounted = true; // Register on init\n\n      if (fieldContext) {\n        var getInternalHooks = fieldContext.getInternalHooks;\n\n        var _getInternalHooks3 = getInternalHooks(HOOK_MARK),\n            registerField = _getInternalHooks3.registerField;\n\n        this.cancelRegisterFunc = registerField(this);\n      } // One more render for component in case fields not ready\n\n\n      if (shouldUpdate === true) {\n        this.reRender();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.cancelRegister();\n      this.triggerMetaEvent(true);\n      this.mounted = false;\n    }\n  }, {\n    key: \"reRender\",\n    value: function reRender() {\n      if (!this.mounted) return;\n      this.forceUpdate();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var resetCount = this.state.resetCount;\n      var children = this.props.children;\n\n      var _this$getOnlyChild = this.getOnlyChild(children),\n          child = _this$getOnlyChild.child,\n          isFunction = _this$getOnlyChild.isFunction; // Not need to `cloneElement` since user can handle this in render function self\n\n\n      var returnChildNode;\n\n      if (isFunction) {\n        returnChildNode = child;\n      } else if ( /*#__PURE__*/React.isValidElement(child)) {\n        returnChildNode = /*#__PURE__*/React.cloneElement(child, this.getControlled(child.props));\n      } else {\n        warning(!child, '`children` of Field is not validate ReactElement.');\n        returnChildNode = child;\n      }\n\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: resetCount\n      }, returnChildNode);\n    }\n  }]);\n\n  return Field;\n}(React.Component);\n\nField.contextType = FieldContext;\nField.defaultProps = {\n  trigger: 'onChange',\n  valuePropName: 'value'\n};\n\nfunction WrapperField(_ref5) {\n  var name = _ref5.name,\n      restProps = _objectWithoutProperties(_ref5, _excluded);\n\n  var fieldContext = React.useContext(FieldContext);\n  var namePath = name !== undefined ? getNamePath(name) : undefined;\n  var key = 'keep';\n\n  if (!restProps.isListField) {\n    key = \"_\".concat((namePath || []).join('_'));\n  } // Warning if it's a directly list field.\n  // We can still support multiple level field preserve.\n\n\n  if (process.env.NODE_ENV !== 'production' && restProps.preserve === false && restProps.isListField && namePath.length <= 1) {\n    warning(false, '`preserve` should not apply on Form.List fields.');\n  }\n\n  return /*#__PURE__*/React.createElement(Field, _extends({\n    key: key,\n    name: namePath\n  }, restProps, {\n    fieldContext: fieldContext\n  }));\n}\n\nexport default WrapperField;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,mBAAmB;AACrG,IAAIC,YAAY,GAAG,EAAE;AAErB,SAASC,aAAaA,CAACC,YAAY,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAE;EAC3E,IAAI,OAAOL,YAAY,KAAK,UAAU,EAAE;IACtC,OAAOA,YAAY,CAACC,IAAI,EAAEC,IAAI,EAAE,QAAQ,IAAIG,IAAI,GAAG;MACjDC,MAAM,EAAED,IAAI,CAACC;IACf,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EAEA,OAAOH,SAAS,KAAKC,SAAS;AAChC,CAAC,CAAC;;AAGF,IAAIG,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnDxB,SAAS,CAACuB,KAAK,EAAEC,gBAAgB,CAAC;EAElC,IAAIC,MAAM,GAAGxB,YAAY,CAACsB,KAAK,CAAC;;EAEhC;AACF;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;EACE;EACA,SAASA,KAAKA,CAACG,KAAK,EAAE;IACpB,IAAIC,KAAK;IAET9B,eAAe,CAAC,IAAI,EAAE0B,KAAK,CAAC;IAE5BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC,CAAC;;IAElCC,KAAK,CAACE,KAAK,GAAG;MACZC,UAAU,EAAE;IACd,CAAC;IACDH,KAAK,CAACI,kBAAkB,GAAG,IAAI;IAC/BJ,KAAK,CAACK,OAAO,GAAG,KAAK;IACrBL,KAAK,CAACM,OAAO,GAAG,KAAK;IACrBN,KAAK,CAACO,KAAK,GAAG,KAAK;IACnBP,KAAK,CAACQ,eAAe,GAAG,IAAI;IAC5BR,KAAK,CAACS,cAAc,GAAG,KAAK,CAAC;IAC7BT,KAAK,CAACU,MAAM,GAAGvB,YAAY;IAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;IAE7Ba,KAAK,CAACY,cAAc,GAAG,YAAY;MACjC,IAAIC,WAAW,GAAGb,KAAK,CAACD,KAAK;QACzBe,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,IAAI,GAAGH,WAAW,CAACG,IAAI;MAE3B,IAAIhB,KAAK,CAACI,kBAAkB,EAAE;QAC5BJ,KAAK,CAACI,kBAAkB,CAACW,WAAW,EAAED,QAAQ,EAAE7B,WAAW,CAAC+B,IAAI,CAAC,CAAC;MACpE;MAEAhB,KAAK,CAACI,kBAAkB,GAAG,IAAI;IACjC,CAAC;IAEDJ,KAAK,CAACf,WAAW,GAAG,YAAY;MAC9B,IAAIgC,YAAY,GAAGjB,KAAK,CAACD,KAAK;QAC1BiB,IAAI,GAAGC,YAAY,CAACD,IAAI;QACxBE,YAAY,GAAGD,YAAY,CAACC,YAAY;MAC5C,IAAIC,qBAAqB,GAAGD,YAAY,CAACE,UAAU;QAC/CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;MAC9E,OAAOH,IAAI,KAAKK,SAAS,GAAG,EAAE,CAACC,MAAM,CAACrD,kBAAkB,CAACmD,UAAU,CAAC,EAAEnD,kBAAkB,CAAC+C,IAAI,CAAC,CAAC,GAAG,EAAE;IACtG,CAAC;IAEDhB,KAAK,CAACuB,QAAQ,GAAG,YAAY;MAC3B,IAAIC,YAAY,GAAGxB,KAAK,CAACD,KAAK;QAC1B0B,kBAAkB,GAAGD,YAAY,CAACE,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;QAC/DP,YAAY,GAAGM,YAAY,CAACN,YAAY;MAC5C,OAAOQ,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;UAC9B,OAAOA,IAAI,CAACV,YAAY,CAAC;QAC3B;QAEA,OAAOU,IAAI;MACb,CAAC,CAAC;IACJ,CAAC;IAED5B,KAAK,CAAC6B,OAAO,GAAG,YAAY;MAC1B,IAAI,CAAC7B,KAAK,CAACK,OAAO,EAAE;MACpB;AACN;AACA;;MAEML,KAAK,CAAC8B,QAAQ,CAAC,UAAUC,IAAI,EAAE;QAC7B,IAAI5B,UAAU,GAAG4B,IAAI,CAAC5B,UAAU;QAChC,OAAO;UACLA,UAAU,EAAEA,UAAU,GAAG;QAC3B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAEDH,KAAK,CAACgC,gBAAgB,GAAG,UAAUC,OAAO,EAAE;MAC1C,IAAIC,YAAY,GAAGlC,KAAK,CAACD,KAAK,CAACmC,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAClE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7HF,OAAO,EAAEA;MACX,CAAC,CAAC,CAAC;IACL,CAAC;IAEDjC,KAAK,CAACoC,aAAa,GAAG,UAAUC,SAAS,EAAEC,YAAY,EAAE5C,IAAI,EAAE;MAC7D,IAAI6C,YAAY,GAAGvC,KAAK,CAACD,KAAK;QAC1BV,YAAY,GAAGkD,YAAY,CAAClD,YAAY;QACxCmD,qBAAqB,GAAGD,YAAY,CAACE,YAAY;QACjDA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC5EE,OAAO,GAAGH,YAAY,CAACG,OAAO;MAClC,IAAIC,KAAK,GAAGjD,IAAI,CAACiD,KAAK;MAEtB,IAAIC,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAElC,IAAIO,SAAS,GAAGQ,KAAK,CAACd,QAAQ,CAACmD,SAAS,CAAC;MAEzC,IAAIQ,QAAQ,GAAG7C,KAAK,CAACd,QAAQ,CAACyD,KAAK,CAAC;MAEpC,IAAIG,aAAa,GAAGR,YAAY,IAAIvD,gBAAgB,CAACuD,YAAY,EAAEM,QAAQ,CAAC,CAAC,CAAC;;MAE9E,IAAIlD,IAAI,CAACqD,IAAI,KAAK,aAAa,IAAIrD,IAAI,CAACC,MAAM,KAAK,UAAU,IAAIH,SAAS,KAAKqD,QAAQ,EAAE;QACvF7C,KAAK,CAACM,OAAO,GAAG,IAAI;QACpBN,KAAK,CAACO,KAAK,GAAG,IAAI;QAClBP,KAAK,CAACQ,eAAe,GAAG,IAAI;QAC5BR,KAAK,CAACU,MAAM,GAAGvB,YAAY;QAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;QAE7Ba,KAAK,CAACgC,gBAAgB,CAAC,CAAC;MAC1B;MAEA,QAAQtC,IAAI,CAACqD,IAAI;QACf,KAAK,OAAO;UACV,IAAI,CAACT,YAAY,IAAIQ,aAAa,EAAE;YAClC;YACA9C,KAAK,CAACM,OAAO,GAAG,KAAK;YACrBN,KAAK,CAACO,KAAK,GAAG,KAAK;YACnBP,KAAK,CAACQ,eAAe,GAAG,IAAI;YAC5BR,KAAK,CAACU,MAAM,GAAGvB,YAAY;YAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;YAE7Ba,KAAK,CAACgC,gBAAgB,CAAC,CAAC;YAExBU,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;YAE3D1C,KAAK,CAAC6B,OAAO,CAAC,CAAC;YAEf;UACF;UAEA;;QAEF;AACR;AACA;AACA;AACA;AACA;;QAEQ,KAAK,QAAQ;UACX;YACE,IAAIxC,YAAY,EAAE;cAChBW,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAEhB;YACF;YAEA;UACF;QAEF,KAAK,UAAU;UACb;YACE,IAAIF,aAAa,EAAE;cACjB,IAAIG,IAAI,GAAGvD,IAAI,CAACuD,IAAI;cAEpB,IAAI,SAAS,IAAIA,IAAI,EAAE;gBACrBjD,KAAK,CAACM,OAAO,GAAG2C,IAAI,CAAC3C,OAAO;cAC9B;cAEA,IAAI,YAAY,IAAI2C,IAAI,IAAI,EAAE,eAAe,IAAIA,IAAI,CAAC,EAAE;gBACtDjD,KAAK,CAACQ,eAAe,GAAGyC,IAAI,CAACC,UAAU,GAAGC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI;cACtE;cAEA,IAAI,QAAQ,IAAIH,IAAI,EAAE;gBACpBjD,KAAK,CAACU,MAAM,GAAGuC,IAAI,CAACvC,MAAM,IAAIvB,YAAY;cAC5C;cAEA,IAAI,UAAU,IAAI8D,IAAI,EAAE;gBACtBjD,KAAK,CAACW,QAAQ,GAAGsC,IAAI,CAACtC,QAAQ,IAAIxB,YAAY;cAChD;cAEAa,KAAK,CAACO,KAAK,GAAG,IAAI;cAElBP,KAAK,CAACgC,gBAAgB,CAAC,CAAC;cAExBhC,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAEhB;YACF,CAAC,CAAC;;YAGF,IAAI3D,YAAY,IAAI,CAACuD,QAAQ,CAACS,MAAM,IAAIjE,aAAa,CAACC,YAAY,EAAEgD,SAAS,EAAEM,KAAK,EAAEnD,SAAS,EAAEqD,QAAQ,EAAEnD,IAAI,CAAC,EAAE;cAChHM,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAEhB;YACF;YAEA;UACF;QAEF,KAAK,oBAAoB;UACvB;YACE;AACZ;AACA;YACY,IAAIM,cAAc,GAAGb,YAAY,CAACd,GAAG,CAAC1C,WAAW,CAAC,CAAC,CAAC;YACpD;YACA;;YAEA,IAAIqE,cAAc,CAACC,IAAI,CAAC,UAAUC,UAAU,EAAE;cAC5C,OAAOzE,gBAAgB,CAACW,IAAI,CAAC+D,aAAa,EAAED,UAAU,CAAC;YACzD,CAAC,CAAC,EAAE;cACFxD,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAEhB;YACF;YAEA;UACF;QAEF;UACE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIF,aAAa,IAAI,CAAC,CAACL,YAAY,CAACY,MAAM,IAAIT,QAAQ,CAACS,MAAM,IAAIhE,YAAY,KAAKD,aAAa,CAACC,YAAY,EAAEgD,SAAS,EAAEM,KAAK,EAAEnD,SAAS,EAAEqD,QAAQ,EAAEnD,IAAI,CAAC,EAAE;YAC1JM,KAAK,CAACgD,QAAQ,CAAC,CAAC;YAEhB;UACF;UAEA;MACJ;MAEA,IAAI3D,YAAY,KAAK,IAAI,EAAE;QACzBW,KAAK,CAACgD,QAAQ,CAAC,CAAC;MAClB;IACF,CAAC;IAEDhD,KAAK,CAAClB,aAAa,GAAG,UAAU4E,OAAO,EAAE;MACvC;MACA,IAAId,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAElC,IAAI0E,YAAY,GAAG3D,KAAK,CAACd,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAGrC,IAAI0E,WAAW,GAAGT,OAAO,CAACC,OAAO,CAAC,CAAC,CAACS,IAAI,CAAC,YAAY;QACnD,IAAI,CAAC7D,KAAK,CAACK,OAAO,EAAE;UAClB,OAAO,EAAE;QACX;QAEA,IAAIyD,YAAY,GAAG9D,KAAK,CAACD,KAAK;UAC1BgE,qBAAqB,GAAGD,YAAY,CAACE,aAAa;UAClDA,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;UAChFE,gBAAgB,GAAGH,YAAY,CAACG,gBAAgB;QAEpD,IAAIC,KAAK,GAAGR,OAAO,IAAI,CAAC,CAAC;UACrBS,WAAW,GAAGD,KAAK,CAACC,WAAW;QAEnC,IAAIC,aAAa,GAAGpE,KAAK,CAACuB,QAAQ,CAAC,CAAC;QAEpC,IAAI4C,WAAW,EAAE;UACfC,aAAa,GAAGA,aAAa,CAACC,MAAM,CAAC,UAAUzC,IAAI,EAAE;YACnD,IAAI0C,eAAe,GAAG1C,IAAI,CAAC0C,eAAe;YAE1C,IAAI,CAACA,eAAe,EAAE;cACpB,OAAO,IAAI;YACb;YAEA,IAAIC,WAAW,GAAG1F,OAAO,CAACyF,eAAe,CAAC;YAC1C,OAAOC,WAAW,CAACC,QAAQ,CAACL,WAAW,CAAC;UAC1C,CAAC,CAAC;QACJ;QAEA,IAAIM,OAAO,GAAG3F,aAAa,CAAC8D,QAAQ,EAAEe,YAAY,EAAES,aAAa,EAAEV,OAAO,EAAEM,aAAa,EAAEC,gBAAgB,CAAC;QAC5GQ,OAAO,CAACC,KAAK,CAAC,UAAUC,CAAC,EAAE;UACzB,OAAOA,CAAC;QACV,CAAC,CAAC,CAACd,IAAI,CAAC,YAAY;UAClB,IAAIe,UAAU,GAAGC,SAAS,CAACxB,MAAM,GAAG,CAAC,IAAIwB,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG1F,YAAY;UAEjG,IAAIa,KAAK,CAACQ,eAAe,KAAKoD,WAAW,EAAE;YACzC5D,KAAK,CAACQ,eAAe,GAAG,IAAI,CAAC,CAAC;;YAE9B,IAAIsE,UAAU,GAAG,EAAE;YACnB,IAAIC,YAAY,GAAG,EAAE;YACrBH,UAAU,CAACI,OAAO,CAAC,UAAUC,KAAK,EAAE;cAClC,IAAIC,WAAW,GAAGD,KAAK,CAACrD,IAAI,CAACsD,WAAW;gBACpCC,YAAY,GAAGF,KAAK,CAACvE,MAAM;gBAC3BA,MAAM,GAAGyE,YAAY,KAAK,KAAK,CAAC,GAAGhG,YAAY,GAAGgG,YAAY;cAElE,IAAID,WAAW,EAAE;gBACfH,YAAY,CAACK,IAAI,CAACC,KAAK,CAACN,YAAY,EAAE9G,kBAAkB,CAACyC,MAAM,CAAC,CAAC;cACnE,CAAC,MAAM;gBACLoE,UAAU,CAACM,IAAI,CAACC,KAAK,CAACP,UAAU,EAAE7G,kBAAkB,CAACyC,MAAM,CAAC,CAAC;cAC/D;YACF,CAAC,CAAC;YACFV,KAAK,CAACU,MAAM,GAAGoE,UAAU;YACzB9E,KAAK,CAACW,QAAQ,GAAGoE,YAAY;YAE7B/E,KAAK,CAACgC,gBAAgB,CAAC,CAAC;YAExBhC,KAAK,CAACgD,QAAQ,CAAC,CAAC;UAClB;QACF,CAAC,CAAC;QACF,OAAOyB,OAAO;MAChB,CAAC,CAAC;MACFzE,KAAK,CAACQ,eAAe,GAAGoD,WAAW;MACnC5D,KAAK,CAACO,KAAK,GAAG,IAAI;MAClBP,KAAK,CAACU,MAAM,GAAGvB,YAAY;MAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;MAE7Ba,KAAK,CAACgC,gBAAgB,CAAC,CAAC,CAAC,CAAC;;MAG1BhC,KAAK,CAACgD,QAAQ,CAAC,CAAC;MAEhB,OAAOY,WAAW;IACpB,CAAC;IAED5D,KAAK,CAACsF,iBAAiB,GAAG,YAAY;MACpC,OAAO,CAAC,CAACtF,KAAK,CAACQ,eAAe;IAChC,CAAC;IAEDR,KAAK,CAACuF,cAAc,GAAG,YAAY;MACjC,OAAOvF,KAAK,CAACM,OAAO;IACtB,CAAC;IAEDN,KAAK,CAACwF,YAAY,GAAG,YAAY;MAC/B;MACA,IAAIxF,KAAK,CAACO,KAAK,IAAIP,KAAK,CAACD,KAAK,CAAC0F,YAAY,KAAKpE,SAAS,EAAE;QACzD,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,IAAIH,YAAY,GAAGlB,KAAK,CAACD,KAAK,CAACmB,YAAY;MAE3C,IAAIwE,qBAAqB,GAAGxE,YAAY,CAACyE,gBAAgB,CAAC/G,SAAS,CAAC;QAChEgH,eAAe,GAAGF,qBAAqB,CAACE,eAAe;MAE3D,IAAIA,eAAe,CAAC5F,KAAK,CAACf,WAAW,CAAC,CAAC,CAAC,KAAKoC,SAAS,EAAE;QACtD,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC;IAEDrB,KAAK,CAAC6F,SAAS,GAAG,YAAY;MAC5B,OAAO7F,KAAK,CAACU,MAAM;IACrB,CAAC;IAEDV,KAAK,CAAC8F,WAAW,GAAG,YAAY;MAC9B,OAAO9F,KAAK,CAACW,QAAQ;IACvB,CAAC;IAEDX,KAAK,CAACe,WAAW,GAAG,YAAY;MAC9B,OAAOf,KAAK,CAACD,KAAK,CAACgB,WAAW;IAChC,CAAC;IAEDf,KAAK,CAAC+F,MAAM,GAAG,YAAY;MACzB,OAAO/F,KAAK,CAACD,KAAK,CAACgG,MAAM;IAC3B,CAAC;IAED/F,KAAK,CAACgG,UAAU,GAAG,YAAY;MAC7B,OAAOhG,KAAK,CAACD,KAAK,CAACe,QAAQ;IAC7B,CAAC;IAEDd,KAAK,CAACmC,OAAO,GAAG,YAAY;MAC1B;MACAnC,KAAK,CAACS,cAAc,GAAGT,KAAK,CAACsF,iBAAiB,CAAC,CAAC;MAChD,IAAIW,IAAI,GAAG;QACT3F,OAAO,EAAEN,KAAK,CAACuF,cAAc,CAAC,CAAC;QAC/BrC,UAAU,EAAElD,KAAK,CAACS,cAAc;QAChCC,MAAM,EAAEV,KAAK,CAACU,MAAM;QACpBC,QAAQ,EAAEX,KAAK,CAACW,QAAQ;QACxBK,IAAI,EAAEhB,KAAK,CAACf,WAAW,CAAC;MAC1B,CAAC;MACD,OAAOgH,IAAI;IACb,CAAC;IAEDjG,KAAK,CAACkG,YAAY,GAAG,UAAUC,QAAQ,EAAE;MACvC;MACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAIF,IAAI,GAAGjG,KAAK,CAACmC,OAAO,CAAC,CAAC;QAE1B,OAAOnE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACkG,YAAY,CAACC,QAAQ,CAACnG,KAAK,CAACoG,aAAa,CAAC,CAAC,EAAEH,IAAI,EAAEjG,KAAK,CAACD,KAAK,CAACmB,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/HmF,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;;MAGF,IAAIC,SAAS,GAAG9H,eAAe,CAAC2H,QAAQ,CAAC;MAEzC,IAAIG,SAAS,CAACjD,MAAM,KAAK,CAAC,IAAI,EAAE,aAAa3E,KAAK,CAAC6H,cAAc,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/E,OAAO;UACLE,KAAK,EAAEF,SAAS;UAChBD,UAAU,EAAE;QACd,CAAC;MACH;MAEA,OAAO;QACLG,KAAK,EAAEF,SAAS,CAAC,CAAC,CAAC;QACnBD,UAAU,EAAE;MACd,CAAC;IACH,CAAC;IAEDrG,KAAK,CAACd,QAAQ,GAAG,UAAUyD,KAAK,EAAE;MAChC,IAAI8D,cAAc,GAAGzG,KAAK,CAACD,KAAK,CAACmB,YAAY,CAACuF,cAAc;MAE5D,IAAI7D,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAElC,OAAOC,QAAQ,CAACyD,KAAK,IAAI8D,cAAc,CAAC,IAAI,CAAC,EAAE7D,QAAQ,CAAC;IAC1D,CAAC;IAED5C,KAAK,CAACoG,aAAa,GAAG,YAAY;MAChC,IAAIM,UAAU,GAAG7B,SAAS,CAACxB,MAAM,GAAG,CAAC,IAAIwB,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvF,IAAI8B,YAAY,GAAG3G,KAAK,CAACD,KAAK;QAC1B6G,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BtC,eAAe,GAAGqC,YAAY,CAACrC,eAAe;QAC9CuC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClCC,aAAa,GAAGJ,YAAY,CAACI,aAAa;QAC1CC,aAAa,GAAGL,YAAY,CAACK,aAAa;QAC1C9F,YAAY,GAAGyF,YAAY,CAACzF,YAAY;MAC5C,IAAI+F,qBAAqB,GAAG3C,eAAe,KAAKjD,SAAS,GAAGiD,eAAe,GAAGpD,YAAY,CAACoD,eAAe;MAE1G,IAAI1B,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAElC,IAAI0G,gBAAgB,GAAGzE,YAAY,CAACyE,gBAAgB;QAChDc,cAAc,GAAGvF,YAAY,CAACuF,cAAc;MAEhD,IAAIS,iBAAiB,GAAGvB,gBAAgB,CAAC/G,SAAS,CAAC;QAC/CuI,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;MAEzC,IAAIC,KAAK,GAAGpH,KAAK,CAACd,QAAQ,CAAC,CAAC;MAE5B,IAAImI,mBAAmB,GAAGL,aAAa,IAAI,UAAUM,GAAG,EAAE;QACxD,OAAOvJ,eAAe,CAAC,CAAC,CAAC,EAAEgJ,aAAa,EAAEO,GAAG,CAAC;MAChD,CAAC,CAAC,CAAC;;MAGH,IAAIC,iBAAiB,GAAGb,UAAU,CAACE,OAAO,CAAC;MAE3C,IAAIY,OAAO,GAAGxJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,UAAU,CAAC,EAAEW,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;;MAGxFI,OAAO,CAACZ,OAAO,CAAC,GAAG,YAAY;QAC7B;QACA5G,KAAK,CAACM,OAAO,GAAG,IAAI;QACpBN,KAAK,CAACO,KAAK,GAAG,IAAI;QAElBP,KAAK,CAACgC,gBAAgB,CAAC,CAAC;QAExB,IAAIyF,QAAQ;QAEZ,KAAK,IAAIC,IAAI,GAAG7C,SAAS,CAACxB,MAAM,EAAEsE,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;UACvFF,IAAI,CAACE,IAAI,CAAC,GAAGhD,SAAS,CAACgD,IAAI,CAAC;QAC9B;QAEA,IAAIhB,iBAAiB,EAAE;UACrBY,QAAQ,GAAGZ,iBAAiB,CAACxB,KAAK,CAAC,KAAK,CAAC,EAAEsC,IAAI,CAAC;QAClD,CAAC,MAAM;UACLF,QAAQ,GAAGzI,wBAAwB,CAACqG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC0B,aAAa,CAAC,CAACzF,MAAM,CAACqG,IAAI,CAAC,CAAC;QACjF;QAEA,IAAIb,SAAS,EAAE;UACbW,QAAQ,GAAGX,SAAS,CAACW,QAAQ,EAAEL,KAAK,EAAEX,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7D;QAEAU,QAAQ,CAAC;UACPpE,IAAI,EAAE,aAAa;UACnBH,QAAQ,EAAEA,QAAQ;UAClBwE,KAAK,EAAEK;QACT,CAAC,CAAC;QAEF,IAAIF,iBAAiB,EAAE;UACrBA,iBAAiB,CAAClC,KAAK,CAAC,KAAK,CAAC,EAAEsC,IAAI,CAAC;QACvC;MACF,CAAC,CAAC,CAAC;;MAGH,IAAIG,mBAAmB,GAAGjJ,OAAO,CAACoI,qBAAqB,IAAI,EAAE,CAAC;MAC9Da,mBAAmB,CAAC9C,OAAO,CAAC,UAAUb,WAAW,EAAE;QACjD;QACA,IAAI4D,aAAa,GAAGP,OAAO,CAACrD,WAAW,CAAC;QAExCqD,OAAO,CAACrD,WAAW,CAAC,GAAG,YAAY;UACjC,IAAI4D,aAAa,EAAE;YACjBA,aAAa,CAAC1C,KAAK,CAAC,KAAK,CAAC,EAAER,SAAS,CAAC;UACxC,CAAC,CAAC;;UAGF,IAAInD,KAAK,GAAG1B,KAAK,CAACD,KAAK,CAAC2B,KAAK;UAE7B,IAAIA,KAAK,IAAIA,KAAK,CAAC2B,MAAM,EAAE;YACzB;YACA;YACA8D,QAAQ,CAAC;cACPpE,IAAI,EAAE,eAAe;cACrBH,QAAQ,EAAEA,QAAQ;cAClBuB,WAAW,EAAEA;YACf,CAAC,CAAC;UACJ;QACF,CAAC;MACH,CAAC,CAAC;MACF,OAAOqD,OAAO;IAChB,CAAC;IAED,IAAIzH,KAAK,CAACmB,YAAY,EAAE;MACtB,IAAIyE,gBAAgB,GAAG5F,KAAK,CAACmB,YAAY,CAACyE,gBAAgB;MAE1D,IAAIqC,kBAAkB,GAAGrC,gBAAgB,CAAC/G,SAAS,CAAC;QAChDqJ,eAAe,GAAGD,kBAAkB,CAACC,eAAe;MAExDA,eAAe,CAAC7J,sBAAsB,CAAC4B,KAAK,CAAC,CAAC;IAChD;IAEA,OAAOA,KAAK;EACd;EAEA7B,YAAY,CAACyB,KAAK,EAAE,CAAC;IACnBsI,GAAG,EAAE,mBAAmB;IACxBd,KAAK,EAAE,SAASe,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,YAAY,GAAG,IAAI,CAACrI,KAAK;QACzBV,YAAY,GAAG+I,YAAY,CAAC/I,YAAY;QACxC6B,YAAY,GAAGkH,YAAY,CAAClH,YAAY;MAC5C,IAAI,CAACb,OAAO,GAAG,IAAI,CAAC,CAAC;;MAErB,IAAIa,YAAY,EAAE;QAChB,IAAIyE,gBAAgB,GAAGzE,YAAY,CAACyE,gBAAgB;QAEpD,IAAI0C,kBAAkB,GAAG1C,gBAAgB,CAAC/G,SAAS,CAAC;UAChD0J,aAAa,GAAGD,kBAAkB,CAACC,aAAa;QAEpD,IAAI,CAAClI,kBAAkB,GAAGkI,aAAa,CAAC,IAAI,CAAC;MAC/C,CAAC,CAAC;;MAGF,IAAIjJ,YAAY,KAAK,IAAI,EAAE;QACzB,IAAI,CAAC2D,QAAQ,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE;IACDkF,GAAG,EAAE,sBAAsB;IAC3Bd,KAAK,EAAE,SAASmB,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAC3H,cAAc,CAAC,CAAC;MACrB,IAAI,CAACoB,gBAAgB,CAAC,IAAI,CAAC;MAC3B,IAAI,CAAC3B,OAAO,GAAG,KAAK;IACtB;EACF,CAAC,EAAE;IACD6H,GAAG,EAAE,UAAU;IACfd,KAAK,EAAE,SAASpE,QAAQA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC3C,OAAO,EAAE;MACnB,IAAI,CAACmI,WAAW,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,QAAQ;IACbd,KAAK,EAAE,SAASqB,MAAMA,CAAA,EAAG;MACvB,IAAItI,UAAU,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU;MACtC,IAAIgG,QAAQ,GAAG,IAAI,CAACpG,KAAK,CAACoG,QAAQ;MAElC,IAAIuC,kBAAkB,GAAG,IAAI,CAACxC,YAAY,CAACC,QAAQ,CAAC;QAChDK,KAAK,GAAGkC,kBAAkB,CAAClC,KAAK;QAChCH,UAAU,GAAGqC,kBAAkB,CAACrC,UAAU,CAAC,CAAC;;MAGhD,IAAIsC,eAAe;MAEnB,IAAItC,UAAU,EAAE;QACdsC,eAAe,GAAGnC,KAAK;MACzB,CAAC,MAAM,IAAK,aAAa9H,KAAK,CAAC6H,cAAc,CAACC,KAAK,CAAC,EAAE;QACpDmC,eAAe,GAAG,aAAajK,KAAK,CAACkK,YAAY,CAACpC,KAAK,EAAE,IAAI,CAACJ,aAAa,CAACI,KAAK,CAACzG,KAAK,CAAC,CAAC;MAC3F,CAAC,MAAM;QACLtB,OAAO,CAAC,CAAC+H,KAAK,EAAE,mDAAmD,CAAC;QACpEmC,eAAe,GAAGnC,KAAK;MACzB;MAEA,OAAO,aAAa9H,KAAK,CAACmK,aAAa,CAACnK,KAAK,CAACoK,QAAQ,EAAE;QACtDZ,GAAG,EAAE/H;MACP,CAAC,EAAEwI,eAAe,CAAC;IACrB;EACF,CAAC,CAAC,CAAC;EAEH,OAAO/I,KAAK;AACd,CAAC,CAAClB,KAAK,CAACqK,SAAS,CAAC;AAElBnJ,KAAK,CAACoJ,WAAW,GAAGrK,YAAY;AAChCiB,KAAK,CAACqJ,YAAY,GAAG;EACnBrC,OAAO,EAAE,UAAU;EACnBG,aAAa,EAAE;AACjB,CAAC;AAED,SAASmC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAInI,IAAI,GAAGmI,KAAK,CAACnI,IAAI;IACjBoI,SAAS,GAAGtL,wBAAwB,CAACqL,KAAK,EAAE5K,SAAS,CAAC;EAE1D,IAAI2C,YAAY,GAAGxC,KAAK,CAAC2K,UAAU,CAAC1K,YAAY,CAAC;EACjD,IAAIiE,QAAQ,GAAG5B,IAAI,KAAKK,SAAS,GAAGpC,WAAW,CAAC+B,IAAI,CAAC,GAAGK,SAAS;EACjE,IAAI6G,GAAG,GAAG,MAAM;EAEhB,IAAI,CAACkB,SAAS,CAACrI,WAAW,EAAE;IAC1BmH,GAAG,GAAG,GAAG,CAAC5G,MAAM,CAAC,CAACsB,QAAQ,IAAI,EAAE,EAAE0G,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC,CAAC;EACF;;EAGA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,SAAS,CAACtI,QAAQ,KAAK,KAAK,IAAIsI,SAAS,CAACrI,WAAW,IAAI6B,QAAQ,CAACS,MAAM,IAAI,CAAC,EAAE;IAC1H5E,OAAO,CAAC,KAAK,EAAE,kDAAkD,CAAC;EACpE;EAEA,OAAO,aAAaC,KAAK,CAACmK,aAAa,CAACjJ,KAAK,EAAE/B,QAAQ,CAAC;IACtDqK,GAAG,EAAEA,GAAG;IACRlH,IAAI,EAAE4B;EACR,CAAC,EAAEwG,SAAS,EAAE;IACZlI,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC;AACL;AAEA,eAAegI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}