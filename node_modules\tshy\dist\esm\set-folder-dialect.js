import chalk from 'chalk';
import { writeFileSync } from 'fs';
import { rimrafSync } from 'rimraf';
import * as console from './console.js';
import getImports from './built-imports.js';
import pkg from './package.js';
const writeDialectPJ = (d, mode) => {
    if (!mode) {
        return rimrafSync(`${d}/package.json`);
    }
    const v = {
        type: mode === 'commonjs' ? 'commonjs' : 'module',
        imports: getImports(pkg),
    };
    writeFileSync(`${d}/package.json`, JSON.stringify(v, null, 2) + '\n');
};
export default (where, mode) => {
    if (mode)
        console.debug(chalk.cyan.dim('set dialect'), { where, mode });
    writeDialectPJ(where, mode);
};
//# sourceMappingURL=set-folder-dialect.js.map