{"version": 3, "file": "package.js", "sourceRoot": "", "sources": ["../../src/package.ts"], "names": [], "mappings": "AAAA,wCAAwC;AAExC,OAAO,EAAE,YAAY,EAAE,MAAM,IAAI,CAAA;AACjC,OAAO,EAAc,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAA;AAC1D,OAAO,IAAI,MAAM,WAAW,CAAA;AAG5B,MAAM,SAAS,GAAG,CAAC,GAAe,EAAkB,EAAE,CACpD,CAAC,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AAEzD,MAAM,OAAO,GAAG,GAAY,EAAE;IAC5B,IAAI,CAAC;QACH,MAAM,GAAG,GAAG,KAAK,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAA;QACvD,IAAI,SAAS,CAAC,GAAG,CAAC;YAAE,OAAO,GAAG,CAAA;QAC9B,MAAM,IAAI,KAAK,CACb,iCAAiC,GAAG,SAAS,CAAC,GAAG,CAAC,CACnD,CAAA;IACH,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAI,CAAC,6BAA6B,EAAE,EAAW,CAAC,CAAA;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;AACH,CAAC,CAAA;AAED,eAAe,OAAO,EAAE,CAAA", "sourcesContent": ["// get the package.json data for the cwd\n\nimport { readFileSync } from 'fs'\nimport { JSONResult, parse, stringify } from 'polite-json'\nimport fail from './fail.js'\nimport { Package } from './types.js'\n\nconst isPackage = (pkg: JSONResult): pkg is Package =>\n  !!pkg && typeof pkg === 'object' && !Array.isArray(pkg)\n\nconst readPkg = (): Package => {\n  try {\n    const res = parse(readFileSync('package.json', 'utf8'))\n    if (isPackage(res)) return res\n    throw new Error(\n      'Invalid package.json contents: ' + stringify(res),\n    )\n  } catch (er) {\n    fail('failed to read package.json', er as Error)\n    process.exit(1)\n  }\n}\n\nexport default readPkg()\n"]}