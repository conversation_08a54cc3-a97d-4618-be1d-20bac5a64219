{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    this.maps = void 0;\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.maps[key] = value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "CacheMap", "maps", "create", "value", "set", "get"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-virtual-list/es/utils/CacheMap.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n\n    this.maps = void 0;\n    this.maps = Object.create(null);\n  }\n\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.maps[key] = value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n  }]);\n\n  return CacheMap;\n}();\n\nexport default CacheMap;"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAEL,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;;AAE5R;AACA,IAAIkB,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG;IAClBpB,eAAe,CAAC,IAAI,EAAEoB,QAAQ,CAAC;IAE/B,IAAI,CAACC,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACA,IAAI,GAAGR,MAAM,CAACS,MAAM,CAAC,IAAI,CAAC;EACjC;EAEAN,YAAY,CAACI,QAAQ,EAAE,CAAC;IACtBL,GAAG,EAAE,KAAK;IACVQ,KAAK,EAAE,SAASC,GAAGA,CAACT,GAAG,EAAEQ,KAAK,EAAE;MAC9B,IAAI,CAACF,IAAI,CAACN,GAAG,CAAC,GAAGQ,KAAK;IACxB;EACF,CAAC,EAAE;IACDR,GAAG,EAAE,KAAK;IACVQ,KAAK,EAAE,SAASE,GAAGA,CAACV,GAAG,EAAE;MACvB,OAAO,IAAI,CAACM,IAAI,CAACN,GAAG,CAAC;IACvB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOK,QAAQ;AACjB,CAAC,CAAC,CAAC;AAEH,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}