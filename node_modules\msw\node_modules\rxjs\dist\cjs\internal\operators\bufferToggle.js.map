{"version": 3, "file": "bufferToggle.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/bufferToggle.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,gDAA+C;AAE/C,qCAAuC;AACvC,qDAAoD;AACpD,2DAAgE;AAChE,qCAAoC;AACpC,+CAA8C;AA6C9C,SAAgB,YAAY,CAC1B,QAA4B,EAC5B,eAAmD;IAEnD,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAM,OAAO,GAAU,EAAE,CAAC;QAG1B,qBAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAC3B,6CAAwB,CACtB,UAAU,EACV,UAAC,SAAS;YACR,IAAM,MAAM,GAAQ,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAGrB,IAAM,mBAAmB,GAAG,IAAI,2BAAY,EAAE,CAAC;YAE/C,IAAM,UAAU,GAAG;gBACjB,qBAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC3B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACpC,CAAC,CAAC;YAGF,mBAAmB,CAAC,GAAG,CAAC,qBAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,6CAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,WAAI,CAAC,CAAC,CAAC,CAAC;QACnI,CAAC,EACD,WAAI,CACL,CACF,CAAC;QAEF,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK;;;gBAEJ,KAAqB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;oBAAzB,IAAM,MAAM,oBAAA;oBACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;;;;;;;;;QACH,CAAC,EACD;YAEE,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAG,CAAC,CAAC;aACnC;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAlDD,oCAkDC"}