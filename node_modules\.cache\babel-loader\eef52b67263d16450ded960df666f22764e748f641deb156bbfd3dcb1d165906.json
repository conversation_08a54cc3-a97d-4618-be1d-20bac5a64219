{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport useState from \"rc-util/es/hooks/useState\";\n/**\n * State generate. Return a `setState` but it will flush all state with one render to save perf.\n * This is not a realization of `unstable_batchedUpdates`.\n */\n\nexport function useBatchFrameState() {\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    forceUpdate = _useState2[1];\n  var statesRef = useRef([]);\n  var walkingIndex = 0;\n  var beforeFrameId = 0;\n  function createState(defaultValue) {\n    var myIndex = walkingIndex;\n    walkingIndex += 1; // Fill value if not exist yet\n\n    if (statesRef.current.length < myIndex + 1) {\n      statesRef.current[myIndex] = defaultValue;\n    } // Return filled as `setState`\n\n    var value = statesRef.current[myIndex];\n    function setValue(val) {\n      statesRef.current[myIndex] = typeof val === 'function' ? val(statesRef.current[myIndex]) : val;\n      raf.cancel(beforeFrameId); // Flush with batch\n\n      beforeFrameId = raf(function () {\n        forceUpdate({}, true);\n      });\n    }\n    return [value, setValue];\n  }\n  return createState;\n}", "map": {"version": 3, "names": ["_slicedToArray", "useRef", "raf", "useState", "useBatchFrameState", "_useState", "_useState2", "forceUpdate", "statesRef", "walkingIndex", "beforeFrameId", "createState", "defaultValue", "myIndex", "current", "length", "value", "setValue", "val", "cancel"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-overflow/es/hooks/useBatchFrameState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport useState from \"rc-util/es/hooks/useState\";\n/**\n * State generate. Return a `setState` but it will flush all state with one render to save perf.\n * This is not a realization of `unstable_batchedUpdates`.\n */\n\nexport function useBatchFrameState() {\n  var _useState = useState({}),\n      _useState2 = _slicedToArray(_useState, 2),\n      forceUpdate = _useState2[1];\n\n  var statesRef = useRef([]);\n  var walkingIndex = 0;\n  var beforeFrameId = 0;\n\n  function createState(defaultValue) {\n    var myIndex = walkingIndex;\n    walkingIndex += 1; // Fill value if not exist yet\n\n    if (statesRef.current.length < myIndex + 1) {\n      statesRef.current[myIndex] = defaultValue;\n    } // Return filled as `setState`\n\n\n    var value = statesRef.current[myIndex];\n\n    function setValue(val) {\n      statesRef.current[myIndex] = typeof val === 'function' ? val(statesRef.current[myIndex]) : val;\n      raf.cancel(beforeFrameId); // Flush with batch\n\n      beforeFrameId = raf(function () {\n        forceUpdate({}, true);\n      });\n    }\n\n    return [value, setValue];\n  }\n\n  return createState;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD;AACA;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACnC,IAAIC,SAAS,GAAGF,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxBG,UAAU,GAAGN,cAAc,CAACK,SAAS,EAAE,CAAC,CAAC;IACzCE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE/B,IAAIE,SAAS,GAAGP,MAAM,CAAC,EAAE,CAAC;EAC1B,IAAIQ,YAAY,GAAG,CAAC;EACpB,IAAIC,aAAa,GAAG,CAAC;EAErB,SAASC,WAAWA,CAACC,YAAY,EAAE;IACjC,IAAIC,OAAO,GAAGJ,YAAY;IAC1BA,YAAY,IAAI,CAAC,CAAC,CAAC;;IAEnB,IAAID,SAAS,CAACM,OAAO,CAACC,MAAM,GAAGF,OAAO,GAAG,CAAC,EAAE;MAC1CL,SAAS,CAACM,OAAO,CAACD,OAAO,CAAC,GAAGD,YAAY;IAC3C,CAAC,CAAC;;IAGF,IAAII,KAAK,GAAGR,SAAS,CAACM,OAAO,CAACD,OAAO,CAAC;IAEtC,SAASI,QAAQA,CAACC,GAAG,EAAE;MACrBV,SAAS,CAACM,OAAO,CAACD,OAAO,CAAC,GAAG,OAAOK,GAAG,KAAK,UAAU,GAAGA,GAAG,CAACV,SAAS,CAACM,OAAO,CAACD,OAAO,CAAC,CAAC,GAAGK,GAAG;MAC9FhB,GAAG,CAACiB,MAAM,CAACT,aAAa,CAAC,CAAC,CAAC;;MAE3BA,aAAa,GAAGR,GAAG,CAAC,YAAY;QAC9BK,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;MACvB,CAAC,CAAC;IACJ;IAEA,OAAO,CAACS,KAAK,EAAEC,QAAQ,CAAC;EAC1B;EAEA,OAAON,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}