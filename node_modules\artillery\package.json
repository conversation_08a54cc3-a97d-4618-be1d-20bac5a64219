{"name": "artillery", "version": "2.0.23", "description": "Cloud-scale load testing. https://www.artillery.io", "main": "./lib", "engines": {"node": ">= 22.13.0"}, "files": ["/bin", "/lib", "console-reporter.js", "util.js", ".artilleryrc"], "oclif": {"update": {"s3": {"bucket": "artillery-cli-assets"}}, "commands": "./lib/cmds", "hooks": {"init": ["./lib/cli/hooks/version"]}, "bin": "artillery", "_helpClass": "./bin/help", "plugins": ["@oclif/plugin-help", "@oclif/plugin-not-found"], "topics": {"aws": {"description": "run tests on AWS", "hidden": true}, "pro": {"description": "deploy and manage Artillery Pro", "hidden": true}}}, "scripts": {"test:unit": "tap --timeout=420 test/unit/*.test.js", "test:acceptance": "tap --timeout=420 test/cli/*.test.js && bash test/lib/run.sh && tap --timeout=420 test/publish-metrics/**/*.test.js && tap --timeout=420 test/integration/**/*.test.js", "test": " npm run test:unit && npm run test:acceptance", "test:windows": "npm run test:unit && tap --timeout=420 test/cli/*.test.js", "test:aws": "tap --timeout=4200 test/cloud-e2e/**/*.test.js", "test:aws:ci": "tap --timeout=4200", "test:aws:windows": "tap --timeout=420 test/cloud-e2e/**/*.test.js --grep \"@windows\"", "lint": "eslint --ext \".js,.ts,.tsx\" .", "lint-fix": "npm run lint -- --fix"}, "tap": {"disable-coverage": true, "allow-empty-coverage": true, "color": true, "test-env": ["ARTILLERY_TELEMETRY_DEFAULTS={\"source\":\"test-suite\"}"]}, "lint-staged": {"**/*.{js,ts,tsx}": "eslint --fix"}, "keywords": ["load testing", "stress testing", "benchmark", "performance", "blackbox testing"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON> (https://github.com/kjgorman)", "<PERSON> (https://github.com/antony)", "<PERSON> (https://github.com/Joe<PERSON>cho)", "<PERSON><PERSON> (https://github.com/kush-jain)"], "license": "MPL-2.0", "preferGlobal": true, "man": "./man/artillery.1", "bin": {"artillery": "./bin/run"}, "repository": {"type": "git", "url": "https://github.com/artilleryio/artillery.git"}, "bugs": {"url": "https://github.com/artilleryio/artillery/issues", "email": "<EMAIL>"}, "dependencies": {"@artilleryio/int-commons": "2.14.0", "@artilleryio/int-core": "2.18.0", "@aws-sdk/credential-providers": "^3.127.0", "@azure/arm-containerinstance": "^9.1.0", "@azure/identity": "^4.2.0", "@azure/storage-blob": "^12.18.0", "@azure/storage-queue": "^12.22.0", "@oclif/core": "^4.0.25", "@oclif/plugin-help": "^6.2.13", "@oclif/plugin-not-found": "^3.2.22", "archiver": "^5.3.1", "artillery-engine-playwright": "1.20.0", "artillery-plugin-apdex": "1.14.0", "artillery-plugin-ensure": "1.17.0", "artillery-plugin-expect": "2.17.0", "artillery-plugin-fake-data": "1.14.0", "artillery-plugin-metrics-by-endpoint": "1.17.0", "artillery-plugin-publish-metrics": "2.28.0", "artillery-plugin-slack": "1.12.0", "async": "^2.6.4", "aws-sdk": "^2.1338.0", "chalk": "^2.4.2", "chokidar": "^3.6.0", "ci-info": "^4.0.0", "cli-table3": "^0.6.0", "cross-spawn": "^7.0.3", "csv-parse": "^4.16.3", "debug": "^4.3.1", "dependency-tree": "^10.0.9", "detective-es6": "^4.0.1", "dotenv": "^16.0.1", "driftless": "^2.0.3", "esbuild-wasm": "^0.19.8", "eventemitter3": "^4.0.4", "fs-extra": "^10.1.0", "got": "^11.8.5", "joi": "^17.6.0", "js-yaml": "^3.13.1", "jsonwebtoken": "^9.0.1", "lodash": "^4.17.19", "moment": "^2.29.4", "nanoid": "^3.3.4", "ora": "^4.0.4", "posthog-node": "^4.2.1", "rc": "^1.2.8", "sqs-consumer": "5.8.0", "temp": "^0.9.4", "tmp": "0.2.1", "walk-sync": "^0.2.3", "yaml-js": "^0.2.3"}, "devDependencies": {"@hapi/hapi": "^20.1.3", "eslint": "^8.6.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "execa": "^0.10.0", "get-bin-path": "^5.1.0", "rewiremock": "^3.14.3", "sinon": "^4.5.0", "tap": "^19.0.2", "zx": "^8.1.4"}}