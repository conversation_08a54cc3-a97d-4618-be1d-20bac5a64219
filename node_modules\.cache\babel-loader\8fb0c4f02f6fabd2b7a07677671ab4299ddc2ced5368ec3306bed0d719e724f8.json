{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\composizioneMagazzino.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* OpJobsUscita - operazioni sulle lavorazioni in uscita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { AggiungiCommposizioneMagazzino } from \"../../aggiunta_dati/aggiungiCommposizioneMagazzino\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\n/* import { InputNumber } from \"primereact/inputnumber\"; */\nimport { InputText } from \"primereact/inputtext\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ComposizioneMagazzino extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      area: \"\",\n      scaffale: \"\",\n      ripiano: \"\",\n      posizione: \"\",\n      eanCode: \"\"\n    };\n    /* Seleziono il punto vendita e controllo che abbia un listino associato */\n    this.onWarehouseSelect = async e => {\n      console.log(e);\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      var url = 'warehousescomp?idWarehouse=' + e.value;\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      await APIRequest('GET', url).then(res => {\n        this.setState({\n          results: res.data,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      result: this.emptyResult,\n      value1: null,\n      value2: null,\n      value3: null,\n      value4: null,\n      value5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      selectedWarehouse: null,\n      displayed: false,\n      loading: true\n    };\n    this.warehouse = [];\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.aggiungiCompMag = this.aggiungiCompMag.bind(this);\n    this.hideCompMag = this.hideCompMag.bind(this);\n    this.modificaComp = this.modificaComp.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.modifica = this.modifica.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'warehousescomp?idWarehouse=' + idWarehouse;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        this.setState({\n          results: res.data,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog3: true,\n        displayed: true\n      });\n    }\n  }\n  aggiungiCompMag() {\n    this.setState({\n      resultDialog: true\n    });\n  }\n  hideCompMag() {\n    this.setState({\n      resultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"warehousescomp/?idComp=\" + this.state.result.id;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    window.location.reload();\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Posizione eliminata con successo\",\n      life: 3000\n    });\n  }\n  modificaComp(result) {\n    this.setState({\n      result,\n      resultDialog2: true,\n      value1: result.area,\n      value2: result.scaffale,\n      value3: result.ripiano,\n      value4: result.posizione,\n      value5: result.eanCode\n    });\n  }\n  hideDialog() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  async modifica() {\n    var body = {\n      composition: {\n        id: this.state.result.id,\n        area: this.state.value1,\n        scaffale: this.state.value2,\n        ripiano: this.state.value3,\n        posizione: this.state.value4,\n        eanCode: this.state.value5\n      }\n    };\n    await APIRequest('PUT', 'warehousescomp', body).then(async res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"Posizione modificata con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile modificare la Posizione. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog3: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideCompMag,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 13\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: 'area',\n      header: Costanti.Area,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'scaffale',\n      header: Costanti.Scaffale,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'ripiano',\n      header: Costanti.Ripiano,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'posizione',\n      header: Costanti.Posizione,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'eanCode',\n      header: Costanti.eanCode,\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.Modifica,\n      handler: this.modificaComp\n    }, {\n      name: Costanti.Elimina,\n      handler: this.confirmDeleteResult\n    }];\n    const items = [{\n      label: Costanti.AggCompMag,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiCompMag();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.ComposizioneMagazzino\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          actionsColumn: actionFields,\n          fileNames: \"ComposizioneMagazzino\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.AggCompMag,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideCompMag,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(AggiungiCommposizioneMagazzino, {\n          idWarehouse: this.state.selectedWarehouse\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeletePos, \" \", Costanti.Area, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.area\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 74\n            }, this), \" \", Costanti.Scaffale, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.scaffale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 127\n            }, this), \" \", Costanti.Ripiano, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.ripiano\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 183\n            }, this), \" \", Costanti.Posizione, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: this.state.result.posizione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 240\n            }, this), \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        style: {\n          width: '800px'\n        },\n        header: Costanti.Modifica,\n        modal: true,\n        className: \"p-fluid\",\n        footer: resultDialogFooter2,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3 d-flex justify-content-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"area\",\n                  children: Costanti.Area\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  inputId: \"area\",\n                  value: this.state.value1,\n                  onChange: e => this.setState({\n                    value1: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"scaffale\",\n                  children: Costanti.Scaffale\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"scaffale\",\n                  value: this.state.value2,\n                  onChange: e => this.setState({\n                    value2: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"ripiano\",\n                  children: Costanti.Ripiano\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"ripiano\",\n                  value: this.state.value3,\n                  onChange: e => this.setState({\n                    value3: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-xs-6 col-sm-6 col-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field col-12 md-col-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"posizione\",\n                  children: Costanti.Posizione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"posizione\",\n                  value: this.state.value4,\n                  onChange: e => this.setState({\n                    value4: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 mt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"p-float-label\",\n                children: [/*#__PURE__*/_jsxDEV(InputText, {\n                  id: \"eanCode\",\n                  value: this.state.value5,\n                  onChange: e => this.setState({\n                    value5: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"eanCode\",\n                  children: Costanti.eanCode\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 d-flex justify-content-end\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  className: \"p-button\",\n                  onClick: this.modifica,\n                  children: [Costanti.Conferma, /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-check ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter3,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"\\xE8 necessario selezionare un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default ComposizioneMagazzino;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "Dropdown", "AggiungiCommposizioneMagazzino", "<PERSON><PERSON>", "Dialog", "Nav", "CustomDataTable", "Caricamento", "InputText", "JoyrideGen", "jsxDEV", "_jsxDEV", "ComposizioneMagazzino", "constructor", "props", "emptyResult", "id", "area", "scaffale", "<PERSON><PERSON>", "posizione", "eanCode", "onWarehouseSelect", "e", "console", "log", "setState", "selectedWarehouse", "value", "url", "window", "sessionStorage", "setItem", "then", "res", "results", "data", "loading", "catch", "_e$response", "_e$response2", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "state", "results2", "result", "value1", "value2", "value3", "value4", "value5", "resultDialog", "resultDialog2", "resultDialog3", "deleteResultDialog", "displayed", "warehouse", "bind", "aggiungiCompMag", "hideCompMag", "modificaComp", "confirmDeleteResult", "hideDeleteResultDialog", "deleteResult", "hideDialog", "modifica", "closeSelectBefore", "componentDidMount", "entry", "push", "name", "warehouseName", "_e$response3", "_e$response4", "idWarehouse", "JSON", "parse", "getItem", "_e$response5", "_e$response6", "filter", "val", "location", "reload", "body", "composition", "setTimeout", "_e$response7", "_e$response8", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "resultDialogFooter3", "deleteResultDialogFooter", "label", "icon", "Si", "fields", "field", "header", "Area", "sortable", "showHeader", "<PERSON><PERSON><PERSON><PERSON>", "Ripiano", "Posizione", "actionFields", "Modifica", "handler", "Elimina", "items", "AggCompMag", "command", "ref", "el", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "splitButtonClass", "actionsColumn", "fileNames", "visible", "modal", "footer", "onHide", "Conferma", "fontSize", "ResDeletePos", "width", "htmlFor", "inputId", "target", "Primadiproseguire", "title", "content"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/composizioneMagazzino.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* OpJobsUscita - operazioni sulle lavorazioni in uscita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { AggiungiCommposizioneMagazzino } from \"../../aggiunta_dati/aggiungiCommposizioneMagazzino\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\n/* import { InputNumber } from \"primereact/inputnumber\"; */\nimport { InputText } from \"primereact/inputtext\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\n\nclass ComposizioneMagazzino extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        area: \"\",\n        scaffale: \"\",\n        ripiano: \"\",\n        posizione: \"\",\n        eanCode: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            result: this.emptyResult,\n            value1: null,\n            value2: null,\n            value3: null,\n            value4: null,\n            value5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            deleteResultDialog: false,\n            selectedWarehouse: null,\n            displayed: false,\n            loading: true\n        }\n\n        this.warehouse = []\n\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.aggiungiCompMag = this.aggiungiCompMag.bind(this);\n        this.hideCompMag = this.hideCompMag.bind(this);\n        this.modificaComp = this.modificaComp.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.modifica = this.modifica.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'warehousescomp?idWarehouse=' + idWarehouse;\n            this.setState({ selectedWarehouse: idWarehouse });\n            await APIRequest(\"GET\", url)\n                .then(res => {\n                    this.setState({\n                        results: res.data,\n                        loading: false\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                })\n        } else {\n            this.setState({ resultDialog3: true, displayed: true })\n        }\n    }\n    /* Seleziono il punto vendita e controllo che abbia un listino associato */\n    onWarehouseSelect = async (e) => {\n        console.log(e)\n        this.setState({ selectedWarehouse: e.value });\n        var url = 'warehousescomp?idWarehouse=' + e.value;\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        await APIRequest('GET', url)\n            .then(res => {\n                this.setState({\n                    results: res.data,\n                    loading: false\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    aggiungiCompMag() {\n        this.setState({\n            resultDialog: true\n        })\n    }\n    hideCompMag() {\n        this.setState({\n            resultDialog: false\n        })\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Chiusura dialogo eliminazione senza azioni\n    hideDeleteResultDialog() {\n        this.setState({ deleteResultDialog: false });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"warehousescomp/?idComp=\" + this.state.result.id;\n        var res = await APIRequest(\"DELETE\", url);\n        console.log(res.data);\n        window.location.reload();\n        this.toast.show({\n            severity: \"success\",\n            summary: \"Successful\",\n            detail: \"Posizione eliminata con successo\",\n            life: 3000,\n        });\n    }\n    modificaComp(result) {\n        this.setState({\n            result,\n            resultDialog2: true,\n            value1: result.area,\n            value2: result.scaffale,\n            value3: result.ripiano,\n            value4: result.posizione,\n            value5: result.eanCode\n        });\n    }\n    hideDialog() {\n        this.setState({\n            resultDialog2: false\n        });\n    }\n    async modifica() {\n        var body = {\n            composition: {\n                id: this.state.result.id,\n                area: this.state.value1,\n                scaffale: this.state.value2,\n                ripiano: this.state.value3,\n                posizione: this.state.value4,\n                eanCode: this.state.value5,\n            }\n        }\n        await APIRequest('PUT', 'warehousescomp', body)\n            .then(async res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Posizione modificata con successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile modificare la Posizione. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog3: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dell'aggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideCompMag}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideDialog} > {Costanti.Chiudi} </Button>\n            </React.Fragment>\n        );\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi di conferma o annullamento del dialogo di cancellazione\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            { field: 'area', header: Costanti.Area, sortable: true, showHeader: true },\n            { field: 'scaffale', header: Costanti.Scaffale, sortable: true, showHeader: true },\n            { field: 'ripiano', header: Costanti.Ripiano, sortable: true, showHeader: true },\n            { field: 'posizione', header: Costanti.Posizione, sortable: true, showHeader: true },\n            { field: 'eanCode', header: Costanti.eanCode, sortable: true, showHeader: true }\n        ];\n        const actionFields = [\n            { name: Costanti.Modifica, handler: this.modificaComp },\n            { name: Costanti.Elimina, handler: this.confirmDeleteResult }\n        ];\n        const items = [\n            {\n                label: Costanti.AggCompMag,\n                icon: 'pi pi-plus-circle',\n                command: () => {\n                    this.aggiungiCompMag()\n                }\n            },\n        ]\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.ComposizioneMagazzino}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        paginator\n                        rows={20}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        splitButtonClass={true}\n                        items={items}\n                        actionsColumn={actionFields}\n                        fileNames=\"ComposizioneMagazzino\"\n                    />\n                </div>\n                {/* Struttura dialogo per la composizione del magazzino */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={Costanti.AggCompMag}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hideCompMag}\n                >\n                    <Caricamento />\n                    <AggiungiCommposizioneMagazzino idWarehouse={this.state.selectedWarehouse} />\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeletePos} {Costanti.Area}: <b>{this.state.result.area}</b> {Costanti.Scaffale}: <b>{this.state.result.scaffale}</b> {Costanti.Ripiano}: <b>{this.state.result.ripiano}</b> {Costanti.Posizione}: <b>{this.state.result.posizione}</b>?\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog visible={this.state.resultDialog2} style={{ width: '800px' }} header={Costanti.Modifica} modal className=\"p-fluid\" footer={resultDialogFooter2} onHide={this.hideDialog}>\n                    <div className='my-3 d-flex justify-content-center'>\n                        <div className='row'>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"area\">{Costanti.Area}</label>\n                                    <InputText inputId='area' value={this.state.value1} onChange={(e) => this.setState({ value1: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"scaffale\">{Costanti.Scaffale}</label>\n                                    <InputText id='scaffale' value={this.state.value2} onChange={(e) => this.setState({ value2: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"ripiano\">{Costanti.Ripiano}</label>\n                                    <InputText id='ripiano' value={this.state.value3} onChange={(e) => this.setState({ value3: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 col-xs-6 col-sm-6 col-md-3'>\n                                <span className=\"field col-12 md-col-3\" >\n                                    <label htmlFor=\"posizione\">{Costanti.Posizione}</label>\n                                    <InputText id='posizione' value={this.state.value4} onChange={(e) => this.setState({ value4: e.target.value })} />\n                                </span>\n                            </div>\n                            <div className='col-12 mt-4'>\n                                <span className=\"p-float-label\" >\n                                    <InputText id='eanCode' value={this.state.value5} onChange={(e) => this.setState({ value5: e.target.value })} />\n                                    <label htmlFor=\"eanCode\">{Costanti.eanCode}</label>\n                                </span>\n                            </div>\n                            <div className='col-12 d-flex justify-content-end'>\n                                <div className='mt-4'>\n                                    <Button\n                                        className=\"p-button\"\n                                        onClick={this.modifica}\n                                    >\n                                        {Costanti.Conferma}\n                                        <i className='pi pi-check ml-2'></i>\n                                    </Button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog3} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter3}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='è necessario selezionare un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n            </div>\n        )\n    }\n}\n\nexport default ComposizioneMagazzino;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,8BAA8B,QAAQ,oDAAoD;AACnG,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AACjD;AACA,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,UAAU,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,qBAAqB,SAASf,SAAS,CAAC;EAU1CgB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAVhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE;IACb,CAAC;IA6ED;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7BC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACG,QAAQ,CAAC;QAAEC,iBAAiB,EAAEJ,CAAC,CAACK;MAAM,CAAC,CAAC;MAC7C,IAAIC,GAAG,GAAG,6BAA6B,GAAGN,CAAC,CAACK,KAAK;MACjDE,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAET,CAAC,CAACK,KAAK,CAAC;MACrD,MAAM5B,UAAU,CAAC,KAAK,EAAE6B,GAAG,CAAC,CACvBI,IAAI,CAACC,GAAG,IAAI;QACT,IAAI,CAACR,QAAQ,CAAC;UACVS,OAAO,EAAED,GAAG,CAACE,IAAI;UACjBC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CAACC,KAAK,CAAEf,CAAC,IAAK;QAAA,IAAAgB,WAAA,EAAAC,YAAA;QACZhB,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACkB,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAP,WAAA,GAAAhB,CAAC,CAACwB,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAYH,IAAI,MAAKY,SAAS,IAAAR,YAAA,GAAGjB,CAAC,CAACwB,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,GAAGb,CAAC,CAAC0B,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IA/FG,IAAI,CAACC,KAAK,GAAG;MACThB,OAAO,EAAE,IAAI;MACbiB,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAACtC,WAAW;MACxBuC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBnC,iBAAiB,EAAE,IAAI;MACvBoC,SAAS,EAAE,KAAK;MAChB1B,OAAO,EAAE;IACb,CAAC;IAED,IAAI,CAAC2B,SAAS,GAAG,EAAE;IAEnB,IAAI,CAAC1C,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC2C,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACD,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACI,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACK,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACL,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACM,YAAY,GAAG,IAAI,CAACA,YAAY,CAACN,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACO,UAAU,GAAG,IAAI,CAACA,UAAU,CAACP,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACT,IAAI,CAAC,IAAI,CAAC;EAC9D;EACA;EACA,MAAMU,iBAAiBA,CAAA,EAAG;IACtB,MAAM3E,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCiC,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAI0C,KAAK,IAAI1C,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAAC4B,SAAS,CAACa,IAAI,CAAC;UAChBC,IAAI,EAAEF,KAAK,CAACG,aAAa;UACzBnD,KAAK,EAAEgD,KAAK,CAAC5D;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDsB,KAAK,CAAEf,CAAC,IAAK;MAAA,IAAAyD,YAAA,EAAAC,YAAA;MACVzD,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACkB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAkC,YAAA,GAAAzD,CAAC,CAACwB,QAAQ,cAAAiC,YAAA,uBAAVA,YAAA,CAAY5C,IAAI,MAAKY,SAAS,IAAAiC,YAAA,GAAG1D,CAAC,CAACwB,QAAQ,cAAAkC,YAAA,uBAAVA,YAAA,CAAY7C,IAAI,GAAGb,CAAC,CAAC0B,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAIgC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACtD,MAAM,CAACC,cAAc,CAACsD,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAIrD,GAAG,GAAG,6BAA6B,GAAGqD,WAAW;MACrD,IAAI,CAACxD,QAAQ,CAAC;QAAEC,iBAAiB,EAAEuD;MAAY,CAAC,CAAC;MACjD,MAAMlF,UAAU,CAAC,KAAK,EAAE6B,GAAG,CAAC,CACvBI,IAAI,CAACC,GAAG,IAAI;QACT,IAAI,CAACR,QAAQ,CAAC;UACVS,OAAO,EAAED,GAAG,CAACE,IAAI;UACjBC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CAACC,KAAK,CAAEf,CAAC,IAAK;QAAA,IAAA+D,YAAA,EAAAC,YAAA;QACZ/D,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACkB,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAwC,YAAA,GAAA/D,CAAC,CAACwB,QAAQ,cAAAuC,YAAA,uBAAVA,YAAA,CAAYlD,IAAI,MAAKY,SAAS,IAAAuC,YAAA,GAAGhE,CAAC,CAACwB,QAAQ,cAAAwC,YAAA,uBAAVA,YAAA,CAAYnD,IAAI,GAAGb,CAAC,CAAC0B,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACxB,QAAQ,CAAC;QAAEmC,aAAa,EAAE,IAAI;QAAEE,SAAS,EAAE;MAAK,CAAC,CAAC;IAC3D;EACJ;EAuBAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACxC,QAAQ,CAAC;MACViC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAQ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzC,QAAQ,CAAC;MACViC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACAU,mBAAmBA,CAAChB,MAAM,EAAE;IACxB,IAAI,CAAC3B,QAAQ,CAAC;MACV2B,MAAM;MACNS,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACAQ,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC5C,QAAQ,CAAC;MAAEoC,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAChD;EACA;EACA,MAAMS,YAAYA,CAAA,EAAG;IACjB,IAAIpC,OAAO,GAAG,IAAI,CAACgB,KAAK,CAAChB,OAAO,CAACqD,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAACzE,EAAE,KAAK,IAAI,CAACmC,KAAK,CAACE,MAAM,CAACrC,EAC1C,CAAC;IACD,IAAI,CAACU,QAAQ,CAAC;MACVS,OAAO;MACP2B,kBAAkB,EAAE,KAAK;MACzBT,MAAM,EAAE,IAAI,CAACtC;IACjB,CAAC,CAAC;IACF,IAAIc,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACsB,KAAK,CAACE,MAAM,CAACrC,EAAE;IAC1D,IAAIkB,GAAG,GAAG,MAAMlC,UAAU,CAAC,QAAQ,EAAE6B,GAAG,CAAC;IACzCL,OAAO,CAACC,GAAG,CAACS,GAAG,CAACE,IAAI,CAAC;IACrBN,MAAM,CAAC4D,QAAQ,CAACC,MAAM,CAAC,CAAC;IACxB,IAAI,CAAClD,KAAK,CAACC,IAAI,CAAC;MACZC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,YAAY;MACrBC,MAAM,EAAE,kCAAkC;MAC1CK,IAAI,EAAE;IACV,CAAC,CAAC;EACN;EACAkB,YAAYA,CAACf,MAAM,EAAE;IACjB,IAAI,CAAC3B,QAAQ,CAAC;MACV2B,MAAM;MACNO,aAAa,EAAE,IAAI;MACnBN,MAAM,EAAED,MAAM,CAACpC,IAAI;MACnBsC,MAAM,EAAEF,MAAM,CAACnC,QAAQ;MACvBsC,MAAM,EAAEH,MAAM,CAAClC,OAAO;MACtBsC,MAAM,EAAEJ,MAAM,CAACjC,SAAS;MACxBsC,MAAM,EAAEL,MAAM,CAAChC;IACnB,CAAC,CAAC;EACN;EACAmD,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC9C,QAAQ,CAAC;MACVkC,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA,MAAMa,QAAQA,CAAA,EAAG;IACb,IAAImB,IAAI,GAAG;MACPC,WAAW,EAAE;QACT7E,EAAE,EAAE,IAAI,CAACmC,KAAK,CAACE,MAAM,CAACrC,EAAE;QACxBC,IAAI,EAAE,IAAI,CAACkC,KAAK,CAACG,MAAM;QACvBpC,QAAQ,EAAE,IAAI,CAACiC,KAAK,CAACI,MAAM;QAC3BpC,OAAO,EAAE,IAAI,CAACgC,KAAK,CAACK,MAAM;QAC1BpC,SAAS,EAAE,IAAI,CAAC+B,KAAK,CAACM,MAAM;QAC5BpC,OAAO,EAAE,IAAI,CAAC8B,KAAK,CAACO;MACxB;IACJ,CAAC;IACD,MAAM1D,UAAU,CAAC,KAAK,EAAE,gBAAgB,EAAE4F,IAAI,CAAC,CAC1C3D,IAAI,CAAC,MAAMC,GAAG,IAAI;MACfV,OAAO,CAACC,GAAG,CAACS,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,mCAAmC;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;MACpH4C,UAAU,CAAC,MAAM;QACbhE,MAAM,CAAC4D,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAACrD,KAAK,CAAEf,CAAC,IAAK;MAAA,IAAAwE,YAAA,EAAAC,YAAA;MACZxE,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACkB,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAiD,YAAA,GAAAxE,CAAC,CAACwB,QAAQ,cAAAgD,YAAA,uBAAVA,YAAA,CAAY3D,IAAI,MAAKY,SAAS,IAAAgD,YAAA,GAAGzE,CAAC,CAACwB,QAAQ,cAAAiD,YAAA,uBAAVA,YAAA,CAAY5D,IAAI,GAAGb,CAAC,CAAC0B,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC/N,CAAC,CAAC;EACV;EACAwB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACvB,KAAK,CAACxB,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVmC,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACpB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA+C,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBvF,OAAA,CAACf,KAAK,CAACuG,QAAQ;MAAAC,QAAA,eACXzF,OAAA,CAACR,MAAM;QAACkG,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACnC,WAAY;QAAAiC,QAAA,GACvD,GAAG,EACHrG,QAAQ,CAACwG,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMC,mBAAmB,gBACrBjG,OAAA,CAACf,KAAK,CAACuG,QAAQ;MAAAC,QAAA,eACXzF,OAAA,CAACR,MAAM;QAACkG,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC9B,UAAW;QAAA4B,QAAA,GAAE,GAAC,EAACrG,QAAQ,CAACwG,MAAM,EAAC,GAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACnB;IACD,MAAME,mBAAmB,gBACrBlG,OAAA,CAACf,KAAK,CAACuG,QAAQ;MAAAC,QAAA,eACXzF,OAAA;QAAK0F,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DzF,OAAA,CAACR,MAAM;UAACkG,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAAC5B,iBAAkB;UAAA0B,QAAA,GAAE,GAAC,EAACrG,QAAQ,CAACwG,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMG,wBAAwB,gBAC1BnG,OAAA,CAACf,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXzF,OAAA,CAACR,MAAM;QACH4G,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBX,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAAChC;MAAuB;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFhG,OAAA,CAACR,MAAM;QAACkG,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC/B,YAAa;QAAA6B,QAAA,GACxD,GAAG,EACHrG,QAAQ,CAACkH,EAAE,EAAE,GAAG;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMO,MAAM,GAAG,CACX;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAErH,QAAQ,CAACsH,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC1E;MAAEJ,KAAK,EAAE,UAAU;MAAEC,MAAM,EAAErH,QAAQ,CAACyH,QAAQ;MAAEF,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAClF;MAAEJ,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAErH,QAAQ,CAAC0H,OAAO;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAChF;MAAEJ,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAErH,QAAQ,CAAC2H,SAAS;MAAEJ,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACpF;MAAEJ,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAErH,QAAQ,CAACsB,OAAO;MAAEiG,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CACnF;IACD,MAAMI,YAAY,GAAG,CACjB;MAAE7C,IAAI,EAAE/E,QAAQ,CAAC6H,QAAQ;MAAEC,OAAO,EAAE,IAAI,CAACzD;IAAa,CAAC,EACvD;MAAEU,IAAI,EAAE/E,QAAQ,CAAC+H,OAAO;MAAED,OAAO,EAAE,IAAI,CAACxD;IAAoB,CAAC,CAChE;IACD,MAAM0D,KAAK,GAAG,CACV;MACIhB,KAAK,EAAEhH,QAAQ,CAACiI,UAAU;MAC1BhB,IAAI,EAAE,mBAAmB;MACzBiB,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAAC/D,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CACJ;IACD,oBACIvD,OAAA;MAAK0F,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CzF,OAAA,CAACb,KAAK;QAACoI,GAAG,EAAGC,EAAE,IAAK,IAAI,CAAC1F,KAAK,GAAG0F;MAAG;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvChG,OAAA,CAACN,GAAG;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPhG,OAAA;QAAK0F,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCzF,OAAA;UAAAyF,QAAA,EAAKrG,QAAQ,CAACa;QAAqB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EACL,IAAI,CAACxD,KAAK,CAACxB,iBAAiB,KAAK,IAAI,iBAClChB,OAAA;QAAK0F,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCzF,OAAA;UAAI0F,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtEzF,OAAA;YAAI0F,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjEzF,OAAA;cAAK0F,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DzF,OAAA;gBAAI0F,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACzF,OAAA;kBAAG0F,SAAS,EAAC,iBAAiB;kBAAC+B,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC5G,QAAQ,CAACsI,SAAS,EAAC,GAAC;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5HhG,OAAA,CAACV,QAAQ;gBAACoG,SAAS,EAAC,QAAQ;gBAACzE,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACxB,iBAAkB;gBAAC2G,OAAO,EAAE,IAAI,CAACtE,SAAU;gBAACuE,QAAQ,EAAE,IAAI,CAACjH,iBAAkB;gBAACkH,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACjD,MAAM;gBAACkD,QAAQ,EAAC;cAAM;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEVhG,OAAA;QAAK0F,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBzF,OAAA,CAACL,eAAe;UACZ4H,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACQ,EAAE,GAAGR,EAAG;UAC1BvG,KAAK,EAAE,IAAI,CAACuB,KAAK,CAAChB,OAAQ;UAC1B+E,MAAM,EAAEA,MAAO;UACf7E,OAAO,EAAE,IAAI,CAACc,KAAK,CAACd,OAAQ;UAC5BuG,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBlB,KAAK,EAAEA,KAAM;UACbmB,aAAa,EAAEvB,YAAa;UAC5BwB,SAAS,EAAC;QAAuB;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhG,OAAA,CAACP,MAAM;QACHgJ,OAAO,EAAE,IAAI,CAACjG,KAAK,CAACQ,YAAa;QACjCyD,MAAM,EAAErH,QAAQ,CAACiI,UAAW;QAC5BqB,KAAK;QACLhD,SAAS,EAAC,kBAAkB;QAC5BiD,MAAM,EAAEpD,kBAAmB;QAC3BqD,MAAM,EAAE,IAAI,CAACpF,WAAY;QAAAiC,QAAA,gBAEzBzF,OAAA,CAACJ,WAAW;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfhG,OAAA,CAACT,8BAA8B;UAACgF,WAAW,EAAE,IAAI,CAAC/B,KAAK,CAACxB;QAAkB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAEThG,OAAA,CAACP,MAAM;QACHgJ,OAAO,EAAE,IAAI,CAACjG,KAAK,CAACW,kBAAmB;QACvCsD,MAAM,EAAErH,QAAQ,CAACyJ,QAAS;QAC1BH,KAAK;QACLC,MAAM,EAAExC,wBAAyB;QACjCyC,MAAM,EAAE,IAAI,CAACjF,sBAAuB;QAAA8B,QAAA,eAEpCzF,OAAA;UAAK0F,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCzF,OAAA;YACI0F,SAAS,EAAC,mCAAmC;YAC7C+B,KAAK,EAAE;cAAEqB,QAAQ,EAAE;YAAO;UAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAACxD,KAAK,CAACE,MAAM,iBACd1C,OAAA;YAAAyF,QAAA,GACKrG,QAAQ,CAAC2J,YAAY,EAAC,GAAC,EAAC3J,QAAQ,CAACsH,IAAI,EAAC,IAAE,eAAA1G,OAAA;cAAAyF,QAAA,EAAI,IAAI,CAACjD,KAAK,CAACE,MAAM,CAACpC;YAAI;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAAC5G,QAAQ,CAACyH,QAAQ,EAAC,IAAE,eAAA7G,OAAA;cAAAyF,QAAA,EAAI,IAAI,CAACjD,KAAK,CAACE,MAAM,CAACnC;YAAQ;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAAC5G,QAAQ,CAAC0H,OAAO,EAAC,IAAE,eAAA9G,OAAA;cAAAyF,QAAA,EAAI,IAAI,CAACjD,KAAK,CAACE,MAAM,CAAClC;YAAO;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAAC5G,QAAQ,CAAC2H,SAAS,EAAC,IAAE,eAAA/G,OAAA;cAAAyF,QAAA,EAAI,IAAI,CAACjD,KAAK,CAACE,MAAM,CAACjC;YAAS;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KACvP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEThG,OAAA,CAACP,MAAM;QAACgJ,OAAO,EAAE,IAAI,CAACjG,KAAK,CAACS,aAAc;QAACwE,KAAK,EAAE;UAAEuB,KAAK,EAAE;QAAQ,CAAE;QAACvC,MAAM,EAAErH,QAAQ,CAAC6H,QAAS;QAACyB,KAAK;QAAChD,SAAS,EAAC,SAAS;QAACiD,MAAM,EAAE1C,mBAAoB;QAAC2C,MAAM,EAAE,IAAI,CAAC/E,UAAW;QAAA4B,QAAA,eAC5KzF,OAAA;UAAK0F,SAAS,EAAC,oCAAoC;UAAAD,QAAA,eAC/CzF,OAAA;YAAK0F,SAAS,EAAC,KAAK;YAAAD,QAAA,gBAChBzF,OAAA;cAAK0F,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CzF,OAAA;gBAAM0F,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCzF,OAAA;kBAAOiJ,OAAO,EAAC,MAAM;kBAAAxD,QAAA,EAAErG,QAAQ,CAACsH;gBAAI;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7ChG,OAAA,CAACH,SAAS;kBAACqJ,OAAO,EAAC,MAAM;kBAACjI,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACG,MAAO;kBAACiF,QAAQ,EAAGhH,CAAC,IAAK,IAAI,CAACG,QAAQ,CAAC;oBAAE4B,MAAM,EAAE/B,CAAC,CAACuI,MAAM,CAAClI;kBAAM,CAAC;gBAAE;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhG,OAAA;cAAK0F,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CzF,OAAA;gBAAM0F,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCzF,OAAA;kBAAOiJ,OAAO,EAAC,UAAU;kBAAAxD,QAAA,EAAErG,QAAQ,CAACyH;gBAAQ;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrDhG,OAAA,CAACH,SAAS;kBAACQ,EAAE,EAAC,UAAU;kBAACY,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACI,MAAO;kBAACgF,QAAQ,EAAGhH,CAAC,IAAK,IAAI,CAACG,QAAQ,CAAC;oBAAE6B,MAAM,EAAEhC,CAAC,CAACuI,MAAM,CAAClI;kBAAM,CAAC;gBAAE;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhG,OAAA;cAAK0F,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CzF,OAAA;gBAAM0F,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCzF,OAAA;kBAAOiJ,OAAO,EAAC,SAAS;kBAAAxD,QAAA,EAAErG,QAAQ,CAAC0H;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnDhG,OAAA,CAACH,SAAS;kBAACQ,EAAE,EAAC,SAAS;kBAACY,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACK,MAAO;kBAAC+E,QAAQ,EAAGhH,CAAC,IAAK,IAAI,CAACG,QAAQ,CAAC;oBAAE8B,MAAM,EAAEjC,CAAC,CAACuI,MAAM,CAAClI;kBAAM,CAAC;gBAAE;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhG,OAAA;cAAK0F,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CzF,OAAA;gBAAM0F,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCzF,OAAA;kBAAOiJ,OAAO,EAAC,WAAW;kBAAAxD,QAAA,EAAErG,QAAQ,CAAC2H;gBAAS;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDhG,OAAA,CAACH,SAAS;kBAACQ,EAAE,EAAC,WAAW;kBAACY,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACM,MAAO;kBAAC8E,QAAQ,EAAGhH,CAAC,IAAK,IAAI,CAACG,QAAQ,CAAC;oBAAE+B,MAAM,EAAElC,CAAC,CAACuI,MAAM,CAAClI;kBAAM,CAAC;gBAAE;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhG,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAAAD,QAAA,eACxBzF,OAAA;gBAAM0F,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC3BzF,OAAA,CAACH,SAAS;kBAACQ,EAAE,EAAC,SAAS;kBAACY,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACO,MAAO;kBAAC6E,QAAQ,EAAGhH,CAAC,IAAK,IAAI,CAACG,QAAQ,CAAC;oBAAEgC,MAAM,EAAEnC,CAAC,CAACuI,MAAM,CAAClI;kBAAM,CAAC;gBAAE;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChHhG,OAAA;kBAAOiJ,OAAO,EAAC,SAAS;kBAAAxD,QAAA,EAAErG,QAAQ,CAACsB;gBAAO;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhG,OAAA;cAAK0F,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAC9CzF,OAAA;gBAAK0F,SAAS,EAAC,MAAM;gBAAAD,QAAA,eACjBzF,OAAA,CAACR,MAAM;kBACHkG,SAAS,EAAC,UAAU;kBACpBC,OAAO,EAAE,IAAI,CAAC7B,QAAS;kBAAA2B,QAAA,GAEtBrG,QAAQ,CAACyJ,QAAQ,eAClB7I,OAAA;oBAAG0F,SAAS,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACThG,OAAA,CAACP,MAAM;QAACgJ,OAAO,EAAE,IAAI,CAACjG,KAAK,CAACU,aAAc;QAACuD,MAAM,EAAErH,QAAQ,CAACgK,iBAAkB;QAACV,KAAK;QAAChD,SAAS,EAAC,kBAAkB;QAACkD,MAAM,EAAE,IAAI,CAAC7E,iBAAkB;QAAC4E,MAAM,EAAEzC,mBAAoB;QAAAT,QAAA,GACzK,IAAI,CAACjD,KAAK,CAACY,SAAS,iBACjBpD,OAAA,CAACF,UAAU;UAACuJ,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,2CAAwC;UAACH,MAAM,EAAC;QAAS;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE/GhG,OAAA;UAAK0F,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DzF,OAAA;YAAI0F,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACzF,OAAA;cAAG0F,SAAS,EAAC,iBAAiB;cAAC+B,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5G,QAAQ,CAACsI,SAAS;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHhG,OAAA;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA,CAACV,QAAQ;YAACoG,SAAS,EAAC,QAAQ;YAACzE,KAAK,EAAE,IAAI,CAACuB,KAAK,CAACxB,iBAAkB;YAAC2G,OAAO,EAAE,IAAI,CAACtE,SAAU;YAACuE,QAAQ,EAAE,IAAI,CAACjH,iBAAkB;YAACkH,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACjD,MAAM;YAACkD,QAAQ,EAAC;UAAM;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAe/F,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}