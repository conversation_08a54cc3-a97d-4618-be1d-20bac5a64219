{"name": "@cypress/react18", "version": "0.0.0-development", "description": "Test React components using Cypress", "main": "dist/cypress-react.cjs.js", "scripts": {"build": "rimraf dist && rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "build-prod": "yarn build", "watch": "yarn build --watch --watch.exclude ./dist/**/*"}, "devDependencies": {"@cypress/react": "0.0.0-development", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.1.1", "@types/react": "^16", "@types/react-dom": "^16", "cypress": "0.0.0-development", "react": "^16", "react-dom": "^16", "rollup": "^2.38.5", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.7.4"}, "peerDependencies": {"@types/react": "^18", "@types/react-dom": "^18", "cypress": "*", "react": "^18", "react-dom": "^18"}, "files": ["dist"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/react18/#readme", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Freact18&template=1-bug-report.md&title=", "keywords": ["react", "cypress", "cypress-io", "test", "testing"], "module": "dist/cypress-react.esm-bundler.js", "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public"}}