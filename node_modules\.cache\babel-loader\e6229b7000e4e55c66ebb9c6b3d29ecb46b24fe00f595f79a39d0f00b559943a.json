{"ast": null, "code": "import Cascader from './Cascader';\nexport default Cascader;", "map": {"version": 3, "names": ["<PERSON>r"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-cascader/es/index.js"], "sourcesContent": ["import Cascader from './Cascader';\nexport default Cascader;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}