export class NetworkNode extends BaseNode {
    /**
     * @param {LH.Artifacts.NetworkRequest} networkRecord
     */
    constructor(networkRecord: LH.Artifacts.NetworkRequest);
    /** @private */
    private _record;
    get type(): "network";
    /**
     * @return {LH.Artifacts.NetworkRequest}
     */
    get record(): NetworkRequest;
    /**
     * @return {string}
     */
    get initiatorType(): string;
    /**
     * @return {boolean}
     */
    get fromDiskCache(): boolean;
    /**
     * @return {boolean}
     */
    get isNonNetworkProtocol(): boolean;
    /**
     * Returns whether this network record can be downloaded without a TCP connection.
     * During simulation we treat data coming in over a network connection separately from on-device data.
     * @return {boolean}
     */
    get isConnectionless(): boolean;
    /**
     * @return {boolean}
     */
    hasRenderBlockingPriority(): boolean;
    /**
     * @return {NetworkNode}
     */
    cloneWithoutRelationships(): NetworkNode;
}
import { BaseNode } from "./base-node.js";
import { NetworkRequest } from "../network-request.js";
import * as LH from "../../../types/lh.js";
//# sourceMappingURL=network-node.d.ts.map