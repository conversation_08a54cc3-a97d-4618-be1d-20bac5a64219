{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TailSpin = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar TailSpin = function TailSpin(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 38 38\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"defs\", null, /*#__PURE__*/_react[\"default\"].createElement(\"linearGradient\", {\n    x1: \"8.042%\",\n    y1: \"0%\",\n    x2: \"65.682%\",\n    y2: \"23.865%\",\n    id: \"a\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"stop\", {\n    stopColor: props.color,\n    stopOpacity: \"0\",\n    offset: \"0%\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"stop\", {\n    stopColor: props.color,\n    stopOpacity: \".631\",\n    offset: \"63.146%\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"stop\", {\n    stopColor: props.color,\n    offset: \"100%\"\n  }))), /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"translate(1 1)\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"path\", {\n    d: \"M36 18c0-9.94-8.06-18-18-18\",\n    id: \"Oval-2\",\n    stroke: props.color,\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    from: \"0 18 18\",\n    to: \"360 18 18\",\n    dur: \"0.9s\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: \"#fff\",\n    cx: \"36\",\n    cy: \"18\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    from: \"0 18 18\",\n    to: \"360 18 18\",\n    dur: \"0.9s\",\n    repeatCount: \"indefinite\"\n  })))));\n};\nexports.TailSpin = TailSpin;\nTailSpin.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nTailSpin.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  radius: 1,\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Tail<PERSON><PERSON>", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "width", "height", "viewBox", "xmlns", "label", "x1", "y1", "x2", "y2", "id", "stopColor", "color", "stopOpacity", "offset", "fill", "fillRule", "transform", "d", "stroke", "strokeWidth", "attributeName", "type", "from", "to", "dur", "repeatCount", "cx", "cy", "r", "radius", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/TailSpin.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TailSpin = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar TailSpin = function TailSpin(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 38 38\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"defs\", null, /*#__PURE__*/_react[\"default\"].createElement(\"linearGradient\", {\n    x1: \"8.042%\",\n    y1: \"0%\",\n    x2: \"65.682%\",\n    y2: \"23.865%\",\n    id: \"a\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"stop\", {\n    stopColor: props.color,\n    stopOpacity: \"0\",\n    offset: \"0%\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"stop\", {\n    stopColor: props.color,\n    stopOpacity: \".631\",\n    offset: \"63.146%\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"stop\", {\n    stopColor: props.color,\n    offset: \"100%\"\n  }))), /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"translate(1 1)\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"path\", {\n    d: \"M36 18c0-9.94-8.06-18-18-18\",\n    id: \"Oval-2\",\n    stroke: props.color,\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    from: \"0 18 18\",\n    to: \"360 18 18\",\n    dur: \"0.9s\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: \"#fff\",\n    cx: \"36\",\n    cy: \"18\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    from: \"0 18 18\",\n    to: \"360 18 18\",\n    dur: \"0.9s\",\n    repeatCount: \"indefinite\"\n  })))));\n};\n\nexports.TailSpin = TailSpin;\nTailSpin.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nTailSpin.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  radius: 1,\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AAEzB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,QAAQ,GAAG,SAASA,QAAQA,CAACO,KAAK,EAAE;EACtC,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnC,YAAY,EAAEL,KAAK,CAACM;EACtB,CAAC,EAAE,aAAaZ,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,gBAAgB,EAAE;IAC3HM,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACtDW,SAAS,EAAEZ,KAAK,CAACa,KAAK;IACtBC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAarB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACvDW,SAAS,EAAEZ,KAAK,CAACa,KAAK;IACtBC,WAAW,EAAE,MAAM;IACnBC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAarB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACvDW,SAAS,EAAEZ,KAAK,CAACa,KAAK;IACtBE,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,EAAE,aAAarB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACtDe,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAavB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDiB,SAAS,EAAE;EACb,CAAC,EAAE,aAAaxB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACtDkB,CAAC,EAAE,6BAA6B;IAChCR,EAAE,EAAE,QAAQ;IACZS,MAAM,EAAEpB,KAAK,CAACa,KAAK;IACnBQ,WAAW,EAAE;EACf,CAAC,EAAE,aAAa3B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClEqB,aAAa,EAAE,WAAW;IAC1BC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,GAAG,EAAE,MAAM;IACXC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAajC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1De,IAAI,EAAE,MAAM;IACZY,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAE9B,KAAK,CAAC+B;EACX,CAAC,EAAE,aAAarC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClEqB,aAAa,EAAE,WAAW;IAC1BC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,GAAG,EAAE,MAAM;IACXC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AAEDpC,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAC3BA,QAAQ,CAACuC,SAAS,GAAG;EACnB7B,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACoC,SAAS,CAAC,CAACpC,UAAU,CAAC,SAAS,CAAC,CAACqC,MAAM,EAAErC,UAAU,CAAC,SAAS,CAAC,CAACsC,MAAM,CAAC,CAAC;EACrGjC,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACoC,SAAS,CAAC,CAACpC,UAAU,CAAC,SAAS,CAAC,CAACqC,MAAM,EAAErC,UAAU,CAAC,SAAS,CAAC,CAACsC,MAAM,CAAC,CAAC;EACpGtB,KAAK,EAAEhB,UAAU,CAAC,SAAS,CAAC,CAACqC,MAAM;EACnC5B,KAAK,EAAET,UAAU,CAAC,SAAS,CAAC,CAACqC,MAAM;EACnCH,MAAM,EAAElC,UAAU,CAAC,SAAS,CAAC,CAACsC;AAChC,CAAC;AACD1C,QAAQ,CAAC2C,YAAY,GAAG;EACtBjC,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTW,KAAK,EAAE,OAAO;EACdkB,MAAM,EAAE,CAAC;EACTzB,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}