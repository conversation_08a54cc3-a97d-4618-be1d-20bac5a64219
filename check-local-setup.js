/**
 * Script per verificare la configurazione locale del frontend
 * e la connessione al backend locale
 */

const fs = require('fs');
const http = require('http');

console.log('🔍 Verifica Configurazione Locale Frontend\n');

// 1. Verifica configurazione .env
console.log('📋 1. Controllo configurazione .env');
try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const lines = envContent.split('\n');
    
    const apiUrl = lines.find(line => line.startsWith('REACT_APP_API_URL='));
    const wsUrl = lines.find(line => line.startsWith('REACT_APP_WS_URL='));
    const port = lines.find(line => line.startsWith('PORT='));
    const debug = lines.find(line => line.startsWith('REACT_APP_DEBUG='));
    
    console.log('✅ File .env trovato');
    console.log(`   ${apiUrl || 'REACT_APP_API_URL non trovato'}`);
    console.log(`   ${wsUrl || 'REACT_APP_WS_URL non trovato'}`);
    console.log(`   ${port || 'PORT non trovato'}`);
    console.log(`   ${debug || 'REACT_APP_DEBUG non trovato'}`);
    
    // Verifica che punti al backend locale
    if (apiUrl && apiUrl.includes('localhost:3001')) {
        console.log('✅ Frontend configurato per backend locale (porta 3001)');
    } else {
        console.log('⚠️ Frontend NON configurato per backend locale');
    }
    
} catch (error) {
    console.log('❌ Errore lettura file .env:', error.message);
}

// 2. Verifica se il frontend è in esecuzione
console.log('\n📋 2. Controllo frontend (porta 3000)');
checkPort(3000, 'Frontend React')
    .then(() => {
        // 3. Verifica se il backend è in esecuzione
        console.log('\n📋 3. Controllo backend (porta 3001)');
        return checkPort(3001, 'Backend API');
    })
    .then(() => {
        console.log('\n🎯 Riepilogo Configurazione:');
        console.log('✅ Frontend: http://localhost:3000');
        console.log('✅ Backend: http://localhost:3001');
        console.log('✅ Configurazione corretta per sviluppo locale');
        
        console.log('\n💡 Per testare la connessione:');
        console.log('1. Apri http://localhost:3000 nel browser');
        console.log('2. Apri Developer Tools (F12)');
        console.log('3. Controlla la console per i log di connessione');
        console.log('4. Il pulsante di test backend dovrebbe essere nascosto se tutto funziona');
    })
    .catch(error => {
        console.log('\n⚠️ Problemi rilevati:', error);
        console.log('\n🔧 Soluzioni:');
        console.log('- Frontend non in esecuzione: npm start');
        console.log('- Backend non in esecuzione: avvia il backend sulla porta 3001');
        console.log('- Verifica che non ci siano conflitti di porta');
    });

function checkPort(port, serviceName) {
    return new Promise((resolve, reject) => {
        const req = http.request({
            hostname: 'localhost',
            port: port,
            method: 'GET',
            timeout: 3000
        }, (res) => {
            console.log(`✅ ${serviceName} attivo su porta ${port}`);
            resolve();
        });
        
        req.on('error', (error) => {
            console.log(`❌ ${serviceName} NON attivo su porta ${port}`);
            reject(`${serviceName} non raggiungibile`);
        });
        
        req.on('timeout', () => {
            console.log(`⏱️ ${serviceName} timeout su porta ${port}`);
            req.destroy();
            reject(`${serviceName} timeout`);
        });
        
        req.end();
    });
}

// 4. Verifica package.json scripts
console.log('\n📋 4. Controllo script disponibili');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts;
    
    console.log('✅ Script disponibili:');
    console.log(`   start: ${scripts.start || 'non trovato'}`);
    console.log(`   build: ${scripts.build || 'non trovato'}`);
    console.log(`   test: ${scripts.test || 'non trovato'}`);
    
    if (scripts.start && scripts.start.includes('react-scripts start')) {
        console.log('✅ Script start configurato correttamente');
    }
    
} catch (error) {
    console.log('❌ Errore lettura package.json:', error.message);
}
