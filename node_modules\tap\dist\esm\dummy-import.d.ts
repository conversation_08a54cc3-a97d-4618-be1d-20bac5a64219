import '@tapjs/after';
import '@tapjs/after-each';
import '@tapjs/asserts';
import '@tapjs/before';
import '@tapjs/before-each';
import '@tapjs/chdir';
import '@tapjs/filter';
import '@tapjs/fixture';
import '@tapjs/intercept';
import '@tapjs/mock';
import '@tapjs/node-serialize';
import '@tapjs/snapshot';
import '@tapjs/spawn';
import '@tapjs/stdin';
import '@tapjs/typescript';
import '@tapjs/worker';
//# sourceMappingURL=dummy-import.d.ts.map