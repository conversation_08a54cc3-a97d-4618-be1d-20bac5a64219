{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\gestioneDocumenti.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneDocumentiAFF - operazioni sui documenti degli affiliati\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Sidebar } from 'primereact/sidebar';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneDocumentiAFF extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    this.state = {\n      results: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      loading: true,\n      displayed: false,\n      resultDialog3: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      totalRecords: 0,\n      search: '',\n      value: null,\n      value2: null,\n      idAffiliate: 0,\n      clienti: null,\n      param: '?idAffiliate=',\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name\n      });\n      var param = '?idRetailer=';\n      var url = 'documents' + param + e.value.code + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            idDocumentHeadOrig: element.idDocumentHeadOrig,\n            tasks: element.tasks\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          param: param,\n          idAffiliate: e.value.code,\n          selectedRetailer: e.value,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.retailers = [];\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"retailers/\").then(res => {\n      res.data.forEach(element => {\n        var x = {\n          name: element.idRegistry.firstName,\n          code: element.id\n        };\n        this.retailers.push(x);\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    var idAffiliate = JSON.parse(window.localStorage.getItem(\"userid\")).affiliate.id;\n    this.setState({\n      idAffiliate: idAffiliate\n    });\n    var url = 'documents?idAffiliate=' + idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          retailer: element.idRetailer.idRegistry.firstName,\n          documentDate: element.documentDate,\n          idDocumentHeadOrig: element.idDocumentHeadOrig,\n          tasks: element.tasks\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idAffiliate = JSON.parse(window.localStorage.getItem(\"userid\")).affiliate.id;\n    this.setState({\n      loading: true,\n      search: '',\n      idAffiliate: idAffiliate,\n      selectedRetailer: null,\n      param: '?idAffiliate='\n    });\n    var url = 'documents?idAffiliate=' + idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          retailer: element.idRetailer.idRegistry.firstName,\n          documentDate: element.documentDate,\n          idDocumentHeadOrig: element.idDocumentHeadOrig,\n          tasks: element.tasks\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idAffiliate = JSON.parse(window.localStorage.getItem(\"userid\")).affiliate.id;\n    this.setState({\n      loading: true,\n      search: '',\n      idAffiliate: idAffiliate,\n      selectedRetailer: null,\n      param: '?idAffiliate='\n    });\n    var url = 'documents?idAffiliate=' + idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          retailer: element.idRetailer.idRegistry.firstName,\n          documentDate: element.documentDate,\n          idDocumentHeadOrig: element.idDocumentHeadOrig,\n          tasks: element.tasks\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            idDocumentHeadOrig: element.idDocumentHeadOrig,\n            tasks: element.tasks\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response1, _e$response10;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            idDocumentHeadOrig: element.idDocumentHeadOrig,\n            tasks: element.tasks\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  openFilter() {\n    this.setState({\n      resultDialog: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 17\n      }, this), this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n        title: \"Prima di procedere\",\n        content: \"Seleziona un magazzino \",\n        target: \".selWar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.GestioneDocumenti\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          actionsColumn: actionFields,\n          showExportCsvButton: true,\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          fileNames: \"Vendite\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          documento: this.state.result,\n          result: this.state.results3,\n          results: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedRetailer,\n          options: this.retailers,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona cliente\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default GestioneDocumentiAFF;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "Dialog", "JoyrideGen", "Toast", "<PERSON><PERSON>", "Print", "Dropdown", "Sidebar", "VisualizzaDocumenti", "Nav", "CustomDataTable", "jsxDEV", "_jsxDEV", "GestioneDocumentiAFF", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "state", "results", "results3", "results4", "results5", "resultDialog", "resultDialog2", "loading", "displayed", "resultDialog3", "opMag", "respMag", "mex", "result", "totalRecords", "search", "value", "value2", "idAffiliate", "clienti", "param", "lazyParams", "first", "rows", "page", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "e", "setState", "name", "url", "code", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "documentDate", "idDocumentHeadOrig", "tasks", "push", "totalCount", "<PERSON><PERSON><PERSON><PERSON>", "pageCount", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "retailers", "loadLazyTimeout", "warehouse", "visualizzaDett", "bind", "hidevisualizzaDett", "reset", "resetDesc", "onPage", "onSort", "onFilter", "openFilter", "closeFilter", "componentDidMount", "JSON", "parse", "window", "localStorage", "getItem", "affiliate", "_e$response3", "_e$response4", "documentBody", "task", "documentBodies", "_e$response5", "_e$response6", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "event", "clearTimeout", "setTimeout", "_e$response1", "_e$response10", "Math", "random", "field", "_objectSpread", "_e$response11", "_e$response12", "loadLazyData", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "doc", "fields", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "icon", "handler", "filterDnone", "ref", "el", "title", "content", "target", "GestioneDocumenti", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "autoLayout", "actionsColumn", "showExportCsvButton", "classInputSearch", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "position", "style", "<PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filter", "filterBy"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/gestioneDocumenti.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneDocumentiAFF - operazioni sui documenti degli affiliati\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Sidebar } from 'primereact/sidebar';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\n\nclass GestioneDocumentiAFF extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            loading: true,\n            displayed: false,\n            resultDialog3: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            totalRecords: 0,\n            search: '',\n            value: null,\n            value2: null,\n            idAffiliate: 0,\n            clienti: null,\n            param: '?idAffiliate=',\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name\n            });\n\n            var param = '?idRetailer='\n\n            var url = 'documents' + param + e.value.code + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig,\n                            tasks: element.tasks\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        param: param,\n                        idAffiliate: e.value.code,\n                        selectedRetailer: e.value,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.retailers = []\n        this.loadLazyTimeout = null;\n        this.warehouse = [];\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"retailers/\")\n            .then((res) => {\n                res.data.forEach(element => {\n                    var x = {\n                        name: element.idRegistry.firstName,\n                        code: element.id\n                    }\n                    this.retailers.push(x)\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n        var idAffiliate = JSON.parse(window.localStorage.getItem(\"userid\")).affiliate.id\n        this.setState({\n            idAffiliate: idAffiliate\n        })\n        var url = 'documents?idAffiliate=' + idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        idDocumentHeadOrig: element.idDocumentHeadOrig,\n                        tasks: element.tasks\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idAffiliate = JSON.parse(window.localStorage.getItem(\"userid\")).affiliate.id\n        this.setState({\n            loading: true,\n            search: '',\n            idAffiliate: idAffiliate,\n            selectedRetailer: null,\n            param: '?idAffiliate='\n        })\n        var url = 'documents?idAffiliate=' + idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        idDocumentHeadOrig: element.idDocumentHeadOrig,\n                        tasks: element.tasks\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idAffiliate = JSON.parse(window.localStorage.getItem(\"userid\")).affiliate.id\n        this.setState({\n            loading: true,\n            search: '',\n            idAffiliate: idAffiliate,\n            selectedRetailer: null,\n            param: '?idAffiliate='\n        })\n        var url = 'documents?idAffiliate=' + idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        idDocumentHeadOrig: element.idDocumentHeadOrig,\n                        tasks: element.tasks\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig,\n                            tasks: element.tasks\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.idAffiliate + '&documentType=CLI-ORDINE,CLI-DDT&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig,\n                            tasks: element.tasks\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    openFilter() {\n        this.setState({\n            resultDialog: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog: false\n        })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"retailer\",\n                header: Costanti.cliente,\n                body: \"retailer\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.status\",\n                header: Costanti.StatoTask,\n                body: \"assigned\",\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n        ];\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n                {this.state.displayed &&\n                    <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                }\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.GestioneDocumenti}</h1>\n                </div>\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        actionsColumn={actionFields}\n                        showExportCsvButton={true}\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        fileNames=\"Vendite\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        documento={this.state.result}\n                        result={this.state.results3}\n                        results={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedRetailer} options={this.retailers} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default GestioneDocumentiAFF;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,oBAAoB,SAASf,SAAS,CAAC;EAazCgB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAGG,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,IAAI,CAACvB,WAAW;MACxBwB,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,eAAe;MACtBC,UAAU,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAEX,KAAK,EAAE,EAAE;YAAEY,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAEZ,KAAK,EAAE,EAAE;YAAEY,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAEZ,KAAK,EAAE,EAAE;YAAEY,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAMC,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACVxB,OAAO,EAAE,IAAI;QACbQ,MAAM,EAAEe,CAAC,CAACd,KAAK,CAACgB;MACpB,CAAC,CAAC;MAEF,IAAIZ,KAAK,GAAG,cAAc;MAE1B,IAAIa,GAAG,GAAG,WAAW,GAAGb,KAAK,GAAGU,CAAC,CAACd,KAAK,CAACkB,IAAI,GAAG,wCAAwC,GAAG,IAAI,CAAClC,KAAK,CAACqB,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACG,IAAI;MAC5J,MAAMlD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJnD,EAAE,EAAEkD,OAAO,CAAClD,EAAE;YACdoD,MAAM,EAAEF,OAAO,CAACE,MAAM;YACtBC,IAAI,EAAEH,OAAO,CAACG,IAAI;YAClBC,QAAQ,EAAEJ,OAAO,CAACK,UAAU,CAACC,UAAU,CAACvD,SAAS;YACjDwD,YAAY,EAAEP,OAAO,CAACO,YAAY;YAClCC,kBAAkB,EAAER,OAAO,CAACQ,kBAAkB;YAC9CC,KAAK,EAAET,OAAO,CAACS;UACnB,CAAC;UACDb,SAAS,CAACc,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACX,QAAQ,CAAC;UACV9B,OAAO,EAAEoC,SAAS;UAClBjC,QAAQ,EAAEiC,SAAS;UACnBvB,YAAY,EAAEsB,GAAG,CAACE,IAAI,CAACc,UAAU;UACjChC,KAAK,EAAEA,KAAK;UACZF,WAAW,EAAEY,CAAC,CAACd,KAAK,CAACkB,IAAI;UACzBmB,gBAAgB,EAAEvB,CAAC,CAACd,KAAK;UACzBK,UAAU,EAAE;YAAEC,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACqB,UAAU,CAACC,KAAK;YAAEC,IAAI,EAAE,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACE,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACxB,KAAK,CAACqB,UAAU,CAACG,IAAI;YAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACc,UAAU,GAAG,IAAI,CAACpD,KAAK,CAACqB,UAAU,CAACE;UAAM,CAAC;UACpLhB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAA0B,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAA1B,CAAC,CAACoC,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYlB,IAAI,MAAK6B,SAAS,IAAAV,YAAA,GAAG3B,CAAC,CAACoC,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYnB,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACE,KAAK,GAAG,IAAI,CAACA,KAAK,CAACF,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACG,SAAS,GAAG,IAAI,CAACA,SAAS,CAACH,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACK,MAAM,GAAG,IAAI,CAACA,MAAM,CAACL,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACM,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACN,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACO,UAAU,GAAG,IAAI,CAACA,UAAU,CAACP,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACQ,WAAW,GAAG,IAAI,CAACA,WAAW,CAACR,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMS,iBAAiBA,CAAA,EAAG;IACtB,MAAM7G,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC6D,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIC,CAAC,GAAG;UACJV,IAAI,EAAES,OAAO,CAACM,UAAU,CAACvD,SAAS;UAClC0C,IAAI,EAAEO,OAAO,CAAClD;QAClB,CAAC;QACD,IAAI,CAAC+E,SAAS,CAACnB,IAAI,CAACT,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN,CAAC,CAAC,CAACa,KAAK,CAAEzB,CAAC,IAAK;MACZ4B,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAIZ,WAAW,GAAGkE,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAClG,EAAE;IAChF,IAAI,CAACwC,QAAQ,CAAC;MACVb,WAAW,EAAEA;IACjB,CAAC,CAAC;IACF,IAAIe,GAAG,GAAG,wBAAwB,GAAGf,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAAClB,KAAK,CAACqB,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACG,IAAI;IAChK,MAAMlD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIC,CAAC,GAAG;UACJnD,EAAE,EAAEkD,OAAO,CAAClD,EAAE;UACdoD,MAAM,EAAEF,OAAO,CAACE,MAAM;UACtBC,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClBC,QAAQ,EAAEJ,OAAO,CAACK,UAAU,CAACC,UAAU,CAACvD,SAAS;UACjDwD,YAAY,EAAEP,OAAO,CAACO,YAAY;UAClCC,kBAAkB,EAAER,OAAO,CAACQ,kBAAkB;UAC9CC,KAAK,EAAET,OAAO,CAACS;QACnB,CAAC;QACDb,SAAS,CAACc,IAAI,CAACT,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACX,QAAQ,CAAC;QACV9B,OAAO,EAAEoC,SAAS;QAClBjC,QAAQ,EAAEiC,SAAS;QACnBvB,YAAY,EAAEsB,GAAG,CAACE,IAAI,CAACc,UAAU;QACjC/B,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACqB,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAACxB,KAAK,CAACqB,UAAU,CAACG,IAAI;UAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACc,UAAU,GAAG,IAAI,CAACpD,KAAK,CAACqB,UAAU,CAACE;QAAM,CAAC;QACpLhB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDgD,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAA4D,YAAA,EAAAC,YAAA;MACVjC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyB,YAAA,GAAA5D,CAAC,CAACoC,QAAQ,cAAAwB,YAAA,uBAAVA,YAAA,CAAYpD,IAAI,MAAK6B,SAAS,IAAAwB,YAAA,GAAG7D,CAAC,CAACoC,QAAQ,cAAAyB,YAAA,uBAAVA,YAAA,CAAYrD,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACA,MAAMI,cAAcA,CAAC5D,MAAM,EAAE;IACzB,IAAIoB,GAAG,GAAG,2BAA2B,GAAGpB,MAAM,CAACtB,EAAE;IACjD,IAAIqG,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMvH,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACXwD,YAAY,GAAGxD,GAAG,CAACE,IAAI,CAACwD,cAAc;MACtCjF,MAAM,CAACiF,cAAc,GAAG1D,GAAG,CAACE,IAAI,CAACwD,cAAc;MAC/CD,IAAI,GAAGzD,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDiB,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAAiE,YAAA,EAAAC,YAAA;MACVtC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8B,YAAA,GAAAjE,CAAC,CAACoC,QAAQ,cAAA6B,YAAA,uBAAVA,YAAA,CAAYzD,IAAI,MAAK6B,SAAS,IAAA6B,YAAA,GAAGlE,CAAC,CAACoC,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAY1D,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBvD,MAAM,CAAC8B,MAAM,GACb,OAAO,GACP,IAAIsD,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAC1F,MAAM,CAACmC,YAAY,CAAC,CAAC;IAC5C,IAAI,CAACjB,QAAQ,CAAC;MACVzB,aAAa,EAAE,IAAI;MACnBO,MAAM,EAAEgF,IAAI;MACZ3F,QAAQ,EAAE0F,YAAY;MACtBhF,GAAG,EAAEwD;IACT,CAAC,CAAC;EACN;EACA;EACAO,kBAAkBA,CAAC9D,MAAM,EAAE;IACvB,IAAI,CAACkB,QAAQ,CAAC;MACVlB,MAAM,EAAEA,MAAM;MACdP,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA,MAAMsE,KAAKA,CAAA,EAAG;IACV,IAAI1D,WAAW,GAAGkE,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAClG,EAAE;IAChF,IAAI,CAACwC,QAAQ,CAAC;MACVxB,OAAO,EAAE,IAAI;MACbQ,MAAM,EAAE,EAAE;MACVG,WAAW,EAAEA,WAAW;MACxBmC,gBAAgB,EAAE,IAAI;MACtBjC,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAIa,GAAG,GAAG,wBAAwB,GAAGf,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAAClB,KAAK,CAACqB,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACG,IAAI;IAChK,MAAMlD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIC,CAAC,GAAG;UACJnD,EAAE,EAAEkD,OAAO,CAAClD,EAAE;UACdoD,MAAM,EAAEF,OAAO,CAACE,MAAM;UACtBC,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClBC,QAAQ,EAAEJ,OAAO,CAACK,UAAU,CAACC,UAAU,CAACvD,SAAS;UACjDwD,YAAY,EAAEP,OAAO,CAACO,YAAY;UAClCC,kBAAkB,EAAER,OAAO,CAACQ,kBAAkB;UAC9CC,KAAK,EAAET,OAAO,CAACS;QACnB,CAAC;QACDb,SAAS,CAACc,IAAI,CAACT,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACX,QAAQ,CAAC;QACV9B,OAAO,EAAEoC,SAAS;QAClBjC,QAAQ,EAAEiC,SAAS;QACnBvB,YAAY,EAAEsB,GAAG,CAACE,IAAI,CAACc,UAAU;QACjC/B,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACqB,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAACxB,KAAK,CAACqB,UAAU,CAACG,IAAI;UAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACc,UAAU,GAAG,IAAI,CAACpD,KAAK,CAACqB,UAAU,CAACE;QAAM,CAAC;QACpLhB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDgD,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAA0E,YAAA,EAAAC,YAAA;MACV/C,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAuC,YAAA,GAAA1E,CAAC,CAACoC,QAAQ,cAAAsC,YAAA,uBAAVA,YAAA,CAAYlE,IAAI,MAAK6B,SAAS,IAAAsC,YAAA,GAAG3E,CAAC,CAACoC,QAAQ,cAAAuC,YAAA,uBAAVA,YAAA,CAAYnE,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACA,MAAMQ,SAASA,CAAA,EAAG;IACd,IAAI3D,WAAW,GAAGkE,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAClG,EAAE;IAChF,IAAI,CAACwC,QAAQ,CAAC;MACVxB,OAAO,EAAE,IAAI;MACbQ,MAAM,EAAE,EAAE;MACVG,WAAW,EAAEA,WAAW;MACxBmC,gBAAgB,EAAE,IAAI;MACtBjC,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAIa,GAAG,GAAG,wBAAwB,GAAGf,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAAClB,KAAK,CAACqB,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACG,IAAI;IAChK,MAAMlD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIC,CAAC,GAAG;UACJnD,EAAE,EAAEkD,OAAO,CAAClD,EAAE;UACdoD,MAAM,EAAEF,OAAO,CAACE,MAAM;UACtBC,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClBC,QAAQ,EAAEJ,OAAO,CAACK,UAAU,CAACC,UAAU,CAACvD,SAAS;UACjDwD,YAAY,EAAEP,OAAO,CAACO,YAAY;UAClCC,kBAAkB,EAAER,OAAO,CAACQ,kBAAkB;UAC9CC,KAAK,EAAET,OAAO,CAACS;QACnB,CAAC;QACDb,SAAS,CAACc,IAAI,CAACT,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACX,QAAQ,CAAC;QACV9B,OAAO,EAAEoC,SAAS;QAClBjC,QAAQ,EAAEiC,SAAS;QACnBvB,YAAY,EAAEsB,GAAG,CAACE,IAAI,CAACc,UAAU;QACjC/B,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACqB,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAACxB,KAAK,CAACqB,UAAU,CAACG,IAAI;UAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACc,UAAU,GAAG,IAAI,CAACpD,KAAK,CAACqB,UAAU,CAACE;QAAM,CAAC;QACpLhB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDgD,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAA4E,YAAA,EAAAC,YAAA;MACVjD,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyC,YAAA,GAAA5E,CAAC,CAACoC,QAAQ,cAAAwC,YAAA,uBAAVA,YAAA,CAAYpE,IAAI,MAAK6B,SAAS,IAAAwC,YAAA,GAAG7E,CAAC,CAACoC,QAAQ,cAAAyC,YAAA,uBAAVA,YAAA,CAAYrE,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAS,MAAMA,CAAC8B,KAAK,EAAE;IACV,IAAI,CAAC7E,QAAQ,CAAC;MAAExB,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAACgE,eAAe,EAAE;MACtBsC,YAAY,CAAC,IAAI,CAACtC,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGuC,UAAU,CAAC,YAAY;MAC1C,IAAI7E,GAAG,GAAG,WAAW,GAAG,IAAI,CAACjC,KAAK,CAACoB,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACkB,WAAW,GAAG,wCAAwC,GAAG0F,KAAK,CAACrF,IAAI,GAAG,QAAQ,GAAGqF,KAAK,CAACpF,IAAI;MACjJ,MAAMlD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJnD,EAAE,EAAEkD,OAAO,CAAClD,EAAE;YACdoD,MAAM,EAAEF,OAAO,CAACE,MAAM;YACtBC,IAAI,EAAEH,OAAO,CAACG,IAAI;YAClBC,QAAQ,EAAEJ,OAAO,CAACK,UAAU,CAACC,UAAU,CAACvD,SAAS;YACjDwD,YAAY,EAAEP,OAAO,CAACO,YAAY;YAClCC,kBAAkB,EAAER,OAAO,CAACQ,kBAAkB;YAC9CC,KAAK,EAAET,OAAO,CAACS;UACnB,CAAC;UACDb,SAAS,CAACc,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACX,QAAQ,CAAC;UACV9B,OAAO,EAAEoC,SAAS;UAClBjC,QAAQ,EAAEiC,SAAS;UACnBvB,YAAY,EAAEsB,GAAG,CAACE,IAAI,CAACc,UAAU;UACjC/B,UAAU,EAAEuF,KAAK;UACjBrG,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAAiF,YAAA,EAAAC,aAAA;QACVtD,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8C,YAAA,GAAAjF,CAAC,CAACoC,QAAQ,cAAA6C,YAAA,uBAAVA,YAAA,CAAYzE,IAAI,MAAK6B,SAAS,IAAA6C,aAAA,GAAGlF,CAAC,CAACoC,QAAQ,cAAA8C,aAAA,uBAAVA,aAAA,CAAY1E,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE4C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAnC,MAAMA,CAAC6B,KAAK,EAAE;IACV,IAAI,CAAC7E,QAAQ,CAAC;MAAExB,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI4G,KAAK,GAAGP,KAAK,CAACnF,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGmF,KAAK,CAACnF,SAAS;IAChG,IAAI,IAAI,CAAC8C,eAAe,EAAE;MACtBsC,YAAY,CAAC,IAAI,CAACtC,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGuC,UAAU,CAAC,YAAY;MAC1C,IAAI7E,GAAG,GAAG,WAAW,GAAG,IAAI,CAACjC,KAAK,CAACoB,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACkB,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAAClB,KAAK,CAACqB,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACG,IAAI,GAAG,SAAS,GAAG2F,KAAK,GAAG,WAAW,IAAIP,KAAK,CAAClF,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MAC9P,MAAMpD,UAAU,CAAC,KAAK,EAAE2D,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJnD,EAAE,EAAEkD,OAAO,CAAClD,EAAE;YACdoD,MAAM,EAAEF,OAAO,CAACE,MAAM;YACtBC,IAAI,EAAEH,OAAO,CAACG,IAAI;YAClBC,QAAQ,EAAEJ,OAAO,CAACK,UAAU,CAACC,UAAU,CAACvD,SAAS;YACjDwD,YAAY,EAAEP,OAAO,CAACO,YAAY;YAClCC,kBAAkB,EAAER,OAAO,CAACQ,kBAAkB;YAC9CC,KAAK,EAAET,OAAO,CAACS;UACnB,CAAC;UACDb,SAAS,CAACc,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACX,QAAQ,CAAC;UACV9B,OAAO,EAAEoC,SAAS;UAClBjC,QAAQ,EAAEiC,SAAS;UACnBvB,YAAY,EAAEsB,GAAG,CAACE,IAAI,CAACc,UAAU;UACjC/B,UAAU,EAAA+F,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACpH,KAAK,CAACqB,UAAU;YAAEI,SAAS,EAAEmF,KAAK,CAACnF,SAAS;YAAEC,SAAS,EAAEkF,KAAK,CAAClF;UAAS,EAAE;UAChGnB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAAuF,aAAA,EAAAC,aAAA;QACV5D,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAoD,aAAA,GAAAvF,CAAC,CAACoC,QAAQ,cAAAmD,aAAA,uBAAVA,aAAA,CAAY/E,IAAI,MAAK6B,SAAS,IAAAmD,aAAA,GAAGxF,CAAC,CAACoC,QAAQ,cAAAoD,aAAA,uBAAVA,aAAA,CAAYhF,IAAI,GAAGR,CAAC,CAACsC,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE4C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EAEAlC,QAAQA,CAAC4B,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC7E,QAAQ,CAAC;MAAEV,UAAU,EAAEuF;IAAM,CAAC,EAAE,IAAI,CAACW,YAAY,CAAC;EAC3D;EACAtC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAClD,QAAQ,CAAC;MACV1B,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA6E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnD,QAAQ,CAAC;MACV1B,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAmH,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBvI,OAAA,CAACf,KAAK,CAACuJ,QAAQ;MAAAC,QAAA,eACXzI,OAAA;QAAK0I,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBzI,OAAA;UAAK0I,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBzI,OAAA;YAAK0I,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvCzI,OAAA,CAACR,MAAM;cACHkJ,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAClD,kBAAmB;cAAAgD,QAAA,GAEhC,GAAG,EACHtJ,QAAQ,CAACyJ,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACThJ,OAAA,CAACP,KAAK;cACF0D,SAAS,EAAE,IAAI,CAACrC,KAAK,CAACa,MAAO;cAC7BX,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE,QAAS;cAC9BU,GAAG,EAAE,IAAI,CAACZ,KAAK,CAACY,GAAI;cACpBuH,GAAG,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAME,MAAM,GAAG,CACX;MACIjB,KAAK,EAAE,QAAQ;MACfkB,MAAM,EAAEhK,QAAQ,CAACiK,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACItB,KAAK,EAAE,MAAM;MACbkB,MAAM,EAAEhK,QAAQ,CAACuE,IAAI;MACrB4F,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACItB,KAAK,EAAE,UAAU;MACjBkB,MAAM,EAAEhK,QAAQ,CAACqK,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACItB,KAAK,EAAE,cAAc;MACrBkB,MAAM,EAAEhK,QAAQ,CAACsK,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACItB,KAAK,EAAE,cAAc;MACrBkB,MAAM,EAAEhK,QAAQ,CAACuK,SAAS;MAC1BL,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMI,YAAY,GAAG,CACjB;MAAE7G,IAAI,EAAE3D,QAAQ,CAACyK,OAAO;MAAEC,IAAI,eAAE7J,OAAA;QAAG0I,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEc,OAAO,EAAE,IAAI,CAACvE;IAAe,CAAC,CAC9F;IACD,IAAIwE,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACjJ,KAAK,CAACe,MAAM,KAAK,EAAE,IAAI,IAAI,CAACf,KAAK,CAACgB,KAAK,KAAK,IAAI,IAAI,IAAI,CAAChB,KAAK,CAACiB,MAAM,KAAK,IAAI,EAAE;MACrFgI,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACI/J,OAAA;MAAK0I,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CzI,OAAA,CAACT,KAAK;QAACyK,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACvF,KAAK,GAAGuF;MAAG;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvChJ,OAAA,CAACH,GAAG;QAAAgJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACN,IAAI,CAAClI,KAAK,CAACQ,SAAS,iBACjBtB,OAAA,CAACV,UAAU;QAAC4K,KAAK,EAAC,oBAAoB;QAACC,OAAO,EAAC,yBAAyB;QAACC,MAAM,EAAC;MAAS;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEhGhJ,OAAA;QAAK0I,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCzI,OAAA;UAAAyI,QAAA,EAAKtJ,QAAQ,CAACkL;QAAiB;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACNhJ,OAAA;QAAK0I,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBzI,OAAA,CAACF,eAAe;UACZkK,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACK,EAAE,GAAGL,EAAG;UAC1BnI,KAAK,EAAE,IAAI,CAAChB,KAAK,CAACC,OAAQ;UAC1BM,OAAO,EAAE,IAAI,CAACP,KAAK,CAACO,OAAQ;UAC5B6H,MAAM,EAAEA,MAAO;UACfqB,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACT9E,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBxD,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACqB,UAAU,CAACC,KAAM;UACnCR,YAAY,EAAE,IAAI,CAACd,KAAK,CAACc,YAAa;UACtCS,IAAI,EAAE,IAAI,CAACvB,KAAK,CAACqB,UAAU,CAACE,IAAK;UACjCsI,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,aAAa,EAAElB,YAAa;UAC5BmB,mBAAmB,EAAE,IAAK;UAC1BjF,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBtD,SAAS,EAAE,IAAI,CAACzB,KAAK,CAACqB,UAAU,CAACI,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAAC1B,KAAK,CAACqB,UAAU,CAACK,SAAU;UAC3CsD,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBrD,OAAO,EAAE,IAAI,CAAC3B,KAAK,CAACqB,UAAU,CAACM,OAAQ;UACvCsI,gBAAgB,EAAE,KAAM;UACxBC,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAAClF,UAAW;UACnCmF,gBAAgB,eAAElL,OAAA;YAAU0I,SAAS,EAAC,MAAM;YAAC5F,IAAI,EAAC;UAAgB;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/EmC,OAAO,EAAC,QAAQ;UAChBC,SAAS,EAAC;QAAS;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhJ,OAAA,CAACX,MAAM;QACHgM,OAAO,EAAE,IAAI,CAACvK,KAAK,CAACM,aAAc;QAClC+H,MAAM,EAAEhK,QAAQ,CAACmM,MAAO;QACxBC,KAAK;QACL7C,SAAS,EAAC,kBAAkB;QAC5B8C,MAAM,EAAEjD,kBAAmB;QAC3BkD,MAAM,EAAE,IAAI,CAAChG,kBAAmB;QAChCiG,SAAS,EAAE,KAAM;QAAAjD,QAAA,eAEjBzI,OAAA,CAACJ,mBAAmB;UAChBuD,SAAS,EAAE,IAAI,CAACrC,KAAK,CAACa,MAAO;UAC7BA,MAAM,EAAE,IAAI,CAACb,KAAK,CAACE,QAAS;UAC5BD,OAAO,EAAE,IAAI,CAACD,KAAK,CAACa,MAAO;UAC3BgK,MAAM,EAAE;QAAK;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACThJ,OAAA,CAACL,OAAO;QAAC0L,OAAO,EAAE,IAAI,CAACvK,KAAK,CAACK,YAAa;QAACyK,QAAQ,EAAC,MAAM;QAACH,MAAM,EAAE,IAAI,CAACzF,WAAY;QAAAyC,QAAA,gBAChFzI,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACqI,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKzI,OAAA;YAAG0I,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ChJ,OAAA;YAAI0I,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACzI,OAAA;cAAG0I,SAAS,EAAC,mBAAmB;cAACmD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC7J,QAAQ,CAAC2M,MAAM;UAAA;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/GhJ,OAAA,CAACR,MAAM;YAACa,EAAE,EAAC,iBAAiB;YAACqI,SAAS,EAAEqB,WAAY;YAACpB,OAAO,EAAE,IAAI,CAACjD,KAAM;YAAA+C,QAAA,gBAACzI,OAAA;cAAG0I,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAAhJ,OAAA;cAAAyI,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACNhJ,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACqI,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DzI,OAAA;YAAI0I,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACzI,OAAA;cAAG0I,SAAS,EAAC,mBAAmB;cAACmD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC7J,QAAQ,CAAC2M,MAAM;UAAA;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/GhJ,OAAA,CAACR,MAAM;YAACa,EAAE,EAAC,kBAAkB;YAACqI,SAAS,EAAEqB,WAAY;YAACpB,OAAO,EAAE,IAAI,CAAChD,SAAU;YAAA8C,QAAA,gBAACzI,OAAA;cAAG0I,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAAhJ,OAAA;cAAAyI,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACNhJ,OAAA;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThJ,OAAA,CAACN,QAAQ;UAACgJ,SAAS,EAAC,OAAO;UAAC5G,KAAK,EAAE,IAAI,CAAChB,KAAK,CAACqD,gBAAiB;UAAC4H,OAAO,EAAE,IAAI,CAAC3G,SAAU;UAAC4G,QAAQ,EAAE,IAAI,CAACrJ,SAAU;UAACsJ,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,mBAAmB;UAACC,MAAM;UAACC,QAAQ,EAAC;QAAM;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe/I,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}