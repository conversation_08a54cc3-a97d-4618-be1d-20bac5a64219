{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"ns\", \"children\"];\nimport { useTranslation } from './useTranslation';\nexport function Translation(props) {\n  var ns = props.ns,\n    children = props.children,\n    options = _objectWithoutProperties(props, _excluded);\n  var _useTranslation = useTranslation(ns, options),\n    _useTranslation2 = _slicedToArray(_useTranslation, 3),\n    t = _useTranslation2[0],\n    i18n = _useTranslation2[1],\n    ready = _useTranslation2[2];\n  return children(t, {\n    i18n: i18n,\n    lng: i18n.language\n  }, ready);\n}", "map": {"version": 3, "names": ["_slicedToArray", "_objectWithoutProperties", "_excluded", "useTranslation", "Translation", "props", "ns", "children", "options", "_useTranslation", "_useTranslation2", "t", "i18n", "ready", "lng", "language"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/Translation.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"ns\", \"children\"];\nimport { useTranslation } from './useTranslation';\nexport function Translation(props) {\n  var ns = props.ns,\n      children = props.children,\n      options = _objectWithoutProperties(props, _excluded);\n\n  var _useTranslation = useTranslation(ns, options),\n      _useTranslation2 = _slicedToArray(_useTranslation, 3),\n      t = _useTranslation2[0],\n      i18n = _useTranslation2[1],\n      ready = _useTranslation2[2];\n\n  return children(t, {\n    i18n: i18n,\n    lng: i18n.language\n  }, ready);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sCAAsC;AACjE,OAAOC,wBAAwB,MAAM,gDAAgD;AACrF,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;AAClC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,IAAIC,EAAE,GAAGD,KAAK,CAACC,EAAE;IACbC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBC,OAAO,GAAGP,wBAAwB,CAACI,KAAK,EAAEH,SAAS,CAAC;EAExD,IAAIO,eAAe,GAAGN,cAAc,CAACG,EAAE,EAAEE,OAAO,CAAC;IAC7CE,gBAAgB,GAAGV,cAAc,CAACS,eAAe,EAAE,CAAC,CAAC;IACrDE,CAAC,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvBE,IAAI,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAC1BG,KAAK,GAAGH,gBAAgB,CAAC,CAAC,CAAC;EAE/B,OAAOH,QAAQ,CAACI,CAAC,EAAE;IACjBC,IAAI,EAAEA,IAAI;IACVE,GAAG,EAAEF,IAAI,CAACG;EACZ,CAAC,EAAEF,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}