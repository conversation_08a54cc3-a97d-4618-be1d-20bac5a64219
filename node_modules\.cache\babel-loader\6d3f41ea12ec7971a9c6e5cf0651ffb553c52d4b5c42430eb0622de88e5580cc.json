{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport { composeRef } from \"rc-util/es/ref\";\nimport raf from \"rc-util/es/raf\";\nimport Tooltip from '../tooltip';\nvar SliderTooltip = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var visible = props.visible;\n  var innerRef = useRef(null);\n  var rafRef = useRef(null);\n  function cancelKeepAlign() {\n    raf.cancel(rafRef.current);\n    rafRef.current = null;\n  }\n  function keepAlign() {\n    rafRef.current = raf(function () {\n      var _a;\n      (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.forcePopupAlign();\n      rafRef.current = null;\n    });\n  }\n  React.useEffect(function () {\n    if (visible) {\n      keepAlign();\n    } else {\n      cancelKeepAlign();\n    }\n    return cancelKeepAlign;\n  }, [visible, props.title]);\n  return /*#__PURE__*/React.createElement(Tooltip, _extends({\n    ref: composeRef(innerRef, ref)\n  }, props));\n});\nexport default SliderTooltip;", "map": {"version": 3, "names": ["_extends", "React", "useRef", "composeRef", "raf", "<PERSON><PERSON><PERSON>", "SliderTooltip", "forwardRef", "props", "ref", "visible", "innerRef", "rafRef", "cancelKeepAlign", "cancel", "current", "keepAlign", "_a", "forcePopupAlign", "useEffect", "title", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/slider/SliderTooltip.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport { composeRef } from \"rc-util/es/ref\";\nimport raf from \"rc-util/es/raf\";\nimport Tooltip from '../tooltip';\nvar SliderTooltip = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var visible = props.visible;\n  var innerRef = useRef(null);\n  var rafRef = useRef(null);\n\n  function cancelKeepAlign() {\n    raf.cancel(rafRef.current);\n    rafRef.current = null;\n  }\n\n  function keepAlign() {\n    rafRef.current = raf(function () {\n      var _a;\n\n      (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.forcePopupAlign();\n      rafRef.current = null;\n    });\n  }\n\n  React.useEffect(function () {\n    if (visible) {\n      keepAlign();\n    } else {\n      cancelKeepAlign();\n    }\n\n    return cancelKeepAlign;\n  }, [visible, props.title]);\n  return /*#__PURE__*/React.createElement(Tooltip, _extends({\n    ref: composeRef(innerRef, ref)\n  }, props));\n});\nexport default SliderTooltip;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,OAAO,MAAM,YAAY;AAChC,IAAIC,aAAa,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC3B,IAAIC,QAAQ,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIU,MAAM,GAAGV,MAAM,CAAC,IAAI,CAAC;EAEzB,SAASW,eAAeA,CAAA,EAAG;IACzBT,GAAG,CAACU,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;IAC1BH,MAAM,CAACG,OAAO,GAAG,IAAI;EACvB;EAEA,SAASC,SAASA,CAAA,EAAG;IACnBJ,MAAM,CAACG,OAAO,GAAGX,GAAG,CAAC,YAAY;MAC/B,IAAIa,EAAE;MAEN,CAACA,EAAE,GAAGN,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,eAAe,CAAC,CAAC;MACjFN,MAAM,CAACG,OAAO,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EAEAd,KAAK,CAACkB,SAAS,CAAC,YAAY;IAC1B,IAAIT,OAAO,EAAE;MACXM,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLH,eAAe,CAAC,CAAC;IACnB;IAEA,OAAOA,eAAe;EACxB,CAAC,EAAE,CAACH,OAAO,EAAEF,KAAK,CAACY,KAAK,CAAC,CAAC;EAC1B,OAAO,aAAanB,KAAK,CAACoB,aAAa,CAAChB,OAAO,EAAEL,QAAQ,CAAC;IACxDS,GAAG,EAAEN,UAAU,CAACQ,QAAQ,EAAEF,GAAG;EAC/B,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}