{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\inventario.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ResponsableJobsIngresso - operazioni sulle lavorazioni in entrata\n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { AggiungiDocIventario } from \"../../aggiunta_dati/aggiungiDocInventario\";\nimport Nav from \"../../components/navigation/Nav\";\nimport DettaglioDocumento from \"../../components/generalizzazioni/dettaglioDocumento\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaOperatore from \"./selezionaOperatore\";\nimport Caricamento from \"../../utils/caricamento\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass Inventario extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      var url = 'documents?idWarehouses=' + e.value + '&documentType=INVENTORY&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      var document = [];\n      await APIRequest(\"GET\", url).then(res => {\n        var nDoc = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks;\n          nDoc.push(element.number);\n          element.status = (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status;\n          document.push(element);\n        });\n        this.setState({\n          results: document,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false,\n          nDoc: nDoc\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei documenti inventario. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      globalFilter: null,\n      deleteResultDialog: false,\n      idEmployee: 0,\n      opMag: \"\",\n      mex: \"\",\n      firstName: \"\",\n      address: \"\",\n      indFatt: \"\",\n      loading: true,\n      selectedWarehouse: null,\n      displayed: false,\n      nDoc: [],\n      totalRecords: 0,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.opMag = [];\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.aggiungiDocumento = this.aggiungiDocumento.bind(this);\n    this.hideaggiungiDocumento = this.hideaggiungiDocumento.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=INVENTORY&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      var document = [];\n      await APIRequest(\"GET\", url).then(res => {\n        var nDoc = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks2;\n          nDoc.push(element.number);\n          element.status = (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status;\n          document.push(element);\n        });\n        this.setState({\n          results: document,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false,\n          nDoc: nDoc\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei documenti inventario. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog4: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        if (entry) {\n          this.warehouse.push({\n            name: entry.warehouseName || 'Magazzino sconosciuto',\n            value: entry.id || 0\n          });\n        }\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    let url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + '&idDocumentHead=' + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.setState({\n        results,\n        deleteResultDialog: false,\n        result: this.emptyResult\n      });\n      window.location.reload();\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo!\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.setState({\n        deleteResultDialog: false\n      });\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti!\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        var taskAss = 0;\n        taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n        opMag.push({\n          label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n          value: element.idemployee\n        });\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog: true,\n      opMag: opMag,\n      mex: message\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiDocumento() {\n    this.setState({\n      resultDialog3: true\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiDocumento() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=INVENTORY&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var document = [];\n        var nDoc = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks3;\n          nDoc.push(element.number);\n          element.status = (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status;\n          document.push(element);\n        });\n        this.setState({\n          results: document,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false,\n          nDoc: nDoc\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei documenti inventario. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=INVENTORY&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var document = [];\n        var nDoc = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks4;\n          nDoc.push(element.number);\n          element.status = (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status;\n          document.push(element);\n        });\n        this.setState({\n          results: document,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            event\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog4: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    var _this$state$result;\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hideaggiungiDocumento,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter4 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 13\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 45\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    const items = [{\n      label: Costanti.aggDoc,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiDocumento();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Inventario\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          fileNames: \"DocInventario\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field\",\n          children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n            result: this.state.result,\n            opMag: this.opMag\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(DettaglioDocumento, {\n          result: this.state.results3\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.aggDoc,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideaggiungiDocumento,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(AggiungiDocIventario, {\n          numero: this.state.nDoc,\n          selectedWarehouse: this.state.selectedWarehouse\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [(_this$state$result = this.state.result) === null || _this$state$result === void 0 ? void 0 : _this$state$result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog4,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter4,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Inventario;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "Toast", "<PERSON><PERSON>", "APIRequest", "Dialog", "Dropdown", "JoyrideGen", "Print", "AggiungiDocIventario", "Nav", "DettaglioDocumento", "CustomDataTable", "SelezionaOperatore", "Caricamento", "jsxDEV", "_jsxDEV", "Inventario", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "url", "state", "lazyParams", "rows", "page", "window", "sessionStorage", "setItem", "document", "then", "res", "nDoc", "data", "documents", "for<PERSON>ach", "element", "_element$tasks", "push", "number", "tasks", "results", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "results2", "results3", "results4", "result", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "globalFilter", "deleteResultDialog", "idEmployee", "opMag", "mex", "address", "indFatt", "displayed", "sortField", "sortOrder", "filters", "matchMode", "loadLazyTimeout", "warehouse", "editR<PERSON>ult", "bind", "hideDialog", "visualizzaDett", "hidevisualizzaDett", "aggiungiDocumento", "hideaggiungiDocumento", "confirmDeleteResult", "hideDeleteResultDialog", "deleteResult", "onPage", "onSort", "onFilter", "closeSelectBefore", "componentDidMount", "idWarehouse", "JSON", "parse", "getItem", "_element$tasks2", "_e$response3", "_e$response4", "_e$response5", "_e$response6", "entry", "name", "warehouseName", "_e$response7", "_e$response8", "filter", "val", "location", "reload", "_e$response9", "_e$response0", "documentBody", "task", "documentBodies", "_e$response1", "_e$response10", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "documentDate", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "submitted", "event", "clearTimeout", "setTimeout", "_element$tasks3", "_e$response11", "_e$response12", "Math", "random", "field", "_element$tasks4", "_e$response13", "_e$response14", "loadLazyData", "render", "_this$state$result", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "doc", "resultDialogFooter3", "resultDialogFooter4", "deleteResultDialogFooter", "icon", "Si", "fields", "header", "NDoc", "body", "sortable", "showHeader", "type", "DataDoc", "Operatore", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "assegnaLavorazione", "status2", "Elimina", "items", "aggDoc", "command", "ref", "el", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "splitButtonClass", "fileNames", "visible", "modal", "footer", "onHide", "draggable", "numero", "Conferma", "fontSize", "ResDeleteDoc", "Primadiproseguire", "title", "content", "target"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/inventario.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ResponsableJobsIngresso - operazioni sulle lavorazioni in entrata\n*\n*/\nimport React, { Component } from \"react\";\nimport { <PERSON><PERSON> } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { AggiungiDocIventario } from \"../../aggiunta_dati/aggiungiDocInventario\";\nimport Nav from \"../../components/navigation/Nav\";\nimport DettaglioDocumento from \"../../components/generalizzazioni/dettaglioDocumento\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaOperatore from \"./selezionaOperatore\";\nimport Caricamento from \"../../utils/caricamento\";\n\nclass Inventario extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            result: this.emptyResult,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            globalFilter: null,\n            deleteResultDialog: false,\n            idEmployee: 0,\n            opMag: \"\",\n            mex: \"\",\n            firstName: \"\",\n            address: \"\",\n            indFatt: \"\",\n            loading: true,\n            selectedWarehouse: null,\n            displayed: false,\n            nDoc: [],\n            totalRecords: 0,\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        this.loadLazyTimeout = null;\n        this.warehouse = [];\n        this.opMag = [];\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.aggiungiDocumento = this.aggiungiDocumento.bind(this);\n        this.hideaggiungiDocumento = this.hideaggiungiDocumento.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=INVENTORY&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse });\n            var document = [];\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var nDoc = []\n                    res.data.documents.forEach((element) => {\n                        nDoc.push(element.number)\n                        element.status = element.tasks?.status\n                        document.push(element);\n                    });\n                    this.setState({\n                        results: document,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false,\n                        nDoc: nDoc\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei documenti inventario. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog4: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    if (entry) {\n                        this.warehouse.push({\n                            name: entry.warehouseName || 'Magazzino sconosciuto',\n                            value: entry.id || 0\n                        })\n                    }\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value });\n        var url = 'documents?idWarehouses=' + e.value + '&documentType=INVENTORY&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        var document = [];\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var nDoc = []\n                res.data.documents.forEach((element) => {\n                    nDoc.push(element.number)\n                    element.status = element.tasks?.status\n                    document.push(element);\n                });\n                this.setState({\n                    results: document,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false,\n                    nDoc: nDoc\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei documenti inventario. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Chiusura dialogo eliminazione senza azioni\n    hideDeleteResultDialog() {\n        this.setState({ deleteResultDialog: false });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n\n        let url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + '&idDocumentHead=' + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.setState({\n                    results,\n                    deleteResultDialog: false,\n                    result: this.emptyResult,\n                });\n                window.location.reload();\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo!\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n            }).catch((e) => {\n                console.log(e)\n                this.setState({\n                    deleteResultDialog: false,\n                });\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti!\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                var taskAss = 0\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                opMag.push({\n                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                    value: element.idemployee,\n                });\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog: true,\n            opMag: opMag,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            submitted: false,\n            resultDialog: false,\n        });\n    }\n    //Apertura dialogo aggiunta\n    aggiungiDocumento() {\n        this.setState({\n            resultDialog3: true,\n        });\n    }\n    //Chiusura dialogo aggiunta\n    hideaggiungiDocumento() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=INVENTORY&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var document = [];\n                    var nDoc = []\n                    res.data.documents.forEach((element) => {\n                        nDoc.push(element.number)\n                        element.status = element.tasks?.status\n                        document.push(element);\n                    });\n                    this.setState({\n                        results: document,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false,\n                        nDoc: nDoc\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei documenti inventario. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=INVENTORY&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var document = [];\n                    var nDoc = []\n                    res.data.documents.forEach((element) => {\n                        nDoc.push(element.number)\n                        element.status = element.tasks?.status\n                        document.push(element);\n                    });\n                    this.setState({\n                        results: document,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, event },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog4: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hideaggiungiDocumento}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        const resultDialogFooter4 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi di conferma o annullamento del dialogo di cancellazione\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                body: \"typeDoc\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.operator.idUser.username\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.status\",\n                header: Costanti.StatoTask,\n                body: \"assigned\",\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n            { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult }\n        ];\n        const items = [\n            {\n                label: Costanti.aggDoc,\n                icon: 'pi pi-plus-circle',\n                command: () => {\n                    this.aggiungiDocumento()\n                }\n            },\n        ]\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => (this.toast = el)} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.Inventario}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => (this.dt = el)}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        splitButtonClass={true}\n                        items={items}\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        fileNames=\"DocInventario\"\n                    />\n                </div>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hideDialog}\n                >\n                    <div className=\"p-field\">\n                        <SelezionaOperatore result={this.state.result} opMag={this.opMag} />\n                    </div>\n                </Dialog>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter2}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <DettaglioDocumento\n                        result={this.state.results3}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per l'aggiunta */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={Costanti.aggDoc}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideaggiungiDocumento}\n                >\n                    <Caricamento />\n                    <AggiungiDocIventario numero={this.state.nDoc} selectedWarehouse={this.state.selectedWarehouse} />\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result?.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog4} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter4}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n            </div>\n        );\n    }\n}\n\nexport default Inventario;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,kBAAkB,MAAM,sDAAsD;AACrF,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,UAAU,SAASjB,SAAS,CAAC;EAa/BkB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAgID;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7C,IAAIC,GAAG,GAAG,yBAAyB,GAAGJ,CAAC,CAACG,KAAK,GAAG,+BAA+B,GAAG,IAAI,CAACE,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MACpJC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEX,CAAC,CAACG,KAAK,CAAC;MACrD,IAAIS,QAAQ,GAAG,EAAE;MACjB,MAAMvC,UAAU,CAAC,KAAK,EAAE+B,GAAG,CAAC,CACvBS,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,IAAI,GAAG,EAAE;QACbD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAEC,OAAO,IAAK;UAAA,IAAAC,cAAA;UACpCL,IAAI,CAACM,IAAI,CAACF,OAAO,CAACG,MAAM,CAAC;UACzBH,OAAO,CAACrB,MAAM,IAAAsB,cAAA,GAAGD,OAAO,CAACI,KAAK,cAAAH,cAAA,uBAAbA,cAAA,CAAetB,MAAM;UACtCc,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAAClB,QAAQ,CAAC;UACVuB,OAAO,EAAEZ,QAAQ;UACjBa,YAAY,EAAEX,GAAG,CAACE,IAAI,CAACU,UAAU;UACjCpB,UAAU,EAAE;YAAEqB,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACC,UAAU,CAACqB,KAAK;YAAEpB,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEoB,SAAS,EAAEd,GAAG,CAACE,IAAI,CAACU,UAAU,GAAG,IAAI,CAACrB,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLsB,OAAO,EAAE,KAAK;UACdd,IAAI,EAAEA;QACV,CAAC,CAAC;MACN,CAAC,CAAC,CACDe,KAAK,CAAE9B,CAAC,IAAK;QAAA,IAAA+B,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;QACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,gGAAAC,MAAA,CAA6F,EAAAT,WAAA,GAAA/B,CAAC,CAACyC,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYf,IAAI,MAAK0B,SAAS,IAAAV,YAAA,GAAGhC,CAAC,CAACyC,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYhB,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;UAClKC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IA5JG,IAAI,CAACvC,KAAK,GAAG;MACTmB,OAAO,EAAE,IAAI;MACbqB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAAC3D,WAAW;MACxB4D,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE,KAAK;MACzBC,UAAU,EAAE,CAAC;MACbC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPlE,SAAS,EAAE,EAAE;MACbmE,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACX9B,OAAO,EAAE,IAAI;MACb3B,iBAAiB,EAAE,IAAI;MACvB0D,SAAS,EAAE,KAAK;MAChB7C,IAAI,EAAE,EAAE;MACRU,YAAY,EAAE,CAAC;MACfnB,UAAU,EAAE;QACRqB,KAAK,EAAE,CAAC;QACRpB,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPqD,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAE5D,KAAK,EAAE,EAAE;YAAE6D,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE7D,KAAK,EAAE,EAAE;YAAE6D,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE7D,KAAK,EAAE,EAAE;YAAE6D,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACV,KAAK,GAAG,EAAE;IACf,IAAI,CAACW,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACE,cAAc,GAAG,IAAI,CAACA,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACK,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACL,IAAI,CAAC,IAAI,CAAC;IAClE,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACN,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACO,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACP,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACA,YAAY,CAACR,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACrE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACqE,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACS,MAAM,GAAG,IAAI,CAACA,MAAM,CAACT,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACU,MAAM,GAAG,IAAI,CAACA,MAAM,CAACV,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACW,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACX,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACY,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACZ,IAAI,CAAC,IAAI,CAAC;EAC9D;EACA;EACA,MAAMa,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC3E,MAAM,CAACC,cAAc,CAAC2E,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI9E,GAAG,GAAG,yBAAyB,GAAG8E,WAAW,GAAG,+BAA+B,GAAG,IAAI,CAAC7E,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MACxJ,IAAI,CAACP,QAAQ,CAAC;QAAEC,iBAAiB,EAAEgF;MAAY,CAAC,CAAC;MACjD,IAAItE,QAAQ,GAAG,EAAE;MACjB,MAAMvC,UAAU,CAAC,KAAK,EAAE+B,GAAG,CAAC,CACvBS,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,IAAI,GAAG,EAAE;QACbD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAEC,OAAO,IAAK;UAAA,IAAAmE,eAAA;UACpCvE,IAAI,CAACM,IAAI,CAACF,OAAO,CAACG,MAAM,CAAC;UACzBH,OAAO,CAACrB,MAAM,IAAAwF,eAAA,GAAGnE,OAAO,CAACI,KAAK,cAAA+D,eAAA,uBAAbA,eAAA,CAAexF,MAAM;UACtCc,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAAClB,QAAQ,CAAC;UACVuB,OAAO,EAAEZ,QAAQ;UACjBa,YAAY,EAAEX,GAAG,CAACE,IAAI,CAACU,UAAU;UACjCpB,UAAU,EAAE;YAAEqB,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACC,UAAU,CAACqB,KAAK;YAAEpB,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEoB,SAAS,EAAEd,GAAG,CAACE,IAAI,CAACU,UAAU,GAAG,IAAI,CAACrB,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLsB,OAAO,EAAE,KAAK;UACdd,IAAI,EAAEA;QACV,CAAC,CAAC;MACN,CAAC,CAAC,CACDe,KAAK,CAAE9B,CAAC,IAAK;QAAA,IAAAuF,YAAA,EAAAC,YAAA;QACVvD,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;QACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,gGAAAC,MAAA,CAA6F,EAAA+C,YAAA,GAAAvF,CAAC,CAACyC,QAAQ,cAAA8C,YAAA,uBAAVA,YAAA,CAAYvE,IAAI,MAAK0B,SAAS,IAAA8C,YAAA,GAAGxF,CAAC,CAACyC,QAAQ,cAAA+C,YAAA,uBAAVA,YAAA,CAAYxE,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;UAClKC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAAC3C,QAAQ,CAAC;QAAEmD,aAAa,EAAE,IAAI;QAAEQ,SAAS,EAAE;MAAK,CAAC,CAAC;IAC3D;IACA,MAAMvF,UAAU,CAAC,KAAK,EAAE,4CAA4C,CAAC,CAChEwC,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACb,QAAQ,CAAC;QACV8C,QAAQ,EAAEjC,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDc,KAAK,CAAE9B,CAAC,IAAK;MAAA,IAAAyF,YAAA,EAAAC,YAAA;MACVzD,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;MACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAAiD,YAAA,GAAAzF,CAAC,CAACyC,QAAQ,cAAAgD,YAAA,uBAAVA,YAAA,CAAYzE,IAAI,MAAK0B,SAAS,IAAAgD,YAAA,GAAG1F,CAAC,CAACyC,QAAQ,cAAAiD,YAAA,uBAAVA,YAAA,CAAY1E,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMvE,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCwC,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAI6E,KAAK,IAAI7E,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI2E,KAAK,EAAE;UACP,IAAI,CAACzB,SAAS,CAAC7C,IAAI,CAAC;YAChBuE,IAAI,EAAED,KAAK,CAACE,aAAa,IAAI,uBAAuB;YACpD1F,KAAK,EAAEwF,KAAK,CAACrG,EAAE,IAAI;UACvB,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,CAAC,CACDwC,KAAK,CAAE9B,CAAC,IAAK;MAAA,IAAA8F,YAAA,EAAAC,YAAA;MACV9D,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;MACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAsD,YAAA,GAAA9F,CAAC,CAACyC,QAAQ,cAAAqD,YAAA,uBAAVA,YAAA,CAAY9E,IAAI,MAAK0B,SAAS,IAAAqD,YAAA,GAAG/F,CAAC,CAACyC,QAAQ,cAAAsD,YAAA,uBAAVA,YAAA,CAAY/E,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EAiCA;EACA8B,mBAAmBA,CAAC1B,MAAM,EAAE;IACxB,IAAI,CAAC/C,QAAQ,CAAC;MACV+C,MAAM;MACNM,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACAqB,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC1E,QAAQ,CAAC;MAAEqD,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAChD;EACA;EACA,MAAMsB,YAAYA,CAAA,EAAG;IACjB,IAAIpD,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACmB,OAAO,CAACwE,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAAC3G,EAAE,KAAK,IAAI,CAACe,KAAK,CAAC2C,MAAM,CAAC1D,EAC1C,CAAC;IAED,IAAIc,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,kBAAkB,GAAG,IAAI,CAACG,KAAK,CAAC2C,MAAM,CAAC1D,EAAE;IAC/G,MAAMjB,UAAU,CAAC,QAAQ,EAAE+B,GAAG,CAAC,CAC1BS,IAAI,CAACC,GAAG,IAAI;MACTmB,OAAO,CAACC,GAAG,CAACpB,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACf,QAAQ,CAAC;QACVuB,OAAO;QACP8B,kBAAkB,EAAE,KAAK;QACzBN,MAAM,EAAE,IAAI,CAAC3D;MACjB,CAAC,CAAC;MACFoB,MAAM,CAACyF,QAAQ,CAACC,MAAM,CAAC,CAAC;MACxB,IAAI,CAAChE,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,SAAS;QAClBC,MAAM,EAAE,kCAAkC;QAC1CK,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC,CAACd,KAAK,CAAE9B,CAAC,IAAK;MAAA,IAAAoG,YAAA,EAAAC,YAAA;MACZpE,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;MACd,IAAI,CAACC,QAAQ,CAAC;QACVqD,kBAAkB,EAAE;MACxB,CAAC,CAAC;MACF,IAAI,CAACnB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,wEAAAC,MAAA,CAAqE,EAAA4D,YAAA,GAAApG,CAAC,CAACyC,QAAQ,cAAA2D,YAAA,uBAAVA,YAAA,CAAYpF,IAAI,MAAK0B,SAAS,IAAA2D,YAAA,GAAGrG,CAAC,CAACyC,QAAQ,cAAA4D,YAAA,uBAAVA,YAAA,CAAYrF,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;QAC1IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACA,MAAM0B,cAAcA,CAACtB,MAAM,EAAE;IACzB,IAAI5C,GAAG,GAAG,2BAA2B,GAAG4C,MAAM,CAAC1D,EAAE;IACjD,IAAIgH,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMlI,UAAU,CAAC,KAAK,EAAE+B,GAAG,CAAC,CACvBS,IAAI,CAAEC,GAAG,IAAK;MACXwF,YAAY,GAAGxF,GAAG,CAACE,IAAI,CAACwF,cAAc;MACtCD,IAAI,GAAGzF,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDc,KAAK,CAAE9B,CAAC,IAAK;MAAA,IAAAyG,YAAA,EAAAC,aAAA;MACVzE,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;MACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAiE,YAAA,GAAAzG,CAAC,CAACyC,QAAQ,cAAAgE,YAAA,uBAAVA,YAAA,CAAYzF,IAAI,MAAK0B,SAAS,IAAAgE,aAAA,GAAG1G,CAAC,CAACyC,QAAQ,cAAAiE,aAAA,uBAAVA,aAAA,CAAY1F,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBK,MAAM,CAAC1B,MAAM,GACb,OAAO,GACP,IAAIqF,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACjE,MAAM,CAACkE,YAAY,CAAC,CAAC;IAC5C,IAAI,CAACjH,QAAQ,CAAC;MACViD,aAAa,EAAE,IAAI;MACnBF,MAAM,EAAEuD,IAAI;MACZ1D,QAAQ,EAAE,IAAI,CAACxC,KAAK,CAACmB,OAAO,CAACwE,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAC3G,EAAE,KAAK0D,MAAM,CAAC1D,EAAE,CAAC;MAClEwD,QAAQ,EAAEwD,YAAY;MACtB7C,GAAG,EAAEd;IACT,CAAC,CAAC;EACN;EACA;EACA4B,kBAAkBA,CAACvB,MAAM,EAAE;IACvB,IAAI,CAAC/C,QAAQ,CAAC;MACV+C,MAAM,EAAEA,MAAM;MACdE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACAiB,UAAUA,CAACnB,MAAM,EAAE;IACf,IAAIL,OAAO,GACP,oBAAoB,GACpBK,MAAM,CAAC1B,MAAM,GACb,OAAO,GACP,IAAIqF,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACjE,MAAM,CAACkE,YAAY,CAAC,CAAC;IAC5C,IAAI1D,KAAK,GAAG,EAAE;IACd,IAAI,IAAI,CAACnD,KAAK,CAAC0C,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAAC1C,KAAK,CAAC0C,QAAQ,CAAC7B,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIgG,OAAO,GAAG,CAAC;QACfA,OAAO,GAAGC,QAAQ,CAACjG,OAAO,CAACkG,WAAW,CAAC,GAAGD,QAAQ,CAACjG,OAAO,CAACmG,YAAY,CAAC,GAAGF,QAAQ,CAACjG,OAAO,CAACoG,aAAa,CAAC;QAC1G/D,KAAK,CAACnC,IAAI,CAAC;UACPmG,KAAK,EAAErG,OAAO,CAACsG,UAAU,GAAG,GAAG,GAAGtG,OAAO,CAACuG,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;UAC1FhH,KAAK,EAAEgB,OAAO,CAACwG;QACnB,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAI,CAACnE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACvD,QAAQ,CAAC;MACV+C,MAAM,EAAA4E,aAAA,KAAO5E,MAAM,CAAE;MACrBC,YAAY,EAAE,IAAI;MAClBO,KAAK,EAAEA,KAAK;MACZC,GAAG,EAAEd;IACT,CAAC,CAAC;EACN;EACA;EACA0B,UAAUA,CAAA,EAAG;IACT,IAAI,CAACpE,QAAQ,CAAC;MACV4H,SAAS,EAAE,KAAK;MAChB5E,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACAuB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvE,QAAQ,CAAC;MACVkD,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACAsB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACxE,QAAQ,CAAC;MACVkD,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA0B,MAAMA,CAACiD,KAAK,EAAE;IACV,IAAI,CAAC7H,QAAQ,CAAC;MAAE4B,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAACoC,eAAe,EAAE;MACtB8D,YAAY,CAAC,IAAI,CAAC9D,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAG+D,UAAU,CAAC,YAAY;MAC1C,IAAI5H,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,+BAA+B,GAAG4H,KAAK,CAACvH,IAAI,GAAG,QAAQ,GAAGuH,KAAK,CAACtH,IAAI;MACzI,MAAMnC,UAAU,CAAC,KAAK,EAAE+B,GAAG,CAAC,CACvBS,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIF,QAAQ,GAAG,EAAE;QACjB,IAAIG,IAAI,GAAG,EAAE;QACbD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAEC,OAAO,IAAK;UAAA,IAAA8G,eAAA;UACpClH,IAAI,CAACM,IAAI,CAACF,OAAO,CAACG,MAAM,CAAC;UACzBH,OAAO,CAACrB,MAAM,IAAAmI,eAAA,GAAG9G,OAAO,CAACI,KAAK,cAAA0G,eAAA,uBAAbA,eAAA,CAAenI,MAAM;UACtCc,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAAClB,QAAQ,CAAC;UACVuB,OAAO,EAAEZ,QAAQ;UACjBa,YAAY,EAAEX,GAAG,CAACE,IAAI,CAACU,UAAU;UACjCpB,UAAU,EAAEwH,KAAK;UACjBjG,OAAO,EAAE,KAAK;UACdd,IAAI,EAAEA;QACV,CAAC,CAAC;MACN,CAAC,CAAC,CACDe,KAAK,CAAE9B,CAAC,IAAK;QAAA,IAAAkI,aAAA,EAAAC,aAAA;QACVlG,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;QACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,gGAAAC,MAAA,CAA6F,EAAA0F,aAAA,GAAAlI,CAAC,CAACyC,QAAQ,cAAAyF,aAAA,uBAAVA,aAAA,CAAYlH,IAAI,MAAK0B,SAAS,IAAAyF,aAAA,GAAGnI,CAAC,CAACyC,QAAQ,cAAA0F,aAAA,uBAAVA,aAAA,CAAYnH,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;UAClKC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEwF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAvD,MAAMA,CAACgD,KAAK,EAAE;IACV,IAAI,CAAC7H,QAAQ,CAAC;MAAE4B,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIyG,KAAK,GAAGR,KAAK,CAACjE,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGiE,KAAK,CAACjE,SAAS;IAChG,IAAI,IAAI,CAACI,eAAe,EAAE;MACtB8D,YAAY,CAAC,IAAI,CAAC9D,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAG+D,UAAU,CAAC,YAAY;MAC1C,IAAI5H,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,+BAA+B,GAAG,IAAI,CAACG,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI,GAAG,SAAS,GAAG8H,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAChE,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACtP,MAAMzF,UAAU,CAAC,KAAK,EAAE+B,GAAG,CAAC,CACvBS,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIF,QAAQ,GAAG,EAAE;QACjB,IAAIG,IAAI,GAAG,EAAE;QACbD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAEC,OAAO,IAAK;UAAA,IAAAoH,eAAA;UACpCxH,IAAI,CAACM,IAAI,CAACF,OAAO,CAACG,MAAM,CAAC;UACzBH,OAAO,CAACrB,MAAM,IAAAyI,eAAA,GAAGpH,OAAO,CAACI,KAAK,cAAAgH,eAAA,uBAAbA,eAAA,CAAezI,MAAM;UACtCc,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAAClB,QAAQ,CAAC;UACVuB,OAAO,EAAEZ,QAAQ;UACjBa,YAAY,EAAEX,GAAG,CAACE,IAAI,CAACU,UAAU;UACjCpB,UAAU,EAAAsH,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACvH,KAAK,CAACC,UAAU;YAAEwH;UAAK,EAAE;UAC/CjG,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE9B,CAAC,IAAK;QAAA,IAAAwI,aAAA,EAAAC,aAAA;QACVxG,OAAO,CAACC,GAAG,CAAClC,CAAC,CAAC;QACd,IAAI,CAACmC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAgG,aAAA,GAAAxI,CAAC,CAACyC,QAAQ,cAAA+F,aAAA,uBAAVA,aAAA,CAAYxH,IAAI,MAAK0B,SAAS,IAAA+F,aAAA,GAAGzI,CAAC,CAACyC,QAAQ,cAAAgG,aAAA,uBAAVA,aAAA,CAAYzH,IAAI,GAAGhB,CAAC,CAAC2C,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEwF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EAEAtD,QAAQA,CAAC+C,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC7H,QAAQ,CAAC;MAAEK,UAAU,EAAEwH;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EAC3D;EACA1D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC3E,KAAK,CAACH,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVmD,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACjB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA+F,MAAMA,CAAA,EAAG;IAAA,IAAAC,kBAAA;IACL;IACA,MAAMC,kBAAkB,gBACpB5J,OAAA,CAACjB,KAAK,CAAC8K,QAAQ;MAAAC,QAAA,eACX9J,OAAA,CAACf,MAAM;QAAC8K,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC5E,UAAW;QAAA0E,QAAA,GACjE,GAAG,EACH3K,QAAQ,CAAC8K,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMC,mBAAmB,gBACrBtK,OAAA,CAACjB,KAAK,CAAC8K,QAAQ;MAAAC,QAAA,eACX9J,OAAA;QAAK+J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrB9J,OAAA;UAAK+J,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnB9J,OAAA;YAAK+J,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvC9J,OAAA,CAACf,MAAM;cACH8K,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAC1E,kBAAmB;cAAAwE,QAAA,GAEhC,GAAG,EACH3K,QAAQ,CAAC8K,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTrK,OAAA,CAACR,KAAK;cACFqE,QAAQ,EAAE,IAAI,CAACzC,KAAK,CAACyC,QAAS;cAC9BW,GAAG,EAAE,IAAI,CAACpD,KAAK,CAACoD,GAAI;cACpB+F,GAAG,EAAE;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMG,mBAAmB,gBACrBxK,OAAA,CAACjB,KAAK,CAAC8K,QAAQ;MAAAC,QAAA,eACX9J,OAAA;QAAK+J,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrB9J,OAAA;UAAK+J,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnB9J,OAAA;YAAK+J,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACvC9J,OAAA,CAACf,MAAM;cACH8K,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAACxE,qBAAsB;cAAAsE,QAAA,GAEnC,GAAG,EACH3K,QAAQ,CAAC8K,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAMI,mBAAmB,gBACrBzK,OAAA,CAACjB,KAAK,CAAC8K,QAAQ;MAAAC,QAAA,eACX9J,OAAA;QAAK+J,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1D9J,OAAA,CAACf,MAAM;UAAC8K,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACjE,iBAAkB;UAAA+D,QAAA,GAAE,GAAC,EAAC3K,QAAQ,CAAC8K,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMK,wBAAwB,gBAC1B1K,OAAA,CAACjB,KAAK,CAAC8K,QAAQ;MAAAC,QAAA,gBACX9J,OAAA,CAACf,MAAM;QACHsJ,KAAK,EAAC,IAAI;QACVoC,IAAI,EAAC,aAAa;QAClBZ,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAACtE;MAAuB;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFrK,OAAA,CAACf,MAAM;QAAC8K,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACrE,YAAa;QAAAmE,QAAA,GACxD,GAAG,EACH3K,QAAQ,CAACyL,EAAE,EAAE,GAAG;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMQ,MAAM,GAAG,CACX;MACIxB,KAAK,EAAE,QAAQ;MACfyB,MAAM,EAAE3L,QAAQ,CAAC4L,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,MAAM;MACbyB,MAAM,EAAE3L,QAAQ,CAACgM,IAAI;MACrBH,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,cAAc;MACrByB,MAAM,EAAE3L,QAAQ,CAACiM,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,gCAAgC;MACvCyB,MAAM,EAAE3L,QAAQ,CAACkM,SAAS;MAC1BL,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,cAAc;MACrByB,MAAM,EAAE3L,QAAQ,CAACmM,SAAS;MAC1BN,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMK,YAAY,GAAG,CACjB;MAAE5E,IAAI,EAAExH,QAAQ,CAACqM,OAAO;MAAEb,IAAI,eAAE3K,OAAA;QAAG+J,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEoB,OAAO,EAAE,IAAI,CAACpG;IAAe,CAAC,EAC3F;MAAEsB,IAAI,EAAExH,QAAQ,CAACuM,kBAAkB;MAAEf,IAAI,eAAE3K,OAAA;QAAG+J,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEoB,OAAO,EAAE,IAAI,CAACvG,UAAU;MAAErE,MAAM,EAAE,QAAQ;MAAE8K,OAAO,EAAE;IAAU,CAAC,EAC/I;MAAEhF,IAAI,EAAExH,QAAQ,CAACyM,OAAO;MAAEjB,IAAI,eAAE3K,OAAA;QAAG+J,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEoB,OAAO,EAAE,IAAI,CAAChG;IAAoB,CAAC,CACrG;IACD,MAAMoG,KAAK,GAAG,CACV;MACItD,KAAK,EAAEpJ,QAAQ,CAAC2M,MAAM;MACtBnB,IAAI,EAAE,mBAAmB;MACzBoB,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACxG,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC,CACJ;IACD,oBACIvF,OAAA;MAAK+J,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9C9J,OAAA,CAACd,KAAK;QAAC8M,GAAG,EAAGC,EAAE,IAAM,IAAI,CAAC/I,KAAK,GAAG+I;MAAI;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzCrK,OAAA,CAACN,GAAG;QAAAwK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPrK,OAAA;QAAK+J,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnC9J,OAAA;UAAA8J,QAAA,EAAK3K,QAAQ,CAACc;QAAU;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EACL,IAAI,CAACjJ,KAAK,CAACH,iBAAiB,KAAK,IAAI,iBAClCjB,OAAA;QAAK+J,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtC9J,OAAA;UAAI+J,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtE9J,OAAA;YAAI+J,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjE9J,OAAA;cAAK+J,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7D9J,OAAA;gBAAI+J,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAAC9J,OAAA;kBAAG+J,SAAS,EAAC,iBAAiB;kBAACmC,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAClL,QAAQ,CAACgN,SAAS,EAAC,GAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5HrK,OAAA,CAACV,QAAQ;gBAACyK,SAAS,EAAC,QAAQ;gBAAC7I,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;gBAACmL,OAAO,EAAE,IAAI,CAACnH,SAAU;gBAACoH,QAAQ,EAAE,IAAI,CAACvL,iBAAkB;gBAACwL,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACxF,MAAM;gBAACyF,QAAQ,EAAC;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEVrK,OAAA;QAAK+J,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjB9J,OAAA,CAACJ,eAAe;UACZoM,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACQ,EAAE,GAAGR,EAAI;UAC5B/K,KAAK,EAAE,IAAI,CAACE,KAAK,CAACmB,OAAQ;UAC1BsI,MAAM,EAAEA,MAAO;UACfjI,OAAO,EAAE,IAAI,CAACxB,KAAK,CAACwB,OAAQ;UAC5B8J,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTjH,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBlD,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACC,UAAU,CAACqB,KAAM;UACnCF,YAAY,EAAE,IAAI,CAACpB,KAAK,CAACoB,YAAa;UACtClB,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAK;UACjCwL,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAExB,YAAa;UAC5ByB,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBpB,KAAK,EAAEA,KAAM;UACbhG,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBjB,SAAS,EAAE,IAAI,CAACxD,KAAK,CAACC,UAAU,CAACuD,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACzD,KAAK,CAACC,UAAU,CAACwD,SAAU;UAC3CiB,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBhB,OAAO,EAAE,IAAI,CAAC1D,KAAK,CAACC,UAAU,CAACyD,OAAQ;UACvCoI,SAAS,EAAC;QAAe;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrK,OAAA,CAACX,MAAM;QACH8N,OAAO,EAAE,IAAI,CAAC/L,KAAK,CAAC4C,YAAa;QACjC8G,MAAM,EAAE,IAAI,CAAC1J,KAAK,CAACoD,GAAI;QACvB4I,KAAK;QACLrD,SAAS,EAAC,kBAAkB;QAC5BsD,MAAM,EAAEzD,kBAAmB;QAC3B0D,MAAM,EAAE,IAAI,CAAClI,UAAW;QAAA0E,QAAA,eAExB9J,OAAA;UAAK+J,SAAS,EAAC,SAAS;UAAAD,QAAA,eACpB9J,OAAA,CAACH,kBAAkB;YAACkE,MAAM,EAAE,IAAI,CAAC3C,KAAK,CAAC2C,MAAO;YAACQ,KAAK,EAAE,IAAI,CAACA;UAAM;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETrK,OAAA,CAACX,MAAM;QACH8N,OAAO,EAAE,IAAI,CAAC/L,KAAK,CAAC6C,aAAc;QAClC6G,MAAM,EAAE,IAAI,CAAC1J,KAAK,CAACoD,GAAI;QACvB4I,KAAK;QACLrD,SAAS,EAAC,kBAAkB;QAC5BsD,MAAM,EAAE/C,mBAAoB;QAC5BgD,MAAM,EAAE,IAAI,CAAChI,kBAAmB;QAChCiI,SAAS,EAAE,KAAM;QAAAzD,QAAA,eAEjB9J,OAAA,CAACL,kBAAkB;UACfoE,MAAM,EAAE,IAAI,CAAC3C,KAAK,CAACyC;QAAS;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAETrK,OAAA,CAACX,MAAM;QACH8N,OAAO,EAAE,IAAI,CAAC/L,KAAK,CAAC8C,aAAc;QAClC4G,MAAM,EAAE3L,QAAQ,CAAC2M,MAAO;QACxBsB,KAAK;QACLrD,SAAS,EAAC,kBAAkB;QAC5BsD,MAAM,EAAE7C,mBAAoB;QAC5B8C,MAAM,EAAE,IAAI,CAAC9H,qBAAsB;QAAAsE,QAAA,gBAEnC9J,OAAA,CAACF,WAAW;UAAAoK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfrK,OAAA,CAACP,oBAAoB;UAAC+N,MAAM,EAAE,IAAI,CAACpM,KAAK,CAACU,IAAK;UAACb,iBAAiB,EAAE,IAAI,CAACG,KAAK,CAACH;QAAkB;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F,CAAC,eAETrK,OAAA,CAACX,MAAM;QACH8N,OAAO,EAAE,IAAI,CAAC/L,KAAK,CAACiD,kBAAmB;QACvCyG,MAAM,EAAE3L,QAAQ,CAACsO,QAAS;QAC1BL,KAAK;QACLC,MAAM,EAAE3C,wBAAyB;QACjC4C,MAAM,EAAE,IAAI,CAAC5H,sBAAuB;QAAAoE,QAAA,eAEpC9J,OAAA;UAAK+J,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjC9J,OAAA;YACI+J,SAAS,EAAC,mCAAmC;YAC7CmC,KAAK,EAAE;cAAEwB,QAAQ,EAAE;YAAO;UAAE;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAACjJ,KAAK,CAAC2C,MAAM,iBACd/D,OAAA;YAAA8J,QAAA,GACK3K,QAAQ,CAACwO,YAAY,EAAC,GAAC,eAAA3N,OAAA;cAAA8J,QAAA,IAAAH,kBAAA,GAAI,IAAI,CAACvI,KAAK,CAAC2C,MAAM,cAAA4F,kBAAA,uBAAjBA,kBAAA,CAAmBtH,MAAM,EAAC,GAAC;YAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACTrK,OAAA,CAACX,MAAM;QAAC8N,OAAO,EAAE,IAAI,CAAC/L,KAAK,CAAC+C,aAAc;QAAC2G,MAAM,EAAE3L,QAAQ,CAACyO,iBAAkB;QAACR,KAAK;QAACrD,SAAS,EAAC,kBAAkB;QAACuD,MAAM,EAAE,IAAI,CAACvH,iBAAkB;QAACsH,MAAM,EAAE5C,mBAAoB;QAAAX,QAAA,GACzK,IAAI,CAAC1I,KAAK,CAACuD,SAAS,iBACjB3E,OAAA,CAACT,UAAU;UAACsO,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhGrK,OAAA;UAAK+J,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3D9J,OAAA;YAAI+J,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAC9J,OAAA;cAAG+J,SAAS,EAAC,iBAAiB;cAACmC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAClL,QAAQ,CAACgN,SAAS;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHrK,OAAA;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrK,OAAA,CAACV,QAAQ;YAACyK,SAAS,EAAC,QAAQ;YAAC7I,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;YAACmL,OAAO,EAAE,IAAI,CAACnH,SAAU;YAACoH,QAAQ,EAAE,IAAI,CAACvL,iBAAkB;YAACwL,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACxF,MAAM;YAACyF,QAAQ,EAAC;UAAM;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAepK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}