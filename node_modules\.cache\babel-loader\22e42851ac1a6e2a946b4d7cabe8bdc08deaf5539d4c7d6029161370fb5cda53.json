{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { FieldContext } from '.';\nimport warning from \"rc-util/es/warning\";\nimport { HOOK_MARK } from './FieldContext';\nimport { useState, useContext, useEffect, useRef } from 'react';\nimport { getNamePath, getValue } from './utils/valueUtil';\nfunction useWatch() {\n  var dependencies = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var form = arguments.length > 1 ? arguments[1] : undefined;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    setValue = _useState2[1];\n  var valueCacheRef = useRef();\n  valueCacheRef.current = value;\n  var fieldContext = useContext(FieldContext);\n  var formInstance = form || fieldContext;\n  var isValidForm = formInstance && formInstance._init; // Warning if not exist form instance\n\n  if (process.env.NODE_ENV !== 'production') {\n    warning(isValidForm, 'useWatch requires a form instance since it can not auto detect from context.');\n  }\n  var namePath = getNamePath(dependencies);\n  var namePathRef = useRef(namePath);\n  namePathRef.current = namePath;\n  useEffect(function () {\n    // Skip if not exist form instance\n    if (!isValidForm) {\n      return;\n    }\n    var getFieldsValue = formInstance.getFieldsValue,\n      getInternalHooks = formInstance.getInternalHooks;\n    var _getInternalHooks = getInternalHooks(HOOK_MARK),\n      registerWatch = _getInternalHooks.registerWatch;\n    var cancelRegister = registerWatch(function (store) {\n      var newValue = getValue(store, namePathRef.current);\n      if (valueCacheRef.current !== newValue) {\n        setValue(newValue);\n      }\n    }); // TODO: We can improve this perf in future\n\n    var initialValue = getValue(getFieldsValue(), namePathRef.current);\n    setValue(initialValue);\n    return cancelRegister;\n  },\n  // We do not need re-register since namePath content is the same\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  return value;\n}\nexport default useWatch;", "map": {"version": 3, "names": ["_slicedToArray", "FieldContext", "warning", "HOOK_MARK", "useState", "useContext", "useEffect", "useRef", "getNamePath", "getValue", "useWatch", "dependencies", "arguments", "length", "undefined", "form", "_useState", "_useState2", "value", "setValue", "valueCacheRef", "current", "fieldContext", "formInstance", "isValidForm", "_init", "process", "env", "NODE_ENV", "namePath", "namePathRef", "getFieldsValue", "getInternalHooks", "_getInternalHooks", "registerWatch", "cancelRegister", "store", "newValue", "initialValue"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/useWatch.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { FieldContext } from '.';\nimport warning from \"rc-util/es/warning\";\nimport { HOOK_MARK } from './FieldContext';\nimport { useState, useContext, useEffect, useRef } from 'react';\nimport { getNamePath, getValue } from './utils/valueUtil';\n\nfunction useWatch() {\n  var dependencies = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var form = arguments.length > 1 ? arguments[1] : undefined;\n\n  var _useState = useState(),\n      _useState2 = _slicedToArray(_useState, 2),\n      value = _useState2[0],\n      setValue = _useState2[1];\n\n  var valueCacheRef = useRef();\n  valueCacheRef.current = value;\n  var fieldContext = useContext(FieldContext);\n  var formInstance = form || fieldContext;\n  var isValidForm = formInstance && formInstance._init; // Warning if not exist form instance\n\n  if (process.env.NODE_ENV !== 'production') {\n    warning(isValidForm, 'useWatch requires a form instance since it can not auto detect from context.');\n  }\n\n  var namePath = getNamePath(dependencies);\n  var namePathRef = useRef(namePath);\n  namePathRef.current = namePath;\n  useEffect(function () {\n    // Skip if not exist form instance\n    if (!isValidForm) {\n      return;\n    }\n\n    var getFieldsValue = formInstance.getFieldsValue,\n        getInternalHooks = formInstance.getInternalHooks;\n\n    var _getInternalHooks = getInternalHooks(HOOK_MARK),\n        registerWatch = _getInternalHooks.registerWatch;\n\n    var cancelRegister = registerWatch(function (store) {\n      var newValue = getValue(store, namePathRef.current);\n\n      if (valueCacheRef.current !== newValue) {\n        setValue(newValue);\n      }\n    }); // TODO: We can improve this perf in future\n\n    var initialValue = getValue(getFieldsValue(), namePathRef.current);\n    setValue(initialValue);\n    return cancelRegister;\n  }, // We do not need re-register since namePath content is the same\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  return value;\n}\n\nexport default useWatch;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,YAAY,QAAQ,GAAG;AAChC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,QAAQ,QAAQ,mBAAmB;AAEzD,SAASC,QAAQA,CAAA,EAAG;EAClB,IAAIC,YAAY,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACzF,IAAIG,IAAI,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAE1D,IAAIE,SAAS,GAAGZ,QAAQ,CAAC,CAAC;IACtBa,UAAU,GAAGjB,cAAc,CAACgB,SAAS,EAAE,CAAC,CAAC;IACzCE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE5B,IAAIG,aAAa,GAAGb,MAAM,CAAC,CAAC;EAC5Ba,aAAa,CAACC,OAAO,GAAGH,KAAK;EAC7B,IAAII,YAAY,GAAGjB,UAAU,CAACJ,YAAY,CAAC;EAC3C,IAAIsB,YAAY,GAAGR,IAAI,IAAIO,YAAY;EACvC,IAAIE,WAAW,GAAGD,YAAY,IAAIA,YAAY,CAACE,KAAK,CAAC,CAAC;;EAEtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC1B,OAAO,CAACsB,WAAW,EAAE,8EAA8E,CAAC;EACtG;EAEA,IAAIK,QAAQ,GAAGrB,WAAW,CAACG,YAAY,CAAC;EACxC,IAAImB,WAAW,GAAGvB,MAAM,CAACsB,QAAQ,CAAC;EAClCC,WAAW,CAACT,OAAO,GAAGQ,QAAQ;EAC9BvB,SAAS,CAAC,YAAY;IACpB;IACA,IAAI,CAACkB,WAAW,EAAE;MAChB;IACF;IAEA,IAAIO,cAAc,GAAGR,YAAY,CAACQ,cAAc;MAC5CC,gBAAgB,GAAGT,YAAY,CAACS,gBAAgB;IAEpD,IAAIC,iBAAiB,GAAGD,gBAAgB,CAAC7B,SAAS,CAAC;MAC/C+B,aAAa,GAAGD,iBAAiB,CAACC,aAAa;IAEnD,IAAIC,cAAc,GAAGD,aAAa,CAAC,UAAUE,KAAK,EAAE;MAClD,IAAIC,QAAQ,GAAG5B,QAAQ,CAAC2B,KAAK,EAAEN,WAAW,CAACT,OAAO,CAAC;MAEnD,IAAID,aAAa,CAACC,OAAO,KAAKgB,QAAQ,EAAE;QACtClB,QAAQ,CAACkB,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIC,YAAY,GAAG7B,QAAQ,CAACsB,cAAc,CAAC,CAAC,EAAED,WAAW,CAACT,OAAO,CAAC;IAClEF,QAAQ,CAACmB,YAAY,CAAC;IACtB,OAAOH,cAAc;EACvB,CAAC;EAAE;EACH;EACA,EAAE,CAAC;EACH,OAAOjB,KAAK;AACd;AAEA,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}