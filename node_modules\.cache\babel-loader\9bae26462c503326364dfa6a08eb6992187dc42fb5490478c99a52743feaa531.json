{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport TabContext from '../TabContext';\nexport default function TabPanelList(_ref) {\n  var id = _ref.id,\n    activeKey = _ref.activeKey,\n    animated = _ref.animated,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl,\n    destroyInactiveTabPane = _ref.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var activeIndex = tabs.findIndex(function (tab) {\n    return tab.key === activeKey;\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated)),\n    style: activeIndex && tabPaneAnimated ? _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', \"-\".concat(activeIndex, \"00%\")) : null\n  }, tabs.map(function (tab) {\n    return /*#__PURE__*/React.cloneElement(tab.node, {\n      key: tab.key,\n      prefixCls: prefixCls,\n      tabKey: tab.key,\n      id: id,\n      animated: tabPaneAnimated,\n      active: tab.key === activeKey,\n      destroyInactiveTabPane: destroyInactiveTabPane\n    });\n  })));\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "TabContext", "TabPanelList", "_ref", "id", "active<PERSON><PERSON>", "animated", "tabPosition", "rtl", "destroyInactiveTabPane", "_React$useContext", "useContext", "prefixCls", "tabs", "tabPaneAnimated", "tabPane", "activeIndex", "findIndex", "tab", "key", "createElement", "className", "concat", "style", "map", "cloneElement", "node", "tabKey", "active"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/TabPanelList/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport TabContext from '../TabContext';\nexport default function TabPanelList(_ref) {\n  var id = _ref.id,\n      activeKey = _ref.activeKey,\n      animated = _ref.animated,\n      tabPosition = _ref.tabPosition,\n      rtl = _ref.rtl,\n      destroyInactiveTabPane = _ref.destroyInactiveTabPane;\n\n  var _React$useContext = React.useContext(TabContext),\n      prefixCls = _React$useContext.prefixCls,\n      tabs = _React$useContext.tabs;\n\n  var tabPaneAnimated = animated.tabPane;\n  var activeIndex = tabs.findIndex(function (tab) {\n    return tab.key === activeKey;\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated)),\n    style: activeIndex && tabPaneAnimated ? _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', \"-\".concat(activeIndex, \"00%\")) : null\n  }, tabs.map(function (tab) {\n    return /*#__PURE__*/React.cloneElement(tab.node, {\n      key: tab.key,\n      prefixCls: prefixCls,\n      tabKey: tab.key,\n      id: id,\n      animated: tabPaneAnimated,\n      active: tab.key === activeKey,\n      destroyInactiveTabPane: destroyInactiveTabPane\n    });\n  })));\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,eAAe;AACtC,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAE;EACzC,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;IACZC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BC,GAAG,GAAGL,IAAI,CAACK,GAAG;IACdC,sBAAsB,GAAGN,IAAI,CAACM,sBAAsB;EAExD,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACV,UAAU,CAAC;IAChDW,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;EAEjC,IAAIC,eAAe,GAAGR,QAAQ,CAACS,OAAO;EACtC,IAAIC,WAAW,GAAGH,IAAI,CAACI,SAAS,CAAC,UAAUC,GAAG,EAAE;IAC9C,OAAOA,GAAG,CAACC,GAAG,KAAKd,SAAS;EAC9B,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAErB,UAAU,CAAC,EAAE,CAACsB,MAAM,CAACV,SAAS,EAAE,iBAAiB,CAAC;EAC/D,CAAC,EAAE,aAAab,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAErB,UAAU,CAAC,EAAE,CAACsB,MAAM,CAACV,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAACU,MAAM,CAACV,SAAS,EAAE,WAAW,CAAC,CAACU,MAAM,CAACf,WAAW,CAAC,EAAET,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwB,MAAM,CAACV,SAAS,EAAE,mBAAmB,CAAC,EAAEE,eAAe,CAAC,CAAC;IAC/LS,KAAK,EAAEP,WAAW,IAAIF,eAAe,GAAGhB,eAAe,CAAC,CAAC,CAAC,EAAEU,GAAG,GAAG,aAAa,GAAG,YAAY,EAAE,GAAG,CAACc,MAAM,CAACN,WAAW,EAAE,KAAK,CAAC,CAAC,GAAG;EACpI,CAAC,EAAEH,IAAI,CAACW,GAAG,CAAC,UAAUN,GAAG,EAAE;IACzB,OAAO,aAAanB,KAAK,CAAC0B,YAAY,CAACP,GAAG,CAACQ,IAAI,EAAE;MAC/CP,GAAG,EAAED,GAAG,CAACC,GAAG;MACZP,SAAS,EAAEA,SAAS;MACpBe,MAAM,EAAET,GAAG,CAACC,GAAG;MACff,EAAE,EAAEA,EAAE;MACNE,QAAQ,EAAEQ,eAAe;MACzBc,MAAM,EAAEV,GAAG,CAACC,GAAG,KAAKd,SAAS;MAC7BI,sBAAsB,EAAEA;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}