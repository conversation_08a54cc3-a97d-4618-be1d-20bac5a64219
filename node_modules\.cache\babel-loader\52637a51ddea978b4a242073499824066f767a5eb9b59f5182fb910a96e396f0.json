{"ast": null, "code": "import React, { Component } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'primereact/core';\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n  return _assertThisInitialized(self);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar CarouselItem = /*#__PURE__*/function (_Component) {\n  _inherits(CarouselItem, _Component);\n  var _super = _createSuper(CarouselItem);\n  function CarouselItem() {\n    _classCallCheck(this, CarouselItem);\n    return _super.apply(this, arguments);\n  }\n  _createClass(CarouselItem, [{\n    key: \"render\",\n    value: function render() {\n      var content = this.props.template(this.props.item);\n      var itemClassName = classNames(this.props.className, 'p-carousel-item', {\n        'p-carousel-item-active': this.props.active,\n        'p-carousel-item-start': this.props.start,\n        'p-carousel-item-end': this.props.end\n      });\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: itemClassName\n      }, content);\n    }\n  }]);\n  return CarouselItem;\n}(Component);\n_defineProperty(CarouselItem, \"defaultProps\", {\n  template: null,\n  item: null,\n  active: false,\n  start: false,\n  end: false,\n  className: null\n});\nvar Carousel = /*#__PURE__*/function (_Component2) {\n  _inherits(Carousel, _Component2);\n  var _super2 = _createSuper(Carousel);\n  function Carousel(props) {\n    var _this;\n    _classCallCheck(this, Carousel);\n    _this = _super2.call(this, props);\n    _this.state = {\n      numVisible: props.numVisible,\n      numScroll: props.numScroll,\n      totalShiftedItems: props.page * props.numScroll * -1\n    };\n    if (!_this.props.onPageChange) {\n      _this.state = _objectSpread(_objectSpread({}, _this.state), {}, {\n        page: props.page\n      });\n    }\n    _this.navBackward = _this.navBackward.bind(_assertThisInitialized(_this));\n    _this.navForward = _this.navForward.bind(_assertThisInitialized(_this));\n    _this.onTransitionEnd = _this.onTransitionEnd.bind(_assertThisInitialized(_this));\n    _this.onTouchStart = _this.onTouchStart.bind(_assertThisInitialized(_this));\n    _this.onTouchMove = _this.onTouchMove.bind(_assertThisInitialized(_this));\n    _this.onTouchEnd = _this.onTouchEnd.bind(_assertThisInitialized(_this));\n    _this.totalIndicators = 0;\n    _this.remainingItems = 0;\n    _this.allowAutoplay = !!_this.props.autoplayInterval;\n    _this.circular = _this.props.circular || _this.allowAutoplay;\n    _this.attributeSelector = UniqueComponentId();\n    _this.swipeThreshold = 20;\n    return _this;\n  }\n  _createClass(Carousel, [{\n    key: \"step\",\n    value: function step(dir, page) {\n      var totalShiftedItems = this.state.totalShiftedItems;\n      var isCircular = this.isCircular();\n      if (page != null) {\n        totalShiftedItems = this.state.numScroll * page * -1;\n        if (isCircular) {\n          totalShiftedItems -= this.state.numVisible;\n        }\n        this.isRemainingItemsAdded = false;\n      } else {\n        totalShiftedItems += this.state.numScroll * dir;\n        if (this.isRemainingItemsAdded) {\n          totalShiftedItems += this.remainingItems - this.state.numScroll * dir;\n          this.isRemainingItemsAdded = false;\n        }\n        var originalShiftedItems = isCircular ? totalShiftedItems + this.state.numVisible : totalShiftedItems;\n        page = Math.abs(Math.floor(originalShiftedItems / this.state.numScroll));\n      }\n      if (isCircular && this.state.page === this.totalIndicators - 1 && dir === -1) {\n        totalShiftedItems = -1 * (this.props.value.length + this.state.numVisible);\n        page = 0;\n      } else if (isCircular && this.state.page === 0 && dir === 1) {\n        totalShiftedItems = 0;\n        page = this.totalIndicators - 1;\n      } else if (page === this.totalIndicators - 1 && this.remainingItems > 0) {\n        totalShiftedItems += this.remainingItems * -1 - this.state.numScroll * dir;\n        this.isRemainingItemsAdded = true;\n      }\n      if (this.itemsContainer) {\n        DomHandler.removeClass(this.itemsContainer, 'p-items-hidden');\n        this.changePosition(totalShiftedItems);\n        this.itemsContainer.style.transition = 'transform 500ms ease 0s';\n      }\n      if (this.props.onPageChange) {\n        this.setState({\n          totalShiftedItems: totalShiftedItems\n        });\n        this.props.onPageChange({\n          page: page\n        });\n      } else {\n        this.setState({\n          page: page,\n          totalShiftedItems: totalShiftedItems\n        });\n      }\n    }\n  }, {\n    key: \"calculatePosition\",\n    value: function calculatePosition() {\n      if (this.itemsContainer && this.responsiveOptions) {\n        var windowWidth = window.innerWidth;\n        var matchedResponsiveData = {\n          numVisible: this.props.numVisible,\n          numScroll: this.props.numScroll\n        };\n        for (var i = 0; i < this.responsiveOptions.length; i++) {\n          var res = this.responsiveOptions[i];\n          if (parseInt(res.breakpoint, 10) >= windowWidth) {\n            matchedResponsiveData = res;\n          }\n        }\n        var state = {};\n        if (this.state.numScroll !== matchedResponsiveData.numScroll) {\n          var page = this.getPage();\n          page = Math.floor(page * this.state.numScroll / matchedResponsiveData.numScroll);\n          var totalShiftedItems = matchedResponsiveData.numScroll * page * -1;\n          if (this.isCircular()) {\n            totalShiftedItems -= matchedResponsiveData.numVisible;\n          }\n          state = {\n            totalShiftedItems: totalShiftedItems,\n            numScroll: matchedResponsiveData.numScroll\n          };\n          if (this.props.onPageChange) {\n            this.props.onPageChange({\n              page: page\n            });\n          } else {\n            state = _objectSpread(_objectSpread({}, state), {}, {\n              page: page\n            });\n          }\n        }\n        if (this.state.numVisible !== matchedResponsiveData.numVisible) {\n          state = _objectSpread(_objectSpread({}, state), {}, {\n            numVisible: matchedResponsiveData.numVisible\n          });\n        }\n        if (Object.keys(state).length) {\n          this.setState(state);\n        }\n      }\n    }\n  }, {\n    key: \"navBackward\",\n    value: function navBackward(e, page) {\n      if (this.circular || this.getPage() !== 0) {\n        this.step(1, page);\n      }\n      this.allowAutoplay = false;\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n  }, {\n    key: \"navForward\",\n    value: function navForward(e, page) {\n      if (this.circular || this.getPage() < this.totalIndicators - 1) {\n        this.step(-1, page);\n      }\n      this.allowAutoplay = false;\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n  }, {\n    key: \"onDotClick\",\n    value: function onDotClick(e, page) {\n      var currentPage = this.getPage();\n      if (page > currentPage) {\n        this.navForward(e, page);\n      } else if (page < currentPage) {\n        this.navBackward(e, page);\n      }\n    }\n  }, {\n    key: \"onTransitionEnd\",\n    value: function onTransitionEnd() {\n      if (this.itemsContainer) {\n        DomHandler.addClass(this.itemsContainer, 'p-items-hidden');\n        this.itemsContainer.style.transition = '';\n        if ((this.state.page === 0 || this.state.page === this.totalIndicators - 1) && this.isCircular()) {\n          this.changePosition(this.state.totalShiftedItems);\n        }\n      }\n    }\n  }, {\n    key: \"onTouchStart\",\n    value: function onTouchStart(e) {\n      var touchobj = e.changedTouches[0];\n      this.startPos = {\n        x: touchobj.pageX,\n        y: touchobj.pageY\n      };\n    }\n  }, {\n    key: \"onTouchMove\",\n    value: function onTouchMove(e) {\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n  }, {\n    key: \"onTouchEnd\",\n    value: function onTouchEnd(e) {\n      var touchobj = e.changedTouches[0];\n      if (this.isVertical()) {\n        this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n      } else {\n        this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n      }\n    }\n  }, {\n    key: \"changePageOnTouch\",\n    value: function changePageOnTouch(e, diff) {\n      if (Math.abs(diff) > this.swipeThreshold) {\n        if (diff < 0) {\n          // left\n          this.navForward(e);\n        } else {\n          // right\n          this.navBackward(e);\n        }\n      }\n    }\n  }, {\n    key: \"bindDocumentListeners\",\n    value: function bindDocumentListeners() {\n      var _this2 = this;\n      if (!this.documentResizeListener) {\n        this.documentResizeListener = function () {\n          _this2.calculatePosition();\n        };\n        window.addEventListener('resize', this.documentResizeListener);\n      }\n    }\n  }, {\n    key: \"unbindDocumentListeners\",\n    value: function unbindDocumentListeners() {\n      if (this.documentResizeListener) {\n        window.removeEventListener('resize', this.documentResizeListener);\n        this.documentResizeListener = null;\n      }\n    }\n  }, {\n    key: \"isVertical\",\n    value: function isVertical() {\n      return this.props.orientation === 'vertical';\n    }\n  }, {\n    key: \"isCircular\",\n    value: function isCircular() {\n      return this.circular && this.props.value.length >= this.state.numVisible;\n    }\n  }, {\n    key: \"getPage\",\n    value: function getPage() {\n      return this.props.onPageChange ? this.props.page : this.state.page;\n    }\n  }, {\n    key: \"getTotalIndicators\",\n    value: function getTotalIndicators() {\n      return this.props.value ? Math.ceil((this.props.value.length - this.state.numVisible) / this.state.numScroll) + 1 : 0;\n    }\n  }, {\n    key: \"isAutoplay\",\n    value: function isAutoplay() {\n      return this.props.autoplayInterval && this.allowAutoplay;\n    }\n  }, {\n    key: \"startAutoplay\",\n    value: function startAutoplay() {\n      var _this3 = this;\n      this.interval = setInterval(function () {\n        if (_this3.state.page === _this3.totalIndicators - 1) {\n          _this3.step(-1, 0);\n        } else {\n          _this3.step(-1, _this3.state.page + 1);\n        }\n      }, this.props.autoplayInterval);\n    }\n  }, {\n    key: \"stopAutoplay\",\n    value: function stopAutoplay() {\n      if (this.interval) {\n        clearInterval(this.interval);\n      }\n    }\n  }, {\n    key: \"createStyle\",\n    value: function createStyle() {\n      if (!this.carouselStyle) {\n        this.carouselStyle = document.createElement('style');\n        document.body.appendChild(this.carouselStyle);\n      }\n      var innerHTML = \"\\n            .p-carousel[\".concat(this.attributeSelector, \"] .p-carousel-item {\\n                flex: 1 0 \").concat(100 / this.state.numVisible, \"%\\n            }\\n        \");\n      if (this.props.responsiveOptions) {\n        this.responsiveOptions = _toConsumableArray(this.props.responsiveOptions);\n        this.responsiveOptions.sort(function (data1, data2) {\n          var value1 = data1.breakpoint;\n          var value2 = data2.breakpoint;\n          var result = null;\n          if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n            numeric: true\n          });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n          return -1 * result;\n        });\n        for (var i = 0; i < this.responsiveOptions.length; i++) {\n          var res = this.responsiveOptions[i];\n          innerHTML += \"\\n                    @media screen and (max-width: \".concat(res.breakpoint, \") {\\n                        .p-carousel[\").concat(this.attributeSelector, \"] .p-carousel-item {\\n                            flex: 1 0 \").concat(100 / res.numVisible, \"%\\n                        }\\n                    }\\n                \");\n        }\n      }\n      this.carouselStyle.innerHTML = innerHTML;\n    }\n  }, {\n    key: \"changePosition\",\n    value: function changePosition(totalShiftedItems) {\n      if (this.itemsContainer) {\n        this.itemsContainer.style.transform = this.isVertical() ? \"translate3d(0, \".concat(totalShiftedItems * (100 / this.state.numVisible), \"%, 0)\") : \"translate3d(\".concat(totalShiftedItems * (100 / this.state.numVisible), \"%, 0, 0)\");\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.container) {\n        this.container.setAttribute(this.attributeSelector, '');\n      }\n      this.createStyle();\n      this.calculatePosition();\n      this.changePosition(this.state.totalShiftedItems);\n      if (this.props.responsiveOptions) {\n        this.bindDocumentListeners();\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      var isCircular = this.isCircular();\n      var stateChanged = false;\n      var totalShiftedItems = this.state.totalShiftedItems;\n      if (this.props.autoplayInterval) {\n        this.stopAutoplay();\n      }\n      if (prevState.numScroll !== this.state.numScroll || prevState.numVisible !== this.state.numVisible || this.props.value && prevProps.value && prevProps.value.length !== this.props.value.length) {\n        this.remainingItems = (this.props.value.length - this.state.numVisible) % this.state.numScroll;\n        var page = this.getPage();\n        if (this.totalIndicators !== 0 && page >= this.totalIndicators) {\n          page = this.totalIndicators - 1;\n          if (this.props.onPageChange) {\n            this.props.onPageChange({\n              page: page\n            });\n          } else {\n            this.setState({\n              page: page\n            });\n          }\n          stateChanged = true;\n        }\n        totalShiftedItems = page * this.state.numScroll * -1;\n        if (isCircular) {\n          totalShiftedItems -= this.state.numVisible;\n        }\n        if (page === this.totalIndicators - 1 && this.remainingItems > 0) {\n          totalShiftedItems += -1 * this.remainingItems + this.state.numScroll;\n          this.isRemainingItemsAdded = true;\n        } else {\n          this.isRemainingItemsAdded = false;\n        }\n        if (totalShiftedItems !== this.state.totalShiftedItems) {\n          this.setState({\n            totalShiftedItems: totalShiftedItems\n          });\n          stateChanged = true;\n        }\n        this.changePosition(totalShiftedItems);\n      }\n      if (isCircular) {\n        if (this.state.page === 0) {\n          totalShiftedItems = -1 * this.state.numVisible;\n        } else if (totalShiftedItems === 0) {\n          totalShiftedItems = -1 * this.props.value.length;\n          if (this.remainingItems > 0) {\n            this.isRemainingItemsAdded = true;\n          }\n        }\n        if (totalShiftedItems !== this.state.totalShiftedItems) {\n          this.setState({\n            totalShiftedItems: totalShiftedItems\n          });\n          stateChanged = true;\n        }\n      }\n      if (prevProps.page !== this.props.page) {\n        if (this.props.page > prevProps.page && this.props.page <= this.totalIndicators - 1) {\n          this.step(-1, this.props.page);\n        } else if (this.props.page < prevProps.page) {\n          this.step(1, this.props.page);\n        }\n      }\n      if (!stateChanged && this.isAutoplay()) {\n        this.startAutoplay();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.props.responsiveOptions) {\n        this.unbindDocumentListeners();\n      }\n      if (this.props.autoplayInterval) {\n        this.stopAutoplay();\n      }\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this4 = this;\n      if (this.props.value && this.props.value.length) {\n        var isCircular = this.isCircular();\n        var clonedItemsForStarting = null;\n        var clonedItemsForFinishing = null;\n        if (isCircular) {\n          var clonedElements = null;\n          clonedElements = this.props.value.slice(-1 * this.state.numVisible);\n          clonedItemsForStarting = clonedElements.map(function (item, index) {\n            var isActive = _this4.state.totalShiftedItems * -1 === _this4.props.value.length + _this4.state.numVisible,\n              start = index === 0,\n              end = index === clonedElements.length - 1;\n            return /*#__PURE__*/React.createElement(CarouselItem, {\n              key: index + '_scloned',\n              className: \"p-carousel-item-cloned\",\n              template: _this4.props.itemTemplate,\n              item: item,\n              active: isActive,\n              start: start,\n              end: end\n            });\n          });\n          clonedElements = this.props.value.slice(0, this.state.numVisible);\n          clonedItemsForFinishing = clonedElements.map(function (item, index) {\n            var isActive = _this4.state.totalShiftedItems === 0,\n              start = index === 0,\n              end = index === clonedElements.length - 1;\n            return /*#__PURE__*/React.createElement(CarouselItem, {\n              key: index + '_fcloned',\n              className: \"p-carousel-item-cloned\",\n              template: _this4.props.itemTemplate,\n              item: item,\n              active: isActive,\n              start: start,\n              end: end\n            });\n          });\n        }\n        var items = this.props.value.map(function (item, index) {\n          var firstIndex = isCircular ? -1 * (_this4.state.totalShiftedItems + _this4.state.numVisible) : _this4.state.totalShiftedItems * -1,\n            lastIndex = firstIndex + _this4.state.numVisible - 1,\n            isActive = firstIndex <= index && lastIndex >= index,\n            start = firstIndex === index,\n            end = lastIndex === index;\n          return /*#__PURE__*/React.createElement(CarouselItem, {\n            key: index,\n            template: _this4.props.itemTemplate,\n            item: item,\n            active: isActive,\n            start: start,\n            end: end\n          });\n        });\n        return /*#__PURE__*/React.createElement(React.Fragment, null, clonedItemsForStarting, items, clonedItemsForFinishing);\n      }\n    }\n  }, {\n    key: \"renderHeader\",\n    value: function renderHeader() {\n      if (this.props.header) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-carousel-header\"\n        }, this.props.header);\n      }\n      return null;\n    }\n  }, {\n    key: \"renderFooter\",\n    value: function renderFooter() {\n      if (this.props.footer) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-carousel-footer\"\n        }, this.props.footer);\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContent\",\n    value: function renderContent() {\n      var _this5 = this;\n      var items = this.renderItems();\n      var height = this.isVertical() ? this.props.verticalViewPortHeight : 'auto';\n      var backwardNavigator = this.renderBackwardNavigator();\n      var forwardNavigator = this.renderForwardNavigator();\n      var containerClassName = classNames('p-carousel-container', this.props.containerClassName);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: containerClassName\n      }, backwardNavigator, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-carousel-items-content\",\n        style: {\n          'height': height\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this5.itemsContainer = el;\n        },\n        className: \"p-carousel-items-container\",\n        onTransitionEnd: this.onTransitionEnd,\n        onTouchStart: this.onTouchStart,\n        onTouchMove: this.onTouchMove,\n        onTouchEnd: this.onTouchEnd\n      }, items)), forwardNavigator);\n    }\n  }, {\n    key: \"renderBackwardNavigator\",\n    value: function renderBackwardNavigator() {\n      var isDisabled = (!this.circular || this.props.value && this.props.value.length < this.state.numVisible) && this.getPage() === 0;\n      var buttonClassName = classNames('p-carousel-prev p-link', {\n          'p-disabled': isDisabled\n        }),\n        iconClassName = classNames('p-carousel-prev-icon pi', {\n          'pi-chevron-left': !this.isVertical(),\n          'pi-chevron-up': this.isVertical()\n        });\n      return /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: buttonClassName,\n        onClick: this.navBackward,\n        disabled: isDisabled\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: iconClassName\n      }), /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  }, {\n    key: \"renderForwardNavigator\",\n    value: function renderForwardNavigator() {\n      var isDisabled = (!this.circular || this.props.value && this.props.value.length < this.state.numVisible) && (this.getPage() === this.totalIndicators - 1 || this.totalIndicators === 0);\n      var buttonClassName = classNames('p-carousel-next p-link', {\n          'p-disabled': isDisabled\n        }),\n        iconClassName = classNames('p-carousel-next-icon pi', {\n          'pi-chevron-right': !this.isVertical(),\n          'pi-chevron-down': this.isVertical()\n        });\n      return /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: buttonClassName,\n        onClick: this.navForward,\n        disabled: isDisabled\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: iconClassName\n      }), /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  }, {\n    key: \"renderIndicator\",\n    value: function renderIndicator(index) {\n      var _this6 = this;\n      var isActive = this.getPage() === index,\n        indicatorItemClassName = classNames('p-carousel-indicator', {\n          'p-highlight': isActive\n        });\n      return /*#__PURE__*/React.createElement(\"li\", {\n        className: indicatorItemClassName,\n        key: 'p-carousel-indicator-' + index\n      }, /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"p-link\",\n        onClick: function onClick(e) {\n          return _this6.onDotClick(e, index);\n        }\n      }, /*#__PURE__*/React.createElement(Ripple, null)));\n    }\n  }, {\n    key: \"renderIndicators\",\n    value: function renderIndicators() {\n      var indicatorsContentClassName = classNames('p-carousel-indicators p-reset', this.props.indicatorsContentClassName);\n      var indicators = [];\n      for (var i = 0; i < this.totalIndicators; i++) {\n        indicators.push(this.renderIndicator(i));\n      }\n      return /*#__PURE__*/React.createElement(\"ul\", {\n        className: indicatorsContentClassName\n      }, indicators);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this7 = this;\n      var className = classNames('p-carousel p-component', {\n        'p-carousel-vertical': this.isVertical(),\n        'p-carousel-horizontal': !this.isVertical()\n      }, this.props.className);\n      var contentClassName = classNames('p-carousel-content', this.props.contentClassName);\n      this.totalIndicators = this.getTotalIndicators();\n      var content = this.renderContent();\n      var indicators = this.renderIndicators();\n      var header = this.renderHeader();\n      var footer = this.renderFooter();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this7.container = el;\n        },\n        id: this.props.id,\n        className: className,\n        style: this.props.style\n      }, header, /*#__PURE__*/React.createElement(\"div\", {\n        className: contentClassName\n      }, content, indicators), footer);\n    }\n  }]);\n  return Carousel;\n}(Component);\n_defineProperty(Carousel, \"defaultProps\", {\n  id: null,\n  value: null,\n  page: 0,\n  header: null,\n  footer: null,\n  style: null,\n  className: null,\n  itemTemplate: null,\n  circular: false,\n  autoplayInterval: 0,\n  numVisible: 1,\n  numScroll: 1,\n  responsiveOptions: null,\n  orientation: \"horizontal\",\n  verticalViewPortHeight: \"300px\",\n  contentClassName: null,\n  containerClassName: null,\n  indicatorsContentClassName: null,\n  onPageChange: null\n});\nexport { Carousel };", "map": {"version": 3, "names": ["React", "Component", "UniqueComponentId", "<PERSON><PERSON><PERSON><PERSON>", "classNames", "<PERSON><PERSON><PERSON>", "_arrayLikeToArray", "arr", "len", "length", "i", "arr2", "Array", "_arrayWithoutHoles", "isArray", "_iterableToArray", "iter", "Symbol", "iterator", "from", "_unsupportedIterableToArray", "o", "minLen", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "test", "_nonIterableSpread", "TypeError", "_toConsumableArray", "_assertThisInitialized", "self", "ReferenceError", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_typeof", "obj", "_possibleConstructorReturn", "_getPrototypeOf", "getPrototypeOf", "_defineProperty", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread", "arguments", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "e", "CarouselItem", "_Component", "_super", "render", "content", "template", "item", "itemClassName", "className", "active", "start", "end", "createElement", "Carousel", "_Component2", "_super2", "_this", "state", "numVisible", "numScroll", "totalShiftedItems", "page", "onPageChange", "navBackward", "bind", "navForward", "onTransitionEnd", "onTouchStart", "onTouchMove", "onTouchEnd", "totalIndicators", "remainingItems", "allowAutoplay", "autoplayInterval", "circular", "attributeSelector", "swipe<PERSON><PERSON><PERSON><PERSON>", "step", "dir", "isCircular", "isRemainingItemsAdded", "originalShiftedItems", "Math", "abs", "floor", "itemsContainer", "removeClass", "changePosition", "style", "transition", "setState", "calculatePosition", "responsiveOptions", "windowWidth", "window", "innerWidth", "matchedResponsiveData", "res", "parseInt", "breakpoint", "getPage", "cancelable", "preventDefault", "onDotClick", "currentPage", "addClass", "<PERSON><PERSON><PERSON>", "changedTouches", "startPos", "x", "pageX", "y", "pageY", "isVertical", "changePageOnTouch", "diff", "bindDocumentListeners", "_this2", "documentResizeListener", "addEventListener", "unbindDocumentListeners", "removeEventListener", "orientation", "getTotalIndicators", "ceil", "isAutoplay", "startAutoplay", "_this3", "interval", "setInterval", "stopAutoplay", "clearInterval", "createStyle", "carouselStyle", "document", "body", "append<PERSON><PERSON><PERSON>", "innerHTML", "concat", "sort", "data1", "data2", "value1", "value2", "localeCompare", "undefined", "numeric", "transform", "componentDidMount", "container", "setAttribute", "componentDidUpdate", "prevProps", "prevState", "stateChanged", "componentWillUnmount", "renderItems", "_this4", "clonedItemsForStarting", "clonedItemsForFinishing", "clonedElements", "map", "index", "isActive", "itemTemplate", "items", "firstIndex", "lastIndex", "Fragment", "renderHeader", "header", "renderFooter", "footer", "renderContent", "_this5", "height", "verticalViewPortHeight", "backwardNavigator", "renderBackwardNavigator", "forward<PERSON><PERSON><PERSON><PERSON>", "renderForwardNavigator", "containerClassName", "ref", "el", "isDisabled", "buttonClassName", "iconClassName", "type", "onClick", "disabled", "renderIndicator", "_this6", "indicatorItemClassName", "renderIndicators", "indicatorsContentClassName", "indicators", "_this7", "contentClassName", "id"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/primereact/carousel/carousel.esm.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'primereact/core';\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nvar CarouselItem = /*#__PURE__*/function (_Component) {\n  _inherits(CarouselItem, _Component);\n\n  var _super = _createSuper(CarouselItem);\n\n  function CarouselItem() {\n    _classCallCheck(this, CarouselItem);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(CarouselItem, [{\n    key: \"render\",\n    value: function render() {\n      var content = this.props.template(this.props.item);\n      var itemClassName = classNames(this.props.className, 'p-carousel-item', {\n        'p-carousel-item-active': this.props.active,\n        'p-carousel-item-start': this.props.start,\n        'p-carousel-item-end': this.props.end\n      });\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: itemClassName\n      }, content);\n    }\n  }]);\n\n  return CarouselItem;\n}(Component);\n\n_defineProperty(CarouselItem, \"defaultProps\", {\n  template: null,\n  item: null,\n  active: false,\n  start: false,\n  end: false,\n  className: null\n});\n\nvar Carousel = /*#__PURE__*/function (_Component2) {\n  _inherits(Carousel, _Component2);\n\n  var _super2 = _createSuper(Carousel);\n\n  function Carousel(props) {\n    var _this;\n\n    _classCallCheck(this, Carousel);\n\n    _this = _super2.call(this, props);\n    _this.state = {\n      numVisible: props.numVisible,\n      numScroll: props.numScroll,\n      totalShiftedItems: props.page * props.numScroll * -1\n    };\n\n    if (!_this.props.onPageChange) {\n      _this.state = _objectSpread(_objectSpread({}, _this.state), {}, {\n        page: props.page\n      });\n    }\n\n    _this.navBackward = _this.navBackward.bind(_assertThisInitialized(_this));\n    _this.navForward = _this.navForward.bind(_assertThisInitialized(_this));\n    _this.onTransitionEnd = _this.onTransitionEnd.bind(_assertThisInitialized(_this));\n    _this.onTouchStart = _this.onTouchStart.bind(_assertThisInitialized(_this));\n    _this.onTouchMove = _this.onTouchMove.bind(_assertThisInitialized(_this));\n    _this.onTouchEnd = _this.onTouchEnd.bind(_assertThisInitialized(_this));\n    _this.totalIndicators = 0;\n    _this.remainingItems = 0;\n    _this.allowAutoplay = !!_this.props.autoplayInterval;\n    _this.circular = _this.props.circular || _this.allowAutoplay;\n    _this.attributeSelector = UniqueComponentId();\n    _this.swipeThreshold = 20;\n    return _this;\n  }\n\n  _createClass(Carousel, [{\n    key: \"step\",\n    value: function step(dir, page) {\n      var totalShiftedItems = this.state.totalShiftedItems;\n      var isCircular = this.isCircular();\n\n      if (page != null) {\n        totalShiftedItems = this.state.numScroll * page * -1;\n\n        if (isCircular) {\n          totalShiftedItems -= this.state.numVisible;\n        }\n\n        this.isRemainingItemsAdded = false;\n      } else {\n        totalShiftedItems += this.state.numScroll * dir;\n\n        if (this.isRemainingItemsAdded) {\n          totalShiftedItems += this.remainingItems - this.state.numScroll * dir;\n          this.isRemainingItemsAdded = false;\n        }\n\n        var originalShiftedItems = isCircular ? totalShiftedItems + this.state.numVisible : totalShiftedItems;\n        page = Math.abs(Math.floor(originalShiftedItems / this.state.numScroll));\n      }\n\n      if (isCircular && this.state.page === this.totalIndicators - 1 && dir === -1) {\n        totalShiftedItems = -1 * (this.props.value.length + this.state.numVisible);\n        page = 0;\n      } else if (isCircular && this.state.page === 0 && dir === 1) {\n        totalShiftedItems = 0;\n        page = this.totalIndicators - 1;\n      } else if (page === this.totalIndicators - 1 && this.remainingItems > 0) {\n        totalShiftedItems += this.remainingItems * -1 - this.state.numScroll * dir;\n        this.isRemainingItemsAdded = true;\n      }\n\n      if (this.itemsContainer) {\n        DomHandler.removeClass(this.itemsContainer, 'p-items-hidden');\n        this.changePosition(totalShiftedItems);\n        this.itemsContainer.style.transition = 'transform 500ms ease 0s';\n      }\n\n      if (this.props.onPageChange) {\n        this.setState({\n          totalShiftedItems: totalShiftedItems\n        });\n        this.props.onPageChange({\n          page: page\n        });\n      } else {\n        this.setState({\n          page: page,\n          totalShiftedItems: totalShiftedItems\n        });\n      }\n    }\n  }, {\n    key: \"calculatePosition\",\n    value: function calculatePosition() {\n      if (this.itemsContainer && this.responsiveOptions) {\n        var windowWidth = window.innerWidth;\n        var matchedResponsiveData = {\n          numVisible: this.props.numVisible,\n          numScroll: this.props.numScroll\n        };\n\n        for (var i = 0; i < this.responsiveOptions.length; i++) {\n          var res = this.responsiveOptions[i];\n\n          if (parseInt(res.breakpoint, 10) >= windowWidth) {\n            matchedResponsiveData = res;\n          }\n        }\n\n        var state = {};\n\n        if (this.state.numScroll !== matchedResponsiveData.numScroll) {\n          var page = this.getPage();\n          page = Math.floor(page * this.state.numScroll / matchedResponsiveData.numScroll);\n          var totalShiftedItems = matchedResponsiveData.numScroll * page * -1;\n\n          if (this.isCircular()) {\n            totalShiftedItems -= matchedResponsiveData.numVisible;\n          }\n\n          state = {\n            totalShiftedItems: totalShiftedItems,\n            numScroll: matchedResponsiveData.numScroll\n          };\n\n          if (this.props.onPageChange) {\n            this.props.onPageChange({\n              page: page\n            });\n          } else {\n            state = _objectSpread(_objectSpread({}, state), {}, {\n              page: page\n            });\n          }\n        }\n\n        if (this.state.numVisible !== matchedResponsiveData.numVisible) {\n          state = _objectSpread(_objectSpread({}, state), {}, {\n            numVisible: matchedResponsiveData.numVisible\n          });\n        }\n\n        if (Object.keys(state).length) {\n          this.setState(state);\n        }\n      }\n    }\n  }, {\n    key: \"navBackward\",\n    value: function navBackward(e, page) {\n      if (this.circular || this.getPage() !== 0) {\n        this.step(1, page);\n      }\n\n      this.allowAutoplay = false;\n\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n  }, {\n    key: \"navForward\",\n    value: function navForward(e, page) {\n      if (this.circular || this.getPage() < this.totalIndicators - 1) {\n        this.step(-1, page);\n      }\n\n      this.allowAutoplay = false;\n\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n  }, {\n    key: \"onDotClick\",\n    value: function onDotClick(e, page) {\n      var currentPage = this.getPage();\n\n      if (page > currentPage) {\n        this.navForward(e, page);\n      } else if (page < currentPage) {\n        this.navBackward(e, page);\n      }\n    }\n  }, {\n    key: \"onTransitionEnd\",\n    value: function onTransitionEnd() {\n      if (this.itemsContainer) {\n        DomHandler.addClass(this.itemsContainer, 'p-items-hidden');\n        this.itemsContainer.style.transition = '';\n\n        if ((this.state.page === 0 || this.state.page === this.totalIndicators - 1) && this.isCircular()) {\n          this.changePosition(this.state.totalShiftedItems);\n        }\n      }\n    }\n  }, {\n    key: \"onTouchStart\",\n    value: function onTouchStart(e) {\n      var touchobj = e.changedTouches[0];\n      this.startPos = {\n        x: touchobj.pageX,\n        y: touchobj.pageY\n      };\n    }\n  }, {\n    key: \"onTouchMove\",\n    value: function onTouchMove(e) {\n      if (e.cancelable) {\n        e.preventDefault();\n      }\n    }\n  }, {\n    key: \"onTouchEnd\",\n    value: function onTouchEnd(e) {\n      var touchobj = e.changedTouches[0];\n\n      if (this.isVertical()) {\n        this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n      } else {\n        this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n      }\n    }\n  }, {\n    key: \"changePageOnTouch\",\n    value: function changePageOnTouch(e, diff) {\n      if (Math.abs(diff) > this.swipeThreshold) {\n        if (diff < 0) {\n          // left\n          this.navForward(e);\n        } else {\n          // right\n          this.navBackward(e);\n        }\n      }\n    }\n  }, {\n    key: \"bindDocumentListeners\",\n    value: function bindDocumentListeners() {\n      var _this2 = this;\n\n      if (!this.documentResizeListener) {\n        this.documentResizeListener = function () {\n          _this2.calculatePosition();\n        };\n\n        window.addEventListener('resize', this.documentResizeListener);\n      }\n    }\n  }, {\n    key: \"unbindDocumentListeners\",\n    value: function unbindDocumentListeners() {\n      if (this.documentResizeListener) {\n        window.removeEventListener('resize', this.documentResizeListener);\n        this.documentResizeListener = null;\n      }\n    }\n  }, {\n    key: \"isVertical\",\n    value: function isVertical() {\n      return this.props.orientation === 'vertical';\n    }\n  }, {\n    key: \"isCircular\",\n    value: function isCircular() {\n      return this.circular && this.props.value.length >= this.state.numVisible;\n    }\n  }, {\n    key: \"getPage\",\n    value: function getPage() {\n      return this.props.onPageChange ? this.props.page : this.state.page;\n    }\n  }, {\n    key: \"getTotalIndicators\",\n    value: function getTotalIndicators() {\n      return this.props.value ? Math.ceil((this.props.value.length - this.state.numVisible) / this.state.numScroll) + 1 : 0;\n    }\n  }, {\n    key: \"isAutoplay\",\n    value: function isAutoplay() {\n      return this.props.autoplayInterval && this.allowAutoplay;\n    }\n  }, {\n    key: \"startAutoplay\",\n    value: function startAutoplay() {\n      var _this3 = this;\n\n      this.interval = setInterval(function () {\n        if (_this3.state.page === _this3.totalIndicators - 1) {\n          _this3.step(-1, 0);\n        } else {\n          _this3.step(-1, _this3.state.page + 1);\n        }\n      }, this.props.autoplayInterval);\n    }\n  }, {\n    key: \"stopAutoplay\",\n    value: function stopAutoplay() {\n      if (this.interval) {\n        clearInterval(this.interval);\n      }\n    }\n  }, {\n    key: \"createStyle\",\n    value: function createStyle() {\n      if (!this.carouselStyle) {\n        this.carouselStyle = document.createElement('style');\n        document.body.appendChild(this.carouselStyle);\n      }\n\n      var innerHTML = \"\\n            .p-carousel[\".concat(this.attributeSelector, \"] .p-carousel-item {\\n                flex: 1 0 \").concat(100 / this.state.numVisible, \"%\\n            }\\n        \");\n\n      if (this.props.responsiveOptions) {\n        this.responsiveOptions = _toConsumableArray(this.props.responsiveOptions);\n        this.responsiveOptions.sort(function (data1, data2) {\n          var value1 = data1.breakpoint;\n          var value2 = data2.breakpoint;\n          var result = null;\n          if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n            numeric: true\n          });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n          return -1 * result;\n        });\n\n        for (var i = 0; i < this.responsiveOptions.length; i++) {\n          var res = this.responsiveOptions[i];\n          innerHTML += \"\\n                    @media screen and (max-width: \".concat(res.breakpoint, \") {\\n                        .p-carousel[\").concat(this.attributeSelector, \"] .p-carousel-item {\\n                            flex: 1 0 \").concat(100 / res.numVisible, \"%\\n                        }\\n                    }\\n                \");\n        }\n      }\n\n      this.carouselStyle.innerHTML = innerHTML;\n    }\n  }, {\n    key: \"changePosition\",\n    value: function changePosition(totalShiftedItems) {\n      if (this.itemsContainer) {\n        this.itemsContainer.style.transform = this.isVertical() ? \"translate3d(0, \".concat(totalShiftedItems * (100 / this.state.numVisible), \"%, 0)\") : \"translate3d(\".concat(totalShiftedItems * (100 / this.state.numVisible), \"%, 0, 0)\");\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.container) {\n        this.container.setAttribute(this.attributeSelector, '');\n      }\n\n      this.createStyle();\n      this.calculatePosition();\n      this.changePosition(this.state.totalShiftedItems);\n\n      if (this.props.responsiveOptions) {\n        this.bindDocumentListeners();\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      var isCircular = this.isCircular();\n      var stateChanged = false;\n      var totalShiftedItems = this.state.totalShiftedItems;\n\n      if (this.props.autoplayInterval) {\n        this.stopAutoplay();\n      }\n\n      if (prevState.numScroll !== this.state.numScroll || prevState.numVisible !== this.state.numVisible || this.props.value && prevProps.value && prevProps.value.length !== this.props.value.length) {\n        this.remainingItems = (this.props.value.length - this.state.numVisible) % this.state.numScroll;\n        var page = this.getPage();\n\n        if (this.totalIndicators !== 0 && page >= this.totalIndicators) {\n          page = this.totalIndicators - 1;\n\n          if (this.props.onPageChange) {\n            this.props.onPageChange({\n              page: page\n            });\n          } else {\n            this.setState({\n              page: page\n            });\n          }\n\n          stateChanged = true;\n        }\n\n        totalShiftedItems = page * this.state.numScroll * -1;\n\n        if (isCircular) {\n          totalShiftedItems -= this.state.numVisible;\n        }\n\n        if (page === this.totalIndicators - 1 && this.remainingItems > 0) {\n          totalShiftedItems += -1 * this.remainingItems + this.state.numScroll;\n          this.isRemainingItemsAdded = true;\n        } else {\n          this.isRemainingItemsAdded = false;\n        }\n\n        if (totalShiftedItems !== this.state.totalShiftedItems) {\n          this.setState({\n            totalShiftedItems: totalShiftedItems\n          });\n          stateChanged = true;\n        }\n\n        this.changePosition(totalShiftedItems);\n      }\n\n      if (isCircular) {\n        if (this.state.page === 0) {\n          totalShiftedItems = -1 * this.state.numVisible;\n        } else if (totalShiftedItems === 0) {\n          totalShiftedItems = -1 * this.props.value.length;\n\n          if (this.remainingItems > 0) {\n            this.isRemainingItemsAdded = true;\n          }\n        }\n\n        if (totalShiftedItems !== this.state.totalShiftedItems) {\n          this.setState({\n            totalShiftedItems: totalShiftedItems\n          });\n          stateChanged = true;\n        }\n      }\n\n      if (prevProps.page !== this.props.page) {\n        if (this.props.page > prevProps.page && this.props.page <= this.totalIndicators - 1) {\n          this.step(-1, this.props.page);\n        } else if (this.props.page < prevProps.page) {\n          this.step(1, this.props.page);\n        }\n      }\n\n      if (!stateChanged && this.isAutoplay()) {\n        this.startAutoplay();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.props.responsiveOptions) {\n        this.unbindDocumentListeners();\n      }\n\n      if (this.props.autoplayInterval) {\n        this.stopAutoplay();\n      }\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this4 = this;\n\n      if (this.props.value && this.props.value.length) {\n        var isCircular = this.isCircular();\n        var clonedItemsForStarting = null;\n        var clonedItemsForFinishing = null;\n\n        if (isCircular) {\n          var clonedElements = null;\n          clonedElements = this.props.value.slice(-1 * this.state.numVisible);\n          clonedItemsForStarting = clonedElements.map(function (item, index) {\n            var isActive = _this4.state.totalShiftedItems * -1 === _this4.props.value.length + _this4.state.numVisible,\n                start = index === 0,\n                end = index === clonedElements.length - 1;\n            return /*#__PURE__*/React.createElement(CarouselItem, {\n              key: index + '_scloned',\n              className: \"p-carousel-item-cloned\",\n              template: _this4.props.itemTemplate,\n              item: item,\n              active: isActive,\n              start: start,\n              end: end\n            });\n          });\n          clonedElements = this.props.value.slice(0, this.state.numVisible);\n          clonedItemsForFinishing = clonedElements.map(function (item, index) {\n            var isActive = _this4.state.totalShiftedItems === 0,\n                start = index === 0,\n                end = index === clonedElements.length - 1;\n            return /*#__PURE__*/React.createElement(CarouselItem, {\n              key: index + '_fcloned',\n              className: \"p-carousel-item-cloned\",\n              template: _this4.props.itemTemplate,\n              item: item,\n              active: isActive,\n              start: start,\n              end: end\n            });\n          });\n        }\n\n        var items = this.props.value.map(function (item, index) {\n          var firstIndex = isCircular ? -1 * (_this4.state.totalShiftedItems + _this4.state.numVisible) : _this4.state.totalShiftedItems * -1,\n              lastIndex = firstIndex + _this4.state.numVisible - 1,\n              isActive = firstIndex <= index && lastIndex >= index,\n              start = firstIndex === index,\n              end = lastIndex === index;\n          return /*#__PURE__*/React.createElement(CarouselItem, {\n            key: index,\n            template: _this4.props.itemTemplate,\n            item: item,\n            active: isActive,\n            start: start,\n            end: end\n          });\n        });\n        return /*#__PURE__*/React.createElement(React.Fragment, null, clonedItemsForStarting, items, clonedItemsForFinishing);\n      }\n    }\n  }, {\n    key: \"renderHeader\",\n    value: function renderHeader() {\n      if (this.props.header) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-carousel-header\"\n        }, this.props.header);\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderFooter\",\n    value: function renderFooter() {\n      if (this.props.footer) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-carousel-footer\"\n        }, this.props.footer);\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderContent\",\n    value: function renderContent() {\n      var _this5 = this;\n\n      var items = this.renderItems();\n      var height = this.isVertical() ? this.props.verticalViewPortHeight : 'auto';\n      var backwardNavigator = this.renderBackwardNavigator();\n      var forwardNavigator = this.renderForwardNavigator();\n      var containerClassName = classNames('p-carousel-container', this.props.containerClassName);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: containerClassName\n      }, backwardNavigator, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-carousel-items-content\",\n        style: {\n          'height': height\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this5.itemsContainer = el;\n        },\n        className: \"p-carousel-items-container\",\n        onTransitionEnd: this.onTransitionEnd,\n        onTouchStart: this.onTouchStart,\n        onTouchMove: this.onTouchMove,\n        onTouchEnd: this.onTouchEnd\n      }, items)), forwardNavigator);\n    }\n  }, {\n    key: \"renderBackwardNavigator\",\n    value: function renderBackwardNavigator() {\n      var isDisabled = (!this.circular || this.props.value && this.props.value.length < this.state.numVisible) && this.getPage() === 0;\n      var buttonClassName = classNames('p-carousel-prev p-link', {\n        'p-disabled': isDisabled\n      }),\n          iconClassName = classNames('p-carousel-prev-icon pi', {\n        'pi-chevron-left': !this.isVertical(),\n        'pi-chevron-up': this.isVertical()\n      });\n      return /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: buttonClassName,\n        onClick: this.navBackward,\n        disabled: isDisabled\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: iconClassName\n      }), /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  }, {\n    key: \"renderForwardNavigator\",\n    value: function renderForwardNavigator() {\n      var isDisabled = (!this.circular || this.props.value && this.props.value.length < this.state.numVisible) && (this.getPage() === this.totalIndicators - 1 || this.totalIndicators === 0);\n      var buttonClassName = classNames('p-carousel-next p-link', {\n        'p-disabled': isDisabled\n      }),\n          iconClassName = classNames('p-carousel-next-icon pi', {\n        'pi-chevron-right': !this.isVertical(),\n        'pi-chevron-down': this.isVertical()\n      });\n      return /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: buttonClassName,\n        onClick: this.navForward,\n        disabled: isDisabled\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: iconClassName\n      }), /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  }, {\n    key: \"renderIndicator\",\n    value: function renderIndicator(index) {\n      var _this6 = this;\n\n      var isActive = this.getPage() === index,\n          indicatorItemClassName = classNames('p-carousel-indicator', {\n        'p-highlight': isActive\n      });\n      return /*#__PURE__*/React.createElement(\"li\", {\n        className: indicatorItemClassName,\n        key: 'p-carousel-indicator-' + index\n      }, /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"p-link\",\n        onClick: function onClick(e) {\n          return _this6.onDotClick(e, index);\n        }\n      }, /*#__PURE__*/React.createElement(Ripple, null)));\n    }\n  }, {\n    key: \"renderIndicators\",\n    value: function renderIndicators() {\n      var indicatorsContentClassName = classNames('p-carousel-indicators p-reset', this.props.indicatorsContentClassName);\n      var indicators = [];\n\n      for (var i = 0; i < this.totalIndicators; i++) {\n        indicators.push(this.renderIndicator(i));\n      }\n\n      return /*#__PURE__*/React.createElement(\"ul\", {\n        className: indicatorsContentClassName\n      }, indicators);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this7 = this;\n\n      var className = classNames('p-carousel p-component', {\n        'p-carousel-vertical': this.isVertical(),\n        'p-carousel-horizontal': !this.isVertical()\n      }, this.props.className);\n      var contentClassName = classNames('p-carousel-content', this.props.contentClassName);\n      this.totalIndicators = this.getTotalIndicators();\n      var content = this.renderContent();\n      var indicators = this.renderIndicators();\n      var header = this.renderHeader();\n      var footer = this.renderFooter();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this7.container = el;\n        },\n        id: this.props.id,\n        className: className,\n        style: this.props.style\n      }, header, /*#__PURE__*/React.createElement(\"div\", {\n        className: contentClassName\n      }, content, indicators), footer);\n    }\n  }]);\n\n  return Carousel;\n}(Component);\n\n_defineProperty(Carousel, \"defaultProps\", {\n  id: null,\n  value: null,\n  page: 0,\n  header: null,\n  footer: null,\n  style: null,\n  className: null,\n  itemTemplate: null,\n  circular: false,\n  autoplayInterval: 0,\n  numVisible: 1,\n  numScroll: 1,\n  responsiveOptions: null,\n  orientation: \"horizontal\",\n  verticalViewPortHeight: \"300px\",\n  contentClassName: null,\n  containerClassName: null,\n  indicatorsContentClassName: null,\n  onPageChange: null\n});\n\nexport { Carousel };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,iBAAiB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM,QAAQ,iBAAiB;AAEnF,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAACE,MAAM,EAAED,<PERSON>G,<PERSON>GD,GAAG,CAACE,MAAM;EAErD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IACnDC,IAAI,CAACD,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC;EAClB;EAEA,OAAOC,IAAI;AACb;AAEA,SAASE,kBAAkBA,CAACN,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACE,OAAO,CAACP,GAAG,CAAC,EAAE,OAAOD,iBAAiB,CAACC,GAAG,CAAC;AACvD;AAEA,SAASQ,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIF,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOJ,KAAK,CAACO,IAAI,CAACH,IAAI,CAAC;AAC3H;AAEA,SAASI,2BAA2BA,CAACC,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOf,iBAAiB,CAACe,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIL,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACQ,WAAW,EAAEN,CAAC,GAAGF,CAAC,CAACQ,WAAW,CAACC,IAAI;EAC3D,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOX,KAAK,CAACO,IAAI,CAACE,CAAC,CAAC;EACpD,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOjB,iBAAiB,CAACe,CAAC,EAAEC,MAAM,CAAC;AAClH;AAEA,SAASU,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASC,kBAAkBA,CAAC3B,GAAG,EAAE;EAC/B,OAAOM,kBAAkB,CAACN,GAAG,CAAC,IAAIQ,gBAAgB,CAACR,GAAG,CAAC,IAAIa,2BAA2B,CAACb,GAAG,CAAC,IAAIyB,kBAAkB,CAAC,CAAC;AACrH;AAEA,SAASG,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIP,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASQ,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,KAAK,CAAClC,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAIkC,UAAU,GAAGD,KAAK,CAACjC,CAAC,CAAC;IACzBkC,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDvB,MAAM,CAACwB,cAAc,CAACN,MAAM,EAAEE,UAAU,CAACK,GAAG,EAAEL,UAAU,CAAC;EAC3D;AACF;AAEA,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEV,iBAAiB,CAACD,WAAW,CAACf,SAAS,EAAE0B,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEX,iBAAiB,CAACD,WAAW,EAAEY,WAAW,CAAC;EAC5D,OAAOZ,WAAW;AACpB;AAEA,SAASa,eAAeA,CAAChC,CAAC,EAAEiC,CAAC,EAAE;EAC7BD,eAAe,GAAG7B,MAAM,CAAC+B,cAAc,IAAI,SAASF,eAAeA,CAAChC,CAAC,EAAEiC,CAAC,EAAE;IACxEjC,CAAC,CAACmC,SAAS,GAAGF,CAAC;IACf,OAAOjC,CAAC;EACV,CAAC;EAED,OAAOgC,eAAe,CAAChC,CAAC,EAAEiC,CAAC,CAAC;AAC9B;AAEA,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAyB,QAAQ,CAACjC,SAAS,GAAGD,MAAM,CAACoC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAClC,SAAS,EAAE;IACrEI,WAAW,EAAE;MACXgC,KAAK,EAAEH,QAAQ;MACfX,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAIa,UAAU,EAAEN,eAAe,CAACK,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASG,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAO9C,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvE4C,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAOA,GAAG,IAAI,OAAO9C,MAAM,KAAK,UAAU,IAAI8C,GAAG,CAAClC,WAAW,KAAKZ,MAAM,IAAI8C,GAAG,KAAK9C,MAAM,CAACQ,SAAS,GAAG,QAAQ,GAAG,OAAOsC,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASC,0BAA0BA,CAAC5B,IAAI,EAAET,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAKmC,OAAO,CAACnC,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACtE,OAAOA,IAAI;EACb;EAEA,OAAOQ,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAAS6B,eAAeA,CAAC5C,CAAC,EAAE;EAC1B4C,eAAe,GAAGzC,MAAM,CAAC+B,cAAc,GAAG/B,MAAM,CAAC0C,cAAc,GAAG,SAASD,eAAeA,CAAC5C,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACmC,SAAS,IAAIhC,MAAM,CAAC0C,cAAc,CAAC7C,CAAC,CAAC;EAChD,CAAC;EACD,OAAO4C,eAAe,CAAC5C,CAAC,CAAC;AAC3B;AAEA,SAAS8C,eAAeA,CAACJ,GAAG,EAAEd,GAAG,EAAEY,KAAK,EAAE;EACxC,IAAIZ,GAAG,IAAIc,GAAG,EAAE;IACdvC,MAAM,CAACwB,cAAc,CAACe,GAAG,EAAEd,GAAG,EAAE;MAC9BY,KAAK,EAAEA,KAAK;MACZhB,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLgB,GAAG,CAACd,GAAG,CAAC,GAAGY,KAAK;EAClB;EAEA,OAAOE,GAAG;AACZ;AAEA,SAASK,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAG/C,MAAM,CAAC+C,IAAI,CAACF,MAAM,CAAC;EAAE,IAAI7C,MAAM,CAACgD,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGjD,MAAM,CAACgD,qBAAqB,CAACH,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOnD,MAAM,CAACoD,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAAC9B,UAAU;MAAE,CAAC,CAAC;IAAE;IAAE0B,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAExV,SAASQ,aAAaA,CAACrC,MAAM,EAAE;EAAE,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,SAAS,CAACvE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIuE,MAAM,GAAGD,SAAS,CAACtE,CAAC,CAAC,IAAI,IAAI,GAAGsE,SAAS,CAACtE,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAE0D,OAAO,CAAC5C,MAAM,CAACyD,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUjC,GAAG,EAAE;QAAEkB,eAAe,CAACzB,MAAM,EAAEO,GAAG,EAAEgC,MAAM,CAAChC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIzB,MAAM,CAAC2D,yBAAyB,EAAE;MAAE3D,MAAM,CAAC4D,gBAAgB,CAAC1C,MAAM,EAAElB,MAAM,CAAC2D,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAEb,OAAO,CAAC5C,MAAM,CAACyD,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUjC,GAAG,EAAE;QAAEzB,MAAM,CAACwB,cAAc,CAACN,MAAM,EAAEO,GAAG,EAAEzB,MAAM,CAACoD,wBAAwB,CAACK,MAAM,EAAEhC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAOP,MAAM;AAAE;AAErhB,SAAS2C,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGzB,eAAe,CAACqB,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAG3B,eAAe,CAAC,IAAI,CAAC,CAACpC,WAAW;MAAE8D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEV,SAAS,EAAEY,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAACZ,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;IAAE;IAAE,OAAOhB,0BAA0B,CAAC,IAAI,EAAE2B,MAAM,CAAC;EAAE,CAAC;AAAE;AAExa,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOK,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACxE,SAAS,CAACyE,OAAO,CAACvE,IAAI,CAACkE,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AAExU,IAAIC,YAAY,GAAG,aAAa,UAAUC,UAAU,EAAE;EACpD5C,SAAS,CAAC2C,YAAY,EAAEC,UAAU,CAAC;EAEnC,IAAIC,MAAM,GAAGjB,YAAY,CAACe,YAAY,CAAC;EAEvC,SAASA,YAAYA,CAAA,EAAG;IACtB9D,eAAe,CAAC,IAAI,EAAE8D,YAAY,CAAC;IAEnC,OAAOE,MAAM,CAACxB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;EACtC;EAEA9B,YAAY,CAACkD,YAAY,EAAE,CAAC;IAC1BnD,GAAG,EAAE,QAAQ;IACbY,KAAK,EAAE,SAAS0C,MAAMA,CAAA,EAAG;MACvB,IAAIC,OAAO,GAAG,IAAI,CAAC7D,KAAK,CAAC8D,QAAQ,CAAC,IAAI,CAAC9D,KAAK,CAAC+D,IAAI,CAAC;MAClD,IAAIC,aAAa,GAAGvG,UAAU,CAAC,IAAI,CAACuC,KAAK,CAACiE,SAAS,EAAE,iBAAiB,EAAE;QACtE,wBAAwB,EAAE,IAAI,CAACjE,KAAK,CAACkE,MAAM;QAC3C,uBAAuB,EAAE,IAAI,CAAClE,KAAK,CAACmE,KAAK;QACzC,qBAAqB,EAAE,IAAI,CAACnE,KAAK,CAACoE;MACpC,CAAC,CAAC;MACF,OAAO,aAAa/G,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;QAC7CJ,SAAS,EAAED;MACb,CAAC,EAAEH,OAAO,CAAC;IACb;EACF,CAAC,CAAC,CAAC;EAEH,OAAOJ,YAAY;AACrB,CAAC,CAACnG,SAAS,CAAC;AAEZkE,eAAe,CAACiC,YAAY,EAAE,cAAc,EAAE;EAC5CK,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVG,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,KAAK;EACZC,GAAG,EAAE,KAAK;EACVH,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,IAAIK,QAAQ,GAAG,aAAa,UAAUC,WAAW,EAAE;EACjDzD,SAAS,CAACwD,QAAQ,EAAEC,WAAW,CAAC;EAEhC,IAAIC,OAAO,GAAG9B,YAAY,CAAC4B,QAAQ,CAAC;EAEpC,SAASA,QAAQA,CAACtE,KAAK,EAAE;IACvB,IAAIyE,KAAK;IAET9E,eAAe,CAAC,IAAI,EAAE2E,QAAQ,CAAC;IAE/BG,KAAK,GAAGD,OAAO,CAACxF,IAAI,CAAC,IAAI,EAAEgB,KAAK,CAAC;IACjCyE,KAAK,CAACC,KAAK,GAAG;MACZC,UAAU,EAAE3E,KAAK,CAAC2E,UAAU;MAC5BC,SAAS,EAAE5E,KAAK,CAAC4E,SAAS;MAC1BC,iBAAiB,EAAE7E,KAAK,CAAC8E,IAAI,GAAG9E,KAAK,CAAC4E,SAAS,GAAG,CAAC;IACrD,CAAC;IAED,IAAI,CAACH,KAAK,CAACzE,KAAK,CAAC+E,YAAY,EAAE;MAC7BN,KAAK,CAACC,KAAK,GAAGtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DI,IAAI,EAAE9E,KAAK,CAAC8E;MACd,CAAC,CAAC;IACJ;IAEAL,KAAK,CAACO,WAAW,GAAGP,KAAK,CAACO,WAAW,CAACC,IAAI,CAACzF,sBAAsB,CAACiF,KAAK,CAAC,CAAC;IACzEA,KAAK,CAACS,UAAU,GAAGT,KAAK,CAACS,UAAU,CAACD,IAAI,CAACzF,sBAAsB,CAACiF,KAAK,CAAC,CAAC;IACvEA,KAAK,CAACU,eAAe,GAAGV,KAAK,CAACU,eAAe,CAACF,IAAI,CAACzF,sBAAsB,CAACiF,KAAK,CAAC,CAAC;IACjFA,KAAK,CAACW,YAAY,GAAGX,KAAK,CAACW,YAAY,CAACH,IAAI,CAACzF,sBAAsB,CAACiF,KAAK,CAAC,CAAC;IAC3EA,KAAK,CAACY,WAAW,GAAGZ,KAAK,CAACY,WAAW,CAACJ,IAAI,CAACzF,sBAAsB,CAACiF,KAAK,CAAC,CAAC;IACzEA,KAAK,CAACa,UAAU,GAAGb,KAAK,CAACa,UAAU,CAACL,IAAI,CAACzF,sBAAsB,CAACiF,KAAK,CAAC,CAAC;IACvEA,KAAK,CAACc,eAAe,GAAG,CAAC;IACzBd,KAAK,CAACe,cAAc,GAAG,CAAC;IACxBf,KAAK,CAACgB,aAAa,GAAG,CAAC,CAAChB,KAAK,CAACzE,KAAK,CAAC0F,gBAAgB;IACpDjB,KAAK,CAACkB,QAAQ,GAAGlB,KAAK,CAACzE,KAAK,CAAC2F,QAAQ,IAAIlB,KAAK,CAACgB,aAAa;IAC5DhB,KAAK,CAACmB,iBAAiB,GAAGrI,iBAAiB,CAAC,CAAC;IAC7CkH,KAAK,CAACoB,cAAc,GAAG,EAAE;IACzB,OAAOpB,KAAK;EACd;EAEAlE,YAAY,CAAC+D,QAAQ,EAAE,CAAC;IACtBhE,GAAG,EAAE,MAAM;IACXY,KAAK,EAAE,SAAS4E,IAAIA,CAACC,GAAG,EAAEjB,IAAI,EAAE;MAC9B,IAAID,iBAAiB,GAAG,IAAI,CAACH,KAAK,CAACG,iBAAiB;MACpD,IAAImB,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;MAElC,IAAIlB,IAAI,IAAI,IAAI,EAAE;QAChBD,iBAAiB,GAAG,IAAI,CAACH,KAAK,CAACE,SAAS,GAAGE,IAAI,GAAG,CAAC,CAAC;QAEpD,IAAIkB,UAAU,EAAE;UACdnB,iBAAiB,IAAI,IAAI,CAACH,KAAK,CAACC,UAAU;QAC5C;QAEA,IAAI,CAACsB,qBAAqB,GAAG,KAAK;MACpC,CAAC,MAAM;QACLpB,iBAAiB,IAAI,IAAI,CAACH,KAAK,CAACE,SAAS,GAAGmB,GAAG;QAE/C,IAAI,IAAI,CAACE,qBAAqB,EAAE;UAC9BpB,iBAAiB,IAAI,IAAI,CAACW,cAAc,GAAG,IAAI,CAACd,KAAK,CAACE,SAAS,GAAGmB,GAAG;UACrE,IAAI,CAACE,qBAAqB,GAAG,KAAK;QACpC;QAEA,IAAIC,oBAAoB,GAAGF,UAAU,GAAGnB,iBAAiB,GAAG,IAAI,CAACH,KAAK,CAACC,UAAU,GAAGE,iBAAiB;QACrGC,IAAI,GAAGqB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACH,oBAAoB,GAAG,IAAI,CAACxB,KAAK,CAACE,SAAS,CAAC,CAAC;MAC1E;MAEA,IAAIoB,UAAU,IAAI,IAAI,CAACtB,KAAK,CAACI,IAAI,KAAK,IAAI,CAACS,eAAe,GAAG,CAAC,IAAIQ,GAAG,KAAK,CAAC,CAAC,EAAE;QAC5ElB,iBAAiB,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC7E,KAAK,CAACkB,KAAK,CAACpD,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACC,UAAU,CAAC;QAC1EG,IAAI,GAAG,CAAC;MACV,CAAC,MAAM,IAAIkB,UAAU,IAAI,IAAI,CAACtB,KAAK,CAACI,IAAI,KAAK,CAAC,IAAIiB,GAAG,KAAK,CAAC,EAAE;QAC3DlB,iBAAiB,GAAG,CAAC;QACrBC,IAAI,GAAG,IAAI,CAACS,eAAe,GAAG,CAAC;MACjC,CAAC,MAAM,IAAIT,IAAI,KAAK,IAAI,CAACS,eAAe,GAAG,CAAC,IAAI,IAAI,CAACC,cAAc,GAAG,CAAC,EAAE;QACvEX,iBAAiB,IAAI,IAAI,CAACW,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,CAACE,SAAS,GAAGmB,GAAG;QAC1E,IAAI,CAACE,qBAAqB,GAAG,IAAI;MACnC;MAEA,IAAI,IAAI,CAACK,cAAc,EAAE;QACvB9I,UAAU,CAAC+I,WAAW,CAAC,IAAI,CAACD,cAAc,EAAE,gBAAgB,CAAC;QAC7D,IAAI,CAACE,cAAc,CAAC3B,iBAAiB,CAAC;QACtC,IAAI,CAACyB,cAAc,CAACG,KAAK,CAACC,UAAU,GAAG,yBAAyB;MAClE;MAEA,IAAI,IAAI,CAAC1G,KAAK,CAAC+E,YAAY,EAAE;QAC3B,IAAI,CAAC4B,QAAQ,CAAC;UACZ9B,iBAAiB,EAAEA;QACrB,CAAC,CAAC;QACF,IAAI,CAAC7E,KAAK,CAAC+E,YAAY,CAAC;UACtBD,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC6B,QAAQ,CAAC;UACZ7B,IAAI,EAAEA,IAAI;UACVD,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDvE,GAAG,EAAE,mBAAmB;IACxBY,KAAK,EAAE,SAAS0F,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACN,cAAc,IAAI,IAAI,CAACO,iBAAiB,EAAE;QACjD,IAAIC,WAAW,GAAGC,MAAM,CAACC,UAAU;QACnC,IAAIC,qBAAqB,GAAG;UAC1BtC,UAAU,EAAE,IAAI,CAAC3E,KAAK,CAAC2E,UAAU;UACjCC,SAAS,EAAE,IAAI,CAAC5E,KAAK,CAAC4E;QACxB,CAAC;QAED,KAAK,IAAI7G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8I,iBAAiB,CAAC/I,MAAM,EAAEC,CAAC,EAAE,EAAE;UACtD,IAAImJ,GAAG,GAAG,IAAI,CAACL,iBAAiB,CAAC9I,CAAC,CAAC;UAEnC,IAAIoJ,QAAQ,CAACD,GAAG,CAACE,UAAU,EAAE,EAAE,CAAC,IAAIN,WAAW,EAAE;YAC/CG,qBAAqB,GAAGC,GAAG;UAC7B;QACF;QAEA,IAAIxC,KAAK,GAAG,CAAC,CAAC;QAEd,IAAI,IAAI,CAACA,KAAK,CAACE,SAAS,KAAKqC,qBAAqB,CAACrC,SAAS,EAAE;UAC5D,IAAIE,IAAI,GAAG,IAAI,CAACuC,OAAO,CAAC,CAAC;UACzBvC,IAAI,GAAGqB,IAAI,CAACE,KAAK,CAACvB,IAAI,GAAG,IAAI,CAACJ,KAAK,CAACE,SAAS,GAAGqC,qBAAqB,CAACrC,SAAS,CAAC;UAChF,IAAIC,iBAAiB,GAAGoC,qBAAqB,CAACrC,SAAS,GAAGE,IAAI,GAAG,CAAC,CAAC;UAEnE,IAAI,IAAI,CAACkB,UAAU,CAAC,CAAC,EAAE;YACrBnB,iBAAiB,IAAIoC,qBAAqB,CAACtC,UAAU;UACvD;UAEAD,KAAK,GAAG;YACNG,iBAAiB,EAAEA,iBAAiB;YACpCD,SAAS,EAAEqC,qBAAqB,CAACrC;UACnC,CAAC;UAED,IAAI,IAAI,CAAC5E,KAAK,CAAC+E,YAAY,EAAE;YAC3B,IAAI,CAAC/E,KAAK,CAAC+E,YAAY,CAAC;cACtBD,IAAI,EAAEA;YACR,CAAC,CAAC;UACJ,CAAC,MAAM;YACLJ,KAAK,GAAGtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cAClDI,IAAI,EAAEA;YACR,CAAC,CAAC;UACJ;QACF;QAEA,IAAI,IAAI,CAACJ,KAAK,CAACC,UAAU,KAAKsC,qBAAqB,CAACtC,UAAU,EAAE;UAC9DD,KAAK,GAAGtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YAClDC,UAAU,EAAEsC,qBAAqB,CAACtC;UACpC,CAAC,CAAC;QACJ;QAEA,IAAI9F,MAAM,CAAC+C,IAAI,CAAC8C,KAAK,CAAC,CAAC5G,MAAM,EAAE;UAC7B,IAAI,CAAC6I,QAAQ,CAACjC,KAAK,CAAC;QACtB;MACF;IACF;EACF,CAAC,EAAE;IACDpE,GAAG,EAAE,aAAa;IAClBY,KAAK,EAAE,SAAS8D,WAAWA,CAACxB,CAAC,EAAEsB,IAAI,EAAE;MACnC,IAAI,IAAI,CAACa,QAAQ,IAAI,IAAI,CAAC0B,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;QACzC,IAAI,CAACvB,IAAI,CAAC,CAAC,EAAEhB,IAAI,CAAC;MACpB;MAEA,IAAI,CAACW,aAAa,GAAG,KAAK;MAE1B,IAAIjC,CAAC,CAAC8D,UAAU,EAAE;QAChB9D,CAAC,CAAC+D,cAAc,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDjH,GAAG,EAAE,YAAY;IACjBY,KAAK,EAAE,SAASgE,UAAUA,CAAC1B,CAAC,EAAEsB,IAAI,EAAE;MAClC,IAAI,IAAI,CAACa,QAAQ,IAAI,IAAI,CAAC0B,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC9B,eAAe,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACO,IAAI,CAAC,CAAC,CAAC,EAAEhB,IAAI,CAAC;MACrB;MAEA,IAAI,CAACW,aAAa,GAAG,KAAK;MAE1B,IAAIjC,CAAC,CAAC8D,UAAU,EAAE;QAChB9D,CAAC,CAAC+D,cAAc,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDjH,GAAG,EAAE,YAAY;IACjBY,KAAK,EAAE,SAASsG,UAAUA,CAAChE,CAAC,EAAEsB,IAAI,EAAE;MAClC,IAAI2C,WAAW,GAAG,IAAI,CAACJ,OAAO,CAAC,CAAC;MAEhC,IAAIvC,IAAI,GAAG2C,WAAW,EAAE;QACtB,IAAI,CAACvC,UAAU,CAAC1B,CAAC,EAAEsB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIA,IAAI,GAAG2C,WAAW,EAAE;QAC7B,IAAI,CAACzC,WAAW,CAACxB,CAAC,EAAEsB,IAAI,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACDxE,GAAG,EAAE,iBAAiB;IACtBY,KAAK,EAAE,SAASiE,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAACmB,cAAc,EAAE;QACvB9I,UAAU,CAACkK,QAAQ,CAAC,IAAI,CAACpB,cAAc,EAAE,gBAAgB,CAAC;QAC1D,IAAI,CAACA,cAAc,CAACG,KAAK,CAACC,UAAU,GAAG,EAAE;QAEzC,IAAI,CAAC,IAAI,CAAChC,KAAK,CAACI,IAAI,KAAK,CAAC,IAAI,IAAI,CAACJ,KAAK,CAACI,IAAI,KAAK,IAAI,CAACS,eAAe,GAAG,CAAC,KAAK,IAAI,CAACS,UAAU,CAAC,CAAC,EAAE;UAChG,IAAI,CAACQ,cAAc,CAAC,IAAI,CAAC9B,KAAK,CAACG,iBAAiB,CAAC;QACnD;MACF;IACF;EACF,CAAC,EAAE;IACDvE,GAAG,EAAE,cAAc;IACnBY,KAAK,EAAE,SAASkE,YAAYA,CAAC5B,CAAC,EAAE;MAC9B,IAAImE,QAAQ,GAAGnE,CAAC,CAACoE,cAAc,CAAC,CAAC,CAAC;MAClC,IAAI,CAACC,QAAQ,GAAG;QACdC,CAAC,EAAEH,QAAQ,CAACI,KAAK;QACjBC,CAAC,EAAEL,QAAQ,CAACM;MACd,CAAC;IACH;EACF,CAAC,EAAE;IACD3H,GAAG,EAAE,aAAa;IAClBY,KAAK,EAAE,SAASmE,WAAWA,CAAC7B,CAAC,EAAE;MAC7B,IAAIA,CAAC,CAAC8D,UAAU,EAAE;QAChB9D,CAAC,CAAC+D,cAAc,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDjH,GAAG,EAAE,YAAY;IACjBY,KAAK,EAAE,SAASoE,UAAUA,CAAC9B,CAAC,EAAE;MAC5B,IAAImE,QAAQ,GAAGnE,CAAC,CAACoE,cAAc,CAAC,CAAC,CAAC;MAElC,IAAI,IAAI,CAACM,UAAU,CAAC,CAAC,EAAE;QACrB,IAAI,CAACC,iBAAiB,CAAC3E,CAAC,EAAEmE,QAAQ,CAACM,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAACG,CAAC,CAAC;MAC7D,CAAC,MAAM;QACL,IAAI,CAACG,iBAAiB,CAAC3E,CAAC,EAAEmE,QAAQ,CAACI,KAAK,GAAG,IAAI,CAACF,QAAQ,CAACC,CAAC,CAAC;MAC7D;IACF;EACF,CAAC,EAAE;IACDxH,GAAG,EAAE,mBAAmB;IACxBY,KAAK,EAAE,SAASiH,iBAAiBA,CAAC3E,CAAC,EAAE4E,IAAI,EAAE;MACzC,IAAIjC,IAAI,CAACC,GAAG,CAACgC,IAAI,CAAC,GAAG,IAAI,CAACvC,cAAc,EAAE;QACxC,IAAIuC,IAAI,GAAG,CAAC,EAAE;UACZ;UACA,IAAI,CAAClD,UAAU,CAAC1B,CAAC,CAAC;QACpB,CAAC,MAAM;UACL;UACA,IAAI,CAACwB,WAAW,CAACxB,CAAC,CAAC;QACrB;MACF;IACF;EACF,CAAC,EAAE;IACDlD,GAAG,EAAE,uBAAuB;IAC5BY,KAAK,EAAE,SAASmH,qBAAqBA,CAAA,EAAG;MACtC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACC,sBAAsB,EAAE;QAChC,IAAI,CAACA,sBAAsB,GAAG,YAAY;UACxCD,MAAM,CAAC1B,iBAAiB,CAAC,CAAC;QAC5B,CAAC;QAEDG,MAAM,CAACyB,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACD,sBAAsB,CAAC;MAChE;IACF;EACF,CAAC,EAAE;IACDjI,GAAG,EAAE,yBAAyB;IAC9BY,KAAK,EAAE,SAASuH,uBAAuBA,CAAA,EAAG;MACxC,IAAI,IAAI,CAACF,sBAAsB,EAAE;QAC/BxB,MAAM,CAAC2B,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACH,sBAAsB,CAAC;QACjE,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACpC;IACF;EACF,CAAC,EAAE;IACDjI,GAAG,EAAE,YAAY;IACjBY,KAAK,EAAE,SAASgH,UAAUA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAAClI,KAAK,CAAC2I,WAAW,KAAK,UAAU;IAC9C;EACF,CAAC,EAAE;IACDrI,GAAG,EAAE,YAAY;IACjBY,KAAK,EAAE,SAAS8E,UAAUA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAACL,QAAQ,IAAI,IAAI,CAAC3F,KAAK,CAACkB,KAAK,CAACpD,MAAM,IAAI,IAAI,CAAC4G,KAAK,CAACC,UAAU;IAC1E;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,SAAS;IACdY,KAAK,EAAE,SAASmG,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACrH,KAAK,CAAC+E,YAAY,GAAG,IAAI,CAAC/E,KAAK,CAAC8E,IAAI,GAAG,IAAI,CAACJ,KAAK,CAACI,IAAI;IACpE;EACF,CAAC,EAAE;IACDxE,GAAG,EAAE,oBAAoB;IACzBY,KAAK,EAAE,SAAS0H,kBAAkBA,CAAA,EAAG;MACnC,OAAO,IAAI,CAAC5I,KAAK,CAACkB,KAAK,GAAGiF,IAAI,CAAC0C,IAAI,CAAC,CAAC,IAAI,CAAC7I,KAAK,CAACkB,KAAK,CAACpD,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC;IACvH;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,YAAY;IACjBY,KAAK,EAAE,SAAS4H,UAAUA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAAC9I,KAAK,CAAC0F,gBAAgB,IAAI,IAAI,CAACD,aAAa;IAC1D;EACF,CAAC,EAAE;IACDnF,GAAG,EAAE,eAAe;IACpBY,KAAK,EAAE,SAAS6H,aAAaA,CAAA,EAAG;MAC9B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACC,QAAQ,GAAGC,WAAW,CAAC,YAAY;QACtC,IAAIF,MAAM,CAACtE,KAAK,CAACI,IAAI,KAAKkE,MAAM,CAACzD,eAAe,GAAG,CAAC,EAAE;UACpDyD,MAAM,CAAClD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,MAAM;UACLkD,MAAM,CAAClD,IAAI,CAAC,CAAC,CAAC,EAAEkD,MAAM,CAACtE,KAAK,CAACI,IAAI,GAAG,CAAC,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC9E,KAAK,CAAC0F,gBAAgB,CAAC;IACjC;EACF,CAAC,EAAE;IACDpF,GAAG,EAAE,cAAc;IACnBY,KAAK,EAAE,SAASiI,YAAYA,CAAA,EAAG;MAC7B,IAAI,IAAI,CAACF,QAAQ,EAAE;QACjBG,aAAa,CAAC,IAAI,CAACH,QAAQ,CAAC;MAC9B;IACF;EACF,CAAC,EAAE;IACD3I,GAAG,EAAE,aAAa;IAClBY,KAAK,EAAE,SAASmI,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;QACvB,IAAI,CAACA,aAAa,GAAGC,QAAQ,CAAClF,aAAa,CAAC,OAAO,CAAC;QACpDkF,QAAQ,CAACC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACH,aAAa,CAAC;MAC/C;MAEA,IAAII,SAAS,GAAG,4BAA4B,CAACC,MAAM,CAAC,IAAI,CAAC/D,iBAAiB,EAAE,kDAAkD,CAAC,CAAC+D,MAAM,CAAC,GAAG,GAAG,IAAI,CAACjF,KAAK,CAACC,UAAU,EAAE,4BAA4B,CAAC;MAEjM,IAAI,IAAI,CAAC3E,KAAK,CAAC6G,iBAAiB,EAAE;QAChC,IAAI,CAACA,iBAAiB,GAAGtH,kBAAkB,CAAC,IAAI,CAACS,KAAK,CAAC6G,iBAAiB,CAAC;QACzE,IAAI,CAACA,iBAAiB,CAAC+C,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;UAClD,IAAIC,MAAM,GAAGF,KAAK,CAACzC,UAAU;UAC7B,IAAI4C,MAAM,GAAGF,KAAK,CAAC1C,UAAU;UAC7B,IAAIpE,MAAM,GAAG,IAAI;UACjB,IAAI+G,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAEhH,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI+G,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAEhH,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI+G,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAEhH,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,OAAO+G,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAEhH,MAAM,GAAG+G,MAAM,CAACE,aAAa,CAACD,MAAM,EAAEE,SAAS,EAAE;YAChRC,OAAO,EAAE;UACX,CAAC,CAAC,CAAC,KAAKnH,MAAM,GAAG+G,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;UAC/D,OAAO,CAAC,CAAC,GAAGhH,MAAM;QACpB,CAAC,CAAC;QAEF,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8I,iBAAiB,CAAC/I,MAAM,EAAEC,CAAC,EAAE,EAAE;UACtD,IAAImJ,GAAG,GAAG,IAAI,CAACL,iBAAiB,CAAC9I,CAAC,CAAC;UACnC2L,SAAS,IAAI,sDAAsD,CAACC,MAAM,CAACzC,GAAG,CAACE,UAAU,EAAE,2CAA2C,CAAC,CAACuC,MAAM,CAAC,IAAI,CAAC/D,iBAAiB,EAAE,8DAA8D,CAAC,CAAC+D,MAAM,CAAC,GAAG,GAAGzC,GAAG,CAACvC,UAAU,EAAE,uEAAuE,CAAC;QAC9U;MACF;MAEA,IAAI,CAAC2E,aAAa,CAACI,SAAS,GAAGA,SAAS;IAC1C;EACF,CAAC,EAAE;IACDpJ,GAAG,EAAE,gBAAgB;IACrBY,KAAK,EAAE,SAASsF,cAAcA,CAAC3B,iBAAiB,EAAE;MAChD,IAAI,IAAI,CAACyB,cAAc,EAAE;QACvB,IAAI,CAACA,cAAc,CAACG,KAAK,CAAC2D,SAAS,GAAG,IAAI,CAAClC,UAAU,CAAC,CAAC,GAAG,iBAAiB,CAACyB,MAAM,CAAC9E,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACH,KAAK,CAACC,UAAU,CAAC,EAAE,OAAO,CAAC,GAAG,cAAc,CAACgF,MAAM,CAAC9E,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACH,KAAK,CAACC,UAAU,CAAC,EAAE,UAAU,CAAC;MACvO;IACF;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,mBAAmB;IACxBY,KAAK,EAAE,SAASmJ,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACC,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,YAAY,CAAC,IAAI,CAAC3E,iBAAiB,EAAE,EAAE,CAAC;MACzD;MAEA,IAAI,CAACyD,WAAW,CAAC,CAAC;MAClB,IAAI,CAACzC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACJ,cAAc,CAAC,IAAI,CAAC9B,KAAK,CAACG,iBAAiB,CAAC;MAEjD,IAAI,IAAI,CAAC7E,KAAK,CAAC6G,iBAAiB,EAAE;QAChC,IAAI,CAACwB,qBAAqB,CAAC,CAAC;MAC9B;IACF;EACF,CAAC,EAAE;IACD/H,GAAG,EAAE,oBAAoB;IACzBY,KAAK,EAAE,SAASsJ,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MACvD,IAAI1E,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;MAClC,IAAI2E,YAAY,GAAG,KAAK;MACxB,IAAI9F,iBAAiB,GAAG,IAAI,CAACH,KAAK,CAACG,iBAAiB;MAEpD,IAAI,IAAI,CAAC7E,KAAK,CAAC0F,gBAAgB,EAAE;QAC/B,IAAI,CAACyD,YAAY,CAAC,CAAC;MACrB;MAEA,IAAIuB,SAAS,CAAC9F,SAAS,KAAK,IAAI,CAACF,KAAK,CAACE,SAAS,IAAI8F,SAAS,CAAC/F,UAAU,KAAK,IAAI,CAACD,KAAK,CAACC,UAAU,IAAI,IAAI,CAAC3E,KAAK,CAACkB,KAAK,IAAIuJ,SAAS,CAACvJ,KAAK,IAAIuJ,SAAS,CAACvJ,KAAK,CAACpD,MAAM,KAAK,IAAI,CAACkC,KAAK,CAACkB,KAAK,CAACpD,MAAM,EAAE;QAC/L,IAAI,CAAC0H,cAAc,GAAG,CAAC,IAAI,CAACxF,KAAK,CAACkB,KAAK,CAACpD,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,SAAS;QAC9F,IAAIE,IAAI,GAAG,IAAI,CAACuC,OAAO,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC9B,eAAe,KAAK,CAAC,IAAIT,IAAI,IAAI,IAAI,CAACS,eAAe,EAAE;UAC9DT,IAAI,GAAG,IAAI,CAACS,eAAe,GAAG,CAAC;UAE/B,IAAI,IAAI,CAACvF,KAAK,CAAC+E,YAAY,EAAE;YAC3B,IAAI,CAAC/E,KAAK,CAAC+E,YAAY,CAAC;cACtBD,IAAI,EAAEA;YACR,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAAC6B,QAAQ,CAAC;cACZ7B,IAAI,EAAEA;YACR,CAAC,CAAC;UACJ;UAEA6F,YAAY,GAAG,IAAI;QACrB;QAEA9F,iBAAiB,GAAGC,IAAI,GAAG,IAAI,CAACJ,KAAK,CAACE,SAAS,GAAG,CAAC,CAAC;QAEpD,IAAIoB,UAAU,EAAE;UACdnB,iBAAiB,IAAI,IAAI,CAACH,KAAK,CAACC,UAAU;QAC5C;QAEA,IAAIG,IAAI,KAAK,IAAI,CAACS,eAAe,GAAG,CAAC,IAAI,IAAI,CAACC,cAAc,GAAG,CAAC,EAAE;UAChEX,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAI,CAACW,cAAc,GAAG,IAAI,CAACd,KAAK,CAACE,SAAS;UACpE,IAAI,CAACqB,qBAAqB,GAAG,IAAI;QACnC,CAAC,MAAM;UACL,IAAI,CAACA,qBAAqB,GAAG,KAAK;QACpC;QAEA,IAAIpB,iBAAiB,KAAK,IAAI,CAACH,KAAK,CAACG,iBAAiB,EAAE;UACtD,IAAI,CAAC8B,QAAQ,CAAC;YACZ9B,iBAAiB,EAAEA;UACrB,CAAC,CAAC;UACF8F,YAAY,GAAG,IAAI;QACrB;QAEA,IAAI,CAACnE,cAAc,CAAC3B,iBAAiB,CAAC;MACxC;MAEA,IAAImB,UAAU,EAAE;QACd,IAAI,IAAI,CAACtB,KAAK,CAACI,IAAI,KAAK,CAAC,EAAE;UACzBD,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAACH,KAAK,CAACC,UAAU;QAChD,CAAC,MAAM,IAAIE,iBAAiB,KAAK,CAAC,EAAE;UAClCA,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC7E,KAAK,CAACkB,KAAK,CAACpD,MAAM;UAEhD,IAAI,IAAI,CAAC0H,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAACS,qBAAqB,GAAG,IAAI;UACnC;QACF;QAEA,IAAIpB,iBAAiB,KAAK,IAAI,CAACH,KAAK,CAACG,iBAAiB,EAAE;UACtD,IAAI,CAAC8B,QAAQ,CAAC;YACZ9B,iBAAiB,EAAEA;UACrB,CAAC,CAAC;UACF8F,YAAY,GAAG,IAAI;QACrB;MACF;MAEA,IAAIF,SAAS,CAAC3F,IAAI,KAAK,IAAI,CAAC9E,KAAK,CAAC8E,IAAI,EAAE;QACtC,IAAI,IAAI,CAAC9E,KAAK,CAAC8E,IAAI,GAAG2F,SAAS,CAAC3F,IAAI,IAAI,IAAI,CAAC9E,KAAK,CAAC8E,IAAI,IAAI,IAAI,CAACS,eAAe,GAAG,CAAC,EAAE;UACnF,IAAI,CAACO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC9F,KAAK,CAAC8E,IAAI,CAAC;QAChC,CAAC,MAAM,IAAI,IAAI,CAAC9E,KAAK,CAAC8E,IAAI,GAAG2F,SAAS,CAAC3F,IAAI,EAAE;UAC3C,IAAI,CAACgB,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC9F,KAAK,CAAC8E,IAAI,CAAC;QAC/B;MACF;MAEA,IAAI,CAAC6F,YAAY,IAAI,IAAI,CAAC7B,UAAU,CAAC,CAAC,EAAE;QACtC,IAAI,CAACC,aAAa,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE;IACDzI,GAAG,EAAE,sBAAsB;IAC3BY,KAAK,EAAE,SAAS0J,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAAC5K,KAAK,CAAC6G,iBAAiB,EAAE;QAChC,IAAI,CAAC4B,uBAAuB,CAAC,CAAC;MAChC;MAEA,IAAI,IAAI,CAACzI,KAAK,CAAC0F,gBAAgB,EAAE;QAC/B,IAAI,CAACyD,YAAY,CAAC,CAAC;MACrB;IACF;EACF,CAAC,EAAE;IACD7I,GAAG,EAAE,aAAa;IAClBY,KAAK,EAAE,SAAS2J,WAAWA,CAAA,EAAG;MAC5B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAC9K,KAAK,CAACkB,KAAK,IAAI,IAAI,CAAClB,KAAK,CAACkB,KAAK,CAACpD,MAAM,EAAE;QAC/C,IAAIkI,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;QAClC,IAAI+E,sBAAsB,GAAG,IAAI;QACjC,IAAIC,uBAAuB,GAAG,IAAI;QAElC,IAAIhF,UAAU,EAAE;UACd,IAAIiF,cAAc,GAAG,IAAI;UACzBA,cAAc,GAAG,IAAI,CAACjL,KAAK,CAACkB,KAAK,CAACjC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACyF,KAAK,CAACC,UAAU,CAAC;UACnEoG,sBAAsB,GAAGE,cAAc,CAACC,GAAG,CAAC,UAAUnH,IAAI,EAAEoH,KAAK,EAAE;YACjE,IAAIC,QAAQ,GAAGN,MAAM,CAACpG,KAAK,CAACG,iBAAiB,GAAG,CAAC,CAAC,KAAKiG,MAAM,CAAC9K,KAAK,CAACkB,KAAK,CAACpD,MAAM,GAAGgN,MAAM,CAACpG,KAAK,CAACC,UAAU;cACtGR,KAAK,GAAGgH,KAAK,KAAK,CAAC;cACnB/G,GAAG,GAAG+G,KAAK,KAAKF,cAAc,CAACnN,MAAM,GAAG,CAAC;YAC7C,OAAO,aAAaT,KAAK,CAACgH,aAAa,CAACZ,YAAY,EAAE;cACpDnD,GAAG,EAAE6K,KAAK,GAAG,UAAU;cACvBlH,SAAS,EAAE,wBAAwB;cACnCH,QAAQ,EAAEgH,MAAM,CAAC9K,KAAK,CAACqL,YAAY;cACnCtH,IAAI,EAAEA,IAAI;cACVG,MAAM,EAAEkH,QAAQ;cAChBjH,KAAK,EAAEA,KAAK;cACZC,GAAG,EAAEA;YACP,CAAC,CAAC;UACJ,CAAC,CAAC;UACF6G,cAAc,GAAG,IAAI,CAACjL,KAAK,CAACkB,KAAK,CAACjC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACyF,KAAK,CAACC,UAAU,CAAC;UACjEqG,uBAAuB,GAAGC,cAAc,CAACC,GAAG,CAAC,UAAUnH,IAAI,EAAEoH,KAAK,EAAE;YAClE,IAAIC,QAAQ,GAAGN,MAAM,CAACpG,KAAK,CAACG,iBAAiB,KAAK,CAAC;cAC/CV,KAAK,GAAGgH,KAAK,KAAK,CAAC;cACnB/G,GAAG,GAAG+G,KAAK,KAAKF,cAAc,CAACnN,MAAM,GAAG,CAAC;YAC7C,OAAO,aAAaT,KAAK,CAACgH,aAAa,CAACZ,YAAY,EAAE;cACpDnD,GAAG,EAAE6K,KAAK,GAAG,UAAU;cACvBlH,SAAS,EAAE,wBAAwB;cACnCH,QAAQ,EAAEgH,MAAM,CAAC9K,KAAK,CAACqL,YAAY;cACnCtH,IAAI,EAAEA,IAAI;cACVG,MAAM,EAAEkH,QAAQ;cAChBjH,KAAK,EAAEA,KAAK;cACZC,GAAG,EAAEA;YACP,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;QAEA,IAAIkH,KAAK,GAAG,IAAI,CAACtL,KAAK,CAACkB,KAAK,CAACgK,GAAG,CAAC,UAAUnH,IAAI,EAAEoH,KAAK,EAAE;UACtD,IAAII,UAAU,GAAGvF,UAAU,GAAG,CAAC,CAAC,IAAI8E,MAAM,CAACpG,KAAK,CAACG,iBAAiB,GAAGiG,MAAM,CAACpG,KAAK,CAACC,UAAU,CAAC,GAAGmG,MAAM,CAACpG,KAAK,CAACG,iBAAiB,GAAG,CAAC,CAAC;YAC/H2G,SAAS,GAAGD,UAAU,GAAGT,MAAM,CAACpG,KAAK,CAACC,UAAU,GAAG,CAAC;YACpDyG,QAAQ,GAAGG,UAAU,IAAIJ,KAAK,IAAIK,SAAS,IAAIL,KAAK;YACpDhH,KAAK,GAAGoH,UAAU,KAAKJ,KAAK;YAC5B/G,GAAG,GAAGoH,SAAS,KAAKL,KAAK;UAC7B,OAAO,aAAa9N,KAAK,CAACgH,aAAa,CAACZ,YAAY,EAAE;YACpDnD,GAAG,EAAE6K,KAAK;YACVrH,QAAQ,EAAEgH,MAAM,CAAC9K,KAAK,CAACqL,YAAY;YACnCtH,IAAI,EAAEA,IAAI;YACVG,MAAM,EAAEkH,QAAQ;YAChBjH,KAAK,EAAEA,KAAK;YACZC,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAO,aAAa/G,KAAK,CAACgH,aAAa,CAAChH,KAAK,CAACoO,QAAQ,EAAE,IAAI,EAAEV,sBAAsB,EAAEO,KAAK,EAAEN,uBAAuB,CAAC;MACvH;IACF;EACF,CAAC,EAAE;IACD1K,GAAG,EAAE,cAAc;IACnBY,KAAK,EAAE,SAASwK,YAAYA,CAAA,EAAG;MAC7B,IAAI,IAAI,CAAC1L,KAAK,CAAC2L,MAAM,EAAE;QACrB,OAAO,aAAatO,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;UAC7CJ,SAAS,EAAE;QACb,CAAC,EAAE,IAAI,CAACjE,KAAK,CAAC2L,MAAM,CAAC;MACvB;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDrL,GAAG,EAAE,cAAc;IACnBY,KAAK,EAAE,SAAS0K,YAAYA,CAAA,EAAG;MAC7B,IAAI,IAAI,CAAC5L,KAAK,CAAC6L,MAAM,EAAE;QACrB,OAAO,aAAaxO,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;UAC7CJ,SAAS,EAAE;QACb,CAAC,EAAE,IAAI,CAACjE,KAAK,CAAC6L,MAAM,CAAC;MACvB;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDvL,GAAG,EAAE,eAAe;IACpBY,KAAK,EAAE,SAAS4K,aAAaA,CAAA,EAAG;MAC9B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIT,KAAK,GAAG,IAAI,CAACT,WAAW,CAAC,CAAC;MAC9B,IAAImB,MAAM,GAAG,IAAI,CAAC9D,UAAU,CAAC,CAAC,GAAG,IAAI,CAAClI,KAAK,CAACiM,sBAAsB,GAAG,MAAM;MAC3E,IAAIC,iBAAiB,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MACtD,IAAIC,gBAAgB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;MACpD,IAAIC,kBAAkB,GAAG7O,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAACuC,KAAK,CAACsM,kBAAkB,CAAC;MAC1F,OAAO,aAAajP,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;QAC7CJ,SAAS,EAAEqI;MACb,CAAC,EAAEJ,iBAAiB,EAAE,aAAa7O,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;QAC5DJ,SAAS,EAAE,0BAA0B;QACrCwC,KAAK,EAAE;UACL,QAAQ,EAAEuF;QACZ;MACF,CAAC,EAAE,aAAa3O,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;QACzCkI,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOT,MAAM,CAACzF,cAAc,GAAGkG,EAAE;QACnC,CAAC;QACDvI,SAAS,EAAE,4BAA4B;QACvCkB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,UAAU,EAAE,IAAI,CAACA;MACnB,CAAC,EAAEgG,KAAK,CAAC,CAAC,EAAEc,gBAAgB,CAAC;IAC/B;EACF,CAAC,EAAE;IACD9L,GAAG,EAAE,yBAAyB;IAC9BY,KAAK,EAAE,SAASiL,uBAAuBA,CAAA,EAAG;MACxC,IAAIM,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC9G,QAAQ,IAAI,IAAI,CAAC3F,KAAK,CAACkB,KAAK,IAAI,IAAI,CAAClB,KAAK,CAACkB,KAAK,CAACpD,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACC,UAAU,KAAK,IAAI,CAAC0C,OAAO,CAAC,CAAC,KAAK,CAAC;MAChI,IAAIqF,eAAe,GAAGjP,UAAU,CAAC,wBAAwB,EAAE;UACzD,YAAY,EAAEgP;QAChB,CAAC,CAAC;QACEE,aAAa,GAAGlP,UAAU,CAAC,yBAAyB,EAAE;UACxD,iBAAiB,EAAE,CAAC,IAAI,CAACyK,UAAU,CAAC,CAAC;UACrC,eAAe,EAAE,IAAI,CAACA,UAAU,CAAC;QACnC,CAAC,CAAC;MACF,OAAO,aAAa7K,KAAK,CAACgH,aAAa,CAAC,QAAQ,EAAE;QAChDuI,IAAI,EAAE,QAAQ;QACd3I,SAAS,EAAEyI,eAAe;QAC1BG,OAAO,EAAE,IAAI,CAAC7H,WAAW;QACzB8H,QAAQ,EAAEL;MACZ,CAAC,EAAE,aAAapP,KAAK,CAACgH,aAAa,CAAC,MAAM,EAAE;QAC1CJ,SAAS,EAAE0I;MACb,CAAC,CAAC,EAAE,aAAatP,KAAK,CAACgH,aAAa,CAAC3G,MAAM,EAAE,IAAI,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACD4C,GAAG,EAAE,wBAAwB;IAC7BY,KAAK,EAAE,SAASmL,sBAAsBA,CAAA,EAAG;MACvC,IAAII,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC9G,QAAQ,IAAI,IAAI,CAAC3F,KAAK,CAACkB,KAAK,IAAI,IAAI,CAAClB,KAAK,CAACkB,KAAK,CAACpD,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACC,UAAU,MAAM,IAAI,CAAC0C,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC9B,eAAe,GAAG,CAAC,IAAI,IAAI,CAACA,eAAe,KAAK,CAAC,CAAC;MACvL,IAAImH,eAAe,GAAGjP,UAAU,CAAC,wBAAwB,EAAE;UACzD,YAAY,EAAEgP;QAChB,CAAC,CAAC;QACEE,aAAa,GAAGlP,UAAU,CAAC,yBAAyB,EAAE;UACxD,kBAAkB,EAAE,CAAC,IAAI,CAACyK,UAAU,CAAC,CAAC;UACtC,iBAAiB,EAAE,IAAI,CAACA,UAAU,CAAC;QACrC,CAAC,CAAC;MACF,OAAO,aAAa7K,KAAK,CAACgH,aAAa,CAAC,QAAQ,EAAE;QAChDuI,IAAI,EAAE,QAAQ;QACd3I,SAAS,EAAEyI,eAAe;QAC1BG,OAAO,EAAE,IAAI,CAAC3H,UAAU;QACxB4H,QAAQ,EAAEL;MACZ,CAAC,EAAE,aAAapP,KAAK,CAACgH,aAAa,CAAC,MAAM,EAAE;QAC1CJ,SAAS,EAAE0I;MACb,CAAC,CAAC,EAAE,aAAatP,KAAK,CAACgH,aAAa,CAAC3G,MAAM,EAAE,IAAI,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACD4C,GAAG,EAAE,iBAAiB;IACtBY,KAAK,EAAE,SAAS6L,eAAeA,CAAC5B,KAAK,EAAE;MACrC,IAAI6B,MAAM,GAAG,IAAI;MAEjB,IAAI5B,QAAQ,GAAG,IAAI,CAAC/D,OAAO,CAAC,CAAC,KAAK8D,KAAK;QACnC8B,sBAAsB,GAAGxP,UAAU,CAAC,sBAAsB,EAAE;UAC9D,aAAa,EAAE2N;QACjB,CAAC,CAAC;MACF,OAAO,aAAa/N,KAAK,CAACgH,aAAa,CAAC,IAAI,EAAE;QAC5CJ,SAAS,EAAEgJ,sBAAsB;QACjC3M,GAAG,EAAE,uBAAuB,GAAG6K;MACjC,CAAC,EAAE,aAAa9N,KAAK,CAACgH,aAAa,CAAC,QAAQ,EAAE;QAC5CuI,IAAI,EAAE,QAAQ;QACd3I,SAAS,EAAE,QAAQ;QACnB4I,OAAO,EAAE,SAASA,OAAOA,CAACrJ,CAAC,EAAE;UAC3B,OAAOwJ,MAAM,CAACxF,UAAU,CAAChE,CAAC,EAAE2H,KAAK,CAAC;QACpC;MACF,CAAC,EAAE,aAAa9N,KAAK,CAACgH,aAAa,CAAC3G,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACD4C,GAAG,EAAE,kBAAkB;IACvBY,KAAK,EAAE,SAASgM,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,0BAA0B,GAAG1P,UAAU,CAAC,+BAA+B,EAAE,IAAI,CAACuC,KAAK,CAACmN,0BAA0B,CAAC;MACnH,IAAIC,UAAU,GAAG,EAAE;MAEnB,KAAK,IAAIrP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACwH,eAAe,EAAExH,CAAC,EAAE,EAAE;QAC7CqP,UAAU,CAAClL,IAAI,CAAC,IAAI,CAAC6K,eAAe,CAAChP,CAAC,CAAC,CAAC;MAC1C;MAEA,OAAO,aAAaV,KAAK,CAACgH,aAAa,CAAC,IAAI,EAAE;QAC5CJ,SAAS,EAAEkJ;MACb,CAAC,EAAEC,UAAU,CAAC;IAChB;EACF,CAAC,EAAE;IACD9M,GAAG,EAAE,QAAQ;IACbY,KAAK,EAAE,SAAS0C,MAAMA,CAAA,EAAG;MACvB,IAAIyJ,MAAM,GAAG,IAAI;MAEjB,IAAIpJ,SAAS,GAAGxG,UAAU,CAAC,wBAAwB,EAAE;QACnD,qBAAqB,EAAE,IAAI,CAACyK,UAAU,CAAC,CAAC;QACxC,uBAAuB,EAAE,CAAC,IAAI,CAACA,UAAU,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAClI,KAAK,CAACiE,SAAS,CAAC;MACxB,IAAIqJ,gBAAgB,GAAG7P,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAACuC,KAAK,CAACsN,gBAAgB,CAAC;MACpF,IAAI,CAAC/H,eAAe,GAAG,IAAI,CAACqD,kBAAkB,CAAC,CAAC;MAChD,IAAI/E,OAAO,GAAG,IAAI,CAACiI,aAAa,CAAC,CAAC;MAClC,IAAIsB,UAAU,GAAG,IAAI,CAACF,gBAAgB,CAAC,CAAC;MACxC,IAAIvB,MAAM,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MAChC,IAAIG,MAAM,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MAChC,OAAO,aAAavO,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;QAC7CkI,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOa,MAAM,CAAC/C,SAAS,GAAGkC,EAAE;QAC9B,CAAC;QACDe,EAAE,EAAE,IAAI,CAACvN,KAAK,CAACuN,EAAE;QACjBtJ,SAAS,EAAEA,SAAS;QACpBwC,KAAK,EAAE,IAAI,CAACzG,KAAK,CAACyG;MACpB,CAAC,EAAEkF,MAAM,EAAE,aAAatO,KAAK,CAACgH,aAAa,CAAC,KAAK,EAAE;QACjDJ,SAAS,EAAEqJ;MACb,CAAC,EAAEzJ,OAAO,EAAEuJ,UAAU,CAAC,EAAEvB,MAAM,CAAC;IAClC;EACF,CAAC,CAAC,CAAC;EAEH,OAAOvH,QAAQ;AACjB,CAAC,CAAChH,SAAS,CAAC;AAEZkE,eAAe,CAAC8C,QAAQ,EAAE,cAAc,EAAE;EACxCiJ,EAAE,EAAE,IAAI;EACRrM,KAAK,EAAE,IAAI;EACX4D,IAAI,EAAE,CAAC;EACP6G,MAAM,EAAE,IAAI;EACZE,MAAM,EAAE,IAAI;EACZpF,KAAK,EAAE,IAAI;EACXxC,SAAS,EAAE,IAAI;EACfoH,YAAY,EAAE,IAAI;EAClB1F,QAAQ,EAAE,KAAK;EACfD,gBAAgB,EAAE,CAAC;EACnBf,UAAU,EAAE,CAAC;EACbC,SAAS,EAAE,CAAC;EACZiC,iBAAiB,EAAE,IAAI;EACvB8B,WAAW,EAAE,YAAY;EACzBsD,sBAAsB,EAAE,OAAO;EAC/BqB,gBAAgB,EAAE,IAAI;EACtBhB,kBAAkB,EAAE,IAAI;EACxBa,0BAA0B,EAAE,IAAI;EAChCpI,YAAY,EAAE;AAChB,CAAC,CAAC;AAEF,SAAST,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}