{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\ufficioVendite.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioVendite - operazioni sull'ufficio vendite\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Sidebar } from 'primereact/sidebar';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport '../../css/DataTableDemo.css';\nimport { chainConfrontoDispQta } from '../../components/route';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DocumentiVendite extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      var url = 'documents?idWarehouses=' + e.value + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.onRowSelect = result => {\n      if (result.tasks !== null && result.status !== 'prepared') {\n        var idFOROrd = [];\n        this.state.results.forEach(element => {\n          if (element.id === result.id) {\n            idFOROrd = element;\n          }\n        });\n        localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n        window.location.pathname = chainConfrontoDispQta;\n      } else {\n        this.toast.show({\n          severity: \"warn\",\n          summary: \"Attenzione\",\n          detail: \"Verificare se è presente una task per questo documento e che la stessa non sia in stato prepared prima di procedere\",\n          life: 3000\n        });\n      }\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      selectedWarehouse: null,\n      selectedDocuments: null,\n      loading: true,\n      displayed: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      totalRecords: 0,\n      search: '',\n      value: null,\n      value2: null,\n      clienti: null,\n      param: '?idWarehouses=',\n      param2: '&idRetailer=',\n      selectedRetailer: null,\n      deleteResultDialog: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.retailers = [];\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.creaDDT = this.creaDDT.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.creaFORORDINE = this.creaFORORDINE.bind(this);\n    this.onRowSelect = this.onRowSelect.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse;\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'retailers/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.retailers.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    await APIRequest(\"GET\", \"employees?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var task = [];\n    var documentBody = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    var respMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'OP_MAG' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n          opMag.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n          respMag.push({\n            label: element.first_name + ' ' + element.last_name,\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog3: true,\n      opMag: opMag,\n      respMag: respMag,\n      mex: message\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedRetailer: null,\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: ''\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedRetailer: null,\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: ''\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            idRetailer: element.idRetailer,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  async creaDDT(result) {\n    if (result.tasks !== null) {\n      var _result$idDocumentHea;\n      let url = '';\n      let tipi = [];\n      if (((_result$idDocumentHea = result.idDocumentHeadOrig) === null || _result$idDocumentHea === void 0 ? void 0 : _result$idDocumentHea.length) > 0) {\n        for (var x = 0; x < ((_result$idDocumentHea2 = result.idDocumentHeadOrig) === null || _result$idDocumentHea2 === void 0 ? void 0 : _result$idDocumentHea2.length); x++) {\n          var _result$idDocumentHea2;\n          url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest;\n          await APIRequest(\"GET\", url).then(res => {\n            console.log(res.data);\n            tipi.push(res.data.type);\n          }).catch(e => {\n            console.log(e);\n          });\n        }\n        var find = tipi.find(el => el === 'CLI-DDT');\n        if (find !== undefined) {\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Il documento è già associato ad una task di tipo CLI-DDT\",\n            life: 3000\n          });\n        } else {\n          url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\";\n          //Chiamata axios per la creazione del documento\n          await APIRequest('POST', url).then(res => {\n            console.log(res.data);\n            this.toast.show({\n              severity: 'success',\n              summary: 'Ottimo',\n              detail: \"Il documento è stato inserito con successo\",\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          }).catch(e => {\n            var _e$response19, _e$response20;\n            console.log(e);\n            this.toast.show({\n              severity: 'error',\n              summary: 'Siamo spiacenti',\n              detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n              life: 3000\n            });\n          });\n        }\n      } else {\n        url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\";\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', url).then(res => {\n          console.log(res.data);\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il documento è stato inserito con successo\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response21, _e$response22;\n          console.log(e);\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response21 = e.response) === null || _e$response21 === void 0 ? void 0 : _e$response21.data) !== undefined ? (_e$response22 = e.response) === null || _e$response22 === void 0 ? void 0 : _e$response22.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\",\n        life: 3000\n      });\n    }\n  }\n  async creaFORORDINE(result) {\n    if (result.tasks !== null) {\n      var _result$idDocumentHea3;\n      let url = '';\n      let tipi = [];\n      if (((_result$idDocumentHea3 = result.idDocumentHeadOrig) === null || _result$idDocumentHea3 === void 0 ? void 0 : _result$idDocumentHea3.length) > 0) {\n        for (var x = 0; x < ((_result$idDocumentHea4 = result.idDocumentHeadOrig) === null || _result$idDocumentHea4 === void 0 ? void 0 : _result$idDocumentHea4.length); x++) {\n          var _result$idDocumentHea4;\n          url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest;\n          await APIRequest(\"GET\", url).then(res => {\n            console.log(res.data);\n            tipi.push(res.data.type);\n          }).catch(e => {\n            console.log(e);\n          });\n        }\n        var find = tipi.find(el => el === 'FOR-ORDINE');\n        if (find !== undefined) {\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Il documento è già associato ad una task di tipo FOR-ORDINE\",\n            life: 3000\n          });\n        } else {\n          url = \"documents/?idWarehouses=\" + result.idRetailer.idRegistry.users[0].warehousesCross[0].idWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=FOR-ORDINE\";\n          //Chiamata axios per la creazione del documento\n          await APIRequest('POST', url).then(res => {\n            console.log(res.data);\n            this.toast.show({\n              severity: 'success',\n              summary: 'Ottimo',\n              detail: \"Il documento è stato inserito con successo\",\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          }).catch(e => {\n            var _e$response23, _e$response24;\n            console.log(e);\n            this.toast.show({\n              severity: 'error',\n              summary: 'Siamo spiacenti',\n              detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response23 = e.response) === null || _e$response23 === void 0 ? void 0 : _e$response23.data) !== undefined ? (_e$response24 = e.response) === null || _e$response24 === void 0 ? void 0 : _e$response24.data : e.message),\n              life: 3000\n            });\n          });\n        }\n      } else {\n        url = \"documents/?idWarehouses=\" + result.idRetailer.idRegistry.users[0].warehousesCross[0].idWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=FOR-ORDINE\";\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', url).then(res => {\n          console.log(res.data);\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il documento è stato inserito con successo\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response25, _e$response26;\n          console.log(e);\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response25 = e.response) === null || _e$response25 === void 0 ? void 0 : _e$response25.data) !== undefined ? (_e$response26 = e.response) === null || _e$response26 === void 0 ? void 0 : _e$response26.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\",\n        life: 3000\n      });\n    }\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var opMag = [];\n    var respMag = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          if (element.role === 'RESP_MAG') {\n            respMag.push({\n              label: element.first_name + ' ' + element.last_name,\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.opMag = opMag;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog3: true,\n        opMag: opMag,\n        respMag: respMag,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null);\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'OP_MAG') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n                opMag.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              }\n            });\n          }\n          this.opMag = opMag;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'OP_MAG') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n              opMag.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            }\n          });\n        }\n        this.opMag = opMag;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog3: true,\n          opMag: opMag,\n          respMag: respMag,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n      window.location.reload();\n    }).catch(e => {\n      var _e$response27, _e$response28;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response27 = e.response) === null || _e$response27 === void 0 ? void 0 : _e$response27.data) !== undefined ? (_e$response28 = e.response) === null || _e$response28 === void 0 ? void 0 : _e$response28.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  render() {\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 842,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 13\n    }, this);\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 875,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.manager.idUser.username\",\n      header: Costanti.Responsabile,\n      body: \"manager\",\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 943,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }, {\n      name: Costanti.ModificaTask,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-pencil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 50\n      }, this),\n      handler: this.onRowSelect\n    }, {\n      name: Costanti.CreaDDT,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-car\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 45\n      }, this),\n      handler: this.creaDDT,\n      status: 'prepared'\n    }, {\n      name: 'Crea FOR-ORDINE',\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-car\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 946,\n        columnNumber: 46\n      }, this),\n      handler: this.creaFORORDINE,\n      status: 'prepared'\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 45\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 958,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DocumentiOrdineVendita\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 962,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\",\n                emptyMessage: \"Nessun elemento disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 968,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 966,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          actionsColumn: actionFields,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButto2n: true,\n          actionExtraButton2: this.assegnaLavorazioni,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Vendite\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          result: this.state.results3,\n          results: this.state.result,\n          documento: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag,\n          respMag: this.state.respMag,\n          chain: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1036,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1061,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1054,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter2,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1071,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1071,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\",\n            emptyMessage: \"Nessun elemento disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1070,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog4,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1080,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1084,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1086,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedRetailer,\n          options: this.retailers,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona cliente\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1087,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 956,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DocumentiVendite;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "Dialog", "Dropdown", "JoyrideGen", "Toast", "<PERSON><PERSON>", "Sidebar", "Print", "VisualizzaDocumenti", "Nav", "CustomDataTable", "SelezionaOperatore", "chainConfrontoDispQta", "jsxDEV", "_jsxDEV", "DocumentiVendite", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "url", "state", "<PERSON><PERSON><PERSON><PERSON>", "param2", "code", "lazyParams", "rows", "page", "window", "sessionStorage", "setItem", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$tasks", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "documentDate", "tasks", "erpSync", "push", "results", "results5", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "onRowSelect", "result", "idFOROrd", "localStorage", "JSON", "stringify", "location", "pathname", "results2", "results3", "results4", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "selectedDocuments", "displayed", "opMag", "respMag", "mex", "search", "value2", "clienti", "param", "deleteResultDialog", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "_element$tasks2", "_e$response3", "_e$response4", "retailers", "loadLazyTimeout", "warehouse", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "reset", "resetDesc", "onPage", "onSort", "onFilter", "creaDDT", "assegnaLavorazioni", "<PERSON><PERSON><PERSON><PERSON>", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "creaFORORDINE", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "parse", "getItem", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "task", "documentBody", "documentBodies", "_e$response1", "_e$response10", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "role", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "_element$tasks4", "_e$response11", "_e$response12", "_element$tasks5", "_e$response13", "_e$response14", "event", "clearTimeout", "setTimeout", "_element$tasks6", "_e$response15", "_e$response16", "Math", "random", "field", "_element$tasks7", "_e$response17", "_e$response18", "loadLazyData", "_result$idDocumentHea", "tipi", "idDocumentHeadOrig", "length", "_result$idDocumentHea2", "idDocDest", "find", "el", "idDocument", "reload", "_e$response19", "_e$response20", "_e$response21", "_e$response22", "_result$idDocumentHea3", "_result$idDocumentHea4", "users", "warehousesCross", "_e$response23", "_e$response24", "_e$response25", "_e$response26", "FilterOp", "operator", "_e$response27", "_e$response28", "render", "resultDialogFooter2", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultD<PERSON><PERSON><PERSON><PERSON>er", "doc", "resultDialogFooter3", "deleteResultDialogFooter", "icon", "Si", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "Responsabile", "Operatore", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "assegnaLavorazione", "status2", "ModificaTask", "CreaDDT", "Elimina", "filterDnone", "ref", "DocumentiOrdineVendita", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "emptyMessage", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "autoLayout", "actionsColumn", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButto2n", "actionExtraButton2", "labelExtraButton2", "disabledExtraButton2", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "classInputSearch", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "chain", "Conferma", "fontSize", "ResDeleteDoc", "Primadiproseguire", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/ufficioVendite.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioVendite - operazioni sull'ufficio vendite\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Sidebar } from 'primereact/sidebar';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport '../../css/DataTableDemo.css';\nimport { chainConfrontoDispQta } from '../../components/route';\n\nclass DocumentiVendite extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            selectedWarehouse: null,\n            selectedDocuments: null,\n            loading: true,\n            displayed: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            totalRecords: 0,\n            search: '',\n            value: null,\n            value2: null,\n            clienti: null,\n            param: '?idWarehouses=',\n            param2: '&idRetailer=',\n            selectedRetailer: null,\n            deleteResultDialog: null,\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedRetailer: e.value\n            });\n\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            idRetailer: element.idRetailer,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.retailers = []\n        this.loadLazyTimeout = null;\n        this.warehouse = [];\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.creaDDT = this.creaDDT.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.selectionHandler = this.selectionHandler.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.creaFORORDINE = this.creaFORORDINE.bind(this);\n        this.onRowSelect = this.onRowSelect.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            idRetailer: element.idRetailer,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest('GET', 'retailers/')\n            .then(res => {\n                res.data.forEach(element => {\n                    if (element && element.idRegistry) {\n                        var x = {\n                            name: element.idRegistry.firstName || 'Nome non disponibile',\n                            code: element.id || 0\n                        }\n                        this.retailers.push(x)\n                    }\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n        await APIRequest(\"GET\", \"employees?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value });\n        var url = 'documents?idWarehouses=' + e.value + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer.idRegistry.firstName,\n                        idRetailer: element.idRetailer,\n                        documentDate: element.documentDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var task = []\n        var documentBody = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        var respMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'OP_MAG' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                    opMag.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n                    respMag.push({\n                        label: element.first_name + ' ' + element.last_name,\n                        value: element.idemployee,\n                    });\n                }\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n        });\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedRetailer: null, selectedWarehouse: idWarehouse, loading: true, search: '' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            idRetailer: element.idRetailer,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false,\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedRetailer: null, selectedWarehouse: idWarehouse, loading: true, search: '' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            idRetailer: element.idRetailer,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            idRetailer: element.idRetailer,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            idRetailer: element.idRetailer,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    async creaDDT(result) {\n        if (result.tasks !== null) {\n            let url = ''\n            let tipi = []\n            if (result.idDocumentHeadOrig?.length > 0) {\n                for (var x = 0; x < result.idDocumentHeadOrig?.length; x++) {\n                    url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest\n                    await APIRequest(\"GET\", url)\n                        .then((res) => {\n                            console.log(res.data)\n                            tipi.push(res.data.type)\n                        })\n                        .catch((e) => {\n                            console.log(e);\n                        });\n                }\n                var find = tipi.find(el => el === 'CLI-DDT')\n                if (find !== undefined) {\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Il documento è già associato ad una task di tipo CLI-DDT\", life: 3000 });\n                } else {\n                    url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\"\n                    //Chiamata axios per la creazione del documento\n                    await APIRequest('POST', url)\n                        .then(res => {\n                            console.log(res.data);\n                            this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.log(e)\n                            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        })\n                }\n            } else {\n                url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\"\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', url)\n                    .then(res => {\n                        console.log(res.data);\n                        this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\", life: 3000 });\n        }\n    }\n    async creaFORORDINE(result) {\n        if (result.tasks !== null) {\n            let url = ''\n            let tipi = []\n            if (result.idDocumentHeadOrig?.length > 0) {\n                for (var x = 0; x < result.idDocumentHeadOrig?.length; x++) {\n                    url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest\n                    await APIRequest(\"GET\", url)\n                        .then((res) => {\n                            console.log(res.data)\n                            tipi.push(res.data.type)\n                        })\n                        .catch((e) => {\n                            console.log(e);\n                        });\n                }\n                var find = tipi.find(el => el === 'FOR-ORDINE')\n                if (find !== undefined) {\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Il documento è già associato ad una task di tipo FOR-ORDINE\", life: 3000 });\n                } else {\n                    url = \"documents/?idWarehouses=\" + result.idRetailer.idRegistry.users[0].warehousesCross[0].idWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=FOR-ORDINE\"\n                    //Chiamata axios per la creazione del documento\n                    await APIRequest('POST', url)\n                        .then(res => {\n                            console.log(res.data);\n                            this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.log(e)\n                            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        })\n                }\n            } else {\n                url = \"documents/?idWarehouses=\" + result.idRetailer.idRegistry.users[0].warehousesCross[0].idWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=FOR-ORDINE\"\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', url)\n                    .then(res => {\n                        console.log(res.data);\n                        this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\", life: 3000 });\n        }\n    }\n\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var opMag = [];\n        var respMag = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    if (element.role === 'RESP_MAG') {\n                        respMag.push({\n                            label: element.first_name + ' ' + element.last_name,\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.opMag = opMag;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog3: true,\n                opMag: opMag,\n                respMag: respMag,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'OP_MAG') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                                opMag.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }\n                        })\n                    }\n                    this.opMag = opMag;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog3: true,\n                        opMag: opMag,\n                        respMag: respMag,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'OP_MAG') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                            opMag.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }\n                    })\n                }\n                this.opMag = opMag;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog3: true,\n                    opMag: opMag,\n                    respMag: respMag,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n\n    selectionHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n                window.location.reload();\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false,\n        });\n    }\n    onRowSelect = (result) => {\n        if (result.tasks !== null && result.status !== 'prepared') {\n            var idFOROrd = []\n            this.state.results.forEach(element => {\n                if (element.id === result.id) {\n                    idFOROrd = element\n                }\n            })\n            localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n            window.location.pathname = chainConfrontoDispQta;\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione\",\n                detail: \"Verificare se è presente una task per questo documento e che la stessa non sia in stato prepared prima di procedere\",\n                life: 3000,\n            });\n        }\n\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog4: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog4: false\n        })\n    }\n    render() {\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                body: \"typeDoc\",\n                showHeader: true,\n            },\n            {\n                field: \"retailer\",\n                header: Costanti.cliente,\n                body: \"retailer\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.manager.idUser.username\",\n                header: Costanti.Responsabile,\n                body: \"manager\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.operator.idUser.username\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.status\",\n                header: Costanti.StatoTask,\n                body: \"assigned\",\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: \"ERP Sync\",\n                body: \"erpSync\",\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n            { name: Costanti.ModificaTask, icon: <i className=\"pi pi-pencil\" />, handler: this.onRowSelect },\n            { name: Costanti.CreaDDT, icon: <i className=\"pi pi-car\" />, handler: this.creaDDT, status: 'prepared' },\n            { name: 'Crea FOR-ORDINE', icon: <i className=\"pi pi-car\" />, handler: this.creaFORORDINE, status: 'prepared' },\n            { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n        ];\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DocumentiOrdineVendita}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        actionsColumn={actionFields}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionHandler(e)}\n                        showExtraButto2n={true}\n                        actionExtraButton2={this.assegnaLavorazioni}\n                        labelExtraButton2={Costanti.assegnaLavorazioni}\n                        disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Vendite\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        result={this.state.results3}\n                        results={this.state.result}\n                        documento={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaOperatore result={this.state.result} opMag={this.opMag} respMag={this.state.respMag} chain={true} />\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter2}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog4} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedRetailer} options={this.retailers} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default DocumentiVendite;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,KAAK,QAAQ,2CAA2C;AACjE,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAO,6BAA6B;AACpC,SAASC,qBAAqB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,gBAAgB,SAASjB,SAAS,CAAC;EAarCkB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IA2MD;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7C,IAAIC,GAAG,GAAG,yBAAyB,GAAGJ,CAAC,CAACG,KAAK,IAAI,IAAI,CAACE,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAC1PC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEd,CAAC,CAACG,KAAK,CAAC;MACrD,MAAMhC,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAwB,cAAA,GAAED,OAAO,CAACS,KAAK,cAAAR,cAAA,uBAAbA,cAAA,CAAexB;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAyC,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAAzC,CAAC,CAACmD,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYvB,IAAI,MAAKkC,SAAS,IAAAV,YAAA,GAAG1C,CAAC,CAACmD,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IAAA,KA6fDC,WAAW,GAAIC,MAAM,IAAK;MACtB,IAAIA,MAAM,CAAC1B,KAAK,KAAK,IAAI,IAAI0B,MAAM,CAAC1D,MAAM,KAAK,UAAU,EAAE;QACvD,IAAI2D,QAAQ,GAAG,EAAE;QACjB,IAAI,CAACpD,KAAK,CAAC4B,OAAO,CAACb,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIA,OAAO,CAAC/B,EAAE,KAAKkE,MAAM,CAAClE,EAAE,EAAE;YAC1BmE,QAAQ,GAAGpC,OAAO;UACtB;QACJ,CAAC,CAAC;QACFqC,YAAY,CAAC5C,OAAO,CAAC,YAAY,EAAE6C,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;QAC5D7C,MAAM,CAACiD,QAAQ,CAACC,QAAQ,GAAG/E,qBAAqB;MACpD,CAAC,MAAM;QACH,IAAI,CAAC8D,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,qHAAqH;UAC7HK,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IAEJ,CAAC;IA/vBG,IAAI,CAACjD,KAAK,GAAG;MACT4B,OAAO,EAAE,IAAI;MACb8B,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACd/B,QAAQ,EAAE,IAAI;MACdgC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBnE,iBAAiB,EAAE,IAAI;MACvBoE,iBAAiB,EAAE,IAAI;MACvB/B,OAAO,EAAE,IAAI;MACbgC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPlB,MAAM,EAAE,IAAI,CAACnE,WAAW;MACxB8C,YAAY,EAAE,CAAC;MACfwC,MAAM,EAAE,EAAE;MACVxE,KAAK,EAAE,IAAI;MACXyE,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,gBAAgB;MACvBvE,MAAM,EAAE,cAAc;MACtBD,gBAAgB,EAAE,IAAI;MACtByE,kBAAkB,EAAE,IAAI;MACxBtE,UAAU,EAAE;QACR4B,KAAK,EAAE,CAAC;QACR3B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPqE,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAE/E,KAAK,EAAE,EAAE;YAAEgF,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAEhF,KAAK,EAAE,EAAE;YAAEgF,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAEhF,KAAK,EAAE,EAAE;YAAEgF,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAMpF,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACVsC,OAAO,EAAE,IAAI;QACboC,MAAM,EAAE3E,CAAC,CAACG,KAAK,CAACkF,IAAI;QACpB/E,gBAAgB,EAAEN,CAAC,CAACG;MACxB,CAAC,CAAC;MAEF,IAAIC,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACyE,KAAK,GAAG,IAAI,CAACzE,KAAK,CAACH,iBAAiB,GAAG,IAAI,CAACG,KAAK,CAACE,MAAM,GAAGP,CAAC,CAACG,KAAK,CAACK,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAClN,MAAMxC,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAiE,eAAA;UAClC,IAAI/D,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAwF,eAAA,GAAEjE,OAAO,CAACS,KAAK,cAAAwD,eAAA,uBAAbA,eAAA,CAAexF;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAuF,YAAA,EAAAC,YAAA;QACV7C,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAqC,YAAA,GAAAvF,CAAC,CAACmD,QAAQ,cAAAoC,YAAA,uBAAVA,YAAA,CAAYrE,IAAI,MAAKkC,SAAS,IAAAoC,YAAA,GAAGxF,CAAC,CAACmD,QAAQ,cAAAqC,YAAA,uBAAVA,YAAA,CAAYtE,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACmC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAAC9F,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC8F,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,MAAM,GAAG,IAAI,CAACA,MAAM,CAACN,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACS,OAAO,GAAG,IAAI,CAACA,OAAO,CAACT,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACU,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACV,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACW,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACX,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACY,YAAY,GAAG,IAAI,CAACA,YAAY,CAACZ,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACa,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACb,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACc,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACd,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACe,aAAa,GAAG,IAAI,CAACA,aAAa,CAACf,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACtC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACsC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACgB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACiB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACjB,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACkB,WAAW,GAAG,IAAI,CAACA,WAAW,CAAClB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMmB,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,WAAW,GAAGtD,IAAI,CAACuD,KAAK,CAACtG,MAAM,CAACC,cAAc,CAACsG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3CA,WAAW,GAAGA,WAAW,CAACzG,IAAI,KAAK4C,SAAS,GAAG6D,WAAW,CAACzG,IAAI,GAAGyG,WAAW;MAC7E,IAAI7G,GAAG,GAAG,yBAAyB,GAAG6G,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC5G,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAE+G;MAAY,CAAC,CAAC;MACjD,MAAM9I,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA+F,eAAA;UAClC,IAAI7F,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAsH,eAAA,GAAE/F,OAAO,CAACS,KAAK,cAAAsF,eAAA,uBAAbA,eAAA,CAAetH;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAqH,YAAA,EAAAC,YAAA;QACV3E,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAmE,YAAA,GAAArH,CAAC,CAACmD,QAAQ,cAAAkE,YAAA,uBAAVA,YAAA,CAAYnG,IAAI,MAAKkC,SAAS,IAAAkE,YAAA,GAAGtH,CAAC,CAACmD,QAAQ,cAAAmE,YAAA,uBAAVA,YAAA,CAAYpG,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACrD,QAAQ,CAAC;QAAEiE,YAAY,EAAE,IAAI;QAAEK,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;IACA,MAAMpG,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC4C,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIuG,KAAK,IAAIvG,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAACyE,SAAS,CAAC3D,IAAI,CAAC;UAChBqD,IAAI,EAAEkC,KAAK,CAACC,aAAa;UACzBrH,KAAK,EAAEoH,KAAK,CAACjI;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDkD,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAAyH,YAAA,EAAAC,YAAA;MACV/E,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAuE,YAAA,GAAAzH,CAAC,CAACmD,QAAQ,cAAAsE,YAAA,uBAAVA,YAAA,CAAYvG,IAAI,MAAKkC,SAAS,IAAAsE,YAAA,GAAG1H,CAAC,CAACmD,QAAQ,cAAAuE,YAAA,uBAAVA,YAAA,CAAYxG,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMnF,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC4C,IAAI,CAACC,GAAG,IAAI;MACTA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,IAAIA,OAAO,CAACO,UAAU,EAAE;UAC/B,IAAIL,CAAC,GAAG;YACJ8D,IAAI,EAAEhE,OAAO,CAACO,UAAU,CAACrC,SAAS,IAAI,sBAAsB;YAC5DiB,IAAI,EAAEa,OAAO,CAAC/B,EAAE,IAAI;UACxB,CAAC;UACD,IAAI,CAACmG,SAAS,CAACzD,IAAI,CAACT,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC,CAACiB,KAAK,CAAExC,CAAC,IAAK;MACZ2C,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,MAAM7B,UAAU,CAAC,KAAK,EAAE,gCAAgC,CAAC,CACpD4C,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACf,QAAQ,CAAC;QACVgE,QAAQ,EAAEjD,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDsB,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAA2H,YAAA,EAAAC,YAAA;MACVjF,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAAyE,YAAA,GAAA3H,CAAC,CAACmD,QAAQ,cAAAwE,YAAA,uBAAVA,YAAA,CAAYzG,IAAI,MAAKkC,SAAS,IAAAwE,YAAA,GAAG5H,CAAC,CAACmD,QAAQ,cAAAyE,YAAA,uBAAVA,YAAA,CAAY1G,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EAyCA;EACA,MAAMsC,cAAcA,CAACpC,MAAM,EAAE;IACzB,IAAIpD,GAAG,GAAG,2BAA2B,GAAGoD,MAAM,CAAClE,EAAE;IACjD,IAAIuI,IAAI,GAAG,EAAE;IACb,IAAIC,YAAY,GAAG,EAAE;IACrB,MAAM3J,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;MACX8G,YAAY,GAAG9G,GAAG,CAACE,IAAI,CAAC6G,cAAc;MACtCvE,MAAM,CAACuE,cAAc,GAAG/G,GAAG,CAACE,IAAI,CAAC6G,cAAc;MAC/CF,IAAI,GAAG7G,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDsB,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAAgI,YAAA,EAAAC,aAAA;MACVtF,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8E,YAAA,GAAAhI,CAAC,CAACmD,QAAQ,cAAA6E,YAAA,uBAAVA,YAAA,CAAY9G,IAAI,MAAKkC,SAAS,IAAA6E,aAAA,GAAGjI,CAAC,CAACmD,QAAQ,cAAA8E,aAAA,uBAAVA,aAAA,CAAY/G,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBG,MAAM,CAAChC,MAAM,GACb,OAAO,GACP,IAAI0G,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAChF,MAAM,CAAC3B,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAC5B,QAAQ,CAAC;MACVkE,aAAa,EAAE,IAAI;MACnBX,MAAM,EAAEqE,IAAI;MACZ9D,QAAQ,EAAE,IAAI,CAAC1D,KAAK,CAAC4B,OAAO,CAACwG,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACpJ,EAAE,KAAKkE,MAAM,CAAClE,EAAE,CAAC;MAClE0E,QAAQ,EAAE8D,YAAY;MACtBpD,GAAG,EAAErB;IACT,CAAC,CAAC;EACN;EACA;EACAyC,kBAAkBA,CAACtC,MAAM,EAAE;IACvB,IAAI,CAACvD,QAAQ,CAAC;MACVuD,MAAM,EAAEA,MAAM;MACdW,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA4B,UAAUA,CAACvC,MAAM,EAAE;IACf,IAAIH,OAAO,GACP,oBAAoB,GACpBG,MAAM,CAAChC,MAAM,GACb,OAAO,GACP,IAAI0G,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAChF,MAAM,CAAC3B,YAAY,CAAC,CAAC;IAC5C,IAAI2C,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACpE,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAAC7C,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAACsH,IAAI,KAAK,QAAQ,IAAInF,MAAM,CAAC1B,KAAK,KAAK,IAAI,EAAE;UACpD,IAAI8G,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAACxH,OAAO,CAACyH,WAAW,CAAC,GAAGD,QAAQ,CAACxH,OAAO,CAAC0H,YAAY,CAAC,GAAGF,QAAQ,CAACxH,OAAO,CAAC2H,aAAa,CAAC;UAC1GxE,KAAK,CAACxC,IAAI,CAAC;YACPiH,KAAK,EAAE5H,OAAO,CAAC6H,UAAU,GAAG,GAAG,GAAG7H,OAAO,CAAC8H,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;YAC1FzI,KAAK,EAAEkB,OAAO,CAAC+H;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAI/H,OAAO,CAACsH,IAAI,KAAK,UAAU,IAAInF,MAAM,CAAC1B,KAAK,KAAK,IAAI,EAAE;UAC7D2C,OAAO,CAACzC,IAAI,CAAC;YACTiH,KAAK,EAAE5H,OAAO,CAAC6H,UAAU,GAAG,GAAG,GAAG7H,OAAO,CAAC8H,SAAS;YACnDhJ,KAAK,EAAEkB,OAAO,CAAC+H;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC5E,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACvE,QAAQ,CAAC;MACVuD,MAAM,EAAA6F,aAAA,KAAO7F,MAAM,CAAE;MACrBY,aAAa,EAAE,IAAI;MACnBI,KAAK,EAAEA,KAAK;MACZC,OAAO,EAAEA,OAAO;MAChBC,GAAG,EAAErB;IACT,CAAC,CAAC;EACN;EACA;EACA2C,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC/F,QAAQ,CAAC;MACVmE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA,MAAM6B,KAAKA,CAAA,EAAG;IACV,IAAIgB,WAAW,GAAGtD,IAAI,CAACuD,KAAK,CAACtG,MAAM,CAACC,cAAc,CAACsG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI7G,GAAG,GAAG,yBAAyB,GAAG6G,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC5G,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEK,gBAAgB,EAAE,IAAI;QAAEJ,iBAAiB,EAAE+G,WAAW;QAAE1E,OAAO,EAAE,IAAI;QAAEoC,MAAM,EAAE;MAAG,CAAC,CAAC;MACpG,MAAMxG,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAiI,eAAA;UAClC,IAAI/H,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAwJ,eAAA,GAAEjI,OAAO,CAACS,KAAK,cAAAwH,eAAA,uBAAbA,eAAA,CAAexJ;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAuJ,aAAA,EAAAC,aAAA;QACV7G,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAqG,aAAA,GAAAvJ,CAAC,CAACmD,QAAQ,cAAAoG,aAAA,uBAAVA,aAAA,CAAYrI,IAAI,MAAKkC,SAAS,IAAAoG,aAAA,GAAGxJ,CAAC,CAACmD,QAAQ,cAAAqG,aAAA,uBAAVA,aAAA,CAAYtI,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAM4C,SAASA,CAAA,EAAG;IACd,IAAIe,WAAW,GAAGtD,IAAI,CAACuD,KAAK,CAACtG,MAAM,CAACC,cAAc,CAACsG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI7G,GAAG,GAAG,yBAAyB,GAAG6G,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC5G,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACV,QAAQ,CAAC;QAAEK,gBAAgB,EAAE,IAAI;QAAEJ,iBAAiB,EAAE+G,WAAW;QAAE1E,OAAO,EAAE,IAAI;QAAEoC,MAAM,EAAE;MAAG,CAAC,CAAC;MACpG,MAAMxG,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAoI,eAAA;UAClC,IAAIlI,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAA2J,eAAA,GAAEpI,OAAO,CAACS,KAAK,cAAA2H,eAAA,uBAAbA,eAAA,CAAe3J;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAE;YAAE4B,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAK;YAAE3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE2B,SAAS,EAAEtB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAC/B,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL6B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAA0J,aAAA,EAAAC,aAAA;QACVhH,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAwG,aAAA,GAAA1J,CAAC,CAACmD,QAAQ,cAAAuG,aAAA,uBAAVA,aAAA,CAAYxI,IAAI,MAAKkC,SAAS,IAAAuG,aAAA,GAAG3J,CAAC,CAACmD,QAAQ,cAAAwG,aAAA,uBAAVA,aAAA,CAAYzI,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA6C,MAAMA,CAACyD,KAAK,EAAE;IACV,IAAI,CAAC3J,QAAQ,CAAC;MAAEsC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAACmD,eAAe,EAAE;MACtBmE,YAAY,CAAC,IAAI,CAACnE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGoE,UAAU,CAAC,YAAY;MAC1C,IAAI1J,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACyE,KAAK,GAAG,IAAI,CAACzE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAGoJ,KAAK,CAAClJ,IAAI,GAAG,QAAQ,GAAGkJ,KAAK,CAACjJ,IAAI;MACpP,MAAMxC,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA0I,eAAA;UAClC,IAAIxI,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAiK,eAAA,GAAE1I,OAAO,CAACS,KAAK,cAAAiI,eAAA,uBAAbA,eAAA,CAAejK;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAEmJ,KAAK;UACjBrH,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAgK,aAAA,EAAAC,aAAA;QACVtH,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8G,aAAA,GAAAhK,CAAC,CAACmD,QAAQ,cAAA6G,aAAA,uBAAVA,aAAA,CAAY9I,IAAI,MAAKkC,SAAS,IAAA6G,aAAA,GAAGjK,CAAC,CAACmD,QAAQ,cAAA8G,aAAA,uBAAVA,aAAA,CAAY/I,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE4G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA/D,MAAMA,CAACwD,KAAK,EAAE;IACV,IAAI,CAAC3J,QAAQ,CAAC;MAAEsC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI6H,KAAK,GAAGR,KAAK,CAAC5E,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAG4E,KAAK,CAAC5E,SAAS;IAChG,IAAI,IAAI,CAACU,eAAe,EAAE;MACtBmE,YAAY,CAAC,IAAI,CAACnE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGoE,UAAU,CAAC,YAAY;MAC1C,IAAI1J,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACyE,KAAK,GAAG,IAAI,CAACzE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI,GAAG,SAAS,GAAGyJ,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAC3E,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAM9G,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAgJ,eAAA;UAClC,IAAI9I,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDoC,UAAU,EAAEN,OAAO,CAACM,UAAU;YAC9BE,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAuK,eAAA,GAAEhJ,OAAO,CAACS,KAAK,cAAAuI,eAAA,uBAAbA,eAAA,CAAevK;UAC3B,CAAC;UACDmB,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVgC,OAAO,EAAEhB,SAAS;UAClBiB,QAAQ,EAAEjB,SAAS;UACnBkB,YAAY,EAAEnB,GAAG,CAACE,IAAI,CAACkB,UAAU;UACjC3B,UAAU,EAAA4I,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAChJ,KAAK,CAACI,UAAU;YAAEuE,SAAS,EAAE4E,KAAK,CAAC5E,SAAS;YAAEC,SAAS,EAAE2E,KAAK,CAAC3E;UAAS,EAAE;UAChG1C,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAExC,CAAC,IAAK;QAAA,IAAAsK,aAAA,EAAAC,aAAA;QACV5H,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;QACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAoH,aAAA,GAAAtK,CAAC,CAACmD,QAAQ,cAAAmH,aAAA,uBAAVA,aAAA,CAAYpJ,IAAI,MAAKkC,SAAS,IAAAmH,aAAA,GAAGvK,CAAC,CAACmD,QAAQ,cAAAoH,aAAA,uBAAVA,aAAA,CAAYrJ,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE4G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EAEA9D,QAAQA,CAACuD,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC3J,QAAQ,CAAC;MAAEQ,UAAU,EAAEmJ;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EAC3D;EACA,MAAMlE,OAAOA,CAAC9C,MAAM,EAAE;IAClB,IAAIA,MAAM,CAAC1B,KAAK,KAAK,IAAI,EAAE;MAAA,IAAA2I,qBAAA;MACvB,IAAIrK,GAAG,GAAG,EAAE;MACZ,IAAIsK,IAAI,GAAG,EAAE;MACb,IAAI,EAAAD,qBAAA,GAAAjH,MAAM,CAACmH,kBAAkB,cAAAF,qBAAA,uBAAzBA,qBAAA,CAA2BG,MAAM,IAAG,CAAC,EAAE;QACvC,KAAK,IAAIrJ,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAAsJ,sBAAA,GAAGrH,MAAM,CAACmH,kBAAkB,cAAAE,sBAAA,uBAAzBA,sBAAA,CAA2BD,MAAM,GAAErJ,CAAC,EAAE,EAAE;UAAA,IAAAsJ,sBAAA;UACxDzK,GAAG,GAAG,2BAA2B,GAAGoD,MAAM,CAACmH,kBAAkB,CAACpJ,CAAC,CAAC,CAACuJ,SAAS;UAC1E,MAAM3M,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;YACX2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;YACrBwJ,IAAI,CAAC1I,IAAI,CAAChB,GAAG,CAACE,IAAI,CAACO,IAAI,CAAC;UAC5B,CAAC,CAAC,CACDe,KAAK,CAAExC,CAAC,IAAK;YACV2C,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;UAClB,CAAC,CAAC;QACV;QACA,IAAI+K,IAAI,GAAGL,IAAI,CAACK,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAK,SAAS,CAAC;QAC5C,IAAID,IAAI,KAAK3H,SAAS,EAAE;UACpB,IAAI,CAACP,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,EAAE,0DAA0D;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;QACtJ,CAAC,MAAM;UACHlD,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,cAAc,GAAGsD,MAAM,CAAClE,EAAE,GAAG,cAAc,GAAGkE,MAAM,CAAC1B,KAAK,CAACmJ,UAAU,CAACtJ,UAAU,CAACrC,EAAE,GAAG,+BAA+B;UACvL;UACA,MAAMnB,UAAU,CAAC,MAAM,EAAEiC,GAAG,CAAC,CACxBW,IAAI,CAACC,GAAG,IAAI;YACT2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;YACrB,IAAI,CAAC2B,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,SAAS;cAAEC,OAAO,EAAE,QAAQ;cAAEC,MAAM,EAAE,4CAA4C;cAAEK,IAAI,EAAE;YAAK,CAAC,CAAC;YAC7HwG,UAAU,CAAC,MAAM;cACblJ,MAAM,CAACiD,QAAQ,CAACqH,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC,CAAC1I,KAAK,CAAExC,CAAC,IAAK;YAAA,IAAAmL,aAAA,EAAAC,aAAA;YACZzI,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;YACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,iBAAiB;cAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAiI,aAAA,GAAAnL,CAAC,CAACmD,QAAQ,cAAAgI,aAAA,uBAAVA,aAAA,CAAYjK,IAAI,MAAKkC,SAAS,IAAAgI,aAAA,GAAGpL,CAAC,CAACmD,QAAQ,cAAAiI,aAAA,uBAAVA,aAAA,CAAYlK,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;UAC/N,CAAC,CAAC;QACV;MACJ,CAAC,MAAM;QACHlD,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,cAAc,GAAGsD,MAAM,CAAClE,EAAE,GAAG,cAAc,GAAGkE,MAAM,CAAC1B,KAAK,CAACmJ,UAAU,CAACtJ,UAAU,CAACrC,EAAE,GAAG,+BAA+B;QACvL;QACA,MAAMnB,UAAU,CAAC,MAAM,EAAEiC,GAAG,CAAC,CACxBW,IAAI,CAACC,GAAG,IAAI;UACT2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;UACrB,IAAI,CAAC2B,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,4CAA4C;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7HwG,UAAU,CAAC,MAAM;YACblJ,MAAM,CAACiD,QAAQ,CAACqH,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAC1I,KAAK,CAAExC,CAAC,IAAK;UAAA,IAAAqL,aAAA,EAAAC,aAAA;UACZ3I,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;UACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAmI,aAAA,GAAArL,CAAC,CAACmD,QAAQ,cAAAkI,aAAA,uBAAVA,aAAA,CAAYnK,IAAI,MAAKkC,SAAS,IAAAkI,aAAA,GAAGtL,CAAC,CAACmD,QAAQ,cAAAmI,aAAA,uBAAVA,aAAA,CAAYpK,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/N,CAAC,CAAC;MACV;IACJ,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,6FAA6F;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;IACzL;EACJ;EACA,MAAMsD,aAAaA,CAACpD,MAAM,EAAE;IACxB,IAAIA,MAAM,CAAC1B,KAAK,KAAK,IAAI,EAAE;MAAA,IAAAyJ,sBAAA;MACvB,IAAInL,GAAG,GAAG,EAAE;MACZ,IAAIsK,IAAI,GAAG,EAAE;MACb,IAAI,EAAAa,sBAAA,GAAA/H,MAAM,CAACmH,kBAAkB,cAAAY,sBAAA,uBAAzBA,sBAAA,CAA2BX,MAAM,IAAG,CAAC,EAAE;QACvC,KAAK,IAAIrJ,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAAiK,sBAAA,GAAGhI,MAAM,CAACmH,kBAAkB,cAAAa,sBAAA,uBAAzBA,sBAAA,CAA2BZ,MAAM,GAAErJ,CAAC,EAAE,EAAE;UAAA,IAAAiK,sBAAA;UACxDpL,GAAG,GAAG,2BAA2B,GAAGoD,MAAM,CAACmH,kBAAkB,CAACpJ,CAAC,CAAC,CAACuJ,SAAS;UAC1E,MAAM3M,UAAU,CAAC,KAAK,EAAEiC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;YACX2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;YACrBwJ,IAAI,CAAC1I,IAAI,CAAChB,GAAG,CAACE,IAAI,CAACO,IAAI,CAAC;UAC5B,CAAC,CAAC,CACDe,KAAK,CAAExC,CAAC,IAAK;YACV2C,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;UAClB,CAAC,CAAC;QACV;QACA,IAAI+K,IAAI,GAAGL,IAAI,CAACK,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAK,YAAY,CAAC;QAC/C,IAAID,IAAI,KAAK3H,SAAS,EAAE;UACpB,IAAI,CAACP,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,EAAE,6DAA6D;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;QACzJ,CAAC,MAAM;UACHlD,GAAG,GAAG,0BAA0B,GAAGoD,MAAM,CAAC7B,UAAU,CAACC,UAAU,CAAC6J,KAAK,CAAC,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC,CAACzE,WAAW,GAAG,cAAc,GAAGzD,MAAM,CAAClE,EAAE,GAAG,cAAc,GAAGkE,MAAM,CAAC1B,KAAK,CAACmJ,UAAU,CAACtJ,UAAU,CAACrC,EAAE,GAAG,kCAAkC;UAClO;UACA,MAAMnB,UAAU,CAAC,MAAM,EAAEiC,GAAG,CAAC,CACxBW,IAAI,CAACC,GAAG,IAAI;YACT2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;YACrB,IAAI,CAAC2B,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,SAAS;cAAEC,OAAO,EAAE,QAAQ;cAAEC,MAAM,EAAE,4CAA4C;cAAEK,IAAI,EAAE;YAAK,CAAC,CAAC;YAC7HwG,UAAU,CAAC,MAAM;cACblJ,MAAM,CAACiD,QAAQ,CAACqH,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC,CAAC1I,KAAK,CAAExC,CAAC,IAAK;YAAA,IAAA2L,aAAA,EAAAC,aAAA;YACZjJ,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;YACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,iBAAiB;cAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAyI,aAAA,GAAA3L,CAAC,CAACmD,QAAQ,cAAAwI,aAAA,uBAAVA,aAAA,CAAYzK,IAAI,MAAKkC,SAAS,IAAAwI,aAAA,GAAG5L,CAAC,CAACmD,QAAQ,cAAAyI,aAAA,uBAAVA,aAAA,CAAY1K,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;UAC/N,CAAC,CAAC;QACV;MACJ,CAAC,MAAM;QACHlD,GAAG,GAAG,0BAA0B,GAAGoD,MAAM,CAAC7B,UAAU,CAACC,UAAU,CAAC6J,KAAK,CAAC,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC,CAACzE,WAAW,GAAG,cAAc,GAAGzD,MAAM,CAAClE,EAAE,GAAG,cAAc,GAAGkE,MAAM,CAAC1B,KAAK,CAACmJ,UAAU,CAACtJ,UAAU,CAACrC,EAAE,GAAG,kCAAkC;QAClO;QACA,MAAMnB,UAAU,CAAC,MAAM,EAAEiC,GAAG,CAAC,CACxBW,IAAI,CAACC,GAAG,IAAI;UACT2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;UACrB,IAAI,CAAC2B,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,4CAA4C;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7HwG,UAAU,CAAC,MAAM;YACblJ,MAAM,CAACiD,QAAQ,CAACqH,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAC1I,KAAK,CAAExC,CAAC,IAAK;UAAA,IAAA6L,aAAA,EAAAC,aAAA;UACZnJ,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;UACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAA2I,aAAA,GAAA7L,CAAC,CAACmD,QAAQ,cAAA0I,aAAA,uBAAVA,aAAA,CAAY3K,IAAI,MAAKkC,SAAS,IAAA0I,aAAA,GAAG9L,CAAC,CAACmD,QAAQ,cAAA2I,aAAA,uBAAVA,aAAA,CAAY5K,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/N,CAAC,CAAC;MACV;IACJ,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,6FAA6F;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;IACzL;EACJ;EAEAiD,kBAAkBA,CAAA,EAAG;IACjB,IAAIlD,OAAO,GACP,iCAAiC;IACrC,IAAImB,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIgE,MAAM,GAAG,IAAI,CAACpI,KAAK,CAACiE,iBAAiB,CAACmE,MAAM,CAACuC,EAAE,IAAIA,EAAE,CAAClJ,KAAK,KAAK,IAAI,CAAC;IACzE,IAAI2G,MAAM,CAACmC,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAACvK,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAAC7C,OAAO,CAAEC,OAAO,IAAK;UACrC,IAAIA,OAAO,CAACsH,IAAI,KAAK,UAAU,EAAE;YAC7BlE,OAAO,CAACzC,IAAI,CAAC;cACTiH,KAAK,EAAE5H,OAAO,CAAC6H,UAAU,GAAG,GAAG,GAAG7H,OAAO,CAAC8H,SAAS;cACnDhJ,KAAK,EAAEkB,OAAO,CAAC+H;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAAC5E,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACvE,QAAQ,CAAC;QACVuD,MAAM,EAAE,IAAI,CAACnD,KAAK,CAACiE,iBAAiB;QACpCF,aAAa,EAAE,IAAI;QACnBI,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA,OAAO;QAChBC,GAAG,EAAErB;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAIoF,MAAM,CAACmC,MAAM,KAAK,IAAI,CAACvK,KAAK,CAACiE,iBAAiB,CAACsG,MAAM,EAAE;MAC9D,IAAImB,QAAQ,GAAG,IAAI,CAAC1L,KAAK,CAACiE,iBAAiB,CAACmE,MAAM,CAACpH,OAAO,IAAIA,OAAO,CAACS,KAAK,CAACkK,QAAQ,KAAK,IAAI,CAAC;MAC9F,IAAID,QAAQ,CAACnB,MAAM,GAAG,CAAC,EAAE;QACrB,IAAImB,QAAQ,CAACnB,MAAM,KAAK,IAAI,CAACvK,KAAK,CAACiE,iBAAiB,CAACsG,MAAM,EAAE;UACzD,IAAI,IAAI,CAACvK,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAAC7C,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAACsH,IAAI,KAAK,QAAQ,EAAE;gBAC3B,IAAIC,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAACxH,OAAO,CAACyH,WAAW,CAAC,GAAGD,QAAQ,CAACxH,OAAO,CAAC0H,YAAY,CAAC,GAAGF,QAAQ,CAACxH,OAAO,CAAC2H,aAAa,CAAC;gBAC1GxE,KAAK,CAACxC,IAAI,CAAC;kBACPiH,KAAK,EAAE5H,OAAO,CAAC6H,UAAU,GAAG,GAAG,GAAG7H,OAAO,CAAC8H,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;kBAC1FzI,KAAK,EAAEkB,OAAO,CAAC+H;gBACnB,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA,IAAI,CAAC5E,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACvE,QAAQ,CAAC;YACVuD,MAAM,EAAE,IAAI,CAACnD,KAAK,CAACiE,iBAAiB;YACpCF,aAAa,EAAE,IAAI;YACnBI,KAAK,EAAEA,KAAK;YACZC,OAAO,EAAEA,OAAO;YAChBC,GAAG,EAAErB;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,qJAAqJ;YAC7JK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAACjD,KAAK,CAAC4D,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAAC5D,KAAK,CAAC4D,QAAQ,CAAC7C,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACsH,IAAI,KAAK,QAAQ,EAAE;cAC3B,IAAIC,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAACxH,OAAO,CAACyH,WAAW,CAAC,GAAGD,QAAQ,CAACxH,OAAO,CAAC0H,YAAY,CAAC,GAAGF,QAAQ,CAACxH,OAAO,CAAC2H,aAAa,CAAC;cAC1GxE,KAAK,CAACxC,IAAI,CAAC;gBACPiH,KAAK,EAAE5H,OAAO,CAAC6H,UAAU,GAAG,GAAG,GAAG7H,OAAO,CAAC8H,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;gBAC1FzI,KAAK,EAAEkB,OAAO,CAAC+H;cACnB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAAC5E,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACvE,QAAQ,CAAC;UACVuD,MAAM,EAAE,IAAI,CAACnD,KAAK,CAACiE,iBAAiB;UACpCF,aAAa,EAAE,IAAI;UACnBI,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,GAAG,EAAErB;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EAEAkD,gBAAgBA,CAACxG,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAEqE,iBAAiB,EAAEtE,CAAC,CAACG;IAAM,CAAC,CAAC;EACjD;;EAEA;EACAwG,mBAAmBA,CAACnD,MAAM,EAAE;IACxB,IAAI,CAACvD,QAAQ,CAAC;MACVuD,MAAM;MACNuB,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAM0B,YAAYA,CAAA,EAAG;IACjB,IAAIxE,OAAO,GAAG,IAAI,CAAC5B,KAAK,CAAC4B,OAAO,CAACwG,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAACpJ,EAAE,KAAK,IAAI,CAACe,KAAK,CAACmD,MAAM,CAAClE,EAC1C,CAAC;IACD,IAAI,CAACW,QAAQ,CAAC;MACVgC,OAAO;MACP8C,kBAAkB,EAAE,KAAK;MACzBvB,MAAM,EAAE,IAAI,CAACnE;IACjB,CAAC,CAAC;IACF,IAAIe,GAAG,GAAG,4BAA4B,GAAG,IAAI,CAACC,KAAK,CAACmD,MAAM,CAAClE,EAAE;IAC7D,MAAMnB,UAAU,CAAC,QAAQ,EAAEiC,GAAG,CAAC,CAC1BW,IAAI,CAACC,GAAG,IAAI;MACT2B,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAAC2B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,kCAAkC;QAC1CK,IAAI,EAAE;MACV,CAAC,CAAC;MACF1C,MAAM,CAACiD,QAAQ,CAACqH,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC1I,KAAK,CAAExC,CAAC,IAAK;MAAA,IAAAiM,aAAA,EAAAC,aAAA;MACZvJ,OAAO,CAACC,GAAG,CAAC5C,CAAC,CAAC;MACd,IAAI,CAAC6C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,wEAAAC,MAAA,CAAqE,EAAA+I,aAAA,GAAAjM,CAAC,CAACmD,QAAQ,cAAA8I,aAAA,uBAAVA,aAAA,CAAY/K,IAAI,MAAKkC,SAAS,IAAA8I,aAAA,GAAGlM,CAAC,CAACmD,QAAQ,cAAA+I,aAAA,uBAAVA,aAAA,CAAYhL,IAAI,GAAGlB,CAAC,CAACqD,OAAO,CAAE;QAC1IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAoD,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACzG,QAAQ,CAAC;MACV8E,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EAqBA8B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACxG,KAAK,CAACH,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACViE,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACrB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAwD,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC7G,QAAQ,CAAC;MACVoE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA0C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9G,QAAQ,CAAC;MACVoE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA8H,MAAMA,CAAA,EAAG;IACL,MAAMC,mBAAmB,gBACrBnN,OAAA,CAACjB,KAAK,CAACqO,QAAQ;MAAAC,QAAA,eACXrN,OAAA;QAAKsN,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DrN,OAAA,CAACT,MAAM;UAAC+N,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAAC3F,iBAAkB;UAAAyF,QAAA,GAAE,GAAC,EAACpO,QAAQ,CAACuO,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMC,kBAAkB,gBACpB7N,OAAA,CAACjB,KAAK,CAACqO,QAAQ;MAAAC,QAAA,eACXrN,OAAA;QAAKsN,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBrN,OAAA;UAAKsN,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBrN,OAAA;YAAKsN,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvCrN,OAAA,CAACT,MAAM;cACH+N,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAC1G,kBAAmB;cAAAwG,QAAA,GAEhC,GAAG,EACHpO,QAAQ,CAACuO,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACT5N,OAAA,CAACP,KAAK;cACFuC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAACmD,MAAO;cAC7BQ,QAAQ,EAAE,IAAI,CAAC3D,KAAK,CAAC2D,QAAS;cAC9BU,GAAG,EAAE,IAAI,CAACrE,KAAK,CAACqE,GAAI;cACpBqI,GAAG,EAAE;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMG,mBAAmB,gBACrB/N,OAAA,CAACjB,KAAK,CAACqO,QAAQ;MAAAC,QAAA,eACXrN,OAAA,CAACT,MAAM;QAAC+N,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACxG,UAAW;QAAAsG,QAAA,GACjE,GAAG,EACHpO,QAAQ,CAACuO,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMI,wBAAwB,gBAC1BhO,OAAA,CAACjB,KAAK,CAACqO,QAAQ;MAAAC,QAAA,gBACXrN,OAAA,CAACT,MAAM;QACHyK,KAAK,EAAC,IAAI;QACViE,IAAI,EAAC,aAAa;QAClBX,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAAC9F;MAAuB;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACF5N,OAAA,CAACT,MAAM;QAAC+N,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC/F,YAAa;QAAA6F,QAAA,GACxD,GAAG,EACHpO,QAAQ,CAACiP,EAAE,EAAE,GAAG;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMO,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACInD,KAAK,EAAE,QAAQ;MACfoD,MAAM,EAAEtP,QAAQ,CAACuP,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,MAAM;MACboD,MAAM,EAAEtP,QAAQ,CAACuD,IAAI;MACrBiM,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,UAAU;MACjBoD,MAAM,EAAEtP,QAAQ,CAAC2P,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,cAAc;MACrBoD,MAAM,EAAEtP,QAAQ,CAAC4P,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,+BAA+B;MACtCoD,MAAM,EAAEtP,QAAQ,CAAC6P,YAAY;MAC7BL,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,gCAAgC;MACvCoD,MAAM,EAAEtP,QAAQ,CAAC8P,SAAS;MAC1BN,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,cAAc;MACrBoD,MAAM,EAAEtP,QAAQ,CAAC+P,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIxD,KAAK,EAAE,SAAS;MAChBoD,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMM,YAAY,GAAG,CACjB;MAAE7I,IAAI,EAAEnH,QAAQ,CAACiQ,OAAO;MAAEjB,IAAI,eAAEjO,OAAA;QAAGsN,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAACxI;IAAe,CAAC,EAC3F;MAAEP,IAAI,EAAEnH,QAAQ,CAACmQ,kBAAkB;MAAEnB,IAAI,eAAEjO,OAAA;QAAGsN,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAACrI,UAAU;MAAEjG,MAAM,EAAE,QAAQ;MAAEwO,OAAO,EAAE;IAAU,CAAC,EAC/I;MAAEjJ,IAAI,EAAEnH,QAAQ,CAACqQ,YAAY;MAAErB,IAAI,eAAEjO,OAAA;QAAGsN,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAAC7K;IAAY,CAAC,EAChG;MAAE8B,IAAI,EAAEnH,QAAQ,CAACsQ,OAAO;MAAEtB,IAAI,eAAEjO,OAAA;QAAGsN,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAAC9H,OAAO;MAAExG,MAAM,EAAE;IAAW,CAAC,EACxG;MAAEuF,IAAI,EAAE,iBAAiB;MAAE6H,IAAI,eAAEjO,OAAA;QAAGsN,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAACxH,aAAa;MAAE9G,MAAM,EAAE;IAAW,CAAC,EAC/G;MAAEuF,IAAI,EAAEnH,QAAQ,CAACuQ,OAAO;MAAEvB,IAAI,eAAEjO,OAAA;QAAGsN,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAACzH;IAAoB,CAAC,CACrG;IACD,IAAI+H,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACrO,KAAK,CAACsE,MAAM,KAAK,EAAE,IAAI,IAAI,CAACtE,KAAK,CAACF,KAAK,KAAK,IAAI,IAAI,IAAI,CAACE,KAAK,CAACuE,MAAM,KAAK,IAAI,EAAE;MACrF8J,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACIzP,OAAA;MAAKsN,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CrN,OAAA,CAACV,KAAK;QAACoQ,GAAG,EAAG3D,EAAE,IAAK,IAAI,CAACnI,KAAK,GAAGmI;MAAG;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvC5N,OAAA,CAACL,GAAG;QAAA8N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEP5N,OAAA;QAAKsN,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCrN,OAAA;UAAAqN,QAAA,EAAKpO,QAAQ,CAAC0Q;QAAsB;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EACL,IAAI,CAACxM,KAAK,CAACH,iBAAiB,KAAK,IAAI,iBAClCjB,OAAA;QAAKsN,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCrN,OAAA;UAAIsN,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtErN,OAAA;YAAIsN,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjErN,OAAA;cAAKsN,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DrN,OAAA;gBAAIsN,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACrN,OAAA;kBAAGsN,SAAS,EAAC,iBAAiB;kBAACsC,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC3O,QAAQ,CAAC4Q,SAAS,EAAC,GAAC;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H5N,OAAA,CAACZ,QAAQ;gBAACkO,SAAS,EAAC,QAAQ;gBAACpM,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;gBAAC6O,OAAO,EAAE,IAAI,CAACpJ,SAAU;gBAACqJ,QAAQ,EAAE,IAAI,CAACjP,iBAAkB;gBAACkP,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACzG,MAAM;gBAAC0G,QAAQ,EAAC,MAAM;gBAACC,YAAY,EAAC;cAA6B;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV5N,OAAA;QAAKsN,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBrN,OAAA,CAACJ,eAAe;UACZ8P,GAAG,EAAG3D,EAAE,IAAK,IAAI,CAACqE,EAAE,GAAGrE,EAAG;UAC1B7K,KAAK,EAAE,IAAI,CAACE,KAAK,CAAC4B,OAAQ;UAC1BM,OAAO,EAAE,IAAI,CAAClC,KAAK,CAACkC,OAAQ;UAC5B6K,MAAM,EAAEA,MAAO;UACfkC,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTtJ,MAAM,EAAE,IAAI,CAACA,MAAO;UACpB9D,KAAK,EAAE,IAAI,CAAChC,KAAK,CAACI,UAAU,CAAC4B,KAAM;UACnCF,YAAY,EAAE,IAAI,CAAC9B,KAAK,CAAC8B,YAAa;UACtCzB,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAK;UACjCgP,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,aAAa,EAAE1B,YAAa;UAC5B2B,mBAAmB,EAAE,IAAK;UAC1BxC,aAAa,EAAC,UAAU;UACxByC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAACnK,cAAe;UAClCoK,SAAS,EAAE,IAAI,CAAC3P,KAAK,CAACiE,iBAAkB;UACxC2L,iBAAiB,EAAGjQ,CAAC,IAAK,IAAI,CAACwG,gBAAgB,CAACxG,CAAC,CAAE;UACnDkQ,gBAAgB,EAAE,IAAK;UACvBC,kBAAkB,EAAE,IAAI,CAAC5J,kBAAmB;UAC5C6J,iBAAiB,EAAElS,QAAQ,CAACqI,kBAAmB;UAC/C8J,oBAAoB,EAAE,CAAC,IAAI,CAAChQ,KAAK,CAACiE,iBAAiB,IAAI,CAAC,IAAI,CAACjE,KAAK,CAACiE,iBAAiB,CAACsG,MAAO;UAC5F0F,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAACzJ,UAAW;UACnC0J,gBAAgB,eAAEvR,OAAA;YAAUsN,SAAS,EAAC,MAAM;YAAClH,IAAI,EAAC;UAAgB;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/E4D,OAAO,EAAC,QAAQ;UAChBrK,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBpB,SAAS,EAAE,IAAI,CAAC3E,KAAK,CAACI,UAAU,CAACuE,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAAC5E,KAAK,CAACI,UAAU,CAACwE,SAAU;UAC3CoB,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBnB,OAAO,EAAE,IAAI,CAAC7E,KAAK,CAACI,UAAU,CAACyE,OAAQ;UACvCwL,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAS;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN5N,OAAA,CAACb,MAAM;QACHwS,OAAO,EAAE,IAAI,CAACvQ,KAAK,CAAC8D,aAAc;QAClCqJ,MAAM,EAAEtP,QAAQ,CAAC2S,MAAO;QACxBC,KAAK;QACLvE,SAAS,EAAC,kBAAkB;QAC5BwE,MAAM,EAAEjE,kBAAmB;QAC3BkE,MAAM,EAAE,IAAI,CAAClL,kBAAmB;QAChCmL,SAAS,EAAE,KAAM;QAAA3E,QAAA,eAEjBrN,OAAA,CAACN,mBAAmB;UAChB6E,MAAM,EAAE,IAAI,CAACnD,KAAK,CAAC2D,QAAS;UAC5B/B,OAAO,EAAE,IAAI,CAAC5B,KAAK,CAACmD,MAAO;UAC3BvC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAACmD,MAAO;UAC7B0N,MAAM,EAAE;QAAK;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAET5N,OAAA,CAACb,MAAM;QACHwS,OAAO,EAAE,IAAI,CAACvQ,KAAK,CAAC+D,aAAc;QAClCoJ,MAAM,EAAE,IAAI,CAACnN,KAAK,CAACqE,GAAI;QACvBoM,KAAK;QACLvE,SAAS,EAAC,kBAAkB;QAC5BwE,MAAM,EAAE/D,mBAAoB;QAC5BgE,MAAM,EAAE,IAAI,CAAChL,UAAW;QAAAsG,QAAA,eAExBrN,OAAA,CAACH,kBAAkB;UAAC0E,MAAM,EAAE,IAAI,CAACnD,KAAK,CAACmD,MAAO;UAACgB,KAAK,EAAE,IAAI,CAACA,KAAM;UAACC,OAAO,EAAE,IAAI,CAACpE,KAAK,CAACoE,OAAQ;UAAC0M,KAAK,EAAE;QAAK;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eAET5N,OAAA,CAACb,MAAM;QACHwS,OAAO,EAAE,IAAI,CAACvQ,KAAK,CAAC0E,kBAAmB;QACvCyI,MAAM,EAAEtP,QAAQ,CAACkT,QAAS;QAC1BN,KAAK;QACLC,MAAM,EAAE9D,wBAAyB;QACjC+D,MAAM,EAAE,IAAI,CAACtK,sBAAuB;QAAA4F,QAAA,eAEpCrN,OAAA;UAAKsN,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCrN,OAAA;YACIsN,SAAS,EAAC,mCAAmC;YAC7CsC,KAAK,EAAE;cAAEwC,QAAQ,EAAE;YAAO;UAAE;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAACxM,KAAK,CAACmD,MAAM,iBACdvE,OAAA;YAAAqN,QAAA,GACKpO,QAAQ,CAACoT,YAAY,EAAC,GAAC,eAAArS,OAAA;cAAAqN,QAAA,GAAI,IAAI,CAACjM,KAAK,CAACmD,MAAM,CAAChC,MAAM,EAAC,GAAC;YAAA;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT5N,OAAA,CAACb,MAAM;QAACwS,OAAO,EAAE,IAAI,CAACvQ,KAAK,CAAC6D,YAAa;QAACsJ,MAAM,EAAEtP,QAAQ,CAACqT,iBAAkB;QAACT,KAAK;QAACvE,SAAS,EAAC,kBAAkB;QAACyE,MAAM,EAAE,IAAI,CAACnK,iBAAkB;QAACkK,MAAM,EAAE3E,mBAAoB;QAAAE,QAAA,GACxK,IAAI,CAACjM,KAAK,CAACkE,SAAS,iBACjBtF,OAAA,CAACX,UAAU;UAACkT,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAAhF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhG5N,OAAA;UAAKsN,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DrN,OAAA;YAAIsN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACrN,OAAA;cAAGsN,SAAS,EAAC,iBAAiB;cAACsC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC3O,QAAQ,CAAC4Q,SAAS;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH5N,OAAA;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5N,OAAA,CAACZ,QAAQ;YAACkO,SAAS,EAAC,QAAQ;YAACpM,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;YAAC6O,OAAO,EAAE,IAAI,CAACpJ,SAAU;YAACqJ,QAAQ,EAAE,IAAI,CAACjP,iBAAkB;YAACkP,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACzG,MAAM;YAAC0G,QAAQ,EAAC,MAAM;YAACC,YAAY,EAAC;UAA6B;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT5N,OAAA,CAACR,OAAO;QAACmS,OAAO,EAAE,IAAI,CAACvQ,KAAK,CAACgE,aAAc;QAACsN,QAAQ,EAAC,MAAM;QAACX,MAAM,EAAE,IAAI,CAACjK,WAAY;QAAAuF,QAAA,gBACjFrN,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACiN,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKrN,OAAA;YAAGsN,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C5N,OAAA;YAAIsN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACrN,OAAA;cAAGsN,SAAS,EAAC,mBAAmB;cAACsC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC3O,QAAQ,CAAC0T,MAAM;UAAA;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G5N,OAAA,CAACT,MAAM;YAACc,EAAE,EAAC,iBAAiB;YAACiN,SAAS,EAAEmC,WAAY;YAAClC,OAAO,EAAE,IAAI,CAACvG,KAAM;YAAAqG,QAAA,gBAACrN,OAAA;cAAGsN,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA5N,OAAA;cAAAqN,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN5N,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACiN,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DrN,OAAA;YAAIsN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACrN,OAAA;cAAGsN,SAAS,EAAC,mBAAmB;cAACsC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC3O,QAAQ,CAAC0T,MAAM;UAAA;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G5N,OAAA,CAACT,MAAM;YAACc,EAAE,EAAC,kBAAkB;YAACiN,SAAS,EAAEmC,WAAY;YAAClC,OAAO,EAAE,IAAI,CAACtG,SAAU;YAAAoG,QAAA,gBAACrN,OAAA;cAAGsN,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA5N,OAAA;cAAAqN,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN5N,OAAA;UAAAyN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5N,OAAA,CAACZ,QAAQ;UAACkO,SAAS,EAAC,OAAO;UAACpM,KAAK,EAAE,IAAI,CAACE,KAAK,CAACC,gBAAiB;UAACyO,OAAO,EAAE,IAAI,CAACtJ,SAAU;UAACuJ,QAAQ,EAAE,IAAI,CAAC5J,SAAU;UAAC6J,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,mBAAmB;UAACzG,MAAM;UAAC0G,QAAQ,EAAC;QAAM;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe3N,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}