{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport copy from 'copy-to-clipboard';\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport ResizeObserver from 'rc-resize-observer';\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { ConfigContext } from '../../config-provider';\nimport { useLocaleReceiver } from '../../locale-provider/LocaleReceiver';\nimport TransButton from '../../_util/transButton';\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport Tooltip from '../../tooltip';\nimport Typography from '../Typography';\nimport Editable from '../Editable';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport useUpdatedEffect from '../hooks/useUpdatedEffect';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nfunction wrapperDecorations(_ref, content) {\n  var mark = _ref.mark,\n    code = _ref.code,\n    underline = _ref.underline,\n    del = _ref[\"delete\"],\n    strong = _ref.strong,\n    keyboard = _ref.keyboard,\n    italic = _ref.italic;\n  var currentContent = content;\n  function wrap(needed, tag) {\n    if (!needed) return;\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap(strong, 'strong');\n  wrap(underline, 'u');\n  wrap(del, 'del');\n  wrap(code, 'code');\n  wrap(mark, 'mark');\n  wrap(keyboard, 'kbd');\n  wrap(italic, 'i');\n  return currentContent;\n}\nfunction getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\nfunction toList(val) {\n  return Array.isArray(val) ? val : [val];\n}\nvar ELLIPSIS_STR = '...';\nvar Base = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    type = props.type,\n    disabled = props.disabled,\n    children = props.children,\n    ellipsis = props.ellipsis,\n    editable = props.editable,\n    copyable = props.copyable,\n    component = props.component,\n    title = props.title,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var textLocale = useLocaleReceiver('Text')[0]; // Force TS get this\n\n  var typographyRef = React.useRef(null);\n  var editIconRef = React.useRef(null); // ============================ MISC ============================\n\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var textProps = omit(restProps, ['mark', 'code', 'delete', 'underline', 'strong', 'keyboard', 'italic']); // ========================== Editable ==========================\n\n  var _useMergedConfig = useMergedConfig(editable),\n    _useMergedConfig2 = _slicedToArray(_useMergedConfig, 2),\n    enableEdit = _useMergedConfig2[0],\n    editConfig = _useMergedConfig2[1];\n  var _useMergedState = useMergedState(false, {\n      value: editConfig.editing\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    editing = _useMergedState2[0],\n    setEditing = _useMergedState2[1];\n  var _editConfig$triggerTy = editConfig.triggerType,\n    triggerType = _editConfig$triggerTy === void 0 ? ['icon'] : _editConfig$triggerTy;\n  var triggerEdit = function triggerEdit(edit) {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  }; // Focus edit icon when back\n\n  useUpdatedEffect(function () {\n    var _a;\n    if (!editing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  var onEditClick = function onEditClick(e) {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  var onEditChange = function onEditChange(value) {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  var onEditCancel = function onEditCancel() {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  }; // ========================== Copyable ==========================\n\n  var _useMergedConfig3 = useMergedConfig(copyable),\n    _useMergedConfig4 = _slicedToArray(_useMergedConfig3, 2),\n    enableCopy = _useMergedConfig4[0],\n    copyConfig = _useMergedConfig4[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    copied = _React$useState2[0],\n    setCopied = _React$useState2[1];\n  var copyIdRef = React.useRef();\n  var cleanCopyId = function cleanCopyId() {\n    clearTimeout(copyIdRef.current);\n  };\n  var onCopyClick = function onCopyClick(e) {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    copy(copyConfig.text || String(children) || '');\n    setCopied(true); // Trigger tips update\n\n    cleanCopyId();\n    copyIdRef.current = setTimeout(function () {\n      setCopied(false);\n    }, 3000);\n    (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n  };\n  React.useEffect(function () {\n    return cleanCopyId;\n  }, []); // ========================== Ellipsis ==========================\n\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    isLineClampSupport = _React$useState4[0],\n    setIsLineClampSupport = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isTextOverflowSupport = _React$useState6[0],\n    setIsTextOverflowSupport = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    expanded = _React$useState8[0],\n    setExpanded = _React$useState8[1];\n  var _React$useState9 = React.useState(false),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    isJsEllipsis = _React$useState10[0],\n    setIsJsEllipsis = _React$useState10[1];\n  var _React$useState11 = React.useState(false),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    isNativeEllipsis = _React$useState12[0],\n    setIsNativeEllipsis = _React$useState12[1];\n  var _useMergedConfig5 = useMergedConfig(ellipsis, {\n      expandable: false\n    }),\n    _useMergedConfig6 = _slicedToArray(_useMergedConfig5, 2),\n    enableEllipsis = _useMergedConfig6[0],\n    ellipsisConfig = _useMergedConfig6[1];\n  var mergedEnableEllipsis = enableEllipsis && !expanded; // Shared prop to reduce bundle size\n\n  var _ellipsisConfig$rows = ellipsisConfig.rows,\n    rows = _ellipsisConfig$rows === void 0 ? 1 : _ellipsisConfig$rows;\n  var needMeasureEllipsis = React.useMemo(function () {\n    return (\n      // Disable ellipsis\n      !mergedEnableEllipsis ||\n      // Provide suffix\n      ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n      // Can't use css ellipsis since we need to provide the place for button\n      ellipsisConfig.expandable || enableEdit || enableCopy\n    );\n  }, [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useIsomorphicLayoutEffect(function () {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  var cssEllipsis = React.useMemo(function () {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  var isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  var cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  var cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis; // >>>>> Expand\n\n  var onExpandClick = function onExpandClick(e) {\n    var _a;\n    setExpanded(true);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e);\n  };\n  var _React$useState13 = React.useState(0),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    ellipsisWidth = _React$useState14[0],\n    setEllipsisWidth = _React$useState14[1];\n  var onResize = function onResize(_ref2) {\n    var offsetWidth = _ref2.offsetWidth;\n    setEllipsisWidth(offsetWidth);\n  }; // >>>>> JS Ellipsis\n\n  var onJsEllipsis = function onJsEllipsis(jsEllipsis) {\n    var _a;\n    setIsJsEllipsis(jsEllipsis); // Trigger if changed\n\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  }; // >>>>> Native ellipsis\n\n  React.useEffect(function () {\n    var textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      var currentEllipsis = cssLineClamp ? textEle.offsetHeight < textEle.scrollHeight : textEle.offsetWidth < textEle.scrollWidth;\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp]); // ========================== Tooltip ===========================\n\n  var tooltipTitle = ellipsisConfig.tooltip === true ? children : ellipsisConfig.tooltip;\n  var topAriaLabel = React.useMemo(function () {\n    var isValid = function isValid(val) {\n      return ['string', 'number'].includes(_typeof(val));\n    };\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    if (isValid(children)) {\n      return children;\n    }\n    if (isValid(title)) {\n      return title;\n    }\n    if (isValid(tooltipTitle)) {\n      return tooltipTitle;\n    }\n    return undefined;\n  }, [enableEllipsis, cssEllipsis, title, tooltipTitle, isMergedEllipsis]); // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  } // >>>>>>>>>>> Typography\n  // Expand\n\n  var renderExpand = function renderExpand() {\n    var expandable = ellipsisConfig.expandable,\n      symbol = ellipsisConfig.symbol;\n    if (!expandable) return null;\n    var expandContent;\n    if (symbol) {\n      expandContent = symbol;\n    } else {\n      expandContent = textLocale.expand;\n    }\n    return /*#__PURE__*/React.createElement(\"a\", {\n      key: \"expand\",\n      className: \"\".concat(prefixCls, \"-expand\"),\n      onClick: onExpandClick,\n      \"aria-label\": textLocale.expand\n    }, expandContent);\n  }; // Edit\n\n  var renderEdit = function renderEdit() {\n    if (!enableEdit) return;\n    var icon = editConfig.icon,\n      tooltip = editConfig.tooltip;\n    var editTitle = toArray(tooltip)[0] || textLocale.edit;\n    var ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      ref: editIconRef,\n      className: \"\".concat(prefixCls, \"-edit\"),\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    }))) : null;\n  }; // Copy\n\n  var renderCopy = function renderCopy() {\n    if (!enableCopy) return;\n    var tooltips = copyConfig.tooltips,\n      icon = copyConfig.icon;\n    var tooltipNodes = toList(tooltips);\n    var iconNodes = toList(icon);\n    var copyTitle = copied ? getNode(tooltipNodes[1], textLocale.copied) : getNode(tooltipNodes[0], textLocale.copy);\n    var systemStr = copied ? textLocale.copied : textLocale.copy;\n    var ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n    return /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"copy\",\n      title: copyTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      className: classNames(\"\".concat(prefixCls, \"-copy\"), copied && \"\".concat(prefixCls, \"-copy-success\")),\n      onClick: onCopyClick,\n      \"aria-label\": ariaLabel\n    }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n  };\n  var renderOperations = function renderOperations(renderExpanded) {\n    return [renderExpanded && renderExpand(), renderEdit(), renderCopy()];\n  };\n  var renderEllipsis = function renderEllipsis(needEllipsis) {\n    return [needEllipsis && /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      key: \"ellipsis\"\n    }, ELLIPSIS_STR), ellipsisConfig.suffix, renderOperations(needEllipsis)];\n  };\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis || cssEllipsis\n  }, function (resizeRef) {\n    var _classNames;\n    return /*#__PURE__*/React.createElement(EllipsisTooltip, {\n      title: tooltipTitle,\n      enabledEllipsis: mergedEnableEllipsis,\n      isEllipsis: isMergedEllipsis\n    }, /*#__PURE__*/React.createElement(Typography, _extends({\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(type), type), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis\"), enableEllipsis), _defineProperty(_classNames, \"\".concat(prefixCls, \"-single-line\"), mergedEnableEllipsis && rows === 1), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-single-line\"), cssTextOverflow), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-multiple-line\"), cssLineClamp), _classNames), className),\n      style: _extends(_extends({}, style), {\n        WebkitLineClamp: cssLineClamp ? rows : undefined\n      }),\n      component: component,\n      ref: composeRef(resizeRef, typographyRef, ref),\n      direction: direction,\n      onClick: triggerType.includes('text') ? onEditClick : null,\n      \"aria-label\": topAriaLabel,\n      title: title\n    }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n      enabledMeasure: mergedEnableEllipsis && !cssEllipsis,\n      text: children,\n      rows: rows,\n      width: ellipsisWidth,\n      onEllipsis: onJsEllipsis\n    }, function (node, needEllipsis) {\n      var renderNode = node;\n      if (node.length && needEllipsis && topAriaLabel) {\n        renderNode = /*#__PURE__*/React.createElement(\"span\", {\n          key: \"show-content\",\n          \"aria-hidden\": true\n        }, renderNode);\n      }\n      var wrappedContext = wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, renderNode, renderEllipsis(needEllipsis)));\n      return wrappedContext;\n    })));\n  });\n});\nexport default Base;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "useMergedState", "toArray", "copy", "omit", "composeRef", "EditOutlined", "CheckOutlined", "CopyOutlined", "ResizeObserver", "useIsomorphicLayoutEffect", "ConfigContext", "useLocaleReceiver", "TransButton", "isStyleSupport", "<PERSON><PERSON><PERSON>", "Typography", "Editable", "useMergedConfig", "useUpdatedEffect", "El<PERSON><PERSON>", "EllipsisTooltip", "wrapperDecorations", "_ref", "content", "mark", "code", "underline", "del", "strong", "keyboard", "italic", "currentC<PERSON>nt", "wrap", "needed", "tag", "createElement", "getNode", "dom", "defaultNode", "needDom", "undefined", "toList", "val", "Array", "isArray", "ELLIPSIS_STR", "Base", "forwardRef", "props", "ref", "customizePrefixCls", "prefixCls", "className", "style", "type", "disabled", "children", "ellipsis", "editable", "copyable", "component", "title", "restProps", "_React$useContext", "useContext", "getPrefixCls", "direction", "textLocale", "typographyRef", "useRef", "editIconRef", "textProps", "_useMergedConfig", "_useMergedConfig2", "enableEdit", "editConfig", "_useMergedState", "value", "editing", "_useMergedState2", "setEditing", "_editConfig$triggerTy", "triggerType", "triggerEdit", "edit", "_a", "onStart", "current", "focus", "onEditClick", "preventDefault", "onEditChange", "onChange", "onEditCancel", "onCancel", "_useMergedConfig3", "_useMergedConfig4", "enableCopy", "copyConfig", "_React$useState", "useState", "_React$useState2", "copied", "setCopied", "copyIdRef", "cleanCopyId", "clearTimeout", "onCopyClick", "stopPropagation", "text", "String", "setTimeout", "onCopy", "useEffect", "_React$useState3", "_React$useState4", "isLineClampSupport", "setIsLineClampSupport", "_React$useState5", "_React$useState6", "isTextOverflowSupport", "setIsTextOverflowSupport", "_React$useState7", "_React$useState8", "expanded", "setExpanded", "_React$useState9", "_React$useState10", "isJsEllipsis", "setIsJsEllipsis", "_React$useState11", "_React$useState12", "isNativeEllipsis", "setIsNativeEllipsis", "_useMergedConfig5", "expandable", "_useMergedConfig6", "enableEllipsis", "ellipsisConfig", "mergedEnableEllipsis", "_ellipsisConfig$rows", "rows", "needMeasureEllipsis", "useMemo", "suffix", "onEllipsis", "cssEllipsis", "isMergedEllipsis", "cssTextOverflow", "cssLineClamp", "onExpandClick", "onExpand", "_React$useState13", "_React$useState14", "ellip<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onResize", "_ref2", "offsetWidth", "onJsEllipsis", "js<PERSON><PERSON><PERSON>", "textEle", "currentEllipsis", "offsetHeight", "scrollHeight", "scrollWidth", "tooltipTitle", "tooltip", "topAriaLabel", "<PERSON><PERSON><PERSON><PERSON>", "includes", "onSave", "onEnd", "max<PERSON><PERSON><PERSON>", "autoSize", "enterIcon", "renderExpand", "symbol", "expandContent", "expand", "key", "concat", "onClick", "renderEdit", "icon", "editTitle", "aria<PERSON><PERSON><PERSON>", "role", "renderCopy", "tooltips", "tooltipNodes", "iconNodes", "copyTitle", "systemStr", "renderOperations", "renderExpanded", "renderEllipsis", "needEllipsis", "resizeRef", "_classNames", "enabledEllipsis", "isEllipsis", "WebkitLineClamp", "enabledMeasure", "width", "node", "renderNode", "wrappedContext", "Fragment"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Base/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport copy from 'copy-to-clipboard';\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport ResizeObserver from 'rc-resize-observer';\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { ConfigContext } from '../../config-provider';\nimport { useLocaleReceiver } from '../../locale-provider/LocaleReceiver';\nimport TransButton from '../../_util/transButton';\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport Tooltip from '../../tooltip';\nimport Typography from '../Typography';\nimport Editable from '../Editable';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport useUpdatedEffect from '../hooks/useUpdatedEffect';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\n\nfunction wrapperDecorations(_ref, content) {\n  var mark = _ref.mark,\n      code = _ref.code,\n      underline = _ref.underline,\n      del = _ref[\"delete\"],\n      strong = _ref.strong,\n      keyboard = _ref.keyboard,\n      italic = _ref.italic;\n  var currentContent = content;\n\n  function wrap(needed, tag) {\n    if (!needed) return;\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n\n  wrap(strong, 'strong');\n  wrap(underline, 'u');\n  wrap(del, 'del');\n  wrap(code, 'code');\n  wrap(mark, 'mark');\n  wrap(keyboard, 'kbd');\n  wrap(italic, 'i');\n  return currentContent;\n}\n\nfunction getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n\n  return dom || needDom && defaultNode;\n}\n\nfunction toList(val) {\n  return Array.isArray(val) ? val : [val];\n}\n\nvar ELLIPSIS_STR = '...';\nvar Base = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style,\n      type = props.type,\n      disabled = props.disabled,\n      children = props.children,\n      ellipsis = props.ellipsis,\n      editable = props.editable,\n      copyable = props.copyable,\n      component = props.component,\n      title = props.title,\n      restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var textLocale = useLocaleReceiver('Text')[0]; // Force TS get this\n\n  var typographyRef = React.useRef(null);\n  var editIconRef = React.useRef(null); // ============================ MISC ============================\n\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var textProps = omit(restProps, ['mark', 'code', 'delete', 'underline', 'strong', 'keyboard', 'italic']); // ========================== Editable ==========================\n\n  var _useMergedConfig = useMergedConfig(editable),\n      _useMergedConfig2 = _slicedToArray(_useMergedConfig, 2),\n      enableEdit = _useMergedConfig2[0],\n      editConfig = _useMergedConfig2[1];\n\n  var _useMergedState = useMergedState(false, {\n    value: editConfig.editing\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      editing = _useMergedState2[0],\n      setEditing = _useMergedState2[1];\n\n  var _editConfig$triggerTy = editConfig.triggerType,\n      triggerType = _editConfig$triggerTy === void 0 ? ['icon'] : _editConfig$triggerTy;\n\n  var triggerEdit = function triggerEdit(edit) {\n    var _a;\n\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n\n    setEditing(edit);\n  }; // Focus edit icon when back\n\n\n  useUpdatedEffect(function () {\n    var _a;\n\n    if (!editing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n\n  var onEditClick = function onEditClick(e) {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n\n  var onEditChange = function onEditChange(value) {\n    var _a;\n\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n\n  var onEditCancel = function onEditCancel() {\n    var _a;\n\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  }; // ========================== Copyable ==========================\n\n\n  var _useMergedConfig3 = useMergedConfig(copyable),\n      _useMergedConfig4 = _slicedToArray(_useMergedConfig3, 2),\n      enableCopy = _useMergedConfig4[0],\n      copyConfig = _useMergedConfig4[1];\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      copied = _React$useState2[0],\n      setCopied = _React$useState2[1];\n\n  var copyIdRef = React.useRef();\n\n  var cleanCopyId = function cleanCopyId() {\n    clearTimeout(copyIdRef.current);\n  };\n\n  var onCopyClick = function onCopyClick(e) {\n    var _a;\n\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    copy(copyConfig.text || String(children) || '');\n    setCopied(true); // Trigger tips update\n\n    cleanCopyId();\n    copyIdRef.current = setTimeout(function () {\n      setCopied(false);\n    }, 3000);\n    (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n  };\n\n  React.useEffect(function () {\n    return cleanCopyId;\n  }, []); // ========================== Ellipsis ==========================\n\n  var _React$useState3 = React.useState(false),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      isLineClampSupport = _React$useState4[0],\n      setIsLineClampSupport = _React$useState4[1];\n\n  var _React$useState5 = React.useState(false),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      isTextOverflowSupport = _React$useState6[0],\n      setIsTextOverflowSupport = _React$useState6[1];\n\n  var _React$useState7 = React.useState(false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      expanded = _React$useState8[0],\n      setExpanded = _React$useState8[1];\n\n  var _React$useState9 = React.useState(false),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      isJsEllipsis = _React$useState10[0],\n      setIsJsEllipsis = _React$useState10[1];\n\n  var _React$useState11 = React.useState(false),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      isNativeEllipsis = _React$useState12[0],\n      setIsNativeEllipsis = _React$useState12[1];\n\n  var _useMergedConfig5 = useMergedConfig(ellipsis, {\n    expandable: false\n  }),\n      _useMergedConfig6 = _slicedToArray(_useMergedConfig5, 2),\n      enableEllipsis = _useMergedConfig6[0],\n      ellipsisConfig = _useMergedConfig6[1];\n\n  var mergedEnableEllipsis = enableEllipsis && !expanded; // Shared prop to reduce bundle size\n\n  var _ellipsisConfig$rows = ellipsisConfig.rows,\n      rows = _ellipsisConfig$rows === void 0 ? 1 : _ellipsisConfig$rows;\n  var needMeasureEllipsis = React.useMemo(function () {\n    return (// Disable ellipsis\n      !mergedEnableEllipsis || // Provide suffix\n      ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis || // Can't use css ellipsis since we need to provide the place for button\n      ellipsisConfig.expandable || enableEdit || enableCopy\n    );\n  }, [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useIsomorphicLayoutEffect(function () {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  var cssEllipsis = React.useMemo(function () {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  var isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  var cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  var cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis; // >>>>> Expand\n\n  var onExpandClick = function onExpandClick(e) {\n    var _a;\n\n    setExpanded(true);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e);\n  };\n\n  var _React$useState13 = React.useState(0),\n      _React$useState14 = _slicedToArray(_React$useState13, 2),\n      ellipsisWidth = _React$useState14[0],\n      setEllipsisWidth = _React$useState14[1];\n\n  var onResize = function onResize(_ref2) {\n    var offsetWidth = _ref2.offsetWidth;\n    setEllipsisWidth(offsetWidth);\n  }; // >>>>> JS Ellipsis\n\n\n  var onJsEllipsis = function onJsEllipsis(jsEllipsis) {\n    var _a;\n\n    setIsJsEllipsis(jsEllipsis); // Trigger if changed\n\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  }; // >>>>> Native ellipsis\n\n\n  React.useEffect(function () {\n    var textEle = typographyRef.current;\n\n    if (enableEllipsis && cssEllipsis && textEle) {\n      var currentEllipsis = cssLineClamp ? textEle.offsetHeight < textEle.scrollHeight : textEle.offsetWidth < textEle.scrollWidth;\n\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp]); // ========================== Tooltip ===========================\n\n  var tooltipTitle = ellipsisConfig.tooltip === true ? children : ellipsisConfig.tooltip;\n  var topAriaLabel = React.useMemo(function () {\n    var isValid = function isValid(val) {\n      return ['string', 'number'].includes(_typeof(val));\n    };\n\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n\n    if (isValid(children)) {\n      return children;\n    }\n\n    if (isValid(title)) {\n      return title;\n    }\n\n    if (isValid(tooltipTitle)) {\n      return tooltipTitle;\n    }\n\n    return undefined;\n  }, [enableEllipsis, cssEllipsis, title, tooltipTitle, isMergedEllipsis]); // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  } // >>>>>>>>>>> Typography\n  // Expand\n\n\n  var renderExpand = function renderExpand() {\n    var expandable = ellipsisConfig.expandable,\n        symbol = ellipsisConfig.symbol;\n    if (!expandable) return null;\n    var expandContent;\n\n    if (symbol) {\n      expandContent = symbol;\n    } else {\n      expandContent = textLocale.expand;\n    }\n\n    return /*#__PURE__*/React.createElement(\"a\", {\n      key: \"expand\",\n      className: \"\".concat(prefixCls, \"-expand\"),\n      onClick: onExpandClick,\n      \"aria-label\": textLocale.expand\n    }, expandContent);\n  }; // Edit\n\n\n  var renderEdit = function renderEdit() {\n    if (!enableEdit) return;\n    var icon = editConfig.icon,\n        tooltip = editConfig.tooltip;\n    var editTitle = toArray(tooltip)[0] || textLocale.edit;\n    var ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      ref: editIconRef,\n      className: \"\".concat(prefixCls, \"-edit\"),\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    }))) : null;\n  }; // Copy\n\n\n  var renderCopy = function renderCopy() {\n    if (!enableCopy) return;\n    var tooltips = copyConfig.tooltips,\n        icon = copyConfig.icon;\n    var tooltipNodes = toList(tooltips);\n    var iconNodes = toList(icon);\n    var copyTitle = copied ? getNode(tooltipNodes[1], textLocale.copied) : getNode(tooltipNodes[0], textLocale.copy);\n    var systemStr = copied ? textLocale.copied : textLocale.copy;\n    var ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n    return /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"copy\",\n      title: copyTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      className: classNames(\"\".concat(prefixCls, \"-copy\"), copied && \"\".concat(prefixCls, \"-copy-success\")),\n      onClick: onCopyClick,\n      \"aria-label\": ariaLabel\n    }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n  };\n\n  var renderOperations = function renderOperations(renderExpanded) {\n    return [renderExpanded && renderExpand(), renderEdit(), renderCopy()];\n  };\n\n  var renderEllipsis = function renderEllipsis(needEllipsis) {\n    return [needEllipsis && /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      key: \"ellipsis\"\n    }, ELLIPSIS_STR), ellipsisConfig.suffix, renderOperations(needEllipsis)];\n  };\n\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis || cssEllipsis\n  }, function (resizeRef) {\n    var _classNames;\n\n    return /*#__PURE__*/React.createElement(EllipsisTooltip, {\n      title: tooltipTitle,\n      enabledEllipsis: mergedEnableEllipsis,\n      isEllipsis: isMergedEllipsis\n    }, /*#__PURE__*/React.createElement(Typography, _extends({\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(type), type), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis\"), enableEllipsis), _defineProperty(_classNames, \"\".concat(prefixCls, \"-single-line\"), mergedEnableEllipsis && rows === 1), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-single-line\"), cssTextOverflow), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-multiple-line\"), cssLineClamp), _classNames), className),\n      style: _extends(_extends({}, style), {\n        WebkitLineClamp: cssLineClamp ? rows : undefined\n      }),\n      component: component,\n      ref: composeRef(resizeRef, typographyRef, ref),\n      direction: direction,\n      onClick: triggerType.includes('text') ? onEditClick : null,\n      \"aria-label\": topAriaLabel,\n      title: title\n    }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n      enabledMeasure: mergedEnableEllipsis && !cssEllipsis,\n      text: children,\n      rows: rows,\n      width: ellipsisWidth,\n      onEllipsis: onJsEllipsis\n    }, function (node, needEllipsis) {\n      var renderNode = node;\n\n      if (node.length && needEllipsis && topAriaLabel) {\n        renderNode = /*#__PURE__*/React.createElement(\"span\", {\n          key: \"show-content\",\n          \"aria-hidden\": true\n        }, renderNode);\n      }\n\n      var wrappedContext = wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, renderNode, renderEllipsis(needEllipsis)));\n      return wrappedContext;\n    })));\n  });\n});\nexport default Base;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AAErE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,yBAAyB,MAAM,kCAAkC;AACxE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAE/C,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAC1BC,GAAG,GAAGL,IAAI,CAAC,QAAQ,CAAC;IACpBM,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,MAAM,GAAGR,IAAI,CAACQ,MAAM;EACxB,IAAIC,cAAc,GAAGR,OAAO;EAE5B,SAASS,IAAIA,CAACC,MAAM,EAAEC,GAAG,EAAE;IACzB,IAAI,CAACD,MAAM,EAAE;IACbF,cAAc,GAAG,aAAajC,KAAK,CAACqC,aAAa,CAACD,GAAG,EAAE,CAAC,CAAC,EAAEH,cAAc,CAAC;EAC5E;EAEAC,IAAI,CAACJ,MAAM,EAAE,QAAQ,CAAC;EACtBI,IAAI,CAACN,SAAS,EAAE,GAAG,CAAC;EACpBM,IAAI,CAACL,GAAG,EAAE,KAAK,CAAC;EAChBK,IAAI,CAACP,IAAI,EAAE,MAAM,CAAC;EAClBO,IAAI,CAACR,IAAI,EAAE,MAAM,CAAC;EAClBQ,IAAI,CAACH,QAAQ,EAAE,KAAK,CAAC;EACrBG,IAAI,CAACF,MAAM,EAAE,GAAG,CAAC;EACjB,OAAOC,cAAc;AACvB;AAEA,SAASK,OAAOA,CAACC,GAAG,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC1C,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE;IACrC,OAAOF,WAAW;EACpB;EAEA,OAAOD,GAAG,IAAIE,OAAO,IAAID,WAAW;AACtC;AAEA,SAASG,MAAMA,CAACC,GAAG,EAAE;EACnB,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACzC;AAEA,IAAIG,YAAY,GAAG,KAAK;AACxB,IAAIC,IAAI,GAAG,aAAahD,KAAK,CAACiD,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACpCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,SAAS,GAAG9E,MAAM,CAACgE,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;EAE5J,IAAIe,iBAAiB,GAAGjE,KAAK,CAACkE,UAAU,CAACtD,aAAa,CAAC;IACnDuD,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,UAAU,GAAGxD,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/C,IAAIyD,aAAa,GAAGtE,KAAK,CAACuE,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIC,WAAW,GAAGxE,KAAK,CAACuE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtC,IAAIlB,SAAS,GAAGc,YAAY,CAAC,YAAY,EAAEf,kBAAkB,CAAC;EAC9D,IAAIqB,SAAS,GAAGpE,IAAI,CAAC2D,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE1G,IAAIU,gBAAgB,GAAGvD,eAAe,CAACyC,QAAQ,CAAC;IAC5Ce,iBAAiB,GAAG1F,cAAc,CAACyF,gBAAgB,EAAE,CAAC,CAAC;IACvDE,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAErC,IAAIG,eAAe,GAAG5E,cAAc,CAAC,KAAK,EAAE;MAC1C6E,KAAK,EAAEF,UAAU,CAACG;IACpB,CAAC,CAAC;IACEC,gBAAgB,GAAGhG,cAAc,CAAC6F,eAAe,EAAE,CAAC,CAAC;IACrDE,OAAO,GAAGC,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIE,qBAAqB,GAAGN,UAAU,CAACO,WAAW;IAC9CA,WAAW,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAGA,qBAAqB;EAErF,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;IAC3C,IAAIC,EAAE;IAEN,IAAID,IAAI,EAAE;MACR,CAACC,EAAE,GAAGV,UAAU,CAACW,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,IAAI,CAACmF,UAAU,CAAC;IACpF;IAEAK,UAAU,CAACI,IAAI,CAAC;EAClB,CAAC,CAAC,CAAC;;EAGHlE,gBAAgB,CAAC,YAAY;IAC3B,IAAImE,EAAE;IAEN,IAAI,CAACP,OAAO,EAAE;MACZ,CAACO,EAAE,GAAGf,WAAW,CAACiB,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,CAAC,CAAC;IAC5E;EACF,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC;EAEb,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAACvG,CAAC,EAAE;IACxCA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACwG,cAAc,CAAC,CAAC;IACxDP,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,IAAIQ,YAAY,GAAG,SAASA,YAAYA,CAACd,KAAK,EAAE;IAC9C,IAAIQ,EAAE;IAEN,CAACA,EAAE,GAAGV,UAAU,CAACiB,QAAQ,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,IAAI,CAACmF,UAAU,EAAEE,KAAK,CAAC;IAC1FM,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,IAAIU,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIR,EAAE;IAEN,CAACA,EAAE,GAAGV,UAAU,CAACmB,QAAQ,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,IAAI,CAACmF,UAAU,CAAC;IACnFQ,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC,CAAC,CAAC;;EAGH,IAAIY,iBAAiB,GAAG9E,eAAe,CAAC0C,QAAQ,CAAC;IAC7CqC,iBAAiB,GAAGjH,cAAc,CAACgH,iBAAiB,EAAE,CAAC,CAAC;IACxDE,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAErC,IAAIG,eAAe,GAAGrG,KAAK,CAACsG,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAGtH,cAAc,CAACoH,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEnC,IAAIG,SAAS,GAAG1G,KAAK,CAACuE,MAAM,CAAC,CAAC;EAE9B,IAAIoC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCC,YAAY,CAACF,SAAS,CAACjB,OAAO,CAAC;EACjC,CAAC;EAED,IAAIoB,WAAW,GAAG,SAASA,WAAWA,CAACzH,CAAC,EAAE;IACxC,IAAImG,EAAE;IAENnG,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACwG,cAAc,CAAC,CAAC;IACxDxG,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0H,eAAe,CAAC,CAAC;IACzD1G,IAAI,CAACgG,UAAU,CAACW,IAAI,IAAIC,MAAM,CAACtD,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/C+C,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEjBE,WAAW,CAAC,CAAC;IACbD,SAAS,CAACjB,OAAO,GAAGwB,UAAU,CAAC,YAAY;MACzCR,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,EAAE,IAAI,CAAC;IACR,CAAClB,EAAE,GAAGa,UAAU,CAACc,MAAM,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,IAAI,CAAC0G,UAAU,EAAEhH,CAAC,CAAC;EACtF,CAAC;EAEDY,KAAK,CAACmH,SAAS,CAAC,YAAY;IAC1B,OAAOR,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIS,gBAAgB,GAAGpH,KAAK,CAACsG,QAAQ,CAAC,KAAK,CAAC;IACxCe,gBAAgB,GAAGpI,cAAc,CAACmI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE/C,IAAIG,gBAAgB,GAAGxH,KAAK,CAACsG,QAAQ,CAAC,KAAK,CAAC;IACxCmB,gBAAgB,GAAGxI,cAAc,CAACuI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,qBAAqB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3CE,wBAAwB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAElD,IAAIG,gBAAgB,GAAG5H,KAAK,CAACsG,QAAQ,CAAC,KAAK,CAAC;IACxCuB,gBAAgB,GAAG5I,cAAc,CAAC2I,gBAAgB,EAAE,CAAC,CAAC;IACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAErC,IAAIG,gBAAgB,GAAGhI,KAAK,CAACsG,QAAQ,CAAC,KAAK,CAAC;IACxC2B,iBAAiB,GAAGhJ,cAAc,CAAC+I,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE1C,IAAIG,iBAAiB,GAAGpI,KAAK,CAACsG,QAAQ,CAAC,KAAK,CAAC;IACzC+B,iBAAiB,GAAGpJ,cAAc,CAACmJ,iBAAiB,EAAE,CAAC,CAAC;IACxDE,gBAAgB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACvCE,mBAAmB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE9C,IAAIG,iBAAiB,GAAGrH,eAAe,CAACwC,QAAQ,EAAE;MAChD8E,UAAU,EAAE;IACd,CAAC,CAAC;IACEC,iBAAiB,GAAGzJ,cAAc,CAACuJ,iBAAiB,EAAE,CAAC,CAAC;IACxDG,cAAc,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACrCE,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAEzC,IAAIG,oBAAoB,GAAGF,cAAc,IAAI,CAACb,QAAQ,CAAC,CAAC;;EAExD,IAAIgB,oBAAoB,GAAGF,cAAc,CAACG,IAAI;IAC1CA,IAAI,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,oBAAoB;EACrE,IAAIE,mBAAmB,GAAGhJ,KAAK,CAACiJ,OAAO,CAAC,YAAY;IAClD;MAAQ;MACN,CAACJ,oBAAoB;MAAI;MACzBD,cAAc,CAACM,MAAM,KAAKxG,SAAS,IAAIkG,cAAc,CAACO,UAAU;MAAI;MACpEP,cAAc,CAACH,UAAU,IAAI7D,UAAU,IAAIuB;IAAU;EAEzD,CAAC,EAAE,CAAC0C,oBAAoB,EAAED,cAAc,EAAEhE,UAAU,EAAEuB,UAAU,CAAC,CAAC;EAClExF,yBAAyB,CAAC,YAAY;IACpC,IAAIgI,cAAc,IAAI,CAACK,mBAAmB,EAAE;MAC1CzB,qBAAqB,CAACxG,cAAc,CAAC,iBAAiB,CAAC,CAAC;MACxD4G,wBAAwB,CAAC5G,cAAc,CAAC,cAAc,CAAC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACiI,mBAAmB,EAAEL,cAAc,CAAC,CAAC;EACzC,IAAIS,WAAW,GAAGpJ,KAAK,CAACiJ,OAAO,CAAC,YAAY;IAC1C,IAAID,mBAAmB,EAAE;MACvB,OAAO,KAAK;IACd;IAEA,IAAID,IAAI,KAAK,CAAC,EAAE;MACd,OAAOrB,qBAAqB;IAC9B;IAEA,OAAOJ,kBAAkB;EAC3B,CAAC,EAAE,CAAC0B,mBAAmB,EAAEtB,qBAAqB,EAAEJ,kBAAkB,CAAC,CAAC;EACpE,IAAI+B,gBAAgB,GAAGR,oBAAoB,KAAKO,WAAW,GAAGd,gBAAgB,GAAGJ,YAAY,CAAC;EAC9F,IAAIoB,eAAe,GAAGT,oBAAoB,IAAIE,IAAI,KAAK,CAAC,IAAIK,WAAW;EACvE,IAAIG,YAAY,GAAGV,oBAAoB,IAAIE,IAAI,GAAG,CAAC,IAAIK,WAAW,CAAC,CAAC;;EAEpE,IAAII,aAAa,GAAG,SAASA,aAAaA,CAACpK,CAAC,EAAE;IAC5C,IAAImG,EAAE;IAENwC,WAAW,CAAC,IAAI,CAAC;IACjB,CAACxC,EAAE,GAAGqD,cAAc,CAACa,QAAQ,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,IAAI,CAACkJ,cAAc,EAAExJ,CAAC,CAAC;EAChG,CAAC;EAED,IAAIsK,iBAAiB,GAAG1J,KAAK,CAACsG,QAAQ,CAAC,CAAC,CAAC;IACrCqD,iBAAiB,GAAG1K,cAAc,CAACyK,iBAAiB,EAAE,CAAC,CAAC;IACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE3C,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;IACnCH,gBAAgB,CAACG,WAAW,CAAC;EAC/B,CAAC,CAAC,CAAC;;EAGH,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,UAAU,EAAE;IACnD,IAAI3E,EAAE;IAEN4C,eAAe,CAAC+B,UAAU,CAAC,CAAC,CAAC;;IAE7B,IAAIhC,YAAY,KAAKgC,UAAU,EAAE;MAC/B,CAAC3E,EAAE,GAAGqD,cAAc,CAACO,UAAU,MAAM,IAAI,IAAI5D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,IAAI,CAACkJ,cAAc,EAAEsB,UAAU,CAAC;IAC3G;EACF,CAAC,CAAC,CAAC;;EAGHlK,KAAK,CAACmH,SAAS,CAAC,YAAY;IAC1B,IAAIgD,OAAO,GAAG7F,aAAa,CAACmB,OAAO;IAEnC,IAAIkD,cAAc,IAAIS,WAAW,IAAIe,OAAO,EAAE;MAC5C,IAAIC,eAAe,GAAGb,YAAY,GAAGY,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACH,WAAW,GAAGG,OAAO,CAACI,WAAW;MAE5H,IAAIjC,gBAAgB,KAAK8B,eAAe,EAAE;QACxC7B,mBAAmB,CAAC6B,eAAe,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACzB,cAAc,EAAES,WAAW,EAAE1F,QAAQ,EAAE6F,YAAY,CAAC,CAAC,CAAC,CAAC;;EAE3D,IAAIiB,YAAY,GAAG5B,cAAc,CAAC6B,OAAO,KAAK,IAAI,GAAG/G,QAAQ,GAAGkF,cAAc,CAAC6B,OAAO;EACtF,IAAIC,YAAY,GAAG1K,KAAK,CAACiJ,OAAO,CAAC,YAAY;IAC3C,IAAI0B,OAAO,GAAG,SAASA,OAAOA,CAAC/H,GAAG,EAAE;MAClC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACgI,QAAQ,CAAC5L,OAAO,CAAC4D,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC+F,cAAc,IAAIS,WAAW,EAAE;MAClC,OAAO1G,SAAS;IAClB;IAEA,IAAIiI,OAAO,CAACjH,QAAQ,CAAC,EAAE;MACrB,OAAOA,QAAQ;IACjB;IAEA,IAAIiH,OAAO,CAAC5G,KAAK,CAAC,EAAE;MAClB,OAAOA,KAAK;IACd;IAEA,IAAI4G,OAAO,CAACH,YAAY,CAAC,EAAE;MACzB,OAAOA,YAAY;IACrB;IAEA,OAAO9H,SAAS;EAClB,CAAC,EAAE,CAACiG,cAAc,EAAES,WAAW,EAAErF,KAAK,EAAEyG,YAAY,EAAEnB,gBAAgB,CAAC,CAAC,CAAC,CAAC;EAC1E;;EAEA,IAAIrE,OAAO,EAAE;IACX,OAAO,aAAahF,KAAK,CAACqC,aAAa,CAACnB,QAAQ,EAAE;MAChD6D,KAAK,EAAE,OAAOrB,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,EAAE;MACnDmH,MAAM,EAAEhF,YAAY;MACpBG,QAAQ,EAAED,YAAY;MACtB+E,KAAK,EAAEjG,UAAU,CAACiG,KAAK;MACvBzH,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA,KAAK;MACZa,SAAS,EAAEA,SAAS;MACpBN,SAAS,EAAEA,SAAS;MACpBiH,SAAS,EAAElG,UAAU,CAACkG,SAAS;MAC/BC,QAAQ,EAAEnG,UAAU,CAACmG,QAAQ;MAC7BC,SAAS,EAAEpG,UAAU,CAACoG;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;;EAGA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIzC,UAAU,GAAGG,cAAc,CAACH,UAAU;MACtC0C,MAAM,GAAGvC,cAAc,CAACuC,MAAM;IAClC,IAAI,CAAC1C,UAAU,EAAE,OAAO,IAAI;IAC5B,IAAI2C,aAAa;IAEjB,IAAID,MAAM,EAAE;MACVC,aAAa,GAAGD,MAAM;IACxB,CAAC,MAAM;MACLC,aAAa,GAAG/G,UAAU,CAACgH,MAAM;IACnC;IAEA,OAAO,aAAarL,KAAK,CAACqC,aAAa,CAAC,GAAG,EAAE;MAC3CiJ,GAAG,EAAE,QAAQ;MACbhI,SAAS,EAAE,EAAE,CAACiI,MAAM,CAAClI,SAAS,EAAE,SAAS,CAAC;MAC1CmI,OAAO,EAAEhC,aAAa;MACtB,YAAY,EAAEnF,UAAU,CAACgH;IAC3B,CAAC,EAAED,aAAa,CAAC;EACnB,CAAC,CAAC,CAAC;;EAGH,IAAIK,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAI,CAAC7G,UAAU,EAAE;IACjB,IAAI8G,IAAI,GAAG7G,UAAU,CAAC6G,IAAI;MACtBjB,OAAO,GAAG5F,UAAU,CAAC4F,OAAO;IAChC,IAAIkB,SAAS,GAAGxL,OAAO,CAACsK,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIpG,UAAU,CAACiB,IAAI;IACtD,IAAIsG,SAAS,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,EAAE;IAC9D,OAAOvG,WAAW,CAACwF,QAAQ,CAAC,MAAM,CAAC,GAAG,aAAa5K,KAAK,CAACqC,aAAa,CAACrB,OAAO,EAAE;MAC9EsK,GAAG,EAAE,MAAM;MACXvH,KAAK,EAAE0G,OAAO,KAAK,KAAK,GAAG,EAAE,GAAGkB;IAClC,CAAC,EAAE,aAAa3L,KAAK,CAACqC,aAAa,CAACvB,WAAW,EAAE;MAC/CqC,GAAG,EAAEqB,WAAW;MAChBlB,SAAS,EAAE,EAAE,CAACiI,MAAM,CAAClI,SAAS,EAAE,OAAO,CAAC;MACxCmI,OAAO,EAAE7F,WAAW;MACpB,YAAY,EAAEiG;IAChB,CAAC,EAAEF,IAAI,IAAI,aAAa1L,KAAK,CAACqC,aAAa,CAAC9B,YAAY,EAAE;MACxDsL,IAAI,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACb,CAAC,CAAC,CAAC;;EAGH,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAI,CAAC3F,UAAU,EAAE;IACjB,IAAI4F,QAAQ,GAAG3F,UAAU,CAAC2F,QAAQ;MAC9BL,IAAI,GAAGtF,UAAU,CAACsF,IAAI;IAC1B,IAAIM,YAAY,GAAGrJ,MAAM,CAACoJ,QAAQ,CAAC;IACnC,IAAIE,SAAS,GAAGtJ,MAAM,CAAC+I,IAAI,CAAC;IAC5B,IAAIQ,SAAS,GAAG1F,MAAM,GAAGlE,OAAO,CAAC0J,YAAY,CAAC,CAAC,CAAC,EAAE3H,UAAU,CAACmC,MAAM,CAAC,GAAGlE,OAAO,CAAC0J,YAAY,CAAC,CAAC,CAAC,EAAE3H,UAAU,CAACjE,IAAI,CAAC;IAChH,IAAI+L,SAAS,GAAG3F,MAAM,GAAGnC,UAAU,CAACmC,MAAM,GAAGnC,UAAU,CAACjE,IAAI;IAC5D,IAAIwL,SAAS,GAAG,OAAOM,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGC,SAAS;IACrE,OAAO,aAAanM,KAAK,CAACqC,aAAa,CAACrB,OAAO,EAAE;MAC/CsK,GAAG,EAAE,MAAM;MACXvH,KAAK,EAAEmI;IACT,CAAC,EAAE,aAAalM,KAAK,CAACqC,aAAa,CAACvB,WAAW,EAAE;MAC/CwC,SAAS,EAAErD,UAAU,CAAC,EAAE,CAACsL,MAAM,CAAClI,SAAS,EAAE,OAAO,CAAC,EAAEmD,MAAM,IAAI,EAAE,CAAC+E,MAAM,CAAClI,SAAS,EAAE,eAAe,CAAC,CAAC;MACrGmI,OAAO,EAAE3E,WAAW;MACpB,YAAY,EAAE+E;IAChB,CAAC,EAAEpF,MAAM,GAAGlE,OAAO,CAAC2J,SAAS,CAAC,CAAC,CAAC,EAAE,aAAajM,KAAK,CAACqC,aAAa,CAAC7B,aAAa,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG8B,OAAO,CAAC2J,SAAS,CAAC,CAAC,CAAC,EAAE,aAAajM,KAAK,CAACqC,aAAa,CAAC5B,YAAY,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACtL,CAAC;EAED,IAAI2L,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,cAAc,EAAE;IAC/D,OAAO,CAACA,cAAc,IAAInB,YAAY,CAAC,CAAC,EAAEO,UAAU,CAAC,CAAC,EAAEK,UAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAACC,YAAY,EAAE;IACzD,OAAO,CAACA,YAAY,IAAI,aAAavM,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAE;MAC/D,aAAa,EAAE,IAAI;MACnBiJ,GAAG,EAAE;IACP,CAAC,EAAEvI,YAAY,CAAC,EAAE6F,cAAc,CAACM,MAAM,EAAEkD,gBAAgB,CAACG,YAAY,CAAC,CAAC;EAC1E,CAAC;EAED,OAAO,aAAavM,KAAK,CAACqC,aAAa,CAAC3B,cAAc,EAAE;IACtDoJ,QAAQ,EAAEA,QAAQ;IAClBrG,QAAQ,EAAE,CAACoF,oBAAoB,IAAIO;EACrC,CAAC,EAAE,UAAUoD,SAAS,EAAE;IACtB,IAAIC,WAAW;IAEf,OAAO,aAAazM,KAAK,CAACqC,aAAa,CAACf,eAAe,EAAE;MACvDyC,KAAK,EAAEyG,YAAY;MACnBkC,eAAe,EAAE7D,oBAAoB;MACrC8D,UAAU,EAAEtD;IACd,CAAC,EAAE,aAAarJ,KAAK,CAACqC,aAAa,CAACpB,UAAU,EAAEnC,QAAQ,CAAC;MACvDwE,SAAS,EAAErD,UAAU,EAAEwM,WAAW,GAAG,CAAC,CAAC,EAAE1N,eAAe,CAAC0N,WAAW,EAAE,EAAE,CAAClB,MAAM,CAAClI,SAAS,EAAE,GAAG,CAAC,CAACkI,MAAM,CAAC/H,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAEzE,eAAe,CAAC0N,WAAW,EAAE,EAAE,CAAClB,MAAM,CAAClI,SAAS,EAAE,WAAW,CAAC,EAAEI,QAAQ,CAAC,EAAE1E,eAAe,CAAC0N,WAAW,EAAE,EAAE,CAAClB,MAAM,CAAClI,SAAS,EAAE,WAAW,CAAC,EAAEsF,cAAc,CAAC,EAAE5J,eAAe,CAAC0N,WAAW,EAAE,EAAE,CAAClB,MAAM,CAAClI,SAAS,EAAE,cAAc,CAAC,EAAEwF,oBAAoB,IAAIE,IAAI,KAAK,CAAC,CAAC,EAAEhK,eAAe,CAAC0N,WAAW,EAAE,EAAE,CAAClB,MAAM,CAAClI,SAAS,EAAE,uBAAuB,CAAC,EAAEiG,eAAe,CAAC,EAAEvK,eAAe,CAAC0N,WAAW,EAAE,EAAE,CAAClB,MAAM,CAAClI,SAAS,EAAE,yBAAyB,CAAC,EAAEkG,YAAY,CAAC,EAAEkD,WAAW,GAAGnJ,SAAS,CAAC;MAC5kBC,KAAK,EAAEzE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyE,KAAK,CAAC,EAAE;QACnCqJ,eAAe,EAAErD,YAAY,GAAGR,IAAI,GAAGrG;MACzC,CAAC,CAAC;MACFoB,SAAS,EAAEA,SAAS;MACpBX,GAAG,EAAE7C,UAAU,CAACkM,SAAS,EAAElI,aAAa,EAAEnB,GAAG,CAAC;MAC9CiB,SAAS,EAAEA,SAAS;MACpBoH,OAAO,EAAEpG,WAAW,CAACwF,QAAQ,CAAC,MAAM,CAAC,GAAGjF,WAAW,GAAG,IAAI;MAC1D,YAAY,EAAE+E,YAAY;MAC1B3G,KAAK,EAAEA;IACT,CAAC,EAAEU,SAAS,CAAC,EAAE,aAAazE,KAAK,CAACqC,aAAa,CAAChB,QAAQ,EAAE;MACxDwL,cAAc,EAAEhE,oBAAoB,IAAI,CAACO,WAAW;MACpDrC,IAAI,EAAErD,QAAQ;MACdqF,IAAI,EAAEA,IAAI;MACV+D,KAAK,EAAElD,aAAa;MACpBT,UAAU,EAAEc;IACd,CAAC,EAAE,UAAU8C,IAAI,EAAER,YAAY,EAAE;MAC/B,IAAIS,UAAU,GAAGD,IAAI;MAErB,IAAIA,IAAI,CAACjN,MAAM,IAAIyM,YAAY,IAAI7B,YAAY,EAAE;QAC/CsC,UAAU,GAAG,aAAahN,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAE;UACpDiJ,GAAG,EAAE,cAAc;UACnB,aAAa,EAAE;QACjB,CAAC,EAAE0B,UAAU,CAAC;MAChB;MAEA,IAAIC,cAAc,GAAG1L,kBAAkB,CAAC2B,KAAK,EAAE,aAAalD,KAAK,CAACqC,aAAa,CAACrC,KAAK,CAACkN,QAAQ,EAAE,IAAI,EAAEF,UAAU,EAAEV,cAAc,CAACC,YAAY,CAAC,CAAC,CAAC;MAChJ,OAAOU,cAAc;IACvB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,eAAejK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}