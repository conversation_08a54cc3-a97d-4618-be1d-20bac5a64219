{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\n/**\n * Fallback to findDOMNode if origin ref do not provide any dom element\n */\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(<PERSON><PERSON>rapper, _React$Component);\n  var _super = _createSuper(DomWrapper);\n  function DomWrapper() {\n    _classCallCheck(this, <PERSON>Wrapper);\n    return _super.apply(this, arguments);\n  }\n  _createClass(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(React.Component);\nexport { Dom<PERSON>rapper as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "DomWrapper", "_React$Component", "_super", "apply", "arguments", "key", "value", "render", "props", "children", "Component", "default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-resize-observer/es/SingleObserver/DomWrapper.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\n/**\n * Fallback to findDOMNode if origin ref do not provide any dom element\n */\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(<PERSON><PERSON>rapper, _React$Component);\n\n  var _super = _createSuper(DomWrapper);\n\n  function DomWrapper() {\n    _classCallCheck(this, <PERSON>Wrapper);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n\n  return DomWrapper;\n}(React.Component);\n\nexport { Dom<PERSON>rapper as default };"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACxDJ,SAAS,CAACG,UAAU,EAAEC,gBAAgB,CAAC;EAEvC,IAAIC,MAAM,GAAGJ,YAAY,CAACE,UAAU,CAAC;EAErC,SAASA,UAAUA,CAAA,EAAG;IACpBL,eAAe,CAAC,IAAI,EAAEK,UAAU,CAAC;IAEjC,OAAOE,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtC;EAEAR,YAAY,CAACI,UAAU,EAAE,CAAC;IACxBK,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,KAAK,CAACC,QAAQ;IAC5B;EACF,CAAC,CAAC,CAAC;EAEH,OAAOT,UAAU;AACnB,CAAC,CAACD,KAAK,CAACW,SAAS,CAAC;AAElB,SAASV,UAAU,IAAIW,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}