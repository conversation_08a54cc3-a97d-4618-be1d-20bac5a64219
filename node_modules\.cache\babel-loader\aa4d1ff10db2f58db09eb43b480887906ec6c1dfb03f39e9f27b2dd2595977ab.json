{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport devWarning from '../_util/devWarning';\nimport Base from './Base';\nvar Link = function Link(_a, ref) {\n  var ellipsis = _a.ellipsis,\n    rel = _a.rel,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  devWarning(_typeof(ellipsis) !== 'object', 'Typography.Link', '`ellipsis` only supports boolean value.');\n  var baseRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return baseRef.current;\n  });\n  var mergedProps = _extends(_extends({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  }); // https://github.com/ant-design/ant-design/issues/26622\n  // @ts-ignore\n\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, _extends({}, mergedProps, {\n    ref: baseRef,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Link);", "map": {"version": 3, "names": ["_extends", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "dev<PERSON><PERSON><PERSON>", "Base", "Link", "_a", "ref", "ellipsis", "rel", "restProps", "baseRef", "useRef", "useImperativeHandle", "current", "mergedProps", "undefined", "target", "navigate", "createElement", "component", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Link.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport devWarning from '../_util/devWarning';\nimport Base from './Base';\n\nvar Link = function Link(_a, ref) {\n  var ellipsis = _a.ellipsis,\n      rel = _a.rel,\n      restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n\n  devWarning(_typeof(ellipsis) !== 'object', 'Typography.Link', '`ellipsis` only supports boolean value.');\n  var baseRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return baseRef.current;\n  });\n\n  var mergedProps = _extends(_extends({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  }); // https://github.com/ant-design/ant-design/issues/26622\n  // @ts-ignore\n\n\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, _extends({}, mergedProps, {\n    ref: baseRef,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n};\n\nexport default /*#__PURE__*/React.forwardRef(Link);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AAEvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,IAAI,MAAM,QAAQ;AAEzB,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAEC,GAAG,EAAE;EAChC,IAAIC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;IACtBC,GAAG,GAAGH,EAAE,CAACG,GAAG;IACZC,SAAS,GAAGtB,MAAM,CAACkB,EAAE,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;EAE/CH,UAAU,CAAChB,OAAO,CAACqB,QAAQ,CAAC,KAAK,QAAQ,EAAE,iBAAiB,EAAE,yCAAyC,CAAC;EACxG,IAAIG,OAAO,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAChCV,KAAK,CAACW,mBAAmB,CAACN,GAAG,EAAE,YAAY;IACzC,OAAOI,OAAO,CAACG,OAAO;EACxB,CAAC,CAAC;EAEF,IAAIC,WAAW,GAAG7B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwB,SAAS,CAAC,EAAE;IAClDD,GAAG,EAAEA,GAAG,KAAKO,SAAS,IAAIN,SAAS,CAACO,MAAM,KAAK,QAAQ,GAAG,qBAAqB,GAAGR;EACpF,CAAC,CAAC,CAAC,CAAC;EACJ;;EAGA,OAAOM,WAAW,CAACG,QAAQ;EAC3B,OAAO,aAAahB,KAAK,CAACiB,aAAa,CAACf,IAAI,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,WAAW,EAAE;IACtER,GAAG,EAAEI,OAAO;IACZH,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBY,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,aAAalB,KAAK,CAACmB,UAAU,CAAChB,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}