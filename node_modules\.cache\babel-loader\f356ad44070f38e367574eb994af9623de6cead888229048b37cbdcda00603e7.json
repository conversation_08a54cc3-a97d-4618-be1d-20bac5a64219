{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\modificaProdOrdine.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ModificaProdottiOrdine - operazioni sulla modifica dell'ordine\n*\n*/\nimport React, { useState, useEffect, useRef } from 'react';\nimport Nav from '../components/navigation/Nav';\nimport CustomDataTable from '../components/customDataTable';\nimport { InputText } from 'primereact/inputtext';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../components/traduttore/const';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { Dialog } from 'primereact/dialog';\nimport { Toast } from 'primereact/toast';\nimport { Calendar } from 'primereact/calendar';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Tooltip } from 'primereact/tooltip';\nimport { Messages } from 'primereact/messages';\nimport { SelectButton } from 'primereact/selectbutton';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport { JoyrideGen } from '../components/footer/joyride';\nimport { distributoreGestioneLogisticaOrdini } from '../components/route';\nimport '../css/MultiSelectDemo.css';\nimport '../css/modale.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModificaProdottiOrdine = props => {\n  _s();\n  var _result$product;\n  let emptyResult = {\n    id: null,\n    name: '',\n    image: null,\n    product: [],\n    description: '',\n    category: null,\n    price: 0,\n    quantity: 0,\n    rating: 0,\n    inventoryStatus: 'INSTOCK'\n  };\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti\n  const [value1, setValue1] = useState('');\n  const [value2, setValue2] = useState('');\n  const [value3, setValue3] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [mex, setMex] = useState([]);\n  const [mexInputTextarea, setMexInputTextarea] = useState([]);\n  const [selectedStatus, setSelectedStatus] = useState(null);\n  const [resultsCopia, setResultsCopia] = useState([]);\n  const [results, setResults] = useState([]);\n  const [results2, setResults2] = useState([]);\n  const [results3, setResults3] = useState([]);\n  const [warehouse, setWarehouse] = useState([]);\n  const [date, setDate] = useState(null);\n  const [date2, setDate2] = useState(null);\n  const [selectedResults, setSelectedResults] = useState(null);\n  const [selectedWarehouse, setSelectedWarehouse] = useState(null);\n  const [codDep, setCodDep] = useState(null);\n  const [deleteResultDialog, setDeleteResultDialog] = useState(false);\n  const [addProdDialog, setAddProdDialog] = useState(false);\n  const [displayed, setDisplayed] = useState(false);\n  const [result, setResult] = useState(emptyResult);\n  const [classMessage, setClassMessage] = useState(\"col-11 mx-auto\");\n  const toast = useRef(null);\n  const msgs2 = useRef(null);\n  const status = ['Annullato', 'Registrato', 'Approvato'];\n  let originalRows = {};\n  const dataTableFuncMap = {\n    'products': setProducts\n  };\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    var prodIns = [];\n    var idRetailer = [];\n    /* Reperisco gli ordini */\n    async function fetchData() {\n      let url = 'orders/?id=' + localStorage.getItem(\"datiComodo\");\n      var res = await APIRequest('GET', url);\n      /* Avviso nel caso la data di consegna non sia impostata */\n      if (res.data.deliveryDate === null) {\n        msgs2.current.show({\n          severity: 'error',\n          sticky: true,\n          content: /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: \"La data di consegna non \\xE8 stata impostata inserirne una prima di procedere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 25\n          }, this)\n        });\n      }\n      setSelectedStatus(res.data.status);\n      setResults3(res.data);\n      setResults2(res.data.orderProducts);\n      setValue1(res.data.deliveryDestination);\n      setValue2(res.data.note);\n      setDate(res.data.orderDate);\n      setDate2(res.data.deliveryDate);\n      idRetailer = res.data.idRetailer;\n      setValue3(res.data.orderProducts);\n      prodIns = res.data.orderProducts;\n      setMex('del: ' + new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      }).format(new Date(res.data.orderDate)) + \" per il cliente: \" + res.data.idRetailer.idRegistry.firstName);\n      /* GET CURRENT DATE */\n      var newDate = new Date();\n      let month = newDate.getMonth() + 1;\n      let day = newDate.getDate();\n      if (month < 10) {\n        month = '0' + month;\n      }\n      if (day < 10) {\n        day = '0' + day;\n      }\n      newDate = newDate.getFullYear() + '-' + month + '-' + day;\n      if (idRetailer !== []) {\n        /* Reperisco i prodotti all'interno del listino retailer */\n        url = 'pricelistretailer/?idRetailer=' + idRetailer.id;\n        res = await APIRequest('GET', url);\n        if (res.data !== '') {\n          var prodotti = res.data.idPriceList2.priceListProducts;\n        } else {\n          /* Reperisco i prodotti all'interno del listino affiliato nel caso non sia presente quello del retailer */\n          url = 'pricelistaffiliate/?idAffiliate=' + idRetailer.idAffiliate;\n          res = await APIRequest('GET', url);\n          prodotti = res.data.idPriceList2.priceListProducts;\n        }\n        /* Escludo i prodotti presenti nell'ordine */\n        for (var i = 0; i < prodIns.length; i++) {\n          for (var j = 0; j < prodotti.length; j++) {\n            if (prodIns[i].productId === prodotti[j].idProduct) {\n              prodotti.splice(j, 1);\n            }\n          }\n        }\n        var prod = [];\n        prodotti.forEach(element => {\n          var availability = element.idProduct2.productsAvailabilities.find(el => {\n            var _el$idWarehouse;\n            return ((_el$idWarehouse = el.idWarehouse) === null || _el$idWarehouse === void 0 ? void 0 : _el$idWarehouse.codDep) === '00';\n          });\n          var x = {\n            id: element.idProduct,\n            description: element.idProduct2.description,\n            externalCode: element.idProduct2.externalCode,\n            availability: availability !== undefined ? availability.availability : 'Non disponibile'\n          };\n          prod.push(x);\n        });\n        setResultsCopia(prod);\n        setResults(prodotti);\n      }\n      await APIRequest(\"GET\", \"warehouses/\").then(res => {\n        var warehouses = [];\n        for (var entry of res.data) {\n          var x = {\n            name: entry.warehouseName,\n            value: entry.id,\n            code: entry.codDep\n          };\n          warehouses.push(x);\n        }\n        setWarehouse(warehouses);\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        toast.current.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n      var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n      if (idWarehouse !== null && idWarehouse !== 0) {\n        var path = 'productsposition?idWarehouse=' + idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse;\n        setSelectedWarehouse(idWarehouse);\n        await APIRequest(\"GET\", path).then(res => {\n          prodIns.forEach(element => {\n            var find = res.data.filter(el => el.idProductsPackaging.idProduct.id === element.product.id && el.idProductsPackaging.pcsXPackage === element.pcsXpackage);\n            if (find !== undefined) {\n              var count = 0;\n              find.forEach(item => {\n                count += item.colli;\n              });\n              element.productsAvailabilities = count;\n            }\n          });\n          /* setResults4(res.data) */\n        }).catch(e => {\n          var _e$response3, _e$response4;\n          console.log(e);\n          toast.current.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n            life: 3000\n          });\n        });\n      } else {\n        setDisplayed(true);\n      }\n    }\n    fetchData();\n  }, []);\n  /* Seleziono il punto vendita e controllo che abbia un listino associato */\n  const onWarehouseSelect = async e => {\n    setSelectedWarehouse(e.value);\n    var find = warehouse.find(el => el.value === e.value);\n    setCodDep(find.code);\n    var url = 'productsposition?idWarehouse=' + e.value;\n    window.sessionStorage.setItem(\"idWarehouse\", e.value);\n    await APIRequest('GET', url).then(res => {\n      value3.forEach(element => {\n        var find = res.data.filter(el => el.idProductsPackaging.idProduct.id === element.product.id && el.idProductsPackaging.pcsXPackage === element.pcsXpackage);\n        if (find !== undefined) {\n          var count = 0;\n          find.forEach(item => {\n            count += item.colli;\n          });\n          element.productsAvailabilities = count;\n        }\n      });\n      /* setResults4(res.data) */\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      toast.current.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  const changeStatus = async e => {\n    setSelectedStatus(e.value);\n  };\n  const Invia = async () => {\n    var totProd = [];\n    let url = 'orders/?id=' + localStorage.getItem(\"datiComodo\");\n    value3.forEach(items => {\n      var tot = new Intl.NumberFormat('de-DE', {\n        style: 'currency',\n        currency: 'EUR',\n        maximumFractionDigits: 6\n      }).format(items.colli * items.pcsXpackage * parseFloat(items.unitPrice));\n      totProd.push({\n        id: items.product.id,\n        quantity: items.colli * items.pcsXpackage,\n        total: parseFloat(tot.replace(\"€\", \"\").replace(\".\", \"\").replace(\",\", \".\")),\n        unitMeasure: items.unitMeasure,\n        pcsXpackage: items.pcsXpackage,\n        unitPrice: items.unitPrice,\n        colli: items.colli,\n        tax: items.tax\n      });\n    });\n    var total = 0;\n    totProd.forEach(element => {\n      total += element.total;\n    });\n    var tot = new Intl.NumberFormat('de-DE', {\n      style: 'currency',\n      currency: 'EUR',\n      maximumFractionDigits: 6\n    }).format(total);\n    //Dichiarazione degli elementi da passare nel JSON con i rispettivi valori\n    let body = {\n      deliveryDate: date2,\n      deliveryDestination: value1,\n      id: results3.id,\n      idRetailer: results3.idRetailer,\n      note: value2,\n      orderDate: date,\n      orderNumber: results3.orderNumber,\n      products: totProd,\n      termsPayment: results3.termsPayment,\n      paymentStatus: '',\n      total: parseFloat(tot.replace(\"€\", \"\").replace(\".\", \"\").replace(\",\", \".\")),\n      totalTaxed: '',\n      status: selectedStatus,\n      numberOfCustomers: results3.numberOfCustomers\n    };\n    if (selectedStatus === 'Approvato' && selectedWarehouse !== null) {\n      await APIRequest('PUT', url, body).then(async res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: 'Ottimo',\n          detail: \"La modifica dell'ordine è avvenuta con successo\",\n          life: 3000\n        });\n        var url2 = 'documents/transformorder?idOrder=' + localStorage.getItem(\"datiComodo\") + '&idWarehouse=' + selectedWarehouse;\n        await APIRequest(\"GET\", url2).then(res => {\n          console.log(res.data);\n          toast.current.show({\n            severity: \"success\",\n            summary: \"Ottimo!\",\n            detail: \"Documento creato\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.pathname = distributoreGestioneLogisticaOrdini;\n          }, 3000);\n        }).catch(e => {\n          var _e$response7, _e$response8;\n          console.log(e);\n          toast.current.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile creare il documento. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n            life: 3000\n          });\n        });\n      }).catch(e => {\n        var _e$response9, _e$response0;\n        console.log(e);\n        toast.current.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile procedere con la modifica. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n          life: 3000\n        });\n      });\n    } else if (selectedStatus !== 'Approvato') {\n      await APIRequest('PUT', url, body).then(async res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: 'Ottimo',\n          detail: \"La modifica dell'ordine è avvenuta con successo\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = distributoreGestioneLogisticaOrdini;\n        }, 3000);\n      }).catch(e => {\n        var _e$response1, _e$response10;\n        console.log(e);\n        toast.current.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile procedere con la modifica. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      toast.current.show({\n        severity: 'warn',\n        summary: 'Attenzione!',\n        detail: \"E' necessario selezionare un magazzino prima di procedere\",\n        life: 3000\n      });\n    }\n  };\n  /* Reperisco quantità disponibili per i prodotti */\n  const quantitDispBodyTemplate = rowData => {\n    if (rowData.product.productsAvailabilities !== null) {\n      var availability = rowData.product.productsAvailabilities.find(el => {\n        var _el$idWarehouse2;\n        return ((_el$idWarehouse2 = el.idWarehouse) === null || _el$idWarehouse2 === void 0 ? void 0 : _el$idWarehouse2.codDep) === (codDep !== null ? codDep : '00');\n      });\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Giacenza\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 21\n        }, this), availability !== undefined ? availability.availability : 'Non disponibile']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 17\n      }, this);\n    }\n  };\n  /* Reperisco quantità disponibili per i prodotti */\n  const quantitDisp2BodyTemplate = rowData => {\n    if (rowData.productsAvailabilities !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.GiacMag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 21\n        }, this), rowData.productsAvailabilities]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 17\n      }, this);\n    }\n  };\n  /* Reperisco i valori del moltiplicatore per i prodotti */\n  const pcxpkgBodyTemplate = rowData => {\n    if (rowData.moltiplicatore !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Pezzi per package\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this), rowData.moltiplicatore]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Pezzi per package\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 21\n        }, this), rowData.pcsXpackage]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 17\n      }, this);\n    }\n  };\n  /* Reperisco il formato dei prodotti */\n  const formatBodyTemplate2 = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Formato\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 17\n      }, this), rowData.unitMeasure]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 13\n    }, this);\n  };\n  /* Reperisco il nome dei prodotti */\n  const descriptionBodyTemplate2 = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 17\n      }, this), rowData.product.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 13\n    }, this);\n  };\n  /* Reperisco i codici esterni dei prodotti */\n  const externalCodeBodyTemplate2 = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 17\n      }, this), rowData.product.externalCode]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 13\n    }, this);\n  };\n  /* Reperisco i totali dei prodotti */\n  const totalBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Tot\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 17\n      }, this), new Intl.NumberFormat('de-DE', {\n        style: 'currency',\n        currency: 'EUR',\n        maximumFractionDigits: 6\n      }).format(rowData.total)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 13\n    }, this);\n  };\n  /* Reperisco i totali tassati dei prodotti */\n  const totalTaxedBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.TotTax\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 17\n      }, this), new Intl.NumberFormat('de-DE', {\n        style: 'currency',\n        currency: 'EUR',\n        maximumFractionDigits: 6\n      }).format(rowData.totalTaxed)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 13\n    }, this);\n  };\n  /* Cambio il valore dell'unità di misura dei prodotti e richiamo le funzioni per aggiornare i totali */\n  const onEditorValueChange = (productKey, props, value, status) => {\n    var formato = '';\n    if (status !== undefined) {\n      status.forEach(element => {\n        if (element.pcsXPackage === value) {\n          formato = element.unitMeasure;\n        }\n      });\n    }\n    let updatedProducts = [...props.value];\n    updatedProducts[props.rowIndex][props.field] = value;\n    updatedProducts[props.rowIndex][\"unitMeasure\"] = formato;\n    updatedProducts[props.rowIndex]['total'] = updatedProducts[props.rowIndex]['colli'] * updatedProducts[props.rowIndex]['pcsXpackage'] * parseFloat(updatedProducts[props.rowIndex]['unitPrice']);\n    if (updatedProducts[props.rowIndex]['tax'] !== undefined) {\n      updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['tax']) / 100;\n    } else {\n      updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['iva']) / 100;\n    }\n    dataTableFuncMap[\"\".concat(productKey)](updatedProducts);\n    calcIva();\n    calcTot();\n    calcTotIva();\n  };\n  /* Cambio il valore del totale dei prodotti e richiamo le funzioni per aggiornare i totali */\n  const onEditorValueChange2 = (productKey, props, value) => {\n    let updatedProducts = [...props.value];\n    updatedProducts[props.rowIndex][props.field] = value;\n    updatedProducts[props.rowIndex]['total'] = value * updatedProducts[props.rowIndex]['pcsXpackage'] * parseFloat(updatedProducts[props.rowIndex]['unitPrice']);\n    if (updatedProducts[props.rowIndex]['tax'] !== undefined) {\n      updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['tax']) / 100;\n    } else {\n      updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['iva']) / 100;\n    }\n    dataTableFuncMap[\"\".concat(productKey)](updatedProducts);\n    calcIva();\n    calcTot();\n    calcTotIva();\n  };\n  /* Componente inputNumber per modificare i colli */\n  const quantityEditor2 = (productKey, props) => {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: props.rowData['colli'],\n      onValueChange: e => onEditorValueChange2(productKey, props, e.value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 16\n    }, this);\n  };\n  /* Ricavo la quantità moltiplicando i colli per il moltiplicatore */\n  const quantitBodyTemplate = rowData => {\n    var quantità = 0;\n    if (rowData.quantity !== undefined) {\n      if (rowData.colli !== undefined && rowData.pcsXpackage !== undefined) {\n        quantità = rowData.colli * rowData.pcsXpackage;\n      } else {\n        quantità = rowData.quantity;\n      }\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"priceAdded price-filled\",\n        children: quantità\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 20\n      }, this);\n    } else {\n      return \"\";\n    }\n  };\n  /* Reperisco i colli dei prodotti */\n  const quantitBodyTemplate3 = rowData => {\n    if (rowData.colli !== undefined) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"priceAdded\",\n        children: rowData.colli\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 20\n      }, this);\n    } else {\n      return \"\";\n    }\n  };\n  /* Definisco il dropdown con i possibili formati ed annessi moltiplicatori */\n  const statusEditor = (productKey, props) => {\n    var status = [];\n    if (props.rowData.idProduct2 !== undefined) {\n      for (const object of props.rowData.idProduct2.productsPackagings) {\n        status.push({\n          unitMeasure: object.unitMeasure,\n          pcsXPackage: object.pcsXPackage\n        });\n      }\n    } else {\n      for (const obj of props.rowData.product.productsPackagings) {\n        status.push({\n          unitMeasure: obj.unitMeasure,\n          pcsXPackage: obj.pcsXPackage\n        });\n      }\n    }\n    return /*#__PURE__*/_jsxDEV(Dropdown, {\n      value: props.rowData['pcsXpackage'],\n      options: status,\n      optionLabel: \"unitMeasure\",\n      optionValue: \"pcsXPackage\",\n      onChange: e => onEditorValueChange(productKey, props, e.value, status),\n      style: {\n        width: '100%'\n      },\n      placeholder: \"Seleziona...\",\n      itemTemplate: option => {\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"product-badge status-\".concat(option.unitMeasure.toLowerCase()),\n          children: option.unitMeasure\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 28\n        }, this);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 13\n    }, this);\n  };\n  /* Apertura dialogo eliminazione prodotto */\n  const confirmDeleteResult = result => {\n    setResult(result);\n    setDeleteResultDialog(true);\n  };\n  /* Chiusura dialogo eliminazione prodotto */\n  const hideDeleteResultDialog = () => {\n    setDeleteResultDialog(false);\n  };\n  /* Eliminazione del prodotto dall'array */\n  const deleteResult = () => {\n    let _products = value3.filter(val => val.id !== result.id);\n    setValue3(_products);\n    _products = results2.filter(val => val.product.id !== result.id);\n    setResults2(_products);\n    setDeleteResultDialog(false);\n    setResult(emptyResult);\n    if (result.idProduct2 === undefined) {\n      result.idProduct2 = result.product;\n    }\n    results.push(result);\n    toast.current.show({\n      severity: 'success',\n      summary: 'Ottimo !',\n      detail: 'Prodotto eliminato correttamente',\n      life: 3000\n    });\n  };\n  /* Footer per dialogo cancellazione prodotti */\n  const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      label: \"No\",\n      icon: \"pi pi-times\",\n      className: \"p-button-text\",\n      onClick: hideDeleteResultDialog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      label: \"Si\",\n      icon: \"pi pi-check\",\n      className: \"p-button-text\",\n      onClick: deleteResult\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 524,\n    columnNumber: 9\n  }, this);\n  /* Icona per azione elimina prodotto */\n  const actionBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".p-button-rounded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-trash\",\n        className: \"p-button-rounded\",\n        \"data-pr-tooltip\": \"Elimina\",\n        \"data-pr-position\": \"top\",\n        onClick: () => confirmDeleteResult(rowData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 13\n    }, this);\n  };\n  /* Aggiungo il prodotto all'ordine e visualizzo l'ultimo prodotto inserito nella lista */\n  const addProd = e => {\n    var totProd = [];\n    if (e.value !== null) {\n      results.forEach(element => {\n        if (element.idProduct === e.value.id) {\n          e.value = element;\n        }\n      });\n      if (e.value.colli === undefined) {\n        e.value.colli = 1;\n        e.value.pcsXpackage = e.value.idProduct2.productsPackagings[0].pcsXPackage;\n        e.value.unitMeasure = e.value.idProduct2.productsPackagings[0].unitMeasure;\n        e.value.quantity = e.value.pcsXpackage * e.value.colli;\n        e.value.total = new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR',\n          maximumFractionDigits: 6\n        }).format(e.value.colli * e.value.pcsXpackage * parseFloat(e.value.price));\n        totProd = [{\n          id: e.value.idProduct2.id,\n          product: e.value.idProduct2,\n          quantity: e.value.quantity,\n          total: e.value.total.replace(\"€\", \"\").replace(/\\s/g, '').replace(\",\", '.'),\n          totalTaxed: new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(parseFloat(e.value.total.replace(\"€\", \"\").replace(/\\s/g, '').replace(\",\", '.')) + parseFloat(e.value.total.replace(\"€\", \"\").replace(/\\s/g, '').replace(\",\", '.')) * parseInt(e.value.idProduct2.iva) / 100),\n          unitMeasure: e.value.unitMeasure,\n          pcsXpackage: e.value.pcsXpackage,\n          unitPrice: e.value.price,\n          colli: e.value.colli,\n          tax: e.value.idProduct2.iva\n        }];\n        value3.push(totProd[0]);\n        for (var i = 0; i < resultsCopia.length; i++) {\n          if (resultsCopia[i].id === totProd[0].id) {\n            resultsCopia.splice(i, 1);\n            break;\n          }\n        }\n      } else {\n        value3.push(e.value);\n        for (var j = 0; j < resultsCopia.length; j++) {\n          if (resultsCopia[j].id === e.value.productId) {\n            resultsCopia.splice(j, 1);\n            break;\n          }\n        }\n      }\n      var rows = document.querySelectorAll('#tabProd tr');\n      // line is zero-based\n      // line is the row number that you want to see into view after scroll    \n      rows[rows.length - 1].scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n    }\n  };\n  /* Reperisco il prodotto prima della modifica */\n  const onRowEditInit = event => {\n    originalRows[event.index] = _objectSpread({}, products[event.index]);\n  };\n  /* Annullo la modifica sul prodotto */\n  const onRowEditCancel = event => {\n    let product = [...products];\n    product[event.index] = originalRows[event.index];\n    delete originalRows[event.index];\n    setProducts(products);\n  };\n  /* Apertura dialogo aggiunta prodotti */\n  const openNew = () => {\n    setAddProdDialog(true);\n  };\n  /* Chiusura dialogo aggiunta prodotti */\n  const hideDialog = () => {\n    setAddProdDialog(false);\n  };\n  /* Tooltip per spiegare all'utente come aggiungere i prodotti all'ordine */\n  const tooltipMex = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".pi-info-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"p-text-center p-text-bold\",\n        children: [Costanti.ProdAggInOrd, \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-info-circle\",\n          \"data-pr-tooltip\": \"Dalla tabella sottostante, seleziona i prodotti che desideri aggiungere a quest'ordine.\",\n          \"data-pr-position\": \"top\",\n          \"data-pr-at\": \"top\",\n          \"data-pr-my\": \"bottom-5\",\n          style: {\n            fontSize: '1.4rem',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 83\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 13\n    }, this);\n  };\n  /* Calcolo il totale e lo formatto in Euro */\n  const calcTot = () => {\n    var tot = 0;\n    if (value3 !== null) {\n      value3.forEach(element => {\n        tot += parseFloat(element.total);\n      });\n      return new Intl.NumberFormat('de-DE', {\n        style: 'currency',\n        currency: 'EUR',\n        maximumFractionDigits: 6\n      }).format(tot);\n    }\n  };\n  /* Calcolo l'iva dalla differenza tra totale e totale ivato e la formatto in Euro */\n  const calcIva = () => {\n    var iva = 0;\n    if (value3 !== null) {\n      value3.forEach(element => {\n        iva += parseFloat(element.totalTaxed.toString().split(\",\").join(\".\")) - parseFloat(element.total.toString().split(\",\").join(\".\"));\n      });\n      return new Intl.NumberFormat('de-DE', {\n        style: 'currency',\n        currency: 'EUR',\n        maximumFractionDigits: 6\n      }).format(iva);\n    }\n  };\n  /* Calcolo il totale ivato e lo formatto in Euro */\n  const calcTotIva = () => {\n    var totIva = 0;\n    if (value3 !== null) {\n      value3.forEach(element => {\n        totIva += parseFloat(element.totalTaxed.toString().split(\",\").join(\".\"));\n      });\n      return new Intl.NumberFormat('de-DE', {\n        style: 'currency',\n        currency: 'EUR',\n        maximumFractionDigits: 6\n      }).format(totIva);\n    }\n  };\n  const onKeyUpHandler = e => {\n    var mex = 'Inseriti: ' + e.target.value.length + ' di ' + e.currentTarget.maxLength + ' caratteri';\n    setMexInputTextarea(mex);\n  };\n  /* Footer dialogo di aggiunta prodotti */\n  const productDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      label: Costanti.Chiudi,\n      className: \"p-button-text\",\n      onClick: hideDialog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 654,\n    columnNumber: 9\n  }, this);\n  const fields = [{\n    field: 'description',\n    header: Costanti.Prodotto,\n    body: 'description',\n    sortable: true,\n    showHeader: true\n  }, {\n    field: 'externalCode',\n    header: Costanti.exCode,\n    body: 'externalCode',\n    sortable: true,\n    showHeader: true\n  }, {\n    field: 'availability',\n    header: Costanti.QtaDisp,\n    body: 'availability',\n    sortable: true,\n    showHeader: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card border-0\",\n    children: [displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n      title: \"Prima di procedere\",\n      content: \"Seleziona un magazzino \",\n      target: \".selWar\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 solid-head\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"m-0\",\n        children: [Costanti.ModOrder, /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-center d-block subtitle\",\n          children: mex\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: classMessage,\n        children: /*#__PURE__*/_jsxDEV(Messages, {\n          ref: msgs2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mx-5 mt-3 container-edit-order pb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 my-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modList row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-field col-12 col-sm-6 my-0 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"description\",\n              children: Costanti.IndCons\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputText, {\n              id: \"textDesc\",\n              className: \"m-0\",\n              value: value1,\n              onChange: e => setValue1(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-field col-12 col-sm-6 my-0 mt-3 mt-sm-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isValid\",\n              children: Costanti.DCons\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n              id: \"selValDate\",\n              className: \"w-100\",\n              value: date2,\n              dateFormat: \"dd/mm/yy\",\n              onChange: e => {\n                setDate2(e.value);\n                setClassMessage('d-none');\n              },\n              placeholder: new Intl.DateTimeFormat(\"it-IT\", {\n                day: '2-digit',\n                month: '2-digit',\n                year: 'numeric'\n              }).format(new Date(date2)),\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row border-top mx-0\",\n          style: {\n            backgroundColor: \"#f3f4f5\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-lg-4 my-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"p-text-bold mb-0\",\n              children: Costanti.ProdInOrd\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-lg-4 my-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center col-md-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row rincaroPrezzi ml-2 p-3\",\n                children: /*#__PURE__*/_jsxDEV(Dropdown, {\n                  className: \"selWar mr-4\",\n                  value: selectedWarehouse,\n                  options: warehouse,\n                  onChange: onWarehouseSelect,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona magazzino\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-lg-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-end my-4 w-100\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                className: \"p-button mx-0 justify-content-center\",\n                onClick: () => openNew(),\n                children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-plus-circle mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 118\n                }, this), \" \", Costanti.AggProdOrd, \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"datatable-responsive-demo wrapper mb-4\",\n          children: /*#__PURE__*/_jsxDEV(DataTable, {\n            id: \"tabProd\",\n            className: \"p-datatable-responsive-demo editable-prices-table\",\n            value: value3,\n            editMode: \"row\",\n            autoLayout: \"true\",\n            onRowEditInit: onRowEditInit,\n            onRowEditCancel: onRowEditCancel,\n            csvSeparator: \";\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              field: \"externalCode\",\n              header: Costanti.exCode,\n              body: externalCodeBodyTemplate2,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"description\",\n              header: Costanti.Prodotto,\n              body: descriptionBodyTemplate2,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"pcsXpackage\",\n              header: Costanti.UnitMis,\n              body: formatBodyTemplate2 /* body={productsPackagingsBodyTemplate} */,\n              editor: props => statusEditor('products', props),\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"pcsXpackage\",\n              header: \"Pezzi per package\",\n              body: pcxpkgBodyTemplate,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"colli\",\n              header: Costanti.Colli,\n              body: quantitBodyTemplate3,\n              editor: props => quantityEditor2('products', props),\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"quantity\",\n              header: Costanti.Quantità,\n              body: quantitBodyTemplate,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"quantity\",\n              header: Costanti.GiacMag,\n              body: quantitDisp2BodyTemplate,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"quantity\",\n              header: Costanti.DispAlyante,\n              body: quantitDispBodyTemplate,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"total\",\n              header: Costanti.Tot,\n              body: totalBodyTemplate,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"totalTaxed\",\n              header: Costanti.TotTax,\n              body: totalTaxedBodyTemplate,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              className: \"modActionColumn\",\n              rowEditor: true,\n              headerStyle: {\n                width: '7rem'\n              },\n              bodyStyle: {\n                textAlign: 'center'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              body: actionBodyTemplate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-end flex-column align-items-end mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: [Costanti.Tot, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 48\n            }, this), \" \", calcTot()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: [Costanti.Iva, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 48\n            }, this), \" \", calcIva()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: [Costanti.TotTax, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 48\n            }, this), \" \", calcTotIva()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-end my-4 w-100\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"p-button mx-0 justify-content-center\",\n            onClick: () => openNew(),\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-plus-circle mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 110\n            }, this), \" \", Costanti.AggProdOrd, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-field p-col-12 mt-5 \",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-float-label\",\n          children: [/*#__PURE__*/_jsxDEV(InputTextarea, {\n            maxLength: 240,\n            onKeyUp: e => onKeyUpHandler(e),\n            style: {\n              width: '100%'\n            },\n            id: \"textarea\",\n            value: value2,\n            onChange: e => setValue2(e.target.value),\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"textarea\",\n            children: Costanti.Note\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: mexInputTextarea\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 69\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"p-text-center p-text-bold\",\n      children: [Costanti.StatOrd, /*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".pi-info-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-info-circle ml-2\",\n        \"data-pr-tooltip\": \"Puoi approvare o annullare l'ordine semplicemente selezionando uno dei seguenti stati.\",\n        \"data-pr-position\": \"top\",\n        \"data-pr-at\": \"top\",\n        \"data-pr-my\": \"bottom-5\",\n        style: {\n          fontSize: '1.4rem',\n          cursor: 'pointer'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mb-3\",\n      children: /*#__PURE__*/_jsxDEV(SelectButton, {\n        className: \"selWar mr-4\",\n        value: selectedStatus,\n        options: status,\n        onChange: e => changeStatus(e),\n        placeholder: \"Stato ordine\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center my-3\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        id: \"invia\",\n        className: \"p-button saveList justify-content-center float-right ionicon mx-0 mt-3\",\n        onClick: (e, key) => Invia(e, key = 'Approvato'),\n        children: Costanti.salva\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 760,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      visible: deleteResultDialog,\n      style: {\n        width: '450px'\n      },\n      header: Costanti.Elimina,\n      modal: true,\n      footer: deleteResultDialogFooter,\n      onHide: hideDeleteResultDialog,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirmation-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-exclamation-triangle p-mr-3\",\n          style: {\n            fontSize: '1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 21\n        }, this), result && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [Costanti.ElProd, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: (_result$product = result.product) === null || _result$product === void 0 ? void 0 : _result$product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 56\n          }, this), \"?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 32\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      visible: addProdDialog,\n      header: tooltipMex,\n      modal: true,\n      className: \"p-fluid modalBox\",\n      footer: productDialogFooter,\n      onHide: hideDialog,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"datatable-responsive-demo wrapper my-4\",\n          children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n            value: resultsCopia,\n            fields: fields,\n            dataKey: \"id\",\n            paginator: true,\n            rows: 5,\n            rowsPerPageOptions: [5, 10, 20, 50],\n            selectionMode: \"single\",\n            selection: selectedResults,\n            onSelectionChange: e => {\n              setSelectedResults(e.value);\n              addProd(e);\n            },\n            responsiveLayout: \"scroll\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 664,\n    columnNumber: 9\n  }, this);\n};\n_s(ModificaProdottiOrdine, \"/RXOtL4MOoAl0F2yIshZrB98lH8=\");\n_c = ModificaProdottiOrdine;\nexport default ModificaProdottiOrdine;\nvar _c;\n$RefreshReg$(_c, \"ModificaProdottiOrdine\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Nav", "CustomDataTable", "InputText", "<PERSON><PERSON>", "<PERSON><PERSON>", "DataTable", "Column", "Dialog", "Toast", "Calendar", "APIRequest", "InputNumber", "Dropdown", "<PERSON><PERSON><PERSON>", "Messages", "SelectButton", "InputTextarea", "JoyrideGen", "distributoreGestioneLogisticaOrdini", "jsxDEV", "_jsxDEV", "ModificaProdottiOrdine", "props", "_s", "_result$product", "emptyResult", "id", "name", "image", "product", "description", "category", "price", "quantity", "rating", "inventoryStatus", "value1", "setValue1", "value2", "setValue2", "value3", "setValue3", "products", "setProducts", "mex", "setMex", "mexInputTextarea", "setMexInputTextarea", "selectedStatus", "setSelectedStatus", "resultsCopia", "setResultsCopia", "results", "setResults", "results2", "setResults2", "results3", "setResults3", "warehouse", "setWarehouse", "date", "setDate", "date2", "setDate2", "selectedResults", "setSelectedResults", "selectedWarehouse", "setSelectedWarehouse", "codDep", "setCodDep", "deleteResultDialog", "setDeleteResultDialog", "addProdDialog", "setAddProdDialog", "displayed", "setDisplayed", "result", "setResult", "classMessage", "setClassMessage", "toast", "msgs2", "status", "originalRows", "dataTableFuncMap", "prodIns", "idRetailer", "fetchData", "url", "localStorage", "getItem", "res", "data", "deliveryDate", "current", "show", "severity", "sticky", "content", "Fragment", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "orderProducts", "deliveryDestination", "note", "orderDate", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "idRegistry", "firstName", "newDate", "getMonth", "getDate", "getFullYear", "prodotti", "idPriceList2", "priceListProducts", "idAffiliate", "i", "length", "j", "productId", "idProduct", "splice", "prod", "for<PERSON>ach", "element", "availability", "idProduct2", "productsAvailabilities", "find", "el", "_el$idWarehouse", "idWarehouse", "x", "externalCode", "undefined", "push", "then", "warehouses", "entry", "warehouseName", "value", "code", "catch", "e", "_e$response", "_e$response2", "console", "log", "summary", "detail", "concat", "response", "message", "life", "JSON", "parse", "window", "sessionStorage", "path", "filter", "idProductsPackaging", "pcsXPackage", "pcsXpackage", "count", "item", "colli", "_e$response3", "_e$response4", "onWarehouseSelect", "setItem", "_e$response5", "_e$response6", "changeStatus", "Invia", "totProd", "items", "tot", "NumberFormat", "style", "currency", "maximumFractionDigits", "parseFloat", "unitPrice", "total", "replace", "unitMeasure", "tax", "body", "orderNumber", "termsPayment", "paymentStatus", "totalTaxed", "numberOfCustomers", "url2", "setTimeout", "location", "pathname", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "_e$response1", "_e$response10", "quantitDispBodyTemplate", "rowData", "_el$idWarehouse2", "className", "Giacenza", "quantitDisp2BodyTemplate", "GiacMag", "pcxpkgBodyTemplate", "moltiplicatore", "formatBodyTemplate2", "Formato", "descriptionBodyTemplate2", "Nome", "externalCodeBodyTemplate2", "totalBodyTemplate", "<PERSON><PERSON>", "totalTaxedBodyTemplate", "TotTax", "onEditorValueChange", "productKey", "formato", "updatedProducts", "rowIndex", "field", "parseInt", "calcIva", "calcTot", "calcTotIva", "onEditorValueChange2", "quantityEditor2", "onValueChange", "quantitBodyTemplate", "quantità", "quantitBodyTemplate3", "statusEditor", "object", "productsPackagings", "obj", "options", "optionLabel", "optionValue", "onChange", "width", "placeholder", "itemTemplate", "option", "toLowerCase", "confirmDeleteResult", "hideDeleteResultDialog", "deleteResult", "_products", "val", "deleteResultDialogFooter", "label", "icon", "onClick", "actionBodyTemplate", "target", "addProd", "iva", "rows", "document", "querySelectorAll", "scrollIntoView", "behavior", "block", "onRowEditInit", "event", "index", "_objectSpread", "onRowEditCancel", "openNew", "hideDialog", "tooltipMex", "ProdAggInOrd", "fontSize", "cursor", "toString", "split", "join", "totIva", "onKeyUpHandler", "currentTarget", "max<PERSON><PERSON><PERSON>", "productDialogFooter", "<PERSON><PERSON>", "fields", "header", "<PERSON><PERSON><PERSON>", "sortable", "showHeader", "exCode", "QtaDisp", "title", "ref", "ModOrder", "htmlFor", "IndCons", "DCons", "dateFormat", "showIcon", "backgroundColor", "ProdInOrd", "AggProdOrd", "editMode", "autoLayout", "csvSeparator", "UnitMis", "editor", "<PERSON><PERSON>", "Quantità", "DispAlyante", "rowEditor", "headerStyle", "bodyStyle", "textAlign", "<PERSON><PERSON>", "onKeyUp", "Note", "StatOrd", "key", "salva", "visible", "Elimina", "modal", "footer", "onHide", "<PERSON><PERSON><PERSON>", "dataKey", "paginator", "rowsPerPageOptions", "selectionMode", "selection", "onSelectionChange", "responsiveLayout", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/modificaProdOrdine.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ModificaProdottiOrdine - operazioni sulla modifica dell'ordine\n*\n*/\nimport React, { useState, useEffect, useRef } from 'react';\nimport Nav from '../components/navigation/Nav';\nimport CustomDataTable from '../components/customDataTable';\nimport { InputText } from 'primereact/inputtext';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../components/traduttore/const';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { Dialog } from 'primereact/dialog';\nimport { Toast } from 'primereact/toast';\nimport { Calendar } from 'primereact/calendar';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Tooltip } from 'primereact/tooltip';\nimport { Messages } from 'primereact/messages';\nimport { SelectButton } from 'primereact/selectbutton';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport { JoyrideGen } from '../components/footer/joyride';\nimport { distributoreGestioneLogisticaOrdini } from '../components/route';\nimport '../css/MultiSelectDemo.css';\nimport '../css/modale.css';\n\nconst ModificaProdottiOrdine = (props) => {\n    let emptyResult = {\n        id: null,\n        name: '',\n        image: null,\n        product: [],\n        description: '',\n        category: null,\n        price: 0,\n        quantity: 0,\n        rating: 0,\n        inventoryStatus: 'INSTOCK'\n    };\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti\n    const [value1, setValue1] = useState('');\n    const [value2, setValue2] = useState('');\n    const [value3, setValue3] = useState([]);\n    const [products, setProducts] = useState([]);\n    const [mex, setMex] = useState([]);\n    const [mexInputTextarea, setMexInputTextarea] = useState([]);\n    const [selectedStatus, setSelectedStatus] = useState(null);\n    const [resultsCopia, setResultsCopia] = useState([]);\n    const [results, setResults] = useState([]);\n    const [results2, setResults2] = useState([]);\n    const [results3, setResults3] = useState([]);\n    const [warehouse, setWarehouse] = useState([]);\n    const [date, setDate] = useState(null);\n    const [date2, setDate2] = useState(null);\n    const [selectedResults, setSelectedResults] = useState(null);\n    const [selectedWarehouse, setSelectedWarehouse] = useState(null);\n    const [codDep, setCodDep] = useState(null);\n    const [deleteResultDialog, setDeleteResultDialog] = useState(false);\n    const [addProdDialog, setAddProdDialog] = useState(false);\n    const [displayed, setDisplayed] = useState(false);\n    const [result, setResult] = useState(emptyResult);\n    const [classMessage, setClassMessage] = useState(\"col-11 mx-auto\")\n    const toast = useRef(null);\n    const msgs2 = useRef(null);\n    const status = ['Annullato', 'Registrato', 'Approvato'];\n    let originalRows = {};\n    const dataTableFuncMap = {\n        'products': setProducts\n    };\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        var prodIns = [];\n        var idRetailer = [];\n        /* Reperisco gli ordini */\n        async function fetchData() {\n            let url = 'orders/?id=' + localStorage.getItem(\"datiComodo\");\n            var res = await APIRequest('GET', url)\n            /* Avviso nel caso la data di consegna non sia impostata */\n            if (res.data.deliveryDate === null) {\n                msgs2.current.show({\n                    severity: 'error', sticky: true, content: (\n                        <React.Fragment>\n                            La data di consegna non è stata impostata inserirne una prima di procedere\n                        </React.Fragment>\n                    )\n                });\n            }\n            setSelectedStatus(res.data.status)\n            setResults3(res.data)\n            setResults2(res.data.orderProducts);\n            setValue1(res.data.deliveryDestination);\n            setValue2(res.data.note);\n            setDate(res.data.orderDate);\n            setDate2(res.data.deliveryDate);\n            idRetailer = res.data.idRetailer\n            setValue3(res.data.orderProducts);\n            prodIns = res.data.orderProducts;\n            setMex('del: ' + new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(res.data.orderDate)) + \" per il cliente: \" + res.data.idRetailer.idRegistry.firstName)\n            /* GET CURRENT DATE */\n            var newDate = new Date();\n            let month = newDate.getMonth() + 1;\n            let day = newDate.getDate();\n            if (month < 10) {\n                month = '0' + month;\n            }\n            if (day < 10) {\n                day = '0' + day;\n            }\n            newDate = newDate.getFullYear() + '-' + month + '-' + day;\n            if (idRetailer !== []) {\n                /* Reperisco i prodotti all'interno del listino retailer */\n                url = 'pricelistretailer/?idRetailer=' + idRetailer.id\n                res = await APIRequest('GET', url)\n                if (res.data !== '') {\n                    var prodotti = res.data.idPriceList2.priceListProducts;\n                } else {\n                    /* Reperisco i prodotti all'interno del listino affiliato nel caso non sia presente quello del retailer */\n                    url = 'pricelistaffiliate/?idAffiliate=' + idRetailer.idAffiliate\n                    res = await APIRequest('GET', url)\n                    prodotti = res.data.idPriceList2.priceListProducts;\n                }\n                /* Escludo i prodotti presenti nell'ordine */\n                for (var i = 0; i < prodIns.length; i++) {\n                    for (var j = 0; j < prodotti.length; j++) {\n                        if (prodIns[i].productId === prodotti[j].idProduct) {\n                            prodotti.splice(j, 1);\n                        }\n                    }\n                }\n                var prod = []\n                prodotti.forEach(element => {\n                    var availability = element.idProduct2.productsAvailabilities.find(el => el.idWarehouse?.codDep === '00')\n                    var x = {\n                        id: element.idProduct,\n                        description: element.idProduct2.description,\n                        externalCode: element.idProduct2.externalCode,\n                        availability: availability !== undefined ? availability.availability : 'Non disponibile'\n                    }\n                    prod.push(x)\n                })\n                setResultsCopia(prod)\n                setResults(prodotti);\n            }\n            await APIRequest(\"GET\", \"warehouses/\")\n                .then((res) => {\n                    var warehouses = []\n                    for (var entry of res.data) {\n                        var x = {\n                            name: entry.warehouseName,\n                            value: entry.id,\n                            code: entry.codDep\n                        }\n                        warehouses.push(x)\n                    }\n                    setWarehouse(warehouses)\n                })\n                .catch((e) => {\n                    console.log(e);\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n            var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n            if (idWarehouse !== null && idWarehouse !== 0) {\n                var path = 'productsposition?idWarehouse=' + idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse;\n                setSelectedWarehouse(idWarehouse);\n                await APIRequest(\"GET\", path)\n                    .then(res => {\n                        prodIns.forEach(element => {\n                            var find = res.data.filter(el => el.idProductsPackaging.idProduct.id === element.product.id && el.idProductsPackaging.pcsXPackage === element.pcsXpackage)\n                            if (find !== undefined) {\n                                var count = 0\n                                find.forEach(item => {\n                                    count += item.colli\n                                })\n                                element.productsAvailabilities = count\n                            }\n                        })\n                        /* setResults4(res.data) */\n                    }).catch((e) => {\n                        console.log(e)\n                        toast.current.show({\n                            severity: \"error\",\n                            summary: \"Siamo spiacenti\",\n                            detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                            life: 3000,\n                        });\n                    })\n            } else {\n                setDisplayed(true)\n            }\n        }\n        fetchData()\n    }, []);\n    /* Seleziono il punto vendita e controllo che abbia un listino associato */\n    const onWarehouseSelect = async (e) => {\n        setSelectedWarehouse(e.value);\n        var find = warehouse.find(el => el.value === e.value)\n        setCodDep(find.code)\n        var url = 'productsposition?idWarehouse=' + e.value;\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        await APIRequest('GET', url)\n            .then(res => {\n                value3.forEach(element => {\n                    var find = res.data.filter(el => el.idProductsPackaging.idProduct.id === element.product.id && el.idProductsPackaging.pcsXPackage === element.pcsXpackage)\n                    if (find !== undefined) {\n                        var count = 0\n                        find.forEach(item => {\n                            count += item.colli\n                        })\n                        element.productsAvailabilities = count\n                    }\n                })\n                /* setResults4(res.data) */\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    const changeStatus = async (e) => {\n        setSelectedStatus(e.value)\n    }\n    const Invia = async () => {\n\n        var totProd = [];\n        let url = 'orders/?id=' + localStorage.getItem(\"datiComodo\");\n        value3.forEach(items => {\n            var tot = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(items.colli * items.pcsXpackage * parseFloat(items.unitPrice))\n            totProd.push({\n                id: items.product.id,\n                quantity: items.colli * items.pcsXpackage,\n                total: parseFloat(tot.replace(\"€\", \"\").replace(\".\", \"\").replace(\",\", \".\")),\n                unitMeasure: items.unitMeasure,\n                pcsXpackage: items.pcsXpackage,\n                unitPrice: items.unitPrice,\n                colli: items.colli,\n                tax: items.tax\n            })\n        })\n        var total = 0;\n        totProd.forEach(element => {\n            total += element.total\n        })\n        var tot = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(total)\n        //Dichiarazione degli elementi da passare nel JSON con i rispettivi valori\n        let body = {\n            deliveryDate: date2,\n            deliveryDestination: value1,\n            id: results3.id,\n            idRetailer: results3.idRetailer,\n            note: value2,\n            orderDate: date,\n            orderNumber: results3.orderNumber,\n            products: totProd,\n            termsPayment: results3.termsPayment,\n            paymentStatus: '',\n            total: parseFloat(tot.replace(\"€\", \"\").replace(\".\", \"\").replace(\",\", \".\")),\n            totalTaxed: '',\n            status: selectedStatus,\n            numberOfCustomers: results3.numberOfCustomers,\n        }\n        if (selectedStatus === 'Approvato' && selectedWarehouse !== null) {\n            await APIRequest('PUT', url, body)\n                .then(async res => {\n                    console.log(res.data);\n                    toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"La modifica dell'ordine è avvenuta con successo\", life: 3000 });\n                    var url2 = 'documents/transformorder?idOrder=' + localStorage.getItem(\"datiComodo\") + '&idWarehouse=' + selectedWarehouse\n                    await APIRequest(\"GET\", url2)\n                        .then((res) => {\n                            console.log(res.data)\n                            toast.current.show({\n                                severity: \"success\",\n                                summary: \"Ottimo!\",\n                                detail: \"Documento creato\",\n                                life: 3000,\n                            });\n                            setTimeout(() => {\n                                window.location.pathname = distributoreGestioneLogisticaOrdini;\n                            }, 3000)\n                        })\n                        .catch((e) => {\n                            console.log(e);\n                            toast.current.show({\n                                severity: \"error\",\n                                summary: \"Siamo spiacenti\",\n                                detail: `Non è stato possibile creare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                                life: 3000,\n                            });\n                        });\n                }).catch((e) => {\n                    console.log(e)\n                    toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile procedere con la modifica. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                })\n        } else if (selectedStatus !== 'Approvato') {\n            await APIRequest('PUT', url, body)\n                .then(async res => {\n                    console.log(res.data);\n                    toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"La modifica dell'ordine è avvenuta con successo\", life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = distributoreGestioneLogisticaOrdini;\n                    }, 3000)\n                }).catch((e) => {\n                    console.log(e)\n                    toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile procedere con la modifica. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                })\n        } else {\n            toast.current.show({ severity: 'warn', summary: 'Attenzione!', detail: \"E' necessario selezionare un magazzino prima di procedere\", life: 3000 });\n        }\n    }\n    /* Reperisco quantità disponibili per i prodotti */\n    const quantitDispBodyTemplate = (rowData) => {\n        if (rowData.product.productsAvailabilities !== null) {\n            var availability = rowData.product.productsAvailabilities.find(el => el.idWarehouse?.codDep === (codDep !== null ? codDep : '00'))\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Giacenza}</span>\n                    {availability !== undefined ? availability.availability : 'Non disponibile'}\n                </React.Fragment>\n            );\n        }\n    }\n    /* Reperisco quantità disponibili per i prodotti */\n    const quantitDisp2BodyTemplate = (rowData) => {\n        if (rowData.productsAvailabilities !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.GiacMag}</span>\n                    {rowData.productsAvailabilities}\n                </React.Fragment>\n            );\n        }\n    }\n    /* Reperisco i valori del moltiplicatore per i prodotti */\n    const pcxpkgBodyTemplate = (rowData) => {\n        if (rowData.moltiplicatore !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">Pezzi per package</span>\n                    {rowData.moltiplicatore}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">Pezzi per package</span>\n                    {rowData.pcsXpackage}\n                </React.Fragment>\n            );\n        }\n    }\n    /* Reperisco il formato dei prodotti */\n    const formatBodyTemplate2 = (rowData) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Formato}</span>\n                {rowData.unitMeasure}\n            </React.Fragment>\n        );\n    }\n    /* Reperisco il nome dei prodotti */\n    const descriptionBodyTemplate2 = (rowData) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {rowData.product.description}\n            </React.Fragment>\n        );\n    }\n    /* Reperisco i codici esterni dei prodotti */\n    const externalCodeBodyTemplate2 = (rowData) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {rowData.product.externalCode}\n            </React.Fragment>\n        );\n    }\n    /* Reperisco i totali dei prodotti */\n    const totalBodyTemplate = (rowData) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Tot}</span>\n                {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(rowData.total)}\n            </React.Fragment>\n        );\n    }\n    /* Reperisco i totali tassati dei prodotti */\n    const totalTaxedBodyTemplate = (rowData) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(rowData.totalTaxed)}\n            </React.Fragment>\n        );\n    }\n    /* Cambio il valore dell'unità di misura dei prodotti e richiamo le funzioni per aggiornare i totali */\n    const onEditorValueChange = (productKey, props, value, status) => {\n        var formato = ''\n        if (status !== undefined) {\n            status.forEach(element => {\n                if (element.pcsXPackage === value) {\n                    formato = element.unitMeasure\n                }\n            })\n        }\n        let updatedProducts = [...props.value];\n        updatedProducts[props.rowIndex][props.field] = value;\n        updatedProducts[props.rowIndex][\"unitMeasure\"] = formato;\n        updatedProducts[props.rowIndex]['total'] = updatedProducts[props.rowIndex]['colli'] * updatedProducts[props.rowIndex]['pcsXpackage'] * parseFloat(updatedProducts[props.rowIndex]['unitPrice']);\n        if (updatedProducts[props.rowIndex]['tax'] !== undefined) {\n            updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['tax']) / 100;\n        } else {\n            updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['iva']) / 100;\n        }\n        dataTableFuncMap[`${productKey}`](updatedProducts);\n        calcIva()\n        calcTot()\n        calcTotIva()\n    }\n    /* Cambio il valore del totale dei prodotti e richiamo le funzioni per aggiornare i totali */\n    const onEditorValueChange2 = (productKey, props, value) => {\n        let updatedProducts = [...props.value];\n        updatedProducts[props.rowIndex][props.field] = value;\n        updatedProducts[props.rowIndex]['total'] = value * updatedProducts[props.rowIndex]['pcsXpackage'] * parseFloat(updatedProducts[props.rowIndex]['unitPrice']);\n        if (updatedProducts[props.rowIndex]['tax'] !== undefined) {\n            updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['tax']) / 100;\n        } else {\n            updatedProducts[props.rowIndex]['totalTaxed'] = parseFloat(updatedProducts[props.rowIndex]['total']) + parseFloat(updatedProducts[props.rowIndex]['total']) * parseInt(updatedProducts[props.rowIndex]['iva']) / 100;\n        }\n        dataTableFuncMap[`${productKey}`](updatedProducts);\n        calcIva()\n        calcTot()\n        calcTotIva()\n    }\n    /* Componente inputNumber per modificare i colli */\n    const quantityEditor2 = (productKey, props) => {\n        return <InputNumber value={props.rowData['colli']} onValueChange={(e) => onEditorValueChange2(productKey, props, e.value)} />\n    }\n    /* Ricavo la quantità moltiplicando i colli per il moltiplicatore */\n    const quantitBodyTemplate = (rowData) => {\n        var quantità = 0;\n        if (rowData.quantity !== undefined) {\n            if (rowData.colli !== undefined && rowData.pcsXpackage !== undefined) {\n                quantità = rowData.colli * rowData.pcsXpackage\n            } else {\n                quantità = rowData.quantity\n            }\n            return <span className=\"priceAdded price-filled\">{quantità}</span>\n        } else {\n            return \"\";\n        }\n    }\n    /* Reperisco i colli dei prodotti */\n    const quantitBodyTemplate3 = (rowData) => {\n        if (rowData.colli !== undefined) {\n            return <span className=\"priceAdded\">{rowData.colli}</span>\n        } else {\n            return \"\";\n        }\n    }\n    /* Definisco il dropdown con i possibili formati ed annessi moltiplicatori */\n    const statusEditor = (productKey, props) => {\n        var status = []\n        if (props.rowData.idProduct2 !== undefined) {\n            for (const object of props.rowData.idProduct2.productsPackagings) {\n                status.push({\n                    unitMeasure: object.unitMeasure,\n                    pcsXPackage: object.pcsXPackage\n                })\n            }\n        } else {\n            for (const obj of props.rowData.product.productsPackagings) {\n                status.push({\n                    unitMeasure: obj.unitMeasure,\n                    pcsXPackage: obj.pcsXPackage\n                })\n            }\n        }\n        return (\n            <Dropdown value={props.rowData['pcsXpackage']} options={status} optionLabel=\"unitMeasure\" optionValue=\"pcsXPackage\"\n                onChange={(e) => onEditorValueChange(productKey, props, e.value, status)} style={{ width: '100%' }} placeholder=\"Seleziona...\"\n                itemTemplate={(option) => {\n                    return <span className={`product-badge status-${option.unitMeasure.toLowerCase()}`}>{option.unitMeasure}</span>\n                }} />\n        );\n    }\n    /* Apertura dialogo eliminazione prodotto */\n    const confirmDeleteResult = (result) => {\n        setResult(result);\n        setDeleteResultDialog(true);\n    }\n    /* Chiusura dialogo eliminazione prodotto */\n    const hideDeleteResultDialog = () => {\n        setDeleteResultDialog(false);\n    }\n    /* Eliminazione del prodotto dall'array */\n    const deleteResult = () => {\n        let _products = value3.filter(val => val.id !== result.id);\n        setValue3(_products);\n        _products = results2.filter(val => val.product.id !== result.id);\n        setResults2(_products);\n        setDeleteResultDialog(false);\n        setResult(emptyResult);\n        if (result.idProduct2 === undefined) {\n            result.idProduct2 = result.product;\n        }\n        results.push(result)\n        toast.current.show({ severity: 'success', summary: 'Ottimo !', detail: 'Prodotto eliminato correttamente', life: 3000 });\n    }\n    /* Footer per dialogo cancellazione prodotti */\n    const deleteResultDialogFooter = (\n        <React.Fragment>\n            <Button label=\"No\" icon=\"pi pi-times\" className=\"p-button-text\" onClick={hideDeleteResultDialog} />\n            <Button label=\"Si\" icon=\"pi pi-check\" className=\"p-button-text\" onClick={deleteResult} />\n        </React.Fragment>\n    );\n    /* Icona per azione elimina prodotto */\n    const actionBodyTemplate = (rowData) => {\n        return (\n            <React.Fragment>\n                <Tooltip target=\".p-button-rounded\" />\n                <Button icon=\"pi pi-trash\" className=\"p-button-rounded\" data-pr-tooltip=\"Elimina\" data-pr-position=\"top\" onClick={() => confirmDeleteResult(rowData)} />\n            </React.Fragment>\n        );\n    }\n    /* Aggiungo il prodotto all'ordine e visualizzo l'ultimo prodotto inserito nella lista */\n    const addProd = (e) => {\n        var totProd = []\n        if (e.value !== null) {\n            results.forEach(element => {\n                if (element.idProduct === e.value.id) {\n                    e.value = element;\n                }\n            })\n            if (e.value.colli === undefined) {\n                e.value.colli = 1\n                e.value.pcsXpackage = e.value.idProduct2.productsPackagings[0].pcsXPackage\n                e.value.unitMeasure = e.value.idProduct2.productsPackagings[0].unitMeasure\n                e.value.quantity = e.value.pcsXpackage * e.value.colli\n                e.value.total = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(e.value.colli * e.value.pcsXpackage * parseFloat(e.value.price));\n                totProd = [{\n                    id: e.value.idProduct2.id,\n                    product: e.value.idProduct2,\n                    quantity: e.value.quantity,\n                    total: e.value.total.replace(\"€\", \"\").replace(/\\s/g, '').replace(\",\", '.'),\n                    totalTaxed: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(e.value.total.replace(\"€\", \"\").replace(/\\s/g, '').replace(\",\", '.')) + parseFloat(e.value.total.replace(\"€\", \"\").replace(/\\s/g, '').replace(\",\", '.')) * parseInt(e.value.idProduct2.iva) / 100),\n                    unitMeasure: e.value.unitMeasure,\n                    pcsXpackage: e.value.pcsXpackage,\n                    unitPrice: e.value.price,\n                    colli: e.value.colli,\n                    tax: e.value.idProduct2.iva\n                }]\n                value3.push(totProd[0])\n                for (var i = 0; i < resultsCopia.length; i++) {\n                    if (resultsCopia[i].id === totProd[0].id) {\n                        resultsCopia.splice(i, 1);\n                        break;\n                    }\n                }\n            } else {\n                value3.push(e.value)\n                for (var j = 0; j < resultsCopia.length; j++) {\n                    if (resultsCopia[j].id === e.value.productId) {\n                        resultsCopia.splice(j, 1);\n                        break;\n                    }\n                }\n            }\n            var rows = document.querySelectorAll('#tabProd tr');\n            // line is zero-based\n            // line is the row number that you want to see into view after scroll    \n            rows[rows.length - 1].scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n        }\n    }\n    /* Reperisco il prodotto prima della modifica */\n    const onRowEditInit = (event) => {\n        originalRows[event.index] = { ...products[event.index] };\n    }\n    /* Annullo la modifica sul prodotto */\n    const onRowEditCancel = (event) => {\n        let product = [...products];\n        product[event.index] = originalRows[event.index];\n        delete originalRows[event.index];\n        setProducts(products);\n    }\n    /* Apertura dialogo aggiunta prodotti */\n    const openNew = () => {\n        setAddProdDialog(true);\n    }\n    /* Chiusura dialogo aggiunta prodotti */\n    const hideDialog = () => {\n        setAddProdDialog(false);\n    }\n    /* Tooltip per spiegare all'utente come aggiungere i prodotti all'ordine */\n    const tooltipMex = () => {\n        return (\n            <div>\n                <Tooltip target=\".pi-info-circle\" />\n                <h3 className=\"p-text-center p-text-bold\">{Costanti.ProdAggInOrd} <i className=\"pi pi-info-circle\" data-pr-tooltip=\"Dalla tabella sottostante, seleziona i prodotti che desideri aggiungere a quest'ordine.\" data-pr-position=\"top\" data-pr-at=\"top\" data-pr-my=\"bottom-5\" style={{ fontSize: '1.4rem', cursor: 'pointer' }}></i></h3>\n            </div>\n        )\n    }\n    /* Calcolo il totale e lo formatto in Euro */\n    const calcTot = () => {\n        var tot = 0;\n        if (value3 !== null) {\n            value3.forEach(element => {\n                tot += parseFloat(element.total)\n            })\n            return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(tot)\n        }\n    }\n    /* Calcolo l'iva dalla differenza tra totale e totale ivato e la formatto in Euro */\n    const calcIva = () => {\n        var iva = 0\n        if (value3 !== null) {\n            value3.forEach(element => {\n                iva += parseFloat(element.totalTaxed.toString().split(\",\").join(\".\")) - parseFloat(element.total.toString().split(\",\").join(\".\"))\n            })\n            return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(iva)\n        }\n    }\n    /* Calcolo il totale ivato e lo formatto in Euro */\n    const calcTotIva = () => {\n        var totIva = 0;\n        if (value3 !== null) {\n            value3.forEach(element => {\n                totIva += parseFloat(element.totalTaxed.toString().split(\",\").join(\".\"))\n            })\n            return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(totIva)\n        }\n    }\n    const onKeyUpHandler = (e) => {\n        var mex = 'Inseriti: ' + e.target.value.length + ' di ' + e.currentTarget.maxLength + ' caratteri'\n        setMexInputTextarea(mex)\n    }\n    /* Footer dialogo di aggiunta prodotti */\n    const productDialogFooter = (\n        <React.Fragment>\n            <Button label={Costanti.Chiudi} className=\"p-button-text\" onClick={hideDialog} />\n        </React.Fragment>\n    );\n    const fields = [\n        { field: 'description', header: Costanti.Prodotto, body: 'description', sortable: true, showHeader: true },\n        { field: 'externalCode', header: Costanti.exCode, body: 'externalCode', sortable: true, showHeader: true },\n        { field: 'availability', header: Costanti.QtaDisp, body: 'availability', sortable: true, showHeader: true }\n    ];\n    return (\n        <div className=\"card border-0\">\n            {displayed &&\n                <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n            }\n            <Toast ref={toast} />\n            <Nav />\n            <div className=\"col-12 solid-head\">\n                <h1 className=\"m-0\">\n                    {Costanti.ModOrder}\n                    <span className=\"text-center d-block subtitle\">{mex}</span>\n                </h1>\n            </div>\n            <div className=\"row\">\n                <div className={classMessage}>\n                    <Messages ref={msgs2} />\n                </div>\n            </div>\n            <div className=\"mx-5 mt-3 container-edit-order pb-2\">\n                <div className=\"col-12 my-2\">\n                    <div className=\"modList row\">\n                        {/* Visualizzo l'indirizzo e la data di consegna */}\n                        <div className=\"p-field col-12 col-sm-6 my-0 mb-3\">\n                            <label htmlFor=\"description\">{Costanti.IndCons}</label>\n                            <InputText id=\"textDesc\" className=\"m-0\" value={value1} onChange={(e) => setValue1(e.target.value)} />\n                        </div>\n                        <div className=\"p-field col-12 col-sm-6 my-0 mt-3 mt-sm-0\">\n                            <label htmlFor=\"isValid\">{Costanti.DCons}</label>\n                            <Calendar id=\"selValDate\" className=\"w-100\" value={date2} dateFormat=\"dd/mm/yy\" onChange={(e) => { setDate2(e.value); setClassMessage('d-none') }} placeholder={new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(date2))} showIcon />\n                        </div>\n                    </div>\n                </div>\n                <div className=\"col-12\">\n                    {/* Bottone con apertura modale per visualizzare i prodotti aggiungibili */}\n                    <div className=\"row border-top mx-0\" style={{ backgroundColor: \"#f3f4f5\" }}>\n                        <div className='col-12 col-lg-4 my-auto'>\n                            <h3 className=\"p-text-bold mb-0\">{Costanti.ProdInOrd}</h3>\n                        </div>\n                        <div className='col-12 col-lg-4 my-auto'>\n                            <div className=\"d-flex justify-content-center col-md-12\">\n                                <div className=\"row rincaroPrezzi ml-2 p-3\">\n                                    <Dropdown className=\"selWar mr-4\" value={selectedWarehouse} options={warehouse} onChange={onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" />\n                                </div>\n                            </div>\n                        </div>\n                        <div className='col-12 col-lg-4'>\n                            <div className=\"d-flex justify-content-end my-4 w-100\">\n                                <Button className=\"p-button mx-0 justify-content-center\" onClick={() => openNew()} > <i className=\"pi pi-plus-circle mr-2\"></i> {Costanti.AggProdOrd} </Button>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"datatable-responsive-demo wrapper mb-4\">\n                        <DataTable id=\"tabProd\" className=\"p-datatable-responsive-demo editable-prices-table\" value={value3} editMode=\"row\" autoLayout=\"true\" onRowEditInit={onRowEditInit} onRowEditCancel={onRowEditCancel} csvSeparator=\";\" >\n                            <Column field=\"externalCode\" header={Costanti.exCode} body={externalCodeBodyTemplate2} sortable ></Column>\n                            <Column field=\"description\" header={Costanti.Prodotto} body={descriptionBodyTemplate2} sortable ></Column>\n                            <Column field=\"pcsXpackage\" header={Costanti.UnitMis} body={formatBodyTemplate2} /* body={productsPackagingsBodyTemplate} */ editor={(props) => statusEditor('products', props)} sortable ></Column>\n                            <Column field=\"pcsXpackage\" header=\"Pezzi per package\" body={pcxpkgBodyTemplate} sortable ></Column>\n                            <Column field=\"colli\" header={Costanti.Colli} body={quantitBodyTemplate3} editor={(props) => quantityEditor2('products', props)} sortable ></Column>\n                            <Column field=\"quantity\" header={Costanti.Quantità} body={quantitBodyTemplate} sortable ></Column>\n                            <Column field=\"quantity\" header={Costanti.GiacMag} body={quantitDisp2BodyTemplate} sortable ></Column>\n                            <Column field=\"quantity\" header={Costanti.DispAlyante} body={quantitDispBodyTemplate} sortable ></Column>\n                            <Column field=\"total\" header={Costanti.Tot} body={totalBodyTemplate} sortable ></Column>\n                            <Column field=\"totalTaxed\" header={Costanti.TotTax} body={totalTaxedBodyTemplate} sortable ></Column>\n                            <Column className=\"modActionColumn\" rowEditor headerStyle={{ width: '7rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                            <Column body={actionBodyTemplate} ></Column>\n                        </DataTable>\n                    </div>\n                    {/* Visualizzo i valori di totale totale ivato e differenza tra totale e totale ivato */}\n                    <div className=\"d-flex justify-content-end flex-column align-items-end mt-3\">\n                        <span className=\"mr-3\"><b>{Costanti.Tot}:</b> {calcTot()}</span>\n                        <span className=\"mr-3\"><b>{Costanti.Iva}:</b> {calcIva()}</span>\n                        <span className=\"mr-3\"><b>{Costanti.TotTax}:</b> {calcTotIva()}</span>\n                    </div>\n                    {/* Bottone con apertura modale per visualizzare i prodotti aggiungibili */}\n                    <div className=\"d-flex justify-content-end my-4 w-100\">\n                        <Button className=\"p-button mx-0 justify-content-center\" onClick={() => openNew()} > <i className=\"pi pi-plus-circle mr-2\"></i> {Costanti.AggProdOrd} </Button>\n                    </div>\n\n                </div>\n                <div className=\"p-field p-col-12 mt-5 \">\n                    {/* InputTextAria per aggiungere o modificare note dell'ordine */}\n                    <span className=\"p-float-label\">\n                        <InputTextarea maxLength={240} onKeyUp={(e) => onKeyUpHandler(e)} style={{ width: '100%' }} id=\"textarea\" value={value2} onChange={(e) => setValue2(e.target.value)} rows={3} />\n                        <label htmlFor=\"textarea\">{Costanti.Note}</label>\n                        <div className='d-flex justify-content-end'><span>{mexInputTextarea}</span></div>\n                    </span>\n                </div>\n            </div>\n            {/* Visualizzo lo stato dell'ordine e posso modificarlo con il selectButton */}\n            <h3 className=\"p-text-center p-text-bold\">\n                {Costanti.StatOrd}\n                <Tooltip target=\".pi-info-circle\" />\n                <i className=\"pi pi-info-circle ml-2\" data-pr-tooltip=\"Puoi approvare o annullare l'ordine semplicemente selezionando uno dei seguenti stati.\" data-pr-position=\"top\" data-pr-at=\"top\" data-pr-my=\"bottom-5\" style={{ fontSize: '1.4rem', cursor: 'pointer' }}></i>\n            </h3>\n            <div className=\"d-flex justify-content-center mb-3\">\n                <SelectButton className=\"selWar mr-4\" value={selectedStatus} options={status} onChange={(e) => changeStatus(e)} placeholder=\"Stato ordine\" />\n            </div>\n            <div className=\"d-flex justify-content-center my-3\">\n                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                <Button id=\"invia\" className=\"p-button saveList justify-content-center float-right ionicon mx-0 mt-3\" onClick={(e, key) => Invia(e, key = 'Approvato')}>{Costanti.salva}</Button>\n            </div>\n            <Dialog visible={deleteResultDialog} style={{ width: '450px' }} header={Costanti.Elimina} modal footer={deleteResultDialogFooter} onHide={hideDeleteResultDialog}>\n                <div className=\"confirmation-content\">\n                    <i className=\"pi pi-exclamation-triangle p-mr-3\" style={{ fontSize: '1rem' }} />\n                    {result && <span>{Costanti.ElProd} <b>{result.product?.description}</b>?</span>}\n                </div>\n            </Dialog>\n            <Dialog visible={addProdDialog} header={tooltipMex} modal className=\"p-fluid modalBox\" footer={productDialogFooter} onHide={hideDialog}>\n                <div className=\"col-12\">\n                    {/* <hr /> */}\n                    <div className=\"datatable-responsive-demo wrapper my-4\">\n                        <CustomDataTable\n                            value={resultsCopia}\n                            fields={fields}\n                            dataKey=\"id\"\n                            paginator\n                            rows={5}\n                            rowsPerPageOptions={[5, 10, 20, 50]}\n                            selectionMode=\"single\"\n                            selection={selectedResults}\n                            onSelectionChange={e => { setSelectedResults(e.value); addProd(e) }}\n                            responsiveLayout=\"scroll\"\n                        />\n                    </div>\n                </div>\n            </Dialog>\n        </div>\n    );\n}\n\nexport default ModificaProdottiOrdine;\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,GAAG,MAAM,8BAA8B;AAC9C,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,mCAAmC,QAAQ,qBAAqB;AACzE,OAAO,4BAA4B;AACnC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,sBAAsB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACtC,IAAIC,WAAW,GAAG;IACdC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE,CAAC;IACTC,eAAe,EAAE;EACrB,CAAC;EACD;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+C,GAAG,EAAEC,MAAM,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+D,IAAI,EAAEC,OAAO,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACiE,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACuE,MAAM,EAAEC,SAAS,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACyE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6E,SAAS,EAAEC,YAAY,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+E,MAAM,EAAEC,SAAS,CAAC,GAAGhF,QAAQ,CAAC4B,WAAW,CAAC;EACjD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,gBAAgB,CAAC;EAClE,MAAMmF,KAAK,GAAGjF,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMkF,KAAK,GAAGlF,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMmF,MAAM,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;EACvD,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,MAAMC,gBAAgB,GAAG;IACrB,UAAU,EAAEzC;EAChB,CAAC;EACD;EACA7C,SAAS,CAAC,MAAM;IACZ,IAAIuF,OAAO,GAAG,EAAE;IAChB,IAAIC,UAAU,GAAG,EAAE;IACnB;IACA,eAAeC,SAASA,CAAA,EAAG;MACvB,IAAIC,GAAG,GAAG,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC5D,IAAIC,GAAG,GAAG,MAAMjF,UAAU,CAAC,KAAK,EAAE8E,GAAG,CAAC;MACtC;MACA,IAAIG,GAAG,CAACC,IAAI,CAACC,YAAY,KAAK,IAAI,EAAE;QAChCZ,KAAK,CAACa,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE,IAAI;UAAEC,OAAO,eACpC9E,OAAA,CAACxB,KAAK,CAACuG,QAAQ;YAAAC,QAAA,EAAC;UAEhB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB;QAExB,CAAC,CAAC;MACN;MACAvD,iBAAiB,CAAC0C,GAAG,CAACC,IAAI,CAACV,MAAM,CAAC;MAClCzB,WAAW,CAACkC,GAAG,CAACC,IAAI,CAAC;MACrBrC,WAAW,CAACoC,GAAG,CAACC,IAAI,CAACa,aAAa,CAAC;MACnCpE,SAAS,CAACsD,GAAG,CAACC,IAAI,CAACc,mBAAmB,CAAC;MACvCnE,SAAS,CAACoD,GAAG,CAACC,IAAI,CAACe,IAAI,CAAC;MACxB9C,OAAO,CAAC8B,GAAG,CAACC,IAAI,CAACgB,SAAS,CAAC;MAC3B7C,QAAQ,CAAC4B,GAAG,CAACC,IAAI,CAACC,YAAY,CAAC;MAC/BP,UAAU,GAAGK,GAAG,CAACC,IAAI,CAACN,UAAU;MAChC7C,SAAS,CAACkD,GAAG,CAACC,IAAI,CAACa,aAAa,CAAC;MACjCpB,OAAO,GAAGM,GAAG,CAACC,IAAI,CAACa,aAAa;MAChC5D,MAAM,CAAC,OAAO,GAAG,IAAIgE,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACxB,GAAG,CAACC,IAAI,CAACgB,SAAS,CAAC,CAAC,GAAG,mBAAmB,GAAGjB,GAAG,CAACC,IAAI,CAACN,UAAU,CAAC8B,UAAU,CAACC,SAAS,CAAC;MAC/M;MACA,IAAIC,OAAO,GAAG,IAAIH,IAAI,CAAC,CAAC;MACxB,IAAIH,KAAK,GAAGM,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;MAClC,IAAIR,GAAG,GAAGO,OAAO,CAACE,OAAO,CAAC,CAAC;MAC3B,IAAIR,KAAK,GAAG,EAAE,EAAE;QACZA,KAAK,GAAG,GAAG,GAAGA,KAAK;MACvB;MACA,IAAID,GAAG,GAAG,EAAE,EAAE;QACVA,GAAG,GAAG,GAAG,GAAGA,GAAG;MACnB;MACAO,OAAO,GAAGA,OAAO,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGT,KAAK,GAAG,GAAG,GAAGD,GAAG;MACzD,IAAIzB,UAAU,KAAK,EAAE,EAAE;QACnB;QACAE,GAAG,GAAG,gCAAgC,GAAGF,UAAU,CAAC5D,EAAE;QACtDiE,GAAG,GAAG,MAAMjF,UAAU,CAAC,KAAK,EAAE8E,GAAG,CAAC;QAClC,IAAIG,GAAG,CAACC,IAAI,KAAK,EAAE,EAAE;UACjB,IAAI8B,QAAQ,GAAG/B,GAAG,CAACC,IAAI,CAAC+B,YAAY,CAACC,iBAAiB;QAC1D,CAAC,MAAM;UACH;UACApC,GAAG,GAAG,kCAAkC,GAAGF,UAAU,CAACuC,WAAW;UACjElC,GAAG,GAAG,MAAMjF,UAAU,CAAC,KAAK,EAAE8E,GAAG,CAAC;UAClCkC,QAAQ,GAAG/B,GAAG,CAACC,IAAI,CAAC+B,YAAY,CAACC,iBAAiB;QACtD;QACA;QACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,OAAO,CAAC0C,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACK,MAAM,EAAEC,CAAC,EAAE,EAAE;YACtC,IAAI3C,OAAO,CAACyC,CAAC,CAAC,CAACG,SAAS,KAAKP,QAAQ,CAACM,CAAC,CAAC,CAACE,SAAS,EAAE;cAChDR,QAAQ,CAACS,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;YACzB;UACJ;QACJ;QACA,IAAII,IAAI,GAAG,EAAE;QACbV,QAAQ,CAACW,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,YAAY,GAAGD,OAAO,CAACE,UAAU,CAACC,sBAAsB,CAACC,IAAI,CAACC,EAAE;YAAA,IAAAC,eAAA;YAAA,OAAI,EAAAA,eAAA,GAAAD,EAAE,CAACE,WAAW,cAAAD,eAAA,uBAAdA,eAAA,CAAgBxE,MAAM,MAAK,IAAI;UAAA,EAAC;UACxG,IAAI0E,CAAC,GAAG;YACJpH,EAAE,EAAE4G,OAAO,CAACJ,SAAS;YACrBpG,WAAW,EAAEwG,OAAO,CAACE,UAAU,CAAC1G,WAAW;YAC3CiH,YAAY,EAAET,OAAO,CAACE,UAAU,CAACO,YAAY;YAC7CR,YAAY,EAAEA,YAAY,KAAKS,SAAS,GAAGT,YAAY,CAACA,YAAY,GAAG;UAC3E,CAAC;UACDH,IAAI,CAACa,IAAI,CAACH,CAAC,CAAC;QAChB,CAAC,CAAC;QACF3F,eAAe,CAACiF,IAAI,CAAC;QACrB/E,UAAU,CAACqE,QAAQ,CAAC;MACxB;MACA,MAAMhH,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCwI,IAAI,CAAEvD,GAAG,IAAK;QACX,IAAIwD,UAAU,GAAG,EAAE;QACnB,KAAK,IAAIC,KAAK,IAAIzD,GAAG,CAACC,IAAI,EAAE;UACxB,IAAIkD,CAAC,GAAG;YACJnH,IAAI,EAAEyH,KAAK,CAACC,aAAa;YACzBC,KAAK,EAAEF,KAAK,CAAC1H,EAAE;YACf6H,IAAI,EAAEH,KAAK,CAAChF;UAChB,CAAC;UACD+E,UAAU,CAACF,IAAI,CAACH,CAAC,CAAC;QACtB;QACAnF,YAAY,CAACwF,UAAU,CAAC;MAC5B,CAAC,CAAC,CACDK,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAC,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QACdzE,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjB8D,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAN,WAAA,GAAAD,CAAC,CAACQ,QAAQ,cAAAP,WAAA,uBAAVA,WAAA,CAAY9D,IAAI,MAAKoD,SAAS,IAAAW,YAAA,GAAGF,CAAC,CAACQ,QAAQ,cAAAN,YAAA,uBAAVA,YAAA,CAAY/D,IAAI,GAAG6D,CAAC,CAACS,OAAO,CAAE;UAC5IC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,IAAItB,WAAW,GAAGuB,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,cAAc,CAAC7E,OAAO,CAAC,aAAa,CAAC,CAAC;MAC1E,IAAImD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;QAC3C,IAAI2B,IAAI,GAAG,+BAA+B,GAAG3B,WAAW,CAACU,IAAI,KAAKP,SAAS,GAAGH,WAAW,CAACU,IAAI,GAAGV,WAAW;QAC5G1E,oBAAoB,CAAC0E,WAAW,CAAC;QACjC,MAAMnI,UAAU,CAAC,KAAK,EAAE8J,IAAI,CAAC,CACxBtB,IAAI,CAACvD,GAAG,IAAI;UACTN,OAAO,CAACgD,OAAO,CAACC,OAAO,IAAI;YACvB,IAAII,IAAI,GAAG/C,GAAG,CAACC,IAAI,CAAC6E,MAAM,CAAC9B,EAAE,IAAIA,EAAE,CAAC+B,mBAAmB,CAACxC,SAAS,CAACxG,EAAE,KAAK4G,OAAO,CAACzG,OAAO,CAACH,EAAE,IAAIiH,EAAE,CAAC+B,mBAAmB,CAACC,WAAW,KAAKrC,OAAO,CAACsC,WAAW,CAAC;YAC1J,IAAIlC,IAAI,KAAKM,SAAS,EAAE;cACpB,IAAI6B,KAAK,GAAG,CAAC;cACbnC,IAAI,CAACL,OAAO,CAACyC,IAAI,IAAI;gBACjBD,KAAK,IAAIC,IAAI,CAACC,KAAK;cACvB,CAAC,CAAC;cACFzC,OAAO,CAACG,sBAAsB,GAAGoC,KAAK;YAC1C;UACJ,CAAC,CAAC;UACF;QACJ,CAAC,CAAC,CAACrB,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAuB,YAAA,EAAAC,YAAA;UACZrB,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;UACdzE,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;YACfC,QAAQ,EAAE,OAAO;YACjB8D,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAgB,YAAA,GAAAvB,CAAC,CAACQ,QAAQ,cAAAe,YAAA,uBAAVA,YAAA,CAAYpF,IAAI,MAAKoD,SAAS,IAAAiC,YAAA,GAAGxB,CAAC,CAACQ,QAAQ,cAAAgB,YAAA,uBAAVA,YAAA,CAAYrF,IAAI,GAAG6D,CAAC,CAACS,OAAO,CAAE;YAC9JC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,CAAC;MACV,CAAC,MAAM;QACHxF,YAAY,CAAC,IAAI,CAAC;MACtB;IACJ;IACAY,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM2F,iBAAiB,GAAG,MAAOzB,CAAC,IAAK;IACnCtF,oBAAoB,CAACsF,CAAC,CAACH,KAAK,CAAC;IAC7B,IAAIZ,IAAI,GAAGhF,SAAS,CAACgF,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACW,KAAK,KAAKG,CAAC,CAACH,KAAK,CAAC;IACrDjF,SAAS,CAACqE,IAAI,CAACa,IAAI,CAAC;IACpB,IAAI/D,GAAG,GAAG,+BAA+B,GAAGiE,CAAC,CAACH,KAAK;IACnDgB,MAAM,CAACC,cAAc,CAACY,OAAO,CAAC,aAAa,EAAE1B,CAAC,CAACH,KAAK,CAAC;IACrD,MAAM5I,UAAU,CAAC,KAAK,EAAE8E,GAAG,CAAC,CACvB0D,IAAI,CAACvD,GAAG,IAAI;MACTnD,MAAM,CAAC6F,OAAO,CAACC,OAAO,IAAI;QACtB,IAAII,IAAI,GAAG/C,GAAG,CAACC,IAAI,CAAC6E,MAAM,CAAC9B,EAAE,IAAIA,EAAE,CAAC+B,mBAAmB,CAACxC,SAAS,CAACxG,EAAE,KAAK4G,OAAO,CAACzG,OAAO,CAACH,EAAE,IAAIiH,EAAE,CAAC+B,mBAAmB,CAACC,WAAW,KAAKrC,OAAO,CAACsC,WAAW,CAAC;QAC1J,IAAIlC,IAAI,KAAKM,SAAS,EAAE;UACpB,IAAI6B,KAAK,GAAG,CAAC;UACbnC,IAAI,CAACL,OAAO,CAACyC,IAAI,IAAI;YACjBD,KAAK,IAAIC,IAAI,CAACC,KAAK;UACvB,CAAC,CAAC;UACFzC,OAAO,CAACG,sBAAsB,GAAGoC,KAAK;QAC1C;MACJ,CAAC,CAAC;MACF;IACJ,CAAC,CAAC,CAACrB,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAA2B,YAAA,EAAAC,YAAA;MACZzB,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACdzE,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,OAAO;QACjB8D,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAoB,YAAA,GAAA3B,CAAC,CAACQ,QAAQ,cAAAmB,YAAA,uBAAVA,YAAA,CAAYxF,IAAI,MAAKoD,SAAS,IAAAqC,YAAA,GAAG5B,CAAC,CAACQ,QAAQ,cAAAoB,YAAA,uBAAVA,YAAA,CAAYzF,IAAI,GAAG6D,CAAC,CAACS,OAAO,CAAE;QAC9JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV,CAAC;EACD,MAAMmB,YAAY,GAAG,MAAO7B,CAAC,IAAK;IAC9BxG,iBAAiB,CAACwG,CAAC,CAACH,KAAK,CAAC;EAC9B,CAAC;EACD,MAAMiC,KAAK,GAAG,MAAAA,CAAA,KAAY;IAEtB,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIhG,GAAG,GAAG,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAC5DlD,MAAM,CAAC6F,OAAO,CAACoD,KAAK,IAAI;MACpB,IAAIC,GAAG,GAAG,IAAI7E,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE,KAAK;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC,CAAC5E,MAAM,CAACuE,KAAK,CAACV,KAAK,GAAGU,KAAK,CAACb,WAAW,GAAGmB,UAAU,CAACN,KAAK,CAACO,SAAS,CAAC,CAAC;MAChLR,OAAO,CAACvC,IAAI,CAAC;QACTvH,EAAE,EAAE+J,KAAK,CAAC5J,OAAO,CAACH,EAAE;QACpBO,QAAQ,EAAEwJ,KAAK,CAACV,KAAK,GAAGU,KAAK,CAACb,WAAW;QACzCqB,KAAK,EAAEF,UAAU,CAACL,GAAG,CAACQ,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC1EC,WAAW,EAAEV,KAAK,CAACU,WAAW;QAC9BvB,WAAW,EAAEa,KAAK,CAACb,WAAW;QAC9BoB,SAAS,EAAEP,KAAK,CAACO,SAAS;QAC1BjB,KAAK,EAAEU,KAAK,CAACV,KAAK;QAClBqB,GAAG,EAAEX,KAAK,CAACW;MACf,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAIH,KAAK,GAAG,CAAC;IACbT,OAAO,CAACnD,OAAO,CAACC,OAAO,IAAI;MACvB2D,KAAK,IAAI3D,OAAO,CAAC2D,KAAK;IAC1B,CAAC,CAAC;IACF,IAAIP,GAAG,GAAG,IAAI7E,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,UAAU;MAAEC,QAAQ,EAAE,KAAK;MAAEC,qBAAqB,EAAE;IAAE,CAAC,CAAC,CAAC5E,MAAM,CAAC+E,KAAK,CAAC;IACxH;IACA,IAAII,IAAI,GAAG;MACPxG,YAAY,EAAE/B,KAAK;MACnB4C,mBAAmB,EAAEtE,MAAM;MAC3BV,EAAE,EAAE8B,QAAQ,CAAC9B,EAAE;MACf4D,UAAU,EAAE9B,QAAQ,CAAC8B,UAAU;MAC/BqB,IAAI,EAAErE,MAAM;MACZsE,SAAS,EAAEhD,IAAI;MACf0I,WAAW,EAAE9I,QAAQ,CAAC8I,WAAW;MACjC5J,QAAQ,EAAE8I,OAAO;MACjBe,YAAY,EAAE/I,QAAQ,CAAC+I,YAAY;MACnCC,aAAa,EAAE,EAAE;MACjBP,KAAK,EAAEF,UAAU,CAACL,GAAG,CAACQ,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC1EO,UAAU,EAAE,EAAE;MACdvH,MAAM,EAAElC,cAAc;MACtB0J,iBAAiB,EAAElJ,QAAQ,CAACkJ;IAChC,CAAC;IACD,IAAI1J,cAAc,KAAK,WAAW,IAAIkB,iBAAiB,KAAK,IAAI,EAAE;MAC9D,MAAMxD,UAAU,CAAC,KAAK,EAAE8E,GAAG,EAAE6G,IAAI,CAAC,CAC7BnD,IAAI,CAAC,MAAMvD,GAAG,IAAI;QACfiE,OAAO,CAACC,GAAG,CAAClE,GAAG,CAACC,IAAI,CAAC;QACrBZ,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAE8D,OAAO,EAAE,QAAQ;UAAEC,MAAM,EAAE,iDAAiD;UAAEI,IAAI,EAAE;QAAK,CAAC,CAAC;QACrI,IAAIwC,IAAI,GAAG,mCAAmC,GAAGlH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAAG,eAAe,GAAGxB,iBAAiB;QACzH,MAAMxD,UAAU,CAAC,KAAK,EAAEiM,IAAI,CAAC,CACxBzD,IAAI,CAAEvD,GAAG,IAAK;UACXiE,OAAO,CAACC,GAAG,CAAClE,GAAG,CAACC,IAAI,CAAC;UACrBZ,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;YACfC,QAAQ,EAAE,SAAS;YACnB8D,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAE,kBAAkB;YAC1BI,IAAI,EAAE;UACV,CAAC,CAAC;UACFyC,UAAU,CAAC,MAAM;YACbtC,MAAM,CAACuC,QAAQ,CAACC,QAAQ,GAAG5L,mCAAmC;UAClE,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CACDsI,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAsD,YAAA,EAAAC,YAAA;UACVpD,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;UACdzE,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;YACfC,QAAQ,EAAE,OAAO;YACjB8D,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,qEAAAC,MAAA,CAAkE,EAAA+C,YAAA,GAAAtD,CAAC,CAACQ,QAAQ,cAAA8C,YAAA,uBAAVA,YAAA,CAAYnH,IAAI,MAAKoD,SAAS,IAAAgE,YAAA,GAAGvD,CAAC,CAACQ,QAAQ,cAAA+C,YAAA,uBAAVA,YAAA,CAAYpH,IAAI,GAAG6D,CAAC,CAACS,OAAO,CAAE;YACvIC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,CAAC;MACV,CAAC,CAAC,CAACX,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAwD,YAAA,EAAAC,YAAA;QACZtD,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QACdzE,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAE8D,OAAO,EAAE,iBAAiB;UAAEC,MAAM,2EAAAC,MAAA,CAAwE,EAAAiD,YAAA,GAAAxD,CAAC,CAACQ,QAAQ,cAAAgD,YAAA,uBAAVA,YAAA,CAAYrH,IAAI,MAAKoD,SAAS,IAAAkE,YAAA,GAAGzD,CAAC,CAACQ,QAAQ,cAAAiD,YAAA,uBAAVA,YAAA,CAAYtH,IAAI,GAAG6D,CAAC,CAACS,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACpO,CAAC,CAAC;IACV,CAAC,MAAM,IAAInH,cAAc,KAAK,WAAW,EAAE;MACvC,MAAMtC,UAAU,CAAC,KAAK,EAAE8E,GAAG,EAAE6G,IAAI,CAAC,CAC7BnD,IAAI,CAAC,MAAMvD,GAAG,IAAI;QACfiE,OAAO,CAACC,GAAG,CAAClE,GAAG,CAACC,IAAI,CAAC;QACrBZ,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAE8D,OAAO,EAAE,QAAQ;UAAEC,MAAM,EAAE,iDAAiD;UAAEI,IAAI,EAAE;QAAK,CAAC,CAAC;QACrIyC,UAAU,CAAC,MAAM;UACbtC,MAAM,CAACuC,QAAQ,CAACC,QAAQ,GAAG5L,mCAAmC;QAClE,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAACsI,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAA0D,YAAA,EAAAC,aAAA;QACZxD,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QACdzE,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAE8D,OAAO,EAAE,iBAAiB;UAAEC,MAAM,2EAAAC,MAAA,CAAwE,EAAAmD,YAAA,GAAA1D,CAAC,CAACQ,QAAQ,cAAAkD,YAAA,uBAAVA,YAAA,CAAYvH,IAAI,MAAKoD,SAAS,IAAAoE,aAAA,GAAG3D,CAAC,CAACQ,QAAQ,cAAAmD,aAAA,uBAAVA,aAAA,CAAYxH,IAAI,GAAG6D,CAAC,CAACS,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACpO,CAAC,CAAC;IACV,CAAC,MAAM;MACHnF,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAE8D,OAAO,EAAE,aAAa;QAAEC,MAAM,EAAE,2DAA2D;QAAEI,IAAI,EAAE;MAAK,CAAC,CAAC;IACrJ;EACJ,CAAC;EACD;EACA,MAAMkD,uBAAuB,GAAIC,OAAO,IAAK;IACzC,IAAIA,OAAO,CAACzL,OAAO,CAAC4G,sBAAsB,KAAK,IAAI,EAAE;MACjD,IAAIF,YAAY,GAAG+E,OAAO,CAACzL,OAAO,CAAC4G,sBAAsB,CAACC,IAAI,CAACC,EAAE;QAAA,IAAA4E,gBAAA;QAAA,OAAI,EAAAA,gBAAA,GAAA5E,EAAE,CAACE,WAAW,cAAA0E,gBAAA,uBAAdA,gBAAA,CAAgBnJ,MAAM,OAAMA,MAAM,KAAK,IAAI,GAAGA,MAAM,GAAG,IAAI,CAAC;MAAA,EAAC;MAClI,oBACIhD,OAAA,CAACxB,KAAK,CAACuG,QAAQ;QAAAC,QAAA,gBACXhF,OAAA;UAAMoM,SAAS,EAAC,gBAAgB;UAAApH,QAAA,EAAEhG,QAAQ,CAACqN;QAAQ;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC1D+B,YAAY,KAAKS,SAAS,GAAGT,YAAY,CAACA,YAAY,GAAG,iBAAiB;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAEzB;EACJ,CAAC;EACD;EACA,MAAMkH,wBAAwB,GAAIJ,OAAO,IAAK;IAC1C,IAAIA,OAAO,CAAC7E,sBAAsB,KAAKO,SAAS,EAAE;MAC9C,oBACI5H,OAAA,CAACxB,KAAK,CAACuG,QAAQ;QAAAC,QAAA,gBACXhF,OAAA;UAAMoM,SAAS,EAAC,gBAAgB;UAAApH,QAAA,EAAEhG,QAAQ,CAACuN;QAAO;UAAAtH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzD8G,OAAO,CAAC7E,sBAAsB;MAAA;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEzB;EACJ,CAAC;EACD;EACA,MAAMoH,kBAAkB,GAAIN,OAAO,IAAK;IACpC,IAAIA,OAAO,CAACO,cAAc,KAAK7E,SAAS,EAAE;MACtC,oBACI5H,OAAA,CAACxB,KAAK,CAACuG,QAAQ;QAAAC,QAAA,gBACXhF,OAAA;UAAMoM,SAAS,EAAC,gBAAgB;UAAApH,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACxD8G,OAAO,CAACO,cAAc;MAAA;QAAAxH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAEzB,CAAC,MAAM;MACH,oBACIpF,OAAA,CAACxB,KAAK,CAACuG,QAAQ;QAAAC,QAAA,gBACXhF,OAAA;UAAMoM,SAAS,EAAC,gBAAgB;UAAApH,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACxD8G,OAAO,CAAC1C,WAAW;MAAA;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEzB;EACJ,CAAC;EACD;EACA,MAAMsH,mBAAmB,GAAIR,OAAO,IAAK;IACrC,oBACIlM,OAAA,CAACxB,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXhF,OAAA;QAAMoM,SAAS,EAAC,gBAAgB;QAAApH,QAAA,EAAEhG,QAAQ,CAAC2N;MAAO;QAAA1H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzD8G,OAAO,CAACnB,WAAW;IAAA;MAAA9F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEzB,CAAC;EACD;EACA,MAAMwH,wBAAwB,GAAIV,OAAO,IAAK;IAC1C,oBACIlM,OAAA,CAACxB,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXhF,OAAA;QAAMoM,SAAS,EAAC,gBAAgB;QAAApH,QAAA,EAAEhG,QAAQ,CAAC6N;MAAI;QAAA5H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtD8G,OAAO,CAACzL,OAAO,CAACC,WAAW;IAAA;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEzB,CAAC;EACD;EACA,MAAM0H,yBAAyB,GAAIZ,OAAO,IAAK;IAC3C,oBACIlM,OAAA,CAACxB,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXhF,OAAA;QAAMoM,SAAS,EAAC,gBAAgB;QAAApH,QAAA,EAAEhG,QAAQ,CAAC6N;MAAI;QAAA5H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtD8G,OAAO,CAACzL,OAAO,CAACkH,YAAY;IAAA;MAAA1C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEzB,CAAC;EACD;EACA,MAAM2H,iBAAiB,GAAIb,OAAO,IAAK;IACnC,oBACIlM,OAAA,CAACxB,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXhF,OAAA;QAAMoM,SAAS,EAAC,gBAAgB;QAAApH,QAAA,EAAEhG,QAAQ,CAACgO;MAAG;QAAA/H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE,KAAK;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC,CAAC5E,MAAM,CAACoG,OAAO,CAACrB,KAAK,CAAC;IAAA;MAAA5F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3G,CAAC;EAEzB,CAAC;EACD;EACA,MAAM6H,sBAAsB,GAAIf,OAAO,IAAK;IACxC,oBACIlM,OAAA,CAACxB,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXhF,OAAA;QAAMoM,SAAS,EAAC,gBAAgB;QAAApH,QAAA,EAAEhG,QAAQ,CAACkO;MAAM;QAAAjI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE,KAAK;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC,CAAC5E,MAAM,CAACoG,OAAO,CAACb,UAAU,CAAC;IAAA;MAAApG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChH,CAAC;EAEzB,CAAC;EACD;EACA,MAAM+H,mBAAmB,GAAGA,CAACC,UAAU,EAAElN,KAAK,EAAEgI,KAAK,EAAEpE,MAAM,KAAK;IAC9D,IAAIuJ,OAAO,GAAG,EAAE;IAChB,IAAIvJ,MAAM,KAAK8D,SAAS,EAAE;MACtB9D,MAAM,CAACmD,OAAO,CAACC,OAAO,IAAI;QACtB,IAAIA,OAAO,CAACqC,WAAW,KAAKrB,KAAK,EAAE;UAC/BmF,OAAO,GAAGnG,OAAO,CAAC6D,WAAW;QACjC;MACJ,CAAC,CAAC;IACN;IACA,IAAIuC,eAAe,GAAG,CAAC,GAAGpN,KAAK,CAACgI,KAAK,CAAC;IACtCoF,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAACrN,KAAK,CAACsN,KAAK,CAAC,GAAGtF,KAAK;IACpDoF,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,aAAa,CAAC,GAAGF,OAAO;IACxDC,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAGD,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAGD,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,aAAa,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IAC/L,IAAID,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK3F,SAAS,EAAE;MACtD0F,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAGE,QAAQ,CAACH,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;IACxN,CAAC,MAAM;MACHD,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAGE,QAAQ,CAACH,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;IACxN;IACAvJ,gBAAgB,IAAA4E,MAAA,CAAIwE,UAAU,EAAG,CAACE,eAAe,CAAC;IAClDI,OAAO,CAAC,CAAC;IACTC,OAAO,CAAC,CAAC;IACTC,UAAU,CAAC,CAAC;EAChB,CAAC;EACD;EACA,MAAMC,oBAAoB,GAAGA,CAACT,UAAU,EAAElN,KAAK,EAAEgI,KAAK,KAAK;IACvD,IAAIoF,eAAe,GAAG,CAAC,GAAGpN,KAAK,CAACgI,KAAK,CAAC;IACtCoF,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAACrN,KAAK,CAACsN,KAAK,CAAC,GAAGtF,KAAK;IACpDoF,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAGrF,KAAK,GAAGoF,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,aAAa,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IAC5J,IAAID,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK3F,SAAS,EAAE;MACtD0F,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAGE,QAAQ,CAACH,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;IACxN,CAAC,MAAM;MACHD,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG5C,UAAU,CAAC2C,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAGE,QAAQ,CAACH,eAAe,CAACpN,KAAK,CAACqN,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;IACxN;IACAvJ,gBAAgB,IAAA4E,MAAA,CAAIwE,UAAU,EAAG,CAACE,eAAe,CAAC;IAClDI,OAAO,CAAC,CAAC;IACTC,OAAO,CAAC,CAAC;IACTC,UAAU,CAAC,CAAC;EAChB,CAAC;EACD;EACA,MAAME,eAAe,GAAGA,CAACV,UAAU,EAAElN,KAAK,KAAK;IAC3C,oBAAOF,OAAA,CAACT,WAAW;MAAC2I,KAAK,EAAEhI,KAAK,CAACgM,OAAO,CAAC,OAAO,CAAE;MAAC6B,aAAa,EAAG1F,CAAC,IAAKwF,oBAAoB,CAACT,UAAU,EAAElN,KAAK,EAAEmI,CAAC,CAACH,KAAK;IAAE;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjI,CAAC;EACD;EACA,MAAM4I,mBAAmB,GAAI9B,OAAO,IAAK;IACrC,IAAI+B,QAAQ,GAAG,CAAC;IAChB,IAAI/B,OAAO,CAACrL,QAAQ,KAAK+G,SAAS,EAAE;MAChC,IAAIsE,OAAO,CAACvC,KAAK,KAAK/B,SAAS,IAAIsE,OAAO,CAAC1C,WAAW,KAAK5B,SAAS,EAAE;QAClEqG,QAAQ,GAAG/B,OAAO,CAACvC,KAAK,GAAGuC,OAAO,CAAC1C,WAAW;MAClD,CAAC,MAAM;QACHyE,QAAQ,GAAG/B,OAAO,CAACrL,QAAQ;MAC/B;MACA,oBAAOb,OAAA;QAAMoM,SAAS,EAAC,yBAAyB;QAAApH,QAAA,EAAEiJ;MAAQ;QAAAhJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IACtE,CAAC,MAAM;MACH,OAAO,EAAE;IACb;EACJ,CAAC;EACD;EACA,MAAM8I,oBAAoB,GAAIhC,OAAO,IAAK;IACtC,IAAIA,OAAO,CAACvC,KAAK,KAAK/B,SAAS,EAAE;MAC7B,oBAAO5H,OAAA;QAAMoM,SAAS,EAAC,YAAY;QAAApH,QAAA,EAAEkH,OAAO,CAACvC;MAAK;QAAA1E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAC9D,CAAC,MAAM;MACH,OAAO,EAAE;IACb;EACJ,CAAC;EACD;EACA,MAAM+I,YAAY,GAAGA,CAACf,UAAU,EAAElN,KAAK,KAAK;IACxC,IAAI4D,MAAM,GAAG,EAAE;IACf,IAAI5D,KAAK,CAACgM,OAAO,CAAC9E,UAAU,KAAKQ,SAAS,EAAE;MACxC,KAAK,MAAMwG,MAAM,IAAIlO,KAAK,CAACgM,OAAO,CAAC9E,UAAU,CAACiH,kBAAkB,EAAE;QAC9DvK,MAAM,CAAC+D,IAAI,CAAC;UACRkD,WAAW,EAAEqD,MAAM,CAACrD,WAAW;UAC/BxB,WAAW,EAAE6E,MAAM,CAAC7E;QACxB,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,KAAK,MAAM+E,GAAG,IAAIpO,KAAK,CAACgM,OAAO,CAACzL,OAAO,CAAC4N,kBAAkB,EAAE;QACxDvK,MAAM,CAAC+D,IAAI,CAAC;UACRkD,WAAW,EAAEuD,GAAG,CAACvD,WAAW;UAC5BxB,WAAW,EAAE+E,GAAG,CAAC/E;QACrB,CAAC,CAAC;MACN;IACJ;IACA,oBACIvJ,OAAA,CAACR,QAAQ;MAAC0I,KAAK,EAAEhI,KAAK,CAACgM,OAAO,CAAC,aAAa,CAAE;MAACqC,OAAO,EAAEzK,MAAO;MAAC0K,WAAW,EAAC,aAAa;MAACC,WAAW,EAAC,aAAa;MAC/GC,QAAQ,EAAGrG,CAAC,IAAK8E,mBAAmB,CAACC,UAAU,EAAElN,KAAK,EAAEmI,CAAC,CAACH,KAAK,EAAEpE,MAAM,CAAE;MAAC0G,KAAK,EAAE;QAAEmE,KAAK,EAAE;MAAO,CAAE;MAACC,WAAW,EAAC,cAAc;MAC9HC,YAAY,EAAGC,MAAM,IAAK;QACtB,oBAAO9O,OAAA;UAAMoM,SAAS,0BAAAxD,MAAA,CAA0BkG,MAAM,CAAC/D,WAAW,CAACgE,WAAW,CAAC,CAAC,CAAG;UAAA/J,QAAA,EAAE8J,MAAM,CAAC/D;QAAW;UAAA9F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACnH;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEjB,CAAC;EACD;EACA,MAAM4J,mBAAmB,GAAIxL,MAAM,IAAK;IACpCC,SAAS,CAACD,MAAM,CAAC;IACjBL,qBAAqB,CAAC,IAAI,CAAC;EAC/B,CAAC;EACD;EACA,MAAM8L,sBAAsB,GAAGA,CAAA,KAAM;IACjC9L,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EACD;EACA,MAAM+L,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIC,SAAS,GAAG/N,MAAM,CAACiI,MAAM,CAAC+F,GAAG,IAAIA,GAAG,CAAC9O,EAAE,KAAKkD,MAAM,CAAClD,EAAE,CAAC;IAC1De,SAAS,CAAC8N,SAAS,CAAC;IACpBA,SAAS,GAAGjN,QAAQ,CAACmH,MAAM,CAAC+F,GAAG,IAAIA,GAAG,CAAC3O,OAAO,CAACH,EAAE,KAAKkD,MAAM,CAAClD,EAAE,CAAC;IAChE6B,WAAW,CAACgN,SAAS,CAAC;IACtBhM,qBAAqB,CAAC,KAAK,CAAC;IAC5BM,SAAS,CAACpD,WAAW,CAAC;IACtB,IAAImD,MAAM,CAAC4D,UAAU,KAAKQ,SAAS,EAAE;MACjCpE,MAAM,CAAC4D,UAAU,GAAG5D,MAAM,CAAC/C,OAAO;IACtC;IACAuB,OAAO,CAAC6F,IAAI,CAACrE,MAAM,CAAC;IACpBI,KAAK,CAACc,OAAO,CAACC,IAAI,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAE8D,OAAO,EAAE,UAAU;MAAEC,MAAM,EAAE,kCAAkC;MAAEI,IAAI,EAAE;IAAK,CAAC,CAAC;EAC5H,CAAC;EACD;EACA,MAAMsG,wBAAwB,gBAC1BrP,OAAA,CAACxB,KAAK,CAACuG,QAAQ;IAAAC,QAAA,gBACXhF,OAAA,CAACjB,MAAM;MAACuQ,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,aAAa;MAACnD,SAAS,EAAC,eAAe;MAACoD,OAAO,EAAEP;IAAuB;MAAAhK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnGpF,OAAA,CAACjB,MAAM;MAACuQ,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,aAAa;MAACnD,SAAS,EAAC,eAAe;MAACoD,OAAO,EAAEN;IAAa;MAAAjK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7E,CACnB;EACD;EACA,MAAMqK,kBAAkB,GAAIvD,OAAO,IAAK;IACpC,oBACIlM,OAAA,CAACxB,KAAK,CAACuG,QAAQ;MAAAC,QAAA,gBACXhF,OAAA,CAACP,OAAO;QAACiQ,MAAM,EAAC;MAAmB;QAAAzK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCpF,OAAA,CAACjB,MAAM;QAACwQ,IAAI,EAAC,aAAa;QAACnD,SAAS,EAAC,kBAAkB;QAAC,mBAAgB,SAAS;QAAC,oBAAiB,KAAK;QAACoD,OAAO,EAAEA,CAAA,KAAMR,mBAAmB,CAAC9C,OAAO;MAAE;QAAAjH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5I,CAAC;EAEzB,CAAC;EACD;EACA,MAAMuK,OAAO,GAAItH,CAAC,IAAK;IACnB,IAAI+B,OAAO,GAAG,EAAE;IAChB,IAAI/B,CAAC,CAACH,KAAK,KAAK,IAAI,EAAE;MAClBlG,OAAO,CAACiF,OAAO,CAACC,OAAO,IAAI;QACvB,IAAIA,OAAO,CAACJ,SAAS,KAAKuB,CAAC,CAACH,KAAK,CAAC5H,EAAE,EAAE;UAClC+H,CAAC,CAACH,KAAK,GAAGhB,OAAO;QACrB;MACJ,CAAC,CAAC;MACF,IAAImB,CAAC,CAACH,KAAK,CAACyB,KAAK,KAAK/B,SAAS,EAAE;QAC7BS,CAAC,CAACH,KAAK,CAACyB,KAAK,GAAG,CAAC;QACjBtB,CAAC,CAACH,KAAK,CAACsB,WAAW,GAAGnB,CAAC,CAACH,KAAK,CAACd,UAAU,CAACiH,kBAAkB,CAAC,CAAC,CAAC,CAAC9E,WAAW;QAC1ElB,CAAC,CAACH,KAAK,CAAC6C,WAAW,GAAG1C,CAAC,CAACH,KAAK,CAACd,UAAU,CAACiH,kBAAkB,CAAC,CAAC,CAAC,CAACtD,WAAW;QAC1E1C,CAAC,CAACH,KAAK,CAACrH,QAAQ,GAAGwH,CAAC,CAACH,KAAK,CAACsB,WAAW,GAAGnB,CAAC,CAACH,KAAK,CAACyB,KAAK;QACtDtB,CAAC,CAACH,KAAK,CAAC2C,KAAK,GAAG,IAAIpF,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE,KAAK;UAAEC,qBAAqB,EAAE;QAAE,CAAC,CAAC,CAAC5E,MAAM,CAACuC,CAAC,CAACH,KAAK,CAACyB,KAAK,GAAGtB,CAAC,CAACH,KAAK,CAACsB,WAAW,GAAGmB,UAAU,CAACtC,CAAC,CAACH,KAAK,CAACtH,KAAK,CAAC,CAAC;QACxLwJ,OAAO,GAAG,CAAC;UACP9J,EAAE,EAAE+H,CAAC,CAACH,KAAK,CAACd,UAAU,CAAC9G,EAAE;UACzBG,OAAO,EAAE4H,CAAC,CAACH,KAAK,CAACd,UAAU;UAC3BvG,QAAQ,EAAEwH,CAAC,CAACH,KAAK,CAACrH,QAAQ;UAC1BgK,KAAK,EAAExC,CAAC,CAACH,KAAK,CAAC2C,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;UAC1EO,UAAU,EAAE,IAAI5F,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAAC5E,MAAM,CAAC6E,UAAU,CAACtC,CAAC,CAACH,KAAK,CAAC2C,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAGH,UAAU,CAACtC,CAAC,CAACH,KAAK,CAAC2C,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG2C,QAAQ,CAACpF,CAAC,CAACH,KAAK,CAACd,UAAU,CAACwI,GAAG,CAAC,GAAG,GAAG,CAAC;UAC/T7E,WAAW,EAAE1C,CAAC,CAACH,KAAK,CAAC6C,WAAW;UAChCvB,WAAW,EAAEnB,CAAC,CAACH,KAAK,CAACsB,WAAW;UAChCoB,SAAS,EAAEvC,CAAC,CAACH,KAAK,CAACtH,KAAK;UACxB+I,KAAK,EAAEtB,CAAC,CAACH,KAAK,CAACyB,KAAK;UACpBqB,GAAG,EAAE3C,CAAC,CAACH,KAAK,CAACd,UAAU,CAACwI;QAC5B,CAAC,CAAC;QACFxO,MAAM,CAACyG,IAAI,CAACuC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvB,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5E,YAAY,CAAC6E,MAAM,EAAED,CAAC,EAAE,EAAE;UAC1C,IAAI5E,YAAY,CAAC4E,CAAC,CAAC,CAACpG,EAAE,KAAK8J,OAAO,CAAC,CAAC,CAAC,CAAC9J,EAAE,EAAE;YACtCwB,YAAY,CAACiF,MAAM,CAACL,CAAC,EAAE,CAAC,CAAC;YACzB;UACJ;QACJ;MACJ,CAAC,MAAM;QACHtF,MAAM,CAACyG,IAAI,CAACQ,CAAC,CAACH,KAAK,CAAC;QACpB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9E,YAAY,CAAC6E,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC1C,IAAI9E,YAAY,CAAC8E,CAAC,CAAC,CAACtG,EAAE,KAAK+H,CAAC,CAACH,KAAK,CAACrB,SAAS,EAAE;YAC1C/E,YAAY,CAACiF,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;YACzB;UACJ;QACJ;MACJ;MACA,IAAIiJ,IAAI,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,aAAa,CAAC;MACnD;MACA;MACAF,IAAI,CAACA,IAAI,CAAClJ,MAAM,GAAG,CAAC,CAAC,CAACqJ,cAAc,CAAC;QACjCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;EACD;EACA,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7BrM,YAAY,CAACqM,KAAK,CAACC,KAAK,CAAC,GAAAC,aAAA,KAAQhP,QAAQ,CAAC8O,KAAK,CAACC,KAAK,CAAC,CAAE;EAC5D,CAAC;EACD;EACA,MAAME,eAAe,GAAIH,KAAK,IAAK;IAC/B,IAAI3P,OAAO,GAAG,CAAC,GAAGa,QAAQ,CAAC;IAC3Bb,OAAO,CAAC2P,KAAK,CAACC,KAAK,CAAC,GAAGtM,YAAY,CAACqM,KAAK,CAACC,KAAK,CAAC;IAChD,OAAOtM,YAAY,CAACqM,KAAK,CAACC,KAAK,CAAC;IAChC9O,WAAW,CAACD,QAAQ,CAAC;EACzB,CAAC;EACD;EACA,MAAMkP,OAAO,GAAGA,CAAA,KAAM;IAClBnN,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD;EACA,MAAMoN,UAAU,GAAGA,CAAA,KAAM;IACrBpN,gBAAgB,CAAC,KAAK,CAAC;EAC3B,CAAC;EACD;EACA,MAAMqN,UAAU,GAAGA,CAAA,KAAM;IACrB,oBACI1Q,OAAA;MAAAgF,QAAA,gBACIhF,OAAA,CAACP,OAAO;QAACiQ,MAAM,EAAC;MAAiB;QAAAzK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCpF,OAAA;QAAIoM,SAAS,EAAC,2BAA2B;QAAApH,QAAA,GAAEhG,QAAQ,CAAC2R,YAAY,EAAC,GAAC,eAAA3Q,OAAA;UAAGoM,SAAS,EAAC,mBAAmB;UAAC,mBAAgB,yFAAyF;UAAC,oBAAiB,KAAK;UAAC,cAAW,KAAK;UAAC,cAAW,UAAU;UAAC5B,KAAK,EAAE;YAAEoG,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE;UAAU;QAAE;UAAA5L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrU,CAAC;EAEd,CAAC;EACD;EACA,MAAMuI,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIrD,GAAG,GAAG,CAAC;IACX,IAAIlJ,MAAM,KAAK,IAAI,EAAE;MACjBA,MAAM,CAAC6F,OAAO,CAACC,OAAO,IAAI;QACtBoD,GAAG,IAAIK,UAAU,CAACzD,OAAO,CAAC2D,KAAK,CAAC;MACpC,CAAC,CAAC;MACF,OAAO,IAAIpF,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE,KAAK;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC,CAAC5E,MAAM,CAACwE,GAAG,CAAC;IACvH;EACJ,CAAC;EACD;EACA,MAAMoD,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIkC,GAAG,GAAG,CAAC;IACX,IAAIxO,MAAM,KAAK,IAAI,EAAE;MACjBA,MAAM,CAAC6F,OAAO,CAACC,OAAO,IAAI;QACtB0I,GAAG,IAAIjF,UAAU,CAACzD,OAAO,CAACmE,UAAU,CAACyF,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGrG,UAAU,CAACzD,OAAO,CAAC2D,KAAK,CAACiG,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;MACrI,CAAC,CAAC;MACF,OAAO,IAAIvL,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE,KAAK;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC,CAAC5E,MAAM,CAAC8J,GAAG,CAAC;IACvH;EACJ,CAAC;EACD;EACA,MAAMhC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIqD,MAAM,GAAG,CAAC;IACd,IAAI7P,MAAM,KAAK,IAAI,EAAE;MACjBA,MAAM,CAAC6F,OAAO,CAACC,OAAO,IAAI;QACtB+J,MAAM,IAAItG,UAAU,CAACzD,OAAO,CAACmE,UAAU,CAACyF,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC5E,CAAC,CAAC;MACF,OAAO,IAAIvL,IAAI,CAAC8E,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE,KAAK;QAAEC,qBAAqB,EAAE;MAAE,CAAC,CAAC,CAAC5E,MAAM,CAACmL,MAAM,CAAC;IAC1H;EACJ,CAAC;EACD,MAAMC,cAAc,GAAI7I,CAAC,IAAK;IAC1B,IAAI7G,GAAG,GAAG,YAAY,GAAG6G,CAAC,CAACqH,MAAM,CAACxH,KAAK,CAACvB,MAAM,GAAG,MAAM,GAAG0B,CAAC,CAAC8I,aAAa,CAACC,SAAS,GAAG,YAAY;IAClGzP,mBAAmB,CAACH,GAAG,CAAC;EAC5B,CAAC;EACD;EACA,MAAM6P,mBAAmB,gBACrBrR,OAAA,CAACxB,KAAK,CAACuG,QAAQ;IAAAC,QAAA,eACXhF,OAAA,CAACjB,MAAM;MAACuQ,KAAK,EAAEtQ,QAAQ,CAACsS,MAAO;MAAClF,SAAS,EAAC,eAAe;MAACoD,OAAO,EAAEiB;IAAW;MAAAxL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CACnB;EACD,MAAMmM,MAAM,GAAG,CACX;IAAE/D,KAAK,EAAE,aAAa;IAAEgE,MAAM,EAAExS,QAAQ,CAACyS,QAAQ;IAAExG,IAAI,EAAE,aAAa;IAAEyG,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC1G;IAAEnE,KAAK,EAAE,cAAc;IAAEgE,MAAM,EAAExS,QAAQ,CAAC4S,MAAM;IAAE3G,IAAI,EAAE,cAAc;IAAEyG,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC1G;IAAEnE,KAAK,EAAE,cAAc;IAAEgE,MAAM,EAAExS,QAAQ,CAAC6S,OAAO;IAAE5G,IAAI,EAAE,cAAc;IAAEyG,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,CAC9G;EACD,oBACI3R,OAAA;IAAKoM,SAAS,EAAC,eAAe;IAAApH,QAAA,GACzB1B,SAAS,iBACNtD,OAAA,CAACH,UAAU;MAACiS,KAAK,EAAC,oBAAoB;MAAChN,OAAO,EAAC,yBAAyB;MAAC4K,MAAM,EAAC;IAAS;MAAAzK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhGpF,OAAA,CAACZ,KAAK;MAAC2S,GAAG,EAAEnO;IAAM;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBpF,OAAA,CAACpB,GAAG;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACPpF,OAAA;MAAKoM,SAAS,EAAC,mBAAmB;MAAApH,QAAA,eAC9BhF,OAAA;QAAIoM,SAAS,EAAC,KAAK;QAAApH,QAAA,GACdhG,QAAQ,CAACgT,QAAQ,eAClBhS,OAAA;UAAMoM,SAAS,EAAC,8BAA8B;UAAApH,QAAA,EAAExD;QAAG;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNpF,OAAA;MAAKoM,SAAS,EAAC,KAAK;MAAApH,QAAA,eAChBhF,OAAA;QAAKoM,SAAS,EAAE1I,YAAa;QAAAsB,QAAA,eACzBhF,OAAA,CAACN,QAAQ;UAACqS,GAAG,EAAElO;QAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNpF,OAAA;MAAKoM,SAAS,EAAC,qCAAqC;MAAApH,QAAA,gBAChDhF,OAAA;QAAKoM,SAAS,EAAC,aAAa;QAAApH,QAAA,eACxBhF,OAAA;UAAKoM,SAAS,EAAC,aAAa;UAAApH,QAAA,gBAExBhF,OAAA;YAAKoM,SAAS,EAAC,mCAAmC;YAAApH,QAAA,gBAC9ChF,OAAA;cAAOiS,OAAO,EAAC,aAAa;cAAAjN,QAAA,EAAEhG,QAAQ,CAACkT;YAAO;cAAAjN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvDpF,OAAA,CAAClB,SAAS;cAACwB,EAAE,EAAC,UAAU;cAAC8L,SAAS,EAAC,KAAK;cAAClE,KAAK,EAAElH,MAAO;cAAC0N,QAAQ,EAAGrG,CAAC,IAAKpH,SAAS,CAACoH,CAAC,CAACqH,MAAM,CAACxH,KAAK;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eACNpF,OAAA;YAAKoM,SAAS,EAAC,2CAA2C;YAAApH,QAAA,gBACtDhF,OAAA;cAAOiS,OAAO,EAAC,SAAS;cAAAjN,QAAA,EAAEhG,QAAQ,CAACmT;YAAK;cAAAlN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjDpF,OAAA,CAACX,QAAQ;cAACiB,EAAE,EAAC,YAAY;cAAC8L,SAAS,EAAC,OAAO;cAAClE,KAAK,EAAExF,KAAM;cAAC0P,UAAU,EAAC,UAAU;cAAC1D,QAAQ,EAAGrG,CAAC,IAAK;gBAAE1F,QAAQ,CAAC0F,CAAC,CAACH,KAAK,CAAC;gBAAEvE,eAAe,CAAC,QAAQ,CAAC;cAAC,CAAE;cAACiL,WAAW,EAAE,IAAInJ,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;gBAAEC,GAAG,EAAE,SAAS;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,IAAI,EAAE;cAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACrD,KAAK,CAAC,CAAE;cAAC2P,QAAQ;YAAA;cAAApN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3R,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNpF,OAAA;QAAKoM,SAAS,EAAC,QAAQ;QAAApH,QAAA,gBAEnBhF,OAAA;UAAKoM,SAAS,EAAC,qBAAqB;UAAC5B,KAAK,EAAE;YAAE8H,eAAe,EAAE;UAAU,CAAE;UAAAtN,QAAA,gBACvEhF,OAAA;YAAKoM,SAAS,EAAC,yBAAyB;YAAApH,QAAA,eACpChF,OAAA;cAAIoM,SAAS,EAAC,kBAAkB;cAAApH,QAAA,EAAEhG,QAAQ,CAACuT;YAAS;cAAAtN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNpF,OAAA;YAAKoM,SAAS,EAAC,yBAAyB;YAAApH,QAAA,eACpChF,OAAA;cAAKoM,SAAS,EAAC,yCAAyC;cAAApH,QAAA,eACpDhF,OAAA;gBAAKoM,SAAS,EAAC,4BAA4B;gBAAApH,QAAA,eACvChF,OAAA,CAACR,QAAQ;kBAAC4M,SAAS,EAAC,aAAa;kBAAClE,KAAK,EAAEpF,iBAAkB;kBAACyL,OAAO,EAAEjM,SAAU;kBAACoM,QAAQ,EAAE5E,iBAAkB;kBAAC0E,WAAW,EAAC,MAAM;kBAACI,WAAW,EAAC;gBAAqB;kBAAA3J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNpF,OAAA;YAAKoM,SAAS,EAAC,iBAAiB;YAAApH,QAAA,eAC5BhF,OAAA;cAAKoM,SAAS,EAAC,uCAAuC;cAAApH,QAAA,eAClDhF,OAAA,CAACjB,MAAM;gBAACqN,SAAS,EAAC,sCAAsC;gBAACoD,OAAO,EAAEA,CAAA,KAAMgB,OAAO,CAAC,CAAE;gBAAAxL,QAAA,GAAE,GAAC,eAAAhF,OAAA;kBAAGoM,SAAS,EAAC;gBAAwB;kBAAAnH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAACpG,QAAQ,CAACwT,UAAU,EAAC,GAAC;cAAA;gBAAAvN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9J;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpF,OAAA;UAAKoM,SAAS,EAAC,wCAAwC;UAAApH,QAAA,eACnDhF,OAAA,CAACf,SAAS;YAACqB,EAAE,EAAC,SAAS;YAAC8L,SAAS,EAAC,mDAAmD;YAAClE,KAAK,EAAE9G,MAAO;YAACqR,QAAQ,EAAC,KAAK;YAACC,UAAU,EAAC,MAAM;YAACvC,aAAa,EAAEA,aAAc;YAACI,eAAe,EAAEA,eAAgB;YAACoC,YAAY,EAAC,GAAG;YAAA3N,QAAA,gBAClNhF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,cAAc;cAACgE,MAAM,EAAExS,QAAQ,CAAC4S,MAAO;cAAC3G,IAAI,EAAE6B,yBAA0B;cAAC4E,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1GpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,aAAa;cAACgE,MAAM,EAAExS,QAAQ,CAACyS,QAAS;cAACxG,IAAI,EAAE2B,wBAAyB;cAAC8E,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1GpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,aAAa;cAACgE,MAAM,EAAExS,QAAQ,CAAC4T,OAAQ;cAAC3H,IAAI,EAAEyB,mBAAoB,CAAC;cAA4CmG,MAAM,EAAG3S,KAAK,IAAKiO,YAAY,CAAC,UAAU,EAAEjO,KAAK,CAAE;cAACwR,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpMpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,aAAa;cAACgE,MAAM,EAAC,mBAAmB;cAACvG,IAAI,EAAEuB,kBAAmB;cAACkF,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpGpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,OAAO;cAACgE,MAAM,EAAExS,QAAQ,CAAC8T,KAAM;cAAC7H,IAAI,EAAEiD,oBAAqB;cAAC2E,MAAM,EAAG3S,KAAK,IAAK4N,eAAe,CAAC,UAAU,EAAE5N,KAAK,CAAE;cAACwR,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpJpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,UAAU;cAACgE,MAAM,EAAExS,QAAQ,CAAC+T,QAAS;cAAC9H,IAAI,EAAE+C,mBAAoB;cAAC0D,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClGpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,UAAU;cAACgE,MAAM,EAAExS,QAAQ,CAACuN,OAAQ;cAACtB,IAAI,EAAEqB,wBAAyB;cAACoF,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtGpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,UAAU;cAACgE,MAAM,EAAExS,QAAQ,CAACgU,WAAY;cAAC/H,IAAI,EAAEgB,uBAAwB;cAACyF,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACzGpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,OAAO;cAACgE,MAAM,EAAExS,QAAQ,CAACgO,GAAI;cAAC/B,IAAI,EAAE8B,iBAAkB;cAAC2E,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxFpF,OAAA,CAACd,MAAM;cAACsO,KAAK,EAAC,YAAY;cAACgE,MAAM,EAAExS,QAAQ,CAACkO,MAAO;cAACjC,IAAI,EAAEgC,sBAAuB;cAACyE,QAAQ;YAAA;cAAAzM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrGpF,OAAA,CAACd,MAAM;cAACkN,SAAS,EAAC,iBAAiB;cAAC6G,SAAS;cAACC,WAAW,EAAE;gBAAEvE,KAAK,EAAE;cAAO,CAAE;cAACwE,SAAS,EAAE;gBAAEC,SAAS,EAAE;cAAS;YAAE;cAAAnO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC5HpF,OAAA,CAACd,MAAM;cAAC+L,IAAI,EAAEwE;YAAmB;cAAAxK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAENpF,OAAA;UAAKoM,SAAS,EAAC,6DAA6D;UAAApH,QAAA,gBACxEhF,OAAA;YAAMoM,SAAS,EAAC,MAAM;YAAApH,QAAA,gBAAChF,OAAA;cAAAgF,QAAA,GAAIhG,QAAQ,CAACgO,GAAG,EAAC,GAAC;YAAA;cAAA/H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAAC,EAACuI,OAAO,CAAC,CAAC;UAAA;YAAA1I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChEpF,OAAA;YAAMoM,SAAS,EAAC,MAAM;YAAApH,QAAA,gBAAChF,OAAA;cAAAgF,QAAA,GAAIhG,QAAQ,CAACqU,GAAG,EAAC,GAAC;YAAA;cAAApO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAAC,EAACsI,OAAO,CAAC,CAAC;UAAA;YAAAzI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChEpF,OAAA;YAAMoM,SAAS,EAAC,MAAM;YAAApH,QAAA,gBAAChF,OAAA;cAAAgF,QAAA,GAAIhG,QAAQ,CAACkO,MAAM,EAAC,GAAC;YAAA;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAAC,EAACwI,UAAU,CAAC,CAAC;UAAA;YAAA3I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eAENpF,OAAA;UAAKoM,SAAS,EAAC,uCAAuC;UAAApH,QAAA,eAClDhF,OAAA,CAACjB,MAAM;YAACqN,SAAS,EAAC,sCAAsC;YAACoD,OAAO,EAAEA,CAAA,KAAMgB,OAAO,CAAC,CAAE;YAAAxL,QAAA,GAAE,GAAC,eAAAhF,OAAA;cAAGoM,SAAS,EAAC;YAAwB;cAAAnH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAACpG,QAAQ,CAACwT,UAAU,EAAC,GAAC;UAAA;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC,eACNpF,OAAA;QAAKoM,SAAS,EAAC,wBAAwB;QAAApH,QAAA,eAEnChF,OAAA;UAAMoM,SAAS,EAAC,eAAe;UAAApH,QAAA,gBAC3BhF,OAAA,CAACJ,aAAa;YAACwR,SAAS,EAAE,GAAI;YAACkC,OAAO,EAAGjL,CAAC,IAAK6I,cAAc,CAAC7I,CAAC,CAAE;YAACmC,KAAK,EAAE;cAAEmE,KAAK,EAAE;YAAO,CAAE;YAACrO,EAAE,EAAC,UAAU;YAAC4H,KAAK,EAAEhH,MAAO;YAACwN,QAAQ,EAAGrG,CAAC,IAAKlH,SAAS,CAACkH,CAAC,CAACqH,MAAM,CAACxH,KAAK,CAAE;YAAC2H,IAAI,EAAE;UAAE;YAAA5K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChLpF,OAAA;YAAOiS,OAAO,EAAC,UAAU;YAAAjN,QAAA,EAAEhG,QAAQ,CAACuU;UAAI;YAAAtO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjDpF,OAAA;YAAKoM,SAAS,EAAC,4BAA4B;YAAApH,QAAA,eAAChF,OAAA;cAAAgF,QAAA,EAAOtD;YAAgB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENpF,OAAA;MAAIoM,SAAS,EAAC,2BAA2B;MAAApH,QAAA,GACpChG,QAAQ,CAACwU,OAAO,eACjBxT,OAAA,CAACP,OAAO;QAACiQ,MAAM,EAAC;MAAiB;QAAAzK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCpF,OAAA;QAAGoM,SAAS,EAAC,wBAAwB;QAAC,mBAAgB,wFAAwF;QAAC,oBAAiB,KAAK;QAAC,cAAW,KAAK;QAAC,cAAW,UAAU;QAAC5B,KAAK,EAAE;UAAEoG,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAU;MAAE;QAAA5L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnQ,CAAC,eACLpF,OAAA;MAAKoM,SAAS,EAAC,oCAAoC;MAAApH,QAAA,eAC/ChF,OAAA,CAACL,YAAY;QAACyM,SAAS,EAAC,aAAa;QAAClE,KAAK,EAAEtG,cAAe;QAAC2M,OAAO,EAAEzK,MAAO;QAAC4K,QAAQ,EAAGrG,CAAC,IAAK6B,YAAY,CAAC7B,CAAC,CAAE;QAACuG,WAAW,EAAC;MAAc;QAAA3J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5I,CAAC,eACNpF,OAAA;MAAKoM,SAAS,EAAC,oCAAoC;MAAApH,QAAA,eAE/ChF,OAAA,CAACjB,MAAM;QAACuB,EAAE,EAAC,OAAO;QAAC8L,SAAS,EAAC,wEAAwE;QAACoD,OAAO,EAAEA,CAACnH,CAAC,EAAEoL,GAAG,KAAKtJ,KAAK,CAAC9B,CAAC,EAAEoL,GAAG,GAAG,WAAW,CAAE;QAAAzO,QAAA,EAAEhG,QAAQ,CAAC0U;MAAK;QAAAzO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChL,CAAC,eACNpF,OAAA,CAACb,MAAM;MAACwU,OAAO,EAAEzQ,kBAAmB;MAACsH,KAAK,EAAE;QAAEmE,KAAK,EAAE;MAAQ,CAAE;MAAC6C,MAAM,EAAExS,QAAQ,CAAC4U,OAAQ;MAACC,KAAK;MAACC,MAAM,EAAEzE,wBAAyB;MAAC0E,MAAM,EAAE9E,sBAAuB;MAAAjK,QAAA,eAC7JhF,OAAA;QAAKoM,SAAS,EAAC,sBAAsB;QAAApH,QAAA,gBACjChF,OAAA;UAAGoM,SAAS,EAAC,mCAAmC;UAAC5B,KAAK,EAAE;YAAEoG,QAAQ,EAAE;UAAO;QAAE;UAAA3L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/E5B,MAAM,iBAAIxD,OAAA;UAAAgF,QAAA,GAAOhG,QAAQ,CAACgV,MAAM,EAAC,GAAC,eAAAhU,OAAA;YAAAgF,QAAA,GAAA5E,eAAA,GAAIoD,MAAM,CAAC/C,OAAO,cAAAL,eAAA,uBAAdA,eAAA,CAAgBM;UAAW;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,KAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACTpF,OAAA,CAACb,MAAM;MAACwU,OAAO,EAAEvQ,aAAc;MAACoO,MAAM,EAAEd,UAAW;MAACmD,KAAK;MAACzH,SAAS,EAAC,kBAAkB;MAAC0H,MAAM,EAAEzC,mBAAoB;MAAC0C,MAAM,EAAEtD,UAAW;MAAAzL,QAAA,eACnIhF,OAAA;QAAKoM,SAAS,EAAC,QAAQ;QAAApH,QAAA,eAEnBhF,OAAA;UAAKoM,SAAS,EAAC,wCAAwC;UAAApH,QAAA,eACnDhF,OAAA,CAACnB,eAAe;YACZqJ,KAAK,EAAEpG,YAAa;YACpByP,MAAM,EAAEA,MAAO;YACf0C,OAAO,EAAC,IAAI;YACZC,SAAS;YACTrE,IAAI,EAAE,CAAE;YACRsE,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;YACpCC,aAAa,EAAC,QAAQ;YACtBC,SAAS,EAAEzR,eAAgB;YAC3B0R,iBAAiB,EAAEjM,CAAC,IAAI;cAAExF,kBAAkB,CAACwF,CAAC,CAACH,KAAK,CAAC;cAAEyH,OAAO,CAACtH,CAAC,CAAC;YAAC,CAAE;YACpEkM,gBAAgB,EAAC;UAAQ;YAAAtP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAAjF,EAAA,CAxvBKF,sBAAsB;AAAAuU,EAAA,GAAtBvU,sBAAsB;AA0vB5B,eAAeA,sBAAsB;AAAC,IAAAuU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}