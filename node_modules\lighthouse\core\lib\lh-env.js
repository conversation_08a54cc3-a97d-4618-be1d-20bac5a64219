/**
 * @license Copyright 2020 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

import process from 'process';

// NODE_ENV is set to test by mocha-setup.js and the smokehouse CLI runner
// CI as a catchall for everything we do in GitHub Actions
const isUnderTest = !!process.env.CI || process.env.NODE_ENV === 'test';

export {
  isUnderTest,
};
