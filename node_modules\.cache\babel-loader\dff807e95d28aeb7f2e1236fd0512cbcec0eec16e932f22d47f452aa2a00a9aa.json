{"ast": null, "code": "'use strict';\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Factory for creating new instances\naxios.create = function create(instanceConfig) {\n  return createInstance(mergeConfig(axios.defaults, instanceConfig));\n};\n\n// Expose Cancel & CancelToken\naxios.Cancel = require('./cancel/Cancel');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\n// Expose isAxiosError\naxios.isAxiosError = require('./helpers/isAxiosError');\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;", "map": {"version": 3, "names": ["utils", "require", "bind", "A<PERSON>os", "mergeConfig", "defaults", "createInstance", "defaultConfig", "context", "instance", "prototype", "request", "extend", "axios", "create", "instanceConfig", "Cancel", "CancelToken", "isCancel", "all", "promises", "Promise", "spread", "isAxiosError", "module", "exports", "default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/axios/lib/axios.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Factory for creating new instances\naxios.create = function create(instanceConfig) {\n  return createInstance(mergeConfig(axios.defaults, instanceConfig));\n};\n\n// Expose Cancel & CancelToken\naxios.Cancel = require('./cancel/Cancel');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\n// Expose isAxiosError\naxios.isAxiosError = require('./helpers/isAxiosError');\n\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIC,IAAI,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACpC,IAAIE,KAAK,GAAGF,OAAO,CAAC,cAAc,CAAC;AACnC,IAAIG,WAAW,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AAC/C,IAAII,QAAQ,GAAGJ,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,cAAcA,CAACC,aAAa,EAAE;EACrC,IAAIC,OAAO,GAAG,IAAIL,KAAK,CAACI,aAAa,CAAC;EACtC,IAAIE,QAAQ,GAAGP,IAAI,CAACC,KAAK,CAACO,SAAS,CAACC,OAAO,EAAEH,OAAO,CAAC;;EAErD;EACAR,KAAK,CAACY,MAAM,CAACH,QAAQ,EAAEN,KAAK,CAACO,SAAS,EAAEF,OAAO,CAAC;;EAEhD;EACAR,KAAK,CAACY,MAAM,CAACH,QAAQ,EAAED,OAAO,CAAC;EAE/B,OAAOC,QAAQ;AACjB;;AAEA;AACA,IAAII,KAAK,GAAGP,cAAc,CAACD,QAAQ,CAAC;;AAEpC;AACAQ,KAAK,CAACV,KAAK,GAAGA,KAAK;;AAEnB;AACAU,KAAK,CAACC,MAAM,GAAG,SAASA,MAAMA,CAACC,cAAc,EAAE;EAC7C,OAAOT,cAAc,CAACF,WAAW,CAACS,KAAK,CAACR,QAAQ,EAAEU,cAAc,CAAC,CAAC;AACpE,CAAC;;AAED;AACAF,KAAK,CAACG,MAAM,GAAGf,OAAO,CAAC,iBAAiB,CAAC;AACzCY,KAAK,CAACI,WAAW,GAAGhB,OAAO,CAAC,sBAAsB,CAAC;AACnDY,KAAK,CAACK,QAAQ,GAAGjB,OAAO,CAAC,mBAAmB,CAAC;;AAE7C;AACAY,KAAK,CAACM,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;EACjC,OAAOC,OAAO,CAACF,GAAG,CAACC,QAAQ,CAAC;AAC9B,CAAC;AACDP,KAAK,CAACS,MAAM,GAAGrB,OAAO,CAAC,kBAAkB,CAAC;;AAE1C;AACAY,KAAK,CAACU,YAAY,GAAGtB,OAAO,CAAC,wBAAwB,CAAC;AAEtDuB,MAAM,CAACC,OAAO,GAAGZ,KAAK;;AAEtB;AACAW,MAAM,CAACC,OAAO,CAACC,OAAO,GAAGb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}