{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\creaOrdine.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* CreaOrdine - operazioni sulla creazione di un ordine per il punto vendita\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Caricamento from '../../utils/caricamento';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Nav from \"../../components/navigation/Nav\";\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../../components/traduttore/const';\nimport { BannerWelcome } from \"../../components/generalizzazioni/bannerWelcome\";\nimport { affiliatoGestionePuntiVendita, basePath } from '../../components/route';\nimport '../../css/ToastDemo.css';\nimport '../../css/DataViewDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiOrdineAffiliato = () => {\n  _s();\n  const [results, setResults] = useState(null);\n  const [results2, setResults2] = useState(null);\n  const [results3, setResults3] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function fetchData() {\n      var idRetailer = JSON.parse(localStorage.getItem(\"datiComodo\")).id;\n      if (idRetailer === '0') {\n        window.location.pathname = affiliatoGestionePuntiVendita;\n      }\n      var url = \"pricelistretailer?idRetailer=\" + idRetailer;\n      await APIRequest('GET', url).then(data => {\n        var datasource = data.data.idPriceList2.priceListProducts;\n        setResults(datasource);\n        setResults2(datasource);\n      }).catch(async () => {\n        await APIRequest('GET', 'pricelistaffiliate/').then(data => {\n          var datasource = data.data[0].idPriceList2.priceListProducts;\n          setResults(datasource);\n          setResults2(datasource);\n        }).catch(e => {\n          var _e$response, _e$response2;\n          toast.current.show({\n            severity: 'error',\n            summary: 'Attenzione',\n            detail: \"Al momento non \\xE8 disponibile un listino per il cliente selezionato. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.pathname = basePath;\n          }, 3000);\n        });\n      });\n      await APIRequest('GET', 'pricelistaffiliate/').then(data => {\n        var datasource = data.data[0].idPriceList2.priceListProducts;\n        setResults3(datasource);\n        setLoading(false);\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        toast.current.show({\n          severity: 'error',\n          summary: 'Attenzione',\n          detail: \"Non \\xE8 stato possibile trovare un listino associato per questo profilo. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = basePath;\n        }, 3000);\n      });\n    }\n    fetchData();\n  }, []);\n  var dati = [];\n  dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dataview-demo creaOrdine\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dataview-demo creaOrdine\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {\n      disabled: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(BannerWelcome, {\n      nome: Costanti.CreaOrdinePDV,\n      createFor: dati.firstName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(MarketplaceGen, {\n      results: results,\n      results2: results2,\n      results3: results3,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiOrdineAffiliato, \"cRzxogWqhpY74xtjjJ6Br+HyCDI=\");\n_c = AggiungiOrdineAffiliato;\nexport default AggiungiOrdineAffiliato;\nvar _c;\n$RefreshReg$(_c, \"AggiungiOrdineAffiliato\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Caricamento", "MarketplaceGen", "Nav", "APIRequest", "Toast", "<PERSON><PERSON>", "BannerWelcome", "affiliatoGestionePuntiVendita", "basePath", "jsxDEV", "_jsxDEV", "AggiungiOrdineAffiliato", "_s", "results", "setResults", "results2", "setResults2", "results3", "setResults3", "loading", "setLoading", "toast", "fetchData", "idRetailer", "JSON", "parse", "localStorage", "getItem", "id", "window", "location", "pathname", "url", "then", "data", "datasource", "idPriceList2", "priceListProducts", "catch", "e", "_e$response", "_e$response2", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "setTimeout", "_e$response3", "_e$response4", "dati", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "nome", "CreaOrdinePDV", "createFor", "firstName", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/creaOrdine.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* CreaOrdine - operazioni sulla creazione di un ordine per il punto vendita\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Caricamento from '../../utils/caricamento';\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Nav from \"../../components/navigation/Nav\";\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../../components/traduttore/const';\nimport { BannerWelcome } from \"../../components/generalizzazioni/bannerWelcome\";\nimport { affiliatoGestionePuntiVendita, basePath } from '../../components/route';\nimport '../../css/ToastDemo.css';\nimport '../../css/DataViewDemo.css';\n\nconst AggiungiOrdineAffiliato = () => {\n    const [results, setResults] = useState(null)\n    const [results2, setResults2] = useState(null)\n    const [results3, setResults3] = useState(null)\n    const [loading, setLoading] = useState(true)\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function fetchData() {\n            var idRetailer = JSON.parse(localStorage.getItem(\"datiComodo\")).id\n            if (idRetailer === '0') {\n                window.location.pathname = affiliatoGestionePuntiVendita\n            }\n            var url = \"pricelistretailer?idRetailer=\" + idRetailer;\n            await APIRequest('GET', url)\n                .then(data => {\n                    var datasource = data.data.idPriceList2.priceListProducts;\n                    setResults(datasource)\n                    setResults2(datasource)\n                }).catch(async () => {\n                    await APIRequest('GET', 'pricelistaffiliate/')\n                        .then(data => {\n                            var datasource = data.data[0].idPriceList2.priceListProducts;\n                            setResults(datasource)\n                            setResults2(datasource)\n                        }).catch((e) => {\n                            toast.current.show({ severity: 'error', summary: 'Attenzione', detail: `Al momento non è disponibile un listino per il cliente selezionato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                            setTimeout(() => {\n                                window.location.pathname = basePath;\n                            }, 3000)\n                        })\n                })\n            await APIRequest('GET', 'pricelistaffiliate/')\n                .then(data => {\n                    var datasource = data.data[0].idPriceList2.priceListProducts;\n                    setResults3(datasource)\n                    setLoading(false)\n                }).catch((e) => {\n                    toast.current.show({ severity: 'error', summary: 'Attenzione', detail: `Non è stato possibile trovare un listino associato per questo profilo. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = basePath;\n                    }, 3000)\n                })\n        }\n        fetchData()\n    }, []);\n    var dati = [];\n    dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"))\n    if (loading) {\n        return <div className=\"dataview-demo creaOrdine\">\n            <Toast ref={toast} />\n            <Caricamento />\n        </div>\n    }\n    return (\n        <div className=\"dataview-demo creaOrdine\">\n            <Toast ref={toast} />\n            <Nav disabled={true} />\n                <BannerWelcome nome={Costanti.CreaOrdinePDV} createFor={dati.firstName} />\n            <MarketplaceGen results={results} results2={results2} results3={results3} loading={loading} />\n        </div>\n    )\n}\n\nexport default AggiungiOrdineAffiliato;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,cAAc,MAAM,2DAA2D;AACtF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,aAAa,QAAQ,iDAAiD;AAC/E,SAASC,6BAA6B,EAAEC,QAAQ,QAAQ,wBAAwB;AAChF,OAAO,yBAAyB;AAChC,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMsB,KAAK,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZ,eAAeyB,SAASA,CAAA,EAAG;MACvB,IAAIC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACC,EAAE;MAClE,IAAIL,UAAU,KAAK,GAAG,EAAE;QACpBM,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGxB,6BAA6B;MAC5D;MACA,IAAIyB,GAAG,GAAG,+BAA+B,GAAGT,UAAU;MACtD,MAAMpB,UAAU,CAAC,KAAK,EAAE6B,GAAG,CAAC,CACvBC,IAAI,CAACC,IAAI,IAAI;QACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAACE,YAAY,CAACC,iBAAiB;QACzDvB,UAAU,CAACqB,UAAU,CAAC;QACtBnB,WAAW,CAACmB,UAAU,CAAC;MAC3B,CAAC,CAAC,CAACG,KAAK,CAAC,YAAY;QACjB,MAAMnC,UAAU,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACzC8B,IAAI,CAACC,IAAI,IAAI;UACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,CAACC,iBAAiB;UAC5DvB,UAAU,CAACqB,UAAU,CAAC;UACtBnB,WAAW,CAACmB,UAAU,CAAC;QAC3B,CAAC,CAAC,CAACG,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAC,WAAA,EAAAC,YAAA;UACZpB,KAAK,CAACqB,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,YAAY;YAAEC,MAAM,8FAAAC,MAAA,CAA2F,EAAAP,WAAA,GAAAD,CAAC,CAACS,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAYN,IAAI,MAAKe,SAAS,IAAAR,YAAA,GAAGF,CAAC,CAACS,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAYP,IAAI,GAAGK,CAAC,CAACW,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UAC9OC,UAAU,CAAC,MAAM;YACbvB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGvB,QAAQ;UACvC,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC;MACV,CAAC,CAAC;MACN,MAAML,UAAU,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACzC8B,IAAI,CAACC,IAAI,IAAI;QACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,CAACC,iBAAiB;QAC5DnB,WAAW,CAACiB,UAAU,CAAC;QACvBf,UAAU,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,CAACkB,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAc,YAAA,EAAAC,YAAA;QACZjC,KAAK,CAACqB,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,YAAY;UAAEC,MAAM,iGAAAC,MAAA,CAA8F,EAAAM,YAAA,GAAAd,CAAC,CAACS,QAAQ,cAAAK,YAAA,uBAAVA,YAAA,CAAYnB,IAAI,MAAKe,SAAS,IAAAK,YAAA,GAAGf,CAAC,CAACS,QAAQ,cAAAM,YAAA,uBAAVA,YAAA,CAAYpB,IAAI,GAAGK,CAAC,CAACW,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QACjPC,UAAU,CAAC,MAAM;UACbvB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGvB,QAAQ;QACvC,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV;IACAc,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAIiC,IAAI,GAAG,EAAE;EACbA,IAAI,GAAG/B,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;EACvD,IAAIR,OAAO,EAAE;IACT,oBAAOT,OAAA;MAAK8C,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAC5C/C,OAAA,CAACN,KAAK;QAACsD,GAAG,EAAErC;MAAM;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBpD,OAAA,CAACV,WAAW;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EACV;EACA,oBACIpD,OAAA;IAAK8C,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACrC/C,OAAA,CAACN,KAAK;MAACsD,GAAG,EAAErC;IAAM;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBpD,OAAA,CAACR,GAAG;MAAC6D,QAAQ,EAAE;IAAK;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBpD,OAAA,CAACJ,aAAa;MAAC0D,IAAI,EAAE3D,QAAQ,CAAC4D,aAAc;MAACC,SAAS,EAAEX,IAAI,CAACY;IAAU;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9EpD,OAAA,CAACT,cAAc;MAACY,OAAO,EAAEA,OAAQ;MAACE,QAAQ,EAAEA,QAAS;MAACE,QAAQ,EAAEA,QAAS;MAACE,OAAO,EAAEA;IAAQ;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7F,CAAC;AAEd,CAAC;AAAAlD,EAAA,CA9DKD,uBAAuB;AAAAyD,EAAA,GAAvBzD,uBAAuB;AAgE7B,eAAeA,uBAAuB;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}