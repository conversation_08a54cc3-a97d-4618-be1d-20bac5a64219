{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\index.js\";\nimport React, { Suspense } from 'react';\nimport ReactDOM from 'react-dom';\nimport 'bootstrap';\nimport 'bootstrap/dist/css/bootstrap.css';\nimport './index.css';\nimport App from './App';\n/* import * as serviceWorker from './serviceWorker'; */\nimport './components/traduttore/i18n';\nimport logo from './img/tm_logo-01.svg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactDOM.render(\n/*#__PURE__*/\n/* <React.StrictMode>  */\n_jsxDEV(Suspense, {\n  fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"suspense-loading logo\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"fontLogo\",\n      className: \"logo text-center my-3 d-flex justify-content-center w-100\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: logo,\n        onError: e => e.target.src = logo,\n        alt: \"Logo\",\n        width: \"200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 154\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 65\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 26\n  }, this),\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 13,\n  columnNumber: 5\n}, this)\n/* </React.StrictMode>, */, document.getElementById('root'));\n\n// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://bit.ly/CRA-PWA\n/* serviceWorker.unregister(); */", "map": {"version": 3, "names": ["React", "Suspense", "ReactDOM", "App", "logo", "jsxDEV", "_jsxDEV", "render", "fallback", "className", "children", "id", "src", "onError", "e", "target", "alt", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "getElementById"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/index.js"], "sourcesContent": ["import React, { Suspense } from 'react';\nimport ReactDOM from 'react-dom';\nimport 'bootstrap';\nimport 'bootstrap/dist/css/bootstrap.css';\nimport './index.css';\nimport App from './App';\n/* import * as serviceWorker from './serviceWorker'; */\nimport './components/traduttore/i18n';\nimport logo from './img/tm_logo-01.svg';\n\nReactDOM.render(\n    /* <React.StrictMode>  */\n    <Suspense fallback={(<div className='suspense-loading logo'><div id=\"fontLogo\" className=\"logo text-center my-3 d-flex justify-content-center w-100\"><img src={logo} onError={(e) => e.target.src = logo} alt=\"Logo\" width=\"200\" /></div></div>)} >\n        <App />\n    </Suspense>\n    /* </React.StrictMode>, */\n    , document.getElementById('root')\n);\n\n// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://bit.ly/CRA-PWA\n/* serviceWorker.unregister(); */\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAO,WAAW;AAClB,OAAO,kCAAkC;AACzC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB;AACA,OAAO,8BAA8B;AACrC,OAAOC,IAAI,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExCJ,QAAQ,CAACK,MAAM;AAAA;AACX;AACAD,OAAA,CAACL,QAAQ;EAACO,QAAQ,eAAGF,OAAA;IAAKG,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eAACJ,OAAA;MAAKK,EAAE,EAAC,UAAU;MAACF,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eAACJ,OAAA;QAAKM,GAAG,EAAER,IAAK;QAACS,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGR,IAAK;QAACY,GAAG,EAAC,MAAM;QAACC,KAAK,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAG;EAAAX,QAAA,eAC7OJ,OAAA,CAACH,GAAG;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACD;AACV,4BACEC,QAAQ,CAACC,cAAc,CAAC,MAAM,CACpC,CAAC;;AAED;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}