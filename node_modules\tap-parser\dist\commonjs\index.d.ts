/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import { EventEmitter } from 'events';
import { FinalResults } from './final-results.js';
import { Result, TapError } from './result.js';
export type { FinalPlan } from './final-plan.js';
export { FinalResults } from './final-results.js';
export { lineType, lineTypes } from './line-type.js';
export type { ParsedLine } from './line-type.js';
export { parseDirective } from './parse-directive.js';
export type { Directive } from './parse-directive.js';
export { Plan } from './plan.js';
export { Result } from './result.js';
export type { TapError } from './result.js';
export type { EventLog };
import type etoa from 'events-to-array';
type EventLog = ReturnType<typeof etoa>;
export interface ParserOptions {
    name?: string;
    passes?: boolean;
    parent?: Parser;
    level?: number;
    closingTestPoint?: Result;
    bail?: boolean;
    omitVersion?: boolean;
    buffered?: boolean;
    preserveWhitespace?: boolean;
    strict?: boolean;
    flat?: boolean;
}
export interface Pragmas {
    [pragma: string]: boolean;
}
export declare class Parser extends EventEmitter implements NodeJS.WritableStream {
    #private;
    aborted: boolean;
    bail: boolean;
    bailedOut: boolean | string;
    braceLevel: number;
    buffer: string;
    buffered: boolean;
    comments: string[];
    count: number;
    fail: number;
    failures: TapError[];
    skips: (Result & {
        skip: string | true;
    })[];
    todos: (Result & {
        todo: string | true;
    })[];
    level: number;
    name: string;
    ok: boolean;
    omitVersion: boolean;
    parent: Parser | null;
    pass: number;
    passes: Result[] | null;
    planComment: string;
    planEnd: number;
    planStart: number;
    pointsSeen: Map<number, Result>;
    pragmas: Pragmas;
    preserveWhitespace: boolean;
    results: FinalResults | null;
    root: Parser;
    skip: number;
    strict: boolean;
    syntheticBailout: boolean;
    syntheticPlan: boolean;
    time: number | null;
    todo: number;
    get closingTestPoint(): Result | null;
    set closingTestPoint(res: Result | null);
    get readable(): false;
    get writable(): true;
    constructor(onComplete?: (results: FinalResults) => any);
    constructor(options?: ParserOptions, onComplete?: (results: FinalResults) => any);
    get fullname(): string;
    tapError(error: Result | {
        tapError: string;
        [k: string]: any;
    } | string, line: string): void;
    parseTestPoint(testPoint: RegExpMatchArray, line: string): void;
    nonTap(data: string, didLine?: boolean): void;
    emitExtra(data: string, fromChild?: boolean): void;
    plan(start: number, end: number, comment: string, line: string): void;
    resetYamlish(): void;
    yamlGarbage(): void;
    yamlishLine(line: string): void;
    processYamlish(): void;
    write(chunk: string | Uint8Array | Buffer, cb?: (...x: any[]) => any): boolean;
    write(chunk: string | Uint8Array | Buffer, encoding?: BufferEncoding): boolean;
    write(chunk: string | Uint8Array | Buffer, encoding?: BufferEncoding, cb?: (...x: any[]) => any): boolean;
    end(chunk?: string | Buffer | Uint8Array, encoding?: BufferEncoding, cb?: (...a: any[]) => any): this;
    end(chunk?: string | Buffer | Uint8Array, cb?: (...a: any[]) => any): this;
    end(cb?: (...a: any[]) => any): this;
    emitComplete(skipAll: boolean): void;
    version(version: number, line: string): void;
    pragma(key: string, value: boolean, line: string): void;
    bailout(reason: string, synthetic?: boolean): void;
    clearExtraQueue(): void;
    endChild(): void;
    emitResult(): void;
    startChild(line: string): void;
    destroy(er?: Error): void;
    abort(message?: string, extra?: any): void;
    emitAssert(res: Result): void;
    emitComment(line: string, skipLine?: boolean, noDuplicate?: boolean): void;
    parse(line: string): boolean | "" | undefined;
    parseIndent(line: string, indent: string): void;
    static parse(str: string, options?: ParserOptions): EventLog;
    static stringify(msg: EventLog, options?: {}): string;
}
//# sourceMappingURL=index.d.ts.map