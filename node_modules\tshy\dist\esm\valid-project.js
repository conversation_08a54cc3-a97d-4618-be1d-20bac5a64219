import { resolve } from 'path';
import { readFileSync } from 'node:fs';
import fail from './fail.js';
export default (p) => {
    if (typeof p === 'string') {
        try {
            readFileSync(resolve(p), 'utf8');
            return true;
        }
        catch (_) { }
    }
    fail(`tshy.project must point to a tsconfig file on disk, ` +
        `got: ${JSON.stringify(p)}`);
    return process.exit(1);
};
//# sourceMappingURL=valid-project.js.map