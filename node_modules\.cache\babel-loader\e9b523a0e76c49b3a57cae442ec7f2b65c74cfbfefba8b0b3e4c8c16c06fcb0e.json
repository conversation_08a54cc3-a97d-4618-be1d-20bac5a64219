{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ListContext } from './index';\nimport { Col } from '../grid';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nexport var Meta = function Meta(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    avatar = _a.avatar,\n    title = _a.title,\n    description = _a.description,\n    others = __rest(_a, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var classString = classNames(\"\".concat(prefixCls, \"-item-meta\"), className);\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-content\")\n  }, title && /*#__PURE__*/React.createElement(\"h4\", {\n    className: \"\".concat(prefixCls, \"-item-meta-title\")\n  }, title), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-description\")\n  }, description));\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, others, {\n    className: classString\n  }), avatar && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-avatar\")\n  }, avatar), (title || description) && content);\n};\nvar Item = function Item(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    children = _a.children,\n    actions = _a.actions,\n    extra = _a.extra,\n    className = _a.className,\n    colStyle = _a.colStyle,\n    others = __rest(_a, [\"prefixCls\", \"children\", \"actions\", \"extra\", \"className\", \"colStyle\"]);\n  var _React$useContext2 = React.useContext(ListContext),\n    grid = _React$useContext2.grid,\n    itemLayout = _React$useContext2.itemLayout;\n  var _React$useContext3 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext3.getPrefixCls;\n  var isItemContainsTextNodeAndNotSingular = function isItemContainsTextNodeAndNotSingular() {\n    var result;\n    React.Children.forEach(children, function (element) {\n      if (typeof element === 'string') {\n        result = true;\n      }\n    });\n    return result && React.Children.count(children) > 1;\n  };\n  var isFlexMode = function isFlexMode() {\n    if (itemLayout === 'vertical') {\n      return !!extra;\n    }\n    return !isItemContainsTextNodeAndNotSingular();\n  };\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var actionsContent = actions && actions.length > 0 && /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-item-action\"),\n    key: \"actions\"\n  }, actions.map(function (action, i) {\n    return (/*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: \"\".concat(prefixCls, \"-item-action-\").concat(i)\n      }, action, i !== actions.length - 1 && /*#__PURE__*/React.createElement(\"em\", {\n        className: \"\".concat(prefixCls, \"-item-action-split\")\n      }))\n    );\n  }));\n  var Element = grid ? 'div' : 'li';\n  var itemChildren = /*#__PURE__*/React.createElement(Element, _extends({}, others, {\n    // `li` element `onCopy` prop args is not same as `div`\n    className: classNames(\"\".concat(prefixCls, \"-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-item-no-flex\"), !isFlexMode()), className)\n  }), itemLayout === 'vertical' && extra ? [/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-main\"),\n    key: \"content\"\n  }, children, actionsContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-extra\"),\n    key: \"extra\"\n  }, extra)] : [children, actionsContent, cloneElement(extra, {\n    key: 'extra'\n  })]);\n  return grid ? /*#__PURE__*/React.createElement(Col, {\n    flex: 1,\n    style: colStyle\n  }, itemChildren) : itemChildren;\n};\nItem.Meta = Meta;\nexport default Item;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ListContext", "Col", "ConfigContext", "cloneElement", "Meta", "_a", "customizePrefixCls", "prefixCls", "className", "avatar", "title", "description", "others", "_React$useContext", "useContext", "getPrefixCls", "classString", "concat", "content", "createElement", "<PERSON><PERSON>", "children", "actions", "extra", "colStyle", "_React$useContext2", "grid", "itemLayout", "_React$useContext3", "isItemContainsTextNodeAndNotSingular", "result", "Children", "for<PERSON>ach", "element", "count", "isFlexMode", "actionsContent", "key", "map", "action", "Element", "itemChildren", "flex", "style"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/list/Item.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ListContext } from './index';\nimport { Col } from '../grid';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nexport var Meta = function Meta(_a) {\n  var customizePrefixCls = _a.prefixCls,\n      className = _a.className,\n      avatar = _a.avatar,\n      title = _a.title,\n      description = _a.description,\n      others = __rest(_a, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var classString = classNames(\"\".concat(prefixCls, \"-item-meta\"), className);\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-content\")\n  }, title && /*#__PURE__*/React.createElement(\"h4\", {\n    className: \"\".concat(prefixCls, \"-item-meta-title\")\n  }, title), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-description\")\n  }, description));\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, others, {\n    className: classString\n  }), avatar && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-avatar\")\n  }, avatar), (title || description) && content);\n};\n\nvar Item = function Item(_a) {\n  var customizePrefixCls = _a.prefixCls,\n      children = _a.children,\n      actions = _a.actions,\n      extra = _a.extra,\n      className = _a.className,\n      colStyle = _a.colStyle,\n      others = __rest(_a, [\"prefixCls\", \"children\", \"actions\", \"extra\", \"className\", \"colStyle\"]);\n\n  var _React$useContext2 = React.useContext(ListContext),\n      grid = _React$useContext2.grid,\n      itemLayout = _React$useContext2.itemLayout;\n\n  var _React$useContext3 = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext3.getPrefixCls;\n\n  var isItemContainsTextNodeAndNotSingular = function isItemContainsTextNodeAndNotSingular() {\n    var result;\n    React.Children.forEach(children, function (element) {\n      if (typeof element === 'string') {\n        result = true;\n      }\n    });\n    return result && React.Children.count(children) > 1;\n  };\n\n  var isFlexMode = function isFlexMode() {\n    if (itemLayout === 'vertical') {\n      return !!extra;\n    }\n\n    return !isItemContainsTextNodeAndNotSingular();\n  };\n\n  var prefixCls = getPrefixCls('list', customizePrefixCls);\n  var actionsContent = actions && actions.length > 0 && /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-item-action\"),\n    key: \"actions\"\n  }, actions.map(function (action, i) {\n    return (\n      /*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: \"\".concat(prefixCls, \"-item-action-\").concat(i)\n      }, action, i !== actions.length - 1 && /*#__PURE__*/React.createElement(\"em\", {\n        className: \"\".concat(prefixCls, \"-item-action-split\")\n      }))\n    );\n  }));\n  var Element = grid ? 'div' : 'li';\n  var itemChildren = /*#__PURE__*/React.createElement(Element, _extends({}, others, {\n    // `li` element `onCopy` prop args is not same as `div`\n    className: classNames(\"\".concat(prefixCls, \"-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-item-no-flex\"), !isFlexMode()), className)\n  }), itemLayout === 'vertical' && extra ? [/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-main\"),\n    key: \"content\"\n  }, children, actionsContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-extra\"),\n    key: \"extra\"\n  }, extra)] : [children, actionsContent, cloneElement(extra, {\n    key: 'extra'\n  })]);\n  return grid ? /*#__PURE__*/React.createElement(Col, {\n    flex: 1,\n    style: colStyle\n  }, itemChildren) : itemChildren;\n};\n\nItem.Meta = Meta;\nexport default Item;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AAEzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASC,GAAG,QAAQ,SAAS;AAC7B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAE;EAClC,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACjCC,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxBC,MAAM,GAAGJ,EAAE,CAACI,MAAM;IAClBC,KAAK,GAAGL,EAAE,CAACK,KAAK;IAChBC,WAAW,GAAGN,EAAE,CAACM,WAAW;IAC5BC,MAAM,GAAG5B,MAAM,CAACqB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;EAErF,IAAIQ,iBAAiB,GAAGf,KAAK,CAACgB,UAAU,CAACZ,aAAa,CAAC;IACnDa,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIR,SAAS,GAAGQ,YAAY,CAAC,MAAM,EAAET,kBAAkB,CAAC;EACxD,IAAIU,WAAW,GAAGjB,UAAU,CAAC,EAAE,CAACkB,MAAM,CAACV,SAAS,EAAE,YAAY,CAAC,EAAEC,SAAS,CAAC;EAC3E,IAAIU,OAAO,GAAG,aAAapB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACpDX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,oBAAoB;EACtD,CAAC,EAAEG,KAAK,IAAI,aAAaZ,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE;IACjDX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,kBAAkB;EACpD,CAAC,EAAEG,KAAK,CAAC,EAAEC,WAAW,IAAI,aAAab,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAChEX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,wBAAwB;EAC1D,CAAC,EAAEI,WAAW,CAAC,CAAC;EAChB,OAAO,aAAab,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAEpC,QAAQ,CAAC,CAAC,CAAC,EAAE6B,MAAM,EAAE;IAClEJ,SAAS,EAAEQ;EACb,CAAC,CAAC,EAAEP,MAAM,IAAI,aAAaX,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACpDX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAEE,MAAM,CAAC,EAAE,CAACC,KAAK,IAAIC,WAAW,KAAKO,OAAO,CAAC;AAChD,CAAC;AAED,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACf,EAAE,EAAE;EAC3B,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACjCc,QAAQ,GAAGhB,EAAE,CAACgB,QAAQ;IACtBC,OAAO,GAAGjB,EAAE,CAACiB,OAAO;IACpBC,KAAK,GAAGlB,EAAE,CAACkB,KAAK;IAChBf,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxBgB,QAAQ,GAAGnB,EAAE,CAACmB,QAAQ;IACtBZ,MAAM,GAAG5B,MAAM,CAACqB,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EAE/F,IAAIoB,kBAAkB,GAAG3B,KAAK,CAACgB,UAAU,CAACd,WAAW,CAAC;IAClD0B,IAAI,GAAGD,kBAAkB,CAACC,IAAI;IAC9BC,UAAU,GAAGF,kBAAkB,CAACE,UAAU;EAE9C,IAAIC,kBAAkB,GAAG9B,KAAK,CAACgB,UAAU,CAACZ,aAAa,CAAC;IACpDa,YAAY,GAAGa,kBAAkB,CAACb,YAAY;EAElD,IAAIc,oCAAoC,GAAG,SAASA,oCAAoCA,CAAA,EAAG;IACzF,IAAIC,MAAM;IACVhC,KAAK,CAACiC,QAAQ,CAACC,OAAO,CAACX,QAAQ,EAAE,UAAUY,OAAO,EAAE;MAClD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BH,MAAM,GAAG,IAAI;MACf;IACF,CAAC,CAAC;IACF,OAAOA,MAAM,IAAIhC,KAAK,CAACiC,QAAQ,CAACG,KAAK,CAACb,QAAQ,CAAC,GAAG,CAAC;EACrD,CAAC;EAED,IAAIc,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIR,UAAU,KAAK,UAAU,EAAE;MAC7B,OAAO,CAAC,CAACJ,KAAK;IAChB;IAEA,OAAO,CAACM,oCAAoC,CAAC,CAAC;EAChD,CAAC;EAED,IAAItB,SAAS,GAAGQ,YAAY,CAAC,MAAM,EAAET,kBAAkB,CAAC;EACxD,IAAI8B,cAAc,GAAGd,OAAO,IAAIA,OAAO,CAAC1B,MAAM,GAAG,CAAC,IAAI,aAAaE,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE;IAC3FX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,cAAc,CAAC;IAC/C8B,GAAG,EAAE;EACP,CAAC,EAAEf,OAAO,CAACgB,GAAG,CAAC,UAAUC,MAAM,EAAE5C,CAAC,EAAE;IAClC,QACE;MACA;MACAG,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE;QACxBkB,GAAG,EAAE,EAAE,CAACpB,MAAM,CAACV,SAAS,EAAE,eAAe,CAAC,CAACU,MAAM,CAACtB,CAAC;MACrD,CAAC,EAAE4C,MAAM,EAAE5C,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,IAAI,aAAaE,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE;QAC5EX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,oBAAoB;MACtD,CAAC,CAAC;IAAC;EAEP,CAAC,CAAC,CAAC;EACH,IAAIiC,OAAO,GAAGd,IAAI,GAAG,KAAK,GAAG,IAAI;EACjC,IAAIe,YAAY,GAAG,aAAa3C,KAAK,CAACqB,aAAa,CAACqB,OAAO,EAAEzD,QAAQ,CAAC,CAAC,CAAC,EAAE6B,MAAM,EAAE;IAChF;IACAJ,SAAS,EAAET,UAAU,CAAC,EAAE,CAACkB,MAAM,CAACV,SAAS,EAAE,OAAO,CAAC,EAAEzB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmC,MAAM,CAACV,SAAS,EAAE,eAAe,CAAC,EAAE,CAAC4B,UAAU,CAAC,CAAC,CAAC,EAAE3B,SAAS;EAC3I,CAAC,CAAC,EAAEmB,UAAU,KAAK,UAAU,IAAIJ,KAAK,GAAG,CAAC,aAAazB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAChFX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,YAAY,CAAC;IAC7C8B,GAAG,EAAE;EACP,CAAC,EAAEhB,QAAQ,EAAEe,cAAc,CAAC,EAAE,aAAatC,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACpEX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACV,SAAS,EAAE,aAAa,CAAC;IAC9C8B,GAAG,EAAE;EACP,CAAC,EAAEd,KAAK,CAAC,CAAC,GAAG,CAACF,QAAQ,EAAEe,cAAc,EAAEjC,YAAY,CAACoB,KAAK,EAAE;IAC1Dc,GAAG,EAAE;EACP,CAAC,CAAC,CAAC,CAAC;EACJ,OAAOX,IAAI,GAAG,aAAa5B,KAAK,CAACqB,aAAa,CAAClB,GAAG,EAAE;IAClDyC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAEnB;EACT,CAAC,EAAEiB,YAAY,CAAC,GAAGA,YAAY;AACjC,CAAC;AAEDrB,IAAI,CAAChB,IAAI,GAAGA,IAAI;AAChB,eAAegB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}