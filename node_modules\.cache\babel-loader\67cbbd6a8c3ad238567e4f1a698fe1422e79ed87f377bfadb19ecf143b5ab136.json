{"ast": null, "code": "import * as React from 'react';\n/**\n * Locker return cached mark.\n * If set to `true`, will return `true` in a short time even if set `false`.\n * If set to `false` and then set to `true`, will change to `true`.\n * And after time duration, it will back to `null` automatically.\n */\n\nexport default function useLock() {\n  var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 250;\n  var lockRef = React.useRef(null);\n  var timeoutRef = React.useRef(null); // Clean up\n\n  React.useEffect(function () {\n    return function () {\n      window.clearTimeout(timeoutRef.current);\n    };\n  }, []);\n  function doLock(locked) {\n    if (locked || lockRef.current === null) {\n      lockRef.current = locked;\n    }\n    window.clearTimeout(timeoutRef.current);\n    timeoutRef.current = window.setTimeout(function () {\n      lockRef.current = null;\n    }, duration);\n  }\n  return [function () {\n    return lockRef.current;\n  }, doLock];\n}", "map": {"version": 3, "names": ["React", "useLock", "duration", "arguments", "length", "undefined", "lockRef", "useRef", "timeoutRef", "useEffect", "window", "clearTimeout", "current", "doLock", "locked", "setTimeout"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/hooks/useLock.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * Locker return cached mark.\n * If set to `true`, will return `true` in a short time even if set `false`.\n * If set to `false` and then set to `true`, will change to `true`.\n * And after time duration, it will back to `null` automatically.\n */\n\nexport default function useLock() {\n  var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 250;\n  var lockRef = React.useRef(null);\n  var timeoutRef = React.useRef(null); // Clean up\n\n  React.useEffect(function () {\n    return function () {\n      window.clearTimeout(timeoutRef.current);\n    };\n  }, []);\n\n  function doLock(locked) {\n    if (locked || lockRef.current === null) {\n      lockRef.current = locked;\n    }\n\n    window.clearTimeout(timeoutRef.current);\n    timeoutRef.current = window.setTimeout(function () {\n      lockRef.current = null;\n    }, duration);\n  }\n\n  return [function () {\n    return lockRef.current;\n  }, doLock];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,OAAOA,CAAA,EAAG;EAChC,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EACtF,IAAIG,OAAO,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,UAAU,GAAGR,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAErCP,KAAK,CAACS,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBC,MAAM,CAACC,YAAY,CAACH,UAAU,CAACI,OAAO,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,SAASC,MAAMA,CAACC,MAAM,EAAE;IACtB,IAAIA,MAAM,IAAIR,OAAO,CAACM,OAAO,KAAK,IAAI,EAAE;MACtCN,OAAO,CAACM,OAAO,GAAGE,MAAM;IAC1B;IAEAJ,MAAM,CAACC,YAAY,CAACH,UAAU,CAACI,OAAO,CAAC;IACvCJ,UAAU,CAACI,OAAO,GAAGF,MAAM,CAACK,UAAU,CAAC,YAAY;MACjDT,OAAO,CAACM,OAAO,GAAG,IAAI;IACxB,CAAC,EAAEV,QAAQ,CAAC;EACd;EAEA,OAAO,CAAC,YAAY;IAClB,OAAOI,OAAO,CAACM,OAAO;EACxB,CAAC,EAAEC,MAAM,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}