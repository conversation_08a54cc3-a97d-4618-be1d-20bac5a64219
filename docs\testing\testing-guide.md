# Guida ai Test Automatici - E-Procurement Frontend

## 📋 Panoramica

Questa guida descrive la suite completa di test automatici implementata per il sistema e-procurement. I test sono organizzati in quattro categorie principali per garantire la qualità e l'affidabilità del sistema.

## 🧪 Tipologie di Test

### 1. Business Rules Tests (`business-rules.test.js`)
Verificano che tutte le regole di business del sistema siano rispettate correttamente.

**Aree coperte:**
- Autenticazione e autorizzazione
- Gestione ordini e stati
- Validazioni magazzino
- Controlli di sicurezza
- Validazioni dati

**Esecuzione:**
```bash
npm run test:business
```

### 2. API Integration Tests (`api-integration.test.js`)
Testano l'integrazione con tutte le API del backend e la gestione degli errori.

**Aree coperte:**
- Endpoint di autenticazione
- API prodotti e marketplace
- Gestione carrello e ordini
- Operazioni magazzino
- Gestione errori di rete

**Esecuzione:**
```bash
npm run test:integration
```

### 3. User Workflows Tests (`user-workflows.test.js`)
Verificano i percorsi utente completi end-to-end per ogni ruolo.

**Aree coperte:**
- Journey completo distributore
- Workflow shopping PDV
- Gestione clienti agente
- Gestione errori utente

**Esecuzione:**
```bash
npm run test:workflows
```

### 4. Component Tests (`components.test.js`)
Testano il comportamento dei componenti UI principali.

**Aree coperte:**
- Componenti di login
- Dashboard per ruoli
- Carrello e checkout
- Validazioni form
- Stati di caricamento

**Esecuzione:**
```bash
npm run test:components
```

## 🚀 Comandi di Test

### Esecuzione Singola
```bash
# Test specifici
npm run test:business      # Solo regole di business
npm run test:integration   # Solo integrazione API
npm run test:workflows     # Solo workflow utente
npm run test:components    # Solo componenti UI

# Test con coverage
npm run test:coverage      # Tutti i test con coverage report
```

### Esecuzione Completa
```bash
# Tutti i test in sequenza
npm run test:all

# Test in modalità CI
npm run test:ci
```

### Modalità Sviluppo
```bash
# Watch mode per sviluppo
npm test

# Test specifico in watch mode
npm test -- --testNamePattern="Login Component"
```

## 📊 Coverage Requirements

### Soglie Minime
- **Componenti**: 80% line coverage
- **Utils**: 90% line coverage
- **API Integration**: 100% happy path + error cases
- **Business Logic**: 95% coverage

### Soglie Specifiche
- **apireq.jsx**: 95% (componente critico)
- **privateRoute.jsx**: 90% (sicurezza)
- **utils/**: 90% (funzioni di supporto)

## 🛠️ Struttura Test

### Organizzazione File
```
tests/
├── business-rules.test.js     # Regole di business
├── api-integration.test.js    # Integrazione API
├── user-workflows.test.js     # Workflow utente
├── components.test.js         # Componenti UI
├── setup/                     # Configurazioni setup
│   ├── test-setup.js         # Setup globale
│   ├── business-setup.js     # Setup business rules
│   ├── api-setup.js          # Setup API integration
│   ├── workflow-setup.js     # Setup workflow
│   └── component-setup.js    # Setup componenti
└── mocks/                     # Mock e utilities
    ├── server.js             # Mock server MSW
    ├── handlers.js           # Handler API mock
    └── utils/                # Utilities test
```

### Pattern di Test
```javascript
describe('Feature Name', () => {
  beforeEach(() => {
    // Setup specifico per ogni test
  });

  describe('Authentication', () => {
    it('should handle valid credentials', () => {
      // Test implementation
    });
  });

  describe('Authorization', () => {
    it('should enforce role permissions', () => {
      // Test implementation
    });
  });
});
```

## 🔧 Configurazione

### Jest Configuration
Il file `jest.config.js` contiene:
- Setup environment jsdom
- Coverage thresholds
- Module mapping
- Test timeout settings

### Mock Server (MSW)
Utilizza Mock Service Worker per:
- Intercettare chiamate API
- Simulare risposte server
- Testare scenari di errore

### Test Utilities
Helper functions per:
- Setup utenti autenticati
- Generazione dati test
- Mock API responses
- Validazioni business

## 📋 Regole di Business Testate

### Autenticazione
- JWT token obbligatorio per API
- Gerarchia ruoli: ADMIN > DISTRIBUTORE > AFFILIATO > AGENTE > PDV
- Controllo accesso per route protette
- Gestione sessioni e logout

### Gestione Ordini
- Stati ordine: BOZZA → CONFERMATO → IN_LAVORAZIONE → SPEDITO → CONSEGNATO
- Validazioni: quantità > 0, prodotti disponibili
- Workflow: Carrello → Ordine → Documento → Spedizione

### Gestione Magazzino
- Calcolo disponibilità: quantity - reserved = available
- Controlli scorte minime
- Validazione scadenze e lotti

### Validazioni Dati
- Email: formato RFC compliant
- P.IVA: obbligatoria per clienti
- Telefono: formato italiano/internazionale
- Indirizzo: via, città, CAP obbligatori

## 🚨 Scenari di Errore Testati

### Errori di Rete
- Timeout connessione
- Server non raggiungibile
- Errori HTTP 500/404/401

### Errori di Business
- Credenziali non valide
- Permessi insufficienti
- Dati non validi
- Stato ordine non valido

### Errori UI
- Form validation
- Loading states
- Error boundaries
- Responsive design

## 📈 Metriche e Reporting

### Coverage Report
```bash
npm run test:coverage
```
Genera report in `coverage/lcov-report/index.html`

### Test Results
- Risultati dettagliati per ogni suite
- Tempo di esecuzione
- Percentuale successo
- Errori e warning

## 🔄 Integrazione CI/CD

### Pre-commit Hooks
```bash
# Verifica che i test passino prima del commit
npm run test:ci
```

### Pipeline CI
```yaml
# Esempio GitHub Actions
- name: Run Tests
  run: |
    npm run test:all
    npm run test:coverage
```

## 📝 Best Practices

### Scrittura Test
1. **Arrange-Act-Assert**: Struttura chiara
2. **Descriptive Names**: Nomi test descrittivi
3. **Single Responsibility**: Un concetto per test
4. **Independent Tests**: Test isolati e indipendenti

### Manutenzione
1. **Update Regularly**: Aggiorna test con nuove feature
2. **Refactor**: Mantieni test puliti e leggibili
3. **Mock Appropriately**: Mock solo dipendenze esterne
4. **Test Edge Cases**: Includi casi limite

### Performance
1. **Parallel Execution**: Esegui test in parallelo
2. **Selective Testing**: Test solo codice modificato
3. **Fast Feedback**: Test rapidi per sviluppo
4. **Comprehensive CI**: Test completi in CI

## 🆘 Troubleshooting

### Problemi Comuni
1. **Test Timeout**: Aumenta timeout in jest.config.js
2. **Mock Issues**: Verifica setup MSW
3. **Coverage Low**: Aggiungi test per codice non coperto
4. **Flaky Tests**: Stabilizza con waitFor e proper cleanup

### Debug
```bash
# Debug singolo test
npm test -- --testNamePattern="specific test" --verbose

# Debug con breakpoint
npm test -- --runInBand --no-cache
```

## 📚 Risorse

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [MSW Documentation](https://mswjs.io/docs/)
- [React Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

---

**Nota**: Mantieni sempre aggiornati i test quando modifichi il codice. I test sono la documentazione vivente del comportamento del sistema.
