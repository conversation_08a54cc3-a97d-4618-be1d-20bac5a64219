{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nexports.useLocaleReceiver = useLocaleReceiver;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _createSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createSuper\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _default = _interopRequireDefault(require(\"./default\"));\nvar _context = _interopRequireDefault(require(\"./context\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar LocaleReceiver = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(LocaleReceiver, _React$Component);\n  var _super = (0, _createSuper2[\"default\"])(LocaleReceiver);\n  function LocaleReceiver() {\n    (0, _classCallCheck2[\"default\"])(this, LocaleReceiver);\n    return _super.apply(this, arguments);\n  }\n  (0, _createClass2[\"default\"])(LocaleReceiver, [{\n    key: \"getLocale\",\n    value: function getLocale() {\n      var _this$props = this.props,\n        componentName = _this$props.componentName,\n        defaultLocale = _this$props.defaultLocale;\n      var locale = defaultLocale || _default[\"default\"][componentName !== null && componentName !== void 0 ? componentName : 'global'];\n      var antLocale = this.context;\n      var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n      return (0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, locale instanceof Function ? locale() : locale), localeFromContext || {});\n    }\n  }, {\n    key: \"getLocaleCode\",\n    value: function getLocaleCode() {\n      var antLocale = this.context;\n      var localeCode = antLocale && antLocale.locale; // Had use LocaleProvide but didn't set locale\n\n      if (antLocale && antLocale.exist && !localeCode) {\n        return _default[\"default\"].locale;\n      }\n      return localeCode;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return this.props.children(this.getLocale(), this.getLocaleCode(), this.context);\n    }\n  }]);\n  return LocaleReceiver;\n}(React.Component);\nexports[\"default\"] = LocaleReceiver;\nLocaleReceiver.defaultProps = {\n  componentName: 'global'\n};\nLocaleReceiver.contextType = _context[\"default\"];\nfunction useLocaleReceiver(componentName, defaultLocale) {\n  var antLocale = React.useContext(_context[\"default\"]);\n  var componentLocale = React.useMemo(function () {\n    var locale = defaultLocale || _default[\"default\"][componentName || 'global'];\n    var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n    return (0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  return [componentLocale];\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "useLocaleReceiver", "_extends2", "_classCallCheck2", "_createClass2", "_inherits2", "_createSuper2", "React", "_interopRequireWildcard", "_default", "_context", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "LocaleReceiver", "_React$Component", "_super", "apply", "arguments", "getLocale", "_this$props", "props", "componentName", "defaultLocale", "locale", "antLocale", "context", "localeFromContext", "Function", "getLocaleCode", "localeCode", "exist", "render", "children", "Component", "defaultProps", "contextType", "useContext", "componentLocale", "useMemo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/locale-provider/LocaleReceiver.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nexports.useLocaleReceiver = useLocaleReceiver;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _createSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createSuper\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _default = _interopRequireDefault(require(\"./default\"));\n\nvar _context = _interopRequireDefault(require(\"./context\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar LocaleReceiver = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(LocaleReceiver, _React$Component);\n\n  var _super = (0, _createSuper2[\"default\"])(LocaleReceiver);\n\n  function LocaleReceiver() {\n    (0, _classCallCheck2[\"default\"])(this, LocaleReceiver);\n    return _super.apply(this, arguments);\n  }\n\n  (0, _createClass2[\"default\"])(LocaleReceiver, [{\n    key: \"getLocale\",\n    value: function getLocale() {\n      var _this$props = this.props,\n          componentName = _this$props.componentName,\n          defaultLocale = _this$props.defaultLocale;\n      var locale = defaultLocale || _default[\"default\"][componentName !== null && componentName !== void 0 ? componentName : 'global'];\n      var antLocale = this.context;\n      var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n      return (0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, locale instanceof Function ? locale() : locale), localeFromContext || {});\n    }\n  }, {\n    key: \"getLocaleCode\",\n    value: function getLocaleCode() {\n      var antLocale = this.context;\n      var localeCode = antLocale && antLocale.locale; // Had use LocaleProvide but didn't set locale\n\n      if (antLocale && antLocale.exist && !localeCode) {\n        return _default[\"default\"].locale;\n      }\n\n      return localeCode;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return this.props.children(this.getLocale(), this.getLocaleCode(), this.context);\n    }\n  }]);\n  return LocaleReceiver;\n}(React.Component);\n\nexports[\"default\"] = LocaleReceiver;\nLocaleReceiver.defaultProps = {\n  componentName: 'global'\n};\nLocaleReceiver.contextType = _context[\"default\"];\n\nfunction useLocaleReceiver(componentName, defaultLocale) {\n  var antLocale = React.useContext(_context[\"default\"]);\n  var componentLocale = React.useMemo(function () {\n    var locale = defaultLocale || _default[\"default\"][componentName || 'global'];\n    var localeFromContext = componentName && antLocale ? antLocale[componentName] : {};\n    return (0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  return [componentLocale];\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3BA,OAAO,CAACE,iBAAiB,GAAGA,iBAAiB;AAE7C,IAAIC,SAAS,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIQ,gBAAgB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIS,aAAa,GAAGV,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIU,UAAU,GAAGX,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIW,aAAa,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIY,KAAK,GAAGC,uBAAuB,CAACb,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIc,QAAQ,GAAGf,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAE3D,IAAIe,QAAQ,GAAGhB,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAE3D,SAASgB,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASJ,uBAAuBA,CAACQ,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIpB,OAAO,CAACoB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGzB,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC0B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAI3B,MAAM,CAAC4B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEhC,MAAM,CAACC,cAAc,CAACuB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIS,cAAc,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC5D,CAAC,CAAC,EAAE1B,UAAU,CAAC,SAAS,CAAC,EAAEyB,cAAc,EAAEC,gBAAgB,CAAC;EAE5D,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAE1B,aAAa,CAAC,SAAS,CAAC,EAAEwB,cAAc,CAAC;EAE1D,SAASA,cAAcA,CAAA,EAAG;IACxB,CAAC,CAAC,EAAE3B,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE2B,cAAc,CAAC;IACtD,OAAOE,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtC;EAEA,CAAC,CAAC,EAAE9B,aAAa,CAAC,SAAS,CAAC,EAAE0B,cAAc,EAAE,CAAC;IAC7CN,GAAG,EAAE,WAAW;IAChBxB,KAAK,EAAE,SAASmC,SAASA,CAAA,EAAG;MAC1B,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QACxBC,aAAa,GAAGF,WAAW,CAACE,aAAa;QACzCC,aAAa,GAAGH,WAAW,CAACG,aAAa;MAC7C,IAAIC,MAAM,GAAGD,aAAa,IAAI9B,QAAQ,CAAC,SAAS,CAAC,CAAC6B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,QAAQ,CAAC;MAChI,IAAIG,SAAS,GAAG,IAAI,CAACC,OAAO;MAC5B,IAAIC,iBAAiB,GAAGL,aAAa,IAAIG,SAAS,GAAGA,SAAS,CAACH,aAAa,CAAC,GAAG,CAAC,CAAC;MAClF,OAAO,CAAC,CAAC,EAAEpC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEsC,MAAM,YAAYI,QAAQ,GAAGJ,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEG,iBAAiB,IAAI,CAAC,CAAC,CAAC;IAC1I;EACF,CAAC,EAAE;IACDnB,GAAG,EAAE,eAAe;IACpBxB,KAAK,EAAE,SAAS6C,aAAaA,CAAA,EAAG;MAC9B,IAAIJ,SAAS,GAAG,IAAI,CAACC,OAAO;MAC5B,IAAII,UAAU,GAAGL,SAAS,IAAIA,SAAS,CAACD,MAAM,CAAC,CAAC;;MAEhD,IAAIC,SAAS,IAAIA,SAAS,CAACM,KAAK,IAAI,CAACD,UAAU,EAAE;QAC/C,OAAOrC,QAAQ,CAAC,SAAS,CAAC,CAAC+B,MAAM;MACnC;MAEA,OAAOM,UAAU;IACnB;EACF,CAAC,EAAE;IACDtB,GAAG,EAAE,QAAQ;IACbxB,KAAK,EAAE,SAASgD,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACX,KAAK,CAACY,QAAQ,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC,EAAE,IAAI,CAACU,aAAa,CAAC,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;IAClF;EACF,CAAC,CAAC,CAAC;EACH,OAAOZ,cAAc;AACvB,CAAC,CAACvB,KAAK,CAAC2C,SAAS,CAAC;AAElBnD,OAAO,CAAC,SAAS,CAAC,GAAG+B,cAAc;AACnCA,cAAc,CAACqB,YAAY,GAAG;EAC5Bb,aAAa,EAAE;AACjB,CAAC;AACDR,cAAc,CAACsB,WAAW,GAAG1C,QAAQ,CAAC,SAAS,CAAC;AAEhD,SAAST,iBAAiBA,CAACqC,aAAa,EAAEC,aAAa,EAAE;EACvD,IAAIE,SAAS,GAAGlC,KAAK,CAAC8C,UAAU,CAAC3C,QAAQ,CAAC,SAAS,CAAC,CAAC;EACrD,IAAI4C,eAAe,GAAG/C,KAAK,CAACgD,OAAO,CAAC,YAAY;IAC9C,IAAIf,MAAM,GAAGD,aAAa,IAAI9B,QAAQ,CAAC,SAAS,CAAC,CAAC6B,aAAa,IAAI,QAAQ,CAAC;IAC5E,IAAIK,iBAAiB,GAAGL,aAAa,IAAIG,SAAS,GAAGA,SAAS,CAACH,aAAa,CAAC,GAAG,CAAC,CAAC;IAClF,OAAO,CAAC,CAAC,EAAEpC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,OAAOsC,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEG,iBAAiB,IAAI,CAAC,CAAC,CAAC;EAC5I,CAAC,EAAE,CAACL,aAAa,EAAEC,aAAa,EAAEE,SAAS,CAAC,CAAC;EAC7C,OAAO,CAACa,eAAe,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}