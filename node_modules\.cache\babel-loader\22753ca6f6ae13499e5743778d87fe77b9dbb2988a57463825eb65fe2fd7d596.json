{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\utenteMagazziniere.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UtenteMagazziniere - operazioni sull'aggiunta utente magazzino\n*\n*/\nimport React, { useState, useEffect, Fragment, useRef } from 'react';\nimport classNames from 'classnames/bind';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dialog } from 'primereact/dialog';\nimport { Divider } from 'primereact/divider';\nimport { Form, Field } from 'react-final-form';\nimport { Password } from 'primereact/password';\nimport { <PERSON><PERSON> } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { scrollTo } from '../components/generalizzazioni/scrollToElement';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport '../css/modale.css';\nimport '../css/FormDemo.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UtenteMagazziniere = () => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState([]);\n  const [showMessage, setShowMessage] = useState(false);\n  const [openForm, setOpenForm] = useState('card d-none');\n  const [formData, setFormData] = useState({});\n  const [role, setRole] = useState('');\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function trovaRisultato() {\n      var url = 'registry/?id=' + localStorage.getItem(\"datiComodo\");\n      await APIRequest('GET', url).then(res => {\n        setResults(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, []);\n  if (results.length === 0) {\n    return null;\n  }\n  const validate = data => {\n    let errors = {};\n    if (!data.email) {\n      errors.email = Costanti.EmailObb;\n    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$/i.test(data.email)) {\n      errors.email = Costanti.EmailNoVal;\n    }\n    if (!data.password) {\n      errors.password = Costanti.PassObb;\n    }\n    if (!data.confirmPassword) {\n      errors.confirmPassword = Costanti.ConfPassObb;\n    } else if (data.confirmPassword !== data.password) {\n      errors.confirmPassword = Costanti.PassValid;\n    }\n    return errors;\n  };\n  const onSubmit = (data, form) => {\n    setFormData(data);\n    let contenuto = {\n      username: data.email,\n      password: data.password,\n      role: role,\n      idRegistry: localStorage.getItem(\"datiComodo\")\n    };\n    APIRequest('POST', 'user/', contenuto).then(res => {\n      console.log(res.data);\n      setShowMessage(true);\n      localStorage.setItem(\"datiComodo\", 0);\n      form.restart();\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Ci dispiace',\n        detail: \"Il nome utente inserito \\xE8 gi\\xE0 in uso. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  const openCloseForm = (e, key) => {\n    if (openForm === 'card d-none') {\n      setOpenForm('card');\n      setRole(key);\n    } else {\n      setOpenForm('card d-none');\n      setRole('');\n    }\n  };\n  /* Scrolling to the form element. */\n  const scrollElement = document.getElementById('createUserAccountForm');\n  scrollTo({\n    scrollElement: scrollElement,\n    block: 'start'\n  });\n  const isFormFieldValid = meta => !!(meta.touched && meta.error);\n  const getFormErrorMessage = meta => {\n    return isFormFieldValid(meta) && /*#__PURE__*/_jsxDEV(\"small\", {\n      className: \"p-error\",\n      children: meta.error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 42\n    }, this);\n  };\n  const dialogFooter = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-d-flex p-jc-center\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      label: \"OK\",\n      className: \"p-button-text\",\n      autoFocus: true,\n      onClick: () => setShowMessage(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 26\n  }, this);\n  const passwordHeader = /*#__PURE__*/_jsxDEV(\"h6\", {\n    children: Costanti.SelectPass\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 28\n  }, this);\n  const passwordFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"p-mt-2\",\n      children: Costanti.PassDevCont\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"p-pl-2 p-ml-2 p-mt-0\",\n      style: {\n        lineHeight: '1.5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Minuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Maiuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.AlmUnNum\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Alm8Car\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-demo\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-fluid p-grid\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-field p-col-12 pb-0 mb-0 p-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"list-group-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-credit-card mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 65\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.pIva\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 107\n                }, this), \": \", results.pIva]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"list-group-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-id-card mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 65\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.rSociale\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 103\n                }, this), \": \", results.firstName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 21\n        }, this), results.users.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-8 d-none d-sm-block\",\n              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                className: \"ml-3\",\n                children: [Costanti.NomeUtente, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-4 d-none d-sm-block\",\n              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                children: [Costanti.Ruolo, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"dett-aff\",\n            children: results.users.map((element, index) => {\n              // Return the element. Also pass key     \n              return /*#__PURE__*/_jsxDEV(Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-12 col-sm-8 mb-2 mb-sm-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          className: \"d-block d-sm-none\",\n                          children: [Costanti.NomeUtente, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 154,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-break\",\n                          children: element.username\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 155,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-12 col-sm-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          className: \"d-block d-sm-none\",\n                          children: [Costanti.Ruolo, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 158,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-break\",\n                          children: element.role\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 159,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 157,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 45\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 41\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-center w-100 mb-4\",\n        children: Costanti.ScegliTipoUtenza\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 border-right mb-3 mb-md-0 d-flex flex-wrap align-content-end justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: Costanti.AggUtOpMag\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"p-button mx-auto justify-content-center mb-4\",\n            onClick: (e, key) => {\n              openCloseForm(e, key = 'OP_MAG');\n              scrollTo();\n            },\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user-plus mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 176\n            }, this), \" \", Costanti.AggUser]\n          }, 'OP_MAG', true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 d-flex flex-wrap align-content-end justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: Costanti.AggUtRespMag\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"p-button mx-auto justify-content-center mb-4\",\n            onClick: (e, key) => {\n              openCloseForm(e, key = 'RESP_MAG');\n              scrollTo();\n            },\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user-plus mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 180\n            }, this), \" \", Costanti.AggUser]\n          }, 'RESP_MAG', true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      visible: showMessage,\n      onHide: () => setShowMessage(false),\n      position: \"top\",\n      footer: dialogFooter,\n      showHeader: false,\n      breakpoints: {\n        '960px': '80vw'\n      },\n      style: {\n        width: '30vw'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-check-circle\",\n          style: {\n            fontSize: '5rem',\n            color: 'var(--green-500)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          children: Costanti.RegSucc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            lineHeight: 1.5,\n            textIndent: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 25\n          }, this), \" \", Costanti.GiaReg, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: formData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 50\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 72\n          }, this), \" \", Costanti.ConEmail, \": \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: formData.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 100\n          }, this), \" .\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-d-flex p-jc-center px-0 px-md-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: openForm,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: onSubmit,\n          initialValues: {\n            email: '',\n            password: ''\n          },\n          validate: validate,\n          render: _ref => {\n            let {\n              handleSubmit\n            } = _ref;\n            return /*#__PURE__*/_jsxDEV(\"form\", {\n              id: \"createUserAccountForm\",\n              onSubmit: handleSubmit,\n              className: \"p-fluid\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-center w-100 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.CreaUtenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 68\n                }, this), \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-chevron-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 107\n                }, this), \" \", role === 'OP_MAG' ? Costanti.operatoreMagazzino : Costanti.responsabileMagazzino]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Field, {\n                name: \"email\",\n                render: _ref2 => {\n                  let {\n                    input,\n                    meta\n                  } = _ref2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-float-label p-input-icon-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"pi pi-envelope\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(InputText, _objectSpread(_objectSpread({\n                        id: \"email\"\n                      }, input), {}, {\n                        className: classNames({\n                          'p-invalid': isFormFieldValid(meta)\n                        })\n                      }), void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"email\",\n                        className: classNames({\n                          'p-error': isFormFieldValid(meta)\n                        }),\n                        children: [Costanti.NomeUtente, \"*\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 41\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 37\n                    }, this), getFormErrorMessage(meta)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 33\n                  }, this);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Field, {\n                name: \"password\",\n                render: _ref3 => {\n                  let {\n                    input,\n                    meta\n                  } = _ref3;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-float-label\",\n                      children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                        id: \"password\"\n                      }, input), {}, {\n                        toggleMask: true,\n                        className: classNames({\n                          'p-invalid': isFormFieldValid(meta)\n                        }),\n                        header: passwordHeader,\n                        footer: passwordFooter\n                      }), void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"password\",\n                        className: classNames({\n                          'p-error': isFormFieldValid(meta)\n                        }),\n                        children: \"Password*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 41\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 37\n                    }, this), getFormErrorMessage(meta)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 33\n                  }, this);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Field, {\n                name: \"confirmPassword\",\n                render: _ref4 => {\n                  let {\n                    input,\n                    meta\n                  } = _ref4;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-float-label\",\n                      children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                        id: \"confirmPassword\"\n                      }, input), {}, {\n                        className: classNames({\n                          'p-invalid': isFormFieldValid(meta)\n                        }),\n                        toggleMask: true,\n                        feedback: false\n                      }), void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 41\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"confirmPassword\",\n                        className: classNames({\n                          'p-error': isFormFieldValid(meta)\n                        }),\n                        children: [Costanti.Conferma, \" password*\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 228,\n                        columnNumber: 41\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 37\n                    }, this), getFormErrorMessage(meta)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 33\n                  }, this);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"buttonForm\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  id: \"user\",\n                  className: \"mx-0 mt-3 d-flex justify-content-center w-auto px-5\",\n                  children: [\" \", Costanti.salva, \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 33\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 25\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 9\n  }, this);\n};\n_s(UtenteMagazziniere, \"JAx/Wl6E9v+zmocpWNOC4f+UVBQ=\");\n_c = UtenteMagazziniere;\nexport default UtenteMagazziniere;\nvar _c;\n$RefreshReg$(_c, \"UtenteMagazziniere\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "useRef", "classNames", "<PERSON><PERSON>", "InputText", "Dialog", "Divider", "Form", "Field", "Password", "<PERSON><PERSON>", "APIRequest", "Toast", "scrollTo", "stopLoading", "jsxDEV", "_jsxDEV", "_Fragment", "UtenteMagazziniere", "_s", "results", "setResults", "showMessage", "setShowMessage", "openForm", "setOpenForm", "formData", "setFormData", "role", "setRole", "toast", "trovaRisultato", "url", "localStorage", "getItem", "then", "res", "data", "catch", "e", "console", "log", "length", "validate", "errors", "email", "<PERSON>ail<PERSON>bb", "test", "EmailNoVal", "password", "PassObb", "confirmPassword", "ConfPassObb", "PassValid", "onSubmit", "form", "contenuto", "username", "idRegistry", "setItem", "restart", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "openCloseForm", "key", "scrollElement", "document", "getElementById", "block", "isFormFieldValid", "meta", "touched", "error", "getFormErrorMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dialogFooter", "label", "autoFocus", "onClick", "passwordHeader", "SelectPass", "passwordFooter", "PassDevCont", "style", "lineHeight", "Minus<PERSON>", "<PERSON><PERSON><PERSON>", "AlmUnNum", "Alm8Car", "ref", "pIva", "rSociale", "firstName", "users", "NomeUtente", "<PERSON><PERSON><PERSON>", "map", "element", "index", "ScegliTipoUtenza", "AggUtOpMag", "AggUser", "AggUtRespMag", "visible", "onHide", "position", "footer", "showHeader", "breakpoints", "width", "fontSize", "color", "RegSucc", "textIndent", "GiaReg", "name", "ConEmail", "initialValues", "render", "_ref", "handleSubmit", "id", "CreaUtenza", "operatoreMagazzino", "responsabileM<PERSON><PERSON><PERSON>", "_ref2", "input", "_objectSpread", "htmlFor", "_ref3", "toggleMask", "header", "_ref4", "feedback", "Conferma", "type", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/utenteMagazziniere.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UtenteMagazziniere - operazioni sull'aggiunta utente magazzino\n*\n*/\nimport React, { useState, useEffect, Fragment, useRef } from 'react';\nimport classNames from 'classnames/bind';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dialog } from 'primereact/dialog';\nimport { Divider } from 'primereact/divider';\nimport { Form, Field } from 'react-final-form';\nimport { Password } from 'primereact/password';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { scrollTo } from '../components/generalizzazioni/scrollToElement';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport '../css/modale.css';\nimport '../css/FormDemo.css';\n\nconst UtenteMagazziniere = () => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState([]);\n    const [showMessage, setShowMessage] = useState(false);\n    const [openForm, setOpenForm] = useState('card d-none')\n    const [formData, setFormData] = useState({});\n    const [role, setRole] = useState('');\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function trovaRisultato() {\n            var url = 'registry/?id=' + localStorage.getItem(\"datiComodo\")\n            await APIRequest('GET', url)\n                .then(res => {\n                    setResults(res.data);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, []);\n    if (results.length === 0) {\n        return null;\n    }\n    const validate = (data) => {\n        let errors = {};\n        if (!data.email) {\n            errors.email = Costanti.EmailObb;\n        }\n        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$/i.test(data.email)) {\n            errors.email = Costanti.EmailNoVal;\n        }\n        if (!data.password) {\n            errors.password = Costanti.PassObb;\n        }\n        if (!data.confirmPassword) {\n            errors.confirmPassword = Costanti.ConfPassObb;\n        }\n        else if (data.confirmPassword !== data.password) {\n            errors.confirmPassword = Costanti.PassValid;\n        }\n        return errors;\n    };\n    const onSubmit = (data, form) => {\n        setFormData(data);\n        let contenuto = {\n            username: data.email,\n            password: data.password,\n            role: role,\n            idRegistry: localStorage.getItem(\"datiComodo\")\n        }\n        APIRequest('POST', 'user/', contenuto)\n            .then(res => {\n                console.log(res.data);\n                setShowMessage(true);\n                localStorage.setItem(\"datiComodo\", 0);\n                form.restart();\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch(e => {\n                console.log(e);\n                toast.current.show({ severity: 'error', summary: 'Ci dispiace', detail: `Il nome utente inserito è già in uso. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n    const openCloseForm = (e, key) => {\n        if (openForm === 'card d-none') {\n            setOpenForm('card')\n            setRole(key)\n        } else {\n            setOpenForm('card d-none')\n            setRole('')\n        }\n    }\n    /* Scrolling to the form element. */\n    const scrollElement = document.getElementById('createUserAccountForm');\n    scrollTo({ scrollElement: scrollElement, block: 'start' });\n\n    const isFormFieldValid = (meta) => !!(meta.touched && meta.error);\n    const getFormErrorMessage = (meta) => {\n        return isFormFieldValid(meta) && <small className=\"p-error\">{meta.error}</small>;\n    };\n    const dialogFooter = <div className=\"p-d-flex p-jc-center\"><Button label=\"OK\" className=\"p-button-text\" autoFocus onClick={() => setShowMessage(false)} /></div>;\n    const passwordHeader = <h6>{Costanti.SelectPass}</h6>;\n    const passwordFooter = (\n        <React.Fragment>\n            <Divider />\n            <p className=\"p-mt-2\">{Costanti.PassDevCont}</p>\n            <ul className=\"p-pl-2 p-ml-2 p-mt-0\" style={{ lineHeight: '1.5' }}>\n                <li>{Costanti.Minuscola}</li>\n                <li>{Costanti.Maiuscola}</li>\n                <li>{Costanti.AlmUnNum}</li>\n                <li>{Costanti.Alm8Car}</li>\n            </ul>\n        </React.Fragment>\n    );\n    return (\n        <div className=\"form-demo\">\n            <Toast ref={toast} />\n            <div className=\"p-fluid p-grid\">\n                <div className=\"p-field p-col-12 pb-0 mb-0 p-md-4\">\n                    <div className='row'>\n                        <div className=\"col-12 mb-0\">\n                            <ul className=\"list-group\">\n                                <li className=\"list-group-item\"><i className=\"pi pi-credit-card mr-3\"></i><strong>{Costanti.pIva}</strong>: {results.pIva}</li>\n                                <li className=\"list-group-item\"><i className=\"pi pi-id-card mr-3\"></i><strong>{Costanti.rSociale}</strong>: {results.firstName}</li>\n                            </ul>\n                            <hr className='my-4' />\n                        </div>\n                    </div>\n                    {results.users.length > 0 &&\n                        <>\n                            <div className=\"row\">\n                                <div className=\"col-8 d-none d-sm-block\">\n                                    <b className=\"ml-3\">{Costanti.NomeUtente}:</b>\n                                </div>\n                                <div className=\"col-4 d-none d-sm-block\">\n                                    <b>{Costanti.Ruolo}:</b>\n                                </div>\n                            </div>\n                            <ul className=\"dett-aff\" >\n                                {results.users.map((element, index) => {\n                                    // Return the element. Also pass key     \n                                    return (\n                                        <Fragment key={index}>\n                                            <div>\n                                                <li>\n                                                    <div className=\"row\">\n                                                        <div className=\"col-12 col-sm-8 mb-2 mb-sm-0\">\n                                                            <strong className='d-block d-sm-none'>{Costanti.NomeUtente}:</strong>\n                                                            <span className='text-break'>{element.username}</span>\n                                                        </div>\n                                                        <div className=\"col-12 col-sm-4\">\n                                                            <strong className='d-block d-sm-none'>{Costanti.Ruolo}:</strong>\n                                                            <span className='text-break'>{element.role}</span>\n                                                        </div>\n                                                    </div>\n                                                </li>\n                                            </div>\n                                        </Fragment>\n                                    )\n                                })\n                                }\n                            </ul>\n                        </>\n                    }\n                </div>\n            </div>\n            <div className=\"col-12\">\n                <h4 className='text-center w-100 mb-4'>{Costanti.ScegliTipoUtenza}</h4>\n                <hr />\n                <div className=\"row\">\n                    <div className=\"col-12 col-md-6 border-right mb-3 mb-md-0 d-flex flex-wrap align-content-end justify-content-center\">\n                        <div className=\"d-flex justify-content-center text-center\">\n                            <p>{Costanti.AggUtOpMag}</p>\n                        </div>\n                        <Button className=\"p-button mx-auto justify-content-center mb-4\" key='OP_MAG' onClick={(e, key) => { openCloseForm(e, key = 'OP_MAG'); scrollTo() }} > <i className=\"pi pi-user-plus mr-2\"></i> {Costanti.AggUser}</Button>\n                    </div>\n                    <div className=\"col-12 col-md-6 d-flex flex-wrap align-content-end justify-content-center\">\n                        <div className=\"d-flex justify-content-center text-center\">\n                            <p>{Costanti.AggUtRespMag}</p>\n                        </div>\n                        <Button className=\"p-button mx-auto justify-content-center mb-4\" key='RESP_MAG' onClick={(e, key) => { openCloseForm(e, key = 'RESP_MAG'); scrollTo() }} > <i className=\"pi pi-user-plus mr-2\"></i> {Costanti.AggUser}</Button>\n                    </div>\n                </div>\n            </div>\n            <Dialog visible={showMessage} onHide={() => setShowMessage(false)} position=\"top\" footer={dialogFooter} showHeader={false} breakpoints={{ '960px': '80vw' }} style={{ width: '30vw' }}>\n                <div className=\"p-d-flex p-ai-center p-dir-col p-pt-6 p-px-3\">\n                    <i className=\"pi pi-check-circle\" style={{ fontSize: '5rem', color: 'var(--green-500)' }}></i>\n                    <h5>{Costanti.RegSucc}</h5>\n                    <p style={{ lineHeight: 1.5, textIndent: '1rem' }}>\n                        <br /> {Costanti.GiaReg} <b>{formData.name}</b><br /> {Costanti.ConEmail}: <b>{formData.email}</b> .\n                    </p>\n                </div>\n            </Dialog>\n            <div className=\"p-d-flex p-jc-center px-0 px-md-3\">\n                <div className={openForm}>\n                    <Form onSubmit={onSubmit} initialValues={{ email: '', password: '' }} validate={validate} render={({ handleSubmit }) => (\n                        <form id=\"createUserAccountForm\" onSubmit={handleSubmit} className=\"p-fluid\">\n                            <h5 className='text-center w-100 mb-4'><strong>{Costanti.CreaUtenza}</strong> <i className=\"pi pi-chevron-right\"></i> {role === 'OP_MAG' ? Costanti.operatoreMagazzino : Costanti.responsabileMagazzino}</h5>\n                            <Field name=\"email\" render={({ input, meta }) => (\n                                <div className=\"p-field\">\n                                    <span className=\"p-float-label p-input-icon-right\">\n                                        <i className=\"pi pi-envelope\" />\n                                        <InputText id=\"email\" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />\n                                        <label htmlFor=\"email\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.NomeUtente}*</label>\n                                    </span>\n                                    {getFormErrorMessage(meta)}\n                                </div>\n                            )} />\n                            <Field name=\"password\" render={({ input, meta }) => (\n                                <div className=\"p-field\">\n                                    <span className=\"p-float-label\">\n                                        <Password id=\"password\" {...input} toggleMask className={classNames({ 'p-invalid': isFormFieldValid(meta) })} header={passwordHeader} footer={passwordFooter} />\n                                        <label htmlFor=\"password\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>Password*</label>\n                                    </span>\n                                    {getFormErrorMessage(meta)}\n                                </div>\n                            )} />\n                            <Field name=\"confirmPassword\" render={({ input, meta }) => (\n                                <div className=\"p-field\">\n                                    <span className=\"p-float-label\">\n                                        <Password id=\"confirmPassword\" {...input} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} toggleMask feedback={false} />\n                                        <label htmlFor=\"confirmPassword\" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Conferma} password*</label>\n                                    </span>\n                                    {getFormErrorMessage(meta)}\n                                </div>\n                            )} />\n                            <div className=\"buttonForm\">\n                                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                                <Button type=\"submit\" id=\"user\" className=\"mx-0 mt-3 d-flex justify-content-center w-auto px-5\" > {Costanti.salva} </Button>\n                            </div>\n                        </form>\n                    )} />\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default UtenteMagazziniere;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACpE,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,IAAI,EAAEC,KAAK,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,gDAAgD;AACzE,SAASC,WAAW,QAAQ,4CAA4C;AACxE,OAAO,mBAAmB;AAC1B,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAhB,QAAA,IAAAiB,SAAA;AAE7B,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,aAAa,CAAC;EACvD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAMgC,KAAK,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAF,SAAS,CAAC,MAAM;IACZ,eAAegC,cAAcA,CAAA,EAAG;MAC5B,IAAIC,GAAG,GAAG,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC9D,MAAMvB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBG,IAAI,CAACC,GAAG,IAAI;QACTf,UAAU,CAACe,GAAG,CAACC,IAAI,CAAC;MACxB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACNzB,WAAW,CAAC,CAAC;IACjB;IACAiB,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIX,OAAO,CAACsB,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACf;EACA,MAAMC,QAAQ,GAAIN,IAAI,IAAK;IACvB,IAAIO,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,CAACP,IAAI,CAACQ,KAAK,EAAE;MACbD,MAAM,CAACC,KAAK,GAAGnC,QAAQ,CAACoC,QAAQ;IACpC,CAAC,MACI,IAAI,CAAC,2CAA2C,CAACC,IAAI,CAACV,IAAI,CAACQ,KAAK,CAAC,EAAE;MACpED,MAAM,CAACC,KAAK,GAAGnC,QAAQ,CAACsC,UAAU;IACtC;IACA,IAAI,CAACX,IAAI,CAACY,QAAQ,EAAE;MAChBL,MAAM,CAACK,QAAQ,GAAGvC,QAAQ,CAACwC,OAAO;IACtC;IACA,IAAI,CAACb,IAAI,CAACc,eAAe,EAAE;MACvBP,MAAM,CAACO,eAAe,GAAGzC,QAAQ,CAAC0C,WAAW;IACjD,CAAC,MACI,IAAIf,IAAI,CAACc,eAAe,KAAKd,IAAI,CAACY,QAAQ,EAAE;MAC7CL,MAAM,CAACO,eAAe,GAAGzC,QAAQ,CAAC2C,SAAS;IAC/C;IACA,OAAOT,MAAM;EACjB,CAAC;EACD,MAAMU,QAAQ,GAAGA,CAACjB,IAAI,EAAEkB,IAAI,KAAK;IAC7B5B,WAAW,CAACU,IAAI,CAAC;IACjB,IAAImB,SAAS,GAAG;MACZC,QAAQ,EAAEpB,IAAI,CAACQ,KAAK;MACpBI,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;MACvBrB,IAAI,EAAEA,IAAI;MACV8B,UAAU,EAAEzB,YAAY,CAACC,OAAO,CAAC,YAAY;IACjD,CAAC;IACDvB,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE6C,SAAS,CAAC,CACjCrB,IAAI,CAACC,GAAG,IAAI;MACTI,OAAO,CAACC,GAAG,CAACL,GAAG,CAACC,IAAI,CAAC;MACrBd,cAAc,CAAC,IAAI,CAAC;MACpBU,YAAY,CAAC0B,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MACrCJ,IAAI,CAACK,OAAO,CAAC,CAAC;MACdC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC1B,KAAK,CAACC,CAAC,IAAI;MAAA,IAAA0B,WAAA,EAAAC,YAAA;MACV1B,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdT,KAAK,CAACqC,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,aAAa;QAAEC,MAAM,mEAAAC,MAAA,CAA6D,EAAAP,WAAA,GAAA1B,CAAC,CAACkC,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAY5B,IAAI,MAAKqC,SAAS,IAAAR,YAAA,GAAG3B,CAAC,CAACkC,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAY7B,IAAI,GAAGE,CAAC,CAACoC,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACrN,CAAC,CAAC;EACV,CAAC;EACD,MAAMC,aAAa,GAAGA,CAACtC,CAAC,EAAEuC,GAAG,KAAK;IAC9B,IAAItD,QAAQ,KAAK,aAAa,EAAE;MAC5BC,WAAW,CAAC,MAAM,CAAC;MACnBI,OAAO,CAACiD,GAAG,CAAC;IAChB,CAAC,MAAM;MACHrD,WAAW,CAAC,aAAa,CAAC;MAC1BI,OAAO,CAAC,EAAE,CAAC;IACf;EACJ,CAAC;EACD;EACA,MAAMkD,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;EACtEpE,QAAQ,CAAC;IAAEkE,aAAa,EAAEA,aAAa;IAAEG,KAAK,EAAE;EAAQ,CAAC,CAAC;EAE1D,MAAMC,gBAAgB,GAAIC,IAAI,IAAK,CAAC,EAAEA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACE,KAAK,CAAC;EACjE,MAAMC,mBAAmB,GAAIH,IAAI,IAAK;IAClC,OAAOD,gBAAgB,CAACC,IAAI,CAAC,iBAAIpE,OAAA;MAAOwE,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAEL,IAAI,CAACE;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACpF,CAAC;EACD,MAAMC,YAAY,gBAAG9E,OAAA;IAAKwE,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eAACzE,OAAA,CAACb,MAAM;MAAC4F,KAAK,EAAC,IAAI;MAACP,SAAS,EAAC,eAAe;MAACQ,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAM1E,cAAc,CAAC,KAAK;IAAE;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAChK,MAAMK,cAAc,gBAAGlF,OAAA;IAAAyE,QAAA,EAAK/E,QAAQ,CAACyF;EAAU;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrD,MAAMO,cAAc,gBAChBpF,OAAA,CAACnB,KAAK,CAACG,QAAQ;IAAAyF,QAAA,gBACXzE,OAAA,CAACV,OAAO;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX7E,OAAA;MAAGwE,SAAS,EAAC,QAAQ;MAAAC,QAAA,EAAE/E,QAAQ,CAAC2F;IAAW;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChD7E,OAAA;MAAIwE,SAAS,EAAC,sBAAsB;MAACc,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAd,QAAA,gBAC9DzE,OAAA;QAAAyE,QAAA,EAAK/E,QAAQ,CAAC8F;MAAS;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7B7E,OAAA;QAAAyE,QAAA,EAAK/E,QAAQ,CAAC+F;MAAS;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7B7E,OAAA;QAAAyE,QAAA,EAAK/E,QAAQ,CAACgG;MAAQ;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5B7E,OAAA;QAAAyE,QAAA,EAAK/E,QAAQ,CAACiG;MAAO;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CACnB;EACD,oBACI7E,OAAA;IAAKwE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBzE,OAAA,CAACJ,KAAK;MAACgG,GAAG,EAAE9E;IAAM;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB7E,OAAA;MAAKwE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BzE,OAAA;QAAKwE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CzE,OAAA;UAAKwE,SAAS,EAAC,KAAK;UAAAC,QAAA,eAChBzE,OAAA;YAAKwE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBzE,OAAA;cAAIwE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACtBzE,OAAA;gBAAIwE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAACzE,OAAA;kBAAGwE,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA7E,OAAA;kBAAAyE,QAAA,EAAS/E,QAAQ,CAACmG;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAACzE,OAAO,CAACyF,IAAI;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/H7E,OAAA;gBAAIwE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAACzE,OAAA;kBAAGwE,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA7E,OAAA;kBAAAyE,QAAA,EAAS/E,QAAQ,CAACoG;gBAAQ;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAACzE,OAAO,CAAC2F,SAAS;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpI,CAAC,eACL7E,OAAA;cAAIwE,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLzE,OAAO,CAAC4F,KAAK,CAACtE,MAAM,GAAG,CAAC,iBACrB1B,OAAA,CAAAC,SAAA;UAAAwE,QAAA,gBACIzE,OAAA;YAAKwE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAChBzE,OAAA;cAAKwE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACpCzE,OAAA;gBAAGwE,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAE/E,QAAQ,CAACuG,UAAU,EAAC,GAAC;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACpCzE,OAAA;gBAAAyE,QAAA,GAAI/E,QAAQ,CAACwG,KAAK,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN7E,OAAA;YAAIwE,SAAS,EAAC,UAAU;YAAAC,QAAA,EACnBrE,OAAO,CAAC4F,KAAK,CAACG,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;cACnC;cACA,oBACIrG,OAAA,CAAChB,QAAQ;gBAAAyF,QAAA,eACLzE,OAAA;kBAAAyE,QAAA,eACIzE,OAAA;oBAAAyE,QAAA,eACIzE,OAAA;sBAAKwE,SAAS,EAAC,KAAK;sBAAAC,QAAA,gBAChBzE,OAAA;wBAAKwE,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBACzCzE,OAAA;0BAAQwE,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,GAAE/E,QAAQ,CAACuG,UAAU,EAAC,GAAC;wBAAA;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACrE7E,OAAA;0BAAMwE,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAE2B,OAAO,CAAC3D;wBAAQ;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACN7E,OAAA;wBAAKwE,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,gBAC5BzE,OAAA;0BAAQwE,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,GAAE/E,QAAQ,CAACwG,KAAK,EAAC,GAAC;wBAAA;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAChE7E,OAAA;0BAAMwE,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAE2B,OAAO,CAACxF;wBAAI;0BAAA8D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC,GAdKwB,KAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CAAC;YAEnB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC;QAAA,eACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN7E,OAAA;MAAKwE,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACnBzE,OAAA;QAAIwE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAE/E,QAAQ,CAAC4G;MAAgB;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvE7E,OAAA;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN7E,OAAA;QAAKwE,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAChBzE,OAAA;UAAKwE,SAAS,EAAC,qGAAqG;UAAAC,QAAA,gBAChHzE,OAAA;YAAKwE,SAAS,EAAC,2CAA2C;YAAAC,QAAA,eACtDzE,OAAA;cAAAyE,QAAA,EAAI/E,QAAQ,CAAC6G;YAAU;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACN7E,OAAA,CAACb,MAAM;YAACqF,SAAS,EAAC,8CAA8C;YAAcS,OAAO,EAAEA,CAAC1D,CAAC,EAAEuC,GAAG,KAAK;cAAED,aAAa,CAACtC,CAAC,EAAEuC,GAAG,GAAG,QAAQ,CAAC;cAAEjE,QAAQ,CAAC,CAAC;YAAC,CAAE;YAAA4E,QAAA,GAAE,GAAC,eAAAzE,OAAA;cAAGwE,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAAC8G,OAAO;UAAA,GAA5I,QAAQ;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA6I,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1N,CAAC,eACN7E,OAAA;UAAKwE,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACtFzE,OAAA;YAAKwE,SAAS,EAAC,2CAA2C;YAAAC,QAAA,eACtDzE,OAAA;cAAAyE,QAAA,EAAI/E,QAAQ,CAAC+G;YAAY;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN7E,OAAA,CAACb,MAAM;YAACqF,SAAS,EAAC,8CAA8C;YAAgBS,OAAO,EAAEA,CAAC1D,CAAC,EAAEuC,GAAG,KAAK;cAAED,aAAa,CAACtC,CAAC,EAAEuC,GAAG,GAAG,UAAU,CAAC;cAAEjE,QAAQ,CAAC,CAAC;YAAC,CAAE;YAAA4E,QAAA,GAAE,GAAC,eAAAzE,OAAA;cAAGwE,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,EAACnF,QAAQ,CAAC8G,OAAO;UAAA,GAAhJ,UAAU;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA+I,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9N,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN7E,OAAA,CAACX,MAAM;MAACqH,OAAO,EAAEpG,WAAY;MAACqG,MAAM,EAAEA,CAAA,KAAMpG,cAAc,CAAC,KAAK,CAAE;MAACqG,QAAQ,EAAC,KAAK;MAACC,MAAM,EAAE/B,YAAa;MAACgC,UAAU,EAAE,KAAM;MAACC,WAAW,EAAE;QAAE,OAAO,EAAE;MAAO,CAAE;MAACzB,KAAK,EAAE;QAAE0B,KAAK,EAAE;MAAO,CAAE;MAAAvC,QAAA,eAClLzE,OAAA;QAAKwE,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBACzDzE,OAAA;UAAGwE,SAAS,EAAC,oBAAoB;UAACc,KAAK,EAAE;YAAE2B,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAmB;QAAE;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9F7E,OAAA;UAAAyE,QAAA,EAAK/E,QAAQ,CAACyH;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3B7E,OAAA;UAAGsF,KAAK,EAAE;YAAEC,UAAU,EAAE,GAAG;YAAE6B,UAAU,EAAE;UAAO,CAAE;UAAA3C,QAAA,gBAC9CzE,OAAA;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,KAAC,EAACnF,QAAQ,CAAC2H,MAAM,EAAC,GAAC,eAAArH,OAAA;YAAAyE,QAAA,EAAI/D,QAAQ,CAAC4G;UAAI;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAAA7E,OAAA;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,KAAC,EAACnF,QAAQ,CAAC6H,QAAQ,EAAC,IAAE,eAAAvH,OAAA;YAAAyE,QAAA,EAAI/D,QAAQ,CAACmB;UAAK;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,MACtG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACT7E,OAAA;MAAKwE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC9CzE,OAAA;QAAKwE,SAAS,EAAEhE,QAAS;QAAAiE,QAAA,eACrBzE,OAAA,CAACT,IAAI;UAAC+C,QAAQ,EAAEA,QAAS;UAACkF,aAAa,EAAE;YAAE3F,KAAK,EAAE,EAAE;YAAEI,QAAQ,EAAE;UAAG,CAAE;UAACN,QAAQ,EAAEA,QAAS;UAAC8F,MAAM,EAAEC,IAAA;YAAA,IAAC;cAAEC;YAAa,CAAC,GAAAD,IAAA;YAAA,oBAC/G1H,OAAA;cAAM4H,EAAE,EAAC,uBAAuB;cAACtF,QAAQ,EAAEqF,YAAa;cAACnD,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACxEzE,OAAA;gBAAIwE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBAACzE,OAAA;kBAAAyE,QAAA,EAAS/E,QAAQ,CAACmI;gBAAU;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,KAAC,eAAA7E,OAAA;kBAAGwE,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAACjE,IAAI,KAAK,QAAQ,GAAGlB,QAAQ,CAACoI,kBAAkB,GAAGpI,QAAQ,CAACqI,qBAAqB;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7M7E,OAAA,CAACR,KAAK;gBAAC8H,IAAI,EAAC,OAAO;gBAACG,MAAM,EAAEO,KAAA;kBAAA,IAAC;oBAAEC,KAAK;oBAAE7D;kBAAK,CAAC,GAAA4D,KAAA;kBAAA,oBACxChI,OAAA;oBAAKwE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACpBzE,OAAA;sBAAMwE,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC9CzE,OAAA;wBAAGwE,SAAS,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChC7E,OAAA,CAACZ,SAAS,EAAA8I,aAAA,CAAAA,aAAA;wBAACN,EAAE,EAAC;sBAAO,GAAKK,KAAK;wBAAEzD,SAAS,EAAEtF,UAAU,CAAC;0BAAE,WAAW,EAAEiF,gBAAgB,CAACC,IAAI;wBAAE,CAAC;sBAAE;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnG7E,OAAA;wBAAOmI,OAAO,EAAC,OAAO;wBAAC3D,SAAS,EAAEtF,UAAU,CAAC;0BAAE,SAAS,EAAEiF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAAK,QAAA,GAAE/E,QAAQ,CAACuG,UAAU,EAAC,GAAC;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACL7E,OAAA,CAACR,KAAK;gBAAC8H,IAAI,EAAC,UAAU;gBAACG,MAAM,EAAEW,KAAA;kBAAA,IAAC;oBAAEH,KAAK;oBAAE7D;kBAAK,CAAC,GAAAgE,KAAA;kBAAA,oBAC3CpI,OAAA;oBAAKwE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACpBzE,OAAA;sBAAMwE,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC3BzE,OAAA,CAACP,QAAQ,EAAAyI,aAAA,CAAAA,aAAA;wBAACN,EAAE,EAAC;sBAAU,GAAKK,KAAK;wBAAEI,UAAU;wBAAC7D,SAAS,EAAEtF,UAAU,CAAC;0BAAE,WAAW,EAAEiF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAACkE,MAAM,EAAEpD,cAAe;wBAAC2B,MAAM,EAAEzB;sBAAe;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChK7E,OAAA;wBAAOmI,OAAO,EAAC,UAAU;wBAAC3D,SAAS,EAAEtF,UAAU,CAAC;0BAAE,SAAS,EAAEiF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAAK,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvG,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACL7E,OAAA,CAACR,KAAK;gBAAC8H,IAAI,EAAC,iBAAiB;gBAACG,MAAM,EAAEc,KAAA;kBAAA,IAAC;oBAAEN,KAAK;oBAAE7D;kBAAK,CAAC,GAAAmE,KAAA;kBAAA,oBAClDvI,OAAA;oBAAKwE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACpBzE,OAAA;sBAAMwE,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC3BzE,OAAA,CAACP,QAAQ,EAAAyI,aAAA,CAAAA,aAAA;wBAACN,EAAE,EAAC;sBAAiB,GAAKK,KAAK;wBAAEzD,SAAS,EAAEtF,UAAU,CAAC;0BAAE,WAAW,EAAEiF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAACiE,UAAU;wBAACG,QAAQ,EAAE;sBAAM;wBAAA9D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxI7E,OAAA;wBAAOmI,OAAO,EAAC,iBAAiB;wBAAC3D,SAAS,EAAEtF,UAAU,CAAC;0BAAE,SAAS,EAAEiF,gBAAgB,CAACC,IAAI;wBAAE,CAAC,CAAE;wBAAAK,QAAA,GAAE/E,QAAQ,CAAC+I,QAAQ,EAAC,YAAU;sBAAA;wBAAA/D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClI,CAAC,EACNN,mBAAmB,CAACH,IAAI,CAAC;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACL7E,OAAA;gBAAKwE,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAEvBzE,OAAA,CAACb,MAAM;kBAACuJ,IAAI,EAAC,QAAQ;kBAACd,EAAE,EAAC,MAAM;kBAACpD,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,GAAE,GAAC,EAAC/E,QAAQ,CAACiJ,KAAK,EAAC,GAAC;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA1E,EAAA,CA3NKD,kBAAkB;AAAA0I,EAAA,GAAlB1I,kBAAkB;AA6NxB,eAAeA,kBAAkB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}