{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport RcTable, { Summary } from 'rc-table';\nimport { INTERNAL_HOOKS } from \"rc-table/es/Table\";\nimport { convertChildrenToColumns } from \"rc-table/es/hooks/useColumns\";\nimport Spin from '../spin';\nimport Pagination from '../pagination';\nimport { ConfigContext } from '../config-provider/context';\nimport usePagination, { DEFAULT_PAGE_SIZE, getPaginationParam } from './hooks/usePagination';\nimport useLazyKVMap from './hooks/useLazyKVMap';\nimport useSelection, { SELECTION_ALL, SELECTION_COLUMN, SELECTION_INVERT, SELECTION_NONE } from './hooks/useSelection';\nimport useSorter, { getSortData } from './hooks/useSorter';\nimport useFilter, { getFilterData } from './hooks/useFilter';\nimport useTitleColumns from './hooks/useTitleColumns';\nimport renderExpandIcon from './ExpandIcon';\nimport scrollTo from '../_util/scrollTo';\nimport defaultLocale from '../locale/en_US';\nimport SizeContext from '../config-provider/SizeContext';\nimport Column from './Column';\nimport ColumnGroup from './ColumnGroup';\nimport devWarning from '../_util/devWarning';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nvar EMPTY_LIST = [];\nfunction InternalTable(props, ref) {\n  var _classNames3;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    customizeSize = props.size,\n    bordered = props.bordered,\n    customizeDropdownPrefixCls = props.dropdownPrefixCls,\n    dataSource = props.dataSource,\n    pagination = props.pagination,\n    rowSelection = props.rowSelection,\n    rowKey = props.rowKey,\n    rowClassName = props.rowClassName,\n    columns = props.columns,\n    children = props.children,\n    legacyChildrenColumnName = props.childrenColumnName,\n    onChange = props.onChange,\n    getPopupContainer = props.getPopupContainer,\n    loading = props.loading,\n    expandIcon = props.expandIcon,\n    expandable = props.expandable,\n    expandedRowRender = props.expandedRowRender,\n    expandIconColumnIndex = props.expandIconColumnIndex,\n    indentSize = props.indentSize,\n    scroll = props.scroll,\n    sortDirections = props.sortDirections,\n    locale = props.locale,\n    _props$showSorterTool = props.showSorterTooltip,\n    showSorterTooltip = _props$showSorterTool === void 0 ? true : _props$showSorterTool;\n  devWarning(!(typeof rowKey === 'function' && rowKey.length > 1), 'Table', '`index` parameter of `rowKey` function is deprecated. There is no guarantee that it will work as expected.');\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]);\n  var needResponsive = React.useMemo(function () {\n    return baseColumns.some(function (col) {\n      return col.responsive;\n    });\n  }, [baseColumns]);\n  var screens = useBreakpoint(needResponsive);\n  var mergedColumns = React.useMemo(function () {\n    var matched = new Set(Object.keys(screens).filter(function (m) {\n      return screens[m];\n    }));\n    return baseColumns.filter(function (c) {\n      return !c.responsive || c.responsive.some(function (r) {\n        return matched.has(r);\n      });\n    });\n  }, [baseColumns, screens]);\n  var tableProps = omit(props, ['className', 'style', 'columns']);\n  var size = React.useContext(SizeContext);\n  var _React$useContext = React.useContext(ConfigContext),\n    _React$useContext$loc = _React$useContext.locale,\n    contextLocale = _React$useContext$loc === void 0 ? defaultLocale : _React$useContext$loc,\n    renderEmpty = _React$useContext.renderEmpty,\n    direction = _React$useContext.direction;\n  var mergedSize = customizeSize || size;\n  var tableLocale = _extends(_extends({}, contextLocale.Table), locale);\n  var rawData = dataSource || EMPTY_LIST;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var prefixCls = getPrefixCls('table', customizePrefixCls);\n  var dropdownPrefixCls = getPrefixCls('dropdown', customizeDropdownPrefixCls);\n  var mergedExpandable = _extends({\n    childrenColumnName: legacyChildrenColumnName,\n    expandIconColumnIndex: expandIconColumnIndex\n  }, expandable);\n  var _mergedExpandable$chi = mergedExpandable.childrenColumnName,\n    childrenColumnName = _mergedExpandable$chi === void 0 ? 'children' : _mergedExpandable$chi;\n  var expandType = React.useMemo(function () {\n    if (rawData.some(function (item) {\n      return item === null || item === void 0 ? void 0 : item[childrenColumnName];\n    })) {\n      return 'nest';\n    }\n    if (expandedRowRender || expandable && expandable.expandedRowRender) {\n      return 'row';\n    }\n    return null;\n  }, [rawData]);\n  var internalRefs = {\n    body: React.useRef()\n  }; // ============================ RowKey ============================\n\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      return record === null || record === void 0 ? void 0 : record[rowKey];\n    };\n  }, [rowKey]);\n  var _useLazyKVMap = useLazyKVMap(rawData, childrenColumnName, getRowKey),\n    _useLazyKVMap2 = _slicedToArray(_useLazyKVMap, 1),\n    getRecordByKey = _useLazyKVMap2[0]; // ============================ Events =============================\n\n  var changeEventInfo = {};\n  var triggerOnChange = function triggerOnChange(info, action) {\n    var reset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var changeInfo = _extends(_extends({}, changeEventInfo), info);\n    if (reset) {\n      changeEventInfo.resetPagination(); // Reset event param\n\n      if (changeInfo.pagination.current) {\n        changeInfo.pagination.current = 1;\n      } // Trigger pagination events\n\n      if (pagination && pagination.onChange) {\n        pagination.onChange(1, changeInfo.pagination.pageSize);\n      }\n    }\n    if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {\n      scrollTo(0, {\n        getContainer: function getContainer() {\n          return internalRefs.body.current;\n        }\n      });\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo.pagination, changeInfo.filters, changeInfo.sorter, {\n      currentDataSource: getFilterData(getSortData(rawData, changeInfo.sorterStates, childrenColumnName), changeInfo.filterStates),\n      action: action\n    });\n  };\n  /**\n   * Controlled state in `columns` is not a good idea that makes too many code (1000+ line?) to read\n   * state out and then put it back to title render. Move these code into `hooks` but still too\n   * complex. We should provides Table props like `sorter` & `filter` to handle control in next big version.\n   */\n  // ============================ Sorter =============================\n\n  var onSorterChange = function onSorterChange(sorter, sorterStates) {\n    triggerOnChange({\n      sorter: sorter,\n      sorterStates: sorterStates\n    }, 'sort', false);\n  };\n  var _useSorter = useSorter({\n      prefixCls: prefixCls,\n      mergedColumns: mergedColumns,\n      onSorterChange: onSorterChange,\n      sortDirections: sortDirections || ['ascend', 'descend'],\n      tableLocale: tableLocale,\n      showSorterTooltip: showSorterTooltip\n    }),\n    _useSorter2 = _slicedToArray(_useSorter, 4),\n    transformSorterColumns = _useSorter2[0],\n    sortStates = _useSorter2[1],\n    sorterTitleProps = _useSorter2[2],\n    getSorters = _useSorter2[3];\n  var sortedData = React.useMemo(function () {\n    return getSortData(rawData, sortStates, childrenColumnName);\n  }, [rawData, sortStates]);\n  changeEventInfo.sorter = getSorters();\n  changeEventInfo.sorterStates = sortStates; // ============================ Filter ============================\n\n  var onFilterChange = function onFilterChange(filters, filterStates) {\n    triggerOnChange({\n      filters: filters,\n      filterStates: filterStates\n    }, 'filter', true);\n  };\n  var _useFilter = useFilter({\n      prefixCls: prefixCls,\n      locale: tableLocale,\n      dropdownPrefixCls: dropdownPrefixCls,\n      mergedColumns: mergedColumns,\n      onFilterChange: onFilterChange,\n      getPopupContainer: getPopupContainer\n    }),\n    _useFilter2 = _slicedToArray(_useFilter, 3),\n    transformFilterColumns = _useFilter2[0],\n    filterStates = _useFilter2[1],\n    getFilters = _useFilter2[2];\n  var mergedData = getFilterData(sortedData, filterStates);\n  changeEventInfo.filters = getFilters();\n  changeEventInfo.filterStates = filterStates; // ============================ Column ============================\n\n  var columnTitleProps = React.useMemo(function () {\n    return _extends({}, sorterTitleProps);\n  }, [sorterTitleProps]);\n  var _useTitleColumns = useTitleColumns(columnTitleProps),\n    _useTitleColumns2 = _slicedToArray(_useTitleColumns, 1),\n    transformTitleColumns = _useTitleColumns2[0]; // ========================== Pagination ==========================\n\n  var onPaginationChange = function onPaginationChange(current, pageSize) {\n    triggerOnChange({\n      pagination: _extends(_extends({}, changeEventInfo.pagination), {\n        current: current,\n        pageSize: pageSize\n      })\n    }, 'paginate');\n  };\n  var _usePagination = usePagination(mergedData.length, pagination, onPaginationChange),\n    _usePagination2 = _slicedToArray(_usePagination, 2),\n    mergedPagination = _usePagination2[0],\n    resetPagination = _usePagination2[1];\n  changeEventInfo.pagination = pagination === false ? {} : getPaginationParam(pagination, mergedPagination);\n  changeEventInfo.resetPagination = resetPagination; // ============================= Data =============================\n\n  var pageData = React.useMemo(function () {\n    if (pagination === false || !mergedPagination.pageSize) {\n      return mergedData;\n    }\n    var _mergedPagination$cur = mergedPagination.current,\n      current = _mergedPagination$cur === void 0 ? 1 : _mergedPagination$cur,\n      total = mergedPagination.total,\n      _mergedPagination$pag = mergedPagination.pageSize,\n      pageSize = _mergedPagination$pag === void 0 ? DEFAULT_PAGE_SIZE : _mergedPagination$pag;\n    devWarning(current > 0, 'Table', '`current` should be positive number.'); // Dynamic table data\n\n    if (mergedData.length < total) {\n      if (mergedData.length > pageSize) {\n        devWarning(false, 'Table', '`dataSource` length is less than `pagination.total` but large than `pagination.pageSize`. Please make sure your config correct data with async mode.');\n        return mergedData.slice((current - 1) * pageSize, current * pageSize);\n      }\n      return mergedData;\n    }\n    return mergedData.slice((current - 1) * pageSize, current * pageSize);\n  }, [!!pagination, mergedData, mergedPagination && mergedPagination.current, mergedPagination && mergedPagination.pageSize, mergedPagination && mergedPagination.total]); // ========================== Selections ==========================\n\n  var _useSelection = useSelection(rowSelection, {\n      prefixCls: prefixCls,\n      data: mergedData,\n      pageData: pageData,\n      getRowKey: getRowKey,\n      getRecordByKey: getRecordByKey,\n      expandType: expandType,\n      childrenColumnName: childrenColumnName,\n      locale: tableLocale,\n      getPopupContainer: getPopupContainer\n    }),\n    _useSelection2 = _slicedToArray(_useSelection, 2),\n    transformSelectionColumns = _useSelection2[0],\n    selectedKeySet = _useSelection2[1];\n  var internalRowClassName = function internalRowClassName(record, index, indent) {\n    var mergedRowClassName;\n    if (typeof rowClassName === 'function') {\n      mergedRowClassName = classNames(rowClassName(record, index, indent));\n    } else {\n      mergedRowClassName = classNames(rowClassName);\n    }\n    return classNames(_defineProperty({}, \"\".concat(prefixCls, \"-row-selected\"), selectedKeySet.has(getRowKey(record, index))), mergedRowClassName);\n  }; // ========================== Expandable ==========================\n  // Pass origin render status into `rc-table`, this can be removed when refactor with `rc-table`\n\n  mergedExpandable.__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon; // Customize expandable icon\n\n  mergedExpandable.expandIcon = mergedExpandable.expandIcon || expandIcon || renderExpandIcon(tableLocale); // Adjust expand icon index, no overwrite expandIconColumnIndex if set.\n\n  if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {\n    mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;\n  } else if (mergedExpandable.expandIconColumnIndex > 0 && rowSelection) {\n    mergedExpandable.expandIconColumnIndex -= 1;\n  } // Indent size\n\n  if (typeof mergedExpandable.indentSize !== 'number') {\n    mergedExpandable.indentSize = typeof indentSize === 'number' ? indentSize : 15;\n  } // ============================ Render ============================\n\n  var transformColumns = React.useCallback(function (innerColumns) {\n    return transformTitleColumns(transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns))));\n  }, [transformSorterColumns, transformFilterColumns, transformSelectionColumns]);\n  var topPaginationNode;\n  var bottomPaginationNode;\n  if (pagination !== false && (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total)) {\n    var paginationSize;\n    if (mergedPagination.size) {\n      paginationSize = mergedPagination.size;\n    } else {\n      paginationSize = mergedSize === 'small' || mergedSize === 'middle' ? 'small' : undefined;\n    }\n    var renderPagination = function renderPagination(position) {\n      return /*#__PURE__*/React.createElement(Pagination, _extends({}, mergedPagination, {\n        className: classNames(\"\".concat(prefixCls, \"-pagination \").concat(prefixCls, \"-pagination-\").concat(position), mergedPagination.className),\n        size: paginationSize\n      }));\n    };\n    var defaultPosition = direction === 'rtl' ? 'left' : 'right';\n    var position = mergedPagination.position;\n    if (position !== null && Array.isArray(position)) {\n      var topPos = position.find(function (p) {\n        return p.indexOf('top') !== -1;\n      });\n      var bottomPos = position.find(function (p) {\n        return p.indexOf('bottom') !== -1;\n      });\n      var isDisable = position.every(function (p) {\n        return \"\".concat(p) === 'none';\n      });\n      if (!topPos && !bottomPos && !isDisable) {\n        bottomPaginationNode = renderPagination(defaultPosition);\n      }\n      if (topPos) {\n        topPaginationNode = renderPagination(topPos.toLowerCase().replace('top', ''));\n      }\n      if (bottomPos) {\n        bottomPaginationNode = renderPagination(bottomPos.toLowerCase().replace('bottom', ''));\n      }\n    } else {\n      bottomPaginationNode = renderPagination(defaultPosition);\n    }\n  } // >>>>>>>>> Spinning\n\n  var spinProps;\n  if (typeof loading === 'boolean') {\n    spinProps = {\n      spinning: loading\n    };\n  } else if (_typeof(loading) === 'object') {\n    spinProps = _extends({\n      spinning: true\n    }, loading);\n  }\n  var wrapperClassNames = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-wrapper-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: wrapperClassNames,\n    style: style\n  }, /*#__PURE__*/React.createElement(Spin, _extends({\n    spinning: false\n  }, spinProps), topPaginationNode, /*#__PURE__*/React.createElement(RcTable, _extends({}, tableProps, {\n    columns: mergedColumns,\n    direction: direction,\n    expandable: mergedExpandable,\n    prefixCls: prefixCls,\n    className: classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-middle\"), mergedSize === 'middle'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-small\"), mergedSize === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-bordered\"), bordered), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-empty\"), rawData.length === 0), _classNames3)),\n    data: pageData,\n    rowKey: getRowKey,\n    rowClassName: internalRowClassName,\n    emptyText: locale && locale.emptyText || renderEmpty('Table') // Internal\n    ,\n\n    internalHooks: INTERNAL_HOOKS,\n    internalRefs: internalRefs,\n    transformColumns: transformColumns\n  })), bottomPaginationNode));\n}\nvar ForwardTable = /*#__PURE__*/React.forwardRef(InternalTable);\nvar Table = ForwardTable;\nTable.defaultProps = {\n  rowKey: 'key'\n};\nTable.SELECTION_COLUMN = SELECTION_COLUMN;\nTable.EXPAND_COLUMN = RcTable.EXPAND_COLUMN;\nTable.SELECTION_ALL = SELECTION_ALL;\nTable.SELECTION_INVERT = SELECTION_INVERT;\nTable.SELECTION_NONE = SELECTION_NONE;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = Summary;\nexport default Table;", "map": {"version": 3, "names": ["_typeof", "_defineProperty", "_slicedToArray", "_extends", "React", "classNames", "omit", "RcTable", "Summary", "INTERNAL_HOOKS", "convertChildrenToColumns", "Spin", "Pagination", "ConfigContext", "usePagination", "DEFAULT_PAGE_SIZE", "getPaginationParam", "useLazyKVMap", "useSelection", "SELECTION_ALL", "SELECTION_COLUMN", "SELECTION_INVERT", "SELECTION_NONE", "useSorter", "getSortData", "useFilter", "getFilterData", "useTitleColumns", "renderExpandIcon", "scrollTo", "defaultLocale", "SizeContext", "Column", "ColumnGroup", "dev<PERSON><PERSON><PERSON>", "useBreakpoint", "EMPTY_LIST", "InternalTable", "props", "ref", "_classNames3", "customizePrefixCls", "prefixCls", "className", "style", "customizeSize", "size", "bordered", "customizeDropdownPrefixCls", "dropdownPrefixCls", "dataSource", "pagination", "rowSelection", "<PERSON><PERSON><PERSON>", "rowClassName", "columns", "children", "legacyChildrenColumnName", "childrenColumnName", "onChange", "getPopupContainer", "loading", "expandIcon", "expandable", "expandedRowRender", "expandIconColumnIndex", "indentSize", "scroll", "sortDirections", "locale", "_props$showSorterTool", "showSorterTooltip", "length", "baseColumns", "useMemo", "needResponsive", "some", "col", "responsive", "screens", "mergedColumns", "matched", "Set", "Object", "keys", "filter", "m", "c", "r", "has", "tableProps", "useContext", "_React$useContext", "_React$useContext$loc", "contextLocale", "renderEmpty", "direction", "mergedSize", "tableLocale", "Table", "rawData", "_React$useContext2", "getPrefixCls", "mergedExpandable", "_mergedExpandable$chi", "expandType", "item", "internalRefs", "body", "useRef", "getRowKey", "record", "_useLazyKVMap", "_useLazyKVMap2", "getRecordByKey", "changeEventInfo", "triggerOnChange", "info", "action", "reset", "arguments", "undefined", "changeInfo", "resetPagination", "current", "pageSize", "scrollToFirstRowOnChange", "getContainer", "filters", "sorter", "currentDataSource", "sorterStates", "filterStates", "onSorterChange", "_useSorter", "_useSorter2", "transformSorterColumns", "sortStates", "sorterTitleProps", "getSorters", "sortedData", "onFilterChange", "_useFilter", "_useFilter2", "transformFilterColumns", "getFilters", "mergedData", "columnTitleProps", "_useTitleColumns", "_useTitleColumns2", "transformTitleColumns", "onPaginationChange", "_usePagination", "_usePagination2", "mergedPagination", "pageData", "_mergedPagination$cur", "total", "_mergedPagination$pag", "slice", "_useSelection", "data", "_useSelection2", "transformSelectionColumns", "selectedKeySet", "internalRowClassName", "index", "indent", "mergedRowClassName", "concat", "__PARENT_RENDER_ICON__", "transformColumns", "useCallback", "innerColumns", "topPaginationNode", "bottomPaginationNode", "paginationSize", "renderPagination", "position", "createElement", "defaultPosition", "Array", "isArray", "topPos", "find", "p", "indexOf", "bottomPos", "isDisable", "every", "toLowerCase", "replace", "spinProps", "spinning", "wrapperClassNames", "emptyText", "internalHooks", "ForwardTable", "forwardRef", "defaultProps", "EXPAND_COLUMN"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/Table.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport RcTable, { Summary } from 'rc-table';\nimport { INTERNAL_HOOKS } from \"rc-table/es/Table\";\nimport { convertChildrenToColumns } from \"rc-table/es/hooks/useColumns\";\nimport Spin from '../spin';\nimport Pagination from '../pagination';\nimport { ConfigContext } from '../config-provider/context';\nimport usePagination, { DEFAULT_PAGE_SIZE, getPaginationParam } from './hooks/usePagination';\nimport useLazyKVMap from './hooks/useLazyKVMap';\nimport useSelection, { SELECTION_ALL, SELECTION_COLUMN, SELECTION_INVERT, SELECTION_NONE } from './hooks/useSelection';\nimport useSorter, { getSortData } from './hooks/useSorter';\nimport useFilter, { getFilterData } from './hooks/useFilter';\nimport useTitleColumns from './hooks/useTitleColumns';\nimport renderExpandIcon from './ExpandIcon';\nimport scrollTo from '../_util/scrollTo';\nimport defaultLocale from '../locale/en_US';\nimport SizeContext from '../config-provider/SizeContext';\nimport Column from './Column';\nimport ColumnGroup from './ColumnGroup';\nimport devWarning from '../_util/devWarning';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nvar EMPTY_LIST = [];\n\nfunction InternalTable(props, ref) {\n  var _classNames3;\n\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style,\n      customizeSize = props.size,\n      bordered = props.bordered,\n      customizeDropdownPrefixCls = props.dropdownPrefixCls,\n      dataSource = props.dataSource,\n      pagination = props.pagination,\n      rowSelection = props.rowSelection,\n      rowKey = props.rowKey,\n      rowClassName = props.rowClassName,\n      columns = props.columns,\n      children = props.children,\n      legacyChildrenColumnName = props.childrenColumnName,\n      onChange = props.onChange,\n      getPopupContainer = props.getPopupContainer,\n      loading = props.loading,\n      expandIcon = props.expandIcon,\n      expandable = props.expandable,\n      expandedRowRender = props.expandedRowRender,\n      expandIconColumnIndex = props.expandIconColumnIndex,\n      indentSize = props.indentSize,\n      scroll = props.scroll,\n      sortDirections = props.sortDirections,\n      locale = props.locale,\n      _props$showSorterTool = props.showSorterTooltip,\n      showSorterTooltip = _props$showSorterTool === void 0 ? true : _props$showSorterTool;\n  devWarning(!(typeof rowKey === 'function' && rowKey.length > 1), 'Table', '`index` parameter of `rowKey` function is deprecated. There is no guarantee that it will work as expected.');\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]);\n  var needResponsive = React.useMemo(function () {\n    return baseColumns.some(function (col) {\n      return col.responsive;\n    });\n  }, [baseColumns]);\n  var screens = useBreakpoint(needResponsive);\n  var mergedColumns = React.useMemo(function () {\n    var matched = new Set(Object.keys(screens).filter(function (m) {\n      return screens[m];\n    }));\n    return baseColumns.filter(function (c) {\n      return !c.responsive || c.responsive.some(function (r) {\n        return matched.has(r);\n      });\n    });\n  }, [baseColumns, screens]);\n  var tableProps = omit(props, ['className', 'style', 'columns']);\n  var size = React.useContext(SizeContext);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      _React$useContext$loc = _React$useContext.locale,\n      contextLocale = _React$useContext$loc === void 0 ? defaultLocale : _React$useContext$loc,\n      renderEmpty = _React$useContext.renderEmpty,\n      direction = _React$useContext.direction;\n\n  var mergedSize = customizeSize || size;\n\n  var tableLocale = _extends(_extends({}, contextLocale.Table), locale);\n\n  var rawData = dataSource || EMPTY_LIST;\n\n  var _React$useContext2 = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext2.getPrefixCls;\n\n  var prefixCls = getPrefixCls('table', customizePrefixCls);\n  var dropdownPrefixCls = getPrefixCls('dropdown', customizeDropdownPrefixCls);\n\n  var mergedExpandable = _extends({\n    childrenColumnName: legacyChildrenColumnName,\n    expandIconColumnIndex: expandIconColumnIndex\n  }, expandable);\n\n  var _mergedExpandable$chi = mergedExpandable.childrenColumnName,\n      childrenColumnName = _mergedExpandable$chi === void 0 ? 'children' : _mergedExpandable$chi;\n  var expandType = React.useMemo(function () {\n    if (rawData.some(function (item) {\n      return item === null || item === void 0 ? void 0 : item[childrenColumnName];\n    })) {\n      return 'nest';\n    }\n\n    if (expandedRowRender || expandable && expandable.expandedRowRender) {\n      return 'row';\n    }\n\n    return null;\n  }, [rawData]);\n  var internalRefs = {\n    body: React.useRef()\n  }; // ============================ RowKey ============================\n\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n\n    return function (record) {\n      return record === null || record === void 0 ? void 0 : record[rowKey];\n    };\n  }, [rowKey]);\n\n  var _useLazyKVMap = useLazyKVMap(rawData, childrenColumnName, getRowKey),\n      _useLazyKVMap2 = _slicedToArray(_useLazyKVMap, 1),\n      getRecordByKey = _useLazyKVMap2[0]; // ============================ Events =============================\n\n\n  var changeEventInfo = {};\n\n  var triggerOnChange = function triggerOnChange(info, action) {\n    var reset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n    var changeInfo = _extends(_extends({}, changeEventInfo), info);\n\n    if (reset) {\n      changeEventInfo.resetPagination(); // Reset event param\n\n      if (changeInfo.pagination.current) {\n        changeInfo.pagination.current = 1;\n      } // Trigger pagination events\n\n\n      if (pagination && pagination.onChange) {\n        pagination.onChange(1, changeInfo.pagination.pageSize);\n      }\n    }\n\n    if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {\n      scrollTo(0, {\n        getContainer: function getContainer() {\n          return internalRefs.body.current;\n        }\n      });\n    }\n\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo.pagination, changeInfo.filters, changeInfo.sorter, {\n      currentDataSource: getFilterData(getSortData(rawData, changeInfo.sorterStates, childrenColumnName), changeInfo.filterStates),\n      action: action\n    });\n  };\n  /**\n   * Controlled state in `columns` is not a good idea that makes too many code (1000+ line?) to read\n   * state out and then put it back to title render. Move these code into `hooks` but still too\n   * complex. We should provides Table props like `sorter` & `filter` to handle control in next big version.\n   */\n  // ============================ Sorter =============================\n\n\n  var onSorterChange = function onSorterChange(sorter, sorterStates) {\n    triggerOnChange({\n      sorter: sorter,\n      sorterStates: sorterStates\n    }, 'sort', false);\n  };\n\n  var _useSorter = useSorter({\n    prefixCls: prefixCls,\n    mergedColumns: mergedColumns,\n    onSorterChange: onSorterChange,\n    sortDirections: sortDirections || ['ascend', 'descend'],\n    tableLocale: tableLocale,\n    showSorterTooltip: showSorterTooltip\n  }),\n      _useSorter2 = _slicedToArray(_useSorter, 4),\n      transformSorterColumns = _useSorter2[0],\n      sortStates = _useSorter2[1],\n      sorterTitleProps = _useSorter2[2],\n      getSorters = _useSorter2[3];\n\n  var sortedData = React.useMemo(function () {\n    return getSortData(rawData, sortStates, childrenColumnName);\n  }, [rawData, sortStates]);\n  changeEventInfo.sorter = getSorters();\n  changeEventInfo.sorterStates = sortStates; // ============================ Filter ============================\n\n  var onFilterChange = function onFilterChange(filters, filterStates) {\n    triggerOnChange({\n      filters: filters,\n      filterStates: filterStates\n    }, 'filter', true);\n  };\n\n  var _useFilter = useFilter({\n    prefixCls: prefixCls,\n    locale: tableLocale,\n    dropdownPrefixCls: dropdownPrefixCls,\n    mergedColumns: mergedColumns,\n    onFilterChange: onFilterChange,\n    getPopupContainer: getPopupContainer\n  }),\n      _useFilter2 = _slicedToArray(_useFilter, 3),\n      transformFilterColumns = _useFilter2[0],\n      filterStates = _useFilter2[1],\n      getFilters = _useFilter2[2];\n\n  var mergedData = getFilterData(sortedData, filterStates);\n  changeEventInfo.filters = getFilters();\n  changeEventInfo.filterStates = filterStates; // ============================ Column ============================\n\n  var columnTitleProps = React.useMemo(function () {\n    return _extends({}, sorterTitleProps);\n  }, [sorterTitleProps]);\n\n  var _useTitleColumns = useTitleColumns(columnTitleProps),\n      _useTitleColumns2 = _slicedToArray(_useTitleColumns, 1),\n      transformTitleColumns = _useTitleColumns2[0]; // ========================== Pagination ==========================\n\n\n  var onPaginationChange = function onPaginationChange(current, pageSize) {\n    triggerOnChange({\n      pagination: _extends(_extends({}, changeEventInfo.pagination), {\n        current: current,\n        pageSize: pageSize\n      })\n    }, 'paginate');\n  };\n\n  var _usePagination = usePagination(mergedData.length, pagination, onPaginationChange),\n      _usePagination2 = _slicedToArray(_usePagination, 2),\n      mergedPagination = _usePagination2[0],\n      resetPagination = _usePagination2[1];\n\n  changeEventInfo.pagination = pagination === false ? {} : getPaginationParam(pagination, mergedPagination);\n  changeEventInfo.resetPagination = resetPagination; // ============================= Data =============================\n\n  var pageData = React.useMemo(function () {\n    if (pagination === false || !mergedPagination.pageSize) {\n      return mergedData;\n    }\n\n    var _mergedPagination$cur = mergedPagination.current,\n        current = _mergedPagination$cur === void 0 ? 1 : _mergedPagination$cur,\n        total = mergedPagination.total,\n        _mergedPagination$pag = mergedPagination.pageSize,\n        pageSize = _mergedPagination$pag === void 0 ? DEFAULT_PAGE_SIZE : _mergedPagination$pag;\n    devWarning(current > 0, 'Table', '`current` should be positive number.'); // Dynamic table data\n\n    if (mergedData.length < total) {\n      if (mergedData.length > pageSize) {\n        devWarning(false, 'Table', '`dataSource` length is less than `pagination.total` but large than `pagination.pageSize`. Please make sure your config correct data with async mode.');\n        return mergedData.slice((current - 1) * pageSize, current * pageSize);\n      }\n\n      return mergedData;\n    }\n\n    return mergedData.slice((current - 1) * pageSize, current * pageSize);\n  }, [!!pagination, mergedData, mergedPagination && mergedPagination.current, mergedPagination && mergedPagination.pageSize, mergedPagination && mergedPagination.total]); // ========================== Selections ==========================\n\n  var _useSelection = useSelection(rowSelection, {\n    prefixCls: prefixCls,\n    data: mergedData,\n    pageData: pageData,\n    getRowKey: getRowKey,\n    getRecordByKey: getRecordByKey,\n    expandType: expandType,\n    childrenColumnName: childrenColumnName,\n    locale: tableLocale,\n    getPopupContainer: getPopupContainer\n  }),\n      _useSelection2 = _slicedToArray(_useSelection, 2),\n      transformSelectionColumns = _useSelection2[0],\n      selectedKeySet = _useSelection2[1];\n\n  var internalRowClassName = function internalRowClassName(record, index, indent) {\n    var mergedRowClassName;\n\n    if (typeof rowClassName === 'function') {\n      mergedRowClassName = classNames(rowClassName(record, index, indent));\n    } else {\n      mergedRowClassName = classNames(rowClassName);\n    }\n\n    return classNames(_defineProperty({}, \"\".concat(prefixCls, \"-row-selected\"), selectedKeySet.has(getRowKey(record, index))), mergedRowClassName);\n  }; // ========================== Expandable ==========================\n  // Pass origin render status into `rc-table`, this can be removed when refactor with `rc-table`\n\n\n  mergedExpandable.__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon; // Customize expandable icon\n\n  mergedExpandable.expandIcon = mergedExpandable.expandIcon || expandIcon || renderExpandIcon(tableLocale); // Adjust expand icon index, no overwrite expandIconColumnIndex if set.\n\n  if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {\n    mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;\n  } else if (mergedExpandable.expandIconColumnIndex > 0 && rowSelection) {\n    mergedExpandable.expandIconColumnIndex -= 1;\n  } // Indent size\n\n\n  if (typeof mergedExpandable.indentSize !== 'number') {\n    mergedExpandable.indentSize = typeof indentSize === 'number' ? indentSize : 15;\n  } // ============================ Render ============================\n\n\n  var transformColumns = React.useCallback(function (innerColumns) {\n    return transformTitleColumns(transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns))));\n  }, [transformSorterColumns, transformFilterColumns, transformSelectionColumns]);\n  var topPaginationNode;\n  var bottomPaginationNode;\n\n  if (pagination !== false && (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total)) {\n    var paginationSize;\n\n    if (mergedPagination.size) {\n      paginationSize = mergedPagination.size;\n    } else {\n      paginationSize = mergedSize === 'small' || mergedSize === 'middle' ? 'small' : undefined;\n    }\n\n    var renderPagination = function renderPagination(position) {\n      return /*#__PURE__*/React.createElement(Pagination, _extends({}, mergedPagination, {\n        className: classNames(\"\".concat(prefixCls, \"-pagination \").concat(prefixCls, \"-pagination-\").concat(position), mergedPagination.className),\n        size: paginationSize\n      }));\n    };\n\n    var defaultPosition = direction === 'rtl' ? 'left' : 'right';\n    var position = mergedPagination.position;\n\n    if (position !== null && Array.isArray(position)) {\n      var topPos = position.find(function (p) {\n        return p.indexOf('top') !== -1;\n      });\n      var bottomPos = position.find(function (p) {\n        return p.indexOf('bottom') !== -1;\n      });\n      var isDisable = position.every(function (p) {\n        return \"\".concat(p) === 'none';\n      });\n\n      if (!topPos && !bottomPos && !isDisable) {\n        bottomPaginationNode = renderPagination(defaultPosition);\n      }\n\n      if (topPos) {\n        topPaginationNode = renderPagination(topPos.toLowerCase().replace('top', ''));\n      }\n\n      if (bottomPos) {\n        bottomPaginationNode = renderPagination(bottomPos.toLowerCase().replace('bottom', ''));\n      }\n    } else {\n      bottomPaginationNode = renderPagination(defaultPosition);\n    }\n  } // >>>>>>>>> Spinning\n\n\n  var spinProps;\n\n  if (typeof loading === 'boolean') {\n    spinProps = {\n      spinning: loading\n    };\n  } else if (_typeof(loading) === 'object') {\n    spinProps = _extends({\n      spinning: true\n    }, loading);\n  }\n\n  var wrapperClassNames = classNames(\"\".concat(prefixCls, \"-wrapper\"), _defineProperty({}, \"\".concat(prefixCls, \"-wrapper-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: wrapperClassNames,\n    style: style\n  }, /*#__PURE__*/React.createElement(Spin, _extends({\n    spinning: false\n  }, spinProps), topPaginationNode, /*#__PURE__*/React.createElement(RcTable, _extends({}, tableProps, {\n    columns: mergedColumns,\n    direction: direction,\n    expandable: mergedExpandable,\n    prefixCls: prefixCls,\n    className: classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-middle\"), mergedSize === 'middle'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-small\"), mergedSize === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-bordered\"), bordered), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-empty\"), rawData.length === 0), _classNames3)),\n    data: pageData,\n    rowKey: getRowKey,\n    rowClassName: internalRowClassName,\n    emptyText: locale && locale.emptyText || renderEmpty('Table') // Internal\n    ,\n    internalHooks: INTERNAL_HOOKS,\n    internalRefs: internalRefs,\n    transformColumns: transformColumns\n  })), bottomPaginationNode));\n}\n\nvar ForwardTable = /*#__PURE__*/React.forwardRef(InternalTable);\nvar Table = ForwardTable;\nTable.defaultProps = {\n  rowKey: 'key'\n};\nTable.SELECTION_COLUMN = SELECTION_COLUMN;\nTable.EXPAND_COLUMN = RcTable.EXPAND_COLUMN;\nTable.SELECTION_ALL = SELECTION_ALL;\nTable.SELECTION_INVERT = SELECTION_INVERT;\nTable.SELECTION_NONE = SELECTION_NONE;\nTable.Column = Column;\nTable.ColumnGroup = ColumnGroup;\nTable.Summary = Summary;\nexport default Table;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,OAAO,IAAIC,OAAO,QAAQ,UAAU;AAC3C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAOC,aAAa,IAAIC,iBAAiB,EAAEC,kBAAkB,QAAQ,uBAAuB;AAC5F,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,IAAIC,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,sBAAsB;AACtH,OAAOC,SAAS,IAAIC,WAAW,QAAQ,mBAAmB;AAC1D,OAAOC,SAAS,IAAIC,aAAa,QAAQ,mBAAmB;AAC5D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,IAAIC,UAAU,GAAG,EAAE;AAEnB,SAASC,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjC,IAAIC,YAAY;EAEhB,IAAIC,kBAAkB,GAAGH,KAAK,CAACI,SAAS;IACpCC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,aAAa,GAAGP,KAAK,CAACQ,IAAI;IAC1BC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,0BAA0B,GAAGV,KAAK,CAACW,iBAAiB;IACpDC,UAAU,GAAGZ,KAAK,CAACY,UAAU;IAC7BC,UAAU,GAAGb,KAAK,CAACa,UAAU;IAC7BC,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,YAAY,GAAGhB,KAAK,CAACgB,YAAY;IACjCC,OAAO,GAAGjB,KAAK,CAACiB,OAAO;IACvBC,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,wBAAwB,GAAGnB,KAAK,CAACoB,kBAAkB;IACnDC,QAAQ,GAAGrB,KAAK,CAACqB,QAAQ;IACzBC,iBAAiB,GAAGtB,KAAK,CAACsB,iBAAiB;IAC3CC,OAAO,GAAGvB,KAAK,CAACuB,OAAO;IACvBC,UAAU,GAAGxB,KAAK,CAACwB,UAAU;IAC7BC,UAAU,GAAGzB,KAAK,CAACyB,UAAU;IAC7BC,iBAAiB,GAAG1B,KAAK,CAAC0B,iBAAiB;IAC3CC,qBAAqB,GAAG3B,KAAK,CAAC2B,qBAAqB;IACnDC,UAAU,GAAG5B,KAAK,CAAC4B,UAAU;IAC7BC,MAAM,GAAG7B,KAAK,CAAC6B,MAAM;IACrBC,cAAc,GAAG9B,KAAK,CAAC8B,cAAc;IACrCC,MAAM,GAAG/B,KAAK,CAAC+B,MAAM;IACrBC,qBAAqB,GAAGhC,KAAK,CAACiC,iBAAiB;IAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;EACvFpC,UAAU,CAAC,EAAE,OAAOmB,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACmB,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,4GAA4G,CAAC;EACvL,IAAIC,WAAW,GAAGrE,KAAK,CAACsE,OAAO,CAAC,YAAY;IAC1C,OAAOnB,OAAO,IAAI7C,wBAAwB,CAAC8C,QAAQ,CAAC;EACtD,CAAC,EAAE,CAACD,OAAO,EAAEC,QAAQ,CAAC,CAAC;EACvB,IAAImB,cAAc,GAAGvE,KAAK,CAACsE,OAAO,CAAC,YAAY;IAC7C,OAAOD,WAAW,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;MACrC,OAAOA,GAAG,CAACC,UAAU;IACvB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;EACjB,IAAIM,OAAO,GAAG5C,aAAa,CAACwC,cAAc,CAAC;EAC3C,IAAIK,aAAa,GAAG5E,KAAK,CAACsE,OAAO,CAAC,YAAY;IAC5C,IAAIO,OAAO,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,CAAC,UAAUC,CAAC,EAAE;MAC7D,OAAOP,OAAO,CAACO,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;IACH,OAAOb,WAAW,CAACY,MAAM,CAAC,UAAUE,CAAC,EAAE;MACrC,OAAO,CAACA,CAAC,CAACT,UAAU,IAAIS,CAAC,CAACT,UAAU,CAACF,IAAI,CAAC,UAAUY,CAAC,EAAE;QACrD,OAAOP,OAAO,CAACQ,GAAG,CAACD,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACf,WAAW,EAAEM,OAAO,CAAC,CAAC;EAC1B,IAAIW,UAAU,GAAGpF,IAAI,CAACgC,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EAC/D,IAAIQ,IAAI,GAAG1C,KAAK,CAACuF,UAAU,CAAC5D,WAAW,CAAC;EAExC,IAAI6D,iBAAiB,GAAGxF,KAAK,CAACuF,UAAU,CAAC9E,aAAa,CAAC;IACnDgF,qBAAqB,GAAGD,iBAAiB,CAACvB,MAAM;IAChDyB,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG/D,aAAa,GAAG+D,qBAAqB;IACxFE,WAAW,GAAGH,iBAAiB,CAACG,WAAW;IAC3CC,SAAS,GAAGJ,iBAAiB,CAACI,SAAS;EAE3C,IAAIC,UAAU,GAAGpD,aAAa,IAAIC,IAAI;EAEtC,IAAIoD,WAAW,GAAG/F,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2F,aAAa,CAACK,KAAK,CAAC,EAAE9B,MAAM,CAAC;EAErE,IAAI+B,OAAO,GAAGlD,UAAU,IAAId,UAAU;EAEtC,IAAIiE,kBAAkB,GAAGjG,KAAK,CAACuF,UAAU,CAAC9E,aAAa,CAAC;IACpDyF,YAAY,GAAGD,kBAAkB,CAACC,YAAY;EAElD,IAAI5D,SAAS,GAAG4D,YAAY,CAAC,OAAO,EAAE7D,kBAAkB,CAAC;EACzD,IAAIQ,iBAAiB,GAAGqD,YAAY,CAAC,UAAU,EAAEtD,0BAA0B,CAAC;EAE5E,IAAIuD,gBAAgB,GAAGpG,QAAQ,CAAC;IAC9BuD,kBAAkB,EAAED,wBAAwB;IAC5CQ,qBAAqB,EAAEA;EACzB,CAAC,EAAEF,UAAU,CAAC;EAEd,IAAIyC,qBAAqB,GAAGD,gBAAgB,CAAC7C,kBAAkB;IAC3DA,kBAAkB,GAAG8C,qBAAqB,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,qBAAqB;EAC9F,IAAIC,UAAU,GAAGrG,KAAK,CAACsE,OAAO,CAAC,YAAY;IACzC,IAAI0B,OAAO,CAACxB,IAAI,CAAC,UAAU8B,IAAI,EAAE;MAC/B,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAChD,kBAAkB,CAAC;IAC7E,CAAC,CAAC,EAAE;MACF,OAAO,MAAM;IACf;IAEA,IAAIM,iBAAiB,IAAID,UAAU,IAAIA,UAAU,CAACC,iBAAiB,EAAE;MACnE,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAACoC,OAAO,CAAC,CAAC;EACb,IAAIO,YAAY,GAAG;IACjBC,IAAI,EAAExG,KAAK,CAACyG,MAAM,CAAC;EACrB,CAAC,CAAC,CAAC;;EAEH,IAAIC,SAAS,GAAG1G,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,IAAI,OAAOrB,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IAEA,OAAO,UAAU0D,MAAM,EAAE;MACvB,OAAOA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC1D,MAAM,CAAC;IACvE,CAAC;EACH,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEZ,IAAI2D,aAAa,GAAG/F,YAAY,CAACmF,OAAO,EAAE1C,kBAAkB,EAAEoD,SAAS,CAAC;IACpEG,cAAc,GAAG/G,cAAc,CAAC8G,aAAa,EAAE,CAAC,CAAC;IACjDE,cAAc,GAAGD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGxC,IAAIE,eAAe,GAAG,CAAC,CAAC;EAExB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC3D,IAAIC,KAAK,GAAGC,SAAS,CAAChD,MAAM,GAAG,CAAC,IAAIgD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAErF,IAAIE,UAAU,GAAGvH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgH,eAAe,CAAC,EAAEE,IAAI,CAAC;IAE9D,IAAIE,KAAK,EAAE;MACTJ,eAAe,CAACQ,eAAe,CAAC,CAAC,CAAC,CAAC;;MAEnC,IAAID,UAAU,CAACvE,UAAU,CAACyE,OAAO,EAAE;QACjCF,UAAU,CAACvE,UAAU,CAACyE,OAAO,GAAG,CAAC;MACnC,CAAC,CAAC;;MAGF,IAAIzE,UAAU,IAAIA,UAAU,CAACQ,QAAQ,EAAE;QACrCR,UAAU,CAACQ,QAAQ,CAAC,CAAC,EAAE+D,UAAU,CAACvE,UAAU,CAAC0E,QAAQ,CAAC;MACxD;IACF;IAEA,IAAI1D,MAAM,IAAIA,MAAM,CAAC2D,wBAAwB,KAAK,KAAK,IAAInB,YAAY,CAACC,IAAI,CAACgB,OAAO,EAAE;MACpF/F,QAAQ,CAAC,CAAC,EAAE;QACVkG,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;UACpC,OAAOpB,YAAY,CAACC,IAAI,CAACgB,OAAO;QAClC;MACF,CAAC,CAAC;IACJ;IAEAjE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+D,UAAU,CAACvE,UAAU,EAAEuE,UAAU,CAACM,OAAO,EAAEN,UAAU,CAACO,MAAM,EAAE;MACzHC,iBAAiB,EAAExG,aAAa,CAACF,WAAW,CAAC4E,OAAO,EAAEsB,UAAU,CAACS,YAAY,EAAEzE,kBAAkB,CAAC,EAAEgE,UAAU,CAACU,YAAY,CAAC;MAC5Hd,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;EACE;;EAGA,IAAIe,cAAc,GAAG,SAASA,cAAcA,CAACJ,MAAM,EAAEE,YAAY,EAAE;IACjEf,eAAe,CAAC;MACda,MAAM,EAAEA,MAAM;MACdE,YAAY,EAAEA;IAChB,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;EACnB,CAAC;EAED,IAAIG,UAAU,GAAG/G,SAAS,CAAC;MACzBmB,SAAS,EAAEA,SAAS;MACpBsC,aAAa,EAAEA,aAAa;MAC5BqD,cAAc,EAAEA,cAAc;MAC9BjE,cAAc,EAAEA,cAAc,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;MACvD8B,WAAW,EAAEA,WAAW;MACxB3B,iBAAiB,EAAEA;IACrB,CAAC,CAAC;IACEgE,WAAW,GAAGrI,cAAc,CAACoI,UAAU,EAAE,CAAC,CAAC;IAC3CE,sBAAsB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACvCE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC3BG,gBAAgB,GAAGH,WAAW,CAAC,CAAC,CAAC;IACjCI,UAAU,GAAGJ,WAAW,CAAC,CAAC,CAAC;EAE/B,IAAIK,UAAU,GAAGxI,KAAK,CAACsE,OAAO,CAAC,YAAY;IACzC,OAAOlD,WAAW,CAAC4E,OAAO,EAAEqC,UAAU,EAAE/E,kBAAkB,CAAC;EAC7D,CAAC,EAAE,CAAC0C,OAAO,EAAEqC,UAAU,CAAC,CAAC;EACzBtB,eAAe,CAACc,MAAM,GAAGU,UAAU,CAAC,CAAC;EACrCxB,eAAe,CAACgB,YAAY,GAAGM,UAAU,CAAC,CAAC;;EAE3C,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACb,OAAO,EAAEI,YAAY,EAAE;IAClEhB,eAAe,CAAC;MACdY,OAAO,EAAEA,OAAO;MAChBI,YAAY,EAAEA;IAChB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;EACpB,CAAC;EAED,IAAIU,UAAU,GAAGrH,SAAS,CAAC;MACzBiB,SAAS,EAAEA,SAAS;MACpB2B,MAAM,EAAE6B,WAAW;MACnBjD,iBAAiB,EAAEA,iBAAiB;MACpC+B,aAAa,EAAEA,aAAa;MAC5B6D,cAAc,EAAEA,cAAc;MAC9BjF,iBAAiB,EAAEA;IACrB,CAAC,CAAC;IACEmF,WAAW,GAAG7I,cAAc,CAAC4I,UAAU,EAAE,CAAC,CAAC;IAC3CE,sBAAsB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACvCX,YAAY,GAAGW,WAAW,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;EAE/B,IAAIG,UAAU,GAAGxH,aAAa,CAACkH,UAAU,EAAER,YAAY,CAAC;EACxDjB,eAAe,CAACa,OAAO,GAAGiB,UAAU,CAAC,CAAC;EACtC9B,eAAe,CAACiB,YAAY,GAAGA,YAAY,CAAC,CAAC;;EAE7C,IAAIe,gBAAgB,GAAG/I,KAAK,CAACsE,OAAO,CAAC,YAAY;IAC/C,OAAOvE,QAAQ,CAAC,CAAC,CAAC,EAAEuI,gBAAgB,CAAC;EACvC,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,IAAIU,gBAAgB,GAAGzH,eAAe,CAACwH,gBAAgB,CAAC;IACpDE,iBAAiB,GAAGnJ,cAAc,CAACkJ,gBAAgB,EAAE,CAAC,CAAC;IACvDE,qBAAqB,GAAGD,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGlD,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC3B,OAAO,EAAEC,QAAQ,EAAE;IACtET,eAAe,CAAC;MACdjE,UAAU,EAAEhD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgH,eAAe,CAAChE,UAAU,CAAC,EAAE;QAC7DyE,OAAO,EAAEA,OAAO;QAChBC,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,EAAE,UAAU,CAAC;EAChB,CAAC;EAED,IAAI2B,cAAc,GAAG1I,aAAa,CAACoI,UAAU,CAAC1E,MAAM,EAAErB,UAAU,EAAEoG,kBAAkB,CAAC;IACjFE,eAAe,GAAGvJ,cAAc,CAACsJ,cAAc,EAAE,CAAC,CAAC;IACnDE,gBAAgB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACrC9B,eAAe,GAAG8B,eAAe,CAAC,CAAC,CAAC;EAExCtC,eAAe,CAAChE,UAAU,GAAGA,UAAU,KAAK,KAAK,GAAG,CAAC,CAAC,GAAGnC,kBAAkB,CAACmC,UAAU,EAAEuG,gBAAgB,CAAC;EACzGvC,eAAe,CAACQ,eAAe,GAAGA,eAAe,CAAC,CAAC;;EAEnD,IAAIgC,QAAQ,GAAGvJ,KAAK,CAACsE,OAAO,CAAC,YAAY;IACvC,IAAIvB,UAAU,KAAK,KAAK,IAAI,CAACuG,gBAAgB,CAAC7B,QAAQ,EAAE;MACtD,OAAOqB,UAAU;IACnB;IAEA,IAAIU,qBAAqB,GAAGF,gBAAgB,CAAC9B,OAAO;MAChDA,OAAO,GAAGgC,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;MACtEC,KAAK,GAAGH,gBAAgB,CAACG,KAAK;MAC9BC,qBAAqB,GAAGJ,gBAAgB,CAAC7B,QAAQ;MACjDA,QAAQ,GAAGiC,qBAAqB,KAAK,KAAK,CAAC,GAAG/I,iBAAiB,GAAG+I,qBAAqB;IAC3F5H,UAAU,CAAC0F,OAAO,GAAG,CAAC,EAAE,OAAO,EAAE,sCAAsC,CAAC,CAAC,CAAC;;IAE1E,IAAIsB,UAAU,CAAC1E,MAAM,GAAGqF,KAAK,EAAE;MAC7B,IAAIX,UAAU,CAAC1E,MAAM,GAAGqD,QAAQ,EAAE;QAChC3F,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,sJAAsJ,CAAC;QAClL,OAAOgH,UAAU,CAACa,KAAK,CAAC,CAACnC,OAAO,GAAG,CAAC,IAAIC,QAAQ,EAAED,OAAO,GAAGC,QAAQ,CAAC;MACvE;MAEA,OAAOqB,UAAU;IACnB;IAEA,OAAOA,UAAU,CAACa,KAAK,CAAC,CAACnC,OAAO,GAAG,CAAC,IAAIC,QAAQ,EAAED,OAAO,GAAGC,QAAQ,CAAC;EACvE,CAAC,EAAE,CAAC,CAAC,CAAC1E,UAAU,EAAE+F,UAAU,EAAEQ,gBAAgB,IAAIA,gBAAgB,CAAC9B,OAAO,EAAE8B,gBAAgB,IAAIA,gBAAgB,CAAC7B,QAAQ,EAAE6B,gBAAgB,IAAIA,gBAAgB,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEzK,IAAIG,aAAa,GAAG9I,YAAY,CAACkC,YAAY,EAAE;MAC7CV,SAAS,EAAEA,SAAS;MACpBuH,IAAI,EAAEf,UAAU;MAChBS,QAAQ,EAAEA,QAAQ;MAClB7C,SAAS,EAAEA,SAAS;MACpBI,cAAc,EAAEA,cAAc;MAC9BT,UAAU,EAAEA,UAAU;MACtB/C,kBAAkB,EAAEA,kBAAkB;MACtCW,MAAM,EAAE6B,WAAW;MACnBtC,iBAAiB,EAAEA;IACrB,CAAC,CAAC;IACEsG,cAAc,GAAGhK,cAAc,CAAC8J,aAAa,EAAE,CAAC,CAAC;IACjDG,yBAAyB,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7CE,cAAc,GAAGF,cAAc,CAAC,CAAC,CAAC;EAEtC,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACtD,MAAM,EAAEuD,KAAK,EAAEC,MAAM,EAAE;IAC9E,IAAIC,kBAAkB;IAEtB,IAAI,OAAOlH,YAAY,KAAK,UAAU,EAAE;MACtCkH,kBAAkB,GAAGnK,UAAU,CAACiD,YAAY,CAACyD,MAAM,EAAEuD,KAAK,EAAEC,MAAM,CAAC,CAAC;IACtE,CAAC,MAAM;MACLC,kBAAkB,GAAGnK,UAAU,CAACiD,YAAY,CAAC;IAC/C;IAEA,OAAOjD,UAAU,CAACJ,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwK,MAAM,CAAC/H,SAAS,EAAE,eAAe,CAAC,EAAE0H,cAAc,CAAC3E,GAAG,CAACqB,SAAS,CAACC,MAAM,EAAEuD,KAAK,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAC;EACjJ,CAAC,CAAC,CAAC;EACH;;EAGAjE,gBAAgB,CAACmE,sBAAsB,GAAGnE,gBAAgB,CAACzC,UAAU,CAAC,CAAC;;EAEvEyC,gBAAgB,CAACzC,UAAU,GAAGyC,gBAAgB,CAACzC,UAAU,IAAIA,UAAU,IAAIlC,gBAAgB,CAACsE,WAAW,CAAC,CAAC,CAAC;;EAE1G,IAAIO,UAAU,KAAK,MAAM,IAAIF,gBAAgB,CAACtC,qBAAqB,KAAKwD,SAAS,EAAE;IACjFlB,gBAAgB,CAACtC,qBAAqB,GAAGb,YAAY,GAAG,CAAC,GAAG,CAAC;EAC/D,CAAC,MAAM,IAAImD,gBAAgB,CAACtC,qBAAqB,GAAG,CAAC,IAAIb,YAAY,EAAE;IACrEmD,gBAAgB,CAACtC,qBAAqB,IAAI,CAAC;EAC7C,CAAC,CAAC;;EAGF,IAAI,OAAOsC,gBAAgB,CAACrC,UAAU,KAAK,QAAQ,EAAE;IACnDqC,gBAAgB,CAACrC,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE;EAChF,CAAC,CAAC;;EAGF,IAAIyG,gBAAgB,GAAGvK,KAAK,CAACwK,WAAW,CAAC,UAAUC,YAAY,EAAE;IAC/D,OAAOvB,qBAAqB,CAACa,yBAAyB,CAACnB,sBAAsB,CAACR,sBAAsB,CAACqC,YAAY,CAAC,CAAC,CAAC,CAAC;EACvH,CAAC,EAAE,CAACrC,sBAAsB,EAAEQ,sBAAsB,EAAEmB,yBAAyB,CAAC,CAAC;EAC/E,IAAIW,iBAAiB;EACrB,IAAIC,oBAAoB;EAExB,IAAI5H,UAAU,KAAK,KAAK,KAAKuG,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACG,KAAK,CAAC,EAAE;IACxH,IAAImB,cAAc;IAElB,IAAItB,gBAAgB,CAAC5G,IAAI,EAAE;MACzBkI,cAAc,GAAGtB,gBAAgB,CAAC5G,IAAI;IACxC,CAAC,MAAM;MACLkI,cAAc,GAAG/E,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,QAAQ,GAAG,OAAO,GAAGwB,SAAS;IAC1F;IAEA,IAAIwD,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,QAAQ,EAAE;MACzD,OAAO,aAAa9K,KAAK,CAAC+K,aAAa,CAACvK,UAAU,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEuJ,gBAAgB,EAAE;QACjF/G,SAAS,EAAEtC,UAAU,CAAC,EAAE,CAACoK,MAAM,CAAC/H,SAAS,EAAE,cAAc,CAAC,CAAC+H,MAAM,CAAC/H,SAAS,EAAE,cAAc,CAAC,CAAC+H,MAAM,CAACS,QAAQ,CAAC,EAAExB,gBAAgB,CAAC/G,SAAS,CAAC;QAC1IG,IAAI,EAAEkI;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAII,eAAe,GAAGpF,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5D,IAAIkF,QAAQ,GAAGxB,gBAAgB,CAACwB,QAAQ;IAExC,IAAIA,QAAQ,KAAK,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;MAChD,IAAIK,MAAM,GAAGL,QAAQ,CAACM,IAAI,CAAC,UAAUC,CAAC,EAAE;QACtC,OAAOA,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;MAChC,CAAC,CAAC;MACF,IAAIC,SAAS,GAAGT,QAAQ,CAACM,IAAI,CAAC,UAAUC,CAAC,EAAE;QACzC,OAAOA,CAAC,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,IAAIE,SAAS,GAAGV,QAAQ,CAACW,KAAK,CAAC,UAAUJ,CAAC,EAAE;QAC1C,OAAO,EAAE,CAAChB,MAAM,CAACgB,CAAC,CAAC,KAAK,MAAM;MAChC,CAAC,CAAC;MAEF,IAAI,CAACF,MAAM,IAAI,CAACI,SAAS,IAAI,CAACC,SAAS,EAAE;QACvCb,oBAAoB,GAAGE,gBAAgB,CAACG,eAAe,CAAC;MAC1D;MAEA,IAAIG,MAAM,EAAE;QACVT,iBAAiB,GAAGG,gBAAgB,CAACM,MAAM,CAACO,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/E;MAEA,IAAIJ,SAAS,EAAE;QACbZ,oBAAoB,GAAGE,gBAAgB,CAACU,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MACxF;IACF,CAAC,MAAM;MACLhB,oBAAoB,GAAGE,gBAAgB,CAACG,eAAe,CAAC;IAC1D;EACF,CAAC,CAAC;;EAGF,IAAIY,SAAS;EAEb,IAAI,OAAOnI,OAAO,KAAK,SAAS,EAAE;IAChCmI,SAAS,GAAG;MACVC,QAAQ,EAAEpI;IACZ,CAAC;EACH,CAAC,MAAM,IAAI7D,OAAO,CAAC6D,OAAO,CAAC,KAAK,QAAQ,EAAE;IACxCmI,SAAS,GAAG7L,QAAQ,CAAC;MACnB8L,QAAQ,EAAE;IACZ,CAAC,EAAEpI,OAAO,CAAC;EACb;EAEA,IAAIqI,iBAAiB,GAAG7L,UAAU,CAAC,EAAE,CAACoK,MAAM,CAAC/H,SAAS,EAAE,UAAU,CAAC,EAAEzC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwK,MAAM,CAAC/H,SAAS,EAAE,cAAc,CAAC,EAAEsD,SAAS,KAAK,KAAK,CAAC,EAAErD,SAAS,CAAC;EAC/J,OAAO,aAAavC,KAAK,CAAC+K,aAAa,CAAC,KAAK,EAAE;IAC7C5I,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAEuJ,iBAAiB;IAC5BtJ,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaxC,KAAK,CAAC+K,aAAa,CAACxK,IAAI,EAAER,QAAQ,CAAC;IACjD8L,QAAQ,EAAE;EACZ,CAAC,EAAED,SAAS,CAAC,EAAElB,iBAAiB,EAAE,aAAa1K,KAAK,CAAC+K,aAAa,CAAC5K,OAAO,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEuF,UAAU,EAAE;IACnGnC,OAAO,EAAEyB,aAAa;IACtBgB,SAAS,EAAEA,SAAS;IACpBjC,UAAU,EAAEwC,gBAAgB;IAC5B7D,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEtC,UAAU,EAAEmC,YAAY,GAAG,CAAC,CAAC,EAAEvC,eAAe,CAACuC,YAAY,EAAE,EAAE,CAACiI,MAAM,CAAC/H,SAAS,EAAE,SAAS,CAAC,EAAEuD,UAAU,KAAK,QAAQ,CAAC,EAAEhG,eAAe,CAACuC,YAAY,EAAE,EAAE,CAACiI,MAAM,CAAC/H,SAAS,EAAE,QAAQ,CAAC,EAAEuD,UAAU,KAAK,OAAO,CAAC,EAAEhG,eAAe,CAACuC,YAAY,EAAE,EAAE,CAACiI,MAAM,CAAC/H,SAAS,EAAE,WAAW,CAAC,EAAEK,QAAQ,CAAC,EAAE9C,eAAe,CAACuC,YAAY,EAAE,EAAE,CAACiI,MAAM,CAAC/H,SAAS,EAAE,QAAQ,CAAC,EAAE0D,OAAO,CAAC5B,MAAM,KAAK,CAAC,CAAC,EAAEhC,YAAY,CAAC,CAAC;IACzYyH,IAAI,EAAEN,QAAQ;IACdtG,MAAM,EAAEyD,SAAS;IACjBxD,YAAY,EAAE+G,oBAAoB;IAClC8B,SAAS,EAAE9H,MAAM,IAAIA,MAAM,CAAC8H,SAAS,IAAIpG,WAAW,CAAC,OAAO,CAAC,CAAC;IAAA;;IAE9DqG,aAAa,EAAE3L,cAAc;IAC7BkG,YAAY,EAAEA,YAAY;IAC1BgE,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC,EAAEI,oBAAoB,CAAC,CAAC;AAC7B;AAEA,IAAIsB,YAAY,GAAG,aAAajM,KAAK,CAACkM,UAAU,CAACjK,aAAa,CAAC;AAC/D,IAAI8D,KAAK,GAAGkG,YAAY;AACxBlG,KAAK,CAACoG,YAAY,GAAG;EACnBlJ,MAAM,EAAE;AACV,CAAC;AACD8C,KAAK,CAAC/E,gBAAgB,GAAGA,gBAAgB;AACzC+E,KAAK,CAACqG,aAAa,GAAGjM,OAAO,CAACiM,aAAa;AAC3CrG,KAAK,CAAChF,aAAa,GAAGA,aAAa;AACnCgF,KAAK,CAAC9E,gBAAgB,GAAGA,gBAAgB;AACzC8E,KAAK,CAAC7E,cAAc,GAAGA,cAAc;AACrC6E,KAAK,CAACnE,MAAM,GAAGA,MAAM;AACrBmE,KAAK,CAAClE,WAAW,GAAGA,WAAW;AAC/BkE,KAAK,CAAC3F,OAAO,GAAGA,OAAO;AACvB,eAAe2F,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}