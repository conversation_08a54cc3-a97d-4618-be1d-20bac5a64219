export namespace UIStrings {
    const navigationDescription: string;
    const timespanDescription: string;
    const snapshotDescription: string;
    const navigationLongDescription: string;
    const timespanLongDescription: string;
    const snapshotLongDescription: string;
    const navigationReport: string;
    const timespanReport: string;
    const snapshotReport: string;
    const summary: string;
    const allReports: string;
    const title: string;
    const categories: string;
    const categoryPerformance: string;
    const categoryAccessibility: string;
    const categoryBestPractices: string;
    const categorySeo: string;
    const categoryProgressiveWebApp: string;
    const desktop: string;
    const mobile: string;
    const ratingPass: string;
    const ratingAverage: string;
    const ratingFail: string;
    const ratingError: string;
    const navigationReportCount: string;
    const timespanReportCount: string;
    const snapshotReportCount: string;
    const save: string;
    const helpLabel: string;
    const helpDialogTitle: string;
    const helpUseCaseInstructionNavigation: string;
    const helpUseCaseInstructionTimespan: string;
    const helpUseCaseInstructionSnapshot: string;
    const helpUseCaseNavigation1: string;
    const helpUseCaseNavigation2: string;
    const helpUseCaseNavigation3: string;
    const helpUseCaseTimespan1: string;
    const helpUseCaseTimespan2: string;
    const helpUseCaseSnapshot1: string;
    const helpUseCaseSnapshot2: string;
    const passedAuditCount: string;
    const passableAuditCount: string;
    const informativeAuditCount: string;
    const highestImpact: string;
}
export type UIStringsType = typeof UIStrings;
//# sourceMappingURL=ui-strings.d.ts.map