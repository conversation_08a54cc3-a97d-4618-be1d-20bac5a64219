{"ast": null, "code": "import * as React from 'react';\nimport Empty from '../empty';\nimport { ConfigConsumer } from '.';\nvar renderEmpty = function renderEmpty(componentName) {\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefix = getPrefixCls('empty');\n    switch (componentName) {\n      case 'Table':\n      case 'List':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE\n        });\n      case 'Select':\n      case 'TreeSelect':\n      case 'Cascader':\n      case 'Transfer':\n      case 'Mentions':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE,\n          className: \"\".concat(prefix, \"-small\")\n        });\n      default:\n        return /*#__PURE__*/React.createElement(Empty, null);\n    }\n  });\n};\nexport default renderEmpty;", "map": {"version": 3, "names": ["React", "Empty", "ConfigConsumer", "renderEmpty", "componentName", "createElement", "_ref", "getPrefixCls", "prefix", "image", "PRESENTED_IMAGE_SIMPLE", "className", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/config-provider/renderEmpty.js"], "sourcesContent": ["import * as React from 'react';\nimport Empty from '../empty';\nimport { ConfigConsumer } from '.';\n\nvar renderEmpty = function renderEmpty(componentName) {\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefix = getPrefixCls('empty');\n\n    switch (componentName) {\n      case 'Table':\n      case 'List':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE\n        });\n\n      case 'Select':\n      case 'TreeSelect':\n      case 'Cascader':\n      case 'Transfer':\n      case 'Mentions':\n        return /*#__PURE__*/React.createElement(Empty, {\n          image: Empty.PRESENTED_IMAGE_SIMPLE,\n          className: \"\".concat(prefix, \"-small\")\n        });\n\n      default:\n        return /*#__PURE__*/React.createElement(Empty, null);\n    }\n  });\n};\n\nexport default renderEmpty;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,cAAc,QAAQ,GAAG;AAElC,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,aAAa,EAAE;EACpD,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAACH,cAAc,EAAE,IAAI,EAAE,UAAUI,IAAI,EAAE;IAC5E,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IACpC,IAAIC,MAAM,GAAGD,YAAY,CAAC,OAAO,CAAC;IAElC,QAAQH,aAAa;MACnB,KAAK,OAAO;MACZ,KAAK,MAAM;QACT,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAACJ,KAAK,EAAE;UAC7CQ,KAAK,EAAER,KAAK,CAACS;QACf,CAAC,CAAC;MAEJ,KAAK,QAA<PERSON>;MACb,KAAK,YAAY;MACjB,KAAK,UAAU;MACf,KAAK,UAAU;MACf,KAAK,UAAU;QACb,OAAO,aAAaV,KAAK,CAACK,aAAa,CAACJ,KAAK,EAAE;UAC7CQ,KAAK,EAAER,KAAK,CAACS,sBAAsB;UACnCC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,MAAM,EAAE,QAAQ;QACvC,CAAC,CAAC;MAEJ;QACE,OAAO,aAAaR,KAAK,CAACK,aAAa,CAACJ,KAAK,EAAE,IAAI,CAAC;IACxD;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAeE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}