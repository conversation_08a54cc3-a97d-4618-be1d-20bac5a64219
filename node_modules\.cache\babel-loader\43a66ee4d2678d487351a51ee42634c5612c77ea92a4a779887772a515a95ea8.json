{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\confrontoDispQta.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ConfrontoDisponibilitàQuantità - operazioni di confronto sulle quantità rispetto alle disponibilità\n*\n*/\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { InputNumber } from \"primereact/inputnumber\";\nimport { confirmDialog } from \"primereact/confirmdialog\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Tooltip } from \"primereact/tooltip\";\nimport { InputText } from \"primereact/inputtext\";\nimport { stopLoading } from \"../../components/generalizzazioni/stopLoading\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\nimport { chainDocumentiOrdineVendita } from \"../../components/route\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ConfrontoDispQtaChain extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: '',\n      externalCode: '',\n      brand: '',\n      nationality: '',\n      region: '',\n      format: '',\n      family: '',\n      subfamily: '',\n      group: '',\n      subgroup: '',\n      deposit: '',\n      productsPackagings: []\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      documento: null,\n      titolo: '',\n      globalFilter: null,\n      result: this.emptyResult,\n      loading: true,\n      addProdDialog: false,\n      selectedResults: null\n    };\n    this.onRowEditComplete = this.onRowEditComplete.bind(this);\n    this.modifica = this.modifica.bind(this);\n    this.modificaDoc = this.modificaDoc.bind(this);\n    this.save = this.save.bind(this);\n    this.openNew = this.openNew.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.addProd = this.addProd.bind(this);\n    this.cambiaStato = this.cambiaStato.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var doc = [];\n    let document = JSON.parse(localStorage.getItem(\"datiComodo\"));\n    var url = \"tasks/?idTask=\" + document.tasks.id;\n    await APIRequest(\"GET\", url).then(res => {\n      res.data[0].idDocument.documentBodies.forEach(element => {\n        var _element$idProductsPa, _element$idProductsPa2, _element$idProductsPa3, _element$idProductsPa4, _element$idProductsPa5, _element$idProductsPa6, _element$idProductsPa7;\n        if (element.idProductsPackaging !== null) {\n          element.eanCode = element.idProductsPackaging.eanCode;\n        }\n        element.externalCode = (_element$idProductsPa = element.idProductsPackaging) === null || _element$idProductsPa === void 0 ? void 0 : _element$idProductsPa.idProduct.externalCode;\n        var x = {\n          id: element.externalCode,\n          docBodyId: element.id,\n          description: (_element$idProductsPa2 = element.idProductsPackaging) === null || _element$idProductsPa2 === void 0 ? void 0 : _element$idProductsPa2.idProduct.description,\n          eanCode: element.eanCode,\n          lotto: element.lotto,\n          scadenza: element.scadenza,\n          qtaOrd: element.colliPreventivo,\n          qtaPrep: element.colliConsuntivo,\n          unitMeasure: element.idProductsPackaging.unitMeasure + ' x ' + element.idProductsPackaging.pcsXPackage,\n          giacenza: ((_element$idProductsPa3 = element.idProductsPackaging) === null || _element$idProductsPa3 === void 0 ? void 0 : _element$idProductsPa3.idProduct.productsAvailabilities.length) > 0 ? ((_element$idProductsPa4 = element.idProductsPackaging) === null || _element$idProductsPa4 === void 0 ? void 0 : (_element$idProductsPa5 = _element$idProductsPa4.idProduct.productsAvailabilities[0]) === null || _element$idProductsPa5 === void 0 ? void 0 : _element$idProductsPa5.availability) - ((_element$idProductsPa6 = element.idProductsPackaging) === null || _element$idProductsPa6 === void 0 ? void 0 : (_element$idProductsPa7 = _element$idProductsPa6.idProduct.productsAvailabilities[0]) === null || _element$idProductsPa7 === void 0 ? void 0 : _element$idProductsPa7.committedCustomer) : 'Non disponibile'\n        };\n        if (res.data[0].idDocument.type === 'INVENTORY') {\n          var _element$idProductsPa8;\n          x.id = (_element$idProductsPa8 = element.idProductsPackaging) === null || _element$idProductsPa8 === void 0 ? void 0 : _element$idProductsPa8.idProduct.externalCode;\n        }\n        doc.push(x);\n      });\n      this.setState({\n        documento: res.data[0],\n        results: doc,\n        loading: false,\n        titolo: 'Documento n.' + document.tasks.idDocument.number + ' del ' + new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        }).format(new Date(document.tasks.idDocument.documentDate))\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lavorazione. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  componentDidUpdate() {\n    setTimeout(() => {\n      if (this.state.documento.status === 'counted' && this.state.documento.idDocument.type !== 'INVENTORY') {\n        var tableRow = document.getElementsByClassName('p-datatable-tbody');\n        Object.entries(tableRow[0].rows).forEach(element => {\n          element[1].classList.add('tableRow');\n        });\n        var findClass = document.getElementsByClassName('tableRow');\n        Object.entries(findClass).forEach(item => {\n          var find = this.state.documento.idDocument.documentBodies.find((el, index) => {\n            var _el$idProductsPackagi;\n            return ((_el$idProductsPackagi = el.idProductsPackaging) === null || _el$idProductsPackagi === void 0 ? void 0 : _el$idProductsPackagi.idProduct.externalCode) === item[1].innerText.split(\"\\t\")[0] && index === item[1].rowIndex - 1;\n          });\n          if (find !== undefined) {\n            if (find.colliConsuntivo === find.colliPreventivo) {\n              item[1].classList.add('matchData');\n            } else {\n              item[1].classList.add('misMatchData');\n            }\n          }\n        });\n      }\n    }, 50);\n  }\n  async save(e) {\n    var tasks = {\n      task: this.state.documento\n    };\n    tasks.task.status = 'counted';\n    await APIRequest('PUT', 'tasks', tasks).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: \"Il documento è modificato\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = chainDocumentiOrdineVendita;\n      }, 3000);\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = chainDocumentiOrdineVendita;\n      }, 3000);\n    });\n  }\n  async creaDoc(e) {\n    var tasks = {\n      task: this.state.documento\n    };\n    if (this.state.documento.idDocument.type === 'INVENTORY') {\n      tasks.task.status = 'inventoried';\n      await APIRequest('PUT', 'tasks', tasks).then(res => {\n        console.log(res.data);\n        this.toast.show({\n          severity: 'success',\n          summary: 'Ottimo !',\n          detail: \"Il documento è passato in stato approvato\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = chainDocumentiOrdineVendita;\n        }, 3000);\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = chainDocumentiOrdineVendita;\n        }, 3000);\n      });\n    }\n  }\n  modifica(e, key, options) {\n    let result = options.value[options.rowIndex];\n    result.qtaPrep = e.value;\n    this.setState({\n      result,\n      index: options.rowIndex\n    });\n  }\n  chiediConferma() {\n    confirmDialog({\n      message: \"L'azione modificherà lo stato della lavorazione in preparato e sarà irreversibile continuare?\",\n      header: 'Attenzione',\n      icon: 'pi pi-exclamation-triangle',\n      acceptLabel: \"Si\",\n      rejectLabel: \"No\",\n      accept: () => this.cambiaStato(),\n      reject: null\n    });\n  }\n  async cambiaStato() {\n    var tasks = {\n      task: this.state.documento\n    };\n    tasks.task.status = 'prepared';\n    await APIRequest('PUT', 'tasks', tasks).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: \"Il documento è passato in stato preparato\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = chainDocumentiOrdineVendita;\n      }, 3000);\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.pathname = chainDocumentiOrdineVendita;\n      }, 3000);\n    });\n  }\n  /* Modifico quantità del prodotto */\n  onRowEditComplete(e) {\n    let result = this.state.result;\n    /*#__PURE__*/_jsxDEV(\"span\", {}, result, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this);\n  }\n  /* Dropdown con selettore di formato per la modifica del moltiplicatore e del formato */\n  qtaConsuntivaEditor(options) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.value[options.rowIndex].qtaPrep,\n      onValueChange: (e, key) => this.modifica(e, key = 'qtaPrep', options) /* options.editorCallback(e.target.value) */\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 16\n    }, this);\n  }\n  async modificaDoc() {\n    let result = this.state.result;\n    var find = this.state.documento.idDocument.documentBodies.find(el => el.id === result.docBodyId);\n    find.colliConsuntivo = result.qtaPrep;\n  }\n  async openNew() {\n    this.setState({\n      addProdDialog: true\n    });\n    var url = '';\n    /* Reperisco i prodotti all'interno del listino retailer */\n    url = 'pricelistretailer/?idRetailer=' + this.state.documento.idDocument.idRetailer.id;\n    await APIRequest('GET', url).then(async res => {\n      if (res.data !== '') {\n        var prodotti = res.data.idPriceList2.priceListProducts;\n      } else {\n        /* Reperisco i prodotti all'interno del listino affiliato nel caso non sia presente quello del retailer */\n        url = 'pricelistaffiliate/?idAffiliate=' + this.state.documento.idDocument.idRetailer.idAffiliate;\n        await APIRequest('GET', url).then(res => {\n          prodotti = res.data.idPriceList2.priceListProducts;\n        }).catch(e => {\n          console.log(e);\n        });\n      }\n      var prod = [];\n      prodotti.forEach(element => {\n        element.idProduct2.price = element.price;\n        prod.push(element.idProduct2);\n      });\n      this.setState({\n        results2: prod\n      });\n      stopLoading();\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  hideDialog() {\n    this.setState({\n      addProdDialog: false\n    });\n  }\n  tooltipMex() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".pi-info-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"p-text-center p-text-bold\",\n        children: [Costanti.ProdAggInDoc, \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-info-circle\",\n          \"data-pr-tooltip\": \"Dalla tabella sottostante, seleziona i prodotti che vuoi aggiungere al documento.\",\n          \"data-pr-position\": \"top\",\n          \"data-pr-at\": \"top\",\n          \"data-pr-my\": \"bottom-5\",\n          style: {\n            fontSize: '1.4rem',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 83\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this);\n  }\n  addProd() {\n    if (this.state.selectedResults !== null && this.state.selectedResults.length > 0) {\n      var filter = this.state.selectedResults.filter(el => el.productsPackagings.length > 1);\n      if (filter.length === 0) {\n        this.state.selectedResults.forEach(element => {\n          var x = {\n            colliConsuntivo: 1,\n            colliPreventivo: 0,\n            eanCode: element.productsPackagings[0].eanCode,\n            externalCode: element.externalCode,\n            idProductsPackaging: element.productsPackagings[0],\n            total: element.price * element.productsPackagings[0].pcsXPackage,\n            totalTaxed: element.price * element.productsPackagings[0].pcsXPackage + element.price * element.productsPackagings[0].pcsXPackage * element.iva / 100\n          };\n          this.state.documento.idDocument.documentBodies.push(x);\n        });\n        var tasks = {\n          task: this.state.documento\n        };\n        APIRequest('PUT', 'tasks', tasks).then(res => {\n          console.log(res.data);\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo !',\n            detail: \"Il documento è modificato\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response9, _e$response0;\n          console.log(e);\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile cambiare lo stato del documento. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        });\n      } else {\n        this.toast.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"E' necessario selezionare i formati dei prodotti per procedere all'aggiunta\",\n          life: 3000\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario selezionare dei prodotti e inserirne i formati per procedere all'aggiunta\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    var _this$state$documento, _this$state$documento2, _this$state$documento3, _this$state$documento4, _this$state$documento5, _this$state$documento6, _this$state$documento7, _this$state$documento8, _this$state$documento9, _this$state$documento0, _this$state$documento1, _this$state$documento10;\n    const header = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-rows\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-input-icon-left d-block mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputText, {\n              className: \"w-100\",\n              type: \"search\",\n              onInput: e => this.setState({\n                globalFilter: e.target.value\n              }),\n              placeholder: \"Cerca...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 21\n        }, this), ((_this$state$documento = this.state.documento) === null || _this$state$documento === void 0 ? void 0 : _this$state$documento.status) !== 'canceled' && ((_this$state$documento2 = this.state.documento) === null || _this$state$documento2 === void 0 ? void 0 : _this$state$documento2.status) !== 'prepared' && ((_this$state$documento3 = this.state.documento) === null || _this$state$documento3 === void 0 ? void 0 : _this$state$documento3.status) !== 'inventoried' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end w-100\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button mx-0 justify-content-center\",\n              onClick: () => this.openNew(),\n              children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-plus-circle mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 123\n              }, this), \" \", Costanti.AggProd, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 13\n    }, this);\n    /* Footer dialogo di aggiunta prodotti */\n    const productDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        label: Costanti.Chiudi,\n        className: \"p-button-text\",\n        onClick: this.hideDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: 'externalCode',\n      header: Costanti.exCode,\n      body: 'externalCode',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'description',\n      header: Costanti.Prodotto,\n      body: 'description',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'productsAvailabilities.physicalStock',\n      header: Costanti.GiacenzaFisica,\n      body: 'physicalStock',\n      showHeader: true\n    }, {\n      field: 'productsAvailabilities.committedCustomer',\n      header: Costanti.ImpegnataCliente,\n      body: 'committedCustomer',\n      showHeader: true\n    }, {\n      field: 'productsAvailabilities.availability',\n      header: Costanti.QtaDisp,\n      body: 'productsAvailabilities',\n      showHeader: true\n    }, {\n      field: 'package',\n      header: Costanti.Formato,\n      body: 'selectFormato',\n      showHeader: true\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: [((_this$state$documento4 = this.state.documento) === null || _this$state$documento4 === void 0 ? void 0 : _this$state$documento4.idDocument.type) !== 'INVENTORY' && /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"m-0\",\n          children: [Costanti.MercUsc, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-center d-block subtitle\",\n            children: this.state.titolo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 25\n        }, this), ((_this$state$documento5 = this.state.documento) === null || _this$state$documento5 === void 0 ? void 0 : _this$state$documento5.idDocument.type) === 'INVENTORY' && /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"m-0\",\n          children: [Costanti.DocInventario, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-center d-block subtitle\",\n            children: this.state.titolo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"datatable-responsive-demo wrapper\",\n        children: /*#__PURE__*/_jsxDEV(DataTable, {\n          ref: el => this.dt = el,\n          className: \"p-datatable-responsive-demo\",\n          dataKey: \"id\",\n          autoLayout: \"true\",\n          value: this.state.results,\n          editMode: \"row\",\n          onRowEditComplete: this.onRowEditComplete,\n          onRowEditSave: this.modificaDoc,\n          header: header,\n          globalFilter: this.state.globalFilter,\n          csvSeparator: \";\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            field: \"id\",\n            header: Costanti.CodProd,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"description\",\n            header: Costanti.Nome,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"unitMeasure\",\n            header: Costanti.Formato,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"eanCode\",\n            header: Costanti.eanCode,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"giacenza\",\n            header: Costanti.QtaDisp,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"qtaOrd\",\n            header: Costanti.qtaPreventiva,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"qtaPrep\",\n            header: Costanti.qtaConsuntiva,\n            editor: options => this.qtaConsuntivaEditor(options),\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            rowEditor: true,\n            headerStyle: {\n              width: '10%',\n              minWidth: '8rem'\n            },\n            bodyStyle: {\n              textAlign: 'center'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 17\n      }, this), ((_this$state$documento6 = this.state.documento) === null || _this$state$documento6 === void 0 ? void 0 : _this$state$documento6.idDocument.type) !== 'CLI-ORDINE' && ((_this$state$documento7 = this.state.documento) === null || _this$state$documento7 === void 0 ? void 0 : _this$state$documento7.status) !== 'canceled' && ((_this$state$documento8 = this.state.documento) === null || _this$state$documento8 === void 0 ? void 0 : _this$state$documento8.status) !== 'inventoried' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          label: Costanti.ApprovaIcon,\n          onClick: e => this.creaDoc(e)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          className: \"mx-3\",\n          label: Costanti.RespingiIcon,\n          onClick: this.respingiJobs\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 21\n      }, this), ((_this$state$documento9 = this.state.documento) === null || _this$state$documento9 === void 0 ? void 0 : _this$state$documento9.status) === 'create' && ((_this$state$documento0 = this.state.documento) === null || _this$state$documento0 === void 0 ? void 0 : _this$state$documento0.idDocument.type) === 'CLI-ORDINE' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center my-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          label: Costanti.salva,\n          onClick: e => this.save(e)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 21\n      }, this), ((_this$state$documento1 = this.state.documento) === null || _this$state$documento1 === void 0 ? void 0 : _this$state$documento1.status) === 'counted' && ((_this$state$documento10 = this.state.documento) === null || _this$state$documento10 === void 0 ? void 0 : _this$state$documento10.idDocument.type) === 'CLI-ORDINE' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center my-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          label: Costanti.PassaAPreparato,\n          onClick: e => this.chiediConferma(e)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.addProdDialog,\n        header: this.tooltipMex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: productDialogFooter,\n        onHide: this.hideDialog,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"datatable-responsive-demo wrapper my-4\",\n            children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n              value: this.state.results2,\n              fields: fields,\n              dataKey: \"id\",\n              paginator: true,\n              rows: 5,\n              rowsPerPageOptions: [5, 10, 20, 50],\n              selection: this.state.selectedResults,\n              onSelectionChange: e => this.setState({\n                selectedResults: e.value\n              }),\n              responsiveLayout: \"scroll\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center my-3\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: \"w-auto\",\n              onClick: this.addProd,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-plus-circle mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 80\n              }, this), Costanti.Aggiungi]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default ConfrontoDispQtaChain;", "map": {"version": 3, "names": ["React", "Component", "Nav", "<PERSON><PERSON>", "Toast", "<PERSON><PERSON>", "APIRequest", "DataTable", "Column", "InputNumber", "confirmDialog", "Dialog", "<PERSON><PERSON><PERSON>", "InputText", "stopLoading", "CustomDataTable", "Caricamento", "chainDocumentiOrdineVendita", "jsxDEV", "_jsxDEV", "ConfrontoDispQtaChain", "constructor", "props", "emptyResult", "id", "externalCode", "brand", "nationality", "region", "format", "family", "subfamily", "group", "subgroup", "deposit", "productsPackagings", "state", "results", "results2", "documento", "titolo", "globalFilter", "result", "loading", "addProdDialog", "selectedResults", "onRowEditComplete", "bind", "modifica", "modificaDoc", "save", "openNew", "hideDialog", "addProd", "cambiaStato", "componentDidMount", "doc", "document", "JSON", "parse", "localStorage", "getItem", "url", "tasks", "then", "res", "data", "idDocument", "documentBodies", "for<PERSON>ach", "element", "_element$idProductsPa", "_element$idProductsPa2", "_element$idProductsPa3", "_element$idProductsPa4", "_element$idProductsPa5", "_element$idProductsPa6", "_element$idProductsPa7", "idProductsPackaging", "eanCode", "idProduct", "x", "docBodyId", "description", "lotto", "scadenza", "qtaOrd", "colliPreventivo", "qtaPrep", "colliConsuntivo", "unitMeasure", "pcsXPackage", "giac<PERSON>za", "productsAvailabilities", "length", "availability", "committedCustomer", "type", "_element$idProductsPa8", "push", "setState", "number", "Intl", "DateTimeFormat", "day", "month", "year", "Date", "documentDate", "catch", "e", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "componentDidUpdate", "setTimeout", "status", "tableRow", "getElementsByClassName", "Object", "entries", "rows", "classList", "add", "findClass", "item", "find", "el", "index", "_el$idProductsPackagi", "innerText", "split", "rowIndex", "task", "window", "location", "pathname", "_e$response3", "_e$response4", "creaDoc", "_e$response5", "_e$response6", "key", "options", "value", "chiediConferma", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "reject", "_e$response7", "_e$response8", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "qtaConsuntivaEditor", "onValueChange", "idRetailer", "prodotti", "idPriceList2", "priceListProducts", "idAffiliate", "prod", "idProduct2", "price", "tooltipMex", "children", "target", "className", "ProdAggInDoc", "style", "fontSize", "cursor", "filter", "total", "totalTaxed", "iva", "reload", "_e$response9", "_e$response0", "render", "_this$state$documento", "_this$state$documento2", "_this$state$documento3", "_this$state$documento4", "_this$state$documento5", "_this$state$documento6", "_this$state$documento7", "_this$state$documento8", "_this$state$documento9", "_this$state$documento0", "_this$state$documento1", "_this$state$documento10", "onInput", "placeholder", "onClick", "<PERSON>gg<PERSON><PERSON>", "productDialogFooter", "Fragment", "label", "<PERSON><PERSON>", "fields", "selectionMode", "headerStyle", "width", "field", "exCode", "body", "sortable", "showHeader", "<PERSON><PERSON><PERSON>", "GiacenzaFisica", "ImpegnataCliente", "QtaDisp", "Formato", "ref", "MercUsc", "DocInventario", "dt", "dataKey", "autoLayout", "editMode", "onRowEditSave", "csvSeparator", "CodProd", "Nome", "qtaPreventiva", "qtaConsuntiva", "editor", "rowEditor", "min<PERSON><PERSON><PERSON>", "bodyStyle", "textAlign", "ApprovaIcon", "RespingiIcon", "respingiJobs", "salva", "PassaAPreparato", "visible", "modal", "footer", "onHide", "paginator", "rowsPerPageOptions", "selection", "onSelectionChange", "responsiveLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/confrontoDispQta.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* ConfrontoDisponibilitàQuantità - operazioni di confronto sulle quantità rispetto alle disponibilità\n*\n*/\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON>nti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { InputNumber } from \"primereact/inputnumber\";\nimport { confirmDialog } from \"primereact/confirmdialog\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Tooltip } from \"primereact/tooltip\";\nimport { InputText } from \"primereact/inputtext\";\nimport { stopLoading } from \"../../components/generalizzazioni/stopLoading\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Caricamento from \"../../utils/caricamento\";\nimport { chainDocumentiOrdineVendita } from \"../../components/route\";\n\nclass ConfrontoDispQtaChain extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: '',\n        externalCode: '',\n        brand: '',\n        nationality: '',\n        region: '',\n        format: '',\n        family: '',\n        subfamily: '',\n        group: '',\n        subgroup: '',\n        deposit: '',\n        productsPackagings: []\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            documento: null,\n            titolo: '',\n            globalFilter: null,\n            result: this.emptyResult,\n            loading: true,\n            addProdDialog: false,\n            selectedResults: null\n        }\n        this.onRowEditComplete = this.onRowEditComplete.bind(this);\n        this.modifica = this.modifica.bind(this);\n        this.modificaDoc = this.modificaDoc.bind(this);\n        this.save = this.save.bind(this);\n        this.openNew = this.openNew.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.addProd = this.addProd.bind(this);\n        this.cambiaStato = this.cambiaStato.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var doc = []\n        let document = JSON.parse(localStorage.getItem(\"datiComodo\"));\n        var url = \"tasks/?idTask=\" + document.tasks.id\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                res.data[0].idDocument.documentBodies.forEach(element => {\n                    if (element.idProductsPackaging !== null) {\n                        element.eanCode = element.idProductsPackaging.eanCode;\n                    }\n                    element.externalCode = element.idProductsPackaging?.idProduct.externalCode;\n                    var x = {\n                        id: element.externalCode,\n                        docBodyId: element.id,\n                        description: element.idProductsPackaging?.idProduct.description,\n                        eanCode: element.eanCode,\n                        lotto: element.lotto,\n                        scadenza: element.scadenza,\n                        qtaOrd: element.colliPreventivo,\n                        qtaPrep: element.colliConsuntivo,\n                        unitMeasure: element.idProductsPackaging.unitMeasure + ' x ' + element.idProductsPackaging.pcsXPackage,\n                        giacenza: element.idProductsPackaging?.idProduct.productsAvailabilities.length > 0 ? element.idProductsPackaging?.idProduct.productsAvailabilities[0]?.availability - element.idProductsPackaging?.idProduct.productsAvailabilities[0]?.committedCustomer : 'Non disponibile'\n                    }\n                    if (res.data[0].idDocument.type === 'INVENTORY') {\n                        x.id = element.idProductsPackaging?.idProduct.externalCode\n                    }\n                    doc.push(x)\n                })\n                this.setState({\n                    documento: res.data[0],\n                    results: doc,\n                    loading: false,\n                    titolo: 'Documento n.' + document.tasks.idDocument.number + ' del ' + new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(document.tasks.idDocument.documentDate))\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lavorazione. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    componentDidUpdate() {\n        setTimeout(() => {\n            if (this.state.documento.status === 'counted' && this.state.documento.idDocument.type !== 'INVENTORY') {\n                var tableRow = document.getElementsByClassName('p-datatable-tbody')\n                Object.entries(tableRow[0].rows).forEach(element => {\n                    element[1].classList.add('tableRow');\n                })\n                var findClass = document.getElementsByClassName('tableRow')\n                Object.entries(findClass).forEach(item => {\n                    var find = this.state.documento.idDocument.documentBodies.find((el, index) => el.idProductsPackaging?.idProduct.externalCode === item[1].innerText.split(\"\\t\")[0] && index === item[1].rowIndex - 1)\n                    if (find !== undefined) {\n                        if (find.colliConsuntivo === find.colliPreventivo) {\n                            item[1].classList.add('matchData')\n                        } else {\n                            item[1].classList.add('misMatchData')\n                        }\n                    }\n                })\n            }\n        }, 50)\n    }\n    async save(e) {\n        var tasks = {\n            task: this.state.documento\n        }\n        tasks.task.status = 'counted'\n        await APIRequest('PUT', 'tasks', tasks)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è modificato\", life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = chainDocumentiOrdineVendita;\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = chainDocumentiOrdineVendita;\n                }, 3000)\n            })\n    }\n    async creaDoc(e) {\n        var tasks = {\n            task: this.state.documento\n        }\n        if (this.state.documento.idDocument.type === 'INVENTORY') {\n            tasks.task.status = 'inventoried'\n            await APIRequest('PUT', 'tasks', tasks)\n                .then(res => {\n                    console.log(res.data);\n                    this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è passato in stato approvato\", life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = chainDocumentiOrdineVendita;\n                    }, 3000)\n                }).catch((e) => {\n                    console.log(e)\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = chainDocumentiOrdineVendita;\n                    }, 3000)\n                })\n        }\n    }\n    modifica(e, key, options) {\n        let result = options.value[options.rowIndex];\n        result.qtaPrep = e.value\n        this.setState({\n            result,\n            index: options.rowIndex\n        })\n    }\n    chiediConferma() {\n        confirmDialog({\n            message: \"L'azione modificherà lo stato della lavorazione in preparato e sarà irreversibile continuare?\",\n            header: 'Attenzione',\n            icon: 'pi pi-exclamation-triangle',\n            acceptLabel: \"Si\",\n            rejectLabel: \"No\",\n            accept: () => this.cambiaStato(),\n            reject: null\n        });\n    }\n    async cambiaStato() {\n        var tasks = {\n            task: this.state.documento\n        }\n        tasks.task.status = 'prepared'\n        await APIRequest('PUT', 'tasks', tasks)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è passato in stato preparato\", life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = chainDocumentiOrdineVendita;\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                setTimeout(() => {\n                    window.location.pathname = chainDocumentiOrdineVendita;\n                }, 3000)\n            })\n    }\n    /* Modifico quantità del prodotto */\n    onRowEditComplete(e) {\n        let result = this.state.result;\n        <span key={result}></span>\n    }\n    /* Dropdown con selettore di formato per la modifica del moltiplicatore e del formato */\n    qtaConsuntivaEditor(options) {\n        return <InputNumber value={options.value[options.rowIndex].qtaPrep} onValueChange={(e, key) => this.modifica(e, key = 'qtaPrep', options)/* options.editorCallback(e.target.value) */} />;\n    }\n    async modificaDoc() {\n        let result = this.state.result;\n        var find = this.state.documento.idDocument.documentBodies.find(el => el.id === result.docBodyId)\n        find.colliConsuntivo = result.qtaPrep\n    }\n    async openNew() {\n        this.setState({\n            addProdDialog: true\n        })\n        var url = ''\n        /* Reperisco i prodotti all'interno del listino retailer */\n        url = 'pricelistretailer/?idRetailer=' + this.state.documento.idDocument.idRetailer.id\n        await APIRequest('GET', url)\n            .then(async res => {\n                if (res.data !== '') {\n                    var prodotti = res.data.idPriceList2.priceListProducts;\n                } else {\n                    /* Reperisco i prodotti all'interno del listino affiliato nel caso non sia presente quello del retailer */\n                    url = 'pricelistaffiliate/?idAffiliate=' + this.state.documento.idDocument.idRetailer.idAffiliate\n                    await APIRequest('GET', url)\n                        .then(res => {\n                            prodotti = res.data.idPriceList2.priceListProducts;\n                        }).catch((e) => {\n                            console.log(e)\n                        })\n\n                }\n                var prod = []\n                prodotti.forEach(element => {\n                    element.idProduct2.price = element.price\n                    prod.push(element.idProduct2)\n                })\n                this.setState({\n                    results2: prod\n                })\n                stopLoading()\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    hideDialog() {\n        this.setState({\n            addProdDialog: false\n        })\n    }\n    tooltipMex() {\n        return (\n            <div>\n                <Tooltip target=\".pi-info-circle\" />\n                <h3 className=\"p-text-center p-text-bold\">{Costanti.ProdAggInDoc} <i className=\"pi pi-info-circle\" data-pr-tooltip=\"Dalla tabella sottostante, seleziona i prodotti che vuoi aggiungere al documento.\" data-pr-position=\"top\" data-pr-at=\"top\" data-pr-my=\"bottom-5\" style={{ fontSize: '1.4rem', cursor: 'pointer' }}></i></h3>\n            </div>\n        )\n    }\n    addProd() {\n        if (this.state.selectedResults !== null && this.state.selectedResults.length > 0) {\n            var filter = this.state.selectedResults.filter(el => el.productsPackagings.length > 1)\n            if (filter.length === 0) {\n                this.state.selectedResults.forEach(element => {\n                    var x = {\n                        colliConsuntivo: 1,\n                        colliPreventivo: 0,\n                        eanCode: element.productsPackagings[0].eanCode,\n                        externalCode: element.externalCode,\n                        idProductsPackaging: element.productsPackagings[0],\n                        total: element.price * element.productsPackagings[0].pcsXPackage,\n                        totalTaxed: element.price * element.productsPackagings[0].pcsXPackage + element.price * element.productsPackagings[0].pcsXPackage * element.iva / 100\n                    }\n                    this.state.documento.idDocument.documentBodies.push(x)\n                })\n                var tasks = {\n                    task: this.state.documento\n                }\n                APIRequest('PUT', 'tasks', tasks)\n                    .then(res => {\n                        console.log(res.data);\n                        this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: \"Il documento è modificato\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile cambiare lo stato del documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    })\n            } else {\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario selezionare i formati dei prodotti per procedere all'aggiunta\", life: 3000 });\n            }\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario selezionare dei prodotti e inserirne i formati per procedere all'aggiunta\", life: 3000 });\n        }\n    }\n    render() {\n        const header = (\n            <div className=\"container-rows\">\n                <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                    <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                        <span className=\"p-input-icon-left d-block mx-auto\">\n                            <i className=\"pi pi-search mr-2\" />\n                            <InputText className=\"w-100\" type=\"search\" onInput={(e) => this.setState({ globalFilter: e.target.value })} placeholder=\"Cerca...\" />\n                        </span>\n                    </div>\n                    {this.state.documento?.status !== 'canceled' && this.state.documento?.status !== 'prepared' && this.state.documento?.status !== 'inventoried' &&\n                        <div className='col-12 col-md-6 mb-3 mb-sm-0'>\n                            <div className=\"d-flex justify-content-end w-100\">\n                                <Button className=\"p-button mx-0 justify-content-center\" onClick={() => this.openNew()} > <i className=\"pi pi-plus-circle mr-2\"></i> {Costanti.AggProd} </Button>\n                            </div>\n                        </div>\n                    }\n\n                </div>\n            </div>\n        );\n        /* Footer dialogo di aggiunta prodotti */\n        const productDialogFooter = (\n            <React.Fragment>\n                <Button label={Costanti.Chiudi} className=\"p-button-text\" onClick={this.hideDialog} />\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            { field: 'externalCode', header: Costanti.exCode, body: 'externalCode', sortable: true, showHeader: true },\n            { field: 'description', header: Costanti.Prodotto, body: 'description', sortable: true, showHeader: true },\n            { field: 'productsAvailabilities.physicalStock', header: Costanti.GiacenzaFisica, body: 'physicalStock', showHeader: true },\n            { field: 'productsAvailabilities.committedCustomer', header: Costanti.ImpegnataCliente, body: 'committedCustomer', showHeader: true },\n            { field: 'productsAvailabilities.availability', header: Costanti.QtaDisp, body: 'productsAvailabilities', showHeader: true },\n            { field: 'package', header: Costanti.Formato, body: 'selectFormato', showHeader: true }\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                <Toast ref={(el) => this.toast = el} />\n                <div>\n                    <Nav />\n                </div>\n                <div className=\"col-12 px-0 solid-head\">\n                    {this.state.documento?.idDocument.type !== 'INVENTORY' &&\n                        <h1 className=\"m-0\">{Costanti.MercUsc}\n                            <span className=\"text-center d-block subtitle\">{this.state.titolo}</span>\n                        </h1>\n                    }\n                    {this.state.documento?.idDocument.type === 'INVENTORY' &&\n                        <h1 className=\"m-0\">{Costanti.DocInventario}\n                            <span className=\"text-center d-block subtitle\">{this.state.titolo}</span>\n                        </h1>\n                    }\n                </div>\n                <div className=\"datatable-responsive-demo wrapper\">\n                    <DataTable ref={(el) => this.dt = el}\n                        className=\"p-datatable-responsive-demo\"\n                        dataKey=\"id\" autoLayout=\"true\"\n                        value={this.state.results}\n                        editMode=\"row\"\n                        onRowEditComplete={this.onRowEditComplete}\n                        onRowEditSave={this.modificaDoc}\n                        header={header}\n                        globalFilter={this.state.globalFilter}\n                        csvSeparator=\";\"\n                    >\n                        <Column field=\"id\" header={Costanti.CodProd} sortable ></Column>\n                        <Column field=\"description\" header={Costanti.Nome} sortable ></Column>\n                        <Column field=\"unitMeasure\" header={Costanti.Formato} sortable ></Column>\n                        <Column field=\"eanCode\" header={Costanti.eanCode} sortable ></Column>\n                        <Column field=\"giacenza\" header={Costanti.QtaDisp} sortable ></Column>\n                        <Column field=\"qtaOrd\" header={Costanti.qtaPreventiva} sortable ></Column>\n                        <Column field=\"qtaPrep\" header={Costanti.qtaConsuntiva} editor={(options) => this.qtaConsuntivaEditor(options)} sortable ></Column>\n                        <Column rowEditor headerStyle={{ width: '10%', minWidth: '8rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                    </DataTable>\n                </div>\n                {this.state.documento?.idDocument.type !== 'CLI-ORDINE' && this.state.documento?.status !== 'canceled' && this.state.documento?.status !== 'inventoried' &&\n                    <div className=\"d-flex justify-content-end mt-3\">\n                        <Button label={Costanti.ApprovaIcon} onClick={(e) => this.creaDoc(e)} />\n                        <Button className=\"mx-3\" label={Costanti.RespingiIcon} onClick={this.respingiJobs} />\n                    </div>\n                }\n                {\n                    this.state.documento?.status === 'create' && this.state.documento?.idDocument.type === 'CLI-ORDINE' &&\n                    <div className=\"d-flex justify-content-center my-3\">\n                        <Button label={Costanti.salva} onClick={(e) => this.save(e)} />\n                        {/* <Button className=\"mx-3\" label={Costanti.RespingiIcon} onClick={this.respingiJobs} /> */}\n                    </div>\n                }\n                {\n                    this.state.documento?.status === 'counted' && this.state.documento?.idDocument.type === 'CLI-ORDINE' &&\n                    <div className=\"d-flex justify-content-center my-3\">\n                        <Button label={Costanti.PassaAPreparato} onClick={(e) => this.chiediConferma(e)} />\n                    </div>\n                }\n                <Dialog visible={this.state.addProdDialog} header={this.tooltipMex} modal className=\"p-fluid modalBox\" footer={productDialogFooter} onHide={this.hideDialog}>\n                    <Caricamento />\n                    <div className=\"col-12\">\n                        <div className=\"datatable-responsive-demo wrapper my-4\">\n                            <CustomDataTable\n                                value={this.state.results2}\n                                fields={fields}\n                                dataKey=\"id\"\n                                paginator\n                                rows={5}\n                                rowsPerPageOptions={[5, 10, 20, 50]}\n                                selection={this.state.selectedResults}\n                                onSelectionChange={(e) => this.setState({ selectedResults: e.value })}\n                                responsiveLayout=\"scroll\"\n                            />\n                        </div>\n                        <div className=\"d-flex justify-content-center my-3\">\n                            <Button className=\"w-auto\" onClick={this.addProd} ><i className=\"pi pi-plus-circle mr-2\"></i>{Costanti.Aggiungi}</Button>\n                        </div>\n                    </div>\n                </Dialog>\n            </div >\n        )\n    }\n}\n\nexport default ConfrontoDispQtaChain;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,iCAAiC;AACjD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,2BAA2B,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,qBAAqB,SAASnB,SAAS,CAAC;EAgB1CoB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAhBhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,EAAE;MACNC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,kBAAkB,EAAE;IACxB,CAAC;IAGG,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,IAAI;MAClBC,MAAM,EAAE,IAAI,CAACnB,WAAW;MACxBoB,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE;IACrB,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,CAACH,IAAI,CAAC,IAAI,CAAC;IAChC,IAAI,CAACI,OAAO,GAAG,IAAI,CAACA,OAAO,CAACJ,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACK,UAAU,GAAG,IAAI,CAACA,UAAU,CAACL,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACM,OAAO,GAAG,IAAI,CAACA,OAAO,CAACN,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACO,WAAW,GAAG,IAAI,CAACA,WAAW,CAACP,IAAI,CAAC,IAAI,CAAC;EAClD;EACA;EACA,MAAMQ,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC7D,IAAIC,GAAG,GAAG,gBAAgB,GAAGL,QAAQ,CAACM,KAAK,CAACvC,EAAE;IAC9C,MAAMlB,UAAU,CAAC,KAAK,EAAEwD,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,UAAU,CAACC,cAAc,CAACC,OAAO,CAACC,OAAO,IAAI;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACrD,IAAIP,OAAO,CAACQ,mBAAmB,KAAK,IAAI,EAAE;UACtCR,OAAO,CAACS,OAAO,GAAGT,OAAO,CAACQ,mBAAmB,CAACC,OAAO;QACzD;QACAT,OAAO,CAAC7C,YAAY,IAAA8C,qBAAA,GAAGD,OAAO,CAACQ,mBAAmB,cAAAP,qBAAA,uBAA3BA,qBAAA,CAA6BS,SAAS,CAACvD,YAAY;QAC1E,IAAIwD,CAAC,GAAG;UACJzD,EAAE,EAAE8C,OAAO,CAAC7C,YAAY;UACxByD,SAAS,EAAEZ,OAAO,CAAC9C,EAAE;UACrB2D,WAAW,GAAAX,sBAAA,GAAEF,OAAO,CAACQ,mBAAmB,cAAAN,sBAAA,uBAA3BA,sBAAA,CAA6BQ,SAAS,CAACG,WAAW;UAC/DJ,OAAO,EAAET,OAAO,CAACS,OAAO;UACxBK,KAAK,EAAEd,OAAO,CAACc,KAAK;UACpBC,QAAQ,EAAEf,OAAO,CAACe,QAAQ;UAC1BC,MAAM,EAAEhB,OAAO,CAACiB,eAAe;UAC/BC,OAAO,EAAElB,OAAO,CAACmB,eAAe;UAChCC,WAAW,EAAEpB,OAAO,CAACQ,mBAAmB,CAACY,WAAW,GAAG,KAAK,GAAGpB,OAAO,CAACQ,mBAAmB,CAACa,WAAW;UACtGC,QAAQ,EAAE,EAAAnB,sBAAA,GAAAH,OAAO,CAACQ,mBAAmB,cAAAL,sBAAA,uBAA3BA,sBAAA,CAA6BO,SAAS,CAACa,sBAAsB,CAACC,MAAM,IAAG,CAAC,GAAG,EAAApB,sBAAA,GAAAJ,OAAO,CAACQ,mBAAmB,cAAAJ,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BM,SAAS,CAACa,sBAAsB,CAAC,CAAC,CAAC,cAAAlB,sBAAA,uBAAhEA,sBAAA,CAAkEoB,YAAY,MAAAnB,sBAAA,GAAGN,OAAO,CAACQ,mBAAmB,cAAAF,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BI,SAAS,CAACa,sBAAsB,CAAC,CAAC,CAAC,cAAAhB,sBAAA,uBAAhEA,sBAAA,CAAkEmB,iBAAiB,IAAG;QAChQ,CAAC;QACD,IAAI/B,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC8B,IAAI,KAAK,WAAW,EAAE;UAAA,IAAAC,sBAAA;UAC7CjB,CAAC,CAACzD,EAAE,IAAA0E,sBAAA,GAAG5B,OAAO,CAACQ,mBAAmB,cAAAoB,sBAAA,uBAA3BA,sBAAA,CAA6BlB,SAAS,CAACvD,YAAY;QAC9D;QACA+B,GAAG,CAAC2C,IAAI,CAAClB,CAAC,CAAC;MACf,CAAC,CAAC;MACF,IAAI,CAACmB,QAAQ,CAAC;QACV7D,SAAS,EAAE0B,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC;QACtB7B,OAAO,EAAEmB,GAAG;QACZb,OAAO,EAAE,KAAK;QACdH,MAAM,EAAE,cAAc,GAAGiB,QAAQ,CAACM,KAAK,CAACI,UAAU,CAACkC,MAAM,GAAG,OAAO,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC,CAAC7E,MAAM,CAAC,IAAI8E,IAAI,CAAClD,QAAQ,CAACM,KAAK,CAACI,UAAU,CAACyC,YAAY,CAAC;MACzN,CAAC,CAAC;IACN,CAAC,CAAC,CACDC,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACVC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,6EAAAC,MAAA,CAA0E,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAY7C,IAAI,MAAKwD,SAAS,IAAAV,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAY9C,IAAI,GAAG4C,CAAC,CAACa,OAAO,CAAE;QAC/IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAC,kBAAkBA,CAAA,EAAG;IACjBC,UAAU,CAAC,MAAM;MACb,IAAI,IAAI,CAAC1F,KAAK,CAACG,SAAS,CAACwF,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC3F,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAAC8B,IAAI,KAAK,WAAW,EAAE;QACnG,IAAI+B,QAAQ,GAAGvE,QAAQ,CAACwE,sBAAsB,CAAC,mBAAmB,CAAC;QACnEC,MAAM,CAACC,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC/D,OAAO,CAACC,OAAO,IAAI;UAChDA,OAAO,CAAC,CAAC,CAAC,CAAC+D,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QACxC,CAAC,CAAC;QACF,IAAIC,SAAS,GAAG9E,QAAQ,CAACwE,sBAAsB,CAAC,UAAU,CAAC;QAC3DC,MAAM,CAACC,OAAO,CAACI,SAAS,CAAC,CAAClE,OAAO,CAACmE,IAAI,IAAI;UACtC,IAAIC,IAAI,GAAG,IAAI,CAACrG,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAACC,cAAc,CAACqE,IAAI,CAAC,CAACC,EAAE,EAAEC,KAAK;YAAA,IAAAC,qBAAA;YAAA,OAAK,EAAAA,qBAAA,GAAAF,EAAE,CAAC5D,mBAAmB,cAAA8D,qBAAA,uBAAtBA,qBAAA,CAAwB5D,SAAS,CAACvD,YAAY,MAAK+G,IAAI,CAAC,CAAC,CAAC,CAACK,SAAS,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIH,KAAK,KAAKH,IAAI,CAAC,CAAC,CAAC,CAACO,QAAQ,GAAG,CAAC;UAAA,EAAC;UACpM,IAAIN,IAAI,KAAKf,SAAS,EAAE;YACpB,IAAIe,IAAI,CAAChD,eAAe,KAAKgD,IAAI,CAAClD,eAAe,EAAE;cAC/CiD,IAAI,CAAC,CAAC,CAAC,CAACH,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;YACtC,CAAC,MAAM;cACHE,IAAI,CAAC,CAAC,CAAC,CAACH,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;YACzC;UACJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAAE,EAAE,CAAC;EACV;EACA,MAAMpF,IAAIA,CAAC4D,CAAC,EAAE;IACV,IAAI/C,KAAK,GAAG;MACRiF,IAAI,EAAE,IAAI,CAAC5G,KAAK,CAACG;IACrB,CAAC;IACDwB,KAAK,CAACiF,IAAI,CAACjB,MAAM,GAAG,SAAS;IAC7B,MAAMzH,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEyD,KAAK,CAAC,CAClCC,IAAI,CAACC,GAAG,IAAI;MACTgD,OAAO,CAACC,GAAG,CAACjD,GAAG,CAACC,IAAI,CAAC;MACrB,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,2BAA2B;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;MAC9GE,UAAU,CAAC,MAAM;QACbmB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGlI,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC4F,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAsC,YAAA,EAAAC,YAAA;MACZpC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAA4B,YAAA,GAAAtC,CAAC,CAACW,QAAQ,cAAA2B,YAAA,uBAAVA,YAAA,CAAYlF,IAAI,MAAKwD,SAAS,IAAA2B,YAAA,GAAGvC,CAAC,CAACW,QAAQ,cAAA4B,YAAA,uBAAVA,YAAA,CAAYnF,IAAI,GAAG4C,CAAC,CAACa,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACnOE,UAAU,CAAC,MAAM;QACbmB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGlI,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC;EACV;EACA,MAAMqI,OAAOA,CAACxC,CAAC,EAAE;IACb,IAAI/C,KAAK,GAAG;MACRiF,IAAI,EAAE,IAAI,CAAC5G,KAAK,CAACG;IACrB,CAAC;IACD,IAAI,IAAI,CAACH,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAAC8B,IAAI,KAAK,WAAW,EAAE;MACtDlC,KAAK,CAACiF,IAAI,CAACjB,MAAM,GAAG,aAAa;MACjC,MAAMzH,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEyD,KAAK,CAAC,CAClCC,IAAI,CAACC,GAAG,IAAI;QACTgD,OAAO,CAACC,GAAG,CAACjD,GAAG,CAACC,IAAI,CAAC;QACrB,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAEC,OAAO,EAAE,UAAU;UAAEC,MAAM,EAAE,2CAA2C;UAAEK,IAAI,EAAE;QAAK,CAAC,CAAC;QAC9HE,UAAU,CAAC,MAAM;UACbmB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGlI,2BAA2B;QAC1D,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAAC4F,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAyC,YAAA,EAAAC,YAAA;QACZvC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAA+B,YAAA,GAAAzC,CAAC,CAACW,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAYrF,IAAI,MAAKwD,SAAS,IAAA8B,YAAA,GAAG1C,CAAC,CAACW,QAAQ,cAAA+B,YAAA,uBAAVA,YAAA,CAAYtF,IAAI,GAAG4C,CAAC,CAACa,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QACnOE,UAAU,CAAC,MAAM;UACbmB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGlI,2BAA2B;QAC1D,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV;EACJ;EACA+B,QAAQA,CAAC8D,CAAC,EAAE2C,GAAG,EAAEC,OAAO,EAAE;IACtB,IAAIhH,MAAM,GAAGgH,OAAO,CAACC,KAAK,CAACD,OAAO,CAACX,QAAQ,CAAC;IAC5CrG,MAAM,CAAC8C,OAAO,GAAGsB,CAAC,CAAC6C,KAAK;IACxB,IAAI,CAACvD,QAAQ,CAAC;MACV1D,MAAM;MACNiG,KAAK,EAAEe,OAAO,CAACX;IACnB,CAAC,CAAC;EACN;EACAa,cAAcA,CAAA,EAAG;IACblJ,aAAa,CAAC;MACViH,OAAO,EAAE,+FAA+F;MACxGkC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE,4BAA4B;MAClCC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC3G,WAAW,CAAC,CAAC;MAChC4G,MAAM,EAAE;IACZ,CAAC,CAAC;EACN;EACA,MAAM5G,WAAWA,CAAA,EAAG;IAChB,IAAIS,KAAK,GAAG;MACRiF,IAAI,EAAE,IAAI,CAAC5G,KAAK,CAACG;IACrB,CAAC;IACDwB,KAAK,CAACiF,IAAI,CAACjB,MAAM,GAAG,UAAU;IAC9B,MAAMzH,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEyD,KAAK,CAAC,CAClCC,IAAI,CAACC,GAAG,IAAI;MACTgD,OAAO,CAACC,GAAG,CAACjD,GAAG,CAACC,IAAI,CAAC;MACrB,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,2CAA2C;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;MAC9HE,UAAU,CAAC,MAAM;QACbmB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGlI,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC4F,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAqD,YAAA,EAAAC,YAAA;MACZnD,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAA2C,YAAA,GAAArD,CAAC,CAACW,QAAQ,cAAA0C,YAAA,uBAAVA,YAAA,CAAYjG,IAAI,MAAKwD,SAAS,IAAA0C,YAAA,GAAGtD,CAAC,CAACW,QAAQ,cAAA2C,YAAA,uBAAVA,YAAA,CAAYlG,IAAI,GAAG4C,CAAC,CAACa,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACnOE,UAAU,CAAC,MAAM;QACbmB,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGlI,2BAA2B;MAC1D,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC;EACV;EACA;EACA6B,iBAAiBA,CAACgE,CAAC,EAAE;IACjB,IAAIpE,MAAM,GAAG,IAAI,CAACN,KAAK,CAACM,MAAM;IAC9B,aAAAvB,OAAA,aAAWuB,MAAM;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAC9B;EACA;EACAC,mBAAmBA,CAACf,OAAO,EAAE;IACzB,oBAAOvI,OAAA,CAACV,WAAW;MAACkJ,KAAK,EAAED,OAAO,CAACC,KAAK,CAACD,OAAO,CAACX,QAAQ,CAAC,CAACvD,OAAQ;MAACkF,aAAa,EAAEA,CAAC5D,CAAC,EAAE2C,GAAG,KAAK,IAAI,CAACzG,QAAQ,CAAC8D,CAAC,EAAE2C,GAAG,GAAG,SAAS,EAAEC,OAAO,CAAC;IAA6C;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7L;EACA,MAAMvH,WAAWA,CAAA,EAAG;IAChB,IAAIP,MAAM,GAAG,IAAI,CAACN,KAAK,CAACM,MAAM;IAC9B,IAAI+F,IAAI,GAAG,IAAI,CAACrG,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAACC,cAAc,CAACqE,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAClH,EAAE,KAAKkB,MAAM,CAACwC,SAAS,CAAC;IAChGuD,IAAI,CAAChD,eAAe,GAAG/C,MAAM,CAAC8C,OAAO;EACzC;EACA,MAAMrC,OAAOA,CAAA,EAAG;IACZ,IAAI,CAACiD,QAAQ,CAAC;MACVxD,aAAa,EAAE;IACnB,CAAC,CAAC;IACF,IAAIkB,GAAG,GAAG,EAAE;IACZ;IACAA,GAAG,GAAG,gCAAgC,GAAG,IAAI,CAAC1B,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAACwG,UAAU,CAACnJ,EAAE;IACtF,MAAMlB,UAAU,CAAC,KAAK,EAAEwD,GAAG,CAAC,CACvBE,IAAI,CAAC,MAAMC,GAAG,IAAI;MACf,IAAIA,GAAG,CAACC,IAAI,KAAK,EAAE,EAAE;QACjB,IAAI0G,QAAQ,GAAG3G,GAAG,CAACC,IAAI,CAAC2G,YAAY,CAACC,iBAAiB;MAC1D,CAAC,MAAM;QACH;QACAhH,GAAG,GAAG,kCAAkC,GAAG,IAAI,CAAC1B,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAACwG,UAAU,CAACI,WAAW;QACjG,MAAMzK,UAAU,CAAC,KAAK,EAAEwD,GAAG,CAAC,CACvBE,IAAI,CAACC,GAAG,IAAI;UACT2G,QAAQ,GAAG3G,GAAG,CAACC,IAAI,CAAC2G,YAAY,CAACC,iBAAiB;QACtD,CAAC,CAAC,CAACjE,KAAK,CAAEC,CAAC,IAAK;UACZG,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QAClB,CAAC,CAAC;MAEV;MACA,IAAIkE,IAAI,GAAG,EAAE;MACbJ,QAAQ,CAACvG,OAAO,CAACC,OAAO,IAAI;QACxBA,OAAO,CAAC2G,UAAU,CAACC,KAAK,GAAG5G,OAAO,CAAC4G,KAAK;QACxCF,IAAI,CAAC7E,IAAI,CAAC7B,OAAO,CAAC2G,UAAU,CAAC;MACjC,CAAC,CAAC;MACF,IAAI,CAAC7E,QAAQ,CAAC;QACV9D,QAAQ,EAAE0I;MACd,CAAC,CAAC;MACFlK,WAAW,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC+F,KAAK,CAAEC,CAAC,IAAK;MACZG,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;IAClB,CAAC,CAAC;EACV;EACA1D,UAAUA,CAAA,EAAG;IACT,IAAI,CAACgD,QAAQ,CAAC;MACVxD,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAuI,UAAUA,CAAA,EAAG;IACT,oBACIhK,OAAA;MAAAiK,QAAA,gBACIjK,OAAA,CAACP,OAAO;QAACyK,MAAM,EAAC;MAAiB;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCrJ,OAAA;QAAImK,SAAS,EAAC,2BAA2B;QAAAF,QAAA,GAAE/K,QAAQ,CAACkL,YAAY,EAAC,GAAC,eAAApK,OAAA;UAAGmK,SAAS,EAAC,mBAAmB;UAAC,mBAAgB,mFAAmF;UAAC,oBAAiB,KAAK;UAAC,cAAW,KAAK;UAAC,cAAW,UAAU;UAACE,KAAK,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE;UAAU;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/T,CAAC;EAEd;EACAnH,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjB,KAAK,CAACS,eAAe,KAAK,IAAI,IAAI,IAAI,CAACT,KAAK,CAACS,eAAe,CAACiD,MAAM,GAAG,CAAC,EAAE;MAC9E,IAAI6F,MAAM,GAAG,IAAI,CAACvJ,KAAK,CAACS,eAAe,CAAC8I,MAAM,CAACjD,EAAE,IAAIA,EAAE,CAACvG,kBAAkB,CAAC2D,MAAM,GAAG,CAAC,CAAC;MACtF,IAAI6F,MAAM,CAAC7F,MAAM,KAAK,CAAC,EAAE;QACrB,IAAI,CAAC1D,KAAK,CAACS,eAAe,CAACwB,OAAO,CAACC,OAAO,IAAI;UAC1C,IAAIW,CAAC,GAAG;YACJQ,eAAe,EAAE,CAAC;YAClBF,eAAe,EAAE,CAAC;YAClBR,OAAO,EAAET,OAAO,CAACnC,kBAAkB,CAAC,CAAC,CAAC,CAAC4C,OAAO;YAC9CtD,YAAY,EAAE6C,OAAO,CAAC7C,YAAY;YAClCqD,mBAAmB,EAAER,OAAO,CAACnC,kBAAkB,CAAC,CAAC,CAAC;YAClDyJ,KAAK,EAAEtH,OAAO,CAAC4G,KAAK,GAAG5G,OAAO,CAACnC,kBAAkB,CAAC,CAAC,CAAC,CAACwD,WAAW;YAChEkG,UAAU,EAAEvH,OAAO,CAAC4G,KAAK,GAAG5G,OAAO,CAACnC,kBAAkB,CAAC,CAAC,CAAC,CAACwD,WAAW,GAAGrB,OAAO,CAAC4G,KAAK,GAAG5G,OAAO,CAACnC,kBAAkB,CAAC,CAAC,CAAC,CAACwD,WAAW,GAAGrB,OAAO,CAACwH,GAAG,GAAG;UACtJ,CAAC;UACD,IAAI,CAAC1J,KAAK,CAACG,SAAS,CAAC4B,UAAU,CAACC,cAAc,CAAC+B,IAAI,CAAClB,CAAC,CAAC;QAC1D,CAAC,CAAC;QACF,IAAIlB,KAAK,GAAG;UACRiF,IAAI,EAAE,IAAI,CAAC5G,KAAK,CAACG;QACrB,CAAC;QACDjC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAEyD,KAAK,CAAC,CAC5BC,IAAI,CAACC,GAAG,IAAI;UACTgD,OAAO,CAACC,GAAG,CAACjD,GAAG,CAACC,IAAI,CAAC;UACrB,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,UAAU;YAAEC,MAAM,EAAE,2BAA2B;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;UAC9GE,UAAU,CAAC,MAAM;YACbmB,MAAM,CAACC,QAAQ,CAAC6C,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAClF,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAkF,YAAA,EAAAC,YAAA;UACZhF,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;UACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,iFAAAC,MAAA,CAA8E,EAAAwE,YAAA,GAAAlF,CAAC,CAACW,QAAQ,cAAAuE,YAAA,uBAAVA,YAAA,CAAY9H,IAAI,MAAKwD,SAAS,IAAAuE,YAAA,GAAGnF,CAAC,CAACW,QAAQ,cAAAwE,YAAA,uBAAVA,YAAA,CAAY/H,IAAI,GAAG4C,CAAC,CAACa,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UACnOE,UAAU,CAAC,MAAM;YACbmB,MAAM,CAACC,QAAQ,CAAC6C,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC;MACV,CAAC,MAAM;QACH,IAAI,CAAC5E,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,EAAE,6EAA6E;UAAEK,IAAI,EAAE;QAAK,CAAC,CAAC;MACzK;IACJ,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,yFAAyF;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;IACrL;EACJ;EACAsE,MAAMA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;IACL,MAAMjD,MAAM,gBACR1I,OAAA;MAAKmK,SAAS,EAAC,gBAAgB;MAAAF,QAAA,eAC3BjK,OAAA;QAAKmK,SAAS,EAAC,2DAA2D;QAAAF,QAAA,gBACtEjK,OAAA;UAAKmK,SAAS,EAAC,8BAA8B;UAAAF,QAAA,eACzCjK,OAAA;YAAMmK,SAAS,EAAC,mCAAmC;YAAAF,QAAA,gBAC/CjK,OAAA;cAAGmK,SAAS,EAAC;YAAmB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCrJ,OAAA,CAACN,SAAS;cAACyK,SAAS,EAAC,OAAO;cAACrF,IAAI,EAAC,QAAQ;cAAC8G,OAAO,EAAGjG,CAAC,IAAK,IAAI,CAACV,QAAQ,CAAC;gBAAE3D,YAAY,EAAEqE,CAAC,CAACuE,MAAM,CAAC1B;cAAM,CAAC,CAAE;cAACqD,WAAW,EAAC;YAAU;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL,EAAA2B,qBAAA,OAAI,CAAC/J,KAAK,CAACG,SAAS,cAAA4J,qBAAA,uBAApBA,qBAAA,CAAsBpE,MAAM,MAAK,UAAU,IAAI,EAAAqE,sBAAA,OAAI,CAAChK,KAAK,CAACG,SAAS,cAAA6J,sBAAA,uBAApBA,sBAAA,CAAsBrE,MAAM,MAAK,UAAU,IAAI,EAAAsE,sBAAA,OAAI,CAACjK,KAAK,CAACG,SAAS,cAAA8J,sBAAA,uBAApBA,sBAAA,CAAsBtE,MAAM,MAAK,aAAa,iBACzI5G,OAAA;UAAKmK,SAAS,EAAC,8BAA8B;UAAAF,QAAA,eACzCjK,OAAA;YAAKmK,SAAS,EAAC,kCAAkC;YAAAF,QAAA,eAC7CjK,OAAA,CAAChB,MAAM;cAACmL,SAAS,EAAC,sCAAsC;cAAC2B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9J,OAAO,CAAC,CAAE;cAAAiI,QAAA,GAAE,GAAC,eAAAjK,OAAA;gBAAGmK,SAAS,EAAC;cAAwB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,KAAC,EAACnK,QAAQ,CAAC6M,OAAO,EAAC,GAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD;IACA,MAAM2C,mBAAmB,gBACrBhM,OAAA,CAACnB,KAAK,CAACoN,QAAQ;MAAAhC,QAAA,eACXjK,OAAA,CAAChB,MAAM;QAACkN,KAAK,EAAEhN,QAAQ,CAACiN,MAAO;QAAChC,SAAS,EAAC,eAAe;QAAC2B,OAAO,EAAE,IAAI,CAAC7J;MAAW;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACnB;IACD,MAAM+C,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MAAEC,KAAK,EAAE,cAAc;MAAE9D,MAAM,EAAExJ,QAAQ,CAACuN,MAAM;MAAEC,IAAI,EAAE,cAAc;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC1G;MAAEJ,KAAK,EAAE,aAAa;MAAE9D,MAAM,EAAExJ,QAAQ,CAAC2N,QAAQ;MAAEH,IAAI,EAAE,aAAa;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC1G;MAAEJ,KAAK,EAAE,sCAAsC;MAAE9D,MAAM,EAAExJ,QAAQ,CAAC4N,cAAc;MAAEJ,IAAI,EAAE,eAAe;MAAEE,UAAU,EAAE;IAAK,CAAC,EAC3H;MAAEJ,KAAK,EAAE,0CAA0C;MAAE9D,MAAM,EAAExJ,QAAQ,CAAC6N,gBAAgB;MAAEL,IAAI,EAAE,mBAAmB;MAAEE,UAAU,EAAE;IAAK,CAAC,EACrI;MAAEJ,KAAK,EAAE,qCAAqC;MAAE9D,MAAM,EAAExJ,QAAQ,CAAC8N,OAAO;MAAEN,IAAI,EAAE,wBAAwB;MAAEE,UAAU,EAAE;IAAK,CAAC,EAC5H;MAAEJ,KAAK,EAAE,SAAS;MAAE9D,MAAM,EAAExJ,QAAQ,CAAC+N,OAAO;MAAEP,IAAI,EAAE,eAAe;MAAEE,UAAU,EAAE;IAAK,CAAC,CAC1F;IACD,oBACI5M,OAAA;MAAKmK,SAAS,EAAC,mCAAmC;MAAAF,QAAA,gBAC9CjK,OAAA,CAACf,KAAK;QAACiO,GAAG,EAAG3F,EAAE,IAAK,IAAI,CAACvB,KAAK,GAAGuB;MAAG;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCrJ,OAAA;QAAAiK,QAAA,eACIjK,OAAA,CAACjB,GAAG;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNrJ,OAAA;QAAKmK,SAAS,EAAC,wBAAwB;QAAAF,QAAA,GAClC,EAAAkB,sBAAA,OAAI,CAAClK,KAAK,CAACG,SAAS,cAAA+J,sBAAA,uBAApBA,sBAAA,CAAsBnI,UAAU,CAAC8B,IAAI,MAAK,WAAW,iBAClD9E,OAAA;UAAImK,SAAS,EAAC,KAAK;UAAAF,QAAA,GAAE/K,QAAQ,CAACiO,OAAO,eACjCnN,OAAA;YAAMmK,SAAS,EAAC,8BAA8B;YAAAF,QAAA,EAAE,IAAI,CAAChJ,KAAK,CAACI;UAAM;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,EAER,EAAA+B,sBAAA,OAAI,CAACnK,KAAK,CAACG,SAAS,cAAAgK,sBAAA,uBAApBA,sBAAA,CAAsBpI,UAAU,CAAC8B,IAAI,MAAK,WAAW,iBAClD9E,OAAA;UAAImK,SAAS,EAAC,KAAK;UAAAF,QAAA,GAAE/K,QAAQ,CAACkO,aAAa,eACvCpN,OAAA;YAAMmK,SAAS,EAAC,8BAA8B;YAAAF,QAAA,EAAE,IAAI,CAAChJ,KAAK,CAACI;UAAM;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,eACNrJ,OAAA;QAAKmK,SAAS,EAAC,mCAAmC;QAAAF,QAAA,eAC9CjK,OAAA,CAACZ,SAAS;UAAC8N,GAAG,EAAG3F,EAAE,IAAK,IAAI,CAAC8F,EAAE,GAAG9F,EAAG;UACjC4C,SAAS,EAAC,6BAA6B;UACvCmD,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAC9B/E,KAAK,EAAE,IAAI,CAACvH,KAAK,CAACC,OAAQ;UAC1BsM,QAAQ,EAAC,KAAK;UACd7L,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;UAC1C8L,aAAa,EAAE,IAAI,CAAC3L,WAAY;UAChC4G,MAAM,EAAEA,MAAO;UACfpH,YAAY,EAAE,IAAI,CAACL,KAAK,CAACK,YAAa;UACtCoM,YAAY,EAAC,GAAG;UAAAzD,QAAA,gBAEhBjK,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,IAAI;YAAC9D,MAAM,EAAExJ,QAAQ,CAACyO,OAAQ;YAAChB,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAChErJ,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,aAAa;YAAC9D,MAAM,EAAExJ,QAAQ,CAAC0O,IAAK;YAACjB,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACtErJ,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,aAAa;YAAC9D,MAAM,EAAExJ,QAAQ,CAAC+N,OAAQ;YAACN,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACzErJ,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,SAAS;YAAC9D,MAAM,EAAExJ,QAAQ,CAAC0E,OAAQ;YAAC+I,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACrErJ,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,UAAU;YAAC9D,MAAM,EAAExJ,QAAQ,CAAC8N,OAAQ;YAACL,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACtErJ,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,QAAQ;YAAC9D,MAAM,EAAExJ,QAAQ,CAAC2O,aAAc;YAAClB,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC1ErJ,OAAA,CAACX,MAAM;YAACmN,KAAK,EAAC,SAAS;YAAC9D,MAAM,EAAExJ,QAAQ,CAAC4O,aAAc;YAACC,MAAM,EAAGxF,OAAO,IAAK,IAAI,CAACe,mBAAmB,CAACf,OAAO,CAAE;YAACoE,QAAQ;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACnIrJ,OAAA,CAACX,MAAM;YAAC2O,SAAS;YAAC1B,WAAW,EAAE;cAAEC,KAAK,EAAE,KAAK;cAAE0B,QAAQ,EAAE;YAAO,CAAE;YAACC,SAAS,EAAE;cAAEC,SAAS,EAAE;YAAS;UAAE;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,EACL,EAAAgC,sBAAA,OAAI,CAACpK,KAAK,CAACG,SAAS,cAAAiK,sBAAA,uBAApBA,sBAAA,CAAsBrI,UAAU,CAAC8B,IAAI,MAAK,YAAY,IAAI,EAAAwG,sBAAA,OAAI,CAACrK,KAAK,CAACG,SAAS,cAAAkK,sBAAA,uBAApBA,sBAAA,CAAsB1E,MAAM,MAAK,UAAU,IAAI,EAAA2E,sBAAA,OAAI,CAACtK,KAAK,CAACG,SAAS,cAAAmK,sBAAA,uBAApBA,sBAAA,CAAsB3E,MAAM,MAAK,aAAa,iBACpJ5G,OAAA;QAAKmK,SAAS,EAAC,iCAAiC;QAAAF,QAAA,gBAC5CjK,OAAA,CAAChB,MAAM;UAACkN,KAAK,EAAEhN,QAAQ,CAACkP,WAAY;UAACtC,OAAO,EAAGnG,CAAC,IAAK,IAAI,CAACwC,OAAO,CAACxC,CAAC;QAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxErJ,OAAA,CAAChB,MAAM;UAACmL,SAAS,EAAC,MAAM;UAAC+B,KAAK,EAAEhN,QAAQ,CAACmP,YAAa;UAACvC,OAAO,EAAE,IAAI,CAACwC;QAAa;UAAApF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,EAGN,EAAAmC,sBAAA,OAAI,CAACvK,KAAK,CAACG,SAAS,cAAAoK,sBAAA,uBAApBA,sBAAA,CAAsB5E,MAAM,MAAK,QAAQ,IAAI,EAAA6E,sBAAA,OAAI,CAACxK,KAAK,CAACG,SAAS,cAAAqK,sBAAA,uBAApBA,sBAAA,CAAsBzI,UAAU,CAAC8B,IAAI,MAAK,YAAY,iBACnG9E,OAAA;QAAKmK,SAAS,EAAC,oCAAoC;QAAAF,QAAA,eAC/CjK,OAAA,CAAChB,MAAM;UAACkN,KAAK,EAAEhN,QAAQ,CAACqP,KAAM;UAACzC,OAAO,EAAGnG,CAAC,IAAK,IAAI,CAAC5D,IAAI,CAAC4D,CAAC;QAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE9D,CAAC,EAGN,EAAAqC,sBAAA,OAAI,CAACzK,KAAK,CAACG,SAAS,cAAAsK,sBAAA,uBAApBA,sBAAA,CAAsB9E,MAAM,MAAK,SAAS,IAAI,EAAA+E,uBAAA,OAAI,CAAC1K,KAAK,CAACG,SAAS,cAAAuK,uBAAA,uBAApBA,uBAAA,CAAsB3I,UAAU,CAAC8B,IAAI,MAAK,YAAY,iBACpG9E,OAAA;QAAKmK,SAAS,EAAC,oCAAoC;QAAAF,QAAA,eAC/CjK,OAAA,CAAChB,MAAM;UAACkN,KAAK,EAAEhN,QAAQ,CAACsP,eAAgB;UAAC1C,OAAO,EAAGnG,CAAC,IAAK,IAAI,CAAC8C,cAAc,CAAC9C,CAAC;QAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eAEVrJ,OAAA,CAACR,MAAM;QAACiP,OAAO,EAAE,IAAI,CAACxN,KAAK,CAACQ,aAAc;QAACiH,MAAM,EAAE,IAAI,CAACsB,UAAW;QAAC0E,KAAK;QAACvE,SAAS,EAAC,kBAAkB;QAACwE,MAAM,EAAE3C,mBAAoB;QAAC4C,MAAM,EAAE,IAAI,CAAC3M,UAAW;QAAAgI,QAAA,gBACxJjK,OAAA,CAACH,WAAW;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfrJ,OAAA;UAAKmK,SAAS,EAAC,QAAQ;UAAAF,QAAA,gBACnBjK,OAAA;YAAKmK,SAAS,EAAC,wCAAwC;YAAAF,QAAA,eACnDjK,OAAA,CAACJ,eAAe;cACZ4I,KAAK,EAAE,IAAI,CAACvH,KAAK,CAACE,QAAS;cAC3BiL,MAAM,EAAEA,MAAO;cACfkB,OAAO,EAAC,IAAI;cACZuB,SAAS;cACT5H,IAAI,EAAE,CAAE;cACR6H,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;cACpCC,SAAS,EAAE,IAAI,CAAC9N,KAAK,CAACS,eAAgB;cACtCsN,iBAAiB,EAAGrJ,CAAC,IAAK,IAAI,CAACV,QAAQ,CAAC;gBAAEvD,eAAe,EAAEiE,CAAC,CAAC6C;cAAM,CAAC,CAAE;cACtEyG,gBAAgB,EAAC;YAAQ;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNrJ,OAAA;YAAKmK,SAAS,EAAC,oCAAoC;YAAAF,QAAA,eAC/CjK,OAAA,CAAChB,MAAM;cAACmL,SAAS,EAAC,QAAQ;cAAC2B,OAAO,EAAE,IAAI,CAAC5J,OAAQ;cAAA+H,QAAA,gBAAEjK,OAAA;gBAAGmK,SAAS,EAAC;cAAwB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAACnK,QAAQ,CAACgQ,QAAQ;YAAA;cAAAhG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEf;AACJ;AAEA,eAAepJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}