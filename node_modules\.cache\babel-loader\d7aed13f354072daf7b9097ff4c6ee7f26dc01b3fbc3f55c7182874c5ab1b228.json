{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"icon\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"twoToneColor\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Context from './Context';\nimport ReactIcon from './IconBase';\nimport { getTwoToneColor, setTwoToneColor } from './twoTonePrimaryColor';\nimport { normalizeTwoToneColors } from '../utils'; // Initial setting\n// should move it to antd main repo?\n\nsetTwoToneColor('#1890ff');\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var className = props.className,\n    icon = props.icon,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    twoToneColor = props.twoToneColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre;\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(icon.name), !!icon.name), _defineProperty(_classNames, \"\".concat(prefixCls, \"-spin\"), !!spin || icon.name === 'loading'), _classNames), className);\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return /*#__PURE__*/React.createElement(\"span\", _objectSpread(_objectSpread({\n    role: \"img\",\n    \"aria-label\": icon.name\n  }, restProps), {}, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), /*#__PURE__*/React.createElement(ReactIcon, {\n    icon: icon,\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor,\n    style: svgStyle\n  }));\n});\nIcon.displayName = 'AntdIcon';\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "_defineProperty", "_objectWithoutProperties", "_excluded", "React", "classNames", "Context", "ReactIcon", "getTwoToneColor", "setTwoToneColor", "normalizeTwoToneColors", "Icon", "forwardRef", "props", "ref", "_classNames", "className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor", "restProps", "_React$useContext", "useContext", "_React$useContext$pre", "prefixCls", "classString", "concat", "name", "iconTabIndex", "undefined", "svgStyle", "msTransform", "transform", "_normalizeTwoToneColo", "_normalizeTwoToneColo2", "primaryColor", "secondaryColor", "createElement", "role", "style", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons/es/components/AntdIcon.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"icon\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"twoToneColor\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Context from './Context';\nimport ReactIcon from './IconBase';\nimport { getTwoToneColor, setTwoToneColor } from './twoTonePrimaryColor';\nimport { normalizeTwoToneColors } from '../utils'; // Initial setting\n// should move it to antd main repo?\n\nsetTwoToneColor('#1890ff');\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n\n  var className = props.className,\n      icon = props.icon,\n      spin = props.spin,\n      rotate = props.rotate,\n      tabIndex = props.tabIndex,\n      onClick = props.onClick,\n      twoToneColor = props.twoToneColor,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var _React$useContext = React.useContext(Context),\n      _React$useContext$pre = _React$useContext.prefixCls,\n      prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre;\n\n  var classString = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(icon.name), !!icon.name), _defineProperty(_classNames, \"\".concat(prefixCls, \"-spin\"), !!spin || icon.name === 'loading'), _classNames), className);\n  var iconTabIndex = tabIndex;\n\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n      _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n      primaryColor = _normalizeTwoToneColo2[0],\n      secondaryColor = _normalizeTwoToneColo2[1];\n\n  return /*#__PURE__*/React.createElement(\"span\", _objectSpread(_objectSpread({\n    role: \"img\",\n    \"aria-label\": icon.name\n  }, restProps), {}, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), /*#__PURE__*/React.createElement(ReactIcon, {\n    icon: icon,\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor,\n    style: svgStyle\n  }));\n});\nIcon.displayName = 'AntdIcon';\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC;AAC9F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,EAAEC,eAAe,QAAQ,uBAAuB;AACxE,SAASC,sBAAsB,QAAQ,UAAU,CAAC,CAAC;AACnD;;AAEAD,eAAe,CAAC,SAAS,CAAC;AAC1B,IAAIE,IAAI,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,WAAW;EAEf,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,IAAI,GAAGL,KAAK,CAACK,IAAI;IACjBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,SAAS,GAAGrB,wBAAwB,CAACW,KAAK,EAAEV,SAAS,CAAC;EAE1D,IAAIqB,iBAAiB,GAAGpB,KAAK,CAACqB,UAAU,CAACnB,OAAO,CAAC;IAC7CoB,qBAAqB,GAAGF,iBAAiB,CAACG,SAAS;IACnDA,SAAS,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,qBAAqB;EAEpF,IAAIE,WAAW,GAAGvB,UAAU,CAACsB,SAAS,GAAGZ,WAAW,GAAG,CAAC,CAAC,EAAEd,eAAe,CAACc,WAAW,EAAE,EAAE,CAACc,MAAM,CAACF,SAAS,EAAE,GAAG,CAAC,CAACE,MAAM,CAACZ,IAAI,CAACa,IAAI,CAAC,EAAE,CAAC,CAACb,IAAI,CAACa,IAAI,CAAC,EAAE7B,eAAe,CAACc,WAAW,EAAE,EAAE,CAACc,MAAM,CAACF,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAACT,IAAI,IAAID,IAAI,CAACa,IAAI,KAAK,SAAS,CAAC,EAAEf,WAAW,GAAGC,SAAS,CAAC;EAC3Q,IAAIe,YAAY,GAAGX,QAAQ;EAE3B,IAAIW,YAAY,KAAKC,SAAS,IAAIX,OAAO,EAAE;IACzCU,YAAY,GAAG,CAAC,CAAC;EACnB;EAEA,IAAIE,QAAQ,GAAGd,MAAM,GAAG;IACtBe,WAAW,EAAE,SAAS,CAACL,MAAM,CAACV,MAAM,EAAE,MAAM,CAAC;IAC7CgB,SAAS,EAAE,SAAS,CAACN,MAAM,CAACV,MAAM,EAAE,MAAM;EAC5C,CAAC,GAAGa,SAAS;EAEb,IAAII,qBAAqB,GAAG1B,sBAAsB,CAACY,YAAY,CAAC;IAC5De,sBAAsB,GAAGrC,cAAc,CAACoC,qBAAqB,EAAE,CAAC,CAAC;IACjEE,YAAY,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACxCE,cAAc,GAAGF,sBAAsB,CAAC,CAAC,CAAC;EAE9C,OAAO,aAAajC,KAAK,CAACoC,aAAa,CAAC,MAAM,EAAEzC,aAAa,CAACA,aAAa,CAAC;IAC1E0C,IAAI,EAAE,KAAK;IACX,YAAY,EAAExB,IAAI,CAACa;EACrB,CAAC,EAAEP,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;IACjBT,GAAG,EAAEA,GAAG;IACRM,QAAQ,EAAEW,YAAY;IACtBV,OAAO,EAAEA,OAAO;IAChBL,SAAS,EAAEY;EACb,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAACoC,aAAa,CAACjC,SAAS,EAAE;IAC9CU,IAAI,EAAEA,IAAI;IACVqB,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA,cAAc;IAC9BG,KAAK,EAAET;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFtB,IAAI,CAACgC,WAAW,GAAG,UAAU;AAC7BhC,IAAI,CAACH,eAAe,GAAGA,eAAe;AACtCG,IAAI,CAACF,eAAe,GAAGA,eAAe;AACtC,eAAeE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}