{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nexport default (function (rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var checkedKeys = rawLabeledValues.map(function (_ref) {\n      var value = _ref.value;\n      return value;\n    });\n    var halfCheckedKeys = rawHalfCheckedValues.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n    if (treeConduction) {\n      var _conductCheck = conductCheck(checkedKeys, true, keyEntities);\n      checkedKeys = _conductCheck.checkedKeys;\n      halfCheckedKeys = _conductCheck.halfCheckedKeys;\n    }\n    return [\n    // Checked keys should fill with missing keys which should de-duplicated\n    Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(checkedKeys)))),\n    // Half checked keys\n    halfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n});", "map": {"version": 3, "names": ["_toConsumableArray", "React", "conduct<PERSON>heck", "rawLabeledValues", "rawHalfCheckedValues", "treeConduction", "keyEntities", "useMemo", "checked<PERSON>eys", "map", "_ref", "value", "halfC<PERSON>cked<PERSON>eys", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "filter", "key", "_conductCheck", "Array", "from", "Set", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree-select/es/hooks/useCheckedKeys.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nexport default (function (rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var checkedKeys = rawLabeledValues.map(function (_ref) {\n      var value = _ref.value;\n      return value;\n    });\n    var halfCheckedKeys = rawHalfCheckedValues.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n\n    if (treeConduction) {\n      var _conductCheck = conductCheck(checkedKeys, true, keyEntities);\n\n      checkedKeys = _conductCheck.checkedKeys;\n      halfCheckedKeys = _conductCheck.halfCheckedKeys;\n    }\n\n    return [// Checked keys should fill with missing keys which should de-duplicated\n    Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(checkedKeys)))), // Half checked keys\n    halfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n});"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,gBAAgB,UAAUC,gBAAgB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,WAAW,EAAE;EAC7F,OAAOL,KAAK,CAACM,OAAO,CAAC,YAAY;IAC/B,IAAIC,WAAW,GAAGL,gBAAgB,CAACM,GAAG,CAAC,UAAUC,IAAI,EAAE;MACrD,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACtB,OAAOA,KAAK;IACd,CAAC,CAAC;IACF,IAAIC,eAAe,GAAGR,oBAAoB,CAACK,GAAG,CAAC,UAAUI,KAAK,EAAE;MAC9D,IAAIF,KAAK,GAAGE,KAAK,CAACF,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACF,IAAIG,aAAa,GAAGN,WAAW,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAE;MACpD,OAAO,CAACV,WAAW,CAACU,GAAG,CAAC;IAC1B,CAAC,CAAC;IAEF,IAAIX,cAAc,EAAE;MAClB,IAAIY,aAAa,GAAGf,YAAY,CAACM,WAAW,EAAE,IAAI,EAAEF,WAAW,CAAC;MAEhEE,WAAW,GAAGS,aAAa,CAACT,WAAW;MACvCI,eAAe,GAAGK,aAAa,CAACL,eAAe;IACjD;IAEA,OAAO;IAAC;IACRM,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,EAAE,CAACC,MAAM,CAACrB,kBAAkB,CAACc,aAAa,CAAC,EAAEd,kBAAkB,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC;IAAE;IACpGI,eAAe,CAAC;EAClB,CAAC,EAAE,CAACT,gBAAgB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,WAAW,CAAC,CAAC;AAC3E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}