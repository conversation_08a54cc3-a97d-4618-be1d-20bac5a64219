{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"],\n  _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nfunction convertNodeToOption(node) {\n  var key = node.key,\n    _node$props = node.props,\n    children = _node$props.children,\n    value = _node$props.value,\n    restProps = _objectWithoutProperties(_node$props, _excluded);\n  return _objectSpread({\n    key: key,\n    value: value !== undefined ? value : key,\n    children: children\n  }, restProps);\n}\nexport function convertChildrenToData(nodes) {\n  var optionOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return toArray(nodes).map(function (node, index) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var isSelectOptGroup = node.type.isSelectOptGroup,\n      key = node.key,\n      _node$props2 = node.props,\n      children = _node$props2.children,\n      restProps = _objectWithoutProperties(_node$props2, _excluded2);\n    if (optionOnly || !isSelectOptGroup) {\n      return convertNodeToOption(node);\n    }\n    return _objectSpread(_objectSpread({\n      key: \"__RC_SELECT_GRP__\".concat(key === null ? index : key, \"__\"),\n      label: key\n    }, restProps), {}, {\n      options: convertChildrenToData(children)\n    });\n  }).filter(function (data) {\n    return data;\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "toArray", "convertNodeToOption", "node", "key", "_node$props", "props", "children", "value", "restProps", "undefined", "convertChildrenToData", "nodes", "optionOnly", "arguments", "length", "map", "index", "isValidElement", "type", "isSelectOptGroup", "_node$props2", "concat", "label", "options", "filter", "data"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/utils/legacyUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"],\n    _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\n\nfunction convertNodeToOption(node) {\n  var key = node.key,\n      _node$props = node.props,\n      children = _node$props.children,\n      value = _node$props.value,\n      restProps = _objectWithoutProperties(_node$props, _excluded);\n\n  return _objectSpread({\n    key: key,\n    value: value !== undefined ? value : key,\n    children: children\n  }, restProps);\n}\n\nexport function convertChildrenToData(nodes) {\n  var optionOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return toArray(nodes).map(function (node, index) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n\n    var isSelectOptGroup = node.type.isSelectOptGroup,\n        key = node.key,\n        _node$props2 = node.props,\n        children = _node$props2.children,\n        restProps = _objectWithoutProperties(_node$props2, _excluded2);\n\n    if (optionOnly || !isSelectOptGroup) {\n      return convertNodeToOption(node);\n    }\n\n    return _objectSpread(_objectSpread({\n      key: \"__RC_SELECT_GRP__\".concat(key === null ? index : key, \"__\"),\n      label: key\n    }, restProps), {}, {\n      options: convertChildrenToData(children)\n    });\n  }).filter(function (data) {\n    return data;\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC;EACjCC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AAEjD,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;IACdC,WAAW,GAAGF,IAAI,CAACG,KAAK;IACxBC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;IAC/BC,KAAK,GAAGH,WAAW,CAACG,KAAK;IACzBC,SAAS,GAAGZ,wBAAwB,CAACQ,WAAW,EAAEP,SAAS,CAAC;EAEhE,OAAOF,aAAa,CAAC;IACnBQ,GAAG,EAAEA,GAAG;IACRI,KAAK,EAAEA,KAAK,KAAKE,SAAS,GAAGF,KAAK,GAAGJ,GAAG;IACxCG,QAAQ,EAAEA;EACZ,CAAC,EAAEE,SAAS,CAAC;AACf;AAEA,OAAO,SAASE,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKJ,SAAS,GAAGI,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC1F,OAAOb,OAAO,CAACW,KAAK,CAAC,CAACI,GAAG,CAAC,UAAUb,IAAI,EAAEc,KAAK,EAAE;IAC/C,IAAI,EAAE,aAAajB,KAAK,CAACkB,cAAc,CAACf,IAAI,CAAC,IAAI,CAACA,IAAI,CAACgB,IAAI,EAAE;MAC3D,OAAO,IAAI;IACb;IAEA,IAAIC,gBAAgB,GAAGjB,IAAI,CAACgB,IAAI,CAACC,gBAAgB;MAC7ChB,GAAG,GAAGD,IAAI,CAACC,GAAG;MACdiB,YAAY,GAAGlB,IAAI,CAACG,KAAK;MACzBC,QAAQ,GAAGc,YAAY,CAACd,QAAQ;MAChCE,SAAS,GAAGZ,wBAAwB,CAACwB,YAAY,EAAEtB,UAAU,CAAC;IAElE,IAAIc,UAAU,IAAI,CAACO,gBAAgB,EAAE;MACnC,OAAOlB,mBAAmB,CAACC,IAAI,CAAC;IAClC;IAEA,OAAOP,aAAa,CAACA,aAAa,CAAC;MACjCQ,GAAG,EAAE,mBAAmB,CAACkB,MAAM,CAAClB,GAAG,KAAK,IAAI,GAAGa,KAAK,GAAGb,GAAG,EAAE,IAAI,CAAC;MACjEmB,KAAK,EAAEnB;IACT,CAAC,EAAEK,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACjBe,OAAO,EAAEb,qBAAqB,CAACJ,QAAQ;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC,CAACkB,MAAM,CAAC,UAAUC,IAAI,EAAE;IACxB,OAAOA,IAAI;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}