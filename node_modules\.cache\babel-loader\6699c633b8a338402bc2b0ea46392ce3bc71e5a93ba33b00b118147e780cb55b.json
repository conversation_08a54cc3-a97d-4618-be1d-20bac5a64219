{"ast": null, "code": "import * as React from 'react';\nexport default function getRanges(_ref) {\n  var prefixCls = _ref.prefixCls,\n    _ref$rangeList = _ref.rangeList,\n    rangeList = _ref$rangeList === void 0 ? [] : _ref$rangeList,\n    _ref$components = _ref.components,\n    components = _ref$components === void 0 ? {} : _ref$components,\n    needConfirmButton = _ref.needConfirmButton,\n    onNow = _ref.onNow,\n    onOk = _ref.onOk,\n    okDisabled = _ref.okDisabled,\n    showNow = _ref.showNow,\n    locale = _ref.locale;\n  var presetNode;\n  var okNode;\n  if (rangeList.length) {\n    var Item = components.rangeItem || 'span';\n    presetNode = /*#__PURE__*/React.createElement(React.Fragment, null, rangeList.map(function (_ref2) {\n      var label = _ref2.label,\n        onClick = _ref2.onClick,\n        onMouseEnter = _ref2.onMouseEnter,\n        onMouseLeave = _ref2.onMouseLeave;\n      return /*#__PURE__*/React.createElement(\"li\", {\n        key: label,\n        className: \"\".concat(prefixCls, \"-preset\")\n      }, /*#__PURE__*/React.createElement(Item, {\n        onClick: onClick,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave\n      }, label));\n    }));\n  }\n  if (needConfirmButton) {\n    var Button = components.button || 'button';\n    if (onNow && !presetNode && showNow !== false) {\n      presetNode = /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-now\")\n      }, /*#__PURE__*/React.createElement(\"a\", {\n        className: \"\".concat(prefixCls, \"-now-btn\"),\n        onClick: onNow\n      }, locale.now));\n    }\n    okNode = needConfirmButton && /*#__PURE__*/React.createElement(\"li\", {\n      className: \"\".concat(prefixCls, \"-ok\")\n    }, /*#__PURE__*/React.createElement(Button, {\n      disabled: okDisabled,\n      onClick: onOk\n    }, locale.ok));\n  }\n  if (!presetNode && !okNode) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-ranges\")\n  }, presetNode, okNode);\n}", "map": {"version": 3, "names": ["React", "getRanges", "_ref", "prefixCls", "_ref$rangeList", "rangeList", "_ref$components", "components", "needConfirmButton", "onNow", "onOk", "okDisabled", "showNow", "locale", "presetNode", "okNode", "length", "<PERSON><PERSON>", "rangeItem", "createElement", "Fragment", "map", "_ref2", "label", "onClick", "onMouseEnter", "onMouseLeave", "key", "className", "concat", "<PERSON><PERSON>", "button", "now", "disabled", "ok"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-picker/es/utils/getRanges.js"], "sourcesContent": ["import * as React from 'react';\nexport default function getRanges(_ref) {\n  var prefixCls = _ref.prefixCls,\n      _ref$rangeList = _ref.rangeList,\n      rangeList = _ref$rangeList === void 0 ? [] : _ref$rangeList,\n      _ref$components = _ref.components,\n      components = _ref$components === void 0 ? {} : _ref$components,\n      needConfirmButton = _ref.needConfirmButton,\n      onNow = _ref.onNow,\n      onOk = _ref.onOk,\n      okDisabled = _ref.okDisabled,\n      showNow = _ref.showNow,\n      locale = _ref.locale;\n  var presetNode;\n  var okNode;\n\n  if (rangeList.length) {\n    var Item = components.rangeItem || 'span';\n    presetNode = /*#__PURE__*/React.createElement(React.Fragment, null, rangeList.map(function (_ref2) {\n      var label = _ref2.label,\n          onClick = _ref2.onClick,\n          onMouseEnter = _ref2.onMouseEnter,\n          onMouseLeave = _ref2.onMouseLeave;\n      return /*#__PURE__*/React.createElement(\"li\", {\n        key: label,\n        className: \"\".concat(prefixCls, \"-preset\")\n      }, /*#__PURE__*/React.createElement(Item, {\n        onClick: onClick,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave\n      }, label));\n    }));\n  }\n\n  if (needConfirmButton) {\n    var Button = components.button || 'button';\n\n    if (onNow && !presetNode && showNow !== false) {\n      presetNode = /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-now\")\n      }, /*#__PURE__*/React.createElement(\"a\", {\n        className: \"\".concat(prefixCls, \"-now-btn\"),\n        onClick: onNow\n      }, locale.now));\n    }\n\n    okNode = needConfirmButton && /*#__PURE__*/React.createElement(\"li\", {\n      className: \"\".concat(prefixCls, \"-ok\")\n    }, /*#__PURE__*/React.createElement(Button, {\n      disabled: okDisabled,\n      onClick: onOk\n    }, locale.ok));\n  }\n\n  if (!presetNode && !okNode) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-ranges\")\n  }, presetNode, okNode);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,cAAc,GAAGF,IAAI,CAACG,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc;IAC3DE,eAAe,GAAGJ,IAAI,CAACK,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;IAC9DE,iBAAiB,GAAGN,IAAI,CAACM,iBAAiB;IAC1CC,KAAK,GAAGP,IAAI,CAACO,KAAK;IAClBC,IAAI,GAAGR,IAAI,CAACQ,IAAI;IAChBC,UAAU,GAAGT,IAAI,CAACS,UAAU;IAC5BC,OAAO,GAAGV,IAAI,CAACU,OAAO;IACtBC,MAAM,GAAGX,IAAI,CAACW,MAAM;EACxB,IAAIC,UAAU;EACd,IAAIC,MAAM;EAEV,IAAIV,SAAS,CAACW,MAAM,EAAE;IACpB,IAAIC,IAAI,GAAGV,UAAU,CAACW,SAAS,IAAI,MAAM;IACzCJ,UAAU,GAAG,aAAad,KAAK,CAACmB,aAAa,CAACnB,KAAK,CAACoB,QAAQ,EAAE,IAAI,EAAEf,SAAS,CAACgB,GAAG,CAAC,UAAUC,KAAK,EAAE;MACjG,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;QACnBC,OAAO,GAAGF,KAAK,CAACE,OAAO;QACvBC,YAAY,GAAGH,KAAK,CAACG,YAAY;QACjCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;MACrC,OAAO,aAAa1B,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;QAC5CQ,GAAG,EAAEJ,KAAK;QACVK,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1B,SAAS,EAAE,SAAS;MAC3C,CAAC,EAAE,aAAaH,KAAK,CAACmB,aAAa,CAACF,IAAI,EAAE;QACxCO,OAAO,EAAEA,OAAO;QAChBC,YAAY,EAAEA,YAAY;QAC1BC,YAAY,EAAEA;MAChB,CAAC,EAAEH,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC;EACL;EAEA,IAAIf,iBAAiB,EAAE;IACrB,IAAIsB,MAAM,GAAGvB,UAAU,CAACwB,MAAM,IAAI,QAAQ;IAE1C,IAAItB,KAAK,IAAI,CAACK,UAAU,IAAIF,OAAO,KAAK,KAAK,EAAE;MAC7CE,UAAU,GAAG,aAAad,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;QAClDS,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1B,SAAS,EAAE,MAAM;MACxC,CAAC,EAAE,aAAaH,KAAK,CAACmB,aAAa,CAAC,GAAG,EAAE;QACvCS,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1B,SAAS,EAAE,UAAU,CAAC;QAC3CqB,OAAO,EAAEf;MACX,CAAC,EAAEI,MAAM,CAACmB,GAAG,CAAC,CAAC;IACjB;IAEAjB,MAAM,GAAGP,iBAAiB,IAAI,aAAaR,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;MACnES,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1B,SAAS,EAAE,KAAK;IACvC,CAAC,EAAE,aAAaH,KAAK,CAACmB,aAAa,CAACW,MAAM,EAAE;MAC1CG,QAAQ,EAAEtB,UAAU;MACpBa,OAAO,EAAEd;IACX,CAAC,EAAEG,MAAM,CAACqB,EAAE,CAAC,CAAC;EAChB;EAEA,IAAI,CAACpB,UAAU,IAAI,CAACC,MAAM,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAO,aAAaf,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;IAC5CS,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC1B,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEW,UAAU,EAAEC,MAAM,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}