{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\menuMobile.jsx\";\nimport React, { Component } from 'react';\nimport { <PERSON>ubar } from 'primereact/menubar';\nimport { <PERSON><PERSON> } from '../traduttore/const';\nimport { login, settings } from '../route';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass MenuMobile extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    var items = [{\n      label: JSON.parse(window.localStorage.userid).firstName,\n      icon: 'pi pi-fw pi-user',\n      className: 'userAccount'\n    }, {\n      separator: true,\n      className: 'mt-0'\n    }, {\n      label: Costanti.Profilo,\n      icon: 'pi pi-cog',\n      command: () => {\n        window.location.pathname = settings;\n      }\n    }, {\n      separator: true\n    }, {\n      label: Costanti.Esci,\n      icon: 'pi pi-unlock',\n      command: () => {\n        localStorage.removeItem('login_token');\n        localStorage.removeItem('role');\n        window.location.pathname = login;\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobileMenuBar text-center d-flex flex-column align-items-center\",\n      children: /*#__PURE__*/_jsxDEV(Menubar, {\n        className: \"menuBar\",\n        model: items\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default MenuMobile;", "map": {"version": 3, "names": ["React", "Component", "Men<PERSON><PERSON>", "<PERSON><PERSON>", "login", "settings", "jsxDEV", "_jsxDEV", "MenuMobile", "constructor", "props", "state", "render", "items", "label", "JSON", "parse", "window", "localStorage", "userid", "firstName", "icon", "className", "separator", "<PERSON>ilo", "command", "location", "pathname", "<PERSON><PERSON><PERSON>", "removeItem", "children", "model", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/menuMobile.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport { Menubar } from 'primereact/menubar';\nimport { <PERSON><PERSON> } from '../traduttore/const';\nimport { login, settings } from '../route';\n\nclass MenuMobile extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {}\n    }\n    render() {\n        var items = [\n            {\n                label: JSON.parse(window.localStorage.userid).firstName,\n                icon: 'pi pi-fw pi-user',\n                className: 'userAccount'\n            },\n            {\n                separator: true,\n                className: 'mt-0'\n            },\n            {\n                label: Costanti.Profilo,\n                icon: 'pi pi-cog',\n                command: () => { window.location.pathname = settings }\n            },\n            {\n                separator: true,\n            },\n            {\n                label: Costanti.Esci,\n                icon: 'pi pi-unlock',\n                command: () => {\n                    localStorage.removeItem('login_token');\n                    localStorage.removeItem('role');\n                    window.location.pathname = login\n                }\n            },\n        ]\n        return (\n            <div className='mobileMenuBar text-center d-flex flex-column align-items-center'>\n                <Menubar className='menuBar' model={items} />\n                {/* <span className=\"Role\">{localStorage.getItem(\"role\")}</span> */}\n            </div>\n        )\n    }\n}\n\nexport default MenuMobile;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,EAAEC,QAAQ,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,UAAU,SAASP,SAAS,CAAC;EAC/BQ,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,MAAMA,CAAA,EAAG;IACL,IAAIC,KAAK,GAAG,CACR;MACIC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,MAAM,CAAC,CAACC,SAAS;MACvDC,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAE;IACf,CAAC,EACD;MACIC,SAAS,EAAE,IAAI;MACfD,SAAS,EAAE;IACf,CAAC,EACD;MACIR,KAAK,EAAEX,QAAQ,CAACqB,OAAO;MACvBH,IAAI,EAAE,WAAW;MACjBI,OAAO,EAAEA,CAAA,KAAM;QAAER,MAAM,CAACS,QAAQ,CAACC,QAAQ,GAAGtB,QAAQ;MAAC;IACzD,CAAC,EACD;MACIkB,SAAS,EAAE;IACf,CAAC,EACD;MACIT,KAAK,EAAEX,QAAQ,CAACyB,IAAI;MACpBP,IAAI,EAAE,cAAc;MACpBI,OAAO,EAAEA,CAAA,KAAM;QACXP,YAAY,CAACW,UAAU,CAAC,aAAa,CAAC;QACtCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;QAC/BZ,MAAM,CAACS,QAAQ,CAACC,QAAQ,GAAGvB,KAAK;MACpC;IACJ,CAAC,CACJ;IACD,oBACIG,OAAA;MAAKe,SAAS,EAAC,iEAAiE;MAAAQ,QAAA,eAC5EvB,OAAA,CAACL,OAAO;QAACoB,SAAS,EAAC,SAAS;QAACS,KAAK,EAAElB;MAAM;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE5C,CAAC;EAEd;AACJ;AAEA,eAAe3B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}