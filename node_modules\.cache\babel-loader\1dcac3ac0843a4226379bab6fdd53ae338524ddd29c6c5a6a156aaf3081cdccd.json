{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiPV.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiPV - operazioni sull'aggiunta punti vendita\n*\n*/\nimport React, { useState, useEffect, useRef } from 'react';\nimport CustomDataTable from '../components/customDataTable';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { AutoComplete } from 'primereact/autocomplete';\nimport '../css/modale.css';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiPV = () => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti\n  const [results, setResults] = useState([]);\n  const [results2, setResults2] = useState([]);\n  const [selectedPDV, setSelectedPDV] = useState(null);\n  const [selectedAffiliate, setSelectedAffiliate] = useState(null);\n  const [filteredResults, setFilteredResults] = useState([]);\n  const [className, setClassName] = useState(\"row d-none\");\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function trovaRisultato() {\n      /* Reperisco gli affiliati */\n      await APIRequest('GET', 'affiliate/').then(res => {\n        setResults(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n      /* Reperisco le anagrafiche */\n      await APIRequest('GET', 'registry/').then(res => {\n        setResults2(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, []);\n  if (results.length === 0 || results2.length === 0) {\n    return null;\n  }\n  /* Seleziono l'affiliato */\n  const selAff = e => {\n    setSelectedAffiliate(e.value);\n    if (e.value.length > 2) {\n      setClassName(\"row mt-3\");\n    } else if (e.value.length === undefined) {\n      setClassName(\"row mt-3\");\n    }\n  };\n  /* Seleziono il punto vendita */\n  const selPDV = async e => {\n    setSelectedPDV(e.value);\n    var completa = {\n      idAffiliate: selectedAffiliate.id,\n      idRegistry: e.value.id\n    };\n    localStorage.setItem(\"datiComodo\", JSON.stringify(completa));\n  };\n  //Ricerca risultati per la select affiliato\n  const searchResults = event => {\n    setTimeout(() => {\n      let _filteredResults;\n      if (!event.query.trim().length) {\n        _filteredResults = [...results];\n      } else {\n        _filteredResults = results.filter(result => {\n          return result.idRegistry2.firstName.toLowerCase().startsWith(event.query.toLowerCase());\n        });\n      }\n      setFilteredResults(_filteredResults);\n    }, 250);\n  };\n  /* Visualizzo la lista di affiliati con nome e partita iva */\n  const itemTemplate = results => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"country-item\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: results.idRegistry2.firstName + ', ' + results.idRegistry2.pIva\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 13\n    }, this);\n  };\n  const fields = [{\n    field: 'firstName',\n    header: Costanti.Nome,\n    sortable: true,\n    showHeader: true\n  }, {\n    field: 'pIva',\n    header: Costanti.pIva,\n    sortable: true,\n    showHeader: true\n  }, {\n    field: 'address',\n    header: Costanti.Indirizzo,\n    sortable: true,\n    showHeader: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"col-12\",\n          htmlFor: \"s-affiliato\",\n          children: Costanti.SelAff\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-float-label col-12\",\n        children: /*#__PURE__*/_jsxDEV(AutoComplete, {\n          id: \"s-affiliato\",\n          value: selectedAffiliate,\n          suggestions: filteredResults,\n          completeMethod: searchResults,\n          itemTemplate: itemTemplate,\n          dropdown: true,\n          field: \"idRegistry2.firstName\",\n          onChange: e => selAff(e)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: className,\n      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"mt-2 ml-3\",\n          children: Costanti.SelPDV\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CustomDataTable, {\n        value: results2,\n        fields: fields,\n        dataKey: \"id\",\n        paginator: true,\n        rows: 5,\n        rowsPerPageOptions: [5, 10, 20, 50],\n        selectionMode: \"single\",\n        selection: selectedPDV,\n        onSelectionChange: e => selPDV(e),\n        responsiveLayout: \"scroll\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiPV, \"FLtQ7zYwLHl/UEStHP2sEwAJHk0=\");\n_c = AggiungiPV;\nexport default AggiungiPV;\nvar _c;\n$RefreshReg$(_c, \"AggiungiPV\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "CustomDataTable", "<PERSON><PERSON>", "APIRequest", "Toast", "AutoComplete", "stopLoading", "jsxDEV", "_jsxDEV", "AggiungiPV", "_s", "results", "setResults", "results2", "setResults2", "selectedPDV", "setSelectedPDV", "selectedAffiliate", "setSelectedAffiliate", "filteredResults", "setFilteredResults", "className", "setClassName", "toast", "trovaRisultato", "then", "res", "data", "catch", "e", "console", "log", "length", "<PERSON><PERSON><PERSON><PERSON>", "value", "undefined", "selPDV", "completa", "idAffiliate", "id", "idRegistry", "localStorage", "setItem", "JSON", "stringify", "searchResults", "event", "setTimeout", "_filteredResults", "query", "trim", "filter", "result", "idRegistry2", "firstName", "toLowerCase", "startsWith", "itemTemplate", "children", "pIva", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fields", "field", "header", "Nome", "sortable", "showHeader", "<PERSON><PERSON><PERSON><PERSON>", "ref", "htmlFor", "<PERSON><PERSON><PERSON><PERSON>", "suggestions", "completeMethod", "dropdown", "onChange", "SelPDV", "dataKey", "paginator", "rows", "rowsPerPageOptions", "selectionMode", "selection", "onSelectionChange", "responsiveLayout", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiPV.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiPV - operazioni sull'aggiunta punti vendita\n*\n*/\nimport React, { useState, useEffect, useRef } from 'react';\nimport CustomDataTable from '../components/customDataTable';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { AutoComplete } from 'primereact/autocomplete';\nimport '../css/modale.css';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\n\nconst AggiungiPV = () => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti\n    const [results, setResults] = useState([]);\n    const [results2, setResults2] = useState([]);\n    const [selectedPDV, setSelectedPDV] = useState(null);\n    const [selectedAffiliate, setSelectedAffiliate] = useState(null);\n    const [filteredResults, setFilteredResults] = useState([]);\n    const [className, setClassName] = useState(\"row d-none\")\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function trovaRisultato() {\n            /* Reperisco gli affiliati */\n            await APIRequest('GET', 'affiliate/')\n                .then(res => {\n                    setResults(res.data);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            /* Reperisco le anagrafiche */\n            await APIRequest('GET', 'registry/')\n                .then(res => {\n                    setResults2(res.data);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, []);\n    if (results.length === 0 || results2.length === 0) {\n        return null;\n    }\n    /* Seleziono l'affiliato */\n    const selAff = (e) => {\n        setSelectedAffiliate(e.value)\n        if (e.value.length > 2) {\n            setClassName(\"row mt-3\")\n        } else if (e.value.length === undefined) {\n            setClassName(\"row mt-3\")\n        }\n    }\n    /* Seleziono il punto vendita */\n    const selPDV = async (e) => {\n        setSelectedPDV(e.value)\n        var completa = {\n            idAffiliate: selectedAffiliate.id,\n            idRegistry: e.value.id\n        }\n        localStorage.setItem(\"datiComodo\", JSON.stringify(completa))\n    };\n    //Ricerca risultati per la select affiliato\n    const searchResults = (event) => {\n        setTimeout(() => {\n            let _filteredResults;\n            if (!event.query.trim().length) {\n                _filteredResults = [...results];\n            }\n            else {\n                _filteredResults = results.filter((result) => {\n                    return result.idRegistry2.firstName.toLowerCase().startsWith(event.query.toLowerCase());\n                });\n            }\n            setFilteredResults(_filteredResults);\n        }, 250);\n    }\n    /* Visualizzo la lista di affiliati con nome e partita iva */\n    const itemTemplate = (results) => {\n        return (\n            <div className=\"country-item\">\n                <div>{results.idRegistry2.firstName + ', ' + results.idRegistry2.pIva}</div>\n            </div>\n        );\n    }\n    const fields = [\n        { field: 'firstName', header: Costanti.Nome, sortable: true, showHeader: true },\n        { field: 'pIva', header: Costanti.pIva, sortable: true, showHeader: true },\n        { field: 'address', header: Costanti.Indirizzo, sortable: true, showHeader: true }\n    ];\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div className=\"row\">\n                <b><label className=\"col-12\" htmlFor=\"s-affiliato\">{Costanti.SelAff}</label></b>\n                <span className=\"p-float-label col-12\">\n                    {/* Componente di prime react per la visualizzazione della select */}\n                    <AutoComplete id=\"s-affiliato\" value={selectedAffiliate} suggestions={filteredResults} completeMethod={searchResults} itemTemplate={itemTemplate} dropdown field=\"idRegistry2.firstName\" onChange={(e) => selAff(e)} />\n                </span>\n            </div>\n            <div className={className}>\n                <b><label className=\"mt-2 ml-3\">{Costanti.SelPDV}</label></b>\n                <CustomDataTable\n                    value={results2}\n                    fields={fields}\n                    dataKey=\"id\"\n                    paginator\n                    rows={5}\n                    rowsPerPageOptions={[5, 10, 20, 50]}\n                    selectionMode=\"single\"\n                    selection={selectedPDV}\n                    onSelectionChange={e => selPDV(e)}\n                    responsiveLayout=\"scroll\"\n                />\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiPV;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,YAAY,CAAC;EACxD,MAAMyB,KAAK,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZ,eAAeyB,cAAcA,CAAA,EAAG;MAC5B;MACA,MAAMrB,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChCsB,IAAI,CAACC,GAAG,IAAI;QACTd,UAAU,CAACc,GAAG,CAACC,IAAI,CAAC;MACxB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACN;MACA,MAAM1B,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,CAC/BsB,IAAI,CAACC,GAAG,IAAI;QACTZ,WAAW,CAACY,GAAG,CAACC,IAAI,CAAC;MACzB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACNvB,WAAW,CAAC,CAAC;IACjB;IACAkB,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIb,OAAO,CAACqB,MAAM,KAAK,CAAC,IAAInB,QAAQ,CAACmB,MAAM,KAAK,CAAC,EAAE;IAC/C,OAAO,IAAI;EACf;EACA;EACA,MAAMC,MAAM,GAAIJ,CAAC,IAAK;IAClBX,oBAAoB,CAACW,CAAC,CAACK,KAAK,CAAC;IAC7B,IAAIL,CAAC,CAACK,KAAK,CAACF,MAAM,GAAG,CAAC,EAAE;MACpBV,YAAY,CAAC,UAAU,CAAC;IAC5B,CAAC,MAAM,IAAIO,CAAC,CAACK,KAAK,CAACF,MAAM,KAAKG,SAAS,EAAE;MACrCb,YAAY,CAAC,UAAU,CAAC;IAC5B;EACJ,CAAC;EACD;EACA,MAAMc,MAAM,GAAG,MAAOP,CAAC,IAAK;IACxBb,cAAc,CAACa,CAAC,CAACK,KAAK,CAAC;IACvB,IAAIG,QAAQ,GAAG;MACXC,WAAW,EAAErB,iBAAiB,CAACsB,EAAE;MACjCC,UAAU,EAAEX,CAAC,CAACK,KAAK,CAACK;IACxB,CAAC;IACDE,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAAC,CAAC;EAChE,CAAC;EACD;EACA,MAAMQ,aAAa,GAAIC,KAAK,IAAK;IAC7BC,UAAU,CAAC,MAAM;MACb,IAAIC,gBAAgB;MACpB,IAAI,CAACF,KAAK,CAACG,KAAK,CAACC,IAAI,CAAC,CAAC,CAAClB,MAAM,EAAE;QAC5BgB,gBAAgB,GAAG,CAAC,GAAGrC,OAAO,CAAC;MACnC,CAAC,MACI;QACDqC,gBAAgB,GAAGrC,OAAO,CAACwC,MAAM,CAAEC,MAAM,IAAK;UAC1C,OAAOA,MAAM,CAACC,WAAW,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,UAAU,CAACV,KAAK,CAACG,KAAK,CAACM,WAAW,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC;MACN;MACAnC,kBAAkB,CAAC4B,gBAAgB,CAAC;IACxC,CAAC,EAAE,GAAG,CAAC;EACX,CAAC;EACD;EACA,MAAMS,YAAY,GAAI9C,OAAO,IAAK;IAC9B,oBACIH,OAAA;MAAKa,SAAS,EAAC,cAAc;MAAAqC,QAAA,eACzBlD,OAAA;QAAAkD,QAAA,EAAM/C,OAAO,CAAC0C,WAAW,CAACC,SAAS,GAAG,IAAI,GAAG3C,OAAO,CAAC0C,WAAW,CAACM;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC;EAEd,CAAC;EACD,MAAMC,MAAM,GAAG,CACX;IAAEC,KAAK,EAAE,WAAW;IAAEC,MAAM,EAAEhE,QAAQ,CAACiE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC/E;IAAEJ,KAAK,EAAE,MAAM;IAAEC,MAAM,EAAEhE,QAAQ,CAACyD,IAAI;IAAES,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC1E;IAAEJ,KAAK,EAAE,SAAS;IAAEC,MAAM,EAAEhE,QAAQ,CAACoE,SAAS;IAAEF,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,CACrF;EACD,oBACI7D,OAAA;IAAKa,SAAS,EAAC,WAAW;IAAAqC,QAAA,gBACtBlD,OAAA,CAACJ,KAAK;MAACmE,GAAG,EAAEhD;IAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBvD,OAAA;MAAKa,SAAS,EAAC,KAAK;MAAAqC,QAAA,gBAChBlD,OAAA;QAAAkD,QAAA,eAAGlD,OAAA;UAAOa,SAAS,EAAC,QAAQ;UAACmD,OAAO,EAAC,aAAa;UAAAd,QAAA,EAAExD,QAAQ,CAACuE;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChFvD,OAAA;QAAMa,SAAS,EAAC,sBAAsB;QAAAqC,QAAA,eAElClD,OAAA,CAACH,YAAY;UAACkC,EAAE,EAAC,aAAa;UAACL,KAAK,EAAEjB,iBAAkB;UAACyD,WAAW,EAAEvD,eAAgB;UAACwD,cAAc,EAAE9B,aAAc;UAACY,YAAY,EAAEA,YAAa;UAACmB,QAAQ;UAACX,KAAK,EAAC,uBAAuB;UAACY,QAAQ,EAAGhD,CAAC,IAAKI,MAAM,CAACJ,CAAC;QAAE;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNvD,OAAA;MAAKa,SAAS,EAAEA,SAAU;MAAAqC,QAAA,gBACtBlD,OAAA;QAAAkD,QAAA,eAAGlD,OAAA;UAAOa,SAAS,EAAC,WAAW;UAAAqC,QAAA,EAAExD,QAAQ,CAAC4E;QAAM;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC7DvD,OAAA,CAACP,eAAe;QACZiC,KAAK,EAAErB,QAAS;QAChBmD,MAAM,EAAEA,MAAO;QACfe,OAAO,EAAC,IAAI;QACZC,SAAS;QACTC,IAAI,EAAE,CAAE;QACRC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACpCC,aAAa,EAAC,QAAQ;QACtBC,SAAS,EAAErE,WAAY;QACvBsE,iBAAiB,EAAExD,CAAC,IAAIO,MAAM,CAACP,CAAC,CAAE;QAClCyD,gBAAgB,EAAC;MAAQ;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAArD,EAAA,CA1GKD,UAAU;AAAA8E,EAAA,GAAV9E,UAAU;AA4GhB,eAAeA,UAAU;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}