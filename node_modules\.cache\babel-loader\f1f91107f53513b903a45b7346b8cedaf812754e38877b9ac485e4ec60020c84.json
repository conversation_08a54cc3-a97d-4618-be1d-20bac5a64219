{"ast": null, "code": "import _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { createMemoryHistory, createLocation, locationsAreEqual, createPath } from 'history';\nimport warning from 'tiny-warning';\nimport createContext from 'mini-create-react-context';\nimport invariant from 'tiny-invariant';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport pathToRegexp from 'path-to-regexp';\nimport { isValidElementType } from 'react-is';\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport hoistStatics from 'hoist-non-react-statics';\n\n// TODO: Replace with React.createContext once we can assume React 16+\n\nvar createNamedContext = function createNamedContext(name) {\n  var context = createContext();\n  context.displayName = name;\n  return context;\n};\nvar historyContext = /*#__PURE__*/createNamedContext(\"Router-History\");\nvar context = /*#__PURE__*/createNamedContext(\"Router\");\n\n/**\n * The public API for putting history on context.\n */\n\nvar Router = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Router, _React$Component);\n  Router.computeRootMatch = function computeRootMatch(pathname) {\n    return {\n      path: \"/\",\n      url: \"/\",\n      params: {},\n      isExact: pathname === \"/\"\n    };\n  };\n  function Router(props) {\n    var _this;\n    _this = _React$Component.call(this, props) || this;\n    _this.state = {\n      location: props.history.location\n    }; // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n\n    _this._isMounted = false;\n    _this._pendingLocation = null;\n    if (!props.staticContext) {\n      _this.unlisten = props.history.listen(function (location) {\n        if (_this._isMounted) {\n          _this.setState({\n            location: location\n          });\n        } else {\n          _this._pendingLocation = location;\n        }\n      });\n    }\n    return _this;\n  }\n  var _proto = Router.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    this._isMounted = true;\n    if (this._pendingLocation) {\n      this.setState({\n        location: this._pendingLocation\n      });\n    }\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  };\n  _proto.render = function render() {\n    return /*#__PURE__*/React.createElement(context.Provider, {\n      value: {\n        history: this.props.history,\n        location: this.state.location,\n        match: Router.computeRootMatch(this.state.location.pathname),\n        staticContext: this.props.staticContext\n      }\n    }, /*#__PURE__*/React.createElement(historyContext.Provider, {\n      children: this.props.children || null,\n      value: this.props.history\n    }));\n  };\n  return Router;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n  Router.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(prevProps.history === this.props.history, \"You cannot change <Router history>\") : void 0;\n  };\n}\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\n\nvar MemoryRouter = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(MemoryRouter, _React$Component);\n  function MemoryRouter() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.history = createMemoryHistory(_this.props);\n    return _this;\n  }\n  var _proto = MemoryRouter.prototype;\n  _proto.render = function render() {\n    return /*#__PURE__*/React.createElement(Router, {\n      history: this.history,\n      children: this.props.children\n    });\n  };\n  return MemoryRouter;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n  MemoryRouter.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!this.props.history, \"<MemoryRouter> ignores the history prop. To use a custom history, \" + \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\") : void 0;\n  };\n}\nvar Lifecycle = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Lifecycle, _React$Component);\n  function Lifecycle() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Lifecycle.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  };\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  };\n  _proto.render = function render() {\n    return null;\n  };\n  return Lifecycle;\n}(React.Component);\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\n\nfunction Prompt(_ref) {\n  var message = _ref.message,\n    _ref$when = _ref.when,\n    when = _ref$when === void 0 ? true : _ref$when;\n  return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n    !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Prompt> outside a <Router>\") : invariant(false) : void 0;\n    if (!when || context.staticContext) return null;\n    var method = context.history.block;\n    return /*#__PURE__*/React.createElement(Lifecycle, {\n      onMount: function onMount(self) {\n        self.release = method(message);\n      },\n      onUpdate: function onUpdate(self, prevProps) {\n        if (prevProps.message !== message) {\n          self.release();\n          self.release = method(message);\n        }\n      },\n      onUnmount: function onUnmount(self) {\n        self.release();\n      },\n      message: message\n    });\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  var messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\nvar cache = {};\nvar cacheLimit = 10000;\nvar cacheCount = 0;\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n  var generator = pathToRegexp.compile(path);\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n  return generator;\n}\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\n\nfunction generatePath(path, params) {\n  if (path === void 0) {\n    path = \"/\";\n  }\n  if (params === void 0) {\n    params = {};\n  }\n  return path === \"/\" ? path : compilePath(path)(params, {\n    pretty: true\n  });\n}\n\n/**\n * The public API for navigating programmatically with a component.\n */\n\nfunction Redirect(_ref) {\n  var computedMatch = _ref.computedMatch,\n    to = _ref.to,\n    _ref$push = _ref.push,\n    push = _ref$push === void 0 ? false : _ref$push;\n  return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n    !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Redirect> outside a <Router>\") : invariant(false) : void 0;\n    var history = context.history,\n      staticContext = context.staticContext;\n    var method = push ? history.push : history.replace;\n    var location = createLocation(computedMatch ? typeof to === \"string\" ? generatePath(to, computedMatch.params) : _extends({}, to, {\n      pathname: generatePath(to.pathname, computedMatch.params)\n    }) : to); // When rendering in a static context,\n    // set the new location immediately.\n\n    if (staticContext) {\n      method(location);\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Lifecycle, {\n      onMount: function onMount() {\n        method(location);\n      },\n      onUpdate: function onUpdate(self, prevProps) {\n        var prevLocation = createLocation(prevProps.to);\n        if (!locationsAreEqual(prevLocation, _extends({}, location, {\n          key: prevLocation.key\n        }))) {\n          method(location);\n        }\n      },\n      to: to\n    });\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\nvar cache$1 = {};\nvar cacheLimit$1 = 10000;\nvar cacheCount$1 = 0;\nfunction compilePath$1(path, options) {\n  var cacheKey = \"\" + options.end + options.strict + options.sensitive;\n  var pathCache = cache$1[cacheKey] || (cache$1[cacheKey] = {});\n  if (pathCache[path]) return pathCache[path];\n  var keys = [];\n  var regexp = pathToRegexp(path, keys, options);\n  var result = {\n    regexp: regexp,\n    keys: keys\n  };\n  if (cacheCount$1 < cacheLimit$1) {\n    pathCache[path] = result;\n    cacheCount$1++;\n  }\n  return result;\n}\n/**\n * Public API for matching a URL pathname to a path.\n */\n\nfunction matchPath(pathname, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = {\n      path: options\n    };\n  }\n  var _options = options,\n    path = _options.path,\n    _options$exact = _options.exact,\n    exact = _options$exact === void 0 ? false : _options$exact,\n    _options$strict = _options.strict,\n    strict = _options$strict === void 0 ? false : _options$strict,\n    _options$sensitive = _options.sensitive,\n    sensitive = _options$sensitive === void 0 ? false : _options$sensitive;\n  var paths = [].concat(path);\n  return paths.reduce(function (matched, path) {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n    var _compilePath = compilePath$1(path, {\n        end: exact,\n        strict: strict,\n        sensitive: sensitive\n      }),\n      regexp = _compilePath.regexp,\n      keys = _compilePath.keys;\n    var match = regexp.exec(pathname);\n    if (!match) return null;\n    var url = match[0],\n      values = match.slice(1);\n    var isExact = pathname === url;\n    if (exact && !isExact) return null;\n    return {\n      path: path,\n      // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url,\n      // the matched portion of the URL\n      isExact: isExact,\n      // whether or not we matched exactly\n      params: keys.reduce(function (memo, key, index) {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\nfunction evalChildrenDev(children, props, path) {\n  var value = children(props);\n  process.env.NODE_ENV !== \"production\" ? warning(value !== undefined, \"You returned `undefined` from the `children` function of \" + (\"<Route\" + (path ? \" path=\\\"\" + path + \"\\\"\" : \"\") + \">, but you \") + \"should have returned a React element or `null`\") : void 0;\n  return value || null;\n}\n/**\n * The public API for matching a single path and rendering.\n */\n\nvar Route = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Route, _React$Component);\n  function Route() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Route.prototype;\n  _proto.render = function render() {\n    var _this = this;\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context$1) {\n      !context$1 ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Route> outside a <Router>\") : invariant(false) : void 0;\n      var location = _this.props.location || context$1.location;\n      var match = _this.props.computedMatch ? _this.props.computedMatch // <Switch> already computed the match for us\n      : _this.props.path ? matchPath(location.pathname, _this.props) : context$1.match;\n      var props = _extends({}, context$1, {\n        location: location,\n        match: match\n      });\n      var _this$props = _this.props,\n        children = _this$props.children,\n        component = _this$props.component,\n        render = _this$props.render; // Preact uses an empty array as children by\n      // default, so use null if that's the case.\n\n      if (Array.isArray(children) && isEmptyChildren(children)) {\n        children = null;\n      }\n      return /*#__PURE__*/React.createElement(context.Provider, {\n        value: props\n      }, props.match ? children ? typeof children === \"function\" ? process.env.NODE_ENV !== \"production\" ? evalChildrenDev(children, props, _this.props.path) : children(props) : children : component ? /*#__PURE__*/React.createElement(component, props) : render ? render(props) : null : typeof children === \"function\" ? process.env.NODE_ENV !== \"production\" ? evalChildrenDev(children, props, _this.props.path) : children(props) : null);\n    });\n  };\n  return Route;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: function component(props, propName) {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\"Invalid prop 'component' supplied to 'Route': the prop is not a valid React component\");\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n  Route.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.children && !isEmptyChildren(this.props.children) && this.props.component), \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.children && !isEmptyChildren(this.props.children) && this.props.render), \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.component && this.props.render), \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\") : void 0;\n  };\n  Route.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.location && !prevProps.location), '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(!this.props.location && prevProps.location), '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.') : void 0;\n  };\n}\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n  return _extends({}, location, {\n    pathname: addLeadingSlash(basename) + location.pathname\n  });\n}\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n  var base = addLeadingSlash(basename);\n  if (location.pathname.indexOf(base) !== 0) return location;\n  return _extends({}, location, {\n    pathname: location.pathname.substr(base.length)\n  });\n}\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\nfunction staticHandler(methodName) {\n  return function () {\n    process.env.NODE_ENV !== \"production\" ? invariant(false, \"You cannot %s with <StaticRouter>\", methodName) : invariant(false);\n  };\n}\nfunction noop() {}\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\n\nvar StaticRouter = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(StaticRouter, _React$Component);\n  function StaticRouter() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.handlePush = function (location) {\n      return _this.navigateTo(location, \"PUSH\");\n    };\n    _this.handleReplace = function (location) {\n      return _this.navigateTo(location, \"REPLACE\");\n    };\n    _this.handleListen = function () {\n      return noop;\n    };\n    _this.handleBlock = function () {\n      return noop;\n    };\n    return _this;\n  }\n  var _proto = StaticRouter.prototype;\n  _proto.navigateTo = function navigateTo(location, action) {\n    var _this$props = this.props,\n      _this$props$basename = _this$props.basename,\n      basename = _this$props$basename === void 0 ? \"\" : _this$props$basename,\n      _this$props$context = _this$props.context,\n      context = _this$props$context === void 0 ? {} : _this$props$context;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  };\n  _proto.render = function render() {\n    var _this$props2 = this.props,\n      _this$props2$basename = _this$props2.basename,\n      basename = _this$props2$basename === void 0 ? \"\" : _this$props2$basename,\n      _this$props2$context = _this$props2.context,\n      context = _this$props2$context === void 0 ? {} : _this$props2$context,\n      _this$props2$location = _this$props2.location,\n      location = _this$props2$location === void 0 ? \"/\" : _this$props2$location,\n      rest = _objectWithoutPropertiesLoose(_this$props2, [\"basename\", \"context\", \"location\"]);\n    var history = {\n      createHref: function createHref(path) {\n        return addLeadingSlash(basename + createURL(path));\n      },\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n    return /*#__PURE__*/React.createElement(Router, _extends({}, rest, {\n      history: history,\n      staticContext: context\n    }));\n  };\n  return StaticRouter;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n  StaticRouter.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!this.props.history, \"<StaticRouter> ignores the history prop. To use a custom history, \" + \"use `import { Router }` instead of `import { StaticRouter as Router }`.\") : void 0;\n  };\n}\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\n\nvar Switch = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Switch, _React$Component);\n  function Switch() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Switch.prototype;\n  _proto.render = function render() {\n    var _this = this;\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n      !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Switch> outside a <Router>\") : invariant(false) : void 0;\n      var location = _this.props.location || context.location;\n      var element, match; // We use React.Children.forEach instead of React.Children.toArray().find()\n      // here because toArray adds keys to all child elements and we do not want\n      // to trigger an unmount/remount for two <Route>s that render the same\n      // component at different URLs.\n\n      React.Children.forEach(_this.props.children, function (child) {\n        if (match == null && /*#__PURE__*/React.isValidElement(child)) {\n          element = child;\n          var path = child.props.path || child.props.from;\n          match = path ? matchPath(location.pathname, _extends({}, child.props, {\n            path: path\n          })) : context.match;\n        }\n      });\n      return match ? /*#__PURE__*/React.cloneElement(element, {\n        location: location,\n        computedMatch: match\n      }) : null;\n    });\n  };\n  return Switch;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n  Switch.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.location && !prevProps.location), '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(!this.props.location && prevProps.location), '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.') : void 0;\n  };\n}\n\n/**\n * A public higher-order component to access the imperative API\n */\n\nfunction withRouter(Component) {\n  var displayName = \"withRouter(\" + (Component.displayName || Component.name) + \")\";\n  var C = function C(props) {\n    var wrappedComponentRef = props.wrappedComponentRef,\n      remainingProps = _objectWithoutPropertiesLoose(props, [\"wrappedComponentRef\"]);\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n      !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <\" + displayName + \" /> outside a <Router>\") : invariant(false) : void 0;\n      return /*#__PURE__*/React.createElement(Component, _extends({}, remainingProps, context, {\n        ref: wrappedComponentRef\n      }));\n    });\n  };\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n  if (process.env.NODE_ENV !== \"production\") {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([PropTypes.string, PropTypes.func, PropTypes.object])\n    };\n  }\n  return hoistStatics(C, Component);\n}\nvar useContext = React.useContext;\nfunction useHistory() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useHistory()\") : invariant(false) : void 0;\n  }\n  return useContext(historyContext);\n}\nfunction useLocation() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useLocation()\") : invariant(false) : void 0;\n  }\n  return useContext(context).location;\n}\nfunction useParams() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useParams()\") : invariant(false) : void 0;\n  }\n  var match = useContext(context).match;\n  return match ? match.params : {};\n}\nfunction useRouteMatch(path) {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useRouteMatch()\") : invariant(false) : void 0;\n  }\n  var location = useLocation();\n  var match = useContext(context).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  if (typeof window !== \"undefined\") {\n    var global = window;\n    var key = \"__react_router_build__\";\n    var buildNames = {\n      cjs: \"CommonJS\",\n      esm: \"ES modules\",\n      umd: \"UMD\"\n    };\n    if (global[key] && global[key] !== \"esm\") {\n      var initialBuildName = buildNames[global[key]];\n      var secondaryBuildName = buildNames[\"esm\"]; // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n\n      throw new Error(\"You are loading the \" + secondaryBuildName + \" build of React Router \" + (\"on a page that is already running the \" + initialBuildName + \" \") + \"build, so things won't work right.\");\n    }\n    global[key] = \"esm\";\n  }\n}\nexport { MemoryRouter, Prompt, Redirect, Route, Router, StaticRouter, Switch, historyContext as __HistoryContext, context as __RouterContext, generatePath, matchPath, useHistory, useLocation, useParams, useRouteMatch, withRouter };", "map": {"version": 3, "names": ["createNamedContext", "name", "context", "createContext", "displayName", "historyContext", "Router", "_React$Component", "computeRootMatch", "pathname", "path", "url", "params", "isExact", "props", "state", "location", "history", "_isMounted", "_pendingLocation", "staticContext", "unlisten", "listen", "_this", "setState", "componentDidMount", "componentWillUnmount", "render", "React", "createElement", "Provider", "value", "match", "children", "Component", "process", "env", "NODE_ENV", "propTypes", "PropTypes", "node", "object", "isRequired", "prototype", "componentDidUpdate", "prevProps", "warning", "MemoryRouter", "createMemoryHistory", "initialEntries", "array", "initialIndex", "number", "getUserConfirmation", "func", "<PERSON><PERSON><PERSON><PERSON>", "Lifecycle", "onMount", "call", "onUpdate", "onUnmount", "Prompt", "_ref", "message", "when", "_ref$when", "Consumer", "invariant", "method", "block", "self", "release", "messageType", "oneOfType", "string", "bool", "cache", "cacheLimit", "cacheCount", "compilePath", "generator", "pathToRegexp", "compile", "generatePath", "pretty", "Redirect", "computedMatch", "to", "push", "_ref$push", "replace", "createLocation", "_extends", "prevLocation", "locationsAreEqual", "key", "from", "cache$1", "cacheLimit$1", "cacheCount$1", "compilePath$1", "options", "cache<PERSON>ey", "end", "strict", "sensitive", "pathCache", "keys", "regexp", "result", "matchPath", "Array", "isArray", "_options", "exact", "_options$exact", "_options$strict", "_options$sensitive", "paths", "concat", "reduce", "matched", "_compilePath", "exec", "values", "slice", "memo", "index", "isEmptyChildren", "Children", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "Route", "context$1", "_this$props", "component", "propName", "isValidElementType", "Error", "arrayOf", "addLeadingSlash", "char<PERSON>t", "addBasename", "basename", "stripBasename", "base", "indexOf", "substr", "length", "createURL", "createPath", "static<PERSON><PERSON><PERSON>", "methodName", "noop", "StaticRouter", "handlePush", "navigateTo", "handleReplace", "handleListen", "handleBlock", "action", "_this$props$basename", "_this$props$context", "_this$props2$basename", "_this$props2$context", "_this$props2$location", "rest", "_objectWithoutPropertiesLoose", "_this$props2", "createHref", "go", "goBack", "goForward", "Switch", "element", "for<PERSON>ach", "child", "isValidElement", "cloneElement", "with<PERSON><PERSON><PERSON>", "C", "wrappedComponentRef", "remainingProps", "ref", "WrappedComponent", "hoistStatics", "useContext", "useHistory", "useLocation", "useParams", "useRouteMatch", "window", "global", "buildNames", "cjs", "esm", "umd", "initialBuildName", "secondaryBuildName"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\createNamedContext.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\HistoryContext.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\RouterContext.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\Router.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\MemoryRouter.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\Lifecycle.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\Prompt.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\generatePath.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\Redirect.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\matchPath.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\Route.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\StaticRouter.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\Switch.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\withRouter.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\hooks.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-router\\modules\\index.js"], "sourcesContent": ["// TODO: Replace with React.createContext once we can assume React 16+\nimport createContext from \"mini-create-react-context\";\n\nconst createNamedContext = name => {\n  const context = createContext();\n  context.displayName = name;\n\n  return context;\n};\n\nexport default createNamedContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst historyContext = /*#__PURE__*/ createNamedContext(\"Router-History\");\nexport default historyContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst context = /*#__PURE__*/ createNamedContext(\"Router\");\nexport default context;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nimport HistoryContext from \"./HistoryContext.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for putting history on context.\n */\nclass Router extends React.Component {\n  static computeRootMatch(pathname) {\n    return { path: \"/\", url: \"/\", params: {}, isExact: pathname === \"/\" };\n  }\n\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      location: props.history.location\n    };\n\n    // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n    this._isMounted = false;\n    this._pendingLocation = null;\n\n    if (!props.staticContext) {\n      this.unlisten = props.history.listen(location => {\n        if (this._isMounted) {\n          this.setState({ location });\n        } else {\n          this._pendingLocation = location;\n        }\n      });\n    }\n  }\n\n  componentDidMount() {\n    this._isMounted = true;\n\n    if (this._pendingLocation) {\n      this.setState({ location: this._pendingLocation });\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  }\n\n  render() {\n    return (\n      <RouterContext.Provider\n        value={{\n          history: this.props.history,\n          location: this.state.location,\n          match: Router.computeRootMatch(this.state.location.pathname),\n          staticContext: this.props.staticContext\n        }}\n      >\n        <HistoryContext.Provider\n          children={this.props.children || null}\n          value={this.props.history}\n        />\n      </RouterContext.Provider>\n    );\n  }\n}\n\nif (__DEV__) {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n\n  Router.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      prevProps.history === this.props.history,\n      \"You cannot change <Router history>\"\n    );\n  };\n}\n\nexport default Router;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createMemoryHistory as createHistory } from \"history\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\nclass MemoryRouter extends React.Component {\n  history = createHistory(this.props);\n\n  render() {\n    return <Router history={this.history} children={this.props.children} />;\n  }\n}\n\nif (__DEV__) {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n\n  MemoryRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<MemoryRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\"\n    );\n  };\n}\n\nexport default MemoryRouter;\n", "import React from \"react\";\n\nclass Lifecycle extends React.Component {\n  componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  }\n\n  componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  }\n\n  render() {\n    return null;\n  }\n}\n\nexport default Lifecycle;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\nfunction Prompt({ message, when = true }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Prompt> outside a <Router>\");\n\n        if (!when || context.staticContext) return null;\n\n        const method = context.history.block;\n\n        return (\n          <Lifecycle\n            onMount={self => {\n              self.release = method(message);\n            }}\n            onUpdate={(self, prevProps) => {\n              if (prevProps.message !== message) {\n                self.release();\n                self.release = method(message);\n              }\n            }}\n            onUnmount={self => {\n              self.release();\n            }}\n            message={message}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  const messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\n\nexport default Prompt;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n\n  const generator = pathToRegexp.compile(path);\n\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n\n  return generator;\n}\n\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\nfunction generatePath(path = \"/\", params = {}) {\n  return path === \"/\" ? path : compilePath(path)(params, { pretty: true });\n}\n\nexport default generatePath;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, locationsAreEqual } from \"history\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\nimport generatePath from \"./generatePath.js\";\n\n/**\n * The public API for navigating programmatically with a component.\n */\nfunction Redirect({ computedMatch, to, push = false }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Redirect> outside a <Router>\");\n\n        const { history, staticContext } = context;\n\n        const method = push ? history.push : history.replace;\n        const location = createLocation(\n          computedMatch\n            ? typeof to === \"string\"\n              ? generatePath(to, computedMatch.params)\n              : {\n                  ...to,\n                  pathname: generatePath(to.pathname, computedMatch.params)\n                }\n            : to\n        );\n\n        // When rendering in a static context,\n        // set the new location immediately.\n        if (staticContext) {\n          method(location);\n          return null;\n        }\n\n        return (\n          <Lifecycle\n            onMount={() => {\n              method(location);\n            }}\n            onUpdate={(self, prevProps) => {\n              const prevLocation = createLocation(prevProps.to);\n              if (\n                !locationsAreEqual(prevLocation, {\n                  ...location,\n                  key: prevLocation.key\n                })\n              ) {\n                method(location);\n              }\n            }}\n            to={to}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\n\nexport default Redirect;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path, options) {\n  const cacheKey = `${options.end}${options.strict}${options.sensitive}`;\n  const pathCache = cache[cacheKey] || (cache[cacheKey] = {});\n\n  if (pathCache[path]) return pathCache[path];\n\n  const keys = [];\n  const regexp = pathToRegexp(path, keys, options);\n  const result = { regexp, keys };\n\n  if (cacheCount < cacheLimit) {\n    pathCache[path] = result;\n    cacheCount++;\n  }\n\n  return result;\n}\n\n/**\n * Public API for matching a URL pathname to a path.\n */\nfunction matchPath(pathname, options = {}) {\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = { path: options };\n  }\n\n  const { path, exact = false, strict = false, sensitive = false } = options;\n\n  const paths = [].concat(path);\n\n  return paths.reduce((matched, path) => {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n\n    const { regexp, keys } = compilePath(path, {\n      end: exact,\n      strict,\n      sensitive\n    });\n    const match = regexp.exec(pathname);\n\n    if (!match) return null;\n\n    const [url, ...values] = match;\n    const isExact = pathname === url;\n\n    if (exact && !isExact) return null;\n\n    return {\n      path, // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url, // the matched portion of the URL\n      isExact, // whether or not we matched exactly\n      params: keys.reduce((memo, key, index) => {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\n\nexport default matchPath;\n", "import React from \"react\";\nimport { isValidElementType } from \"react-is\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\n\nfunction evalChildrenDev(children, props, path) {\n  const value = children(props);\n\n  warning(\n    value !== undefined,\n    \"You returned `undefined` from the `children` function of \" +\n      `<Route${path ? ` path=\"${path}\"` : \"\"}>, but you ` +\n      \"should have returned a React element or `null`\"\n  );\n\n  return value || null;\n}\n\n/**\n * The public API for matching a single path and rendering.\n */\nclass Route extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Route> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n          const match = this.props.computedMatch\n            ? this.props.computedMatch // <Switch> already computed the match for us\n            : this.props.path\n            ? matchPath(location.pathname, this.props)\n            : context.match;\n\n          const props = { ...context, location, match };\n\n          let { children, component, render } = this.props;\n\n          // Preact uses an empty array as children by\n          // default, so use null if that's the case.\n          if (Array.isArray(children) && isEmptyChildren(children)) {\n            children = null;\n          }\n\n          return (\n            <RouterContext.Provider value={props}>\n              {props.match\n                ? children\n                  ? typeof children === \"function\"\n                    ? __DEV__\n                      ? evalChildrenDev(children, props, this.props.path)\n                      : children(props)\n                    : children\n                  : component\n                  ? React.createElement(component, props)\n                  : render\n                  ? render(props)\n                  : null\n                : typeof children === \"function\"\n                ? __DEV__\n                  ? evalChildrenDev(children, props, this.props.path)\n                  : children(props)\n                : null}\n            </RouterContext.Provider>\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: (props, propName) => {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\n          `Invalid prop 'component' supplied to 'Route': the prop is not a valid React component`\n        );\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.arrayOf(PropTypes.string)\n    ]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n\n  Route.prototype.componentDidMount = function() {\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.component\n      ),\n      \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\"\n    );\n\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.render\n      ),\n      \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\"\n    );\n\n    warning(\n      !(this.props.component && this.props.render),\n      \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\"\n    );\n  };\n\n  Route.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Route;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, createPath } from \"history\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\n\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n\n  return {\n    ...location,\n    pathname: addLeadingSlash(basename) + location.pathname\n  };\n}\n\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n\n  const base = addLeadingSlash(basename);\n\n  if (location.pathname.indexOf(base) !== 0) return location;\n\n  return {\n    ...location,\n    pathname: location.pathname.substr(base.length)\n  };\n}\n\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\n\nfunction staticHandler(methodName) {\n  return () => {\n    invariant(false, \"You cannot %s with <StaticRouter>\", methodName);\n  };\n}\n\nfunction noop() {}\n\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\nclass StaticRouter extends React.Component {\n  navigateTo(location, action) {\n    const { basename = \"\", context = {} } = this.props;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  }\n\n  handlePush = location => this.navigateTo(location, \"PUSH\");\n  handleReplace = location => this.navigateTo(location, \"REPLACE\");\n  handleListen = () => noop;\n  handleBlock = () => noop;\n\n  render() {\n    const { basename = \"\", context = {}, location = \"/\", ...rest } = this.props;\n\n    const history = {\n      createHref: path => addLeadingSlash(basename + createURL(path)),\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n\n    return <Router {...rest} history={history} staticContext={context} />;\n  }\n}\n\nif (__DEV__) {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n\n  StaticRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<StaticRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { StaticRouter as Router }`.\"\n    );\n  };\n}\n\nexport default StaticRouter;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\nclass Switch extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Switch> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n\n          let element, match;\n\n          // We use React.Children.forEach instead of React.Children.toArray().find()\n          // here because toArray adds keys to all child elements and we do not want\n          // to trigger an unmount/remount for two <Route>s that render the same\n          // component at different URLs.\n          React.Children.forEach(this.props.children, child => {\n            if (match == null && React.isValidElement(child)) {\n              element = child;\n\n              const path = child.props.path || child.props.from;\n\n              match = path\n                ? matchPath(location.pathname, { ...child.props, path })\n                : context.match;\n            }\n          });\n\n          return match\n            ? React.cloneElement(element, { location, computedMatch: match })\n            : null;\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n\n  Switch.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Switch;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport hoistStatics from \"hoist-non-react-statics\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * A public higher-order component to access the imperative API\n */\nfunction withRouter(Component) {\n  const displayName = `withRouter(${Component.displayName || Component.name})`;\n  const C = props => {\n    const { wrappedComponentRef, ...remainingProps } = props;\n\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(\n            context,\n            `You should not use <${displayName} /> outside a <Router>`\n          );\n          return (\n            <Component\n              {...remainingProps}\n              {...context}\n              ref={wrappedComponentRef}\n            />\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  };\n\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n\n  if (__DEV__) {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([\n        PropTypes.string,\n        PropTypes.func,\n        PropTypes.object\n      ])\n    };\n  }\n\n  return hoistStatics(C, Component);\n}\n\nexport default withRouter;\n", "import React from \"react\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport HistoryContext from \"./HistoryContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nconst useContext = React.useContext;\n\nexport function useHistory() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useHistory()\"\n    );\n  }\n\n  return useContext(HistoryContext);\n}\n\nexport function useLocation() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useLocation()\"\n    );\n  }\n\n  return useContext(RouterContext).location;\n}\n\nexport function useParams() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useParams()\"\n    );\n  }\n\n  const match = useContext(RouterContext).match;\n  return match ? match.params : {};\n}\n\nexport function useRouteMatch(path) {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useRouteMatch()\"\n    );\n  }\n\n  const location = useLocation();\n  const match = useContext(RouterContext).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\n", "if (__DEV__) {\n  if (typeof window !== \"undefined\") {\n    const global = window;\n    const key = \"__react_router_build__\";\n    const buildNames = { cjs: \"CommonJS\", esm: \"ES modules\", umd: \"UMD\" };\n\n    if (global[key] && global[key] !== process.env.BUILD_FORMAT) {\n      const initialBuildName = buildNames[global[key]];\n      const secondaryBuildName = buildNames[process.env.BUILD_FORMAT];\n\n      // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n      throw new Error(\n        `You are loading the ${secondaryBuildName} build of React Router ` +\n          `on a page that is already running the ${initialBuildName} ` +\n          `build, so things won't work right.`\n      );\n    }\n\n    global[key] = process.env.BUILD_FORMAT;\n  }\n}\n\nexport { default as MemoryRouter } from \"./MemoryRouter.js\";\nexport { default as Prompt } from \"./Prompt.js\";\nexport { default as Redirect } from \"./Redirect.js\";\nexport { default as Route } from \"./Route.js\";\nexport { default as Router } from \"./Router.js\";\nexport { default as StaticRouter } from \"./StaticRouter.js\";\nexport { default as Switch } from \"./Switch.js\";\nexport { default as generatePath } from \"./generatePath.js\";\nexport { default as matchPath } from \"./matchPath.js\";\nexport { default as withRouter } from \"./withRouter.js\";\n\nexport { default as __HistoryContext } from \"./HistoryContext.js\";\nexport { default as __RouterContext } from \"./RouterContext.js\";\n\nexport { useHistory, useLocation, useParams, useRouteMatch } from \"./hooks.js\";\n"], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,IAAMA,kBAAkB,GAAG,SAArBA,kBAAqBA,CAAAC,IAAI,EAAI;MAC3BC,OAAO,GAAGC,aAAa,EAA7B;EACAD,OAAO,CAACE,WAAR,GAAsBH,IAAtB;SAEOC,OAAP;CAJF;ACDA,IAAMG,cAAc,gBAAiBL,kBAAkB,CAAC,gBAAD,CAAvD;ACAA,IAAME,OAAO,gBAAiBF,kBAAkB,CAAC,QAAD,CAAhD;;ACKA;;;;IAGMM,MAAA,0BAAAC,gBAAA;;SACGC,gBAAA,GAAP,SAAAA,iBAAwBC,QAAxB,EAAkC;WACzB;MAAEC,IAAI,EAAE,GAAR;MAAaC,GAAG,EAAE,GAAlB;MAAuBC,MAAM,EAAE,EAA/B;MAAmCC,OAAO,EAAEJ,QAAQ,KAAK;KAAhE;;kBAGUK,KAAZ,EAAmB;;wCACXA,KAAN;UAEKC,KAAL,GAAa;MACXC,QAAQ,EAAEF,KAAK,CAACG,OAAN,CAAcD;KAD1B,CAHiB;;;;;;UAYZE,UAAL,GAAkB,KAAlB;UACKC,gBAAL,GAAwB,IAAxB;QAEI,CAACL,KAAK,CAACM,aAAX,EAA0B;YACnBC,QAAL,GAAgBP,KAAK,CAACG,OAAN,CAAcK,MAAd,CAAqB,UAAAN,QAAQ,EAAI;YAC3CO,KAAA,CAAKL,UAAT,EAAqB;gBACdM,QAAL,CAAc;YAAER,QAAQ,EAARA;WAAhB;SADF,MAEO;gBACAG,gBAAL,GAAwBH,QAAxB;;OAJY,CAAhB;;;;;SAUJS,iBAAA,YAAAA,kBAAA,EAAoB;SACbP,UAAL,GAAkB,IAAlB;QAEI,KAAKC,gBAAT,EAA2B;WACpBK,QAAL,CAAc;QAAER,QAAQ,EAAE,KAAKG;OAA/B;;;SAIJO,oBAAA,YAAAA,qBAAA,EAAuB;QACjB,KAAKL,QAAT,EAAmB;WACZA,QAAL;WACKH,UAAL,GAAkB,KAAlB;WACKC,gBAAL,GAAwB,IAAxB;;;SAIJQ,MAAA,YAAAA,OAAA,EAAS;wBAELC,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAe4B,QAAf;MACEC,KAAK,EAAE;QACLd,OAAO,EAAE,KAAKH,KAAL,CAAWG,OADf;QAELD,QAAQ,EAAE,KAAKD,KAAL,CAAWC,QAFhB;QAGLgB,KAAK,EAAE1B,MAAM,CAACE,gBAAP,CAAwB,KAAKO,KAAL,CAAWC,QAAX,CAAoBP,QAA5C,CAHF;QAILW,aAAa,EAAE,KAAKN,KAAL,CAAWM;;oBAG5BQ,KAAA,CAAAC,aAAA,CAACxB,cAAD,CAAgByB,QAAhB;MACEG,QAAQ,EAAE,KAAKnB,KAAL,CAAWmB,QAAX,IAAuB,IADnC;MAEEF,KAAK,EAAE,KAAKjB,KAAL,CAAWG;MAVtB,CADF;;;EAhDiBW,KAAK,CAACM,SAAA;AAkE3B,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACX/B,MAAM,CAACgC,SAAP,GAAmB;IACjBL,QAAQ,EAAEM,SAAS,CAACC,IADH;IAEjBvB,OAAO,EAAEsB,SAAS,CAACE,MAAV,CAAiBC,UAFT;IAGjBtB,aAAa,EAAEmB,SAAS,CAACE;GAH3B;EAMAnC,MAAM,CAACqC,SAAP,CAAiBC,kBAAjB,GAAsC,UAASC,SAAT,EAAoB;4CACxDC,OAAO,CACLD,SAAS,CAAC5B,OAAV,KAAsB,KAAKH,KAAL,CAAWG,OAD5B,EAEL,oCAFK,CAAP;GADF;;;AC5EF;;;;IAGM8B,YAAA,0BAAAxC,gBAAA;;;;;;;;UACJU,OAAA,GAAU+B,mBAAa,CAACzB,KAAA,CAAKT,KAAN;;;;SAEvBa,MAAA,YAAAA,OAAA,EAAS;wBACAC,KAAA,CAAAC,aAAA,CAACvB,MAAD;MAAQW,OAAO,EAAE,KAAKA,OAAtB;MAA+BgB,QAAQ,EAAE,KAAKnB,KAAL,CAAWmB;MAA3D;;;EAJuBL,KAAK,CAACM,SAAA;AAQjC,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXU,YAAY,CAACT,SAAb,GAAyB;IACvBW,cAAc,EAAEV,SAAS,CAACW,KADH;IAEvBC,YAAY,EAAEZ,SAAS,CAACa,MAFD;IAGvBC,mBAAmB,EAAEd,SAAS,CAACe,IAHR;IAIvBC,SAAS,EAAEhB,SAAS,CAACa,MAJE;IAKvBnB,QAAQ,EAAEM,SAAS,CAACC;GALtB;EAQAO,YAAY,CAACJ,SAAb,CAAuBlB,iBAAvB,GAA2C,YAAW;4CACpDqB,OAAO,CACL,CAAC,KAAKhC,KAAL,CAAWG,OADP,EAEL,uEACE,yEAHG,CAAP;GADF;;ICzBIuC,SAAA,0BAAAjD,gBAAA;;;;;;SACJkB,iBAAA,YAAAA,kBAAA,EAAoB;QACd,KAAKX,KAAL,CAAW2C,OAAf,EAAwB,KAAK3C,KAAL,CAAW2C,OAAX,CAAmBC,IAAnB,CAAwB,IAAxB,EAA8B,IAA9B;;SAG1Bd,kBAAA,YAAAA,mBAAmBC,SAAnB,EAA8B;QACxB,KAAK/B,KAAL,CAAW6C,QAAf,EAAyB,KAAK7C,KAAL,CAAW6C,QAAX,CAAoBD,IAApB,CAAyB,IAAzB,EAA+B,IAA/B,EAAqCb,SAArC;;SAG3BnB,oBAAA,YAAAA,qBAAA,EAAuB;QACjB,KAAKZ,KAAL,CAAW8C,SAAf,EAA0B,KAAK9C,KAAL,CAAW8C,SAAX,CAAqBF,IAArB,CAA0B,IAA1B,EAAgC,IAAhC;;SAG5B/B,MAAA,YAAAA,OAAA,EAAS;WACA,IAAP;;;EAdoBC,KAAK,CAACM,SAAA;;ACK9B;;;;AAGA,SAAS2B,MAATA,CAAAC,IAAA,EAA0C;MAAxBC,OAAwB,GAAAD,IAAA,CAAxBC,OAAwB;qBAAfC,IAAe;IAAfA,IAAe,GAAAC,SAAA,cAAR,IAAQ,GAAAA,SAAA;sBAEtCrC,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAegE,QAAf,QACG,UAAAhE,OAAO,EAAI;KACAA,OAAV,GAAAiC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAAU,gDAAV,CAAT,GAAAA,SAAS,OAAT;QAEI,CAACH,IAAD,IAAS9D,OAAO,CAACkB,aAArB,EAAoC,OAAO,IAAP;QAE9BgD,MAAM,GAAGlE,OAAO,CAACe,OAAR,CAAgBoD,KAA/B;wBAGEzC,KAAA,CAAAC,aAAA,CAAC2B,SAAD;MACEC,OAAO,EAAE,SAAAA,QAAAa,IAAI,EAAI;QACfA,IAAI,CAACC,OAAL,GAAeH,MAAM,CAACL,OAAD,CAArB;OAFJ;MAIEJ,QAAQ,EAAE,SAAAA,SAACW,IAAD,EAAOzB,SAAP,EAAqB;YACzBA,SAAS,CAACkB,OAAV,KAAsBA,OAA1B,EAAmC;UACjCO,IAAI,CAACC,OAAL;UACAD,IAAI,CAACC,OAAL,GAAeH,MAAM,CAACL,OAAD,CAArB;;OAPN;MAUEH,SAAS,EAAE,SAAAA,UAAAU,IAAI,EAAI;QACjBA,IAAI,CAACC,OAAL;OAXJ;MAaER,OAAO,EAAEA;MAdb;GARJ,CADF;;AA+BF,IAAA5B,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;MACLmC,WAAW,GAAGjC,SAAS,CAACkC,SAAV,CAAoB,CAAClC,SAAS,CAACe,IAAX,EAAiBf,SAAS,CAACmC,MAA3B,CAApB,CAApB;EAEAb,MAAM,CAACvB,SAAP,GAAmB;IACjB0B,IAAI,EAAEzB,SAAS,CAACoC,IADC;IAEjBZ,OAAO,EAAES,WAAW,CAAC9B;GAFvB;;AC3CF,IAAMkC,KAAK,GAAG,EAAd;AACA,IAAMC,UAAU,GAAG,KAAnB;AACA,IAAIC,UAAU,GAAG,CAAjB;AAEA,SAASC,WAATA,CAAqBrE,IAArB,EAA2B;MACrBkE,KAAK,CAAClE,IAAD,CAAT,EAAiB,OAAOkE,KAAK,CAAClE,IAAD,CAAZ;MAEXsE,SAAS,GAAGC,YAAY,CAACC,OAAb,CAAqBxE,IAArB,CAAlB;MAEIoE,UAAU,GAAGD,UAAjB,EAA6B;IAC3BD,KAAK,CAAClE,IAAD,CAAL,GAAcsE,SAAd;IACAF,UAAU;;SAGLE,SAAP;;;;;;AAMF,SAASG,YAATA,CAAsBzE,IAAtB,EAAkCE,MAAlC,EAA+C;MAAzBF,IAAyB;IAAzBA,IAAyB,GAAlB,GAAkB;;MAAbE,MAAa;IAAbA,MAAa,GAAJ,EAAI;;SACtCF,IAAI,KAAK,GAAT,GAAeA,IAAf,GAAsBqE,WAAW,CAACrE,IAAD,CAAX,CAAkBE,MAAlB,EAA0B;IAAEwE,MAAM,EAAE;GAApC,CAA7B;;;ACdF;;;;AAGA,SAASC,QAATA,CAAAvB,IAAA,EAAuD;MAAnCwB,aAAmC,GAAAxB,IAAA,CAAnCwB,aAAmC;IAApBC,EAAoB,GAAAzB,IAAA,CAApByB,EAAoB;qBAAhBC,IAAgB;IAAhBA,IAAgB,GAAAC,SAAA,cAAT,KAAS,GAAAA,SAAA;sBAEnD7D,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAegE,QAAf,QACG,UAAAhE,OAAO,EAAI;KACAA,OAAV,GAAAiC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAAU,kDAAV,CAAT,GAAAA,SAAS,OAAT;QAEQlD,OAHE,GAGyBf,OAHzB,CAGFe,OAHE;MAGOG,aAHP,GAGyBlB,OAHzB,CAGOkB,aAHP;QAKJgD,MAAM,GAAGoB,IAAI,GAAGvE,OAAO,CAACuE,IAAX,GAAkBvE,OAAO,CAACyE,OAA7C;QACM1E,QAAQ,GAAG2E,cAAc,CAC7BL,aAAa,GACT,OAAOC,EAAP,KAAc,QAAd,GACEJ,YAAY,CAACI,EAAD,EAAKD,aAAa,CAAC1E,MAAnB,CADd,GAAAgF,QAAA,KAGOL,EAHP;MAII9E,QAAQ,EAAE0E,YAAY,CAACI,EAAE,CAAC9E,QAAJ,EAAc6E,aAAa,CAAC1E,MAA5B;MALjB,GAOT2E,EARyB,CAA/B,CANU;;;QAmBNnE,aAAJ,EAAmB;MACjBgD,MAAM,CAACpD,QAAD,CAAN;aACO,IAAP;;wBAIAY,KAAA,CAAAC,aAAA,CAAC2B,SAAD;MACEC,OAAO,EAAE,SAAAA,QAAA,EAAM;QACbW,MAAM,CAACpD,QAAD,CAAN;OAFJ;MAIE2C,QAAQ,EAAE,SAAAA,SAACW,IAAD,EAAOzB,SAAP,EAAqB;YACvBgD,YAAY,GAAGF,cAAc,CAAC9C,SAAS,CAAC0C,EAAX,CAAnC;YAEE,CAACO,iBAAiB,CAACD,YAAD,EAAAD,QAAA,KACb5E,QADa;UAEhB+E,GAAG,EAAEF,YAAY,CAACE;WAHtB,EAKE;UACA3B,MAAM,CAACpD,QAAD,CAAN;;OAZN;MAeEuE,EAAE,EAAEA;MAhBR;GAzBJ,CADF;;AAkDF,IAAApD,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXgD,QAAQ,CAAC/C,SAAT,GAAqB;IACnBkD,IAAI,EAAEjD,SAAS,CAACoC,IADG;IAEnBqB,IAAI,EAAEzD,SAAS,CAACmC,MAFG;IAGnBa,EAAE,EAAEhD,SAAS,CAACkC,SAAV,CAAoB,CAAClC,SAAS,CAACmC,MAAX,EAAmBnC,SAAS,CAACE,MAA7B,CAApB,EAA0DC;GAHhE;;AC9DF,IAAMuD,OAAK,GAAG,EAAd;AACA,IAAMC,YAAU,GAAG,KAAnB;AACA,IAAIC,YAAU,GAAG,CAAjB;AAEA,SAASC,aAATrB,CAAqBrE,IAArB,EAA2B2F,OAA3B,EAAoC;MAC5BC,QAAQ,QAAMD,OAAO,CAACE,GAAd,GAAoBF,OAAO,CAACG,MAA5B,GAAqCH,OAAO,CAACI,SAA3D;MACMC,SAAS,GAAGT,OAAK,CAACK,QAAD,CAAL,KAAoBL,OAAK,CAACK,QAAD,CAAL,GAAkB,EAAtC,CAAlB;MAEII,SAAS,CAAChG,IAAD,CAAb,EAAqB,OAAOgG,SAAS,CAAChG,IAAD,CAAhB;MAEfiG,IAAI,GAAG,EAAb;MACMC,MAAM,GAAG3B,YAAY,CAACvE,IAAD,EAAOiG,IAAP,EAAaN,OAAb,CAA3B;MACMQ,MAAM,GAAG;IAAED,MAAM,EAANA,MAAF;IAAUD,IAAI,EAAJA;GAAzB;MAEIR,YAAU,GAAGD,YAAjB,EAA6B;IAC3BQ,SAAS,CAAChG,IAAD,CAAT,GAAkBmG,MAAlB;IACAV,YAAU;;SAGLU,MAAP;;;;;;AAMF,SAASC,SAATA,CAAmBrG,QAAnB,EAA6B4F,OAA7B,EAA2C;MAAdA,OAAc;IAAdA,OAAc,GAAJ,EAAI;;MACrC,OAAOA,OAAP,KAAmB,QAAnB,IAA+BU,KAAK,CAACC,OAAN,CAAcX,OAAd,CAAnC,EAA2D;IACzDA,OAAO,GAAG;MAAE3F,IAAI,EAAE2F;KAAlB;;iBAGiEA,OAL1B;IAKjC3F,IALiC,GAAAuG,QAAA,CAKjCvG,IALiC;8BAK3BwG,KAL2B;IAK3BA,KAL2B,GAAAC,cAAA,cAKnB,KALmB,GAAAA,cAAA;+BAKZX,MALY;IAKZA,MALY,GAAAY,eAAA,cAKH,KALG,GAAAA,eAAA;kCAKIX,SALJ;IAKIA,SALJ,GAAAY,kBAAA,cAKgB,KALhB,GAAAA,kBAAA;MAOnCC,KAAK,GAAG,GAAGC,MAAH,CAAU7G,IAAV,CAAd;SAEO4G,KAAK,CAACE,MAAN,CAAa,UAACC,OAAD,EAAU/G,IAAV,EAAmB;QACjC,CAACA,IAAD,IAASA,IAAI,KAAK,EAAtB,EAA0B,OAAO,IAAP;QACtB+G,OAAJ,EAAa,OAAOA,OAAP;uBAEYrB,aAAW,CAAC1F,IAAD,EAAO;QACzC6F,GAAG,EAAEW,KADoC;QAEzCV,MAAM,EAANA,MAFyC;QAGzCC,SAAS,EAATA;OAHkC,CAJC;MAI7BG,MAJ6B,GAAAc,YAAA,CAI7Bd,MAJ6B;MAIrBD,IAJqB,GAAAe,YAAA,CAIrBf,IAJqB;QAS/B3E,KAAK,GAAG4E,MAAM,CAACe,IAAP,CAAYlH,QAAZ,CAAd;QAEI,CAACuB,KAAL,EAAY,OAAO,IAAP;QAELrB,GAb8B,GAaZqB,KAbY;MAatB4F,MAbsB,GAaZ5F,KAbY,CAAA6F,KAAA;QAc/BhH,OAAO,GAAGJ,QAAQ,KAAKE,GAA7B;QAEIuG,KAAK,IAAI,CAACrG,OAAd,EAAuB,OAAO,IAAP;WAEhB;MACLH,IAAI,EAAJA,IADK;;MAELC,GAAG,EAAED,IAAI,KAAK,GAAT,IAAgBC,GAAG,KAAK,EAAxB,GAA6B,GAA7B,GAAmCA,GAFnC;;MAGLE,OAAO,EAAPA,OAHK;;MAILD,MAAM,EAAE+F,IAAI,CAACa,MAAL,CAAY,UAACM,IAAD,EAAO/B,GAAP,EAAYgC,KAAZ,EAAsB;QACxCD,IAAI,CAAC/B,GAAG,CAAC9F,IAAL,CAAJ,GAAiB2H,MAAM,CAACG,KAAD,CAAvB;eACOD,IAAP;OAFM,EAGL,EAHK;KAJV;GAlBK,EA2BJ,IA3BI,CAAP;;AC3BF,SAASE,eAATA,CAAyB/F,QAAzB,EAAmC;SAC1BL,KAAK,CAACqG,QAAN,CAAeC,KAAf,CAAqBjG,QAArB,MAAmC,CAA1C;;AAGF,SAASkG,eAATA,CAAyBlG,QAAzB,EAAmCnB,KAAnC,EAA0CJ,IAA1C,EAAgD;MACxCqB,KAAK,GAAGE,QAAQ,CAACnB,KAAD,CAAtB;0CAEAgC,OAAO,CACLf,KAAK,KAAKqG,SADL,EAEL,2EACW1H,IAAI,gBAAaA,IAAb,UAAuB,EADtC,qBAEE,gDAJG,CAAP;SAOOqB,KAAK,IAAI,IAAhB;;;;;;IAMIsG,KAAA,0BAAA9H,gBAAA;;;;;;SACJoB,MAAA,YAAAA,OAAA,EAAS;;wBAELC,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAegE,QAAf,QACG,UAAAoE,SAAO,EAAI;OACAA,SAAV,GAAAnG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAAU,+CAAV,CAAT,GAAAA,SAAS,OAAT;UAEMnD,QAAQ,GAAGO,KAAI,CAACT,KAAL,CAAWE,QAAX,IAAuBsH,SAAO,CAACtH,QAAhD;UACMgB,KAAK,GAAGT,KAAI,CAACT,KAAL,CAAWwE,aAAX,GACV/D,KAAI,CAACT,KAAL,CAAWwE,aADD;MAAA,EAEV/D,KAAI,CAACT,KAAL,CAAWJ,IAAX,GACAoG,SAAS,CAAC9F,QAAQ,CAACP,QAAV,EAAoBc,KAAI,CAACT,KAAzB,CADT,GAEAwH,SAAO,CAACtG,KAJZ;UAMMlB,KAAK,GAAA8E,QAAA,KAAQ0C,SAAR;QAAiBtH,QAAQ,EAARA,QAAjB;QAA2BgB,KAAK,EAALA;QAAtC;wBAEsCT,KAAI,CAACT,KAZjC;QAYJmB,QAZI,GAAAsG,WAAA,CAYJtG,QAZI;QAYMuG,SAZN,GAAAD,WAAA,CAYMC,SAZN;QAYiB7G,MAZjB,GAAA4G,WAAA,CAYiB5G,MAZjB;;;UAgBNoF,KAAK,CAACC,OAAN,CAAc/E,QAAd,KAA2B+F,eAAe,CAAC/F,QAAD,CAA9C,EAA0D;QACxDA,QAAQ,GAAG,IAAX;;0BAIAL,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAe4B,QAAf;QAAwBC,KAAK,EAAEjB;SAC5BA,KAAK,CAACkB,KAAN,GACGC,QAAQ,GACN,OAAOA,QAAP,KAAoB,UAApB,GACEE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBACE8F,eAAe,CAAClG,QAAD,EAAWnB,KAAX,EAAkBS,KAAI,CAACT,KAAL,CAAWJ,IAA7B,CADjB,GAEEuB,QAAQ,CAACnB,KAAD,CAHZ,GAIEmB,QALI,GAMNuG,SAAS,gBACT5G,KAAK,CAACC,aAAN,CAAoB2G,SAApB,EAA+B1H,KAA/B,CADS,GAETa,MAAM,GACNA,MAAM,CAACb,KAAD,CADA,GAEN,IAXL,GAYG,OAAOmB,QAAP,KAAoB,UAApB,GACAE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBACE8F,eAAe,CAAClG,QAAD,EAAWnB,KAAX,EAAkBS,KAAI,CAACT,KAAL,CAAWJ,IAA7B,CADjB,GAEEuB,QAAQ,CAACnB,KAAD,CAHV,GAIA,IAjBN,CADF;KArBJ,CADF;;;EAFgBc,KAAK,CAACM,SAAA;AAmD1B,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXgG,KAAK,CAAC/F,SAAN,GAAkB;IAChBL,QAAQ,EAAEM,SAAS,CAACkC,SAAV,CAAoB,CAAClC,SAAS,CAACe,IAAX,EAAiBf,SAAS,CAACC,IAA3B,CAApB,CADM;IAEhBgG,SAAS,EAAE,SAAAA,UAAC1H,KAAD,EAAQ2H,QAAR,EAAqB;UAC1B3H,KAAK,CAAC2H,QAAD,CAAL,IAAmB,CAACC,kBAAkB,CAAC5H,KAAK,CAAC2H,QAAD,CAAN,CAA1C,EAA6D;eACpD,IAAIE,KAAJ,yFAAP;;KAJY;IAShBzB,KAAK,EAAE3E,SAAS,CAACoC,IATD;IAUhB3D,QAAQ,EAAEuB,SAAS,CAACE,MAVJ;IAWhB/B,IAAI,EAAE6B,SAAS,CAACkC,SAAV,CAAoB,CACxBlC,SAAS,CAACmC,MADc,EAExBnC,SAAS,CAACqG,OAAV,CAAkBrG,SAAS,CAACmC,MAA5B,CAFwB,CAApB,CAXU;IAehB/C,MAAM,EAAEY,SAAS,CAACe,IAfF;IAgBhBmD,SAAS,EAAElE,SAAS,CAACoC,IAhBL;IAiBhB6B,MAAM,EAAEjE,SAAS,CAACoC;GAjBpB;EAoBA0D,KAAK,CAAC1F,SAAN,CAAgBlB,iBAAhB,GAAoC,YAAW;4CAC7CqB,OAAO,CACL,EACE,KAAKhC,KAAL,CAAWmB,QAAX,IACA,CAAC+F,eAAe,CAAC,KAAKlH,KAAL,CAAWmB,QAAZ,CADhB,IAEA,KAAKnB,KAAL,CAAW0H,SAHb,CADK,EAML,gHANK,CAAP;4CASA1F,OAAO,CACL,EACE,KAAKhC,KAAL,CAAWmB,QAAX,IACA,CAAC+F,eAAe,CAAC,KAAKlH,KAAL,CAAWmB,QAAZ,CADhB,IAEA,KAAKnB,KAAL,CAAWa,MAHb,CADK,EAML,0GANK,CAAP;4CASAmB,OAAO,CACL,EAAE,KAAKhC,KAAL,CAAW0H,SAAX,IAAwB,KAAK1H,KAAL,CAAWa,MAArC,CADK,EAEL,2GAFK,CAAP;GAnBF;EAyBA0G,KAAK,CAAC1F,SAAN,CAAgBC,kBAAhB,GAAqC,UAASC,SAAT,EAAoB;4CACvDC,OAAO,CACL,EAAE,KAAKhC,KAAL,CAAWE,QAAX,IAAuB,CAAC6B,SAAS,CAAC7B,QAApC,CADK,EAEL,yKAFK,CAAP;4CAKA8B,OAAO,CACL,EAAE,CAAC,KAAKhC,KAAL,CAAWE,QAAZ,IAAwB6B,SAAS,CAAC7B,QAApC,CADK,EAEL,qKAFK,CAAP;GANF;;ACtHF,SAAS6H,eAATA,CAAyBnI,IAAzB,EAA+B;SACtBA,IAAI,CAACoI,MAAL,CAAY,CAAZ,MAAmB,GAAnB,GAAyBpI,IAAzB,GAAgC,MAAMA,IAA7C;;AAGF,SAASqI,WAATA,CAAqBC,QAArB,EAA+BhI,QAA/B,EAAyC;MACnC,CAACgI,QAAL,EAAe,OAAOhI,QAAP;sBAGVA,QADL;IAEEP,QAAQ,EAAEoI,eAAe,CAACG,QAAD,CAAf,GAA4BhI,QAAQ,CAACP;;;AAInD,SAASwI,aAATA,CAAuBD,QAAvB,EAAiChI,QAAjC,EAA2C;MACrC,CAACgI,QAAL,EAAe,OAAOhI,QAAP;MAETkI,IAAI,GAAGL,eAAe,CAACG,QAAD,CAA5B;MAEIhI,QAAQ,CAACP,QAAT,CAAkB0I,OAAlB,CAA0BD,IAA1B,MAAoC,CAAxC,EAA2C,OAAOlI,QAAP;sBAGtCA,QADL;IAEEP,QAAQ,EAAEO,QAAQ,CAACP,QAAT,CAAkB2I,MAAlB,CAAyBF,IAAI,CAACG,MAA9B;;;AAId,SAASC,SAATA,CAAmBtI,QAAnB,EAA6B;SACpB,OAAOA,QAAP,KAAoB,QAApB,GAA+BA,QAA/B,GAA0CuI,UAAU,CAACvI,QAAD,CAA3D;;AAGF,SAASwI,aAATA,CAAuBC,UAAvB,EAAmC;SAC1B,YAAM;4CACXtF,SAAS,QAAQ,mCAAR,EAA6CsF,UAA7C,CAAT,GAAAtF,SAAS,OAAT;GADF;;AAKF,SAASuF,IAATA,CAAA,EAAgB;;;;;;;;IAQVC,YAAA,0BAAApJ,gBAAA;;;;;;;;UAQJqJ,UAAA,GAAa,UAAA5I,QAAQ;aAAIO,KAAA,CAAKsI,UAAL,CAAgB7I,QAAhB,EAA0B,MAA1B,CAAJ;;UACrB8I,aAAA,GAAgB,UAAA9I,QAAQ;aAAIO,KAAA,CAAKsI,UAAL,CAAgB7I,QAAhB,EAA0B,SAA1B,CAAJ;;UACxB+I,YAAA,GAAe;aAAML,IAAN;;UACfM,WAAA,GAAc;aAAMN,IAAN;;;;;SAVdG,UAAA,YAAAA,WAAW7I,QAAX,EAAqBiJ,MAArB,EAA6B;sBACa,KAAKnJ,KADlB;yCACnBkI,QADmB;MACnBA,QADmB,GAAAkB,oBAAA,cACR,EADQ,GAAAA,oBAAA;wCACJhK,OADI;MACJA,OADI,GAAAiK,mBAAA,cACM,EADN,GAAAA,mBAAA;IAE3BjK,OAAO,CAAC+J,MAAR,GAAiBA,MAAjB;IACA/J,OAAO,CAACc,QAAR,GAAmB+H,WAAW,CAACC,QAAD,EAAWrD,cAAc,CAAC3E,QAAD,CAAzB,CAA9B;IACAd,OAAO,CAACS,GAAR,GAAc2I,SAAS,CAACpJ,OAAO,CAACc,QAAT,CAAvB;;SAQFW,MAAA,YAAAA,OAAA,EAAS;uBAC0D,KAAKb,KAD/D;2CACCkI,QADD;MACCA,QADD,GAAAoB,qBAAA,cACY,EADZ,GAAAA,qBAAA;0CACgBlK,OADhB;MACgBA,OADhB,GAAAmK,oBAAA,cAC0B,EAD1B,GAAAA,oBAAA;2CAC8BrJ,QAD9B;MAC8BA,QAD9B,GAAAsJ,qBAAA,cACyC,GADzC,GAAAA,qBAAA;MACiDC,IADjD,GAAAC,6BAAA,CAAAC,YAAA;QAGDxJ,OAAO,GAAG;MACdyJ,UAAU,EAAE,SAAAA,WAAAhK,IAAI;eAAImI,eAAe,CAACG,QAAQ,GAAGM,SAAS,CAAC5I,IAAD,CAArB,CAAnB;OADF;MAEduJ,MAAM,EAAE,KAFM;MAGdjJ,QAAQ,EAAEiI,aAAa,CAACD,QAAD,EAAWrD,cAAc,CAAC3E,QAAD,CAAzB,CAHT;MAIdwE,IAAI,EAAE,KAAKoE,UAJG;MAKdlE,OAAO,EAAE,KAAKoE,aALA;MAMda,EAAE,EAAEnB,aAAa,CAAC,IAAD,CANH;MAOdoB,MAAM,EAAEpB,aAAa,CAAC,QAAD,CAPP;MAQdqB,SAAS,EAAErB,aAAa,CAAC,WAAD,CARV;MASdlI,MAAM,EAAE,KAAKyI,YATC;MAUd1F,KAAK,EAAE,KAAK2F;KAVd;wBAaOpI,KAAA,CAAAC,aAAA,CAACvB,MAAD,EAAAsF,QAAA,KAAY2E,IAAZ;MAAkBtJ,OAAO,EAAEA,OAA3B;MAAoCG,aAAa,EAAElB;OAA1D;;;EA7BuB0B,KAAK,CAACM,SAAA;AAiCjC,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXsH,YAAY,CAACrH,SAAb,GAAyB;IACvB0G,QAAQ,EAAEzG,SAAS,CAACmC,MADG;IAEvBxE,OAAO,EAAEqC,SAAS,CAACE,MAFI;IAGvBzB,QAAQ,EAAEuB,SAAS,CAACkC,SAAV,CAAoB,CAAClC,SAAS,CAACmC,MAAX,EAAmBnC,SAAS,CAACE,MAA7B,CAApB;GAHZ;EAMAkH,YAAY,CAAChH,SAAb,CAAuBlB,iBAAvB,GAA2C,YAAW;4CACpDqB,OAAO,CACL,CAAC,KAAKhC,KAAL,CAAWG,OADP,EAEL,uEACE,yEAHG,CAAP;GADF;;;ACpFF;;;;IAGM6J,MAAA,0BAAAvK,gBAAA;;;;;;SACJoB,MAAA,YAAAA,OAAA,EAAS;;wBAELC,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAegE,QAAf,QACG,UAAAhE,OAAO,EAAI;OACAA,OAAV,GAAAiC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAAU,gDAAV,CAAT,GAAAA,SAAS,OAAT;UAEMnD,QAAQ,GAAGO,KAAI,CAACT,KAAL,CAAWE,QAAX,IAAuBd,OAAO,CAACc,QAAhD;UAEI+J,OAAJ,EAAa/I,KAAb,CALU;;;;;MAWVJ,KAAK,CAACqG,QAAN,CAAe+C,OAAf,CAAuBzJ,KAAI,CAACT,KAAL,CAAWmB,QAAlC,EAA4C,UAAAgJ,KAAK,EAAI;YAC/CjJ,KAAK,IAAI,IAAT,iBAAiBJ,KAAK,CAACsJ,cAAN,CAAqBD,KAArB,CAArB,EAAkD;UAChDF,OAAO,GAAGE,KAAV;cAEMvK,IAAI,GAAGuK,KAAK,CAACnK,KAAN,CAAYJ,IAAZ,IAAoBuK,KAAK,CAACnK,KAAN,CAAYkF,IAA7C;UAEAhE,KAAK,GAAGtB,IAAI,GACRoG,SAAS,CAAC9F,QAAQ,CAACP,QAAV,EAAAmF,QAAA,KAAyBqF,KAAK,CAACnK,KAA/B;YAAsCJ,IAAI,EAAJA;aADvC,GAERR,OAAO,CAAC8B,KAFZ;;OANJ;aAYOA,KAAK,gBACRJ,KAAK,CAACuJ,YAAN,CAAmBJ,OAAnB,EAA4B;QAAE/J,QAAQ,EAARA,QAAF;QAAYsE,aAAa,EAAEtD;OAAvD,CADQ,GAER,IAFJ;KAxBJ,CADF;;;EAFiBJ,KAAK,CAACM,SAAA;AAoC3B,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXyI,MAAM,CAACxI,SAAP,GAAmB;IACjBL,QAAQ,EAAEM,SAAS,CAACC,IADH;IAEjBxB,QAAQ,EAAEuB,SAAS,CAACE;GAFtB;EAKAqI,MAAM,CAACnI,SAAP,CAAiBC,kBAAjB,GAAsC,UAASC,SAAT,EAAoB;4CACxDC,OAAO,CACL,EAAE,KAAKhC,KAAL,CAAWE,QAAX,IAAuB,CAAC6B,SAAS,CAAC7B,QAApC,CADK,EAEL,0KAFK,CAAP;4CAKA8B,OAAO,CACL,EAAE,CAAC,KAAKhC,KAAL,CAAWE,QAAZ,IAAwB6B,SAAS,CAAC7B,QAApC,CADK,EAEL,sKAFK,CAAP;GANF;;;AC9CF;;;;AAGA,SAASoK,UAATA,CAAoBlJ,SAApB,EAA+B;MACvB9B,WAAW,oBAAiB8B,SAAS,CAAC9B,WAAV,IAAyB8B,SAAS,CAACjC,IAApD,OAAjB;MACMoL,CAAC,GAAG,SAAJA,CAAIA,CAAAvK,KAAK,EAAI;QACTwK,mBADS,GACkCxK,KADlC,CACTwK,mBADS;MACeC,cADf,GAAAf,6BAAA,CACkC1J,KADlC;wBAIfc,KAAA,CAAAC,aAAA,CAAC3B,OAAD,CAAegE,QAAf,QACG,UAAAhE,OAAO,EAAI;OAERA,OADF,GAAAiC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,iCAEgB/D,WAFhB,4BAAT,GAAA+D,SAAS,OAAT;0BAKEvC,KAAA,CAAAC,aAAA,CAACK,SAAD,EAAA0D,QAAA,KACM2F,cADN,EAEMrL,OAFN;QAGEsL,GAAG,EAAEF;SAJT;KANJ,CADF;GAHF;EAsBAD,CAAC,CAACjL,WAAF,GAAgBA,WAAhB;EACAiL,CAAC,CAACI,gBAAF,GAAqBvJ,SAArB;6CAEa;IACXmJ,CAAC,CAAC/I,SAAF,GAAc;MACZgJ,mBAAmB,EAAE/I,SAAS,CAACkC,SAAV,CAAoB,CACvClC,SAAS,CAACmC,MAD6B,EAEvCnC,SAAS,CAACe,IAF6B,EAGvCf,SAAS,CAACE,MAH6B,CAApB;KADvB;;SASKiJ,YAAY,CAACL,CAAD,EAAInJ,SAAJ,CAAnB;;ACxCF,IAAMyJ,UAAU,GAAG/J,KAAK,CAAC+J,UAAzB;AAEA,SAAgBC,UAATA,CAAA,EAAsB;6CACd;MAET,OAAOD,UAAP,KAAsB,UADxB,IAAAxJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAEP,yDAFO,CAAT,GAAAA,SAAS,OAAT;;SAMKwH,UAAU,CAACtL,cAAD,CAAjB;;AAGF,SAAgBwL,WAATA,CAAA,EAAuB;6CACf;MAET,OAAOF,UAAP,KAAsB,UADxB,IAAAxJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAEP,0DAFO,CAAT,GAAAA,SAAS,OAAT;;SAMKwH,UAAU,CAACzL,OAAD,CAAV,CAA0Bc,QAAjC;;AAGF,SAAgB8K,SAATA,CAAA,EAAqB;6CACb;MAET,OAAOH,UAAP,KAAsB,UADxB,IAAAxJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAEP,wDAFO,CAAT,GAAAA,SAAS,OAAT;;MAMInC,KAAK,GAAG2J,UAAU,CAACzL,OAAD,CAAV,CAA0B8B,KAAxC;SACOA,KAAK,GAAGA,KAAK,CAACpB,MAAT,GAAkB,EAA9B;;AAGF,SAAgBmL,aAATA,CAAuBrL,IAAvB,EAA6B;6CACrB;MAET,OAAOiL,UAAP,KAAsB,UADxB,IAAAxJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA8B,SAAS,QAEP,4DAFO,CAAT,GAAAA,SAAS,OAAT;;MAMInD,QAAQ,GAAG6K,WAAW,EAA5B;MACM7J,KAAK,GAAG2J,UAAU,CAACzL,OAAD,CAAV,CAA0B8B,KAAxC;SACOtB,IAAI,GAAGoG,SAAS,CAAC9F,QAAQ,CAACP,QAAV,EAAoBC,IAApB,CAAZ,GAAwCsB,KAAnD;;ACrDF,IAAAG,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;MACP,OAAO2J,MAAP,KAAkB,WAAtB,EAAmC;QAC3BC,MAAM,GAAGD,MAAf;QACMjG,GAAG,GAAG,wBAAZ;QACMmG,UAAU,GAAG;MAAEC,GAAG,EAAE,UAAP;MAAmBC,GAAG,EAAE,YAAxB;MAAsCC,GAAG,EAAE;KAA9D;QAEIJ,MAAM,CAAClG,GAAD,CAAN,IAAekG,MAAM,CAAClG,GAAD,CAAN,KAAgB,KAAnC,EAA6D;UACrDuG,gBAAgB,GAAGJ,UAAU,CAACD,MAAM,CAAClG,GAAD,CAAP,CAAnC;UACMwG,kBAAkB,GAAGL,UAAU,CAAC,KAAD,CAArC,CAF2D;;;YAMrD,IAAIvD,KAAJ,CACJ,yBAAuB4D,kBAAvB,2EAC2CD,gBAD3C,8CADI,CAAN;;IAOFL,MAAM,CAAClG,GAAD,CAAN,GAAc,KAAd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}