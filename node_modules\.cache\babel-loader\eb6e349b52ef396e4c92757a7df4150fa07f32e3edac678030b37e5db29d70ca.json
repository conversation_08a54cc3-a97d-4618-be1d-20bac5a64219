{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nvar Typography = function Typography(_a, ref) {\n  var customizePrefixCls = _a.prefixCls,\n    _a$component = _a.component,\n    component = _a$component === void 0 ? 'article' : _a$component,\n    className = _a.className,\n    ariaLabel = _a['aria-label'],\n    setContentRef = _a.setContentRef,\n    children = _a.children,\n    restProps = __rest(_a, [\"prefixCls\", \"component\", \"className\", 'aria-label', \"setContentRef\", \"children\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var mergedRef = ref;\n  if (setContentRef) {\n    devWarning(false, 'Typography', '`setContentRef` is deprecated. Please use `ref` instead.');\n    mergedRef = composeRef(ref, setContentRef);\n  }\n  var Component = component;\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var componentClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    className: componentClassName,\n    \"aria-label\": ariaLabel,\n    ref: mergedRef\n  }, restProps), children);\n};\nvar RefTypography = /*#__PURE__*/React.forwardRef(Typography);\nRefTypography.displayName = 'Typography'; // es default export should use const instead of let\n\nvar ExportTypography = RefTypography;\nexport default ExportTypography;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "composeRef", "ConfigContext", "dev<PERSON><PERSON><PERSON>", "Typography", "_a", "ref", "customizePrefixCls", "prefixCls", "_a$component", "component", "className", "aria<PERSON><PERSON><PERSON>", "setContentRef", "children", "restProps", "_React$useContext", "useContext", "getPrefixCls", "direction", "mergedRef", "Component", "componentClassName", "concat", "createElement", "RefTypography", "forwardRef", "displayName", "ExportTypography"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Typography.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\n\nvar Typography = function Typography(_a, ref) {\n  var customizePrefixCls = _a.prefixCls,\n      _a$component = _a.component,\n      component = _a$component === void 0 ? 'article' : _a$component,\n      className = _a.className,\n      ariaLabel = _a['aria-label'],\n      setContentRef = _a.setContentRef,\n      children = _a.children,\n      restProps = __rest(_a, [\"prefixCls\", \"component\", \"className\", 'aria-label', \"setContentRef\", \"children\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var mergedRef = ref;\n\n  if (setContentRef) {\n    devWarning(false, 'Typography', '`setContentRef` is deprecated. Please use `ref` instead.');\n    mergedRef = composeRef(ref, setContentRef);\n  }\n\n  var Component = component;\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var componentClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  return /*#__PURE__*/React.createElement(Component, _extends({\n    className: componentClassName,\n    \"aria-label\": ariaLabel,\n    ref: mergedRef\n  }, restProps), children);\n};\n\nvar RefTypography = /*#__PURE__*/React.forwardRef(Typography);\nRefTypography.displayName = 'Typography'; // es default export should use const instead of let\n\nvar ExportTypography = RefTypography;\nexport default ExportTypography;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AAEvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAE5C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,EAAE,EAAEC,GAAG,EAAE;EAC5C,IAAIC,kBAAkB,GAAGF,EAAE,CAACG,SAAS;IACjCC,YAAY,GAAGJ,EAAE,CAACK,SAAS;IAC3BA,SAAS,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,YAAY;IAC9DE,SAAS,GAAGN,EAAE,CAACM,SAAS;IACxBC,SAAS,GAAGP,EAAE,CAAC,YAAY,CAAC;IAC5BQ,aAAa,GAAGR,EAAE,CAACQ,aAAa;IAChCC,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IACtBC,SAAS,GAAG9B,MAAM,CAACoB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EAE9G,IAAIW,iBAAiB,GAAGjB,KAAK,CAACkB,UAAU,CAACf,aAAa,CAAC;IACnDgB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,SAAS,GAAGd,GAAG;EAEnB,IAAIO,aAAa,EAAE;IACjBV,UAAU,CAAC,KAAK,EAAE,YAAY,EAAE,0DAA0D,CAAC;IAC3FiB,SAAS,GAAGnB,UAAU,CAACK,GAAG,EAAEO,aAAa,CAAC;EAC5C;EAEA,IAAIQ,SAAS,GAAGX,SAAS;EACzB,IAAIF,SAAS,GAAGU,YAAY,CAAC,YAAY,EAAEX,kBAAkB,CAAC;EAC9D,IAAIe,kBAAkB,GAAGtB,UAAU,CAACQ,SAAS,EAAExB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuC,MAAM,CAACf,SAAS,EAAE,MAAM,CAAC,EAAEW,SAAS,KAAK,KAAK,CAAC,EAAER,SAAS,CAAC;EACjI,OAAO,aAAaZ,KAAK,CAACyB,aAAa,CAACH,SAAS,EAAEtC,QAAQ,CAAC;IAC1D4B,SAAS,EAAEW,kBAAkB;IAC7B,YAAY,EAAEV,SAAS;IACvBN,GAAG,EAAEc;EACP,CAAC,EAAEL,SAAS,CAAC,EAAED,QAAQ,CAAC;AAC1B,CAAC;AAED,IAAIW,aAAa,GAAG,aAAa1B,KAAK,CAAC2B,UAAU,CAACtB,UAAU,CAAC;AAC7DqB,aAAa,CAACE,WAAW,GAAG,YAAY,CAAC,CAAC;;AAE1C,IAAIC,gBAAgB,GAAGH,aAAa;AACpC,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}