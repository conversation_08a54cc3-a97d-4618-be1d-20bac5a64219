{"ast": null, "code": "import axios from \"axios\";\nimport { error } from \"../route\";\n\n// Backend Endpoints Configuration\nconst vercelBackend = 'https://ep-backend-zeta.vercel.app'; // Primary staging backend\nconst localBackend = 'http://localhost:3001'; // Local backend\nconst localBackendAlt = 'http://localhost:3002'; // Alternative local backend\n\n// Smart backend selection with fallback\nlet currentBackend = null;\n\n// Initialize currentBackend immediately\nconst initializeBackend = () => {\n  if (process.env.REACT_APP_API_URL) {\n    currentBackend = process.env.REACT_APP_API_URL;\n  } else {\n    currentBackend = process.env.NODE_ENV === 'production' ? vercelBackend : localBackend;\n  }\n  return currentBackend;\n};\n\n// Initialize on module load\ninitializeBackend();\nconst getBaseURL = () => {\n  // Return current backend or initialize if needed\n  return currentBackend || initializeBackend();\n};\nexport const baseURL = getBaseURL();\n\n// Debug logging\nconsole.log('🔧 API Configuration:');\nconsole.log('📍 Base URL:', baseURL);\nconsole.log('🌍 Environment:', process.env.NODE_ENV);\nconsole.log('🔗 API URL Override:', process.env.REACT_APP_API_URL);\n\n// Proxy configuration for different environments\nexport const baseProxy = (() => {\n  // For Vercel backend, no proxy needed\n  if (baseURL.includes('vercel.app')) {\n    return '';\n  }\n  // For production domains, use /api/ prefix\n  if (window.location.href.includes('eprocurement.winet') || window.location.href.includes('eprocurement.tmselezioni')) {\n    return '/api/';\n  }\n  // For local development, no prefix\n  return '';\n})();\n\n// Debug logging for proxy\nconsole.log('🔀 Proxy Configuration:', baseProxy);\nconsole.log('🌐 Window Location:', window.location.href);\nconsole.log('🎯 Final API Endpoint:', baseURL + baseProxy);\n\n// Backend Health Check\nlet backendHealthStatus = 'unknown';\nlet lastHealthCheck = null;\nconst checkBackendHealth = async function () {\n  let showToast = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  const backendsToTry = [localBackend,\n  // Try local first (3001)\n  localBackendAlt,\n  // Then alternative local (3002)\n  vercelBackend // Then staging as fallback\n  ];\n  for (const backend of backendsToTry) {\n    try {\n      console.log(\"\\uD83D\\uDD0D Checking backend health: \".concat(backend));\n      const response = await fetch(backend + '/health', {\n        method: 'GET',\n        timeout: 3000\n      });\n      if (response.ok) {\n        // Update current backend if different\n        if (currentBackend !== backend) {\n          console.log(\"\\uD83D\\uDD04 Switching backend from \".concat(currentBackend, \" to \").concat(backend));\n          currentBackend = backend;\n        }\n        backendHealthStatus = 'online';\n        console.log(\"\\u2705 Backend is online: \".concat(backend));\n        if (showToast && process.env.REACT_APP_DEBUG === 'true') {\n          showBackendNotification('success', '✅ Backend Connected', \"Successfully connected to \".concat(backend));\n        }\n        lastHealthCheck = new Date();\n        return; // Exit on first successful connection\n      }\n    } catch (error) {\n      console.log(\"\\u274C Backend \".concat(backend, \" failed: \").concat(error.message));\n    }\n  }\n\n  // All backends failed\n  backendHealthStatus = 'offline';\n  console.error('❌ All backends failed');\n  updateStatusIndicator('offline', currentBackend);\n  if (showToast) {\n    showBackendNotification('error', '❌ All Backends Offline', 'Cannot connect to any backend. Please check your connection.');\n  }\n  lastHealthCheck = new Date();\n};\nconst showBackendNotification = (type, title, message) => {\n  // Solo log in console, nessuna notifica visiva\n  if (process.env.REACT_APP_DEBUG === 'true') {\n    console.log(\"\".concat(type === 'success' ? '✅' : '❌', \" \").concat(title, \": \").concat(message));\n  }\n};\n\n// Indicatori di stato rimossi - gestiti dal BackendTestButton\nconst createStatusIndicator = () => {\n  // Non creare più indicatori fissi\n  return null;\n};\nconst updateStatusIndicator = (status, url) => {\n  // Non aggiornare più indicatori fissi\n  // Il BackendTestButton gestisce già lo stato\n};\n\n// Check backend health on load (silenzioso)\nsetTimeout(() => {\n  checkBackendHealth(false); // false = nessun toast\n}, 1000);\n\n/* Google reCaptcha v2 Site Key */\nconst gKeyLocal = \"6LfpdiIeAAAAACLH86utUFKqupLyW7ZcQZJb0N03\"; // localhost\nconst gKeyColl = \"6LdiNykeAAAAACcZzR_bhiiTC7eePzj7lMrsRANx\"; // collaudo\nconst gKeyProd = \"6LekRnkeAAAAAJlPYR3cIG8NrFs5BNf-P3SQa4OU\"; // produzione\nexport const SITE_KEY = window.location.href.includes('eprocurement.winet') ? gKeyProd : window.location.href.includes('centralunit.viniexport') ? gKeyColl : gKeyLocal;\nexport const APIRequest = (method, path, data) => {\n  return new Promise((resolve, reject) => {\n    if (typeof method === \"string\" && typeof path === \"string\") {\n      // Get current backend URL (dynamic) with safe fallback\n      const currentURL = currentBackend || baseURL || 'http://localhost:3002';\n      const safeProxy = baseProxy || '';\n\n      // Simple and safe URL construction\n      const parts = [currentURL.replace(/\\/$/, ''),\n      // Remove trailing slash\n      safeProxy.replace(/^\\/|\\/$/g, ''),\n      // Remove leading/trailing slashes\n      path.replace(/^\\//, '') // Remove leading slash\n      ].filter(part => part.length > 0); // Remove empty parts\n\n      const fullURL = parts.join('/');\n\n      // Log solo in modalità debug\n      if (process.env.REACT_APP_DEBUG === 'true') {\n        console.log('🔗 API Request:', method, fullURL);\n      }\n\n      // Validate URL before making request\n      if (!fullURL || fullURL === 'nullundefinedauth/') {\n        console.error('❌ Invalid URL constructed:', fullURL);\n        throw new Error('Invalid API URL constructed');\n      }\n      axios({\n        timeout: 60 * 60 * 1000,\n        method: method,\n        url: fullURL,\n        data: data,\n        headers: {\n          auth: localStorage.login_token,\n          accept: \"application/json\",\n          \"Content-Type\": \"application/json\"\n        }\n      }).then(res => {\n        var newToken = res.headers.token !== undefined ? res.headers.token : res.data.token !== undefined ? res.data.token : localStorage.getItem(\"login_token\");\n        if (newToken !== \"\") {\n          localStorage.setItem(\"login_token\", newToken);\n        }\n        resolve(res);\n      }).catch(e => {\n        // Enhanced error logging - solo in modalità debug\n        if (process.env.REACT_APP_DEBUG === 'true') {\n          var _e$response, _e$response2;\n          console.error('🚨 API Request Error:', {\n            url: baseURL + baseProxy + path,\n            method: method,\n            error: e,\n            response: e.response,\n            status: (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status,\n            data: (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data,\n            message: e.message\n          });\n        }\n\n        // Check if this is a connection error (backend offline)\n        if (!e.response || e.code === 'NETWORK_ERROR' || e.message.includes('Network Error')) {\n          if (backendHealthStatus !== 'offline') {\n            backendHealthStatus = 'offline';\n            // Log silenzioso per connessione persa\n            console.warn('⚠️ Backend connection lost:', currentURL);\n\n            // Try to switch to a working backend\n            checkBackendHealth(false).then(() => {\n              // If we found a working backend, retry the request\n              if (backendHealthStatus === 'online' && currentBackend !== currentURL) {\n                if (process.env.REACT_APP_DEBUG === 'true') {\n                  console.log('🔄 Retrying request with new backend:', currentBackend);\n                }\n                // Retry the same request with new backend\n                APIRequest(method, path, data).then(resolve).catch(reject);\n                return;\n              }\n            });\n          }\n        }\n        if (e.response !== undefined) {\n          if (e.response.data === \"invalid password\" || e.response.data === \"username not found\") {\n            alert(e.response.data);\n          } else if (e.response.status === 401) {\n            localStorage.setItem(\"login_token\", \"\");\n            setTimeout(() => {\n              window.location.pathname = error;\n            }, 4000);\n          } else if (e.response.status === 500) {\n            window.sessionStorage.setItem(\"Error\", JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data));\n            window.sessionStorage.setItem(\"CodeError\", e.response.status);\n            setTimeout(() => {\n              window.location.pathname = error;\n            }, 4000);\n          }\n        } else {\n          if (e.response !== undefined) {\n            window.sessionStorage.setItem(\"Error\", JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data));\n            window.sessionStorage.setItem(\"CodeError\", e.response.status);\n            setTimeout(() => {\n              window.location.pathname = error;\n            }, 4000);\n          }\n        }\n        reject(e);\n      });\n    }\n  });\n};\n_c = APIRequest;\nvar _c;\n$RefreshReg$(_c, \"APIRequest\");", "map": {"version": 3, "names": ["axios", "error", "vercelBackend", "localBackend", "localBackendAlt", "currentBackend", "initializeBackend", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "getBaseURL", "baseURL", "console", "log", "baseProxy", "includes", "window", "location", "href", "backendHealthStatus", "lastHealthCheck", "checkBackendHealth", "showToast", "arguments", "length", "undefined", "backendsToTry", "backend", "concat", "response", "fetch", "method", "timeout", "ok", "REACT_APP_DEBUG", "showBackendNotification", "Date", "message", "updateStatusIndicator", "type", "title", "createStatusIndicator", "status", "url", "setTimeout", "gKeyLocal", "gKeyColl", "gKeyProd", "SITE_KEY", "APIRequest", "path", "data", "Promise", "resolve", "reject", "currentURL", "safeProxy", "parts", "replace", "filter", "part", "fullURL", "join", "Error", "headers", "auth", "localStorage", "login_token", "accept", "then", "res", "newToken", "token", "getItem", "setItem", "catch", "e", "_e$response", "_e$response2", "code", "warn", "alert", "pathname", "sessionStorage", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/apireq.jsx"], "sourcesContent": ["import axios from \"axios\";\nimport { error } from \"../route\";\n\n// Backend Endpoints Configuration\nconst vercelBackend = 'https://ep-backend-zeta.vercel.app'  // Primary staging backend\nconst localBackend = 'http://localhost:3001'  // Local backend\nconst localBackendAlt = 'http://localhost:3002'  // Alternative local backend\n\n// Smart backend selection with fallback\nlet currentBackend = null;\n\n// Initialize currentBackend immediately\nconst initializeBackend = () => {\n  if (process.env.REACT_APP_API_URL) {\n    currentBackend = process.env.REACT_APP_API_URL;\n  } else {\n    currentBackend = process.env.NODE_ENV === 'production' ? vercelBackend : localBackend;\n  }\n\n  return currentBackend;\n};\n\n// Initialize on module load\ninitializeBackend();\n\nconst getBaseURL = () => {\n  // Return current backend or initialize if needed\n  return currentBackend || initializeBackend();\n};\n\nexport const baseURL = getBaseURL();\n\n// Debug logging\nconsole.log('🔧 API Configuration:');\nconsole.log('📍 Base URL:', baseURL);\nconsole.log('🌍 Environment:', process.env.NODE_ENV);\nconsole.log('🔗 API URL Override:', process.env.REACT_APP_API_URL);\n\n// Proxy configuration for different environments\nexport const baseProxy = (() => {\n  // For Vercel backend, no proxy needed\n  if (baseURL.includes('vercel.app')) {\n    return '';\n  }\n  // For production domains, use /api/ prefix\n  if (window.location.href.includes('eprocurement.winet') ||\n      window.location.href.includes('eprocurement.tmselezioni')) {\n    return '/api/';\n  }\n  // For local development, no prefix\n  return '';\n})();\n\n// Debug logging for proxy\nconsole.log('🔀 Proxy Configuration:', baseProxy);\nconsole.log('🌐 Window Location:', window.location.href);\nconsole.log('🎯 Final API Endpoint:', baseURL + baseProxy);\n\n// Backend Health Check\nlet backendHealthStatus = 'unknown';\nlet lastHealthCheck = null;\n\nconst checkBackendHealth = async (showToast = false) => {\n  const backendsToTry = [\n    localBackend,     // Try local first (3001)\n    localBackendAlt,  // Then alternative local (3002)\n    vercelBackend     // Then staging as fallback\n  ];\n\n  for (const backend of backendsToTry) {\n    try {\n      console.log(`🔍 Checking backend health: ${backend}`);\n      const response = await fetch(backend + '/health', {\n        method: 'GET',\n        timeout: 3000\n      });\n\n      if (response.ok) {\n        // Update current backend if different\n        if (currentBackend !== backend) {\n          console.log(`🔄 Switching backend from ${currentBackend} to ${backend}`);\n          currentBackend = backend;\n        }\n\n        backendHealthStatus = 'online';\n        console.log(`✅ Backend is online: ${backend}`);\n\n        if (showToast && process.env.REACT_APP_DEBUG === 'true') {\n          showBackendNotification('success', '✅ Backend Connected',\n            `Successfully connected to ${backend}`);\n        }\n\n        lastHealthCheck = new Date();\n        return; // Exit on first successful connection\n      }\n    } catch (error) {\n      console.log(`❌ Backend ${backend} failed: ${error.message}`);\n    }\n  }\n\n  // All backends failed\n  backendHealthStatus = 'offline';\n  console.error('❌ All backends failed');\n  updateStatusIndicator('offline', currentBackend);\n\n  if (showToast) {\n    showBackendNotification('error', '❌ All Backends Offline',\n      'Cannot connect to any backend. Please check your connection.');\n  }\n\n  lastHealthCheck = new Date();\n};\n\nconst showBackendNotification = (type, title, message) => {\n  // Solo log in console, nessuna notifica visiva\n  if (process.env.REACT_APP_DEBUG === 'true') {\n    console.log(`${type === 'success' ? '✅' : '❌'} ${title}: ${message}`);\n  }\n};\n\n// Indicatori di stato rimossi - gestiti dal BackendTestButton\nconst createStatusIndicator = () => {\n  // Non creare più indicatori fissi\n  return null;\n};\n\nconst updateStatusIndicator = (status, url) => {\n  // Non aggiornare più indicatori fissi\n  // Il BackendTestButton gestisce già lo stato\n};\n\n// Check backend health on load (silenzioso)\nsetTimeout(() => {\n  checkBackendHealth(false); // false = nessun toast\n}, 1000);\n\n/* Google reCaptcha v2 Site Key */\nconst gKeyLocal = \"6LfpdiIeAAAAACLH86utUFKqupLyW7ZcQZJb0N03\" // localhost\nconst gKeyColl = \"6LdiNykeAAAAACcZzR_bhiiTC7eePzj7lMrsRANx\" // collaudo\nconst gKeyProd = \"6LekRnkeAAAAAJlPYR3cIG8NrFs5BNf-P3SQa4OU\" // produzione\nexport const SITE_KEY = window.location.href.includes('eprocurement.winet') ? gKeyProd : (window.location.href.includes('centralunit.viniexport') ? gKeyColl : gKeyLocal) ;\n\nexport const APIRequest = (method, path, data) => {\n\n  return new Promise((resolve, reject) => {\n    if (typeof method === \"string\" && typeof path === \"string\") {\n\n      // Get current backend URL (dynamic) with safe fallback\n      const currentURL = currentBackend || baseURL || 'http://localhost:3002';\n      const safeProxy = baseProxy || '';\n\n      // Simple and safe URL construction\n      const parts = [\n        currentURL.replace(/\\/$/, ''), // Remove trailing slash\n        safeProxy.replace(/^\\/|\\/$/g, ''), // Remove leading/trailing slashes\n        path.replace(/^\\//, '') // Remove leading slash\n      ].filter(part => part.length > 0); // Remove empty parts\n\n      const fullURL = parts.join('/');\n\n      // Log solo in modalità debug\n      if (process.env.REACT_APP_DEBUG === 'true') {\n        console.log('🔗 API Request:', method, fullURL);\n      }\n\n      // Validate URL before making request\n      if (!fullURL || fullURL === 'nullundefinedauth/') {\n        console.error('❌ Invalid URL constructed:', fullURL);\n        throw new Error('Invalid API URL constructed');\n      }\n\n      axios({\n        timeout: 60 * 60 * 1000,\n        method: method,\n        url: fullURL,\n        data: data,\n        headers: {\n          auth: localStorage.login_token,\n          accept: \"application/json\",\n          \"Content-Type\": \"application/json\"\n        },\n\n      })\n        .then((res) => {\n          var newToken = res.headers.token !== undefined ? res.headers.token : (res.data.token !== undefined ? res.data.token : localStorage.getItem(\"login_token\"));\n          if (newToken !== \"\") {\n            localStorage.setItem(\"login_token\", newToken);\n          }\n          resolve(res);\n        })\n        .catch((e) => {\n          // Enhanced error logging - solo in modalità debug\n          if (process.env.REACT_APP_DEBUG === 'true') {\n            console.error('🚨 API Request Error:', {\n              url: baseURL + baseProxy + path,\n              method: method,\n              error: e,\n              response: e.response,\n              status: e.response?.status,\n              data: e.response?.data,\n              message: e.message\n            });\n          }\n\n          // Check if this is a connection error (backend offline)\n          if (!e.response || e.code === 'NETWORK_ERROR' || e.message.includes('Network Error')) {\n            if (backendHealthStatus !== 'offline') {\n              backendHealthStatus = 'offline';\n              // Log silenzioso per connessione persa\n              console.warn('⚠️ Backend connection lost:', currentURL);\n\n              // Try to switch to a working backend\n              checkBackendHealth(false).then(() => {\n                // If we found a working backend, retry the request\n                if (backendHealthStatus === 'online' && currentBackend !== currentURL) {\n                  if (process.env.REACT_APP_DEBUG === 'true') {\n                    console.log('🔄 Retrying request with new backend:', currentBackend);\n                  }\n                  // Retry the same request with new backend\n                  APIRequest(method, path, data).then(resolve).catch(reject);\n                  return;\n                }\n              });\n            }\n          }\n\n          if (e.response !== undefined) {\n            if (\n              e.response.data === \"invalid password\" ||\n              e.response.data === \"username not found\"\n            ) {\n              alert(e.response.data);\n            } else if (e.response.status === 401) {\n              localStorage.setItem(\"login_token\", \"\");\n              setTimeout(() => {\n                window.location.pathname = error;\n              }, 4000);\n            } else if (e.response.status === 500) {\n              window.sessionStorage.setItem(\n                \"Error\",\n                JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data)\n              );\n              window.sessionStorage.setItem(\"CodeError\", e.response.status);\n              setTimeout(() => {\n                window.location.pathname = error;\n              }, 4000);\n            }\n          } else {\n            if (e.response !== undefined) {\n              window.sessionStorage.setItem(\n                \"Error\",\n                JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data)\n              );\n              window.sessionStorage.setItem(\"CodeError\", e.response.status);\n              setTimeout(() => {\n                window.location.pathname = error;\n              }, 4000);\n            }\n          }\n          reject(e);\n        });\n    }\n  });\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,UAAU;;AAEhC;AACA,MAAMC,aAAa,GAAG,oCAAoC,EAAE;AAC5D,MAAMC,YAAY,GAAG,uBAAuB,EAAE;AAC9C,MAAMC,eAAe,GAAG,uBAAuB,EAAE;;AAEjD;AACA,IAAIC,cAAc,GAAG,IAAI;;AAEzB;AACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;IACjCJ,cAAc,GAAGE,OAAO,CAACC,GAAG,CAACC,iBAAiB;EAChD,CAAC,MAAM;IACLJ,cAAc,GAAGE,OAAO,CAACC,GAAG,CAACE,QAAQ,KAAK,YAAY,GAAGR,aAAa,GAAGC,YAAY;EACvF;EAEA,OAAOE,cAAc;AACvB,CAAC;;AAED;AACAC,iBAAiB,CAAC,CAAC;AAEnB,MAAMK,UAAU,GAAGA,CAAA,KAAM;EACvB;EACA,OAAON,cAAc,IAAIC,iBAAiB,CAAC,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMM,OAAO,GAAGD,UAAU,CAAC,CAAC;;AAEnC;AACAE,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;AACpCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;AACpCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,OAAO,CAACC,GAAG,CAACE,QAAQ,CAAC;AACpDG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEP,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAElE;AACA,OAAO,MAAMM,SAAS,GAAG,CAAC,MAAM;EAC9B;EACA,IAAIH,OAAO,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;IAClC,OAAO,EAAE;EACX;EACA;EACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,oBAAoB,CAAC,IACnDC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IAC7D,OAAO,OAAO;EAChB;EACA;EACA,OAAO,EAAE;AACX,CAAC,EAAE,CAAC;;AAEJ;AACAH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,SAAS,CAAC;AACjDF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;AACxDN,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,OAAO,GAAGG,SAAS,CAAC;;AAE1D;AACA,IAAIK,mBAAmB,GAAG,SAAS;AACnC,IAAIC,eAAe,GAAG,IAAI;AAE1B,MAAMC,kBAAkB,GAAG,eAAAA,CAAA,EAA6B;EAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACjD,MAAMG,aAAa,GAAG,CACpBxB,YAAY;EAAM;EAClBC,eAAe;EAAG;EAClBF,aAAa,CAAK;EAAA,CACnB;EAED,KAAK,MAAM0B,OAAO,IAAID,aAAa,EAAE;IACnC,IAAI;MACFd,OAAO,CAACC,GAAG,0CAAAe,MAAA,CAAgCD,OAAO,CAAE,CAAC;MACrD,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAACH,OAAO,GAAG,SAAS,EAAE;QAChDI,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACI,EAAE,EAAE;QACf;QACA,IAAI7B,cAAc,KAAKuB,OAAO,EAAE;UAC9Bf,OAAO,CAACC,GAAG,wCAAAe,MAAA,CAA8BxB,cAAc,UAAAwB,MAAA,CAAOD,OAAO,CAAE,CAAC;UACxEvB,cAAc,GAAGuB,OAAO;QAC1B;QAEAR,mBAAmB,GAAG,QAAQ;QAC9BP,OAAO,CAACC,GAAG,8BAAAe,MAAA,CAAyBD,OAAO,CAAE,CAAC;QAE9C,IAAIL,SAAS,IAAIhB,OAAO,CAACC,GAAG,CAAC2B,eAAe,KAAK,MAAM,EAAE;UACvDC,uBAAuB,CAAC,SAAS,EAAE,qBAAqB,+BAAAP,MAAA,CACzBD,OAAO,CAAE,CAAC;QAC3C;QAEAP,eAAe,GAAG,IAAIgB,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC;MACV;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdY,OAAO,CAACC,GAAG,mBAAAe,MAAA,CAAcD,OAAO,eAAAC,MAAA,CAAY5B,KAAK,CAACqC,OAAO,CAAE,CAAC;IAC9D;EACF;;EAEA;EACAlB,mBAAmB,GAAG,SAAS;EAC/BP,OAAO,CAACZ,KAAK,CAAC,uBAAuB,CAAC;EACtCsC,qBAAqB,CAAC,SAAS,EAAElC,cAAc,CAAC;EAEhD,IAAIkB,SAAS,EAAE;IACba,uBAAuB,CAAC,OAAO,EAAE,wBAAwB,EACvD,8DAA8D,CAAC;EACnE;EAEAf,eAAe,GAAG,IAAIgB,IAAI,CAAC,CAAC;AAC9B,CAAC;AAED,MAAMD,uBAAuB,GAAGA,CAACI,IAAI,EAAEC,KAAK,EAAEH,OAAO,KAAK;EACxD;EACA,IAAI/B,OAAO,CAACC,GAAG,CAAC2B,eAAe,KAAK,MAAM,EAAE;IAC1CtB,OAAO,CAACC,GAAG,IAAAe,MAAA,CAAIW,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG,OAAAX,MAAA,CAAIY,KAAK,QAAAZ,MAAA,CAAKS,OAAO,CAAE,CAAC;EACvE;AACF,CAAC;;AAED;AACA,MAAMI,qBAAqB,GAAGA,CAAA,KAAM;EAClC;EACA,OAAO,IAAI;AACb,CAAC;AAED,MAAMH,qBAAqB,GAAGA,CAACI,MAAM,EAAEC,GAAG,KAAK;EAC7C;EACA;AAAA,CACD;;AAED;AACAC,UAAU,CAAC,MAAM;EACfvB,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,CAAC,EAAE,IAAI,CAAC;;AAER;AACA,MAAMwB,SAAS,GAAG,0CAA0C,EAAC;AAC7D,MAAMC,QAAQ,GAAG,0CAA0C,EAAC;AAC5D,MAAMC,QAAQ,GAAG,0CAA0C,EAAC;AAC5D,OAAO,MAAMC,QAAQ,GAAGhC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,oBAAoB,CAAC,GAAGgC,QAAQ,GAAI/B,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,wBAAwB,CAAC,GAAG+B,QAAQ,GAAGD,SAAU;AAEzK,OAAO,MAAMI,UAAU,GAAGA,CAAClB,MAAM,EAAEmB,IAAI,EAAEC,IAAI,KAAK;EAEhD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI,OAAOvB,MAAM,KAAK,QAAQ,IAAI,OAAOmB,IAAI,KAAK,QAAQ,EAAE;MAE1D;MACA,MAAMK,UAAU,GAAGnD,cAAc,IAAIO,OAAO,IAAI,uBAAuB;MACvE,MAAM6C,SAAS,GAAG1C,SAAS,IAAI,EAAE;;MAEjC;MACA,MAAM2C,KAAK,GAAG,CACZF,UAAU,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAAE;MAC/BF,SAAS,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MAAE;MACnCR,IAAI,CAACQ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAAA,CACzB,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACpC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEnC,MAAMqC,OAAO,GAAGJ,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;;MAE/B;MACA,IAAIxD,OAAO,CAACC,GAAG,CAAC2B,eAAe,KAAK,MAAM,EAAE;QAC1CtB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkB,MAAM,EAAE8B,OAAO,CAAC;MACjD;;MAEA;MACA,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,oBAAoB,EAAE;QAChDjD,OAAO,CAACZ,KAAK,CAAC,4BAA4B,EAAE6D,OAAO,CAAC;QACpD,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEAhE,KAAK,CAAC;QACJiC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACvBD,MAAM,EAAEA,MAAM;QACdY,GAAG,EAAEkB,OAAO;QACZV,IAAI,EAAEA,IAAI;QACVa,OAAO,EAAE;UACPC,IAAI,EAAEC,YAAY,CAACC,WAAW;UAC9BC,MAAM,EAAE,kBAAkB;UAC1B,cAAc,EAAE;QAClB;MAEF,CAAC,CAAC,CACCC,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,QAAQ,GAAGD,GAAG,CAACN,OAAO,CAACQ,KAAK,KAAK/C,SAAS,GAAG6C,GAAG,CAACN,OAAO,CAACQ,KAAK,GAAIF,GAAG,CAACnB,IAAI,CAACqB,KAAK,KAAK/C,SAAS,GAAG6C,GAAG,CAACnB,IAAI,CAACqB,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,aAAa,CAAE;QAC1J,IAAIF,QAAQ,KAAK,EAAE,EAAE;UACnBL,YAAY,CAACQ,OAAO,CAAC,aAAa,EAAEH,QAAQ,CAAC;QAC/C;QACAlB,OAAO,CAACiB,GAAG,CAAC;MACd,CAAC,CAAC,CACDK,KAAK,CAAEC,CAAC,IAAK;QACZ;QACA,IAAItE,OAAO,CAACC,GAAG,CAAC2B,eAAe,KAAK,MAAM,EAAE;UAAA,IAAA2C,WAAA,EAAAC,YAAA;UAC1ClE,OAAO,CAACZ,KAAK,CAAC,uBAAuB,EAAE;YACrC2C,GAAG,EAAEhC,OAAO,GAAGG,SAAS,GAAGoC,IAAI;YAC/BnB,MAAM,EAAEA,MAAM;YACd/B,KAAK,EAAE4E,CAAC;YACR/C,QAAQ,EAAE+C,CAAC,CAAC/C,QAAQ;YACpBa,MAAM,GAAAmC,WAAA,GAAED,CAAC,CAAC/C,QAAQ,cAAAgD,WAAA,uBAAVA,WAAA,CAAYnC,MAAM;YAC1BS,IAAI,GAAA2B,YAAA,GAAEF,CAAC,CAAC/C,QAAQ,cAAAiD,YAAA,uBAAVA,YAAA,CAAY3B,IAAI;YACtBd,OAAO,EAAEuC,CAAC,CAACvC;UACb,CAAC,CAAC;QACJ;;QAEA;QACA,IAAI,CAACuC,CAAC,CAAC/C,QAAQ,IAAI+C,CAAC,CAACG,IAAI,KAAK,eAAe,IAAIH,CAAC,CAACvC,OAAO,CAACtB,QAAQ,CAAC,eAAe,CAAC,EAAE;UACpF,IAAII,mBAAmB,KAAK,SAAS,EAAE;YACrCA,mBAAmB,GAAG,SAAS;YAC/B;YACAP,OAAO,CAACoE,IAAI,CAAC,6BAA6B,EAAEzB,UAAU,CAAC;;YAEvD;YACAlC,kBAAkB,CAAC,KAAK,CAAC,CAACgD,IAAI,CAAC,MAAM;cACnC;cACA,IAAIlD,mBAAmB,KAAK,QAAQ,IAAIf,cAAc,KAAKmD,UAAU,EAAE;gBACrE,IAAIjD,OAAO,CAACC,GAAG,CAAC2B,eAAe,KAAK,MAAM,EAAE;kBAC1CtB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAET,cAAc,CAAC;gBACtE;gBACA;gBACA6C,UAAU,CAAClB,MAAM,EAAEmB,IAAI,EAAEC,IAAI,CAAC,CAACkB,IAAI,CAAChB,OAAO,CAAC,CAACsB,KAAK,CAACrB,MAAM,CAAC;gBAC1D;cACF;YACF,CAAC,CAAC;UACJ;QACF;QAEA,IAAIsB,CAAC,CAAC/C,QAAQ,KAAKJ,SAAS,EAAE;UAC5B,IACEmD,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,KAAK,kBAAkB,IACtCyB,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,KAAK,oBAAoB,EACxC;YACA8B,KAAK,CAACL,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAAC;UACxB,CAAC,MAAM,IAAIyB,CAAC,CAAC/C,QAAQ,CAACa,MAAM,KAAK,GAAG,EAAE;YACpCwB,YAAY,CAACQ,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;YACvC9B,UAAU,CAAC,MAAM;cACf5B,MAAM,CAACC,QAAQ,CAACiE,QAAQ,GAAGlF,KAAK;YAClC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM,IAAI4E,CAAC,CAAC/C,QAAQ,CAACa,MAAM,KAAK,GAAG,EAAE;YACpC1B,MAAM,CAACmE,cAAc,CAACT,OAAO,CAC3B,OAAO,EACPU,IAAI,CAACC,SAAS,CAACT,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAACd,OAAO,KAAKZ,SAAS,GAAGmD,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAACd,OAAO,GAAGuC,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAClG,CAAC;YACDnC,MAAM,CAACmE,cAAc,CAACT,OAAO,CAAC,WAAW,EAAEE,CAAC,CAAC/C,QAAQ,CAACa,MAAM,CAAC;YAC7DE,UAAU,CAAC,MAAM;cACf5B,MAAM,CAACC,QAAQ,CAACiE,QAAQ,GAAGlF,KAAK;YAClC,CAAC,EAAE,IAAI,CAAC;UACV;QACF,CAAC,MAAM;UACL,IAAI4E,CAAC,CAAC/C,QAAQ,KAAKJ,SAAS,EAAE;YAC5BT,MAAM,CAACmE,cAAc,CAACT,OAAO,CAC3B,OAAO,EACPU,IAAI,CAACC,SAAS,CAACT,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAACd,OAAO,KAAKZ,SAAS,GAAGmD,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAACd,OAAO,GAAGuC,CAAC,CAAC/C,QAAQ,CAACsB,IAAI,CAClG,CAAC;YACDnC,MAAM,CAACmE,cAAc,CAACT,OAAO,CAAC,WAAW,EAAEE,CAAC,CAAC/C,QAAQ,CAACa,MAAM,CAAC;YAC7DE,UAAU,CAAC,MAAM;cACf5B,MAAM,CAACC,QAAQ,CAACiE,QAAQ,GAAGlF,KAAK;YAClC,CAAC,EAAE,IAAI,CAAC;UACV;QACF;QACAsD,MAAM,CAACsB,CAAC,CAAC;MACX,CAAC,CAAC;IACN;EACF,CAAC,CAAC;AACJ,CAAC;AAACU,EAAA,GAzHWrC,UAAU;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}