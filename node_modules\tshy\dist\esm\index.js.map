{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAEA,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,KAAK,MAAM,YAAY,CAAA;AAC9B,OAAO,KAAK,YAAY,MAAM,cAAc,CAAA;AAC5C,OAAO,cAAc,CAAA;AACrB,OAAO,GAAG,MAAM,cAAc,CAAA;AAC9B,OAAO,KAAK,MAAM,YAAY,CAAA;AAC9B,OAAO,KAAK,MAAM,YAAY,CAAA;AAE9B,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,CAAA;AAElC,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE;IACtB,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,QAAQ,GAAG,EAAE,CAAC;YACZ,KAAK,QAAQ,CAAC;YACd,KAAK,IAAI;gBACP,OAAO,KAAK,EAAE,CAAA;YAChB,KAAK,SAAS,CAAC;YACf,KAAK,IAAI;gBACP,OAAO,KAAK,EAAE,CAAA;YAChB;gBACE,OAAO,KAAK,CAAC,qBAAqB,GAAG,EAAE,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;IAED,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;IAChE,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,CAAA;IACvD,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAA;IAElD,MAAM,KAAK,EAAE,CAAA;IAEb,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;AAChD,CAAC,CAAA;AACD,MAAM,IAAI,EAAE,CAAA", "sourcesContent": ["#!/usr/bin/env node\n\nimport chalk from 'chalk'\nimport build from './build.js'\nimport * as debugConsole from './console.js'\nimport './exports.js'\nimport pkg from './package.js'\nimport usage from './usage.js'\nimport watch from './watch.js'\n\nconst { exports: exp, tshy } = pkg\n\nconst main = async () => {\n  for (const arg of process.argv.slice(2)) {\n    switch (arg) {\n      case '--help':\n      case '-h':\n        return usage()\n      case '--watch':\n      case '-w':\n        return watch()\n      default:\n        return usage(`Unknown argument: ${arg}`)\n    }\n  }\n\n  debugConsole.debug(chalk.yellow.bold('building'), process.cwd())\n  debugConsole.debug(chalk.cyan.dim('tshy config'), tshy)\n  debugConsole.debug(chalk.cyan.dim('exports'), exp)\n\n  await build()\n\n  debugConsole.log(chalk.bold.green('success!'))\n}\nawait main()\n"]}