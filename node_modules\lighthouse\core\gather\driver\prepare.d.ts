/**
 * Prepares a target for observational analysis by setting throttling and network headers/blocked patterns.
 *
 * This method assumes `prepareTargetForNavigationMode` or `prepareTargetForTimespanMode` has already been invoked.
 *
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {LH.Config.Settings} settings
 * @param {{disableThrottling: boolean, blockedUrlPatterns?: string[]}} options
 */
export function prepareThrottlingAndNetwork(session: LH.Gatherer.FRProtocolSession, settings: LH.Config.Settings, options: {
    disableThrottling: boolean;
    blockedUrlPatterns?: string[];
}): Promise<void>;
/**
 * Prepares a target to be analyzed in timespan mode by enabling protocol domains, emulation, and throttling.
 *
 * @param {LH.Gatherer.FRTransitionalDriver} driver
 * @param {LH.Config.Settings} settings
 */
export function prepareTargetForTimespanMode(driver: LH.Gatherer.FRTransitionalDriver, settings: LH.Config.Settings): Promise<void>;
/**
 * Prepares a target to be analyzed in navigation mode by enabling protocol domains, emulation, and new document
 * handlers for global APIs or error handling.
 *
 * This method should be used in combination with `prepareTargetForIndividualNavigation` before a specific navigation occurs.
 *
 * @param {LH.Gatherer.FRTransitionalDriver} driver
 * @param {LH.Config.Settings} settings
 */
export function prepareTargetForNavigationMode(driver: LH.Gatherer.FRTransitionalDriver, settings: LH.Config.Settings): Promise<void>;
/**
 * Prepares a target for a particular navigation by resetting storage and setting network.
 *
 * This method assumes `prepareTargetForNavigationMode` has already been invoked.
 *
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {LH.Config.Settings} settings
 * @param {Pick<LH.Config.NavigationDefn, 'disableThrottling'|'disableStorageReset'|'blockedUrlPatterns'> & {requestor: LH.NavigationRequestor}} navigation
 * @return {Promise<{warnings: Array<LH.IcuMessage>}>}
 */
export function prepareTargetForIndividualNavigation(session: LH.Gatherer.FRProtocolSession, settings: LH.Config.Settings, navigation: Pick<import("../../../types/config.js").default.NavigationDefn, "blockedUrlPatterns" | "disableStorageReset" | "disableThrottling"> & {
    requestor: LH.NavigationRequestor;
}): Promise<{
    warnings: Array<LH.IcuMessage>;
}>;
/**
 * Enables `Debugger` domain to receive async stacktrace information on network request initiators.
 * This is critical for tracking attribution of tasks and performance simulation accuracy.
 * @param {LH.Gatherer.FRProtocolSession} session
 */
export function enableAsyncStacks(session: LH.Gatherer.FRProtocolSession): Promise<() => Promise<void>>;
//# sourceMappingURL=prepare.d.ts.map