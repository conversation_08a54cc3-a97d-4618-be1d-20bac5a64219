{"ast": null, "code": "import { Row } from '../grid';\nexport default Row;", "map": {"version": 3, "names": ["Row"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/row/index.js"], "sourcesContent": ["import { Row } from '../grid';\nexport default Row;"], "mappings": "AAAA,SAASA,GAAG,QAAQ,SAAS;AAC7B,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}