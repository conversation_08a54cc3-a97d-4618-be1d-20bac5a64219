{"ast": null, "code": "export function hasAddon(props) {\n  return !!(props.addonBefore || props.addonAfter);\n}\nexport function hasPrefixSuffix(props) {\n  return !!(props.prefix || props.suffix || props.allowClear);\n}\nexport function resolveOnChange(target, e, onChange, targetValue) {\n  if (!onChange) {\n    return;\n  }\n  var event = e;\n  if (e.type === 'click') {\n    // Clone a new target for event.\n    // Avoid the following usage, the setQuery method gets the original value.\n    //\n    // const [query, setQuery] = React.useState('');\n    // <Input\n    //   allowClear\n    //   value={query}\n    //   onChange={(e)=> {\n    //     setQuery((prevStatus) => e.target.value);\n    //   }}\n    // />\n    var currentTarget = target.cloneNode(true); // click clear icon\n\n    event = Object.create(e, {\n      target: {\n        value: currentTarget\n      },\n      currentTarget: {\n        value: currentTarget\n      }\n    });\n    currentTarget.value = '';\n    onChange(event);\n    return;\n  } // Trigger by composition event, this means we need force change the input value\n\n  if (targetValue !== undefined) {\n    event = Object.create(e, {\n      target: {\n        value: target\n      },\n      currentTarget: {\n        value: target\n      }\n    });\n    target.value = targetValue;\n    onChange(event);\n    return;\n  }\n  onChange(event);\n}\nexport function triggerFocus(element, option) {\n  if (!element) return;\n  element.focus(option); // Selection content\n\n  var _ref = option || {},\n    cursor = _ref.cursor;\n  if (cursor) {\n    var len = element.value.length;\n    switch (cursor) {\n      case 'start':\n        element.setSelectionRange(0, 0);\n        break;\n      case 'end':\n        element.setSelectionRange(len, len);\n        break;\n      default:\n        element.setSelectionRange(0, len);\n    }\n  }\n}\nexport function fixControlledValue(value) {\n  if (typeof value === 'undefined' || value === null) {\n    return '';\n  }\n  return String(value);\n}", "map": {"version": 3, "names": ["hasAddon", "props", "addonBefore", "addonAfter", "hasPrefixSuffix", "prefix", "suffix", "allowClear", "resolveOnChange", "target", "e", "onChange", "targetValue", "event", "type", "currentTarget", "cloneNode", "Object", "create", "value", "undefined", "triggerFocus", "element", "option", "focus", "_ref", "cursor", "len", "length", "setSelectionRange", "fixControlledValue", "String"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-input/es/utils/commonUtils.js"], "sourcesContent": ["export function hasAddon(props) {\n  return !!(props.addonBefore || props.addonAfter);\n}\nexport function hasPrefixSuffix(props) {\n  return !!(props.prefix || props.suffix || props.allowClear);\n}\nexport function resolveOnChange(target, e, onChange, targetValue) {\n  if (!onChange) {\n    return;\n  }\n\n  var event = e;\n\n  if (e.type === 'click') {\n    // Clone a new target for event.\n    // Avoid the following usage, the setQuery method gets the original value.\n    //\n    // const [query, setQuery] = React.useState('');\n    // <Input\n    //   allowClear\n    //   value={query}\n    //   onChange={(e)=> {\n    //     setQuery((prevStatus) => e.target.value);\n    //   }}\n    // />\n    var currentTarget = target.cloneNode(true); // click clear icon\n\n    event = Object.create(e, {\n      target: {\n        value: currentTarget\n      },\n      currentTarget: {\n        value: currentTarget\n      }\n    });\n    currentTarget.value = '';\n    onChange(event);\n    return;\n  } // Trigger by composition event, this means we need force change the input value\n\n\n  if (targetValue !== undefined) {\n    event = Object.create(e, {\n      target: {\n        value: target\n      },\n      currentTarget: {\n        value: target\n      }\n    });\n    target.value = targetValue;\n    onChange(event);\n    return;\n  }\n\n  onChange(event);\n}\nexport function triggerFocus(element, option) {\n  if (!element) return;\n  element.focus(option); // Selection content\n\n  var _ref = option || {},\n      cursor = _ref.cursor;\n\n  if (cursor) {\n    var len = element.value.length;\n\n    switch (cursor) {\n      case 'start':\n        element.setSelectionRange(0, 0);\n        break;\n\n      case 'end':\n        element.setSelectionRange(len, len);\n        break;\n\n      default:\n        element.setSelectionRange(0, len);\n    }\n  }\n}\nexport function fixControlledValue(value) {\n  if (typeof value === 'undefined' || value === null) {\n    return '';\n  }\n\n  return String(value);\n}"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,CAAC,EAAEA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACE,UAAU,CAAC;AAClD;AACA,OAAO,SAASC,eAAeA,CAACH,KAAK,EAAE;EACrC,OAAO,CAAC,EAAEA,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACK,MAAM,IAAIL,KAAK,CAACM,UAAU,CAAC;AAC7D;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAChE,IAAI,CAACD,QAAQ,EAAE;IACb;EACF;EAEA,IAAIE,KAAK,GAAGH,CAAC;EAEb,IAAIA,CAAC,CAACI,IAAI,KAAK,OAAO,EAAE;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,aAAa,GAAGN,MAAM,CAACO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE5CH,KAAK,GAAGI,MAAM,CAACC,MAAM,CAACR,CAAC,EAAE;MACvBD,MAAM,EAAE;QACNU,KAAK,EAAEJ;MACT,CAAC;MACDA,aAAa,EAAE;QACbI,KAAK,EAAEJ;MACT;IACF,CAAC,CAAC;IACFA,aAAa,CAACI,KAAK,GAAG,EAAE;IACxBR,QAAQ,CAACE,KAAK,CAAC;IACf;EACF,CAAC,CAAC;;EAGF,IAAID,WAAW,KAAKQ,SAAS,EAAE;IAC7BP,KAAK,GAAGI,MAAM,CAACC,MAAM,CAACR,CAAC,EAAE;MACvBD,MAAM,EAAE;QACNU,KAAK,EAAEV;MACT,CAAC;MACDM,aAAa,EAAE;QACbI,KAAK,EAAEV;MACT;IACF,CAAC,CAAC;IACFA,MAAM,CAACU,KAAK,GAAGP,WAAW;IAC1BD,QAAQ,CAACE,KAAK,CAAC;IACf;EACF;EAEAF,QAAQ,CAACE,KAAK,CAAC;AACjB;AACA,OAAO,SAASQ,YAAYA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC5C,IAAI,CAACD,OAAO,EAAE;EACdA,OAAO,CAACE,KAAK,CAACD,MAAM,CAAC,CAAC,CAAC;;EAEvB,IAAIE,IAAI,GAAGF,MAAM,IAAI,CAAC,CAAC;IACnBG,MAAM,GAAGD,IAAI,CAACC,MAAM;EAExB,IAAIA,MAAM,EAAE;IACV,IAAIC,GAAG,GAAGL,OAAO,CAACH,KAAK,CAACS,MAAM;IAE9B,QAAQF,MAAM;MACZ,KAAK,OAAO;QACVJ,OAAO,CAACO,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/B;MAEF,KAAK,KAAK;QACRP,OAAO,CAACO,iBAAiB,CAACF,GAAG,EAAEA,GAAG,CAAC;QACnC;MAEF;QACEL,OAAO,CAACO,iBAAiB,CAAC,CAAC,EAAEF,GAAG,CAAC;IACrC;EACF;AACF;AACA,OAAO,SAASG,kBAAkBA,CAACX,KAAK,EAAE;EACxC,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClD,OAAO,EAAE;EACX;EAEA,OAAOY,MAAM,CAACZ,KAAK,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}