{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"forwardedRef\"];\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nimport React from 'react';\nimport { useTranslation } from './useTranslation';\nimport { getDisplayName } from './utils';\nexport function withTranslation(ns) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      var forwardedRef = _ref.forwardedRef,\n        rest = _objectWithoutProperties(_ref, _excluded);\n      var _useTranslation = useTranslation(ns, rest),\n        _useTranslation2 = _slicedToArray(_useTranslation, 3),\n        t = _useTranslation2[0],\n        i18n = _useTranslation2[1],\n        ready = _useTranslation2[2];\n      var passDownProps = _objectSpread(_objectSpread({}, rest), {}, {\n        t: t,\n        i18n: i18n,\n        tReady: ready\n      });\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n      return React.createElement(WrappedComponent, passDownProps);\n    }\n    I18nextWithTranslation.displayName = \"withI18nextTranslation(\".concat(getDisplayName(WrappedComponent), \")\");\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n    var forwardRef = function forwardRef(props, ref) {\n      return React.createElement(I18nextWithTranslation, Object.assign({}, props, {\n        forwardedRef: ref\n      }));\n    };\n    return options.withRef ? React.forwardRef(forwardRef) : I18nextWithTranslation;\n  };\n}", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "_objectWithoutProperties", "_excluded", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "React", "useTranslation", "getDisplayName", "withTranslation", "ns", "options", "undefined", "Extend", "WrappedComponent", "I18nextWithTranslation", "_ref", "forwardedRef", "rest", "_useTranslation", "_useTranslation2", "t", "i18n", "ready", "passDownProps", "tReady", "with<PERSON>ef", "ref", "createElement", "displayName", "concat", "forwardRef", "props", "assign"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/withTranslation.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"forwardedRef\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport React from 'react';\nimport { useTranslation } from './useTranslation';\nimport { getDisplayName } from './utils';\nexport function withTranslation(ns) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      var forwardedRef = _ref.forwardedRef,\n          rest = _objectWithoutProperties(_ref, _excluded);\n\n      var _useTranslation = useTranslation(ns, rest),\n          _useTranslation2 = _slicedToArray(_useTranslation, 3),\n          t = _useTranslation2[0],\n          i18n = _useTranslation2[1],\n          ready = _useTranslation2[2];\n\n      var passDownProps = _objectSpread(_objectSpread({}, rest), {}, {\n        t: t,\n        i18n: i18n,\n        tReady: ready\n      });\n\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n\n      return React.createElement(WrappedComponent, passDownProps);\n    }\n\n    I18nextWithTranslation.displayName = \"withI18nextTranslation(\".concat(getDisplayName(WrappedComponent), \")\");\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n\n    var forwardRef = function forwardRef(props, ref) {\n      return React.createElement(I18nextWithTranslation, Object.assign({}, props, {\n        forwardedRef: ref\n      }));\n    };\n\n    return options.withRef ? React.forwardRef(forwardRef) : I18nextWithTranslation;\n  };\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uCAAuC;AACnE,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,wBAAwB,MAAM,gDAAgD;AACrF,IAAIC,SAAS,GAAG,CAAC,cAAc,CAAC;AAEhC,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAExB,eAAe,CAACkB,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACiB,yBAAyB,EAAE;MAAEjB,MAAM,CAACkB,gBAAgB,CAACR,MAAM,EAAEV,MAAM,CAACiB,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACmB,cAAc,CAACT,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,OAAOU,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,SAAS;AACxC,OAAO,SAASC,eAAeA,CAACC,EAAE,EAAE;EAClC,IAAIC,OAAO,GAAGb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKc,SAAS,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,OAAO,SAASe,MAAMA,CAACC,gBAAgB,EAAE;IACvC,SAASC,sBAAsBA,CAACC,IAAI,EAAE;MACpC,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;QAChCC,IAAI,GAAGtC,wBAAwB,CAACoC,IAAI,EAAEnC,SAAS,CAAC;MAEpD,IAAIsC,eAAe,GAAGZ,cAAc,CAACG,EAAE,EAAEQ,IAAI,CAAC;QAC1CE,gBAAgB,GAAGzC,cAAc,CAACwC,eAAe,EAAE,CAAC,CAAC;QACrDE,CAAC,GAAGD,gBAAgB,CAAC,CAAC,CAAC;QACvBE,IAAI,GAAGF,gBAAgB,CAAC,CAAC,CAAC;QAC1BG,KAAK,GAAGH,gBAAgB,CAAC,CAAC,CAAC;MAE/B,IAAII,aAAa,GAAG7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7DG,CAAC,EAAEA,CAAC;QACJC,IAAI,EAAEA,IAAI;QACVG,MAAM,EAAEF;MACV,CAAC,CAAC;MAEF,IAAIZ,OAAO,CAACe,OAAO,IAAIT,YAAY,EAAE;QACnCO,aAAa,CAACG,GAAG,GAAGV,YAAY;MAClC,CAAC,MAAM,IAAI,CAACN,OAAO,CAACe,OAAO,IAAIT,YAAY,EAAE;QAC3CO,aAAa,CAACP,YAAY,GAAGA,YAAY;MAC3C;MAEA,OAAOX,KAAK,CAACsB,aAAa,CAACd,gBAAgB,EAAEU,aAAa,CAAC;IAC7D;IAEAT,sBAAsB,CAACc,WAAW,GAAG,yBAAyB,CAACC,MAAM,CAACtB,cAAc,CAACM,gBAAgB,CAAC,EAAE,GAAG,CAAC;IAC5GC,sBAAsB,CAACD,gBAAgB,GAAGA,gBAAgB;IAE1D,IAAIiB,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEL,GAAG,EAAE;MAC/C,OAAOrB,KAAK,CAACsB,aAAa,CAACb,sBAAsB,EAAE7B,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAED,KAAK,EAAE;QAC1Ef,YAAY,EAAEU;MAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAOhB,OAAO,CAACe,OAAO,GAAGpB,KAAK,CAACyB,UAAU,CAACA,UAAU,CAAC,GAAGhB,sBAAsB;EAChF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}