{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { isMemo } from 'react-is';\nimport useMemo from './hooks/useMemo';\nexport function fillRef(ref, node) {\n  if (typeof ref === 'function') {\n    ref(node);\n  } else if (_typeof(ref) === 'object' && ref && 'current' in ref) {\n    ref.current = node;\n  }\n}\n/**\n * Merge refs into one ref function to support ref passing.\n */\n\nexport function composeRef() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  var refList = refs.filter(function (ref) {\n    return ref;\n  });\n  if (refList.length <= 1) {\n    return refList[0];\n  }\n  return function (node) {\n    refs.forEach(function (ref) {\n      fillRef(ref, node);\n    });\n  };\n}\nexport function useComposeRef() {\n  for (var _len2 = arguments.length, refs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    refs[_key2] = arguments[_key2];\n  }\n  return useMemo(function () {\n    return composeRef.apply(void 0, refs);\n  }, refs, function (prev, next) {\n    return prev.length === next.length && prev.every(function (ref, i) {\n      return ref === next[i];\n    });\n  });\n}\nexport function supportRef(nodeOrComponent) {\n  var _type$prototype, _nodeOrComponent$prot;\n  var type = isMemo(nodeOrComponent) ? nodeOrComponent.type.type : nodeOrComponent.type; // Function component node\n\n  if (typeof type === 'function' && !((_type$prototype = type.prototype) === null || _type$prototype === void 0 ? void 0 : _type$prototype.render)) {\n    return false;\n  } // Class component\n\n  if (typeof nodeOrComponent === 'function' && !((_nodeOrComponent$prot = nodeOrComponent.prototype) === null || _nodeOrComponent$prot === void 0 ? void 0 : _nodeOrComponent$prot.render)) {\n    return false;\n  }\n  return true;\n}\n/* eslint-enable */", "map": {"version": 3, "names": ["_typeof", "isMemo", "useMemo", "fillRef", "ref", "node", "current", "composeRef", "_len", "arguments", "length", "refs", "Array", "_key", "refList", "filter", "for<PERSON>ach", "useComposeRef", "_len2", "_key2", "apply", "prev", "next", "every", "i", "supportRef", "nodeOrComponent", "_type$prototype", "_nodeOrComponent$prot", "type", "prototype", "render"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/ref.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { isMemo } from 'react-is';\nimport useMemo from './hooks/useMemo';\nexport function fillRef(ref, node) {\n  if (typeof ref === 'function') {\n    ref(node);\n  } else if (_typeof(ref) === 'object' && ref && 'current' in ref) {\n    ref.current = node;\n  }\n}\n/**\n * Merge refs into one ref function to support ref passing.\n */\n\nexport function composeRef() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  var refList = refs.filter(function (ref) {\n    return ref;\n  });\n\n  if (refList.length <= 1) {\n    return refList[0];\n  }\n\n  return function (node) {\n    refs.forEach(function (ref) {\n      fillRef(ref, node);\n    });\n  };\n}\nexport function useComposeRef() {\n  for (var _len2 = arguments.length, refs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    refs[_key2] = arguments[_key2];\n  }\n\n  return useMemo(function () {\n    return composeRef.apply(void 0, refs);\n  }, refs, function (prev, next) {\n    return prev.length === next.length && prev.every(function (ref, i) {\n      return ref === next[i];\n    });\n  });\n}\nexport function supportRef(nodeOrComponent) {\n  var _type$prototype, _nodeOrComponent$prot;\n\n  var type = isMemo(nodeOrComponent) ? nodeOrComponent.type.type : nodeOrComponent.type; // Function component node\n\n  if (typeof type === 'function' && !((_type$prototype = type.prototype) === null || _type$prototype === void 0 ? void 0 : _type$prototype.render)) {\n    return false;\n  } // Class component\n\n\n  if (typeof nodeOrComponent === 'function' && !((_nodeOrComponent$prot = nodeOrComponent.prototype) === null || _nodeOrComponent$prot === void 0 ? void 0 : _nodeOrComponent$prot.render)) {\n    return false;\n  }\n\n  return true;\n}\n/* eslint-enable */"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACjC,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;IAC7BA,GAAG,CAACC,IAAI,CAAC;EACX,CAAC,MAAM,IAAIL,OAAO,CAACI,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,IAAI,SAAS,IAAIA,GAAG,EAAE;IAC/DA,GAAG,CAACE,OAAO,GAAGD,IAAI;EACpB;AACF;AACA;AACA;AACA;;AAEA,OAAO,SAASE,UAAUA,CAAA,EAAG;EAC3B,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EAEA,IAAIC,OAAO,GAAGH,IAAI,CAACI,MAAM,CAAC,UAAUX,GAAG,EAAE;IACvC,OAAOA,GAAG;EACZ,CAAC,CAAC;EAEF,IAAIU,OAAO,CAACJ,MAAM,IAAI,CAAC,EAAE;IACvB,OAAOI,OAAO,CAAC,CAAC,CAAC;EACnB;EAEA,OAAO,UAAUT,IAAI,EAAE;IACrBM,IAAI,CAACK,OAAO,CAAC,UAAUZ,GAAG,EAAE;MAC1BD,OAAO,CAACC,GAAG,EAAEC,IAAI,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;AACH;AACA,OAAO,SAASY,aAAaA,CAAA,EAAG;EAC9B,KAAK,IAAIC,KAAK,GAAGT,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC7FR,IAAI,CAACQ,KAAK,CAAC,GAAGV,SAAS,CAACU,KAAK,CAAC;EAChC;EAEA,OAAOjB,OAAO,CAAC,YAAY;IACzB,OAAOK,UAAU,CAACa,KAAK,CAAC,KAAK,CAAC,EAAET,IAAI,CAAC;EACvC,CAAC,EAAEA,IAAI,EAAE,UAAUU,IAAI,EAAEC,IAAI,EAAE;IAC7B,OAAOD,IAAI,CAACX,MAAM,KAAKY,IAAI,CAACZ,MAAM,IAAIW,IAAI,CAACE,KAAK,CAAC,UAAUnB,GAAG,EAAEoB,CAAC,EAAE;MACjE,OAAOpB,GAAG,KAAKkB,IAAI,CAACE,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAE;EAC1C,IAAIC,eAAe,EAAEC,qBAAqB;EAE1C,IAAIC,IAAI,GAAG5B,MAAM,CAACyB,eAAe,CAAC,GAAGA,eAAe,CAACG,IAAI,CAACA,IAAI,GAAGH,eAAe,CAACG,IAAI,CAAC,CAAC;;EAEvF,IAAI,OAAOA,IAAI,KAAK,UAAU,IAAI,EAAE,CAACF,eAAe,GAAGE,IAAI,CAACC,SAAS,MAAM,IAAI,IAAIH,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACI,MAAM,CAAC,EAAE;IAChJ,OAAO,KAAK;EACd,CAAC,CAAC;;EAGF,IAAI,OAAOL,eAAe,KAAK,UAAU,IAAI,EAAE,CAACE,qBAAqB,GAAGF,eAAe,CAACI,SAAS,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,MAAM,CAAC,EAAE;IACxL,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}