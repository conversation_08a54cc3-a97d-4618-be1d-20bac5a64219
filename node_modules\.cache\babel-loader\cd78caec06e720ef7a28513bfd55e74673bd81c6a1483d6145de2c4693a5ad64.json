{"ast": null, "code": "import * as React from 'react';\nimport Tooltip from '../../tooltip';\nvar EllipsisTooltip = function EllipsisTooltip(_ref) {\n  var title = _ref.title,\n    enabledEllipsis = _ref.enabledEllipsis,\n    isEllipsis = _ref.isEllipsis,\n    children = _ref.children;\n  if (!title || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    title: title,\n    visible: isEllipsis ? undefined : false\n  }, children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "EllipsisTooltip", "_ref", "title", "enabledEllipsis", "isEllipsis", "children", "createElement", "visible", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Base/EllipsisTooltip.js"], "sourcesContent": ["import * as React from 'react';\nimport Tooltip from '../../tooltip';\n\nvar EllipsisTooltip = function EllipsisTooltip(_ref) {\n  var title = _ref.title,\n      enabledEllipsis = _ref.enabledEllipsis,\n      isEllipsis = _ref.isEllipsis,\n      children = _ref.children;\n\n  if (!title || !enabledEllipsis) {\n    return children;\n  }\n\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    title: title,\n    visible: isEllipsis ? undefined : false\n  }, children);\n};\n\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\n\nexport default EllipsisTooltip;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,eAAe;AAEnC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EACnD,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,eAAe,GAAGF,IAAI,CAACE,eAAe;IACtCC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAE5B,IAAI,CAACH,KAAK,IAAI,CAACC,eAAe,EAAE;IAC9B,OAAOE,QAAQ;EACjB;EAEA,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAACP,OAAO,EAAE;IAC/CG,KAAK,EAAEA,KAAK;IACZK,OAAO,EAAEH,UAAU,GAAGI,SAAS,GAAG;EACpC,CAAC,EAAEH,QAAQ,CAAC;AACd,CAAC;AAED,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCX,eAAe,CAACY,WAAW,GAAG,iBAAiB;AACjD;AAEA,eAAeZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}