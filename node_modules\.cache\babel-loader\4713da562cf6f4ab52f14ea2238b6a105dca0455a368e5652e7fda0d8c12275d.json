{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"eventKey\", \"className\", \"style\", \"dragOver\", \"dragOverGapTop\", \"dragOverGapBottom\", \"isLeaf\", \"isStart\", \"isEnd\", \"expanded\", \"selected\", \"checked\", \"halfChecked\", \"loading\", \"domRef\", \"active\", \"data\", \"onMouseMove\", \"selectable\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\"; // @ts-ignore\n\nimport { TreeContext } from './contextTypes';\nimport Indent from './Indent';\nimport { convertNodePropsToEventData } from './utils/treeUtil';\nvar ICON_OPEN = 'open';\nvar ICON_CLOSE = 'close';\nvar defaultTitle = '---';\nvar InternalTreeNode = /*#__PURE__*/function (_React$Component) {\n  _inherits(InternalTreeNode, _React$Component);\n  var _super = _createSuper(InternalTreeNode);\n  function InternalTreeNode() {\n    var _this;\n    _classCallCheck(this, InternalTreeNode);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      dragNodeHighlight: false\n    };\n    _this.selectHandle = void 0;\n    _this.onSelectorClick = function (e) {\n      // Click trigger before select/check operation\n      var onNodeClick = _this.props.context.onNodeClick;\n      onNodeClick(e, convertNodePropsToEventData(_this.props));\n      if (_this.isSelectable()) {\n        _this.onSelect(e);\n      } else {\n        _this.onCheck(e);\n      }\n    };\n    _this.onSelectorDoubleClick = function (e) {\n      var onNodeDoubleClick = _this.props.context.onNodeDoubleClick;\n      onNodeDoubleClick(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onSelect = function (e) {\n      if (_this.isDisabled()) return;\n      var onNodeSelect = _this.props.context.onNodeSelect;\n      e.preventDefault();\n      onNodeSelect(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onCheck = function (e) {\n      if (_this.isDisabled()) return;\n      var _this$props = _this.props,\n        disableCheckbox = _this$props.disableCheckbox,\n        checked = _this$props.checked;\n      var onNodeCheck = _this.props.context.onNodeCheck;\n      if (!_this.isCheckable() || disableCheckbox) return;\n      e.preventDefault();\n      var targetChecked = !checked;\n      onNodeCheck(e, convertNodePropsToEventData(_this.props), targetChecked);\n    };\n    _this.onMouseEnter = function (e) {\n      var onNodeMouseEnter = _this.props.context.onNodeMouseEnter;\n      onNodeMouseEnter(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onMouseLeave = function (e) {\n      var onNodeMouseLeave = _this.props.context.onNodeMouseLeave;\n      onNodeMouseLeave(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onContextMenu = function (e) {\n      var onNodeContextMenu = _this.props.context.onNodeContextMenu;\n      onNodeContextMenu(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onDragStart = function (e) {\n      var onNodeDragStart = _this.props.context.onNodeDragStart;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: true\n      });\n      onNodeDragStart(e, _assertThisInitialized(_this));\n      try {\n        // ie throw error\n        // firefox-need-it\n        e.dataTransfer.setData('text/plain', '');\n      } catch (error) {// empty\n      }\n    };\n    _this.onDragEnter = function (e) {\n      var onNodeDragEnter = _this.props.context.onNodeDragEnter;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragEnter(e, _assertThisInitialized(_this));\n    };\n    _this.onDragOver = function (e) {\n      var onNodeDragOver = _this.props.context.onNodeDragOver;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragOver(e, _assertThisInitialized(_this));\n    };\n    _this.onDragLeave = function (e) {\n      var onNodeDragLeave = _this.props.context.onNodeDragLeave;\n      e.stopPropagation();\n      onNodeDragLeave(e, _assertThisInitialized(_this));\n    };\n    _this.onDragEnd = function (e) {\n      var onNodeDragEnd = _this.props.context.onNodeDragEnd;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDragEnd(e, _assertThisInitialized(_this));\n    };\n    _this.onDrop = function (e) {\n      var onNodeDrop = _this.props.context.onNodeDrop;\n      e.preventDefault();\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDrop(e, _assertThisInitialized(_this));\n    };\n    _this.onExpand = function (e) {\n      var _this$props2 = _this.props,\n        loading = _this$props2.loading,\n        onNodeExpand = _this$props2.context.onNodeExpand;\n      if (loading) return;\n      onNodeExpand(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.setSelectHandle = function (node) {\n      _this.selectHandle = node;\n    };\n    _this.getNodeState = function () {\n      var expanded = _this.props.expanded;\n      if (_this.isLeaf()) {\n        return null;\n      }\n      return expanded ? ICON_OPEN : ICON_CLOSE;\n    };\n    _this.hasChildren = function () {\n      var eventKey = _this.props.eventKey;\n      var keyEntities = _this.props.context.keyEntities;\n      var _ref = keyEntities[eventKey] || {},\n        children = _ref.children;\n      return !!(children || []).length;\n    };\n    _this.isLeaf = function () {\n      var _this$props3 = _this.props,\n        isLeaf = _this$props3.isLeaf,\n        loaded = _this$props3.loaded;\n      var loadData = _this.props.context.loadData;\n      var hasChildren = _this.hasChildren();\n      if (isLeaf === false) {\n        return false;\n      }\n      return isLeaf || !loadData && !hasChildren || loadData && loaded && !hasChildren;\n    };\n    _this.isDisabled = function () {\n      var disabled = _this.props.disabled;\n      var treeDisabled = _this.props.context.disabled;\n      return !!(treeDisabled || disabled);\n    };\n    _this.isCheckable = function () {\n      var checkable = _this.props.checkable;\n      var treeCheckable = _this.props.context.checkable; // Return false if tree or treeNode is not checkable\n\n      if (!treeCheckable || checkable === false) return false;\n      return treeCheckable;\n    };\n    _this.syncLoadData = function (props) {\n      var expanded = props.expanded,\n        loading = props.loading,\n        loaded = props.loaded;\n      var _this$props$context = _this.props.context,\n        loadData = _this$props$context.loadData,\n        onNodeLoad = _this$props$context.onNodeLoad;\n      if (loading) {\n        return;\n      } // read from state to avoid loadData at same time\n\n      if (loadData && expanded && !_this.isLeaf()) {\n        // We needn't reload data when has children in sync logic\n        // It's only needed in node expanded\n        if (!_this.hasChildren() && !loaded) {\n          onNodeLoad(convertNodePropsToEventData(_this.props));\n        }\n      }\n    };\n    _this.isDraggable = function () {\n      var _this$props4 = _this.props,\n        data = _this$props4.data,\n        draggable = _this$props4.context.draggable;\n      return !!(draggable && (!draggable.nodeDraggable || draggable.nodeDraggable(data)));\n    };\n    _this.renderDragHandler = function () {\n      var _this$props$context2 = _this.props.context,\n        draggable = _this$props$context2.draggable,\n        prefixCls = _this$props$context2.prefixCls;\n      return (draggable === null || draggable === void 0 ? void 0 : draggable.icon) ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-draggable-icon\")\n      }, draggable.icon) : null;\n    };\n    _this.renderSwitcherIconDom = function (isLeaf) {\n      var switcherIconFromProps = _this.props.switcherIcon;\n      var switcherIconFromCtx = _this.props.context.switcherIcon;\n      var switcherIcon = switcherIconFromProps || switcherIconFromCtx; // if switcherIconDom is null, no render switcher span\n\n      if (typeof switcherIcon === 'function') {\n        return switcherIcon(_objectSpread(_objectSpread({}, _this.props), {}, {\n          isLeaf: isLeaf\n        }));\n      }\n      return switcherIcon;\n    };\n    _this.renderSwitcher = function () {\n      var expanded = _this.props.expanded;\n      var prefixCls = _this.props.context.prefixCls;\n      if (_this.isLeaf()) {\n        // if switcherIconDom is null, no render switcher span\n        var _switcherIconDom = _this.renderSwitcherIconDom(true);\n        return _switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher-noop\"))\n        }, _switcherIconDom) : null;\n      }\n      var switcherCls = classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher_\").concat(expanded ? ICON_OPEN : ICON_CLOSE));\n      var switcherIconDom = _this.renderSwitcherIconDom(false);\n      return switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n        onClick: _this.onExpand,\n        className: switcherCls\n      }, switcherIconDom) : null;\n    };\n    _this.renderCheckbox = function () {\n      var _this$props5 = _this.props,\n        checked = _this$props5.checked,\n        halfChecked = _this$props5.halfChecked,\n        disableCheckbox = _this$props5.disableCheckbox;\n      var prefixCls = _this.props.context.prefixCls;\n      var disabled = _this.isDisabled();\n      var checkable = _this.isCheckable();\n      if (!checkable) return null; // [Legacy] Custom element should be separate with `checkable` in future\n\n      var $custom = typeof checkable !== 'boolean' ? checkable : null;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-checkbox\"), checked && \"\".concat(prefixCls, \"-checkbox-checked\"), !checked && halfChecked && \"\".concat(prefixCls, \"-checkbox-indeterminate\"), (disabled || disableCheckbox) && \"\".concat(prefixCls, \"-checkbox-disabled\")),\n        onClick: _this.onCheck\n      }, $custom);\n    };\n    _this.renderIcon = function () {\n      var loading = _this.props.loading;\n      var prefixCls = _this.props.context.prefixCls;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__\").concat(_this.getNodeState() || 'docu'), loading && \"\".concat(prefixCls, \"-icon_loading\"))\n      });\n    };\n    _this.renderSelector = function () {\n      var dragNodeHighlight = _this.state.dragNodeHighlight;\n      var _this$props6 = _this.props,\n        title = _this$props6.title,\n        selected = _this$props6.selected,\n        icon = _this$props6.icon,\n        loading = _this$props6.loading,\n        data = _this$props6.data;\n      var _this$props$context3 = _this.props.context,\n        prefixCls = _this$props$context3.prefixCls,\n        showIcon = _this$props$context3.showIcon,\n        treeIcon = _this$props$context3.icon,\n        loadData = _this$props$context3.loadData,\n        titleRender = _this$props$context3.titleRender;\n      var disabled = _this.isDisabled();\n      var wrapClass = \"\".concat(prefixCls, \"-node-content-wrapper\"); // Icon - Still show loading icon when loading without showIcon\n\n      var $icon;\n      if (showIcon) {\n        var currentIcon = icon || treeIcon;\n        $icon = currentIcon ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__customize\"))\n        }, typeof currentIcon === 'function' ? currentIcon(_this.props) : currentIcon) : _this.renderIcon();\n      } else if (loadData && loading) {\n        $icon = _this.renderIcon();\n      } // Title\n\n      var titleNode;\n      if (typeof title === 'function') {\n        titleNode = title(data);\n      } else if (titleRender) {\n        titleNode = titleRender(data);\n      } else {\n        titleNode = title;\n      }\n      var $title = /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title\")\n      }, titleNode);\n      return /*#__PURE__*/React.createElement(\"span\", {\n        ref: _this.setSelectHandle,\n        title: typeof title === 'string' ? title : '',\n        className: classNames(\"\".concat(wrapClass), \"\".concat(wrapClass, \"-\").concat(_this.getNodeState() || 'normal'), !disabled && (selected || dragNodeHighlight) && \"\".concat(prefixCls, \"-node-selected\")),\n        onMouseEnter: _this.onMouseEnter,\n        onMouseLeave: _this.onMouseLeave,\n        onContextMenu: _this.onContextMenu,\n        onClick: _this.onSelectorClick,\n        onDoubleClick: _this.onSelectorDoubleClick\n      }, $icon, $title, _this.renderDropIndicator());\n    };\n    _this.renderDropIndicator = function () {\n      var _this$props7 = _this.props,\n        disabled = _this$props7.disabled,\n        eventKey = _this$props7.eventKey;\n      var _this$props$context4 = _this.props.context,\n        draggable = _this$props$context4.draggable,\n        dropLevelOffset = _this$props$context4.dropLevelOffset,\n        dropPosition = _this$props$context4.dropPosition,\n        prefixCls = _this$props$context4.prefixCls,\n        indent = _this$props$context4.indent,\n        dropIndicatorRender = _this$props$context4.dropIndicatorRender,\n        dragOverNodeKey = _this$props$context4.dragOverNodeKey,\n        direction = _this$props$context4.direction;\n      var rootDraggable = draggable !== false; // allowDrop is calculated in Tree.tsx, there is no need for calc it here\n\n      var showIndicator = !disabled && rootDraggable && dragOverNodeKey === eventKey;\n      return showIndicator ? dropIndicatorRender({\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        indent: indent,\n        prefixCls: prefixCls,\n        direction: direction\n      }) : null;\n    };\n    return _this;\n  }\n  _createClass(InternalTreeNode, [{\n    key: \"componentDidMount\",\n    value:\n    // Isomorphic needn't load data in server side\n    function componentDidMount() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"isSelectable\",\n    value: function isSelectable() {\n      var selectable = this.props.selectable;\n      var treeSelectable = this.props.context.selectable; // Ignore when selectable is undefined or null\n\n      if (typeof selectable === 'boolean') {\n        return selectable;\n      }\n      return treeSelectable;\n    }\n  }, {\n    key: \"render\",\n    value:\n    // =========================== Render ===========================\n    function render() {\n      var _classNames;\n      var _this$props8 = this.props,\n        eventKey = _this$props8.eventKey,\n        className = _this$props8.className,\n        style = _this$props8.style,\n        dragOver = _this$props8.dragOver,\n        dragOverGapTop = _this$props8.dragOverGapTop,\n        dragOverGapBottom = _this$props8.dragOverGapBottom,\n        isLeaf = _this$props8.isLeaf,\n        isStart = _this$props8.isStart,\n        isEnd = _this$props8.isEnd,\n        expanded = _this$props8.expanded,\n        selected = _this$props8.selected,\n        checked = _this$props8.checked,\n        halfChecked = _this$props8.halfChecked,\n        loading = _this$props8.loading,\n        domRef = _this$props8.domRef,\n        active = _this$props8.active,\n        data = _this$props8.data,\n        onMouseMove = _this$props8.onMouseMove,\n        selectable = _this$props8.selectable,\n        otherProps = _objectWithoutProperties(_this$props8, _excluded);\n      var _this$props$context5 = this.props.context,\n        prefixCls = _this$props$context5.prefixCls,\n        filterTreeNode = _this$props$context5.filterTreeNode,\n        keyEntities = _this$props$context5.keyEntities,\n        dropContainerKey = _this$props$context5.dropContainerKey,\n        dropTargetKey = _this$props$context5.dropTargetKey,\n        draggingNodeKey = _this$props$context5.draggingNodeKey;\n      var disabled = this.isDisabled();\n      var dataOrAriaAttributeProps = pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      });\n      var _ref2 = keyEntities[eventKey] || {},\n        level = _ref2.level;\n      var isEndNode = isEnd[isEnd.length - 1];\n      var mergedDraggable = this.isDraggable();\n      var draggableWithoutDisabled = !disabled && mergedDraggable;\n      var dragging = draggingNodeKey === eventKey;\n      var ariaSelected = selectable !== undefined ? {\n        'aria-selected': !!selectable\n      } : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: domRef,\n        className: classNames(className, \"\".concat(prefixCls, \"-treenode\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-switcher-\").concat(expanded ? 'open' : 'close'), !isLeaf), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-indeterminate\"), halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-selected\"), selected), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-loading\"), loading), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-leaf-last\"), isEndNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-draggable\"), draggableWithoutDisabled), _defineProperty(_classNames, \"dragging\", dragging), _defineProperty(_classNames, 'drop-target', dropTargetKey === eventKey), _defineProperty(_classNames, 'drop-container', dropContainerKey === eventKey), _defineProperty(_classNames, 'drag-over', !disabled && dragOver), _defineProperty(_classNames, 'drag-over-gap-top', !disabled && dragOverGapTop), _defineProperty(_classNames, 'drag-over-gap-bottom', !disabled && dragOverGapBottom), _defineProperty(_classNames, 'filter-node', filterTreeNode && filterTreeNode(convertNodePropsToEventData(this.props))), _classNames)),\n        style: style // Draggable config\n        ,\n\n        draggable: draggableWithoutDisabled,\n        \"aria-grabbed\": dragging,\n        onDragStart: draggableWithoutDisabled ? this.onDragStart : undefined // Drop config\n        ,\n\n        onDragEnter: mergedDraggable ? this.onDragEnter : undefined,\n        onDragOver: mergedDraggable ? this.onDragOver : undefined,\n        onDragLeave: mergedDraggable ? this.onDragLeave : undefined,\n        onDrop: mergedDraggable ? this.onDrop : undefined,\n        onDragEnd: mergedDraggable ? this.onDragEnd : undefined,\n        onMouseMove: onMouseMove\n      }, ariaSelected, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(Indent, {\n        prefixCls: prefixCls,\n        level: level,\n        isStart: isStart,\n        isEnd: isEnd\n      }), this.renderDragHandler(), this.renderSwitcher(), this.renderCheckbox(), this.renderSelector());\n    }\n  }]);\n  return InternalTreeNode;\n}(React.Component);\nvar ContextTreeNode = function ContextTreeNode(props) {\n  return /*#__PURE__*/React.createElement(TreeContext.Consumer, null, function (context) {\n    return /*#__PURE__*/React.createElement(InternalTreeNode, _extends({}, props, {\n      context: context\n    }));\n  });\n};\nContextTreeNode.displayName = 'TreeNode';\nContextTreeNode.defaultProps = {\n  title: defaultTitle\n};\nContextTreeNode.isTreeNode = 1;\nexport { InternalTreeNode };\nexport default ContextTreeNode;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_excluded", "React", "classNames", "pickAttrs", "TreeContext", "Indent", "convertNodePropsToEventData", "ICON_OPEN", "ICON_CLOSE", "defaultTitle", "InternalTreeNode", "_React$Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "state", "dragNodeHighlight", "selectHandle", "onSelectorClick", "e", "onNodeClick", "props", "context", "isSelectable", "onSelect", "onCheck", "onSelectorDoubleClick", "onNodeDoubleClick", "isDisabled", "onNodeSelect", "preventDefault", "_this$props", "disableCheckbox", "checked", "onNodeCheck", "isCheckable", "targetChecked", "onMouseEnter", "onNodeMouseEnter", "onMouseLeave", "onNodeMouseLeave", "onContextMenu", "onNodeContextMenu", "onDragStart", "onNodeDragStart", "stopPropagation", "setState", "dataTransfer", "setData", "error", "onDragEnter", "onNodeDragEnter", "onDragOver", "onNodeDragOver", "onDragLeave", "onNodeDragLeave", "onDragEnd", "onNodeDragEnd", "onDrop", "onNodeDrop", "onExpand", "_this$props2", "loading", "onNodeExpand", "setSelectHandle", "node", "getNodeState", "expanded", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventKey", "keyEntities", "_ref", "children", "_this$props3", "loaded", "loadData", "disabled", "treeDisabled", "checkable", "treeCheckable", "syncLoadData", "_this$props$context", "onNodeLoad", "isDraggable", "_this$props4", "data", "draggable", "nodeDraggable", "renderDragHandler", "_this$props$context2", "prefixCls", "icon", "createElement", "className", "renderSwitcherIconDom", "switcherIconFromProps", "switcherIcon", "switcherIconFromCtx", "renderSwitcher", "_switcherIconDom", "switcherCls", "switcherIconDom", "onClick", "renderCheckbox", "_this$props5", "halfChecked", "$custom", "renderIcon", "renderSelector", "_this$props6", "title", "selected", "_this$props$context3", "showIcon", "treeIcon", "titleRender", "wrapClass", "$icon", "currentIcon", "titleNode", "$title", "ref", "onDoubleClick", "renderDropIndicator", "_this$props7", "_this$props$context4", "dropLevelOffset", "dropPosition", "indent", "dropIndicatorRender", "dragOverNodeKey", "direction", "rootDraggable", "showIndicator", "key", "value", "componentDidMount", "componentDidUpdate", "selectable", "treeSelectable", "render", "_classNames", "_this$props8", "style", "dragOver", "dragOverGapTop", "dragOverGapBottom", "isStart", "isEnd", "domRef", "active", "onMouseMove", "otherProps", "_this$props$context5", "filterTreeNode", "dropContainerKey", "dropTargetKey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataOrAriaAttributeProps", "aria", "_ref2", "level", "isEndNode", "mergedDraggable", "draggableWithoutDisabled", "dragging", "ariaSelected", "undefined", "Component", "ContextTreeNode", "Consumer", "displayName", "defaultProps", "isTreeNode"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree/es/TreeNode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"eventKey\", \"className\", \"style\", \"dragOver\", \"dragOverGapTop\", \"dragOverGapBottom\", \"isLeaf\", \"isStart\", \"isEnd\", \"expanded\", \"selected\", \"checked\", \"halfChecked\", \"loading\", \"domRef\", \"active\", \"data\", \"onMouseMove\", \"selectable\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\"; // @ts-ignore\n\nimport { TreeContext } from './contextTypes';\nimport Indent from './Indent';\nimport { convertNodePropsToEventData } from './utils/treeUtil';\nvar ICON_OPEN = 'open';\nvar ICON_CLOSE = 'close';\nvar defaultTitle = '---';\n\nvar InternalTreeNode = /*#__PURE__*/function (_React$Component) {\n  _inherits(InternalTreeNode, _React$Component);\n\n  var _super = _createSuper(InternalTreeNode);\n\n  function InternalTreeNode() {\n    var _this;\n\n    _classCallCheck(this, InternalTreeNode);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      dragNodeHighlight: false\n    };\n    _this.selectHandle = void 0;\n\n    _this.onSelectorClick = function (e) {\n      // Click trigger before select/check operation\n      var onNodeClick = _this.props.context.onNodeClick;\n      onNodeClick(e, convertNodePropsToEventData(_this.props));\n\n      if (_this.isSelectable()) {\n        _this.onSelect(e);\n      } else {\n        _this.onCheck(e);\n      }\n    };\n\n    _this.onSelectorDoubleClick = function (e) {\n      var onNodeDoubleClick = _this.props.context.onNodeDoubleClick;\n      onNodeDoubleClick(e, convertNodePropsToEventData(_this.props));\n    };\n\n    _this.onSelect = function (e) {\n      if (_this.isDisabled()) return;\n      var onNodeSelect = _this.props.context.onNodeSelect;\n      e.preventDefault();\n      onNodeSelect(e, convertNodePropsToEventData(_this.props));\n    };\n\n    _this.onCheck = function (e) {\n      if (_this.isDisabled()) return;\n      var _this$props = _this.props,\n          disableCheckbox = _this$props.disableCheckbox,\n          checked = _this$props.checked;\n      var onNodeCheck = _this.props.context.onNodeCheck;\n      if (!_this.isCheckable() || disableCheckbox) return;\n      e.preventDefault();\n      var targetChecked = !checked;\n      onNodeCheck(e, convertNodePropsToEventData(_this.props), targetChecked);\n    };\n\n    _this.onMouseEnter = function (e) {\n      var onNodeMouseEnter = _this.props.context.onNodeMouseEnter;\n      onNodeMouseEnter(e, convertNodePropsToEventData(_this.props));\n    };\n\n    _this.onMouseLeave = function (e) {\n      var onNodeMouseLeave = _this.props.context.onNodeMouseLeave;\n      onNodeMouseLeave(e, convertNodePropsToEventData(_this.props));\n    };\n\n    _this.onContextMenu = function (e) {\n      var onNodeContextMenu = _this.props.context.onNodeContextMenu;\n      onNodeContextMenu(e, convertNodePropsToEventData(_this.props));\n    };\n\n    _this.onDragStart = function (e) {\n      var onNodeDragStart = _this.props.context.onNodeDragStart;\n      e.stopPropagation();\n\n      _this.setState({\n        dragNodeHighlight: true\n      });\n\n      onNodeDragStart(e, _assertThisInitialized(_this));\n\n      try {\n        // ie throw error\n        // firefox-need-it\n        e.dataTransfer.setData('text/plain', '');\n      } catch (error) {// empty\n      }\n    };\n\n    _this.onDragEnter = function (e) {\n      var onNodeDragEnter = _this.props.context.onNodeDragEnter;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragEnter(e, _assertThisInitialized(_this));\n    };\n\n    _this.onDragOver = function (e) {\n      var onNodeDragOver = _this.props.context.onNodeDragOver;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragOver(e, _assertThisInitialized(_this));\n    };\n\n    _this.onDragLeave = function (e) {\n      var onNodeDragLeave = _this.props.context.onNodeDragLeave;\n      e.stopPropagation();\n      onNodeDragLeave(e, _assertThisInitialized(_this));\n    };\n\n    _this.onDragEnd = function (e) {\n      var onNodeDragEnd = _this.props.context.onNodeDragEnd;\n      e.stopPropagation();\n\n      _this.setState({\n        dragNodeHighlight: false\n      });\n\n      onNodeDragEnd(e, _assertThisInitialized(_this));\n    };\n\n    _this.onDrop = function (e) {\n      var onNodeDrop = _this.props.context.onNodeDrop;\n      e.preventDefault();\n      e.stopPropagation();\n\n      _this.setState({\n        dragNodeHighlight: false\n      });\n\n      onNodeDrop(e, _assertThisInitialized(_this));\n    };\n\n    _this.onExpand = function (e) {\n      var _this$props2 = _this.props,\n          loading = _this$props2.loading,\n          onNodeExpand = _this$props2.context.onNodeExpand;\n      if (loading) return;\n      onNodeExpand(e, convertNodePropsToEventData(_this.props));\n    };\n\n    _this.setSelectHandle = function (node) {\n      _this.selectHandle = node;\n    };\n\n    _this.getNodeState = function () {\n      var expanded = _this.props.expanded;\n\n      if (_this.isLeaf()) {\n        return null;\n      }\n\n      return expanded ? ICON_OPEN : ICON_CLOSE;\n    };\n\n    _this.hasChildren = function () {\n      var eventKey = _this.props.eventKey;\n      var keyEntities = _this.props.context.keyEntities;\n\n      var _ref = keyEntities[eventKey] || {},\n          children = _ref.children;\n\n      return !!(children || []).length;\n    };\n\n    _this.isLeaf = function () {\n      var _this$props3 = _this.props,\n          isLeaf = _this$props3.isLeaf,\n          loaded = _this$props3.loaded;\n      var loadData = _this.props.context.loadData;\n\n      var hasChildren = _this.hasChildren();\n\n      if (isLeaf === false) {\n        return false;\n      }\n\n      return isLeaf || !loadData && !hasChildren || loadData && loaded && !hasChildren;\n    };\n\n    _this.isDisabled = function () {\n      var disabled = _this.props.disabled;\n      var treeDisabled = _this.props.context.disabled;\n      return !!(treeDisabled || disabled);\n    };\n\n    _this.isCheckable = function () {\n      var checkable = _this.props.checkable;\n      var treeCheckable = _this.props.context.checkable; // Return false if tree or treeNode is not checkable\n\n      if (!treeCheckable || checkable === false) return false;\n      return treeCheckable;\n    };\n\n    _this.syncLoadData = function (props) {\n      var expanded = props.expanded,\n          loading = props.loading,\n          loaded = props.loaded;\n      var _this$props$context = _this.props.context,\n          loadData = _this$props$context.loadData,\n          onNodeLoad = _this$props$context.onNodeLoad;\n\n      if (loading) {\n        return;\n      } // read from state to avoid loadData at same time\n\n\n      if (loadData && expanded && !_this.isLeaf()) {\n        // We needn't reload data when has children in sync logic\n        // It's only needed in node expanded\n        if (!_this.hasChildren() && !loaded) {\n          onNodeLoad(convertNodePropsToEventData(_this.props));\n        }\n      }\n    };\n\n    _this.isDraggable = function () {\n      var _this$props4 = _this.props,\n          data = _this$props4.data,\n          draggable = _this$props4.context.draggable;\n      return !!(draggable && (!draggable.nodeDraggable || draggable.nodeDraggable(data)));\n    };\n\n    _this.renderDragHandler = function () {\n      var _this$props$context2 = _this.props.context,\n          draggable = _this$props$context2.draggable,\n          prefixCls = _this$props$context2.prefixCls;\n      return (draggable === null || draggable === void 0 ? void 0 : draggable.icon) ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-draggable-icon\")\n      }, draggable.icon) : null;\n    };\n\n    _this.renderSwitcherIconDom = function (isLeaf) {\n      var switcherIconFromProps = _this.props.switcherIcon;\n      var switcherIconFromCtx = _this.props.context.switcherIcon;\n      var switcherIcon = switcherIconFromProps || switcherIconFromCtx; // if switcherIconDom is null, no render switcher span\n\n      if (typeof switcherIcon === 'function') {\n        return switcherIcon(_objectSpread(_objectSpread({}, _this.props), {}, {\n          isLeaf: isLeaf\n        }));\n      }\n\n      return switcherIcon;\n    };\n\n    _this.renderSwitcher = function () {\n      var expanded = _this.props.expanded;\n      var prefixCls = _this.props.context.prefixCls;\n\n      if (_this.isLeaf()) {\n        // if switcherIconDom is null, no render switcher span\n        var _switcherIconDom = _this.renderSwitcherIconDom(true);\n\n        return _switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher-noop\"))\n        }, _switcherIconDom) : null;\n      }\n\n      var switcherCls = classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher_\").concat(expanded ? ICON_OPEN : ICON_CLOSE));\n\n      var switcherIconDom = _this.renderSwitcherIconDom(false);\n\n      return switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n        onClick: _this.onExpand,\n        className: switcherCls\n      }, switcherIconDom) : null;\n    };\n\n    _this.renderCheckbox = function () {\n      var _this$props5 = _this.props,\n          checked = _this$props5.checked,\n          halfChecked = _this$props5.halfChecked,\n          disableCheckbox = _this$props5.disableCheckbox;\n      var prefixCls = _this.props.context.prefixCls;\n\n      var disabled = _this.isDisabled();\n\n      var checkable = _this.isCheckable();\n\n      if (!checkable) return null; // [Legacy] Custom element should be separate with `checkable` in future\n\n      var $custom = typeof checkable !== 'boolean' ? checkable : null;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-checkbox\"), checked && \"\".concat(prefixCls, \"-checkbox-checked\"), !checked && halfChecked && \"\".concat(prefixCls, \"-checkbox-indeterminate\"), (disabled || disableCheckbox) && \"\".concat(prefixCls, \"-checkbox-disabled\")),\n        onClick: _this.onCheck\n      }, $custom);\n    };\n\n    _this.renderIcon = function () {\n      var loading = _this.props.loading;\n      var prefixCls = _this.props.context.prefixCls;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__\").concat(_this.getNodeState() || 'docu'), loading && \"\".concat(prefixCls, \"-icon_loading\"))\n      });\n    };\n\n    _this.renderSelector = function () {\n      var dragNodeHighlight = _this.state.dragNodeHighlight;\n      var _this$props6 = _this.props,\n          title = _this$props6.title,\n          selected = _this$props6.selected,\n          icon = _this$props6.icon,\n          loading = _this$props6.loading,\n          data = _this$props6.data;\n      var _this$props$context3 = _this.props.context,\n          prefixCls = _this$props$context3.prefixCls,\n          showIcon = _this$props$context3.showIcon,\n          treeIcon = _this$props$context3.icon,\n          loadData = _this$props$context3.loadData,\n          titleRender = _this$props$context3.titleRender;\n\n      var disabled = _this.isDisabled();\n\n      var wrapClass = \"\".concat(prefixCls, \"-node-content-wrapper\"); // Icon - Still show loading icon when loading without showIcon\n\n      var $icon;\n\n      if (showIcon) {\n        var currentIcon = icon || treeIcon;\n        $icon = currentIcon ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__customize\"))\n        }, typeof currentIcon === 'function' ? currentIcon(_this.props) : currentIcon) : _this.renderIcon();\n      } else if (loadData && loading) {\n        $icon = _this.renderIcon();\n      } // Title\n\n\n      var titleNode;\n\n      if (typeof title === 'function') {\n        titleNode = title(data);\n      } else if (titleRender) {\n        titleNode = titleRender(data);\n      } else {\n        titleNode = title;\n      }\n\n      var $title = /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title\")\n      }, titleNode);\n      return /*#__PURE__*/React.createElement(\"span\", {\n        ref: _this.setSelectHandle,\n        title: typeof title === 'string' ? title : '',\n        className: classNames(\"\".concat(wrapClass), \"\".concat(wrapClass, \"-\").concat(_this.getNodeState() || 'normal'), !disabled && (selected || dragNodeHighlight) && \"\".concat(prefixCls, \"-node-selected\")),\n        onMouseEnter: _this.onMouseEnter,\n        onMouseLeave: _this.onMouseLeave,\n        onContextMenu: _this.onContextMenu,\n        onClick: _this.onSelectorClick,\n        onDoubleClick: _this.onSelectorDoubleClick\n      }, $icon, $title, _this.renderDropIndicator());\n    };\n\n    _this.renderDropIndicator = function () {\n      var _this$props7 = _this.props,\n          disabled = _this$props7.disabled,\n          eventKey = _this$props7.eventKey;\n      var _this$props$context4 = _this.props.context,\n          draggable = _this$props$context4.draggable,\n          dropLevelOffset = _this$props$context4.dropLevelOffset,\n          dropPosition = _this$props$context4.dropPosition,\n          prefixCls = _this$props$context4.prefixCls,\n          indent = _this$props$context4.indent,\n          dropIndicatorRender = _this$props$context4.dropIndicatorRender,\n          dragOverNodeKey = _this$props$context4.dragOverNodeKey,\n          direction = _this$props$context4.direction;\n      var rootDraggable = draggable !== false; // allowDrop is calculated in Tree.tsx, there is no need for calc it here\n\n      var showIndicator = !disabled && rootDraggable && dragOverNodeKey === eventKey;\n      return showIndicator ? dropIndicatorRender({\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        indent: indent,\n        prefixCls: prefixCls,\n        direction: direction\n      }) : null;\n    };\n\n    return _this;\n  }\n\n  _createClass(InternalTreeNode, [{\n    key: \"componentDidMount\",\n    value: // Isomorphic needn't load data in server side\n    function componentDidMount() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"isSelectable\",\n    value: function isSelectable() {\n      var selectable = this.props.selectable;\n      var treeSelectable = this.props.context.selectable; // Ignore when selectable is undefined or null\n\n      if (typeof selectable === 'boolean') {\n        return selectable;\n      }\n\n      return treeSelectable;\n    }\n  }, {\n    key: \"render\",\n    value: // =========================== Render ===========================\n    function render() {\n      var _classNames;\n\n      var _this$props8 = this.props,\n          eventKey = _this$props8.eventKey,\n          className = _this$props8.className,\n          style = _this$props8.style,\n          dragOver = _this$props8.dragOver,\n          dragOverGapTop = _this$props8.dragOverGapTop,\n          dragOverGapBottom = _this$props8.dragOverGapBottom,\n          isLeaf = _this$props8.isLeaf,\n          isStart = _this$props8.isStart,\n          isEnd = _this$props8.isEnd,\n          expanded = _this$props8.expanded,\n          selected = _this$props8.selected,\n          checked = _this$props8.checked,\n          halfChecked = _this$props8.halfChecked,\n          loading = _this$props8.loading,\n          domRef = _this$props8.domRef,\n          active = _this$props8.active,\n          data = _this$props8.data,\n          onMouseMove = _this$props8.onMouseMove,\n          selectable = _this$props8.selectable,\n          otherProps = _objectWithoutProperties(_this$props8, _excluded);\n\n      var _this$props$context5 = this.props.context,\n          prefixCls = _this$props$context5.prefixCls,\n          filterTreeNode = _this$props$context5.filterTreeNode,\n          keyEntities = _this$props$context5.keyEntities,\n          dropContainerKey = _this$props$context5.dropContainerKey,\n          dropTargetKey = _this$props$context5.dropTargetKey,\n          draggingNodeKey = _this$props$context5.draggingNodeKey;\n      var disabled = this.isDisabled();\n      var dataOrAriaAttributeProps = pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      });\n\n      var _ref2 = keyEntities[eventKey] || {},\n          level = _ref2.level;\n\n      var isEndNode = isEnd[isEnd.length - 1];\n      var mergedDraggable = this.isDraggable();\n      var draggableWithoutDisabled = !disabled && mergedDraggable;\n      var dragging = draggingNodeKey === eventKey;\n      var ariaSelected = selectable !== undefined ? {\n        'aria-selected': !!selectable\n      } : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: domRef,\n        className: classNames(className, \"\".concat(prefixCls, \"-treenode\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-switcher-\").concat(expanded ? 'open' : 'close'), !isLeaf), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-indeterminate\"), halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-selected\"), selected), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-loading\"), loading), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-leaf-last\"), isEndNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-draggable\"), draggableWithoutDisabled), _defineProperty(_classNames, \"dragging\", dragging), _defineProperty(_classNames, 'drop-target', dropTargetKey === eventKey), _defineProperty(_classNames, 'drop-container', dropContainerKey === eventKey), _defineProperty(_classNames, 'drag-over', !disabled && dragOver), _defineProperty(_classNames, 'drag-over-gap-top', !disabled && dragOverGapTop), _defineProperty(_classNames, 'drag-over-gap-bottom', !disabled && dragOverGapBottom), _defineProperty(_classNames, 'filter-node', filterTreeNode && filterTreeNode(convertNodePropsToEventData(this.props))), _classNames)),\n        style: style // Draggable config\n        ,\n        draggable: draggableWithoutDisabled,\n        \"aria-grabbed\": dragging,\n        onDragStart: draggableWithoutDisabled ? this.onDragStart : undefined // Drop config\n        ,\n        onDragEnter: mergedDraggable ? this.onDragEnter : undefined,\n        onDragOver: mergedDraggable ? this.onDragOver : undefined,\n        onDragLeave: mergedDraggable ? this.onDragLeave : undefined,\n        onDrop: mergedDraggable ? this.onDrop : undefined,\n        onDragEnd: mergedDraggable ? this.onDragEnd : undefined,\n        onMouseMove: onMouseMove\n      }, ariaSelected, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(Indent, {\n        prefixCls: prefixCls,\n        level: level,\n        isStart: isStart,\n        isEnd: isEnd\n      }), this.renderDragHandler(), this.renderSwitcher(), this.renderCheckbox(), this.renderSelector());\n    }\n  }]);\n\n  return InternalTreeNode;\n}(React.Component);\n\nvar ContextTreeNode = function ContextTreeNode(props) {\n  return /*#__PURE__*/React.createElement(TreeContext.Consumer, null, function (context) {\n    return /*#__PURE__*/React.createElement(InternalTreeNode, _extends({}, props, {\n      context: context\n    }));\n  });\n};\n\nContextTreeNode.displayName = 'TreeNode';\nContextTreeNode.defaultProps = {\n  title: defaultTitle\n};\nContextTreeNode.isTreeNode = 1;\nexport { InternalTreeNode };\nexport default ContextTreeNode;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,YAAY,CAAC;AACzP,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,sBAAsB,CAAC,CAAC;;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,2BAA2B,QAAQ,kBAAkB;AAC9D,IAAIC,SAAS,GAAG,MAAM;AACtB,IAAIC,UAAU,GAAG,OAAO;AACxB,IAAIC,YAAY,GAAG,KAAK;AAExB,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC9Db,SAAS,CAACY,gBAAgB,EAAEC,gBAAgB,CAAC;EAE7C,IAAIC,MAAM,GAAGb,YAAY,CAACW,gBAAgB,CAAC;EAE3C,SAASA,gBAAgBA,CAAA,EAAG;IAC1B,IAAIG,KAAK;IAETlB,eAAe,CAAC,IAAI,EAAEe,gBAAgB,CAAC;IAEvC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACU,KAAK,GAAG;MACZC,iBAAiB,EAAE;IACrB,CAAC;IACDX,KAAK,CAACY,YAAY,GAAG,KAAK,CAAC;IAE3BZ,KAAK,CAACa,eAAe,GAAG,UAAUC,CAAC,EAAE;MACnC;MACA,IAAIC,WAAW,GAAGf,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACF,WAAW;MACjDA,WAAW,CAACD,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;MAExD,IAAIhB,KAAK,CAACkB,YAAY,CAAC,CAAC,EAAE;QACxBlB,KAAK,CAACmB,QAAQ,CAACL,CAAC,CAAC;MACnB,CAAC,MAAM;QACLd,KAAK,CAACoB,OAAO,CAACN,CAAC,CAAC;MAClB;IACF,CAAC;IAEDd,KAAK,CAACqB,qBAAqB,GAAG,UAAUP,CAAC,EAAE;MACzC,IAAIQ,iBAAiB,GAAGtB,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACK,iBAAiB;MAC7DA,iBAAiB,CAACR,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;IAChE,CAAC;IAEDhB,KAAK,CAACmB,QAAQ,GAAG,UAAUL,CAAC,EAAE;MAC5B,IAAId,KAAK,CAACuB,UAAU,CAAC,CAAC,EAAE;MACxB,IAAIC,YAAY,GAAGxB,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACO,YAAY;MACnDV,CAAC,CAACW,cAAc,CAAC,CAAC;MAClBD,YAAY,CAACV,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;IAC3D,CAAC;IAEDhB,KAAK,CAACoB,OAAO,GAAG,UAAUN,CAAC,EAAE;MAC3B,IAAId,KAAK,CAACuB,UAAU,CAAC,CAAC,EAAE;MACxB,IAAIG,WAAW,GAAG1B,KAAK,CAACgB,KAAK;QACzBW,eAAe,GAAGD,WAAW,CAACC,eAAe;QAC7CC,OAAO,GAAGF,WAAW,CAACE,OAAO;MACjC,IAAIC,WAAW,GAAG7B,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACY,WAAW;MACjD,IAAI,CAAC7B,KAAK,CAAC8B,WAAW,CAAC,CAAC,IAAIH,eAAe,EAAE;MAC7Cb,CAAC,CAACW,cAAc,CAAC,CAAC;MAClB,IAAIM,aAAa,GAAG,CAACH,OAAO;MAC5BC,WAAW,CAACf,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,EAAEe,aAAa,CAAC;IACzE,CAAC;IAED/B,KAAK,CAACgC,YAAY,GAAG,UAAUlB,CAAC,EAAE;MAChC,IAAImB,gBAAgB,GAAGjC,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACgB,gBAAgB;MAC3DA,gBAAgB,CAACnB,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;IAC/D,CAAC;IAEDhB,KAAK,CAACkC,YAAY,GAAG,UAAUpB,CAAC,EAAE;MAChC,IAAIqB,gBAAgB,GAAGnC,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACkB,gBAAgB;MAC3DA,gBAAgB,CAACrB,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;IAC/D,CAAC;IAEDhB,KAAK,CAACoC,aAAa,GAAG,UAAUtB,CAAC,EAAE;MACjC,IAAIuB,iBAAiB,GAAGrC,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACoB,iBAAiB;MAC7DA,iBAAiB,CAACvB,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;IAChE,CAAC;IAEDhB,KAAK,CAACsC,WAAW,GAAG,UAAUxB,CAAC,EAAE;MAC/B,IAAIyB,eAAe,GAAGvC,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACsB,eAAe;MACzDzB,CAAC,CAAC0B,eAAe,CAAC,CAAC;MAEnBxC,KAAK,CAACyC,QAAQ,CAAC;QACb9B,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF4B,eAAe,CAACzB,CAAC,EAAE9B,sBAAsB,CAACgB,KAAK,CAAC,CAAC;MAEjD,IAAI;QACF;QACA;QACAc,CAAC,CAAC4B,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAC;MAAA;IAEnB,CAAC;IAED5C,KAAK,CAAC6C,WAAW,GAAG,UAAU/B,CAAC,EAAE;MAC/B,IAAIgC,eAAe,GAAG9C,KAAK,CAACgB,KAAK,CAACC,OAAO,CAAC6B,eAAe;MACzDhC,CAAC,CAACW,cAAc,CAAC,CAAC;MAClBX,CAAC,CAAC0B,eAAe,CAAC,CAAC;MACnBM,eAAe,CAAChC,CAAC,EAAE9B,sBAAsB,CAACgB,KAAK,CAAC,CAAC;IACnD,CAAC;IAEDA,KAAK,CAAC+C,UAAU,GAAG,UAAUjC,CAAC,EAAE;MAC9B,IAAIkC,cAAc,GAAGhD,KAAK,CAACgB,KAAK,CAACC,OAAO,CAAC+B,cAAc;MACvDlC,CAAC,CAACW,cAAc,CAAC,CAAC;MAClBX,CAAC,CAAC0B,eAAe,CAAC,CAAC;MACnBQ,cAAc,CAAClC,CAAC,EAAE9B,sBAAsB,CAACgB,KAAK,CAAC,CAAC;IAClD,CAAC;IAEDA,KAAK,CAACiD,WAAW,GAAG,UAAUnC,CAAC,EAAE;MAC/B,IAAIoC,eAAe,GAAGlD,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACiC,eAAe;MACzDpC,CAAC,CAAC0B,eAAe,CAAC,CAAC;MACnBU,eAAe,CAACpC,CAAC,EAAE9B,sBAAsB,CAACgB,KAAK,CAAC,CAAC;IACnD,CAAC;IAEDA,KAAK,CAACmD,SAAS,GAAG,UAAUrC,CAAC,EAAE;MAC7B,IAAIsC,aAAa,GAAGpD,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACmC,aAAa;MACrDtC,CAAC,CAAC0B,eAAe,CAAC,CAAC;MAEnBxC,KAAK,CAACyC,QAAQ,CAAC;QACb9B,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEFyC,aAAa,CAACtC,CAAC,EAAE9B,sBAAsB,CAACgB,KAAK,CAAC,CAAC;IACjD,CAAC;IAEDA,KAAK,CAACqD,MAAM,GAAG,UAAUvC,CAAC,EAAE;MAC1B,IAAIwC,UAAU,GAAGtD,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACqC,UAAU;MAC/CxC,CAAC,CAACW,cAAc,CAAC,CAAC;MAClBX,CAAC,CAAC0B,eAAe,CAAC,CAAC;MAEnBxC,KAAK,CAACyC,QAAQ,CAAC;QACb9B,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF2C,UAAU,CAACxC,CAAC,EAAE9B,sBAAsB,CAACgB,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEDA,KAAK,CAACuD,QAAQ,GAAG,UAAUzC,CAAC,EAAE;MAC5B,IAAI0C,YAAY,GAAGxD,KAAK,CAACgB,KAAK;QAC1ByC,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,YAAY,GAAGF,YAAY,CAACvC,OAAO,CAACyC,YAAY;MACpD,IAAID,OAAO,EAAE;MACbC,YAAY,CAAC5C,CAAC,EAAErB,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;IAC3D,CAAC;IAEDhB,KAAK,CAAC2D,eAAe,GAAG,UAAUC,IAAI,EAAE;MACtC5D,KAAK,CAACY,YAAY,GAAGgD,IAAI;IAC3B,CAAC;IAED5D,KAAK,CAAC6D,YAAY,GAAG,YAAY;MAC/B,IAAIC,QAAQ,GAAG9D,KAAK,CAACgB,KAAK,CAAC8C,QAAQ;MAEnC,IAAI9D,KAAK,CAAC+D,MAAM,CAAC,CAAC,EAAE;QAClB,OAAO,IAAI;MACb;MAEA,OAAOD,QAAQ,GAAGpE,SAAS,GAAGC,UAAU;IAC1C,CAAC;IAEDK,KAAK,CAACgE,WAAW,GAAG,YAAY;MAC9B,IAAIC,QAAQ,GAAGjE,KAAK,CAACgB,KAAK,CAACiD,QAAQ;MACnC,IAAIC,WAAW,GAAGlE,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACiD,WAAW;MAEjD,IAAIC,IAAI,GAAGD,WAAW,CAACD,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClCG,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAE5B,OAAO,CAAC,CAAC,CAACA,QAAQ,IAAI,EAAE,EAAEjE,MAAM;IAClC,CAAC;IAEDH,KAAK,CAAC+D,MAAM,GAAG,YAAY;MACzB,IAAIM,YAAY,GAAGrE,KAAK,CAACgB,KAAK;QAC1B+C,MAAM,GAAGM,YAAY,CAACN,MAAM;QAC5BO,MAAM,GAAGD,YAAY,CAACC,MAAM;MAChC,IAAIC,QAAQ,GAAGvE,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACsD,QAAQ;MAE3C,IAAIP,WAAW,GAAGhE,KAAK,CAACgE,WAAW,CAAC,CAAC;MAErC,IAAID,MAAM,KAAK,KAAK,EAAE;QACpB,OAAO,KAAK;MACd;MAEA,OAAOA,MAAM,IAAI,CAACQ,QAAQ,IAAI,CAACP,WAAW,IAAIO,QAAQ,IAAID,MAAM,IAAI,CAACN,WAAW;IAClF,CAAC;IAEDhE,KAAK,CAACuB,UAAU,GAAG,YAAY;MAC7B,IAAIiD,QAAQ,GAAGxE,KAAK,CAACgB,KAAK,CAACwD,QAAQ;MACnC,IAAIC,YAAY,GAAGzE,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACuD,QAAQ;MAC/C,OAAO,CAAC,EAAEC,YAAY,IAAID,QAAQ,CAAC;IACrC,CAAC;IAEDxE,KAAK,CAAC8B,WAAW,GAAG,YAAY;MAC9B,IAAI4C,SAAS,GAAG1E,KAAK,CAACgB,KAAK,CAAC0D,SAAS;MACrC,IAAIC,aAAa,GAAG3E,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACyD,SAAS,CAAC,CAAC;;MAEnD,IAAI,CAACC,aAAa,IAAID,SAAS,KAAK,KAAK,EAAE,OAAO,KAAK;MACvD,OAAOC,aAAa;IACtB,CAAC;IAED3E,KAAK,CAAC4E,YAAY,GAAG,UAAU5D,KAAK,EAAE;MACpC,IAAI8C,QAAQ,GAAG9C,KAAK,CAAC8C,QAAQ;QACzBL,OAAO,GAAGzC,KAAK,CAACyC,OAAO;QACvBa,MAAM,GAAGtD,KAAK,CAACsD,MAAM;MACzB,IAAIO,mBAAmB,GAAG7E,KAAK,CAACgB,KAAK,CAACC,OAAO;QACzCsD,QAAQ,GAAGM,mBAAmB,CAACN,QAAQ;QACvCO,UAAU,GAAGD,mBAAmB,CAACC,UAAU;MAE/C,IAAIrB,OAAO,EAAE;QACX;MACF,CAAC,CAAC;;MAGF,IAAIc,QAAQ,IAAIT,QAAQ,IAAI,CAAC9D,KAAK,CAAC+D,MAAM,CAAC,CAAC,EAAE;QAC3C;QACA;QACA,IAAI,CAAC/D,KAAK,CAACgE,WAAW,CAAC,CAAC,IAAI,CAACM,MAAM,EAAE;UACnCQ,UAAU,CAACrF,2BAA2B,CAACO,KAAK,CAACgB,KAAK,CAAC,CAAC;QACtD;MACF;IACF,CAAC;IAEDhB,KAAK,CAAC+E,WAAW,GAAG,YAAY;MAC9B,IAAIC,YAAY,GAAGhF,KAAK,CAACgB,KAAK;QAC1BiE,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,SAAS,GAAGF,YAAY,CAAC/D,OAAO,CAACiE,SAAS;MAC9C,OAAO,CAAC,EAAEA,SAAS,KAAK,CAACA,SAAS,CAACC,aAAa,IAAID,SAAS,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC,CAAC;IACrF,CAAC;IAEDjF,KAAK,CAACoF,iBAAiB,GAAG,YAAY;MACpC,IAAIC,oBAAoB,GAAGrF,KAAK,CAACgB,KAAK,CAACC,OAAO;QAC1CiE,SAAS,GAAGG,oBAAoB,CAACH,SAAS;QAC1CI,SAAS,GAAGD,oBAAoB,CAACC,SAAS;MAC9C,OAAO,CAACJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,IAAI,IAAI,aAAanG,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QACvHC,SAAS,EAAE,EAAE,CAAChF,MAAM,CAAC6E,SAAS,EAAE,iBAAiB;MACnD,CAAC,EAAEJ,SAAS,CAACK,IAAI,CAAC,GAAG,IAAI;IAC3B,CAAC;IAEDvF,KAAK,CAAC0F,qBAAqB,GAAG,UAAU3B,MAAM,EAAE;MAC9C,IAAI4B,qBAAqB,GAAG3F,KAAK,CAACgB,KAAK,CAAC4E,YAAY;MACpD,IAAIC,mBAAmB,GAAG7F,KAAK,CAACgB,KAAK,CAACC,OAAO,CAAC2E,YAAY;MAC1D,IAAIA,YAAY,GAAGD,qBAAqB,IAAIE,mBAAmB,CAAC,CAAC;;MAEjE,IAAI,OAAOD,YAAY,KAAK,UAAU,EAAE;QACtC,OAAOA,YAAY,CAAC/G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,KAAK,CAACgB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACpE+C,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;MACL;MAEA,OAAO6B,YAAY;IACrB,CAAC;IAED5F,KAAK,CAAC8F,cAAc,GAAG,YAAY;MACjC,IAAIhC,QAAQ,GAAG9D,KAAK,CAACgB,KAAK,CAAC8C,QAAQ;MACnC,IAAIwB,SAAS,GAAGtF,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACqE,SAAS;MAE7C,IAAItF,KAAK,CAAC+D,MAAM,CAAC,CAAC,EAAE;QAClB;QACA,IAAIgC,gBAAgB,GAAG/F,KAAK,CAAC0F,qBAAqB,CAAC,IAAI,CAAC;QAExD,OAAOK,gBAAgB,KAAK,KAAK,GAAG,aAAa3G,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;UAC3EC,SAAS,EAAEpG,UAAU,CAAC,EAAE,CAACoB,MAAM,CAAC6E,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC7E,MAAM,CAAC6E,SAAS,EAAE,gBAAgB,CAAC;QACjG,CAAC,EAAES,gBAAgB,CAAC,GAAG,IAAI;MAC7B;MAEA,IAAIC,WAAW,GAAG3G,UAAU,CAAC,EAAE,CAACoB,MAAM,CAAC6E,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC7E,MAAM,CAAC6E,SAAS,EAAE,YAAY,CAAC,CAAC7E,MAAM,CAACqD,QAAQ,GAAGpE,SAAS,GAAGC,UAAU,CAAC,CAAC;MAE7I,IAAIsG,eAAe,GAAGjG,KAAK,CAAC0F,qBAAqB,CAAC,KAAK,CAAC;MAExD,OAAOO,eAAe,KAAK,KAAK,GAAG,aAAa7G,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC1EU,OAAO,EAAElG,KAAK,CAACuD,QAAQ;QACvBkC,SAAS,EAAEO;MACb,CAAC,EAAEC,eAAe,CAAC,GAAG,IAAI;IAC5B,CAAC;IAEDjG,KAAK,CAACmG,cAAc,GAAG,YAAY;MACjC,IAAIC,YAAY,GAAGpG,KAAK,CAACgB,KAAK;QAC1BY,OAAO,GAAGwE,YAAY,CAACxE,OAAO;QAC9ByE,WAAW,GAAGD,YAAY,CAACC,WAAW;QACtC1E,eAAe,GAAGyE,YAAY,CAACzE,eAAe;MAClD,IAAI2D,SAAS,GAAGtF,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACqE,SAAS;MAE7C,IAAId,QAAQ,GAAGxE,KAAK,CAACuB,UAAU,CAAC,CAAC;MAEjC,IAAImD,SAAS,GAAG1E,KAAK,CAAC8B,WAAW,CAAC,CAAC;MAEnC,IAAI,CAAC4C,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC;;MAE7B,IAAI4B,OAAO,GAAG,OAAO5B,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,IAAI;MAC/D,OAAO,aAAatF,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC9CC,SAAS,EAAEpG,UAAU,CAAC,EAAE,CAACoB,MAAM,CAAC6E,SAAS,EAAE,WAAW,CAAC,EAAE1D,OAAO,IAAI,EAAE,CAACnB,MAAM,CAAC6E,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAAC1D,OAAO,IAAIyE,WAAW,IAAI,EAAE,CAAC5F,MAAM,CAAC6E,SAAS,EAAE,yBAAyB,CAAC,EAAE,CAACd,QAAQ,IAAI7C,eAAe,KAAK,EAAE,CAAClB,MAAM,CAAC6E,SAAS,EAAE,oBAAoB,CAAC,CAAC;QACvQY,OAAO,EAAElG,KAAK,CAACoB;MACjB,CAAC,EAAEkF,OAAO,CAAC;IACb,CAAC;IAEDtG,KAAK,CAACuG,UAAU,GAAG,YAAY;MAC7B,IAAI9C,OAAO,GAAGzD,KAAK,CAACgB,KAAK,CAACyC,OAAO;MACjC,IAAI6B,SAAS,GAAGtF,KAAK,CAACgB,KAAK,CAACC,OAAO,CAACqE,SAAS;MAC7C,OAAO,aAAalG,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC9CC,SAAS,EAAEpG,UAAU,CAAC,EAAE,CAACoB,MAAM,CAAC6E,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC7E,MAAM,CAAC6E,SAAS,EAAE,SAAS,CAAC,CAAC7E,MAAM,CAACT,KAAK,CAAC6D,YAAY,CAAC,CAAC,IAAI,MAAM,CAAC,EAAEJ,OAAO,IAAI,EAAE,CAAChD,MAAM,CAAC6E,SAAS,EAAE,eAAe,CAAC;MAClL,CAAC,CAAC;IACJ,CAAC;IAEDtF,KAAK,CAACwG,cAAc,GAAG,YAAY;MACjC,IAAI7F,iBAAiB,GAAGX,KAAK,CAACU,KAAK,CAACC,iBAAiB;MACrD,IAAI8F,YAAY,GAAGzG,KAAK,CAACgB,KAAK;QAC1B0F,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QAChCpB,IAAI,GAAGkB,YAAY,CAAClB,IAAI;QACxB9B,OAAO,GAAGgD,YAAY,CAAChD,OAAO;QAC9BwB,IAAI,GAAGwB,YAAY,CAACxB,IAAI;MAC5B,IAAI2B,oBAAoB,GAAG5G,KAAK,CAACgB,KAAK,CAACC,OAAO;QAC1CqE,SAAS,GAAGsB,oBAAoB,CAACtB,SAAS;QAC1CuB,QAAQ,GAAGD,oBAAoB,CAACC,QAAQ;QACxCC,QAAQ,GAAGF,oBAAoB,CAACrB,IAAI;QACpChB,QAAQ,GAAGqC,oBAAoB,CAACrC,QAAQ;QACxCwC,WAAW,GAAGH,oBAAoB,CAACG,WAAW;MAElD,IAAIvC,QAAQ,GAAGxE,KAAK,CAACuB,UAAU,CAAC,CAAC;MAEjC,IAAIyF,SAAS,GAAG,EAAE,CAACvG,MAAM,CAAC6E,SAAS,EAAE,uBAAuB,CAAC,CAAC,CAAC;;MAE/D,IAAI2B,KAAK;MAET,IAAIJ,QAAQ,EAAE;QACZ,IAAIK,WAAW,GAAG3B,IAAI,IAAIuB,QAAQ;QAClCG,KAAK,GAAGC,WAAW,GAAG,aAAa9H,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;UAC7DC,SAAS,EAAEpG,UAAU,CAAC,EAAE,CAACoB,MAAM,CAAC6E,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC7E,MAAM,CAAC6E,SAAS,EAAE,kBAAkB,CAAC;QAClG,CAAC,EAAE,OAAO4B,WAAW,KAAK,UAAU,GAAGA,WAAW,CAAClH,KAAK,CAACgB,KAAK,CAAC,GAAGkG,WAAW,CAAC,GAAGlH,KAAK,CAACuG,UAAU,CAAC,CAAC;MACrG,CAAC,MAAM,IAAIhC,QAAQ,IAAId,OAAO,EAAE;QAC9BwD,KAAK,GAAGjH,KAAK,CAACuG,UAAU,CAAC,CAAC;MAC5B,CAAC,CAAC;;MAGF,IAAIY,SAAS;MAEb,IAAI,OAAOT,KAAK,KAAK,UAAU,EAAE;QAC/BS,SAAS,GAAGT,KAAK,CAACzB,IAAI,CAAC;MACzB,CAAC,MAAM,IAAI8B,WAAW,EAAE;QACtBI,SAAS,GAAGJ,WAAW,CAAC9B,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLkC,SAAS,GAAGT,KAAK;MACnB;MAEA,IAAIU,MAAM,GAAG,aAAahI,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QACpDC,SAAS,EAAE,EAAE,CAAChF,MAAM,CAAC6E,SAAS,EAAE,QAAQ;MAC1C,CAAC,EAAE6B,SAAS,CAAC;MACb,OAAO,aAAa/H,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC9C6B,GAAG,EAAErH,KAAK,CAAC2D,eAAe;QAC1B+C,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE;QAC7CjB,SAAS,EAAEpG,UAAU,CAAC,EAAE,CAACoB,MAAM,CAACuG,SAAS,CAAC,EAAE,EAAE,CAACvG,MAAM,CAACuG,SAAS,EAAE,GAAG,CAAC,CAACvG,MAAM,CAACT,KAAK,CAAC6D,YAAY,CAAC,CAAC,IAAI,QAAQ,CAAC,EAAE,CAACW,QAAQ,KAAKmC,QAAQ,IAAIhG,iBAAiB,CAAC,IAAI,EAAE,CAACF,MAAM,CAAC6E,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACvMtD,YAAY,EAAEhC,KAAK,CAACgC,YAAY;QAChCE,YAAY,EAAElC,KAAK,CAACkC,YAAY;QAChCE,aAAa,EAAEpC,KAAK,CAACoC,aAAa;QAClC8D,OAAO,EAAElG,KAAK,CAACa,eAAe;QAC9ByG,aAAa,EAAEtH,KAAK,CAACqB;MACvB,CAAC,EAAE4F,KAAK,EAAEG,MAAM,EAAEpH,KAAK,CAACuH,mBAAmB,CAAC,CAAC,CAAC;IAChD,CAAC;IAEDvH,KAAK,CAACuH,mBAAmB,GAAG,YAAY;MACtC,IAAIC,YAAY,GAAGxH,KAAK,CAACgB,KAAK;QAC1BwD,QAAQ,GAAGgD,YAAY,CAAChD,QAAQ;QAChCP,QAAQ,GAAGuD,YAAY,CAACvD,QAAQ;MACpC,IAAIwD,oBAAoB,GAAGzH,KAAK,CAACgB,KAAK,CAACC,OAAO;QAC1CiE,SAAS,GAAGuC,oBAAoB,CAACvC,SAAS;QAC1CwC,eAAe,GAAGD,oBAAoB,CAACC,eAAe;QACtDC,YAAY,GAAGF,oBAAoB,CAACE,YAAY;QAChDrC,SAAS,GAAGmC,oBAAoB,CAACnC,SAAS;QAC1CsC,MAAM,GAAGH,oBAAoB,CAACG,MAAM;QACpCC,mBAAmB,GAAGJ,oBAAoB,CAACI,mBAAmB;QAC9DC,eAAe,GAAGL,oBAAoB,CAACK,eAAe;QACtDC,SAAS,GAAGN,oBAAoB,CAACM,SAAS;MAC9C,IAAIC,aAAa,GAAG9C,SAAS,KAAK,KAAK,CAAC,CAAC;;MAEzC,IAAI+C,aAAa,GAAG,CAACzD,QAAQ,IAAIwD,aAAa,IAAIF,eAAe,KAAK7D,QAAQ;MAC9E,OAAOgE,aAAa,GAAGJ,mBAAmB,CAAC;QACzCF,YAAY,EAAEA,YAAY;QAC1BD,eAAe,EAAEA,eAAe;QAChCE,MAAM,EAAEA,MAAM;QACdtC,SAAS,EAAEA,SAAS;QACpByC,SAAS,EAAEA;MACb,CAAC,CAAC,GAAG,IAAI;IACX,CAAC;IAED,OAAO/H,KAAK;EACd;EAEAjB,YAAY,CAACc,gBAAgB,EAAE,CAAC;IAC9BqI,GAAG,EAAE,mBAAmB;IACxBC,KAAK;IAAE;IACP,SAASC,iBAAiBA,CAAA,EAAG;MAC3B,IAAI,CAACxD,YAAY,CAAC,IAAI,CAAC5D,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDkH,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASE,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACzD,YAAY,CAAC,IAAI,CAAC5D,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDkH,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASjH,YAAYA,CAAA,EAAG;MAC7B,IAAIoH,UAAU,GAAG,IAAI,CAACtH,KAAK,CAACsH,UAAU;MACtC,IAAIC,cAAc,GAAG,IAAI,CAACvH,KAAK,CAACC,OAAO,CAACqH,UAAU,CAAC,CAAC;;MAEpD,IAAI,OAAOA,UAAU,KAAK,SAAS,EAAE;QACnC,OAAOA,UAAU;MACnB;MAEA,OAAOC,cAAc;IACvB;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,QAAQ;IACbC,KAAK;IAAE;IACP,SAASK,MAAMA,CAAA,EAAG;MAChB,IAAIC,WAAW;MAEf,IAAIC,YAAY,GAAG,IAAI,CAAC1H,KAAK;QACzBiD,QAAQ,GAAGyE,YAAY,CAACzE,QAAQ;QAChCwB,SAAS,GAAGiD,YAAY,CAACjD,SAAS;QAClCkD,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QAChCC,cAAc,GAAGH,YAAY,CAACG,cAAc;QAC5CC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;QAClD/E,MAAM,GAAG2E,YAAY,CAAC3E,MAAM;QAC5BgF,OAAO,GAAGL,YAAY,CAACK,OAAO;QAC9BC,KAAK,GAAGN,YAAY,CAACM,KAAK;QAC1BlF,QAAQ,GAAG4E,YAAY,CAAC5E,QAAQ;QAChC6C,QAAQ,GAAG+B,YAAY,CAAC/B,QAAQ;QAChC/E,OAAO,GAAG8G,YAAY,CAAC9G,OAAO;QAC9ByE,WAAW,GAAGqC,YAAY,CAACrC,WAAW;QACtC5C,OAAO,GAAGiF,YAAY,CAACjF,OAAO;QAC9BwF,MAAM,GAAGP,YAAY,CAACO,MAAM;QAC5BC,MAAM,GAAGR,YAAY,CAACQ,MAAM;QAC5BjE,IAAI,GAAGyD,YAAY,CAACzD,IAAI;QACxBkE,WAAW,GAAGT,YAAY,CAACS,WAAW;QACtCb,UAAU,GAAGI,YAAY,CAACJ,UAAU;QACpCc,UAAU,GAAGxK,wBAAwB,CAAC8J,YAAY,EAAEvJ,SAAS,CAAC;MAElE,IAAIkK,oBAAoB,GAAG,IAAI,CAACrI,KAAK,CAACC,OAAO;QACzCqE,SAAS,GAAG+D,oBAAoB,CAAC/D,SAAS;QAC1CgE,cAAc,GAAGD,oBAAoB,CAACC,cAAc;QACpDpF,WAAW,GAAGmF,oBAAoB,CAACnF,WAAW;QAC9CqF,gBAAgB,GAAGF,oBAAoB,CAACE,gBAAgB;QACxDC,aAAa,GAAGH,oBAAoB,CAACG,aAAa;QAClDC,eAAe,GAAGJ,oBAAoB,CAACI,eAAe;MAC1D,IAAIjF,QAAQ,GAAG,IAAI,CAACjD,UAAU,CAAC,CAAC;MAChC,IAAImI,wBAAwB,GAAGpK,SAAS,CAAC8J,UAAU,EAAE;QACnDO,IAAI,EAAE,IAAI;QACV1E,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAI2E,KAAK,GAAG1F,WAAW,CAACD,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnC4F,KAAK,GAAGD,KAAK,CAACC,KAAK;MAEvB,IAAIC,SAAS,GAAGd,KAAK,CAACA,KAAK,CAAC7I,MAAM,GAAG,CAAC,CAAC;MACvC,IAAI4J,eAAe,GAAG,IAAI,CAAChF,WAAW,CAAC,CAAC;MACxC,IAAIiF,wBAAwB,GAAG,CAACxF,QAAQ,IAAIuF,eAAe;MAC3D,IAAIE,QAAQ,GAAGR,eAAe,KAAKxF,QAAQ;MAC3C,IAAIiG,YAAY,GAAG5B,UAAU,KAAK6B,SAAS,GAAG;QAC5C,eAAe,EAAE,CAAC,CAAC7B;MACrB,CAAC,GAAG6B,SAAS;MACb,OAAO,aAAa/K,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE9G,QAAQ,CAAC;QACtD2I,GAAG,EAAE4B,MAAM;QACXxD,SAAS,EAAEpG,UAAU,CAACoG,SAAS,EAAE,EAAE,CAAChF,MAAM,CAAC6E,SAAS,EAAE,WAAW,CAAC,GAAGmD,WAAW,GAAG,CAAC,CAAC,EAAE9J,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,oBAAoB,CAAC,EAAEd,QAAQ,CAAC,EAAE7F,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,qBAAqB,CAAC,CAAC7E,MAAM,CAACqD,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE,CAACC,MAAM,CAAC,EAAEpF,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,4BAA4B,CAAC,EAAE1D,OAAO,CAAC,EAAEjD,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,kCAAkC,CAAC,EAAEe,WAAW,CAAC,EAAE1H,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,oBAAoB,CAAC,EAAEqB,QAAQ,CAAC,EAAEhI,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,mBAAmB,CAAC,EAAE7B,OAAO,CAAC,EAAE9E,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,kBAAkB,CAAC,EAAE4D,MAAM,CAAC,EAAEvK,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,qBAAqB,CAAC,EAAEwE,SAAS,CAAC,EAAEnL,eAAe,CAAC8J,WAAW,EAAE,EAAE,CAAChI,MAAM,CAAC6E,SAAS,EAAE,qBAAqB,CAAC,EAAE0E,wBAAwB,CAAC,EAAErL,eAAe,CAAC8J,WAAW,EAAE,UAAU,EAAEwB,QAAQ,CAAC,EAAEtL,eAAe,CAAC8J,WAAW,EAAE,aAAa,EAAEe,aAAa,KAAKvF,QAAQ,CAAC,EAAEtF,eAAe,CAAC8J,WAAW,EAAE,gBAAgB,EAAEc,gBAAgB,KAAKtF,QAAQ,CAAC,EAAEtF,eAAe,CAAC8J,WAAW,EAAE,WAAW,EAAE,CAACjE,QAAQ,IAAIoE,QAAQ,CAAC,EAAEjK,eAAe,CAAC8J,WAAW,EAAE,mBAAmB,EAAE,CAACjE,QAAQ,IAAIqE,cAAc,CAAC,EAAElK,eAAe,CAAC8J,WAAW,EAAE,sBAAsB,EAAE,CAACjE,QAAQ,IAAIsE,iBAAiB,CAAC,EAAEnK,eAAe,CAAC8J,WAAW,EAAE,aAAa,EAAEa,cAAc,IAAIA,cAAc,CAAC7J,2BAA2B,CAAC,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC,EAAEyH,WAAW,CAAC,CAAC;QAC78CE,KAAK,EAAEA,KAAK,CAAC;QAAA;;QAEbzD,SAAS,EAAE8E,wBAAwB;QACnC,cAAc,EAAEC,QAAQ;QACxB3H,WAAW,EAAE0H,wBAAwB,GAAG,IAAI,CAAC1H,WAAW,GAAG6H,SAAS,CAAC;QAAA;;QAErEtH,WAAW,EAAEkH,eAAe,GAAG,IAAI,CAAClH,WAAW,GAAGsH,SAAS;QAC3DpH,UAAU,EAAEgH,eAAe,GAAG,IAAI,CAAChH,UAAU,GAAGoH,SAAS;QACzDlH,WAAW,EAAE8G,eAAe,GAAG,IAAI,CAAC9G,WAAW,GAAGkH,SAAS;QAC3D9G,MAAM,EAAE0G,eAAe,GAAG,IAAI,CAAC1G,MAAM,GAAG8G,SAAS;QACjDhH,SAAS,EAAE4G,eAAe,GAAG,IAAI,CAAC5G,SAAS,GAAGgH,SAAS;QACvDhB,WAAW,EAAEA;MACf,CAAC,EAAEe,YAAY,EAAER,wBAAwB,CAAC,EAAE,aAAatK,KAAK,CAACoG,aAAa,CAAChG,MAAM,EAAE;QACnF8F,SAAS,EAAEA,SAAS;QACpBuE,KAAK,EAAEA,KAAK;QACZd,OAAO,EAAEA,OAAO;QAChBC,KAAK,EAAEA;MACT,CAAC,CAAC,EAAE,IAAI,CAAC5D,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACU,cAAc,CAAC,CAAC,EAAE,IAAI,CAACK,cAAc,CAAC,CAAC,EAAE,IAAI,CAACK,cAAc,CAAC,CAAC,CAAC;IACpG;EACF,CAAC,CAAC,CAAC;EAEH,OAAO3G,gBAAgB;AACzB,CAAC,CAACT,KAAK,CAACgL,SAAS,CAAC;AAElB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACrJ,KAAK,EAAE;EACpD,OAAO,aAAa5B,KAAK,CAACoG,aAAa,CAACjG,WAAW,CAAC+K,QAAQ,EAAE,IAAI,EAAE,UAAUrJ,OAAO,EAAE;IACrF,OAAO,aAAa7B,KAAK,CAACoG,aAAa,CAAC3F,gBAAgB,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEsC,KAAK,EAAE;MAC5EC,OAAO,EAAEA;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AAEDoJ,eAAe,CAACE,WAAW,GAAG,UAAU;AACxCF,eAAe,CAACG,YAAY,GAAG;EAC7B9D,KAAK,EAAE9G;AACT,CAAC;AACDyK,eAAe,CAACI,UAAU,GAAG,CAAC;AAC9B,SAAS5K,gBAAgB;AACzB,eAAewK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}