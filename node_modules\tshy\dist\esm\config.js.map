{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config.ts"], "names": [], "mappings": "AAAA,uCAAuC;AAEvC,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AACrC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,IAAI,MAAM,WAAW,CAAA;AAC5B,OAAO,GAAG,MAAM,cAAc,CAAA;AAC9B,OAAO,OAAO,MAAM,cAAc,CAAA;AAMlC,OAAO,aAAa,MAAM,qBAAqB,CAAA;AAC/C,OAAO,YAAY,MAAM,oBAAoB,CAAA;AAC7C,OAAO,YAAY,MAAM,oBAAoB,CAAA;AAC7C,OAAO,kBAAkB,MAAM,2BAA2B,CAAA;AAC1D,OAAO,YAAY,MAAM,oBAAoB,CAAA;AAC7C,OAAO,YAAY,MAAM,oBAAoB,CAAA;AAE7C,MAAM,YAAY,GAAG,CAAC,CAAsB,EAAE,IAAY,EAAE,EAAE;IAC5D,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;IACjB,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,SAAS;QAAE,OAAO,IAAI,CAAA;IAC1D,IAAI,CAAC,QAAQ,IAAI,8CAA8C,GAAG,CAAC,CAAC,CAAA;IACpE,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACxB,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,CAAC,CAAM,EAAiB,EAAE,CAC9C,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAA;AAEhE,MAAM,WAAW,GAAG,CAAC,CAAM,EAAmC,EAAE,CAC9D,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,QAAQ;IACrB,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS;QACtB,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ;QAC7B,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC;QACxB,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAI,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS,IAAI,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS,IAAI,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACpD,kBAAkB,CAAC,CAAC,CAAC;IACrB,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC;IAC3B,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;AAE5B,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,OAAoB,EAAW,EAAE,CACzD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAE/B,MAAM,YAAY,GAAG,CAAC,CAAoB,EAAe,EAAE,CACzD,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAErB,MAAM,SAAS,GAAG,CAChB,GAAY,EACZ,OAAoB,EACR,EAAE;IACd,MAAM,IAAI,GACR,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;IACvC,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAA;IAChC,IACE,OAAO,aAAa,KAAK,QAAQ;QACjC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAC5B,CAAC;QACD,iDAAiD;QACjD,+DAA+D;QAC/D,MAAM,GAAG,GAA8C,EAAE,CAAA;QACzD,MAAM,OAAO,GAAsB,aAAa,CAAA;QAChD,MAAM,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;QAC/B,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,SAAQ;YAC/C,4CAA4C;YAC5C,MAAM,EAAE,GACN,sCAAsC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CACrD,IAAI;gBACJ,CAAC;qBACE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;qBACzB,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CACzC,CAAA;YACH,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAA;QACpB,CAAC;QACD,4CAA4C;QAC5C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAA;YAC/D,OAAO,IAAI,CAAC,OAAO,CAAA;YACnB,aAAa,GAAG,SAAS,CAAA;QAC3B,CAAC;aAAM,CAAC;YACN,oBAAoB;YACpB,GAAG,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAA;YACxC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;QACpB,CAAC;IACH,CAAC;IACD,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,EAAgB,CAAA;IACxC,MAAM,EAAE,GAAG,MAAwC,CAAA;IACnD,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CACX,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YACvB,uCAAuC,CAC1C,CAAA;QACD,GAAG,CAAC,OAAO,GAAG;YACZ,GAAG,GAAG,CAAC,OAAO;YACd,GAAG,EAAE,CAAC,OAAO;SACd,CAAA;QACD,OAAO,EAAE,CAAC,OAAO,CAAA;IACnB,CAAC;IACD,YAAY,CAAC,GAAG,CAAC,CAAA;IACjB,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,CAAC,GAA8C;YACnD,gBAAgB,EAAE,gBAAgB;SACnC,CAAA;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;YACxB,IAAI,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;gBACV,MAAK;YACP,CAAC;QACH,CAAC;QACD,MAAM,CAAC,OAAO,GAAG,CAAC,CAAA;QAClB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;QAChB,aAAa,GAAG,CAAC,CAAA;IACnB,CAAC;IACD,2DAA2D;IAC3D,gEAAgE;IAChE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,CAAA;IAC9C,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,MAAM,MAAM,GAAe,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;AAClD,eAAe,MAAM,CAAA", "sourcesContent": ["// get the config and package and stuff\n\nimport chalk from 'chalk'\nimport { Minimatch } from 'minimatch'\nimport * as console from './console.js'\nimport fail from './fail.js'\nimport pkg from './package.js'\nimport sources from './sources.js'\nimport {\n  Package,\n  TshyConfig,\n  TshyConfigMaybeGlobExports,\n} from './types.js'\nimport validDialects from './valid-dialects.js'\nimport validExclude from './valid-exclude.js'\nimport validExports from './valid-exports.js'\nimport validExtraDialects from './valid-extra-dialects.js'\nimport validImports from './valid-imports.js'\nimport validProject from './valid-project.js'\n\nconst validBoolean = (e: Record<string, any>, name: string) => {\n  const v = e[name]\n  if (v === undefined || typeof v === 'boolean') return true\n  fail(`tshy.${name} must be a boolean value if specified, got: ` + v)\n  return process.exit(1)\n}\n\nconst isStringArray = (e: any): e is string[] =>\n  !!e && Array.isArray(e) && !e.some(e => typeof e !== 'string')\n\nconst validConfig = (e: any): e is TshyConfigMaybeGlobExports =>\n  !!e &&\n  typeof e === 'object' &&\n  (e.exports === undefined ||\n    typeof e.exports === 'string' ||\n    isStringArray(e.exports) ||\n    validExports(e.exports)) &&\n  (e.dialects === undefined || validDialects(e.dialects)) &&\n  (e.project === undefined || validProject(e.project)) &&\n  (e.exclude === undefined || validExclude(e.exclude)) &&\n  validExtraDialects(e) &&\n  validBoolean(e, 'selfLink') &&\n  validBoolean(e, 'main') &&\n  validBoolean(e, 'liveDev')\n\nconst match = (e: string, pattern: Minimatch[]): boolean =>\n  pattern.some(m => m.match(e))\n\nconst parsePattern = (p: string | string[]): Minimatch[] =>\n  Array.isArray(p) ?\n    p.map(p => new Minimatch(p.replace(/^\\.\\//, '')))\n  : parsePattern([p])\n\nconst getConfig = (\n  pkg: Package,\n  sources: Set<string>,\n): TshyConfig => {\n  const tshy: TshyConfigMaybeGlobExports =\n    validConfig(pkg.tshy) ? pkg.tshy : {}\n  let exportsConfig = tshy.exports\n  if (\n    typeof exportsConfig === 'string' ||\n    Array.isArray(exportsConfig)\n  ) {\n    // Strip off the `./src` prefix and the extension\n    // exports: \"src/**/*.ts\" => exports: {\"./foo\": \"./src/foo.ts\"}\n    const exp: Exclude<TshyConfig['exports'], undefined> = {}\n    const pattern: string | string[] = exportsConfig\n    const m = parsePattern(pattern)\n    for (const e of sources) {\n      if (!match(e.replace(/^\\.\\//, ''), m)) continue\n      // index is main, anything else is a subpath\n      const sp =\n        /^\\.\\/src\\/index.([mc]?[jt]s|[jt]sx)$/.test(e) ? '.' : (\n          './' +\n          e\n            .replace(/^\\.\\/src\\//, '')\n            .replace(/\\.([mc]?[tj]s|[jt]sx)$/, '')\n        )\n      exp[sp] = `./${e}`\n    }\n    /* c8 ignore start - should be impossible */\n    if (!validExports(exp)) {\n      console.error('invalid exports pattern, using default exports')\n      delete tshy.exports\n      exportsConfig = undefined\n    } else {\n      /* c8 ignore stop */\n      exp['./package.json'] = './package.json'\n      tshy.exports = exp\n    }\n  }\n  const config = { ...tshy } as TshyConfig\n  const ti = config as TshyConfig & { imports?: any }\n  if (ti.imports) {\n    console.debug(\n      chalk.cyan.dim('imports') +\n        ' moving from tshy config to top level',\n    )\n    pkg.imports = {\n      ...pkg.imports,\n      ...ti.imports,\n    }\n    delete ti.imports\n  }\n  validImports(pkg)\n  if (!exportsConfig) {\n    const e: Exclude<TshyConfig['exports'], undefined> = {\n      './package.json': './package.json',\n    }\n    for (const i of sources) {\n      if (/^\\.\\/src\\/index\\.[^\\.]+$/.test(i)) {\n        e['.'] = i\n        break\n      }\n    }\n    config.exports = e\n    tshy.exports = e\n    exportsConfig = e\n  }\n  // return the filled out config, but leave the package.json\n  // exports as they were, as long as they turned out to be valid.\n  pkg.tshy = { ...tshy, exports: exportsConfig }\n  return config\n}\n\nconst config: TshyConfig = getConfig(pkg, sources)\nexport default config\n"]}