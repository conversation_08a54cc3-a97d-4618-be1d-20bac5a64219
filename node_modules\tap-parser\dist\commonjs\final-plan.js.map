{"version": 3, "file": "final-plan.js", "sourceRoot": "", "sources": ["../../src/final-plan.ts"], "names": [], "mappings": ";;;AACA;;;GAGG;AACH,MAAa,SAAS;IACpB,KAAK,GAAkB,IAAI,CAAA;IAC3B,GAAG,GAAkB,IAAI,CAAA;IACzB,OAAO,CAAS;IAChB,UAAU,CAAQ;IAClB,OAAO,CAAQ;IAEf,YAAY,OAAgB,EAAE,IAAY;QACxC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC;YAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;QACpD,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC;YAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;QAC9C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;QACjD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAA;IACvC,CAAC;CACF;AAdD,8BAcC", "sourcesContent": ["import type { Parser } from './index.js'\n/**\n * The summary of the plan, for inclusion in the results\n * provided in the `complete` event.\n */\nexport class FinalPlan {\n  start: number | null = null\n  end: number | null = null\n  skipAll: boolean\n  skipReason: string\n  comment: string\n\n  constructor(skipAll: boolean, self: Parser) {\n    if (self.planStart >= 0) this.start = self.planStart\n    if (self.planEnd >= 0) this.end = self.planEnd\n    this.skipAll = skipAll\n    this.skipReason = skipAll ? self.planComment : ''\n    this.comment = self.planComment || ''\n  }\n}\n"]}