{"ast": null, "code": "import OriginTypography from './Typography';\nimport Text from './Text';\nimport Link from './Link';\nimport Title from './Title';\nimport Paragraph from './Paragraph';\nvar Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;", "map": {"version": 3, "names": ["OriginTypography", "Text", "Link", "Title", "Paragraph", "Typography"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/index.js"], "sourcesContent": ["import OriginTypography from './Typography';\nimport Text from './Text';\nimport Link from './Link';\nimport Title from './Title';\nimport Paragraph from './Paragraph';\nvar Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,cAAc;AAC3C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,UAAU,GAAGL,gBAAgB;AACjCK,UAAU,CAACJ,IAAI,GAAGA,IAAI;AACtBI,UAAU,CAACH,IAAI,GAAGA,IAAI;AACtBG,UAAU,CAACF,KAAK,GAAGA,KAAK;AACxBE,UAAU,CAACD,SAAS,GAAGA,SAAS;AAChC,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}