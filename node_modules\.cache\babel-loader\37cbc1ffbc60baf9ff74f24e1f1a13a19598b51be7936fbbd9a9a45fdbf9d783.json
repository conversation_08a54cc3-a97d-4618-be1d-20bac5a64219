{"ast": null, "code": "import { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport ReactDOM from 'react-dom';\nimport canUseDom from './Dom/canUseDom';\nvar Portal = /*#__PURE__*/forwardRef(function (props, ref) {\n  var didUpdate = props.didUpdate,\n    getContainer = props.getContainer,\n    children = props.children;\n  var parentRef = useRef();\n  var containerRef = useRef(); // Ref return nothing, only for wrapper check exist\n\n  useImperativeHandle(ref, function () {\n    return {};\n  }); // Create container in client side with sync to avoid useEffect not get ref\n\n  var initRef = useRef(false);\n  if (!initRef.current && canUseDom()) {\n    containerRef.current = getContainer();\n    parentRef.current = containerRef.current.parentNode;\n    initRef.current = true;\n  } // [Legacy] Used by `rc-trigger`\n\n  useEffect(function () {\n    didUpdate === null || didUpdate === void 0 ? void 0 : didUpdate(props);\n  });\n  useEffect(function () {\n    // Restore container to original place\n    // React 18 StrictMode will unmount first and mount back for effect test:\n    // https://reactjs.org/blog/2022/03/29/react-v18.html#new-strict-mode-behaviors\n    if (containerRef.current.parentNode === null && parentRef.current !== null) {\n      parentRef.current.appendChild(containerRef.current);\n    }\n    return function () {\n      var _containerRef$current, _containerRef$current2;\n\n      // [Legacy] This should not be handle by Portal but parent PortalWrapper instead.\n      // Since some component use `Portal` directly, we have to keep the logic here.\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : (_containerRef$current2 = _containerRef$current.parentNode) === null || _containerRef$current2 === void 0 ? void 0 : _containerRef$current2.removeChild(containerRef.current);\n    };\n  }, []);\n  return containerRef.current ? /*#__PURE__*/ReactDOM.createPortal(children, containerRef.current) : null;\n});\nexport default Portal;", "map": {"version": 3, "names": ["useRef", "useEffect", "forwardRef", "useImperativeHandle", "ReactDOM", "canUseDom", "Portal", "props", "ref", "didUpdate", "getContainer", "children", "parentRef", "containerRef", "initRef", "current", "parentNode", "append<PERSON><PERSON><PERSON>", "_containerRef$current", "_containerRef$current2", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Portal.js"], "sourcesContent": ["import { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport ReactDOM from 'react-dom';\nimport canUseDom from './Dom/canUseDom';\nvar Portal = /*#__PURE__*/forwardRef(function (props, ref) {\n  var didUpdate = props.didUpdate,\n      getContainer = props.getContainer,\n      children = props.children;\n  var parentRef = useRef();\n  var containerRef = useRef(); // Ref return nothing, only for wrapper check exist\n\n  useImperativeHandle(ref, function () {\n    return {};\n  }); // Create container in client side with sync to avoid useEffect not get ref\n\n  var initRef = useRef(false);\n\n  if (!initRef.current && canUseDom()) {\n    containerRef.current = getContainer();\n    parentRef.current = containerRef.current.parentNode;\n    initRef.current = true;\n  } // [Legacy] Used by `rc-trigger`\n\n\n  useEffect(function () {\n    didUpdate === null || didUpdate === void 0 ? void 0 : didUpdate(props);\n  });\n  useEffect(function () {\n    // Restore container to original place\n    // React 18 StrictMode will unmount first and mount back for effect test:\n    // https://reactjs.org/blog/2022/03/29/react-v18.html#new-strict-mode-behaviors\n    if (containerRef.current.parentNode === null && parentRef.current !== null) {\n      parentRef.current.appendChild(containerRef.current);\n    }\n\n    return function () {\n      var _containerRef$current, _containerRef$current2;\n\n      // [Legacy] This should not be handle by Portal but parent PortalWrapper instead.\n      // Since some component use `Portal` directly, we have to keep the logic here.\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : (_containerRef$current2 = _containerRef$current.parentNode) === null || _containerRef$current2 === void 0 ? void 0 : _containerRef$current2.removeChild(containerRef.current);\n    };\n  }, []);\n  return containerRef.current ? /*#__PURE__*/ReactDOM.createPortal(children, containerRef.current) : null;\n});\nexport default Portal;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAC1E,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,IAAIC,MAAM,GAAG,aAAaJ,UAAU,CAAC,UAAUK,KAAK,EAAEC,GAAG,EAAE;EACzD,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC7B,IAAIC,SAAS,GAAGZ,MAAM,CAAC,CAAC;EACxB,IAAIa,YAAY,GAAGb,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE7BG,mBAAmB,CAACK,GAAG,EAAE,YAAY;IACnC,OAAO,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIM,OAAO,GAAGd,MAAM,CAAC,KAAK,CAAC;EAE3B,IAAI,CAACc,OAAO,CAACC,OAAO,IAAIV,SAAS,CAAC,CAAC,EAAE;IACnCQ,YAAY,CAACE,OAAO,GAAGL,YAAY,CAAC,CAAC;IACrCE,SAAS,CAACG,OAAO,GAAGF,YAAY,CAACE,OAAO,CAACC,UAAU;IACnDF,OAAO,CAACC,OAAO,GAAG,IAAI;EACxB,CAAC,CAAC;;EAGFd,SAAS,CAAC,YAAY;IACpBQ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACF,KAAK,CAAC;EACxE,CAAC,CAAC;EACFN,SAAS,CAAC,YAAY;IACpB;IACA;IACA;IACA,IAAIY,YAAY,CAACE,OAAO,CAACC,UAAU,KAAK,IAAI,IAAIJ,SAAS,CAACG,OAAO,KAAK,IAAI,EAAE;MAC1EH,SAAS,CAACG,OAAO,CAACE,WAAW,CAACJ,YAAY,CAACE,OAAO,CAAC;IACrD;IAEA,OAAO,YAAY;MACjB,IAAIG,qBAAqB,EAAEC,sBAAsB;;MAEjD;MACA;MACA,CAACD,qBAAqB,GAAGL,YAAY,CAACE,OAAO,MAAM,IAAI,IAAIG,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACF,UAAU,MAAM,IAAI,IAAIG,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACC,WAAW,CAACP,YAAY,CAACE,OAAO,CAAC;IACtR,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,YAAY,CAACE,OAAO,GAAG,aAAaX,QAAQ,CAACiB,YAAY,CAACV,QAAQ,EAAEE,YAAY,CAACE,OAAO,CAAC,GAAG,IAAI;AACzG,CAAC,CAAC;AACF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}