{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\gestioneAutisti.jsx\";\n/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneAutisti - operazioni sugli autisti\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Caricamento from \"../../utils/caricamento\";\nimport AggiungiAutisti from \"../../aggiunta_dati/aggiungiAutisti\";\nimport UtenteAutista from \"../../aggiunta_dati/utenteAutista\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { <PERSON>nti } from \"../../components/traduttore/const\";\nimport \"../../css/DataTableDemo.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneAutisti extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      customerName: \"\",\n      address: \"\",\n      pIva: \"\",\n      email: \"\",\n      isValid: \"\",\n      createAt: \"\",\n      updateAt: \"\"\n    };\n    this.array = [];\n    this.state = {\n      results: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: true\n    };\n    //Dichiarazione funzioni e metodi\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.aggiungiAutisti = this.aggiungiAutisti.bind(this);\n    this.hideaggiungiAffiliati = this.hideaggiungiAffiliati.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.addUser = this.addUser.bind(this);\n    this.hideUtenteAffiliato = this.hideUtenteAffiliato.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=AUTISTA\").then(res => {\n      this.setState({\n        results: res.data,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli autisti. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Apertura dialogo aggiunta utente\n  addUser(rowData, value) {\n    localStorage.setItem(\"datiComodo\", rowData.id);\n    this.setState({\n      resultDialog3: true\n    });\n  }\n  //Chiusura dialogo aggiunta utente\n  hideUtenteAffiliato() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiAutisti() {\n    this.setState({\n      resultDialog2: true\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAffiliati() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.idemployee !== this.state.result.idemployee);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"employees/?id=\" + this.state.result.idemployee;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Autista eliminato con successo\",\n      life: 3000\n    });\n    window.location.reload();\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideaggiungiAffiliati,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideUtenteAffiliato,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n    const fields = [{\n      field: \"username\",\n      header: \"Username\",\n      body: \"username\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"first_name\",\n      header: Costanti.Nome,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"address\",\n      header: Costanti.Indirizzo,\n      body: \"address\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"city\",\n      header: Costanti.Città,\n      body: \"city\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"cap\",\n      header: Costanti.CodPost,\n      body: \"cap\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"p_iva\",\n      header: Costanti.pIva,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tel\",\n      header: Costanti.Tel,\n      body: \"tel\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"email\",\n      header: Costanti.Email,\n      body: \"email\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"isValid\",\n      header: Costanti.Validità,\n      body: \"isValid\",\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.addUser,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-user-plus\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 39\n      }, this),\n      handler: this.addUser\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 39\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    const items = [{\n      label: Costanti.AggAut,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiAutisti();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneAutisti\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          selectionMode: \"single\",\n          cellSelection: true,\n          fileNames: \"Autisti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.AggUtAut,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideUtenteAffiliato,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UtenteAutista, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.SelAnagrafica,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hideaggiungiAffiliati,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AggiungiAutisti, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteCli, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.first_name + ' ' + this.state.result.last_name, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default GestioneAutisti;", "map": {"version": 3, "names": ["React", "Component", "Nav", "Caricamento", "AggiungiAutisti", "UtenteAutista", "CustomDataTable", "APIRequest", "Toast", "<PERSON><PERSON>", "Dialog", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "GestioneAutisti", "constructor", "props", "emptyResult", "id", "customerName", "address", "pIva", "email", "<PERSON><PERSON><PERSON><PERSON>", "createAt", "updateAt", "array", "state", "results", "resultDialog", "resultDialog2", "resultDialog3", "deleteResultDialog", "submitted", "result", "globalFilter", "loading", "confirmDeleteResult", "bind", "aggiungi<PERSON><PERSON><PERSON><PERSON>", "hideaggiungiAffiliati", "deleteResult", "hideDeleteResultDialog", "addUser", "hideUtenteAffiliato", "componentDidMount", "then", "res", "setState", "data", "catch", "e", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "rowData", "value", "localStorage", "setItem", "filter", "val", "idemployee", "url", "window", "location", "reload", "render", "resultDialogFooter2", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter3", "deleteResultDialogFooter", "label", "icon", "Si", "fields", "field", "header", "body", "sortable", "showHeader", "Nome", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Tel", "Email", "Validità", "actionFields", "name", "handler", "Elimina", "items", "AggAut", "command", "ref", "el", "gestioneAutisti", "dt", "dataKey", "paginator", "rows", "rowsPerPageOptions", "actionsColumn", "autoLayout", "splitButtonClass", "selectionMode", "cellSelection", "fileNames", "visible", "AggUtAut", "modal", "footer", "onHide", "SelAnagrafica", "Conferma", "style", "fontSize", "ResDeleteCli", "first_name", "last_name"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/gestioneAutisti.jsx"], "sourcesContent": ["/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneAutisti - operazioni sugli autisti\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Caricamento from \"../../utils/caricamento\";\nimport AggiungiAutisti from \"../../aggiunta_dati/aggiungiAutisti\";\nimport UtenteAutista from \"../../aggiunta_dati/utenteAutista\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Costa<PERSON> } from \"../../components/traduttore/const\";\nimport \"../../css/DataTableDemo.css\";\n\nclass GestioneAutisti extends Component {\n  //Stato iniziale elementi tabella\n  emptyResult = {\n    id: null,\n    customerName: \"\",\n    address: \"\",\n    pIva: \"\",\n    email: \"\",\n    isValid: \"\",\n    createAt: \"\",\n    updateAt: \"\",\n  };\n  array = [];\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    this.state = {\n      results: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: true,\n    };\n    //Dichiarazione funzioni e metodi\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.aggiungiAutisti = this.aggiungiAutisti.bind(this);\n    this.hideaggiungiAffiliati = this.hideaggiungiAffiliati.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.addUser = this.addUser.bind(this);\n    this.hideUtenteAffiliato = this.hideUtenteAffiliato.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=AUTISTA\")\n      .then((res) => {\n        this.setState({\n          results: res.data,\n          loading: false\n        });\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare gli autisti. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({ deleteResultDialog: false });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true,\n    });\n  }\n  //Apertura dialogo aggiunta utente\n  addUser(rowData, value) {\n    localStorage.setItem(\"datiComodo\", rowData.id);\n    this.setState({\n      resultDialog3: true,\n    });\n  }\n  //Chiusura dialogo aggiunta utente\n  hideUtenteAffiliato() {\n    this.setState({\n      resultDialog3: false,\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiAutisti() {\n    this.setState({\n      resultDialog2: true,\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAffiliati() {\n    this.setState({\n      resultDialog2: false,\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(\n      (val) => val.idemployee !== this.state.result.idemployee\n    );\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult,\n    });\n\n    let url = \"employees/?id=\" + this.state.result.idemployee;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Autista eliminato con successo\",\n      life: 3000,\n    });\n    window.location.reload();\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideaggiungiAffiliati}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter3 = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideUtenteAffiliato}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = (\n      <React.Fragment>\n        <Button\n          label=\"No\"\n          icon=\"pi pi-times\"\n          className=\"p-button-text\"\n          onClick={this.hideDeleteResultDialog}\n        />\n        <Button className=\"p-button-text\" onClick={this.deleteResult}>\n          {\" \"}\n          {Costanti.Si}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    const fields = [\n      {\n        field: \"username\",\n        header: \"Username\",\n        body: \"username\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"first_name\",\n        header: Costanti.Nome,\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"address\",\n        header: Costanti.Indirizzo,\n        body: \"address\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"city\",\n        header: Costanti.Città,\n        body: \"city\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"cap\",\n        header: Costanti.CodPost,\n        body: \"cap\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"p_iva\",\n        header: Costanti.pIva,\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"tel\",\n        header: Costanti.Tel,\n        body: \"tel\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"email\",\n        header: Costanti.Email,\n        body: \"email\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"isValid\",\n        header: Costanti.Validità,\n        body: \"isValid\",\n        showHeader: true,\n      },\n    ];\n    const actionFields = [\n      { name: Costanti.addUser, icon: <i className=\"pi pi-user-plus\" />, handler: this.addUser },\n      { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n    ];\n    const items = [\n      {\n        label: Costanti.AggAut,\n        icon: 'pi pi-plus-circle',\n        command: () => {\n          this.aggiungiAutisti()\n        }\n      },\n    ]\n    return (\n      <div className=\"datatable-responsive-demo wrapper\">\n        {/* Il componente Toast permette di creare e visualizzare messaggi */}\n        <Toast ref={(el) => (this.toast = el)} />\n        {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n        <Nav />\n        <div className=\"col-12 px-0 solid-head\">\n          <h1>{Costanti.gestioneAutisti}</h1>\n        </div>\n        <div className=\"card\">\n          {/* Componente primereact per la creazione della tabella */}\n          <CustomDataTable\n            ref={(el) => (this.dt = el)}\n            value={this.state.results}\n            fields={fields}\n            loading={this.state.loading}\n            dataKey=\"id\"\n            paginator\n            rows={20}\n            rowsPerPageOptions={[10, 20, 50]}\n            actionsColumn={actionFields}\n            autoLayout={true}\n            splitButtonClass={true}\n            items={items}\n            selectionMode=\"single\"\n            cellSelection={true}\n            fileNames=\"Autisti\"\n          />\n        </div>\n        {/* Struttura dialogo per l'aggiunta utente */}\n        <Dialog\n          visible={this.state.resultDialog3}\n          header={Costanti.AggUtAut}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter3}\n          onHide={this.hideUtenteAffiliato}\n        >\n          <Caricamento />\n          <UtenteAutista />\n        </Dialog>\n        {/* Struttura dialogo per l'aggiunta */}\n        <Dialog\n          visible={this.state.resultDialog2}\n          header={Costanti.SelAnagrafica}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter2}\n          onHide={this.hideaggiungiAffiliati}\n        >\n          <Caricamento />\n          <AggiungiAutisti />\n        </Dialog>\n        {/* Struttura dialogo per la cancellazione */}\n        <Dialog\n          visible={this.state.deleteResultDialog}\n          header={Costanti.Conferma}\n          modal\n          footer={deleteResultDialogFooter}\n          onHide={this.hideDeleteResultDialog}\n        >\n          <div className=\"confirmation-content\">\n            <i\n              className=\"pi pi-exclamation-triangle p-mr-3\"\n              style={{ fontSize: \"2rem\" }}\n            />\n            {this.state.result && (\n              <span>\n                {Costanti.ResDeleteCli} <b>{this.state.result.first_name + ' ' + this.state.result.last_name}?</b>\n              </span>\n            )}\n          </div>\n        </Dialog>\n      </div>\n    );\n  }\n}\n\nexport default GestioneAutisti;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,eAAe,SAASb,SAAS,CAAC;EAatCc,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ;IAdF;IAAA,KACAC,WAAW,GAAG;MACZC,EAAE,EAAE,IAAI;MACRC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;IAAA,KACDC,KAAK,GAAG,EAAE;IAIR,IAAI,CAACC,KAAK,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,IAAI,CAACjB,WAAW;MACxBkB,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;IACX,CAAC;IACD;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACD,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACF,IAAI,CAAC,IAAI,CAAC;IAClE,IAAI,CAACG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACI,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO,CAACL,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACN,IAAI,CAAC,IAAI,CAAC;EAChE;EACA;EACA,MAAMO,iBAAiBA,CAAA,EAAG;IACxB,MAAMtC,UAAU,CAAC,KAAK,EAAE,6CAA6C,CAAC,CACnEuC,IAAI,CAAEC,GAAG,IAAK;MACb,IAAI,CAACC,QAAQ,CAAC;QACZpB,OAAO,EAAEmB,GAAG,CAACE,IAAI;QACjBb,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,CACDc,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACZC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYH,IAAI,MAAKc,SAAS,IAAAV,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,GAAGE,CAAC,CAACa,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EACA;EACAvB,sBAAsBA,CAAA,EAAG;IACvB,IAAI,CAACM,QAAQ,CAAC;MAAEhB,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAC9C;EACA;EACAK,mBAAmBA,CAACH,MAAM,EAAE;IAC1B,IAAI,CAACc,QAAQ,CAAC;MACZd,MAAM;MACNF,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;EACA;EACAW,OAAOA,CAACuB,OAAO,EAAEC,KAAK,EAAE;IACtBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEH,OAAO,CAAChD,EAAE,CAAC;IAC9C,IAAI,CAAC8B,QAAQ,CAAC;MACZjB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAa,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACI,QAAQ,CAAC;MACZjB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAQ,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACS,QAAQ,CAAC;MACZlB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAU,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAACQ,QAAQ,CAAC;MACZlB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACA,MAAMW,YAAYA,CAAA,EAAG;IACnB,IAAIb,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAAC0C,MAAM,CACpCC,GAAG,IAAKA,GAAG,CAACC,UAAU,KAAK,IAAI,CAAC7C,KAAK,CAACO,MAAM,CAACsC,UAChD,CAAC;IACD,IAAI,CAACxB,QAAQ,CAAC;MACZpB,OAAO;MACPI,kBAAkB,EAAE,KAAK;MACzBE,MAAM,EAAE,IAAI,CAACjB;IACf,CAAC,CAAC;IAEF,IAAIwD,GAAG,GAAG,gBAAgB,GAAG,IAAI,CAAC9C,KAAK,CAACO,MAAM,CAACsC,UAAU;IACzD,IAAIzB,GAAG,GAAG,MAAMxC,UAAU,CAAC,QAAQ,EAAEkE,GAAG,CAAC;IACzCnB,OAAO,CAACC,GAAG,CAACR,GAAG,CAACE,IAAI,CAAC;IACrB,IAAI,CAACO,KAAK,CAACC,IAAI,CAAC;MACdC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,YAAY;MACrBC,MAAM,EAAE,gCAAgC;MACxCK,IAAI,EAAE;IACR,CAAC,CAAC;IACFS,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACP;IACA,MAAMC,mBAAmB,gBACvBjE,OAAA,CAACb,KAAK,CAAC+E,QAAQ;MAAAC,QAAA,eACbnE,OAAA,CAACJ,MAAM;QAACwE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC1C,qBAAsB;QAAAwC,QAAA,GACnE,GAAG,EACHrE,QAAQ,CAACwE,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAMC,mBAAmB,gBACvB3E,OAAA,CAACb,KAAK,CAAC+E,QAAQ;MAAAC,QAAA,eACbnE,OAAA,CAACJ,MAAM;QAACwE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACtC,mBAAoB;QAAAoC,QAAA,GACjE,GAAG,EACHrE,QAAQ,CAACwE,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAME,wBAAwB,gBAC5B5E,OAAA,CAACb,KAAK,CAAC+E,QAAQ;MAAAC,QAAA,gBACbnE,OAAA,CAACJ,MAAM;QACLiF,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBV,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAACxC;MAAuB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACF1E,OAAA,CAACJ,MAAM;QAACwE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACzC,YAAa;QAAAuC,QAAA,GAC1D,GAAG,EACHrE,QAAQ,CAACiF,EAAE,EAAE,GAAG;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD,MAAMM,MAAM,GAAG,CACb;MACEC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,YAAY;MACnBC,MAAM,EAAEpF,QAAQ,CAACwF,IAAI;MACrBF,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAEpF,QAAQ,CAACyF,SAAS;MAC1BJ,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAEpF,QAAQ,CAAC0F,KAAK;MACtBL,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,KAAK;MACZC,MAAM,EAAEpF,QAAQ,CAAC2F,OAAO;MACxBN,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,OAAO;MACdC,MAAM,EAAEpF,QAAQ,CAACU,IAAI;MACrB4E,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,KAAK;MACZC,MAAM,EAAEpF,QAAQ,CAAC4F,GAAG;MACpBP,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,OAAO;MACdC,MAAM,EAAEpF,QAAQ,CAAC6F,KAAK;MACtBR,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAEpF,QAAQ,CAAC8F,QAAQ;MACzBT,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IACd,CAAC,CACF;IACD,MAAMQ,YAAY,GAAG,CACnB;MAAEC,IAAI,EAAEhG,QAAQ,CAACgC,OAAO;MAAEgD,IAAI,eAAE9E,OAAA;QAAGoE,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEqB,OAAO,EAAE,IAAI,CAACjE;IAAQ,CAAC,EAC1F;MAAEgE,IAAI,EAAEhG,QAAQ,CAACkG,OAAO;MAAElB,IAAI,eAAE9E,OAAA;QAAGoE,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEqB,OAAO,EAAE,IAAI,CAACvE;IAAoB,CAAC,CACnG;IACD,MAAMyE,KAAK,GAAG,CACZ;MACEpB,KAAK,EAAE/E,QAAQ,CAACoG,MAAM;MACtBpB,IAAI,EAAE,mBAAmB;MACzBqB,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACzE,eAAe,CAAC,CAAC;MACxB;IACF,CAAC,CACF;IACD,oBACE1B,OAAA;MAAKoE,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAEhDnE,OAAA,CAACL,KAAK;QAACyG,GAAG,EAAGC,EAAE,IAAM,IAAI,CAAC1D,KAAK,GAAG0D;MAAI;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzC1E,OAAA,CAACX,GAAG;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP1E,OAAA;QAAKoE,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrCnE,OAAA;UAAAmE,QAAA,EAAKrE,QAAQ,CAACwG;QAAe;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACN1E,OAAA;QAAKoE,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEnBnE,OAAA,CAACP,eAAe;UACd2G,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACE,EAAE,GAAGF,EAAI;UAC5B/C,KAAK,EAAE,IAAI,CAACxC,KAAK,CAACC,OAAQ;UAC1BiE,MAAM,EAAEA,MAAO;UACfzD,OAAO,EAAE,IAAI,CAACT,KAAK,CAACS,OAAQ;UAC5BiF,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEf,YAAa;UAC5BgB,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBb,KAAK,EAAEA,KAAM;UACbc,aAAa,EAAC,QAAQ;UACtBC,aAAa,EAAE,IAAK;UACpBC,SAAS,EAAC;QAAS;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1E,OAAA,CAACH,MAAM;QACLqH,OAAO,EAAE,IAAI,CAACpG,KAAK,CAACI,aAAc;QAClCgE,MAAM,EAAEpF,QAAQ,CAACqH,QAAS;QAC1BC,KAAK;QACLhD,SAAS,EAAC,kBAAkB;QAC5BiD,MAAM,EAAE1C,mBAAoB;QAC5B2C,MAAM,EAAE,IAAI,CAACvF,mBAAoB;QAAAoC,QAAA,gBAEjCnE,OAAA,CAACV,WAAW;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf1E,OAAA,CAACR,aAAa;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAET1E,OAAA,CAACH,MAAM;QACLqH,OAAO,EAAE,IAAI,CAACpG,KAAK,CAACG,aAAc;QAClCiE,MAAM,EAAEpF,QAAQ,CAACyH,aAAc;QAC/BH,KAAK;QACLhD,SAAS,EAAC,kBAAkB;QAC5BiD,MAAM,EAAEpD,mBAAoB;QAC5BqD,MAAM,EAAE,IAAI,CAAC3F,qBAAsB;QAAAwC,QAAA,gBAEnCnE,OAAA,CAACV,WAAW;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf1E,OAAA,CAACT,eAAe;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAET1E,OAAA,CAACH,MAAM;QACLqH,OAAO,EAAE,IAAI,CAACpG,KAAK,CAACK,kBAAmB;QACvC+D,MAAM,EAAEpF,QAAQ,CAAC0H,QAAS;QAC1BJ,KAAK;QACLC,MAAM,EAAEzC,wBAAyB;QACjC0C,MAAM,EAAE,IAAI,CAACzF,sBAAuB;QAAAsC,QAAA,eAEpCnE,OAAA;UAAKoE,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnCnE,OAAA;YACEoE,SAAS,EAAC,mCAAmC;YAC7CqD,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,EACD,IAAI,CAAC5D,KAAK,CAACO,MAAM,iBAChBrB,OAAA;YAAAmE,QAAA,GACGrE,QAAQ,CAAC6H,YAAY,EAAC,GAAC,eAAA3H,OAAA;cAAAmE,QAAA,GAAI,IAAI,CAACrD,KAAK,CAACO,MAAM,CAACuG,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC9G,KAAK,CAACO,MAAM,CAACwG,SAAS,EAAC,GAAC;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;AACF;AAEA,eAAezE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}