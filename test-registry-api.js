/**
 * <PERSON>ript per testare la risposta dell'API GET /registry/
 * Verifica se la struttura della risposta è cambiata
 */

const http = require('http');

console.log('🔍 Test API GET /registry/ - Verifica struttura risposta\n');

// Funzione per fare la richiesta HTTP
function testRegistryAPI() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/registry/',
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Aggiungi qui eventuali header di autenticazione se necessari
            },
            timeout: 5000
        };

        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.end();
    });
}

// Esegui il test
testRegistryAPI()
    .then(response => {
        console.log('✅ Risposta ricevuta dal server:');
        console.log('📊 Status Code:', response.status);
        console.log('📋 Content-Type:', response.headers['content-type']);
        
        if (response.parseError) {
            console.log('❌ Errore parsing JSON:', response.parseError);
            console.log('📄 Raw data:', response.data);
            return;
        }

        console.log('\n📦 Struttura della risposta:');
        console.log('Type of response.data:', typeof response.data);
        console.log('Is Array:', Array.isArray(response.data));
        
        if (Array.isArray(response.data)) {
            console.log('✅ CORRETTO: response.data è un array');
            console.log('📊 Numero elementi:', response.data.length);
            
            if (response.data.length > 0) {
                console.log('\n📋 Struttura primo elemento:');
                const firstItem = response.data[0];
                console.log('Chiavi disponibili:', Object.keys(firstItem));
                console.log('Primo elemento:', JSON.stringify(firstItem, null, 2));
            } else {
                console.log('⚠️ Array vuoto - nessun registry presente');
            }
        } else {
            console.log('❌ PROBLEMA: response.data NON è un array');
            console.log('Tipo effettivo:', typeof response.data);
            console.log('Struttura:', JSON.stringify(response.data, null, 2));
            
            // Verifica se i dati sono annidati
            if (response.data && typeof response.data === 'object') {
                console.log('\n🔍 Analisi struttura:');
                Object.keys(response.data).forEach(key => {
                    const value = response.data[key];
                    console.log(`- ${key}: ${typeof value} ${Array.isArray(value) ? `(array con ${value.length} elementi)` : ''}`);
                    
                    if (Array.isArray(value) && value.length > 0) {
                        console.log(`  Primo elemento di ${key}:`, Object.keys(value[0]));
                    }
                });
            }
        }

        console.log('\n🎯 Analisi per il frontend:');
        console.log('Il frontend si aspetta: res.data (array di registry)');
        console.log('Il server restituisce:', Array.isArray(response.data) ? 'Array ✅' : `${typeof response.data} ❌`);
        
        if (!Array.isArray(response.data)) {
            console.log('\n🔧 AZIONE RICHIESTA:');
            console.log('Il backend deve restituire un array direttamente in response.data');
            console.log('Oppure il frontend deve essere aggiornato per accedere ai dati nella struttura corretta');
        }
    })
    .catch(error => {
        console.log('❌ Errore nella richiesta:', error.message);
        
        if (error.message === 'Request timeout') {
            console.log('⏱️ Il server non risponde entro 5 secondi');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('🔌 Il backend non è in esecuzione sulla porta 3001');
        } else if (error.code === 'ENOTFOUND') {
            console.log('🌐 Impossibile raggiungere localhost');
        }
        
        console.log('\n🔧 Verificare:');
        console.log('1. Il backend è in esecuzione su localhost:3001');
        console.log('2. L\'endpoint /registry/ è disponibile');
        console.log('3. Non ci sono errori di autenticazione');
    });

console.log('🚀 Avvio test...');
console.log('📡 Chiamata: GET http://localhost:3001/registry/');
console.log('⏱️ Timeout: 5 secondi\n');
