{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Puff = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar Puff = function Puff(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 44 44\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    stroke: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"0s\",\n    dur: \"1.8s\",\n    values: \"1; 20\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.165, 0.84, 0.44, 1\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeOpacity\",\n    begin: \"0s\",\n    dur: \"1.8s\",\n    values: \"1; 0\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.3, 0.61, 0.355, 1\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"-0.9s\",\n    dur: \"1.8s\",\n    values: \"1; 20\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.165, 0.84, 0.44, 1\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeOpacity\",\n    begin: \"-0.9s\",\n    dur: \"1.8s\",\n    values: \"1; 0\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.3, 0.61, 0.355, 1\",\n    repeatCount: \"indefinite\"\n  }))));\n};\nexports.Puff = Puff;\nPuff.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nPuff.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 1\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON>", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "width", "height", "viewBox", "xmlns", "stroke", "color", "label", "fill", "fillRule", "strokeWidth", "cx", "cy", "r", "radius", "attributeName", "begin", "dur", "values", "calcMode", "keyTimes", "keySplines", "repeatCount", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/Puff.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Puff = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar Puff = function Puff(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 44 44\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    stroke: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"0s\",\n    dur: \"1.8s\",\n    values: \"1; 20\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.165, 0.84, 0.44, 1\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeOpacity\",\n    begin: \"0s\",\n    dur: \"1.8s\",\n    values: \"1; 0\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.3, 0.61, 0.355, 1\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"-0.9s\",\n    dur: \"1.8s\",\n    values: \"1; 20\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.165, 0.84, 0.44, 1\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeOpacity\",\n    begin: \"-0.9s\",\n    dur: \"1.8s\",\n    values: \"1; 0\",\n    calcMode: \"spline\",\n    keyTimes: \"0; 1\",\n    keySplines: \"0.3, 0.61, 0.355, 1\",\n    repeatCount: \"indefinite\"\n  }))));\n};\n\nexports.Puff = Puff;\nPuff.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nPuff.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 1\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AAErB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,IAAI,GAAG,SAASA,IAAIA,CAACO,KAAK,EAAE;EAC9B,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAEN,KAAK,CAACO,KAAK;IACnB,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDQ,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE;EACf,CAAC,EAAE,aAAajB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDW,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEd,KAAK,CAACe;EACX,CAAC,EAAE,aAAarB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDe,aAAa,EAAE,GAAG;IAClBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,sBAAsB;IAClCC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1De,aAAa,EAAE,eAAe;IAC9BC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,qBAAqB;IACjCC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DW,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEd,KAAK,CAACe;EACX,CAAC,EAAE,aAAarB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDe,aAAa,EAAE,GAAG;IAClBC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,sBAAsB;IAClCC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1De,aAAa,EAAE,eAAe;IAC9BC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,qBAAqB;IACjCC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAEDhC,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBA,IAAI,CAAC+B,SAAS,GAAG;EACfrB,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAAC4B,SAAS,CAAC,CAAC5B,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM,EAAE7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,CAAC,CAAC;EACrGzB,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAAC4B,SAAS,CAAC,CAAC5B,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM,EAAE7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,CAAC,CAAC;EACpGpB,KAAK,EAAEV,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM;EACnClB,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM;EACnCX,MAAM,EAAElB,UAAU,CAAC,SAAS,CAAC,CAAC8B;AAChC,CAAC;AACDlC,IAAI,CAACmC,YAAY,GAAG;EAClBzB,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTK,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,eAAe;EACtBO,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}