{"ast": null, "code": "import * as React from 'react';\nimport { useLayoutEffect } from './useLayoutEffect';\n/**\n * Work as `componentDidUpdate`\n */\n\nexport default function useUpdateEffect(callback, condition) {\n  var initRef = React.useRef(false);\n  useLayoutEffect(function () {\n    if (!initRef.current) {\n      initRef.current = true;\n      return undefined;\n    }\n    return callback();\n  }, condition);\n}", "map": {"version": 3, "names": ["React", "useLayoutEffect", "useUpdateEffect", "callback", "condition", "initRef", "useRef", "current", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-input-number/es/hooks/useUpdateEffect.js"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from './useLayoutEffect';\n/**\n * Work as `componentDidUpdate`\n */\n\nexport default function useUpdateEffect(callback, condition) {\n  var initRef = React.useRef(false);\n  useLayoutEffect(function () {\n    if (!initRef.current) {\n      initRef.current = true;\n      return undefined;\n    }\n\n    return callback();\n  }, condition);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,mBAAmB;AACnD;AACA;AACA;;AAEA,eAAe,SAASC,eAAeA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC3D,IAAIC,OAAO,GAAGL,KAAK,CAACM,MAAM,CAAC,KAAK,CAAC;EACjCL,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACI,OAAO,CAACE,OAAO,EAAE;MACpBF,OAAO,CAACE,OAAO,GAAG,IAAI;MACtB,OAAOC,SAAS;IAClB;IAEA,OAAOL,QAAQ,CAAC,CAAC;EACnB,CAAC,EAAEC,SAAS,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}