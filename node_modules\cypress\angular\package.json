{"name": "@cypress/angular", "version": "0.0.0-development", "description": "Test Angular Components using Cypress", "main": "dist/index.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "build-prod": "yarn build", "check-ts": "tsc --noEmit"}, "dependencies": {}, "devDependencies": {"@angular/common": "^14.2.0", "@angular/core": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@cypress/mount-utils": "0.0.0-development", "typescript": "^4.7.4", "zone.js": "~0.11.4"}, "peerDependencies": {"@angular/common": ">=13", "@angular/core": ">=13", "@angular/platform-browser-dynamic": ">=13", "zone.js": ">=0.11.0"}, "files": ["dist"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/angular/#readme", "author": "<PERSON>", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Fangular&template=1-bug-report.md&title=", "keywords": ["angular", "cypress", "cypress-io", "test", "testing"], "contributors": [{"name": "<PERSON>", "social": "@jordanpowell88"}, {"name": "<PERSON>", "social": "@ZachJW34"}], "module": "dist/index.js", "publishConfig": {"access": "public"}, "standard": {"globals": ["Cypress", "cy", "expect"]}}