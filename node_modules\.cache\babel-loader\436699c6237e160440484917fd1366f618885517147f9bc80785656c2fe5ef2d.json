{"ast": null, "code": "/* eslint import/no-unresolved: 0 */\n// @ts-ignore\nimport version from './version';\nexport default version;", "map": {"version": 3, "names": ["version"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/version/index.js"], "sourcesContent": ["/* eslint import/no-unresolved: 0 */\n// @ts-ignore\nimport version from './version';\nexport default version;"], "mappings": "AAAA;AACA;AACA,OAAOA,OAAO,MAAM,WAAW;AAC/B,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}