{"ast": null, "code": "import InternalForm, { useForm, useWatch } from './Form';\nimport Item from './FormItem';\nimport ErrorList from './ErrorList';\nimport List from './FormList';\nimport { FormProvider } from './context';\nimport devWarning from '../_util/devWarning';\nimport useFormInstance from './hooks/useFormInstance';\nvar Form = InternalForm;\nForm.Item = Item;\nForm.List = List;\nForm.ErrorList = ErrorList;\nForm.useForm = useForm;\nForm.useFormInstance = useFormInstance;\nForm.useWatch = useWatch;\nForm.Provider = FormProvider;\nForm.create = function () {\n  devWarning(false, 'Form', 'antd v4 removed `Form.create`. Please remove or use `@ant-design/compatible` instead.');\n};\nexport default Form;", "map": {"version": 3, "names": ["InternalForm", "useForm", "useWatch", "<PERSON><PERSON>", "ErrorList", "List", "FormProvider", "dev<PERSON><PERSON><PERSON>", "useFormInstance", "Form", "Provider", "create"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/index.js"], "sourcesContent": ["import InternalForm, { useForm, useWatch } from './Form';\nimport Item from './FormItem';\nimport ErrorList from './ErrorList';\nimport List from './FormList';\nimport { FormProvider } from './context';\nimport devWarning from '../_util/devWarning';\nimport useFormInstance from './hooks/useFormInstance';\nvar Form = InternalForm;\nForm.Item = Item;\nForm.List = List;\nForm.ErrorList = ErrorList;\nForm.useForm = useForm;\nForm.useFormInstance = useFormInstance;\nForm.useWatch = useWatch;\nForm.Provider = FormProvider;\n\nForm.create = function () {\n  devWarning(false, 'Form', 'antd v4 removed `Form.create`. Please remove or use `@ant-design/compatible` instead.');\n};\n\nexport default Form;"], "mappings": "AAAA,OAAOA,YAAY,IAAIC,OAAO,EAAEC,QAAQ,QAAQ,QAAQ;AACxD,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,IAAIC,IAAI,GAAGT,YAAY;AACvBS,IAAI,CAACN,IAAI,GAAGA,IAAI;AAChBM,IAAI,CAACJ,IAAI,GAAGA,IAAI;AAChBI,IAAI,CAACL,SAAS,GAAGA,SAAS;AAC1BK,IAAI,CAACR,OAAO,GAAGA,OAAO;AACtBQ,IAAI,CAACD,eAAe,GAAGA,eAAe;AACtCC,IAAI,CAACP,QAAQ,GAAGA,QAAQ;AACxBO,IAAI,CAACC,QAAQ,GAAGJ,YAAY;AAE5BG,IAAI,CAACE,MAAM,GAAG,YAAY;EACxBJ,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,uFAAuF,CAAC;AACpH,CAAC;AAED,eAAeE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}