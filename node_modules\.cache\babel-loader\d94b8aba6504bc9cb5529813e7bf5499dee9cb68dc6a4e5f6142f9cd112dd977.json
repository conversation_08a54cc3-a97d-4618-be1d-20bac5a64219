{"ast": null, "code": "import React, { Component } from 'react';\nimport { <PERSON>ique<PERSON><PERSON>ponentId, ConnectedOverlayS<PERSON><PERSON><PERSON><PERSON><PERSON>, DomHandler, OverlayService, ZIndexUtils, Ripple, classNames, CSSTransition, Portal } from 'primereact/core';\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n  return _assertThisInitialized(self);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar OverlayPanel = /*#__PURE__*/function (_Component) {\n  _inherits(OverlayPanel, _Component);\n  var _super = _createSuper(OverlayPanel);\n  function OverlayPanel(props) {\n    var _this;\n    _classCallCheck(this, OverlayPanel);\n    _this = _super.call(this, props);\n    _this.state = {\n      visible: false\n    };\n    _this.onCloseClick = _this.onCloseClick.bind(_assertThisInitialized(_this));\n    _this.onPanelClick = _this.onPanelClick.bind(_assertThisInitialized(_this));\n    _this.onEnter = _this.onEnter.bind(_assertThisInitialized(_this));\n    _this.onEntered = _this.onEntered.bind(_assertThisInitialized(_this));\n    _this.onExit = _this.onExit.bind(_assertThisInitialized(_this));\n    _this.onExited = _this.onExited.bind(_assertThisInitialized(_this));\n    _this.attributeSelector = UniqueComponentId();\n    _this.overlayRef = /*#__PURE__*/React.createRef();\n    return _this;\n  }\n  _createClass(OverlayPanel, [{\n    key: \"bindDocumentClickListener\",\n    value: function bindDocumentClickListener() {\n      var _this2 = this;\n      if (!this.documentClickListener && this.props.dismissable) {\n        this.documentClickListener = function (event) {\n          if (!_this2.isPanelClicked && _this2.isOutsideClicked(event.target)) {\n            _this2.hide();\n          }\n          _this2.isPanelClicked = false;\n        };\n        document.addEventListener('click', this.documentClickListener);\n      }\n    }\n  }, {\n    key: \"unbindDocumentClickListener\",\n    value: function unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        document.removeEventListener('click', this.documentClickListener);\n        this.documentClickListener = null;\n      }\n    }\n  }, {\n    key: \"bindScrollListener\",\n    value: function bindScrollListener() {\n      var _this3 = this;\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, function () {\n          if (_this3.state.visible) {\n            _this3.hide();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n  }, {\n    key: \"unbindScrollListener\",\n    value: function unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n  }, {\n    key: \"bindResizeListener\",\n    value: function bindResizeListener() {\n      var _this4 = this;\n      if (!this.resizeListener) {\n        this.resizeListener = function () {\n          if (_this4.state.visible && !DomHandler.isAndroid()) {\n            _this4.hide();\n          }\n        };\n        window.addEventListener('resize', this.resizeListener);\n      }\n    }\n  }, {\n    key: \"unbindResizeListener\",\n    value: function unbindResizeListener() {\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n        this.resizeListener = null;\n      }\n    }\n  }, {\n    key: \"isOutsideClicked\",\n    value: function isOutsideClicked(target) {\n      return this.overlayRef && this.overlayRef.current && !(this.overlayRef.current.isSameNode(target) || this.overlayRef.current.contains(target));\n    }\n  }, {\n    key: \"hasTargetChanged\",\n    value: function hasTargetChanged(event, target) {\n      return this.target != null && this.target !== (target || event.currentTarget || event.target);\n    }\n  }, {\n    key: \"onCloseClick\",\n    value: function onCloseClick(event) {\n      this.hide();\n      event.preventDefault();\n    }\n  }, {\n    key: \"onPanelClick\",\n    value: function onPanelClick(event) {\n      this.isPanelClicked = true;\n      OverlayService.emit('overlay-click', {\n        originalEvent: event,\n        target: this.target\n      });\n    }\n  }, {\n    key: \"onContentClick\",\n    value: function onContentClick() {\n      this.isPanelClicked = true;\n    }\n  }, {\n    key: \"toggle\",\n    value: function toggle(event, target) {\n      var _this5 = this;\n      if (this.state.visible) {\n        this.hide();\n        if (this.hasTargetChanged(event, target)) {\n          this.target = target || event.currentTarget || event.target;\n          setTimeout(function () {\n            _this5.show(event, _this5.target);\n          }, 200);\n        }\n      } else {\n        this.show(event, target);\n      }\n    }\n  }, {\n    key: \"show\",\n    value: function show(event, target) {\n      var _this6 = this;\n      this.target = target || event.currentTarget || event.target;\n      if (this.state.visible) {\n        this.align();\n      } else {\n        this.setState({\n          visible: true\n        }, function () {\n          _this6.overlayEventListener = function (e) {\n            if (!_this6.isOutsideClicked(e.target)) {\n              _this6.isPanelClicked = true;\n            }\n          };\n          OverlayService.on('overlay-click', _this6.overlayEventListener);\n        });\n      }\n    }\n  }, {\n    key: \"hide\",\n    value: function hide() {\n      var _this7 = this;\n      this.setState({\n        visible: false\n      }, function () {\n        OverlayService.off('overlay-click', _this7.overlayEventListener);\n        _this7.overlayEventListener = null;\n      });\n    }\n  }, {\n    key: \"onEnter\",\n    value: function onEnter() {\n      ZIndexUtils.set('overlay', this.overlayRef.current);\n      this.overlayRef.current.setAttribute(this.attributeSelector, '');\n      this.align();\n    }\n  }, {\n    key: \"onEntered\",\n    value: function onEntered() {\n      this.bindDocumentClickListener();\n      this.bindScrollListener();\n      this.bindResizeListener();\n      this.props.onShow && this.props.onShow();\n    }\n  }, {\n    key: \"onExit\",\n    value: function onExit() {\n      this.unbindDocumentClickListener();\n      this.unbindScrollListener();\n      this.unbindResizeListener();\n    }\n  }, {\n    key: \"onExited\",\n    value: function onExited() {\n      ZIndexUtils.clear(this.overlayRef.current);\n      this.props.onHide && this.props.onHide();\n    }\n  }, {\n    key: \"align\",\n    value: function align() {\n      if (this.target) {\n        DomHandler.absolutePosition(this.overlayRef.current, this.target);\n        var containerOffset = DomHandler.getOffset(this.overlayRef.current);\n        var targetOffset = DomHandler.getOffset(this.target);\n        var arrowLeft = 0;\n        if (containerOffset.left < targetOffset.left) {\n          arrowLeft = targetOffset.left - containerOffset.left;\n        }\n        this.overlayRef.current.style.setProperty('--overlayArrowLeft', \"\".concat(arrowLeft, \"px\"));\n        if (containerOffset.top < targetOffset.top) {\n          DomHandler.addClass(this.overlayRef.current, 'p-overlaypanel-flipped');\n        }\n      }\n    }\n  }, {\n    key: \"createStyle\",\n    value: function createStyle() {\n      if (!this.styleElement) {\n        this.styleElement = document.createElement('style');\n        document.head.appendChild(this.styleElement);\n        var innerHTML = '';\n        for (var breakpoint in this.props.breakpoints) {\n          innerHTML += \"\\n                    @media screen and (max-width: \".concat(breakpoint, \") {\\n                        .p-overlaypanel[\").concat(this.attributeSelector, \"] {\\n                            width: \").concat(this.props.breakpoints[breakpoint], \" !important;\\n                        }\\n                    }\\n                \");\n        }\n        this.styleElement.innerHTML = innerHTML;\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.props.breakpoints) {\n        this.createStyle();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unbindDocumentClickListener();\n      this.unbindResizeListener();\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.styleElement) {\n        document.head.removeChild(this.styleElement);\n        this.styleElement = null;\n      }\n      if (this.overlayEventListener) {\n        OverlayService.off('overlay-click', this.overlayEventListener);\n        this.overlayEventListener = null;\n      }\n      ZIndexUtils.clear(this.overlayRef.current);\n    }\n  }, {\n    key: \"renderCloseIcon\",\n    value: function renderCloseIcon() {\n      if (this.props.showCloseIcon) {\n        return /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          className: \"p-overlaypanel-close p-link\",\n          onClick: this.onCloseClick,\n          \"aria-label\": this.props.ariaCloseLabel\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: \"p-overlaypanel-close-icon pi pi-times\"\n        }), /*#__PURE__*/React.createElement(Ripple, null));\n      }\n      return null;\n    }\n  }, {\n    key: \"renderElement\",\n    value: function renderElement() {\n      var className = classNames('p-overlaypanel p-component', this.props.className);\n      var closeIcon = this.renderCloseIcon();\n      return /*#__PURE__*/React.createElement(CSSTransition, {\n        nodeRef: this.overlayRef,\n        classNames: \"p-overlaypanel\",\n        in: this.state.visible,\n        timeout: {\n          enter: 120,\n          exit: 100\n        },\n        options: this.props.transitionOptions,\n        unmountOnExit: true,\n        onEnter: this.onEnter,\n        onEntered: this.onEntered,\n        onExit: this.onExit,\n        onExited: this.onExited\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.overlayRef,\n        id: this.props.id,\n        className: className,\n        style: this.props.style,\n        onClick: this.onPanelClick\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-overlaypanel-content\",\n        onClick: this.onContentClick,\n        onMouseDown: this.onContentClick\n      }, this.props.children), closeIcon));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var element = this.renderElement();\n      return /*#__PURE__*/React.createElement(Portal, {\n        element: element,\n        appendTo: this.props.appendTo\n      });\n    }\n  }]);\n  return OverlayPanel;\n}(Component);\n_defineProperty(OverlayPanel, \"defaultProps\", {\n  id: null,\n  dismissable: true,\n  showCloseIcon: false,\n  style: null,\n  className: null,\n  appendTo: null,\n  breakpoints: null,\n  ariaCloseLabel: 'close',\n  transitionOptions: null,\n  onShow: null,\n  onHide: null\n});\nexport { OverlayPanel };", "map": {"version": 3, "names": ["React", "Component", "UniqueComponentId", "ConnectedOverlayScrollHandler", "<PERSON><PERSON><PERSON><PERSON>", "OverlayService", "ZIndexUtils", "<PERSON><PERSON><PERSON>", "classNames", "CSSTransition", "Portal", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "constructor", "value", "_typeof", "obj", "Symbol", "iterator", "_possibleConstructorReturn", "call", "_getPrototypeOf", "getPrototypeOf", "_defineProperty", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "arguments", "apply", "sham", "Proxy", "Boolean", "valueOf", "e", "OverlayPanel", "_Component", "_super", "_this", "state", "visible", "onCloseClick", "bind", "onPanelClick", "onEnter", "onEntered", "onExit", "onExited", "attributeSelector", "overlayRef", "createRef", "bindDocumentClickListener", "_this2", "documentClickListener", "dismissable", "event", "isPanelClicked", "isOutsideClicked", "hide", "document", "addEventListener", "unbindDocumentClickListener", "removeEventListener", "bindScrollListener", "_this3", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "bindResizeListener", "_this4", "resizeListener", "isAndroid", "window", "unbindResizeListener", "current", "isSameNode", "contains", "hasTargetChanged", "currentTarget", "preventDefault", "emit", "originalEvent", "onContentClick", "toggle", "_this5", "setTimeout", "show", "_this6", "align", "setState", "overlayEventListener", "on", "_this7", "off", "set", "setAttribute", "onShow", "clear", "onHide", "absolutePosition", "containerOffset", "getOffset", "targetOffset", "arrowLeft", "left", "style", "setProperty", "concat", "top", "addClass", "createStyle", "styleElement", "createElement", "head", "append<PERSON><PERSON><PERSON>", "innerHTML", "breakpoint", "breakpoints", "componentDidMount", "componentWillUnmount", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "renderCloseIcon", "showCloseIcon", "type", "className", "onClick", "ariaCloseLabel", "renderElement", "closeIcon", "nodeRef", "in", "timeout", "enter", "exit", "options", "transitionOptions", "unmountOnExit", "ref", "id", "onMouseDown", "children", "render", "element", "appendTo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/primereact/overlaypanel/overlaypanel.esm.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport { <PERSON>ique<PERSON><PERSON>ponentId, ConnectedOverlayS<PERSON><PERSON><PERSON><PERSON><PERSON>, DomHandler, OverlayService, ZIndexUtils, Ripple, classNames, CSSTransition, Portal } from 'primereact/core';\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nvar OverlayPanel = /*#__PURE__*/function (_Component) {\n  _inherits(OverlayPanel, _Component);\n\n  var _super = _createSuper(OverlayPanel);\n\n  function OverlayPanel(props) {\n    var _this;\n\n    _classCallCheck(this, OverlayPanel);\n\n    _this = _super.call(this, props);\n    _this.state = {\n      visible: false\n    };\n    _this.onCloseClick = _this.onCloseClick.bind(_assertThisInitialized(_this));\n    _this.onPanelClick = _this.onPanelClick.bind(_assertThisInitialized(_this));\n    _this.onEnter = _this.onEnter.bind(_assertThisInitialized(_this));\n    _this.onEntered = _this.onEntered.bind(_assertThisInitialized(_this));\n    _this.onExit = _this.onExit.bind(_assertThisInitialized(_this));\n    _this.onExited = _this.onExited.bind(_assertThisInitialized(_this));\n    _this.attributeSelector = UniqueComponentId();\n    _this.overlayRef = /*#__PURE__*/React.createRef();\n    return _this;\n  }\n\n  _createClass(OverlayPanel, [{\n    key: \"bindDocumentClickListener\",\n    value: function bindDocumentClickListener() {\n      var _this2 = this;\n\n      if (!this.documentClickListener && this.props.dismissable) {\n        this.documentClickListener = function (event) {\n          if (!_this2.isPanelClicked && _this2.isOutsideClicked(event.target)) {\n            _this2.hide();\n          }\n\n          _this2.isPanelClicked = false;\n        };\n\n        document.addEventListener('click', this.documentClickListener);\n      }\n    }\n  }, {\n    key: \"unbindDocumentClickListener\",\n    value: function unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        document.removeEventListener('click', this.documentClickListener);\n        this.documentClickListener = null;\n      }\n    }\n  }, {\n    key: \"bindScrollListener\",\n    value: function bindScrollListener() {\n      var _this3 = this;\n\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, function () {\n          if (_this3.state.visible) {\n            _this3.hide();\n          }\n        });\n      }\n\n      this.scrollHandler.bindScrollListener();\n    }\n  }, {\n    key: \"unbindScrollListener\",\n    value: function unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n  }, {\n    key: \"bindResizeListener\",\n    value: function bindResizeListener() {\n      var _this4 = this;\n\n      if (!this.resizeListener) {\n        this.resizeListener = function () {\n          if (_this4.state.visible && !DomHandler.isAndroid()) {\n            _this4.hide();\n          }\n        };\n\n        window.addEventListener('resize', this.resizeListener);\n      }\n    }\n  }, {\n    key: \"unbindResizeListener\",\n    value: function unbindResizeListener() {\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n        this.resizeListener = null;\n      }\n    }\n  }, {\n    key: \"isOutsideClicked\",\n    value: function isOutsideClicked(target) {\n      return this.overlayRef && this.overlayRef.current && !(this.overlayRef.current.isSameNode(target) || this.overlayRef.current.contains(target));\n    }\n  }, {\n    key: \"hasTargetChanged\",\n    value: function hasTargetChanged(event, target) {\n      return this.target != null && this.target !== (target || event.currentTarget || event.target);\n    }\n  }, {\n    key: \"onCloseClick\",\n    value: function onCloseClick(event) {\n      this.hide();\n      event.preventDefault();\n    }\n  }, {\n    key: \"onPanelClick\",\n    value: function onPanelClick(event) {\n      this.isPanelClicked = true;\n      OverlayService.emit('overlay-click', {\n        originalEvent: event,\n        target: this.target\n      });\n    }\n  }, {\n    key: \"onContentClick\",\n    value: function onContentClick() {\n      this.isPanelClicked = true;\n    }\n  }, {\n    key: \"toggle\",\n    value: function toggle(event, target) {\n      var _this5 = this;\n\n      if (this.state.visible) {\n        this.hide();\n\n        if (this.hasTargetChanged(event, target)) {\n          this.target = target || event.currentTarget || event.target;\n          setTimeout(function () {\n            _this5.show(event, _this5.target);\n          }, 200);\n        }\n      } else {\n        this.show(event, target);\n      }\n    }\n  }, {\n    key: \"show\",\n    value: function show(event, target) {\n      var _this6 = this;\n\n      this.target = target || event.currentTarget || event.target;\n\n      if (this.state.visible) {\n        this.align();\n      } else {\n        this.setState({\n          visible: true\n        }, function () {\n          _this6.overlayEventListener = function (e) {\n            if (!_this6.isOutsideClicked(e.target)) {\n              _this6.isPanelClicked = true;\n            }\n          };\n\n          OverlayService.on('overlay-click', _this6.overlayEventListener);\n        });\n      }\n    }\n  }, {\n    key: \"hide\",\n    value: function hide() {\n      var _this7 = this;\n\n      this.setState({\n        visible: false\n      }, function () {\n        OverlayService.off('overlay-click', _this7.overlayEventListener);\n        _this7.overlayEventListener = null;\n      });\n    }\n  }, {\n    key: \"onEnter\",\n    value: function onEnter() {\n      ZIndexUtils.set('overlay', this.overlayRef.current);\n      this.overlayRef.current.setAttribute(this.attributeSelector, '');\n      this.align();\n    }\n  }, {\n    key: \"onEntered\",\n    value: function onEntered() {\n      this.bindDocumentClickListener();\n      this.bindScrollListener();\n      this.bindResizeListener();\n      this.props.onShow && this.props.onShow();\n    }\n  }, {\n    key: \"onExit\",\n    value: function onExit() {\n      this.unbindDocumentClickListener();\n      this.unbindScrollListener();\n      this.unbindResizeListener();\n    }\n  }, {\n    key: \"onExited\",\n    value: function onExited() {\n      ZIndexUtils.clear(this.overlayRef.current);\n      this.props.onHide && this.props.onHide();\n    }\n  }, {\n    key: \"align\",\n    value: function align() {\n      if (this.target) {\n        DomHandler.absolutePosition(this.overlayRef.current, this.target);\n        var containerOffset = DomHandler.getOffset(this.overlayRef.current);\n        var targetOffset = DomHandler.getOffset(this.target);\n        var arrowLeft = 0;\n\n        if (containerOffset.left < targetOffset.left) {\n          arrowLeft = targetOffset.left - containerOffset.left;\n        }\n\n        this.overlayRef.current.style.setProperty('--overlayArrowLeft', \"\".concat(arrowLeft, \"px\"));\n\n        if (containerOffset.top < targetOffset.top) {\n          DomHandler.addClass(this.overlayRef.current, 'p-overlaypanel-flipped');\n        }\n      }\n    }\n  }, {\n    key: \"createStyle\",\n    value: function createStyle() {\n      if (!this.styleElement) {\n        this.styleElement = document.createElement('style');\n        document.head.appendChild(this.styleElement);\n        var innerHTML = '';\n\n        for (var breakpoint in this.props.breakpoints) {\n          innerHTML += \"\\n                    @media screen and (max-width: \".concat(breakpoint, \") {\\n                        .p-overlaypanel[\").concat(this.attributeSelector, \"] {\\n                            width: \").concat(this.props.breakpoints[breakpoint], \" !important;\\n                        }\\n                    }\\n                \");\n        }\n\n        this.styleElement.innerHTML = innerHTML;\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.props.breakpoints) {\n        this.createStyle();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unbindDocumentClickListener();\n      this.unbindResizeListener();\n\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n\n      if (this.styleElement) {\n        document.head.removeChild(this.styleElement);\n        this.styleElement = null;\n      }\n\n      if (this.overlayEventListener) {\n        OverlayService.off('overlay-click', this.overlayEventListener);\n        this.overlayEventListener = null;\n      }\n\n      ZIndexUtils.clear(this.overlayRef.current);\n    }\n  }, {\n    key: \"renderCloseIcon\",\n    value: function renderCloseIcon() {\n      if (this.props.showCloseIcon) {\n        return /*#__PURE__*/React.createElement(\"button\", {\n          type: \"button\",\n          className: \"p-overlaypanel-close p-link\",\n          onClick: this.onCloseClick,\n          \"aria-label\": this.props.ariaCloseLabel\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: \"p-overlaypanel-close-icon pi pi-times\"\n        }), /*#__PURE__*/React.createElement(Ripple, null));\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderElement\",\n    value: function renderElement() {\n      var className = classNames('p-overlaypanel p-component', this.props.className);\n      var closeIcon = this.renderCloseIcon();\n      return /*#__PURE__*/React.createElement(CSSTransition, {\n        nodeRef: this.overlayRef,\n        classNames: \"p-overlaypanel\",\n        in: this.state.visible,\n        timeout: {\n          enter: 120,\n          exit: 100\n        },\n        options: this.props.transitionOptions,\n        unmountOnExit: true,\n        onEnter: this.onEnter,\n        onEntered: this.onEntered,\n        onExit: this.onExit,\n        onExited: this.onExited\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.overlayRef,\n        id: this.props.id,\n        className: className,\n        style: this.props.style,\n        onClick: this.onPanelClick\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-overlaypanel-content\",\n        onClick: this.onContentClick,\n        onMouseDown: this.onContentClick\n      }, this.props.children), closeIcon));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var element = this.renderElement();\n      return /*#__PURE__*/React.createElement(Portal, {\n        element: element,\n        appendTo: this.props.appendTo\n      });\n    }\n  }]);\n\n  return OverlayPanel;\n}(Component);\n\n_defineProperty(OverlayPanel, \"defaultProps\", {\n  id: null,\n  dismissable: true,\n  showCloseIcon: false,\n  style: null,\n  className: null,\n  appendTo: null,\n  breakpoints: null,\n  ariaCloseLabel: 'close',\n  transitionOptions: null,\n  onShow: null,\n  onHide: null\n});\n\nexport { OverlayPanel };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,iBAAiB,EAAEC,6BAA6B,EAAEC,UAAU,EAAEC,cAAc,EAAEC,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AAEtK,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAC3D;AACF;AAEA,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAC5D,OAAOhB,WAAW;AACpB;AAEA,SAASkB,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7BF,eAAe,GAAGV,MAAM,CAACa,cAAc,IAAI,SAASH,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACxED,CAAC,CAACG,SAAS,GAAGF,CAAC;IACf,OAAOD,CAAC;EACV,CAAC;EAED,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B;AAEA,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAI3B,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEA0B,QAAQ,CAACV,SAAS,GAAGN,MAAM,CAACkB,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACX,SAAS,EAAE;IACrEa,WAAW,EAAE;MACXC,KAAK,EAAEJ,QAAQ;MACfjB,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAImB,UAAU,EAAEP,eAAe,CAACM,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASI,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACH,WAAW,KAAKI,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACjB,SAAS,GAAG,QAAQ,GAAG,OAAOgB,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASG,0BAA0BA,CAACjB,IAAI,EAAEkB,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAKL,OAAO,CAACK,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACtE,OAAOA,IAAI;EACb;EAEA,OAAOnB,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASmB,eAAeA,CAAChB,CAAC,EAAE;EAC1BgB,eAAe,GAAG3B,MAAM,CAACa,cAAc,GAAGb,MAAM,CAAC4B,cAAc,GAAG,SAASD,eAAeA,CAAChB,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAId,MAAM,CAAC4B,cAAc,CAACjB,CAAC,CAAC;EAChD,CAAC;EACD,OAAOgB,eAAe,CAAChB,CAAC,CAAC;AAC3B;AAEA,SAASkB,eAAeA,CAACP,GAAG,EAAEpB,GAAG,EAAEkB,KAAK,EAAE;EACxC,IAAIlB,GAAG,IAAIoB,GAAG,EAAE;IACdtB,MAAM,CAACC,cAAc,CAACqB,GAAG,EAAEpB,GAAG,EAAE;MAC9BkB,KAAK,EAAEA,KAAK;MACZvB,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLuB,GAAG,CAACpB,GAAG,CAAC,GAAGkB,KAAK;EAClB;EAEA,OAAOE,GAAG;AACZ;AAEA,SAASQ,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGR,eAAe,CAACI,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAGV,eAAe,CAAC,IAAI,CAAC,CAACR,WAAW;MAAEiB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEK,SAAS,EAAEH,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAACM,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IAAE;IAAE,OAAOf,0BAA0B,CAAC,IAAI,EAAEW,MAAM,CAAC;EAAE,CAAC;AAAE;AAExa,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOK,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACG,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACtC,SAAS,CAACuC,OAAO,CAACnB,IAAI,CAACY,OAAO,CAACC,SAAS,CAACK,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,IAAIC,YAAY,GAAG,aAAa,UAAUC,UAAU,EAAE;EACpDjC,SAAS,CAACgC,YAAY,EAAEC,UAAU,CAAC;EAEnC,IAAIC,MAAM,GAAGnB,YAAY,CAACiB,YAAY,CAAC;EAEvC,SAASA,YAAYA,CAACtD,KAAK,EAAE;IAC3B,IAAIyD,KAAK;IAET/D,eAAe,CAAC,IAAI,EAAE4D,YAAY,CAAC;IAEnCG,KAAK,GAAGD,MAAM,CAACvB,IAAI,CAAC,IAAI,EAAEjC,KAAK,CAAC;IAChCyD,KAAK,CAACC,KAAK,GAAG;MACZC,OAAO,EAAE;IACX,CAAC;IACDF,KAAK,CAACG,YAAY,GAAGH,KAAK,CAACG,YAAY,CAACC,IAAI,CAAC/C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IAC3EA,KAAK,CAACK,YAAY,GAAGL,KAAK,CAACK,YAAY,CAACD,IAAI,CAAC/C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IAC3EA,KAAK,CAACM,OAAO,GAAGN,KAAK,CAACM,OAAO,CAACF,IAAI,CAAC/C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IACjEA,KAAK,CAACO,SAAS,GAAGP,KAAK,CAACO,SAAS,CAACH,IAAI,CAAC/C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IACrEA,KAAK,CAACQ,MAAM,GAAGR,KAAK,CAACQ,MAAM,CAACJ,IAAI,CAAC/C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IAC/DA,KAAK,CAACS,QAAQ,GAAGT,KAAK,CAACS,QAAQ,CAACL,IAAI,CAAC/C,sBAAsB,CAAC2C,KAAK,CAAC,CAAC;IACnEA,KAAK,CAACU,iBAAiB,GAAGlF,iBAAiB,CAAC,CAAC;IAC7CwE,KAAK,CAACW,UAAU,GAAG,aAAarF,KAAK,CAACsF,SAAS,CAAC,CAAC;IACjD,OAAOZ,KAAK;EACd;EAEA/C,YAAY,CAAC4C,YAAY,EAAE,CAAC;IAC1B7C,GAAG,EAAE,2BAA2B;IAChCkB,KAAK,EAAE,SAAS2C,yBAAyBA,CAAA,EAAG;MAC1C,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACC,qBAAqB,IAAI,IAAI,CAACxE,KAAK,CAACyE,WAAW,EAAE;QACzD,IAAI,CAACD,qBAAqB,GAAG,UAAUE,KAAK,EAAE;UAC5C,IAAI,CAACH,MAAM,CAACI,cAAc,IAAIJ,MAAM,CAACK,gBAAgB,CAACF,KAAK,CAAC3E,MAAM,CAAC,EAAE;YACnEwE,MAAM,CAACM,IAAI,CAAC,CAAC;UACf;UAEAN,MAAM,CAACI,cAAc,GAAG,KAAK;QAC/B,CAAC;QAEDG,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACP,qBAAqB,CAAC;MAChE;IACF;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,6BAA6B;IAClCkB,KAAK,EAAE,SAASqD,2BAA2BA,CAAA,EAAG;MAC5C,IAAI,IAAI,CAACR,qBAAqB,EAAE;QAC9BM,QAAQ,CAACG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACT,qBAAqB,CAAC;QACjE,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACnC;IACF;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,oBAAoB;IACzBkB,KAAK,EAAE,SAASuD,kBAAkBA,CAAA,EAAG;MACnC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;QACvB,IAAI,CAACA,aAAa,GAAG,IAAIlG,6BAA6B,CAAC,IAAI,CAACa,MAAM,EAAE,YAAY;UAC9E,IAAIoF,MAAM,CAACzB,KAAK,CAACC,OAAO,EAAE;YACxBwB,MAAM,CAACN,IAAI,CAAC,CAAC;UACf;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAACO,aAAa,CAACF,kBAAkB,CAAC,CAAC;IACzC;EACF,CAAC,EAAE;IACDzE,GAAG,EAAE,sBAAsB;IAC3BkB,KAAK,EAAE,SAAS0D,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACD,aAAa,EAAE;QACtB,IAAI,CAACA,aAAa,CAACC,oBAAoB,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,EAAE;IACD5E,GAAG,EAAE,oBAAoB;IACzBkB,KAAK,EAAE,SAAS2D,kBAAkBA,CAAA,EAAG;MACnC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;QACxB,IAAI,CAACA,cAAc,GAAG,YAAY;UAChC,IAAID,MAAM,CAAC7B,KAAK,CAACC,OAAO,IAAI,CAACxE,UAAU,CAACsG,SAAS,CAAC,CAAC,EAAE;YACnDF,MAAM,CAACV,IAAI,CAAC,CAAC;UACf;QACF,CAAC;QAEDa,MAAM,CAACX,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACS,cAAc,CAAC;MACxD;IACF;EACF,CAAC,EAAE;IACD/E,GAAG,EAAE,sBAAsB;IAC3BkB,KAAK,EAAE,SAASgE,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACH,cAAc,EAAE;QACvBE,MAAM,CAACT,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACO,cAAc,CAAC;QACzD,IAAI,CAACA,cAAc,GAAG,IAAI;MAC5B;IACF;EACF,CAAC,EAAE;IACD/E,GAAG,EAAE,kBAAkB;IACvBkB,KAAK,EAAE,SAASiD,gBAAgBA,CAAC7E,MAAM,EAAE;MACvC,OAAO,IAAI,CAACqE,UAAU,IAAI,IAAI,CAACA,UAAU,CAACwB,OAAO,IAAI,EAAE,IAAI,CAACxB,UAAU,CAACwB,OAAO,CAACC,UAAU,CAAC9F,MAAM,CAAC,IAAI,IAAI,CAACqE,UAAU,CAACwB,OAAO,CAACE,QAAQ,CAAC/F,MAAM,CAAC,CAAC;IAChJ;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,kBAAkB;IACvBkB,KAAK,EAAE,SAASoE,gBAAgBA,CAACrB,KAAK,EAAE3E,MAAM,EAAE;MAC9C,OAAO,IAAI,CAACA,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,MAAMA,MAAM,IAAI2E,KAAK,CAACsB,aAAa,IAAItB,KAAK,CAAC3E,MAAM,CAAC;IAC/F;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAASiC,YAAYA,CAACc,KAAK,EAAE;MAClC,IAAI,CAACG,IAAI,CAAC,CAAC;MACXH,KAAK,CAACuB,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDxF,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE,SAASmC,YAAYA,CAACY,KAAK,EAAE;MAClC,IAAI,CAACC,cAAc,GAAG,IAAI;MAC1BvF,cAAc,CAAC8G,IAAI,CAAC,eAAe,EAAE;QACnCC,aAAa,EAAEzB,KAAK;QACpB3E,MAAM,EAAE,IAAI,CAACA;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,gBAAgB;IACrBkB,KAAK,EAAE,SAASyE,cAAcA,CAAA,EAAG;MAC/B,IAAI,CAACzB,cAAc,GAAG,IAAI;IAC5B;EACF,CAAC,EAAE;IACDlE,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAAS0E,MAAMA,CAAC3B,KAAK,EAAE3E,MAAM,EAAE;MACpC,IAAIuG,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAC5C,KAAK,CAACC,OAAO,EAAE;QACtB,IAAI,CAACkB,IAAI,CAAC,CAAC;QAEX,IAAI,IAAI,CAACkB,gBAAgB,CAACrB,KAAK,EAAE3E,MAAM,CAAC,EAAE;UACxC,IAAI,CAACA,MAAM,GAAGA,MAAM,IAAI2E,KAAK,CAACsB,aAAa,IAAItB,KAAK,CAAC3E,MAAM;UAC3DwG,UAAU,CAAC,YAAY;YACrBD,MAAM,CAACE,IAAI,CAAC9B,KAAK,EAAE4B,MAAM,CAACvG,MAAM,CAAC;UACnC,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,MAAM;QACL,IAAI,CAACyG,IAAI,CAAC9B,KAAK,EAAE3E,MAAM,CAAC;MAC1B;IACF;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,MAAM;IACXkB,KAAK,EAAE,SAAS6E,IAAIA,CAAC9B,KAAK,EAAE3E,MAAM,EAAE;MAClC,IAAI0G,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC1G,MAAM,GAAGA,MAAM,IAAI2E,KAAK,CAACsB,aAAa,IAAItB,KAAK,CAAC3E,MAAM;MAE3D,IAAI,IAAI,CAAC2D,KAAK,CAACC,OAAO,EAAE;QACtB,IAAI,CAAC+C,KAAK,CAAC,CAAC;MACd,CAAC,MAAM;QACL,IAAI,CAACC,QAAQ,CAAC;UACZhD,OAAO,EAAE;QACX,CAAC,EAAE,YAAY;UACb8C,MAAM,CAACG,oBAAoB,GAAG,UAAUvD,CAAC,EAAE;YACzC,IAAI,CAACoD,MAAM,CAAC7B,gBAAgB,CAACvB,CAAC,CAACtD,MAAM,CAAC,EAAE;cACtC0G,MAAM,CAAC9B,cAAc,GAAG,IAAI;YAC9B;UACF,CAAC;UAEDvF,cAAc,CAACyH,EAAE,CAAC,eAAe,EAAEJ,MAAM,CAACG,oBAAoB,CAAC;QACjE,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDnG,GAAG,EAAE,MAAM;IACXkB,KAAK,EAAE,SAASkD,IAAIA,CAAA,EAAG;MACrB,IAAIiC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACH,QAAQ,CAAC;QACZhD,OAAO,EAAE;MACX,CAAC,EAAE,YAAY;QACbvE,cAAc,CAAC2H,GAAG,CAAC,eAAe,EAAED,MAAM,CAACF,oBAAoB,CAAC;QAChEE,MAAM,CAACF,oBAAoB,GAAG,IAAI;MACpC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDnG,GAAG,EAAE,SAAS;IACdkB,KAAK,EAAE,SAASoC,OAAOA,CAAA,EAAG;MACxB1E,WAAW,CAAC2H,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC5C,UAAU,CAACwB,OAAO,CAAC;MACnD,IAAI,CAACxB,UAAU,CAACwB,OAAO,CAACqB,YAAY,CAAC,IAAI,CAAC9C,iBAAiB,EAAE,EAAE,CAAC;MAChE,IAAI,CAACuC,KAAK,CAAC,CAAC;IACd;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,WAAW;IAChBkB,KAAK,EAAE,SAASqC,SAASA,CAAA,EAAG;MAC1B,IAAI,CAACM,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAACY,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACI,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACtF,KAAK,CAACkH,MAAM,IAAI,IAAI,CAAClH,KAAK,CAACkH,MAAM,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE;IACDzG,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAASsC,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACe,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACK,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACM,oBAAoB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE;IACDlF,GAAG,EAAE,UAAU;IACfkB,KAAK,EAAE,SAASuC,QAAQA,CAAA,EAAG;MACzB7E,WAAW,CAAC8H,KAAK,CAAC,IAAI,CAAC/C,UAAU,CAACwB,OAAO,CAAC;MAC1C,IAAI,CAAC5F,KAAK,CAACoH,MAAM,IAAI,IAAI,CAACpH,KAAK,CAACoH,MAAM,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,OAAO;IACZkB,KAAK,EAAE,SAAS+E,KAAKA,CAAA,EAAG;MACtB,IAAI,IAAI,CAAC3G,MAAM,EAAE;QACfZ,UAAU,CAACkI,gBAAgB,CAAC,IAAI,CAACjD,UAAU,CAACwB,OAAO,EAAE,IAAI,CAAC7F,MAAM,CAAC;QACjE,IAAIuH,eAAe,GAAGnI,UAAU,CAACoI,SAAS,CAAC,IAAI,CAACnD,UAAU,CAACwB,OAAO,CAAC;QACnE,IAAI4B,YAAY,GAAGrI,UAAU,CAACoI,SAAS,CAAC,IAAI,CAACxH,MAAM,CAAC;QACpD,IAAI0H,SAAS,GAAG,CAAC;QAEjB,IAAIH,eAAe,CAACI,IAAI,GAAGF,YAAY,CAACE,IAAI,EAAE;UAC5CD,SAAS,GAAGD,YAAY,CAACE,IAAI,GAAGJ,eAAe,CAACI,IAAI;QACtD;QAEA,IAAI,CAACtD,UAAU,CAACwB,OAAO,CAAC+B,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,IAAI,CAAC,CAAC;QAE3F,IAAIH,eAAe,CAACQ,GAAG,GAAGN,YAAY,CAACM,GAAG,EAAE;UAC1C3I,UAAU,CAAC4I,QAAQ,CAAC,IAAI,CAAC3D,UAAU,CAACwB,OAAO,EAAE,wBAAwB,CAAC;QACxE;MACF;IACF;EACF,CAAC,EAAE;IACDnF,GAAG,EAAE,aAAa;IAClBkB,KAAK,EAAE,SAASqG,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACtB,IAAI,CAACA,YAAY,GAAGnD,QAAQ,CAACoD,aAAa,CAAC,OAAO,CAAC;QACnDpD,QAAQ,CAACqD,IAAI,CAACC,WAAW,CAAC,IAAI,CAACH,YAAY,CAAC;QAC5C,IAAII,SAAS,GAAG,EAAE;QAElB,KAAK,IAAIC,UAAU,IAAI,IAAI,CAACtI,KAAK,CAACuI,WAAW,EAAE;UAC7CF,SAAS,IAAI,sDAAsD,CAACR,MAAM,CAACS,UAAU,EAAE,+CAA+C,CAAC,CAACT,MAAM,CAAC,IAAI,CAAC1D,iBAAiB,EAAE,0CAA0C,CAAC,CAAC0D,MAAM,CAAC,IAAI,CAAC7H,KAAK,CAACuI,WAAW,CAACD,UAAU,CAAC,EAAE,kFAAkF,CAAC;QACnV;QAEA,IAAI,CAACL,YAAY,CAACI,SAAS,GAAGA,SAAS;MACzC;IACF;EACF,CAAC,EAAE;IACD5H,GAAG,EAAE,mBAAmB;IACxBkB,KAAK,EAAE,SAAS6G,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACxI,KAAK,CAACuI,WAAW,EAAE;QAC1B,IAAI,CAACP,WAAW,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDvH,GAAG,EAAE,sBAAsB;IAC3BkB,KAAK,EAAE,SAAS8G,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACzD,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACW,oBAAoB,CAAC,CAAC;MAE3B,IAAI,IAAI,CAACP,aAAa,EAAE;QACtB,IAAI,CAACA,aAAa,CAACsD,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACtD,aAAa,GAAG,IAAI;MAC3B;MAEA,IAAI,IAAI,CAAC6C,YAAY,EAAE;QACrBnD,QAAQ,CAACqD,IAAI,CAACQ,WAAW,CAAC,IAAI,CAACV,YAAY,CAAC;QAC5C,IAAI,CAACA,YAAY,GAAG,IAAI;MAC1B;MAEA,IAAI,IAAI,CAACrB,oBAAoB,EAAE;QAC7BxH,cAAc,CAAC2H,GAAG,CAAC,eAAe,EAAE,IAAI,CAACH,oBAAoB,CAAC;QAC9D,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAClC;MAEAvH,WAAW,CAAC8H,KAAK,CAAC,IAAI,CAAC/C,UAAU,CAACwB,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE;IACDnF,GAAG,EAAE,iBAAiB;IACtBkB,KAAK,EAAE,SAASiH,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAAC5I,KAAK,CAAC6I,aAAa,EAAE;QAC5B,OAAO,aAAa9J,KAAK,CAACmJ,aAAa,CAAC,QAAQ,EAAE;UAChDY,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE,6BAA6B;UACxCC,OAAO,EAAE,IAAI,CAACpF,YAAY;UAC1B,YAAY,EAAE,IAAI,CAAC5D,KAAK,CAACiJ;QAC3B,CAAC,EAAE,aAAalK,KAAK,CAACmJ,aAAa,CAAC,MAAM,EAAE;UAC1Ca,SAAS,EAAE;QACb,CAAC,CAAC,EAAE,aAAahK,KAAK,CAACmJ,aAAa,CAAC5I,MAAM,EAAE,IAAI,CAAC,CAAC;MACrD;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDmB,GAAG,EAAE,eAAe;IACpBkB,KAAK,EAAE,SAASuH,aAAaA,CAAA,EAAG;MAC9B,IAAIH,SAAS,GAAGxJ,UAAU,CAAC,4BAA4B,EAAE,IAAI,CAACS,KAAK,CAAC+I,SAAS,CAAC;MAC9E,IAAII,SAAS,GAAG,IAAI,CAACP,eAAe,CAAC,CAAC;MACtC,OAAO,aAAa7J,KAAK,CAACmJ,aAAa,CAAC1I,aAAa,EAAE;QACrD4J,OAAO,EAAE,IAAI,CAAChF,UAAU;QACxB7E,UAAU,EAAE,gBAAgB;QAC5B8J,EAAE,EAAE,IAAI,CAAC3F,KAAK,CAACC,OAAO;QACtB2F,OAAO,EAAE;UACPC,KAAK,EAAE,GAAG;UACVC,IAAI,EAAE;QACR,CAAC;QACDC,OAAO,EAAE,IAAI,CAACzJ,KAAK,CAAC0J,iBAAiB;QACrCC,aAAa,EAAE,IAAI;QACnB5F,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAE,aAAanF,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;QACzC0B,GAAG,EAAE,IAAI,CAACxF,UAAU;QACpByF,EAAE,EAAE,IAAI,CAAC7J,KAAK,CAAC6J,EAAE;QACjBd,SAAS,EAAEA,SAAS;QACpBpB,KAAK,EAAE,IAAI,CAAC3H,KAAK,CAAC2H,KAAK;QACvBqB,OAAO,EAAE,IAAI,CAAClF;MAChB,CAAC,EAAE,aAAa/E,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;QACzCa,SAAS,EAAE,wBAAwB;QACnCC,OAAO,EAAE,IAAI,CAAC5C,cAAc;QAC5B0D,WAAW,EAAE,IAAI,CAAC1D;MACpB,CAAC,EAAE,IAAI,CAACpG,KAAK,CAAC+J,QAAQ,CAAC,EAAEZ,SAAS,CAAC,CAAC;IACtC;EACF,CAAC,EAAE;IACD1I,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,SAASqI,MAAMA,CAAA,EAAG;MACvB,IAAIC,OAAO,GAAG,IAAI,CAACf,aAAa,CAAC,CAAC;MAClC,OAAO,aAAanK,KAAK,CAACmJ,aAAa,CAACzI,MAAM,EAAE;QAC9CwK,OAAO,EAAEA,OAAO;QAChBC,QAAQ,EAAE,IAAI,CAAClK,KAAK,CAACkK;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EAEH,OAAO5G,YAAY;AACrB,CAAC,CAACtE,SAAS,CAAC;AAEZoD,eAAe,CAACkB,YAAY,EAAE,cAAc,EAAE;EAC5CuG,EAAE,EAAE,IAAI;EACRpF,WAAW,EAAE,IAAI;EACjBoE,aAAa,EAAE,KAAK;EACpBlB,KAAK,EAAE,IAAI;EACXoB,SAAS,EAAE,IAAI;EACfmB,QAAQ,EAAE,IAAI;EACd3B,WAAW,EAAE,IAAI;EACjBU,cAAc,EAAE,OAAO;EACvBS,iBAAiB,EAAE,IAAI;EACvBxC,MAAM,EAAE,IAAI;EACZE,MAAM,EAAE;AACV,CAAC,CAAC;AAEF,SAAS9D,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}