{"name": "filing-cabinet", "version": "4.2.0", "description": "Find files based on partial paths", "main": "index.js", "files": ["bin/cli.js", "index.js"], "bin": {"filing-cabinet": "bin/cli.js"}, "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-filing-cabinet.git"}, "keywords": ["lookup", "es6", "amd", "commonjs", "sass", "less", "stylus", "partial", "resolution", "paths"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-filing-cabinet/issues"}, "homepage": "https://github.com/dependents/node-filing-cabinet", "engines": {"node": ">=14"}, "dependencies": {"app-module-path": "^2.2.0", "commander": "^10.0.1", "enhanced-resolve": "^5.14.1", "is-relative-path": "^1.0.2", "module-definition": "^5.0.1", "module-lookup-amd": "^8.0.5", "resolve": "^1.22.3", "resolve-dependency-path": "^3.0.2", "sass-lookup": "^5.0.1", "stylus-lookup": "^5.0.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.4"}, "devDependencies": {"c8": "^7.13.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "max-nested-callbacks": ["error", 6], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-string-slice": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}