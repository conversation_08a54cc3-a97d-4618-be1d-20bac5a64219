{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useForceUpdate() {\n  var _React$useReducer = React.useReducer(function (x) {\n      return x + 1;\n    }, 0),\n    _React$useReducer2 = _slicedToArray(_React$useReducer, 2),\n    forceUpdate = _React$useReducer2[1];\n  return forceUpdate;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useForceUpdate", "_React$useReducer", "useReducer", "x", "_React$useReducer2", "forceUpdate"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/hooks/useForceUpdate.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useForceUpdate() {\n  var _React$useReducer = React.useReducer(function (x) {\n    return x + 1;\n  }, 0),\n      _React$useReducer2 = _slicedToArray(_React$useReducer, 2),\n      forceUpdate = _React$useReducer2[1];\n\n  return forceUpdate;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,IAAIC,iBAAiB,GAAGF,KAAK,CAACG,UAAU,CAAC,UAAUC,CAAC,EAAE;MACpD,OAAOA,CAAC,GAAG,CAAC;IACd,CAAC,EAAE,CAAC,CAAC;IACDC,kBAAkB,GAAGN,cAAc,CAACG,iBAAiB,EAAE,CAAC,CAAC;IACzDI,WAAW,GAAGD,kBAAkB,CAAC,CAAC,CAAC;EAEvC,OAAOC,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}