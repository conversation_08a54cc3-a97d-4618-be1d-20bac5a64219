/**
 * @license Copyright 2020 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

import {Audit} from './audit.js';
import * as i18n from '../lib/i18n/i18n.js';

const UIStrings = {
  /** Descriptive title of a diagnostic audit that provides up to the top five elements contributing to Cumulative Layout Shift. */
  title: 'Avoid large layout shifts',
  /** Description of a diagnostic audit that provides up to the top five elements contributing to Cumulative Layout Shift. The last sentence starting with 'Learn' becomes link text to additional documentation. */
  description: 'These DOM elements contribute most to the CLS of the page. [Learn how to improve CLS](https://web.dev/optimize-cls/)',
  /**  Label for a column in a data table; entries in this column will be the amount that the corresponding element contributes to the total CLS metric score. */
  columnContribution: 'CLS Contribution',
};

const str_ = i18n.createIcuMessageFn(import.meta.url, UIStrings);

class LayoutShiftElements extends Audit {
  /**
   * @return {LH.Audit.Meta}
   */
  static get meta() {
    return {
      id: 'layout-shift-elements',
      title: str_(UIStrings.title),
      description: str_(UIStrings.description),
      scoreDisplayMode: Audit.SCORING_MODES.INFORMATIVE,
      requiredArtifacts: ['traces', 'TraceElements'],
    };
  }

  /**
   * @param {LH.Artifacts} artifacts
   * @return {LH.Audit.Product}
   */
  static audit(artifacts) {
    const clsElements = artifacts.TraceElements
      .filter(element => element.traceEventType === 'layout-shift');

    const clsElementData = clsElements.map(element => {
      return {
        node: Audit.makeNodeItem(element.node),
        score: element.score,
      };
    });

    /** @type {LH.Audit.Details.Table['headings']} */
    const headings = [
      {key: 'node', valueType: 'node', label: str_(i18n.UIStrings.columnElement)},
      {key: 'score', valueType: 'numeric',
        granularity: 0.001, label: str_(UIStrings.columnContribution)},
    ];

    const details = Audit.makeTableDetails(headings, clsElementData);
    let displayValue;
    if (clsElementData.length > 0) {
      displayValue = str_(i18n.UIStrings.displayValueElementsFound,
        {nodeCount: clsElementData.length});
    }

    return {
      score: 1,
      notApplicable: details.items.length === 0,
      displayValue,
      details,
    };
  }
}

export default LayoutShiftElements;
export {UIStrings};
