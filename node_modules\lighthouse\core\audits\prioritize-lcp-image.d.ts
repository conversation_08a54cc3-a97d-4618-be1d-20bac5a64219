export default PrioritizeLcpImage;
export type InitiatorType = LH.Crdp.Network.Initiator['type'] | 'redirect' | 'fallbackToMain';
export type InitiatorPath = Array<{
    url: string;
    initiatorType: InitiatorType;
}>;
/**
 * @typedef {LH.Crdp.Network.Initiator['type']|'redirect'|'fallbackToMain'} InitiatorType
 * @typedef {Array<{url: string, initiatorType: InitiatorType}>} InitiatorPath
 */
declare class PrioritizeLcpImage extends Audit {
    /**
     *
     * @param {LH.Artifacts.NetworkRequest} request
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @param {InitiatorPath} initiatorPath
     * @return {boolean}
     */
    static shouldPreloadRequest(request: LH.Artifacts.NetworkRequest, mainResource: LH.Artifacts.NetworkRequest, initiatorPath: InitiatorPath): boolean;
    /**
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @param {NetworkRequest} lcpRecord
     * @return {LH.Gatherer.Simulation.GraphNetworkNode|undefined}
     */
    static findLCPNode(graph: LH.Gatherer.Simulation.GraphNode, lcpRecord: NetworkRequest): LH.Gatherer.Simulation.GraphNetworkNode | undefined;
    /**
     * Get the initiator path starting with lcpRecord back to mainResource, inclusive.
     * Navigation redirects *to* the mainResource are not included.
     * Path returned will always be at least [lcpRecord, mainResource].
     * @param {NetworkRequest} lcpRecord
     * @param {NetworkRequest} mainResource
     * @return {InitiatorPath}
     */
    static getLcpInitiatorPath(lcpRecord: NetworkRequest, mainResource: NetworkRequest): InitiatorPath;
    /**
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @param {NetworkRequest|undefined} lcpRecord
     * @return {{lcpNodeToPreload?: LH.Gatherer.Simulation.GraphNetworkNode, initiatorPath?: InitiatorPath}}
     */
    static getLCPNodeToPreload(mainResource: LH.Artifacts.NetworkRequest, graph: LH.Gatherer.Simulation.GraphNode, lcpRecord: NetworkRequest | undefined): {
        lcpNodeToPreload?: import("../lib/dependency-graph/network-node.js").NetworkNode | undefined;
        initiatorPath?: InitiatorPath | undefined;
    };
    /**
     * Match the LCP event with the paint event to get the request of the image actually painted.
     * This could differ from the `ImageElement` associated with the nodeId if e.g. the LCP
     * was a pseudo-element associated with a node containing a smaller background-image.
     * @param {LH.Trace} trace
     * @param {LH.Artifacts.ProcessedNavigation} processedNavigation
     * @param {Array<NetworkRequest>} networkRecords
     * @return {NetworkRequest|undefined}
     */
    static getLcpRecord(trace: LH.Trace, processedNavigation: LH.Artifacts.ProcessedNavigation, networkRecords: Array<NetworkRequest>): NetworkRequest | undefined;
    /**
     * Computes the estimated effect of preloading the LCP image.
     * @param {LH.Artifacts.TraceElement} lcpElement
     * @param {LH.Gatherer.Simulation.GraphNetworkNode|undefined} lcpNode
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @param {LH.Gatherer.Simulation.Simulator} simulator
     * @return {{wastedMs: number, results: Array<{node: LH.Audit.Details.NodeValue, url: string, wastedMs: number}>}}
     */
    static computeWasteWithGraph(lcpElement: LH.Artifacts.TraceElement, lcpNode: LH.Gatherer.Simulation.GraphNetworkNode | undefined, graph: LH.Gatherer.Simulation.GraphNode, simulator: LH.Gatherer.Simulation.Simulator): {
        wastedMs: number;
        results: {
            node: LH.Audit.Details.NodeValue;
            url: string;
            wastedMs: number;
        }[];
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
}
import { Audit } from "./audit.js";
import { NetworkRequest } from "../lib/network-request.js";
import { ProcessedNavigation } from "../computed/processed-navigation.js";
//# sourceMappingURL=prioritize-lcp-image.d.ts.map