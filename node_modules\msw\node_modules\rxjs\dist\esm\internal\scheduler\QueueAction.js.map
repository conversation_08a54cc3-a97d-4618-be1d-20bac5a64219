{"version": 3, "file": "QueueAction.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/QueueAction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAM5C,MAAM,OAAO,WAAe,SAAQ,WAAc;IAChD,YAAsB,SAAyB,EAAY,IAAmD;QAC5G,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QADH,cAAS,GAAT,SAAS,CAAgB;QAAY,SAAI,GAAJ,IAAI,CAA+C;IAE9G,CAAC;IAEM,QAAQ,CAAC,KAAS,EAAE,QAAgB,CAAC;QAC1C,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,OAAO,CAAC,KAAQ,EAAE,KAAa;QACpC,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;IAES,cAAc,CAAC,SAAyB,EAAE,EAAgB,EAAE,QAAgB,CAAC;QAKrF,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;YACrE,OAAO,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;SACnD;QAGD,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAMtB,OAAO,CAAC,CAAC;IACX,CAAC;CACF"}