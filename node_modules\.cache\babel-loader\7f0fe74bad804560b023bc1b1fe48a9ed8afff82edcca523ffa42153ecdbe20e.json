{"ast": null, "code": "import { PresetColorTypes } from '../_util/colors'; // eslint-disable-next-line import/prefer-default-export\n\nexport function isPresetColor(color) {\n  return PresetColorTypes.indexOf(color) !== -1;\n}", "map": {"version": 3, "names": ["PresetColorTypes", "isPresetColor", "color", "indexOf"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/badge/utils.js"], "sourcesContent": ["import { PresetColorTypes } from '../_util/colors'; // eslint-disable-next-line import/prefer-default-export\n\nexport function isPresetColor(color) {\n  return PresetColorTypes.indexOf(color) !== -1;\n}"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB,CAAC,CAAC;;AAEpD,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAOF,gBAAgB,CAACG,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}