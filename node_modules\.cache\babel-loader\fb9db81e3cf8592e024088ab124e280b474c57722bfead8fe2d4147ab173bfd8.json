{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport CheckableTag from './CheckableTag';\nimport { ConfigContext } from '../config-provider';\nimport { PresetColorTypes, PresetStatusColorTypes } from '../_util/colors';\nimport Wave from '../_util/wave';\nvar PresetColorRegex = new RegExp(\"^(\".concat(PresetColorTypes.join('|'), \")(-inverse)?$\"));\nvar PresetStatusColorRegex = new RegExp(\"^(\".concat(PresetStatusColorTypes.join('|'), \")$\"));\nvar InternalTag = function InternalTag(_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    style = _a.style,\n    children = _a.children,\n    icon = _a.icon,\n    color = _a.color,\n    onClose = _a.onClose,\n    closeIcon = _a.closeIcon,\n    _a$closable = _a.closable,\n    closable = _a$closable === void 0 ? false : _a$closable,\n    props = __rest(_a, [\"prefixCls\", \"className\", \"style\", \"children\", \"icon\", \"color\", \"onClose\", \"closeIcon\", \"closable\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  React.useEffect(function () {\n    if ('visible' in props) {\n      setVisible(props.visible);\n    }\n  }, [props.visible]);\n  var isPresetColor = function isPresetColor() {\n    if (!color) {\n      return false;\n    }\n    return PresetColorRegex.test(color) || PresetStatusColorRegex.test(color);\n  };\n  var tagStyle = _extends({\n    backgroundColor: color && !isPresetColor() ? color : undefined\n  }, style);\n  var presetColor = isPresetColor();\n  var prefixCls = getPrefixCls('tag', customizePrefixCls);\n  var tagClassName = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(color), presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-color\"), color && !presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-hidden\"), !visible), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var handleCloseClick = function handleCloseClick(e) {\n    e.stopPropagation();\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n    if (e.defaultPrevented) {\n      return;\n    }\n    if (!('visible' in props)) {\n      setVisible(false);\n    }\n  };\n  var renderCloseIcon = function renderCloseIcon() {\n    if (closable) {\n      return closeIcon ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      }, closeIcon) : /*#__PURE__*/React.createElement(CloseOutlined, {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      });\n    }\n    return null;\n  };\n  var isNeedWave = 'onClick' in props || children && children.type === 'a';\n  var tagProps = omit(props, ['visible']);\n  var iconNode = icon || null;\n  var kids = iconNode ? /*#__PURE__*/React.createElement(React.Fragment, null, iconNode, /*#__PURE__*/React.createElement(\"span\", null, children)) : children;\n  var tagNode = /*#__PURE__*/React.createElement(\"span\", _extends({}, tagProps, {\n    ref: ref,\n    className: tagClassName,\n    style: tagStyle\n  }), kids, renderCloseIcon());\n  return isNeedWave ? /*#__PURE__*/React.createElement(Wave, null, tagNode) : tagNode;\n};\nvar Tag = /*#__PURE__*/React.forwardRef(InternalTag);\nTag.displayName = 'Tag';\nTag.CheckableTag = CheckableTag;\nexport default Tag;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "omit", "CloseOutlined", "CheckableTag", "ConfigContext", "PresetColorTypes", "PresetStatusColorTypes", "Wave", "PresetColorRegex", "RegExp", "concat", "join", "PresetStatusColorRegex", "InternalTag", "_a", "ref", "_classNames", "customizePrefixCls", "prefixCls", "className", "style", "children", "icon", "color", "onClose", "closeIcon", "_a$closable", "closable", "props", "_React$useContext", "useContext", "getPrefixCls", "direction", "_React$useState", "useState", "_React$useState2", "visible", "setVisible", "useEffect", "isPresetColor", "test", "tagStyle", "backgroundColor", "undefined", "presetColor", "tagClassName", "handleCloseClick", "stopPropagation", "defaultPrevented", "renderCloseIcon", "createElement", "onClick", "isNeedWave", "type", "tagProps", "iconNode", "kids", "Fragment", "tagNode", "Tag", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/tag/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport CheckableTag from './CheckableTag';\nimport { ConfigContext } from '../config-provider';\nimport { PresetColorTypes, PresetStatusColorTypes } from '../_util/colors';\nimport Wave from '../_util/wave';\nvar PresetColorRegex = new RegExp(\"^(\".concat(PresetColorTypes.join('|'), \")(-inverse)?$\"));\nvar PresetStatusColorRegex = new RegExp(\"^(\".concat(PresetStatusColorTypes.join('|'), \")$\"));\n\nvar InternalTag = function InternalTag(_a, ref) {\n  var _classNames;\n\n  var customizePrefixCls = _a.prefixCls,\n      className = _a.className,\n      style = _a.style,\n      children = _a.children,\n      icon = _a.icon,\n      color = _a.color,\n      onClose = _a.onClose,\n      closeIcon = _a.closeIcon,\n      _a$closable = _a.closable,\n      closable = _a$closable === void 0 ? false : _a$closable,\n      props = __rest(_a, [\"prefixCls\", \"className\", \"style\", \"children\", \"icon\", \"color\", \"onClose\", \"closeIcon\", \"closable\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var _React$useState = React.useState(true),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      visible = _React$useState2[0],\n      setVisible = _React$useState2[1];\n\n  React.useEffect(function () {\n    if ('visible' in props) {\n      setVisible(props.visible);\n    }\n  }, [props.visible]);\n\n  var isPresetColor = function isPresetColor() {\n    if (!color) {\n      return false;\n    }\n\n    return PresetColorRegex.test(color) || PresetStatusColorRegex.test(color);\n  };\n\n  var tagStyle = _extends({\n    backgroundColor: color && !isPresetColor() ? color : undefined\n  }, style);\n\n  var presetColor = isPresetColor();\n  var prefixCls = getPrefixCls('tag', customizePrefixCls);\n  var tagClassName = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(color), presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-color\"), color && !presetColor), _defineProperty(_classNames, \"\".concat(prefixCls, \"-hidden\"), !visible), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n\n  var handleCloseClick = function handleCloseClick(e) {\n    e.stopPropagation();\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n\n    if (e.defaultPrevented) {\n      return;\n    }\n\n    if (!('visible' in props)) {\n      setVisible(false);\n    }\n  };\n\n  var renderCloseIcon = function renderCloseIcon() {\n    if (closable) {\n      return closeIcon ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      }, closeIcon) : /*#__PURE__*/React.createElement(CloseOutlined, {\n        className: \"\".concat(prefixCls, \"-close-icon\"),\n        onClick: handleCloseClick\n      });\n    }\n\n    return null;\n  };\n\n  var isNeedWave = 'onClick' in props || children && children.type === 'a';\n  var tagProps = omit(props, ['visible']);\n  var iconNode = icon || null;\n  var kids = iconNode ? /*#__PURE__*/React.createElement(React.Fragment, null, iconNode, /*#__PURE__*/React.createElement(\"span\", null, children)) : children;\n  var tagNode = /*#__PURE__*/React.createElement(\"span\", _extends({}, tagProps, {\n    ref: ref,\n    className: tagClassName,\n    style: tagStyle\n  }), kids, renderCloseIcon());\n  return isNeedWave ? /*#__PURE__*/React.createElement(Wave, null, tagNode) : tagNode;\n};\n\nvar Tag = /*#__PURE__*/React.forwardRef(InternalTag);\nTag.displayName = 'Tag';\nTag.CheckableTag = CheckableTag;\nexport default Tag;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AAErE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,iBAAiB;AAC1E,OAAOC,IAAI,MAAM,eAAe;AAChC,IAAIC,gBAAgB,GAAG,IAAIC,MAAM,CAAC,IAAI,CAACC,MAAM,CAACL,gBAAgB,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC;AAC3F,IAAIC,sBAAsB,GAAG,IAAIH,MAAM,CAAC,IAAI,CAACC,MAAM,CAACJ,sBAAsB,CAACK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAE5F,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAE;EAC9C,IAAIC,WAAW;EAEf,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACjCC,SAAS,GAAGL,EAAE,CAACK,SAAS;IACxBC,KAAK,GAAGN,EAAE,CAACM,KAAK;IAChBC,QAAQ,GAAGP,EAAE,CAACO,QAAQ;IACtBC,IAAI,GAAGR,EAAE,CAACQ,IAAI;IACdC,KAAK,GAAGT,EAAE,CAACS,KAAK;IAChBC,OAAO,GAAGV,EAAE,CAACU,OAAO;IACpBC,SAAS,GAAGX,EAAE,CAACW,SAAS;IACxBC,WAAW,GAAGZ,EAAE,CAACa,QAAQ;IACzBA,QAAQ,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACvDE,KAAK,GAAG3C,MAAM,CAAC6B,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EAE5H,IAAIe,iBAAiB,GAAG9B,KAAK,CAAC+B,UAAU,CAAC1B,aAAa,CAAC;IACnD2B,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,eAAe,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,IAAI,CAAC;IACtCC,gBAAgB,GAAGnD,cAAc,CAACiD,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpCpC,KAAK,CAACuC,SAAS,CAAC,YAAY;IAC1B,IAAI,SAAS,IAAIV,KAAK,EAAE;MACtBS,UAAU,CAACT,KAAK,CAACQ,OAAO,CAAC;IAC3B;EACF,CAAC,EAAE,CAACR,KAAK,CAACQ,OAAO,CAAC,CAAC;EAEnB,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI,CAAChB,KAAK,EAAE;MACV,OAAO,KAAK;IACd;IAEA,OAAOf,gBAAgB,CAACgC,IAAI,CAACjB,KAAK,CAAC,IAAIX,sBAAsB,CAAC4B,IAAI,CAACjB,KAAK,CAAC;EAC3E,CAAC;EAED,IAAIkB,QAAQ,GAAG1D,QAAQ,CAAC;IACtB2D,eAAe,EAAEnB,KAAK,IAAI,CAACgB,aAAa,CAAC,CAAC,GAAGhB,KAAK,GAAGoB;EACvD,CAAC,EAAEvB,KAAK,CAAC;EAET,IAAIwB,WAAW,GAAGL,aAAa,CAAC,CAAC;EACjC,IAAIrB,SAAS,GAAGa,YAAY,CAAC,KAAK,EAAEd,kBAAkB,CAAC;EACvD,IAAI4B,YAAY,GAAG7C,UAAU,CAACkB,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAElC,eAAe,CAACkC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,GAAG,CAAC,CAACR,MAAM,CAACa,KAAK,CAAC,EAAEqB,WAAW,CAAC,EAAE9D,eAAe,CAACkC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,YAAY,CAAC,EAAEK,KAAK,IAAI,CAACqB,WAAW,CAAC,EAAE9D,eAAe,CAACkC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,SAAS,CAAC,EAAE,CAACkB,OAAO,CAAC,EAAEtD,eAAe,CAACkC,WAAW,EAAE,EAAE,CAACN,MAAM,CAACQ,SAAS,EAAE,MAAM,CAAC,EAAEc,SAAS,KAAK,KAAK,CAAC,EAAEhB,WAAW,GAAGG,SAAS,CAAC;EAE3Z,IAAI2B,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC3D,CAAC,EAAE;IAClDA,CAAC,CAAC4D,eAAe,CAAC,CAAC;IACnBvB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACrC,CAAC,CAAC;IAE5D,IAAIA,CAAC,CAAC6D,gBAAgB,EAAE;MACtB;IACF;IAEA,IAAI,EAAE,SAAS,IAAIpB,KAAK,CAAC,EAAE;MACzBS,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIY,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAItB,QAAQ,EAAE;MACZ,OAAOF,SAAS,GAAG,aAAa1B,KAAK,CAACmD,aAAa,CAAC,MAAM,EAAE;QAC1D/B,SAAS,EAAE,EAAE,CAACT,MAAM,CAACQ,SAAS,EAAE,aAAa,CAAC;QAC9CiC,OAAO,EAAEL;MACX,CAAC,EAAErB,SAAS,CAAC,GAAG,aAAa1B,KAAK,CAACmD,aAAa,CAAChD,aAAa,EAAE;QAC9DiB,SAAS,EAAE,EAAE,CAACT,MAAM,CAACQ,SAAS,EAAE,aAAa,CAAC;QAC9CiC,OAAO,EAAEL;MACX,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI;EACb,CAAC;EAED,IAAIM,UAAU,GAAG,SAAS,IAAIxB,KAAK,IAAIP,QAAQ,IAAIA,QAAQ,CAACgC,IAAI,KAAK,GAAG;EACxE,IAAIC,QAAQ,GAAGrD,IAAI,CAAC2B,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC;EACvC,IAAI2B,QAAQ,GAAGjC,IAAI,IAAI,IAAI;EAC3B,IAAIkC,IAAI,GAAGD,QAAQ,GAAG,aAAaxD,KAAK,CAACmD,aAAa,CAACnD,KAAK,CAAC0D,QAAQ,EAAE,IAAI,EAAEF,QAAQ,EAAE,aAAaxD,KAAK,CAACmD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE7B,QAAQ,CAAC,CAAC,GAAGA,QAAQ;EAC3J,IAAIqC,OAAO,GAAG,aAAa3D,KAAK,CAACmD,aAAa,CAAC,MAAM,EAAEnE,QAAQ,CAAC,CAAC,CAAC,EAAEuE,QAAQ,EAAE;IAC5EvC,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAE0B,YAAY;IACvBzB,KAAK,EAAEqB;EACT,CAAC,CAAC,EAAEe,IAAI,EAAEP,eAAe,CAAC,CAAC,CAAC;EAC5B,OAAOG,UAAU,GAAG,aAAarD,KAAK,CAACmD,aAAa,CAAC3C,IAAI,EAAE,IAAI,EAAEmD,OAAO,CAAC,GAAGA,OAAO;AACrF,CAAC;AAED,IAAIC,GAAG,GAAG,aAAa5D,KAAK,CAAC6D,UAAU,CAAC/C,WAAW,CAAC;AACpD8C,GAAG,CAACE,WAAW,GAAG,KAAK;AACvBF,GAAG,CAACxD,YAAY,GAAGA,YAAY;AAC/B,eAAewD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}