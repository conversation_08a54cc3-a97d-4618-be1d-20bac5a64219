export class TopbarFeatures {
    /**
     * @param {ReportUIFeatures} reportUIFeatures
     * @param {DOM} dom
     */
    constructor(reportUIFeatures: ReportUIFeatures, dom: DOM);
    /** @type {LH.Result} */
    lhr: LH.Result;
    _reportUIFeatures: import("./report-ui-features").ReportUIFeatures;
    _dom: import("./dom.js").DOM;
    _dropDownMenu: DropDownMenu;
    _copyAttempt: boolean;
    /** @type {HTMLElement} */
    topbarEl: HTMLElement;
    /** @type {HTMLElement} */
    categoriesEl: HTMLElement;
    /** @type {HTMLElement?} */
    stickyHeaderEl: HTMLElement | null;
    /** @type {HTMLElement} */
    highlightEl: HTMLElement;
    /**
     * Handler for tool button.
     * @param {Event} e
     */
    onDropDownMenuClick(e: Event): void;
    /**
     * Keyup handler for the document.
     * @param {KeyboardEvent} e
     */
    onKeyUp(e: KeyboardEvent): void;
    /**
     * Handle copy events.
     * @param {ClipboardEvent} e
     */
    onCopy(e: ClipboardEvent): void;
    /**
     * Collapses all audit `<details>`.
     * open a `<details>` element.
     */
    collapseAllDetails(): void;
    /**
     * @param {LH.Result} lhr
     */
    enable(lhr: LH.Result): void;
    /**
     * Copies the report JSON to the clipboard (if supported by the browser).
     */
    onCopyButtonClick(): void;
    /**
     * Expands all audit `<details>`.
     * Ideally, a print stylesheet could take care of this, but CSS has no way to
     * open a `<details>` element.
     */
    expandAllDetails(): void;
    _print(): void;
    /**
     * Resets the state of page before capturing the page for export.
     * When the user opens the exported HTML page, certain UI elements should
     * be in their closed state (not opened) and the templates should be unstamped.
     */
    resetUIState(): void;
    /**
     * Finds the first scrollable ancestor of `element`. Falls back to the document.
     * @param {Element} element
     * @return {Element | Document}
     */
    _getScrollParent(element: Element): Element | Document;
    /**
     * Sets up listeners to collapse audit `<details>` when the user closes the
     * print dialog, all `<details>` are collapsed.
     */
    _setUpCollapseDetailsAfterPrinting(): void;
    _setupStickyHeader(): void;
    /**
     * Toggle visibility and update highlighter position
     */
    _updateStickyHeader(): void;
}
export type DOM = import('./dom.js').DOM;
export type ReportUIFeatures = import('./report-ui-features').ReportUIFeatures;
import { DropDownMenu } from "./drop-down-menu.js";
//# sourceMappingURL=topbar-features.d.ts.map