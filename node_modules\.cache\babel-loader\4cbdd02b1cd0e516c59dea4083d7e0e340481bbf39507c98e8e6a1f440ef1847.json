{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer'; // Use shared variable to save bundle size\n\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedHidden = responsive && !display; // ================================ Effect ================================\n\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  React.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []); // ================================ Render ================================\n\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: _objectSpread(_objectSpread({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      }\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/React.forwardRef(InternalItem);\nItem.displayName = 'Item';\nexport default Item;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_excluded", "React", "classNames", "ResizeObserver", "UNDEFINED", "undefined", "InternalItem", "props", "ref", "prefixCls", "invalidate", "item", "renderItem", "responsive", "registerSize", "itemKey", "className", "style", "children", "display", "order", "_props$component", "component", "Component", "restProps", "mergedHidden", "internalRegisterSize", "width", "useEffect", "childNode", "overflowStyle", "opacity", "height", "overflowY", "pointerEvents", "position", "overflowProps", "itemNode", "createElement", "onResize", "_ref", "offsetWidth", "<PERSON><PERSON>", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-overflow/es/Item.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer'; // Use shared variable to save bundle size\n\nvar UNDEFINED = undefined;\n\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n      invalidate = props.invalidate,\n      item = props.item,\n      renderItem = props.renderItem,\n      responsive = props.responsive,\n      registerSize = props.registerSize,\n      itemKey = props.itemKey,\n      className = props.className,\n      style = props.style,\n      children = props.children,\n      display = props.display,\n      order = props.order,\n      _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var mergedHidden = responsive && !display; // ================================ Effect ================================\n\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n\n  React.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []); // ================================ Render ================================\n\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item) : children;\n  var overflowStyle;\n\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n\n  var overflowProps = {};\n\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n\n  var itemNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: _objectSpread(_objectSpread({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n\n  if (responsive) {\n    itemNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      }\n    }, itemNode);\n  }\n\n  return itemNode;\n}\n\nvar Item = /*#__PURE__*/React.forwardRef(InternalItem);\nItem.displayName = 'Item';\nexport default Item;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC;AAC7K,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB,CAAC,CAAC;;AAEjD,IAAIC,SAAS,GAAGC,SAAS;AAEzB,SAASC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAChC,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;IACvBC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,gBAAgB,GAAGd,KAAK,CAACe,SAAS;IAClCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEG,SAAS,GAAGzB,wBAAwB,CAACQ,KAAK,EAAEP,SAAS,CAAC;EAE1D,IAAIyB,YAAY,GAAGZ,UAAU,IAAI,CAACM,OAAO,CAAC,CAAC;;EAE3C,SAASO,oBAAoBA,CAACC,KAAK,EAAE;IACnCb,YAAY,CAACC,OAAO,EAAEY,KAAK,CAAC;EAC9B;EAEA1B,KAAK,CAAC2B,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBF,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIG,SAAS,GAAGjB,UAAU,IAAID,IAAI,KAAKP,SAAS,GAAGQ,UAAU,CAACD,IAAI,CAAC,GAAGO,QAAQ;EAC9E,IAAIY,aAAa;EAEjB,IAAI,CAACpB,UAAU,EAAE;IACfoB,aAAa,GAAG;MACdC,OAAO,EAAEN,YAAY,GAAG,CAAC,GAAG,CAAC;MAC7BO,MAAM,EAAEP,YAAY,GAAG,CAAC,GAAGrB,SAAS;MACpC6B,SAAS,EAAER,YAAY,GAAG,QAAQ,GAAGrB,SAAS;MAC9CgB,KAAK,EAAEP,UAAU,GAAGO,KAAK,GAAGhB,SAAS;MACrC8B,aAAa,EAAET,YAAY,GAAG,MAAM,GAAGrB,SAAS;MAChD+B,QAAQ,EAAEV,YAAY,GAAG,UAAU,GAAGrB;IACxC,CAAC;EACH;EAEA,IAAIgC,aAAa,GAAG,CAAC,CAAC;EAEtB,IAAIX,YAAY,EAAE;IAChBW,aAAa,CAAC,aAAa,CAAC,GAAG,IAAI;EACrC;EAEA,IAAIC,QAAQ,GAAG,aAAapC,KAAK,CAACqC,aAAa,CAACf,SAAS,EAAE1B,QAAQ,CAAC;IAClEmB,SAAS,EAAEd,UAAU,CAAC,CAACQ,UAAU,IAAID,SAAS,EAAEO,SAAS,CAAC;IAC1DC,KAAK,EAAEnB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,aAAa,CAAC,EAAEb,KAAK;EAC9D,CAAC,EAAEmB,aAAa,EAAEZ,SAAS,EAAE;IAC3BhB,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEqB,SAAS,CAAC;EAEd,IAAIhB,UAAU,EAAE;IACdwB,QAAQ,GAAG,aAAapC,KAAK,CAACqC,aAAa,CAACnC,cAAc,EAAE;MAC1DoC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;QAChC,IAAIC,WAAW,GAAGD,IAAI,CAACC,WAAW;QAClCf,oBAAoB,CAACe,WAAW,CAAC;MACnC;IACF,CAAC,EAAEJ,QAAQ,CAAC;EACd;EAEA,OAAOA,QAAQ;AACjB;AAEA,IAAIK,IAAI,GAAG,aAAazC,KAAK,CAAC0C,UAAU,CAACrC,YAAY,CAAC;AACtDoC,IAAI,CAACE,WAAW,GAAG,MAAM;AACzB,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}