/**
* Winet e-procurement GUI
* 2020 - Viniexport.com (C)
*
* AggiungiCSV - operazioni sull'aggiunta CSV 
*
*/
import React, { useRef, useState } from "react";
import { APIRequest } from "../../../components/generalizzazioni/apireq";
import { <PERSON>nti } from "../../../components/traduttore/const";
import { Button } from "primereact/button";
import { FileUpload } from "primereact/fileupload";
import { Toast } from "primereact/toast";
import { InputText } from "primereact/inputtext";
import { SelectButton } from "primereact/selectbutton";
import { chainGestioneProdotti } from "../../../components/route";
import ScaricaCSVProva from "./scaricaCSVProva";
import { useEffect } from "react";

export const AggiungiCSV = (props) => {
    const [selectedFile, setSelectedFile] = useState(null);
    const [result, setResult] = useState(null);
    const [disabled, setDisabled] = useState('');
    const [value1, setValue1] = useState(null);
    const [value2, setValue2] = useState(null);
    const [value3, setValue3] = useState('COD_PROD');
    const options = [{ name: 'Codice prodotto', value: 'COD_PROD' }, { name: 'ID prodotto', value: 'ID_PROD' }];
    const toast = useRef(null);

    useEffect(() => {
        var results = []
        var result = props.results.splice(1,10)
        result.forEach(element => {
            delete element.id
            delete element.productsAvailabilities
            delete element.productsPackagings
            delete element.createAt
            delete element.updateAt
            results.push(element)
        });
        setResult(results)
    }, [props])

    const onCancel = () => {
        setDisabled(false)
    }
    const uploadFile = (e) => {
        console.log(e)
        if (e.files[0].size < 1300000) {
            setSelectedFile(e.files[0])
            setDisabled(true)
        }
    }
    const Invia = async () => {
        if (window.location.pathname === chainGestioneProdotti) {
            if (selectedFile !== null) {
                // Create an object of formData 
                const formData = new FormData();
                // Update the formData object 
                formData.append(
                    "csv",
                    selectedFile
                );
                await APIRequest('POST', 'uploads/products', formData)
                    .then(res => {
                        console.log(res.data);
                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: "Il CSV è stato inserito con successo", life: 3000 });
                        setTimeout(() => {
                            window.location.reload()
                        }, 3000)
                    }).catch((e) => {
                        console.error('❌ Errore caricamento CSV prodotti:', e);

                        // Gestione specifica degli errori
                        const errorStatus = e.response?.status;
                        const errorData = e.response?.data;
                        const errorMessage = e.response?.data?.message || e.message;

                        let userMessage = '';
                        let summary = 'Errore';

                        if (errorStatus === 400) {
                            // Errori di validazione file
                            summary = '📄 File non valido';
                            if (errorMessage.toLowerCase().includes('formato') || errorMessage.toLowerCase().includes('csv')) {
                                userMessage = 'Il file CSV non è nel formato corretto. Verificare la struttura del file.';
                            } else if (errorMessage.toLowerCase().includes('dimensione') || errorMessage.toLowerCase().includes('size')) {
                                userMessage = 'Il file è troppo grande. Ridurre le dimensioni del file e riprovare.';
                            } else {
                                userMessage = `File non valido: ${errorMessage}`;
                            }
                        } else if (errorStatus === 413) {
                            // File troppo grande
                            summary = '📦 File troppo grande';
                            userMessage = 'Il file supera la dimensione massima consentita. Ridurre le dimensioni e riprovare.';
                        } else if (errorStatus === 422) {
                            // Errori di contenuto
                            summary = '📝 Contenuto non valido';
                            userMessage = `Il contenuto del CSV non è valido: ${errorMessage}`;
                        } else if (errorStatus === 500) {
                            // Errore server
                            summary = '🔧 Errore del server';
                            userMessage = 'Si è verificato un errore interno durante l\'elaborazione del file.';
                        } else if (!errorStatus) {
                            // Errore di rete
                            summary = '🌐 Errore di connessione';
                            userMessage = 'Impossibile caricare il file. Verificare la connessione internet.';
                        } else {
                            // Altri errori
                            summary = '❌ Errore imprevisto';
                            userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
                        }

                        toast.current.show({
                            severity: 'error',
                            summary: summary,
                            detail: userMessage,
                            life: 6000
                        });

                        // Log dettagliato per debugging
                        console.error('Dettagli errore CSV prodotti:', {
                            status: errorStatus,
                            data: errorData,
                            message: errorMessage,
                            fullError: e
                        });
                    })
            } else {
                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: "Non è stato selezionato un CSV per l'importazione", life: 3000 });
            }
        } else if (props.importPriceList) {
            if (selectedFile !== null) {
                // Create an object of formData 
                const formData = new FormData();
                // Update the formData object 
                formData.append(
                    "csv",
                    selectedFile
                );
                var url = 'uploads/pricelist?separator=' + value1 + '&decimalDelimeter=' + value2 + '&idPriceList=' + localStorage.getItem("datiComodo") + '&type=' + value3
                await APIRequest('POST', url, formData)
                    .then(res => {
                        console.log(res.data);
                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: "Il CSV è stato inserito con successo", life: 3000 });
                    }).catch((e) => {
                        console.error('❌ Errore caricamento CSV listino:', e);

                        // Gestione specifica degli errori
                        const errorStatus = e.response?.status;
                        const errorData = e.response?.data;
                        const errorMessage = e.response?.data?.message || e.message;

                        let userMessage = '';
                        let summary = 'Errore';

                        if (errorStatus === 400) {
                            // Errori di validazione file
                            summary = '📄 File listino non valido';
                            if (errorMessage.toLowerCase().includes('formato') || errorMessage.toLowerCase().includes('csv')) {
                                userMessage = 'Il file CSV del listino non è nel formato corretto. Verificare la struttura del file.';
                            } else if (errorMessage.toLowerCase().includes('prezzo') || errorMessage.toLowerCase().includes('price')) {
                                userMessage = 'I prezzi nel listino non sono nel formato corretto. Verificare i valori numerici.';
                            } else {
                                userMessage = `File listino non valido: ${errorMessage}`;
                            }
                        } else if (errorStatus === 413) {
                            // File troppo grande
                            summary = '📦 File troppo grande';
                            userMessage = 'Il file listino supera la dimensione massima consentita.';
                        } else if (errorStatus === 422) {
                            // Errori di contenuto
                            summary = '📝 Contenuto listino non valido';
                            userMessage = `Il contenuto del listino non è valido: ${errorMessage}`;
                        } else if (errorStatus === 500) {
                            // Errore server
                            summary = '🔧 Errore del server';
                            userMessage = 'Si è verificato un errore interno durante l\'elaborazione del listino.';
                        } else if (!errorStatus) {
                            // Errore di rete
                            summary = '🌐 Errore di connessione';
                            userMessage = 'Impossibile caricare il listino. Verificare la connessione internet.';
                        } else {
                            // Altri errori
                            summary = '❌ Errore imprevisto';
                            userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;
                        }

                        toast.current.show({
                            severity: 'error',
                            summary: summary,
                            detail: userMessage,
                            life: 6000
                        });

                        // Log dettagliato per debugging
                        console.error('Dettagli errore CSV listino:', {
                            status: errorStatus,
                            data: errorData,
                            message: errorMessage,
                            fullError: e
                        });
                    })
            } else {
                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: "Non è stato selezionato un CSV per l'importazione", life: 3000 });
            }
        }
    }
    return (
        <div className="card p-2">
            <Toast ref={toast} />
            {props.importPriceList &&
                <div className="row px-5 pb-5 pt-3">
                    <div className="col-12 col-md-6 mb-3 d-flex justify-content-center flex-column align-items-center">
                        <h4>{Costanti.SelSep}:</h4>
                        <InputText value={value1} onChange={(e) => setValue1(e.target.value)} />
                    </div>
                    <div className="col-12 col-md-6 mb-3 d-flex justify-content-center flex-column align-items-center">
                        <h4>{Costanti.SelDelDec}:</h4>
                        <InputText value={value2} onChange={(e) => setValue2(e.target.value)} />
                    </div>
                    <div className="col-12 d-flex justify-content-center flex-column align-items-center">
                        <h4>{Costanti.SelectType}:</h4>
                        <SelectButton className="w-100" value={value3} options={options} optionLabel='name' optionValue="value" onChange={(e) => setValue3(e.value)} />
                    </div>
                </div>
            }
            <div className="row">
                <div className="col-12 d-flex justify-content-center">
                    <h5 className="mt-2 text-center">{Costanti.SelCSV}</h5>
                </div>
                <div className="col-12">
                    <FileUpload id="upload" onSelect={e => uploadFile(e)} className="form-control border-0" chooseLabel="Seleziona" /*uploadLabel="Carica" cancelLabel="Elimina"*/
                        uploadOptions={{ className: 'd-none' }} cancelOptions={{ className: 'd-none' }} maxFileSize='1300000'
                        invalidFileSizeMessageSummary="Il file selezionato supera la dimensione massima consentita" invalidFileSizeMessageDetail=""
                        disabled={disabled} onRemove={onCancel} accept=".CSV"
                    />
                </div>
                <div className="col-12">
                    <hr />
                    <div className="row">
                        <div className="col-12 col-md-6 border-right mb-3 mb-md-0">
                            <div className="d-flex justify-content-center">
                                <p>{Costanti.CSVEX}</p>
                            </div>
                            <ScaricaCSVProva label={'ScaricaCSV'} results={result} />
                        </div>
                        <div className="col-12 col-md-6">
                            <div className="d-flex justify-content-center text-center">
                                <p>{Costanti.ProcediImportCSV}</p>
                            </div>
                            <Button className="d-flex justify-content-center" onClick={Invia}><span className='pi pi-upload mr-2' />{Costanti.Importa}</Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}