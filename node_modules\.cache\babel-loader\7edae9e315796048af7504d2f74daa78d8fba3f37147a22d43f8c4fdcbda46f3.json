{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport isMobile from \"rc-util/es/isMobile\";\n/**\n * When click and hold on a button - the speed of auto changing the value.\n */\n\nvar STEP_INTERVAL = 200;\n/**\n * When click and hold on a button - the delay before auto changing the value.\n */\n\nvar STEP_DELAY = 600;\nexport default function StepHandler(_ref) {\n  var prefixCls = _ref.prefixCls,\n    upNode = _ref.upNode,\n    downNode = _ref.downNode,\n    upDisabled = _ref.upDisabled,\n    downDisabled = _ref.downDisabled,\n    onStep = _ref.onStep;\n  // ======================== Step ========================\n  var stepTimeoutRef = React.useRef();\n  var onStepRef = React.useRef();\n  onStepRef.current = onStep; // We will interval update step when hold mouse down\n\n  var onStepMouseDown = function onStepMouseDown(e, up) {\n    e.preventDefault();\n    onStepRef.current(up); // Loop step for interval\n\n    function loopStep() {\n      onStepRef.current(up);\n      stepTimeoutRef.current = setTimeout(loopStep, STEP_INTERVAL);\n    } // First time press will wait some time to trigger loop step update\n\n    stepTimeoutRef.current = setTimeout(loopStep, STEP_DELAY);\n  };\n  var onStopStep = function onStopStep() {\n    clearTimeout(stepTimeoutRef.current);\n  };\n  React.useEffect(function () {\n    return onStopStep;\n  }, []); // ======================= Render =======================\n\n  if (isMobile()) {\n    return null;\n  }\n  var handlerClassName = \"\".concat(prefixCls, \"-handler\");\n  var upClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-up\"), _defineProperty({}, \"\".concat(handlerClassName, \"-up-disabled\"), upDisabled));\n  var downClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-down\"), _defineProperty({}, \"\".concat(handlerClassName, \"-down-disabled\"), downDisabled));\n  var sharedHandlerProps = {\n    unselectable: 'on',\n    role: 'button',\n    onMouseUp: onStopStep,\n    onMouseLeave: onStopStep\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(handlerClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, true);\n    },\n    \"aria-label\": \"Increase Value\",\n    \"aria-disabled\": upDisabled,\n    className: upClassName\n  }), upNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-up-inner\")\n  })), /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, false);\n    },\n    \"aria-label\": \"Decrease Value\",\n    \"aria-disabled\": downDisabled,\n    className: downClassName\n  }), downNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-down-inner\")\n  })));\n}", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "classNames", "isMobile", "STEP_INTERVAL", "STEP_DELAY", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "prefixCls", "upNode", "downNode", "upDisabled", "downDisabled", "onStep", "stepTimeoutRef", "useRef", "onStepRef", "current", "onStepMouseDown", "e", "up", "preventDefault", "loopStep", "setTimeout", "onStopStep", "clearTimeout", "useEffect", "handlerClassName", "concat", "upClassName", "downClassName", "sharedHandlerProps", "unselectable", "role", "onMouseUp", "onMouseLeave", "createElement", "className", "onMouseDown"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-input-number/es/StepHandler.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport isMobile from \"rc-util/es/isMobile\";\n/**\n * When click and hold on a button - the speed of auto changing the value.\n */\n\nvar STEP_INTERVAL = 200;\n/**\n * When click and hold on a button - the delay before auto changing the value.\n */\n\nvar STEP_DELAY = 600;\nexport default function StepHandler(_ref) {\n  var prefixCls = _ref.prefixCls,\n      upNode = _ref.upNode,\n      downNode = _ref.downNode,\n      upDisabled = _ref.upDisabled,\n      downDisabled = _ref.downDisabled,\n      onStep = _ref.onStep;\n  // ======================== Step ========================\n  var stepTimeoutRef = React.useRef();\n  var onStepRef = React.useRef();\n  onStepRef.current = onStep; // We will interval update step when hold mouse down\n\n  var onStepMouseDown = function onStepMouseDown(e, up) {\n    e.preventDefault();\n    onStepRef.current(up); // Loop step for interval\n\n    function loopStep() {\n      onStepRef.current(up);\n      stepTimeoutRef.current = setTimeout(loopStep, STEP_INTERVAL);\n    } // First time press will wait some time to trigger loop step update\n\n\n    stepTimeoutRef.current = setTimeout(loopStep, STEP_DELAY);\n  };\n\n  var onStopStep = function onStopStep() {\n    clearTimeout(stepTimeoutRef.current);\n  };\n\n  React.useEffect(function () {\n    return onStopStep;\n  }, []); // ======================= Render =======================\n\n  if (isMobile()) {\n    return null;\n  }\n\n  var handlerClassName = \"\".concat(prefixCls, \"-handler\");\n  var upClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-up\"), _defineProperty({}, \"\".concat(handlerClassName, \"-up-disabled\"), upDisabled));\n  var downClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-down\"), _defineProperty({}, \"\".concat(handlerClassName, \"-down-disabled\"), downDisabled));\n  var sharedHandlerProps = {\n    unselectable: 'on',\n    role: 'button',\n    onMouseUp: onStopStep,\n    onMouseLeave: onStopStep\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(handlerClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, true);\n    },\n    \"aria-label\": \"Increase Value\",\n    \"aria-disabled\": upDisabled,\n    className: upClassName\n  }), upNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-up-inner\")\n  })), /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, false);\n    },\n    \"aria-label\": \"Decrease Value\",\n    \"aria-disabled\": downDisabled,\n    className: downClassName\n  }), downNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-down-inner\")\n  })));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C;AACA;AACA;;AAEA,IAAIC,aAAa,GAAG,GAAG;AACvB;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,GAAG;AACpB,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,YAAY,GAAGL,IAAI,CAACK,YAAY;IAChCC,MAAM,GAAGN,IAAI,CAACM,MAAM;EACxB;EACA,IAAIC,cAAc,GAAGb,KAAK,CAACc,MAAM,CAAC,CAAC;EACnC,IAAIC,SAAS,GAAGf,KAAK,CAACc,MAAM,CAAC,CAAC;EAC9BC,SAAS,CAACC,OAAO,GAAGJ,MAAM,CAAC,CAAC;;EAE5B,IAAIK,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAEC,EAAE,EAAE;IACpDD,CAAC,CAACE,cAAc,CAAC,CAAC;IAClBL,SAAS,CAACC,OAAO,CAACG,EAAE,CAAC,CAAC,CAAC;;IAEvB,SAASE,QAAQA,CAAA,EAAG;MAClBN,SAAS,CAACC,OAAO,CAACG,EAAE,CAAC;MACrBN,cAAc,CAACG,OAAO,GAAGM,UAAU,CAACD,QAAQ,EAAElB,aAAa,CAAC;IAC9D,CAAC,CAAC;;IAGFU,cAAc,CAACG,OAAO,GAAGM,UAAU,CAACD,QAAQ,EAAEjB,UAAU,CAAC;EAC3D,CAAC;EAED,IAAImB,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrCC,YAAY,CAACX,cAAc,CAACG,OAAO,CAAC;EACtC,CAAC;EAEDhB,KAAK,CAACyB,SAAS,CAAC,YAAY;IAC1B,OAAOF,UAAU;EACnB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIrB,QAAQ,CAAC,CAAC,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIwB,gBAAgB,GAAG,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,UAAU,CAAC;EACvD,IAAIqB,WAAW,GAAG3B,UAAU,CAACyB,gBAAgB,EAAE,EAAE,CAACC,MAAM,CAACD,gBAAgB,EAAE,KAAK,CAAC,EAAE3B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4B,MAAM,CAACD,gBAAgB,EAAE,cAAc,CAAC,EAAEhB,UAAU,CAAC,CAAC;EAChK,IAAImB,aAAa,GAAG5B,UAAU,CAACyB,gBAAgB,EAAE,EAAE,CAACC,MAAM,CAACD,gBAAgB,EAAE,OAAO,CAAC,EAAE3B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4B,MAAM,CAACD,gBAAgB,EAAE,gBAAgB,CAAC,EAAEf,YAAY,CAAC,CAAC;EACxK,IAAImB,kBAAkB,GAAG;IACvBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEV,UAAU;IACrBW,YAAY,EAAEX;EAChB,CAAC;EACD,OAAO,aAAavB,KAAK,CAACmC,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACT,MAAM,CAACD,gBAAgB,EAAE,OAAO;EAChD,CAAC,EAAE,aAAa1B,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,kBAAkB,EAAE;IAC3EO,WAAW,EAAE,SAASA,WAAWA,CAACnB,CAAC,EAAE;MACnCD,eAAe,CAACC,CAAC,EAAE,IAAI,CAAC;IAC1B,CAAC;IACD,YAAY,EAAE,gBAAgB;IAC9B,eAAe,EAAER,UAAU;IAC3B0B,SAAS,EAAER;EACb,CAAC,CAAC,EAAEpB,MAAM,IAAI,aAAaR,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IACrDJ,YAAY,EAAE,IAAI;IAClBK,SAAS,EAAE,EAAE,CAACT,MAAM,CAACpB,SAAS,EAAE,mBAAmB;EACrD,CAAC,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,kBAAkB,EAAE;IAC7EO,WAAW,EAAE,SAASA,WAAWA,CAACnB,CAAC,EAAE;MACnCD,eAAe,CAACC,CAAC,EAAE,KAAK,CAAC;IAC3B,CAAC;IACD,YAAY,EAAE,gBAAgB;IAC9B,eAAe,EAAEP,YAAY;IAC7ByB,SAAS,EAAEP;EACb,CAAC,CAAC,EAAEpB,QAAQ,IAAI,aAAaT,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IACvDJ,YAAY,EAAE,IAAI;IAClBK,SAAS,EAAE,EAAE,CAACT,MAAM,CAACpB,SAAS,EAAE,qBAAqB;EACvD,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}