{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport unauthorized from './unauthorized';\nexport var IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport var ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n}; // ExceptionImageMap keys\n\nvar ExceptionStatus = Object.keys(ExceptionMap);\n/**\n * Render icon if ExceptionStatus includes ,render svg image else render iconNode\n *\n * @param prefixCls\n * @param {status, icon}\n */\n\nvar renderIcon = function renderIcon(prefixCls, _ref) {\n  var status = _ref.status,\n    icon = _ref.icon;\n  var className = classNames(\"\".concat(prefixCls, \"-icon\"));\n  devWarning(!(typeof icon === 'string' && icon.length > 2), 'Result', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\"));\n  if (ExceptionStatus.includes(\"\".concat(status))) {\n    var SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(className, \" \").concat(prefixCls, \"-image\")\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n  var iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\nvar renderExtra = function renderExtra(prefixCls, _ref2) {\n  var extra = _ref2.extra;\n  return extra && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra\")\n  }, extra);\n};\nvar Result = function Result(_ref3) {\n  var customizePrefixCls = _ref3.prefixCls,\n    customizeClassName = _ref3.className,\n    subTitle = _ref3.subTitle,\n    title = _ref3.title,\n    style = _ref3.style,\n    children = _ref3.children,\n    _ref3$status = _ref3.status,\n    status = _ref3$status === void 0 ? 'info' : _ref3$status,\n    icon = _ref3.icon,\n    extra = _ref3.extra;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('result', customizePrefixCls);\n  var className = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(status), customizeClassName, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, renderIcon(prefixCls, {\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-subtitle\")\n  }, subTitle), renderExtra(prefixCls, {\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children));\n};\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nexport default Result;", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "CheckCircleFilled", "CloseCircleFilled", "ExclamationCircleFilled", "WarningFilled", "ConfigContext", "dev<PERSON><PERSON><PERSON>", "noFound", "serverError", "unauthorized", "IconMap", "success", "error", "info", "warning", "ExceptionMap", "ExceptionStatus", "Object", "keys", "renderIcon", "prefixCls", "_ref", "status", "icon", "className", "concat", "length", "includes", "SVGComponent", "createElement", "iconNode", "renderExtra", "_ref2", "extra", "Result", "_ref3", "customizePrefixCls", "customizeClassName", "subTitle", "title", "style", "children", "_ref3$status", "_React$useContext", "useContext", "getPrefixCls", "direction", "PRESENTED_IMAGE_403", "PRESENTED_IMAGE_404", "PRESENTED_IMAGE_500"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/result/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport unauthorized from './unauthorized';\nexport var IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport var ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n}; // ExceptionImageMap keys\n\nvar ExceptionStatus = Object.keys(ExceptionMap);\n/**\n * Render icon if ExceptionStatus includes ,render svg image else render iconNode\n *\n * @param prefixCls\n * @param {status, icon}\n */\n\nvar renderIcon = function renderIcon(prefixCls, _ref) {\n  var status = _ref.status,\n      icon = _ref.icon;\n  var className = classNames(\"\".concat(prefixCls, \"-icon\"));\n  devWarning(!(typeof icon === 'string' && icon.length > 2), 'Result', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\"));\n\n  if (ExceptionStatus.includes(\"\".concat(status))) {\n    var SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(className, \" \").concat(prefixCls, \"-image\")\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n\n  var iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\n\nvar renderExtra = function renderExtra(prefixCls, _ref2) {\n  var extra = _ref2.extra;\n  return extra && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra\")\n  }, extra);\n};\n\nvar Result = function Result(_ref3) {\n  var customizePrefixCls = _ref3.prefixCls,\n      customizeClassName = _ref3.className,\n      subTitle = _ref3.subTitle,\n      title = _ref3.title,\n      style = _ref3.style,\n      children = _ref3.children,\n      _ref3$status = _ref3.status,\n      status = _ref3$status === void 0 ? 'info' : _ref3$status,\n      icon = _ref3.icon,\n      extra = _ref3.extra;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var prefixCls = getPrefixCls('result', customizePrefixCls);\n  var className = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(status), customizeClassName, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, renderIcon(prefixCls, {\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-subtitle\")\n  }, subTitle), renderExtra(prefixCls, {\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children));\n};\n\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nexport default Result;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,uBAAuB,MAAM,oDAAoD;AACxF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAO,IAAIC,OAAO,GAAG;EACnBC,OAAO,EAAEV,iBAAiB;EAC1BW,KAAK,EAAEV,iBAAiB;EACxBW,IAAI,EAAEV,uBAAuB;EAC7BW,OAAO,EAAEV;AACX,CAAC;AACD,OAAO,IAAIW,YAAY,GAAG;EACxB,KAAK,EAAER,OAAO;EACd,KAAK,EAAEC,WAAW;EAClB,KAAK,EAAEC;AACT,CAAC,CAAC,CAAC;;AAEH,IAAIO,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAII,UAAU,GAAG,SAASA,UAAUA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACpD,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,IAAIC,SAAS,GAAGxB,UAAU,CAAC,EAAE,CAACyB,MAAM,CAACL,SAAS,EAAE,OAAO,CAAC,CAAC;EACzDd,UAAU,CAAC,EAAE,OAAOiB,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,0EAA0E,CAACD,MAAM,CAACF,IAAI,EAAE,yCAAyC,CAAC,CAAC;EAExM,IAAIP,eAAe,CAACW,QAAQ,CAAC,EAAE,CAACF,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC/C,IAAIM,YAAY,GAAGb,YAAY,CAACO,MAAM,CAAC;IACvC,OAAO,aAAavB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;MAC7CL,SAAS,EAAE,EAAE,CAACC,MAAM,CAACD,SAAS,EAAE,GAAG,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,QAAQ;IACjE,CAAC,EAAE,aAAarB,KAAK,CAAC8B,aAAa,CAACD,YAAY,EAAE,IAAI,CAAC,CAAC;EAC1D;EAEA,IAAIE,QAAQ,GAAG,aAAa/B,KAAK,CAAC8B,aAAa,CAACnB,OAAO,CAACY,MAAM,CAAC,CAAC;EAChE,OAAO,aAAavB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7CL,SAAS,EAAEA;EACb,CAAC,EAAED,IAAI,IAAIO,QAAQ,CAAC;AACtB,CAAC;AAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACX,SAAS,EAAEY,KAAK,EAAE;EACvD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;EACvB,OAAOA,KAAK,IAAI,aAAalC,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACtDL,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEa,KAAK,CAAC;AACX,CAAC;AAED,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,kBAAkB,GAAGD,KAAK,CAACf,SAAS;IACpCiB,kBAAkB,GAAGF,KAAK,CAACX,SAAS;IACpCc,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,YAAY,GAAGP,KAAK,CAACb,MAAM;IAC3BA,MAAM,GAAGoB,YAAY,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,YAAY;IACxDnB,IAAI,GAAGY,KAAK,CAACZ,IAAI;IACjBU,KAAK,GAAGE,KAAK,CAACF,KAAK;EAEvB,IAAIU,iBAAiB,GAAG5C,KAAK,CAAC6C,UAAU,CAACvC,aAAa,CAAC;IACnDwC,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAI1B,SAAS,GAAGyB,YAAY,CAAC,QAAQ,EAAET,kBAAkB,CAAC;EAC1D,IAAIZ,SAAS,GAAGxB,UAAU,CAACoB,SAAS,EAAE,EAAE,CAACK,MAAM,CAACL,SAAS,EAAE,GAAG,CAAC,CAACK,MAAM,CAACH,MAAM,CAAC,EAAEe,kBAAkB,EAAEvC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2B,MAAM,CAACL,SAAS,EAAE,MAAM,CAAC,EAAE0B,SAAS,KAAK,KAAK,CAAC,CAAC;EAC3K,OAAO,aAAa/C,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7CL,SAAS,EAAEA,SAAS;IACpBgB,KAAK,EAAEA;EACT,CAAC,EAAErB,UAAU,CAACC,SAAS,EAAE;IACvBE,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC1CL,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEmB,KAAK,CAAC,EAAED,QAAQ,IAAI,aAAavC,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7DL,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,WAAW;EAC7C,CAAC,EAAEkB,QAAQ,CAAC,EAAEP,WAAW,CAACX,SAAS,EAAE;IACnCa,KAAK,EAAEA;EACT,CAAC,CAAC,EAAEQ,QAAQ,IAAI,aAAa1C,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACtDL,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEqB,QAAQ,CAAC,CAAC;AACf,CAAC;AAEDP,MAAM,CAACa,mBAAmB,GAAGhC,YAAY,CAAC,KAAK,CAAC;AAChDmB,MAAM,CAACc,mBAAmB,GAAGjC,YAAY,CAAC,KAAK,CAAC;AAChDmB,MAAM,CAACe,mBAAmB,GAAGlC,YAAY,CAAC,KAAK,CAAC;AAChD,eAAemB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}