{"ast": null, "code": "\"use strict\";\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _configProvider = require(\"../config-provider\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar Simple = function Simple() {\n  var _React$useContext = React.useContext(_configProvider.ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('empty-img-simple');\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    className: prefixCls,\n    width: \"64\",\n    height: \"41\",\n    viewBox: \"0 0 64 41\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(0 1)\",\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    className: \"\".concat(prefixCls, \"-ellipse\"),\n    cx: \"32\",\n    cy: \"33\",\n    rx: \"32\",\n    ry: \"7\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    className: \"\".concat(prefixCls, \"-g\"),\n    fillRule: \"nonzero\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\",\n    className: \"\".concat(prefixCls, \"-path\")\n  }))));\n};\nvar _default = Simple;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_typeof", "require", "Object", "defineProperty", "exports", "value", "React", "_interopRequireWildcard", "_config<PERSON><PERSON><PERSON>", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "Simple", "_React$useContext", "useContext", "ConfigContext", "getPrefixCls", "prefixCls", "createElement", "className", "width", "height", "viewBox", "xmlns", "transform", "fill", "fillRule", "concat", "cx", "cy", "rx", "ry", "d", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/empty/simple.js"], "sourcesContent": ["\"use strict\";\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _configProvider = require(\"../config-provider\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar Simple = function Simple() {\n  var _React$useContext = React.useContext(_configProvider.ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('empty-img-simple');\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    className: prefixCls,\n    width: \"64\",\n    height: \"41\",\n    viewBox: \"0 0 64 41\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(0 1)\",\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    className: \"\".concat(prefixCls, \"-ellipse\"),\n    cx: \"32\",\n    cy: \"33\",\n    rx: \"32\",\n    ry: \"7\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    className: \"\".concat(prefixCls, \"-g\"),\n    fillRule: \"nonzero\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\",\n    className: \"\".concat(prefixCls, \"-path\")\n  }))));\n};\n\nvar _default = Simple;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AAEtDC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,KAAK,GAAGC,uBAAuB,CAACN,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,eAAe,GAAGP,OAAO,CAAC,oBAAoB,CAAC;AAEnD,SAASQ,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASH,uBAAuBA,CAACO,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAId,OAAO,CAACc,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGlB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACmB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIpB,MAAM,CAACqB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGlB,MAAM,CAACmB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEzB,MAAM,CAACC,cAAc,CAACgB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIS,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,iBAAiB,GAAGvB,KAAK,CAACwB,UAAU,CAACtB,eAAe,CAACuB,aAAa,CAAC;IACnEC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;EAEjD,IAAIC,SAAS,GAAGD,YAAY,CAAC,kBAAkB,CAAC;EAChD,OAAO,aAAa1B,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEF,SAAS;IACpBG,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC,EAAE,aAAajC,KAAK,CAAC4B,aAAa,CAAC,GAAG,EAAE;IACvCM,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAapC,KAAK,CAAC4B,aAAa,CAAC,SAAS,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,UAAU,CAAC;IAC3CW,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAazC,KAAK,CAAC4B,aAAa,CAAC,GAAG,EAAE;IACxCC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,IAAI,CAAC;IACrCS,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAapC,KAAK,CAAC4B,aAAa,CAAC,MAAM,EAAE;IAC1Cc,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAa1C,KAAK,CAAC4B,aAAa,CAAC,MAAM,EAAE;IAC3Cc,CAAC,EAAE,+OAA+O;IAClPb,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,OAAO;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAIgB,QAAQ,GAAGrB,MAAM;AACrBxB,OAAO,CAAC,SAAS,CAAC,GAAG6C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}