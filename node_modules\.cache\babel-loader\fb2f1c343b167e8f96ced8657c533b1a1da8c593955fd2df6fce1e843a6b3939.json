{"ast": null, "code": "import InternalRadio from './radio';\nimport Group from './group';\nimport Button from './radioButton';\nvar Radio = InternalRadio;\nRadio.Button = Button;\nRadio.Group = Group;\nexport { Button, Group };\nexport default Radio;", "map": {"version": 3, "names": ["InternalRadio", "Group", "<PERSON><PERSON>", "Radio"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/radio/index.js"], "sourcesContent": ["import InternalRadio from './radio';\nimport Group from './group';\nimport Button from './radioButton';\nvar Radio = InternalRadio;\nRadio.Button = Button;\nRadio.Group = Group;\nexport { Button, Group };\nexport default Radio;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,eAAe;AAClC,IAAIC,KAAK,GAAGH,aAAa;AACzBG,KAAK,CAACD,MAAM,GAAGA,MAAM;AACrBC,KAAK,CAACF,KAAK,GAAGA,KAAK;AACnB,SAASC,MAAM,EAAED,KAAK;AACtB,eAAeE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}