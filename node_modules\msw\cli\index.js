#!/usr/bin/env node
const yargs = require('yargs')

yargs
  .usage('$0 <cmd> [args]')
  .command(
    'init <publicDir>',
    'Initializes Mock Service Worker at the specified directory',
    (yargs) => {
      yargs
        .positional('publicDir', {
          type: 'string',
          description: 'Relative path to the public directory',
          required: true,
          normalize: true,
        })
        .example('init', 'msw init public')
        .option('save', {
          type: 'boolean',
          description: 'Save the worker directory in your package.json',
        })
    },
    require('./init'),
  )
  .demandCommand()
  .help().argv
