{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport { FormItemPrefixContext } from './context';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nvar EMPTY_LIST = [];\nfunction toErrorEntity(error, errorStatus, prefix) {\n  var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  return {\n    key: typeof error === 'string' ? error : \"\".concat(prefix, \"-\").concat(index),\n    error: error,\n    errorStatus: errorStatus\n  };\n}\nexport default function ErrorList(_ref) {\n  var help = _ref.help,\n    helpStatus = _ref.helpStatus,\n    _ref$errors = _ref.errors,\n    errors = _ref$errors === void 0 ? EMPTY_LIST : _ref$errors,\n    _ref$warnings = _ref.warnings,\n    warnings = _ref$warnings === void 0 ? EMPTY_LIST : _ref$warnings,\n    rootClassName = _ref.className;\n  var _React$useContext = React.useContext(FormItemPrefixContext),\n    prefixCls = _React$useContext.prefixCls;\n  var _React$useContext2 = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext2.getPrefixCls;\n  var baseClassName = \"\".concat(prefixCls, \"-item-explain\");\n  var rootPrefixCls = getPrefixCls();\n  var fullKeyList = React.useMemo(function () {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, helpStatus, 'help')];\n    }\n    return [].concat(_toConsumableArray(errors.map(function (error, index) {\n      return toErrorEntity(error, 'error', 'error', index);\n    })), _toConsumableArray(warnings.map(function (warning, index) {\n      return toErrorEntity(warning, 'warning', 'warning', index);\n    })));\n  }, [help, helpStatus, errors, warnings]);\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, collapseMotion, {\n    motionName: \"\".concat(rootPrefixCls, \"-show-help\"),\n    motionAppear: false,\n    motionEnter: false,\n    visible: !!fullKeyList.length,\n    onLeaveStart: function onLeaveStart(node) {\n      // Force disable css override style in index.less configured\n      node.style.height = 'auto';\n      return {\n        height: node.offsetHeight\n      };\n    }\n  }), function (holderProps) {\n    var holderClassName = holderProps.className,\n      holderStyle = holderProps.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(baseClassName, holderClassName, rootClassName),\n      style: holderStyle\n    }, /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n      keys: fullKeyList\n    }, collapseMotion, {\n      motionName: \"\".concat(rootPrefixCls, \"-show-help-item\"),\n      component: false\n    }), function (itemProps) {\n      var key = itemProps.key,\n        error = itemProps.error,\n        errorStatus = itemProps.errorStatus,\n        itemClassName = itemProps.className,\n        itemStyle = itemProps.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        role: \"alert\",\n        className: classNames(itemClassName, _defineProperty({}, \"\".concat(baseClassName, \"-\").concat(errorStatus), errorStatus)),\n        style: itemStyle\n      }, error);\n    }));\n  });\n}", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_toConsumableArray", "React", "classNames", "CSSMotion", "CSSMotionList", "FormItemPrefixContext", "ConfigContext", "collapseMotion", "EMPTY_LIST", "toErrorEntity", "error", "errorStatus", "prefix", "index", "arguments", "length", "undefined", "key", "concat", "ErrorList", "_ref", "help", "helpStatus", "_ref$errors", "errors", "_ref$warnings", "warnings", "rootClassName", "className", "_React$useContext", "useContext", "prefixCls", "_React$useContext2", "getPrefixCls", "baseClassName", "rootPrefixCls", "fullKeyList", "useMemo", "map", "warning", "createElement", "motionName", "motionAppear", "motionEnter", "visible", "onLeaveStart", "node", "style", "height", "offsetHeight", "holderProps", "holderClassName", "holder<PERSON>tyle", "keys", "component", "itemProps", "itemClassName", "itemStyle", "role"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/ErrorList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport { FormItemPrefixContext } from './context';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nvar EMPTY_LIST = [];\n\nfunction toErrorEntity(error, errorStatus, prefix) {\n  var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  return {\n    key: typeof error === 'string' ? error : \"\".concat(prefix, \"-\").concat(index),\n    error: error,\n    errorStatus: errorStatus\n  };\n}\n\nexport default function ErrorList(_ref) {\n  var help = _ref.help,\n      helpStatus = _ref.helpStatus,\n      _ref$errors = _ref.errors,\n      errors = _ref$errors === void 0 ? EMPTY_LIST : _ref$errors,\n      _ref$warnings = _ref.warnings,\n      warnings = _ref$warnings === void 0 ? EMPTY_LIST : _ref$warnings,\n      rootClassName = _ref.className;\n\n  var _React$useContext = React.useContext(FormItemPrefixContext),\n      prefixCls = _React$useContext.prefixCls;\n\n  var _React$useContext2 = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext2.getPrefixCls;\n\n  var baseClassName = \"\".concat(prefixCls, \"-item-explain\");\n  var rootPrefixCls = getPrefixCls();\n  var fullKeyList = React.useMemo(function () {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, helpStatus, 'help')];\n    }\n\n    return [].concat(_toConsumableArray(errors.map(function (error, index) {\n      return toErrorEntity(error, 'error', 'error', index);\n    })), _toConsumableArray(warnings.map(function (warning, index) {\n      return toErrorEntity(warning, 'warning', 'warning', index);\n    })));\n  }, [help, helpStatus, errors, warnings]);\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, collapseMotion, {\n    motionName: \"\".concat(rootPrefixCls, \"-show-help\"),\n    motionAppear: false,\n    motionEnter: false,\n    visible: !!fullKeyList.length,\n    onLeaveStart: function onLeaveStart(node) {\n      // Force disable css override style in index.less configured\n      node.style.height = 'auto';\n      return {\n        height: node.offsetHeight\n      };\n    }\n  }), function (holderProps) {\n    var holderClassName = holderProps.className,\n        holderStyle = holderProps.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(baseClassName, holderClassName, rootClassName),\n      style: holderStyle\n    }, /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n      keys: fullKeyList\n    }, collapseMotion, {\n      motionName: \"\".concat(rootPrefixCls, \"-show-help-item\"),\n      component: false\n    }), function (itemProps) {\n      var key = itemProps.key,\n          error = itemProps.error,\n          errorStatus = itemProps.errorStatus,\n          itemClassName = itemProps.className,\n          itemStyle = itemProps.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        role: \"alert\",\n        className: classNames(itemClassName, _defineProperty({}, \"\".concat(baseClassName, \"-\").concat(errorStatus), errorStatus)),\n        style: itemStyle\n      }, error);\n    }));\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,IAAIC,aAAa,QAAQ,WAAW;AACpD,SAASC,qBAAqB,QAAQ,WAAW;AACjD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,IAAIC,UAAU,GAAG,EAAE;AAEnB,SAASC,aAAaA,CAACC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAE;EACjD,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjF,OAAO;IACLG,GAAG,EAAE,OAAOP,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE,CAACQ,MAAM,CAACN,MAAM,EAAE,GAAG,CAAC,CAACM,MAAM,CAACL,KAAK,CAAC;IAC7EH,KAAK,EAAEA,KAAK;IACZC,WAAW,EAAEA;EACf,CAAC;AACH;AAEA,eAAe,SAASQ,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,WAAW,GAAGH,IAAI,CAACI,MAAM;IACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGf,UAAU,GAAGe,WAAW;IAC1DE,aAAa,GAAGL,IAAI,CAACM,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGjB,UAAU,GAAGiB,aAAa;IAChEE,aAAa,GAAGP,IAAI,CAACQ,SAAS;EAElC,IAAIC,iBAAiB,GAAG5B,KAAK,CAAC6B,UAAU,CAACzB,qBAAqB,CAAC;IAC3D0B,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EAE3C,IAAIC,kBAAkB,GAAG/B,KAAK,CAAC6B,UAAU,CAACxB,aAAa,CAAC;IACpD2B,YAAY,GAAGD,kBAAkB,CAACC,YAAY;EAElD,IAAIC,aAAa,GAAG,EAAE,CAAChB,MAAM,CAACa,SAAS,EAAE,eAAe,CAAC;EACzD,IAAII,aAAa,GAAGF,YAAY,CAAC,CAAC;EAClC,IAAIG,WAAW,GAAGnC,KAAK,CAACoC,OAAO,CAAC,YAAY;IAC1C,IAAIhB,IAAI,KAAKL,SAAS,IAAIK,IAAI,KAAK,IAAI,EAAE;MACvC,OAAO,CAACZ,aAAa,CAACY,IAAI,EAAEC,UAAU,EAAE,MAAM,CAAC,CAAC;IAClD;IAEA,OAAO,EAAE,CAACJ,MAAM,CAAClB,kBAAkB,CAACwB,MAAM,CAACc,GAAG,CAAC,UAAU5B,KAAK,EAAEG,KAAK,EAAE;MACrE,OAAOJ,aAAa,CAACC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAEG,KAAK,CAAC;IACtD,CAAC,CAAC,CAAC,EAAEb,kBAAkB,CAAC0B,QAAQ,CAACY,GAAG,CAAC,UAAUC,OAAO,EAAE1B,KAAK,EAAE;MAC7D,OAAOJ,aAAa,CAAC8B,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE1B,KAAK,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,CAACQ,IAAI,EAAEC,UAAU,EAAEE,MAAM,EAAEE,QAAQ,CAAC,CAAC;EACxC,OAAO,aAAazB,KAAK,CAACuC,aAAa,CAACrC,SAAS,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAES,cAAc,EAAE;IAC9EkC,UAAU,EAAE,EAAE,CAACvB,MAAM,CAACiB,aAAa,EAAE,YAAY,CAAC;IAClDO,YAAY,EAAE,KAAK;IACnBC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE,CAAC,CAACR,WAAW,CAACrB,MAAM;IAC7B8B,YAAY,EAAE,SAASA,YAAYA,CAACC,IAAI,EAAE;MACxC;MACAA,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;MAC1B,OAAO;QACLA,MAAM,EAAEF,IAAI,CAACG;MACf,CAAC;IACH;EACF,CAAC,CAAC,EAAE,UAAUC,WAAW,EAAE;IACzB,IAAIC,eAAe,GAAGD,WAAW,CAACtB,SAAS;MACvCwB,WAAW,GAAGF,WAAW,CAACH,KAAK;IACnC,OAAO,aAAa9C,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;MAC7CZ,SAAS,EAAE1B,UAAU,CAACgC,aAAa,EAAEiB,eAAe,EAAExB,aAAa,CAAC;MACpEoB,KAAK,EAAEK;IACT,CAAC,EAAE,aAAanD,KAAK,CAACuC,aAAa,CAACpC,aAAa,EAAEN,QAAQ,CAAC;MAC1DuD,IAAI,EAAEjB;IACR,CAAC,EAAE7B,cAAc,EAAE;MACjBkC,UAAU,EAAE,EAAE,CAACvB,MAAM,CAACiB,aAAa,EAAE,iBAAiB,CAAC;MACvDmB,SAAS,EAAE;IACb,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;MACvB,IAAItC,GAAG,GAAGsC,SAAS,CAACtC,GAAG;QACnBP,KAAK,GAAG6C,SAAS,CAAC7C,KAAK;QACvBC,WAAW,GAAG4C,SAAS,CAAC5C,WAAW;QACnC6C,aAAa,GAAGD,SAAS,CAAC3B,SAAS;QACnC6B,SAAS,GAAGF,SAAS,CAACR,KAAK;MAC/B,OAAO,aAAa9C,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;QAC7CvB,GAAG,EAAEA,GAAG;QACRyC,IAAI,EAAE,OAAO;QACb9B,SAAS,EAAE1B,UAAU,CAACsD,aAAa,EAAEzD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmB,MAAM,CAACgB,aAAa,EAAE,GAAG,CAAC,CAAChB,MAAM,CAACP,WAAW,CAAC,EAAEA,WAAW,CAAC,CAAC;QACzHoC,KAAK,EAAEU;MACT,CAAC,EAAE/C,KAAK,CAAC;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}