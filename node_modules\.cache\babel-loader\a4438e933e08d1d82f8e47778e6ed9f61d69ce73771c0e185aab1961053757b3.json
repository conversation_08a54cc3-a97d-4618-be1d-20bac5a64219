{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = createUseMessage;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useNotification = _interopRequireDefault(require(\"rc-notification/lib/useNotification\"));\nvar _configProvider = require(\"../../config-provider\");\nvar _ = require(\"..\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction createUseMessage(getRcNotificationInstance, getRCNoticeProps) {\n  var useMessage = function useMessage() {\n    // We can only get content by render\n    var getPrefixCls;\n    var getPopupContainer; // We create a proxy to handle delay created instance\n\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = (0, _useNotification[\"default\"])(proxy),\n      _useRCNotification2 = (0, _slicedToArray2[\"default\"])(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('message', customizePrefixCls);\n      var rootPrefixCls = getPrefixCls();\n      var target = args.key || (0, _.getKeyThenIncreaseKey)();\n      var closePromise = new Promise(function (resolve) {\n        var callback = function callback() {\n          if (typeof args.onClose === 'function') {\n            args.onClose();\n          }\n          return resolve(true);\n        };\n        getRcNotificationInstance((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n          prefixCls: mergedPrefixCls,\n          rootPrefixCls: rootPrefixCls,\n          getPopupContainer: getPopupContainer\n        }), function (_ref) {\n          var prefixCls = _ref.prefixCls,\n            instance = _ref.instance;\n          innerInstance = instance;\n          hookNotify(getRCNoticeProps((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n            key: target,\n            onClose: callback\n          }), prefixCls));\n        });\n      });\n      var result = function result() {\n        if (innerInstance) {\n          innerInstance.removeNotice(target);\n        }\n      };\n      result.then = function (filled, rejected) {\n        return closePromise.then(filled, rejected);\n      };\n      result.promise = closePromise;\n      return result;\n    } // Fill functions\n\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    _.typeList.forEach(function (type) {\n      return (0, _.attachTypeApi)(hookApiRef.current, type);\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(_configProvider.ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      getPopupContainer = context.getPopupContainer;\n      return holder;\n    })];\n  };\n  return useMessage;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "createUseMessage", "_extends2", "_slicedToArray2", "React", "_interopRequireWildcard", "_useNotification", "_config<PERSON><PERSON><PERSON>", "_", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "getRcNotificationInstance", "getRCNoticeProps", "useMessage", "getPrefixCls", "getPopupContainer", "innerInstance", "proxy", "add", "noticeProps", "<PERSON><PERSON><PERSON><PERSON>", "component", "_useRCNotification", "_useRCNotification2", "hookNotify", "holder", "notify", "args", "customizePrefixCls", "prefixCls", "mergedPrefixCls", "rootPrefixCls", "target", "getKeyThenIncreaseKey", "closePromise", "Promise", "resolve", "callback", "onClose", "_ref", "instance", "result", "removeNotice", "then", "filled", "rejected", "promise", "hookApiRef", "useRef", "current", "open", "typeList", "for<PERSON>ach", "type", "attachTypeApi", "createElement", "ConfigConsumer", "context"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/message/hooks/useMessage.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = createUseMessage;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _useNotification = _interopRequireDefault(require(\"rc-notification/lib/useNotification\"));\n\nvar _configProvider = require(\"../../config-provider\");\n\nvar _ = require(\"..\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction createUseMessage(getRcNotificationInstance, getRCNoticeProps) {\n  var useMessage = function useMessage() {\n    // We can only get content by render\n    var getPrefixCls;\n    var getPopupContainer; // We create a proxy to handle delay created instance\n\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n\n    var _useRCNotification = (0, _useNotification[\"default\"])(proxy),\n        _useRCNotification2 = (0, _slicedToArray2[\"default\"])(_useRCNotification, 2),\n        hookNotify = _useRCNotification2[0],\n        holder = _useRCNotification2[1];\n\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('message', customizePrefixCls);\n      var rootPrefixCls = getPrefixCls();\n      var target = args.key || (0, _.getKeyThenIncreaseKey)();\n      var closePromise = new Promise(function (resolve) {\n        var callback = function callback() {\n          if (typeof args.onClose === 'function') {\n            args.onClose();\n          }\n\n          return resolve(true);\n        };\n\n        getRcNotificationInstance((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n          prefixCls: mergedPrefixCls,\n          rootPrefixCls: rootPrefixCls,\n          getPopupContainer: getPopupContainer\n        }), function (_ref) {\n          var prefixCls = _ref.prefixCls,\n              instance = _ref.instance;\n          innerInstance = instance;\n          hookNotify(getRCNoticeProps((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n            key: target,\n            onClose: callback\n          }), prefixCls));\n        });\n      });\n\n      var result = function result() {\n        if (innerInstance) {\n          innerInstance.removeNotice(target);\n        }\n      };\n\n      result.then = function (filled, rejected) {\n        return closePromise.then(filled, rejected);\n      };\n\n      result.promise = closePromise;\n      return result;\n    } // Fill functions\n\n\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n\n    _.typeList.forEach(function (type) {\n      return (0, _.attachTypeApi)(hookApiRef.current, type);\n    });\n\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(_configProvider.ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      getPopupContainer = context.getPopupContainer;\n      return holder;\n    })];\n  };\n\n  return useMessage;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,gBAAgB;AAErC,IAAIC,SAAS,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIQ,eAAe,GAAGT,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE7F,IAAIS,KAAK,GAAGC,uBAAuB,CAACV,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIW,gBAAgB,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE7F,IAAIY,eAAe,GAAGZ,OAAO,CAAC,uBAAuB,CAAC;AAEtD,IAAIa,CAAC,GAAGb,OAAO,CAAC,IAAI,CAAC;AAErB,SAASc,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASL,uBAAuBA,CAACS,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIlB,OAAO,CAACkB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGvB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACwB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIzB,MAAM,CAAC0B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGvB,MAAM,CAACwB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE9B,MAAM,CAACC,cAAc,CAACqB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,SAASlB,gBAAgBA,CAAC2B,yBAAyB,EAAEC,gBAAgB,EAAE;EACrE,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC;IACA,IAAIC,YAAY;IAChB,IAAIC,iBAAiB,CAAC,CAAC;;IAEvB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,KAAK,GAAG;MACVC,GAAG,EAAE,SAASA,GAAGA,CAACC,WAAW,EAAEC,cAAc,EAAE;QAC7CJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,SAAS,CAACH,GAAG,CAACC,WAAW,EAAEC,cAAc,CAAC;MACxH;IACF,CAAC;IAED,IAAIE,kBAAkB,GAAG,CAAC,CAAC,EAAEjC,gBAAgB,CAAC,SAAS,CAAC,EAAE4B,KAAK,CAAC;MAC5DM,mBAAmB,GAAG,CAAC,CAAC,EAAErC,eAAe,CAAC,SAAS,CAAC,EAAEoC,kBAAkB,EAAE,CAAC,CAAC;MAC5EE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;MACnCE,MAAM,GAAGF,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAASG,MAAMA,CAACC,IAAI,EAAE;MACpB,IAAIC,kBAAkB,GAAGD,IAAI,CAACE,SAAS;MACvC,IAAIC,eAAe,GAAGhB,YAAY,CAAC,SAAS,EAAEc,kBAAkB,CAAC;MACjE,IAAIG,aAAa,GAAGjB,YAAY,CAAC,CAAC;MAClC,IAAIkB,MAAM,GAAGL,IAAI,CAACtB,GAAG,IAAI,CAAC,CAAC,EAAEd,CAAC,CAAC0C,qBAAqB,EAAE,CAAC;MACvD,IAAIC,YAAY,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;QAChD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;UACjC,IAAI,OAAOV,IAAI,CAACW,OAAO,KAAK,UAAU,EAAE;YACtCX,IAAI,CAACW,OAAO,CAAC,CAAC;UAChB;UAEA,OAAOF,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QAEDzB,yBAAyB,CAAC,CAAC,CAAC,EAAE1B,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0C,IAAI,CAAC,EAAE;UACvFE,SAAS,EAAEC,eAAe;UAC1BC,aAAa,EAAEA,aAAa;UAC5BhB,iBAAiB,EAAEA;QACrB,CAAC,CAAC,EAAE,UAAUwB,IAAI,EAAE;UAClB,IAAIV,SAAS,GAAGU,IAAI,CAACV,SAAS;YAC1BW,QAAQ,GAAGD,IAAI,CAACC,QAAQ;UAC5BxB,aAAa,GAAGwB,QAAQ;UACxBhB,UAAU,CAACZ,gBAAgB,CAAC,CAAC,CAAC,EAAE3B,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0C,IAAI,CAAC,EAAE;YACzFtB,GAAG,EAAE2B,MAAM;YACXM,OAAO,EAAED;UACX,CAAC,CAAC,EAAER,SAAS,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAIY,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QAC7B,IAAIzB,aAAa,EAAE;UACjBA,aAAa,CAAC0B,YAAY,CAACV,MAAM,CAAC;QACpC;MACF,CAAC;MAEDS,MAAM,CAACE,IAAI,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAE;QACxC,OAAOX,YAAY,CAACS,IAAI,CAACC,MAAM,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MAEDJ,MAAM,CAACK,OAAO,GAAGZ,YAAY;MAC7B,OAAOO,MAAM;IACf,CAAC,CAAC;;IAGF,IAAIM,UAAU,GAAG5D,KAAK,CAAC6D,MAAM,CAAC,CAAC,CAAC,CAAC;IACjCD,UAAU,CAACE,OAAO,CAACC,IAAI,GAAGxB,MAAM;IAEhCnC,CAAC,CAAC4D,QAAQ,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACjC,OAAO,CAAC,CAAC,EAAE9D,CAAC,CAAC+D,aAAa,EAAEP,UAAU,CAACE,OAAO,EAAEI,IAAI,CAAC;IACvD,CAAC,CAAC;IAEF,OAAO,CAACN,UAAU,CAACE,OAAO,EAAE,aAAa9D,KAAK,CAACoE,aAAa,CAACjE,eAAe,CAACkE,cAAc,EAAE;MAC3FnD,GAAG,EAAE;IACP,CAAC,EAAE,UAAUoD,OAAO,EAAE;MACpB3C,YAAY,GAAG2C,OAAO,CAAC3C,YAAY;MACnCC,iBAAiB,GAAG0C,OAAO,CAAC1C,iBAAiB;MAC7C,OAAOU,MAAM;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAOZ,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}