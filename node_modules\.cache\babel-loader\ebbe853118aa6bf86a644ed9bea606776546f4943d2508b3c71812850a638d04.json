{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport TextArea from 'rc-textarea';\nimport KeywordTrigger from './KeywordTrigger';\nimport { MentionsContextProvider } from './MentionsContext';\nimport Option from './Option';\nimport { filterOption as defaultFilterOption, getBeforeSelectionText, getLastMeasureIndex, omit, replaceWithMeasure, setInputSelection, validateSearch as defaultValidateSearch } from './util';\nvar Mentions = /*#__PURE__*/function (_React$Component) {\n  _inherits(Mentions, _React$Component);\n  var _super = _createSuper(Mentions);\n  function Mentions(props) {\n    var _this;\n    _classCallCheck(this, Mentions);\n    _this = _super.call(this, props);\n    _this.focusId = undefined;\n    _this.triggerChange = function (value) {\n      var onChange = _this.props.onChange;\n      if (!('value' in _this.props)) {\n        _this.setState({\n          value: value\n        });\n      }\n      if (onChange) {\n        onChange(value);\n      }\n    };\n    _this.onChange = function (_ref) {\n      var value = _ref.target.value;\n      _this.triggerChange(value);\n    }; // Check if hit the measure keyword\n\n    _this.onKeyDown = function (event) {\n      var which = event.which;\n      var _this$state = _this.state,\n        activeIndex = _this$state.activeIndex,\n        measuring = _this$state.measuring;\n      var clientOnKeyDown = _this.props.onKeyDown;\n      if (clientOnKeyDown) {\n        clientOnKeyDown(event);\n      } // Skip if not measuring\n\n      if (!measuring) {\n        return;\n      }\n      if (which === KeyCode.UP || which === KeyCode.DOWN) {\n        // Control arrow function\n        var optionLen = _this.getOptions().length;\n        var offset = which === KeyCode.UP ? -1 : 1;\n        var newActiveIndex = (activeIndex + offset + optionLen) % optionLen;\n        _this.setState({\n          activeIndex: newActiveIndex\n        });\n        event.preventDefault();\n      } else if (which === KeyCode.ESC) {\n        _this.stopMeasure();\n      } else if (which === KeyCode.ENTER) {\n        // Measure hit\n        event.preventDefault();\n        var options = _this.getOptions();\n        if (!options.length) {\n          _this.stopMeasure();\n          return;\n        }\n        var option = options[activeIndex];\n        _this.selectOption(option);\n      }\n    };\n    /**\n     * When to start measure:\n     * 1. When user press `prefix`\n     * 2. When measureText !== prevMeasureText\n     *  - If measure hit\n     *  - If measuring\n     *\n     * When to stop measure:\n     * 1. Selection is out of range\n     * 2. Contains `space`\n     * 3. ESC or select one\n     */\n\n    _this.onKeyUp = function (event) {\n      var key = event.key,\n        which = event.which;\n      var _this$state2 = _this.state,\n        prevMeasureText = _this$state2.measureText,\n        measuring = _this$state2.measuring;\n      var _this$props = _this.props,\n        _this$props$prefix = _this$props.prefix,\n        prefix = _this$props$prefix === void 0 ? '' : _this$props$prefix,\n        clientOnKeyUp = _this$props.onKeyUp,\n        onSearch = _this$props.onSearch,\n        validateSearch = _this$props.validateSearch;\n      var target = event.target;\n      var selectionStartText = getBeforeSelectionText(target);\n      var _getLastMeasureIndex = getLastMeasureIndex(selectionStartText, prefix),\n        measureIndex = _getLastMeasureIndex.location,\n        measurePrefix = _getLastMeasureIndex.prefix; // If the client implements an onKeyUp handler, call it\n\n      if (clientOnKeyUp) {\n        clientOnKeyUp(event);\n      } // Skip if match the white key list\n\n      if ([KeyCode.ESC, KeyCode.UP, KeyCode.DOWN, KeyCode.ENTER].indexOf(which) !== -1) {\n        return;\n      }\n      if (measureIndex !== -1) {\n        var measureText = selectionStartText.slice(measureIndex + measurePrefix.length);\n        var validateMeasure = validateSearch(measureText, _this.props);\n        var matchOption = !!_this.getOptions(measureText).length;\n        if (validateMeasure) {\n          if (key === measurePrefix || key === 'Shift' || measuring || measureText !== prevMeasureText && matchOption) {\n            _this.startMeasure(measureText, measurePrefix, measureIndex);\n          }\n        } else if (measuring) {\n          // Stop if measureText is invalidate\n          _this.stopMeasure();\n        }\n        /**\n         * We will trigger `onSearch` to developer since they may use for async update.\n         * If met `space` means user finished searching.\n         */\n\n        if (onSearch && validateMeasure) {\n          onSearch(measureText, measurePrefix);\n        }\n      } else if (measuring) {\n        _this.stopMeasure();\n      }\n    };\n    _this.onPressEnter = function (event) {\n      var measuring = _this.state.measuring;\n      var onPressEnter = _this.props.onPressEnter;\n      if (!measuring && onPressEnter) {\n        onPressEnter(event);\n      }\n    };\n    _this.onInputFocus = function (event) {\n      _this.onFocus(event);\n    };\n    _this.onInputBlur = function (event) {\n      _this.onBlur(event);\n    };\n    _this.onDropdownFocus = function () {\n      _this.onFocus();\n    };\n    _this.onDropdownBlur = function () {\n      _this.onBlur();\n    };\n    _this.onFocus = function (event) {\n      window.clearTimeout(_this.focusId);\n      var isFocus = _this.state.isFocus;\n      var onFocus = _this.props.onFocus;\n      if (!isFocus && event && onFocus) {\n        onFocus(event);\n      }\n      _this.setState({\n        isFocus: true\n      });\n    };\n    _this.onBlur = function (event) {\n      _this.focusId = window.setTimeout(function () {\n        var onBlur = _this.props.onBlur;\n        _this.setState({\n          isFocus: false\n        });\n        _this.stopMeasure();\n        if (onBlur) {\n          onBlur(event);\n        }\n      }, 0);\n    };\n    _this.selectOption = function (option) {\n      var _this$state3 = _this.state,\n        value = _this$state3.value,\n        measureLocation = _this$state3.measureLocation,\n        measurePrefix = _this$state3.measurePrefix;\n      var _this$props2 = _this.props,\n        split = _this$props2.split,\n        onSelect = _this$props2.onSelect;\n      var _option$value = option.value,\n        mentionValue = _option$value === void 0 ? '' : _option$value;\n      var _replaceWithMeasure = replaceWithMeasure(value, {\n          measureLocation: measureLocation,\n          targetText: mentionValue,\n          prefix: measurePrefix,\n          selectionStart: _this.textarea.selectionStart,\n          split: split\n        }),\n        text = _replaceWithMeasure.text,\n        selectionLocation = _replaceWithMeasure.selectionLocation;\n      _this.triggerChange(text);\n      _this.stopMeasure(function () {\n        // We need restore the selection position\n        setInputSelection(_this.textarea, selectionLocation);\n      });\n      if (onSelect) {\n        onSelect(option, measurePrefix);\n      }\n    };\n    _this.setActiveIndex = function (activeIndex) {\n      _this.setState({\n        activeIndex: activeIndex\n      });\n    };\n    _this.setTextAreaRef = function (element) {\n      var _element$resizableTex;\n      _this.textarea = element === null || element === void 0 ? void 0 : (_element$resizableTex = element.resizableTextArea) === null || _element$resizableTex === void 0 ? void 0 : _element$resizableTex.textArea;\n    };\n    _this.setMeasureRef = function (element) {\n      _this.measure = element;\n    };\n    _this.getOptions = function (measureText) {\n      var targetMeasureText = measureText || _this.state.measureText || '';\n      var _this$props3 = _this.props,\n        children = _this$props3.children,\n        filterOption = _this$props3.filterOption;\n      var list = toArray(children).map(function (_ref2) {\n        var props = _ref2.props,\n          key = _ref2.key;\n        return _objectSpread(_objectSpread({}, props), {}, {\n          key: key || props.value\n        });\n      }).filter(function (option) {\n        /** Return all result if `filterOption` is false. */\n        if (filterOption === false) {\n          return true;\n        }\n        return filterOption(targetMeasureText, option);\n      });\n      return list;\n    };\n    _this.state = {\n      value: props.defaultValue || props.value || '',\n      measuring: false,\n      measureLocation: 0,\n      measureText: null,\n      measurePrefix: '',\n      activeIndex: 0,\n      isFocus: false\n    };\n    return _this;\n  }\n  _createClass(Mentions, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      var measuring = this.state.measuring; // Sync measure div top with textarea for rc-trigger usage\n\n      if (measuring) {\n        this.measure.scrollTop = this.textarea.scrollTop;\n      }\n    }\n  }, {\n    key: \"startMeasure\",\n    value: function startMeasure(measureText, measurePrefix, measureLocation) {\n      this.setState({\n        measuring: true,\n        measureText: measureText,\n        measurePrefix: measurePrefix,\n        measureLocation: measureLocation,\n        activeIndex: 0\n      });\n    }\n  }, {\n    key: \"stopMeasure\",\n    value: function stopMeasure(callback) {\n      this.setState({\n        measuring: false,\n        measureLocation: 0,\n        measureText: null\n      }, callback);\n    }\n  }, {\n    key: \"focus\",\n    value: function focus() {\n      this.textarea.focus();\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.textarea.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state4 = this.state,\n        value = _this$state4.value,\n        measureLocation = _this$state4.measureLocation,\n        measurePrefix = _this$state4.measurePrefix,\n        measuring = _this$state4.measuring,\n        activeIndex = _this$state4.activeIndex;\n      var _this$props4 = this.props,\n        prefixCls = _this$props4.prefixCls,\n        placement = _this$props4.placement,\n        direction = _this$props4.direction,\n        transitionName = _this$props4.transitionName,\n        className = _this$props4.className,\n        style = _this$props4.style,\n        autoFocus = _this$props4.autoFocus,\n        notFoundContent = _this$props4.notFoundContent,\n        getPopupContainer = _this$props4.getPopupContainer,\n        dropdownClassName = _this$props4.dropdownClassName,\n        restProps = _objectWithoutProperties(_this$props4, [\"prefixCls\", \"placement\", \"direction\", \"transitionName\", \"className\", \"style\", \"autoFocus\", \"notFoundContent\", \"getPopupContainer\", \"dropdownClassName\"]);\n      var inputProps = omit(restProps, 'value', 'defaultValue', 'prefix', 'split', 'children', 'validateSearch', 'filterOption', 'onSelect', 'onSearch');\n      var options = measuring ? this.getOptions() : [];\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(prefixCls, className),\n        style: style\n      }, /*#__PURE__*/React.createElement(TextArea, _extends({\n        autoFocus: autoFocus,\n        ref: this.setTextAreaRef,\n        value: value\n      }, inputProps, {\n        onChange: this.onChange,\n        onKeyDown: this.onKeyDown,\n        onKeyUp: this.onKeyUp,\n        onPressEnter: this.onPressEnter,\n        onFocus: this.onInputFocus,\n        onBlur: this.onInputBlur\n      })), measuring && /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.setMeasureRef,\n        className: \"\".concat(prefixCls, \"-measure\")\n      }, value.slice(0, measureLocation), /*#__PURE__*/React.createElement(MentionsContextProvider, {\n        value: {\n          notFoundContent: notFoundContent,\n          activeIndex: activeIndex,\n          setActiveIndex: this.setActiveIndex,\n          selectOption: this.selectOption,\n          onFocus: this.onDropdownFocus,\n          onBlur: this.onDropdownBlur\n        }\n      }, /*#__PURE__*/React.createElement(KeywordTrigger, {\n        prefixCls: prefixCls,\n        transitionName: transitionName,\n        placement: placement,\n        direction: direction,\n        options: options,\n        visible: true,\n        getPopupContainer: getPopupContainer,\n        dropdownClassName: dropdownClassName\n      }, /*#__PURE__*/React.createElement(\"span\", null, measurePrefix))), value.slice(measureLocation + measurePrefix.length)));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var newState = {};\n      if ('value' in props && props.value !== prevState.value) {\n        newState.value = props.value || '';\n      }\n      return newState;\n    }\n  }]);\n  return Mentions;\n}(React.Component);\nMentions.Option = Option;\nMentions.defaultProps = {\n  prefixCls: 'rc-mentions',\n  prefix: '@',\n  split: ' ',\n  validateSearch: defaultValidateSearch,\n  filterOption: defaultFilterOption,\n  notFoundContent: 'Not Found',\n  rows: 1\n};\nexport default Mentions;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "classNames", "toArray", "KeyCode", "React", "TextArea", "KeywordTrigger", "MentionsContextProvider", "Option", "filterOption", "defaultFilterOption", "getBeforeSelectionText", "getLastMeasureIndex", "omit", "replaceWithMeasure", "setInputSelection", "validateSearch", "defaultValidateSearch", "Mentions", "_React$Component", "_super", "props", "_this", "call", "focusId", "undefined", "trigger<PERSON>hange", "value", "onChange", "setState", "_ref", "target", "onKeyDown", "event", "which", "_this$state", "state", "activeIndex", "measuring", "clientOnKeyDown", "UP", "DOWN", "optionLen", "getOptions", "length", "offset", "newActiveIndex", "preventDefault", "ESC", "stopMeasure", "ENTER", "options", "option", "selectOption", "onKeyUp", "key", "_this$state2", "prevMeasureText", "measureText", "_this$props", "_this$props$prefix", "prefix", "clientOnKeyUp", "onSearch", "selectionStartText", "_getLastMeasureIndex", "measureIndex", "location", "measurePrefix", "indexOf", "slice", "validateMeasure", "matchOption", "startMeasure", "onPressEnter", "onInputFocus", "onFocus", "onInputBlur", "onBlur", "onDropdownFocus", "onDropdownBlur", "window", "clearTimeout", "isFocus", "setTimeout", "_this$state3", "measureLocation", "_this$props2", "split", "onSelect", "_option$value", "mentionValue", "_replaceWithMeasure", "targetText", "selectionStart", "textarea", "text", "selectionLocation", "setActiveIndex", "setTextAreaRef", "element", "_element$resizableTex", "resizableTextArea", "textArea", "setMeasureRef", "measure", "targetMeasureText", "_this$props3", "children", "list", "map", "_ref2", "filter", "defaultValue", "componentDidUpdate", "scrollTop", "callback", "focus", "blur", "render", "_this$state4", "_this$props4", "prefixCls", "placement", "direction", "transitionName", "className", "style", "autoFocus", "notFoundContent", "getPopupContainer", "dropdownClassName", "restProps", "inputProps", "createElement", "ref", "concat", "visible", "getDerivedStateFromProps", "prevState", "newState", "Component", "defaultProps", "rows"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-mentions/es/Mentions.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport TextArea from 'rc-textarea';\nimport KeywordTrigger from './KeywordTrigger';\nimport { MentionsContextProvider } from './MentionsContext';\nimport Option from './Option';\nimport { filterOption as defaultFilterOption, getBeforeSelectionText, getLastMeasureIndex, omit, replaceWithMeasure, setInputSelection, validateSearch as defaultValidateSearch } from './util';\n\nvar Mentions = /*#__PURE__*/function (_React$Component) {\n  _inherits(Mentions, _React$Component);\n\n  var _super = _createSuper(Mentions);\n\n  function Mentions(props) {\n    var _this;\n\n    _classCallCheck(this, Mentions);\n\n    _this = _super.call(this, props);\n    _this.focusId = undefined;\n\n    _this.triggerChange = function (value) {\n      var onChange = _this.props.onChange;\n\n      if (!('value' in _this.props)) {\n        _this.setState({\n          value: value\n        });\n      }\n\n      if (onChange) {\n        onChange(value);\n      }\n    };\n\n    _this.onChange = function (_ref) {\n      var value = _ref.target.value;\n\n      _this.triggerChange(value);\n    }; // Check if hit the measure keyword\n\n\n    _this.onKeyDown = function (event) {\n      var which = event.which;\n      var _this$state = _this.state,\n          activeIndex = _this$state.activeIndex,\n          measuring = _this$state.measuring;\n      var clientOnKeyDown = _this.props.onKeyDown;\n\n      if (clientOnKeyDown) {\n        clientOnKeyDown(event);\n      } // Skip if not measuring\n\n\n      if (!measuring) {\n        return;\n      }\n\n      if (which === KeyCode.UP || which === KeyCode.DOWN) {\n        // Control arrow function\n        var optionLen = _this.getOptions().length;\n\n        var offset = which === KeyCode.UP ? -1 : 1;\n        var newActiveIndex = (activeIndex + offset + optionLen) % optionLen;\n\n        _this.setState({\n          activeIndex: newActiveIndex\n        });\n\n        event.preventDefault();\n      } else if (which === KeyCode.ESC) {\n        _this.stopMeasure();\n      } else if (which === KeyCode.ENTER) {\n        // Measure hit\n        event.preventDefault();\n\n        var options = _this.getOptions();\n\n        if (!options.length) {\n          _this.stopMeasure();\n\n          return;\n        }\n\n        var option = options[activeIndex];\n\n        _this.selectOption(option);\n      }\n    };\n    /**\n     * When to start measure:\n     * 1. When user press `prefix`\n     * 2. When measureText !== prevMeasureText\n     *  - If measure hit\n     *  - If measuring\n     *\n     * When to stop measure:\n     * 1. Selection is out of range\n     * 2. Contains `space`\n     * 3. ESC or select one\n     */\n\n\n    _this.onKeyUp = function (event) {\n      var key = event.key,\n          which = event.which;\n      var _this$state2 = _this.state,\n          prevMeasureText = _this$state2.measureText,\n          measuring = _this$state2.measuring;\n      var _this$props = _this.props,\n          _this$props$prefix = _this$props.prefix,\n          prefix = _this$props$prefix === void 0 ? '' : _this$props$prefix,\n          clientOnKeyUp = _this$props.onKeyUp,\n          onSearch = _this$props.onSearch,\n          validateSearch = _this$props.validateSearch;\n      var target = event.target;\n      var selectionStartText = getBeforeSelectionText(target);\n\n      var _getLastMeasureIndex = getLastMeasureIndex(selectionStartText, prefix),\n          measureIndex = _getLastMeasureIndex.location,\n          measurePrefix = _getLastMeasureIndex.prefix; // If the client implements an onKeyUp handler, call it\n\n\n      if (clientOnKeyUp) {\n        clientOnKeyUp(event);\n      } // Skip if match the white key list\n\n\n      if ([KeyCode.ESC, KeyCode.UP, KeyCode.DOWN, KeyCode.ENTER].indexOf(which) !== -1) {\n        return;\n      }\n\n      if (measureIndex !== -1) {\n        var measureText = selectionStartText.slice(measureIndex + measurePrefix.length);\n        var validateMeasure = validateSearch(measureText, _this.props);\n        var matchOption = !!_this.getOptions(measureText).length;\n\n        if (validateMeasure) {\n          if (key === measurePrefix || key === 'Shift' || measuring || measureText !== prevMeasureText && matchOption) {\n            _this.startMeasure(measureText, measurePrefix, measureIndex);\n          }\n        } else if (measuring) {\n          // Stop if measureText is invalidate\n          _this.stopMeasure();\n        }\n        /**\n         * We will trigger `onSearch` to developer since they may use for async update.\n         * If met `space` means user finished searching.\n         */\n\n\n        if (onSearch && validateMeasure) {\n          onSearch(measureText, measurePrefix);\n        }\n      } else if (measuring) {\n        _this.stopMeasure();\n      }\n    };\n\n    _this.onPressEnter = function (event) {\n      var measuring = _this.state.measuring;\n      var onPressEnter = _this.props.onPressEnter;\n\n      if (!measuring && onPressEnter) {\n        onPressEnter(event);\n      }\n    };\n\n    _this.onInputFocus = function (event) {\n      _this.onFocus(event);\n    };\n\n    _this.onInputBlur = function (event) {\n      _this.onBlur(event);\n    };\n\n    _this.onDropdownFocus = function () {\n      _this.onFocus();\n    };\n\n    _this.onDropdownBlur = function () {\n      _this.onBlur();\n    };\n\n    _this.onFocus = function (event) {\n      window.clearTimeout(_this.focusId);\n      var isFocus = _this.state.isFocus;\n      var onFocus = _this.props.onFocus;\n\n      if (!isFocus && event && onFocus) {\n        onFocus(event);\n      }\n\n      _this.setState({\n        isFocus: true\n      });\n    };\n\n    _this.onBlur = function (event) {\n      _this.focusId = window.setTimeout(function () {\n        var onBlur = _this.props.onBlur;\n\n        _this.setState({\n          isFocus: false\n        });\n\n        _this.stopMeasure();\n\n        if (onBlur) {\n          onBlur(event);\n        }\n      }, 0);\n    };\n\n    _this.selectOption = function (option) {\n      var _this$state3 = _this.state,\n          value = _this$state3.value,\n          measureLocation = _this$state3.measureLocation,\n          measurePrefix = _this$state3.measurePrefix;\n      var _this$props2 = _this.props,\n          split = _this$props2.split,\n          onSelect = _this$props2.onSelect;\n      var _option$value = option.value,\n          mentionValue = _option$value === void 0 ? '' : _option$value;\n\n      var _replaceWithMeasure = replaceWithMeasure(value, {\n        measureLocation: measureLocation,\n        targetText: mentionValue,\n        prefix: measurePrefix,\n        selectionStart: _this.textarea.selectionStart,\n        split: split\n      }),\n          text = _replaceWithMeasure.text,\n          selectionLocation = _replaceWithMeasure.selectionLocation;\n\n      _this.triggerChange(text);\n\n      _this.stopMeasure(function () {\n        // We need restore the selection position\n        setInputSelection(_this.textarea, selectionLocation);\n      });\n\n      if (onSelect) {\n        onSelect(option, measurePrefix);\n      }\n    };\n\n    _this.setActiveIndex = function (activeIndex) {\n      _this.setState({\n        activeIndex: activeIndex\n      });\n    };\n\n    _this.setTextAreaRef = function (element) {\n      var _element$resizableTex;\n\n      _this.textarea = element === null || element === void 0 ? void 0 : (_element$resizableTex = element.resizableTextArea) === null || _element$resizableTex === void 0 ? void 0 : _element$resizableTex.textArea;\n    };\n\n    _this.setMeasureRef = function (element) {\n      _this.measure = element;\n    };\n\n    _this.getOptions = function (measureText) {\n      var targetMeasureText = measureText || _this.state.measureText || '';\n      var _this$props3 = _this.props,\n          children = _this$props3.children,\n          filterOption = _this$props3.filterOption;\n      var list = toArray(children).map(function (_ref2) {\n        var props = _ref2.props,\n            key = _ref2.key;\n        return _objectSpread(_objectSpread({}, props), {}, {\n          key: key || props.value\n        });\n      }).filter(function (option) {\n        /** Return all result if `filterOption` is false. */\n        if (filterOption === false) {\n          return true;\n        }\n\n        return filterOption(targetMeasureText, option);\n      });\n      return list;\n    };\n\n    _this.state = {\n      value: props.defaultValue || props.value || '',\n      measuring: false,\n      measureLocation: 0,\n      measureText: null,\n      measurePrefix: '',\n      activeIndex: 0,\n      isFocus: false\n    };\n    return _this;\n  }\n\n  _createClass(Mentions, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      var measuring = this.state.measuring; // Sync measure div top with textarea for rc-trigger usage\n\n      if (measuring) {\n        this.measure.scrollTop = this.textarea.scrollTop;\n      }\n    }\n  }, {\n    key: \"startMeasure\",\n    value: function startMeasure(measureText, measurePrefix, measureLocation) {\n      this.setState({\n        measuring: true,\n        measureText: measureText,\n        measurePrefix: measurePrefix,\n        measureLocation: measureLocation,\n        activeIndex: 0\n      });\n    }\n  }, {\n    key: \"stopMeasure\",\n    value: function stopMeasure(callback) {\n      this.setState({\n        measuring: false,\n        measureLocation: 0,\n        measureText: null\n      }, callback);\n    }\n  }, {\n    key: \"focus\",\n    value: function focus() {\n      this.textarea.focus();\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.textarea.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state4 = this.state,\n          value = _this$state4.value,\n          measureLocation = _this$state4.measureLocation,\n          measurePrefix = _this$state4.measurePrefix,\n          measuring = _this$state4.measuring,\n          activeIndex = _this$state4.activeIndex;\n\n      var _this$props4 = this.props,\n          prefixCls = _this$props4.prefixCls,\n          placement = _this$props4.placement,\n          direction = _this$props4.direction,\n          transitionName = _this$props4.transitionName,\n          className = _this$props4.className,\n          style = _this$props4.style,\n          autoFocus = _this$props4.autoFocus,\n          notFoundContent = _this$props4.notFoundContent,\n          getPopupContainer = _this$props4.getPopupContainer,\n          dropdownClassName = _this$props4.dropdownClassName,\n          restProps = _objectWithoutProperties(_this$props4, [\"prefixCls\", \"placement\", \"direction\", \"transitionName\", \"className\", \"style\", \"autoFocus\", \"notFoundContent\", \"getPopupContainer\", \"dropdownClassName\"]);\n\n      var inputProps = omit(restProps, 'value', 'defaultValue', 'prefix', 'split', 'children', 'validateSearch', 'filterOption', 'onSelect', 'onSearch');\n      var options = measuring ? this.getOptions() : [];\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(prefixCls, className),\n        style: style\n      }, /*#__PURE__*/React.createElement(TextArea, _extends({\n        autoFocus: autoFocus,\n        ref: this.setTextAreaRef,\n        value: value\n      }, inputProps, {\n        onChange: this.onChange,\n        onKeyDown: this.onKeyDown,\n        onKeyUp: this.onKeyUp,\n        onPressEnter: this.onPressEnter,\n        onFocus: this.onInputFocus,\n        onBlur: this.onInputBlur\n      })), measuring && /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.setMeasureRef,\n        className: \"\".concat(prefixCls, \"-measure\")\n      }, value.slice(0, measureLocation), /*#__PURE__*/React.createElement(MentionsContextProvider, {\n        value: {\n          notFoundContent: notFoundContent,\n          activeIndex: activeIndex,\n          setActiveIndex: this.setActiveIndex,\n          selectOption: this.selectOption,\n          onFocus: this.onDropdownFocus,\n          onBlur: this.onDropdownBlur\n        }\n      }, /*#__PURE__*/React.createElement(KeywordTrigger, {\n        prefixCls: prefixCls,\n        transitionName: transitionName,\n        placement: placement,\n        direction: direction,\n        options: options,\n        visible: true,\n        getPopupContainer: getPopupContainer,\n        dropdownClassName: dropdownClassName\n      }, /*#__PURE__*/React.createElement(\"span\", null, measurePrefix))), value.slice(measureLocation + measurePrefix.length)));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var newState = {};\n\n      if ('value' in props && props.value !== prevState.value) {\n        newState.value = props.value || '';\n      }\n\n      return newState;\n    }\n  }]);\n\n  return Mentions;\n}(React.Component);\n\nMentions.Option = Option;\nMentions.defaultProps = {\n  prefixCls: 'rc-mentions',\n  prefix: '@',\n  split: ' ',\n  validateSearch: defaultValidateSearch,\n  filterOption: defaultFilterOption,\n  notFoundContent: 'Not Found',\n  rows: 1\n};\nexport default Mentions;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,YAAY,IAAIC,mBAAmB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,IAAI,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,cAAc,IAAIC,qBAAqB,QAAQ,QAAQ;AAE/L,IAAIC,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACtDpB,SAAS,CAACmB,QAAQ,EAAEC,gBAAgB,CAAC;EAErC,IAAIC,MAAM,GAAGpB,YAAY,CAACkB,QAAQ,CAAC;EAEnC,SAASA,QAAQA,CAACG,KAAK,EAAE;IACvB,IAAIC,KAAK;IAETzB,eAAe,CAAC,IAAI,EAAEqB,QAAQ,CAAC;IAE/BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,OAAO,GAAGC,SAAS;IAEzBH,KAAK,CAACI,aAAa,GAAG,UAAUC,KAAK,EAAE;MACrC,IAAIC,QAAQ,GAAGN,KAAK,CAACD,KAAK,CAACO,QAAQ;MAEnC,IAAI,EAAE,OAAO,IAAIN,KAAK,CAACD,KAAK,CAAC,EAAE;QAC7BC,KAAK,CAACO,QAAQ,CAAC;UACbF,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MAEA,IAAIC,QAAQ,EAAE;QACZA,QAAQ,CAACD,KAAK,CAAC;MACjB;IACF,CAAC;IAEDL,KAAK,CAACM,QAAQ,GAAG,UAAUE,IAAI,EAAE;MAC/B,IAAIH,KAAK,GAAGG,IAAI,CAACC,MAAM,CAACJ,KAAK;MAE7BL,KAAK,CAACI,aAAa,CAACC,KAAK,CAAC;IAC5B,CAAC,CAAC,CAAC;;IAGHL,KAAK,CAACU,SAAS,GAAG,UAAUC,KAAK,EAAE;MACjC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACvB,IAAIC,WAAW,GAAGb,KAAK,CAACc,KAAK;QACzBC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,SAAS,GAAGH,WAAW,CAACG,SAAS;MACrC,IAAIC,eAAe,GAAGjB,KAAK,CAACD,KAAK,CAACW,SAAS;MAE3C,IAAIO,eAAe,EAAE;QACnBA,eAAe,CAACN,KAAK,CAAC;MACxB,CAAC,CAAC;;MAGF,IAAI,CAACK,SAAS,EAAE;QACd;MACF;MAEA,IAAIJ,KAAK,KAAK/B,OAAO,CAACqC,EAAE,IAAIN,KAAK,KAAK/B,OAAO,CAACsC,IAAI,EAAE;QAClD;QACA,IAAIC,SAAS,GAAGpB,KAAK,CAACqB,UAAU,CAAC,CAAC,CAACC,MAAM;QAEzC,IAAIC,MAAM,GAAGX,KAAK,KAAK/B,OAAO,CAACqC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QAC1C,IAAIM,cAAc,GAAG,CAACT,WAAW,GAAGQ,MAAM,GAAGH,SAAS,IAAIA,SAAS;QAEnEpB,KAAK,CAACO,QAAQ,CAAC;UACbQ,WAAW,EAAES;QACf,CAAC,CAAC;QAEFb,KAAK,CAACc,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIb,KAAK,KAAK/B,OAAO,CAAC6C,GAAG,EAAE;QAChC1B,KAAK,CAAC2B,WAAW,CAAC,CAAC;MACrB,CAAC,MAAM,IAAIf,KAAK,KAAK/B,OAAO,CAAC+C,KAAK,EAAE;QAClC;QACAjB,KAAK,CAACc,cAAc,CAAC,CAAC;QAEtB,IAAII,OAAO,GAAG7B,KAAK,CAACqB,UAAU,CAAC,CAAC;QAEhC,IAAI,CAACQ,OAAO,CAACP,MAAM,EAAE;UACnBtB,KAAK,CAAC2B,WAAW,CAAC,CAAC;UAEnB;QACF;QAEA,IAAIG,MAAM,GAAGD,OAAO,CAACd,WAAW,CAAC;QAEjCf,KAAK,CAAC+B,YAAY,CAACD,MAAM,CAAC;MAC5B;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI9B,KAAK,CAACgC,OAAO,GAAG,UAAUrB,KAAK,EAAE;MAC/B,IAAIsB,GAAG,GAAGtB,KAAK,CAACsB,GAAG;QACfrB,KAAK,GAAGD,KAAK,CAACC,KAAK;MACvB,IAAIsB,YAAY,GAAGlC,KAAK,CAACc,KAAK;QAC1BqB,eAAe,GAAGD,YAAY,CAACE,WAAW;QAC1CpB,SAAS,GAAGkB,YAAY,CAAClB,SAAS;MACtC,IAAIqB,WAAW,GAAGrC,KAAK,CAACD,KAAK;QACzBuC,kBAAkB,GAAGD,WAAW,CAACE,MAAM;QACvCA,MAAM,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;QAChEE,aAAa,GAAGH,WAAW,CAACL,OAAO;QACnCS,QAAQ,GAAGJ,WAAW,CAACI,QAAQ;QAC/B/C,cAAc,GAAG2C,WAAW,CAAC3C,cAAc;MAC/C,IAAIe,MAAM,GAAGE,KAAK,CAACF,MAAM;MACzB,IAAIiC,kBAAkB,GAAGrD,sBAAsB,CAACoB,MAAM,CAAC;MAEvD,IAAIkC,oBAAoB,GAAGrD,mBAAmB,CAACoD,kBAAkB,EAAEH,MAAM,CAAC;QACtEK,YAAY,GAAGD,oBAAoB,CAACE,QAAQ;QAC5CC,aAAa,GAAGH,oBAAoB,CAACJ,MAAM,CAAC,CAAC;;MAGjD,IAAIC,aAAa,EAAE;QACjBA,aAAa,CAAC7B,KAAK,CAAC;MACtB,CAAC,CAAC;;MAGF,IAAI,CAAC9B,OAAO,CAAC6C,GAAG,EAAE7C,OAAO,CAACqC,EAAE,EAAErC,OAAO,CAACsC,IAAI,EAAEtC,OAAO,CAAC+C,KAAK,CAAC,CAACmB,OAAO,CAACnC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QAChF;MACF;MAEA,IAAIgC,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB,IAAIR,WAAW,GAAGM,kBAAkB,CAACM,KAAK,CAACJ,YAAY,GAAGE,aAAa,CAACxB,MAAM,CAAC;QAC/E,IAAI2B,eAAe,GAAGvD,cAAc,CAAC0C,WAAW,EAAEpC,KAAK,CAACD,KAAK,CAAC;QAC9D,IAAImD,WAAW,GAAG,CAAC,CAAClD,KAAK,CAACqB,UAAU,CAACe,WAAW,CAAC,CAACd,MAAM;QAExD,IAAI2B,eAAe,EAAE;UACnB,IAAIhB,GAAG,KAAKa,aAAa,IAAIb,GAAG,KAAK,OAAO,IAAIjB,SAAS,IAAIoB,WAAW,KAAKD,eAAe,IAAIe,WAAW,EAAE;YAC3GlD,KAAK,CAACmD,YAAY,CAACf,WAAW,EAAEU,aAAa,EAAEF,YAAY,CAAC;UAC9D;QACF,CAAC,MAAM,IAAI5B,SAAS,EAAE;UACpB;UACAhB,KAAK,CAAC2B,WAAW,CAAC,CAAC;QACrB;QACA;AACR;AACA;AACA;;QAGQ,IAAIc,QAAQ,IAAIQ,eAAe,EAAE;UAC/BR,QAAQ,CAACL,WAAW,EAAEU,aAAa,CAAC;QACtC;MACF,CAAC,MAAM,IAAI9B,SAAS,EAAE;QACpBhB,KAAK,CAAC2B,WAAW,CAAC,CAAC;MACrB;IACF,CAAC;IAED3B,KAAK,CAACoD,YAAY,GAAG,UAAUzC,KAAK,EAAE;MACpC,IAAIK,SAAS,GAAGhB,KAAK,CAACc,KAAK,CAACE,SAAS;MACrC,IAAIoC,YAAY,GAAGpD,KAAK,CAACD,KAAK,CAACqD,YAAY;MAE3C,IAAI,CAACpC,SAAS,IAAIoC,YAAY,EAAE;QAC9BA,YAAY,CAACzC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDX,KAAK,CAACqD,YAAY,GAAG,UAAU1C,KAAK,EAAE;MACpCX,KAAK,CAACsD,OAAO,CAAC3C,KAAK,CAAC;IACtB,CAAC;IAEDX,KAAK,CAACuD,WAAW,GAAG,UAAU5C,KAAK,EAAE;MACnCX,KAAK,CAACwD,MAAM,CAAC7C,KAAK,CAAC;IACrB,CAAC;IAEDX,KAAK,CAACyD,eAAe,GAAG,YAAY;MAClCzD,KAAK,CAACsD,OAAO,CAAC,CAAC;IACjB,CAAC;IAEDtD,KAAK,CAAC0D,cAAc,GAAG,YAAY;MACjC1D,KAAK,CAACwD,MAAM,CAAC,CAAC;IAChB,CAAC;IAEDxD,KAAK,CAACsD,OAAO,GAAG,UAAU3C,KAAK,EAAE;MAC/BgD,MAAM,CAACC,YAAY,CAAC5D,KAAK,CAACE,OAAO,CAAC;MAClC,IAAI2D,OAAO,GAAG7D,KAAK,CAACc,KAAK,CAAC+C,OAAO;MACjC,IAAIP,OAAO,GAAGtD,KAAK,CAACD,KAAK,CAACuD,OAAO;MAEjC,IAAI,CAACO,OAAO,IAAIlD,KAAK,IAAI2C,OAAO,EAAE;QAChCA,OAAO,CAAC3C,KAAK,CAAC;MAChB;MAEAX,KAAK,CAACO,QAAQ,CAAC;QACbsD,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;IAED7D,KAAK,CAACwD,MAAM,GAAG,UAAU7C,KAAK,EAAE;MAC9BX,KAAK,CAACE,OAAO,GAAGyD,MAAM,CAACG,UAAU,CAAC,YAAY;QAC5C,IAAIN,MAAM,GAAGxD,KAAK,CAACD,KAAK,CAACyD,MAAM;QAE/BxD,KAAK,CAACO,QAAQ,CAAC;UACbsD,OAAO,EAAE;QACX,CAAC,CAAC;QAEF7D,KAAK,CAAC2B,WAAW,CAAC,CAAC;QAEnB,IAAI6B,MAAM,EAAE;UACVA,MAAM,CAAC7C,KAAK,CAAC;QACf;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAEDX,KAAK,CAAC+B,YAAY,GAAG,UAAUD,MAAM,EAAE;MACrC,IAAIiC,YAAY,GAAG/D,KAAK,CAACc,KAAK;QAC1BT,KAAK,GAAG0D,YAAY,CAAC1D,KAAK;QAC1B2D,eAAe,GAAGD,YAAY,CAACC,eAAe;QAC9ClB,aAAa,GAAGiB,YAAY,CAACjB,aAAa;MAC9C,IAAImB,YAAY,GAAGjE,KAAK,CAACD,KAAK;QAC1BmE,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;MACpC,IAAIC,aAAa,GAAGtC,MAAM,CAACzB,KAAK;QAC5BgE,YAAY,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;MAEhE,IAAIE,mBAAmB,GAAG9E,kBAAkB,CAACa,KAAK,EAAE;UAClD2D,eAAe,EAAEA,eAAe;UAChCO,UAAU,EAAEF,YAAY;UACxB9B,MAAM,EAAEO,aAAa;UACrB0B,cAAc,EAAExE,KAAK,CAACyE,QAAQ,CAACD,cAAc;UAC7CN,KAAK,EAAEA;QACT,CAAC,CAAC;QACEQ,IAAI,GAAGJ,mBAAmB,CAACI,IAAI;QAC/BC,iBAAiB,GAAGL,mBAAmB,CAACK,iBAAiB;MAE7D3E,KAAK,CAACI,aAAa,CAACsE,IAAI,CAAC;MAEzB1E,KAAK,CAAC2B,WAAW,CAAC,YAAY;QAC5B;QACAlC,iBAAiB,CAACO,KAAK,CAACyE,QAAQ,EAAEE,iBAAiB,CAAC;MACtD,CAAC,CAAC;MAEF,IAAIR,QAAQ,EAAE;QACZA,QAAQ,CAACrC,MAAM,EAAEgB,aAAa,CAAC;MACjC;IACF,CAAC;IAED9C,KAAK,CAAC4E,cAAc,GAAG,UAAU7D,WAAW,EAAE;MAC5Cf,KAAK,CAACO,QAAQ,CAAC;QACbQ,WAAW,EAAEA;MACf,CAAC,CAAC;IACJ,CAAC;IAEDf,KAAK,CAAC6E,cAAc,GAAG,UAAUC,OAAO,EAAE;MACxC,IAAIC,qBAAqB;MAEzB/E,KAAK,CAACyE,QAAQ,GAAGK,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,OAAO,CAACE,iBAAiB,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,QAAQ;IAC/M,CAAC;IAEDjF,KAAK,CAACkF,aAAa,GAAG,UAAUJ,OAAO,EAAE;MACvC9E,KAAK,CAACmF,OAAO,GAAGL,OAAO;IACzB,CAAC;IAED9E,KAAK,CAACqB,UAAU,GAAG,UAAUe,WAAW,EAAE;MACxC,IAAIgD,iBAAiB,GAAGhD,WAAW,IAAIpC,KAAK,CAACc,KAAK,CAACsB,WAAW,IAAI,EAAE;MACpE,IAAIiD,YAAY,GAAGrF,KAAK,CAACD,KAAK;QAC1BuF,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCnG,YAAY,GAAGkG,YAAY,CAAClG,YAAY;MAC5C,IAAIoG,IAAI,GAAG3G,OAAO,CAAC0G,QAAQ,CAAC,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAE;QAChD,IAAI1F,KAAK,GAAG0F,KAAK,CAAC1F,KAAK;UACnBkC,GAAG,GAAGwD,KAAK,CAACxD,GAAG;QACnB,OAAO3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDkC,GAAG,EAAEA,GAAG,IAAIlC,KAAK,CAACM;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC,CAACqF,MAAM,CAAC,UAAU5D,MAAM,EAAE;QAC1B;QACA,IAAI3C,YAAY,KAAK,KAAK,EAAE;UAC1B,OAAO,IAAI;QACb;QAEA,OAAOA,YAAY,CAACiG,iBAAiB,EAAEtD,MAAM,CAAC;MAChD,CAAC,CAAC;MACF,OAAOyD,IAAI;IACb,CAAC;IAEDvF,KAAK,CAACc,KAAK,GAAG;MACZT,KAAK,EAAEN,KAAK,CAAC4F,YAAY,IAAI5F,KAAK,CAACM,KAAK,IAAI,EAAE;MAC9CW,SAAS,EAAE,KAAK;MAChBgD,eAAe,EAAE,CAAC;MAClB5B,WAAW,EAAE,IAAI;MACjBU,aAAa,EAAE,EAAE;MACjB/B,WAAW,EAAE,CAAC;MACd8C,OAAO,EAAE;IACX,CAAC;IACD,OAAO7D,KAAK;EACd;EAEAxB,YAAY,CAACoB,QAAQ,EAAE,CAAC;IACtBqC,GAAG,EAAE,oBAAoB;IACzB5B,KAAK,EAAE,SAASuF,kBAAkBA,CAAA,EAAG;MACnC,IAAI5E,SAAS,GAAG,IAAI,CAACF,KAAK,CAACE,SAAS,CAAC,CAAC;;MAEtC,IAAIA,SAAS,EAAE;QACb,IAAI,CAACmE,OAAO,CAACU,SAAS,GAAG,IAAI,CAACpB,QAAQ,CAACoB,SAAS;MAClD;IACF;EACF,CAAC,EAAE;IACD5D,GAAG,EAAE,cAAc;IACnB5B,KAAK,EAAE,SAAS8C,YAAYA,CAACf,WAAW,EAAEU,aAAa,EAAEkB,eAAe,EAAE;MACxE,IAAI,CAACzD,QAAQ,CAAC;QACZS,SAAS,EAAE,IAAI;QACfoB,WAAW,EAAEA,WAAW;QACxBU,aAAa,EAAEA,aAAa;QAC5BkB,eAAe,EAAEA,eAAe;QAChCjD,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDkB,GAAG,EAAE,aAAa;IAClB5B,KAAK,EAAE,SAASsB,WAAWA,CAACmE,QAAQ,EAAE;MACpC,IAAI,CAACvF,QAAQ,CAAC;QACZS,SAAS,EAAE,KAAK;QAChBgD,eAAe,EAAE,CAAC;QAClB5B,WAAW,EAAE;MACf,CAAC,EAAE0D,QAAQ,CAAC;IACd;EACF,CAAC,EAAE;IACD7D,GAAG,EAAE,OAAO;IACZ5B,KAAK,EAAE,SAAS0F,KAAKA,CAAA,EAAG;MACtB,IAAI,CAACtB,QAAQ,CAACsB,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,MAAM;IACX5B,KAAK,EAAE,SAAS2F,IAAIA,CAAA,EAAG;MACrB,IAAI,CAACvB,QAAQ,CAACuB,IAAI,CAAC,CAAC;IACtB;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,QAAQ;IACb5B,KAAK,EAAE,SAAS4F,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACpF,KAAK;QACzBT,KAAK,GAAG6F,YAAY,CAAC7F,KAAK;QAC1B2D,eAAe,GAAGkC,YAAY,CAAClC,eAAe;QAC9ClB,aAAa,GAAGoD,YAAY,CAACpD,aAAa;QAC1C9B,SAAS,GAAGkF,YAAY,CAAClF,SAAS;QAClCD,WAAW,GAAGmF,YAAY,CAACnF,WAAW;MAE1C,IAAIoF,YAAY,GAAG,IAAI,CAACpG,KAAK;QACzBqG,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClCC,cAAc,GAAGJ,YAAY,CAACI,cAAc;QAC5CC,SAAS,GAAGL,YAAY,CAACK,SAAS;QAClCC,KAAK,GAAGN,YAAY,CAACM,KAAK;QAC1BC,SAAS,GAAGP,YAAY,CAACO,SAAS;QAClCC,eAAe,GAAGR,YAAY,CAACQ,eAAe;QAC9CC,iBAAiB,GAAGT,YAAY,CAACS,iBAAiB;QAClDC,iBAAiB,GAAGV,YAAY,CAACU,iBAAiB;QAClDC,SAAS,GAAGzI,wBAAwB,CAAC8H,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;MAEjN,IAAIY,UAAU,GAAGxH,IAAI,CAACuH,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;MAClJ,IAAIjF,OAAO,GAAGb,SAAS,GAAG,IAAI,CAACK,UAAU,CAAC,CAAC,GAAG,EAAE;MAChD,OAAO,aAAavC,KAAK,CAACkI,aAAa,CAAC,KAAK,EAAE;QAC7CR,SAAS,EAAE7H,UAAU,CAACyH,SAAS,EAAEI,SAAS,CAAC;QAC3CC,KAAK,EAAEA;MACT,CAAC,EAAE,aAAa3H,KAAK,CAACkI,aAAa,CAACjI,QAAQ,EAAEX,QAAQ,CAAC;QACrDsI,SAAS,EAAEA,SAAS;QACpBO,GAAG,EAAE,IAAI,CAACpC,cAAc;QACxBxE,KAAK,EAAEA;MACT,CAAC,EAAE0G,UAAU,EAAE;QACbzG,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBI,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBsB,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBoB,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BE,OAAO,EAAE,IAAI,CAACD,YAAY;QAC1BG,MAAM,EAAE,IAAI,CAACD;MACf,CAAC,CAAC,CAAC,EAAEvC,SAAS,IAAI,aAAalC,KAAK,CAACkI,aAAa,CAAC,KAAK,EAAE;QACxDC,GAAG,EAAE,IAAI,CAAC/B,aAAa;QACvBsB,SAAS,EAAE,EAAE,CAACU,MAAM,CAACd,SAAS,EAAE,UAAU;MAC5C,CAAC,EAAE/F,KAAK,CAAC2C,KAAK,CAAC,CAAC,EAAEgB,eAAe,CAAC,EAAE,aAAalF,KAAK,CAACkI,aAAa,CAAC/H,uBAAuB,EAAE;QAC5FoB,KAAK,EAAE;UACLsG,eAAe,EAAEA,eAAe;UAChC5F,WAAW,EAAEA,WAAW;UACxB6D,cAAc,EAAE,IAAI,CAACA,cAAc;UACnC7C,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BuB,OAAO,EAAE,IAAI,CAACG,eAAe;UAC7BD,MAAM,EAAE,IAAI,CAACE;QACf;MACF,CAAC,EAAE,aAAa5E,KAAK,CAACkI,aAAa,CAAChI,cAAc,EAAE;QAClDoH,SAAS,EAAEA,SAAS;QACpBG,cAAc,EAAEA,cAAc;QAC9BF,SAAS,EAAEA,SAAS;QACpBC,SAAS,EAAEA,SAAS;QACpBzE,OAAO,EAAEA,OAAO;QAChBsF,OAAO,EAAE,IAAI;QACbP,iBAAiB,EAAEA,iBAAiB;QACpCC,iBAAiB,EAAEA;MACrB,CAAC,EAAE,aAAa/H,KAAK,CAACkI,aAAa,CAAC,MAAM,EAAE,IAAI,EAAElE,aAAa,CAAC,CAAC,CAAC,EAAEzC,KAAK,CAAC2C,KAAK,CAACgB,eAAe,GAAGlB,aAAa,CAACxB,MAAM,CAAC,CAAC,CAAC;IAC3H;EACF,CAAC,CAAC,EAAE,CAAC;IACHW,GAAG,EAAE,0BAA0B;IAC/B5B,KAAK,EAAE,SAAS+G,wBAAwBA,CAACrH,KAAK,EAAEsH,SAAS,EAAE;MACzD,IAAIC,QAAQ,GAAG,CAAC,CAAC;MAEjB,IAAI,OAAO,IAAIvH,KAAK,IAAIA,KAAK,CAACM,KAAK,KAAKgH,SAAS,CAAChH,KAAK,EAAE;QACvDiH,QAAQ,CAACjH,KAAK,GAAGN,KAAK,CAACM,KAAK,IAAI,EAAE;MACpC;MAEA,OAAOiH,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EAEH,OAAO1H,QAAQ;AACjB,CAAC,CAACd,KAAK,CAACyI,SAAS,CAAC;AAElB3H,QAAQ,CAACV,MAAM,GAAGA,MAAM;AACxBU,QAAQ,CAAC4H,YAAY,GAAG;EACtBpB,SAAS,EAAE,aAAa;EACxB7D,MAAM,EAAE,GAAG;EACX2B,KAAK,EAAE,GAAG;EACVxE,cAAc,EAAEC,qBAAqB;EACrCR,YAAY,EAAEC,mBAAmB;EACjCuH,eAAe,EAAE,WAAW;EAC5Bc,IAAI,EAAE;AACR,CAAC;AACD,eAAe7H,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}