{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.NoStyleItemContext = exports.NoFormStatus = exports.FormProvider = exports.FormItemPrefixContext = exports.FormItemInputContext = exports.FormContext = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _omit = _interopRequireDefault(require(\"rc-util/lib/omit\"));\nvar _rcFieldForm = require(\"rc-field-form\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  vertical: false,\n  itemRef: function itemRef() {}\n});\nexports.FormContext = FormContext;\nvar NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexports.NoStyleItemContext = NoStyleItemContext;\nvar FormProvider = function FormProvider(props) {\n  var providerProps = (0, _omit[\"default\"])(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(_rcFieldForm.FormProvider, providerProps);\n};\nexports.FormProvider = FormProvider;\nvar FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexports.FormItemPrefixContext = FormItemPrefixContext;\nvar FormItemInputContext = /*#__PURE__*/React.createContext({});\nexports.FormItemInputContext = FormItemInputContext;\nvar NoFormStatus = function NoFormStatus(_ref) {\n  var children = _ref.children;\n  var emptyContext = (0, React.useMemo)(function () {\n    return {};\n  }, []);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: emptyContext\n  }, children);\n};\nexports.NoFormStatus = NoFormStatus;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "NoStyleItemContext", "NoFormStatus", "FormProvider", "FormItemPrefixContext", "FormItemInputContext", "FormContext", "React", "_interopRequireWildcard", "_omit", "_rcFieldForm", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "createContext", "labelAlign", "vertical", "itemRef", "props", "providerProps", "createElement", "prefixCls", "_ref", "children", "emptyContext", "useMemo", "Provider"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/form/context.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.NoStyleItemContext = exports.NoFormStatus = exports.FormProvider = exports.FormItemPrefixContext = exports.FormItemInputContext = exports.FormContext = void 0;\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _omit = _interopRequireDefault(require(\"rc-util/lib/omit\"));\n\nvar _rcFieldForm = require(\"rc-field-form\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  vertical: false,\n  itemRef: function itemRef() {}\n});\nexports.FormContext = FormContext;\nvar NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexports.NoStyleItemContext = NoStyleItemContext;\n\nvar FormProvider = function FormProvider(props) {\n  var providerProps = (0, _omit[\"default\"])(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(_rcFieldForm.FormProvider, providerProps);\n};\n\nexports.FormProvider = FormProvider;\nvar FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexports.FormItemPrefixContext = FormItemPrefixContext;\nvar FormItemInputContext = /*#__PURE__*/React.createContext({});\nexports.FormItemInputContext = FormItemInputContext;\n\nvar NoFormStatus = function NoFormStatus(_ref) {\n  var children = _ref.children;\n  var emptyContext = (0, React.useMemo)(function () {\n    return {};\n  }, []);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: emptyContext\n  }, children);\n};\n\nexports.NoFormStatus = NoFormStatus;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,qBAAqB,GAAGL,OAAO,CAACM,oBAAoB,GAAGN,OAAO,CAACO,WAAW,GAAG,KAAK,CAAC;AAEtK,IAAIC,KAAK,GAAGC,uBAAuB,CAACb,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIc,KAAK,GAAGf,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAE/D,IAAIe,YAAY,GAAGf,OAAO,CAAC,eAAe,CAAC;AAE3C,SAASgB,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASJ,uBAAuBA,CAACQ,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIpB,OAAO,CAACoB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGzB,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC0B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAI3B,MAAM,CAAC4B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEhC,MAAM,CAACC,cAAc,CAACuB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIf,WAAW,GAAG,aAAaC,KAAK,CAACuB,aAAa,CAAC;EACjDC,UAAU,EAAE,OAAO;EACnBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;AAC/B,CAAC,CAAC;AACFlC,OAAO,CAACO,WAAW,GAAGA,WAAW;AACjC,IAAIL,kBAAkB,GAAG,aAAaM,KAAK,CAACuB,aAAa,CAAC,IAAI,CAAC;AAC/D/B,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAE/C,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAAC+B,KAAK,EAAE;EAC9C,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAE1B,KAAK,CAAC,SAAS,CAAC,EAAEyB,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC/D,OAAO,aAAa3B,KAAK,CAAC6B,aAAa,CAAC1B,YAAY,CAACP,YAAY,EAAEgC,aAAa,CAAC;AACnF,CAAC;AAEDpC,OAAO,CAACI,YAAY,GAAGA,YAAY;AACnC,IAAIC,qBAAqB,GAAG,aAAaG,KAAK,CAACuB,aAAa,CAAC;EAC3DO,SAAS,EAAE;AACb,CAAC,CAAC;AACFtC,OAAO,CAACK,qBAAqB,GAAGA,qBAAqB;AACrD,IAAIC,oBAAoB,GAAG,aAAaE,KAAK,CAACuB,aAAa,CAAC,CAAC,CAAC,CAAC;AAC/D/B,OAAO,CAACM,oBAAoB,GAAGA,oBAAoB;AAEnD,IAAIH,YAAY,GAAG,SAASA,YAAYA,CAACoC,IAAI,EAAE;EAC7C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAEjC,KAAK,CAACkC,OAAO,EAAE,YAAY;IAChD,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAalC,KAAK,CAAC6B,aAAa,CAAC/B,oBAAoB,CAACqC,QAAQ,EAAE;IACrE1C,KAAK,EAAEwC;EACT,CAAC,EAAED,QAAQ,CAAC;AACd,CAAC;AAEDxC,OAAO,CAACG,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}