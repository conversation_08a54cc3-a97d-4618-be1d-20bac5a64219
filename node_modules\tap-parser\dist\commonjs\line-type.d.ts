/**
 * Collection of the various types of lines encountered in a TAP stream
 */
export declare const lineTypes: {
    readonly testPoint: RegExp;
    readonly pragma: RegExp;
    readonly bailout: RegExp;
    readonly version: RegExp;
    readonly childVersion: RegExp;
    readonly plan: RegExp;
    readonly subtest: RegExp;
    readonly subtestIndent: RegExp;
    readonly comment: RegExp;
};
/**
 * The type of a line, and its constituent parsed pieces
 */
export type ParsedLine = [string, RegExpMatchArray];
/**
 * Determine the type of line, and parse it into a {@link ParsedLine}
 */
export declare const lineType: (line: string) => ParsedLine | null;
//# sourceMappingURL=line-type.d.ts.map