{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"className\", \"children\", \"style\", \"width\", \"height\", \"defaultOpen\", \"open\", \"prefixCls\", \"placement\", \"level\", \"levelMove\", \"ease\", \"duration\", \"getContainer\", \"handler\", \"onChange\", \"afterVisibleChange\", \"showMask\", \"maskClosable\", \"maskStyle\", \"onClose\", \"onHandleClick\", \"keyboard\", \"getOpenCount\", \"scrollLocker\", \"contentWrapperStyle\"];\nimport * as React from 'react';\nimport classnames from 'classnames';\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport { addEventListener, dataToArray, getTouchParentScroll, isNumeric, removeEventListener, transformArguments, transitionEnd, transitionStr, windowIsUndefined } from './utils';\nvar currentDrawer = {};\nvar DrawerChild = /*#__PURE__*/function (_React$Component) {\n  _inherits(DrawerChild, _React$Component);\n  var _super = _createSuper(DrawerChild);\n  function DrawerChild(props) {\n    var _this;\n    _classCallCheck(this, DrawerChild);\n    _this = _super.call(this, props);\n    _this.levelDom = void 0;\n    _this.dom = void 0;\n    _this.contentWrapper = void 0;\n    _this.contentDom = void 0;\n    _this.maskDom = void 0;\n    _this.handlerDom = void 0;\n    _this.drawerId = void 0;\n    _this.timeout = void 0;\n    _this.passive = void 0;\n    _this.startPos = void 0;\n    _this.domFocus = function () {\n      if (_this.dom) {\n        _this.dom.focus();\n      }\n    };\n    _this.removeStartHandler = function (e) {\n      if (e.touches.length > 1) {\n        // need clear the startPos when another touch event happens\n        _this.startPos = null;\n        return;\n      }\n      _this.startPos = {\n        x: e.touches[0].clientX,\n        y: e.touches[0].clientY\n      };\n    };\n    _this.removeMoveHandler = function (e) {\n      // the startPos may be null or undefined\n      if (e.changedTouches.length > 1 || !_this.startPos) {\n        return;\n      }\n      var currentTarget = e.currentTarget;\n      var differX = e.changedTouches[0].clientX - _this.startPos.x;\n      var differY = e.changedTouches[0].clientY - _this.startPos.y;\n      if ((currentTarget === _this.maskDom || currentTarget === _this.handlerDom || currentTarget === _this.contentDom && getTouchParentScroll(currentTarget, e.target, differX, differY)) && e.cancelable) {\n        e.preventDefault();\n      }\n    };\n    _this.transitionEnd = function (e) {\n      var dom = e.target;\n      removeEventListener(dom, transitionEnd, _this.transitionEnd);\n      dom.style.transition = '';\n    };\n    _this.onKeyDown = function (e) {\n      if (e.keyCode === KeyCode.ESC) {\n        var onClose = _this.props.onClose;\n        e.stopPropagation();\n        if (onClose) {\n          onClose(e);\n        }\n      }\n    };\n    _this.onWrapperTransitionEnd = function (e) {\n      var _this$props = _this.props,\n        open = _this$props.open,\n        afterVisibleChange = _this$props.afterVisibleChange;\n      if (e.target === _this.contentWrapper && e.propertyName.match(/transform$/)) {\n        _this.dom.style.transition = '';\n        if (!open && _this.getCurrentDrawerSome()) {\n          document.body.style.overflowX = '';\n          if (_this.maskDom) {\n            _this.maskDom.style.left = '';\n            _this.maskDom.style.width = '';\n          }\n        }\n        if (afterVisibleChange) {\n          afterVisibleChange(!!open);\n        }\n      }\n    };\n    _this.openLevelTransition = function () {\n      var _this$props2 = _this.props,\n        open = _this$props2.open,\n        width = _this$props2.width,\n        height = _this$props2.height;\n      var _this$getHorizontalBo = _this.getHorizontalBoolAndPlacementName(),\n        isHorizontal = _this$getHorizontalBo.isHorizontal,\n        placementName = _this$getHorizontalBo.placementName;\n      var contentValue = _this.contentDom ? _this.contentDom.getBoundingClientRect()[isHorizontal ? 'width' : 'height'] : 0;\n      var value = (isHorizontal ? width : height) || contentValue;\n      _this.setLevelAndScrolling(open, placementName, value);\n    };\n    _this.setLevelTransform = function (open, placementName, value, right) {\n      var _this$props3 = _this.props,\n        placement = _this$props3.placement,\n        levelMove = _this$props3.levelMove,\n        duration = _this$props3.duration,\n        ease = _this$props3.ease,\n        showMask = _this$props3.showMask; // router 切换时可能会导至页面失去滚动条，所以需要时时获取。\n\n      _this.levelDom.forEach(function (dom) {\n        dom.style.transition = \"transform \".concat(duration, \" \").concat(ease);\n        addEventListener(dom, transitionEnd, _this.transitionEnd);\n        var levelValue = open ? value : 0;\n        if (levelMove) {\n          var $levelMove = transformArguments(levelMove, {\n            target: dom,\n            open: open\n          });\n          levelValue = open ? $levelMove[0] : $levelMove[1] || 0;\n        }\n        var $value = typeof levelValue === 'number' ? \"\".concat(levelValue, \"px\") : levelValue;\n        var placementPos = placement === 'left' || placement === 'top' ? $value : \"-\".concat($value);\n        placementPos = showMask && placement === 'right' && right ? \"calc(\".concat(placementPos, \" + \").concat(right, \"px)\") : placementPos;\n        dom.style.transform = levelValue ? \"\".concat(placementName, \"(\").concat(placementPos, \")\") : '';\n      });\n    };\n    _this.setLevelAndScrolling = function (open, placementName, value) {\n      var onChange = _this.props.onChange;\n      if (!windowIsUndefined) {\n        var right = document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth ? getScrollBarSize(true) : 0;\n        _this.setLevelTransform(open, placementName, value, right);\n        _this.toggleScrollingToDrawerAndBody(right);\n      }\n      if (onChange) {\n        onChange(open);\n      }\n    };\n    _this.toggleScrollingToDrawerAndBody = function (right) {\n      var _this$props4 = _this.props,\n        getContainer = _this$props4.getContainer,\n        showMask = _this$props4.showMask,\n        open = _this$props4.open;\n      var container = getContainer && getContainer(); // 处理 body 滚动\n\n      if (container && container.parentNode === document.body && showMask) {\n        var eventArray = ['touchstart'];\n        var domArray = [document.body, _this.maskDom, _this.handlerDom, _this.contentDom];\n        if (open && document.body.style.overflow !== 'hidden') {\n          if (right) {\n            _this.addScrollingEffect(right);\n          }\n          document.body.style.touchAction = 'none'; // 手机禁滚\n\n          domArray.forEach(function (item, i) {\n            if (!item) {\n              return;\n            }\n            addEventListener(item, eventArray[i] || 'touchmove', i ? _this.removeMoveHandler : _this.removeStartHandler, _this.passive);\n          });\n        } else if (_this.getCurrentDrawerSome()) {\n          document.body.style.touchAction = '';\n          if (right) {\n            _this.remScrollingEffect(right);\n          } // 恢复事件\n\n          domArray.forEach(function (item, i) {\n            if (!item) {\n              return;\n            }\n            removeEventListener(item, eventArray[i] || 'touchmove', i ? _this.removeMoveHandler : _this.removeStartHandler, _this.passive);\n          });\n        }\n      }\n    };\n    _this.addScrollingEffect = function (right) {\n      var _this$props5 = _this.props,\n        placement = _this$props5.placement,\n        duration = _this$props5.duration,\n        ease = _this$props5.ease;\n      var widthTransition = \"width \".concat(duration, \" \").concat(ease);\n      var transformTransition = \"transform \".concat(duration, \" \").concat(ease);\n      _this.dom.style.transition = 'none';\n      switch (placement) {\n        case 'right':\n          _this.dom.style.transform = \"translateX(-\".concat(right, \"px)\");\n          break;\n        case 'top':\n        case 'bottom':\n          _this.dom.style.width = \"calc(100% - \".concat(right, \"px)\");\n          _this.dom.style.transform = 'translateZ(0)';\n          break;\n        default:\n          break;\n      }\n      clearTimeout(_this.timeout);\n      _this.timeout = setTimeout(function () {\n        if (_this.dom) {\n          _this.dom.style.transition = \"\".concat(transformTransition, \",\").concat(widthTransition);\n          _this.dom.style.width = '';\n          _this.dom.style.transform = '';\n        }\n      });\n    };\n    _this.remScrollingEffect = function (right) {\n      var _this$props6 = _this.props,\n        placement = _this$props6.placement,\n        duration = _this$props6.duration,\n        ease = _this$props6.ease;\n      if (transitionStr) {\n        document.body.style.overflowX = 'hidden';\n      }\n      _this.dom.style.transition = 'none';\n      var heightTransition;\n      var widthTransition = \"width \".concat(duration, \" \").concat(ease);\n      var transformTransition = \"transform \".concat(duration, \" \").concat(ease);\n      switch (placement) {\n        case 'left':\n          {\n            _this.dom.style.width = '100%';\n            widthTransition = \"width 0s \".concat(ease, \" \").concat(duration);\n            break;\n          }\n        case 'right':\n          {\n            _this.dom.style.transform = \"translateX(\".concat(right, \"px)\");\n            _this.dom.style.width = '100%';\n            widthTransition = \"width 0s \".concat(ease, \" \").concat(duration);\n            if (_this.maskDom) {\n              _this.maskDom.style.left = \"-\".concat(right, \"px\");\n              _this.maskDom.style.width = \"calc(100% + \".concat(right, \"px)\");\n            }\n            break;\n          }\n        case 'top':\n        case 'bottom':\n          {\n            _this.dom.style.width = \"calc(100% + \".concat(right, \"px)\");\n            _this.dom.style.height = '100%';\n            _this.dom.style.transform = 'translateZ(0)';\n            heightTransition = \"height 0s \".concat(ease, \" \").concat(duration);\n            break;\n          }\n        default:\n          break;\n      }\n      clearTimeout(_this.timeout);\n      _this.timeout = setTimeout(function () {\n        if (_this.dom) {\n          _this.dom.style.transition = \"\".concat(transformTransition, \",\").concat(heightTransition ? \"\".concat(heightTransition, \",\") : '').concat(widthTransition);\n          _this.dom.style.transform = '';\n          _this.dom.style.width = '';\n          _this.dom.style.height = '';\n        }\n      });\n    };\n    _this.getCurrentDrawerSome = function () {\n      return !Object.keys(currentDrawer).some(function (key) {\n        return currentDrawer[key];\n      });\n    };\n    _this.getLevelDom = function (_ref) {\n      var level = _ref.level,\n        getContainer = _ref.getContainer;\n      if (windowIsUndefined) {\n        return;\n      }\n      var container = getContainer && getContainer();\n      var parent = container ? container.parentNode : null;\n      _this.levelDom = [];\n      if (level === 'all') {\n        var children = parent ? Array.prototype.slice.call(parent.children) : [];\n        children.forEach(function (child) {\n          if (child.nodeName !== 'SCRIPT' && child.nodeName !== 'STYLE' && child.nodeName !== 'LINK' && child !== container) {\n            _this.levelDom.push(child);\n          }\n        });\n      } else if (level) {\n        dataToArray(level).forEach(function (key) {\n          document.querySelectorAll(key).forEach(function (item) {\n            _this.levelDom.push(item);\n          });\n        });\n      }\n    };\n    _this.getHorizontalBoolAndPlacementName = function () {\n      var placement = _this.props.placement;\n      var isHorizontal = placement === 'left' || placement === 'right';\n      var placementName = \"translate\".concat(isHorizontal ? 'X' : 'Y');\n      return {\n        isHorizontal: isHorizontal,\n        placementName: placementName\n      };\n    };\n    _this.state = {\n      _self: _assertThisInitialized(_this)\n    };\n    return _this;\n  }\n  _createClass(DrawerChild, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n      if (!windowIsUndefined) {\n        var passiveSupported = false;\n        try {\n          window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n            get: function get() {\n              passiveSupported = true;\n              return null;\n            }\n          }));\n        } catch (err) {}\n        this.passive = passiveSupported ? {\n          passive: false\n        } : false;\n      }\n      var _this$props7 = this.props,\n        open = _this$props7.open,\n        getContainer = _this$props7.getContainer,\n        showMask = _this$props7.showMask,\n        autoFocus = _this$props7.autoFocus;\n      var container = getContainer && getContainer();\n      this.drawerId = \"drawer_id_\".concat(Number((Date.now() + Math.random()).toString().replace('.', Math.round(Math.random() * 9).toString())).toString(16));\n      this.getLevelDom(this.props);\n      if (open) {\n        if (container && container.parentNode === document.body) {\n          currentDrawer[this.drawerId] = open;\n        } // 默认打开状态时推出 level;\n\n        this.openLevelTransition();\n        this.forceUpdate(function () {\n          if (autoFocus) {\n            _this2.domFocus();\n          }\n        });\n        if (showMask) {\n          var _this$props$scrollLoc;\n          (_this$props$scrollLoc = this.props.scrollLocker) === null || _this$props$scrollLoc === void 0 ? void 0 : _this$props$scrollLoc.lock();\n        }\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props8 = this.props,\n        open = _this$props8.open,\n        getContainer = _this$props8.getContainer,\n        scrollLocker = _this$props8.scrollLocker,\n        showMask = _this$props8.showMask,\n        autoFocus = _this$props8.autoFocus;\n      var container = getContainer && getContainer();\n      if (open !== prevProps.open) {\n        if (container && container.parentNode === document.body) {\n          currentDrawer[this.drawerId] = !!open;\n        }\n        this.openLevelTransition();\n        if (open) {\n          if (autoFocus) {\n            this.domFocus();\n          }\n          if (showMask) {\n            scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.lock();\n          }\n        } else {\n          scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.unLock();\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var _this$props9 = this.props,\n        open = _this$props9.open,\n        scrollLocker = _this$props9.scrollLocker;\n      delete currentDrawer[this.drawerId];\n      if (open) {\n        this.setLevelTransform(false);\n        document.body.style.touchAction = '';\n      }\n      scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.unLock();\n    }\n  }, {\n    key: \"render\",\n    value:\n    // tslint:disable-next-line:member-ordering\n    function render() {\n      var _classnames,\n        _this3 = this;\n      var _this$props10 = this.props,\n        className = _this$props10.className,\n        children = _this$props10.children,\n        style = _this$props10.style,\n        width = _this$props10.width,\n        height = _this$props10.height,\n        defaultOpen = _this$props10.defaultOpen,\n        $open = _this$props10.open,\n        prefixCls = _this$props10.prefixCls,\n        placement = _this$props10.placement,\n        level = _this$props10.level,\n        levelMove = _this$props10.levelMove,\n        ease = _this$props10.ease,\n        duration = _this$props10.duration,\n        getContainer = _this$props10.getContainer,\n        handler = _this$props10.handler,\n        onChange = _this$props10.onChange,\n        afterVisibleChange = _this$props10.afterVisibleChange,\n        showMask = _this$props10.showMask,\n        maskClosable = _this$props10.maskClosable,\n        maskStyle = _this$props10.maskStyle,\n        onClose = _this$props10.onClose,\n        onHandleClick = _this$props10.onHandleClick,\n        keyboard = _this$props10.keyboard,\n        getOpenCount = _this$props10.getOpenCount,\n        scrollLocker = _this$props10.scrollLocker,\n        contentWrapperStyle = _this$props10.contentWrapperStyle,\n        props = _objectWithoutProperties(_this$props10, _excluded); // 首次渲染都将是关闭状态。\n\n      var open = this.dom ? $open : false;\n      var wrapperClassName = classnames(prefixCls, (_classnames = {}, _defineProperty(_classnames, \"\".concat(prefixCls, \"-\").concat(placement), true), _defineProperty(_classnames, \"\".concat(prefixCls, \"-open\"), open), _defineProperty(_classnames, className || '', !!className), _defineProperty(_classnames, 'no-mask', !showMask), _classnames));\n      var _this$getHorizontalBo2 = this.getHorizontalBoolAndPlacementName(),\n        placementName = _this$getHorizontalBo2.placementName; // 百分比与像素动画不同步，第一次打用后全用像素动画。\n      // const defaultValue = !this.contentDom || !level ? '100%' : `${value}px`;\n\n      var placementPos = placement === 'left' || placement === 'top' ? '-100%' : '100%';\n      var transform = open ? '' : \"\".concat(placementName, \"(\").concat(placementPos, \")\");\n      var handlerChildren = handler && /*#__PURE__*/React.cloneElement(handler, {\n        onClick: function onClick(e) {\n          if (handler.props.onClick) {\n            handler.props.onClick();\n          }\n          if (onHandleClick) {\n            onHandleClick(e);\n          }\n        },\n        ref: function ref(c) {\n          _this3.handlerDom = c;\n        }\n      });\n      return /*#__PURE__*/React.createElement(\"div\", _extends({}, omit(props, ['switchScrollingEffect', 'autoFocus']), {\n        tabIndex: -1,\n        className: wrapperClassName,\n        style: style,\n        ref: function ref(c) {\n          _this3.dom = c;\n        },\n        onKeyDown: open && keyboard ? this.onKeyDown : undefined,\n        onTransitionEnd: this.onWrapperTransitionEnd\n      }), showMask && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-mask\"),\n        onClick: maskClosable ? onClose : undefined,\n        style: maskStyle,\n        ref: function ref(c) {\n          _this3.maskDom = c;\n        }\n      }), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content-wrapper\"),\n        style: _objectSpread({\n          transform: transform,\n          msTransform: transform,\n          width: isNumeric(width) ? \"\".concat(width, \"px\") : width,\n          height: isNumeric(height) ? \"\".concat(height, \"px\") : height\n        }, contentWrapperStyle),\n        ref: function ref(c) {\n          _this3.contentWrapper = c;\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content\"),\n        ref: function ref(c) {\n          _this3.contentDom = c;\n        }\n      }, children), handlerChildren));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, _ref2) {\n      var prevProps = _ref2.prevProps,\n        _self = _ref2._self;\n      var nextState = {\n        prevProps: props\n      };\n      if (prevProps !== undefined) {\n        var placement = props.placement,\n          level = props.level;\n        if (placement !== prevProps.placement) {\n          // test 的 bug, 有动画过场，删除 dom\n          _self.contentDom = null;\n        }\n        if (level !== prevProps.level) {\n          _self.getLevelDom(props);\n        }\n      }\n      return nextState;\n    }\n  }]);\n  return DrawerChild;\n}(React.Component);\nexport default DrawerChild;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_objectWithoutProperties", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_excluded", "React", "classnames", "getScrollBarSize", "KeyCode", "omit", "addEventListener", "dataToArray", "getTouchParentScroll", "isNumeric", "removeEventListener", "transformArguments", "transitionEnd", "transitionStr", "windowIsUndefined", "currentDrawer", "Drawer<PERSON><PERSON><PERSON>", "_React$Component", "_super", "props", "_this", "call", "levelDom", "dom", "contentWrapper", "contentDom", "maskDom", "handlerDom", "drawerId", "timeout", "passive", "startPos", "domFocus", "focus", "removeStartHandler", "e", "touches", "length", "x", "clientX", "y", "clientY", "removeMove<PERSON><PERSON>ler", "changedTouches", "currentTarget", "differX", "differY", "target", "cancelable", "preventDefault", "style", "transition", "onKeyDown", "keyCode", "ESC", "onClose", "stopPropagation", "onWrapperTransitionEnd", "_this$props", "open", "afterVisibleChange", "propertyName", "match", "getCurrentDrawerSome", "document", "body", "overflowX", "left", "width", "openLevelTransition", "_this$props2", "height", "_this$getHorizontalBo", "getHorizontalBoolAndPlacementName", "isHorizontal", "placementName", "contentValue", "getBoundingClientRect", "value", "setLevelAndScrolling", "setLevelTransform", "right", "_this$props3", "placement", "levelMove", "duration", "ease", "showMask", "for<PERSON>ach", "concat", "levelValue", "$levelMove", "$value", "placementPos", "transform", "onChange", "scrollHeight", "window", "innerHeight", "documentElement", "clientHeight", "innerWidth", "offsetWidth", "toggleScrollingToDrawerAndBody", "_this$props4", "getContainer", "container", "parentNode", "eventArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overflow", "addScrollingEffect", "touchAction", "item", "i", "remScrollingEffect", "_this$props5", "widthTransition", "transformTransition", "clearTimeout", "setTimeout", "_this$props6", "heightTransition", "Object", "keys", "some", "key", "getLevelDom", "_ref", "level", "parent", "children", "Array", "prototype", "slice", "child", "nodeName", "push", "querySelectorAll", "state", "_self", "componentDidMount", "_this2", "passiveSupported", "defineProperty", "get", "err", "_this$props7", "autoFocus", "Number", "Date", "now", "Math", "random", "toString", "replace", "round", "forceUpdate", "_this$props$scrollLoc", "scrollLocker", "lock", "componentDidUpdate", "prevProps", "_this$props8", "unLock", "componentWillUnmount", "_this$props9", "render", "_classnames", "_this3", "_this$props10", "className", "defaultOpen", "$open", "prefixCls", "handler", "maskClosable", "maskStyle", "onHandleClick", "keyboard", "getOpenCount", "contentWrapperStyle", "wrapperClassName", "_this$getHorizontalBo2", "handler<PERSON><PERSON><PERSON><PERSON>", "cloneElement", "onClick", "ref", "c", "createElement", "tabIndex", "undefined", "onTransitionEnd", "msTransform", "getDerivedStateFromProps", "_ref2", "nextState", "Component"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-drawer/es/DrawerChild.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"className\", \"children\", \"style\", \"width\", \"height\", \"defaultOpen\", \"open\", \"prefixCls\", \"placement\", \"level\", \"levelMove\", \"ease\", \"duration\", \"getContainer\", \"handler\", \"onChange\", \"afterVisibleChange\", \"showMask\", \"maskClosable\", \"maskStyle\", \"onClose\", \"onHandleClick\", \"keyboard\", \"getOpenCount\", \"scrollLocker\", \"contentWrapperStyle\"];\nimport * as React from 'react';\nimport classnames from 'classnames';\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport { addEventListener, dataToArray, getTouchParentScroll, isNumeric, removeEventListener, transformArguments, transitionEnd, transitionStr, windowIsUndefined } from './utils';\nvar currentDrawer = {};\n\nvar DrawerChild = /*#__PURE__*/function (_React$Component) {\n  _inherits(DrawerChild, _React$Component);\n\n  var _super = _createSuper(DrawerChild);\n\n  function DrawerChild(props) {\n    var _this;\n\n    _classCallCheck(this, DrawerChild);\n\n    _this = _super.call(this, props);\n    _this.levelDom = void 0;\n    _this.dom = void 0;\n    _this.contentWrapper = void 0;\n    _this.contentDom = void 0;\n    _this.maskDom = void 0;\n    _this.handlerDom = void 0;\n    _this.drawerId = void 0;\n    _this.timeout = void 0;\n    _this.passive = void 0;\n    _this.startPos = void 0;\n\n    _this.domFocus = function () {\n      if (_this.dom) {\n        _this.dom.focus();\n      }\n    };\n\n    _this.removeStartHandler = function (e) {\n      if (e.touches.length > 1) {\n        // need clear the startPos when another touch event happens\n        _this.startPos = null;\n        return;\n      }\n\n      _this.startPos = {\n        x: e.touches[0].clientX,\n        y: e.touches[0].clientY\n      };\n    };\n\n    _this.removeMoveHandler = function (e) {\n      // the startPos may be null or undefined\n      if (e.changedTouches.length > 1 || !_this.startPos) {\n        return;\n      }\n\n      var currentTarget = e.currentTarget;\n      var differX = e.changedTouches[0].clientX - _this.startPos.x;\n      var differY = e.changedTouches[0].clientY - _this.startPos.y;\n\n      if ((currentTarget === _this.maskDom || currentTarget === _this.handlerDom || currentTarget === _this.contentDom && getTouchParentScroll(currentTarget, e.target, differX, differY)) && e.cancelable) {\n        e.preventDefault();\n      }\n    };\n\n    _this.transitionEnd = function (e) {\n      var dom = e.target;\n      removeEventListener(dom, transitionEnd, _this.transitionEnd);\n      dom.style.transition = '';\n    };\n\n    _this.onKeyDown = function (e) {\n      if (e.keyCode === KeyCode.ESC) {\n        var onClose = _this.props.onClose;\n        e.stopPropagation();\n\n        if (onClose) {\n          onClose(e);\n        }\n      }\n    };\n\n    _this.onWrapperTransitionEnd = function (e) {\n      var _this$props = _this.props,\n          open = _this$props.open,\n          afterVisibleChange = _this$props.afterVisibleChange;\n\n      if (e.target === _this.contentWrapper && e.propertyName.match(/transform$/)) {\n        _this.dom.style.transition = '';\n\n        if (!open && _this.getCurrentDrawerSome()) {\n          document.body.style.overflowX = '';\n\n          if (_this.maskDom) {\n            _this.maskDom.style.left = '';\n            _this.maskDom.style.width = '';\n          }\n        }\n\n        if (afterVisibleChange) {\n          afterVisibleChange(!!open);\n        }\n      }\n    };\n\n    _this.openLevelTransition = function () {\n      var _this$props2 = _this.props,\n          open = _this$props2.open,\n          width = _this$props2.width,\n          height = _this$props2.height;\n\n      var _this$getHorizontalBo = _this.getHorizontalBoolAndPlacementName(),\n          isHorizontal = _this$getHorizontalBo.isHorizontal,\n          placementName = _this$getHorizontalBo.placementName;\n\n      var contentValue = _this.contentDom ? _this.contentDom.getBoundingClientRect()[isHorizontal ? 'width' : 'height'] : 0;\n      var value = (isHorizontal ? width : height) || contentValue;\n\n      _this.setLevelAndScrolling(open, placementName, value);\n    };\n\n    _this.setLevelTransform = function (open, placementName, value, right) {\n      var _this$props3 = _this.props,\n          placement = _this$props3.placement,\n          levelMove = _this$props3.levelMove,\n          duration = _this$props3.duration,\n          ease = _this$props3.ease,\n          showMask = _this$props3.showMask; // router 切换时可能会导至页面失去滚动条，所以需要时时获取。\n\n      _this.levelDom.forEach(function (dom) {\n        dom.style.transition = \"transform \".concat(duration, \" \").concat(ease);\n        addEventListener(dom, transitionEnd, _this.transitionEnd);\n        var levelValue = open ? value : 0;\n\n        if (levelMove) {\n          var $levelMove = transformArguments(levelMove, {\n            target: dom,\n            open: open\n          });\n          levelValue = open ? $levelMove[0] : $levelMove[1] || 0;\n        }\n\n        var $value = typeof levelValue === 'number' ? \"\".concat(levelValue, \"px\") : levelValue;\n        var placementPos = placement === 'left' || placement === 'top' ? $value : \"-\".concat($value);\n        placementPos = showMask && placement === 'right' && right ? \"calc(\".concat(placementPos, \" + \").concat(right, \"px)\") : placementPos;\n        dom.style.transform = levelValue ? \"\".concat(placementName, \"(\").concat(placementPos, \")\") : '';\n      });\n    };\n\n    _this.setLevelAndScrolling = function (open, placementName, value) {\n      var onChange = _this.props.onChange;\n\n      if (!windowIsUndefined) {\n        var right = document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth ? getScrollBarSize(true) : 0;\n\n        _this.setLevelTransform(open, placementName, value, right);\n\n        _this.toggleScrollingToDrawerAndBody(right);\n      }\n\n      if (onChange) {\n        onChange(open);\n      }\n    };\n\n    _this.toggleScrollingToDrawerAndBody = function (right) {\n      var _this$props4 = _this.props,\n          getContainer = _this$props4.getContainer,\n          showMask = _this$props4.showMask,\n          open = _this$props4.open;\n      var container = getContainer && getContainer(); // 处理 body 滚动\n\n      if (container && container.parentNode === document.body && showMask) {\n        var eventArray = ['touchstart'];\n        var domArray = [document.body, _this.maskDom, _this.handlerDom, _this.contentDom];\n\n        if (open && document.body.style.overflow !== 'hidden') {\n          if (right) {\n            _this.addScrollingEffect(right);\n          }\n\n          document.body.style.touchAction = 'none'; // 手机禁滚\n\n          domArray.forEach(function (item, i) {\n            if (!item) {\n              return;\n            }\n\n            addEventListener(item, eventArray[i] || 'touchmove', i ? _this.removeMoveHandler : _this.removeStartHandler, _this.passive);\n          });\n        } else if (_this.getCurrentDrawerSome()) {\n          document.body.style.touchAction = '';\n\n          if (right) {\n            _this.remScrollingEffect(right);\n          } // 恢复事件\n\n\n          domArray.forEach(function (item, i) {\n            if (!item) {\n              return;\n            }\n\n            removeEventListener(item, eventArray[i] || 'touchmove', i ? _this.removeMoveHandler : _this.removeStartHandler, _this.passive);\n          });\n        }\n      }\n    };\n\n    _this.addScrollingEffect = function (right) {\n      var _this$props5 = _this.props,\n          placement = _this$props5.placement,\n          duration = _this$props5.duration,\n          ease = _this$props5.ease;\n      var widthTransition = \"width \".concat(duration, \" \").concat(ease);\n      var transformTransition = \"transform \".concat(duration, \" \").concat(ease);\n      _this.dom.style.transition = 'none';\n\n      switch (placement) {\n        case 'right':\n          _this.dom.style.transform = \"translateX(-\".concat(right, \"px)\");\n          break;\n\n        case 'top':\n        case 'bottom':\n          _this.dom.style.width = \"calc(100% - \".concat(right, \"px)\");\n          _this.dom.style.transform = 'translateZ(0)';\n          break;\n\n        default:\n          break;\n      }\n\n      clearTimeout(_this.timeout);\n      _this.timeout = setTimeout(function () {\n        if (_this.dom) {\n          _this.dom.style.transition = \"\".concat(transformTransition, \",\").concat(widthTransition);\n          _this.dom.style.width = '';\n          _this.dom.style.transform = '';\n        }\n      });\n    };\n\n    _this.remScrollingEffect = function (right) {\n      var _this$props6 = _this.props,\n          placement = _this$props6.placement,\n          duration = _this$props6.duration,\n          ease = _this$props6.ease;\n\n      if (transitionStr) {\n        document.body.style.overflowX = 'hidden';\n      }\n\n      _this.dom.style.transition = 'none';\n      var heightTransition;\n      var widthTransition = \"width \".concat(duration, \" \").concat(ease);\n      var transformTransition = \"transform \".concat(duration, \" \").concat(ease);\n\n      switch (placement) {\n        case 'left':\n          {\n            _this.dom.style.width = '100%';\n            widthTransition = \"width 0s \".concat(ease, \" \").concat(duration);\n            break;\n          }\n\n        case 'right':\n          {\n            _this.dom.style.transform = \"translateX(\".concat(right, \"px)\");\n            _this.dom.style.width = '100%';\n            widthTransition = \"width 0s \".concat(ease, \" \").concat(duration);\n\n            if (_this.maskDom) {\n              _this.maskDom.style.left = \"-\".concat(right, \"px\");\n              _this.maskDom.style.width = \"calc(100% + \".concat(right, \"px)\");\n            }\n\n            break;\n          }\n\n        case 'top':\n        case 'bottom':\n          {\n            _this.dom.style.width = \"calc(100% + \".concat(right, \"px)\");\n            _this.dom.style.height = '100%';\n            _this.dom.style.transform = 'translateZ(0)';\n            heightTransition = \"height 0s \".concat(ease, \" \").concat(duration);\n            break;\n          }\n\n        default:\n          break;\n      }\n\n      clearTimeout(_this.timeout);\n      _this.timeout = setTimeout(function () {\n        if (_this.dom) {\n          _this.dom.style.transition = \"\".concat(transformTransition, \",\").concat(heightTransition ? \"\".concat(heightTransition, \",\") : '').concat(widthTransition);\n          _this.dom.style.transform = '';\n          _this.dom.style.width = '';\n          _this.dom.style.height = '';\n        }\n      });\n    };\n\n    _this.getCurrentDrawerSome = function () {\n      return !Object.keys(currentDrawer).some(function (key) {\n        return currentDrawer[key];\n      });\n    };\n\n    _this.getLevelDom = function (_ref) {\n      var level = _ref.level,\n          getContainer = _ref.getContainer;\n\n      if (windowIsUndefined) {\n        return;\n      }\n\n      var container = getContainer && getContainer();\n      var parent = container ? container.parentNode : null;\n      _this.levelDom = [];\n\n      if (level === 'all') {\n        var children = parent ? Array.prototype.slice.call(parent.children) : [];\n        children.forEach(function (child) {\n          if (child.nodeName !== 'SCRIPT' && child.nodeName !== 'STYLE' && child.nodeName !== 'LINK' && child !== container) {\n            _this.levelDom.push(child);\n          }\n        });\n      } else if (level) {\n        dataToArray(level).forEach(function (key) {\n          document.querySelectorAll(key).forEach(function (item) {\n            _this.levelDom.push(item);\n          });\n        });\n      }\n    };\n\n    _this.getHorizontalBoolAndPlacementName = function () {\n      var placement = _this.props.placement;\n      var isHorizontal = placement === 'left' || placement === 'right';\n      var placementName = \"translate\".concat(isHorizontal ? 'X' : 'Y');\n      return {\n        isHorizontal: isHorizontal,\n        placementName: placementName\n      };\n    };\n\n    _this.state = {\n      _self: _assertThisInitialized(_this)\n    };\n    return _this;\n  }\n\n  _createClass(DrawerChild, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n\n      if (!windowIsUndefined) {\n        var passiveSupported = false;\n\n        try {\n          window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n            get: function get() {\n              passiveSupported = true;\n              return null;\n            }\n          }));\n        } catch (err) {}\n\n        this.passive = passiveSupported ? {\n          passive: false\n        } : false;\n      }\n\n      var _this$props7 = this.props,\n          open = _this$props7.open,\n          getContainer = _this$props7.getContainer,\n          showMask = _this$props7.showMask,\n          autoFocus = _this$props7.autoFocus;\n      var container = getContainer && getContainer();\n      this.drawerId = \"drawer_id_\".concat(Number((Date.now() + Math.random()).toString().replace('.', Math.round(Math.random() * 9).toString())).toString(16));\n      this.getLevelDom(this.props);\n\n      if (open) {\n        if (container && container.parentNode === document.body) {\n          currentDrawer[this.drawerId] = open;\n        } // 默认打开状态时推出 level;\n\n\n        this.openLevelTransition();\n        this.forceUpdate(function () {\n          if (autoFocus) {\n            _this2.domFocus();\n          }\n        });\n\n        if (showMask) {\n          var _this$props$scrollLoc;\n\n          (_this$props$scrollLoc = this.props.scrollLocker) === null || _this$props$scrollLoc === void 0 ? void 0 : _this$props$scrollLoc.lock();\n        }\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props8 = this.props,\n          open = _this$props8.open,\n          getContainer = _this$props8.getContainer,\n          scrollLocker = _this$props8.scrollLocker,\n          showMask = _this$props8.showMask,\n          autoFocus = _this$props8.autoFocus;\n      var container = getContainer && getContainer();\n\n      if (open !== prevProps.open) {\n        if (container && container.parentNode === document.body) {\n          currentDrawer[this.drawerId] = !!open;\n        }\n\n        this.openLevelTransition();\n\n        if (open) {\n          if (autoFocus) {\n            this.domFocus();\n          }\n\n          if (showMask) {\n            scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.lock();\n          }\n        } else {\n          scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.unLock();\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var _this$props9 = this.props,\n          open = _this$props9.open,\n          scrollLocker = _this$props9.scrollLocker;\n      delete currentDrawer[this.drawerId];\n\n      if (open) {\n        this.setLevelTransform(false);\n        document.body.style.touchAction = '';\n      }\n\n      scrollLocker === null || scrollLocker === void 0 ? void 0 : scrollLocker.unLock();\n    }\n  }, {\n    key: \"render\",\n    value: // tslint:disable-next-line:member-ordering\n    function render() {\n      var _classnames,\n          _this3 = this;\n\n      var _this$props10 = this.props,\n          className = _this$props10.className,\n          children = _this$props10.children,\n          style = _this$props10.style,\n          width = _this$props10.width,\n          height = _this$props10.height,\n          defaultOpen = _this$props10.defaultOpen,\n          $open = _this$props10.open,\n          prefixCls = _this$props10.prefixCls,\n          placement = _this$props10.placement,\n          level = _this$props10.level,\n          levelMove = _this$props10.levelMove,\n          ease = _this$props10.ease,\n          duration = _this$props10.duration,\n          getContainer = _this$props10.getContainer,\n          handler = _this$props10.handler,\n          onChange = _this$props10.onChange,\n          afterVisibleChange = _this$props10.afterVisibleChange,\n          showMask = _this$props10.showMask,\n          maskClosable = _this$props10.maskClosable,\n          maskStyle = _this$props10.maskStyle,\n          onClose = _this$props10.onClose,\n          onHandleClick = _this$props10.onHandleClick,\n          keyboard = _this$props10.keyboard,\n          getOpenCount = _this$props10.getOpenCount,\n          scrollLocker = _this$props10.scrollLocker,\n          contentWrapperStyle = _this$props10.contentWrapperStyle,\n          props = _objectWithoutProperties(_this$props10, _excluded); // 首次渲染都将是关闭状态。\n\n\n      var open = this.dom ? $open : false;\n      var wrapperClassName = classnames(prefixCls, (_classnames = {}, _defineProperty(_classnames, \"\".concat(prefixCls, \"-\").concat(placement), true), _defineProperty(_classnames, \"\".concat(prefixCls, \"-open\"), open), _defineProperty(_classnames, className || '', !!className), _defineProperty(_classnames, 'no-mask', !showMask), _classnames));\n\n      var _this$getHorizontalBo2 = this.getHorizontalBoolAndPlacementName(),\n          placementName = _this$getHorizontalBo2.placementName; // 百分比与像素动画不同步，第一次打用后全用像素动画。\n      // const defaultValue = !this.contentDom || !level ? '100%' : `${value}px`;\n\n\n      var placementPos = placement === 'left' || placement === 'top' ? '-100%' : '100%';\n      var transform = open ? '' : \"\".concat(placementName, \"(\").concat(placementPos, \")\");\n      var handlerChildren = handler && /*#__PURE__*/React.cloneElement(handler, {\n        onClick: function onClick(e) {\n          if (handler.props.onClick) {\n            handler.props.onClick();\n          }\n\n          if (onHandleClick) {\n            onHandleClick(e);\n          }\n        },\n        ref: function ref(c) {\n          _this3.handlerDom = c;\n        }\n      });\n      return /*#__PURE__*/React.createElement(\"div\", _extends({}, omit(props, ['switchScrollingEffect', 'autoFocus']), {\n        tabIndex: -1,\n        className: wrapperClassName,\n        style: style,\n        ref: function ref(c) {\n          _this3.dom = c;\n        },\n        onKeyDown: open && keyboard ? this.onKeyDown : undefined,\n        onTransitionEnd: this.onWrapperTransitionEnd\n      }), showMask && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-mask\"),\n        onClick: maskClosable ? onClose : undefined,\n        style: maskStyle,\n        ref: function ref(c) {\n          _this3.maskDom = c;\n        }\n      }), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content-wrapper\"),\n        style: _objectSpread({\n          transform: transform,\n          msTransform: transform,\n          width: isNumeric(width) ? \"\".concat(width, \"px\") : width,\n          height: isNumeric(height) ? \"\".concat(height, \"px\") : height\n        }, contentWrapperStyle),\n        ref: function ref(c) {\n          _this3.contentWrapper = c;\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content\"),\n        ref: function ref(c) {\n          _this3.contentDom = c;\n        }\n      }, children), handlerChildren));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, _ref2) {\n      var prevProps = _ref2.prevProps,\n          _self = _ref2._self;\n      var nextState = {\n        prevProps: props\n      };\n\n      if (prevProps !== undefined) {\n        var placement = props.placement,\n            level = props.level;\n\n        if (placement !== prevProps.placement) {\n          // test 的 bug, 有动画过场，删除 dom\n          _self.contentDom = null;\n        }\n\n        if (level !== prevProps.level) {\n          _self.getLevelDom(props);\n        }\n      }\n\n      return nextState;\n    }\n  }]);\n\n  return DrawerChild;\n}(React.Component);\n\nexport default DrawerChild;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,oBAAoB,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,qBAAqB,CAAC;AACrW,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,QAAQ,SAAS;AAClL,IAAIC,aAAa,GAAG,CAAC,CAAC;AAEtB,IAAIC,WAAW,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACzDnB,SAAS,CAACkB,WAAW,EAAEC,gBAAgB,CAAC;EAExC,IAAIC,MAAM,GAAGnB,YAAY,CAACiB,WAAW,CAAC;EAEtC,SAASA,WAAWA,CAACG,KAAK,EAAE;IAC1B,IAAIC,KAAK;IAETzB,eAAe,CAAC,IAAI,EAAEqB,WAAW,CAAC;IAElCI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,QAAQ,GAAG,KAAK,CAAC;IACvBF,KAAK,CAACG,GAAG,GAAG,KAAK,CAAC;IAClBH,KAAK,CAACI,cAAc,GAAG,KAAK,CAAC;IAC7BJ,KAAK,CAACK,UAAU,GAAG,KAAK,CAAC;IACzBL,KAAK,CAACM,OAAO,GAAG,KAAK,CAAC;IACtBN,KAAK,CAACO,UAAU,GAAG,KAAK,CAAC;IACzBP,KAAK,CAACQ,QAAQ,GAAG,KAAK,CAAC;IACvBR,KAAK,CAACS,OAAO,GAAG,KAAK,CAAC;IACtBT,KAAK,CAACU,OAAO,GAAG,KAAK,CAAC;IACtBV,KAAK,CAACW,QAAQ,GAAG,KAAK,CAAC;IAEvBX,KAAK,CAACY,QAAQ,GAAG,YAAY;MAC3B,IAAIZ,KAAK,CAACG,GAAG,EAAE;QACbH,KAAK,CAACG,GAAG,CAACU,KAAK,CAAC,CAAC;MACnB;IACF,CAAC;IAEDb,KAAK,CAACc,kBAAkB,GAAG,UAAUC,CAAC,EAAE;MACtC,IAAIA,CAAC,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACxB;QACAjB,KAAK,CAACW,QAAQ,GAAG,IAAI;QACrB;MACF;MAEAX,KAAK,CAACW,QAAQ,GAAG;QACfO,CAAC,EAAEH,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO;QACvBC,CAAC,EAAEL,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACK;MAClB,CAAC;IACH,CAAC;IAEDrB,KAAK,CAACsB,iBAAiB,GAAG,UAAUP,CAAC,EAAE;MACrC;MACA,IAAIA,CAAC,CAACQ,cAAc,CAACN,MAAM,GAAG,CAAC,IAAI,CAACjB,KAAK,CAACW,QAAQ,EAAE;QAClD;MACF;MAEA,IAAIa,aAAa,GAAGT,CAAC,CAACS,aAAa;MACnC,IAAIC,OAAO,GAAGV,CAAC,CAACQ,cAAc,CAAC,CAAC,CAAC,CAACJ,OAAO,GAAGnB,KAAK,CAACW,QAAQ,CAACO,CAAC;MAC5D,IAAIQ,OAAO,GAAGX,CAAC,CAACQ,cAAc,CAAC,CAAC,CAAC,CAACF,OAAO,GAAGrB,KAAK,CAACW,QAAQ,CAACS,CAAC;MAE5D,IAAI,CAACI,aAAa,KAAKxB,KAAK,CAACM,OAAO,IAAIkB,aAAa,KAAKxB,KAAK,CAACO,UAAU,IAAIiB,aAAa,KAAKxB,KAAK,CAACK,UAAU,IAAIjB,oBAAoB,CAACoC,aAAa,EAAET,CAAC,CAACY,MAAM,EAAEF,OAAO,EAAEC,OAAO,CAAC,KAAKX,CAAC,CAACa,UAAU,EAAE;QACpMb,CAAC,CAACc,cAAc,CAAC,CAAC;MACpB;IACF,CAAC;IAED7B,KAAK,CAACR,aAAa,GAAG,UAAUuB,CAAC,EAAE;MACjC,IAAIZ,GAAG,GAAGY,CAAC,CAACY,MAAM;MAClBrC,mBAAmB,CAACa,GAAG,EAAEX,aAAa,EAAEQ,KAAK,CAACR,aAAa,CAAC;MAC5DW,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,EAAE;IAC3B,CAAC;IAED/B,KAAK,CAACgC,SAAS,GAAG,UAAUjB,CAAC,EAAE;MAC7B,IAAIA,CAAC,CAACkB,OAAO,KAAKjD,OAAO,CAACkD,GAAG,EAAE;QAC7B,IAAIC,OAAO,GAAGnC,KAAK,CAACD,KAAK,CAACoC,OAAO;QACjCpB,CAAC,CAACqB,eAAe,CAAC,CAAC;QAEnB,IAAID,OAAO,EAAE;UACXA,OAAO,CAACpB,CAAC,CAAC;QACZ;MACF;IACF,CAAC;IAEDf,KAAK,CAACqC,sBAAsB,GAAG,UAAUtB,CAAC,EAAE;MAC1C,IAAIuB,WAAW,GAAGtC,KAAK,CAACD,KAAK;QACzBwC,IAAI,GAAGD,WAAW,CAACC,IAAI;QACvBC,kBAAkB,GAAGF,WAAW,CAACE,kBAAkB;MAEvD,IAAIzB,CAAC,CAACY,MAAM,KAAK3B,KAAK,CAACI,cAAc,IAAIW,CAAC,CAAC0B,YAAY,CAACC,KAAK,CAAC,YAAY,CAAC,EAAE;QAC3E1C,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,EAAE;QAE/B,IAAI,CAACQ,IAAI,IAAIvC,KAAK,CAAC2C,oBAAoB,CAAC,CAAC,EAAE;UACzCC,QAAQ,CAACC,IAAI,CAACf,KAAK,CAACgB,SAAS,GAAG,EAAE;UAElC,IAAI9C,KAAK,CAACM,OAAO,EAAE;YACjBN,KAAK,CAACM,OAAO,CAACwB,KAAK,CAACiB,IAAI,GAAG,EAAE;YAC7B/C,KAAK,CAACM,OAAO,CAACwB,KAAK,CAACkB,KAAK,GAAG,EAAE;UAChC;QACF;QAEA,IAAIR,kBAAkB,EAAE;UACtBA,kBAAkB,CAAC,CAAC,CAACD,IAAI,CAAC;QAC5B;MACF;IACF,CAAC;IAEDvC,KAAK,CAACiD,mBAAmB,GAAG,YAAY;MACtC,IAAIC,YAAY,GAAGlD,KAAK,CAACD,KAAK;QAC1BwC,IAAI,GAAGW,YAAY,CAACX,IAAI;QACxBS,KAAK,GAAGE,YAAY,CAACF,KAAK;QAC1BG,MAAM,GAAGD,YAAY,CAACC,MAAM;MAEhC,IAAIC,qBAAqB,GAAGpD,KAAK,CAACqD,iCAAiC,CAAC,CAAC;QACjEC,YAAY,GAAGF,qBAAqB,CAACE,YAAY;QACjDC,aAAa,GAAGH,qBAAqB,CAACG,aAAa;MAEvD,IAAIC,YAAY,GAAGxD,KAAK,CAACK,UAAU,GAAGL,KAAK,CAACK,UAAU,CAACoD,qBAAqB,CAAC,CAAC,CAACH,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;MACrH,IAAII,KAAK,GAAG,CAACJ,YAAY,GAAGN,KAAK,GAAGG,MAAM,KAAKK,YAAY;MAE3DxD,KAAK,CAAC2D,oBAAoB,CAACpB,IAAI,EAAEgB,aAAa,EAAEG,KAAK,CAAC;IACxD,CAAC;IAED1D,KAAK,CAAC4D,iBAAiB,GAAG,UAAUrB,IAAI,EAAEgB,aAAa,EAAEG,KAAK,EAAEG,KAAK,EAAE;MACrE,IAAIC,YAAY,GAAG9D,KAAK,CAACD,KAAK;QAC1BgE,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;QAChCC,IAAI,GAAGJ,YAAY,CAACI,IAAI;QACxBC,QAAQ,GAAGL,YAAY,CAACK,QAAQ,CAAC,CAAC;;MAEtCnE,KAAK,CAACE,QAAQ,CAACkE,OAAO,CAAC,UAAUjE,GAAG,EAAE;QACpCA,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,YAAY,CAACsC,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,IAAI,CAAC;QACtEhF,gBAAgB,CAACiB,GAAG,EAAEX,aAAa,EAAEQ,KAAK,CAACR,aAAa,CAAC;QACzD,IAAI8E,UAAU,GAAG/B,IAAI,GAAGmB,KAAK,GAAG,CAAC;QAEjC,IAAIM,SAAS,EAAE;UACb,IAAIO,UAAU,GAAGhF,kBAAkB,CAACyE,SAAS,EAAE;YAC7CrC,MAAM,EAAExB,GAAG;YACXoC,IAAI,EAAEA;UACR,CAAC,CAAC;UACF+B,UAAU,GAAG/B,IAAI,GAAGgC,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;QACxD;QAEA,IAAIC,MAAM,GAAG,OAAOF,UAAU,KAAK,QAAQ,GAAG,EAAE,CAACD,MAAM,CAACC,UAAU,EAAE,IAAI,CAAC,GAAGA,UAAU;QACtF,IAAIG,YAAY,GAAGV,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,GAAGS,MAAM,GAAG,GAAG,CAACH,MAAM,CAACG,MAAM,CAAC;QAC5FC,YAAY,GAAGN,QAAQ,IAAIJ,SAAS,KAAK,OAAO,IAAIF,KAAK,GAAG,OAAO,CAACQ,MAAM,CAACI,YAAY,EAAE,KAAK,CAAC,CAACJ,MAAM,CAACR,KAAK,EAAE,KAAK,CAAC,GAAGY,YAAY;QACnItE,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAGJ,UAAU,GAAG,EAAE,CAACD,MAAM,CAACd,aAAa,EAAE,GAAG,CAAC,CAACc,MAAM,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,EAAE;MACjG,CAAC,CAAC;IACJ,CAAC;IAEDzE,KAAK,CAAC2D,oBAAoB,GAAG,UAAUpB,IAAI,EAAEgB,aAAa,EAAEG,KAAK,EAAE;MACjE,IAAIiB,QAAQ,GAAG3E,KAAK,CAACD,KAAK,CAAC4E,QAAQ;MAEnC,IAAI,CAACjF,iBAAiB,EAAE;QACtB,IAAImE,KAAK,GAAGjB,QAAQ,CAACC,IAAI,CAAC+B,YAAY,IAAIC,MAAM,CAACC,WAAW,IAAIlC,QAAQ,CAACmC,eAAe,CAACC,YAAY,CAAC,IAAIH,MAAM,CAACI,UAAU,GAAGrC,QAAQ,CAACC,IAAI,CAACqC,WAAW,GAAGnG,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC;QAEpLiB,KAAK,CAAC4D,iBAAiB,CAACrB,IAAI,EAAEgB,aAAa,EAAEG,KAAK,EAAEG,KAAK,CAAC;QAE1D7D,KAAK,CAACmF,8BAA8B,CAACtB,KAAK,CAAC;MAC7C;MAEA,IAAIc,QAAQ,EAAE;QACZA,QAAQ,CAACpC,IAAI,CAAC;MAChB;IACF,CAAC;IAEDvC,KAAK,CAACmF,8BAA8B,GAAG,UAAUtB,KAAK,EAAE;MACtD,IAAIuB,YAAY,GAAGpF,KAAK,CAACD,KAAK;QAC1BsF,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxClB,QAAQ,GAAGiB,YAAY,CAACjB,QAAQ;QAChC5B,IAAI,GAAG6C,YAAY,CAAC7C,IAAI;MAC5B,IAAI+C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAC;;MAEhD,IAAIC,SAAS,IAAIA,SAAS,CAACC,UAAU,KAAK3C,QAAQ,CAACC,IAAI,IAAIsB,QAAQ,EAAE;QACnE,IAAIqB,UAAU,GAAG,CAAC,YAAY,CAAC;QAC/B,IAAIC,QAAQ,GAAG,CAAC7C,QAAQ,CAACC,IAAI,EAAE7C,KAAK,CAACM,OAAO,EAAEN,KAAK,CAACO,UAAU,EAAEP,KAAK,CAACK,UAAU,CAAC;QAEjF,IAAIkC,IAAI,IAAIK,QAAQ,CAACC,IAAI,CAACf,KAAK,CAAC4D,QAAQ,KAAK,QAAQ,EAAE;UACrD,IAAI7B,KAAK,EAAE;YACT7D,KAAK,CAAC2F,kBAAkB,CAAC9B,KAAK,CAAC;UACjC;UAEAjB,QAAQ,CAACC,IAAI,CAACf,KAAK,CAAC8D,WAAW,GAAG,MAAM,CAAC,CAAC;;UAE1CH,QAAQ,CAACrB,OAAO,CAAC,UAAUyB,IAAI,EAAEC,CAAC,EAAE;YAClC,IAAI,CAACD,IAAI,EAAE;cACT;YACF;YAEA3G,gBAAgB,CAAC2G,IAAI,EAAEL,UAAU,CAACM,CAAC,CAAC,IAAI,WAAW,EAAEA,CAAC,GAAG9F,KAAK,CAACsB,iBAAiB,GAAGtB,KAAK,CAACc,kBAAkB,EAAEd,KAAK,CAACU,OAAO,CAAC;UAC7H,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIV,KAAK,CAAC2C,oBAAoB,CAAC,CAAC,EAAE;UACvCC,QAAQ,CAACC,IAAI,CAACf,KAAK,CAAC8D,WAAW,GAAG,EAAE;UAEpC,IAAI/B,KAAK,EAAE;YACT7D,KAAK,CAAC+F,kBAAkB,CAAClC,KAAK,CAAC;UACjC,CAAC,CAAC;;UAGF4B,QAAQ,CAACrB,OAAO,CAAC,UAAUyB,IAAI,EAAEC,CAAC,EAAE;YAClC,IAAI,CAACD,IAAI,EAAE;cACT;YACF;YAEAvG,mBAAmB,CAACuG,IAAI,EAAEL,UAAU,CAACM,CAAC,CAAC,IAAI,WAAW,EAAEA,CAAC,GAAG9F,KAAK,CAACsB,iBAAiB,GAAGtB,KAAK,CAACc,kBAAkB,EAAEd,KAAK,CAACU,OAAO,CAAC;UAChI,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IAEDV,KAAK,CAAC2F,kBAAkB,GAAG,UAAU9B,KAAK,EAAE;MAC1C,IAAImC,YAAY,GAAGhG,KAAK,CAACD,KAAK;QAC1BgE,SAAS,GAAGiC,YAAY,CAACjC,SAAS;QAClCE,QAAQ,GAAG+B,YAAY,CAAC/B,QAAQ;QAChCC,IAAI,GAAG8B,YAAY,CAAC9B,IAAI;MAC5B,IAAI+B,eAAe,GAAG,QAAQ,CAAC5B,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,IAAI,CAAC;MACjE,IAAIgC,mBAAmB,GAAG,YAAY,CAAC7B,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,IAAI,CAAC;MACzElE,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,MAAM;MAEnC,QAAQgC,SAAS;QACf,KAAK,OAAO;UACV/D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAG,cAAc,CAACL,MAAM,CAACR,KAAK,EAAE,KAAK,CAAC;UAC/D;QAEF,KAAK,KAAK;QACV,KAAK,QAAQ;UACX7D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACkB,KAAK,GAAG,cAAc,CAACqB,MAAM,CAACR,KAAK,EAAE,KAAK,CAAC;UAC3D7D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAG,eAAe;UAC3C;QAEF;UACE;MACJ;MAEAyB,YAAY,CAACnG,KAAK,CAACS,OAAO,CAAC;MAC3BT,KAAK,CAACS,OAAO,GAAG2F,UAAU,CAAC,YAAY;QACrC,IAAIpG,KAAK,CAACG,GAAG,EAAE;UACbH,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,EAAE,CAACsC,MAAM,CAAC6B,mBAAmB,EAAE,GAAG,CAAC,CAAC7B,MAAM,CAAC4B,eAAe,CAAC;UACxFjG,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACkB,KAAK,GAAG,EAAE;UAC1BhD,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAG,EAAE;QAChC;MACF,CAAC,CAAC;IACJ,CAAC;IAED1E,KAAK,CAAC+F,kBAAkB,GAAG,UAAUlC,KAAK,EAAE;MAC1C,IAAIwC,YAAY,GAAGrG,KAAK,CAACD,KAAK;QAC1BgE,SAAS,GAAGsC,YAAY,CAACtC,SAAS;QAClCE,QAAQ,GAAGoC,YAAY,CAACpC,QAAQ;QAChCC,IAAI,GAAGmC,YAAY,CAACnC,IAAI;MAE5B,IAAIzE,aAAa,EAAE;QACjBmD,QAAQ,CAACC,IAAI,CAACf,KAAK,CAACgB,SAAS,GAAG,QAAQ;MAC1C;MAEA9C,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,MAAM;MACnC,IAAIuE,gBAAgB;MACpB,IAAIL,eAAe,GAAG,QAAQ,CAAC5B,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,IAAI,CAAC;MACjE,IAAIgC,mBAAmB,GAAG,YAAY,CAAC7B,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,IAAI,CAAC;MAEzE,QAAQH,SAAS;QACf,KAAK,MAAM;UACT;YACE/D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACkB,KAAK,GAAG,MAAM;YAC9BiD,eAAe,GAAG,WAAW,CAAC5B,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,CAACG,MAAM,CAACJ,QAAQ,CAAC;YAChE;UACF;QAEF,KAAK,OAAO;UACV;YACEjE,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAG,aAAa,CAACL,MAAM,CAACR,KAAK,EAAE,KAAK,CAAC;YAC9D7D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACkB,KAAK,GAAG,MAAM;YAC9BiD,eAAe,GAAG,WAAW,CAAC5B,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,CAACG,MAAM,CAACJ,QAAQ,CAAC;YAEhE,IAAIjE,KAAK,CAACM,OAAO,EAAE;cACjBN,KAAK,CAACM,OAAO,CAACwB,KAAK,CAACiB,IAAI,GAAG,GAAG,CAACsB,MAAM,CAACR,KAAK,EAAE,IAAI,CAAC;cAClD7D,KAAK,CAACM,OAAO,CAACwB,KAAK,CAACkB,KAAK,GAAG,cAAc,CAACqB,MAAM,CAACR,KAAK,EAAE,KAAK,CAAC;YACjE;YAEA;UACF;QAEF,KAAK,KAAK;QACV,KAAK,QAAQ;UACX;YACE7D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACkB,KAAK,GAAG,cAAc,CAACqB,MAAM,CAACR,KAAK,EAAE,KAAK,CAAC;YAC3D7D,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACqB,MAAM,GAAG,MAAM;YAC/BnD,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAG,eAAe;YAC3C4B,gBAAgB,GAAG,YAAY,CAACjC,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,CAACG,MAAM,CAACJ,QAAQ,CAAC;YAClE;UACF;QAEF;UACE;MACJ;MAEAkC,YAAY,CAACnG,KAAK,CAACS,OAAO,CAAC;MAC3BT,KAAK,CAACS,OAAO,GAAG2F,UAAU,CAAC,YAAY;QACrC,IAAIpG,KAAK,CAACG,GAAG,EAAE;UACbH,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACC,UAAU,GAAG,EAAE,CAACsC,MAAM,CAAC6B,mBAAmB,EAAE,GAAG,CAAC,CAAC7B,MAAM,CAACiC,gBAAgB,GAAG,EAAE,CAACjC,MAAM,CAACiC,gBAAgB,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAACjC,MAAM,CAAC4B,eAAe,CAAC;UACzJjG,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAAC4C,SAAS,GAAG,EAAE;UAC9B1E,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACkB,KAAK,GAAG,EAAE;UAC1BhD,KAAK,CAACG,GAAG,CAAC2B,KAAK,CAACqB,MAAM,GAAG,EAAE;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC;IAEDnD,KAAK,CAAC2C,oBAAoB,GAAG,YAAY;MACvC,OAAO,CAAC4D,MAAM,CAACC,IAAI,CAAC7G,aAAa,CAAC,CAAC8G,IAAI,CAAC,UAAUC,GAAG,EAAE;QACrD,OAAO/G,aAAa,CAAC+G,GAAG,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC;IAED1G,KAAK,CAAC2G,WAAW,GAAG,UAAUC,IAAI,EAAE;MAClC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBxB,YAAY,GAAGuB,IAAI,CAACvB,YAAY;MAEpC,IAAI3F,iBAAiB,EAAE;QACrB;MACF;MAEA,IAAI4F,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAC,CAAC;MAC9C,IAAIyB,MAAM,GAAGxB,SAAS,GAAGA,SAAS,CAACC,UAAU,GAAG,IAAI;MACpDvF,KAAK,CAACE,QAAQ,GAAG,EAAE;MAEnB,IAAI2G,KAAK,KAAK,KAAK,EAAE;QACnB,IAAIE,QAAQ,GAAGD,MAAM,GAAGE,KAAK,CAACC,SAAS,CAACC,KAAK,CAACjH,IAAI,CAAC6G,MAAM,CAACC,QAAQ,CAAC,GAAG,EAAE;QACxEA,QAAQ,CAAC3C,OAAO,CAAC,UAAU+C,KAAK,EAAE;UAChC,IAAIA,KAAK,CAACC,QAAQ,KAAK,QAAQ,IAAID,KAAK,CAACC,QAAQ,KAAK,OAAO,IAAID,KAAK,CAACC,QAAQ,KAAK,MAAM,IAAID,KAAK,KAAK7B,SAAS,EAAE;YACjHtF,KAAK,CAACE,QAAQ,CAACmH,IAAI,CAACF,KAAK,CAAC;UAC5B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIN,KAAK,EAAE;QAChB1H,WAAW,CAAC0H,KAAK,CAAC,CAACzC,OAAO,CAAC,UAAUsC,GAAG,EAAE;UACxC9D,QAAQ,CAAC0E,gBAAgB,CAACZ,GAAG,CAAC,CAACtC,OAAO,CAAC,UAAUyB,IAAI,EAAE;YACrD7F,KAAK,CAACE,QAAQ,CAACmH,IAAI,CAACxB,IAAI,CAAC;UAC3B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;IAED7F,KAAK,CAACqD,iCAAiC,GAAG,YAAY;MACpD,IAAIU,SAAS,GAAG/D,KAAK,CAACD,KAAK,CAACgE,SAAS;MACrC,IAAIT,YAAY,GAAGS,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO;MAChE,IAAIR,aAAa,GAAG,WAAW,CAACc,MAAM,CAACf,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC;MAChE,OAAO;QACLA,YAAY,EAAEA,YAAY;QAC1BC,aAAa,EAAEA;MACjB,CAAC;IACH,CAAC;IAEDvD,KAAK,CAACuH,KAAK,GAAG;MACZC,KAAK,EAAE/I,sBAAsB,CAACuB,KAAK;IACrC,CAAC;IACD,OAAOA,KAAK;EACd;EAEAxB,YAAY,CAACoB,WAAW,EAAE,CAAC;IACzB8G,GAAG,EAAE,mBAAmB;IACxBhD,KAAK,EAAE,SAAS+D,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAChI,iBAAiB,EAAE;QACtB,IAAIiI,gBAAgB,GAAG,KAAK;QAE5B,IAAI;UACF9C,MAAM,CAAC3F,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEqH,MAAM,CAACqB,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;YACzEC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;cAClBF,gBAAgB,GAAG,IAAI;cACvB,OAAO,IAAI;YACb;UACF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,OAAOG,GAAG,EAAE,CAAC;QAEf,IAAI,CAACpH,OAAO,GAAGiH,gBAAgB,GAAG;UAChCjH,OAAO,EAAE;QACX,CAAC,GAAG,KAAK;MACX;MAEA,IAAIqH,YAAY,GAAG,IAAI,CAAChI,KAAK;QACzBwC,IAAI,GAAGwF,YAAY,CAACxF,IAAI;QACxB8C,YAAY,GAAG0C,YAAY,CAAC1C,YAAY;QACxClB,QAAQ,GAAG4D,YAAY,CAAC5D,QAAQ;QAChC6D,SAAS,GAAGD,YAAY,CAACC,SAAS;MACtC,IAAI1C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAC,CAAC;MAC9C,IAAI,CAAC7E,QAAQ,GAAG,YAAY,CAAC6D,MAAM,CAAC4D,MAAM,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAEH,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,EAAE,CAAC,CAAC;MACxJ,IAAI,CAAC3B,WAAW,CAAC,IAAI,CAAC5G,KAAK,CAAC;MAE5B,IAAIwC,IAAI,EAAE;QACR,IAAI+C,SAAS,IAAIA,SAAS,CAACC,UAAU,KAAK3C,QAAQ,CAACC,IAAI,EAAE;UACvDlD,aAAa,CAAC,IAAI,CAACa,QAAQ,CAAC,GAAG+B,IAAI;QACrC,CAAC,CAAC;;QAGF,IAAI,CAACU,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAACwF,WAAW,CAAC,YAAY;UAC3B,IAAIT,SAAS,EAAE;YACbN,MAAM,CAAC9G,QAAQ,CAAC,CAAC;UACnB;QACF,CAAC,CAAC;QAEF,IAAIuD,QAAQ,EAAE;UACZ,IAAIuE,qBAAqB;UAEzB,CAACA,qBAAqB,GAAG,IAAI,CAAC3I,KAAK,CAAC4I,YAAY,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,IAAI,CAAC,CAAC;QACxI;MACF;IACF;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,oBAAoB;IACzBhD,KAAK,EAAE,SAASmF,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIC,YAAY,GAAG,IAAI,CAAChJ,KAAK;QACzBwC,IAAI,GAAGwG,YAAY,CAACxG,IAAI;QACxB8C,YAAY,GAAG0D,YAAY,CAAC1D,YAAY;QACxCsD,YAAY,GAAGI,YAAY,CAACJ,YAAY;QACxCxE,QAAQ,GAAG4E,YAAY,CAAC5E,QAAQ;QAChC6D,SAAS,GAAGe,YAAY,CAACf,SAAS;MACtC,IAAI1C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAC,CAAC;MAE9C,IAAI9C,IAAI,KAAKuG,SAAS,CAACvG,IAAI,EAAE;QAC3B,IAAI+C,SAAS,IAAIA,SAAS,CAACC,UAAU,KAAK3C,QAAQ,CAACC,IAAI,EAAE;UACvDlD,aAAa,CAAC,IAAI,CAACa,QAAQ,CAAC,GAAG,CAAC,CAAC+B,IAAI;QACvC;QAEA,IAAI,CAACU,mBAAmB,CAAC,CAAC;QAE1B,IAAIV,IAAI,EAAE;UACR,IAAIyF,SAAS,EAAE;YACb,IAAI,CAACpH,QAAQ,CAAC,CAAC;UACjB;UAEA,IAAIuD,QAAQ,EAAE;YACZwE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACC,IAAI,CAAC,CAAC;UACjF;QACF,CAAC,MAAM;UACLD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACK,MAAM,CAAC,CAAC;QACnF;MACF;IACF;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,sBAAsB;IAC3BhD,KAAK,EAAE,SAASuF,oBAAoBA,CAAA,EAAG;MACrC,IAAIC,YAAY,GAAG,IAAI,CAACnJ,KAAK;QACzBwC,IAAI,GAAG2G,YAAY,CAAC3G,IAAI;QACxBoG,YAAY,GAAGO,YAAY,CAACP,YAAY;MAC5C,OAAOhJ,aAAa,CAAC,IAAI,CAACa,QAAQ,CAAC;MAEnC,IAAI+B,IAAI,EAAE;QACR,IAAI,CAACqB,iBAAiB,CAAC,KAAK,CAAC;QAC7BhB,QAAQ,CAACC,IAAI,CAACf,KAAK,CAAC8D,WAAW,GAAG,EAAE;MACtC;MAEA+C,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACK,MAAM,CAAC,CAAC;IACnF;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,QAAQ;IACbhD,KAAK;IAAE;IACP,SAASyF,MAAMA,CAAA,EAAG;MAChB,IAAIC,WAAW;QACXC,MAAM,GAAG,IAAI;MAEjB,IAAIC,aAAa,GAAG,IAAI,CAACvJ,KAAK;QAC1BwJ,SAAS,GAAGD,aAAa,CAACC,SAAS;QACnCxC,QAAQ,GAAGuC,aAAa,CAACvC,QAAQ;QACjCjF,KAAK,GAAGwH,aAAa,CAACxH,KAAK;QAC3BkB,KAAK,GAAGsG,aAAa,CAACtG,KAAK;QAC3BG,MAAM,GAAGmG,aAAa,CAACnG,MAAM;QAC7BqG,WAAW,GAAGF,aAAa,CAACE,WAAW;QACvCC,KAAK,GAAGH,aAAa,CAAC/G,IAAI;QAC1BmH,SAAS,GAAGJ,aAAa,CAACI,SAAS;QACnC3F,SAAS,GAAGuF,aAAa,CAACvF,SAAS;QACnC8C,KAAK,GAAGyC,aAAa,CAACzC,KAAK;QAC3B7C,SAAS,GAAGsF,aAAa,CAACtF,SAAS;QACnCE,IAAI,GAAGoF,aAAa,CAACpF,IAAI;QACzBD,QAAQ,GAAGqF,aAAa,CAACrF,QAAQ;QACjCoB,YAAY,GAAGiE,aAAa,CAACjE,YAAY;QACzCsE,OAAO,GAAGL,aAAa,CAACK,OAAO;QAC/BhF,QAAQ,GAAG2E,aAAa,CAAC3E,QAAQ;QACjCnC,kBAAkB,GAAG8G,aAAa,CAAC9G,kBAAkB;QACrD2B,QAAQ,GAAGmF,aAAa,CAACnF,QAAQ;QACjCyF,YAAY,GAAGN,aAAa,CAACM,YAAY;QACzCC,SAAS,GAAGP,aAAa,CAACO,SAAS;QACnC1H,OAAO,GAAGmH,aAAa,CAACnH,OAAO;QAC/B2H,aAAa,GAAGR,aAAa,CAACQ,aAAa;QAC3CC,QAAQ,GAAGT,aAAa,CAACS,QAAQ;QACjCC,YAAY,GAAGV,aAAa,CAACU,YAAY;QACzCrB,YAAY,GAAGW,aAAa,CAACX,YAAY;QACzCsB,mBAAmB,GAAGX,aAAa,CAACW,mBAAmB;QACvDlK,KAAK,GAAGzB,wBAAwB,CAACgL,aAAa,EAAE1K,SAAS,CAAC,CAAC,CAAC;;MAGhE,IAAI2D,IAAI,GAAG,IAAI,CAACpC,GAAG,GAAGsJ,KAAK,GAAG,KAAK;MACnC,IAAIS,gBAAgB,GAAGpL,UAAU,CAAC4K,SAAS,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAE/K,eAAe,CAAC+K,WAAW,EAAE,EAAE,CAAC/E,MAAM,CAACqF,SAAS,EAAE,GAAG,CAAC,CAACrF,MAAM,CAACN,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE1F,eAAe,CAAC+K,WAAW,EAAE,EAAE,CAAC/E,MAAM,CAACqF,SAAS,EAAE,OAAO,CAAC,EAAEnH,IAAI,CAAC,EAAElE,eAAe,CAAC+K,WAAW,EAAEG,SAAS,IAAI,EAAE,EAAE,CAAC,CAACA,SAAS,CAAC,EAAElL,eAAe,CAAC+K,WAAW,EAAE,SAAS,EAAE,CAACjF,QAAQ,CAAC,EAAEiF,WAAW,CAAC,CAAC;MAEjV,IAAIe,sBAAsB,GAAG,IAAI,CAAC9G,iCAAiC,CAAC,CAAC;QACjEE,aAAa,GAAG4G,sBAAsB,CAAC5G,aAAa,CAAC,CAAC;MAC1D;;MAGA,IAAIkB,YAAY,GAAGV,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;MACjF,IAAIW,SAAS,GAAGnC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC8B,MAAM,CAACd,aAAa,EAAE,GAAG,CAAC,CAACc,MAAM,CAACI,YAAY,EAAE,GAAG,CAAC;MACnF,IAAI2F,eAAe,GAAGT,OAAO,IAAI,aAAa9K,KAAK,CAACwL,YAAY,CAACV,OAAO,EAAE;QACxEW,OAAO,EAAE,SAASA,OAAOA,CAACvJ,CAAC,EAAE;UAC3B,IAAI4I,OAAO,CAAC5J,KAAK,CAACuK,OAAO,EAAE;YACzBX,OAAO,CAAC5J,KAAK,CAACuK,OAAO,CAAC,CAAC;UACzB;UAEA,IAAIR,aAAa,EAAE;YACjBA,aAAa,CAAC/I,CAAC,CAAC;UAClB;QACF,CAAC;QACDwJ,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBnB,MAAM,CAAC9I,UAAU,GAAGiK,CAAC;QACvB;MACF,CAAC,CAAC;MACF,OAAO,aAAa3L,KAAK,CAAC4L,aAAa,CAAC,KAAK,EAAEtM,QAAQ,CAAC,CAAC,CAAC,EAAEc,IAAI,CAACc,KAAK,EAAE,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC,EAAE;QAC/G2K,QAAQ,EAAE,CAAC,CAAC;QACZnB,SAAS,EAAEW,gBAAgB;QAC3BpI,KAAK,EAAEA,KAAK;QACZyI,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBnB,MAAM,CAAClJ,GAAG,GAAGqK,CAAC;QAChB,CAAC;QACDxI,SAAS,EAAEO,IAAI,IAAIwH,QAAQ,GAAG,IAAI,CAAC/H,SAAS,GAAG2I,SAAS;QACxDC,eAAe,EAAE,IAAI,CAACvI;MACxB,CAAC,CAAC,EAAE8B,QAAQ,IAAI,aAAatF,KAAK,CAAC4L,aAAa,CAAC,KAAK,EAAE;QACtDlB,SAAS,EAAE,EAAE,CAAClF,MAAM,CAACqF,SAAS,EAAE,OAAO,CAAC;QACxCY,OAAO,EAAEV,YAAY,GAAGzH,OAAO,GAAGwI,SAAS;QAC3C7I,KAAK,EAAE+H,SAAS;QAChBU,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBnB,MAAM,CAAC/I,OAAO,GAAGkK,CAAC;QACpB;MACF,CAAC,CAAC,EAAE,aAAa3L,KAAK,CAAC4L,aAAa,CAAC,KAAK,EAAE;QAC1ClB,SAAS,EAAE,EAAE,CAAClF,MAAM,CAACqF,SAAS,EAAE,kBAAkB,CAAC;QACnD5H,KAAK,EAAE1D,aAAa,CAAC;UACnBsG,SAAS,EAAEA,SAAS;UACpBmG,WAAW,EAAEnG,SAAS;UACtB1B,KAAK,EAAE3D,SAAS,CAAC2D,KAAK,CAAC,GAAG,EAAE,CAACqB,MAAM,CAACrB,KAAK,EAAE,IAAI,CAAC,GAAGA,KAAK;UACxDG,MAAM,EAAE9D,SAAS,CAAC8D,MAAM,CAAC,GAAG,EAAE,CAACkB,MAAM,CAAClB,MAAM,EAAE,IAAI,CAAC,GAAGA;QACxD,CAAC,EAAE8G,mBAAmB,CAAC;QACvBM,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBnB,MAAM,CAACjJ,cAAc,GAAGoK,CAAC;QAC3B;MACF,CAAC,EAAE,aAAa3L,KAAK,CAAC4L,aAAa,CAAC,KAAK,EAAE;QACzClB,SAAS,EAAE,EAAE,CAAClF,MAAM,CAACqF,SAAS,EAAE,UAAU,CAAC;QAC3Ca,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBnB,MAAM,CAAChJ,UAAU,GAAGmK,CAAC;QACvB;MACF,CAAC,EAAEzD,QAAQ,CAAC,EAAEqD,eAAe,CAAC,CAAC;IACjC;EACF,CAAC,CAAC,EAAE,CAAC;IACH1D,GAAG,EAAE,0BAA0B;IAC/BhD,KAAK,EAAE,SAASoH,wBAAwBA,CAAC/K,KAAK,EAAEgL,KAAK,EAAE;MACrD,IAAIjC,SAAS,GAAGiC,KAAK,CAACjC,SAAS;QAC3BtB,KAAK,GAAGuD,KAAK,CAACvD,KAAK;MACvB,IAAIwD,SAAS,GAAG;QACdlC,SAAS,EAAE/I;MACb,CAAC;MAED,IAAI+I,SAAS,KAAK6B,SAAS,EAAE;QAC3B,IAAI5G,SAAS,GAAGhE,KAAK,CAACgE,SAAS;UAC3B8C,KAAK,GAAG9G,KAAK,CAAC8G,KAAK;QAEvB,IAAI9C,SAAS,KAAK+E,SAAS,CAAC/E,SAAS,EAAE;UACrC;UACAyD,KAAK,CAACnH,UAAU,GAAG,IAAI;QACzB;QAEA,IAAIwG,KAAK,KAAKiC,SAAS,CAACjC,KAAK,EAAE;UAC7BW,KAAK,CAACb,WAAW,CAAC5G,KAAK,CAAC;QAC1B;MACF;MAEA,OAAOiL,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOpL,WAAW;AACpB,CAAC,CAACf,KAAK,CAACoM,SAAS,CAAC;AAElB,eAAerL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}