export { InteractiveComputed as Interactive };
export type TimePeriod = {
    start: number;
    end: number;
};
declare const InteractiveComputed: typeof Interactive & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.Metric | import("../../index.js").Artifacts.LanternMetric>;
};
/**
 * @fileoverview Computes "Time To Interactive", the time at which the page has loaded critical
 * resources and is mostly idle.
 * @see https://docs.google.com/document/d/1yE4YWsusi5wVXrnwhR61j-QyjK9tzENIzfxrCjA1NAk/edit#heading=h.yozfsuqcgpc4
 */
declare class Interactive extends NavigationMetric {
    /**
     * Finds all time periods where the number of inflight requests is less than or equal to the
     * number of allowed concurrent requests (2).
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {{timestamps: {traceEnd: number}}} processedNavigation
     * @return {Array<TimePeriod>}
     */
    static _findNetworkQuietPeriods(networkRecords: Array<LH.Artifacts.NetworkRequest>, processedNavigation: {
        timestamps: {
            traceEnd: number;
        };
    }): Array<TimePeriod>;
    /**
     * Finds all time periods where there are no long tasks.
     * @param {Array<TimePeriod>} longTasks
     * @param {{timestamps: {timeOrigin: number, traceEnd: number}}} processedNavigation
     * @return {Array<TimePeriod>}
     */
    static _findCPUQuietPeriods(longTasks: Array<TimePeriod>, processedNavigation: {
        timestamps: {
            timeOrigin: number;
            traceEnd: number;
        };
    }): Array<TimePeriod>;
    /**
     * Finds the first time period where a network quiet period and a CPU quiet period overlap.
     * @param {Array<TimePeriod>} longTasks
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {LH.Artifacts.ProcessedNavigation} processedNavigation
     * @return {{cpuQuietPeriod: TimePeriod, networkQuietPeriod: TimePeriod, cpuQuietPeriods: Array<TimePeriod>, networkQuietPeriods: Array<TimePeriod>}}
     */
    static findOverlappingQuietPeriods(longTasks: Array<TimePeriod>, networkRecords: Array<LH.Artifacts.NetworkRequest>, processedNavigation: LH.Artifacts.ProcessedNavigation): {
        cpuQuietPeriod: TimePeriod;
        networkQuietPeriod: TimePeriod;
        cpuQuietPeriods: Array<TimePeriod>;
        networkQuietPeriods: Array<TimePeriod>;
    };
    /**
     * @param {LH.Artifacts.NavigationMetricComputationData} data
     * @return {Promise<LH.Artifacts.Metric>}
     */
    static computeObservedMetric(data: LH.Artifacts.NavigationMetricComputationData): Promise<LH.Artifacts.Metric>;
}
import { NavigationMetric } from "./navigation-metric.js";
//# sourceMappingURL=interactive.d.ts.map