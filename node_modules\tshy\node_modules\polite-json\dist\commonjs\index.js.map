{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;;;AAEH,mBAAmB;AACnB,qBAAqB;AACrB,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE,CAC3B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KACV,GAAG,CACF,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CACxE;KACA,IAAI,CAAC,EAAE,CAAC,CAAA;AAOb,MAAM,UAAU,GAAG,CAAC,CAAQ,EAAE,GAAW,EAAE,OAAe,EAAkB,EAAE;IAC5E,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO;YACL,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,6BAA6B;YAClD,QAAQ,EAAE,CAAC;SACZ,CAAA;IACH,CAAC;IACD,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;IACjE,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAA;IAEpD,mBAAmB;IACnB,qBAAqB;IACrB,MAAM,MAAM,GAAG,sDAAsD,CAAC,IAAI,CACxE,CAAC,CAAC,OAAO,CACV;QACC,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC;QAChB,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;gBACtC,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,IAAI,CAAA;IAER,MAAM,GAAG,GACP,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACf,qBAAqB,EACrB,oBAAoB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CACxD,QAAQ,CAAC,CAAC,CAAC,CACZ,GAAG,CACL;QACH,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IACf,oBAAoB;IAEpB,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,KAAK,GAAG,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,OAAO,CAAA;QAEtD,MAAM,GAAG,GAAG,MAAM,GAAG,OAAO,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,OAAO,CAAA;QAE1E,MAAM,KAAK,GACT,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACrB,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QAEnC,MAAM,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAA;QAEzC,OAAO;YACL,OAAO,EAAE,GAAG,GAAG,kBAAkB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC/D,QAAQ,EAAE,MAAM;SACjB,CAAA;IACH,CAAC;SAAM,CAAC;QACN,OAAO;YACL,OAAO,EAAE,GAAG,GAAG,mBAAmB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG;YAC9D,QAAQ,EAAE,CAAC;SACZ,CAAA;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAa,cAAe,SAAQ,WAAW;IAC7C,IAAI,CAAc;IAClB,KAAK,CAAO;IACZ,QAAQ,CAAQ;IAChB,YACE,EAAS,EACT,GAAW,EACX,UAAkB,EAAE,EACpB,MAA0C;QAE1C,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;QACf,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,IAAI,GAAG,YAAY,CAAA;QACxB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,CAAA;IAC3D,CAAC;IACD,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;IAC9B,CAAC;IACD,IAAI,IAAI,CAAC,CAAC,IAAG,CAAC;IACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;IAC9B,CAAC;CACF;AAxBD,wCAwBC;AAEY,QAAA,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;AAC9B,QAAA,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AAC7C,uEAAuE;AACvE,wEAAwE;AACxE,2EAA2E;AAC3E,sEAAsE;AACtE,MAAM,QAAQ,GAAG,gCAAgC,CAAA;AACjD,MAAM,OAAO,GAAG,8BAA8B,CAAA;AAiBvC,MAAM,KAAK,GAAG,CACnB,GAAoB,EACpB,OAAwB,EACxB,OAAgB,EACJ,EAAE;IACd,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;IACvC,IAAI,CAAC,OAAO;QAAE,OAAO,GAAG,SAAS,CAAA;IACjC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;IACvB,IAAI,CAAC;QACH,yDAAyD;QACzD,oEAAoE;QACpE,sEAAsE;QACtE,8DAA8D;QAC9D,8DAA8D;QAC9D,sEAAsE;QACtE,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;YAChE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;QAEzC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAC7C,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,MAAM,CAAC,gBAAQ,CAAC,GAAG,OAAO,CAAA;YAC1B,MAAM,CAAC,eAAO,CAAC,GAAG,MAAM,CAAA;QAC1B,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACrD,MAAM,YAAY,GAChB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAK,GAAkB,CAAC,MAAM,KAAK,CAAC,CAAA;YACxD,MAAM,MAAM,CAAC,MAAM,CACjB,IAAI,SAAS,CACX,gBAAgB,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAChE,EACD;gBACE,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,CAAC;aACf,CACF,CAAA;QACH,CAAC;QAED,MAAM,IAAI,cAAc,CAAC,CAAU,EAAE,SAAS,EAAE,OAAO,EAAE,aAAK,CAAC,CAAA;IACjE,CAAC;AACH,CAAC,CAAA;AAzCY,QAAA,KAAK,SAyCjB;AAEM,MAAM,iBAAiB,GAAG,CAAC,GAAoB,EAAE,OAAiB,EAAE,EAAE;IAC3E,IAAI,CAAC;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACnD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;AAChB,CAAC,CAAA;AAJY,QAAA,iBAAiB,qBAI7B;AAED,kEAAkE;AAClE,iEAAiE;AACjE,yCAAyC;AACzC,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;AAE7D,MAAM,SAAS,GAAG,CACvB,GAAQ,EACR,QAAmB,EACnB,MAAwB,EACxB,EAAE;IACF,MAAM,KAAK,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,eAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAC1D,0CAA0C;IAC1C,MAAM,GAAG;IACP,qBAAqB;IACrB,OAAO,QAAQ,KAAK,UAAU;QAC5B,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC;QACtC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;IAC1C,oBAAoB;IACpB,MAAM,EAAE,GAAG,GAAG,CAAC,gBAAQ,CAAC,IAAI,IAAI,CAAA;IAChC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAA;AAC1E,CAAC,CAAA;AAfY,QAAA,SAAS,aAerB", "sourcesContent": ["/**\n * Copyright 2017 <PERSON>\n * Copyright npm, Inc.\n * Copyright 2023 <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a\n * copy of this software and associated documentation files (the \"Software\"),\n * to deal in the Software without restriction, including without limitation\n * the rights to use, copy, modify, merge, publish, distribute, sublicense,\n * and/or sell copies of the Software, and to permit persons to whom the\n * Software is furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n * DEALINGS IN THE SOFTWARE.\n *\n * ---\n *\n * 'polite-json' is a fork of 'json-parse-even-better-errors',\n * extended and distributed under the terms of the <PERSON> license\n * above.\n *\n * 'json-parse-even-better-errors' is a fork of\n * 'json-parse-better-errors' by Kat Marchán, extended and\n * distributed under the terms of the MIT license above.\n */\n\n// version specific\n/* c8 ignore start */\nconst hexify = (s: string) =>\n  Array.from(s)\n    .map(\n      c => '0x' + c.charCodeAt(0).toString(16).toUpperCase().padStart(2, '0')\n    )\n    .join('')\n/* c8 ignore stop */\n\ntype ParseErrorMeta = {\n  message: string\n  position: number\n}\nconst parseError = (e: Error, txt: string, context: number): ParseErrorMeta => {\n  if (!txt) {\n    return {\n      message: e.message + ' while parsing empty string',\n      position: 0,\n    }\n  }\n  const badToken = e.message.match(/^Unexpected (?:token (.*?))?/i)\n  const atPos = e.message.match(/at positions? (\\d+)/)\n\n  // version specific\n  /* c8 ignore start */\n  const errIdx = /^Unexpected end of JSON|Unterminated string in JSON/i.test(\n    e.message\n  )\n    ? txt.length - 1\n    : atPos && atPos[1]\n    ? +atPos[1]\n    : /is not valid JSON$/.test(e.message)\n    ? 0\n    : null\n\n  const msg =\n    badToken && badToken[1]\n      ? e.message.replace(\n          /^Unexpected token ./,\n          `Unexpected token ${JSON.stringify(badToken[1])} (${hexify(\n            badToken[1]\n          )})`\n        )\n      : e.message\n  /* c8 ignore stop */\n\n  if (errIdx !== null && errIdx !== undefined) {\n    const start = errIdx <= context ? 0 : errIdx - context\n\n    const end = errIdx + context >= txt.length ? txt.length : errIdx + context\n\n    const slice =\n      (start === 0 ? '' : '...') +\n      txt.slice(start, end) +\n      (end === txt.length ? '' : '...')\n\n    const near = txt === slice ? '' : 'near '\n\n    return {\n      message: msg + ` while parsing ${near}${JSON.stringify(slice)}`,\n      position: errIdx,\n    }\n  } else {\n    return {\n      message: msg + ` while parsing '${txt.slice(0, context * 2)}'`,\n      position: 0,\n    }\n  }\n}\n\nexport class JSONParseError extends SyntaxError {\n  code: 'EJSONPARSE'\n  cause: Error\n  position: number\n  constructor(\n    er: Error,\n    txt: string,\n    context: number = 20,\n    caller?: Function | ((...a: any[]) => any)\n  ) {\n    const { message, position } = parseError(er, txt, context)\n    super(message)\n    this.cause = er\n    this.position = position\n    this.code = 'EJSONPARSE'\n    Error.captureStackTrace(this, caller || this.constructor)\n  }\n  get name() {\n    return this.constructor.name\n  }\n  set name(_) {}\n  get [Symbol.toStringTag]() {\n    return this.constructor.name\n  }\n}\n\nexport const kIndent = Symbol.for('indent')\nexport const kNewline = Symbol.for('newline')\n// only respect indentation if we got a line break, otherwise squash it\n// things other than objects and arrays aren't indented, so ignore those\n// Important: in both of these regexps, the $1 capture group is the newline\n// or undefined, and the $2 capture group is the indent, or undefined.\nconst formatRE = /^\\s*[{\\[]((?:\\r?\\n)+)([\\s\\t]*)/\nconst emptyRE = /^(?:\\{\\}|\\[\\])((?:\\r?\\n)+)?$/\n\nexport type Reviver = (this: any, key: string, value: any) => any\nexport type Replacer =\n  | ((this: any, key: string, value: any) => any)\n  | (string | number)[]\n  | null\nexport type Scalar = string | number | null\nexport type JSONResult =\n  | {\n      [k: string]: JSONResult\n      [kIndent]?: string\n      [kNewline]?: string\n    }\n  | (JSONResult[] & { [kIndent]?: string; [kNewline]?: string })\n  | Scalar\n\nexport const parse = (\n  txt: string | Buffer,\n  reviver?: Reviver | null,\n  context?: number\n): JSONResult => {\n  const parseText = stripBOM(String(txt))\n  if (!reviver) reviver = undefined\n  context = context || 20\n  try {\n    // get the indentation so that we can save it back nicely\n    // if the file starts with {\" then we have an indent of '', ie, none\n    // otherwise, pick the indentation of the next line after the first \\n\n    // If the pattern doesn't match, then it means no indentation.\n    // JSON.stringify ignores symbols, so this is reasonably safe.\n    // if the string is '{}' or '[]', then use the default 2-space indent.\n    const [, newline = '\\n', indent = '  '] = parseText.match(emptyRE) ||\n      parseText.match(formatRE) || [, '', '']\n\n    const result = JSON.parse(parseText, reviver)\n    if (result && typeof result === 'object') {\n      result[kNewline] = newline\n      result[kIndent] = indent\n    }\n    return result\n  } catch (e) {\n    if (typeof txt !== 'string' && !Buffer.isBuffer(txt)) {\n      const isEmptyArray =\n        Array.isArray(txt) && (txt as Array<any>).length === 0\n      throw Object.assign(\n        new TypeError(\n          `Cannot parse ${isEmptyArray ? 'an empty array' : String(txt)}`\n        ),\n        {\n          code: 'EJSONPARSE',\n          systemError: e,\n        }\n      )\n    }\n\n    throw new JSONParseError(e as Error, parseText, context, parse)\n  }\n}\n\nexport const parseNoExceptions = (txt: string | Buffer, reviver?: Reviver) => {\n  try {\n    return JSON.parse(stripBOM(String(txt)), reviver)\n  } catch (e) {}\n}\n\n// Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n// because the buffer-to-string conversion in `fs.readFileSync()`\n// translates it to FEFF, the UTF-16 BOM.\nconst stripBOM = (txt: string) => String(txt).replace(/^\\uFEFF/, '')\n\nexport const stringify = (\n  obj: any,\n  replacer?: Replacer,\n  indent?: string | number\n) => {\n  const space = indent === undefined ? obj[kIndent] : indent\n  // TS is so weird with parameter overloads\n  const res =\n    /* c8 ignore start */\n    typeof replacer === 'function'\n      ? JSON.stringify(obj, replacer, space)\n      : JSON.stringify(obj, replacer, space)\n  /* c8 ignore stop */\n  const nl = obj[kNewline] || '\\n'\n  return space ? (nl === '\\n' ? res : res.split('\\n').join(nl)) + nl : res\n}\n"]}