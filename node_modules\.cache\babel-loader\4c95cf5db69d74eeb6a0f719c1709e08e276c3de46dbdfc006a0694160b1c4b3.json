{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport RcDropdown from 'rc-dropdown';\nimport classNames from 'classnames';\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport DropdownButton from './dropdown-button';\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport { tuple } from '../_util/type';\nimport { cloneElement } from '../_util/reactNode';\nimport getPlacements from '../_util/placements';\nvar Placements = tuple('topLeft', 'topCenter', 'topRight', 'bottomLeft', 'bottomCenter', 'bottomRight', 'top', 'bottom');\nvar Dropdown = function Dropdown(props) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getContextPopupContainer = _React$useContext.getPopupContainer,\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var getTransitionName = function getTransitionName() {\n    var rootPrefixCls = getPrefixCls();\n    var _props$placement = props.placement,\n      placement = _props$placement === void 0 ? '' : _props$placement,\n      transitionName = props.transitionName;\n    if (transitionName !== undefined) {\n      return transitionName;\n    }\n    if (placement.indexOf('top') >= 0) {\n      return \"\".concat(rootPrefixCls, \"-slide-down\");\n    }\n    return \"\".concat(rootPrefixCls, \"-slide-up\");\n  };\n  var renderOverlay = function renderOverlay(prefixCls) {\n    // rc-dropdown already can process the function of overlay, but we have check logic here.\n    // So we need render the element to check and pass back to rc-dropdown.\n    var overlay = props.overlay;\n    var overlayNode;\n    if (typeof overlay === 'function') {\n      overlayNode = overlay();\n    } else {\n      overlayNode = overlay;\n    }\n    overlayNode = React.Children.only(typeof overlayNode === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, overlayNode) : overlayNode);\n    var overlayProps = overlayNode.props; // Warning if use other mode\n\n    devWarning(!overlayProps.mode || overlayProps.mode === 'vertical', 'Dropdown', \"mode=\\\"\".concat(overlayProps.mode, \"\\\" is not supported for Dropdown's Menu.\")); // menu cannot be selectable in dropdown defaultly\n\n    var _overlayProps$selecta = overlayProps.selectable,\n      selectable = _overlayProps$selecta === void 0 ? false : _overlayProps$selecta,\n      expandIcon = overlayProps.expandIcon;\n    var overlayNodeExpandIcon = typeof expandIcon !== 'undefined' && /*#__PURE__*/React.isValidElement(expandIcon) ? expandIcon : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-menu-submenu-arrow\")\n    }, /*#__PURE__*/React.createElement(RightOutlined, {\n      className: \"\".concat(prefixCls, \"-menu-submenu-arrow-icon\")\n    }));\n    var fixedModeOverlay = typeof overlayNode.type === 'string' ? overlayNode : cloneElement(overlayNode, {\n      mode: 'vertical',\n      selectable: selectable,\n      expandIcon: overlayNodeExpandIcon\n    });\n    return fixedModeOverlay;\n  };\n  var getPlacement = function getPlacement() {\n    var placement = props.placement;\n    if (!placement) {\n      return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';\n    }\n    if (placement.includes('Center')) {\n      var newPlacement = placement.slice(0, placement.indexOf('Center'));\n      devWarning(!placement.includes('Center'), 'Dropdown', \"You are using '\".concat(placement, \"' placement in Dropdown, which is deprecated. Try to use '\").concat(newPlacement, \"' instead.\"));\n      return newPlacement;\n    }\n    return placement;\n  };\n  var arrow = props.arrow,\n    customizePrefixCls = props.prefixCls,\n    children = props.children,\n    trigger = props.trigger,\n    disabled = props.disabled,\n    getPopupContainer = props.getPopupContainer,\n    overlayClassName = props.overlayClassName;\n  var prefixCls = getPrefixCls('dropdown', customizePrefixCls);\n  var child = React.Children.only(children);\n  var dropdownTrigger = cloneElement(child, {\n    className: classNames(\"\".concat(prefixCls, \"-trigger\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), child.props.className),\n    disabled: disabled\n  });\n  var overlayClassNameCustomized = classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n  var triggerActions = disabled ? [] : trigger;\n  var alignPoint;\n  if (triggerActions && triggerActions.indexOf('contextMenu') !== -1) {\n    alignPoint = true;\n  }\n  var builtinPlacements = getPlacements({\n    arrowPointAtCenter: _typeof(arrow) === 'object' && arrow.pointAtCenter,\n    autoAdjustOverflow: true\n  });\n  return /*#__PURE__*/React.createElement(RcDropdown, _extends({\n    alignPoint: alignPoint\n  }, props, {\n    builtinPlacements: builtinPlacements,\n    arrow: !!arrow,\n    overlayClassName: overlayClassNameCustomized,\n    prefixCls: prefixCls,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    transitionName: getTransitionName(),\n    trigger: triggerActions,\n    overlay: function overlay() {\n      return renderOverlay(prefixCls);\n    },\n    placement: getPlacement()\n  }), dropdownTrigger);\n};\nDropdown.Button = DropdownButton;\nDropdown.defaultProps = {\n  mouseEnterDelay: 0.15,\n  mouseLeaveDelay: 0.1\n};\nexport default Dropdown;", "map": {"version": 3, "names": ["_extends", "_typeof", "_defineProperty", "React", "RcDropdown", "classNames", "RightOutlined", "DropdownButton", "ConfigContext", "dev<PERSON><PERSON><PERSON>", "tuple", "cloneElement", "getPlacements", "Placements", "Dropdown", "props", "_React$useContext", "useContext", "getContextPopupContainer", "getPopupContainer", "getPrefixCls", "direction", "getTransitionName", "rootPrefixCls", "_props$placement", "placement", "transitionName", "undefined", "indexOf", "concat", "renderOverlay", "prefixCls", "overlay", "overlayNode", "Children", "only", "createElement", "overlayProps", "mode", "_overlayProps$selecta", "selectable", "expandIcon", "overlayNodeExpandIcon", "isValidElement", "className", "fixedModeOverlay", "type", "getPlacement", "includes", "newPlacement", "slice", "arrow", "customizePrefixCls", "children", "trigger", "disabled", "overlayClassName", "child", "dropdownTrigger", "overlayClassNameCustomized", "triggerActions", "alignPoint", "builtinPlacements", "arrowPointAtCenter", "pointAtCenter", "autoAdjustOverflow", "<PERSON><PERSON>", "defaultProps", "mouseEnterDelay", "mouseLeaveDelay"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/dropdown/dropdown.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport RcDropdown from 'rc-dropdown';\nimport classNames from 'classnames';\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport DropdownButton from './dropdown-button';\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport { tuple } from '../_util/type';\nimport { cloneElement } from '../_util/reactNode';\nimport getPlacements from '../_util/placements';\nvar Placements = tuple('topLeft', 'topCenter', 'topRight', 'bottomLeft', 'bottomCenter', 'bottomRight', 'top', 'bottom');\n\nvar Dropdown = function Dropdown(props) {\n  var _React$useContext = React.useContext(ConfigContext),\n      getContextPopupContainer = _React$useContext.getPopupContainer,\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var getTransitionName = function getTransitionName() {\n    var rootPrefixCls = getPrefixCls();\n    var _props$placement = props.placement,\n        placement = _props$placement === void 0 ? '' : _props$placement,\n        transitionName = props.transitionName;\n\n    if (transitionName !== undefined) {\n      return transitionName;\n    }\n\n    if (placement.indexOf('top') >= 0) {\n      return \"\".concat(rootPrefixCls, \"-slide-down\");\n    }\n\n    return \"\".concat(rootPrefixCls, \"-slide-up\");\n  };\n\n  var renderOverlay = function renderOverlay(prefixCls) {\n    // rc-dropdown already can process the function of overlay, but we have check logic here.\n    // So we need render the element to check and pass back to rc-dropdown.\n    var overlay = props.overlay;\n    var overlayNode;\n\n    if (typeof overlay === 'function') {\n      overlayNode = overlay();\n    } else {\n      overlayNode = overlay;\n    }\n\n    overlayNode = React.Children.only(typeof overlayNode === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, overlayNode) : overlayNode);\n    var overlayProps = overlayNode.props; // Warning if use other mode\n\n    devWarning(!overlayProps.mode || overlayProps.mode === 'vertical', 'Dropdown', \"mode=\\\"\".concat(overlayProps.mode, \"\\\" is not supported for Dropdown's Menu.\")); // menu cannot be selectable in dropdown defaultly\n\n    var _overlayProps$selecta = overlayProps.selectable,\n        selectable = _overlayProps$selecta === void 0 ? false : _overlayProps$selecta,\n        expandIcon = overlayProps.expandIcon;\n    var overlayNodeExpandIcon = typeof expandIcon !== 'undefined' && /*#__PURE__*/React.isValidElement(expandIcon) ? expandIcon : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-menu-submenu-arrow\")\n    }, /*#__PURE__*/React.createElement(RightOutlined, {\n      className: \"\".concat(prefixCls, \"-menu-submenu-arrow-icon\")\n    }));\n    var fixedModeOverlay = typeof overlayNode.type === 'string' ? overlayNode : cloneElement(overlayNode, {\n      mode: 'vertical',\n      selectable: selectable,\n      expandIcon: overlayNodeExpandIcon\n    });\n    return fixedModeOverlay;\n  };\n\n  var getPlacement = function getPlacement() {\n    var placement = props.placement;\n\n    if (!placement) {\n      return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';\n    }\n\n    if (placement.includes('Center')) {\n      var newPlacement = placement.slice(0, placement.indexOf('Center'));\n      devWarning(!placement.includes('Center'), 'Dropdown', \"You are using '\".concat(placement, \"' placement in Dropdown, which is deprecated. Try to use '\").concat(newPlacement, \"' instead.\"));\n      return newPlacement;\n    }\n\n    return placement;\n  };\n\n  var arrow = props.arrow,\n      customizePrefixCls = props.prefixCls,\n      children = props.children,\n      trigger = props.trigger,\n      disabled = props.disabled,\n      getPopupContainer = props.getPopupContainer,\n      overlayClassName = props.overlayClassName;\n  var prefixCls = getPrefixCls('dropdown', customizePrefixCls);\n  var child = React.Children.only(children);\n  var dropdownTrigger = cloneElement(child, {\n    className: classNames(\"\".concat(prefixCls, \"-trigger\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), child.props.className),\n    disabled: disabled\n  });\n  var overlayClassNameCustomized = classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n  var triggerActions = disabled ? [] : trigger;\n  var alignPoint;\n\n  if (triggerActions && triggerActions.indexOf('contextMenu') !== -1) {\n    alignPoint = true;\n  }\n\n  var builtinPlacements = getPlacements({\n    arrowPointAtCenter: _typeof(arrow) === 'object' && arrow.pointAtCenter,\n    autoAdjustOverflow: true\n  });\n  return /*#__PURE__*/React.createElement(RcDropdown, _extends({\n    alignPoint: alignPoint\n  }, props, {\n    builtinPlacements: builtinPlacements,\n    arrow: !!arrow,\n    overlayClassName: overlayClassNameCustomized,\n    prefixCls: prefixCls,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    transitionName: getTransitionName(),\n    trigger: triggerActions,\n    overlay: function overlay() {\n      return renderOverlay(prefixCls);\n    },\n    placement: getPlacement()\n  }), dropdownTrigger);\n};\n\nDropdown.Button = DropdownButton;\nDropdown.defaultProps = {\n  mouseEnterDelay: 0.15,\n  mouseLeaveDelay: 0.1\n};\nexport default Dropdown;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,IAAIC,UAAU,GAAGH,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC;AAExH,IAAII,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACT,aAAa,CAAC;IACnDU,wBAAwB,GAAGF,iBAAiB,CAACG,iBAAiB;IAC9DC,YAAY,GAAGJ,iBAAiB,CAACI,YAAY;IAC7CC,SAAS,GAAGL,iBAAiB,CAACK,SAAS;EAE3C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIC,aAAa,GAAGH,YAAY,CAAC,CAAC;IAClC,IAAII,gBAAgB,GAAGT,KAAK,CAACU,SAAS;MAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;MAC/DE,cAAc,GAAGX,KAAK,CAACW,cAAc;IAEzC,IAAIA,cAAc,KAAKC,SAAS,EAAE;MAChC,OAAOD,cAAc;IACvB;IAEA,IAAID,SAAS,CAACG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;MACjC,OAAO,EAAE,CAACC,MAAM,CAACN,aAAa,EAAE,aAAa,CAAC;IAChD;IAEA,OAAO,EAAE,CAACM,MAAM,CAACN,aAAa,EAAE,WAAW,CAAC;EAC9C,CAAC;EAED,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAE;IACpD;IACA;IACA,IAAIC,OAAO,GAAGjB,KAAK,CAACiB,OAAO;IAC3B,IAAIC,WAAW;IAEf,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MACjCC,WAAW,GAAGD,OAAO,CAAC,CAAC;IACzB,CAAC,MAAM;MACLC,WAAW,GAAGD,OAAO;IACvB;IAEAC,WAAW,GAAG9B,KAAK,CAAC+B,QAAQ,CAACC,IAAI,CAAC,OAAOF,WAAW,KAAK,QAAQ,GAAG,aAAa9B,KAAK,CAACiC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEH,WAAW,CAAC,GAAGA,WAAW,CAAC;IAC9I,IAAII,YAAY,GAAGJ,WAAW,CAAClB,KAAK,CAAC,CAAC;;IAEtCN,UAAU,CAAC,CAAC4B,YAAY,CAACC,IAAI,IAAID,YAAY,CAACC,IAAI,KAAK,UAAU,EAAE,UAAU,EAAE,SAAS,CAACT,MAAM,CAACQ,YAAY,CAACC,IAAI,EAAE,0CAA0C,CAAC,CAAC,CAAC,CAAC;;IAEjK,IAAIC,qBAAqB,GAAGF,YAAY,CAACG,UAAU;MAC/CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;MAC7EE,UAAU,GAAGJ,YAAY,CAACI,UAAU;IACxC,IAAIC,qBAAqB,GAAG,OAAOD,UAAU,KAAK,WAAW,IAAI,aAAatC,KAAK,CAACwC,cAAc,CAACF,UAAU,CAAC,GAAGA,UAAU,GAAG,aAAatC,KAAK,CAACiC,aAAa,CAAC,MAAM,EAAE;MACrKQ,SAAS,EAAE,EAAE,CAACf,MAAM,CAACE,SAAS,EAAE,qBAAqB;IACvD,CAAC,EAAE,aAAa5B,KAAK,CAACiC,aAAa,CAAC9B,aAAa,EAAE;MACjDsC,SAAS,EAAE,EAAE,CAACf,MAAM,CAACE,SAAS,EAAE,0BAA0B;IAC5D,CAAC,CAAC,CAAC;IACH,IAAIc,gBAAgB,GAAG,OAAOZ,WAAW,CAACa,IAAI,KAAK,QAAQ,GAAGb,WAAW,GAAGtB,YAAY,CAACsB,WAAW,EAAE;MACpGK,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAEA,UAAU;MACtBC,UAAU,EAAEC;IACd,CAAC,CAAC;IACF,OAAOG,gBAAgB;EACzB,CAAC;EAED,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAItB,SAAS,GAAGV,KAAK,CAACU,SAAS;IAE/B,IAAI,CAACA,SAAS,EAAE;MACd,OAAOJ,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,YAAY;IAC3D;IAEA,IAAII,SAAS,CAACuB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAChC,IAAIC,YAAY,GAAGxB,SAAS,CAACyB,KAAK,CAAC,CAAC,EAAEzB,SAAS,CAACG,OAAO,CAAC,QAAQ,CAAC,CAAC;MAClEnB,UAAU,CAAC,CAACgB,SAAS,CAACuB,QAAQ,CAAC,QAAQ,CAAC,EAAE,UAAU,EAAE,iBAAiB,CAACnB,MAAM,CAACJ,SAAS,EAAE,4DAA4D,CAAC,CAACI,MAAM,CAACoB,YAAY,EAAE,YAAY,CAAC,CAAC;MAC3L,OAAOA,YAAY;IACrB;IAEA,OAAOxB,SAAS;EAClB,CAAC;EAED,IAAI0B,KAAK,GAAGpC,KAAK,CAACoC,KAAK;IACnBC,kBAAkB,GAAGrC,KAAK,CAACgB,SAAS;IACpCsB,QAAQ,GAAGtC,KAAK,CAACsC,QAAQ;IACzBC,OAAO,GAAGvC,KAAK,CAACuC,OAAO;IACvBC,QAAQ,GAAGxC,KAAK,CAACwC,QAAQ;IACzBpC,iBAAiB,GAAGJ,KAAK,CAACI,iBAAiB;IAC3CqC,gBAAgB,GAAGzC,KAAK,CAACyC,gBAAgB;EAC7C,IAAIzB,SAAS,GAAGX,YAAY,CAAC,UAAU,EAAEgC,kBAAkB,CAAC;EAC5D,IAAIK,KAAK,GAAGtD,KAAK,CAAC+B,QAAQ,CAACC,IAAI,CAACkB,QAAQ,CAAC;EACzC,IAAIK,eAAe,GAAG/C,YAAY,CAAC8C,KAAK,EAAE;IACxCb,SAAS,EAAEvC,UAAU,CAAC,EAAE,CAACwB,MAAM,CAACE,SAAS,EAAE,UAAU,CAAC,EAAE7B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2B,MAAM,CAACE,SAAS,EAAE,MAAM,CAAC,EAAEV,SAAS,KAAK,KAAK,CAAC,EAAEoC,KAAK,CAAC1C,KAAK,CAAC6B,SAAS,CAAC;IACtJW,QAAQ,EAAEA;EACZ,CAAC,CAAC;EACF,IAAII,0BAA0B,GAAGtD,UAAU,CAACmD,gBAAgB,EAAEtD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2B,MAAM,CAACE,SAAS,EAAE,MAAM,CAAC,EAAEV,SAAS,KAAK,KAAK,CAAC,CAAC;EACrI,IAAIuC,cAAc,GAAGL,QAAQ,GAAG,EAAE,GAAGD,OAAO;EAC5C,IAAIO,UAAU;EAEd,IAAID,cAAc,IAAIA,cAAc,CAAChC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;IAClEiC,UAAU,GAAG,IAAI;EACnB;EAEA,IAAIC,iBAAiB,GAAGlD,aAAa,CAAC;IACpCmD,kBAAkB,EAAE9D,OAAO,CAACkD,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,CAACa,aAAa;IACtEC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,OAAO,aAAa9D,KAAK,CAACiC,aAAa,CAAChC,UAAU,EAAEJ,QAAQ,CAAC;IAC3D6D,UAAU,EAAEA;EACd,CAAC,EAAE9C,KAAK,EAAE;IACR+C,iBAAiB,EAAEA,iBAAiB;IACpCX,KAAK,EAAE,CAAC,CAACA,KAAK;IACdK,gBAAgB,EAAEG,0BAA0B;IAC5C5B,SAAS,EAAEA,SAAS;IACpBZ,iBAAiB,EAAEA,iBAAiB,IAAID,wBAAwB;IAChEQ,cAAc,EAAEJ,iBAAiB,CAAC,CAAC;IACnCgC,OAAO,EAAEM,cAAc;IACvB5B,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B,OAAOF,aAAa,CAACC,SAAS,CAAC;IACjC,CAAC;IACDN,SAAS,EAAEsB,YAAY,CAAC;EAC1B,CAAC,CAAC,EAAEW,eAAe,CAAC;AACtB,CAAC;AAED5C,QAAQ,CAACoD,MAAM,GAAG3D,cAAc;AAChCO,QAAQ,CAACqD,YAAY,GAAG;EACtBC,eAAe,EAAE,IAAI;EACrBC,eAAe,EAAE;AACnB,CAAC;AACD,eAAevD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}