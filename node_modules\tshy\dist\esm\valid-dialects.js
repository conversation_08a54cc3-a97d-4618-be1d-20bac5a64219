import fail from './fail.js';
export const isDialect = (d) => d === 'commonjs' || d === 'esm';
export default (d) => {
    if (!!d &&
        Array.isArray(d) &&
        d.length &&
        !d.some(d => !isDialect(d))) {
        return true;
    }
    fail(`tshy.dialects must be an array including "esm" and/or "commonjs", ` +
        `got: ${JSON.stringify(d)}`);
    return process.exit(1);
};
//# sourceMappingURL=valid-dialects.js.map