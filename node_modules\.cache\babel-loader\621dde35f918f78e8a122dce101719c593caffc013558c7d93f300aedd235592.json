{"ast": null, "code": "import { useContext } from 'react';\nimport { getI18n, I18nContext } from './context';\nexport function useSSR(initialI18nStore, initialLanguage) {\n  var props = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var i18nFromProps = props.i18n;\n  var _ref = useContext(I18nContext) || {},\n    i18nFromContext = _ref.i18n;\n  var i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n.options && i18n.options.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce(function (mem, lngResources) {\n      Object.keys(lngResources).forEach(function (ns) {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n}", "map": {"version": 3, "names": ["useContext", "getI18n", "I18nContext", "useSSR", "initialI18nStore", "initialLanguage", "props", "arguments", "length", "undefined", "i18nFromProps", "i18n", "_ref", "i18nFromContext", "options", "isClone", "initializedStoreOnce", "services", "resourceStore", "data", "ns", "Object", "values", "reduce", "mem", "lngResources", "keys", "for<PERSON>ach", "indexOf", "push", "isInitialized", "initializedLanguageOnce", "changeLanguage"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-i18next/dist/es/useSSR.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { getI18n, I18nContext } from './context';\nexport function useSSR(initialI18nStore, initialLanguage) {\n  var props = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var i18nFromProps = props.i18n;\n\n  var _ref = useContext(I18nContext) || {},\n      i18nFromContext = _ref.i18n;\n\n  var i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n.options && i18n.options.isClone) return;\n\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce(function (mem, lngResources) {\n      Object.keys(lngResources).forEach(function (ns) {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,OAAO,EAAEC,WAAW,QAAQ,WAAW;AAChD,OAAO,SAASC,MAAMA,CAACC,gBAAgB,EAAEC,eAAe,EAAE;EACxD,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIG,aAAa,GAAGJ,KAAK,CAACK,IAAI;EAE9B,IAAIC,IAAI,GAAGZ,UAAU,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC;IACpCW,eAAe,GAAGD,IAAI,CAACD,IAAI;EAE/B,IAAIA,IAAI,GAAGD,aAAa,IAAIG,eAAe,IAAIZ,OAAO,CAAC,CAAC;EACxD,IAAIU,IAAI,CAACG,OAAO,IAAIH,IAAI,CAACG,OAAO,CAACC,OAAO,EAAE;EAE1C,IAAIX,gBAAgB,IAAI,CAACO,IAAI,CAACK,oBAAoB,EAAE;IAClDL,IAAI,CAACM,QAAQ,CAACC,aAAa,CAACC,IAAI,GAAGf,gBAAgB;IACnDO,IAAI,CAACG,OAAO,CAACM,EAAE,GAAGC,MAAM,CAACC,MAAM,CAAClB,gBAAgB,CAAC,CAACmB,MAAM,CAAC,UAAUC,GAAG,EAAEC,YAAY,EAAE;MACpFJ,MAAM,CAACK,IAAI,CAACD,YAAY,CAAC,CAACE,OAAO,CAAC,UAAUP,EAAE,EAAE;QAC9C,IAAII,GAAG,CAACI,OAAO,CAACR,EAAE,CAAC,GAAG,CAAC,EAAEI,GAAG,CAACK,IAAI,CAACT,EAAE,CAAC;MACvC,CAAC,CAAC;MACF,OAAOI,GAAG;IACZ,CAAC,EAAEb,IAAI,CAACG,OAAO,CAACM,EAAE,CAAC;IACnBT,IAAI,CAACK,oBAAoB,GAAG,IAAI;IAChCL,IAAI,CAACmB,aAAa,GAAG,IAAI;EAC3B;EAEA,IAAIzB,eAAe,IAAI,CAACM,IAAI,CAACoB,uBAAuB,EAAE;IACpDpB,IAAI,CAACqB,cAAc,CAAC3B,eAAe,CAAC;IACpCM,IAAI,CAACoB,uBAAuB,GAAG,IAAI;EACrC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}