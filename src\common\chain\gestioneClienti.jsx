/**
* Winet e-procurement GUI
* 2020 - Viniexport.com (C)
*
* gestioneAnagrafiche - operazioni su clienti
*
*/
import React, { Component } from 'react';
import Nav from "../../components/navigation/Nav";
import AggiungiAnagrafica from '../../aggiunta_dati/aggiungiAnagrafica';
import CustomDataTable from '../../components/customDataTable';
import classNames from 'classnames/bind';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { Costanti } from '../../components/traduttore/const';
import { APIRequest } from '../../components/generalizzazioni/apireq';
import { Dialog } from 'primereact/dialog';
import { Form, Field } from 'react-final-form';
import { InputText } from 'primereact/inputtext';
import '../../css/DataTableDemo.css';

class AnagraficheChain extends Component {
    //Stato iniziale elementi tabella
    emptyResult = {
        id: null,
        customerName: '',
        address: '',
        pIva: '',
        email: '',
        isValid: '',
        createAt: '',
        updateAt: ''
    };
    constructor(props) {
        super(props);
        //Dichiarazione variabili di scena
        this.state = {
            results: null,
            resultDialog: false,
            deleteResultDialog: false,
            deleteResultsDialog: false,
            result: this.emptyResult,
            selectedResults: null,
            submitted: false,
            globalFilter: null,
            showModal: false,
            loading: true,
            resultDialog2: false
        };
        //Dichiarazione funzioni e metodi
        this.aggiungiRegistry = this.aggiungiRegistry.bind(this);
        this.hideaggiungiAnagrafica = this.hideaggiungiAnagrafica.bind(this);
        this.modificaAnagrafica = this.modificaAnagrafica.bind(this);
        this.validate = this.validate.bind(this);
        this.modifica = this.modifica.bind(this);
        this.hideDialog = this.hideDialog.bind(this);
    }
    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta
    async componentDidMount() {
        await APIRequest('GET', 'registry/')
            .then(res => {
                this.setState({
                    results: res.data,
                    loading: false,
                })
            }).catch((e) => {
                console.log(e);
                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile visualizzare le anagrafiche. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });
            })
    }
    aggiungiRegistry() {
        this.setState({
            resultDialog: true,
        });
    }
    //Chiusura dialogo aggiunta
    hideaggiungiAnagrafica() {
        this.setState({
            resultDialog: false
        });
    }
    modificaAnagrafica(result) {
        this.setState({
            result,
            resultDialog2: true,
        });
    }
    hideDialog() {
        this.setState({
            resultDialog2: false
        });
    }
    validate = (data) => {
        let errors = {};

        if (!data.firstName) {
            errors.firstName = Costanti.NomeObb;
        }

        // Rileva se è un'azienda basandosi su P.IVA O nome aziendale
        const hasCompanyPIva = data.pIva && /^\d{11}$/.test(data.pIva.replace(/\s/g, ''));
        const hasCompanyName = data.firstName && (
            data.firstName.toUpperCase().includes('SRL') ||
            data.firstName.toUpperCase().includes('S.R.L.') ||
            data.firstName.toUpperCase().includes('SPA') ||
            data.firstName.toUpperCase().includes('S.P.A.') ||
            data.firstName.toUpperCase().includes('SNCA') ||
            data.firstName.toUpperCase().includes('SAS') ||
            data.firstName.toUpperCase().includes('SNC') ||
            data.firstName.toUpperCase().includes('SOCIETÀ') ||
            data.firstName.toUpperCase().includes('COMPANY') ||
            data.firstName.toUpperCase().includes('SERVICES') ||
            data.firstName.toUpperCase().includes('TRADING') ||
            data.firstName.toUpperCase().includes('BROKER') ||
            data.firstName.toUpperCase().includes('GROUP') ||
            data.firstName.toUpperCase().includes('HOLDING') ||
            data.firstName.toUpperCase().includes('CORPORATION') ||
            data.firstName.toUpperCase().includes('CORP') ||
            data.firstName.toUpperCase().includes('LTD') ||
            data.firstName.toUpperCase().includes('LIMITED') ||
            data.firstName.toUpperCase().includes('INC') ||
            data.firstName.length > 30 // Nome molto lungo probabilmente è ragione sociale
        );

        // È un'azienda se ha P.IVA di 11 cifre O nome che sembra aziendale
        const isCompany = hasCompanyPIva || hasCompanyName;

        // Cognome obbligatorio solo se non è un'azienda
        if (!isCompany && !data.lastName) {
            errors.lastName = Costanti.CognObb;
        }

        // Email opzionale ma se inserita deve essere valida
        if (data.email && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(data.email)) {
            errors.email = Costanti.EmailNoVal;
        }

        if (!data.telnum) {
            errors.telnum = Costanti.TelObb;
        }

        if (!data.cellnum) {
            errors.cellnum = Costanti.CelObb;
        }

        if (!data.pIva) {
            errors.pIva = Costanti.pIvaObb;
        }

        if (!data.address) {
            errors.address = Costanti.IndObb;
        }

        if (!data.city) {
            errors.city = Costanti.CityObb;
        }

        if (!data.cap) {
            errors.cap = Costanti.CapObb;
        }

        if (!data.paymentMetod) {
            errors.paymentMetod = Costanti.paymentMetodObb;
        }

        return errors;
    }
    async modifica(data, form) {
        var body = {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            tel: data.cellnum + '/' + data.telnum,
            pIva: data.pIva,
            address: data.address,
            city: data.city,
            cap: data.cap,
            paymentMetod: data.paymentMetod
        }
        var url = 'registry/?idRegistry=' + this.state.result.id
        await APIRequest('PUT', url, body)
            .then(async res => {
                console.log(res.data);
                this.toast.show({ severity: 'success', summary: 'Ottimo', detail: "Anagrafica modificata con successo", life: 3000 });
                setTimeout(() => {
                    window.location.reload()
                }, 3000)
            }).catch((e) => {
                console.error('❌ Errore modifica anagrafica:', e);

                // Gestione dettagliata degli errori per troubleshooting
                const errorStatus = e.response?.status;
                const errorData = e.response?.data;
                const errorMessage = errorData?.message || errorData?.error || e.message;

                let toastConfig = {
                    severity: 'error',
                    life: 8000
                };

                // Gestione specifica per codice di stato
                switch (errorStatus) {
                    case 400:
                        toastConfig.summary = '❌ Dati Non Validi';
                        if (errorData?.field) {
                            toastConfig.detail = `Il campo "${errorData.field}" non è valido: ${errorMessage}`;
                        } else if (errorMessage.toLowerCase().includes('email')) {
                            toastConfig.detail = 'Formato email non valido. Verificare l\'indirizzo inserito.';
                        } else if (errorMessage.toLowerCase().includes('piva') || errorMessage.toLowerCase().includes('partita')) {
                            toastConfig.detail = 'Partita IVA non valida. Deve contenere esattamente 11 cifre numeriche.';
                        } else if (errorMessage.toLowerCase().includes('phone') || errorMessage.toLowerCase().includes('tel')) {
                            toastConfig.detail = 'Numero di telefono non valido. Verificare il formato inserito.';
                        } else {
                            toastConfig.detail = `Dati inseriti non validi: ${errorMessage}`;
                        }
                        break;

                    case 401:
                        toastConfig.summary = '🔐 Sessione Scaduta';
                        toastConfig.detail = 'La sessione è scaduta. Ricaricare la pagina per effettuare nuovamente il login.';
                        toastConfig.life = 10000;
                        break;

                    case 403:
                        toastConfig.summary = '🚫 Accesso Negato';
                        toastConfig.detail = 'Non si dispone dei permessi necessari per modificare questa anagrafica.';
                        break;

                    case 404:
                        toastConfig.summary = '🔍 Anagrafica Non Trovata';
                        toastConfig.detail = 'L\'anagrafica che si sta tentando di modificare non esiste più. Ricaricare la pagina.';
                        break;

                    case 409:
                        toastConfig.summary = '⚠️ Conflitto Dati';
                        if (errorMessage.toLowerCase().includes('email')) {
                            toastConfig.detail = 'Questa email è già utilizzata da un\'altra anagrafica. Inserire un indirizzo diverso.';
                        } else if (errorMessage.toLowerCase().includes('piva') || errorMessage.toLowerCase().includes('partita')) {
                            toastConfig.detail = 'Questa Partita IVA è già registrata nel sistema. Verificare il numero inserito.';
                        } else {
                            toastConfig.detail = `Conflitto nei dati: ${errorMessage}`;
                        }
                        break;

                    case 422:
                        toastConfig.summary = '📝 Dati Incompleti';
                        toastConfig.detail = `Alcuni campi obbligatori sono mancanti o non validi: ${errorMessage}`;
                        break;

                    case 500:
                        toastConfig.summary = '💥 Errore del Server';
                        toastConfig.detail = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\'assistenza.';
                        console.error('Server Error Details:', errorData);
                        break;

                    case 503:
                        toastConfig.summary = '⏱️ Servizio Non Disponibile';
                        toastConfig.detail = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';
                        break;

                    default:
                        if (e.code === 'NETWORK_ERROR' || e.message.includes('Network Error')) {
                            toastConfig.summary = '🌐 Errore di Connessione';
                            toastConfig.detail = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';
                        } else if (e.code === 'TIMEOUT_ERROR' || e.message.includes('timeout')) {
                            toastConfig.summary = '⏱️ Timeout';
                            toastConfig.detail = 'La richiesta ha impiegato troppo tempo. Verificare la connessione e riprovare.';
                        } else {
                            toastConfig.summary = '❓ Errore Sconosciuto';
                            toastConfig.detail = `Errore imprevisto (${errorStatus || 'N/A'}): ${errorMessage}`;
                        }
                        break;
                }

                // Log dettagliato per debugging
                console.error('🔍 Dettagli errore modifica:', {
                    status: errorStatus,
                    data: errorData,
                    message: errorMessage,
                    fullError: e,
                    requestBody: body,
                    url: url
                });

                this.toast.show(toastConfig);
            })
    }
    render() {
        const isFormFieldValid = (meta) => !!(meta.touched && meta.error);
        const getFormErrorMessage = (meta) => {
            return isFormFieldValid(meta) && <small className="p-error">{meta.error}</small>;
        };
        //Elementi del footer nelle finestre di dialogo dell'aggiunta 
        const resultDialogFooter = (
            <React.Fragment>
                <Button className="p-button-text" onClick={this.hideaggiungiAnagrafica} > {Costanti.Chiudi} </Button>
            </React.Fragment>
        );
        //Elementi del footer nelle finestre di dialogo della modifica
        const resultDialogFooter2 = (
            <React.Fragment>
                <Button className="p-button-text" onClick={this.hideDialog} > {Costanti.Chiudi} </Button>
            </React.Fragment>
        );
        const fields = [
            { field: 'id', header: 'ID', body: 'id', sortable: true, showHeader: true },
            { field: 'firstName', header: Costanti.rSociale, body: 'firstName', sortable: true, showHeader: true },
            { field: 'address', header: Costanti.Indirizzo, body: 'address', sortable: true, showHeader: true },
            { field: 'city', header: Costanti.Città, body: 'city', sortable: true, showHeader: true },
            { field: 'cap', header: Costanti.CodPost, body: 'cap', sortable: true, showHeader: true },
            { field: 'pIva', header: Costanti.pIva, body: 'pIva', sortable: true, showHeader: true },
            { field: 'tel', header: Costanti.Tel, body: 'tel', sortable: true, showHeader: true },
            { field: 'email', header: Costanti.Email, body: 'email', sortable: true, showHeader: true },
            { field: 'createdAt', header: Costanti.dInserimento, body: 'createdAt', sortable: true, showHeader: true },
            { field: 'updateAt', header: Costanti.dAggiornamento, body: 'updateAt', sortable: true, showHeader: true }
        ];
        const actionFields = [
            { name: Costanti.Modifica, icon: <i className="pi pi-pencil" />, handler: this.modificaAnagrafica }
        ];
        const items = [
            {
                label: Costanti.AggAnag,
                icon: 'pi pi-plus-circle',
                command: () => {
                    this.aggiungiRegistry()
                }
            },
        ]
        return (
            <div className="datatable-responsive-demo wrapper">
                {/* Il componente Toast permette di creare e visualizzare messaggi */}
                <Toast ref={(el) => this.toast = el} />
                {/* Il componente Nav contiene l'header ed il menù di navigazione */}
                <Nav />
                <div className="col-12 px-0 solid-head">
                    <h1>{Costanti.generali}</h1>
                </div>
                <div className="card">
                    {/* Componente primereact per la creazione della tabella */}
                    <CustomDataTable
                        ref={(el) => this.dt = el}
                        value={this.state.results}
                        fields={fields}
                        loading={this.state.loading}
                        dataKey="id"
                        paginator
                        rows={20}
                        rowsPerPageOptions={[10, 20, 50]}
                        autoLayout={true}
                        actionsColumn={actionFields}
                        splitButtonClass={true}
                        items={items}
                        fileNames="Anagrafiche"
                    />
                </div>
                {/* Struttura dialogo per l'aggiunta */}
                <Dialog visible={this.state.resultDialog} header={Costanti.AggAnag} modal className="p-fluid modalBox" footer={resultDialogFooter} onHide={this.hideaggiungiAnagrafica}>
                    <AggiungiAnagrafica />
                </Dialog>
                {/* Struttura dialogo per la modifica */}
                <Dialog visible={this.state.resultDialog2} style={{ width: '800px' }} header={Costanti.Modifica} modal className="p-fluid" footer={resultDialogFooter2} onHide={this.hideDialog}>
                    <div className="modalBody">
                        <Form onSubmit={this.modifica} initialValues={{ firstName: this.state.result.firstName, lastName: this.state.result.lastName, email: this.state.result.email, telnum: this.state.result.tel?.split('/')[1], cellnum: this.state.result.tel?.split('/')[0], pIva: this.state.result.pIva, address: this.state.result.address, city: this.state.result.city, cap: this.state.result.cap, paymentMetod: this.state.result.paymentMetod }} validate={this.validate} render={({ handleSubmit, form, values }) => (
                            <form onSubmit={handleSubmit} className="p-fluid">
                                <div className='row'>
                                    <Field name="firstName" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label p-input-icon-right">
                                                <i className="pi pi-envelope" />
                                                <InputText id="firstName" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="firstName" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Nome}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="lastName" render={({ input, meta }) => {
                                        // Rileva se è un'azienda basandosi sui valori del form
                                        const hasCompanyPIva = values.pIva && /^\d{11}$/.test(values.pIva.replace(/\s/g, ''));
                                        const hasCompanyName = values.firstName && (
                                            values.firstName.toUpperCase().includes('SRL') ||
                                            values.firstName.toUpperCase().includes('S.R.L.') ||
                                            values.firstName.toUpperCase().includes('SPA') ||
                                            values.firstName.toUpperCase().includes('S.P.A.') ||
                                            values.firstName.toUpperCase().includes('SNCA') ||
                                            values.firstName.toUpperCase().includes('SAS') ||
                                            values.firstName.toUpperCase().includes('SNC') ||
                                            values.firstName.toUpperCase().includes('SOCIETÀ') ||
                                            values.firstName.toUpperCase().includes('COMPANY') ||
                                            values.firstName.toUpperCase().includes('SERVICES') ||
                                            values.firstName.toUpperCase().includes('TRADING') ||
                                            values.firstName.toUpperCase().includes('BROKER') ||
                                            values.firstName.toUpperCase().includes('GROUP') ||
                                            values.firstName.toUpperCase().includes('HOLDING') ||
                                            values.firstName.toUpperCase().includes('CORPORATION') ||
                                            values.firstName.toUpperCase().includes('CORP') ||
                                            values.firstName.toUpperCase().includes('LTD') ||
                                            values.firstName.toUpperCase().includes('LIMITED') ||
                                            values.firstName.toUpperCase().includes('INC') ||
                                            values.firstName.length > 30
                                        );

                                        const isCompany = hasCompanyPIva || hasCompanyName;

                                        return (
                                            <div className="p-field col-12 col-sm-6">
                                                <span className="p-float-label">
                                                    <InputText
                                                        id="lastName"
                                                        {...input}
                                                        keyfilter={/^[^#<>*!]+$/}
                                                        className={classNames({ 'p-invalid': !isCompany && isFormFieldValid(meta) })}
                                                    />
                                                    <label htmlFor="lastName" className={classNames({ 'p-error': !isCompany && isFormFieldValid(meta) })}>
                                                        {Costanti.Cognome}
                                                        {!isCompany && '*'}
                                                        {isCompany && <span style={{color: '#6c757d', fontSize: '12px'}}> (opzionale)</span>}
                                                    </label>
                                                </span>
                                                {!isCompany && getFormErrorMessage(meta)}
                                                {isCompany && (
                                                    <small className="p-text-secondary">
                                                        <i className="pi pi-building"></i> Campo opzionale per aziende
                                                    </small>
                                                )}
                                            </div>
                                        );
                                    }} />
                                    <Field name="email" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="email" {...input} type="email" keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="email" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Email}</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="telnum" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText type="tel" id="telnum" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="telnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Tel}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="cellnum" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText type="tel" id="cellnum" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="cellnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Cell}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="pIva" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="pIva" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="pIva" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.pIva}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="address" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="address" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="address" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Indirizzo}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="city" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="city" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="city" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Città}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="cap" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="cap" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="cap" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.CodPost}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="paymentMetod" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="paymentMetod" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="paymentMetod" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Pagamento}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                </div>
                                <div className="buttonForm">
                                    {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}
                                    <Button type="submit" id="user" > {Costanti.salva} </Button>
                                </div>
                            </form>
                        )} />
                    </div>
                </Dialog>
            </div>
        );
    }
}

export default AnagraficheChain;
