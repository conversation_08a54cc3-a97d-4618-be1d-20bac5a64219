/**
* Winet e-procurement GUI
* 2020 - Viniexport.com (C)
*
* gestioneAnagrafiche - operazioni su clienti
*
*/
import React, { Component } from 'react';
import Nav from "../../components/navigation/Nav";
import AggiungiAnagrafica from '../../aggiunta_dati/aggiungiAnagrafica';
import CustomDataTable from '../../components/customDataTable';
import classNames from 'classnames/bind';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { Costanti } from '../../components/traduttore/const';
import { APIRequest } from '../../components/generalizzazioni/apireq';
import { Dialog } from 'primereact/dialog';
import { Form, Field } from 'react-final-form';
import { InputText } from 'primereact/inputtext';
import '../../css/DataTableDemo.css';

class AnagraficheChain extends Component {
    //Stato iniziale elementi tabella
    emptyResult = {
        id: null,
        customerName: '',
        address: '',
        pIva: '',
        email: '',
        isValid: '',
        createAt: '',
        updateAt: ''
    };
    constructor(props) {
        super(props);
        //Dichiarazione variabili di scena
        this.state = {
            results: null,
            resultDialog: false,
            deleteResultDialog: false,
            deleteResultsDialog: false,
            result: this.emptyResult,
            selectedResults: null,
            submitted: false,
            globalFilter: null,
            showModal: false,
            loading: true,
            resultDialog2: false
        };
        //Dichiarazione funzioni e metodi
        this.aggiungiRegistry = this.aggiungiRegistry.bind(this);
        this.hideaggiungiAnagrafica = this.hideaggiungiAnagrafica.bind(this);
        this.modificaAnagrafica = this.modificaAnagrafica.bind(this);
        this.validate = this.validate.bind(this);
        this.modifica = this.modifica.bind(this);
        this.hideDialog = this.hideDialog.bind(this);
    }
    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta
    async componentDidMount() {
        await APIRequest('GET', 'registry/')
            .then(res => {
                this.setState({
                    results: res.data,
                    loading: false,
                })
            }).catch((e) => {
                console.log(e);
                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile visualizzare le anagrafiche. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });
            })
    }
    aggiungiRegistry() {
        this.setState({
            resultDialog: true,
        });
    }
    //Chiusura dialogo aggiunta
    hideaggiungiAnagrafica() {
        this.setState({
            resultDialog: false
        });
    }
    modificaAnagrafica(result) {
        this.setState({
            result,
            resultDialog2: true,
        });
    }
    hideDialog() {
        this.setState({
            resultDialog2: false
        });
    }
    validate = (data) => {
        let errors = {};

        if (!data.firstName) {
            errors.firstName = Costanti.NomeObb;
        }

        // Rileva se è un'azienda basandosi su P.IVA e nome
        const isCompany = data.pIva && /^\d{11}$/.test(data.pIva.replace(/\s/g, '')) &&
                         data.firstName && (
                             data.firstName.toUpperCase().includes('SRL') ||
                             data.firstName.toUpperCase().includes('SPA') ||
                             data.firstName.toUpperCase().includes('SNCA') ||
                             data.firstName.toUpperCase().includes('SAS') ||
                             data.firstName.toUpperCase().includes('SNC') ||
                             data.firstName.toUpperCase().includes('SOCIETÀ') ||
                             data.firstName.toUpperCase().includes('COMPANY') ||
                             data.firstName.toUpperCase().includes('SERVICES') ||
                             data.firstName.length > 30 // Nome molto lungo probabilmente è ragione sociale
                         );

        // Cognome obbligatorio solo se non è un'azienda
        if (!isCompany && !data.lastName) {
            errors.lastName = Costanti.CognObb;
        }

        if (!data.email) {
            errors.email = Costanti.EmailObb;
        }
        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(data.email)) {
            errors.email = Costanti.EmailNoVal;
        }

        if (!data.telnum) {
            errors.telnum = Costanti.TelObb;
        }

        if (!data.cellnum) {
            errors.cellnum = Costanti.CelObb;
        }

        if (!data.pIva) {
            errors.pIva = Costanti.pIvaObb;
        }

        if (!data.address) {
            errors.address = Costanti.IndObb;
        }

        if (!data.city) {
            errors.city = Costanti.CityObb;
        }

        if (!data.cap) {
            errors.cap = Costanti.CapObb;
        }

        if (!data.paymentMetod) {
            errors.paymentMetod = Costanti.paymentMetodObb;
        }

        return errors;
    }
    async modifica(data, form) {
        var body = {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            tel: data.cellnum + '/' + data.telnum,
            pIva: data.pIva,
            address: data.address,
            city: data.city,
            cap: data.cap,
            paymentMetod: data.paymentMetod
        }
        var url = 'registry/?idRegistry=' + this.state.result.id
        await APIRequest('PUT', url, body)
            .then(async res => {
                console.log(res.data);
                this.toast.show({ severity: 'success', summary: 'Ottimo', detail: "Anagrafica modificata con successo", life: 3000 });
                setTimeout(() => {
                    window.location.reload()
                }, 3000)
            }).catch((e) => {
                console.log(e)
                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile modificare l'anagrafica. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });
            })
    }
    render() {
        const isFormFieldValid = (meta) => !!(meta.touched && meta.error);
        const getFormErrorMessage = (meta) => {
            return isFormFieldValid(meta) && <small className="p-error">{meta.error}</small>;
        };
        //Elementi del footer nelle finestre di dialogo dell'aggiunta 
        const resultDialogFooter = (
            <React.Fragment>
                <Button className="p-button-text" onClick={this.hideaggiungiAnagrafica} > {Costanti.Chiudi} </Button>
            </React.Fragment>
        );
        //Elementi del footer nelle finestre di dialogo della modifica
        const resultDialogFooter2 = (
            <React.Fragment>
                <Button className="p-button-text" onClick={this.hideDialog} > {Costanti.Chiudi} </Button>
            </React.Fragment>
        );
        const fields = [
            { field: 'id', header: 'ID', body: 'id', sortable: true, showHeader: true },
            { field: 'firstName', header: Costanti.rSociale, body: 'firstName', sortable: true, showHeader: true },
            { field: 'address', header: Costanti.Indirizzo, body: 'address', sortable: true, showHeader: true },
            { field: 'city', header: Costanti.Città, body: 'city', sortable: true, showHeader: true },
            { field: 'cap', header: Costanti.CodPost, body: 'cap', sortable: true, showHeader: true },
            { field: 'pIva', header: Costanti.pIva, body: 'pIva', sortable: true, showHeader: true },
            { field: 'tel', header: Costanti.Tel, body: 'tel', sortable: true, showHeader: true },
            { field: 'email', header: Costanti.Email, body: 'email', sortable: true, showHeader: true },
            { field: 'createdAt', header: Costanti.dInserimento, body: 'createdAt', sortable: true, showHeader: true },
            { field: 'updateAt', header: Costanti.dAggiornamento, body: 'updateAt', sortable: true, showHeader: true }
        ];
        const actionFields = [
            { name: Costanti.Modifica, icon: <i className="pi pi-pencil" />, handler: this.modificaAnagrafica }
        ];
        const items = [
            {
                label: Costanti.AggAnag,
                icon: 'pi pi-plus-circle',
                command: () => {
                    this.aggiungiRegistry()
                }
            },
        ]
        return (
            <div className="datatable-responsive-demo wrapper">
                {/* Il componente Toast permette di creare e visualizzare messaggi */}
                <Toast ref={(el) => this.toast = el} />
                {/* Il componente Nav contiene l'header ed il menù di navigazione */}
                <Nav />
                <div className="col-12 px-0 solid-head">
                    <h1>{Costanti.generali}</h1>
                </div>
                <div className="card">
                    {/* Componente primereact per la creazione della tabella */}
                    <CustomDataTable
                        ref={(el) => this.dt = el}
                        value={this.state.results}
                        fields={fields}
                        loading={this.state.loading}
                        dataKey="id"
                        paginator
                        rows={20}
                        rowsPerPageOptions={[10, 20, 50]}
                        autoLayout={true}
                        actionsColumn={actionFields}
                        splitButtonClass={true}
                        items={items}
                        fileNames="Anagrafiche"
                    />
                </div>
                {/* Struttura dialogo per l'aggiunta */}
                <Dialog visible={this.state.resultDialog} header={Costanti.AggAnag} modal className="p-fluid modalBox" footer={resultDialogFooter} onHide={this.hideaggiungiAnagrafica}>
                    <AggiungiAnagrafica />
                </Dialog>
                {/* Struttura dialogo per la modifica */}
                <Dialog visible={this.state.resultDialog2} style={{ width: '800px' }} header={Costanti.Modifica} modal className="p-fluid" footer={resultDialogFooter2} onHide={this.hideDialog}>
                    <div className="modalBody">
                        <Form onSubmit={this.modifica} initialValues={{ firstName: this.state.result.firstName, lastName: this.state.result.lastName, email: this.state.result.email, telnum: this.state.result.tel?.split('/')[1], cellnum: this.state.result.tel?.split('/')[0], pIva: this.state.result.pIva, address: this.state.result.address, city: this.state.result.city, cap: this.state.result.cap, paymentMetod: this.state.result.paymentMetod }} validate={this.validate} render={({ handleSubmit }) => (
                            <form onSubmit={handleSubmit} className="p-fluid">
                                <div className='row'>
                                    <Field name="firstName" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label p-input-icon-right">
                                                <i className="pi pi-envelope" />
                                                <InputText id="firstName" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="firstName" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Nome}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="lastName" render={({ input, meta, form }) => {
                                        // Rileva se è un'azienda basandosi sui valori del form
                                        const formValues = form.getState().values;
                                        const isCompany = formValues.pIva && /^\d{11}$/.test(formValues.pIva.replace(/\s/g, '')) &&
                                                         formValues.firstName && (
                                                             formValues.firstName.toUpperCase().includes('SRL') ||
                                                             formValues.firstName.toUpperCase().includes('SPA') ||
                                                             formValues.firstName.toUpperCase().includes('SNCA') ||
                                                             formValues.firstName.toUpperCase().includes('SAS') ||
                                                             formValues.firstName.toUpperCase().includes('SNC') ||
                                                             formValues.firstName.toUpperCase().includes('SOCIETÀ') ||
                                                             formValues.firstName.toUpperCase().includes('COMPANY') ||
                                                             formValues.firstName.toUpperCase().includes('SERVICES') ||
                                                             formValues.firstName.length > 30
                                                         );

                                        return (
                                            <div className="p-field col-12 col-sm-6">
                                                <span className="p-float-label">
                                                    <InputText
                                                        id="lastName"
                                                        {...input}
                                                        keyfilter={/^[^#<>*!]+$/}
                                                        className={classNames({ 'p-invalid': !isCompany && isFormFieldValid(meta) })}
                                                        placeholder={isCompany ? "Opzionale per aziende" : ""}
                                                    />
                                                    <label htmlFor="lastName" className={classNames({ 'p-error': !isCompany && isFormFieldValid(meta) })}>
                                                        {Costanti.Cognome}
                                                        {!isCompany && '*'}
                                                        {isCompany && <span style={{color: '#6c757d', fontSize: '12px'}}> (opzionale)</span>}
                                                    </label>
                                                </span>
                                                {!isCompany && getFormErrorMessage(meta)}
                                                {isCompany && (
                                                    <small className="p-text-secondary">
                                                        <i className="pi pi-building"></i> Campo opzionale per aziende
                                                    </small>
                                                )}
                                            </div>
                                        );
                                    }} />
                                    <Field name="email" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="email" {...input} type="email" keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="email" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Email}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="telnum" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText type="tel" id="telnum" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="telnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Tel}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="cellnum" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText type="tel" id="cellnum" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="cellnum" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Cell}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="pIva" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="pIva" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="pIva" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.pIva}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="address" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="address" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="address" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Indirizzo}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="city" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="city" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="city" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Città}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="cap" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="cap" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="cap" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.CodPost}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                    <Field name="paymentMetod" render={({ input, meta }) => (
                                        <div className="p-field col-12 col-sm-6">
                                            <span className="p-float-label">
                                                <InputText id="paymentMetod" {...input} keyfilter={/^[^#<>*!]+$/} className={classNames({ 'p-invalid': isFormFieldValid(meta) })} />
                                                <label htmlFor="paymentMetod" className={classNames({ 'p-error': isFormFieldValid(meta) })}>{Costanti.Pagamento}*</label>
                                            </span>
                                            {getFormErrorMessage(meta)}
                                        </div>
                                    )} />
                                </div>
                                <div className="buttonForm">
                                    {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}
                                    <Button type="submit" id="user" > {Costanti.salva} </Button>
                                </div>
                            </form>
                        )} />
                    </div>
                </Dialog>
            </div>
        );
    }
}

export default AnagraficheChain;
