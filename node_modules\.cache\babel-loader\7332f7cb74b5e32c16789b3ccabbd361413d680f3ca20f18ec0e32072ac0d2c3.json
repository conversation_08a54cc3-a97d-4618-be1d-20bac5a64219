{"ast": null, "code": "import InternalInput from './Input';\nimport Group from './Group';\nimport Search from './Search';\nimport TextArea from './TextArea';\nimport Password from './Password';\nvar Input = InternalInput;\nInput.Group = Group;\nInput.Search = Search;\nInput.TextArea = TextArea;\nInput.Password = Password;\nexport default Input;", "map": {"version": 3, "names": ["InternalInput", "Group", "Search", "TextArea", "Password", "Input"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/input/index.js"], "sourcesContent": ["import InternalInput from './Input';\nimport Group from './Group';\nimport Search from './Search';\nimport TextArea from './TextArea';\nimport Password from './Password';\nvar Input = InternalInput;\nInput.Group = Group;\nInput.Search = Search;\nInput.TextArea = TextArea;\nInput.Password = Password;\nexport default Input;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,IAAIC,KAAK,GAAGL,aAAa;AACzBK,KAAK,CAACJ,KAAK,GAAGA,KAAK;AACnBI,KAAK,CAACH,MAAM,GAAGA,MAAM;AACrBG,KAAK,CAACF,QAAQ,GAAGA,QAAQ;AACzBE,KAAK,CAACD,QAAQ,GAAGA,QAAQ;AACzB,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}