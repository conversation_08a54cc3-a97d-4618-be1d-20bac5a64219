{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _empty = _interopRequireDefault(require(\"../empty\"));\nvar _ = require(\".\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar renderEmpty = function renderEmpty(componentName) {\n  return /*#__PURE__*/React.createElement(_.ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefix = getPrefixCls('empty');\n    switch (componentName) {\n      case 'Table':\n      case 'List':\n        return /*#__PURE__*/React.createElement(_empty[\"default\"], {\n          image: _empty[\"default\"].PRESENTED_IMAGE_SIMPLE\n        });\n      case 'Select':\n      case 'TreeSelect':\n      case 'Cascader':\n      case 'Transfer':\n      case 'Mentions':\n        return /*#__PURE__*/React.createElement(_empty[\"default\"], {\n          image: _empty[\"default\"].PRESENTED_IMAGE_SIMPLE,\n          className: \"\".concat(prefix, \"-small\")\n        });\n      default:\n        return /*#__PURE__*/React.createElement(_empty[\"default\"], null);\n    }\n  });\n};\nvar _default = renderEmpty;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "React", "_interopRequireWildcard", "_empty", "_", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "renderEmpty", "componentName", "createElement", "ConfigConsumer", "_ref", "getPrefixCls", "prefix", "image", "PRESENTED_IMAGE_SIMPLE", "className", "concat", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/config-provider/renderEmpty.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _empty = _interopRequireDefault(require(\"../empty\"));\n\nvar _ = require(\".\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar renderEmpty = function renderEmpty(componentName) {\n  return /*#__PURE__*/React.createElement(_.ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefix = getPrefixCls('empty');\n\n    switch (componentName) {\n      case 'Table':\n      case 'List':\n        return /*#__PURE__*/React.createElement(_empty[\"default\"], {\n          image: _empty[\"default\"].PRESENTED_IMAGE_SIMPLE\n        });\n\n      case 'Select':\n      case 'TreeSelect':\n      case 'Cascader':\n      case 'Transfer':\n      case 'Mentions':\n        return /*#__PURE__*/React.createElement(_empty[\"default\"], {\n          image: _empty[\"default\"].PRESENTED_IMAGE_SIMPLE,\n          className: \"\".concat(prefix, \"-small\")\n        });\n\n      default:\n        return /*#__PURE__*/React.createElement(_empty[\"default\"], null);\n    }\n  });\n};\n\nvar _default = renderEmpty;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,KAAK,GAAGC,uBAAuB,CAACP,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIQ,MAAM,GAAGT,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AAExD,IAAIS,CAAC,GAAGT,OAAO,CAAC,GAAG,CAAC;AAEpB,SAASU,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASJ,uBAAuBA,CAACQ,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAId,OAAO,CAACc,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGnB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACoB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIrB,MAAM,CAACsB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGnB,MAAM,CAACoB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE1B,MAAM,CAACC,cAAc,CAACiB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIS,WAAW,GAAG,SAASA,WAAWA,CAACC,aAAa,EAAE;EACpD,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACtB,CAAC,CAACuB,cAAc,EAAE,IAAI,EAAE,UAAUC,IAAI,EAAE;IAC9E,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IACpC,IAAIC,MAAM,GAAGD,YAAY,CAAC,OAAO,CAAC;IAElC,QAAQJ,aAAa;MACnB,KAAK,OAAO;MACZ,KAAK,MAAM;QACT,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACvB,MAAM,CAAC,SAAS,CAAC,EAAE;UACzD4B,KAAK,EAAE5B,MAAM,CAAC,SAAS,CAAC,CAAC6B;QAC3B,CAAC,CAAC;MAEJ,KAAK,QAAQ;MACb,KAAK,YAAY;MACjB,KAAK,UAAU;MACf,KAAK,UAAU;MACf,KAAK,UAAU;QACb,OAAO,aAAa/B,KAAK,CAACyB,aAAa,CAACvB,MAAM,CAAC,SAAS,CAAC,EAAE;UACzD4B,KAAK,EAAE5B,MAAM,CAAC,SAAS,CAAC,CAAC6B,sBAAsB;UAC/CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,MAAM,EAAE,QAAQ;QACvC,CAAC,CAAC;MAEJ;QACE,OAAO,aAAa7B,KAAK,CAACyB,aAAa,CAACvB,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC;IACpE;EACF,CAAC,CAAC;AACJ,CAAC;AAED,IAAIgC,QAAQ,GAAGX,WAAW;AAC1BzB,OAAO,CAAC,SAAS,CAAC,GAAGoC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}