{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\gestioneConsegne.jsx\";\n/*\n *\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneConsegne - visualizzazione e assegnazione dei documenti relativi alle consegne\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaAutista from \"../logistica/selezionaAutista\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\nimport ModificaStato from \"../../aggiunta_dati/modificaStato\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport \"../../css/DataTableDemo.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass AssegnaConsegneChain extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      description: \"\",\n      createAt: \"\",\n      updateAt: \"\",\n      isValid: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value,\n        loading: true\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      var url = 'documents?idWarehouses=' + e.value + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: [],\n      results3: [],\n      results4: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      resultDialog5: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: false,\n      dateFilter: null,\n      autisti: [],\n      idEmployee: 0,\n      mex: \"\",\n      firstName: \"\",\n      address: \"\",\n      indFatt: \"\",\n      selectedWarehouse: null,\n      displayed: false,\n      respLog: '',\n      search: '',\n      value: null,\n      value2: null,\n      selectedDocuments: null,\n      clienti: null,\n      param: '?idWarehouses=',\n      totalRecords: 0,\n      selectedRetailer: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n      var param = '?idRetailer=';\n      var url = 'documents' + param + e.value.code + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          param: param,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.retailers = [];\n    this.autista = [];\n    this.warehouse = [];\n    this.loadLazyTimeout = null;\n    //Dichiarazione funzioni e metodi\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.modifica = this.modifica.bind(this);\n    this.hideDialogModifica = this.hideDialogModifica.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.filterDate = this.filterDate.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog5: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"employees/?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli autisti. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'retailers/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.retailers.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizza dettagli\n  hidevisualizzaDett() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      submitted: false,\n      resultDialog: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var autisti = [];\n    var respLog = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'AUTISTA' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_assigned);\n          autisti.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'LOGISTICA' && result.tasks === null) {\n          var tasks = 0;\n          tasks = parseInt(element.task_create) + parseInt(element.task_assigned);\n          respLog.push({\n            label: element.first_name + ' (task assegnate: ' + tasks + ')',\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.autista = autisti;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog: true,\n      autisti: autisti,\n      respLog: respLog,\n      mex: message\n    });\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var autisti = [];\n    var respLog = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          /* if (element.idUser.role === 'AUTISTA') {\n              autisti.push({\n                  label: element.idUser.idRegistry.firstName,\n                  value: element.idUser.idEmployee.id,\n              });\n          } else */\n          if (element.role === 'LOGISTICA') {\n            var tasks = 0;\n            tasks = parseInt(element.task_create) + parseInt(element.task_assigned);\n            respLog.push({\n              label: element.first_name + ' (task assegnate: ' + tasks + ')',\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.autista = autisti;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog: true,\n        autisti: autisti,\n        respLog: respLog,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null);\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'AUTISTA') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_assigned);\n                autisti.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              } /*  else if (element.idUser.role === 'LOGISTICA') {\n                   respLog.push({\n                       label: element.idUser.idRegistry.firstName,\n                       value: element.idUser.idEmployee.id,\n                   });\n                } */\n            });\n          }\n          this.autista = autisti;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog: true,\n            autisti: autisti,\n            respLog: respLog,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di autisti assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'AUTISTA') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_assigned);\n              autisti.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            } /*  else if (element.idUser.role === 'LOGISTICA') {\n                 respLog.push({\n                     label: element.idUser.idRegistry.firstName,\n                     value: element.idUser.idEmployee.id,\n                 });\n              } */\n          });\n        }\n        this.autista = autisti;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog: true,\n          autisti: autisti,\n          respLog: respLog,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  modifica(result) {\n    if (result.tasks !== null) {\n      this.setState({\n        result,\n        resultDialog3: true\n      });\n    } else {\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"E' necessario assegnare la lavorazione prima di poterne modificare lo stato\",\n        life: 3000\n      });\n    }\n  }\n  hideDialogModifica() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        param: '?idWarehouses='\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        param: '?idWarehouses='\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  filterDate(e, key) {\n    var filter = [];\n    if (key === 'dataConsegna') {\n      filter = this.state.results2.filter(el => new Date(el.deliveryDate).toLocaleDateString() === new Date(e.value).toLocaleDateString());\n      this.setState({\n        value: e.value,\n        results: filter,\n        value2: null\n      });\n    } else {\n      filter = this.state.results2.filter(el => new Date(el.documentDate).toLocaleDateString() === new Date(e.value).toLocaleDateString());\n      this.setState({\n        value2: e.value,\n        results: filter,\n        value: null\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            deliveryDate: element.deliveryDate,\n            documentBodies: element.documentBodies,\n            tasks: element.tasks,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status,\n            note: element.note,\n            erpSync: element.erpSync,\n            totalPayed: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalPayed)),\n            total: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(element.totalTaxed))\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  selectionChangeHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog5: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              firstName: this.state.firstName,\n              address: this.state.address,\n              indFatt: this.state.indFatt,\n              mex: this.state.mex,\n              doc: true,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 755,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialogModifica,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter4 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 791,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"deliveryDate\",\n      header: Costanti.DCons,\n      body: \"deliveryDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.manager.idUser.username\",\n      header: Costanti.Responsabile,\n      showHeader: true\n    }, {\n      field: \"operator\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"status\",\n      header: Costanti.Stato,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"total\",\n      header: Costanti.Tot,\n      showHeader: true\n    }, {\n      field: \"totalPayed\",\n      header: Costanti.TotPag,\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-plus-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create'\n    }, {\n      name: Costanti.CambiaStato,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 49\n      }, this),\n      handler: this.modifica\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneConsegne\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionChangeHandler(e),\n          showExtraButton2: true,\n          actionExtraButton2: this.assegnaLavorazioni,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Ordini\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaAutista, {\n          result: this.state.result,\n          autista: this.autista,\n          respLog: this.state.respLog,\n          chain: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          documento: this.state.result,\n          result: this.state.results3,\n          results: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 952,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.CambiaStato,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialogModifica,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field\",\n          children: /*#__PURE__*/_jsxDEV(ModificaStato, {\n            result: this.state.result,\n            results: this.state.results\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog5,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter4,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 983,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 985,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 981,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog4,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 999,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedRetailer,\n          options: this.retailers,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona cliente\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 991,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default AssegnaConsegneChain;", "map": {"version": 3, "names": ["React", "Component", "Nav", "CustomDataTable", "SelezionaAutista", "VisualizzaDocumenti", "ModificaStato", "Print", "JoyrideGen", "Dropdown", "Toast", "<PERSON><PERSON>", "Dialog", "Sidebar", "<PERSON><PERSON>", "APIRequest", "jsxDEV", "_jsxDEV", "AssegnaConsegneChain", "constructor", "props", "emptyResult", "id", "description", "createAt", "updateAt", "<PERSON><PERSON><PERSON><PERSON>", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "loading", "window", "sessionStorage", "setItem", "url", "state", "lazyParams", "rows", "page", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$tasks", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "firstName", "documentDate", "deliveryDate", "documentBodies", "tasks", "status", "note", "erpSync", "totalPayed", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "parseFloat", "total", "totalTaxed", "push", "results", "results2", "totalRecords", "totalCount", "first", "pageCount", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "results3", "results4", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "resultDialog5", "submitted", "result", "globalFilter", "dateFilter", "autisti", "idEmployee", "mex", "address", "indFatt", "displayed", "respLog", "search", "value2", "selectedDocuments", "clienti", "param", "<PERSON><PERSON><PERSON><PERSON>", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "code", "_element$tasks2", "_e$response3", "_e$response4", "retailers", "au<PERSON>", "warehouse", "loadLazyTimeout", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "modifica", "hideDialogModifica", "reset", "resetDesc", "filterDate", "assegnaLavorazioni", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onPage", "onSort", "onFilter", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "JSON", "parse", "getItem", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "documentBody", "task", "_e$response1", "_e$response10", "DateTimeFormat", "day", "month", "year", "Date", "role", "taskAss", "parseInt", "task_create", "task_assigned", "label", "first_name", "last_name", "idemployee", "_objectSpread", "filter", "el", "length", "FilterOp", "operator", "_element$tasks4", "_e$response11", "_e$response12", "_element$tasks5", "_e$response13", "_e$response14", "key", "toLocaleDateString", "event", "clearTimeout", "setTimeout", "_element$tasks6", "_e$response15", "_e$response16", "Math", "random", "field", "_element$tasks7", "_e$response17", "_e$response18", "loadLazyData", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "doc", "disabled", "resultDialogFooter3", "resultDialogFooter4", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "DCons", "Responsabile", "Operatore", "Stato", "<PERSON><PERSON>", "TotPag", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "icon", "handler", "assegnaLavorazione", "CambiaStato", "filterDnone", "ref", "gestioneConsegne", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "disabledExtraButton2", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "classInputSearch", "fileNames", "visible", "modal", "footer", "onHide", "chain", "DocAll", "draggable", "orders", "Primadiproseguire", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/gestioneConsegne.jsx"], "sourcesContent": ["/*\n *\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneConsegne - visualizzazione e assegnazione dei documenti relativi alle consegne\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport SelezionaAutista from \"../logistica/selezionaAutista\";\nimport VisualizzaDocumenti from \"../../components/generalizzazioni/visualizzaDocumenti\";\nimport ModificaStato from \"../../aggiunta_dati/modificaStato\";\nimport { Print } from \"../../components/print/templateOrderPrint\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { <PERSON>nti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport \"../../css/DataTableDemo.css\";\n\nclass AssegnaConsegneChain extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        description: \"\",\n        createAt: \"\",\n        updateAt: \"\",\n        isValid: \"\",\n    };\n    constructor(props) {\n        super(props);\n        //Dichiarazione variabili di scena\n        this.state = {\n            results: null,\n            results2: [],\n            results3: [],\n            results4: [],\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            resultDialog5: false,\n            submitted: false,\n            result: this.emptyResult,\n            globalFilter: null,\n            loading: false,\n            dateFilter: null,\n            autisti: [],\n            idEmployee: 0,\n            mex: \"\",\n            firstName: \"\",\n            address: \"\",\n            indFatt: \"\",\n            selectedWarehouse: null,\n            displayed: false,\n            respLog: '',\n            search: '',\n            value: null,\n            value2: null,\n            selectedDocuments: null,\n            clienti: null,\n            param: '?idWarehouses=',\n            totalRecords: 0,\n            selectedRetailer: null,\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedRetailer: e.value\n            });\n\n            var param = '?idRetailer='\n\n\n            var url = 'documents' + param + e.value.code + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        param: param,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.retailers = []\n        this.autista = [];\n        this.warehouse = [];\n        this.loadLazyTimeout = null;\n        //Dichiarazione funzioni e metodi\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.modifica = this.modifica.bind(this);\n        this.hideDialogModifica = this.hideDialogModifica.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.filterDate = this.filterDate.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog5: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"employees/?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli autisti. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest('GET', 'retailers/')\n            .then(res => {\n                res.data.forEach(element => {\n                    if (element && element.idRegistry) {\n                        var x = {\n                            name: element.idRegistry.firstName || 'Nome non disponibile',\n                            code: element.id || 0\n                        }\n                        this.retailers.push(x)\n                    }\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value, loading: true });\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        var url = 'documents?idWarehouses=' + e.value + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        deliveryDate: element.deliveryDate,\n                        documentBodies: element.documentBodies,\n                        tasks: element.tasks,\n                        status: element.tasks?.status,\n                        note: element.note,\n                        erpSync: element.erpSync,\n                        totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                        total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results2: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizza dettagli\n    hidevisualizzaDett() {\n        this.setState({\n            resultDialog2: false,\n        });\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            submitted: false,\n            resultDialog: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var autisti = [];\n        var respLog = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'AUTISTA' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_assigned)\n                    autisti.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'LOGISTICA' && result.tasks === null) {\n                    var tasks = 0\n                    tasks = parseInt(element.task_create) + parseInt(element.task_assigned)\n                    respLog.push({\n                        label: element.first_name + ' (task assegnate: ' + tasks + ')',\n                        value: element.idemployee,\n                    });\n                }\n            })\n        }\n        this.autista = autisti;\n        this.setState({\n            result: { ...result },\n            resultDialog: true,\n            autisti: autisti,\n            respLog: respLog,\n            mex: message,\n        });\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var autisti = [];\n        var respLog = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    /* if (element.idUser.role === 'AUTISTA') {\n                        autisti.push({\n                            label: element.idUser.idRegistry.firstName,\n                            value: element.idUser.idEmployee.id,\n                        });\n                    } else */\n                    if (element.role === 'LOGISTICA') {\n                        var tasks = 0\n                        tasks = parseInt(element.task_create) + parseInt(element.task_assigned)\n                        respLog.push({\n                            label: element.first_name + ' (task assegnate: ' + tasks + ')',\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.autista = autisti;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog: true,\n                autisti: autisti,\n                respLog: respLog,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'AUTISTA') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_assigned)\n                                autisti.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }/*  else if (element.idUser.role === 'LOGISTICA') {\n                                respLog.push({\n                                    label: element.idUser.idRegistry.firstName,\n                                    value: element.idUser.idEmployee.id,\n                                });\n                            } */\n                        })\n                    }\n                    this.autista = autisti;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog: true,\n                        autisti: autisti,\n                        respLog: respLog,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di autisti assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'AUTISTA') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_assigned)\n                            autisti.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }/*  else if (element.idUser.role === 'LOGISTICA') {\n                            respLog.push({\n                                label: element.idUser.idRegistry.firstName,\n                                value: element.idUser.idEmployee.id,\n                            });\n                        } */\n                    })\n                }\n                this.autista = autisti;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog: true,\n                    autisti: autisti,\n                    respLog: respLog,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n    modifica(result) {\n        if (result.tasks !== null) {\n            this.setState({\n                result,\n                resultDialog3: true\n            })\n        } else {\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: \"E' necessario assegnare la lavorazione prima di poterne modificare lo stato\",\n                life: 3000,\n            });\n        }\n    }\n    hideDialogModifica() {\n        this.setState({\n            resultDialog3: false\n        })\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', param: '?idWarehouses=' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', param: '?idWarehouses=' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    filterDate(e, key) {\n        var filter = []\n        if (key === 'dataConsegna') {\n            filter = this.state.results2.filter(el => new Date(el.deliveryDate).toLocaleDateString() === new Date(e.value).toLocaleDateString())\n            this.setState({ value: e.value, results: filter, value2: null })\n        } else {\n            filter = this.state.results2.filter(el => new Date(el.documentDate).toLocaleDateString() === new Date(e.value).toLocaleDateString())\n            this.setState({ value2: e.value, results: filter, value: null })\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI-DDT. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + '&documentType=CLI-DDT,CLI-FATACC&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            deliveryDate: element.deliveryDate,\n                            documentBodies: element.documentBodies,\n                            tasks: element.tasks,\n                            status: element.tasks?.status,\n                            note: element.note,\n                            erpSync: element.erpSync,\n                            totalPayed: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalPayed)),\n                            total: new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(element.totalTaxed))\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    selectionChangeHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog5: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog4: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog4: false\n        })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                firstName={this.state.firstName}\n                                address={this.state.address}\n                                indFatt={this.state.indFatt}\n                                mex={this.state.mex}\n                                doc={true}\n                                disabled={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialogModifica}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const resultDialogFooter4 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"retailer\",\n                header: Costanti.cliente,\n                body: \"retailer\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"deliveryDate\",\n                header: Costanti.DCons,\n                body: \"deliveryDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.manager.idUser.username\",\n                header: Costanti.Responsabile,\n                showHeader: true,\n            },\n            {\n                field: \"operator\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"status\",\n                header: Costanti.Stato,\n                body: \"assigned\",\n                showHeader: true,\n            },\n            {\n                field: \"total\",\n                header: Costanti.Tot,\n                showHeader: true,\n            },\n            {\n                field: \"totalPayed\",\n                header: Costanti.TotPag,\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: \"ERP Sync\",\n                body: \"erpSync\",\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-plus-circle\" />, handler: this.editResult, status: 'create' },\n            { name: Costanti.CambiaStato, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.modifica },\n        ];\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => (this.toast = el)} />\n                {/* Il componente NavLogistica contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.gestioneConsegne}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => (this.dt = el)}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionChangeHandler(e)}\n                        showExtraButton2={true}\n                        actionExtraButton2={this.assegnaLavorazioni}\n                        labelExtraButton2={Costanti.assegnaLavorazioni}\n                        disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Ordini\"\n                    />\n                </div>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaAutista result={this.state.result} autista={this.autista} respLog={this.state.respLog} chain={true} />\n                </Dialog>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter2}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        documento={this.state.result}\n                        result={this.state.results3}\n                        results={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={Costanti.CambiaStato}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialogModifica}\n                >\n                    <div className=\"p-field\">\n                        <ModificaStato result={this.state.result} results={this.state.results} />\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog5} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter4}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog4} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedRetailer} options={this.retailers} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default AssegnaConsegneChain;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,oBAAoB,SAASjB,SAAS,CAAC;EASzCkB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IAVJ;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACb,CAAC;IA4ND;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG,KAAK;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5DC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEP,CAAC,CAACG,KAAK,CAAC;MACrD,IAAIK,GAAG,GAAG,yBAAyB,GAAGR,CAAC,CAACG,KAAK,GAAG,wCAAwC,GAAG,IAAI,CAACM,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MAC7J,MAAMzB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAAZ,cAAA,GAAED,OAAO,CAACY,KAAK,cAAAX,cAAA,uBAAbA,cAAA,CAAeY,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCvC,UAAU,EAAE;YAAEwC,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACwC,KAAK;YAAEvC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEuC,SAAS,EAAErC,GAAG,CAACE,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACxC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAAqD,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAT,WAAA,GAAArD,CAAC,CAAC+D,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYrC,IAAI,MAAKgD,SAAS,IAAAV,YAAA,GAAGtD,CAAC,CAAC+D,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYtC,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IAnQG,IAAI,CAACzD,KAAK,GAAG;MACTqC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,EAAE;MACZoB,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,IAAI,CAAClF,WAAW;MACxBmF,YAAY,EAAE,IAAI;MAClBxE,OAAO,EAAE,KAAK;MACdyE,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC;MACbC,GAAG,EAAE,EAAE;MACPrD,SAAS,EAAE,EAAE;MACbsD,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXhF,iBAAiB,EAAE,IAAI;MACvBiF,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVlF,KAAK,EAAE,IAAI;MACXmF,MAAM,EAAE,IAAI;MACZC,iBAAiB,EAAE,IAAI;MACvBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,gBAAgB;MACvBzC,YAAY,EAAE,CAAC;MACf0C,gBAAgB,EAAE,IAAI;MACtBhF,UAAU,EAAE;QACRwC,KAAK,EAAE,CAAC;QACRvC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACP+E,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAE1F,KAAK,EAAE,EAAE;YAAE2F,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE3F,KAAK,EAAE,EAAE;YAAE2F,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE3F,KAAK,EAAE,EAAE;YAAE2F,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAM/F,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACVG,OAAO,EAAE,IAAI;QACbiF,MAAM,EAAErF,CAAC,CAACG,KAAK,CAAC6F,IAAI;QACpBN,gBAAgB,EAAE1F,CAAC,CAACG;MACxB,CAAC,CAAC;MAEF,IAAIsF,KAAK,GAAG,cAAc;MAG1B,IAAIjF,GAAG,GAAG,WAAW,GAAGiF,KAAK,GAAGzF,CAAC,CAACG,KAAK,CAAC8F,IAAI,GAAG,wCAAwC,GAAG,IAAI,CAACxF,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MAC5J,MAAMzB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA+E,eAAA;UAClC,IAAI7E,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAAkE,eAAA,GAAE/E,OAAO,CAACY,KAAK,cAAAmE,eAAA,uBAAbA,eAAA,CAAelE,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCwC,KAAK,EAAEA,KAAK;UACZ/E,UAAU,EAAE;YAAEwC,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACwC,KAAK;YAAEvC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEuC,SAAS,EAAErC,GAAG,CAACE,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACxC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAAmG,YAAA,EAAAC,YAAA;QACV7C,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAqC,YAAA,GAAAnG,CAAC,CAAC+D,QAAQ,cAAAoC,YAAA,uBAAVA,YAAA,CAAYnF,IAAI,MAAKgD,SAAS,IAAAoC,YAAA,GAAGpG,CAAC,CAAC+D,QAAQ,cAAAqC,YAAA,uBAAVA,YAAA,CAAYpF,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACmC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAAC3G,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC2G,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACJ,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACK,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACL,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACM,KAAK,GAAG,IAAI,CAACA,KAAK,CAACN,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACO,SAAS,GAAG,IAAI,CAACA,SAAS,CAACP,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACR,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACT,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACU,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACV,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACX,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACY,MAAM,GAAG,IAAI,CAACA,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACb,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACc,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACd,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACe,UAAU,GAAG,IAAI,CAACA,UAAU,CAACf,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACgB,WAAW,GAAG,IAAI,CAACA,WAAW,CAAChB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA;EACA,MAAMiB,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACzH,MAAM,CAACC,cAAc,CAACyH,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK5D,SAAS,EAAE;MACxE,IAAIxD,GAAG,GAAG,yBAAyB,GAAGoH,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAACnH,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MACjK,IAAI,CAACX,QAAQ,CAAC;QAAEC,iBAAiB,EAAE0H;MAAY,CAAC,CAAC;MACjD,MAAMzI,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA6G,eAAA;UAClC,IAAI3G,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAAgG,eAAA,GAAE7G,OAAO,CAACY,KAAK,cAAAiG,eAAA,uBAAbA,eAAA,CAAehG,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCvC,UAAU,EAAE;YAAEwC,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACwC,KAAK;YAAEvC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEuC,SAAS,EAAErC,GAAG,CAACE,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACxC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAAiI,YAAA,EAAAC,YAAA;QACV3E,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAmE,YAAA,GAAAjI,CAAC,CAAC+D,QAAQ,cAAAkE,YAAA,uBAAVA,YAAA,CAAYjH,IAAI,MAAKgD,SAAS,IAAAkE,YAAA,GAAGlI,CAAC,CAAC+D,QAAQ,cAAAmE,YAAA,uBAAVA,YAAA,CAAYlH,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACjE,QAAQ,CAAC;QAAEwE,aAAa,EAAE,IAAI;QAAEU,SAAS,EAAE;MAAK,CAAC,CAAC;IAC3D;IACA,MAAMhG,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC0B,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIqH,KAAK,IAAIrH,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAACuF,SAAS,CAAC1D,IAAI,CAAC;UAChBmD,IAAI,EAAEmC,KAAK,CAACC,aAAa;UACzBjI,KAAK,EAAEgI,KAAK,CAACzI;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACD0D,KAAK,CAAEpD,CAAC,IAAK;MAAA,IAAAqI,YAAA,EAAAC,YAAA;MACV/E,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;MACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAuE,YAAA,GAAArI,CAAC,CAAC+D,QAAQ,cAAAsE,YAAA,uBAAVA,YAAA,CAAYrH,IAAI,MAAKgD,SAAS,IAAAsE,YAAA,GAAGtI,CAAC,CAAC+D,QAAQ,cAAAuE,YAAA,uBAAVA,YAAA,CAAYtH,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAM/E,UAAU,CAAC,KAAK,EAAE,iCAAiC,CAAC,CACrD0B,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACb,QAAQ,CAAC;QACVmE,QAAQ,EAAEtD,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDoC,KAAK,CAAEpD,CAAC,IAAK;MAAA,IAAAuI,YAAA,EAAAC,YAAA;MACVjF,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;MACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAyE,YAAA,GAAAvI,CAAC,CAAC+D,QAAQ,cAAAwE,YAAA,uBAAVA,YAAA,CAAYvH,IAAI,MAAKgD,SAAS,IAAAwE,YAAA,GAAGxI,CAAC,CAAC+D,QAAQ,cAAAyE,YAAA,uBAAVA,YAAA,CAAYxH,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAM/E,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC0B,IAAI,CAACC,GAAG,IAAI;MACTA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,IAAIA,OAAO,CAACO,UAAU,EAAE;UAC/B,IAAIL,CAAC,GAAG;YACJ2E,IAAI,EAAE7E,OAAO,CAACO,UAAU,CAACC,SAAS,IAAI,sBAAsB;YAC5DsE,IAAI,EAAE9E,OAAO,CAACzB,EAAE,IAAI;UACxB,CAAC;UACD,IAAI,CAAC2G,SAAS,CAACxD,IAAI,CAACxB,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC,CAAC+B,KAAK,CAAEpD,CAAC,IAAK;MACZuD,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;IAClB,CAAC,CAAC;EACV;EA6CA;EACA,MAAMyG,cAAcA,CAAC9B,MAAM,EAAE;IACzB,IAAInE,GAAG,GAAG,2BAA2B,GAAGmE,MAAM,CAACjF,EAAE;IACjD,IAAI+I,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMvJ,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;MACX2H,YAAY,GAAG3H,GAAG,CAACE,IAAI,CAACc,cAAc;MACtC6C,MAAM,CAAC7C,cAAc,GAAGhB,GAAG,CAACE,IAAI,CAACc,cAAc;MAC/C4G,IAAI,GAAG5H,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDoC,KAAK,CAAEpD,CAAC,IAAK;MAAA,IAAA2I,YAAA,EAAAC,aAAA;MACVrF,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;MACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA6E,YAAA,GAAA3I,CAAC,CAAC+D,QAAQ,cAAA4E,YAAA,uBAAVA,YAAA,CAAY3H,IAAI,MAAKgD,SAAS,IAAA4E,aAAA,GAAG5I,CAAC,CAAC+D,QAAQ,cAAA6E,aAAA,uBAAVA,aAAA,CAAY5H,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBU,MAAM,CAACrD,MAAM,GACb,OAAO,GACP,IAAIc,IAAI,CAACyG,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACvG,MAAM,CAAC,IAAIwG,IAAI,CAACtE,MAAM,CAAC/C,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAC3B,QAAQ,CAAC;MACVqE,aAAa,EAAE,IAAI;MACnBK,MAAM,EAAE+D,IAAI;MACZvE,QAAQ,EAAEsE,YAAY;MACtBzD,GAAG,EAAEf;IACT,CAAC,CAAC;EACN;EACA;EACA0C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC1G,QAAQ,CAAC;MACVqE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACAuC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC5G,QAAQ,CAAC;MACVyE,SAAS,EAAE,KAAK;MAChBL,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACAuC,UAAUA,CAACjC,MAAM,EAAE;IACf,IAAIV,OAAO,GACP,oBAAoB,GACpBU,MAAM,CAACrD,MAAM,GACb,OAAO,GACP,IAAIc,IAAI,CAACyG,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACvG,MAAM,CAAC,IAAIwG,IAAI,CAACtE,MAAM,CAAC/C,YAAY,CAAC,CAAC;IAC5C,IAAIkD,OAAO,GAAG,EAAE;IAChB,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC3E,KAAK,CAAC2D,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAAC3D,KAAK,CAAC2D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAAC+H,IAAI,KAAK,SAAS,IAAIvE,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;UACrD,IAAIoH,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAACjI,OAAO,CAACkI,WAAW,CAAC,GAAGD,QAAQ,CAACjI,OAAO,CAACmI,aAAa,CAAC;UACzExE,OAAO,CAACjC,IAAI,CAAC;YACT0G,KAAK,EAAEpI,OAAO,CAACqI,UAAU,GAAG,GAAG,GAAGrI,OAAO,CAACsI,SAAS,GAAG,oBAAoB,GAAGN,OAAO,GAAG,GAAG;YAC1FhJ,KAAK,EAAEgB,OAAO,CAACuI;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAIvI,OAAO,CAAC+H,IAAI,KAAK,WAAW,IAAIvE,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;UAC9D,IAAIA,KAAK,GAAG,CAAC;UACbA,KAAK,GAAGqH,QAAQ,CAACjI,OAAO,CAACkI,WAAW,CAAC,GAAGD,QAAQ,CAACjI,OAAO,CAACmI,aAAa,CAAC;UACvElE,OAAO,CAACvC,IAAI,CAAC;YACT0G,KAAK,EAAEpI,OAAO,CAACqI,UAAU,GAAG,oBAAoB,GAAGzH,KAAK,GAAG,GAAG;YAC9D5B,KAAK,EAAEgB,OAAO,CAACuI;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACpD,OAAO,GAAGxB,OAAO;IACtB,IAAI,CAAC7E,QAAQ,CAAC;MACV0E,MAAM,EAAAgF,aAAA,KAAOhF,MAAM,CAAE;MACrBN,YAAY,EAAE,IAAI;MAClBS,OAAO,EAAEA,OAAO;MAChBM,OAAO,EAAEA,OAAO;MAChBJ,GAAG,EAAEf;IACT,CAAC,CAAC;EACN;EACAkD,kBAAkBA,CAAA,EAAG;IACjB,IAAIlD,OAAO,GACP,iCAAiC;IACrC,IAAIa,OAAO,GAAG,EAAE;IAChB,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAIwE,MAAM,GAAG,IAAI,CAACnJ,KAAK,CAAC8E,iBAAiB,CAACqE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC9H,KAAK,KAAK,IAAI,CAAC;IACzE,IAAI6H,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAACrJ,KAAK,CAAC2D,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAAC3D,KAAK,CAAC2D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;UACrC;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAIA,OAAO,CAAC+H,IAAI,KAAK,WAAW,EAAE;YAC9B,IAAInH,KAAK,GAAG,CAAC;YACbA,KAAK,GAAGqH,QAAQ,CAACjI,OAAO,CAACkI,WAAW,CAAC,GAAGD,QAAQ,CAACjI,OAAO,CAACmI,aAAa,CAAC;YACvElE,OAAO,CAACvC,IAAI,CAAC;cACT0G,KAAK,EAAEpI,OAAO,CAACqI,UAAU,GAAG,oBAAoB,GAAGzH,KAAK,GAAG,GAAG;cAC9D5B,KAAK,EAAEgB,OAAO,CAACuI;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACpD,OAAO,GAAGxB,OAAO;MACtB,IAAI,CAAC7E,QAAQ,CAAC;QACV0E,MAAM,EAAE,IAAI,CAAClE,KAAK,CAAC8E,iBAAiB;QACpClB,YAAY,EAAE,IAAI;QAClBS,OAAO,EAAEA,OAAO;QAChBM,OAAO,EAAEA,OAAO;QAChBJ,GAAG,EAAEf;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAI2F,MAAM,CAACE,MAAM,KAAK,IAAI,CAACrJ,KAAK,CAAC8E,iBAAiB,CAACuE,MAAM,EAAE;MAC9D,IAAIC,QAAQ,GAAG,IAAI,CAACtJ,KAAK,CAAC8E,iBAAiB,CAACqE,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACY,KAAK,CAACiI,QAAQ,KAAK,IAAI,CAAC;MAC9F,IAAID,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIC,QAAQ,CAACD,MAAM,KAAK,IAAI,CAACrJ,KAAK,CAAC8E,iBAAiB,CAACuE,MAAM,EAAE;UACzD,IAAI,IAAI,CAACrJ,KAAK,CAAC2D,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAAC3D,KAAK,CAAC2D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAAC+H,IAAI,KAAK,SAAS,EAAE;gBAC5B,IAAIC,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAACjI,OAAO,CAACkI,WAAW,CAAC,GAAGD,QAAQ,CAACjI,OAAO,CAACmI,aAAa,CAAC;gBACzExE,OAAO,CAACjC,IAAI,CAAC;kBACT0G,KAAK,EAAEpI,OAAO,CAACqI,UAAU,GAAG,GAAG,GAAGrI,OAAO,CAACsI,SAAS,GAAG,oBAAoB,GAAGN,OAAO,GAAG,GAAG;kBAC1FhJ,KAAK,EAAEgB,OAAO,CAACuI;gBACnB,CAAC,CAAC;cACN,CAAC;AAC7B;AACA;AACA;AACA;AACA;YACwB,CAAC,CAAC;UACN;UACA,IAAI,CAACpD,OAAO,GAAGxB,OAAO;UACtB,IAAI,CAAC7E,QAAQ,CAAC;YACV0E,MAAM,EAAE,IAAI,CAAClE,KAAK,CAAC8E,iBAAiB;YACpClB,YAAY,EAAE,IAAI;YAClBS,OAAO,EAAEA,OAAO;YAChBM,OAAO,EAAEA,OAAO;YAChBJ,GAAG,EAAEf;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,sIAAsI;YAC9IK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAACzD,KAAK,CAAC2D,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAAC3D,KAAK,CAAC2D,QAAQ,CAAClD,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAAC+H,IAAI,KAAK,SAAS,EAAE;cAC5B,IAAIC,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAACjI,OAAO,CAACkI,WAAW,CAAC,GAAGD,QAAQ,CAACjI,OAAO,CAACmI,aAAa,CAAC;cACzExE,OAAO,CAACjC,IAAI,CAAC;gBACT0G,KAAK,EAAEpI,OAAO,CAACqI,UAAU,GAAG,GAAG,GAAGrI,OAAO,CAACsI,SAAS,GAAG,oBAAoB,GAAGN,OAAO,GAAG,GAAG;gBAC1FhJ,KAAK,EAAEgB,OAAO,CAACuI;cACnB,CAAC,CAAC;YACN,CAAC;AACzB;AACA;AACA;AACA;AACA;UACoB,CAAC,CAAC;QACN;QACA,IAAI,CAACpD,OAAO,GAAGxB,OAAO;QACtB,IAAI,CAAC7E,QAAQ,CAAC;UACV0E,MAAM,EAAE,IAAI,CAAClE,KAAK,CAAC8E,iBAAiB;UACpClB,YAAY,EAAE,IAAI;UAClBS,OAAO,EAAEA,OAAO;UAChBM,OAAO,EAAEA,OAAO;UAChBJ,GAAG,EAAEf;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA4C,QAAQA,CAACnC,MAAM,EAAE;IACb,IAAIA,MAAM,CAAC5C,KAAK,KAAK,IAAI,EAAE;MACvB,IAAI,CAAC9B,QAAQ,CAAC;QACV0E,MAAM;QACNJ,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACd,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,6EAA6E;QACrFK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA6C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC9G,QAAQ,CAAC;MACVsE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA,MAAMyC,KAAKA,CAAA,EAAG;IACV,IAAIY,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACzH,MAAM,CAACC,cAAc,CAACyH,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK5D,SAAS,EAAE;MACxE,IAAIxD,GAAG,GAAG,yBAAyB,GAAGoH,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAACnH,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MACjK,IAAI,CAACX,QAAQ,CAAC;QAAEC,iBAAiB,EAAE0H,WAAW;QAAExH,OAAO,EAAE,IAAI;QAAEiF,MAAM,EAAE,EAAE;QAAEI,KAAK,EAAE;MAAiB,CAAC,CAAC;MACrG,MAAMtG,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA8I,eAAA;UAClC,IAAI5I,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAAiI,eAAA,GAAE9I,OAAO,CAACY,KAAK,cAAAkI,eAAA,uBAAbA,eAAA,CAAejI,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCvC,UAAU,EAAE;YAAEwC,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACwC,KAAK;YAAEvC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEuC,SAAS,EAAErC,GAAG,CAACE,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACxC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAAkK,aAAA,EAAAC,aAAA;QACV5G,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAoG,aAAA,GAAAlK,CAAC,CAAC+D,QAAQ,cAAAmG,aAAA,uBAAVA,aAAA,CAAYlJ,IAAI,MAAKgD,SAAS,IAAAmG,aAAA,GAAGnK,CAAC,CAAC+D,QAAQ,cAAAoG,aAAA,uBAAVA,aAAA,CAAYnJ,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAM+C,SAASA,CAAA,EAAG;IACd,IAAIW,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACzH,MAAM,CAACC,cAAc,CAACyH,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK5D,SAAS,EAAE;MACxE,IAAIxD,GAAG,GAAG,yBAAyB,GAAGoH,WAAW,GAAG,wCAAwC,GAAG,IAAI,CAACnH,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI;MACjK,IAAI,CAACX,QAAQ,CAAC;QAAEC,iBAAiB,EAAE0H,WAAW;QAAExH,OAAO,EAAE,IAAI;QAAEiF,MAAM,EAAE,EAAE;QAAEI,KAAK,EAAE;MAAiB,CAAC,CAAC;MACrG,MAAMtG,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAiJ,eAAA;UAClC,IAAI/I,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAAoI,eAAA,GAAEjJ,OAAO,CAACY,KAAK,cAAAqI,eAAA,uBAAbA,eAAA,CAAepI,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCvC,UAAU,EAAE;YAAEwC,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACwC,KAAK;YAAEvC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACC,UAAU,CAACE,IAAI;YAAEuC,SAAS,EAAErC,GAAG,CAACE,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACxC,KAAK,CAACC,UAAU,CAACC;UAAM,CAAC;UACpLP,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAAqK,aAAA,EAAAC,aAAA;QACV/G,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAAuG,aAAA,GAAArK,CAAC,CAAC+D,QAAQ,cAAAsG,aAAA,uBAAVA,aAAA,CAAYrJ,IAAI,MAAKgD,SAAS,IAAAsG,aAAA,GAAGtK,CAAC,CAAC+D,QAAQ,cAAAuG,aAAA,uBAAVA,aAAA,CAAYtJ,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACAgD,UAAUA,CAAClH,CAAC,EAAEuK,GAAG,EAAE;IACf,IAAIX,MAAM,GAAG,EAAE;IACf,IAAIW,GAAG,KAAK,cAAc,EAAE;MACxBX,MAAM,GAAG,IAAI,CAACnJ,KAAK,CAACsC,QAAQ,CAAC6G,MAAM,CAACC,EAAE,IAAI,IAAIZ,IAAI,CAACY,EAAE,CAAChI,YAAY,CAAC,CAAC2I,kBAAkB,CAAC,CAAC,KAAK,IAAIvB,IAAI,CAACjJ,CAAC,CAACG,KAAK,CAAC,CAACqK,kBAAkB,CAAC,CAAC,CAAC;MACpI,IAAI,CAACvK,QAAQ,CAAC;QAAEE,KAAK,EAAEH,CAAC,CAACG,KAAK;QAAE2C,OAAO,EAAE8G,MAAM;QAAEtE,MAAM,EAAE;MAAK,CAAC,CAAC;IACpE,CAAC,MAAM;MACHsE,MAAM,GAAG,IAAI,CAACnJ,KAAK,CAACsC,QAAQ,CAAC6G,MAAM,CAACC,EAAE,IAAI,IAAIZ,IAAI,CAACY,EAAE,CAACjI,YAAY,CAAC,CAAC4I,kBAAkB,CAAC,CAAC,KAAK,IAAIvB,IAAI,CAACjJ,CAAC,CAACG,KAAK,CAAC,CAACqK,kBAAkB,CAAC,CAAC,CAAC;MACpI,IAAI,CAACvK,QAAQ,CAAC;QAAEqF,MAAM,EAAEtF,CAAC,CAACG,KAAK;QAAE2C,OAAO,EAAE8G,MAAM;QAAEzJ,KAAK,EAAE;MAAK,CAAC,CAAC;IACpE;EACJ;EACAkH,MAAMA,CAACoD,KAAK,EAAE;IACV,IAAI,CAACxK,QAAQ,CAAC;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAACoG,eAAe,EAAE;MACtBkE,YAAY,CAAC,IAAI,CAAClE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGmE,UAAU,CAAC,YAAY;MAC1C,IAAInK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACgF,KAAK,GAAG,IAAI,CAAChF,KAAK,CAACP,iBAAiB,GAAG,wCAAwC,GAAGuK,KAAK,CAAC9J,IAAI,GAAG,QAAQ,GAAG8J,KAAK,CAAC7J,IAAI;MACvJ,MAAMzB,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyJ,eAAA;UAClC,IAAIvJ,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAA4I,eAAA,GAAEzJ,OAAO,CAACY,KAAK,cAAA6I,eAAA,uBAAbA,eAAA,CAAe5I,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCvC,UAAU,EAAE+J,KAAK;UACjBrK,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAA6K,aAAA,EAAAC,aAAA;QACVvH,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,mFAAAC,MAAA,CAAgF,EAAA+G,aAAA,GAAA7K,CAAC,CAAC+D,QAAQ,cAAA8G,aAAA,uBAAVA,aAAA,CAAY7J,IAAI,MAAKgD,SAAS,IAAA8G,aAAA,GAAG9K,CAAC,CAAC+D,QAAQ,cAAA+G,aAAA,uBAAVA,aAAA,CAAY9J,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACrJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE6G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA1D,MAAMA,CAACmD,KAAK,EAAE;IACV,IAAI,CAACxK,QAAQ,CAAC;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI6K,KAAK,GAAGR,KAAK,CAAC9E,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAG8E,KAAK,CAAC9E,SAAS;IAChG,IAAI,IAAI,CAACa,eAAe,EAAE;MACtBkE,YAAY,CAAC,IAAI,CAAClE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGmE,UAAU,CAAC,YAAY;MAC1C,IAAInK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACgF,KAAK,GAAG,IAAI,CAAChF,KAAK,CAACP,iBAAiB,GAAG,wCAAwC,GAAG,IAAI,CAACO,KAAK,CAACC,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACE,IAAI,GAAG,SAAS,GAAGqK,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAC7E,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACpQ,MAAMzG,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBK,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA+J,eAAA;UAClC,IAAI7J,CAAC,GAAG;YACJ3B,EAAE,EAAEyB,OAAO,CAACzB,EAAE;YACd4B,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACC,SAAS;YACjDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,cAAc,EAAEX,OAAO,CAACW,cAAc;YACtCC,KAAK,EAAEZ,OAAO,CAACY,KAAK;YACpBC,MAAM,GAAAkJ,eAAA,GAAE/J,OAAO,CAACY,KAAK,cAAAmJ,eAAA,uBAAbA,eAAA,CAAelJ,MAAM;YAC7BC,IAAI,EAAEd,OAAO,CAACc,IAAI;YAClBC,OAAO,EAAEf,OAAO,CAACe,OAAO;YACxBC,UAAU,EAAE,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACgB,UAAU,CAAC,CAAC;YACnJQ,KAAK,EAAE,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACvB,OAAO,CAACyB,UAAU,CAAC;UACjJ,CAAC;UACD7B,SAAS,CAAC8B,IAAI,CAACxB,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACpB,QAAQ,CAAC;UACV6C,OAAO,EAAE/B,SAAS;UAClBgC,QAAQ,EAAEhC,SAAS;UACnBiC,YAAY,EAAElC,GAAG,CAACE,IAAI,CAACiC,UAAU;UACjCvC,UAAU,EAAAiJ,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAClJ,KAAK,CAACC,UAAU;YAAEiF,SAAS,EAAE8E,KAAK,CAAC9E,SAAS;YAAEC,SAAS,EAAE6E,KAAK,CAAC7E;UAAS,EAAE;UAChGxF,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDgD,KAAK,CAAEpD,CAAC,IAAK;QAAA,IAAAmL,aAAA,EAAAC,aAAA;QACV7H,OAAO,CAACC,GAAG,CAACxD,CAAC,CAAC;QACd,IAAI,CAACyD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAqH,aAAA,GAAAnL,CAAC,CAAC+D,QAAQ,cAAAoH,aAAA,uBAAVA,aAAA,CAAYnK,IAAI,MAAKgD,SAAS,IAAAoH,aAAA,GAAGpL,CAAC,CAAC+D,QAAQ,cAAAqH,aAAA,uBAAVA,aAAA,CAAYpK,IAAI,GAAGhB,CAAC,CAACiE,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE6G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAzD,QAAQA,CAACkD,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAACxK,QAAQ,CAAC;MAAES,UAAU,EAAE+J;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EAC3D;EACAjE,sBAAsBA,CAACpH,CAAC,EAAE;IACtB,IAAI,CAACC,QAAQ,CAAC;MAAEsF,iBAAiB,EAAEvF,CAAC,CAACG;IAAM,CAAC,CAAC;EACjD;EACAqH,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC/G,KAAK,CAACP,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVwE,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAAChB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAuD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACxH,QAAQ,CAAC;MACVuE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAkD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzH,QAAQ,CAAC;MACVuE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA8G,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBlM,OAAA,CAACjB,KAAK,CAACoN,QAAQ;MAAAC,QAAA,eACXpM,OAAA,CAACN,MAAM;QAAC2M,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC9E,UAAW;QAAA4E,QAAA,GACjE,GAAG,EACHvM,QAAQ,CAAC0M,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMC,mBAAmB,gBACrB5M,OAAA,CAACjB,KAAK,CAACoN,QAAQ;MAAAC,QAAA,eACXpM,OAAA;QAAKqM,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBpM,OAAA;UAAKqM,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBpM,OAAA;YAAKqM,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvCpM,OAAA,CAACN,MAAM;cACH2M,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAChF,kBAAmB;cAAA8E,QAAA,GAEhC,GAAG,EACHvM,QAAQ,CAAC0M,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACT3M,OAAA,CAACV,KAAK;cACFoC,SAAS,EAAE,IAAI,CAACN,KAAK,CAACkE,MAAO;cAC7BR,QAAQ,EAAE,IAAI,CAAC1D,KAAK,CAAC0D,QAAS;cAC9BxC,SAAS,EAAE,IAAI,CAAClB,KAAK,CAACkB,SAAU;cAChCsD,OAAO,EAAE,IAAI,CAACxE,KAAK,CAACwE,OAAQ;cAC5BC,OAAO,EAAE,IAAI,CAACzE,KAAK,CAACyE,OAAQ;cAC5BF,GAAG,EAAE,IAAI,CAACvE,KAAK,CAACuE,GAAI;cACpBkH,GAAG,EAAE,IAAK;cACVC,QAAQ,EAAE;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMI,mBAAmB,gBACrB/M,OAAA,CAACjB,KAAK,CAACoN,QAAQ;MAAAC,QAAA,eACXpM,OAAA,CAACN,MAAM;QAAC2M,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC5E,kBAAmB;QAAA0E,QAAA,GACzE,GAAG,EACHvM,QAAQ,CAAC0M,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMK,mBAAmB,gBACrBhN,OAAA,CAACjB,KAAK,CAACoN,QAAQ;MAAAC,QAAA,eACXpM,OAAA;QAAKqM,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DpM,OAAA,CAACN,MAAM;UAAC2M,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACnE,iBAAkB;UAAAiE,QAAA,GAAE,GAAC,EAACvM,QAAQ,CAAC0M,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAMM,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACIxB,KAAK,EAAE,QAAQ;MACfyB,MAAM,EAAExN,QAAQ,CAACyN,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,MAAM;MACbyB,MAAM,EAAExN,QAAQ,CAACqC,IAAI;MACrBsL,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,UAAU;MACjByB,MAAM,EAAExN,QAAQ,CAAC6N,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,cAAc;MACrByB,MAAM,EAAExN,QAAQ,CAAC8N,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,cAAc;MACrByB,MAAM,EAAExN,QAAQ,CAAC+N,KAAK;MACtBL,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,+BAA+B;MACtCyB,MAAM,EAAExN,QAAQ,CAACgO,YAAY;MAC7BJ,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,UAAU;MACjByB,MAAM,EAAExN,QAAQ,CAACiO,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,QAAQ;MACfyB,MAAM,EAAExN,QAAQ,CAACkO,KAAK;MACtBR,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,OAAO;MACdyB,MAAM,EAAExN,QAAQ,CAACmO,GAAG;MACpBP,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,YAAY;MACnByB,MAAM,EAAExN,QAAQ,CAACoO,MAAM;MACvBR,UAAU,EAAE;IAChB,CAAC,EACD;MACI7B,KAAK,EAAE,SAAS;MAChByB,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMS,YAAY,GAAG,CACjB;MAAEvH,IAAI,EAAE9G,QAAQ,CAACsO,OAAO;MAAEC,IAAI,eAAEpO,OAAA;QAAGqM,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAACjH;IAAe,CAAC,EAC3F;MAAET,IAAI,EAAE9G,QAAQ,CAACyO,kBAAkB;MAAEF,IAAI,eAAEpO,OAAA;QAAGqM,SAAS,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC9G,UAAU;MAAE5E,MAAM,EAAE;IAAS,CAAC,EAC5H;MAAEgE,IAAI,EAAE9G,QAAQ,CAAC0O,WAAW;MAAEH,IAAI,eAAEpO,OAAA;QAAGqM,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAE0B,OAAO,EAAE,IAAI,CAAC5G;IAAS,CAAC,CACnG;IACD,IAAI+G,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACpN,KAAK,CAAC4E,MAAM,KAAK,EAAE,IAAI,IAAI,CAAC5E,KAAK,CAACN,KAAK,KAAK,IAAI,IAAI,IAAI,CAACM,KAAK,CAAC6E,MAAM,KAAK,IAAI,EAAE;MACrFuI,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACIxO,OAAA;MAAKqM,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CpM,OAAA,CAACP,KAAK;QAACgP,GAAG,EAAGjE,EAAE,IAAM,IAAI,CAACpG,KAAK,GAAGoG;MAAI;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzC3M,OAAA,CAACf,GAAG;QAAAuN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP3M,OAAA;QAAKqM,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCpM,OAAA;UAAAoM,QAAA,EAAKvM,QAAQ,CAAC6O;QAAgB;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,EACL,IAAI,CAACvL,KAAK,CAACP,iBAAiB,KAAK,IAAI,iBAClCb,OAAA;QAAKqM,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCpM,OAAA;UAAIqM,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtEpM,OAAA;YAAIqM,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjEpM,OAAA;cAAKqM,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DpM,OAAA;gBAAIqM,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACpM,OAAA;kBAAGqM,SAAS,EAAC,iBAAiB;kBAACpJ,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAuJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC9M,QAAQ,CAAC8O,SAAS,EAAC,GAAC;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H3M,OAAA,CAACR,QAAQ;gBAAC6M,SAAS,EAAC,QAAQ;gBAACvL,KAAK,EAAE,IAAI,CAACM,KAAK,CAACP,iBAAkB;gBAAC+N,OAAO,EAAE,IAAI,CAAC1H,SAAU;gBAAC2H,QAAQ,EAAE,IAAI,CAACnO,iBAAkB;gBAACoO,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACxE,MAAM;gBAACyE,QAAQ,EAAC;cAAM;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV3M,OAAA;QAAKqM,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBpM,OAAA,CAACd,eAAe;UACZuP,GAAG,EAAGjE,EAAE,IAAM,IAAI,CAACyE,EAAE,GAAGzE,EAAI;UAC5B1J,KAAK,EAAE,IAAI,CAACM,KAAK,CAACqC,OAAQ;UAC1BwJ,MAAM,EAAEA,MAAO;UACflM,OAAO,EAAE,IAAI,CAACK,KAAK,CAACL,OAAQ;UAC5BmO,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTrH,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBnE,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACC,UAAU,CAACwC,KAAM;UACnCF,YAAY,EAAE,IAAI,CAACvC,KAAK,CAACuC,YAAa;UACtCrC,IAAI,EAAE,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,IAAK;UACjCgO,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAErB,YAAa;UAC5BsB,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BvC,aAAa,EAAC,UAAU;UACxBwC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAACvI,cAAe;UAClCwI,SAAS,EAAE,IAAI,CAACxO,KAAK,CAAC8E,iBAAkB;UACxC2J,iBAAiB,EAAGlP,CAAC,IAAK,IAAI,CAACoH,sBAAsB,CAACpH,CAAC,CAAE;UACzDmP,gBAAgB,EAAE,IAAK;UACvBC,kBAAkB,EAAE,IAAI,CAACjI,kBAAmB;UAC5CkI,iBAAiB,EAAEnQ,QAAQ,CAACiI,kBAAmB;UAC/CmI,oBAAoB,EAAE,CAAC,IAAI,CAAC7O,KAAK,CAAC8E,iBAAiB,IAAI,CAAC,IAAI,CAAC9E,KAAK,CAAC8E,iBAAiB,CAACuE,MAAO;UAC5FyF,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAAC/H,UAAW;UACnCgI,gBAAgB,eAAEpQ,OAAA;YAAUqM,SAAS,EAAC,MAAM;YAAC1F,IAAI,EAAC;UAAgB;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/E0D,OAAO,EAAC,QAAQ;UAChBpI,MAAM,EAAE,IAAI,CAACA,MAAO;UACpB3B,SAAS,EAAE,IAAI,CAAClF,KAAK,CAACC,UAAU,CAACiF,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACnF,KAAK,CAACC,UAAU,CAACkF,SAAU;UAC3C2B,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxB1B,OAAO,EAAE,IAAI,CAACpF,KAAK,CAACC,UAAU,CAACmF,OAAQ;UACvC8J,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAQ;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3M,OAAA,CAACL,MAAM;QACH6Q,OAAO,EAAE,IAAI,CAACpP,KAAK,CAAC4D,YAAa;QACjCqI,MAAM,EAAE,IAAI,CAACjM,KAAK,CAACuE,GAAI;QACvB8K,KAAK;QACLpE,SAAS,EAAC,kBAAkB;QAC5BqE,MAAM,EAAExE,kBAAmB;QAC3ByE,MAAM,EAAE,IAAI,CAACnJ,UAAW;QAAA4E,QAAA,eAExBpM,OAAA,CAACb,gBAAgB;UAACmG,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACkE,MAAO;UAAC2B,OAAO,EAAE,IAAI,CAACA,OAAQ;UAAClB,OAAO,EAAE,IAAI,CAAC3E,KAAK,CAAC2E,OAAQ;UAAC6K,KAAK,EAAE;QAAK;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC,eAET3M,OAAA,CAACL,MAAM;QACH6Q,OAAO,EAAE,IAAI,CAACpP,KAAK,CAAC6D,aAAc;QAClCoI,MAAM,EAAExN,QAAQ,CAACgR,MAAO;QACxBJ,KAAK;QACLpE,SAAS,EAAC,kBAAkB;QAC5BqE,MAAM,EAAE9D,mBAAoB;QAC5B+D,MAAM,EAAE,IAAI,CAACrJ,kBAAmB;QAChCwJ,SAAS,EAAE,KAAM;QAAA1E,QAAA,eAEjBpM,OAAA,CAACZ,mBAAmB;UAChBsC,SAAS,EAAE,IAAI,CAACN,KAAK,CAACkE,MAAO;UAC7BA,MAAM,EAAE,IAAI,CAAClE,KAAK,CAAC0D,QAAS;UAC5BrB,OAAO,EAAE,IAAI,CAACrC,KAAK,CAACkE,MAAO;UAC3ByL,MAAM,EAAE;QAAK;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAET3M,OAAA,CAACL,MAAM;QACH6Q,OAAO,EAAE,IAAI,CAACpP,KAAK,CAAC8D,aAAc;QAClCmI,MAAM,EAAExN,QAAQ,CAAC0O,WAAY;QAC7BkC,KAAK;QACLpE,SAAS,EAAC,kBAAkB;QAC5BqE,MAAM,EAAE3D,mBAAoB;QAC5B4D,MAAM,EAAE,IAAI,CAACjJ,kBAAmB;QAAA0E,QAAA,eAEhCpM,OAAA;UAAKqM,SAAS,EAAC,SAAS;UAAAD,QAAA,eACpBpM,OAAA,CAACX,aAAa;YAACiG,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACkE,MAAO;YAAC7B,OAAO,EAAE,IAAI,CAACrC,KAAK,CAACqC;UAAQ;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT3M,OAAA,CAACL,MAAM;QAAC6Q,OAAO,EAAE,IAAI,CAACpP,KAAK,CAACgE,aAAc;QAACiI,MAAM,EAAExN,QAAQ,CAACmR,iBAAkB;QAACP,KAAK;QAACpE,SAAS,EAAC,kBAAkB;QAACsE,MAAM,EAAE,IAAI,CAACxI,iBAAkB;QAACuI,MAAM,EAAE1D,mBAAoB;QAAAZ,QAAA,GACzK,IAAI,CAAChL,KAAK,CAAC0E,SAAS,iBACjB9F,OAAA,CAACT,UAAU;UAAC0R,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhG3M,OAAA;UAAKqM,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DpM,OAAA;YAAIqM,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACpM,OAAA;cAAGqM,SAAS,EAAC,iBAAiB;cAACpJ,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC9M,QAAQ,CAAC8O,SAAS;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH3M,OAAA;YAAAwM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3M,OAAA,CAACR,QAAQ;YAAC6M,SAAS,EAAC,QAAQ;YAACvL,KAAK,EAAE,IAAI,CAACM,KAAK,CAACP,iBAAkB;YAAC+N,OAAO,EAAE,IAAI,CAAC1H,SAAU;YAAC2H,QAAQ,EAAE,IAAI,CAACnO,iBAAkB;YAACoO,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACxE,MAAM;YAACyE,QAAQ,EAAC;UAAM;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT3M,OAAA,CAACJ,OAAO;QAAC4Q,OAAO,EAAE,IAAI,CAACpP,KAAK,CAAC+D,aAAc;QAACiM,QAAQ,EAAC,MAAM;QAACT,MAAM,EAAE,IAAI,CAACtI,WAAY;QAAA+D,QAAA,gBACjFpM,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACgM,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKpM,OAAA;YAAGqM,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C3M,OAAA;YAAIqM,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACpM,OAAA;cAAGqM,SAAS,EAAC,mBAAmB;cAACpJ,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC9M,QAAQ,CAACwR,MAAM;UAAA;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G3M,OAAA,CAACN,MAAM;YAACW,EAAE,EAAC,iBAAiB;YAACgM,SAAS,EAAEmC,WAAY;YAAClC,OAAO,EAAE,IAAI,CAAC3E,KAAM;YAAAyE,QAAA,gBAACpM,OAAA;cAAGqM,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA3M,OAAA;cAAAoM,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN3M,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACgM,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DpM,OAAA;YAAIqM,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACpM,OAAA;cAAGqM,SAAS,EAAC,mBAAmB;cAACpJ,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC9M,QAAQ,CAACwR,MAAM;UAAA;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G3M,OAAA,CAACN,MAAM;YAACW,EAAE,EAAC,kBAAkB;YAACgM,SAAS,EAAEmC,WAAY;YAAClC,OAAO,EAAE,IAAI,CAAC1E,SAAU;YAAAwE,QAAA,gBAACpM,OAAA;cAAGqM,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA3M,OAAA;cAAAoM,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN3M,OAAA;UAAAwM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3M,OAAA,CAACR,QAAQ;UAAC6M,SAAS,EAAC,OAAO;UAACvL,KAAK,EAAE,IAAI,CAACM,KAAK,CAACiF,gBAAiB;UAACuI,OAAO,EAAE,IAAI,CAAC5H,SAAU;UAAC6H,QAAQ,EAAE,IAAI,CAACnI,SAAU;UAACoI,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,mBAAmB;UAACxE,MAAM;UAACyE,QAAQ,EAAC;QAAM;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe1M,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}