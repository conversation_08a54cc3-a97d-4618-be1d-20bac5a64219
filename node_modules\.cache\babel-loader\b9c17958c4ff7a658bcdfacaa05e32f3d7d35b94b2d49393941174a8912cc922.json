{"ast": null, "code": "import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\nexport function canHaveLength() {\n  var arguments_ = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    arguments_[_i] = arguments[_i];\n  }\n  return arguments_.every(function (d) {\n    return is.string(d) || is.array(d) || is.plainObject(d);\n  });\n}\nexport function checkEquality(left, right, value) {\n  if (!isSameType(left, right)) {\n    return false;\n  }\n  if ([left, right].every(is.array)) {\n    return !left.some(hasValue(value)) && right.some(hasValue(value));\n  }\n  /* istanbul ignore else */\n  if ([left, right].every(is.plainObject)) {\n    return !Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value));\n  }\n  return right === value;\n}\nexport function compareNumbers(previousData, data, options) {\n  var actual = options.actual,\n    key = options.key,\n    previous = options.previous,\n    type = options.type;\n  var left = nested(previousData, key);\n  var right = nested(data, key);\n  var changed = [left, right].every(is.number) && (type === 'increased' ? left < right : left > right);\n  if (!is.undefined(actual)) {\n    changed = changed && right === actual;\n  }\n  if (!is.undefined(previous)) {\n    changed = changed && left === previous;\n  }\n  return changed;\n}\nexport function compareValues(previousData, data, options) {\n  var key = options.key,\n    type = options.type,\n    value = options.value;\n  var left = nested(previousData, key);\n  var right = nested(data, key);\n  var primary = type === 'added' ? left : right;\n  var secondary = type === 'added' ? right : left;\n  // console.log({ primary, secondary });\n  if (!is.nullOrUndefined(value)) {\n    if (is.defined(primary)) {\n      // check if nested data matches\n      if (is.array(primary) || is.plainObject(primary)) {\n        return checkEquality(primary, secondary, value);\n      }\n    } else {\n      return equal(secondary, value);\n    }\n    return false;\n  }\n  if ([left, right].every(is.array)) {\n    return !secondary.every(isEqualPredicate(primary));\n  }\n  if ([left, right].every(is.plainObject)) {\n    return hasExtraKeys(Object.keys(primary), Object.keys(secondary));\n  }\n  return ![left, right].every(function (d) {\n    return is.primitive(d) && is.defined(d);\n  }) && (type === 'added' ? !is.defined(left) && is.defined(right) : is.defined(left) && !is.defined(right));\n}\nexport function getIterables(previousData, data, _a) {\n  var _b = _a === void 0 ? {} : _a,\n    key = _b.key;\n  var left = nested(previousData, key);\n  var right = nested(data, key);\n  if (!isSameType(left, right)) {\n    throw new TypeError('Inputs have different types');\n  }\n  if (!canHaveLength(left, right)) {\n    throw new TypeError(\"Inputs don't have length\");\n  }\n  if ([left, right].every(is.plainObject)) {\n    left = Object.keys(left);\n    right = Object.keys(right);\n  }\n  return [left, right];\n}\nexport function hasEntry(input) {\n  return function (_a) {\n    var key = _a[0],\n      value = _a[1];\n    if (is.array(input)) {\n      return equal(input, value) || input.some(function (d) {\n        return equal(d, value) || is.array(value) && isEqualPredicate(value)(d);\n      });\n    }\n    /* istanbul ignore else */\n    if (is.plainObject(input) && input[key]) {\n      return !!input[key] && equal(input[key], value);\n    }\n    return equal(input, value);\n  };\n}\nexport function hasExtraKeys(left, right) {\n  return right.some(function (d) {\n    return !left.includes(d);\n  });\n}\nexport function hasValue(input) {\n  return function (value) {\n    if (is.array(input)) {\n      return input.some(function (d) {\n        return equal(d, value) || is.array(value) && isEqualPredicate(value)(d);\n      });\n    }\n    return equal(input, value);\n  };\n}\nexport function includesOrEqualsTo(previousValue, value) {\n  return is.array(previousValue) ? previousValue.some(function (d) {\n    return equal(d, value);\n  }) : equal(previousValue, value);\n}\nexport function isEqualPredicate(data) {\n  return function (value) {\n    return data.some(function (d) {\n      return equal(d, value);\n    });\n  };\n}\nexport function isSameType() {\n  var arguments_ = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    arguments_[_i] = arguments[_i];\n  }\n  return arguments_.every(is.array) || arguments_.every(is.number) || arguments_.every(is.plainObject) || arguments_.every(is.string);\n}\nexport function nested(data, property) {\n  /* istanbul ignore else */\n  if (is.plainObject(data) || is.array(data)) {\n    /* istanbul ignore else */\n    if (is.string(property)) {\n      var props = property.split('.');\n      return props.reduce(function (acc, d) {\n        return acc && acc[d];\n      }, data);\n    }\n    /* istanbul ignore else */\n    if (is.number(property)) {\n      return data[property];\n    }\n    return data;\n  }\n  return data;\n}", "map": {"version": 3, "names": ["equal", "is", "canHaveLength", "arguments_", "_i", "arguments", "length", "every", "d", "string", "array", "plainObject", "checkEquality", "left", "right", "value", "isSameType", "some", "hasValue", "Object", "entries", "hasEntry", "compareNumbers", "previousData", "data", "options", "actual", "key", "previous", "type", "nested", "changed", "number", "undefined", "compareValues", "primary", "secondary", "nullOrUndefined", "defined", "isEqualPredicate", "hasExtraKeys", "keys", "primitive", "getIterables", "_a", "_b", "TypeError", "input", "includes", "includesOrEqualsTo", "previousValue", "property", "props", "split", "reduce", "acc"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\tree-changes\\src\\helpers.ts"], "sourcesContent": ["import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\n\nimport { CompareValuesOptions, Data, Key, Options, ValidTypes, Value } from './types';\n\nexport function canHaveLength(...arguments_: any): boolean {\n  return arguments_.every((d: unknown) => is.string(d) || is.array(d) || is.plainObject(d));\n}\n\nexport function checkEquality(left: Data, right: Data, value: Value) {\n  if (!isSameType(left, right)) {\n    return false;\n  }\n\n  if ([left, right].every(is.array)) {\n    return !left.some(hasValue(value)) && right.some(hasValue(value));\n  }\n\n  /* istanbul ignore else */\n  if ([left, right].every(is.plainObject)) {\n    return (\n      !Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value))\n    );\n  }\n\n  return right === value;\n}\n\nexport function compareNumbers<K = Key>(\n  previousData: Data,\n  data: Data,\n  options: Options<K>,\n): boolean {\n  const { actual, key, previous, type } = options;\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n\n  let changed =\n    [left, right].every(is.number) && (type === 'increased' ? left < right : left > right);\n\n  if (!is.undefined(actual)) {\n    changed = changed && right === actual;\n  }\n\n  if (!is.undefined(previous)) {\n    changed = changed && left === previous;\n  }\n\n  return changed;\n}\n\nexport function compareValues<K = Key>(\n  previousData: Data,\n  data: Data,\n  options: CompareValuesOptions<K>,\n) {\n  const { key, type, value } = options;\n\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n  const primary = type === 'added' ? left : right;\n  const secondary = type === 'added' ? right : left;\n\n  // console.log({ primary, secondary });\n\n  if (!is.nullOrUndefined(value)) {\n    if (is.defined(primary)) {\n      // check if nested data matches\n      if (is.array(primary) || is.plainObject(primary)) {\n        return checkEquality(primary, secondary, value);\n      }\n    } else {\n      return equal(secondary, value);\n    }\n\n    return false;\n  }\n\n  if ([left, right].every(is.array)) {\n    return !secondary.every(isEqualPredicate(primary));\n  }\n\n  if ([left, right].every(is.plainObject)) {\n    return hasExtraKeys(Object.keys(primary), Object.keys(secondary));\n  }\n\n  return (\n    ![left, right].every(d => is.primitive(d) && is.defined(d)) &&\n    (type === 'added'\n      ? !is.defined(left) && is.defined(right)\n      : is.defined(left) && !is.defined(right))\n  );\n}\n\nexport function getIterables<K = Key>(previousData: Data, data: Data, { key }: Options<K> = {}) {\n  let left = nested(previousData, key);\n  let right = nested(data, key);\n\n  if (!isSameType(left, right)) {\n    throw new TypeError('Inputs have different types');\n  }\n\n  if (!canHaveLength(left, right)) {\n    throw new TypeError(\"Inputs don't have length\");\n  }\n\n  if ([left, right].every(is.plainObject)) {\n    left = Object.keys(left);\n    right = Object.keys(right);\n  }\n\n  return [left, right];\n}\n\nexport function hasEntry(input: Value) {\n  return ([key, value]: [string, Value]) => {\n    if (is.array(input)) {\n      return (\n        equal(input, value) ||\n        input.some(d => equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)))\n      );\n    }\n\n    /* istanbul ignore else */\n    if (is.plainObject(input) && input[key]) {\n      return !!input[key] && equal(input[key], value);\n    }\n\n    return equal(input, value);\n  };\n}\n\nexport function hasExtraKeys(left: string[], right: string[]): boolean {\n  return right.some(d => !left.includes(d));\n}\n\nexport function hasValue(input: Value) {\n  return (value: Value) => {\n    if (is.array(input)) {\n      return input.some(d => equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)));\n    }\n\n    return equal(input, value);\n  };\n}\n\nexport function includesOrEqualsTo<T>(previousValue: T | T[], value: T): boolean {\n  return is.array(previousValue)\n    ? previousValue.some(d => equal(d, value))\n    : equal(previousValue, value);\n}\n\nexport function isEqualPredicate(data: unknown[]) {\n  return (value: unknown) => data.some(d => equal(d, value));\n}\n\nexport function isSameType(...arguments_: ValidTypes[]): boolean {\n  return (\n    arguments_.every(is.array) ||\n    arguments_.every(is.number) ||\n    arguments_.every(is.plainObject) ||\n    arguments_.every(is.string)\n  );\n}\n\nexport function nested<T extends Data, K = Key>(data: T, property?: K) {\n  /* istanbul ignore else */\n  if (is.plainObject(data) || is.array(data)) {\n    /* istanbul ignore else */\n    if (is.string(property)) {\n      const props: Array<any> = property.split('.');\n\n      return props.reduce((acc, d) => acc && acc[d], data);\n    }\n\n    /* istanbul ignore else */\n    if (is.number(property)) {\n      return data[property];\n    }\n\n    return data;\n  }\n\n  return data;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,EAAE,MAAM,SAAS;AAIxB,OAAM,SAAUC,aAAaA,CAAA;EAAC,IAAAC,UAAA;OAAA,IAAAC,EAAA,IAAkB,EAAlBA,EAAA,GAAAC,SAAA,CAAAC,MAAkB,EAAlBF,EAAA,EAAkB;IAAlBD,UAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAC5B,OAAOD,UAAU,CAACI,KAAK,CAAC,UAACC,CAAU;IAAK,OAAAP,EAAE,CAACQ,MAAM,CAACD,CAAC,CAAC,IAAIP,EAAE,CAACS,KAAK,CAACF,CAAC,CAAC,IAAIP,EAAE,CAACU,WAAW,CAACH,CAAC,CAAC;EAAhD,CAAgD,CAAC;AAC3F;AAEA,OAAM,SAAUI,aAAaA,CAACC,IAAU,EAAEC,KAAW,EAAEC,KAAY;EACjE,IAAI,CAACC,UAAU,CAACH,IAAI,EAAEC,KAAK,CAAC,EAAE;IAC5B,OAAO,KAAK;;EAGd,IAAI,CAACD,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAACN,EAAE,CAACS,KAAK,CAAC,EAAE;IACjC,OAAO,CAACG,IAAI,CAACI,IAAI,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC,IAAID,KAAK,CAACG,IAAI,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;;EAGnE;EACA,IAAI,CAACF,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAACN,EAAE,CAACU,WAAW,CAAC,EAAE;IACvC,OACE,CAACQ,MAAM,CAACC,OAAO,CAACP,IAAI,CAAC,CAACI,IAAI,CAACI,QAAQ,CAACN,KAAK,CAAC,CAAC,IAAII,MAAM,CAACC,OAAO,CAACN,KAAK,CAAC,CAACG,IAAI,CAACI,QAAQ,CAACN,KAAK,CAAC,CAAC;;EAI9F,OAAOD,KAAK,KAAKC,KAAK;AACxB;AAEA,OAAM,SAAUO,cAAcA,CAC5BC,YAAkB,EAClBC,IAAU,EACVC,OAAmB;EAEX,IAAAC,MAAM,GAA0BD,OAAO,CAAAC,MAAjC;IAAEC,GAAG,GAAqBF,OAAO,CAAAE,GAA5B;IAAEC,QAAQ,GAAWH,OAAO,CAAAG,QAAlB;IAAEC,IAAI,GAAKJ,OAAO,CAAAI,IAAZ;EACnC,IAAMhB,IAAI,GAAGiB,MAAM,CAACP,YAAY,EAAEI,GAAG,CAAC;EACtC,IAAMb,KAAK,GAAGgB,MAAM,CAACN,IAAI,EAAEG,GAAG,CAAC;EAE/B,IAAII,OAAO,GACT,CAAClB,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAACN,EAAE,CAAC+B,MAAM,CAAC,KAAKH,IAAI,KAAK,WAAW,GAAGhB,IAAI,GAAGC,KAAK,GAAGD,IAAI,GAAGC,KAAK,CAAC;EAExF,IAAI,CAACb,EAAE,CAACgC,SAAS,CAACP,MAAM,CAAC,EAAE;IACzBK,OAAO,GAAGA,OAAO,IAAIjB,KAAK,KAAKY,MAAM;;EAGvC,IAAI,CAACzB,EAAE,CAACgC,SAAS,CAACL,QAAQ,CAAC,EAAE;IAC3BG,OAAO,GAAGA,OAAO,IAAIlB,IAAI,KAAKe,QAAQ;;EAGxC,OAAOG,OAAO;AAChB;AAEA,OAAM,SAAUG,aAAaA,CAC3BX,YAAkB,EAClBC,IAAU,EACVC,OAAgC;EAExB,IAAAE,GAAG,GAAkBF,OAAO,CAAAE,GAAzB;IAAEE,IAAI,GAAYJ,OAAO,CAAAI,IAAnB;IAAEd,KAAK,GAAKU,OAAO,CAAAV,KAAZ;EAExB,IAAMF,IAAI,GAAGiB,MAAM,CAACP,YAAY,EAAEI,GAAG,CAAC;EACtC,IAAMb,KAAK,GAAGgB,MAAM,CAACN,IAAI,EAAEG,GAAG,CAAC;EAC/B,IAAMQ,OAAO,GAAGN,IAAI,KAAK,OAAO,GAAGhB,IAAI,GAAGC,KAAK;EAC/C,IAAMsB,SAAS,GAAGP,IAAI,KAAK,OAAO,GAAGf,KAAK,GAAGD,IAAI;EAEjD;EAEA,IAAI,CAACZ,EAAE,CAACoC,eAAe,CAACtB,KAAK,CAAC,EAAE;IAC9B,IAAId,EAAE,CAACqC,OAAO,CAACH,OAAO,CAAC,EAAE;MACvB;MACA,IAAIlC,EAAE,CAACS,KAAK,CAACyB,OAAO,CAAC,IAAIlC,EAAE,CAACU,WAAW,CAACwB,OAAO,CAAC,EAAE;QAChD,OAAOvB,aAAa,CAACuB,OAAO,EAAEC,SAAS,EAAErB,KAAK,CAAC;;KAElD,MAAM;MACL,OAAOf,KAAK,CAACoC,SAAS,EAAErB,KAAK,CAAC;;IAGhC,OAAO,KAAK;;EAGd,IAAI,CAACF,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAACN,EAAE,CAACS,KAAK,CAAC,EAAE;IACjC,OAAO,CAAC0B,SAAS,CAAC7B,KAAK,CAACgC,gBAAgB,CAACJ,OAAO,CAAC,CAAC;;EAGpD,IAAI,CAACtB,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAACN,EAAE,CAACU,WAAW,CAAC,EAAE;IACvC,OAAO6B,YAAY,CAACrB,MAAM,CAACsB,IAAI,CAACN,OAAO,CAAC,EAAEhB,MAAM,CAACsB,IAAI,CAACL,SAAS,CAAC,CAAC;;EAGnE,OACE,CAAC,CAACvB,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAAC,UAAAC,CAAC;IAAI,OAAAP,EAAE,CAACyC,SAAS,CAAClC,CAAC,CAAC,IAAIP,EAAE,CAACqC,OAAO,CAAC9B,CAAC,CAAC;EAAhC,CAAgC,CAAC,KAC1DqB,IAAI,KAAK,OAAO,GACb,CAAC5B,EAAE,CAACqC,OAAO,CAACzB,IAAI,CAAC,IAAIZ,EAAE,CAACqC,OAAO,CAACxB,KAAK,CAAC,GACtCb,EAAE,CAACqC,OAAO,CAACzB,IAAI,CAAC,IAAI,CAACZ,EAAE,CAACqC,OAAO,CAACxB,KAAK,CAAC,CAAC;AAE/C;AAEA,OAAM,SAAU6B,YAAYA,CAAUpB,YAAkB,EAAEC,IAAU,EAAEoB,EAAwB;MAAxBC,EAAA,GAAAD,EAAA,cAAsB,EAAE,GAAAA,EAAA;IAAtBjB,GAAG,GAAAkB,EAAA,CAAAlB,GAAA;EACzE,IAAId,IAAI,GAAGiB,MAAM,CAACP,YAAY,EAAEI,GAAG,CAAC;EACpC,IAAIb,KAAK,GAAGgB,MAAM,CAACN,IAAI,EAAEG,GAAG,CAAC;EAE7B,IAAI,CAACX,UAAU,CAACH,IAAI,EAAEC,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAIgC,SAAS,CAAC,6BAA6B,CAAC;;EAGpD,IAAI,CAAC5C,aAAa,CAACW,IAAI,EAAEC,KAAK,CAAC,EAAE;IAC/B,MAAM,IAAIgC,SAAS,CAAC,0BAA0B,CAAC;;EAGjD,IAAI,CAACjC,IAAI,EAAEC,KAAK,CAAC,CAACP,KAAK,CAACN,EAAE,CAACU,WAAW,CAAC,EAAE;IACvCE,IAAI,GAAGM,MAAM,CAACsB,IAAI,CAAC5B,IAAI,CAAC;IACxBC,KAAK,GAAGK,MAAM,CAACsB,IAAI,CAAC3B,KAAK,CAAC;;EAG5B,OAAO,CAACD,IAAI,EAAEC,KAAK,CAAC;AACtB;AAEA,OAAM,SAAUO,QAAQA,CAAC0B,KAAY;EACnC,OAAO,UAACH,EAA6B;QAA5BjB,GAAG,GAAAiB,EAAA;MAAE7B,KAAK,GAAA6B,EAAA;IACjB,IAAI3C,EAAE,CAACS,KAAK,CAACqC,KAAK,CAAC,EAAE;MACnB,OACE/C,KAAK,CAAC+C,KAAK,EAAEhC,KAAK,CAAC,IACnBgC,KAAK,CAAC9B,IAAI,CAAC,UAAAT,CAAC;QAAI,OAAAR,KAAK,CAACQ,CAAC,EAAEO,KAAK,CAAC,IAAKd,EAAE,CAACS,KAAK,CAACK,KAAK,CAAC,IAAIwB,gBAAgB,CAACxB,KAAK,CAAC,CAACP,CAAC,CAAE;MAAlE,CAAkE,CAAC;;IAIvF;IACA,IAAIP,EAAE,CAACU,WAAW,CAACoC,KAAK,CAAC,IAAIA,KAAK,CAACpB,GAAG,CAAC,EAAE;MACvC,OAAO,CAAC,CAACoB,KAAK,CAACpB,GAAG,CAAC,IAAI3B,KAAK,CAAC+C,KAAK,CAACpB,GAAG,CAAC,EAAEZ,KAAK,CAAC;;IAGjD,OAAOf,KAAK,CAAC+C,KAAK,EAAEhC,KAAK,CAAC;EAC5B,CAAC;AACH;AAEA,OAAM,SAAUyB,YAAYA,CAAC3B,IAAc,EAAEC,KAAe;EAC1D,OAAOA,KAAK,CAACG,IAAI,CAAC,UAAAT,CAAC;IAAI,QAACK,IAAI,CAACmC,QAAQ,CAACxC,CAAC,CAAC;EAAjB,CAAiB,CAAC;AAC3C;AAEA,OAAM,SAAUU,QAAQA,CAAC6B,KAAY;EACnC,OAAO,UAAChC,KAAY;IAClB,IAAId,EAAE,CAACS,KAAK,CAACqC,KAAK,CAAC,EAAE;MACnB,OAAOA,KAAK,CAAC9B,IAAI,CAAC,UAAAT,CAAC;QAAI,OAAAR,KAAK,CAACQ,CAAC,EAAEO,KAAK,CAAC,IAAKd,EAAE,CAACS,KAAK,CAACK,KAAK,CAAC,IAAIwB,gBAAgB,CAACxB,KAAK,CAAC,CAACP,CAAC,CAAE;MAAlE,CAAkE,CAAC;;IAG5F,OAAOR,KAAK,CAAC+C,KAAK,EAAEhC,KAAK,CAAC;EAC5B,CAAC;AACH;AAEA,OAAM,SAAUkC,kBAAkBA,CAAIC,aAAsB,EAAEnC,KAAQ;EACpE,OAAOd,EAAE,CAACS,KAAK,CAACwC,aAAa,CAAC,GAC1BA,aAAa,CAACjC,IAAI,CAAC,UAAAT,CAAC;IAAI,OAAAR,KAAK,CAACQ,CAAC,EAAEO,KAAK,CAAC;EAAf,CAAe,CAAC,GACxCf,KAAK,CAACkD,aAAa,EAAEnC,KAAK,CAAC;AACjC;AAEA,OAAM,SAAUwB,gBAAgBA,CAACf,IAAe;EAC9C,OAAO,UAACT,KAAc;IAAK,OAAAS,IAAI,CAACP,IAAI,CAAC,UAAAT,CAAC;MAAI,OAAAR,KAAK,CAACQ,CAAC,EAAEO,KAAK,CAAC;IAAf,CAAe,CAAC;EAA/B,CAA+B;AAC5D;AAEA,OAAM,SAAUC,UAAUA,CAAA;EAAC,IAAAb,UAAA;OAAA,IAAAC,EAAA,IAA2B,EAA3BA,EAAA,GAAAC,SAAA,CAAAC,MAA2B,EAA3BF,EAAA,EAA2B;IAA3BD,UAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACzB,OACED,UAAU,CAACI,KAAK,CAACN,EAAE,CAACS,KAAK,CAAC,IAC1BP,UAAU,CAACI,KAAK,CAACN,EAAE,CAAC+B,MAAM,CAAC,IAC3B7B,UAAU,CAACI,KAAK,CAACN,EAAE,CAACU,WAAW,CAAC,IAChCR,UAAU,CAACI,KAAK,CAACN,EAAE,CAACQ,MAAM,CAAC;AAE/B;AAEA,OAAM,SAAUqB,MAAMA,CAA0BN,IAAO,EAAE2B,QAAY;EACnE;EACA,IAAIlD,EAAE,CAACU,WAAW,CAACa,IAAI,CAAC,IAAIvB,EAAE,CAACS,KAAK,CAACc,IAAI,CAAC,EAAE;IAC1C;IACA,IAAIvB,EAAE,CAACQ,MAAM,CAAC0C,QAAQ,CAAC,EAAE;MACvB,IAAMC,KAAK,GAAeD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;MAE7C,OAAOD,KAAK,CAACE,MAAM,CAAC,UAACC,GAAG,EAAE/C,CAAC;QAAK,OAAA+C,GAAG,IAAIA,GAAG,CAAC/C,CAAC,CAAC;MAAb,CAAa,EAAEgB,IAAI,CAAC;;IAGtD;IACA,IAAIvB,EAAE,CAAC+B,MAAM,CAACmB,QAAQ,CAAC,EAAE;MACvB,OAAO3B,IAAI,CAAC2B,QAAQ,CAAC;;IAGvB,OAAO3B,IAAI;;EAGb,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}