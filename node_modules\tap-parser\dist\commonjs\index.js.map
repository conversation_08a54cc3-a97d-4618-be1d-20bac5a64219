{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;AAAA,mCAAqC;AACrC,wDAA2B;AAE3B,yDAAiD;AACjD,iDAAoD;AACpD,6DAAqD;AACrD,uCAAgC;AAChC,2CAA8C;AAI9C,uDAAiD;AAAxC,gHAAA,YAAY,OAAA;AACrB,+CAAoD;AAA3C,wGAAA,QAAQ,OAAA;AAAE,yGAAA,SAAS,OAAA;AAE5B,2DAAqD;AAA5C,oHAAA,cAAc,OAAA;AAEvB,qCAAgC;AAAvB,+FAAA,IAAI,OAAA;AACb,yCAAoC;AAA3B,mGAAA,MAAM,OAAA;AAMf,6CAA+C;AAG/C,2CAAmC;AAoBnC,iCAAiC;AAEjC,MAAa,MACX,SAAQ,qBAAY;IAGpB,MAAM,GAAkB,IAAI,CAAA;IAC5B,QAAQ,GAAkB,IAAI,CAAA;IAC9B,WAAW,GAAuB,EAAE,CAAA;IACpC,WAAW,GAAkB,IAAI,CAAA;IACjC,SAAS,GAAY,KAAK,CAAA;IAC1B,cAAc,GAAkB,IAAI,CAAA;IACpC,QAAQ,GAAW,EAAE,CAAA;IACrB,KAAK,GAAW,EAAE,CAAA;IAClB,WAAW,GAAY,KAAK,CAAA;IAE5B,OAAO,GAAY,KAAK,CAAA;IACxB,IAAI,GAAY,KAAK,CAAA;IACrB,SAAS,GAAqB,KAAK,CAAA;IACnC,WAAW,GAAqB,KAAK,CAAA;IACrC,UAAU,GAAW,CAAC,CAAA;IACtB,MAAM,GAAW,EAAE,CAAA;IACnB,QAAQ,CAAS;IACjB,iBAAiB,GAAkB,IAAI,CAAA;IACvC,QAAQ,GAAa,EAAE,CAAA;IACvB,KAAK,GAAW,CAAC,CAAA;IACjB,IAAI,GAAW,CAAC,CAAA;IAChB,QAAQ,GAAe,EAAE,CAAA;IACzB,KAAK,GAAyC,EAAE,CAAA;IAChD,KAAK,GAAyC,EAAE,CAAA;IAChD,KAAK,CAAQ;IACb,IAAI,CAAQ;IACZ,EAAE,GAAY,IAAI,CAAA;IAClB,WAAW,CAAS;IACpB,MAAM,GAAkB,IAAI,CAAA;IAC5B,IAAI,GAAW,CAAC,CAAA;IAChB,MAAM,CAAiB;IACvB,WAAW,GAAW,EAAE,CAAA;IACxB,OAAO,GAAW,CAAC,CAAC,CAAA;IACpB,SAAS,GAAW,CAAC,CAAC,CAAA;IACtB,UAAU,GAAwB,IAAI,GAAG,EAAE,CAAA;IAC3C,OAAO,CAAS;IAChB,kBAAkB,CAAS;IAC3B,OAAO,GAAwB,IAAI,CAAA;IACnC,IAAI,CAAQ;IACZ,IAAI,GAAW,CAAC,CAAA;IAChB,MAAM,CAAS;IACf,gBAAgB,GAAY,KAAK,CAAA;IACjC,aAAa,GAAY,KAAK,CAAA;IAC9B,IAAI,GAAkB,IAAI,CAAA;IAC1B,IAAI,GAAW,CAAC,CAAA;IAEhB,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,iBAAiB,CAAA;IAC/B,CAAC;IACD,IAAI,gBAAgB,CAAC,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAA;QAC5B,IAAI,GAAG;YAAE,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAA;IACtC,CAAC;IAED,qBAAqB;IACrB,IAAI,QAAQ;QACV,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAA;IACb,CAAC;IAQD,YACE,OAA0D,EAC1D,UAA2C;QAE3C,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YAClC,UAAU,GAAG,OAAO,CAAA;YACpB,OAAO,GAAG,EAAmB,CAAA;QAC/B,CAAC;QAED,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;QACvB,KAAK,EAAE,CAAA;QAEP,IAAI,UAAU;YAAE,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QAE/C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAA;QAC9B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAA;QACpC,IAAI,CAAC,gBAAgB;YACnB,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAA;QACnD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;QACvD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,CAAA;QAE/B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAA;QAC1B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,WAAW,CAAA;QACxC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAA;QAClC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,KAAK,CAAA;QAE7D,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAA;QAC9B,IAAI,CAAC,OAAO,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;IACxC,CAAC;IAED,IAAI,QAAQ;QACV,MAAM,CAAC,GAAa,EAAE,CAAA;QACtB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;QAC/C,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;QACnC,IAAI,EAAE;YAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAClB,IAAI,EAAE;YAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAClB,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACtB,CAAC;IAED,QAAQ,CACN,KAA+D,EAC/D,IAAY;QAEZ,IAAI,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACjC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAA;QACf,IAAI,CAAC,IAAI,EAAE,CAAA;QACX,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG;gBACN,QAAQ,EAAE,KAAK;aAChB,CAAA;QACH,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAED,cAAc,CAAC,SAA2B,EAAE,IAAY;QACtD,0DAA0D;QAC1D,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,UAAU,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAE1B,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;QAC1B,MAAM,GAAG,GAAG,IAAI,kBAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAEvC,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACnC,MAAM,aAAa,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAA;YAC7C,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;YAC5C,IAAI,aAAa,IAAI,cAAc,EAAE,CAAC;gBACpC,IAAI,aAAa;oBAAE,GAAG,CAAC,QAAQ,GAAG,yBAAyB,CAAA;;oBACtD,GAAG,CAAC,QAAQ,GAAG,0BAA0B,CAAA;gBAC9C,GAAG,CAAC,IAAI,GAAG,IAAI,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;gBACjD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YAC1B,CAAC;QACH,CAAC;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YACzC,GAAG,CAAC,QAAQ;gBACV,gBAAgB,GAAG,KAAK,GAAG,yBAAyB,CAAA;YACtD,qBAAqB;YACrB,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAA;YAClD,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAC1B,CAAC;aAAM,IAAI,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBAC/B,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAA;YACpC,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,kEAAkE;YAClE,+CAA+C;YAC/C,IAAI,IAAI,CAAC,SAAS;gBAAE,OAAM;QAC5B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAEvB,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAA;QAEvD,yDAAyD;QACzD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,IAAY,EAAE,UAAmB,KAAK;QAC3C,IAAI,IAAI,CAAC,WAAW,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAM;QAE1D,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,GAAG,GAAG;gBACV,QAAQ,EAAE,yCAAyC;gBACnD,IAAI,EAAE,IAAI;aACX,CAAA;YACD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACxB,IAAI,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAClD,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,OAAO;YACV,IAAI;iBACD,KAAK,CAAC,IAAI,CAAC;iBACX,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACZ,OAAO,CAAC,IAAI,CAAC,EAAE;gBACd,IAAI,IAAI,IAAI,CAAA;gBACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM;oBAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAA;;oBAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YAC9B,CAAC,CAAC,CAAA;QAEN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IAED,SAAS,CAAC,IAAY,EAAE,YAAqB,KAAK;QAChD,IAAI,IAAI,CAAC,MAAM;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,CACnB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,EACrD,IAAI,CACL,CAAA;aACE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,IAAI,CAAC,KAAa,EAAE,GAAW,EAAE,OAAe,EAAE,IAAY;QAC5D,yCAAyC;QACzC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACjB,OAAM;QACR,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACjB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAE1B,0DAA0D;QAC1D,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,MAAM;gBACb,IAAI,CAAC,QAAQ,CACX;oBACE,QAAQ,EAAE,yCAAyC;oBACnD,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;iBACrB,EACD,IAAI,CACL,CAAA;;gBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACtB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;QAClB,MAAM,CAAC,GAAS,IAAI,cAAI,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;QAC7C,IAAI,CAAC,CAAC,OAAO;YAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,GAAG,OAAO,CAAA;QAErD,iEAAiE;QACjE,mEAAmE;QACnE,gEAAgE;QAChE,kBAAkB;QAClB,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAA;YACtB,KAAK,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;gBAClD,MAAM,QAAQ,GACZ,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,yBAAyB;oBACtC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,0BAA0B;wBACvC,CAAC,CAAC,IAAI,CAAA;gBACR,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;oBAClB,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAA;oBACvB,GAAG,CAAC,IAAI,GAAG,IAAI,cAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;oBAC/B,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;gBAC1B,CAAC;YACH,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QACvB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;IACtB,CAAC;IAED,YAAY;QACV,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;QACf,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;IACpB,CAAC;IAED,gEAAgE;IAChE,WAAW;QACT,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAA;QACxD,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,WAAW,CAAC,IAAY;QACtB,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;YAClC,qBAAqB;YACrB,IAAI,CAAC,cAAc,EAAE,CAAA;QACvB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAA;QACvB,CAAC;IACH,CAAC;IAED,cAAc;QACZ,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CACb,oDAAoD,CACrD,CAAA;QACH,CAAC;QACD,oBAAoB;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC7B,IAAI,CAAC,YAAY,EAAE,CAAA;QAEnB,IAAI,KAAU,CAAA;QACd,IAAI,CAAC;YACH,KAAK,GAAG,kBAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAC7B,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CACT,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EACrD,IAAI,CACL,CAAA;YACD,OAAM;QACR,CAAC;QAED,IACE,OAAO,KAAK,CAAC,WAAW,KAAK,QAAQ;YACrC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,EAC3B,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,WAAW,CAAA;YACtC,OAAO,KAAK,CAAC,WAAW,CAAA;QAC1B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAA;QAC1B,4DAA4D;QAC5D,wCAAwC;IAC1C,CAAC;IAeD,KAAK,CACH,KAAmC,EACnC,QAAkD,EAClD,EAAyB;QAEzB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IACE,OAAO,QAAQ,KAAK,QAAQ;YAC5B,QAAQ,KAAK,MAAM;YACnB,OAAO,KAAK,KAAK,QAAQ,EACzB,CAAC;YACD,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QACtC,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAChC,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QAED,IAAI,CAAC,MAAM,IAAI,KAAK,CAAA;QACpB,GAAG,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC3C,IAAI,CAAC,KAAK;gBAAE,MAAK;YAEjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;YACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QACtB,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAC;QAE5B,IAAI,EAAE;YAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAE5B,OAAO,IAAI,CAAA;IACb,CAAC;IAYD,GAAG,CACD,KAA6D,EAC7D,QAAkD,EAClD,EAAyB;QAEzB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACzC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACnC,EAAE,GAAG,QAAQ,CAAA;gBACb,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAC7B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAClB,CAAC;QAED,wDAAwD;QACxD,IAAI,IAAI,CAAC,QAAQ;YAAE,IAAI,CAAC,WAAW,EAAE,CAAA;QAErC,IAAI,CAAC,UAAU,EAAE,CAAA;QAEjB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAA;YAC7B,MAAM,MAAM,GACV,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAA;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC,CAAA;QAChD,CAAC;QAED,IAAI,OAAO,GAAY,KAAK,CAAA;QAE5B,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,GAAG,IAAI,CAAA;YACd,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;gBACrB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAA;YAChB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAA;YAChE,CAAC;QACH,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACpD,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;gBACzB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;oBAClB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;gBAClB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;wBACtB,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAA;oBACtC,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,EAAE,yBAAyB,CAAC,CAAA;gBAC9D,CAAC;gBACD,OAAO,GAAG,IAAI,CAAA;YAChB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC;aAAM,IACL,IAAI,CAAC,EAAE;YACP,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,EAChD,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAA;QAChD,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;QAC1B,IAAI,EAAE;YAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAE5B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,YAAY,CAAC,OAAgB;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,+BAAY,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;YAE9D,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;gBACjB,qDAAqD;gBACrD,8DAA8D;gBAC9D,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC;oBAC/B,IAAI,CAAC,WAAW,CACd,aAAa;wBACX,GAAG,CAAC,KAAK;wBACT,YAAY;wBACZ,GAAG,CAAC,IAAI,CAAC,GAAG;wBACZ,GAAG,EACL,KAAK,EACL,IAAI,CACL,CAAA;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpB,CAAC;IACH,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,IAAY;QACnC,0DAA0D;QAC1D,IACE,OAAO,IAAI,EAAE;YACb,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC;YACrB,IAAI,CAAC,KAAK,KAAK,CAAC;YAChB,CAAC,IAAI,CAAC,QAAQ,EACd,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAC/B,CAAC;;YAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,MAAM,CAAC,GAAW,EAAE,KAAc,EAAE,IAAY;QAC9C,8CAA8C;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACjB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAC1B,iDAAiD;QACjD,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACrB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAA;IACjC,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,YAAqB,KAAK;QAChD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAA;QAEjC,IAAI,IAAI,CAAC,WAAW;YAAE,OAAM;QAE5B,+DAA+D;QAC/D,2DAA2D;QAC3D,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,IAAI,CAAA;QAEjC,IAAI,CAAC,SAAS;YAAE,IAAI,CAAC,UAAU,EAAE,CAAA;;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QAEzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAA;QACjC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAA;QACf,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,wCAAwC;YACxC,IAAI,IAAI,GAAG,WAAW,CAAA;YACtB,IAAI,MAAM;gBAAE,IAAI,IAAI,GAAG,GAAG,MAAM,CAAA;YAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,CAAA;QAChC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;QAC5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,GAAG,EAAE,CAAA;YACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC;IACH,CAAC;IAED,eAAe;QACb,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACrB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,IAAI,IAAI,CAAA;YAC7D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAA;YACjC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA;YACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACpB,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAE1B,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,IAAI,CAAC,YAAY,EAAE,CAAA;QAEnB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC,eAAe,EAAE,CAAA;QAEjD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QAEpB,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,GAAG,CAAA;QAC1B,IAAI,IAAI;YAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC,CAAA;QAC3C,IAAI,IAAI;YAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC,CAAA;QAC3C,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,EAAE,GAAG,KAAK,CAAA;gBACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;QAED,IAAI,GAAG,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,EAAE,CAAA;QACzB,IAAI,GAAG,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,EAAE,CAAA;QAEzB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;QACpB,IACE,IAAI,CAAC,IAAI;YACT,CAAC,GAAG,CAAC,EAAE;YACP,CAAC,GAAG,CAAC,IAAI;YACT,CAAC,GAAG,CAAC,IAAI;YACT,CAAC,IAAI,CAAC,WAAW,EACjB,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAClD,IAAI,CAAS,CAAA;YACb,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM;gBAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;YAC/C,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAA;QAC9C,CAAC;QACD,IAAI,CAAC,eAAe,EAAE,CAAA;IACxB,CAAC;IAED,kEAAkE;IAClE,iEAAiE;IACjE,kEAAkE;IAClE,+DAA+D;IAC/D,qDAAqD;IACrD,UAAU,CAAC,IAAY;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAA;QAC7D,MAAM,cAAc,GAAG,CAAC,aAAa,IAAI,IAAI,CAAC,WAAW,CAAA;QACzD,MAAM,YAAY,GAChB,CAAC,aAAa;YACd,CAAC,cAAc;YACf,wBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEpC,oEAAoE;QACpE,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAAC,aAAa;YAAE,IAAI,CAAC,UAAU,EAAE,CAAA;QAErC,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAE1B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC;YACrB,QAAQ,EAAE,aAAa,IAAI,SAAS;YACpC,gBAAgB,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,SAAS;YAC/D,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAAE,IAAI,CAAC,EAAE,GAAG,KAAK,CAAA;QAClC,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,kBAAkB;gBAAE,CAAC,GAAG,MAAM,GAAG,CAAC,CAAA;YACvD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;QACtB,CAAC,CAAC,CAAA;QAEF,yDAAyD;QACzD,kEAAkE;QAClE,8CAA8C;QAC9C,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACxB,IAAI,cAAsB,CAAA;QAC1B,IAAI,YAAY,EAAE,CAAC;YACjB,cAAc,GAAG,IAAI,CAAA;YACrB,IAAI,GAAG,EAAE,CAAA;QACX,CAAC;aAAM,IAAI,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1C,cAAc,GAAG,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAA;QAC5D,CAAC;aAAM,CAAC;YACN,cAAc,GAAG,IAAI,CAAC,WAAW,IAAI,aAAa,CAAA;QACpD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,cAAc;aAC9B,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC;aAC/B,IAAI,EAAE,CAAA;QAET,wDAAwD;QACxD,6DAA6D;QAC7D,qDAAqD;QACrD,uCAAuC;QACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;QAC5D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAC/B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAC7C,IAAI,IAAI;YAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,OAAO,CAAC,EAAU;QAChB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;IAC7B,CAAC;IAED,KAAK,CAAC,UAAkB,EAAE,EAAE,KAAW;QACrC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAA;YAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YACjC,KAAK,GAAG,IAAI,CAAA;YACZ,IAAI,CAAC;gBAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAC5B,CAAC;QAED,IAAI,IAAI,GAAW,EAAE,CAAA;QACrB,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,IAAI,GAAG,kBAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAA;gBACtC,qBAAqB;YACvB,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;YACf,oBAAoB;QACtB,CAAC;QAED,MAAM,CAAC,GACL,IAAI,CAAC,CAAC;YACJ,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;YAC3D,CAAC,CAAC,IAAI,CAAA;QAER,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAEzD,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC3D,mCAAmC;YACnC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACnB,OAAM;QACR,CAAC;QAED,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAA;QAC7C,IAAI,KAAK,GAAG,WAAW,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO,GAAG,IAAI,GAAG,CAAC,CAAA;QAExD,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC;YAAE,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,CAAA;QAElD,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;QACrD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAAC,GAAG,EAAE,CAAA;IACZ,CAAC;IAED,UAAU,CAAC,GAAW;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QAExB,6CAA6C;QAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,EAAE,CAAC;YACN,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IACE,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;gBACnB,CAAC,CAAC,OAAO;gBACT,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,CAAC,CAAC,OAAO,CAAC,KAAK;gBACf,CAAC,GAAG,CAAC,IAAI;gBACT,CAAC,GAAG,CAAC,IAAI,EACT,CAAC;gBACD,6BAA6B;gBAC7B,OAAM;YACR,CAAC;QACH,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC7B,IAAI,GAAG,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;aACpC,IAAI,GAAG,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;aACzC,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;;YACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAClC,CAAC;IAED,WAAW,CACT,IAAY,EACZ,WAAoB,KAAK,EACzB,cAAuB,KAAK;QAE5B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,IAAI,GAAG,IAAI,GAAG,IAAI,CAAA;QAErD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;YAAE,IAAI,IAAI,IAAI,CAAA;QAEzC,6BAA6B;QAC7B,qBAAqB;QACrB,IAAI,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAAE,OAAM;QAC7D,oBAAoB;QAEpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxB,MAAM,GAAG,GAAG,IAAA,mCAAc,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;QAC/D,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ;YACxD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QAEpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC7C,8CAA8C;YAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAA;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ;gBAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAY;QAChB,yBAAyB;QACzB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAElC,2DAA2D;QAC3D,2DAA2D;QAC3D,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM;gBAAE,IAAI,GAAG,MAAM,GAAG,IAAI,CAAA;iBAChC,IAAI,IAAI,CAAC,KAAK;gBAAE,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QAC/C,CAAC;QAED,kEAAkE;QAClE,kEAAkE;QAClE,sEAAsE;QACtE,mEAAmE;QACnE,mCAAmC;QACnC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;gBAAE,OAAM;iBAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU;gBACxD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAA;;gBAC1B,OAAM;QACb,CAAC;QAED,+DAA+D;QAC/D,+DAA+D;QAC/D,iCAAiC;QACjC,IACE,IAAI,CAAC,WAAW;YAChB,wBAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5B,CAAC,IAAI,CAAC,KAAK,EACX,CAAC;YACD,OAAM;QACR,CAAC;QAED,wCAAwC;QACxC,0DAA0D;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACpC,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;YACjC,OAAM;QACR,CAAC;QAED,gEAAgE;QAChE,gEAAgE;QAChE,iEAAiE;QACjE,+DAA+D;QAC/D,kEAAkE;QAElE,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAA;YACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YACvB,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,OAAM;QACR,CAAC;QAED,mDAAmD;QACnD,MAAM,SAAS,GACb,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,CAAA;QACtD,IAAI,IAAI,KAAK,IAAI;YAAE,OAAO,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAE9D,oCAAoC;QACpC,IACE,IAAI,CAAC,QAAQ;YACb,IAAI,KAAK,KAAK;YACd,IAAI,CAAC,QAAQ,CAAC,IAAI;YAClB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACvB,CAAC,IAAI,CAAC,MAAM,EACZ,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YACvB,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;YAC7B,OAAM;QACR,CAAC;QAED,6DAA6D;QAC7D,qCAAqC;QACrC,MAAM,IAAI,GAAG,IAAA,uBAAQ,EAAC,IAAI,CAAC,CAAA;QAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACjB,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACtB,OAAM;QACR,CAAC;QAED,qEAAqE;QACrE,sEAAsE;QACtE,mEAAmE;QACnE,aAAa;QACb,IAAI,IAAI,CAAC,KAAK;YAAE,IAAI,CAAC,WAAW,EAAE,CAAA;QAElC,6DAA6D;QAC7D,6CAA6C;QAC7C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACzB,CAAC;QAED,sDAAsD;QACtD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACjB,OAAM;QACR,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;YAC9B,IAAI,CAAC,OAAO,CAAC,IAAA,iBAAK,EAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;YAC/B,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YACzB,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAChC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,CAAA;YAC9C,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YACvB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;YACpD,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YACpB,IAAI,CAAC,IAAI,CACP,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAChB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAChB,IAAA,iBAAK,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,EAC3B,IAAI,CACL,CAAA;YACD,OAAM;QACR,CAAC;QAED,6DAA6D;QAC7D,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;YAC5B,gEAAgE;YAChE,2DAA2D;YAC3D,+DAA+D;YAC/D,8DAA8D;YAC9D,+DAA+D;YAC/D,gEAAgE;YAChE,gEAAgE;YAChE,8DAA8D;YAC9D,2DAA2D;YAC3D,kDAAkD;YAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YAClC,OAAM;QACR,CAAC;QAED,6DAA6D;QAC7D,8DAA8D;QAC9D,gDAAgD;QAChD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAC1B,gDAAgD;YAChD,oBAAoB;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,qBAAqB;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC/C,CAAC;QACD,oBAAoB;IACtB,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,MAAc;QACtC,gDAAgD;QAChD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;YACnD,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACvB,OAAM;QACR,CAAC;QAED,UAAU;QACV,0BAA0B;QAC1B,wBAAwB;QACxB,sBAAsB;QACtB,+DAA+D;QAC/D,kCAAkC;QAClC,0BAA0B;QAE1B,+BAA+B;QAC/B,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;gBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;gBACtB,OAAM;YACR,CAAC;iBAAM,CAAC;gBACN,iDAAiD;gBACjD,iEAAiE;gBACjE,6DAA6D;gBAC7D,IAAI,CAAC,WAAW,EAAE,CAAA;YACpB,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,GAAG,OAAO,EAAE,CAAC;YAC9D,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YACvB,OAAM;QACR,CAAC;QAED,8DAA8D;QAC9D,+DAA+D;QAC/D,6DAA6D;QAC7D,4CAA4C;QAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;YACpC,IACE,IAAI,CAAC,WAAW;gBAChB,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACzC,wBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAClC,CAAC;gBACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;gBACrB,OAAM;YACR,CAAC;YAED,gEAAgE;YAChE,+DAA+D;YAC/D,kCAAkC;YAClC,EAAE;YACF,qEAAqE;YACrE,gEAAgE;YAChE,gEAAgE;YAChE,gEAAgE;YAChE,iEAAiE;YACjE,oEAAoE;YACpE,qEAAqE;YACrE,oEAAoE;YACpE,mCAAmC;YACnC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBACjC,kCAAkC;gBAClC,MAAM,IAAI,GAAG,IAAA,uBAAQ,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,IAAI,IAAI,EAAE,CAAC;oBACT,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;wBAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;wBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;oBACxB,CAAC;yBAAM,CAAC;wBACN,sDAAsD;wBACtD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;oBACvB,CAAC;oBACD,OAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,gEAAgE;QAEhE,IAAI,wBAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACtB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,GAAW,EAAE,UAAyB,EAAE;QACnD,OAAO,IAAA,kBAAK,EAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAC5B,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,GAAa,EAAE,OAAO,GAAG,EAAE;QAC1C,OAAO,IAAA,sBAAS,EAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAChC,CAAC;CACF;AArhCD,wBAqhCC", "sourcesContent": ["import { EventEmitter } from 'events'\nimport yaml from 'tap-yaml'\n\nimport { FinalResults } from './final-results.js'\nimport { lineType, lineTypes } from './line-type.js'\nimport { parseDirective } from './parse-directive.js'\nimport { Plan } from './plan.js'\nimport { Result, TapError } from './result.js'\n\n/* c8 ignore start */\nexport type { FinalPlan } from './final-plan.js'\nexport { FinalResults } from './final-results.js'\nexport { lineType, lineTypes } from './line-type.js'\nexport type { ParsedLine } from './line-type.js'\nexport { parseDirective } from './parse-directive.js'\nexport type { Directive } from './parse-directive.js'\nexport { Plan } from './plan.js'\nexport { Result } from './result.js'\nexport type { TapError } from './result.js'\nexport type { EventLog }\n/* c8 ignore stop */\n\nimport type etoa from 'events-to-array'\nimport { parse, stringify } from './statics.js'\ntype EventLog = ReturnType<typeof etoa>\n\nimport { unesc } from './escape.js'\n\nexport interface ParserOptions {\n  name?: string\n  passes?: boolean\n  parent?: Parser\n  level?: number\n  closingTestPoint?: Result\n  bail?: boolean\n  omitVersion?: boolean\n  buffered?: boolean\n  preserveWhitespace?: boolean\n  strict?: boolean\n  flat?: boolean\n}\n\nexport interface Pragmas {\n  [pragma: string]: boolean\n}\n\n// TODO: declare event signatures\n\nexport class Parser\n  extends EventEmitter\n  implements NodeJS.WritableStream\n{\n  #child: Parser | null = null\n  #current: Result | null = null\n  #extraQueue: [string, string][] = []\n  #maybeChild: string | null = null\n  #postPlan: boolean = false\n  #previousChild: Parser | null = null\n  #yamlish: string = ''\n  #yind: string = ''\n  #sawVersion: boolean = false\n\n  aborted: boolean = false\n  bail: boolean = false\n  bailedOut: boolean | string = false\n  #bailingOut: boolean | string = false\n  braceLevel: number = 0\n  buffer: string = ''\n  buffered: boolean\n  #closingTestPoint: Result | null = null\n  comments: string[] = []\n  count: number = 0\n  fail: number = 0\n  failures: TapError[] = []\n  skips: (Result & { skip: string | true })[] = []\n  todos: (Result & { todo: string | true })[] = []\n  level: number\n  name: string\n  ok: boolean = true\n  omitVersion: boolean\n  parent: Parser | null = null\n  pass: number = 0\n  passes: Result[] | null\n  planComment: string = ''\n  planEnd: number = -1\n  planStart: number = -1\n  pointsSeen: Map<number, Result> = new Map()\n  pragmas: Pragmas\n  preserveWhitespace: boolean\n  results: FinalResults | null = null\n  root: Parser\n  skip: number = 0\n  strict: boolean\n  syntheticBailout: boolean = false\n  syntheticPlan: boolean = false\n  time: number | null = null\n  todo: number = 0\n\n  get closingTestPoint() {\n    return this.#closingTestPoint\n  }\n  set closingTestPoint(res) {\n    this.#closingTestPoint = res\n    if (res) res.closingTestPoint = true\n  }\n\n  /* c8 ignore start */\n  get readable(): false {\n    return false\n  }\n  get writable(): true {\n    return true\n  }\n  /* c8 ignore stop */\n\n  constructor(onComplete?: (results: FinalResults) => any)\n  constructor(\n    options?: ParserOptions,\n    onComplete?: (results: FinalResults) => any,\n  )\n  constructor(\n    options?: ParserOptions | ((results: FinalResults) => any),\n    onComplete?: (results: FinalResults) => any,\n  ) {\n    if (typeof options === 'function') {\n      onComplete = options\n      options = {} as ParserOptions\n    }\n\n    options = options || {}\n    super()\n\n    if (onComplete) this.on('complete', onComplete)\n\n    this.name = options.name || ''\n    this.parent = options.parent || null\n    this.closingTestPoint =\n      (this.parent && options.closingTestPoint) || null\n    this.root = options.parent ? options.parent.root : this\n    this.passes = options.passes ? [] : null\n    this.level = options.level || 0\n\n    this.bail = !!options.bail\n    this.omitVersion = !!options.omitVersion\n    this.buffered = !!options.buffered\n    this.preserveWhitespace = options.preserveWhitespace || false\n\n    this.strict = !!options.strict\n    this.pragmas = { strict: this.strict }\n  }\n\n  get fullname(): string {\n    const n: string[] = []\n    const pn = (this.parent?.fullname ?? '').trim()\n    const mn = (this.name || '').trim()\n    if (pn) n.push(pn)\n    if (mn) n.push(mn)\n    return n.join(' > ')\n  }\n\n  tapError(\n    error: Result | { tapError: string; [k: string]: any } | string,\n    line: string,\n  ) {\n    if (line) this.emit('line', line)\n    this.ok = false\n    this.fail++\n    if (typeof error === 'string') {\n      error = {\n        tapError: error,\n      }\n    }\n    this.failures.push(error)\n  }\n\n  parseTestPoint(testPoint: RegExpMatchArray, line: string) {\n    // need to hold off on this when we have a child so we can\n    // associate the closing test point with the test.\n    if (!this.#child) this.emitResult()\n\n    if (this.bailedOut) return\n\n    const resId = testPoint[2]\n    const res = new Result(testPoint, this)\n\n    if (resId && this.planStart !== -1) {\n      const lessThanStart = res.id < this.planStart\n      const greaterThanEnd = res.id > this.planEnd\n      if (lessThanStart || greaterThanEnd) {\n        if (lessThanStart) res.tapError = 'id less than plan start'\n        else res.tapError = 'id greater than plan end'\n        res.plan = new Plan(this.planStart, this.planEnd)\n        this.tapError(res, line)\n      }\n    }\n\n    if (resId && this.pointsSeen.has(res.id)) {\n      res.tapError =\n        'test point id ' + resId + ' appears multiple times'\n      /* c8 ignore start */\n      res.previous = this.pointsSeen.get(res.id) || null\n      /* c8 ignore stop */\n      this.tapError(res, line)\n    } else if (resId) {\n      this.pointsSeen.set(res.id, res)\n    }\n\n    if (this.#child) {\n      if (!this.#child.closingTestPoint)\n        this.#child.closingTestPoint = res\n      this.emitResult()\n      // can only bail out here in the case of a child with broken diags\n      // anything else would have bailed out already.\n      if (this.bailedOut) return\n    }\n\n    this.emit('line', line)\n\n    if (!res.skip && !res.todo) this.ok = this.ok && res.ok\n\n    // hold onto it, because we might get yamlish diagnostics\n    this.#current = res\n  }\n\n  nonTap(data: string, didLine: boolean = false) {\n    if (this.#bailingOut && /^( {4})*\\}\\n$/.test(data)) return\n\n    if (this.strict) {\n      const err = {\n        tapError: 'Non-TAP data encountered in strict mode',\n        data: data,\n      }\n      this.tapError(err, data)\n      if (this.parent) this.parent.tapError(err, data)\n    }\n\n    // emit each line, then the extra as a whole\n    if (!didLine)\n      data\n        .split('\\n')\n        .slice(0, -1)\n        .forEach(line => {\n          line += '\\n'\n          if (this.#current || this.#extraQueue.length)\n            this.#extraQueue.push(['line', line])\n          else this.emit('line', line)\n        })\n\n    this.emitExtra(data)\n  }\n\n  emitExtra(data: string, fromChild: boolean = false) {\n    if (this.parent)\n      this.parent.emitExtra(\n        data.replace(/\\n$/, '').replace(/^/gm, '    ') + '\\n',\n        true,\n      )\n    else if (!fromChild && (this.#current || this.#extraQueue.length))\n      this.#extraQueue.push(['extra', data])\n    else this.emit('extra', data)\n  }\n\n  plan(start: number, end: number, comment: string, line: string) {\n    // not allowed to have more than one plan\n    if (this.planStart !== -1) {\n      this.nonTap(line)\n      return\n    }\n\n    // can't put a plan in a child.\n    if (this.#child || this.#yind) {\n      this.nonTap(line)\n      return\n    }\n\n    this.emitResult()\n    if (this.bailedOut) return\n\n    // 1..0 is a special case. Otherwise, end must be >= start\n    if (end < start && end !== 0 && start !== 1) {\n      if (this.strict)\n        this.tapError(\n          {\n            tapError: 'plan end cannot be less than plan start',\n            plan: { start, end },\n          },\n          line,\n        )\n      else this.nonTap(line)\n      return\n    }\n\n    this.planStart = start\n    this.planEnd = end\n    const p: Plan = new Plan(start, end, comment)\n    if (p.comment) this.planComment = p.comment = comment\n\n    // This means that the plan is coming at the END of all the tests\n    // Plans MUST be either at the beginning or the very end.  We treat\n    // plans like '1..0' the same, since they indicate that no tests\n    // will be coming.\n    if (this.count !== 0 || this.planEnd === 0) {\n      const seen = new Set()\n      for (const [id, res] of this.pointsSeen.entries()) {\n        const tapError =\n          id < start ? 'id less than plan start'\n          : id > end ? 'id greater than plan end'\n          : null\n        if (tapError) {\n          seen.add(tapError)\n          res.tapError = tapError\n          res.plan = new Plan(start, end)\n          this.tapError(res, line)\n        }\n      }\n      this.#postPlan = true\n    }\n\n    this.emit('line', line)\n    this.emit('plan', p)\n  }\n\n  resetYamlish() {\n    this.#yind = ''\n    this.#yamlish = ''\n  }\n\n  // that moment when you realize it's not what you thought it was\n  yamlGarbage() {\n    const yamlGarbage = this.#yind + '---\\n' + this.#yamlish\n    this.emitResult()\n    if (this.bailedOut) return\n    this.nonTap(yamlGarbage, true)\n  }\n\n  yamlishLine(line: string) {\n    if (line === this.#yind + '...\\n') {\n      // end the yaml block\n      this.processYamlish()\n    } else {\n      this.#yamlish += line\n    }\n  }\n\n  processYamlish() {\n    /* c8 ignore start */\n    if (!this.#current) {\n      throw new Error(\n        'called processYamlish without a current test point',\n      )\n    }\n    /* c8 ignore stop */\n    const yamlish = this.#yamlish\n    this.resetYamlish()\n\n    let diags: any\n    try {\n      diags = yaml.parse(yamlish)\n    } catch (er) {\n      this.nonTap(\n        this.#yind + '---\\n' + yamlish + this.#yind + '...\\n',\n        true,\n      )\n      return\n    }\n\n    if (\n      typeof diags.duration_ms === 'number' &&\n      this.#current.time === null\n    ) {\n      this.#current.time = diags.duration_ms\n      delete diags.duration_ms\n    }\n\n    this.#current.diag = diags\n    // we still don't emit the result here yet, to support diags\n    // that come ahead of buffered subtests.\n  }\n\n  write(\n    chunk: string | Uint8Array | Buffer,\n    cb?: (...x: any[]) => any,\n  ): boolean\n  write(\n    chunk: string | Uint8Array | Buffer,\n    encoding?: BufferEncoding,\n  ): boolean\n  write(\n    chunk: string | Uint8Array | Buffer,\n    encoding?: BufferEncoding,\n    cb?: (...x: any[]) => any,\n  ): boolean\n  write(\n    chunk: string | Uint8Array | Buffer,\n    encoding?: BufferEncoding | ((...a: any[]) => any),\n    cb?: (...x: any[]) => any,\n  ): boolean {\n    if (this.aborted) {\n      return false\n    }\n\n    if (\n      typeof encoding === 'string' &&\n      encoding !== 'utf8' &&\n      typeof chunk === 'string'\n    ) {\n      chunk = Buffer.from(chunk, encoding)\n    }\n\n    if (Buffer.isBuffer(chunk)) {\n      chunk = chunk.toString('utf8')\n    }\n\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n\n    this.buffer += chunk\n    do {\n      const match = this.buffer.match(/^.*\\r?\\n/)\n      if (!match) break\n\n      this.buffer = this.buffer.substring(match[0].length)\n      this.parse(match[0])\n    } while (this.buffer.length)\n\n    if (cb) process.nextTick(cb)\n\n    return true\n  }\n\n  end(\n    chunk?: string | Buffer | Uint8Array,\n    encoding?: BufferEncoding,\n    cb?: (...a: any[]) => any,\n  ): this\n  end(\n    chunk?: string | Buffer | Uint8Array,\n    cb?: (...a: any[]) => any,\n  ): this\n  end(cb?: (...a: any[]) => any): this\n  end(\n    chunk?: string | Buffer | Uint8Array | ((...a: any[]) => any),\n    encoding?: BufferEncoding | ((...a: any[]) => any),\n    cb?: (...a: any[]) => any,\n  ): this {\n    if (chunk && typeof chunk !== 'function') {\n      if (typeof encoding === 'function') {\n        cb = encoding\n        this.write(chunk)\n      } else {\n        this.write(chunk, encoding)\n      }\n    }\n\n    if (this.buffer) {\n      this.write('\\n')\n    }\n\n    // if we have yamlish, means we didn't finish with a ...\n    if (this.#yamlish) this.yamlGarbage()\n\n    this.emitResult()\n\n    if (this.syntheticBailout && this.level === 0) {\n      this.syntheticBailout = false\n      const reason =\n        this.bailedOut === true ? '' : ' ' + this.bailedOut\n      this.emit('line', 'Bail out!' + reason + '\\n')\n    }\n\n    let skipAll: boolean = false\n\n    if (this.planEnd === 0 && this.planStart === 1) {\n      skipAll = true\n      if (this.count === 0) {\n        this.ok = true\n      } else {\n        this.tapError('Plan of 1..0, but test points encountered', '')\n      }\n    } else if (!this.bailedOut && this.planStart === -1) {\n      if (this.count === 0 && !this.syntheticPlan) {\n        this.syntheticPlan = true\n        if (this.buffered) {\n          this.planStart = 1\n          this.planEnd = 0\n        } else {\n          if (!this.#sawVersion) {\n            this.version(14, 'TAP version 14\\n')\n          }\n          this.plan(1, 0, 'no tests found', '1..0 # no tests found\\n')\n        }\n        skipAll = true\n      } else {\n        this.tapError('no plan', '')\n      }\n    } else if (\n      this.ok &&\n      this.count !== this.planEnd - this.planStart + 1\n    ) {\n      this.tapError('incorrect number of tests', '')\n    }\n\n    this.emitComplete(skipAll)\n    if (cb) process.nextTick(cb)\n\n    return this\n  }\n\n  emitComplete(skipAll: boolean) {\n    if (!this.results) {\n      const res = (this.results = new FinalResults(!!skipAll, this))\n\n      if (!res.bailout) {\n        // comment a bit at the end so we know what happened.\n        // but don't repeat these comments if they're already present.\n        if (res.plan.end !== res.count) {\n          this.emitComment(\n            'test count(' +\n              res.count +\n              ') != plan(' +\n              res.plan.end +\n              ')',\n            false,\n            true,\n          )\n        }\n      }\n\n      this.emit('complete', this.results)\n      this.emit('finish')\n      this.emit('close')\n    }\n  }\n\n  version(version: number, line: string) {\n    // If version is specified, must be at the very beginning.\n    if (\n      version >= 13 &&\n      this.planStart === -1 &&\n      this.count === 0 &&\n      !this.#current\n    ) {\n      this.#sawVersion = true\n      this.emit('line', line)\n      this.emit('version', version)\n    } else this.nonTap(line)\n  }\n\n  pragma(key: string, value: boolean, line: string) {\n    // can't put a pragma in a child or yaml block\n    if (this.#child) {\n      this.nonTap(line)\n      return\n    }\n\n    this.emitResult()\n    if (this.bailedOut) return\n    // only the 'strict' pragma is currently relevant\n    if (key === 'strict') {\n      this.strict = value\n    }\n    this.pragmas[key] = value\n    this.emit('line', line)\n    this.emit('pragma', key, value)\n  }\n\n  bailout(reason: string, synthetic: boolean = false) {\n    this.syntheticBailout = synthetic\n\n    if (this.#bailingOut) return\n\n    // Guard because emitting a result can trigger a forced bailout\n    // if the harness decides that failures should be bailouts.\n    this.#bailingOut = reason || true\n\n    if (!synthetic) this.emitResult()\n    else this.#current = null\n\n    this.bailedOut = this.#bailingOut\n    this.ok = false\n    if (!synthetic) {\n      // synthetic bailouts get emitted on end\n      let line = 'Bail out!'\n      if (reason) line += ' ' + reason\n      this.emit('line', line + '\\n')\n    }\n    this.emit('bailout', reason)\n    if (this.parent) {\n      this.end()\n      this.parent.bailout(reason, true)\n    }\n  }\n\n  clearExtraQueue() {\n    for (const [ev, data] of this.#extraQueue) {\n      this.emit(ev, data)\n    }\n    this.#extraQueue.length = 0\n  }\n\n  endChild() {\n    if (this.#child && (!this.#bailingOut || this.#child.count)) {\n      this.#child.time = this.#child.closingTestPoint?.time || null\n      this.#previousChild = this.#child\n      this.#child.end()\n      this.#child = null\n    }\n  }\n\n  emitResult() {\n    if (this.bailedOut) return\n\n    this.endChild()\n    this.resetYamlish()\n\n    if (!this.#current) return this.clearExtraQueue()\n\n    const res = this.#current\n    this.#current = null\n\n    this.count++\n    const { skip, todo } = res\n    if (skip) this.skips.push({ ...res, skip })\n    if (todo) this.todos.push({ ...res, todo })\n    if (res.ok) {\n      this.pass++\n      if (!skip && !todo && this.passes) this.passes.push(res)\n    } else {\n      this.fail++\n      if (!res.todo && !res.skip) {\n        this.ok = false\n        this.failures.push(res)\n      }\n    }\n\n    if (res.skip) this.skip++\n    if (res.todo) this.todo++\n\n    this.emitAssert(res)\n    if (\n      this.bail &&\n      !res.ok &&\n      !res.todo &&\n      !res.skip &&\n      !this.#bailingOut\n    ) {\n      this.#maybeChild = null\n      const ind = new Array(this.level + 1).join('    ')\n      let p: Parser\n      for (p = this; p.parent; p = p.parent);\n      const bailName = res.name ? ' ' + res.name : ''\n      p.parse(ind + 'Bail out!' + bailName + '\\n')\n    }\n    this.clearExtraQueue()\n  }\n\n  // TODO: We COULD say that any \"relevant tap\" line that's indented\n  // by 4 spaces starts a child test, and just call it 'unnamed' if\n  // it does not have a prefix comment.  In that case, any number of\n  // 4-space indents can be plucked off to try to find a relevant\n  // TAP line type, and if so, start the unnamed child.\n  startChild(line: string) {\n    const maybeBuffered = this.#current && this.#current.buffered\n    const unindentStream = !maybeBuffered && this.#maybeChild\n    const indentStream =\n      !maybeBuffered &&\n      !unindentStream &&\n      lineTypes.subtestIndent.test(line)\n\n    // If we have any other result waiting in the wings, we need to emit\n    // that now.  A buffered test emits its test point at the *end* of\n    // the child subtest block, so as to match streamed test semantics.\n    if (!maybeBuffered) this.emitResult()\n\n    if (this.bailedOut) return\n\n    this.#child = new Parser({\n      bail: this.bail,\n      parent: this,\n      level: this.level + 1,\n      buffered: maybeBuffered || undefined,\n      closingTestPoint: (maybeBuffered && this.#current) || undefined,\n      preserveWhitespace: this.preserveWhitespace,\n      omitVersion: true,\n      strict: this.strict,\n    })\n\n    this.#child.on('complete', results => {\n      if (!results.ok) this.ok = false\n    })\n\n    this.#child.on('line', l => {\n      if (l.trim() || this.preserveWhitespace) l = '    ' + l\n      this.emit('line', l)\n    })\n\n    // Canonicalize the parsing result of any kind of subtest\n    // if it's a buffered subtest or a non-indented Subtest directive,\n    // then synthetically emit the Subtest comment\n    line = line.substring(4)\n    let subtestComment: string\n    if (indentStream) {\n      subtestComment = line\n      line = ''\n    } else if (maybeBuffered && this.#current) {\n      subtestComment = '# Subtest: ' + this.#current.name + '\\n'\n    } else {\n      subtestComment = this.#maybeChild || '# Subtest\\n'\n    }\n\n    this.#maybeChild = null\n    this.#child.name = subtestComment\n      .substring('# Subtest: '.length)\n      .trim()\n\n    // at some point, we may wish to move 100% to preferring\n    // the Subtest comment on the parent level.  If so, uncomment\n    // this line, and remove the child.emitComment below.\n    // this.emit('comment', subtestComment)\n    if (!this.#child.buffered) this.emit('line', subtestComment)\n    this.emit('child', this.#child)\n    this.#child.emitComment(subtestComment, true)\n    if (line) this.#child.parse(line)\n  }\n\n  destroy(er?: Error) {\n    this.abort('destroyed', er)\n  }\n\n  abort(message: string = '', extra?: any) {\n    if (this.#child) {\n      const b = this.#child.buffered\n      this.#child.abort(message, extra)\n      extra = null\n      if (b) this.write('\\n}\\n')\n    }\n\n    let dump: string = ''\n    if (extra && Object.keys(extra).length) {\n      try {\n        dump = yaml.stringify(extra).trimEnd()\n        /* c8 ignore start */\n      } catch (er) {}\n      /* c8 ignore stop */\n    }\n\n    const y: string =\n      dump ?\n        '  ---\\n  ' + dump.split('\\n').join('\\n  ') + '\\n  ...\\n'\n      : '\\n'\n\n    const n = (this.count || 0) + 1 + (this.#current ? 1 : 0)\n\n    if (this.planEnd !== -1 && this.planEnd < n && this.parent) {\n      // skip it, let the parent do this.\n      this.aborted = true\n      return\n    }\n\n    message = message.replace(/[\\n\\r\\s\\t]/g, ' ')\n    let point = '\\nnot ok ' + n + ' - ' + message + '\\n' + y\n\n    if (this.planEnd === -1) point += '1..' + n + '\\n'\n\n    if (!this.#sawVersion) this.write('TAP version 14\\n')\n    this.write(point)\n    this.aborted = true\n    this.end()\n  }\n\n  emitAssert(res: Result) {\n    this.emit('assert', res)\n\n    // see if we need to surface to the top level\n    const c = this.#child || this.#previousChild\n    if (c) {\n      this.#previousChild = null\n      if (\n        res.name === c.name &&\n        c.results &&\n        res.ok === c.results.ok &&\n        c.results.count &&\n        !res.todo &&\n        !res.skip\n      ) {\n        // just procedural, ignore it\n        return\n      }\n    }\n\n    // surface result to the top level parser\n    this.root.emit('result', res)\n    if (res.skip) this.root.emit('skip', res)\n    else if (res.todo) this.root.emit('todo', res)\n    else if (!res.ok) this.root.emit('fail', res)\n    else this.root.emit('pass', res)\n  }\n\n  emitComment(\n    line: string,\n    skipLine: boolean = false,\n    noDuplicate: boolean = false,\n  ) {\n    if (line.trim().charAt(0) !== '#') line = '# ' + line\n\n    if (line.slice(-1) !== '\\n') line += '\\n'\n\n    // XXX: is this still needed?\n    /* c8 ignore start */\n    if (noDuplicate && this.comments.indexOf(line) !== -1) return\n    /* c8 ignore stop */\n\n    this.comments.push(line)\n    const dir = parseDirective(line.replace(/^\\s*#\\s*/, '').trim())\n    if (dir && dir[0] === 'time' && typeof dir[1] === 'number')\n      this.time = dir[1]\n\n    if (this.#current || this.#extraQueue.length) {\n      // no way to get here with skipLine being true\n      this.#extraQueue.push(['line', line])\n      this.#extraQueue.push(['comment', line])\n    } else {\n      if (!skipLine) this.emit('line', line)\n      this.emit('comment', line)\n    }\n  }\n\n  parse(line: string) {\n    // normalize line endings\n    line = line.replace(/\\r\\n$/, '\\n')\n\n    // sometimes empty lines get trimmed, but are still part of\n    // a subtest or a yaml block.  Otherwise, nothing to parse!\n    if (line === '\\n') {\n      if (this.#child) line = '    ' + line\n      else if (this.#yind) line = this.#yind + line\n    }\n\n    // If we're bailing out, then the only thing we want to see is the\n    // end of a buffered child test.  Anything else should be ignored.\n    // But!  if we're bailing out a nested child, and ANOTHER nested child\n    // comes after that one, then we don't want the second child's } to\n    // also show up, or it looks weird.\n    if (this.#bailingOut) {\n      if (!/^\\s*}\\n$/.test(line)) return\n      else if (!this.braceLevel || line.length < this.braceLevel)\n        this.braceLevel = line.length\n      else return\n    }\n\n    // This allows omitting even parsing the version if the test is\n    // an indented child test.  Several parsers get upset when they\n    // see an indented version field.\n    if (\n      this.omitVersion &&\n      lineTypes.version.test(line) &&\n      !this.#yind\n    ) {\n      return\n    }\n\n    // check to see if the line is indented.\n    // if it is, then it's either a subtest, yaml, or garbage.\n    const indent = line.match(/^[ \\t]*/)\n    if (indent && indent[0]) {\n      this.parseIndent(line, indent[0])\n      return\n    }\n\n    // In any case where we're going to emitResult, that can trigger\n    // a bailout, so we need to only emit the line once we know that\n    // isn't happening, to prevent cases where there's a bailout, and\n    // then one more line of output.  That'll also prevent the case\n    // where the test point is emitted AFTER the line that follows it.\n\n    // buffered subtests must end with a }\n    if (this.#child && this.#child.buffered && line === '}\\n') {\n      this.endChild()\n      this.emit('line', line)\n      this.emitResult()\n      return\n    }\n\n    // just a \\n, emit only if we care about whitespace\n    const validLine =\n      this.preserveWhitespace || line.trim() || this.#yind\n    if (line === '\\n') return validLine && this.emit('line', line)\n\n    // buffered subtest with diagnostics\n    if (\n      this.#current &&\n      line === '{\\n' &&\n      this.#current.diag &&\n      !this.#current.buffered &&\n      !this.#child\n    ) {\n      this.emit('line', line)\n      this.#current.buffered = true\n      return\n    }\n\n    // now we know it's not indented, so if it's either valid tap\n    // or garbage.  Get the type of line.\n    const type = lineType(line)\n    if (!type) {\n      this.nonTap(line)\n      return\n    }\n\n    if (type[0] === 'comment') {\n      this.emitComment(line)\n      return\n    }\n\n    // if we have any yamlish, it's garbage now.  We tolerate non-TAP and\n    // comments in the midst of yaml (though, perhaps, that's questionable\n    // behavior), but any actual TAP means that the yaml block was just\n    // not valid.\n    if (this.#yind) this.yamlGarbage()\n\n    // If it's anything other than a comment or garbage, then any\n    // maybeChild is just an unsatisfied promise.\n    if (this.#maybeChild) {\n      this.emitComment(this.#maybeChild)\n      this.#maybeChild = null\n    }\n\n    // nothing but comments can come after a trailing plan\n    if (this.#postPlan) {\n      this.nonTap(line)\n      return\n    }\n\n    // ok, now it's maybe a thing\n    if (type[0] === 'bailout') {\n      const msg = type[1]?.[1] || ''\n      this.bailout(unesc(msg), false)\n      return\n    }\n\n    if (type[0] === 'pragma') {\n      const [_, posNeg, key] = type[1]\n      this.pragma(String(key), posNeg === '+', line)\n      return\n    }\n\n    if (type[0] === 'version') {\n      const version = type[1]\n      this.version(parseInt(String(version[1]), 10), line)\n      return\n    }\n\n    if (type[0] === 'plan') {\n      const plan = type[1]\n      this.plan(\n        +String(plan[1]),\n        +String(plan[2]),\n        unesc(plan[3] || '').trim(),\n        line,\n      )\n      return\n    }\n\n    // streamed subtests will end when this test point is emitted\n    if (type[0] === 'testPoint') {\n      // note: it's weird, but possible, to have a testpoint ending in\n      // { before a streamed subtest which ends with a test point\n      // instead of a }.  In this case, the parser gets confused, but\n      // also, even beginning to handle that means doing a much more\n      // involved multi-line parse.  By that point, the subtest block\n      // has already been emitted as a 'child' event, so it's too late\n      // to really do the optimal thing.  The only way around would be\n      // to buffer up everything and do a multi-line parse.  This is\n      // rare and weird, and a multi-line parse would be a bigger\n      // rewrite, so I'm allowing it as it currently is.\n      this.parseTestPoint(type[1], line)\n      return\n    }\n\n    // We already detected nontap up above, so the only case left\n    // should be a `# Subtest:` comment.  Ignore for coverage, but\n    // include the error here just for good measure.\n    if (type[0] === 'subtest') {\n      // this is potentially a subtest.  Not indented.\n      // hold until later.\n      this.#maybeChild = line\n      /* c8 ignore start */\n    } else {\n      throw new Error('Unhandled case: ' + type[0])\n    }\n    /* c8 ignore stop */\n  }\n\n  parseIndent(line: string, indent: string) {\n    // still belongs to the child, so pass it along.\n    if (this.#child && line.substring(0, 4) === '    ') {\n      line = line.substring(4)\n      this.#child.write(line)\n      return\n    }\n\n    // one of:\n    // - continuing yaml block\n    // - starting yaml block\n    // - ending yaml block\n    // - body of a new child subtest that was previously introduced\n    // - An indented subtest directive\n    // - A comment, or garbage\n\n    // continuing/ending yaml block\n    if (this.#yind) {\n      if (line.indexOf(this.#yind) === 0) {\n        this.emit('line', line)\n        this.yamlishLine(line)\n        return\n      } else {\n        // oops!  that was not actually yamlish, I guess.\n        // this is a case where the indent is shortened mid-yamlish block\n        // treat existing yaml as garbage, continue parsing this line\n        this.yamlGarbage()\n      }\n    }\n\n    // start a yaml block under a test point\n    if (this.#current && !this.#yind && line === indent + '---\\n') {\n      this.#yind = indent\n      this.emit('line', line)\n      return\n    }\n\n    // at this point, not yamlish, and not an existing child test.\n    // We may have already seen an unindented Subtest directive, or\n    // a test point that ended in { indicating a buffered subtest\n    // Child tests are always indented 4 spaces.\n    if (line.substring(0, 4) === '    ') {\n      if (\n        this.#maybeChild ||\n        (this.#current && this.#current.buffered) ||\n        lineTypes.subtestIndent.test(line)\n      ) {\n        this.startChild(line)\n        return\n      }\n\n      // It's _something_ indented, if the indentation is divisible by\n      // 4 spaces, and the result is actual TAP of some sort, then do\n      // a child subtest for it as well.\n      //\n      // This will lead to some ambiguity in cases where there are multiple\n      // levels of non-signaled subtests, but a Subtest comment in the\n      // middle of them, which may or may not be considered \"indented\"\n      // See the subtest-no-comment-mid-comment fixture for an example\n      // of this.  As it happens, the preference is towards an indented\n      // Subtest comment as the interpretation, which is the only possible\n      // way to resolve this, since otherwise there's no way to distinguish\n      // between an anonymous subtest with a non-indented Subtest comment,\n      // and an indented Subtest comment.\n      const s = line.match(/( {4})+(.*\\n)$/)\n      if (s && s[2]?.charAt(0) !== ' ') {\n        // integer number of indentations.\n        const type = lineType(String(s[2]))\n        if (type) {\n          if (type[0] === 'comment') {\n            this.emit('line', line)\n            this.emitComment(line)\n          } else {\n            // it's relevant!  start as an \"unnamed\" child subtest\n            this.startChild(line)\n          }\n          return\n        }\n      }\n    }\n\n    // at this point, it's either a non-subtest comment, or garbage.\n\n    if (lineTypes.comment.test(line)) {\n      this.emitComment(line)\n      return\n    }\n\n    this.nonTap(line)\n  }\n\n  static parse(str: string, options: ParserOptions = {}): EventLog {\n    return parse(str, options)\n  }\n\n  static stringify(msg: EventLog, options = {}): string {\n    return stringify(msg, options)\n  }\n}\n"]}