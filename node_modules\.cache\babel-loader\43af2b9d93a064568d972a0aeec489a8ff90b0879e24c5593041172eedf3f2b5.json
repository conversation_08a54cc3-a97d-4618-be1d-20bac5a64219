{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport classNames from 'classnames';\nimport Element from './Element';\nimport { ConfigContext } from '../config-provider';\nvar SkeletonButton = function SkeletonButton(props) {\n  var _classNames;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    _props$block = props.block,\n    block = _props$block === void 0 ? false : _props$block;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-button\")\n  }, otherProps)));\n};\nSkeletonButton.defaultProps = {\n  size: 'default'\n};\nexport default SkeletonButton;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "omit", "classNames", "Element", "ConfigContext", "SkeletonButton", "props", "_classNames", "customizePrefixCls", "prefixCls", "className", "active", "_props$block", "block", "_React$useContext", "useContext", "getPrefixCls", "otherProps", "cls", "concat", "createElement", "defaultProps", "size"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Button.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport classNames from 'classnames';\nimport Element from './Element';\nimport { ConfigContext } from '../config-provider';\n\nvar SkeletonButton = function SkeletonButton(props) {\n  var _classNames;\n\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      active = props.active,\n      _props$block = props.block,\n      block = _props$block === void 0 ? false : _props$block;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-button\")\n  }, otherProps)));\n};\n\nSkeletonButton.defaultProps = {\n  size: 'default'\n};\nexport default SkeletonButton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,aAAa,QAAQ,oBAAoB;AAElD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,WAAW;EAEf,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACpCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,YAAY,GAAGN,KAAK,CAACO,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;EAE1D,IAAIE,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACX,aAAa,CAAC;IACnDY,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIP,SAAS,GAAGO,YAAY,CAAC,UAAU,EAAER,kBAAkB,CAAC;EAC5D,IAAIS,UAAU,GAAGhB,IAAI,CAACK,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC3C,IAAIY,GAAG,GAAGhB,UAAU,CAACO,SAAS,EAAE,EAAE,CAACU,MAAM,CAACV,SAAS,EAAE,UAAU,CAAC,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAER,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACY,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,EAAEZ,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACY,MAAM,CAACV,SAAS,EAAE,QAAQ,CAAC,EAAEI,KAAK,CAAC,EAAEN,WAAW,GAAGG,SAAS,CAAC;EACzP,OAAO,aAAaV,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7CV,SAAS,EAAEQ;EACb,CAAC,EAAE,aAAalB,KAAK,CAACoB,aAAa,CAACjB,OAAO,EAAEL,QAAQ,CAAC;IACpDW,SAAS,EAAE,EAAE,CAACU,MAAM,CAACV,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEQ,UAAU,CAAC,CAAC,CAAC;AAClB,CAAC;AAEDZ,cAAc,CAACgB,YAAY,GAAG;EAC5BC,IAAI,EAAE;AACR,CAAC;AACD,eAAejB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}