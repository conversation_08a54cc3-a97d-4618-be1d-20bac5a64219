{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RevolvingDot = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar RevolvingDot = function RevolvingDot(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    version: \"1.1\",\n    width: props.width,\n    height: props.height,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    x: \"0px\",\n    y: \"0px\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: \"none\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    cx: \"50\",\n    cy: \"50\",\n    r: props.radius + 38,\n    style: {\n      opacity: 0.5\n    }\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: props.color,\n    stroke: props.color,\n    strokeWidth: \"3\",\n    cx: \"8\",\n    cy: \"54\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"2s\",\n    type: \"rotate\",\n    from: \"0 50 48\",\n    to: \"360 50 52\",\n    repeatCount: \"indefinite\"\n  })));\n};\nexports.RevolvingDot = RevolvingDot;\nRevolvingDot.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nRevolvingDot.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 6\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "RevolvingDot", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "version", "width", "height", "xmlns", "x", "y", "label", "fill", "stroke", "color", "strokeWidth", "cx", "cy", "r", "radius", "style", "opacity", "attributeName", "dur", "type", "from", "to", "repeatCount", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/RevolvingDot.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RevolvingDot = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar RevolvingDot = function RevolvingDot(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    version: \"1.1\",\n    width: props.width,\n    height: props.height,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    x: \"0px\",\n    y: \"0px\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: \"none\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    cx: \"50\",\n    cy: \"50\",\n    r: props.radius + 38,\n    style: {\n      opacity: 0.5\n    }\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: props.color,\n    stroke: props.color,\n    strokeWidth: \"3\",\n    cx: \"8\",\n    cy: \"54\",\n    r: props.radius\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"2s\",\n    type: \"rotate\",\n    from: \"0 50 48\",\n    to: \"360 50 52\",\n    repeatCount: \"indefinite\"\n  })));\n};\n\nexports.RevolvingDot = RevolvingDot;\nRevolvingDot.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nRevolvingDot.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 6\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAE7B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,KAAK,EAAE;EAC9C,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAEH,KAAK,CAACG,KAAK;IAClBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,CAAC,EAAE,KAAK;IACRC,CAAC,EAAE,KAAK;IACR,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDQ,IAAI,EAAE,MAAM;IACZC,MAAM,EAAEV,KAAK,CAACW,KAAK;IACnBC,WAAW,EAAE,GAAG;IAChBC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB,MAAM,GAAG,EAAE;IACpBC,KAAK,EAAE;MACLC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,EAAE,aAAaxB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACzDQ,IAAI,EAAET,KAAK,CAACW,KAAK;IACjBD,MAAM,EAAEV,KAAK,CAACW,KAAK;IACnBC,WAAW,EAAE,GAAG;IAChBC,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB;EACX,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClEkB,aAAa,EAAE,WAAW;IAC1BC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAEDjC,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCA,YAAY,CAACgC,SAAS,GAAG;EACvBrB,MAAM,EAAEP,UAAU,CAAC,SAAS,CAAC,CAAC6B,SAAS,CAAC,CAAC7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAAC;EACrGzB,KAAK,EAAEN,UAAU,CAAC,SAAS,CAAC,CAAC6B,SAAS,CAAC,CAAC7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAAC;EACpGjB,KAAK,EAAEd,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM;EACnCnB,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM;EACnCX,MAAM,EAAEnB,UAAU,CAAC,SAAS,CAAC,CAAC+B;AAChC,CAAC;AACDnC,YAAY,CAACoC,YAAY,GAAG;EAC1BzB,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTQ,KAAK,EAAE,OAAO;EACdH,KAAK,EAAE,eAAe;EACtBQ,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}