{"ast": null, "code": "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar String = global.String;\nvar TypeError = global.TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw TypeError(String(argument) + ' is not an object');\n};", "map": {"version": 3, "names": ["global", "require", "isObject", "String", "TypeError", "module", "exports", "argument"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/an-object.js"], "sourcesContent": ["var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar String = global.String;\nvar TypeError = global.TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw TypeError(String(argument) + ' is not an object');\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIE,MAAM,GAAGH,MAAM,CAACG,MAAM;AAC1B,IAAIC,SAAS,GAAGJ,MAAM,CAACI,SAAS;;AAEhC;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIL,QAAQ,CAACK,QAAQ,CAAC,EAAE,OAAOA,QAAQ;EACvC,MAAMH,SAAS,CAACD,MAAM,CAACI,QAAQ,CAAC,GAAG,mBAAmB,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}