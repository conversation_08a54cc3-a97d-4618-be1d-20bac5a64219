{"ast": null, "code": "var global = require('../internals/global');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar Object = global.Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};", "map": {"version": 3, "names": ["global", "require", "requireObjectCoercible", "Object", "module", "exports", "argument"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/to-object.js"], "sourcesContent": ["var global = require('../internals/global');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar Object = global.Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,uCAAuC,CAAC;AAE7E,IAAIE,MAAM,GAAGH,MAAM,CAACG,MAAM;;AAE1B;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,OAAOH,MAAM,CAACD,sBAAsB,CAACI,QAAQ,CAAC,CAAC;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}