# Winet E-Procurement - Frontend

Sistema di e-procurement per il settore food & beverage sviluppato in React.

## 📋 Panoramica

**Winet E-Procurement** è un'applicazione web completa per la gestione di processi di approvvigionamento nel settore alimentare e delle bevande. Il sistema supporta multiple tipologie di utenti con dashboard e funzionalità specifiche per ogni ruolo.

### 🎯 Caratteristiche Principali

- **Multi-Role Architecture**: 11 ruoli utente distinti (Admin, Distributore, Affiliato, Agente, PDV, etc.)
- **Dashboard Personalizzate**: Interfacce specifiche per ogni tipologia di utente
- **E-commerce Integrato**: Sistema di carrello e marketplace per ordini
- **Gestione Magazzino**: Controllo inventario, movimentazioni, lotti e scadenze
- **Sistema Logistico**: Gestione consegne, tracking e ottimizzazione percorsi
- **Analytics Avanzate**: Reporting e previsioni di vendita
- **Integrazione Sistemi Esterni**: Connessioni con Alyante, MrWine e altri
- **Responsive Design**: Ottimizzato per desktop, tablet e mobile
- **Internazionalizzazione**: Supporto multilingua (IT/EN)
- **🆕 Gestione PDV Autonoma**: Gli agenti possono creare e gestire autonomamente i propri PDV
- **🆕 Sistema Notifiche Real-time**: WebSocket integration per notifiche live
- **🆕 Feature Flags Avanzati**: Sistema licenze con controllo accesso funzionalità
- **🔍 Lookup P.IVA Automatico**: Integrazione VIES per auto-compilazione dati aziendali
- **🧠 Gestione Intelligente Aziende**: Rilevamento automatico e validazione dinamica
- **🚨 Error Handling Avanzato**: Gestione errori specifica per troubleshooting

## 🤖 Guida per AI Agents

### Principi di Sviluppo
Quando lavori su questo progetto, segui sempre questi principi:

1. **Sicurezza Prima di Tutto**
   - Ogni modifica deve rispettare le autorizzazioni per ruolo
   - Validare sempre input utente lato client E server
   - Non esporre mai dati sensibili nei log o errori

2. **Consistenza dell'Architettura**
   - Rispetta la struttura esistente dei componenti
   - Usa i pattern stabiliti (PrivateRoute, APIRequest, etc.)
   - Mantieni la separazione tra logica business e UI

3. **Testing Obbligatorio**
   - Ogni nuova funzionalità deve avere test unitari
   - Test di integrazione per workflow completi
   - Test E2E per user journey critici

### Regole di Business Critiche

#### Autenticazione e Autorizzazione
- **JWT Token**: Obbligatorio per tutte le API (header `auth`)
- **Ruoli Gerarchici**: ADMIN > DISTRIBUTORE > AFFILIATO > AGENTE > PDV
- **Controllo Accesso**: Ogni route deve verificare ruolo e permessi
- **Session Management**: Token in localStorage, ruolo in localStorage

#### Gestione Ordini
- **Stati Ordine**: BOZZA → CONFERMATO → IN_LAVORAZIONE → SPEDITO → CONSEGNATO
- **Validazioni**: Quantità > 0, prodotti disponibili, magazzino valido
- **Workflow**: Carrello → Ordine → Documento → Spedizione → Consegna

#### Gestione Magazzino
- **Disponibilità**: quantity - reserved = available
- **Movimenti**: Ogni operazione deve creare movimento di magazzino
- **Controlli**: Scorte minime, scadenze, lotti

#### Validazioni Standard
```javascript
// Email: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i
// P.IVA: Obbligatoria per tutti i clienti
// Telefono: Obbligatorio
// Indirizzo completo: Via, Città, CAP obbligatori
```

### Struttura Componenti

#### Naming Convention
- **Componenti**: PascalCase (es. `DashboardDistributore`)
- **File**: camelCase (es. `dashboardDistributore.jsx`)
- **Costanti**: UPPER_SNAKE_CASE (es. `DISTRIBUTORE`)
- **API Endpoints**: lowercase con slash (es. `products/`)

#### Pattern Obbligatori
```javascript
// 1. Gestione Errori
try {
  const response = await APIRequest('GET', 'endpoint/');
  // handle success
} catch (error) {
  console.error('Specific error context:', error);
  this.toast.show({
    severity: 'error',
    summary: 'Errore',
    detail: 'Messaggio user-friendly',
    life: 3000
  });
}

// 2. Validazione Form
validate = (data) => {
  let errors = {};
  if (!data.field) {
    errors.field = Costanti.FieldRequired;
  }
  return errors;
}

// 3. Loading States
this.setState({ loading: true });
// API call
this.setState({ loading: false });
```

### API Guidelines

#### Request Format
```javascript
// Sempre usare APIRequest utility
await APIRequest('METHOD', 'endpoint/', data)
  .then(response => {
    // Handle success
  })
  .catch(error => {
    // Handle error
  });
```

#### Response Format Standard
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1000,
    "pages": 20
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "1.0"
  }
}
```

### Testing Requirements

#### Test Coverage Minima
- **Componenti**: 80% line coverage
- **Utils**: 90% line coverage
- **API Integration**: 100% happy path + error cases
- **User Workflows**: Tutti i percorsi critici

#### Test Structure
```javascript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  });

  describe('Authentication', () => {
    it('should redirect to login when not authenticated', () => {
      // Test
    });
  });

  describe('Authorization', () => {
    it('should show only authorized features for role', () => {
      // Test
    });
  });

  describe('Business Logic', () => {
    it('should validate business rules', () => {
      // Test
    });
  });
});
```

### 🏗️ Stack Tecnologico

- **Frontend**: React 17.0.1 + Redux
- **UI Framework**: PrimeReact + Ant Design + Bootstrap
- **Routing**: React Router DOM
- **HTTP Client**: Axios
- **Maps**: Leaflet + React Leaflet
- **Charts**: Chart.js
- **Build Tool**: Create React App
- **Containerization**: Docker

## 📚 Documentazione

### 📖 Documentazione Completa
- **[Documentazione Tecnica](./DOCUMENTAZIONE_TECNICA.md)** - Panoramica completa dell'architettura e delle funzionalità
- **[Interfacce API](./INTERFACCE_API.md)** - Documentazione dettagliata delle API e modelli dati
- **[Guida Installazione](./GUIDA_INSTALLAZIONE.md)** - Istruzioni per installazione, build e deployment

### 🆕 **Pianificazione e Sviluppo**
- **[Piano Sviluppo Nuove Feature](./PIANO_SVILUPPO_NUOVE_FEATURE.md)** - Roadmap implementazioni e coordinamento
- **[Backend Requirements](./BACKEND_REQUIREMENTS_SPEC.md)** - Specifiche tecniche per Backend Agent
- **[Prompt Backend Agent](./PROMPT_BACKEND_AGENT.md)** - Documento di coordinamento team
- **[Architettura Componenti](./ARCHITETTURA_COMPONENTI.md)** - Pattern di sviluppo e best practices
- **[Guida Testing](./TESTING_GUIDE.md)** - Suite completa di test automatici e regole di business

### 🚀 Quick Start

```bash
# Clone del repository
git clone <repository-url> cu-frontend-coll
cd cu-frontend-coll

# Installazione dipendenze
npm install

# Avvio ambiente di sviluppo
npm start

# L'applicazione sarà disponibile su http://localhost:3000
```

### 🏗️ Build per Produzione

```bash
# Build ottimizzato
npm run build

# Test build locale
npx serve -s build
```

### 🐳 Docker Deployment

```bash
# Build immagine Docker
docker build -t cu-frontend:latest .

# Avvio con Docker Compose
docker-compose up -d
```

## 🎭 Ruoli Utente e Funzionalità

### 👨‍💼 Amministratore (ADMIN)
- Gestione utenti sistema
- Configurazioni globali
- Monitoraggio e analytics
- Gestione corporate

### 🏢 Distributore (DISTRIBUTORE)
- Dashboard completa con analytics
- Gestione magazzini e inventario
- Ufficio vendite e acquisti
- Gestione affiliati e punti vendita
- Logistica e consegne
- Previsioni acquisti
- Integrazione sistemi esterni

### 🤝 Affiliato (AFFILIATO)
- Visualizzazione listini personalizzati
- Creazione e gestione ordini
- Gestione punti vendita
- Approvvigionamento automatico
- Sistema messaggi
- Gestione documenti

### 👨‍💼 Agente (AGENTE)
- Gestione portafoglio clienti
- Creazione ordini diretti
- Visualizzazione listini
- Storico documenti e vendite
- Sistema messaggi
- Gestione merce inevasa

### 🏪 Punto Vendita (PDV)
- Marketplace prodotti interattivo
- Carrello acquisti avanzato
- Gestione ordini
- Storico documenti
- Sistema messaggi

### 📦 Responsabile Magazzino (RESP_MAG)
- Inventario e movimentazioni
- Controllo lotti e scadenze
- Gestione lavorazioni
- Composizione magazzino
- Gestione operatori
- Gestione prodotti posizionati

### 🚛 Autista (AUTISTA)
- Gestione consegne
- Tracking GPS
- Dettagli consegna
- Ottimizzazione percorsi

### 🔗 Chain & Ring
- Gestione rete distributiva
- Coordinamento multi-punto
- Approvvigionamento circolare
- Marketplace interno

## 🛠️ Sviluppo

### Struttura Progetto
```
cu-frontend-coll/
├── public/                 # File statici e traduzioni
├── src/
│   ├── components/         # Componenti base e condivisi
│   ├── common/            # Componenti specifici per ruolo
│   ├── aggiunta_dati/     # Form CRUD
│   ├── css/               # Stili personalizzati
│   ├── utils/             # Utility functions
│   └── App.js             # Componente principale
├── docker/                # Configurazioni Docker
└── docs/                  # Documentazione
```

### Script Disponibili

```bash
# Sviluppo
npm start          # Server di sviluppo con hot reload
npm test           # Esecuzione test suite
npm run build      # Build produzione ottimizzato

# Analisi
npm run analyze    # Analisi bundle size
npm run lint       # Linting codice
npm run format     # Formattazione codice
```

### Variabili Ambiente

Creare file `.env` per configurazione locale:
```env
REACT_APP_API_URL=http://localhost:3001
REACT_APP_ENVIRONMENT=development
REACT_APP_RECAPTCHA_SITE_KEY=your_recaptcha_key
REACT_APP_DEBUG=true
```

## 🔧 Configurazione

### Proxy Backend
Il progetto è configurato per utilizzare un proxy verso il backend:
```json
{
  "proxy": "http://*************:3001"
}
```

### API Base URL
```javascript
const produzione = 'https://eprocurement.tmselezioni.it/api'
```

## 🧪 Testing

### ⭐ Test Pre-Deploy (OBBLIGATORI)

**IMPORTANTE**: Questi test devono passare al 100% prima di ogni deploy in produzione.

```bash
# Esegui test pre-deploy
npm run test:pre-deploy

# Per CI/CD
npm run test:pre-deploy:ci
```

**Cosa testano**:
- ✅ Caricamento componenti senza errori
- ✅ Presenza di tutti i campi obbligatori
- ✅ Gestione corretta dei ruoli (AGENTE/AFFILIATO)
- ✅ Visibilità pulsanti controllata
- ✅ Form con ID corretto per submit esterno
- ✅ Dropdown senza label duplicata
- ✅ Validazione form funzionante
- ✅ Payload API corretto (senza idAffiliate)
- ✅ Gestione input malevoli
- ✅ Gestione errori API
- ✅ Performance accettabile
- ✅ Responsive design
- ✅ Accessibilità

### Altri Test

```bash
# Test unitari
npm test

# Test con coverage
npm test -- --coverage

# Test end-to-end
npm run test:e2e

# Test automatici business rules
npm run test:business

# Test integrazione API
npm run test:integration
```

### Deployment & Environment

#### Environment Variables
```bash
# Development
REACT_APP_API_URL=http://localhost:3001
REACT_APP_ENVIRONMENT=development

# Production
REACT_APP_API_URL=https://eprocurement.tmselezioni.it/api
REACT_APP_ENVIRONMENT=production
```

#### Pre-commit Checklist
- [ ] **Test pre-deploy passano** (npm run test:pre-deploy) ⭐ OBBLIGATORIO
- [ ] Tests passano (npm test)
- [ ] Linting pulito (npm run lint)
- [ ] Build successful (npm run build)
- [ ] Coverage mantenuta (npm run test:coverage)
- [ ] Documentazione aggiornata

### Debugging & Monitoring

#### Logging Standards
```javascript
// Development
console.log('Debug info:', data);

// Production
if (process.env.REACT_APP_DEBUG === 'true') {
  console.log('Debug info:', data);
}

// Errors (sempre)
console.error('Error context:', error);
```

#### Performance Monitoring
- Lazy loading per componenti pesanti
- Memoization per calcoli costosi
- Pagination per liste lunghe
- Debouncing per search/filter

### Comunicazione tra AI Agents

#### Formato Prompt Standard
```
CONTEXT: [Breve descrizione del task]
ROLE: [Ruolo utente coinvolto]
BUSINESS_RULES: [Regole specifiche da rispettare]
TECHNICAL_REQUIREMENTS: [Requisiti tecnici]
TESTING_NEEDS: [Cosa testare]
DEPENDENCIES: [Altri componenti/API coinvolti]
```

#### ✅ Issue Risolto: Creazione Retailer Semplificata

**SOLUZIONE IMPLEMENTATA**: Rimosso `idAffiliate` dalla richiesta - il backend lo assegna automaticamente.

**Frontend Status**: ✅ COMPLETAMENTE FUNZIONANTE
- Layout modale corretto
- Validazione form OK
- Creazione registry OK
- Gestione errori OK
- **Payload semplificato per /retailers/**

**Modifica Implementata**: ✅ Payload ottimizzato
```javascript
// PRIMA (causava 403)
{
  idAffiliate: 26786,
  idRegistry: 26790
}

// DOPO (funziona)
{
  idRegistry: 26790
  // idAffiliate assegnato automaticamente dal backend
}
```

**Implementazione Backend Richiesta**:
```
CONTEXT: Frontend ottimizzato per creazione retailer semplificata
ROLE: AGENTE/AFFILIATO (autenticato tramite JWT)
BUSINESS_RULES: Il backend deve assegnare automaticamente idAffiliate basandosi sull'utente autenticato
TECHNICAL_REQUIREMENTS:
- Endpoint: POST /retailers/
- Payload semplificato: {idRegistry: 26790}
- Backend deve estrarre idAffiliate dal token JWT/sessione
TESTING_NEEDS: Verificare che tutti i ruoli autorizzati possano creare retailer
DEPENDENCIES:
- Registry già creato con successo
- Utente autenticato correttamente
- Sistema di autenticazione JWT funzionante
```

**Dati Test Funzionanti**:
```json
{
  "userData": {
    "id": 26786,
    "firstName": "Mirtha",
    "lastName": "reales",
    "role": "AGENTE"
  },
  "registryCreated": {
    "id": 26790,
    "firstName": "prova",
    "lastName": "prova",
    "email": "<EMAIL>"
  },
  "retailerPayload": {
    "idAffiliate": 26786,
    "idRegistry": 26790
  }
}
```

#### Handoff Checklist
- [ ] Codice documentato
- [ ] Test implementati e passanti
- [ ] Validazioni business implementate
- [ ] Error handling completo
- [ ] Performance verificate
- [ ] Compatibilità browser testata

## 📊 Performance

### Ottimizzazioni Implementate
- **Code Splitting**: Caricamento lazy dei componenti
- **Bundle Optimization**: Minimizzazione per produzione
- **Image Optimization**: Compressione e lazy loading
- **Caching**: Strategia di cache intelligente
- **Memoization**: React.memo per componenti pesanti

### Metriche
- Bundle size ottimizzato
- First Paint < 2s
- Time to Interactive < 3s
- Lighthouse Score > 90

## 🔒 Sicurezza

- **JWT Authentication**: Token sicuri
- **Route Protection**: Controllo accessi basato su ruoli
- **HTTPS**: Comunicazioni criptate
- **Input Validation**: Sanitizzazione input
- **CSRF Protection**: Protezione cross-site

## 🌐 Internazionalizzazione

Supporto per multiple lingue:
- **Italiano** (default)
- **Inglese**

File di traduzione in `public/locales/`

## 🚀 Deployment

### Ambienti Supportati
- **Development**: Ambiente locale con hot reload
- **Staging**: Ambiente di test pre-produzione  
- **Production**: Ambiente produzione con HTTPS

### CI/CD
Configurazioni disponibili per:
- GitHub Actions
- GitLab CI/CD
- Jenkins
- Docker deployment

## 📈 Roadmap

### Prossimi Sviluppi
- [ ] PWA (Progressive Web App)
- [ ] App mobile nativa
- [ ] WebSocket real-time
- [ ] Integrazione AI/ML
- [ ] Migrazione TypeScript
- [ ] Architettura micro-frontend

## 🤝 Contribuire

1. Fork del progetto
2. Creazione feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit modifiche (`git commit -m 'Add AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apertura Pull Request

## 📝 Changelog

### v1.1.18 (Corrente)
- Miglioramenti performance
- Nuove funzionalità analytics
- Bug fixes e ottimizzazioni

Vedi [CHANGELOG.md](./CHANGELOG.md) per la cronologia completa.

## 📚 Documentazione Organizzata

La documentazione è stata completamente riorganizzata nella cartella `/docs`:

### 🏗️ **Architettura**
- [📋 Panoramica Sistema](./docs/architecture/system-overview.md)
- [🎨 Struttura Frontend](./docs/architecture/frontend-structure.md)
- [🔗 Integrazione Backend](./docs/architecture/backend-integration.md)

### 🚀 **Setup e Configurazione**
- [⚡ Setup Iniziale](./docs/setup/initial-setup.md)
- [🔧 Configurazione GitHub](./docs/setup/github-setup.md)
- [☁️ Integrazione Vercel](./docs/setup/vercel-integration.md)

### 🧪 **Testing**
- [📋 Piano Master Testing](./docs/testing/automated-testing-master-plan.md)
- [🔧 Sistema di Testing](./docs/testing/testing-system.md)
- [🎯 Guida Testing](./docs/testing/testing-guide.md)

### 🔧 **Sviluppo**
- [📝 Log di Sviluppo](./docs/development/development-log.md) - **Importante per AI Agents**
- [🎯 Piano Feature](./docs/development/feature-development-plan.md)
- [🤖 Prompt Backend Agent](./docs/development/backend-agent-prompt.md)

### 🚨 **Error Handling**
- [🔧 Miglioramenti Backend](./docs/error-handling/backend-improvements.md)
- [🎨 Miglioramenti Frontend](./docs/error-handling/frontend-improvements.md)

## 🆕 Ultime Implementazioni (16 Gen 2025)

### ✨ **Lookup P.IVA Automatico**
- **Integrazione VIES**: Servizio europeo per validazione P.IVA
- **Auto-compilazione**: Nome, indirizzo, città popolati automaticamente
- **Gestione errori**: Fallback manuale sempre disponibile

### 🧠 **Gestione Intelligente Aziende**
- **Rilevamento automatico**: P.IVA + parole chiave (SRL, TRADING, etc.)
- **Validazione dinamica**: Cognome opzionale per aziende
- **UI adattiva**: Icone e messaggi specifici per tipo anagrafica

### 🚨 **Error Handling Avanzato**
- **Messaggi specifici**: Invece di errori generici
- **Troubleshooting**: Logging dettagliato per debugging
- **Codici HTTP**: Gestione specifica per ogni status code

## 📞 Supporto

- **Email**: <EMAIL>
- **Documentazione**: [Wiki del progetto](./docs/)
- **Issues**: [GitHub Issues](./issues)

## 📄 Licenza

Questo progetto è proprietario di TM Selezioni.
Tutti i diritti riservati © 2024 TM Selezioni

---

**Versione**: 1.1.18  
**Ultimo aggiornamento**: 2024  
**Ambiente produzione**: https://eprocurement.tmselezioni.it
