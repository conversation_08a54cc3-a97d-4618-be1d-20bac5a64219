{"ast": null, "code": "import * as React from 'react';\nvar CascaderContext = /*#__PURE__*/React.createContext(null);\nexport default CascaderContext;", "map": {"version": 3, "names": ["React", "CascaderContext", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-cascader/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar CascaderContext = /*#__PURE__*/React.createContext(null);\nexport default CascaderContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}