{
  "extends": "./tsconfig-base.json",
  "compilerOptions": {
    "resolveJsonModule": true,
  },
  "references": [
    {"path": "./types/lhr/"},
    {"path": "./report/"},
    {"path": "./report/generator/"},
    {"path": "./shared/"},
    {"path": "./treemap/"},
  ],
  "include": [
    "root.js",
    "esm-utils.js",
    "cli/**/*.js",
    "core/**/*.js",
    "core/index.cjs",
    "clients/**/*.js",
    "build/**/*.js",
    "./types/**/*.d.ts",
    "eslint-local-rules.cjs",
    "third-party/axe/valid-langs.js",

    "core/test/results/sample_v2.json",
    "core/test/fixtures/unresolved-perflog.json",
    "core/test/fixtures/traces/lcp-m78.devtools.log.json",
    "core/audits/byte-efficiency/polyfill-graph-data.json",
    "shared/localization/locales/en-US.json",
  ],
  "exclude": [
    "core/test/audits/**/*.js",
    "core/test/fixtures/**/*.js",
    "core/test/computed/**/*.js",
    "clients/test/**/*.js",
    "cli/test/fixtures/**/*.js",
    "core/scripts/legacy-javascript/variants",
    // These test files require further changes before they can be type checked.
    "core/test/config/budget-test.js",
    "core/test/config/config-helpers-test.js",
    "core/test/config/config-plugin-test.js",
    "core/test/legacy/config/config-test.js",
    "core/test/config/default-config-test.js",
    "core/test/create-test-trace.js",
    "core/test/gather/driver/wait-for-condition-test.js",
    "core/test/gather/gatherers/accessibility-test.js",
    "core/test/gather/gatherers/cache-contents-test.js",
    "core/test/gather/gatherers/console-messages-test.js",
    "core/test/gather/gatherers/devtools-log-test.js",
    "core/test/gather/gatherers/dobetterweb/optimized-images-test.js",
    "core/test/gather/gatherers/dobetterweb/response-compression-test.js",
    "core/test/gather/gatherers/dobetterweb/tags-blocking-first-paint-test.js",
    "core/test/gather/gatherers/full-page-screenshot-test.js",
    "core/test/gather/gatherers/global-listeners-test.js",
    "core/test/gather/gatherers/html-without-javascript-test.js",
    "core/test/gather/gatherers/js-usage-test.js",
    "core/test/gather/gatherers/link-elements-test.js",
    "core/test/gather/gatherers/offline-test.js",
    "core/test/gather/gatherers/runtime-exceptions-test.js",
    "core/test/gather/gatherers/seo/font-size-test.js",
    "core/test/gather/gatherers/service-worker-test.js",
    "core/test/gather/gatherers/source-maps-test.js",
    "core/test/gather/gatherers/stack-collector-test.js",
    "core/test/gather/gatherers/start-url-test.js",
    "core/test/gather/gatherers/trace-elements-test.js",
    "core/test/gather/gatherers/viewport-dimensions-test.js",
    "core/test/index-test.js",
    "core/test/lib/arbitrary-equality-map-test.js",
    "core/test/lib/asset-saver-test.js",
    "core/test/lib/dependency-graph/base-node-test.js",
    "core/test/lib/dependency-graph/simulator/connection-pool-test.js",
    "core/test/lib/dependency-graph/simulator/dns-cache-test.js",
    "core/test/lib/dependency-graph/simulator/network-analyzer-test.js",
    "core/test/lib/dependency-graph/simulator/simulator-test.js",
    "core/test/lib/emulation-test.js",
    "core/test/lib/i18n/i18n-test.js",
    "core/test/lib/icons-test.js",
    "core/test/lib/lh-element-test.js",
    "core/test/lib/lighthouse-compatibility-test.js",
    "core/test/lib/manifest-parser-test.js",
    "core/test/lib/median-run-test.js",
    "core/test/lib/minification-estimator-test.js",
    "core/test/lib/minify-devtoolslog-test.js",
    "core/test/lib/network-recorder-test.js",
    "core/test/lib/network-request-test.js",
    "core/test/lib/page-functions-test.js",
    "core/test/lib/proto-preprocessor-test.js",
    "core/test/lib/rect-helpers-test.js",
    "core/test/lib/sentry-test.js",
    "core/test/lib/stack-packs-test.js",
    "core/test/lib/statistics-test.js",
    "core/test/lib/timing-trace-saver-test.js",
    "core/test/lib/tracehouse/cpu-profile-model-test.js",
    "core/test/lib/tracehouse/main-thread-tasks-test.js",
    "core/test/lib/tracehouse/task-summary-test.js",
    "core/test/lib/tracehouse/trace-processor-test.js",
    "core/test/lib/traces/metrics-trace-events-test.js",
    "core/test/lib/url-utils-test.js",
    "core/test/network-records-to-devtools-log-test.js",
    "core/test/runner-test.js",
    "core/test/scoring-test.js",
    "core/test/scripts/i18n/bake-ctc-to-lhl-test.js",
  ],
  "files": [
    // Opt-in to typechecking for some core tests and test-support files.
    "cli/test/fixtures/static-server.js",
    "core/test/audits/bf-cache-test.js",
    "core/test/audits/script-treemap-data-test.js",
    "core/test/computed/metrics/interactive-test.js",
    "core/test/fixtures/config-plugins/lighthouse-plugin-simple/plugin-simple.js",
  ],
}
