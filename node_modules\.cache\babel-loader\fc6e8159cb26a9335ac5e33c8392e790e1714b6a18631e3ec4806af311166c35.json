{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "arr", "each", "for<PERSON>ach", "slice", "defaults", "call", "arguments", "source", "prop", "undefined", "hasXMLHttpRequest", "XMLHttpRequest", "isPromise", "<PERSON><PERSON><PERSON><PERSON>", "then", "makePromise", "Promise", "resolve"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/i18next-http-backend/esm/utils.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\n\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\n\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n\n  return Promise.resolve(maybePromise);\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAE/U,IAAIK,GAAG,GAAG,EAAE;AACZ,IAAIC,IAAI,GAAGD,GAAG,CAACE,OAAO;AACtB,IAAIC,KAAK,GAAGH,GAAG,CAACG,KAAK;AACrB,OAAO,SAASC,QAAQA,CAACT,GAAG,EAAE;EAC5BM,IAAI,CAACI,IAAI,CAACF,KAAK,CAACE,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC,EAAE,UAAUC,MAAM,EAAE;IACpD,IAAIA,MAAM,EAAE;MACV,KAAK,IAAIC,IAAI,IAAID,MAAM,EAAE;QACvB,IAAIZ,GAAG,CAACa,IAAI,CAAC,KAAKC,SAAS,EAAEd,GAAG,CAACa,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;EACF,OAAOb,GAAG;AACZ;AACA,OAAO,SAASe,iBAAiBA,CAAA,EAAG;EAClC,OAAO,OAAOC,cAAc,KAAK,UAAU,IAAI,CAAC,OAAOA,cAAc,KAAK,WAAW,GAAG,WAAW,GAAGjB,OAAO,CAACiB,cAAc,CAAC,MAAM,QAAQ;AAC7I;AAEA,SAASC,SAASA,CAACC,YAAY,EAAE;EAC/B,OAAO,CAAC,CAACA,YAAY,IAAI,OAAOA,YAAY,CAACC,IAAI,KAAK,UAAU;AAClE;AAEA,OAAO,SAASC,WAAWA,CAACF,YAAY,EAAE;EACxC,IAAID,SAAS,CAACC,YAAY,CAAC,EAAE;IAC3B,OAAOA,YAAY;EACrB;EAEA,OAAOG,OAAO,CAACC,OAAO,CAACJ,YAAY,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}