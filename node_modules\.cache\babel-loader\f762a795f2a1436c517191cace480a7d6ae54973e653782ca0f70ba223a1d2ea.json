{"ast": null, "code": "import * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../input';\nexport default function Search(props) {\n  var _props$placeholder = props.placeholder,\n    placeholder = _props$placeholder === void 0 ? '' : _props$placeholder,\n    value = props.value,\n    prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    handleClear = props.handleClear;\n  var handleChange = React.useCallback(function (e) {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n}", "map": {"version": 3, "names": ["React", "SearchOutlined", "Input", "Search", "props", "_props$placeholder", "placeholder", "value", "prefixCls", "disabled", "onChange", "handleClear", "handleChange", "useCallback", "e", "target", "createElement", "className", "allowClear", "prefix"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/transfer/search.js"], "sourcesContent": ["import * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../input';\nexport default function Search(props) {\n  var _props$placeholder = props.placeholder,\n      placeholder = _props$placeholder === void 0 ? '' : _props$placeholder,\n      value = props.value,\n      prefixCls = props.prefixCls,\n      disabled = props.disabled,\n      onChange = props.onChange,\n      handleClear = props.handleClear;\n  var handleChange = React.useCallback(function (e) {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,KAAK,MAAM,UAAU;AAC5B,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EACpC,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,WAAW;IACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;IACrEE,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,WAAW,GAAGP,KAAK,CAACO,WAAW;EACnC,IAAIC,YAAY,GAAGZ,KAAK,CAACa,WAAW,CAAC,UAAUC,CAAC,EAAE;IAChDJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACI,CAAC,CAAC;IAE/D,IAAIA,CAAC,CAACC,MAAM,CAACR,KAAK,KAAK,EAAE,EAAE;MACzBI,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;IACzE;EACF,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC;EACd,OAAO,aAAaV,KAAK,CAACgB,aAAa,CAACd,KAAK,EAAE;IAC7CI,WAAW,EAAEA,WAAW;IACxBW,SAAS,EAAET,SAAS;IACpBD,KAAK,EAAEA,KAAK;IACZG,QAAQ,EAAEE,YAAY;IACtBH,QAAQ,EAAEA,QAAQ;IAClBS,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,aAAanB,KAAK,CAACgB,aAAa,CAACf,cAAc,EAAE,IAAI;EAC/D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}