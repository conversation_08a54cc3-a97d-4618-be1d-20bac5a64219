{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\BackendTestButton.jsx\";\nimport React, { Component } from 'react';\nimport { Button } from 'primereact/button';\nimport { baseURL } from './generalizzazioni/apireq';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass BackendTestButton extends Component {\n  constructor(props) {\n    super(props);\n    this.startSilentMonitoring = () => {\n      // Check immediato\n      this.checkBackendSilently();\n\n      // Check ogni 30 secondi\n      this.checkInterval = setInterval(() => {\n        this.checkBackendSilently();\n      }, 30000);\n    };\n    this.checkBackendSilently = async () => {\n      try {\n        const response = await fetch(baseURL + '/health', {\n          method: 'GET',\n          timeout: 5000\n        });\n        if (response.ok) {\n          const wasOffline = this.state.backendStatus === 'offline';\n          this.setState({\n            backendStatus: 'online',\n            isVisible: false // Nascondi il pulsante quando il backend è online\n          });\n          if (wasOffline) {\n            console.log('✅ Backend connection restored:', baseURL);\n          }\n        } else {\n          throw new Error(\"HTTP \".concat(response.status));\n        }\n      } catch (error) {\n        const wasOnline = this.state.backendStatus === 'online';\n        this.setState({\n          backendStatus: 'offline',\n          isVisible: true // Mostra il pulsante quando il backend è offline\n        });\n        if (wasOnline || this.state.backendStatus === 'unknown') {\n          console.warn('⚠️ Backend connection lost:', baseURL, error.message);\n        }\n      }\n    };\n    this.testBackend = async () => {\n      this.setState({\n        testing: true\n      });\n      try {\n        console.log('🧪 Manual backend test initiated');\n        const response = await fetch(baseURL + '/health', {\n          method: 'GET',\n          timeout: 5000\n        });\n        if (response.ok) {\n          this.setState({\n            lastResult: 'success',\n            testing: false,\n            backendStatus: 'online',\n            isVisible: false\n          });\n          console.log('✅ Manual backend test successful:', baseURL);\n          this.showNotification('success', '✅ Backend Test Successful', \"Backend is responding correctly at \".concat(baseURL));\n        } else {\n          throw new Error(\"HTTP \".concat(response.status));\n        }\n      } catch (error) {\n        this.setState({\n          lastResult: 'error',\n          testing: false,\n          backendStatus: 'offline'\n        });\n        console.error('❌ Manual backend test failed:', baseURL, error.message);\n        this.showNotification('error', '❌ Backend Test Failed', \"Cannot connect to \".concat(baseURL, \": \").concat(error.message));\n      }\n    };\n    this.showNotification = (type, title, message) => {\n      const notification = document.createElement('div');\n      notification.style.cssText = \"\\n            position: fixed;\\n            top: 20px;\\n            right: 20px;\\n            z-index: 9999;\\n            padding: 15px 20px;\\n            border-radius: 8px;\\n            color: white;\\n            font-family: Arial, sans-serif;\\n            font-size: 14px;\\n            max-width: 400px;\\n            box-shadow: 0 4px 12px rgba(0,0,0,0.3);\\n            background: \".concat(type === 'success' ? '#4CAF50' : '#f44336', \";\\n            animation: slideIn 0.3s ease-out;\\n        \");\n      notification.innerHTML = \"\\n            <div style=\\\"font-weight: bold; margin-bottom: 5px;\\\">\".concat(title, \"</div>\\n            <div style=\\\"font-size: 12px; opacity: 0.9;\\\">\").concat(message, \"</div>\\n        \");\n      document.body.appendChild(notification);\n      setTimeout(() => {\n        notification.style.animation = 'slideIn 0.3s ease-out reverse';\n        setTimeout(() => {\n          if (notification.parentNode) {\n            notification.parentNode.removeChild(notification);\n          }\n        }, 300);\n      }, 4000);\n    };\n    this.state = {\n      testing: false,\n      lastResult: null,\n      backendStatus: 'unknown',\n      // unknown, online, offline\n      isVisible: false\n    };\n    this.checkInterval = null;\n  }\n  componentDidMount() {\n    // Avvia il monitoraggio silenzioso del backend\n    this.startSilentMonitoring();\n  }\n  componentWillUnmount() {\n    if (this.checkInterval) {\n      clearInterval(this.checkInterval);\n    }\n  }\n  render() {\n    const {\n      testing,\n      lastResult,\n      isVisible,\n      backendStatus\n    } = this.state;\n\n    // Non mostrare il pulsante se il backend è online\n    if (!isVisible && backendStatus === 'online') {\n      return null;\n    }\n    let buttonSeverity = 'secondary';\n    let buttonIcon = 'pi pi-wifi';\n    let buttonLabel = 'Test Backend';\n    if (backendStatus === 'offline') {\n      buttonSeverity = 'danger';\n      buttonIcon = 'pi pi-exclamation-triangle';\n      buttonLabel = 'Backend Offline';\n    } else if (lastResult === 'success') {\n      buttonSeverity = 'success';\n      buttonIcon = 'pi pi-check';\n      buttonLabel = 'Backend Online';\n    } else if (lastResult === 'error') {\n      buttonSeverity = 'danger';\n      buttonIcon = 'pi pi-times';\n      buttonLabel = 'Connection Failed';\n    }\n    return /*#__PURE__*/_jsxDEV(Button, {\n      label: buttonLabel,\n      icon: buttonIcon,\n      severity: buttonSeverity,\n      loading: testing,\n      onClick: this.testBackend,\n      size: \"small\",\n      tooltip: \"Backend status: \".concat(backendStatus, \". Click to test connection to \").concat(baseURL),\n      tooltipOptions: {\n        position: 'top'\n      },\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        zIndex: 1000,\n        minWidth: '140px',\n        animation: backendStatus === 'offline' ? 'pulse 2s infinite' : 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default BackendTestButton;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "baseURL", "jsxDEV", "_jsxDEV", "BackendTestButton", "constructor", "props", "startSilentMonitoring", "checkBackendSilently", "checkInterval", "setInterval", "response", "fetch", "method", "timeout", "ok", "wasOffline", "state", "backendStatus", "setState", "isVisible", "console", "log", "Error", "concat", "status", "error", "wasOnline", "warn", "message", "testBackend", "testing", "lastResult", "showNotification", "type", "title", "notification", "document", "createElement", "style", "cssText", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "animation", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "componentDidMount", "componentWillUnmount", "clearInterval", "render", "buttonSeverity", "buttonIcon", "buttonLabel", "label", "icon", "severity", "loading", "onClick", "size", "tooltip", "tooltipOptions", "position", "bottom", "right", "zIndex", "min<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/BackendTestButton.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport { Button } from 'primereact/button';\nimport { baseURL } from './generalizzazioni/apireq';\n\nclass BackendTestButton extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            testing: false,\n            lastResult: null,\n            backendStatus: 'unknown', // unknown, online, offline\n            isVisible: false\n        };\n        this.checkInterval = null;\n    }\n\n    componentDidMount() {\n        // Avvia il monitoraggio silenzioso del backend\n        this.startSilentMonitoring();\n    }\n\n    componentWillUnmount() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n        }\n    }\n\n    startSilentMonitoring = () => {\n        // Check immediato\n        this.checkBackendSilently();\n\n        // Check ogni 30 secondi\n        this.checkInterval = setInterval(() => {\n            this.checkBackendSilently();\n        }, 30000);\n    }\n\n    checkBackendSilently = async () => {\n        try {\n            const response = await fetch(baseURL + '/health', {\n                method: 'GET',\n                timeout: 5000\n            });\n\n            if (response.ok) {\n                const wasOffline = this.state.backendStatus === 'offline';\n                this.setState({\n                    backendStatus: 'online',\n                    isVisible: false // Nascondi il pulsante quando il backend è online\n                });\n\n                if (wasOffline) {\n                    console.log('✅ Backend connection restored:', baseURL);\n                }\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (error) {\n            const wasOnline = this.state.backendStatus === 'online';\n            this.setState({\n                backendStatus: 'offline',\n                isVisible: true // Mostra il pulsante quando il backend è offline\n            });\n\n            if (wasOnline || this.state.backendStatus === 'unknown') {\n                console.warn('⚠️ Backend connection lost:', baseURL, error.message);\n            }\n        }\n    }\n\n    testBackend = async () => {\n        this.setState({ testing: true });\n\n        try {\n            console.log('🧪 Manual backend test initiated');\n\n            const response = await fetch(baseURL + '/health', {\n                method: 'GET',\n                timeout: 5000\n            });\n\n            if (response.ok) {\n                this.setState({\n                    lastResult: 'success',\n                    testing: false,\n                    backendStatus: 'online',\n                    isVisible: false\n                });\n                console.log('✅ Manual backend test successful:', baseURL);\n                this.showNotification('success', '✅ Backend Test Successful',\n                    `Backend is responding correctly at ${baseURL}`);\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (error) {\n            this.setState({\n                lastResult: 'error',\n                testing: false,\n                backendStatus: 'offline'\n            });\n            console.error('❌ Manual backend test failed:', baseURL, error.message);\n            this.showNotification('error', '❌ Backend Test Failed',\n                `Cannot connect to ${baseURL}: ${error.message}`);\n        }\n    }\n\n    showNotification = (type, title, message) => {\n        const notification = document.createElement('div');\n        notification.style.cssText = `\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            z-index: 9999;\n            padding: 15px 20px;\n            border-radius: 8px;\n            color: white;\n            font-family: Arial, sans-serif;\n            font-size: 14px;\n            max-width: 400px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n            background: ${type === 'success' ? '#4CAF50' : '#f44336'};\n            animation: slideIn 0.3s ease-out;\n        `;\n        \n        notification.innerHTML = `\n            <div style=\"font-weight: bold; margin-bottom: 5px;\">${title}</div>\n            <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n        `;\n        \n        document.body.appendChild(notification);\n        \n        setTimeout(() => {\n            notification.style.animation = 'slideIn 0.3s ease-out reverse';\n            setTimeout(() => {\n                if (notification.parentNode) {\n                    notification.parentNode.removeChild(notification);\n                }\n            }, 300);\n        }, 4000);\n    }\n\n    render() {\n        const { testing, lastResult, isVisible, backendStatus } = this.state;\n\n        // Non mostrare il pulsante se il backend è online\n        if (!isVisible && backendStatus === 'online') {\n            return null;\n        }\n\n        let buttonSeverity = 'secondary';\n        let buttonIcon = 'pi pi-wifi';\n        let buttonLabel = 'Test Backend';\n\n        if (backendStatus === 'offline') {\n            buttonSeverity = 'danger';\n            buttonIcon = 'pi pi-exclamation-triangle';\n            buttonLabel = 'Backend Offline';\n        } else if (lastResult === 'success') {\n            buttonSeverity = 'success';\n            buttonIcon = 'pi pi-check';\n            buttonLabel = 'Backend Online';\n        } else if (lastResult === 'error') {\n            buttonSeverity = 'danger';\n            buttonIcon = 'pi pi-times';\n            buttonLabel = 'Connection Failed';\n        }\n\n        return (\n            <Button\n                label={buttonLabel}\n                icon={buttonIcon}\n                severity={buttonSeverity}\n                loading={testing}\n                onClick={this.testBackend}\n                size=\"small\"\n                tooltip={`Backend status: ${backendStatus}. Click to test connection to ${baseURL}`}\n                tooltipOptions={{ position: 'top' }}\n                style={{\n                    position: 'fixed',\n                    bottom: '20px',\n                    right: '20px',\n                    zIndex: 1000,\n                    minWidth: '140px',\n                    animation: backendStatus === 'offline' ? 'pulse 2s infinite' : 'none'\n                }}\n            />\n        );\n    }\n}\n\nexport default BackendTestButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,iBAAiB,SAASL,SAAS,CAAC;EACtCM,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAAC,KAqBjBC,qBAAqB,GAAG,MAAM;MAC1B;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;;MAE3B;MACA,IAAI,CAACC,aAAa,GAAGC,WAAW,CAAC,MAAM;QACnC,IAAI,CAACF,oBAAoB,CAAC,CAAC;MAC/B,CAAC,EAAE,KAAK,CAAC;IACb,CAAC;IAAA,KAEDA,oBAAoB,GAAG,YAAY;MAC/B,IAAI;QACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACX,OAAO,GAAG,SAAS,EAAE;UAC9CY,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;QACb,CAAC,CAAC;QAEF,IAAIH,QAAQ,CAACI,EAAE,EAAE;UACb,MAAMC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,KAAK,SAAS;UACzD,IAAI,CAACC,QAAQ,CAAC;YACVD,aAAa,EAAE,QAAQ;YACvBE,SAAS,EAAE,KAAK,CAAC;UACrB,CAAC,CAAC;UAEF,IAAIJ,UAAU,EAAE;YACZK,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAErB,OAAO,CAAC;UAC1D;QACJ,CAAC,MAAM;UACH,MAAM,IAAIsB,KAAK,SAAAC,MAAA,CAASb,QAAQ,CAACc,MAAM,CAAE,CAAC;QAC9C;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZ,MAAMC,SAAS,GAAG,IAAI,CAACV,KAAK,CAACC,aAAa,KAAK,QAAQ;QACvD,IAAI,CAACC,QAAQ,CAAC;UACVD,aAAa,EAAE,SAAS;UACxBE,SAAS,EAAE,IAAI,CAAC;QACpB,CAAC,CAAC;QAEF,IAAIO,SAAS,IAAI,IAAI,CAACV,KAAK,CAACC,aAAa,KAAK,SAAS,EAAE;UACrDG,OAAO,CAACO,IAAI,CAAC,6BAA6B,EAAE3B,OAAO,EAAEyB,KAAK,CAACG,OAAO,CAAC;QACvE;MACJ;IACJ,CAAC;IAAA,KAEDC,WAAW,GAAG,YAAY;MACtB,IAAI,CAACX,QAAQ,CAAC;QAAEY,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhC,IAAI;QACAV,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAE/C,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAACX,OAAO,GAAG,SAAS,EAAE;UAC9CY,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;QACb,CAAC,CAAC;QAEF,IAAIH,QAAQ,CAACI,EAAE,EAAE;UACb,IAAI,CAACI,QAAQ,CAAC;YACVa,UAAU,EAAE,SAAS;YACrBD,OAAO,EAAE,KAAK;YACdb,aAAa,EAAE,QAAQ;YACvBE,SAAS,EAAE;UACf,CAAC,CAAC;UACFC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAErB,OAAO,CAAC;UACzD,IAAI,CAACgC,gBAAgB,CAAC,SAAS,EAAE,2BAA2B,wCAAAT,MAAA,CAClBvB,OAAO,CAAE,CAAC;QACxD,CAAC,MAAM;UACH,MAAM,IAAIsB,KAAK,SAAAC,MAAA,CAASb,QAAQ,CAACc,MAAM,CAAE,CAAC;QAC9C;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZ,IAAI,CAACP,QAAQ,CAAC;UACVa,UAAU,EAAE,OAAO;UACnBD,OAAO,EAAE,KAAK;UACdb,aAAa,EAAE;QACnB,CAAC,CAAC;QACFG,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEzB,OAAO,EAAEyB,KAAK,CAACG,OAAO,CAAC;QACtE,IAAI,CAACI,gBAAgB,CAAC,OAAO,EAAE,uBAAuB,uBAAAT,MAAA,CAC7BvB,OAAO,QAAAuB,MAAA,CAAKE,KAAK,CAACG,OAAO,CAAE,CAAC;MACzD;IACJ,CAAC;IAAA,KAEDI,gBAAgB,GAAG,CAACC,IAAI,EAAEC,KAAK,EAAEN,OAAO,KAAK;MACzC,MAAMO,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAClDF,YAAY,CAACG,KAAK,CAACC,OAAO,wYAAAhB,MAAA,CAYRU,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,+DAE3D;MAEDE,YAAY,CAACK,SAAS,0EAAAjB,MAAA,CACoCW,KAAK,wEAAAX,MAAA,CACbK,OAAO,qBACxD;MAEDQ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;MAEvCQ,UAAU,CAAC,MAAM;QACbR,YAAY,CAACG,KAAK,CAACM,SAAS,GAAG,+BAA+B;QAC9DD,UAAU,CAAC,MAAM;UACb,IAAIR,YAAY,CAACU,UAAU,EAAE;YACzBV,YAAY,CAACU,UAAU,CAACC,WAAW,CAACX,YAAY,CAAC;UACrD;QACJ,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC;IApIG,IAAI,CAACnB,KAAK,GAAG;MACTc,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,IAAI;MAChBd,aAAa,EAAE,SAAS;MAAE;MAC1BE,SAAS,EAAE;IACf,CAAC;IACD,IAAI,CAACX,aAAa,GAAG,IAAI;EAC7B;EAEAuC,iBAAiBA,CAAA,EAAG;IAChB;IACA,IAAI,CAACzC,qBAAqB,CAAC,CAAC;EAChC;EAEA0C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACxC,aAAa,EAAE;MACpByC,aAAa,CAAC,IAAI,CAACzC,aAAa,CAAC;IACrC;EACJ;EAoHA0C,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEpB,OAAO;MAAEC,UAAU;MAAEZ,SAAS;MAAEF;IAAc,CAAC,GAAG,IAAI,CAACD,KAAK;;IAEpE;IACA,IAAI,CAACG,SAAS,IAAIF,aAAa,KAAK,QAAQ,EAAE;MAC1C,OAAO,IAAI;IACf;IAEA,IAAIkC,cAAc,GAAG,WAAW;IAChC,IAAIC,UAAU,GAAG,YAAY;IAC7B,IAAIC,WAAW,GAAG,cAAc;IAEhC,IAAIpC,aAAa,KAAK,SAAS,EAAE;MAC7BkC,cAAc,GAAG,QAAQ;MACzBC,UAAU,GAAG,4BAA4B;MACzCC,WAAW,GAAG,iBAAiB;IACnC,CAAC,MAAM,IAAItB,UAAU,KAAK,SAAS,EAAE;MACjCoB,cAAc,GAAG,SAAS;MAC1BC,UAAU,GAAG,aAAa;MAC1BC,WAAW,GAAG,gBAAgB;IAClC,CAAC,MAAM,IAAItB,UAAU,KAAK,OAAO,EAAE;MAC/BoB,cAAc,GAAG,QAAQ;MACzBC,UAAU,GAAG,aAAa;MAC1BC,WAAW,GAAG,mBAAmB;IACrC;IAEA,oBACInD,OAAA,CAACH,MAAM;MACHuD,KAAK,EAAED,WAAY;MACnBE,IAAI,EAAEH,UAAW;MACjBI,QAAQ,EAAEL,cAAe;MACzBM,OAAO,EAAE3B,OAAQ;MACjB4B,OAAO,EAAE,IAAI,CAAC7B,WAAY;MAC1B8B,IAAI,EAAC,OAAO;MACZC,OAAO,qBAAArC,MAAA,CAAqBN,aAAa,oCAAAM,MAAA,CAAiCvB,OAAO,CAAG;MACpF6D,cAAc,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAE;MACpCxB,KAAK,EAAE;QACHwB,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,OAAO;QACjBtB,SAAS,EAAE3B,aAAa,KAAK,SAAS,GAAG,mBAAmB,GAAG;MACnE;IAAE;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;AACJ;AAEA,eAAenE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}