{"ast": null, "code": "import Statistic from './Statistic';\nimport Countdown from './Countdown';\nStatistic.Countdown = Countdown;\nexport default Statistic;", "map": {"version": 3, "names": ["Statistic", "Countdown"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/statistic/index.js"], "sourcesContent": ["import Statistic from './Statistic';\nimport Countdown from './Countdown';\nStatistic.Countdown = Countdown;\nexport default Statistic;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnCD,SAAS,CAACC,SAAS,GAAGA,SAAS;AAC/B,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}