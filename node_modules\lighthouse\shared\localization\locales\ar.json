{"core/audits/accessibility/accesskeys.js | description": {"message": "تتيح مفاتيح الوصول للمستخدمين التركيز بسرعة على جزء من الصفحة. للانتقال إلى الموضع الصحيح من الصفحة، يجب أن يكون كل مفتاح وصول فريدًا. [مزيد من المعلومات حول مفاتيح الوصول](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "قيم `[accesskey]` هي غير فريدة"}, "core/audits/accessibility/accesskeys.js | title": {"message": "قيم `[accesskey]` فريدة"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "يوفّر كل `role` في ARIA مجموعة فرعية محدَّدة من سمات `aria-*`. يؤدي عدم تطابق هذه الأدوار إلى إلغاء سمات `aria-*`. تعرَّف على [كيفية مطابقة سمات ARIA مع أدوارها](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "سمات `[aria-*]` لا تتطابق مع أدوارها"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "سمات `[aria-*]` هي مطابقة لأدوارها"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "عند عدم ظهور اسم أحد العناصر على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر باستخدام اسم عام، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. تعرَّف على [كيفية تسهيل استخدام عناصر الأوامر](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "لا تتوفّر لعناصر `button` و`link` و`menuitem` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "تتوفّر لعناصر `button` و`link` و`menuitem` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "لا تعمل التكنولوجيا المساعِدة، مثل برامج قراءة الشاشة، بشكل متسق عند ضبط `aria-hidden=\"true\"` في المستند `<body>`. تعرّف على [مدى تأثير السمة `aria-hidden` في نص المستند](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` وارد في المستند `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` غير وارد في المستند `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "العناصر التابعة التي يمكن التركيز عليها ضِمن عنصر `[aria-hidden=\"true\"]` تمنع إتاحة العناصر التفاعلية لمستخدمي التكنولوجيا المساعِدة، مثل برامج قراءة الشاشة. تعرَّف على [مدى تأثير `aria-hidden` في العناصر التي يمكن التركيز عليها](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "تحتوي عناصر `[aria-hidden=\"true\"]` على عناصر منحدرة قابلة للتركيز"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "لا تحتوي عناصر `[aria-hidden=\"true\"]` على عناصر منحدرة قابلة للتركيز"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "عند عدم ظهور اسم أحد حقول الإدخال على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر باستخدام اسم عام، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. [مزيد من المعلومات حول تصنيفات حقول الإدخال](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "لا تحتوي حقول إدخال ARIA على أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "تحتوي حقول إدخال ARIA على أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "عند عدم ظهور اسم أحد عناصر \"مقياس\" (meter) على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر بصفة عامة، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. [تعرَّف على كيفية تسمية عناصر `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "لا تتوفّر لعناصر ARIA `meter` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "تتوفّر لعناصر ARIA `meter` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "عندما لا يكون اسم أحد عناصر `progressbar` ظاهرًا على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر باستخدام اسم عام، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. تعرَّف على [كيفية تصنيف عناصر `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "لا تتوفّر لعناصر ARIA `progressbar` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "تتوفّر لعناصر ARIA `progressbar` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "تتطلّب بعض أدوار ARIA تزويد برامج قراءة الشاشة بسمات تصف حالة العنصر. [مزيد من المعلومات حول الأدوار والسمات المطلوبة](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` لا تحتوي على جميع سمات`[aria-*]` المطلوبة"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` تحتوي على جميع سمات `[aria-*]` المطلوبة"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "يجب أن تحتوي بعض أدوار ARIA الرئيسية على أدوار ثانوية محدَّدة لأداء وظائف إمكانية الوصول المقصودة. [مزيد من المعلومات حول الأدوار والعناصر الثانوية المطلوبة](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "إنّ العناصر التي تتضمّن ARIA `[role]` والتي تتطلب عناصر ثانوية للاحتواء على عنصر `[role]` محدّد لا تتضمّن بعض هذه العناصر الثانوية المطلوبة أو جميعها."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "إنّ العناصر التي تتضمن ARIA `[role]` والتي تتطلب عناصر ثانوية للاحتواء على عنصر `[role]` محدّد تشتمل على جميع العناصر الثانوية المطلوبة."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "يجب إدراج بعض أدوار ARIA الثانوية ضِمن أدوار رئيسية محدَّدة لتنفيذ وظائف إمكانية الوصول المقصودة بشكل صحيح. [مزيد من المعلومات حول أدوار ARIA والعنصر الرئيسي المطلوب](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` غير مضمّنة في العنصر الرئيسي المطلوب"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` مضمّنة في العنصر الرئيسي المطلوب"}, "core/audits/accessibility/aria-roles.js | description": {"message": "يجب أن تحتوي أدوار ARIA على قيم صالحة لتنفيذ وظائف إمكانية الوصول المقصودة. [مزيد من المعلومات حول أدوار ARIA الصالحة](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "قيم `[role]` هي غير صالحة"}, "core/audits/accessibility/aria-roles.js | title": {"message": "قيم `[role]` هي صالحة"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "عند عدم ظهور اسم أحد حقول التبديل على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر باستخدام اسم عام، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. [مزيد من المعلومات حول حقول التبديل](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "لا تحتوي حقول تبديل ARIA على أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "تحتوي جميع حقول تبديل ARIA على أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "عند عدم ظهور اسم أحد عناصر \"تلميح\" (tooltip) على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر بصفة عامة، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. [تعرَّف على كيفية تسمية عناصر `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "لا تتوفّر لعناصر ARIA `tooltip` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "تتوفّر لعناصر ARIA `tooltip` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "عندما لا يكون اسم أحد عناصر `treeitem` ظاهرًا على واجهة المستخدم، تشير برامج قراءة الشاشة إلى هذا العنصر باستخدام اسم عام، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. [مزيد من المعلومات حول تصنيف عناصر `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "لا تتوفّر لعناصر ARIA `treeitem` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "تتوفّر لعناصر ARIA `treeitem` أسماء يمكن الوصول إليها"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "لا يمكن للتكنولوجيا المساعِدة، مثل برامج قراءة الشاشة، تفسير سمات ARIA باستخدام قيم غير صالحة. [مزيد من المعلومات حول القيم الصالحة لسمات ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "سمات `[aria-*]` لا تحتوي على قيم صحيحة"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "سمات `[aria-*]` تحتوي على قيم صالحة"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "لا يمكن للتكنولوجيا المساعِدة، مثل برامج قراءة الشاشة، تفسير سمات ARIA بأسماء غير صالحة. [مزيد من المعلومات حول سمات ARIA الصالحة](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "سمات `[aria-*]` هي غير صالحة أو بها أخطاء إملائية"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "سمات `[aria-*]` هي صالحة وليس بها أخطاء إملائية"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "العناصر التي رسبت في عملية التدقيق"}, "core/audits/accessibility/button-name.js | description": {"message": "عند عدم ظهور اسم أحد الأزرار على واجهة المستخدم، تشير برامج قراءة الشاشة إليه باسم \"زر\"، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. تعرَّف على [كيفية تسهيل استخدام الأزرار](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "عدم احتواء الأزرار على اسم يمكن الوصول إليه"}, "core/audits/accessibility/button-name.js | title": {"message": "احتواء الأزرار على اسم الوصول"}, "core/audits/accessibility/bypass.js | description": {"message": "تؤدي إضافة طرق لاستبعاد المحتوى المكرَّر إلى السماح لمستخدمي لوحة المفاتيح بالتنقّل في الصفحة بكفاءة أكبر. [مزيد من المعلومات حول روابط استبعاد المحتوى المكرَّر](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "عدم احتواء الصفحة على عنوان أو رابط تخطٍ أو منطقة معالم"}, "core/audits/accessibility/bypass.js | title": {"message": "احتواء الصفحة على عنوان أو رابط تخطٍ أو منطقة معالم"}, "core/audits/accessibility/color-contrast.js | description": {"message": "إنّ عملية قراءة النص المنخفض التباين تُعد صعبة أو مستحيلة بالنسبة إلى العديد من المستخدمين. تعرَّف على [كيفية توفير نص بألوان متباينة بشكلٍ كافٍ](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "عد<PERSON> احتواء الخلفية وألوان الخلفية على نسبة تباين كافية"}, "core/audits/accessibility/color-contrast.js | title": {"message": "تمييز الخلفية والألوان الخلفية بنسبة تباين كافية"}, "core/audits/accessibility/definition-list.js | description": {"message": "عندما لا يتم ترميز قوائم التعريفات بشكل صحيح، قد تقدِّم برامج قراءة الشاشة نتائج غير واضحة أو غير دقيقة. تعرَّف على [كيفية تنظيم قوائم التعريفات بشكل صحيح](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` لا تحتوي على مجموعات `<dt>` و`<dd>` المرتبة بشكلٍ صحيح فقط، أو العناصر `<script>` أو`<template>` أو`<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` تحتوي على مجموعات `<dt>` و`<dd>` المرتبة بشكلٍ صحيح فقط، أو العناصر `<script>` أو`<template>` أو `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "يجب إدراج عناصر قائمة التعريفات (`<dt>` و`<dd>`) في عنصر `<dl>` رئيسي، وذلك لضمان إمكانية قراءة تلك العناصر بشكل صحيح من خلال برامج قراءة الشاشة. تعرَّف على [كيفية تنظيم قوائم التعريفات بشكل صحيح](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "عناصر قائمة التعريفات غير مضّمنة في عناصر `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "عناصر قائمة التعريفات مضمّنة في عناصر `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "يوفّر العنوان لمستخدمي قارئ الشاشة نظرة عامة حول الصفحة، ويعتمد مستخدمو محرك البحث على هذا بشكل كبير لتحديد ما إذا كانت الصفحة ذات صلة ببحثهم أو لا. [مزيد من المعلومات حول عناوين المستندات](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "المستند لا يحتوي على عنصر `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "المستند يحتوي على عنصر `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "يجب أن يتوفر لكل العناصر التي يمكن التركيز عليها `id` فريدًا لضمان إمكانية وصول التكنولوجيا المساعِدة إليها. تعرَّف على [كيفية حلّ مشاكل `id` المكرّرة](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "سمات `[id]` المتوفّرة في العناصر النشطة والقابلة للتركيز غير فريدة"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "سمات `[id]` المتوفّرة في العناصر النشطة والقابلة للتركيز فريدة"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "يجب أن تكون قيمة رقم تعريف ARIA فريدة حتى لا تتجاهل التكنولوجيا المساعِدة الأمثلة الأخرى. تعرَّف على [كيفية حلّ مشكلة أرقام تعريف ARIA المكرَّرة](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "أرقام تعريف ARIA غير فريدة"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "أرقام تعريف ARIA فريدة"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "إنّ التكنولوجيا المساعِدة، مثل برامج قراءة الشاشة التي تستخدم إمّا التصنيف الأول أو الأخير أو كل التصنيفات، قد تشير عن طريق الخطأ إلى الحقول النموذجية المتعددة التصنيف. تعرَّف على [كيفية استخدام تصنيفات النماذج](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "تحتوي الحقول النموذجية على تصنيفات متعددة"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "لا تحتوي الحقول النموذجية على تصنيفات متعددة"}, "core/audits/accessibility/frame-title.js | description": {"message": "يعتمد مستخدمو برامج قراءة الشاشة على عناوين الإطارات لوصف محتوى الإطارات. [مزيد من المعلومات حول عناوين الإطارات](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "عناصر `<frame>` أو `<iframe>` لا تحتوي على عنوان"}, "core/audits/accessibility/frame-title.js | title": {"message": "عناصر `<frame>` أو `<iframe>` تحتوي على عنوان"}, "core/audits/accessibility/heading-order.js | description": {"message": "إنّ العناوين المرتّبة بطريقة مناسبة ولا تتخطّى المستويات تنقل البنية الدلالية للصفحة، ما يسهِّل تصفُّحها والتعرّف عليها عند استخدام التكنولوجيا المساعِدة. [مزيد من المعلومات حول ترتيب العناوين](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "العناصر المُعنوَنة غير مرتبة بشكل تنازلي متسلسل"}, "core/audits/accessibility/heading-order.js | title": {"message": "تظهر العناصر المُعنوَنة بترتيب تنازلي متسلسل"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "في حال لم تحدِّد الصفحة سمة `lang`، يفترض قارئ الشاشة أن تكون الصفحة باللغة التلقائية التي اختارها المستخدم عند إعداد قارئ الشاشة. في حال لم تكن الصفحة باللغة التلقائية، قد لا يشير قارئ الشاشة إلى نص الصفحة بشكل صحيح. [مزيد من المعلومات حول السمة `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "العنصر `<html>` لا يحتوي على سمة `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "عنصر `<html>` يحتوي على سمة `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "يؤدي تحديد [لغة BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) صحيحة إلى مساعدة برامج قراءة الشاشة على الإشارة إلى النص بشكلٍ صحيح. تعرَّف على [كيفية استخدام السمة `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "العنصر `<html>` لا يحتوي على قيمة صالحة للسمة `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "عنصر `<html>` يحتوي على قيمة صحيحة لسمة `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "يجب أن تتضمن العناصر الإعلامية نصًا بديلاً وصفيًا وقصيرًا. يمكن تجاهل العناصر غير الضرورية من خلال استخدام سمة نص بديل فارغة. [مزيد من المعلومات حول السمة `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "عناصر الصور لا تحتوي على سمات `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "عناصر الصور تحتوي على سمات `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "عند استخدام صورة كزر `<input>`، يمكن أن يساعد توفير نص بديل مستخدمي قارئ الشاشة على فهم الغرض من الزر. [مزيد من المعلومات حول إدخال نص بديل للصورة](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "عناصر `<input type=\"image\">` لا تحتوي على نص `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "عناصر `<input type=\"image\">` تحتوي على نص `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "تضمن التصنيفات الإشارة إلى عناصر التحكّم في النموذج بشكلٍ صحيح من خلال التكنولوجيا المساعِدة، مثل برامج قراءة الشاشة. [مزيد من المعلومات حول تصنيفات عناصر النموذج](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "عدم احتواء عناصر النموذج على تصنيفات مرتبطة"}, "core/audits/accessibility/label.js | title": {"message": "احتواء عناصر النموذج على التصنيفات المرتبطة"}, "core/audits/accessibility/link-name.js | description": {"message": "إنّ نص الرابط، (والنص البديل للصور، عند استخدامه كرابط) الذي يكون مميّزًا وفريدًا وقابلاً للتركيز عليه، يحسِّن تجربة التنقّل لمستخدمي برامج قراءة الشاشة. تعرَّف على [كيفية إتاحة الوصول إلى الروابط](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "عدم احتواء الروابط على اسم مميّز"}, "core/audits/accessibility/link-name.js | title": {"message": "احتواء الروابط على اسم مميز"}, "core/audits/accessibility/list.js | description": {"message": "تعتمد برامج قراءة الشاشة على طريقة محدَّدة للإشارة إلى القوائم. يؤدّي ضمان بنية القائمة المناسبة إلى المساعدة على الاستماع إلى قارئ الشاشة. [مزيد من المعلومات حول بنية القائمة المناسبة](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "القوائم لا تحتوي على عناصر `<li>` وعناصر دعم النص البرمجي (`<script>` و`<template>`) فقط."}, "core/audits/accessibility/list.js | title": {"message": "القوائم تحتوي على عناصر `<li>` وعناصر دعم النص البرمجي (`<script>` و`<template>`) فقط."}, "core/audits/accessibility/listitem.js | description": {"message": "تتطلّب برامج قراءة الشاشة عناصر قائمة (`<li>`) يجب إدراجها ضِمن العنصر الرئيسي `<ul>` أو `<ol>` أو `<menu>` لتتم الإشارة إليها بشكلٍ صحيح. [مزيد من المعلومات حول بنية القائمة المناسبة](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "عناصر القائمة (`<li>`) غير مدرَجة ضِمن العناصر الرئيسية `<ul>` أو `<ol>` أو `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "عناصر القائمة (`<li>`) مُدرَجة ضِمن العناصر الرئيسية `<ul>` أو `<ol>` أو `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "لا يتوقع المستخدمون إعادة تحميل الصفحة تلقائيًا. وإذا تمت إعادة التحميل تلقائيًا، سيتحوّل تركيز المستخدمين إلى أعلى الصفحة. وقد ينشأ عن ذلك تجربة استخدام محبطة ومربكة. [مزيد من المعلومات حول العلامة الوصفية لإعادة التحميل](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "المستند يستخدم `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "المستند لا يستخدم `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "يسبّب إيقاف ميزة التكبير أو التصغير مشكلة للمستخدمين الذين يعانون من ضعف في النظر ويعتمدون على ميزة تكبير الشاشة لرؤية محتوى صفحة الويب على نحوٍ أفضل. [مزيد من المعلومات حول العلامة الوصفية لإطار العرض](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "يتم استخدام `[user-scalable=\"no\"]` في العنصر `<meta name=\"viewport\">` أو السمة `[maximum-scale]` هي أقل من 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` غير مستخدم في العنصر `<meta name=\"viewport\">` والسمة `[maximum-scale]` لا تقلّ عن 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "لا يمكن لبرامج قراءة الشاشة ترجمة المحتوى غير النصي. وتؤدي إضافة نص بديل إلى عناصر `<object>` إلى مساعدة برامج قراءة الشاشة على إيضاح المعنى للمستخدمين. [مزيد من المعلومات حول النص البديل لعناصر `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "عناصر `<object>` لا تحتوي على نص بديل"}, "core/audits/accessibility/object-alt.js | title": {"message": "عناصر `<object>` تحتوي على نص بديل"}, "core/audits/accessibility/tabindex.js | description": {"message": "تشير القيمة الأكبر من 0 إلى تقديم طلب صريح للتنقّل. على الرغم من صحة ذلك تقنيًّا، غالبًا ما يؤدي إلى إنشاء تجارب محبطة للمستخدمين الذين يعتمدون على التكنولوجيا المساعدة. [مزيد من المعلومات حول السمة `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "بعض العناصر تحتوي على قيمة `[tabindex]` أكبر من 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "لا يتوفّر عنصر له قيمة `[tabindex]` أكبر من 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "تحتوي برامج قراءة الشاشة على ميزات لتسهيل التنقّل بين الجداول. يمكن تحسين تجربة استخدام برامج قراءة الشاشة من خلال ضمان إشارة الخلايا `<td>` التي تستخدم السمة `[headers]` إلى خلايا أخرى في الجدول نفسه فقط. [مزيد من المعلومات حول السمة `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "إنّ الخلايا الواردة في `<table>` والتي تستخدم السمة `[headers]` تشير إلى عنصر `id` غير موجود في الجدول نفسه."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "إنّ الخلايا الواردة في `<table>` والتي تستخدم السمة `[headers]` تشير إلى الخلايا في الجدول نفسه."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "تحتوي برامج قراءة الشاشة على ميزات لتسهيل التنقّل بين الجداول. ويمكن تحسين تجربة استخدام برامج قراءة الشاشة من خلال الحرص على أن تشير عناوين الجداول دائمًا إلى بعض مجموعات الخلايا. [مزيد من المعلومات حول عناوين الجداول](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "عناصر `<th>` وعناصر `[role=\"columnheader\"/\"rowheader\"]` لا تحتوي على خلايا البيانات التي يتم وصفها."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "عناصر `<th>` وعناصر `[role=\"columnheader\"/\"rowheader\"]` تحتوي على خلايا البيانات التي يتم وصفها"}, "core/audits/accessibility/valid-lang.js | description": {"message": "يؤدي تحديد [لغة BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) صحيحة في العناصر إلى مساعدة قارئ الشاشة على قراءة النص بشكلٍ صحيح. تعرَّف على [كيفية استخدام السمة `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "سمات `[lang]` لا تحتوي على قيمة صالحة"}, "core/audits/accessibility/valid-lang.js | title": {"message": "سمات `[lang]` تحتوي على قيمة صالحة"}, "core/audits/accessibility/video-caption.js | description": {"message": "عندما يقدِّم الفيديو ترجمة وشرحًا، يَسهُل على المستخدمين الصُم والذين يعانون من مشاكل في السمع فهم مضمونه. [مزيد من المعلومات حول تقديم الترجمة والشرح على الفيديو](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "عناصر `<video>` لا تحتوي على عنصر `<track>` مع `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "عناصر `<video>` تحتوي على عنصر `<track>` مع `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "القيمة الحالية"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "الرموز المميزة المقترحة"}, "core/audits/autocomplete.js | description": {"message": "تساعد `autocomplete` المستخدمين على إرسال النماذج بشكل أسرع. لتوفير الجهد على المستخدمين، ننصح بتفعيلها من خلال ضبط سمة `autocomplete` على قيمة صالحة. [مزيد من المعلومات حول `autocomplete` في النماذج](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "لا تحتوي عناصر `<input>` على سمات صحيحة لميزة `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "يتطلب مراجعة يدوية"}, "core/audits/autocomplete.js | reviewOrder": {"message": "مراجعة طلب الرموز المميزة"}, "core/audits/autocomplete.js | title": {"message": "تستخدم عناصر `<input>` ميزة `autocomplete` بشكل صحيح"}, "core/audits/autocomplete.js | warningInvalid": {"message": "رموز `autocomplete`: الرمز \"{token}\" غير صالح في {snippet}."}, "core/audits/autocomplete.js | warningOrder": {"message": "مراجعة ترتيب الرموز المميزة: \"{tokens}\" في {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "أخطاء يمكن اتخاذ إجراء بشأنها"}, "core/audits/bf-cache.js | description": {"message": "يتم تنفيذ العديد من عمليات التنقل بالرجوع إلى صفحة سابقة أو الانتقال إلى الصفحة التالية مرة أخرى. يمكنك استخدام ميزة \"التخزين المؤقت للصفحات\" (bfcache) لتسريع عمليات الرجوع هذه. [مزيد من المعلومات حول ميزة \"التخزين المؤقت للصفحات\"](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{هناك سبب واحد لعدم تنفيذ العملية.}zero{هناك # سبب لعدم تنفيذ العملية.}two{هناك سببان لعدم تنفيذ العملية.}few{هناك # أسباب لعدم تنفيذ العملية.}many{هناك # سببًا لعدم تنفيذ العملية.}other{هناك # سبب لعدم تنفيذ العملية.}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "سبب الإخفاق"}, "core/audits/bf-cache.js | failureTitle": {"message": "تم منع استعادة الصفحة من عملية التخزين المؤقت باستخدام ميزة \"التخزين المؤقت للصفحات\""}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "نوع الخطأ"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "أخطاء لا يمكن اتخاذ إجراء بشأنها"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "ميزة التوافق مع المتصفِّح ليست متوفّرة إلى الآن."}, "core/audits/bf-cache.js | title": {"message": "لم يتم منع استعادة الصفحة من عملية التخزين المؤقت باستخدام ميزة \"التخزين المؤقت للصفحات\""}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "أثّرت \"إضافات Chrome\" بشكلٍ سلبي في أداء التحميل لهذه الصفحة. ويمكنك تجربة تدقيق الصفحة في وضع التصفُّح المُتخفّي أو من ملف شخصي على Chrome بدون الإضافات."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "تقييم النص البرمجي"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "تحليل النص البرمجي"}, "core/audits/bootup-time.js | columnTotal": {"message": "الوقت الإجمالي لوحدة المعالجة المركزية"}, "core/audits/bootup-time.js | description": {"message": "يمكنك تقليل الوقت المستغرَق في تحليل بيانات JavaScript وتجميعها وتنفيذها. قد يتبيّن لك أنّ عرض حمولات JavaScript بحجم أصغر يساعد على ذلك. [تعرَّف على كيفية تقليل وقت تنفيذ بيانات JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "تقليل وقت تنفيذ JavaScript"}, "core/audits/bootup-time.js | title": {"message": "وقت تنفيذ JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "يمكنك إزالة وحدات JavaScript الكبيرة المكررة من الحِزم لتقليل وحدات البايت غير الضرورية التي يستهلكها نشاط الشبكة. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "عليك إزالة الوحدات المكررة في حِزم JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "ملفات GIF الكبيرة غير فعّالة في عرض محتوى الصور المتحركة. يمكنك استخدام فيديوهات بتنسيق MPEG4 أو WebM للصور المتحركة وتنسيق PNG أو WebP للصور الثابتة بدلاً من ملف GIF لتوفير وحدات البايت على الشبكة. [مزيد من المعلومات حول صيغ الفيديوهات الفعّالة](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "استخدام تنسيقات الفيديو لمحتوى الصور المتحركة"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "تساعد الرموز البرمجية polyfills وtransforms المتصفّحات القديمة في استخدام ميزات JavaScript الجديدة. ومع ذلك، يكون العديد منها غير ضروري للمتصفّحات الحديثة. وبالنسبة إلى حِزم JavaScript، يمكنك استخدام استراتيجية حديثة لنشر النصوص البرمجية باستخدام ميزة الكشف عن الميزات \"module/nomodule\" لتقليل عدد الرموز البرمجية التي يتم نقلها إلى المتصفّحات الحديثة مع استمرار التوافق مع المتصفّحات القديمة. تعرَّف على [كيفية استخدام لغة JavaScript الحديثة](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "تجنُّب عرض ميزات JavaScript القديمة في المتصفحات الحديثة"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "غالبًا ما توفِّر تنسيقات الصور، مثل WebP وAVIF، ضغطًا أفضل للصور من تنسيق PNG أو JPEG، وهذا بدوره يعني تنزيلاً أسرع واستهلاكًا أقل للبيانات. [مزيد من المعلومات عن تنسيقات الصور الحديثة](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "عرض الصور بتنسيقات الجيل القادم"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "يمكنك استخدام طريقة التحميل الكسول للصور خارج الشاشة والصور المخفية بعد الانتهاء من تحميل جميع الموارد المُهمّة لتقليل وقت التفاعل. تعرَّف على [كيفية تأجيل تحميل الصور خارج الشاشة](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "تأجيل الصور خارج الشاشة"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "تحظر الموارد عرض محتوى صفحتك. يمكنك تضمين محتوى JavaScript أو CSS المُهم وتأجيل جميع الأنماط أو محتوى JavaScript غير المُهم. تعرَّف على [كيفية إزالة الموارد التي تحظر العرض](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "استبعاد موارد حظر العرض"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "الحمولات الكبيرة للبيانات على الشبكة تُكلِّف المستخدمين الكثير من الأموال وترتبط إلى حد كبير بأوقات التحميل الطويلة. [تعرَّف على طريقة تقليل حمولات البيانات على الشبكة](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "كان الحجم الإجمالي {totalBytes, number, bytes} كيبيبايت."}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "تجنُّب الأحمال الضخمة للشبكة"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "تجنُّب الأحمال الضخمة للشبكة"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "يمكن أن يؤدي تصغير ملفات CSS إلى تقليل أحجام حمولات البيانات على الشبكة. تعرَّف على [كيفية تصغير ملفات CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "تصغير CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "يمكن أن يؤدي تصغير ملفات JavaScript إلى تقليل أحجام الحمولات ووقت تحليل النصوص البرمجية. تعرَّف على [كيفية تصغير ملف JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "تصغير JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "يمكنك الحدّ من القواعد غير المستخدَمة الواردة في أوراق الأنماط، كما يمكنك تأجيل تحميل محتوى CSS غير المستخدَم في الجزء المرئي من الصفحة لتقليل وحدات البايت التي يستهلكها نشاط الشبكة. تعرَّف على [كيفية الحدّ من محتوى CSS غير المستخدَم](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "الحدّ من محتوى CSS غير المُستخدَم"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "يمكنك الحدّ من محتوى JavaScript غير المستخدَم وتأجيل تحميل النصوص البرمجية إلى حين الحاجة إليها لتقليل وحدات البايت التي يستهلكها نشاط الشبكة. تعرَّف على [كيفية الحدّ من محتوى JavaScript غير المستخدَم](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "الحدّ من محتوى JavaScript غير المستخدَم"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "يمكن لفترة التخزين المؤقت الطويلة زيادة سرعة الزيارات المتكررة إلى صفحتك. [مزيد من المعلومات حول سياسات ذاكرة التخزين المؤقت الفعّالة](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على مورد واحد}zero{تم العثور على # مورد}two{تم العثور على مورديْنِ (#)}few{تم العثور على # موارد}many{تم العثور على # موردًا}other{تم العثور على # مورد}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "عرض الأصول الثابتة من خلال سياسة ذاكرة التخزين المؤقت الفعالة"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "استخدام سياسة ذاكرة التخزين المؤقت الفعالة على الأصول الثابتة"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "يتم تحميل الصور المحسَّنة بشكلٍ أسرع وتستهلك كمية أقل من بيانات شبكة الجوّال. تعرَّف على [كيفية ترميز الصور بكفاءة](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "ترميز الصور بكفاءة"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "الأبعاد الفعلية"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "الأبعاد المعروضة"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "كانت الصور أكبر من حجمها المعروض"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "كانت الصور مناسبة لحجمها المعروض"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "يمكنك عرض صور بحجم مناسب لحفظ بيانات شبكة الجوّال وتحسين وقت التحميل. تعرَّف على [طريقة تحديد حجم الصور](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "الصور ذات الحجم المناسب"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "يجب عرض الموارد المستنِدة إلى النص باستخدام أدوات الضغط (من خلال gzip أو deflate أو brotli) لتقليل إجمالي وحدات البايت على الشبكة. [مزيد من المعلومات حول ضغط النص](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "تفعيل ضغط النص"}, "core/audits/content-width.js | description": {"message": "إذا كان عرض محتوى التطبيق لا يتطابق مع عرض إطار العرض، قد لا يتم تحسين تطبيقك ليتوافق مع شاشات الأجهزة الجوّالة. تعرّف على [كيفية تحديد حجم المحتوى بما يتناسب مع إطار العرض](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "لا يتطابق حجم إطار العرض {innerWidth} بكسل مع حجم النافذة {outerWidth} بكسل."}, "core/audits/content-width.js | failureTitle": {"message": "عدم تحديد حجم المحتوى بشكلٍ صحيح لإطار العرض"}, "core/audits/content-width.js | title": {"message": "تحديد حجم المحتوى بشكلٍ صحيح لإطار العرض"}, "core/audits/critical-request-chains.js | description": {"message": "توضّح لك \"سلاسل الطلبات المُهمّة\" أدناه الموارد التي تم تحميلها بأولوية عالية. ويمكنك تقليل طول السلاسل أو تقليل حجم تنزيل الموارد أو تأجيل تنزيل الموارد غير الضرورية لتحسين تحميل الصفحة. تعرَّف على [كيفية تجنُّب تسلسل الطلبات المُهمّة](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على سلسلة واحدة}zero{تم العثور على # سلسلة}two{تم العثور على سلسلتيْنِ (#)}few{تم العثور على # سلاسل}many{تم العثور على # سلسلةً}other{تم العثور على # سلسلة}}"}, "core/audits/critical-request-chains.js | title": {"message": "تجنَّب سلاسل الطلبات المهمة"}, "core/audits/csp-xss.js | columnDirective": {"message": "أمر توجيهي"}, "core/audits/csp-xss.js | columnSeverity": {"message": "درجة الخطورة"}, "core/audits/csp-xss.js | description": {"message": "تقلِّل سياسة أمان المحتوى (CSP) القوية بشكل كبير من خطر الهجمات التي تستخدم النصوص البرمجية على المواقع الإلكترونية (XSS). تعرَّف على [كيفية استخدام سياسة أمان المحتوى لمنع الهجمات التي تستخدم النصوص البرمجية على المواقع الإلكترونية (XSS)](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "البنية"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "تحتوي الصفحة على سياسة أمان محتوى (CSP) تم تحديدها في علامة <meta>. يمكنك نقل سياسة CSP إلى عنوان HTTP أو تحديد سياسة CSP صارمة أخرى في عنوان HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "لم يتم العثور على سياسة CSP في وضع \"التنفيذ\"."}, "core/audits/csp-xss.js | title": {"message": "التأكُّد من فاعلية سياسة CSP ضد هجمات XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "إيقاف / تحذير"}, "core/audits/deprecations.js | columnLine": {"message": "السطر"}, "core/audits/deprecations.js | description": {"message": "ستتم في النهاية إزالة واجهات برمجة التطبيقات المتوقِّفة نهائيًا من المتصفِّح. [مزيد من المعلومات حول واجهات برمجة التطبيقات التي تم إيقافها نهائيًا](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على تحذير واحد}zero{تم العثور على # تحذير}two{تم العثور على تحذيرين (#)}few{تم العثور على # تحذيرات}many{تم العثور على # تحذيرًا}other{تم العثور على # تحذير}}"}, "core/audits/deprecations.js | failureTitle": {"message": "يتم استخدام واجهات برمجة التطبيقات المتوقفة"}, "core/audits/deprecations.js | title": {"message": "يتم تجنّب واجهات برمجة التطبيقات المتوقفة"}, "core/audits/dobetterweb/charset.js | description": {"message": "يجب تعريف ترميز الأحرف. ويمكن إجراء ذلك باستخدام علامة `<meta>` في أول 1024 وحدة بايت من عنوان HTML أو في عنوان استجابة HTTP لنوع المحتوى. [مزيد من المعلومات حول تعريف ترميز الأحرف](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "تعريف ترميز الأحرف غير متوفر أو تأخر ظهوره جدًا في HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "تحديد ترميز الأحرف بشكلٍ صحيح"}, "core/audits/dobetterweb/doctype.js | description": {"message": "يؤدي تحديد doctype إلى منع المتصفِّح من التبديل إلى وضع Quirks. [مزيد من المعلومات حول بيان DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "يجب أن يكون اسم DOCTYPE هو سلسلة `html`."}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "يحتوي المستند على \"`doctype`\"، ما يؤدي إلى تشغيل \"`limited-quirks-mode`\"."}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "يجب أن يحتوي المستند على DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "من الممكن أن تكون publicId المتوقعة سلسلة فارغة"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "من الممكن أن تكون systemId المتوقعة سلسلة فارغة"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "يحتوي المستند على \"`doctype`\"، ما يؤدي إلى تشغيل \"`quirks-mode`\"."}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "تفتقر الصفحة إلى HTML DOCTYPE، مما يؤدي إلى تشغيل وضع Quirks"}, "core/audits/dobetterweb/doctype.js | title": {"message": "الصفحة تحتوي على HTML DOCTYPE"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "الإحصائية"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "القيمة"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "سيزيد حجم عناصر DOM الكبير من استخدام الذاكرة، وسيتسبب في إجراء [حسابات للأنماط](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) تستغرق مدة أطول، بالإضافة إلى إنتاج عمليات مُكلِفة [لإعادة تدفق التنسيقات](https://developers.google.com/speed/articles/reflow). تعرَّف على [كيفية تجنُّب زيادة حجم عناصر DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{عنصر واحد}zero{# عنصر}two{عنصرين (#)}few{# عناصر}many{# عنصرًا}other{# عنصر}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "تجنُب حجم DOM الزائد"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "الح<PERSON> الأقصى لعمق DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "إجمالي عدد عناصر DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "ال<PERSON><PERSON> الأقصى من عناصر الأطفال"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "تجنُب حجم DOM الزائد"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "لا يثق المستخدمون في المواقع الإلكترونية التي تطلب مواقعهم الجغرافية بدون سياق أو قد يؤدي ذلك إلى إرباكهم. يمكنك ربط الطلب بإجراء المستخدم بدلاً من ذلك. [مزيد من المعلومات حول إذن رصد الموقع الجغرافي](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "يتم طلب إذن رصد الموقع الجغرافي عند تحميل الصفحة"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "يتم تجنُب طلب إذن رصد الموقع الجغرافي عند تحميل الصفحة"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "نوع المشكلة"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "تشير المشاكل التي تم تسجيلها في لوحة `Issues` ضِمن \"أدوات مطوري البرامج في Chrome\" إلى وجود مشاكل لم يتم حلها. قد تنتج هذه المشاكل بسبب إخفاقات في طلبات الشبكة وعدم توفّر عناصر كافية للتحكّم في الأمان ومشاكل أخرى تتعلق بالمتصفِّح. للاطّلاع على مزيد من التفاصيل عن كل مشكلة، يمكنك الانتقال إلى لوحة Issues (لوحة \"المشاكل\") ضِمن \"أدوات مطوري البرامج في Chrome\"."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "تم تسجيل المشاكل في لوحة `Issues` ضِمن \"أدوات مطوري البرامج في Chrome\""}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "تم حظر المورد وفقًا لسياسة مشاركة الموارد عبر المصادر الخاصة بالموقع الإلكتروني."}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "تستخدم الإعلانات موارد المتصفِّح بكثافة."}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "ما مِن مشاكل في لوحة `Issues` ضِمن \"أدوات مطوري البرامج في Chrome\""}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "الإصدار"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "تم رصد جميع مكتبات JavaScript للواجهة الأمامية على الصفحة. [مزيد من المعلومات حول التدقيق في بيانات التشخيص لرصد مكتبة JavaScript هذه](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "مكتبات JavaScript التي تم رصدها"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "بالنسبة إلى المستخدمين الذين لديهم اتصالات بطيئة، يمكن أن تؤدي النصوص البرمجية الخارجية التي يتم إدخالها ديناميكيًا من خلال `document.write()` إلى تأخير تحميل الصفحة لمدة ثوانٍ متعددة. تعرَّف على [كيفية تجنُّب document.write()‎](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "تجنَّب `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "يتم تجنُب `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "لا يثق المستخدمون في المواقع الإلكترونية التي تطلب إرسال الإشعارات بدون سياق أو قد يؤدي ذلك إلى إرباكهم. يمكنك ربط الطلب بإيماءات المستخدم بدلاً من ذلك. [مزيد من المعلومات حول الحصول على إذن بشأن الإشعارات بشكلٍ مسؤول](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "يتم طلب إذن الإشعار عند تحميل الصفحة"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "يتم تجنُّب طلب إذن الإشعار عند تحميل الصفحة"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "البروتوكول"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "يوفّر HTTP/2 العديد من المزايا مقارنةً بمزايا HTTP/1.1، بما في ذلك عناوين البرامج الثنائية وعملية مضاعفة توجيه الإشارات. [مزيد من المعلومات حول مزايا HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{لم يتم عرض طلب واحد عبر HTTP/2}zero{لم يتم عرض # طلب عبر HTTP/2}two{لم يتم عرض طلبين (#) عبر HTTP/2}few{لم يتم عرض # طلبات عبر HTTP/2}many{لم يتم عرض # طلبًا عبر HTTP/2}other{لم يتم عرض # طلب عبر HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "استخدام HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "يمكنك وضع علامة على \"أدوات معالجة أحداث لمس الشاشة وتحريك الماوس\" بصفتها `passive` لتحسين عملية التنقل في صفحتك. [مزيد من المعلومات حول استخدام أدوات معالجة الأحداث السلبية](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "لا يتم استخدام أدوات معالجة الحدث السلبية لتحسين عملية التنقل في الصفحة"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "يتم استخدام أدوات معالجة الحدث السلبية لتحسين أداء التمرير"}, "core/audits/errors-in-console.js | description": {"message": "تشير الأخطاء التي تم تسجيلها في وحدة التحكّم إلى مشاكل لم يتم حلها. قد تنتج هذه المشاكل بسبب إخفاقات في طلبات الشبكة ومشاكل أخرى تتعلق بالمتصفِّح. [مزيد من المعلومات حول عملية التدقيق في بيانات التشخيص](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "تم تسجيل أخطاء المتصفح في وحدة التحكّم"}, "core/audits/errors-in-console.js | title": {"message": "لم يتم تسجيل أخطاء المتصفح في وحدة التحكّم"}, "core/audits/font-display.js | description": {"message": "يمكنك الاستفادة من ميزة `font-display` في CSS لضمان أن يكون النص مرئيًا للمستخدم أثناء تحميل خطوط موقع ويب. [مزيد من المعلومات حول `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "التأكد من بقاء النص مرئيًا أثناء تحميل خط موقع إلكتروني"}, "core/audits/font-display.js | title": {"message": "تظل جميع النصوص مرئية أثناء تحميل خط موقع إلكتروني"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{لم تتمكّن أداة Lighthouse من التحقّق تلقائيًا من قيمة `font-display` في أصل واحد ({fontOrigin}).}zero{لم تتمكّن أداة Lighthouse من التحقّق تلقائيًا من قيمة `font-display` في {fontOrigin} أصل.}two{لم تتمكّن أداة Lighthouse من التحقّق تلقائيًا من قيمة `font-display` في أصلَين ({fontOrigin}).}few{لم تتمكّن أداة Lighthouse من التحقّق تلقائيًا من قيمة `font-display` في {fontOrigin} أصول.}many{لم تتمكّن أداة Lighthouse من التحقّق تلقائيًا من قيمة `font-display` في {fontOrigin} أصلاً.}other{لم تتمكّن أداة Lighthouse من التحقّق تلقائيًا من قيمة واحدة (`font-display`) في {fontOrigin} أصل.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "نسبة العرض إلى الارتفاع (الفعلية)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "نسبة العرض إلى الارتفاع (معروضة)"}, "core/audits/image-aspect-ratio.js | description": {"message": "يجب أن تتوافق أبعاد عرض الصورة مع نسبة العرض إلى الارتفاع الطبيعية. [مزيد من المعلومات حول نسبة العرض إلى الارتفاع للصورة](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "يتم عرض الصور مع نسبة عرض إلى ارتفاع غير صحيحة"}, "core/audits/image-aspect-ratio.js | title": {"message": "يتم عرض الصور مع نسبة العرض إلى الارتفاع الصحيحة"}, "core/audits/image-size-responsive.js | columnActual": {"message": "الحجم الفعلي"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "الحجم المعروض"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "الحجم المتوقع"}, "core/audits/image-size-responsive.js | description": {"message": "يجب أن تكون الأبعاد الطبيعية للصورة متناسبة مع حجم العرض ونسبة وحدة البكسل لزيادة وضوح الصورة إلى أقصى حد. تعرَّف على [كيفية تقديم صور سريعة الاستجابة](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "يتم عرض الصور بدقة منخفضة"}, "core/audits/image-size-responsive.js | title": {"message": "يتم عرض الصور بدقة مناسبة"}, "core/audits/installable-manifest.js | already-installed": {"message": "سبق وتم تثبيت هذا التطبيق."}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "تعذَّر تنزيل رمز مطلوب من ملف البيان."}, "core/audits/installable-manifest.js | columnValue": {"message": "سبب الإخفاق"}, "core/audits/installable-manifest.js | description": {"message": "مشغّل الخدمات هو التكنولوجيا التي تمكّن تطبيقك من استخدام ميزات عديدة في \"تطبيق الويب التقدّمي\"، مثل الاستجابة عند عدم الاتصال بالإنترنت والإضافة إلى الشاشة الرئيسية والإشعارات الفورية. من خلال مشغّل الخدمات المناسب وعمليات تنفيذ ملفات البيان، يمكن للمتصفِّحات أن تطلب من المستخدمين بشكل مسبَق إضافة تطبيقك إلى الشاشة الرئيسية، ويمكن بذلك زيادة التفاعل. [مزيد من المعلومات حول متطلبات تثبيت البيان](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{سبب واحد}zero{# أسباب}two{سببان}few{# أسباب}many{# سببًا}other{# سبب}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "عدم استيفاء ملف بيان تطبيق الويب أو مشغّل الخدمات لمتطلبات التثبيت"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "لا يتطابق عنوان URL الخاص بالتطبيق في \"متجر Play\" مع رقم تعريف التطبيق في \"متجر Play\"."}, "core/audits/installable-manifest.js | in-incognito": {"message": "تم تحميل الصفحة في نافذة تصفُّح متخفٍ."}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "يجب أن تكون خاصية \"display\" في ملف البيان بالقيمة \"standalone\" أو \"fullscreen\" أو \"minimal-ui\"."}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "يتضمن ملف البيان حقل \"display_override\"، لذا يجب أن يكون وضع العرض الأول المتاح بالقيمة \"standalone\" أو \"fullscreen\" أو \"minimal-ui\"."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "البيان فارغ أو تعذّر استرجاعه أو لم يتم تحليله."}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "تم تغيير عنوان URL لملف البيان أثناء استرجاع الملف."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "لا يتضمّن ملف البيان حقل \"name\" أو \"short_name\"."}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "لا يتضمّن ملف البيان رمزًا مناسبًا. يجب استخدام رمز بتنسيق PNG أو SVG أو WebP بحجم {value0} بكسل على الأقل، ويجب ضبط السمة \"sizes\"، وأن تتضمّن السمة \"purpose\" القيمة \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "لا يتوفّر رمز بحجم {value0} بكسل مربّع على الأقل بتنسيق PNG أو SVG أو WebP، بغرض عدم ضبط السمة أو ضبطها على \"أي تنسيق\"."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "الرمز الذي تم تحميله فارغ أو تالف."}, "core/audits/installable-manifest.js | no-id-specified": {"message": "لم يتم توفير رقم تعريف \"متجر Play\"."}, "core/audits/installable-manifest.js | no-manifest": {"message": "لا تتضمّن الصفحة عنوان URL <link> لملف البيان."}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "لم يتم رصد مشغّل خدمات مطابق. قد تحتاج إلى إعادة تحميل الصفحة، أو التأكّد أن نطاق مشغّل الخدمات للصفحة الحالية يشتمل على النطاق وعنوان URL البداية من ملف البيان."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "تعذّر التحقّق من مشغّل الخدمات بدون توفّر حقل \"start_url\" في ملف البيان."}, "core/audits/installable-manifest.js | noErrorId": {"message": "لم يتم التعرّف على رقم تعريف خطأ قابلية التثبيت \"{errorId}\"."}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "لا يتم عرض الصفحة من مصدر آمن."}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "لا يتم تحميل الصفحة في الإطار الرئيسي."}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "لا تعمل هذه الصفحة بلا إنترنت."}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "تم إلغاء تثبيت تطبيق الويب التقدّمي (PWA) وتتم حاليًا إعادة ضبط عمليات التحقّق من قابلية التثبيت."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "لا تتوفّر منصة التطبيقات المحدّدة على Android."}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "يضبط ملف البيان قيمة prefer_related_applications على true."}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "لا يتوافق حقل الإدخال prefer_related_applications إلا مع الإصدار التجريبي من متصفِّح Chrome والقنوات الثابتة على Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "تعذَّر على أداة Lighthouse تحديد ما إذا كان يتوفّر مشغّل خدمات. يُرجى إعادة المحاولة باستخدام إصدار أحدث من متصفّح Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "مخطّط عنوان URL الخاص بملف البيان ({scheme}) غير متاح لنظام التشغيل Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "عنوان URL البداية لملف البيان غير صالح."}, "core/audits/installable-manifest.js | title": {"message": "استيفاء ملف بيان تطبيق الويب ومشغّل الخدمات لمتطلبات التثبيت"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "يحتوي عنوان URL في ملف البيان على اسم مستخدم أو كلمة مرور أو منفذ."}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "لا تعمل هذه الصفحة بلا إنترنت. وستصبح هذه الصفحة غير قابلة للتثبيت بعد إطلاق الإصدار الثابت من Chrome 93 في آب (أغسطس) 2021."}, "core/audits/is-on-https.js | allowed": {"message": "مسموح به"}, "core/audits/is-on-https.js | blocked": {"message": "م<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "عنوان URL غير آمن"}, "core/audits/is-on-https.js | columnResolution": {"message": "معالجة الطلبات"}, "core/audits/is-on-https.js | description": {"message": "يجب حماية جميع المواقع الإلكترونية باستخدام HTTPS، حتى تلك المواقع التي لا تتعامل مع البيانات الحسّاسة. تتضمّن هذه الحماية تجنُّب [المحتوى المختلَط](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) حيث يتم تحميل بعض الموارد على HTTP على الرغم من عرض الطلب الأوّلي على HTTPS. ويمنع HTTPS الدخلاء من العبث بالاتصالات بين تطبيقك والمستخدمين أو التنصّت عليها، وهو شرط مُسبَق لبروتوكول HTTP/2 والعديد من واجهات برمجة التطبيقات الجديدة للأنظمة الأساسية على الويب. [مزيد من المعلومات حول HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على طلب غير آمن واحد}zero{تم العثور على # طلب غير آمن}two{تم العثور على طلبين غير آمنين (#)}few{تم العثور على # طلبات غير آمنة}many{تم العثور على # طلبًا غير آمن}other{تم العثور على # طلب غير آمن}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "لا يتم استخدام HTTPS"}, "core/audits/is-on-https.js | title": {"message": "يتم استخدام HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "تمت ترقيته إلى HTTPS تلقائيًا"}, "core/audits/is-on-https.js | warning": {"message": "مسموح به مع التحذير"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "هذا هو الجزء الأكبر من المحتوى الذي تم عرضه على الصفحة ضمن إطار العرض. [مزيد من المعلومات حول مقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "عنصر \"سرعة عرض أكبر جزء من المحتوى على الصفحة\""}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "المساهمة في متغيّرات التصميم التراكمية (CLS)"}, "core/audits/layout-shift-elements.js | description": {"message": "تساهم عناصر DOM هذه أكثر في متغيّرات التصميم التراكمية (CLS) الخاصة بالصفحة. تعرَّف على [كيفية تحسين CLS](https://web.dev/optimize-cls/)."}, "core/audits/layout-shift-elements.js | title": {"message": "تجنُّب متغيّرات التصميم الكبيرة"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "تُعرَض لاحقًا الصور التي تم تحميلها في الجزء المرئي من الصفحة باستخدام طريقة التحميل الكسول، ما يتسبب في تأخير عرض أكبر جزء من المحتوى على الصفحة. [مزيد من المعلومات حول طريقة التحميل الكسول المحسَّنة](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "تم عرض أكبر صورة ظاهرة في الصفحة بسرعة غير مناسبة"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "تم عرض أكبر صورة ظاهرة في الصفحة بسرعة مناسبة"}, "core/audits/long-tasks.js | description": {"message": "يتم إدراج المهام التي تستغرق وقتًا أطول في سلسلة التعليمات الرئيسية، ما يساعد في تحديد أكثر العوامل التي تسبِّب تأخيرًا في عملية الإدخال. تعرَّف على [كيفية تجنُّب المهام التي تستغرق وقتًا طويلاً والمُدرَجة في سلسلة التعليمات الرئيسية](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على مهمّة طويلة واحدة}zero{تم العثور على # مهمّة طويلة}two{تم العثور على مهمّتَين طويلتَين}few{تم العثور على # مهمّات طويلة}many{تم العثور على # مهمّة طويلة}other{تم العثور على # مهمّة طويلة}}"}, "core/audits/long-tasks.js | title": {"message": "نجنُّب سلسة المهام الرئيسية الطويلة"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "الفئة"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "يمكنك تقليل الوقت المستغرَق في تحليل بيانات JavaScript وتجميعها وتنفيذها. قد يتبيّن لك أنّ عرض حمولات JavaScript بحجم أصغر يساعد على ذلك. تعرَّف على [كيفية تقليل سلسلة العمل الرئيسية](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "تقليل سلسلة العمل الرئيسية"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "تقليل سلسلة العمل الرئيسية"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "للوصول إلى أكبر عدد من المستخدمين، يجب إتاحة استخدام المواقع الإلكترونية على كل متصفِّح رئيسي. تعرَّف على [كيفية توافق المواقع الإلكترونية مع جميع المتصفِّحات](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "الموقع الإلكتروني يعمل عبر المتصفح"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "يُرجى التأكّد من إمكانية إنشاء روابط تؤدي إلى الصفحات الفردية من خلال عناوين URL، ويجب أن تكون عناوين URL هذه فريدة لتوفير إمكانية المشاركة على وسائل التواصل الاجتماعي. [مزيد من المعلومات عن كيفية توفير روابط لصفحات معيّنة](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "كل صفحة تحتوي على عنوان URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "يجب أن تكون عمليات الانتقال بين الصفحات سريعة، حتى عند استخدام شبكة بطيئة، وذلك لأنّ سرعة استجابة التطبيق تُعدّ من العناصر الأساسية التي يستند إليها المستخدم في تقييم أداء التطبيق. [مزيد من المعلومات حول عمليات الانتقال بين الصفحات](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "عمليات نقل الصفحة لا تبدو أنها محظورة على الشبكة"}, "core/audits/maskable-icon.js | description": {"message": "يضمن الرمز التكيُّفي تعبئة الصورة للشكل بالكامل بدون أن تصبح مُعدَّة للعرض على شاشة عريضة أفقيًا أثناء تثبيت التطبيق على أحد الأجهزة. [مزيد من المعلومات حول رموز البيانات التكيُّفية](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "البيان لا يحتوي على رمز قابل للإخفاء"}, "core/audits/maskable-icon.js | title": {"message": "البيان يحتوي على رمز قابل للإخفاء"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "يحدِّد مقياس \"متغيّرات التصميم التراكمية\" مقدار حركة العناصر المرئية في إطار العرض. [مزيد من المعلومات حول مقياس \"متغيّرات التصميم التراكمية\"](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "يحدِّد مقياس \"مدة عرض الاستجابة لتفاعل المستخدم\" سرعة استجابة الصفحة والمدّة التي تستغرِقها الصفحة للاستجابة بشكل واضح للبيانات التي أدخلها المستخدم. [مزيد من المعلومات حول مقياس \"مدة عرض الاستجابة لتفاعل المستخدم\"](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "يحدِّد مقياس \"سرعة عرض المحتوى على الصفحة\" الوقت الذي يُعرَض فيه أول نص أو صورة من محتوى الصفحة. [مزيد من المعلومات حول مقياس \"سرعة عرض المحتوى على الصفحة\"](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "يوضِّح مقياس \"سرعة عرض أوّل محتوى مفيد على الصفحة\" الوقت الذي تم فيه عرض المحتوى الأساسي لإحدى الصفحات. [مزيد من المعلومات حول مقياس \"سرعة عرض أوّل محتوى مفيد على الصفحة\"](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "\"وقت التفاعل\" هو مقدار الوقت المستغرَق حتى تصبح الصفحة تفاعلية بالكامل. [مزيد من المعلومات حول مقياس \"وقت التفاعل\"](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "يحدِّد مقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة\" المدة التي يتم خلالها عرض أكبر صورة أو نص. [مزيد من المعلومات حول مقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "مقياس \"الحد الأقصى المحتمَل لمهلة الاستجابة لأوّل إدخال\" هو أطول مدّة تستغرقها إحدى المهام استجابةً لإدخال المستخدمين. [مزيد من المعلومات حول مقياس \"الحد الأقصى المحتمَل لمهلة الاستجابة لأوّل إدخال\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "يوضّح مؤشر السرعة وتيرة تعبئة محتوى الصفحة على شاشة المستخدم. [مزيد من المعلومات حول مقياس مؤشر السرعة](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "مجموع الفترات الزمنية بين \"سرعة عرض المحتوى على الصفحة\" و\"وقت التفاعل\" عندما تتجاوز مدّة المهمة 50 ملي ثانية، معبرًا عنها بالملي ثانية. [مزيد من المعلومات حول مقياس \"إجمالي وقت الحظر\"](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "تؤثر مُدد إرسال البيانات واستقبالها على الشبكة تأثيرًا كبيرًا في مستوى الأداء. في حال كانت مدّة الإرسال والاستقبال طويلة، يشير ذلك إلى احتمال تحسُّن أداء الخوادم الأقرب إلى المستخدم. [مزيد من المعلومات حول مدة إرسال البيانات واستقبالها على الشبكة](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "أوقات إرسال واستقبال الشبكة"}, "core/audits/network-server-latency.js | description": {"message": "قد تؤثر أوقات استجابة الخادم في أداء الويب. في حال كان وقت استجابة الخادم للمصدر طويلاً، يشير هذا إلى أنّه تم تحميل الخادم بشكلٍ زائد أو أنّ مستوى أدائه ضعيف في الخلفية. [مزيد من المعلومات حول وقت استجابة الخادم](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "أوقات الاستجابة لواجهة الخادم الخلفية"}, "core/audits/no-unload-listeners.js | description": {"message": "لا يعمل حدث `unload` بفاعلية ويمكن أن تؤدي معالجته إلى عدم تنفيذ عمليات تحسين المتصفّح، مثل عملية \"التخزين المؤقت للصفحات\". وبدلاً من هذا الحدث، يمكنك استخدام `pagehide` أو `visibilitychange`. [مزيد من المعلومات حول إلغاء تحميل أدوات معالجة الأحداث](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "يتم استخدام أدوات معالجة حدث `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "لا يتم استخدام أدوات معالجة حدث `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "يمكن للصور المتحركة غير المركّبة أن تكون بجودة رديئة وأن تزيد متغيّرات التصميم التراكمية (CLS). تعرَّف على [كيفية تجنُّب استخدام الصور المتحركة غير المركّبة](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على عنصر متحرك واحد}zero{تم العثور على # عنصر متحرك}two{تم العثور على عنصرَين متحركَين}few{تم العثور على # عناصر متحركة}many{تم العثور على # عنصرًا متحركًا}other{تم العثور على # عنصر متحرك}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "قد تحرّك الخاصية المتعلّقة بالفلتر وحدات بكسل."}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "يتم استهداف صورة متحركة أخرى غير متوافقة."}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "يحتوي \"التأثير\" على وضع مركّب غير \"replace\"."}, "core/audits/non-composited-animations.js | title": {"message": "تجنُّب الصور المتحركة غير المركّبة"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "تعتمد الخاصية المتعلّقة بالتحويل على حجم المربع."}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{خاصية CSS غير المتوافقة: {properties}}zero{خصائص CSS غير المتوافقة: {properties}}two{خاصيتا CSS غير المتوافقتَين: {properties}}few{خصائص CSS غير المتوافقة: {properties}}many{خصائص CSS غير المتوافقة: {properties}}other{خصائص CSS غير المتوافقة: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "يحتوي \"التأثير\" على مَعلَمات توقيت غير متوافقة"}, "core/audits/performance-budget.js | description": {"message": "يمكنك الحفاظ على كمية طلبات الشبكة وحجمها ضِمن الأهداف المحدَّدة في الميزانية القائمة على الأداء التي تم تقديمها. [مزيد من المعلومات حول الميزانيات القائمة على الأداء](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{طلب واحد}zero{# طلب}two{طلبان (#)}few{# طلبات}many{# طلبًا}other{# طلب}}"}, "core/audits/performance-budget.js | title": {"message": "ميزانية الأداء"}, "core/audits/preload-fonts.js | description": {"message": "يجب تحميل الخطوط `optional` بشكل مسبق حتى يتسنى للزوار الجدد استخدامها. [مزيد من المعلومات عن التحميل المسبَق للخطوط](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "الخطوط التي استخدمت `font-display: optional` لم يتم تحميلها مُسبَقًا"}, "core/audits/preload-fonts.js | title": {"message": "تم تحميل الخطوط التي استخدمت `font-display: optional` مُسبَقًا"}, "core/audits/prioritize-lcp-image.js | description": {"message": "في حال إضافة المقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة\" (LCP) إلى الصفحة بشكل ديناميكي، يجب تحميل الصورة مسبقًا لتحسين مقياس LCP. [مزيد من المعلومات حول التحميل المسبق لعناصر LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "التحميل المُسبَق لصورة المقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة\""}, "core/audits/redirects.js | description": {"message": "تؤدي عمليات إعادة التوجيه إلى حدوث تأخيرات إضافية قبل أن يتم تحميل الصفحة. تعرَّف على [كيفية تجنُّب عمليات إعادة توجيه الصفحة](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "تجنُب عمليات إعادة توجيه الصفحات المتعددة"}, "core/audits/resource-summary.js | description": {"message": "لتحديد ميزانيات لكمية موارد الصفحة وحجمها، يمكنك إضافة ملف budget.json. [مزيد من المعلومات حول الميزانيات القائمة على الأداء](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{طلب واحد • {byteCount, number, bytes} كيبيبايت}zero{# طلب • {byteCount, number, bytes} كيبيبايت}two{طلبان • {byteCount, number, bytes} كيبيبايت}few{# طلبات • {byteCount, number, bytes} كيبيبايت}many{# طلبًا • {byteCount, number, bytes} كيبيبايت}other{# طلب • {byteCount, number, bytes} كيبيبايت}}"}, "core/audits/resource-summary.js | title": {"message": "الحفاظ على انخفاض عدد الطلبات ونقل الأحجام الصغيرة"}, "core/audits/seo/canonical.js | description": {"message": "تقترح الروابط الأساسية عنوان URL للعرض في نتائج البحث. [مزيد من المعلومات حول الروابط الأساسية](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "تتعدّد عناوين URL المتضاربة ({urlList})."}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "عنوان URL غير صالح ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "يشير عنوان URL إلى موقع جغرافي `hreflang` آخر ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "عنوان URL غير كامل ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "يشير إلى عنوان URL الجذر للنطاق (الصفحة الرئيسية)، بدلاً من صفحة مكافئة للمحتوى"}, "core/audits/seo/canonical.js | failureTitle": {"message": "المستند لا يحتوي على سمة `rel=canonical` صالحة"}, "core/audits/seo/canonical.js | title": {"message": "المستند يحتوي على سمة `rel=canonical` صالحة"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "الروابط التي لا يمكن الزحف إليها"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "قد تستخدم محركات البحث سمات `href` على الروابط بهدف الزحف إلى المواقع الإلكترونية. يُرجى التأكّد من أنّ سمة `href` لعناصر الارتساء ترتبط بوجهة مناسبة، حيث يمكن اكتشاف مزيد من صفحات الموقع الإلكتروني. تعرَّف على [كيفية إتاحة إمكانية الزحف إلى الروابط](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "لا يمكن الزح<PERSON> إلى الروابط"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "يمكن الزح<PERSON> إل<PERSON> الروابط"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "نص إضافي غير قابل للقراءة"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "حج<PERSON> الخط"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% من نص الصفحة"}, "core/audits/seo/font-size.js | columnSelector": {"message": "أداة الاختيار"}, "core/audits/seo/font-size.js | description": {"message": "تكون أحجام الخطوط الأقل من 12 بكسل صغيرة جدًا بحيث لا يمكن قراءتها بسهولة وتتطلب من مستخدمي الأجهزة الجوّالة \"استخدام الإصبعين للتكبير\" من أجل قراءتها. يُرجى بذل قصارى جهدك لضبط الخطوط في أكثر من ‏60% من نص الصفحة على حجم أكبر من أو يساوي 12 بكسل. [مزيد من المعلومات حول أحجام الخطوط القابلة للقراءة](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "نص {decimalProportion, number, extendedPercent} قابل للقراءة"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "النص غير مقروء لأنه لا تتوفّر علامة وصفية لإطار العرض محسنة لشاشات الجوّال."}, "core/audits/seo/font-size.js | failureTitle": {"message": "لا يستخدم المستند أحجام الخطوط القابلة للقراءة"}, "core/audits/seo/font-size.js | legibleText": {"message": "نص قابل للقراءة"}, "core/audits/seo/font-size.js | title": {"message": "يستخدم المستند أحجام الخط القابلة للقراءة"}, "core/audits/seo/hreflang.js | description": {"message": "توضّح روابط hreflang لمحركات البحث إصدار الصفحة الذي يجب إدراجه في نتائج البحث للغة أو منطقة معيّنة. [مزيد من المعلومات حول `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "المستند لا يحتوي على سمة `hreflang` صالحة"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "قيمة href غير مؤهلة بالكامل."}, "core/audits/seo/hreflang.js | title": {"message": "المستند يحتوي على سمة `hreflang` صالحة"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "رمز اللغة غير متوقّع."}, "core/audits/seo/http-status-code.js | description": {"message": "قد لا يتم إجراء الفهرسة بشكلٍ صحيح للصفحات التي تتضمّن رموز حالة HTTP غير صالحة. [مزيد من المعلومات حول رموز حالة HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "تحتوي الصفحة على رمز حالة HTTP غير صالح"}, "core/audits/seo/http-status-code.js | title": {"message": "تحتوي الصفحة على رمز حالة HTTP صالح"}, "core/audits/seo/is-crawlable.js | description": {"message": "يتعذّر على محركات البحث تضمين صفحاتك في نتائج البحث في حال عدم حصولها على إذن للزحف إلى هذه الصفحات. [مزيد من المعلومات حول توجيهات الزاحف](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "يتم حظر الصفحة من الفهرسة"}, "core/audits/seo/is-crawlable.js | title": {"message": "الصفحة ليست محظورة من الفهرسة"}, "core/audits/seo/link-text.js | description": {"message": "يساعد نص الرابط الوصفي محركات البحث على فهم المحتوى. تعرَّف على [كيفية تسهيل الوصول إلى الروابط](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على رابط واحد}zero{تم العثور على # رابط}two{تم العثور على رابطين (#)}few{تم العثور على # روابط}many{تم العثور على # رابطًا}other{تم العثور على # رابط}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "عدم احتواء الروابط على نص وصفي"}, "core/audits/seo/link-text.js | title": {"message": "تحتوي الروابط على نص وصفي"}, "core/audits/seo/manual/structured-data.js | description": {"message": "يمكنك تشغيل [أداة اختبار البيانات المنظَّمة](https://search.google.com/structured-data/testing-tool/) وأداة [Structured Data Linter](http://linter.structured-data.org/) للتحقّق من البيانات المنظَّمة. [مزيد من المعلومات حول البيانات المنظَّمة](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "البيانات المنظَّمة صالحة"}, "core/audits/seo/meta-description.js | description": {"message": "قد يتم تضمين الأوصاف التعريفية في نتائج البحث لتلخيص محتوى الصفحة بإيجاز. [مزيد من المعلومات عن الوصف التعريفي](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "نص الوصف فارغ."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "لا يحتوي المستند على وصف تعريفي"}, "core/audits/seo/meta-description.js | title": {"message": "يحتوي المستند على وصف تعريفي"}, "core/audits/seo/plugins.js | description": {"message": "لا يمكن لمحركات البحث فهرسة محتوى مكوِّن إضافي، وتحظر العديد من الأجهزة استخدام المكوِّنات الإضافية أو لا تتوافق معها. [مزيد من المعلومات حول تجنُّب استخدام المكوِّنات الإضافية](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "يستخدم المستند مكونات إضافية"}, "core/audits/seo/plugins.js | title": {"message": "يتجنّب المستند المكونات الإضافية"}, "core/audits/seo/robots-txt.js | description": {"message": "في حال كان ملف robots.txt مكتوبًا بصيغة غير صحيحة، يمكن أن يتعذّر على برامج الزحف فهم الطريقة المطلوبة للزحف إلى موقعك الإلكتروني أو فهرسته. [مزيد من المعلومات حول ملف robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "الطلب لملف robots.txt عرض حالة HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{تم العثور على خطأ واحد}zero{تم العثور على # خطأ}two{تم العثور على خطأين (#)}few{تم العثور على # أخطاء}many{تم العثور على # خطأً}other{تم العثور على # خطأ}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "تعذّر على Lighthouse تنزيل ملف robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "ملف \"robots.txt\" غير صالح"}, "core/audits/seo/robots-txt.js | title": {"message": "ملف \"robots.txt\" صالح"}, "core/audits/seo/tap-targets.js | description": {"message": "يجب أن تكون العناصر التفاعلية، مثل الأزرار والروابط، كبيرة بشكلٍ كافٍ (48 × 48 بكسل) وأن تحيط بها مساحة كافية ليكون من السهل النقر عليها بدون النقر على أي عناصر أخرى. [مزيد من المعلومات حول أهداف النقر](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "تم تحديد حجم {decimalProportion, number, percent} لأهداف النقر بشكلٍ مناسب"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "أهدا<PERSON> النقر صغيرة جدًا لأنه لا تتوفّر علامة وصفية لإطار العرض محسنة لشاشات الجوّال."}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "لم يتم تحديد حجم أهداف النقر بشكل مناسب"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "استهداف متداخِل"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON> النقر"}, "core/audits/seo/tap-targets.js | title": {"message": "يتم تحديد حجم أهداف النقر بشكل مناسب"}, "core/audits/server-response-time.js | description": {"message": "يجب إبقاء وقت استجابة الخادم للمستند الرئيسي قصيرًا، حيث تعتمد جميع الطلبات الأخرى على هذا الإجراء. [مزيد من المعلومات حول مقياس \"وقت وصول أول بايت\"](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "استغرق مستند الجذر {timeInMs, number, milliseconds} مللي ثانية"}, "core/audits/server-response-time.js | failureTitle": {"message": "تقليل وقت استجابة الخادم الأوّلي"}, "core/audits/server-response-time.js | title": {"message": "وقت استجابة الخادم الأوّلي قصير"}, "core/audits/service-worker.js | description": {"message": "مشغّل الخدمات هو التكنولوجيا التي تمكّن تطبيقك من استخدام ميزات عديدة في \"تطبيق الويب التقدّمي\"، مثل الاستجابة عند عدم الاتصال بالإنترنت والإضافة إلى الشاشة الرئيسية والإشعارات الفورية. [مزيد من المعلومات حول مشغِّلات الخدمات](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "يتحكم مشغّل الخدمات في هذه الصفحة، ومع ذلك لم يتم العثور على `start_url` بسبب تعذّر تحليل البيان كملف JSON صالح"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "يتحكم مشغّل الخدمات في هذه الصفحة، ومع ذلك لا يتوفر `start_url` ({startUrl}) في نطاق مشغّل الخدمات ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "يتحكم مشغّل الخدمات في هذه الصفحة، ومع ذلك لم يتم العثور على `start_url` لأنه لم يتم جلب أي بيان."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "تحتوي نقطة الانطلاق هذه على مشغّل خدمات واحد أو أكثر، ولكن لا يوجد مشغّل يتحكم في الصفحة ({pageUrl})."}, "core/audits/service-worker.js | failureTitle": {"message": "عدم تسجيل مشغّل الخدمات الذي يتحكّم في صفحة و`start_url`"}, "core/audits/service-worker.js | title": {"message": "تسجيل مشغّل الخدمات الذي يتحكّم في صفحة و`start_url`"}, "core/audits/splash-screen.js | description": {"message": "تضمن شاشة البداية المميَّزة توفير تجربة عالية الجودة عند تشغيل المستخدمين لتطبيقك من الشاشات الرئيسية. [مزيد من المعلومات حول شاشات البداية](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "عدم الضبط لشاشة بداية مخصّصة"}, "core/audits/splash-screen.js | title": {"message": "تم الضبط لشاشة البداية المخصّصة"}, "core/audits/themed-omnibox.js | description": {"message": "يمكن تصميم ألوان شريط العناوين للمتصفِّح بما يتطابق مع موقعك الإلكتروني. [مزيد من المعلومات حول تصميم ألوان شريط العناوين](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "عدم ضبط لون تصميم لشريط العناوين"}, "core/audits/themed-omnibox.js | title": {"message": "ضبط لون تصميم لشريط العناوين"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (منتج متعلق بخدمة العملاء)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (منتج متعلق بالتسويق)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (منتج متعلق بوسائل التواصل الاجتماعي)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (منتج متعلق بالفيديو)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "المنتج"}, "core/audits/third-party-facades.js | description": {"message": "بعض الرموز المضمّنة التابعة لجهات خارجية يمكن تحميلها باستخدام طريقة التحميل الكسول. ويمكنك استبدال هذه الرموز المضمّنة بإحدى الواجهات إلى حين الحاجة إليها. تعرَّف على [طريقة تأجيل الرموز التابعة لجهات خارجية بإحدى الواجهات](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{تتوفّر واجهة واحدة بديلة.}zero{تتوفّر # واجهة بديلة.}two{تتوفّر واجهتان بديلتان.}few{تتوفّر # واجهات بديلة.}many{تتوفّر # واجهة بديلة.}other{تتوفّر # واجهة بديلة.}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "بعض الموارد التابعة لجهات خارجية يمكن تحميلها ببطء مع واجهة"}, "core/audits/third-party-facades.js | title": {"message": "تحميل الموارد التابعة لجهات خارجية ببطء مع واجهات"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "الجهة الخارجية"}, "core/audits/third-party-summary.js | description": {"message": "يمكن أن يؤثر الرمز البرمجي التابع لجهة خارجية بشكل كبير في أداء التحميل. يمكنك تحديد عدد مقدِّمي الخدمة المكرّرين والتابعين لجهات خارجية ومحاولة تحميل الرمز البرمجي الخاص بالجهة الخارجية بعد انتهاء تحميل صفحتك بشكل أساسي. تعرَّف على [كيفية تقليل تأثير الرموز التابعة للجهات الخارجية](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "لقد حظر رمز الجهة الخارجية سلسلة المحادثات الرئيسية لمدة {timeInMs, number, milliseconds} مللي ثانية"}, "core/audits/third-party-summary.js | failureTitle": {"message": "تقليل تأثير رمز الجهة الخارجية"}, "core/audits/third-party-summary.js | title": {"message": "تقليل استخدام الرموز التابعة لجهات خارجية"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "القياسات"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "المقياس"}, "core/audits/timing-budget.js | description": {"message": "يمكنك ضبط ميزانية قائمة على الوقت لتساعدك في مراقبة أداء موقعك الإلكتروني. تتميّز المواقع الإلكترونية ذات الأداء العالي بسرعة التحميل والاستجابة للبيانات التي أدخلها المستخدم. [مزيد من المعلومات حول الميزانيات القائمة على الأداء](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "ميزانية قائمة على الوقت"}, "core/audits/unsized-images.js | description": {"message": "يجب ضبط قيَم واضحة للعرض والارتفاع في عناصر الصور للحدّ من متغيّرات التصميم وتحسين متغيّرات التصميم التراكمية (CLS). تعرَّف على [كيفية ضبط أبعاد الصور](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "عناصر الصور لا تحتوي على قيَم `width` و`height` محدَّدة"}, "core/audits/unsized-images.js | title": {"message": "عناصر الصورة تحتوي على قيَم `width` و`height` محدَّدة"}, "core/audits/user-timings.js | columnType": {"message": "النوع"}, "core/audits/user-timings.js | description": {"message": "يمكنك توجيه تطبيقك باستخدام واجهة برمجة التطبيقات User Timing API لقياس الأداء الفعلي لتطبيقك أثناء التجارب الأساسية للمستخدمين. [مزيد من المعلومات حول علامات User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{وقت واحد للمستخدم}zero{# وقت للمستخدم}two{وقتا (#) المستخدم}few{# أوقات للمستخدم}many{# وقتًا للمستخدم}other{# وقت للمستخدم}}"}, "core/audits/user-timings.js | title": {"message": "علامات أوقات المستخدم وقياساتها"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "تم العثور على رابط `<link rel=preconnect>` لـ \"{securityOrigin}\"، ولكن لم يتم استخدامه من خلال المتصفّح. يُرجى التحقّق من استخدام السمة `crossorigin` بشكل صحيح."}, "core/audits/uses-rel-preconnect.js | description": {"message": "يمكنك إضافة تعديلات المورد `preconnect` أو `dns-prefetch` لإنشاء اتصالات مبكرة بالمصادر المُهمّة التابعة لجهات خارجية. تعرَّف على [كيفية الاتصال مسبقًا بالمصادر المطلوبة](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "الاتصال المسبق للأصول المطلوبة"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "تم العثور على أكثر من رابطَيّ `<link rel=preconnect>`. ننصحك بعدم استخدام هذه الروابط بشكل كبير واقتصارها على المصادر الأكثر أهمية فقط."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "تم العثور على رابط `<link rel=preconnect>` لـ \"{securityOrigin}\"، ولكن لم يتم استخدامه من خلال المتصفّح. يجب فقط استخدام `preconnect` للمواقع الإلكترونية المهمّة التي ستطلبها الصفحة خلال عملية التحميل."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "تم العثور على رابط `<link>` للتحميل المُسبَق لـ \"{preloadURL}\"، ولكن لم يتم استخدامه من خلال المتصفّح. يُرجى التحقّق من استخدام السمة `crossorigin` بشكل صحيح."}, "core/audits/uses-rel-preload.js | description": {"message": "يمكنك استخدام `<link rel=preload>` لإعطاء أولوية لاسترجاع الموارد المطلوبة حاليًا في وقت لاحق أثناء تحميل الصفحة. تعرَّف على [طريقة التحميل المسبَق للطلبات الرئيسية](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "التحميل المسبق للطلبات الأساسية"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "عنوان URL لخريطة المصدر"}, "core/audits/valid-source-maps.js | description": {"message": "تحوِّل خرائط المصدر الرمز المصغَّر إلى رمز المصدر الأصلي. ويساعد هذا الإجراء المطوّرين على تصحيح الأخطاء في مرحلة الإنتاج. ويمكن لأداة Lighthouse أيضًا أن توفّر معلومات إضافية. ننصحك بنشر خرائط المصدر للاستفادة من هذه المزايا. [مزيد من المعلومات حول خرائط المصدر](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "عدم توفّر خرائط مصدر للغة JavaScript من الطرف الأول الكبير"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "لا يحتوي ملف JavaScript الكبير على خريطة مصدر"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{تحذير: هناك عنصر واحد غير متوفّر في السمة `.sourcesContent`}zero{تحذير: هناك # عنصر غير متوفّر في السمة `.sourcesContent`}two{تحذير: هناك عنصران غير متوفرَّين في السمة `.sourcesContent`}few{تحذير: هناك # عناصر غير متوفّرة في السمة `.sourcesContent`}many{تحذير: هناك # عنصرًا غير متوفّر في السمة `.sourcesContent`}other{تحذير: هناك # عنصر غير متوفّر في السمة `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "تحتوي الصفحة على خرائط مصدر صالحة"}, "core/audits/viewport.js | description": {"message": "لا يساعد إطار العرض `<meta name=\"viewport\">` على تحسين تطبيقك ليناسب أحجام شاشات الأجهزة الجوّالة فحسب، بل يمنع أيضًا [تأخُّر البيانات التي يدخلها المستخدم بمقدار 300 ملي ثانية](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [مزيد من المعلومات حول استخدام العلامة الوصفية لإطار العرض](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "لم يتم العثور على علامة `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "عدم الاحتواء على علامة `<meta name=\"viewport\">` مع `width` أو `initial-scale`"}, "core/audits/viewport.js | title": {"message": "تضمين علامة `<meta name=\"viewport\">` مع `width` أو `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "يشير هذا إلى إجراء حظر سلسلة التعليمات الذي يحدث أثناء قياس \"مدة عرض الاستجابة لتفاعل المستخدم\". [مزيد من المعلومات حول مقياس \"مدة عرض الاستجابة لتفاعل المستخدم\"](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "تم استغراق {timeInMs, number, milliseconds} ملي ثانية لإكمال الحدث \"{interactionType}\"."}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON> الحد<PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "تقليل العمل أثناء التفاعل الأساسي للمستخدم"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "تأخير عملية الإدخال"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "تأخير عرض الاستجابة"}, "core/audits/work-during-interaction.js | processingTime": {"message": "وقت المعالجة"}, "core/audits/work-during-interaction.js | title": {"message": "تقليل العمل أثناء التفاعل الأساسي للمستخدم"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "هذه هي فرص لتحسين استخدام ARIA في تطبيقك، ما قد يحسّن تجربة مستخدمي التكنولوجيا المساعدة، مثل قارئ الشاشة."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "هذه الفرص تتيح توفير محتوى بديل للصوت والفيديو. قد يحسّن ذلك التجربة للمستخدمين الذين يعانون من إعاقات سمعية أو بصرية."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "الصوت والفيديو"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "تحدّد هذه العناصر أفضل الممارسات الشائعة المتعلقة بإمكانية الوصول."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "أفضل الممارسات"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "تحدّد عمليات التحقق هذه الفرص التي تتيح [تحسين إمكانية الوصول إلى تطبيق الويب](https://developer.chrome.com/docs/lighthouse/accessibility/). ولا يمكن إجراء رصد تلقائي إلّا لمجموعة فرعية من مشاكل إمكانية الوصول، لذلك يُنصح أيضًا باستخدام الاختبار اليدوي."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "تعالج هذه العناصر المناطق التي يتعذر على أداة الاختبار المبرمجة تغطيتها. تعرّف على مزيد من المعلومات في دليلنا حول [مراجعة إمكانية الوصول](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "إمكانية الوصول"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "هذه هي فرص لتحسين سهولة قراءة المحتوى."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "التباين"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "هذه هي فرص لتحسين تفسير المحتوى من خلال المستخدمين بلغات مختلفة."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "التدويل والأقلمة"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "هذه هي فرص لتحسين دلالات عناصر التحكُّم في التطبيق. قد يحسّن ذلك من تجربة مستخدمي التكنولوجيا المساعدة، مثل قارئ الشاشة."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "الأسماء والتصنيفات"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "هذه الفرص تتيح لك تحسين التنقل باستخدام لوحة المفاتيح في تطبيقك."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "التنقل"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "تُعرَض هنا فرص لتحسين تجربة قراءة بيانات القوائم أو الجداول باستخدام التكنولوجيا المساعِدة، مثل قارئ الشاشة."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "الجداول والقوائم"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "توافق المتصفّح"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "أفضل الممارسات"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "عام"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "الثقة والأمان"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "انطباع المستخدم"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "تضبط ميزانيات الأداء معايير لأداء موقعك الإلكتروني."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "الميزانيات"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "مزيد من المعلومات حول أداء تطبيقك لا تؤثر هذه الأرقام [ بشكل مباشر في ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) نتيجة الأداء."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "بيانات التشخيص"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "يمثل الجانب الأكثر أهمية للأداء مدى السرعة التي يتم بها عرض وحدات البكسل على الشاشة. المقاييس الرئيسية: First Contentful Paint وFirst Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "تحسينات العرض الأول"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "يمكن أن تساعد هذه الاقتراحات على تحميل صفحتك بشكل أسرع. لا تؤثر هذه الاقتراحات [بشكل مباشر](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) في نتيجة الأداء."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "فرص تحسين الأداء"}, "core/config/default-config.js | metricGroupTitle": {"message": "المقاييس"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "يمكنك تحسين تجربة التحميل العامة لتصبح هذه الصفحة سريعة الاستجابة وجاهزة للاستخدام في أقرب وقت ممكن. المقاييس الأساسية: وقت التفاعل ومؤشر السرعة."}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "التحسينات العامة"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "الأداء"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "تتحقَّق عمليات التحقُّق هذه من جوانب تطبيق الويب التقدّمي. [تعرَّف على العوامل التي تساهم في إنشاء تطبيق ويب تقدّمي جيد](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "يُطلب إجراء عمليات التحقّق هذه من خلال المرجع [قائمة التحقق PWA](https://web.dev/pwa-checklist/)، ولكن لم يتم التحقُّق منها تلقائيًا من خلال Lighthouse. لا تؤثر عمليات التحقق هذه في نتيجتك، ولكن من المهم أنك تتحقق منها يدويًا."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "القسم القابل للتثبيت"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "تحسين PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "تضمن عمليات التحقّق هذه اتّباع صفحتك للنصائح الأساسية الخاصة بتحسين محركات البحث. هناك العديد من العوامل الإضافية التي تتسبب في عدم تحقيق أداة Lighthouse لأي نقاط هنا والتي قد تؤثّر في ترتيب نتائج البحث، بما في ذلك الأداء في ما يخص [مؤشرات أداء الويب الأساسية](https://web.dev/learn-core-web-vitals/). [مزيد من المعلومات حول \"أساسيات بحث Google\"](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "تشغيل أدوات التحقُّق الإضافية هذه على موقعك الإلكتروني للتحقُّق من أفضل ممارسات تحسين محركات البحث الإضافية."}, "core/config/default-config.js | seoCategoryTitle": {"message": "تحسين محركات البحث"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "يمكنك تنسيق HTML بطريقة تتيح لبرامج الزحف فهم محتوى تطبيقك بشكلٍ أفضل."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "أفضل ممارسات المحتوى"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "للظهور في نتائج البحث، تحتاج برامج الزحف إلى الوصول إلى تطبيقك."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "الزحف والفهرسة"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "احرص على أن تكون صفحاتك متوافقة مع الأجهزة الجوّالة كي لا يحتاج المستخدمون إلى تصغير أو تكبير الشاشة بإصبعين من أجل الاطّلاع على صفحات المحتوى. [تعرَّف على كيفية جعل الصفحات متوافقة مع الأجهزة الجوّالة](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "متوافق مع الجوّال"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "يبدو أنّ الجهاز المختبَر يحتوي على وحدة معالجة مركزية (CPU) أبطأ مما توقعته أداة Lighthouse. قد يؤثر ذلك سلبيًا على نتيجة الأداء. تعرّف على المزيد من المعلومات عن [معايرة مُضاعِف بطء وحدة المعالجة المركزية (CPU) المناسب](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "قد لا يتم تحميل الصفحة كما هو متوقَع لأن عنوان URL للاختبار ({requested}) تمت إعادة توجيهه إلى {final}. يُرجى محاولة اختبار عنوان URL الثاني مباشرةً."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "أدّى عدم تحميل الصفحة بسرعة كافية إلى تعذّر انتهاء تحميلها في الوقت المحدّد. وقد تكون النتائج غير مكتملة."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "انتهت مهلة محو ذاكرة التخزين المؤقت للمتصفّح. حاوِل التدقيق في هذه الصفحة مرة أخرى وأرسِل تقريرًا بالخطأ إذا استمرت المشكلة."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{قد تكون هناك بيانات مخزَّنة تؤثر على أداء التحميل في مكان تخزين البيانات التالي: {locations} يُرجى إجراء تدقيق لهذه الصفحة في نافذة التصفح المتخفي لمنع هذه المصادر من التأثير على نتيجتك.}zero{قد تكون هناك بيانات مخزَّنة تؤثر على أداء التحميل في أماكن تخزين البيانات التالية: {locations} يُرجى إجراء تدقيق لهذه الصفحة في نافذة التصفح المتخفي لمنع هذه المصادر من التأثير على نتيجتك.}two{قد تكون هناك بيانات مخزَّنة تؤثر على أداء التحميل في مكانَي تخزين البيانات التاليَين: {locations} يُرجى إجراء تدقيق لهذه الصفحة في نافذة التصفح المتخفي لمنع هذه المصادر من التأثير على نتيجتك.}few{قد تكون هناك بيانات مخزَّنة تؤثر على أداء التحميل في أماكن تخزين البيانات التالية: {locations} يُرجى إجراء تدقيق لهذه الصفحة في نافذة التصفح المتخفي لمنع هذه المصادر من التأثير على نتيجتك.}many{قد تكون هناك بيانات مخزَّنة تؤثر على أداء التحميل في أماكن تخزين البيانات التالية: {locations} يُرجى إجراء تدقيق لهذه الصفحة في نافذة التصفح المتخفي لمنع هذه المصادر من التأثير على نتيجتك.}other{قد تكون هناك بيانات مخزَّنة تؤثر على أداء التحميل في أماكن تخزين البيانات التالية: {locations} يُرجى إجراء تدقيق لهذه الصفحة في نافذة التصفح المتخفي لمنع هذه المصادر من التأثير على نتيجتك.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "انتهت مهلة محو بيانات المصدر. حاوِل التدقيق في هذه الصفحة مرة أخرى وأرسِل تقريرًا بالخطأ إذا استمرت المشكلة."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات فقط التي تم تحميلها من خلال طلب استرداد بيانات باستخدام GET."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "لا يمكن إجراء تخزين مؤقت إلا للصفحات التي تتضمّن رمز الحالة 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "اكتشَف Chrome محاولةً لتنفيذ JavaScript أثناء التخزين المؤقت."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت AppBanner."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" في chrome://flags. يمكنك الانتقال إلى الرابط chrome://flags/#back-forward-cache لتفعيلها على هذا الجهاز."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "أوقفَ سطر الأوامر ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" بسبب عدم توفّر مساحة كافية في الذاكرة."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "لا تتوفّر ميزة \"التخزين المؤقت للصفحات\" من خلال التفويض."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" في العارض المسبق."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "لا يمكن تخزين الصفحة مؤقتًا لأنها تحتوي على مثيل BroadcastChannel يتضمّن أدوات مسجَّلة لمعالجة الحدث."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "لا يمكن تخزين الصفحات التي تحتوي على عنوان cache-control:no-store باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "تم محو ذاكرة التخزين المؤقت عن قصد."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "تم إخراج الصفحة من ذاكرة التخزين المؤقت للسماح بتخزين صفحة أخرى مؤقتًا."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تحتوي على مكوّنات إضافية."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم FileChooser API."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم File System Access API."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم Media Device Dispatcher (أداة إرسال جهاز الوسائط)."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "كان أحد مشغّلات الوسائط قيد التشغيل أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم MediaSession API وتضبط حالة للتشغيل."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم MediaSession API وتضبط معالِجات الإجراءات."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" بسبب استخدام قارئ الشاشة."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم SecurityHandler."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم Serial API."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم WebAuthentication API."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم WebBluetooth API."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" لتخزين الصفحات التي تستخدم WebUSB API."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم مشغّل خدمات مخصَّصًا أو وظيفة مصغَّرة."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "تمّت مغادرة المستند قبل أن ينتهي من التحميل."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "كان بانر التطبيق قيد الاستخدام أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "كان \"مدير كلمات المرور\" في Chrome قيد الاستخدام أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "كانت عملية استخلاص \"نموذج العناصر في المستند\" (DOM) يتم تنفيذها أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "كان عارض ميزة DOM Distiller قيد الاستخدام أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" لأنّ الإضافات تستخدم واجهة برمجة تطبيقات خدمة المراسلة."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "الإضافات ذات الاتصال الطويل الأمد يجب أن تُغلِق الاتصال قبل تخزينها مؤقتًا باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "حاولت الإضافات ذات الاتصال الطويل الأمد إرسال رسائل إلى الإطارات المدرَجة في ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" بسبب استخدام الإضافات."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "تم عرض مربّع حوار نمطي في الصفحة أثناء مغادرتها، مثل مربّع حوار إعادة إرسال النموذج أو مربّع حوار مصادقة HTTP باستخدام كلمة مرور."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "تم عرض الصفحة المتوفّرة بلا اتصال بالإنترنت أثناء المغادرة."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "كان شريط Out-Of-Memory Intervention قيد الاستخدام أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "كانت هناك طلبات أذونات أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "كان حاجب النوافذ المنبثقة قيد الاستخدام أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "تم عرض تفاصيل \"التصفُّح الآمن\" أثناء مغادرة الصفحة."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "رصدت ميزة \"التصفُّح الآمن\" أنّ هذه الصفحة تتضمن محتوى مسيئًا وحظرت نافذة منبثقة."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "كان أحد مشغّلي الخدمات مفعّلاً أثناء إجراء تخزين مؤقت للصفحة باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" بسبب خطأ في المستند."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "لا يمكن تخزين الصفحات التي تستخدم الإطار FencedFrames من خلال ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "تم إخراج الصفحة من ذاكرة التخزين المؤقت للسماح بتخزين صفحة أخرى مؤقتًا."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي سمحت بالوصول إلى محتوى الوسائط."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم بوابات الويب."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم IdleManager."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تحتوي على اتصال IndexedDB مفتوح."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "تم استخدام واجهات غير مؤهَّلة لبرمجة التطبيقات."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي يتم إدخال رموز JavaScript فيها من خلال الإضافات."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تم إدخال ورقة أنماط فيها من خلال الإضافات."}, "core/lib/bf-cache-strings.js | internalError": {"message": "حدث خطأ داخلي."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "تم إيقاف ميزة \"التخزين المؤقت للصفحات\" بسبب طلب التحقّق من الاتصال."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم قفل لوحة المفاتيح."}, "core/lib/bf-cache-strings.js | loading": {"message": "تمت مغادرة الصفحة قبل أن تنتهي من التحميل."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "لا يمكن تخزين الصفحات التي يحتوي موردها الرئيسي على cache-control:no-cache باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "لا يمكن تخزين الصفحات التي يحتوي موردها الرئيسي على cache-control:no-store باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "تم إلغاء التنقّل قبل أن تتم استعادة الصفحة من عملية التخزين المؤقت باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "تم إخراج الصفحة من ذاكرة التخزين المؤقت لأنّ الاتصال النشط بالشبكة تلقّى الكثير من البيانات، علمًا بأنّ Chrome يقلِّل مقدار البيانات التي قد تتلقّاها الصفحة أثناء تخزينها مؤقتًا."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم XHR أو fetch()‎ يتم تنفيذهما حاليًا."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "تم إخراج الصفحة من عملية التخزين المؤقت باستخدام ميزة \"التخزين المؤقت للصفحات\" لأنّ أحد طلبات الشبكة النشطة تضمَّن إعادة توجيه."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "تم إخراج الصفحة من ذاكرة التخزين المؤقت لأنّ الاتصال بالشبكة كان مفتوحًا لفترة طويلة جدًا، علمًا بأنّ Chrome يقلِّل مقدار الوقت الذي قد تتلقّى خلاله الصفحة البيانات أثناء تخزينها مؤقتًا."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "لا يمكن تخزين الصفحات التي لا تحتوي على عنوان استجابة صالح باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "حدث التنقّل في إطار غير الإطار الرئيسي."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تتضمّن عمليات جارية لقاعدة البيانات المُفهرَسة."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم طلب شبكة يتم تنفيذه حاليًا."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم طلب استرجاع لبيانات شبكة يتم تنفيذه حاليًا."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم طلب شبكة يتم تنفيذه حاليًا."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم طلب شبكة XHR يتم تنفيذه حاليًا."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم PaymentManager."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم ميزة \"نافذة ضمن النافذة\"."}, "core/lib/bf-cache-strings.js | portal": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم بوابات الويب."}, "core/lib/bf-cache-strings.js | printing": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تعرض \"واجهة المستخدم الخاصة بالطباعة\"."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "تم فتح الصفحة باستخدام `window.open()` وعلامة تبويب أخرى بها مرجع إليها، أو فتحت الصفحة نافذة."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "تعطّلت عملية العرض للصفحة خلال تخزينها باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "تم إنهاء عملية العرض للصفحة خلال تخزينها باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت أذونات تسجيل الصوت."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت أذونات أدوات الاستشعار."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت إذن الاسترجاع أو إذن المزامنة في الخلفية."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت أذونات الوصول إلى أجهزة MIDI."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت أذونات إرسال الإشعارات."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت الوصول إلى مساحة التخزين."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي طلبت أذونات تسجيل الفيديو."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "لا يمكن إجراء تخزين مؤقت إلا للصفحات التي يكون مخطَّط URL فيها هو HTTP/HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "استدعى أحد مشغّلي الخدمات الصفحة أثناء تخزينها مؤقتًا باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "حاول أحد مشغّلي الخدمات إرسال `MessageEvent` إلى الصفحة خلال تخزينها باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "تم إلغاء تسجيل ServiceWorkers أثناء تخزين صفحة باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "تم استبعاد هذه الصفحة من ميزة \"التخزين المؤقت للصفحات\" بسبب تفعيل مشغّل خدمات."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "تمت إعادة تشغيل Chrome ومحو إدخالات ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم SharedWorker."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم SpeechRecognizer."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم SpeechSynthesis."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "بدأ إطار if<PERSON>e على الصفحة عملية تنقُّل لم تكتمل."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "لا يمكن تخزين الصفحات التي يحتوي موردها الفرعي على cache-control:no-cache باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "لا يمكن تخزين الصفحات التي يحتوي موردها الفرعي على cache-control:no-store باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | timeout": {"message": "تجاوزت الصفحة الحدّ الأقصى لوقت التخزين في ميزة \"التخزين المؤقت للصفحات\" وانتهت صلاحيتها."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "انتهت مهلة تخزين الصفحة باستخدام ميزة \"التخزين المؤقت للصفحات\" (من المرجَّح أنّ سبب ذلك هو معالِجات إخفاء الصفحات التي تستغرق مدة زمنية طويلة)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "تحتوي الصفحة على معالج لإلغاء التحميل في الإطار الرئيسي."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "تحتوي الصفحة على معالِج لإلغاء التحميل في إطار فرعي."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "غيَّر المتصفّح عنوان تجاوز وكيل المستخدم."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي سمحَت بتسجيل فيديو أو صوت."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebDatabase."}, "core/lib/bf-cache-strings.js | webHID": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebHID."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebLocks."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebNfc."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebOTPService."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "لا يمكن تخزين الصفحات التي تحتوي على WebRTC باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | webShare": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebShare."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "لا يمكن تخزين الصفحات التي تحتوي على WebSocket باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "لا يمكن تخزين الصفحات التي تحتوي على WebTransport باستخدام ميزة \"التخزين المؤقت للصفحات\"."}, "core/lib/bf-cache-strings.js | webXR": {"message": "لا يمكن استخدام ميزة \"التخزين المؤقت للصفحات\" حاليًا لتخزين الصفحات التي تستخدم WebXR."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "ننصحك بإضافة مخطّطات عناوين URL تستخدم https:‎ وhttp:‎ (التي تتجاهلها المتصفِّحات المتوافقة مع 'strict-dynamic') لجعل سياسة CSP متوافقة مع أنظمة المتصفِّحات القديمة."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "تم إيقاف disown-opener منذ بدء استخدام CSP3. يُرجى استخدام عنوان السياسة Cross-Origin-Opener-Policy بدلاً من ذلك."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "تم إيقاف referrer منذ بدء استخدام سياسة CSP2. يُرجى استخدام العنوان Referrer-Policy بدلاً من ذلك."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "تم إيقاف reflected-xss منذ بدء استخدام سياسة CSP2. يُرجى استخدام العنوان X-XSS-Protection بدلاً من ذلك."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "يسمح عدم توفُّر base-uri بأن تضبط علامات <base> التي تم إدراجها عنوان URL الأساسي لجميع عناوين URL النسبية (على سبيل المثال، النصوص البرمجية) على نطاق يتحكّم فيه مهاجم. ننصحك بضبط base-uri على \"none\" أو \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "إنّ عدم توفّر الأمر object-src يسمح بإدخال المكوّنات الإضافية التي تنفِّذ نصوصًا برمجية غير آمنة، ولذلك ننصحك بضبط object-src على 'none' إذا أمكن."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "الأمر التوجيهي script-src غير متوفّر. يمكن أن يسمح ذلك بتنفيذ نصوص برمجية غير آمنة."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "هل نسيت إضافة الفاصلة المنقوطة؟ {keyword} هو أمر توجيهي، وليس كلمة رئيسية."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "يجب أن تستخدم الأرقام الخاصة ترميز الأحرف base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "يجب ألّا تقل الأرقام الخاصة عن 8 أحرف."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "تجنَّب استخدام المخطّطات العادية لعناوين URL ({keyword}) في هذا الأمر لأنّها تسمح بجلب النصوص البرمجية من نطاق غير آمن."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "تجنَّب استخدام أحرف البدل العادية ({keyword}) في هذا الأمر لأنّها تسمح بجلب النصوص البرمجية من نطاق غير آمن."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "يتم ضبط وجهة إعداد التقارير من خلال الأمر التوجيهي report-to فقط. يتوافق هذا الأمر التوجيهي مع المتصفِّحات المستنِدة إلى Chromium فقط، ولذلك نقترح استخدام الأمر التوجيهي report-uri أيضًا."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "لا توفّر سياسة CSP أي وجهة إعداد تقارير، ما يجعل من الصعب الحفاظ على هذه السياسة بمرور الوقت ورصد أي خلل فيها."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "قد يتم تجاوز قائمة المضيفين المسموح بها بشكل متكرّر. ننصحك باستخدام الأرقام الخاصة لسياسة أمان المحتوى (CSP) أو أجزائها بدلاً من ذلك، بالإضافة إلى 'strict-dynamic' إذا لزم الأمر."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "أمر CSP توجيهي غير معروف."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "الكلمة الرئيسية {keyword} غير صالحة."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "يسمح 'unsafe-inline' بتنفيذ نصوص برمجية ومعالجات أحداث غير آمنة في الصفحة. ننصحك باستخدام أرقام CSP الخاصة أو أجزائها للسماح بالنصوص البرمجية كل على حدة."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "ننصحك بإضافة الأمر التوجيهي 'unsafe-inline' (الذي تتجاهله المتصفِّحات المتوافقة مع nonces/hashes) لجعل CSP متوافقة مع أنظمة المتصفِّحات القديمة."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "لن يشمل رمز حرف البدل (*) الإذن في معالجة العنوان `Access-Control-Allow-Headers` في سياسة Cross-Origin Resource Sharing ‏(CORS)."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "يتم حظر طلبات الموارد التي تتضمّن عناوين URL الخاصة بها كلاً من أحرف `(n|r|t)` للمسافة البيضاء وأحرف \"أقل من\" (`<`) التي تمت إزالتها. لتحميل هذه الموارد، يُرجى إزالة السطور الجديدة وترميز أحرف \"أقل من\" من خلال مواضع مثل قيم سمات العناصر."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "تم إيقاف `chrome.loadTimes()` نهائيًا، ويمكنك بدلاً منها استخدام واجهة برمجة التطبيقات Navigation Timing 2 الموحَّدة."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "تم إيقاف واجهة برمجة التطبيقات `chrome.loadTimes()` نهائيًا. وبدلاً منها، يمكنك استخدام واجهة برمجة التطبيقات الموحَّدة Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "تم إيقاف واجهة برمجة التطبيقات `chrome.loadTimes()` نهائيًا. وبدلاً منها، يمكنك استخدام واجهة برمجة التطبيقات الموحَّدة Navigation Timing 2 التي تتضمّن `nextHopProtocol`."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "سيتم رفض ملفات تعريف الارتباط التي تتضمّن الحرف `(0|r|n)` بدلاً من اقتطاعها."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "إنّ ميزة تخفيف قيود السياسة المشتركة المصدر من خلال ضبط `document.domain` تم إيقافها نهائيًا، وستصبح غير مفعَّلة تلقائيًا. ويتعلّق تحذير الإيقاف النهائي هذا بإذن الوصول من مصادر متعددة والذي تم تفعيله من خلال ضبط `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "إنّ ميزة تنفيذ {PH1} المستنِدة إلى إطارات iframe من مصادر متعددة تم إيقافها نهائيًا وستتم إزالتها في المستقبل."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "يجب استخدام السمة `disableRemotePlayback` لإيقاف الدمج التلقائي للبث بدلاً من استخدام أداة الاختيار `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "تم إيقاف {PH1} نهائيًا. يُرجى استخدام {PH2} بدلاً منها."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "هذا مثال على رسالة مُترجمة بشأن مشكلة ناتجة عن إيقاف ميزة نهائيًا."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "إنّ ميزة تخفيف قيود السياسة المشتركة المصدر من خلال ضبط `document.domain` تم إيقافها نهائيًا، وستصبح غير مفعَّلة تلقائيًا. لمواصلة استخدام هذه الميزة، يُرجى إيقاف مجموعات الوكلاء المرتبطة حسب المصدر من خلال إرسال العنوان `Origin-Agent-Cluster: ?0` مع استجابة HTTP للمستند والإطارات. لمعرفة مزيد من التفاصيل، يمكنك الاطّلاع على https://developer.chrome.com/blog/immutable-document-domain/‎."}, "core/lib/deprecations-strings.js | eventPath": {"message": "تم إيقاف واجهة برمجة التطبيقات `Event.path` نهائيًا وستتم إزالتها. يُرجى استخدام `Event.composedPath()` بدلاً منها."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "تم إيقاف عنوان `Expect-CT` نهائيًا وستتم إزالته. يتطلَّب Chrome توفُّر شهادة الشفافية لجميع الشهادات الموثوق بها التي تم إصدارها بشكل علني بعد 30 نيسان (أبريل) 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "لمزيد من التفاصيل، يُرجى الاطّلاع على صفحة حالة الميزة."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "لم تعُد الميزتان `getCurrentPosition()` و`watchPosition()` متوافقتين مع المصادر غير الآمنة. لاستخدام هذه الميزة، يجب مراعاة نقل تطبيقك إلى مصدر آمن مثل HTTPS. لمعرفة مزيد من التفاصيل، يُرجى الاطّلاع على https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "تم إيقاف الإجراءين `getCurrentPosition()` و`watchPosition()` نهائيًا على المصادر غير الآمنة. لاستخدام هذه الميزة، يجب مراعاة نقل تطبيقك إلى مصدر آمن مثل HTTPS. لمعرفة مزيد من التفاصيل، يُرجى الاطّلاع على https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "لم تعُد ميزة `getUserMedia()` متوافقة مع المصادر غير الآمنة. لاستخدام هذه الميزة، يجب مراعاة نقل تطبيقك إلى مصدر آمن مثل HTTPS. لمعرفة مزيد من التفاصيل، يُرجى الاطّلاع على https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "تم إيقاف `RTCPeerConnectionIceErrorEvent.hostCandidate` نهائيًا. يُرجى استخدام `RTCPeerConnectionIceErrorEvent.address` أو `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "إنّ مصدر تطبيق التاجر والبيانات العشوائية من حدث مشغّل الخدمات في `canmakepayment` تم إيقافهما نهائيًا وستتم إزالتهما: `topOrigin` و`paymentRequestOrigin` و`methodData` و`modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "طلب الموقع الإلكتروني موردًا فرعيًا من شبكة يمكن الوصول إليه من خلال الموقع الإلكتروني فقط بسبب موضع مستخدميه المميّز على الشبكة. تكشف هذه الطلبات عن الأجهزة والخوادم الخاصة على الإنترنت، ما يزيد من خطر التعرض لهجمات تقليد الطلبات من موقع إلكتروني مختلف (CSRF) و/أو تسرُّب المعلومات. وللحدِّ من هذه المخاطر، أوقف Chrome نهائيًا طلبات الوصول إلى موارد فرعية خاصة، وذلك عند تقديمها من خلال سياقات غير آمنة، وسيبدأ في حظرها."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "لا يمكن تحميل صفحات الأنماط المتتالية (CSS) من عناوين URL للملفات `file:` ما لم تنتهِ الملفات بالامتداد `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "نظرًا لتغيّر المواصفات، تم بشكل نهائي إيقاف إمكانية استخدام `SourceBuffer.abort()` الهادفة إلى إلغاء عملية إزالة النطاق غير المتزامن الذي يخصّ `remove()`. ستتم إزالة الميزة في المستقبل. وعليك معالجة الحدث `updateend` بدلاً من ذلك. ‫`abort()` مخصَّص فقط لإلغاء إمكانية إلحاق الوسائط غير المتزامنة أو إعادة ضبط حالة المحلِّل."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "نظرًا لتغيّر المواصفات، تم بشكل نهائي إيقاف إمكانية ضبط `MediaSource.duration` على قيمة أقل من الحد الأقصى للطابع الزمني المخصَّص لعرض أي إطارات تم ترميزها وتخزينها مؤقتًا. إنّ الإزالة الضمنية للوسائط المُقتطَعة التي تم تخزينها مؤقتًا لن تكون متاحة في المستقبل. وبدلاً من ذلك، يجب تطبيق `remove(newDuration, oldDuration)` بشكل واضح على جميع `sourceBuffers` التي تتوفّر بها `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "سيسري هذا التغيير عند تحديد المرحلة {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "ستطلب واجهة برمجة التطبيقات Web MIDI إذنًا للاستخدام حتى في حال عدم تحديد رسائل النظام الحصرية (Sysex) في `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "قد يتوقف استخدام واجهة برمجة التطبيقات Notification API من مصادر غير آمنة. يجب مراعاة نقل تطبيقك إلى مصدر آمن، مثل HTTPS. لمعرفة مزيد من التفاصيل، يُرجى الاطّلاع على https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "قد تتوقّف إمكانية طلب إذن لواجهة برمجة التطبيقات Notification API من أحد إطارات iframe من مصادر متعددة. يجب مراعاة طلب إذن من إطار عالي المستوى أو فتح نافذة جديدة بدلاً من ذلك."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "يتفاوض شريكك لاستخدام إصدار (D) القديم من بروتوكول أمان طبقة النقل (TLS). يُرجى التحقّق من الأمر مع شريكك لحلّ هذه المشكلة."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "تم إيقاف WebSQL نهائيًا في السياقات غير الآمنة وستتم إزالتها قريبًا. يُرجى استخدام Web Storage أو Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "في حال تحديد القيمة `overflow: visible` ضمن علامات img وvideo وcanvas، قد يُعرَض محتوى مرئي خارج حدود العنصر. يُرجى الاطّلاع على الرابط https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "تم إيقاف `paymentManager.instruments` نهائيًا. يُرجى استخدام ميزة التثبيت الفوري (JIT) مع تطبيقات معالجة عمليات الدفع."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "تجاوز طلب البيانات من واجهة برمجة التطبيقات `PaymentRequest` التوجيه `connect-src` في Content-Security-Policy (سياسة أمان المحتوى). تم إيقاف هذا التجاوز نهائيًا. يُرجى إضافة معرِّف طريقة الدفع من واجهة برمجة التطبيقات `PaymentRequest` (في حقل `supportedMethods`) إلى توجيه `connect-src` في سياسة أمان المحتوى."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "تم إيقاف `StorageType.persistent` نهائيًا. يُرجى استخدام `navigator.storage` الموحّدة بدلاً منها."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "إنّ استخدام `<source src>` مع العنصر الرئيسي `<picture>` غير صالح، وبالتالي سيتم تجاهله. يُرجى استخدام `<source srcset>` بدلاً من ذلك."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "تم إيقاف `window.webkitStorageInfo` نهائيًا. يُرجى استخدام `navigator.storage` الموحّدة بدلاً منها."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "يتم حظر طلبات الموارد الفرعية إذا كانت عناوين URL الخاصة بها تحتوي على بيانات اعتماد مُضمَّنة (مثل `**********************/`)."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "تمت إزالة القيد `DtlsSrtpKeyAgreement`. لقد حدَّدت القيمة `false` لهذا القيد، وهو ما يتم تفسيره كمحاولة لاستخدام إجراء `SDES key negotiation` الذي تمت إزالته. لقد تمت إزالة هذه الوظيفة. وبدلاً منها، يمكنك استخدام إحدى الخدمات المتوافقة مع `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "تمت إزالة القيد `DtlsSrtpKeyAgreement`. لقد حدَّدت القيمة `true` لهذا القيد، وهو إجراء ليس له أي تأثير، غير أنّه يمكنك إزالة هذا القيد بغرض التنظيم."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "تم رصد استخدام `Complex Plan B SDP`. لم يعُد هذا الإصدار من `Session Description Protocol` متاحًا. يُرجى استخدام `Unified Plan SDP` بدلاً من ذلك."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "إنّ إصدار `Plan B SDP semantics` المُستخدَم مع `{sdpSemantics:plan-b}` لتصميم `RTCPeerConnection` هو إصدار قديم وغير عادي من `Session Description Protocol`، وتم حذفه نهائيًا من النظام الأساسي للويب. لا يزال هذا الإصدار مُستخدَمًا للتصميم باستخدام `IS_FUCHSIA`، ولكننا ننوي حذفه في أقرب وقت ممكن. يُرجى التوقف عن استخدامه. لمعرفة حالته، يمكنك الاطّلاع على https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "تم إيقاف الخيار `rtcpMuxPolicy` نهائيًا وستتم إزالته."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "ستتطلّب `SharedArrayBuffer` حظر الوصول من نطاقات أخرى. لمعرفة مزيد من التفاصيل، يُرجى الاطّلاع على https://developer.chrome.com/blog/enabling-shared-array-buffer/‎."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "إنّ ميزة تنفيذ `speechSynthesis.speak()` بدون تفعيل المستخدِم له متوقفة نهائيًا وستتم إزالتها."}, "core/lib/deprecations-strings.js | title": {"message": "تم استخدام ميزة متوقّفة نهائيًا"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "يجب أن تستخدم الإضافات ميزة حظر الوصول من نطاقات أخرى لمواصلة استخدام `SharedArrayBuffer`. يُرجى الاطّلاع على https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/‎."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} مخصَّصة للمورِّدين. يُرجى استخدام {PH2} العادية بدلاً منها."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "لا يتوفّر الترميز UTF-16 من خلال استجابة تنسيق json في واجهة برمجة التطبيقات `XMLHttpRequest`."}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "واجهة برمجة التطبيقات `XMLHttpRequest` المتزامنة في سلسلة أنظمة التشغيل الرئيسية متوقفة نهائيًا بسبب تأثيراتها الضارة في تجربة المستخدِم النهائي. للحصول على مزيد من المساعدة، يُرجى الاطّلاع على https://xhr.spec.whatwg.org/‎."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "تم إيقاف `supportsSession()` نهائيًا. يُرجى استخدام `isSessionSupported()` والتحقّق من القيمة المنطقية النهائية."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "وقت حظر سلسلة المحادثات الأساسية"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "ذاكرة التخزين المؤقت TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "الوصف"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "المدة"}, "core/lib/i18n/i18n.js | columnElement": {"message": "العنصر"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "العناصر التي لم تلبِّ الشروط"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "الموقع الجغرافي"}, "core/lib/i18n/i18n.js | columnName": {"message": "الاسم"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "تجاوز الميزانية"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "الطلبات"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "حجم المصدر"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "نوع المورد"}, "core/lib/i18n/i18n.js | columnSize": {"message": "الحجم"}, "core/lib/i18n/i18n.js | columnSource": {"message": "المصدر"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "وقت البدء"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "الوقت المستغرَق"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "حجم النقل"}, "core/lib/i18n/i18n.js | columnURL": {"message": "عنوان URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "التوفيرات المحتملة"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "التوفيرات المحتملة"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "احتمال توفير {wastedBytes, number, bytes} كيبيبايت"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{تم العثور على عنصر واحد}zero{تم العثور على # عنصر}two{تم العثور على عنصرَين}few{تم العثور على # عناصر}many{تم العثور على # عنصرًا}other{تم العثور على # عنصر}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "التوفيرات المحتملة من {wastedMs, number, milliseconds} مللي ثانية"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "المستند"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "الخط"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "الصورة"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "مدة عرض الاستجابة لتفاعل المستخدم"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "مرتفع"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "من<PERSON><PERSON>ض"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "متوسط"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "الح<PERSON> الأق<PERSON>ى المحتمل من مهلة الاستجابة لأوّل إدخال"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "الوسائط"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} مللي ثانية"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON>ير ذلك"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "مو<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "النص البرمجي"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} ثانية"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "ورقة الأنماط"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "الجهة الخارجية"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "الإجمالي"}, "core/lib/lh-error.js | badTraceRecording": {"message": "حدث خطأ أثناء تسجيل التتبع عند تحميل صفحتك. يُرجى تشغيل Lighthouse مرة أخرى. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "مهلة انتظار ربط بروتوكول برنامج تصحيح الأخطاء الأول."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "لم يجمع Chrome أي لقطات شاشة خلال عملية تحميل الصفحة. يُرجى التأكّد من توفّر محتوى مرئي على الصفحة، ثم محاولة إعادة تشغيل Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "تعذّر على خوادم نظام أسماء النطاقات حل مشكلة النطاق المُقدّم."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "واجهت عملية التجميع {artifactName} المطلوبة خطأً: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "حدث خطأ في متصفّح Chrome الداخلي. يُرجى إعادة تشغيل Chrome ومحاولة إعادة تشغيل Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "كان من المطلوب تجميع {artifactName} ولكن لم يتم تنفيذ ذلك."}, "core/lib/lh-error.js | noFcp": {"message": "لم تعرض الصفحة أي محتوى. يُرجى التأكّد من إبقاء نافذة المتصفّح في المقدّمة أثناء التحميل ثم إعادة المحاولة. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "لم تعرض الصفحة محتوى مؤهّلاً لمقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة\" (LCP). يُرجى التأكُّد من أنّ الصفحة تتضمّن محتوى صالحًا مؤهلاً لمقياس LCP ثم أعِد المحاولة. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "الصفحة المقدّمة غير متوفّرة بتنسيق HTML (يتم عرضها كصفحة من النوع MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "هذا الإصدار من Chrome قديم جدًا وغير متوافق مع \"{featureName}\". يجب استخدام إصدار أحدث للاطّلاع على النتائج الكاملة."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "لم يتمكّن Lighthouse من تحميل الصفحة المطلوبة بشكل موثوق. يمكنك التأكُّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "لم يتمكّن Lighthouse من تحميل عنوان URL الذي طلبته بشكل موثوق لأن الصفحة توقفت عن الاستجابة."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "لا يحتوي عنوان URL الذي قدمته على شهادة أمان صالحة. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "منَع Chrome تحميل صفحة مع محتوى بيني. عليك التأكّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "لم يتمكّن Lighthouse من تحميل الصفحة المطلوبة بشكل موثوق. عليك التأكّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات. (التفاصيل: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "لم يتمكّن Lighthouse من تحميل الصفحة المطلوبة بشكل موثوق. عليك التأكّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات. (رمز الحالة: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "استغرق تحميل الصفحة وقتًا طويلاً. يُرجى اتّباع الفرص الواردة في التقرير لتقليل وقت تحميل الصفحة، ثم محاولة إعادة تشغيل Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "لقد تجاوز وقت انتظار استجابة بروتوكول DevTools الوقت المخصص. (الطريقة: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "تجاوز جلب محتوى المورد الوقت المخصّص"}, "core/lib/lh-error.js | urlInvalid": {"message": "يبدو أن عنوان URL الذي قدمته غير صحيح."}, "core/lib/navigation-error.js | warningXhtml": {"message": "نوع MIME للصفحة هو XHTML: لا يتوافق وضع Lighthouse مع هذا النوع من المستندات بشكلٍ صريح."}, "core/user-flow.js | defaultFlowName": {"message": "مسار المستخدم ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "تقرير التنقّل في الصفحة ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "تقرير تحليل الصفحة في لحظة معيَّنة ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "تقرير عن تحليل التفاعل خلال فترة زمنية ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "كل التقارير"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "الفئات"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "إمكانية الوصول"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "أفضل الممارسات"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "الأداء"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "تطبيق الويب التقدّمي"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "تحسين محركات البحث"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "سط<PERSON> المكتب"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "فهم تقرير مسار Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "فهم المسارات"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "يمكنك استخدام تقارير التنقُّل من أجل..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "يمكنك استخدام التقارير الخاصة بلحظات معيّنة من أجل..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "يمكنك استخدام تقارير الفترات الزمنية من أجل..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "الحصول على نتيجة أداء أداة Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "التعرّف على قيم مقاييس أداء تحميل الصفحة، مثل سرعة عرض أكبر جزء من المحتوى على الصفحة ومؤشر السرعة"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "تقييم إمكانات تطبيقات الويب التقدّمية"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "التعرّف على المشاكل التي تحول دون سهولة الاستخدام في التطبيقات المكوّنة من صفحة واحدة أو النماذج المُعقَّدة"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "تقييم أفضل الممارسات المتعلّقة بالقوائم وعناصر واجهة المستخدم المخفية خلف التفاعلات"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "قياس متغيّرات التصميم ووقت تنفيذ JavaScript على سلسلة من التفاعلات"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "التعرّف على فرص تحسين الأداء من أجل تحسين تجربة استخدام الصفحات التي يفتحها المستخدم لمدة طويلة والتطبيقات المكوّنة من صفحة واحدة"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "عمليات التدقيق الأعلى تأثيرًا"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{تدقيق واحد ({numInformative}) مفيد}zero{{numInformative} تدقيق مفيد}two{تدقيقان ({numInformative}) مفيدان}few{{numInformative} عمليات تدقيق مفيدة}many{{numInformative} تدقيقًا مفيدًا}other{{numInformative} تدقيق مفيد}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "الأجهزة الجوّالة"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "تحميل الصفحة"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "تحلِّل تقارير التنقُّل أداء تحميل صفحة واحدة، تمامًا مثل تقارير Lighthouse الأصلية."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "تقرير التنقُّل في الصفحة"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{تقرير تنقُّل واحد ({numNavigation}) في الصفحة}zero{{numNavigation} تقرير تنقُّل في الصفحة}two{تقريرا تنقُّل ({numNavigation}) في الصفحة}few{{numNavigation} تقارير تنقُّل في الصفحة}many{{numNavigation} تقرير تنقُّل في الصفحة}other{{numNavigation} تقرير تنقُّل في الصفحة}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{تدقيق واحد ({numPassableAudits}) يمكن اجتيازه}zero{{numPassableAudits} تدقيق يمكن اجتيازه}two{تدقيقان ({numPassableAudits}) يمكن اجتيازهما}few{{numPassableAudits} عمليات تدقيق يمكن اجتيازها}many{{numPassableAudits} تدقيقًا يمكن اجتيازه}other{{numPassableAudits} تدقيق يمكن اجتيازه}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{تم اجتياز تدقيق واحد ({numPassed})}zero{تم اجتياز {numPassed} تدقيق}two{تم اجتياز تدقيقَين ({numPassed})}few{تم اجتياز {numPassed} عمليات تدقيق}many{تم اجتياز {numPassed} تدقيقًا}other{تم اجتياز {numPassed} تدقيق}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "متوسط"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON>ي<PERSON> جيد"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON>ي<PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "الحالة التي تم تسجيلها للصفحة"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "تُجري التقارير الخاصة بلحظات معيّنة تحليلاً للصفحة في حالة مُحدَّدة، عادةً ما بعد تفاعلات المستخدم."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "التقرير الخاص بالصفحة في لحظة معيَّنة"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{تقرير واحد ({numSnapshot}) لتقييم الصفحة في لحظة معيَّنة}zero{{numSnapshot} تقرير لتقييم الصفحة في لحظة معيَّنة}two{تقريران ({numSnapshot}) لتقييم الصفحة في لحظة معيَّنة}few{{numSnapshot} تقارير لتقييم الصفحة في لحظة معيَّنة}many{{numSnapshot} تقريرًا لتقييم الصفحة في لحظة معيَّنة}other{{numSnapshot} تقرير لتقييم الصفحة في لحظة معيَّنة}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "ملخّص"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "تفاعلات المستخدمين"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "تحلِّل تقارير الفترات الزمنية أداء صفحة خلال فترات زمنية عشوائية، وعادةً ما تتضمّن تفاعلات المستخدم."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "تقرير الإطار الزمني"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{تقرير واحد ({numTimespan}) لتقييم الصفحة خلال فترة زمنية}zero{{numTimespan} تقرير لتقييم الصفحة خلال فترة زمنية}two{تقريران ({numTimespan}) لتقييم الصفحة خلال فترة زمنية}few{{numTimespan} تقارير لتقييم الصفحة خلال فترة زمنية}many{{numTimespan} تقريرًا لتقييم الصفحة خلال فترة زمنية}other{{numTimespan} تقرير لتقييم الصفحة خلال فترة زمنية}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "تقرير تدفق المستخدمين في أداة Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "بالنسبة إلى المحتوى الذي يتضمن صورًا متحركة، يمكنك استخدام [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) لتقليل استخدام وحدة المعالجة المركزية (CPU) عندما يكون المحتوى خارج الشاشة."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "يجب عرض جميع مكوِّنات [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) بتنسيق WebP مع تحديد عنصر احتياطي مناسب للمتصفّحات الأخرى. [مزيد من المعلومات](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "يُرجى الحرص على استخدام [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) حتى يتم تحميل الصور ببطء تلقائيًا. [مزيد من المعلومات](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "استخدِم الأدوات، مثل [مُحسِّن AMP](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) في [تنسيقات AMP للعرض من جانب الخادم](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "يُرجى الرجوع إلى [مستندات AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) للتأكد من توفُّر جميع الأنماط."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "يوفّر المكوِّن [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) السمة [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) لتحديد مواد عرض الصور المطلوب استخدامها استنادًا إلى حجم الشاشة. [مزيد من المعلومات](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "عليك استخدام التمرير الافتراضي مع مجموعة تطوير المكوّنات (CDK) في حال عرض قوائم كبيرة جدًا. [مزيد من المعلومات](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "يمكنك تطبيق [تقسيم الرموز على مستوى المسار](https://web.dev/route-level-code-splitting-in-angular/) لتقليل حجم حِزم JavaScript. ويجب أيضًا دفع تكاليف مواد العرض نقدًا ومُسبقًا من خلال [مشغّل خدمات Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "في حال استخدام Angular CLI، يُرجى التأكد من أنه يتم إنشاء الإصدارات في وضع الإنتاج. [مزيد من المعلومات](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "في حال استخدام Angular CLI، يُرجى تضمين خرائط المصدر في إصدار الإنتاج لفحص الحِزم. [مزيد من المعلومات](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "عليك التحميل المسبق للمسارات لتسريع عملية التنقل. [مزيد من المعلومات](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "عليك استخدام الأداة `BreakpointObserver` في مجموعة تطوير المكوّنات (CDK) لإدارة النقاط الفاصلة للصور. [مزيد من المعلومات](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "يمكنك تحميل ملف GIF إلى خدمة ستتيح تضمينه في شكل فيديو HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "يمكنك اختيار `@font-display` عند تحديد خطوط مخصّصة في التصميم."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "يمكنك إعداد [تنسيقات الصور WebP باستخدام طريقة تحويل الصور](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) على موقعك الإلكتروني."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "يمكنك تثبيت [وحدة Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) تتيح تحميل الصور ببطء. وتتيح هذه الوحدات إمكانية تأجيل أي صور خارج الشاشة لتحسين الأداء."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "يمكنك استخدام وحدة لتضمين لغتَيّ CSS وJavaScript المهمّتَين أو تحميل الأصول بشكلٍ غير متزامن من خلال JavaScript مثل وحدة [تجميع CSS/JS المتقدّم](https://www.drupal.org/project/advagg). يجب الانتباه إلى أنّ التحسينات التي توفّرها هذه الوحدة قد توقف موقعك الإلكتروني، لذلك من المحتمل أن تحتاج إلى إجراء تغييرات في الرمز البرمجي."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "تساهم التصاميم والوحدات ومواصفات الخادم في تحسين وقت استجابة الخادم. يمكنك البحث عن تصميم مُحسّن بدرجة أكبر و/أو اختيار وحدة تحسين بعناية و/أو ترقية الخادم. ويجب أن تستفيد خوادم الاستضافة من التخزين المؤقت لأجزاء عمليات لغة PHP والتخزين المؤقت للذاكرة لتقليل الأوقات التي تستغرقها طلبات قواعد البيانات مثل Redis أو Memcached، بالإضافة إلى منطق التطبيق المحسّن لتحضير الصفحات بشكلٍ أسرع."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "يمكنك استخدام [أنماط الصور السريعة الاستجابة](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) لتقليل حجم الصور المحمّلة على صفحتك. وإذا كنت تستخدم \"Views\" لعرض عناصر محتوى متعددة على صفحة، يمكنك التقسيم على صفحات بدلاً من ذلك للحدّ من عدد عناصر المحتوى المعروضة في صفحة معيّنة."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "يُرجى التأكّد من تفعيل \"تجميع ملفات CSS\" في صفحة \"الإدارة » الإعداد » التطوير\". ويمكنك إعداد خيارات تجميع أكثر تقدّمًا من خلال [وحدات إضافية](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) لزيادة سرعة موقعك الإلكتروني عن طريق ربط أنماط لغة CSS وتصغيرها وضغطها."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "يُرجى التأكّد من تفعيل \"تجميع ملفات JavaScript\" في صفحة \"الإدارة » الإعداد » التطوير\". ويمكنك إعداد خيارات تجميع أكثر تقدّمًا من خلال [وحدات إضافية](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) لزيادة سرعة موقعك الإلكتروني عن طريق ربط أصول JavaScript وتصغيرها وضغطها."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "يمكنك إزالة قواعد CSS غير المستخدمة وإرفاق مكتبات Drupal المطلوبة فقط بالصفحة ذات الصلة أو بالمكوّن ذي الصلة في الصفحة. للحصول على التفاصيل، يُرجى الاطّلاع على [رابط مستندات Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). لتحديد المكتبات المرفَقة التي تضيف لغة CSS دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في Chrome DevTools. ويمكنك تحديد التصميم/الوحدة المسؤولة من عنوان URL لورقة الأنماط عندما يكون تجميع لغة CSS قيد الإيقاف في موقعك الإلكتروني على Drupal. يمكنك البحث عن التصاميم/الوحدات التي تحتوي على العديد من أوراق الأنماط في القائمة والتي تتضمن الكثير من اللون الأحمر في تغطية الرمز البرمجي. ومن المفترض أن يدرِج التصميم/الوحدة ورقة أنماط فقط في حال كانت مُستخدَمة في الصفحة."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "يمكنك إزالة أصول ملفات JavaScript غير المستخدمة وإرفاق مكتبات Drupal المطلوبة فقط بالصفحة ذات الصلة أو بالمكوِّن ذي الصلة في الصفحة. للحصول على التفاصيل، يُرجى الاطّلاع على [رابط مستندات Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). لتحديد المكتبات المرفقة التي تضيف لغة JavaScript دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في Chrome DevTools. ويمكنك تحديد التصميم/الوحدة المسؤولة من عنوان URL للنص البرمجي عندما يكون تجميع لغة JavaScript قيد الإيقاف في موقعك الإلكتروني على Drupal. يمكنك البحث عن التصاميم/الوحدات التي تحتوي على العديد من النصوص البرمجية في القائمة والتي تتضمن الكثير من اللون الأحمر في تغطية الرمز البرمجي. ومن المفترض أن يدرِج التصميم/الوحدة نصًا برمجيًا فقط في حال كان مُستخدَمًا في الصفحة."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "يمكنك ضبط \"الحد الأقصى لعمر المتصفِّح وذاكرة التخزين المؤقت للوكيل\" في صفحة \"الإدارة » الإعداد » التطوير\". ويمكنك الاطّلاع على مزيد من المعلومات عن [ذاكرة التخزين المؤقت في Drupal وتحسين الأداء](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "يمكنك استخدام [وحدة](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) تتولّى تلقائيًا مهمّة تحسين وتقليل حجم الصور التي يتم تحميلها على موقعك الإلكتروني مع المحافظة على مستوى الجودة. ويُرجى التأكّد من أنّك تستخدم [أنماط الصور السريعة الاستجابة](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) الأصلية والتي يوفّرها Drupal (متوفّرة في Drupal 8 والإصدارات الأحدث)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "يمكنك إضافة تعديلات الموارد preconnect أو dns-prefetch من خلال تثبيت وإعداد [وحدة](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) تسهّل إضافة تعديلات الموارد المُقدّمة للوكيل المستخدم."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "يُرجى التأكّد من أنّك تستخدم [أنماط الصور السريعة الاستجابة](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) الأصلية والتي يوفّرها Drupal (متوفّرة في Drupal 8 والإصدارات الأحدث). ويمكنك استخدام أنماط الصور السريعة الاستجابة عند عرض حقول الصور من خلال أوضاع العرض أو طرق العرض أو الصور التي يتم تحميلها من خلال محرِّر WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Optimize Fonts` للاستفادة تلقائيًا من ميزة ‎`font-display` CSS لضمان أن يكون النص مرئيًا للمستخدم أثناء تحميل خطوط موقع ويب."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Next-Gen Formats` لتحويل الصور إلى تنسيق WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Lazy Load Images` لتأجيل تحميل الصور التي لا تظهر على الشاشة حتى يحين وقت الحاجة إليها."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعدادين `Critical CSS` و`Script Delay` لتأجيل تحميل ملفات JS/CSS غير المهمة."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "يمكنك استخدام خدمة [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) لتخزين المحتوى على شبكتنا في جميع أنحاء العالم، ما يحسّن وقت وصول أول بايت."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Minify CSS` لتصغير ملفات CSS تلقائيًا وتقليل أحجام حمولة الشبكة."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Minify Javascript` لتصغير ملف JS تلقائيًا وتقليل أحجام حمولة الشبكة."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Remove Unused CSS` للمساعدة في حلّ هذه المشكلة. يحدِّد هذا الإعداد فئات CSS المستخدَمة في كل صفحة من صفحات موقعك الإلكتروني، ويزيل أي فئات أخرى ليبقى حجم الملف صغيرًا."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Efficient Static Cache Policy` لضبط القيم المفضّلة للأصول الثابتة في ترويسات التخزين المؤقت."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Next-Gen Formats` لتحويل الصور إلى تنسيق WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Pre-Connect Origins` لإضافة تعديلات الموارد `preconnect` وإنشاء اتصالات مبكرة بالمصادر المُهمّة التابعة لجهات خارجية."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعدادين `Preload Fonts` و`Preload Background Images` لإضافة روابط `preload` لتحديد أولويات جلب الموارد المطلوبة حاليًا في وقتٍ لاحق في تحميل الصفحة."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "يمكنك استخدام الأداة [Ezoic Leap](https://pubdash.ezoic.com/speed) وتفعيل الإعداد `Resize Images` لتغيير حجم الصور إلى الحجم الملائم للجهاز، ما يقلّل أحجام حمولة الشبكة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "يمكنك تحميل ملف GIF إلى خدمة ستتيح تضمينه في شكل فيديو HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "يمكنك استخدام [مكون إضافي](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) أو خدمة تتيح لك تحويل صورك المحمَّلة إلى أفضل التنسيقات تلقائيًا."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "يمكنك تثبيت [مكوِّن Joomla الإضافي المُساعِد في التحميل البطيء](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) والذي يتيح لك إمكانية تأجيل أي صور خارج الشاشة، أو يمكنك الانتقال إلى نموذج يوفّر هذه الوظيفة. وبدءًا من الإصدار Joomla 4.0، تحصل كل الصور الجديدة [تلقائيًا](https://github.com/joomla/joomla-cms/pull/30748) على السمة `loading` من الحزمة الأساسية."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "هناك عدد من مكوّنات Joomla الإضافية التي يمكنها مساعدتك على [تضمين الأصول المهمة](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) أو [تأجيل الموارد الأقل أهمية](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). يجب الانتباه إلى أنّ التحسينات التي توفّرها هذه المكوّنات قد توقف ميزات النماذج أو المكوّنات الإضافية، لذلك ستحتاج إلى اختبارها بدقة تامّة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "تساهم النماذج والإضافات ومواصفات الخادم جميعًا في تحسين وقت استجابة الخادم. يمكنك البحث عن نموذج مُحسّن بدرجة أكبر و/أو اختيار إضافة للتحسين و/أو ترقية الخادم."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "يمكنك عرض مقتطفات في فئات مقالاتك (مثلًا من خلال رابط \"قراءة المزيد\")، أو تقليل عدد المقالات المعروضة في صفحة معينة، أو تقسيم مشاركاتك الطويلة إلى صفحات متعدّدة، أو استخدام مكوّن إضافي لتحميل التعليقات ببطء."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "يمكن لعدد من [إضا<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) زيادة سرعة موقعك الإلكتروني من خلال ربط أنماط لغة CSS وتصغيرها وضغطها. وهناك أيضًا نماذج توفّر هذه الوظيفة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "يمكن لعدد من [إضا<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) زيادة سرعة موقعك الإلكتروني من خلال ربط النصوص البرمجية وتصغيرها وضغطها. وهناك أيضًا نماذج توفّر هذه الوظيفة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "يمكنك تقليل أو تغيير عدد [إضافات Joom<PERSON>](https://extensions.joomla.org/) التي تُحمِّل في صفحتك لغة CSS غير المستخدمة. لتحديد الإضافات التي تضيف لغة CSS دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في Chrome DevTools. يمكنك تحديد التصميم/المكوّن الإضافي المسؤول من عنوان URL لورقة الأنماط. ويمكنك البحث عن المكوّنات الإضافية التي تحتوي على العديد من أوراق الأنماط في القائمة والتي تتضمن الكثير من اللون الأحمر في تغطية الرمز. ومن المفترض أن يُدرِج المكوّن الإضافي ورقة أنماط فقط في حال كانت مُستخدَمة في الصفحة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "يمكنك تقليل أو تغيير عدد [إضافات Joomla](https://extensions.joomla.org/) التي تُحمِّل في صفحتك ملفات JavaScript غير المستخدمة. لتحديد المكونات الإضافية التي تضيف لغة JavaScript دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في Chrome DevTools. يمكنك تحديد الإضافة المسؤولة من عنوان URL للنص البرمجي. ويمكنك البحث عن الإضافات التي تحتوي على العديد من النصوص البرمجية في القائمة والتي تتضمن الكثير من اللون الأحمر في تغطية الرمز البرمجي. ومن المفترض أن تُدرِج الإضافة نصًا برمجيًا فقط في حال كان مُستخدَمًا في الصفحة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "يمكنك الاطّلاع على المزيد من المعلومات عن [التخزين المؤقت للمتصفِّح في Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "يمكنك استخدام [مكوّن إضافي لتحسين الصورة](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) لضغط صورك مع المحافظة على مستوى الجودة."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "يمكنك استخدام [مكوّن إضافي للصور السريعة الاستجابة](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) لاستخدام صور سريعة الاستجابة في المحتوى."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "يمكنك تفعيل ميزة \"ضغط النص\" من خلال تفعيل ضغط صفحة Gzip في Joomla (النظام > الإعداد العام > الخادم)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "في حال عدم إنشاء حزمة لمواد عرض JavaScript، عليك استخدام [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "أوقِف [التصغير وحزمة JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) المدمجة للنظام الأساسي Magento واستخدِم [baler](https://github.com/magento/baler/) بدلاً من ذلك."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "حدِّد `@font-display` عند [وضع خطوط مخصصة](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "عليك البحث عن [سوق <PERSON>](https://marketplace.magento.com/catalogsearch/result/?q=webp) للحصول على مجموعة متنوعة من إضافات الجهات الخارجية للاستفادة من تنسيقات الصور الأحدث."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "عليك تعديل نماذج الكتالوج والمنتج للاستفادة من ميزة [التحميل الخامل](https://web.dev/native-lazy-loading) للنظام الأساسي للويب."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "استخدِم [دمج <PERSON>ar<PERSON>](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) للنظام الأساسي Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "فعِّل الخيار \"تصغير ملفات CSS\" في إعدادات مطوّر البرامج في المتجر. [مزيد من المعلومات](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "يمكنك استخدام [Terser](https://www.npmjs.com/package/terser) لتصغير جميع أصول JavaScript التي تنتُج عن نشر المحتوى الثابت، وإيقاف ميزة التصغير المضمَّنة."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "أوقِف [حزمة JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) المدمجة للنظام الأساسي Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "عليك البحث عن [سوق <PERSON>o](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) للحصول على مجموعة متنوعة من إضافات الجهات الخارجية لتحسين الصور."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "يمكن إضافة تعديلات الموارد preconnect أو dns-prefetch من خلال [تعديل تنسيق التصميمات](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "يمكن إضافة علامات `<link rel=preload>` عن طريق [تعديل تنسيق التصميمات](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "يمكنك استخدام المكوّن `next/image` بدلاً من `<img>` لتحسين تنسيق الصورة تلقائيًا. [مزيد من المعلومات](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "يمكنك استخدام المكوّن `next/image` بدلاً من `<img>` لتحميل الصور تلقائيًا باستخدام طريقة التحميل الكسول. [مزيد من المعلومات](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "يمكنك استخدام المكوِّن `next/image` وضبط \"الأولوية\" على \"صحيح\" لإجراء التحميل المُسبق لصورة المقياس \"سرعة عرض أكبر جزء من المحتوى على الصفحة (LCP)\". [مزيد من المعلومات](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "يمكنك استخدام المكوِّن `next/script` لتأجيل تحميل النصوص البرمجية غير المهمة التابعة لجهات خارجية. [مزيد من المعلومات](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "يمكنك استخدام المكوّن `next/image` للتأكد من أنّ حجم الصور مضبوط دائمًا بالشكل المناسب. [مزيد من المعلومات](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "يمكنك إعداد `PurgeCSS` في عملية ضبط `Next.js` لإزالة أي قواعد غير مستخدَمة من أوراق الأنماط. [مزيد من المعلومات](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "يمكنك استخدام الأداة `Webpack Bundle Analyzer` لرصد رمز JavaScript غير المستخدَم. [مزيد من المعلومات](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "يمكنك استخدام `Next.js Analytics` لقياس الأداء الفعلي العالمي لتطبيقك. [مزيد من المعلومات](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "يمكنك ضبط ميزة التخزين المؤقت لمواد العرض وصفحات `Server-side Rendered` (SSR) الثابتة. [مزيد من المعلومات](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "يمكنك استخدام المكوّن `next/image` بدلاً من `<img>` لتعديل جودة الصورة. [مزيد من المعلومات](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "يمكنك استخدام المكوِّن `next/image` لضبط `sizes` المناسبة. [مزيد من المعلومات](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "يمكنك تفعيل ميزة ضغط البيانات في خادم Next.js الخاص بك. [مزيد من المعلومات](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "يمكنك استخدام المكوّن `nuxt/image` وضبط `format=\"webp\"`. [مزيد من المعلومات](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "يمكنك استخدام المكوّن `nuxt/image` وضبط `loading=\"lazy\"` لتأجيل تحميل الصور خارج الشاشة. [مزيد من المعلومات](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "يمكنك استخدام المكوِّن `nuxt/image` وتحديد `preload` لصورة المقياس LCP. [مزيد من المعلومات](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "يمكنك استخدام المكوّن `nuxt/image` واختيار `width` و`height` مُحدّدين. [مزيد من المعلومات](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "يمكنك استخدام المكوِّن `nuxt/image` وضبط `quality` المناسبة. [مزيد من المعلومات](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "يمكنك استخدام المكوِّن `nuxt/image` وضبط `sizes` المناسبة. [مزيد من المعلومات](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[يمكنك استخدام الفيديو بدلاً من صور GIF المتحركة](https://web.dev/replace-gifs-with-videos/) لتحميل صفحات الويب بشكل أسرع، بالإضافة إلى استخدام تنسيقات ملفات حديثة، مثل [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) أو [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) لتحسين كفاءة الضغط بنسبة تزيد عن 30% على برنامج ترميز الفيديو المتطوّر الحالي، VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "يمكنك استخدام [مكوّن إضافي](https://octobercms.com/plugins?search=image) أو خدمة تتيح تحويل الصور المحمَّلة إلى أفضل التنسيقات تلقائيًا. يقل [حجم صور WebP بدون فقدان أي تفاصيل](https://developers.google.com/speed/webp) بنسبة 26% مقارنةً بصور PNG، ويقل بنسبة تتراوح بين 25% و34% مقارنةً بصور JPEG في مؤشر الجودة المعادِلة وفق مقياس مؤشر التشابه الهيكلي (SSIM). يمكن استخدام تنسيق [AVIF](https://jakearchibald.com/2020/avif-has-landed/) لعرض الصور بتنسيقات الجيل القادم."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "يمكنك تثبيت [مكوّن إضافي لتحميل الصور ببطء](https://octobercms.com/plugins?search=lazy) يتيح لك إمكانية تأجيل عرض أي صور خارج الشاشة، أو يمكنك الانتقال إلى مظهر يوفّر هذه الوظيفة. يمكنك أيضًا استخدام [مكون AMP الإضافي](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "ثمّة مكوّنات إضافية تساعد على تشغيل [مواد العرض المهمة المضمَّنة](https://octobercms.com/plugins?search=css) على نحو سليم. قد تتسبب هذه المكوّنات الإضافية في إيقاف مكونّات إضافية أخرى، لذا يجب اختبارها جيدًا."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "تساهم المظاهر والمكونات الإضافية ومواصفات الخادم في تحسين وقت استجابة الخادم. ابحث عن مظهر محسَّن للغاية و/أو اختَر مكوّنًا إضافيًا و/أو قم بترقية الخادم. إنّ منصّة October لإدارة المحتوى تتيح لمطوّري البرامج إمكانية استخدام [`Queues`](https://octobercms.com/docs/services/queues) لتأجيل معالجة أي مَهمّة تستهلك الوقت، مثل إرسال رسالة إلكترونية. ويساعد ذلك على تسريع تنفيذ طلبات الويب بشدّة."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "يمكنك عرض مقتطفات في قائمة المشاركات (مثلاً، باستخدام زر `show more`) أو تقليل عدد المشاركات المعروضة في صفحة ويب معيّنة أو تقسيم المشاركات الطويلة إلى صفحات ويب متعدّدة أو استخدام مكوّن إضافي لتحميل التعليقات ببطء."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "يمكن لعدد من [المكوّنات الإضافية](https://octobercms.com/plugins?search=css) زيادة سرعة موقعك الإلكتروني من خلال دمج الأنماط وتصغيرها وضغطها. يمكن تسريع عملية التطوير من خلال استخدام عملية تصميم تتيح تصغير الأنماط في البداية."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "يمكن لعدد من [المكوّنات الإضافية](https://octobercms.com/plugins?search=javascript) زيادة سرعة الموقع الإلكتروني من خلال دمج النصوص البرمجية وضغطها وتصغيرها. يمكن تسريع عملية التطوير من خلال استخدام عملية تصميم تتيح تصغير الأنماط في البداية."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "يمكنك مراجعة [المكوّنات الإضافية](https://octobercms.com/plugins) التي تُحمِّل على الموقع الإلكتروني صفحات الأنماط المتتالية (CSS) غير المستخدَمة. لتحديد المكوّنات الإضافية التي تضيف صفحات الأنماط المتتالية (CSS) غير الضرورية، يمكنك تشغيل مقياس [نسبة استخدام رموز الصفحة](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في \"أدوات مطوري البرامج في Chrome\". يمكنك تحديد المظهر/المكوّن الإضافي المسؤول من عنوان URL لورقة الأنماط. ابحث عن المكونات الإضافية التي تتضمّن العديد من أوراق الأنماط المشتمِلة على الكثير من اللون الأحمر في مقياس نسبة استخدام رموز الصفحة. ومن المفترض أن يضيف المكوّن الإضافي ورقة أنماط فقط إذا كانت مُستخدَمة حاليًا في الصفحة."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "يمكنك مراجعة [المكوّنات الإضافية](https://octobercms.com/plugins?search=javascript) التي تُحمِّل في صفحة الويب ملفات JavaScript غير مستخدَمة. لتحديد المكوّنات الإضافية التي تضيف ملفات JavaScript غير ضرورية، يمكنك تشغيل مقياس [نسبة استخدام رموز الصفحة](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في \"أدوات مطوري البرامج في Chrome\". ويمكن تحديد المظهر/المكوّن الإضافي المعنيّ من عنوان URL للنص البرمجي. ابحث عن المكونات الإضافية التي تتضمّن العديد من النصوص البرمجية المشتمِلة على الكثير من اللون الأحمر في مقياس نسبة استخدام رموز الصفحة. ومن المفترض أن يضيف المكوّن الإضافي نصًا برمجيًا فقط إذا كان مُستخدَمًا حاليًا في صفحة الويب."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "يمكنك الاطّلاع على قسم [منع الطلبات غير الضرورية من الشبكة باستخدام ذاكرة التخزين المؤقت عبر HTTP](https://web.dev/http-cache/#caching-checklist). ويمكن استخدام العديد من [المكوّنات الإضافية](https://octobercms.com/plugins?search=Caching) لتسريع التخزين المؤقت."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "يمكنك استخدام [مكوّن إضافي لتحسين الصور](https://octobercms.com/plugins?search=image) لضغط الصور مع الاحتفاظ بمستوى الجودة."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "يمكنك تحميل الصور مباشرةً من خلال مدير الوسائط للتأكّد من توفّر أحجام الصور المطلوبة. وبإمكانك استخدام [فلتر تغيير الحجم](https://octobercms.com/docs/markup/filter-resize) أو [مكوّن إضافي لتغيير حجم الصور](https://octobercms.com/plugins?search=image) للتأكّد من استخدام أفضل الأحجام للصور."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "يمكنك تفعيل ضغط النص في إعداد خادم الويب."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "يمكنك استخدام مكتبة \"عرض المحتوى في النافذة الحالية\" مثل `react-window` لتقليل عدد عُقَد DOM التي يتم إنشاؤها في حال عرض العديد من العناصر المتكررة على الصفحة. [يُرجى الاطّلاع على مزيد من المعلومات.](https://web.dev/virtualize-long-lists-react-window/) ويمكنك تقليل عمليات إعادة العرض غير الضرورية باستخدام [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action) أو [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) أو [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) و[تخطّي التأثيرات](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) فقط حتى يتم تغيير اعتماديات معيّنة في حال استخدام موضع الإدراج `Effect` لتحسين مستوى أداء وقت التشغيل."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "في حال استخدام React Router، عليك تقليل استخدام المكوّن `<Redirect>` في [عمليات تنقل المسار](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "في حال كنت تعرض من جانب الخادم أي مكوّنات React، عليك استخدام `renderToPipeableStream()` أو `renderToStaticNodeStream()` للسماح للعميل بتلقي أجزاء مختلفة من الترميز ودمجها بدلاً من جميع الأجزاء دفعة واحدة. [مزيد من المعلومات](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "في حال تصغير نظام الإصدار لملفات CSS تلقائيًا، يُرجى التأكد من أنك تنشر إصدار الإنتاج لتطبيقك. يمكنك التحقّق من ذلك باستخدام إضافة React Developer Tools. [مزيد من المعلومات](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "في حال تصغير نظام الإصدار لملفات JavaScript تلقائيًا، يُرجى التأكد من أنك تنشر إصدار الإنتاج لتطبيقك. يمكنك التحقّق من ذلك باستخدام إضافة React Developer Tools. [مزيد من المعلومات](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "في حال عدم العرض من جانب الخادم، عليك [تقسيم حِزم JavaScript](https://web.dev/code-splitting-suspense/) من خلال `React.lazy()`. بخلاف ذلك، يمكن تقسيم الرمز باستخدام مكتبة تابعة لجهات خارجية، مثل [المكوّنات القابلة للتحميل](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "استخدِم محلِّل React DevTools الذي يتيح الاستفادة من واجهة برمجة تطبيقات المحلّل لقياس مستوى أداء العرض للمكوّنات. [مزيد من المعلومات](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "يمكنك تحميل ملف GIF إلى خدمة ستتيح تضمينه في شكل فيديو HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "يمكنك استخدام المكوِّن الإضافي [Performance Lab](https://wordpress.org/plugins/performance-lab/) لتحويل الصور المحمّلة بتنسيق JPEG إلى تنسيق WebP بشكل تلقائي، إذا كان ذلك متاحًا."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "يمكنك تثبيت [مكون WordPress الإضافي للتحميل الكسول](https://wordpress.org/plugins/search/lazy+load/) الذي يوفر القدرة على تأجيل أي صور خارج الشاشة، أو التبديل إلى تصميم يوفِّر هذه القدرة الوظيفية. يمكنك أيضًا استخدام [مكون AMP الإضافي](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "هناك عدد من مكونات WordPress الإضافية التي يمكنها مساعدتك على [تضمين مواد العرض المهمة](https://wordpress.org/plugins/search/critical+css/) أو [تأجيل موارد أقل أهمية](https://wordpress.org/plugins/search/defer+css+javascript/). عليك توخي الحذر من أن التحسينات التي توفرها هذه الإضافات قد توقف ميزات التصميم أو المكونات الإضافية، لذلك ستحتاج على الأرجح إلى إجراء تغييرات في الرمز البرمجي."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "تساهم التصاميم والمكونات الإضافية ومواصفات الخادم في تحسين وقت استجابة الخادم. يمكنك البحث عن تصميم مُحسّن أكثر و/أو اختيار مكون إضافي للتحسين و/أو ترقية الخادم."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "يمكنك عرض مقتطفات في قوائم مشاركاتك (مثلاً عبر العلامة \"المزيد\")، أو تقليل عدد المشاركات المعروضة في صفحة معينة، أو تقسيم مشاركاتك الطويلة إلى صفحات متعددة، أو استخدام مكون إضافي لتحميل التعليقات ذات التحميل الكسول."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "يمكن لعدد من [مكونات WordPress الإضافية](https://wordpress.org/plugins/search/minify+css/) زيادة سرعة موقعك الإلكتروني من خلال ربط الأنماط وتصغيرها وضغطها. يمكنك أيضًا استخدام عملية إنشاء الموقع الإلكتروني لإزالة البيانات غير الضرورية بشكل مسبق إذا أمكن ذلك."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "يمكن لعدد من [مكونات WordPress الإضافية ](https://wordpress.org/plugins/search/minify+javascript/) زيادة سرعة موقعك الإلكتروني من خلال ربط النصوص البرمجية وتصغيرها وضغطها. يمكنك أيضًا استخدام عملية إنشاء الموقع الإلكتروني لإزالة البيانات غير الضرورية بشكل مسبق إذا أمكن ذلك."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "يمكنك تقليل عدد [مكونات WordPress الإضافية](https://wordpress.org/plugins/) التي تُحمِّل خدمة CSS غير المُستخدَمة في صفحتك أو تبديلها. لتحديد المكونات الإضافية التي تضيف CSS دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developer.chrome.com/docs/devtools/coverage/) في Chrome DevTools. يمكنك تحديد التصميم/المكون الإضافي المسؤول عن عنوان URL لورقة الأنماط. يمكنك البحث عن المكونات الإضافية التي تحتوي على العديد من أوراق الأنماط في القائمة والتي تحتوي على الكثير من اللون الأحمر في تغطية الرمز البرمجي. يجب أن يدرِج المكون الإضافي ورقة أنماط فقط في حال تم استخدامه في الصفحة فعليًا."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "يمكنك تقليل عدد [مكونات WordPress الإضافية](https://wordpress.org/plugins/) التي تُحمِّل لغة JavaScript غير المُستخدَمة في صفحتك أو تبديلها. لتحديد المكونات الإضافية التي تضيف لغة JavaScript دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developer.chrome.com/docs/devtools/coverage/) في Chrome DevTools. يمكنك تحديد التصميم/المكون الإضافي المسؤول عن عنوان URL للنص البرمجي. يمكنك البحث عن المكونات الإضافية التي تحتوي على العديد من النصوص البرمجية في القائمة والتي تحتوي على الكثير من اللون الأحمر في تغطية الرمز البرمجي. يجب أن يدرِج المكون الإضافي نصًا برمجيًا فقط في حال تم استخدامه في الصفحة فعليًا."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "يمكنك الاطّلاع على [ذاكرة التخزين المؤقت للمتصفّح في WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "يمكنك استخدام [مكون WordPress الإضافي لتحسين الصورة](https://wordpress.org/plugins/search/optimize+images/) الذي يضغط صورك مع المحافظة على الجودة."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "يمكنك تحميل الصور مباشرةً من خلال [مكتبة الوسائط](https://wordpress.org/support/article/media-library-screen/) للتأكّد من توفّر أحجام الصور المطلوبة، ثم إدراجها من مكتبة الوسائط أو استخدام أداة الصورة لضمان استخدام أفضل حجم للصورة (بما في ذلك تلك الخاصة بنقاط فاصلة متجاوبة). يمكنك تجنب استخدام صور `Full Size` إلا إذا كانت الأبعاد كافية لاستخدامها. [مزيد من المعلومات](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "يمكنك تفعيل ضغط النص في إعداد خادم الويب."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "يمكنك تفعيل الإضافة Imagify من علامة التبويب Image Optimization (تحسين الصور) في WP Rocket لتحويل تنسيق صورك إلى WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "يمكنك تفعيل ميزة [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) (تأخير التحميل) في WP Rocket للتعامل مع هذا الاقتراح. تؤخّر هذه الميزة تحميل الصور إلى أن ينتقل الزائر إلى أسفل الصفحة ويحتاج إلى الاطّلاع على الصور."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "يمكنك تفعيل ميزتَي [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (إزالة محتوى CSS غير المستخدَم) و[Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (تأجيل تحميل JavaScript) في WP Rocket لمعالجة هذا الاقتراح. ستعمل هاتان الميزتان على تحسين ملفات CSS وJavaScript على التوالي بحيث لا تمنع عرض صفحتك."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "يمكنك تفعيل ميزة [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (تصغير ملفات CSS) في WP Rocket لحل هذه المشكلة. ستتم إزالة أي مساحات وتعليقات في ملفات CSS الخاصة بموقعك الإلكتروني لتصغير حجم الملف وزيادة سرعة تنزيله."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "يمكنك تفعيل ميزة [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (تصغير ملفات JavaScript) في WP Rocket لحل هذه المشكلة. ستتم إزالة المساحات الفارغة والتعليقات من ملفات JavaScript لتصغير حجمها وزيادة سرعة تنزيلها."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "يمكنك تفعيل ميزة [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (إزالة محتوى CSS غير المستخدَم) في WP Rocket لحل هذه المشكلة. تُقلِّل هذه الميزة حجم الصفحة عن طريق إزالة كل محتوى CSS وأوراق الأنماط غير المُستخدَمة مع الاحتفاظ بمحتوى CSS المستخدَم فقط لكل صفحة."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "يمكنك تفعيل ميزة [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (تأخير تنفيذ JavaScript) في WP Rocket لحل هذه المشكلة. سيؤدي ذلك إلى تحسين عملية تحميل الصفحة من خلال تأخير تنفيذ النصوص البرمجية إلى أن يتفاعل المستخدم مع الصفحة. إذا كان موقعك الإلكتروني يحتوي على إطارات iframe، يمكنك استخدام ميزتَي [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (تفعيل ميزة \"تأخير التحميل\" لتحميل إطارات iframe والفيديوهات) و[Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (استبدال إطار iframe في YouTube بصورة المعاينة) في WP Rocket."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "يمكنك تفعيل الإضافة Imagify من علامة التبويب Image Optimization (تحسين الصور) في WP Rocket ثم إجراء عملية Bulk Optimization (تحسين مُجمَّع) لضغط صورك."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "يمكنك استخدام ميزة [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (طلبات الجلب المسبق لنظام أسماء النطاقات) في WP Rocket لإضافة dns-prefetch وزيادة سرعة الاتصال بالنطاقات الخارجية. يضيف WP Rocket أيضًا التلميح preconnect بشكل تلقائي إلى [نطاق Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) وأي إعدادات CNAME تتم إضافتها من خلال ميزة [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (تفعيل شبكة توصيل المحتوى (CDN))."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "لحل هذه المشكلة بالنسبة إلى الخطوط، يمكنك تفعيل ميزة [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (إزالة محتوى CSS غير المستخدَم) في WP Rocket. سيتم تحديد الأولوية لتحميل الخطوط المهمة مسبقًا في موقعك الإلكتروني."}, "report/renderer/report-utils.js | calculatorLink": {"message": "اطّلِع على الآلة الحاسبة."}, "report/renderer/report-utils.js | collapseView": {"message": "تصغير العرض"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "التنقل الأوّلي"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "الح<PERSON> الأقصى لوقت استجابة المسار المهم:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "نسخ كائن JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "تبديل المظهر الداكن"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "توسيع مدى الطباعة"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "طباعة ملخّص التقرير"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "ح<PERSON><PERSON> بتنسيق Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "حفظ بتنسيق HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "حفظ بتنسيق JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "فتح في العارِض"}, "report/renderer/report-utils.js | errorLabel": {"message": "خطأ!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "الإبلاغ عن خطأ: لا تتوفَّر معلومات تدقيق"}, "report/renderer/report-utils.js | expandView": {"message": "توسيع العرض"}, "report/renderer/report-utils.js | footerIssue": {"message": "الإبلاغ عن مشكلة"}, "report/renderer/report-utils.js | hide": {"message": "إخفاء"}, "report/renderer/report-utils.js | labDataTitle": {"message": "بيانات المختبَر"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "أجرت أداة [Lighthouse](https://developers.google.com/web/tools/lighthouse/) تحليلًا للصفحة الحالية على شبكة الجوّال في وضع المحاكاة. القيم تقديرية وقابلة للتغيير."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "عناصر إضافية للتحقُّق يدويًا"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "غير سارٍ"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "فرصة تحسين الأداء"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "التوفيرات المُقدرة"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "اجتياز عمليات التدقيق بنجاح"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "التحميل الأولي للصفحة"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "تقييد مخصَّص لتحميل الصفحة"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "محاكاة سطح المكتب"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "بلا محاكاة"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "إصد<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "طاقة وحدة المعالجة المركزية (CPU)/الذاكرة غير مقيَّدة"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "تقييد وحدة المعالجة المركزية (CPU)"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "<PERSON><PERSON><PERSON> الح<PERSON> الأقصى المسموح لعرض نطاق الشبكة"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "محاكاة الشاشة"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "وكيل المستخدم (شبكة)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "تحميل صفحة واحدة"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "هذه البيانات مأخوذة من صفحة واحدة تم تحميلها، على عكس بيانات الحقول التي تلخِّص العديد من الجلسات."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "التقييد البطيء لتحميل الصفحة في شبكة الجيل الرابع"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "غير معروف"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "عرض عمليات التدقيق المرتبطة بالمقاييس:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "تصغير المقتطف"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "توسيع المقتطف"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "عرض موارد الجهات الخارجية"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "مقدَّم من البيئة"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "حدثت مشاكل تؤثر في تشغيل Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "القيم تقديرية وقابلة للتغيير. ويتم [حساب نتيجة الأداء](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) مباشرة من خلال هذه المقاييس."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "عرض سجلّ التتبّع الأصلي"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "عرض سجلّ التتبُّع"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "عرض \"المخطّط الهيكلي\""}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "عمليات التدقيق التي تم اجتيازها، ولكن تتضمّن التحذيرات"}, "report/renderer/report-utils.js | warningHeader": {"message": "التحذيرات: "}, "treemap/app/src/util.js | allLabel": {"message": "الكل"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "جميع النصوص البرمجية"}, "treemap/app/src/util.js | coverageColumnName": {"message": "التغطية"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "الوحدات المكرَّرة"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "حجم الموارد بوحدة البايت"}, "treemap/app/src/util.js | tableColumnName": {"message": "الاسم"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "تبديل الجدول"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "وحدات بايت غير المستخدمة"}}