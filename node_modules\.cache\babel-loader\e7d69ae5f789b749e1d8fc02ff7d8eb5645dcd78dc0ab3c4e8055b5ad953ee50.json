{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\BackendTestButton.jsx\";\nimport React, { Component } from 'react';\nimport { Button } from 'primereact/button';\nimport { baseURL } from './generalizzazioni/apireq';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass BackendTestButton extends Component {\n  constructor(props) {\n    super(props);\n    this.testBackend = async () => {\n      this.setState({\n        testing: true\n      });\n      try {\n        console.log('🧪 Manual backend test initiated');\n        const response = await fetch(baseURL + '/health', {\n          method: 'GET',\n          timeout: 5000\n        });\n        if (response.ok) {\n          this.setState({\n            lastResult: 'success',\n            testing: false\n          });\n          this.showNotification('success', '✅ Backend Test Successful', \"Backend is responding correctly at \".concat(baseURL));\n        } else {\n          throw new Error(\"HTTP \".concat(response.status));\n        }\n      } catch (error) {\n        this.setState({\n          lastResult: 'error',\n          testing: false\n        });\n        this.showNotification('error', '❌ Backend Test Failed', \"Cannot connect to \".concat(baseURL, \": \").concat(error.message));\n      }\n    };\n    this.showNotification = (type, title, message) => {\n      const notification = document.createElement('div');\n      notification.style.cssText = \"\\n            position: fixed;\\n            top: 20px;\\n            right: 20px;\\n            z-index: 9999;\\n            padding: 15px 20px;\\n            border-radius: 8px;\\n            color: white;\\n            font-family: Arial, sans-serif;\\n            font-size: 14px;\\n            max-width: 400px;\\n            box-shadow: 0 4px 12px rgba(0,0,0,0.3);\\n            background: \".concat(type === 'success' ? '#4CAF50' : '#f44336', \";\\n            animation: slideIn 0.3s ease-out;\\n        \");\n      notification.innerHTML = \"\\n            <div style=\\\"font-weight: bold; margin-bottom: 5px;\\\">\".concat(title, \"</div>\\n            <div style=\\\"font-size: 12px; opacity: 0.9;\\\">\").concat(message, \"</div>\\n        \");\n      document.body.appendChild(notification);\n      setTimeout(() => {\n        notification.style.animation = 'slideIn 0.3s ease-out reverse';\n        setTimeout(() => {\n          if (notification.parentNode) {\n            notification.parentNode.removeChild(notification);\n          }\n        }, 300);\n      }, 4000);\n    };\n    this.state = {\n      testing: false,\n      lastResult: null\n    };\n  }\n  render() {\n    const {\n      testing,\n      lastResult\n    } = this.state;\n    let buttonSeverity = 'secondary';\n    let buttonIcon = 'pi pi-wifi';\n    if (lastResult === 'success') {\n      buttonSeverity = 'success';\n      buttonIcon = 'pi pi-check';\n    } else if (lastResult === 'error') {\n      buttonSeverity = 'danger';\n      buttonIcon = 'pi pi-times';\n    }\n    return /*#__PURE__*/_jsxDEV(Button, {\n      label: \"Test Backend\",\n      icon: buttonIcon,\n      severity: buttonSeverity,\n      loading: testing,\n      onClick: this.testBackend,\n      size: \"small\",\n      tooltip: \"Test connection to \".concat(baseURL),\n      tooltipOptions: {\n        position: 'top'\n      },\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        zIndex: 1000,\n        minWidth: '120px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default BackendTestButton;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "baseURL", "jsxDEV", "_jsxDEV", "BackendTestButton", "constructor", "props", "testBackend", "setState", "testing", "console", "log", "response", "fetch", "method", "timeout", "ok", "lastResult", "showNotification", "concat", "Error", "status", "error", "message", "type", "title", "notification", "document", "createElement", "style", "cssText", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "animation", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "state", "render", "buttonSeverity", "buttonIcon", "label", "icon", "severity", "loading", "onClick", "size", "tooltip", "tooltipOptions", "position", "bottom", "right", "zIndex", "min<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/BackendTestButton.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport { Button } from 'primereact/button';\nimport { baseURL } from './generalizzazioni/apireq';\n\nclass BackendTestButton extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            testing: false,\n            lastResult: null\n        };\n    }\n\n    testBackend = async () => {\n        this.setState({ testing: true });\n        \n        try {\n            console.log('🧪 Manual backend test initiated');\n            \n            const response = await fetch(baseURL + '/health', {\n                method: 'GET',\n                timeout: 5000\n            });\n            \n            if (response.ok) {\n                this.setState({ \n                    lastResult: 'success',\n                    testing: false \n                });\n                this.showNotification('success', '✅ Backend Test Successful', \n                    `Backend is responding correctly at ${baseURL}`);\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (error) {\n            this.setState({ \n                lastResult: 'error',\n                testing: false \n            });\n            this.showNotification('error', '❌ Backend Test Failed', \n                `Cannot connect to ${baseURL}: ${error.message}`);\n        }\n    }\n\n    showNotification = (type, title, message) => {\n        const notification = document.createElement('div');\n        notification.style.cssText = `\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            z-index: 9999;\n            padding: 15px 20px;\n            border-radius: 8px;\n            color: white;\n            font-family: Arial, sans-serif;\n            font-size: 14px;\n            max-width: 400px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n            background: ${type === 'success' ? '#4CAF50' : '#f44336'};\n            animation: slideIn 0.3s ease-out;\n        `;\n        \n        notification.innerHTML = `\n            <div style=\"font-weight: bold; margin-bottom: 5px;\">${title}</div>\n            <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n        `;\n        \n        document.body.appendChild(notification);\n        \n        setTimeout(() => {\n            notification.style.animation = 'slideIn 0.3s ease-out reverse';\n            setTimeout(() => {\n                if (notification.parentNode) {\n                    notification.parentNode.removeChild(notification);\n                }\n            }, 300);\n        }, 4000);\n    }\n\n    render() {\n        const { testing, lastResult } = this.state;\n        \n        let buttonSeverity = 'secondary';\n        let buttonIcon = 'pi pi-wifi';\n        \n        if (lastResult === 'success') {\n            buttonSeverity = 'success';\n            buttonIcon = 'pi pi-check';\n        } else if (lastResult === 'error') {\n            buttonSeverity = 'danger';\n            buttonIcon = 'pi pi-times';\n        }\n        \n        return (\n            <Button\n                label=\"Test Backend\"\n                icon={buttonIcon}\n                severity={buttonSeverity}\n                loading={testing}\n                onClick={this.testBackend}\n                size=\"small\"\n                tooltip={`Test connection to ${baseURL}`}\n                tooltipOptions={{ position: 'top' }}\n                style={{ \n                    position: 'fixed', \n                    bottom: '20px', \n                    right: '20px', \n                    zIndex: 1000,\n                    minWidth: '120px'\n                }}\n            />\n        );\n    }\n}\n\nexport default BackendTestButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,iBAAiB,SAASL,SAAS,CAAC;EACtCM,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAAC,KAOjBC,WAAW,GAAG,YAAY;MACtB,IAAI,CAACC,QAAQ,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhC,IAAI;QACAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAE/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACZ,OAAO,GAAG,SAAS,EAAE;UAC9Ca,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;QACb,CAAC,CAAC;QAEF,IAAIH,QAAQ,CAACI,EAAE,EAAE;UACb,IAAI,CAACR,QAAQ,CAAC;YACVS,UAAU,EAAE,SAAS;YACrBR,OAAO,EAAE;UACb,CAAC,CAAC;UACF,IAAI,CAACS,gBAAgB,CAAC,SAAS,EAAE,2BAA2B,wCAAAC,MAAA,CAClBlB,OAAO,CAAE,CAAC;QACxD,CAAC,MAAM;UACH,MAAM,IAAImB,KAAK,SAAAD,MAAA,CAASP,QAAQ,CAACS,MAAM,CAAE,CAAC;QAC9C;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZ,IAAI,CAACd,QAAQ,CAAC;UACVS,UAAU,EAAE,OAAO;UACnBR,OAAO,EAAE;QACb,CAAC,CAAC;QACF,IAAI,CAACS,gBAAgB,CAAC,OAAO,EAAE,uBAAuB,uBAAAC,MAAA,CAC7BlB,OAAO,QAAAkB,MAAA,CAAKG,KAAK,CAACC,OAAO,CAAE,CAAC;MACzD;IACJ,CAAC;IAAA,KAEDL,gBAAgB,GAAG,CAACM,IAAI,EAAEC,KAAK,EAAEF,OAAO,KAAK;MACzC,MAAMG,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAClDF,YAAY,CAACG,KAAK,CAACC,OAAO,wYAAAX,MAAA,CAYRK,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,+DAE3D;MAEDE,YAAY,CAACK,SAAS,0EAAAZ,MAAA,CACoCM,KAAK,wEAAAN,MAAA,CACbI,OAAO,qBACxD;MAEDI,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;MAEvCQ,UAAU,CAAC,MAAM;QACbR,YAAY,CAACG,KAAK,CAACM,SAAS,GAAG,+BAA+B;QAC9DD,UAAU,CAAC,MAAM;UACb,IAAIR,YAAY,CAACU,UAAU,EAAE;YACzBV,YAAY,CAACU,UAAU,CAACC,WAAW,CAACX,YAAY,CAAC;UACrD;QACJ,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC;IAtEG,IAAI,CAACY,KAAK,GAAG;MACT7B,OAAO,EAAE,KAAK;MACdQ,UAAU,EAAE;IAChB,CAAC;EACL;EAoEAsB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE9B,OAAO;MAAEQ;IAAW,CAAC,GAAG,IAAI,CAACqB,KAAK;IAE1C,IAAIE,cAAc,GAAG,WAAW;IAChC,IAAIC,UAAU,GAAG,YAAY;IAE7B,IAAIxB,UAAU,KAAK,SAAS,EAAE;MAC1BuB,cAAc,GAAG,SAAS;MAC1BC,UAAU,GAAG,aAAa;IAC9B,CAAC,MAAM,IAAIxB,UAAU,KAAK,OAAO,EAAE;MAC/BuB,cAAc,GAAG,QAAQ;MACzBC,UAAU,GAAG,aAAa;IAC9B;IAEA,oBACItC,OAAA,CAACH,MAAM;MACH0C,KAAK,EAAC,cAAc;MACpBC,IAAI,EAAEF,UAAW;MACjBG,QAAQ,EAAEJ,cAAe;MACzBK,OAAO,EAAEpC,OAAQ;MACjBqC,OAAO,EAAE,IAAI,CAACvC,WAAY;MAC1BwC,IAAI,EAAC,OAAO;MACZC,OAAO,wBAAA7B,MAAA,CAAwBlB,OAAO,CAAG;MACzCgD,cAAc,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAE;MACpCrB,KAAK,EAAE;QACHqB,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE;MACd;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;AACJ;AAEA,eAAetD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}