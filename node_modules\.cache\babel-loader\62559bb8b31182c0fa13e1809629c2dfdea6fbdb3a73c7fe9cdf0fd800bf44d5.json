{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiAnagrafica.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiAnagrafica = props => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState('');\n  const [results2, setResults2] = useState('');\n  const [results3, setResults3] = useState('');\n  const [results4, setResults4] = useState('');\n  const [results5, setResults5] = useState('');\n  const [results6, setResults6] = useState('');\n  const [results7, setResults7] = useState('');\n  const [results8, setResults8] = useState('');\n  const [results9, setResults9] = useState('');\n  const [results10, setResults10] = useState(null);\n  const [modPag, setModPag] = useState(null);\n  const [isLookingUp, setIsLookingUp] = useState(false);\n  const [lookupStatus, setLookupStatus] = useState(null); // 'success', 'error', 'not_found'\n  const toast = useRef(null);\n  //Setto le variabili di stato con il valore di props mediante useEffect\n  useEffect(() => {\n    async function renderString() {\n      if (props.result !== undefined) {\n        setResults(props.result.firstName);\n        setResults2(props.result.idRetailer.idRegistry.email);\n        setResults3(props.result.idRetailer.idRegistry.tel);\n        setResults4(props.result.idRetailer.idRegistry.pIva);\n        setResults5(props.result.idRetailer.idRegistry.address);\n        setResults6(props.result.idRetailer.idRegistry.city);\n        setResults7(props.result.idRetailer.idRegistry.cap);\n        setResults8(props.result.idRetailer.idRegistry.lastName);\n        setResults9(props.result.idRetailer.idRegistry.tel);\n        setResults10(props.result.idRetailer.idRegistry.paymentMetod);\n      }\n      //Chiamata axios per la visualizzazione dei registry\n      await APIRequest('GET', 'paymentmethods/').then(res => {\n        var pm = [];\n        res.data.forEach(element => {\n          var x = {\n            name: element.description,\n            code: element.description\n          };\n          pm.push(x);\n        });\n        setModPag(pm);\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    renderString();\n  }, [props.result]);\n  // Funzione di validazione\n  const validateForm = () => {\n    const errors = [];\n\n    // Validazione campi obbligatori\n    if (!results || results.trim() === '') {\n      errors.push('📝 Nome è obbligatorio');\n    }\n    if (!results8 || results8.trim() === '') {\n      errors.push('📝 Cognome è obbligatorio');\n    }\n    if (!results4 || results4.trim() === '') {\n      errors.push('📝 Partita IVA è obbligatoria');\n    }\n\n    // Validazione formato P.IVA (11 cifre)\n    if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n    }\n\n    // Validazione email obbligatoria\n    if (!results2 || results2.trim() === '') {\n      errors.push('📧 Email è obbligatoria');\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n      errors.push('📧 Indirizzo email non valido');\n    }\n\n    // Validazione contatti: almeno uno tra telefono e cellulare\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (!hasTelefono && !hasCellulare) {\n      errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n    }\n\n    // Validazione formato telefono (se presente)\n    if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n      errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n    }\n\n    // Validazione formato cellulare (se presente)\n    if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n      errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n    }\n    return errors;\n  };\n\n  // Funzione per il lookup automatico tramite P.IVA\n  const lookupPIva = async () => {\n    if (!results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ P.IVA non valida',\n        detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',\n        life: 4000\n      });\n      return;\n    }\n    setIsLookingUp(true);\n    try {\n      // Chiamata al nuovo endpoint per lookup P.IVA\n      const vatNumber = results4.replace(/\\s/g, '');\n      const response = await APIRequest('GET', \"company-lookup?vat=IT\".concat(vatNumber));\n      if (response.data && response.data.company) {\n        const data = response.data.company;\n\n        // Popola automaticamente i campi con i dati trovati dal servizio VIES\n        if (data.name) setResults(data.name);\n        if (data.address) {\n          // Estrae città e indirizzo se forniti insieme\n          const addressParts = data.address.split(',');\n          if (addressParts.length > 1) {\n            setResults5(addressParts[0].trim()); // Indirizzo\n            setResults6(addressParts[addressParts.length - 1].trim()); // Città\n          } else {\n            setResults5(data.address);\n          }\n        }\n        if (data.city) setResults6(data.city);\n        if (data.postalCode) setResults7(data.postalCode);\n        setLookupStatus('success');\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Azienda trovata',\n          detail: \"Dati recuperati automaticamente per \".concat(data.name, \". Verificare e completare le informazioni mancanti.\"),\n          life: 5000\n        });\n      } else {\n        throw new Error('Nessun risultato trovato');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _errorData$error, _errorData$error2;\n      console.error('❌ Errore lookup P.IVA:', error);\n\n      // Previeni il refresh della pagina gestendo l'errore localmente\n      const errorStatus = (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status;\n      const errorData = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data;\n      const errorCode = errorData === null || errorData === void 0 ? void 0 : (_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.code;\n      const errorMessage = (errorData === null || errorData === void 0 ? void 0 : (_errorData$error2 = errorData.error) === null || _errorData$error2 === void 0 ? void 0 : _errorData$error2.message) || error.message;\n\n      // Gestione specifica per diversi tipi di errore\n      if (errorStatus === 404 || errorCode === 'VAT_NOT_FOUND') {\n        setLookupStatus('not_found');\n        toast.current.show({\n          severity: 'info',\n          summary: '🔍 Azienda non trovata',\n          detail: 'Nessuna azienda trovata con questa Partita IVA. Puoi continuare con la compilazione manuale dei campi.',\n          life: 6000\n        });\n      } else if (errorStatus === 503 || errorCode === 'SERVICE_UNAVAILABLE') {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '⏱️ Servizio temporaneamente non disponibile',\n          detail: 'Il servizio di ricerca P.IVA non è al momento raggiungibile. Puoi continuare con la compilazione manuale.',\n          life: 6000\n        });\n      } else if (errorStatus === 400 || errorCode === 'INVALID_VAT_FORMAT') {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'error',\n          summary: '❌ Formato P.IVA non valido',\n          detail: 'Il formato della Partita IVA inserita non è corretto. Verificare e riprovare.',\n          life: 5000\n        });\n      } else if (errorStatus === 500 || errorCode === 'LOOKUP_ERROR') {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '⚠️ Errore temporaneo',\n          detail: 'Si è verificato un errore temporaneo durante la ricerca. Puoi continuare con la compilazione manuale o riprovare più tardi.',\n          life: 6000\n        });\n      } else {\n        // Errore generico - non bloccare l'utente\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '⚠️ Ricerca non disponibile',\n          detail: 'La ricerca automatica non è al momento disponibile. Puoi continuare con la compilazione manuale dei campi.',\n          life: 5000\n        });\n      }\n\n      // Non propagare l'errore per evitare il refresh della pagina\n      // L'utente può continuare con la compilazione manuale\n    } finally {\n      setIsLookingUp(false);\n    }\n  };\n  const Invia = async () => {\n    // Validazione frontend\n    const validationErrors = validateForm();\n    if (validationErrors.length > 0) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ Dati mancanti o non validi',\n        detail: validationErrors.join('. '),\n        life: 5000\n      });\n      return;\n    }\n\n    // Verifica che tutti i campi obbligatori siano compilati\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {\n      var corpo = {\n        firstName: results,\n        lastName: results8,\n        email: results2,\n        telnum: results3,\n        cellnum: results9,\n        pIva: results4,\n        address: results5,\n        city: results6,\n        cap: results7,\n        paymentMetod: (results10 === null || results10 === void 0 ? void 0 : results10.name) || ''\n      };\n      //Chiamata axios per la creazione del registry\n      await APIRequest('POST', 'registry/', corpo).then(res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Successo',\n          detail: \"L'anagrafica è stata inserita con successo\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      }).catch(e => {\n        var _e$response, _e$response2, _e$response3, _e$response3$data;\n        console.error('❌ Errore creazione anagrafica:', e);\n\n        // Gestione specifica degli errori\n        const errorStatus = (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status;\n        const errorData = (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data;\n        const errorMessage = ((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : (_e$response3$data = _e$response3.data) === null || _e$response3$data === void 0 ? void 0 : _e$response3$data.message) || e.message;\n        let userMessage = '';\n        let summary = 'Errore';\n        if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') || errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n          // Violazione unique constraint (P.IVA duplicata)\n          summary = '⚠️ Partita IVA già presente';\n          userMessage = \"La Partita IVA \\\"\".concat(results4, \"\\\" \\xE8 gi\\xE0 registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.\");\n        } else if (errorStatus === 400) {\n          // Errori di validazione\n          summary = '📝 Dati non validi';\n          if (errorMessage.toLowerCase().includes('email')) {\n            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n          } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n          } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n            userMessage = 'Il numero di telefono inserito non è valido.';\n          } else {\n            userMessage = \"Dati inseriti non validi: \".concat(errorMessage);\n          }\n        } else if (errorStatus === 422) {\n          // Errori di business logic\n          summary = '🚫 Operazione non consentita';\n          userMessage = \"Impossibile completare l'operazione: \".concat(errorMessage);\n        } else if (errorStatus === 500 || errorStatus === 501) {\n          // Errori del server\n          summary = '🔧 Errore del server';\n          userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n        } else if (errorStatus === 503) {\n          // Servizio non disponibile\n          summary = '⏱️ Servizio temporaneamente non disponibile';\n          userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n        } else if (!e.response) {\n          // Errori di rete\n          summary = '🌐 Errore di connessione';\n          userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n        } else {\n          // Altri errori\n          summary = '❌ Errore imprevisto';\n          userMessage = \"Si \\xE8 verificato un errore imprevisto: \".concat(errorMessage);\n        }\n        toast.current.show({\n          severity: 'error',\n          summary: summary,\n          detail: userMessage,\n          life: 6000\n        });\n\n        // Log dettagliato per debugging\n        console.error('Dettagli errore:', {\n          status: errorStatus,\n          data: errorData,\n          message: errorMessage,\n          fullError: e\n        });\n      });\n    } else {\n      toast.current.show({\n        severity: 'warn',\n        summary: '📝 Campi obbligatori mancanti',\n        detail: \"Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\",\n        life: 6000\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-grid p-fluid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-card p-p-3\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #dee2e6',\n            borderRadius: '8px',\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            style: {\n              color: '#495057',\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search\",\n              style: {\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 29\n            }, this), \"Ricerca Automatica Dati Azienda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6c757d',\n              fontSize: '14px',\n              marginBottom: '15px'\n            },\n            children: \"Inserisci la Partita IVA per compilare automaticamente i dati azienda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-col-12 p-md-6\",\n            style: {\n              padding: '0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [Costanti.pIva, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'red'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-inputgroup\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"p-inputgroup-addon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-credit-card\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                type: \"text\",\n                value: results4,\n                onChange: e => {\n                  const newValue = e.target.value;\n                  setResults4(newValue);\n\n                  // Auto-lookup quando P.IVA è valida (11 cifre)\n                  if (/^\\d{11}$/.test(newValue.replace(/\\s/g, ''))) {\n                    // Debounce per evitare troppe chiamate\n                    setTimeout(() => {\n                      if (results4 === newValue) {\n                        // Verifica che il valore non sia cambiato\n                        lookupPIva();\n                      }\n                    }, 500);\n                  }\n                },\n                keyfilter: /^[\\d\\s]+$/,\n                placeholder: \"Inserisci partita IVA (11 cifre) - ricerca automatica\",\n                editable: \"true\",\n                maxLength: 11,\n                className: !results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"button\",\n                icon: isLookingUp ? \"pi pi-spin pi-spinner\" : \"pi pi-search\",\n                className: \"p-button-outlined p-button-secondary\",\n                onClick: lookupPIva,\n                disabled: isLookingUp || !results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')),\n                tooltip: \"Cerca automaticamente i dati aziendali\",\n                tooltipOptions: {\n                  position: 'top'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 29\n            }, this), results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-error\",\n              children: \"La Partita IVA deve contenere esattamente 11 cifre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-info-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 33\n              }, this), \" Inserisci una P.IVA valida (11 cifre) per la ricerca automatica dei dati aziendali\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"h5\", {\n          style: {\n            color: '#495057',\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-user\",\n            style: {\n              marginRight: '8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 25\n          }, this), \"Dati Obbligatori\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Nome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results,\n            onChange: e => setResults(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci nome (obbligatorio)\",\n            editable: \"true\",\n            className: !results || results.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cognome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 44\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results8,\n            onChange: e => setResults8(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci cognome (obbligatorio)\",\n            editable: \"true\",\n            className: !results8 || results8.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Email, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-envelope\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"email\",\n            value: results2,\n            onChange: e => setResults2(e.target.value),\n            placeholder: \"Inserisci email (obbligatoria)\",\n            editable: \"true\",\n            className: !results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 21\n        }, this), (!results2 || results2.trim() === '') && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Email \\xE8 obbligatoria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 25\n        }, this), results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato email non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Tel, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'orange'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 40\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results3,\n            onChange: e => setResults3(e.target.value),\n            keyfilter: /^[\\d\\s\\-\\(\\)\\+]+$/,\n            placeholder: \"Inserisci telefono fisso\",\n            editable: \"true\",\n            className: results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 21\n        }, this), results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato telefono non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cell, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'orange'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-mobile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results9,\n            onChange: e => setResults9(e.target.value),\n            keyfilter: /^[\\d\\s\\-\\(\\)\\+]+$/,\n            placeholder: \"Inserisci cellulare\",\n            editable: \"true\",\n            className: results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 21\n        }, this), results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato cellulare non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 17\n      }, this), (!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-info-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 29\n          }, this), \" Inserire almeno un numero di telefono (fisso o cellulare)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-directions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results5,\n            onChange: e => setResults5(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci indirizzo\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Città\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results6,\n            onChange: e => setResults6(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci citt\\xE0\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.CodPost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results7,\n            onChange: e => setResults7(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci C.A.P.\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Pagamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"w-100\",\n            value: results10,\n            options: modPag,\n            onChange: e => setResults10(e.target.value),\n            optionLabel: \"name\",\n            placeholder: \"Seleziona metodo di pagamento\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mb-2 mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        id: \"invia\",\n        className: \"p-button saveList justify-content-center float-right ionicon mx-0 w-50\",\n        onClick: Invia,\n        children: Costanti.salva\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 340,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiAnagrafica, \"LA1xm5HKPTnIEp0HfKVXhonXwbE=\");\n_c = AggiungiAnagrafica;\nexport default AggiungiAnagrafica;\nvar _c;\n$RefreshReg$(_c, \"AggiungiAnagrafica\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "InputText", "<PERSON><PERSON>", "APIRequest", "Toast", "<PERSON><PERSON>", "Dropdown", "jsxDEV", "_jsxDEV", "AggiungiAnagrafica", "props", "_s", "results", "setResults", "results2", "setResults2", "results3", "setResults3", "results4", "setResults4", "results5", "setResults5", "results6", "setResults6", "results7", "setResults7", "results8", "setResults8", "results9", "setResults9", "results10", "setResults10", "modPag", "setModPag", "isLookingUp", "setIsLookingUp", "lookupStatus", "setLookupStatus", "toast", "renderString", "result", "undefined", "firstName", "idRetailer", "idRegistry", "email", "tel", "pIva", "address", "city", "cap", "lastName", "paymentMetod", "then", "res", "pm", "data", "for<PERSON>ach", "element", "x", "name", "description", "code", "push", "catch", "e", "console", "log", "validateForm", "errors", "trim", "test", "replace", "hasTelefono", "has<PERSON><PERSON><PERSON><PERSON>", "lookupPIva", "current", "show", "severity", "summary", "detail", "life", "vatNumber", "response", "concat", "company", "addressParts", "split", "length", "postalCode", "Error", "error", "_error$response", "_error$response2", "_errorData$error", "_errorData$error2", "errorStatus", "status", "errorData", "errorCode", "errorMessage", "message", "Invia", "validationErrors", "join", "corpo", "telnum", "cellnum", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "_e$response3", "_e$response3$data", "userMessage", "toLowerCase", "includes", "fullError", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "border", "borderRadius", "marginBottom", "color", "marginRight", "fontSize", "padding", "type", "value", "onChange", "newValue", "target", "keyfilter", "placeholder", "editable", "max<PERSON><PERSON><PERSON>", "icon", "onClick", "disabled", "tooltip", "tooltipOptions", "position", "Nome", "Cognome", "Email", "Tel", "Cell", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Pagamento", "options", "optionLabel", "filter", "filterBy", "id", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiAnagrafica.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { <PERSON>nti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\n\nconst AggiungiAnagrafica = (props) => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState('');\n    const [results2, setResults2] = useState('');\n    const [results3, setResults3] = useState('');\n    const [results4, setResults4] = useState('');\n    const [results5, setResults5] = useState('');\n    const [results6, setResults6] = useState('');\n    const [results7, setResults7] = useState('');\n    const [results8, setResults8] = useState('');\n    const [results9, setResults9] = useState('');\n    const [results10, setResults10] = useState(null);\n    const [modPag, setModPag] = useState(null);\n    const [isLookingUp, setIsLookingUp] = useState(false);\n    const [lookupStatus, setLookupStatus] = useState(null); // 'success', 'error', 'not_found'\n    const toast = useRef(null);\n    //Setto le variabili di stato con il valore di props mediante useEffect\n    useEffect(() => {\n        async function renderString() {\n            if (props.result !== undefined) {\n                setResults(props.result.firstName)\n                setResults2(props.result.idRetailer.idRegistry.email)\n                setResults3(props.result.idRetailer.idRegistry.tel)\n                setResults4(props.result.idRetailer.idRegistry.pIva)\n                setResults5(props.result.idRetailer.idRegistry.address)\n                setResults6(props.result.idRetailer.idRegistry.city)\n                setResults7(props.result.idRetailer.idRegistry.cap)\n                setResults8(props.result.idRetailer.idRegistry.lastName)\n                setResults9(props.result.idRetailer.idRegistry.tel)\n                setResults10(props.result.idRetailer.idRegistry.paymentMetod)\n            }\n            //Chiamata axios per la visualizzazione dei registry\n            await APIRequest('GET', 'paymentmethods/')\n                .then(res => {\n                    var pm = []\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.description,\n                            code: element.description\n                        }\n                        pm.push(x)\n                    });\n                    setModPag(pm)\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        renderString();\n    }, [props.result]);\n    // Funzione di validazione\n    const validateForm = () => {\n        const errors = [];\n\n        // Validazione campi obbligatori\n        if (!results || results.trim() === '') {\n            errors.push('📝 Nome è obbligatorio');\n        }\n        if (!results8 || results8.trim() === '') {\n            errors.push('📝 Cognome è obbligatorio');\n        }\n        if (!results4 || results4.trim() === '') {\n            errors.push('📝 Partita IVA è obbligatoria');\n        }\n\n        // Validazione formato P.IVA (11 cifre)\n        if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n        }\n\n        // Validazione email obbligatoria\n        if (!results2 || results2.trim() === '') {\n            errors.push('📧 Email è obbligatoria');\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n            errors.push('📧 Indirizzo email non valido');\n        }\n\n        // Validazione contatti: almeno uno tra telefono e cellulare\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (!hasTelefono && !hasCellulare) {\n            errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n        }\n\n        // Validazione formato telefono (se presente)\n        if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n            errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n        }\n\n        // Validazione formato cellulare (se presente)\n        if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n            errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n        }\n\n        return errors;\n    };\n\n    // Funzione per il lookup automatico tramite P.IVA\n    const lookupPIva = async () => {\n        if (!results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ P.IVA non valida',\n                detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',\n                life: 4000\n            });\n            return;\n        }\n\n        setIsLookingUp(true);\n\n        try {\n            // Chiamata al nuovo endpoint per lookup P.IVA\n            const vatNumber = results4.replace(/\\s/g, '');\n            const response = await APIRequest('GET', `company-lookup?vat=IT${vatNumber}`);\n\n            if (response.data && response.data.company) {\n                const data = response.data.company;\n\n                // Popola automaticamente i campi con i dati trovati dal servizio VIES\n                if (data.name) setResults(data.name);\n                if (data.address) {\n                    // Estrae città e indirizzo se forniti insieme\n                    const addressParts = data.address.split(',');\n                    if (addressParts.length > 1) {\n                        setResults5(addressParts[0].trim()); // Indirizzo\n                        setResults6(addressParts[addressParts.length - 1].trim()); // Città\n                    } else {\n                        setResults5(data.address);\n                    }\n                }\n                if (data.city) setResults6(data.city);\n                if (data.postalCode) setResults7(data.postalCode);\n\n                setLookupStatus('success');\n                toast.current.show({\n                    severity: 'success',\n                    summary: '✅ Azienda trovata',\n                    detail: `Dati recuperati automaticamente per ${data.name}. Verificare e completare le informazioni mancanti.`,\n                    life: 5000\n                });\n            } else {\n                throw new Error('Nessun risultato trovato');\n            }\n        } catch (error) {\n            console.error('❌ Errore lookup P.IVA:', error);\n\n            // Previeni il refresh della pagina gestendo l'errore localmente\n            const errorStatus = error.response?.status;\n            const errorData = error.response?.data;\n            const errorCode = errorData?.error?.code;\n            const errorMessage = errorData?.error?.message || error.message;\n\n            // Gestione specifica per diversi tipi di errore\n            if (errorStatus === 404 || errorCode === 'VAT_NOT_FOUND') {\n                setLookupStatus('not_found');\n                toast.current.show({\n                    severity: 'info',\n                    summary: '🔍 Azienda non trovata',\n                    detail: 'Nessuna azienda trovata con questa Partita IVA. Puoi continuare con la compilazione manuale dei campi.',\n                    life: 6000\n                });\n            } else if (errorStatus === 503 || errorCode === 'SERVICE_UNAVAILABLE') {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⏱️ Servizio temporaneamente non disponibile',\n                    detail: 'Il servizio di ricerca P.IVA non è al momento raggiungibile. Puoi continuare con la compilazione manuale.',\n                    life: 6000\n                });\n            } else if (errorStatus === 400 || errorCode === 'INVALID_VAT_FORMAT') {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'error',\n                    summary: '❌ Formato P.IVA non valido',\n                    detail: 'Il formato della Partita IVA inserita non è corretto. Verificare e riprovare.',\n                    life: 5000\n                });\n            } else if (errorStatus === 500 || errorCode === 'LOOKUP_ERROR') {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⚠️ Errore temporaneo',\n                    detail: 'Si è verificato un errore temporaneo durante la ricerca. Puoi continuare con la compilazione manuale o riprovare più tardi.',\n                    life: 6000\n                });\n            } else {\n                // Errore generico - non bloccare l'utente\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⚠️ Ricerca non disponibile',\n                    detail: 'La ricerca automatica non è al momento disponibile. Puoi continuare con la compilazione manuale dei campi.',\n                    life: 5000\n                });\n            }\n\n            // Non propagare l'errore per evitare il refresh della pagina\n            // L'utente può continuare con la compilazione manuale\n        } finally {\n            setIsLookingUp(false);\n        }\n    };\n\n    const Invia = async () => {\n        // Validazione frontend\n        const validationErrors = validateForm();\n        if (validationErrors.length > 0) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ Dati mancanti o non validi',\n                detail: validationErrors.join('. '),\n                life: 5000\n            });\n            return;\n        }\n\n        // Verifica che tutti i campi obbligatori siano compilati\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {\n            var corpo = {\n                firstName: results,\n                lastName: results8,\n                email: results2,\n                telnum: results3,\n                cellnum: results9,\n                pIva: results4,\n                address: results5,\n                city: results6,\n                cap: results7,\n                paymentMetod: results10?.name || ''\n            }\n            //Chiamata axios per la creazione del registry\n            await APIRequest('POST', 'registry/', corpo)\n                .then(res => {\n                    console.log(res.data);\n                    toast.current.show({\n                        severity: 'success',\n                        summary: '✅ Successo',\n                        detail: \"L'anagrafica è stata inserita con successo\",\n                        life: 3000\n                    });\n                    setTimeout(() => {\n                        window.location.reload()\n                    }, 3000)\n                }).catch((e) => {\n                    console.error('❌ Errore creazione anagrafica:', e);\n\n                    // Gestione specifica degli errori\n                    const errorStatus = e.response?.status;\n                    const errorData = e.response?.data;\n                    const errorMessage = e.response?.data?.message || e.message;\n\n                    let userMessage = '';\n                    let summary = 'Errore';\n\n                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||\n                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n                        // Violazione unique constraint (P.IVA duplicata)\n                        summary = '⚠️ Partita IVA già presente';\n                        userMessage = `La Partita IVA \"${results4}\" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;\n                    } else if (errorStatus === 400) {\n                        // Errori di validazione\n                        summary = '📝 Dati non validi';\n                        if (errorMessage.toLowerCase().includes('email')) {\n                            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n                            userMessage = 'Il numero di telefono inserito non è valido.';\n                        } else {\n                            userMessage = `Dati inseriti non validi: ${errorMessage}`;\n                        }\n                    } else if (errorStatus === 422) {\n                        // Errori di business logic\n                        summary = '🚫 Operazione non consentita';\n                        userMessage = `Impossibile completare l'operazione: ${errorMessage}`;\n                    } else if (errorStatus === 500 || errorStatus === 501) {\n                        // Errori del server\n                        summary = '🔧 Errore del server';\n                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n                    } else if (errorStatus === 503) {\n                        // Servizio non disponibile\n                        summary = '⏱️ Servizio temporaneamente non disponibile';\n                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n                    } else if (!e.response) {\n                        // Errori di rete\n                        summary = '🌐 Errore di connessione';\n                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n                    } else {\n                        // Altri errori\n                        summary = '❌ Errore imprevisto';\n                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;\n                    }\n\n                    toast.current.show({\n                        severity: 'error',\n                        summary: summary,\n                        detail: userMessage,\n                        life: 6000\n                    });\n\n                    // Log dettagliato per debugging\n                    console.error('Dettagli errore:', {\n                        status: errorStatus,\n                        data: errorData,\n                        message: errorMessage,\n                        fullError: e\n                    });\n                })\n        } else {\n            toast.current.show({\n                severity: 'warn',\n                summary: '📝 Campi obbligatori mancanti',\n                detail: \"Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\",\n                life: 6000\n            });\n        }\n    };\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div className=\"p-grid p-fluid\">\n                {/* Sezione Ricerca Automatica P.IVA */}\n                <div className=\"p-col-12\">\n                    <div className=\"p-card p-p-3\" style={{backgroundColor: '#f8f9fa', border: '1px solid #dee2e6', borderRadius: '8px', marginBottom: '20px'}}>\n                        <h5 style={{color: '#495057', marginBottom: '10px'}}>\n                            <i className=\"pi pi-search\" style={{marginRight: '8px'}}></i>\n                            Ricerca Automatica Dati Azienda\n                        </h5>\n                        <p style={{color: '#6c757d', fontSize: '14px', marginBottom: '15px'}}>\n                            Inserisci la Partita IVA per compilare automaticamente i dati azienda\n                        </p>\n\n                        <div className=\"p-col-12 p-md-6\" style={{padding: '0'}}>\n                            <h6>{Costanti.pIva} <span style={{color: 'red'}}>*</span></h6>\n                            <div className=\"p-inputgroup\">\n                                <span className=\"p-inputgroup-addon\">\n                                    <i className=\"pi pi-credit-card\"></i>\n                                </span>\n                                <InputText\n                                    type='text'\n                                    value={results4}\n                                    onChange={(e) => {\n                                        const newValue = e.target.value;\n                                        setResults4(newValue);\n\n                                        // Auto-lookup quando P.IVA è valida (11 cifre)\n                                        if (/^\\d{11}$/.test(newValue.replace(/\\s/g, ''))) {\n                                            // Debounce per evitare troppe chiamate\n                                            setTimeout(() => {\n                                                if (results4 === newValue) { // Verifica che il valore non sia cambiato\n                                                    lookupPIva();\n                                                }\n                                            }, 500);\n                                        }\n                                    }}\n                                    keyfilter={/^[\\d\\s]+$/}\n                                    placeholder=\"Inserisci partita IVA (11 cifre) - ricerca automatica\"\n                                    editable='true'\n                                    maxLength={11}\n                                    className={!results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''}\n                                />\n                                <Button\n                                    type=\"button\"\n                                    icon={isLookingUp ? \"pi pi-spin pi-spinner\" : \"pi pi-search\"}\n                                    className=\"p-button-outlined p-button-secondary\"\n                                    onClick={lookupPIva}\n                                    disabled={isLookingUp || !results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))}\n                                    tooltip=\"Cerca automaticamente i dati aziendali\"\n                                    tooltipOptions={{position: 'top'}}\n                                />\n                            </div>\n                            {results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && (\n                                <small className=\"p-error\">La Partita IVA deve contenere esattamente 11 cifre</small>\n                            )}\n                            <small className=\"p-text-secondary\">\n                                <i className=\"pi pi-info-circle\"></i> Inserisci una P.IVA valida (11 cifre) per la ricerca automatica dei dati aziendali\n                            </small>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Sezione Dati Obbligatori */}\n                <div className=\"p-col-12\">\n                    <h5 style={{color: '#495057', marginBottom: '15px'}}>\n                        <i className=\"pi pi-user\" style={{marginRight: '8px'}}></i>\n                        Dati Obbligatori\n                    </h5>\n                </div>\n\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Nome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results}\n                            onChange={(e) => setResults(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci nome (obbligatorio)\"\n                            editable='true'\n                            className={!results || results.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cognome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results8}\n                            onChange={(e) => setResults8(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci cognome (obbligatorio)\"\n                            editable='true'\n                            className={!results8 || results8.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Email} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-envelope\"></i>\n                        </span>\n                        <InputText\n                            type=\"email\"\n                            value={results2}\n                            onChange={(e) => setResults2(e.target.value)}\n                            placeholder=\"Inserisci email (obbligatoria)\"\n                            editable='true'\n                            className={!results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {(!results2 || results2.trim() === '') && (\n                        <small className=\"p-error\">Email è obbligatoria</small>\n                    )}\n                    {results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && (\n                        <small className=\"p-error\">Formato email non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Tel} <span style={{color: 'orange'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-phone\"></i>\n                        </span>\n                        <InputText\n                            type=\"tel\"\n                            value={results3}\n                            onChange={(e) => setResults3(e.target.value)}\n                            keyfilter={/^[\\d\\s\\-\\(\\)\\+]+$/}\n                            placeholder=\"Inserisci telefono fisso\"\n                            editable='true'\n                            className={results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) && (\n                        <small className=\"p-error\">Formato telefono non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cell} <span style={{color: 'orange'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-mobile\"></i>\n                        </span>\n                        <InputText\n                            type=\"tel\"\n                            value={results9}\n                            onChange={(e) => setResults9(e.target.value)}\n                            keyfilter={/^[\\d\\s\\-\\(\\)\\+]+$/}\n                            placeholder=\"Inserisci cellulare\"\n                            editable='true'\n                            className={results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) && (\n                        <small className=\"p-error\">Formato cellulare non valido</small>\n                    )}\n                </div>\n\n                {/* Messaggio informativo per i contatti */}\n                {(!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && (\n                    <div className=\"p-col-12\">\n                        <small className=\"p-error\">\n                            <i className=\"pi pi-info-circle\"></i> Inserire almeno un numero di telefono (fisso o cellulare)\n                        </small>\n                    </div>\n                )}\n\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Indirizzo}</h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-directions\"></i>\n                        </span>\n                        <InputText type='text' value={results5} onChange={(e) => setResults5(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci indirizzo\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Città}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results6} onChange={(e) => setResults6(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci città\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.CodPost}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results7} onChange={(e) => setResults7(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci C.A.P.\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Pagamento}</h6>\n                    <div className=\"p-inputgroup\">\n                        <Dropdown className='w-100' value={results10} options={modPag} onChange={(e) => setResults10(e.target.value)} optionLabel=\"name\" placeholder=\"Seleziona metodo di pagamento\" filter filterBy='name' />\n                    </div>\n                </div>\n            </div>\n            <div className=\"d-flex justify-content-center mb-2 mt-4\">\n                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                <Button id=\"invia\" className=\"p-button saveList justify-content-center float-right ionicon mx-0 w-50\" onClick={Invia}>{Costanti.salva}</Button>\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiAnagrafica;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAMwC,KAAK,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAC,SAAS,CAAC,MAAM;IACZ,eAAeuC,YAAYA,CAAA,EAAG;MAC1B,IAAI7B,KAAK,CAAC8B,MAAM,KAAKC,SAAS,EAAE;QAC5B5B,UAAU,CAACH,KAAK,CAAC8B,MAAM,CAACE,SAAS,CAAC;QAClC3B,WAAW,CAACL,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACC,KAAK,CAAC;QACrD5B,WAAW,CAACP,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnD3B,WAAW,CAACT,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACG,IAAI,CAAC;QACpD1B,WAAW,CAACX,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACI,OAAO,CAAC;QACvDzB,WAAW,CAACb,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACK,IAAI,CAAC;QACpDxB,WAAW,CAACf,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACM,GAAG,CAAC;QACnDvB,WAAW,CAACjB,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACO,QAAQ,CAAC;QACxDtB,WAAW,CAACnB,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnDf,YAAY,CAACrB,KAAK,CAAC8B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACQ,YAAY,CAAC;MACjE;MACA;MACA,MAAMjD,UAAU,CAAC,KAAK,EAAE,iBAAiB,CAAC,CACrCkD,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,EAAE,GAAG,EAAE;QACXD,GAAG,CAACE,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAG;YACJC,IAAI,EAAEF,OAAO,CAACG,WAAW;YACzBC,IAAI,EAAEJ,OAAO,CAACG;UAClB,CAAC;UACDN,EAAE,CAACQ,IAAI,CAACJ,CAAC,CAAC;QACd,CAAC,CAAC;QACF1B,SAAS,CAACsB,EAAE,CAAC;MACjB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACA1B,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC7B,KAAK,CAAC8B,MAAM,CAAC,CAAC;EAClB;EACA,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI,CAACzD,OAAO,IAAIA,OAAO,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnCD,MAAM,CAACN,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,IAAI,CAACrC,QAAQ,IAAIA,QAAQ,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,2BAA2B,CAAC;IAC5C;IACA,IAAI,CAAC7C,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,IAAI7C,QAAQ,IAAI,CAAC,UAAU,CAACqD,IAAI,CAACrD,QAAQ,CAACsD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC3DH,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;;IAEA;IACA,IAAI,CAACjD,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAACQ,IAAI,CAACzD,QAAQ,CAAC,EAAE;MACrDuD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,MAAMU,WAAW,GAAGzD,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAG9C,QAAQ,IAAIA,QAAQ,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAI,CAACG,WAAW,IAAI,CAACC,YAAY,EAAE;MAC/BL,MAAM,CAACN,IAAI,CAAC,8DAA8D,CAAC;IAC/E;;IAEA;IACA,IAAI/C,QAAQ,IAAI,CAAC,uBAAuB,CAACuD,IAAI,CAACvD,QAAQ,CAAC,EAAE;MACrDqD,MAAM,CAACN,IAAI,CAAC,yDAAyD,CAAC;IAC1E;;IAEA;IACA,IAAInC,QAAQ,IAAI,CAAC,uBAAuB,CAAC2C,IAAI,CAAC3C,QAAQ,CAAC,EAAE;MACrDyC,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;IAEA,OAAOM,MAAM;EACjB,CAAC;;EAED;EACA,MAAMM,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACzD,QAAQ,IAAI,CAAC,UAAU,CAACqD,IAAI,CAACrD,QAAQ,CAACsD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC5DlC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,iFAAiF;QACzFC,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;IAEA9C,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACA;MACA,MAAM+C,SAAS,GAAGhE,QAAQ,CAACsD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC7C,MAAMW,QAAQ,GAAG,MAAMhF,UAAU,CAAC,KAAK,0BAAAiF,MAAA,CAA0BF,SAAS,CAAE,CAAC;MAE7E,IAAIC,QAAQ,CAAC3B,IAAI,IAAI2B,QAAQ,CAAC3B,IAAI,CAAC6B,OAAO,EAAE;QACxC,MAAM7B,IAAI,GAAG2B,QAAQ,CAAC3B,IAAI,CAAC6B,OAAO;;QAElC;QACA,IAAI7B,IAAI,CAACI,IAAI,EAAE/C,UAAU,CAAC2C,IAAI,CAACI,IAAI,CAAC;QACpC,IAAIJ,IAAI,CAACR,OAAO,EAAE;UACd;UACA,MAAMsC,YAAY,GAAG9B,IAAI,CAACR,OAAO,CAACuC,KAAK,CAAC,GAAG,CAAC;UAC5C,IAAID,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;YACzBnE,WAAW,CAACiE,YAAY,CAAC,CAAC,CAAC,CAAChB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC/C,WAAW,CAAC+D,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC,CAAClB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,MAAM;YACHjD,WAAW,CAACmC,IAAI,CAACR,OAAO,CAAC;UAC7B;QACJ;QACA,IAAIQ,IAAI,CAACP,IAAI,EAAE1B,WAAW,CAACiC,IAAI,CAACP,IAAI,CAAC;QACrC,IAAIO,IAAI,CAACiC,UAAU,EAAEhE,WAAW,CAAC+B,IAAI,CAACiC,UAAU,CAAC;QAEjDpD,eAAe,CAAC,SAAS,CAAC;QAC1BC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,mBAAmB;UAC5BC,MAAM,yCAAAI,MAAA,CAAyC5B,IAAI,CAACI,IAAI,wDAAqD;UAC7GqB,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM;QACH,MAAM,IAAIS,KAAK,CAAC,0BAA0B,CAAC;MAC/C;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;MACZ7B,OAAO,CAACyB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,MAAMK,WAAW,IAAAJ,eAAA,GAAGD,KAAK,CAACR,QAAQ,cAAAS,eAAA,uBAAdA,eAAA,CAAgBK,MAAM;MAC1C,MAAMC,SAAS,IAAAL,gBAAA,GAAGF,KAAK,CAACR,QAAQ,cAAAU,gBAAA,uBAAdA,gBAAA,CAAgBrC,IAAI;MACtC,MAAM2C,SAAS,GAAGD,SAAS,aAATA,SAAS,wBAAAJ,gBAAA,GAATI,SAAS,CAAEP,KAAK,cAAAG,gBAAA,uBAAhBA,gBAAA,CAAkBhC,IAAI;MACxC,MAAMsC,YAAY,GAAG,CAAAF,SAAS,aAATA,SAAS,wBAAAH,iBAAA,GAATG,SAAS,CAAEP,KAAK,cAAAI,iBAAA,uBAAhBA,iBAAA,CAAkBM,OAAO,KAAIV,KAAK,CAACU,OAAO;;MAE/D;MACA,IAAIL,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,eAAe,EAAE;QACtD9D,eAAe,CAAC,WAAW,CAAC;QAC5BC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,wBAAwB;UACjCC,MAAM,EAAE,wGAAwG;UAChHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAIe,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,qBAAqB,EAAE;QACnE9D,eAAe,CAAC,OAAO,CAAC;QACxBC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,6CAA6C;UACtDC,MAAM,EAAE,2GAA2G;UACnHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAIe,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,oBAAoB,EAAE;QAClE9D,eAAe,CAAC,OAAO,CAAC;QACxBC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,4BAA4B;UACrCC,MAAM,EAAE,+EAA+E;UACvFC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAIe,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,cAAc,EAAE;QAC5D9D,eAAe,CAAC,OAAO,CAAC;QACxBC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,sBAAsB;UAC/BC,MAAM,EAAE,6HAA6H;UACrIC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM;QACH;QACA5C,eAAe,CAAC,OAAO,CAAC;QACxBC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,4BAA4B;UACrCC,MAAM,EAAE,4GAA4G;UACpHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;;MAEA;MACA;IACJ,CAAC,SAAS;MACN9C,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;EAED,MAAMmE,KAAK,GAAG,MAAAA,CAAA,KAAY;IACtB;IACA,MAAMC,gBAAgB,GAAGnC,YAAY,CAAC,CAAC;IACvC,IAAImC,gBAAgB,CAACf,MAAM,GAAG,CAAC,EAAE;MAC7BlD,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAEuB,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;QACnCvB,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;;IAEA;IACA,MAAMR,WAAW,GAAGzD,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAG9C,QAAQ,IAAIA,QAAQ,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAI1D,OAAO,IAAIc,QAAQ,IAAIR,QAAQ,IAAIJ,QAAQ,KAAK2D,WAAW,IAAIC,YAAY,CAAC,EAAE;MAC9E,IAAI+B,KAAK,GAAG;QACR/D,SAAS,EAAE9B,OAAO;QAClBuC,QAAQ,EAAEzB,QAAQ;QAClBmB,KAAK,EAAE/B,QAAQ;QACf4F,MAAM,EAAE1F,QAAQ;QAChB2F,OAAO,EAAE/E,QAAQ;QACjBmB,IAAI,EAAE7B,QAAQ;QACd8B,OAAO,EAAE5B,QAAQ;QACjB6B,IAAI,EAAE3B,QAAQ;QACd4B,GAAG,EAAE1B,QAAQ;QACb4B,YAAY,EAAE,CAAAtB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8B,IAAI,KAAI;MACrC,CAAC;MACD;MACA,MAAMzD,UAAU,CAAC,MAAM,EAAE,WAAW,EAAEsG,KAAK,CAAC,CACvCpD,IAAI,CAACC,GAAG,IAAI;QACTY,OAAO,CAACC,GAAG,CAACb,GAAG,CAACE,IAAI,CAAC;QACrBlB,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,4CAA4C;UACpDC,IAAI,EAAE;QACV,CAAC,CAAC;QACF2B,UAAU,CAAC,MAAM;UACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAAC/C,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAA+C,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA;QACZjD,OAAO,CAACyB,KAAK,CAAC,gCAAgC,EAAE1B,CAAC,CAAC;;QAElD;QACA,MAAM+B,WAAW,IAAAgB,WAAA,GAAG/C,CAAC,CAACkB,QAAQ,cAAA6B,WAAA,uBAAVA,WAAA,CAAYf,MAAM;QACtC,MAAMC,SAAS,IAAAe,YAAA,GAAGhD,CAAC,CAACkB,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAYzD,IAAI;QAClC,MAAM4C,YAAY,GAAG,EAAAc,YAAA,GAAAjD,CAAC,CAACkB,QAAQ,cAAA+B,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAY1D,IAAI,cAAA2D,iBAAA,uBAAhBA,iBAAA,CAAkBd,OAAO,KAAIpC,CAAC,CAACoC,OAAO;QAE3D,IAAIe,WAAW,GAAG,EAAE;QACpB,IAAIrC,OAAO,GAAG,QAAQ;QAEtB,IAAIiB,WAAW,KAAK,GAAG,IAAII,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACpElB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAIlB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;UACvG;UACAvC,OAAO,GAAG,6BAA6B;UACvCqC,WAAW,uBAAAhC,MAAA,CAAsBlE,QAAQ,sGAA4F;QACzI,CAAC,MAAM,IAAI8E,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAjB,OAAO,GAAG,oBAAoB;UAC9B,IAAIqB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC9CF,WAAW,GAAG,kEAAkE;UACpF,CAAC,MAAM,IAAIhB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIlB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC1GF,WAAW,GAAG,yEAAyE;UAC3F,CAAC,MAAM,IAAIhB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIlB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACtGF,WAAW,GAAG,8CAA8C;UAChE,CAAC,MAAM;YACHA,WAAW,gCAAAhC,MAAA,CAAgCgB,YAAY,CAAE;UAC7D;QACJ,CAAC,MAAM,IAAIJ,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAjB,OAAO,GAAG,8BAA8B;UACxCqC,WAAW,2CAAAhC,MAAA,CAA2CgB,YAAY,CAAE;QACxE,CAAC,MAAM,IAAIJ,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;UACnD;UACAjB,OAAO,GAAG,sBAAsB;UAChCqC,WAAW,GAAG,gHAAgH;QAClI,CAAC,MAAM,IAAIpB,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAjB,OAAO,GAAG,6CAA6C;UACvDqC,WAAW,GAAG,8EAA8E;QAChG,CAAC,MAAM,IAAI,CAACnD,CAAC,CAACkB,QAAQ,EAAE;UACpB;UACAJ,OAAO,GAAG,0BAA0B;UACpCqC,WAAW,GAAG,oFAAoF;QACtG,CAAC,MAAM;UACH;UACArC,OAAO,GAAG,qBAAqB;UAC/BqC,WAAW,+CAAAhC,MAAA,CAA4CgB,YAAY,CAAE;QACzE;QAEA9D,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAEoC,WAAW;UACnBnC,IAAI,EAAE;QACV,CAAC,CAAC;;QAEF;QACAf,OAAO,CAACyB,KAAK,CAAC,kBAAkB,EAAE;UAC9BM,MAAM,EAAED,WAAW;UACnBxC,IAAI,EAAE0C,SAAS;UACfG,OAAO,EAAED,YAAY;UACrBmB,SAAS,EAAEtD;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH3B,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAE,uGAAuG;QAC/GC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ,CAAC;EACD,oBACIzE,OAAA;IAAKgH,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBjH,OAAA,CAACJ,KAAK;MAACsH,GAAG,EAAEpF;IAAM;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBtH,OAAA;MAAKgH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE3BjH,OAAA;QAAKgH,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBjH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAACO,KAAK,EAAE;YAACC,eAAe,EAAE,SAAS;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,YAAY,EAAE,KAAK;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAV,QAAA,gBACtIjH,OAAA;YAAIuH,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAM,CAAE;YAAAV,QAAA,gBAChDjH,OAAA;cAAGgH,SAAS,EAAC,cAAc;cAACO,KAAK,EAAE;gBAACM,WAAW,EAAE;cAAK;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mCAEjE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtH,OAAA;YAAGuH,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEE,QAAQ,EAAE,MAAM;cAAEH,YAAY,EAAE;YAAM,CAAE;YAAAV,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJtH,OAAA;YAAKgH,SAAS,EAAC,iBAAiB;YAACO,KAAK,EAAE;cAACQ,OAAO,EAAE;YAAG,CAAE;YAAAd,QAAA,gBACnDjH,OAAA;cAAAiH,QAAA,GAAKvH,QAAQ,CAAC6C,IAAI,EAAC,GAAC,eAAAvC,OAAA;gBAAMuH,KAAK,EAAE;kBAACK,KAAK,EAAE;gBAAK,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DtH,OAAA;cAAKgH,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBjH,OAAA;gBAAMgH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eAChCjH,OAAA;kBAAGgH,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACPtH,OAAA,CAACP,SAAS;gBACNuI,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEvH,QAAS;gBAChBwH,QAAQ,EAAGzE,CAAC,IAAK;kBACb,MAAM0E,QAAQ,GAAG1E,CAAC,CAAC2E,MAAM,CAACH,KAAK;kBAC/BtH,WAAW,CAACwH,QAAQ,CAAC;;kBAErB;kBACA,IAAI,UAAU,CAACpE,IAAI,CAACoE,QAAQ,CAACnE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;oBAC9C;oBACAoC,UAAU,CAAC,MAAM;sBACb,IAAI1F,QAAQ,KAAKyH,QAAQ,EAAE;wBAAE;wBACzBhE,UAAU,CAAC,CAAC;sBAChB;oBACJ,CAAC,EAAE,GAAG,CAAC;kBACX;gBACJ,CAAE;gBACFkE,SAAS,EAAE,WAAY;gBACvBC,WAAW,EAAC,uDAAuD;gBACnEC,QAAQ,EAAC,MAAM;gBACfC,SAAS,EAAE,EAAG;gBACdxB,SAAS,EAAE,CAACtG,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAACC,IAAI,CAACrD,QAAQ,CAACsD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,WAAW,GAAG;cAAG;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC,eACFtH,OAAA,CAACH,MAAM;gBACHmI,IAAI,EAAC,QAAQ;gBACbS,IAAI,EAAE/G,WAAW,GAAG,uBAAuB,GAAG,cAAe;gBAC7DsF,SAAS,EAAC,sCAAsC;gBAChD0B,OAAO,EAAEvE,UAAW;gBACpBwE,QAAQ,EAAEjH,WAAW,IAAI,CAAChB,QAAQ,IAAI,CAAC,UAAU,CAACqD,IAAI,CAACrD,QAAQ,CAACsD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE;gBACpF4E,OAAO,EAAC,wCAAwC;gBAChDC,cAAc,EAAE;kBAACC,QAAQ,EAAE;gBAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL5G,QAAQ,IAAI,CAAC,UAAU,CAACqD,IAAI,CAACrD,QAAQ,CAACsD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,iBACtDhE,OAAA;cAAOgH,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACvF,eACDtH,OAAA;cAAOgH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BjH,OAAA;gBAAGgH,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uFACzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNtH,OAAA;QAAKgH,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBjH,OAAA;UAAIuH,KAAK,EAAE;YAACK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAM,CAAE;UAAAV,QAAA,gBAChDjH,OAAA;YAAGgH,SAAS,EAAC,YAAY;YAACO,KAAK,EAAE;cAACM,WAAW,EAAE;YAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAE/D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,GAAKvH,QAAQ,CAACqJ,IAAI,EAAC,GAAC,eAAA/I,OAAA;YAAMuH,KAAK,EAAE;cAACK,KAAK,EAAE;YAAK,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjH,OAAA;YAAMgH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjH,OAAA;cAAGgH,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPtH,OAAA,CAACP,SAAS;YACNuI,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE7H,OAAQ;YACf8H,QAAQ,EAAGzE,CAAC,IAAKpD,UAAU,CAACoD,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAC5CI,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,+BAA+B;YAC3CC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE,CAAC5G,OAAO,IAAIA,OAAO,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,GAAKvH,QAAQ,CAACsJ,OAAO,EAAC,GAAC,eAAAhJ,OAAA;YAAMuH,KAAK,EAAE;cAACK,KAAK,EAAE;YAAK,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjH,OAAA;YAAMgH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjH,OAAA;cAAGgH,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPtH,OAAA,CAACP,SAAS;YACNuI,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE/G,QAAS;YAChBgH,QAAQ,EAAGzE,CAAC,IAAKtC,WAAW,CAACsC,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAC7CI,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,kCAAkC;YAC9CC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE,CAAC9F,QAAQ,IAAIA,QAAQ,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,GAAKvH,QAAQ,CAACuJ,KAAK,EAAC,GAAC,eAAAjJ,OAAA;YAAMuH,KAAK,EAAE;cAACK,KAAK,EAAE;YAAK,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjH,OAAA;YAAMgH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjH,OAAA;cAAGgH,SAAS,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACPtH,OAAA,CAACP,SAAS;YACNuI,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE3H,QAAS;YAChB4H,QAAQ,EAAGzE,CAAC,IAAKlD,WAAW,CAACkD,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAC7CK,WAAW,EAAC,gCAAgC;YAC5CC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE,CAAC1G,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAACzD,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL,CAAC,CAAChH,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,kBACjC9D,OAAA;UAAOgH,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzD,EACAhH,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAACzD,QAAQ,CAAC,iBAC/EN,OAAA;UAAOgH,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC7D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,GAAKvH,QAAQ,CAACwJ,GAAG,EAAC,GAAC,eAAAlJ,OAAA;YAAMuH,KAAK,EAAE;cAACK,KAAK,EAAE;YAAQ,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjH,OAAA;YAAMgH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjH,OAAA;cAAGgH,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACPtH,OAAA,CAACP,SAAS;YACNuI,IAAI,EAAC,KAAK;YACVC,KAAK,EAAEzH,QAAS;YAChB0H,QAAQ,EAAGzE,CAAC,IAAKhD,WAAW,CAACgD,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAC7CI,SAAS,EAAE,mBAAoB;YAC/BC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAExG,QAAQ,IAAI,CAAC,uBAAuB,CAACuD,IAAI,CAACvD,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL9G,QAAQ,IAAI,CAAC,uBAAuB,CAACuD,IAAI,CAACvD,QAAQ,CAAC,iBAChDR,OAAA;UAAOgH,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,GAAKvH,QAAQ,CAACyJ,IAAI,EAAC,GAAC,eAAAnJ,OAAA;YAAMuH,KAAK,EAAE;cAACK,KAAK,EAAE;YAAQ,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjH,OAAA;YAAMgH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjH,OAAA;cAAGgH,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACPtH,OAAA,CAACP,SAAS;YACNuI,IAAI,EAAC,KAAK;YACVC,KAAK,EAAE7G,QAAS;YAChB8G,QAAQ,EAAGzE,CAAC,IAAKpC,WAAW,CAACoC,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAC7CI,SAAS,EAAE,mBAAoB;YAC/BC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE5F,QAAQ,IAAI,CAAC,uBAAuB,CAAC2C,IAAI,CAAC3C,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLlG,QAAQ,IAAI,CAAC,uBAAuB,CAAC2C,IAAI,CAAC3C,QAAQ,CAAC,iBAChDpB,OAAA;UAAOgH,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA4B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACjE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGL,CAAC,CAAC9G,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC1C,QAAQ,IAAIA,QAAQ,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,iBAC3E9D,OAAA;QAAKgH,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBjH,OAAA;UAAOgH,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtBjH,OAAA;YAAGgH,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,8DACzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACR,eAEDtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,EAAKvH,QAAQ,CAAC0J;QAAS;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjH,OAAA;YAAMgH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjH,OAAA;cAAGgH,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACPtH,OAAA,CAACP,SAAS;YAACuI,IAAI,EAAC,MAAM;YAACC,KAAK,EAAErH,QAAS;YAACsH,QAAQ,EAAGzE,CAAC,IAAK5C,WAAW,CAAC4C,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAACI,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,qBAAqB;YAACC,QAAQ,EAAC;UAAM;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,EAAKvH,QAAQ,CAAC2J;QAAK;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzBtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjH,OAAA,CAACP,SAAS;YAACuI,IAAI,EAAC,MAAM;YAACC,KAAK,EAAEnH,QAAS;YAACoH,QAAQ,EAAGzE,CAAC,IAAK1C,WAAW,CAAC0C,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAACI,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,oBAAiB;YAACC,QAAQ,EAAC;UAAM;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,EAAKvH,QAAQ,CAAC4J;QAAO;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3BtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjH,OAAA,CAACP,SAAS;YAACuI,IAAI,EAAC,MAAM;YAACC,KAAK,EAAEjH,QAAS;YAACkH,QAAQ,EAAGzE,CAAC,IAAKxC,WAAW,CAACwC,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAACI,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,kBAAkB;YAACC,QAAQ,EAAC;UAAM;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtH,OAAA;QAAKgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjH,OAAA;UAAAiH,QAAA,EAAKvH,QAAQ,CAAC6J;QAAS;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BtH,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjH,OAAA,CAACF,QAAQ;YAACkH,SAAS,EAAC,OAAO;YAACiB,KAAK,EAAE3G,SAAU;YAACkI,OAAO,EAAEhI,MAAO;YAAC0G,QAAQ,EAAGzE,CAAC,IAAKlC,YAAY,CAACkC,CAAC,CAAC2E,MAAM,CAACH,KAAK,CAAE;YAACwB,WAAW,EAAC,MAAM;YAACnB,WAAW,EAAC,+BAA+B;YAACoB,MAAM;YAACC,QAAQ,EAAC;UAAM;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNtH,OAAA;MAAKgH,SAAS,EAAC,yCAAyC;MAAAC,QAAA,eAEpDjH,OAAA,CAACH,MAAM;QAAC+J,EAAE,EAAC,OAAO;QAAC5C,SAAS,EAAC,wEAAwE;QAAC0B,OAAO,EAAE5C,KAAM;QAAAmB,QAAA,EAAEvH,QAAQ,CAACmK;MAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9I,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAnH,EAAA,CArhBKF,kBAAkB;AAAA6J,EAAA,GAAlB7J,kBAAkB;AAuhBxB,eAAeA,kBAAkB;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}