{"ast": null, "code": "var raf = function raf(callback) {\n  return +setTimeout(callback, 16);\n};\nvar caf = function caf(num) {\n  return clearTimeout(num);\n};\nif (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {\n  raf = function raf(callback) {\n    return window.requestAnimationFrame(callback);\n  };\n  caf = function caf(handle) {\n    return window.cancelAnimationFrame(handle);\n  };\n}\nvar rafUUID = 0;\nvar rafIds = new Map();\nfunction cleanup(id) {\n  rafIds.delete(id);\n}\nexport default function wrapperRaf(callback) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  rafUUID += 1;\n  var id = rafUUID;\n  function callRef(leftTimes) {\n    if (leftTimes === 0) {\n      // Clean up\n      cleanup(id); // Trigger\n\n      callback();\n    } else {\n      // Next raf\n      var realId = raf(function () {\n        callRef(leftTimes - 1);\n      }); // Bind real raf id\n\n      rafIds.set(id, realId);\n    }\n  }\n  callRef(times);\n  return id;\n}\nwrapperRaf.cancel = function (id) {\n  var realId = rafIds.get(id);\n  cleanup(realId);\n  return caf(realId);\n};", "map": {"version": 3, "names": ["raf", "callback", "setTimeout", "caf", "num", "clearTimeout", "window", "requestAnimationFrame", "handle", "cancelAnimationFrame", "rafUUID", "rafIds", "Map", "cleanup", "id", "delete", "wrapperRaf", "times", "arguments", "length", "undefined", "callRef", "leftTimes", "realId", "set", "cancel", "get"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/raf.js"], "sourcesContent": ["var raf = function raf(callback) {\n  return +setTimeout(callback, 16);\n};\n\nvar caf = function caf(num) {\n  return clearTimeout(num);\n};\n\nif (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {\n  raf = function raf(callback) {\n    return window.requestAnimationFrame(callback);\n  };\n\n  caf = function caf(handle) {\n    return window.cancelAnimationFrame(handle);\n  };\n}\n\nvar rafUUID = 0;\nvar rafIds = new Map();\n\nfunction cleanup(id) {\n  rafIds.delete(id);\n}\n\nexport default function wrapperRaf(callback) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  rafUUID += 1;\n  var id = rafUUID;\n\n  function callRef(leftTimes) {\n    if (leftTimes === 0) {\n      // Clean up\n      cleanup(id); // Trigger\n\n      callback();\n    } else {\n      // Next raf\n      var realId = raf(function () {\n        callRef(leftTimes - 1);\n      }); // Bind real raf id\n\n      rafIds.set(id, realId);\n    }\n  }\n\n  callRef(times);\n  return id;\n}\n\nwrapperRaf.cancel = function (id) {\n  var realId = rafIds.get(id);\n  cleanup(realId);\n  return caf(realId);\n};"], "mappings": "AAAA,IAAIA,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;EAC/B,OAAO,CAACC,UAAU,CAACD,QAAQ,EAAE,EAAE,CAAC;AAClC,CAAC;AAED,IAAIE,GAAG,GAAG,SAASA,GAAGA,CAACC,GAAG,EAAE;EAC1B,OAAOC,YAAY,CAACD,GAAG,CAAC;AAC1B,CAAC;AAED,IAAI,OAAOE,MAAM,KAAK,WAAW,IAAI,uBAAuB,IAAIA,MAAM,EAAE;EACtEN,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;IAC3B,OAAOK,MAAM,CAACC,qBAAqB,CAACN,QAAQ,CAAC;EAC/C,CAAC;EAEDE,GAAG,GAAG,SAASA,GAAGA,CAACK,MAAM,EAAE;IACzB,OAAOF,MAAM,CAACG,oBAAoB,CAACD,MAAM,CAAC;EAC5C,CAAC;AACH;AAEA,IAAIE,OAAO,GAAG,CAAC;AACf,IAAIC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEtB,SAASC,OAAOA,CAACC,EAAE,EAAE;EACnBH,MAAM,CAACI,MAAM,CAACD,EAAE,CAAC;AACnB;AAEA,eAAe,SAASE,UAAUA,CAACf,QAAQ,EAAE;EAC3C,IAAIgB,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjFR,OAAO,IAAI,CAAC;EACZ,IAAII,EAAE,GAAGJ,OAAO;EAEhB,SAASW,OAAOA,CAACC,SAAS,EAAE;IAC1B,IAAIA,SAAS,KAAK,CAAC,EAAE;MACnB;MACAT,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC;;MAEbb,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA,IAAIsB,MAAM,GAAGvB,GAAG,CAAC,YAAY;QAC3BqB,OAAO,CAACC,SAAS,GAAG,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC;;MAEJX,MAAM,CAACa,GAAG,CAACV,EAAE,EAAES,MAAM,CAAC;IACxB;EACF;EAEAF,OAAO,CAACJ,KAAK,CAAC;EACd,OAAOH,EAAE;AACX;AAEAE,UAAU,CAACS,MAAM,GAAG,UAAUX,EAAE,EAAE;EAChC,IAAIS,MAAM,GAAGZ,MAAM,CAACe,GAAG,CAACZ,EAAE,CAAC;EAC3BD,OAAO,CAACU,MAAM,CAAC;EACf,OAAOpB,GAAG,CAACoB,MAAM,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}