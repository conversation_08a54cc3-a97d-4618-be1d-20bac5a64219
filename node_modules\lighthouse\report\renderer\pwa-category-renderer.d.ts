export class PwaCategoryRenderer extends CategoryRenderer {
    /**
     * Alters SVG id references so multiple instances of an SVG element can coexist
     * in a single page. If `svgRoot` has a `<defs>` block, gives all elements defined
     * in it unique ids, then updates id references (`<use xlink:href="...">`,
     * `fill="url(#...)"`) to the altered ids in all descendents of `svgRoot`.
     * @param {SVGElement} svgRoot
     */
    static _makeSvgReferencesUnique(svgRoot: SVGElement): void;
    /**
     * @param {LH.ReportResult.Category} category
     * @param {Object<string, LH.Result.ReportGroup>} [groupDefinitions]
     * @return {Element}
     */
    render(category: LH.ReportResult.Category, groupDefinitions?: {
        [x: string]: import("../../types/lhr/lhr.js").default.ReportGroup;
    } | undefined): Element;
    /**
     * @param {LH.ReportResult.Category} category
     * @param {Record<string, LH.Result.ReportGroup>} groupDefinitions
     * @return {DocumentFragment}
     */
    renderCategoryScore(category: LH.ReportResult.Category, groupDefinitions: Record<string, LH.Result.ReportGroup>): DocumentFragment;
    /**
     * Returns the group IDs found in auditRefs.
     * @param {Array<LH.ReportResult.AuditRef>} auditRefs
     * @return {!Set<string>}
     */
    _getGroupIds(auditRefs: Array<LH.ReportResult.AuditRef>): Set<string>;
    /**
     * Returns the group IDs whose audits are all considered passing.
     * @param {Array<LH.ReportResult.AuditRef>} auditRefs
     * @return {Set<string>}
     */
    _getPassingGroupIds(auditRefs: Array<LH.ReportResult.AuditRef>): Set<string>;
    /**
     * Returns a tooltip string summarizing group pass rates.
     * @param {Array<LH.ReportResult.AuditRef>} auditRefs
     * @param {Record<string, LH.Result.ReportGroup>} groupDefinitions
     * @return {string}
     */
    _getGaugeTooltip(auditRefs: Array<LH.ReportResult.AuditRef>, groupDefinitions: Record<string, LH.Result.ReportGroup>): string;
    /**
     * Render non-manual audits in groups, giving a badge to any group that has
     * all passing audits.
     * @param {Array<LH.ReportResult.AuditRef>} auditRefs
     * @param {Object<string, LH.Result.ReportGroup>} groupDefinitions
     * @return {Element}
     */
    _renderAudits(auditRefs: Array<LH.ReportResult.AuditRef>, groupDefinitions: {
        [x: string]: LH.Result.ReportGroup;
    }): Element;
}
import { CategoryRenderer } from "./category-renderer.js";
//# sourceMappingURL=pwa-category-renderer.d.ts.map