{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport FieldContext from './FieldContext';\nimport Field from './Field';\nimport { move as _move, getNamePath } from './utils/valueUtil';\nimport ListContext from './ListContext';\nvar List = function List(_ref) {\n  var name = _ref.name,\n    initialValue = _ref.initialValue,\n    children = _ref.children,\n    rules = _ref.rules,\n    validateTrigger = _ref.validateTrigger;\n  var context = React.useContext(FieldContext);\n  var keyRef = React.useRef({\n    keys: [],\n    id: 0\n  });\n  var keyManager = keyRef.current;\n  var prefixName = React.useMemo(function () {\n    var parentPrefixName = getNamePath(context.prefixName) || [];\n    return [].concat(_toConsumableArray(parentPrefixName), _toConsumableArray(getNamePath(name)));\n  }, [context.prefixName, name]);\n  var fieldContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, context), {}, {\n      prefixName: prefixName\n    });\n  }, [context, prefixName]); // List context\n\n  var listContext = React.useMemo(function () {\n    return {\n      getKey: function getKey(namePath) {\n        var len = prefixName.length;\n        var pathName = namePath[len];\n        return [keyManager.keys[pathName], namePath.slice(len + 1)];\n      }\n    };\n  }, [prefixName]); // User should not pass `children` as other type.\n\n  if (typeof children !== 'function') {\n    warning(false, 'Form.List only accepts function as children.');\n    return null;\n  }\n  var shouldUpdate = function shouldUpdate(prevValue, nextValue, _ref2) {\n    var source = _ref2.source;\n    if (source === 'internal') {\n      return false;\n    }\n    return prevValue !== nextValue;\n  };\n  return /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: listContext\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: fieldContext\n  }, /*#__PURE__*/React.createElement(Field, {\n    name: [],\n    shouldUpdate: shouldUpdate,\n    rules: rules,\n    validateTrigger: validateTrigger,\n    initialValue: initialValue,\n    isList: true\n  }, function (_ref3, meta) {\n    var _ref3$value = _ref3.value,\n      value = _ref3$value === void 0 ? [] : _ref3$value,\n      onChange = _ref3.onChange;\n    var getFieldValue = context.getFieldValue;\n    var getNewValue = function getNewValue() {\n      var values = getFieldValue(prefixName || []);\n      return values || [];\n    };\n    /**\n     * Always get latest value in case user update fields by `form` api.\n     */\n\n    var operations = {\n      add: function add(defaultValue, index) {\n        // Mapping keys\n        var newValue = getNewValue();\n        if (index >= 0 && index <= newValue.length) {\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys.slice(0, index)), [keyManager.id], _toConsumableArray(keyManager.keys.slice(index)));\n          onChange([].concat(_toConsumableArray(newValue.slice(0, index)), [defaultValue], _toConsumableArray(newValue.slice(index))));\n        } else {\n          if (process.env.NODE_ENV !== 'production' && (index < 0 || index > newValue.length)) {\n            warning(false, 'The second parameter of the add function should be a valid positive number.');\n          }\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys), [keyManager.id]);\n          onChange([].concat(_toConsumableArray(newValue), [defaultValue]));\n        }\n        keyManager.id += 1;\n      },\n      remove: function remove(index) {\n        var newValue = getNewValue();\n        var indexSet = new Set(Array.isArray(index) ? index : [index]);\n        if (indexSet.size <= 0) {\n          return;\n        }\n        keyManager.keys = keyManager.keys.filter(function (_, keysIndex) {\n          return !indexSet.has(keysIndex);\n        }); // Trigger store change\n\n        onChange(newValue.filter(function (_, valueIndex) {\n          return !indexSet.has(valueIndex);\n        }));\n      },\n      move: function move(from, to) {\n        if (from === to) {\n          return;\n        }\n        var newValue = getNewValue(); // Do not handle out of range\n\n        if (from < 0 || from >= newValue.length || to < 0 || to >= newValue.length) {\n          return;\n        }\n        keyManager.keys = _move(keyManager.keys, from, to); // Trigger store change\n\n        onChange(_move(newValue, from, to));\n      }\n    };\n    var listValue = value || [];\n    if (!Array.isArray(listValue)) {\n      listValue = [];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"Current value of '\".concat(prefixName.join(' > '), \"' is not an array type.\"));\n      }\n    }\n    return children(listValue.map(function (__, index) {\n      var key = keyManager.keys[index];\n      if (key === undefined) {\n        keyManager.keys[index] = keyManager.id;\n        key = keyManager.keys[index];\n        keyManager.id += 1;\n      }\n      return {\n        name: index,\n        key: key,\n        isListField: true\n      };\n    }), operations, meta);\n  })));\n};\nexport default List;", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "React", "warning", "FieldContext", "Field", "move", "_move", "getNamePath", "ListContext", "List", "_ref", "name", "initialValue", "children", "rules", "validate<PERSON><PERSON>ger", "context", "useContext", "keyRef", "useRef", "keys", "id", "keyManager", "current", "prefixName", "useMemo", "parentPrefixName", "concat", "fieldContext", "listContext", "<PERSON><PERSON><PERSON>", "namePath", "len", "length", "pathName", "slice", "shouldUpdate", "prevValue", "nextValue", "_ref2", "source", "createElement", "Provider", "value", "isList", "_ref3", "meta", "_ref3$value", "onChange", "getFieldValue", "getNewValue", "values", "operations", "add", "defaultValue", "index", "newValue", "process", "env", "NODE_ENV", "remove", "indexSet", "Set", "Array", "isArray", "size", "filter", "_", "keysIndex", "has", "valueIndex", "from", "to", "listValue", "join", "map", "__", "key", "undefined", "isListField"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/List.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport FieldContext from './FieldContext';\nimport Field from './Field';\nimport { move as _move, getNamePath } from './utils/valueUtil';\nimport ListContext from './ListContext';\n\nvar List = function List(_ref) {\n  var name = _ref.name,\n      initialValue = _ref.initialValue,\n      children = _ref.children,\n      rules = _ref.rules,\n      validateTrigger = _ref.validateTrigger;\n  var context = React.useContext(FieldContext);\n  var keyRef = React.useRef({\n    keys: [],\n    id: 0\n  });\n  var keyManager = keyRef.current;\n  var prefixName = React.useMemo(function () {\n    var parentPrefixName = getNamePath(context.prefixName) || [];\n    return [].concat(_toConsumableArray(parentPrefixName), _toConsumableArray(getNamePath(name)));\n  }, [context.prefixName, name]);\n  var fieldContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, context), {}, {\n      prefixName: prefixName\n    });\n  }, [context, prefixName]); // List context\n\n  var listContext = React.useMemo(function () {\n    return {\n      getKey: function getKey(namePath) {\n        var len = prefixName.length;\n        var pathName = namePath[len];\n        return [keyManager.keys[pathName], namePath.slice(len + 1)];\n      }\n    };\n  }, [prefixName]); // User should not pass `children` as other type.\n\n  if (typeof children !== 'function') {\n    warning(false, 'Form.List only accepts function as children.');\n    return null;\n  }\n\n  var shouldUpdate = function shouldUpdate(prevValue, nextValue, _ref2) {\n    var source = _ref2.source;\n\n    if (source === 'internal') {\n      return false;\n    }\n\n    return prevValue !== nextValue;\n  };\n\n  return /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: listContext\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: fieldContext\n  }, /*#__PURE__*/React.createElement(Field, {\n    name: [],\n    shouldUpdate: shouldUpdate,\n    rules: rules,\n    validateTrigger: validateTrigger,\n    initialValue: initialValue,\n    isList: true\n  }, function (_ref3, meta) {\n    var _ref3$value = _ref3.value,\n        value = _ref3$value === void 0 ? [] : _ref3$value,\n        onChange = _ref3.onChange;\n    var getFieldValue = context.getFieldValue;\n\n    var getNewValue = function getNewValue() {\n      var values = getFieldValue(prefixName || []);\n      return values || [];\n    };\n    /**\n     * Always get latest value in case user update fields by `form` api.\n     */\n\n\n    var operations = {\n      add: function add(defaultValue, index) {\n        // Mapping keys\n        var newValue = getNewValue();\n\n        if (index >= 0 && index <= newValue.length) {\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys.slice(0, index)), [keyManager.id], _toConsumableArray(keyManager.keys.slice(index)));\n          onChange([].concat(_toConsumableArray(newValue.slice(0, index)), [defaultValue], _toConsumableArray(newValue.slice(index))));\n        } else {\n          if (process.env.NODE_ENV !== 'production' && (index < 0 || index > newValue.length)) {\n            warning(false, 'The second parameter of the add function should be a valid positive number.');\n          }\n\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys), [keyManager.id]);\n          onChange([].concat(_toConsumableArray(newValue), [defaultValue]));\n        }\n\n        keyManager.id += 1;\n      },\n      remove: function remove(index) {\n        var newValue = getNewValue();\n        var indexSet = new Set(Array.isArray(index) ? index : [index]);\n\n        if (indexSet.size <= 0) {\n          return;\n        }\n\n        keyManager.keys = keyManager.keys.filter(function (_, keysIndex) {\n          return !indexSet.has(keysIndex);\n        }); // Trigger store change\n\n        onChange(newValue.filter(function (_, valueIndex) {\n          return !indexSet.has(valueIndex);\n        }));\n      },\n      move: function move(from, to) {\n        if (from === to) {\n          return;\n        }\n\n        var newValue = getNewValue(); // Do not handle out of range\n\n        if (from < 0 || from >= newValue.length || to < 0 || to >= newValue.length) {\n          return;\n        }\n\n        keyManager.keys = _move(keyManager.keys, from, to); // Trigger store change\n\n        onChange(_move(newValue, from, to));\n      }\n    };\n    var listValue = value || [];\n\n    if (!Array.isArray(listValue)) {\n      listValue = [];\n\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"Current value of '\".concat(prefixName.join(' > '), \"' is not an array type.\"));\n      }\n    }\n\n    return children(listValue.map(function (__, index) {\n      var key = keyManager.keys[index];\n\n      if (key === undefined) {\n        keyManager.keys[index] = keyManager.id;\n        key = keyManager.keys[index];\n        keyManager.id += 1;\n      }\n\n      return {\n        name: index,\n        key: key,\n        isListField: true\n      };\n    }), operations, meta);\n  })));\n};\n\nexport default List;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,IAAI,IAAIC,KAAK,EAAEC,WAAW,QAAQ,mBAAmB;AAC9D,OAAOC,WAAW,MAAM,eAAe;AAEvC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAE;EAC7B,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,YAAY,GAAGF,IAAI,CAACE,YAAY;IAChCC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,eAAe,GAAGL,IAAI,CAACK,eAAe;EAC1C,IAAIC,OAAO,GAAGf,KAAK,CAACgB,UAAU,CAACd,YAAY,CAAC;EAC5C,IAAIe,MAAM,GAAGjB,KAAK,CAACkB,MAAM,CAAC;IACxBC,IAAI,EAAE,EAAE;IACRC,EAAE,EAAE;EACN,CAAC,CAAC;EACF,IAAIC,UAAU,GAAGJ,MAAM,CAACK,OAAO;EAC/B,IAAIC,UAAU,GAAGvB,KAAK,CAACwB,OAAO,CAAC,YAAY;IACzC,IAAIC,gBAAgB,GAAGnB,WAAW,CAACS,OAAO,CAACQ,UAAU,CAAC,IAAI,EAAE;IAC5D,OAAO,EAAE,CAACG,MAAM,CAAC3B,kBAAkB,CAAC0B,gBAAgB,CAAC,EAAE1B,kBAAkB,CAACO,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC;EAC/F,CAAC,EAAE,CAACK,OAAO,CAACQ,UAAU,EAAEb,IAAI,CAAC,CAAC;EAC9B,IAAIiB,YAAY,GAAG3B,KAAK,CAACwB,OAAO,CAAC,YAAY;IAC3C,OAAO1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACnDQ,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,OAAO,EAAEQ,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE3B,IAAIK,WAAW,GAAG5B,KAAK,CAACwB,OAAO,CAAC,YAAY;IAC1C,OAAO;MACLK,MAAM,EAAE,SAASA,MAAMA,CAACC,QAAQ,EAAE;QAChC,IAAIC,GAAG,GAAGR,UAAU,CAACS,MAAM;QAC3B,IAAIC,QAAQ,GAAGH,QAAQ,CAACC,GAAG,CAAC;QAC5B,OAAO,CAACV,UAAU,CAACF,IAAI,CAACc,QAAQ,CAAC,EAAEH,QAAQ,CAACI,KAAK,CAACH,GAAG,GAAG,CAAC,CAAC,CAAC;MAC7D;IACF,CAAC;EACH,CAAC,EAAE,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB,IAAI,OAAOX,QAAQ,KAAK,UAAU,EAAE;IAClCX,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC;IAC9D,OAAO,IAAI;EACb;EAEA,IAAIkC,YAAY,GAAG,SAASA,YAAYA,CAACC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAE;IACpE,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAEzB,IAAIA,MAAM,KAAK,UAAU,EAAE;MACzB,OAAO,KAAK;IACd;IAEA,OAAOH,SAAS,KAAKC,SAAS;EAChC,CAAC;EAED,OAAO,aAAarC,KAAK,CAACwC,aAAa,CAACjC,WAAW,CAACkC,QAAQ,EAAE;IAC5DC,KAAK,EAAEd;EACT,CAAC,EAAE,aAAa5B,KAAK,CAACwC,aAAa,CAACtC,YAAY,CAACuC,QAAQ,EAAE;IACzDC,KAAK,EAAEf;EACT,CAAC,EAAE,aAAa3B,KAAK,CAACwC,aAAa,CAACrC,KAAK,EAAE;IACzCO,IAAI,EAAE,EAAE;IACRyB,YAAY,EAAEA,YAAY;IAC1BtB,KAAK,EAAEA,KAAK;IACZC,eAAe,EAAEA,eAAe;IAChCH,YAAY,EAAEA,YAAY;IAC1BgC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,KAAK,EAAEC,IAAI,EAAE;IACxB,IAAIC,WAAW,GAAGF,KAAK,CAACF,KAAK;MACzBA,KAAK,GAAGI,WAAW,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,WAAW;MACjDC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IAC7B,IAAIC,aAAa,GAAGjC,OAAO,CAACiC,aAAa;IAEzC,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;MACvC,IAAIC,MAAM,GAAGF,aAAa,CAACzB,UAAU,IAAI,EAAE,CAAC;MAC5C,OAAO2B,MAAM,IAAI,EAAE;IACrB,CAAC;IACD;AACJ;AACA;;IAGI,IAAIC,UAAU,GAAG;MACfC,GAAG,EAAE,SAASA,GAAGA,CAACC,YAAY,EAAEC,KAAK,EAAE;QACrC;QACA,IAAIC,QAAQ,GAAGN,WAAW,CAAC,CAAC;QAE5B,IAAIK,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAIC,QAAQ,CAACvB,MAAM,EAAE;UAC1CX,UAAU,CAACF,IAAI,GAAG,EAAE,CAACO,MAAM,CAAC3B,kBAAkB,CAACsB,UAAU,CAACF,IAAI,CAACe,KAAK,CAAC,CAAC,EAAEoB,KAAK,CAAC,CAAC,EAAE,CAACjC,UAAU,CAACD,EAAE,CAAC,EAAErB,kBAAkB,CAACsB,UAAU,CAACF,IAAI,CAACe,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC;UACnJP,QAAQ,CAAC,EAAE,CAACrB,MAAM,CAAC3B,kBAAkB,CAACwD,QAAQ,CAACrB,KAAK,CAAC,CAAC,EAAEoB,KAAK,CAAC,CAAC,EAAE,CAACD,YAAY,CAAC,EAAEtD,kBAAkB,CAACwD,QAAQ,CAACrB,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9H,CAAC,MAAM;UACL,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAKJ,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGC,QAAQ,CAACvB,MAAM,CAAC,EAAE;YACnF/B,OAAO,CAAC,KAAK,EAAE,6EAA6E,CAAC;UAC/F;UAEAoB,UAAU,CAACF,IAAI,GAAG,EAAE,CAACO,MAAM,CAAC3B,kBAAkB,CAACsB,UAAU,CAACF,IAAI,CAAC,EAAE,CAACE,UAAU,CAACD,EAAE,CAAC,CAAC;UACjF2B,QAAQ,CAAC,EAAE,CAACrB,MAAM,CAAC3B,kBAAkB,CAACwD,QAAQ,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC,CAAC;QACnE;QAEAhC,UAAU,CAACD,EAAE,IAAI,CAAC;MACpB,CAAC;MACDuC,MAAM,EAAE,SAASA,MAAMA,CAACL,KAAK,EAAE;QAC7B,IAAIC,QAAQ,GAAGN,WAAW,CAAC,CAAC;QAC5B,IAAIW,QAAQ,GAAG,IAAIC,GAAG,CAACC,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAC;QAE9D,IAAIM,QAAQ,CAACI,IAAI,IAAI,CAAC,EAAE;UACtB;QACF;QAEA3C,UAAU,CAACF,IAAI,GAAGE,UAAU,CAACF,IAAI,CAAC8C,MAAM,CAAC,UAAUC,CAAC,EAAEC,SAAS,EAAE;UAC/D,OAAO,CAACP,QAAQ,CAACQ,GAAG,CAACD,SAAS,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC;;QAEJpB,QAAQ,CAACQ,QAAQ,CAACU,MAAM,CAAC,UAAUC,CAAC,EAAEG,UAAU,EAAE;UAChD,OAAO,CAACT,QAAQ,CAACQ,GAAG,CAACC,UAAU,CAAC;QAClC,CAAC,CAAC,CAAC;MACL,CAAC;MACDjE,IAAI,EAAE,SAASA,IAAIA,CAACkE,IAAI,EAAEC,EAAE,EAAE;QAC5B,IAAID,IAAI,KAAKC,EAAE,EAAE;UACf;QACF;QAEA,IAAIhB,QAAQ,GAAGN,WAAW,CAAC,CAAC,CAAC,CAAC;;QAE9B,IAAIqB,IAAI,GAAG,CAAC,IAAIA,IAAI,IAAIf,QAAQ,CAACvB,MAAM,IAAIuC,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAIhB,QAAQ,CAACvB,MAAM,EAAE;UAC1E;QACF;QAEAX,UAAU,CAACF,IAAI,GAAGd,KAAK,CAACgB,UAAU,CAACF,IAAI,EAAEmD,IAAI,EAAEC,EAAE,CAAC,CAAC,CAAC;;QAEpDxB,QAAQ,CAAC1C,KAAK,CAACkD,QAAQ,EAAEe,IAAI,EAAEC,EAAE,CAAC,CAAC;MACrC;IACF,CAAC;IACD,IAAIC,SAAS,GAAG9B,KAAK,IAAI,EAAE;IAE3B,IAAI,CAACoB,KAAK,CAACC,OAAO,CAACS,SAAS,CAAC,EAAE;MAC7BA,SAAS,GAAG,EAAE;MAEd,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCzD,OAAO,CAAC,KAAK,EAAE,oBAAoB,CAACyB,MAAM,CAACH,UAAU,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAE,yBAAyB,CAAC,CAAC;MAChG;IACF;IAEA,OAAO7D,QAAQ,CAAC4D,SAAS,CAACE,GAAG,CAAC,UAAUC,EAAE,EAAErB,KAAK,EAAE;MACjD,IAAIsB,GAAG,GAAGvD,UAAU,CAACF,IAAI,CAACmC,KAAK,CAAC;MAEhC,IAAIsB,GAAG,KAAKC,SAAS,EAAE;QACrBxD,UAAU,CAACF,IAAI,CAACmC,KAAK,CAAC,GAAGjC,UAAU,CAACD,EAAE;QACtCwD,GAAG,GAAGvD,UAAU,CAACF,IAAI,CAACmC,KAAK,CAAC;QAC5BjC,UAAU,CAACD,EAAE,IAAI,CAAC;MACpB;MAEA,OAAO;QACLV,IAAI,EAAE4C,KAAK;QACXsB,GAAG,EAAEA,GAAG;QACRE,WAAW,EAAE;MACf,CAAC;IACH,CAAC,CAAC,EAAE3B,UAAU,EAAEN,IAAI,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAED,eAAerC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}