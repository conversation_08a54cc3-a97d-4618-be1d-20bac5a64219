{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nvar MobilePopupInner = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    visible = props.visible,\n    zIndex = props.zIndex,\n    children = props.children,\n    _props$mobile = props.mobile;\n  _props$mobile = _props$mobile === void 0 ? {} : _props$mobile;\n  var popupClassName = _props$mobile.popupClassName,\n    popupStyle = _props$mobile.popupStyle,\n    _props$mobile$popupMo = _props$mobile.popupMotion,\n    popupMotion = _props$mobile$popupMo === void 0 ? {} : _props$mobile$popupMo,\n    popupRender = _props$mobile.popupRender;\n  var elementRef = React.useRef(); // ========================= Refs =========================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {},\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  }); // ======================== Render ========================\n\n  var mergedStyle = _objectSpread({\n    zIndex: zIndex\n  }, popupStyle);\n  var childNode = children; // Wrapper when multiple children\n\n  if (React.Children.count(children) > 1) {\n    childNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, children);\n  } // Mobile support additional render\n\n  if (popupRender) {\n    childNode = popupRender(childNode);\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: visible,\n    ref: elementRef,\n    removeOnLeave: true\n  }, popupMotion), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedClassName = classNames(prefixCls, popupClassName, motionClassName);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: motionRef,\n      className: mergedClassName,\n      style: _objectSpread(_objectSpread({}, motionStyle), mergedStyle)\n    }, childNode);\n  });\n});\nMobilePopupInner.displayName = 'MobilePopupInner';\nexport default MobilePopupInner;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "React", "CSSMotion", "classNames", "MobilePopupInner", "forwardRef", "props", "ref", "prefixCls", "visible", "zIndex", "children", "_props$mobile", "mobile", "popupClassName", "popupStyle", "_props$mobile$popupMo", "popupMotion", "popupRender", "elementRef", "useRef", "useImperativeHandle", "forceAlign", "getElement", "current", "mergedStyle", "childNode", "Children", "count", "createElement", "className", "concat", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle", "style", "mergedClassName", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-trigger/es/Popup/MobilePopupInner.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nvar MobilePopupInner = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n      visible = props.visible,\n      zIndex = props.zIndex,\n      children = props.children,\n      _props$mobile = props.mobile;\n  _props$mobile = _props$mobile === void 0 ? {} : _props$mobile;\n  var popupClassName = _props$mobile.popupClassName,\n      popupStyle = _props$mobile.popupStyle,\n      _props$mobile$popupMo = _props$mobile.popupMotion,\n      popupMotion = _props$mobile$popupMo === void 0 ? {} : _props$mobile$popupMo,\n      popupRender = _props$mobile.popupRender;\n  var elementRef = React.useRef(); // ========================= Refs =========================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {},\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  }); // ======================== Render ========================\n\n  var mergedStyle = _objectSpread({\n    zIndex: zIndex\n  }, popupStyle);\n\n  var childNode = children; // Wrapper when multiple children\n\n  if (React.Children.count(children) > 1) {\n    childNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, children);\n  } // Mobile support additional render\n\n\n  if (popupRender) {\n    childNode = popupRender(childNode);\n  }\n\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: visible,\n    ref: elementRef,\n    removeOnLeave: true\n  }, popupMotion), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n    var mergedClassName = classNames(prefixCls, popupClassName, motionClassName);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: motionRef,\n      className: mergedClassName,\n      style: _objectSpread(_objectSpread({}, motionStyle), mergedStyle)\n    }, childNode);\n  });\n});\nMobilePopupInner.displayName = 'MobilePopupInner';\nexport default MobilePopupInner;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,gBAAgB,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACzE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,aAAa,GAAGN,KAAK,CAACO,MAAM;EAChCD,aAAa,GAAGA,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;EAC7D,IAAIE,cAAc,GAAGF,aAAa,CAACE,cAAc;IAC7CC,UAAU,GAAGH,aAAa,CAACG,UAAU;IACrCC,qBAAqB,GAAGJ,aAAa,CAACK,WAAW;IACjDA,WAAW,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;IAC3EE,WAAW,GAAGN,aAAa,CAACM,WAAW;EAC3C,IAAIC,UAAU,GAAGlB,KAAK,CAACmB,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEjCnB,KAAK,CAACoB,mBAAmB,CAACd,GAAG,EAAE,YAAY;IACzC,OAAO;MACLe,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG,CAAC,CAAC;MACpCC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOJ,UAAU,CAACK,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,WAAW,GAAGzB,aAAa,CAAC;IAC9BU,MAAM,EAAEA;EACV,CAAC,EAAEK,UAAU,CAAC;EAEd,IAAIW,SAAS,GAAGf,QAAQ,CAAC,CAAC;;EAE1B,IAAIV,KAAK,CAAC0B,QAAQ,CAACC,KAAK,CAACjB,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtCe,SAAS,GAAG,aAAazB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;MAClDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAEG,QAAQ,CAAC;EACd,CAAC,CAAC;;EAGF,IAAIO,WAAW,EAAE;IACfQ,SAAS,GAAGR,WAAW,CAACQ,SAAS,CAAC;EACpC;EAEA,OAAO,aAAazB,KAAK,CAAC4B,aAAa,CAAC3B,SAAS,EAAEH,QAAQ,CAAC;IAC1DU,OAAO,EAAEA,OAAO;IAChBF,GAAG,EAAEY,UAAU;IACfa,aAAa,EAAE;EACjB,CAAC,EAAEf,WAAW,CAAC,EAAE,UAAUgB,IAAI,EAAEC,SAAS,EAAE;IAC1C,IAAIC,eAAe,GAAGF,IAAI,CAACH,SAAS;MAChCM,WAAW,GAAGH,IAAI,CAACI,KAAK;IAC5B,IAAIC,eAAe,GAAGnC,UAAU,CAACK,SAAS,EAAEM,cAAc,EAAEqB,eAAe,CAAC;IAC5E,OAAO,aAAalC,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;MAC7CtB,GAAG,EAAE2B,SAAS;MACdJ,SAAS,EAAEQ,eAAe;MAC1BD,KAAK,EAAErC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,WAAW,CAAC,EAAEX,WAAW;IAClE,CAAC,EAAEC,SAAS,CAAC;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFtB,gBAAgB,CAACmC,WAAW,GAAG,kBAAkB;AACjD,eAAenC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}