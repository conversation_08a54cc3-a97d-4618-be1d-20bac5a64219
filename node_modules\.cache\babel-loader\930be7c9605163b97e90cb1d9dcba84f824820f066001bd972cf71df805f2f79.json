{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nexport var offset = 4;\nexport default function dropIndicatorRender(props) {\n  var _style;\n  var dropPosition = props.dropPosition,\n    dropLevelOffset = props.dropLevelOffset,\n    prefixCls = props.prefixCls,\n    indent = props.indent,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction;\n  var startPosition = direction === 'ltr' ? 'left' : 'right';\n  var endPosition = direction === 'ltr' ? 'right' : 'left';\n  var style = (_style = {}, _defineProperty(_style, startPosition, -dropLevelOffset * indent + offset), _defineProperty(_style, endPosition, 0), _style);\n  switch (dropPosition) {\n    case -1:\n      style.top = -3;\n      break;\n    case 1:\n      style.bottom = -3;\n      break;\n    default:\n      // dropPosition === 0\n      style.bottom = -3;\n      style[startPosition] = indent + offset;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: \"\".concat(prefixCls, \"-drop-indicator\")\n  });\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "offset", "dropIndicatorRender", "props", "_style", "dropPosition", "dropLevelOffset", "prefixCls", "indent", "_props$direction", "direction", "startPosition", "endPosition", "style", "top", "bottom", "createElement", "className", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/tree/utils/dropIndicator.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nexport var offset = 4;\nexport default function dropIndicatorRender(props) {\n  var _style;\n\n  var dropPosition = props.dropPosition,\n      dropLevelOffset = props.dropLevelOffset,\n      prefixCls = props.prefixCls,\n      indent = props.indent,\n      _props$direction = props.direction,\n      direction = _props$direction === void 0 ? 'ltr' : _props$direction;\n  var startPosition = direction === 'ltr' ? 'left' : 'right';\n  var endPosition = direction === 'ltr' ? 'right' : 'left';\n  var style = (_style = {}, _defineProperty(_style, startPosition, -dropLevelOffset * indent + offset), _defineProperty(_style, endPosition, 0), _style);\n\n  switch (dropPosition) {\n    case -1:\n      style.top = -3;\n      break;\n\n    case 1:\n      style.bottom = -3;\n      break;\n\n    default:\n      // dropPosition === 0\n      style.bottom = -3;\n      style[startPosition] = indent + offset;\n      break;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: \"\".concat(prefixCls, \"-drop-indicator\")\n  });\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,MAAM,GAAG,CAAC;AACrB,eAAe,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACjD,IAAIC,MAAM;EAEV,IAAIC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,eAAe,GAAGH,KAAK,CAACG,eAAe;IACvCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,gBAAgB,GAAGN,KAAK,CAACO,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;EACtE,IAAIE,aAAa,GAAGD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;EAC1D,IAAIE,WAAW,GAAGF,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;EACxD,IAAIG,KAAK,IAAIT,MAAM,GAAG,CAAC,CAAC,EAAEL,eAAe,CAACK,MAAM,EAAEO,aAAa,EAAE,CAACL,eAAe,GAAGE,MAAM,GAAGP,MAAM,CAAC,EAAEF,eAAe,CAACK,MAAM,EAAEQ,WAAW,EAAE,CAAC,CAAC,EAAER,MAAM,CAAC;EAEtJ,QAAQC,YAAY;IAClB,KAAK,CAAC,CAAC;MACLQ,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC;MACd;IAEF,KAAK,CAAC;MACJD,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACjB;IAEF;MACE;MACAF,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACjBF,KAAK,CAACF,aAAa,CAAC,GAAGH,MAAM,GAAGP,MAAM;MACtC;EACJ;EAEA,OAAO,aAAaD,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC7CH,KAAK,EAAEA,KAAK;IACZI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACX,SAAS,EAAE,iBAAiB;EACnD,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}