export default MultiCheckAudit;
declare class MultiCheckAudit extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
    /**
     * @param {{failures: Array<string>, manifestValues?: LH.Artifacts.ManifestValues}} result
     * @return {LH.Audit.Product}
     */
    static createAuditProduct(result: {
        failures: Array<string>;
        manifestValues?: LH.Artifacts.ManifestValues;
    }): LH.Audit.Product;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<{failures: Array<string>, manifestValues?: LH.Artifacts.ManifestValues}>}
     */
    static audit_(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<{
        failures: Array<string>;
        manifestValues?: LH.Artifacts.ManifestValues;
    }>;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=multi-check-audit.d.ts.map