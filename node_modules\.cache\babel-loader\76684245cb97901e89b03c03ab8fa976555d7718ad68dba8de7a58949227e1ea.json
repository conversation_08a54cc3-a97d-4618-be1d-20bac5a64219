{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nimport { defaults, hasXMLHttpRequest } from './utils.js';\nimport * as fetchNode from './getFetch.cjs';\nvar fetchApi;\nif (typeof fetch === 'function') {\n  if (typeof global !== 'undefined' && global.fetch) {\n    fetchApi = global.fetch;\n  } else if (typeof window !== 'undefined' && window.fetch) {\n    fetchApi = window.fetch;\n  }\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (!fetchApi && fetchNode && !XmlHttpRequestApi && !ActiveXObjectApi) fetchApi = fetchNode.default || fetchNode;\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = defaults({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (payload) headers['Content-Type'] = 'application/json';\n  fetchApi(url, defaults({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions)).then(function (response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  }).catch(callback);\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x;\n    if (XmlHttpRequestApi) {\n      x = new XmlHttpRequestApi();\n    } else {\n      x = new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    }\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n};\nexport default request;", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "defaults", "hasXMLHttpRequest", "fetchNode", "fetchApi", "fetch", "global", "window", "XmlHttpRequestApi", "XMLHttpRequest", "ActiveXObjectApi", "ActiveXObject", "default", "undefined", "addQueryString", "url", "params", "queryString", "paramName", "encodeURIComponent", "indexOf", "slice", "requestWithFetch", "options", "payload", "callback", "queryStringParams", "headers", "customHeaders", "method", "body", "stringify", "requestOptions", "then", "response", "ok", "statusText", "status", "text", "data", "catch", "requestWithXmlHttpRequest", "x", "open", "crossDomain", "setRequestHeader", "withCredentials", "overrideMimeType", "h", "i", "onreadystatechange", "readyState", "responseText", "send", "e", "console", "log", "request"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/i18next-http-backend/esm/request.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nimport { defaults, hasXMLHttpRequest } from './utils.js';\nimport * as fetchNode from './getFetch.cjs';\nvar fetchApi;\n\nif (typeof fetch === 'function') {\n  if (typeof global !== 'undefined' && global.fetch) {\n    fetchApi = global.fetch;\n  } else if (typeof window !== 'undefined' && window.fetch) {\n    fetchApi = window.fetch;\n  }\n}\n\nvar XmlHttpRequestApi;\n\nif (hasXMLHttpRequest) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\n\nvar ActiveXObjectApi;\n\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\n\nif (!fetchApi && fetchNode && !XmlHttpRequestApi && !ActiveXObjectApi) fetchApi = fetchNode.default || fetchNode;\nif (typeof fetchApi !== 'function') fetchApi = undefined;\n\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n\n  return url;\n};\n\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n\n  var headers = defaults({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (payload) headers['Content-Type'] = 'application/json';\n  fetchApi(url, defaults({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions)).then(function (response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  }).catch(callback);\n};\n\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n\n  try {\n    var x;\n\n    if (XmlHttpRequestApi) {\n      x = new XmlHttpRequestApi();\n    } else {\n      x = new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    }\n\n    x.open(payload ? 'POST' : 'GET', url, 1);\n\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n\n    x.withCredentials = !!options.withCredentials;\n\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\n\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n\n  callback = callback || function () {};\n\n  if (fetchApi) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n\n  if (hasXMLHttpRequest || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n};\n\nexport default request;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAE/U,SAASK,QAAQ,EAAEC,iBAAiB,QAAQ,YAAY;AACxD,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAC3C,IAAIC,QAAQ;AAEZ,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE;EAC/B,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACD,KAAK,EAAE;IACjDD,QAAQ,GAAGE,MAAM,CAACD,KAAK;EACzB,CAAC,MAAM,IAAI,OAAOE,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACF,KAAK,EAAE;IACxDD,QAAQ,GAAGG,MAAM,CAACF,KAAK;EACzB;AACF;AAEA,IAAIG,iBAAiB;AAErB,IAAIN,iBAAiB,EAAE;EACrB,IAAI,OAAOI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACG,cAAc,EAAE;IAC1DD,iBAAiB,GAAGF,MAAM,CAACG,cAAc;EAC3C,CAAC,MAAM,IAAI,OAAOF,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACE,cAAc,EAAE;IACjED,iBAAiB,GAAGD,MAAM,CAACE,cAAc;EAC3C;AACF;AAEA,IAAIC,gBAAgB;AAEpB,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;EACvC,IAAI,OAAOL,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACK,aAAa,EAAE;IACzDD,gBAAgB,GAAGJ,MAAM,CAACK,aAAa;EACzC,CAAC,MAAM,IAAI,OAAOJ,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACI,aAAa,EAAE;IAChED,gBAAgB,GAAGH,MAAM,CAACI,aAAa;EACzC;AACF;AAEA,IAAI,CAACP,QAAQ,IAAID,SAAS,IAAI,CAACK,iBAAiB,IAAI,CAACE,gBAAgB,EAAEN,QAAQ,GAAGD,SAAS,CAACS,OAAO,IAAIT,SAAS;AAChH,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAEA,QAAQ,GAAGS,SAAS;AAExD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACxD,IAAIA,MAAM,IAAIrB,OAAO,CAACqB,MAAM,CAAC,KAAK,QAAQ,EAAE;IAC1C,IAAIC,WAAW,GAAG,EAAE;IAEpB,KAAK,IAAIC,SAAS,IAAIF,MAAM,EAAE;MAC5BC,WAAW,IAAI,GAAG,GAAGE,kBAAkB,CAACD,SAAS,CAAC,GAAG,GAAG,GAAGC,kBAAkB,CAACH,MAAM,CAACE,SAAS,CAAC,CAAC;IAClG;IAEA,IAAI,CAACD,WAAW,EAAE,OAAOF,GAAG;IAC5BA,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC;EAC1E;EAEA,OAAON,GAAG;AACZ,CAAC;AAED,IAAIO,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,OAAO,EAAER,GAAG,EAAES,OAAO,EAAEC,QAAQ,EAAE;EAChF,IAAIF,OAAO,CAACG,iBAAiB,EAAE;IAC7BX,GAAG,GAAGD,cAAc,CAACC,GAAG,EAAEQ,OAAO,CAACG,iBAAiB,CAAC;EACtD;EAEA,IAAIC,OAAO,GAAG1B,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAOsB,OAAO,CAACK,aAAa,KAAK,UAAU,GAAGL,OAAO,CAACK,aAAa,CAAC,CAAC,GAAGL,OAAO,CAACK,aAAa,CAAC;EACzH,IAAIJ,OAAO,EAAEG,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EACzDvB,QAAQ,CAACW,GAAG,EAAEd,QAAQ,CAAC;IACrB4B,MAAM,EAAEL,OAAO,GAAG,MAAM,GAAG,KAAK;IAChCM,IAAI,EAAEN,OAAO,GAAGD,OAAO,CAACQ,SAAS,CAACP,OAAO,CAAC,GAAGX,SAAS;IACtDc,OAAO,EAAEA;EACX,CAAC,EAAE,OAAOJ,OAAO,CAACS,cAAc,KAAK,UAAU,GAAGT,OAAO,CAACS,cAAc,CAACR,OAAO,CAAC,GAAGD,OAAO,CAACS,cAAc,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,QAAQ,EAAE;IACpI,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,OAAOV,QAAQ,CAACS,QAAQ,CAACE,UAAU,IAAI,OAAO,EAAE;MAChEC,MAAM,EAAEH,QAAQ,CAACG;IACnB,CAAC,CAAC;IACFH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACL,IAAI,CAAC,UAAUM,IAAI,EAAE;MACnCd,QAAQ,CAAC,IAAI,EAAE;QACbY,MAAM,EAAEH,QAAQ,CAACG,MAAM;QACvBE,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,CAACC,KAAK,CAACf,QAAQ,CAAC;EACpB,CAAC,CAAC,CAACe,KAAK,CAACf,QAAQ,CAAC;AACpB,CAAC;AAED,IAAIgB,yBAAyB,GAAG,SAASA,yBAAyBA,CAAClB,OAAO,EAAER,GAAG,EAAES,OAAO,EAAEC,QAAQ,EAAE;EAClG,IAAID,OAAO,IAAI7B,OAAO,CAAC6B,OAAO,CAAC,KAAK,QAAQ,EAAE;IAC5CA,OAAO,GAAGV,cAAc,CAAC,EAAE,EAAEU,OAAO,CAAC,CAACH,KAAK,CAAC,CAAC,CAAC;EAChD;EAEA,IAAIE,OAAO,CAACG,iBAAiB,EAAE;IAC7BX,GAAG,GAAGD,cAAc,CAACC,GAAG,EAAEQ,OAAO,CAACG,iBAAiB,CAAC;EACtD;EAEA,IAAI;IACF,IAAIgB,CAAC;IAEL,IAAIlC,iBAAiB,EAAE;MACrBkC,CAAC,GAAG,IAAIlC,iBAAiB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLkC,CAAC,GAAG,IAAIhC,gBAAgB,CAAC,oBAAoB,CAAC;IAChD;IAEAgC,CAAC,CAACC,IAAI,CAACnB,OAAO,GAAG,MAAM,GAAG,KAAK,EAAET,GAAG,EAAE,CAAC,CAAC;IAExC,IAAI,CAACQ,OAAO,CAACqB,WAAW,EAAE;MACxBF,CAAC,CAACG,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;IAC1D;IAEAH,CAAC,CAACI,eAAe,GAAG,CAAC,CAACvB,OAAO,CAACuB,eAAe;IAE7C,IAAItB,OAAO,EAAE;MACXkB,CAAC,CAACG,gBAAgB,CAAC,cAAc,EAAE,mCAAmC,CAAC;IACzE;IAEA,IAAIH,CAAC,CAACK,gBAAgB,EAAE;MACtBL,CAAC,CAACK,gBAAgB,CAAC,kBAAkB,CAAC;IACxC;IAEA,IAAIC,CAAC,GAAGzB,OAAO,CAACK,aAAa;IAC7BoB,CAAC,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,CAAC,GAAGA,CAAC;IAErC,IAAIA,CAAC,EAAE;MACL,KAAK,IAAIC,CAAC,IAAID,CAAC,EAAE;QACfN,CAAC,CAACG,gBAAgB,CAACI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;MAC7B;IACF;IAEAP,CAAC,CAACQ,kBAAkB,GAAG,YAAY;MACjCR,CAAC,CAACS,UAAU,GAAG,CAAC,IAAI1B,QAAQ,CAACiB,CAAC,CAACL,MAAM,IAAI,GAAG,GAAGK,CAAC,CAACN,UAAU,GAAG,IAAI,EAAE;QAClEC,MAAM,EAAEK,CAAC,CAACL,MAAM;QAChBE,IAAI,EAAEG,CAAC,CAACU;MACV,CAAC,CAAC;IACJ,CAAC;IAEDV,CAAC,CAACW,IAAI,CAAC7B,OAAO,CAAC;EACjB,CAAC,CAAC,OAAO8B,CAAC,EAAE;IACVC,OAAO,IAAIA,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;EAC3B;AACF,CAAC;AAED,IAAIG,OAAO,GAAG,SAASA,OAAOA,CAAClC,OAAO,EAAER,GAAG,EAAES,OAAO,EAAEC,QAAQ,EAAE;EAC9D,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;IACjCC,QAAQ,GAAGD,OAAO;IAClBA,OAAO,GAAGX,SAAS;EACrB;EAEAY,QAAQ,GAAGA,QAAQ,IAAI,YAAY,CAAC,CAAC;EAErC,IAAIrB,QAAQ,EAAE;IACZ,OAAOkB,gBAAgB,CAACC,OAAO,EAAER,GAAG,EAAES,OAAO,EAAEC,QAAQ,CAAC;EAC1D;EAEA,IAAIvB,iBAAiB,IAAI,OAAOS,aAAa,KAAK,UAAU,EAAE;IAC5D,OAAO8B,yBAAyB,CAAClB,OAAO,EAAER,GAAG,EAAES,OAAO,EAAEC,QAAQ,CAAC;EACnE;AACF,CAAC;AAED,eAAegC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}