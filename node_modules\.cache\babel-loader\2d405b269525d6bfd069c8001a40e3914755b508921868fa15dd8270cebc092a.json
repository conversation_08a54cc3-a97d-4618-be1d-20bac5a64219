{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nvar path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\nvar SkeletonImage = function SkeletonImage(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-image\"), className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: \"\".concat(prefixCls, \"-image-svg\")\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: \"\".concat(prefixCls, \"-image-path\")\n  }))));\n};\nexport default SkeletonImage;", "map": {"version": 3, "names": ["React", "classNames", "ConfigContext", "path", "SkeletonImage", "props", "customizePrefixCls", "prefixCls", "className", "style", "_React$useContext", "useContext", "getPrefixCls", "cls", "concat", "createElement", "viewBox", "xmlns", "d"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Image.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nvar path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\n\nvar SkeletonImage = function SkeletonImage(props) {\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-image\"), className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: \"\".concat(prefixCls, \"-image-svg\")\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: \"\".concat(prefixCls, \"-image-path\")\n  }))));\n};\n\nexport default SkeletonImage;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,IAAI,GAAG,i3BAAi3B;AAE53B,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACpCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;EAEvB,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACT,aAAa,CAAC;IACnDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIL,SAAS,GAAGK,YAAY,CAAC,UAAU,EAAEN,kBAAkB,CAAC;EAC5D,IAAIO,GAAG,GAAGZ,UAAU,CAACM,SAAS,EAAE,EAAE,CAACO,MAAM,CAACP,SAAS,EAAE,UAAU,CAAC,EAAEC,SAAS,CAAC;EAC5E,OAAO,aAAaR,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IAC7CP,SAAS,EAAEK;EACb,CAAC,EAAE,aAAab,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACzCP,SAAS,EAAEP,UAAU,CAAC,EAAE,CAACa,MAAM,CAACP,SAAS,EAAE,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAChEC,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaT,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACzCC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE,4BAA4B;IACnCT,SAAS,EAAE,EAAE,CAACM,MAAM,CAACP,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAE,aAAaP,KAAK,CAACe,aAAa,CAAC,MAAM,EAAE;IAC1CG,CAAC,EAAEf,IAAI;IACPK,SAAS,EAAE,EAAE,CAACM,MAAM,CAACP,SAAS,EAAE,aAAa;EAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}