{"ast": null, "code": "/*$$           + + + + + + + || THE GREATEST || + + + + + + +        \n| $$                                                               \n| $$        /$$$$$$   /$$$$$$   /$$$$$$   /$$$$$$$    /$$  /$$$$$$$\n| $$       /$$__  $$ /$$__  $$ /$$__  $$ /$$_____/   |__/ /$$_____/\n| $$      | $$  \\ $$| $$  \\ $$| $$  \\ $$|  $$$$$$     /$$|  $$$$$$ \n| $$      | $$  | $$| $$  | $$| $$  | $$ \\____  $$   | $$ \\____  $$\n| $$$$$$$$|  $$$$$$/|  $$$$$$$|  $$$$$$/ /$$$$$$$//$$| $$ /$$$$$$$/\n|________/ \\______/  \\____  $$ \\______/ |_______/|__/| $$|_______/ \n                     /$$  \\ $$                  /$$  | $$          \n                    |  $$$$$$/                 |  $$$$$$/          \n                     \\______/                   \\_____*/\n\nconst Logos = {\n  prestashop: require('../img/prestashop.svg').default,\n  // Prestashop Logo\n  mail: require('../img/mail.svg').default,\n  // Mail Logo\n  alyante: require('../img/alyante.svg').default,\n  // Alyante Logo\n  mrwine: require('../img/mrwine.ico').default,\n  // Mrwine Logo\n  alcolica: require('../img/alcolica.png').default,\n  // Alcolica Artigianale Logo\n  sabores: require('../img/sabores.ico').default // Sabores de Italia Logo\n};\nexport default Logos;", "map": {"version": 3, "names": ["Logos", "prestashop", "require", "default", "mail", "alyante", "mr<PERSON>", "alcolica", "sabores"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/resources/Logos.js"], "sourcesContent": ["/*$$           + + + + + + + || THE GREATEST || + + + + + + +        \n| $$                                                               \n| $$        /$$$$$$   /$$$$$$   /$$$$$$   /$$$$$$$    /$$  /$$$$$$$\n| $$       /$$__  $$ /$$__  $$ /$$__  $$ /$$_____/   |__/ /$$_____/\n| $$      | $$  \\ $$| $$  \\ $$| $$  \\ $$|  $$$$$$     /$$|  $$$$$$ \n| $$      | $$  | $$| $$  | $$| $$  | $$ \\____  $$   | $$ \\____  $$\n| $$$$$$$$|  $$$$$$/|  $$$$$$$|  $$$$$$/ /$$$$$$$//$$| $$ /$$$$$$$/\n|________/ \\______/  \\____  $$ \\______/ |_______/|__/| $$|_______/ \n                     /$$  \\ $$                  /$$  | $$          \n                    |  $$$$$$/                 |  $$$$$$/          \n                     \\______/                   \\_____*/\n\nconst Logos = {\n    prestashop : require('../img/prestashop.svg').default, // Prestashop Logo\n    mail       : require('../img/mail.svg').default,       // Mail Logo\n    alyante    : require('../img/alyante.svg').default,    // Alyante Logo\n    mrwine     : require('../img/mrwine.ico').default,     // Mrwine Logo\n    alcolica   : require('../img/alcolica.png').default,   // Alcolica Artigianale Logo\n    sabores    : require('../img/sabores.ico').default,    // Sabores de Italia Logo\n}\nexport default Logos;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAG;EACVC,UAAU,EAAGC,OAAO,CAAC,uBAAuB,CAAC,CAACC,OAAO;EAAE;EACvDC,IAAI,EAASF,OAAO,CAAC,iBAAiB,CAAC,CAACC,OAAO;EAAQ;EACvDE,OAAO,EAAMH,OAAO,CAAC,oBAAoB,CAAC,CAACC,OAAO;EAAK;EACvDG,MAAM,EAAOJ,OAAO,CAAC,mBAAmB,CAAC,CAACC,OAAO;EAAM;EACvDI,QAAQ,EAAKL,OAAO,CAAC,qBAAqB,CAAC,CAACC,OAAO;EAAI;EACvDK,OAAO,EAAMN,OAAO,CAAC,oBAAoB,CAAC,CAACC,OAAO,CAAK;AAC3D,CAAC;AACD,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}