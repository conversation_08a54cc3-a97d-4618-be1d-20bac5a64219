{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/**\n * TODO: 4.0\n *\n * - Remove `dataSource`\n * - `size` not work with customizeInput\n * - CustomizeInput not feedback `ENTER` key since accessibility enhancement\n */\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport Select from '../select';\nimport { ConfigConsumer } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport { isValidElement } from '../_util/reactNode';\nvar Option = Select.Option;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return child && child.type && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nvar AutoComplete = function AutoComplete(props, ref) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    dataSource = props.dataSource;\n  var childNodes = toArray(children); // ============================= Input =============================\n\n  var customizeInput;\n  if (childNodes.length === 1 && isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    var _childNodes = _slicedToArray(childNodes, 1);\n    customizeInput = _childNodes[0];\n  }\n  var getInputElement = customizeInput ? function () {\n    return customizeInput;\n  } : undefined; // ============================ Options ============================\n\n  var optionChildren; // [Legacy] convert `children` or `dataSource` into option children\n\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(function (item) {\n      if (isValidElement(item)) {\n        return item;\n      }\n      switch (_typeof(item)) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            var optionValue = item.value;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          throw new Error('AutoComplete[dataSource] only supports type `string[] | Object[]`.');\n      }\n    }) : [];\n  } // ============================ Warning ============================\n\n  React.useEffect(function () {\n    devWarning(!('dataSource' in props), 'AutoComplete', '`dataSource` is deprecated, please use `options` instead.');\n    devWarning(!customizeInput || !('size' in props), 'AutoComplete', 'You need to control style self instead of setting `size` when using customize input.');\n  }, []);\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefixCls = getPrefixCls('select', customizePrefixCls);\n    return /*#__PURE__*/React.createElement(Select, _extends({\n      ref: ref\n    }, omit(props, ['dataSource']), {\n      prefixCls: prefixCls,\n      className: classNames(\"\".concat(prefixCls, \"-auto-complete\"), className),\n      mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n      // Internal api\n      getInputElement: getInputElement\n    }), optionChildren);\n  });\n};\nvar RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nRefAutoComplete.Option = Option;\nexport default RefAutoComplete;", "map": {"version": 3, "names": ["_extends", "_typeof", "_slicedToArray", "React", "toArray", "classNames", "omit", "Select", "ConfigConsumer", "dev<PERSON><PERSON><PERSON>", "isValidElement", "Option", "isSelectOptionOrSelectOptGroup", "child", "type", "isSelectOption", "isSelectOptGroup", "AutoComplete", "props", "ref", "customizePrefixCls", "prefixCls", "className", "children", "dataSource", "childNodes", "customizeInput", "length", "_childNodes", "getInputElement", "undefined", "option<PERSON><PERSON><PERSON>n", "map", "item", "createElement", "key", "value", "optionValue", "text", "Error", "useEffect", "_ref", "getPrefixCls", "concat", "mode", "SECRET_COMBOBOX_MODE_DO_NOT_USE", "RefAutoComplete", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/auto-complete/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/**\n * TODO: 4.0\n *\n * - Remove `dataSource`\n * - `size` not work with customizeInput\n * - CustomizeInput not feedback `ENTER` key since accessibility enhancement\n */\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport Select from '../select';\nimport { ConfigConsumer } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport { isValidElement } from '../_util/reactNode';\nvar Option = Select.Option;\n\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return child && child.type && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\n\nvar AutoComplete = function AutoComplete(props, ref) {\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      children = props.children,\n      dataSource = props.dataSource;\n  var childNodes = toArray(children); // ============================= Input =============================\n\n  var customizeInput;\n\n  if (childNodes.length === 1 && isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    var _childNodes = _slicedToArray(childNodes, 1);\n\n    customizeInput = _childNodes[0];\n  }\n\n  var getInputElement = customizeInput ? function () {\n    return customizeInput;\n  } : undefined; // ============================ Options ============================\n\n  var optionChildren; // [Legacy] convert `children` or `dataSource` into option children\n\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(function (item) {\n      if (isValidElement(item)) {\n        return item;\n      }\n\n      switch (_typeof(item)) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n\n        case 'object':\n          {\n            var optionValue = item.value;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n\n        default:\n          throw new Error('AutoComplete[dataSource] only supports type `string[] | Object[]`.');\n      }\n    }) : [];\n  } // ============================ Warning ============================\n\n\n  React.useEffect(function () {\n    devWarning(!('dataSource' in props), 'AutoComplete', '`dataSource` is deprecated, please use `options` instead.');\n    devWarning(!customizeInput || !('size' in props), 'AutoComplete', 'You need to control style self instead of setting `size` when using customize input.');\n  }, []);\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var prefixCls = getPrefixCls('select', customizePrefixCls);\n    return /*#__PURE__*/React.createElement(Select, _extends({\n      ref: ref\n    }, omit(props, ['dataSource']), {\n      prefixCls: prefixCls,\n      className: classNames(\"\".concat(prefixCls, \"-auto-complete\"), className),\n      mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n      // Internal api\n      getInputElement: getInputElement\n    }), optionChildren);\n  });\n};\n\nvar RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nRefAutoComplete.Option = Option;\nexport default RefAutoComplete;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,IAAIC,MAAM,GAAGJ,MAAM,CAACI,MAAM;AAE1B,SAASC,8BAA8BA,CAACC,KAAK,EAAE;EAC7C,OAAOA,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAKD,KAAK,CAACC,IAAI,CAACC,cAAc,IAAIF,KAAK,CAACC,IAAI,CAACE,gBAAgB,CAAC;AAC1F;AAEA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACpCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,UAAU,GAAGN,KAAK,CAACM,UAAU;EACjC,IAAIC,UAAU,GAAGrB,OAAO,CAACmB,QAAQ,CAAC,CAAC,CAAC;;EAEpC,IAAIG,cAAc;EAElB,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,IAAIjB,cAAc,CAACe,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAACb,8BAA8B,CAACa,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9G,IAAIG,WAAW,GAAG1B,cAAc,CAACuB,UAAU,EAAE,CAAC,CAAC;IAE/CC,cAAc,GAAGE,WAAW,CAAC,CAAC,CAAC;EACjC;EAEA,IAAIC,eAAe,GAAGH,cAAc,GAAG,YAAY;IACjD,OAAOA,cAAc;EACvB,CAAC,GAAGI,SAAS,CAAC,CAAC;;EAEf,IAAIC,cAAc,CAAC,CAAC;;EAEpB,IAAIN,UAAU,CAACE,MAAM,IAAIf,8BAA8B,CAACa,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACtEM,cAAc,GAAGR,QAAQ;EAC3B,CAAC,MAAM;IACLQ,cAAc,GAAGP,UAAU,GAAGA,UAAU,CAACQ,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC3D,IAAIvB,cAAc,CAACuB,IAAI,CAAC,EAAE;QACxB,OAAOA,IAAI;MACb;MAEA,QAAQhC,OAAO,CAACgC,IAAI,CAAC;QACnB,KAAK,QAAQ;UACX,OAAO,aAAa9B,KAAK,CAAC+B,aAAa,CAACvB,MAAM,EAAE;YAC9CwB,GAAG,EAAEF,IAAI;YACTG,KAAK,EAAEH;UACT,CAAC,EAAEA,IAAI,CAAC;QAEV,KAAK,QAAQ;UACX;YACE,IAAII,WAAW,GAAGJ,IAAI,CAACG,KAAK;YAC5B,OAAO,aAAajC,KAAK,CAAC+B,aAAa,CAACvB,MAAM,EAAE;cAC9CwB,GAAG,EAAEE,WAAW;cAChBD,KAAK,EAAEC;YACT,CAAC,EAAEJ,IAAI,CAACK,IAAI,CAAC;UACf;QAEF;UACE,MAAM,IAAIC,KAAK,CAAC,oEAAoE,CAAC;MACzF;IACF,CAAC,CAAC,GAAG,EAAE;EACT,CAAC,CAAC;;EAGFpC,KAAK,CAACqC,SAAS,CAAC,YAAY;IAC1B/B,UAAU,CAAC,EAAE,YAAY,IAAIS,KAAK,CAAC,EAAE,cAAc,EAAE,2DAA2D,CAAC;IACjHT,UAAU,CAAC,CAACiB,cAAc,IAAI,EAAE,MAAM,IAAIR,KAAK,CAAC,EAAE,cAAc,EAAE,sFAAsF,CAAC;EAC3J,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaf,KAAK,CAAC+B,aAAa,CAAC1B,cAAc,EAAE,IAAI,EAAE,UAAUiC,IAAI,EAAE;IAC5E,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IACpC,IAAIrB,SAAS,GAAGqB,YAAY,CAAC,QAAQ,EAAEtB,kBAAkB,CAAC;IAC1D,OAAO,aAAajB,KAAK,CAAC+B,aAAa,CAAC3B,MAAM,EAAEP,QAAQ,CAAC;MACvDmB,GAAG,EAAEA;IACP,CAAC,EAAEb,IAAI,CAACY,KAAK,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE;MAC9BG,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEjB,UAAU,CAAC,EAAE,CAACsC,MAAM,CAACtB,SAAS,EAAE,gBAAgB,CAAC,EAAEC,SAAS,CAAC;MACxEsB,IAAI,EAAErC,MAAM,CAACsC,+BAA+B;MAC5C;MACAhB,eAAe,EAAEA;IACnB,CAAC,CAAC,EAAEE,cAAc,CAAC;EACrB,CAAC,CAAC;AACJ,CAAC;AAED,IAAIe,eAAe,GAAG,aAAa3C,KAAK,CAAC4C,UAAU,CAAC9B,YAAY,CAAC;AACjE6B,eAAe,CAACnC,MAAM,GAAGA,MAAM;AAC/B,eAAemC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}