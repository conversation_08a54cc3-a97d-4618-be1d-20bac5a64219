{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport warning, { noteOnce } from \"rc-util/es/warning\";\nimport toNodeArray from \"rc-util/es/Children/toArray\";\nimport { convertChildrenToData } from './legacyUtil';\nimport { toArray } from './commonUtil';\nimport { isMultiple } from '../BaseSelect';\nfunction warningProps(props) {\n  var mode = props.mode,\n    options = props.options,\n    children = props.children,\n    backfill = props.backfill,\n    allowClear = props.allowClear,\n    placeholder = props.placeholder,\n    getInputElement = props.getInputElement,\n    showSearch = props.showSearch,\n    onSearch = props.onSearch,\n    defaultOpen = props.defaultOpen,\n    autoFocus = props.autoFocus,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    inputValue = props.inputValue,\n    optionLabelProp = props.optionLabelProp;\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = showSearch !== undefined ? showSearch : multiple || mode === 'combobox';\n  var mergedOptions = options || convertChildrenToData(children); // `tags` should not set option as disabled\n\n  warning(mode !== 'tags' || mergedOptions.every(function (opt) {\n    return !opt.disabled;\n  }), 'Please avoid setting option to disabled in tags mode since user can always type text as tag.'); // `combobox` & `tags` should option be `string` type\n\n  if (mode === 'tags' || mode === 'combobox') {\n    var hasNumberValue = mergedOptions.some(function (item) {\n      if (item.options) {\n        return item.options.some(function (opt) {\n          return typeof ('value' in opt ? opt.value : opt.key) === 'number';\n        });\n      }\n      return typeof ('value' in item ? item.value : item.key) === 'number';\n    });\n    warning(!hasNumberValue, '`value` of Option should not use number type when `mode` is `tags` or `combobox`.');\n  } // `combobox` should not use `optionLabelProp`\n\n  warning(mode !== 'combobox' || !optionLabelProp, '`combobox` mode not support `optionLabelProp`. Please set `value` on Option directly.'); // Only `combobox` support `backfill`\n\n  warning(mode === 'combobox' || !backfill, '`backfill` only works with `combobox` mode.'); // Only `combobox` support `getInputElement`\n\n  warning(mode === 'combobox' || !getInputElement, '`getInputElement` only work with `combobox` mode.'); // Customize `getInputElement` should not use `allowClear` & `placeholder`\n\n  noteOnce(mode !== 'combobox' || !getInputElement || !allowClear || !placeholder, 'Customize `getInputElement` should customize clear and placeholder logic instead of configuring `allowClear` and `placeholder`.'); // `onSearch` should use in `combobox` or `showSearch`\n\n  if (onSearch && !mergedShowSearch && mode !== 'combobox' && mode !== 'tags') {\n    warning(false, '`onSearch` should work with `showSearch` instead of use alone.');\n  }\n  noteOnce(!defaultOpen || autoFocus, '`defaultOpen` makes Select open without focus which means it will not close by click outside. You can set `autoFocus` if needed.');\n  if (value !== undefined && value !== null) {\n    var values = toArray(value);\n    warning(!labelInValue || values.every(function (val) {\n      return _typeof(val) === 'object' && ('key' in val || 'value' in val);\n    }), '`value` should in shape of `{ value: string | number, label?: ReactNode }` when you set `labelInValue` to `true`');\n    warning(!multiple || Array.isArray(value), '`value` should be array when `mode` is `multiple` or `tags`');\n  } // Syntactic sugar should use correct children type\n\n  if (children) {\n    var invalidateChildType = null;\n    toNodeArray(children).some(function (node) {\n      if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n        return false;\n      }\n      var type = node.type;\n      if (type.isSelectOption) {\n        return false;\n      }\n      if (type.isSelectOptGroup) {\n        var allChildrenValid = toNodeArray(node.props.children).every(function (subNode) {\n          if (! /*#__PURE__*/React.isValidElement(subNode) || !node.type || subNode.type.isSelectOption) {\n            return true;\n          }\n          invalidateChildType = subNode.type;\n          return false;\n        });\n        if (allChildrenValid) {\n          return false;\n        }\n        return true;\n      }\n      invalidateChildType = type;\n      return true;\n    });\n    if (invalidateChildType) {\n      warning(false, \"`children` should be `Select.Option` or `Select.OptGroup` instead of `\".concat(invalidateChildType.displayName || invalidateChildType.name || invalidateChildType, \"`.\"));\n    }\n    warning(inputValue === undefined, '`inputValue` is deprecated, please use `searchValue` instead.');\n  }\n}\nexport default warningProps;", "map": {"version": 3, "names": ["_typeof", "React", "warning", "noteOnce", "toNodeArray", "convertChildrenToData", "toArray", "isMultiple", "warningProps", "props", "mode", "options", "children", "backfill", "allowClear", "placeholder", "getInputElement", "showSearch", "onSearch", "defaultOpen", "autoFocus", "labelInValue", "value", "inputValue", "optionLabelProp", "multiple", "mergedShowSearch", "undefined", "mergedOptions", "every", "opt", "disabled", "hasNumberValue", "some", "item", "key", "values", "val", "Array", "isArray", "invalidateChildType", "node", "isValidElement", "type", "isSelectOption", "isSelectOptGroup", "allChil<PERSON>n<PERSON><PERSON>", "subNode", "concat", "displayName", "name"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/utils/warningPropsUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport warning, { noteOnce } from \"rc-util/es/warning\";\nimport toNodeArray from \"rc-util/es/Children/toArray\";\nimport { convertChildrenToData } from './legacyUtil';\nimport { toArray } from './commonUtil';\nimport { isMultiple } from '../BaseSelect';\n\nfunction warningProps(props) {\n  var mode = props.mode,\n      options = props.options,\n      children = props.children,\n      backfill = props.backfill,\n      allowClear = props.allowClear,\n      placeholder = props.placeholder,\n      getInputElement = props.getInputElement,\n      showSearch = props.showSearch,\n      onSearch = props.onSearch,\n      defaultOpen = props.defaultOpen,\n      autoFocus = props.autoFocus,\n      labelInValue = props.labelInValue,\n      value = props.value,\n      inputValue = props.inputValue,\n      optionLabelProp = props.optionLabelProp;\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = showSearch !== undefined ? showSearch : multiple || mode === 'combobox';\n  var mergedOptions = options || convertChildrenToData(children); // `tags` should not set option as disabled\n\n  warning(mode !== 'tags' || mergedOptions.every(function (opt) {\n    return !opt.disabled;\n  }), 'Please avoid setting option to disabled in tags mode since user can always type text as tag.'); // `combobox` & `tags` should option be `string` type\n\n  if (mode === 'tags' || mode === 'combobox') {\n    var hasNumberValue = mergedOptions.some(function (item) {\n      if (item.options) {\n        return item.options.some(function (opt) {\n          return typeof ('value' in opt ? opt.value : opt.key) === 'number';\n        });\n      }\n\n      return typeof ('value' in item ? item.value : item.key) === 'number';\n    });\n    warning(!hasNumberValue, '`value` of Option should not use number type when `mode` is `tags` or `combobox`.');\n  } // `combobox` should not use `optionLabelProp`\n\n\n  warning(mode !== 'combobox' || !optionLabelProp, '`combobox` mode not support `optionLabelProp`. Please set `value` on Option directly.'); // Only `combobox` support `backfill`\n\n  warning(mode === 'combobox' || !backfill, '`backfill` only works with `combobox` mode.'); // Only `combobox` support `getInputElement`\n\n  warning(mode === 'combobox' || !getInputElement, '`getInputElement` only work with `combobox` mode.'); // Customize `getInputElement` should not use `allowClear` & `placeholder`\n\n  noteOnce(mode !== 'combobox' || !getInputElement || !allowClear || !placeholder, 'Customize `getInputElement` should customize clear and placeholder logic instead of configuring `allowClear` and `placeholder`.'); // `onSearch` should use in `combobox` or `showSearch`\n\n  if (onSearch && !mergedShowSearch && mode !== 'combobox' && mode !== 'tags') {\n    warning(false, '`onSearch` should work with `showSearch` instead of use alone.');\n  }\n\n  noteOnce(!defaultOpen || autoFocus, '`defaultOpen` makes Select open without focus which means it will not close by click outside. You can set `autoFocus` if needed.');\n\n  if (value !== undefined && value !== null) {\n    var values = toArray(value);\n    warning(!labelInValue || values.every(function (val) {\n      return _typeof(val) === 'object' && ('key' in val || 'value' in val);\n    }), '`value` should in shape of `{ value: string | number, label?: ReactNode }` when you set `labelInValue` to `true`');\n    warning(!multiple || Array.isArray(value), '`value` should be array when `mode` is `multiple` or `tags`');\n  } // Syntactic sugar should use correct children type\n\n\n  if (children) {\n    var invalidateChildType = null;\n    toNodeArray(children).some(function (node) {\n      if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n        return false;\n      }\n\n      var type = node.type;\n\n      if (type.isSelectOption) {\n        return false;\n      }\n\n      if (type.isSelectOptGroup) {\n        var allChildrenValid = toNodeArray(node.props.children).every(function (subNode) {\n          if (! /*#__PURE__*/React.isValidElement(subNode) || !node.type || subNode.type.isSelectOption) {\n            return true;\n          }\n\n          invalidateChildType = subNode.type;\n          return false;\n        });\n\n        if (allChildrenValid) {\n          return false;\n        }\n\n        return true;\n      }\n\n      invalidateChildType = type;\n      return true;\n    });\n\n    if (invalidateChildType) {\n      warning(false, \"`children` should be `Select.Option` or `Select.OptGroup` instead of `\".concat(invalidateChildType.displayName || invalidateChildType.name || invalidateChildType, \"`.\"));\n    }\n\n    warning(inputValue === undefined, '`inputValue` is deprecated, please use `searchValue` instead.');\n  }\n}\n\nexport default warningProps;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,IAAIC,QAAQ,QAAQ,oBAAoB;AACtD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,qBAAqB,QAAQ,cAAc;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,UAAU,QAAQ,eAAe;AAE1C,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACjBC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,eAAe,GAAGP,KAAK,CAACO,eAAe;IACvCC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,YAAY,GAAGZ,KAAK,CAACY,YAAY;IACjCC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,eAAe,GAAGf,KAAK,CAACe,eAAe;EAC3C,IAAIC,QAAQ,GAAGlB,UAAU,CAACG,IAAI,CAAC;EAC/B,IAAIgB,gBAAgB,GAAGT,UAAU,KAAKU,SAAS,GAAGV,UAAU,GAAGQ,QAAQ,IAAIf,IAAI,KAAK,UAAU;EAC9F,IAAIkB,aAAa,GAAGjB,OAAO,IAAIN,qBAAqB,CAACO,QAAQ,CAAC,CAAC,CAAC;;EAEhEV,OAAO,CAACQ,IAAI,KAAK,MAAM,IAAIkB,aAAa,CAACC,KAAK,CAAC,UAAUC,GAAG,EAAE;IAC5D,OAAO,CAACA,GAAG,CAACC,QAAQ;EACtB,CAAC,CAAC,EAAE,8FAA8F,CAAC,CAAC,CAAC;;EAErG,IAAIrB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;IAC1C,IAAIsB,cAAc,GAAGJ,aAAa,CAACK,IAAI,CAAC,UAAUC,IAAI,EAAE;MACtD,IAAIA,IAAI,CAACvB,OAAO,EAAE;QAChB,OAAOuB,IAAI,CAACvB,OAAO,CAACsB,IAAI,CAAC,UAAUH,GAAG,EAAE;UACtC,OAAO,QAAQ,OAAO,IAAIA,GAAG,GAAGA,GAAG,CAACR,KAAK,GAAGQ,GAAG,CAACK,GAAG,CAAC,KAAK,QAAQ;QACnE,CAAC,CAAC;MACJ;MAEA,OAAO,QAAQ,OAAO,IAAID,IAAI,GAAGA,IAAI,CAACZ,KAAK,GAAGY,IAAI,CAACC,GAAG,CAAC,KAAK,QAAQ;IACtE,CAAC,CAAC;IACFjC,OAAO,CAAC,CAAC8B,cAAc,EAAE,mFAAmF,CAAC;EAC/G,CAAC,CAAC;;EAGF9B,OAAO,CAACQ,IAAI,KAAK,UAAU,IAAI,CAACc,eAAe,EAAE,uFAAuF,CAAC,CAAC,CAAC;;EAE3ItB,OAAO,CAACQ,IAAI,KAAK,UAAU,IAAI,CAACG,QAAQ,EAAE,6CAA6C,CAAC,CAAC,CAAC;;EAE1FX,OAAO,CAACQ,IAAI,KAAK,UAAU,IAAI,CAACM,eAAe,EAAE,mDAAmD,CAAC,CAAC,CAAC;;EAEvGb,QAAQ,CAACO,IAAI,KAAK,UAAU,IAAI,CAACM,eAAe,IAAI,CAACF,UAAU,IAAI,CAACC,WAAW,EAAE,iIAAiI,CAAC,CAAC,CAAC;;EAErN,IAAIG,QAAQ,IAAI,CAACQ,gBAAgB,IAAIhB,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC3ER,OAAO,CAAC,KAAK,EAAE,gEAAgE,CAAC;EAClF;EAEAC,QAAQ,CAAC,CAACgB,WAAW,IAAIC,SAAS,EAAE,kIAAkI,CAAC;EAEvK,IAAIE,KAAK,KAAKK,SAAS,IAAIL,KAAK,KAAK,IAAI,EAAE;IACzC,IAAIc,MAAM,GAAG9B,OAAO,CAACgB,KAAK,CAAC;IAC3BpB,OAAO,CAAC,CAACmB,YAAY,IAAIe,MAAM,CAACP,KAAK,CAAC,UAAUQ,GAAG,EAAE;MACnD,OAAOrC,OAAO,CAACqC,GAAG,CAAC,KAAK,QAAQ,KAAK,KAAK,IAAIA,GAAG,IAAI,OAAO,IAAIA,GAAG,CAAC;IACtE,CAAC,CAAC,EAAE,kHAAkH,CAAC;IACvHnC,OAAO,CAAC,CAACuB,QAAQ,IAAIa,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE,6DAA6D,CAAC;EAC3G,CAAC,CAAC;;EAGF,IAAIV,QAAQ,EAAE;IACZ,IAAI4B,mBAAmB,GAAG,IAAI;IAC9BpC,WAAW,CAACQ,QAAQ,CAAC,CAACqB,IAAI,CAAC,UAAUQ,IAAI,EAAE;MACzC,IAAI,EAAE,aAAaxC,KAAK,CAACyC,cAAc,CAACD,IAAI,CAAC,IAAI,CAACA,IAAI,CAACE,IAAI,EAAE;QAC3D,OAAO,KAAK;MACd;MAEA,IAAIA,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEpB,IAAIA,IAAI,CAACC,cAAc,EAAE;QACvB,OAAO,KAAK;MACd;MAEA,IAAID,IAAI,CAACE,gBAAgB,EAAE;QACzB,IAAIC,gBAAgB,GAAG1C,WAAW,CAACqC,IAAI,CAAChC,KAAK,CAACG,QAAQ,CAAC,CAACiB,KAAK,CAAC,UAAUkB,OAAO,EAAE;UAC/E,IAAI,EAAE,aAAa9C,KAAK,CAACyC,cAAc,CAACK,OAAO,CAAC,IAAI,CAACN,IAAI,CAACE,IAAI,IAAII,OAAO,CAACJ,IAAI,CAACC,cAAc,EAAE;YAC7F,OAAO,IAAI;UACb;UAEAJ,mBAAmB,GAAGO,OAAO,CAACJ,IAAI;UAClC,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIG,gBAAgB,EAAE;UACpB,OAAO,KAAK;QACd;QAEA,OAAO,IAAI;MACb;MAEAN,mBAAmB,GAAGG,IAAI;MAC1B,OAAO,IAAI;IACb,CAAC,CAAC;IAEF,IAAIH,mBAAmB,EAAE;MACvBtC,OAAO,CAAC,KAAK,EAAE,wEAAwE,CAAC8C,MAAM,CAACR,mBAAmB,CAACS,WAAW,IAAIT,mBAAmB,CAACU,IAAI,IAAIV,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAC3L;IAEAtC,OAAO,CAACqB,UAAU,KAAKI,SAAS,EAAE,+DAA+D,CAAC;EACpG;AACF;AAEA,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}