{"core/audits/accessibility/accesskeys.js | description": {"message": "Med adgangsnøgler kan brugerne hurtigt fokusere på dele af siden. Hver nøgle skal være unik for at navigere korrekt. [Få flere oplysninger om adgangsnøgler](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-værdi<PERSON> er ikke unikke"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-væ<PERSON><PERSON> er unikke"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON> <PERSON>-`role` understøtter en bestemt delmængde af `aria-*`-attributter. Hvis der opstår uoverensstemmelser, g<PERSON><PERSON> `aria-*`-attributterne ugyldige. [Få flere oplysninger om, hvordan du matcher ARIA-attributter med deres roller](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-attributterne stemmer ikke overens med deres roller"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-attributterne stemmer overens med deres roller"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "<PERSON><PERSON> et element ikke har et tilgængeligt navn, giver skærmlæsere feltet et generisk navn, så det ikke kan anvendes af brugere, der får læst indhold op af skærmlæsere. [Få flere oplysninger om, hvordan du gør kommandoelementer mere tilgængelige](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementerne `button`, `link` og `menuitem` har ikke tilgængelige navne."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementerne `button`, `link` og `menuitem` har tilgængelige navne"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Hjælpeteknologier såsom skærmlæsere fungerer ikke optimalt, når `aria-hidden=\"true\"` er angivet for dokumentet `<body>`. [Få flere oplysninger om, hvordan `aria-hidden` påvirker dokumentets brødtekst](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` findes i dokumentet `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` findes ikke i dokumentet `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> elementer, der kan fokuseres på, i et `[aria-hidden=\"true\"]`-element forhindrer sådanne interaktive elementer i at være tilgængelige for brugere, der anvender hjælpeteknologier såsom skærmlæsere. [Få flere oplysninger om, hvordan `aria-hidden` p<PERSON>vir<PERSON> elementer, der kan fokuseres på](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]`-elementerne indeholder faldende elementer, der kan fokuseres på"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]`-elementerne indeholder ikke faldende elementer, der kan fokuseres på"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON><PERSON> et indtastningsfelt ikke har et tilgængeligt navn, giver skærmlæsere feltet et generisk navn, så det ikke kan anvendes af brugere, der får læst indhold op af skærmlæsere. [Få flere oplysninger om etiketter til indtastningsfelter](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA-indtastningsfelterne har ikke et tilgængeligt navn"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA-indtastningsfelterne har tilgængelige navne"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Hvis et element af typen \"meter\" ikke har et tilgængeligt navn, giver skærmlæsere det et generisk navn, så det ikke er nyttigt for brugere, der får læst indhold op af skærmlæsere. [<PERSON>, h<PERSON><PERSON> du navngiver `meter`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "\"ARIA `meter`\"-<PERSON><PERSON> har ikke tilgængelige navne."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "\"ARIA `meter`\"-<PERSON><PERSON> har tilgængelige navne"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "<PERSON><PERSON> et element med `progressbar` ikke har et tilgængeligt navn, giver skærmlæsere feltet et generisk navn, så det ikke kan anvendes af brugere, der får læst indhold op af skærmlæsere. [Få flere oplysninger om, hvordan du føjer etiketter til `progressbar`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "\"ARIA `progressbar`\"-<PERSON><PERSON> har ikke tilgængelige navne."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "\"ARIA `progressbar`\"-<PERSON><PERSON> har tilgængelige navne"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Nogle ARIA-roller har opnået attributter, der beskriver elementets tilstand for skærmlæsere. [Få flere oplysninger om roller og obligatoriske attributter](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-<PERSON><PERSON> har ikke alle de obligatoriske `[aria-*]`-attributter"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-element<PERSON> har alle obligatoriske `[aria-*]`-attributter"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Nogle overordnede ARIA-roller skal indeholde bestemte underordnede roller for at udføre deres tilsigtede hjælpefunktioner [Få flere oplysninger om roller og obligatoriske underordnede elementer](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementer med ARIA-rollen `[role]`, der kræver underordnede elementer med en bestemt `[role]`, mangler nogle eller alle disse påkrævede underordnede elementer."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementer med ARIA-rollen `[role]`, der kræver underordnede elementer med en bestemt `[role]`, har alle de påkrævede underordnede elementer."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Nogle underordnede ARIA-roller skal indgå i bestemte overordnede roller for at udføre deres tilsigtede hjælpefunktioner korrekt. [Få flere oplysninger om ARIA-roller og obligatoriske overordnede elementer](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-elementerne indgår ikke i deres påkrævede overordnede element"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-elementerne indgår i deres påkrævede overordnede element"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA-roller skal have gyldige værdier for at udføre deres tilsigtede hjælpefunktioner. [Få flere oplysninger om gyldige ARIA-roller](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-v<PERSON><PERSON><PERSON> er ikke gyldige"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-v<PERSON><PERSON><PERSON> er gyldige"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON><PERSON> et felt med en til/fra-kontakt ikke har et tilgængeligt navn, giver skærmlæsere feltet et generisk navn, så det ikke kan anvendes af brugere, der får læst indhold op af skærmlæsere. [Få flere oplysninger om felter med en til/fra-kontakt](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA-kontakterne har ikke tilgængelige navne"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA-kontakterne har tilgængelige navne"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "H<PERSON> et værktøjstip-element ikke har et tilgængelighedsnavn, giver skærmlæsere det et generisk navn, så det ikke er nyttigt for brugere, der får læst indhold op af skærmlæsere. [Se, h<PERSON><PERSON> du navngiver `tooltip`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "\"ARIA `tooltip`\"-<PERSON><PERSON> har ikke tilgængelige navne."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "\"ARIA `tooltip`\"-<PERSON><PERSON> har tilgængelige navne"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "<PERSON><PERSON> et element med `treeitem` ikke har et tilgængeligt navn, giver skærmlæsere feltet et generisk navn, så det ikke kan anvendes af brugere, der får læst indhold op af skærmlæsere. [Få flere oplysninger om etikettering af `treeitem`-elementer](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "\"ARIA `treeitem`\"-<PERSON><PERSON> har ikke tilgængelige navne."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "\"ARIA `treeitem`\"-<PERSON><PERSON> har tilgængelige navne"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Hjælpeteknologier som f.eks. skærmlæsere kan ikke fortolke ARIA-attributter med ugyldige værdier. [Få flere oplysninger om gyldige værdier for ARIA-attributter](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-attributterne har ikke gyldige værdier"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-attributterne har gyldige værdier"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Hjælpeteknologier som f.eks. skærmlæsere kan ikke fortolke ARIA-attributter med ugyldige navne. [Få flere oplysninger om gyldige ARIA-attributter](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-attributterne er ikke gyldige eller er stavet forkert"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-attributterne er gyldige og er stavet korrekt"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON>, der ikke bestod gennemgangen"}, "core/audits/accessibility/button-name.js | description": {"message": "<PERSON><PERSON> en knap ikke har et tilgængeligt navn, op<PERSON><PERSON><PERSON> skærmlæsere knappen som \"knap\", hvilket gør den ubrugelig for brugere, der anvender skærmlæsere. [Få flere oplysninger om, hvordan du gør knapperne mere tilgængelige](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> har ikke et tilgængeligt navn"}, "core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> har et tilgængeligt navn"}, "core/audits/accessibility/bypass.js | description": {"message": "Tastaturbrugere kan nemmere finde rundt på siden, når der tilføjes metoder til omgåelse af gentagelser. [Få flere oplysninger om omgåelsesblokke](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Siden indeholder hverken en heading, et skip link eller en landmark region"}, "core/audits/accessibility/bypass.js | title": {"message": "Siden inde<PERSON> en heading, et skip link eller en landmark region"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Tekst med lav kontrast er for mange brugere svær eller umulig at læse. [Få flere oplysninger om, hvordan du leverer tilstrækkelig farvekontrast](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Farverne i baggrunden og forgrunden har ikke nok kontrastforhold."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Farverne i baggrunden og forgrunden har nok kontrastforhold"}, "core/audits/accessibility/definition-list.js | description": {"message": "<PERSON><PERSON> lister over definitioner ikke opmærkes korrekt, kan skærmlæseres oplæsning være forvirrende og forkert. [Få flere oplysninger om, hvordan du strukturerer definitionslister korrekt](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-elementerne indeholder ikke udelukkende korrekt organiserede `<dt>`- og `<dd>`-grupper, `<script>`-, `<template>`- eller `<div>`-elementer."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`-element<PERSON> indeholder kun korrekt organiserede `<dt>`- og `<dd>`-grupper, `<script>`-, `<template>`- eller `<div>`-elementer."}, "core/audits/accessibility/dlitem.js | description": {"message": "Elementer med lister over definitioner (`<dt>` og `<dd>`) skal indkapsles af et overordnet `<dl>`-element for at sikre, at skærmlæsere kan læse dem op korrekt. [Få flere oplysninger om, hvordan du strukturerer definitionslister korrekt](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementer med lister over definitioner er ikke indkapslet af `<dl>`-elementer"}, "core/audits/accessibility/dlitem.js | title": {"message": "Elementer med lister over definitioner er indkapslet af `<dl>`-elementer"}, "core/audits/accessibility/document-title.js | description": {"message": "Titlen giver brugere af skærmlæsere et overblik over siden, og brugere af søgemaskiner skal bruge den til at afgøre, om en side er relevant for deres søgning. [Få flere oplysninger om dokumenttitler](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumentet har ikke et `<title>`-element"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokumentet har et `<title>`-element"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "<PERSON><PERSON> element<PERSON>, der kan fokuseres på, skal have en unik `id` for at sikre, at de er synlige for hjælpeteknologier. [Få flere oplysninger om, hvordan du løser problemer med identiske `id`'er](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]`-attributterne på aktive elementer, der kan fokuseres på, er ikke unikke"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]`-attributterne på aktive elementer, der kan fokuseres på, er unikke"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Værdien af et ARIA-id skal være unik for at forhindre, at andre forekomster bliver overset af hjælpeteknologier. [Få flere oplysninger om, hvordan du løser problemer med identiske ARIA-id'er](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA-id'erne er ikke unikke"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA-id'erne er unikke"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Formularfelter med flere etiketter kan blive forvekslet og læst op af hjælpeteknologier såsom skærmlæsere, der anvender den første, den sidste eller alle etiketter. [Få flere oplysninger om, hvordan du bruger formularetiketter](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Formularfelterne har flere etiketter"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Der er ingen formularfelter med flere etiketter"}, "core/audits/accessibility/frame-title.js | description": {"message": "Brugere af skærmlæsere har brug for skærmtitler, der beskriver indholdet på skærmen. [Få flere oplysninger om skærmtitler](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- eller `<iframe>`-element<PERSON> har ikke en titel"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- eller `<iframe>`-element<PERSON> har en titel"}, "core/audits/accessibility/heading-order.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, der er sorteret korrekt og ikke springer niveauer over, geng<PERSON> sidens semantiske struktur, så den er nemmere at navigere i og forstå, når du anvender hjælpeteknologier. [Få flere oplysninger om rækkefølgen for overskrifter](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Overskriftselementerne vises ikke i en fortløbende faldende rækkefølge"}, "core/audits/accessibility/heading-order.js | title": {"message": "Overskriftselementerne vises i en fortløbende faldende rækkefølge"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "<PERSON>vis siden ikke angiver en `lang`-attribut, antager en skærmlæser, at siden vises på det standardsprog, som brugeren valgte ved konfigurationen af sin skærmlæser. Hvis siden ikke vises på standardsproget, oplæser skærmlæseren muligvis ikke teksten på siden korrekt. [Få flere oplysninger om attributten `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-elementet har ikke en `[lang]`-attribut"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-elementet har en `[lang]`-attribut"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> skærmlæsere med at oplæse tekst korrekt ved at angive et gyldigt [BCP 47-sprog](https://www.w3.org/International/questions/qa-choosing-language-tags#question). [<PERSON>å flere oplysninger om, hvor<PERSON> du bruger attributten `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-elementet har ikke en gyldig værdi for sin `[lang]`-attribut."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>`-element<PERSON> har en gyldig værdi for `[lang]`-attributten"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informative elementer bør anvende en kort, beskrivende alternativ tekst. Dekorative elementer kan ignoreres med en tom alt-attribut. [Få flere oplysninger om attributten `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> har ikke `[alt]`-attributter"}, "core/audits/accessibility/image-alt.js | title": {"message": "Bill<PERSON><PERSON>erne indeholder `[alt]`-attributter"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> et billede bruges som en `<input>`-knap, kan alternativ tekst hjælpe brugere af skærmlæsere med at forstå knappens formål. [Få flere oplysninger om alternativ tekst for inputbillede](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-element<PERSON> har ikke `[alt]`-tekst"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-element<PERSON> har `[alt]`-tekst"}, "core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> sikrer, at formularstyring oplæses korrekt af hjælpeteknologier som f.eks. skærmlæsere. [Få flere oplysninger om formularelementetiketter](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Formularelementerne har ikke tilknyttede etiketter"}, "core/audits/accessibility/label.js | title": {"message": "Formularelementerne har tilknyttede etiketter"}, "core/audits/accessibility/link-name.js | description": {"message": "Linktekst (og alternativ tekst til billeder, når de bruges som links), der er skelnelig, unik og fokuserbar, gør det nemmere for brugere af skærmlæsere at finde rundt. [Få flere oplysninger om, hvordan du gør links tilgængelige](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Linkene har ikke skelnelige navne"}, "core/audits/accessibility/link-name.js | title": {"message": "Linkene har skelnelige navne"}, "core/audits/accessibility/list.js | description": {"message": "Skærmlæsere oplæser lister på en bestemt måde. Du kan forbedre skærmlæsernes output ved at angive en ordentlig listestruktur. [Få flere oplysninger om korrekt listestruktur](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Listerne indeholder ikke kun `<li>`-elementer og elementer, der understøtter scripts (`<script>` og `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Listerne indeholder kun `<li>`-elementer og elementer, der understøtter scripts (`<script>` og `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Skærmlæsere kræver, at listeelementer (`<li>`) indgår i et overordnet `<ul>`-, `<ol>`- eller `<menu>`-element for at blive oplæst korrekt. [Få flere oplysninger om korrekt listestruktur](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Listeelementerne (`<li>`) indgår ikke i de overordnede `<ul>`-, `<ol>`- eller `<menu>`-elementer."}, "core/audits/accessibility/listitem.js | title": {"message": "Listeelementerne (`<li>`) indgår i de overordnede `<ul>`-, `<ol>`- eller `<menu>`-elementer"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Brugere forventer ikke, at en side opdateres automatisk, og automatisk opdatering flytter fokus tilbage til toppen af siden. Dette kan være frustrerende og forvirrende for brugerne. [Få flere oplysninger om metatagget for opdatering](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumentet bruger `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumentet bruger ikke `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "<PERSON><PERSON> zoom de<PERSON><PERSON><PERSON>, kan det skabe problemer for svagtseende brugere, der har brug for skærmforstørrelse til at se indholdet på en webside. [Få flere oplysninger om viewport-metatagget](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` anvendes i elementet `<meta name=\"viewport\">`, eller attributten `[maximum-scale]` er mindre end 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` anvendes ikke i `<meta name=\"viewport\">`-elementet, og attributten `[maximum-scale]` er ikke mindre end 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Skærmlæsere kan ikke oversætte indhold, som ikke er tekst Du kan føje alternativ tekst til `<object>`-elementer for at hjælpe skærmlæsere med at formidle meningen til brugerne. [Få flere oplysninger om alternativ tekst for `object`-elementer](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-<PERSON><PERSON> har ikke alternativ tekst"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-elementerne har alternativ tekst"}, "core/audits/accessibility/tabindex.js | description": {"message": "En værdi over 0 antyder en utvetydig sortering af navigation. Selvom dette teknisk er gyldigt, skaber det ofte en frustrerende oplevelse for brugere, der anvender hjælpeteknologier. [Få flere oplysninger om attributten `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Nogle elementer har en `[tabindex]`-v<PERSON><PERSON>, som er større end 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Ingen af elementerne har en `[tabindex]`-<PERSON><PERSON><PERSON>, der overstiger 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Skæ<PERSON><PERSON>æsere har funktioner, der gør det nemmere at finde rundt i tabeller. Du kan give brugere af skærmlæsere en bedre oplevelse ved at sikre, at `<td>`-celler, der anvender attributten `[headers]`, kun henviser til andre celler i samme tabel. [Få flere oplysninger om attributten `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Celler i et `<table>`-element, der anvender attributten `[headers]`, henviser til et element `id`, som ikke findes i samme tabel."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Celler i et `<table>`-element, der anvender attributten `[headers]`, henviser til tabelceller i den samme tabel."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Skærmlæsere har funktioner, der gør det nemmere at finde rundt i tabeller. Du kan give brugere af skærmlæsere en bedre oplevelse ved at sikre, at tabeloverskrifter altid henviser til nogle cellesæt. [Få flere oplysninger om tabeloverskrifter](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>`-element<PERSON> og elementerne med `[role=\"columnheader\"/\"rowheader\"]` indeholder ikke de dataceller, de beskriver."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>`-<PERSON><PERSON> og elementer med `[role=\"columnheader\"/\"rowheader\"]` indeholder de dataceller, de beskriver."}, "core/audits/accessibility/valid-lang.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>p med at sikre, at tekst udtales korrekt af skærmlæsere, ved at angive et gyldigt [BCP 47-sprog](https://www.w3.org/International/questions/qa-choosing-language-tags#question) for elementer. [Få flere oplysninger om, hvordan du bruger attributten `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-attributterne har ikke en gyldig værdi"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-attributterne har en gyldig værdi"}, "core/audits/accessibility/video-caption.js | description": {"message": "Det er nemmere for døve og hørehæmmede at få adgang til en videos oplysninger, hvis videoen tilbyder undertekster. [Få flere oplysninger om videoundertekster](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>`-elementerne indeholder ikke et `<track>`-element med `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>`-elementerne indeholder et `<track>`-element med `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Aktuel værdi"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Foreslået token"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` hj<PERSON><PERSON><PERSON> brugere med at indsende formularer hurtigere. Du kan hjælpe brugerne ved at indstille attributten `autocomplete` til en gyldig værdi. [Få flere oplysninger om `autocomplete` i formularer](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>`-elementer har ikke korrekte attributter for `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON><PERSON><PERSON> man<PERSON> g<PERSON>g"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Gennemgå rækkefølgen på tokens"}, "core/audits/autocomplete.js | title": {"message": "`<input>`-elementer anvender `autocomplete` kor<PERSON>t."}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete`-tokens: \"{token}\" er ugyldig i {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Gennemgå rækkefølgen på tokens: \"{tokens}\" i {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Handlingsrettet"}, "core/audits/bf-cache.js | description": {"message": "Mange navigationer udf<PERSON><PERSON> ved at gå tilbage til en tidligere side eller gå frem igen. Back/forward-cachen (bfcache) kan gøre disse returnavigationer hurtigere. [Få flere oplysninger om bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 årsag til fejl}one{# årsag til fejl}other{# årsager til fejl}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Årsag til fejl"}, "core/audits/bf-cache.js | failureTitle": {"message": "<PERSON>n forhindrede gendannelse af back/forward-cache"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Fejltype"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Ikke handlingsrettet"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Afventer browserunderstøttelse"}, "core/audits/bf-cache.js | title": {"message": "<PERSON>n for<PERSON>drede ikke gendannelse af back/forward-cachen"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome-udvidelser påvirkede denne sides indlæsning negativt. Prøv at revidere siden i inkognitotilstand eller fra en Chrome-profil uden udvidelser."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Scriptparsing"}, "core/audits/bootup-time.js | columnTotal": {"message": "Samlet CPU-tid"}, "core/audits/bootup-time.js | description": {"message": "Overvej at reducere den tid, der bruges på at parse, kompilere og udføre JavaScript. Levering af mindre JavaScript-datapakker kan hjælpe med dette. [Få flere oplysninger om, hvordan du reducerer JavaScript-udførelsestiden](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Reducer udførelsestiden for JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Udførelsestid for JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Fjern store, identiske JavaScript-moduler <PERSON><PERSON> for at reducere antallet af unødvendige bytes, der anvendes i forbindelse med netværksaktivitet. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Fjern identiske moduler i JavaScript-pakker"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Store giffer er mindre effektive til at levere animeret indhold. Du kan også overveje at bruge MPEG4-/WebM-videoer til animationer og PNG/WebP til statiske billeder i stedet for giffer for at spare netværksbytes. [Få flere oplysninger om effektive videoformater](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Brug videoformater til animeret indhold"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill-koder og transformeringer gør det muligt for browsere at anvende nye JavaScript-funktioner. Mange af dem er dog ikke nødvendige til moderne browsere. Hvis du har JavaScript-pakker, skal du bruge en strategi til implementering af moderne scripts, der anvender registrering af funktioner med eller uden moduler til at reducere mængden af kode, som sendes til moderne browsere, men som samtidig bevarer understøttelsen af forældede browsere. [Få flere oplysninger om, hvordan du bruger moderne JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Undgå at vise forældet JavaScript i moderne browsere"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Billedformater som f.eks. WebP og AVIF giver ofte en bedre komprimering end PNG og JPEG, hvilket betyder hurtigere downloads og mindre dataforbrug. [Få flere oplysninger om moderne billedformater](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Vis billeder i formater af næste generation"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Overvej at bruge lazy loading til billeder, der ikke er på skærmen, eller som er skjulte, når alle kritiske ressourcer er indlæst for at reducere den tiden inden interaktiv tilstand. [Få flere oplysninger om, hvordan du udskyder indlæsning af billeder, der ikke er på skærmen](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>, der ikke er på skærmen"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ressourcer blokerer for første visning af din side. Overvej at levere kritisk JavaScript/CSS indlejret og udskyde alle ikke-kritiske JavaScript-elementer/typografier. [Få flere oplysninger om, hvordan du fjerner gengivelsesblokerende ressourcer](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "<PERSON><PERSON><PERSON> ressourcer til blokering af gengivelse"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Store datapakker på netværk koster brugerne mange penge og er forbundet med lang indlæsningstid. [Få flere oplysninger om, hvordan du reducerer størrelsen på datapakker](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Den samlede størrelse var {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Undgå kæmpe datapakker på netværk"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Undgår kæmpe datapakker på netværk"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Formindskelse af CSS-filer kan <PERSON>re stø<PERSON> på datapakker på netværk. [Få flere oplysninger om, hvordan du formindsker CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Formindsk CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Formindskelse af JavaScript-filer kan reducere stø<PERSON>sen på datapakker og varigheden af scriptparsing. [Få flere oplysninger om, hvordan du formindsker JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Formindsk JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Reducer antallet af regler fra typografiark, som ikke bruges, og udskyd CSS, som ikke bruges til indhold over skillelinjen, for at skære ned på unødvendigt forbrug af bytes i forbindelse med netværksaktivitet. [Få flere oplysninger om, hvordan du reducerer ubrugt CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reducer antallet af CSS, som ikke bruges"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reducer antallet af JavaScript, som ikke bruges, og udskyd indlæsningen af scripts, indtil de er nødvendige, for at skære ned på unødvendigt forbrug af bytes i forbindelse med netværksaktivitet. [Få flere oplysninger om, hvordan du reducerer ubrugt JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reducer antallet af JavaScript, som ikke bruges"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "En lang cachelevetid kan gøre indlæsningen hurtigere for tilbagevendende besøgende på din side. [Få flere oplysninger om effektive cachepolitikker](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Der blev fundet 1 ressource}one{Der blev fundet # ressource}other{Der blev fundet # ressourcer}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Vis statiske aktiver med en effektiv cachepolitik"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Anvender effektiv cachepolitik på statiske aktiver"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimerede billeder indlæses hurtigere og bruger mindre mobildata. [Få flere oplysninger om, hvordan du indkoder billeder effektivt](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> effektivt"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Faktiske mål"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> var større end deres viste størrelse"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "<PERSON><PERSON><PERSON> passede til deres viste størrelse"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Vis billeder i korrekte størrelser for at spare mobildata og forbedre indlæsningstiden. [Få flere oplysninger om, hvordan du justerer størrelsen på billeder](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Brug korrekte billedstørrelser"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstbaserede ressourcer bør vises i komprimeret format (gzip, Deflate eller Brotli), så netværkets samlede antal bytes formindskes. [Få flere oplysninger om tekstkomprimering](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Aktiv<PERSON>r <PERSON>"}, "core/audits/content-width.js | description": {"message": "<PERSON><PERSON> bredden på indholdet i din app ikke stemmer overens med bredden på din visning, bliver din app muligvis ikke optimeret til mobilskærme. [Få flere oplysninger om, hvordan du tilpasser indholdets størrelse til det synlige område](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Visningens størrelse på {innerWidth} px stemmer ikke overens med vinduets størrelse på {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "Indholdet har ikke den rigtige størrelse til visningen"}, "core/audits/content-width.js | title": {"message": "Indholdet har den rigtige størrelse til visningen"}, "core/audits/critical-request-chains.js | description": {"message": "Kæderne med kritiske anmodninger nedenfor viser dig, hvilke ressourcer der indlæses med høj prioritet. Overvej at reducere kædernes længde, så ressourcernes downloadstørrelse bliver mindre, eller at udskyde download af unødvendige ressourcer, så sideindlæsningen forbedres. [Få oplysninger om, hvordan du undgår at sammenkæde kritiske anmodninger](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Der blev fundet 1 kæde}one{Der blev fundet # kæde}other{Der blev fundet # kæder}}"}, "core/audits/critical-request-chains.js | title": {"message": "Undgå at kædeforbinde kritiske anmodninger"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiv"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON>fang"}, "core/audits/csp-xss.js | description": {"message": "En stærk indholdssikkerhedspolitik (CSP, Content Security Policy) mindsker risikoen for angreb med scripts på tværs af websteder (XSS, cross-site scripting) betragteligt. [Få flere oplysninger om, hvordan du bruger en CSP (Content Security Policy) til at forhindre XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntaks"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Siden indeholder en CSP, der er defineret i et <meta>-tag. Overvej at flytte CSP'en til en HTTP-header eller definere en anden streng CSP i en HTTP-header."}, "core/audits/csp-xss.js | noCsp": {"message": "Der blev ikke fundet nogen CSP i håndhævelsestilstand"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON> for, at CSP'en beskytter effektivt mod XSS-angreb"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Udfasning/advarsel"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Udfasede API'er fjernes med tiden fra browseren. [Få flere oplysninger om udfasede API'er](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 advarsel blev fundet}one{# advarsel blev fundet}other{# advarsler blev fundet}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Bruger udfasede API'er"}, "core/audits/deprecations.js | title": {"message": "Undgår udfasede API'er"}, "core/audits/dobetterweb/charset.js | description": {"message": "Der skal angives en erklæring om tegnkodning. Det kan gøres med et `<meta>`-tag i de første 1024 bytes i HTML-koden eller i HTTP-svarheaderen for Content-Type. [Få flere oplysninger om erklæring af tegnkodning](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Erklæringen om charset mangler eller vises for sent i HTML-koden"}, "core/audits/dobetterweb/charset.js | title": {"message": "Charset er angivet korrekt"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Når der angives en dokumenttype, forhindres browseren i at skifte til quirks-tilstand. [Få flere oplysninger om erklæringen vedrørende dokumenttype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Navnet på dokumenttypen skal være strengen `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokumentet indeholder en `doctype`, der aktiverer `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumentet skal indeholde en doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "En tom streng var forventet for publicId"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "En tom streng var forventet for systemId"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokumentet indeholder en `doctype`, der aktiverer `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Siden mangler dokumenttypen HTML og aktiverer derfor quirks-tilstand"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Siden har dokumenttypen HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistik"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "En stor DOM øger hukommelsesforbruget, medfører længerevarende [beregninger af typografi](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) og resulterer i dyre [omformateringer af layout](https://developers.google.com/speed/articles/reflow). [Få oplysninger om, hvordan du undgår en for stor DOM-størrelse](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}one{# element}other{# elementer}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Undgå en overdreven DOM-størrelse"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimal DOM-dybde"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Samlet antal DOM-elementer"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Højeste antal underordnede elementer"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Undgår en overdreven DOM-størrelse"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Brugere er mistænksomme over for eller forvirres af websites, der anmoder om deres lokation uden sammenhæng. Overvej at knytte anmodningen til en brugerhandling i stedet for. [Få flere oplysninger om tilladelsen for geoplacering](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Anmoder om tilladelse til geoplacering ved indlæsning af siden"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Undgår at anmode om tilladelse til geoplacering ved indlæsning af siden"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Problemtype"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Der er registreret uløste problemer i panelet `Issues` i Udviklerværktøjer til Chrome De kan stamme fra mislykkede netværksanmodninger, utilstrækkelige sikkerhedsfunktioner og andre browserproblemer. Åbn panelet Issues i Udviklerværktøjer til Chrome for at få flere oplysninger om hvert problem."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Der er registreret problemer i panelet `Issues` i Udviklerværktøjer til Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blokeret af politik for krydsoprindelse"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Kraftig ressourceforbrug af annoncer"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Der er ikke registreret nogen problemer i panelet `Issues` i Udviklerværktøjer til Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Version"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Alle JavaScript-biblioteker i frontend, der registreres på siden. [Få flere oplysninger om denne revision af JavaScript-bibliotekets registrering af diagnostik](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Registrerede JavaScript-biblioteker"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Eksterne scripts, der indsættes dynamisk via `document.write()`, kan forsinke sideindlæsningen med flere sekunder for brugere med langsomme forbindelser. [Få flere oplysninger om, hvordan du undgår document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Undgå `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Undgår `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Brugere er mistænksomme over for eller forvirres af websites, der anmoder om at sende notifikationer uden sammenhæng. Overvej at knytte anmodningen til brugerbevægelser i stedet for. [Få flere oplysninger om ansvarlig indhentning af tilladelse til notifikationer](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Anmoder om tilladelse til notifikationer ved indlæsning af siden"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Undgår at anmode om tilladelse til notifikationer ved indlæsning af siden"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 tilbyder mange fordele i forhold til HTTP/1.1, herunder binære headers og multiplexing. [Få flere oplysninger om HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 anmodning blev ikke leveret via HTTP/2}one{# anmodning blev ikke leveret via HTTP/2}other{# anmodninger blev ikke leveret via HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Brug HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Overvej at markere hændelsesfunktionerne for tryk og hjul som `passive` for at forbedre effektiviteten ved rulning på siden. [Få flere oplysninger om implementering af passive hændelsesfunktioner](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Anvender ikke passive hændelsesfunktioner til at forbedre rulning"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Anvender passive hændelsesfunktioner til at forbedre rulning"}, "core/audits/errors-in-console.js | description": {"message": "<PERSON><PERSON><PERSON>, der er logført i konsollen, angiver ul<PERSON><PERSON> problemer. De kan stamme fra mislykkede netværksanmodninger og andre browserproblemer. [Få flere oplysninger om disse fejl i revisionen af konsoldiagnostik](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Der blev logført browserfejl i konsollen"}, "core/audits/errors-in-console.js | title": {"message": "Der blev ikke logført nogen browserfejl i konsollen"}, "core/audits/font-display.js | description": {"message": "Brug `font-display` CSS-funktionen til at sikre, at teksten kan ses af brugerne, mens der indlæses webfonts. [Få flere oplysninger om `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> for, at tekst forbliver synlig under indlæsning af webfont"}, "core/audits/font-display.js | title": {"message": "Al tekst forbliver synlig under indlæsning af webfont"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse kunne ikke automatisk tjekke `font-display`-værdien for det oprindelige website {fontOrigin}.}one{Lighthouse kunne ikke automatisk tjekke `font-display`-værdien for det oprindelige website {fontOrigin}.}other{Lighthouse kunne ikke automatisk tjekke `font-display`-værdierne for det oprindelige website {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Billedformat (faktisk)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Billedformat (vist)"}, "core/audits/image-aspect-ratio.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> for billedvisningen bør matche det naturlige billedformat. [Få flere oplysninger om billedformater](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Viser billeder med forkert billedformat"}, "core/audits/image-aspect-ratio.js | title": {"message": "Viser billeder med korrekt billedformat"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Faktisk størrelse"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON> st<PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Forventet størrelse"}, "core/audits/image-size-responsive.js | description": {"message": "For at gøre billedet så tydeligt som muligt bør billedets naturlige mål være proportionelt med både skærmens størrelse og pixelratioen. [Få flere oplysninger om, hvordan du leverer responsive billeder](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Viser billeder med en lav opløsning"}, "core/audits/image-size-responsive.js | title": {"message": "Viser billeder med en passende opløsning"}, "core/audits/installable-manifest.js | already-installed": {"message": "Appen er allerede installeret"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Et påkrævet ikon blev ikke downloadet fra manifestet"}, "core/audits/installable-manifest.js | columnValue": {"message": "Årsag til fejl"}, "core/audits/installable-manifest.js | description": {"message": "Scripttjenesten er den teknologi, der gør det muligt for din app at bruge mange funktioner til progressive webapps, f.eks. offlinefunktioner, tilføjelse på startskærme og push-notifikationer. Med korrekt scripttjeneste og manifestimplementeringer kan browsere proaktivt bede brugere om at føje din app til deres startskærm, hvilket kan medføre større interaktion. [Få flere oplysninger om krav til installerbarhed for manifester](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 årsag}one{# årsag}other{# årsager}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifestets webadresse eller scripttjenesten opfylder ikke kravene til installation"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play Butik-webadressen til appen og Play Butik-id'et stemmer ikke overens"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Siden er indlæst i et inkognitovindue"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Manifestets \"display\"-ejendom skal være enten \"standalone\", \"fullscreen\" eller \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifestet indeholder feltet \"display_override\", og den første understøttede skærmtilstand skal være enten \"standalone\", \"fullscreen\" eller \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifestet kunne ikke hentes, er tomt eller kunne ikke parses"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifestets webadresse blev ændret, mens manifestet blev hentet."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifestet indeholder ikke feltet \"name\" el<PERSON> \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifestet indeholder ikke et passende ikon. Der kræves PNG-, SVG- eller WebP-format med mindst {value0} px, attributten for størrelser skal angives, og attributten for form<PERSON><PERSON>, hvis denne er angivet, skal indeholde \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Der er ikke angivet et kvadratisk ikon på mindst {value0} px i PNG-, SVG- eller WebP-format, hvor attributten for formål ikke er angivet eller er angivet som \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Downloadikonet var tomt eller beskadiget"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Der er ikke angivet noget Play Butik-id"}, "core/audits/installable-manifest.js | no-manifest": {"message": "<PERSON>n har ikke nogen <link>-webadresse for manifestet"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Der blev ikke registreret nogen matchende scripttjeneste. Du skal muligvis genindlæse siden eller tjekke, at omfanget af scripttjenesten for den aktuelle side omslutter omfanget og startwebadressen fra manifestet."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Scripttjenesten kunne ikke tjek<PERSON>, da feltet \"start_url\" i manifestet ikke var angivet"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Installationsfejl-id'et \"{errorId}\" kan ikke genkendes"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Siden vises ikke fra et sikkert oprindelsessted"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Siden indlæses ikke i hovedrammen"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Siden fungerer ikke offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA'en er blevet afinstalleret, og installerbarhedstjek nulstilles."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Den angivne app-platform understøttes ikke i Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Følgende er angivet i manifestet: prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications understøttes kun i betaversionen af Chrome og stabile kanaler i Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse kunne ikke fastslå, om der var en scripttjeneste. Prøv med en nyere version af Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Manifestets webadresseskema ({scheme}) understøttes ikke i Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Manifestets startwebadresse er ikke gyldig"}, "core/audits/installable-manifest.js | title": {"message": "Manifestets webapp og scripttjenesten opfylder kravene til installation"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "En webadresse i manifestet indeholder et brugernavn, en adgangskode eller en port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Siden fungerer ikke offline. Siden anses ikke som installerbar efter udgivelsen af Chrome 93 i august 2021."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Blokeret"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON>nd<PERSON> af an<PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Alle websites bør beskyttes med HTTPS, selv websites, der ikke håndterer følsomme oplysninger. Du bør derfor undgå [blandet indhold](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), hvor nogle ressourcer indlæses via HTTP, også selvom den indledende anmodning vises via HTTPS. HTTPS forhindrer uvedkommende i at manipulere med eller passivt lytte med på kommunikationen mellem din app og dine brugere og er en forudsætning for HTTP/2 og mange nye webplatform-API'er. [Få flere oplysninger om HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 usikker anmodning blev fundet}one{# usikker anmodning blev fundet}other{# usikre anmodninger blev fundet}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Anvender ikke HTTPS"}, "core/audits/is-on-https.js | title": {"message": "<PERSON><PERSON>er HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatisk opgraderet til HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Tilladt med advarsel"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Dette element har den største udfyldning af indhold i det synlige område. [Få flere oplysninger om elementet med den største udfyldning af indhold](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element med størst udfyldning af indhold"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS-bidrag"}, "core/audits/layout-shift-elements.js | description": {"message": "Disse DOM-elementer bidrager mest til sidens CLS. [<PERSON>, hvordan du forbedrer CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Undgå store layoutskift"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "<PERSON><PERSON><PERSON> over <PERSON><PERSON><PERSON><PERSON>, der anvender lazy loading, geng<PERSON> senere på siden, hvilket kan forsinke den største udfyldning af indhold. [Få flere oplysninger om optimal lazy loading](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Den største udfyldning af billedindhold blev indlæst med lazy loading"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Den største udfyldning af billedindhold blev ikke indlæst med lazy loading"}, "core/audits/long-tasks.js | description": {"message": "Angiver lange opgaver i hovedtråden, hvilket er nyttigt til at identificere de bidragydere, der forårsager mest forsinkelse. [Få flere oplysninger om, hvordan du undgår lange opgaver i hovedtråden](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Der blev fundet # lang  opgave}one{Der blev fundet # lang  opgave}other{Der blev fundet # lange  opgaver}}"}, "core/audits/long-tasks.js | title": {"message": "Undgå lange opgaver i hovedtråden"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Overvej at reducere den tid, der bruges på at parse, kompilere og udføre JavaScript. Levering af mindre JavaScript-datapakker kan hjælpe med dette. [Få flere oplysninger om, hvordan du minimerer opgaver i hovedtråden](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Formindsk primært trådarbejde"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Formindsker primært trådarbejde"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Websites bør fungere på alle populære browsere, så de når ud til flest mulige brugere. [Få flere oplysninger om kompatibilitet på tværs af browsere](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Websitet fungerer i forskellige browsere"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON> for, at der kan føjes et dybt link til individuelle sider via en webadresse, og at denne webadresse er unik, så den kan deles på sociale medier. [Få flere oplysninger om tilføjelse af dybe links](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Hver side har en webadresse"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Overgange bør være hurtige, når der trykkes på forskellige elementer, også selvom netværket er langsomt. Det er afgørende for brugerens opfattelse af effektiviteten. [Få flere oplysninger om overgange på sider](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Det føles ikke som om sideovergange blokeres på netværket"}, "core/audits/maskable-icon.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON>, der kan maskeres, <PERSON><PERSON><PERSON>, at billedet fylder hele formen uden at indgå i letterbox-format, når appen installeres på en enhed. [Få flere oplysninger om manifestikoner, der kan maskeres](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifestet har ikke et ikon, der kan maskeres"}, "core/audits/maskable-icon.js | title": {"message": "Manifestet har et ikon, der kan maskeres"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Akkumuleret layoutskift måler synlige elementers bevægelse i det synlige område. [Få flere oplysninger om metric'en Akkumuleret layoutskift](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interaktion indtil næste visning måler sidens svartid, dvs. hvor lang tid det tager, før siden reagerer på brugerinput på en synlig måde. [Få flere oplysninger om metric'en for interaktion indtil næste visning](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON><PERSON>rste visning af indhold markerer tidspunktet, hvor den første tekst eller det første billede vises. [Få flere oplysninger om metric'en Første visning af indhold](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> meningsfulde visning måler, h<PERSON><PERSON><PERSON><PERSON> det primære indhold på en side kan ses. [Få flere oplysninger om metric'en Første meningsfulde visning](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Tid inden interaktiv tilstand er den mængde tid, det tager, før siden er helt interaktiv. [Få flere oplysninger om metric'en Tid inden interaktiv tilstand](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Største udfyldning af indhold angiver det tidspunkt, hvor den største tekst eller det største billede blev anvendt. [Få flere oplysninger om metric'en Største udfyldning af indhold](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Den maksimale potentielle ventetid efter første input, som dine brugere kan opleve, er varigheden af den længste proces. [Få flere oplysninger om metric'en for maksimal potentiel ventetid efter første input](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Speed Index viser, hvor hurtigt indholdet på en side er visuelt udfyldt. [Få flere oplysninger om metric'en Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Summen af alle tidsrum mellem første visning af indhold og tid inden interaktiv tilstand, når proceslængden overstiger 50 ms, udtrykt i millisekunder. [Få flere oplysninger om metric'en Total Blocking Time](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Netværkets pingtid har stor indflydelse på ydeevnen. <PERSON><PERSON> pingtiden til et oprindelsespunkt er lang, er det tegn på, at servere, der er tættere på brugeren, kan forbedre ydeevnen. [Få flere oplysninger om pingtid](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON> for netværk"}, "core/audits/network-server-latency.js | description": {"message": "Serverforsinkelser kan have indvirkning på websitets ydeevne. Hvis serverforsinkelsen for et oprindelsespunkt er høj, er det tegn på, at serveren er overbelastet, eller at backend-ydeevnen er dårlig. [Få flere oplysninger om serversvartid](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Forsinkelser for serverens backend"}, "core/audits/no-unload-listeners.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `unload` starter i<PERSON><PERSON> stabilt, og hvis der ventes på den, kan det forhindre browseroptimeringer som f.eks. Back-Forward Cache. Anven<PERSON> hænde<PERSON> `pagehide` eller `visibilitychange` i stedet. [Få flere oplysninger om hændelsesfunktioner for frigivelse](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registrerer en hændelsesfunktion for `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Undgå hændelsesfunktioner for `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "<PERSON><PERSON>, der ikke er sammensat, kan være langsomme og øge CLS. [Få oplysninger om, hvordan du undgår ikke-sammensatte animationer](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animeret element blev fundet}one{# animeret element blev fundet}other{# animerede elementer blev fundet}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Den \"filter\"-<PERSON><PERSON><PERSON> ejendom flytter muligvis pixels"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Den brugerangivne animation har en anden animation, der ikke er kompatibel"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Effekten har en sammensætningstilstand, der ikke er \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Undgå ikke-sammensatte animationer"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "En \"transform\"-<PERSON><PERSON> eje<PERSON>m afhænger af boksens størrelse"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Ikke-understøttet CSS-ejendom: {properties}}one{Ikke-understøttet CSS-ejendom: {properties}}other{Ikke-understøttede CSS-ejendomme: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "<PERSON>ffekt<PERSON> har timingparametre, der ikke understøttes"}, "core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON><PERSON> for, at antallet af og størrelsen på netværksanmodningerne ikke overskrider målene i det angivne budget for ydeevne. [Få flere oplysninger om budgetter for ydeevne](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 anmodning}one{# anmodning}other{# anmodninger}}"}, "core/audits/performance-budget.js | title": {"message": "Budget for ydeevne"}, "core/audits/preload-fonts.js | description": {"message": "Forudindlæs skrifttyper med `optional`, så førstegangsbesøgende kan bruge dem. [Få flere oplysninger om forudindlæsning af skrifttyper](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Skrifttyper med `font-display: optional` er ikke forudindlæst"}, "core/audits/preload-fonts.js | title": {"message": "Skrifttyper med `font-display: optional` er forudindlæst"}, "core/audits/prioritize-lcp-image.js | description": {"message": "<PERSON><PERSON> LCP-elementet føjes til siden dynamisk, skal du forudindlæse billedet for at forbedre LCP. [Få flere oplysninger om forudindlæsning af LCP-elementer](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Forudindlæs billedet med størst udfyldning af indhold"}, "core/audits/redirects.js | description": {"message": "Omdirigeringer medfører yderligere for<PERSON>, inden siden kan indlæses. [Få oplysninger om, hvordan du undgår sideomdirigeringer](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Undgå mange sideomdirigeringer"}, "core/audits/resource-summary.js | description": {"message": "Tilføj en budget.json-fil for at angive budgetter for antallet af og størrelsen på sideressourcer. [Få flere oplysninger om budgetter for ydeevne](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 anmodning • {byteCount, number, bytes} Ki<PERSON>}one{# anmodning • {byteCount, number, bytes} Ki<PERSON>}other{# anmodninger • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "<PERSON><PERSON><PERSON> for, at antallet af anmodninger er lavt, og at overførslerne ikke er for store"}, "core/audits/seo/canonical.js | description": {"message": "Kanoniske links fore<PERSON><PERSON><PERSON><PERSON>, hvilken webadresse der skal vises i søgeresultater. [Få flere oplysninger om kanoniske links](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON>e webadresser ({urlList}) er modstridende"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Ugyld<PERSON> webadresse ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "<PERSON><PERSON><PERSON> på en anden placering for `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Webadressen er ikke en absolut webadresse ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "<PERSON><PERSON><PERSON> på domænets rodwebadresse (startsiden) i stedet for en tilsvarende side med indhold"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumentet har ikke et gyldigt `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokumentet har et gyldigt `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON>, der ikke kan crawles"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Søgemaskiner kan anvende `href`-attributter i links til at crawle websites. <PERSON><PERSON><PERSON> for, at `href`-attributten for ankerelementer linker til en passende destination, så flere af websitets sider kan findes. [Få flere oplysninger om, hvordan du gør links klar til crawl](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON>s kan ikke crawles"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON>s kan crawles"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Yderligere ulæselig tekst"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Skriftstørrelse"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% af sideteksten"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Skriftstørrelser på mindre end 12 pixel er for små til at være læselige og kræver, at mobilbrugere \"kniber fingrene sammen for at zoome\" for at læse teksten. Du bør bestræbe dig på at gøre over 60 % af sideteksten større end eller lig med 12 pixel. [Få flere oplysninger om læselige skriftstørrelser](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} læselig tekst"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "<PERSON><PERSON>ten er ulæselig, fordi der ikke er et viewport-metatag, som er optimeret til mobilskærme."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumentet bruger ikke læselige skriftstørrelser"}, "core/audits/seo/font-size.js | legibleText": {"message": "Læselig tekst"}, "core/audits/seo/font-size.js | title": {"message": "Dokumentet anvender læselige skriftstørrelser"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang-links fortæller søgemaskiner, hvilken version af en side de skal angive på listen over søgeresultater for et vilkårligt sprog eller område. [Få flere oplysninger om `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumentet har ikke en gyldig `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativ href-værdi"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumentet har et gyldigt `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Uventet sprogkode"}, "core/audits/seo/http-status-code.js | description": {"message": "Sider med ugyldige HTTP-statuskoder indekseres muligvis ikke korrekt. [Få flere oplysninger om HTTP-statuskoder](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "HTTP-statuskoden for siden er ugyldig"}, "core/audits/seo/http-status-code.js | title": {"message": "HTTP-statuskoden for siden er gyldig"}, "core/audits/seo/is-crawlable.js | description": {"message": "Søgemaskiner kan ikke medtage dine sider i søgeresultater, hvis de ikke har tilladelse til at crawle dem. [Få flere oplysninger om direktiver for crawlere](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Siden er blokeret for indeksering"}, "core/audits/seo/is-crawlable.js | title": {"message": "Siden er ikke blokeret for indeksering"}, "core/audits/seo/link-text.js | description": {"message": "Beskrivende linktekst hjælper søgemaskiner med at forstå dit indhold. [Få flere oplysninger om, hvordan du gør links mere tilgængelige](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Der blev fundet 1 link}one{Der blev fundet # link}other{Der blev fundet # links}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Linkene har ikke beskrivende tekst"}, "core/audits/seo/link-text.js | title": {"message": "Linkene har beskrivende tekst"}, "core/audits/seo/manual/structured-data.js | description": {"message": "<PERSON><PERSON><PERSON> [testværktøjet til strukturerede data](https://search.google.com/structured-data/testing-tool/) og [Structured Data Linter](http://linter.structured-data.org/) for at validere strukturerede data. [Få flere oplysninger om strukturerede data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "De strukturerede data er gyldige"}, "core/audits/seo/meta-description.js | description": {"message": "Metabeskrivelser kan medtages i søgeresultater for kortfattet at opsummere sideindhold. [Få flere oplysninger om metabeskrivelsen](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Beskrivelsesteksten er tom."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumentet har ikke en metabeskrivelse"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumentet har en metabeskrivelse"}, "core/audits/seo/plugins.js | description": {"message": "Søgemaskiner kan ikke indeksere indhold i plugins, og mange enheder begrænser plugins eller understøtter dem ikke. [Få flere oplysninger om, hvordan du undgår plugins](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumentet bruger plugins"}, "core/audits/seo/plugins.js | title": {"message": "Dokumentet undgår plugins"}, "core/audits/seo/robots-txt.js | description": {"message": "Hvis din robots.txt-fil indeholder fejl, kan crawlere muligvis ikke forstå, hvordan du vil have dit website crawlet eller indekseret. [Få flere oplysninger om robots.txt.](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Anmodningen om robots.txt returnerede følgende HTTP-status: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Der blev fundet 1 fejl}one{Der blev fundet # fejl}other{Der blev fundet # fejl}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse kunne ikke downloade en robots.txt-fil"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt er ikke gyldig"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt er gyldig"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktive elementer som f.eks. knapper og links skal være store nok (48 x 48 px) og have tilstrækkelig plads omkring sig for at gøre det let at trykke på dem uden at overlappe andre elementer. [Få flere oplysninger om trykbare elementer](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} trykbare elementer med passende størrelse"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Trykbare elementer er for små, fordi der ikke er et viewport-metatag, som er optimeret til mobilskærme"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Trykbare elementer har ikke en passende størrelse"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "<PERSON>lappen<PERSON> mål"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Trykbart element"}, "core/audits/seo/tap-targets.js | title": {"message": "Trykbare elementer har en passende størrelse"}, "core/audits/server-response-time.js | description": {"message": "<PERSON><PERSON><PERSON> for at holde serversvartiden for hoveddokumentet kort, da alle andre anmodninger afhænger af den. [Få flere oplysninger om metric'en Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Roddokumentet tog {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reducer den indledende serversvartid"}, "core/audits/server-response-time.js | title": {"message": "Den indledende serversvartid var kort"}, "core/audits/service-worker.js | description": {"message": "Scripttjenesten er den teknologi, der gør det muligt for din app at bruge mange funktioner til progressive webapps, f.eks. offline, tilføjelse på startskærme og push-notifikationer. [Få flere oplysninger om scripttjenester](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Denne side styres af en scripttjeneste, men der blev ikke fundet noget `start_url`, fordi manifestet ikke kunne parse den som en gyldig JSON-fil"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Denne side styres af en scripttjeneste, men `start_url` ({startUrl}) er ikke omfattet af scripttjenesten ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Denne side styres af en scripttjeneste, men der blev ikke fundet nogen `start_url`, da der ikke blev hentet noget manifest."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Dette website har én eller flere scripttje<PERSON>ter, men siden ({pageUrl}) er ikke omfattet af disse."}, "core/audits/service-worker.js | failureTitle": {"message": "Registrerer ikke en scripttje<PERSON>, der styrer siden og `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registrerer en scripttjeneste, der styrer siden og `start_url`"}, "core/audits/splash-screen.js | description": {"message": "En splash-skærm med tema sikrer, at brugerne får en god oplevelse, når de starter din app på deres startskærm. [Få flere oplysninger om splash-skærme](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Websitet er ikke konfigureret til en tilpasset splash-skærm"}, "core/audits/splash-screen.js | title": {"message": "Konfigureret til en tilpasset splash-skærm"}, "core/audits/themed-omnibox.js | description": {"message": "Der kan angives et tema for browserens adresselinje, som matcher dit website. [Få flere oplysninger om tematisering af adresselinjen](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Angiver ikke en temafarve til adresselinjen."}, "core/audits/themed-omnibox.js | title": {"message": "Angiver en temafarve til adresselinjen."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (kundeservice)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkt"}, "core/audits/third-party-facades.js | description": {"message": "Nogle indlejringer fra tredjeparter kan indlæses efter behov. Overvej at udskifte dem med en facade, indtil de skal bruges. [Få oplysninger om, hvordan du udsætter tredjeparter med en facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# facadealternativ er tilgængeligt}one{# facadealternativ er tilgængeligt}other{# facadealternativer er tilgængelige}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Nogle tredjepartsressourcer kan indlæses efter behov med en facade"}, "core/audits/third-party-facades.js | title": {"message": "Ressourcer fra tredjeparter kan indlæses efter behov med facader"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Tredjepart"}, "core/audits/third-party-summary.js | description": {"message": "Kode fra tredjeparter kan have en væsentlig indvirkning på indlæsningen. Begræns antallet af overflødige tredjepartsudbydere, og prøv at indlæse kode fra tredjeparter, når indlæsningen af siden næsten er færdig. [Få flere oplysninger om, hvordan du minimerer indvirkning fra tredjeparter](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Tredjepartskode blokerede den primære tråd i {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reducer virkningen af tredjepartskode"}, "core/audits/third-party-summary.js | title": {"message": "<PERSON><PERSON> brugen af tredjeparter"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Metric"}, "core/audits/timing-budget.js | description": {"message": "Angiv et tidsbudget for at følge med i dit websites effektivitet. Effektive websites indlæser hurtigt og reagerer hurtigt på brugerindtastninger. [Få flere oplysninger om budgetter for ydeevne](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Tidsbudget"}, "core/audits/unsized-images.js | description": {"message": "Anvend en fast bredde og højde for billedelementer for at reducere layoutskift og forbedre CLS. [Få flere oplysninger om, hvordan du angiver billedmål](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> har ikke eksplicit `width` og `height`"}, "core/audits/unsized-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> har eksplicit `width` og `height`"}, "core/audits/user-timings.js | columnType": {"message": "Type"}, "core/audits/user-timings.js | description": {"message": "Du kan også vælge at bruge User Timing API som værktøj til din app for at måle appens ydeevne i den virkelige verden i forbindelse med vigtige brugeroplevelser. [Få flere oplysninger om User Timing-markeringer](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 brugstid}one{# brugstid}other{# brugstider}}"}, "core/audits/user-timings.js | title": {"message": "Brugstider markerer og måler"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Der blev fundet et `<link rel=preconnect>` for \"{securityO<PERSON>in}\", men det blev ikke anvendt af browseren. T<PERSON>k, at du bruger attributten `crossorigin` korrekt."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Overvej at tilføje ressourcehints til `preconnect` eller `dns-prefetch` for at oprette tidlige forbindelser til vigtige tredjepartswebsites. [Få flere oplysninger om, hvordan du opretter forbindelse på forhånd til obligatoriske oprindelser](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Opret forbindelse på forhånd til påkrævede websites"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Der blev fundet mere end 2 `<link rel=preconnect>` forbindelser. Disse bør bruges sparsomt og kun til de vigtigste kilder."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Der blev fundet et `<link rel=preconnect>` for \"{securityOrigin}\", men det blev ikke anvendt af browseren. Du bør kun bruge `preconnect` til vigtige kilder, som siden helt sikkert vil anmode om."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Der blev fundet et forudindlæst `<link>` for \"{preloadURL}\", men det blev ikke anvendt af browseren. Tjek, at du bruger attributten `crossorigin` korrekt."}, "core/audits/uses-rel-preload.js | description": {"message": "Overvej at bruge `<link rel=preload>` til at prioritere hentning af ressourcer, der er anmodet om senere i sideindlæsningen. [Få flere oplysninger om, hvordan du forudindlæser vigtige anmodninger](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vigtige an<PERSON>"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "<PERSON><PERSON><PERSON> webadresse"}, "core/audits/valid-source-maps.js | description": {"message": "Kildekort oversætter formindsket kode til den originale kildekode. Dette hjælper udviklere med at fejlrette under produktion. Derudover kan Lighthouse levere yderligere indsigter. Overvej at implementere kildekort for at drage fordel af disse. [Få flere oplysninger om kildekort](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Manglende kildekort til stor førsteparts-JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Stor JavaScript-fil mangler et kildekort"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Advarsel! 1 manglende element i `.sourcesContent`}one{Advarsel! # manglende element i `.sourcesContent`}other{Advarsel! # manglende elementer i `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "<PERSON>n har gyldige kildekort"}, "core/audits/viewport.js | description": {"message": "En `<meta name=\"viewport\">` optimerer ikke blot din app til mobilskærme, men den forhindrer også [en forsinkelse af brugerinput på 300 millisekunder](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Få flere oplysninger om brug af viewport-metatagget](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Der blev ikke fundet noget `<meta name=\"viewport\">`-tag"}, "core/audits/viewport.js | failureTitle": {"message": "Der ikke noget `<meta name=\"viewport\">`-tag med `width` eller `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Der er et `<meta name=\"viewport\">`-tag med `width` eller `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Dette er trådb<PERSON>keringopgaver, der sker under interaktionen indtil målingen af næste visning. [Få flere oplysninger om metric'en for interaktion indtil næste visning](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms blev brugt på hændelsen \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Hændelsesmål"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimer opgaver under vigtige interaktioner"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Inputforsinkelse"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Præsentationsforsinkelse"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Behandlingstid"}, "core/audits/work-during-interaction.js | title": {"message": "Minimerer opgaver under vigtige interaktioner"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Disse er muligheder for at forbedre brugen af ARIA i din app, hvilket kan forbedre oplevelsen for brugere af hjælpeteknologi, f.eks. skærmlæsere."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Disse er muligheder for at angive alternativt indhold for lyd og video. <PERSON><PERSON> kan for<PERSON><PERSON> op<PERSON> for brugere med nedsat hørelse eller syn."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON> <PERSON> video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Disse elementer fremhæver almindelige optimale løsninger for hjælpefunktioner."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Optimale løsninger"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Disse kontroller fremhæver muligheder for at [forbedre tilgængeligheden af din webapp](https://developer.chrome.com/docs/lighthouse/accessibility/). Det er kun visse tilgængelighedsproblemer, der kan registreres automatisk, og derfor anbefales det også at teste manuelt."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Disse elementer omhandler områder, som et automatisk testværktøj ikke kan dække. Få flere oplysninger ved at læse vores vejledning i, hvordan du [udfører en gennemgang af hjælpefunktioner](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Hjælpefunktioner"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Disse er muligheder for at forbedre forståelsen af dit indhold."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Disse er muligheder for at gøre det nemmere for brugere med forskellige landestandarder at forstå dit indhold."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisering og lokalisering"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Disse er muligheder for at forbedre semantikken i styringen af din app. De kan forbedre oplevelsen for brugere af hjælpeteknologi, f.eks. skærmlæsere."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Navne og etiketter"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Disse er muligheder for at forbedre tastaturnavigation i din app."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigation"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Disse er muligheder for at forbedre oplevelsen af oplæste tabel- eller listedata ved hjælp af hjælpeteknologi som f.eks. en skærmlæser."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> og lister"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Browserkompatibilitet"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Optimale løsninger"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Generelt"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "<PERSON>id og sikkerhed"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Brugeroplevelse"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Budgetter for ydeevne angiver standarder for dit websites ydeevne."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budgetter"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Få flere oplysninger om din apps ydeevne. Resultatet [påvirkes ikke direkte](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) af disse tal."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostik"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Det vigtigste aspekt af effektivitet er, hvor hurtigt pixels gengives på skærmen. Vigtige metrics: <PERSON><PERSON>rste udfyldning af indhold, <PERSON><PERSON><PERSON><PERSON> betydningsfulde udfyldning"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Forbedringer af første udfyldning"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Disse forslag kan være med til at forbedre indlæsningstiden for din side. De [berører ikke direkte](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) resultatet."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "Metrics"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "<PERSON><PERSON><PERSON> den overordnede indlæsning bedre, så siden hurtigst muligt bliver responsiv og klar til brug. Vigtige metrics: Tid inden interaktiv tilstand, Hastighedsindeks"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> forbed<PERSON>er"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Effektivitet"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Disse tjek validerer aspekterne i en progressiv webapp. [Se, hvad der gør en progressiv webapp god](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Disse tjek kræves af den grundlæggende [tjekliste til progressive webapps](https://web.dev/pwa-checklist/), men de udføres ikke automatisk af Lighthouse. De påvirker ikke dine resultater, men det er vigtigt, at du bekræfter dem manuelt."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Websitet kan installeres"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA-optimeret"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Disse tjek sikrer, at din side fø<PERSON><PERSON> de grundlæggende råd om søgemaskineoptimering. Der er mange yderligere faktorer, som Lighthouse ikke medregner her, der kan påvirke din søgerangering, bl.a. dine resultater i [Core Web Vitals](https://web.dev/learn-core-web-vitals/). [Få flere oplysninger om Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> disse yderligere valideringer på dit website for at tjekke andre optimale SEO-løsninger."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatér din HTML på en sådan måde, at den gør det lettere for crawlere at forstå indholdet i din app."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Optimale løsninger for indhold"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Hvis dit website skal vises i søgeresultater, skal crawlere have adgang til din app."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawl og indeksering"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON> for, at dine sider er mobilvenlige, så brugere ikke behøver at knibe fingrene sammen eller zoome ind for at se indholdet. [Få flere oplysninger om, hvordan du gør sider mobilvenlige](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobilvenlig"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Den testede enhed ser ud til at have en langsommere CPU, end Lighthouse forventer. Dette kan påvirke resultatet negativt. Få flere oplysninger om [kalibrering af en passende CPU-multiplikator for køkørsel](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON>n indlæses muligvis ikke som forventet, da din testwebadresse ({requested}) blev omdirigeret til {final}. <PERSON><PERSON><PERSON><PERSON> at teste den anden webadresse direkte."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "<PERSON>n blev indlæst for langsomt til at afslutte inden for tidsgrænsen. Resultaterne kan være ufuldstændige."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Rydningen af browserens cache oplevede timeout Prøv at gennemgå denne side igen, og rapportér en fejl, hvis problemet fortsætter."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Der kan være gemte data, som påvirker indlæsningseffektiviteten på følgende placering: {locations}. Gennemgå denne side i et inkognitovindue for at forhindre, at disse ressourcer påvirker dine resultater.}one{Der kan være gemte data, som påvirker indlæsningseffektiviteten på følgende placering: {locations}. Gennemgå denne side i et inkognitovindue for at forhindre, at disse ressourcer påvirker dine resultater.}other{Der kan være gemte data, som påvirker indlæsningseffektiviteten på følgende placeringer: {locations}. Gennemgå denne side i et inkognitovindue for at forhindre, at disse ressourcer påvirker dine resultater.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Rydningen af oprindelsesdata oplevede timeout. Prøv at gennemgå denne side igen, og rapportér en fejl, hvis problemet fortsætter."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Det er kun sider, der indlæses via en GET-anmodning, som er kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Det er kun sider med en 2XX-statuskode, som kan cachelagres."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome registrerede et forsøg på at køre JavaScript, mens siden var i cachen."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON>, der har anmodet om et AppBanner, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Back/forward-cachen er deaktiveret af demofunktioner. Gå til chrome://flags/#back-forward-cache for at aktivere den lokalt på denne enhed."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Back/forward-cachen er deaktiveret af kommandolinjen."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Back/forward-cachen er deaktiveret på grund af manglende hukommelse."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Back/forward-cachen understøttes ikke af delegeret."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Back/forward-cachen er deaktiveret for forhåndsgengivelsen."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "<PERSON>n kan ik<PERSON>, da den har en BroadcastChannel-forekomst med registrerede hændelsesfunktioner."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Sider med headeren cache-control:no-store kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Cachen blev ryddet bevidst."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Siden blev fjernet fra cachen, så en anden side kunne cachelagres."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "<PERSON><PERSON>, der indeholder plugins, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "<PERSON><PERSON>, der anvender FileChooser API, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "<PERSON><PERSON>, der anvender File System Access API, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON>, der anvender Media Device Dispatcher, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "En medieafspiller afspillede indhold, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "<PERSON><PERSON>, der anvender MediaSession API og angiver en afspilningstilstand, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "<PERSON><PERSON>, der anvender MediaSession API og angiver handlingshandlere, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Back/forward-cachen er deaktiveret pga. skærmlæseren."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON>, der <PERSON><PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "<PERSON><PERSON>, der anvender Serial API, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON>, der anvender WebAuthentication API, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "<PERSON><PERSON>, der anvender WebBluetooth API, er ikke kvalificeret til back/forward-cachen."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "<PERSON><PERSON>, der anvender WebUSB API, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON>, der anvender en dedikeret scripttjeneste eller worklet, ikke er kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Der blev navigeret væk, før dokumentet blev indlæst."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kørte, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome Adgangskodeadministrator kørte, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-destillation var i gang, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer kørte, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Back/forward-cachen er deaktiveret på grund af udvidelser, der anvender Messaging API."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Udvidelser med langvarig forbindelse skal afbryde forbindel<PERSON>, før de kan gemmes i back/forward-cachen."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Udvidelser med langvarig forbindelse forsøgte at sende meddelelser til rammer i back/forward-cachen."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Back/forward-cachen er deaktiveret på grund af udvidelser."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "En modal dialogboks som f.eks. dialogboksen for genindsendelse af en formular eller http-adgangskoder blev vist for siden, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Offlinesiden blev vist, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Bjælken til indgriben ved manglende hukommelse kørte, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Der var tilladelsesanmodninger, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Pop op-blokering var aktiv, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Der blev vist oply<PERSON>ning<PERSON> for Beskyttet browsing, da der blev navigeret væk."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Beskyttet browsing vurderede, at denne side udgør misbrug, og blokerede pop op-vinduet."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "En scripttjeneste blev aktiveret, mens siden var i back/forward-cachen."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Back/forward-cachen er blevet deaktiveret, fordi der er et problem med et dokument."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON>, der bruger <PERSON><PERSON>, kan ikke gemmes i back/forward-cachen."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Siden blev fjernet fra cachen, så en anden side kunne cachelagres."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON>, der har givet adgang til streaming af medier, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON>, der an<PERSON> portaler, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON>, der <PERSON><PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON>, der har en åben IndexedDB-forbindelse, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Der blev brugt ikke-kvalificerede API'er."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON>, <PERSON><PERSON> ud<PERSON><PERSON><PERSON> indsætter JavaScript, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON>, som udvidelser indsætter StyleSheet på, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Der opstod en intern fejl."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Back/forward-cachen er deaktiveret på grund af en keepalive-anmodning."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON>, der <PERSON><PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | loading": {"message": "Der blev navigeret væk fra siden, før den blev indlæst."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, hvis primære ressource har cache-control:no-cache, kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, hvis primære ressource har cache-control:no-store, kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigationen blev annuller<PERSON>, før siden kunne gendannes fra back/forward-cachen."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Siden blev fjernet fra cachen, fordi en aktiv netværksforbindelse modtog for mange data. Chrome begrænser den mængde af data, som en side kan modtage under cachelagring."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Sider med fetch() el<PERSON>, der behandles, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Siden blev fjernet fra back/forward-cachen, fordi en aktiv netværksanmodning vedrørte en omdirigering."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Siden blev fjernet fra cachen, fordi en netværksforbindelse var åben i for lang tid. Chrome begrænser, hvor længe en side kan modtage data under cachelagring."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON><PERSON>, der ikke har en gyldi<PERSON> s<PERSON>, kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigationen foregik i en anden ramme end den primære ramme."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Sider med løbende IndexedDB-transaktioner er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Sider med en netværksanmodning, der behandles, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Sider med en fetch()-net<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, som behandles, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Sider med en netværksanmodning, der behandles, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Sider med en XHR-netværksanmodning, som behandles, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON>, der an<PERSON> PaymentManager, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON>, der anvender Picture-in-Picture (integreret billede), er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON>, der an<PERSON> portaler, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON><PERSON>, der viser udskrivningsbrugerfladen, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Siden blev åbnet via `window.open()`, og en anden fane havde en henvisning til den, eller også åbnede siden et vindue."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Gengivelsesprocessen for siden i back/forward-cachen mislykkedes."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Gengivelsesprocessen for siden i back/forward-cachen blev afbrudt."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON>, der har anmodet om adgangstilladelse til lydoptagelser, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON>, der har anmodet om sensortillade<PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON>, der har anmodet om synkronisering i baggrunden eller fetch()-<PERSON><PERSON><PERSON><PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON>, der har anmodet om MIDI-till<PERSON><PERSON><PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON>, der har anmodet om notifikationstilladelser, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON>, der har anmodet om adgang til lagerpladsen, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON>, der har anmodet om tilladelser til optagelse af video, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Du kan kun cachelagre sider, hvis webadresseskema er HTTP/HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "En scripttjeneste anmodede om siden, da den var i back/forward-cachen."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "En scripttjeneste forsøgte at sende en `MessageEvent` til siden i back/forward-cachen."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Scripttjenesten var ikke registreret, da siden var i back/forward-cachen."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Siden blev fjernet fra back/forward-cachen, fordi en scripttjeneste blev aktiveret."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome genstartede og ryddede posterne i back/forward-cachen."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON>, der an<PERSON> Shared<PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON>, der an<PERSON>, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON>, der anvender SpeechSynthesis, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "En iframe på siden startede en navigation, der ikke blev gennemført."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, hvis underressource har cache-control:no-cache, kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, hvis underressource har cache-control:no-store, kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Siden var i back/forward-cachen i længere tid end den maksimalt tilladte varighed og fik timeout."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "<PERSON>n oplevede timeout under <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> til back/forward-cachen (sandsynligvis på grund af handlers til skjulning af sider, som havde kørt i lang tid)"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Siden har en frigivelseshandler i hovedrammen."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Siden har en frigivelseshandler i en underramme."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "B<PERSON>eren har ændret brugeragentens tilsidesættelsesheader."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON>, der har givet adgangtilladelse til optagelse af video eller lyd, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "<PERSON><PERSON>, der anvender WebDatabase, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webHID": {"message": "<PERSON><PERSON>, der anvender WebHID, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON><PERSON>, der <PERSON><PERSON> WebLocks, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "<PERSON><PERSON>, der an<PERSON> WebNfc, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "<PERSON><PERSON>, der anvender WebOTPService, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Sider med WebRTC kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | webShare": {"message": "<PERSON><PERSON>, der anvender WebShare, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Sider med WebSocket kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Sider med WebTransport kan ikke føjes til back/forward-cachen."}, "core/lib/bf-cache-strings.js | webXR": {"message": "<PERSON><PERSON>, der anvender WebXR, er ikke kvalificeret til back/forward-cachen i øjeblikket."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Overvej at tilføje webadresseformaterne https: og http: (ignoreres af browsere, der understøtter \"strict-dynamic\") for at gøre CSP'en bagudkompatibel med ældre browsere."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "\"disown-opener\" har været udfaset siden CSP3. Brug headeren \"Cross-Origin-Opener-Policy\" i stedet."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "\"referrer\" har været udfaset siden CSP2. Brug headeren \"Referrer-Policy\" i stedet."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "\"reflected-xss\" har været udfaset siden CSP2. Brug headeren \"X-XSS-Protection\" i stedet."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "En manglende basis-u<PERSON> <PERSON><PERSON> indsatte <base>-tags at angive basewebadressen for alle relative webadresser (f.eks. scripts) for et domæne, der styres af en hacker. Du bør overveje at angive basis-uri'en som \"none\" eller \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "<PERSON><PERSON> \"object-src\" mangler, kan der indsættes plugins, som udfører usikre scripts. <PERSON> bør overveje at indstille \"object-src\" til \"ingen\"."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "\"script-src\"-direkt<PERSON><PERSON> mangler. <PERSON>te kan muliggøre kørsel af usikre scripts."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Glemte du at tilføje et semikolon? {keyword} lader til at være et direktiv – ikke et søgeord."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "<PERSON>ces bør bruge \"base64\"-charsettet."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces skal bestå af mindst otte tegn."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Undgå brug af almindelige webadresseskemaer ({keyword}) i dette direktiv. Ved brug af almindelige webadresseskemaer kan der hentes scripts fra et usikkert domæne."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Undgå at bruge almindelige jokertegn ({keyword}) i dette direktiv. Ved brug af almindelige jokertegn kan der hentes scripts fra et usikkert domæne."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Rapporteringsdestinationen konfigureres kun via direktivet \"report-to\". Dette direktiv understøttes kun i Chromium-baserede browsere. Derfor anbefaler vi, at du også bruger et \"report-uri\"-direktiv."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Ingen CSP har konfigureret en rapporteringsdestination. Det<PERSON> gør det svært at vedligeholde CSP'en på længere sigt og holde øje med sårbarheder."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Hosttillaldelseslister kan ofte omgås. Du bør i stedet overveje at bruge CSP-nonces eller -hashes sammen med \"strict-dynamic\", hvis det er nødvendigt."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Ukendt CSP-direktiv."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} lader til at være et ugyldigt søgeord."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "\"unsafe-inline\" tillader, at der kan køres \"unsafe in-page\"-scripts og hændelseshandlers. Overvej at bruge CSP-nonces eller -hashes til at tillade scripts på individuel basis."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Overvej at tilføje \"unsafe-inline\" (ignoreres af browsere, der understøtter nonces/hashes) for at gøre CSP'en bagudkompatibel med ældre browsere."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Godkendelse dækkes ikke af jokertegnet (*) ved behandling af CORS `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "<PERSON><PERSON><PERSON><PERSON> om ressourcer, hvis webadresser både indeholder tegnene `(n|r|t)` uden blanktegn og mindre end-tegn (`<`), blokeres. Hvis du vil indlæse disse ressourcer, skal du fjerne linjeskift og indkode mindre end-tegn fra f.eks. attributværdier for elementer."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` er udfaset. Brug i stedet den standardiserede API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` er udfaset. Brug i stedet den standardiserede API: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` er udfaset. Brug i stedet den standardiserede API: `nextHopProtocol` i Navigation Timing 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "<PERSON><PERSON>, der indeholder et `(0|r|n)`-tegn, afvises i stedet for at blive forkortet."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Slækkelse af politikken for samme oprindelse ved at konfigurere `document.domain` er udfaset og deaktiveres som standard. Denne advarsel om udfasning gælder en adgang med krydsoprindelse, som blev aktiveret ved at konfigurere `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Aktivering af {PH1} fra iframes med krydsoprindelse er udfaset og fjernes senere hen."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Attributten `disableRemotePlayback` skal bruges i stedet for selektoren `-internal-media-controls-overlay-cast-button` for at deaktivere standardintegrationen af Cast."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} er udfaset. Brug i stedet {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Dette er et eksempel på en oversat meddelelse vedrørende et udfasningsproblem."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Slækkelse af politikken for samme oprindelse ved at konfigurere `document.domain` er udfaset og deaktiveres som standard. Hvis du vil fortsætte med at bruge denne funktion, skal du fravælge agentklynger med indtastede oprindelser ved at sende en `Origin-Agent-Cluster: ?0`-header sammen med HTTP-svaret for dokumentet og rammerne. Få flere oplysninger på https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` er udfaset og vil blive fjernet. Brug i stedet `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Headeren `Expect-CT` er udfaset og bliver fjernet. Chrome kræver Certifikatgennemsigtighed for alle offentligt godkendte certifikater, der er givet efter den 30. april 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Du kan få flere oplysninger på siden for funktionsstatus."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` og `watchPosition()` fungerer ikke længere på usikre oprindelser. Hvis du vil bruge denne funktion, bør du overveje at skifte til en sikker oprindelse for din app, f.eks. HTTPS. Få flere oplysninger på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` og `watchPosition()` er udfasede på usikre oprindelser. Hvis du vil bruge denne funktion, bør du overveje at skifte til en sikker oprindelse for din app, f.eks. HTTPS. Få flere oplysninger på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` fungerer ikke længere på usikre oprindelser. Hvis du vil bruge denne funktion, bør du overveje at skifte til en sikker oprindelse for din app, f.eks. HTTPS. Få flere oplysninger på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` er udfaset. Brug i stedet `RTCPeerConnectionIceErrorEvent.address` eller `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Sælgerens oprindelse og de vilkårlige data fra `canmakepayment`-hændelsens scripttjeneste er udfaset og fjernes: `topOrigin`, `paymentRequestOrigin`, `methodData` og `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Websitet har anmodet om en underressource fra et netværk, som websitet kun havde adgang til på grund af dets brugeres privilegerede netværksposition. Disse anmodninger eksponerer ikke-offentlige enheder og servere på internettet, hvilket øger risikoen for et angreb med en forfalsket anmodning fra et andet website og/eller læk af oplysninger. For at minimere disse risici udfaser Chrome anmodninger til ikke-offentlige underressourcer, når de stammer fra usikre kontekster, og begynder at blokere dem."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS kan ikke indlæses fra webadresser med `file:`, medmindre de slutter med filtypen `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Brug af `SourceBuffer.abort()` til at annullere fjernelse af det asynkrone område for `remove()` er udfaset på grund af ændringer i specifikationerne. Understøttelsen fjernes senere hen. Du bør lytte til `updateend`-hændelsen i stedet. `abort()` er kun beregnet til at annullere en tilføjelse af asynkrone medier eller nulstille parsertilstanden."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Konfiguration af `MediaSource.duration` under det højeste tidsstempel for præsentation af kodede rammer, der er gemt i bufferen, er udfaset på grund af ændringer i specifikationerne. Understøttelse af implicit fjernelse af forkortede medier, der er gemt i bufferen, fjernes senere hen. Du bør i stedet udføre eksplicit `remove(newDuration, oldDuration)` på alle `sourceBuffers`, hvor `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "<PERSON>ne ænd<PERSON> træder i kraft i det overordnede versionsnummer {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI anmoder om tilladelse til brug, selvom sysex ikke er angivet i `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Notification API kan ikke længere bruges fra usikre oprindelser. Overvej at skifte til en sikker oprindelse for din app, f.eks. HTTPS. Få flere oplysninger på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Det er ikke længere muligt at anmode om tilladelse til Notification API fra en iframe med krydsoprindelse. Du bør i stedet overveje at anmode om tilladelse fra en ramme på øverste niveau eller åbne et nyt vindue."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Din partner forhand<PERSON> om en forældet version af (D)TLS. Kontakt din partner for at løse dette problem."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL i usikre kontekster er udfaset og fjernes snart. Du skal bruge Web Storage eller Indexed Database."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Ang<PERSON>lse af `overflow: visible` i img-, video- og canvas-tags kan medføre, at de producerer visuelt indhold uden for elementgrænserne. Se https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` er udfaset. Brug just-in-time-installation for betalingshandlere i stedet."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Dit `PaymentRequest`-opkald omgik Content Security Policy-direktivet `connect-src` (CSP). Denne omgåelse er udfaset. Føj betalingsmetodens id fra `PaymentRequest` API (i feltet `supportedMethods`) til CSP-direktivet `connect-src`."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` er udfaset. Brug i stedet standardiseret `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` med en overordnet `<picture>` er ugyldig og ignoreres. Brug i stedet `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` er udfaset. Brug i stedet standardiseret `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON> om underressourcer, hvis webadresser indeholder indlejrede loginoplysninger (f.eks. `**********************/`), blokeres."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Begrænsningen `DtlsSrtpKeyAgreement` er fjernet. Du har angivet værdien `false` for den<PERSON> begr<PERSON>, hvilket tolkes som et forsøg på at bruge den fjernede metode `SDES key negotiation`. Denne funktion er fjernet. Brug i stedet en tjeneste, der understøtter `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Begrænsningen `DtlsSrtpKeyAgreement` er fjernet. Du har angivet værdien `true` for denne begræ<PERSON>, hvilket ikke har nogen effekt, men du kan fjerne denne begrænsning af hensyn til ryddeligheden."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` er registreret. Denne dialekt af `Session Description Protocol` understøttes ikke længere. Brug i stedet `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, som bruges ved konstruktion af en `RTCPeerConnection` med `{sdpSemantics:plan-b}`, er en forældet version, der ikke er standard, af `Session Description Protocol`, som er slettet permanent fra Web Platform. Den er stadig tilgængelig for udvikling med `IS_FUCHSIA`, men vi har til hensigt at slette den snarest muligt. Undlad at bruge den. Se status på https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Valgmuligheden `rtcpMuxPolicy` er udfaset og vil blive fjernet."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` kræver domæneisolering. Få flere oplysninger på https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` uden brugeraktivering er udfaset og vil blive fjernet."}, "core/lib/deprecations-strings.js | title": {"message": "Brug af en udfaset funktion"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Udvidelser skal tilvælge domæneisolering for fortsat at kunne bruge `SharedArrayBuffer`. Se https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} er leverandørspecifik. Brug i stedet standarden {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 underst<PERSON><PERSON> ikke af svar-json i `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synkront `XMLHttpRequest` i den primære tråd er udfaset, fordi det negativt påvirkede slutbrugerens oplevelse. Tjek https://xhr.spec.whatwg.org/ for at få mere hjælp."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` er udfaset. Brug i stedet `isSessionSupported()`, og tjek den matchede booleske værdi."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tidspunkt for blokering af den primære tråd"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Cache-TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Beskrivelse"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON>, der ikke bestod gennemgangen"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Placering"}, "core/lib/i18n/i18n.js | columnName": {"message": "Navn"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Over budget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Ressourcestørrelse"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Ressourcetype"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Starttidspunkt"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tidsforbrug"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Overførselsstørrelse"}, "core/lib/i18n/i18n.js | columnURL": {"message": "Webadresse"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Poten<PERSON>l besparelse"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Poten<PERSON>l besparelse"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potentiel besparelse på {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element blev fundet}one{# element blev fundet}other{# elementer blev fundet}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potentiel besparelse på {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "<PERSON><PERSON><PERSON><PERSON> betydningsfulde udfyldning"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Skrifttype"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaktion indtil næste visning"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Lavt"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Middel"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maks. potentiel ventetid efter første input"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Medier"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "And<PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} sek."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Typografiark"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Tredjepart"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "I alt"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Der opstod en fejl ved registreringen af din sideindlæsning. Kør Lighthouse igen. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "<PERSON> opstod timeout under ventetiden til den indledende forbindelse til Debugger-protokollen."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome indsamlede ikke nogen screenshots under sideindlæsningen. <PERSON><PERSON><PERSON> for, at der er synligt indhold på siden, og prøv derefter at køre Lighthouse igen. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS-serverne kunne ikke oversætte det angivne domæne."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Der opstod en fejl i den obligatoriske {artifactName}-inds<PERSON><PERSON>: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Der opstod en intern Chrome-fejl. Genstart Chrome, og prøv at køre Lighthouse igen."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Den obligatoriske {artifactName}-indsamler blev ikke kørt."}, "core/lib/lh-error.js | noFcp": {"message": "<PERSON>n viste ikke noget indhold. <PERSON><PERSON><PERSON> for, at browservinduet er i forgrunden under indlæsningen, og prøv igen. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Siden viste ikke indhold, der er kvalificeret som en største udfyldning af indhold (LCP, Largest Contentful Paint). <PERSON><PERSON><PERSON> for, at siden har et gyldigt LCP-element, og prøv derefter igen. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Den angivne side er ikke HTML (vises som MIME-typen {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Denne version af Chrome er for gammel til at understøtte \"{featureName}\". Brug en nyere version for at se alle resultater."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse kunne ikke indlæse den side, du anmodede om. <PERSON><PERSON>rg for at teste den rigtige webadresse, og tjek, at serveren svarer korrekt på alle anmodninger."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse kunne ikke indlæse den webadresse, du anmodede om, da <PERSON>n stoppede med at svare."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "<PERSON>, du har angivet, har ikke et gyldigt sikkerhedscertifikat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome forhindrede indlæsning af en side med en mellemliggende annonce. <PERSON><PERSON><PERSON> for, at du tester den rigtige webadresse, og tjek, at serveren svarer korrekt på alle anmodninger."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse kunne ikke indlæse den side, du anmodede om. <PERSON><PERSON><PERSON> for, at du tester den rigtige webadresse, og tjek, at serveren svarer korrekt på alle anmodninger. (Info: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse kunne ikke indlæse den side, du anmodede om. <PERSON><PERSON><PERSON> for, at du tester den rigtige webadresse, og tjek, at serveren svarer korrekt på alle anmodninger. (Statuskode: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Det tog for lang tid at indlæse siden. <PERSON><PERSON> muli<PERSON> i rapporten for at reducere indlæsningstiden for din side. Prø<PERSON> derefter at køre Lighthouse igen. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools-pro<PERSON><PERSON><PERSON> har overskredet den tilladte ventetid for svar. (Metode: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "He<PERSON><PERSON> af ressourceindhold har taget længere tid end tilladt"}, "core/lib/lh-error.js | urlInvalid": {"message": "Den angivne webadresse lader til at være ugyldig."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Sidens MIME-type er XHTML: Lighthouse understøtter ikke udtrykkeligt denne dokumenttype"}, "core/user-flow.js | defaultFlowName": {"message": "Brugerflow ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Rapport over navigation ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Rapport med øjebliksbillede ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Rapport over tidsperiode ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Alle rapporter"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Hjælpefunktioner"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Optimale løsninger"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Effektivitet"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressiv webapp"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Computer"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Sådan skal rapporten over flow i Lighthouse forstås"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Sådan skal flow forstås"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Brug rapporter over navigation til..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Brug øjebliksbillederapporter til..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Brug rapporter over tidsperioder til..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Få en Lighthouse-ydeevnescore."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Mål metrics for sideindlæsning såsom største udfyldning af indhold og Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "<PERSON><PERSON>der mulighederne med progressive webapps."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Find tilgængelighedsproblemer i enkeltsideapps og komplekse formularer."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Evaluer optimale løsninger til menuer og brugerfladeelementer, der er skjult bag interaktioner."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Mål layoutskift og tid for JavaScript-udførelse på en række interaktioner."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, der kan forbedre oplevelsen af langvarige sider og enkeltssideapps."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativ gennemgang}one{{numInformative} informativ gennemgang}other{{numInformative} informative gennemgange}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Sideindlæsning"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Rapporter over navigation analyserer indlæsning af en enkelt side, præcis som de oprindelige Lighthouse-rapporter."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Rapport over navigation"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navigationsrapportering}one{{numNavigation} navigationsrapportering}other{{numNavigation} navigationsrapporteringer}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} genne<PERSON>gang, der kan godkendes}one{{numPassableAudits} gennemgang, der kan godkendes}other{{numPassableAudits} genne<PERSON>gan<PERSON>, der kan godkendes}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} gennemgang blev godkendt}one{{numPassed} gennemgang blev godkendt}other{{numPassed} gennemgange blev godkendt}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Gennemsnit"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "God"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Gem"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "En sides tilstand på et specifikt tidspunkt"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Øjebliksbillederapporter analyserer siden i en specifik tilstand, typisk efter brugerinteraktioner."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Rapport med øjebliksbillede"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} rapportering af øjebliksbillede}one{{numSnapshot} rapportering af øjebliksbillede}other{{numSnapshot} rapporteringer af øjebliksbillede}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Oversigt"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Brugerinteraktioner"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Rapporter over tidsperioder analyserer en tilfældig periode, typisk med brugerinteraktioner."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Rapport over tidsperiode"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} perioderapportering}one{{numTimespan} perioderapportering}other{{numTimespan} perioderapporteringer}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Rapport over brugerflow i Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "<PERSON>ed <PERSON>ret indhold bør du bruge [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) til at minimere brugen af CPU, når indholdet er uden for skærmbilledet."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Du kan også vælge at vise alle [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-komponenter i WebP-formater, mens du angiver et passende alternativ for andre browsere. [Få flere oplysninger](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> for, at du bruger [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) til billeder for at få automatisk udskudt indlæsning. [Få flere oplysninger](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Brug værktøjer som f.eks. [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) til at [gengive AMP-layouts på serveren](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> [AMP-dokumentationen](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) for at sikre, at alle skrifttyper understøttes."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponenten [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) understøtter attributten [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) for at specificere, hvilke billedaktiver der skal bruges, baseret på skærmens størrelse. [Få flere oplysninger](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Du kan også vælge virtuel rulning med Component Dev Kit (CDK), h<PERSON> du får vist meget store lister. [Få flere oplysninger](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Brug [kodesplitning på ruteniveau](https://web.dev/route-level-code-splitting-in-angular/) til at minimere stø<PERSON>sen på dine JavaScript-pakker. Du kan også vælge at gemme aktiver i cachen på forhånd ved hjælp af[Angular-scripttjenesten](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON> du bruger Angular CLI, skal du sørge for, at builds genereres i produktionstilstand. [Få flere oplysninger](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "<PERSON><PERSON> du bruger Angular CLI, bør du medtage kildekort i dit produktionsbuild for at inspicere dine pakker. [Få flere oplysninger](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON><PERSON> ruter på forhånd for at gøre navigationen hurtigere. [Få flere oplysninger](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Du kan også vælge at bruge værktøjet `BreakpointObserver` i Component Dev Kit (CDK) til at administrere skillepunkter for billeder. [Få flere oplysninger](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Overvej at uploade din gif til en tjeneste, hvor den kan indlejres som en HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Angiv `@font-display`, når du definerer tilpassede skrifttyper i dit tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Overvej at konfigurere [WebP-billedformater med en Convert-billedstilart](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) på dit website."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Installer [et Drupal-modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) til udskudt indlæsning af billeder. Denne slags moduler gør det muligt at udskyde billeder, som ikke er på skærmen, og på den måde forbedre ydeevnen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Overvej at bruge et modul til at indlejre vigtig CSS og JavaScript eller indlæse aktiver asynkront via JavaScript som f.eks. modulet [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Vær opmærksom på, at optimeringer via dette modul kan ødelægge dit website. Du bliver derfor sandsynligvis nødt til at foretage kodeændringer."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, moduler og serverspecifikationer påvirker alle serverens svartid. Overvej at finde et mere optimeret tema, vælge et modul til optimering og/eller opgradere din server. Dine hostingservere skal bruge PHP-opkodningscachelagring, hukommelsescachelagring for at reducere forespørgselstider i databaser, f.eks. Redis eller Memcached, samt optimeret applogik for hurtigere forberedelse af sider."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Overvej at bruge [responsive billedformater](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) for at reducere størrelsen på de billeder, der indlæses på din side. Hvis du bruger Visninger til at vise flere indholdselementer på en side, kan du overveje at implementere sideinddeling for at begrænse mængden af indholdselementer, der vises på en bestemt side."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "<PERSON><PERSON>rg for, at du har aktiveret \"Sammenlæg CSS-filer\" på siden \"Administration » Konfiguration » Udvikling\". Du kan også konfigurere flere avancerede sammenlægningsmuligheder gennem [yderligere moduler](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) for at gøre dit website hurtigere ved at sammenkæde, formindske og komprimere CSS-formater."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "<PERSON><PERSON>rg for, at du har aktiveret \"Sammenlæg JavaScript-filer\" på siden \"Administration » Konfiguration » Udvikling\". Du kan også konfigurere flere avancerede sammenlægningsmuligheder gennem [yderligere moduler](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) for at gøre dit website hurtigere ved at sammenkæde, formindske og komprimere JavaScript-aktiver."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Overvej at fjerne ubrugte CSS-regler og kun vedhæfte de nødvendige Drupal-samlinger til den relevante side eller komponent på en side. Du kan finde flere oplysninger via [linket til Drupal-dokumentation](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Hvis du vil identificere vedhæftede samlinger, der tilføjer irrelevant CSS, kan du prøve at køre [kodedækning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan identificere det problematiske tema/modul via webadressen for typografiarket, når CSS-sammenlægning er deaktiveret på dit Drupal-website. Kig efter temaer/moduler med mange typografiark på listen, som indeholder meget rødt i kodedækningen. Et tema/modul bør kun sætte et typografiark i kø, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Overvej at fjerne ubrugte JavaScript-aktiver og kun vedhæfte de nødvendige Drupal-samlinger til den relevante side eller komponent på en side. Du kan finde flere oplysninger via [linket til Drupal-dokumentationen](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Hvis du vil identificere vedhæftede samlinger, der tilføjer irrelevant JavaScript, kan du prøve at køre [kodedækning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan identificere det problematiske tema/modul via webadressen for scriptet, når JavaScript-sammenlægning er deaktiveret på dit Drupal-website. Kig efter temaer/moduler med mange scripts på listen, som indeholder meget rødt i kodedækningen. Et tema/modul bør kun sætte et script i kø, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON> \"Maks. alder for browser- og proxycachelagring\" på siden \"Administration » Konfiguration » Udvikling\". Læs om [Drupal-cache og optimering for bedre ydeevne](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Overvej at bruge [et modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), der automatisk optimerer og reducerer størrelsen på billeder, der uploades gennem websitet, mens kvaliteten bevares. Sørg også for at bruge de indbyggede [responsive billedformater](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) fra Drupal (tilgængelig i Drupal 8 og nyere versioner) til alle billeder, der gengives på dette website."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Ressourcehints til at oprette forbindelse på forhånd eller ressourcehints til forhåndsindlæsning af DNS kan tilføjes ved at installere og konfigurere [et modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), der leverer faciliteter til ressourcehints for brugeragenter."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "<PERSON><PERSON>rg for at bruge de indbyggede [responsive billedformater](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) fra Drupal (tilgængelig i Drupal 8 og nyere versioner). Brug de responsive billedformater, når du gengiver billedfelter gennem visningstilstande, visninger eller billeder, der uploades via WYSIWYG-editoren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Optimize Fonts` for automatisk at benytte funktionen for prissammenligningstjenesten (CSS) (`font-display`-funktionen), der sikrer, at tekst vises til brugerne, mens der indlæses webfonts."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Next-Gen Formats` for at konvertere billeder til WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `<PERSON><PERSON>ad Images` for at udsætte indlæsning af billeder, der ikke vises på skærmen, indtil der er behov for dem."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Critical CSS` og `Script Delay` for at udsætte ikke-kritisk JavaScript/prissammenligningstjeneste (CSS)."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Brug [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) for at cachelagre dit indhold på vores verdensomspændende netværk, hvilket forbedrer Time To First Byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Minify CSS` for automatisk at formindske din prissammenligningstjeneste (CSS) med henblik på at reducere størrelsen på netværkets datapakker."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Minify Javascript` for automatisk at formindske JavaScript med henblik på at reducere størrelsen på netværkets datapakker."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Remove Unused CSS` for at hjælpe med at løse dette problem. Dette vil identificere de prissammenligningstjenesteklasser, der aktuelt anvendes på hver side på dit website, og fjerne eventuelle andre for at sikre, at filstørrelsen forbliver lille."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Efficient Static Cache Policy` for at angive anbefalede værdier i cacheheaderen for statiske aktiver."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Next-Gen Formats` for at konvertere billeder til WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Pre-Connect Origins` for automatisk at tilføje `preconnect`-ressourcehints med henblik på at etablere tidlige forbindelser til vigtige tredjepartsoprindelser."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Preload Fonts` og `Preload Background Images` for at tilføje `preload`-links med henblik på at prioritere hentning af ressourcer, der aktuelt anmodes om, senere i sideindlæsningen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Brug [Ezoic Leap](https://pubdash.ezoic.com/speed), og aktivér `Resize Images` for at tilpasse størrelsen på billeder til en størrelse, der er passende for en enhed, hvilket reducerer størrelsen på netværkets datapakker."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Overvej at uploade din gif til en tjeneste, hvor den kan indlejres som en HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Overvej at bruge et [plugin](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) eller en tjeneste, der automatisk konverterer dine uploadede billeder til deres optimale formater."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Installer et [Joomla-plugin til udskudt indlæsning](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), der gør det muligt at udskyde eventuelle billeder, som ikke er på skærmen, el<PERSON> skifte til en skabelon, der leverer denne funktionalitet. Fra og med Joomla 4.0 får alle nye billeder [automatisk](https://github.com/joomla/joomla-cms/pull/30748) attributten `loading` fra kernefunktionen."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Der er en række Jo<PERSON>-plugins, som kan hjæ<PERSON><PERSON> dig med at [indlejre vigtige aktiver](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) eller [udskyde mindre vigtige ressourcer](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Vær opmærksom på, at optimeringer via disse plugins kan ødelægge funktioner i dine skabeloner og plugins. Du bliver derfor nødt til at teste disse omhyggeligt."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, udvidelser og serverspecifikationer påvirker alle serverens svartid. Overvej at finde en mere optimeret skabelon, vælge en udvidelse til optimering og/eller opgradere din server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Overvej at vise uddrag på dine artikelkategorier (f.eks. via linket Læs mere), reducere antallet af viste artikler på en given side, opdele dine lange opslag i flere sider eller bruge et plugin til at indlæse kommentarer langsomt."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "En række [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) kan gøre dit website hurtigere ved at sammenkæde, formindske og komprimere dine typografier. Der er også skabeloner, der leverer denne funktionalitet."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "En række [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) kan gøre dit website hurtigere ved at sammenkæde, formindske og komprimere dine scripts. Der er også skabeloner, der leverer denne funktionalitet."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Overvej at reducere eller ændre antallet af [Joomla-udvidelser](https://extensions.joomla.org/), der indlæser ubrugt CSS på din side. Hvis du vil identificere udvidelser, der tilføjer irrelevant CSS, kan du prøve at køre [kodedækning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan identificere det problematiske tema/plugin via webadressen for typografiarket. Kig efter plugins med mange typografiark på listen, som indeholder meget rødt i kodedækningen. Et plugin bør kun sætte et typografiark i kø, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Overvej at reducere eller ændre antallet af [Joomla-udvidelser](https://extensions.joomla.org/), der indlæser ubrugt JavaScript på din side. Hvis du vil identificere plugins, der tilføjer irrelevant JavaScript, kan du prøve at køre [koded<PERSON>k<PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chrome DevTools. Du kan identificere den problematiske udvidelse via webadressen for scriptet. Kig efter udvidelser med mange scripts på listen, som indeholder meget rødt i kodedækningen. En udvidelse bør kun sætte et script i kø, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> mere om [browserens cachelagring i Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Overvej at bruge et [plugin til billedoptimering](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), der komprimerer dine billeder uden at gå på kompromis med kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Overvej at bruge et [plugin til responsive billeder](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), så du kan bruge responsive billeder i dit indhold."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Du kan aktivere tekstkomprimering ved at aktivere Gzip-sidekomprimering i Joomla (System > Global configuration > Server [System > Global konfiguration > Server])."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "<PERSON><PERSON> du ikke pakker dine JavaScript-aktiver, bør du overveje at bruge [Ba<PERSON>](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Deaktiver Ma<PERSON> indbyggede [JavaScript-pakning og -formindskning](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), og overvej at bruge [Baler](https://github.com/magento/baler/) i stedet."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Angiv `@font-display`, n<PERSON>r du [definerer tilpassede skrifttyper](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Du kan også vælge at søge på [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) efter et udvalg af tredjepartsudvidelser for at benytte nyere billedformater."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Du kan også vælge at ændre skabelonerne til dine produkter og kataloger for at udnytte funktionen [udskudt indlæsning](https://web.dev/native-lazy-loading) på webplatformen."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Brug Magentos [Varnish-integration](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Aktivér indstillingen \"Minify CSS Files\" (Formindsk CSS-filer) i udviklingsindstillingerne for din butik. [Få flere oplysninger](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Brug [Terser](https://www.npmjs.com/package/terser) til at formindske alle JavaScript-aktiver fra statisk indholdsimplementering og til at deaktivere den indbyggede funktion til formindskning."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Deaktiver Ma<PERSON> indbyggede [JavaScript-pakning](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Du kan også vælge at søge på [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) efter et udvalg af tredjepartsudvidelser for at optimere billeder."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Ressourcehints til at oprette forbindelse på forhånd eller ressourcehints til forhåndsindlæsning af DNS kan tilføjes ved at [ændre layoutet for et tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>`-tags kan tilfø<PERSON>s ved at [ændre layoutet for et tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Brug komponenten `next/image` i stedet for `<img>` til automatisk at optimere billedformatet. [Få flere oplysninger](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Brug komponenten `next/image` i stedet for `<img>` til automatisk at udføre lazy loading af billeder. [Få flere oplysninger](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Brug `next/image`-komponenten, og angiv \"prioritet\" som sand for at forudindlæse LCP-billedet [Få flere oplysninger](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Brug komponenten `next/script` til at udskyde indlæsning af tredjepartsscripts, som ikke er kritiske. [Få flere oplysninger](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Brug komponenten `next/image` til at sikre, at billeder altid har den korrekte størrelse. [Få flere oplysninger](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Overvej at konfigurere `PurgeCSS` i `Next.js`-konfigurationen for at fjerne ubrugte regler fra typografiark. [Få flere oplysninger](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Brug `Webpack Bundle Analyzer` for at registrere ubrugt JavaScript-kode. [Få flere oplysninger](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Overvej at bruge `Next.js Analytics` til at måle din apps ydeevne i den virkelige verden. [Få flere oplysninger](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurer cachelagring for uforanderlige aktiver og SSR-sider (`Server-side Rendered`). [Få flere oplysninger](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Brug komponenten `next/image` i stedet for `<img>` til at justere billedkvaliteten. [Få flere oplysninger](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Brug komponenten `next/image` til at angive de rigtige `sizes`. [Få flere oplysninger](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "<PERSON>kt<PERSON><PERSON><PERSON> komp<PERSON>ering på din Next.js-server. [Få flere oplysninger](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Brug komponenten `nuxt/image`, og angiv `format=\"webp\"`. [Få flere oplysninger](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Brug komponenten `nuxt/image`, og angiv `loading=\"lazy\"` for billed<PERSON>, der ikke er på skærmen. [Få flere oplysninger](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Brug komponenten `nuxt/image`, og angiv `preload` for LCP-billedet. [Få flere oplysninger](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Brug komponenten `nuxt/image`, og angiv udtrykkeligt `width` og `height`. [Få flere oplysninger](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Brug komponenten `nuxt/image`, og angiv den relevante `quality`. [Få flere oplysninger](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Brug komponenten `nuxt/image`, og angiv de relevante `sizes`. [Få flere oplysninger](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Udskift animerede giffer med video](https://web.dev/replace-gifs-with-videos/) for at få hurtigere websideindlæsning, og overvej at bruge moderne filformater som f.eks. [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) eller [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) for at forbedre effektiviteten af komprimering med mere end 30 % i forhold til det topmoderne videocodec VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Overvej at bruge et [plugin](https://octobercms.com/plugins?search=image) el<PERSON> en tjeneste, der automatisk konverterer de uploadede billeder til de optimale formater. [Tabsfri WebP-billeder](https://developers.google.com/speed/webp) er 26 % mindre end PNG'er og 25-34 % mindre end sammenlignelige JPEG-billeder med tilsvarende SSIM-kvalitetsindeks. Du kan også overveje at bruge \"next-gen\"-billedformatet [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Overvej at installere et [plugin til udskudt billedindlæsning](https://octobercms.com/plugins?search=lazy), der gør det muligt at udskyde eventuelle billeder, som ikke er på skærmen, eller skifte til et tema, der leverer denne funktionalitet. Overvej også at bruge [AMP-pluginnet](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Der er mange plugins, som hjælper med at [indlejre vigtige aktiver](https://octobercms.com/plugins?search=css). Disse plugins kan ø<PERSON>ægge andre plugins, så du bør foretage grundige tests."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, plugins og serverspecifikationer påvirker alle serverens svartid. Overvej at finde et mere optimeret tema, vælge en udvidelse til optimering og/eller opgradere serveren. October CMS giver også udviklerne mulighed for at bruge [`Queues`](https://octobercms.com/docs/services/queues) til at udskyde udførelsen af en tidskrævende opgave, f.eks. afsendelse af en mail. Dette øger hastigheden på webanmodninger betragteligt."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Overvej at vise uddrag på dine opslagslister (f.eks. via knappen `show more`), reducere antallet af viste opslag på en given webside, opdele dine lange opslag i flere websider eller bruge et plugin til at indlæse kommentarer efter behov."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Der er mange [plugins](https://octobercms.com/plugins?search=css), der kan gøre et website hurtigere ved at sammenkæde, formindske og komprimere formater. Hvis du bruger en buildproces til på forhånd at foretage denne formindskelse, kan det øge effektiviteten."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Der er mange [plugins](https://octobercms.com/plugins?search=javascript), som kan gøre dit website hurtigere ved at sammenkæde, formindske og komprimere scripts. Hvis du bruger en buildproces til på forhånd at foretage denne formindskelse, kan det øge effektiviteten."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Overvej at gennemgå de [plugins](https://octobercms.com/plugins), der indlæser ubrugt CSS på websitet. Hvis du vil identificere plugins, der tilføjer unødvendig CSS, skal du køre [kodedækning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Udviklerværktøjer til Chrome. Identificer det problematiske tema/plugin via webadressen for typografiarket. Kig efter plugins med mange typografiark og meget rødt i kodedækningen. Et plugin bør kun tilføje et typografiark, hvis det rent faktisk anvendes på websiden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Overvej at gennemgå de [plugins](https://octobercms.com/plugins?search=javascript), der indlæser ubrugt JavaScript på websiden. Hvis du vil identificere plugins, der tilføjer unødvendig JavaScript, skal du køre [kodedækning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Udviklerværktøjer til Chrome. Identificer det problematiske tema/plugin fra scriptets webadresse. Kig efter plugins med mange scripts og meget rødt i kodedækningen. Et plugin bør kun tilføje et script, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Få flere oplysninger om, hvordan du [forhindrer unødvendige netværksanmodninger med HTTP-cachen](https://web.dev/http-cache/#caching-checklist). Der er mange [plugins](https://octobercms.com/plugins?search=Caching), der kan bruges til at gemme hurtigere i cachen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Overvej at bruge et [plugin til billedoptimering](https://octobercms.com/plugins?search=image) til at komprimere billeder uden at gå på kompromis med kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Upload billeder direkte i medieadministrationen for at sikre, at de påkrævede billedstørrelser er tilgængelige. Overvej at bruge [filteret til valg af en anden størrelse](https://octobercms.com/docs/markup/filter-resize) el<PERSON> et [plugin til valg af en anden billedstørrelse](https://octobercms.com/plugins?search=image) for at sikre, at de optimale billedstørrelser anvendes."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Aktivér tekstkomprimering i konfigurationen af din webserver."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "<PERSON><PERSON> du gengiver mange gentagne elementer på siden, kan du overveje at bruge et \"vinduesbibliotek\" som f.eks. `react-window` for at minimere antallet af DOM-noder, der oprettes. [Få flere oplysninger](https://web.dev/virtualize-long-lists-react-window/). <PERSON><PERSON> du bruger `Effect`-hooken til at forbedre kørselstiden, bør du også minimere antallet af unødvendige gentagne gengivelser ved kun at bruge [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) eller [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) og [springe effekter over](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), indtil visse afhængige elementer er ændret."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON> du bruger React Router, bør du minimere brugen af komponenten `<Redirect>` til [rutenavigationer](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "<PERSON><PERSON> du gengiver React-komponenter på serveren, bø<PERSON> du overveje at bruge `renderToPipeableStream()` eller `renderToStaticNodeStream()` for at give klienten mulighed for at modtage og hydrere forskellige dele af opmærkningen i stedet for det hele på én gang. [Få flere oplysninger](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Hvis dit buildsystem formindsker CSS-filer automatisk, skal du sørge for, at du implementerer produktionsbuildet af din app. Du kan tjekke dette med udvidelsen React Developer Tools. [Få flere oplysninger](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Hvis dit buildsystem formindsker JS-filer automatisk, skal du sørge for, at du implementerer produktionsbuildet af din app. Du kan tjekke dette med udvidelsen React Developer Tools. [Få flere oplysninger](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON>vis du ikke gengiver på serveren, [kan du opdele JavaScript-pakker](https://web.dev/code-splitting-suspense/) med `React.lazy()`. Du kan også kodesplitte ved hjælp at et tredjepartsbibliotek som f.eks.[indlæsbare komponenter](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Brug React DevTools Profiler, som anvender Profiler API til at måle effektiviteten og gengivelsen af dine komponenter. [Få flere oplysninger.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Overvej at uploade din gif til en tjeneste, hvor den kan indlejres som en HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Overvej at bruge pluginnet [Performance Lab](https://wordpress.org/plugins/performance-lab/) til automatisk at konvertere dine uploadede JPEG-billeder til WebP, når det understøttes."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Installer et [WordPress-plugin til udskudt indlæsning](https://wordpress.org/plugins/search/lazy+load/), der gør det muligt at udskyde eventuelle billeder, som ikke er på skærmen, eller skifte til et tema, der leverer denne funktionalitet. Overvej også at bruge [AMP-pluginnet](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Der er en række WordPress-plugins, som kan hjæ<PERSON><PERSON> dig med at [indlejre vigtige aktiver](https://wordpress.org/plugins/search/critical+css/) eller [udskyde mindre vigtige ressourcer](https://wordpress.org/plugins/search/defer+css+javascript/). Vær opmærksom på, at optimeringer via disse plugins kan ødelægge funktioner i dine temaer og plugins. Du bliver derfor sandsynligvis nødt til at foretage kodeændringer."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, plugins og serverspecifikationer påvirker alle serverens svartid. Overvej at finde et mere optimeret tema, vælge et plugin til optimering og/eller opgradere din server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Overvej at vise uddrag på dine opslagslister (f.eks. via tagget Mere), reducere antallet af viste opslag på en given side, opdele dine lange opslag i flere sider eller bruge et plugin til at indlæse kommentarer langsomt."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "En række [WordPress-plugins](https://wordpress.org/plugins/search/minify+css/) kan gøre dit website hurtigere ved at sammenkæde, formindske og komprimere dine typografier. Det kan også være en god idé at bruge en buildproces til at udføre denne formindskelse på forhånd, hvis det er muligt."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "En række [WordPress-plugins](https://wordpress.org/plugins/search/minify+javascript/) kan gøre dit website hurtigere ved at sammenkæde, formindske og komprimere dine scripts. Det kan også være en god idé at bruge en buildproces til at udføre denne formindskelse på forhånd, hvis det er muligt."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Overvej at reducere eller ændre antallet af [WordPress-plugins](https://wordpress.org/plugins/), der indlæser ubrugt CSS på din side. Hvis du vil identificere plugins, der tilføjer irrelevant CSS, kan du prøve at køre [kodedækning](https://developer.chrome.com/docs/devtools/coverage/) i Chrome DevTools. Du kan identificere det problematiske tema/plugin via webadressen for typografiarket. Kig efter plugins med mange typografiark på listen, som indeholder meget rødt i kodedækningen. Et plugin bør kun sætte et typografiark i kø, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Overvej at reducere eller ændre antallet af [WordPress-plugins](https://wordpress.org/plugins/), der indlæser ubrugt JavaScript på din side. Hvis du vil identificere plugins, der tilføjer irrelevant JavaScript, kan du prøve at køre [kodedækning](https://developer.chrome.com/docs/devtools/coverage/) i Chrome DevTools. Du kan identificere det problematiske tema/plugin via webadressen for scriptet. Kig efter plugins med mange scripts på listen, som indeholder meget rødt i kodedækningen. Et plugin bør kun sætte et script i kø, hvis det rent faktisk anvendes på siden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> om [browserens cachelagring i WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Overvej at bruge et [WordPress-plugin til billedoptimering](https://wordpress.org/plugins/search/optimize+images/), der komprimerer dine billeder uden at gå på kompromis med kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Upload billeder direkte via [mediesamlingen](https://wordpress.org/support/article/media-library-screen/) for at sikre, at de påkrævede billedstørrelser er tilgængelige, og indsæt dem derefter fra mediesamlingen, eller brug billedwidgetten til at sikre, at de optimale billedstørrelser anvendes (inklusive dem til responsive skillepunkter). Undgå at bruge billeder i `Full Size`, medmindre dimensionerne er passende til brugen. [Få flere oplysninger](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Du kan aktivere tekstkomprimering ved konfigurationen af din webserver."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Aktiver \"Imagify\" (billedgør) på fanen Imagine Optimization (billedoptimering) i \"WP Rocket\" for at konvertere dine billeder til WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Aktiv<PERSON><PERSON> [La<PERSON><PERSON>oa<PERSON>](https://docs.wp-rocket.me/article/1141-lazyload-for-images) i WP Rocket for at imødekomme denne anbefaling. Denne funktion forsinker indlæsningen af billederne, indtil den besøgende ruller ned på siden for rent faktisk at se dem."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Aktivér [Remove Unused CSS (fjer<PERSON>S, som ikke bruges)](https://docs.wp-rocket.me/article/1529-remove-unused-css) og [Load JavaScript deferred (indlæs udskudt JavaScript)](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) i \"WP Rocket\" for at imødekomme denne anbefaling. Disse funktioner optimerer CSS- og JavaScript-filerne, så de ikke blokerer for gengivelsen af din side."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Aktivér [Minify CSS-files (komprimer CSS-filer)](https://docs.wp-rocket.me/article/1350-css-minify-combine) i \"WP Rocket\" for at løse problemet. Eventuelle mellemrum og kommentarer i dit websites CSS-filer fjernes for at gøre filen mindre og hurtigere at downloade."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Aktivér [Minify JavaScript files (komprimer JavaScript-filer)](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) i \"WP Rocket\" for at løse dette problem. Eventuelle mellemrum og kommentarer fjernes fra JavaScript-filer for at gøre dem mindre og hurtigere at downloade."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Aktivér [Remove Unused CSS (fjern CSS, som ikke bruges)](https://docs.wp-rocket.me/article/1529-remove-unused-css) i \"WP Rocket\" for at løse problemet. Dette reducerer størrelsen på siden ved at fjerne alle CSS'er og typografiark, som ikke bruges, mens kun den anvendte CSS beholdes for hver side."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Aktivér [Delay JavaScript execution (forsink JavaScript-udførelse)](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) i \"WP Rocket\" for at løse dette problem. Denne funktion forbedrer indlæsningen af din side ved at forsinke udførelsen af scripts, indtil der registreres brugerinteraktion. Hvis dit website har iframes, kan du også bruge WP Rockets [LazyLoad for iframes and videos (LazyLoad for iframes og videoer)](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) og [Replace YouTube iframe with preview image (erstat YouTube-iframe med billede til forhåndsvisning)](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Aktiv<PERSON>r \"Imagify\" (billedgør) på fanen Image Optimization (billedoptimering) i \"WP Rocket\", og kør Bulk Optimization (masseoptimering) for at komprimere dine billeder."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Brug [Prefetch DNS Requests (hent DNS-anmodninger på forhånd)](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) i \"WP Rocket\" for at tilføje \"dns-prefetch\" og gøre forbindelsen til eksterne domæner hurtigere. \"WP Rocket\" føjer også automatisk \"preconnect\" til [Google Fonts-domænet](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) og eventuelle CNAME'er, der tilføjes via funktionen [Enable CDN (aktivér CDN)](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Du kan løse dette problem for skrifttyper ved at aktivere [Remove Unused CSS (fjern CSS, som ikke bruges)](https://docs.wp-rocket.me/article/1529-remove-unused-css) i \"WP Rocket\". De vigtigste skrifttyper for dit website forudindlæses med prioritet."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON> beregner."}, "report/renderer/report-utils.js | collapseView": {"message": "Skjul visning"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Indledende navigation"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> forsin<PERSON> for kritisk sti:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON><PERSON><PERSON>r JSO<PERSON>"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Slå Mørkt tema til/fra"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Udskriften blev udvidet"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Udskriftsoversigt"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Gem som Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Gem som HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Gem som JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Åbn i fremviser"}, "report/renderer/report-utils.js | errorLabel": {"message": "Der opstod en fejl"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Rapportfejl: Der er ingen revisionsoplysninger"}, "report/renderer/report-utils.js | expandView": {"message": "<PERSON><PERSON><PERSON> visning"}, "report/renderer/report-utils.js | footerIssue": {"message": "Indsend et problem"}, "report/renderer/report-utils.js | hide": {"message": "Skjul"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratoriedata"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/)-analyse af den aktuelle side på et emuleret mobilnetværk. Værdierne er estimater og kan variere."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Yderligere elementer, der skal tjekkes manuelt"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Ikke <PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON> revisioner"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Oprindelig sideindlæsning"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Tilpass<PERSON> begrænsning"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emuleret computer"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Ingen emulering"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-version"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Ubegrænset CPU/hukommelse"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU-beg<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Enhed"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Netværksbegrænsning"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Skærmemulering"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Brugeragent (netværk)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Enkeltstående sideindlæsning"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Disse data stammer fra en enkelt sideindlæsning i modsætning til feltdata, der omfatter flere sessioner."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Begrænsning til langsom 4G-forbindelse"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Ukendt"}, "report/renderer/report-utils.js | show": {"message": "Vis"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Vis gennem<PERSON>ge, som er relevante for:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Sk<PERSON><PERSON> uddrag"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Vis ressourcer fra tredjeparter"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Leveret af miljøet"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Der blev registreret problemer, som påvirkede denne kørsel af Lighthouse:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Værdierne er estimater og kan variere. [Resultatet beregnes](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) direkte på baggrund af disse metrics."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Se oprindeligt spor"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Se spor"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Se trædiagram"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON> <PERSON>er, men med advarsler"}, "report/renderer/report-utils.js | warningHeader": {"message": "<PERSON><PERSON><PERSON>! "}, "treemap/app/src/util.js | allLabel": {"message": "Alle"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Alle scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "D<PERSON><PERSON>ning"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Identiske moduler"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bytes i ressourcefil"}, "treemap/app/src/util.js | tableColumnName": {"message": "Navn"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Vis/skjul tabel"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Ubrugte bytes"}}