{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport raf from \"rc-util/es/raf\";\nimport contains from \"rc-util/es/Dom/contains\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport Portal from \"rc-util/es/Portal\";\nimport classNames from 'classnames';\nimport { getAlignFromPlacement, getAlignPopupClassName } from './utils/alignUtil';\nimport Popup from './Popup';\nimport TriggerContext from './context';\nfunction noop() {}\nfunction returnEmptyString() {\n  return '';\n}\nfunction returnDocument(element) {\n  if (element) {\n    return element.ownerDocument;\n  }\n  return window.document;\n}\nvar ALL_HANDLERS = ['onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur', 'onContextMenu'];\n/**\n * Internal usage. Do not use in your code since this will be removed.\n */\n\nexport function generateTrigger(PortalComponent) {\n  var Trigger = /*#__PURE__*/function (_React$Component) {\n    _inherits(Trigger, _React$Component);\n    var _super = _createSuper(Trigger);\n\n    // ensure `getContainer` will be called only once\n    function Trigger(props) {\n      var _this;\n      _classCallCheck(this, Trigger);\n      _this = _super.call(this, props);\n      _this.popupRef = /*#__PURE__*/React.createRef();\n      _this.triggerRef = /*#__PURE__*/React.createRef();\n      _this.portalContainer = void 0;\n      _this.attachId = void 0;\n      _this.clickOutsideHandler = void 0;\n      _this.touchOutsideHandler = void 0;\n      _this.contextMenuOutsideHandler1 = void 0;\n      _this.contextMenuOutsideHandler2 = void 0;\n      _this.mouseDownTimeout = void 0;\n      _this.focusTime = void 0;\n      _this.preClickTime = void 0;\n      _this.preTouchTime = void 0;\n      _this.delayTimer = void 0;\n      _this.hasPopupMouseDown = void 0;\n      _this.onMouseEnter = function (e) {\n        var mouseEnterDelay = _this.props.mouseEnterDelay;\n        _this.fireEvents('onMouseEnter', e);\n        _this.delaySetPopupVisible(true, mouseEnterDelay, mouseEnterDelay ? null : e);\n      };\n      _this.onMouseMove = function (e) {\n        _this.fireEvents('onMouseMove', e);\n        _this.setPoint(e);\n      };\n      _this.onMouseLeave = function (e) {\n        _this.fireEvents('onMouseLeave', e);\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      };\n      _this.onPopupMouseEnter = function () {\n        _this.clearDelayTimer();\n      };\n      _this.onPopupMouseLeave = function (e) {\n        var _this$popupRef$curren;\n\n        // https://github.com/react-component/trigger/pull/13\n        // react bug?\n        if (e.relatedTarget && !e.relatedTarget.setTimeout && contains((_this$popupRef$curren = _this.popupRef.current) === null || _this$popupRef$curren === void 0 ? void 0 : _this$popupRef$curren.getElement(), e.relatedTarget)) {\n          return;\n        }\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      };\n      _this.onFocus = function (e) {\n        _this.fireEvents('onFocus', e); // incase focusin and focusout\n\n        _this.clearDelayTimer();\n        if (_this.isFocusToShow()) {\n          _this.focusTime = Date.now();\n          _this.delaySetPopupVisible(true, _this.props.focusDelay);\n        }\n      };\n      _this.onMouseDown = function (e) {\n        _this.fireEvents('onMouseDown', e);\n        _this.preClickTime = Date.now();\n      };\n      _this.onTouchStart = function (e) {\n        _this.fireEvents('onTouchStart', e);\n        _this.preTouchTime = Date.now();\n      };\n      _this.onBlur = function (e) {\n        _this.fireEvents('onBlur', e);\n        _this.clearDelayTimer();\n        if (_this.isBlurToHide()) {\n          _this.delaySetPopupVisible(false, _this.props.blurDelay);\n        }\n      };\n      _this.onContextMenu = function (e) {\n        e.preventDefault();\n        _this.fireEvents('onContextMenu', e);\n        _this.setPopupVisible(true, e);\n      };\n      _this.onContextMenuClose = function () {\n        if (_this.isContextMenuToShow()) {\n          _this.close();\n        }\n      };\n      _this.onClick = function (event) {\n        _this.fireEvents('onClick', event); // focus will trigger click\n\n        if (_this.focusTime) {\n          var preTime;\n          if (_this.preClickTime && _this.preTouchTime) {\n            preTime = Math.min(_this.preClickTime, _this.preTouchTime);\n          } else if (_this.preClickTime) {\n            preTime = _this.preClickTime;\n          } else if (_this.preTouchTime) {\n            preTime = _this.preTouchTime;\n          }\n          if (Math.abs(preTime - _this.focusTime) < 20) {\n            return;\n          }\n          _this.focusTime = 0;\n        }\n        _this.preClickTime = 0;\n        _this.preTouchTime = 0; // Only prevent default when all the action is click.\n        // https://github.com/ant-design/ant-design/issues/17043\n        // https://github.com/ant-design/ant-design/issues/17291\n\n        if (_this.isClickToShow() && (_this.isClickToHide() || _this.isBlurToHide()) && event && event.preventDefault) {\n          event.preventDefault();\n        }\n        var nextVisible = !_this.state.popupVisible;\n        if (_this.isClickToHide() && !nextVisible || nextVisible && _this.isClickToShow()) {\n          _this.setPopupVisible(!_this.state.popupVisible, event);\n        }\n      };\n      _this.onPopupMouseDown = function () {\n        _this.hasPopupMouseDown = true;\n        clearTimeout(_this.mouseDownTimeout);\n        _this.mouseDownTimeout = window.setTimeout(function () {\n          _this.hasPopupMouseDown = false;\n        }, 0);\n        if (_this.context) {\n          var _this$context;\n          (_this$context = _this.context).onPopupMouseDown.apply(_this$context, arguments);\n        }\n      };\n      _this.onDocumentClick = function (event) {\n        if (_this.props.mask && !_this.props.maskClosable) {\n          return;\n        }\n        var target = event.target;\n        var root = _this.getRootDomNode();\n        var popupNode = _this.getPopupDomNode();\n        if (\n        // mousedown on the target should also close popup when action is contextMenu.\n        // https://github.com/ant-design/ant-design/issues/29853\n        (!contains(root, target) || _this.isContextMenuOnly()) && !contains(popupNode, target) && !_this.hasPopupMouseDown) {\n          _this.close();\n        }\n      };\n      _this.getRootDomNode = function () {\n        var getTriggerDOMNode = _this.props.getTriggerDOMNode;\n        if (getTriggerDOMNode) {\n          return getTriggerDOMNode(_this.triggerRef.current);\n        }\n        try {\n          var domNode = findDOMNode(_this.triggerRef.current);\n          if (domNode) {\n            return domNode;\n          }\n        } catch (err) {// Do nothing\n        }\n        return ReactDOM.findDOMNode(_assertThisInitialized(_this));\n      };\n      _this.getPopupClassNameFromAlign = function (align) {\n        var className = [];\n        var _this$props = _this.props,\n          popupPlacement = _this$props.popupPlacement,\n          builtinPlacements = _this$props.builtinPlacements,\n          prefixCls = _this$props.prefixCls,\n          alignPoint = _this$props.alignPoint,\n          getPopupClassNameFromAlign = _this$props.getPopupClassNameFromAlign;\n        if (popupPlacement && builtinPlacements) {\n          className.push(getAlignPopupClassName(builtinPlacements, prefixCls, align, alignPoint));\n        }\n        if (getPopupClassNameFromAlign) {\n          className.push(getPopupClassNameFromAlign(align));\n        }\n        return className.join(' ');\n      };\n      _this.getComponent = function () {\n        var _this$props2 = _this.props,\n          prefixCls = _this$props2.prefixCls,\n          destroyPopupOnHide = _this$props2.destroyPopupOnHide,\n          popupClassName = _this$props2.popupClassName,\n          onPopupAlign = _this$props2.onPopupAlign,\n          popupMotion = _this$props2.popupMotion,\n          popupAnimation = _this$props2.popupAnimation,\n          popupTransitionName = _this$props2.popupTransitionName,\n          popupStyle = _this$props2.popupStyle,\n          mask = _this$props2.mask,\n          maskAnimation = _this$props2.maskAnimation,\n          maskTransitionName = _this$props2.maskTransitionName,\n          maskMotion = _this$props2.maskMotion,\n          zIndex = _this$props2.zIndex,\n          popup = _this$props2.popup,\n          stretch = _this$props2.stretch,\n          alignPoint = _this$props2.alignPoint,\n          mobile = _this$props2.mobile,\n          forceRender = _this$props2.forceRender;\n        var _this$state = _this.state,\n          popupVisible = _this$state.popupVisible,\n          point = _this$state.point;\n        var align = _this.getPopupAlign();\n        var mouseProps = {};\n        if (_this.isMouseEnterToShow()) {\n          mouseProps.onMouseEnter = _this.onPopupMouseEnter;\n        }\n        if (_this.isMouseLeaveToHide()) {\n          mouseProps.onMouseLeave = _this.onPopupMouseLeave;\n        }\n        mouseProps.onMouseDown = _this.onPopupMouseDown;\n        mouseProps.onTouchStart = _this.onPopupMouseDown;\n        return /*#__PURE__*/React.createElement(Popup, _extends({\n          prefixCls: prefixCls,\n          destroyPopupOnHide: destroyPopupOnHide,\n          visible: popupVisible,\n          point: alignPoint && point,\n          className: popupClassName,\n          align: align,\n          onAlign: onPopupAlign,\n          animation: popupAnimation,\n          getClassNameFromAlign: _this.getPopupClassNameFromAlign\n        }, mouseProps, {\n          stretch: stretch,\n          getRootDomNode: _this.getRootDomNode,\n          style: popupStyle,\n          mask: mask,\n          zIndex: zIndex,\n          transitionName: popupTransitionName,\n          maskAnimation: maskAnimation,\n          maskTransitionName: maskTransitionName,\n          maskMotion: maskMotion,\n          ref: _this.popupRef,\n          motion: popupMotion,\n          mobile: mobile,\n          forceRender: forceRender\n        }), typeof popup === 'function' ? popup() : popup);\n      };\n      _this.attachParent = function (popupContainer) {\n        raf.cancel(_this.attachId);\n        var _this$props3 = _this.props,\n          getPopupContainer = _this$props3.getPopupContainer,\n          getDocument = _this$props3.getDocument;\n        var domNode = _this.getRootDomNode();\n        var mountNode;\n        if (!getPopupContainer) {\n          mountNode = getDocument(_this.getRootDomNode()).body;\n        } else if (domNode || getPopupContainer.length === 0) {\n          // Compatible for legacy getPopupContainer with domNode argument.\n          // If no need `domNode` argument, will call directly.\n          // https://codesandbox.io/s/eloquent-mclean-ss93m?file=/src/App.js\n          mountNode = getPopupContainer(domNode);\n        }\n        if (mountNode) {\n          mountNode.appendChild(popupContainer);\n        } else {\n          // Retry after frame render in case parent not ready\n          _this.attachId = raf(function () {\n            _this.attachParent(popupContainer);\n          });\n        }\n      };\n      _this.getContainer = function () {\n        if (!_this.portalContainer) {\n          // In React.StrictMode component will call render multiple time in first mount.\n          // When you want to refactor with FC, useRef will also init multiple time and\n          // point to different useRef instance which will create multiple element\n          // (This multiple render will not trigger effect so you can not clean up this\n          // in effect). But this is safe with class component since it always point to same class instance.\n          var getDocument = _this.props.getDocument;\n          var popupContainer = getDocument(_this.getRootDomNode()).createElement('div'); // Make sure default popup container will never cause scrollbar appearing\n          // https://github.com/react-component/trigger/issues/41\n\n          popupContainer.style.position = 'absolute';\n          popupContainer.style.top = '0';\n          popupContainer.style.left = '0';\n          popupContainer.style.width = '100%';\n          _this.portalContainer = popupContainer;\n        }\n        _this.attachParent(_this.portalContainer);\n        return _this.portalContainer;\n      };\n      _this.setPoint = function (point) {\n        var alignPoint = _this.props.alignPoint;\n        if (!alignPoint || !point) return;\n        _this.setState({\n          point: {\n            pageX: point.pageX,\n            pageY: point.pageY\n          }\n        });\n      };\n      _this.handlePortalUpdate = function () {\n        if (_this.state.prevPopupVisible !== _this.state.popupVisible) {\n          _this.props.afterPopupVisibleChange(_this.state.popupVisible);\n        }\n      };\n      _this.triggerContextValue = {\n        onPopupMouseDown: _this.onPopupMouseDown\n      };\n      var _popupVisible;\n      if ('popupVisible' in props) {\n        _popupVisible = !!props.popupVisible;\n      } else {\n        _popupVisible = !!props.defaultPopupVisible;\n      }\n      _this.state = {\n        prevPopupVisible: _popupVisible,\n        popupVisible: _popupVisible\n      };\n      ALL_HANDLERS.forEach(function (h) {\n        _this[\"fire\".concat(h)] = function (e) {\n          _this.fireEvents(h, e);\n        };\n      });\n      return _this;\n    }\n    _createClass(Trigger, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.componentDidUpdate();\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate() {\n        var props = this.props;\n        var state = this.state; // We must listen to `mousedown` or `touchstart`, edge case:\n        // https://github.com/ant-design/ant-design/issues/5804\n        // https://github.com/react-component/calendar/issues/250\n        // https://github.com/react-component/trigger/issues/50\n\n        if (state.popupVisible) {\n          var currentDocument;\n          if (!this.clickOutsideHandler && (this.isClickToHide() || this.isContextMenuToShow())) {\n            currentDocument = props.getDocument(this.getRootDomNode());\n            this.clickOutsideHandler = addEventListener(currentDocument, 'mousedown', this.onDocumentClick);\n          } // always hide on mobile\n\n          if (!this.touchOutsideHandler) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.touchOutsideHandler = addEventListener(currentDocument, 'touchstart', this.onDocumentClick);\n          } // close popup when trigger type contains 'onContextMenu' and document is scrolling.\n\n          if (!this.contextMenuOutsideHandler1 && this.isContextMenuToShow()) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.contextMenuOutsideHandler1 = addEventListener(currentDocument, 'scroll', this.onContextMenuClose);\n          } // close popup when trigger type contains 'onContextMenu' and window is blur.\n\n          if (!this.contextMenuOutsideHandler2 && this.isContextMenuToShow()) {\n            this.contextMenuOutsideHandler2 = addEventListener(window, 'blur', this.onContextMenuClose);\n          }\n          return;\n        }\n        this.clearOutsideHandler();\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.clearDelayTimer();\n        this.clearOutsideHandler();\n        clearTimeout(this.mouseDownTimeout);\n        raf.cancel(this.attachId);\n      }\n    }, {\n      key: \"getPopupDomNode\",\n      value: function getPopupDomNode() {\n        var _this$popupRef$curren2;\n\n        // for test\n        return ((_this$popupRef$curren2 = this.popupRef.current) === null || _this$popupRef$curren2 === void 0 ? void 0 : _this$popupRef$curren2.getElement()) || null;\n      }\n    }, {\n      key: \"getPopupAlign\",\n      value: function getPopupAlign() {\n        var props = this.props;\n        var popupPlacement = props.popupPlacement,\n          popupAlign = props.popupAlign,\n          builtinPlacements = props.builtinPlacements;\n        if (popupPlacement && builtinPlacements) {\n          return getAlignFromPlacement(builtinPlacements, popupPlacement, popupAlign);\n        }\n        return popupAlign;\n      }\n    }, {\n      key: \"setPopupVisible\",\n      value:\n      /**\n       * @param popupVisible    Show or not the popup element\n       * @param event           SyntheticEvent, used for `pointAlign`\n       */\n      function setPopupVisible(popupVisible, event) {\n        var alignPoint = this.props.alignPoint;\n        var prevPopupVisible = this.state.popupVisible;\n        this.clearDelayTimer();\n        if (prevPopupVisible !== popupVisible) {\n          if (!('popupVisible' in this.props)) {\n            this.setState({\n              popupVisible: popupVisible,\n              prevPopupVisible: prevPopupVisible\n            });\n          }\n          this.props.onPopupVisibleChange(popupVisible);\n        } // Always record the point position since mouseEnterDelay will delay the show\n\n        if (alignPoint && event && popupVisible) {\n          this.setPoint(event);\n        }\n      }\n    }, {\n      key: \"delaySetPopupVisible\",\n      value: function delaySetPopupVisible(visible, delayS, event) {\n        var _this2 = this;\n        var delay = delayS * 1000;\n        this.clearDelayTimer();\n        if (delay) {\n          var point = event ? {\n            pageX: event.pageX,\n            pageY: event.pageY\n          } : null;\n          this.delayTimer = window.setTimeout(function () {\n            _this2.setPopupVisible(visible, point);\n            _this2.clearDelayTimer();\n          }, delay);\n        } else {\n          this.setPopupVisible(visible, event);\n        }\n      }\n    }, {\n      key: \"clearDelayTimer\",\n      value: function clearDelayTimer() {\n        if (this.delayTimer) {\n          clearTimeout(this.delayTimer);\n          this.delayTimer = null;\n        }\n      }\n    }, {\n      key: \"clearOutsideHandler\",\n      value: function clearOutsideHandler() {\n        if (this.clickOutsideHandler) {\n          this.clickOutsideHandler.remove();\n          this.clickOutsideHandler = null;\n        }\n        if (this.contextMenuOutsideHandler1) {\n          this.contextMenuOutsideHandler1.remove();\n          this.contextMenuOutsideHandler1 = null;\n        }\n        if (this.contextMenuOutsideHandler2) {\n          this.contextMenuOutsideHandler2.remove();\n          this.contextMenuOutsideHandler2 = null;\n        }\n        if (this.touchOutsideHandler) {\n          this.touchOutsideHandler.remove();\n          this.touchOutsideHandler = null;\n        }\n      }\n    }, {\n      key: \"createTwoChains\",\n      value: function createTwoChains(event) {\n        var childPros = this.props.children.props;\n        var props = this.props;\n        if (childPros[event] && props[event]) {\n          return this[\"fire\".concat(event)];\n        }\n        return childPros[event] || props[event];\n      }\n    }, {\n      key: \"isClickToShow\",\n      value: function isClickToShow() {\n        var _this$props4 = this.props,\n          action = _this$props4.action,\n          showAction = _this$props4.showAction;\n        return action.indexOf('click') !== -1 || showAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isContextMenuOnly\",\n      value: function isContextMenuOnly() {\n        var action = this.props.action;\n        return action === 'contextMenu' || action.length === 1 && action[0] === 'contextMenu';\n      }\n    }, {\n      key: \"isContextMenuToShow\",\n      value: function isContextMenuToShow() {\n        var _this$props5 = this.props,\n          action = _this$props5.action,\n          showAction = _this$props5.showAction;\n        return action.indexOf('contextMenu') !== -1 || showAction.indexOf('contextMenu') !== -1;\n      }\n    }, {\n      key: \"isClickToHide\",\n      value: function isClickToHide() {\n        var _this$props6 = this.props,\n          action = _this$props6.action,\n          hideAction = _this$props6.hideAction;\n        return action.indexOf('click') !== -1 || hideAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isMouseEnterToShow\",\n      value: function isMouseEnterToShow() {\n        var _this$props7 = this.props,\n          action = _this$props7.action,\n          showAction = _this$props7.showAction;\n        return action.indexOf('hover') !== -1 || showAction.indexOf('mouseEnter') !== -1;\n      }\n    }, {\n      key: \"isMouseLeaveToHide\",\n      value: function isMouseLeaveToHide() {\n        var _this$props8 = this.props,\n          action = _this$props8.action,\n          hideAction = _this$props8.hideAction;\n        return action.indexOf('hover') !== -1 || hideAction.indexOf('mouseLeave') !== -1;\n      }\n    }, {\n      key: \"isFocusToShow\",\n      value: function isFocusToShow() {\n        var _this$props9 = this.props,\n          action = _this$props9.action,\n          showAction = _this$props9.showAction;\n        return action.indexOf('focus') !== -1 || showAction.indexOf('focus') !== -1;\n      }\n    }, {\n      key: \"isBlurToHide\",\n      value: function isBlurToHide() {\n        var _this$props10 = this.props,\n          action = _this$props10.action,\n          hideAction = _this$props10.hideAction;\n        return action.indexOf('focus') !== -1 || hideAction.indexOf('blur') !== -1;\n      }\n    }, {\n      key: \"forcePopupAlign\",\n      value: function forcePopupAlign() {\n        if (this.state.popupVisible) {\n          var _this$popupRef$curren3;\n          (_this$popupRef$curren3 = this.popupRef.current) === null || _this$popupRef$curren3 === void 0 ? void 0 : _this$popupRef$curren3.forceAlign();\n        }\n      }\n    }, {\n      key: \"fireEvents\",\n      value: function fireEvents(type, e) {\n        var childCallback = this.props.children.props[type];\n        if (childCallback) {\n          childCallback(e);\n        }\n        var callback = this.props[type];\n        if (callback) {\n          callback(e);\n        }\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        this.setPopupVisible(false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var popupVisible = this.state.popupVisible;\n        var _this$props11 = this.props,\n          children = _this$props11.children,\n          forceRender = _this$props11.forceRender,\n          alignPoint = _this$props11.alignPoint,\n          className = _this$props11.className,\n          autoDestroy = _this$props11.autoDestroy;\n        var child = React.Children.only(children);\n        var newChildProps = {\n          key: 'trigger'\n        }; // ============================== Visible Handlers ==============================\n        // >>> ContextMenu\n\n        if (this.isContextMenuToShow()) {\n          newChildProps.onContextMenu = this.onContextMenu;\n        } else {\n          newChildProps.onContextMenu = this.createTwoChains('onContextMenu');\n        } // >>> Click\n\n        if (this.isClickToHide() || this.isClickToShow()) {\n          newChildProps.onClick = this.onClick;\n          newChildProps.onMouseDown = this.onMouseDown;\n          newChildProps.onTouchStart = this.onTouchStart;\n        } else {\n          newChildProps.onClick = this.createTwoChains('onClick');\n          newChildProps.onMouseDown = this.createTwoChains('onMouseDown');\n          newChildProps.onTouchStart = this.createTwoChains('onTouchStart');\n        } // >>> Hover(enter)\n\n        if (this.isMouseEnterToShow()) {\n          newChildProps.onMouseEnter = this.onMouseEnter; // Point align\n\n          if (alignPoint) {\n            newChildProps.onMouseMove = this.onMouseMove;\n          }\n        } else {\n          newChildProps.onMouseEnter = this.createTwoChains('onMouseEnter');\n        } // >>> Hover(leave)\n\n        if (this.isMouseLeaveToHide()) {\n          newChildProps.onMouseLeave = this.onMouseLeave;\n        } else {\n          newChildProps.onMouseLeave = this.createTwoChains('onMouseLeave');\n        } // >>> Focus\n\n        if (this.isFocusToShow() || this.isBlurToHide()) {\n          newChildProps.onFocus = this.onFocus;\n          newChildProps.onBlur = this.onBlur;\n        } else {\n          newChildProps.onFocus = this.createTwoChains('onFocus');\n          newChildProps.onBlur = this.createTwoChains('onBlur');\n        } // =================================== Render ===================================\n\n        var childrenClassName = classNames(child && child.props && child.props.className, className);\n        if (childrenClassName) {\n          newChildProps.className = childrenClassName;\n        }\n        var cloneProps = _objectSpread({}, newChildProps);\n        if (supportRef(child)) {\n          cloneProps.ref = composeRef(this.triggerRef, child.ref);\n        }\n        var trigger = /*#__PURE__*/React.cloneElement(child, cloneProps);\n        var portal; // prevent unmounting after it's rendered\n\n        if (popupVisible || this.popupRef.current || forceRender) {\n          portal = /*#__PURE__*/React.createElement(PortalComponent, {\n            key: \"portal\",\n            getContainer: this.getContainer,\n            didUpdate: this.handlePortalUpdate\n          }, this.getComponent());\n        }\n        if (!popupVisible && autoDestroy) {\n          portal = null;\n        }\n        return /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n          value: this.triggerContextValue\n        }, trigger, portal);\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref, prevState) {\n        var popupVisible = _ref.popupVisible;\n        var newState = {};\n        if (popupVisible !== undefined && prevState.popupVisible !== popupVisible) {\n          newState.popupVisible = popupVisible;\n          newState.prevPopupVisible = prevState.popupVisible;\n        }\n        return newState;\n      }\n    }]);\n    return Trigger;\n  }(React.Component);\n  Trigger.contextType = TriggerContext;\n  Trigger.defaultProps = {\n    prefixCls: 'rc-trigger-popup',\n    getPopupClassNameFromAlign: returnEmptyString,\n    getDocument: returnDocument,\n    onPopupVisibleChange: noop,\n    afterPopupVisibleChange: noop,\n    onPopupAlign: noop,\n    popupClassName: '',\n    mouseEnterDelay: 0,\n    mouseLeaveDelay: 0.1,\n    focusDelay: 0,\n    blurDelay: 0.15,\n    popupStyle: {},\n    destroyPopupOnHide: false,\n    popupAlign: {},\n    defaultPopupVisible: false,\n    mask: false,\n    maskClosable: true,\n    action: [],\n    showAction: [],\n    hideAction: [],\n    autoDestroy: false\n  };\n  return Trigger;\n}\nexport default generateTrigger(Portal);", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "React", "ReactDOM", "raf", "contains", "findDOMNode", "composeRef", "supportRef", "addEventListener", "Portal", "classNames", "getAlignFromPlacement", "getAlignPopupClassName", "Popup", "TriggerContext", "noop", "returnEmptyString", "returnDocument", "element", "ownerDocument", "window", "document", "ALL_HANDLERS", "generateTrigger", "PortalComponent", "<PERSON><PERSON>", "_React$Component", "_super", "props", "_this", "call", "popupRef", "createRef", "triggerRef", "portalContainer", "attachId", "clickOutsideHandler", "touchOutsideHandler", "contextMenuOutsideHandler1", "contextMenuOutsideHandler2", "mouseDownTimeout", "focusTime", "preClickTime", "preTouchTime", "delayTimer", "hasPopupMouseDown", "onMouseEnter", "e", "mouseEnterDelay", "fireEvents", "delaySetPopupVisible", "onMouseMove", "setPoint", "onMouseLeave", "mouseLeaveDelay", "onPopupMouseEnter", "clearDelayTimer", "onPopupMouseLeave", "_this$popupRef$curren", "relatedTarget", "setTimeout", "current", "getElement", "onFocus", "isFocusToShow", "Date", "now", "focusDelay", "onMouseDown", "onTouchStart", "onBlur", "isBlurToHide", "blurDelay", "onContextMenu", "preventDefault", "setPopupVisible", "onContextMenuClose", "isContextMenuToShow", "close", "onClick", "event", "preTime", "Math", "min", "abs", "isClickToShow", "isClickToHide", "nextVisible", "state", "popupVisible", "onPopupMouseDown", "clearTimeout", "context", "_this$context", "apply", "arguments", "onDocumentClick", "mask", "maskClosable", "target", "root", "getRootDomNode", "popupNode", "getPopupDomNode", "isContextMenuOnly", "getTriggerDOMNode", "domNode", "err", "getPopupClassNameFromAlign", "align", "className", "_this$props", "popupPlacement", "builtinPlacements", "prefixCls", "alignPoint", "push", "join", "getComponent", "_this$props2", "destroyPopupOnHide", "popupClassName", "onPopupAlign", "popupMotion", "popupAnimation", "popupTransitionName", "popupStyle", "maskAnimation", "maskTransitionName", "maskMotion", "zIndex", "popup", "stretch", "mobile", "forceRender", "_this$state", "point", "getPopupAlign", "mouseProps", "isMouseEnterToShow", "isMouseLeaveToHide", "createElement", "visible", "onAlign", "animation", "getClassNameFromAlign", "style", "transitionName", "ref", "motion", "attachParent", "popup<PERSON><PERSON><PERSON>", "cancel", "_this$props3", "getPopupContainer", "getDocument", "mountNode", "body", "length", "append<PERSON><PERSON><PERSON>", "getContainer", "position", "top", "left", "width", "setState", "pageX", "pageY", "handlePortalUpdate", "prevPopupVisible", "afterPopupVisibleChange", "triggerContextValue", "_popupVisible", "defaultPopupVisible", "for<PERSON>ach", "h", "concat", "key", "value", "componentDidMount", "componentDidUpdate", "currentDocument", "clearOutsideHandler", "componentWillUnmount", "_this$popupRef$curren2", "popupAlign", "onPopupVisibleChange", "delayS", "_this2", "delay", "remove", "createTwoChains", "child<PERSON><PERSON>", "children", "_this$props4", "action", "showAction", "indexOf", "_this$props5", "_this$props6", "hideAction", "_this$props7", "_this$props8", "_this$props9", "_this$props10", "forcePopupAlign", "_this$popupRef$curren3", "forceAlign", "type", "child<PERSON><PERSON><PERSON>", "callback", "render", "_this$props11", "autoDestroy", "child", "Children", "only", "newChildProps", "childrenClassName", "cloneProps", "trigger", "cloneElement", "portal", "didUpdate", "Provider", "getDerivedStateFromProps", "_ref", "prevState", "newState", "undefined", "Component", "contextType", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-trigger/es/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport raf from \"rc-util/es/raf\";\nimport contains from \"rc-util/es/Dom/contains\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport Portal from \"rc-util/es/Portal\";\nimport classNames from 'classnames';\nimport { getAlignFromPlacement, getAlignPopupClassName } from './utils/alignUtil';\nimport Popup from './Popup';\nimport TriggerContext from './context';\n\nfunction noop() {}\n\nfunction returnEmptyString() {\n  return '';\n}\n\nfunction returnDocument(element) {\n  if (element) {\n    return element.ownerDocument;\n  }\n\n  return window.document;\n}\n\nvar ALL_HANDLERS = ['onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur', 'onContextMenu'];\n/**\n * Internal usage. Do not use in your code since this will be removed.\n */\n\nexport function generateTrigger(PortalComponent) {\n  var Trigger = /*#__PURE__*/function (_React$Component) {\n    _inherits(Trigger, _React$Component);\n\n    var _super = _createSuper(Trigger);\n\n    // ensure `getContainer` will be called only once\n    function Trigger(props) {\n      var _this;\n\n      _classCallCheck(this, Trigger);\n\n      _this = _super.call(this, props);\n      _this.popupRef = /*#__PURE__*/React.createRef();\n      _this.triggerRef = /*#__PURE__*/React.createRef();\n      _this.portalContainer = void 0;\n      _this.attachId = void 0;\n      _this.clickOutsideHandler = void 0;\n      _this.touchOutsideHandler = void 0;\n      _this.contextMenuOutsideHandler1 = void 0;\n      _this.contextMenuOutsideHandler2 = void 0;\n      _this.mouseDownTimeout = void 0;\n      _this.focusTime = void 0;\n      _this.preClickTime = void 0;\n      _this.preTouchTime = void 0;\n      _this.delayTimer = void 0;\n      _this.hasPopupMouseDown = void 0;\n\n      _this.onMouseEnter = function (e) {\n        var mouseEnterDelay = _this.props.mouseEnterDelay;\n\n        _this.fireEvents('onMouseEnter', e);\n\n        _this.delaySetPopupVisible(true, mouseEnterDelay, mouseEnterDelay ? null : e);\n      };\n\n      _this.onMouseMove = function (e) {\n        _this.fireEvents('onMouseMove', e);\n\n        _this.setPoint(e);\n      };\n\n      _this.onMouseLeave = function (e) {\n        _this.fireEvents('onMouseLeave', e);\n\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      };\n\n      _this.onPopupMouseEnter = function () {\n        _this.clearDelayTimer();\n      };\n\n      _this.onPopupMouseLeave = function (e) {\n        var _this$popupRef$curren;\n\n        // https://github.com/react-component/trigger/pull/13\n        // react bug?\n        if (e.relatedTarget && !e.relatedTarget.setTimeout && contains((_this$popupRef$curren = _this.popupRef.current) === null || _this$popupRef$curren === void 0 ? void 0 : _this$popupRef$curren.getElement(), e.relatedTarget)) {\n          return;\n        }\n\n        _this.delaySetPopupVisible(false, _this.props.mouseLeaveDelay);\n      };\n\n      _this.onFocus = function (e) {\n        _this.fireEvents('onFocus', e); // incase focusin and focusout\n\n\n        _this.clearDelayTimer();\n\n        if (_this.isFocusToShow()) {\n          _this.focusTime = Date.now();\n\n          _this.delaySetPopupVisible(true, _this.props.focusDelay);\n        }\n      };\n\n      _this.onMouseDown = function (e) {\n        _this.fireEvents('onMouseDown', e);\n\n        _this.preClickTime = Date.now();\n      };\n\n      _this.onTouchStart = function (e) {\n        _this.fireEvents('onTouchStart', e);\n\n        _this.preTouchTime = Date.now();\n      };\n\n      _this.onBlur = function (e) {\n        _this.fireEvents('onBlur', e);\n\n        _this.clearDelayTimer();\n\n        if (_this.isBlurToHide()) {\n          _this.delaySetPopupVisible(false, _this.props.blurDelay);\n        }\n      };\n\n      _this.onContextMenu = function (e) {\n        e.preventDefault();\n\n        _this.fireEvents('onContextMenu', e);\n\n        _this.setPopupVisible(true, e);\n      };\n\n      _this.onContextMenuClose = function () {\n        if (_this.isContextMenuToShow()) {\n          _this.close();\n        }\n      };\n\n      _this.onClick = function (event) {\n        _this.fireEvents('onClick', event); // focus will trigger click\n\n\n        if (_this.focusTime) {\n          var preTime;\n\n          if (_this.preClickTime && _this.preTouchTime) {\n            preTime = Math.min(_this.preClickTime, _this.preTouchTime);\n          } else if (_this.preClickTime) {\n            preTime = _this.preClickTime;\n          } else if (_this.preTouchTime) {\n            preTime = _this.preTouchTime;\n          }\n\n          if (Math.abs(preTime - _this.focusTime) < 20) {\n            return;\n          }\n\n          _this.focusTime = 0;\n        }\n\n        _this.preClickTime = 0;\n        _this.preTouchTime = 0; // Only prevent default when all the action is click.\n        // https://github.com/ant-design/ant-design/issues/17043\n        // https://github.com/ant-design/ant-design/issues/17291\n\n        if (_this.isClickToShow() && (_this.isClickToHide() || _this.isBlurToHide()) && event && event.preventDefault) {\n          event.preventDefault();\n        }\n\n        var nextVisible = !_this.state.popupVisible;\n\n        if (_this.isClickToHide() && !nextVisible || nextVisible && _this.isClickToShow()) {\n          _this.setPopupVisible(!_this.state.popupVisible, event);\n        }\n      };\n\n      _this.onPopupMouseDown = function () {\n        _this.hasPopupMouseDown = true;\n        clearTimeout(_this.mouseDownTimeout);\n        _this.mouseDownTimeout = window.setTimeout(function () {\n          _this.hasPopupMouseDown = false;\n        }, 0);\n\n        if (_this.context) {\n          var _this$context;\n\n          (_this$context = _this.context).onPopupMouseDown.apply(_this$context, arguments);\n        }\n      };\n\n      _this.onDocumentClick = function (event) {\n        if (_this.props.mask && !_this.props.maskClosable) {\n          return;\n        }\n\n        var target = event.target;\n\n        var root = _this.getRootDomNode();\n\n        var popupNode = _this.getPopupDomNode();\n\n        if ( // mousedown on the target should also close popup when action is contextMenu.\n        // https://github.com/ant-design/ant-design/issues/29853\n        (!contains(root, target) || _this.isContextMenuOnly()) && !contains(popupNode, target) && !_this.hasPopupMouseDown) {\n          _this.close();\n        }\n      };\n\n      _this.getRootDomNode = function () {\n        var getTriggerDOMNode = _this.props.getTriggerDOMNode;\n\n        if (getTriggerDOMNode) {\n          return getTriggerDOMNode(_this.triggerRef.current);\n        }\n\n        try {\n          var domNode = findDOMNode(_this.triggerRef.current);\n\n          if (domNode) {\n            return domNode;\n          }\n        } catch (err) {// Do nothing\n        }\n\n        return ReactDOM.findDOMNode(_assertThisInitialized(_this));\n      };\n\n      _this.getPopupClassNameFromAlign = function (align) {\n        var className = [];\n        var _this$props = _this.props,\n            popupPlacement = _this$props.popupPlacement,\n            builtinPlacements = _this$props.builtinPlacements,\n            prefixCls = _this$props.prefixCls,\n            alignPoint = _this$props.alignPoint,\n            getPopupClassNameFromAlign = _this$props.getPopupClassNameFromAlign;\n\n        if (popupPlacement && builtinPlacements) {\n          className.push(getAlignPopupClassName(builtinPlacements, prefixCls, align, alignPoint));\n        }\n\n        if (getPopupClassNameFromAlign) {\n          className.push(getPopupClassNameFromAlign(align));\n        }\n\n        return className.join(' ');\n      };\n\n      _this.getComponent = function () {\n        var _this$props2 = _this.props,\n            prefixCls = _this$props2.prefixCls,\n            destroyPopupOnHide = _this$props2.destroyPopupOnHide,\n            popupClassName = _this$props2.popupClassName,\n            onPopupAlign = _this$props2.onPopupAlign,\n            popupMotion = _this$props2.popupMotion,\n            popupAnimation = _this$props2.popupAnimation,\n            popupTransitionName = _this$props2.popupTransitionName,\n            popupStyle = _this$props2.popupStyle,\n            mask = _this$props2.mask,\n            maskAnimation = _this$props2.maskAnimation,\n            maskTransitionName = _this$props2.maskTransitionName,\n            maskMotion = _this$props2.maskMotion,\n            zIndex = _this$props2.zIndex,\n            popup = _this$props2.popup,\n            stretch = _this$props2.stretch,\n            alignPoint = _this$props2.alignPoint,\n            mobile = _this$props2.mobile,\n            forceRender = _this$props2.forceRender;\n        var _this$state = _this.state,\n            popupVisible = _this$state.popupVisible,\n            point = _this$state.point;\n\n        var align = _this.getPopupAlign();\n\n        var mouseProps = {};\n\n        if (_this.isMouseEnterToShow()) {\n          mouseProps.onMouseEnter = _this.onPopupMouseEnter;\n        }\n\n        if (_this.isMouseLeaveToHide()) {\n          mouseProps.onMouseLeave = _this.onPopupMouseLeave;\n        }\n\n        mouseProps.onMouseDown = _this.onPopupMouseDown;\n        mouseProps.onTouchStart = _this.onPopupMouseDown;\n        return /*#__PURE__*/React.createElement(Popup, _extends({\n          prefixCls: prefixCls,\n          destroyPopupOnHide: destroyPopupOnHide,\n          visible: popupVisible,\n          point: alignPoint && point,\n          className: popupClassName,\n          align: align,\n          onAlign: onPopupAlign,\n          animation: popupAnimation,\n          getClassNameFromAlign: _this.getPopupClassNameFromAlign\n        }, mouseProps, {\n          stretch: stretch,\n          getRootDomNode: _this.getRootDomNode,\n          style: popupStyle,\n          mask: mask,\n          zIndex: zIndex,\n          transitionName: popupTransitionName,\n          maskAnimation: maskAnimation,\n          maskTransitionName: maskTransitionName,\n          maskMotion: maskMotion,\n          ref: _this.popupRef,\n          motion: popupMotion,\n          mobile: mobile,\n          forceRender: forceRender\n        }), typeof popup === 'function' ? popup() : popup);\n      };\n\n      _this.attachParent = function (popupContainer) {\n        raf.cancel(_this.attachId);\n        var _this$props3 = _this.props,\n            getPopupContainer = _this$props3.getPopupContainer,\n            getDocument = _this$props3.getDocument;\n\n        var domNode = _this.getRootDomNode();\n\n        var mountNode;\n\n        if (!getPopupContainer) {\n          mountNode = getDocument(_this.getRootDomNode()).body;\n        } else if (domNode || getPopupContainer.length === 0) {\n          // Compatible for legacy getPopupContainer with domNode argument.\n          // If no need `domNode` argument, will call directly.\n          // https://codesandbox.io/s/eloquent-mclean-ss93m?file=/src/App.js\n          mountNode = getPopupContainer(domNode);\n        }\n\n        if (mountNode) {\n          mountNode.appendChild(popupContainer);\n        } else {\n          // Retry after frame render in case parent not ready\n          _this.attachId = raf(function () {\n            _this.attachParent(popupContainer);\n          });\n        }\n      };\n\n      _this.getContainer = function () {\n        if (!_this.portalContainer) {\n          // In React.StrictMode component will call render multiple time in first mount.\n          // When you want to refactor with FC, useRef will also init multiple time and\n          // point to different useRef instance which will create multiple element\n          // (This multiple render will not trigger effect so you can not clean up this\n          // in effect). But this is safe with class component since it always point to same class instance.\n          var getDocument = _this.props.getDocument;\n          var popupContainer = getDocument(_this.getRootDomNode()).createElement('div'); // Make sure default popup container will never cause scrollbar appearing\n          // https://github.com/react-component/trigger/issues/41\n\n          popupContainer.style.position = 'absolute';\n          popupContainer.style.top = '0';\n          popupContainer.style.left = '0';\n          popupContainer.style.width = '100%';\n          _this.portalContainer = popupContainer;\n        }\n\n        _this.attachParent(_this.portalContainer);\n\n        return _this.portalContainer;\n      };\n\n      _this.setPoint = function (point) {\n        var alignPoint = _this.props.alignPoint;\n        if (!alignPoint || !point) return;\n\n        _this.setState({\n          point: {\n            pageX: point.pageX,\n            pageY: point.pageY\n          }\n        });\n      };\n\n      _this.handlePortalUpdate = function () {\n        if (_this.state.prevPopupVisible !== _this.state.popupVisible) {\n          _this.props.afterPopupVisibleChange(_this.state.popupVisible);\n        }\n      };\n\n      _this.triggerContextValue = {\n        onPopupMouseDown: _this.onPopupMouseDown\n      };\n\n      var _popupVisible;\n\n      if ('popupVisible' in props) {\n        _popupVisible = !!props.popupVisible;\n      } else {\n        _popupVisible = !!props.defaultPopupVisible;\n      }\n\n      _this.state = {\n        prevPopupVisible: _popupVisible,\n        popupVisible: _popupVisible\n      };\n      ALL_HANDLERS.forEach(function (h) {\n        _this[\"fire\".concat(h)] = function (e) {\n          _this.fireEvents(h, e);\n        };\n      });\n      return _this;\n    }\n\n    _createClass(Trigger, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.componentDidUpdate();\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate() {\n        var props = this.props;\n        var state = this.state; // We must listen to `mousedown` or `touchstart`, edge case:\n        // https://github.com/ant-design/ant-design/issues/5804\n        // https://github.com/react-component/calendar/issues/250\n        // https://github.com/react-component/trigger/issues/50\n\n        if (state.popupVisible) {\n          var currentDocument;\n\n          if (!this.clickOutsideHandler && (this.isClickToHide() || this.isContextMenuToShow())) {\n            currentDocument = props.getDocument(this.getRootDomNode());\n            this.clickOutsideHandler = addEventListener(currentDocument, 'mousedown', this.onDocumentClick);\n          } // always hide on mobile\n\n\n          if (!this.touchOutsideHandler) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.touchOutsideHandler = addEventListener(currentDocument, 'touchstart', this.onDocumentClick);\n          } // close popup when trigger type contains 'onContextMenu' and document is scrolling.\n\n\n          if (!this.contextMenuOutsideHandler1 && this.isContextMenuToShow()) {\n            currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n            this.contextMenuOutsideHandler1 = addEventListener(currentDocument, 'scroll', this.onContextMenuClose);\n          } // close popup when trigger type contains 'onContextMenu' and window is blur.\n\n\n          if (!this.contextMenuOutsideHandler2 && this.isContextMenuToShow()) {\n            this.contextMenuOutsideHandler2 = addEventListener(window, 'blur', this.onContextMenuClose);\n          }\n\n          return;\n        }\n\n        this.clearOutsideHandler();\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.clearDelayTimer();\n        this.clearOutsideHandler();\n        clearTimeout(this.mouseDownTimeout);\n        raf.cancel(this.attachId);\n      }\n    }, {\n      key: \"getPopupDomNode\",\n      value: function getPopupDomNode() {\n        var _this$popupRef$curren2;\n\n        // for test\n        return ((_this$popupRef$curren2 = this.popupRef.current) === null || _this$popupRef$curren2 === void 0 ? void 0 : _this$popupRef$curren2.getElement()) || null;\n      }\n    }, {\n      key: \"getPopupAlign\",\n      value: function getPopupAlign() {\n        var props = this.props;\n        var popupPlacement = props.popupPlacement,\n            popupAlign = props.popupAlign,\n            builtinPlacements = props.builtinPlacements;\n\n        if (popupPlacement && builtinPlacements) {\n          return getAlignFromPlacement(builtinPlacements, popupPlacement, popupAlign);\n        }\n\n        return popupAlign;\n      }\n    }, {\n      key: \"setPopupVisible\",\n      value:\n      /**\n       * @param popupVisible    Show or not the popup element\n       * @param event           SyntheticEvent, used for `pointAlign`\n       */\n      function setPopupVisible(popupVisible, event) {\n        var alignPoint = this.props.alignPoint;\n        var prevPopupVisible = this.state.popupVisible;\n        this.clearDelayTimer();\n\n        if (prevPopupVisible !== popupVisible) {\n          if (!('popupVisible' in this.props)) {\n            this.setState({\n              popupVisible: popupVisible,\n              prevPopupVisible: prevPopupVisible\n            });\n          }\n\n          this.props.onPopupVisibleChange(popupVisible);\n        } // Always record the point position since mouseEnterDelay will delay the show\n\n\n        if (alignPoint && event && popupVisible) {\n          this.setPoint(event);\n        }\n      }\n    }, {\n      key: \"delaySetPopupVisible\",\n      value: function delaySetPopupVisible(visible, delayS, event) {\n        var _this2 = this;\n\n        var delay = delayS * 1000;\n        this.clearDelayTimer();\n\n        if (delay) {\n          var point = event ? {\n            pageX: event.pageX,\n            pageY: event.pageY\n          } : null;\n          this.delayTimer = window.setTimeout(function () {\n            _this2.setPopupVisible(visible, point);\n\n            _this2.clearDelayTimer();\n          }, delay);\n        } else {\n          this.setPopupVisible(visible, event);\n        }\n      }\n    }, {\n      key: \"clearDelayTimer\",\n      value: function clearDelayTimer() {\n        if (this.delayTimer) {\n          clearTimeout(this.delayTimer);\n          this.delayTimer = null;\n        }\n      }\n    }, {\n      key: \"clearOutsideHandler\",\n      value: function clearOutsideHandler() {\n        if (this.clickOutsideHandler) {\n          this.clickOutsideHandler.remove();\n          this.clickOutsideHandler = null;\n        }\n\n        if (this.contextMenuOutsideHandler1) {\n          this.contextMenuOutsideHandler1.remove();\n          this.contextMenuOutsideHandler1 = null;\n        }\n\n        if (this.contextMenuOutsideHandler2) {\n          this.contextMenuOutsideHandler2.remove();\n          this.contextMenuOutsideHandler2 = null;\n        }\n\n        if (this.touchOutsideHandler) {\n          this.touchOutsideHandler.remove();\n          this.touchOutsideHandler = null;\n        }\n      }\n    }, {\n      key: \"createTwoChains\",\n      value: function createTwoChains(event) {\n        var childPros = this.props.children.props;\n        var props = this.props;\n\n        if (childPros[event] && props[event]) {\n          return this[\"fire\".concat(event)];\n        }\n\n        return childPros[event] || props[event];\n      }\n    }, {\n      key: \"isClickToShow\",\n      value: function isClickToShow() {\n        var _this$props4 = this.props,\n            action = _this$props4.action,\n            showAction = _this$props4.showAction;\n        return action.indexOf('click') !== -1 || showAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isContextMenuOnly\",\n      value: function isContextMenuOnly() {\n        var action = this.props.action;\n        return action === 'contextMenu' || action.length === 1 && action[0] === 'contextMenu';\n      }\n    }, {\n      key: \"isContextMenuToShow\",\n      value: function isContextMenuToShow() {\n        var _this$props5 = this.props,\n            action = _this$props5.action,\n            showAction = _this$props5.showAction;\n        return action.indexOf('contextMenu') !== -1 || showAction.indexOf('contextMenu') !== -1;\n      }\n    }, {\n      key: \"isClickToHide\",\n      value: function isClickToHide() {\n        var _this$props6 = this.props,\n            action = _this$props6.action,\n            hideAction = _this$props6.hideAction;\n        return action.indexOf('click') !== -1 || hideAction.indexOf('click') !== -1;\n      }\n    }, {\n      key: \"isMouseEnterToShow\",\n      value: function isMouseEnterToShow() {\n        var _this$props7 = this.props,\n            action = _this$props7.action,\n            showAction = _this$props7.showAction;\n        return action.indexOf('hover') !== -1 || showAction.indexOf('mouseEnter') !== -1;\n      }\n    }, {\n      key: \"isMouseLeaveToHide\",\n      value: function isMouseLeaveToHide() {\n        var _this$props8 = this.props,\n            action = _this$props8.action,\n            hideAction = _this$props8.hideAction;\n        return action.indexOf('hover') !== -1 || hideAction.indexOf('mouseLeave') !== -1;\n      }\n    }, {\n      key: \"isFocusToShow\",\n      value: function isFocusToShow() {\n        var _this$props9 = this.props,\n            action = _this$props9.action,\n            showAction = _this$props9.showAction;\n        return action.indexOf('focus') !== -1 || showAction.indexOf('focus') !== -1;\n      }\n    }, {\n      key: \"isBlurToHide\",\n      value: function isBlurToHide() {\n        var _this$props10 = this.props,\n            action = _this$props10.action,\n            hideAction = _this$props10.hideAction;\n        return action.indexOf('focus') !== -1 || hideAction.indexOf('blur') !== -1;\n      }\n    }, {\n      key: \"forcePopupAlign\",\n      value: function forcePopupAlign() {\n        if (this.state.popupVisible) {\n          var _this$popupRef$curren3;\n\n          (_this$popupRef$curren3 = this.popupRef.current) === null || _this$popupRef$curren3 === void 0 ? void 0 : _this$popupRef$curren3.forceAlign();\n        }\n      }\n    }, {\n      key: \"fireEvents\",\n      value: function fireEvents(type, e) {\n        var childCallback = this.props.children.props[type];\n\n        if (childCallback) {\n          childCallback(e);\n        }\n\n        var callback = this.props[type];\n\n        if (callback) {\n          callback(e);\n        }\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        this.setPopupVisible(false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var popupVisible = this.state.popupVisible;\n        var _this$props11 = this.props,\n            children = _this$props11.children,\n            forceRender = _this$props11.forceRender,\n            alignPoint = _this$props11.alignPoint,\n            className = _this$props11.className,\n            autoDestroy = _this$props11.autoDestroy;\n        var child = React.Children.only(children);\n        var newChildProps = {\n          key: 'trigger'\n        }; // ============================== Visible Handlers ==============================\n        // >>> ContextMenu\n\n        if (this.isContextMenuToShow()) {\n          newChildProps.onContextMenu = this.onContextMenu;\n        } else {\n          newChildProps.onContextMenu = this.createTwoChains('onContextMenu');\n        } // >>> Click\n\n\n        if (this.isClickToHide() || this.isClickToShow()) {\n          newChildProps.onClick = this.onClick;\n          newChildProps.onMouseDown = this.onMouseDown;\n          newChildProps.onTouchStart = this.onTouchStart;\n        } else {\n          newChildProps.onClick = this.createTwoChains('onClick');\n          newChildProps.onMouseDown = this.createTwoChains('onMouseDown');\n          newChildProps.onTouchStart = this.createTwoChains('onTouchStart');\n        } // >>> Hover(enter)\n\n\n        if (this.isMouseEnterToShow()) {\n          newChildProps.onMouseEnter = this.onMouseEnter; // Point align\n\n          if (alignPoint) {\n            newChildProps.onMouseMove = this.onMouseMove;\n          }\n        } else {\n          newChildProps.onMouseEnter = this.createTwoChains('onMouseEnter');\n        } // >>> Hover(leave)\n\n\n        if (this.isMouseLeaveToHide()) {\n          newChildProps.onMouseLeave = this.onMouseLeave;\n        } else {\n          newChildProps.onMouseLeave = this.createTwoChains('onMouseLeave');\n        } // >>> Focus\n\n\n        if (this.isFocusToShow() || this.isBlurToHide()) {\n          newChildProps.onFocus = this.onFocus;\n          newChildProps.onBlur = this.onBlur;\n        } else {\n          newChildProps.onFocus = this.createTwoChains('onFocus');\n          newChildProps.onBlur = this.createTwoChains('onBlur');\n        } // =================================== Render ===================================\n\n\n        var childrenClassName = classNames(child && child.props && child.props.className, className);\n\n        if (childrenClassName) {\n          newChildProps.className = childrenClassName;\n        }\n\n        var cloneProps = _objectSpread({}, newChildProps);\n\n        if (supportRef(child)) {\n          cloneProps.ref = composeRef(this.triggerRef, child.ref);\n        }\n\n        var trigger = /*#__PURE__*/React.cloneElement(child, cloneProps);\n        var portal; // prevent unmounting after it's rendered\n\n        if (popupVisible || this.popupRef.current || forceRender) {\n          portal = /*#__PURE__*/React.createElement(PortalComponent, {\n            key: \"portal\",\n            getContainer: this.getContainer,\n            didUpdate: this.handlePortalUpdate\n          }, this.getComponent());\n        }\n\n        if (!popupVisible && autoDestroy) {\n          portal = null;\n        }\n\n        return /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n          value: this.triggerContextValue\n        }, trigger, portal);\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref, prevState) {\n        var popupVisible = _ref.popupVisible;\n        var newState = {};\n\n        if (popupVisible !== undefined && prevState.popupVisible !== popupVisible) {\n          newState.popupVisible = popupVisible;\n          newState.prevPopupVisible = prevState.popupVisible;\n        }\n\n        return newState;\n      }\n    }]);\n\n    return Trigger;\n  }(React.Component);\n\n  Trigger.contextType = TriggerContext;\n  Trigger.defaultProps = {\n    prefixCls: 'rc-trigger-popup',\n    getPopupClassNameFromAlign: returnEmptyString,\n    getDocument: returnDocument,\n    onPopupVisibleChange: noop,\n    afterPopupVisibleChange: noop,\n    onPopupAlign: noop,\n    popupClassName: '',\n    mouseEnterDelay: 0,\n    mouseLeaveDelay: 0.1,\n    focusDelay: 0,\n    blurDelay: 0.15,\n    popupStyle: {},\n    destroyPopupOnHide: false,\n    popupAlign: {},\n    defaultPopupVisible: false,\n    mask: false,\n    maskClosable: true,\n    action: [],\n    showAction: [],\n    hideAction: [],\n    autoDestroy: false\n  };\n  return Trigger;\n}\nexport default generateTrigger(Portal);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACvD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,qBAAqB,EAAEC,sBAAsB,QAAQ,mBAAmB;AACjF,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,MAAM,WAAW;AAEtC,SAASC,IAAIA,CAAA,EAAG,CAAC;AAEjB,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,OAAO,EAAE;AACX;AAEA,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAIA,OAAO,EAAE;IACX,OAAOA,OAAO,CAACC,aAAa;EAC9B;EAEA,OAAOC,MAAM,CAACC,QAAQ;AACxB;AAEA,IAAIC,YAAY,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,CAAC;AACnI;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,eAAe,EAAE;EAC/C,IAAIC,OAAO,GAAG,aAAa,UAAUC,gBAAgB,EAAE;IACrD3B,SAAS,CAAC0B,OAAO,EAAEC,gBAAgB,CAAC;IAEpC,IAAIC,MAAM,GAAG3B,YAAY,CAACyB,OAAO,CAAC;;IAElC;IACA,SAASA,OAAOA,CAACG,KAAK,EAAE;MACtB,IAAIC,KAAK;MAETjC,eAAe,CAAC,IAAI,EAAE6B,OAAO,CAAC;MAE9BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;MAChCC,KAAK,CAACE,QAAQ,GAAG,aAAa9B,KAAK,CAAC+B,SAAS,CAAC,CAAC;MAC/CH,KAAK,CAACI,UAAU,GAAG,aAAahC,KAAK,CAAC+B,SAAS,CAAC,CAAC;MACjDH,KAAK,CAACK,eAAe,GAAG,KAAK,CAAC;MAC9BL,KAAK,CAACM,QAAQ,GAAG,KAAK,CAAC;MACvBN,KAAK,CAACO,mBAAmB,GAAG,KAAK,CAAC;MAClCP,KAAK,CAACQ,mBAAmB,GAAG,KAAK,CAAC;MAClCR,KAAK,CAACS,0BAA0B,GAAG,KAAK,CAAC;MACzCT,KAAK,CAACU,0BAA0B,GAAG,KAAK,CAAC;MACzCV,KAAK,CAACW,gBAAgB,GAAG,KAAK,CAAC;MAC/BX,KAAK,CAACY,SAAS,GAAG,KAAK,CAAC;MACxBZ,KAAK,CAACa,YAAY,GAAG,KAAK,CAAC;MAC3Bb,KAAK,CAACc,YAAY,GAAG,KAAK,CAAC;MAC3Bd,KAAK,CAACe,UAAU,GAAG,KAAK,CAAC;MACzBf,KAAK,CAACgB,iBAAiB,GAAG,KAAK,CAAC;MAEhChB,KAAK,CAACiB,YAAY,GAAG,UAAUC,CAAC,EAAE;QAChC,IAAIC,eAAe,GAAGnB,KAAK,CAACD,KAAK,CAACoB,eAAe;QAEjDnB,KAAK,CAACoB,UAAU,CAAC,cAAc,EAAEF,CAAC,CAAC;QAEnClB,KAAK,CAACqB,oBAAoB,CAAC,IAAI,EAAEF,eAAe,EAAEA,eAAe,GAAG,IAAI,GAAGD,CAAC,CAAC;MAC/E,CAAC;MAEDlB,KAAK,CAACsB,WAAW,GAAG,UAAUJ,CAAC,EAAE;QAC/BlB,KAAK,CAACoB,UAAU,CAAC,aAAa,EAAEF,CAAC,CAAC;QAElClB,KAAK,CAACuB,QAAQ,CAACL,CAAC,CAAC;MACnB,CAAC;MAEDlB,KAAK,CAACwB,YAAY,GAAG,UAAUN,CAAC,EAAE;QAChClB,KAAK,CAACoB,UAAU,CAAC,cAAc,EAAEF,CAAC,CAAC;QAEnClB,KAAK,CAACqB,oBAAoB,CAAC,KAAK,EAAErB,KAAK,CAACD,KAAK,CAAC0B,eAAe,CAAC;MAChE,CAAC;MAEDzB,KAAK,CAAC0B,iBAAiB,GAAG,YAAY;QACpC1B,KAAK,CAAC2B,eAAe,CAAC,CAAC;MACzB,CAAC;MAED3B,KAAK,CAAC4B,iBAAiB,GAAG,UAAUV,CAAC,EAAE;QACrC,IAAIW,qBAAqB;;QAEzB;QACA;QACA,IAAIX,CAAC,CAACY,aAAa,IAAI,CAACZ,CAAC,CAACY,aAAa,CAACC,UAAU,IAAIxD,QAAQ,CAAC,CAACsD,qBAAqB,GAAG7B,KAAK,CAACE,QAAQ,CAAC8B,OAAO,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,UAAU,CAAC,CAAC,EAAEf,CAAC,CAACY,aAAa,CAAC,EAAE;UAC5N;QACF;QAEA9B,KAAK,CAACqB,oBAAoB,CAAC,KAAK,EAAErB,KAAK,CAACD,KAAK,CAAC0B,eAAe,CAAC;MAChE,CAAC;MAEDzB,KAAK,CAACkC,OAAO,GAAG,UAAUhB,CAAC,EAAE;QAC3BlB,KAAK,CAACoB,UAAU,CAAC,SAAS,EAAEF,CAAC,CAAC,CAAC,CAAC;;QAGhClB,KAAK,CAAC2B,eAAe,CAAC,CAAC;QAEvB,IAAI3B,KAAK,CAACmC,aAAa,CAAC,CAAC,EAAE;UACzBnC,KAAK,CAACY,SAAS,GAAGwB,IAAI,CAACC,GAAG,CAAC,CAAC;UAE5BrC,KAAK,CAACqB,oBAAoB,CAAC,IAAI,EAAErB,KAAK,CAACD,KAAK,CAACuC,UAAU,CAAC;QAC1D;MACF,CAAC;MAEDtC,KAAK,CAACuC,WAAW,GAAG,UAAUrB,CAAC,EAAE;QAC/BlB,KAAK,CAACoB,UAAU,CAAC,aAAa,EAAEF,CAAC,CAAC;QAElClB,KAAK,CAACa,YAAY,GAAGuB,IAAI,CAACC,GAAG,CAAC,CAAC;MACjC,CAAC;MAEDrC,KAAK,CAACwC,YAAY,GAAG,UAAUtB,CAAC,EAAE;QAChClB,KAAK,CAACoB,UAAU,CAAC,cAAc,EAAEF,CAAC,CAAC;QAEnClB,KAAK,CAACc,YAAY,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC;MACjC,CAAC;MAEDrC,KAAK,CAACyC,MAAM,GAAG,UAAUvB,CAAC,EAAE;QAC1BlB,KAAK,CAACoB,UAAU,CAAC,QAAQ,EAAEF,CAAC,CAAC;QAE7BlB,KAAK,CAAC2B,eAAe,CAAC,CAAC;QAEvB,IAAI3B,KAAK,CAAC0C,YAAY,CAAC,CAAC,EAAE;UACxB1C,KAAK,CAACqB,oBAAoB,CAAC,KAAK,EAAErB,KAAK,CAACD,KAAK,CAAC4C,SAAS,CAAC;QAC1D;MACF,CAAC;MAED3C,KAAK,CAAC4C,aAAa,GAAG,UAAU1B,CAAC,EAAE;QACjCA,CAAC,CAAC2B,cAAc,CAAC,CAAC;QAElB7C,KAAK,CAACoB,UAAU,CAAC,eAAe,EAAEF,CAAC,CAAC;QAEpClB,KAAK,CAAC8C,eAAe,CAAC,IAAI,EAAE5B,CAAC,CAAC;MAChC,CAAC;MAEDlB,KAAK,CAAC+C,kBAAkB,GAAG,YAAY;QACrC,IAAI/C,KAAK,CAACgD,mBAAmB,CAAC,CAAC,EAAE;UAC/BhD,KAAK,CAACiD,KAAK,CAAC,CAAC;QACf;MACF,CAAC;MAEDjD,KAAK,CAACkD,OAAO,GAAG,UAAUC,KAAK,EAAE;QAC/BnD,KAAK,CAACoB,UAAU,CAAC,SAAS,EAAE+B,KAAK,CAAC,CAAC,CAAC;;QAGpC,IAAInD,KAAK,CAACY,SAAS,EAAE;UACnB,IAAIwC,OAAO;UAEX,IAAIpD,KAAK,CAACa,YAAY,IAAIb,KAAK,CAACc,YAAY,EAAE;YAC5CsC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACtD,KAAK,CAACa,YAAY,EAAEb,KAAK,CAACc,YAAY,CAAC;UAC5D,CAAC,MAAM,IAAId,KAAK,CAACa,YAAY,EAAE;YAC7BuC,OAAO,GAAGpD,KAAK,CAACa,YAAY;UAC9B,CAAC,MAAM,IAAIb,KAAK,CAACc,YAAY,EAAE;YAC7BsC,OAAO,GAAGpD,KAAK,CAACc,YAAY;UAC9B;UAEA,IAAIuC,IAAI,CAACE,GAAG,CAACH,OAAO,GAAGpD,KAAK,CAACY,SAAS,CAAC,GAAG,EAAE,EAAE;YAC5C;UACF;UAEAZ,KAAK,CAACY,SAAS,GAAG,CAAC;QACrB;QAEAZ,KAAK,CAACa,YAAY,GAAG,CAAC;QACtBb,KAAK,CAACc,YAAY,GAAG,CAAC,CAAC,CAAC;QACxB;QACA;;QAEA,IAAId,KAAK,CAACwD,aAAa,CAAC,CAAC,KAAKxD,KAAK,CAACyD,aAAa,CAAC,CAAC,IAAIzD,KAAK,CAAC0C,YAAY,CAAC,CAAC,CAAC,IAAIS,KAAK,IAAIA,KAAK,CAACN,cAAc,EAAE;UAC7GM,KAAK,CAACN,cAAc,CAAC,CAAC;QACxB;QAEA,IAAIa,WAAW,GAAG,CAAC1D,KAAK,CAAC2D,KAAK,CAACC,YAAY;QAE3C,IAAI5D,KAAK,CAACyD,aAAa,CAAC,CAAC,IAAI,CAACC,WAAW,IAAIA,WAAW,IAAI1D,KAAK,CAACwD,aAAa,CAAC,CAAC,EAAE;UACjFxD,KAAK,CAAC8C,eAAe,CAAC,CAAC9C,KAAK,CAAC2D,KAAK,CAACC,YAAY,EAAET,KAAK,CAAC;QACzD;MACF,CAAC;MAEDnD,KAAK,CAAC6D,gBAAgB,GAAG,YAAY;QACnC7D,KAAK,CAACgB,iBAAiB,GAAG,IAAI;QAC9B8C,YAAY,CAAC9D,KAAK,CAACW,gBAAgB,CAAC;QACpCX,KAAK,CAACW,gBAAgB,GAAGpB,MAAM,CAACwC,UAAU,CAAC,YAAY;UACrD/B,KAAK,CAACgB,iBAAiB,GAAG,KAAK;QACjC,CAAC,EAAE,CAAC,CAAC;QAEL,IAAIhB,KAAK,CAAC+D,OAAO,EAAE;UACjB,IAAIC,aAAa;UAEjB,CAACA,aAAa,GAAGhE,KAAK,CAAC+D,OAAO,EAAEF,gBAAgB,CAACI,KAAK,CAACD,aAAa,EAAEE,SAAS,CAAC;QAClF;MACF,CAAC;MAEDlE,KAAK,CAACmE,eAAe,GAAG,UAAUhB,KAAK,EAAE;QACvC,IAAInD,KAAK,CAACD,KAAK,CAACqE,IAAI,IAAI,CAACpE,KAAK,CAACD,KAAK,CAACsE,YAAY,EAAE;UACjD;QACF;QAEA,IAAIC,MAAM,GAAGnB,KAAK,CAACmB,MAAM;QAEzB,IAAIC,IAAI,GAAGvE,KAAK,CAACwE,cAAc,CAAC,CAAC;QAEjC,IAAIC,SAAS,GAAGzE,KAAK,CAAC0E,eAAe,CAAC,CAAC;QAEvC;QAAK;QACL;QACA,CAAC,CAACnG,QAAQ,CAACgG,IAAI,EAAED,MAAM,CAAC,IAAItE,KAAK,CAAC2E,iBAAiB,CAAC,CAAC,KAAK,CAACpG,QAAQ,CAACkG,SAAS,EAAEH,MAAM,CAAC,IAAI,CAACtE,KAAK,CAACgB,iBAAiB,EAAE;UAClHhB,KAAK,CAACiD,KAAK,CAAC,CAAC;QACf;MACF,CAAC;MAEDjD,KAAK,CAACwE,cAAc,GAAG,YAAY;QACjC,IAAII,iBAAiB,GAAG5E,KAAK,CAACD,KAAK,CAAC6E,iBAAiB;QAErD,IAAIA,iBAAiB,EAAE;UACrB,OAAOA,iBAAiB,CAAC5E,KAAK,CAACI,UAAU,CAAC4B,OAAO,CAAC;QACpD;QAEA,IAAI;UACF,IAAI6C,OAAO,GAAGrG,WAAW,CAACwB,KAAK,CAACI,UAAU,CAAC4B,OAAO,CAAC;UAEnD,IAAI6C,OAAO,EAAE;YACX,OAAOA,OAAO;UAChB;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE,CAAC;QAAA;QAGf,OAAOzG,QAAQ,CAACG,WAAW,CAACP,sBAAsB,CAAC+B,KAAK,CAAC,CAAC;MAC5D,CAAC;MAEDA,KAAK,CAAC+E,0BAA0B,GAAG,UAAUC,KAAK,EAAE;QAClD,IAAIC,SAAS,GAAG,EAAE;QAClB,IAAIC,WAAW,GAAGlF,KAAK,CAACD,KAAK;UACzBoF,cAAc,GAAGD,WAAW,CAACC,cAAc;UAC3CC,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB;UACjDC,SAAS,GAAGH,WAAW,CAACG,SAAS;UACjCC,UAAU,GAAGJ,WAAW,CAACI,UAAU;UACnCP,0BAA0B,GAAGG,WAAW,CAACH,0BAA0B;QAEvE,IAAII,cAAc,IAAIC,iBAAiB,EAAE;UACvCH,SAAS,CAACM,IAAI,CAACxG,sBAAsB,CAACqG,iBAAiB,EAAEC,SAAS,EAAEL,KAAK,EAAEM,UAAU,CAAC,CAAC;QACzF;QAEA,IAAIP,0BAA0B,EAAE;UAC9BE,SAAS,CAACM,IAAI,CAACR,0BAA0B,CAACC,KAAK,CAAC,CAAC;QACnD;QAEA,OAAOC,SAAS,CAACO,IAAI,CAAC,GAAG,CAAC;MAC5B,CAAC;MAEDxF,KAAK,CAACyF,YAAY,GAAG,YAAY;QAC/B,IAAIC,YAAY,GAAG1F,KAAK,CAACD,KAAK;UAC1BsF,SAAS,GAAGK,YAAY,CAACL,SAAS;UAClCM,kBAAkB,GAAGD,YAAY,CAACC,kBAAkB;UACpDC,cAAc,GAAGF,YAAY,CAACE,cAAc;UAC5CC,YAAY,GAAGH,YAAY,CAACG,YAAY;UACxCC,WAAW,GAAGJ,YAAY,CAACI,WAAW;UACtCC,cAAc,GAAGL,YAAY,CAACK,cAAc;UAC5CC,mBAAmB,GAAGN,YAAY,CAACM,mBAAmB;UACtDC,UAAU,GAAGP,YAAY,CAACO,UAAU;UACpC7B,IAAI,GAAGsB,YAAY,CAACtB,IAAI;UACxB8B,aAAa,GAAGR,YAAY,CAACQ,aAAa;UAC1CC,kBAAkB,GAAGT,YAAY,CAACS,kBAAkB;UACpDC,UAAU,GAAGV,YAAY,CAACU,UAAU;UACpCC,MAAM,GAAGX,YAAY,CAACW,MAAM;UAC5BC,KAAK,GAAGZ,YAAY,CAACY,KAAK;UAC1BC,OAAO,GAAGb,YAAY,CAACa,OAAO;UAC9BjB,UAAU,GAAGI,YAAY,CAACJ,UAAU;UACpCkB,MAAM,GAAGd,YAAY,CAACc,MAAM;UAC5BC,WAAW,GAAGf,YAAY,CAACe,WAAW;QAC1C,IAAIC,WAAW,GAAG1G,KAAK,CAAC2D,KAAK;UACzBC,YAAY,GAAG8C,WAAW,CAAC9C,YAAY;UACvC+C,KAAK,GAAGD,WAAW,CAACC,KAAK;QAE7B,IAAI3B,KAAK,GAAGhF,KAAK,CAAC4G,aAAa,CAAC,CAAC;QAEjC,IAAIC,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI7G,KAAK,CAAC8G,kBAAkB,CAAC,CAAC,EAAE;UAC9BD,UAAU,CAAC5F,YAAY,GAAGjB,KAAK,CAAC0B,iBAAiB;QACnD;QAEA,IAAI1B,KAAK,CAAC+G,kBAAkB,CAAC,CAAC,EAAE;UAC9BF,UAAU,CAACrF,YAAY,GAAGxB,KAAK,CAAC4B,iBAAiB;QACnD;QAEAiF,UAAU,CAACtE,WAAW,GAAGvC,KAAK,CAAC6D,gBAAgB;QAC/CgD,UAAU,CAACrE,YAAY,GAAGxC,KAAK,CAAC6D,gBAAgB;QAChD,OAAO,aAAazF,KAAK,CAAC4I,aAAa,CAAChI,KAAK,EAAElB,QAAQ,CAAC;UACtDuH,SAAS,EAAEA,SAAS;UACpBM,kBAAkB,EAAEA,kBAAkB;UACtCsB,OAAO,EAAErD,YAAY;UACrB+C,KAAK,EAAErB,UAAU,IAAIqB,KAAK;UAC1B1B,SAAS,EAAEW,cAAc;UACzBZ,KAAK,EAAEA,KAAK;UACZkC,OAAO,EAAErB,YAAY;UACrBsB,SAAS,EAAEpB,cAAc;UACzBqB,qBAAqB,EAAEpH,KAAK,CAAC+E;QAC/B,CAAC,EAAE8B,UAAU,EAAE;UACbN,OAAO,EAAEA,OAAO;UAChB/B,cAAc,EAAExE,KAAK,CAACwE,cAAc;UACpC6C,KAAK,EAAEpB,UAAU;UACjB7B,IAAI,EAAEA,IAAI;UACViC,MAAM,EAAEA,MAAM;UACdiB,cAAc,EAAEtB,mBAAmB;UACnCE,aAAa,EAAEA,aAAa;UAC5BC,kBAAkB,EAAEA,kBAAkB;UACtCC,UAAU,EAAEA,UAAU;UACtBmB,GAAG,EAAEvH,KAAK,CAACE,QAAQ;UACnBsH,MAAM,EAAE1B,WAAW;UACnBU,MAAM,EAAEA,MAAM;UACdC,WAAW,EAAEA;QACf,CAAC,CAAC,EAAE,OAAOH,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK,CAAC;MACpD,CAAC;MAEDtG,KAAK,CAACyH,YAAY,GAAG,UAAUC,cAAc,EAAE;QAC7CpJ,GAAG,CAACqJ,MAAM,CAAC3H,KAAK,CAACM,QAAQ,CAAC;QAC1B,IAAIsH,YAAY,GAAG5H,KAAK,CAACD,KAAK;UAC1B8H,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;UAClDC,WAAW,GAAGF,YAAY,CAACE,WAAW;QAE1C,IAAIjD,OAAO,GAAG7E,KAAK,CAACwE,cAAc,CAAC,CAAC;QAEpC,IAAIuD,SAAS;QAEb,IAAI,CAACF,iBAAiB,EAAE;UACtBE,SAAS,GAAGD,WAAW,CAAC9H,KAAK,CAACwE,cAAc,CAAC,CAAC,CAAC,CAACwD,IAAI;QACtD,CAAC,MAAM,IAAInD,OAAO,IAAIgD,iBAAiB,CAACI,MAAM,KAAK,CAAC,EAAE;UACpD;UACA;UACA;UACAF,SAAS,GAAGF,iBAAiB,CAAChD,OAAO,CAAC;QACxC;QAEA,IAAIkD,SAAS,EAAE;UACbA,SAAS,CAACG,WAAW,CAACR,cAAc,CAAC;QACvC,CAAC,MAAM;UACL;UACA1H,KAAK,CAACM,QAAQ,GAAGhC,GAAG,CAAC,YAAY;YAC/B0B,KAAK,CAACyH,YAAY,CAACC,cAAc,CAAC;UACpC,CAAC,CAAC;QACJ;MACF,CAAC;MAED1H,KAAK,CAACmI,YAAY,GAAG,YAAY;QAC/B,IAAI,CAACnI,KAAK,CAACK,eAAe,EAAE;UAC1B;UACA;UACA;UACA;UACA;UACA,IAAIyH,WAAW,GAAG9H,KAAK,CAACD,KAAK,CAAC+H,WAAW;UACzC,IAAIJ,cAAc,GAAGI,WAAW,CAAC9H,KAAK,CAACwE,cAAc,CAAC,CAAC,CAAC,CAACwC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;UAC/E;;UAEAU,cAAc,CAACL,KAAK,CAACe,QAAQ,GAAG,UAAU;UAC1CV,cAAc,CAACL,KAAK,CAACgB,GAAG,GAAG,GAAG;UAC9BX,cAAc,CAACL,KAAK,CAACiB,IAAI,GAAG,GAAG;UAC/BZ,cAAc,CAACL,KAAK,CAACkB,KAAK,GAAG,MAAM;UACnCvI,KAAK,CAACK,eAAe,GAAGqH,cAAc;QACxC;QAEA1H,KAAK,CAACyH,YAAY,CAACzH,KAAK,CAACK,eAAe,CAAC;QAEzC,OAAOL,KAAK,CAACK,eAAe;MAC9B,CAAC;MAEDL,KAAK,CAACuB,QAAQ,GAAG,UAAUoF,KAAK,EAAE;QAChC,IAAIrB,UAAU,GAAGtF,KAAK,CAACD,KAAK,CAACuF,UAAU;QACvC,IAAI,CAACA,UAAU,IAAI,CAACqB,KAAK,EAAE;QAE3B3G,KAAK,CAACwI,QAAQ,CAAC;UACb7B,KAAK,EAAE;YACL8B,KAAK,EAAE9B,KAAK,CAAC8B,KAAK;YAClBC,KAAK,EAAE/B,KAAK,CAAC+B;UACf;QACF,CAAC,CAAC;MACJ,CAAC;MAED1I,KAAK,CAAC2I,kBAAkB,GAAG,YAAY;QACrC,IAAI3I,KAAK,CAAC2D,KAAK,CAACiF,gBAAgB,KAAK5I,KAAK,CAAC2D,KAAK,CAACC,YAAY,EAAE;UAC7D5D,KAAK,CAACD,KAAK,CAAC8I,uBAAuB,CAAC7I,KAAK,CAAC2D,KAAK,CAACC,YAAY,CAAC;QAC/D;MACF,CAAC;MAED5D,KAAK,CAAC8I,mBAAmB,GAAG;QAC1BjF,gBAAgB,EAAE7D,KAAK,CAAC6D;MAC1B,CAAC;MAED,IAAIkF,aAAa;MAEjB,IAAI,cAAc,IAAIhJ,KAAK,EAAE;QAC3BgJ,aAAa,GAAG,CAAC,CAAChJ,KAAK,CAAC6D,YAAY;MACtC,CAAC,MAAM;QACLmF,aAAa,GAAG,CAAC,CAAChJ,KAAK,CAACiJ,mBAAmB;MAC7C;MAEAhJ,KAAK,CAAC2D,KAAK,GAAG;QACZiF,gBAAgB,EAAEG,aAAa;QAC/BnF,YAAY,EAAEmF;MAChB,CAAC;MACDtJ,YAAY,CAACwJ,OAAO,CAAC,UAAUC,CAAC,EAAE;QAChClJ,KAAK,CAAC,MAAM,CAACmJ,MAAM,CAACD,CAAC,CAAC,CAAC,GAAG,UAAUhI,CAAC,EAAE;UACrClB,KAAK,CAACoB,UAAU,CAAC8H,CAAC,EAAEhI,CAAC,CAAC;QACxB,CAAC;MACH,CAAC,CAAC;MACF,OAAOlB,KAAK;IACd;IAEAhC,YAAY,CAAC4B,OAAO,EAAE,CAAC;MACrBwJ,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;QAClC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC3B;IACF,CAAC,EAAE;MACDH,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,SAASE,kBAAkBA,CAAA,EAAG;QACnC,IAAIxJ,KAAK,GAAG,IAAI,CAACA,KAAK;QACtB,IAAI4D,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;QACxB;QACA;QACA;;QAEA,IAAIA,KAAK,CAACC,YAAY,EAAE;UACtB,IAAI4F,eAAe;UAEnB,IAAI,CAAC,IAAI,CAACjJ,mBAAmB,KAAK,IAAI,CAACkD,aAAa,CAAC,CAAC,IAAI,IAAI,CAACT,mBAAmB,CAAC,CAAC,CAAC,EAAE;YACrFwG,eAAe,GAAGzJ,KAAK,CAAC+H,WAAW,CAAC,IAAI,CAACtD,cAAc,CAAC,CAAC,CAAC;YAC1D,IAAI,CAACjE,mBAAmB,GAAG5B,gBAAgB,CAAC6K,eAAe,EAAE,WAAW,EAAE,IAAI,CAACrF,eAAe,CAAC;UACjG,CAAC,CAAC;;UAGF,IAAI,CAAC,IAAI,CAAC3D,mBAAmB,EAAE;YAC7BgJ,eAAe,GAAGA,eAAe,IAAIzJ,KAAK,CAAC+H,WAAW,CAAC,IAAI,CAACtD,cAAc,CAAC,CAAC,CAAC;YAC7E,IAAI,CAAChE,mBAAmB,GAAG7B,gBAAgB,CAAC6K,eAAe,EAAE,YAAY,EAAE,IAAI,CAACrF,eAAe,CAAC;UAClG,CAAC,CAAC;;UAGF,IAAI,CAAC,IAAI,CAAC1D,0BAA0B,IAAI,IAAI,CAACuC,mBAAmB,CAAC,CAAC,EAAE;YAClEwG,eAAe,GAAGA,eAAe,IAAIzJ,KAAK,CAAC+H,WAAW,CAAC,IAAI,CAACtD,cAAc,CAAC,CAAC,CAAC;YAC7E,IAAI,CAAC/D,0BAA0B,GAAG9B,gBAAgB,CAAC6K,eAAe,EAAE,QAAQ,EAAE,IAAI,CAACzG,kBAAkB,CAAC;UACxG,CAAC,CAAC;;UAGF,IAAI,CAAC,IAAI,CAACrC,0BAA0B,IAAI,IAAI,CAACsC,mBAAmB,CAAC,CAAC,EAAE;YAClE,IAAI,CAACtC,0BAA0B,GAAG/B,gBAAgB,CAACY,MAAM,EAAE,MAAM,EAAE,IAAI,CAACwD,kBAAkB,CAAC;UAC7F;UAEA;QACF;QAEA,IAAI,CAAC0G,mBAAmB,CAAC,CAAC;MAC5B;IACF,CAAC,EAAE;MACDL,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE,SAASK,oBAAoBA,CAAA,EAAG;QACrC,IAAI,CAAC/H,eAAe,CAAC,CAAC;QACtB,IAAI,CAAC8H,mBAAmB,CAAC,CAAC;QAC1B3F,YAAY,CAAC,IAAI,CAACnD,gBAAgB,CAAC;QACnCrC,GAAG,CAACqJ,MAAM,CAAC,IAAI,CAACrH,QAAQ,CAAC;MAC3B;IACF,CAAC,EAAE;MACD8I,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAAS3E,eAAeA,CAAA,EAAG;QAChC,IAAIiF,sBAAsB;;QAE1B;QACA,OAAO,CAAC,CAACA,sBAAsB,GAAG,IAAI,CAACzJ,QAAQ,CAAC8B,OAAO,MAAM,IAAI,IAAI2H,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAAC1H,UAAU,CAAC,CAAC,KAAK,IAAI;MAChK;IACF,CAAC,EAAE;MACDmH,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAASzC,aAAaA,CAAA,EAAG;QAC9B,IAAI7G,KAAK,GAAG,IAAI,CAACA,KAAK;QACtB,IAAIoF,cAAc,GAAGpF,KAAK,CAACoF,cAAc;UACrCyE,UAAU,GAAG7J,KAAK,CAAC6J,UAAU;UAC7BxE,iBAAiB,GAAGrF,KAAK,CAACqF,iBAAiB;QAE/C,IAAID,cAAc,IAAIC,iBAAiB,EAAE;UACvC,OAAOtG,qBAAqB,CAACsG,iBAAiB,EAAED,cAAc,EAAEyE,UAAU,CAAC;QAC7E;QAEA,OAAOA,UAAU;MACnB;IACF,CAAC,EAAE;MACDR,GAAG,EAAE,iBAAiB;MACtBC,KAAK;MACL;AACN;AACA;AACA;MACM,SAASvG,eAAeA,CAACc,YAAY,EAAET,KAAK,EAAE;QAC5C,IAAImC,UAAU,GAAG,IAAI,CAACvF,KAAK,CAACuF,UAAU;QACtC,IAAIsD,gBAAgB,GAAG,IAAI,CAACjF,KAAK,CAACC,YAAY;QAC9C,IAAI,CAACjC,eAAe,CAAC,CAAC;QAEtB,IAAIiH,gBAAgB,KAAKhF,YAAY,EAAE;UACrC,IAAI,EAAE,cAAc,IAAI,IAAI,CAAC7D,KAAK,CAAC,EAAE;YACnC,IAAI,CAACyI,QAAQ,CAAC;cACZ5E,YAAY,EAAEA,YAAY;cAC1BgF,gBAAgB,EAAEA;YACpB,CAAC,CAAC;UACJ;UAEA,IAAI,CAAC7I,KAAK,CAAC8J,oBAAoB,CAACjG,YAAY,CAAC;QAC/C,CAAC,CAAC;;QAGF,IAAI0B,UAAU,IAAInC,KAAK,IAAIS,YAAY,EAAE;UACvC,IAAI,CAACrC,QAAQ,CAAC4B,KAAK,CAAC;QACtB;MACF;IACF,CAAC,EAAE;MACDiG,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE,SAAShI,oBAAoBA,CAAC4F,OAAO,EAAE6C,MAAM,EAAE3G,KAAK,EAAE;QAC3D,IAAI4G,MAAM,GAAG,IAAI;QAEjB,IAAIC,KAAK,GAAGF,MAAM,GAAG,IAAI;QACzB,IAAI,CAACnI,eAAe,CAAC,CAAC;QAEtB,IAAIqI,KAAK,EAAE;UACT,IAAIrD,KAAK,GAAGxD,KAAK,GAAG;YAClBsF,KAAK,EAAEtF,KAAK,CAACsF,KAAK;YAClBC,KAAK,EAAEvF,KAAK,CAACuF;UACf,CAAC,GAAG,IAAI;UACR,IAAI,CAAC3H,UAAU,GAAGxB,MAAM,CAACwC,UAAU,CAAC,YAAY;YAC9CgI,MAAM,CAACjH,eAAe,CAACmE,OAAO,EAAEN,KAAK,CAAC;YAEtCoD,MAAM,CAACpI,eAAe,CAAC,CAAC;UAC1B,CAAC,EAAEqI,KAAK,CAAC;QACX,CAAC,MAAM;UACL,IAAI,CAAClH,eAAe,CAACmE,OAAO,EAAE9D,KAAK,CAAC;QACtC;MACF;IACF,CAAC,EAAE;MACDiG,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAAS1H,eAAeA,CAAA,EAAG;QAChC,IAAI,IAAI,CAACZ,UAAU,EAAE;UACnB+C,YAAY,CAAC,IAAI,CAAC/C,UAAU,CAAC;UAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;QACxB;MACF;IACF,CAAC,EAAE;MACDqI,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE,SAASI,mBAAmBA,CAAA,EAAG;QACpC,IAAI,IAAI,CAAClJ,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAAC0J,MAAM,CAAC,CAAC;UACjC,IAAI,CAAC1J,mBAAmB,GAAG,IAAI;QACjC;QAEA,IAAI,IAAI,CAACE,0BAA0B,EAAE;UACnC,IAAI,CAACA,0BAA0B,CAACwJ,MAAM,CAAC,CAAC;UACxC,IAAI,CAACxJ,0BAA0B,GAAG,IAAI;QACxC;QAEA,IAAI,IAAI,CAACC,0BAA0B,EAAE;UACnC,IAAI,CAACA,0BAA0B,CAACuJ,MAAM,CAAC,CAAC;UACxC,IAAI,CAACvJ,0BAA0B,GAAG,IAAI;QACxC;QAEA,IAAI,IAAI,CAACF,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACyJ,MAAM,CAAC,CAAC;UACjC,IAAI,CAACzJ,mBAAmB,GAAG,IAAI;QACjC;MACF;IACF,CAAC,EAAE;MACD4I,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAASa,eAAeA,CAAC/G,KAAK,EAAE;QACrC,IAAIgH,SAAS,GAAG,IAAI,CAACpK,KAAK,CAACqK,QAAQ,CAACrK,KAAK;QACzC,IAAIA,KAAK,GAAG,IAAI,CAACA,KAAK;QAEtB,IAAIoK,SAAS,CAAChH,KAAK,CAAC,IAAIpD,KAAK,CAACoD,KAAK,CAAC,EAAE;UACpC,OAAO,IAAI,CAAC,MAAM,CAACgG,MAAM,CAAChG,KAAK,CAAC,CAAC;QACnC;QAEA,OAAOgH,SAAS,CAAChH,KAAK,CAAC,IAAIpD,KAAK,CAACoD,KAAK,CAAC;MACzC;IACF,CAAC,EAAE;MACDiG,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAAS7F,aAAaA,CAAA,EAAG;QAC9B,IAAI6G,YAAY,GAAG,IAAI,CAACtK,KAAK;UACzBuK,MAAM,GAAGD,YAAY,CAACC,MAAM;UAC5BC,UAAU,GAAGF,YAAY,CAACE,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MAC7E;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,SAAS1E,iBAAiBA,CAAA,EAAG;QAClC,IAAI2F,MAAM,GAAG,IAAI,CAACvK,KAAK,CAACuK,MAAM;QAC9B,OAAOA,MAAM,KAAK,aAAa,IAAIA,MAAM,CAACrC,MAAM,KAAK,CAAC,IAAIqC,MAAM,CAAC,CAAC,CAAC,KAAK,aAAa;MACvF;IACF,CAAC,EAAE;MACDlB,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE,SAASrG,mBAAmBA,CAAA,EAAG;QACpC,IAAIyH,YAAY,GAAG,IAAI,CAAC1K,KAAK;UACzBuK,MAAM,GAAGG,YAAY,CAACH,MAAM;UAC5BC,UAAU,GAAGE,YAAY,CAACF,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;MACzF;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAAS5F,aAAaA,CAAA,EAAG;QAC9B,IAAIiH,YAAY,GAAG,IAAI,CAAC3K,KAAK;UACzBuK,MAAM,GAAGI,YAAY,CAACJ,MAAM;UAC5BK,UAAU,GAAGD,YAAY,CAACC,UAAU;QACxC,OAAOL,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIG,UAAU,CAACH,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MAC7E;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,SAASvC,kBAAkBA,CAAA,EAAG;QACnC,IAAI8D,YAAY,GAAG,IAAI,CAAC7K,KAAK;UACzBuK,MAAM,GAAGM,YAAY,CAACN,MAAM;UAC5BC,UAAU,GAAGK,YAAY,CAACL,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;MAClF;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,SAAStC,kBAAkBA,CAAA,EAAG;QACnC,IAAI8D,YAAY,GAAG,IAAI,CAAC9K,KAAK;UACzBuK,MAAM,GAAGO,YAAY,CAACP,MAAM;UAC5BK,UAAU,GAAGE,YAAY,CAACF,UAAU;QACxC,OAAOL,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIG,UAAU,CAACH,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;MAClF;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,SAASlH,aAAaA,CAAA,EAAG;QAC9B,IAAI2I,YAAY,GAAG,IAAI,CAAC/K,KAAK;UACzBuK,MAAM,GAAGQ,YAAY,CAACR,MAAM;UAC5BC,UAAU,GAAGO,YAAY,CAACP,UAAU;QACxC,OAAOD,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAID,UAAU,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MAC7E;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE,SAAS3G,YAAYA,CAAA,EAAG;QAC7B,IAAIqI,aAAa,GAAG,IAAI,CAAChL,KAAK;UAC1BuK,MAAM,GAAGS,aAAa,CAACT,MAAM;UAC7BK,UAAU,GAAGI,aAAa,CAACJ,UAAU;QACzC,OAAOL,MAAM,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIG,UAAU,CAACH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;MAC5E;IACF,CAAC,EAAE;MACDpB,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,SAAS2B,eAAeA,CAAA,EAAG;QAChC,IAAI,IAAI,CAACrH,KAAK,CAACC,YAAY,EAAE;UAC3B,IAAIqH,sBAAsB;UAE1B,CAACA,sBAAsB,GAAG,IAAI,CAAC/K,QAAQ,CAAC8B,OAAO,MAAM,IAAI,IAAIiJ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACC,UAAU,CAAC,CAAC;QAC/I;MACF;IACF,CAAC,EAAE;MACD9B,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE,SAASjI,UAAUA,CAAC+J,IAAI,EAAEjK,CAAC,EAAE;QAClC,IAAIkK,aAAa,GAAG,IAAI,CAACrL,KAAK,CAACqK,QAAQ,CAACrK,KAAK,CAACoL,IAAI,CAAC;QAEnD,IAAIC,aAAa,EAAE;UACjBA,aAAa,CAAClK,CAAC,CAAC;QAClB;QAEA,IAAImK,QAAQ,GAAG,IAAI,CAACtL,KAAK,CAACoL,IAAI,CAAC;QAE/B,IAAIE,QAAQ,EAAE;UACZA,QAAQ,CAACnK,CAAC,CAAC;QACb;MACF;IACF,CAAC,EAAE;MACDkI,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE,SAASpG,KAAKA,CAAA,EAAG;QACtB,IAAI,CAACH,eAAe,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC,EAAE;MACDsG,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;QACvB,IAAI1H,YAAY,GAAG,IAAI,CAACD,KAAK,CAACC,YAAY;QAC1C,IAAI2H,aAAa,GAAG,IAAI,CAACxL,KAAK;UAC1BqK,QAAQ,GAAGmB,aAAa,CAACnB,QAAQ;UACjC3D,WAAW,GAAG8E,aAAa,CAAC9E,WAAW;UACvCnB,UAAU,GAAGiG,aAAa,CAACjG,UAAU;UACrCL,SAAS,GAAGsG,aAAa,CAACtG,SAAS;UACnCuG,WAAW,GAAGD,aAAa,CAACC,WAAW;QAC3C,IAAIC,KAAK,GAAGrN,KAAK,CAACsN,QAAQ,CAACC,IAAI,CAACvB,QAAQ,CAAC;QACzC,IAAIwB,aAAa,GAAG;UAClBxC,GAAG,EAAE;QACP,CAAC,CAAC,CAAC;QACH;;QAEA,IAAI,IAAI,CAACpG,mBAAmB,CAAC,CAAC,EAAE;UAC9B4I,aAAa,CAAChJ,aAAa,GAAG,IAAI,CAACA,aAAa;QAClD,CAAC,MAAM;UACLgJ,aAAa,CAAChJ,aAAa,GAAG,IAAI,CAACsH,eAAe,CAAC,eAAe,CAAC;QACrE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAACzG,aAAa,CAAC,CAAC,IAAI,IAAI,CAACD,aAAa,CAAC,CAAC,EAAE;UAChDoI,aAAa,CAAC1I,OAAO,GAAG,IAAI,CAACA,OAAO;UACpC0I,aAAa,CAACrJ,WAAW,GAAG,IAAI,CAACA,WAAW;UAC5CqJ,aAAa,CAACpJ,YAAY,GAAG,IAAI,CAACA,YAAY;QAChD,CAAC,MAAM;UACLoJ,aAAa,CAAC1I,OAAO,GAAG,IAAI,CAACgH,eAAe,CAAC,SAAS,CAAC;UACvD0B,aAAa,CAACrJ,WAAW,GAAG,IAAI,CAAC2H,eAAe,CAAC,aAAa,CAAC;UAC/D0B,aAAa,CAACpJ,YAAY,GAAG,IAAI,CAAC0H,eAAe,CAAC,cAAc,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAACpD,kBAAkB,CAAC,CAAC,EAAE;UAC7B8E,aAAa,CAAC3K,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC;;UAEhD,IAAIqE,UAAU,EAAE;YACdsG,aAAa,CAACtK,WAAW,GAAG,IAAI,CAACA,WAAW;UAC9C;QACF,CAAC,MAAM;UACLsK,aAAa,CAAC3K,YAAY,GAAG,IAAI,CAACiJ,eAAe,CAAC,cAAc,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAACnD,kBAAkB,CAAC,CAAC,EAAE;UAC7B6E,aAAa,CAACpK,YAAY,GAAG,IAAI,CAACA,YAAY;QAChD,CAAC,MAAM;UACLoK,aAAa,CAACpK,YAAY,GAAG,IAAI,CAAC0I,eAAe,CAAC,cAAc,CAAC;QACnE,CAAC,CAAC;;QAGF,IAAI,IAAI,CAAC/H,aAAa,CAAC,CAAC,IAAI,IAAI,CAACO,YAAY,CAAC,CAAC,EAAE;UAC/CkJ,aAAa,CAAC1J,OAAO,GAAG,IAAI,CAACA,OAAO;UACpC0J,aAAa,CAACnJ,MAAM,GAAG,IAAI,CAACA,MAAM;QACpC,CAAC,MAAM;UACLmJ,aAAa,CAAC1J,OAAO,GAAG,IAAI,CAACgI,eAAe,CAAC,SAAS,CAAC;UACvD0B,aAAa,CAACnJ,MAAM,GAAG,IAAI,CAACyH,eAAe,CAAC,QAAQ,CAAC;QACvD,CAAC,CAAC;;QAGF,IAAI2B,iBAAiB,GAAGhN,UAAU,CAAC4M,KAAK,IAAIA,KAAK,CAAC1L,KAAK,IAAI0L,KAAK,CAAC1L,KAAK,CAACkF,SAAS,EAAEA,SAAS,CAAC;QAE5F,IAAI4G,iBAAiB,EAAE;UACrBD,aAAa,CAAC3G,SAAS,GAAG4G,iBAAiB;QAC7C;QAEA,IAAIC,UAAU,GAAGjO,aAAa,CAAC,CAAC,CAAC,EAAE+N,aAAa,CAAC;QAEjD,IAAIlN,UAAU,CAAC+M,KAAK,CAAC,EAAE;UACrBK,UAAU,CAACvE,GAAG,GAAG9I,UAAU,CAAC,IAAI,CAAC2B,UAAU,EAAEqL,KAAK,CAAClE,GAAG,CAAC;QACzD;QAEA,IAAIwE,OAAO,GAAG,aAAa3N,KAAK,CAAC4N,YAAY,CAACP,KAAK,EAAEK,UAAU,CAAC;QAChE,IAAIG,MAAM,CAAC,CAAC;;QAEZ,IAAIrI,YAAY,IAAI,IAAI,CAAC1D,QAAQ,CAAC8B,OAAO,IAAIyE,WAAW,EAAE;UACxDwF,MAAM,GAAG,aAAa7N,KAAK,CAAC4I,aAAa,CAACrH,eAAe,EAAE;YACzDyJ,GAAG,EAAE,QAAQ;YACbjB,YAAY,EAAE,IAAI,CAACA,YAAY;YAC/B+D,SAAS,EAAE,IAAI,CAACvD;UAClB,CAAC,EAAE,IAAI,CAAClD,YAAY,CAAC,CAAC,CAAC;QACzB;QAEA,IAAI,CAAC7B,YAAY,IAAI4H,WAAW,EAAE;UAChCS,MAAM,GAAG,IAAI;QACf;QAEA,OAAO,aAAa7N,KAAK,CAAC4I,aAAa,CAAC/H,cAAc,CAACkN,QAAQ,EAAE;UAC/D9C,KAAK,EAAE,IAAI,CAACP;QACd,CAAC,EAAEiD,OAAO,EAAEE,MAAM,CAAC;MACrB;IACF,CAAC,CAAC,EAAE,CAAC;MACH7C,GAAG,EAAE,0BAA0B;MAC/BC,KAAK,EAAE,SAAS+C,wBAAwBA,CAACC,IAAI,EAAEC,SAAS,EAAE;QACxD,IAAI1I,YAAY,GAAGyI,IAAI,CAACzI,YAAY;QACpC,IAAI2I,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAI3I,YAAY,KAAK4I,SAAS,IAAIF,SAAS,CAAC1I,YAAY,KAAKA,YAAY,EAAE;UACzE2I,QAAQ,CAAC3I,YAAY,GAAGA,YAAY;UACpC2I,QAAQ,CAAC3D,gBAAgB,GAAG0D,SAAS,CAAC1I,YAAY;QACpD;QAEA,OAAO2I,QAAQ;MACjB;IACF,CAAC,CAAC,CAAC;IAEH,OAAO3M,OAAO;EAChB,CAAC,CAACxB,KAAK,CAACqO,SAAS,CAAC;EAElB7M,OAAO,CAAC8M,WAAW,GAAGzN,cAAc;EACpCW,OAAO,CAAC+M,YAAY,GAAG;IACrBtH,SAAS,EAAE,kBAAkB;IAC7BN,0BAA0B,EAAE5F,iBAAiB;IAC7C2I,WAAW,EAAE1I,cAAc;IAC3ByK,oBAAoB,EAAE3K,IAAI;IAC1B2J,uBAAuB,EAAE3J,IAAI;IAC7B2G,YAAY,EAAE3G,IAAI;IAClB0G,cAAc,EAAE,EAAE;IAClBzE,eAAe,EAAE,CAAC;IAClBM,eAAe,EAAE,GAAG;IACpBa,UAAU,EAAE,CAAC;IACbK,SAAS,EAAE,IAAI;IACfsD,UAAU,EAAE,CAAC,CAAC;IACdN,kBAAkB,EAAE,KAAK;IACzBiE,UAAU,EAAE,CAAC,CAAC;IACdZ,mBAAmB,EAAE,KAAK;IAC1B5E,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,IAAI;IAClBiG,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdI,UAAU,EAAE,EAAE;IACda,WAAW,EAAE;EACf,CAAC;EACD,OAAO5L,OAAO;AAChB;AACA,eAAeF,eAAe,CAACd,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}