{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\traduttore\\\\traduttore.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport i18next from 'i18next';\nimport cookies from 'js-cookie';\nimport classNames from 'classnames';\nimport 'flag-icon-css/css/flag-icon.min.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst languages = [{\n  code: 'it',\n  name: 'Italiano',\n  country_code: 'it'\n}, {\n  code: 'en',\n  name: 'English',\n  country_code: 'gb'\n}];\nexport function Traduttore() {\n  _s();\n  const currentLanguageCode = cookies.get('i18next') || 'it';\n  const currentLanguage = languages.find(l => l.code === currentLanguageCode);\n  const {\n    t\n  } = useTranslation();\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {}, [currentLanguage, t]);\n  //Definisco le icone per le varie lingue\n  const GlobeIcon = () => {\n    document.documentElement.lang = currentLanguageCode; // <---- this line right here\n    if (currentLanguageCode === 'it') {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"flag-icon flag-icon-\".concat(languages[0].country_code, \" mx-2\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"flag-icon flag-icon-\".concat(languages[1].country_code, \" mx-2\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"language-select mr-2\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-end align-items-center language-select-root\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dropdown\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-link dropdown-toggle\",\n          type: \"button\",\n          id: \"dropdownMenuButton1\",\n          \"data-toggle\": \"dropdown\",\n          \"aria-expanded\": \"false\",\n          children: /*#__PURE__*/_jsxDEV(GlobeIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"dropdown-menu dropdown-menu-right\",\n          \"aria-labelledby\": \"dropdownMenuButton1\",\n          children: languages.map(_ref => {\n            let {\n              code,\n              name,\n              country_code\n            } = _ref;\n            return /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: classNames('abandiera dropdown-item', {\n                  disabled: currentLanguageCode === code\n                }),\n                onClick: () => {\n                  i18next.changeLanguage(code);\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flag-icon flag-icon-\".concat(country_code, \" mr-2\"),\n                  style: {\n                    opacity: currentLanguageCode === code ? 0.5 : 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this), name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this)\n            }, country_code, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(Traduttore, \"yIHkV3cUFHCxFeD16vLIxDL2JDQ=\", false, function () {\n  return [useTranslation];\n});\n_c = Traduttore;\nvar _c;\n$RefreshReg$(_c, \"Traduttore\");", "map": {"version": 3, "names": ["React", "useEffect", "useTranslation", "i18next", "cookies", "classNames", "jsxDEV", "_jsxDEV", "languages", "code", "name", "country_code", "<PERSON><PERSON><PERSON><PERSON>", "_s", "currentLanguageCode", "get", "currentLanguage", "find", "l", "t", "GlobeIcon", "document", "documentElement", "lang", "className", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "type", "id", "map", "_ref", "disabled", "onClick", "changeLanguage", "style", "opacity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/traduttore/traduttore.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport i18next from 'i18next';\nimport cookies from 'js-cookie';\nimport classNames from 'classnames';\nimport 'flag-icon-css/css/flag-icon.min.css';\n\nconst languages = [\n  {\n    code: 'it',\n    name: 'Italiano',\n    country_code: 'it',\n  },\n  {\n    code: 'en',\n    name: 'English',\n    country_code: 'gb',\n  },\n]\n\nexport function Traduttore() {\n  const currentLanguageCode = cookies.get('i18next') || 'it'\n  const currentLanguage = languages.find((l) => l.code === currentLanguageCode)\n  const { t } = useTranslation()\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n  }, [currentLanguage, t])\n  //Definisco le icone per le varie lingue\n  const GlobeIcon = () => {\n    document.documentElement.lang = currentLanguageCode  // <---- this line right here\n    if (currentLanguageCode === 'it') {\n      return (\n        <span className={`flag-icon flag-icon-${languages[0].country_code} mx-2`}></span>\n      )\n    } else {\n      return (\n        <span className={`flag-icon flag-icon-${languages[1].country_code} mx-2`}></span>\n      )\n    }\n  }\n  return (\n    <div className=\"language-select mr-2\">\n      <div className=\"d-flex justify-content-end align-items-center language-select-root\">\n        <div className=\"dropdown\">\n          <button\n            className=\"btn btn-link dropdown-toggle\"\n            type=\"button\"\n            id=\"dropdownMenuButton1\"\n            data-toggle=\"dropdown\"\n            aria-expanded=\"false\"\n          >\n            <GlobeIcon />\n          </button>\n          <ul className=\"dropdown-menu dropdown-menu-right\" aria-labelledby=\"dropdownMenuButton1\">\n            {languages.map(({ code, name, country_code }) => (\n              <li key={country_code}>\n                <span\n                  className={classNames('abandiera dropdown-item', {\n                    disabled: currentLanguageCode === code,\n                  })}\n                  onClick={() => {\n                    i18next.changeLanguage(code)\n                  }}\n                >\n                  <span\n                    className={`flag-icon flag-icon-${country_code} mr-2`}\n                    style={{\n                      opacity: currentLanguageCode === code ? 0.5 : 1,\n                    }}\n                  ></span>\n                  {name}\n                </span>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,SAAS,GAAG,CAChB;EACEC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAE;AAChB,CAAC,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,SAAS;EACfC,YAAY,EAAE;AAChB,CAAC,CACF;AAED,OAAO,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAMC,mBAAmB,GAAGV,OAAO,CAACW,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI;EAC1D,MAAMC,eAAe,GAAGR,SAAS,CAACS,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACT,IAAI,KAAKK,mBAAmB,CAAC;EAC7E,MAAM;IAAEK;EAAE,CAAC,GAAGjB,cAAc,CAAC,CAAC;EAC9B;EACAD,SAAS,CAAC,MAAM,CAChB,CAAC,EAAE,CAACe,eAAe,EAAEG,CAAC,CAAC,CAAC;EACxB;EACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtBC,QAAQ,CAACC,eAAe,CAACC,IAAI,GAAGT,mBAAmB,EAAE;IACrD,IAAIA,mBAAmB,KAAK,IAAI,EAAE;MAChC,oBACEP,OAAA;QAAMiB,SAAS,yBAAAC,MAAA,CAAyBjB,SAAS,CAAC,CAAC,CAAC,CAACG,YAAY;MAAQ;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAErF,CAAC,MAAM;MACL,oBACEtB,OAAA;QAAMiB,SAAS,yBAAAC,MAAA,CAAyBjB,SAAS,CAAC,CAAC,CAAC,CAACG,YAAY;MAAQ;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAErF;EACF,CAAC;EACD,oBACEtB,OAAA;IAAKiB,SAAS,EAAC,sBAAsB;IAAAM,QAAA,eACnCvB,OAAA;MAAKiB,SAAS,EAAC,oEAAoE;MAAAM,QAAA,eACjFvB,OAAA;QAAKiB,SAAS,EAAC,UAAU;QAAAM,QAAA,gBACvBvB,OAAA;UACEiB,SAAS,EAAC,8BAA8B;UACxCO,IAAI,EAAC,QAAQ;UACbC,EAAE,EAAC,qBAAqB;UACxB,eAAY,UAAU;UACtB,iBAAc,OAAO;UAAAF,QAAA,eAErBvB,OAAA,CAACa,SAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACTtB,OAAA;UAAIiB,SAAS,EAAC,mCAAmC;UAAC,mBAAgB,qBAAqB;UAAAM,QAAA,EACpFtB,SAAS,CAACyB,GAAG,CAACC,IAAA;YAAA,IAAC;cAAEzB,IAAI;cAAEC,IAAI;cAAEC;YAAa,CAAC,GAAAuB,IAAA;YAAA,oBAC1C3B,OAAA;cAAAuB,QAAA,eACEvB,OAAA;gBACEiB,SAAS,EAAEnB,UAAU,CAAC,yBAAyB,EAAE;kBAC/C8B,QAAQ,EAAErB,mBAAmB,KAAKL;gBACpC,CAAC,CAAE;gBACH2B,OAAO,EAAEA,CAAA,KAAM;kBACbjC,OAAO,CAACkC,cAAc,CAAC5B,IAAI,CAAC;gBAC9B,CAAE;gBAAAqB,QAAA,gBAEFvB,OAAA;kBACEiB,SAAS,yBAAAC,MAAA,CAAyBd,YAAY,UAAQ;kBACtD2B,KAAK,EAAE;oBACLC,OAAO,EAAEzB,mBAAmB,KAAKL,IAAI,GAAG,GAAG,GAAG;kBAChD;gBAAE;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EACPnB,IAAI;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GAhBAlB,YAAY;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBjB,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChB,EAAA,CA3DeD,UAAU;EAAA,QAGVV,cAAc;AAAA;AAAAsC,EAAA,GAHd5B,UAAU;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}