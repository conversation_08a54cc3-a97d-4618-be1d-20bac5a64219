{"ast": null, "code": "import * as React from 'react';\nvar FilterDropdownMenuWrapper = function FilterDropdownMenuWrapper(props) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: props.className,\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    }\n  }, props.children);\n};\nexport default FilterDropdownMenuWrapper;", "map": {"version": 3, "names": ["React", "FilterDropdownMenuWrapper", "props", "createElement", "className", "onClick", "e", "stopPropagation", "children"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/hooks/useFilter/FilterWrapper.js"], "sourcesContent": ["import * as React from 'react';\n\nvar FilterDropdownMenuWrapper = function FilterDropdownMenuWrapper(props) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: props.className,\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    }\n  }, props.children);\n};\n\nexport default FilterDropdownMenuWrapper;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,KAAK,EAAE;EACxE,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEF,KAAK,CAACE,SAAS;IAC1BC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;MAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;IAC5B;EACF,CAAC,EAAEL,KAAK,CAACM,QAAQ,CAAC;AACpB,CAAC;AAED,eAAeP,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}