# 📚 Indice Completo Documentazione

Indice alfabetico e tematico di tutta la documentazione del progetto E-Procurement Frontend.

## 🔍 Ricerca Rapida

### Per Argomento
- **🏗️ Architettura**: [system-overview.md](./architecture/system-overview.md), [frontend-structure.md](./architecture/frontend-structure.md), [backend-integration.md](./architecture/backend-integration.md)
- **🚀 Setup**: [initial-setup.md](./setup/initial-setup.md), [github-setup.md](./setup/github-setup.md), [vercel-integration.md](./setup/vercel-integration.md)
- **🧪 Testing**: [automated-testing-master-plan.md](./testing/automated-testing-master-plan.md), [testing-guide.md](./testing/testing-guide.md)
- **🔧 Sviluppo**: [development-log.md](./development/development-log.md), [feature-development-plan.md](./development/feature-development-plan.md)
- **🚨 Error Handling**: [backend-improvements.md](./error-handling/backend-improvements.md), [frontend-improvements.md](./error-handling/frontend-improvements.md)

### Per Ruolo
- **👨‍💻 Developer**: [development-log.md](./development/development-log.md), [frontend-structure.md](./architecture/frontend-structure.md)
- **🤖 AI Agent**: [development-log.md](./development/development-log.md), [backend-agent-prompt.md](./development/backend-agent-prompt.md)
- **🧪 Tester**: [testing-guide.md](./testing/testing-guide.md), [automated-testing-master-plan.md](./testing/automated-testing-master-plan.md)
- **🚀 DevOps**: [vercel-integration.md](./setup/vercel-integration.md), [build-commands.md](./setup/build-commands.md)

## 📁 Struttura Directory

```
docs/
├── README.md                           # Panoramica generale
├── INDEX.md                           # Questo file
├── architecture/                      # Architettura sistema
│   ├── system-overview.md            # Panoramica generale
│   ├── frontend-structure.md         # Struttura frontend
│   └── backend-integration.md        # Integrazione backend
├── setup/                            # Setup e configurazione
│   ├── initial-setup.md             # Setup iniziale
│   ├── github-setup.md              # Configurazione GitHub
│   ├── vercel-integration.md        # Integrazione Vercel
│   └── build-commands.md            # Comandi build
├── testing/                          # Testing e QA
│   ├── automated-testing-master-plan.md  # Piano master testing
│   ├── testing-guide.md             # Guida testing
│   ├── testing-system.md            # Sistema testing
│   └── testing-system-old.md        # Versione precedente
├── development/                      # Sviluppo
│   ├── development-log.md           # Log sviluppo (IMPORTANTE)
│   ├── feature-development-plan.md  # Piano feature
│   ├── implementation-strategy.md   # Strategia implementazione
│   └── backend-agent-prompt.md      # Prompt backend agent
├── error-handling/                   # Gestione errori
│   ├── backend-improvements.md      # Miglioramenti backend
│   ├── frontend-improvements.md     # Miglioramenti frontend
│   └── backend-monitoring.md        # Monitoraggio backend
└── user-roles/                      # Ruoli utente
    └── agenti-testing.md            # Testing agenti
```

## 🎯 File Più Importanti

### **🔥 Essenziali per AI Agents**
1. **[development-log.md](./development/development-log.md)** - Log dettagliato di tutte le modifiche
2. **[backend-agent-prompt.md](./development/backend-agent-prompt.md)** - Prompt per coordinamento backend
3. **[system-overview.md](./architecture/system-overview.md)** - Architettura generale

### **🚀 Setup e Deploy**
1. **[initial-setup.md](./setup/initial-setup.md)** - Setup completo progetto
2. **[vercel-integration.md](./setup/vercel-integration.md)** - Deploy su Vercel
3. **[github-setup.md](./setup/github-setup.md)** - Configurazione repository

### **🧪 Testing**
1. **[automated-testing-master-plan.md](./testing/automated-testing-master-plan.md)** - Piano testing completo
2. **[testing-guide.md](./testing/testing-guide.md)** - Guida pratica testing

### **🔧 Sviluppo**
1. **[frontend-structure.md](./architecture/frontend-structure.md)** - Struttura codice frontend
2. **[backend-integration.md](./architecture/backend-integration.md)** - API e integrazione
3. **[feature-development-plan.md](./development/feature-development-plan.md)** - Piano nuove feature

## 🔄 Flusso di Lavoro Consigliato

### **Per Nuovi AI Agents**
1. Leggi [development-log.md](./development/development-log.md) per contesto
2. Studia [system-overview.md](./architecture/system-overview.md) per architettura
3. Consulta [frontend-structure.md](./architecture/frontend-structure.md) per codice
4. Usa [backend-agent-prompt.md](./development/backend-agent-prompt.md) per coordinamento

### **Per Nuove Feature**
1. Consulta [feature-development-plan.md](./development/feature-development-plan.md)
2. Segui [implementation-strategy.md](./development/implementation-strategy.md)
3. Implementa seguendo [frontend-structure.md](./architecture/frontend-structure.md)
4. Testa con [testing-guide.md](./testing/testing-guide.md)
5. Aggiorna [development-log.md](./development/development-log.md)

### **Per Troubleshooting**
1. Controlla [error-handling/](./error-handling/) per gestione errori
2. Verifica [backend-integration.md](./architecture/backend-integration.md) per API
3. Consulta [development-log.md](./development/development-log.md) per modifiche recenti

## 📊 Statistiche Documentazione

- **File totali**: 25+
- **Categorie**: 6 (Architecture, Setup, Testing, Development, Error Handling, User Roles)
- **Ultimo aggiornamento**: 16 Gennaio 2025
- **Versione**: 1.0.0

## 🔗 Link Esterni

- **Repository**: https://github.com/Vincenzo-krsc/ep-frontend.git
- **Production**: https://eprocurement.tmselezioni.it
- **Staging**: https://ep-frontend-coral.vercel.app/
- **Backend**: https://epbackend-kekl2s5de-vincenzo-2210s-projects.vercel.app

## 📝 Note per Manutenzione

### **Quando Aggiungere Nuova Documentazione**
- Nuove feature → Aggiorna [development-log.md](./development/development-log.md)
- Modifiche architettura → Aggiorna [architecture/](./architecture/)
- Nuovi test → Aggiorna [testing/](./testing/)
- Problemi risolti → Aggiorna [error-handling/](./error-handling/)

### **Convenzioni**
- **Nomi file**: kebab-case (es: `my-document.md`)
- **Emoji**: Usa emoji per categorizzazione visiva
- **Link**: Sempre relativi alla cartella docs
- **Aggiornamenti**: Data in formato YYYY-MM-DD

---

**📅 Ultimo aggiornamento**: 16 Gennaio 2025  
**👨‍💻 Maintainer**: Augment Agent  
**📦 Versione**: 1.0.0
