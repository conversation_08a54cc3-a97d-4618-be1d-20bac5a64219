{"ast": null, "code": "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar TypeError = global.TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};", "map": {"version": 3, "names": ["global", "require", "call", "isCallable", "isObject", "TypeError", "module", "exports", "input", "pref", "fn", "val", "toString", "valueOf"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/ordinary-to-primitive.js"], "sourcesContent": ["var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar TypeError = global.TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,IAAI,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIG,QAAQ,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAII,SAAS,GAAGL,MAAM,CAACK,SAAS;;AAEhC;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;EACtC,IAAIC,EAAE,EAAEC,GAAG;EACX,IAAIF,IAAI,KAAK,QAAQ,IAAIN,UAAU,CAACO,EAAE,GAAGF,KAAK,CAACI,QAAQ,CAAC,IAAI,CAACR,QAAQ,CAACO,GAAG,GAAGT,IAAI,CAACQ,EAAE,EAAEF,KAAK,CAAC,CAAC,EAAE,OAAOG,GAAG;EACxG,IAAIR,UAAU,CAACO,EAAE,GAAGF,KAAK,CAACK,OAAO,CAAC,IAAI,CAACT,QAAQ,CAACO,GAAG,GAAGT,IAAI,CAACQ,EAAE,EAAEF,KAAK,CAAC,CAAC,EAAE,OAAOG,GAAG;EAClF,IAAIF,IAAI,KAAK,QAAQ,IAAIN,UAAU,CAACO,EAAE,GAAGF,KAAK,CAACI,QAAQ,CAAC,IAAI,CAACR,QAAQ,CAACO,GAAG,GAAGT,IAAI,CAACQ,EAAE,EAAEF,KAAK,CAAC,CAAC,EAAE,OAAOG,GAAG;EACxG,MAAMN,SAAS,CAAC,yCAAyC,CAAC;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}