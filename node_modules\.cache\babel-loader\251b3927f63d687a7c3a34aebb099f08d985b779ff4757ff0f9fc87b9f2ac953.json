{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport classNames from 'classnames';\nimport Element from './Element';\nimport { ConfigContext } from '../config-provider';\nvar SkeletonInput = function SkeletonInput(props) {\n  var _classNames;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    block = props.block;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-input\")\n  }, otherProps)));\n};\nSkeletonInput.defaultProps = {\n  size: 'default'\n};\nexport default SkeletonInput;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "omit", "classNames", "Element", "ConfigContext", "SkeletonInput", "props", "_classNames", "customizePrefixCls", "prefixCls", "className", "active", "block", "_React$useContext", "useContext", "getPrefixCls", "otherProps", "cls", "concat", "createElement", "defaultProps", "size"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Input.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport classNames from 'classnames';\nimport Element from './Element';\nimport { ConfigContext } from '../config-provider';\n\nvar SkeletonInput = function SkeletonInput(props) {\n  var _classNames;\n\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      active = props.active,\n      block = props.block;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-input\")\n  }, otherProps)));\n};\n\nSkeletonInput.defaultProps = {\n  size: 'default'\n};\nexport default SkeletonInput;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,aAAa,QAAQ,oBAAoB;AAElD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,WAAW;EAEf,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACpCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,KAAK,GAAGN,KAAK,CAACM,KAAK;EAEvB,IAAIC,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACV,aAAa,CAAC;IACnDW,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIN,SAAS,GAAGM,YAAY,CAAC,UAAU,EAAEP,kBAAkB,CAAC;EAC5D,IAAIQ,UAAU,GAAGf,IAAI,CAACK,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC3C,IAAIW,GAAG,GAAGf,UAAU,CAACO,SAAS,EAAE,EAAE,CAACS,MAAM,CAACT,SAAS,EAAE,UAAU,CAAC,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAER,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACW,MAAM,CAACT,SAAS,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,EAAEZ,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACW,MAAM,CAACT,SAAS,EAAE,QAAQ,CAAC,EAAEG,KAAK,CAAC,EAAEL,WAAW,GAAGG,SAAS,CAAC;EACzP,OAAO,aAAaV,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CT,SAAS,EAAEO;EACb,CAAC,EAAE,aAAajB,KAAK,CAACmB,aAAa,CAAChB,OAAO,EAAEL,QAAQ,CAAC;IACpDW,SAAS,EAAE,EAAE,CAACS,MAAM,CAACT,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEO,UAAU,CAAC,CAAC,CAAC;AAClB,CAAC;AAEDX,aAAa,CAACe,YAAY,GAAG;EAC3BC,IAAI,EAAE;AACR,CAAC;AACD,eAAehB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}