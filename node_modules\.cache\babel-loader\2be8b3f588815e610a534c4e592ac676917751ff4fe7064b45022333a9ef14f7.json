{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"name\", \"initialValues\", \"fields\", \"form\", \"preserve\", \"children\", \"component\", \"validateMessages\", \"validateTrigger\", \"onValuesChange\", \"onFieldsChange\", \"onFinish\", \"onFinishFailed\"];\nimport * as React from 'react';\nimport useForm from './useForm';\nimport FieldContext, { HOOK_MARK } from './FieldContext';\nimport FormContext from './FormContext';\nimport { isSimilar } from './utils/valueUtil';\nvar Form = function Form(_ref, ref) {\n  var name = _ref.name,\n    initialValues = _ref.initialValues,\n    fields = _ref.fields,\n    form = _ref.form,\n    preserve = _ref.preserve,\n    children = _ref.children,\n    _ref$component = _ref.component,\n    Component = _ref$component === void 0 ? 'form' : _ref$component,\n    validateMessages = _ref.validateMessages,\n    _ref$validateTrigger = _ref.validateTrigger,\n    validateTrigger = _ref$validateTrigger === void 0 ? 'onChange' : _ref$validateTrigger,\n    onValuesChange = _ref.onValuesChange,\n    _onFieldsChange = _ref.onFieldsChange,\n    _onFinish = _ref.onFinish,\n    onFinishFailed = _ref.onFinishFailed,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var formContext = React.useContext(FormContext); // We customize handle event since Context will makes all the consumer re-render:\n  // https://reactjs.org/docs/context.html#contextprovider\n\n  var _useForm = useForm(form),\n    _useForm2 = _slicedToArray(_useForm, 1),\n    formInstance = _useForm2[0];\n  var _formInstance$getInte = formInstance.getInternalHooks(HOOK_MARK),\n    useSubscribe = _formInstance$getInte.useSubscribe,\n    setInitialValues = _formInstance$getInte.setInitialValues,\n    setCallbacks = _formInstance$getInte.setCallbacks,\n    setValidateMessages = _formInstance$getInte.setValidateMessages,\n    setPreserve = _formInstance$getInte.setPreserve,\n    destroyForm = _formInstance$getInte.destroyForm; // Pass ref with form instance\n\n  React.useImperativeHandle(ref, function () {\n    return formInstance;\n  }); // Register form into Context\n\n  React.useEffect(function () {\n    formContext.registerForm(name, formInstance);\n    return function () {\n      formContext.unregisterForm(name);\n    };\n  }, [formContext, formInstance, name]); // Pass props to store\n\n  setValidateMessages(_objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages));\n  setCallbacks({\n    onValuesChange: onValuesChange,\n    onFieldsChange: function onFieldsChange(changedFields) {\n      formContext.triggerFormChange(name, changedFields);\n      if (_onFieldsChange) {\n        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n        _onFieldsChange.apply(void 0, [changedFields].concat(rest));\n      }\n    },\n    onFinish: function onFinish(values) {\n      formContext.triggerFormFinish(name, values);\n      if (_onFinish) {\n        _onFinish(values);\n      }\n    },\n    onFinishFailed: onFinishFailed\n  });\n  setPreserve(preserve); // Set initial value, init store value when first mount\n\n  var mountRef = React.useRef(null);\n  setInitialValues(initialValues, !mountRef.current);\n  if (!mountRef.current) {\n    mountRef.current = true;\n  }\n  React.useEffect(function () {\n    return destroyForm;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []); // Prepare children by `children` type\n\n  var childrenNode;\n  var childrenRenderProps = typeof children === 'function';\n  if (childrenRenderProps) {\n    var values = formInstance.getFieldsValue(true);\n    childrenNode = children(values, formInstance);\n  } else {\n    childrenNode = children;\n  } // Not use subscribe when using render props\n\n  useSubscribe(!childrenRenderProps); // Listen if fields provided. We use ref to save prev data here to avoid additional render\n\n  var prevFieldsRef = React.useRef();\n  React.useEffect(function () {\n    if (!isSimilar(prevFieldsRef.current || [], fields || [])) {\n      formInstance.setFields(fields || []);\n    }\n    prevFieldsRef.current = fields;\n  }, [fields, formInstance]);\n  var formContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      validateTrigger: validateTrigger\n    });\n  }, [formInstance, validateTrigger]);\n  var wrapperNode = /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: formContextValue\n  }, childrenNode);\n  if (Component === false) {\n    return wrapperNode;\n  }\n  return /*#__PURE__*/React.createElement(Component, _extends({}, restProps, {\n    onSubmit: function onSubmit(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      formInstance.submit();\n    },\n    onReset: function onReset(event) {\n      var _restProps$onReset;\n      event.preventDefault();\n      formInstance.resetFields();\n      (_restProps$onReset = restProps.onReset) === null || _restProps$onReset === void 0 ? void 0 : _restProps$onReset.call(restProps, event);\n    }\n  }), wrapperNode);\n};\nexport default Form;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "useForm", "FieldContext", "HOOK_MARK", "FormContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Form", "_ref", "ref", "name", "initialValues", "fields", "form", "preserve", "children", "_ref$component", "component", "Component", "validateMessages", "_ref$validateTrigger", "validate<PERSON><PERSON>ger", "onValuesChange", "_onFields<PERSON>hange", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onFinish", "onFinish", "onFinishFailed", "restProps", "formContext", "useContext", "_useForm", "_useForm2", "formInstance", "_formInstance$getInte", "getInternalHooks", "useSubscribe", "setInitialValues", "setCallbacks", "setValidateMessages", "setPreserve", "destroyForm", "useImperativeHandle", "useEffect", "registerForm", "unregisterForm", "changed<PERSON>ields", "triggerForm<PERSON>hange", "_len", "arguments", "length", "rest", "Array", "_key", "apply", "concat", "values", "triggerFormFinish", "mountRef", "useRef", "current", "childrenNode", "childrenRenderProps", "getFieldsValue", "prevFieldsRef", "setFields", "formContextValue", "useMemo", "wrapperNode", "createElement", "Provider", "value", "onSubmit", "event", "preventDefault", "stopPropagation", "submit", "onReset", "_restProps$onReset", "resetFields", "call"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/Form.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"name\", \"initialValues\", \"fields\", \"form\", \"preserve\", \"children\", \"component\", \"validateMessages\", \"validateTrigger\", \"onValuesChange\", \"onFieldsChange\", \"onFinish\", \"onFinishFailed\"];\nimport * as React from 'react';\nimport useForm from './useForm';\nimport FieldContext, { HOOK_MARK } from './FieldContext';\nimport FormContext from './FormContext';\nimport { isSimilar } from './utils/valueUtil';\n\nvar Form = function Form(_ref, ref) {\n  var name = _ref.name,\n      initialValues = _ref.initialValues,\n      fields = _ref.fields,\n      form = _ref.form,\n      preserve = _ref.preserve,\n      children = _ref.children,\n      _ref$component = _ref.component,\n      Component = _ref$component === void 0 ? 'form' : _ref$component,\n      validateMessages = _ref.validateMessages,\n      _ref$validateTrigger = _ref.validateTrigger,\n      validateTrigger = _ref$validateTrigger === void 0 ? 'onChange' : _ref$validateTrigger,\n      onValuesChange = _ref.onValuesChange,\n      _onFieldsChange = _ref.onFieldsChange,\n      _onFinish = _ref.onFinish,\n      onFinishFailed = _ref.onFinishFailed,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n\n  var formContext = React.useContext(FormContext); // We customize handle event since Context will makes all the consumer re-render:\n  // https://reactjs.org/docs/context.html#contextprovider\n\n  var _useForm = useForm(form),\n      _useForm2 = _slicedToArray(_useForm, 1),\n      formInstance = _useForm2[0];\n\n  var _formInstance$getInte = formInstance.getInternalHooks(HOOK_MARK),\n      useSubscribe = _formInstance$getInte.useSubscribe,\n      setInitialValues = _formInstance$getInte.setInitialValues,\n      setCallbacks = _formInstance$getInte.setCallbacks,\n      setValidateMessages = _formInstance$getInte.setValidateMessages,\n      setPreserve = _formInstance$getInte.setPreserve,\n      destroyForm = _formInstance$getInte.destroyForm; // Pass ref with form instance\n\n\n  React.useImperativeHandle(ref, function () {\n    return formInstance;\n  }); // Register form into Context\n\n  React.useEffect(function () {\n    formContext.registerForm(name, formInstance);\n    return function () {\n      formContext.unregisterForm(name);\n    };\n  }, [formContext, formInstance, name]); // Pass props to store\n\n  setValidateMessages(_objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages));\n  setCallbacks({\n    onValuesChange: onValuesChange,\n    onFieldsChange: function onFieldsChange(changedFields) {\n      formContext.triggerFormChange(name, changedFields);\n\n      if (_onFieldsChange) {\n        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n\n        _onFieldsChange.apply(void 0, [changedFields].concat(rest));\n      }\n    },\n    onFinish: function onFinish(values) {\n      formContext.triggerFormFinish(name, values);\n\n      if (_onFinish) {\n        _onFinish(values);\n      }\n    },\n    onFinishFailed: onFinishFailed\n  });\n  setPreserve(preserve); // Set initial value, init store value when first mount\n\n  var mountRef = React.useRef(null);\n  setInitialValues(initialValues, !mountRef.current);\n\n  if (!mountRef.current) {\n    mountRef.current = true;\n  }\n\n  React.useEffect(function () {\n    return destroyForm;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  []); // Prepare children by `children` type\n\n  var childrenNode;\n  var childrenRenderProps = typeof children === 'function';\n\n  if (childrenRenderProps) {\n    var values = formInstance.getFieldsValue(true);\n    childrenNode = children(values, formInstance);\n  } else {\n    childrenNode = children;\n  } // Not use subscribe when using render props\n\n\n  useSubscribe(!childrenRenderProps); // Listen if fields provided. We use ref to save prev data here to avoid additional render\n\n  var prevFieldsRef = React.useRef();\n  React.useEffect(function () {\n    if (!isSimilar(prevFieldsRef.current || [], fields || [])) {\n      formInstance.setFields(fields || []);\n    }\n\n    prevFieldsRef.current = fields;\n  }, [fields, formInstance]);\n  var formContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      validateTrigger: validateTrigger\n    });\n  }, [formInstance, validateTrigger]);\n  var wrapperNode = /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: formContextValue\n  }, childrenNode);\n\n  if (Component === false) {\n    return wrapperNode;\n  }\n\n  return /*#__PURE__*/React.createElement(Component, _extends({}, restProps, {\n    onSubmit: function onSubmit(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      formInstance.submit();\n    },\n    onReset: function onReset(event) {\n      var _restProps$onReset;\n\n      event.preventDefault();\n      formInstance.resetFields();\n      (_restProps$onReset = restProps.onReset) === null || _restProps$onReset === void 0 ? void 0 : _restProps$onReset.call(restProps, event);\n    }\n  }), wrapperNode);\n};\n\nexport default Form;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,CAAC;AACzM,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,YAAY,IAAIC,SAAS,QAAQ,gBAAgB;AACxD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,SAAS,QAAQ,mBAAmB;AAE7C,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAClC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,aAAa,GAAGH,IAAI,CAACG,aAAa;IAClCC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,IAAI,GAAGL,IAAI,CAACK,IAAI;IAChBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,cAAc,GAAGR,IAAI,CAACS,SAAS;IAC/BC,SAAS,GAAGF,cAAc,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,cAAc;IAC/DG,gBAAgB,GAAGX,IAAI,CAACW,gBAAgB;IACxCC,oBAAoB,GAAGZ,IAAI,CAACa,eAAe;IAC3CA,eAAe,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,oBAAoB;IACrFE,cAAc,GAAGd,IAAI,CAACc,cAAc;IACpCC,eAAe,GAAGf,IAAI,CAACgB,cAAc;IACrCC,SAAS,GAAGjB,IAAI,CAACkB,QAAQ;IACzBC,cAAc,GAAGnB,IAAI,CAACmB,cAAc;IACpCC,SAAS,GAAG7B,wBAAwB,CAACS,IAAI,EAAER,SAAS,CAAC;EAEzD,IAAI6B,WAAW,GAAG5B,KAAK,CAAC6B,UAAU,CAACzB,WAAW,CAAC,CAAC,CAAC;EACjD;;EAEA,IAAI0B,QAAQ,GAAG7B,OAAO,CAACW,IAAI,CAAC;IACxBmB,SAAS,GAAGlC,cAAc,CAACiC,QAAQ,EAAE,CAAC,CAAC;IACvCE,YAAY,GAAGD,SAAS,CAAC,CAAC,CAAC;EAE/B,IAAIE,qBAAqB,GAAGD,YAAY,CAACE,gBAAgB,CAAC/B,SAAS,CAAC;IAChEgC,YAAY,GAAGF,qBAAqB,CAACE,YAAY;IACjDC,gBAAgB,GAAGH,qBAAqB,CAACG,gBAAgB;IACzDC,YAAY,GAAGJ,qBAAqB,CAACI,YAAY;IACjDC,mBAAmB,GAAGL,qBAAqB,CAACK,mBAAmB;IAC/DC,WAAW,GAAGN,qBAAqB,CAACM,WAAW;IAC/CC,WAAW,GAAGP,qBAAqB,CAACO,WAAW,CAAC,CAAC;;EAGrDxC,KAAK,CAACyC,mBAAmB,CAACjC,GAAG,EAAE,YAAY;IACzC,OAAOwB,YAAY;EACrB,CAAC,CAAC,CAAC,CAAC;;EAEJhC,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1Bd,WAAW,CAACe,YAAY,CAAClC,IAAI,EAAEuB,YAAY,CAAC;IAC5C,OAAO,YAAY;MACjBJ,WAAW,CAACgB,cAAc,CAACnC,IAAI,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACmB,WAAW,EAAEI,YAAY,EAAEvB,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEvC6B,mBAAmB,CAAC1C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,WAAW,CAACV,gBAAgB,CAAC,EAAEA,gBAAgB,CAAC,CAAC;EACrGmB,YAAY,CAAC;IACXhB,cAAc,EAAEA,cAAc;IAC9BE,cAAc,EAAE,SAASA,cAAcA,CAACsB,aAAa,EAAE;MACrDjB,WAAW,CAACkB,iBAAiB,CAACrC,IAAI,EAAEoC,aAAa,CAAC;MAElD,IAAIvB,eAAe,EAAE;QACnB,KAAK,IAAIyB,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QAClC;QAEA9B,eAAe,CAAC+B,KAAK,CAAC,KAAK,CAAC,EAAE,CAACR,aAAa,CAAC,CAACS,MAAM,CAACJ,IAAI,CAAC,CAAC;MAC7D;IACF,CAAC;IACDzB,QAAQ,EAAE,SAASA,QAAQA,CAAC8B,MAAM,EAAE;MAClC3B,WAAW,CAAC4B,iBAAiB,CAAC/C,IAAI,EAAE8C,MAAM,CAAC;MAE3C,IAAI/B,SAAS,EAAE;QACbA,SAAS,CAAC+B,MAAM,CAAC;MACnB;IACF,CAAC;IACD7B,cAAc,EAAEA;EAClB,CAAC,CAAC;EACFa,WAAW,CAAC1B,QAAQ,CAAC,CAAC,CAAC;;EAEvB,IAAI4C,QAAQ,GAAGzD,KAAK,CAAC0D,MAAM,CAAC,IAAI,CAAC;EACjCtB,gBAAgB,CAAC1B,aAAa,EAAE,CAAC+C,QAAQ,CAACE,OAAO,CAAC;EAElD,IAAI,CAACF,QAAQ,CAACE,OAAO,EAAE;IACrBF,QAAQ,CAACE,OAAO,GAAG,IAAI;EACzB;EAEA3D,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1B,OAAOF,WAAW;EACpB,CAAC;EAAE;EACH,EAAE,CAAC,CAAC,CAAC;;EAEL,IAAIoB,YAAY;EAChB,IAAIC,mBAAmB,GAAG,OAAO/C,QAAQ,KAAK,UAAU;EAExD,IAAI+C,mBAAmB,EAAE;IACvB,IAAIN,MAAM,GAAGvB,YAAY,CAAC8B,cAAc,CAAC,IAAI,CAAC;IAC9CF,YAAY,GAAG9C,QAAQ,CAACyC,MAAM,EAAEvB,YAAY,CAAC;EAC/C,CAAC,MAAM;IACL4B,YAAY,GAAG9C,QAAQ;EACzB,CAAC,CAAC;;EAGFqB,YAAY,CAAC,CAAC0B,mBAAmB,CAAC,CAAC,CAAC;;EAEpC,IAAIE,aAAa,GAAG/D,KAAK,CAAC0D,MAAM,CAAC,CAAC;EAClC1D,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACrC,SAAS,CAAC0D,aAAa,CAACJ,OAAO,IAAI,EAAE,EAAEhD,MAAM,IAAI,EAAE,CAAC,EAAE;MACzDqB,YAAY,CAACgC,SAAS,CAACrD,MAAM,IAAI,EAAE,CAAC;IACtC;IAEAoD,aAAa,CAACJ,OAAO,GAAGhD,MAAM;EAChC,CAAC,EAAE,CAACA,MAAM,EAAEqB,YAAY,CAAC,CAAC;EAC1B,IAAIiC,gBAAgB,GAAGjE,KAAK,CAACkE,OAAO,CAAC,YAAY;IAC/C,OAAOtE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MACxDZ,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACY,YAAY,EAAEZ,eAAe,CAAC,CAAC;EACnC,IAAI+C,WAAW,GAAG,aAAanE,KAAK,CAACoE,aAAa,CAAClE,YAAY,CAACmE,QAAQ,EAAE;IACxEC,KAAK,EAAEL;EACT,CAAC,EAAEL,YAAY,CAAC;EAEhB,IAAI3C,SAAS,KAAK,KAAK,EAAE;IACvB,OAAOkD,WAAW;EACpB;EAEA,OAAO,aAAanE,KAAK,CAACoE,aAAa,CAACnD,SAAS,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAEgC,SAAS,EAAE;IACzE4C,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;MACjCA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB1C,YAAY,CAAC2C,MAAM,CAAC,CAAC;IACvB,CAAC;IACDC,OAAO,EAAE,SAASA,OAAOA,CAACJ,KAAK,EAAE;MAC/B,IAAIK,kBAAkB;MAEtBL,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBzC,YAAY,CAAC8C,WAAW,CAAC,CAAC;MAC1B,CAACD,kBAAkB,GAAGlD,SAAS,CAACiD,OAAO,MAAM,IAAI,IAAIC,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,IAAI,CAACpD,SAAS,EAAE6C,KAAK,CAAC;IACzI;EACF,CAAC,CAAC,EAAEL,WAAW,CAAC;AAClB,CAAC;AAED,eAAe7D,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}