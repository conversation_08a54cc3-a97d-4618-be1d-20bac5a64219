{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nvar CheckableTag = function CheckableTag(_a) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    checked = _a.checked,\n    onChange = _a.onChange,\n    onClick = _a.onClick,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"checked\", \"onChange\", \"onClick\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var handleClick = function handleClick(e) {\n    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  var prefixCls = getPrefixCls('tag', customizePrefixCls);\n  var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checkable\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-checkable-checked\"), checked), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n    className: cls,\n    onClick: handleClick\n  }));\n};\nexport default CheckableTag;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ConfigContext", "CheckableTag", "_a", "_classNames", "customizePrefixCls", "prefixCls", "className", "checked", "onChange", "onClick", "restProps", "_React$useContext", "useContext", "getPrefixCls", "handleClick", "cls", "concat", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/tag/CheckableTag.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\n\nvar CheckableTag = function CheckableTag(_a) {\n  var _classNames;\n\n  var customizePrefixCls = _a.prefixCls,\n      className = _a.className,\n      checked = _a.checked,\n      onChange = _a.onChange,\n      onClick = _a.onClick,\n      restProps = __rest(_a, [\"prefixCls\", \"className\", \"checked\", \"onChange\", \"onClick\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var handleClick = function handleClick(e) {\n    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n\n  var prefixCls = getPrefixCls('tag', customizePrefixCls);\n  var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checkable\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-checkable-checked\"), checked), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n    className: cls,\n    onClick: handleClick\n  }));\n};\n\nexport default CheckableTag;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AAEvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAElD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,EAAE,EAAE;EAC3C,IAAIC,WAAW;EAEf,IAAIC,kBAAkB,GAAGF,EAAE,CAACG,SAAS;IACjCC,SAAS,GAAGJ,EAAE,CAACI,SAAS;IACxBC,OAAO,GAAGL,EAAE,CAACK,OAAO;IACpBC,QAAQ,GAAGN,EAAE,CAACM,QAAQ;IACtBC,OAAO,GAAGP,EAAE,CAACO,OAAO;IACpBC,SAAS,GAAG1B,MAAM,CAACkB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EAExF,IAAIS,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACZ,aAAa,CAAC;IACnDa,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAC5B,CAAC,EAAE;IACxCsB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAACD,OAAO,CAAC;IACtEE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACvB,CAAC,CAAC;EAC9D,CAAC;EAED,IAAImB,SAAS,GAAGQ,YAAY,CAAC,KAAK,EAAET,kBAAkB,CAAC;EACvD,IAAIW,GAAG,GAAGhB,UAAU,CAACM,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAEpB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACa,MAAM,CAACX,SAAS,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,EAAEtB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACa,MAAM,CAACX,SAAS,EAAE,oBAAoB,CAAC,EAAEE,OAAO,CAAC,EAAEJ,WAAW,GAAGG,SAAS,CAAC;EACtO,OAAO,aAAaR,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAE4B,SAAS,EAAE;IACtEJ,SAAS,EAAES,GAAG;IACdN,OAAO,EAAEK;EACX,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAeb,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}