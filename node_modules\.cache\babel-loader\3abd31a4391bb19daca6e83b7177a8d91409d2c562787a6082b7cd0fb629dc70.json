{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport React, { Component } from 'react';\nimport classNames from 'classnames';\nvar Checkbox = /*#__PURE__*/function (_Component) {\n  _inherits(Checkbox, _Component);\n  var _super = _createSuper(Checkbox);\n  function Checkbox(props) {\n    var _this;\n    _classCallCheck(this, Checkbox);\n    _this = _super.call(this, props);\n    _this.handleChange = function (e) {\n      var _this$props = _this.props,\n        disabled = _this$props.disabled,\n        onChange = _this$props.onChange;\n      if (disabled) {\n        return;\n      }\n      if (!('checked' in _this.props)) {\n        _this.setState({\n          checked: e.target.checked\n        });\n      }\n      if (onChange) {\n        onChange({\n          target: _objectSpread(_objectSpread({}, _this.props), {}, {\n            checked: e.target.checked\n          }),\n          stopPropagation: function stopPropagation() {\n            e.stopPropagation();\n          },\n          preventDefault: function preventDefault() {\n            e.preventDefault();\n          },\n          nativeEvent: e.nativeEvent\n        });\n      }\n    };\n    _this.saveInput = function (node) {\n      _this.input = node;\n    };\n    var checked = 'checked' in props ? props.checked : props.defaultChecked;\n    _this.state = {\n      checked: checked\n    };\n    return _this;\n  }\n  _createClass(Checkbox, [{\n    key: \"focus\",\n    value: function focus() {\n      this.input.focus();\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.input.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        style = _this$props2.style,\n        name = _this$props2.name,\n        id = _this$props2.id,\n        type = _this$props2.type,\n        disabled = _this$props2.disabled,\n        readOnly = _this$props2.readOnly,\n        tabIndex = _this$props2.tabIndex,\n        onClick = _this$props2.onClick,\n        onFocus = _this$props2.onFocus,\n        onBlur = _this$props2.onBlur,\n        onKeyDown = _this$props2.onKeyDown,\n        onKeyPress = _this$props2.onKeyPress,\n        onKeyUp = _this$props2.onKeyUp,\n        autoFocus = _this$props2.autoFocus,\n        value = _this$props2.value,\n        required = _this$props2.required,\n        others = _objectWithoutProperties(_this$props2, [\"prefixCls\", \"className\", \"style\", \"name\", \"id\", \"type\", \"disabled\", \"readOnly\", \"tabIndex\", \"onClick\", \"onFocus\", \"onBlur\", \"onKeyDown\", \"onKeyPress\", \"onKeyUp\", \"autoFocus\", \"value\", \"required\"]);\n      var globalProps = Object.keys(others).reduce(function (prev, key) {\n        if (key.substr(0, 5) === 'aria-' || key.substr(0, 5) === 'data-' || key === 'role') {\n          // eslint-disable-next-line no-param-reassign\n          prev[key] = others[key];\n        }\n        return prev;\n      }, {});\n      var checked = this.state.checked;\n      var classString = classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames));\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classString,\n        style: style\n      }, /*#__PURE__*/React.createElement(\"input\", _extends({\n        name: name,\n        id: id,\n        type: type,\n        required: required,\n        readOnly: readOnly,\n        disabled: disabled,\n        tabIndex: tabIndex,\n        className: \"\".concat(prefixCls, \"-input\"),\n        checked: !!checked,\n        onClick: onClick,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onKeyUp: onKeyUp,\n        onKeyDown: onKeyDown,\n        onKeyPress: onKeyPress,\n        onChange: this.handleChange,\n        autoFocus: autoFocus,\n        ref: this.saveInput,\n        value: value\n      }, globalProps)), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-inner\")\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, state) {\n      if ('checked' in props) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          checked: props.checked\n        });\n      }\n      return null;\n    }\n  }]);\n  return Checkbox;\n}(Component);\nCheckbox.defaultProps = {\n  prefixCls: 'rc-checkbox',\n  className: '',\n  style: {},\n  type: 'checkbox',\n  defaultChecked: false,\n  onFocus: function onFocus() {},\n  onBlur: function onBlur() {},\n  onChange: function onChange() {},\n  onKeyDown: function onKeyDown() {},\n  onKeyPress: function onKeyPress() {},\n  onKeyUp: function onKeyUp() {}\n};\nexport default Checkbox;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "Component", "classNames", "Checkbox", "_Component", "_super", "props", "_this", "call", "handleChange", "e", "_this$props", "disabled", "onChange", "setState", "checked", "target", "stopPropagation", "preventDefault", "nativeEvent", "saveInput", "node", "input", "defaultChecked", "state", "key", "value", "focus", "blur", "render", "_classNames", "_this$props2", "prefixCls", "className", "style", "name", "id", "type", "readOnly", "tabIndex", "onClick", "onFocus", "onBlur", "onKeyDown", "onKeyPress", "onKeyUp", "autoFocus", "required", "others", "globalProps", "Object", "keys", "reduce", "prev", "substr", "classString", "concat", "createElement", "ref", "getDerivedStateFromProps", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-checkbox/es/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport React, { Component } from 'react';\nimport classNames from 'classnames';\n\nvar Checkbox = /*#__PURE__*/function (_Component) {\n  _inherits(Checkbox, _Component);\n\n  var _super = _createSuper(Checkbox);\n\n  function Checkbox(props) {\n    var _this;\n\n    _classCallCheck(this, Checkbox);\n\n    _this = _super.call(this, props);\n\n    _this.handleChange = function (e) {\n      var _this$props = _this.props,\n          disabled = _this$props.disabled,\n          onChange = _this$props.onChange;\n\n      if (disabled) {\n        return;\n      }\n\n      if (!('checked' in _this.props)) {\n        _this.setState({\n          checked: e.target.checked\n        });\n      }\n\n      if (onChange) {\n        onChange({\n          target: _objectSpread(_objectSpread({}, _this.props), {}, {\n            checked: e.target.checked\n          }),\n          stopPropagation: function stopPropagation() {\n            e.stopPropagation();\n          },\n          preventDefault: function preventDefault() {\n            e.preventDefault();\n          },\n          nativeEvent: e.nativeEvent\n        });\n      }\n    };\n\n    _this.saveInput = function (node) {\n      _this.input = node;\n    };\n\n    var checked = 'checked' in props ? props.checked : props.defaultChecked;\n    _this.state = {\n      checked: checked\n    };\n    return _this;\n  }\n\n  _createClass(Checkbox, [{\n    key: \"focus\",\n    value: function focus() {\n      this.input.focus();\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.input.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n\n      var _this$props2 = this.props,\n          prefixCls = _this$props2.prefixCls,\n          className = _this$props2.className,\n          style = _this$props2.style,\n          name = _this$props2.name,\n          id = _this$props2.id,\n          type = _this$props2.type,\n          disabled = _this$props2.disabled,\n          readOnly = _this$props2.readOnly,\n          tabIndex = _this$props2.tabIndex,\n          onClick = _this$props2.onClick,\n          onFocus = _this$props2.onFocus,\n          onBlur = _this$props2.onBlur,\n          onKeyDown = _this$props2.onKeyDown,\n          onKeyPress = _this$props2.onKeyPress,\n          onKeyUp = _this$props2.onKeyUp,\n          autoFocus = _this$props2.autoFocus,\n          value = _this$props2.value,\n          required = _this$props2.required,\n          others = _objectWithoutProperties(_this$props2, [\"prefixCls\", \"className\", \"style\", \"name\", \"id\", \"type\", \"disabled\", \"readOnly\", \"tabIndex\", \"onClick\", \"onFocus\", \"onBlur\", \"onKeyDown\", \"onKeyPress\", \"onKeyUp\", \"autoFocus\", \"value\", \"required\"]);\n\n      var globalProps = Object.keys(others).reduce(function (prev, key) {\n        if (key.substr(0, 5) === 'aria-' || key.substr(0, 5) === 'data-' || key === 'role') {\n          // eslint-disable-next-line no-param-reassign\n          prev[key] = others[key];\n        }\n\n        return prev;\n      }, {});\n      var checked = this.state.checked;\n      var classString = classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames));\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classString,\n        style: style\n      }, /*#__PURE__*/React.createElement(\"input\", _extends({\n        name: name,\n        id: id,\n        type: type,\n        required: required,\n        readOnly: readOnly,\n        disabled: disabled,\n        tabIndex: tabIndex,\n        className: \"\".concat(prefixCls, \"-input\"),\n        checked: !!checked,\n        onClick: onClick,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onKeyUp: onKeyUp,\n        onKeyDown: onKeyDown,\n        onKeyPress: onKeyPress,\n        onChange: this.handleChange,\n        autoFocus: autoFocus,\n        ref: this.saveInput,\n        value: value\n      }, globalProps)), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-inner\")\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, state) {\n      if ('checked' in props) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          checked: props.checked\n        });\n      }\n\n      return null;\n    }\n  }]);\n\n  return Checkbox;\n}(Component);\n\nCheckbox.defaultProps = {\n  prefixCls: 'rc-checkbox',\n  className: '',\n  style: {},\n  type: 'checkbox',\n  defaultChecked: false,\n  onFocus: function onFocus() {},\n  onBlur: function onBlur() {},\n  onChange: function onChange() {},\n  onKeyDown: function onKeyDown() {},\n  onKeyPress: function onKeyPress() {},\n  onKeyUp: function onKeyUp() {}\n};\nexport default Checkbox;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE;AACA,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,YAAY;AAEnC,IAAIC,QAAQ,GAAG,aAAa,UAAUC,UAAU,EAAE;EAChDN,SAAS,CAACK,QAAQ,EAAEC,UAAU,CAAC;EAE/B,IAAIC,MAAM,GAAGN,YAAY,CAACI,QAAQ,CAAC;EAEnC,SAASA,QAAQA,CAACG,KAAK,EAAE;IACvB,IAAIC,KAAK;IAETX,eAAe,CAAC,IAAI,EAAEO,QAAQ,CAAC;IAE/BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAEhCC,KAAK,CAACE,YAAY,GAAG,UAAUC,CAAC,EAAE;MAChC,IAAIC,WAAW,GAAGJ,KAAK,CAACD,KAAK;QACzBM,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;MAEnC,IAAID,QAAQ,EAAE;QACZ;MACF;MAEA,IAAI,EAAE,SAAS,IAAIL,KAAK,CAACD,KAAK,CAAC,EAAE;QAC/BC,KAAK,CAACO,QAAQ,CAAC;UACbC,OAAO,EAAEL,CAAC,CAACM,MAAM,CAACD;QACpB,CAAC,CAAC;MACJ;MAEA,IAAIF,QAAQ,EAAE;QACZA,QAAQ,CAAC;UACPG,MAAM,EAAErB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEY,KAAK,CAACD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACxDS,OAAO,EAAEL,CAAC,CAACM,MAAM,CAACD;UACpB,CAAC,CAAC;UACFE,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;YAC1CP,CAAC,CAACO,eAAe,CAAC,CAAC;UACrB,CAAC;UACDC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;YACxCR,CAAC,CAACQ,cAAc,CAAC,CAAC;UACpB,CAAC;UACDC,WAAW,EAAET,CAAC,CAACS;QACjB,CAAC,CAAC;MACJ;IACF,CAAC;IAEDZ,KAAK,CAACa,SAAS,GAAG,UAAUC,IAAI,EAAE;MAChCd,KAAK,CAACe,KAAK,GAAGD,IAAI;IACpB,CAAC;IAED,IAAIN,OAAO,GAAG,SAAS,IAAIT,KAAK,GAAGA,KAAK,CAACS,OAAO,GAAGT,KAAK,CAACiB,cAAc;IACvEhB,KAAK,CAACiB,KAAK,GAAG;MACZT,OAAO,EAAEA;IACX,CAAC;IACD,OAAOR,KAAK;EACd;EAEAV,YAAY,CAACM,QAAQ,EAAE,CAAC;IACtBsB,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAAA,EAAG;MACtB,IAAI,CAACL,KAAK,CAACK,KAAK,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASE,IAAIA,CAAA,EAAG;MACrB,IAAI,CAACN,KAAK,CAACM,IAAI,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASG,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;MAEf,IAAIC,YAAY,GAAG,IAAI,CAACzB,KAAK;QACzB0B,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,KAAK,GAAGH,YAAY,CAACG,KAAK;QAC1BC,IAAI,GAAGJ,YAAY,CAACI,IAAI;QACxBC,EAAE,GAAGL,YAAY,CAACK,EAAE;QACpBC,IAAI,GAAGN,YAAY,CAACM,IAAI;QACxBzB,QAAQ,GAAGmB,YAAY,CAACnB,QAAQ;QAChC0B,QAAQ,GAAGP,YAAY,CAACO,QAAQ;QAChCC,QAAQ,GAAGR,YAAY,CAACQ,QAAQ;QAChCC,OAAO,GAAGT,YAAY,CAACS,OAAO;QAC9BC,OAAO,GAAGV,YAAY,CAACU,OAAO;QAC9BC,MAAM,GAAGX,YAAY,CAACW,MAAM;QAC5BC,SAAS,GAAGZ,YAAY,CAACY,SAAS;QAClCC,UAAU,GAAGb,YAAY,CAACa,UAAU;QACpCC,OAAO,GAAGd,YAAY,CAACc,OAAO;QAC9BC,SAAS,GAAGf,YAAY,CAACe,SAAS;QAClCpB,KAAK,GAAGK,YAAY,CAACL,KAAK;QAC1BqB,QAAQ,GAAGhB,YAAY,CAACgB,QAAQ;QAChCC,MAAM,GAAGtD,wBAAwB,CAACqC,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;MAE1P,IAAIkB,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,UAAUC,IAAI,EAAE5B,GAAG,EAAE;QAChE,IAAIA,GAAG,CAAC6B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAI7B,GAAG,CAAC6B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAI7B,GAAG,KAAK,MAAM,EAAE;UAClF;UACA4B,IAAI,CAAC5B,GAAG,CAAC,GAAGuB,MAAM,CAACvB,GAAG,CAAC;QACzB;QAEA,OAAO4B,IAAI;MACb,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAItC,OAAO,GAAG,IAAI,CAACS,KAAK,CAACT,OAAO;MAChC,IAAIwC,WAAW,GAAGrD,UAAU,CAAC8B,SAAS,EAAEC,SAAS,GAAGH,WAAW,GAAG,CAAC,CAAC,EAAErC,eAAe,CAACqC,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACxB,SAAS,EAAE,UAAU,CAAC,EAAEjB,OAAO,CAAC,EAAEtB,eAAe,CAACqC,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACxB,SAAS,EAAE,WAAW,CAAC,EAAEpB,QAAQ,CAAC,EAAEkB,WAAW,CAAC,CAAC;MACvO,OAAO,aAAa9B,KAAK,CAACyD,aAAa,CAAC,MAAM,EAAE;QAC9CxB,SAAS,EAAEsB,WAAW;QACtBrB,KAAK,EAAEA;MACT,CAAC,EAAE,aAAalC,KAAK,CAACyD,aAAa,CAAC,OAAO,EAAEjE,QAAQ,CAAC;QACpD2C,IAAI,EAAEA,IAAI;QACVC,EAAE,EAAEA,EAAE;QACNC,IAAI,EAAEA,IAAI;QACVU,QAAQ,EAAEA,QAAQ;QAClBT,QAAQ,EAAEA,QAAQ;QAClB1B,QAAQ,EAAEA,QAAQ;QAClB2B,QAAQ,EAAEA,QAAQ;QAClBN,SAAS,EAAE,EAAE,CAACuB,MAAM,CAACxB,SAAS,EAAE,QAAQ,CAAC;QACzCjB,OAAO,EAAE,CAAC,CAACA,OAAO;QAClByB,OAAO,EAAEA,OAAO;QAChBC,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA,MAAM;QACdG,OAAO,EAAEA,OAAO;QAChBF,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtB/B,QAAQ,EAAE,IAAI,CAACJ,YAAY;QAC3BqC,SAAS,EAAEA,SAAS;QACpBY,GAAG,EAAE,IAAI,CAACtC,SAAS;QACnBM,KAAK,EAAEA;MACT,CAAC,EAAEuB,WAAW,CAAC,CAAC,EAAE,aAAajD,KAAK,CAACyD,aAAa,CAAC,MAAM,EAAE;QACzDxB,SAAS,EAAE,EAAE,CAACuB,MAAM,CAACxB,SAAS,EAAE,QAAQ;MAC1C,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,EAAE,CAAC;IACHP,GAAG,EAAE,0BAA0B;IAC/BC,KAAK,EAAE,SAASiC,wBAAwBA,CAACrD,KAAK,EAAEkB,KAAK,EAAE;MACrD,IAAI,SAAS,IAAIlB,KAAK,EAAE;QACtB,OAAOX,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDT,OAAO,EAAET,KAAK,CAACS;QACjB,CAAC,CAAC;MACJ;MAEA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EAEH,OAAOZ,QAAQ;AACjB,CAAC,CAACF,SAAS,CAAC;AAEZE,QAAQ,CAACyD,YAAY,GAAG;EACtB5B,SAAS,EAAE,aAAa;EACxBC,SAAS,EAAE,EAAE;EACbC,KAAK,EAAE,CAAC,CAAC;EACTG,IAAI,EAAE,UAAU;EAChBd,cAAc,EAAE,KAAK;EACrBkB,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;EAC9BC,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG,CAAC,CAAC;EAC5B7B,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG,CAAC,CAAC;EAChC8B,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG,CAAC,CAAC;EAClCC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG,CAAC,CAAC;EACpCC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;AAC/B,CAAC;AACD,eAAe1C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}