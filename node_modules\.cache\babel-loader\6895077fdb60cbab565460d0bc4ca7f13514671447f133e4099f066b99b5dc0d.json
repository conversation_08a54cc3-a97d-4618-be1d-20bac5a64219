{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Title from './Title';\nimport Paragraph from './Paragraph';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport SkeletonInput from './Input';\nimport SkeletonImage from './Image';\nfunction getComponentProps(prop) {\n  if (prop && _typeof(prop) === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  var basicProps = {}; // Width\n\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  } // Rows\n\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nvar Skeleton = function Skeleton(props) {\n  var customizePrefixCls = props.prefixCls,\n    loading = props.loading,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    avatar = props.avatar,\n    title = props.title,\n    paragraph = props.paragraph,\n    active = props.active,\n    round = props.round;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  if (loading || !('loading' in props)) {\n    var _classNames;\n    var hasAvatar = !!avatar;\n    var hasTitle = !!title;\n    var hasParagraph = !!paragraph; // Avatar\n\n    var avatarNode;\n    if (hasAvatar) {\n      var avatarProps = _extends(_extends({\n        prefixCls: \"\".concat(prefixCls, \"-avatar\")\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar)); // We direct use SkeletonElement as avatar in skeleton internal.\n\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-header\")\n      }, /*#__PURE__*/React.createElement(Element, avatarProps));\n    }\n    var contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      var $title;\n      if (hasTitle) {\n        var titleProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-title\")\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, titleProps);\n      } // Paragraph\n\n      var paragraphNode;\n      if (hasParagraph) {\n        var paragraphProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-paragraph\")\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, paragraphProps);\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content\")\n      }, $title, paragraphNode);\n    }\n    var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-avatar\"), hasAvatar), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-round\"), round), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, avatarNode, contentNode);\n  }\n  return typeof children !== 'undefined' ? children : null;\n};\nSkeleton.defaultProps = {\n  avatar: false,\n  title: true,\n  paragraph: true\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nexport default Skeleton;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_typeof", "React", "classNames", "Title", "Paragraph", "ConfigContext", "Element", "SkeletonAvatar", "SkeletonButton", "SkeletonInput", "SkeletonImage", "getComponentProps", "prop", "getAvatarBasicProps", "hasTitle", "hasParagraph", "size", "shape", "getTitleBasicProps", "has<PERSON><PERSON><PERSON>", "width", "getParagraphBasicProps", "basicProps", "rows", "Skeleton", "props", "customizePrefixCls", "prefixCls", "loading", "className", "style", "children", "avatar", "title", "paragraph", "active", "round", "_React$useContext", "useContext", "getPrefixCls", "direction", "_classNames", "avatarNode", "avatarProps", "concat", "createElement", "contentNode", "$title", "titleProps", "paragraphNode", "paragraphProps", "cls", "defaultProps", "<PERSON><PERSON>", "Avatar", "Input", "Image"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Skeleton.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Title from './Title';\nimport Paragraph from './Paragraph';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport SkeletonInput from './Input';\nimport SkeletonImage from './Image';\n\nfunction getComponentProps(prop) {\n  if (prop && _typeof(prop) === 'object') {\n    return prop;\n  }\n\n  return {};\n}\n\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\n\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n\n  return {};\n}\n\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  var basicProps = {}; // Width\n\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  } // Rows\n\n\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n\n  return basicProps;\n}\n\nvar Skeleton = function Skeleton(props) {\n  var customizePrefixCls = props.prefixCls,\n      loading = props.loading,\n      className = props.className,\n      style = props.style,\n      children = props.children,\n      avatar = props.avatar,\n      title = props.title,\n      paragraph = props.paragraph,\n      active = props.active,\n      round = props.round;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n\n  if (loading || !('loading' in props)) {\n    var _classNames;\n\n    var hasAvatar = !!avatar;\n    var hasTitle = !!title;\n    var hasParagraph = !!paragraph; // Avatar\n\n    var avatarNode;\n\n    if (hasAvatar) {\n      var avatarProps = _extends(_extends({\n        prefixCls: \"\".concat(prefixCls, \"-avatar\")\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar)); // We direct use SkeletonElement as avatar in skeleton internal.\n\n\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-header\")\n      }, /*#__PURE__*/React.createElement(Element, avatarProps));\n    }\n\n    var contentNode;\n\n    if (hasTitle || hasParagraph) {\n      // Title\n      var $title;\n\n      if (hasTitle) {\n        var titleProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-title\")\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n\n        $title = /*#__PURE__*/React.createElement(Title, titleProps);\n      } // Paragraph\n\n\n      var paragraphNode;\n\n      if (hasParagraph) {\n        var paragraphProps = _extends(_extends({\n          prefixCls: \"\".concat(prefixCls, \"-paragraph\")\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, paragraphProps);\n      }\n\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-content\")\n      }, $title, paragraphNode);\n    }\n\n    var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-avatar\"), hasAvatar), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-round\"), round), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, avatarNode, contentNode);\n  }\n\n  return typeof children !== 'undefined' ? children : null;\n};\n\nSkeleton.defaultProps = {\n  avatar: false,\n  title: true,\n  paragraph: true\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nexport default Skeleton;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,UAAU;AACrC,OAAOC,cAAc,MAAM,UAAU;AACrC,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,aAAa,MAAM,SAAS;AAEnC,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIA,IAAI,IAAIZ,OAAO,CAACY,IAAI,CAAC,KAAK,QAAQ,EAAE;IACtC,OAAOA,IAAI;EACb;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EACnD,IAAID,QAAQ,IAAI,CAACC,YAAY,EAAE;IAC7B;IACA,OAAO;MACLC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;IACT,CAAC;EACH;EAEA,OAAO;IACLD,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;EACT,CAAC;AACH;AAEA,SAASC,kBAAkBA,CAACC,SAAS,EAAEJ,YAAY,EAAE;EACnD,IAAI,CAACI,SAAS,IAAIJ,YAAY,EAAE;IAC9B,OAAO;MACLK,KAAK,EAAE;IACT,CAAC;EACH;EAEA,IAAID,SAAS,IAAIJ,YAAY,EAAE;IAC7B,OAAO;MACLK,KAAK,EAAE;IACT,CAAC;EACH;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASC,sBAAsBA,CAACF,SAAS,EAAEL,QAAQ,EAAE;EACnD,IAAIQ,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;EAErB,IAAI,CAACH,SAAS,IAAI,CAACL,QAAQ,EAAE;IAC3BQ,UAAU,CAACF,KAAK,GAAG,KAAK;EAC1B,CAAC,CAAC;;EAGF,IAAI,CAACD,SAAS,IAAIL,QAAQ,EAAE;IAC1BQ,UAAU,CAACC,IAAI,GAAG,CAAC;EACrB,CAAC,MAAM;IACLD,UAAU,CAACC,IAAI,GAAG,CAAC;EACrB;EAEA,OAAOD,UAAU;AACnB;AAEA,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACpCC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,KAAK,GAAGX,KAAK,CAACW,KAAK;EAEvB,IAAIC,iBAAiB,GAAGpC,KAAK,CAACqC,UAAU,CAACjC,aAAa,CAAC;IACnDkC,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIb,SAAS,GAAGY,YAAY,CAAC,UAAU,EAAEb,kBAAkB,CAAC;EAE5D,IAAIE,OAAO,IAAI,EAAE,SAAS,IAAIH,KAAK,CAAC,EAAE;IACpC,IAAIgB,WAAW;IAEf,IAAItB,SAAS,GAAG,CAAC,CAACa,MAAM;IACxB,IAAIlB,QAAQ,GAAG,CAAC,CAACmB,KAAK;IACtB,IAAIlB,YAAY,GAAG,CAAC,CAACmB,SAAS,CAAC,CAAC;;IAEhC,IAAIQ,UAAU;IAEd,IAAIvB,SAAS,EAAE;MACb,IAAIwB,WAAW,GAAG5C,QAAQ,CAACA,QAAQ,CAAC;QAClC4B,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACjB,SAAS,EAAE,SAAS;MAC3C,CAAC,EAAEd,mBAAmB,CAACC,QAAQ,EAAEC,YAAY,CAAC,CAAC,EAAEJ,iBAAiB,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC;;MAG7EU,UAAU,GAAG,aAAazC,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;QACnDhB,SAAS,EAAE,EAAE,CAACe,MAAM,CAACjB,SAAS,EAAE,SAAS;MAC3C,CAAC,EAAE,aAAa1B,KAAK,CAAC4C,aAAa,CAACvC,OAAO,EAAEqC,WAAW,CAAC,CAAC;IAC5D;IAEA,IAAIG,WAAW;IAEf,IAAIhC,QAAQ,IAAIC,YAAY,EAAE;MAC5B;MACA,IAAIgC,MAAM;MAEV,IAAIjC,QAAQ,EAAE;QACZ,IAAIkC,UAAU,GAAGjD,QAAQ,CAACA,QAAQ,CAAC;UACjC4B,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACjB,SAAS,EAAE,QAAQ;QAC1C,CAAC,EAAET,kBAAkB,CAACC,SAAS,EAAEJ,YAAY,CAAC,CAAC,EAAEJ,iBAAiB,CAACsB,KAAK,CAAC,CAAC;QAE1Ec,MAAM,GAAG,aAAa9C,KAAK,CAAC4C,aAAa,CAAC1C,KAAK,EAAE6C,UAAU,CAAC;MAC9D,CAAC,CAAC;;MAGF,IAAIC,aAAa;MAEjB,IAAIlC,YAAY,EAAE;QAChB,IAAImC,cAAc,GAAGnD,QAAQ,CAACA,QAAQ,CAAC;UACrC4B,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACjB,SAAS,EAAE,YAAY;QAC9C,CAAC,EAAEN,sBAAsB,CAACF,SAAS,EAAEL,QAAQ,CAAC,CAAC,EAAEH,iBAAiB,CAACuB,SAAS,CAAC,CAAC;QAE9Ee,aAAa,GAAG,aAAahD,KAAK,CAAC4C,aAAa,CAACzC,SAAS,EAAE8C,cAAc,CAAC;MAC7E;MAEAJ,WAAW,GAAG,aAAa7C,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;QACpDhB,SAAS,EAAE,EAAE,CAACe,MAAM,CAACjB,SAAS,EAAE,UAAU;MAC5C,CAAC,EAAEoB,MAAM,EAAEE,aAAa,CAAC;IAC3B;IAEA,IAAIE,GAAG,GAAGjD,UAAU,CAACyB,SAAS,GAAGc,WAAW,GAAG,CAAC,CAAC,EAAE3C,eAAe,CAAC2C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACjB,SAAS,EAAE,cAAc,CAAC,EAAER,SAAS,CAAC,EAAErB,eAAe,CAAC2C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACjB,SAAS,EAAE,SAAS,CAAC,EAAEQ,MAAM,CAAC,EAAErC,eAAe,CAAC2C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACjB,SAAS,EAAE,MAAM,CAAC,EAAEa,SAAS,KAAK,KAAK,CAAC,EAAE1C,eAAe,CAAC2C,WAAW,EAAE,EAAE,CAACG,MAAM,CAACjB,SAAS,EAAE,QAAQ,CAAC,EAAES,KAAK,CAAC,EAAEK,WAAW,GAAGZ,SAAS,CAAC;IACvX,OAAO,aAAa5B,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;MAC7ChB,SAAS,EAAEsB,GAAG;MACdrB,KAAK,EAAEA;IACT,CAAC,EAAEY,UAAU,EAAEI,WAAW,CAAC;EAC7B;EAEA,OAAO,OAAOf,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI;AAC1D,CAAC;AAEDP,QAAQ,CAAC4B,YAAY,GAAG;EACtBpB,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE;AACb,CAAC;AACDV,QAAQ,CAAC6B,MAAM,GAAG7C,cAAc;AAChCgB,QAAQ,CAAC8B,MAAM,GAAG/C,cAAc;AAChCiB,QAAQ,CAAC+B,KAAK,GAAG9C,aAAa;AAC9Be,QAAQ,CAACgC,KAAK,GAAG9C,aAAa;AAC9B,eAAec,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}