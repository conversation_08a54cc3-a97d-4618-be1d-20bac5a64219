{"ast": null, "code": "import canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nexport var canUseDocElement = function canUseDocElement() {\n  return canUseDom() && window.document.documentElement;\n};\nexport { isStyleSupport };\nvar flexGapSupported;\nexport var detectFlexGapSupported = function detectFlexGapSupported() {\n  if (!canUseDocElement()) {\n    return false;\n  }\n  if (flexGapSupported !== undefined) {\n    return flexGapSupported;\n  } // create flex container with row-gap set\n\n  var flex = document.createElement('div');\n  flex.style.display = 'flex';\n  flex.style.flexDirection = 'column';\n  flex.style.rowGap = '1px'; // create two, elements inside it\n\n  flex.appendChild(document.createElement('div'));\n  flex.appendChild(document.createElement('div')); // append to the DOM (needed to obtain scrollHeight)\n\n  document.body.appendChild(flex);\n  flexGapSupported = flex.scrollHeight === 1; // flex container should be 1px high from the row-gap\n\n  document.body.removeChild(flex);\n  return flexGapSupported;\n};", "map": {"version": 3, "names": ["canUseDom", "isStyleSupport", "canUseDocElement", "window", "document", "documentElement", "flexGapSupported", "detectFlexGapSupported", "undefined", "flex", "createElement", "style", "display", "flexDirection", "rowGap", "append<PERSON><PERSON><PERSON>", "body", "scrollHeight", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/styleChecker.js"], "sourcesContent": ["import canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nexport var canUseDocElement = function canUseDocElement() {\n  return canUseDom() && window.document.documentElement;\n};\nexport { isStyleSupport };\nvar flexGapSupported;\nexport var detectFlexGapSupported = function detectFlexGapSupported() {\n  if (!canUseDocElement()) {\n    return false;\n  }\n\n  if (flexGapSupported !== undefined) {\n    return flexGapSupported;\n  } // create flex container with row-gap set\n\n\n  var flex = document.createElement('div');\n  flex.style.display = 'flex';\n  flex.style.flexDirection = 'column';\n  flex.style.rowGap = '1px'; // create two, elements inside it\n\n  flex.appendChild(document.createElement('div'));\n  flex.appendChild(document.createElement('div')); // append to the DOM (needed to obtain scrollHeight)\n\n  document.body.appendChild(flex);\n  flexGapSupported = flex.scrollHeight === 1; // flex container should be 1px high from the row-gap\n\n  document.body.removeChild(flex);\n  return flexGapSupported;\n};"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,OAAO,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;EACxD,OAAOF,SAAS,CAAC,CAAC,IAAIG,MAAM,CAACC,QAAQ,CAACC,eAAe;AACvD,CAAC;AACD,SAASJ,cAAc;AACvB,IAAIK,gBAAgB;AACpB,OAAO,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;EACpE,IAAI,CAACL,gBAAgB,CAAC,CAAC,EAAE;IACvB,OAAO,KAAK;EACd;EAEA,IAAII,gBAAgB,KAAKE,SAAS,EAAE;IAClC,OAAOF,gBAAgB;EACzB,CAAC,CAAC;;EAGF,IAAIG,IAAI,GAAGL,QAAQ,CAACM,aAAa,CAAC,KAAK,CAAC;EACxCD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;EAC3BH,IAAI,CAACE,KAAK,CAACE,aAAa,GAAG,QAAQ;EACnCJ,IAAI,CAACE,KAAK,CAACG,MAAM,GAAG,KAAK,CAAC,CAAC;;EAE3BL,IAAI,CAACM,WAAW,CAACX,QAAQ,CAACM,aAAa,CAAC,KAAK,CAAC,CAAC;EAC/CD,IAAI,CAACM,WAAW,CAACX,QAAQ,CAACM,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEjDN,QAAQ,CAACY,IAAI,CAACD,WAAW,CAACN,IAAI,CAAC;EAC/BH,gBAAgB,GAAGG,IAAI,CAACQ,YAAY,KAAK,CAAC,CAAC,CAAC;;EAE5Cb,QAAQ,CAACY,IAAI,CAACE,WAAW,CAACT,IAAI,CAAC;EAC/B,OAAOH,gBAAgB;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}