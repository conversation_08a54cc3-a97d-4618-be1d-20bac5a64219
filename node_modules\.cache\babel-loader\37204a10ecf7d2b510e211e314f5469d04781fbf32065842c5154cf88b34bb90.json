{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\previsioneAcquisti.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* BuyerPrediction - Previsione sull'acquistato\n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { PanelMenu } from 'primereact/panelmenu';\nimport { Chart } from 'primereact/chart';\nimport { TabView, TabPanel } from 'primereact/tabview';\nimport { Dialog } from \"primereact/dialog\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { Toast } from 'primereact/toast';\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass BuyerPrediction extends Component {\n  constructor(props) {\n    super(props);\n    this.onWarehouseChange = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      await APIRequest('GET', \"statistic/supplysuggestion?mese=1&warehouse=\".concat(e.value)).then(res => {\n        this.setState({\n          results: res.data,\n          results2: res.data\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n      var date = [];\n      var dati = [];\n      var dati2 = [];\n      await APIRequest('GET', \"statistic/salesflow?warehouse=\".concat(e.value)).then(res => {\n        res.data.sort((a, b) => {\n          if (a.anno < b.anno) {\n            return -1;\n          }\n          return /*#__PURE__*/_jsxDEV(\"span\", {}, a.anno, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 29\n          }, this);\n        });\n        res.data.forEach(element => {\n          date.push(element.mese + '/' + element.anno);\n          dati.push(parseFloat(element.n_customer));\n          dati2.push(parseFloat(element.n_prodotti_ordinati));\n          /* label.push(element.external_code) */\n        });\n        this.setState({\n          basicData: {\n            labels: date,\n            datasets: [{\n              label: 'Clienti attivi',\n              backgroundColor: '#292c5c',\n              data: dati\n            }]\n          },\n          basicData2: {\n            labels: date,\n            datasets: [{\n              label: 'Prodotti ordinati',\n              backgroundColor: '#8EB6DC',\n              data: dati2\n            }]\n          }\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n      this.defineSort();\n    };\n    this.state = {\n      results: null,\n      displayed: true,\n      results2: null,\n      basicData: null,\n      basicData2: null,\n      resultDialog: false,\n      resultDialog2: false,\n      value1: {\n        name: Costanti.Gennaio,\n        code: '1'\n      },\n      /*value2: null, { name: 'Mancanze', code: 'Mancanze' } */\n      search: '',\n      selectedWarehouse: null\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterProd = e => {\n      this.setState({\n        search: e.item.label\n      });\n      var subfamily = [];\n      /* var family = []; */\n      var risultato = [];\n      var finRes = [];\n      var brand = [];\n      var group = [];\n      var prezzi = [];\n      var data = this.state.results;\n      var filter = '';\n      if (e.item.label === 'ALTRO') {\n        filter = null;\n      } else {\n        filter = e.item.label.trim().toLowerCase();\n      }\n      if (filter === null || filter.length > 0) {\n        data.forEach(element => {\n          if (element.subfamily !== null && element.subfamily !== '') {\n            subfamily.push(element.subfamily);\n          } else {\n            if (e.item.value === 'subfamily') {\n              finRes = data.filter(element => element.subfamily === null || element.subfamily === '');\n            }\n          }\n          if (element.group !== null && element.group !== '') {\n            group.push(element.group);\n          } else {\n            if (e.item.value === 'group') {\n              finRes = data.filter(element => element.subfamily === e.item.dad && (element.group === null || element.group === ''));\n            }\n          }\n          /* if (element.family !== null && element.family !== '') {\n              family.push(element.family)\n          } else {\n              risultato = data.filter(element => element.family === null || element.family === '');\n          } */\n          if (element.brand !== null && element.brand !== '') {\n            brand.push(element.brand);\n          } else {\n            if (e.item.value === 'brand') {\n              finRes = data.filter(element => element.brand === null || element.brand === '');\n            }\n          }\n        });\n        var subf = subfamily.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        var gruppo = group.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        /*  var fam = family.filter(function (i) {\n             return i.toLowerCase().match(filter);\n         }); */\n        var brandPresent = brand.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        if (subf.length > 0) {\n          risultato = data.filter(element => element.subfamily === subf[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (gruppo.length > 0) {\n          risultato = data.filter(element => element.group === gruppo[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        } /* if (fam.length > 0) {\n          risultato = data.filter(element => element.family === fam[0]);\n          risultato.forEach(el => {\n            finRes.push(el)\n          }) \n          } */\n        if (brandPresent.length > 0) {\n          risultato = data.filter(element => element.brand === brandPresent[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (finRes.length > 0) {\n          finRes.forEach(el => {\n            prezzi.push(parseFloat(el.price));\n          });\n          prezzi.sort(function (a, b) {\n            if (a < b) {\n              return -1;\n            }\n            return /*#__PURE__*/_jsxDEV(\"span\", {}, a, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 32\n            }, this);\n          });\n          this.setState({\n            results2: finRes\n          });\n        } else {\n          this.setState({\n            results2: []\n          });\n        }\n      } else {\n        /* results.forEach(el => {\n            prezzi.push(parseFloat(el.price))\n        })\n        prezzi.sort(function (a, b) {\n            if (a < b) {\n                return -1\n            }\n            return <span key={a}></span>\n        }) */\n        this.setState({\n          results2: this.state.results\n        });\n      }\n    };\n    this.options = [{\n      name: Costanti.Gennaio,\n      code: '1'\n    }, {\n      name: Costanti.Febbraio,\n      code: '2'\n    }, {\n      name: Costanti.Marzo,\n      code: '3'\n    }, {\n      name: Costanti.Aprile,\n      code: '4'\n    }, {\n      name: Costanti.Maggio,\n      code: '5'\n    }, {\n      name: Costanti.Giugno,\n      code: '6'\n    }, {\n      name: Costanti.Luglio,\n      code: '7'\n    }, {\n      name: Costanti.Agosto,\n      code: '8'\n    }, {\n      name: Costanti.Settembre,\n      code: '9'\n    }, {\n      name: Costanti.Ottobre,\n      code: '10'\n    }, {\n      name: Costanti.Novembre,\n      code: '11'\n    }, {\n      name: Costanti.Dicembre,\n      code: '12'\n    }];\n    this.options2 = [{\n      name: Costanti.Mancanze,\n      code: 'Mancanze'\n    }, {\n      name: Costanti.Esuberi,\n      code: 'Esuberi'\n    }];\n    this.items = [{\n      label: Costanti.Categorie,\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.items2 = [{\n      label: \"Brand\",\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.warehouse = [];\n    this.getLightTheme = this.getLightTheme.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onWarehouseChange = this.onWarehouseChange.bind(this);\n    this.defineSort = this.defineSort.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      console.log(e);\n    });\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        displayed: false\n      });\n      await APIRequest('GET', \"statistic/supplysuggestion?mese=1&warehouse=\".concat(idWarehouse)).then(res => {\n        this.setState({\n          results: res.data,\n          results2: res.data\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n      var date = [];\n      var dati = [];\n      var dati2 = [];\n      await APIRequest('GET', \"statistic/salesflow?warehouse=\".concat(idWarehouse)).then(res => {\n        res.data.sort((a, b) => {\n          if (a.anno < b.anno) {\n            return -1;\n          }\n          return /*#__PURE__*/_jsxDEV(\"span\", {}, a.anno, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 33\n          }, this);\n        });\n        res.data.forEach(element => {\n          date.push(element.mese + '/' + element.anno);\n          dati.push(parseFloat(element.n_customer));\n          dati2.push(parseFloat(element.n_prodotti_ordinati));\n          /* label.push(element.external_code) */\n        });\n        this.setState({\n          basicData: {\n            labels: date,\n            datasets: [{\n              label: 'Clienti attivi',\n              backgroundColor: '#292c5c',\n              data: dati\n            }]\n          },\n          basicData2: {\n            labels: date,\n            datasets: [{\n              label: 'Prodotti ordinati',\n              backgroundColor: '#8EB6DC',\n              data: dati2\n            }]\n          }\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n      this.defineSort();\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n  }\n  /* Definiamo le categorie per il filtraggio  */\n  defineSort() {\n    var family = [];\n    var sottoCategorie = [];\n    var brand = [];\n    var group = [];\n    var mix = [];\n    if (this.state.results2.length > 0) {\n      this.state.results2.forEach(element => {\n        family.push(element.family);\n        sottoCategorie.push(element.subfamily);\n        brand.push(element.brand);\n        mix.push(element.subfamily + ' ' + element.group);\n        group.push(element.group);\n      });\n      family = [...new Set(family)].sort();\n      sottoCategorie = [...new Set(sottoCategorie)].sort();\n      group = [...new Set(group)].sort();\n      mix = [...new Set(mix)];\n      brand = [...new Set(brand)].sort();\n      var elementnull = [];\n      brand.forEach(element => {\n        if (element !== '') {\n          this.items2[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'brand',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items2[0].items.push(items);\n      });\n      elementnull = [];\n      sottoCategorie.forEach(element => {\n        if (element !== null && element !== '') {\n          this.items[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            },\n            items: []\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'subfamily',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items[0].items.push(items);\n      });\n      group.forEach(items => {\n        this.items[0].items.forEach(item => {\n          if (item.label !== null) {\n            if (mix.includes(item.label.concat(' ' + items))) {\n              if (items !== null && items !== '') {\n                item.items.push({\n                  label: items,\n                  command: e => {\n                    this.filterProd(e);\n                  }\n                });\n              } else {\n                item.items.push({\n                  label: \"ALTRO\",\n                  value: 'group',\n                  dad: item.label,\n                  command: e => {\n                    this.filterProd(e);\n                  }\n                });\n              }\n            }\n          }\n        });\n      });\n      /* this.setState({\n          family: family,\n          sottoCategorie: sottoCategorie,\n          brand: brand,\n          group: group\n      }) */\n    } else {\n      this.items = [{\n        label: Costanti.Categorie,\n        icon: 'pi pi-fw pi-file',\n        items: []\n      }];\n      this.items2 = [{\n        label: \"Brand\",\n        icon: 'pi pi-fw pi-file',\n        items: []\n      }];\n    }\n  }\n  getLightTheme() {\n    let basicOptions = {\n      maintainAspectRatio: false,\n      aspectRatio: .8,\n      plugins: {\n        legend: {\n          labels: {\n            color: '#495057'\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        },\n        y: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        }\n      }\n    };\n    return {\n      basicOptions\n    };\n  }\n  async cambiaDati(e) {\n    this.setState({\n      value1: e.value\n    });\n    await APIRequest('GET', \"statistic/supplysuggestion?mese=\".concat(e.value.code, \"&warehouse=\").concat(this.state.selectedWarehouse)).then(res => {\n      this.setState({\n        results: res.data,\n        results2: res.data\n      });\n      this.defineSort();\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  /* cambiaDati2(e) {\n      var filter = []\n      if (e.value.code === 'Esuberi') {\n          filter = this.state.results.filter(el => el.delta > 0)\n      } else {\n          filter = this.state.results.filter(el => el.delta < 0)\n      }\n      this.setState({\n          value2: e.value,\n          results2: filter\n      })\n  } */\n  /* Reselt filtro descrizione e codice esterno */\n  reset() {\n    this.setState({\n      results2: this.state.results,\n      search: ''\n      //value2: null\n    });\n  }\n  /* Reselt filtro categorie */\n  resetDesc() {\n    this.setState({\n      results2: this.state.results,\n      search: ''\n      //value2: null\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog2: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  render() {\n    const {\n      basicOptions\n    } = this.getLightTheme();\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: 'external_code',\n      header: Costanti.CodProd,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'description',\n      header: Costanti.Nome,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'physical_stock',\n      header: Costanti.GiacenzaFisica,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'committed_customer',\n      header: Costanti.ImpegnataCliente,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'supply_order',\n      header: Costanti.OrdinatoAlFornitore,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'availability',\n      header: Costanti.Giacenza,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'media_pond',\n      header: Costanti.QtaConsigliata,\n      showHeader: true,\n      sortable: true\n    }, {\n      field: 'delta',\n      header: 'Delta',\n      showHeader: true,\n      sortable: true\n    }];\n    /* Controllo del reset dei filtri che compare solo quando è inserito un filtro */\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.previsioneAcquisti\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TabView, {\n        className: \"tabview-custom\",\n        children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n          style: {\n            padding: '0px'\n          },\n          header: Costanti.previsioneAcquisti,\n          leftIcon: \"pi pi-th-large mr-2\",\n          children: [this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"activeFilterContainer p-2\",\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mr-3 mb-0 w-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"pi pi-home mr-2\",\n                      style: {\n                        'fontSize': '.8em'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 77\n                    }, this), Costanti.Magazzino, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                    className: \"selWar\",\n                    value: this.state.selectedWarehouse,\n                    options: this.warehouse,\n                    onChange: this.onWarehouseChange,\n                    optionLabel: \"name\",\n                    placeholder: \"Seleziona magazzino\",\n                    filter: true,\n                    filterBy: \"name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mr-3 mb-0 w-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"pi pi-calendar mr-2\",\n                      style: {\n                        'fontSize': '.8em'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 77\n                    }, this), Costanti.SelPer]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                    value: this.state.value1,\n                    options: this.options,\n                    onChange: e => this.cambiaDati(e),\n                    optionLabel: \"name\",\n                    placeholder: \"Seleziona mese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"datatable-responsive-demo wrapper\",\n            children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n              value: this.state.results2,\n              fields: fields,\n              showExtraButton: true,\n              actionExtraButton: this.openFilter,\n              labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n                className: \"mr-2\",\n                name: \"filter-outline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 51\n              }, this),\n              dataKey: \"id\",\n              responsiveLayout: \"scroll\",\n              autoLayout: true,\n              paginator: true,\n              rows: 20,\n              rowsPerPageOptions: [10, 20, 50]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          header: Costanti.StatsOrd,\n          leftIcon: \"pi pi-chart-bar mr-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row container-boxed mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-lg-6\",\n              children: /*#__PURE__*/_jsxDEV(Chart, {\n                type: \"line\",\n                data: this.state.basicData,\n                options: basicOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-lg-6\",\n              children: /*#__PURE__*/_jsxDEV(Chart, {\n                type: \"line\",\n                data: this.state.basicData2,\n                options: basicOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseChange,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog2,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(PanelMenu, {\n          className: \"panelMenuClass mb-2\",\n          model: this.items\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(PanelMenu, {\n          className: \"panelMenuClass mb-3\",\n          model: this.items2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default BuyerPrediction;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "JoyrideGen", "APIRequest", "<PERSON><PERSON>", "Dropdown", "PanelMenu", "Chart", "TabView", "TabPanel", "Dialog", "Sidebar", "Toast", "CustomDataTable", "Nav", "jsxDEV", "_jsxDEV", "BuyerPrediction", "constructor", "props", "onWarehouseChange", "e", "setState", "selectedWarehouse", "value", "window", "sessionStorage", "setItem", "concat", "then", "res", "results", "data", "results2", "catch", "console", "log", "date", "dati", "dati2", "sort", "a", "b", "anno", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "for<PERSON>ach", "element", "push", "mese", "parseFloat", "n_customer", "n_prodotti_ordinati", "basicData", "labels", "datasets", "label", "backgroundColor", "basicData2", "defineSort", "state", "displayed", "resultDialog", "resultDialog2", "value1", "name", "Gennaio", "code", "search", "filterProd", "item", "subfamily", "risultato", "finRes", "brand", "group", "<PERSON>zzi", "filter", "trim", "toLowerCase", "length", "dad", "subf", "i", "match", "gruppo", "brandPresent", "el", "price", "options", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>e", "Maggio", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre", "options2", "Mancanze", "<PERSON><PERSON><PERSON><PERSON>", "items", "Categorie", "icon", "items2", "warehouse", "getLightTheme", "bind", "reset", "resetDesc", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "entry", "warehouseName", "id", "idWarehouse", "JSON", "parse", "getItem", "family", "sottoCategorie", "mix", "Set", "elementnull", "command", "includes", "basicOptions", "maintainAspectRatio", "aspectRatio", "plugins", "legend", "color", "scales", "x", "ticks", "grid", "y", "cambiaDati", "toast", "show", "severity", "summary", "detail", "life", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fields", "field", "header", "CodProd", "showHeader", "sortable", "Nome", "GiacenzaFisica", "ImpegnataCliente", "OrdinatoAlFornitore", "Giacenza", "QtaConsigliata", "filterDnone", "ref", "previsioneAcquisti", "style", "padding", "leftIcon", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "optionLabel", "placeholder", "filterBy", "<PERSON><PERSON><PERSON><PERSON>", "showExtraButton", "actionExtraButton", "labelExtraButton", "dataKey", "responsiveLayout", "autoLayout", "paginator", "rows", "rowsPerPageOptions", "StatsOrd", "type", "visible", "Primadiproseguire", "modal", "onHide", "footer", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>", "model"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/previsioneAcquisti.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* BuyerPrediction - Previsione sull'acquistato\n*\n*/\nimport React, { Component } from \"react\";\nimport { Button } from \"primereact/button\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { PanelMenu } from 'primereact/panelmenu';\nimport { Chart } from 'primereact/chart';\nimport { TabView, TabPanel } from 'primereact/tabview';\nimport { Dialog } from \"primereact/dialog\";\nimport { Sidebar } from \"primereact/sidebar\";\nimport { Toast } from 'primereact/toast';\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Nav from \"../../components/navigation/Nav\";\n\nclass BuyerPrediction extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            displayed: true,\n            results2: null,\n            basicData: null,\n            basicData2: null,\n            resultDialog: false,\n            resultDialog2: false,\n            value1: { name: Costanti.Gennaio, code: '1' },\n            /*value2: null, { name: 'Mancanze', code: 'Mancanze' } */\n            search: '',\n            selectedWarehouse: null\n        }\n        /* Ricerca elementi per categoria selezionata */\n        this.filterProd = e => {\n            this.setState({\n                search: e.item.label\n            });\n            var subfamily = [];\n            /* var family = []; */\n            var risultato = [];\n            var finRes = [];\n            var brand = [];\n            var group = [];\n            var prezzi = []\n            var data = this.state.results;\n            var filter = ''\n            if (e.item.label === 'ALTRO') {\n                filter = null\n            } else {\n                filter = e.item.label.trim().toLowerCase();\n            }\n            if (filter === null || filter.length > 0) {\n                data.forEach(element => {\n                    if (element.subfamily !== null && element.subfamily !== '') {\n                        subfamily.push(element.subfamily)\n                    } else {\n                        if (e.item.value === 'subfamily') {\n                            finRes = data.filter(element => element.subfamily === null || element.subfamily === '');\n                        }\n                    } if (element.group !== null && element.group !== '') {\n                        group.push(element.group)\n                    } else {\n                        if (e.item.value === 'group') {\n                            finRes = data.filter(element => element.subfamily === e.item.dad && (element.group === null || element.group === ''));\n                        }\n                    }\n                    /* if (element.family !== null && element.family !== '') {\n                        family.push(element.family)\n                    } else {\n                        risultato = data.filter(element => element.family === null || element.family === '');\n                    } */\n                    if (element.brand !== null && element.brand !== '') {\n                        brand.push(element.brand)\n                    } else {\n                        if (e.item.value === 'brand') {\n                            finRes = data.filter(element => element.brand === null || element.brand === '');\n                        }\n                    }\n                })\n                var subf = subfamily.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                var gruppo = group.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                /*  var fam = family.filter(function (i) {\n                     return i.toLowerCase().match(filter);\n                 }); */\n                var brandPresent = brand.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                if (subf.length > 0) {\n                    risultato = data.filter(element => element.subfamily === subf[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                }\n                if (gruppo.length > 0) {\n                    risultato = data.filter(element => element.group === gruppo[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                } /* if (fam.length > 0) {\n                risultato = data.filter(element => element.family === fam[0]);\n                risultato.forEach(el => {\n                    finRes.push(el)\n                }) \n            } */if (brandPresent.length > 0) {\n                    risultato = data.filter(element => element.brand === brandPresent[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                } if (finRes.length > 0) {\n                    finRes.forEach(el => {\n                        prezzi.push(parseFloat(el.price))\n                    })\n                    prezzi.sort(function (a, b) {\n                        if (a < b) {\n                            return -1\n                        }\n                        return <span key={a}></span>\n                    })\n                    this.setState({\n                        results2: finRes\n                    })\n\n                }\n                else {\n                    this.setState({\n                        results2: []\n                    })\n                }\n            } else {\n                /* results.forEach(el => {\n                    prezzi.push(parseFloat(el.price))\n                })\n                prezzi.sort(function (a, b) {\n                    if (a < b) {\n                        return -1\n                    }\n                    return <span key={a}></span>\n                }) */\n                this.setState({\n                    results2: this.state.results\n                })\n\n            }\n        };\n        this.options = [\n            { name: Costanti.Gennaio, code: '1' },\n            { name: Costanti.Febbraio, code: '2' },\n            { name: Costanti.Marzo, code: '3' },\n            { name: Costanti.Aprile, code: '4' },\n            { name: Costanti.Maggio, code: '5' },\n            { name: Costanti.Giugno, code: '6' },\n            { name: Costanti.Luglio, code: '7' },\n            { name: Costanti.Agosto, code: '8' },\n            { name: Costanti.Settembre, code: '9' },\n            { name: Costanti.Ottobre, code: '10' },\n            { name: Costanti.Novembre, code: '11' },\n            { name: Costanti.Dicembre, code: '12' }\n        ]\n        this.options2 = [\n            { name: Costanti.Mancanze, code: 'Mancanze' },\n            { name: Costanti.Esuberi, code: 'Esuberi' }\n        ]\n        this.items = [{\n            label: Costanti.Categorie,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.items2 = [{\n            label: \"Brand\",\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.warehouse = []\n        this.getLightTheme = this.getLightTheme.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.onWarehouseChange = this.onWarehouseChange.bind(this);\n        this.defineSort = this.defineSort.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n            });\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            this.setState({ selectedWarehouse: idWarehouse, displayed: false });\n            await APIRequest('GET', `statistic/supplysuggestion?mese=1&warehouse=${idWarehouse}`)\n                .then(res => {\n                    this.setState({\n                        results: res.data,\n                        results2: res.data\n                    })\n                }).catch((e) => {\n                    console.log(e);\n                })\n            var date = [];\n            var dati = [];\n            var dati2 = [];\n            await APIRequest('GET', `statistic/salesflow?warehouse=${idWarehouse}`)\n                .then(res => {\n                    res.data.sort((a, b) => {\n                        if (a.anno < b.anno) {\n                            return -1\n                        }\n                        return (<span key={a.anno}></span>)\n                    })\n                    res.data.forEach((element) => {\n                        date.push(element.mese + '/' + element.anno)\n                        dati.push(parseFloat(element.n_customer))\n                        dati2.push(parseFloat(element.n_prodotti_ordinati))\n                        /* label.push(element.external_code) */\n                    })\n                    this.setState({\n                        basicData: {\n                            labels: date,\n                            datasets: [\n                                {\n                                    label: 'Clienti attivi',\n                                    backgroundColor: '#292c5c',\n                                    data: dati\n                                }\n                            ]\n                        },\n                        basicData2: {\n                            labels: date,\n                            datasets: [\n                                {\n                                    label: 'Prodotti ordinati',\n                                    backgroundColor: '#8EB6DC',\n                                    data: dati2\n                                }\n                            ]\n                        }\n                    })\n                }).catch((e) => {\n                    console.log(e);\n                })\n            this.defineSort()\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n    }\n    /* Definiamo le categorie per il filtraggio  */\n    defineSort() {\n        var family = []\n        var sottoCategorie = []\n        var brand = []\n        var group = []\n        var mix = []\n        if (this.state.results2.length > 0) {\n            this.state.results2.forEach(element => {\n                family.push(element.family)\n                sottoCategorie.push(element.subfamily)\n                brand.push(element.brand)\n                mix.push(element.subfamily + ' ' + element.group)\n                group.push(element.group)\n            })\n            family = [...new Set(family)].sort();\n            sottoCategorie = [...new Set(sottoCategorie)].sort();\n            group = [...new Set(group)].sort();\n            mix = [...new Set(mix)];\n            brand = [...new Set(brand)].sort();\n            var elementnull = []\n            brand.forEach(element => {\n                if (element !== '') {\n                    this.items2[0].items.push({ label: element, command: (e) => { this.filterProd(e) } })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'brand', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items2[0].items.push(items)\n            })\n            elementnull = []\n            sottoCategorie.forEach(element => {\n                if (element !== null && element !== '') {\n                    this.items[0].items.push({ label: element, command: (e) => { this.filterProd(e) }, items: [] })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'subfamily', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items[0].items.push(items)\n            })\n            group.forEach(items => {\n                this.items[0].items.forEach(item => {\n                    if (item.label !== null) {\n                        if (mix.includes(item.label.concat(' ' + items))) {\n                            if (items !== null && items !== '') {\n                                item.items.push({ label: items, command: (e) => { this.filterProd(e) }, })\n                            } else {\n                                item.items.push({ label: \"ALTRO\", value: 'group', dad: item.label, command: (e) => { this.filterProd(e) } })\n                            }\n                        }\n                    }\n\n                })\n            })\n            /* this.setState({\n                family: family,\n                sottoCategorie: sottoCategorie,\n                brand: brand,\n                group: group\n            }) */\n        } else {\n            this.items = [{\n                label: Costanti.Categorie,\n                icon: 'pi pi-fw pi-file',\n                items: []\n            }]\n            this.items2 = [{\n                label: \"Brand\",\n                icon: 'pi pi-fw pi-file',\n                items: []\n            }]\n        }\n    }\n    onWarehouseChange = async (e) => {\n        this.setState({ selectedWarehouse: e.value })\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        await APIRequest('GET', `statistic/supplysuggestion?mese=1&warehouse=${e.value}`)\n            .then(res => {\n                this.setState({\n                    results: res.data,\n                    results2: res.data\n                })\n            }).catch((e) => {\n                console.log(e);\n            })\n        var date = [];\n        var dati = [];\n        var dati2 = [];\n        await APIRequest('GET', `statistic/salesflow?warehouse=${e.value}`)\n            .then(res => {\n                res.data.sort((a, b) => {\n                    if (a.anno < b.anno) {\n                        return -1\n                    }\n                    return (<span key={a.anno}></span>)\n                })\n                res.data.forEach((element) => {\n                    date.push(element.mese + '/' + element.anno)\n                    dati.push(parseFloat(element.n_customer))\n                    dati2.push(parseFloat(element.n_prodotti_ordinati))\n                    /* label.push(element.external_code) */\n                })\n                this.setState({\n                    basicData: {\n                        labels: date,\n                        datasets: [\n                            {\n                                label: 'Clienti attivi',\n                                backgroundColor: '#292c5c',\n                                data: dati\n                            }\n                        ]\n                    },\n                    basicData2: {\n                        labels: date,\n                        datasets: [\n                            {\n                                label: 'Prodotti ordinati',\n                                backgroundColor: '#8EB6DC',\n                                data: dati2\n                            }\n                        ]\n                    }\n                })\n            }).catch((e) => {\n                console.log(e);\n            })\n        this.defineSort()\n    }\n    getLightTheme() {\n        let basicOptions = {\n            maintainAspectRatio: false,\n            aspectRatio: .8,\n            plugins: {\n                legend: {\n                    labels: {\n                        color: '#495057'\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                }\n            }\n        };\n        return {\n            basicOptions\n        }\n    }\n    async cambiaDati(e) {\n        this.setState({\n            value1: e.value\n        })\n        await APIRequest('GET', `statistic/supplysuggestion?mese=${e.value.code}&warehouse=${this.state.selectedWarehouse}`)\n            .then(res => {\n                this.setState({\n                    results: res.data,\n                    results2: res.data\n                })\n                this.defineSort()\n            }).catch((e) => {\n                console.log(e);\n            })\n    }\n    /* cambiaDati2(e) {\n        var filter = []\n        if (e.value.code === 'Esuberi') {\n            filter = this.state.results.filter(el => el.delta > 0)\n        } else {\n            filter = this.state.results.filter(el => el.delta < 0)\n        }\n        this.setState({\n            value2: e.value,\n            results2: filter\n        })\n    } */\n    /* Reselt filtro descrizione e codice esterno */\n    reset() {\n        this.setState({\n            results2: this.state.results,\n            search: '',\n            //value2: null\n        })\n    }\n    /* Reselt filtro categorie */\n    resetDesc() {\n        this.setState({\n            results2: this.state.results,\n            search: '',\n            //value2: null\n        })\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog2: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog2: false\n        })\n    }\n    render() {\n        const { basicOptions } = this.getLightTheme();\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            { field: 'external_code', header: Costanti.CodProd, showHeader: true, sortable: true },\n            { field: 'description', header: Costanti.Nome, showHeader: true, sortable: true },\n            { field: 'physical_stock', header: Costanti.GiacenzaFisica, showHeader: true, sortable: true },\n            { field: 'committed_customer', header: Costanti.ImpegnataCliente, showHeader: true, sortable: true },\n            { field: 'supply_order', header: Costanti.OrdinatoAlFornitore, showHeader: true, sortable: true },\n            { field: 'availability', header: Costanti.Giacenza, showHeader: true, sortable: true },\n            { field: 'media_pond', header: Costanti.QtaConsigliata, showHeader: true, sortable: true },\n            { field: 'delta', header: 'Delta', showHeader: true, sortable: true }\n        ];\n        /* Controllo del reset dei filtri che compare solo quando è inserito un filtro */\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '') {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"card\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.previsioneAcquisti}</h1>\n                </div>\n                <TabView className=\"tabview-custom\">\n                    <TabPanel style={{ padding: '0px' }} header={Costanti.previsioneAcquisti} leftIcon=\"pi pi-th-large mr-2\">\n                        {this.state.selectedWarehouse !== null &&\n                            <div className='activeFilterContainer p-2'>\n                                <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                                    <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                        <div className='d-flex justify-content-center align-items-center'>\n                                            <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                            <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseChange} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                        </div>\n                                    </li>\n                                    <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                        <div className='d-flex justify-content-center align-items-center'>\n                                            <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-calendar mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.SelPer}</h5>\n                                            <Dropdown value={this.state.value1} options={this.options} onChange={(e) => this.cambiaDati(e)} optionLabel=\"name\" placeholder=\"Seleziona mese\" />\n                                        </div>\n                                    </li>\n                                </ul>\n                            </div>\n                        }\n                        <div className=\"datatable-responsive-demo wrapper\">\n                            <CustomDataTable\n                                value={this.state.results2}\n                                fields={fields}\n                                showExtraButton={true}\n                                actionExtraButton={this.openFilter}\n                                labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                                dataKey=\"id\"\n                                responsiveLayout=\"scroll\"\n                                autoLayout={true}\n                                paginator\n                                rows={20}\n                                rowsPerPageOptions={[10, 20, 50]}\n                            />\n                        </div>\n                    </TabPanel>\n                    <TabPanel header={Costanti.StatsOrd} leftIcon=\"pi pi-chart-bar mr-2\">\n                        <div className=\"row container-boxed mt-2\">\n                            <div className=\"col-12 col-lg-6\">\n                                <Chart type=\"line\" data={this.state.basicData} options={basicOptions} />\n                            </div>\n                            <div className=\"col-12 col-lg-6\">\n                                <Chart type=\"line\" data={this.state.basicData2} options={basicOptions} />\n                            </div>\n                        </div>\n                    </TabPanel>\n                </TabView>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseChange} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog2} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <PanelMenu className=\"panelMenuClass mb-2\" model={this.items} />\n                    <PanelMenu className=\"panelMenuClass mb-3\" model={this.items2} />\n                </Sidebar>\n            </div >\n        );\n    }\n}\n\nexport default BuyerPrediction;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,oBAAoB;AACtD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,GAAG,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,eAAe,SAASjB,SAAS,CAAC;EACpCkB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAAC,KA2TjBC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7CC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEN,CAAC,CAACG,KAAK,CAAC;MACrD,MAAMrB,UAAU,CAAC,KAAK,iDAAAyB,MAAA,CAAiDP,CAAC,CAACG,KAAK,CAAE,CAAC,CAC5EK,IAAI,CAACC,GAAG,IAAI;QACT,IAAI,CAACR,QAAQ,CAAC;UACVS,OAAO,EAAED,GAAG,CAACE,IAAI;UACjBC,QAAQ,EAAEH,GAAG,CAACE;QAClB,CAAC,CAAC;MACN,CAAC,CAAC,CAACE,KAAK,CAAEb,CAAC,IAAK;QACZc,OAAO,CAACC,GAAG,CAACf,CAAC,CAAC;MAClB,CAAC,CAAC;MACN,IAAIgB,IAAI,GAAG,EAAE;MACb,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,KAAK,GAAG,EAAE;MACd,MAAMpC,UAAU,CAAC,KAAK,mCAAAyB,MAAA,CAAmCP,CAAC,CAACG,KAAK,CAAE,CAAC,CAC9DK,IAAI,CAACC,GAAG,IAAI;QACTA,GAAG,CAACE,IAAI,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACpB,IAAID,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAE;YACjB,OAAO,CAAC,CAAC;UACb;UACA,oBAAQ3B,OAAA,aAAWyB,CAAC,CAACE,IAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QACtC,CAAC,CAAC;QACFjB,GAAG,CAACE,IAAI,CAACgB,OAAO,CAAEC,OAAO,IAAK;UAC1BZ,IAAI,CAACa,IAAI,CAACD,OAAO,CAACE,IAAI,GAAG,GAAG,GAAGF,OAAO,CAACN,IAAI,CAAC;UAC5CL,IAAI,CAACY,IAAI,CAACE,UAAU,CAACH,OAAO,CAACI,UAAU,CAAC,CAAC;UACzCd,KAAK,CAACW,IAAI,CAACE,UAAU,CAACH,OAAO,CAACK,mBAAmB,CAAC,CAAC;UACnD;QACJ,CAAC,CAAC;QACF,IAAI,CAAChC,QAAQ,CAAC;UACViC,SAAS,EAAE;YACPC,MAAM,EAAEnB,IAAI;YACZoB,QAAQ,EAAE,CACN;cACIC,KAAK,EAAE,gBAAgB;cACvBC,eAAe,EAAE,SAAS;cAC1B3B,IAAI,EAAEM;YACV,CAAC;UAET,CAAC;UACDsB,UAAU,EAAE;YACRJ,MAAM,EAAEnB,IAAI;YACZoB,QAAQ,EAAE,CACN;cACIC,KAAK,EAAE,mBAAmB;cAC1BC,eAAe,EAAE,SAAS;cAC1B3B,IAAI,EAAEO;YACV,CAAC;UAET;QACJ,CAAC,CAAC;MACN,CAAC,CAAC,CAACL,KAAK,CAAEb,CAAC,IAAK;QACZc,OAAO,CAACC,GAAG,CAACf,CAAC,CAAC;MAClB,CAAC,CAAC;MACN,IAAI,CAACwC,UAAU,CAAC,CAAC;IACrB,CAAC;IAjXG,IAAI,CAACC,KAAK,GAAG;MACT/B,OAAO,EAAE,IAAI;MACbgC,SAAS,EAAE,IAAI;MACf9B,QAAQ,EAAE,IAAI;MACdsB,SAAS,EAAE,IAAI;MACfK,UAAU,EAAE,IAAI;MAChBI,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE;QAAEC,IAAI,EAAE/D,QAAQ,CAACgE,OAAO;QAAEC,IAAI,EAAE;MAAI,CAAC;MAC7C;MACAC,MAAM,EAAE,EAAE;MACV/C,iBAAiB,EAAE;IACvB,CAAC;IACD;IACA,IAAI,CAACgD,UAAU,GAAGlD,CAAC,IAAI;MACnB,IAAI,CAACC,QAAQ,CAAC;QACVgD,MAAM,EAAEjD,CAAC,CAACmD,IAAI,CAACd;MACnB,CAAC,CAAC;MACF,IAAIe,SAAS,GAAG,EAAE;MAClB;MACA,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIC,KAAK,GAAG,EAAE;MACd,IAAIC,KAAK,GAAG,EAAE;MACd,IAAIC,MAAM,GAAG,EAAE;MACf,IAAI9C,IAAI,GAAG,IAAI,CAAC8B,KAAK,CAAC/B,OAAO;MAC7B,IAAIgD,MAAM,GAAG,EAAE;MACf,IAAI1D,CAAC,CAACmD,IAAI,CAACd,KAAK,KAAK,OAAO,EAAE;QAC1BqB,MAAM,GAAG,IAAI;MACjB,CAAC,MAAM;QACHA,MAAM,GAAG1D,CAAC,CAACmD,IAAI,CAACd,KAAK,CAACsB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9C;MACA,IAAIF,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;QACtClD,IAAI,CAACgB,OAAO,CAACC,OAAO,IAAI;UACpB,IAAIA,OAAO,CAACwB,SAAS,KAAK,IAAI,IAAIxB,OAAO,CAACwB,SAAS,KAAK,EAAE,EAAE;YACxDA,SAAS,CAACvB,IAAI,CAACD,OAAO,CAACwB,SAAS,CAAC;UACrC,CAAC,MAAM;YACH,IAAIpD,CAAC,CAACmD,IAAI,CAAChD,KAAK,KAAK,WAAW,EAAE;cAC9BmD,MAAM,GAAG3C,IAAI,CAAC+C,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAACwB,SAAS,KAAK,IAAI,IAAIxB,OAAO,CAACwB,SAAS,KAAK,EAAE,CAAC;YAC3F;UACJ;UAAE,IAAIxB,OAAO,CAAC4B,KAAK,KAAK,IAAI,IAAI5B,OAAO,CAAC4B,KAAK,KAAK,EAAE,EAAE;YAClDA,KAAK,CAAC3B,IAAI,CAACD,OAAO,CAAC4B,KAAK,CAAC;UAC7B,CAAC,MAAM;YACH,IAAIxD,CAAC,CAACmD,IAAI,CAAChD,KAAK,KAAK,OAAO,EAAE;cAC1BmD,MAAM,GAAG3C,IAAI,CAAC+C,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAACwB,SAAS,KAAKpD,CAAC,CAACmD,IAAI,CAACW,GAAG,KAAKlC,OAAO,CAAC4B,KAAK,KAAK,IAAI,IAAI5B,OAAO,CAAC4B,KAAK,KAAK,EAAE,CAAC,CAAC;YACzH;UACJ;UACA;AACpB;AACA;AACA;AACA;UACoB,IAAI5B,OAAO,CAAC2B,KAAK,KAAK,IAAI,IAAI3B,OAAO,CAAC2B,KAAK,KAAK,EAAE,EAAE;YAChDA,KAAK,CAAC1B,IAAI,CAACD,OAAO,CAAC2B,KAAK,CAAC;UAC7B,CAAC,MAAM;YACH,IAAIvD,CAAC,CAACmD,IAAI,CAAChD,KAAK,KAAK,OAAO,EAAE;cAC1BmD,MAAM,GAAG3C,IAAI,CAAC+C,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAAC2B,KAAK,KAAK,IAAI,IAAI3B,OAAO,CAAC2B,KAAK,KAAK,EAAE,CAAC;YACnF;UACJ;QACJ,CAAC,CAAC;QACF,IAAIQ,IAAI,GAAGX,SAAS,CAACM,MAAM,CAAC,UAAUM,CAAC,EAAE;UACrC,OAAOA,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACK,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIQ,MAAM,GAAGV,KAAK,CAACE,MAAM,CAAC,UAAUM,CAAC,EAAE;UACnC,OAAOA,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACK,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF;AAChB;AACA;QACgB,IAAIS,YAAY,GAAGZ,KAAK,CAACG,MAAM,CAAC,UAAUM,CAAC,EAAE;UACzC,OAAOA,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACK,KAAK,CAACP,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIK,IAAI,CAACF,MAAM,GAAG,CAAC,EAAE;UACjBR,SAAS,GAAG1C,IAAI,CAAC+C,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAACwB,SAAS,KAAKW,IAAI,CAAC,CAAC,CAAC,CAAC;UACjEV,SAAS,CAAC1B,OAAO,CAACyC,EAAE,IAAI;YACpBd,MAAM,CAACzB,IAAI,CAACuC,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QACA,IAAIF,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;UACnBR,SAAS,GAAG1C,IAAI,CAAC+C,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAAC4B,KAAK,KAAKU,MAAM,CAAC,CAAC,CAAC,CAAC;UAC/Db,SAAS,CAAC1B,OAAO,CAACyC,EAAE,IAAI;YACpBd,MAAM,CAACzB,IAAI,CAACuC,EAAE,CAAC;UACnB,CAAC,CAAC;QACN,CAAC,CAAC;AAClB;AACA;AACA;AACA;AACA;QAAgB,IAAID,YAAY,CAACN,MAAM,GAAG,CAAC,EAAE;UACzBR,SAAS,GAAG1C,IAAI,CAAC+C,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAAC2B,KAAK,KAAKY,YAAY,CAAC,CAAC,CAAC,CAAC;UACrEd,SAAS,CAAC1B,OAAO,CAACyC,EAAE,IAAI;YACpBd,MAAM,CAACzB,IAAI,CAACuC,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QAAE,IAAId,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;UACrBP,MAAM,CAAC3B,OAAO,CAACyC,EAAE,IAAI;YACjBX,MAAM,CAAC5B,IAAI,CAACE,UAAU,CAACqC,EAAE,CAACC,KAAK,CAAC,CAAC;UACrC,CAAC,CAAC;UACFZ,MAAM,CAACtC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;YACxB,IAAID,CAAC,GAAGC,CAAC,EAAE;cACP,OAAO,CAAC,CAAC;YACb;YACA,oBAAO1B,OAAA,aAAWyB,CAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAChC,CAAC,CAAC;UACF,IAAI,CAACzB,QAAQ,CAAC;YACVW,QAAQ,EAAE0C;UACd,CAAC,CAAC;QAEN,CAAC,MACI;UACD,IAAI,CAACrD,QAAQ,CAAC;YACVW,QAAQ,EAAE;UACd,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,CAACX,QAAQ,CAAC;UACVW,QAAQ,EAAE,IAAI,CAAC6B,KAAK,CAAC/B;QACzB,CAAC,CAAC;MAEN;IACJ,CAAC;IACD,IAAI,CAAC4D,OAAO,GAAG,CACX;MAAExB,IAAI,EAAE/D,QAAQ,CAACgE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAC,EACrC;MAAEF,IAAI,EAAE/D,QAAQ,CAACwF,QAAQ;MAAEvB,IAAI,EAAE;IAAI,CAAC,EACtC;MAAEF,IAAI,EAAE/D,QAAQ,CAACyF,KAAK;MAAExB,IAAI,EAAE;IAAI,CAAC,EACnC;MAAEF,IAAI,EAAE/D,QAAQ,CAAC0F,MAAM;MAAEzB,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEF,IAAI,EAAE/D,QAAQ,CAAC2F,MAAM;MAAE1B,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEF,IAAI,EAAE/D,QAAQ,CAAC4F,MAAM;MAAE3B,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEF,IAAI,EAAE/D,QAAQ,CAAC6F,MAAM;MAAE5B,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEF,IAAI,EAAE/D,QAAQ,CAAC8F,MAAM;MAAE7B,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEF,IAAI,EAAE/D,QAAQ,CAAC+F,SAAS;MAAE9B,IAAI,EAAE;IAAI,CAAC,EACvC;MAAEF,IAAI,EAAE/D,QAAQ,CAACgG,OAAO;MAAE/B,IAAI,EAAE;IAAK,CAAC,EACtC;MAAEF,IAAI,EAAE/D,QAAQ,CAACiG,QAAQ;MAAEhC,IAAI,EAAE;IAAK,CAAC,EACvC;MAAEF,IAAI,EAAE/D,QAAQ,CAACkG,QAAQ;MAAEjC,IAAI,EAAE;IAAK,CAAC,CAC1C;IACD,IAAI,CAACkC,QAAQ,GAAG,CACZ;MAAEpC,IAAI,EAAE/D,QAAQ,CAACoG,QAAQ;MAAEnC,IAAI,EAAE;IAAW,CAAC,EAC7C;MAAEF,IAAI,EAAE/D,QAAQ,CAACqG,OAAO;MAAEpC,IAAI,EAAE;IAAU,CAAC,CAC9C;IACD,IAAI,CAACqC,KAAK,GAAG,CAAC;MACVhD,KAAK,EAAEtD,QAAQ,CAACuG,SAAS;MACzBC,IAAI,EAAE,kBAAkB;MACxBF,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACG,MAAM,GAAG,CAAC;MACXnD,KAAK,EAAE,OAAO;MACdkD,IAAI,EAAE,kBAAkB;MACxBF,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACI,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAAC5F,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC4F,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACnD,UAAU,GAAG,IAAI,CAACA,UAAU,CAACmD,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACH,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACI,UAAU,GAAG,IAAI,CAACA,UAAU,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACK,WAAW,GAAG,IAAI,CAACA,WAAW,CAACL,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMM,iBAAiBA,CAAA,EAAG;IACtB,MAAMnH,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC0B,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIyF,KAAK,IAAIzF,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAAC8E,SAAS,CAAC5D,IAAI,CAAC;UAChBiB,IAAI,EAAEoD,KAAK,CAACC,aAAa;UACzBhG,KAAK,EAAE+F,KAAK,CAACE;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDvF,KAAK,CAAEb,CAAC,IAAK;MACVc,OAAO,CAACC,GAAG,CAACf,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAIqG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACnG,MAAM,CAACC,cAAc,CAACmG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI,CAACpG,QAAQ,CAAC;QAAEC,iBAAiB,EAAEmG,WAAW;QAAE3D,SAAS,EAAE;MAAM,CAAC,CAAC;MACnE,MAAM5D,UAAU,CAAC,KAAK,iDAAAyB,MAAA,CAAiD8F,WAAW,CAAE,CAAC,CAChF7F,IAAI,CAACC,GAAG,IAAI;QACT,IAAI,CAACR,QAAQ,CAAC;UACVS,OAAO,EAAED,GAAG,CAACE,IAAI;UACjBC,QAAQ,EAAEH,GAAG,CAACE;QAClB,CAAC,CAAC;MACN,CAAC,CAAC,CAACE,KAAK,CAAEb,CAAC,IAAK;QACZc,OAAO,CAACC,GAAG,CAACf,CAAC,CAAC;MAClB,CAAC,CAAC;MACN,IAAIgB,IAAI,GAAG,EAAE;MACb,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,KAAK,GAAG,EAAE;MACd,MAAMpC,UAAU,CAAC,KAAK,mCAAAyB,MAAA,CAAmC8F,WAAW,CAAE,CAAC,CAClE7F,IAAI,CAACC,GAAG,IAAI;QACTA,GAAG,CAACE,IAAI,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACpB,IAAID,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAE;YACjB,OAAO,CAAC,CAAC;UACb;UACA,oBAAQ3B,OAAA,aAAWyB,CAAC,CAACE,IAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QACtC,CAAC,CAAC;QACFjB,GAAG,CAACE,IAAI,CAACgB,OAAO,CAAEC,OAAO,IAAK;UAC1BZ,IAAI,CAACa,IAAI,CAACD,OAAO,CAACE,IAAI,GAAG,GAAG,GAAGF,OAAO,CAACN,IAAI,CAAC;UAC5CL,IAAI,CAACY,IAAI,CAACE,UAAU,CAACH,OAAO,CAACI,UAAU,CAAC,CAAC;UACzCd,KAAK,CAACW,IAAI,CAACE,UAAU,CAACH,OAAO,CAACK,mBAAmB,CAAC,CAAC;UACnD;QACJ,CAAC,CAAC;QACF,IAAI,CAAChC,QAAQ,CAAC;UACViC,SAAS,EAAE;YACPC,MAAM,EAAEnB,IAAI;YACZoB,QAAQ,EAAE,CACN;cACIC,KAAK,EAAE,gBAAgB;cACvBC,eAAe,EAAE,SAAS;cAC1B3B,IAAI,EAAEM;YACV,CAAC;UAET,CAAC;UACDsB,UAAU,EAAE;YACRJ,MAAM,EAAEnB,IAAI;YACZoB,QAAQ,EAAE,CACN;cACIC,KAAK,EAAE,mBAAmB;cAC1BC,eAAe,EAAE,SAAS;cAC1B3B,IAAI,EAAEO;YACV,CAAC;UAET;QACJ,CAAC,CAAC;MACN,CAAC,CAAC,CAACL,KAAK,CAAEb,CAAC,IAAK;QACZc,OAAO,CAACC,GAAG,CAACf,CAAC,CAAC;MAClB,CAAC,CAAC;MACN,IAAI,CAACwC,UAAU,CAAC,CAAC;IACrB,CAAC,MAAM;MACH,IAAI,CAACvC,QAAQ,CAAC;QAAE0C,YAAY,EAAE,IAAI;QAAED,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;EACJ;EACA;EACAF,UAAUA,CAAA,EAAG;IACT,IAAIiE,MAAM,GAAG,EAAE;IACf,IAAIC,cAAc,GAAG,EAAE;IACvB,IAAInD,KAAK,GAAG,EAAE;IACd,IAAIC,KAAK,GAAG,EAAE;IACd,IAAImD,GAAG,GAAG,EAAE;IACZ,IAAI,IAAI,CAAClE,KAAK,CAAC7B,QAAQ,CAACiD,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,CAACpB,KAAK,CAAC7B,QAAQ,CAACe,OAAO,CAACC,OAAO,IAAI;QACnC6E,MAAM,CAAC5E,IAAI,CAACD,OAAO,CAAC6E,MAAM,CAAC;QAC3BC,cAAc,CAAC7E,IAAI,CAACD,OAAO,CAACwB,SAAS,CAAC;QACtCG,KAAK,CAAC1B,IAAI,CAACD,OAAO,CAAC2B,KAAK,CAAC;QACzBoD,GAAG,CAAC9E,IAAI,CAACD,OAAO,CAACwB,SAAS,GAAG,GAAG,GAAGxB,OAAO,CAAC4B,KAAK,CAAC;QACjDA,KAAK,CAAC3B,IAAI,CAACD,OAAO,CAAC4B,KAAK,CAAC;MAC7B,CAAC,CAAC;MACFiD,MAAM,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACH,MAAM,CAAC,CAAC,CAACtF,IAAI,CAAC,CAAC;MACpCuF,cAAc,GAAG,CAAC,GAAG,IAAIE,GAAG,CAACF,cAAc,CAAC,CAAC,CAACvF,IAAI,CAAC,CAAC;MACpDqC,KAAK,GAAG,CAAC,GAAG,IAAIoD,GAAG,CAACpD,KAAK,CAAC,CAAC,CAACrC,IAAI,CAAC,CAAC;MAClCwF,GAAG,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACD,GAAG,CAAC,CAAC;MACvBpD,KAAK,GAAG,CAAC,GAAG,IAAIqD,GAAG,CAACrD,KAAK,CAAC,CAAC,CAACpC,IAAI,CAAC,CAAC;MAClC,IAAI0F,WAAW,GAAG,EAAE;MACpBtD,KAAK,CAAC5B,OAAO,CAACC,OAAO,IAAI;QACrB,IAAIA,OAAO,KAAK,EAAE,EAAE;UAChB,IAAI,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAACH,KAAK,CAACxD,IAAI,CAAC;YAAEQ,KAAK,EAAET,OAAO;YAAEkF,OAAO,EAAG9G,CAAC,IAAK;cAAE,IAAI,CAACkD,UAAU,CAAClD,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACzF,CAAC,MAAM;UACH6G,WAAW,CAAChF,IAAI,CAAC;YAAEQ,KAAK,EAAE,OAAO;YAAElC,KAAK,EAAE,OAAO;YAAE2G,OAAO,EAAG9G,CAAC,IAAK;cAAE,IAAI,CAACkD,UAAU,CAAClD,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QAChG;MACJ,CAAC,CAAC;MACF6G,WAAW,CAAClF,OAAO,CAAC0D,KAAK,IAAI;QACzB,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAACH,KAAK,CAACxD,IAAI,CAACwD,KAAK,CAAC;MACpC,CAAC,CAAC;MACFwB,WAAW,GAAG,EAAE;MAChBH,cAAc,CAAC/E,OAAO,CAACC,OAAO,IAAI;QAC9B,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,EAAE,EAAE;UACpC,IAAI,CAACyD,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACxD,IAAI,CAAC;YAAEQ,KAAK,EAAET,OAAO;YAAEkF,OAAO,EAAG9G,CAAC,IAAK;cAAE,IAAI,CAACkD,UAAU,CAAClD,CAAC,CAAC;YAAC,CAAC;YAAEqF,KAAK,EAAE;UAAG,CAAC,CAAC;QACnG,CAAC,MAAM;UACHwB,WAAW,CAAChF,IAAI,CAAC;YAAEQ,KAAK,EAAE,OAAO;YAAElC,KAAK,EAAE,WAAW;YAAE2G,OAAO,EAAG9G,CAAC,IAAK;cAAE,IAAI,CAACkD,UAAU,CAAClD,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACpG;MACJ,CAAC,CAAC;MACF6G,WAAW,CAAClF,OAAO,CAAC0D,KAAK,IAAI;QACzB,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACxD,IAAI,CAACwD,KAAK,CAAC;MACnC,CAAC,CAAC;MACF7B,KAAK,CAAC7B,OAAO,CAAC0D,KAAK,IAAI;QACnB,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC1D,OAAO,CAACwB,IAAI,IAAI;UAChC,IAAIA,IAAI,CAACd,KAAK,KAAK,IAAI,EAAE;YACrB,IAAIsE,GAAG,CAACI,QAAQ,CAAC5D,IAAI,CAACd,KAAK,CAAC9B,MAAM,CAAC,GAAG,GAAG8E,KAAK,CAAC,CAAC,EAAE;cAC9C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;gBAChClC,IAAI,CAACkC,KAAK,CAACxD,IAAI,CAAC;kBAAEQ,KAAK,EAAEgD,KAAK;kBAAEyB,OAAO,EAAG9G,CAAC,IAAK;oBAAE,IAAI,CAACkD,UAAU,CAAClD,CAAC,CAAC;kBAAC;gBAAG,CAAC,CAAC;cAC9E,CAAC,MAAM;gBACHmD,IAAI,CAACkC,KAAK,CAACxD,IAAI,CAAC;kBAAEQ,KAAK,EAAE,OAAO;kBAAElC,KAAK,EAAE,OAAO;kBAAE2D,GAAG,EAAEX,IAAI,CAACd,KAAK;kBAAEyE,OAAO,EAAG9G,CAAC,IAAK;oBAAE,IAAI,CAACkD,UAAU,CAAClD,CAAC,CAAC;kBAAC;gBAAE,CAAC,CAAC;cAChH;YACJ;UACJ;QAEJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF;AACZ;AACA;AACA;AACA;AACA;IACQ,CAAC,MAAM;MACH,IAAI,CAACqF,KAAK,GAAG,CAAC;QACVhD,KAAK,EAAEtD,QAAQ,CAACuG,SAAS;QACzBC,IAAI,EAAE,kBAAkB;QACxBF,KAAK,EAAE;MACX,CAAC,CAAC;MACF,IAAI,CAACG,MAAM,GAAG,CAAC;QACXnD,KAAK,EAAE,OAAO;QACdkD,IAAI,EAAE,kBAAkB;QACxBF,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ;EAyDAK,aAAaA,CAAA,EAAG;IACZ,IAAIsB,YAAY,GAAG;MACfC,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJjF,MAAM,EAAE;YACJkF,KAAK,EAAE;UACX;QACJ;MACJ,CAAC;MACDC,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAE;UACX,CAAC;UACDI,IAAI,EAAE;YACFJ,KAAK,EAAE;UACX;QACJ,CAAC;QACDK,CAAC,EAAE;UACCF,KAAK,EAAE;YACHH,KAAK,EAAE;UACX,CAAC;UACDI,IAAI,EAAE;YACFJ,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC;IACD,OAAO;MACHL;IACJ,CAAC;EACL;EACA,MAAMW,UAAUA,CAAC3H,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MACV4C,MAAM,EAAE7C,CAAC,CAACG;IACd,CAAC,CAAC;IACF,MAAMrB,UAAU,CAAC,KAAK,qCAAAyB,MAAA,CAAqCP,CAAC,CAACG,KAAK,CAAC6C,IAAI,iBAAAzC,MAAA,CAAc,IAAI,CAACkC,KAAK,CAACvC,iBAAiB,CAAE,CAAC,CAC/GM,IAAI,CAACC,GAAG,IAAI;MACT,IAAI,CAACR,QAAQ,CAAC;QACVS,OAAO,EAAED,GAAG,CAACE,IAAI;QACjBC,QAAQ,EAAEH,GAAG,CAACE;MAClB,CAAC,CAAC;MACF,IAAI,CAAC6B,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC3B,KAAK,CAAEb,CAAC,IAAK;MACZc,OAAO,CAACC,GAAG,CAACf,CAAC,CAAC;IAClB,CAAC,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA4F,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC3F,QAAQ,CAAC;MACVW,QAAQ,EAAE,IAAI,CAAC6B,KAAK,CAAC/B,OAAO;MAC5BuC,MAAM,EAAE;MACR;IACJ,CAAC,CAAC;EACN;EACA;EACA4C,SAASA,CAAA,EAAG;IACR,IAAI,CAAC5F,QAAQ,CAAC;MACVW,QAAQ,EAAE,IAAI,CAAC6B,KAAK,CAAC/B,OAAO;MAC5BuC,MAAM,EAAE;MACR;IACJ,CAAC,CAAC;EACN;EACA6C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACrD,KAAK,CAACvC,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACV0C,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACiF,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAlC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC9F,QAAQ,CAAC;MACV2C,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAoD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/F,QAAQ,CAAC;MACV2C,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAsF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAElB;IAAa,CAAC,GAAG,IAAI,CAACtB,aAAa,CAAC,CAAC;IAC7C,MAAMyC,kBAAkB,gBACpBxI,OAAA,CAACjB,KAAK,CAAC0J,QAAQ;MAAAC,QAAA,eACX1I,OAAA;QAAK2I,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1D1I,OAAA,CAACf,MAAM;UAAC0J,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACzC,iBAAkB;UAAAuC,QAAA,GAAE,GAAC,EAACtJ,QAAQ,CAACyJ,MAAM,EAAC,GAAC;QAAA;UAAAjH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAM+G,MAAM,GAAG,CACX;MAAEC,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAE5J,QAAQ,CAAC6J,OAAO;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EACtF;MAAEJ,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAE5J,QAAQ,CAACgK,IAAI;MAAEF,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EACjF;MAAEJ,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE5J,QAAQ,CAACiK,cAAc;MAAEH,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAC9F;MAAEJ,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAE5J,QAAQ,CAACkK,gBAAgB;MAAEJ,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EACpG;MAAEJ,KAAK,EAAE,cAAc;MAAEC,MAAM,EAAE5J,QAAQ,CAACmK,mBAAmB;MAAEL,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EACjG;MAAEJ,KAAK,EAAE,cAAc;MAAEC,MAAM,EAAE5J,QAAQ,CAACoK,QAAQ;MAAEN,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EACtF;MAAEJ,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE5J,QAAQ,CAACqK,cAAc;MAAEP,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAC1F;MAAEJ,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MAAEE,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CACxE;IACD;IACA,IAAIO,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAAC5G,KAAK,CAACQ,MAAM,KAAK,EAAE,EAAE;MAC1BoG,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACI1J,OAAA;MAAK2I,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAEjB1I,OAAA,CAACJ,KAAK;QAAC+J,GAAG,EAAGlF,EAAE,IAAK,IAAI,CAACwD,KAAK,GAAGxD;MAAG;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvC/B,OAAA,CAACF,GAAG;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP/B,OAAA;QAAK2I,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnC1I,OAAA;UAAA0I,QAAA,EAAKtJ,QAAQ,CAACwK;QAAkB;UAAAhI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACN/B,OAAA,CAACR,OAAO;QAACmJ,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC/B1I,OAAA,CAACP,QAAQ;UAACoK,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAM,CAAE;UAACd,MAAM,EAAE5J,QAAQ,CAACwK,kBAAmB;UAACG,QAAQ,EAAC,qBAAqB;UAAArB,QAAA,GACnG,IAAI,CAAC5F,KAAK,CAACvC,iBAAiB,KAAK,IAAI,iBAClCP,OAAA;YAAK2I,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACtC1I,OAAA;cAAI2I,SAAS,EAAC,4DAA4D;cAAAD,QAAA,gBACtE1I,OAAA;gBAAI2I,SAAS,EAAC,uDAAuD;gBAAAD,QAAA,eACjE1I,OAAA;kBAAK2I,SAAS,EAAC,kDAAkD;kBAAAD,QAAA,gBAC7D1I,OAAA;oBAAI2I,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,gBAAC1I,OAAA;sBAAG2I,SAAS,EAAC,iBAAiB;sBAACkB,KAAK,EAAE;wBAAE,UAAU,EAAE;sBAAO;oBAAE;sBAAAjI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAAC3C,QAAQ,CAAC4K,SAAS,EAAC,GAAC;kBAAA;oBAAApI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5H/B,OAAA,CAACX,QAAQ;oBAACsJ,SAAS,EAAC,QAAQ;oBAACnI,KAAK,EAAE,IAAI,CAACsC,KAAK,CAACvC,iBAAkB;oBAACoE,OAAO,EAAE,IAAI,CAACmB,SAAU;oBAACmE,QAAQ,EAAE,IAAI,CAAC7J,iBAAkB;oBAAC8J,WAAW,EAAC,MAAM;oBAACC,WAAW,EAAC,qBAAqB;oBAACpG,MAAM;oBAACqG,QAAQ,EAAC;kBAAM;oBAAAxI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1M;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACL/B,OAAA;gBAAI2I,SAAS,EAAC,uDAAuD;gBAAAD,QAAA,eACjE1I,OAAA;kBAAK2I,SAAS,EAAC,kDAAkD;kBAAAD,QAAA,gBAC7D1I,OAAA;oBAAI2I,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,gBAAC1I,OAAA;sBAAG2I,SAAS,EAAC,qBAAqB;sBAACkB,KAAK,EAAE;wBAAE,UAAU,EAAE;sBAAO;oBAAE;sBAAAjI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAAC3C,QAAQ,CAACiL,MAAM;kBAAA;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5H/B,OAAA,CAACX,QAAQ;oBAACmB,KAAK,EAAE,IAAI,CAACsC,KAAK,CAACI,MAAO;oBAACyB,OAAO,EAAE,IAAI,CAACA,OAAQ;oBAACsF,QAAQ,EAAG5J,CAAC,IAAK,IAAI,CAAC2H,UAAU,CAAC3H,CAAC,CAAE;oBAAC6J,WAAW,EAAC,MAAM;oBAACC,WAAW,EAAC;kBAAgB;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEV/B,OAAA;YAAK2I,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAC9C1I,OAAA,CAACH,eAAe;cACZW,KAAK,EAAE,IAAI,CAACsC,KAAK,CAAC7B,QAAS;cAC3B6H,MAAM,EAAEA,MAAO;cACfwB,eAAe,EAAE,IAAK;cACtBC,iBAAiB,EAAE,IAAI,CAACnE,UAAW;cACnCoE,gBAAgB,eAAExK,OAAA;gBAAU2I,SAAS,EAAC,MAAM;gBAACxF,IAAI,EAAC;cAAgB;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAE;cAC/E0I,OAAO,EAAC,IAAI;cACZC,gBAAgB,EAAC,QAAQ;cACzBC,UAAU,EAAE,IAAK;cACjBC,SAAS;cACTC,IAAI,EAAE,EAAG;cACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;YAAE;cAAAlJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACX/B,OAAA,CAACP,QAAQ;UAACuJ,MAAM,EAAE5J,QAAQ,CAAC2L,QAAS;UAAChB,QAAQ,EAAC,sBAAsB;UAAArB,QAAA,eAChE1I,OAAA;YAAK2I,SAAS,EAAC,0BAA0B;YAAAD,QAAA,gBACrC1I,OAAA;cAAK2I,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC5B1I,OAAA,CAACT,KAAK;gBAACyL,IAAI,EAAC,MAAM;gBAAChK,IAAI,EAAE,IAAI,CAAC8B,KAAK,CAACP,SAAU;gBAACoC,OAAO,EAAE0C;cAAa;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACN/B,OAAA;cAAK2I,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC5B1I,OAAA,CAACT,KAAK;gBAACyL,IAAI,EAAC,MAAM;gBAAChK,IAAI,EAAE,IAAI,CAAC8B,KAAK,CAACF,UAAW;gBAAC+B,OAAO,EAAE0C;cAAa;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACV/B,OAAA,CAACN,MAAM;QAACuL,OAAO,EAAE,IAAI,CAACnI,KAAK,CAACE,YAAa;QAACgG,MAAM,EAAE5J,QAAQ,CAAC8L,iBAAkB;QAACC,KAAK;QAACxC,SAAS,EAAC,kBAAkB;QAACyC,MAAM,EAAE,IAAI,CAACjF,iBAAkB;QAACkF,MAAM,EAAE7C,kBAAmB;QAAAE,QAAA,GACvK,IAAI,CAAC5F,KAAK,CAACC,SAAS,iBACjB/C,OAAA,CAACd,UAAU;UAACoM,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAA5J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhG/B,OAAA;UAAK2I,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3D1I,OAAA;YAAI2I,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAC1I,OAAA;cAAG2I,SAAS,EAAC,iBAAiB;cAACkB,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC3C,QAAQ,CAAC4K,SAAS;UAAA;YAAApI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH/B,OAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/B,OAAA,CAACX,QAAQ;YAACsJ,SAAS,EAAC,QAAQ;YAACnI,KAAK,EAAE,IAAI,CAACsC,KAAK,CAACvC,iBAAkB;YAACoE,OAAO,EAAE,IAAI,CAACmB,SAAU;YAACmE,QAAQ,EAAE,IAAI,CAAC7J,iBAAkB;YAAC8J,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACpG,MAAM;YAACqG,QAAQ,EAAC;UAAM;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT/B,OAAA,CAACL,OAAO;QAACsL,OAAO,EAAE,IAAI,CAACnI,KAAK,CAACG,aAAc;QAACwI,QAAQ,EAAC,MAAM;QAACL,MAAM,EAAE,IAAI,CAAC/E,WAAY;QAAAqC,QAAA,gBACjF1I,OAAA;UAAKyG,EAAE,EAAC,cAAc;UAACkC,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrK1I,OAAA;YAAG2I,SAAS,EAAC;UAA0B;YAAA/G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C/B,OAAA;YAAI2I,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAC1I,OAAA;cAAG2I,SAAS,EAAC,mBAAmB;cAACkB,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC3C,QAAQ,CAACsM,MAAM;UAAA;YAAA9J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G/B,OAAA,CAACf,MAAM;YAACwH,EAAE,EAAC,iBAAiB;YAACkC,SAAS,EAAEe,WAAY;YAACd,OAAO,EAAE,IAAI,CAAC3C,KAAM;YAAAyC,QAAA,gBAAC1I,OAAA;cAAG2I,SAAS,EAAC;YAAkB;cAAA/G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA/B,OAAA;cAAA0I,QAAA,EAAM;YAAK;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN/B,OAAA;UAAKyG,EAAE,EAAC,kBAAkB;UAACkC,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/D1I,OAAA;YAAI2I,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAC1I,OAAA;cAAG2I,SAAS,EAAC,mBAAmB;cAACkB,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC3C,QAAQ,CAACsM,MAAM;UAAA;YAAA9J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G/B,OAAA,CAACf,MAAM;YAACwH,EAAE,EAAC,kBAAkB;YAACkC,SAAS,EAAEe,WAAY;YAACd,OAAO,EAAE,IAAI,CAAC1C,SAAU;YAAAwC,QAAA,gBAAC1I,OAAA;cAAG2I,SAAS,EAAC;YAAkB;cAAA/G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA/B,OAAA;cAAA0I,QAAA,EAAM;YAAK;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN/B,OAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/B,OAAA,CAACV,SAAS;UAACqJ,SAAS,EAAC,qBAAqB;UAACgD,KAAK,EAAE,IAAI,CAACjG;QAAM;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChE/B,OAAA,CAACV,SAAS;UAACqJ,SAAS,EAAC,qBAAqB;UAACgD,KAAK,EAAE,IAAI,CAAC9F;QAAO;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEf;AACJ;AAEA,eAAe9B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}