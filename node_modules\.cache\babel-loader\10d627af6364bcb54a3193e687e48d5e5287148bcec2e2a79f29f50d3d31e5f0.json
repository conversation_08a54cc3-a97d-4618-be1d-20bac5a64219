{"ast": null, "code": "/* eslint-disable no-template-curly-in-string */\nimport Pagination from \"rc-pagination/es/locale/en_US\";\nimport DatePicker from '../date-picker/locale/en_US';\nimport TimePicker from '../time-picker/locale/en_US';\nimport Calendar from '../calendar/locale/en_US';\nvar typeTemplate = '${label} is not a valid ${type}';\nvar localeValues = {\n  locale: 'en',\n  Pagination: Pagination,\n  DatePicker: DatePicker,\n  TimePicker: TimePicker,\n  Calendar: Calendar,\n  global: {\n    placeholder: 'Please select'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    filterCheckall: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectNone: 'Clear all data',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No Data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand'\n  },\n  PageHeader: {\n    back: 'Back'\n  },\n  Form: {\n    optional: '(optional)',\n    defaultValidateMessages: {\n      \"default\": 'Field validation error for ${label}',\n      required: 'Please enter ${label}',\n      \"enum\": '${label} must be one of [${enum}]',\n      whitespace: '${label} cannot be a blank character',\n      date: {\n        format: '${label} date format is invalid',\n        parse: '${label} cannot be converted to a date',\n        invalid: '${label} is an invalid date'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        \"boolean\": typeTemplate,\n        integer: typeTemplate,\n        \"float\": typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label} must be ${len} characters',\n        min: '${label} must be at least ${min} characters',\n        max: '${label} must be up to ${max} characters',\n        range: '${label} must be between ${min}-${max} characters'\n      },\n      number: {\n        len: '${label} must be equal to ${len}',\n        min: '${label} must be minimum ${min}',\n        max: '${label} must be maximum ${max}',\n        range: '${label} must be between ${min}-${max}'\n      },\n      array: {\n        len: 'Must be ${len} ${label}',\n        min: 'At least ${min} ${label}',\n        max: 'At most ${max} ${label}',\n        range: 'The amount of ${label} must be between ${min}-${max}'\n      },\n      pattern: {\n        mismatch: '${label} does not match the pattern ${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: 'Preview'\n  }\n};\nexport default localeValues;", "map": {"version": 3, "names": ["Pagination", "DatePicker", "TimePicker", "Calendar", "typeTemplate", "localeValues", "locale", "global", "placeholder", "Table", "filterTitle", "filterConfirm", "filterReset", "filterEmptyText", "filterCheckall", "filterSearchPlaceholder", "emptyText", "selectAll", "selectInvert", "selectNone", "selectionAll", "sortTitle", "expand", "collapse", "triggerDesc", "triggerAsc", "cancelSort", "Modal", "okText", "cancelText", "justOkText", "Popconfirm", "Transfer", "titles", "searchPlaceholder", "itemUnit", "itemsUnit", "remove", "selectCurrent", "removeCurrent", "removeAll", "Upload", "uploading", "removeFile", "uploadError", "previewFile", "downloadFile", "Empty", "description", "Icon", "icon", "Text", "edit", "copy", "copied", "<PERSON><PERSON><PERSON><PERSON>", "back", "Form", "optional", "defaultValidateMessages", "required", "whitespace", "date", "format", "parse", "invalid", "types", "string", "method", "array", "object", "number", "integer", "regexp", "email", "url", "hex", "len", "min", "max", "range", "pattern", "mismatch", "Image", "preview"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/locale/default.js"], "sourcesContent": ["/* eslint-disable no-template-curly-in-string */\nimport Pagination from \"rc-pagination/es/locale/en_US\";\nimport DatePicker from '../date-picker/locale/en_US';\nimport TimePicker from '../time-picker/locale/en_US';\nimport Calendar from '../calendar/locale/en_US';\nvar typeTemplate = '${label} is not a valid ${type}';\nvar localeValues = {\n  locale: 'en',\n  Pagination: Pagination,\n  DatePicker: DatePicker,\n  TimePicker: TimePicker,\n  Calendar: Calendar,\n  global: {\n    placeholder: 'Please select'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    filterCheckall: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectNone: 'Clear all data',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No Data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand'\n  },\n  PageHeader: {\n    back: 'Back'\n  },\n  Form: {\n    optional: '(optional)',\n    defaultValidateMessages: {\n      \"default\": 'Field validation error for ${label}',\n      required: 'Please enter ${label}',\n      \"enum\": '${label} must be one of [${enum}]',\n      whitespace: '${label} cannot be a blank character',\n      date: {\n        format: '${label} date format is invalid',\n        parse: '${label} cannot be converted to a date',\n        invalid: '${label} is an invalid date'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        \"boolean\": typeTemplate,\n        integer: typeTemplate,\n        \"float\": typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label} must be ${len} characters',\n        min: '${label} must be at least ${min} characters',\n        max: '${label} must be up to ${max} characters',\n        range: '${label} must be between ${min}-${max} characters'\n      },\n      number: {\n        len: '${label} must be equal to ${len}',\n        min: '${label} must be minimum ${min}',\n        max: '${label} must be maximum ${max}',\n        range: '${label} must be between ${min}-${max}'\n      },\n      array: {\n        len: 'Must be ${len} ${label}',\n        min: 'At least ${min} ${label}',\n        max: 'At most ${max} ${label}',\n        range: 'The amount of ${label} must be between ${min}-${max}'\n      },\n      pattern: {\n        mismatch: '${label} does not match the pattern ${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: 'Preview'\n  }\n};\nexport default localeValues;"], "mappings": "AAAA;AACA,OAAOA,UAAU,MAAM,+BAA+B;AACtD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,IAAIC,YAAY,GAAG,iCAAiC;AACpD,IAAIC,YAAY,GAAG;EACjBC,MAAM,EAAE,IAAI;EACZN,UAAU,EAAEA,UAAU;EACtBC,UAAU,EAAEA,UAAU;EACtBC,UAAU,EAAEA,UAAU;EACtBC,QAAQ,EAAEA,QAAQ;EAClBI,MAAM,EAAE;IACNC,WAAW,EAAE;EACf,CAAC;EACDC,KAAK,EAAE;IACLC,WAAW,EAAE,aAAa;IAC1BC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,OAAO;IACpBC,eAAe,EAAE,YAAY;IAC7BC,cAAc,EAAE,kBAAkB;IAClCC,uBAAuB,EAAE,mBAAmB;IAC5CC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,qBAAqB;IAChCC,YAAY,EAAE,qBAAqB;IACnCC,UAAU,EAAE,gBAAgB;IAC5BC,YAAY,EAAE,iBAAiB;IAC/BC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE,0BAA0B;IACvCC,UAAU,EAAE,yBAAyB;IACrCC,UAAU,EAAE;EACd,CAAC;EACDC,KAAK,EAAE;IACLC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVH,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE;EACd,CAAC;EACDG,QAAQ,EAAE;IACRC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAChBC,iBAAiB,EAAE,aAAa;IAChCC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,qBAAqB;IACpCC,aAAa,EAAE,qBAAqB;IACpCtB,SAAS,EAAE,iBAAiB;IAC5BuB,SAAS,EAAE,iBAAiB;IAC5BtB,YAAY,EAAE;EAChB,CAAC;EACDuB,MAAM,EAAE;IACNC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE;IACLC,WAAW,EAAE;EACf,CAAC;EACDC,IAAI,EAAE;IACJC,IAAI,EAAE;EACR,CAAC;EACDC,IAAI,EAAE;IACJC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBhC,MAAM,EAAE;EACV,CAAC;EACDiC,UAAU,EAAE;IACVC,IAAI,EAAE;EACR,CAAC;EACDC,IAAI,EAAE;IACJC,QAAQ,EAAE,YAAY;IACtBC,uBAAuB,EAAE;MACvB,SAAS,EAAE,qCAAqC;MAChDC,QAAQ,EAAE,uBAAuB;MACjC,MAAM,EAAE,mCAAmC;MAC3CC,UAAU,EAAE,sCAAsC;MAClDC,IAAI,EAAE;QACJC,MAAM,EAAE,iCAAiC;QACzCC,KAAK,EAAE,wCAAwC;QAC/CC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE/D,YAAY;QACpBgE,MAAM,EAAEhE,YAAY;QACpBiE,KAAK,EAAEjE,YAAY;QACnBkE,MAAM,EAAElE,YAAY;QACpBmE,MAAM,EAAEnE,YAAY;QACpB0D,IAAI,EAAE1D,YAAY;QAClB,SAAS,EAAEA,YAAY;QACvBoE,OAAO,EAAEpE,YAAY;QACrB,OAAO,EAAEA,YAAY;QACrBqE,MAAM,EAAErE,YAAY;QACpBsE,KAAK,EAAEtE,YAAY;QACnBuE,GAAG,EAAEvE,YAAY;QACjBwE,GAAG,EAAExE;MACP,CAAC;MACD+D,MAAM,EAAE;QACNU,GAAG,EAAE,oCAAoC;QACzCC,GAAG,EAAE,6CAA6C;QAClDC,GAAG,EAAE,0CAA0C;QAC/CC,KAAK,EAAE;MACT,CAAC;MACDT,MAAM,EAAE;QACNM,GAAG,EAAE,kCAAkC;QACvCC,GAAG,EAAE,iCAAiC;QACtCC,GAAG,EAAE,iCAAiC;QACtCC,KAAK,EAAE;MACT,CAAC;MACDX,KAAK,EAAE;QACLQ,GAAG,EAAE,yBAAyB;QAC9BC,GAAG,EAAE,0BAA0B;QAC/BC,GAAG,EAAE,yBAAyB;QAC9BC,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EAAE;QACPC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDC,KAAK,EAAE;IACLC,OAAO,EAAE;EACX;AACF,CAAC;AACD,eAAe/E,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}