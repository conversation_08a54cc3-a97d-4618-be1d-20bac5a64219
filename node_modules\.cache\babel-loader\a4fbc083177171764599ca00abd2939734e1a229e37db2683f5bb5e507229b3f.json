{"ast": null, "code": "import { tinycolor } from './index';\nexport * from './index';\nexport * from './css-color-names';\nexport * from './readability';\nexport * from './to-ms-filter';\nexport * from './from-ratio';\nexport * from './format-input';\nexport * from './random';\nexport * from './interfaces';\nexport * from './conversion';\n// kept for backwards compatability with v1\nexport default tinycolor;", "map": {"version": 3, "names": ["tinycolor"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ctrl/tinycolor/dist/module/public_api.js"], "sourcesContent": ["import { tinycolor } from './index';\nexport * from './index';\nexport * from './css-color-names';\nexport * from './readability';\nexport * from './to-ms-filter';\nexport * from './from-ratio';\nexport * from './format-input';\nexport * from './random';\nexport * from './interfaces';\nexport * from './conversion';\n// kept for backwards compatability with v1\nexport default tinycolor;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,SAAS;AACnC,cAAc,SAAS;AACvB,cAAc,mBAAmB;AACjC,cAAc,eAAe;AAC7B,cAAc,gBAAgB;AAC9B,cAAc,cAAc;AAC5B,cAAc,gBAAgB;AAC9B,cAAc,UAAU;AACxB,cAAc,cAAc;AAC5B,cAAc,cAAc;AAC5B;AACA,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}