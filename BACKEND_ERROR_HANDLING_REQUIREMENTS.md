# Requisiti per il Miglioramento della Gestione Errori Backend

## Panoramica del Problema

Il frontend attualmente riceve errori generici (principalmente 501) che non forniscono informazioni sufficienti per guidare l'utente. È necessario migliorare le risposte di errore del backend per fornire feedback specifici e utili.

## Endpoint Coinvolto

**POST /registry/** - Creazione nuove anagrafiche

## Problemi Attuali Identificati

### 1. Violazione Unique Constraint (Partita IVA duplicata)
- **Problema**: Il backend restituisce un errore generico 501
- **Comportamento atteso**: Errore 409 (Conflict) con messaggio specifico

### 2. Errori di Validazione
- **Problema**: Errori di validazione non specifici
- **Comportamento atteso**: Errore 400 (Bad Request) con dettagli sui campi

### 3. Mancanza di Messaggi Strutturati
- **Problema**: Messaggi di errore non standardizzati
- **Comportamento atteso**: Formato JSON consistente

## Specifiche Tecniche Richieste

### 1. Codici di Stato HTTP Corretti

```
200 - Successo
400 - Errori di validazione (dati malformati, campi mancanti)
409 - Conflitto (violazione unique constraint)
422 - Errori di business logic
500 - Errori interni del server
503 - Servizio temporaneamente non disponibile
```

### 2. Formato Risposta di Errore Standardizzato

```json
{
  "error": {
    "code": "DUPLICATE_VAT_NUMBER",
    "message": "La Partita IVA è già registrata nel sistema",
    "details": {
      "field": "pIva",
      "value": "***********",
      "constraint": "unique_vat_number"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### 3. Codici di Errore Specifici

#### Violazioni Unique Constraint
```json
{
  "error": {
    "code": "DUPLICATE_VAT_NUMBER",
    "message": "La Partita IVA è già registrata nel sistema",
    "details": {
      "field": "pIva",
      "value": "***********"
    }
  }
}
```

#### Errori di Validazione
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Dati di input non validi",
    "details": {
      "fields": [
        {
          "field": "email",
          "message": "Formato email non valido",
          "value": "email-non-valida"
        },
        {
          "field": "pIva",
          "message": "La Partita IVA deve contenere 11 cifre",
          "value": "123"
        }
      ]
    }
  }
}
```

#### Campi Obbligatori Mancanti
```json
{
  "error": {
    "code": "MISSING_REQUIRED_FIELDS",
    "message": "Campi obbligatori mancanti",
    "details": {
      "missingFields": ["firstName", "lastName", "pIva"]
    }
  }
}
```

## Implementazione Richiesta

### 1. Validazione Partita IVA
- Verificare formato (11 cifre numeriche)
- Controllare unicità nel database
- Restituire 409 se già esistente

### 2. Validazione Email
- Verificare formato email valido
- Controllare unicità se richiesta
- Restituire 400 per formato non valido

### 3. Validazione Campi Obbligatori
- firstName (obbligatorio)
- lastName (obbligatorio)  
- pIva (obbligatorio)
- Restituire 400 se mancanti

### 4. Gestione Errori Database
- Catturare violazioni unique constraint
- Mappare a codici HTTP appropriati
- Fornire messaggi specifici

## Esempi di Implementazione

### Controller (Node.js/Express esempio)
```javascript
try {
  // Validazione input
  const validationErrors = validateRegistryInput(req.body);
  if (validationErrors.length > 0) {
    return res.status(400).json({
      error: {
        code: "VALIDATION_ERROR",
        message: "Dati di input non validi",
        details: { fields: validationErrors }
      }
    });
  }

  // Creazione anagrafica
  const registry = await createRegistry(req.body);
  res.status(201).json(registry);

} catch (error) {
  if (error.code === 'ER_DUP_ENTRY' || error.constraint === 'unique_vat') {
    return res.status(409).json({
      error: {
        code: "DUPLICATE_VAT_NUMBER",
        message: "La Partita IVA è già registrata nel sistema",
        details: {
          field: "pIva",
          value: req.body.pIva
        }
      }
    });
  }
  
  // Altri errori
  res.status(500).json({
    error: {
      code: "INTERNAL_ERROR",
      message: "Errore interno del server"
    }
  });
}
```

## Test Cases Richiesti

### 1. Test Partita IVA Duplicata
```
POST /registry/
{
  "firstName": "Mario",
  "lastName": "Rossi", 
  "pIva": "***********" // Già esistente
}

Risposta attesa: 409 con messaggio specifico
```

### 2. Test Validazione Email
```
POST /registry/
{
  "firstName": "Mario",
  "lastName": "Rossi",
  "pIva": "12345678902",
  "email": "email-non-valida"
}

Risposta attesa: 400 con dettagli validazione
```

### 3. Test Campi Mancanti
```
POST /registry/
{
  "firstName": "Mario"
  // lastName e pIva mancanti
}

Risposta attesa: 400 con lista campi mancanti
```

## Priorità di Implementazione

1. **Alta**: Gestione corretta violazioni unique constraint (P.IVA duplicata)
2. **Alta**: Codici di stato HTTP corretti (409, 400 invece di 501)
3. **Media**: Formato JSON strutturato per errori
4. **Media**: Validazione lato server completa
5. **Bassa**: Codici di errore specifici per ogni tipo

## Note per l'Implementazione

- Mantenere retrocompatibilità con frontend esistente
- Implementare gradualmente, iniziando dai casi più critici
- Testare accuratamente ogni tipo di errore
- Documentare tutti i nuovi codici di errore
- Considerare internazionalizzazione dei messaggi

## Benefici Attesi

- Migliore esperienza utente con messaggi di errore chiari
- Riduzione del supporto tecnico per errori comuni
- Debugging più semplice per sviluppatori
- Interfaccia più professionale e user-friendly
