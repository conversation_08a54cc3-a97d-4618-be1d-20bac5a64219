{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport * as React from 'react';\nimport React__default from 'react';\nimport { formSubscriptionItems, createForm, version as version$1, fieldSubscriptionItems } from 'final-form';\nvar _excluded$3 = [\"render\", \"children\", \"component\"];\n// shared logic between components that use either render prop,\n// children render function, or component prop\nfunction renderComponent(props, lazyProps, name) {\n  var render = props.render,\n    children = props.children,\n    component = props.component,\n    rest = _objectWithoutPropertiesLoose(props, _excluded$3);\n  if (component) {\n    return /*#__PURE__*/React.createElement(component, Object.assign(lazyProps, rest, {\n      children: children,\n      render: render\n    }));\n  }\n  if (render) {\n    return render(children === undefined ? Object.assign(lazyProps, rest) :\n    // inject children back in\n    Object.assign(lazyProps, rest, {\n      children: children\n    }));\n  }\n  if (typeof children !== \"function\") {\n    throw new Error(\"Must specify either a render prop, a render function as children, or a component prop to \" + name);\n  }\n  return children(Object.assign(lazyProps, rest));\n}\nfunction useWhenValueChanges(value, callback, isEqual) {\n  if (isEqual === void 0) {\n    isEqual = function isEqual(a, b) {\n      return a === b;\n    };\n  }\n  var previous = React__default.useRef(value);\n  React__default.useEffect(function () {\n    if (!isEqual(value, previous.current)) {\n      callback();\n      previous.current = value;\n    }\n  });\n}\n\n/**\n * A simple hook to create a constant value that lives for\n * the lifetime of the component.\n *\n * Plagiarized from https://github.com/Andarist/use-constant\n *\n * Do NOT reuse this code unless you know what you're doing.\n * Use Andarist's hook; it's more fault tolerant to things like\n * falsy values.\n *\n * @param {Function} init - A function to generate the value\n */\n\nfunction useConstant(init) {\n  var ref = React__default.useRef();\n  if (!ref.current) {\n    ref.current = init();\n  }\n  return ref.current;\n}\nvar shallowEqual = function shallowEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== \"object\" || !a || typeof b !== \"object\" || !b) {\n    return false;\n  }\n  var keysA = Object.keys(a);\n  var keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(b);\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n    if (!bHasOwnProperty(key) || a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n};\nvar isSyntheticEvent = function isSyntheticEvent(candidate) {\n  return !!(candidate && typeof candidate.stopPropagation === \"function\");\n};\nvar ReactFinalFormContext = /*#__PURE__*/React.createContext();\nfunction useLatest(value) {\n  var ref = React__default.useRef(value);\n  React__default.useEffect(function () {\n    ref.current = value;\n  });\n  return ref;\n}\nvar version = \"6.5.8\";\nvar addLazyState = function addLazyState(dest, state, keys) {\n  keys.forEach(function (key) {\n    Object.defineProperty(dest, key, {\n      get: function get() {\n        return state[key];\n      },\n      enumerable: true\n    });\n  });\n};\nvar addLazyFormState = function addLazyFormState(dest, state) {\n  return addLazyState(dest, state, [\"active\", \"dirty\", \"dirtyFields\", \"dirtySinceLastSubmit\", \"dirtyFieldsSinceLastSubmit\", \"error\", \"errors\", \"hasSubmitErrors\", \"hasValidationErrors\", \"initialValues\", \"invalid\", \"modified\", \"modifiedSinceLastSubmit\", \"pristine\", \"submitError\", \"submitErrors\", \"submitFailed\", \"submitSucceeded\", \"submitting\", \"touched\", \"valid\", \"validating\", \"values\", \"visited\"]);\n};\nvar addLazyFieldMetaState = function addLazyFieldMetaState(dest, state) {\n  return addLazyState(dest, state, [\"active\", \"data\", \"dirty\", \"dirtySinceLastSubmit\", \"error\", \"initial\", \"invalid\", \"length\", \"modified\", \"modifiedSinceLastSubmit\", \"pristine\", \"submitError\", \"submitFailed\", \"submitSucceeded\", \"submitting\", \"touched\", \"valid\", \"validating\", \"visited\"]);\n};\nvar _excluded$2 = [\"debug\", \"decorators\", \"destroyOnUnregister\", \"form\", \"initialValues\", \"initialValuesEqual\", \"keepDirtyOnReinitialize\", \"mutators\", \"onSubmit\", \"subscription\", \"validate\", \"validateOnBlur\"];\nvar versions = {\n  \"final-form\": version$1,\n  \"react-final-form\": version\n};\nvar all$1 = formSubscriptionItems.reduce(function (result, key) {\n  result[key] = true;\n  return result;\n}, {});\nfunction ReactFinalForm(_ref) {\n  var debug = _ref.debug,\n    _ref$decorators = _ref.decorators,\n    decorators = _ref$decorators === void 0 ? [] : _ref$decorators,\n    destroyOnUnregister = _ref.destroyOnUnregister,\n    alternateFormApi = _ref.form,\n    initialValues = _ref.initialValues,\n    initialValuesEqual = _ref.initialValuesEqual,\n    keepDirtyOnReinitialize = _ref.keepDirtyOnReinitialize,\n    mutators = _ref.mutators,\n    onSubmit = _ref.onSubmit,\n    _ref$subscription = _ref.subscription,\n    subscription = _ref$subscription === void 0 ? all$1 : _ref$subscription,\n    validate = _ref.validate,\n    validateOnBlur = _ref.validateOnBlur,\n    rest = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n  var config = {\n    debug: debug,\n    destroyOnUnregister: destroyOnUnregister,\n    initialValues: initialValues,\n    keepDirtyOnReinitialize: keepDirtyOnReinitialize,\n    mutators: mutators,\n    onSubmit: onSubmit,\n    validate: validate,\n    validateOnBlur: validateOnBlur\n  };\n  var form = useConstant(function () {\n    var f = alternateFormApi || createForm(config); // pause validation until children register all fields on first render (unpaused in useEffect() below)\n\n    f.pauseValidation();\n    return f;\n  }); // synchronously register and unregister to query form state for our subscription on first render\n\n  var _React$useState = React.useState(function () {\n      var initialState = {};\n      form.subscribe(function (state) {\n        initialState = state;\n      }, subscription)();\n      return initialState;\n    }),\n    state = _React$useState[0],\n    setState = _React$useState[1]; // save a copy of state that can break through the closure\n  // on the shallowEqual() line below.\n\n  var stateRef = useLatest(state);\n  React.useEffect(function () {\n    // We have rendered, so all fields are now registered, so we can unpause validation\n    form.isValidationPaused() && form.resumeValidation();\n    var unsubscriptions = [form.subscribe(function (s) {\n      if (!shallowEqual(s, stateRef.current)) {\n        setState(s);\n      }\n    }, subscription)].concat(decorators ? decorators.map(function (decorator) {\n      return (\n        // this noop ternary is to appease the flow gods\n        // istanbul ignore next\n        decorator(form)\n      );\n    }) : []);\n    return function () {\n      form.pauseValidation(); // pause validation so we don't revalidate on every field deregistration\n\n      unsubscriptions.reverse().forEach(function (unsubscribe) {\n        return unsubscribe();\n      }); // don't need to resume validation here; either unmounting, or will re-run this hook with new deps\n    }; // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, decorators); // warn about decorator changes\n  // istanbul ignore next\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // You're never supposed to use hooks inside a conditional, but in this\n    // case we can be certain that you're not going to be changing your\n    // NODE_ENV between renders, so this is safe.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useWhenValueChanges(decorators, function () {\n      console.error(\"Form decorators should not change from one render to the next as new values will be ignored\");\n    }, shallowEqual);\n  } // allow updatable config\n\n  useWhenValueChanges(debug, function () {\n    form.setConfig(\"debug\", debug);\n  });\n  useWhenValueChanges(destroyOnUnregister, function () {\n    form.destroyOnUnregister = !!destroyOnUnregister;\n  });\n  useWhenValueChanges(keepDirtyOnReinitialize, function () {\n    form.setConfig(\"keepDirtyOnReinitialize\", keepDirtyOnReinitialize);\n  });\n  useWhenValueChanges(initialValues, function () {\n    form.setConfig(\"initialValues\", initialValues);\n  }, initialValuesEqual || shallowEqual);\n  useWhenValueChanges(mutators, function () {\n    form.setConfig(\"mutators\", mutators);\n  });\n  useWhenValueChanges(onSubmit, function () {\n    form.setConfig(\"onSubmit\", onSubmit);\n  });\n  useWhenValueChanges(validate, function () {\n    form.setConfig(\"validate\", validate);\n  });\n  useWhenValueChanges(validateOnBlur, function () {\n    form.setConfig(\"validateOnBlur\", validateOnBlur);\n  });\n  var handleSubmit = function handleSubmit(event) {\n    if (event) {\n      // sometimes not true, e.g. React Native\n      if (typeof event.preventDefault === \"function\") {\n        event.preventDefault();\n      }\n      if (typeof event.stopPropagation === \"function\") {\n        // prevent any outer forms from receiving the event too\n        event.stopPropagation();\n      }\n    }\n    return form.submit();\n  };\n  var renderProps = {\n    form: _extends({}, form, {\n      reset: function reset(eventOrValues) {\n        if (isSyntheticEvent(eventOrValues)) {\n          // it's a React SyntheticEvent, call reset with no arguments\n          form.reset();\n        } else {\n          form.reset(eventOrValues);\n        }\n      }\n    }),\n    handleSubmit: handleSubmit\n  };\n  addLazyFormState(renderProps, state);\n  return /*#__PURE__*/React.createElement(ReactFinalFormContext.Provider, {\n    value: form\n  }, renderComponent(_extends({}, rest, {\n    __versions: versions\n  }), renderProps, \"ReactFinalForm\"));\n}\nfunction useForm(componentName) {\n  var form = React.useContext(ReactFinalFormContext);\n  if (!form) {\n    throw new Error((componentName || \"useForm\") + \" must be used inside of a <Form> component\");\n  }\n  return form;\n}\nfunction useFormState(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    onChange = _ref.onChange,\n    _ref$subscription = _ref.subscription,\n    subscription = _ref$subscription === void 0 ? all$1 : _ref$subscription;\n  var form = useForm(\"useFormState\");\n  var firstRender = React.useRef(true);\n  var onChangeRef = React.useRef(onChange);\n  onChangeRef.current = onChange; // synchronously register and unregister to query field state for our subscription on first render\n\n  var _React$useState = React.useState(function () {\n      var initialState = {};\n      form.subscribe(function (state) {\n        initialState = state;\n      }, subscription)();\n      if (onChange) {\n        onChange(initialState);\n      }\n      return initialState;\n    }),\n    state = _React$useState[0],\n    setState = _React$useState[1];\n  React.useEffect(function () {\n    return form.subscribe(function (newState) {\n      if (firstRender.current) {\n        firstRender.current = false;\n      } else {\n        setState(newState);\n        if (onChangeRef.current) {\n          onChangeRef.current(newState);\n        }\n      }\n    }, subscription);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  var lazyState = {};\n  addLazyFormState(lazyState, state);\n  return lazyState;\n}\nvar _excluded$1 = [\"onChange\", \"subscription\"];\nfunction FormSpy(_ref) {\n  var onChange = _ref.onChange,\n    subscription = _ref.subscription,\n    rest = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n  var reactFinalForm = useForm(\"FormSpy\");\n  var state = useFormState({\n    onChange: onChange,\n    subscription: subscription\n  });\n  if (onChange) {\n    return null;\n  }\n  var renderProps = {\n    form: _extends({}, reactFinalForm, {\n      reset: function reset(eventOrValues) {\n        if (isSyntheticEvent(eventOrValues)) {\n          // it's a React SyntheticEvent, call reset with no arguments\n          reactFinalForm.reset();\n        } else {\n          reactFinalForm.reset(eventOrValues);\n        }\n      }\n    })\n  };\n  return renderComponent(_extends({}, rest, renderProps), state, \"FormSpy\");\n}\nvar isReactNative = typeof window !== \"undefined\" && window.navigator && window.navigator.product && window.navigator.product === \"ReactNative\";\nvar getSelectedValues = function getSelectedValues(options) {\n  var result = [];\n  if (options) {\n    for (var index = 0; index < options.length; index++) {\n      var option = options[index];\n      if (option.selected) {\n        result.push(option.value);\n      }\n    }\n  }\n  return result;\n};\nvar getValue = function getValue(event, currentValue, valueProp, isReactNative) {\n  if (!isReactNative && event.nativeEvent && event.nativeEvent.text !== undefined) {\n    return event.nativeEvent.text;\n  }\n  if (isReactNative && event.nativeEvent) {\n    return event.nativeEvent.text;\n  }\n  var detypedEvent = event;\n  var _detypedEvent$target = detypedEvent.target,\n    type = _detypedEvent$target.type,\n    value = _detypedEvent$target.value,\n    checked = _detypedEvent$target.checked;\n  switch (type) {\n    case \"checkbox\":\n      if (valueProp !== undefined) {\n        // we are maintaining an array, not just a boolean\n        if (checked) {\n          // add value to current array value\n          return Array.isArray(currentValue) ? currentValue.concat(valueProp) : [valueProp];\n        } else {\n          // remove value from current array value\n          if (!Array.isArray(currentValue)) {\n            return currentValue;\n          }\n          var index = currentValue.indexOf(valueProp);\n          if (index < 0) {\n            return currentValue;\n          } else {\n            return currentValue.slice(0, index).concat(currentValue.slice(index + 1));\n          }\n        }\n      } else {\n        // it's just a boolean\n        return !!checked;\n      }\n    case \"select-multiple\":\n      return getSelectedValues(event.target.options);\n    default:\n      return value;\n  }\n};\n\n/**\n * Creates a callback, even with closures, that will be\n * instance === for the lifetime of the component, always\n * calling the most recent version of the function and its\n * closures.\n */\n\nfunction useConstantCallback(callback) {\n  var ref = React.useRef(callback);\n  React.useEffect(function () {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current.apply(null, args);\n  }, []);\n}\nvar all = fieldSubscriptionItems.reduce(function (result, key) {\n  result[key] = true;\n  return result;\n}, {});\nvar defaultFormat = function defaultFormat(value, name) {\n  return value === undefined ? \"\" : value;\n};\nvar defaultParse = function defaultParse(value, name) {\n  return value === \"\" ? undefined : value;\n};\nvar defaultIsEqual = function defaultIsEqual(a, b) {\n  return a === b;\n};\nfunction useField(name, config) {\n  if (config === void 0) {\n    config = {};\n  }\n  var _config = config,\n    afterSubmit = _config.afterSubmit,\n    allowNull = _config.allowNull,\n    component = _config.component,\n    data = _config.data,\n    defaultValue = _config.defaultValue,\n    _config$format = _config.format,\n    format = _config$format === void 0 ? defaultFormat : _config$format,\n    formatOnBlur = _config.formatOnBlur,\n    initialValue = _config.initialValue,\n    multiple = _config.multiple,\n    _config$parse = _config.parse,\n    parse = _config$parse === void 0 ? defaultParse : _config$parse,\n    _config$subscription = _config.subscription,\n    subscription = _config$subscription === void 0 ? all : _config$subscription,\n    type = _config.type,\n    validateFields = _config.validateFields,\n    _value = _config.value;\n  var form = useForm(\"useField\");\n  var configRef = useLatest(config);\n  var register = function register(callback, silent) {\n    return (\n      // avoid using `state` const in any closures created inside `register`\n      // because they would refer `state` from current execution context\n      // whereas actual `state` would defined in the subsequent `useField` hook\n      // execution\n      // (that would be caused by `setState` call performed in `register` callback)\n      form.registerField(name, callback, subscription, {\n        afterSubmit: afterSubmit,\n        beforeSubmit: function beforeSubmit() {\n          var _configRef$current = configRef.current,\n            beforeSubmit = _configRef$current.beforeSubmit,\n            formatOnBlur = _configRef$current.formatOnBlur,\n            _configRef$current$fo = _configRef$current.format,\n            format = _configRef$current$fo === void 0 ? defaultFormat : _configRef$current$fo;\n          if (formatOnBlur) {\n            var _ref = form.getFieldState(name),\n              value = _ref.value;\n            var formatted = format(value, name);\n            if (formatted !== value) {\n              form.change(name, formatted);\n            }\n          }\n          return beforeSubmit && beforeSubmit();\n        },\n        data: data,\n        defaultValue: defaultValue,\n        getValidator: function getValidator() {\n          return configRef.current.validate;\n        },\n        initialValue: initialValue,\n        isEqual: function isEqual(a, b) {\n          return (configRef.current.isEqual || defaultIsEqual)(a, b);\n        },\n        silent: silent,\n        validateFields: validateFields\n      })\n    );\n  };\n  var firstRender = React.useRef(true); // synchronously register and unregister to query field state for our subscription on first render\n\n  var _React$useState = React.useState(function () {\n      var initialState = {}; // temporarily disable destroyOnUnregister\n\n      // temporarily disable destroyOnUnregister\n      var destroyOnUnregister = form.destroyOnUnregister;\n      form.destroyOnUnregister = false;\n      register(function (state) {\n        initialState = state;\n      }, true)(); // return destroyOnUnregister to its original value\n\n      // return destroyOnUnregister to its original value\n      form.destroyOnUnregister = destroyOnUnregister;\n      return initialState;\n    }),\n    state = _React$useState[0],\n    setState = _React$useState[1];\n  React.useEffect(function () {\n    return register(function (state) {\n      if (firstRender.current) {\n        firstRender.current = false;\n      } else {\n        setState(state);\n      }\n    }, false);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [name, data, defaultValue,\n  // If we want to allow inline fat-arrow field-level validation functions, we\n  // cannot reregister field every time validate function !==.\n  // validate,\n  initialValue // The validateFields array is often passed as validateFields={[]}, creating\n  // a !== new array every time. If it needs to be changed, a rerender/reregister\n  // can be forced by changing the key prop\n  // validateFields\n  ]);\n  var meta = {};\n  addLazyFieldMetaState(meta, state);\n  var input = {\n    name: name,\n    get value() {\n      var value = state.value;\n      if (formatOnBlur) {\n        if (component === \"input\") {\n          value = defaultFormat(value);\n        }\n      } else {\n        value = format(value, name);\n      }\n      if (value === null && !allowNull) {\n        value = \"\";\n      }\n      if (type === \"checkbox\" || type === \"radio\") {\n        return _value;\n      } else if (component === \"select\" && multiple) {\n        return value || [];\n      }\n      return value;\n    },\n    get checked() {\n      var value = state.value;\n      if (type === \"checkbox\") {\n        value = format(value, name);\n        if (_value === undefined) {\n          return !!value;\n        } else {\n          return !!(Array.isArray(value) && ~value.indexOf(_value));\n        }\n      } else if (type === \"radio\") {\n        return format(value, name) === _value;\n      }\n      return undefined;\n    },\n    onBlur: useConstantCallback(function (event) {\n      state.blur();\n      if (formatOnBlur) {\n        /**\n         * Here we must fetch the value directly from Final Form because we cannot\n         * trust that our `state` closure has the most recent value. This is a problem\n         * if-and-only-if the library consumer has called `onChange()` immediately\n         * before calling `onBlur()`, but before the field has had a chance to receive\n         * the value update from Final Form.\n         */\n        var fieldState = form.getFieldState(state.name);\n        state.change(format(fieldState.value, state.name));\n      }\n    }),\n    onChange: useConstantCallback(function (event) {\n      // istanbul ignore next\n      if (process.env.NODE_ENV !== \"production\" && event && event.target) {\n        var targetType = event.target.type;\n        var unknown = ~[\"checkbox\", \"radio\", \"select-multiple\"].indexOf(targetType) && !type && component !== \"select\";\n        var _value2 = targetType === \"select-multiple\" ? state.value : _value;\n        if (unknown) {\n          console.error(\"You must pass `type=\\\"\" + (targetType === \"select-multiple\" ? \"select\" : targetType) + \"\\\"` prop to your Field(\" + name + \") component.\\n\" + (\"Without it we don't know how to unpack your `value` prop - \" + (Array.isArray(_value2) ? \"[\" + _value2 + \"]\" : \"\\\"\" + _value2 + \"\\\"\") + \".\"));\n        }\n      }\n      var value = event && event.target ? getValue(event, state.value, _value, isReactNative) : event;\n      state.change(parse(value, name));\n    }),\n    onFocus: useConstantCallback(function (event) {\n      return state.focus();\n    })\n  };\n  if (multiple) {\n    input.multiple = multiple;\n  }\n  if (type !== undefined) {\n    input.type = type;\n  }\n  var renderProps = {\n    input: input,\n    meta: meta\n  }; // assign to force Flow check\n\n  return renderProps;\n}\nvar _excluded = [\"afterSubmit\", \"allowNull\", \"beforeSubmit\", \"children\", \"component\", \"data\", \"defaultValue\", \"format\", \"formatOnBlur\", \"initialValue\", \"isEqual\", \"multiple\", \"name\", \"parse\", \"subscription\", \"type\", \"validate\", \"validateFields\", \"value\"];\nvar Field = /*#__PURE__*/React.forwardRef(function Field(_ref, ref) {\n  var afterSubmit = _ref.afterSubmit,\n    allowNull = _ref.allowNull,\n    beforeSubmit = _ref.beforeSubmit,\n    children = _ref.children,\n    component = _ref.component,\n    data = _ref.data,\n    defaultValue = _ref.defaultValue,\n    format = _ref.format,\n    formatOnBlur = _ref.formatOnBlur,\n    initialValue = _ref.initialValue,\n    isEqual = _ref.isEqual,\n    multiple = _ref.multiple,\n    name = _ref.name,\n    parse = _ref.parse,\n    subscription = _ref.subscription,\n    type = _ref.type,\n    validate = _ref.validate,\n    validateFields = _ref.validateFields,\n    value = _ref.value,\n    rest = _objectWithoutPropertiesLoose(_ref, _excluded);\n  var field = useField(name, {\n    afterSubmit: afterSubmit,\n    allowNull: allowNull,\n    beforeSubmit: beforeSubmit,\n    children: children,\n    component: component,\n    data: data,\n    defaultValue: defaultValue,\n    format: format,\n    formatOnBlur: formatOnBlur,\n    initialValue: initialValue,\n    isEqual: isEqual,\n    multiple: multiple,\n    parse: parse,\n    subscription: subscription,\n    type: type,\n    validate: validate,\n    validateFields: validateFields,\n    value: value\n  });\n  if (typeof children === \"function\") {\n    return children(_extends({}, field, rest));\n  }\n  if (typeof component === \"string\") {\n    // ignore meta, combine input with any other props\n    return /*#__PURE__*/React.createElement(component, _extends({}, field.input, {\n      children: children,\n      ref: ref\n    }, rest));\n  }\n  if (!name) {\n    throw new Error(\"prop name cannot be undefined in <Field> component\");\n  }\n  return renderComponent(_extends({\n    children: children,\n    component: component,\n    ref: ref\n  }, rest), field, \"Field(\" + name + \")\");\n});\nfunction withTypes() {\n  return {\n    Form: ReactFinalForm,\n    FormSpy: FormSpy\n  };\n}\nexport { Field, ReactFinalForm as Form, FormSpy, useField, useForm, useFormState, version, withTypes };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "React", "React__default", "formSubscriptionItems", "createForm", "version", "version$1", "fieldSubscriptionItems", "_excluded$3", "renderComponent", "props", "lazyProps", "name", "render", "children", "component", "rest", "createElement", "Object", "assign", "undefined", "Error", "useWhenValueChanges", "value", "callback", "isEqual", "a", "b", "previous", "useRef", "useEffect", "current", "useConstant", "init", "ref", "shallowEqual", "keysA", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "isSyntheticEvent", "candidate", "stopPropagation", "ReactFinalFormContext", "createContext", "useLatest", "addLazyState", "dest", "state", "for<PERSON>ach", "defineProperty", "get", "enumerable", "addLazyFormState", "addLazyFieldMetaState", "_excluded$2", "versions", "all$1", "reduce", "result", "ReactFinalForm", "_ref", "debug", "_ref$decorators", "decorators", "destroyOnUnregister", "alternateFormApi", "form", "initialValues", "initialValuesEqual", "keepDirtyOnReinitialize", "mutators", "onSubmit", "_ref$subscription", "subscription", "validate", "validateOnBlur", "config", "f", "pauseValidation", "_React$useState", "useState", "initialState", "subscribe", "setState", "stateRef", "isValidationPaused", "resumeValidation", "unsubscriptions", "s", "concat", "map", "decorator", "reverse", "unsubscribe", "process", "env", "NODE_ENV", "console", "error", "setConfig", "handleSubmit", "event", "preventDefault", "submit", "renderProps", "reset", "event<PERSON>r<PERSON><PERSON><PERSON>", "Provider", "__versions", "useForm", "componentName", "useContext", "useFormState", "_temp", "onChange", "firstRender", "onChangeRef", "newState", "lazyState", "_excluded$1", "FormSpy", "reactFinalForm", "isReactNative", "window", "navigator", "product", "getSelectedValues", "options", "index", "option", "selected", "push", "getValue", "currentValue", "valueProp", "nativeEvent", "text", "detypedEvent", "_detypedEvent$target", "target", "type", "checked", "Array", "isArray", "indexOf", "slice", "useConstantCallback", "useCallback", "_len", "arguments", "args", "_key", "apply", "all", "defaultFormat", "defaultParse", "defaultIsEqual", "useField", "_config", "afterSubmit", "allowNull", "data", "defaultValue", "_config$format", "format", "formatOnBlur", "initialValue", "multiple", "_config$parse", "parse", "_config$subscription", "validateFields", "_value", "configRef", "register", "silent", "registerField", "beforeSubmit", "_configRef$current", "_configRef$current$fo", "getFieldState", "formatted", "change", "getValidator", "meta", "input", "onBlur", "blur", "fieldState", "targetType", "unknown", "_value2", "onFocus", "focus", "_excluded", "Field", "forwardRef", "field", "withTypes", "Form"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-final-form/dist/react-final-form.es.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport * as React from 'react';\nimport React__default from 'react';\nimport { formSubscriptionItems, createForm, version as version$1, fieldSubscriptionItems } from 'final-form';\n\nvar _excluded$3 = [\"render\", \"children\", \"component\"];\n// shared logic between components that use either render prop,\n// children render function, or component prop\nfunction renderComponent(props, lazyProps, name) {\n  var render = props.render,\n      children = props.children,\n      component = props.component,\n      rest = _objectWithoutPropertiesLoose(props, _excluded$3);\n\n  if (component) {\n    return /*#__PURE__*/React.createElement(component, Object.assign(lazyProps, rest, {\n      children: children,\n      render: render\n    }));\n  }\n\n  if (render) {\n    return render(children === undefined ? Object.assign(lazyProps, rest) : // inject children back in\n    Object.assign(lazyProps, rest, {\n      children: children\n    }));\n  }\n\n  if (typeof children !== \"function\") {\n    throw new Error(\"Must specify either a render prop, a render function as children, or a component prop to \" + name);\n  }\n\n  return children(Object.assign(lazyProps, rest));\n}\n\nfunction useWhenValueChanges(value, callback, isEqual) {\n  if (isEqual === void 0) {\n    isEqual = function isEqual(a, b) {\n      return a === b;\n    };\n  }\n\n  var previous = React__default.useRef(value);\n  React__default.useEffect(function () {\n    if (!isEqual(value, previous.current)) {\n      callback();\n      previous.current = value;\n    }\n  });\n}\n\n/**\n * A simple hook to create a constant value that lives for\n * the lifetime of the component.\n *\n * Plagiarized from https://github.com/Andarist/use-constant\n *\n * Do NOT reuse this code unless you know what you're doing.\n * Use Andarist's hook; it's more fault tolerant to things like\n * falsy values.\n *\n * @param {Function} init - A function to generate the value\n */\n\nfunction useConstant(init) {\n  var ref = React__default.useRef();\n\n  if (!ref.current) {\n    ref.current = init();\n  }\n\n  return ref.current;\n}\n\nvar shallowEqual = function shallowEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== \"object\" || !a || typeof b !== \"object\" || !b) {\n    return false;\n  }\n\n  var keysA = Object.keys(a);\n  var keysB = Object.keys(b);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(b);\n\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key) || a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\nvar isSyntheticEvent = function isSyntheticEvent(candidate) {\n  return !!(candidate && typeof candidate.stopPropagation === \"function\");\n};\n\nvar ReactFinalFormContext = /*#__PURE__*/React.createContext();\n\nfunction useLatest(value) {\n  var ref = React__default.useRef(value);\n  React__default.useEffect(function () {\n    ref.current = value;\n  });\n  return ref;\n}\n\nvar version = \"6.5.8\";\n\nvar addLazyState = function addLazyState(dest, state, keys) {\n  keys.forEach(function (key) {\n    Object.defineProperty(dest, key, {\n      get: function get() {\n        return state[key];\n      },\n      enumerable: true\n    });\n  });\n};\n\nvar addLazyFormState = function addLazyFormState(dest, state) {\n  return addLazyState(dest, state, [\"active\", \"dirty\", \"dirtyFields\", \"dirtySinceLastSubmit\", \"dirtyFieldsSinceLastSubmit\", \"error\", \"errors\", \"hasSubmitErrors\", \"hasValidationErrors\", \"initialValues\", \"invalid\", \"modified\", \"modifiedSinceLastSubmit\", \"pristine\", \"submitError\", \"submitErrors\", \"submitFailed\", \"submitSucceeded\", \"submitting\", \"touched\", \"valid\", \"validating\", \"values\", \"visited\"]);\n};\nvar addLazyFieldMetaState = function addLazyFieldMetaState(dest, state) {\n  return addLazyState(dest, state, [\"active\", \"data\", \"dirty\", \"dirtySinceLastSubmit\", \"error\", \"initial\", \"invalid\", \"length\", \"modified\", \"modifiedSinceLastSubmit\", \"pristine\", \"submitError\", \"submitFailed\", \"submitSucceeded\", \"submitting\", \"touched\", \"valid\", \"validating\", \"visited\"]);\n};\n\nvar _excluded$2 = [\"debug\", \"decorators\", \"destroyOnUnregister\", \"form\", \"initialValues\", \"initialValuesEqual\", \"keepDirtyOnReinitialize\", \"mutators\", \"onSubmit\", \"subscription\", \"validate\", \"validateOnBlur\"];\nvar versions = {\n  \"final-form\": version$1,\n  \"react-final-form\": version\n};\nvar all$1 = formSubscriptionItems.reduce(function (result, key) {\n  result[key] = true;\n  return result;\n}, {});\n\nfunction ReactFinalForm(_ref) {\n  var debug = _ref.debug,\n      _ref$decorators = _ref.decorators,\n      decorators = _ref$decorators === void 0 ? [] : _ref$decorators,\n      destroyOnUnregister = _ref.destroyOnUnregister,\n      alternateFormApi = _ref.form,\n      initialValues = _ref.initialValues,\n      initialValuesEqual = _ref.initialValuesEqual,\n      keepDirtyOnReinitialize = _ref.keepDirtyOnReinitialize,\n      mutators = _ref.mutators,\n      onSubmit = _ref.onSubmit,\n      _ref$subscription = _ref.subscription,\n      subscription = _ref$subscription === void 0 ? all$1 : _ref$subscription,\n      validate = _ref.validate,\n      validateOnBlur = _ref.validateOnBlur,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n\n  var config = {\n    debug: debug,\n    destroyOnUnregister: destroyOnUnregister,\n    initialValues: initialValues,\n    keepDirtyOnReinitialize: keepDirtyOnReinitialize,\n    mutators: mutators,\n    onSubmit: onSubmit,\n    validate: validate,\n    validateOnBlur: validateOnBlur\n  };\n  var form = useConstant(function () {\n    var f = alternateFormApi || createForm(config); // pause validation until children register all fields on first render (unpaused in useEffect() below)\n\n    f.pauseValidation();\n    return f;\n  }); // synchronously register and unregister to query form state for our subscription on first render\n\n  var _React$useState = React.useState(function () {\n    var initialState = {};\n    form.subscribe(function (state) {\n      initialState = state;\n    }, subscription)();\n    return initialState;\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1]; // save a copy of state that can break through the closure\n  // on the shallowEqual() line below.\n\n\n  var stateRef = useLatest(state);\n  React.useEffect(function () {\n    // We have rendered, so all fields are now registered, so we can unpause validation\n    form.isValidationPaused() && form.resumeValidation();\n    var unsubscriptions = [form.subscribe(function (s) {\n      if (!shallowEqual(s, stateRef.current)) {\n        setState(s);\n      }\n    }, subscription)].concat(decorators ? decorators.map(function (decorator) {\n      return (// this noop ternary is to appease the flow gods\n        // istanbul ignore next\n        decorator(form)\n      );\n    }) : []);\n    return function () {\n      form.pauseValidation(); // pause validation so we don't revalidate on every field deregistration\n\n      unsubscriptions.reverse().forEach(function (unsubscribe) {\n        return unsubscribe();\n      }); // don't need to resume validation here; either unmounting, or will re-run this hook with new deps\n    }; // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, decorators); // warn about decorator changes\n  // istanbul ignore next\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // You're never supposed to use hooks inside a conditional, but in this\n    // case we can be certain that you're not going to be changing your\n    // NODE_ENV between renders, so this is safe.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useWhenValueChanges(decorators, function () {\n      console.error(\"Form decorators should not change from one render to the next as new values will be ignored\");\n    }, shallowEqual);\n  } // allow updatable config\n\n\n  useWhenValueChanges(debug, function () {\n    form.setConfig(\"debug\", debug);\n  });\n  useWhenValueChanges(destroyOnUnregister, function () {\n    form.destroyOnUnregister = !!destroyOnUnregister;\n  });\n  useWhenValueChanges(keepDirtyOnReinitialize, function () {\n    form.setConfig(\"keepDirtyOnReinitialize\", keepDirtyOnReinitialize);\n  });\n  useWhenValueChanges(initialValues, function () {\n    form.setConfig(\"initialValues\", initialValues);\n  }, initialValuesEqual || shallowEqual);\n  useWhenValueChanges(mutators, function () {\n    form.setConfig(\"mutators\", mutators);\n  });\n  useWhenValueChanges(onSubmit, function () {\n    form.setConfig(\"onSubmit\", onSubmit);\n  });\n  useWhenValueChanges(validate, function () {\n    form.setConfig(\"validate\", validate);\n  });\n  useWhenValueChanges(validateOnBlur, function () {\n    form.setConfig(\"validateOnBlur\", validateOnBlur);\n  });\n\n  var handleSubmit = function handleSubmit(event) {\n    if (event) {\n      // sometimes not true, e.g. React Native\n      if (typeof event.preventDefault === \"function\") {\n        event.preventDefault();\n      }\n\n      if (typeof event.stopPropagation === \"function\") {\n        // prevent any outer forms from receiving the event too\n        event.stopPropagation();\n      }\n    }\n\n    return form.submit();\n  };\n\n  var renderProps = {\n    form: _extends({}, form, {\n      reset: function reset(eventOrValues) {\n        if (isSyntheticEvent(eventOrValues)) {\n          // it's a React SyntheticEvent, call reset with no arguments\n          form.reset();\n        } else {\n          form.reset(eventOrValues);\n        }\n      }\n    }),\n    handleSubmit: handleSubmit\n  };\n  addLazyFormState(renderProps, state);\n  return /*#__PURE__*/React.createElement(ReactFinalFormContext.Provider, {\n    value: form\n  }, renderComponent(_extends({}, rest, {\n    __versions: versions\n  }), renderProps, \"ReactFinalForm\"));\n}\n\nfunction useForm(componentName) {\n  var form = React.useContext(ReactFinalFormContext);\n\n  if (!form) {\n    throw new Error((componentName || \"useForm\") + \" must be used inside of a <Form> component\");\n  }\n\n  return form;\n}\n\nfunction useFormState(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      onChange = _ref.onChange,\n      _ref$subscription = _ref.subscription,\n      subscription = _ref$subscription === void 0 ? all$1 : _ref$subscription;\n\n  var form = useForm(\"useFormState\");\n  var firstRender = React.useRef(true);\n  var onChangeRef = React.useRef(onChange);\n  onChangeRef.current = onChange; // synchronously register and unregister to query field state for our subscription on first render\n\n  var _React$useState = React.useState(function () {\n    var initialState = {};\n    form.subscribe(function (state) {\n      initialState = state;\n    }, subscription)();\n\n    if (onChange) {\n      onChange(initialState);\n    }\n\n    return initialState;\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  React.useEffect(function () {\n    return form.subscribe(function (newState) {\n      if (firstRender.current) {\n        firstRender.current = false;\n      } else {\n        setState(newState);\n\n        if (onChangeRef.current) {\n          onChangeRef.current(newState);\n        }\n      }\n    }, subscription);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  var lazyState = {};\n  addLazyFormState(lazyState, state);\n  return lazyState;\n}\n\nvar _excluded$1 = [\"onChange\", \"subscription\"];\n\nfunction FormSpy(_ref) {\n  var onChange = _ref.onChange,\n      subscription = _ref.subscription,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n\n  var reactFinalForm = useForm(\"FormSpy\");\n  var state = useFormState({\n    onChange: onChange,\n    subscription: subscription\n  });\n\n  if (onChange) {\n    return null;\n  }\n\n  var renderProps = {\n    form: _extends({}, reactFinalForm, {\n      reset: function reset(eventOrValues) {\n        if (isSyntheticEvent(eventOrValues)) {\n          // it's a React SyntheticEvent, call reset with no arguments\n          reactFinalForm.reset();\n        } else {\n          reactFinalForm.reset(eventOrValues);\n        }\n      }\n    })\n  };\n  return renderComponent(_extends({}, rest, renderProps), state, \"FormSpy\");\n}\n\nvar isReactNative = typeof window !== \"undefined\" && window.navigator && window.navigator.product && window.navigator.product === \"ReactNative\";\n\nvar getSelectedValues = function getSelectedValues(options) {\n  var result = [];\n\n  if (options) {\n    for (var index = 0; index < options.length; index++) {\n      var option = options[index];\n\n      if (option.selected) {\n        result.push(option.value);\n      }\n    }\n  }\n\n  return result;\n};\n\nvar getValue = function getValue(event, currentValue, valueProp, isReactNative) {\n  if (!isReactNative && event.nativeEvent && event.nativeEvent.text !== undefined) {\n    return event.nativeEvent.text;\n  }\n\n  if (isReactNative && event.nativeEvent) {\n    return event.nativeEvent.text;\n  }\n\n  var detypedEvent = event;\n  var _detypedEvent$target = detypedEvent.target,\n      type = _detypedEvent$target.type,\n      value = _detypedEvent$target.value,\n      checked = _detypedEvent$target.checked;\n\n  switch (type) {\n    case \"checkbox\":\n      if (valueProp !== undefined) {\n        // we are maintaining an array, not just a boolean\n        if (checked) {\n          // add value to current array value\n          return Array.isArray(currentValue) ? currentValue.concat(valueProp) : [valueProp];\n        } else {\n          // remove value from current array value\n          if (!Array.isArray(currentValue)) {\n            return currentValue;\n          }\n\n          var index = currentValue.indexOf(valueProp);\n\n          if (index < 0) {\n            return currentValue;\n          } else {\n            return currentValue.slice(0, index).concat(currentValue.slice(index + 1));\n          }\n        }\n      } else {\n        // it's just a boolean\n        return !!checked;\n      }\n\n    case \"select-multiple\":\n      return getSelectedValues(event.target.options);\n\n    default:\n      return value;\n  }\n};\n\n/**\n * Creates a callback, even with closures, that will be\n * instance === for the lifetime of the component, always\n * calling the most recent version of the function and its\n * closures.\n */\n\nfunction useConstantCallback(callback) {\n  var ref = React.useRef(callback);\n  React.useEffect(function () {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return ref.current.apply(null, args);\n  }, []);\n}\n\nvar all = fieldSubscriptionItems.reduce(function (result, key) {\n  result[key] = true;\n  return result;\n}, {});\n\nvar defaultFormat = function defaultFormat(value, name) {\n  return value === undefined ? \"\" : value;\n};\n\nvar defaultParse = function defaultParse(value, name) {\n  return value === \"\" ? undefined : value;\n};\n\nvar defaultIsEqual = function defaultIsEqual(a, b) {\n  return a === b;\n};\n\nfunction useField(name, config) {\n  if (config === void 0) {\n    config = {};\n  }\n\n  var _config = config,\n      afterSubmit = _config.afterSubmit,\n      allowNull = _config.allowNull,\n      component = _config.component,\n      data = _config.data,\n      defaultValue = _config.defaultValue,\n      _config$format = _config.format,\n      format = _config$format === void 0 ? defaultFormat : _config$format,\n      formatOnBlur = _config.formatOnBlur,\n      initialValue = _config.initialValue,\n      multiple = _config.multiple,\n      _config$parse = _config.parse,\n      parse = _config$parse === void 0 ? defaultParse : _config$parse,\n      _config$subscription = _config.subscription,\n      subscription = _config$subscription === void 0 ? all : _config$subscription,\n      type = _config.type,\n      validateFields = _config.validateFields,\n      _value = _config.value;\n  var form = useForm(\"useField\");\n  var configRef = useLatest(config);\n\n  var register = function register(callback, silent) {\n    return (// avoid using `state` const in any closures created inside `register`\n      // because they would refer `state` from current execution context\n      // whereas actual `state` would defined in the subsequent `useField` hook\n      // execution\n      // (that would be caused by `setState` call performed in `register` callback)\n      form.registerField(name, callback, subscription, {\n        afterSubmit: afterSubmit,\n        beforeSubmit: function beforeSubmit() {\n          var _configRef$current = configRef.current,\n              beforeSubmit = _configRef$current.beforeSubmit,\n              formatOnBlur = _configRef$current.formatOnBlur,\n              _configRef$current$fo = _configRef$current.format,\n              format = _configRef$current$fo === void 0 ? defaultFormat : _configRef$current$fo;\n\n          if (formatOnBlur) {\n            var _ref = form.getFieldState(name),\n                value = _ref.value;\n\n            var formatted = format(value, name);\n\n            if (formatted !== value) {\n              form.change(name, formatted);\n            }\n          }\n\n          return beforeSubmit && beforeSubmit();\n        },\n        data: data,\n        defaultValue: defaultValue,\n        getValidator: function getValidator() {\n          return configRef.current.validate;\n        },\n        initialValue: initialValue,\n        isEqual: function isEqual(a, b) {\n          return (configRef.current.isEqual || defaultIsEqual)(a, b);\n        },\n        silent: silent,\n        validateFields: validateFields\n      })\n    );\n  };\n\n  var firstRender = React.useRef(true); // synchronously register and unregister to query field state for our subscription on first render\n\n  var _React$useState = React.useState(function () {\n    var initialState = {}; // temporarily disable destroyOnUnregister\n\n    // temporarily disable destroyOnUnregister\n    var destroyOnUnregister = form.destroyOnUnregister;\n    form.destroyOnUnregister = false;\n    register(function (state) {\n      initialState = state;\n    }, true)(); // return destroyOnUnregister to its original value\n\n    // return destroyOnUnregister to its original value\n    form.destroyOnUnregister = destroyOnUnregister;\n    return initialState;\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  React.useEffect(function () {\n    return register(function (state) {\n      if (firstRender.current) {\n        firstRender.current = false;\n      } else {\n        setState(state);\n      }\n    }, false);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [name, data, defaultValue, // If we want to allow inline fat-arrow field-level validation functions, we\n  // cannot reregister field every time validate function !==.\n  // validate,\n  initialValue // The validateFields array is often passed as validateFields={[]}, creating\n  // a !== new array every time. If it needs to be changed, a rerender/reregister\n  // can be forced by changing the key prop\n  // validateFields\n  ]);\n  var meta = {};\n  addLazyFieldMetaState(meta, state);\n  var input = {\n    name: name,\n\n    get value() {\n      var value = state.value;\n\n      if (formatOnBlur) {\n        if (component === \"input\") {\n          value = defaultFormat(value);\n        }\n      } else {\n        value = format(value, name);\n      }\n\n      if (value === null && !allowNull) {\n        value = \"\";\n      }\n\n      if (type === \"checkbox\" || type === \"radio\") {\n        return _value;\n      } else if (component === \"select\" && multiple) {\n        return value || [];\n      }\n\n      return value;\n    },\n\n    get checked() {\n      var value = state.value;\n\n      if (type === \"checkbox\") {\n        value = format(value, name);\n\n        if (_value === undefined) {\n          return !!value;\n        } else {\n          return !!(Array.isArray(value) && ~value.indexOf(_value));\n        }\n      } else if (type === \"radio\") {\n        return format(value, name) === _value;\n      }\n\n      return undefined;\n    },\n\n    onBlur: useConstantCallback(function (event) {\n      state.blur();\n\n      if (formatOnBlur) {\n        /**\n         * Here we must fetch the value directly from Final Form because we cannot\n         * trust that our `state` closure has the most recent value. This is a problem\n         * if-and-only-if the library consumer has called `onChange()` immediately\n         * before calling `onBlur()`, but before the field has had a chance to receive\n         * the value update from Final Form.\n         */\n        var fieldState = form.getFieldState(state.name);\n        state.change(format(fieldState.value, state.name));\n      }\n    }),\n    onChange: useConstantCallback(function (event) {\n      // istanbul ignore next\n      if (process.env.NODE_ENV !== \"production\" && event && event.target) {\n        var targetType = event.target.type;\n        var unknown = ~[\"checkbox\", \"radio\", \"select-multiple\"].indexOf(targetType) && !type && component !== \"select\";\n\n        var _value2 = targetType === \"select-multiple\" ? state.value : _value;\n\n        if (unknown) {\n          console.error(\"You must pass `type=\\\"\" + (targetType === \"select-multiple\" ? \"select\" : targetType) + \"\\\"` prop to your Field(\" + name + \") component.\\n\" + (\"Without it we don't know how to unpack your `value` prop - \" + (Array.isArray(_value2) ? \"[\" + _value2 + \"]\" : \"\\\"\" + _value2 + \"\\\"\") + \".\"));\n        }\n      }\n\n      var value = event && event.target ? getValue(event, state.value, _value, isReactNative) : event;\n      state.change(parse(value, name));\n    }),\n    onFocus: useConstantCallback(function (event) {\n      return state.focus();\n    })\n  };\n\n  if (multiple) {\n    input.multiple = multiple;\n  }\n\n  if (type !== undefined) {\n    input.type = type;\n  }\n\n  var renderProps = {\n    input: input,\n    meta: meta\n  }; // assign to force Flow check\n\n  return renderProps;\n}\n\nvar _excluded = [\"afterSubmit\", \"allowNull\", \"beforeSubmit\", \"children\", \"component\", \"data\", \"defaultValue\", \"format\", \"formatOnBlur\", \"initialValue\", \"isEqual\", \"multiple\", \"name\", \"parse\", \"subscription\", \"type\", \"validate\", \"validateFields\", \"value\"];\nvar Field = /*#__PURE__*/React.forwardRef(function Field(_ref, ref) {\n  var afterSubmit = _ref.afterSubmit,\n      allowNull = _ref.allowNull,\n      beforeSubmit = _ref.beforeSubmit,\n      children = _ref.children,\n      component = _ref.component,\n      data = _ref.data,\n      defaultValue = _ref.defaultValue,\n      format = _ref.format,\n      formatOnBlur = _ref.formatOnBlur,\n      initialValue = _ref.initialValue,\n      isEqual = _ref.isEqual,\n      multiple = _ref.multiple,\n      name = _ref.name,\n      parse = _ref.parse,\n      subscription = _ref.subscription,\n      type = _ref.type,\n      validate = _ref.validate,\n      validateFields = _ref.validateFields,\n      value = _ref.value,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  var field = useField(name, {\n    afterSubmit: afterSubmit,\n    allowNull: allowNull,\n    beforeSubmit: beforeSubmit,\n    children: children,\n    component: component,\n    data: data,\n    defaultValue: defaultValue,\n    format: format,\n    formatOnBlur: formatOnBlur,\n    initialValue: initialValue,\n    isEqual: isEqual,\n    multiple: multiple,\n    parse: parse,\n    subscription: subscription,\n    type: type,\n    validate: validate,\n    validateFields: validateFields,\n    value: value\n  });\n\n  if (typeof children === \"function\") {\n    return children(_extends({}, field, rest));\n  }\n\n  if (typeof component === \"string\") {\n    // ignore meta, combine input with any other props\n    return /*#__PURE__*/React.createElement(component, _extends({}, field.input, {\n      children: children,\n      ref: ref\n    }, rest));\n  }\n\n  if (!name) {\n    throw new Error(\"prop name cannot be undefined in <Field> component\");\n  }\n\n  return renderComponent(_extends({\n    children: children,\n    component: component,\n    ref: ref\n  }, rest), field, \"Field(\" + name + \")\");\n});\n\nfunction withTypes() {\n  return {\n    Form: ReactFinalForm,\n    FormSpy: FormSpy\n  };\n}\n\nexport { Field, ReactFinalForm as Form, FormSpy, useField, useForm, useFormState, version, withTypes };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,OAAO;AAClC,SAASC,qBAAqB,EAAEC,UAAU,EAAEC,OAAO,IAAIC,SAAS,EAAEC,sBAAsB,QAAQ,YAAY;AAE5G,IAAIC,WAAW,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC;AACrD;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAE;EAC/C,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,IAAI,GAAGhB,6BAA6B,CAACU,KAAK,EAAEF,WAAW,CAAC;EAE5D,IAAIO,SAAS,EAAE;IACb,OAAO,aAAad,KAAK,CAACgB,aAAa,CAACF,SAAS,EAAEG,MAAM,CAACC,MAAM,CAACR,SAAS,EAAEK,IAAI,EAAE;MAChFF,QAAQ,EAAEA,QAAQ;MAClBD,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC;EACL;EAEA,IAAIA,MAAM,EAAE;IACV,OAAOA,MAAM,CAACC,QAAQ,KAAKM,SAAS,GAAGF,MAAM,CAACC,MAAM,CAACR,SAAS,EAAEK,IAAI,CAAC;IAAG;IACxEE,MAAM,CAACC,MAAM,CAACR,SAAS,EAAEK,IAAI,EAAE;MAC7BF,QAAQ,EAAEA;IACZ,CAAC,CAAC,CAAC;EACL;EAEA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAClC,MAAM,IAAIO,KAAK,CAAC,2FAA2F,GAAGT,IAAI,CAAC;EACrH;EAEA,OAAOE,QAAQ,CAACI,MAAM,CAACC,MAAM,CAACR,SAAS,EAAEK,IAAI,CAAC,CAAC;AACjD;AAEA,SAASM,mBAAmBA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;MAC/B,OAAOD,CAAC,KAAKC,CAAC;IAChB,CAAC;EACH;EAEA,IAAIC,QAAQ,GAAG1B,cAAc,CAAC2B,MAAM,CAACN,KAAK,CAAC;EAC3CrB,cAAc,CAAC4B,SAAS,CAAC,YAAY;IACnC,IAAI,CAACL,OAAO,CAACF,KAAK,EAAEK,QAAQ,CAACG,OAAO,CAAC,EAAE;MACrCP,QAAQ,CAAC,CAAC;MACVI,QAAQ,CAACG,OAAO,GAAGR,KAAK;IAC1B;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASS,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,GAAG,GAAGhC,cAAc,CAAC2B,MAAM,CAAC,CAAC;EAEjC,IAAI,CAACK,GAAG,CAACH,OAAO,EAAE;IAChBG,GAAG,CAACH,OAAO,GAAGE,IAAI,CAAC,CAAC;EACtB;EAEA,OAAOC,GAAG,CAACH,OAAO;AACpB;AAEA,IAAII,YAAY,GAAG,SAASA,YAAYA,CAACT,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EAEA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,IAAI,OAAOC,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,EAAE;IAC9D,OAAO,KAAK;EACd;EAEA,IAAIS,KAAK,GAAGlB,MAAM,CAACmB,IAAI,CAACX,CAAC,CAAC;EAC1B,IAAIY,KAAK,GAAGpB,MAAM,CAACmB,IAAI,CAACV,CAAC,CAAC;EAE1B,IAAIS,KAAK,CAACG,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,IAAIC,eAAe,GAAGtB,MAAM,CAACuB,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChB,CAAC,CAAC;EAE7D,KAAK,IAAIiB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,KAAK,CAACG,MAAM,EAAEK,GAAG,EAAE,EAAE;IAC3C,IAAIC,GAAG,GAAGT,KAAK,CAACQ,GAAG,CAAC;IAEpB,IAAI,CAACJ,eAAe,CAACK,GAAG,CAAC,IAAInB,CAAC,CAACmB,GAAG,CAAC,KAAKlB,CAAC,CAACkB,GAAG,CAAC,EAAE;MAC9C,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,SAAS,EAAE;EAC1D,OAAO,CAAC,EAAEA,SAAS,IAAI,OAAOA,SAAS,CAACC,eAAe,KAAK,UAAU,CAAC;AACzE,CAAC;AAED,IAAIC,qBAAqB,GAAG,aAAahD,KAAK,CAACiD,aAAa,CAAC,CAAC;AAE9D,SAASC,SAASA,CAAC5B,KAAK,EAAE;EACxB,IAAIW,GAAG,GAAGhC,cAAc,CAAC2B,MAAM,CAACN,KAAK,CAAC;EACtCrB,cAAc,CAAC4B,SAAS,CAAC,YAAY;IACnCI,GAAG,CAACH,OAAO,GAAGR,KAAK;EACrB,CAAC,CAAC;EACF,OAAOW,GAAG;AACZ;AAEA,IAAI7B,OAAO,GAAG,OAAO;AAErB,IAAI+C,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAEjB,IAAI,EAAE;EAC1DA,IAAI,CAACkB,OAAO,CAAC,UAAUV,GAAG,EAAE;IAC1B3B,MAAM,CAACsC,cAAc,CAACH,IAAI,EAAER,GAAG,EAAE;MAC/BY,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB,OAAOH,KAAK,CAACT,GAAG,CAAC;MACnB,CAAC;MACDa,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACN,IAAI,EAAEC,KAAK,EAAE;EAC5D,OAAOF,YAAY,CAACC,IAAI,EAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,sBAAsB,EAAE,4BAA4B,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,yBAAyB,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC/Y,CAAC;AACD,IAAIM,qBAAqB,GAAG,SAASA,qBAAqBA,CAACP,IAAI,EAAEC,KAAK,EAAE;EACtE,OAAOF,YAAY,CAACC,IAAI,EAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,yBAAyB,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,iBAAiB,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AAChS,CAAC;AAED,IAAIO,WAAW,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,qBAAqB,EAAE,MAAM,EAAE,eAAe,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,gBAAgB,CAAC;AAChN,IAAIC,QAAQ,GAAG;EACb,YAAY,EAAExD,SAAS;EACvB,kBAAkB,EAAED;AACtB,CAAC;AACD,IAAI0D,KAAK,GAAG5D,qBAAqB,CAAC6D,MAAM,CAAC,UAAUC,MAAM,EAAEpB,GAAG,EAAE;EAC9DoB,MAAM,CAACpB,GAAG,CAAC,GAAG,IAAI;EAClB,OAAOoB,MAAM;AACf,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC5B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,eAAe,GAAGF,IAAI,CAACG,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;IAC9DE,mBAAmB,GAAGJ,IAAI,CAACI,mBAAmB;IAC9CC,gBAAgB,GAAGL,IAAI,CAACM,IAAI;IAC5BC,aAAa,GAAGP,IAAI,CAACO,aAAa;IAClCC,kBAAkB,GAAGR,IAAI,CAACQ,kBAAkB;IAC5CC,uBAAuB,GAAGT,IAAI,CAACS,uBAAuB;IACtDC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;IACxBC,QAAQ,GAAGX,IAAI,CAACW,QAAQ;IACxBC,iBAAiB,GAAGZ,IAAI,CAACa,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGhB,KAAK,GAAGgB,iBAAiB;IACvEE,QAAQ,GAAGd,IAAI,CAACc,QAAQ;IACxBC,cAAc,GAAGf,IAAI,CAACe,cAAc;IACpClE,IAAI,GAAGhB,6BAA6B,CAACmE,IAAI,EAAEN,WAAW,CAAC;EAE3D,IAAIsB,MAAM,GAAG;IACXf,KAAK,EAAEA,KAAK;IACZG,mBAAmB,EAAEA,mBAAmB;IACxCG,aAAa,EAAEA,aAAa;IAC5BE,uBAAuB,EAAEA,uBAAuB;IAChDC,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA,QAAQ;IAClBG,QAAQ,EAAEA,QAAQ;IAClBC,cAAc,EAAEA;EAClB,CAAC;EACD,IAAIT,IAAI,GAAGzC,WAAW,CAAC,YAAY;IACjC,IAAIoD,CAAC,GAAGZ,gBAAgB,IAAIpE,UAAU,CAAC+E,MAAM,CAAC,CAAC,CAAC;;IAEhDC,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,OAAOD,CAAC;EACV,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIE,eAAe,GAAGrF,KAAK,CAACsF,QAAQ,CAAC,YAAY;MAC/C,IAAIC,YAAY,GAAG,CAAC,CAAC;MACrBf,IAAI,CAACgB,SAAS,CAAC,UAAUnC,KAAK,EAAE;QAC9BkC,YAAY,GAAGlC,KAAK;MACtB,CAAC,EAAE0B,YAAY,CAAC,CAAC,CAAC;MAClB,OAAOQ,YAAY;IACrB,CAAC,CAAC;IACElC,KAAK,GAAGgC,eAAe,CAAC,CAAC,CAAC;IAC1BI,QAAQ,GAAGJ,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC;;EAGA,IAAIK,QAAQ,GAAGxC,SAAS,CAACG,KAAK,CAAC;EAC/BrD,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAC1B;IACA2C,IAAI,CAACmB,kBAAkB,CAAC,CAAC,IAAInB,IAAI,CAACoB,gBAAgB,CAAC,CAAC;IACpD,IAAIC,eAAe,GAAG,CAACrB,IAAI,CAACgB,SAAS,CAAC,UAAUM,CAAC,EAAE;MACjD,IAAI,CAAC5D,YAAY,CAAC4D,CAAC,EAAEJ,QAAQ,CAAC5D,OAAO,CAAC,EAAE;QACtC2D,QAAQ,CAACK,CAAC,CAAC;MACb;IACF,CAAC,EAAEf,YAAY,CAAC,CAAC,CAACgB,MAAM,CAAC1B,UAAU,GAAGA,UAAU,CAAC2B,GAAG,CAAC,UAAUC,SAAS,EAAE;MACxE;QAAQ;QACN;QACAA,SAAS,CAACzB,IAAI;MAAC;IAEnB,CAAC,CAAC,GAAG,EAAE,CAAC;IACR,OAAO,YAAY;MACjBA,IAAI,CAACY,eAAe,CAAC,CAAC,CAAC,CAAC;;MAExBS,eAAe,CAACK,OAAO,CAAC,CAAC,CAAC5C,OAAO,CAAC,UAAU6C,WAAW,EAAE;QACvD,OAAOA,WAAW,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;EACL,CAAC,EAAE9B,UAAU,CAAC,CAAC,CAAC;EAChB;;EAEA,IAAI+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACA;IACA;IACAjF,mBAAmB,CAACgD,UAAU,EAAE,YAAY;MAC1CkC,OAAO,CAACC,KAAK,CAAC,6FAA6F,CAAC;IAC9G,CAAC,EAAEtE,YAAY,CAAC;EAClB,CAAC,CAAC;;EAGFb,mBAAmB,CAAC8C,KAAK,EAAE,YAAY;IACrCK,IAAI,CAACiC,SAAS,CAAC,OAAO,EAAEtC,KAAK,CAAC;EAChC,CAAC,CAAC;EACF9C,mBAAmB,CAACiD,mBAAmB,EAAE,YAAY;IACnDE,IAAI,CAACF,mBAAmB,GAAG,CAAC,CAACA,mBAAmB;EAClD,CAAC,CAAC;EACFjD,mBAAmB,CAACsD,uBAAuB,EAAE,YAAY;IACvDH,IAAI,CAACiC,SAAS,CAAC,yBAAyB,EAAE9B,uBAAuB,CAAC;EACpE,CAAC,CAAC;EACFtD,mBAAmB,CAACoD,aAAa,EAAE,YAAY;IAC7CD,IAAI,CAACiC,SAAS,CAAC,eAAe,EAAEhC,aAAa,CAAC;EAChD,CAAC,EAAEC,kBAAkB,IAAIxC,YAAY,CAAC;EACtCb,mBAAmB,CAACuD,QAAQ,EAAE,YAAY;IACxCJ,IAAI,CAACiC,SAAS,CAAC,UAAU,EAAE7B,QAAQ,CAAC;EACtC,CAAC,CAAC;EACFvD,mBAAmB,CAACwD,QAAQ,EAAE,YAAY;IACxCL,IAAI,CAACiC,SAAS,CAAC,UAAU,EAAE5B,QAAQ,CAAC;EACtC,CAAC,CAAC;EACFxD,mBAAmB,CAAC2D,QAAQ,EAAE,YAAY;IACxCR,IAAI,CAACiC,SAAS,CAAC,UAAU,EAAEzB,QAAQ,CAAC;EACtC,CAAC,CAAC;EACF3D,mBAAmB,CAAC4D,cAAc,EAAE,YAAY;IAC9CT,IAAI,CAACiC,SAAS,CAAC,gBAAgB,EAAExB,cAAc,CAAC;EAClD,CAAC,CAAC;EAEF,IAAIyB,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC9C,IAAIA,KAAK,EAAE;MACT;MACA,IAAI,OAAOA,KAAK,CAACC,cAAc,KAAK,UAAU,EAAE;QAC9CD,KAAK,CAACC,cAAc,CAAC,CAAC;MACxB;MAEA,IAAI,OAAOD,KAAK,CAAC5D,eAAe,KAAK,UAAU,EAAE;QAC/C;QACA4D,KAAK,CAAC5D,eAAe,CAAC,CAAC;MACzB;IACF;IAEA,OAAOyB,IAAI,CAACqC,MAAM,CAAC,CAAC;EACtB,CAAC;EAED,IAAIC,WAAW,GAAG;IAChBtC,IAAI,EAAE1E,QAAQ,CAAC,CAAC,CAAC,EAAE0E,IAAI,EAAE;MACvBuC,KAAK,EAAE,SAASA,KAAKA,CAACC,aAAa,EAAE;QACnC,IAAInE,gBAAgB,CAACmE,aAAa,CAAC,EAAE;UACnC;UACAxC,IAAI,CAACuC,KAAK,CAAC,CAAC;QACd,CAAC,MAAM;UACLvC,IAAI,CAACuC,KAAK,CAACC,aAAa,CAAC;QAC3B;MACF;IACF,CAAC,CAAC;IACFN,YAAY,EAAEA;EAChB,CAAC;EACDhD,gBAAgB,CAACoD,WAAW,EAAEzD,KAAK,CAAC;EACpC,OAAO,aAAarD,KAAK,CAACgB,aAAa,CAACgC,qBAAqB,CAACiE,QAAQ,EAAE;IACtE3F,KAAK,EAAEkD;EACT,CAAC,EAAEhE,eAAe,CAACV,QAAQ,CAAC,CAAC,CAAC,EAAEiB,IAAI,EAAE;IACpCmG,UAAU,EAAErD;EACd,CAAC,CAAC,EAAEiD,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACrC;AAEA,SAASK,OAAOA,CAACC,aAAa,EAAE;EAC9B,IAAI5C,IAAI,GAAGxE,KAAK,CAACqH,UAAU,CAACrE,qBAAqB,CAAC;EAElD,IAAI,CAACwB,IAAI,EAAE;IACT,MAAM,IAAIpD,KAAK,CAAC,CAACgG,aAAa,IAAI,SAAS,IAAI,4CAA4C,CAAC;EAC9F;EAEA,OAAO5C,IAAI;AACb;AAEA,SAAS8C,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIrD,IAAI,GAAGqD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;IACpCC,QAAQ,GAAGtD,IAAI,CAACsD,QAAQ;IACxB1C,iBAAiB,GAAGZ,IAAI,CAACa,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGhB,KAAK,GAAGgB,iBAAiB;EAE3E,IAAIN,IAAI,GAAG2C,OAAO,CAAC,cAAc,CAAC;EAClC,IAAIM,WAAW,GAAGzH,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EACpC,IAAI8F,WAAW,GAAG1H,KAAK,CAAC4B,MAAM,CAAC4F,QAAQ,CAAC;EACxCE,WAAW,CAAC5F,OAAO,GAAG0F,QAAQ,CAAC,CAAC;;EAEhC,IAAInC,eAAe,GAAGrF,KAAK,CAACsF,QAAQ,CAAC,YAAY;MAC/C,IAAIC,YAAY,GAAG,CAAC,CAAC;MACrBf,IAAI,CAACgB,SAAS,CAAC,UAAUnC,KAAK,EAAE;QAC9BkC,YAAY,GAAGlC,KAAK;MACtB,CAAC,EAAE0B,YAAY,CAAC,CAAC,CAAC;MAElB,IAAIyC,QAAQ,EAAE;QACZA,QAAQ,CAACjC,YAAY,CAAC;MACxB;MAEA,OAAOA,YAAY;IACrB,CAAC,CAAC;IACElC,KAAK,GAAGgC,eAAe,CAAC,CAAC,CAAC;IAC1BI,QAAQ,GAAGJ,eAAe,CAAC,CAAC,CAAC;EAEjCrF,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAC1B,OAAO2C,IAAI,CAACgB,SAAS,CAAC,UAAUmC,QAAQ,EAAE;MACxC,IAAIF,WAAW,CAAC3F,OAAO,EAAE;QACvB2F,WAAW,CAAC3F,OAAO,GAAG,KAAK;MAC7B,CAAC,MAAM;QACL2D,QAAQ,CAACkC,QAAQ,CAAC;QAElB,IAAID,WAAW,CAAC5F,OAAO,EAAE;UACvB4F,WAAW,CAAC5F,OAAO,CAAC6F,QAAQ,CAAC;QAC/B;MACF;IACF,CAAC,EAAE5C,YAAY,CAAC;EAClB,CAAC;EAAE;EACH,EAAE,CAAC;EACH,IAAI6C,SAAS,GAAG,CAAC,CAAC;EAClBlE,gBAAgB,CAACkE,SAAS,EAAEvE,KAAK,CAAC;EAClC,OAAOuE,SAAS;AAClB;AAEA,IAAIC,WAAW,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC;AAE9C,SAASC,OAAOA,CAAC5D,IAAI,EAAE;EACrB,IAAIsD,QAAQ,GAAGtD,IAAI,CAACsD,QAAQ;IACxBzC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChChE,IAAI,GAAGhB,6BAA6B,CAACmE,IAAI,EAAE2D,WAAW,CAAC;EAE3D,IAAIE,cAAc,GAAGZ,OAAO,CAAC,SAAS,CAAC;EACvC,IAAI9D,KAAK,GAAGiE,YAAY,CAAC;IACvBE,QAAQ,EAAEA,QAAQ;IAClBzC,YAAY,EAAEA;EAChB,CAAC,CAAC;EAEF,IAAIyC,QAAQ,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,IAAIV,WAAW,GAAG;IAChBtC,IAAI,EAAE1E,QAAQ,CAAC,CAAC,CAAC,EAAEiI,cAAc,EAAE;MACjChB,KAAK,EAAE,SAASA,KAAKA,CAACC,aAAa,EAAE;QACnC,IAAInE,gBAAgB,CAACmE,aAAa,CAAC,EAAE;UACnC;UACAe,cAAc,CAAChB,KAAK,CAAC,CAAC;QACxB,CAAC,MAAM;UACLgB,cAAc,CAAChB,KAAK,CAACC,aAAa,CAAC;QACrC;MACF;IACF,CAAC;EACH,CAAC;EACD,OAAOxG,eAAe,CAACV,QAAQ,CAAC,CAAC,CAAC,EAAEiB,IAAI,EAAE+F,WAAW,CAAC,EAAEzD,KAAK,EAAE,SAAS,CAAC;AAC3E;AAEA,IAAI2E,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,CAACC,OAAO,IAAIF,MAAM,CAACC,SAAS,CAACC,OAAO,KAAK,aAAa;AAE/I,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,OAAO,EAAE;EAC1D,IAAIrE,MAAM,GAAG,EAAE;EAEf,IAAIqE,OAAO,EAAE;IACX,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,OAAO,CAAC/F,MAAM,EAAEgG,KAAK,EAAE,EAAE;MACnD,IAAIC,MAAM,GAAGF,OAAO,CAACC,KAAK,CAAC;MAE3B,IAAIC,MAAM,CAACC,QAAQ,EAAE;QACnBxE,MAAM,CAACyE,IAAI,CAACF,MAAM,CAACjH,KAAK,CAAC;MAC3B;IACF;EACF;EAEA,OAAO0C,MAAM;AACf,CAAC;AAED,IAAI0E,QAAQ,GAAG,SAASA,QAAQA,CAAC/B,KAAK,EAAEgC,YAAY,EAAEC,SAAS,EAAEZ,aAAa,EAAE;EAC9E,IAAI,CAACA,aAAa,IAAIrB,KAAK,CAACkC,WAAW,IAAIlC,KAAK,CAACkC,WAAW,CAACC,IAAI,KAAK3H,SAAS,EAAE;IAC/E,OAAOwF,KAAK,CAACkC,WAAW,CAACC,IAAI;EAC/B;EAEA,IAAId,aAAa,IAAIrB,KAAK,CAACkC,WAAW,EAAE;IACtC,OAAOlC,KAAK,CAACkC,WAAW,CAACC,IAAI;EAC/B;EAEA,IAAIC,YAAY,GAAGpC,KAAK;EACxB,IAAIqC,oBAAoB,GAAGD,YAAY,CAACE,MAAM;IAC1CC,IAAI,GAAGF,oBAAoB,CAACE,IAAI;IAChC5H,KAAK,GAAG0H,oBAAoB,CAAC1H,KAAK;IAClC6H,OAAO,GAAGH,oBAAoB,CAACG,OAAO;EAE1C,QAAQD,IAAI;IACV,KAAK,UAAU;MACb,IAAIN,SAAS,KAAKzH,SAAS,EAAE;QAC3B;QACA,IAAIgI,OAAO,EAAE;UACX;UACA,OAAOC,KAAK,CAACC,OAAO,CAACV,YAAY,CAAC,GAAGA,YAAY,CAAC5C,MAAM,CAAC6C,SAAS,CAAC,GAAG,CAACA,SAAS,CAAC;QACnF,CAAC,MAAM;UACL;UACA,IAAI,CAACQ,KAAK,CAACC,OAAO,CAACV,YAAY,CAAC,EAAE;YAChC,OAAOA,YAAY;UACrB;UAEA,IAAIL,KAAK,GAAGK,YAAY,CAACW,OAAO,CAACV,SAAS,CAAC;UAE3C,IAAIN,KAAK,GAAG,CAAC,EAAE;YACb,OAAOK,YAAY;UACrB,CAAC,MAAM;YACL,OAAOA,YAAY,CAACY,KAAK,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAACvC,MAAM,CAAC4C,YAAY,CAACY,KAAK,CAACjB,KAAK,GAAG,CAAC,CAAC,CAAC;UAC3E;QACF;MACF,CAAC,MAAM;QACL;QACA,OAAO,CAAC,CAACa,OAAO;MAClB;IAEF,KAAK,iBAAiB;MACpB,OAAOf,iBAAiB,CAACzB,KAAK,CAACsC,MAAM,CAACZ,OAAO,CAAC;IAEhD;MACE,OAAO/G,KAAK;EAChB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASkI,mBAAmBA,CAACjI,QAAQ,EAAE;EACrC,IAAIU,GAAG,GAAGjC,KAAK,CAAC4B,MAAM,CAACL,QAAQ,CAAC;EAChCvB,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAC1BI,GAAG,CAACH,OAAO,GAAGP,QAAQ;EACxB,CAAC,CAAC;EACF,OAAOvB,KAAK,CAACyJ,WAAW,CAAC,YAAY;IACnC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACrH,MAAM,EAAEsH,IAAI,GAAG,IAAIR,KAAK,CAACM,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;IAC9B;IAEA,OAAO5H,GAAG,CAACH,OAAO,CAACgI,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;AACR;AAEA,IAAIG,GAAG,GAAGzJ,sBAAsB,CAACyD,MAAM,CAAC,UAAUC,MAAM,EAAEpB,GAAG,EAAE;EAC7DoB,MAAM,CAACpB,GAAG,CAAC,GAAG,IAAI;EAClB,OAAOoB,MAAM;AACf,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,IAAIgG,aAAa,GAAG,SAASA,aAAaA,CAAC1I,KAAK,EAAEX,IAAI,EAAE;EACtD,OAAOW,KAAK,KAAKH,SAAS,GAAG,EAAE,GAAGG,KAAK;AACzC,CAAC;AAED,IAAI2I,YAAY,GAAG,SAASA,YAAYA,CAAC3I,KAAK,EAAEX,IAAI,EAAE;EACpD,OAAOW,KAAK,KAAK,EAAE,GAAGH,SAAS,GAAGG,KAAK;AACzC,CAAC;AAED,IAAI4I,cAAc,GAAG,SAASA,cAAcA,CAACzI,CAAC,EAAEC,CAAC,EAAE;EACjD,OAAOD,CAAC,KAAKC,CAAC;AAChB,CAAC;AAED,SAASyI,QAAQA,CAACxJ,IAAI,EAAEuE,MAAM,EAAE;EAC9B,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAG,CAAC,CAAC;EACb;EAEA,IAAIkF,OAAO,GAAGlF,MAAM;IAChBmF,WAAW,GAAGD,OAAO,CAACC,WAAW;IACjCC,SAAS,GAAGF,OAAO,CAACE,SAAS;IAC7BxJ,SAAS,GAAGsJ,OAAO,CAACtJ,SAAS;IAC7ByJ,IAAI,GAAGH,OAAO,CAACG,IAAI;IACnBC,YAAY,GAAGJ,OAAO,CAACI,YAAY;IACnCC,cAAc,GAAGL,OAAO,CAACM,MAAM;IAC/BA,MAAM,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGT,aAAa,GAAGS,cAAc;IACnEE,YAAY,GAAGP,OAAO,CAACO,YAAY;IACnCC,YAAY,GAAGR,OAAO,CAACQ,YAAY;IACnCC,QAAQ,GAAGT,OAAO,CAACS,QAAQ;IAC3BC,aAAa,GAAGV,OAAO,CAACW,KAAK;IAC7BA,KAAK,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGb,YAAY,GAAGa,aAAa;IAC/DE,oBAAoB,GAAGZ,OAAO,CAACrF,YAAY;IAC3CA,YAAY,GAAGiG,oBAAoB,KAAK,KAAK,CAAC,GAAGjB,GAAG,GAAGiB,oBAAoB;IAC3E9B,IAAI,GAAGkB,OAAO,CAAClB,IAAI;IACnB+B,cAAc,GAAGb,OAAO,CAACa,cAAc;IACvCC,MAAM,GAAGd,OAAO,CAAC9I,KAAK;EAC1B,IAAIkD,IAAI,GAAG2C,OAAO,CAAC,UAAU,CAAC;EAC9B,IAAIgE,SAAS,GAAGjI,SAAS,CAACgC,MAAM,CAAC;EAEjC,IAAIkG,QAAQ,GAAG,SAASA,QAAQA,CAAC7J,QAAQ,EAAE8J,MAAM,EAAE;IACjD;MAAQ;MACN;MACA;MACA;MACA;MACA7G,IAAI,CAAC8G,aAAa,CAAC3K,IAAI,EAAEY,QAAQ,EAAEwD,YAAY,EAAE;QAC/CsF,WAAW,EAAEA,WAAW;QACxBkB,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;UACpC,IAAIC,kBAAkB,GAAGL,SAAS,CAACrJ,OAAO;YACtCyJ,YAAY,GAAGC,kBAAkB,CAACD,YAAY;YAC9CZ,YAAY,GAAGa,kBAAkB,CAACb,YAAY;YAC9Cc,qBAAqB,GAAGD,kBAAkB,CAACd,MAAM;YACjDA,MAAM,GAAGe,qBAAqB,KAAK,KAAK,CAAC,GAAGzB,aAAa,GAAGyB,qBAAqB;UAErF,IAAId,YAAY,EAAE;YAChB,IAAIzG,IAAI,GAAGM,IAAI,CAACkH,aAAa,CAAC/K,IAAI,CAAC;cAC/BW,KAAK,GAAG4C,IAAI,CAAC5C,KAAK;YAEtB,IAAIqK,SAAS,GAAGjB,MAAM,CAACpJ,KAAK,EAAEX,IAAI,CAAC;YAEnC,IAAIgL,SAAS,KAAKrK,KAAK,EAAE;cACvBkD,IAAI,CAACoH,MAAM,CAACjL,IAAI,EAAEgL,SAAS,CAAC;YAC9B;UACF;UAEA,OAAOJ,YAAY,IAAIA,YAAY,CAAC,CAAC;QACvC,CAAC;QACDhB,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY;QAC1BqB,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;UACpC,OAAOV,SAAS,CAACrJ,OAAO,CAACkD,QAAQ;QACnC,CAAC;QACD4F,YAAY,EAAEA,YAAY;QAC1BpJ,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;UAC9B,OAAO,CAACyJ,SAAS,CAACrJ,OAAO,CAACN,OAAO,IAAI0I,cAAc,EAAEzI,CAAC,EAAEC,CAAC,CAAC;QAC5D,CAAC;QACD2J,MAAM,EAAEA,MAAM;QACdJ,cAAc,EAAEA;MAClB,CAAC;IAAC;EAEN,CAAC;EAED,IAAIxD,WAAW,GAAGzH,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtC,IAAIyD,eAAe,GAAGrF,KAAK,CAACsF,QAAQ,CAAC,YAAY;MAC/C,IAAIC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEvB;MACA,IAAIjB,mBAAmB,GAAGE,IAAI,CAACF,mBAAmB;MAClDE,IAAI,CAACF,mBAAmB,GAAG,KAAK;MAChC8G,QAAQ,CAAC,UAAU/H,KAAK,EAAE;QACxBkC,YAAY,GAAGlC,KAAK;MACtB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEZ;MACAmB,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB;MAC9C,OAAOiB,YAAY;IACrB,CAAC,CAAC;IACElC,KAAK,GAAGgC,eAAe,CAAC,CAAC,CAAC;IAC1BI,QAAQ,GAAGJ,eAAe,CAAC,CAAC,CAAC;EAEjCrF,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAC1B,OAAOuJ,QAAQ,CAAC,UAAU/H,KAAK,EAAE;MAC/B,IAAIoE,WAAW,CAAC3F,OAAO,EAAE;QACvB2F,WAAW,CAAC3F,OAAO,GAAG,KAAK;MAC7B,CAAC,MAAM;QACL2D,QAAQ,CAACpC,KAAK,CAAC;MACjB;IACF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAAE;EACH,CAAC1C,IAAI,EAAE4J,IAAI,EAAEC,YAAY;EAAE;EAC3B;EACA;EACAI,YAAY,CAAC;EACb;EACA;EACA;EAAA,CACC,CAAC;EACF,IAAIkB,IAAI,GAAG,CAAC,CAAC;EACbnI,qBAAqB,CAACmI,IAAI,EAAEzI,KAAK,CAAC;EAClC,IAAI0I,KAAK,GAAG;IACVpL,IAAI,EAAEA,IAAI;IAEV,IAAIW,KAAKA,CAAA,EAAG;MACV,IAAIA,KAAK,GAAG+B,KAAK,CAAC/B,KAAK;MAEvB,IAAIqJ,YAAY,EAAE;QAChB,IAAI7J,SAAS,KAAK,OAAO,EAAE;UACzBQ,KAAK,GAAG0I,aAAa,CAAC1I,KAAK,CAAC;QAC9B;MACF,CAAC,MAAM;QACLA,KAAK,GAAGoJ,MAAM,CAACpJ,KAAK,EAAEX,IAAI,CAAC;MAC7B;MAEA,IAAIW,KAAK,KAAK,IAAI,IAAI,CAACgJ,SAAS,EAAE;QAChChJ,KAAK,GAAG,EAAE;MACZ;MAEA,IAAI4H,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,EAAE;QAC3C,OAAOgC,MAAM;MACf,CAAC,MAAM,IAAIpK,SAAS,KAAK,QAAQ,IAAI+J,QAAQ,EAAE;QAC7C,OAAOvJ,KAAK,IAAI,EAAE;MACpB;MAEA,OAAOA,KAAK;IACd,CAAC;IAED,IAAI6H,OAAOA,CAAA,EAAG;MACZ,IAAI7H,KAAK,GAAG+B,KAAK,CAAC/B,KAAK;MAEvB,IAAI4H,IAAI,KAAK,UAAU,EAAE;QACvB5H,KAAK,GAAGoJ,MAAM,CAACpJ,KAAK,EAAEX,IAAI,CAAC;QAE3B,IAAIuK,MAAM,KAAK/J,SAAS,EAAE;UACxB,OAAO,CAAC,CAACG,KAAK;QAChB,CAAC,MAAM;UACL,OAAO,CAAC,EAAE8H,KAAK,CAACC,OAAO,CAAC/H,KAAK,CAAC,IAAI,CAACA,KAAK,CAACgI,OAAO,CAAC4B,MAAM,CAAC,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIhC,IAAI,KAAK,OAAO,EAAE;QAC3B,OAAOwB,MAAM,CAACpJ,KAAK,EAAEX,IAAI,CAAC,KAAKuK,MAAM;MACvC;MAEA,OAAO/J,SAAS;IAClB,CAAC;IAED6K,MAAM,EAAExC,mBAAmB,CAAC,UAAU7C,KAAK,EAAE;MAC3CtD,KAAK,CAAC4I,IAAI,CAAC,CAAC;MAEZ,IAAItB,YAAY,EAAE;QAChB;AACR;AACA;AACA;AACA;AACA;AACA;QACQ,IAAIuB,UAAU,GAAG1H,IAAI,CAACkH,aAAa,CAACrI,KAAK,CAAC1C,IAAI,CAAC;QAC/C0C,KAAK,CAACuI,MAAM,CAAClB,MAAM,CAACwB,UAAU,CAAC5K,KAAK,EAAE+B,KAAK,CAAC1C,IAAI,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IACF6G,QAAQ,EAAEgC,mBAAmB,CAAC,UAAU7C,KAAK,EAAE;MAC7C;MACA,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIK,KAAK,IAAIA,KAAK,CAACsC,MAAM,EAAE;QAClE,IAAIkD,UAAU,GAAGxF,KAAK,CAACsC,MAAM,CAACC,IAAI;QAClC,IAAIkD,OAAO,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC9C,OAAO,CAAC6C,UAAU,CAAC,IAAI,CAACjD,IAAI,IAAIpI,SAAS,KAAK,QAAQ;QAE9G,IAAIuL,OAAO,GAAGF,UAAU,KAAK,iBAAiB,GAAG9I,KAAK,CAAC/B,KAAK,GAAG4J,MAAM;QAErE,IAAIkB,OAAO,EAAE;UACX7F,OAAO,CAACC,KAAK,CAAC,wBAAwB,IAAI2F,UAAU,KAAK,iBAAiB,GAAG,QAAQ,GAAGA,UAAU,CAAC,GAAG,yBAAyB,GAAGxL,IAAI,GAAG,gBAAgB,IAAI,6DAA6D,IAAIyI,KAAK,CAACC,OAAO,CAACgD,OAAO,CAAC,GAAG,GAAG,GAAGA,OAAO,GAAG,GAAG,GAAG,IAAI,GAAGA,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAC7S;MACF;MAEA,IAAI/K,KAAK,GAAGqF,KAAK,IAAIA,KAAK,CAACsC,MAAM,GAAGP,QAAQ,CAAC/B,KAAK,EAAEtD,KAAK,CAAC/B,KAAK,EAAE4J,MAAM,EAAElD,aAAa,CAAC,GAAGrB,KAAK;MAC/FtD,KAAK,CAACuI,MAAM,CAACb,KAAK,CAACzJ,KAAK,EAAEX,IAAI,CAAC,CAAC;IAClC,CAAC,CAAC;IACF2L,OAAO,EAAE9C,mBAAmB,CAAC,UAAU7C,KAAK,EAAE;MAC5C,OAAOtD,KAAK,CAACkJ,KAAK,CAAC,CAAC;IACtB,CAAC;EACH,CAAC;EAED,IAAI1B,QAAQ,EAAE;IACZkB,KAAK,CAAClB,QAAQ,GAAGA,QAAQ;EAC3B;EAEA,IAAI3B,IAAI,KAAK/H,SAAS,EAAE;IACtB4K,KAAK,CAAC7C,IAAI,GAAGA,IAAI;EACnB;EAEA,IAAIpC,WAAW,GAAG;IAChBiF,KAAK,EAAEA,KAAK;IACZD,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC;;EAEH,OAAOhF,WAAW;AACpB;AAEA,IAAI0F,SAAS,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,CAAC;AAC9P,IAAIC,KAAK,GAAG,aAAazM,KAAK,CAAC0M,UAAU,CAAC,SAASD,KAAKA,CAACvI,IAAI,EAAEjC,GAAG,EAAE;EAClE,IAAIoI,WAAW,GAAGnG,IAAI,CAACmG,WAAW;IAC9BC,SAAS,GAAGpG,IAAI,CAACoG,SAAS;IAC1BiB,YAAY,GAAGrH,IAAI,CAACqH,YAAY;IAChC1K,QAAQ,GAAGqD,IAAI,CAACrD,QAAQ;IACxBC,SAAS,GAAGoD,IAAI,CAACpD,SAAS;IAC1ByJ,IAAI,GAAGrG,IAAI,CAACqG,IAAI;IAChBC,YAAY,GAAGtG,IAAI,CAACsG,YAAY;IAChCE,MAAM,GAAGxG,IAAI,CAACwG,MAAM;IACpBC,YAAY,GAAGzG,IAAI,CAACyG,YAAY;IAChCC,YAAY,GAAG1G,IAAI,CAAC0G,YAAY;IAChCpJ,OAAO,GAAG0C,IAAI,CAAC1C,OAAO;IACtBqJ,QAAQ,GAAG3G,IAAI,CAAC2G,QAAQ;IACxBlK,IAAI,GAAGuD,IAAI,CAACvD,IAAI;IAChBoK,KAAK,GAAG7G,IAAI,CAAC6G,KAAK;IAClBhG,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCmE,IAAI,GAAGhF,IAAI,CAACgF,IAAI;IAChBlE,QAAQ,GAAGd,IAAI,CAACc,QAAQ;IACxBiG,cAAc,GAAG/G,IAAI,CAAC+G,cAAc;IACpC3J,KAAK,GAAG4C,IAAI,CAAC5C,KAAK;IAClBP,IAAI,GAAGhB,6BAA6B,CAACmE,IAAI,EAAEsI,SAAS,CAAC;EAEzD,IAAIG,KAAK,GAAGxC,QAAQ,CAACxJ,IAAI,EAAE;IACzB0J,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA,SAAS;IACpBiB,YAAY,EAAEA,YAAY;IAC1B1K,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpByJ,IAAI,EAAEA,IAAI;IACVC,YAAY,EAAEA,YAAY;IAC1BE,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BpJ,OAAO,EAAEA,OAAO;IAChBqJ,QAAQ,EAAEA,QAAQ;IAClBE,KAAK,EAAEA,KAAK;IACZhG,YAAY,EAAEA,YAAY;IAC1BmE,IAAI,EAAEA,IAAI;IACVlE,QAAQ,EAAEA,QAAQ;IAClBiG,cAAc,EAAEA,cAAc;IAC9B3J,KAAK,EAAEA;EACT,CAAC,CAAC;EAEF,IAAI,OAAOT,QAAQ,KAAK,UAAU,EAAE;IAClC,OAAOA,QAAQ,CAACf,QAAQ,CAAC,CAAC,CAAC,EAAE6M,KAAK,EAAE5L,IAAI,CAAC,CAAC;EAC5C;EAEA,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;IACjC;IACA,OAAO,aAAad,KAAK,CAACgB,aAAa,CAACF,SAAS,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAE6M,KAAK,CAACZ,KAAK,EAAE;MAC3ElL,QAAQ,EAAEA,QAAQ;MAClBoB,GAAG,EAAEA;IACP,CAAC,EAAElB,IAAI,CAAC,CAAC;EACX;EAEA,IAAI,CAACJ,IAAI,EAAE;IACT,MAAM,IAAIS,KAAK,CAAC,oDAAoD,CAAC;EACvE;EAEA,OAAOZ,eAAe,CAACV,QAAQ,CAAC;IAC9Be,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBmB,GAAG,EAAEA;EACP,CAAC,EAAElB,IAAI,CAAC,EAAE4L,KAAK,EAAE,QAAQ,GAAGhM,IAAI,GAAG,GAAG,CAAC;AACzC,CAAC,CAAC;AAEF,SAASiM,SAASA,CAAA,EAAG;EACnB,OAAO;IACLC,IAAI,EAAE5I,cAAc;IACpB6D,OAAO,EAAEA;EACX,CAAC;AACH;AAEA,SAAS2E,KAAK,EAAExI,cAAc,IAAI4I,IAAI,EAAE/E,OAAO,EAAEqC,QAAQ,EAAEhD,OAAO,EAAEG,YAAY,EAAElH,OAAO,EAAEwM,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}