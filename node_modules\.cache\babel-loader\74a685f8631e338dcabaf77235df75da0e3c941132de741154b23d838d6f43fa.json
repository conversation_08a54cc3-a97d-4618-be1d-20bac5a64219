{"ast": null, "code": "/**\n* The function scrollTo() scrolls to the element with the id createUserAccountForm\n*/\n/* const scrollElement = document.getElementById(props.id); */\nexport function scrollTo(props) {\n  setTimeout(function () {\n    props.scrollElement.scrollIntoView({\n      behavior: 'smooth',\n      block: props.block,\n      inline: props.inline\n    });\n  }, 100);\n}", "map": {"version": 3, "names": ["scrollTo", "props", "setTimeout", "scrollElement", "scrollIntoView", "behavior", "block", "inline"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/scrollToElement.jsx"], "sourcesContent": ["/**\n* The function scrollTo() scrolls to the element with the id createUserAccountForm\n*/\n/* const scrollElement = document.getElementById(props.id); */\nexport function scrollTo(props) {\n    setTimeout(function () {\n        props.scrollElement.scrollIntoView({ behavior: 'smooth', block: props.block, inline: props.inline });\n    }, 100)\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC5BC,UAAU,CAAC,YAAY;IACnBD,KAAK,CAACE,aAAa,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,KAAK,EAAEL,KAAK,CAACK,KAAK;MAAEC,MAAM,EAAEN,KAAK,CAACM;IAAO,CAAC,CAAC;EACxG,CAAC,EAAE,GAAG,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}