{"ast": null, "code": "/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react'; // We will never use default, here only to fix TypeScript warning\n\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport var MentionsContextProvider = MentionsContext.Provider;\nexport var MentionsContextConsumer = MentionsContext.Consumer;", "map": {"version": 3, "names": ["React", "MentionsContext", "createContext", "MentionsContextProvider", "Provider", "MentionsContextConsumer", "Consumer"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-mentions/es/MentionsContext.js"], "sourcesContent": ["/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react'; // We will never use default, here only to fix TypeScript warning\n\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport var MentionsContextProvider = MentionsContext.Provider;\nexport var MentionsContextConsumer = MentionsContext.Consumer;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO,CAAC,CAAC;;AAEhC,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D,OAAO,IAAIC,uBAAuB,GAAGF,eAAe,CAACG,QAAQ;AAC7D,OAAO,IAAIC,uBAAuB,GAAGJ,eAAe,CAACK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}