{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\menuItem.jsx\";\nimport React from \"react\";\nimport { Menu, Dropdown } from \"antd\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default class MenuItem extends React.Component {\n  /* Per Utilizzare il componente è necessario passargli il props fields\n     Il fields deve essere un array di oggetto :[{name:\"nome dell,item\",handler:funzione associata all'item}]  */\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n\n  // {<i className=\"pi pi-ellipsis-h\" />}\n\n  render() {\n    const menu = /*#__PURE__*/_jsxDEV(Menu, {\n      children: this.props.fields.map((el, key) => !el.status ? /*#__PURE__*/_jsxDEV(Menu.Item, {\n        icon: el.icon,\n        onClick: () => el.handler(this.props.rowData),\n        children: el.name\n      }, key, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 25\n      }, this) : (el === null || el === void 0 ? void 0 : el.status) === this.props.rowData.status || (el === null || el === void 0 ? void 0 : el.status2) === this.props.rowData.status || (el === null || el === void 0 ? void 0 : el.status3) === this.props.rowData.status ? /*#__PURE__*/_jsxDEV(Menu.Item, {\n        icon: el.icon,\n        onClick: () => el.handler(this.props.rowData),\n        children: el.name\n      }, key, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 25\n      }, this) : null)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this);\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Dropdown.Button, {\n        trigger: \"click\",\n        overlay: menu,\n        placement: \"bottomRight\",\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"center\"\n          },\n          className: \"pi pi-ellipsis-h\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Dropdown", "jsxDEV", "_jsxDEV", "MenuItem", "Component", "constructor", "props", "state", "render", "menu", "children", "fields", "map", "el", "key", "status", "<PERSON><PERSON>", "icon", "onClick", "handler", "rowData", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "status2", "status3", "Fragment", "<PERSON><PERSON>", "trigger", "overlay", "placement", "style", "display", "justifyContent", "className"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/menuItem.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Menu, Dropdown } from \"antd\";\nexport default class MenuItem extends React.Component {\n    /* Per Utilizzare il componente è necessario passargli il props fields\n       Il fields deve essere un array di oggetto :[{name:\"nome dell,item\",handler:funzione associata all'item}]  */\n    constructor(props) {\n        super(props);\n        this.state = {};\n    }\n\n\n    // {<i className=\"pi pi-ellipsis-h\" />}\n\n    render() {\n        const menu = (\n            <Menu>\n                {this.props.fields.map((el, key) =>\n                    !el.status ? (\n                        <Menu.Item icon={el.icon} key={key} onClick={() => el.handler(this.props.rowData)}>\n                            {el.name}\n                        </Menu.Item>\n                    ) : el?.status === this.props.rowData.status || el?.status2 === this.props.rowData.status || el?.status3 === this.props.rowData.status? (\n                        <Menu.Item key={key} icon={el.icon} onClick={() => el.handler(this.props.rowData)}>\n                            {el.name}\n                        </Menu.Item>\n                    ) : null\n                )}\n            </Menu>\n        );\n        return (\n            <React.Fragment>\n                <Dropdown.Button\n                    trigger=\"click\"\n                    overlay={menu}\n                    placement=\"bottomRight\"\n                    icon={\n                        <i\n                            style={{ display: \"flex\", justifyContent: \"center\" }}\n                            className=\"pi pi-ellipsis-h\"\n                        />\n                    }\n                ></Dropdown.Button>\n            </React.Fragment>\n        );\n    }\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,QAAQ,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACtC,eAAe,MAAMC,QAAQ,SAASL,KAAK,CAACM,SAAS,CAAC;EAClD;AACJ;EACIC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;;EAGA;;EAEAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,gBACNP,OAAA,CAACH,IAAI;MAAAW,QAAA,EACA,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,GAAG,CAAC,CAACC,EAAE,EAAEC,GAAG,KAC3B,CAACD,EAAE,CAACE,MAAM,gBACNb,OAAA,CAACH,IAAI,CAACiB,IAAI;QAACC,IAAI,EAAEJ,EAAE,CAACI,IAAK;QAAWC,OAAO,EAAEA,CAAA,KAAML,EAAE,CAACM,OAAO,CAAC,IAAI,CAACb,KAAK,CAACc,OAAO,CAAE;QAAAV,QAAA,EAC7EG,EAAE,CAACQ;MAAI,GADmBP,GAAG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEvB,CAAC,GACZ,CAAAZ,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAEE,MAAM,MAAK,IAAI,CAACT,KAAK,CAACc,OAAO,CAACL,MAAM,IAAI,CAAAF,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAEa,OAAO,MAAK,IAAI,CAACpB,KAAK,CAACc,OAAO,CAACL,MAAM,IAAI,CAAAF,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAEc,OAAO,MAAK,IAAI,CAACrB,KAAK,CAACc,OAAO,CAACL,MAAM,gBAClIb,OAAA,CAACH,IAAI,CAACiB,IAAI;QAAWC,IAAI,EAAEJ,EAAE,CAACI,IAAK;QAACC,OAAO,EAAEA,CAAA,KAAML,EAAE,CAACM,OAAO,CAAC,IAAI,CAACb,KAAK,CAACc,OAAO,CAAE;QAAAV,QAAA,EAC7EG,EAAE,CAACQ;MAAI,GADIP,GAAG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,GACZ,IACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACT;IACD,oBACIvB,OAAA,CAACJ,KAAK,CAAC8B,QAAQ;MAAAlB,QAAA,eACXR,OAAA,CAACF,QAAQ,CAAC6B,MAAM;QACZC,OAAO,EAAC,OAAO;QACfC,OAAO,EAAEtB,IAAK;QACduB,SAAS,EAAC,aAAa;QACvBf,IAAI,eACAf,OAAA;UACI+B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UACrDC,SAAS,EAAC;QAAkB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEzB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}