export default ResourceBudget;
export type ResourceEntry = import('../computed/resource-summary.js').ResourceEntry;
export type BudgetItem = {
    resourceType: LH.Budget.ResourceType;
    label: LH.IcuMessage;
    requestCount: number;
    transferSize: number;
    sizeOverBudget: number | undefined;
    countOverBudget: LH.IcuMessage | undefined;
};
/** @typedef {import('../computed/resource-summary.js').ResourceEntry} ResourceEntry */
/** @typedef {{resourceType: LH.Budget.ResourceType, label: LH.IcuMessage, requestCount: number, transferSize: number, sizeOverBudget: number | undefined, countOverBudget: LH.IcuMessage | undefined}} BudgetItem */
declare class ResourceBudget extends Audit {
    /**
     * @param {LH.Budget.ResourceType} resourceType
     * @return {string}
     */
    static getRowLabel(resourceType: LH.Budget.ResourceType): string;
    /**
     * @param {LH.Util.Immutable<LH.Budget>} budget
     * @param {Record<LH.Budget.ResourceType, ResourceEntry>} summary
     * @return {Array<BudgetItem>}
     */
    static tableItems(budget: LH.Util.Immutable<LH.Budget>, summary: Record<LH.Budget.ResourceType, ResourceEntry>): Array<BudgetItem>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const requestCountOverBudget: string;
}
import { Budget } from "../config/budget.js";
import { Audit } from "./audit.js";
//# sourceMappingURL=performance-budget.d.ts.map