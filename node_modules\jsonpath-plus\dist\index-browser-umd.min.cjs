!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).JSONPath={})}(this,(function(e){"use strict";class t{static get version(){return"1.4.0"}static toString(){return"JavaScript Expression Parser (JSEP) v"+t.version}static addUnaryOp(e){return t.max_unop_len=Math.max(e.length,t.max_unop_len),t.unary_ops[e]=1,t}static addBinaryOp(e,r,s){return t.max_binop_len=Math.max(e.length,t.max_binop_len),t.binary_ops[e]=r,s?t.right_associative.add(e):t.right_associative.delete(e),t}static addIdentifierChar(e){return t.additional_identifier_chars.add(e),t}static addLiteral(e,r){return t.literals[e]=r,t}static removeUnaryOp(e){return delete t.unary_ops[e],e.length===t.max_unop_len&&(t.max_unop_len=t.getMaxKeyLen(t.unary_ops)),t}static removeAllUnaryOps(){return t.unary_ops={},t.max_unop_len=0,t}static removeIdentifierChar(e){return t.additional_identifier_chars.delete(e),t}static removeBinaryOp(e){return delete t.binary_ops[e],e.length===t.max_binop_len&&(t.max_binop_len=t.getMaxKeyLen(t.binary_ops)),t.right_associative.delete(e),t}static removeAllBinaryOps(){return t.binary_ops={},t.max_binop_len=0,t}static removeLiteral(e){return delete t.literals[e],t}static removeAllLiterals(){return t.literals={},t}get char(){return this.expr.charAt(this.index)}get code(){return this.expr.charCodeAt(this.index)}constructor(e){this.expr=e,this.index=0}static parse(e){return new t(e).parse()}static getMaxKeyLen(e){return Math.max(0,...Object.keys(e).map((e=>e.length)))}static isDecimalDigit(e){return e>=48&&e<=57}static binaryPrecedence(e){return t.binary_ops[e]||0}static isIdentifierStart(e){return e>=65&&e<=90||e>=97&&e<=122||e>=128&&!t.binary_ops[String.fromCharCode(e)]||t.additional_identifier_chars.has(String.fromCharCode(e))}static isIdentifierPart(e){return t.isIdentifierStart(e)||t.isDecimalDigit(e)}throwError(e){const t=new Error(e+" at character "+this.index);throw t.index=this.index,t.description=e,t}runHook(e,r){if(t.hooks[e]){const s={context:this,node:r};return t.hooks.run(e,s),s.node}return r}searchHook(e){if(t.hooks[e]){const r={context:this};return t.hooks[e].find((function(e){return e.call(r.context,r),r.node})),r.node}}gobbleSpaces(){let e=this.code;for(;e===t.SPACE_CODE||e===t.TAB_CODE||e===t.LF_CODE||e===t.CR_CODE;)e=this.expr.charCodeAt(++this.index);this.runHook("gobble-spaces")}parse(){this.runHook("before-all");const e=this.gobbleExpressions(),r=1===e.length?e[0]:{type:t.COMPOUND,body:e};return this.runHook("after-all",r)}gobbleExpressions(e){let r,s,n=[];for(;this.index<this.expr.length;)if(r=this.code,r===t.SEMCOL_CODE||r===t.COMMA_CODE)this.index++;else if(s=this.gobbleExpression())n.push(s);else if(this.index<this.expr.length){if(r===e)break;this.throwError('Unexpected "'+this.char+'"')}return n}gobbleExpression(){const e=this.searchHook("gobble-expression")||this.gobbleBinaryExpression();return this.gobbleSpaces(),this.runHook("after-expression",e)}gobbleBinaryOp(){this.gobbleSpaces();let e=this.expr.substr(this.index,t.max_binop_len),r=e.length;for(;r>0;){if(t.binary_ops.hasOwnProperty(e)&&(!t.isIdentifierStart(this.code)||this.index+e.length<this.expr.length&&!t.isIdentifierPart(this.expr.charCodeAt(this.index+e.length))))return this.index+=r,e;e=e.substr(0,--r)}return!1}gobbleBinaryExpression(){let e,r,s,n,i,o,a,h,l;if(o=this.gobbleToken(),!o)return o;if(r=this.gobbleBinaryOp(),!r)return o;for(i={value:r,prec:t.binaryPrecedence(r),right_a:t.right_associative.has(r)},a=this.gobbleToken(),a||this.throwError("Expected expression after "+r),n=[o,i,a];r=this.gobbleBinaryOp();){if(s=t.binaryPrecedence(r),0===s){this.index-=r.length;break}i={value:r,prec:s,right_a:t.right_associative.has(r)},l=r;const h=e=>i.right_a&&e.right_a?s>e.prec:s<=e.prec;for(;n.length>2&&h(n[n.length-2]);)a=n.pop(),r=n.pop().value,o=n.pop(),e={type:t.BINARY_EXP,operator:r,left:o,right:a},n.push(e);e=this.gobbleToken(),e||this.throwError("Expected expression after "+l),n.push(i,e)}for(h=n.length-1,e=n[h];h>1;)e={type:t.BINARY_EXP,operator:n[h-1].value,left:n[h-2],right:e},h-=2;return e}gobbleToken(){let e,r,s,n;if(this.gobbleSpaces(),n=this.searchHook("gobble-token"),n)return this.runHook("after-token",n);if(e=this.code,t.isDecimalDigit(e)||e===t.PERIOD_CODE)return this.gobbleNumericLiteral();if(e===t.SQUOTE_CODE||e===t.DQUOTE_CODE)n=this.gobbleStringLiteral();else if(e===t.OBRACK_CODE)n=this.gobbleArray();else{for(r=this.expr.substr(this.index,t.max_unop_len),s=r.length;s>0;){if(t.unary_ops.hasOwnProperty(r)&&(!t.isIdentifierStart(this.code)||this.index+r.length<this.expr.length&&!t.isIdentifierPart(this.expr.charCodeAt(this.index+r.length)))){this.index+=s;const e=this.gobbleToken();return e||this.throwError("missing unaryOp argument"),this.runHook("after-token",{type:t.UNARY_EXP,operator:r,argument:e,prefix:!0})}r=r.substr(0,--s)}t.isIdentifierStart(e)?(n=this.gobbleIdentifier(),t.literals.hasOwnProperty(n.name)?n={type:t.LITERAL,value:t.literals[n.name],raw:n.name}:n.name===t.this_str&&(n={type:t.THIS_EXP})):e===t.OPAREN_CODE&&(n=this.gobbleGroup())}return n?(n=this.gobbleTokenProperty(n),this.runHook("after-token",n)):this.runHook("after-token",!1)}gobbleTokenProperty(e){this.gobbleSpaces();let r=this.code;for(;r===t.PERIOD_CODE||r===t.OBRACK_CODE||r===t.OPAREN_CODE||r===t.QUMARK_CODE;){let s;if(r===t.QUMARK_CODE){if(this.expr.charCodeAt(this.index+1)!==t.PERIOD_CODE)break;s=!0,this.index+=2,this.gobbleSpaces(),r=this.code}this.index++,r===t.OBRACK_CODE?((e={type:t.MEMBER_EXP,computed:!0,object:e,property:this.gobbleExpression()}).property||this.throwError('Unexpected "'+this.char+'"'),this.gobbleSpaces(),r=this.code,r!==t.CBRACK_CODE&&this.throwError("Unclosed ["),this.index++):r===t.OPAREN_CODE?e={type:t.CALL_EXP,arguments:this.gobbleArguments(t.CPAREN_CODE),callee:e}:(r===t.PERIOD_CODE||s)&&(s&&this.index--,this.gobbleSpaces(),e={type:t.MEMBER_EXP,computed:!1,object:e,property:this.gobbleIdentifier()}),s&&(e.optional=!0),this.gobbleSpaces(),r=this.code}return e}gobbleNumericLiteral(){let e,r,s="";for(;t.isDecimalDigit(this.code);)s+=this.expr.charAt(this.index++);if(this.code===t.PERIOD_CODE)for(s+=this.expr.charAt(this.index++);t.isDecimalDigit(this.code);)s+=this.expr.charAt(this.index++);if(e=this.char,"e"===e||"E"===e){for(s+=this.expr.charAt(this.index++),e=this.char,"+"!==e&&"-"!==e||(s+=this.expr.charAt(this.index++));t.isDecimalDigit(this.code);)s+=this.expr.charAt(this.index++);t.isDecimalDigit(this.expr.charCodeAt(this.index-1))||this.throwError("Expected exponent ("+s+this.char+")")}return r=this.code,t.isIdentifierStart(r)?this.throwError("Variable names cannot start with a number ("+s+this.char+")"):(r===t.PERIOD_CODE||1===s.length&&s.charCodeAt(0)===t.PERIOD_CODE)&&this.throwError("Unexpected period"),{type:t.LITERAL,value:parseFloat(s),raw:s}}gobbleStringLiteral(){let e="";const r=this.index,s=this.expr.charAt(this.index++);let n=!1;for(;this.index<this.expr.length;){let t=this.expr.charAt(this.index++);if(t===s){n=!0;break}if("\\"===t)switch(t=this.expr.charAt(this.index++),t){case"n":e+="\n";break;case"r":e+="\r";break;case"t":e+="\t";break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:e+=t}else e+=t}return n||this.throwError('Unclosed quote after "'+e+'"'),{type:t.LITERAL,value:e,raw:this.expr.substring(r,this.index)}}gobbleIdentifier(){let e=this.code,r=this.index;for(t.isIdentifierStart(e)?this.index++:this.throwError("Unexpected "+this.char);this.index<this.expr.length&&(e=this.code,t.isIdentifierPart(e));)this.index++;return{type:t.IDENTIFIER,name:this.expr.slice(r,this.index)}}gobbleArguments(e){const r=[];let s=!1,n=0;for(;this.index<this.expr.length;){this.gobbleSpaces();let i=this.code;if(i===e){s=!0,this.index++,e===t.CPAREN_CODE&&n&&n>=r.length&&this.throwError("Unexpected token "+String.fromCharCode(e));break}if(i===t.COMMA_CODE){if(this.index++,n++,n!==r.length)if(e===t.CPAREN_CODE)this.throwError("Unexpected token ,");else if(e===t.CBRACK_CODE)for(let e=r.length;e<n;e++)r.push(null)}else if(r.length!==n&&0!==n)this.throwError("Expected comma");else{const e=this.gobbleExpression();e&&e.type!==t.COMPOUND||this.throwError("Expected comma"),r.push(e)}}return s||this.throwError("Expected "+String.fromCharCode(e)),r}gobbleGroup(){this.index++;let e=this.gobbleExpressions(t.CPAREN_CODE);if(this.code===t.CPAREN_CODE)return this.index++,1===e.length?e[0]:!!e.length&&{type:t.SEQUENCE_EXP,expressions:e};this.throwError("Unclosed (")}gobbleArray(){return this.index++,{type:t.ARRAY_EXP,elements:this.gobbleArguments(t.CBRACK_CODE)}}}const r=new class{add(e,t,r){if("string"!=typeof arguments[0])for(let e in arguments[0])this.add(e,arguments[0][e],arguments[1]);else(Array.isArray(e)?e:[e]).forEach((function(e){this[e]=this[e]||[],t&&this[e][r?"unshift":"push"](t)}),this)}run(e,t){this[e]=this[e]||[],this[e].forEach((function(e){e.call(t&&t.context?t.context:t,t)}))}};Object.assign(t,{hooks:r,plugins:new class{constructor(e){this.jsep=e,this.registered={}}register(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.forEach((e=>{if("object"!=typeof e||!e.name||!e.init)throw new Error("Invalid JSEP plugin format");this.registered[e.name]||(e.init(this.jsep),this.registered[e.name]=e)}))}}(t),COMPOUND:"Compound",SEQUENCE_EXP:"SequenceExpression",IDENTIFIER:"Identifier",MEMBER_EXP:"MemberExpression",LITERAL:"Literal",THIS_EXP:"ThisExpression",CALL_EXP:"CallExpression",UNARY_EXP:"UnaryExpression",BINARY_EXP:"BinaryExpression",ARRAY_EXP:"ArrayExpression",TAB_CODE:9,LF_CODE:10,CR_CODE:13,SPACE_CODE:32,PERIOD_CODE:46,COMMA_CODE:44,SQUOTE_CODE:39,DQUOTE_CODE:34,OPAREN_CODE:40,CPAREN_CODE:41,OBRACK_CODE:91,CBRACK_CODE:93,QUMARK_CODE:63,SEMCOL_CODE:59,COLON_CODE:58,unary_ops:{"-":1,"!":1,"~":1,"+":1},binary_ops:{"||":1,"??":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":10,"/":10,"%":10,"**":11},right_associative:new Set(["**"]),additional_identifier_chars:new Set(["$","_"]),literals:{true:!0,false:!1,null:null},this_str:"this"}),t.max_unop_len=t.getMaxKeyLen(t.unary_ops),t.max_binop_len=t.getMaxKeyLen(t.binary_ops);const s=e=>new t(e).parse(),n=Object.getOwnPropertyNames(class{});Object.getOwnPropertyNames(t).filter((e=>!n.includes(e)&&void 0===s[e])).forEach((e=>{s[e]=t[e]})),s.Jsep=t;var i={name:"ternary",init(e){e.hooks.add("after-expression",(function(t){if(t.node&&this.code===e.QUMARK_CODE){this.index++;const r=t.node,s=this.gobbleExpression();if(s||this.throwError("Expected expression"),this.gobbleSpaces(),this.code===e.COLON_CODE){this.index++;const n=this.gobbleExpression();if(n||this.throwError("Expected expression"),t.node={type:"ConditionalExpression",test:r,consequent:s,alternate:n},r.operator&&e.binary_ops[r.operator]<=.9){let s=r;for(;s.right.operator&&e.binary_ops[s.right.operator]<=.9;)s=s.right;t.node.test=s.right,s.right=t.node,t.node=r}}else this.throwError("Expected :")}}))}};s.plugins.register(i);var o={name:"regex",init(e){e.hooks.add("gobble-token",(function(t){if(47===this.code){const r=++this.index;let s=!1;for(;this.index<this.expr.length;){if(47===this.code&&!s){const s=this.expr.slice(r,this.index);let n,i="";for(;++this.index<this.expr.length;){const e=this.code;if(!(e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57))break;i+=this.char}try{n=new RegExp(s,i)}catch(e){this.throwError(e.message)}return t.node={type:e.LITERAL,value:n,raw:this.expr.slice(r-1,this.index)},t.node=this.gobbleTokenProperty(t.node),t.node}this.code===e.OBRACK_CODE?s=!0:s&&this.code===e.CBRACK_CODE&&(s=!1),this.index+=92===this.code?2:1}this.throwError("Unclosed Regex")}}))}};const a={name:"assignment",assignmentOperators:new Set(["=","*=","**=","/=","%=","+=","-=","<<=",">>=",">>>=","&=","^=","|=","||=","&&=","??="]),updateOperators:[43,45],assignmentPrecedence:.9,init(e){const t=[e.IDENTIFIER,e.MEMBER_EXP];function r(e){a.assignmentOperators.has(e.operator)?(e.type="AssignmentExpression",r(e.left),r(e.right)):e.operator||Object.values(e).forEach((e=>{e&&"object"==typeof e&&r(e)}))}a.assignmentOperators.forEach((t=>e.addBinaryOp(t,a.assignmentPrecedence,!0))),e.hooks.add("gobble-token",(function(e){const r=this.code;a.updateOperators.some((e=>e===r&&e===this.expr.charCodeAt(this.index+1)))&&(this.index+=2,e.node={type:"UpdateExpression",operator:43===r?"++":"--",argument:this.gobbleTokenProperty(this.gobbleIdentifier()),prefix:!0},e.node.argument&&t.includes(e.node.argument.type)||this.throwError(`Unexpected ${e.node.operator}`))})),e.hooks.add("after-token",(function(e){if(e.node){const r=this.code;a.updateOperators.some((e=>e===r&&e===this.expr.charCodeAt(this.index+1)))&&(t.includes(e.node.type)||this.throwError(`Unexpected ${e.node.operator}`),this.index+=2,e.node={type:"UpdateExpression",operator:43===r?"++":"--",argument:e.node,prefix:!1})}})),e.hooks.add("after-expression",(function(e){e.node&&r(e.node)}))}};s.plugins.register(o,a),s.addUnaryOp("typeof"),s.addLiteral("null",null),s.addLiteral("undefined",void 0);const h=new Set(["constructor","__proto__","__defineGetter__","__defineSetter__"]),l={evalAst(e,t){switch(e.type){case"BinaryExpression":case"LogicalExpression":return l.evalBinaryExpression(e,t);case"Compound":return l.evalCompound(e,t);case"ConditionalExpression":return l.evalConditionalExpression(e,t);case"Identifier":return l.evalIdentifier(e,t);case"Literal":return l.evalLiteral(e,t);case"MemberExpression":return l.evalMemberExpression(e,t);case"UnaryExpression":return l.evalUnaryExpression(e,t);case"ArrayExpression":return l.evalArrayExpression(e,t);case"CallExpression":return l.evalCallExpression(e,t);case"AssignmentExpression":return l.evalAssignmentExpression(e,t);default:throw SyntaxError("Unexpected expression",e)}},evalBinaryExpression:(e,t)=>({"||":(e,t)=>e||t(),"&&":(e,t)=>e&&t(),"|":(e,t)=>e|t(),"^":(e,t)=>e^t(),"&":(e,t)=>e&t(),"==":(e,t)=>e==t(),"!=":(e,t)=>e!=t(),"===":(e,t)=>e===t(),"!==":(e,t)=>e!==t(),"<":(e,t)=>e<t(),">":(e,t)=>e>t(),"<=":(e,t)=>e<=t(),">=":(e,t)=>e>=t(),"<<":(e,t)=>e<<t(),">>":(e,t)=>e>>t(),">>>":(e,t)=>e>>>t(),"+":(e,t)=>e+t(),"-":(e,t)=>e-t(),"*":(e,t)=>e*t(),"/":(e,t)=>e/t(),"%":(e,t)=>e%t()}[e.operator](l.evalAst(e.left,t),(()=>l.evalAst(e.right,t)))),evalCompound(e,t){let r;for(let s=0;s<e.body.length;s++){"Identifier"===e.body[s].type&&["var","let","const"].includes(e.body[s].name)&&e.body[s+1]&&"AssignmentExpression"===e.body[s+1].type&&(s+=1);const n=e.body[s];r=l.evalAst(n,t)}return r},evalConditionalExpression:(e,t)=>l.evalAst(e.test,t)?l.evalAst(e.consequent,t):l.evalAst(e.alternate,t),evalIdentifier(e,t){if(Object.hasOwn(t,e.name))return t[e.name];throw ReferenceError(`${e.name} is not defined`)},evalLiteral:e=>e.value,evalMemberExpression(e,t){const r=String(e.computed?l.evalAst(e.property):e.property.name),s=l.evalAst(e.object,t);if(null==s)throw TypeError(`Cannot read properties of ${s} (reading '${r}')`);if(!Object.hasOwn(s,r)&&h.has(r))throw TypeError(`Cannot read properties of ${s} (reading '${r}')`);const n=s[r];return"function"==typeof n?n.bind(s):n},evalUnaryExpression:(e,t)=>({"-":e=>-l.evalAst(e,t),"!":e=>!l.evalAst(e,t),"~":e=>~l.evalAst(e,t),"+":e=>+l.evalAst(e,t),typeof:e=>typeof l.evalAst(e,t)}[e.operator](e.argument)),evalArrayExpression:(e,t)=>e.elements.map((e=>l.evalAst(e,t))),evalCallExpression(e,t){const r=e.arguments.map((e=>l.evalAst(e,t)));return l.evalAst(e.callee,t)(...r)},evalAssignmentExpression(e,t){if("Identifier"!==e.left.type)throw SyntaxError("Invalid left-hand side in assignment");const r=e.left.name,s=l.evalAst(e.right,t);return t[r]=s,t[r]}};function c(e,t){return(e=e.slice()).push(t),e}function p(e,t){return(t=t.slice()).unshift(e),t}class u extends Error{constructor(e){super('JSONPath should not be called with "new" (it prevents return of (unwrapped) scalar values)'),this.avoidNew=!0,this.value=e,this.name="NewError"}}function d(e,t,r,s,n){if(!(this instanceof d))try{return new d(e,t,r,s,n)}catch(e){if(!e.avoidNew)throw e;return e.value}"string"==typeof e&&(n=s,s=r,r=t,t=e,e=null);const i=e&&"object"==typeof e;if(e=e||{},this.json=e.json||r,this.path=e.path||t,this.resultType=e.resultType||"value",this.flatten=e.flatten||!1,this.wrap=!Object.hasOwn(e,"wrap")||e.wrap,this.sandbox=e.sandbox||{},this.eval=void 0===e.eval?"safe":e.eval,this.ignoreEvalErrors=void 0!==e.ignoreEvalErrors&&e.ignoreEvalErrors,this.parent=e.parent||null,this.parentProperty=e.parentProperty||null,this.callback=e.callback||s||null,this.otherTypeCallback=e.otherTypeCallback||n||function(){throw new TypeError("You must supply an otherTypeCallback callback option with the @other() operator.")},!1!==e.autostart){const s={path:i?e.path:t};i?"json"in e&&(s.json=e.json):s.json=r;const n=this.evaluate(s);if(!n||"object"!=typeof n)throw new u(n);return n}}d.prototype.evaluate=function(e,t,r,s){let n=this.parent,i=this.parentProperty,{flatten:o,wrap:a}=this;if(this.currResultType=this.resultType,this.currEval=this.eval,this.currSandbox=this.sandbox,r=r||this.callback,this.currOtherTypeCallback=s||this.otherTypeCallback,t=t||this.json,(e=e||this.path)&&"object"==typeof e&&!Array.isArray(e)){if(!e.path&&""!==e.path)throw new TypeError('You must supply a "path" property when providing an object argument to JSONPath.evaluate().');if(!Object.hasOwn(e,"json"))throw new TypeError('You must supply a "json" property when providing an object argument to JSONPath.evaluate().');({json:t}=e),o=Object.hasOwn(e,"flatten")?e.flatten:o,this.currResultType=Object.hasOwn(e,"resultType")?e.resultType:this.currResultType,this.currSandbox=Object.hasOwn(e,"sandbox")?e.sandbox:this.currSandbox,a=Object.hasOwn(e,"wrap")?e.wrap:a,this.currEval=Object.hasOwn(e,"eval")?e.eval:this.currEval,r=Object.hasOwn(e,"callback")?e.callback:r,this.currOtherTypeCallback=Object.hasOwn(e,"otherTypeCallback")?e.otherTypeCallback:this.currOtherTypeCallback,n=Object.hasOwn(e,"parent")?e.parent:n,i=Object.hasOwn(e,"parentProperty")?e.parentProperty:i,e=e.path}if(n=n||null,i=i||null,Array.isArray(e)&&(e=d.toPathString(e)),!e&&""!==e||!t)return;const h=d.toPathArray(e);"$"===h[0]&&h.length>1&&h.shift(),this._hasParentSelector=null;const l=this._trace(h,t,["$"],n,i,r).filter((function(e){return e&&!e.isParentSelector}));return l.length?a||1!==l.length||l[0].hasArrExpr?l.reduce(((e,t)=>{const r=this._getPreferredOutput(t);return o&&Array.isArray(r)?e=e.concat(r):e.push(r),e}),[]):this._getPreferredOutput(l[0]):a?[]:void 0},d.prototype._getPreferredOutput=function(e){const t=this.currResultType;switch(t){case"all":{const t=Array.isArray(e.path)?e.path:d.toPathArray(e.path);return e.pointer=d.toPointer(t),e.path="string"==typeof e.path?e.path:d.toPathString(e.path),e}case"value":case"parent":case"parentProperty":return e[t];case"path":return d.toPathString(e[t]);case"pointer":return d.toPointer(e.path);default:throw new TypeError("Unknown result type")}},d.prototype._handleCallback=function(e,t,r){if(t){const s=this._getPreferredOutput(e);e.path="string"==typeof e.path?e.path:d.toPathString(e.path),t(s,r,e)}},d.prototype._trace=function(e,t,r,s,n,i,o,a){let h;if(!e.length)return h={path:r,value:t,parent:s,parentProperty:n,hasArrExpr:o},this._handleCallback(h,i,"value"),h;const l=e[0],u=e.slice(1),d=[];function f(e){Array.isArray(e)?e.forEach((e=>{d.push(e)})):d.push(e)}if(("string"!=typeof l||a)&&t&&Object.hasOwn(t,l))f(this._trace(u,t[l],c(r,l),t,l,i,o));else if("*"===l)this._walk(t,(e=>{f(this._trace(u,t[e],c(r,e),t,e,i,!0,!0))}));else if(".."===l)f(this._trace(u,t,r,s,n,i,o)),this._walk(t,(s=>{"object"==typeof t[s]&&f(this._trace(e.slice(),t[s],c(r,s),t,s,i,!0))}));else{if("^"===l)return this._hasParentSelector=!0,{path:r.slice(0,-1),expr:u,isParentSelector:!0};if("~"===l)return h={path:c(r,l),value:n,parent:s,parentProperty:null},this._handleCallback(h,i,"property"),h;if("$"===l)f(this._trace(u,t,r,null,null,i,o));else if(/^(-?\d*):(-?\d*):?(\d*)$/u.test(l))f(this._slice(l,u,t,r,s,n,i));else if(0===l.indexOf("?(")){if(!1===this.currEval)throw new Error("Eval [?(expr)] prevented in JSONPath expression.");const e=l.replace(/^\?\((.*?)\)$/u,"$1"),o=/@.?([^?]*)[['](\??\(.*?\))(?!.\)\])[\]']/gu.exec(e);o?this._walk(t,(e=>{const a=[o[2]],h=o[1]?t[e][o[1]]:t[e];this._trace(a,h,r,s,n,i,!0).length>0&&f(this._trace(u,t[e],c(r,e),t,e,i,!0))})):this._walk(t,(o=>{this._eval(e,t[o],o,r,s,n)&&f(this._trace(u,t[o],c(r,o),t,o,i,!0))}))}else if("("===l[0]){if(!1===this.currEval)throw new Error("Eval [(expr)] prevented in JSONPath expression.");f(this._trace(p(this._eval(l,t,r.at(-1),r.slice(0,-1),s,n),u),t,r,s,n,i,o))}else if("@"===l[0]){let e=!1;const o=l.slice(1,-2);switch(o){case"scalar":t&&["object","function"].includes(typeof t)||(e=!0);break;case"boolean":case"string":case"undefined":case"function":typeof t===o&&(e=!0);break;case"integer":!Number.isFinite(t)||t%1||(e=!0);break;case"number":Number.isFinite(t)&&(e=!0);break;case"nonFinite":"number"!=typeof t||Number.isFinite(t)||(e=!0);break;case"object":t&&typeof t===o&&(e=!0);break;case"array":Array.isArray(t)&&(e=!0);break;case"other":e=this.currOtherTypeCallback(t,r,s,n);break;case"null":null===t&&(e=!0);break;default:throw new TypeError("Unknown value type "+o)}if(e)return h={path:r,value:t,parent:s,parentProperty:n},this._handleCallback(h,i,"value"),h}else if("`"===l[0]&&t&&Object.hasOwn(t,l.slice(1))){const e=l.slice(1);f(this._trace(u,t[e],c(r,e),t,e,i,o,!0))}else if(l.includes(",")){const e=l.split(",");for(const o of e)f(this._trace(p(o,u),t,r,s,n,i,!0))}else!a&&t&&Object.hasOwn(t,l)&&f(this._trace(u,t[l],c(r,l),t,l,i,o,!0))}if(this._hasParentSelector)for(let e=0;e<d.length;e++){const r=d[e];if(r&&r.isParentSelector){const a=this._trace(r.expr,t,r.path,s,n,i,o);if(Array.isArray(a)){d[e]=a[0];const t=a.length;for(let r=1;r<t;r++)e++,d.splice(e,0,a[r])}else d[e]=a}}return d},d.prototype._walk=function(e,t){if(Array.isArray(e)){const r=e.length;for(let e=0;e<r;e++)t(e)}else e&&"object"==typeof e&&Object.keys(e).forEach((e=>{t(e)}))},d.prototype._slice=function(e,t,r,s,n,i,o){if(!Array.isArray(r))return;const a=r.length,h=e.split(":"),l=h[2]&&Number.parseInt(h[2])||1;let c=h[0]&&Number.parseInt(h[0])||0,u=h[1]&&Number.parseInt(h[1])||a;c=c<0?Math.max(0,c+a):Math.min(a,c),u=u<0?Math.max(0,u+a):Math.min(a,u);const d=[];for(let e=c;e<u;e+=l){this._trace(p(e,t),r,s,n,i,o,!0).forEach((e=>{d.push(e)}))}return d},d.prototype._eval=function(e,t,r,s,n,i){this.currSandbox._$_parentProperty=i,this.currSandbox._$_parent=n,this.currSandbox._$_property=r,this.currSandbox._$_root=this.json,this.currSandbox._$_v=t;const o=e.includes("@path");o&&(this.currSandbox._$_path=d.toPathString(s.concat([r])));const a=this.currEval+"Script:"+e;if(!d.cache[a]){let t=e.replaceAll("@parentProperty","_$_parentProperty").replaceAll("@parent","_$_parent").replaceAll("@property","_$_property").replaceAll("@root","_$_root").replaceAll(/@([.\s)[])/gu,"_$_v$1");if(o&&(t=t.replaceAll("@path","_$_path")),"safe"===this.currEval||!0===this.currEval||void 0===this.currEval)d.cache[a]=new this.safeVm.Script(t);else if("native"===this.currEval)d.cache[a]=new this.vm.Script(t);else if("function"==typeof this.currEval&&this.currEval.prototype&&Object.hasOwn(this.currEval.prototype,"runInNewContext")){const e=this.currEval;d.cache[a]=new e(t)}else{if("function"!=typeof this.currEval)throw new TypeError(`Unknown "eval" property "${this.currEval}"`);d.cache[a]={runInNewContext:e=>this.currEval(t,e)}}}try{return d.cache[a].runInNewContext(this.currSandbox)}catch(t){if(this.ignoreEvalErrors)return!1;throw new Error("jsonPath: "+t.message+": "+e)}},d.cache={},d.toPathString=function(e){const t=e,r=t.length;let s="$";for(let e=1;e<r;e++)/^(~|\^|@.*?\(\))$/u.test(t[e])||(s+=/^[0-9*]+$/u.test(t[e])?"["+t[e]+"]":"['"+t[e]+"']");return s},d.toPointer=function(e){const t=e,r=t.length;let s="";for(let e=1;e<r;e++)/^(~|\^|@.*?\(\))$/u.test(t[e])||(s+="/"+t[e].toString().replaceAll("~","~0").replaceAll("/","~1"));return s},d.toPathArray=function(e){const{cache:t}=d;if(t[e])return t[e].concat();const r=[],s=e.replaceAll(/@(?:null|boolean|number|string|integer|undefined|nonFinite|scalar|array|object|function|other)\(\)/gu,";$&;").replaceAll(/[['](\??\(.*?\))[\]'](?!.\])/gu,(function(e,t){return"[#"+(r.push(t)-1)+"]"})).replaceAll(/\[['"]([^'\]]*)['"]\]/gu,(function(e,t){return"['"+t.replaceAll(".","%@%").replaceAll("~","%%@@%%")+"']"})).replaceAll("~",";~;").replaceAll(/['"]?\.['"]?(?![^[]*\])|\[['"]?/gu,";").replaceAll("%@%",".").replaceAll("%%@@%%","~").replaceAll(/(?:;)?(\^+)(?:;)?/gu,(function(e,t){return";"+t.split("").join(";")+";"})).replaceAll(/;;;|;;/gu,";..;").replaceAll(/;$|'?\]|'$/gu,"").split(";").map((function(e){const t=e.match(/#(\d+)/u);return t&&t[1]?r[t[1]]:e}));return t[e]=s,t[e].concat()},d.prototype.safeVm={Script:class{constructor(e){this.code=e,this.ast=s(this.code)}runInNewContext(e){const t=Object.assign(Object.create(null),e);return l.evalAst(this.ast,t)}}};d.prototype.vm={Script:class{constructor(e){this.code=e}runInNewContext(e){let t=this.code;const r=Object.keys(e),s=[];!function(e,t,r){const s=e.length;for(let n=0;n<s;n++)r(e[n])&&t.push(e.splice(n--,1)[0])}(r,s,(t=>"function"==typeof e[t]));const n=r.map((t=>e[t]));t=s.reduce(((t,r)=>{let s=e[r].toString();return/function/u.test(s)||(s="function "+s),"var "+r+"="+s+";"+t}),"")+t,/(['"])use strict\1/u.test(t)||r.includes("arguments")||(t="var arguments = undefined;"+t),t=t.replace(/;\s*$/u,"");const i=t.lastIndexOf(";"),o=-1!==i?t.slice(0,i+1)+" return "+t.slice(i+1):" return "+t;return new Function(...r,o)(...n)}}},e.JSONPath=d}));
//# sourceMappingURL=index-browser-umd.min.cjs.map
